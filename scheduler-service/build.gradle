plugins {
    id 'org.springframework.boot' version '2.4.4'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'java'
    id 'org.sonarqube' version '3.2.0'
}


group = 'com.ssy.lingxi.scheduler'
version = '3.0.0'
sourceCompatibility = '1.8'

repositories {
    mavenLocal()
    maven {
        url 'http://10.0.0.21:8081/repository/maven-public/'
    }
    maven {
        url 'https://maven.aliyun.com/repository/public'
    }
    mavenCentral()
}

dependencies {
    //引用公共组件
    implementation project(':component:base-spring-boot-starter')
    annotationProcessor project(':component:base-spring-boot-starter')

    //引用其他组件
    implementation project(':component:redis-spring-boot-starter')
    implementation project(':component:skywalking-spring-boot-starter')

    implementation project(':scheduler-service:scheduler-service-api')

    //spring cloud
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.cloud:spring-cloud-loadbalancer'

    //spring boot
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-quartz'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'io.projectreactor:reactor-test'

    //nacos
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery'
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config'

    //sentinel
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel'

    //seata
    implementation('com.alibaba.cloud:spring-cloud-starter-alibaba-seata') {
        exclude group: 'io.seata', module: 'seata-spring-boot-starter'
    }
    implementation('io.seata:seata-spring-boot-starter:1.6.0'){
        exclude group: 'com.alibaba', module: 'druid'
    }

    //arthas
    //implementation 'com.taobao.arthas:arthas-spring-boot-starter:3.6.7'

    //prometheus
    implementation 'io.micrometer:micrometer-registry-prometheus'

    //pgsql
    runtimeOnly 'org.postgresql:postgresql'
}

ext {
    set('springCloudVersion', "2020.0.2")
    set('springCloudAlibabaVersion', "2021.1")
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.alibaba.cloud:spring-cloud-alibaba-dependencies:${springCloudAlibabaVersion}"
    }
}

sonarqube {
    properties {
        property "sonar.host.url", project.properties['sonarHostUrl']
        property "sonar.login", project.properties['sonarUserName']
        property "sonar.password", project.properties['sonarUserPassword']
        property "sonar.projectKey", project.properties['projectName'] + "." + project.name
        property "sonar.projectName", project.properties['projectName'] + "." + project.name
        property "sonar.projectVersion", version
        property "sonar.sources", "src/main/java"
        property "sonar.sourceEncoding", "UTF-8"
    }
}