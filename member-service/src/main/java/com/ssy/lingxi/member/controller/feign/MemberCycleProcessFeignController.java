package com.ssy.lingxi.member.controller.feign;

import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.req.engine.ProcessEngineReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberCycleProcessFeign;
import com.ssy.lingxi.member.service.web.IPlatformMemberCycleProcessService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 内部接口 - 会员流程服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-28
 * @ignore 不需要提交到Yapi
 **/
@RestController
public class MemberCycleProcessFeignController implements IMemberCycleProcessFeign {

    @Resource
    private IPlatformMemberCycleProcessService iPlatformMemberCycleProcessService;

    /**
     * 保存基础流程
     * @param engineBO 基础流程
     * @return Void
     */
    @Override
    public WrapperResp<Void> saveBaseProcess(@RequestBody @Valid ProcessEngineReq engineBO) {
        iPlatformMemberCycleProcessService.saveBaseProcess(engineBO);
        return WrapperUtil.success();
    }

    /**
     * 查询会员流程
     * @param engineRuleQueryReq 查询流程
     * @return ProcessEngineRuleVO
     */
    @Override
    public WrapperResp<List<ProcessEngineRuleResp>> getMemberProcess(@RequestBody @Valid EngineRuleQueryReq engineRuleQueryReq) {
        return WrapperUtil.success(iPlatformMemberCycleProcessService.getMemberProcess(engineRuleQueryReq));
    }

}
