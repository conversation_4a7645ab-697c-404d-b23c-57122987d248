package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.member.handler.annotation.*;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 会员入库分类审核 - 主营品类接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-25
 */
@Data
public class BusinessCategoryReq implements Serializable {
    private static final long serialVersionUID = -7703112045191287208L;

    /**
     * 品类列表,当为默认结算方式时，可为空
     */
    private List<BusinessCategoryDetailReq> details;

    /**
     * 结算方式，1-现结，2-账期(按天), 3-账期(按月)，4-月结
     */
    @NotNull(message = "结算方式不能为空")
    @PurchaseContractPayTypeAnnotation
    private Integer payType;

    /**
     * 月， 结算方式为账期(按月)时要大于0
     */
    @Max(value = 1000000000, message = "结算周期（月）超过限制")
    private Integer month;

    /**
     * 每月几号，结算方式为“账期(按月)” 或 “月结” 时要大于0
     */
    @Max(value = 31, message = "结算日期最大为31")
    private Integer monthDay;

    /**
     * 天，结算方式为账期(按天)时要大于0
     */
    @Max(value = 1000000000, message = "结算周期（天）超过限制")
    private Integer days;

    /**
     * 发票类型，1-增值税专用发票，2-普通发票，3-机动车专用发票，4-机打发票，5-定额发票
     */
    @NotNull(message = "发票类型不能为空")
    @BusinessCategoryInvoiceTypeAnnotation
    private Integer invoiceType;

    /**
     * 税点，只要百分比的分子部分，不要转换为小数
     */
    @NotNull(message = "税点不能为空")
    @Min(value = 0, message = "税点最小值为0")
    @Max(value = 100, message = "税点最大值为100")
    private BigDecimal taxPoint;

    /**
     * 预付款：1:不需预付 2：需要预付
     */
    @NotNull(message = "预付款不能为空")
    @AdvanceChargeAnnotation
    private Integer advanceCharge;

    /**
     * 结算单据枚举
         1、订单
         2、物流单
         3、生产通知单
         4、发货单
         5、收货单
         6、发票单
         7、收货单+发票单
     */
    @SettlementDocumentsAnnotation
    private Integer settlementDocuments;

    /**
     * 付款方式枚举
         1、现金
         2、转账
         3、支票
         4、电汇（T/T）
         5、信汇
         6、银行汇票
         7、银行承兑汇票3个月
         8、银行承兑汇票6个月
         9、D/P付款交单
         10、D/A承兑交单
         11、L/C信用证
     */
    @PaymentTypeAnnotation
    private Integer paymentType;

    /**
     * 是否为默认，1为默认，0为非默认
     */
    @NotNull(message = "是否默认不能为空")
    private Integer isDefault;

    public List<BusinessCategoryDetailReq> getDetails() {
        return details;
    }

    public void setDetails(List<BusinessCategoryDetailReq> details) {
        this.details = details;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getMonthDay() {
        return monthDay;
    }

    public void setMonthDay(Integer monthDay) {
        this.monthDay = monthDay;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public BigDecimal getTaxPoint() {
        return taxPoint;
    }

    public void setTaxPoint(BigDecimal taxPoint) {
        this.taxPoint = taxPoint;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }
}
