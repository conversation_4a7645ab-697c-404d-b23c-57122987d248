package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelConfigDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员等级操作Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-07
 */
@Repository
public interface MemberLevelConfigRepository extends JpaRepository<MemberLevelConfigDO, Long>, JpaSpecificationExecutor<MemberLevelConfigDO> {

    MemberLevelConfigDO findFirstByMemberIdAndRoleIdAndSubRoleIdAndStatus(Long memberId, Long roleId, Long subRoleId, Integer status, Sort sort);

    MemberLevelConfigDO findFirstByMemberIdAndRoleIdAndSubRoleIdAndLevel(Long memberId, Long roleId, Long subRoleId, Integer level);

    List<MemberLevelConfigDO> findByMemberIdAndRoleIdAndStatus(Long memberId, Long roleId, Integer status);

    List<MemberLevelConfigDO> findByLevelTypeAndStatus(Integer levelType, Integer status);

    List<MemberLevelConfigDO> findByLevelTypeAndStatusAndSubRoleId(Integer memberLevelTypeEnum, Integer status, Long subRoleId);

    List<MemberLevelConfigDO> findByMemberIdAndRoleId(Long memberId, Long roleId);

    List<MemberLevelConfigDO> findByMemberIdAndRoleIdAndSubRoleId(Long memberId, Long roleId, Long subRoleId);

    List<MemberLevelConfigDO> findByMemberIdAndRoleIdAndSubRoleIdAndStatus(Long memberId, Long roleId, Long subRoleId, Integer status);

    Boolean existsByMemberIdAndRoleIdAndSubRoleIdAndLevelAndStatus(Long memberId, Long roleId, Long subRoleId, Integer level, Integer status);

    Boolean existsByMemberIdAndRoleIdAndLevelAndSubRoleIdIn(Long memberId, Long roleId, Integer level, List<Long> subRoleIds);

    Boolean existsByMemberIdAndRoleIdAndLevelAndSubRoleIdAndIdNot(Long memberId, Long roleId, Integer level, Long subRoleId, Long id);

    @Transactional
    void deleteByMemberIdAndRoleId(Long memberId, Long roleId);

    Boolean existsByMemberIdAndRoleIdAndSubRoleId(Long memberId, Long roleId, Long subRoleId);

    Boolean existsByLevelTypeAndSubRoleId(Integer code, Long subRoleId);
}
