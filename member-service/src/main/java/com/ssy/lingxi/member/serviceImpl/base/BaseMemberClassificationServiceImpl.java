package com.ssy.lingxi.member.serviceImpl.base;

import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.PurchaseContractPayTypeEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.BusinessCategoryInvoiceTypeEnum;
import com.ssy.lingxi.component.base.enums.member.PaymentTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.req.ProvinceCityCodeReq;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.member.api.model.resp.BusinessCategoryFeignResp;
import com.ssy.lingxi.member.api.model.resp.MemberCategoryFeignResp;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.BusinessCategoryBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberBusinessCategoryDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberClassificationDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.req.validate.BusinessCategoryDetailReq;
import com.ssy.lingxi.member.model.req.validate.BusinessCategoryReq;
import com.ssy.lingxi.member.model.resp.validate.*;
import com.ssy.lingxi.member.repository.MemberBusinessCategoryRepository;
import com.ssy.lingxi.member.repository.MemberClassificationRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberClassificationService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
@Service
public class BaseMemberClassificationServiceImpl implements IBaseMemberClassificationService {
    @Resource
    private MemberClassificationRepository memberClassificationRepository;

    @Resource
    private MemberBusinessCategoryRepository memberBusinessCategoryRepository;

    /**
     * 保存会员入库信息，外部调用方要保存MemberRelationDO
     *
     * @param relationDO      会员关系
     * @param code            会员编码
     * @param partnerType 会员合作类型枚举
     * @param maxAmount       合作金额
     * @param areaCodes       适用区域
     * @param categories      主营品类列表
     * @return 保存结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void saveMemberClassification(MemberRelationDO relationDO, String remark, Integer currencyType, String code, Integer partnerType, BigDecimal maxAmount, List<ProvinceCityCodeReq> areaCodes, List<BusinessCategoryReq> categories) {
        //判断会员编码是否重复
        List<MemberClassificationDO> classificationList = memberClassificationRepository.findByMemberIdAndRoleId(relationDO.getMemberId(), relationDO.getRoleId());
        if(!CollectionUtils.isEmpty(classificationList) && relationDO.getClassification() == null && classificationList.stream().anyMatch(clf -> clf.getCode().equalsIgnoreCase(code))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_CLASSIFY_CODE_EXISTS);
        }

        if(!CollectionUtils.isEmpty(classificationList) && relationDO.getClassification() != null && classificationList.stream().filter(cf -> !cf.getRelation().getId().equals(relationDO.getId())).anyMatch(clf -> clf.getCode().equalsIgnoreCase(code))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_CLASSIFY_CODE_EXISTS);
        }

        //校验是否只有一个默认结算方式
        List<BusinessCategoryReq> defaultCategories = categories.stream().filter(category -> Objects.equals(category.getIsDefault(), IsDefaultEnum.YES.getCode())).collect(Collectors.toList());
        if (defaultCategories.size() > 1){
            throw new BusinessException(ResponseCodeEnum.MC_MS_DEFAULT_MAY_ONLY_ONE);
        }

        for (BusinessCategoryReq category : categories) {
            //校验非默认结算方式中的品类字段
            if (!Objects.equals(category.getIsDefault(), IsDefaultEnum.YES.getCode())) {
                for (BusinessCategoryDetailReq detail : category.getDetails()) {
                    if (NumberUtil.isNullOrLteZero(detail.getLevel())) {
                        throw new BusinessException(ResponseCodeEnum.MC_MS_BUSINESS_CATEGORY_LEVEL_DOES_NOT_EXIST_AND_MORE_THAN_ZERO);
                    }
                    if (NumberUtil.isNullOrLteZero(detail.getCategoryId())) {
                        throw new BusinessException(ResponseCodeEnum.MC_MS_BUSINESS_CATEGORY_ID_DOES_NOT_EXIST_AND_MORE_THAN_ZERO);
                    }
                    if (Objects.isNull(detail.getName())) {
                        throw new BusinessException(ResponseCodeEnum.MC_MS_BUSINESS_CATEGORY_NAME_DOES_NOT_EXIST);
                    }
                    if (Objects.isNull(detail.getParentId())) {
                        throw new BusinessException(ResponseCodeEnum.MC_MS_BUSINESS_CATEGORY_PARENT_ID_DOES_NOT_EXIST);
                    }
                }
            }

            //品类层级要从1开始
            if (category.getDetails().stream().min(Comparator.comparingInt(BusinessCategoryDetailReq::getLevel)).map(BusinessCategoryDetailReq::getLevel).orElse(0) != 1 && !Objects.equals(category.getIsDefault(), IsDefaultEnum.YES.getCode())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_BUSINESS_CATEGORY_LEVEL_MUST_START_WITH_ONE);
            }

            //校验主营品类信息中的结算周期
            //账期（按月）
            if(category.getPayType().equals(PurchaseContractPayTypeEnum.PERIOD_MONTHLY.getCode())) {
                if(NumberUtil.isNullOrNegative(category.getMonth())) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_BUSINESS_CATEGORY_MONTH_MUST_GT_ZERO);
                }

                if(NumberUtil.isNullOrNegative(category.getMonthDay())) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_BUSINESS_CATEGORY_MONTH_DAY_MUST_GT_ZERO);
                }
            }

            //账期（按天）
            if(category.getPayType().equals(PurchaseContractPayTypeEnum.PERIOD_DAILY.getCode())) {
                if(NumberUtil.isNullOrNegative(category.getDays())) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_BUSINESS_CATEGORY_MONTH_DAY_MUST_GT_ZERO);
                }
            }

            //月结
            if(category.getPayType().equals(PurchaseContractPayTypeEnum.MONTHLY.getCode())) {
                if(NumberUtil.isNullOrNegative(category.getMonthDay())) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_BUSINESS_CATEGORY_MONTH_DAY_MUST_GT_ZERO);
                }
            }
        }

        //删除旧数据
        MemberClassificationDO classification = relationDO.getClassification();
        if(classification != null) {
            relationDO.setClassification(null);
            memberClassificationRepository.delete(classification);
        }

        classification = new MemberClassificationDO();
        classification.setMemberId(relationDO.getMemberId());
        classification.setRoleId(relationDO.getRoleId());
        classification.setCode(code);
        classification.setRelation(relationDO);
        classification.setPartnerType(partnerType);
        classification.setMaxAmount(maxAmount);
        classification.setRemark(remark);
        classification.setCurrencyType(currencyType);
        if(CollectionUtils.isEmpty(areaCodes) || areaCodes.stream().anyMatch(provinceCityCodeReq -> !StringUtils.hasLength(provinceCityCodeReq.getProvinceCode()))) {
            classification.setAreas(new ArrayList<>());
        } else {
            List<String> codeList = areaCodes.stream().flatMap(areaCodeVO -> {
                if(StringUtils.hasLength(areaCodeVO.getCityCode())) {
                    return Stream.of(areaCodeVO.getProvinceCode(), areaCodeVO.getCityCode());
                } else {
                    return Stream.of(areaCodeVO.getProvinceCode());
                }
            }).distinct().collect(Collectors.toList());

            List<AreaCodeNameResp> dtoList = AreaUtil.findByCodeIn(codeList);
            if(codeList.size() != dtoList.size()) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_COUNTRY_AREA_DOES_NOT_EXIST);
            }

            classification.setAreas(AreaUtil.transferToDTO(areaCodes, dtoList));
        }
        memberClassificationRepository.save(classification);

        List<MemberBusinessCategoryDO> categoryList = new ArrayList<>();
        for (BusinessCategoryReq businessCategoryReq : categories) {
            MemberBusinessCategoryDO categoryDO = new MemberBusinessCategoryDO();
            //若是非默认的，才存储其品类列表
            if (!Objects.equals(categoryDO.getIsDefault(), IsDefaultEnum.YES.getCode())) {
                categoryDO.setCategories(businessCategoryReq.getDetails().stream().map(detail -> new BusinessCategoryBO(detail.getLevel(), detail.getCategoryId(), detail.getName(), detail.getParentId(), transitionBusinessCategoryBO(detail.getChildren()))).collect(Collectors.toList()));
            }
            categoryDO.setPayType(businessCategoryReq.getPayType());
            categoryDO.setClassification(classification);
            categoryDO.setMonth(NumberUtil.isNullOrZero(businessCategoryReq.getMonth()) ? 0 : businessCategoryReq.getMonth());
            categoryDO.setMonthDay(NumberUtil.isNullOrZero(businessCategoryReq.getMonthDay()) ? 0 : businessCategoryReq.getMonthDay());
            categoryDO.setDays(NumberUtil.isNullOrZero(businessCategoryReq.getDays()) ? 0 : businessCategoryReq.getDays());
            categoryDO.setInvoiceType(businessCategoryReq.getInvoiceType());
            categoryDO.setInvoiceTypeName(BusinessCategoryInvoiceTypeEnum.getNameByCode(businessCategoryReq.getInvoiceType()));
            categoryDO.setTaxPoint(businessCategoryReq.getTaxPoint().divide(BigDecimal.valueOf(100), 2 , RoundingMode.HALF_UP));
            categoryDO.setAdvanceCharge(businessCategoryReq.getAdvanceCharge());
            categoryDO.setSettlementDocuments(businessCategoryReq.getSettlementDocuments());
            categoryDO.setPaymentType(businessCategoryReq.getPaymentType());
            categoryDO.setIsDefault(businessCategoryReq.getIsDefault());
            if(!CollectionUtils.isEmpty(categoryDO.getCategories())){
                List<BusinessCategoryBO> categoriesList = categoryDO.getCategories();
                List<CategoryIdResp> categoryIdResps = new ArrayList<>();
                getCategoryIdVOS(categoriesList, categoryIdResps);
                if(!CollectionUtils.isEmpty(categoryIdResps)){
                    categoryDO.setCategoryIdList(JSONUtil.toJsonStr(categoryIdResps));
                }
            }
            categoryList.add(categoryDO);
        }
        memberBusinessCategoryRepository.saveAll(categoryList);

        classification.setCategories(new HashSet<>(categoryList));
        memberClassificationRepository.saveAndFlush(classification);

        //在这里设置MemberRelation的关联，外部调用方要保存MemberRelation
        relationDO.setClassification(classification);

    }

    /**
     * 查询会员入库分类信息
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public MemberClassifyResp getMemberClassification(MemberRelationDO relationDO) {
        MemberClassifyResp classifyVO = new MemberClassifyResp();
        classifyVO.setBrandCode(relationDO.getSubMember().getBrandCode());
        classifyVO.setBrandName(relationDO.getSubMember().getBrandName());
        MemberClassificationDO classification = relationDO.getClassification();
        if(classification != null) {
            classifyVO.setCode(classification.getCode());
            classifyVO.setPartnerTypeName(MemberPartnerTypeEnum.getCodeMsg(classification.getPartnerType()));
            classifyVO.setCurrencyType(classification.getCurrencyType());
            classifyVO.setCurrencyTypeName(CurrencyTypeEnum.getCodeMsg(classification.getCurrencyType()));
            classifyVO.setRemark(classification.getRemark());
            //保留两位小数
            DecimalFormat df = new DecimalFormat("#0.00");
            classifyVO.setMaxAmount(df.format(classification.getMaxAmount()));
            classifyVO.setClassifyAreas(AreaUtil.transferToList(classification.getAreas()));
            if(!CollectionUtils.isEmpty(classification.getCategories())) {
                classifyVO.setCategories(classification.getCategories().stream().sorted(Comparator.comparingLong(MemberBusinessCategoryDO::getId)).map(memberBusinessCategoryDO -> {
                    BusinessCategoryQueryResp queryVO = new BusinessCategoryQueryResp();
                    queryVO.setId(memberBusinessCategoryDO.getId());
                    queryVO.setDetails(CollectionUtils.isEmpty(memberBusinessCategoryDO.getCategories()) ? new ArrayList<>() : memberBusinessCategoryDO.getCategories().stream().map(category -> new BusinessCategoryDetailQueryResp(category.getLevel(), category.getCategoryId(), category.getName(),category.getParentId(),transitionBusinessCategoryDetailQueryVO(category.getChildren()))).sorted(Comparator.comparingInt(BusinessCategoryDetailQueryResp::getLevel)).collect(Collectors.toList()));
                    queryVO.setPayType(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getPayType()) ? 0 : memberBusinessCategoryDO.getPayType());
                    queryVO.setPayTypeName(PurchaseContractPayTypeEnum.getNameByCode(queryVO.getPayType()));
                    queryVO.setMonth(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getMonth()) ? 0 : memberBusinessCategoryDO.getMonth());
                    queryVO.setMonthDay(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getMonthDay()) ? 0 : memberBusinessCategoryDO.getMonthDay());
                    queryVO.setDays(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getDays()) ? 0 : memberBusinessCategoryDO.getDays());
                    queryVO.setInvoiceType(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getInvoiceType()) ? 0 : memberBusinessCategoryDO.getInvoiceType());
                    queryVO.setInvoiceTypeName(BusinessCategoryInvoiceTypeEnum.getNameByCode(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getInvoiceType()) ? 0 : memberBusinessCategoryDO.getInvoiceType()));
                    queryVO.setTaxPoint(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getTaxPoint()) ? "0" : MemberConstant.BIG_DECIMAL_FORMAT.format(memberBusinessCategoryDO.getTaxPoint().multiply(BigDecimal.valueOf(100))));
                    queryVO.setAdvanceCharge(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getInvoiceType()) ? 0 : memberBusinessCategoryDO.getAdvanceCharge());
                    queryVO.setAdvanceChargeName(AdvanceChargeEnum.getNameByCode(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getAdvanceCharge()) ? 0 : memberBusinessCategoryDO.getAdvanceCharge()));
                    queryVO.setSettlementDocuments(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getSettlementDocuments()) ? 0 : memberBusinessCategoryDO.getSettlementDocuments());
                    queryVO.setSettlementDocumentsName(SettlementDocumentsEnum.getNameByCode(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getSettlementDocuments()) ? 0 : memberBusinessCategoryDO.getSettlementDocuments()));
                    queryVO.setPaymentType(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getPaymentType()) ? 0 : memberBusinessCategoryDO.getPaymentType());
                    queryVO.setPaymentTypeName(PaymentTypeEnum.getNameByCode(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getPaymentType()) ? 0 : memberBusinessCategoryDO.getPaymentType()));
                    queryVO.setIsDefault(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getIsDefault()) ? 0 : memberBusinessCategoryDO.getIsDefault());
                    return queryVO;
                }).collect(Collectors.toList()));
            }
        }
        return classifyVO;
    }

    /**
     * 查询会员入库分类信息（可编辑）
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public MemberClassifyQueryResp findMemberClassification(MemberRelationDO relationDO) {
        MemberClassifyQueryResp classifyVO = new MemberClassifyQueryResp();
        classifyVO.setPartnerTypes(MemberPartnerTypeEnum.toDropdownList());

        MemberClassificationDO classification = relationDO.getClassification();
        if(classification != null) {
            classifyVO.setCode(classification.getCode());
            classifyVO.setPartnerType(classification.getPartnerType());
            classifyVO.setPartnerTypes(MemberPartnerTypeEnum.toEditableDropdownList());
            classifyVO.setCurrencyType(classification.getCurrencyType());
            classifyVO.setCurrencyTypeName(CurrencyTypeEnum.getCodeMsg(classification.getCurrencyType()));
            classifyVO.setRemark(classification.getRemark());
            //保留两位小数
            DecimalFormat df = new DecimalFormat("#0.00");
            classifyVO.setMaxAmount(df.format(classification.getMaxAmount()));
            classifyVO.setAreaCodes(AreaUtil.transferToCodeList(classification.getAreas()));
            if(!CollectionUtils.isEmpty(classification.getCategories())) {
                classifyVO.setCategories(classification.getCategories().stream().sorted(Comparator.comparingLong(MemberBusinessCategoryDO::getId)).map(memberBusinessCategoryDO -> {
                    BusinessCategoryQueryResp queryVO = new BusinessCategoryQueryResp();
                    queryVO.setId(memberBusinessCategoryDO.getId());
                    queryVO.setDetails(memberBusinessCategoryDO.getCategories().stream().map(category -> new BusinessCategoryDetailQueryResp(category.getLevel(), category.getCategoryId(), category.getName(),category.getParentId(),transitionBusinessCategoryDetailQueryVO(category.getChildren()))).sorted(Comparator.comparingInt(BusinessCategoryDetailQueryResp::getLevel)).collect(Collectors.toList()));
                    queryVO.setPayType(memberBusinessCategoryDO.getPayType());
                    queryVO.setPayTypeName(PurchaseContractPayTypeEnum.getNameByCode(memberBusinessCategoryDO.getPayType()));
                    queryVO.setMonth(memberBusinessCategoryDO.getMonth());
                    queryVO.setMonthDay(memberBusinessCategoryDO.getMonthDay());
                    queryVO.setDays(memberBusinessCategoryDO.getDays());
                    queryVO.setInvoiceType(memberBusinessCategoryDO.getInvoiceType());
                    queryVO.setInvoiceTypeName(BusinessCategoryInvoiceTypeEnum.getNameByCode(memberBusinessCategoryDO.getInvoiceType()));
                    queryVO.setTaxPoint(MemberConstant.BIG_DECIMAL_FORMAT.format(memberBusinessCategoryDO.getTaxPoint()));
                    queryVO.setAdvanceCharge(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getAdvanceCharge()) ? 1 : memberBusinessCategoryDO.getAdvanceCharge());
                    queryVO.setAdvanceChargeName(AdvanceChargeEnum.getNameByCode(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getAdvanceCharge()) ? 1 : memberBusinessCategoryDO.getAdvanceCharge()));
                    queryVO.setSettlementDocuments(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getSettlementDocuments()) ? 0 : memberBusinessCategoryDO.getSettlementDocuments());
                    queryVO.setSettlementDocumentsName(SettlementDocumentsEnum.getNameByCode(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getSettlementDocuments()) ? 0 : memberBusinessCategoryDO.getSettlementDocuments()));
                    queryVO.setPaymentType(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getPaymentType()) ? 0 : memberBusinessCategoryDO.getPaymentType());
                    queryVO.setPaymentTypeName(PaymentTypeEnum.getNameByCode(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getPaymentType()) ? 0 : memberBusinessCategoryDO.getPaymentType()));
                    queryVO.setIsDefault(NumberUtil.isNullOrZero(memberBusinessCategoryDO.getIsDefault()) ? 0 : memberBusinessCategoryDO.getIsDefault());
                    return queryVO;
                }).collect(Collectors.toList()));
            }
        }
        return classifyVO;
    }

    /**
     * 查询会员入库分类信息 - 主营品类信息
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public List<MemberCategoryFeignResp> findMemberBusinessCategories(MemberRelationDO relationDO) {
        MemberClassificationDO classification = relationDO.getClassification();
        if(classification == null || CollectionUtils.isEmpty(classification.getCategories())) {
            return new ArrayList<>();
        }

        List<MemberCategoryFeignResp> result = classification.getCategories().stream().map(memberBusinessCategoryDO -> {
            MemberCategoryFeignResp queryVO = new MemberCategoryFeignResp();
            // 找到所有的品类
            List<BusinessCategoryBO> allCategory = getAllCategory(memberBusinessCategoryDO.getCategories(), new ArrayList<>());
            // 转成需要返回的VO
            List<BusinessCategoryFeignResp> categories = allCategory.stream().map(c -> {
                BusinessCategoryFeignResp feignVO = new BusinessCategoryFeignResp();
                feignVO.setLevel(c.getLevel());
                feignVO.setCategoryId(c.getCategoryId());
                feignVO.setName(c.getName());
                return feignVO;
            }).collect(Collectors.toList());

            List<BusinessCategoryFeignResp> categoriesVO = categories.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(BusinessCategoryFeignResp::getCategoryId))),
                            p -> p.stream().sorted(Comparator.comparing(BusinessCategoryFeignResp::getCategoryId).reversed())
                                    .collect(Collectors.toList())));
            queryVO.setCategories(categoriesVO);
            queryVO.setPayType(memberBusinessCategoryDO.getPayType());
            queryVO.setPayTypeName(PurchaseContractPayTypeEnum.getNameByCode(memberBusinessCategoryDO.getPayType()));
            queryVO.setMonth(memberBusinessCategoryDO.getMonth());
            queryVO.setMonthDay(memberBusinessCategoryDO.getMonthDay());
            queryVO.setDays(memberBusinessCategoryDO.getDays());
            queryVO.setInvoiceType(memberBusinessCategoryDO.getInvoiceType());
            queryVO.setInvoiceTypeName(memberBusinessCategoryDO.getInvoiceTypeName());
            queryVO.setTaxPoint(memberBusinessCategoryDO.getTaxPoint());
            return queryVO;
        }).collect(Collectors.toList());
        // 添加默认的品类结算方式
        MemberBusinessCategoryDO categoryDO = classification.getCategories().stream().filter(f -> EnableDisableStatusEnum.ENABLE.getCode().equals(f.getIsDefault())).findFirst().orElse(null);
        if(!ObjectUtils.isEmpty(categoryDO)){
            MemberCategoryFeignResp vo = new MemberCategoryFeignResp();
            vo.setIsDefault(EnableDisableStatusEnum.ENABLE.getCode());
            vo.setPayType(categoryDO.getPayType());
            vo.setPayTypeName(PurchaseContractPayTypeEnum.getNameByCode(categoryDO.getPayType()));
            vo.setMonth(categoryDO.getMonth());
            vo.setMonthDay(categoryDO.getMonthDay());
            vo.setDays(categoryDO.getDays());
            result.add(vo);
        }
        return result;
    }

    @Async
    @Override
    public void updateCategoryIds() {
        List<MemberBusinessCategoryDO> all = memberBusinessCategoryRepository.findAll();
        for (MemberBusinessCategoryDO categoryDO : all) {
            List<BusinessCategoryBO> categoriesList = categoryDO.getCategories();
            List<CategoryIdResp> categoryIdResps = new ArrayList<>();
            getCategoryIdVOS(categoriesList, categoryIdResps);
            if(!CollectionUtils.isEmpty(categoryIdResps)){
                categoryDO.setCategoryIdList(JSONUtil.toJsonStr(categoryIdResps));
            }
        }
        memberBusinessCategoryRepository.saveAll(all);

    }

    private List<BusinessCategoryBO> getAllCategory(List<BusinessCategoryBO>  categories1, List<BusinessCategoryBO> target) {
        // step 1:设置出栈
        if (CollectionUtils.isEmpty(categories1) ) {
            return new ArrayList<>();
        }
        // step 2:添加所有下级
        target.addAll(categories1);
        for (BusinessCategoryBO item: categories1) {
            // 循环遍历
            getAllCategory(item.getChildren(), target);
        }
        return target;
    }

    private List<BusinessCategoryBO> transitionBusinessCategoryBO(List<BusinessCategoryDetailReq> children){
        if(CollectionUtils.isEmpty(children)){
            return new ArrayList<>();
        }
        List<BusinessCategoryBO> categoryChildren = children.stream().map(category -> {
            BusinessCategoryBO businessCategoryBO = new BusinessCategoryBO();
            businessCategoryBO.setCategoryId(category.getCategoryId());
            businessCategoryBO.setLevel(category.getLevel());
            businessCategoryBO.setName(category.getName());
            businessCategoryBO.setParentId(category.getParentId());
            if (CollectionUtils.isEmpty(category.getChildren())) {
                businessCategoryBO.setChildren(new ArrayList<>());
            } else {
                businessCategoryBO.setChildren(transitionBusinessCategoryBO(category.getChildren()));
            }
            return businessCategoryBO;
        }).collect(Collectors.toList());
        return categoryChildren;
    }

    private List<BusinessCategoryDetailQueryResp> transitionBusinessCategoryDetailQueryVO(List<BusinessCategoryBO> children){
        if(CollectionUtils.isEmpty(children)){
            return new ArrayList<>();
        }
        List<BusinessCategoryDetailQueryResp> categoryChildren = children.stream().map(category -> {
            BusinessCategoryDetailQueryResp businessCategoryBO = new BusinessCategoryDetailQueryResp();
            businessCategoryBO.setCategoryId(category.getCategoryId());
            businessCategoryBO.setLevel(category.getLevel());
            businessCategoryBO.setName(category.getName());
            businessCategoryBO.setParentId(category.getParentId());
            if (CollectionUtils.isEmpty(category.getChildren())) {
                businessCategoryBO.setChildren(new ArrayList<>());
            } else {
                businessCategoryBO.setChildren(transitionBusinessCategoryDetailQueryVO(category.getChildren()));
            }
            return businessCategoryBO;
        }).collect(Collectors.toList());
        return categoryChildren;
    }

    private void getCategoryIdVOS(List<BusinessCategoryBO> children,List<CategoryIdResp> categoryIdResps){
        for (BusinessCategoryBO category : children) {
            CategoryIdResp businessCategoryBO = new CategoryIdResp();
            businessCategoryBO.setId(category.getCategoryId());
            categoryIdResps.add(businessCategoryBO);
            if (!CollectionUtils.isEmpty(category.getChildren())) {
                getCategoryIdVOS(category.getChildren(), categoryIdResps);
            }
        }
    }

    private void findAllBusinessCategoryDetail(BusinessCategoryDetailReq detail, List<BusinessCategoryDetailReq> categoryList) {
        categoryList.add(detail);
        if(!CollectionUtils.isEmpty(detail.getChildren())){
            detail.getChildren().forEach(d ->{
                findAllBusinessCategoryDetail(d,categoryList);
            });
        }
    }
}
