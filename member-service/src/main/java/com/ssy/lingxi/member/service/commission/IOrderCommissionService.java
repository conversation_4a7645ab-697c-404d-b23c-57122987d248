package com.ssy.lingxi.member.service.commission;

import com.ssy.lingxi.member.model.dto.OrderChangeCommissionDTO;

/**
 * 订单分佣处理服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-07-08
 */
public interface IOrderCommissionService {

    /**
     * 处理订单变更分佣
     *
     * @param orderChangeDTO 订单变更信息
     */
    void processOrderChangeCommission(OrderChangeCommissionDTO orderChangeDTO);

    /**
     * 处理订单创建分佣
     *
     * @param orderChangeDTO 订单变更信息
     */
    void processOrderCreatedCommission(OrderChangeCommissionDTO orderChangeDTO);

    /**
     * 处理订单完成分佣
     *
     * @param orderChangeDTO 订单变更信息
     */
    void processOrderCompletedCommission(OrderChangeCommissionDTO orderChangeDTO);

    /**
     * 处理订单取消分佣
     *
     * @param orderChangeDTO 订单变更信息
     */
    void processOrderCancelledCommission(OrderChangeCommissionDTO orderChangeDTO);
}
