package com.ssy.lingxi.member.controller.web.configManage;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.configManage.MemberRegisterConfigPageDataReq;
import com.ssy.lingxi.member.model.req.configManage.MemberRegisterConfigReq;
import com.ssy.lingxi.member.model.resp.configManage.MemberConfigEnumResp;
import com.ssy.lingxi.member.model.resp.configManage.MemberConfigFieldTypeResp;
import com.ssy.lingxi.member.model.resp.configManage.MemberRegisterConfigPageResp;
import com.ssy.lingxi.member.model.resp.configManage.MemberRegisterConfigResp;
import com.ssy.lingxi.member.service.configManage.IManageMemberRegisterConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 会员注册资料配置相关接口
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/6/30
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/registerConfig")
public class MemberRegisterConfigController {
    private final IManageMemberRegisterConfigService memberRegisterConfigService;

    /**
     * 查询会员注册资料字段类型列表
     *
     * @return 标签列表
     */
    @GetMapping("/getConfigFieldTypeList")
    public WrapperResp<List<MemberConfigFieldTypeResp>> getMemberConfigFieldTypeList() {
        return WrapperUtil.success(memberRegisterConfigService.getMemberConfigFieldTypeList());
    }

    /**
     * 查询会员注册字段标签列表
     *
     * @return 标签列表
     */
    @GetMapping("/getConfigTagList")
    public WrapperResp<List<MemberConfigEnumResp>> getMemberConfigTagList() {
        return WrapperUtil.success(memberRegisterConfigService.getMemberConfigTagList());
    }

    /**
     * 查询会员注册字段校验规则列表
     *
     * @return 校验规则列表
     */
    @GetMapping("/getConfigCheckRuleList")
    public WrapperResp<List<MemberConfigEnumResp>> getMemberConfigCheckRuleList() {
        return WrapperUtil.success(memberRegisterConfigService.getMemberConfigCheckRuleList());
    }

    /**
     * 分页查询会员注册资料配置
     *
     * @param registerConfigReq 接口参数
     * @return 注册资料配置分页列表
     */
    @GetMapping("/getRegisterConfig")
    public WrapperResp<PageDataResp<MemberRegisterConfigPageResp>> getRegisterConfigPage(@Valid MemberRegisterConfigPageDataReq registerConfigReq) {
        return WrapperUtil.success(memberRegisterConfigService.getRegisterConfigPage(registerConfigReq));
    }

    /**
     * 查询会员注册资料配置详情
     *
     * @param commonIdReq 接口参数
     * @return 注册资料配置详情
     */
    @GetMapping("/getRegisterConfigById")
    public WrapperResp<MemberRegisterConfigResp> getRegisterConfigById(@Valid CommonIdReq commonIdReq) {
        return WrapperUtil.success(memberRegisterConfigService.getRegisterConfigById(commonIdReq));
    }

    /**
     * 新增会员注册资料配置
     *
     * @param configReq 接口参数
     * @return 会员资料
     */
    @PostMapping("/add")
    public WrapperResp<MemberRegisterConfigResp> addMemberConfig(@RequestBody @Validated(MemberRegisterConfigReq.Add.class) MemberRegisterConfigReq configReq) {
        return WrapperUtil.success(memberRegisterConfigService.addMemberConfig(configReq));
    }

    /**
     * 更改会员注册资料配置状态
     *
     * @param configReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/changeStatus")
    public WrapperResp<Void> updateMemberConfigStatus(@RequestBody @Validated(MemberRegisterConfigReq.UpdateStatus.class) MemberRegisterConfigReq configReq) {
         memberRegisterConfigService.updateMemberConfigStatus(configReq);
        return WrapperUtil.success();
    }

    /**
     * 修改会员注册资料配置
     *
     * @param configVO 接口参数
     * @return 会员注册资料
     */
    @PostMapping("/update")
    public WrapperResp<MemberRegisterConfigResp> updateMemberConfig(@RequestBody @Validated(MemberRegisterConfigReq.Update.class) MemberRegisterConfigReq configVO) {
        return WrapperUtil.success(memberRegisterConfigService.updateMemberConfig(configVO));
    }

    /**
     * 删除会员注册资料配置
     *
     * @param configVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> deleteMemberConfig(@RequestBody @Validated(MemberRegisterConfigReq.Delete.class) MemberRegisterConfigReq configVO) {
         memberRegisterConfigService.deleteMemberConfig(configVO);
        return WrapperUtil.success();
    }
}
