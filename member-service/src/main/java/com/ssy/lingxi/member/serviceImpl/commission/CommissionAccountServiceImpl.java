package com.ssy.lingxi.member.serviceImpl.commission;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.commission.AccountFreezeRecordDO;
import com.ssy.lingxi.member.entity.do_.commission.BankCardDO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionAccountDO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionDetailDO;
import com.ssy.lingxi.member.model.req.commission.BindBankCardReq;
import com.ssy.lingxi.member.model.req.commission.CommissionAccountQueryReq;
import com.ssy.lingxi.member.model.req.commission.CommissionDetailQueryReq;
import com.ssy.lingxi.member.model.resp.commission.*;
import com.ssy.lingxi.member.repository.commission.*;
import com.ssy.lingxi.member.service.commission.ICommissionAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户分佣账户服务实现
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Slf4j
@Service
public class CommissionAccountServiceImpl implements ICommissionAccountService {

    @Resource
    private CommissionAccountRepository commissionAccountRepository;

    @Resource
    private BankCardRepository bankCardRepository;

    @Resource
    private CommissionWithdrawalRepository commissionWithdrawalRepository;

    @Resource
    private CommissionDetailRepository commissionDetailRepository;

    @Resource
    private AccountFreezeRecordRepository accountFreezeRecordRepository;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public PageDataResp<CommissionAccountResp> getCommissionAccountPage(UserLoginCacheDTO loginUser, CommissionAccountQueryReq request) {
        // 构建查询条件
        Specification<CommissionAccountDO> spec = buildSpecification(request);

        // 分页参数，按更新时间倒序
        Pageable pageable = PageRequest.of(
                request.getCurrent() - 1,
                request.getPageSize(),
                Sort.by(Sort.Direction.DESC, "updateTime")
        );

        // 执行分页查询
        Page<CommissionAccountDO> page = commissionAccountRepository.findAll(spec, pageable);

        // 批量获取关联数据
        List<Long> userIds = page.getContent().stream()
                .map(CommissionAccountDO::getUserId)
                .collect(Collectors.toList());

        Map<Long, MemberDO> memberMap = getMemberMap(userIds);
        Map<Long, Boolean> bankCardAuthMap = getBankCardAuthMap(userIds);
        Map<Long, BigDecimal> withdrawnAmountMap = getWithdrawnAmountMap(userIds);

        // 转换为响应对象
        List<CommissionAccountResp> list = page.getContent().stream()
                .map(account -> convertToAccountResp(account, memberMap, bankCardAuthMap, withdrawnAmountMap))
                .collect(Collectors.toList());

        return new PageDataResp<>(page.getTotalElements(), list);
    }

    @Override
    public CommissionAccountDetailResp getCommissionAccountDetail(UserLoginCacheDTO loginUser, Long accountId) {
        // 查询账户信息
        CommissionAccountDO account = commissionAccountRepository.findById(accountId)
                .orElseThrow(() -> new BusinessException("分佣账户不存在"));

        // 查询关联数据
        Optional<MemberDO> memberOpt = Optional.empty(); // 暂时为空，需要根据实际业务逻辑实现
        BigDecimal withdrawnAmount = calculateWithdrawnAmount(account.getUserId());

        // 转换为响应对象
        return convertToAccountDetail(account, memberOpt.orElse(null), withdrawnAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void freezeCommissionAccount(UserLoginCacheDTO loginUser, Long accountId) {
        CommissionAccountDO account = commissionAccountRepository.findById(accountId)
                .orElseThrow(() -> new BusinessException("分佣账户不存在"));

        if (Integer.valueOf(2).equals(account.getAccountStatus())) {
            throw new BusinessException("账户已处于冻结状态");
        }

        // 更新账户状态为冻结
        account.setAccountStatus(2);
        account.setUpdateTime(System.currentTimeMillis());
        commissionAccountRepository.saveAndFlush(account);

        // 插入冻结记录
        AccountFreezeRecordDO freezeRecord = new AccountFreezeRecordDO();
        freezeRecord.setCommissionAccountId(accountId);
        freezeRecord.setUserId(account.getUserId());
        freezeRecord.setOperationType(1); // 1-冻结
        freezeRecord.setOperatorUserId(loginUser.getUserId());
        freezeRecord.setCreateTime(System.currentTimeMillis());
        freezeRecord.setRemark("管理员冻结账户");
        accountFreezeRecordRepository.saveAndFlush(freezeRecord);

        log.info("冻结分佣账户成功，账户id：{}，操作人：{}，冻结记录id：{}", accountId, loginUser.getUserName(), freezeRecord.getId());
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void unfreezeCommissionAccount(UserLoginCacheDTO loginUser, Long accountId) {
        CommissionAccountDO account = commissionAccountRepository.findById(accountId)
                .orElseThrow(() -> new BusinessException("分佣账户不存在"));

        if (Integer.valueOf(1).equals(account.getAccountStatus())) {
            throw new BusinessException("账户已处于正常状态");
        }

        // 更新账户状态为正常
        account.setAccountStatus(1);
        account.setUpdateTime(System.currentTimeMillis());
        commissionAccountRepository.saveAndFlush(account);

        // 插入解冻记录
        AccountFreezeRecordDO unfreezeRecord = new AccountFreezeRecordDO();
        unfreezeRecord.setCommissionAccountId(accountId);
        unfreezeRecord.setUserId(account.getUserId());
        unfreezeRecord.setOperationType(2); // 2-解冻
        unfreezeRecord.setOperatorUserId(loginUser.getUserId());
        unfreezeRecord.setCreateTime(System.currentTimeMillis());
        unfreezeRecord.setRemark("管理员解冻账户");
        accountFreezeRecordRepository.saveAndFlush(unfreezeRecord);

        log.info("解冻分佣账户成功，账户id：{}，操作人：{}，解冻记录id：{}", accountId, loginUser.getUserName(), unfreezeRecord.getId());
        return null;
    }

    // ==================== 私有方法 ====================

    /**
     * 构建查询条件
     */
    private Specification<CommissionAccountDO> buildSpecification(CommissionAccountQueryReq request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 账户状态过滤
            if (request.getAccountStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("accountStatus"), request.getAccountStatus()));
            }

            // 时间范围过滤
            if (request.getStartTime() != null && request.getEndTime() != null) {
                predicates.add(criteriaBuilder.between(root.get("updateTime"), request.getStartTime(), request.getEndTime()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 批量获取会员信息
     */
    private Map<Long, MemberDO> getMemberMap(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 通过UserRepository查询用户信息，然后获取关联的会员信息
        List<UserDO> users = userRepository.findAllById(userIds);
        Map<Long, MemberDO> memberMap = new HashMap<>();

        for (UserDO user : users) {
            if (user.getMember() != null) {
                memberMap.put(user.getId(), user.getMember());
            }
        }

        return memberMap;
    }

    /**
     * 批量获取银行卡认证状态
     */
    private Map<Long, Boolean> getBankCardAuthMap(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }
        // 批量查询银行卡认证状态
        Map<Long, Boolean> resultMap = new HashMap<>();
        for (Long userId : userIds) {
            List<BankCardDO> bankCards = bankCardRepository.findByUserIdAndStatusOrderByCreateTimeDesc(userId, 1);
            resultMap.put(userId, !bankCards.isEmpty());
        }
        return resultMap;
    }

    /**
     * 批量获取已提现金额
     */
    private Map<Long, BigDecimal> getWithdrawnAmountMap(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }
        // 批量计算已提现金额
        Map<Long, BigDecimal> resultMap = new HashMap<>();
        for (Long userId : userIds) {
            BigDecimal amount = commissionWithdrawalRepository.sumSuccessfulWithdrawalAmountByUserId(userId);
            resultMap.put(userId, amount != null ? amount : BigDecimal.ZERO);
        }
        return resultMap;
    }

    /**
     * 转换为账户响应对象
     */
    private CommissionAccountResp convertToAccountResp(CommissionAccountDO account,
                                                       Map<Long, MemberDO> memberMap,
                                                       Map<Long, Boolean> bankCardAuthMap,
                                                       Map<Long, BigDecimal> withdrawnAmountMap) {
        CommissionAccountResp resp = new CommissionAccountResp();
        resp.setId(account.getId());
        resp.setUserId(account.getUserId());
        resp.setAccountBalance(account.getAccountBalance());
        resp.setWithdrawableBalance(account.getWithdrawableBalance());
        resp.setWithdrawingAmount(account.getWithdrawingAmount());
        resp.setAccountStatus(account.getAccountStatus());

        // 设置会员信息（暂时使用模拟数据）
        MemberDO member = memberMap.get(account.getUserId());
        if (member != null) {
            resp.setMemberId(member.getId());
            resp.setCustomerName(member.getName());
            resp.setCustomerCode(member.getCode());
            resp.setCustomerType(1); // 默认企业会员，实际应该通过会员角色获取
        } else {
            // 模拟数据
            resp.setMemberId(1L);
            resp.setCustomerName("客户" + account.getUserId());
            resp.setCustomerCode("C" + String.format("%06d", account.getUserId()));
            resp.setCustomerType(1);
        }

        // 设置客户类型名称
        if (resp.getCustomerType() != null) {
            resp.setCustomerTypeName(resp.getCustomerType() == 1 ? "企业会员" : "个人会员");
        }

        // 设置账户状态名称
        if (resp.getAccountStatus() != null) {
            resp.setAccountStatusName(resp.getAccountStatus() == 1 ? "正常" : "冻结");
        }

        // 设置银行卡认证状态
        Boolean authenticated = bankCardAuthMap.getOrDefault(account.getUserId(), false);
        resp.setBankCardAuthenticated(authenticated);
        resp.setBankCardAuthenticatedName(authenticated ? "已认证" : "未认证");

        // 设置已提现金额
        BigDecimal withdrawnAmount = withdrawnAmountMap.getOrDefault(account.getUserId(), BigDecimal.ZERO);
        resp.setWithdrawnAmount(withdrawnAmount);

        // 格式化更新时间
        resp.setUpdateTime(formatTime(account.getUpdateTime()));

        return resp;
    }

    /**
     * 转换为账户详情响应对象
     */
    private CommissionAccountDetailResp convertToAccountDetail(CommissionAccountDO account,
                                                               MemberDO member,
                                                               BigDecimal withdrawnAmount) {
        CommissionAccountDetailResp detail = new CommissionAccountDetailResp();
        detail.setId(account.getId());
        detail.setUserId(account.getUserId());
        detail.setAccountBalance(account.getAccountBalance());
        detail.setWithdrawableBalance(account.getWithdrawableBalance());
        detail.setWithdrawingAmount(account.getWithdrawingAmount());
        detail.setAccountStatus(account.getAccountStatus());

        // 设置会员信息
        if (member != null) {
            detail.setMemberId(member.getId());
            detail.setCustomerName(member.getName());
            detail.setCustomerCode(member.getCode());
            detail.setCustomerType(1); // 默认企业会员，实际应该通过会员角色获取
            detail.setCustomerPhone(member.getPhone());
            detail.setCustomerEmail(member.getEmail());
        }

        // 设置客户类型名称
        if (detail.getCustomerType() != null) {
            detail.setCustomerTypeName(detail.getCustomerType() == 1 ? "企业会员" : "个人会员");
        }

        // 设置账户状态名称
        if (detail.getAccountStatus() != null) {
            detail.setAccountStatusName(detail.getAccountStatus() == 1 ? "正常" : "冻结");
        }


        // 设置各种金额
        detail.setWithdrawnAmount(withdrawnAmount);
        detail.setTotalEarningsAmount(BigDecimal.ZERO); // 需要根据实际业务逻辑计算
        if (detail.getAccountBalance() != null && detail.getWithdrawableBalance() != null) {
            detail.setToBeEarningsAmount(detail.getAccountBalance().subtract(detail.getWithdrawableBalance()));
        }
        // 格式化时间
        detail.setCreateTime(formatTime(account.getCreateTime()));
        detail.setUpdateTime(formatTime(account.getUpdateTime()));

        return detail;
    }

    /**
     * 计算已提现金额
     */
    private BigDecimal calculateWithdrawnAmount(Long userId) {
        if (userId == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal amount = commissionWithdrawalRepository.sumSuccessfulWithdrawalAmountByUserId(userId);
        return amount != null ? amount : BigDecimal.ZERO;
    }


    /**
     * 格式化时间戳
     */
    private String formatTime(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.ofHours(8))
                .format(DATE_TIME_FORMATTER);
    }

    @Override
    public PageDataResp<CommissionDetailResp> getCommissionDetailPage(UserLoginCacheDTO loginUser, CommissionDetailQueryReq request) {
        // 构建查询条件
        Specification<CommissionDetailDO> spec = buildCommissionDetailSpecification(request);

        // 分页参数，按交易时间倒序
        Pageable pageable = PageRequest.of(
                request.getCurrent() - 1,
                request.getPageSize(),
                Sort.by(Sort.Direction.DESC, "tradeTime")
        );

        // 执行分页查询
        Page<CommissionDetailDO> page = commissionDetailRepository.findAll(spec, pageable);

        // 转换为响应对象
        List<CommissionDetailResp> list = page.getContent().stream()
                .map(this::convertToCommissionDetailResp)
                .collect(Collectors.toList());

        return new PageDataResp<>(page.getTotalElements(), list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long bindBankCard(UserLoginCacheDTO loginUser, BindBankCardReq request) {
        Long userId = Objects.isNull(request.getUserId()) ? loginUser.getUserId() : request.getUserId();
        log.info("用户绑定银行卡开始，userId: {}, cardHolderName: {}", userId, request.getCardHolderName());

        // 1. 校验银行卡号是否已存在
        String cardNumber = request.getCardNumber();
        Optional<BankCardDO> existingCardOpt = bankCardRepository.findByCardNumber(cardNumber);
        if (existingCardOpt.isPresent()) {
            BankCardDO existingCard = existingCardOpt.get();
            if (existingCard.getStatus() == 1) { // 正常状态
                log.warn("银行卡号已存在，cardNumber: {}", maskCardNumberToLast4(cardNumber));
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "该银行卡已被绑定");
            }
        }


        // 3. 创建银行卡记录
        BankCardDO bankCard = new BankCardDO();
        bankCard.setUserId(userId);
        bankCard.setCardHolderName(request.getCardHolderName());
        bankCard.setCardNumber(cardNumber);
        bankCard.setBankName(request.getBankName());
        bankCard.setBankCode(request.getBankCode());
        bankCard.setBranchName(request.getBranchName());
        bankCard.setPhoneNumber(request.getPhoneNumber());
        bankCard.setIdCardNumber(request.getIdCardNumber());
        bankCard.setStatus(1); // 1-正常
        long currentTime = System.currentTimeMillis();
        bankCard.setCreateTime(currentTime);
        bankCard.setUpdateTime(currentTime);

        // 4. 保存到数据库
        BankCardDO savedCard = bankCardRepository.save(bankCard);

        log.info("用户绑定银行卡成功，userId: {}, bankCardId: {}, cardNumber: {}",
                userId, savedCard.getId(), maskCardNumberToLast4(cardNumber));

        return savedCard.getId();
    }

    @Override
    public List<BankCardResp> getBankCardList(UserLoginCacheDTO loginUser, Long userId) {
        // 查询用户的银行卡列表（排除已删除的）
        List<BankCardDO> bankCards = bankCardRepository.findByUserIdAndStatusOrderByCreateTimeDesc(userId, 1);
        if (CollectionUtil.isEmpty(bankCards)) {
            return new ArrayList<>();
        }
        // 转换为响应对象
        return bankCards.stream()
                .map(this::convertToBankCardResp)
                .collect(Collectors.toList());
    }

    @Override
    public List<AccountFreezeRecordResp> getAccountFreezeRecordList(UserLoginCacheDTO loginUser, Long commissionAccountId) {
        // 查询冻结记录列表，按创建时间倒序（不分页，获取所有记录）
        Pageable unpaged = Pageable.unpaged();
        Page<AccountFreezeRecordDO> recordPage = accountFreezeRecordRepository.findByCommissionAccountIdOrderByCreateTimeDesc(commissionAccountId, unpaged);
        List<AccountFreezeRecordDO> records = recordPage.getContent();

        // 转换为响应对象
        return records.stream()
                .map(this::convertToAccountFreezeRecordResp)
                .collect(Collectors.toList());
    }

    // ==================== 新增的私有辅助方法 ====================

    /**
     * 构建佣金明细查询条件
     */
    private Specification<CommissionDetailDO> buildCommissionDetailSpecification(CommissionDetailQueryReq request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 分佣账户id过滤
            if (request.getCommissionAccountId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("commissionAccountId"), request.getCommissionAccountId()));
            }

            // 时间范围过滤
            if (request.getStartTime() != null && request.getEndTime() != null) {
                predicates.add(criteriaBuilder.between(root.get("tradeTime"), request.getStartTime(), request.getEndTime()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }


    /**
     * 转换为佣金明细响应对象
     */
    private CommissionDetailResp convertToCommissionDetailResp(CommissionDetailDO detail) {
        CommissionDetailResp resp = new CommissionDetailResp();
        resp.setId(detail.getId());
        resp.setCommissionAccountId(detail.getCommissionAccountId());
        resp.setUserId(detail.getUserId());
        resp.setTradeCode(detail.getTradeCode());
        resp.setTradeType(detail.getTradeType());
        resp.setChangeAmount(detail.getChangeAmount());
        // 这些字段在实际的CommissionDetailDO中可能不存在，暂时设置默认值
        // resp.setBalanceAfter(BigDecimal.ZERO); // CommissionDetailResp中没有此字段
        resp.setRelatedOrderNo(detail.getRemark()); // 使用备注字段代替
        resp.setInvitedUserId(null); // 需要根据实际字段调整
        resp.setInvitedUserName(""); // 需要根据实际字段调整
        resp.setRemark(detail.getRemark());

        // 设置交易类型名称
        resp.setTradeTypeName(getTradeTypeName(detail.getTradeType()));

        // 格式化交易时间
        resp.setTradeTime(formatTime(detail.getTradeTime()));

        return resp;
    }


    /**
     * 转换为银行卡响应对象
     */
    private BankCardResp convertToBankCardResp(BankCardDO bankCard) {
        if (Objects.isNull(bankCard)) {
            return null;
        }
        BankCardResp resp = new BankCardResp();
        resp.setId(bankCard.getId());
        resp.setUserId(bankCard.getUserId());
        resp.setCardHolderName(bankCard.getCardHolderName());
        resp.setCardNumber(maskCardNumberToLast4(bankCard.getCardNumber())); // 只显示后4位
        resp.setBankName(bankCard.getBankName());
        resp.setBankCode(bankCard.getBankCode());
        resp.setBranchName(bankCard.getBranchName());
        resp.setStatus(bankCard.getStatus());
        resp.setPhoneNumber(maskPhoneNumber(bankCard.getPhoneNumber()));

        // 设置状态名称
        resp.setStatusName(getBankCardStatusName(bankCard.getStatus()));

        // 格式化时间
        resp.setCreateTime(formatTime(bankCard.getCreateTime()));
        resp.setUpdateTime(formatTime(bankCard.getUpdateTime()));

        return resp;
    }

    /**
     * 转换为冻结记录响应对象
     */
    private AccountFreezeRecordResp convertToAccountFreezeRecordResp(AccountFreezeRecordDO record) {
        AccountFreezeRecordResp resp = new AccountFreezeRecordResp();
        resp.setId(record.getId());
        resp.setCommissionAccountId(record.getCommissionAccountId());
        resp.setUserId(record.getUserId());
        resp.setOperationType(record.getOperationType());
        resp.setOperatorUserId(record.getOperatorUserId());
        resp.setOperatorUserName("系统管理员"); // 需要根据实际字段调整
        resp.setRemark(record.getRemark());

        // 设置操作类型名称
        resp.setOperationTypeName(getOperationTypeName(record.getOperationType()));

        // 格式化时间
        resp.setCreateTime(formatTime(record.getCreateTime()));

        return resp;
    }

    /**
     * 银行卡号只显示后4位
     */
    private String maskCardNumberToLast4(String cardNumber) {
        if (StrUtil.isBlank(cardNumber) || cardNumber.length() < 4) {
            return cardNumber;
        }
        return "****" + cardNumber.substring(cardNumber.length() - 4);
    }

    /**
     * 手机号脱敏
     */
    private String maskPhoneNumber(String phoneNumber) {
        if (StrUtil.isBlank(phoneNumber) || phoneNumber.length() < 7) {
            return phoneNumber;
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 4);
    }

    /**
     * 获取交易类型名称
     */
    private String getTradeTypeName(Integer tradeType) {
        if (tradeType == null) {
            return "";
        }
        switch (tradeType) {
            case 1:
                return "邀请注册奖励";
            case 2:
                return "邀请认证奖励";
            case 3:
                return "邀请首单奖励";
            case 4:
                return "下单分佣";
            default:
                return "未知类型";
        }
    }


    /**
     * 获取银行卡状态名称
     */
    private String getBankCardStatusName(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "已停用";
            case 3:
                return "已删除";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取操作类型名称
     */
    private String getOperationTypeName(Integer operationType) {
        if (operationType == null) {
            return "";
        }
        switch (operationType) {
            case 1:
                return "冻结";
            case 2:
                return "解冻";
            default:
                return "未知操作";
        }
    }


    @Override
    public void exportCommissionAccountList(UserLoginCacheDTO loginUser, CommissionAccountQueryReq request, HttpServletResponse response) {
        // 构建查询条件（不分页，查询所有符合条件的数据）
        Specification<CommissionAccountDO> spec = buildSpecification(request);

        // 按更新时间倒序查询所有数据
        List<CommissionAccountDO> accountList = commissionAccountRepository.findAll(spec,
                Sort.by(Sort.Direction.DESC, "updateTime"));

        // 批量获取关联数据
        List<Long> userIds = accountList.stream()
                .map(CommissionAccountDO::getUserId)
                .collect(Collectors.toList());

        Map<Long, MemberDO> memberMap = getMemberMap(userIds);
        Map<Long, Boolean> bankCardAuthMap = getBankCardAuthMap(userIds);
        Map<Long, BigDecimal> withdrawnAmountMap = getWithdrawnAmountMap(userIds);

        // 转换为导出数据
        List<Map<String, Object>> dataList = accountList.stream()
                .map(account -> convertToExportData(account, memberMap, bankCardAuthMap, withdrawnAmountMap))
                .collect(Collectors.toList());

        // 定义表头
        List<String> headers = Arrays.asList(
                "客户名称", "客户编码", "客户类型", "银行卡认证状态",
                "账户余额（元）", "可提现余额（元）", "提现中金额（元）", "已提现金额（元）",
                "更新时间", "账户状态"
        );

        try {
            // 生成文件名
            String fileName = "客户分佣账户列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));

            // 使用hutool的ExcelWriter导出
            ExcelWriter writer = ExcelUtil.getBigWriter();

            // 设置表头
            for (int i = 0; i < headers.size(); i++) {
                writer.addHeaderAlias(headers.get(i), headers.get(i));
            }

            // 写入数据
            writer.write(dataList, true);

            // 设置响应头
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));

            // 输出到响应流
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
            out.close();

            log.info("导出客户分佣账户列表成功，操作人：{}，导出条数：{}", loginUser.getUserName(), dataList.size());
        } catch (Exception e) {
            log.error("导出客户分佣账户列表失败，操作人：{}，错误信息：{}", loginUser.getUserName(), e.getMessage(), e);
            throw new BusinessException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 转换为导出数据
     */
    private Map<String, Object> convertToExportData(CommissionAccountDO account,
                                                    Map<Long, MemberDO> memberMap,
                                                    Map<Long, Boolean> bankCardAuthMap,
                                                    Map<Long, BigDecimal> withdrawnAmountMap) {
        Map<String, Object> data = new LinkedHashMap<>();

        // 设置会员信息（暂时使用模拟数据）
        MemberDO member = memberMap.get(account.getUserId());
        if (member != null) {
            data.put("客户名称", member.getName());
            data.put("客户编码", member.getCode());
            data.put("客户类型", "企业会员"); // 默认企业会员，实际应该通过会员角色获取
        } else {
            // 模拟数据
            data.put("客户名称", "客户" + account.getUserId());
            data.put("客户编码", "C" + String.format("%06d", account.getUserId()));
            data.put("客户类型", "企业会员");
        }

        // 银行卡认证状态
        Boolean bankCardAuth = bankCardAuthMap.get(account.getUserId());
        data.put("银行卡认证状态", (bankCardAuth != null && bankCardAuth) ? "已认证" : "未认证");

        // 账户金额信息
        data.put("账户余额（元）", account.getAccountBalance() != null ? account.getAccountBalance() : BigDecimal.ZERO);
        data.put("可提现余额（元）", account.getWithdrawableBalance() != null ? account.getWithdrawableBalance() : BigDecimal.ZERO);
        data.put("提现中金额（元）", account.getWithdrawingAmount() != null ? account.getWithdrawingAmount() : BigDecimal.ZERO);

        // 已提现金额
        BigDecimal withdrawnAmount = withdrawnAmountMap.get(account.getUserId());
        data.put("已提现金额（元）", withdrawnAmount != null ? withdrawnAmount : BigDecimal.ZERO);

        // 时间信息
        data.put("更新时间", formatTime(account.getUpdateTime()));

        // 账户状态
        data.put("账户状态", getAccountStatusName(account.getAccountStatus()));

        return data;
    }

    /**
     * 获取账户状态名称
     */
    private String getAccountStatusName(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return "正常";
            case 2:
                return "冻结";
            default:
                return "未知状态";
        }
    }
}
