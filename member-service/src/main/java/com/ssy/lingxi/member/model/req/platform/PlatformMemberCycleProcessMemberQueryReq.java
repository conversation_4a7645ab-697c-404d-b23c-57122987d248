package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 平台后台分页查询流程关联会员
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@Data
public class PlatformMemberCycleProcessMemberQueryReq implements Serializable {

    private static final long serialVersionUID = -299950348913232407L;

    /**
     * 物料流程规则配置id
     */
    @NotNull(message = "流程规则配置id要大于0")
    @Positive(message = "流程规则配置id要大于0")
    private Long processId;

    /**
     * 会员名称
     */
    private String name;

}
