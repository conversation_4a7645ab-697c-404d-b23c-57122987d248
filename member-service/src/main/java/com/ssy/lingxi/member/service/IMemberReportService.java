package com.ssy.lingxi.member.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.ReportTodayResp;
import com.ssy.lingxi.member.model.resp.MemberReportResp;

/**
 * 首页-会员中心
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/12/8
 */
public interface IMemberReportService {

    /**
     * 待办统计
     * @param sysUser 登录用户
     **/
    MemberReportResp getMember(UserLoginCacheDTO sysUser);

    /**
     * 今日新增--平台后台
     */
    ReportTodayResp getTodayNew();
}
