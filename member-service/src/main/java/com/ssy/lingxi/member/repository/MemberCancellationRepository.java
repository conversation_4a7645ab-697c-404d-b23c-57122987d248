package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberCancellationDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/2
 */
@Repository
public interface MemberCancellationRepository extends JpaRepository<MemberCancellationDO, Long>, JpaSpecificationExecutor<MemberCancellationDO> {
}
