package com.ssy.lingxi.member.service.openapi;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.api.model.req.MemberOpenApiCreateReq;
import com.ssy.lingxi.member.api.model.resp.MemberOpenApiCreateResultResp;

/**
 * OpenApi - 会员创建相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-10-15
 */
public interface IMemberOpenApiCreationService {
    /**
     * 创建会员
     * @param createVO 接口参数
     * @return 创建结果
     */
    WrapperResp<MemberOpenApiCreateResultResp> createMember(MemberOpenApiCreateReq createVO);
}
