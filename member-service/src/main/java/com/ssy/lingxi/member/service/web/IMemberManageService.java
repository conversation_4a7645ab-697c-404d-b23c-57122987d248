package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.api.model.resp.MemberManageQueryResp;
import com.ssy.lingxi.member.model.req.basic.MemberNameDataReq;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.basic.UserPageDataReq;
import com.ssy.lingxi.member.model.req.manage.*;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.UserQueryResp;
import com.ssy.lingxi.member.model.resp.manage.*;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 平台后台及业务平台 - 会员查询服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-03
 */
public interface IMemberManageService {
    /**
     * 查询所有角色
     * @param headers Http头部信息
     * @param roleTypeVO 接口参数
     * @return 角色列表
     */
    List<RoleIdAndNameResp> allRoles(HttpHeaders headers, MemberManageRoleTypeReq roleTypeVO);

    /**
     * 根据下级会员名称，模糊分页查询下级会员列表
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberManageQueryResp> pageLowerMembersByName(HttpHeaders headers, MemberManagePageByNameDataReq pageVO);

    /**
     * 根据下级会员名称、下级会员角色Id，模糊分页查询下级会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberManageQueryResp> pageLowerMembersByNameAndRole(HttpHeaders headers, MemberManagePageByNameAndRoleIdDataReq pageVO);

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务提供者的下级会员列表
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberManageQueryResp> pageLowerProviderMembersByName(HttpHeaders headers, MemberManageNameWithExcludePageDataReq pageVO);

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务消费者的下级会员列表
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberManageQueryResp> pageLowerConsumerMembersByName(HttpHeaders headers, MemberManageNameWithExcludePageDataReq pageVO);

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务消费者的下级会员列表
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberManageQueryResp> pageLowerConsumerMembersByName(HttpHeaders headers, MemberManagePageByNameDataReq pageVO);


    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务提供者的下级“企业会员”列表
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberManageQueryResp> pageLowerMerchantProviderMembersByName(HttpHeaders headers, MemberManagePageByNameDataReq pageVO);

    /**
     * 根据会员名称等条件+商城类型分页查询下属会员列表
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberManageQueryResp> pageMemberByNameAndMallType(HttpHeaders headers, MemberManagePageByNameAndMallTypeDataReq pageVO);

    /**
     * 根据上级会员Id和上级会员角色Id，以及当前用户，查询价格权益参数设置
     * @param headers HttpHeaders信息
     * @param upperVO 接口参数
     * @return 操作结果
     */
    MemberManageMemberCreditParameterResp getUpperMemberCreditParameter(HttpHeaders headers, MemberManageUpperMemberAndRoleReq upperVO);

    /**
     * 根据当前用户（上级会员），查询下级会员的价格权益参数设置
     * @param headers HttpHeaders信息
     * @param subVO 接口参数
     * @return 操作结果
     */
    MemberManageMemberCreditParameterResp getLowerMemberCreditParameter(HttpHeaders headers, SubMemberIdRoleIdDataReq subVO);

    /**
     * 根据下单类型和会员名称，模糊分页查询会员
     * @param headers HttpHeaders信息
     * @param typeVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberManageQueryResp> pageMembersByOrderTypeAndName(HttpHeaders headers, MemberManagePageByOrderTypeAndNameDataReq typeVO);

    /**
     * 分页查询会员列表页面搜索条件内容
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    MemberManageSearchConditionResp getPageSearchConditions(HttpHeaders headers, MemberManageMemberItemReq itemVO);

    /**
     * 根据当前登录会员，查询下级会员角色列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<RoleIdAndNameResp> getSubRoleListByMember(HttpHeaders headers);

    /**
     * “新增仓位存储”：根据商城类型分页查询会员
     * @param headers HttpHeaders信息
     * @param typeVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageMembersByShopType(HttpHeaders headers, MemberManagePageByShopTypeDataReq typeVO);

    /**
     * 分页查询角色为“服务提供者”的会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageMembersByServiceProviderRole(HttpHeaders headers, MemberManagePageDataReq pageVO);

    /**
     * 分页查询角色为“服务消费者”的平台会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageMembersByServiceConsumerRole(HttpHeaders headers, MemberManagePageDataReq pageVO);

    /**
     * 根据会员名称，分页查询角色类型为服务提供者的平台商户会员
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pagePlatformServiceProviderMerchantMember(HttpHeaders headers, MemberManagePageByNameDataReq pageVO);

    /**
     * 根据会员名称，分页查询角色类型为服务提供者的平台企业会员（非企业个人会员）
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pagePlatformServiceProviderEnterpriseMember(HttpHeaders headers, MemberManagePageByNameDataReq pageVO);

    /**
     * 根据会员名称，分页查询角色为服务提供者的上级会员列表
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageUpperProviderMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO);

    /**
     * 根据会员名称，分页查询上级会员列表
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageUpperMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO);


    /**
     * “售后能力 - 提交换货申请单” - 选择供应会员
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageAfterSaleMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO);

    /**
     * “物流能力 - 新增物流公司” - 选择物流服务商
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageLogisticSubMember(HttpHeaders headers, MemberManagePageLogisticsDataReq pageVO);

    /**
     * 分页查询会员下属用户
     * @param headers Http头部新
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<UserQueryResp> pageUsers(HttpHeaders headers, UserPageDataReq pageVO);

    /**
     * "适用会员等级", 查询等级配置详情
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberSuitableLevelConfigResp> pageMarketingSuitableLevelConfig(HttpHeaders headers, MemberLevelDetailPageRespData pageVO);

    /**
     * “营销能力” - 获取适用会员查询条件
     * @param headers Http头部信息
     * @param typeVO 接口参数
     * @return 查询结果
     */
    MemberSuitableConditionResp getMarketingSuitableCondition(HttpHeaders headers, SuitableMemberTypeReq typeVO);

    /**
     * “平台营销” - 获取适用会员查询条件
     * @param headers Http头部信息
     * @param typeVO 接口参数
     * @return 查询结果
     */
    PlatformMemberSuitableConditionResp getPlatformMarketingSuitableCondition(HttpHeaders headers, PlatformSuitableMemberTypeReq typeVO);

    /**
     * “营销能力” - 查询适用会员(分页)
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberSuitableResp> pageMarketingSuitable(HttpHeaders headers, MarketingSuitablePageDataReq pageVO);

    /**
     * “平台营销” - 查询适用会员(分页)
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberSuitableResp> pagePlatformMarketingSuitable(HttpHeaders headers, MarketingSuitablePageDataReq pageVO);

    /**
     * “平台营销” - 获取邀请报名参加会员查询条件
     * @param headers Http头部信息
     * @return 查询结果
     */
    PlatformMemberInviteConditionResp getPlatformMarketingInviteCondition(HttpHeaders headers);

    /**
     * “平台营销” - 查询邀请报名参加会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<PlatformMarketingInvitePageResp> pagePlatformMarketingInvite(HttpHeaders headers, PlatformMarketingInviteQueryDataReq pageVO);

    /**
     * “订单服务 - 代客下单” - 查询角色为服务消费者的平台会员列表
     * @param headers Http头部信息
     * @param nameVO 接口参数
     * @return 查询结果
     */
    List<MemberManageQueryResp> findPlatformConsumerMembers(HttpHeaders headers, MemberManageNameReq nameVO);

    /**
     * “订单服务 - 新增现货采购订单” - 查询供应会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageBuyerOrderMembers(HttpHeaders headers, MemberManagePageByNameDataReq pageVO);

    /**
     * 根据会员名称，分页查询角色为服务提供者的上级会员列表(增加条件会员等级类型为商户会员)
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageUpperProviderMerchantMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO);

    /**
     * 根据会员名称，分页查询角色为服务消费者的上级会员列表(增加条件会员等级类型为商户会员)
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageUpperConsumerMerchantMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO);

    /**
     * 根据会员名称，分页查询角色为当前会员所属的下属会员且角色类型为服务提供的会员
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> pageLowerProviderMerchantMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO);

    /**
     * “选择采购会员”功能
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 新增结果
     */
    PageDataResp<MemberManageQueryResp> buyerMemberInfo(HttpHeaders headers, MemberNameDataReq nameVO);

    /**
     * “选择供应会员”功能
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 新增结果
     */
    PageDataResp<MemberManageQueryResp> supplyMemberInfo(HttpHeaders headers, MemberNameDataReq nameVO);

    /**
     * “选择采购会员”功能 - 用于流程规则
     * 1、当前会员的会员类型是企业会员或个人会员且会员角色类型是服务提供则客户数据来源于【会员能力--会员管理--会员信息】中当前会员的下级会员且会员类型为企业会员或个人会员且会员角色类型为服务消费的会员数据
     * 2、当前会员的会员类型是渠道企业会员或渠道个人会员且会员角色类型是服务提供时，客户数据来源于【会员能力--会员管理--会员信息】中当前渠道会员的下级渠道会员且会员类型为渠道企业会员或渠道个人会员且会员角色类型为服务消费
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 新增结果
     */
    PageDataResp<MemberManageQueryResp> processBuyerMember(HttpHeaders headers, MemberNameDataReq nameVO);

    /**
     * 选择客户功能 - 用于配置流程引擎
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 采购会员信息
     */
    PageDataResp<MemberManageQueryResp> customerList(HttpHeaders headers, MemberNameDataReq nameVO);
}
