package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.component.base.model.req.ProvinceCityCodeReq;
import com.ssy.lingxi.member.handler.annotation.CurrencyTypeAnnotation;
import com.ssy.lingxi.member.handler.annotation.MemberClassifyCodeAnnotation;
import com.ssy.lingxi.member.handler.annotation.MemberPartnerTypeAnnotation;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 会员“待入库分类”审核接口参数BaseOrderPaymentServiceImplBaseOrderPaymentServiceImpl
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberToClassifyDepositReq extends MemberDepositReq implements Serializable {
    private static final long serialVersionUID = -1429891197820894279L;

    /**
     * 会员编码
     */
    @MemberClassifyCodeAnnotation
    private String code;

    /**
     * 合作关系类型枚举
     */
    @MemberPartnerTypeAnnotation
    private Integer partnerType;

    /**
     * 单次合作金额
     */
    @NotNull(message = "合作金额不能为空")
    @PositiveOrZero(message = "合作金额要大于等于0")
    @Max(value = 1000000000, message = "单次合作金额不能超过10的9次方")
    private BigDecimal maxAmount;

    /**
     * 适用区域编码列表
     */
    @Valid
    private List<ProvinceCityCodeReq> areaCodes;

    /**
     * 主营品类列表
     */
    @NotEmpty(message = "请选择主营品类")
    @Valid
    private List<BusinessCategoryReq> categories;

    /**
     * 币别枚举
     * 1: CNY-人民币
     * 2: USD-美元
     * 3: JPY-日元
     * 4: EUR-欧元
     */
    @CurrencyTypeAnnotation
    private Integer currencyType;

    /**
     * 备注
     */
    @Size(max = 200, message = "备注最长200个字符")
    private String remark;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getPartnerType() {
        return partnerType;
    }

    public void setPartnerType(Integer partnerType) {
        this.partnerType = partnerType;
    }

    public BigDecimal getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(BigDecimal maxAmount) {
        this.maxAmount = maxAmount;
    }

    public List<ProvinceCityCodeReq> getAreaCodes() {
        return areaCodes;
    }

    public void setAreaCodes(List<ProvinceCityCodeReq> areaCodes) {
        this.areaCodes = areaCodes;
    }

    public List<BusinessCategoryReq> getCategories() {
        return categories;
    }

    public void setCategories(List<BusinessCategoryReq> categories) {
        this.categories = categories;
    }
}
