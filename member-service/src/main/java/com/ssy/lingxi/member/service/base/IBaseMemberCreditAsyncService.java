package com.ssy.lingxi.member.service.base;

/**
 * 会员信用相关计算异步接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-11-18
 */
public interface IBaseMemberCreditAsyncService {

    /**
     * 计算会员交易评价信用积分
     * @param upperMemberId 上级会员Id（评价发起方）
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId  下级会员Id  （被评论方）
     * @param subRoleId   下级会员角色Id
     * @param commentStar 评论星级
     */
    void calculateMemberTradeCommentCredit(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Integer commentStar);

    /**
     * 计算会员投诉信用积分
     * @param upperMemberId 上级会员Id（投诉发起方）
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId  下级会员Id  （被投诉方）
     * @param subRoleId   下级会员角色Id
     */
    void calculateMemberTradeComplainCredit(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId);

    /**
     * 计算会员售后评价信用积分
     * @param upperMemberId 上级会员Id（评论发起方）
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId  下级会员Id  （被评论方）
     * @param subRoleId   下级会员角色Id
     * @param afterSaleTime 售后时间
     * @param star        评论星级
     * @param product     售后商品
     * @param comment      评论内容
     * @param orderNo     售后单号
     */
    void calculateMemberAfterSaleCommentCredit(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, String afterSaleTime, Integer star, String product, String comment, String orderNo);


    /**
     * 计算会员（下级会员）注册年数信用积分
     * @param relId 关系表id
     */
    void calculateMemberRegisterYearsCredit(Long relId);
}
