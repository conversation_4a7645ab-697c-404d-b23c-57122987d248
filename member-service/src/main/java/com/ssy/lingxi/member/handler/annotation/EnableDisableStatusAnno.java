package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.EnableDisableStatusValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 转态字段（0-停止 1-启用）参数校验注解
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-15
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {EnableDisableStatusValidator.class})
public @interface EnableDisableStatusAnno {
    boolean required() default true;

    String message() default "参数值范围： 0-否 1-是";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
