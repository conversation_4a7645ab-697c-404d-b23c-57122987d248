package com.ssy.lingxi.member.repository.commission;

import com.ssy.lingxi.member.entity.do_.commission.CommissionWithdrawalDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 佣金提现明细Repository
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Repository
public interface CommissionWithdrawalRepository extends JpaRepository<CommissionWithdrawalDO, Long>, JpaSpecificationExecutor<CommissionWithdrawalDO> {

    /**
     * 根据分佣账户id查询提现记录列表
     *
     * @param commissionAccountId 分佣账户id
     * @param pageable            分页参数
     * @return 提现记录列表
     */
    Page<CommissionWithdrawalDO> findByCommissionAccountIdOrderByCreateTimeDesc(Long commissionAccountId, Pageable pageable);

    /**
     * 根据用户id查询提现记录列表
     *
     * @param userId   用户id
     * @param pageable 分页参数
     * @return 提现记录列表
     */
    Page<CommissionWithdrawalDO> findByUserIdOrderByCreateTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据提现流水号查询提现记录
     *
     * @param withdrawalTradeCode 提现流水号
     * @return 提现记录
     */
    Optional<CommissionWithdrawalDO> findByWithdrawalTradeCode(String withdrawalTradeCode);

    /**
     * 检查提现流水号是否存在
     *
     * @param withdrawalTradeCode 提现流水号
     * @return 是否存在
     */
    boolean existsByWithdrawalTradeCode(String withdrawalTradeCode);

    /**
     * 根据提现状态查询提现记录列表
     *
     * @param withdrawalStatus 提现状态
     * @param pageable         分页参数
     * @return 提现记录列表
     */
    Page<CommissionWithdrawalDO> findByWithdrawalStatusOrderByCreateTimeDesc(Integer withdrawalStatus, Pageable pageable);

    /**
     * 根据银行卡id查询提现记录列表
     *
     * @param bankCardId 银行卡id
     * @param pageable   分页参数
     * @return 提现记录列表
     */
    Page<CommissionWithdrawalDO> findByBankCardIdOrderByCreateTimeDesc(Long bankCardId, Pageable pageable);

    /**
     * 根据用户id和提现状态查询提现记录列表
     *
     * @param userId           用户id
     * @param withdrawalStatus 提现状态
     * @param pageable         分页参数
     * @return 提现记录列表
     */
    Page<CommissionWithdrawalDO> findByUserIdAndWithdrawalStatusOrderByCreateTimeDesc(Long userId, Integer withdrawalStatus, Pageable pageable);

    /**
     * 查询指定时间范围内的提现记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageable  分页参数
     * @return 提现记录列表
     */
    @Query("SELECT cw FROM CommissionWithdrawalDO cw WHERE cw.createTime >= :startTime AND cw.createTime <= :endTime ORDER BY cw.createTime DESC")
    Page<CommissionWithdrawalDO> findByCreateTimeBetween(@Param("startTime") Long startTime, @Param("endTime") Long endTime, Pageable pageable);

    /**
     * 查询用户待处理的提现申请
     *
     * @param userId 用户id
     * @return 待处理的提现申请列表
     */
    @Query("SELECT cw FROM CommissionWithdrawalDO cw WHERE cw.userId = :userId AND cw.withdrawalStatus IN (1, 2) ORDER BY cw.createTime DESC")
    List<CommissionWithdrawalDO> findPendingWithdrawalsByUserId(@Param("userId") Long userId);

    /**
     * 统计用户总提现金额
     *
     * @param userId 用户id
     * @return 总提现金额
     */
    @Query("SELECT COALESCE(SUM(cw.withdrawalAmount), 0) FROM CommissionWithdrawalDO cw WHERE cw.userId = :userId AND cw.withdrawalStatus = 3")
    BigDecimal sumSuccessfulWithdrawalAmountByUserId(@Param("userId") Long userId);

    /**
     * 统计用户提现中的金额
     *
     * @param userId 用户id
     * @return 提现中的金额
     */
    @Query("SELECT COALESCE(SUM(cw.withdrawalAmount), 0) FROM CommissionWithdrawalDO cw WHERE cw.userId = :userId AND cw.withdrawalStatus IN (1, 2)")
    BigDecimal sumPendingWithdrawalAmountByUserId(@Param("userId") Long userId);

    /**
     * 统计指定状态的提现记录数量
     *
     * @param withdrawalStatus 提现状态
     * @return 记录数量
     */
    Long countByWithdrawalStatus(Integer withdrawalStatus);

}
