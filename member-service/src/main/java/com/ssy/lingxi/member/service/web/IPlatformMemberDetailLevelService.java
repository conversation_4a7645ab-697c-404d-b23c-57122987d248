package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailLevelHistoryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;
import org.springframework.http.HttpHeaders;

/**
 * 平台后台 - 会员维护 - 会员详情 - 等级信息服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
public interface IPlatformMemberDetailLevelService {

    /**
     * 查询会员详情 - 会员等级信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberValidateDetailLevelResp getMemberDetailLevel(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 分页查询会员等级历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

}
