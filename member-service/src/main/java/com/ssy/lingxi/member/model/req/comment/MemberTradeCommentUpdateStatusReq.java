package com.ssy.lingxi.member.model.req.comment;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 订单评价接口查询参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@Data
public class MemberTradeCommentUpdateStatusReq implements Serializable {
    private static final long serialVersionUID = 7460699713611960655L;

    /**
     * 记录id
     */
    @NotNull(message = "交易评价历史记录id不能为空")
    private Long id;

    /**
     * 是否屏蔽 1-显示 2-隐藏
     */
    @NotNull(message = "是否屏蔽不能为空")
    private Integer status;
}
