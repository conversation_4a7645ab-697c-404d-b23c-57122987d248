package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.BaseLevelRuleDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 基础升级规则Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-23
 */
@Repository
public interface BaseLevelRuleRepository extends JpaRepository<BaseLevelRuleDO, Long>, JpaSpecificationExecutor<BaseLevelRuleDO> {
    BaseLevelRuleDO findFirstByRuleTypeEnumAndMemberLevelTypeEnum(Integer ruleTypeEnum, Integer memberLevelTypeEnum);

    List<BaseLevelRuleDO> findByMemberLevelTypeEnumAndStatus(Integer memberLevelTypeEnum, Integer status);

    List<BaseLevelRuleDO> findByMemberLevelTypeEnumNotAndStatus(Integer memberLevelTypeEnum, Integer status);
}
