package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.invitation.MemberReceiveInvitationDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 接收邀请Repository
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/30 10:21
 */
public interface MemberReceiveInvitationRepository extends JpaRepository<MemberReceiveInvitationDO, Long>, JpaSpecificationExecutor<MemberReceiveInvitationDO> {

    MemberReceiveInvitationDO findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);

    MemberReceiveInvitationDO findBySubMemberIdAndSubRoleIdAndInviteType(Long subMemberId, Long subRoleId, int inviteType);
}
