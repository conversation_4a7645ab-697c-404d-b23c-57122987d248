package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.manage.api.feign.IInitConfigFeign;
import com.ssy.lingxi.manage.api.feign.IPlatformParameterFeign;
import com.ssy.lingxi.manage.api.feign.ISensitiveWordFeign;
import com.ssy.lingxi.manage.api.model.req.sensitive.SensitiveWordFilterReq;
import com.ssy.lingxi.manage.api.model.req.sensitive.SensitiveWordListFilterReq;
import com.ssy.lingxi.manage.api.model.resp.parameter.PlatformParameterManageResp;
import com.ssy.lingxi.manage.api.model.resp.sensitive.SensitiveWordFilterResp;
import com.ssy.lingxi.manage.api.model.resp.sensitive.SensitiveWordListFilterResp;
import com.ssy.lingxi.member.service.feign.IManageFeignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 调用平台管理服务Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-03
 */
@Slf4j
@Service
public class PlatformManageFeignServiceImpl implements IManageFeignService {
    @Resource
    private ISensitiveWordFeign ISensitiveWordFeign;

    @Resource
    private IPlatformParameterFeign IParameterManageFeign;

    @Resource
    private IInitConfigFeign IInitConfigFeign;

    /**
     * 敏感词过滤
     * @param sentence 要过滤的语句
     * @return 过滤后的语句
     */
    @Override
    public String filterSensitiveWord(String sentence) {
        SensitiveWordFilterReq sensitiveWordRequest = new SensitiveWordFilterReq();
        sensitiveWordRequest.setContent(sentence);
        WrapperResp<SensitiveWordFilterResp> sensitiveWordResponse = ISensitiveWordFeign.filterSensitiveWord(sensitiveWordRequest);
        if (ResponseCodeEnum.SUCCESS.getCode() != sensitiveWordResponse.getCode()) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_MANAGE_ERROR);
        }

        return sensitiveWordResponse.getData().getContent();
    }

    /**
     * 敏感词过滤
     * @param sentenceList 要过滤的语句列表
     * @return 过滤后的语句列表
     */
    @Override
    public List<String> filterSensitiveWordList(List<String> sentenceList) {
        SensitiveWordListFilterReq sensitiveWordRequest = new SensitiveWordListFilterReq();
        sensitiveWordRequest.setContents(sentenceList);
        WrapperResp<SensitiveWordListFilterResp> sensitiveWordResponse = ISensitiveWordFeign.filterSensitiveWordList(sensitiveWordRequest);
        if (ResponseCodeEnum.SUCCESS.getCode() != sensitiveWordResponse.getCode()) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_MANAGE_ERROR);
        }

        return sensitiveWordResponse.getData().getContents();
    }

    /**
     * 查询平台参数配置
     * @param code 参数编码
     * @return 返回结果
     */
    @Override
    public PlatformParameterManageResp parameterManageDetails(String code) {
        WrapperResp<PlatformParameterManageResp> wrapperResp = IParameterManageFeign.getPlatformParameter(code);
        if (ResponseCodeEnum.SUCCESS.getCode() != wrapperResp.getCode()) {
            throw new BusinessException(wrapperResp.getCode(),wrapperResp.getMessage());
        }
        return wrapperResp.getData();
    }

    @Override
    public Boolean enableMultiTenancy() {
        WrapperResp<Boolean> wrapperResp = IInitConfigFeign.enableMultiTenancy();
        if (ResponseCodeEnum.SUCCESS.getCode() != wrapperResp.getCode()) {
            throw new BusinessException(wrapperResp.getCode(),wrapperResp.getMessage());
        }
        return wrapperResp.getData();
    }
}
