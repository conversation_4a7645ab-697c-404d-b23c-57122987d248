package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 平台后台会员生命周期关联会员
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@Data
public class PlatformMemberCycleProcessMemberReq implements Serializable {

    private static final long serialVersionUID = 7403111175055230572L;

    /**
     * 会员id
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;

    /**
     * 会员角色id
     */
    @NotNull(message = "会员角色id要大于0")
    @Positive(message = "会员角色id要大于0")
    private Long roleId;
}
