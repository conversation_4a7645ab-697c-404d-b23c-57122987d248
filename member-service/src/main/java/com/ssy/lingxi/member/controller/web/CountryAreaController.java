package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.model.req.AreaCodeReq;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * Web/App通用 - 省市区相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-12
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/area")
public class CountryAreaController {

    /**
     * 注册页面 - 查询省列表
     * @return 查询结果
     */
    @GetMapping("/province")
    public WrapperResp<List<AreaCodeNameResp>> listProvince() {
        return WrapperUtil.success(AreaUtil.listProvince());
    }

    /**
     * 注册页面 - 根据省编码，查询市列表
     * @param codeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/city")
    public WrapperResp<List<AreaCodeNameResp>> listCitiesByProvinceCode(@Valid AreaCodeReq codeVO) {
        return WrapperUtil.success(AreaUtil.listCityByProvinceCode(codeVO.getCode()));
    }

    /**
     * 注册页面 - 根据市编码，查询区列表
     * @param codeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/district")
    public WrapperResp<List<AreaCodeNameResp>> listDistrictsByCityCode(@Valid AreaCodeReq codeVO) {
        return WrapperUtil.success(AreaUtil.listDistrictByCityCode(codeVO.getCode()));
    }
}
