package com.ssy.lingxi.member.controller.feign;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMobileMemberSalesFeign;
import com.ssy.lingxi.member.api.model.req.MemberSalesFindUserIdReq;
import com.ssy.lingxi.member.api.model.resp.MemberSalesFeignPageQueryResp;
import com.ssy.lingxi.member.service.mobile.IMobileMemberSalesService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 业务员小程序--远程调用
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-24
 * @ignore 不需要提交到Yapi
 */
@RestController
public class MobileMemberSalesFeignController implements IMobileMemberSalesFeign {

    @Resource
    IMobileMemberSalesService iMobileMemberSalesService;

    /**
     * 根据业务员Id查询下级会员信息
     *
     * @param pageVO 查询条件
     * @return 返回下级会员信息
     */
    @Override
    public WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> getSalesList(@RequestBody @Valid MemberSalesFindUserIdReq pageVO) {
        return WrapperUtil.success(iMobileMemberSalesService.getSalesList(pageVO));
    }

}
