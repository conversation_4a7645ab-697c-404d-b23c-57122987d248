package com.ssy.lingxi.member.serviceImpl.commission;

import com.ssy.lingxi.member.constant.CommissionConstant;
import com.ssy.lingxi.member.entity.do_.commission.CommissionAccountDO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionConfigDO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionDetailDO;
import com.ssy.lingxi.member.entity.do_.commission.InvitationRecordDO;
import com.ssy.lingxi.member.enums.commission.CommissionTradeTypeEnum;
import com.ssy.lingxi.member.enums.commission.OrderChangeTypeEnum;
import com.ssy.lingxi.member.model.dto.OrderChangeCommissionDTO;
import com.ssy.lingxi.member.repository.commission.CommissionAccountRepository;
import com.ssy.lingxi.member.repository.commission.CommissionConfigRepository;
import com.ssy.lingxi.member.repository.commission.CommissionDetailRepository;
import com.ssy.lingxi.member.repository.commission.InvitationRecordRepository;
import com.ssy.lingxi.member.service.commission.IOrderCommissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * 订单分佣处理服务实现
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-07-08
 */
@Slf4j
@Service
public class OrderCommissionServiceImpl implements IOrderCommissionService {

    @Resource
    private CommissionAccountRepository commissionAccountRepository;

    @Resource
    private CommissionDetailRepository commissionDetailRepository;

    @Resource
    private CommissionConfigRepository commissionConfigRepository;

    @Resource
    private InvitationRecordRepository invitationRecordRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrderChangeCommission(OrderChangeCommissionDTO orderChangeDTO) {
        log.info("开始处理订单变更分佣，订单号：{}，变更类型：{}", 
                orderChangeDTO.getOrderNo(), orderChangeDTO.getChangeType());

        OrderChangeTypeEnum changeType = OrderChangeTypeEnum.getByCode(orderChangeDTO.getChangeType());
        if (changeType == null) {
            log.warn("未知的订单变更类型：{}", orderChangeDTO.getChangeType());
            return;
        }

        switch (changeType) {
            case ORDER_CREATED:
                processOrderCreatedCommission(orderChangeDTO);
                break;
            case ORDER_COMPLETED:
                processOrderCompletedCommission(orderChangeDTO);
                break;
            case ORDER_CANCELLED:
                processOrderCancelledCommission(orderChangeDTO);
                break;
            default:
                log.warn("未处理的订单变更类型：{}", changeType);
                break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrderCreatedCommission(OrderChangeCommissionDTO orderChangeDTO) {
        log.info("处理订单创建分佣，订单号：{}", orderChangeDTO.getOrderNo());

        // 查找邀请关系
        Optional<InvitationRecordDO> invitationOpt = invitationRecordRepository
                .findByInviteeUserId(orderChangeDTO.getBuyerUserId());
        
        if (!invitationOpt.isPresent()) {
            log.info("用户{}没有邀请关系，无需处理分佣", orderChangeDTO.getBuyerUserId());
            return;
        }

        InvitationRecordDO invitation = invitationOpt.get();
        
        // 查找邀请人的分佣账户
        Optional<CommissionAccountDO> accountOpt = commissionAccountRepository
                .findByUserId(invitation.getInviterUserId());
        
        if (!accountOpt.isPresent()) {
            log.warn("邀请人{}没有分佣账户，无法处理分佣", invitation.getInviterUserId());
            return;
        }

        CommissionAccountDO account = accountOpt.get();
        
        // 获取分佣配置
        CommissionConfigDO config = getCommissionConfig();
        if (config == null || !isOrderCommissionEnabled(config)) {
            log.info("订单分佣功能未启用，跳过处理");
            return;
        }

        // 判断是否为首单
        boolean isFirstOrder = isUserFirstOrder(orderChangeDTO.getBuyerUserId(), orderChangeDTO.getBuyerMemberId());

        if (isFirstOrder) {
            log.info("用户{}首单，不创建订单分佣记录，等待订单完成后处理首单分佣", orderChangeDTO.getBuyerUserId());
            return;
        }

        // 计算分佣金额
        BigDecimal commissionAmount = calculateOrderCommission(orderChangeDTO, config);
        if (commissionAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("分佣金额为0，跳过处理");
            return;
        }

        // 创建待收益分佣记录
        createCommissionDetail(account, commissionAmount, 
                CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING,
                orderChangeDTO.getOrderNo(), orderChangeDTO.getBuyerUserId(),
                "订单创建分佣，订单号：" + orderChangeDTO.getOrderNo());

        // 更新账户待收益金额
        account.setToBeEarningsAmount(account.getToBeEarningsAmount().add(commissionAmount));
        account.setUpdateTime(System.currentTimeMillis());
        commissionAccountRepository.saveAndFlush(account);

        log.info("订单创建分佣处理完成，邀请人：{}，分佣金额：{}", 
                invitation.getInviterUserId(), commissionAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrderCompletedCommission(OrderChangeCommissionDTO orderChangeDTO) {
        log.info("处理订单完成分佣，订单号：{}", orderChangeDTO.getOrderNo());

        // 判断是否为首单
        boolean isFirstOrder = isUserFirstOrder(orderChangeDTO.getBuyerUserId(), orderChangeDTO.getBuyerMemberId());

        if (isFirstOrder) {
            // 处理首单分佣
            processFirstOrderCommission(orderChangeDTO);
        } else {
            // 处理普通订单分佣确认
            processNormalOrderCompletedCommission(orderChangeDTO);
        }
    }

    /**
     * 处理首单分佣
     */
    private void processFirstOrderCommission(OrderChangeCommissionDTO orderChangeDTO) {
        log.info("处理首单分佣，订单号：{}", orderChangeDTO.getOrderNo());

        // 查找邀请关系
        Optional<InvitationRecordDO> invitationOpt = invitationRecordRepository
                .findByInviteeUserId(orderChangeDTO.getBuyerUserId());

        if (!invitationOpt.isPresent()) {
            log.info("用户{}没有邀请关系，无需处理首单分佣", orderChangeDTO.getBuyerUserId());
            return;
        }

        InvitationRecordDO invitation = invitationOpt.get();

        // 查找邀请人的分佣账户
        Optional<CommissionAccountDO> accountOpt = commissionAccountRepository
                .findByUserId(invitation.getInviterUserId());

        if (!accountOpt.isPresent()) {
            log.warn("邀请人{}没有分佣账户，无法处理首单分佣", invitation.getInviterUserId());
            return;
        }

        CommissionAccountDO account = accountOpt.get();

        // 获取分佣配置
        CommissionConfigDO config = getCommissionConfig();
        if (config == null || !isFirstOrderCommissionEnabled(config)) {
            log.info("首单分佣功能未启用，跳过处理");
            return;
        }

        // 获取首单分佣金额
        BigDecimal firstOrderReward = config.getConfigBO().getInvitationFirstOrderReward();
        if (firstOrderReward == null || firstOrderReward.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("首单分佣金额为0，跳过处理");
            return;
        }

        // 创建首单分佣记录
        createCommissionDetail(account, firstOrderReward,
                CommissionTradeTypeEnum.INVITATION_FIRST_ORDER_REWARD,
                orderChangeDTO.getOrderNo(), orderChangeDTO.getBuyerUserId(),
                "邀请首单奖励，订单号：" + orderChangeDTO.getOrderNo());

        // 更新账户余额：直接加到可提现余额
        account.setWithdrawableBalance(account.getWithdrawableBalance().add(firstOrderReward));
        account.setAccountBalance(account.getAccountBalance().add(firstOrderReward));
        account.setUpdateTime(System.currentTimeMillis());
        commissionAccountRepository.saveAndFlush(account);

        log.info("首单分佣处理完成，邀请人：{}，首单奖励：{}",
                invitation.getInviterUserId(), firstOrderReward);
    }

    /**
     * 处理普通订单完成分佣确认
     */
    private void processNormalOrderCompletedCommission(OrderChangeCommissionDTO orderChangeDTO) {
        log.info("处理普通订单完成分佣确认，订单号：{}", orderChangeDTO.getOrderNo());

        // 查找对应的待收益分佣记录
        Optional<CommissionDetailDO> pendingCommissionOpt = commissionDetailRepository
                .findByRelatedOrderNoAndTradeType(orderChangeDTO.getOrderNo(),
                        CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING.getCode());

        if (!pendingCommissionOpt.isPresent()) {
            log.warn("未找到订单{}的待收益分佣记录", orderChangeDTO.getOrderNo());
            return;
        }

        CommissionDetailDO pendingCommission = pendingCommissionOpt.get();

        // 查找分佣账户
        Optional<CommissionAccountDO> accountOpt = commissionAccountRepository
                .findById(pendingCommission.getCommissionAccountId());

        if (!accountOpt.isPresent()) {
            log.warn("未找到分佣账户，账户ID：{}", pendingCommission.getCommissionAccountId());
            return;
        }

        CommissionAccountDO account = accountOpt.get();
        BigDecimal commissionAmount = pendingCommission.getChangeAmount();

        // 创建已收益分佣记录
        createCommissionDetail(account, commissionAmount,
                CommissionTradeTypeEnum.ORDER_COMMISSION_CONFIRMED,
                orderChangeDTO.getOrderNo(), orderChangeDTO.getBuyerUserId(),
                "订单完成分佣确认，订单号：" + orderChangeDTO.getOrderNo());

        // 更新账户余额：从待收益转为可提现余额
        account.setToBeEarningsAmount(account.getToBeEarningsAmount().subtract(commissionAmount));
        account.setWithdrawableBalance(account.getWithdrawableBalance().add(commissionAmount));
        account.setAccountBalance(account.getAccountBalance().add(commissionAmount));
        account.setUpdateTime(System.currentTimeMillis());
        commissionAccountRepository.saveAndFlush(account);

        log.info("普通订单完成分佣处理完成，用户：{}，分佣金额：{}",
                account.getUserId(), commissionAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processOrderCancelledCommission(OrderChangeCommissionDTO orderChangeDTO) {
        log.info("处理订单取消分佣，订单号：{}", orderChangeDTO.getOrderNo());

        // 判断是否为首单
        boolean isFirstOrder = isUserFirstOrder(orderChangeDTO.getBuyerUserId(), orderChangeDTO.getBuyerMemberId());

        if (isFirstOrder) {
            log.info("用户{}首单取消，无需处理分佣扣减", orderChangeDTO.getBuyerUserId());
            return;
        }

        // 查找对应的待收益分佣记录
        Optional<CommissionDetailDO> pendingCommissionOpt = commissionDetailRepository
                .findByRelatedOrderNoAndTradeType(orderChangeDTO.getOrderNo(),
                        CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING.getCode());

        if (!pendingCommissionOpt.isPresent()) {
            log.warn("未找到订单{}的待收益分佣记录", orderChangeDTO.getOrderNo());
            return;
        }

        CommissionDetailDO pendingCommission = pendingCommissionOpt.get();

        // 查找分佣账户
        Optional<CommissionAccountDO> accountOpt = commissionAccountRepository
                .findById(pendingCommission.getCommissionAccountId());

        if (!accountOpt.isPresent()) {
            log.warn("未找到分佣账户，账户ID：{}", pendingCommission.getCommissionAccountId());
            return;
        }

        CommissionAccountDO account = accountOpt.get();
        BigDecimal commissionAmount = pendingCommission.getChangeAmount();

        // 创建取消分佣记录
        createCommissionDetail(account, commissionAmount.negate(),
                CommissionTradeTypeEnum.ORDER_COMMISSION_CANCELLED,
                orderChangeDTO.getOrderNo(), orderChangeDTO.getBuyerUserId(),
                "订单取消扣除分佣，订单号：" + orderChangeDTO.getOrderNo());

        // 更新账户待收益金额（扣减）
        account.setToBeEarningsAmount(account.getToBeEarningsAmount().subtract(commissionAmount));
        account.setUpdateTime(System.currentTimeMillis());
        commissionAccountRepository.saveAndFlush(account);

        log.info("订单取消分佣处理完成，用户：{}，扣减金额：{}",
                account.getUserId(), commissionAmount);
    }

    /**
     * 判断用户是否为首单
     */
    private boolean isUserFirstOrder(Long userId, Long memberId) {
        try {
            // 查询用户历史已完成订单数量
            // 这里需要调用订单服务查询用户历史订单
            // 暂时简化实现：查询是否存在已完成的分佣记录
            long completedOrderCount = commissionDetailRepository.countByRelatedUserIdAndTradeType(
                    userId, CommissionTradeTypeEnum.ORDER_COMMISSION_CONFIRMED.getCode());

            // 查询是否存在首单分佣记录
            long firstOrderCount = commissionDetailRepository.countByRelatedUserIdAndTradeType(
                    userId, CommissionTradeTypeEnum.INVITATION_FIRST_ORDER_REWARD.getCode());

            // 如果没有已完成的订单分佣记录且没有首单分佣记录，则认为是首单
            boolean isFirstOrder = (completedOrderCount == 0 && firstOrderCount == 0);

            log.debug("用户{}首单判断：已完成订单分佣数量={}，首单分佣数量={}，是否首单={}",
                    userId, completedOrderCount, firstOrderCount, isFirstOrder);

            return isFirstOrder;
        } catch (Exception e) {
            log.error("判断用户{}是否首单时发生异常：{}", userId, e.getMessage(), e);
            // 异常情况下默认不是首单，避免影响正常分佣流程
            return false;
        }
    }

    /**
     * 检查首单分佣功能是否启用
     */
    private boolean isFirstOrderCommissionEnabled(CommissionConfigDO config) {
        if (config == null || config.getConfigBO() == null) {
            return false;
        }
        return config.getConfigBO().getInvitationFirstOrderRewardEnabled() != null
                && config.getConfigBO().getInvitationFirstOrderRewardEnabled();
    }


    /**
     * 获取分佣配置
     */
    private CommissionConfigDO getCommissionConfig() {
        Optional<CommissionConfigDO> configOpt = commissionConfigRepository.findFirstBy();
        return configOpt.orElse(null);
    }
    /**
     * 判断订单分佣是否启用
     */
    private boolean isOrderCommissionEnabled(CommissionConfigDO config) {
        return config.getConfigBO() != null && 
               Boolean.TRUE.equals(config.getConfigBO().getOrderCommissionEnabled());
    }

    /**
     * 计算订单分佣金额
     */
    private BigDecimal calculateOrderCommission(OrderChangeCommissionDTO orderChangeDTO, CommissionConfigDO config) {
        if (orderChangeDTO.getOrderWeight() == null || config.getConfigBO() == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal commissionPerGram = config.getConfigBO().getCommissionPerGram();
        if (commissionPerGram == null) {
            commissionPerGram = CommissionConstant.DEFAULT_COMMISSION_PER_GRAM;
        }

        return orderChangeDTO.getOrderWeight().multiply(commissionPerGram)
                .setScale(CommissionConstant.COMMISSION_AMOUNT_SCALE, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 创建分佣明细记录
     */
    private void createCommissionDetail(CommissionAccountDO account, BigDecimal changeAmount,
                                      CommissionTradeTypeEnum tradeType, String orderNo, 
                                      Long relatedUserId, String remark) {
        CommissionDetailDO detail = new CommissionDetailDO();
        detail.setCommissionAccountId(account.getId());
        detail.setUserId(account.getUserId());
        detail.setTradeCode(generateTradeCode());
        detail.setTradeTime(System.currentTimeMillis());
        detail.setOriginalAmount(getAccountCurrentAmount(account, tradeType));
        detail.setChangeAmount(changeAmount);
        detail.setCurrentAmount(detail.getOriginalAmount().add(changeAmount));
        detail.setTradeType(tradeType.getCode());
        detail.setRelatedOrderNo(orderNo);
        detail.setRelatedUserId(relatedUserId);
        detail.setRemark(remark);
        detail.setCreateTime(System.currentTimeMillis());

        commissionDetailRepository.saveAndFlush(detail);
    }

    /**
     * 获取账户当前金额（根据交易类型）
     */
    private BigDecimal getAccountCurrentAmount(CommissionAccountDO account, CommissionTradeTypeEnum tradeType) {
        switch (tradeType) {
            case ORDER_COMMISSION_PENDING:
            case ORDER_COMMISSION_CANCELLED:
                return account.getToBeEarningsAmount();
            case ORDER_COMMISSION_CONFIRMED:
                return account.getWithdrawableBalance();
            default:
                return account.getAccountBalance();
        }
    }

    /**
     * 生成交易流水号
     */
    private String generateTradeCode() {
        return CommissionConstant.COMMISSION_TRADE_CODE_PREFIX + System.currentTimeMillis();
    }
}
