package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.collection.CollectionUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.SelectLongResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberCancellationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRightDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.req.maintenance.MemberCancellationPageDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberCancellationValidateReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.resp.basic.DetailTextGroupResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberCancellationPageConditionResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberCancellationPageResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberDetailBasicResp;
import com.ssy.lingxi.member.model.resp.validate.WorkFlowStepResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.configManage.IBasePaasService;
import com.ssy.lingxi.member.service.feign.ISmsFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IPlatformMemberDetailBasicService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 平台后台 - 会员维护 - 会员详情 - 基本信息服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
@Service
public class PlatformMemberDetailBasicServiceImpl implements IPlatformMemberDetailBasicService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private ISmsFeignService smsFeignService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberRegisterDetailRepository memberRegisterDetailRepository;

    @Resource
    private MemberCancellationRepository cancellationRepository;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    @Resource
    private IBasePaasService iBasePaasService;

    @Resource
    private IBaseTokenManageService tokenManageService;

    /**
     * 查询会员详情 - 会员基本信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public PlatformMemberDetailBasicResp getMemberDetailBasic(HttpHeaders headers, MemberValidateReq validateVO) {
        MemberDO memberDO = memberRepository.findById(validateVO.getMemberId()).orElse(null);
        if(memberDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberLevelRightDO levelDO = relationDO.getLevelRight();
        if(levelDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
        }

        PlatformMemberDetailBasicResp basicVO = new PlatformMemberDetailBasicResp();
        //外部流程暂时固定
        List<WorkFlowStepResp> stepVOList = new ArrayList<>();
        WorkFlowStepResp stepVO = new WorkFlowStepResp();
        stepVO.setStep(1);
        stepVO.setStepName("申请会员");
        stepVO.setRoleName(relationDO.getSubRoleName());
        stepVOList.add(stepVO);

        stepVO = new WorkFlowStepResp();
        stepVO.setStep(2);
        stepVO.setStepName("审核会员");
        stepVO.setRoleName(relationDO.getRole().getRoleName());
        stepVOList.add(stepVO);

        //外部审核流程和当前步骤
        basicVO.setOuterVerifySteps(stepVOList);
        if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode()) || relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode())) {
            basicVO.setCurrentOuterStep(1);
        } else {
            basicVO.setCurrentOuterStep(2);
        }

        //内部流程和审核步骤
        WorkflowTaskListBO taskStepQueryResult = workflowFeignService.listMemberProcessSteps(relationDO);

        basicVO.setInnerVerifySteps(taskStepQueryResult.getStepList());
        basicVO.setCurrentInnerStep(taskStepQueryResult.getCurrentStep());

        //基本信息
        basicVO.setMemberId(memberDO.getId());
        basicVO.setValidateId(relationDO.getId());
        basicVO.setName(relationDO.getSubMember().getName());
        basicVO.setOuterStatus(relationDO.getOuterStatus());
        basicVO.setOuterStatusName(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));
        basicVO.setInnerStatus(relationDO.getInnerStatus());
        basicVO.setInnerStatusName(PlatformInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()));
        basicVO.setStatus(relationDO.getStatus());
        basicVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        basicVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
        basicVO.setRoleName(relationDO.getSubRole().getRoleName());
        basicVO.setLevelTag(levelDO.getLevelTag());
        basicVO.setAccount(memberDO.getAccount());
        basicVO.setPhone(memberDO.getPhone());
        basicVO.setEmail(StringUtils.hasLength(memberDO.getEmail()) ? memberDO.getEmail() : "");
        basicVO.setCreateTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));

        //如果有注销，回显注销原因
        basicVO.setCancellationReason(Optional.ofNullable(relationDO.getMemberCancellation()).map(MemberCancellationDO::getCancellationReason).orElse(""));

        //对资料配置进行分组
        if (MemberDetailVersionEnum.TO_BE_VALIDATE.getCode().equals(validateVO.getDetailVersionType())) {
            basicVO.setGroups(baseMemberRegisterDetailService.groupMemberRegisterDetailText(memberDO, MemberDetailVersionEnum.TO_BE_VALIDATE));
        } else {
            basicVO.setGroups(baseMemberRegisterDetailService.switchMemberRegisterDetailText(memberDO));
        }

        basicVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, null));

        basicVO.setInnerHistory(baseMemberHistoryService.listMemberInnerHistory(relationDO, null));

        return basicVO;
    }

    @Override
    public MemberCancellationPageConditionResp getCancellationPageCondition(HttpHeaders headers) {
        memberCacheService.needLoginFromManagePlatform(headers);

        MemberCancellationPageConditionResp conditionVO = new MemberCancellationPageConditionResp();

        //内部状态
        conditionVO.setCancellationStatus(MemberCancellationEnum.getDropdownItemResp());

        //会员状态
        conditionVO.setStatus(MemberStatusEnum.getCancellationDropdownItemList());

        //会员类型
        conditionVO.setMemberTypes(MemberTypeEnum.getDropdownItemResp());

        //会员角色（按照Id升序排序）
        conditionVO.setMemberRoles(memberRoleRepository.findAll().stream().filter(r -> !r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).map(r -> new SelectLongResp(r.getId(), r.getRoleName())).sorted(Comparator.comparing(SelectLongResp::getValue)).collect(Collectors.toList()));

        return conditionVO;
    }

    @Override
    public PageDataResp<MemberCancellationPageResp> getCancellationPage(HttpHeaders headers, MemberCancellationPageDataReq pageReq) {
        memberCacheService.needLoginFromManagePlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            //上级为平台
            list.add(cb.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            //按会员注销状态筛选
            Join<MemberRelationDO, MemberCancellationDO> cancellationJoin = root.join("memberCancellation", JoinType.INNER);
            if (NumberUtil.isNullOrZero(pageReq.getCancellationStatus())) {
                list.add(cb.in(cancellationJoin.get("cancellationStatus")).value(MemberCancellationEnum.getCodeList()));
            } else {
                list.add(cb.equal(cancellationJoin.get("cancellationStatus").as(Integer.class), pageReq.getCancellationStatus()));
            }

            //会员角色
            if (NumberUtil.notNullOrZero(pageReq.getRoleId())) {
                list.add(cb.equal(root.get("subRoleId").as(Long.class), pageReq.getRoleId()));
            }

            //会员状态
            if (NumberUtil.notNullOrZero(pageReq.getStatus())) {
                list.add(cb.equal(root.get("status").as(Integer.class), pageReq.getStatus()));
            }

            //会员名称
            if (StringUtils.hasLength(pageReq.getName())) {
                Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(cb.like(subMemberJoin.get("name").as(String.class), "%" + pageReq.getName().trim() + "%"));
            }

            //会员类型
            if (NumberUtil.notNullOrZero(pageReq.getMemberType())) {
                list.add(cb.equal(root.get("subMemberTypeEnum").as(Integer.class), pageReq.getMemberType()));
            }

            Predicate[] p = new Predicate[list.size()];
            return cb.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageReq.getCurrent() - 1, pageReq.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);

        List<MemberRelationDO> content = pageList.getContent();
        return new PageDataResp<>(pageList.getTotalElements(), content.stream().map(relDO -> {
            MemberCancellationPageResp cancellationPageResp = new MemberCancellationPageResp();
            cancellationPageResp.setValidateId(relDO.getId());
            cancellationPageResp.setMemberId(relDO.getSubMemberId());
            cancellationPageResp.setRoleId(relDO.getSubRoleId());
            cancellationPageResp.setName(relDO.getSubMember().getName());
            cancellationPageResp.setMemberType(relDO.getSubRole().getMemberType());
            cancellationPageResp.setMemberTypeName(MemberTypeEnum.getName(relDO.getSubRole().getMemberType()));
            cancellationPageResp.setRoleName(relDO.getSubRole().getRoleName());
            cancellationPageResp.setApplyCancellationTime(relDO.getMemberCancellation().getApplyCancellationTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            cancellationPageResp.setLevel(relDO.getLevelRight().getLevel());
            cancellationPageResp.setLevelTag(relDO.getLevelRight().getLevelTag());
            cancellationPageResp.setStatus(relDO.getStatus());
            cancellationPageResp.setStatusName(MemberStatusEnum.getCodeMessage(relDO.getStatus()));
            cancellationPageResp.setCancellationStatus(relDO.getMemberCancellation().getCancellationStatus());
            cancellationPageResp.setCancellationStatusName(MemberCancellationEnum.getCodeMsg(relDO.getMemberCancellation().getCancellationStatus()));
            return cancellationPageResp;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员详情 - 会员注销审核
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancellationAuth(HttpHeaders headers, MemberCancellationValidateReq cancellationReq) {
        // 校验平台登录
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromManagePlatform(headers);

        // 获取待注销会员
        MemberDO memberDO = memberRepository.findById(cancellationReq.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));

        // 查询会员所有的上级会员
        List<MemberRelationDO> relDOList = relationRepository.findAllBySubMemberId(memberDO.getId());

        // 校验注销状态
        BusinessAssertUtil.isTrue(relDOList.stream().allMatch(platformRelDO -> MemberStatusEnum.WAIT_CANCELLATION.getCode().equals(platformRelDO.getStatus())), ResponseCodeEnum.MC_MS_THERE_ARE_NON_PENDING_RELATIONSHIP_RECORDS);

        final String cancellationStr = "账号已注销";

        if (cancellationReq.getVerify()) {// 审核通过
            // 设置关系表的相关注销信息
            for (MemberRelationDO relDO : relDOList) {
                // 平台关系和上下级关系的内部状态是两个枚举
                if (MemberRelationTypeEnum.PLATFORM.getCode().equals(relDO.getRelType())) {
                    relDO.setInnerStatus(PlatformInnerStatusEnum.CANCELLATION.getCode());
                } else {
                    relDO.setInnerStatus(MemberInnerStatusEnum.CANCELLATION.getCode());
                }
                relDO.setOuterStatus(MemberOuterStatusEnum.CANCELLATION.getCode());
                relDO.setStatus(MemberStatusEnum.CANCELLATION.getCode());
                relationRepository.save(relDO);

                MemberCancellationDO memberCancellationDO = relDO.getMemberCancellation();
                memberCancellationDO.setCancellationComments(cancellationReq.getCancellationComments());
                memberCancellationDO.setCancellationStatus(MemberCancellationEnum.CANCELLATION_SUCCESS.getCode());
                cancellationRepository.save(memberCancellationDO);

                //内部记录
                baseMemberHistoryService.saveMemberInnerHistory(relDO, loginCacheDTO, MemberValidateHistoryOperationEnum.CANCELLATION_REVIEW_PASSED, cancellationReq.getCancellationComments());

                //外部记录
                baseMemberHistoryService.saveMemberOuterHistory(relDO, relDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.CANCELLATION_REVIEW_PASSED, MemberStatusEnum.getCodeMessage(relDO.getStatus()), cancellationReq.getCancellationComments());
            }

            // 设置会员的注销状态
            memberDO.setAccount(cancellationStr);
            memberDO.setName(cancellationStr);
            memberDO.setPhone(cancellationStr);
            memberDO.setEmail(cancellationStr);
            memberDO.setPayPassword(cancellationStr);
            memberDO.setLogo(cancellationStr);
            memberRepository.save(memberDO);

            // 注销会员的平台注册资料
            for (MemberRegisterDetailDO registerDetailDO : memberDO.getRegisterDetails()) {
                registerDetailDO.setDetail(cancellationStr);
                memberRegisterDetailRepository.save(registerDetailDO);
            }

            // 设置会员用户的注销状态
            for (UserDO userDO : memberDO.getUsers()) {
                userDO.setAccount(cancellationStr);
                userDO.setPhone(cancellationStr);
                userDO.setName(cancellationStr);
                userDO.setRealName(cancellationStr);
                userDO.setEmail(cancellationStr);
                userDO.setIdCardNo(cancellationStr);
                userDO.setIdCardImg(new HashMap<>());
                userDO.setIsAuth(false);
                userDO.setLogo(cancellationStr);
                userDO.setStatus(EnableDisableStatusEnum.DISABLE.getCode());
                userRepository.save(userDO);
            }

            // 发送注销成功短信通知
            smsFeignService.sendMemberCancellationSuccessSms(memberDO.getTelCode(), memberDO.getPhone());
        } else {// 审核不通过
            // 设置会员平台关系的注销状态
            for (MemberRelationDO relDO : relDOList) {
                MemberCancellationDO memberCancellationDO = relDO.getMemberCancellation();
                relDO.setStatus(memberCancellationDO.getCancellationBeforeStatus());
                relationRepository.save(relDO);

                memberCancellationDO.setCancellationComments(cancellationReq.getCancellationComments());
                memberCancellationDO.setCancellationStatus(MemberCancellationEnum.CANCELLATION_FAIL.getCode());
                cancellationRepository.save(memberCancellationDO);

                //内部记录
                baseMemberHistoryService.saveMemberInnerHistory(relDO, loginCacheDTO, MemberValidateHistoryOperationEnum.CANCELLATION_REVIEW_FAILED, cancellationReq.getCancellationComments());

                //外部记录
                baseMemberHistoryService.saveMemberOuterHistory(relDO, relDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.CANCELLATION_REVIEW_FAILED, MemberStatusEnum.getCodeMessage(relDO.getStatus()), cancellationReq.getCancellationComments());
            }

            // 发送注销失败短信通知
            smsFeignService.sendMemberCancellationFailSms(memberDO.getTelCode(), memberDO.getPhone(), cancellationReq.getCancellationComments());
        }

        //删除指定会员的所有token
        CompletableFuture.runAsync(() -> tokenManageService.memberOffline(Collections.singletonList(memberDO.getId())), ThreadPoolConfig.asyncDefaultExecutor);
    }
}
