package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.component.base.annotation.AesDecryptAnnotation;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.model.req.ProvinceCityCodeReq;
import com.ssy.lingxi.member.handler.annotation.MemberTypeAnno;
import com.ssy.lingxi.member.handler.annotation.PwdSteEstimatorAnnotation;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 会员能力 - 新增会员接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-18
 */
@Data
public class MemberAbilityAddMemberReq implements Serializable {
    private static final long serialVersionUID = -7117536522555225885L;

    /**
     * 会员类型
     * @see MemberTypeEnum
     */
    @MemberTypeAnno
    private Integer memberType;

    /**
     * 角色Id
     */
    @NotNull(message = "角色Id不能为空")
    @Positive(message = "角色Id要大于0")
    private Long roleId;

    /**
     * 所属会员Id
     */
    private Long memberId;

    /**
     * 会员等级
     */
    private Integer level;

    /**
     * 手机号码前缀
     */
    @NotBlank(message = "手机号码前缀不能为空")
    private String telCode;

    /**
     * 手机号码（需要加密）
     */
    @AesDecryptAnnotation
    @NotNull(message = "手机号码不能为空")
//    @PhoneAnno
    private String phone;

    /**
     * 注册邮箱（需要加密）
     */
    @AesDecryptAnnotation
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 密码（需要加密）
     */
    @AesDecryptAnnotation
    @PwdSteEstimatorAnnotation
    private String password;

    /**
     * 渠道类型Id，如果会员类型为渠道会员、渠道个人会员，必填
     */
    private Long channelTypeId;

    /**
     * 城市编码列表，如果会员类型为渠道会员、渠道个人会员，必填
     */
    @Valid
    private List<ProvinceCityCodeReq> areas;

    /**
     * 上级会员关系Id
     */
    private Long upperRelationId;

    /**
     * 渠道描述
     */
    private String remark;

    /**
     * 会员注册资料，Map对象类型
     * Key为获取页面内容接口返回的fieldName，value为用户填写的内容
     */
    private Map<String, Object> detail;
}
