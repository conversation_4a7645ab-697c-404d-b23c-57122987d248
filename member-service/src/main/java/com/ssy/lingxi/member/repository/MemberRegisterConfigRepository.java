package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员注册资料配置JpaRepository
 * <AUTHOR>
 * @since 2020-05-29
 * @version 2.0.0
 */
@Repository
public interface MemberRegisterConfigRepository extends JpaRepository<MemberRegisterConfigDO,Long>, JpaSpecificationExecutor<MemberRegisterConfigDO> {
    Boolean existsByFieldNameAndParentIdAndIdNot(String fieldName, Long parentId, Long id);

    Boolean existsByTagEnumAndParentIdEquals(Integer tagEnum, Long pid);

    Boolean existsByTagEnumAndIdNotAndParentIdEquals(Integer tagEnum, Long id, Long pid);

    List<MemberRegisterConfigDO> findByFieldStatusAndIdIn(Integer status, List<Long> ids);

    List<MemberRegisterConfigDO> findAllByParentId(Long id);

    MemberRegisterConfigDO findByTagEnumAndFieldTypeNotAndParentIdEquals(Integer tagEnum, String fieldType, Long pid);
}
