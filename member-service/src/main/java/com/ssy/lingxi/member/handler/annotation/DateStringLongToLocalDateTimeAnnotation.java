package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.DateStringLongToLocalDateTimeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 时间戳格式的字符串转换为LocalDateTime校验注解
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-30
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {DateStringLongToLocalDateTimeValidator.class})
public @interface DateStringLongToLocalDateTimeAnnotation {
    boolean required() default true;

    boolean nullable() default true;

    String message() default "日期格式错误";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
