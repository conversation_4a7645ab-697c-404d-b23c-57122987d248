package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.do_.basic.CorporationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.enums.MemberInnerStatusEnum;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.UpperRelationIdAndNameResp;
import com.ssy.lingxi.member.model.resp.platform.RoleRuleManageResp;
import com.ssy.lingxi.member.model.resp.validate.WorkFlowStepResp;

import java.util.List;

/**
 * 会员审核、资料入库基础服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-14
 */
public interface IBaseMemberValidateService {
    /**
     * 根据创建下级会员的规则，查询下级会员的会员类型列表
     * @param memberTypeEnum 上级会员的会员类型枚举
     * @return 查询结果
     */
    List<MemberTypeAndNameResp> getSubMemberTypeList(Integer memberTypeEnum);

    /**
     * 根据创建下级会员的规则，查询下级会员的会员类型列表
     * @param memberTypeEnumList 上级会员的会员类型枚举列表
     * @return 查询结果
     */
    List<MemberTypeAndNameResp> getSubMemberTypeList(List<Integer> memberTypeEnumList);

    /**
     * 根据创建下级会员的规则，查询下级会员的会员类型列表(SASS)
     * @param memberTypeEnum 上级会员的会员类型枚举
     * @param roleManageVOList 会员适用角色list
     * @return 查询结果
     */
    List<MemberTypeAndNameResp> getSubMemberTypeList(Integer memberTypeEnum, List<RoleRuleManageResp> roleManageVOList);

    /**
     * 平台会员审核外部流程定义
     * @param roleName 角色名称
     * @param roleTag 角色标签
     * @return 步骤列表
     */
    List<WorkFlowStepResp> getPlatformValidateOuterSteps(String roleName, Integer roleTag);

    /**
     * 会员入库审核外部流程定义
     * @param relationDO 会员关系
     * @param roleTag 角色标签
     * @return 步骤列表
     */
    List<WorkFlowStepResp> getMemberDepositOuterSteps(MemberRelationDO relationDO, Integer roleTag);

    /**
     * 会员入库审核外部流程定义
     * @param roleName 上级角色名称
     * @param subRoleName 下级角色名称
     * @param roleTag 角色标签
     * @return 步骤列表
     */
    List<WorkFlowStepResp> getMemberDepositOuterSteps(String roleName, String subRoleName, Integer roleTag);

    /**
     * 会员变更审核外部流程定义
     * @param relationDO 会员关系
     * @return 步骤列表
     */
    List<WorkFlowStepResp> getMemberModifyOuterSteps(MemberRelationDO relationDO);

    /**
     * 添加角色时，检验角色是否符合规则定义
     * @param memberRoleDO 被检验的角色
     * @param memberTypeEnum 会员类型枚举，定义在MemberTypeEnum和MemberTypeDO中
     */
    void checkRoleWithMemberType(MemberRoleDO memberRoleDO, Integer memberTypeEnum);

    /**
     * 查询当前流程的步骤定义
     * @param relationDO 会员关系
     * @return 查询结果
     */
    WorkflowTaskListBO getMemberProcessSteps(MemberRelationDO relationDO);

    /**
     * 根据创建下级会员的规则，查询下级会员的角色列表
     * @param memberTypeEnum 上级会员的会员类型枚举
     * @param roleTag 角色标签
     * @return 查询结果
     */
    List<RoleIdAndNameResp> getSubRoleList(Integer memberTypeEnum, Integer roleTag);

    /**
     * 根据创建下级会员的规则，查询下级会员的角色列表(SAAS)
     *
     * @param memberTypeEnum   上级会员的会员类型枚举
     * @param roleManageVOList 会员适用角色list
     * @return 查询结果
     */
    List<RoleIdAndNameResp> getSubRoleList(Integer memberTypeEnum, List<RoleRuleManageResp> roleManageVOList);

    /**
     * 查询上级会员关系和名称列表
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @return 查询结果
     */
    List<UpperRelationIdAndNameResp> getUpperRelationList(Long subMemberId, Long subRoleId);

    /**
     * 新增角色时，根据会员类型，查询角色列表
     * @param memberType 会员类型Id
     * @return 查询结果
     */
    List<RoleIdAndNameResp> getRoleListByMemberType(Integer memberType);

    /**
     * 批量入库、资料审核
     * @param loginUser 登录用户
     * @param validateIds 审核内容Id列表
     * @param innerStatusEnum 匹配的内部状态
     * @param roleTag 角色标签
     * @return 审核结果
     */
    void batchExecMemberProcess(UserLoginCacheDTO loginUser, List<Long> validateIds, MemberInnerStatusEnum innerStatusEnum, Integer roleTag);

    /**
     * 批量入库、资料审核
     * @param loginUser 登录用户
     * @param validateIds 审核内容Id列表
     * @param innerStatusEnums 匹配的内部状态列表
     * @param roleTag 角色标签
     * @return 审核结果
     */
    void batchExecMemberProcess(UserLoginCacheDTO loginUser, List<Long> validateIds, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag);

    /**
     * 执行入库、资料变更流程
     * @param loginUser 登录用户
     * @param validateId 会员关系Id
     * @param agree  审核结果 0-不通过， 1-通过
     * @param reason 审核意见
     * @param innerStatusEnum 匹配的内部状态
     * @param roleTag 角色标签
     * @return 执行结果
     */
    void execMemberProcess(UserLoginCacheDTO loginUser, Long validateId, Integer agree, String reason, MemberInnerStatusEnum innerStatusEnum, Integer roleTag);

    /**
     * 执行入库、资料变更流程
     * @param loginUser 登录用户
     * @param validateId 会员关系Id
     * @param agree  审核结果 0-不通过， 1-通过
     * @param reason 审核意见
     * @param innerStatusEnums 匹配的内部状态列表
     * @return 执行结果
     */
    void execMemberProcess(UserLoginCacheDTO loginUser, Long validateId, Integer agree, String reason, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag);

    /**
     * 执行入库、资料变更流程
     * @param loginUser 登录用户
     * @param validateId 会员关系Id
     * @param agree  审核结果 0-不通过， 1-通过
     * @param reason 审核意见
     * @param innerStatusEnums 匹配的内部状态列表
     * @return 执行结果
     */
    void execMemberProcess(UserLoginCacheDTO loginUser, Long validateId, Integer agree, String reason, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag,String brandCode);


    /**
     * 执行入库、资料变更流程
     * @param loginUser 登录用户
     * @param validateId 会员关系Id
     * @param agree  审核结果 0-不通过， 1-通过
     * @param reason 审核意见
     * @param brandCode 品牌代码
     * @param simpleMemberName 客户简称
     * @param innerStatusEnums 匹配的内部状态列表
     * @return 执行结果
     */
    void execMemberProcess(UserLoginCacheDTO loginUser, Long validateId, Integer agree, String reason, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag,String brandCode,String simpleMemberName);
    /**
     * 执行入库、资料变更流程
     * @param loginUser 登录用户
     * @param relationDO 会员关系
     * @param agree    审核结果 0-不通过， 1-通过
     * @param reason   不通过的原因
     * @param innerStatusEnum 匹配的内部状态
     * @return 执行结果
     */
    void execMemberProcess(UserLoginCacheDTO loginUser, MemberRelationDO relationDO, Integer agree, String reason, MemberInnerStatusEnum innerStatusEnum, Integer roleTag);

    /**
     * 执行入库、资料变更流程
     * @param loginUser 登录用户
     * @param relationDO 会员关系
     * @param agree    审核结果 0-不通过， 1-通过
     * @param reason   不通过的原因
     * @param innerStatusEnums 匹配的内部状态列表
     * @return 执行结果
     */
    void execMemberProcess(UserLoginCacheDTO loginUser, MemberRelationDO relationDO, Integer agree, String reason, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag);

    /**
     * 初始化店铺
     * @param relationDO
     * @param corporationDO
     */
    void createInitShop(MemberRelationDO relationDO, CorporationDO corporationDO);
}
