package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.api.model.resp.RunBrandFeignResp;
import com.ssy.lingxi.member.model.req.basic.RunBrandAddReq;
import com.ssy.lingxi.member.model.resp.basic.RunBrandResp;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 会员店铺service
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/5/26
 */
public interface IRunBrandService {

    static String buildMemberName(String memberName,String brandCode){
        if(!StringUtils.hasLength(memberName)){
            return memberName;
        }
        String[] split = memberName.split("-\\w*$");
        return split[0]+"-"+brandCode;
    }
    List<RunBrandResp> list(UserLoginCacheDTO sysUser);

    List<RunBrandFeignResp> listByFeign();

    Long add(UserLoginCacheDTO sysUser, RunBrandAddReq req);
}
