package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.member.entity.bo.ButtonAuthBO;
import com.ssy.lingxi.member.entity.bo.MenuAuthBO;
import com.ssy.lingxi.member.entity.bo.AuthBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserRoleDO;
import com.ssy.lingxi.member.entity.do_.menuAuth.MenuDO;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * (内部)会员、用户菜单权限、数据权限服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-15
 */
public interface IBaseAuthService {
    /**
     * 获取指定端菜单id集合
     */
    Set<Long> getMenuIdSetBySource(Integer source);

    /**
     * 获取指定端按钮id集合
     */
    Set<Long> getButtonIdSetByMenuSource(Integer source);

    /**
     * 获取指定端菜单集合
     */
    Set<MenuAuthBO> getMenuSetBySource(Integer source, String language);

    /**
     * 获取指定端按钮集合
     */
    Set<ButtonAuthBO> getButtonSetBySource(Integer source, String language);

    /**
     * 根据菜单id集合查询菜单集合
     */
    Set<MenuAuthBO> getMenuSetByIdSet(Set<Long> menuIdSet, String language);

    /**
     * 根据按钮id集合查询按钮集合
     */
    Set<ButtonAuthBO> getButtonSetByIdSet(Set<Long> buttonIdSet, String language);

    /**
     * 根据菜单path集合查询菜单集合
     */
    Set<MenuAuthBO> getMenuSetByPathSet(Set<String> menuPathSet, String language);

    /**
     * 根据按钮path集合查询按钮集合
     */
    Set<ButtonAuthBO> getButtonSetByPathSet(Set<String> buttonPathSet, String language);

    /**
     * 根据用户id查询权限
     */
    AuthBO getAuthByUserId(Long userId);

    /**
     * 根据关系id查询权限
     */
    AuthBO getAuthByRelId(Long relId);

    /**
     * 查询角色菜单权限（树形方式返回）
     */
    AuthTreeResp getAuthTree(byte[] menuAuth, byte[] buttonAuth, CompletableFuture<Set<MenuAuthBO>> menuAuthBOSetFuture, CompletableFuture<Set<ButtonAuthBO>> buttonAuthBOSetFuture);

    /**
     * 重建单个会员权限
     * @param memberDO 会员
     * @return 合并结果
     */
    void rebuildMemberAuth(MemberDO memberDO);

    /**
     * 更新用户角色后，更新用户的权限
     * @param userRoleDO 已经更新的会员角色
     * @return 更新结果
     */
    void updateUserAuthByUserRole(UserRoleDO userRoleDO);

    /**
     * 该接口为了做性能优化，牺牲了一些可读性
     * 修改角色权限后，同步平台会员权限（已经删除的菜单，要从权限中剔除，新增的菜单不自动勾选）
     * 1.引用该会员角色的所有会员权限均要修改
     * 2.会员下自建的所有用户角色权限均要修改
     * 3.会员下的用户权限均要修改
     * 4.记录权限变更记录（待定）
     * 5.删除所有权限变更的会员的token，使其重新登陆（重构登陆缓存）
     *
     * @param memberRoleDO    角色
     * @return 更新结果
     */
    void rebuildMemberAuthByUpdateRoleAuth(MemberRoleDO memberRoleDO);

    /**
     * 获取菜单按钮权限相关信息
     * @param menuDOList 菜单列表
     * @return 权限元祖
     */
    AuthTuple<byte[],byte[]> getMenuAndButtonAuth(List<MenuDO> menuDOList);

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class AuthTuple<M, B> {
        /**
         * 菜单权限
         */
        M menuAuth;

        /**
         * 按钮权限
         */
        B buttonAuth;
    }
}
