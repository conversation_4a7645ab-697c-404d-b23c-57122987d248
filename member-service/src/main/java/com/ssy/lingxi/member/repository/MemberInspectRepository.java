package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.inspect.MemberInspectDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员考察Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Repository
public interface MemberInspectRepository extends JpaRepository<MemberInspectDO, Long>, JpaSpecificationExecutor<MemberInspectDO> {
    MemberInspectDO findFirstByMemberIdAndRoleIdAndSubMemberAndSubRoleIdAndSource(Long memberId, Long roleId, MemberDO subMember, Long subRoleId, Integer source);
}
