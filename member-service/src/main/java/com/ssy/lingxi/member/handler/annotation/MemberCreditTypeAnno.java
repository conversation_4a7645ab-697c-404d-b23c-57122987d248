package com.ssy.lingxi.member.handler.annotation;


import com.ssy.lingxi.member.handler.validator.MemberCreditTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 会员信用积分规则校验注解
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-09
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MemberCreditTypeValidator.class)
public @interface MemberCreditTypeAnno {
    boolean required() default true;

    String message() default "会员信用评估规则枚举参数值不在枚举定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
