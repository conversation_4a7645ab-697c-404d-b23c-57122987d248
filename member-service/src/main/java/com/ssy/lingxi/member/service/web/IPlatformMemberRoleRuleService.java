package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.model.req.basic.MemberIdReq;
import com.ssy.lingxi.member.model.req.platform.*;
import com.ssy.lingxi.member.model.resp.platform.*;
import org.springframework.http.HttpHeaders;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 会员角色规则配置相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-10
 **/
public interface IPlatformMemberRoleRuleService {
    /**
     * 根据名称，模糊、分页查询会员列表
     *
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberManageResp> pageMembers(PageMemberByNameDataReq pageVO);

    /**
     * 根据名称，模糊、分页查询会员管理列表
     *
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberRoleRuleResp> pageRuleMembers(PageMemberByNameDataReq pageVO);

    /**
     * 根据名称，模糊、分页查询角色详情列表
     *
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<RoleRuleManageResp> pageRoles(PageRoleRuleDataReq pageVO);

    /**
     * 查询当前会员适用角色详情列表
     *
     * @param id 接口参数
     * @return 查询结果
     */
    List<RoleRuleManageResp> memberRoles(Long id);

    /**
     * 查询下级会员适用角色详情列表
     *
     * @param id 接口参数
     * @return 查询结果
     */
    List<RoleRuleManageResp> subMemberRoles(Long id);

    /**
     * 查询下级会员适用角色详情列表(注册)
     *
     * @param id 接口参数
     * @return 查询结果
     */
    List<RoleRuleRegisterResp> subMemberRolesRegister(Long id);

    /**
     * 选择会员
     *
     * @param headers    Http头部信息
     * @param memberIdReq 接口参数
     */
    MemberSelectResp selectMember(HttpHeaders headers, MemberIdReq memberIdReq);

    /**
     * 新增会员适用角色
     *
     * @param headers    Http头部信息
     * @param roleRuleVO 接口参数
     */
    void addRuleRoles(HttpHeaders headers, AddRoleRuleReq roleRuleVO);

    /**
     * 删除会员适用角色
     *
     * @param headers    Http头部信息
     * @param roleRuleVO 接口参数
     */
    void delRuleRoles(HttpHeaders headers, @Valid DelRoleRuleReq roleRuleVO);

    /**
     * 会员适用角色详情列表
     *
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberRoleRuleDetailResp getDetails(@Valid MemberDetailByIdReq idVO);
}
