package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 菜单来源VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-04-28
 */
@Data
public class MenuSourceReq implements Serializable {
    private static final long serialVersionUID = -5661050467332722764L;

    /**
     * 适用平台客户端
     */
    @NotNull(message = "适用平台客户端枚举不能为空，1-Web，2-App，99-平台后台")
    private Integer source;
}
