package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.MemberCycleProcessDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 会员生命周期流程
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
public interface MemberCycleProcessRepository extends JpaRepository<MemberCycleProcessDO, Long>, JpaSpecificationExecutor<MemberCycleProcessDO> {

    MemberCycleProcessDO findFirstByProcessTypeAndIsDefaultAndSource(Integer processType, Integer isDefault, Integer source);

    List<MemberCycleProcessDO> findByMemberIdAndRoleIdAndProcessTypeAndStatus(Long memberId, Long roleId, Integer processType, Integer status);

    List<MemberCycleProcessDO> findByMemberIdAndRoleIdAndIsDefaultAndSource(Long memberId, Long roleId, Integer isDefault, Integer source);

    MemberCycleProcessDO findFirstByMemberIdAndRoleIdAndProcessTypeAndIsDefault(Long memberId, Long roleId, Integer processType, Integer isDefault);

    MemberCycleProcessDO findByMemberIdAndRoleIdAndId(Long memberId, Long memberRoleId, Long id);
}
