package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.ipMonitor.anno.LoginIpMonitor;
import com.ssy.lingxi.component.ipMonitor.anno.SmsCodeIpMonitor;
import com.ssy.lingxi.member.entity.bo.login.MobileLoginBO;
import com.ssy.lingxi.member.model.dto.MobileLoginDTO;
import com.ssy.lingxi.member.model.dto.MobilePhoneLoginDTO;
import com.ssy.lingxi.member.model.req.login.MobileLoginReq;
import com.ssy.lingxi.member.model.req.login.MobilePhoneLoginReq;
import com.ssy.lingxi.member.model.req.login.PhoneLoginSmsCode;
import com.ssy.lingxi.member.service.mobile.IMobileLoginService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 微信小程序-业务员登录
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-23
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/wechat/applet")
public class MobileWechatAppletLoginController {
    private final IMobileLoginService mobileLoginService;

    /**
     * 账号登录
     *
     * @param headers Http头部信息
     * @param loginVO 接口参数
     * @return 登录用户信息
     */
    @LoginIpMonitor
    @PostMapping("/login/account")
    public WrapperResp<MobileLoginBO> accountLogin(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobileLoginReq loginVO) {
        return WrapperUtil.success(mobileLoginService.accountOrPhoneLogin(headers, new MobileLoginDTO(loginVO, true)));
    }

    /**
     * 发送“手机号登录”的短信验证码
     *
     * @param headers Http头部信息
     * @param phoneReq 接口参数
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @PostMapping("/sends")
    public WrapperResp<Void> sendPhoneLoginSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneLoginSmsCode phoneReq) {
        mobileLoginService.sendPhoneLoginSmsCode(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 手机号登录
     *
     * @param headers Http头部信息
     * @param loginVO 接口参数
     * @return 登录用户信息
     */
    @LoginIpMonitor
    @PostMapping("/login/phone")
    public WrapperResp<MobileLoginBO> phoneLogin(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobilePhoneLoginReq loginVO) {
        return WrapperUtil.success(mobileLoginService.phoneLogin(headers, new MobilePhoneLoginDTO(loginVO, true)));
    }

}
