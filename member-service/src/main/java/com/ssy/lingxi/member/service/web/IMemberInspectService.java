package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.model.req.basic.NamePageDataReq;
import com.ssy.lingxi.member.model.req.basic.UserPageDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.MemberInspectAddReq;
import com.ssy.lingxi.member.model.req.lifecycle.MemberInspectPageDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.MemberInspectUpdateReq;
import com.ssy.lingxi.member.model.resp.basic.UserQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberInspectPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberInspectResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordInspectPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.SubMemberQueryResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员考察服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
public interface IMemberInspectService {

    /**
     * 获取“考察类型”下拉框条件
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<DropdownItemResp> getPageConditions(HttpHeaders headers);

    /**
     * “新增会员审查” - 选择下级会员
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<SubMemberQueryResp> pageSubMembers(HttpHeaders headers, NamePageDataReq pageVO, Integer roleTag);

    /**
     * “新增或修改会员审查” - 选择用户
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<UserQueryResp> pageUsers(HttpHeaders headers, UserPageDataReq pageVO);

    /**
     * 分页查询会员考察
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberInspectPageQueryResp> pageMemberInspect(HttpHeaders headers, MemberInspectPageDataReq pageVO, Integer roleTag);

    /**
     * 会员信息 - 会员详情 - 分页查询会员考察
     * @param memberId 上级会员Id
     * @param roleId    上级会员角色Id
     * @param subMember 下级会员
     * @param subRoleId 下级会员角色Id
     * @param current  当前页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberRecordInspectPageQueryResp> pageMemberInspect(Long memberId, Long roleId, MemberDO subMember, Long subRoleId, int current, int pageSize);

    /**
     * 新增会员考察
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @param roleTag 角色标签
     * @return 新增结果
     */
    void addMemberInspect(HttpHeaders headers, MemberInspectAddReq addVO, Integer roleTag);

    /**
     * 查询会员考察详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberInspectResp getMemberInspect(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);


    /**
     * 修改会员考察
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @param roleTag 角色标签
     * @return 修改结果
     */
    void updateMemberInspect(HttpHeaders headers, MemberInspectUpdateReq updateVO, Integer roleTag);

    /**
     * 删除会员考察
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 删除结果
     */
    void deleteMemberInspect(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);
}
