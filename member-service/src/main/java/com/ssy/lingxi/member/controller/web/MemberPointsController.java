package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.component.ipMonitor.anno.LoginIpMonitor;
import com.ssy.lingxi.member.model.req.points.MemberPointsBatchAddReq;
import com.ssy.lingxi.member.model.resp.points.MemberPointsQuerySearchConditionResp;
import com.ssy.lingxi.member.model.resp.points.MemberPointsSelectResp;
import com.ssy.lingxi.member.service.web.IMemberPointsService;
import org.springframework.web.bind.annotation.*;

import com.ssy.lingxi.member.model.req.points.MemberPointsPageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.resp.points.MemberPointsPageDataResp;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;


import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 平台后台 - 平台会员管理 - 会员积分
 *
 * <AUTHOR>
 * @since 2024-08-08 10:25:36
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/member/points")
public class MemberPointsController extends BaseController{
    /**
     * 服务对象
     */
    @Resource
    private IMemberPointsService memberPointsService;


    /**
     * 分页查询 - 下拉条件
     *
     * @return 查询结果
     */
    @GetMapping("/getPageCondition")
    public WrapperResp<MemberPointsQuerySearchConditionResp> getPageCondition() {
        return WrapperUtil.success(memberPointsService.getPageCondition(getSysUser()));
    }

    /**
     * 分页查询
     *
     * @param memberPointsPageDataReq 筛选条件
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberPointsPageDataResp>> page(@Valid MemberPointsPageDataReq memberPointsPageDataReq) {
        return WrapperUtil.success(memberPointsService.page(getSysUser(), memberPointsPageDataReq));
    }

    /**
     * 添加会员积分 - 选择会员弹窗
     *
     * @param memberPointsPageDataReq 筛选条件
     * @return 查询结果
     */
    @GetMapping("/selectMemberPage")
    public WrapperResp<PageDataResp<MemberPointsSelectResp>> selectMemberPage(@Valid MemberPointsPageDataReq memberPointsPageDataReq) {
        return WrapperUtil.success(memberPointsService.selectMemberPage(getSysUser(), memberPointsPageDataReq));
    }



    /**
     * 添加会员积分 - 提交
     *
     * @param batchAddReq 实体
     * @return 新增or修改结果
     */
    @PostMapping("/batchAdd")
    public WrapperResp<Void> batchAdd(@RequestBody @Valid MemberPointsBatchAddReq batchAddReq) {
        memberPointsService.batchAdd(getSysUser(),batchAddReq);
        return WrapperUtil.success();
    }

}

