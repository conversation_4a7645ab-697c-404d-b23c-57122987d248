package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.MessageNoticeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskResultBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.rectify.MemberRectifyDO;
import com.ssy.lingxi.member.enums.MemberRectifyAgreeResultEnum;
import com.ssy.lingxi.member.enums.MemberRectifyStatusEnum;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.repository.MemberRectifyRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberHistoryService;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberRectifyService;
import com.ssy.lingxi.member.util.CodeUtil;
import com.ssy.lingxi.member.util.FileObjectUtil;
import com.ssy.lingxi.workflow.api.enums.ProcessTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 会员整改服务实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Slf4j
@Service
public class MemberRectifyServiceImpl implements IMemberRectifyService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRectifyRepository memberRectifyRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private IBaseMemberHistoryService memberHistoryService;

    @Resource
    private IMessageFeignService messageFeignService;

    @Override
    public List<StatusResp> listMemberRectifyOuterStatus(HttpHeaders headers) {
        List<StatusResp> statusRespList = Stream.of(MemberRectifyStatusEnum.values()).map(e -> new StatusResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
        statusRespList.add(0, new StatusResp(0, MemberStringEnum.ALL.getName()));
        return statusRespList;
    }

    @Override
    public List<StatusResp> listSubMemberRectifyOuterStatus(HttpHeaders headers) {
        List<StatusResp> statusRespList = Stream.of(
                        MemberRectifyStatusEnum.WAIT_RECTIFY,
                        MemberRectifyStatusEnum.WAIT_CONFIRM,
                        MemberRectifyStatusEnum.REJECT,
                        MemberRectifyStatusEnum.APPROVE)
                .map(e -> new StatusResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
        statusRespList.add(0, new StatusResp(0, MemberStringEnum.ALL.getName()));
        return statusRespList;
    }

    @Override
    public PageDataResp<MemberRectifySummaryPageQueryResp> pageSummaryMemberRectify(HttpHeaders headers, MemberAddRectifySummaryPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Page<MemberRectifyDO> pageList = basePageMemberRectify(loginUser, NumberUtil.isNullOrZero(pageVO.getOuterStatus()) ? Collections.emptyList() : Collections.singletonList(pageVO.getOuterStatus()), pageVO, roleTag);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberRectifyDO -> {
            MemberRectifySummaryPageQueryResp queryVO = new MemberRectifySummaryPageQueryResp();
            queryVO.setId(memberRectifyDO.getId());
            queryVO.setRectifyNo(memberRectifyDO.getRectifyNo());
            queryVO.setOuterStatus(memberRectifyDO.getOuterStatus());
            queryVO.setOuterStatusName(MemberRectifyStatusEnum.getCodeMessage(memberRectifyDO.getOuterStatus()));
            queryVO.setSubject(memberRectifyDO.getSubject());
            queryVO.setName(memberRectifyDO.getSubMember().getName());
            queryVO.setRectifyDayStart(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeStart()));
            queryVO.setRectifyDayEnd(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeEnd()));
            queryVO.setAgreeResult(memberRectifyDO.getAgreeResult());
            queryVO.setAgreeResultName(MemberRectifyAgreeResultEnum.getCodeMessage(memberRectifyDO.getAgreeResult()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员信息 - 会员详情 - 分页查询会员整改
     *
     * @param member    上级会员
     * @param role      上级会员角色Id
     * @param subMember 下级会员
     * @param subRole   下级会员角色Id
     * @param current   当前页
     * @param pageSize  每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberRecordRectifyResp> pageMemberRecordRectify(MemberDO member, MemberRoleDO role, MemberDO subMember, MemberRoleDO subRole, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("createTime").descending());
        Page<MemberRectifyDO> pageList = memberRectifyRepository.findAll((Specification<MemberRectifyDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), member));
            predicateList.add(cb.equal(root.get("role").as(MemberRoleDO.class), role));
            predicateList.add(cb.equal(root.get("subMember").as(MemberDO.class), subMember));
            predicateList.add(cb.equal(root.get("subRole").as(MemberRoleDO.class), subRole));
            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        }, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberRectifyDO -> {
            MemberRecordRectifyResp queryVO = new MemberRecordRectifyResp();
            queryVO.setId(memberRectifyDO.getId());
            queryVO.setSubject(memberRectifyDO.getSubject());
            queryVO.setRectifyTimeStart(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeStart()));
            queryVO.setRectifyTimeEnd(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeEnd()));
            queryVO.setResult(memberRectifyDO.getAgreeResult() == null ? "" : (memberRectifyDO.getAgreeResult().equals(EnableDisableStatusEnum.ENABLE.getCode()) ? "整改通过" : "整改不通过"));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    @Override
    public PageDataResp<MemberRectifyAddPageQueryResp> pageAddMemberRectify(HttpHeaders headers, MemberAddRectifyPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Page<MemberRectifyDO> pageList = basePageMemberRectify(loginUser, Collections.singletonList(MemberRectifyStatusEnum.WAIT_SEND.getCode()), pageVO, roleTag);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberRectifyDO -> {
            MemberRectifyAddPageQueryResp queryVO = new MemberRectifyAddPageQueryResp();
            queryVO.setId(memberRectifyDO.getId());
            queryVO.setRectifyNo(memberRectifyDO.getRectifyNo());
            queryVO.setOuterStatus(memberRectifyDO.getOuterStatus());
            queryVO.setOuterStatusName(MemberRectifyStatusEnum.getCodeMessage(memberRectifyDO.getOuterStatus()));
            queryVO.setSubject(memberRectifyDO.getSubject());
            queryVO.setName(memberRectifyDO.getSubMember().getName());
            queryVO.setRectifyDayStart(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeStart()));
            queryVO.setRectifyDayEnd(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeEnd()));
            queryVO.setSendOrUpdateOrDel(MemberRectifyStatusEnum.WAIT_SEND.getCode().equals(memberRectifyDO.getOuterStatus()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    @Override
    public PageDataResp<MemberRectifyConfirmPageQueryResp> pageConfirmMemberRectify(HttpHeaders headers, MemberAddRectifyPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Page<MemberRectifyDO> pageList = basePageMemberRectify(loginUser, Collections.singletonList(MemberRectifyStatusEnum.WAIT_CONFIRM.getCode()), pageVO, roleTag);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberRectifyDO -> {
            MemberRectifyConfirmPageQueryResp queryVO = new MemberRectifyConfirmPageQueryResp();
            queryVO.setId(memberRectifyDO.getId());
            queryVO.setRectifyNo(memberRectifyDO.getRectifyNo());
            queryVO.setOuterStatus(memberRectifyDO.getOuterStatus());
            queryVO.setOuterStatusName(MemberRectifyStatusEnum.getCodeMessage(memberRectifyDO.getOuterStatus()));
            queryVO.setSubject(memberRectifyDO.getSubject());
            queryVO.setName(memberRectifyDO.getSubMember().getName());
            queryVO.setRectifyDayStart(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeStart()));
            queryVO.setRectifyDayEnd(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeEnd()));
            queryVO.setConfirm(MemberRectifyStatusEnum.WAIT_CONFIRM.getCode().equals(memberRectifyDO.getOuterStatus()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    @Override
    public PageDataResp<MemberRectifyManagePageQueryResp> pageManageMemberRectify(HttpHeaders headers, MemberAddRectifySummaryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());

        Page<MemberRectifyDO> pageList = memberRectifyRepository.findAll((Specification<MemberRectifyDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("subMember"), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("subRole"), loginUser.getMemberRoleId()));

            if (NumberUtil.notNullOrZero(pageVO.getOuterStatus())) {
                predicateList.add(cb.equal(root.get("outerStatus"), pageVO.getOuterStatus()));
            }

            if (StringUtils.isNotEmpty(pageVO.getName())) {
                Join<MemberRectifyDO, MemberDO> upperMemberJoin = root.join("member", JoinType.LEFT);
                predicateList.add(cb.like(upperMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getSubject())) {
                predicateList.add(cb.like(root.get("subject"), "%" + pageVO.getSubject() + "%"));
            }

            if (Objects.nonNull(pageVO.getRectifyDayStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateStart(pageVO.getRectifyDayStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("rectifyTimeStart"), startDate));
            }

            if (Objects.nonNull(pageVO.getRectifyDayEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateEnd(pageVO.getRectifyDayEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("rectifyTimeEnd"), endDate));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        }, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberRectifyDO -> {
            MemberRectifyManagePageQueryResp queryVO = new MemberRectifyManagePageQueryResp();
            queryVO.setId(memberRectifyDO.getId());
            queryVO.setRectifyNo(memberRectifyDO.getRectifyNo());
            queryVO.setOuterStatus(memberRectifyDO.getOuterStatus());
            queryVO.setOuterStatusName(MemberRectifyStatusEnum.getCodeMessage(memberRectifyDO.getOuterStatus()));
            queryVO.setSubject(memberRectifyDO.getSubject());
            queryVO.setUpperMemberName(memberRectifyDO.getMember().getName());
            queryVO.setRectifyDayStart(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeStart()));
            queryVO.setRectifyDayEnd(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeEnd()));
            queryVO.setAgreeResult(memberRectifyDO.getAgreeResult());
            queryVO.setAgreeResultName(MemberRectifyAgreeResultEnum.getCodeMessage(memberRectifyDO.getAgreeResult()));
            List<Integer> statusList = Arrays.asList(MemberRectifyStatusEnum.WAIT_RECTIFY.getCode(), MemberRectifyStatusEnum.REJECT.getCode());
            queryVO.setRectifyOrUpdate(statusList.contains(memberRectifyDO.getOuterStatus()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    private Page<MemberRectifyDO> basePageMemberRectify(UserLoginCacheDTO loginUser, List<Integer> statusList, MemberAddRectifyPageDataReq pageVO, Integer roleTag) {
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());

        return memberRectifyRepository.findAll((Specification<MemberRectifyDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("member"), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("role"), loginUser.getMemberRoleId()));

            if (!CollectionUtils.isEmpty(statusList)) {
                predicateList.add(cb.and(root.get("outerStatus").in(statusList)));
            }

            if (StringUtils.isNotEmpty(pageVO.getName())) {
                Join<MemberRectifyDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                predicateList.add(cb.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getSubject())) {
                predicateList.add(cb.like(root.get("subject"), "%" + pageVO.getSubject() + "%"));
            }

            if (Objects.nonNull(pageVO.getRectifyDayStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateStart(pageVO.getRectifyDayStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("rectifyTimeStart"), startDate));
            }

            if (Objects.nonNull(pageVO.getRectifyDayEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateEnd(pageVO.getRectifyDayEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("rectifyTimeEnd"), endDate));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
                predicateList.add(cb.equal(subRoleJoin.get("roleTag").as(Integer.class), roleTag));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        }, page);
    }

    @Override
    public MemberRectifyResp getMemberRectify(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRectifyDO memberRectifyDO = memberRectifyRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberRectifyDO)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (memberRectifyDO.getSubRole().getRoleTag() == null || !roleTag.equals(memberRectifyDO.getSubRole().getRoleTag()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberRectifyResp memberRectifyResp = new MemberRectifyResp();
        memberRectifyResp.setId(memberRectifyDO.getId());
        memberRectifyResp.setRectifyNo(memberRectifyDO.getRectifyNo());
        memberRectifyResp.setOuterStatus(memberRectifyDO.getOuterStatus());
        memberRectifyResp.setOuterStatusName(MemberRectifyStatusEnum.getCodeMessage(memberRectifyDO.getOuterStatus()));
        memberRectifyResp.setSubject(memberRectifyDO.getSubject());
        memberRectifyResp.setSubMemberId(memberRectifyDO.getSubMember().getId());
        memberRectifyResp.setSubRoleId(memberRectifyDO.getSubRole().getId());
        memberRectifyResp.setName(memberRectifyDO.getSubMember().getName());
        memberRectifyResp.setRectifyDayStart(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeStart()));
        memberRectifyResp.setRectifyDayEnd(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeEnd()));
        memberRectifyResp.setReason(memberRectifyDO.getReason());
        memberRectifyResp.setRequire(memberRectifyDO.getRequire());
        memberRectifyResp.setAttachments(FileObjectUtil.toVOList(memberRectifyDO.getAttachments()));

        WrapperResp<WorkflowTaskListBO> result = workflowFeignService.listExternalProcessSteps(memberRectifyDO.getRole().getId(), memberRectifyDO.getSubRole().getId(), MemberConstant.MEMBER_RECTIFY_PROCESS_KEY, memberRectifyDO.getTaskId());
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {

            throw new BusinessException(result.getCode(), result.getMessage());
        }


        if (StringUtils.isBlank(memberRectifyDO.getTaskId()) && MemberRectifyStatusEnum.APPROVE.getCode().equals(memberRectifyDO.getOuterStatus())) {
            memberRectifyResp.setCurrentOuterStep(4);
        } else {
            memberRectifyResp.setCurrentOuterStep(result.getData().getCurrentStep());
        }
        memberRectifyResp.setOuterVerifySteps(result.getData().getStepList());
        memberRectifyResp.setOuterHistory(memberHistoryService.listMemberRectifyOuterHistory(memberRectifyDO.getId()));
        memberRectifyResp.setInnerHistory(memberHistoryService.listMemberRectifyInnerHistory(memberRectifyDO.getId()));

        return memberRectifyResp;
    }

    @Override
    public MemberRectifyResultResp getMemberRectifyResult(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRectifyDO memberRectifyDO = memberRectifyRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberRectifyDO)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberRectifyDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberRectifyDO.getSubRole().getRoleTag()) )) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberRectifyResultResp memberRectifyVO = new MemberRectifyResultResp();
        memberRectifyVO.setId(memberRectifyDO.getId());
        memberRectifyVO.setRectifyNo(memberRectifyDO.getRectifyNo());
        memberRectifyVO.setOuterStatus(memberRectifyDO.getOuterStatus());
        memberRectifyVO.setOuterStatusName(MemberRectifyStatusEnum.getCodeMessage(memberRectifyDO.getOuterStatus()));
        memberRectifyVO.setSubject(memberRectifyDO.getSubject());
        memberRectifyVO.setSubMemberId(memberRectifyDO.getSubMember().getId());
        memberRectifyVO.setSubRoleId(memberRectifyDO.getSubRole().getId());
        memberRectifyVO.setName(memberRectifyDO.getSubMember().getName());
        memberRectifyVO.setRectifyDayStart(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeStart()));
        memberRectifyVO.setRectifyDayEnd(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeEnd()));
        memberRectifyVO.setReason(memberRectifyDO.getReason());
        memberRectifyVO.setRequire(memberRectifyDO.getRequire());
        memberRectifyVO.setAttachments(FileObjectUtil.toVOList(memberRectifyDO.getAttachments()));
        memberRectifyVO.setReportDigest(memberRectifyDO.getReportDigest());
        memberRectifyVO.setReportAttachments(FileObjectUtil.toVOList(memberRectifyDO.getReportAttachments()));
        memberRectifyVO.setAgreeResult(memberRectifyDO.getAgreeResult());
        memberRectifyVO.setResultRemark(memberRectifyDO.getResultRemark());

        WrapperResp<WorkflowTaskListBO> result = workflowFeignService.listExternalProcessSteps(memberRectifyDO.getRole().getId(), memberRectifyDO.getSubRole().getId(), MemberConstant.MEMBER_RECTIFY_PROCESS_KEY, memberRectifyDO.getTaskId());
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {


            throw new BusinessException(result.getCode(), result.getMessage());
        }

        if (StringUtils.isBlank(memberRectifyDO.getTaskId()) && MemberRectifyStatusEnum.APPROVE.getCode().equals(memberRectifyDO.getOuterStatus())) {
            memberRectifyVO.setCurrentOuterStep(4);
        } else {
            memberRectifyVO.setCurrentOuterStep(result.getData().getCurrentStep());
        }
        memberRectifyVO.setOuterVerifySteps(result.getData().getStepList());
        memberRectifyVO.setOuterHistory(memberHistoryService.listMemberRectifyOuterHistory(memberRectifyDO.getId()));
        memberRectifyVO.setInnerHistory(memberHistoryService.listMemberRectifyInnerHistory(memberRectifyDO.getId()));

        return memberRectifyVO;
    }

    @Override
    public MemberRectifyManageResp getMemberRectifyManage(HttpHeaders headers, CommonIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRectifyDO memberRectifyDO = memberRectifyRepository.findById(idVO.getId()).orElse(null);
        if (memberRectifyDO == null
                || !memberRectifyDO.getSubMember().getId().equals(loginUser.getMemberId())
                || !memberRectifyDO.getSubRole().getId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberRectifyManageResp memberRectifyVO = new MemberRectifyManageResp();
        memberRectifyVO.setId(memberRectifyDO.getId());
        memberRectifyVO.setRectifyNo(memberRectifyDO.getRectifyNo());
        memberRectifyVO.setOuterStatus(memberRectifyDO.getOuterStatus());
        memberRectifyVO.setOuterStatusName(MemberRectifyStatusEnum.getCodeMessage(memberRectifyDO.getOuterStatus()));
        memberRectifyVO.setSubject(memberRectifyDO.getSubject());
        memberRectifyVO.setMemberId(memberRectifyDO.getMember().getId());
        memberRectifyVO.setRoleId(memberRectifyDO.getSubRole().getId());
        memberRectifyVO.setUpperMemberName(memberRectifyDO.getMember().getName());
        memberRectifyVO.setRectifyDayStart(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeStart()));
        memberRectifyVO.setRectifyDayEnd(DateTimeUtil.formatDate(memberRectifyDO.getRectifyTimeEnd()));
        memberRectifyVO.setReason(memberRectifyDO.getReason());
        memberRectifyVO.setRequire(memberRectifyDO.getRequire());
        memberRectifyVO.setAttachments(FileObjectUtil.toVOList(memberRectifyDO.getAttachments()));
        memberRectifyVO.setReportDigest(memberRectifyDO.getReportDigest());
        memberRectifyVO.setReportAttachments(FileObjectUtil.toVOList(memberRectifyDO.getReportAttachments()));
        memberRectifyVO.setAgreeResult(memberRectifyDO.getAgreeResult());
        memberRectifyVO.setResultRemark(memberRectifyDO.getResultRemark());

        WrapperResp<WorkflowTaskListBO> result = workflowFeignService.listExternalProcessSteps(memberRectifyDO.getRole().getId(), memberRectifyDO.getSubRole().getId(), MemberConstant.MEMBER_RECTIFY_PROCESS_KEY, memberRectifyDO.getTaskId());
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {


            throw new BusinessException(result.getCode(), result.getMessage());
        }

        if (StringUtils.isBlank(memberRectifyDO.getTaskId()) && MemberRectifyStatusEnum.APPROVE.getCode().equals(memberRectifyDO.getOuterStatus())) {
            memberRectifyVO.setCurrentOuterStep(4);
        } else {
            memberRectifyVO.setCurrentOuterStep(result.getData().getCurrentStep());
        }
        memberRectifyVO.setOuterVerifySteps(result.getData().getStepList());
        memberRectifyVO.setOuterHistory(memberHistoryService.listMemberRectifyOuterHistory(memberRectifyDO.getId()));

        return memberRectifyVO;
    }

    @Override
    public void updateReportMemberRectify(HttpHeaders headers, MemberRectifyReportReq reportVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRectifyDO memberRectifyDO = memberRectifyRepository.findById(reportVO.getId()).orElse(null);
        if (memberRectifyDO == null
                || !memberRectifyDO.getSubMember().getId().equals(loginUser.getMemberId())
                || !memberRectifyDO.getSubRole().getId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        memberRectifyDO.setReportDigest(reportVO.getReportDigest());
        memberRectifyDO.setReportAttachments(FileObjectUtil.toBOList(reportVO.getReportAttachments()));

        memberRectifyRepository.saveAndFlush(memberRectifyDO);


    }

    /**
     * 待确认整改结果 - 会员整改新增
     *
     * @param headers Http头部信息
     * @param addVO   接口参数
     * @param roleTag 角色标签
     * @return 新增结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addMemberRectify(HttpHeaders headers, MemberRectifyAddReq addVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(
                loginUser.getMemberId(), loginUser.getMemberRoleId(),
                addVO.getSubMemberId(), addVO.getSubRoleId());

        if (relationDO == null
                || !relationDO.getMemberId().equals(loginUser.getMemberId())
                || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (!MemberStatusEnum.NORMAL.getCode().equals(relationDO.getStatus())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_STATUS_INCORRECT_OPERATE_INVALID);
        }

        MemberRectifyDO memberRectifyDO = new MemberRectifyDO();
        memberRectifyDO.setSubject(addVO.getSubject());
        memberRectifyDO.setSubMember(relationDO.getSubMember());
        memberRectifyDO.setSubRole(relationDO.getSubRole());
        memberRectifyDO.setAttachments(FileObjectUtil.toBOList(addVO.getAttachments()));
        memberRectifyDO.setMember(relationDO.getMember());
        memberRectifyDO.setRole(relationDO.getRole());
        memberRectifyDO.setCreateTime(LocalDateTime.now());
        memberRectifyDO.setRectifyTimeStart(DateTimeUtil.parseConcatDateTime(addVO.getRectifyDayStart()));
        memberRectifyDO.setRectifyTimeEnd(DateTimeUtil.parseConcatDateTime(addVO.getRectifyDayEnd()));
        memberRectifyDO.setReason(addVO.getReason());
        memberRectifyDO.setRequire(addVO.getRequire());
        memberRectifyRepository.saveAndFlush(memberRectifyDO);

        memberRectifyDO.setRectifyNo("RN" + CodeUtil.digits32(memberRectifyDO.getId(), 4));
        memberRectifyRepository.saveAndFlush(memberRectifyDO);

        // 工作流
        WorkflowTaskResultBO result = workflowFeignService.startMemberProcess(MemberConstant.MEMBER_RECTIFY_PROCESS_KEY,
                loginUser.getMemberId(), loginUser.getMemberRoleId(), memberRectifyDO.getId());
        memberRectifyDO.setInnerStatus(result.getInnerStatus());
        memberRectifyDO.setOuterStatus(result.getInnerStatus());
        memberRectifyDO.setTaskId(result.getTaskId());
        memberRectifyRepository.saveAndFlush(memberRectifyDO);

        // 发送消息
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        if (roleTagEnum == RoleTagEnum.MEMBER) {
            messageFeignService.sendSystemMessage(memberRectifyDO.getMember().getId(), memberRectifyDO.getRole().getId(),
                    MessageNoticeEnum.MC_RECTIFY_NOTIFICATION.getCode(), Arrays.asList(memberRectifyDO.getRole().getRoleName(), memberRectifyDO.getMember().getName()));
        }


    }

    @Override
    public void updateMemberRectify(HttpHeaders headers, MemberRectifyUpdateReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRectifyDO memberRectifyDO = memberRectifyRepository.findById(updateVO.getId()).orElse(null);
        if (Objects.isNull(memberRectifyDO)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberRectifyStatusEnum.WAIT_SEND.getCode().equals(memberRectifyDO.getOuterStatus())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(
                loginUser.getMemberId(), loginUser.getMemberRoleId(),
                updateVO.getSubMemberId(), updateVO.getSubRoleId());
        if (relationDO == null
                || !relationDO.getMemberId().equals(loginUser.getMemberId())
                || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        memberRectifyDO.setSubject(updateVO.getSubject());
        memberRectifyDO.setSubMember(relationDO.getSubMember());
        memberRectifyDO.setSubRole(relationDO.getSubRole());
        memberRectifyDO.setAttachments(FileObjectUtil.toBOList(updateVO.getAttachments()));
        memberRectifyDO.setMember(relationDO.getMember());
        memberRectifyDO.setRole(relationDO.getRole());
        memberRectifyDO.setCreateTime(LocalDateTime.now());
        memberRectifyDO.setRectifyTimeStart(DateTimeUtil.parseConcatDateTime(updateVO.getRectifyDayStart()));
        memberRectifyDO.setRectifyTimeEnd(DateTimeUtil.parseConcatDateTime(updateVO.getRectifyDayEnd()));
        memberRectifyDO.setReason(updateVO.getReason());
        memberRectifyDO.setRequire(updateVO.getRequire());
        memberRectifyRepository.saveAndFlush(memberRectifyDO);


    }

    @Override
    public void deleteMemberRectify(HttpHeaders headers, CommonIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRectifyDO memberRectifyDO = memberRectifyRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberRectifyDO)
                || !memberRectifyDO.getMember().getId().equals(loginUser.getMemberId())
                || !memberRectifyDO.getRole().getId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberRectifyStatusEnum.WAIT_SEND.getCode().equals(memberRectifyDO.getOuterStatus())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        memberRectifyRepository.delete(memberRectifyDO);


    }

    /**
     * 待确认整改结果 - 发送
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sendMemberRectify(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRectifyDO memberRectifyDO = memberRectifyRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberRectifyDO)
                || !memberRectifyDO.getMember().getId().equals(loginUser.getMemberId())
                || !memberRectifyDO.getRole().getId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberRectifyStatusEnum.WAIT_SEND.getCode().equals(memberRectifyDO.getOuterStatus())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(
                memberRectifyDO.getMember().getId(), memberRectifyDO.getRole().getId(),
                memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId());
        if (Objects.isNull(relationDO)) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (!MemberStatusEnum.NORMAL.getCode().equals(relationDO.getStatus())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_STATUS_INCORRECT_OPERATE_INVALID);
        }

        List<Integer> statusList = Arrays.asList(MemberStatusEnum.NORMAL.getCode(), MemberStatusEnum.CORRECTING.getCode());
        if (!statusList.contains(relationDO.getStatus())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_UPPER_MEMBER_STATUS_IS_NOT_NORMAL);
        }

        // 工作流
        WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_RECTIFY_PROCESS_KEY, memberRectifyDO.getTaskId(),
                loginUser.getMemberId(), loginUser.getMemberRoleId(), memberRectifyDO.getId(), ProcessTaskStatusEnum.GOTO_NEXT_STEP.getCode());

        memberRectifyDO.setInnerStatus(result.getInnerStatus());
        memberRectifyDO.setOuterStatus(result.getInnerStatus());
        memberRectifyDO.setTaskId(result.getTaskId());
        memberRectifyRepository.saveAndFlush(memberRectifyDO);

        //修改会员关系的状态
        if (MemberStatusEnum.NORMAL.getCode().equals(relationDO.getStatus())) {
            relationDO.setStatus(MemberStatusEnum.CORRECTING.getCode());
            relationRepository.saveAndFlush(relationDO);
        }

        // 发送消息
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        switch (roleTagEnum) {
            case MEMBER:
                messageFeignService.sendSystemMessage(memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId(),
                        MessageNoticeEnum.MC_RECTIFY_WAIT_RECTIFY.getCode(), Arrays.asList(memberRectifyDO.getRole().getRoleName(), memberRectifyDO.getMember().getName()));
                break;
            case SUPPLIER:
                messageFeignService.sendSystemMessage(memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId(),
                        MessageNoticeEnum.VENDOR_RECTIFY_RECEIVED_NOTICE.getCode(), Arrays.asList(memberRectifyDO.getRectifyNo(), memberRectifyDO.getSubject()));
                break;
            default:
                break;
        }

        memberHistoryService.saveMemberRectifyOuterHistory(loginUser, memberRectifyDO.getId(),
                memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId(),
                memberRectifyDO.getOuterStatus(), result.getOperation(), "");

        memberHistoryService.saveMemberRectifyInnerHistory(loginUser, memberRectifyDO.getId(),
                memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId(),
                memberRectifyDO.getOuterStatus(), result.getOperation(), "");


    }

    /**
     * 待确认整改结果 - 整改
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void rectifyMemberRectify(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRectifyDO memberRectifyDO = memberRectifyRepository.findById(idVO.getId()).orElse(null);
        if (memberRectifyDO == null
                || !memberRectifyDO.getSubMember().getId().equals(loginUser.getMemberId())
                || !memberRectifyDO.getSubRole().getId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (CollectionUtils.isEmpty(memberRectifyDO.getReportAttachments())
                || CollectionUtils.isEmpty(memberRectifyDO.getReportAttachments().stream().filter(fileBO -> StringUtils.isNotBlank(fileBO.getUrl())).collect(Collectors.toList()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECTIFY_REPORT_NO_UPLOAD);
        }

        // 工作流
        WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_RECTIFY_PROCESS_KEY, memberRectifyDO.getTaskId(),
                loginUser.getMemberId(), loginUser.getMemberRoleId(), memberRectifyDO.getId(), ProcessTaskStatusEnum.GOTO_NEXT_STEP.getCode());
        memberRectifyDO.setInnerStatus(result.getInnerStatus());
        memberRectifyDO.setOuterStatus(result.getInnerStatus());
        memberRectifyDO.setTaskId(result.getTaskId());
        memberRectifyRepository.saveAndFlush(memberRectifyDO);

        // 发送消息
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        switch (roleTagEnum) {
            case MEMBER:
                messageFeignService.sendSystemMessage(memberRectifyDO.getMember().getId(), memberRectifyDO.getRole().getId(),
                        MessageNoticeEnum.MC_RECTIFY_RESULT_CONFIRM.getCode(), Arrays.asList(memberRectifyDO.getRole().getRoleName(), memberRectifyDO.getMember().getName()));
                break;
            case SUPPLIER:
                messageFeignService.sendSystemMessage(memberRectifyDO.getMember().getId(), memberRectifyDO.getRole().getId(),
                        MessageNoticeEnum.VENDOR_RECTIFY_SEND_NOTICE.getCode(), Collections.singletonList(memberRectifyDO.getSubMember().getName()));
                break;
            default:
                break;
        }

        memberHistoryService.saveMemberRectifyOuterHistory(loginUser, memberRectifyDO.getId(),
                memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId(),
                memberRectifyDO.getOuterStatus(), result.getOperation(), "");


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirmMemberRectify(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRectifyDO memberRectifyDO = memberRectifyRepository.findById(agreeVO.getId()).orElse(null);
        if (Objects.isNull(memberRectifyDO)
                || !memberRectifyDO.getMember().getId().equals(loginUser.getMemberId())
                || !memberRectifyDO.getRole().getId().equals(loginUser.getMemberRoleId())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberRectifyDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberRectifyDO.getSubRole().getRoleTag()) )) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberRelationDO memberRelationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(
                memberRectifyDO.getMember().getId(), memberRectifyDO.getRole().getId(),
                memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId());
        if (Objects.isNull(memberRelationDO)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }
        if (!MemberStatusEnum.CORRECTING.getCode().equals(memberRelationDO.getStatus())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 工作流
        WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_RECTIFY_PROCESS_KEY, memberRectifyDO.getTaskId(),
                loginUser.getMemberId(), loginUser.getMemberRoleId(), memberRectifyDO.getId(), agreeVO.getAgree());
        memberRectifyDO.setInnerStatus(result.getInnerStatus());
        memberRectifyDO.setOuterStatus(result.getInnerStatus());
        memberRectifyDO.setTaskId(result.getTaskId());
        memberRectifyDO.setAgreeResult(agreeVO.getAgree());
        memberRectifyDO.setResultRemark(agreeVO.getReason());
        memberRectifyRepository.saveAndFlush(memberRectifyDO);

        //修改会员关系的状态
        if (EnableDisableStatusEnum.ENABLE.getCode().equals(agreeVO.getAgree())) {
            memberRelationDO.setStatus(MemberStatusEnum.NORMAL.getCode());
            relationRepository.saveAndFlush(memberRelationDO);
        }

        if (!EnableDisableStatusEnum.ENABLE.getCode().equals(agreeVO.getAgree())) {
            // 发送消息
            messageFeignService.sendSystemMessage(memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId(),
                    MessageNoticeEnum.MC_RECTIFY_WAIT_RECTIFY.getCode(), Arrays.asList(memberRectifyDO.getRole().getRoleName(), memberRectifyDO.getMember().getName()));
        }

        memberHistoryService.saveMemberRectifyOuterHistory(loginUser, memberRectifyDO.getId(),
                memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId(),
                memberRectifyDO.getOuterStatus(), result.getOperation(), agreeVO.getReason());

        memberHistoryService.saveMemberRectifyInnerHistory(loginUser, memberRectifyDO.getId(),
                memberRectifyDO.getSubMember().getId(), memberRectifyDO.getSubRole().getId(),
                memberRectifyDO.getOuterStatus(), result.getOperation(), agreeVO.getReason());


    }
}
