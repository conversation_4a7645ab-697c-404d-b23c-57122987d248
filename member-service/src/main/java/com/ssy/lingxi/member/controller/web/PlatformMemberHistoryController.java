package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.MemberHistoryPageDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberInnerHistoryDetailResp;
import com.ssy.lingxi.member.model.resp.basic.MemberOuterHitoryDetailResp;
import com.ssy.lingxi.member.service.web.IPlatformMemberHistoryService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 平台后台 - 日志中心相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-04
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/history")
public class PlatformMemberHistoryController {
    @Resource
    private IPlatformMemberHistoryService platformMemberHistoryService;

    /**
     * 分页查询外部流转记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/outer/page")
    public WrapperResp<PageDataResp<MemberOuterHitoryDetailResp>> findOuterHistories(@RequestHeader HttpHeaders headers, @Valid MemberHistoryPageDataReq pageVO) {
        return WrapperUtil.success(platformMemberHistoryService.findOuterHistories(headers, pageVO));
    }

    /**
     * 分页查询内部流转记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/inner/page")
    public WrapperResp<PageDataResp<MemberInnerHistoryDetailResp>> findInnerHistories(@RequestHeader HttpHeaders headers, @Valid MemberHistoryPageDataReq pageVO) {
        return WrapperUtil.success(platformMemberHistoryService.findInnerHistories(headers, pageVO));
    }
}
