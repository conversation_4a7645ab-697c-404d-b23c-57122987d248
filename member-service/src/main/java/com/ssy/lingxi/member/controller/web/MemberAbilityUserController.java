package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserQueryResp;
import com.ssy.lingxi.member.service.web.IMemberAbilityUserService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 系统能力-权限管理-用户管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-29
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/user")
public class MemberAbilityUserController {
    @Resource
    private IMemberAbilityUserService memberAbilityUserService;

    /**
     * 新增会员用户
     * @param headers HttpHeaders信息
     * @param memberUserVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/add")
    public WrapperResp<Void> addMemberUser(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserAddReq memberUserVO) {
        memberAbilityUserService.addMemberUser(headers, memberUserVO);
        return WrapperUtil.success();
    }

    /**
     * 查询用户信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberUserGetResp> getMemberUser(@RequestHeader HttpHeaders headers, @Valid MemberUserIdReq idVO) {
        return WrapperUtil.success(memberAbilityUserService.getMemberUser(headers, idVO));
    }

    /**
     * 修改会员用户
     * @param headers HttpHeaders信息
     * @param memberUserVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> updateMemberUser(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserUpdateReq memberUserVO) {
        memberAbilityUserService.updateMemberUser(headers, memberUserVO);
        return WrapperUtil.success();
    }

    /**
     * 删除会员用户
     * @param headers HttpHeaders信息
     * @param memberUserIdReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> deleteMemberUser(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserIdReq memberUserIdReq) {
        memberAbilityUserService.deleteMemberUser(headers, memberUserIdReq);
        return WrapperUtil.success();
    }

    /**
     * 更改用户状态
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/updatestatus")
    public WrapperResp<Void> updateMemberUserStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserUpdateStatusReq statusVO) {
        memberAbilityUserService.updateMemberUserStatus(headers, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 分页查询会员用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberUserQueryResp>> pageMemberUser(@RequestHeader HttpHeaders headers, @Valid PageQueryMemberUserDataReq pageVO) {
        return WrapperUtil.success(memberAbilityUserService.pageMemberUser(headers, pageVO));
    }

    /**
     * 8D整改- 分页查询会员下的用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/eight/list")
    public WrapperResp<PageDataResp<MemberUserQueryResp>> eightMemberUser(@RequestHeader HttpHeaders headers, @RequestBody @Valid PageQueryMemberUserDataReq pageVO) {
        return WrapperUtil.success(memberAbilityUserService.eightMemberUser(headers, pageVO));
    }

    /**
     * 分页查询会员用户（业务员）
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/salesMan/page")
    public WrapperResp<List<MemberUserQueryResp>> pageMemberSalesMan(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityUserService.pageMemberSalesMan(headers));
    }

    /**
     * 根据角色id分页查询会员用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/pageByRoleId")
    public WrapperResp<PageDataResp<MemberUserQueryResp>> pageMemberUserByRoleId(@RequestHeader HttpHeaders headers, @Valid PageQueryMemberUserDataReq pageVO) {
        return WrapperUtil.success(memberAbilityUserService.pageMemberUserByRoleId(headers, pageVO));
    }

    /**
     * 根据传入的userId进行IM 用户注册
     */
    @RequestMapping(value = "/registerTencentIM", method = RequestMethod.GET)
    public WrapperResp<Void> registerTencentIM(@RequestParam("userId") Long userId) {
        return memberAbilityUserService.registerTencentIM(userId);
    }
}
