package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.DateStringFormatValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 日期字符串格式校验注解
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-10
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {DateStringFormatValidator.class})
public @interface DateStringFormatAnnotation {
    boolean nullable() default true;

    String format() default "yyyy-MM-dd";

    String message() default "日期格式必须为yyyy-MM-dd";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
