package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.member.enums.MemberRegisterSourceEnum;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.login.MemberRegisterReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordByEmailCodeReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordBySmsCodeReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterResultResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterTypeMenuResp;
import com.ssy.lingxi.member.model.resp.login.MultiAccInfoResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseRegisterService;
import com.ssy.lingxi.member.service.web.IMemberRegisterService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户注册接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-03
 */
@Service
public class MemberRegisterServiceImpl implements IMemberRegisterService {
    @Resource
    private IBaseRegisterService baseRegisterService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    /**
     * 检查手机号码是否被注册使用
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    @Override
    public void checkPhoneRegistered(HttpHeaders headers, PhoneReq phoneReq) {
        memberCacheService.checkWebRequestHeader(headers);
        baseRegisterService.checkPhoneRegistered(phoneReq);
    }

    /**
     * 检查邮箱是否被注册使用
     * @param headers Http头部信息
     * @param emailReq 邮箱
     * @return 检查结果
     */
    @Override
    public void checkEmailRegistered(HttpHeaders headers, EmailReq emailReq) {
        memberCacheService.checkWebRequestHeader(headers);
        baseRegisterService.checkEmailRegistered(emailReq);
    }

    /**
     * 发送注册时短信验证码
     * @param headers Http头部信息
     * @param phoneVO 接口参数
     * @return 发送结果
     */
    @Override
    public void sendRegisterSmsCode(HttpHeaders headers, SmsPhoneReq phoneVO) {
        memberCacheService.checkWebRequestHeader(headers);
        baseRegisterService.sendRegisterSmsCode(phoneVO);
    }

    /**
     * 发送手机号找回密码时的短信验证码
     * @param headers Http头部信息
     * @param phoneVO 手机号码
     * @return 发送结果
     */
    @Override
    public void sendResetPasswordSmsCode(HttpHeaders headers, SmsPhoneReq phoneVO) {
        memberCacheService.checkWebRequestHeader(headers);
        baseRegisterService.sendResetPasswordSmsCode(phoneVO);
    }

    /**
     * 校验手机号找回密码时的短信验证码是否正确
     * @param headers Http头部信息
     * @param phoneSmsReq 手机号码、验证码
     * @return 发送结果
     */
    @Override
    public List<MultiAccInfoResp> checkResetPasswordSmsCode(HttpHeaders headers, PhoneSmsReq phoneSmsReq) {
        memberCacheService.checkWebRequestHeader(headers);
        return baseRegisterService.checkResetPasswordSmsCode(phoneSmsReq);
    }

    /**
     * 根据短信验证码重设密码
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 操作结果
     */
    @Override
    public void resetPasswordBySmsCode(HttpHeaders headers, ResetPasswordBySmsCodeReq codeVO) {
        memberCacheService.checkWebRequestHeader(headers);
        baseRegisterService.resetPasswordBySmsCode(codeVO);
    }

    /**
     * 发送邮箱找回密码时的邮件
     * @param headers Http头部信息
     * @param emailReq 邮箱地址
     * @return 发送结果
     */
    @Override
    public void sendResetPasswordEmail(HttpHeaders headers, EmailReq emailReq) {
        memberCacheService.checkWebRequestHeader(headers);
        baseRegisterService.sendResetPasswordEmail(emailReq, false);
    }

    /**
     * 校验邮箱找回密码时的邮件验证码是否正确
     * @param headers Http头部信息
     * @param emailReq 邮箱地址
     * @return 发送结果
     */
    @Override
    public List<MultiAccInfoResp> checkResetPasswordEmailCode(HttpHeaders headers, EmailSmsReq emailReq) {
        memberCacheService.checkWebRequestHeader(headers);
        return baseRegisterService.checkResetPasswordEmailCode(emailReq);
    }

    /**
     * 根据邮箱验证码重设密码
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 操作结果
     */
    @Override
    public void resetPasswordByEmailCode(HttpHeaders headers, ResetPasswordByEmailCodeReq codeVO) {
        memberCacheService.checkWebRequestHeader(headers);
        baseRegisterService.resetPasswordByEmailCode(codeVO);
    }

    /**
     * 获取会员注册页面-会员类型、商业类型页面内容（第二页）
     * @param headers Http头部信息
     * @return 操作结果
     */
    @Override
    public List<MemberRegisterTypeMenuResp> getRegisterTypePageContent(HttpHeaders headers) {
        memberCacheService.checkWebRequestHeader(headers);
        return baseRegisterService.getRegisterTypePageContent();
    }

    /**
     * 获取会员注册页面-详细信息页面内容（第三页）
     * @param headers Http头部信息
     * @param detailVO 接口参数
     * @return 注册第三页的内容
     */
    @Override
    public List<MemberConfigGroupResp> getRegisterDetailPageContent(HttpHeaders headers, MemberDetailReq detailVO) {
        memberCacheService.checkWebRequestHeader(headers);
        return baseRegisterService.getRegisterDetailPageContent(detailVO);
    }

    /**
     * Web站点页面注册
     * @param headers Http头部信息
     * @param registerVO 接口参数
     * @return 注册结果
     */
    @Override
    public MemberRegisterResultResp registerPlatformMember(HttpHeaders headers, MemberRegisterReq registerVO) {
        memberCacheService.checkWebRequestHeader(headers);
        return baseRegisterService.registerPlatformMember(MemberRegisterSourceEnum.FROM_ENTERPRISE_WEB_SHOP, registerVO.getSmsCode(), registerVO.getTelCode(), registerVO.getPhone(), registerVO.getPassword(), registerVO.getEmail(), registerVO.getMemberType(), registerVO.getMemberRoleId(), registerVO.getDetail(), registerVO.getInvitationCode(), null);
    }

    @Override
    public void checkInvitationCodeExists(HttpHeaders headers, InvitationCodeReq invitationCodeReq) {
        memberCacheService.checkWebRequestHeader(headers);
        baseRegisterService.checkInvitationCodeExists(invitationCodeReq);
    }
}