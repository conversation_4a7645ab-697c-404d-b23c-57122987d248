package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.engine.RuleEngineProcessReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.RuleEngineProcessResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.platform.MemberCycleProcessPageQueryDataReq;
import com.ssy.lingxi.member.model.resp.platform.MemberCycleProcessPageResp;
import com.ssy.lingxi.member.service.web.IMemberCycleProcessService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员能力-会员生命周期变更流程规则引擎
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/lifeCycle/process/engine")
public class MemberCycleProcessEngineController extends BaseController {

    @Resource
    private IMemberCycleProcessService service;

    /**
     * 分页查询流程规则
     * @param pageVO 分页查询条件
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberCycleProcessPageResp>> processPage(@Valid MemberCycleProcessPageQueryDataReq pageVO) {
        return WrapperUtil.success(service.pageProcesses(getSysUser(), pageVO, EnableDisableStatusEnum.ENABLE.getCode()));
    }

    /**
     * 查询流程规则引擎详情
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<RuleEngineProcessResp> getMemberCycleProcesses(@Valid RuleEngineProcessReq request) {
        return WrapperUtil.success(service.getMemberCycleProcesses(getSysUser(), request));
    }

}
