package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.MemberValidateAgreeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 会员审批状态枚举校验注解
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-29
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {MemberValidateAgreeValidator.class})
public @interface MemberValidateAgreeAnnotation {
    boolean required() default true;

    String message() default "agree参数值不在枚举定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
