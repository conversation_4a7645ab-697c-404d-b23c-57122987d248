package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.manage.MessageNoticeEnum;
import com.ssy.lingxi.member.api.model.req.MemberOrderCommentReq;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderCommentDO;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderProductCommentDO;
import com.ssy.lingxi.member.repository.comment.MemberOrderCommentRepository;
import com.ssy.lingxi.member.repository.comment.MemberOrderProductCommentRepository;
import com.ssy.lingxi.member.service.feign.IMemberOrderCommentFeignService;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.web.comment.IMemberOrderCommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员订单评价feign服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/3
 */
@Slf4j
@Service
public class MemberOrderCommentFeignServiceImpl implements IMemberOrderCommentFeignService {
    @Resource
    private MemberOrderCommentRepository memberOrderCommentRepository;

    @Resource
    private MemberOrderProductCommentRepository memberOrderProductCommentRepository;

    @Resource
    private IMessageFeignService messageFeignService;

    @Resource
    private IMemberOrderCommentService memberOrderCommentService;

    /**
     * 保存订单数据
     * @param memberOrderCommentReq 接口参数
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveMemberOrderComment(MemberOrderCommentReq memberOrderCommentReq) {
        log.info("会员订单评价，收到订单数据：{}", JsonUtil.toJson(memberOrderCommentReq));

        // 订单
        MemberOrderCommentDO memberOrderCommentDO = new MemberOrderCommentDO();
        memberOrderCommentDO.setId(memberOrderCommentReq.getId());
        memberOrderCommentDO.setCreateTime(memberOrderCommentReq.getCreateTime());
        memberOrderCommentDO.setBuyerMemberId(memberOrderCommentReq.getBuyerMemberId());
        memberOrderCommentDO.setBuyerRoleId(memberOrderCommentReq.getBuyerRoleId());
        memberOrderCommentDO.setBuyerMemberName(memberOrderCommentReq.getBuyerMemberName());
        memberOrderCommentDO.setVendorMemberId(memberOrderCommentReq.getVendorMemberId());
        memberOrderCommentDO.setVendorRoleId(memberOrderCommentReq.getVendorRoleId());
        memberOrderCommentDO.setVendorMemberName(memberOrderCommentReq.getVendorMemberName());
        memberOrderCommentDO.setOrderNo(memberOrderCommentReq.getOrderNo());
        memberOrderCommentDO.setShopId(memberOrderCommentReq.getShopId());
        memberOrderCommentDO.setShopType(memberOrderCommentReq.getShopType());
        memberOrderCommentDO.setShopEnvironment(memberOrderCommentReq.getShopEnvironment());
        memberOrderCommentDO.setShopName(memberOrderCommentReq.getShopName());
        memberOrderCommentDO.setOrderMode(memberOrderCommentReq.getOrderMode());
        memberOrderCommentDO.setOrderType(memberOrderCommentReq.getOrderType());
        memberOrderCommentDO.setOrderKind(memberOrderCommentReq.getOrderKind());
        memberOrderCommentDO.setPayType(memberOrderCommentReq.getPayType());
        memberOrderCommentDO.setPayChannel(memberOrderCommentReq.getPayChannel());
        memberOrderCommentDO.setQuoteNo(memberOrderCommentReq.getQuoteNo());
        memberOrderCommentDO.setDigest(memberOrderCommentReq.getDigest());
        memberOrderCommentDO.setProductAmount(memberOrderCommentReq.getProductAmount());
        memberOrderCommentDO.setFreight(memberOrderCommentReq.getFreight());
        memberOrderCommentDO.setPromotionAmount(memberOrderCommentReq.getPromotionAmount());
        memberOrderCommentDO.setCouponAmount(memberOrderCommentReq.getCouponAmount());
        memberOrderCommentDO.setTotalAmount(memberOrderCommentReq.getTotalAmount());
        memberOrderCommentDO.setBuyerInnerStatus(memberOrderCommentReq.getBuyerInnerStatus());
        memberOrderCommentDO.setVendorInnerStatus(memberOrderCommentReq.getVendorInnerStatus());
        memberOrderCommentDO.setOuterStatus(memberOrderCommentReq.getOuterStatus());
        memberOrderCommentDO.setBuyerCompleteCommentStatus(EnableDisableStatusEnum.DISABLE.getCode());
        memberOrderCommentDO.setVendorCompleteCommentStatus(EnableDisableStatusEnum.DISABLE.getCode());
        memberOrderCommentDO.setCompleteTime(LocalDateTime.now());
        memberOrderCommentRepository.saveAndFlush(memberOrderCommentDO);

        // 订单商品
        List<MemberOrderProductCommentDO> memberOrderProductCommentDOList = memberOrderCommentReq.getProducts().stream().map(product -> {
            MemberOrderProductCommentDO memberOrderProductCommentDO = new MemberOrderProductCommentDO();
            memberOrderProductCommentDO.setId(product.getId());
            memberOrderProductCommentDO.setOrder(memberOrderCommentDO);
            memberOrderProductCommentDO.setProductId(product.getProductId());
            memberOrderProductCommentDO.setSkuId(product.getSkuId());
            memberOrderProductCommentDO.setProductNo(product.getProductNo());
            memberOrderProductCommentDO.setName(product.getName());
            memberOrderProductCommentDO.setCategory(product.getCategory());
            memberOrderProductCommentDO.setBrand(product.getBrand());
            memberOrderProductCommentDO.setSpec(product.getSpec());
            memberOrderProductCommentDO.setUnit(product.getUnit());
            memberOrderProductCommentDO.setLogo(product.getLogo());
            memberOrderProductCommentDO.setPrice(product.getPrice());
            memberOrderProductCommentDO.setQuantity(product.getQuantity());
            memberOrderProductCommentDO.setAmount(product.getAmount());
            memberOrderProductCommentDO.setDeliverType(product.getDeliverType());
            memberOrderProductCommentDO.setAddress(product.getAddress());
            memberOrderProductCommentDO.setReceiver(product.getReceiver());
            memberOrderProductCommentDO.setPhone(product.getPhone());
            memberOrderProductCommentDO.setBuyerCommentStatus(EnableDisableStatusEnum.DISABLE.getCode());
            memberOrderProductCommentDO.setVendorCommentStatus(EnableDisableStatusEnum.DISABLE.getCode());
            return memberOrderProductCommentDO;
        }).collect(Collectors.toList());
        memberOrderProductCommentRepository.saveAll(memberOrderProductCommentDOList);

        // 异步采购消息
        messageFeignService.sendSystemMessage(memberOrderCommentDO.getBuyerMemberId(), memberOrderCommentDO.getBuyerRoleId(),
                MessageNoticeEnum.PROCUREMENT_ORDER_COMMENT.getCode(), Arrays.asList(memberOrderCommentDO.getOrderNo(), memberOrderCommentDO.getDigest()));

        // 异步供应消息
        messageFeignService.sendSystemMessage(memberOrderCommentDO.getVendorMemberId(), memberOrderCommentDO.getVendorRoleId(),
                MessageNoticeEnum.SELL_ORDER_COMMENT.getCode(), Arrays.asList(memberOrderCommentDO.getOrderNo(), memberOrderCommentDO.getDigest()));
    }

    @Override
    public Integer findWailtCommentByOrderIdList(List<Long> orderIdList, Integer type, UserLoginCacheDTO loginUser) {
        return memberOrderCommentService.findWaitCommentOrder(orderIdList, type, loginUser);
    }
}
