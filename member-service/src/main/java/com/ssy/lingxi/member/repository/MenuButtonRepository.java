package com.ssy.lingxi.member.repository;


import com.ssy.lingxi.member.entity.do_.menuAuth.ButtonDO;
import com.ssy.lingxi.member.entity.do_.menuAuth.MenuDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 业务平台 - 菜单页面按钮数据库操作JpaRepository
 * <AUTHOR>
 * @since 2020-06-15
 * @version 2.0.0
 */
@Repository
public interface MenuButtonRepository extends JpaRepository<ButtonDO, Long>, JpaSpecificationExecutor<ButtonDO> {
    List<ButtonDO> findAllByMenu(MenuDO menuDO);

    boolean existsByPathAndMenuId(String path, Long menuId);

    boolean existsByPathAndMenuIdAndIdNot(String path, Long menuId, Long buttonId);
}
