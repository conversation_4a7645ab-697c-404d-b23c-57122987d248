package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.detail.MemberClassificationDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员入库分类信息Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-25
 */
@Repository
public interface MemberClassificationRepository extends JpaRepository<MemberClassificationDO, Long>, JpaSpecificationExecutor<MemberClassificationDO> {

    List<MemberClassificationDO> findByMemberIdAndRoleId(Long memberId, Long roleId);
}
