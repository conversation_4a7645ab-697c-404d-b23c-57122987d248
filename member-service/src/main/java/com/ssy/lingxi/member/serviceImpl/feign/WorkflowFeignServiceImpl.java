package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskResultBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.model.resp.validate.WorkFlowStepResp;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.workflow.api.feign.IProcessFeign;
import com.ssy.lingxi.workflow.api.feign.IProcessMemberRoleFeign;
import com.ssy.lingxi.workflow.api.model.req.*;
import com.ssy.lingxi.workflow.api.model.resp.ComplexTaskCompleteResp;
import com.ssy.lingxi.workflow.api.model.resp.SimpleProcessDefResp;
import com.ssy.lingxi.workflow.api.model.resp.SimpleTaskCompleteResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调用工作流服务Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-14
 */
@Service
public class WorkflowFeignServiceImpl implements IWorkflowFeignService {
    private static final Logger logger = LoggerFactory.getLogger(WorkflowFeignServiceImpl.class);

    @Resource
    private IProcessFeign processFeign;

    @Resource
    private IProcessMemberRoleFeign processMemberRoleControllerFeign;

    /**
     * 启动会员流程
     *
     * @param relationDO 会员关系
     * @return 启动结果
     */
    @Override
    public WorkflowTaskResultBO startMemberProcess(MemberRelationDO relationDO) {
        return startMemberProcess(relationDO.getValidateTask().getProcessKey(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId());

    }

    /**
     * 启动会员入库、资料变更流程
     *
     * @param processKey 流程的Key
     * @param memberId   执行流程的会员Id
     * @param roleId     执行流程的会员角色Id
     * @param dataId     审核数据Id
     * @return 启动结果
     */
    @Override
    public WorkflowTaskResultBO startMemberProcess(String processKey, Long memberId, Long roleId, Long dataId) {
        TaskStartReq startVO = new TaskStartReq();
        startVO.setProcessKey(processKey);
        startVO.setMemberId(memberId);
        startVO.setRoleId(roleId);
        startVO.setDataId(dataId);
        logger.info("===开启工作流：processKey：{}，memberId：{}，roleId：{}，dataId：{}", processKey, memberId, roleId, dataId);
        WrapperResp<SimpleTaskCompleteResp> result = processFeign.startSimpleProcess(startVO);
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(result.getCode(), result.getMessage());
        }

        return new WorkflowTaskResultBO(result.getData().getTaskId(), result.getData().getStatus(), result.getData().getProperties().getOrDefault("oper", ""));
    }

    /**
     * 执行会员流程
     *
     * @param relationDO 会员关系
     * @param agree      审核意见
     * @return 执行结果
     */
    @Override
    public WorkflowTaskResultBO execMemberProcess(MemberRelationDO relationDO, Integer agree) {
        return execMemberProcess(relationDO.getValidateTask().getProcessKey(), relationDO.getValidateTask().getTaskId(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId(), agree);
    }

    /**
     * 执行会员入库、资料变更流程
     *
     * @param processKey 流程的Key
     * @param taskId     流程任务Id
     * @param memberId   执行流程的会员Id
     * @param roleId     执行流程的会员角色Id
     * @param dataId     审核数据Id
     * @param agree      审核结果
     * @return 启动结果
     */
    @Override
    public WorkflowTaskResultBO execMemberProcess(String processKey, String taskId, Long memberId, Long roleId, Long dataId, Integer agree) {
        TaskExecuteReq executeVO = new TaskExecuteReq();
        executeVO.setProcessKey(processKey);
        executeVO.setMemberId(memberId);
        executeVO.setRoleId(roleId);
        executeVO.setDataId(dataId);
        executeVO.setTaskId(taskId);
        executeVO.setAgree(agree);
        logger.info("===工作流执行：processKey：{}，memberId：{}，roleId：{}，dataId：{}， taskId：{}", processKey, memberId, roleId, dataId, taskId);
        WrapperResp<SimpleTaskCompleteResp> result = processFeign.completeSimpleTask(executeVO);
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(result.getCode(), result.getMessage());
        }

        return new WorkflowTaskResultBO(result.getData().getTaskId(), result.getData().getStatus(), result.getData().getProperties().getOrDefault("oper", ""));
    }

    /**
     * 连续执行会员流程
     * @param processKey 流程的Key
     * @param taskId     流程任务Id
     * @param memberId   执行流程的会员Id
     * @param roleId     执行流程的会员角色Id
     * @param dataId     审核数据Id
     * @param execTimes  执行次数
     * @param agrees     审核结果数组
     * @return 启动结果
     */
    @Override
    public WorkflowTaskResultBO execMemberSerialProcess(String processKey, String taskId, Long memberId, Long roleId, Long dataId, Integer execTimes, List<Integer> agrees) {
        SerialTaskExecuteReq executeVO = new SerialTaskExecuteReq();
        executeVO.setProcessKey(processKey);
        executeVO.setMemberId(memberId);
        executeVO.setRoleId(roleId);
        executeVO.setDataId(dataId);
        executeVO.setTaskId(taskId);
        executeVO.setExecTimes(execTimes);
        executeVO.setAgrees(agrees);
        WrapperResp<ComplexTaskCompleteResp> result = processFeign.completeSerialTasks(executeVO);
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(result.getCode(), result.getMessage());
        }

        return new WorkflowTaskResultBO(result.getData().getTaskId(), result.getData().getInnerStatus(), result.getData().getInnerProperties().getOrDefault("oper", ""));
    }

    /**
     * 查询会员流程步骤
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public WorkflowTaskListBO listMemberProcessSteps(MemberRelationDO relationDO) {
        return listMemberProcessSteps(relationDO.getMemberId(), relationDO.getValidateTask().getProcessKey(), relationDO.getValidateTask().getTaskId());
    }

    /**
     * 查询会员入库审核步骤
     * @param memberId        执行流程的会员Id
     * @param processKey      流程的ProcessKey
     * @param taskId          将要执行的工作流任务Id
     * @return 查询结果
     */
    @Override
    public WorkflowTaskListBO listMemberProcessSteps(Long memberId, String processKey, String taskId) {
        //如果是“无需审核”流程（空流程），直接返回
        if(processKey.equals(MemberConstant.EMPTY_PLATFORM_VALIDATE_PROCESS_KEY) || processKey.equals(MemberConstant.EMPTY_DEPOSITORY_PROCESS_KEY)) {
            return new WorkflowTaskListBO();
        }

        InternalProcessQueryReq queryVO = new InternalProcessQueryReq();
        queryVO.setProcessKey(processKey);
        queryVO.setMemberId(memberId);
        queryVO.setTaskId(taskId);

        WrapperResp<SimpleProcessDefResp> result = processFeign.findSimpleInternalTaskDefinitions(queryVO);
        if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(result.getCode(), result.getMessage());
        }

        return new WorkflowTaskListBO(result.getData().getCurrentStep(), result.getData().getTasks().stream().map(task -> {
            WorkFlowStepResp stepVO = new WorkFlowStepResp();
            stepVO.setStep(task.getTaskStep());
            stepVO.setStepName(task.getTaskName());
            stepVO.setRoleName(task.getRoleName());
            return stepVO;
        }).sorted(Comparator.comparingInt(WorkFlowStepResp::getStep)).collect(Collectors.toList()));
    }

    /**
     * 查询外部流程步骤
     * @param roleId 上级角色id
     * @param subRoleId 下级角色id
     * @param processKey 流程的ProcessKey
     * @param taskId 将要执行的工作流任务id
     * @return 查询结果
     */
    @Override
    public WrapperResp<WorkflowTaskListBO> listExternalProcessSteps(Long roleId, Long subRoleId, String processKey, String taskId) {
        ExternalProcessQueryReq queryVO = new ExternalProcessQueryReq();
        queryVO.setProcessKey(processKey);
        queryVO.setRoleId(roleId);
        queryVO.setSubRoleId(subRoleId);
        queryVO.setTaskId(taskId);

        WrapperResp<SimpleProcessDefResp> result;
        try {
            result = processFeign.findSimpleExternalTaskDefinitions(queryVO);
            if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return WrapperUtil.fail(result.getCode(), result.getMessage());
            }

            return WrapperUtil.success(new WorkflowTaskListBO(result.getData().getCurrentStep(), result.getData().getTasks().stream().map(task -> {
                WorkFlowStepResp stepVO = new WorkFlowStepResp();
                stepVO.setStep(task.getTaskStep());
                stepVO.setStepName(task.getTaskName());
                stepVO.setRoleName(task.getRoleName());
                return stepVO;
            }).sorted(Comparator.comparingInt(WorkFlowStepResp::getStep)).collect(Collectors.toList())));
        } catch (Exception e) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_WORKFLOW_ERROR);
        }
    }

//    /**
//     * 新增或修改会员角色时，关联工作流步骤
//     * @param memberId       会员Id
//     * @param memberRoleId   会员自定义角色Id
//     * @param memberRoleName 会员自定义角色名称
//     * @param authBOList     会员自定义角色权限列表
//     */
//    @Async
//    @Override
//    public void upsertMemberRoleToProcessAsync(Long memberId, Long memberRoleId, String memberRoleName, List<AuthBO> authBOList) {
//        try {
//            if (CollectionUtils.isEmpty(authBOList)) {
//                Wrapper.success();
//                return;
//            }
//
//            List<AuthPathVO> accessPaths = authBOList.stream().map(authBO -> new AuthPathVO(authBO.getPath(), authBO.getSource())).collect(Collectors.toList());
//
//            ProcessMemberRoleVO memberRoleVO = new ProcessMemberRoleVO();
//            memberRoleVO.setMemberId(memberId);
//            memberRoleVO.setMemberRoleId(memberRoleId);
//            memberRoleVO.setMemberRoleName(memberRoleName);
//            memberRoleVO.setPaths(accessPaths);
//            Wrapper<Void> result = processMemberRoleControllerFeign.updateInsertProcessAsync(memberRoleVO);
//            if(result.getCode() != ResponseCode.SUCCESS.getCode()) {
//                logger.error("通知工作流服务关联会员自定义角色错误：" + result.getMessage());
//            }
//        } catch (Exception e) {
//            logger.error("通知工作流服务关联会员自定义角色错误", e);
//        }
//    }


    /**
     * 删除会员角色时，从关联的工作流步骤中移除会员角色
     * @param memberId     会员Id
     * @param memberRoleId 会员自定义角色Id
     */
    @Async
    @Override
    public void removeMemberRoleInProcessAsync(Long memberId, Long memberRoleId) {
        try {
            ProcessRemoveMemberRoleReq removeMemberRoleVO = new ProcessRemoveMemberRoleReq();
            removeMemberRoleVO.setMemberId(memberId);
            removeMemberRoleVO.setMemberRoleId(memberRoleId);
            processMemberRoleControllerFeign.removeMemberRoleAsync(removeMemberRoleVO);
        } catch (Exception e) {
            logger.error("通知工作流服务移除会员自定义角色错误", e);
        }
    }
}
