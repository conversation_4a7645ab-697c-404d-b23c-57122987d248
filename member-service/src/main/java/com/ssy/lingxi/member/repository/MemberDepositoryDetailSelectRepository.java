package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailSelectDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员入库资料Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-20
 */
@Repository
public interface MemberDepositoryDetailSelectRepository extends JpaRepository<MemberDepositoryDetailSelectDO, Long>, JpaSpecificationExecutor<MemberDepositoryDetailSelectDO> {

    @Transactional
    void deleteByRelation(MemberRelationDO relationDO);

}
