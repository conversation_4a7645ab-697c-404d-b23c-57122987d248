package com.ssy.lingxi.member.model.req.validate;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 会员流程规则配置 - 修改流程规则接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-17
 */
public class MemberProcessUpdateReq extends MemberProcessAddReq implements Serializable {
    private static final long serialVersionUID = 1506126899587634875L;

    /**
     * 流程规则Id
     */
    @NotNull(message = "流程规则Id要大于0")
    @Positive(message = "流程规则Id要大于0")
    private Long id;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
