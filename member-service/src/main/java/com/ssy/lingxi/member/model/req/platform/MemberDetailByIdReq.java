package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 根据会员id查询会员列表VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-3-10
 */
@Data
public class MemberDetailByIdReq implements Serializable {
    private static final long serialVersionUID = 1179105779226765425L;

    /**
     * 会员id
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;
}
