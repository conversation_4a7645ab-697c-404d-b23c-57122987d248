package com.ssy.lingxi.member.model.req.lifecycle;

import com.ssy.lingxi.member.handler.annotation.DateStringFormatAnnotation;
import com.ssy.lingxi.member.model.req.basic.FileUploadReq;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.util.List;

/**
 * 会员整改VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Data
public class MemberRectifyAddReq implements Serializable {
    private static final long serialVersionUID = 7278091101075789501L;

    /**
     * 整改主题
     */
    @NotBlank(message = "整改主题不能为空")
    private String subject;

    /**
     * 下级会员id
     */
    @NotNull(message = "下级会员Id要大于0")
    @Positive(message = "下级会员Id要大于0")
    private Long subMemberId;

    /**
     * 下级角色id
     */
    @NotNull(message = "下级会员角色Id要大于0")
    @Positive(message = "下级会员角色Id要大于0")
    private Long subRoleId;

    /**
     * 整改期限开始, 格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation(message = "整改期限开始格式错误")
    private String rectifyDayStart;

    /**
     * 整改期限结束, 格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation(message = "整改期限结束格式错误")
    private String rectifyDayEnd;

    /**
     * 整改原因
     */
    @NotBlank(message = "整改原因不能为空")
    private String reason;

    /**
     * 整改要求
     */
    @NotBlank(message = "整改要求不能为空")
    private String require;

    /**
     * 整改要求附件
     */
    @Valid
    private List<FileUploadReq> attachments;
}
