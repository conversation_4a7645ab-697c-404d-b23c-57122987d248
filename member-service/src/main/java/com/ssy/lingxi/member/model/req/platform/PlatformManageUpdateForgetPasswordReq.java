package com.ssy.lingxi.member.model.req.platform;

import com.ssy.lingxi.component.base.annotation.AesDecryptAnnotation;
import com.ssy.lingxi.member.handler.annotation.PwdSteEstimatorAnnotation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 平台后台 - 登陆页忘记密码 - 更新忘记密码req
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlatformManageUpdateForgetPasswordReq implements Serializable {
    private static final long serialVersionUID = -7125097337182818205L;

    /**
     * 手机号码
     */
    @AesDecryptAnnotation
    private String phone;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    @AesDecryptAnnotation
    private String email;

    /**
     * 手机验证码（需要加密）
     */
    @AesDecryptAnnotation
    @NotBlank(message = "手机验证码不能为空")
    private String phoneSmsCode;

    /**
     * 邮箱验证码（需要加密）
     */
    @AesDecryptAnnotation
    @NotBlank(message = "邮箱验证码不能为空")
    private String emailSmsCode;

    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    @AesDecryptAnnotation
    @PwdSteEstimatorAnnotation
    private String newPassword;
}
