package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.basic.OrganizationPageDataReq;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRegisterTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberRightScoreResp;
import com.ssy.lingxi.member.model.resp.basic.UserDetailResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrganizationQueryResp;
import org.springframework.http.HttpHeaders;

/**
 * 会员业务服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
public interface IMemberCommonBusinessService {

    /**
     * 查询会员平台权益积分、上级会员下的权益积分
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberRightScoreResp getMemberRightPoint(HttpHeaders headers, MemberAndRoleIdReq idVO);

    /**
     * 查询会员标签注册资料
     * @param headers Http头部信息
     * @return 查询结果
     */
    MemberRegisterTagResp getMemberRegisterTagDetail(HttpHeaders headers);

    /**
     * 查询用户注册资料
     * @param headers Http头部信息
     * @return 查询结果
     */
    UserDetailResp getUserDetail(HttpHeaders headers);

    /**
     * 分页查询会员组织机构
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberOrganizationQueryResp> pageMemberOrganizations(HttpHeaders headers, OrganizationPageDataReq pageVO);
}
