package com.ssy.lingxi.member.serviceImpl.web;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.common.util.CollectionPageUtil;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.member.api.enums.MemberRightTypeEnum;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleStagesRuleCheckFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberManageQueryResp;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.QMemberRoleDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelConfigDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRightDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberRightConfigDO;
import com.ssy.lingxi.member.entity.do_.levelRight.QMemberLevelConfigDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifeCycleRuleConfigDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifecycleStagesDO;
import com.ssy.lingxi.member.enums.MemberConfigFieldTypeEnum;
import com.ssy.lingxi.member.enums.MemberManageOrderTypeEnum;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.model.req.basic.MemberNameDataReq;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.basic.UserPageDataReq;
import com.ssy.lingxi.member.model.req.manage.*;
import com.ssy.lingxi.member.model.resp.basic.*;
import com.ssy.lingxi.member.model.resp.manage.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.IManageFeignService;
import com.ssy.lingxi.member.service.feign.IMemberLifeCycleStageRuleFeignService;
import com.ssy.lingxi.member.service.web.IMemberManageService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 用户管理接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-03
 */
@Service
public class MemberManageServiceImpl implements IMemberManageService {
    @Resource
    private MemberRoleRepository roleRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberLevelConfigRepository levelConfigRepository;

    @Resource
    private IBaseUserDetailService baseUserDetailService;

    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    @Resource
    private IManageFeignService platformManageFeignService;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private IBaseSiteService siteService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private MemberProcessRuleRepository memberProcessRuleRepository;

    @Resource
    private MemberLifecycleStagesRepository memberLifecycleStagesRepository;

    @Resource
    private IMemberLifeCycleStageRuleFeignService memberLifeCycleStageRuleFeignService;

    /**
     * 商城类型 - 企业商城 - 1
     */
    private final Integer MALL_TYPE_ENTERPRISE = 1;

    /**
     * 商城类型 - 积分商城 - 2
     */
    private final Integer MALL_TYPE_SCORE = 2;

    /**
     * 查询所有角色
     * @param headers    Http头部信息
     * @param roleTypeVO 接口参数
     * @return 角色列表
     */
    @Override
    public List<RoleIdAndNameResp> allRoles(HttpHeaders headers, MemberManageRoleTypeReq roleTypeVO) {
        List<MemberRoleDO> roleDOList = roleRepository.findAll().stream().filter(memberRoleDO -> !memberRoleDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).collect(Collectors.toList());
        if (roleTypeVO.getRoleTypeEnum() != null && !roleTypeVO.getRoleTypeEnum().equals(0)) {
            roleDOList = roleDOList.stream().filter(memberRoleDO -> memberRoleDO.getRoleType().equals(roleTypeVO.getRoleTypeEnum())).collect(Collectors.toList());
        }
        return roleDOList.stream().map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).sorted(Comparator.comparingLong(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList());
    }

    /**
     * 模糊分页查询下级会员列表
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageLowerMembersByName(HttpHeaders headers, MemberManagePageByNameDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> result = relationRepository.findAll(specification, page);
        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据下级会员名称、下级会员角色Id，模糊分页查询下级会员列表
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageLowerMembersByNameAndRole(HttpHeaders headers, MemberManagePageByNameAndRoleIdDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (pageVO.getRoleId() != null && pageVO.getRoleId() > 0) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), pageVO.getRoleId()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> result = relationRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务提供者的下级会员列表
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageLowerProviderMembersByName(HttpHeaders headers, MemberManageNameWithExcludePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.in(root.get("status")).value(Stream.of(MemberStatusEnum.NORMAL.getCode(),MemberStatusEnum.CORRECTING.getCode()).collect(Collectors.toList())));
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            //会员名称
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            //会员角色
            if(NumberUtil.notNullOrZero(pageVO.getRoleId())) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), pageVO.getRoleId()));
            }

            if(!CollectionUtils.isEmpty(pageVO.getMemberConfigs()) && NumberUtil.notNullOrZero(pageVO.getRoleId())){
                MemberRoleDO memberRoleDO = roleRepository.findById(pageVO.getRoleId()).orElse(null);
                List<MemberRegisterConfigDO> memberRegisterConfigDOList = memberProcessRuleRepository
                        .findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(loginUser.getMemberId(), loginUser.getMemberRoleId(),
                                memberRoleDO, EnableDisableStatusEnum.ENABLE.getCode(), Sort.by("id").ascending()).getConfigs().stream().filter(memberConfigDO -> CommonBooleanEnum.YES.getCode().equals(memberConfigDO.getAllowSelect())).collect(Collectors.toList());
                Join<Object,Object> depositoryDetailSelectJoin = root.join("depositoryDetailSelect", JoinType.LEFT);
                Join<Object, Object> depositDetailsJoin = root.join("depositDetails", JoinType.LEFT);
                List<Predicate> memberConfigList = new ArrayList<>();

                for (Map.Entry<String, Object> entry : pageVO.getMemberConfigs().entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue() instanceof HashMap ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());
                    if (StringUtils.hasLength(value)) {
                        MemberRegisterConfigDO configDO = memberRegisterConfigDOList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
                        if(!Objects.isNull(configDO)){
                            if(MemberConfigFieldTypeEnum.AREA.getMessage().equals(configDO.getFieldType())){
                                RegisterAreaResp area = SerializeUtil.deserialize(value, RegisterAreaResp.class);
                                if(!Objects.isNull(area)){
                                    String cityCode = area.getCityCode();
                                    String districtCode = area.getDistrictCode();
                                    String provinceCode = area.getProvinceCode();
                                    if(StringUtils.hasLength(provinceCode) && StringUtils.hasLength(cityCode) && StringUtils.hasLength(districtCode)){
                                        memberConfigList.add(criteriaBuilder.and(criteriaBuilder.equal(depositDetailsJoin.get("fieldName").as(String.class),key),criteriaBuilder.equal(depositDetailsJoin.get("provinceCode").as(String.class),provinceCode),criteriaBuilder.equal(depositDetailsJoin.get("cityCode").as(String.class),cityCode),criteriaBuilder.equal(depositDetailsJoin.get("districtCode").as(String.class),districtCode)));
                                    }
                                    else if(StringUtils.hasLength(provinceCode) && StringUtils.hasLength(cityCode)){
                                        memberConfigList.add(criteriaBuilder.and(criteriaBuilder.equal(depositDetailsJoin.get("fieldName").as(String.class),key),criteriaBuilder.equal(depositDetailsJoin.get("provinceCode").as(String.class),provinceCode),criteriaBuilder.equal(depositDetailsJoin.get("cityCode").as(String.class),cityCode)));
                                    }
                                    else if(StringUtils.hasLength(provinceCode)){
                                        memberConfigList.add(criteriaBuilder.and(criteriaBuilder.equal(depositDetailsJoin.get("fieldName").as(String.class),key),criteriaBuilder.equal(depositDetailsJoin.get("provinceCode").as(String.class),provinceCode)));
                                    }
                                }
                            }else{
                                memberConfigList.add(criteriaBuilder.and(criteriaBuilder.like(depositoryDetailSelectJoin.get("detail").as(String.class),"%" + key + ":"+ value.trim() + "%")));
                            }
                        }
                    }
                }
                if(memberConfigList.size() > 0){
                    Predicate[] orP = new Predicate[memberConfigList.size()];
                    list.add(criteriaBuilder.and(memberConfigList.toArray(orP)));
                }
            }

            //币别
            if(NumberUtil.notNullOrZero(pageVO.getCurrencyType())) {
                list.add(criteriaBuilder.equal(root.get("classification").get("currencyType").as(Integer.class),pageVO.getCurrencyType()));
            }

            //会员编码
            if(StringUtils.hasLength(pageVO.getCode())) {
                list.add(criteriaBuilder.like(root.get("classification").get("code").as(String.class),"%" + pageVO.getCode().trim() + "%"));
            }

            //主营品类
            List<Long> categoryId = pageVO.getCategoryId();
            if(!CollectionUtils.isEmpty(categoryId)) {
                Join<Object, Object> classificationJoin = root.join("classification", JoinType.LEFT);
                Join<Object, Object> categoriesJoin = classificationJoin.join("categories", JoinType.LEFT);
                list.add(criteriaBuilder.like(categoriesJoin.get("categoryIdList").as(String.class), "%:"+ categoryId.get(categoryId.size()-1) + "}%"));
            }

            //会员状态
            if(NumberUtil.notNullOrZero(pageVO.getStatus())) {
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), pageVO.getStatus()));
            }

            //会员等级
            if(NumberUtil.notNullOrZero(pageVO.getLevel())) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), pageVO.getLevel()));
            }

            //会员类型
            if(NumberUtil.notNullOrZero(pageVO.getMemberType())) {
                Join<Object, Object> memberTypeJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(memberTypeJoin.get("memberType"), pageVO.getMemberType()));
            }

            // 如果当前会员的下属会员当前生命周期阶段在【系统能力--生命周期规则配置--供应商生命周期规则配置】中有配置时，此供应商才允许出现在选择会员弹窗中
            // 如果未设置生命周期，要求显示全部供应商
            if (NumberUtil.notNullOrZero(pageVO.getLifeCycleStageRuleId())) {
                List<MemberLifecycleStagesDO> memberLifecycleStagesDOList = memberLifecycleStagesRepository.findByMemberIdAndRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId());
                if(!CollectionUtils.isEmpty(memberLifecycleStagesDOList)) {
                    Join<Object, Object> memberLifecycleStageJoin = root.join("memberLifecycleStages", JoinType.LEFT);
                    Join<Object, Object> memberLifeCycleRuleConfigJoin = memberLifecycleStageJoin.join("memberLifeCycleRuleConfigDOSet", JoinType.LEFT);
                    list.add(criteriaBuilder.equal(memberLifeCycleRuleConfigJoin.get("lifeCycleRuleEnum").as(Integer.class), pageVO.getLifeCycleStageRuleId()));
                }
            }

            query.groupBy(root.get("subMember")
                    ,root.get("subMemberId")
                    ,root.get("id")
                    ,root.get("subRole")
                    ,root.get("subRoleId")
                    ,root.get("subRoleName")
                    ,root.get("createTime")
                    ,root.get("levelRight")
                    ,root.get("status")
                    );

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification, Sort.by("id").ascending());

        if (!CollectionUtils.isEmpty(pageVO.getExcludeList())) {
            relationDOList = relationDOList.stream().filter(r -> pageVO.getExcludeList().stream().noneMatch(memberExcludeVO -> memberExcludeVO.getMemberId().equals(r.getSubMemberId()) && memberExcludeVO.getRoleId().equals(r.getSubRoleId()))).collect(Collectors.toList());
        }

        int totalCount = relationDOList.size();
        List<MemberRelationDO> pageList = CollectionPageUtil.pageList(relationDOList, totalCount, pageVO.getCurrent(), pageVO.getPageSize());

        return new PageDataResp<>((long) totalCount, pageList.stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getSubMemberId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleId(p.getSubRoleId());
            queryVO.setRoleName(p.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            queryVO.setStatus(p.getStatus());
            queryVO.setStatusName(MemberStatusEnum.getCodeMessage(p.getStatus()));
            queryVO.setDepositTime(p.getDepositTime() == null ? "" : p.getDepositTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setLifeCycleStageName(p.getMemberLifecycleStages() == null ? "" : p.getMemberLifecycleStages().getLifecycleStagesName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务消费者的下级会员列表
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageLowerConsumerMembersByName(HttpHeaders headers, MemberManageNameWithExcludePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            //会员名称
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            // 如果当前会员的下属会员当前生命周期阶段在【系统能力--生命周期规则配置--供应商生命周期规则配置】中有配置时，此供应商才允许出现在选择会员弹窗中
            // 如果未设置生命周期，要求显示全部供应商
            if (NumberUtil.notNullOrZero(pageVO.getLifeCycleStageRuleId())) {
                Join<Object, Object> memberLifecycleStageJoin = root.join("memberLifecycleStages", JoinType.LEFT);
                Join<Object, Object> memberLifeCycleRuleConfigJoin = memberLifecycleStageJoin.join("memberLifeCycleRuleConfigDOSet", JoinType.LEFT);
                list.add(criteriaBuilder.equal(memberLifeCycleRuleConfigJoin.get("lifeCycleRuleEnum").as(Integer.class), pageVO.getLifeCycleStageRuleId()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification, Sort.by("id").ascending());

        if (!CollectionUtils.isEmpty(pageVO.getExcludeList())) {
            relationDOList = relationDOList.stream().filter(r -> pageVO.getExcludeList().stream().noneMatch(memberExcludeVO -> memberExcludeVO.getMemberId().equals(r.getSubMemberId()) && memberExcludeVO.getRoleId().equals(r.getSubRoleId()))).collect(Collectors.toList());
        }

        int totalCount = relationDOList.size();
        List<MemberRelationDO> pageList = CollectionPageUtil.pageList(relationDOList, totalCount, pageVO.getCurrent(), pageVO.getPageSize());

        return new PageDataResp<>((long) totalCount, pageList.stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getSubMemberId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleId(p.getSubRoleId());
            queryVO.setRoleName(p.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务消费者的下级渠道会员列表
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageLowerConsumerMembersByName(HttpHeaders headers, MemberManagePageByNameDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            if (Objects.nonNull(pageVO.getSubRoleId())) {
                list.add(criteriaBuilder.equal(subRoleJoin.get("id").as(Long.class), pageVO.getSubRoleId()));
            }

            //会员名称
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> memberRelationDOList = relationRepository.findAll(specification, Sort.by("id").ascending());

        if (!ObjectUtils.isEmpty(pageVO.getLifeCycleStageRuleId())) {
            memberRelationDOList = memberRelationDOList.stream().filter(memberRelationDO -> {
                //校验是否又设置生命周期规则，未设置生命周期不做任何限制
                MemberLifeCycleStagesRuleCheckFeignReq memberLifeCycleStagesRuleCheckFeignReq = new MemberLifeCycleStagesRuleCheckFeignReq();
                memberLifeCycleStagesRuleCheckFeignReq.setMemberId(loginUser.getMemberId());
                memberLifeCycleStagesRuleCheckFeignReq.setRoleId(loginUser.getMemberRoleId());
                memberLifeCycleStagesRuleCheckFeignReq.setSubMemberId(memberRelationDO.getSubMemberId());
                memberLifeCycleStagesRuleCheckFeignReq.setSubRoleId(memberRelationDO.getSubRoleId());
                memberLifeCycleStagesRuleCheckFeignReq.setLifeCycleStageRuleId(pageVO.getLifeCycleStageRuleId());
                memberLifeCycleStagesRuleCheckFeignReq.setSubRoleTag(RoleTagEnum.CUSTOMER.getCode());
                return memberLifeCycleStageRuleFeignService.checkMemberLifeCycleStagesRuleOrNotAstrict(memberLifeCycleStagesRuleCheckFeignReq);
            }).collect(Collectors.toList());
        }

        int totalCount = memberRelationDOList.size();
        List<MemberRelationDO> pageList = CollectionPageUtil.pageList(memberRelationDOList, totalCount, pageVO.getCurrent(), pageVO.getPageSize());

        return new PageDataResp<>((long)totalCount,pageList.stream().map(p ->{
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getSubMemberId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleId(p.getSubRoleId());
            queryVO.setRoleName(p.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            queryVO.setStatus(p.getStatus());
            queryVO.setStatusName(MemberStatusEnum.getCodeMessage(p.getStatus()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务提供者的下级“企业会员”列表
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageLowerMerchantProviderMembersByName(HttpHeaders headers, MemberManagePageByNameDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.or(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据会员名称、商城类型分页查询会员列表
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageMemberByNameAndMallType(HttpHeaders headers, MemberManagePageByNameAndMallTypeDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            if (pageVO.getMemberTypeEnum() != null && !pageVO.getMemberTypeEnum().equals(0)) {
                list.add(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), pageVO.getMemberTypeEnum()));
            } else {
                list.add(criteriaBuilder.or(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
            }

            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName() + "%"));
            }

            if (pageVO.getRoleId() != null && !pageVO.getRoleId().equals(0L)) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), pageVO.getRoleId()));
            }

            if (pageVO.getLevel() != null && !pageVO.getLevel().equals(0)) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), pageVO.getLevel()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()));
    }

//    /**
//     * 根据上级会员Id和上级会员角色Id，以及当前用户，查询价格权益参数设置
//     *
//     * @param headers HttpHeaders信息
//     * @param upperVO 接口参数
//     * @return 操作结果
//     */
//    @Override
//    public Wrapper<MemberManageMemberCreditParameterVO> getUpperMemberCreditParameter(HttpHeaders headers, MemberManageUpperMemberAndRoleVO upperVO) {
//        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
//
//        MemberManageMemberCreditParameterVO parameterVO = new MemberManageMemberCreditParameterVO();
//        parameterVO.setParameter(new BigDecimal(1));
//        //查询上下级关系
//        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperVO.getParentMemberId(), upperVO.getParentMemberRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
//        if (relationDO == null || !relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode()) || !relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
//            return Wrapper.success(parameterVO);
//        }
//
//        MemberLevelRightDO levelRight = relationDO.getLevelRight();
//        if (levelRight == null) {
//            return Wrapper.success(parameterVO);
//        }
//
//        MemberLevelConfigDO levelConfig = levelRight.getLevelConfig();
//        if (levelConfig == null) {
//            return Wrapper.success(parameterVO);
//        }
//
//        if (CollectionUtils.isEmpty(levelConfig.getRights())) {
//            return Wrapper.success(parameterVO);
//        }
//
//        MemberRightConfigDO configDO = levelConfig.getRights().stream().filter(config -> config.getRightType().equals(MemberRightTypeEnum.PRICE_RIGHT.getCode())).findFirst().orElse(null);
//        if (configDO == null || configDO.getStatus().equals(EnableDisableStatus.DISABLE.getCode()) || configDO.getParameter().compareTo(BigDecimal.ZERO) == 0) {
//            return Wrapper.success(parameterVO);
//        }
//
//        parameterVO.setParameter(configDO.getParameter());
//
//        return Wrapper.success(parameterVO);
//    }

    /**
     * 根据上级会员Id和上级会员角色Id，以及当前用户，查询价格权益参数设置
     *
     * @param headers HttpHeaders信息
     * @param upperVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberManageMemberCreditParameterResp getUpperMemberCreditParameter(HttpHeaders headers, MemberManageUpperMemberAndRoleReq upperVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        MemberManageMemberCreditParameterResp parameterVO = new MemberManageMemberCreditParameterResp();
        parameterVO.setParameter(new BigDecimal(1));
        //查询上下级关系
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperVO.getParentMemberId(), upperVO.getParentMemberRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if (relationDO == null || !relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode()) || !relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            return parameterVO;
        }

        MemberLevelRightDO levelRight = relationDO.getLevelRight();
        if (levelRight == null) {
            return parameterVO;
        }

        MemberLevelConfigDO levelConfig = levelRight.getLevelConfig();
        if (levelConfig == null) {
            return parameterVO;
        }

        if (CollectionUtils.isEmpty(levelConfig.getRights())) {
            return parameterVO;
        }

        MemberRightConfigDO configDO = levelConfig.getRights().stream().filter(config -> config.getRightType().equals(MemberRightTypeEnum.PRICE_RIGHT.getCode())).findFirst().orElse(null);
        if (configDO == null || configDO.getStatus().equals(EnableDisableStatusEnum.DISABLE.getCode()) || configDO.getParameter().compareTo(BigDecimal.ZERO) == 0) {
            return parameterVO;
        }

        parameterVO.setParameter(configDO.getParameter());

        return parameterVO;
    }

    /**
     * 根据当前用户（上级会员），查询下级会员的价格权益参数设置
     *
     * @param headers HttpHeaders信息
     * @param subVO   接口参数
     * @return 操作结果
     */
    @Override
    public MemberManageMemberCreditParameterResp getLowerMemberCreditParameter(HttpHeaders headers, SubMemberIdRoleIdDataReq subVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        MemberManageMemberCreditParameterResp parameterVO = new MemberManageMemberCreditParameterResp();
        parameterVO.setParameter(new BigDecimal(1));
        //查询上下级关系
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), subVO.getSubMemberId(), subVO.getSubRoleId());
        if (relationDO == null || !relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode()) || !relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            return parameterVO;
        }

        MemberLevelRightDO levelRight = relationDO.getLevelRight();
        if (levelRight == null) {
            return parameterVO;
        }

        MemberLevelConfigDO levelConfig = levelRight.getLevelConfig();
        if (levelConfig == null) {
            return parameterVO;
        }

        if (CollectionUtils.isEmpty(levelConfig.getRights())) {
            return parameterVO;
        }

        MemberRightConfigDO configDO = levelConfig.getRights().stream().filter(config -> config.getRightType().equals(MemberRightTypeEnum.PRICE_RIGHT.getCode())).findFirst().orElse(null);
        if (configDO == null || configDO.getStatus().equals(EnableDisableStatusEnum.DISABLE.getCode()) || configDO.getParameter().compareTo(BigDecimal.ZERO) == 0) {
            return parameterVO;
        }

        parameterVO.setParameter(configDO.getParameter());

        return parameterVO;
    }

    /**
     * 根据下单类型和会员名称，模糊分页查询会员
     *
     * @param headers HttpHeaders信息
     * @param typeVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageMembersByOrderTypeAndName(HttpHeaders headers, MemberManagePageByOrderTypeAndNameDataReq typeVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        long totalCount = 0;
        List<MemberManageQueryResp> queryVOList = new ArrayList<>();

        switch (MemberManageOrderTypeEnum.toEnum(typeVO.getOrderType())) {
            //下单模式手工下单、合并订单下单则供应会员数据来源于平台后台--平台会员管理中角色类型为服务提供的且会员类型为非渠道企业会员与非渠道个人会员的会员ID、会员名称、会员类型、会员角色、会员等级
            case MANUAL_ORDER:
            case COMBINED_ORDER:
                Pageable page = PageRequest.of(typeVO.getCurrent() - 1, typeVO.getPageSize(), Sort.by("id").ascending());
                Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
                    List<Predicate> list = new ArrayList<>();
                    list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
                    list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

                    list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
                    list.add(criteriaBuilder.notEqual(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
                    if (StringUtils.hasLength(typeVO.getName())) {
                        Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                        list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + typeVO.getName().trim() + "%"));
                    }

                    Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
                    list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                    Predicate[] p = new Predicate[list.size()];
                    return criteriaBuilder.and(list.toArray(p));
                };
                Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
                totalCount = pageList.getTotalElements();
                queryVOList = pageList.getContent().stream().map(relationDO -> {
                    MemberManageQueryResp queryVO = new MemberManageQueryResp();
                    queryVO.setId(relationDO.getId());
                    queryVO.setMemberId(relationDO.getSubMemberId());
                    queryVO.setName(relationDO.getSubMember().getName());
                    queryVO.setRoleId(relationDO.getSubRoleId());
                    queryVO.setRoleName(relationDO.getSubRoleName());
                    queryVO.setLevel(relationDO.getLevelRight().getLevel());
                    queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
                    queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
                    return queryVO;
                }).collect(Collectors.toList());
                break;
            default:
                break;
        }

        return new PageDataResp<>(totalCount, queryVOList);
    }

    /**
     * 分页查询会员列表页面搜索条件内容
     *
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @Override
    public MemberManageSearchConditionResp getPageSearchConditions(HttpHeaders headers, MemberManageMemberItemReq itemVO) {
        MemberManageSearchConditionResp conditionVO = new MemberManageSearchConditionResp();
        //会员类型
        conditionVO.setMemberTypes(baseMemberValidateService.getSubMemberTypeList(Arrays.asList(MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
        //角色
        List<MemberRoleDO> roleDOList = roleRepository.findAll();
        if (itemVO.getRoleTypeEnum() != null && !itemVO.getRoleTypeEnum().equals(0)) {
            conditionVO.setRoles(roleDOList.stream().filter(memberRoleDO -> !memberRoleDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode()) && memberRoleDO.getRoleType().equals(itemVO.getRoleTypeEnum())).map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).sorted(Comparator.comparingLong(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList()));
        } else {
            conditionVO.setRoles(roleDOList.stream().filter(memberRoleDO -> !memberRoleDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).sorted(Comparator.comparingLong(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList()));
        }

        //等级
        List<LevelAndTagResp> levelList = levelConfigRepository.findByLevelTypeAndStatus(MemberLevelTypeEnum.PLATFORM.getCode(), EnableDisableStatusEnum.ENABLE.getCode())
                .stream()
                .map(memberLevelConfigDO -> new LevelAndTagResp(memberLevelConfigDO.getLevel(), memberLevelConfigDO.getLevelTag(), memberLevelConfigDO.getSubRoleId()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(LevelAndTagResp::getRoleId).thenComparing(LevelAndTagResp::getLevel))), ArrayList::new))
                .stream()
                .sorted(Comparator.comparing(LevelAndTagResp::getLevel))
                .collect(Collectors.toList());
        conditionVO.setLevels(levelList);

        return conditionVO;
    }

    /**
     * 根据当前登录会员，查询下级会员角色列表
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public List<RoleIdAndNameResp> getSubRoleListByMember(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Integer memberTypeEnum = loginUser.getMemberType();
        Specification<MemberRoleDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.notEqual(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
            list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(root.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return roleRepository.findAll(specification).stream().map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).collect(Collectors.toList());
    }

    /**
     * “新增仓位存储”：根据商城类型分页查询会员
     *
     * @param headers HttpHeaders信息
     * @param typeVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageMembersByShopType(HttpHeaders headers, MemberManagePageByShopTypeDataReq typeVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        //当前选择的商城类型为企业商城、积分商城时，会员数据来源于会员能力--会员管理中当前会员所属的（下级）商户会员且角色为消费者类型
        Pageable page = PageRequest.of(typeVO.getCurrent() - 1, typeVO.getPageSize(), Sort.by("id").ascending());
        if (typeVO.getShopType().equals(ShopTypeEnum.ENTERPRISE.getCode()) || typeVO.getShopType().equals(ShopTypeEnum.SCORE.getCode())) {
            Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();

                list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
                list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
                list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
                list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

                Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.or(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()),
                        criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
                list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

                //会员名称
                if (StringUtils.hasLength(typeVO.getName())) {
                    Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                    list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + typeVO.getName().trim() + "%"));
                }

                //会员角色
                if (typeVO.getRoleId() != null && !typeVO.getRoleId().equals(0L)) {
                    list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), typeVO.getRoleId()));
                }

                //会员类型
                if (typeVO.getMemberType() != null && !typeVO.getMemberType().equals(0L)) {
                    list.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), typeVO.getMemberType()));
                }

                //等级
                if (typeVO.getLevel() != null && !typeVO.getLevel().equals(0)) {
                    Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                    list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), typeVO.getLevel()));
                }

                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };

            Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
            return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
                MemberManageQueryResp queryVO = new MemberManageQueryResp();
                queryVO.setId(relationDO.getId());
                queryVO.setMemberId(relationDO.getSubMemberId());
                queryVO.setName(relationDO.getSubMember().getName());
                queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
                queryVO.setLevel(relationDO.getLevelRight().getLevel());
                queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
                queryVO.setRoleId(relationDO.getSubRoleId());
                queryVO.setRoleName(relationDO.getSubRole().getRoleName());
                return queryVO;
            }).collect(Collectors.toList()));
        }
        return new PageDataResp<>(0L, new ArrayList<>());
    }

    /**
     * 分页查询角色为“服务提供者”的会员列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageMembersByServiceProviderRole(HttpHeaders headers, MemberManagePageDataReq pageVO) {
        memberCacheService.checkUserFromCache(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //下级会员角色为服务提供者
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            //会员名称
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            //会员类型
            if (pageVO.getMemberType() != null && !pageVO.getMemberType().equals(0L)) {
                list.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), pageVO.getMemberType()));
            }

            //角色Id
            if (pageVO.getRoleId() != null && !pageVO.getRoleId().equals(0L)) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), pageVO.getRoleId()));
            }

            //等级
            if (pageVO.getLevel() != null && !pageVO.getLevel().equals(0)) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), pageVO.getLevel()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 分页查询角色为“服务消费者”的平台会员列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageMembersByServiceConsumerRole(HttpHeaders headers, MemberManagePageDataReq pageVO) {
        memberCacheService.checkUserFromCache(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //下级会员角色为服务消费者
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            //会员名称
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            //会员类型
            if (pageVO.getMemberType() != null && !pageVO.getMemberType().equals(0L)) {
                list.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), pageVO.getMemberType()));
            }

            //角色Id
            if (pageVO.getRoleId() != null && !pageVO.getRoleId().equals(0L)) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), pageVO.getRoleId()));
            }

            //等级
            if (pageVO.getLevel() != null && !pageVO.getLevel().equals(0)) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), pageVO.getLevel()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据会员名称，分页查询角色类型为服务提供者的平台商户会员
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pagePlatformServiceProviderMerchantMember(HttpHeaders headers, MemberManagePageByNameDataReq pageVO) {
        memberCacheService.checkUserFromCache(headers);
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //下级会员角色为服务提供者
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
            list.add(criteriaBuilder.or(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

            // 下级会员名称
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据会员名称，分页查询角色类型为服务提供者的平台企业会员（非企业个人会员）
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pagePlatformServiceProviderEnterpriseMember(HttpHeaders headers, MemberManagePageByNameDataReq pageVO) {
        memberCacheService.checkUserFromCache(headers);
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //下级会员角色为服务提供者、会员类型为“企业会员”
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
            list.add(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()));

            // 下级会员名称
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据会员名称，分页查询角色为服务提供者的上级会员列表
     *
     * @param headers      Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageUpperProviderMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //上级会员角色为服务提供者
            Join<Object, Object> roleJoin = root.join("role", JoinType.LEFT);
            list.add(criteriaBuilder.equal(roleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            // 上级会员名称
            if (StringUtils.hasLength(pageByNameVO.getName())) {
                Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
                list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + pageByNameVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageByNameVO.getCurrent() - 1, pageByNameVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getMemberId());
            queryVO.setName(relationDO.getMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setRoleId(relationDO.getRoleId());
            queryVO.setRoleName(relationDO.getRole().getRoleName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据会员名称，分页查询上级会员列表
     *
     * @param headers      Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageUpperMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            // 上级会员名称
            if (StringUtils.hasLength(pageByNameVO.getName())) {
                Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
                list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + pageByNameVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageByNameVO.getCurrent() - 1, pageByNameVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getMemberId());
            queryVO.setName(relationDO.getMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setRoleId(relationDO.getRoleId());
            queryVO.setRoleName(relationDO.getRole().getRoleName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “售后能力 - 提交换货申请单” - 选择供应会员
     *
     * @param headers      Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageAfterSaleMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable page = PageRequest.of(pageByNameVO.getCurrent() - 1, pageByNameVO.getPageSize(), Sort.by("id").ascending());
        // step 1:调用查询站点是否有勾选【SAAS多租户部署】
        Boolean enableMultiTenancy = platformManageFeignService.enableMultiTenancy();
        boolean flag = enableMultiTenancy != null ? enableMultiTenancy : Boolean.FALSE;
        Specification<MemberRelationDO> specification;
        // 当前会员的【会员类型】是【企业会员】或【个人会员】且【会员角色类型】是【服务消费】且【PAAS-站点管理】有勾选【SAAS多租户部署】
        if ((loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))&& flag) {
            // 会员数据来源于【会员能力--会员信息查询】中当前会员的上级会员且【会员类型】为【企业会员】或【个人会员】且【会员角色类型】为【服务提供】的会员数据
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                // 排除 会员等级是平台会员
                list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
                list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));

                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

                list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
                // 【上级会员角色】
                Join<Object, Object> roleJoin = root.join("role", JoinType.LEFT);
                // 【会员角色类型】为【服务提供】
                list.add(criteriaBuilder.equal(roleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
                // 【会员类型】为【企业会员】或【个人会员】等同 【会员等级类型】为【商户会员】
                list.add(criteriaBuilder.or(criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                // 上级会员名称
                if (StringUtils.hasLength(pageByNameVO.getName())) {
                    Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
                    list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + pageByNameVO.getName().trim() + "%"));
                }
                return query.where(list.toArray(new Predicate[0])).getRestriction();
            };
            Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
            return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
                MemberManageQueryResp queryVO = new MemberManageQueryResp();
                queryVO.setId(relationDO.getId());
                queryVO.setMemberId(relationDO.getMemberId());
                queryVO.setName(relationDO.getMember().getName());
                queryVO.setRoleId(relationDO.getRoleId());
                queryVO.setRoleName(relationDO.getRole().getRoleName());
                queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getRole().getMemberType()));
                queryVO.setLevel(relationDO.getLevelRight().getLevel());
                queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
                return queryVO;
            }).collect(Collectors.toList()));
        } else if ((loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode())) && !flag) {
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

                list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
                Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
                list.add(criteriaBuilder.or(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                // 平台会员名称
                if (StringUtils.hasLength(pageByNameVO.getName())) {
                    Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                    list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageByNameVO.getName().trim() + "%"));
                }

                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };

            Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
            return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
                MemberManageQueryResp queryVO = new MemberManageQueryResp();
                queryVO.setId(relationDO.getId());
                queryVO.setMemberId(relationDO.getSubMemberId());
                queryVO.setName(relationDO.getSubMember().getName());
                queryVO.setRoleId(relationDO.getSubRoleId());
                queryVO.setRoleName(relationDO.getSubRole().getRoleName());
                queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
                queryVO.setLevel(relationDO.getLevelRight().getLevel());
                queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
                return queryVO;
            }).collect(Collectors.toList()));
        }
        return new PageDataResp<>(0L, new ArrayList<>());
    }

    /**
     * “物流能力 - 新增物流公司” - 选择物流服务商
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageLogisticSubMember(HttpHeaders headers, MemberManagePageLogisticsDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //服务提供者
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            //会员名称
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (pageVO.getRoleId() != null && !pageVO.getRoleId().equals(0L)) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), pageVO.getRoleId()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification, Sort.by("id").ascending());

        if (!CollectionUtils.isEmpty(pageVO.getExcludeList())) {
            relationDOList = relationDOList.stream().filter(r -> pageVO.getExcludeList().stream().noneMatch(memberExcludeVO -> memberExcludeVO.getMemberId().equals(r.getSubMemberId()) && memberExcludeVO.getRoleId().equals(r.getSubRoleId()))).collect(Collectors.toList());
        }

        int totalCount = relationDOList.size();
        List<MemberRelationDO> pageList = CollectionPageUtil.pageList(relationDOList, totalCount, pageVO.getCurrent(), pageVO.getPageSize());

        return new PageDataResp<>((long) totalCount, pageList.stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getSubMemberId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleId(p.getSubRoleId());
            queryVO.setRoleName(p.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 分页查询会员下属用户
     *
     * @param headers Http头部新
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<UserQueryResp> pageUsers(HttpHeaders headers, UserPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        return baseUserDetailService.pageUsers(loginUser.getMemberId(), pageVO.getName(), pageVO.getOrgName(), pageVO.getJobTitle(), pageVO.getCurrent(), pageVO.getPageSize(), true);
    }

    /**
     * "适用会员等级", 查询等级配置详情
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSuitableLevelConfigResp> pageMarketingSuitableLevelConfig(HttpHeaders headers, MemberLevelDetailPageRespData pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        //当前会员的会员类型是企业会员或个人会员且会员角色类型是服务提供时，数据来源于当前会员的会员能力--会员规则配置--会员等级管理中且会员等级类型为商户会员且会员角色类型为服务消费的会员等级数据
        //当前会员的会员类型是渠道企业会员或渠道个人会员且会员角色类型是服务提供时，数据来源于当前会员的会员能力--会员规则配置--会员等级管理中且会员等级类型为渠道会员且会员角色类型为服务消费的会员等级数据
        if(!loginUser.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            return new PageDataResp<>(0L , new ArrayList<>());
        }

        QMemberLevelConfigDO qMemberLevelConfig = QMemberLevelConfigDO.memberLevelConfigDO;
        QMemberRoleDO qMemberRoleDO= QMemberRoleDO.memberRoleDO;

        JPAQuery<MemberSuitableLevelConfigResp> query = jpaQueryFactory.select(Projections.constructor(MemberSuitableLevelConfigResp.class, qMemberLevelConfig.id, qMemberRoleDO.memberType, qMemberLevelConfig.subRoleId, qMemberRoleDO.roleType, qMemberRoleDO.roleName, qMemberLevelConfig.levelType, qMemberLevelConfig.level, qMemberLevelConfig.levelTag))
                .from(qMemberLevelConfig).leftJoin(qMemberRoleDO).on(qMemberLevelConfig.subRoleId.eq(qMemberRoleDO.id))
                .where(qMemberLevelConfig.memberId.eq(loginUser.getMemberId()).and(qMemberLevelConfig.roleId.eq(loginUser.getMemberRoleId())).and(qMemberLevelConfig.levelType.eq(loginUser.getMemberLevelType())))
                .where(qMemberRoleDO.roleType.eq(RoleTypeEnum.SERVICE_CONSUMER.getCode()))
                .limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        if(StringUtils.hasLength(pageVO.getMemberTypes())) {
            List<Integer> memberTypes = Stream.of(pageVO.getMemberTypes().split(","))
                    .filter(StringUtils::hasText)
                    .map(String::trim)
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            query.where(qMemberRoleDO.memberType.in(memberTypes));
        }

        return new PageDataResp<>(query.fetchCount(), query.fetch());
    }

    /**
     * “营销能力” - 获取适用会员查询条件
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public MemberSuitableConditionResp getMarketingSuitableCondition(HttpHeaders headers, SuitableMemberTypeReq typeVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberSuitableConditionResp conditionVO = new MemberSuitableConditionResp();
        // 会员类型（这里返回的是Id）
        List<MemberTypeAndNameResp> memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType());
        memberTypeList.add(0, new MemberTypeAndNameResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setMemberTypes(memberTypeList);

        // 会员等级
        List<LevelAndTagResp> levelList = baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId());
        levelList.add(0, new LevelAndTagResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setLevels(levelList);

        // 适用新老用户
        List<SelectItemResp> suitableMemberTypes = Optional.ofNullable(typeVO).map(SuitableMemberTypeReq::getSuitableMemberTypes).map(memberTypes -> memberTypes.stream().map(e -> {
            AbilitySuitableMemberEnum abilitySuitableMemberEnum = AbilitySuitableMemberEnum.parse(e);
            return new SelectItemResp(abilitySuitableMemberEnum.getCode(), abilitySuitableMemberEnum.getName());
        }).collect(Collectors.toList())).orElse(new ArrayList<>());
        suitableMemberTypes.add(0, new SelectItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setSuitableMemberTypes(suitableMemberTypes);

        return conditionVO;
    }

    /**
     * “平台营销” - 获取适用会员查询条件
     * @param headers Http头部信息
     * @param typeVO 接口参数
     * @return 查询结果
     */
    @Override
    public PlatformMemberSuitableConditionResp getPlatformMarketingSuitableCondition(HttpHeaders headers, PlatformSuitableMemberTypeReq typeVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        PlatformMemberSuitableConditionResp conditionVO = new PlatformMemberSuitableConditionResp();

        // 会员类型(这里是memberTypeEnum)
//        List<SelectItemResp> memberTypeList = Optional.ofNullable(typeVO).map(PlatformSuitableMemberTypeVO::getMemberTypes).map(memberTypes -> memberTypes.stream().map(type -> {
//            MemberTypeEnum memberTypeEnum = MemberTypeEnum.parse(type);
//            return new SelectItemResp(memberTypeEnum.getCode(), memberTypeEnum.getNameByCode());
//        }).collect(Collectors.toList())).orElse(new ArrayList<>());

        List<SelectItemResp> memberTypeList = Stream.of(MemberTypeEnum.MERCHANT, MemberTypeEnum.MERCHANT_PERSONAL).map(e -> new SelectItemResp(e.getCode(), e.getName())).collect(Collectors.toList());
        memberTypeList.add(0, new SelectItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setMemberTypes(memberTypeList);

        //会员等级
        List<LevelAndTagResp> levelList = baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId());
        levelList.add(0, new LevelAndTagResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setLevels(levelList);

        // 适用新老会员
//        List<SelectItemResp> suitableMemberTypes = Optional.ofNullable(typeVO).map(PlatformSuitableMemberTypeVO::getSuitableMemberTypes).map(memberTypes -> memberTypes.stream().map(e -> {
//            PlatformSuitableMemberEnum suitableMemberTypeEnum = PlatformSuitableMemberEnum.parse(e);
//            return new SelectItemResp(suitableMemberTypeEnum.getCode(), suitableMemberTypeEnum.getNameByCode());
//        }).collect(Collectors.toList())).orElse(new ArrayList<>());
        List<SelectItemResp> suitableMemberTypes = Arrays.stream(PlatformSuitableMemberEnum.values()).map(e -> new SelectItemResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
        suitableMemberTypes.add(0, new SelectItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setSuitableMemberTypes(suitableMemberTypes);

        return conditionVO;
    }

    /**
     * “营销能力” - 查询适用会员(分页)
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSuitableResp> pageMarketingSuitable(HttpHeaders headers, MarketingSuitablePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            // 会员已入库
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));

            // 服务消费者
            list.add(criteriaBuilder.equal(root.get("subRole").get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            // 根据会员等级id
            if(!CollectionUtils.isEmpty(pageVO.getSuitableMemberLevelTypes())) {
                list.add(root.get("levelRight").get("levelConfig").get("id").in(pageVO.getSuitableMemberLevelTypes()));
            }

            if (NumberUtil.notNullOrZero(pageVO.getMemberId())) {
                list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), pageVO.getMemberId()));
            }

            if (StringUtils.hasLength(pageVO.getName())) {
                list.add(criteriaBuilder.like(root.get("subMember").get("name").as(String.class), "%" + pageVO.getName() + "%"));
            }

            if (NumberUtil.notNullOrZero(pageVO.getMemberTypeEnum())) {
                list.add(criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), pageVO.getMemberTypeEnum()));
            }

            if (NumberUtil.notNullOrZero(pageVO.getLevel())) {
                list.add(criteriaBuilder.equal(root.get("levelRight").get("level").as(Integer.class), pageVO.getLevel()));
            }

            if (NumberUtil.notNullOrZero(pageVO.getSuitableMemberType())) {
                if (AbilitySuitableMemberEnum.NEW_MEMBER.getCode().equals(pageVO.getSuitableMemberType())) {
                    list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("depositTime").as(LocalDateTime.class), DateTimeUtil.getTodayBeginLocal()));
                } else {
                    list.add(criteriaBuilder.lessThan(root.get("depositTime").as(LocalDateTime.class), DateTimeUtil.getTodayBeginLocal()));
                }
            }

            if (Objects.nonNull(pageVO.getBecomeTimeStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateTime(pageVO.getBecomeTimeStart());
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("depositTime"), startDate));
            }

            if (Objects.nonNull(pageVO.getBecomeTimeEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateTime(pageVO.getBecomeTimeEnd());
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("depositTime"), endDate));
            }

            if(!CollectionUtils.isEmpty(pageVO.getExcludeMemberList())) {
                List<Predicate> excludeList = pageVO.getExcludeMemberList().stream().map(ex -> criteriaBuilder.and(criteriaBuilder.or(criteriaBuilder.notEqual(root.get("subMemberId").as(Long.class), ex.getMemberId()), criteriaBuilder.notEqual(root.get("subRoleId").as(Long.class), ex.getRoleId())))).collect(Collectors.toList());
                list.addAll(excludeList);
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberSuitableResp queryVO = new MemberSuitableResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setName(relationDO.getSubMember().getName());
            // 适用会员类型
            AbilitySuitableMemberEnum abilitySuitableMemberEnum = Optional.ofNullable(relationDO.getDepositTime())
                    .map(depositTime -> depositTime.isBefore(DateTimeUtil.getTodayBeginLocal()) ? AbilitySuitableMemberEnum.NEW_MEMBER : AbilitySuitableMemberEnum.OLD_MEMBER)
                    .orElse(null);
            queryVO.setSuitableMemberType(Objects.isNull(abilitySuitableMemberEnum) ? 0 : abilitySuitableMemberEnum.getCode());
            queryVO.setSuitableMemberTypeName(Objects.isNull(abilitySuitableMemberEnum) ? "" : abilitySuitableMemberEnum.getName());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setBecomeTime(DateTimeUtil.formatDateTime(relationDO.getDepositTime()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “平台营销” - 查询适用会员(分页)
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSuitableResp> pagePlatformMarketingSuitable(HttpHeaders headers, MarketingSuitablePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            // 会员已入库
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));

            // 服务消费者
            list.add(criteriaBuilder.equal(root.get("subRole").get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            // 根据会员等级id
            if(!CollectionUtils.isEmpty(pageVO.getSuitableMemberLevelTypes())) {
                list.add(root.get("levelRight").get("levelConfig").get("id").in(pageVO.getSuitableMemberLevelTypes()));
            }

            if (NumberUtil.notNullOrZero(pageVO.getMemberId())) {
                list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), pageVO.getMemberId()));
            }

            if (StringUtils.hasLength(pageVO.getName())) {
                list.add(criteriaBuilder.like(root.get("subMember").get("name").as(String.class), "%" + pageVO.getName() + "%"));
            }

            if (NumberUtil.notNullOrZero(pageVO.getMemberTypeEnum())) {
                list.add(criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), pageVO.getMemberTypeEnum()));
            }

            if (NumberUtil.notNullOrZero(pageVO.getLevel())) {
                list.add(criteriaBuilder.equal(root.get("levelRight").get("level").as(Integer.class), pageVO.getLevel()));
            }

            if (NumberUtil.notNullOrZero(pageVO.getSuitableMemberType())) {
                if (AbilitySuitableMemberEnum.NEW_MEMBER.getCode().equals(pageVO.getSuitableMemberType())) {
                    list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("depositTime").as(LocalDateTime.class), DateTimeUtil.getTodayBeginLocal()));
                } else {
                    list.add(criteriaBuilder.lessThan(root.get("depositTime").as(LocalDateTime.class), DateTimeUtil.getTodayBeginLocal()));
                }
            }

            if (Objects.nonNull(pageVO.getBecomeTimeStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateTime(pageVO.getBecomeTimeStart());
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("depositTime"), startDate));
            }

            if (Objects.nonNull(pageVO.getBecomeTimeEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateTime(pageVO.getBecomeTimeEnd());
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("depositTime"), endDate));
            }

            if(!CollectionUtils.isEmpty(pageVO.getExcludeMemberList())) {
                List<Predicate> excludeList = pageVO.getExcludeMemberList().stream().map(ex -> criteriaBuilder.and(criteriaBuilder.or(criteriaBuilder.notEqual(root.get("subMemberId").as(Long.class), ex.getMemberId()), criteriaBuilder.notEqual(root.get("subRoleId").as(Long.class), ex.getRoleId())))).collect(Collectors.toList());
                list.addAll(excludeList);
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberSuitableResp queryVO = new MemberSuitableResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setName(relationDO.getSubMember().getName());
            // 适用会员类型
            AbilitySuitableMemberEnum abilitySuitableMemberEnum = Optional.ofNullable(relationDO.getDepositTime())
                    .map(depositTime -> depositTime.isBefore(DateTimeUtil.getTodayBeginLocal()) ? AbilitySuitableMemberEnum.NEW_MEMBER : AbilitySuitableMemberEnum.OLD_MEMBER)
                    .orElse(null);
            queryVO.setSuitableMemberType(Objects.isNull(abilitySuitableMemberEnum) ? 0 : abilitySuitableMemberEnum.getCode());
            queryVO.setSuitableMemberTypeName(Objects.isNull(abilitySuitableMemberEnum) ? "" : abilitySuitableMemberEnum.getName());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setBecomeTime(DateTimeUtil.formatDateTime(relationDO.getDepositTime()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “平台营销” - 获取邀请报名参加会员查询条件
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public PlatformMemberInviteConditionResp getPlatformMarketingInviteCondition(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        PlatformMemberInviteConditionResp conditionVO = new PlatformMemberInviteConditionResp();

        // 会员类型
        List<SelectItemResp> memberTypeList = Stream.of(MemberTypeEnum.MERCHANT, MemberTypeEnum.MERCHANT_PERSONAL)
                .map(type -> new SelectItemResp(type.getCode(), type.getName()))
                .collect(Collectors.toList());
        memberTypeList.add(0, new SelectItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setMemberTypes(memberTypeList);

        //会员等级
        List<LevelAndTagResp> levelList = baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId());
        levelList.add(0, new LevelAndTagResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setLevels(levelList);

        // 会员角色
        conditionVO.setRoles(baseMemberLevelConfigService.findLevelConfigRoles(loginUser.getMemberId(), loginUser.getMemberRoleId()));

        return conditionVO;
    }

    /**
     * “平台营销” - 查询邀请报名参加会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<PlatformMarketingInvitePageResp> pagePlatformMarketingInvite(HttpHeaders headers, PlatformMarketingInviteQueryDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Long.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            // 会员审核通过
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));

            // 服务提供者
            Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
            // 企业或个人
            List<Integer> memberTypeList = Arrays.asList(MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode());
            list.add(criteriaBuilder.and(subRoleJoin.get("memberType").as(Integer.class).in(memberTypeList)));

            if (StringUtils.hasLength(pageVO.getName())) {
                Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName() + "%"));
            }

            if (NumberUtil.notNullOrZero(pageVO.getRoleId())) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), pageVO.getRoleId()));
            }

            if (NumberUtil.notNullOrZero(pageVO.getLevel())) {
                Join<MemberRelationDO, MemberLevelRightDO> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), pageVO.getLevel()));
            }

            if(!CollectionUtils.isEmpty(pageVO.getExcludeMemberList())) {
                List<Predicate> excludeList = pageVO.getExcludeMemberList().stream().map(ex -> criteriaBuilder.and(criteriaBuilder.or(criteriaBuilder.notEqual(root.get("subMemberId").as(Long.class), ex.getMemberId()), criteriaBuilder.notEqual(root.get("subRoleId").as(Long.class), ex.getRoleId())))).collect(Collectors.toList());
                list.addAll(excludeList);
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relation -> {
            PlatformMarketingInvitePageResp invitePageVO = new PlatformMarketingInvitePageResp();
            invitePageVO.setId(relation.getId());
            invitePageVO.setMemberId(relation.getSubMemberId());
            invitePageVO.setRoleId(relation.getSubRoleId());
            invitePageVO.setName(relation.getSubMember().getName());
            invitePageVO.setRoleName(relation.getSubRoleName());
            invitePageVO.setMemberTypeName(MemberTypeEnum.getName(relation.getSubRole().getMemberType()));
            invitePageVO.setLevel(relation.getLevelRight() == null ? 0 : relation.getLevelRight().getLevel());
            invitePageVO.setLevelTag(relation.getLevelRight() == null ? "" : relation.getLevelRight().getLevelTag());
            return invitePageVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “订单服务 - 代客下单” - 查询角色为服务消费者的平台会员列表
     *
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberManageQueryResp> findPlatformConsumerMembers(HttpHeaders headers, MemberManageNameReq nameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        //如果输入name为空或当前登录会员角色类型不是服务提供者
        if (!StringUtils.hasText(nameVO.getName()) || !loginUser.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            return new ArrayList<>();
        }

        // step 1:调用查询站点是否有勾选【SAAS多租户部署】
        Boolean enableMultiTenancy = platformManageFeignService.enableMultiTenancy();
        boolean flag = enableMultiTenancy != null ? enableMultiTenancy : Boolean.FALSE;

        //通用筛选条件   如果后期需求又迭代了，这部分条件可以拆了放下面
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("relType").as(Long.class), MemberRelationTypeEnum.OTHER.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));

            if (StringUtils.hasText(nameVO.getName())) {
                Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + nameVO.getName().trim() + "%"));
            }

            Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.LEFT);
            //会员角色类型为服务消费者
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        //当前会员的会员类型是企业会员或个人会员且会员角色类型是服务提供且PAAS-站点管理有勾选SAAS多租户部署
        if ((loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode())) && flag) {
            specification = specification.and((root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.LEFT);
                //下级会员类型是企业会员或个人会员
                list.add(criteriaBuilder.or(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
                return criteriaBuilder.and(list.toArray(new Predicate[0]));
            });
        } else if ((loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))) {
            //当前会员的会员类型是企业会员或个人会员且会员角色类型是服务提供者
            specification = specification.and((root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.LEFT);
                //下级会员类型是企业会员或个人会员
                list.add(criteriaBuilder.or(criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(subRoleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
                return criteriaBuilder.and(list.toArray(new Predicate[0]));
            });
        } else {
            specification = null;
        }

        Pageable pageable = PageRequest.of(0, 30, Sort.by("id").ascending());
        return relationRepository.findAll(specification, pageable).getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * “订单服务 - 新增现货采购订单” - 查询供应会员列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageBuyerOrderMembers(HttpHeaders headers, MemberManagePageByNameDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        //如果当前登录会员角色类型不是服务消费者
        if (!loginUser.getMemberRoleType().equals(RoleTypeEnum.SERVICE_CONSUMER.getCode())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        // step 1:调用查询站点是否有勾选【SAAS多租户部署】
        Boolean isEnableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        Specification<MemberRelationDO> specification;

        //如果会员类型是企业会员或个人会员且会员角色类型是服务消费且【PAAS-站点管理】有勾选【SAAS多租户部署】
        //企业会员或个人会员这个条件等价于会员等级类型为商户会员
        if (loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode()) && isEnableMultiTenancy) {
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
                list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
                list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

                //上级会员角色为服务提供者且会员类型为企业会员或个人会员
                Join<Object, Object> roleJoin = root.join("role", JoinType.LEFT);
                list.add(criteriaBuilder.equal(roleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
                list.add(criteriaBuilder.or(criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                // 上级会员名称
                if (StringUtils.hasLength(pageVO.getName())) {
                    Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
                    list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
                }

                //生命周期规则判断
                if (NumberUtil.notNullOrZero(pageVO.getLifeCycleStageRuleId())) {
                    Join<Object, Object> memberLifecycleStageJoin = root.join("memberLifecycleStages", JoinType.LEFT);
                    Join<Object, Object> memberLifeCycleRuleConfigJoin = memberLifecycleStageJoin.join("memberLifeCycleRuleConfigDOSet", JoinType.LEFT);
                    list.add(criteriaBuilder.equal(memberLifeCycleRuleConfigJoin.get("lifeCycleRuleEnum").as(Integer.class), pageVO.getLifeCycleStageRuleId()));
                }

                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };
        } else {
            //当前会员的会员类型是企业会员或个人会员且会员角色类型是服务消费时，会员数据来源于【平台后台--会员管理--会员维护】中会员类型为企业会员或个人会员且会员角色类型为服务提供的会员数据
            //当前会员的会员类型是渠道企业会员或渠道个人会员且会员角色类型是服务消费时，会员数据来源于会员能力--会员信息查询中当前渠道会员的上级渠道会员且会员等级类型为渠道会员且会员角色类型为服务提供
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                if (loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
                    list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
                    list.add(criteriaBuilder.equal(root.get("subRole").get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                    if (StringUtils.hasText(pageVO.getName())) {
                        list.add(criteriaBuilder.like(root.get("subMember").get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
                    }

                    // 查询不符合生命周期的账号
                    List<String> phones = getMemberRelation(loginUser, pageVO).stream()
                            .filter(memberRelation -> isFilter(memberRelation, pageVO.getLifeCycleStageRuleId()))
                            .map(MemberRelationDO::getMember).map(MemberDO::getPhone).collect(Collectors.toList());
                    if (!phones.isEmpty()){
                        list.add(criteriaBuilder.in(root.get("subMember").get("phone")).value(phones).not());
                    }
                }

                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };
        }

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(p -> crateMemberManageQueryVO(loginUser, p, isEnableMultiTenancy)).collect(Collectors.toList()));
    }

    private boolean isFilter(MemberRelationDO memberRelation, Integer rule){
        if (Objects.nonNull(memberRelation.getMemberLifecycleStages())) {
            List<Integer> lifeCycleRuleList = memberRelation.getMemberLifecycleStages().getMemberLifeCycleRuleConfigDOSet().stream().map(MemberLifeCycleRuleConfigDO::getLifeCycleRuleEnum).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lifeCycleRuleList)) {
                return Boolean.FALSE;
            }
            if (!lifeCycleRuleList.contains(rule)){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    private MemberManageQueryResp crateMemberManageQueryVO(UserLoginCacheDTO loginUser, MemberRelationDO memberRelationDO, Boolean isEnableMultiTenancy){
        MemberManageQueryResp queryVO = new MemberManageQueryResp();
        queryVO.setId(memberRelationDO.getId());
        //如果是渠道会员或有开启saas多租户部署，则取上级会员会员信息
        if (isEnableMultiTenancy) {
            queryVO.setMemberId(memberRelationDO.getMemberId());
            queryVO.setName(memberRelationDO.getMember().getName());
            queryVO.setRoleId(memberRelationDO.getRoleId());
            queryVO.setRoleName(memberRelationDO.getRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(memberRelationDO.getRole().getMemberType()));
            queryVO.setLevel(memberRelationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(memberRelationDO.getLevelRight().getLevelTag());
        } else {
            //取平台会员的下级会员信息
            queryVO.setMemberId(memberRelationDO.getSubMemberId());
            queryVO.setName(memberRelationDO.getSubMember().getName());
            queryVO.setRoleId(memberRelationDO.getSubRoleId());
            queryVO.setRoleName(memberRelationDO.getSubRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(memberRelationDO.getSubRole().getMemberType()));
            queryVO.setLevel(memberRelationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(memberRelationDO.getLevelRight().getLevelTag());
        }
        return queryVO;
    }

    /**
     * 获取不符合生命周期的供应商
     * @param loginUser 登录用户
     * @param pageVO    查询条件
     * @return List<MemberRelationDO>
     */
    private List<MemberRelationDO> getMemberRelation(UserLoginCacheDTO loginUser, MemberManagePageByNameDataReq pageVO){
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            // 上级会员角色为服务提供者且会员类型为企业会员或个人会员
            Join<Object, Object> roleJoin = root.join("role", JoinType.LEFT);
            list.add(criteriaBuilder.equal(roleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
            list.add(criteriaBuilder.or(criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

            return criteriaBuilder.and(list.toArray(new Predicate[0]));
        };
        return relationRepository.findAll(specification);
    }


    /**
     * 根据会员名称，分页查询角色为服务提供者的上级会员列表(增加条件会员等级类型为商户会员)
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageUpperProviderMerchantMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO) {

        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //上级会员角色为服务提供者且会员类型为企业会员或个人会员
            Join<Object, Object> roleJoin = root.join("role", JoinType.LEFT);
            list.add(criteriaBuilder.equal(roleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
            list.add(criteriaBuilder.or(criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

            // 上级会员名称
            if (StringUtils.hasLength(pageByNameVO.getName())) {
                Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
                list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + pageByNameVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageByNameVO.getCurrent() - 1, pageByNameVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getMemberId());
            queryVO.setName(relationDO.getMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setRoleId(relationDO.getRoleId());
            queryVO.setRoleName(relationDO.getRole().getRoleName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据会员名称，分页查询角色为服务消费者的上级会员列表(增加条件会员等级类型为商户会员)
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageUpperConsumerMerchantMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //上级会员角色为服务提供者
            Join<Object, Object> roleJoin = root.join("role", JoinType.LEFT);
            list.add(criteriaBuilder.equal(roleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            // 上级会员名称
            if (StringUtils.hasLength(pageByNameVO.getName())) {
                Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
                list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + pageByNameVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageByNameVO.getCurrent() - 1, pageByNameVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getMemberId());
            queryVO.setName(relationDO.getMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setRoleId(relationDO.getRoleId());
            queryVO.setRoleName(relationDO.getRole().getRoleName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据会员名称，分页查询角色为当前会员所属的下属会员且角色类型为服务提供的会员
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> pageLowerProviderMerchantMember(HttpHeaders headers, MemberManagePageByNameDataReq pageByNameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //下级会员角色为服务提供者
            Join<Object, Object> roleJoin = root.join("role", JoinType.LEFT);
            list.add(criteriaBuilder.equal(roleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            // 下级会员名称
            if (StringUtils.hasLength(pageByNameVO.getName())) {
                Join<Object, Object> memberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + pageByNameVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        Pageable page = PageRequest.of(pageByNameVO.getCurrent() - 1, pageByNameVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(relationDO.getId());
            queryVO.setMemberId(relationDO.getMemberId());
            queryVO.setName(relationDO.getMember().getName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setRoleId(relationDO.getRoleId());
            queryVO.setRoleName(relationDO.getRole().getRoleName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “选择采购会员”功能
     *
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 新增结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> buyerMemberInfo(HttpHeaders headers, MemberNameDataReq nameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        //如果当前登录会员角色类型不是服务消费者
        if (!loginUser.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        // step 1:调用查询站点是否有勾选【SAAS多租户部署】
        Boolean isEnableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        Specification<MemberRelationDO> specification;

        //如果会员类型是企业会员或个人会员且会员角色类型是服务消费且【PAAS-站点管理】有勾选【SAAS多租户部署】
        //企业会员或个人会员这个条件等价于会员等级类型为商户会员
        if (loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode()) && isEnableMultiTenancy) {
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
                list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
                list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
                list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

                //下级会员角色为服务消费者且会员类型为企业会员或个人会员
                Join<Object, Object> roleJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(roleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));
                list.add(criteriaBuilder.or(criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                // 下级会员名称
                if (StringUtils.hasLength(nameVO.getMemberName())) {
                    Join<Object, Object> memberJoin = root.join("subMember", JoinType.LEFT);
                    list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + nameVO.getMemberName().trim() + "%"));
                }

                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };
        } else {
            //当前会员的会员类型是企业会员或个人会员且会员角色类型是服务提供时，会员数据来源于【平台后台--会员管理--会员维护】中会员类型为企业会员或个人会员且会员角色类型为服务消费的会员数据
            //当前会员的会员类型是渠道企业会员或渠道个人会员且会员角色类型是服务提供时，会员数据来源于【会员能力--会员管理--会员信息】中当前渠道会员的下级渠道会员且会员类型为渠道企业会员或渠道个人会员且会员角色类型为服务消费
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                if (loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
                    list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
                    list.add(criteriaBuilder.equal(root.get("subRole").get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                    if (StringUtils.hasText(nameVO.getMemberName())) {
                        list.add(criteriaBuilder.like(root.get("subMember").get("name").as(String.class), "%" + nameVO.getMemberName().trim() + "%"));
                    }
                }

                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };
        }

        Pageable pageable = PageRequest.of(nameVO.getCurrent() - 1, nameVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getSubMemberId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleId(p.getSubRoleId());
            queryVO.setRoleName(p.getSubRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “选择供应会员”功能
     *
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 新增结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> supplyMemberInfo(HttpHeaders headers, MemberNameDataReq nameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        //如果当前登录会员角色类型不是服务消费者
        if (!loginUser.getMemberRoleType().equals(RoleTypeEnum.SERVICE_CONSUMER.getCode())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        // step 1:调用查询站点是否有勾选【SAAS多租户部署】
        Boolean isEnableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        Specification<MemberRelationDO> specification;

        //如果会员类型是企业会员或个人会员且会员角色类型是服务消费且【PAAS-站点管理】有勾选【SAAS多租户部署】
        //企业会员或个人会员这个条件等价于会员等级类型为商户会员
        if (loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode()) && isEnableMultiTenancy) {
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
                list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
                list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

                //上级会员角色为服务提供者且会员类型为企业会员或个人会员
                Join<Object, Object> roleJoin = root.join("role", JoinType.LEFT);
                list.add(criteriaBuilder.equal(roleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
                list.add(criteriaBuilder.or(criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(roleJoin.get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                // 上级会员名称
                if (StringUtils.hasLength(nameVO.getMemberName())) {
                    Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
                    list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + nameVO.getMemberName().trim() + "%"));
                }

                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };
        } else {
            //当前会员的会员类型是企业会员或个人会员且会员角色类型是服务消费时，会员数据来源于【平台后台--会员管理--会员维护】中会员类型为企业会员或个人会员且会员角色类型为服务提供的会员数据
            //当前会员的会员类型是渠道企业会员或渠道个人会员且会员角色类型是服务消费时，会员数据来源于会员能力--会员信息查询中当前渠道会员的上级渠道会员且会员等级类型为渠道会员且会员角色类型为服务提供
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                if (loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
                    list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
                    list.add(criteriaBuilder.equal(root.get("subRole").get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                    if (StringUtils.hasText(nameVO.getMemberName())) {
                        list.add(criteriaBuilder.like(root.get("subMember").get("name").as(String.class), "%" + nameVO.getMemberName().trim() + "%"));
                    }
                }

                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };
        }

        Pageable pageable = PageRequest.of(nameVO.getCurrent() - 1, nameVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            //如果是渠道会员或有开启saas多租户部署，则取上级会员会员信息
            if (isEnableMultiTenancy) {
                queryVO.setMemberId(p.getMemberId());
                queryVO.setName(p.getMember().getName());
                queryVO.setRoleId(p.getRoleId());
                queryVO.setRoleName(p.getRole().getRoleName());
                queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getRole().getMemberType()));
                queryVO.setLevel(p.getLevelRight().getLevel());
                queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            } else {
                //取平台会员的下级会员信息
                queryVO.setMemberId(p.getSubMemberId());
                queryVO.setName(p.getSubMember().getName());
                queryVO.setRoleId(p.getSubRoleId());
                queryVO.setRoleName(p.getSubRoleName());
                queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
                queryVO.setLevel(p.getLevelRight().getLevel());
                queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            }
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “选择采购会员”功能 - 用于流程规则
     * 1、当前会员的会员类型是企业会员或个人会员且会员角色类型是服务提供则客户数据来源于【会员能力--会员管理--会员信息】中当前会员的下级会员且会员类型为企业会员或个人会员且会员角色类型为服务消费的会员数据
     * 2、当前会员的会员类型是渠道企业会员或渠道个人会员且会员角色类型是服务提供时，客户数据来源于【会员能力--会员管理--会员信息】中当前渠道会员的下级渠道会员且会员类型为渠道企业会员或渠道个人会员且会员角色类型为服务消费
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 新增结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> processBuyerMember(HttpHeaders headers, MemberNameDataReq nameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        //如果当前登录会员角色类型不是服务消费者
        if (!loginUser.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        Specification<MemberRelationDO> specification;
        //当前会员的会员类型是企业会员或个人会员且会员角色类型是服务提供时，会员数据来源于【平台后台--会员管理--会员维护】中会员类型为企业会员或个人会员且会员角色类型为服务消费的会员数据
        specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
                list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
                list.add(criteriaBuilder.equal(root.get("subRole").get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));
                list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

                if (StringUtils.hasText(nameVO.getMemberName())) {
                    list.add(criteriaBuilder.like(root.get("subMember").get("name").as(String.class), "%" + nameVO.getMemberName().trim() + "%"));
                }
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable pageable = PageRequest.of(nameVO.getCurrent() - 1, nameVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getSubMemberId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleId(p.getSubRoleId());
            queryVO.setRoleName(p.getSubRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    @Override
    public PageDataResp<MemberManageQueryResp> customerList(HttpHeaders headers, MemberNameDataReq nameVO) {

        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        // 如果当前登录会员角色类型不是服务消费者
        if (!loginUser.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> condition = new ArrayList<>();
            condition.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            condition.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            condition.add(criteriaBuilder.equal(root.get("verified").as(Long.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            condition.add(criteriaBuilder.equal(root.get("subRole").get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));
            condition.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT.getCode()), criteriaBuilder.equal(root.get("subRole").get("memberType").as(Integer.class), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
            condition.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), RoleTagEnum.CUSTOMER.getCode()));
            if (StringUtils.hasText(nameVO.getMemberName())) {
                condition.add(criteriaBuilder.like(root.get("subMember").get("name").as(String.class), "%" + nameVO.getMemberName().trim() + "%"));
            }
            Predicate[] p = new Predicate[condition.size()];
            return criteriaBuilder.and(condition.toArray(p));
        };

        Pageable pageable = PageRequest.of(nameVO.getCurrent() - 1, nameVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getSubMemberId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleId(p.getSubRoleId());
            queryVO.setRoleName(p.getSubRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            queryVO.setStatusName(MemberStatusEnum.getCodeMessage(p.getStatus()));
            queryVO.setDepositTime(p.getDepositTime() == null ? "" : p.getDepositTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setLifeCycleStageName(p.getMemberLifecycleStages() == null ? "" : p.getMemberLifecycleStages().getLifecycleStagesName());
            return queryVO;
        }).collect(Collectors.toList()));
    }
}
