package com.ssy.lingxi.member.controller.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberLevelRightCreditFeign;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;
import com.ssy.lingxi.member.service.feign.IMemberLevelRightCreditFeignService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员等级（Level）、权益（Right）、信用（Credit）内部Feign服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-20
 * @ignore 不需要提交到Yapi
 */
@RestController
public class MemberLevelRightCreditFeignController implements IMemberLevelRightCreditFeign {

    @Resource
    private IMemberLevelRightCreditFeignService levelRightCreditFeignService;

    /**
     * 查询平台会员的等级、权益、信用积分（多个）
     * @param memberIdList 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    @Override
    public WrapperResp<List<MemberFeignLrcResp>> getPlatformMemberLrcBatch(@RequestBody @Valid List<Long> memberIdList) {
        return WrapperUtil.success(levelRightCreditFeignService.getPlatformMemberLrcBatch(memberIdList));
    }

    /**
     * 查询平台会员的等级、权益、信用积分
     * @param feignVO 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    @Override
    public WrapperResp<MemberFeignLrcResp> getPlatformMemberLrc(@RequestBody @Valid MemberFeignReq feignVO) {
        return WrapperUtil.success(levelRightCreditFeignService.getPlatformMemberLrc(feignVO));
    }

    /**
     * 查询下级会员的等级、权益、信用积分
     * <p> 注意：如果不是上下级会员关系，或下级会员被冻结、或未审核通过返回Null</p>
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的等级、权益、信用积分
     */
    @Override
    public WrapperResp<MemberFeignLrcResp> getMemberLrc(@RequestBody @Valid MemberFeignRelationReq relationVO) {
        return WrapperUtil.success(levelRightCreditFeignService.getMemberLrc(relationVO));
    }

    /**
     * 查询下级会员的价格权益设置
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public WrapperResp<MemberFeignRightResp> getMemberPriceRight(@RequestBody @Valid MemberFeignRelationReq relationVO) {
        return WrapperUtil.success(levelRightCreditFeignService.getMemberPriceRight(relationVO));
    }

    /**
     * 批量查询下级会员的价格权益
     * @param batchVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public WrapperResp<List<MemberFeignRightDetailResp>> batchMemberPriceRight(@RequestBody @Valid MemberFeignBatchReq batchVO) {
        return WrapperUtil.success(levelRightCreditFeignService.batchMemberPriceRight(batchVO));
    }

    /**
     * 批量查询下级会员的积分权益
     * @param batchVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public WrapperResp<List<MemberFeignRightByOrderResp>> batchMemberPriceRightForOrder(@RequestBody @Valid MemberFeignBatchReq batchVO) {
        return WrapperUtil.success(levelRightCreditFeignService.batchMemberPriceRightForOrder(batchVO));
    }

    /**
     * 批量查询下级会员的价格权益
     * @param feignRelations 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public WrapperResp<List<MemberFeignRelationRightDetailResp>> batchMemberPriceRight(@RequestBody @Valid List<MemberFeignRelationReq> feignRelations) {
        return WrapperUtil.success(levelRightCreditFeignService.batchMemberPriceRight(feignRelations));
    }

    /**
     * 查询下级会员的返现权益设置
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的返现权益
     */
    @Override
    public WrapperResp<MemberFeignRightResp> getMemberReturnRight(@RequestBody @Valid MemberFeignRelationReq relationVO) {
        return WrapperUtil.success(levelRightCreditFeignService.getMemberReturnRight(relationVO));
    }

    /**
     * （批量）查询下级会员在平台后台、上级会员下的返现权益设置
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的返现权益
     */
    @Override
    public WrapperResp<List<MemberFeignRightDetailResp>> findMemberReturnRight(@RequestBody @Valid MemberFeignRelationReq relationVO) {
        return WrapperUtil.success(levelRightCreditFeignService.findMemberReturnRight(relationVO));
    }

    /**
     * 订单完成后，计算等级和权益积分
     * @param orderVO 接口参数
     * @return 计算结果
     */
    @Override
    public WrapperResp<MemberFeignReturnRightResp> calculateMemberLrcByOrder(@RequestBody @Valid MemberFeignOrderReq orderVO) {
        return WrapperUtil.success(levelRightCreditFeignService.calculateMemberLrcByOrder(orderVO));
    }

    /**
     * 积分支付订单，校验可用信用积分、支付密码，再异步计算下级会员的权益积分
     * @param spendVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> calculateMemberUsedRightPoint(@RequestBody @Valid MemberFeignRightSpendReq spendVO) {
        levelRightCreditFeignService.calculateMemberUsedRightPoint(spendVO);
        return WrapperUtil.success();
    }

    /**
     * 售后评论成功后，计算会员信用积分
     * @param commentVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> calculateMemberAfterSaleCreditPoint(@RequestBody @Valid MemberFeignAfterSaleCommentReq commentVO) {
        levelRightCreditFeignService.calculateMemberAfterSaleCreditPoint(commentVO);
        return WrapperUtil.success();
    }

    /**
     * 根据memberLevelConfigId查询会员等级配置信息
     * @param memberLevelConfigId 接口参数
     * @return 返回结果
     */
    @Override
    public WrapperResp<List<MemberFeignLevelConfigResp>> getMemberLevelConfigBatch(@RequestBody List<Long> memberLevelConfigId) {
        return WrapperUtil.success(levelRightCreditFeignService.getMemberLevelConfigBatch(memberLevelConfigId));
    }

    /**
     * 积分抵扣订单金额，校验可用信用积分、支付密码，扣除积分
     * @param spendVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> calculateMemberDeductionRightPoint(@RequestBody @Valid MemberFeignRightDeductionReq spendVO) {
        levelRightCreditFeignService.calculateMemberDeductionRightPoint(spendVO);
        return WrapperUtil.success();
    }

    /**
     * 返还-积分抵扣订单金额的积分
     * @param returnVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> returnMemberRightPoint(@RequestBody @Valid MemberFeignRightReturnReq returnVO) {
        levelRightCreditFeignService.returnMemberRightPoint(returnVO);
        return WrapperUtil.success();
    }

    /**
     * 根据上级会员Id和上级会员角色Id，以及当前用户，查询价格权益参数设置
     * @param upperVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberFeignManageMemberCreditParameterResp> getUpperMemberCreditParameter(@RequestBody @Valid MemberFeignManageUpperMemberAndRoleReq upperVO) {
        return WrapperUtil.success(levelRightCreditFeignService.getUpperMemberCreditParameter(upperVO));
    }

    /**
     * 批量查询下级会员在上级会员下的等级
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignLevelDetailResp>> findSubMemberLevels(@RequestBody @Valid MemberFeignReq feignVO) {
        return WrapperUtil.success(levelRightCreditFeignService.findSubMemberLevels(feignVO));
    }

    /**
     * 查询会员等级配置
     * @param req 接口参数
     * @return 等级配置列表
     */
    @Override
    public WrapperResp<List<MemberFeignLevelResp>> findMemberLevelConfigs(@RequestBody @Valid MemberFeignLevelReq req) {
        return WrapperUtil.success(levelRightCreditFeignService.findMemberLevelConfigs(req));
    }

    /**
     * V3 - 订单、营销服务查询供应商信息
     * @param req 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignCalcResp>> findVendors(@RequestBody @Valid MemberFeignCalcReq req) {
        return WrapperUtil.success(levelRightCreditFeignService.findVendors(req));
    }
}
