package com.ssy.lingxi.member.model.req.comment;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 发出订单评价接口查询参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberSendTradeCommentDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -5073479492109431680L;

    /**
     * 评价星级（1-5）
     */
    private Integer star;

    /**
     * 交易时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dealTimeStart;

    /**
     * 交易时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dealTimeEnd;

    /**
     * 被评价方名称
     */
    private String subMemberName;
}
