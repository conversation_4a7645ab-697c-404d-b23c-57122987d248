package com.ssy.lingxi.member.serviceImpl.base;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.member.TokenStrategyEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.TokenContext;
import com.ssy.lingxi.component.base.model.dto.login.AccessTokenDTO;
import com.ssy.lingxi.component.base.model.dto.login.TokenRedisDTO;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.TokenUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.redis.service.IRedissonUtils;
import com.ssy.lingxi.member.config.MemberRefreshConfig;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.entity.bo.login.LoginContext;
import com.ssy.lingxi.member.entity.do_.basic.QUserDO;
import com.ssy.lingxi.member.model.dto.UserOffLineDTO;
import com.ssy.lingxi.member.service.base.IBaseTokenManageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * token相关功能管理类
 *
 * <AUTHOR>
 * @version 3.0.0
 * @date 2023/12/16
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BaseTokenManageServiceImpl implements IBaseTokenManageService {
    private final MemberRefreshConfig memberRefreshConfig;
    private final IRedisUtils redisUtils;
    private final IRedissonUtils redissonUtils;
    private final JPAQueryFactory jpaQueryFactory;

    @Override
    public TokenContext checkRefreshToken(HttpHeaders headers) {
        // 查找请求头中的token
        String accessToken = headers.getFirst(Constant.ACCESS_TOKEN);
        String refreshToken = headers.getFirst(Constant.REFRESH_TOKEN);
        String source = headers.getFirst(Constant.LOGIN_SOURCE);

        // 校验参数不能为空
        BusinessAssertUtil.notBlank(source, ResponseCodeEnum.REQUEST_HEADER_ERROR);
        BusinessAssertUtil.allNotBlank(Arrays.asList(accessToken, refreshToken).toArray(new CharSequence[0]), ResponseCodeEnum.TOKEN_EXPIRE);

        // 解析加密的token
        AccessTokenDTO accessTokenDTO = TokenUtil.analysisAccessToken(accessToken);

        // 构造token上下文
        SystemSourceEnum systemSourceEnum = SystemSourceEnum.parseInt(Integer.parseInt(source));
        TokenContext tokenContext = new TokenContext(accessTokenDTO.getMemberId(), accessTokenDTO.getRoleId(), accessTokenDTO.getUserId()
                , systemSourceEnum, TokenStrategyEnum.RENEWAL, accessTokenDTO.getRawToken());

        // 找到用户在这个端的所有refreshToken
        List<TokenRedisDTO> tokenRedisDTOList = getRefreshTokenRedisDTO(tokenContext);
        BusinessAssertUtil.notEmpty(tokenRedisDTOList, ResponseCodeEnum.TOKEN_EXPIRE);

        // 查找旧token对应存储在redis中的数据，该对象必须存在且未过期
        TokenRedisDTO tokenRedisDTO = tokenContext.findOldTokenRedisDTO();
        BusinessAssertUtil.notNull(tokenRedisDTO, ResponseCodeEnum.TOKEN_EXPIRE);
        BusinessAssertUtil.isTrue(tokenRedisDTO.getRefreshToken().equals(refreshToken), ResponseCodeEnum.TOKEN_EXPIRE);
        BusinessAssertUtil.isFalse(tokenRedisDTO.isExpired(getRefreshTokenExpireMillis(systemSourceEnum)), ResponseCodeEnum.TOKEN_EXPIRE);

        return tokenContext;
    }

    @Override
    public void distUsableToken(LoginContext loginContext) {
        TokenContext tokenContext = loginContext.getTokenContext();

        String lockKey = TokenUtil.getRefreshTokenLockKey(
                tokenContext.getMemberId(),
                tokenContext.getUserId(),
                tokenContext.getLoginSourceEnum());

        RLock lock = redissonUtils.getLock(lockKey);
        try {
            // 获取refreshToken锁，类似：Token:Lock:MemberId:UserId:source
            redissonUtils.lock(lock, 6, TimeUnit.SECONDS);

            // 执行token相关逻辑
            doDistUsableToken(loginContext);

            // 缓存数据
            saveLoginToken(loginContext).get(3L, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("token管理出错，accessTokenRedisKey：{}，rawRefreshToken：{}, refreshTokenRedisKey：{}，tokenRedisDTOList：{}, 错误原因：{}",
                    tokenContext.getAccessTokenRedisKey(),
                    tokenContext.getRawRefreshToken(),
                    tokenContext.getRefreshTokenRedisKey(),
                    JsonUtil.toJson(tokenContext.getTokenRedisDTOList()),
                    e.getMessage());
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR);
        } finally {
            redissonUtils.unlock(lock);
        }
    }

    /**
     * 开始执行token维护相关逻辑
     */
    private void doDistUsableToken(LoginContext loginContext) {
        TokenContext tokenContext = loginContext.getTokenContext();

        // 如果配置为不允许同时登录，则需要将这个账号的同端登录顶下线
        if (!memberRefreshConfig.getIsConcurrent()) {
            replaced(tokenContext);
            return;
        }

        // 是否要共用refreshToken
        if (memberRefreshConfig.getIsShare()) {
            // 复用同端refreshToken
            reusingTokens(tokenContext);
            return;
        }

        // 允许同时登录，又不需要复用token，则每次登录都生成新token
        maintainRefreshTokenRedisData(tokenContext);
    }

    /**
     * 顶人下线
     * 1.如果是重新登录，则需要把原先所有的登录清除
     * 2.如果是token续期和切换角色，则只保留旧token对应的refreshToken
     */
    private void replaced(TokenContext tokenContext) {
        // 找到用户在指定端的所有refreshToken
        getRefreshTokenRedisDTO(tokenContext);

        // 删除旧accessToken相关数据
        deleteAllAccessToken(tokenContext);

        // 如果token策略不是登录，且有传oldToken，则看oldToken是否存在
        if (!TokenStrategyEnum.LOGIN.equals(tokenContext.getTokenStrategyEnum())) {
            // 解析并复用旧token
            tokenContext.analysisOldToken();
        }

        // 重制TokenRedisDTOList
        tokenContext.resetTokenRedisDTOList();
    }

    /**
     * 共用一个token，refreshToken就一个，accessToken可以有多个
     */
    private void reusingTokens(TokenContext tokenContext) {
        List<TokenRedisDTO> tokenRedisDTOList = getRefreshTokenRedisDTO(tokenContext);

        if (TokenStrategyEnum.LOGIN.equals(tokenContext.getTokenStrategyEnum())) {
            // 如果原来已经生成，且refreshToken都是一个，则复用
            if (canReusing(tokenContext)) {
                tokenContext.addDefaultTokenRedisDTO(tokenRedisDTOList.get(0).getRefreshToken());
            } else {
                // 如果refreshToken不只一个或为0，则删掉所有旧的accessToken并重新生成
                deleteAllAccessToken(tokenContext);
                tokenContext.resetTokenRedisDTOList();
            }
        } else {
            // 如果不是登录，且原来已经生成了，则需要考虑复用策略
            filterReusingToken(tokenContext);
            tokenContext.addDefaultTokenRedisDTO();
        }

        // 过滤出已经过期的
        filterExpireToken(tokenContext);

        // 如果有数量校验，且数量已经超出，则删掉最早的token
        maintainTokenCount(tokenContext);
    }

    private boolean canReusing(TokenContext tokenContext) {
        return tokenContext.getTokenRedisDTOList()
                .stream()
                .map(TokenRedisDTO::getRefreshToken)
                .collect(Collectors.toSet())
                .size() == 1;
    }

    /**
     * refreshToken和accessToken一对一，如果有数量校验，则维持数量不超过上限
     */
    private void filterReusingToken(TokenContext tokenContext) {
        List<TokenRedisDTO> tokenRedisDTOList = tokenContext.getTokenRedisDTOList();

        // 解析oldToken
        TokenRedisDTO oldTokenRedisDTO = tokenContext.analysisOldToken();

        // 找出不是该refreshToken的数据，删除掉
        tokenContext.addWaitRemoveToken(
                tokenRedisDTOList.stream()
                        .filter(refreshTokenRedisDTO -> !refreshTokenRedisDTO.getRefreshToken().equals(oldTokenRedisDTO.getRefreshToken()))
                        .map(TokenRedisDTO::getAccessTokenRedisKey)
                        .collect(Collectors.toSet()));

        // 过滤出tokenRedisDTOList中和指定refreshToken相同的，并重置到tokenContext中
        tokenContext.setTokenRedisDTOList(
                tokenRedisDTOList.stream()
                        .filter(refreshTokenRedisDTO -> refreshTokenRedisDTO.getRefreshToken().equals(oldTokenRedisDTO.getRefreshToken()))
                        .filter(refreshTokenRedisDTO -> !refreshTokenRedisDTO.getAccessTokenRedisKey().equals(oldTokenRedisDTO.getAccessTokenRedisKey()))
                        .distinct()
                        .collect(Collectors.toList()));
    }

    /**
     * refreshToken和accessToken一对一，如果有数量校验，则维持数量不超过上限
     */
    private void maintainRefreshTokenRedisData(TokenContext tokenContext) {
        // 找到用户在这个端的所有refreshToken
        getRefreshTokenRedisDTO(tokenContext);

        // 如果不是登录，则复用旧refreshToken
        if (!TokenStrategyEnum.LOGIN.equals(tokenContext.getTokenStrategyEnum())) {
            tokenContext.analysisOldToken();
        }

        // 增加一个默认token到集合中
        tokenContext.addDefaultTokenRedisDTO();

        // 过滤出已经过期的
        filterExpireToken(tokenContext);

        // 如果有数量校验，且数量已经超出，则删掉最早的token
        maintainTokenCount(tokenContext);
    }

    /**
     * 维护token数量
     */
    private void maintainTokenCount(TokenContext tokenContext) {
        // 获取最大登录数量
        int maxLoginCount = memberRefreshConfig.getMaxLoginCount();

        // 找到用户在这个端的所有refreshToken
        List<TokenRedisDTO> tokenRedisDTOList = getRefreshTokenRedisDTO(tokenContext);

        // 如果有数量校验，且数量已经超出，则删掉最早的token
        if (maxLoginCount > 0 && tokenRedisDTOList.size() > maxLoginCount) {
            // 计算待删除的数量
            AtomicInteger deductSize = new AtomicInteger(tokenRedisDTOList.size() - maxLoginCount);

            // 循环删除
            tokenRedisDTOList.removeIf(tokenRedisDTO -> {
                // 如果待删除的数量不大于1，则不删除元素
                if (deductSize.get() <= 0) {
                    return false;
                }

                // 把删除的accessToken添加到待删除集合中
                tokenContext.addWaitRemoveToken(tokenRedisDTO.getAccessTokenRedisKey());

                // 待删除数量自减1
                deductSize.getAndDecrement();
                return true;
            });
        }
    }

    /**
     * 删除所有accessToken
     */
    private void deleteAllAccessToken(TokenContext tokenContext) {
        // 过滤出已经过期的，并设置到tokenContext中
        tokenContext.addWaitRemoveToken(
                tokenContext.getTokenRedisDTOList()
                        .stream()
                        .map(TokenRedisDTO::getAccessTokenRedisKey)
                        .collect(Collectors.toSet()));
    }

    /**
     * 过滤过期的token，并存入TokenContext
     */
    private void filterExpireToken(TokenContext tokenContext) {
        List<TokenRedisDTO> tokenRedisDTOList = tokenContext.getTokenRedisDTOList();

        // 获取accessToken过期时间
        long tokenExpireMillis = getRefreshTokenExpireMillis(tokenContext.getLoginSourceEnum());

        // 过滤出已经过期的，并设置到tokenContext中
        tokenContext.addWaitRemoveToken(
                tokenRedisDTOList.stream()
                        .filter(tokenRedisDTO -> tokenRedisDTO.isExpired(tokenExpireMillis))
                        .map(TokenRedisDTO::getAccessTokenRedisKey)
                        .collect(Collectors.toSet()));

        // 过滤出还未过期的，并重置到tokenContext中
        tokenContext.setTokenRedisDTOList(
                tokenRedisDTOList.stream()
                        .filter(tokenRedisDTO -> !tokenRedisDTO.isExpired(tokenExpireMillis))
                        .distinct()
                        .collect(Collectors.toList()));
    }

    /**
     * 保存token，删除旧accessToken，缓存accessToken与refreshToken
     */
    private CompletableFuture<Void> saveLoginToken(LoginContext loginContext) {
        TokenContext tokenContext = loginContext.getTokenContext();

        // 删除旧的token
        CompletableFuture<Void> removeOldTokenFuture = CompletableFuture.completedFuture(null);
        if (!CollectionUtils.isEmpty(tokenContext.getWaitRemoveTokenSet())) {
            removeOldTokenFuture = CompletableFuture.runAsync(
                    () -> redisUtils.keyDelBatch(new ArrayList<>(tokenContext.getWaitRemoveTokenSet()), RedisConstant.REDIS_USER_INDEX)
                    , ThreadPoolConfig.asyncDefaultExecutor);
        }

        // 缓存accessToken与登录对象
        CompletableFuture<Void> saveAccessTokenFuture = CompletableFuture.runAsync(
                () -> redisUtils.stringSet(
                        tokenContext.getAccessTokenRedisKey(),
                        JsonUtil.toJson(buildUserLoginCacheDTO(loginContext)),
                        getAccessTokenExpireSeconds(),
                        RedisConstant.REDIS_USER_INDEX)
                , ThreadPoolConfig.asyncDefaultExecutor);

        // 缓存refreshToken
        CompletableFuture<Void> saveRefreshTokenFuture = CompletableFuture.runAsync(
                () -> redisUtils.stringSet(
                        tokenContext.getRefreshTokenRedisKey(),
                        JsonUtil.toJson(tokenContext.getTokenRedisDTOList()),
                        getRefreshTokenExpireSeconds(tokenContext),
                        RedisConstant.REDIS_USER_INDEX)
                , ThreadPoolConfig.asyncDefaultExecutor);

        return CompletableFuture.allOf(removeOldTokenFuture, saveAccessTokenFuture, saveRefreshTokenFuture);
    }

    /**
     * 找到用户在指定端的所有refreshToken
     */
    private List<TokenRedisDTO> getRefreshTokenRedisDTO(TokenContext tokenContext) {
        return Optional.ofNullable(tokenContext.getTokenRedisDTOList())
                .filter(tokenRedisDTOList -> !CollectionUtils.isEmpty(tokenRedisDTOList))
                .orElseGet(() -> {
                    // 查询redis中存储的refreshToken对象
                    List<TokenRedisDTO> tokenRedisDTOList = searchRefreshTokenRedisDTOList(tokenContext.getRefreshTokenRedisKey());

                    // 设置查出的tokenRedisDTOList到TokenContext中
                    tokenContext.setTokenRedisDTOList(tokenRedisDTOList);
                    return tokenRedisDTOList;
                });
    }

    /**
     * 找到用户在指定端的所有refreshToken
     */
    private List<TokenRedisDTO> searchRefreshTokenRedisDTOList(String refreshTokenRedisKey) {
        return redisUtils.stringGetList(refreshTokenRedisKey, TokenRedisDTO.class, RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 获取refreshToken的过期时间（返回单位：秒）
     * 1.如果是登录模式，redisKey过期时间取配置的时间
     * 2.如果不是登录模式，redisKey过期时间取当前TokenRedisDTOList()中最大的创建时间 + 过期时间 - 当前时间
     */
    private long getRefreshTokenExpireSeconds(TokenContext tokenContext) {
        if (TokenStrategyEnum.LOGIN.equals(tokenContext.getTokenStrategyEnum())) {
            return getRefreshTokenExpireSeconds(tokenContext.getLoginSourceEnum());
        } else {
            return getMaxTokenCreateTime(tokenContext.getTokenRedisDTOList(), tokenContext.getLoginSourceEnum());
        }
    }

    private Long getMaxTokenCreateTime(List<TokenRedisDTO> tokenRedisDTOList, SystemSourceEnum loginSourceEnum) {
        return (getMaxTokenCreateTime(tokenRedisDTOList)
                + getRefreshTokenExpireMillis(loginSourceEnum)
                - System.currentTimeMillis()) / 1000;
    }

    private Long getMaxTokenCreateTime(List<TokenRedisDTO> tokenRedisDTOList) {
        return tokenRedisDTOList.stream()
                .map(TokenRedisDTO::getRefreshTokenCreateTime)
                .max(Long::compareTo)
                .orElse(System.currentTimeMillis());
    }

    @Override
    public long getAccessTokenExpireMillis() {
        return getAccessTokenExpireSeconds() * 1000;
    }

    @Override
    public long getAccessTokenExpireSeconds() {
        return memberRefreshConfig.getAccessTokenExpireMinutes() * 60;
    }

    @Override
    public long getRefreshTokenExpireMillis(SystemSourceEnum loginSource) {
        return getRefreshTokenExpireSeconds(loginSource) * 1000;
    }

    @Override
    public long getRefreshTokenExpireSeconds(SystemSourceEnum loginSource) {
        switch (loginSource) {
            case BUSINESS_WEB:
                return memberRefreshConfig.getPcRefreshTokenExpireMinutes() * 60;
            case BUSINESS_MANAGEMENT_PLATFORM:
                return memberRefreshConfig.getPtRefreshTokenExpireMinutes() * 60;
            case BUSINESS_MOBILE:
                return memberRefreshConfig.getAppRefreshTokenExpireMinutes() * 60;
            default:
                return 0L;
        }
    }

    /**
     * 构造登录缓存对象实体
     */
    @Override
    public UserLoginCacheDTO buildUserLoginCacheDTO(LoginContext loginContext) {
        UserLoginCacheDTO loginCacheDTO = new UserLoginCacheDTO();
        loginCacheDTO.setRelId(loginContext.getRelId());
        loginCacheDTO.setMemberId(loginContext.getMemberId());
        loginCacheDTO.setMemberName(loginContext.getMemberName());
        loginCacheDTO.setMemberRoleId(loginContext.getMemberRoleId());
        loginCacheDTO.setMemberRoleName(loginContext.getMemberRoleName());
        loginCacheDTO.setMemberRoleType(loginContext.getMemberRoleType());
        loginCacheDTO.setMemberType(loginContext.getMemberType());
        loginCacheDTO.setRoleTag(loginContext.getRoleTag());
        loginCacheDTO.setLevel(loginContext.getLevel());
        loginCacheDTO.setMemberLevelType(loginContext.getMemberLevelType());
        loginCacheDTO.setLoginSource(loginContext.getTokenContext().getLoginSourceEnum().getCode());
        loginCacheDTO.setVerified(loginContext.getVerified());
        loginCacheDTO.setUserId(loginContext.getUserId());
        loginCacheDTO.setAccount(loginContext.getAccount());
        loginCacheDTO.setUserName(loginContext.getUserName());
        loginCacheDTO.setTelCode(loginContext.getTelCode());
        loginCacheDTO.setUserPhone(loginContext.getPhone());
        loginCacheDTO.setUserEmail(loginContext.getEmail());
        loginCacheDTO.setJobTitle(loginContext.getJobTitle());
        loginCacheDTO.setOrgName(loginContext.getOrgName());
        loginCacheDTO.setUserRoleIds(loginContext.getUserRoleIds());
        loginCacheDTO.setUserRoleName(loginContext.getUserRoleName());
        loginCacheDTO.setUserType(loginContext.getUserType());
        loginCacheDTO.setLogo(loginContext.getLogo());

        return loginCacheDTO;
    }

    @Override
    public void deleteAccessToken(String accessTokenRedisKey) {
        log.info("删除accessToken为：{}的缓存", accessTokenRedisKey);
        redisUtils.keyDel(accessTokenRedisKey, RedisConstant.REDIS_USER_INDEX);
    }

    @Override
    public void deleteRefreshToken(String refreshTokenRedisKey) {
        // 从redis中查找缓存的数据
        List<TokenRedisDTO> tokenRedisDTOS = searchRefreshTokenRedisDTOList(refreshTokenRedisKey);

        // 查找所有待删除待accessToken
        Set<String> waitDeleteTokenList = tokenRedisDTOS.stream()
                .map(TokenRedisDTO::getAccessTokenRedisKey)
                .collect(Collectors.toSet());

        // 加入当前refreshToken
        waitDeleteTokenList.add(refreshTokenRedisKey);

        // 开始删除
        redisUtils.keyDelBatch(new ArrayList<>(waitDeleteTokenList), RedisConstant.REDIS_USER_INDEX);

        log.info("删除refreshToken为：{}的缓存，相关key为：{}", refreshTokenRedisKey, JsonUtil.toJson(waitDeleteTokenList));
    }

    @Override
    public void userOffline(List<Long> userIdList) {
        // 查询用户和会员相关信息
        QUserDO qUserDO = QUserDO.userDO;
        List<UserOffLineDTO> userOffLineDTOList = jpaQueryFactory.select(Projections.constructor(UserOffLineDTO.class, qUserDO.member.id, qUserDO.id, qUserDO.relType))
                .from(qUserDO)
                .where(qUserDO.id.in(userIdList))
                .fetch();

        doUserOffline(userOffLineDTOList);
    }

    @Override
    public void memberOffline(List<Long> memberIdList) {
        // 查询用户和会员相关信息
        QUserDO qUserDO = QUserDO.userDO;
        List<UserOffLineDTO> userOffLineDTOList = jpaQueryFactory.select(Projections.constructor(UserOffLineDTO.class, qUserDO.member.id, qUserDO.id, qUserDO.relType))
                .from(qUserDO)
                .where(qUserDO.member.id.in(memberIdList))
                .fetch();

        // 构造refreshTokenRedisKey
        doUserOffline(userOffLineDTOList);
    }

    private void doUserOffline(List<UserOffLineDTO> userOffLineDTOList) {
        // 构造refreshTokenRedisKey
        List<String> refreshTokenRedisKeyList = userOffLineDTOList.stream()
                .map(offlineDTO -> {
                    List<String> waitRemoveRedisKey = new ArrayList<>();
                    if (MemberRelationTypeEnum.PLATFORM.getCode().equals(offlineDTO.getRelType())) {
                        // 平台后台
                        waitRemoveRedisKey.add(TokenUtil.generateRefreshTokenRedisKey(offlineDTO.getMemberId(), offlineDTO.getUserId(), SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM));
                    } else {
                        // pc端
                        waitRemoveRedisKey.add(TokenUtil.generateRefreshTokenRedisKey(offlineDTO.getMemberId(), offlineDTO.getUserId(), SystemSourceEnum.BUSINESS_WEB));
                        // 移动端
                        waitRemoveRedisKey.add(TokenUtil.generateRefreshTokenRedisKey(offlineDTO.getMemberId(), offlineDTO.getUserId(), SystemSourceEnum.BUSINESS_MOBILE));
                    }
                    return waitRemoveRedisKey;
                }).flatMap(Collection::stream)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(refreshTokenRedisKeyList)) {
            // 获取缓存的TokenRedisDTO，从中获取到accessTokenRedisKey
            Map<String, List<TokenRedisDTO>> refreshTokenRedisDTOMap = redisUtils.stringMulGetList(refreshTokenRedisKeyList, TokenRedisDTO.class, RedisConstant.REDIS_USER_INDEX);
            List<String> accessTokenRedisKeyList = refreshTokenRedisDTOMap.values().stream().filter(Objects::nonNull).flatMap(Collection::stream).map(TokenRedisDTO::getAccessTokenRedisKey).collect(Collectors.toList());

            // 删除相关tokenRedisKey
            if (!CollectionUtils.isEmpty(accessTokenRedisKeyList)) {
                redisUtils.keyDelBatch(accessTokenRedisKeyList, RedisConstant.REDIS_USER_INDEX);
            }
            if (!CollectionUtils.isEmpty(refreshTokenRedisKeyList)) {
                redisUtils.keyDelBatch(refreshTokenRedisKeyList, RedisConstant.REDIS_USER_INDEX);
            }
        }
    }

    @Override
    public void logOut(HttpHeaders headers) {
        // 查找请求头中的token
        String accessToken = headers.getFirst(Constant.ACCESS_TOKEN);
        String source = headers.getFirst(Constant.LOGIN_SOURCE);

        // 校验参数不能为空
        BusinessAssertUtil.notBlank(source, ResponseCodeEnum.REQUEST_HEADER_ERROR);
        BusinessAssertUtil.notBlank(accessToken, ResponseCodeEnum.TOKEN_EXPIRE);

        // 解析加密的token
        AccessTokenDTO accessTokenDTO = TokenUtil.analysisAccessToken(accessToken);

        // 相关数据
        SystemSourceEnum systemSourceEnum = SystemSourceEnum.parseInt(Integer.parseInt(source));
        Long memberId = accessTokenDTO.getMemberId();
        Long userId = accessTokenDTO.getUserId();

        String oldAccessTokenRedisKey = TokenUtil.generateAccessTokenRedisKey(memberId, userId, accessTokenDTO.getRawToken());
        String refreshTokenRedisKey = TokenUtil.generateRefreshTokenRedisKey(memberId, userId, systemSourceEnum);

        String lockKey = TokenUtil.getRefreshTokenLockKey(memberId, userId, systemSourceEnum);

        // 获取分布式锁对象
        RLock lock = redissonUtils.getLock(lockKey);
        try {
            // 加锁
            redissonUtils.lock(lock, 3);

            // 查找refreshTokenRedisDTOList
            List<TokenRedisDTO> tokenRedisDTOList = searchRefreshTokenRedisDTOList(refreshTokenRedisKey);

            // 过滤出要登出的tokenRedisDTO
            TokenRedisDTO waitRemoveTokenRedisDTO = tokenRedisDTOList.stream()
                    .filter(tokenRedisDTO -> tokenRedisDTO.getAccessTokenRedisKey().equals(oldAccessTokenRedisKey))
                    .findAny()
                    .orElseThrow(() -> new BusinessException(ResponseCodeEnum.TOKEN_EXPIRE));

            // 如果存在，则从集合中删除
            tokenRedisDTOList.remove(waitRemoveTokenRedisDTO);

            // 删除oldAccessTokenRedisKey
            if (StringUtils.hasLength(oldAccessTokenRedisKey)) {
                redisUtils.keyDel(oldAccessTokenRedisKey, RedisConstant.REDIS_USER_INDEX);
            }

            // 重制RefreshTokenRedisDTOList
            if (!CollectionUtils.isEmpty(tokenRedisDTOList)) {
                redisUtils.stringSet(refreshTokenRedisKey,
                        JsonUtil.toJson(tokenRedisDTOList),
                        getMaxTokenCreateTime(tokenRedisDTOList, systemSourceEnum),
                        RedisConstant.REDIS_USER_INDEX);
            } else {
                redisUtils.keyDel(refreshTokenRedisKey, RedisConstant.REDIS_USER_INDEX);
            }
        } catch (Exception e) {
            log.error("登出失败，失败原因：{}，memberId：{}，userId：{}，oldAccessTokenRedisKey：{}，refreshTokenRedisKey：{}", e.getMessage(), memberId, userId, oldAccessTokenRedisKey, refreshTokenRedisKey);
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR);
        } finally {
            redissonUtils.unlock(lock);
        }
    }
}
