package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.branch.MemberBranchDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 会员店铺Repository
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/5/26
 */
public interface MemberBranchRepository extends JpaRepository<MemberBranchDO, Long>, JpaSpecificationExecutor<MemberBranchDO> {

    List<MemberBranchDO> findAllByMemberIdAndMemberRoleId(Long memberId, Long memberRoleId);

    boolean existsByMemberIdAndMemberRoleId(Long memberId, Long memberRoleId);

    List<MemberBranchDO> findAllByIdIn(List<Long> ids);

}
