package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.maintenance.MemberCancellationPageDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberCancellationValidateReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberCancellationPageConditionResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberCancellationPageResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberDetailBasicResp;
import org.springframework.http.HttpHeaders;

/**
 * 平台后台 - 会员维护 - 会员详情 - 基本信息服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
public interface IPlatformMemberDetailBasicService {
    /**
     * 会员详情 - 会员基本信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    PlatformMemberDetailBasicResp getMemberDetailBasic(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员注销审核列表查询下拉框内容
     * @return 操作结果
     */
    MemberCancellationPageConditionResp getCancellationPageCondition(HttpHeaders headers);

    /**
     * 会员详情 - 会员注销审核列表查询
     * @param headers HttpHeaders信息
     * @param pageReq 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberCancellationPageResp> getCancellationPage(HttpHeaders headers, MemberCancellationPageDataReq pageReq);

    /**
     * 会员详情 - 会员注销审核
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    void cancellationAuth(HttpHeaders headers, MemberCancellationValidateReq cancellationReq);
}
