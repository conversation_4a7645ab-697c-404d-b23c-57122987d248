package com.ssy.lingxi.member.serviceImpl.web.comment;

import com.ssy.lingxi.common.enums.order.OrderSourceKindEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderCommentDO;
import com.ssy.lingxi.member.model.req.comment.WaitCommentOrderQueryDataReq;
import com.ssy.lingxi.member.model.resp.comment.WaitCommentOrderPageResp;
import com.ssy.lingxi.member.repository.comment.MemberOrderCommentRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.comment.IMemberOrderCommentService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员订单评价服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/3
 */
@Service
public class MemberOrderCommentServiceImpl implements IMemberOrderCommentService {

    @Resource
    private MemberOrderCommentRepository memberOrderCommentRepository;

    @Resource
    private IBaseMemberCacheService memberCacheService;


    /**
     * 采购会员 - 待评价订单
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<WaitCommentOrderPageResp> pageBuyerWaitCommentOrder(HttpHeaders headers, WaitCommentOrderQueryDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());
        Page<MemberOrderCommentDO> pageList = memberOrderCommentRepository.findAll((Specification<MemberOrderCommentDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("buyerMemberId"), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("buyerRoleId"), loginUser.getMemberRoleId()));
            predicateList.add(cb.equal(root.get("buyerCompleteCommentStatus"), EnableDisableStatusEnum.DISABLE.getCode()));

            // 非SRM订单(SRM订单含有物料,不能评价)
            predicateList.add(cb.notEqual(root.get("orderKind").as(Integer.class), OrderSourceKindEnum.SRM.getCode()));

            if (StringUtils.isNotEmpty(pageVO.getOrderNo())) {
                predicateList.add(cb.like(root.get("orderNo"), "%" + pageVO.getOrderNo() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getDigest())) {
                predicateList.add(cb.like(root.get("digest"), "%" + pageVO.getDigest() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getMemberName())) {
                predicateList.add(cb.like(root.get("vendorMemberName"), "%" + pageVO.getMemberName() + "%"));
            }

            if (Objects.nonNull(pageVO.getCreateTimeStart())) {
                LocalDateTime startDateTime = DateTimeUtil.parseDateTime(pageVO.getCreateTimeStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), startDateTime));
            }

            if (Objects.nonNull(pageVO.getCreateTimeEnd())) {
                LocalDateTime endDateTime = DateTimeUtil.parseDateTime(pageVO.getCreateTimeEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("createTime"), endDateTime));
            }

            if (Objects.nonNull(pageVO.getOrderType())) {
                predicateList.add(cb.equal(root.get("orderType"), pageVO.getOrderType()));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<WaitCommentOrderPageResp> resultList = pageList.stream().map(e -> {
            WaitCommentOrderPageResp waitCommentOrderPageResp = new WaitCommentOrderPageResp();
            waitCommentOrderPageResp.setId(e.getId());
            waitCommentOrderPageResp.setOrderNo(e.getOrderNo());
            waitCommentOrderPageResp.setDigest(e.getDigest());
            waitCommentOrderPageResp.setMemberName(e.getVendorMemberName());
            waitCommentOrderPageResp.setCreateTime(DateTimeUtil.formatDateTime(e.getCreateTime()));
            waitCommentOrderPageResp.setTotalAmount(e.getTotalAmount());
            waitCommentOrderPageResp.setOrderType(e.getOrderType());
            waitCommentOrderPageResp.setOrderTypeName(OrderTypeEnum.getNameByCode(e.getOrderType()));
            waitCommentOrderPageResp.setInnerStatus(e.getBuyerInnerStatus());
            waitCommentOrderPageResp.setInnerStatusName("已完成");
            waitCommentOrderPageResp.setOuterStatus(e.getOuterStatus());
            waitCommentOrderPageResp.setOuterStatusName("已完成");
            waitCommentOrderPageResp.setCompleteCommentStatus(e.getBuyerCompleteCommentStatus());
            return waitCommentOrderPageResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 供应会员 - 待评价订单
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<WaitCommentOrderPageResp> pageVendorWaitCommentOrder(HttpHeaders headers, WaitCommentOrderQueryDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());
        Page<MemberOrderCommentDO> pageList = memberOrderCommentRepository.findAll((Specification<MemberOrderCommentDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("vendorMemberId"), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("vendorRoleId"), loginUser.getMemberRoleId()));
            predicateList.add(cb.equal(root.get("vendorCompleteCommentStatus"), EnableDisableStatusEnum.DISABLE.getCode()));

            // 非SRM订单(SRM订单含有物料,不能评价)
            predicateList.add(cb.notEqual(root.get("orderKind").as(Integer.class), OrderSourceKindEnum.SRM.getCode()));

            if (StringUtils.isNotEmpty(pageVO.getOrderNo())) {
                predicateList.add(cb.like(root.get("orderNo"), "%" + pageVO.getOrderNo() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getDigest())) {
                predicateList.add(cb.like(root.get("digest"), "%" + pageVO.getDigest() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getMemberName())) {
                predicateList.add(cb.like(root.get("buyerMemberName"), "%" + pageVO.getMemberName() + "%"));
            }

            if (Objects.nonNull(pageVO.getCreateTimeStart())) {
                LocalDateTime startDateTime = DateTimeUtil.parseDateTime(pageVO.getCreateTimeStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), startDateTime));
            }

            if (Objects.nonNull(pageVO.getCreateTimeEnd())) {
                LocalDateTime endDateTime = DateTimeUtil.parseDateTime(pageVO.getCreateTimeEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("createTime"), endDateTime));
            }

            if (Objects.nonNull(pageVO.getOrderType())) {
                predicateList.add(cb.equal(root.get("orderType"), pageVO.getOrderType()));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<WaitCommentOrderPageResp> resultList = pageList.stream().map(e -> {
            WaitCommentOrderPageResp waitCommentOrderPageResp = new WaitCommentOrderPageResp();
            waitCommentOrderPageResp.setId(e.getId());
            waitCommentOrderPageResp.setOrderNo(e.getOrderNo());
            waitCommentOrderPageResp.setDigest(e.getDigest());
            waitCommentOrderPageResp.setMemberName(e.getBuyerMemberName());
            waitCommentOrderPageResp.setCreateTime(DateTimeUtil.formatDateTime(e.getCreateTime()));
            waitCommentOrderPageResp.setTotalAmount(e.getTotalAmount());
            waitCommentOrderPageResp.setOrderType(e.getOrderType());
            waitCommentOrderPageResp.setOrderTypeName(OrderTypeEnum.getNameByCode(e.getOrderType()));
            waitCommentOrderPageResp.setInnerStatus(e.getVendorInnerStatus());
            waitCommentOrderPageResp.setInnerStatusName("已完成");
            waitCommentOrderPageResp.setOuterStatus(e.getOuterStatus());
            waitCommentOrderPageResp.setOuterStatusName("已完成");
            waitCommentOrderPageResp.setCompleteCommentStatus(e.getVendorCompleteCommentStatus());
            return waitCommentOrderPageResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public Integer findWaitCommentOrder(List<Long> orderIdList, Integer type, UserLoginCacheDTO loginUser) {

        List<MemberOrderCommentDO> pageList = memberOrderCommentRepository.findAll((Specification<MemberOrderCommentDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            if (Objects.equals(type, 1)) {
                //销售订单
                predicateList.add(cb.equal(root.get("vendorCompleteCommentStatus"), EnableDisableStatusEnum.DISABLE.getCode()));
                predicateList.add(cb.equal(root.get("vendorMemberId"), loginUser.getMemberId()));
                predicateList.add(cb.equal(root.get("vendorRoleId"), loginUser.getMemberRoleId()));
                predicateList.add(cb.equal(root.get("vendorCompleteCommentStatus"), EnableDisableStatusEnum.DISABLE.getCode()));
            } else {
                //采购订单
                predicateList.add(cb.equal(root.get("buyerCompleteCommentStatus"), EnableDisableStatusEnum.DISABLE.getCode()));
                predicateList.add(cb.equal(root.get("buyerMemberId"), loginUser.getMemberId()));
                predicateList.add(cb.equal(root.get("buyerRoleId"), loginUser.getMemberRoleId()));
            }
            predicateList.add(cb.in(root.get("id")).value(orderIdList));
            // 非SRM订单(SRM订单含有物料,不能评价)
            predicateList.add(cb.notEqual(root.get("orderKind").as(Integer.class), OrderSourceKindEnum.SRM.getCode()));

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        });
        return pageList.size();
    }
}
