package com.ssy.lingxi.member.model.req.comment;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 订单评价接口查询参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@Data
public class MemberTradeCommentIdsReq implements Serializable {
    private static final long serialVersionUID = 5329315749606790432L;

    /**
     * 记录id集合
     */
    @NotEmpty(message = "交易评价历史记录id不能为空")
    private List<Long> ids;
}
