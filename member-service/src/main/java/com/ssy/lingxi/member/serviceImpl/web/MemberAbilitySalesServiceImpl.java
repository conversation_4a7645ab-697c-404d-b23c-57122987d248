package com.ssy.lingxi.member.serviceImpl.web;

import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.ChannelAuthBO;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.model.req.basic.UserPageDataReq;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilitySalesService;
import com.ssy.lingxi.member.util.MemberOrganizationUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 渠道能力 - 业务员管理服务接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-04-14
 */
@Service
public class MemberAbilitySalesServiceImpl implements IMemberAbilitySalesService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberUserAuthRepository memberUserAuthRepository;

    @Resource
    private MemberUserChannelRepository memberUserChannelRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    /**
     * 分页查询已经绑定渠道的业务员列表
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSalesPageQueryResp> pageMemberSales(HttpHeaders headers, MemberSalesPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        List<MemberOrganizationDO> orgList = new ArrayList<>(memberDO.getOrgs());

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());

        Specification<UserDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            //注意这里的写法，会转换为查询member_id
            list.add(criteriaBuilder.equal(root.get("member").as(MemberDO.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("isSales").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
            list.add(criteriaBuilder.equal(root.get("userType").as(Integer.class), UserTypeEnum.NORMAL.getCode()));

            if (StringUtils.hasLength(pageVO.getAccount())) {
                list.add(criteriaBuilder.like(root.get("account").as(String.class), "%" + pageVO.getAccount().trim() + "%"));
            }

            if (StringUtils.hasLength(pageVO.getName())) {
                list.add(criteriaBuilder.like(root.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<UserDO> pageList = userRepository.findAll(specification, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(userDO -> {
            MemberSalesPageQueryResp queryVO = new MemberSalesPageQueryResp();
            queryVO.setUserId(userDO.getId());
            queryVO.setAccount(userDO.getAccount());
            queryVO.setName(userDO.getName());
            queryVO.setCountryCode(userDO.getTelCode());
            queryVO.setPhone(userDO.getPhone());
            queryVO.setMemberRoleName(userDO.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(",")));
            queryVO.setJobTitle(StringUtils.hasLength(userDO.getJobTitle()) ? userDO.getJobTitle() : "");
            queryVO.setOrgName(MemberOrganizationUtil.joinTitleToString(userDO.getOrg() == null ? 0L : userDO.getOrg().getId(), orgList));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 分页查询业务员列表
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSalesSelectQueryResp> pageSelectMemberSales(HttpHeaders headers, UserPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        List<MemberOrganizationDO> orgList = new ArrayList<>(memberDO.getOrgs());

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Specification<UserDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            //注意这里的用法，Jpa会转换为Id查询
            list.add(criteriaBuilder.equal(root.get("member").as(MemberDO.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
            list.add(criteriaBuilder.equal(root.get("userType").as(Integer.class), UserTypeEnum.NORMAL.getCode()));
            //选取还不是业务员的
            list.add(criteriaBuilder.equal(root.get("isSales").as(Integer.class), EnableDisableStatusEnum.DISABLE.getCode()));

            if (StringUtils.hasLength(pageVO.getName())) {
                list.add(criteriaBuilder.like(root.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (StringUtils.hasLength(pageVO.getOrgName())) {
                Join<Object, Object> orgJoin = root.join("org", JoinType.LEFT);
                list.add(criteriaBuilder.like(orgJoin.get("title").as(String.class), "%" + pageVO.getOrgName().trim() + "%"));
            }

            if (StringUtils.hasLength(pageVO.getJobTitle())) {
                list.add(criteriaBuilder.like(root.get("jobTitle").as(String.class), "%" + pageVO.getJobTitle().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<UserDO> pageList = userRepository.findAll(specification, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(userDO -> {
            MemberSalesSelectQueryResp queryVO = new MemberSalesSelectQueryResp();
            queryVO.setUserId(userDO.getId());
            queryVO.setPhone(userDO.getPhone());
            queryVO.setName(userDO.getName());
            queryVO.setJobTitle(StringUtils.hasLength(userDO.getJobTitle()) ? userDO.getJobTitle() : "");
            queryVO.setOrgName(MemberOrganizationUtil.joinTitleToString(userDO.getOrg() == null ? 0L : userDO.getOrg().getId(), orgList));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 新增业务员
     *
     * @param headers HttpHeader信息
     * @param idVO    接口参数
     * @return 操作结果
     */
    @Override
    public MemberSalesGetResp addMemberSales(HttpHeaders headers, MemberUserIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        UserDO userDO = userRepository.findById(idVO.getUserId()).orElse(null);
        if (userDO == null || userDO.getMember() == null || !userDO.getMember().getId().equals(loginUser.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (userDO.getIsSales()) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_IS_ALREADY_SALES);
        }

        userDO.setIsSales(Boolean.TRUE);
        userDO.getUserAuth().setChannelAuth(new ArrayList<>());
        userRepository.saveAndFlush(userDO);

        List<MemberOrganizationDO> orgList = new ArrayList<>(userDO.getMember().getOrgs());

        MemberSalesGetResp getVO = new MemberSalesGetResp();
        getVO.setUserId(userDO.getId());
        getVO.setName(userDO.getName());
        getVO.setMemberRoleName(userDO.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(",")));
        getVO.setOrgName(MemberOrganizationUtil.joinTitleToString(userDO.getOrg() == null ? 0L : userDO.getOrg().getId(), orgList));
        getVO.setCountryCode(StringUtils.hasLength(userDO.getTelCode()) ? userDO.getTelCode() : "");
        getVO.setPhone(userDO.getPhone());
        getVO.setJobTitle(StringUtils.hasLength(userDO.getJobTitle()) ? userDO.getJobTitle() : "");

        return getVO;
    }

    /**
     * 查询业务员基本信息
     *
     * @param headers HttpHeader信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public MemberSalesGetResp getMemberSales(HttpHeaders headers, MemberUserIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        UserDO userDO = userRepository.findById(idVO.getUserId()).orElse(null);
        if (userDO == null || userDO.getMember() == null || !userDO.getMember().getId().equals(loginUser.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (userDO.getIsSales() == null || userDO.getIsSales().equals(Boolean.valueOf(EnableDisableStatusEnum.DISABLE.getCode().toString()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_IS_NOT_SALES);
        }

        List<MemberOrganizationDO> orgList = new ArrayList<>(userDO.getMember().getOrgs());

        MemberSalesGetResp getVO = new MemberSalesGetResp();
        getVO.setUserId(userDO.getId());
        getVO.setName(userDO.getName());
        getVO.setMemberRoleName(userDO.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(",")));
        getVO.setOrgName(MemberOrganizationUtil.joinTitleToString(userDO.getOrg() == null ? 0L : userDO.getOrg().getId(), orgList));
        getVO.setCountryCode(StringUtils.hasLength(userDO.getTelCode()) ? userDO.getTelCode() : "");
        getVO.setPhone(userDO.getPhone());
        getVO.setJobTitle(StringUtils.hasLength(userDO.getJobTitle()) ? userDO.getJobTitle() : "");

        return getVO;
    }

    /**
     * 分页查询渠道会员列表
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSalesBindChannelQueryResp> pageChannels(HttpHeaders headers, MemberSalesSelectPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        //当前会员+角色已绑定渠道的所有下级会员relationIds
        List<MemberUserChannelDO> userChannels = memberUserChannelRepository.findByMemberIdAndRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId());
        Set<Long> relationIds = userChannels.stream().map(MemberUserChannelDO::getMemberRelationId).collect(Collectors.toSet());

        //前端选中的业务员已绑定渠道的下级会员
        Set<Long> saleManBindRelationIds = userChannels.stream().filter(channels -> pageVO.getUserId().equals(channels.getUserId())).map(MemberUserChannelDO::getMemberRelationId).collect(Collectors.toSet());

        //求已经解绑的relationIds：当前会员已经绑定的relationIds与前端传入的relationIds的差集
        saleManBindRelationIds.removeAll(pageVO.getRelationIds());
        //求绑定的relationIds(要把前端解绑的重新返回，所以这里要扣去解绑的)：当前会员绑定的下级会员的relationIds与已经解绑的relationIds的差集
        relationIds.removeAll(saleManBindRelationIds);

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            //not in 查询
            if (!CollectionUtils.isEmpty(relationIds)) {
                list.add(criteriaBuilder.not(root.get("id").in(relationIds)));
            }

            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberSalesBindChannelQueryResp queryVO = new MemberSalesBindChannelQueryResp();
            queryVO.setRelationId(relationDO.getId());
            queryVO.setMemberId(relationDO.getSubMemberId());
            queryVO.setName(relationDO.getSubMember().getName());
            queryVO.setRoleId(relationDO.getSubRoleId());
            queryVO.setRoleName(relationDO.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            queryVO.setLevel(relationDO.getLevelRight().getLevel());
            queryVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            queryVO.setCreateTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setStatus(relationDO.getStatus());
            queryVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 查询业务员信息、分页查询绑定的渠道列表
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public MemberSalesChannelQueryResp getSalesAndPageChannels(HttpHeaders headers, MemberSalesChannelPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        UserDO userDO = userRepository.findById(pageVO.getUserId()).orElse(null);
        if (userDO == null || userDO.getMember() == null || !userDO.getMember().getId().equals(loginUser.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (userDO.getIsSales() == null || userDO.getIsSales().equals(Boolean.valueOf(EnableDisableStatusEnum.DISABLE.getCode().toString()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_IS_NOT_SALES);
        }

        List<MemberOrganizationDO> orgList = new ArrayList<>(userDO.getMember().getOrgs());

        MemberSalesChannelQueryResp queryVO = new MemberSalesChannelQueryResp();
        queryVO.setUserId(userDO.getId());
        queryVO.setName(userDO.getName());
        queryVO.setMemberRoleName(userDO.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(",")));
        queryVO.setOrgName(MemberOrganizationUtil.joinTitleToString(userDO.getOrg() == null ? 0L : userDO.getOrg().getId(), orgList));
        queryVO.setCountryCode(StringUtils.hasLength(userDO.getTelCode()) ? userDO.getTelCode() : "");
        queryVO.setPhone(userDO.getPhone());
        queryVO.setJobTitle(StringUtils.hasLength(userDO.getJobTitle()) ? userDO.getJobTitle() : "");

        List<MemberUserChannelDO> channels = userDO.getUserAuth().getChannels().stream().filter(channel -> channel.getRoleId().equals(loginUser.getMemberRoleId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(channels)) {
            queryVO.setTotalCount(0L);
            queryVO.setData(new ArrayList<>());
            return queryVO;
        }

        List<Long> relationIds = channels.stream().map(MemberUserChannelDO::getMemberRelationId).collect(Collectors.toList());

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            // in 查询
            Expression<Long> idInExp = root.get("id").as(Long.class);
            list.add(idInExp.in(relationIds));

            if (StringUtils.hasLength(pageVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);

        queryVO.setTotalCount(pageList.getTotalElements());
        queryVO.setData(pageList.getContent().stream().map(relationDO -> {
            MemberSalesBindChannelQueryResp channelVO = new MemberSalesBindChannelQueryResp();
            channelVO.setRelationId(relationDO.getId());
            channelVO.setMemberId(relationDO.getSubMemberId());
            channelVO.setName(relationDO.getSubMember().getName());
            channelVO.setRoleId(relationDO.getSubRoleId());
            channelVO.setRoleName(relationDO.getSubRole().getRoleName());
            channelVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            channelVO.setLevel(relationDO.getLevelRight().getLevel());
            channelVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            channelVO.setCreateTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            channelVO.setStatus(relationDO.getStatus());
            channelVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
            return channelVO;
        }).collect(Collectors.toList()));

        return queryVO;
    }

    /**
     * 业务员绑定渠道列表
     *
     * @param headers       HttpHeader信息
     * @param bindChannelVO 接口参数
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void bindSalesChannels(HttpHeaders headers, MemberSalesBindChannelReq bindChannelVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        UserDO userDO = userRepository.findById(bindChannelVO.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (userDO.getUserAuth() == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_USER_AUTH_DOES_NOT_EXIST);
        }

        //判断下级会员与当前会员是否上下级关系
        List<MemberRelationDO> relationDOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(bindChannelVO.getRelationIds())) {
            relationDOList = relationRepository.findAllById(bindChannelVO.getRelationIds());
            if (relationDOList.size() != bindChannelVO.getRelationIds().size() || relationDOList.stream().anyMatch(relationDO -> !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId()))) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_CHANNEL_DOES_NOT_EXIST);
            }
        }

        QMemberRelationDO qMemberRelationDO = QMemberRelationDO.memberRelationDO;

        //删除被取消绑定的relationId列表：原渠道和现传入的渠道的差集
        Set<MemberUserChannelDO> userChannelDOS = userDO.getUserAuth().getChannels();
        Set<Long> oldRelationIds = userChannelDOS.stream().map(MemberUserChannelDO::getMemberRelationId).collect(Collectors.toSet());
        Set<Long> selectedRelationIds = new HashSet<>(bindChannelVO.getRelationIds());
        if (!CollectionUtils.isEmpty(userChannelDOS)) {
            //如果前端传入的下级会员列表和当前用户管理的渠道（下级会员）列表 元素相同则表示不需要处理，直接返回
            if (NumberUtil.collectionEqual(selectedRelationIds, oldRelationIds)) {
                return ;
            }

            //删除旧的渠道数据，并将relation表的userId字段置为0
            oldRelationIds.removeAll(selectedRelationIds);
            Set<MemberUserChannelDO> removeChannelDO = userChannelDOS.stream().filter(userChannelDO -> oldRelationIds.contains(userChannelDO.getMemberRelationId())).collect(Collectors.toSet());
            memberUserChannelRepository.deleteAll(removeChannelDO);
            jpaQueryFactory.update(qMemberRelationDO)
                    .where(qMemberRelationDO.id.in(oldRelationIds))
                    .set(qMemberRelationDO.userId, 0L)
                    .execute();
        }

        //求新增绑定的下级会员（要扣去原先已经存在的）：求前端传入的会员列表与原先已经绑定的会员列表的差集
        selectedRelationIds.removeAll(userChannelDOS.stream().map(MemberUserChannelDO::getMemberRelationId).collect(Collectors.toSet()));
        //添加数据
        //channelList是根据前端传入列表查询的全部需要绑定的，addChannelList是扣去已经绑定的会员
        List<MemberUserChannelDO> channelList = relationDOList.stream()
                .map(relationDO -> {
                    MemberUserChannelDO channelDO = new MemberUserChannelDO();
                    channelDO.setCreateTime(LocalDateTime.now());
                    channelDO.setMemberId(loginUser.getMemberId());
                    channelDO.setRoleId(loginUser.getMemberRoleId());
                    channelDO.setUserId(userDO.getId());
                    channelDO.setMemberRelationId(relationDO.getId());
                    channelDO.setSubMemberId(relationDO.getSubMemberId());
                    channelDO.setSubRoleId(relationDO.getSubRoleId());
                    return channelDO;
                }).collect(Collectors.toList());
        List<MemberUserChannelDO> addChannelList = channelList.stream().filter(memberRelationDO -> selectedRelationIds.contains(memberRelationDO.getMemberRelationId())).collect(Collectors.toList());
        memberUserChannelRepository.saveAll(addChannelList);

        //重新存储ChannelAuthBO对象
        userDO.getUserAuth().getChannels().addAll(addChannelList);
        userDO.getUserAuth().setChannelAuth(channelList.stream().map(channelDO -> {
            ChannelAuthBO channelAuthBO = new ChannelAuthBO();
            channelAuthBO.setRelationId(channelDO.getMemberRelationId());
            channelAuthBO.setMemberId(channelDO.getSubMemberId());
            channelAuthBO.setRoleId(channelDO.getSubRoleId());
            return channelAuthBO;
        }).collect(Collectors.toList()));

        userDO.setIsSales(Boolean.TRUE);
        userRepository.saveAndFlush(userDO);

        //将当前业务员id覆盖操作员id
        jpaQueryFactory.update(qMemberRelationDO)
                .where(qMemberRelationDO.id.in(selectedRelationIds))
                .set(qMemberRelationDO.userId, bindChannelVO.getUserId())
                .execute();

    }

    /**
     * 解除业务员与渠道的绑定
     *
     * @param headers         HttpHeader信息
     * @param unBindChannelVO 接口参数
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void unBindSalesChannel(HttpHeaders headers, MemberSalesUnBindChannelReq unBindChannelVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        UserDO userDO = userRepository.findById(unBindChannelVO.getUserId()).orElse(null);
        if (userDO == null || !userDO.getMember().getId().equals(loginUser.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        MemberUserAuthDO userAuth = userDO.getUserAuth();
        if (userAuth == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_USER_AUTH_DOES_NOT_EXIST);
        }

        List<MemberUserChannelDO> channelDOList = memberUserChannelRepository.findByMemberIdAndRoleIdAndUserIdAndMemberRelationIdIn(loginUser.getMemberId(), loginUser.getMemberRoleId(), unBindChannelVO.getUserId(), unBindChannelVO.getRelationIds());
        if (unBindChannelVO.getRelationIds().size() != channelDOList.size()) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_CHANNEL_NOT_EXIST);
        }

        userAuth.getChannels().removeIf(channelDO -> unBindChannelVO.getRelationIds().contains(channelDO.getMemberRelationId()));

        userAuth.setChannelAuth(userAuth.getChannels().stream().map(channelDO -> {
            ChannelAuthBO channelAuthBO = new ChannelAuthBO();
            channelAuthBO.setRelationId(channelDO.getMemberRelationId());
            channelAuthBO.setMemberId(channelDO.getSubMemberId());
            channelAuthBO.setRoleId(channelDO.getSubRoleId());
            return channelAuthBO;
        }).collect(Collectors.toList()));

        memberUserAuthRepository.saveAndFlush(userAuth);
        memberUserChannelRepository.deleteAll(channelDOList);

        List<Long> relIdList = channelDOList.stream().map(MemberUserChannelDO::getMemberRelationId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(relIdList)) {
            //将要删除数据的当前绑定的业务员id置为0
            QMemberRelationDO qMemberRelationDO = QMemberRelationDO.memberRelationDO;
            jpaQueryFactory.update(qMemberRelationDO)
                    .where(qMemberRelationDO.id.in(relIdList))
                    .set(qMemberRelationDO.userId, 0L)
                    .execute();
        }

    }

    /**
     * 删除业务员
     *
     * @param headers HttpHeader信息
     * @param idVO    接口参数
     * @return 操作结果
     */
    @Override
    public void deleteSales(HttpHeaders headers, MemberUserIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        UserDO userDO = userRepository.findById(idVO.getUserId()).orElse(null);
        if (userDO == null || !userDO.getMember().getId().equals(loginUser.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (userDO.getIsSales() == null || userDO.getIsSales().equals(Boolean.valueOf(EnableDisableStatusEnum.DISABLE.getCode().toString())) || userDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_IS_NOT_SALES);
        }

        MemberUserAuthDO userAuth = userDO.getUserAuth();
        if (userAuth == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_USER_AUTH_DOES_NOT_EXIST);
        }

        if (!CollectionUtils.isEmpty(userAuth.getChannels())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_SALES_CHANNEL_IS_NOT_EMPTY);
        }

        userDO.setIsSales(Boolean.FALSE);
        userRepository.saveAndFlush(userDO);

    }

    /**
     * 返回组织机构
     *
     * @param headers 头部信息
     * @return 组织机构列表
     */
    @Override
    public List<String> getMemberOrganizationList(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }
        List<MemberOrganizationDO> orgList = new ArrayList<>(memberDO.getOrgs());
        return orgList.stream().map(MemberOrganizationDO::getTitle).collect(Collectors.toList());
    }
}
