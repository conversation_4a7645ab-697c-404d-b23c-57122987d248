package com.ssy.lingxi.member.controller.web.supplier;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberScoringIndicatorGroupResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberScoringIndicatorResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberScoringTemplateDetailResp;
import com.ssy.lingxi.member.model.resp.lifecycle.StatusResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberScoringService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商能力 - 评分模板
 * <AUTHOR>
 * @since 2022/6/26 21:01
 * @version 1.0
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/supplier/scoring")
public class SupplierScoringController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.SUPPLIER.getCode();

    @Resource
    private IMemberScoringService memberScoringService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    /**
     * 模板类型下拉查询
     * @return 查询结果
     */
    @GetMapping("/template/typeList")
    public WrapperResp<List<StatusResp>> templateTypeList() {
        return WrapperUtil.success(memberScoringService.templateTypeList(roleTag));
    }

    /**
     * 指标标准定义 - 评分标准提交
     * @param headers Http头部信息
     * @param submitVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/indicator/submit")
    public WrapperResp<Void> submitScoringIndicator(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberScoringIndicatorSubmitReq submitVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberScoringService.submitScoringIndicator(loginUser, submitVO.getMemberScoringIndicatorSubmitList(), roleTag);
         return WrapperUtil.success();
    }

    /**
     * 指标标准定义 - 评分标准列表
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/indicator/page")
    public WrapperResp<List<MemberScoringIndicatorGroupResp>> pageScoringIndicator(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberScoringService.pageScoringIndicator(loginUser, roleTag));
    }

    /**
     * 指标标准定义 - 未配置评分项目
     * @param headers Http头部信息
     * @param queryVO 请求参数
     * @return 操作结果
     */
    @GetMapping("/indicator/toAssignedPage")
    public WrapperResp<List<MemberScoringIndicatorResp>> toAssignedPageScoringIndicator(@RequestHeader HttpHeaders headers, @Valid MemberScoringIndicatorPageReq queryVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberScoringService.toAssignedPageScoringIndicator(loginUser, queryVO, roleTag));
    }

    /**
     * 评分模板配置 - 新增评分模板
     * @param headers Http头部信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @PostMapping("/template/add")
    public WrapperResp<Void> addTemplate(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberScoringTemplateReq vo) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberScoringService.addTemplate(loginUser, vo, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 评分模板配置 - 停用/启用
     * @param headers Http头部信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @PostMapping("/template/startOrStop")
    public WrapperResp<Void> startOrStopTemplate(@RequestHeader HttpHeaders headers, @RequestBody @Valid ScoringTemplateStartOrStopReq vo) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberScoringService.startOrStopTemplate(loginUser, vo, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 评分模板配置 - 修改评分模板
     * @param headers Http头部信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @PostMapping("/template/update")
    public WrapperResp<Void> updateTemplate(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberScoringTemplateReq vo) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberScoringService.updateTemplate(loginUser, vo, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 评分模板配置 - 删除评分模板
     * @param headers Http头部信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @PostMapping("/template/delete")
    public WrapperResp<Void> deleteTemplate(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberScoringTemplateDeleteReq vo) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberScoringService.deleteTemplate(loginUser, vo, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 评分模板配置 - 详情
     * @param headers Http头部信息
     * @param vo 接口参数
     * @return 查询结果
     */
    @GetMapping("/template/detail")
    public WrapperResp<MemberScoringTemplateDetailResp> templateDetail(@RequestHeader HttpHeaders headers, @Valid TemplateIdReq vo) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberScoringService.templateDetail(loginUser, vo, roleTag));
    }

    /**
     * 评分模板配置 - 列表查询
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/template/page")
    public WrapperResp<List<MemberScoringTemplateDetailResp>> templatePage(@RequestHeader HttpHeaders headers, @Valid MemberScoringTemplatePageReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberScoringService.templatePage(loginUser, pageVO, roleTag));
    }
    
}