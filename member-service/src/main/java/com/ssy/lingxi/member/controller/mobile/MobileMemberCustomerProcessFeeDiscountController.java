package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.model.req.CustomerCalReq;
import com.ssy.lingxi.member.api.model.resp.MobileCustomerFeeDiscountResp;
import com.ssy.lingxi.member.service.web.ICustomerProcessFeeDiscountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * 客户工费优惠相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-26
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/customer/discount")
@Slf4j
public class MobileMemberCustomerProcessFeeDiscountController {

    @Resource
    private ICustomerProcessFeeDiscountService customerProcessFeeDiscountService;


    /**
     * 计算工费优惠
     *
     * @param customerCalReq 接口参数
     * @return 查询结果
     */
    @PostMapping("/calculateDiscount")
    public WrapperResp<List<MobileCustomerFeeDiscountResp>> calculateDiscount(@Valid @RequestBody CustomerCalReq customerCalReq) {
        return WrapperUtil.success(customerProcessFeeDiscountService.calculateDiscount(customerCalReq));
    }
}
