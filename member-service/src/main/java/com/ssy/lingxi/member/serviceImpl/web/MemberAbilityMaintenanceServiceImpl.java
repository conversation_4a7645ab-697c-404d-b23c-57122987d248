package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.util.ObjectUtil;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.commodity.api.feign.ICountryAreaFeign;
import com.ssy.lingxi.commodity.api.model.resp.support.CountryAreaResp;
import com.ssy.lingxi.common.enums.DataSourceEnum;
import com.ssy.lingxi.common.enums.member.MemberConfigTagEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.*;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.model.resp.MemberManageQueryResp;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.BusinessCategoryBO;
import com.ssy.lingxi.member.entity.bo.MemberDetailLabelBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.branch.MemberBranchDO;
import com.ssy.lingxi.member.entity.do_.detail.*;
import com.ssy.lingxi.member.entity.do_.lifecycle.QMemberLifecycleStagesDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.req.basic.MemberNameDataReq;
import com.ssy.lingxi.member.model.req.basic.ProvinceCodeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityMaintenanceMemberQueryDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordInspectPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordRectifyResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.IPayFeignService;
import com.ssy.lingxi.member.service.web.*;
import com.ssy.lingxi.member.util.RgConfigUtil;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员能力 - 会员维护相关接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
@Service
public class MemberAbilityMaintenanceServiceImpl implements IMemberAbilityMaintenanceService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private BaseMemberRuleRepository baseMemberRuleRepository;

    @Resource
    private IBaseMemberDetailService baseMemberDetailService;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseMemberQualificationService baseMemberQualificationService;

    @Resource
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    @Resource
    private IBaseMemberClassificationService baseMemberClassificationService;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private IMemberInspectService memberInspectService;

    @Resource
    private IMemberAppraisalService memberAppraisalService;

    @Resource
    private IMemberRectifyService memberRectifyService;

    @Resource
    private IMemberComplaintService memberComplaintService;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private IPayFeignService payFeignService;

    @Resource
    private IBaseSiteService siteService;

    @Resource
    private IPlatformMemberRoleRuleService roleRuleService;

    @Resource
    private ICountryAreaFeign countryAreaFeign;

    @Resource
    private MemberRegisterDetailRepository memberRegisterDetailRepository;

    @Resource
    private MemberProcessRuleRepository memberProcessRuleRepository;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private MemberLevelConfigRepository memberLevelConfigRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private CorporationRepository corporationRepository;

    @Resource
    private ActualControllerRepository actualControllerRepository;
    @Resource
    private MemberBranchRepository memberBranchRepository;

    private final List<MemberOuterStatusEnum> outerStatusEnums = Stream.of(
            MemberOuterStatusEnum.DEPOSITING,
            MemberOuterStatusEnum.DEPOSITORY_PASSED,
            MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED,
            MemberOuterStatusEnum.MODIFYING,
            MemberOuterStatusEnum.MODIFY_PASSED,
            MemberOuterStatusEnum.MODIFY_NOT_PASSED
    ).collect(Collectors.toList());

    /**
     * 获取分页查询会员列表页面中各个查询条件下拉选择框的内容
     *
     * @param headers HttpHeaders信息
     * @param loginUser 登录用户
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public MemberMaintenanceSearchConditionResp getPageCondition(HttpHeaders headers, UserLoginCacheDTO loginUser, Integer roleTag) {
        //规则：
        MemberMaintenanceSearchConditionResp conditionVO = new MemberMaintenanceSearchConditionResp();
        //内部状态
        List<DropdownItemResp> itemList = MemberInnerStatusEnum.toDropdownItemResps();
        itemList.forEach(d -> {
            if (NumberUtil.notNullOrZero(roleTag)) {
                d.setText(d.getText().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag)));
            }
        });
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setInnerStatus(itemList);

        //外部状态
        itemList = outerStatusEnums.stream().map(e -> new DropdownItemResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
        itemList.forEach(d -> {
            if (NumberUtil.notNullOrZero(roleTag)) {
                d.setText(d.getText().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag)));
            }
        });
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setOuterStatus(itemList);

        //注册来源
        itemList = MemberRegisterSourceEnum.toAbilityDropdownList();
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setSources(itemList);

        //币别
        itemList = CurrencyTypeEnum.toEditableDropdownList();
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setCurrencyType(itemList);

        //会员状态
        itemList = Arrays.stream(MemberStatusEnum.values()).sorted(Comparator.comparingInt(MemberStatusEnum::getCode)).map(e -> new DropdownItemResp(e.getCode(), e.getName())).collect(Collectors.toList());
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setStatus(itemList);

        //币别
        itemList = CurrencyTypeEnum.toEditableDropdownList();
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setCurrencyType(itemList);

        //获取是否开启saas多租户部署
        Boolean enableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        List<MemberTypeAndNameResp> memberTypeList;
        List<RoleIdAndNameResp> roleList;
        if (enableMultiTenancy) {//有开启
            //会员类型（这里返回的是Id）
            memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.subMemberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
            //会员角色（按照Id升序排序）
            roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.subMemberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
        } else {//未开启
            //会员类型（这里返回的是Id）
            memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType());
            //会员角色（按照Id升序排序）
            roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), roleTag);
        }
        memberTypeList.add(0, new MemberTypeAndNameResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setMemberTypes(memberTypeList);
        roleList.add(0, new RoleIdAndNameResp(0L, MemberStringEnum.ALL.getName()));
        conditionVO.setRoles(roleList);

        //会员等级
        List<LevelAndTagResp> levelList = baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId());
        levelList.add(0, new LevelAndTagResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setLevels(levelList);

        return conditionVO;
    }

    /**
     * 分页、模糊查询会员
     *
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @param loginUser 登录用户
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberMaintenancePageQueryResp> pageMembers(HttpHeaders headers, MemberAbilityMaintenanceMemberQueryDataReq queryVO, UserLoginCacheDTO loginUser, Integer roleTag) {
        Specification<MemberRelationDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));

            //注册开始时间
            if (StringUtils.hasLength(queryVO.getStartDate())) {
                LocalDateTime startDate = LocalDateTime.parse(queryVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), startDate));
            }

            //注册结束时间
            if (StringUtils.hasLength(queryVO.getEndDate())) {
                LocalDateTime endDate = LocalDateTime.parse(queryVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), endDate));
            }

            //内部状态
            if (NumberUtil.isNullOrZero(queryVO.getInnerStatus())) {
                list.add(criteriaBuilder.notEqual(root.get("innerStatus").as(Integer.class), MemberInnerStatusEnum.NEW.getCode()));
            } else {
                list.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), queryVO.getInnerStatus()));
            }

            //外部状态
            if (NumberUtil.isNullOrZero(queryVO.getOuterStatus())) {
                list.add(root.get("outerStatus").as(Integer.class).in(outerStatusEnums.stream().map(MemberOuterStatusEnum::getCode).collect(Collectors.toList())));
            } else {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), queryVO.getOuterStatus()));
            }


            //会员状态
            if (NumberUtil.notNullOrZero(queryVO.getStatus())) {
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), queryVO.getStatus()));
            }

            //会员角色
            if (NumberUtil.notNullOrZero(queryVO.getRoleId())) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), queryVO.getRoleId()));
            }

            //会员名称、注册来源
            if (StringUtils.hasLength(queryVO.getName()) || NumberUtil.notNullOrZero(queryVO.getSource())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                if (StringUtils.hasLength(queryVO.getName())) {
                    list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + queryVO.getName().trim() + "%"));
                }

                if (NumberUtil.notNullOrZero(queryVO.getSource())) {
                    list.add(criteriaBuilder.equal(subMemberJoin.get("source").as(Integer.class), queryVO.getSource()));
                }
            }

            // 判断是否曾经审核通过
            if (ObjectUtil.isNotEmpty(queryVO.getVerified())) {
                list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), queryVO.getVerified()));
            }

            if (!CollectionUtils.isEmpty(queryVO.getMemberConfigs()) && NumberUtil.notNullOrZero(queryVO.getRoleId())) {
                MemberRoleDO memberRoleDO = memberRoleRepository.findById(queryVO.getRoleId()).orElse(null);
                List<MemberRegisterConfigDO> memberRegisterConfigDOList = memberProcessRuleRepository
                        .findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(loginUser.getMemberId(), loginUser.getMemberRoleId(),
                                memberRoleDO, EnableDisableStatusEnum.ENABLE.getCode(), Sort.by("id").ascending()).getConfigs().stream().filter(memberConfigDO -> CommonBooleanEnum.YES.getCode().equals(memberConfigDO.getAllowSelect())).collect(Collectors.toList());
                Join<Object, Object> depositDetailsJoin = root.join("depositDetails", JoinType.LEFT);
                Join<Object, Object> depositoryDetailSelectJoin = root.join("depositoryDetailSelect", JoinType.LEFT);
                List<Predicate> memberConfigList = new ArrayList<>();

                for (Map.Entry<String, Object> entry : queryVO.getMemberConfigs().entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue() instanceof HashMap ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());
                    if (StringUtils.hasLength(value)) {
                        MemberRegisterConfigDO configDO = memberRegisterConfigDOList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
                        if (!Objects.isNull(configDO)) {
                            if (MemberConfigFieldTypeEnum.AREA.getMessage().equals(configDO.getFieldType())) {
                                RegisterAreaResp area = SerializeUtil.deserialize(value, RegisterAreaResp.class);
                                if (!Objects.isNull(area)) {
                                    String cityCode = area.getCityCode();
                                    String districtCode = area.getDistrictCode();
                                    String provinceCode = area.getProvinceCode();
                                    if (StringUtils.hasLength(provinceCode) && StringUtils.hasLength(cityCode) && StringUtils.hasLength(districtCode)) {
                                        memberConfigList.add(criteriaBuilder.and(criteriaBuilder.equal(depositDetailsJoin.get("fieldName").as(String.class), key), criteriaBuilder.equal(depositDetailsJoin.get("provinceCode").as(String.class), provinceCode), criteriaBuilder.equal(depositDetailsJoin.get("cityCode").as(String.class), cityCode), criteriaBuilder.equal(depositDetailsJoin.get("districtCode").as(String.class), districtCode)));
                                    } else if (StringUtils.hasLength(provinceCode) && StringUtils.hasLength(cityCode)) {
                                        memberConfigList.add(criteriaBuilder.and(criteriaBuilder.equal(depositDetailsJoin.get("fieldName").as(String.class), key), criteriaBuilder.equal(depositDetailsJoin.get("provinceCode").as(String.class), provinceCode), criteriaBuilder.equal(depositDetailsJoin.get("cityCode").as(String.class), cityCode)));
                                    } else if (StringUtils.hasLength(provinceCode)) {
                                        memberConfigList.add(criteriaBuilder.and(criteriaBuilder.equal(depositDetailsJoin.get("fieldName").as(String.class), key), criteriaBuilder.equal(depositDetailsJoin.get("provinceCode").as(String.class), provinceCode)));
                                    }
                                }
                            }else{
                                memberConfigList.add(criteriaBuilder.and(criteriaBuilder.like(depositoryDetailSelectJoin.get("detail").as(String.class),"%" + key + ":"+ value.trim() + "%")));
                            }
                        }
                    }
                }
                if (memberConfigList.size() > 0) {
                    Predicate[] orP = new Predicate[memberConfigList.size()];
                    list.add(criteriaBuilder.and(memberConfigList.toArray(orP)));
                }
            }

            //币别
            if (NumberUtil.notNullOrZero(queryVO.getCurrencyType())) {
                list.add(criteriaBuilder.equal(root.get("classification").get("currencyType").as(Integer.class), queryVO.getCurrencyType()));
            }

            //会员编码
            if (StringUtils.hasLength(queryVO.getCode())) {
                list.add(criteriaBuilder.like(root.get("classification").get("code").as(String.class), "%" + queryVO.getCode().trim() + "%"));
            }

            //主营品类
            List<Long> categoryId = queryVO.getCategoryId();
            if (!CollectionUtils.isEmpty(categoryId)) {
                Join<Object, Object> classificationJoin = root.join("classification", JoinType.LEFT);
                Join<Object, Object> categoriesJoin = classificationJoin.join("categories", JoinType.LEFT);
                list.add(criteriaBuilder.like(categoriesJoin.get("categoryIdList").as(String.class), "%:" + categoryId.get(categoryId.size() - 1) + "}%"));
            }

            //会员信息
            String account = queryVO.getAccount();
            if (StringUtils.hasText(account)) {
                Join<MemberRelationDO, MemberDO> classificationJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(classificationJoin.get("account").as(String.class), "%" + account.trim() + "%"));
            }

            //会员等级
            if (NumberUtil.notNullOrZero(queryVO.getLevel())) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), queryVO.getLevel()));
            }

            // 会员类型
            if(NumberUtil.notNullOrZero(queryVO.getMemberType())) {
                Join<Object, Object> memberTypeJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(memberTypeJoin.get("memberType"), queryVO.getMemberType()));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), roleTag));
            }

            // 会员ID
            if (NumberUtil.notNullOrZero(queryVO.getSubMemberId())) {
                list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), queryVO.getSubMemberId()));
            }

            // 生命周期阶段Id
            if (NumberUtil.notNullOrZero(queryVO.getLifeCycleStageId())) {
                Join<Object, Object> lifeCycleStageJoin = root.join("memberLifecycleStages", JoinType.LEFT);
                list.add(criteriaBuilder.equal(lifeCycleStageJoin.get("id").as(Long.class), queryVO.getLifeCycleStageId()));
            }

            query.groupBy(root.get("subMember")
                    , root.get("id")
                    , root.get("subRole")
                    , root.get("subRoleId")
                    , root.get("subRoleName")
                    , root.get("createTime")
                    , root.get("levelRight")
                    , root.get("status")
                    , root.get("innerStatus")
                    , root.get("outerStatus"));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(queryVO.getCurrent() - 1, queryVO.getPageSize(), Sort.by("createTime").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(spec, page);

        List<Long> subMemberIds = pageList.getContent().stream().map(MemberRelationDO::getSubMemberId).collect(Collectors.toList());
        List<CorporationDO> corporationList = corporationRepository.findAllByMemberIdIn(subMemberIds);
        Map<Long, CorporationDO> corporationDOMap = corporationList.stream().collect(Collectors.toMap(CorporationDO::getMemberId, Function.identity()));

        List<Long> actualControllerList = corporationList.stream().map(CorporationDO::getActualControllerId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, String> actualControllerMap = actualControllerRepository.findAllById(actualControllerList).stream().collect(Collectors.toMap(ActualControllerDO::getId, ActualControllerDO::getName));

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberMaintenancePageQueryResp memberVO = new MemberMaintenancePageQueryResp();
            memberVO.setMemberId(relationDO.getSubMember().getId());
            memberVO.setValidateId(relationDO.getId());
            memberVO.setSubMemberId(relationDO.getSubMemberId());
            memberVO.setCustomerType(relationDO.getSubMemberTypeEnum() == null ? MemberTypeEnum.MERCHANT.getCode() : relationDO.getSubMemberTypeEnum());
            memberVO.setName(relationDO.getSubMember().getName());
            memberVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            memberVO.setRoleId(relationDO.getSubRoleId());
            memberVO.setRoleName(relationDO.getSubRoleName());
            memberVO.setSourceName(MemberRegisterSourceEnum.getCodeMessage(relationDO.getSubMember().getSource()));
            memberVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            memberVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
            memberVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            memberVO.setStatus(relationDO.getStatus());
            memberVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
            memberVO.setInnerStatus(relationDO.getInnerStatus());
            memberVO.setInnerStatusName(SecurityStringUtil.replaceMemberPrefix(MemberInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()), roleTag));
            memberVO.setOuterStatus(relationDO.getOuterStatus());
            memberVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
            memberVO.setMemberCode(Objects.isNull(relationDO.getClassification()) ? "" : relationDO.getClassification().getCode());
            memberVO.setLifeCycleStageName(Objects.isNull(relationDO.getMemberLifecycleStages()) ? "" : relationDO.getMemberLifecycleStages().getLifecycleStagesName());
            memberVO.setLifeCycleStageId(Objects.isNull(relationDO.getMemberLifecycleStages()) ? null : relationDO.getMemberLifecycleStages().getId());
            memberVO.setDepositTime(relationDO.getDepositTime() == null ? null : relationDO.getDepositTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            memberVO.setDepositTime(relationDO.getDepositTime() == null ? null : relationDO.getDepositTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            memberVO.setOuterStatusName(NumberUtil.notNullOrZero(loginUser.getRoleTag()) ? MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()).replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(loginUser.getRoleTag()))
                    : MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));

            CorporationDO corporationDO = corporationDOMap.get(relationDO.getSubMemberId());
            if (Objects.nonNull(corporationDO)) {
                memberVO.setActualControllerId(corporationDO.getActualControllerId());
                memberVO.setActualControllerName(Optional.ofNullable(corporationDO.getActualControllerId()).map(actualControllerMap::get).orElse(null));
                memberVO.setActualControllerGroupIdentifier(corporationDO.getGroupIdentifier());
                memberVO.setCorporationName(corporationDO.getName());
            }

            Boolean enableMultiTenancy = siteService.isEnableMultiTenancy(headers);
            if (relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode()) || relationDO.getOuterStatus().equals(MemberOuterStatusEnum.MODIFY_PASSED.getCode())) {
                if (relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
                    memberVO.setShowFreeze(true);
                    memberVO.setShowUnfreeze(false);
                    //如果开启saas多租户部署，就隐藏修改按钮
                    memberVO.setShowCorrect(!enableMultiTenancy);
                    memberVO.setShowEliminate(true);
                    memberVO.setShowBlacklist(true);
                } else {
                    memberVO.setShowFreeze(false);
                    memberVO.setShowUnfreeze(relationDO.getStatus().equals(MemberStatusEnum.FROZEN.getCode()));
                    memberVO.setShowCorrect(false);
                    memberVO.setShowEliminate(false);
                    memberVO.setShowBlacklist(false);
                }
            } else {
                memberVO.setShowFreeze(false);
                memberVO.setShowUnfreeze(false);
                memberVO.setShowCorrect(false);
                memberVO.setShowEliminate(false);
                memberVO.setShowBlacklist(false);
            }

            return memberVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员处理页面 - 会员详情
     * @param loginUser 登录用户
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public MemberMaintenanceDetailResp getSubMemberDetail(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        SubMemberDetailResp subMemberDetailResp = baseMemberDetailService.getSubMemberDetail(relationDO, roleTag);

        MemberMaintenanceDetailResp detailVO = new MemberMaintenanceDetailResp();
        BeanUtils.copyProperties(subMemberDetailResp, detailVO);

        //详细信息分组内容
        detailVO.setGroups(baseMemberRegisterDetailService.groupMemberRegisterDetailText(relationDO.getSubMember(), MemberDetailVersionEnum.USING));

        //外部历史记录（即历史流转记录）
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        return detailVO;
    }

    /**
     * 会员冻结、解冻
     *
     * @param loginUser 登录用户
     * @param statusVO  接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateMemberRelationFreezeStatus(UserLoginCacheDTO loginUser, MemberFreezeStatusReq statusVO) {
        MemberRelationDO relationDO = relationRepository.findById(statusVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        relationDO.setStatus(statusVO.getStatus());
        relationRepository.saveAndFlush(relationDO);

        //内部记录
        MemberValidateHistoryOperationEnum operationEnum = statusVO.getStatus().equals(MemberStatusEnum.NORMAL.getCode()) ? MemberValidateHistoryOperationEnum.UNFREEZE_MEMBER : MemberValidateHistoryOperationEnum.FREEZE_MEMBER;
        baseMemberHistoryService.saveMemberInnerHistory(relationDO, loginUser, operationEnum, statusVO.getReason());

        //外部记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), operationEnum, MemberStatusEnum.getCodeMessage(relationDO.getStatus()), statusVO.getReason());

        //通知支付服务，冻结-解冻资金账户
        payFeignService.notifyUpdateMemberAssetAccount(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), relationDO.getRelType(), relationDO.getStatus());

    }

    /**
     * 会员处理 - 会员淘汰（解除关系）
     *
     * @param loginUser 登录用户信息
     * @param vo        接口参数
     * @return 操作结果
     */
    @Override
    public void updateMemberEliminatedStatus(UserLoginCacheDTO loginUser, MemberEliminateOrBlacklistReq vo) {
        MemberRelationDO relationDO = relationRepository.findById(vo.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        relationDO.setStatus(MemberStatusEnum.ELIMINATED.getCode());
        relationRepository.saveAndFlush(relationDO);

        String reason = (StringUtils.hasLength(vo.getDate()) ? vo.getDate() : "").concat(",").concat(vo.getReason());

        //内部记录
        baseMemberHistoryService.saveMemberInnerHistory(relationDO, loginUser, MemberValidateHistoryOperationEnum.ELIMINATE_MEMBER, reason);

        //外部记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.ELIMINATE_MEMBER, MemberStatusEnum.getCodeMessage(relationDO.getStatus()), reason);

        //通知支付服务，冻结资金账户
        payFeignService.notifyToFreezeAssetAccount(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), relationDO.getRelType());

    }

    /**
     * 会员处理 - 会员黑名单
     *
     * @param loginUser 登录用户信息
     * @param vo        接口参数
     * @return 操作结果
     */
    @Override
    public void updateMemberBlacklistStatus(UserLoginCacheDTO loginUser, MemberEliminateOrBlacklistReq vo) {
        MemberRelationDO relationDO = relationRepository.findById(vo.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        relationDO.setStatus(MemberStatusEnum.BLACK_LIST.getCode());
        relationRepository.saveAndFlush(relationDO);

        String reason = (StringUtils.hasLength(vo.getDate()) ? vo.getDate() : "").concat(",").concat(vo.getReason());

        //内部记录
        baseMemberHistoryService.saveMemberInnerHistory(relationDO, loginUser, MemberValidateHistoryOperationEnum.BLACK_LIST, reason);

        //外部记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.BLACK_LIST, MemberStatusEnum.getCodeMessage(relationDO.getStatus()), reason);

        //通知支付服务，冻结资金账户
        payFeignService.notifyToFreezeAssetAccount(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), relationDO.getRelType());

    }

    /**
     * 会员详情 - 会员基本信息
     *
     * @param headers HttpHeaders信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberMaintenanceDetailBasicResp getMemberDetailBasic(HttpHeaders headers, ValidateIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }
        MemberDO member = relationDO.getSubMember();
        MemberMaintenanceDetailBasicResp detailVO = new MemberMaintenanceDetailBasicResp();

        // 只有企业采购商需要工作流程校验，个人采购商不需要校验
        Integer memberType = relationDO.getSubMemberTypeEnum();

        Integer dataSource = member.getDataSource();
        if (DataSourceEnum.MALL.getCode().equals(dataSource) && MemberTypeEnum.MERCHANT.getCode().equals(memberType)) {

            WorkflowTaskListBO result = baseMemberValidateService.getMemberProcessSteps(relationDO);
            //内部流程
            result.getStepList().forEach(step -> {
                if (NumberUtil.notNullOrZero(roleTag)) {
                    step.setStepName(step.getStepName().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag)));
                }
            });

            detailVO.setInnerVerifySteps(result.getStepList());
            detailVO.setCurrentInnerStep(result.getCurrentStep());
            detailVO.setInnerVerifyProcessName(NumberUtil.notNullOrZero(roleTag) ? result.getProcessName().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag))
                    : result.getProcessName());
        }
        SubMemberDetailResp subMemberDetailResp = baseMemberDetailService.getSubMemberDetail(relationDO, roleTag);


        BeanUtils.copyProperties(subMemberDetailResp, detailVO);
        //外部流程
        detailVO.setOuterVerifySteps(baseMemberValidateService.getMemberDepositOuterSteps(relationDO, roleTag));
        detailVO.setCurrentOuterStep(2);



        //注册信息分组内容（优先显示入库资料，如果没有入库资料则显示注册资料）
        List<DetailTextGroupResp> depositoryDetails = baseMemberDepositDetailService.switchMemberDepositoryDetailText(relationDO);
        if (CollectionUtils.isEmpty(depositoryDetails)) {
            detailVO.setGroups(baseMemberRegisterDetailService.switchMemberRegisterDetailText(relationDO.getSubMember()));
        } else {
            detailVO.setGroups(depositoryDetails);
        }

        //外部历史记录（即历史流转记录）
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        //内部历史记录
        detailVO.setInnerHistory(baseMemberHistoryService.listMemberInnerHistory(relationDO, roleTag));

        // 是否配置会员等级
        detailVO.setConfigurationLevel(memberLevelConfigRepository.existsByMemberIdAndRoleIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleId()));

        //店铺信息
        CorporationDO corporationDO = corporationRepository.findFirstByMemberId(relationDO.getSubMemberId());
        List<MemberBranchDO> memberBranchDOS = memberBranchRepository.findAllByMemberIdAndMemberRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());
        List<MemberBranchResp> memberBranchResps = memberBranchDOS.stream().map(o -> {
            MemberBranchResp resp = new MemberBranchResp();
            BeanUtils.copyProperties(o, resp);
            if(corporationDO!=null){
                resp.setCorporationName(corporationDO.getName());
            }
            return resp;
        }).collect(Collectors.toList());
        detailVO.setMemberBranch(memberBranchResps);

        return detailVO;
    }

    /**
     * 会员详情 - 会员档案信息
     *
     * @param headers HttpHeader信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public MemberMaintenanceRecordResp getMemberRecords(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberMaintenanceRecordResp recordVO = new MemberMaintenanceRecordResp();
        recordVO.setClassification(baseMemberClassificationService.getMemberClassification(relationDO));
        recordVO.setDepositDetails(baseMemberDepositDetailService.switchMemberDepositoryDetailText(relationDO));
        recordVO.setQualities(baseMemberQualificationService.findMemberQualities(relationDO));
        return recordVO;
    }

    /**
     * 会员详情 - 会员档案 - 查询入库分类信息（修改页面）
     *
     * @param headers HttpHeader信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public MemberClassifyQueryResp getMemberClassification(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberClassificationService.findMemberClassification(relationDO);
    }

    /**
     * “修改入库分类信息” - “适用区域”-省列表
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public List<AreaCodeNameResp> getClassifyProvinces(HttpHeaders headers) {
        return AreaUtil.listProvince();
    }

    /**
     * “修改入库分类” - “适用区域”-根据省编码查询市列表
     *
     * @param headers Http头部信息
     * @param codeVO  接口参数
     * @return 查询结果
     */
    @Override
    public List<AreaCodeNameResp> getClassifyCities(HttpHeaders headers, ProvinceCodeReq codeVO) {
        return AreaUtil.listCityByProvinceCode(codeVO.getProvinceCode());
    }

    /**
     * “修改入库分类” - 品类信息 - 查询结算方式与发票类型
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public MemberClassifyCategoryItemResp getToClassifyCategoryItems(HttpHeaders headers) {
        MemberClassifyCategoryItemResp itemVO = new MemberClassifyCategoryItemResp();
        itemVO.setPayTypes(baseMemberRuleRepository.findByRuleTypeAndStatus(PlatformRuleTypeEnum.PURCHASE_CONTRACT.getCode(), EnableDisableStatusEnum.ENABLE.getCode()).stream().map(baseMemberRule -> new BusinessCategoryPayTypeResp(baseMemberRule.getMethodCode(), PurchaseContractPayTypeEnum.getNameByCode(baseMemberRule.getMethodCode()))).sorted(Comparator.comparingInt(BusinessCategoryPayTypeResp::getPayType)).collect(Collectors.toList()));
        itemVO.setInvoiceTypes(Arrays.stream(BusinessCategoryInvoiceTypeEnum.values()).map(e -> new BusinessCategoryInvoiceTypeResp(e.getCode(), e.getName())).sorted(Comparator.comparingInt(BusinessCategoryInvoiceTypeResp::getInvoiceType)).collect(Collectors.toList()));
        itemVO.setCurrencyTypes(Arrays.stream(CurrencyTypeEnum.values()).map(e -> new BusinessCurrencyTypeResp(e.getCode(), e.getMessage())).sorted(Comparator.comparingInt(BusinessCurrencyTypeResp::getCurrencyType)).collect(Collectors.toList()));
        itemVO.setSettlementDocuments(Arrays.stream(SettlementDocumentsEnum.values()).map(e -> new BusinessSettlementDocumentsTypeResp(e.getCode(), e.getMessage())).sorted(Comparator.comparingInt(BusinessSettlementDocumentsTypeResp::getSettlementDocumentsType)).collect(Collectors.toList()));
        itemVO.setPaymentTypes(Arrays.stream(PaymentTypeEnum.values()).map(e -> new BusinessPaymentTypeResp(e.getCode(), e.getName())).sorted(Comparator.comparingInt(BusinessPaymentTypeResp::getPaymentType)).collect(Collectors.toList()));
        itemVO.setAdvanceCharges(Arrays.stream(AdvanceChargeEnum.values()).map(e -> new BusinessAdvanceChargeTypeResp(e.getCode(), e.getMessage())).sorted(Comparator.comparingInt(BusinessAdvanceChargeTypeResp::getAdvanceChargeType)).collect(Collectors.toList()));
        return itemVO;
    }

    /**
     * 会员详情 - 会员档案 - 修改入库分类信息
     *
     * @param headers  HttpHeader信息
     * @param updateVO 接口参数
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateMemberClassification(HttpHeaders headers, MemberMaintenanceClassificationUpdateReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(updateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //检查并保存入库分类信息
        baseMemberClassificationService.saveMemberClassification(relationDO, updateVO.getRemark(), updateVO.getCurrencyType(), updateVO.getCode(), updateVO.getPartnerType(), updateVO.getMaxAmount(), updateVO.getAreaCodes(), updateVO.getCategories());
        relationRepository.saveAndFlush(relationDO);
    }

    /**
     * 会员详情 - 会员档案 - 分页查询考察信息
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberRecordInspectPageQueryResp> pageMemberInspect(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }
        return memberInspectService.pageMemberInspect(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMember(), relationDO.getSubRoleId(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情- 会员档案 - 分页查询考评信息
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberAppraisalPageQueryResp> pageMemberAppraisal(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }
        return memberAppraisalService.pageMemberAppraisal(relationDO.getMember(), relationDO.getRole(), relationDO.getSubMember(), relationDO.getSubRole(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员档案 - 分页查询会员整改
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberRecordRectifyResp> pageMemberRecordRectify(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }
        return memberRectifyService.pageMemberRecordRectify(relationDO.getMember(), relationDO.getRole(), relationDO.getSubMember(), relationDO.getSubRole(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员等级信息
     *
     * @param headers    Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberValidateDetailLevelResp getMemberLevelDetail(HttpHeaders headers, MemberValidateReq validateVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailLevel(relationDO);
    }

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberLevelDetailHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize(), MemberConstant.DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 会员详情 - 会员权益信息
     *
     * @param headers    Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberDetailRightResp getMemberDetailRight(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailRight(relationDO);
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberDetailRightHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize(), MemberConstant.DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益使用记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberDetailRightSpendHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize(), MemberConstant.DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 会员详情 - 会员信用信息
     *
     * @param headers    Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberDetailCreditResp getMemberDetailCredit(HttpHeaders headers, MemberValidateReq validateVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailCredit(relationDO);
    }

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     *
     * @param headers    Http头部新
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(HttpHeaders headers, MemberValidateReq validateVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailCreditTradeCommentSummary(relationDO);
    }

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberDetailCreditTradeCommentHistory(relationDO, pageVO.getStarLevel(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     *
     * @param headers    Http头部新
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(HttpHeaders headers, MemberValidateReq validateVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailCreditAfterSaleCommentSummary(relationDO);
    }

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberDetailCreditAfterSaleCommentHistory(relationDO, pageVO.getStarLevel(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     *
     * @param headers    Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(HttpHeaders headers, MemberValidateReq validateVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return memberComplaintService.summaryUpperMemberComplaints(relationDO.getMember(), relationDO.getRoleId(), relationDO.getSubMember(), relationDO.getSubRoleId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberCreditComplaintPageQueryResp> pageMemberDetailCreditComplainHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }
        return memberComplaintService.pageUpperMemberComplaint(relationDO.getMember(), relationDO.getRoleId(), relationDO.getSubMember(), relationDO.getSubRoleId(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员变更 - 分页查询会员变更记录
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDepositDetailHistoryResp> pageMemberDepositDetailHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberHistoryService.pageMemberDepositDetailHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize());
    }

    @Override
    public PageDataResp<PlatformPageQueryMemberResp> pagePlatformMembers(HttpHeaders headers, PlatformMemberQueryDataReq queryVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        //当前会员的下级会员id和下级角色id进行一对一绑定
        List<MemberRelationDO> subMemberIdAndSubRoleIds = relationRepository.findAllByMemberIdAndRoleIdAndRelType(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.OTHER.getCode()).stream().collect(Collectors.toList());
        //筛选出平台后台的下级会员已经添加为当前会员的下级会员
        List<Long> memberRelationIds = subMemberIdAndSubRoleIds.stream().map(subMemberIdAndSubRoleId -> relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(subMemberIdAndSubRoleId.getSubMemberId(), subMemberIdAndSubRoleId.getSubRoleId(), MemberRelationTypeEnum.PLATFORM.getCode()).getId()).collect(Collectors.toList());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级为平台
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            //只查询会员类型为“企业会员”和“个人会员,且还没添加为当前用户的下级会员的会员
            list.add(criteriaBuilder.in(root.get("subMemberTypeEnum")).value(Arrays.asList(MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
            if (!CollectionUtils.isEmpty(memberRelationIds)) {
                list.add(criteriaBuilder.not(criteriaBuilder.in(root.get("id")).value(memberRelationIds)));
            }
            //不显示平台后台的下级会员是当前登录会员，不管什么角色
            list.add(criteriaBuilder.notEqual(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
            //查询内外部状态都为平台审核通过的
            list.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), PlatformInnerStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode()));

            //会员角色
            if (NumberUtil.notNullOrZero(queryVO.getRoleId())) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), queryVO.getRoleId()));
            }

            //内部状态
            if (NumberUtil.notNullOrZero(queryVO.getInnerStatus())) {
                list.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), queryVO.getInnerStatus()));
            }

            //外部状态
            if (NumberUtil.notNullOrZero(queryVO.getOuterStatus())) {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), queryVO.getOuterStatus()));
            }

            //会员状态
            if (NumberUtil.notNullOrZero(queryVO.getStatus())) {
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), queryVO.getStatus()));
            }

            //注册起始时间
            if (StringUtils.hasLength(queryVO.getStartDate())) {
                LocalDateTime startDate = LocalDateTime.parse(queryVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), startDate));
            }

            //注册结束时间
            if (StringUtils.hasLength(queryVO.getEndDate())) {
                LocalDateTime endDate = LocalDateTime.parse(queryVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), endDate));
            }

            //会员名称
            Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
            if (StringUtils.hasLength(queryVO.getName())) {
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + queryVO.getName().trim() + "%"));
            }

            //注册来源
            if (NumberUtil.notNullOrZero(queryVO.getSource())) {
                list.add(criteriaBuilder.equal(subMemberJoin.get("source").as(Integer.class), queryVO.getSource()));
            }

            //会员等级
            if (NumberUtil.notNullOrZero(queryVO.getLevel())) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), queryVO.getLevel()));
            }

            //会员类型
            if (NumberUtil.notNullOrZero(queryVO.getMemberType())) {
                Join<Object, Object> memberTypeJoin2 = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(memberTypeJoin2.get("memberType"), queryVO.getMemberType()));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), roleTag));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(queryVO.getCurrent() - 1, queryVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            PlatformPageQueryMemberResp memberVO = new PlatformPageQueryMemberResp();
            memberVO.setMemberId(relationDO.getSubMemberId());
            memberVO.setUpperMemberId(relationDO.getMemberId());
            memberVO.setValidateId(relationDO.getId());
            memberVO.setRoleId(relationDO.getSubRoleId());
            memberVO.setUpperRoleId(relationDO.getRoleId());
            memberVO.setName(relationDO.getSubMember().getName());
            memberVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            memberVO.setMemberType(relationDO.getSubRole().getMemberType());
            memberVO.setTelCode(Optional.ofNullable(WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(relationDO.getSubMember().getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST)).map(CountryAreaResp::getTelCode).orElse(null));
            memberVO.setPhone(relationDO.getSubMember().getPhone());
            memberVO.setEmail(relationDO.getSubMember().getEmail());
            memberVO.setUpperRelationId(0L);// 去除了渠道功能，可能会有bug，等后期重构完善
            memberVO.setDetail(convertRegisterDetails(relationDO.getSubMember().getRegisterDetails()));
            memberVO.setRelType(relationDO.getRelType());
            memberVO.setRoleName(relationDO.getSubRole().getRoleName());
            memberVO.setSource(relationDO.getSubMember().getSource());
            memberVO.setSourceName(MemberRegisterSourceEnum.getCodeMessage(relationDO.getSubMember().getSource()));
            memberVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));

            //从关联的角色查找等级
            memberVO.setLevel(relationDO.getLevelRight().getLevel());
            memberVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            memberVO.setStatus(relationDO.getStatus());
            memberVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
            memberVO.setInnerStatus(relationDO.getInnerStatus());
            memberVO.setInnerStatusName(PlatformInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()));
            memberVO.setOuterStatus(relationDO.getOuterStatus());
            memberVO.setOuterStatusName(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));
            return memberVO;
        }).collect(Collectors.toList()));
    }

    //转换会员的注册信息
    private Map<String, Object> convertRegisterDetails(Set<MemberRegisterDetailDO> registerDetails) {
        return registerDetails.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(memberRegisterDetailDO -> memberRegisterDetailDO.getMemberConfig().getFieldName()))), ArrayList::new)).stream()
                .collect(Collectors.toMap(memberRegisterDetailDO -> memberRegisterDetailDO.getMemberConfig().getFieldName(), registerDetail -> {
                    if ("select".equals(registerDetail.getFieldType()) || "radio".equals(registerDetail.getFieldType())) {
                        return registerDetail.getLabels().stream().findFirst().map(MemberDetailLabelBO::getLabelId).orElse(null);
                    } else if ("checkbox".equals(registerDetail.getFieldType())) {
                        return registerDetail.getLabels().stream().map(MemberDetailLabelBO::getLabelId).collect(Collectors.toList());
                    } else if ("area".equals(registerDetail.getFieldType())) {
                        Map areaMap = new HashMap(16);
                        areaMap.put("cityCode", registerDetail.getCityCode());
                        areaMap.put("districtCode", registerDetail.getDistrictCode());
                        areaMap.put("provinceCode", registerDetail.getProvinceCode());
                        return areaMap;
                    }
                    return registerDetail.getDetail();
                }));
    }

    /**
     * 根据会员id和角色id，查询会员角色为服务提供者的下级会员列表
     *
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageQueryResp> subOrdinateMemberList(HttpHeaders headers, MemberNameDataReq nameVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            if (StringUtils.hasLength(nameVO.getMemberName())) {
                list.add(criteriaBuilder.like(root.get("subMember").get("name").as(String.class), "%" + nameVO.getMemberName() + "%"));
            }
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        Pageable page = PageRequest.of(nameVO.getCurrent() - 1, nameVO.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> relationDOList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(relationDOList.getTotalElements(), relationDOList.getContent().stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getSubMemberId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleId(p.getSubRoleId());
            queryVO.setRoleName(p.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 查询当前会员可查询的会员注册资料信息
     *
     * @return 查询结果
     */
    @Override
    public List<MemberConfigResp> registerDetailByAllowSelect(HttpHeaders headers, RoleIdReq roleIdReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        // 查询入库资料
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(roleIdReq.getRoleId()).orElse(null);
        MemberProcessRuleDO processRuleDO = memberProcessRuleRepository
                .findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(loginUser.getMemberId(), loginUser.getMemberRoleId(),
                        memberRoleDO, EnableDisableStatusEnum.ENABLE.getCode(), Sort.by("id").ascending());

        if (Objects.isNull(processRuleDO) || CollectionUtils.isEmpty(processRuleDO.getConfigs())) {
            return new ArrayList<>();
        }

        return processRuleDO.getConfigs().stream().filter(c -> CommonBooleanEnum.YES.getCode().equals(c.getAllowSelect())).map(c -> {
            MemberConfigResp detail = new MemberConfigResp();
            detail.setFieldName(c.getFieldName());
            detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c));
            detail.setFieldType(c.getFieldType());
            detail.setFieldEnum(CollectionUtils.isEmpty(c.getLabels()) ? new ArrayList<>() : c.getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));
            getFieldEnum(detail, c);
            return detail;
        }).collect(Collectors.toList());
    }

    /**
     * 分页、模糊查询平台会员信息列表 - 会员发现
     * @param headers HttpHeaders信息
     * @param memberNameReq 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDiscoverQueryResp> queryMembersByCategory(HttpHeaders headers, MemberNameDataReq memberNameReq, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Pageable page = PageRequest.of(memberNameReq.getCurrent() - 1, memberNameReq.getPageSize());
        String memberName = "";

        if (StringUtils.hasLength(memberNameReq.getMemberName())) {
            memberName = "%" + memberNameReq.getMemberName() + "%";
        }

        Page<Map<String, Object>> pageList = relationRepository.findMembersByCategory(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode(),
                MemberInnerStatusEnum.VERIFY_PASSED.getCode(), PlatformInnerStatusEnum.VERIFY_PASSED.getCode(),  MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode(), MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode(), roleTag,
                memberName, page);

        List<Long> relationIds = new ArrayList<>();
        pageList.getContent().forEach(map -> {
          if (Objects.nonNull(map.get("id"))) {
              relationIds.add(Long.valueOf(map.get("id").toString()));
          }
        });

        List<MemberRelationDO> relationList = relationRepository.findByIdIn(relationIds);
        List<MemberDiscoverQueryResp> collect = relationList.stream().map(relationDO -> {
            MemberDiscoverQueryResp memberDiscoverQueryResp = new MemberDiscoverQueryResp();
            MemberDO member = relationDO.getSubMember();
            memberDiscoverQueryResp.setLogo(member.getLogo());
            memberDiscoverQueryResp.setMemberName(member.getName());
            memberDiscoverQueryResp.setMemberId(member.getId());
            memberDiscoverQueryResp.setRoleId(relationDO.getSubRoleId());
            String mainBusiness = Optional.ofNullable(relationDO.getClassification()).map(MemberClassificationDO::getCategories).orElse(new HashSet<>())
                    .stream().map(businessCategory ->
                            businessCategory.getCategories().stream().map(BusinessCategoryBO::getName).distinct().collect(Collectors.joining("|"))
                    ).distinct().collect(Collectors.joining("|"));
            memberDiscoverQueryResp.setMainBusiness(mainBusiness);
            List<MemberRegisterDetailDO> registerDetailList = memberRegisterDetailRepository.findByMember(member);
            List<MemberRegisterDetailDO> registerDetails = new ArrayList<>();
            if (!CollectionUtils.isEmpty(registerDetailList)) {
                registerDetails = registerDetailList.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(registerDetails)) {
                    registerDetails = registerDetailList.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());
                }
            }
            memberDiscoverQueryResp.setRegisteredCapital(registerDetails.stream().filter(register -> MemberConfigTagEnum.REGISTERED_CAPITAL.getTagName().equals(register.getMemberConfig().getFieldName())).map(MemberRegisterDetailDO::getDetail).findFirst().orElse(""));
            memberDiscoverQueryResp.setBusiness(registerDetails.stream().filter(register -> MemberConfigTagEnum.BUSINESS.getTagName().equals(register.getMemberConfig().getFieldName())).map(MemberRegisterDetailDO::getDetail).findFirst().orElse(""));
            memberDiscoverQueryResp.setRegisterArea(registerDetails.stream().filter(register -> MemberConfigTagEnum.REGISTER_ADDRESS.getTagName().equals(register.getMemberConfig().getFieldName())).map(MemberRegisterDetailDO::getDetail).findFirst().orElse(""));
            return memberDiscoverQueryResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), collect);
    }

    /**
     * 统计各个生命周期下级会员人数
     *
     * @param loginUser 当前登录用户
     * @param roleTag   角色标签
     * @return 返回列表
     */
    @Override
    public List<MemberLifeCycleStatisticDetailResp> statisticMemberLifeCycleCount(UserLoginCacheDTO loginUser, Integer roleTag) {
        // 查看当前登录人配置的生命周期
        QMemberRelationDO qMemberRelationDO = QMemberRelationDO.memberRelationDO;
        QMemberLifecycleStagesDO qMemberLifecycleStagesDO = QMemberLifecycleStagesDO.memberLifecycleStagesDO;

        JPAQuery<MemberLifeCycleStatisticDetailResp> jpaQuery = jpaQueryFactory.select(Projections.constructor(MemberLifeCycleStatisticDetailResp.class, qMemberLifecycleStagesDO.id, qMemberLifecycleStagesDO.lifecycleStagesName, qMemberRelationDO.id.count().as("count")))
                .from(qMemberLifecycleStagesDO)
                .leftJoin(qMemberRelationDO).on(qMemberLifecycleStagesDO.id.eq(qMemberRelationDO.memberLifecycleStages.id))
                .where(qMemberLifecycleStagesDO.memberId.eq(loginUser.getMemberId()))
                .where(qMemberLifecycleStagesDO.roleId.eq(loginUser.getMemberRoleId()))
                .where(qMemberLifecycleStagesDO.roleTag.eq(roleTag))
                .groupBy(qMemberLifecycleStagesDO.id)
                .orderBy(qMemberLifecycleStagesDO.lifecycleStagesNum.asc())
                .fetchAll();
        return jpaQuery.fetchResults().getResults();
    }

    /**
     * 查询注册资料的标签
     *
     * @param detailDO 注册资料
     */
    private void getFieldEnum(MemberConfigResp detailDO, MemberRegisterConfigDO c) {
        //只有单选，多选，下拉单选才需要查询可选择的标签
        if ("select".equals(detailDO.getFieldType()) || "radio".equals(detailDO.getFieldType()) || "checkbox".equals(detailDO.getFieldType())) {
            detailDO.setFieldEnum(c.getLabels().stream().map(l -> {
                ConfigDetailLabelResp configDetailLabelResp = new ConfigDetailLabelResp();
                configDetailLabelResp.setValue(l.getId());
                configDetailLabelResp.setLabel(RgConfigUtil.getLabelValue(l));
                return configDetailLabelResp;
            }).collect(Collectors.toList()));
        }
    }
}
