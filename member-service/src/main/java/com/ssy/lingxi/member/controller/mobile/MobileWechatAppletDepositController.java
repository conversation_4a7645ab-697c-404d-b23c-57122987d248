package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.dataauth.annotation.member.MemberAuth;
import com.ssy.lingxi.member.model.req.basic.ProvinceCodeReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.validate.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilityDepositService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商能力 - 供应商入库审核相关接口（App）
 * <AUTHOR>
 * @since 2022/5/24 18:02
 */

@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/deposit")
public class MobileWechatAppletDepositController {

    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IMemberAbilityDepositService memberAbilityDepositService;


    /**
     * 获取供应商审核入库各个步骤分页查询列表页面下拉框
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/page/conditions")
    public WrapperResp<MemberDepositSearchConditionResp> getDepositPageConditions(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.getDepositPageConditions(loginUser, roleTag));
    }

    /**
     * 分页查询“待审核入库资料”供应商列表
     * @param headers Http头部新
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/verify/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToVerifyDepositoryDetail(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToVerifyDepositoryDetail(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核入库资料” - 查询供应商详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/verify/detail")
    public WrapperResp<MemberToVerifyDepositDetailResp> getToVerifyDepositoryDetail(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToVerifyDepositoryDetail(loginUser, idVO, roleTag));
    }


    /**
     * 分页查询“待审核入库资质”供应商列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/qualify/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToVerifyDepositoryQualification(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToVerifyDepositoryQualification(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核入库资质” - 查询供应商详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/qualify/detail")
    public WrapperResp<MemberDepositDetailResp> getToVerifyDepositoryQualification(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToVerifyDepositoryQualification(loginUser, idVO, roleTag));
    }


    /**
     * 分页查询“待入库考察”供应商列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/inspect/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToInspectDepository(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToInspectDepository(loginUser, pageVO, roleTag));
    }

    /**
     * “待入库考察” - 查询供应商详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/inspect/detail")
    public WrapperResp<MemberDepositDetailResp> getToInspectDepository(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToInspectDepository(loginUser, idVO, roleTag));
    }

    /**
     * 分页查询“待入库分类”供应商列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/classify/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToClassifyDepository(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToClassifyDepository(loginUser, pageVO, roleTag));
    }

    /**
     * “待入库分类” - 查询供应商详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/classify/detail")
    public WrapperResp<MemberToClassifyDetailResp> getToClassifyDepository(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToClassifyDepository(loginUser, idVO, roleTag));
    }

    /**
     * 分页查询供应商列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/grade/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToDepositGrade(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToDepositGrade(headers, pageVO, loginUser));
    }

    /**
     * 分页查询“待审核入库(一级)”供应商列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/grade/one/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToDepositGradeOne(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToDepositGradeOne(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核入库(一级)” - 查询供应商详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/grade/one/detail")
    public WrapperResp<MemberDepositGradeDetailResp> getToDepositGradeOne(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToDepositGradeOne(loginUser, idVO, roleTag));
    }

    /**
     * 分页查询“待审核入库(二级)”供应商列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/grade/two/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToDepositGradeTwo(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToDepositGradeTwo(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核入库(二级)” - 查询供应商详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/grade/two/detail")
    public WrapperResp<MemberDepositGradeDetailResp> getToDepositGradeTwo(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToDepositGradeTwo(loginUser, idVO, roleTag));
    }

    /**
     * 分页查询“待确认入库”供应商列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/confirm/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToConfirmDepository(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToConfirmDepository(loginUser, pageVO, roleTag));
    }

    /**
     * “待确认入库” - 查询供应商详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/confirm/detail")
    public WrapperResp<MemberDepositGradeDetailResp> getToConfirmDepository(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToConfirmDepository(loginUser, idVO, roleTag));
    }

    /**
     * “待审核入库资料” - 审核供应商
     * @param headers Http头部信息
     * @param depositVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/verify")
    public WrapperResp<Void> toVerifyDepositoryDetail(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberToVerifyDepositReq depositVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityDepositService.toVerifyDepositoryDetail(loginUser, depositVO, roleTag);
        return WrapperUtil.success();
    }


    /**
     * “待审核入库资质” - 审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/qualify")
    public WrapperResp<Void> toVerifyDepositoryQualification(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateAgreeReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityDepositService.toVerifyDepositoryQualification(loginUser, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待入库考察” - 审核
     * @param headers Http头部信息
     * @param depositVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/inspect")
    public WrapperResp<Void> toInspectDepository(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberToInspectDepositReq depositVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityDepositService.toInspectDepository(loginUser, depositVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待入库分类” - 审核
     * @param headers Http头部信息
     * @param depositVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/classify")
    public WrapperResp<Void> toClassifyDepository(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberToClassifyDepositReq depositVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityDepositService.toClassifyDepository(loginUser, depositVO, roleTag);
        return WrapperUtil.success();
    }


    /**
     * “待审核入库(一级)” - 审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/grade/one")
    public WrapperResp<Void> toDepositGradeOne(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateAgreeReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityDepositService.toDepositGradeOne(loginUser, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待审核入库(二级)” - 审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/grade/two")
    public WrapperResp<Void> toDepositGradeTwo(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateAgreeReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityDepositService.toDepositGradeTwo(loginUser, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待确认入库” - 审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/confirm")
    public WrapperResp<Void> toConfirmDepository(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateAgreeReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityDepositService.toConfirmDepository(loginUser, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待入库分类” - “适用区域”-省列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/classify/province")
    public WrapperResp<List<AreaCodeNameResp>> getToClassifyProvinces(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityDepositService.getToClassifyProvinces(headers));
    }

    /**
     * “待入库分类” - 品类信息 - 查询结算方式，发票类型，币别，结算单据，付款方式
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/classify/category/items")
    public WrapperResp<MemberClassifyCategoryItemResp> getToClassifyCategoryItems(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityDepositService.getToClassifyCategoryItems(headers));
    }

    /**
     * “待入库分类” - “适用区域”-根据省编码查询市列表
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/classify/city")
    public WrapperResp<List<AreaCodeNameResp>> getToClassifyCities(@RequestHeader HttpHeaders headers, @Valid ProvinceCodeReq codeVO) {
        return WrapperUtil.success(memberAbilityDepositService.getToClassifyCities(headers, codeVO));
    }

}
