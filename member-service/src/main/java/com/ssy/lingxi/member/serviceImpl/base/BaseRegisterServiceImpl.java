package com.ssy.lingxi.member.serviceImpl.base;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.commodity.api.feign.ICountryAreaFeign;
import com.ssy.lingxi.commodity.api.model.resp.support.CountryAreaResp;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.DataSourceEnum;
import com.ssy.lingxi.common.model.req.api.member.CorporationSyncReq;
import com.ssy.lingxi.common.model.req.api.member.CustomerSyncReq;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.config.SmsConfig;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.*;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.rest.config.EosApiProperties;
import com.ssy.lingxi.member.config.MemberRefreshConfig;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.constant.MemberRegisterDetailConfigConstant;
import com.ssy.lingxi.member.entity.bo.AddMemberBO;
import com.ssy.lingxi.member.entity.bo.ProcessBO;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import com.ssy.lingxi.member.entity.do_.detail.QMemberRegisterDetailDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberReceiveInvitationDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberSendInvitationDO;
import com.ssy.lingxi.member.enums.InvitationCodeStateEnum;
import com.ssy.lingxi.member.enums.MemberInviteTypeEnum;
import com.ssy.lingxi.member.enums.MemberRegisterSourceEnum;
import com.ssy.lingxi.member.enums.PlatformInnerStatusEnum;
import com.ssy.lingxi.member.handler.listener.event.RedisKeyRemoveEvent;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.login.ResetPasswordByEmailCodeReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordBySmsCodeReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterResultResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterTypeMenuResp;
import com.ssy.lingxi.member.model.resp.login.MultiAccInfoResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.feign.ISmsFeignService;
import com.ssy.lingxi.member.service.web.IMemberReceiveInvitationService;
import com.ssy.lingxi.member.util.MailUtil;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import com.ssy.lingxi.member.model.dto.commission.MemberRegistrationSuccessDTO;
import com.ssy.lingxi.common.constant.mq.MemberMqConstant;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.common.util.SerializeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员注册相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-03
 */
@Slf4j
@Service
public class BaseRegisterServiceImpl implements IBaseRegisterService {
    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private ICountryAreaFeign countryAreaFeign;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private IBasePlatformProcessService basePlatformProcessService;

    @Resource
    private IBaseMemberInnerService memberInnerService;

    @Resource
    private ISmsFeignService smsFeignService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private MemberSendInvitationRepository memberSendInvitationRepository;

    @Resource
    private IMemberReceiveInvitationService memberReceiveInvitationService;

    @Resource
    private IMessageFeignService messageFeignService;

    @Resource
    private MemberRefreshConfig memberRefreshConfig;

    @Resource
    private SmsConfig smsConfig;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    @Resource
    private CorporationRepository corporationRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberRegisterDetailRepository memberRegisterDetailRepository;

    @Resource
    private MemberDepositoryDetailRepository memberDepositoryDetailRepository;

    @Resource
    private IMqUtils mqUtils;

    @Resource
    private EosApiProperties eosApiProperties;

    /**
     * 检查手机号码是否被注册使用
     *
     * @param phoneReq 手机号码
     */
    @Override
    public void checkPhoneRegistered(PhoneReq phoneReq) {
        UserDO userDO = userRepository.findFirstByPhoneAndRelType(phoneReq.getPhone(), MemberRelationTypeEnum.OTHER.getCode());

        if(userDO != null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }
    }

    /**
     * 移动端 - 手机找回密码页面，检查手机号码是否被注册使用
     *
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    @Override
    public void checkPhoneExistsByMobile(PhoneReq phoneReq) {
        String phoneResult = checkCountryPhone(phoneReq.getTelCode(), phoneReq.getPhone(), false);

        BusinessAssertUtil.isTrue(userRepository.existsByRelTypeAndPhone(MemberRelationTypeEnum.OTHER.getCode(), phoneResult), ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
    }

    /**
     * 检查邮箱是否被注册使用
     *
     * @param emailReq 邮箱
     * @return 检查结果
     */
    @Override
    public void checkEmailRegistered(EmailReq emailReq) {
        //查询邮箱是否已经存在
        Specification<UserDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("email").as(String.class), emailReq.getEmail()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        if(userRepository.count(specification) > 0) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
        }

    }

    /**
     * 校验邀请码是否存在
     *
     * @param invitationCodeReq 邀请码
     * @return 检查结果
     */
    @Override
    public void checkInvitationCodeExists(InvitationCodeReq invitationCodeReq) {
        boolean exists = userRepository.existsByInvitationCode(invitationCodeReq.getInvitationCode());
        if (!exists) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_INVITATION_CODE_NOT_EXISTS);
        }
    }

    /**
     * 发送注册时短信验证码（无滑块验证）
     *
     * @param phoneReq 手机号码
     * @return 发送结果
     */
    @Override
    public void sendRegisterSmsCode(PhoneReq phoneReq) {
        String phoneResult = checkCountryPhone(phoneReq.getTelCode(), phoneReq.getPhone(), false);
        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.REGISTER_BY_PHONE_REDIS_KEY_PREFIX + phoneResult;
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendRegisterSms(phoneReq.getTelCode(), phoneResult, smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 发送注册时短信验证码
     *
     * @param phoneVO 手机号码
     * @return 发送结果
     */
    @Override
    public void sendRegisterSmsCode(SmsPhoneReq phoneVO) {
        String phoneResult = checkCountryPhone(phoneVO.getTelCode(), phoneVO.getPhone(), false);

        //校验滑块数据
        checkCaptcha(phoneVO.getImgId(), phoneVO.getWidth());

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.REGISTER_BY_PHONE_REDIS_KEY_PREFIX + phoneResult;
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendRegisterSms(phoneVO.getTelCode(), phoneResult, smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 发送手机号找回密码时的短信验证码
     *
     * @param phoneVO 手机号码
     * @return 发送结果
     */
    @Override
    public void sendResetPasswordSmsCode(SmsPhoneReq phoneVO) {
        String phoneResult = checkCountryPhone(phoneVO.getTelCode(), phoneVO.getPhone(), true);

        //校验滑块数据
        checkCaptcha(phoneVO.getImgId(), phoneVO.getWidth());

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.RESET_PWD_BY_PHONE_REDIS_KEY_PREFIX + phoneResult;
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendResetPasswordSms(phoneVO.getTelCode(), phoneResult, smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * App - 发送手机号找回密码时的短信验证码
     *
     * @param phoneReq 手机号码
     * @return 发送结果
     */
    @Override
    public void sendMobileResetPasswordSmsCode(PhoneReq phoneReq) {
        String phoneResult = checkCountryPhone(phoneReq.getTelCode(), phoneReq.getPhone(), true);

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.RESET_PWD_BY_PHONE_REDIS_KEY_PREFIX + phoneResult;
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        smsFeignService.sendResetPasswordSms(phoneReq.getTelCode(), phoneResult, smsCode);
        memberCacheService.setString(cacheKey, smsCode, smsConfig.getExpireSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 校验手机号找回密码时的短信验证码是否正确
     *
     * @param phoneSmsReq 手机号码、验证码
     * @return 发送结果
     */
    @Override
    public List<MultiAccInfoResp> checkResetPasswordSmsCode(PhoneSmsReq phoneSmsReq) {
        String phoneResult = checkCountryPhone(phoneSmsReq.getTelCode(), phoneSmsReq.getPhone(), false);

        // 如果配置的是无需校验，则跳过
        if(memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.RESET_PWD_BY_PHONE_REDIS_KEY_PREFIX + phoneResult;
            String smsCode = memberCacheService.getString(cacheKey);
            if(!StringUtils.hasLength(smsCode) || !smsCode.equals(phoneSmsReq.getSmsCode())){
                throw new BusinessException(ResponseCodeEnum.MC_MS_SMS_CODE_EXPIRE_OR_MISMATCH);
            }
        }

        List<UserDO> userDOList = userRepository.findAllByPhoneAndRelType(phoneResult, MemberRelationTypeEnum.OTHER.getCode());
        BusinessAssertUtil.notEmpty(userDOList, ResponseCodeEnum.LS_DATA_PHONE_VALIDATE);

        // 构建返回数据
        return userDOList.stream()
                .map(userDO -> new MultiAccInfoResp(userDO.getMember().getId(), userDO.getId(), userDO.getMember().getName(), userDO.getMember().getLogo()))
                .sorted(Comparator.comparing(MultiAccInfoResp::getMemberId))
                .collect(Collectors.toList());
    }

    /**
     * 根据短信验证码重设密码
     *
     * @param codeReq 接口参数
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPasswordBySmsCode(ResetPasswordBySmsCodeReq codeReq) {
        if (memberRefreshConfig.getCheckSmsCode()) {
            String cacheKey = MemberRedisConstant.RESET_PWD_BY_PHONE_REDIS_KEY_PREFIX + codeReq.getPhone();
            String smsCode = memberCacheService.getString(cacheKey);
            if (!StringUtils.hasLength(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!codeReq.getSmsCode().equals(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Collections.singletonList(cacheKey)));
        }

        // 查找用户列表
        List<UserDO> userDOList = userRepository.findAllByPhoneAndRelType(codeReq.getPhone(), MemberRelationTypeEnum.OTHER.getCode());
        BusinessAssertUtil.notEmpty(userDOList, ResponseCodeEnum.LS_DATA_PHONE_VALIDATE);

        if (!CollectionUtils.isEmpty(codeReq.getUserIdList())) {
            userDOList = userDOList.stream().filter(userDO -> codeReq.getUserIdList().stream().anyMatch(userIdReq -> userIdReq.equals(userDO.getId()))).collect(Collectors.toList());
        } else if (userDOList.size() > 1) {
            throw new BusinessException(ResponseCodeEnum.MULTI_ACCOUNT_SPECIFIED_NOT_FOUND);
        }

        for (UserDO userDO : userDOList) {
            userDO.setPassword(PasswordUtil.tryEncrypt(codeReq.getPassword()));
            userDO.setLastModifyPwdTime(LocalDateTime.now());
            userRepository.save(userDO);
        }
    }

    /**
     * 发送邮箱找回密码时的邮件
     *
     * @param fromMobile 是否来自移动客户端（如果是，则要判断有无服务消费者角色）
     * @param emailReq    邮箱地址
     * @return 发送结果
     */
    @Override
    public void sendResetPasswordEmail(EmailReq emailReq, Boolean fromMobile) {
        UserDO userDO = userRepository.findFirstByEmailAndRelType(emailReq.getEmail(), MemberRelationTypeEnum.OTHER.getCode());
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_DOES_NOT_REGISTER);
        }

//        if(fromMobile && baseMobileService.hasNoConsumerRole(userDO)) {
//            throw new BusinessException(ResponseCodeEnum.MC_MS_MOBILE_USER_HAS_NO_CONSUMER_ROLE);
//        }

        String smsCode = SecurityStringUtil.getRandomSmsCode();
        String cacheKey = MemberRedisConstant.RESET_PWD_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getEmail();
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);

        // 如果验证码已经发送，则不再发送
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 发送验证码
        memberCacheService.setString(cacheKey, smsCode, memberRefreshConfig.getMailCodeCachedTimeSeconds());
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);

        String title = MemberStringEnum.DYNAMIC_EMAIL_CODE_TITLE.getName();
        String content = MemberStringEnum.DYNAMIC_EMAIL_CODE_TEMPLATE.getName();

        MailUtil.sendTextMail(title, String.format(content, smsCode), userDO.getEmail());
    }

    /**
     * 校验发送邮箱找回密码时的邮件中的验证码是否正确
     * @param emailVO 邮箱地址
     * @return 发送结果
     */
    @Override
    public List<MultiAccInfoResp> checkResetPasswordEmailCode(EmailSmsReq emailVO) {
        // 如果配置的是无需校验，则跳过
        if(memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.RESET_PWD_BY_EMAIL_REDIS_KEY_PREFIX + emailVO.getEmail();
            String smsCode = memberCacheService.getString(cacheKey);
            if(!StringUtils.hasLength(smsCode) || !smsCode.equals(emailVO.getSmsCode())){
                throw new BusinessException(ResponseCodeEnum.MC_MS_SMS_CODE_EXPIRE_OR_MISMATCH);
            }
        }

        List<UserDO> userDOList = userRepository.findAllByEmailAndRelType(emailVO.getEmail(), MemberRelationTypeEnum.OTHER.getCode());
        BusinessAssertUtil.notEmpty(userDOList, ResponseCodeEnum.MC_MS_MANAGE_EMAIL_ERROR);

        // 构建返回数据
        return userDOList.stream()
                .map(userDO -> new MultiAccInfoResp(userDO.getMember().getId(), userDO.getId(), userDO.getMember().getName(), userDO.getMember().getLogo()))
                .sorted(Comparator.comparing(MultiAccInfoResp::getMemberId))
                .collect(Collectors.toList());
    }

    /**
     * 根据邮箱验证码重设密码
     *
     * @param codeReq 接口参数
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPasswordByEmailCode(ResetPasswordByEmailCodeReq codeReq) {
        if (memberRefreshConfig.getCheckSmsCode()){
            String cacheKey = MemberRedisConstant.RESET_PWD_BY_EMAIL_REDIS_KEY_PREFIX + codeReq.getEmail();
            String smsCode = memberCacheService.getString(cacheKey);
            if(!StringUtils.hasLength(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if(!codeReq.getSmsCode().equals(smsCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }

            // 删除使用过的验证码
            eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Collections.singletonList(cacheKey)));
        }

        // 查找用户列表
        List<UserDO> userDOList = userRepository.findAllByEmailAndRelType(codeReq.getEmail(), MemberRelationTypeEnum.OTHER.getCode());
        BusinessAssertUtil.notEmpty(userDOList, ResponseCodeEnum.MC_MS_MANAGE_EMAIL_ERROR);

        if (!CollectionUtils.isEmpty(codeReq.getUserIdList())) {
            userDOList = userDOList.stream().filter(userDO -> codeReq.getUserIdList().stream().anyMatch(userIdReq -> userIdReq.equals(userDO.getId()))).collect(Collectors.toList());
        } else if (userDOList.size() > 1) {
            throw new BusinessException(ResponseCodeEnum.MULTI_ACCOUNT_SPECIFIED_NOT_FOUND);
        }

        for (UserDO userDO : userDOList) {
            userDO.setPassword(PasswordUtil.tryEncrypt(codeReq.getPassword()));
            userDO.setLastModifyPwdTime(LocalDateTime.now());
            userRepository.save(userDO);
        }
    }

    /**
     * 获取会员注册页面-会员类型、商业类型页面内容（第二页）
     * @return 操作结果
     */
    @Override
    public List<MemberRegisterTypeMenuResp> getRegisterTypePageContent() {
        //去掉平台后台的业务类型，以及状态为禁用的角色关联的类型
        return memberRoleRepository.findFirstByRelTypeNotAndStatus(MemberRelationTypeEnum.PLATFORM.getCode(), EnableDisableStatusEnum.ENABLE.getCode())
                .stream()
                .collect(Collectors.groupingBy(MemberRoleDO::getMemberType))
                .entrySet()
                .stream()
                .map(entry -> {
                    MemberTypeEnum memberTypeEnum = MemberTypeEnum.toEnum(entry.getKey());
                    MemberRegisterTypeMenuResp memberRegisterTypeMenuResp = new MemberRegisterTypeMenuResp();
                    memberRegisterTypeMenuResp.setMemberType(memberTypeEnum.getCode());
                    memberRegisterTypeMenuResp.setMemberTypeName(memberTypeEnum.getName());
                    memberRegisterTypeMenuResp.setMemberRoleVOList(entry.getValue()
                            .stream()
                            .map(memberRoleDO -> new MemberRegisterTypeMenuResp.MemberRoleVO(memberRoleDO.getId(), memberRoleDO.getRoleName()))
                            .collect(Collectors.toList()));
                    return memberRegisterTypeMenuResp;
                })
                .sorted(Comparator.comparing(MemberRegisterTypeMenuResp::getMemberType))
                .collect(Collectors.toList());
    }

    /**
     * 获取App客户端会员注册页面-会员类型、商业类型页面内容（第二页）
     *
     * @return 操作结果
     */
    @Override
    public List<MemberRegisterTypeMenuResp> getMobileRegisterTypePageContent() {
        return memberRoleRepository.findAllByRelTypeNotAndRoleTypeAndStatus(MemberRelationTypeEnum.PLATFORM.getCode(),
                        RoleTypeEnum.SERVICE_CONSUMER.getCode(),
                        EnableDisableStatusEnum.ENABLE.getCode())
                .stream()
                .collect(Collectors.groupingBy(MemberRoleDO::getMemberType))
                .entrySet()
                .stream()
                .map(entry -> {
                    MemberTypeEnum memberTypeEnum = MemberTypeEnum.toEnum(entry.getKey());
                    MemberRegisterTypeMenuResp memberRegisterTypeMenuResp = new MemberRegisterTypeMenuResp();
                    memberRegisterTypeMenuResp.setMemberType(memberTypeEnum.getCode());
                    memberRegisterTypeMenuResp.setMemberTypeName(memberTypeEnum.getName());
                    memberRegisterTypeMenuResp.setMemberRoleVOList(entry.getValue()
                            .stream()
                            .map(memberRoleDO -> new MemberRegisterTypeMenuResp.MemberRoleVO(memberRoleDO.getId(), memberRoleDO.getRoleName()))
                            .collect(Collectors.toList()));
                    return memberRegisterTypeMenuResp;
                })
                .sorted(Comparator.comparing(MemberRegisterTypeMenuResp::getMemberType))
                .collect(Collectors.toList());
    }

    /**
     * 获取会员注册页面-详细信息页面内容（第三页）
     * @param detailVO 接口参数
     * @return 注册第三页的内容
     */
    @Override
    public List<MemberConfigGroupResp> getRegisterDetailPageContent(MemberDetailReq detailVO) {
        MemberRoleDO memberRoleDO = memberRoleRepository.findFirstByIdAndRelTypeNotAndStatus(detailVO.getRoleId(), MemberRelationTypeEnum.PLATFORM.getCode(), EnableDisableStatusEnum.ENABLE.getCode());
        BusinessAssertUtil.isFalse(memberRoleDO == null, ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);

        return baseMemberRegisterDetailService.groupMemberConfig(new ArrayList<>(memberRoleDO.getConfigs()));
    }

    /**
     * 平台会员注册
     * @param registerSourceEnum 注册来源枚举
     * @param smsCode  短信验证码
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param userPassword 用户密码（Aes加密后的密码）
     * @param email 邮箱
     * @param memberType 会员类型
     * @param memberRoleId 会员角色类型
     * @param detail 注册资料
     * @param invitationCode 邀请码
     * @param groupIdentifier 实控人分组标识
     * @return 注册结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberRegisterResultResp registerPlatformMember(MemberRegisterSourceEnum registerSourceEnum, String smsCode, String telCode, String phone, String userPassword, String email, Integer memberType, Long memberRoleId, Map<String, Object> detail, String invitationCode, String groupIdentifier) {
        log.info("注册信息提交，内容为{}", JSONObject.toJSONString(detail));
        MemberSendInvitationDO sendInvitation = null;
        // 邀请码注册
//        if (StringUtils.hasLength(invitationCode)) {
//            sendInvitation = memberSendInvitationRepository.findByInvitationCode(invitationCode);
//            if (Objects.isNull(sendInvitation) || !InvitationCodeStateEnum.UNREGISTERED.getCode().equals(sendInvitation.getInvitationCodeState())) {
//                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_CODE_EXPIRED);
//            }
//        }

        // 短信校验
        if(memberRefreshConfig.getCheckSmsCode()) {
            String cacheKey = MemberRedisConstant.REGISTER_BY_PHONE_REDIS_KEY_PREFIX + phone;
            String cacheCode = memberCacheService.getString(cacheKey);
            if (!StringUtils.hasLength(cacheCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!smsCode.equals(cacheCode)) {
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }

        //幂等校验：判断手机号和邮箱是否存在
        if(memberCacheService.existRegisterKey(phone, false)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_ACCOUNT_OR_PHONE_EXISTS);
        }

        if(StringUtils.hasLength(email) && memberCacheService.existRegisterKey(email, false)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
        }

        memberCacheService.setRegisterKey(phone, email, false);

        //查询平台会员和平台角色
        MemberDO platformMemberDO = memberRepository.findPlatformMember();
        if (platformMemberDO == null) {
            memberCacheService.deleteRegisterKey(phone, email, false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRoleDO platformRoleDO = platformMemberDO.getMemberRoles().stream().filter(r -> r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).findFirst().orElse(null);
        if (platformRoleDO == null) {
            memberCacheService.deleteRegisterKey(phone, email, false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        //检查手机号是否已经存在
        if(userRepository.existsByRelTypeAndPhone(MemberRelationTypeEnum.OTHER.getCode(), phone)) {
            memberCacheService.deleteRegisterKey(phone, email, false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }

        //判断邮箱（如果非空）是否存在
        if(StringUtils.hasLength(email) && userRepository.existsByRelTypeAndEmail(MemberRelationTypeEnum.OTHER.getCode(), email)) {
            memberCacheService.deleteRegisterKey(phone, email, false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
        }

        //通过会员类型查找角色
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(memberRoleId).orElse(null);
        if (memberRoleDO == null) {
            memberCacheService.deleteRegisterKey(phone, email, false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //检查注册信息
        Set<MemberRegisterConfigDO> memberRoleDOConfigs = memberRoleDO.getConfigs();
        List<MemberRegisterDetailDO> memberRegisterDetails = new ArrayList<>();
        //会员名称和详细信息
        String memberName;
        try {
            memberName = baseMemberRegisterDetailService.checkMemberRegisterDetail(detail, new ArrayList<>(memberRoleDOConfigs), memberRegisterDetails, phone);
        } catch (BusinessException e) {
            memberCacheService.deleteRegisterKey(phone, email, false);
            throw e;
        }

        if(StringUtils.hasLength(memberName) && memberRepository.existsByName(memberName)) {
            memberCacheService.deleteRegisterKey(phone, email, false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
        }

        String md5Password;
        try {
            md5Password = PasswordUtil.tryEncrypt(userPassword);
        } catch (Exception e) {
            memberCacheService.deleteRegisterKey(phone, email, false);
            throw new BusinessException(ResponseCodeEnum.AES_DECRYPTION_ERROR);
        }

        //根据角色查找平台会员审核流程的Key
        ProcessBO processResult = basePlatformProcessService.findRolePlatformProcess(memberRoleDO);

        //该会员角色是否配置的是无需审核的工作流
        Boolean verify = processResult.getEmptyProcess();

        //该会员角色是否有必填资料
        Boolean requireComplete = memberRoleDOConfigs.stream().anyMatch(registerConfigDO -> Objects.equals(EnableDisableStatusEnum.DISABLE.getCode(), registerConfigDO.getFieldEmpty()));

        //新增会员
        AddMemberBO memberBO = new AddMemberBO();
        memberBO.setPlatformProcess(processResult);
        memberBO.setUpperMember(platformMemberDO);
        memberBO.setUpperRole(platformRoleDO);
        memberBO.setMemberRoleDO(memberRoleDO);
        memberBO.setMemberTypeEnum(MemberTypeEnum.toEnum(memberType));
        memberBO.setRegisterDetails(memberRegisterDetails);

        String memberCode = "B2B" + redisUtils.getSerialNumberByDay(MemberRedisConstant.MEMBER_CODE_PREFIX, 4, RedisConstant.REDIS_USER_INDEX);
        memberBO.setCode(memberCode);
        memberBO.setName(memberName);
        memberBO.setTelCode(telCode);
        memberBO.setPhone(phone);
        //通过注册页面的手机号即账号
        memberBO.setAccount(phone);
        memberBO.setPassword(md5Password);
        memberBO.setEmail(StringUtils.hasLength(email) ? email.trim() : "");
        //注册来源
        memberBO.setSource(registerSourceEnum);
        //会员注册，其外部审核记录的操作角色为会员角色
        memberBO.setOperatorRoleName(memberRoleDO.getRoleName());
        memberBO.setInnerStatus(PlatformInnerStatusEnum.TO_BE_COMMIT.getCode());
        //设置等级为Null，自动查询最小等级
        memberBO.setLevel(null);

        MemberRelationDO relationDO = new MemberRelationDO();
        try {
            relationDO = memberInnerService.addPlatformMember(memberBO, RoleTagEnum.MEMBER.getCode());
        }catch (Exception e){
            memberCacheService.deleteRegisterKey(phone, email, false);
            throw new BusinessException(e.getMessage());
        }

        MemberDO upperMember = memberRepository.findById(baiTaiMemberProperties.getSelfMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));
        MemberRoleDO upperMemberRole = memberRoleRepository.findById(baiTaiMemberProperties.getSelfRoleId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST));

        // 新增会员上下级关系
        memberInnerService.addMemberRelation(upperMember, upperMemberRole, relationDO, memberRoleDO, memberType, null, MemberLevelTypeEnum.MERCHANT.getCode(), RoleTagEnum.CUSTOMER.getCode(), false);

        if (StringUtils.hasLength(groupIdentifier)) {
            MemberDO subMember = relationDO.getSubMember();

            CorporationDO corporationDO = new CorporationDO();
            corporationDO.setMemberId(subMember.getId());
            corporationDO.setCode(memberCode);
            corporationDO.setName((String) detail.get(MemberRegisterDetailConfigConstant.CORPORATION_NAME));
            corporationDO.setUnifiedSocialCode((String) detail.get(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE));
            corporationDO.setBusinessLicense((String) detail.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE));
            corporationDO.setBusinessLicenseValidEndTime((String) detail.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME));
            corporationDO.setLegalPersonName((String) detail.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME));
            corporationDO.setLegalPersonIdentityCardNo((String) detail.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO));
            corporationDO.setIdCardFront((String) detail.get(MemberRegisterDetailConfigConstant.ID_CARD_FRONT));
            corporationDO.setIdCardBack((String) detail.get(MemberRegisterDetailConfigConstant.ID_CARD_BACK));
            corporationDO.setBusinessManager((String) detail.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER));
            corporationDO.setBusinessManagerPhone((String) detail.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE));
            corporationDO.setFinanceManager((String) detail.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER));
            corporationDO.setFinanceManagerPhone((String) detail.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE));
            corporationDO.setGroupIdentifier(groupIdentifier);
            corporationRepository.saveAndFlush(corporationDO);

            subMember.setCorporationId(corporationDO.getId());
            subMember.setMainFlag(true);
            memberRepository.saveAndFlush(subMember);
        }

        // 邀请码注册
//        if (Objects.nonNull(sendInvitation)) {
//            LocalDateTime now = LocalDateTime.now();
//            // 更新发送邀请
//            sendInvitation.setInvitationCodeState(InvitationCodeStateEnum.REGISTERED.getCode());
//            sendInvitation.setAccount(phone);
//            sendInvitation.setSubMemberId(relationDO.getSubMember().getId());
//            sendInvitation.setUpdateTime(now);
//            memberSendInvitationRepository.save(sendInvitation);
//
//            MemberDO member = memberRepository.findById(sendInvitation.getMemberId()).orElse(new MemberDO());
//            MemberRoleDO role = memberRoleRepository.findById(sendInvitation.getRoleId()).orElse(new MemberRoleDO());
//            // 生成邀请消息
//            MemberReceiveInvitationDO receiveInvitation = new MemberReceiveInvitationDO();
//            receiveInvitation.setMemberId(sendInvitation.getMemberId());
//            receiveInvitation.setRoleId(sendInvitation.getRoleId());
//            receiveInvitation.setMember(member);
//            receiveInvitation.setSubMemberId(relationDO.getSubMemberId());
//            receiveInvitation.setSubRoleId(relationDO.getSubRoleId());
//            receiveInvitation.setSubMember(relationDO.getSubMember());
//            receiveInvitation.setCreateTime(now);
//            receiveInvitation.setInvitationCode(invitationCode);
//            receiveInvitation.setFillInDepositoryDetail(CommonBooleanEnum.NO.getCode());
//            receiveInvitation.setInviteType(MemberInviteTypeEnum.INVITE.getCode());
//            receiveInvitation.setRole(role);
//            receiveInvitation.setRoleTag(Optional.ofNullable(role.getRoleTag()).orElse(0));
//            receiveInvitation.setSubRole(memberRoleDO);
//            receiveInvitation.setSubRoleTag(Optional.ofNullable(memberRoleDO.getRoleTag()).orElse(0));
//            if (PlatformInnerStatusEnum.VERIFY_PASSED.getCode().equals(relationDO.getInnerStatus())) {
//                receiveInvitation.setInvitationTime(now);
//                receiveInvitation.setState(EnableDisableStatusEnum.ENABLE.getCode());
//            } else {
//                receiveInvitation.setState(EnableDisableStatusEnum.DISABLE.getCode());
//            }
//
//            memberReceiveInvitationService.saveInvitation(receiveInvitation);
//
//            if (PlatformInnerStatusEnum.VERIFY_PASSED.getCode().equals(relationDO.getInnerStatus())) {
//                messageFeignService.sendMemberInvitationMessage(receiveInvitation, Optional.ofNullable(sendInvitation.getSubRole().getRoleTag()).orElse(RoleTagEnum.MEMBER.getCode()));
//            }
//        }

        // 发送注册成功分佣处理MQ消息
        sendRegistrationSuccessMessage(relationDO.getSubMember(), invitationCode, registerSourceEnum);

        return new MemberRegisterResultResp(verify, requireComplete, relationDO.getSubMemberId(), relationDO.getSubMember().getCorporationId());
    }

    /**
     * 同步企业
     * @param corporationSyncReq 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncCorporation(CorporationSyncReq corporationSyncReq) {
        log.info("开始同步企业信息：{}", JsonUtil.toJson(corporationSyncReq));

        CorporationDO corporationDO = corporationRepository.findFirstByCode(corporationSyncReq.getCode());

        corporationDO = Optional.ofNullable(corporationDO).orElse(new CorporationDO());

        corporationDO.setCode(corporationSyncReq.getCode());
        corporationDO.setName(corporationSyncReq.getName());
        corporationDO.setUnifiedSocialCode(corporationSyncReq.getUnifiedSocialCode());
        corporationDO.setLegalPersonName(corporationSyncReq.getLegalPersonName());
        corporationDO.setBusinessLicense(corporationSyncReq.getBusinessLicense());
        corporationDO.setBusinessLicenseValidEndTime(corporationSyncReq.getBusinessLicenseValidEndTime());
        corporationDO.setLegalPersonIdentityCardNo(corporationSyncReq.getLegalPersonIdCardNo());
        corporationDO.setLegalPersonPhone(corporationSyncReq.getLegalPersonPhone());
        corporationDO.setIdCardFront(corporationSyncReq.getIdCardFront());
        corporationDO.setIdCardBack(corporationSyncReq.getIdCardBack());
        corporationDO.setBusinessManager(corporationSyncReq.getBusinessManager());
        corporationDO.setBusinessManagerPhone(corporationSyncReq.getBusinessManagerPhone());
        corporationDO.setFinanceManager(corporationSyncReq.getFinanceManager());
        corporationDO.setFinanceManagerPhone(corporationSyncReq.getFinanceManagerPhone());
        corporationDO.setDataSource(DataSourceEnum.EOS.getCode());

        corporationRepository.saveAndFlush(corporationDO);
    }

    /**
     * 同步客户
     * @param customerSyncReq 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncCustomer(CustomerSyncReq customerSyncReq) {
        try {
            log.info("开始同步客户信息：{}", JsonUtil.toJson(customerSyncReq));

            if (!StringUtils.hasLength(customerSyncReq.getAccount())) {
                throw new BusinessException("账号或密码不能为空");
            }
            //初始化密码
            customerSyncReq.setPassword(customerSyncReq.getAccount()+eosApiProperties.getUserPwdSuffix());
            MemberDO existMember = memberRepository.findFirstByCode(customerSyncReq.getCode());

            CorporationDO corporationDO;
            if (!Objects.equals(customerSyncReq.getDataSource(), "sjysz_ys") && Objects.nonNull(existMember) && Objects.nonNull(existMember.getCorporationId())) {
                corporationDO = corporationRepository.findById(existMember.getCorporationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));
            } else {
                corporationDO = corporationRepository.findFirstByCode(customerSyncReq.getCode().split("-")[0]);
            }

            if (Objects.isNull(corporationDO)) {
                log.info("未找到企业{}", customerSyncReq.getCode());
                return;
            }

            String code = customerSyncReq.getCode();
            String phone = customerSyncReq.getPhone();
            String email = customerSyncReq.getEmail();

            // 品牌会员
            if (Objects.nonNull(existMember)) {
                log.info("已存在会员{}，修改会员", existMember.getCode());
                if (Objects.equals(existMember.getDataSource(), DataSourceEnum.MALL.getCode())) {
                    log.info("商城新增的会员{}，不允许修改", existMember.getCode());
                    return;
                }
                // 修改会员信息
                AopProxyUtil.getCurrentProxy(this.getClass()).updateMemberInfo(customerSyncReq, existMember, corporationDO);
                return;
            }

            Map<String, Object> detail = MapUtil.<String, Object>builder()
                    .put(MemberRegisterDetailConfigConstant.MEMBER_NAME, customerSyncReq.getName())
                    .put(MemberRegisterDetailConfigConstant.CORPORATION_NAME, corporationDO.getName())
                    .put(MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME, customerSyncReq.getSimpleName())
                    .put(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE, corporationDO.getUnifiedSocialCode())
                    .put(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE, corporationDO.getBusinessLicense())
                    .put(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME, corporationDO.getBusinessLicenseValidEndTime())
                    .put(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME, corporationDO.getLegalPersonName())
                    .put(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO, corporationDO.getLegalPersonIdentityCardNo())
                    //.put(MemberRegisterDetailConfigConstant.LEGAL_PERSON_PHONE, corporationDO.getLegalPersonPhone())
                    .put(MemberRegisterDetailConfigConstant.ID_CARD_FRONT, corporationDO.getIdCardFront())
                    .put(MemberRegisterDetailConfigConstant.ID_CARD_BACK, corporationDO.getIdCardBack())
                    .put(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER, corporationDO.getBusinessManager())
                    .put(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE, corporationDO.getBusinessManagerPhone())
                    .put(MemberRegisterDetailConfigConstant.FINANCE_MANAGER, corporationDO.getFinanceManager())
                    .put(MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE, corporationDO.getFinanceManagerPhone())
                    .build();

            detail.entrySet().forEach(entry -> {
                if (Objects.isNull(entry.getValue())) {
                    entry.setValue("");
                }
            });

            //查询平台会员和平台角色
            MemberDO platformMemberDO = memberRepository.findPlatformMember();
            if (platformMemberDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
            }

            MemberRoleDO platformRoleDO = platformMemberDO.getMemberRoles().stream().filter(r -> r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).findFirst().orElse(null);
            if (platformRoleDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
            }

            //检查手机号是否已经存在
            if (StringUtils.hasLength(phone) && userRepository.existsByRelTypeAndPhone(MemberRelationTypeEnum.OTHER.getCode(), phone)) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
            }

            //判断邮箱（如果非空）是否存在
            if (StringUtils.hasLength(email) && userRepository.existsByRelTypeAndEmail(MemberRelationTypeEnum.OTHER.getCode(), email)) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
            }

            //通过会员类型查找角色
            MemberRoleDO memberRoleDO = memberRoleRepository.findById(baiTaiMemberProperties.getCustomerRoleId()).orElse(null);
            if (memberRoleDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
            }

            //检查注册信息
            Set<MemberRegisterConfigDO> memberRoleDOConfigs = memberRoleDO.getConfigs();
            List<MemberRegisterDetailDO> memberRegisterDetails = new ArrayList<>();

            ThreadLocalUtils.set(false);

            //会员名称和详细信息
            String memberName = baseMemberRegisterDetailService.checkMemberRegisterDetail(detail, new ArrayList<>(memberRoleDOConfigs), memberRegisterDetails, phone);
            if (StringUtils.hasLength(memberName) && memberRepository.existsByName(memberName)) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
            }

            //根据角色查找平台会员审核流程的Key
            ProcessBO processResult = new ProcessBO(MemberConstant.EMPTY_PLATFORM_VALIDATE_PROCESS_KEY, true);

            //新增会员
            AddMemberBO memberBO = new AddMemberBO();
            memberBO.setPlatformProcess(processResult);
            memberBO.setUpperMember(platformMemberDO);
            memberBO.setUpperRole(platformRoleDO);
            memberBO.setMemberRoleDO(memberRoleDO);
            memberBO.setMemberTypeEnum(MemberTypeEnum.MERCHANT);
            memberBO.setRegisterDetails(memberRegisterDetails);
            memberBO.setCode(code);
            memberBO.setName(memberName);
            memberBO.setTelCode("+86");
            memberBO.setPhone(phone);
            //通过注册页面的手机号即账号
            memberBO.setAccount(customerSyncReq.getAccount());
            memberBO.setPassword(PasswordUtil.tryEncrypt(customerSyncReq.getPassword()));
            memberBO.setEmail(StringUtils.hasLength(email) ? email.trim() : "");
            //注册来源
            memberBO.setSource(MemberRegisterSourceEnum.FROM_ENTERPRISE_WEB_SHOP);
            //会员注册，其外部审核记录的操作角色为会员角色
            memberBO.setOperatorRoleName(memberRoleDO.getRoleName());
            memberBO.setInnerStatus(PlatformInnerStatusEnum.TO_BE_COMMIT.getCode());
            //设置等级为Null，自动查询最小等级
            memberBO.setLevel(null);

            memberBO.setDataSource(DataSourceEnum.EOS.getCode());

            MemberRelationDO relationDO = memberInnerService.addPlatformMember(memberBO, RoleTagEnum.MEMBER.getCode());

            MemberRoleDO vendorMemberRole = memberRoleRepository.findById(baiTaiMemberProperties.getSelfRoleId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));

            MemberDO memberDO = memberRepository.findById(baiTaiMemberProperties.getSelfMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));

            //新增当前会员的下级会员（上下级关系）
            memberInnerService.addMemberRelation(memberDO, vendorMemberRole, relationDO, memberRoleDO, MemberTypeEnum.MERCHANT.getCode(), null, MemberLevelTypeEnum.MERCHANT.getCode(), RoleTagEnum.CUSTOMER.getCode(), true);

            //同步企业
            MemberDO subMember = relationDO.getSubMember();
            subMember.setCorporationId(corporationDO.getId());
            subMember.setSimpleMemberName(customerSyncReq.getSimpleName());
            memberRepository.saveAndFlush(subMember);
        } finally {
            ThreadLocalUtils.clear();
        }
    }

    /**
     * 修改会员信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMemberInfo(CustomerSyncReq customerSyncReq, MemberDO existMember, CorporationDO corporationDO) {
        QMemberRegisterDetailDO qMemberRegisterDetailDO = QMemberRegisterDetailDO.memberRegisterDetailDO;
        Map<MemberRegisterConfigDO, MemberRegisterDetailDO> memberRegisterDetailDOMap = jpaQueryFactory.select(qMemberRegisterDetailDO.memberConfig, qMemberRegisterDetailDO)
                .from(qMemberRegisterDetailDO)
                .where(qMemberRegisterDetailDO.member.id.eq(existMember.getId()))
                .fetch()
                .stream().collect(Collectors.toMap(v -> v.get(qMemberRegisterDetailDO.memberConfig), v -> v.get(qMemberRegisterDetailDO)));

        memberRegisterDetailDOMap.forEach((memberRegisterConfigDO, memberRegisterDetailDO) -> {
            if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.MEMBER_NAME)) {
                memberRegisterDetailDO.setDetail(customerSyncReq.getName());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.CORPORATION_NAME)) {
                memberRegisterDetailDO.setDetail(corporationDO.getName());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME)) {
                memberRegisterDetailDO.setDetail(customerSyncReq.getSimpleName());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE)) {
                memberRegisterDetailDO.setDetail(corporationDO.getUnifiedSocialCode());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_LICENSE)) {
                memberRegisterDetailDO.setDetail(corporationDO.getBusinessLicense());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME)) {
                memberRegisterDetailDO.setDetail(corporationDO.getBusinessLicenseValidEndTime());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME)) {
                memberRegisterDetailDO.setDetail(corporationDO.getLegalPersonName());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO)) {
                memberRegisterDetailDO.setDetail(corporationDO.getLegalPersonIdentityCardNo());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.LEGAL_PERSON_PHONE)) {
                memberRegisterDetailDO.setDetail(corporationDO.getLegalPersonPhone());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.ID_CARD_FRONT)) {
                memberRegisterDetailDO.setDetail(corporationDO.getIdCardFront());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.ID_CARD_BACK)) {
                memberRegisterDetailDO.setDetail(corporationDO.getIdCardBack());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_MANAGER)) {
                memberRegisterDetailDO.setDetail(corporationDO.getBusinessManager());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE)) {
                memberRegisterDetailDO.setDetail(corporationDO.getBusinessManagerPhone());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.FINANCE_MANAGER)) {
                memberRegisterDetailDO.setDetail(corporationDO.getFinanceManager());
            } else if (Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE)) {
                memberRegisterDetailDO.setDetail(corporationDO.getFinanceManagerPhone());
            }
        });
        List<MemberRegisterDetailDO> memberRegisterDetailDOS = new ArrayList<>(memberRegisterDetailDOMap.values());
        memberRegisterDetailRepository.saveAll(memberRegisterDetailDOS);

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), existMember.getId(), baiTaiMemberProperties.getCustomerRoleId());
        Set<MemberDepositoryDetailDO> depositDetails = relationDO.getDepositDetails();
        depositDetails.forEach(memberDepositoryDetailDO -> {
            if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.MEMBER_NAME)) {
                memberDepositoryDetailDO.setDetail(customerSyncReq.getName());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.CORPORATION_NAME)) {
                memberDepositoryDetailDO.setDetail(customerSyncReq.getName());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME)) {
                memberDepositoryDetailDO.setDetail(customerSyncReq.getSimpleName());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getUnifiedSocialCode());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_LICENSE)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getBusinessLicense());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getBusinessLicenseValidEndTime());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getLegalPersonName());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getLegalPersonIdentityCardNo());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.LEGAL_PERSON_PHONE)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getLegalPersonPhone());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.ID_CARD_FRONT)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getIdCardFront());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.ID_CARD_BACK)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getIdCardBack());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_MANAGER)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getBusinessManager());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getBusinessManagerPhone());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.FINANCE_MANAGER)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getFinanceManager());
            } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE)) {
                memberDepositoryDetailDO.setDetail(corporationDO.getFinanceManagerPhone());
            }
        });
        memberDepositoryDetailRepository.saveAll(depositDetails);

        if (!Objects.equals(customerSyncReq.getName(), existMember.getName())) {
            existMember.setName(customerSyncReq.getName());
            memberRepository.saveAndFlush(existMember);

            UserDO userDO = userRepository.findFirstByMemberIdAndUserType(existMember.getId(), UserTypeEnum.ADMIN.getCode());
            userDO.setName(customerSyncReq.getName());
            userRepository.saveAndFlush(userDO);
        }
    }

    /**
     * 校验手机号码前缀、手机号码
     *
     * @param telCode         手机号码前缀
     * @param phone           手机号码
     * @param checkPhoneExist 是否检查手机号是否已经注册
     * @return 解密后的手机号码
     */
    private String checkCountryPhone(String telCode, String phone, boolean checkPhoneExist) {
        CountryAreaResp countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(telCode), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);

        if (!StringUtils.hasLength(phone) || !SecurityStringUtil.checkPhone(countryAreaResp.getTelCode(), phone)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PHONE_FORMAT_INCORRECT);
        }

        if (checkPhoneExist && !userRepository.existsByRelTypeAndPhone(MemberRelationTypeEnum.OTHER.getCode(), phone)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_DOES_NOT_REGISTER);
        }

        return phone;
    }

    /**
     * 验证滑块
     * @param imgId 滑块Id
     * @param width 滑块宽度
     */
    private void checkCaptcha(String imgId, BigDecimal width) {
        String widthCache = redisUtils.stringGet(MemberRedisConstant.REGISTER_CAPTCHA_IMG_REDIS_KEY_PREFIX + imgId, RedisConstant.REDIS_USER_INDEX);
        if(!StringUtils.hasLength(widthCache)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CAPTCHA_EXPIRED);
        }

        BigDecimal xWidth = new BigDecimal(widthCache).subtract(width);
        if(!(xWidth.compareTo(BigDecimal.valueOf(5).negate()) >= 0 && xWidth.compareTo(BigDecimal.valueOf(5)) <= 0)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CAPTCHA_MISMATCHED);
        }

        redisUtils.keyDel(MemberRedisConstant.REGISTER_CAPTCHA_IMG_REDIS_KEY_PREFIX + imgId, RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 发送注册成功分佣处理MQ消息
     * @param memberDO 注册的会员
     * @param invitationCode 邀请码
     * @param registerSourceEnum 注册来源
     */
    private void sendRegistrationSuccessMessage(MemberDO memberDO, String invitationCode, MemberRegisterSourceEnum registerSourceEnum) {
        try {
            if (!StringUtils.hasLength(invitationCode)){
                log.info("非邀请码注册用户不发消息");
                return;
            }
            // 获取用户信息
            UserDO userDO = userRepository.findFirstByMemberIdAndUserType(memberDO.getId(), UserTypeEnum.ADMIN.getCode());
            if (Objects.isNull(userDO)) {
                log.warn("未找到会员对应的用户信息，跳过发送注册成功分佣消息，会员ID：{}", memberDO.getId());
                return;
            }

            // 构建消息DTO
            MemberRegistrationSuccessDTO registrationDTO = new MemberRegistrationSuccessDTO();
            registrationDTO.setUserId(userDO.getId());
            registrationDTO.setUserCode(userDO.getCode());
            registrationDTO.setMemberId(memberDO.getId());
            registrationDTO.setMemberCode(memberDO.getCode());
            registrationDTO.setInvitationCode(invitationCode);
            registrationDTO.setRegisterSource(registerSourceEnum.getCode());
            registrationDTO.setRegistrationTime(System.currentTimeMillis());

            // 发送MQ消息
            String messageBody = SerializeUtil.serialize(registrationDTO);
            mqUtils.sendMsg(MemberMqConstant.MEMBER_REGISTRATION_COMMISSION_EXCHANGE,
                           MemberMqConstant.MEMBER_REGISTRATION_COMMISSION_ROUTING_KEY,
                           messageBody);

            log.info("发送注册成功分佣处理MQ消息成功，用户ID：{}，会员ID：{}，邀请码：{}",
                    userDO.getId(), memberDO.getId(), invitationCode);
        } catch (Exception e) {
            log.error("发送注册成功分佣处理MQ消息失败，会员ID：{}，邀请码：{}", memberDO.getId(), invitationCode, e);
            // 这里不抛出异常，避免影响注册流程
        }
    }
}
