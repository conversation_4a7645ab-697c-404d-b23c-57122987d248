package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.component.base.model.req.ProvinceCityCodeReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 会员能力 - 会员入库 - 审核入库资料接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberToVerifyDepositReq extends MemberDepositReq implements Serializable {
    private static final long serialVersionUID = -5314648855087770827L;

    /**
     * 渠道类型Id，当会员是渠道会员时必填
     */
    private Long channelTypeId;

    /**
     * 上级会员Id，当会员是渠道会员时要大于等于0
     */
    private Long upperRelationId;

    /**
     * 城市编码列表列表，当会员是渠道会员时必填
     */
    @Valid
    private List<ProvinceCityCodeReq> areaCodes;

    /**
     * 渠道描述
     */
    private String remark;

    /**
     * 入库资料
     */
    private Map<String, Object> depositDetails;

    /**
     * 资质文件列表
     */
    @Valid
    private List<MemberQualityReq> qualities;
}
