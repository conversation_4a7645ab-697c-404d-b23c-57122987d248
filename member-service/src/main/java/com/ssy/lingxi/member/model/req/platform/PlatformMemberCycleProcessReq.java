package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 平台后台会员生命周期保存
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@Data
public class PlatformMemberCycleProcessReq implements Serializable {

    private static final long serialVersionUID = 4785221215804103981L;

    /**
     * 规则名称
     */
    @NotBlank(message = "平台流程规则名称不能为空")
    @Size(max = 200, message = "平台流程规则名称最长200个字")
    private String name;

    /**
     * 基础流程id
     */
    @NotNull(message = "请选择流程规则")
    @Positive(message = "请选择流程规则")
    private Long baseProcessId;

    /**
     * 是否适用所有会员
     * true - 是
     * false - 否
     */
    @NotNull(message = "是否适用所有会员属性不能为空")
    private Boolean allMembers;

    /**
     * 适用会员列表，如不适用所有会员，不能为空
     */
    @Valid
    private List<PlatformMemberCycleProcessMemberReq> members;
}
