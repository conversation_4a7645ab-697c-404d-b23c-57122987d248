package com.ssy.lingxi.member.repository.commission;

import com.ssy.lingxi.member.entity.do_.commission.AccountFreezeRecordDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 账户冻结记录Repository
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Repository
public interface AccountFreezeRecordRepository extends JpaRepository<AccountFreezeRecordDO, Long>, JpaSpecificationExecutor<AccountFreezeRecordDO> {

    /**
     * 根据分佣账户id查询冻结记录列表
     *
     * @param commissionAccountId 分佣账户id
     * @param pageable            分页参数
     * @return 冻结记录列表
     */
    Page<AccountFreezeRecordDO> findByCommissionAccountIdOrderByCreateTimeDesc(Long commissionAccountId, Pageable pageable);

    /**
     * 根据用户id查询冻结记录列表
     *
     * @param userId   用户id
     * @param pageable 分页参数
     * @return 冻结记录列表
     */
    Page<AccountFreezeRecordDO> findByUserIdOrderByCreateTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据操作类型查询冻结记录列表
     *
     * @param operationType 操作类型
     * @param pageable      分页参数
     * @return 冻结记录列表
     */
    Page<AccountFreezeRecordDO> findByOperationTypeOrderByCreateTimeDesc(Integer operationType, Pageable pageable);

    /**
     * 根据操作人用户id查询冻结记录列表
     *
     * @param operatorUserId 操作人用户id
     * @param pageable       分页参数
     * @return 冻结记录列表
     */
    Page<AccountFreezeRecordDO> findByOperatorUserIdOrderByCreateTimeDesc(Long operatorUserId, Pageable pageable);


    /**
     * 根据用户id和操作类型查询冻结记录列表
     *
     * @param userId        用户id
     * @param operationType 操作类型
     * @param pageable      分页参数
     * @return 冻结记录列表
     */
    Page<AccountFreezeRecordDO> findByUserIdAndOperationTypeOrderByCreateTimeDesc(Long userId, Integer operationType, Pageable pageable);

    /**
     * 查询指定时间范围内的冻结记录
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageable  分页参数
     * @return 冻结记录列表
     */
    @Query("SELECT afr FROM AccountFreezeRecordDO afr WHERE afr.createTime >= :startTime AND afr.createTime <= :endTime ORDER BY afr.createTime DESC")
    Page<AccountFreezeRecordDO> findByCreateTimeBetween(@Param("startTime") Long startTime, @Param("endTime") Long endTime, Pageable pageable);


    /**
     * 查询用户最近的冻结记录
     *
     * @param userId 用户id
     * @return 最近的冻结记录
     */
    @Query("SELECT afr FROM AccountFreezeRecordDO afr WHERE afr.userId = :userId ORDER BY afr.createTime DESC")
    List<AccountFreezeRecordDO> findLatestByUserId(@Param("userId") Long userId, Pageable pageable);




}
