package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.PaymentTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 付款方式枚举参数校验注解
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {PaymentTypeValidator.class})
public @interface PaymentTypeAnnotation {
    String message() default "付款方式参数值不在定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
