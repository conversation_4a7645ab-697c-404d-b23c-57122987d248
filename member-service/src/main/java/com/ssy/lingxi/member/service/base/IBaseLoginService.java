package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.component.base.model.TokenContext;
import com.ssy.lingxi.member.entity.bo.login.LoginContext;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.model.req.login.EmailLoginSmsCode;
import com.ssy.lingxi.member.model.req.login.MultiAccCheckReq;
import com.ssy.lingxi.member.model.req.login.PhoneBindSmsCodeReq;
import com.ssy.lingxi.member.model.req.login.PhoneLoginSmsCode;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 登陆通用层
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/6
 */
public interface IBaseLoginService {
    /**
     * 判断登录失败次数是否超过限制
     */
    void checkLoginFailures(String loginFailuresKey);

    /**
     * 判断登录失败次数是否超过限制并校验密码
     */
    void checkLoginFailuresAndPwd(Long userId, String dbPwd, String loginPwd);

    /**
     * 判断登录失败次数是否超过限制并校验验证码
     */
    void checkLoginFailuresAndSmsCode(Long userId, String smsCode, String smsCodeReq);

    /**
     * 判断登录失败次数是否超过限制
     */
    List<UserDO> checkLoginFailures(List<UserDO> userDOList, List<String> loginFailuresKeyList);

    /**
     * 判断登录失败次数是否超过限制并校验密码
     */
    List<UserDO> checkLoginFailuresAndPwd(List<UserDO> userDOList, MultiAccCheckReq multiAccCheckReq);

    /**
     * 判断登录失败次数是否超过限制并校验验证码
     */
    List<UserDO> checkLoginFailuresAndSmsCode(List<UserDO> userDOList, MultiAccCheckReq multiAccCheckReq);

    /**
     * 基于登陆方式查找userDO（用于兼容多主体场景）
     */
    List<UserDO> getAllUserDOByLoginStrategy(MultiAccCheckReq multiAccCheckReq);

    /**
     * 基于登陆方式查找userDO（用于兼容多主体场景）
     */
    List<UserDO> getAllUserDOByLoginStrategy(String mainField, Integer loginStrategy);

    /**
     * 查找指定userDO（用于兼容多主体场景）
     */
    UserDO getSpecifyUserDOIfNecessary(String mainField, Long userId, Integer loginStrategy);

    /**
     * 获取更新密码的天数阈值(返回小于0则表示无须校验)
     */
    Integer getUpdatePwdIntervalDays(LocalDateTime lastModifyPwdTime);

    /**
     * 更新用户最后一次登录时间
     */
    void updateUserLastLoginTimeAsync(Long userId);

    /**
     * mobile登录会员下级会员的关系列表（查询服务消费者角色）
     */
    Specification<MemberRelationDO> getMobileRelationSpec(MemberDO memberDO, Boolean isSalesman);

    /**
     * 登陆时发送短信验证码
     */
    void sendPhoneLoginSmsCode(PhoneLoginSmsCode phoneReq, UserDO userDO, String cacheKey, String templateCode);

    /**
     * 登陆时发送邮箱验证码
     */
    void sendEmailLoginSmsCode(EmailLoginSmsCode emailReq, UserDO userDO, String cacheKey);

    /**
     * 超管登录需要校验验证码
     */
    void adminAccountSecurityCheck(UserDO userDO, String phoneSmsCode, String emailSmsCode, String phonePrefix, String emailPrefix);

    /**
     * 手机号绑定发送短信验证码
     */
    void sendPhoneBindSmsCode(PhoneBindSmsCodeReq req, UserDO userDO, String cacheKey);

    /**
     * 封装并返回登陆上下文
     */
    LoginContext getLoginContext(List<MemberRelationDO> availableRelDOList, MemberRelationDO relDO, MemberDO memberDO, UserDO userDO, TokenContext tokenContext);

    /**
     * 封装并返回平台后台登陆上下文
     */
    LoginContext getManageLoginContext(MemberDO memberDO, MemberRoleDO memberRoleDO, UserDO userDO);

    /**
     * 通用登陆逻辑
     * memberRoleId如果不为空，则代表指定使用该角色登陆
     */
    LoginContext baseLogin(List<MemberRelationDO> relDOList, MemberDO memberDO, UserDO userDO, Long specifyMemberRoleId, TokenContext tokenContext);
}
