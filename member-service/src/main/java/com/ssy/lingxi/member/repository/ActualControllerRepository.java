package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.ActualControllerDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 实控人repository
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/7
 */
public interface ActualControllerRepository extends JpaRepository<ActualControllerDO, Long>, JpaSpecificationExecutor<ActualControllerDO> {

    ActualControllerDO findFirstByCode(String code);

}
