package com.ssy.lingxi.member.repository;


import com.ssy.lingxi.member.entity.do_.menuAuth.MenuDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 业务平台 - 菜单数据库操作JpaRepository
 * <AUTHOR>
 * @since 2020-06-15
 * @version 2.0.0
 */
@Repository
public interface MenuRepository extends JpaRepository<MenuDO, Long>, JpaSpecificationExecutor<MenuDO> {
    Boolean existsByPathAndSource(String path, Integer source);

    List<MenuDO> findAllByParentId(Long parentId);

    MenuDO findFirstByIdAndSource(Long id, Integer source);

    List<MenuDO> findBySource(Integer source);

    List<MenuDO> findByIdInAndSourceNot(List<Long> ids, Integer source);

    Boolean existsByPathAndSourceAndIdNot(String path, Integer source, Long id);
}
