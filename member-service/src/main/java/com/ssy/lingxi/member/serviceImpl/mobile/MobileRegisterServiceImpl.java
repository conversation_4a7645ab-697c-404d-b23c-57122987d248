package com.ssy.lingxi.member.serviceImpl.mobile;

import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopEnvironmentEnum;
import com.ssy.lingxi.component.base.model.req.AreaCodeReq;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.member.enums.MemberRegisterSourceEnum;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.login.MobileRegisterReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordByEmailCodeReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordBySmsCodeReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterResultResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterTypeMenuResp;
import com.ssy.lingxi.member.model.resp.login.MultiAccInfoResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseRegisterService;
import com.ssy.lingxi.member.service.mobile.IMobileRegisterService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * App - 会员注册相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-03
 */
@Service
public class MobileRegisterServiceImpl implements IMobileRegisterService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseRegisterService baseRegisterService;

    /**
     * 检查手机号码是否被注册使用
     *
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    @Override
    public void checkPhoneRegistered(HttpHeaders headers, PhoneReq phoneReq) {
        memberCacheService.checkMobileRequestHeader(headers);
        baseRegisterService.checkPhoneRegistered(phoneReq);
    }

    /**
     * “手机号找回密码” - 检查手机号码是否存在
     *
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    @Override
    public void checkPhoneExistsByMobile(HttpHeaders headers, PhoneReq phoneReq) {
        memberCacheService.checkMobileRequestHeader(headers);
        baseRegisterService.checkPhoneExistsByMobile(phoneReq);
    }

    /**
     * 检查邮箱是否被注册使用
     *
     * @param headers Http头部信息
     * @param emailReq 邮箱
     * @return 检查结果
     */
    @Override
    public void checkEmailRegistered(HttpHeaders headers, EmailReq emailReq) {
        memberCacheService.checkMobileRequestHeader(headers);
        baseRegisterService.checkEmailRegistered(emailReq);
    }

    /**
     * 校验邀请码是否存在
     *
     * @param headers Http头部信息
     * @param invitationCodeReq 邀请码
     * @return 检查结果
     */
    @Override
    public void checkInvitationCodeExists(HttpHeaders headers, InvitationCodeReq invitationCodeReq) {
        memberCacheService.checkMobileRequestHeader(headers);
        baseRegisterService.checkInvitationCodeExists(invitationCodeReq);
    }

    /**
     * 发送注册时短信验证码
     *
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 发送结果
     */
    @Override
    public void sendRegisterSmsCode(HttpHeaders headers, PhoneReq phoneReq) {
        memberCacheService.checkMobileRequestHeader(headers);
        baseRegisterService.sendRegisterSmsCode(phoneReq);
    }

    /**
     * 发送手机号找回密码时的短信验证码
     *
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 发送结果
     */
    @Override
    public void sendResetPasswordSmsCode(HttpHeaders headers, PhoneReq phoneReq) {
        memberCacheService.checkMobileRequestHeader(headers);
        baseRegisterService.sendMobileResetPasswordSmsCode(phoneReq);
    }

    /**
     * 校验手机号找回密码时的短信验证码是否正确
     *
     * @param headers    Http头部信息
     * @param phoneSmsReq 手机号码、验证码
     * @return 发送结果
     */
    @Override
    public List<MultiAccInfoResp> checkResetPasswordSmsCode(HttpHeaders headers, PhoneSmsReq phoneSmsReq) {
        memberCacheService.checkMobileRequestHeader(headers);
        return baseRegisterService.checkResetPasswordSmsCode(phoneSmsReq);
    }

    /**
     * 根据短信验证码重设密码
     *
     * @param headers Http头部信息
     * @param codeVO  接口参数
     * @return 操作结果
     */
    @Override
    public void resetPasswordBySmsCode(HttpHeaders headers, ResetPasswordBySmsCodeReq codeVO) {
        memberCacheService.checkMobileRequestHeader(headers);
        baseRegisterService.resetPasswordBySmsCode(codeVO);
    }

    /**
     * 发送邮箱找回密码时的邮件
     *
     * @param headers Http头部信息
     * @param emailReq 邮箱地址
     */
    @Override
    public void sendResetPasswordEmail(HttpHeaders headers, EmailReq emailReq) {
        memberCacheService.checkMobileRequestHeader(headers);
        baseRegisterService.sendResetPasswordEmail(emailReq, true);
    }

    /**
     * 发送邮箱找回密码时的邮件
     *
     * @param headers Http头部信息
     * @param emailVO 邮箱地址
     * @return 发送结果
     */
    @Override
    public List<MultiAccInfoResp> checkResetPasswordEmailCode(HttpHeaders headers, EmailSmsReq emailVO) {
        memberCacheService.checkMobileRequestHeader(headers);
        return baseRegisterService.checkResetPasswordEmailCode(emailVO);
    }

    /**
     * 根据邮箱验证码重设密码
     *
     * @param headers Http头部信息
     * @param codeVO  接口参数
     * @return 操作结果
     */
    @Override
    public void resetPasswordByEmailCode(HttpHeaders headers, ResetPasswordByEmailCodeReq codeVO) {
        memberCacheService.checkMobileRequestHeader(headers);
        baseRegisterService.resetPasswordByEmailCode(codeVO);
    }

    /**
     * 获取会员注册页面-会员类型、商业类型页面内容（第二页）
     * @param headers Http头部信息
     * @return 操作结果
     */
    @Override
    public List<MemberRegisterTypeMenuResp> getRegisterTypePageContent(HttpHeaders headers) {
        memberCacheService.checkMobileRequestHeader(headers);
        return baseRegisterService.getMobileRegisterTypePageContent();
    }

    /**
     * 获取会员注册页面-详细信息页面内容（第三页）
     * @param headers  Http头部信息
     * @param detailVO 接口参数
     * @return 注册第三页的内容
     */
    @Override
    public List<MemberConfigGroupResp> getRegisterDetailPageContent(HttpHeaders headers, MemberDetailReq detailVO) {
        memberCacheService.checkMobileRequestHeader(headers);
        return baseRegisterService.getRegisterDetailPageContent(detailVO);
    }

    /**
     * 查询省列表
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public List<AreaCodeNameResp> listProvince(HttpHeaders headers) {
        memberCacheService.checkMobileRequestHeader(headers);
        return AreaUtil.listProvince();
    }

    /**
     * 根据省编码，查询市列表
     *
     * @param headers Http头部信息
     * @param codeVO  接口参数
     * @return 查询结果
     */
    @Override
    public List<AreaCodeNameResp> listCitiesByProvinceCode(HttpHeaders headers, AreaCodeReq codeVO) {
        memberCacheService.checkMobileRequestHeader(headers);
        return AreaUtil.listCityByProvinceCode(codeVO.getCode());
    }

    /**
     * 根据市编码，查询区列表
     *
     * @param headers Http头部信息
     * @param codeVO  接口参数
     * @return 查询结果
     */
    @Override
    public List<AreaCodeNameResp> listDistrictsByCityCode(HttpHeaders headers, AreaCodeReq codeVO) {
        memberCacheService.checkMobileRequestHeader(headers);
        return AreaUtil.listDistrictByCityCode(codeVO.getCode());
    }

    /**
     * 平台会员注册
     * @param headers    Http头部信息
     * @param registerVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberRegisterResultResp registerPlatformMember(HttpHeaders headers, MobileRegisterReq registerVO) {
        memberCacheService.checkMobileRequestHeader(headers);
        BusinessAssertUtil.isTrue(Optional.ofNullable(headers.getFirst("environment"))
                        .map(Integer::parseInt)
                        .map(ShopEnvironmentEnum::parse)
                        .map(ShopEnvironmentEnum::isMobileEnv)
                        .orElse(false),
                ResponseCodeEnum.MC_MS_THIS_REGISTRATION_INTERFACE_ONLY_ALLOWS_MOBILE_ACCESS);

        return baseRegisterService.registerPlatformMember(MemberRegisterSourceEnum.parseInt(registerVO.getRegisterSource()), registerVO.getSmsCode(), registerVO.getTelCode(), registerVO.getPhone(), registerVO.getPassword(), registerVO.getEmail(), registerVO.getMemberType(), registerVO.getMemberRoleId(), registerVO.getDetail(), registerVO.getInvitationCode(), null);
    }
}
