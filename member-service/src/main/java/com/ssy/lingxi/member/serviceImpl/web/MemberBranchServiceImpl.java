package com.ssy.lingxi.member.serviceImpl.web;

import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.rest.model.resp.eos.DictDataResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.QMemberDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.branch.MemberBranchDO;
import com.ssy.lingxi.member.model.req.branch.MemberBranchAgentSaveOrUpdateReq;
import com.ssy.lingxi.member.model.req.branch.MemberBranchPageReq;
import com.ssy.lingxi.member.model.req.branch.MemberBranchSaveOrUpdateReq;
import com.ssy.lingxi.member.model.resp.branch.*;
import com.ssy.lingxi.member.repository.MemberBranchRepository;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.repository.MemberRoleRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.web.IMemberBranchService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员店铺service实现类
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/5/26
 */
@Service
public class MemberBranchServiceImpl implements IMemberBranchService {

    @Resource
    private MemberBranchRepository memberBranchRepository;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private EosApiService eosApiService;

    @Resource
    private UserRepository userRepository;

    /**
     * 分页查询会员店铺
     *
     * @param pageReq 查询参数
     * @return 响应结果
     */
    @Override
    public PageDataResp<MemberBranchPageResp> page(UserLoginCacheDTO loginUser, MemberBranchPageReq pageReq) {
        Pageable pageable = PageRequest.of(pageReq.getCurrent() -1, pageReq.getPageSize(), Sort.by("id").descending());
        Page<MemberBranchDO> pageList = memberBranchRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            // 非商家只能查询自己的数据
            if (!Objects.equals(loginUser.getMemberId(), baiTaiMemberProperties.getSelfMemberId())) {
                list.add(criteriaBuilder.and(criteriaBuilder.equal(root.get(MemberBranchDO.Fields.memberId).as(Long.class), loginUser.getMemberId())));
            }

            if (Objects.nonNull(pageReq.getMemberId())) {
                list.add(criteriaBuilder.and(criteriaBuilder.equal(root.get(MemberBranchDO.Fields.memberId).as(Long.class), pageReq.getMemberId())));
            }

            if (StringUtils.isNotBlank(pageReq.getName())) {
                list.add(criteriaBuilder.and(criteriaBuilder.like(root.get(MemberBranchDO.Fields.name).as(String.class), "%" + pageReq.getName().trim() + "%")));
            }

            if (Objects.nonNull(pageReq.getRelationStatus())) {
                if (Objects.equals(pageReq.getRelationStatus(), CommonBooleanEnum.YES.getCode())) {
                    list.add(criteriaBuilder.and(criteriaBuilder.isNotNull(root.get(MemberBranchDO.Fields.memberId).as(Integer.class))));
                } else {
                    list.add(criteriaBuilder.and(criteriaBuilder.isNull(root.get(MemberBranchDO.Fields.memberId).as(Integer.class))));
                }
            }

            if (StringUtils.isNotBlank(pageReq.getCreateStartTime())) {
                list.add(criteriaBuilder.and(criteriaBuilder.greaterThanOrEqualTo(root.get(MemberBranchDO.Fields.createTime).as(LocalDateTime.class), DateTimeUtil.parseDateTime(pageReq.getCreateStartTime()))));
            }

            if (StringUtils.isNotBlank(pageReq.getCreateEndTime())) {
                list.add(criteriaBuilder.and(criteriaBuilder.lessThanOrEqualTo(root.get(MemberBranchDO.Fields.createTime).as(LocalDateTime.class), DateTimeUtil.parseDateTime(pageReq.getCreateEndTime()))));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        }, pageable);

        List<Long> memberIds = pageList.getContent().stream().map(MemberBranchDO::getMemberId).distinct().collect(Collectors.toList());
        List<MemberDO> memberDOList = memberRepository.findAllById(memberIds);
        Map<Long, String> memberNameMap = memberDOList.stream().collect(Collectors.toMap(MemberDO::getId, MemberDO::getName));

        List<MemberBranchPageResp> resultList = pageList.getContent().stream().map(v -> {
            MemberBranchPageResp memberBranchPageResp = new MemberBranchPageResp();
            memberBranchPageResp.setId(v.getId());
            memberBranchPageResp.setMemberId(v.getMemberId());
            memberBranchPageResp.setMemberName(memberNameMap.get(v.getMemberId()));
            memberBranchPageResp.setMemberRoleId(v.getMemberRoleId());
            memberBranchPageResp.setName(v.getName());
            memberBranchPageResp.setSimpleName(v.getSimpleName());
            memberBranchPageResp.setType(v.getType());
            memberBranchPageResp.setBusinessAreas(v.getBusinessAreas());
            memberBranchPageResp.setOperateType(v.getOperateType());
            memberBranchPageResp.setOperateBrand(v.getOperateBrand());
            memberBranchPageResp.setBusinessPartition(v.getBusinessPartition());
            memberBranchPageResp.setCommissionDivision(v.getCommissionDivision());
            memberBranchPageResp.setSalesPerson(v.getSalesPerson());
            memberBranchPageResp.setBusinessManager(v.getBusinessManager());
            memberBranchPageResp.setRegionalHead(v.getRegionalHead());
            memberBranchPageResp.setRelationStatusName(Objects.nonNull(v.getMemberId()) ? "已关联用户" : "未关联用户");
            memberBranchPageResp.setCreateUserId(v.getCreateUserId());
            memberBranchPageResp.setCreateUserName(v.getCreateUserName());
            memberBranchPageResp.setCreateTime(DateTimeUtil.formatDateTime(v.getCreateTime()));
            return memberBranchPageResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 获取会员店铺详情
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @Override
    public MemberBranchDetailResp get(CommonIdReq req) {
        MemberBranchDO memberBranchDO = memberBranchRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));

        MemberDO memberDO = null;
        if (Objects.nonNull(memberBranchDO.getMemberId())) {
            memberDO = memberRepository.findById(memberBranchDO.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));
        }

        MemberBranchDetailResp memberBranchDetailResp = new MemberBranchDetailResp();
        memberBranchDetailResp.setId(memberBranchDO.getId());
        memberBranchDetailResp.setMemberId(memberBranchDO.getMemberId());
        memberBranchDetailResp.setMemberName(Optional.ofNullable(memberDO).map(MemberDO::getName).orElse(null));
        memberBranchDetailResp.setMemberRoleId(memberBranchDO.getMemberRoleId());
        memberBranchDetailResp.setName(memberBranchDO.getName());
        memberBranchDetailResp.setSimpleName(memberBranchDO.getSimpleName());
        memberBranchDetailResp.setType(memberBranchDO.getType());
        memberBranchDetailResp.setBusinessAreas(memberBranchDO.getBusinessAreas());
        memberBranchDetailResp.setOperateType(memberBranchDO.getOperateType());
        memberBranchDetailResp.setOperateBrand(memberBranchDO.getOperateBrand());
        memberBranchDetailResp.setBusinessPartition(memberBranchDO.getBusinessPartition());
        memberBranchDetailResp.setCommissionDivision(memberBranchDO.getCommissionDivision());
        memberBranchDetailResp.setSalesPerson(memberBranchDO.getSalesPerson());
        memberBranchDetailResp.setBusinessManager(memberBranchDO.getBusinessManager());
        memberBranchDetailResp.setRegionalHead(memberBranchDO.getRegionalHead());
        memberBranchDetailResp.setCreateTime(DateTimeUtil.formatDateTime(memberBranchDO.getCreateTime()));

        return memberBranchDetailResp;
    }

    /**
     * 代创建保存或更新店铺
     *
     * @param req 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void agentSaveOrUpdate(UserLoginCacheDTO loginUser, MemberBranchAgentSaveOrUpdateReq req) {
        BusinessAssertUtil.isTrue(Objects.equals(loginUser.getMemberId(), baiTaiMemberProperties.getSelfMemberId()), ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED);

        if (Objects.isNull(req.getId())) {
            MemberBranchDO memberBranchDO = new MemberBranchDO();
            memberBranchDO.setName(req.getName());
            memberBranchDO.setSimpleName(req.getSimpleName());
            memberBranchDO.setType(req.getType());
            memberBranchDO.setBusinessAreas(req.getBusinessAreas());
            memberBranchDO.setOperateType(req.getOperateType());
            memberBranchDO.setOperateBrand(req.getOperateBrand());
            memberBranchDO.setBusinessPartition(req.getBusinessPartition());
            memberBranchDO.setCommissionDivision(req.getCommissionDivision());
            memberBranchDO.setSalesPerson(req.getSalesPerson());
            memberBranchDO.setBusinessManager(req.getBusinessManager());
            memberBranchDO.setRegionalHead(req.getRegionalHead());
            memberBranchDO.setCreateMemberId(loginUser.getMemberId());
            memberBranchDO.setCreateMemberRoleId(loginUser.getMemberRoleId());
            memberBranchDO.setCreateUserId(loginUser.getUserId());
            memberBranchDO.setCreateUserName(loginUser.getUserName());
            memberBranchRepository.saveAndFlush(memberBranchDO);
            return;
        }

        MemberBranchDO memberBranchDO = memberBranchRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));
        memberBranchDO.setName(req.getName());
        memberBranchDO.setSimpleName(req.getSimpleName());
        memberBranchDO.setType(req.getType());
        memberBranchDO.setBusinessAreas(req.getBusinessAreas());
        memberBranchDO.setBusinessPartition(req.getBusinessPartition());
        memberBranchDO.setOperateType(req.getOperateType());
        memberBranchDO.setOperateBrand(req.getOperateBrand());
        memberBranchDO.setCommissionDivision(req.getCommissionDivision());
        memberBranchDO.setSalesPerson(req.getSalesPerson());
        memberBranchDO.setBusinessManager(req.getBusinessManager());
        memberBranchDO.setRegionalHead(req.getRegionalHead());
        memberBranchRepository.saveAndFlush(memberBranchDO);
    }

    /**
     * 保存或更新店铺
     *
     * @param req 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(UserLoginCacheDTO loginUser, MemberBranchSaveOrUpdateReq req) {
        BusinessAssertUtil.isTrue(Objects.equals(loginUser.getMemberRoleId(), baiTaiMemberProperties.getCustomerRoleId()), ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED);

        if (Objects.isNull(req.getId())) {
            MemberBranchDO memberBranchDO = new MemberBranchDO();
            memberBranchDO.setMemberId(loginUser.getMemberId());
            memberBranchDO.setMemberRoleId(loginUser.getMemberRoleId());
            memberBranchDO.setName(req.getName());
            memberBranchDO.setSimpleName(req.getSimpleName());
            memberBranchDO.setBusinessAreas(req.getBusinessAreas());
            memberBranchDO.setBusinessPartition(req.getBusinessPartition());
            memberBranchDO.setOperateType(req.getOperateType());
            memberBranchDO.setOperateBrand(req.getOperateBrand());
            memberBranchDO.setCreateMemberId(loginUser.getMemberId());
            memberBranchDO.setCreateMemberRoleId(loginUser.getMemberRoleId());
            memberBranchDO.setCreateUserId(loginUser.getUserId());
            memberBranchDO.setCreateUserName(loginUser.getUserName());
            memberBranchDO.setBrandId(req.getBrandId());
            memberBranchRepository.saveAndFlush(memberBranchDO);

            UserDO userDO = userRepository.findById(loginUser.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));
            Set<MemberBranchDO> branches = userDO.getBranches();
            branches.add(memberBranchDO);
            userDO.setBranches(branches);
            userRepository.saveAndFlush(userDO);
            return;
        }

        MemberBranchDO memberBranchDO = memberBranchRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));
        memberBranchDO.setMemberId(loginUser.getMemberId());
        memberBranchDO.setMemberRoleId(loginUser.getMemberRoleId());
        memberBranchDO.setName(req.getName());
        memberBranchDO.setSimpleName(req.getSimpleName());
        memberBranchDO.setBusinessAreas(req.getBusinessAreas());
        memberBranchDO.setBusinessPartition(req.getBusinessPartition());
        memberBranchDO.setOperateType(req.getOperateType());
        memberBranchDO.setOperateBrand(req.getOperateBrand());
        memberBranchRepository.saveAndFlush(memberBranchDO);

        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));
        Set<MemberBranchDO> branches = userDO.getBranches();
        branches.add(memberBranchDO);
        userDO.setBranches(branches);
        userRepository.saveAndFlush(userDO);
    }

    /**
     * 删除店铺
     *
     * @param req 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(UserLoginCacheDTO sysUser, CommonIdReq req) {
        MemberBranchDO memberBranchDO = memberBranchRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));
        BusinessAssertUtil.isTrue(Objects.equals(memberBranchDO.getMemberId(), sysUser.getMemberId()) || Objects.equals(memberBranchDO.getCreateMemberId(), sysUser.getMemberId()));

        List<UserDO> userDOList = memberBranchDO.getUsers().stream().filter(v -> !Objects.equals(v.getUserType(), UserTypeEnum.ADMIN.getCode())).collect(Collectors.toList());

        // 因为超级管理员是不允许修改的，没办法解除关联，所以特殊处理
        Set<UserDO> users = memberBranchDO.getUsers();
        UserDO userDO = users.stream().filter(v -> Objects.equals(v.getUserType(), UserTypeEnum.ADMIN.getCode())).findFirst().orElse(null);
        if (Objects.nonNull(userDO)) {
            userDO.getBranches().clear();
            userRepository.saveAndFlush(userDO);
        }

        if (!CollectionUtils.isEmpty(userDOList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_BRANCH_HAS_BIND_WITH_USER_NOT_ALLOW_DELETE);
        }

        memberBranchRepository.deleteById(req.getId());
    }

    /**
     * 获取会员店铺列表
     *
     * @return 会员店铺列表
     */
    @Override
    public List<MemberBranchResp> listMemberBranch(UserLoginCacheDTO loginUser) {
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));

        Set<MemberBranchDO> branches = userDO.getBranches();

        return branches.stream().map(v -> {
            MemberBranchResp memberBranchResp = new MemberBranchResp();
            memberBranchResp.setId(v.getId());
            memberBranchResp.setName(v.getName());
            return memberBranchResp;
        }).collect(Collectors.toList());
    }

    /**
     * 会员分页查询
     *
     * @param pageReq 请求参数
     * @return 会员信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageDataResp<MemberSimpleResp> memberPage(MemberPageReq pageReq) {
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(baiTaiMemberProperties.getCustomerRoleId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST));

        QMemberDO qMemberDO = QMemberDO.memberDO;
        JPAQuery<MemberDO> jpaQuery = jpaQueryFactory.select(qMemberDO)
                .from(qMemberDO)
                .where(qMemberDO.memberRoles.contains(memberRoleDO));

        if (StringUtils.isNotBlank(pageReq.getMemberName())) {
            jpaQuery.where(qMemberDO.name.like("%" + pageReq.getMemberName() + "%"));
        }

        long totalCount = jpaQuery.fetchCount();
        if (totalCount <= 0) {
            return new PageDataResp<>();
        }

        List<MemberDO> memberDOList = jpaQuery.offset((long) (pageReq.getCurrent() - 1) * pageReq.getPageSize()).limit(pageReq.getPageSize()).fetch();

        List<MemberSimpleResp> resultList = memberDOList.stream().map(v -> {
            MemberSimpleResp memberSimpleResp = new MemberSimpleResp();
            memberSimpleResp.setId(v.getId());
            memberSimpleResp.setName(v.getName());
            return memberSimpleResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(totalCount, resultList);
    }

    /**
     * 查询字典数据
     */
    @Override
    public MemberBranchSelectResp getDictDataList() {
        List<DictDataResp> dictDataList = eosApiService.getDictDataList();

        MemberBranchSelectResp memberBranchSelectResp = new MemberBranchSelectResp();
        memberBranchSelectResp.setTypeList(dictDataList.stream().map(DictDataResp::getLxmc).collect(Collectors.toList()));
        memberBranchSelectResp.setOperateBrandList(dictDataList.stream().map(DictDataResp::getJypp).collect(Collectors.toList()));
        memberBranchSelectResp.setOperateTypeList(dictDataList.stream().map(DictDataResp::getJylx).collect(Collectors.toList()));

        return memberBranchSelectResp;
    }

    /**
     * 绑定关系
     * @param req 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindRelation(UserLoginCacheDTO loginUser, MemberBranchRelationBindReq req) {
        MemberBranchDO memberBranchDO = memberBranchRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));

        BusinessAssertUtil.isTrue(Objects.isNull(memberBranchDO.getMemberId()), ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED);

        memberBranchDO.setMemberId(req.getMemberId());
        memberBranchDO.setMemberRoleId(baiTaiMemberProperties.getCustomerRoleId());
        memberBranchRepository.saveAndFlush(memberBranchDO);

        UserDO userDO = userRepository.findFirstByMemberIdAndUserType(req.getMemberId(), UserTypeEnum.ADMIN.getCode());
        Set<MemberBranchDO> branches = userDO.getBranches();
        branches.add(memberBranchDO);
        userDO.setBranches(branches);
        userRepository.saveAndFlush(userDO);
    }

    /**
     * 解除绑定关系
     *
     * @param loginUser 登录用户
     * @param req 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unBindRelation(UserLoginCacheDTO loginUser, CommonIdReq req) {
        MemberBranchDO memberBranchDO = memberBranchRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));

        Long originMemberId = memberBranchDO.getMemberId();

        memberBranchDO.setMemberId(null);
        memberBranchDO.setMemberRoleId(null);
        memberBranchRepository.saveAndFlush(memberBranchDO);

        UserDO userDO = userRepository.findFirstByMemberIdAndUserType(originMemberId, UserTypeEnum.ADMIN.getCode());
        Set<MemberBranchDO> branches = userDO.getBranches();
        if (!CollectionUtils.isEmpty(branches)) {
            branches.removeIf(v -> Objects.equals(v.getId(), req.getId()));
            userRepository.saveAndFlush(userDO);
        }
    }

}
