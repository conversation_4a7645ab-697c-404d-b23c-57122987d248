package com.ssy.lingxi.member.service.feign;

/**
 * 调用商品服务Feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-01-24
 */
public interface IProductFeignService {

    /**
     * 平台会员被冻结时，通知商品服务下架所有商品
     * @param memberId 会员Id
     * @param roleId   角色Id
     * @param memberTypeEnum 会员类型枚举
     */
    void asyncNotifyToOffProducts(Long memberId, Long roleId, Integer memberTypeEnum);

    /**
     * 更新商品对应的店铺平台信用积分
     * @param memberId 平台会员Id
     * @param roleId 平台会员角色Id
     * @param memberTypeEnum 会员类型枚举
     * @param plusCreditScore 平台会员信用积分变动的分数
     */
    void updateCommodityCreditScoreAsync(Long memberId, Long roleId, Integer memberTypeEnum, Integer plusCreditScore);
}
