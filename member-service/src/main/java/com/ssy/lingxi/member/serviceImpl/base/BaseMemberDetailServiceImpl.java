package com.ssy.lingxi.member.serviceImpl.base;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRightSpendTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.enums.*;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.MemberAfterSaleHistoryDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberOrganizationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.comment.MemberTradeCommentHistoryDO;
import com.ssy.lingxi.member.entity.do_.complain.MemberComplainHistoryDO;
import com.ssy.lingxi.member.entity.do_.levelRight.*;
import com.ssy.lingxi.member.enums.MemberInnerStatusEnum;
import com.ssy.lingxi.member.enums.MemberOuterStatusEnum;
import com.ssy.lingxi.member.model.dto.ComplainSummaryDTO;
import com.ssy.lingxi.member.model.resp.basic.LineChartItem;
import com.ssy.lingxi.member.model.resp.basic.SubMemberDetailResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.repository.comment.MemberTradeCommentHistoryRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberDetailService;
import com.ssy.lingxi.member.service.base.IBaseMemberLevelConfigService;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * (内部)会员详细信息查询服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-07
 */
@Service
public class BaseMemberDetailServiceImpl implements IBaseMemberDetailService {
    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberRightHistoryRepository memberRightHistoryRepository;

    @Resource
    private MemberRightSpendHistoryRepository memberRightSpendHistoryRepository;

    @Resource
    private BaseCreditConfigRepository baseCreditConfigRepository;

    @Resource
    private MemberLevelConfigRepository memberLevelConfigRepository;

    @Resource
    private MemberLevelHistoryRepository memberLevelHistoryRepository;

    @Resource
    private MemberCreditRepository memberCreditRepository;

    @Resource
    private MemberTradeCommentHistoryRepository memberTradeCommentHistoryRepository;

    @Resource
    private MemberAfterSaleHistoryRepository memberAfterSaleHistoryRepository;

    @Resource
    private MemberComplainHistoryRepository memberComplainHistoryRepository;

    @Resource
    private MemberOrganizationRepository memberOrganizationRepository;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询下级会员详细信息
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public SubMemberDetailResp getSubMemberDetail(MemberRelationDO relationDO, Integer roleTag) {
        SubMemberDetailResp detailVO = new SubMemberDetailResp();
        detailVO.setValidateId(relationDO.getId());
        detailVO.setMemberId(relationDO.getSubMemberId());
        detailVO.setRoleId(relationDO.getSubRoleId());
        detailVO.setName(relationDO.getSubMember().getName());
        detailVO.setStatus(relationDO.getStatus());
        detailVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        detailVO.setInnerStatus(relationDO.getInnerStatus());
        detailVO.setInnerStatusName(SecurityStringUtil.replaceMemberPrefix(MemberInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()), roleTag));
        detailVO.setOuterStatus(relationDO.getOuterStatus());
        detailVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
        detailVO.setMemberTypeEnum(relationDO.getSubMemberTypeEnum());
        detailVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubMemberTypeEnum()));
        detailVO.setRoleName(relationDO.getSubRoleName());
        detailVO.setRoleId(relationDO.getSubRoleId());
        detailVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
        detailVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
        detailVO.setAccount(relationDO.getSubMember().getAccount());
        detailVO.setPhone(relationDO.getSubMember().getPhone());
        detailVO.setEmail(relationDO.getSubMember().getEmail());
        detailVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
        detailVO.setBrandName(relationDO.getSubMember().getBrandName());
        detailVO.setBrandCode(relationDO.getSubMember().getBrandCode());

        return detailVO;
    }

    /**
     * 查询会员详情 - 会员等级信息
     *
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    @Override
    public MemberValidateDetailLevelResp getMemberDetailLevel(MemberRelationDO relationDO) {
        MemberValidateDetailLevelResp levelVO = new MemberValidateDetailLevelResp();
        MemberLevelRightDO levelDO = relationDO.getLevelRight();

        if (levelDO == null) {
            return levelVO;
        }

        levelVO.setLevelTag(levelDO.getLevelTag());
        levelVO.setScore(levelDO.getScore());
        levelVO.setNextScore(levelDO.getLevelConfig() == null ? 0 : levelDO.getLevelConfig().getPoint());
        levelVO.setNextLevelTag(baseMemberLevelConfigService.findNextLevelTag(relationDO, levelDO));

        //今年第一天
        LocalDateTime firstDay = LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear()).withHour(0).withMinute(0).withSecond(0);

        List<MemberLevelHistoryDO> historyDOList = memberLevelHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());

        //汇总统计
        List<LineChartItem> itemList = historyDOList.stream().filter(h -> h.getCreateTime().isAfter(firstDay)).map(h -> {
            LineChartItem item = new LineChartItem();
            item.setX(h.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM")));
            item.setY(h.getScore());
            return item;
        }).collect(Collectors.groupingBy(LineChartItem::getX)).entrySet().stream().map(entry -> {
            LineChartItem item = new LineChartItem();
            item.setX(entry.getKey());
            item.setY(entry.getValue().stream().mapToInt(LineChartItem::getY).sum());
            return item;
        }).collect(Collectors.toList());

        levelVO.setPoints(itemList);

        return levelVO;
    }

    /**
     * 会员详情 - 分页查询会员等级历史记录
     *
     * @param relationDO 会员上下级关系
     * @param current    当前分页
     * @param pageSize   每页行数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(MemberRelationDO relationDO, int current, int pageSize, DateTimeFormatter formatter) {
        MemberLevelRightDO levelDO = relationDO.getLevelRight();

        if (levelDO == null) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());
        Page<MemberLevelHistoryDO> result = memberLevelHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), page);
        List<MemberDetailLevelHistoryResp> historyVOList = result.getContent().stream().map(h -> {
            MemberDetailLevelHistoryResp historyVO = new MemberDetailLevelHistoryResp();
            historyVO.setId(h.getId());
            historyVO.setCreateTime(h.getCreateTime().format(formatter));
            historyVO.setRuleName(MemberLevelRuleTypeEnum.getCodeMsg(h.getLevelRuleTypeEnum()));
            historyVO.setScore(h.getScore());

            String remark;
            if(h.getLevelRuleTypeEnum().equals(MemberLevelRuleTypeEnum.BY_LOGIN.getCode())) {
                remark = MemberLevelRuleTypeEnum.BY_LOGIN.getMessage();
            } else {
                remark = StringUtils.hasLength(h.getRemark()) ? MemberStringEnum.ORDER_NO.getName().concat(":").concat(h.getRemark()) : "";
            }
            historyVO.setRemark(remark);

            return historyVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(result.getTotalElements(), historyVOList);
    }

    /**
     * 会员详情 - 会员权益信息
     *
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    @Override
    public MemberDetailRightResp getMemberDetailRight(MemberRelationDO relationDO) {
        MemberDetailRightResp rightVO = new MemberDetailRightResp();
        rightVO.setLevelTag("");
        rightVO.setSumReturnMoney(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        rightVO.setSumPoint(0);
        rightVO.setCurrentPoint(0);
        rightVO.setSumUsedPoint(0);
        rightVO.setRights(new ArrayList<>());

        MemberLevelRightDO levelRight = relationDO.getLevelRight();
        if (levelRight == null) {
            return rightVO;
        }

        rightVO.setLevelTag(levelRight.getLevelTag());
        rightVO.setCurrentPoint(levelRight.getCurrentPoint());
        rightVO.setSumPoint(levelRight.getSumPoint());
        //返现金额保留两位小数
        rightVO.setSumReturnMoney(levelRight.getSumReturnMoney().setScale(2, RoundingMode.HALF_UP));
        rightVO.setSumUsedPoint(levelRight.getSumUsedPoint());

        if(levelRight.getLevelConfig() == null || CollectionUtils.isEmpty(levelRight.getLevelConfig().getRights())) {
            return rightVO;
        }

        List<MemberDetailRightConfigResp> configVOList = levelRight.getLevelConfig().getRights().stream().map(memberRightConfigDO -> {
            MemberDetailRightConfigResp configVO = new MemberDetailRightConfigResp();
            configVO.setId(memberRightConfigDO.getId());
            configVO.setRightTypeEnum(memberRightConfigDO.getRightType());
            configVO.setName(MemberRightTypeEnum.getCodeMessage(memberRightConfigDO.getRightType()));
            configVO.setRemark(MemberRightTypeEnum.getRemark(memberRightConfigDO.getRightType()));
            configVO.setAcquireWay(MemberRightAcquireWayEnum.getCodeMsg(memberRightConfigDO.getAcquireWay()));
            configVO.setParamWay(MemberRightParamWayEnum.getCodeMsg(memberRightConfigDO.getParamWay()));
            configVO.setStatus(memberRightConfigDO.getStatus());
            BigDecimal parameter = BigDecimal.ZERO;
            if(memberRightConfigDO.getStatus().equals(EnableDisableStatusEnum.DISABLE.getCode()) || NumberUtil.isNullOrZero(memberRightConfigDO.getParameter())) {
                if(memberRightConfigDO.getRightType().equals(MemberRightTypeEnum.PRICE_RIGHT.getCode())) {
                    parameter = BigDecimal.ONE;
                }
            } else {
                parameter = memberRightConfigDO.getParameter();
            }

            String param = parameter.multiply(new BigDecimal(100)).stripTrailingZeros().toPlainString();
            configVO.setParameter(param);
            return configVO;
        }).collect(Collectors.toList());

        rightVO.setRights(configVOList);

        return rightVO;
    }

    /**
     * 会员详情 - 分页查询会员权益获取记录
     *
     * @param relationDO 会员上下级关系
     * @param current    当前分页
     * @param pageSize   每页行数
     * @param formatter 时间日期格式
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(MemberRelationDO relationDO, int current, int pageSize, DateTimeFormatter formatter) {
        MemberLevelRightDO levelDO = relationDO.getLevelRight();

        if (levelDO == null) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());
        Page<MemberRightHistoryDO> result = memberRightHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(r -> {
            MemberDetailRightHistoryResp historyVO = new MemberDetailRightHistoryResp();
            historyVO.setId(r.getId());
            historyVO.setCreateTime(r.getCreateTime().format(formatter));
            historyVO.setRightTypeName(MemberRightTypeEnum.getCodeMessage(r.getRightTypeEnum()));
            //保留两位小数，小数部分是0的话保留整数，小数第一位不是0的话则保留一位
            historyVO.setPoint(MemberConstant.BIG_DECIMAL_FORMAT.format(r.getPoint()));
            historyVO.setRemark(r.getRemark());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员详情 - 分页查询会员权益使用记录
     *
     * @param relationDO 会员上下级关系
     * @param current    当前分页
     * @param pageSize   每页行数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(MemberRelationDO relationDO, int current, int pageSize, DateTimeFormatter formatter) {
        MemberLevelRightDO levelDO = relationDO.getLevelRight();

        if (levelDO == null) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());
        Page<MemberRightSpendHistoryDO> result = memberRightSpendHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(r -> {
            MemberDetailRightSpendHistoryResp historyVO = new MemberDetailRightSpendHistoryResp();
            historyVO.setId(r.getId());
            historyVO.setCreateTime(r.getCreateTime().format(formatter));
            historyVO.setRightTypeName(MemberRightTypeEnum.getCodeMessage(r.getRightTypeEnum()));
            historyVO.setSpendTypeName(MemberRightSpendTypeEnum.getNameByCode(r.getSpendTypeEnum()));
            historyVO.setPoint(r.getPoint());
            historyVO.setRemark(r.getRemark());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员详情 - 会员信用信息
     *
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditResp getMemberDetailCredit(MemberRelationDO relationDO) {
        MemberCreditDO creditDO = relationDO.getCredit();
        if (creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_CREDIT_DOES_NOT_EXIST);
        }

        MemberDetailCreditResp creditVO = new MemberDetailCreditResp();
        creditVO.setAfterSaleCommentPoint(creditDO.getAfterSaleCommentPoint());
        creditVO.setComplainPoint(creditDO.getComplainPoint());
        creditVO.setCreditPoint(creditDO.getCreditPoint());
        creditVO.setRegisterYearsPoint(creditDO.getRegisterYearsPoint());
        creditVO.setTradeCommentPoint(creditDO.getTradeCommentPoint());

        List<BaseCreditConfigDO> configDOList = baseCreditConfigRepository.findByStatus(EnableDisableStatusEnum.ENABLE.getCode());
        creditVO.setConfigs(configDOList.stream().map(c -> {
            MemberDetailCreditResp.CreditConfigItem item = new MemberDetailCreditResp.CreditConfigItem();
            item.setId(c.getId());
            item.setCreditTypeEnum(c.getCreditTypeEnum());
            item.setCreditTypeName(MemberCreditTypeEnum.getNameByCode(c.getCreditTypeEnum()));
            item.setCreditPoint(c.getCreditPoint());
            item.setRemark(MemberCreditTypeEnum.getRemark(c.getCreditTypeEnum()));
            if(c.getCreditTypeEnum().equals(MemberCreditTypeEnum.BY_TRADE_COMMENT_FIVE_STARS.getCode())) {
                item.setCurrentPoint(creditDO.getTradeCommentPoint());
            } else if(c.getCreditTypeEnum().equals(MemberCreditTypeEnum.BY_COMPLAIN.getCode())) {
                item.setCurrentPoint(creditDO.getComplainPoint());
            } else if(c.getCreditTypeEnum().equals(MemberCreditTypeEnum.BY_COMMENT_AFTER_TRADE.getCode())) {
                item.setCurrentPoint(creditDO.getAfterSaleCommentPoint());
            } else if(c.getCreditTypeEnum().equals(MemberCreditTypeEnum.BY_REGISTER_YEARS.getCode())) {
                item.setCurrentPoint(creditDO.getRegisterYearsPoint());
            } else {
                item.setCurrentPoint(0);
            }

            return item;
        }).collect(Collectors.toList()));

        return creditVO;
    }

    /**
     * 会员详情 - 会员信用信息(平台层面汇总)
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberDetailCreditResp> getAllMemberDetailCredit(Long subMemberId, Long subRoleId) {
        List<MemberCreditDO> creditDOList = memberCreditRepository.findBySubMemberIdAndSubRoleId(subMemberId, subRoleId);
        Integer sumAfterSaleCommentPoint = creditDOList.stream().map(MemberCreditDO::getAfterSaleCommentPoint).reduce(Integer::sum).orElse(0);
        Integer sumComplainPoint = creditDOList.stream().map(MemberCreditDO::getComplainPoint).reduce(Integer::sum).orElse(0);
        Integer sumCreditPoint = creditDOList.stream().map(MemberCreditDO::getCreditPoint).reduce(Integer::sum).orElse(0);
        Integer sumRegisterYearsPoint = creditDOList.stream().map(MemberCreditDO::getRegisterYearsPoint).reduce(Integer::sum).orElse(0);
        Integer sumTradeCommentPoint = creditDOList.stream().map(MemberCreditDO::getTradeCommentPoint).reduce(Integer::sum).orElse(0);

        MemberDetailCreditResp creditVO = new MemberDetailCreditResp();
        creditVO.setAfterSaleCommentPoint(sumAfterSaleCommentPoint);
        creditVO.setComplainPoint(sumComplainPoint);
        creditVO.setCreditPoint(sumCreditPoint);
        creditVO.setRegisterYearsPoint(sumRegisterYearsPoint);
        creditVO.setTradeCommentPoint(sumTradeCommentPoint);

        List<BaseCreditConfigDO> configDOList = baseCreditConfigRepository.findByStatus(EnableDisableStatusEnum.ENABLE.getCode());
        creditVO.setConfigs(configDOList.stream().map(c -> {
            MemberDetailCreditResp.CreditConfigItem item = new MemberDetailCreditResp.CreditConfigItem();
            item.setId(c.getId());
            item.setCreditTypeName(MemberCreditTypeEnum.getNameByCode(c.getCreditTypeEnum()));
            item.setCreditPoint(c.getCreditPoint());
            item.setRemark(MemberCreditTypeEnum.getRemark(c.getCreditTypeEnum()));
            if(c.getCreditTypeEnum().equals(MemberCreditTypeEnum.BY_TRADE_COMMENT_FIVE_STARS.getCode())) {
                item.setCurrentPoint(sumTradeCommentPoint);
            } else if(c.getCreditTypeEnum().equals(MemberCreditTypeEnum.BY_COMPLAIN.getCode())) {
                item.setCurrentPoint(sumComplainPoint);
            } else if(c.getCreditTypeEnum().equals(MemberCreditTypeEnum.BY_COMMENT_AFTER_TRADE.getCode())) {
                item.setCurrentPoint(sumAfterSaleCommentPoint);
            } else if(c.getCreditTypeEnum().equals(MemberCreditTypeEnum.BY_REGISTER_YEARS.getCode())) {
                item.setCurrentPoint(sumRegisterYearsPoint);
            } else {
                item.setCurrentPoint(0);
            }
            return item;
        }).collect(Collectors.toList()));

        return WrapperUtil.success(creditVO);
    }

    /**
     * 会员详情 - 会员信用 - 交易评价汇总
     *
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(MemberRelationDO relationDO) {
        MemberDetailCreditCommentSummaryResp summaryVO = new MemberDetailCreditCommentSummaryResp();
        List<MemberTradeCommentHistoryDO> historyDOList = memberTradeCommentHistoryRepository.findAllByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());

        //四舍五入计算平均星级
        double avgStarD = historyDOList.stream().mapToInt(MemberTradeCommentHistoryDO::getStar).average().orElse(0);
        Integer avgStar = BigDecimal.valueOf(Math.round(avgStarD)).intValue();
        summaryVO.setAvgStar(avgStar);

        //循环计算
        LocalDateTime last7days = LocalDateTime.now().minusDays(7);
        LocalDateTime last30days = LocalDateTime.now().minusDays(30);
        LocalDateTime last180days = LocalDateTime.now().minusDays(180);

        List<MemberDetailCreditCommentSummaryResp.TableRow> rows = Stream.of(5, 4, 3, 2, 1).map(star -> {
            MemberDetailCreditCommentSummaryResp.TableRow tableRow = new MemberDetailCreditCommentSummaryResp.TableRow();
            List<MemberTradeCommentHistoryDO> subList = historyDOList.stream().filter(h -> h.getStar().equals(star)).collect(Collectors.toList());

            long countIn7Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last7days)).count();
            long countIn30Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last30days)).count();
            long countIn180Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last180days)).count();
            long countBefore180Days = subList.stream().filter(h -> h.getCreateTime().isBefore(last180days)).count();
            long sum = countIn180Days + countBefore180Days;

            tableRow.setStar(star);
            tableRow.setLast7days(countIn7Days);
            tableRow.setLast30days(countIn30Days);
            tableRow.setLast180days(countIn180Days);
            tableRow.setBefore180days(countBefore180Days);
            tableRow.setSum(sum);
            return tableRow;
        }).collect(Collectors.toList());

        summaryVO.setRows(rows);

        return summaryVO;
    }


    /**
     * 会员详情 - 会员信用 - 交易评价汇总（平台层面汇总）
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getAllMemberDetailCreditTradeCommentSummary(Long subMemberId, Long subRoleId) {
        MemberDetailCreditCommentSummaryResp summaryVO = new MemberDetailCreditCommentSummaryResp();
        List<MemberTradeCommentHistoryDO> historyDOList = memberTradeCommentHistoryRepository.findAllBySubMemberIdAndSubRoleId(subMemberId, subRoleId);

        //四舍五入计算平均星级
        double avgStarD = historyDOList.stream().mapToInt(MemberTradeCommentHistoryDO::getStar).average().orElse(0);
        Integer avgStar = BigDecimal.valueOf(Math.round(avgStarD)).intValue();
        summaryVO.setAvgStar(avgStar);

        //循环计算
        LocalDateTime last7days = LocalDateTime.now().minusDays(7);
        LocalDateTime last30days = LocalDateTime.now().minusDays(30);
        LocalDateTime last180days = LocalDateTime.now().minusDays(180);

        List<MemberDetailCreditCommentSummaryResp.TableRow> rows = Stream.of(5, 4, 3, 2, 1).map(star -> {
            MemberDetailCreditCommentSummaryResp.TableRow tableRow = new MemberDetailCreditCommentSummaryResp.TableRow();
            List<MemberTradeCommentHistoryDO> subList = historyDOList.stream().filter(h -> h.getStar().equals(star)).collect(Collectors.toList());

            long countIn7Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last7days)).count();
            long countIn30Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last30days)).count();
            long countIn180Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last180days)).count();
            long countBefore180Days = subList.stream().filter(h -> h.getCreateTime().isBefore(last180days)).count();
            long sum = countIn180Days + countBefore180Days;

            tableRow.setStar(star);
            tableRow.setLast7days(countIn7Days);
            tableRow.setLast30days(countIn30Days);
            tableRow.setLast180days(countIn180Days);
            tableRow.setBefore180days(countBefore180Days);
            tableRow.setSum(sum);
            return tableRow;
        }).collect(Collectors.toList());

        summaryVO.setRows(rows);

        return summaryVO;
    }

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     *
     * @param relationDO 会员上下级关系
     * @param starLevel 评论级别
     * @param current    当前分页
     * @param pageSize   每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(MemberRelationDO relationDO, Integer starLevel, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());

        final Integer oneOrTwoStarsLevel = 1;
        final Integer threeStarsLevel = 2;
        final Integer fourOrFiveStarsLevel = 3;

        Specification<MemberTradeCommentHistoryDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), relationDO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), relationDO.getRoleId()));
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), relationDO.getSubMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), relationDO.getSubRoleId()));

            if (starLevel != null) {
                if (starLevel.equals(oneOrTwoStarsLevel)) {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("star").as(Integer.class), 1), criteriaBuilder.equal(root.get("star").as(Integer.class), 2)));
                } else if (starLevel.equals(threeStarsLevel)) {
                    list.add(criteriaBuilder.equal(root.get("star").as(Integer.class), 3));
                } else {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("star").as(Integer.class), 4), criteriaBuilder.equal(root.get("star").as(Integer.class), 5)));
                }
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberTradeCommentHistoryDO> result = memberTradeCommentHistoryRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(h -> {
            MemberDetailCreditTradeHistoryResp historyVO = new MemberDetailCreditTradeHistoryResp();
            historyVO.setId(h.getId());
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setDealTime(h.getDealTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setStar(h.getStar());
            historyVO.setByMemberName(h.getMemberName());
            historyVO.setComment(Optional.ofNullable(h.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(h.getComment()));
            historyVO.setProduct(h.getProduct());
            historyVO.setOrderNo(h.getOrderNo());
            historyVO.setPrice(h.getPrice());
            historyVO.setPurchaseCount(h.getDealCount());
            historyVO.setOrderId(h.getOrderId());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录（平台层面汇总）
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param starLevel   评论等级
     * @param current     当前分页
     * @param pageSize    每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditTradeHistoryResp> pageAllMemberDetailCreditTradeCommentHistory(Long subMemberId, Long subRoleId, Integer starLevel, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());

        final Integer oneOrTwoStarsLevel = 1;
        final Integer threeStarsLevel = 2;

        Specification<MemberTradeCommentHistoryDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //下级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), subMemberId));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), subRoleId));

            if (!NumberUtil.isNullOrLteZero(starLevel)) {
                if (starLevel.equals(oneOrTwoStarsLevel)) {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("star").as(Integer.class), 1), criteriaBuilder.equal(root.get("star").as(Integer.class), 2)));
                } else if (starLevel.equals(threeStarsLevel)) {
                    list.add(criteriaBuilder.equal(root.get("star").as(Integer.class), 3));
                } else {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("star").as(Integer.class), 4), criteriaBuilder.equal(root.get("star").as(Integer.class), 5)));
                }
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberTradeCommentHistoryDO> result = memberTradeCommentHistoryRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(h -> {
            MemberDetailCreditTradeHistoryResp historyVO = new MemberDetailCreditTradeHistoryResp();
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setDealTime(h.getDealTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setStar(h.getStar());
            historyVO.setByMemberName(h.getMemberName());
            historyVO.setComment(Optional.ofNullable(h.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(h.getComment()));
            historyVO.setProduct(h.getProduct());
            historyVO.setOrderNo(h.getOrderNo());
            historyVO.setPrice(h.getPrice());
            historyVO.setPurchaseCount(h.getDealCount());
            historyVO.setOrderId(h.getOrderId());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员详情 - 会员信用 - 售后评价汇总
     *
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(MemberRelationDO relationDO) {
        MemberDetailCreditCommentSummaryResp summaryVO = new MemberDetailCreditCommentSummaryResp();

        List<MemberAfterSaleHistoryDO> historyDOList = memberAfterSaleHistoryRepository.findAllByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());

        //四舍五入计算平均星级
        double avgStarD = historyDOList.stream().mapToInt(MemberAfterSaleHistoryDO::getStar).average().orElse(0);
        Integer avgStar = BigDecimal.valueOf(Math.round(avgStarD)).intValue();
        summaryVO.setAvgStar(avgStar);

        //循环计算
        LocalDateTime last7days = LocalDateTime.now().minusDays(7);
        LocalDateTime last30days = LocalDateTime.now().minusDays(30);
        LocalDateTime last180days = LocalDateTime.now().minusDays(180);

        List<MemberDetailCreditCommentSummaryResp.TableRow> rows = Stream.of(5, 4, 3, 2, 1).map(star -> {
            MemberDetailCreditCommentSummaryResp.TableRow tableRow = new MemberDetailCreditCommentSummaryResp.TableRow();
            List<MemberAfterSaleHistoryDO> subList = historyDOList.stream().filter(h -> h.getStar().equals(star)).collect(Collectors.toList());

            long countIn7Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last7days)).count();
            long countIn30Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last30days)).count();
            long countIn180Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last180days)).count();
            long countBefore180Days = subList.stream().filter(h -> h.getCreateTime().isBefore(last180days)).count();
            long sum = countIn180Days + countBefore180Days;

            tableRow.setStar(star);
            tableRow.setLast7days(countIn7Days);
            tableRow.setLast30days(countIn30Days);
            tableRow.setLast180days(countIn180Days);
            tableRow.setBefore180days(countBefore180Days);
            tableRow.setSum(sum);
            return tableRow;
        }).collect(Collectors.toList());

        summaryVO.setRows(rows);

        return summaryVO;
    }

    /**
     * 会员详情 - 会员信用 - 售后评价汇总（平台层面汇总）
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getAllMemberDetailCreditAfterSaleCommentSummary(Long subMemberId, Long subRoleId) {
        MemberDetailCreditCommentSummaryResp summaryVO = new MemberDetailCreditCommentSummaryResp();

        List<MemberAfterSaleHistoryDO> historyDOList = memberAfterSaleHistoryRepository.findAllBySubMemberIdAndSubRoleId(subMemberId, subRoleId);

        //四舍五入计算平均星级
        double avgStarD = historyDOList.stream().mapToInt(MemberAfterSaleHistoryDO::getStar).average().orElse(0);
        Integer avgStar = BigDecimal.valueOf(Math.round(avgStarD)).intValue();
        summaryVO.setAvgStar(avgStar);

        //循环计算
        LocalDateTime last7days = LocalDateTime.now().minusDays(7);
        LocalDateTime last30days = LocalDateTime.now().minusDays(30);
        LocalDateTime last180days = LocalDateTime.now().minusDays(180);

        List<MemberDetailCreditCommentSummaryResp.TableRow> rows = Stream.of(5, 4, 3, 2, 1).map(star -> {
            MemberDetailCreditCommentSummaryResp.TableRow tableRow = new MemberDetailCreditCommentSummaryResp.TableRow();
            List<MemberAfterSaleHistoryDO> subList = historyDOList.stream().filter(h -> h.getStar().equals(star)).collect(Collectors.toList());

            long countIn7Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last7days)).count();
            long countIn30Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last30days)).count();
            long countIn180Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last180days)).count();
            long countBefore180Days = subList.stream().filter(h -> h.getCreateTime().isBefore(last180days)).count();
            long sum = countIn180Days + countBefore180Days;

            tableRow.setStar(star);
            tableRow.setLast7days(countIn7Days);
            tableRow.setLast30days(countIn30Days);
            tableRow.setLast180days(countIn180Days);
            tableRow.setBefore180days(countBefore180Days);
            tableRow.setSum(sum);
            return tableRow;
        }).collect(Collectors.toList());

        summaryVO.setRows(rows);

        return summaryVO;
    }

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     *
     * @param relationDO 会员上下级关系
     * @param starLevel  评论等级
     * @param current    当前分页
     * @param pageSize   每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(MemberRelationDO relationDO, Integer starLevel, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());

        final Integer oneOrTwoStarsLevel = 1;
        final Integer threeStarsLevel = 2;

        Specification<MemberAfterSaleHistoryDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), relationDO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), relationDO.getRoleId()));
            //下级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), relationDO.getSubMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), relationDO.getSubRoleId()));

            if (starLevel != null) {
                if (starLevel.equals(oneOrTwoStarsLevel)) {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("star").as(Integer.class), 1), criteriaBuilder.equal(root.get("star").as(Integer.class), 2)));
                } else if (starLevel.equals(threeStarsLevel)) {
                    list.add(criteriaBuilder.equal(root.get("star").as(Integer.class), 3));
                } else {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("star").as(Integer.class), 4), criteriaBuilder.equal(root.get("star").as(Integer.class), 5)));
                }
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberAfterSaleHistoryDO> result = memberAfterSaleHistoryRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(h -> {
            MemberDetailCreditAfterSaleHistoryResp historyVO = new MemberDetailCreditAfterSaleHistoryResp();
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setStar(h.getStar());
            historyVO.setByMemberName(h.getMemberName());
            historyVO.setComment(h.getComment());
            historyVO.setProduct(h.getProduct());
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录（平台层面汇总）
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param starLevel   评论等级
     * @param current     当前分页
     * @param pageSize    每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageAllMemberDetailCreditAfterSaleCommentHistory(Long subMemberId, Long subRoleId, Integer starLevel, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());

        final Integer oneOrTwoStarsLevel = 1;
        final Integer threeStarsLevel = 2;

        Specification<MemberAfterSaleHistoryDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //下级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), subMemberId));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), subRoleId));

            if (starLevel != null) {
                if (starLevel.equals(oneOrTwoStarsLevel)) {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("star").as(Integer.class), 1), criteriaBuilder.equal(root.get("star").as(Integer.class), 2)));
                } else if (starLevel.equals(threeStarsLevel)) {
                    list.add(criteriaBuilder.equal(root.get("star").as(Integer.class), 3));
                } else {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("star").as(Integer.class), 4), criteriaBuilder.equal(root.get("star").as(Integer.class), 5)));
                }
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberAfterSaleHistoryDO> result = memberAfterSaleHistoryRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(h -> {
            MemberDetailCreditAfterSaleHistoryResp historyVO = new MemberDetailCreditAfterSaleHistoryResp();
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setStar(h.getStar());
            historyVO.setByMemberName(h.getMemberName());
            historyVO.setComment(h.getComment());
            historyVO.setProduct(h.getProduct());
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     *
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberDetailCreditComplainSummaryResp> getMemberDetailCreditComplainSummary(MemberRelationDO relationDO) {
        MemberDetailCreditComplainSummaryResp summaryVO = new MemberDetailCreditComplainSummaryResp();

        String countSqlStr = "select count(*) as total, sum(case when date_part('day',  localtimestamp - create_time) <=7 then 1 else 0 end) as in7days, sum(case when date_part('day',  localtimestamp - create_time) <=30 then 1 else 0 end) as in30days, sum(case when date_part('day',  localtimestamp - create_time) <=180 then 1 else 0 end) as in180days, sum(case when date_part('day',  localtimestamp - create_time) >180 then 1 else 0 end) as before180days from mem_member_complain_history ";
        String whereSqlStr = String.format("where member_id  = %d and role_id = %d and sub_member_id = %d and sub_role_id = %d ", relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        String groupSqlStr = "group by member_id, role_id, sub_member_id, sub_role_id";

        String sql = countSqlStr.concat(whereSqlStr).concat(groupSqlStr);

        ComplainSummaryDTO summaryDTO = new ComplainSummaryDTO(0,0,0,0,0);
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
        if(!CollectionUtils.isEmpty(result)) {
            Map<String, Object> map = result.get(0);
            summaryDTO.setTotal((long) map.get("total"));
            summaryDTO.setIn7days((long) map.get("in7days"));
            summaryDTO.setIn30days((long) map.get("in30days"));
            summaryDTO.setIn180days((long) map.get("in180days"));
            summaryDTO.setBefore180days((long) map.get("before180days"));
        }

        return WrapperUtil.success(summaryVO);
    }

    /**
     * 会员详情 - 会员信用 - 投诉汇总（平台层面汇总）
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditComplainSummaryResp getAllMemberDetailCreditComplainSummary(Long subMemberId, Long subRoleId) {
        MemberDetailCreditComplainSummaryResp summaryVO = new MemberDetailCreditComplainSummaryResp();

        List<MemberComplainHistoryDO> historyDOList = memberComplainHistoryRepository.findAllBySubMemberIdAndSubRoleId(subMemberId, subRoleId);

        //计算
        LocalDateTime last7days = LocalDateTime.now().minusDays(7);
        LocalDateTime last30days = LocalDateTime.now().minusDays(30);
        LocalDateTime last180days = LocalDateTime.now().minusDays(180);

        long countIn7Days = historyDOList.stream().filter(h -> h.getCreateTime().isAfter(last7days)).count();
        long countIn30Days = historyDOList.stream().filter(h -> h.getCreateTime().isAfter(last30days)).count();
        long countIn180Days = historyDOList.stream().filter(h -> h.getCreateTime().isAfter(last180days)).count();
        long countBefore180Days = historyDOList.stream().filter(h -> h.getCreateTime().isBefore(last180days)).count();
        long sum = countIn180Days + countBefore180Days;

        summaryVO.setLast7days(countIn7Days);
        summaryVO.setLast30days(countIn30Days);
        summaryVO.setLast180days(countIn180Days);
        summaryVO.setBefore180days(countBefore180Days);
        summaryVO.setSum(sum);

        return summaryVO;
    }

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     *
     * @param relationDO 会员上下级关系
     * @param current    当前分页
     * @param pageSize   每页行数
     * @return 查询结果
     */
    @Override
    public WrapperResp<PageDataResp<MemberDetailCreditComplainHistoryResp>> pageMemberDetailCreditComplainHistory(MemberRelationDO relationDO, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());
        Page<MemberComplainHistoryDO> result = memberComplainHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), page);

        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(h -> {
            MemberDetailCreditComplainHistoryResp historyVO = new MemberDetailCreditComplainHistoryResp();
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setContent(h.getContent());
            historyVO.setByMemberName(h.getMemberName());
            historyVO.setReason(h.getReason());
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList())));
    }

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录(平台层面汇总)
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param current     当前分页
     * @param pageSize    每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditComplainHistoryResp> pageAllMemberDetailCreditComplainHistory(Long subMemberId, Long subRoleId, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());
        Page<MemberComplainHistoryDO> result = memberComplainHistoryRepository.findAllBySubMemberIdAndSubRoleId(subMemberId, subRoleId, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(h -> {
            MemberDetailCreditComplainHistoryResp historyVO = new MemberDetailCreditComplainHistoryResp();
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setContent(h.getContent());
            historyVO.setByMemberName(h.getMemberName());
            historyVO.setReason(h.getReason());
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 分页查询会员组织机构
     *
     * @param memberId 会员Id
     * @param current  当前分页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberOrganizationQueryResp> pageMemberOrganizations(Long memberId, String code, String title, int current, int pageSize) {
        Pageable pageable = PageRequest.of(current -1, pageSize, Sort.by("id").ascending());
        Specification<MemberOrganizationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("member").as(MemberDO.class), memberId));
            if(StringUtils.hasLength(code)) {
                list.add(criteriaBuilder.like(root.get("code").as(String.class), "%" + code.trim() + "%"));
            }

            if(StringUtils.hasLength(title)) {
                list.add(criteriaBuilder.like(root.get("title").as(String.class), "%" + title.trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberOrganizationDO> pageList = memberOrganizationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(org -> {
            MemberOrganizationQueryResp queryVO = new MemberOrganizationQueryResp();
            queryVO.setId(org.getId());
            queryVO.setTitle(org.getTitle());
            queryVO.setCode(org.getCode());
            queryVO.setRemark(StringUtils.hasLength(org.getRemark()) ? org.getRemark() : "");
            return queryVO;
        }).collect(Collectors.toList()));
    }
}
