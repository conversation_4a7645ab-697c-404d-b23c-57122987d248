package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.member.model.req.maintenance.MemberOrganizationAddReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberOrganizationDeleteReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberOrganizationGetReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberOrganizationUpdateReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrgTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrganizationQueryResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 平台后台 - 会员组织机构服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
public interface IPlatformMemberOrganizationService {

    /**
     * 新增会员组织架构菜单
     * @param headers HttpHeaders信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void addMemberOrg(HttpHeaders headers, MemberOrganizationAddReq addVO);

    /**
     * 根据菜单Id，更新组织机构信息
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateMemberOrg(HttpHeaders headers, MemberOrganizationUpdateReq updateVO);

    /**
     * 删除一个会员组织架构
     * @param headers Http头部信息
     * @param deleteVO 接口参数
     * @return 操作结果
     */
    void deleteMemberOrg(HttpHeaders headers, MemberOrganizationDeleteReq deleteVO);

    /**
     * 查询一个会员组织架构
     * @param headers Http头部信息
     * @param getVO 接口参数
     * @return 操作结果
     */
    MemberOrganizationQueryResp getMemberOrg(HttpHeaders headers, MemberOrganizationGetReq getVO);

    /**
     * 查询会员的所有组织架构，以树形菜单的形式返回
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    List<MemberOrgTreeResp> treeMemberOrg(HttpHeaders headers);
}
