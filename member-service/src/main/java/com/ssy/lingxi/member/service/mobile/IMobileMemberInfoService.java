package com.ssy.lingxi.member.service.mobile;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.maintenance.MemberDetailCreditHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.mobile.*;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RegisterDetailGroupResp;
import com.ssy.lingxi.member.model.resp.basic.UserQueryResp;
import com.ssy.lingxi.member.model.resp.customer.*;
import com.ssy.lingxi.member.model.resp.info.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.mobile.MobileInfoApplyButtonResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileUpdateDepositDetailQueryResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileValidateHistoryResp;
import com.ssy.lingxi.member.model.resp.validate.ValidateStepResp;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * App - 会员信息管理查询服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-08
 */
public interface IMobileMemberInfoService {
    /**
     * “会员中心“ - 根据前端商城类型、属性，查询会员关系
     *
     * @param loginUser     登录用户
     * @param shopType      商城类型
     * @param shopProperty  商城属性
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级角色Id
     * @return 查询结果
     */
    MemberRelationDO findMobileMemberRelation(UserLoginCacheDTO loginUser, Integer shopType, Integer shopProperty, Long upperMemberId, Long upperRoleId);

    /**
     * 查找登录会员的logo
     *
     * @param loginUser 登录用户
     * @return 会员Logo
     */
    String findMemberLogo(UserLoginCacheDTO loginUser);

    /**
     * “我的-卡包” - 分页查询归属会员列表
     *
     * @param headers     Http头部信息
     * @param pageDataReq 接口参数
     * @return 查询结果
     */
    PageDataResp<MobileInfoLevelRightResp> pageUpperMemberLevelRights(HttpHeaders headers, PageDataReq pageDataReq);

    /**
     * “会员中心” - 查询当前会员的积分、权益信息
     *
     * @param headers Http头部信息
     * @param shopVO  接口参数
     * @return 查询结果
     */
    MobileLevelRightResp getMemberDetailLevelRight(HttpHeaders headers, MobileShopReq shopVO);

    /**
     * “会员中心” - 分页查询会员权益获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(HttpHeaders headers, MobileShopPageDataReq pageVO);

    /**
     * “会员中心” - 分页查询会员权益使用记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(HttpHeaders headers, MobileShopPageDataReq pageVO);

    /**
     * “会员中心” - 分页查询等级分获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(HttpHeaders headers, MobileShopPageDataReq pageVO);


    /**
     * 分页查询归属会员列表
     *
     * @param headers     Http头部信息
     * @param pageDataReq 接口参数
     * @return 查询结果
     */
    PageDataResp<UpperMemberInfoResp> pageUpperMembers(HttpHeaders headers, PageDataReq pageDataReq);

    /**
     * 会员详情 - 会员基本信息
     *
     * @param headers Http头部信息
     * @param shopVO  接口参数
     * @return 查询结果
     */
    MobileInfoBasicDetailResp getMemberBasicDetail(HttpHeaders headers, MobileShopReq shopVO);


    /**
     * 会员详情 - 会员诚信信息 - 会员基本信用信息
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    MemberDetailCreditResp getMemberDetailCredit(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    MobileCommentSummaryResp getMemberDetailCreditTradeCommentSummary(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    MobileCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberCreditComplaintPageQueryResp> pageMemberDetailCreditComplainHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 查询审批流程
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    ValidateStepResp getMobileOuterValidateSteps(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 分页查询审核历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MobileValidateHistoryResp> pageMobileMemberOuterValidateHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 获取“修改会员信息”页面，会员注册资料信息
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<RegisterDetailGroupResp> getMemberRegisterDetail(HttpHeaders headers);

    /**
     * 修改会员信息
     *
     * @param headers  Http头部信息
     * @param detailVO 接口参数
     */
    void updateMemberRegisterDetail(HttpHeaders headers, UpdateRegisterDetailReq detailVO);

    /**
     * 分页查询会员下属用户
     *
     * @param headers Http头部新
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<UserQueryResp> pageUsers(HttpHeaders headers, MobileUserPageDataReq pageVO);

    /**
     * “店铺会员”页面，查询“申请会员”按钮状态和文本
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 查询结果
     */
    MobileInfoApplyButtonResp getMobileApplyCondition(HttpHeaders headers, UpperMemberIdRoleIdReq upperMemberVO);

    /**
     * “店铺会员”页面，查询“入会享特权”的权益列表
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 查询结果
     */
    List<MemberDetailRightConfigResp> getUpperMemberRights(HttpHeaders headers, UpperMemberIdRoleIdReq upperMemberVO);

    /**
     * “店铺会员”页面，申请成为会员时，查询需要填写的入库资料
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 查询结果
     */
    List<RegisterDetailGroupResp> findMobileApplyDepositDetails(HttpHeaders headers, UpperMemberIdRoleIdReq upperMemberVO);

    /**
     * “店铺会员”页面，修改入库资料时，查询已有的入库资料信息
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 修改结果
     */
    MobileUpdateDepositDetailQueryResp getMemberDepositDetail(@RequestHeader HttpHeaders headers, @Valid UpperMemberIdRoleIdReq upperMemberVO);

    /**
     * “店铺会员”页面，当审核不通过时，修改入库资料
     *
     * @param headers         Http头部信息
     * @param depositDetailVO 接口参数
     * @param roleTag         角色标签
     */
    void updateDepositDetail(HttpHeaders headers, MobileUpdateDepositDetailReq depositDetailVO, Integer roleTag);

    /**
     * “申请会员” - 提交
     *
     * @param headers Http头部信息
     * @param subVO   接口参数
     */
    void applyToBeSubMember(HttpHeaders headers, MobileApplyForSubReq subVO);

    /**
     * “店铺会员” - 查询当前会员的等级、权益信息
     *
     * @param headers                Http头部信息
     * @param upperMemberIdRoleIdReq 接口参数
     * @return 查询结果
     */
    MobileLevelRightResp getShopMemberDetailLevelRight(HttpHeaders headers, UpperMemberIdRoleIdReq upperMemberIdRoleIdReq);

    /**
     * “店铺会员”  - 分页查询会员权益获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightHistoryResp> pageShopMemberDetailRightHistory(HttpHeaders headers, MobileUpperPageDataReq pageVO);

    /**
     * “店铺会员”  - 分页查询会员权益使用记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightSpendHistoryResp> pageShopMemberDetailRightSpendHistory(HttpHeaders headers, MobileUpperPageDataReq pageVO);

    /**
     * “店铺会员”  - 分页查询活跃分获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailLevelHistoryResp> pageShopMemberLevelDetailHistory(HttpHeaders headers, MobileUpperPageDataReq pageVO);

    /**
     * 企业认证 - 获取会员注册资料
     *
     * @param loginUser 登录用户
     * @return 查询结果
     */
    List<MemberConfigGroupResp> getCorporationCertificationDetail(UserLoginCacheDTO loginUser);

    /**
     * 保存企业认证信息草稿
     *
     * @param loginUser 登录用户
     * @param req       请求参数
     */
    void saveCorporationCertificationDraft(UserLoginCacheDTO loginUser, CorporationCertificationDraftReq req);

    /**
     * 获取会员企业认证信息草稿
     *
     * @return 会员企业认证信息草稿
     */
    Map<String, Object> getCorporationCertificationDraft(UserLoginCacheDTO loginUser);

    /**
     * 企业认证
     *
     * @param loginUser 登录用户
     * @param req       请求参数
     */
    void corporationCertification(UserLoginCacheDTO loginUser, CertificationCertificationReq req);

    /**
     * 获取认证资料详情
     *
     * @param corporationId 企业认证id
     * @return 查询结果
     */
    List<RegisterDetailGroupResp> getMemberCorporationCertificationDetail(UserLoginCacheDTO sysUser, Long corporationId);

    /**
     * 企业认证变更
     *
     * @param loginUser 登录用户
     * @param req       请求参数
     */
    void updateCorporationCertification(UserLoginCacheDTO loginUser, CertificationCertificationUpdateReq req);

    /**
     * 获取企业认证信息
     *
     * @param loginUser 登录用户
     * @return 企业认证信息
     */
    CorporationResp getCorporationCertificationInfo(UserLoginCacheDTO loginUser);

    /**
     * 获取企业认证信息
     *
     * @param loginUser 登录用户
     * @param corporationId 企业id
     * @return 企业认证信息
     */
    CorporationResp getCorporationCertificationInfoById(UserLoginCacheDTO loginUser, Long corporationId);

    /**
     * 企业主体列表
     *
     * @return 企业主体列表
     */
    CorporationInfoResp listCorporation(UserLoginCacheDTO loginUser);

    /**
     * 查询品牌客户列表
     * @return 品牌客户列表
     */
    List<BrandResp> listBrandCustomer(UserLoginCacheDTO loginUser);

    /**
     * 品牌客户列表
     *
     * @param loginUser 登录用户
     * @return 品牌客户列表
     */
    List<BrandCustomerResp> listBrandCustomerByCorporationId(UserLoginCacheDTO loginUser, Long corporationId);

    /**
     * 新增企业主体
     *
     * @param req 添加参数
     */
    void addCorporation(UserLoginCacheDTO sysUser, CorporationAddReq req);

    /**
     * 新增品牌会员
     *
     * @param req 请求参数
     */
    void addBrandMember(UserLoginCacheDTO sysUser, BrandMemberAddReq req);

    /**
     * 获取企业认证状态
     *
     * @param loginUser 登录用户
     * @return 企业认证状态
     */
    CorporationCertificationStatusResp getCorporationCertificationStatus(UserLoginCacheDTO loginUser);

    /**
     * 查询品牌会员
     *
     * @return 品牌会员
     */
    BrandCustomerResp getBrandMember(UserLoginCacheDTO sysUser, Long memberId);

    /**
     * 品牌会员编辑
     *
     * @param req 接口参数
     */
    void brandMemberUpdate(UserLoginCacheDTO sysUser, BrandMemberUpdateReq req);


}
