package com.ssy.lingxi.member.controller.web;


import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.comment.MemberLifeCycleCheckOrderPermissionReq;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberLifecycleRuleService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员能力 - 会员生命周期规则
 * <AUTHOR>
 * @since 2022/7/15 15:40
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/lifecycle/rule")
public class MemberLifecycleRuleController {

    @Resource
    private IMemberLifecycleRuleService memberLifecycleStagesService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    /**
     * 当上级配置生命周期时，校验下级会员是否有下单权限
     *
     * @param headers 请求头
     * @param memberLifeCycleCheckOrderPermission 请求参数
     * @return 返回
     */
    @PostMapping("/check/sub-member/order/permission")
    public WrapperResp<Boolean> checkSubMemberOrderPermission(@RequestHeader HttpHeaders headers,
                                                              @RequestBody @Valid MemberLifeCycleCheckOrderPermissionReq memberLifeCycleCheckOrderPermission) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberLifecycleStagesService.checkSubMemberOrderPermission(loginCacheDTO, memberLifeCycleCheckOrderPermission));
    }
}
