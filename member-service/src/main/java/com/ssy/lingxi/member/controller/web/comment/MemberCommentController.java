package com.ssy.lingxi.member.controller.web.comment;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.enums.MemberTradeTypeEnum;
import com.ssy.lingxi.member.model.req.comment.*;
import com.ssy.lingxi.member.model.resp.comment.MemberOrderCommentDetailResp;
import com.ssy.lingxi.member.model.resp.comment.MemberReceiveTradeCommentPageResp;
import com.ssy.lingxi.member.model.resp.comment.MemberSendTradeCommentPageResp;
import com.ssy.lingxi.member.model.resp.comment.MemberTradeCommentDetailResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditCommentSummaryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditTradeHistoryResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.comment.IMemberCommentService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 交易能力-会员评价接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/comment")
public class MemberCommentController {
    @Resource
    private IMemberCommentService memberCommentService;
    @Resource
    private IBaseMemberCacheService memberCacheService;

    // ====================采购会员========================

    /**
     * 交易能力-采购会员评价管理-交易评价汇总
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/consumer/count/trade/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> getSubMemberTradeCommentSummary(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.getSubMemberTradeCommentSummary(loginUser, MemberTradeTypeEnum.SELLER));
    }

    /**
     * 交易能力-采购会员评价管理-分页查询交易评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/consumer/count/trade/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditTradeHistoryResp>> pageMemberTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.pageSubMemberTradeCommentHistory(loginUser, MemberTradeTypeEnum.SELLER, pageVO));
    }

    /**
     * 交易能力-采购会员评价管理-待评价订单-评价详情
     * @param headers Http头部信息
     * @param commonIdReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/consumer/order/trade/detail")
    public WrapperResp<MemberOrderCommentDetailResp> getMemberOrderCommentDetail(@RequestHeader HttpHeaders headers, @Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.getMemberOrderCommentDetail(loginUser, MemberTradeTypeEnum.BUYER, commonIdReq));
    }

    /**
     * 交易能力-采购会员评价管理-待评价订单-发表评价
     * @param headers Http头部信息
     * @param memberTradeCommentSubmitReq 接口参数
     * @return 查询结果
     */
    @PostMapping("/consumer/order/trade/submit")
    public WrapperResp<Void> submitTradeComment(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberTradeCommentSubmitReq memberTradeCommentSubmitReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberCommentService.submitMemberTradeComment(loginUser, MemberTradeTypeEnum.BUYER, memberTradeCommentSubmitReq);
        return WrapperUtil.success();
    }

    /**
     * 交易能力-采购会员评价管理-收到评价-收到评价分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/consumer/receive/trade/history/page")
    public WrapperResp<PageDataResp<MemberReceiveTradeCommentPageResp>> pageReceiveMemberTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberReceiveTradeCommentDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.pageMemberReceiveTradeCommentHistory(loginUser, MemberTradeTypeEnum.SELLER, pageVO));
    }

    /**
     * 交易能力-采购会员评价管理-收到评价-收到评价详情
     * @param headers Http头部信息
     * @param tradeCommentIdVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/consumer/receive/trade/history/get")
    public WrapperResp<MemberTradeCommentDetailResp> getMemberReceivedTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberTradeCommentIdReq tradeCommentIdVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.getMemberTradeCommentHistory(loginUser, tradeCommentIdVO));
    }

    /**
     * 交易能力-采购会员评价管理-发出评价-发出评价分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/consumer/send/trade/history/page")
    public WrapperResp<PageDataResp<MemberSendTradeCommentPageResp>> pageMemberSendTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberSendTradeCommentDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.pageMemberSendTradeCommentHistory(loginUser, MemberTradeTypeEnum.BUYER, pageVO));
    }

    /**
     * 交易能力-采购会员评价管理-发出评价-发出评价详情
     * @param headers Http头部信息
     * @param tradeCommentIdVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/consumer/send/trade/history/get")
    public WrapperResp<MemberTradeCommentDetailResp> getMemberSendTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberTradeCommentIdReq tradeCommentIdVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.getMemberTradeCommentHistory(loginUser, tradeCommentIdVO));
    }

    /**
     * 交易能力-采购会员评价管理-发出评价-修改评价详情
     * @param headers Http头部信息
     * @param tradeCommentSubmitVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/consumer/send/trade/history/update")
    public WrapperResp<Void> updateMemberTradeCommentHistory(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberTradeCommentUpdateDetailReq tradeCommentSubmitVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberCommentService.updateMemberTradeCommentHistory(tradeCommentSubmitVO);
        return WrapperUtil.success();
    }

    // ====================供应会员========================

    /**
     * 交易能力-供应会员评价管理-交易评价汇总
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/supply/count/trade/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> getSupplyMemberTradeCommentSummary(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.getSubMemberTradeCommentSummary(loginUser, MemberTradeTypeEnum.BUYER));
    }

    /**
     *
     * 交易能力-供应会员评价管理-分页查询交易评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/supply/count/trade/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditTradeHistoryResp>> pageSupplyMemberTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.pageSubMemberTradeCommentHistory(loginUser, MemberTradeTypeEnum.BUYER, pageVO));
    }

    /**
     * 交易能力-供应会员评价管理-待评价订单-评价详情
     * @param headers Http头部信息
     * @param commonIdReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/supply/order/trade/detail")
    public WrapperResp<MemberOrderCommentDetailResp> getSupplyMemberOrderCommentDetail(@RequestHeader HttpHeaders headers, @Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.getMemberOrderCommentDetail(loginUser, MemberTradeTypeEnum.SELLER, commonIdReq));
    }

    /**
     * 交易能力-供应会员评价管理-待评价订单-发表评价
     * @param headers Http头部信息
     * @param memberTradeCommentSubmitReq 接口参数
     * @return 查询结果
     */
    @PostMapping("/supply/order/trade/submit")
    public WrapperResp<Void> submitSupplyTradeComment(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberTradeCommentSubmitReq memberTradeCommentSubmitReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberCommentService.submitMemberTradeComment(loginUser, MemberTradeTypeEnum.SELLER, memberTradeCommentSubmitReq);
        return WrapperUtil.success();
    }

    /**
     * 交易能力-供会应员评价管理-收到评价-收到评价分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/supply/receive/trade/history/page")
    public WrapperResp<PageDataResp<MemberReceiveTradeCommentPageResp>> pageSupplyReceiveMemberTradeCommentHistory(@RequestHeader HttpHeaders headers, MemberReceiveTradeCommentDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.pageMemberReceiveTradeCommentHistory(loginUser, MemberTradeTypeEnum.BUYER, pageVO));
    }

    /**
     * 交易能力-供应会员评价管理-收到评价-解释回复
     * @param headers Http头部信息
     * @param memberTradeCommentSubmitVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/supply/receive/trade/history/reply")
    public WrapperResp<Void> replySupplyReceiveMemberTradeCommentHistory(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberTradeReceiveCommentReplyReq memberTradeCommentSubmitVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberCommentService.replyReceiveMemberTradeCommentHistory(loginUser, memberTradeCommentSubmitVO);
        return WrapperUtil.success();
    }

    /**
     * 交易能力-供应会员评价管理-收到评价-收到评价详情
     * @param headers Http头部信息
     * @param tradeCommentIdVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/supply/receive/trade/history/get")
    public WrapperResp<MemberTradeCommentDetailResp> getSupplyMemberReceivedTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberTradeCommentIdReq tradeCommentIdVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.getMemberTradeCommentHistory(loginUser, tradeCommentIdVO));
    }

    /**
     * 交易能力-供应会员评价管理-发出评价分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/supply/send/trade/history/page")
    public WrapperResp<PageDataResp<MemberSendTradeCommentPageResp>> pageSupplyMemberSendTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberSendTradeCommentDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.pageMemberSendTradeCommentHistory(loginUser, MemberTradeTypeEnum.SELLER, pageVO));
    }

    /**
     * 交易能力-供应会员评价管理-发出评价-发出评价详情
     * @param headers Http头部信息
     * @param tradeCommentIdVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/supply/send/trade/history/get")
    public WrapperResp<MemberTradeCommentDetailResp> getSupplyMemberSendTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberTradeCommentIdReq tradeCommentIdVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberCommentService.getMemberTradeCommentHistory(loginUser, tradeCommentIdVO));
    }

    /**
     * 交易能力-供应会员评价管理-发出评价-修改评价详情
     * @param headers Http头部信息
     * @param tradeCommentSubmitVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/supply/send/trade/history/update")
    public WrapperResp<Void> updateMemberSendTradeCommentHistory(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberTradeCommentUpdateDetailReq tradeCommentSubmitVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberCommentService.updateMemberTradeCommentHistory(tradeCommentSubmitVO);
        return WrapperUtil.success();
    }

    /**
     * 交易能力-供应会员评价管理-收到评价-修改是否显示商品评价
     * @param headers  Http头部信息
     * @param updateVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/supply/receive/show/evaluation/update")
    public WrapperResp<Void> updateSupplyReceiveShowEvaluation(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberTradeCommentUpdateStatusReq updateVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        memberCommentService.updateSupplyReceiveShowEvaluation(updateVO);
        return WrapperUtil.success();
    }
}
