package com.ssy.lingxi.member.model.req.validate;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 批量审核接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-22
 */
@Data
public class ValidateIdsReq implements Serializable {
    private static final long serialVersionUID = 8641855064205369081L;

    /**
     * 审核内容Id列表
     */
    @NotEmpty(message = "审核内容Id列表不能为空")
    List<Long> validateIds;
}
