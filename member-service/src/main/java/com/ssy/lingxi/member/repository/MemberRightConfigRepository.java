package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.MemberRightConfigDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员权益操作Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-07
 */
@Repository
public interface MemberRightConfigRepository extends JpaRepository<MemberRightConfigDO, Long>, JpaSpecificationExecutor<MemberRightConfigDO> {
    @Transactional
    void deleteByMemberIdAndRoleId(Long memberId, Long roleId);
}
