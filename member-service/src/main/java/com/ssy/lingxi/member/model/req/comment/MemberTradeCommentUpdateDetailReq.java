package com.ssy.lingxi.member.model.req.comment;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 订单评价接口修改参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@Data
public class MemberTradeCommentUpdateDetailReq implements Serializable {
    private static final long serialVersionUID = -141775102712592171L;

    /**
     * 记录id
     */
    @NotNull(message = "记录id不能为空")
    private Long id;

    /**
     * 评价星级（1-5）
     */
    @NotNull(message = "评价星级（1-5）不能为空")
    @Min(value = 1, message = "评价星级范围为1-5星")
    @Max(value = 5, message = "评价星级范围为1-5星")
    private Integer star;

    /**
     * 评价内容
     */
//    @NotBlank(message = "评价内容不能为空")
    private String comment = "";

    /**
     *  评价图片
     */
    private List<String> pics;
}