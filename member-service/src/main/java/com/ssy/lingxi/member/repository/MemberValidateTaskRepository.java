package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.validate.MemberValidateTaskDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员审核任务Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-18
 */
@Repository
public interface MemberValidateTaskRepository extends JpaRepository<MemberValidateTaskDO, Long>, JpaSpecificationExecutor<MemberValidateTaskDO> {
}
