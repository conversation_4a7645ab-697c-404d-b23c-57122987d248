package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.validate.MemberValidateHistoryDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员审核历史记录
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-14
 */
@Repository
public interface MemberValidateHistoryRepository extends JpaRepository<MemberValidateHistoryDO, Long>, JpaSpecificationExecutor<MemberValidateHistoryDO> {

    List<MemberValidateHistoryDO> findAllByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Sort sort);

    Page<MemberValidateHistoryDO> findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Pageable page);

    List<MemberValidateHistoryDO> findBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    @Transactional
    void deleteAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    @Transactional
    void deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);
}
