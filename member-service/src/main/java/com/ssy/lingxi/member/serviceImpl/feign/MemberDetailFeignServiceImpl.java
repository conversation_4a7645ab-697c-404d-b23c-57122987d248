package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberClassificationService;
import com.ssy.lingxi.member.service.feign.IMemberDetailFeignService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员、用户信息相关内部Feign服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-06-03
 */
@Service
public class MemberDetailFeignServiceImpl implements IMemberDetailFeignService {
    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private IBaseMemberClassificationService baseMemberClassificationService;

    /**
     * 查询用户Web端菜单权限Url列表
     *
     * @param userId 用户Id
     * @return 查询结果
     */
    @Override
    public List<String> getUserWebAuthUrls(Long userId, Long memberId, Long roleId) {
        // TODO: 2023/8/11 暂不兼容首页统计的path
//        //查询会员用户的所有权限
//        List<AuthBO> loginAuth = memberLoginService.authList(memberId,roleId,userId);
//        //构建会员权限数（过滤无根节点）
//        List<MenuAuthResp> authTree = memberLoginService.createAuthTree(loginAuth);
//        //平铺树
//        List<MenuAuthResp> authList = memberLoginService.createAuthListByTree(authTree);
//        return Wrapper.success(authList.stream().map(MenuAuthResp::getPath).collect(Collectors.toList()));
        return new ArrayList<>();
    }

    /**
     * 根据会员id查询用户
     * @param memberIds 会员id
     * @return 查询结果
     */
    @Override
    public List<MemberFeignMsgByMemberIdResp> getUserByMemberIds(List<Long> memberIds) {

        Specification<UserDO> specification = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(cb.and(root.get("member").as(MemberDO.class).in(memberIds)));

            Predicate[] p = new Predicate[list.size()];
            return cb.and(list.toArray(p));
        };

        List<UserDO> userDOList = userRepository.findAll(specification);

        return userDOList.stream().map(userDO -> {
            MemberFeignMsgByMemberIdResp memberFeignMsgByMemberIdResp = new MemberFeignMsgByMemberIdResp();
            memberFeignMsgByMemberIdResp.setMemberId(userDO.getMember().getId());
            memberFeignMsgByMemberIdResp.setUserId(userDO.getId());
            return memberFeignMsgByMemberIdResp;
        }).collect(Collectors.toList());

    }

    /**
     * 根据角色id查询用户
     * @param roleIds 角色id
     * @return 查询结果
     */
    @Override
    public List<MemberFeignMsgByRoleIdResp> getUserByRoleIds(List<Long> roleIds) {
        Specification<UserDO> specification = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();

            Join<UserDO, MemberDO> memberJoin = root.join("member", JoinType.LEFT);
            Join<MemberDO, MemberRoleDO> roleJoin = memberJoin.join("memberRoles", JoinType.LEFT);
            list.add(cb.and(roleJoin.get("id").as(Long.class).in(roleIds)));

            Predicate[] p = new Predicate[list.size()];
            return cb.and(list.toArray(p));
        };

        List<UserDO> userDOList = userRepository.findAll(specification);

        List<MemberFeignMsgByRoleIdResp> resultList = userDOList.stream()
                .flatMap(userDO -> userDO.getMember().getMemberRoles().stream()
                        .filter(memberRoleDO -> roleIds.contains(memberRoleDO.getId()))
                        .map(memberRoleDO -> {
                            MemberFeignMsgByRoleIdResp memberFeignMsgByRoleIdResp = new MemberFeignMsgByRoleIdResp();
                            memberFeignMsgByRoleIdResp.setMemberId(userDO.getMember().getId());
                            memberFeignMsgByRoleIdResp.setRoleId(memberRoleDO.getId());
                            memberFeignMsgByRoleIdResp.setUserId(userDO.getId());
                            return memberFeignMsgByRoleIdResp;
                        })
                ).collect(Collectors.toList());

        return resultList;
    }

    /**
     * 结算能力 - 查询入库分类信息中，主营品类及结算方式
     *
     * @param relationVO 接口参数
     * @return 查询结果，如无配置返回空列表
     */
    @Override
    public List<MemberCategoryFeignResp> findMemberBusinessCategories(MemberFeignRelationReq relationVO) {
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationVO.getUpperMemberId(), relationVO.getUpperRoleId(), relationVO.getSubMemberId(), relationVO.getSubRoleId());
        if(relationDO == null || !relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode())) {
            return new ArrayList<>();
        }

        return baseMemberClassificationService.findMemberBusinessCategories(relationDO);
    }

    /**
     * 根据用户Id查询用户信息
     *
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberFeignUserDetailResp findMemberUser(MemberFeignUserIdReq idVO) {
        UserDO userDO = userRepository.findById(idVO.getUserId()).orElse(null);
        if(userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        MemberFeignUserDetailResp detailVO = new MemberFeignUserDetailResp();
        detailVO.setUserId(userDO.getId());
        detailVO.setUserName(userDO.getName());
        detailVO.setJobTitle(userDO.getOrg() == null ? "" : userDO.getOrg().getTitle());
        detailVO.setJobTitle(userDO.getJobTitle());

        return detailVO;
    }

    /**
     * 根据会员id和角色id，查询会员角色为服务提供者的下级会员列表
     * @param memberVO 上级会员信息
     * @return 查询结果
     */
    @Override
    public List<MemberManageQueryResp> subordinateMemberList(MemberFeignSubordinateMemberReq memberVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), memberVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), memberVO.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification, Sort.by("id").ascending());
        return relationDOList.stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getSubMemberId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleId(p.getSubRoleId());
            queryVO.setRoleName(p.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据会员id和角色id，查询会员角色为服务消费者的上级会员列表
     * @param memberVO 下级会员信息
     * @return 查询结果
     */
    @Override
    public List<MemberManageQueryResp> superiorMemberList(MemberFeignSuperiorMemberReq memberVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), memberVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), memberVO.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            Join<Object, Object> subRoleJoin = root.join("role", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification, Sort.by("id").ascending());
        return relationDOList.stream().map(p -> {
            MemberManageQueryResp queryVO = new MemberManageQueryResp();
            queryVO.setId(p.getId());
            queryVO.setMemberId(p.getMemberId());
            queryVO.setName(p.getMember().getName());
            queryVO.setRoleId(p.getRoleId());
            queryVO.setRoleName(p.getRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 是否具有支付及查看订单价格权限
     * @param req 请求参数
     * @return 查询结果
     */
    @Override
    public Boolean hasOrderAuth(UserIdFeignReq req) {
        UserDO userDO = userRepository.findById(req.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));
        if (Objects.equals(userDO.getUserType(), UserTypeEnum.ADMIN.getCode())) {
            return true;
        }
        return BooleanUtils.isTrue(userDO.isHasOrderAuth());
    }

}
