package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 会员能力 - 审核入库 - 分页查询会员列表接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberDepositPageDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = 2464415639472006011L;

    /**
     * 会员名称
     */
    private String name;

    /**
     * 会员类型Id
     */
    private Long memberType;

    /**
     * 角色Id
     */
    private Long roleId;

    /**
     * 注册来源
     */
    private Integer source;

    /**
     * 内部状态
     */
    private Integer innerStatus;

    /**
     * 账户名称
     */
    private String account;
}
