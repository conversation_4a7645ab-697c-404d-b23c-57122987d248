package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.maintenance.MemberDetailCreditHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import org.springframework.http.HttpHeaders;

/**
 * 平台后台 - 会员维护 - 会员详情 - 信用信息服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
public interface IPlatformMemberDetailCreditService {

    /**
     * 查询会员详情 - 会员信用信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailCreditResp getMemberDetailCredit(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员信用 - 交易评价汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO);


    /**
     * 会员详情 - 会员信用 - 售后评价汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailCreditComplainHistoryResp> pageMemberDetailCreditComplainHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);
}
