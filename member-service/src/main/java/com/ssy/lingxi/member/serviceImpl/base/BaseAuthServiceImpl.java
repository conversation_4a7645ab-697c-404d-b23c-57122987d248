package com.ssy.lingxi.member.serviceImpl.base;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.common.util.TreeUtils;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.entity.bo.AuthBO;
import com.ssy.lingxi.member.entity.bo.ButtonAuthBO;
import com.ssy.lingxi.member.entity.bo.MenuAuthBO;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.menuAuth.*;
import com.ssy.lingxi.member.model.dto.rebuildAuth.*;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeNodeResp;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.repository.UserRoleRepository;
import com.ssy.lingxi.member.service.base.IBaseAuthService;
import com.ssy.lingxi.member.service.base.IBaseTokenManageService;
import lombok.extern.slf4j.Slf4j;
import org.roaringbitmap.RoaringBitmap;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (内部)会员、用户菜单权限、数据权限、渠道权限服务接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-15
 */
@Service
@Slf4j
public class BaseAuthServiceImpl implements IBaseAuthService {
    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private IBaseTokenManageService tokenManageService;

    @Override
    public Set<Long> getMenuIdSetBySource(Integer source) {
        QMenuDO qMenuDO = QMenuDO.menuDO;

        JPAQuery<Long> jpaQuery = jpaQueryFactory.select(qMenuDO.id)
                .from(qMenuDO);

        if (source > 0) {
            jpaQuery.where(qMenuDO.source.eq(source));
        }

        return new HashSet<>(jpaQuery.fetch());
    }

    @Override
    public Set<Long> getButtonIdSetByMenuSource(Integer source) {
        QMenuDO qMenuDO = QMenuDO.menuDO;
        QButtonDO qButtonDO = QButtonDO.buttonDO;

        JPAQuery<Long> jpaQuery = jpaQueryFactory.select(qButtonDO.id)
                .from(qMenuDO)
                .from(qButtonDO)
                .where(qMenuDO.id.eq(qButtonDO.menu.id));

        if (source > 0) {
            jpaQuery.where(qMenuDO.source.eq(source));
        }

        return new HashSet<>(jpaQuery.fetch());
    }

    @Override
    public Set<MenuAuthBO> getMenuSetBySource(Integer source, String language) {
        QMenuDO qMenuDO = QMenuDO.menuDO;
        QMenuNameDO qMenuNameDO = QMenuNameDO.menuNameDO;

        JPAQuery<MenuAuthBO> jpaQuery = jpaQueryFactory.select(Projections.fields(MenuAuthBO.class, qMenuDO.id, qMenuDO.parentId, qMenuDO.code, qMenuNameDO.name, qMenuDO.path, qMenuDO.sort, qMenuDO.level, qMenuDO.source))
                .from(qMenuDO)
                .innerJoin(qMenuNameDO)
                .on(qMenuDO.id.eq(qMenuNameDO.menuId))
                .where(qMenuNameDO.language.eq(language));

        if (source > 0) {
            jpaQuery.where(qMenuDO.source.eq(source));
        }

        return new HashSet<>(jpaQuery.fetch());
    }

    @Override
    public Set<ButtonAuthBO> getButtonSetBySource(Integer source, String language) {
        QMenuDO qMenuDO = QMenuDO.menuDO;
        QButtonDO qButtonDO = QButtonDO.buttonDO;
        QButtonNameDO qButtonNameDO = QButtonNameDO.buttonNameDO;

        JPAQuery<ButtonAuthBO> jpaQuery = jpaQueryFactory.select(Projections.fields(ButtonAuthBO.class, qButtonDO.id, qButtonDO.code, qButtonDO.menu.id.as("menuId"), qButtonNameDO.name, qButtonDO.path))
                .from(qButtonDO)
                .innerJoin(qMenuDO)
                .on(qMenuDO.id.eq(qButtonDO.menu.id))
                .innerJoin(qButtonNameDO)
                .on(qButtonDO.id.eq(qButtonNameDO.buttonId))
                .where(qButtonNameDO.language.eq(language));

        if (source > 0) {
            jpaQuery.where(qMenuDO.source.eq(source));
        }

        return new HashSet<>(jpaQuery.fetch());
    }

    /**
     * 下面两个根据菜单和按钮id做in查询，后期可以做优化
     */
    @Override
    public Set<MenuAuthBO> getMenuSetByIdSet(Set<Long> menuIdSet, String language) {
        QMenuDO qMenuDO = QMenuDO.menuDO;
        QMenuNameDO qMenuNameDO = QMenuNameDO.menuNameDO;

        JPAQuery<MenuAuthBO> jpaQuery = jpaQueryFactory.select(Projections.fields(MenuAuthBO.class, qMenuDO.id, qMenuDO.parentId, qMenuDO.code, qMenuNameDO.name, qMenuDO.path, qMenuDO.sort, qMenuDO.level, qMenuDO.source))
                .from(qMenuDO)
                .innerJoin(qMenuNameDO)
                .on(qMenuDO.id.eq(qMenuNameDO.menuId))
                .where(qMenuNameDO.language.eq(language))
                .where(qMenuDO.id.in(menuIdSet));

        return new HashSet<>(jpaQuery.fetch());
    }

    @Override
    public Set<ButtonAuthBO> getButtonSetByIdSet(Set<Long> buttonIdSet, String language) {
        QButtonDO qButtonDO = QButtonDO.buttonDO;
        QButtonNameDO qButtonNameDO = QButtonNameDO.buttonNameDO;

        JPAQuery<ButtonAuthBO> jpaQuery = jpaQueryFactory.select(Projections.fields(ButtonAuthBO.class, qButtonDO.id, qButtonDO.code, qButtonDO.menu.id.as("menuId"), qButtonNameDO.name, qButtonDO.path))
                .from(qButtonDO)
                .innerJoin(qButtonNameDO)
                .on(qButtonDO.id.eq(qButtonNameDO.buttonId))
                .where(qButtonNameDO.language.eq(language))
                .where(qButtonDO.id.in(buttonIdSet));

        return new HashSet<>(jpaQuery.fetch());
    }

    @Override
    public Set<MenuAuthBO> getMenuSetByPathSet(Set<String> menuPathSet, String language) {
        QMenuDO qMenuDO = QMenuDO.menuDO;
        QMenuNameDO qMenuNameDO = QMenuNameDO.menuNameDO;

        JPAQuery<MenuAuthBO> jpaQuery = jpaQueryFactory.select(Projections.fields(MenuAuthBO.class, qMenuDO.id, qMenuDO.parentId, qMenuDO.code, qMenuNameDO.name, qMenuDO.path, qMenuDO.sort, qMenuDO.level, qMenuDO.source))
                .from(qMenuDO)
                .innerJoin(qMenuNameDO)
                .on(qMenuDO.id.eq(qMenuNameDO.menuId))
                .where(qMenuNameDO.language.eq(language))
                .where(qMenuDO.path.in(menuPathSet));

        return new HashSet<>(jpaQuery.fetch());
    }

    @Override
    public Set<ButtonAuthBO> getButtonSetByPathSet(Set<String> buttonPathSet, String language) {
        QButtonDO qButtonDO = QButtonDO.buttonDO;
        QButtonNameDO qButtonNameDO = QButtonNameDO.buttonNameDO;

        JPAQuery<ButtonAuthBO> jpaQuery = jpaQueryFactory.select(Projections.fields(ButtonAuthBO.class, qButtonDO.id, qButtonDO.code, qButtonDO.menu.id.as("menuId"), qButtonNameDO.name, qButtonDO.path))
                .from(qButtonDO)
                .innerJoin(qButtonNameDO)
                .on(qButtonDO.id.eq(qButtonNameDO.buttonId))
                .where(qButtonNameDO.language.eq(language))
                .where(qButtonDO.path.in(buttonPathSet));

        return new HashSet<>(jpaQuery.fetch());
    }

    @Override
    public AuthBO getAuthByUserId(Long userId) {
        QUserDO qUserDO = QUserDO.userDO;

        JPAQuery<AuthBO> jpaQuery = jpaQueryFactory.select(Projections.fields(AuthBO.class, qUserDO.menuAuth, qUserDO.buttonAuth, qUserDO.apiAuth))
                .from(qUserDO)
                .where(qUserDO.id.eq(userId));

        return jpaQuery.fetchFirst();
    }

    @Override
    public AuthBO getAuthByRelId(Long relId) {
        QMemberRelationDO qMemberRelationDO = QMemberRelationDO.memberRelationDO;

        JPAQuery<AuthBO> jpaQuery = jpaQueryFactory.select(Projections.fields(AuthBO.class, qMemberRelationDO.menuAuth, qMemberRelationDO.buttonAuth, qMemberRelationDO.apiAuth))
                .from(qMemberRelationDO)
                .where(qMemberRelationDO.id.eq(relId));

        return jpaQuery.fetchFirst();
    }

    @Override
    public AuthTreeResp getAuthTree(byte[] menuAuth, byte[] buttonAuth, CompletableFuture<Set<MenuAuthBO>> menuAuthBOSetFuture, CompletableFuture<Set<ButtonAuthBO>> buttonAuthBOSetFuture) {
        // 根据菜单信息获取树形菜单按钮集合（扁平结构，未递归构建）
        List<AuthTreeNodeResp> authTreeNodeRespList = new ArrayList<>();

        // 数据库里对应端的菜单数据，map<菜单id，菜单info>
        Map<Long, MenuAuthBO> menuAuthBOMap = menuAuthBOSetFuture.join().stream().collect(Collectors.toMap(MenuAuthBO::getId, Function.identity()));

        // 组装菜单树节点
        menuAuthBOMap.entrySet().removeIf(menuAuthBOEntry -> {
            // 父菜单存在或者是第一级菜单,则存入authTreeNodeRespList，否则就说明菜单已经不需要了，则从集合中移除
            MenuAuthBO menuAuthBO = menuAuthBOEntry.getValue();
            MenuAuthBO parentMenuAuthBO = menuAuthBOMap.get(menuAuthBO.getParentId());
            if (Objects.nonNull(parentMenuAuthBO) || menuAuthBO.getParentId() == 0L) {// 父菜单存在或者是第一级菜单
                AuthTreeNodeResp menuConfigResp = new AuthTreeNodeResp();
                menuConfigResp.setCode(menuAuthBO.getCode());
                menuConfigResp.setParentCode(Optional.ofNullable(parentMenuAuthBO).map(MenuAuthBO::getCode).orElse("0"));
                menuConfigResp.setRealId(menuAuthBO.getId());
                menuConfigResp.setName(menuAuthBO.getName());
                menuConfigResp.setPath(menuAuthBO.getPath());
                menuConfigResp.setSort(menuAuthBO.getSort());
                menuConfigResp.setIsBtn(false);
                authTreeNodeRespList.add(menuConfigResp);
                return false;
            } else {
                return true;
            }
        });

        // 数据库里对应端的按钮数据，map<按钮id，按钮info>
        Map<Long, ButtonAuthBO> buttonAuthBOMap = buttonAuthBOSetFuture.join().stream().collect(Collectors.toMap(ButtonAuthBO::getId, Function.identity()));

        // 组装按钮树节点
        buttonAuthBOMap.entrySet().removeIf(buttonAuthBOEntry -> {
            // 父菜单存在,则存入authTreeNodeRespList，否则就说明按钮已经不需要了，则从集合中移除
            ButtonAuthBO buttonAuthBO = buttonAuthBOEntry.getValue();
            MenuAuthBO parentMenuAuthBO = menuAuthBOMap.get(buttonAuthBO.getMenuId());// 按钮挂载在菜单上，菜单编码即为按钮的父编码
            if (Objects.nonNull(parentMenuAuthBO)) {
                AuthTreeNodeResp buttonConfigResp = new AuthTreeNodeResp();
                buttonConfigResp.setCode(buttonAuthBO.getCode());
                buttonConfigResp.setParentCode(parentMenuAuthBO.getCode());
                buttonConfigResp.setRealId(buttonAuthBO.getId());
                buttonConfigResp.setName(buttonAuthBO.getName());
                buttonConfigResp.setPath(buttonAuthBO.getPath());
                buttonConfigResp.setSort(0);
                buttonConfigResp.setIsBtn(true);
                authTreeNodeRespList.add(buttonConfigResp);
                return false;
            } else {
                return true;// 去除父菜单不存在的按钮
            }
        });

        // 构造返回
        AuthTreeResp authTreeResp = new AuthTreeResp();

        // 异步构建树形权限
        CompletableFuture<Void> buildAuthTreeNodeFuture = CompletableFuture.runAsync(
                () -> authTreeResp.setAuthTreeNodeList(
                        TreeUtils.transferToTree(authTreeNodeRespList,
                                "0",
                                AuthTreeNodeResp::getCode,
                                AuthTreeNodeResp::getParentCode,
                                AuthTreeNodeResp::setChildren,
                                Comparator.comparing(AuthTreeNodeResp::getSort)))
                , ThreadPoolConfig.asyncDefaultExecutor);

        // 设置已经勾选的菜单
        List<String> menuCodeList = Optional.ofNullable(menuAuth)
                .map(BitMapUtil::toIdSet)
                .map(menuIdSet -> menuIdSet.stream().map(menuAuthBOMap::get).filter(Objects::nonNull).map(MenuAuthBO::getCode).collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        // 设置已经勾选的按钮
        List<String> buttonCodeList = Optional.ofNullable(buttonAuth)
                .map(BitMapUtil::toIdSet)
                .map(buttonIdSet -> buttonIdSet.stream().map(buttonAuthBOMap::get).filter(Objects::nonNull).map(ButtonAuthBO::getCode).collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        // 合并已经勾选的权限编码集合
        menuCodeList.addAll(buttonCodeList);

        // 设置返回结果
        authTreeResp.setCheckIds(menuCodeList);

        try {
            buildAuthTreeNodeFuture.get(3L, TimeUnit.SECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            Thread.currentThread().interrupt();
            log.error("角色绑定权限的树形菜单构建树形权限出错，原因:{}", e.getMessage());
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR);
        }

        return authTreeResp;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void rebuildMemberAuth(MemberDO memberDO) {
        // 仅更新当前公司的权限，取当前公司所有拥有的平台关系权限来重新更新内部的用户角色和用户（和整个平台权限重建逻辑略微不同）
        List<MemberRelationDO> relDOList = relationRepository.findBySubMemberIdAndRelType(memberDO.getId(), MemberRelationTypeEnum.PLATFORM.getCode());

        // 菜单按钮等权限
        List<byte[]> menuAuthList = new ArrayList<>();
        List<byte[]> buttonAuthList = new ArrayList<>();
        List<byte[]> apiAuthList = new ArrayList<>();

        // 组装权限位图列表
        relDOList.forEach(relDO -> {
            menuAuthList.add(relDO.getMenuAuth());
            buttonAuthList.add(relDO.getButtonAuth());
            apiAuthList.add(relDO.getApiAuth());
        });

        // 关系表权限并集作为会员权限集合
        RoaringBitmap menuAuthBitmap = BitMapUtil.or(menuAuthList, BitMapUtil.ReturnType.RoaringBitmap);
        RoaringBitmap buttonAuthBitmap = BitMapUtil.or(buttonAuthList, BitMapUtil.ReturnType.RoaringBitmap);
        RoaringBitmap apiAuthBitmap = BitMapUtil.or(apiAuthList, BitMapUtil.ReturnType.RoaringBitmap);

        // 更新用户角色权限
        memberDO.getUserRoles().forEach(userRoleDO -> {
            // 超管拥有该会员的所有权限,普通用户角色按旧的权限和新权限取交集
            userRoleDO.setMenuAuth(getAuthByUserType(menuAuthBitmap, userRoleDO.getMenuAuth(), userRoleDO.getUserType(), BitMapUtil.ReturnType.ByteArray));
            userRoleDO.setButtonAuth(getAuthByUserType(buttonAuthBitmap, userRoleDO.getButtonAuth(), userRoleDO.getUserType(), BitMapUtil.ReturnType.ByteArray));
            userRoleDO.setApiAuth(getAuthByUserType(apiAuthBitmap, userRoleDO.getApiAuth(), userRoleDO.getUserType(), BitMapUtil.ReturnType.ByteArray));
            userRoleRepository.save(userRoleDO);

            // 更新用户权限
            updateUserAuthByUserRole(userRoleDO);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUserAuthByUserRole(UserRoleDO userRoleDO) {
        userRoleDO.getUsers().forEach(userDO -> {
            List<byte[]> menuAuthList = new ArrayList<>();
            List<byte[]> buttonAuthList = new ArrayList<>();
            List<byte[]> apiAuthList = new ArrayList<>();

            userDO.getRoles().forEach(roleDO -> {
                menuAuthList.add(roleDO.getMenuAuth());
                buttonAuthList.add(roleDO.getButtonAuth());
                apiAuthList.add(roleDO.getApiAuth());
            });

            // 用户权限为其所使用的用户角色权限的并集
            userDO.setMenuAuth(BitMapUtil.or(menuAuthList, BitMapUtil.ReturnType.ByteArray));
            userDO.setButtonAuth(BitMapUtil.or(buttonAuthList, BitMapUtil.ReturnType.ByteArray));
            userDO.setApiAuth(BitMapUtil.or(apiAuthList, BitMapUtil.ReturnType.ByteArray));
            userRepository.save(userDO);
        });
    }

    @Override
    public void rebuildMemberAuthByUpdateRoleAuth(MemberRoleDO memberRoleDO) {
        // 查询角色的平台关系
        List<RebuildMemberRelationDTO> rebuildRelDTOList = jdbcTemplate.query(RebuildAuthSql.GET_RELATION_INFO_SQL,
                (rs, rowNum) -> new RebuildMemberRelationDTO(rs.getLong(1), rs.getLong(2), rs.getBytes(3), rs.getBytes(4), rs.getBytes(5)),
                memberRoleDO.getId(),
                MemberRelationTypeEnum.PLATFORM.getCode());

        // 如果为空则跳过
        if (CollectionUtils.isEmpty(rebuildRelDTOList)) {
            return;
        }

        // 更新关系表权限
        CompletableFuture<Void> updateRelFuture = updateRelAsync(memberRoleDO, rebuildRelDTOList);

        // 查找会员权限信息
        CompletableFuture<Map<Long, RebuildMemberDTO.MemberAuth>> getMemberAuthMapFuture = getMemberAuthMapAsync(memberRoleDO);

        // 查询待更新的用户角色
        CompletableFuture<List<RebuildUserRoleDTO>> getUserRoleDTOListFuture = getUserRoleDTOListAsync(memberRoleDO);

        // 转换用户角色数据结构
        CompletableFuture<Map<Long, RebuildUserRoleDTO.UserRoleAuth>> getUserRoleDTOMapFuture = getUserRoleDTOMapAsync(getUserRoleDTOListFuture, getMemberAuthMapFuture);

        // 更新用户角色权限
        CompletableFuture<Void> updateUserRoleFuture = updateUserRoleAsync(getUserRoleDTOMapFuture, getUserRoleDTOListFuture);

        // 更新用户权限
        CompletableFuture<Void> updateUserFuture = updateUserAsync(memberRoleDO, getMemberAuthMapFuture, getUserRoleDTOMapFuture);

        try {
            CompletableFuture.allOf(updateRelFuture, updateUserRoleFuture, updateUserFuture).get(30L, TimeUnit.SECONDS);
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("角色权限重建失败，原因:{0}", e);
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR);
        }
    }

    private CompletableFuture<Void> updateRelAsync(MemberRoleDO memberRoleDO, List<RebuildMemberRelationDTO> rebuildRelDTOList) {
        return CompletableFuture.runAsync(() -> {
            // 更新涉及的关系表权限
            transactionTemplate.executeWithoutResult(transactionStatus ->
                    jdbcTemplate.batchUpdate(RebuildAuthSql.UPDATE_RELATION_AUTH_SQL, rebuildRelDTOList, 1000, (ps, rebuildRelDTO) -> {
                        ps.setBytes(1, memberRoleDO.getMenuAuth());
                        ps.setBytes(2, memberRoleDO.getButtonAuth());
                        ps.setBytes(3, memberRoleDO.getApiAuth());
                        ps.setLong(4, rebuildRelDTO.getRelationId());
                    }));
        }, ThreadPoolConfig.asyncDefaultExecutor);
    }

    private CompletableFuture<Map<Long, RebuildMemberDTO.MemberAuth>> getMemberAuthMapAsync(MemberRoleDO memberRoleDO) {
        return CompletableFuture.supplyAsync(() -> {
                    // 查找系统全部会员角色，并转换成map<会员角色id，会员角色Info>
                    return jdbcTemplate.query(RebuildAuthSql.GET_MEMBER_ROLE_INFO_SQL,
                                    (rs, rowNum) -> new RebuildMemberRoleDTO(rs.getLong(1), rs.getBytes(2), rs.getBytes(3), rs.getBytes(4)),
                                    MemberRelationTypeEnum.PLATFORM.getCode())
                            .stream()
                            .map(memberRoleDTO -> new RebuildMemberRoleDTO.MemberRoleAuth(
                                    memberRoleDTO.getMemberRoleId(),
                                    BitMapUtil.toBitMap(memberRoleDTO.getMenuAuth()),
                                    BitMapUtil.toBitMap(memberRoleDTO.getButtonAuth()),
                                    BitMapUtil.toBitMap(memberRoleDTO.getApiAuth())))
                            .collect(Collectors.toMap(RebuildMemberRoleDTO.MemberRoleAuth::getMemberRoleId, Function.identity()));
                }, ThreadPoolConfig.asyncDefaultExecutor)
                .thenApplyAsync(memberRoleAuthMap -> {
                    // 1.先查找会员使用的角色，按照memberId，roles方式返回，其中roles格式为：1,2,3
                    // 2.将会员使用的角色权限合并,并把转换成map<会员id，会员信息Info>
                    return jdbcTemplate.query(RebuildAuthSql.GET_MEMBER_USE_MEMBER_ROLE_INFO_SQL,
                                    (rs, rowNum) -> new RebuildMemberDTO(rs.getLong(1), rs.getString(2)),
                                    memberRoleDO.getId())
                            .stream()
                            .map(rebuildMemberDTO -> {
                                List<RoaringBitmap> menuBitMapList = new ArrayList<>();
                                List<RoaringBitmap> buttonBitMapList = new ArrayList<>();
                                List<RoaringBitmap> apiBitMapList = new ArrayList<>();

                                for (String roleIdStr : rebuildMemberDTO.getMemberRoles().split(",")) {
                                    RebuildMemberRoleDTO.MemberRoleAuth memberRoleAuth = memberRoleAuthMap.get(Long.parseLong(roleIdStr));
                                    if (Objects.nonNull(memberRoleAuth)) {
                                        menuBitMapList.add(memberRoleAuth.getMenuAuthBitMap());
                                        buttonBitMapList.add(memberRoleAuth.getButtonAuthBitMap());
                                        apiBitMapList.add(memberRoleAuth.getApiAuthBitMap());
                                    }
                                }

                                return new RebuildMemberDTO.MemberAuth(rebuildMemberDTO.getMemberId(),
                                        BitMapUtil.or(menuBitMapList.iterator(), BitMapUtil.ReturnType.RoaringBitmap),
                                        BitMapUtil.or(buttonBitMapList.iterator(), BitMapUtil.ReturnType.RoaringBitmap),
                                        BitMapUtil.or(apiBitMapList.iterator(), BitMapUtil.ReturnType.RoaringBitmap));
                            }).collect(Collectors.toMap(RebuildMemberDTO.MemberAuth::getMemberId, Function.identity()));
                }, ThreadPoolConfig.asyncDefaultExecutor);
    }

    private CompletableFuture<List<RebuildUserRoleDTO>> getUserRoleDTOListAsync(MemberRoleDO memberRoleDO) {
        return CompletableFuture.supplyAsync(() ->
                        jdbcTemplate.query(RebuildAuthSql.GET_USER_USE_USER_ROLE_INFO_SQL,
                                (rs, rowNum) -> new RebuildUserRoleDTO(rs.getLong(1), rs.getLong(2), rs.getInt(3), rs.getBytes(4), rs.getBytes(5), rs.getBytes(6)),
                                memberRoleDO.getId())
                , ThreadPoolConfig.asyncDefaultExecutor);
    }

    private static CompletableFuture<Map<Long, RebuildUserRoleDTO.UserRoleAuth>> getUserRoleDTOMapAsync(CompletableFuture<List<RebuildUserRoleDTO>> getUserRoleDTOListFuture, CompletableFuture<Map<Long, RebuildMemberDTO.MemberAuth>> getMemberAuthMapFuture) {
        // 重新计算用户角色权限，并将其转换成map<用户角色id，用户角色Info> (这边计算好并制作map，要给重制用户角色和重制用户权限使用)
        return getUserRoleDTOListFuture.thenCombineAsync(getMemberAuthMapFuture, (userRoleDTOList, memberAuthMap) ->
                        userRoleDTOList.stream()
                                .map(rebuildUserRoleDTO -> {
                                    // 超管角色拥有该公司的所有权限，普通用户角色取旧权限和公司权限的交集
                                    RebuildMemberDTO.MemberAuth memberAuth = memberAuthMap.get(rebuildUserRoleDTO.getMemberId());
                                    return new RebuildUserRoleDTO.UserRoleAuth(
                                            rebuildUserRoleDTO.getUserRoleId(),
                                            rebuildUserRoleDTO.getMemberId(),
                                            getAuthByUserType(memberAuth.getMenuAuthBitMap(), rebuildUserRoleDTO.getMenuAuth(), rebuildUserRoleDTO.getUserType(), BitMapUtil.ReturnType.RoaringBitmap),
                                            getAuthByUserType(memberAuth.getButtonAuthBitMap(), rebuildUserRoleDTO.getButtonAuth(), rebuildUserRoleDTO.getUserType(), BitMapUtil.ReturnType.RoaringBitmap),
                                            getAuthByUserType(memberAuth.getApiAuthBitMap(), rebuildUserRoleDTO.getApiAuth(), rebuildUserRoleDTO.getUserType(), BitMapUtil.ReturnType.RoaringBitmap));
                                })
                                .collect(Collectors.toMap(RebuildUserRoleDTO.UserRoleAuth::getUserRoleId, Function.identity()))
                , ThreadPoolConfig.asyncDefaultExecutor);
    }

    private CompletableFuture<Void> updateUserRoleAsync(CompletableFuture<Map<Long, RebuildUserRoleDTO.UserRoleAuth>> getRebuildUserRoleDTOMapFuture, CompletableFuture<List<RebuildUserRoleDTO>> getUserRoleDTOListFuture) {
        return getRebuildUserRoleDTOMapFuture.thenAcceptBothAsync(getUserRoleDTOListFuture, (userRoleDTOMap, userRoleDTOList) -> {
            userRoleDTOList.forEach(rebuildUserRoleDTO -> {
                RebuildUserRoleDTO.UserRoleAuth userRoleAuth = userRoleDTOMap.get(rebuildUserRoleDTO.getUserRoleId());
                rebuildUserRoleDTO.setMenuAuth(BitMapUtil.toByteArray(userRoleAuth.getMenuAuthBitMap()));
                rebuildUserRoleDTO.setButtonAuth(BitMapUtil.toByteArray(userRoleAuth.getButtonAuthBitMap()));
                rebuildUserRoleDTO.setApiAuth(BitMapUtil.toByteArray(userRoleAuth.getApiAuthBitMap()));
            });

            // 更新用户角色
            transactionTemplate.executeWithoutResult(transactionStatus ->
                    jdbcTemplate.batchUpdate(RebuildAuthSql.UPDATE_USER_ROLE_AUTH_SQL, userRoleDTOList, 1000, (ps, rebuildUserRoleDTO) -> {
                        ps.setBytes(1, rebuildUserRoleDTO.getMenuAuth());
                        ps.setBytes(2, rebuildUserRoleDTO.getButtonAuth());
                        ps.setBytes(3, rebuildUserRoleDTO.getApiAuth());
                        ps.setLong(4, rebuildUserRoleDTO.getUserRoleId());
                    }));
        }, ThreadPoolConfig.asyncDefaultExecutor);
    }

    private CompletableFuture<Void> updateUserAsync(MemberRoleDO memberRoleDO, CompletableFuture<Map<Long, RebuildMemberDTO.MemberAuth>> getMemberAuthMapFuture, CompletableFuture<Map<Long, RebuildUserRoleDTO.UserRoleAuth>> getUserRoleDTOMapFuture) {
        return getMemberAuthMapFuture.thenCombineAsync(getUserRoleDTOMapFuture, (memberAuthMap, userRoleDTOMap) -> {
                    // 1.先查找用户信息，按照memberId，memberId，userType，roles方式返回，其中roles格式为：1,2,3
                    // 2.重制用户角色权限（将用户使用的用户角色权限合并）
                    return jdbcTemplate.query(RebuildAuthSql.GET_USER_INFO_SQL,
                                    (rs, rowNum) -> new RebuildUserDTO(rs.getLong(1), rs.getLong(2), rs.getInt(3), rs.getString(4)),
                                    memberRoleDO.getId())
                            .stream()
                            .peek(memberUserDTO -> {
                                // 如果是超管，赋予会员全部权限
                                if (Objects.equals(UserTypeEnum.ADMIN.getCode(), memberUserDTO.getUserType())) {
                                    RebuildMemberDTO.MemberAuth memberAuth = memberAuthMap.get(memberUserDTO.getMemberId());
                                    memberUserDTO.setMenuAuth(BitMapUtil.toByteArray(memberAuth.getMenuAuthBitMap()));
                                    memberUserDTO.setButtonAuth(BitMapUtil.toByteArray(memberAuth.getButtonAuthBitMap()));
                                    memberUserDTO.setApiAuth(BitMapUtil.toByteArray(memberAuth.getApiAuthBitMap()));
                                } else {
                                    // 非超管赋予拥有的用户角色权限的并集
                                    List<RoaringBitmap> menuBitMapList = new ArrayList<>();
                                    List<RoaringBitmap> buttonBitMapList = new ArrayList<>();
                                    List<RoaringBitmap> apiBitMapList = new ArrayList<>();

                                    for (String roleIdStr : memberUserDTO.getUserRoles().split(",")) {
                                        RebuildUserRoleDTO.UserRoleAuth userRoleAuth = userRoleDTOMap.get(Long.parseLong(roleIdStr));
                                        menuBitMapList.add(userRoleAuth.getMenuAuthBitMap());
                                        buttonBitMapList.add(userRoleAuth.getButtonAuthBitMap());
                                        apiBitMapList.add(userRoleAuth.getApiAuthBitMap());
                                    }

                                    memberUserDTO.setMenuAuth(BitMapUtil.or(menuBitMapList.iterator(), BitMapUtil.ReturnType.ByteArray));
                                    memberUserDTO.setButtonAuth(BitMapUtil.or(buttonBitMapList.iterator(), BitMapUtil.ReturnType.ByteArray));
                                    memberUserDTO.setApiAuth(BitMapUtil.or(apiBitMapList.iterator(), BitMapUtil.ReturnType.ByteArray));
                                }
                            }).collect(Collectors.toList());
                }, ThreadPoolConfig.asyncDefaultExecutor)
                .thenAcceptAsync(userDTOList -> {
                    // 更新用户权限
                    transactionTemplate.executeWithoutResult(transactionStatus ->
                            jdbcTemplate.batchUpdate(RebuildAuthSql.UPDATE_USER_AUTH_SQL, userDTOList, 1000, (ps, rebuildUserDTO) -> {
                                ps.setBytes(1, rebuildUserDTO.getMenuAuth());
                                ps.setBytes(2, rebuildUserDTO.getButtonAuth());
                                ps.setBytes(3, rebuildUserDTO.getApiAuth());
                                ps.setLong(4, rebuildUserDTO.getUserId());
                            }));
                }, ThreadPoolConfig.asyncDefaultExecutor);
    }

    @Override
    public AuthTuple<byte[], byte[]> getMenuAndButtonAuth(List<MenuDO> menuDOList) {
        if (CollectionUtils.isEmpty(menuDOList)) {
            return new AuthTuple<>(new byte[0], new byte[0]);
        }

        RoaringBitmap menuBitmap = new RoaringBitmap();
        RoaringBitmap buttonBitmap = new RoaringBitmap();
        menuDOList.forEach(menuDO -> {
            menuBitmap.add(menuDO.getId().intValue());
            buttonBitmap.add(menuDO.getButtons().stream().map(ButtonDO::getId).mapToInt(Long::intValue).toArray());
        });

        return new AuthTuple<>(BitMapUtil.toByteArray(menuBitmap), BitMapUtil.toByteArray(buttonBitmap));
    }

    /**
     * 基于用户类型返回权限
     *
     * @param returnType {@link BitMapUtil.ReturnType}
     */
    public static <T> T getAuthByUserType(RoaringBitmap memberAuth, byte[] specifyAuth, Integer userType, Class<T> returnType) {
        if (Objects.equals(UserTypeEnum.ADMIN.getCode(), userType)) {
            return BitMapUtil.returnStrategy(memberAuth, returnType);
        }
        return BitMapUtil.and(memberAuth, specifyAuth, returnType);
    }
}