package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.bo.login.ManageLoginBO;
import com.ssy.lingxi.member.entity.bo.login.WebLoginBO;
import com.ssy.lingxi.member.model.req.login.*;
import com.ssy.lingxi.member.model.resp.login.*;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * Web客户端、平台后台用户登录服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-05
 */
public interface IMemberLoginService {

    /**
     * 业务平台 - 账号密码登录
     * @param headers Http头部信息
     * @param loginReq 接口参数
     * @return 操作结果 操作结果
     */
    WebLoginBO accountOrPhoneLogin(HttpHeaders headers, MemberLoginReq loginReq);

    /**
     * 业务平台 - 切换登录用户的会员角色
     * @param headers HttpHeaders信息
     * @param roleReq 接口参数
     * @return 操作结果 操作结果
     */
    WebLoginBO switchMemberRole(HttpHeaders headers, MemberLoginSwitchMemberRoleReq roleReq);

    /**
     * 业务平台 - 获取登录授权链接
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    MemberAuthCodeResp getLoginAuthCode(HttpHeaders headers);

    /**
     * 业务平台 - 验证扫码登录(轮询)
     * @param headers HttpHeaders信息
     * @param memberAuthCodeReq 授权码
     * @return 操作结果
     */
    WebLoginBO getAuthCodeLoginInfo(HttpHeaders headers, ActivityMemberAuthCodeReq memberAuthCodeReq);

    /**
     * 获取滑块图片
     * @param headers HttpHeaders信息
     * @param captchaReq 接口参数
     * @return 滑块图片
     */
    CaptchaPicResp captcha(HttpHeaders headers, CaptchaReq captchaReq);

    /**
     * 业务平台 - 登陆时发送短信验证码
     * @param headers  Http头部信息
     * @param phoneReq 接口参数
     */
    void sendPhoneLoginSmsCode(HttpHeaders headers, PhoneLoginSmsCode phoneReq);

    /**
     * 业务平台 - 登陆时发送邮箱验证码
     * @param headers  Http头部信息
     * @param emailReq 接口参数
     * @return 发送结果
     */
    void sendEmailLoginSmsCode(HttpHeaders headers, EmailLoginSmsCode emailReq);

    /**
     * 平台后台登陆时发送短信验证码
     *
     * @param headers  Http头部信息
     * @param phoneReq 接口参数
     */
    void sendManageLoginPhoneSmsCode(HttpHeaders headers, PhoneLoginSmsCode phoneReq);

    /**
     * 平台后台登陆时发送邮箱验证码
     * @param headers Http头部信息
     * @param emailReq 接口参数
     * @return 发送结果
     */
    void sendManageLoginEmailSmsCode(HttpHeaders headers, EmailLoginSmsCode emailReq);

    /**
     * 多主体检测
     *
     * @param headers       HttpHeaders信息
     * @param multiAccCheckReq 接口参数
     * @return 检测结果
     */
    List<MultiAccCheckResp> multiAccCheck(HttpHeaders headers, MultiAccCheckReq multiAccCheckReq);

    /**
     * 平台后台 - 账号预登录
     * @param headers HttpHeaders信息
     * @param securityCheckReq 接口参数
     * @return 登录结果
     */
    LoginSecurityCheckResp accountSecurityCheck(HttpHeaders headers, LoginSecurityCheckReq securityCheckReq);

    /**
     * 平台后台 - 账号密码登录
     * @param headers HttpHeaders信息
     * @param loginReq 接口参数
     * @return 操作结果
     */
    ManageLoginBO manageAccountLogin(HttpHeaders headers, ManageLoginReq loginReq);

    /**
     * 是否具有支付及查看订单价格权限
     * @param headers HttpHeaders信息
     * @retrurn 是否具有权限
     */
    Boolean hasPayAndOrderPriceAuth(HttpHeaders headers);

    /**
     * 登出
     * @param headers HttpHeaders信息
     */
    void logOut(HttpHeaders headers);

    /**
     * 能力中心 - 获取权限树
     * @param headers HttpHeaders信息
     * @return 权限树
     */
    List<LoginAuthResp> getAuthTree(HttpHeaders headers);

    /**
     * toke续期
     * @param headers HttpHeaders信息
     * @return 续期信息
     */
    WrapperResp<?> refreshToken(HttpHeaders headers);

    /**
     * 发送手机号绑定的短信验证码
     *
     * @param req 请求参数
     */
    void sendPhoneBindSmsCode(PhoneBindSmsCodeReq req);

    /**
     * 绑定手机号
     *
     * @param req 请求参数
     */
    void bindPhone(PhoneBindReq req);

}
