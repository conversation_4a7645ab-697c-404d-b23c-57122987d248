package com.ssy.lingxi.member.model.req.lifecycle;

import com.ssy.lingxi.component.base.annotation.DateTimeStringFormatAnnotation;
import com.ssy.lingxi.member.model.req.basic.FileUploadReq;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 会员投诉与建议新增VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Data
public class MemberComplaintSubAddReq implements Serializable {
    private static final long serialVersionUID = -7265522955426342638L;

    /**
     * 业务类型 1-投诉 2-建议
     */
    @NotNull(message = "业务类型不能为空")
    private Integer type;

    /**
     * 事件分类 1-关于产品 2-关于订单 3-关于配送 4-关于售后 5-关于服务 6-其他
     */
    @NotNull(message = "事件分类不能为空")
    private Integer classify;

    /**
     * 事件主题
     */
    @NotBlank(message = "事件主题不能为空")
    private String subject;

    /**
     * 上级会员id
     */
    private Long memberId;

    /**
     * 上级角色id
     */
    private Long roleId;

    /**
     * 提交人用户id
     */
    private Long byUserId;

    /**
     * 提交人用户编辑名称
     */
    private String byUserEditName;

    /**
     * 提交人用户编辑手机
     */
    private String byUserEditPhone;

    /**
     * 事件时间，格式为yyyy-MM-dd HH:mm:ss
     */
    @DateTimeStringFormatAnnotation(message = "事件时间格式错误")
    private String eventTime;

    /**
     * 事件描述
     */
    @NotBlank(message = "事件描述不能为空")
    private String eventDesc;

    /**
     * 事件建议
     */
    private String eventSuggest;

    /**
     * 事件附件
     */
    private List<FileUploadReq> attachments;
}
