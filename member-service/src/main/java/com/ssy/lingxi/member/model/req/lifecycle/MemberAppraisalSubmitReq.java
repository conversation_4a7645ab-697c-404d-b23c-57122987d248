package com.ssy.lingxi.member.model.req.lifecycle;

import com.ssy.lingxi.member.model.req.basic.FileUploadReq;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 会员考评提交VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Data
public class MemberAppraisalSubmitReq implements Serializable {
    private static final long serialVersionUID = 4050083849037819116L;

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空")
    private Long id;

    /**
     * 总得分
     */
    @NotNull(message = "总得分不能为空")
    @PositiveOrZero(message = "总得分要大于等于0")
    @Max(value = 1000000000, message = "总得分不能超过10的9次方")
    private BigDecimal totalScore;

    /**
     * 考评结果
     */
    @NotBlank(message = "考评结果不能为空")
    @Size(max = 60, message = "考评结果最长60个字符")
    private String result;

    /**
     * 通知会员考评结果 0-否 1-是
     */
    private Integer notifyMember;

    /**
     * 考评结果附件
     */
    @Valid
    private List<FileUploadReq> resultAttachments;

    /**
     * 考评项目
     */
    @Valid
    private List<MemberAppraisalItemSubmitReq> items = new ArrayList<>();
}
