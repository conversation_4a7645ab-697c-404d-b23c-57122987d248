package com.ssy.lingxi.member.serviceImpl.mobile;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.ssy.lingxi.common.enums.DataSourceEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.dto.AreaDTO;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.ThreadLocalUtils;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.manage.api.feign.IAreaFeign;
import com.ssy.lingxi.manage.api.model.resp.area.AreaCodeResp;
import com.ssy.lingxi.member.api.enums.MemberRightAcquireWayEnum;
import com.ssy.lingxi.member.api.enums.MemberRightParamWayEnum;
import com.ssy.lingxi.member.api.enums.MemberRightTypeEnum;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.constant.MemberRegisterDetailConfigConstant;
import com.ssy.lingxi.member.entity.bo.LevelBO;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRightDO;
import com.ssy.lingxi.member.entity.do_.validate.MemberValidateHistoryDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.branch.MemberBranchSaveOrUpdateReq;
import com.ssy.lingxi.member.model.req.info.MemberInfoAddRoleReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberDetailCreditHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.mobile.*;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RegisterDetailGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RegisterDetailResp;
import com.ssy.lingxi.member.model.resp.basic.UserQueryResp;
import com.ssy.lingxi.member.model.resp.branch.MemberBranchResp;
import com.ssy.lingxi.member.model.resp.customer.*;
import com.ssy.lingxi.member.model.resp.info.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterResultResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.mobile.MobileInfoApplyButtonResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileUpdateDepositDetailQueryResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileValidateHistoryResp;
import com.ssy.lingxi.member.model.resp.validate.ValidateStepResp;
import com.ssy.lingxi.member.model.resp.validate.WorkFlowStepResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.mobile.IMobileMemberInfoService;
import com.ssy.lingxi.member.service.web.IMemberBranchService;
import com.ssy.lingxi.member.service.web.IRunBrandService;
import com.ssy.lingxi.member.util.RgConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * App - 会员信息管理查询服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-08
 */
@Slf4j
@Service
public class MobileMemberInfoServiceImpl implements IMobileMemberInfoService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseMemberDetailService baseMemberDetailService;

    @Resource
    private IBaseMemberInfoService baseMemberInfoService;

    @Resource
    private IBaseMemberInnerService baseMemberInnerService;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberValidateHistoryRepository memberValidateHistoryRepository;

    @Resource
    private IBaseUserDetailService baseUserDetailService;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private IBaseSiteService siteService;

    @Resource
    private MemberRoleRepository repository;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    @Resource
    private EnterpriseCertificationDraftRepository enterpriseCertificationDraftRepository;

    @Resource
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    @Resource
    private CorporationRepository corporationRepository;

    @Resource
    private MemberDepositoryDetailRepository memberDepositoryDetailRepository;

    @Resource
    private IBaseRegisterService baseRegisterService;

    @Resource
    private IMemberBranchService memberBranchService;

    @Resource
    private IAreaFeign areaFeign;

    @Resource
    private RunBrandRepository runBrandRepository;

    @Resource
    private UserRepository userRepository;

    /**
     * “会员中心“ - 根据前端商城类型、属性，查询会员关系
     *
     * @param loginUser     登录用户
     * @param shopType      商城类型
     * @param self          是否自营商城
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级角色Id
     * @return 查询结果
     */
    @Override
    public MemberRelationDO findMobileMemberRelation(UserLoginCacheDTO loginUser, Integer shopType, Integer self, Long upperMemberId, Long upperRoleId) {
        MemberRelationDO relationDO;
        if(shopType.equals(ShopTypeEnum.ENTERPRISE.getCode()) && CommonBooleanEnum.NO.getCode().equals(self)) {
            relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
            if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            }
        } else if(shopType.equals(ShopTypeEnum.ENTERPRISE.getCode()) && CommonBooleanEnum.YES.getCode().equals(self)) {
            if(!loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_TYPE_NOT_MATCHED);
            }

            if(NumberUtil.isNullOrZero(upperMemberId) || NumberUtil.isNullOrZero(upperRoleId)) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_SELF_SHOP_UPPER_MEMBER_CAN_NOT_BE_EMPTY);
            }

            relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperMemberId, upperRoleId, loginUser.getMemberId(), loginUser.getMemberRoleId());

            if (relationDO != null && (!relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId()))) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            }
        } else {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_SHOP_MISMATCHED);
        }

        return relationDO;
    }

    /**
     * 查找登录会员的logo
     *
     * @param loginUser 登录用户
     * @return 会员Logo
     */
    @Override
    public String findMemberLogo(UserLoginCacheDTO loginUser) {
        if(loginUser.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            return loginUser.getLogo();
        }

        MemberDO member = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        return member == null ? "" : member.getLogo();
    }

    /**
     * “我的-卡包” - 分页查询归属会员列表
     *
     * @param headers Http头部信息
     * @param pageDataReq  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileInfoLevelRightResp> pageUpperMemberLevelRights(HttpHeaders headers, PageDataReq pageDataReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        Pageable pageable = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize(), Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findBySubMemberIdAndSubRoleIdAndRelTypeAndVerified(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.OTHER.getCode(), MemberValidateStatusEnum.VERIFY_PASSED.getCode(), pageable);

        //前端展示的是“店铺名称”
        Map<Long, Long> memberMap = pageList.getContent().stream().collect(Collectors.toMap(MemberRelationDO::getMemberId, MemberRelationDO::getRoleId));

        //注意！！！这里返回的是下级会员（即当前登录会员）的memberId，所以都是一样的
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MobileInfoLevelRightResp infoVO = new MobileInfoLevelRightResp();
            infoVO.setValidateId(relationDO.getId());
            infoVO.setUpperMemberId(relationDO.getMemberId());
            infoVO.setUpperRoleId(relationDO.getRoleId());
            infoVO.setLogo(relationDO.getMember().getLogo());

            if(!StringUtils.hasLength(infoVO.getUpperMemberName())) {
                infoVO.setUpperMemberName(relationDO.getMember().getName());
            }

            infoVO.setLevel(relationDO.getLevelRight().getLevel());
            infoVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            infoVO.setScore(relationDO.getLevelRight().getScore());
            LevelBO levelBO = baseMemberLevelConfigService.findNextLevel(relationDO.getMemberId(), relationDO.getRoleId(),relationDO.getSubRoleId(), relationDO.getLevelRight().getLevel());
            infoVO.setNextLevelTag(levelBO.getNextTag());
            infoVO.setNextScore(levelBO.getNextScore());
            infoVO.setSumReturnMoney(relationDO.getLevelRight().getSumReturnMoney());
            infoVO.setCurrentPoint(relationDO.getLevelRight().getCurrentPoint());
            infoVO.setStatus(relationDO.getStatus());
            infoVO.setStatusName(MemberStatusEnum.getCodeMessage(infoVO.getStatus()));

            return infoVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “会员中心” - 查询当前会员的积分、权益信息
     * @param headers    Http头部信息
     * @param shopVO 接口参数
     * @return 查询结果
     */
    @Override
    public MobileLevelRightResp getMemberDetailLevelRight(HttpHeaders headers, MobileShopReq shopVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        //如当前商城类型是企业商城且商城属性为B端商城或C端商城时，查询当前会员角色归属至平台的会员等级、会员权益、信用信息；
        //如当前商城类型是企业商城且商城属性为B端自营商城或C端自营商城时，查询当前会员角色归属至当前自营商城所属的商户会员的会员等级、会员权益、信用信息；
        //如当前商城类型是渠道商城或渠道自有商城时，查询归属于渠道商城首页选择的上级渠道的会员权益
        MemberRelationDO relationDO = findMobileMemberRelation(loginUser, shopVO.getShopType(), shopVO.getSelf(), shopVO.getUpperMemberId(), shopVO.getUpperRoleId());

        if(relationDO == null) {
            return new MobileLevelRightResp(findMemberLogo(loginUser));
        }

        MemberLevelRightDO levelDO = relationDO.getLevelRight();
        if (levelDO == null) {
            return new MobileLevelRightResp(findMemberLogo(loginUser));
        }

        MobileLevelRightResp levelRightVO = new MobileLevelRightResp();
        levelRightVO.setUpperMemberId(relationDO.getMemberId());
        levelRightVO.setUpperRoleId(relationDO.getRoleId());
        levelRightVO.setLogo(relationDO.getSubMember().getLogo());
        levelRightVO.setLevel(levelDO.getLevel());
        levelRightVO.setLevelTag(levelDO.getLevelTag());
        levelRightVO.setScore(levelDO.getScore());
        levelRightVO.setNextScore(levelDO.getLevelConfig() == null ? 0 : levelDO.getLevelConfig().getPoint());
        levelRightVO.setCurrentPoint(levelDO.getCurrentPoint());
        levelRightVO.setSumReturnMoney(levelDO.getSumReturnMoney());
        LevelBO levelBO = baseMemberLevelConfigService.findNextLevel(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleId(), relationDO.getLevelRight().getLevel());
        //下一等级
        levelRightVO.setNextLevelTag(levelBO.getNextTag());
        //权益列表
        levelRightVO.setRights(new ArrayList<>());

        if(levelDO.getLevelConfig() == null || CollectionUtils.isEmpty(levelDO.getLevelConfig().getRights())) {
            return levelRightVO;
        }

        List<MemberDetailRightConfigResp> configVOList = levelDO.getLevelConfig().getRights().stream().map(memberRightConfigDO -> {
            MemberDetailRightConfigResp configVO = new MemberDetailRightConfigResp();
            configVO.setId(memberRightConfigDO.getId());
            configVO.setRightTypeEnum(memberRightConfigDO.getRightType());
            configVO.setName(MemberRightTypeEnum.getCodeMessage(memberRightConfigDO.getRightType()));
            configVO.setRemark(MemberRightTypeEnum.getRemark(memberRightConfigDO.getRightType()));
            configVO.setAcquireWay(MemberRightAcquireWayEnum.getCodeMsg(memberRightConfigDO.getAcquireWay()));
            configVO.setParamWay(MemberRightParamWayEnum.getCodeMsg(memberRightConfigDO.getParamWay()));
            configVO.setStatus(memberRightConfigDO.getStatus());
            String param = String.format("%.2f", memberRightConfigDO.getParameter().multiply(new BigDecimal(100)).doubleValue()).concat("%");
            configVO.setParameter(param);
            return configVO;
        }).collect(Collectors.toList());

        levelRightVO.setRights(configVOList);

        return levelRightVO;
    }

    /**
     * “会员中心”  - 分页查询权益获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(HttpHeaders headers, MobileShopPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        MemberRelationDO relationResult = findMobileMemberRelation(loginUser, pageVO.getShopType(), pageVO.getSelf(), pageVO.getUpperMemberId(), pageVO.getUpperRoleId());
        if(relationResult == null) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        return baseMemberDetailService.pageMemberDetailRightHistory(relationResult, pageVO.getCurrent(), pageVO.getPageSize(), MemberConstant.APP_DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * “会员中心”  - 分页查询权益获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(HttpHeaders headers, MobileShopPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        MemberRelationDO relationResult = findMobileMemberRelation(loginUser, pageVO.getShopType(), pageVO.getSelf(), pageVO.getUpperMemberId(), pageVO.getUpperRoleId());
        if(relationResult == null) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        return baseMemberDetailService.pageMemberDetailRightSpendHistory(relationResult, pageVO.getCurrent(), pageVO.getPageSize(), MemberConstant.APP_DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * “会员中心” - 分页查询等级分获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(HttpHeaders headers, MobileShopPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        MemberRelationDO relationResult = findMobileMemberRelation(loginUser, pageVO.getShopType(), pageVO.getSelf(), pageVO.getUpperMemberId(), pageVO.getUpperRoleId());

        if(relationResult == null) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        return baseMemberInfoService.pageMemberLevelDetailHistory(relationResult, pageVO.getCurrent(), pageVO.getPageSize(), MemberConstant.APP_DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 会员详情 - 会员基本信息
     *
     * @param headers    Http头部信息
     * @param shopVO 接口参数
     * @return 查询结果
     */
    @Override
    public MobileInfoBasicDetailResp getMemberBasicDetail(HttpHeaders headers, MobileShopReq shopVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        //如果没有上下级关系，查询平台会员
        MemberRelationDO relationDO = findMobileMemberRelation(loginUser, shopVO.getShopType(), shopVO.getSelf(), shopVO.getUpperMemberId(), shopVO.getUpperRoleId());
        if(relationDO == null) {
            relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
            if(relationDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            }
        }

        return baseMemberInfoService.getMobileMemberBasicDetail(relationDO);
    }

    /**
     * 分页查询归属会员列表
     *
     * @param headers Http头部信息
     * @param pageDataReq  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<UpperMemberInfoResp> pageUpperMembers(HttpHeaders headers, PageDataReq pageDataReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        Boolean wrapperResp = siteService.isEnableMultiTenancy(headers);
        return baseMemberInfoService.pageUpperMembers(loginUser.getMemberId(), loginUser.getMemberRoleId(), "", "", "", null, pageDataReq.getCurrent(), pageDataReq.getPageSize(), wrapperResp,null,null,null,null, null);
    }

    /**
     * 会员详情 - 诚信信息 - 会员基本信用信息
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditResp getMemberDetailCredit(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberInfoService.getMemberDetailCredit(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MobileCommentSummaryResp getMemberDetailCreditTradeCommentSummary(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberInfoService.getMobileMemberDetailTradeCommentSummary(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberInfoService.pageMemberDetailCreditTradeCommentHistory(loginUser, pageVO);
    }

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MobileCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberInfoService.getMobileMemberDetailCreditAfterSaleCommentSummary(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberInfoService.pageMemberDetailCreditAfterSaleCommentHistory(loginUser, pageVO);
    }

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberInfoService.getMemberDetailCreditComplainSummary(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberCreditComplaintPageQueryResp> pageMemberDetailCreditComplainHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberInfoService.pageMemberDetailCreditComplainHistory(loginUser, pageVO);
    }

    /**
     * 查询审批流程
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public ValidateStepResp getMobileOuterValidateSteps(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        ValidateStepResp stepVO = new ValidateStepResp();
        if(relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode())) {
            stepVO.setCurrentStep(2);
        } else {
            stepVO.setCurrentStep(1);
        }

        List<WorkFlowStepResp> stepList = new ArrayList<>();
        WorkFlowStepResp workFlowStepResp = new WorkFlowStepResp();
        workFlowStepResp.setStep(1);
        workFlowStepResp.setStepName("申请会员");
        workFlowStepResp.setRoleName(relationDO.getSubRole().getRoleName());
        stepList.add(workFlowStepResp);

        workFlowStepResp = new WorkFlowStepResp();
        workFlowStepResp.setStep(2);
        workFlowStepResp.setStepName("审核会员");
        workFlowStepResp.setRoleName(relationDO.getRole().getRoleName());
        stepList.add(workFlowStepResp);

        stepVO.setSteps(stepList);

        return stepVO;
    }

    /**
     * 分页查询审核历史记录
     * @param headers    Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileValidateHistoryResp> pageMobileMemberOuterValidateHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        Pageable page = PageRequest.of(pageVO.getCurrent() -1, pageVO.getPageSize(), Sort.by("createTime").descending());
        Page<MemberValidateHistoryDO> pageList = memberValidateHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(h -> {
            MobileValidateHistoryResp historyVO = new MobileValidateHistoryResp();
            historyVO.setCreateTime(h.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
            historyVO.setName(h.getMemberName());
            historyVO.setOperation(h.getOperation());
            historyVO.setRoleName(h.getRoleName());
            historyVO.setStatus(h.getOuterStatus());
            historyVO.setStatusDescription(MemberOuterStatusEnum.getCodeMsg(h.getOuterStatus()));
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 获取“修改会员信息”页面，会员注册资料信息
     *
     * @param headers    Http头部信息
     * @return 查询结果
     */
    @Override
    public List<RegisterDetailGroupResp> getMemberRegisterDetail(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        MemberDO member = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        MemberRoleDO memberRoleDO = repository.findById(loginUser.getMemberRoleId()).orElse(null);
        if(member == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        return baseMemberRegisterDetailService.groupMemberRegisterDetail(member, memberRoleDO, MemberDetailVersionEnum.USING);
    }

    /**
     * 修改会员信息
     *
     * @param headers  Http头部信息
     * @param detailVO 接口参数
     */
    @Override
    public void updateMemberRegisterDetail(HttpHeaders headers, UpdateRegisterDetailReq detailVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        //修改注册信息后，强制重新提交平台审核
        //由于注册信息是所有角色共用的，所以要判断所有角色的平台会员
        List<MemberRelationDO> relationList = relationRepository.findBySubMemberIdAndRelType(loginUser.getMemberId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if(CollectionUtils.isEmpty(relationList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberRelationDO relationDO = relationList.stream().filter(relation -> relation.getSubMemberId().equals(loginUser.getMemberId()) && relation.getSubRoleId().equals(loginUser.getMemberRoleId())).findFirst().orElse(null);
        if (relationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //审核进行中（状态不为“通过”、“未通过”），不能修改信息
        MemberRelationDO otherRoleRelation = relationList.stream().filter(relation -> !relation.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode()) && !relation.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode())).findFirst().orElse(null);
        if(otherRoleRelation != null) {
            if(otherRoleRelation.getId().equals(relationDO.getId())){
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
            }else {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_HAS_ANOTHER_ROLE_IS_VERIFYING);
            }
        }

        baseMemberRegisterDetailService.updatePlatformMemberRegisterDetail(relationDO, "", detailVO.getDetail(), false,true);
    }

    /**
     * 分页查询会员下属用户
     * @param headers Http头部新
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<UserQueryResp> pageUsers(HttpHeaders headers, MobileUserPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseUserDetailService.pageUsers(loginUser.getMemberId(), pageVO.getKeyword(), pageVO.getCurrent(), pageVO.getPageSize(), true);
    }

    /**
     * “店铺会员”页面，查询“申请会员”按钮状态和文本
     *
     * @param headers     Http头部信息
     * @param upperMemberVO 接口参数
     * @return 查询结果
     */
    @Override
    public MobileInfoApplyButtonResp getMobileApplyCondition(HttpHeaders headers, UpperMemberIdRoleIdReq upperMemberVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberInfoService.getMobileApplyCondition(loginUser, upperMemberVO.getUpperMemberId(), upperMemberVO.getUpperRoleId());
    }

    /**
     * “店铺会员”页面，查询“入会享特权”的权益列表
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberDetailRightConfigResp> getUpperMemberRights(HttpHeaders headers, UpperMemberIdRoleIdReq upperMemberVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberLevelConfigService.findSubMemberRights(upperMemberVO.getUpperMemberId(), upperMemberVO.getUpperRoleId(), loginUser.getMemberRoleId());
    }

    /**
     * “店铺会员”页面，申请成为会员时，查询需要填写的入库资料
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<RegisterDetailGroupResp> findMobileApplyDepositDetails(HttpHeaders headers, UpperMemberIdRoleIdReq upperMemberVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        MemberInfoApplyDepositDetailResp depositDetailResult = baseMemberInfoService.getApplyDepositDetail(loginUser, upperMemberVO.getUpperMemberId(), upperMemberVO.getUpperRoleId());

        return depositDetailResult.getDepositDetails();
    }

    /**
     * “店铺会员”页面，修改入库资料时，查询已有的入库资料信息
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 修改结果
     */
    @Override
    public MobileUpdateDepositDetailQueryResp getMemberDepositDetail(HttpHeaders headers, @Valid UpperMemberIdRoleIdReq upperMemberVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberInfoService.getMemberDepositDetail(loginUser, upperMemberVO.getUpperMemberId(), upperMemberVO.getUpperRoleId());
    }

    /**
     * “店铺会员”页面，当审核不通过时，修改入库资料
     *
     * @param headers         Http头部信息
     * @param depositDetailVO 接口参数
     * @param roleTag 角色标签
     * @return 修改结果
     */
    @Override
    public void updateDepositDetail(HttpHeaders headers, MobileUpdateDepositDetailReq depositDetailVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        baseMemberInfoService.updateMobileDepositDetail(loginUser, depositDetailVO, roleTag);
    }

    /**
     * “申请会员” - 提交
     *
     * @param headers Http头部信息
     * @param subVO   接口参数
     */
    @Override
    public void applyToBeSubMember(HttpHeaders headers, MobileApplyForSubReq subVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        baseMemberInnerService.applyToBeSubMember(subVO.getUpperMemberId(), subVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), subVO.getDepositDetails(), subVO.getQualities());
    }

    /**
     * “店铺会员” - 查询当前会员的等级、权益信息
     *
     * @param headers               Http头部信息
     * @param upperMemberIdRoleIdReq 接口参数
     * @return 查询结果
     */
    @Override
    public MobileLevelRightResp getShopMemberDetailLevelRight(HttpHeaders headers, UpperMemberIdRoleIdReq upperMemberIdRoleIdReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperMemberIdRoleIdReq.getUpperMemberId(), upperMemberIdRoleIdReq.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if(relationDO == null) {
            return new MobileLevelRightResp(findMemberLogo(loginUser));
        }

        MemberLevelRightDO levelDO = relationDO.getLevelRight();
        if (levelDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
        }

        MobileLevelRightResp levelRightVO = new MobileLevelRightResp();
        levelRightVO.setUpperMemberId(relationDO.getMemberId());
        levelRightVO.setUpperRoleId(relationDO.getRoleId());
        levelRightVO.setLogo(relationDO.getSubMember().getLogo());
        levelRightVO.setLevel(levelDO.getLevel());
        levelRightVO.setLevelTag(levelDO.getLevelTag());
        levelRightVO.setScore(levelDO.getScore());
        levelRightVO.setNextScore(levelDO.getLevelConfig() == null ? 0 : levelDO.getLevelConfig().getPoint());
        levelRightVO.setCurrentPoint(levelDO.getCurrentPoint());
        levelRightVO.setSumReturnMoney(levelDO.getSumReturnMoney());
        LevelBO levelBO = baseMemberLevelConfigService.findNextLevel(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleId(), relationDO.getLevelRight().getLevel());
        //下一等级
        levelRightVO.setNextLevelTag(levelBO.getNextTag());
        //权益列表
        levelRightVO.setRights(new ArrayList<>());

        if(levelDO.getLevelConfig() == null || CollectionUtils.isEmpty(levelDO.getLevelConfig().getRights())) {
            return levelRightVO;
        }

        List<MemberDetailRightConfigResp> configVOList = levelDO.getLevelConfig().getRights().stream().map(memberRightConfigDO -> {
            MemberDetailRightConfigResp configVO = new MemberDetailRightConfigResp();
            configVO.setId(memberRightConfigDO.getId());
            configVO.setRightTypeEnum(memberRightConfigDO.getRightType());
            configVO.setName(MemberRightTypeEnum.getCodeMessage(memberRightConfigDO.getRightType()));
            configVO.setRemark(MemberRightTypeEnum.getRemark(memberRightConfigDO.getRightType()));
            configVO.setAcquireWay(MemberRightAcquireWayEnum.getCodeMsg(memberRightConfigDO.getAcquireWay()));
            configVO.setParamWay(MemberRightParamWayEnum.getCodeMsg(memberRightConfigDO.getParamWay()));
            configVO.setStatus(memberRightConfigDO.getStatus());
            String param = String.format("%.2f", memberRightConfigDO.getParameter().multiply(new BigDecimal(100)).doubleValue()).concat("%");
            configVO.setParameter(param);
            return configVO;
        }).collect(Collectors.toList());

        levelRightVO.setRights(configVOList);

        return levelRightVO;
    }

    /**
     * “店铺会员”  - 分页查询会员权益获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightHistoryResp> pageShopMemberDetailRightHistory(HttpHeaders headers, MobileUpperPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(pageVO.getUpperMemberId(), pageVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if(relationDO == null) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        return baseMemberDetailService.pageMemberDetailRightHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize(), MemberConstant.APP_DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * “店铺会员”  - 分页查询会员权益使用记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightSpendHistoryResp> pageShopMemberDetailRightSpendHistory(HttpHeaders headers, MobileUpperPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(pageVO.getUpperMemberId(), pageVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if(relationDO == null) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        return baseMemberDetailService.pageMemberDetailRightSpendHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize(), MemberConstant.APP_DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * “店铺会员”  - 分页查询活跃分获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailLevelHistoryResp> pageShopMemberLevelDetailHistory(HttpHeaders headers, MobileUpperPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(pageVO.getUpperMemberId(), pageVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if(relationDO == null) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        return baseMemberDetailService.pageMemberLevelDetailHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize(), MemberConstant.APP_DEFAULT_DATETIME_FORMATTER);
    }


    /**
     * 企业认证 - 获取会员注册资料
     * @return 查询结果
     */
    @Override
    public List<MemberConfigGroupResp> getCorporationCertificationDetail(UserLoginCacheDTO loginUser) {
        MemberRoleDO memberRoleDO = repository.findById(baiTaiMemberProperties.getCustomerRoleId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST));
        BusinessAssertUtil.isFalse(memberRoleDO == null, ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);

        Set<MemberRegisterConfigDO> configs = memberRoleDO.getConfigs();

        List<MemberRegisterConfigDO> memberRegisterConfigDOList = configs.stream()
                .filter(memberRegisterConfigDO -> !Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.MEMBER_NAME))
                .filter(memberRegisterConfigDO -> !Objects.equals(memberRegisterConfigDO.getFieldName(), MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME))
                .filter(memberRegisterConfigDO -> !Objects.equals(RgConfigUtil.getFieldGroupName(memberRegisterConfigDO), MemberRegisterDetailConfigConstant.RELEVANT_CONTACT_PERSON))
                .collect(Collectors.toList());

        List<MemberConfigGroupResp> resultList = baseMemberRegisterDetailService.groupMemberConfig(new ArrayList<>(memberRegisterConfigDOList));
        return resultList.stream().filter(v -> !CollectionUtils.isEmpty(v.getElements())).collect(Collectors.toList());
    }

    /**
     * 企业认证
     * @param loginUser 登录用户
     * @param req 请求参数
     */
    @Override
    public void corporationCertification(UserLoginCacheDTO loginUser, CertificationCertificationReq req) {
        log.info("企业认证：{}", JsonUtil.toJson(req));

        MemberRelationDO memberRelationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), loginUser.getMemberId(), baiTaiMemberProperties.getCustomerRoleId());
        if (Objects.nonNull(memberRelationDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_CERTIFICATION_EXIST);
        }

        boolean existFlag = corporationRepository.existsByName((String) req.getDetail().get(MemberRegisterDetailConfigConstant.CORPORATION_NAME));
        if (existFlag) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_EXIST);
        }

        MemberInfoAddRoleReq memberInfoAddRoleReq = new MemberInfoAddRoleReq();
        memberInfoAddRoleReq.setRoleId(baiTaiMemberProperties.getCustomerRoleId());
        memberInfoAddRoleReq.setDetail(req.getDetail());
        memberInfoAddRoleReq.setEnterpriseCertificationFlag(true);
        memberInfoAddRoleReq.setAddress(req.getAddress());

        // 填充会员名称资料
        req.getDetail().put(MemberRegisterDetailConfigConstant.MEMBER_NAME, loginUser.getMemberName());

        baseMemberInfoService.addMemberRole(loginUser.getMemberId(), loginUser.getMemberRoleId(), memberInfoAddRoleReq, loginUser);
    }

    /**
     * 获取认证资料详情
     *
     * @param corporationId 企业认证id
     * @return 查询结果
     */
    @Override
    public List<RegisterDetailGroupResp> getMemberCorporationCertificationDetail(UserLoginCacheDTO loginUser, Long corporationId) {
        List<MemberDO> memberDOList = memberRepository.findAllByCorporationId(corporationId);

        MemberDO memberDO = memberDOList.stream().filter(v -> BooleanUtils.isTrue(v.getMainFlag())).findFirst().orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), memberDO.getId(), baiTaiMemberProperties.getCustomerRoleId());

        BusinessAssertUtil.notNull(relationDO, ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);

        //非平台会员才可以修改入库资料
        if (!relationDO.getSubMemberLevelTypeEnum().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_MERCHANT_OR_CHANNEL_LEVEL_TYPE);
        }

        List<Integer> outerStatusList = Stream.of(MemberOuterStatusEnum.DEPOSITORY_PASSED, MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED, MemberOuterStatusEnum.MODIFY_PASSED, MemberOuterStatusEnum.MODIFY_NOT_PASSED).map(MemberOuterStatusEnum::getCode).collect(Collectors.toList());
        if(!outerStatusList.contains(relationDO.getOuterStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        List<RegisterDetailGroupResp> groups = baseMemberDepositDetailService.switchMemberDepositoryDetail(relationDO);

        for (RegisterDetailGroupResp next : groups) {
            if (!Objects.equals(next.getGroupName(), MemberRegisterDetailConfigConstant.CORPORATION_INFO)) {
                continue;
            }
            List<RegisterDetailResp> elements = next.getElements();
            elements.removeIf(v -> Objects.equals(v.getFieldName(), MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME));
        }

        return groups.stream()
                .filter(v -> !Objects.equals(v.getGroupName(), MemberRegisterDetailConfigConstant.BASIC_INFO_GROUP))
                .filter(v -> !Objects.equals(v.getGroupName(), MemberRegisterDetailConfigConstant.RELEVANT_CONTACT_PERSON))
                .collect(Collectors.toList());
    }

    /**
     * 企业认证变更
     * @param loginUser 登录用户
     * @param req 请求参数
     */
    @Override
    public void updateCorporationCertification(UserLoginCacheDTO loginUser, CertificationCertificationUpdateReq req) {
        log.info("会员变更认证信息：{}", JsonUtil.toJson(req));

        CorporationDO corporationDO = corporationRepository.findById(req.getCorporationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), corporationDO.getMemberId(), baiTaiMemberProperties.getCustomerRoleId());

        BusinessAssertUtil.notNull(relationDO, ResponseCodeEnum.MC_MS_CORPORATION_CERTIFICATION_INFO_NOT_EXIST);

        // 非平台会员才可以修改入库资料
        if (!Objects.equals(relationDO.getSubMemberLevelTypeEnum(), MemberLevelTypeEnum.MERCHANT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_MERCHANT_OR_CHANNEL_LEVEL_TYPE);
        }

        List<Integer> outerStatusList = Stream.of(MemberOuterStatusEnum.DEPOSITORY_PASSED, MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED, MemberOuterStatusEnum.MODIFY_PASSED, MemberOuterStatusEnum.MODIFY_NOT_PASSED).map(MemberOuterStatusEnum::getCode).collect(Collectors.toList());
        if(!outerStatusList.contains(relationDO.getOuterStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        // 企业客户名称不能重复
        boolean existFlag = corporationRepository.existsByNameAndIdNot((String) req.getDetail().get(MemberRegisterDetailConfigConstant.CORPORATION_NAME), req.getCorporationId());
        if (existFlag) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_EXIST);
        }

        // 优时客户不允许在商城修改
        if (!Objects.equals(corporationDO.getDataSource(), DataSourceEnum.MALL.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_YS_CUSTOMER_ALLOW_UPDATE_AT_MALL);
        }

        req.getDetail().put(MemberRegisterDetailConfigConstant.MEMBER_NAME, relationDO.getSubMember().getName());

        baseMemberDepositDetailService.checkAndUpdateMemberDepositoryDetail(relationDO, req.getDetail(), RoleTagEnum.MEMBER.getCode());
    }

    /**
     * 获取企业认证信息
     *
     * @param loginUser 登录用户
     * @return 企业认证信息
     */
    @Override
    public CorporationResp getCorporationCertificationInfo(UserLoginCacheDTO loginUser) {
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));

        if (Objects.nonNull(memberDO.getCorporationId())) {
            CorporationDO corporationDO = corporationRepository.findById(memberDO.getCorporationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));

            CorporationResp corporationResp = new CorporationResp();
            corporationResp.setId(corporationDO.getId());
            corporationResp.setName(corporationDO.getName());
            corporationResp.setSocialUnifiedCode(corporationDO.getUnifiedSocialCode());
            corporationResp.setLegalPersonName(corporationDO.getLegalPersonName());
            corporationResp.setLegalPersonIdentityCardNo(corporationDO.getLegalPersonIdentityCardNo());
            corporationResp.setBusinessLicenseValidEndTime(corporationDO.getBusinessLicenseValidEndTime());
            corporationResp.setHasFaceRecognition(corporationDO.getHasFaceRecognition());
            corporationResp.setPassedDate(DateTimeUtil.formatDateTime(corporationDO.getPassedDate()));
            return corporationResp;
        }

        return null;
    }

    /**
     * 获取企业认证信息
     *
     * @param loginUser 登录用户
     * @param corporationId 企业id
     * @return 企业认证信息
     */
    @Override
    public CorporationResp getCorporationCertificationInfoById(UserLoginCacheDTO loginUser, Long corporationId) {
        CorporationDO corporationDO = corporationRepository.findById(corporationId).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));

        // 查询认证状态
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), corporationDO.getMemberId(), baiTaiMemberProperties.getCustomerRoleId());

        CorporationResp corporationResp = new CorporationResp();
        corporationResp.setId(corporationDO.getId());

        // 判断是否处于审核中状态，如果是则返回审核中的企业信息
        if (relationDO != null && isInModifyingStatus(relationDO)) {
            // 获取审核中的企业信息
            populateCorporationRespFromAuditingData(corporationResp, relationDO, corporationDO);
        } else {
            // 返回已通过审核的企业信息
            populateCorporationRespFromApprovedData(corporationResp, corporationDO);
        }

        // 设置认证状态
        if (Objects.isNull(relationDO)) {
            corporationResp.setStatus(CorporationCertificationStatusEnum.UN_CERTIFICATION.getCode());
        } else if (Objects.equals(relationDO.getVerified(), CommonBooleanEnum.YES.getCode())) {
            corporationResp.setStatus(CorporationCertificationStatusEnum.CERTIFICATION_SUCCESS.getCode());
        } else if (Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED.getCode())) {
            corporationResp.setStatus(CorporationCertificationStatusEnum.CERTIFICATION_NOT_PASSED.getCode());
        } else {
            corporationResp.setStatus(CorporationCertificationStatusEnum.CERTIFICATION_PENDING.getCode());
        }

        return corporationResp;
    }

    /**
     * 判断是否处于变更审核中状态
     */
    private boolean isInModifyingStatus(MemberRelationDO relationDO) {
        return Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.MODIFYING.getCode()) ||
               Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.MODIFY_NOT_PASSED.getCode());
    }

    /**
     * 从审核中的数据填充企业认证信息
     */
    private void populateCorporationRespFromAuditingData(CorporationResp corporationResp, MemberRelationDO relationDO, CorporationDO corporationDO) {
        try {
            // 查询审核中版本的企业认证详情
            List<MemberDepositoryDetailDO> auditingDetails = memberDepositoryDetailRepository.findByRelationAndVersion(relationDO, MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());

            if (CollectionUtils.isEmpty(auditingDetails)) {
                // 如果没有审核中的数据，则使用已通过审核的数据
                populateCorporationRespFromApprovedData(corporationResp, corporationDO);
                return;
            }

            // 从审核中的详情数据中提取企业信息
            Map<String, String> auditingDataMap = auditingDetails.stream()
                .collect(Collectors.toMap(
                    MemberDepositoryDetailDO::getFieldName,
                    detail -> detail.getDetail() != null ? detail.getDetail() : "",
                    (existing, replacement) -> replacement
                ));

            // 设置企业信息（优先使用审核中的数据，如果没有则使用原数据）
            corporationResp.setName(auditingDataMap.getOrDefault(MemberRegisterDetailConfigConstant.CORPORATION_NAME, corporationDO.getName()));
            corporationResp.setSocialUnifiedCode(auditingDataMap.getOrDefault(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE, corporationDO.getUnifiedSocialCode()));
            corporationResp.setLegalPersonName(auditingDataMap.getOrDefault(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME, corporationDO.getLegalPersonName()));
            corporationResp.setLegalPersonIdentityCardNo(auditingDataMap.getOrDefault(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO, corporationDO.getLegalPersonIdentityCardNo()));
            corporationResp.setBusinessLicenseValidEndTime(auditingDataMap.getOrDefault(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME, corporationDO.getBusinessLicenseValidEndTime()));

            // 人脸识别状态和通过日期使用原数据
            corporationResp.setHasFaceRecognition(corporationDO.getHasFaceRecognition());
            corporationResp.setPassedDate(DateTimeUtil.formatDateTime(corporationDO.getPassedDate()));

        } catch (Exception e) {
            log.error("获取审核中企业信息失败，corporationId: {}, relationId: {}", corporationDO.getId(), relationDO.getId(), e);
            // 异常时使用已通过审核的数据
            populateCorporationRespFromApprovedData(corporationResp, corporationDO);
        }
    }

    /**
     * 从已通过审核的数据填充企业认证信息
     */
    private void populateCorporationRespFromApprovedData(CorporationResp corporationResp, CorporationDO corporationDO) {
        corporationResp.setName(corporationDO.getName());
        corporationResp.setSocialUnifiedCode(corporationDO.getUnifiedSocialCode());
        corporationResp.setLegalPersonName(corporationDO.getLegalPersonName());
        corporationResp.setLegalPersonIdentityCardNo(corporationDO.getLegalPersonIdentityCardNo());
        corporationResp.setBusinessLicenseValidEndTime(corporationDO.getBusinessLicenseValidEndTime());
        corporationResp.setHasFaceRecognition(corporationDO.getHasFaceRecognition());
        corporationResp.setPassedDate(DateTimeUtil.formatDateTime(corporationDO.getPassedDate()));
    }

    /**
     * 保存企业认证信息草稿
     * @param loginUser 登录用户
     * @param req 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCorporationCertificationDraft(UserLoginCacheDTO loginUser, CorporationCertificationDraftReq req) {
        log.info("保存会员企业认证信息草稿：{}", JsonUtil.toJson(req));

        EnterpriseCertificationDraftDO enterpriseCertificationDraftDO = enterpriseCertificationDraftRepository.findFirstByMemberIdOrderByIdDesc(loginUser.getMemberId());

        if (Objects.isNull(enterpriseCertificationDraftDO)) {
            enterpriseCertificationDraftDO = new EnterpriseCertificationDraftDO();
        }

        enterpriseCertificationDraftDO.setMemberId(loginUser.getMemberId());
        enterpriseCertificationDraftDO.setDraft(req.getDetail());

        enterpriseCertificationDraftRepository.saveAndFlush(enterpriseCertificationDraftDO);
    }

    /**
     * 获取会员企业认证信息草稿
     *
     * @return 会员企业认证信息草稿
     */
    @Override
    public Map<String, Object> getCorporationCertificationDraft(UserLoginCacheDTO loginUser) {
        EnterpriseCertificationDraftDO enterpriseCertificationDraftDO = enterpriseCertificationDraftRepository.findFirstByMemberIdOrderByIdDesc(loginUser.getMemberId());
        return Optional.ofNullable(enterpriseCertificationDraftDO).map(EnterpriseCertificationDraftDO::getDraft).orElse(null);
    }

    /**
     * 企业主体列表
     *
     * @return 企业主体列表
     */
    @Override
    public CorporationInfoResp listCorporation(UserLoginCacheDTO loginUser) {
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));

        CorporationInfoResp corporationInfoResp = new CorporationInfoResp();
        corporationInfoResp.setCanAddCorporation(BooleanUtils.isTrue(memberDO.getMainFlag()));

        if (BooleanUtils.isTrue(memberDO.getMainFlag())) {
            List<CorporationDO> corporationDOList = corporationRepository.findAllByGroupIdentifier(memberDO.getPhone());
            if (CollectionUtils.isEmpty(corporationDOList)) {
                return corporationInfoResp;
            }

            List<Long> subMemberIds = corporationDOList.stream().map(CorporationDO::getMemberId).collect(Collectors.toList());
            List<MemberRelationDO> memberRelationDOList = relationRepository.findAllByMemberIdAndRoleIdAndSubMemberIdInAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), subMemberIds, baiTaiMemberProperties.getCustomerRoleId());
            Map<Long, MemberRelationDO> relationDOMap = memberRelationDOList.stream().collect(Collectors.toMap(MemberRelationDO::getSubMemberId, Function.identity(), (v1, v2) -> v1));

            List<CorporationSimpleResp> resultList = corporationDOList.stream()
                    .map(v -> {
                        CorporationSimpleResp corporationSimpleResp = new CorporationSimpleResp();
                        corporationSimpleResp.setId(v.getId());
                        corporationSimpleResp.setName(v.getName());
                        corporationSimpleResp.setSocialUnifiedCode(v.getUnifiedSocialCode());

                        MemberRelationDO relationDO = relationDOMap.get(v.getMemberId());
                        // 转换认证状态名称
                        calCertificationStatusName(relationDO, corporationSimpleResp);
                        return corporationSimpleResp;
                    })
                    .collect(Collectors.toList());
            corporationInfoResp.setCorporationList(resultList);
            return corporationInfoResp;
        }

        // 非主会员（品牌账号）需要显示上级企业及自己这个品牌
        CorporationDO corporationDO = corporationRepository.findFirstByMemberId(loginUser.getMemberId());

        if (Objects.isNull(corporationDO)) {
            return corporationInfoResp;
        }

        // 品牌账号只显示上级企业及自己这个品牌
        List<CorporationDO> corporationDOList = new ArrayList<>();
        
        // 1. 添加自己的企业
        corporationDOList.add(corporationDO);
        
        // 2. 查找上级企业（主账号的企业）
        // 通过groupIdentifier查找具有相同groupIdentifier的所有企业，然后找主企业
        List<CorporationDO> allGroupCorporations = corporationRepository.findAllByGroupIdentifier(corporationDO.getGroupIdentifier());
        log.info("allGroupCorporations size is {}",allGroupCorporations.size());
        for (CorporationDO groupCorp : allGroupCorporations) {
            if (!groupCorp.getId().equals(corporationDO.getId())) {
                // 检查这个企业对应的会员是否为主账号
                MemberDO member = memberRepository.findById(groupCorp.getMemberId()).orElse(null);
                if (Objects.nonNull(member) && BooleanUtils.isTrue(member.getMainFlag())) {
                    corporationDOList.add(groupCorp);
                    break; // 只需要找到一个主企业即可
                }
            }
        }

        List<Long> subMemberIds = corporationDOList.stream().map(CorporationDO::getMemberId).collect(Collectors.toList());
        List<MemberRelationDO> memberRelationDOList = relationRepository.findAllByMemberIdAndRoleIdAndSubMemberIdInAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), subMemberIds, baiTaiMemberProperties.getCustomerRoleId());
        Map<Long, MemberRelationDO> relationDOMap = memberRelationDOList.stream().collect(Collectors.toMap(MemberRelationDO::getSubMemberId, Function.identity(), (v1, v2) -> v1));

        List<CorporationSimpleResp> resultList = corporationDOList.stream()
                .map(v -> {
                    CorporationSimpleResp corporationSimpleResp = new CorporationSimpleResp();
                    corporationSimpleResp.setId(v.getId());
                    corporationSimpleResp.setName(v.getName());
                    corporationSimpleResp.setSocialUnifiedCode(v.getUnifiedSocialCode());

                    MemberRelationDO relationDO = relationDOMap.get(v.getMemberId());
                    // 转换认证状态名称
                    calCertificationStatusName(relationDO, corporationSimpleResp);
                    return corporationSimpleResp;
                })
                .collect(Collectors.toList());
        corporationInfoResp.setCorporationList(resultList);

        return corporationInfoResp;
    }

    /**
     * 查询品牌客户列表
     * @return 品牌客户列表
     */
    @Override
    public List<BrandResp> listBrandCustomer(UserLoginCacheDTO loginUser) {
        List<MemberDO> memberDOList = new ArrayList<>();
        if (baiTaiMemberProperties.getSelfMemberId().equals(loginUser.getMemberId())) {
            // 查询source!=99,和id!=loginUser.getMemberId()的会员信息
            memberDOList = memberRepository.findBySourceNotAndIdNot(MemberRegisterSourceEnum.FROM_MANAGE_PLATFORM.getCode(), loginUser.getMemberId());
        } else {
            CorporationInfoResp corporationInfoResp = listCorporation(loginUser);
            List<Long> longList = corporationInfoResp.getCorporationList().stream().map(CorporationSimpleResp::getId).collect(Collectors.toList());
            memberDOList = memberRepository.findByCorporationIdIn(longList);
        }

        if (CollectionUtil.isEmpty(memberDOList)) {
            return Collections.emptyList();
        }
        return memberDOList.stream().map(s -> {
            BrandResp brandResp = new BrandResp();
            brandResp.setBrandId(s.getId());
            brandResp.setOperateBrand(s.getName());
            return brandResp;
        }).collect(Collectors.toList());
    }

    private static void calCertificationStatusName(MemberRelationDO relationDO, CorporationSimpleResp corporationSimpleResp) {
        if (Objects.equals(relationDO.getVerified(), CommonBooleanEnum.YES.getCode())) {
            corporationSimpleResp.setCertificationStatusName(CorporationCertificationStatusEnum.CERTIFICATION_SUCCESS.getName());
        } else if (Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED.getCode())) {
            corporationSimpleResp.setCertificationStatusName(CorporationCertificationStatusEnum.CERTIFICATION_NOT_PASSED.getName());
        } else {
            corporationSimpleResp.setCertificationStatusName(CorporationCertificationStatusEnum.CERTIFICATION_PENDING.getName());
        }
    }

    /**
     * 根据企业主体查询品牌客户列表
     *
     * @return 品牌客户列表
     */
    @Override
    public List<BrandCustomerResp> listBrandCustomerByCorporationId(UserLoginCacheDTO loginUser, Long corporationId) {
        List<MemberDO> memberDOList = memberRepository.findAllByCorporationId(corporationId);

        List<Long> subMemberIds = memberDOList.stream().map(MemberDO::getId).collect(Collectors.toList());
        List<MemberRelationDO> memberRelationDOList = relationRepository.findAllByMemberIdAndRoleIdAndSubMemberIdInAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), subMemberIds, baiTaiMemberProperties.getCustomerRoleId());
        Map<Long, MemberRelationDO> relationDOMap = memberRelationDOList.stream().collect(Collectors.toMap(MemberRelationDO::getSubMemberId, Function.identity(), (v1, v2) -> v1));

        return memberDOList.stream().map(v -> {
            BrandCustomerResp brandCustomerResp = new BrandCustomerResp();
            brandCustomerResp.setId(v.getId());
            brandCustomerResp.setName(v.getName());
            brandCustomerResp.setAccount(v.getAccount());
            brandCustomerResp.setPhone(v.getPhone());
            brandCustomerResp.setRegisteredTrademark(v.getRegisteredTrademark());

            MemberRelationDO relationDO = relationDOMap.get(v.getId());
            brandCustomerResp.setRelationOuterStatus(relationDO.getOuterStatus());
            brandCustomerResp.setRelationOuterStatusName(Optional.ofNullable(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus())).map(d -> d.replace("入库", "")).orElse(null));
            brandCustomerResp.setShowModifyBtn(!Objects.equals(relationDO.getVerified(), CommonBooleanEnum.YES.getCode()) && Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED.getCode()));

            return brandCustomerResp;
        }).collect(Collectors.toList());
    }

    /**
     * 新增企业主体
     *
     * @param req 添加参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCorporation(UserLoginCacheDTO loginUser, CorporationAddReq req) {
        log.info("新增企业主体：{}", JsonUtil.toJson(req));

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), loginUser.getMemberId(), baiTaiMemberProperties.getCustomerRoleId());
        if (Objects.nonNull(relationDO) && !Objects.equals(relationDO.getVerified(), CommonBooleanEnum.YES.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_CERTIFICATION_VERIFIED_BEFORE_NOT_ALLOW_ADD_BRAND_MEMBER);
        }

        boolean existFlag = corporationRepository.existsByName((String) req.getDetail().get(MemberRegisterDetailConfigConstant.CORPORATION_NAME));
        if (existFlag) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_EXIST);
        }

        // 将企业名称拷贝到会员名称中
        req.getDetail().put(MemberRegisterDetailConfigConstant.MEMBER_NAME, req.getMemberName());

        MemberRegisterResultResp memberRegisterResultResp = baseRegisterService.registerPlatformMember(MemberRegisterSourceEnum.parseInt(req.getRegisterSource()), null, "+86", req.getPhone(), req.getPassword(), null, MemberTypeEnum.MERCHANT.getCode(), baiTaiMemberProperties.getCustomerRoleId(), req.getDetail(), null, loginUser.getUserPhone());

        CorporationDO corporationDO = corporationRepository.findById(memberRegisterResultResp.getCorporationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));
        CorporationDO otherCorporationDO = corporationRepository.findAllByGroupIdentifier(loginUser.getUserPhone()).stream().filter(v -> !v.getId().equals(corporationDO.getId()) && Objects.nonNull(v.getActualControllerId())).findFirst().orElse(null);
        if (Objects.nonNull(otherCorporationDO)) {
            corporationDO.setActualControllerId(otherCorporationDO.getActualControllerId());
            corporationDO.setActualControllerCode(otherCorporationDO.getActualControllerCode());
            corporationRepository.saveAndFlush(corporationDO);
        }

        log.info("新增企业主体完成：{}", JsonUtil.toJson(memberRegisterResultResp));
    }

    /**
     * 新增品牌会员
     *
     * @param req 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addBrandMember(UserLoginCacheDTO loginUser, BrandMemberAddReq req) {
        log.info("新增品牌会员：{}", JsonUtil.toJson(req));

        RunBrandDO runBrandDO = runBrandRepository.findFirstByCode(req.getBrandCode());
        if(runBrandDO==null){
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_NOT_EXISTS);
        }

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), loginUser.getMemberId(), baiTaiMemberProperties.getCustomerRoleId());

        // 检查是否为优时存量客户
        boolean isYouShiCustomer = checkIfYouShiCustomer(loginUser.getMemberId());

        // 新增品牌会员的注册资料复用当前会员的
        Set<MemberDepositoryDetailDO> depositDetails = relationDO.getDepositDetails().stream().filter(v -> Objects.equals(v.getVersion(), MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toSet());
        Map<String, String> memberDepositoryDetailDOMap = depositDetails.stream()
                .filter(detail -> detail.getFieldName() != null && detail.getDetail() != null) // 过滤掉null值
                .collect(Collectors.toMap(
                    MemberDepositoryDetailDO::getFieldName,
                    MemberDepositoryDetailDO::getDetail,
                    (existing, replacement) -> existing // 如果有重复键，保留第一个值
                ));

        //会员名称中，拼接经营品牌代码
        req.setName(IRunBrandService.buildMemberName(req.getName(),runBrandDO.getCode()));

        Map<String, Object> detail = MapUtil.<String, Object>builder()
                .put(MemberRegisterDetailConfigConstant.MEMBER_NAME, req.getName())
                .put(MemberRegisterDetailConfigConstant.CORPORATION_NAME, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.CORPORATION_NAME))
                .put(MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME))
                .put(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE))
                .put(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE))
                .put(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME))
                .put(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME))
                .put(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO))
//                .put(MemberRegisterDetailConfigConstant.LEGAL_PERSON_PHONE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_PHONE))
                .put(MemberRegisterDetailConfigConstant.ID_CARD_FRONT, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.ID_CARD_FRONT))
                .put(MemberRegisterDetailConfigConstant.ID_CARD_BACK, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.ID_CARD_BACK))
                .put(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER))
                .put(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE))
                .put(MemberRegisterDetailConfigConstant.FINANCE_MANAGER, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER))
                .put(MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE))
                .build();

        detail.entrySet().forEach(entry -> {
            if (Objects.isNull(entry.getValue())) {
                entry.setValue("");
            }
        });

        // 优时存量客户新增品牌会员时，跳过必填字段校验
        if (isYouShiCustomer) {
            ThreadLocalUtils.set(false);
            log.info("优时存量客户新增品牌会员，跳过必填字段校验，memberId: {}", loginUser.getMemberId());
        }

        MemberRegisterResultResp memberRegisterResultResp = baseRegisterService.registerPlatformMember(MemberRegisterSourceEnum.parseInt(req.getRegisterSource()), null, "+86", req.getPhone(), req.getPassword(), null, MemberTypeEnum.MERCHANT.getCode(), baiTaiMemberProperties.getCustomerRoleId(), detail, null, null);

        MemberDO memberDO = memberRepository.findById(memberRegisterResultResp.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));
        memberDO.setCorporationId(req.getCorporationId());
        //设置会员经营品牌
        memberDO.setBrandCode(runBrandDO.getCode());
        memberDO.setBrandName(runBrandDO.getName());
        memberRepository.saveAndFlush(memberDO);

        // 清理ThreadLocal，避免内存泄漏
        if (isYouShiCustomer) {
            ThreadLocalUtils.clear();
            log.debug("优时存量客户新增品牌会员完成，清理ThreadLocal，memberId: {}", loginUser.getMemberId());
        }

        log.info("新增品牌会员完成：{}", JsonUtil.toJson(memberRegisterResultResp));
    }

    /**
     * 获取企业认证状态
     *
     * @param loginUser 登录用户
     * @return 企业认证状态
     */
    @Override
    public CorporationCertificationStatusResp getCorporationCertificationStatus(UserLoginCacheDTO loginUser) {
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), loginUser.getMemberId(), baiTaiMemberProperties.getCustomerRoleId());

        CorporationCertificationStatusResp corporationCertificationStatusResp = new CorporationCertificationStatusResp();

        if (Objects.isNull(relationDO)) {
            corporationCertificationStatusResp.setStatus(CorporationCertificationStatusEnum.UN_CERTIFICATION.getCode());
        } else if (Objects.equals(relationDO.getVerified(), CommonBooleanEnum.YES.getCode())) {
            corporationCertificationStatusResp.setStatus(CorporationCertificationStatusEnum.CERTIFICATION_SUCCESS.getCode());
        } else if (Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED.getCode())) {
            corporationCertificationStatusResp.setStatus(CorporationCertificationStatusEnum.CERTIFICATION_NOT_PASSED.getCode());
        } else {
            corporationCertificationStatusResp.setStatus(CorporationCertificationStatusEnum.CERTIFICATION_PENDING.getCode());
        }
        return corporationCertificationStatusResp;
    }

    /**
     * 查询品牌会员
     *
     * @return 品牌会员
     */
    @Override
    public BrandCustomerResp getBrandMember(UserLoginCacheDTO sysUser, Long memberId) {
        MemberDO memberDO = memberRepository.findById(memberId).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));

        BrandCustomerResp brandCustomerResp = new BrandCustomerResp();
        brandCustomerResp.setId(memberDO.getId());
        brandCustomerResp.setName(memberDO.getName());
        brandCustomerResp.setAccount(memberDO.getAccount());
        brandCustomerResp.setPhone(memberDO.getPhone());
        brandCustomerResp.setRegisteredTrademark(memberDO.getRegisteredTrademark());

        return brandCustomerResp;
    }

    /**
     * 品牌会员编辑
     *
     * @param req 接口参数
     */
    @Override
    public void brandMemberUpdate(UserLoginCacheDTO sysUser, BrandMemberUpdateReq req) {
        log.info("品牌会员修改信息：{}", JsonUtil.toJson(req));

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), req.getMemberId(), baiTaiMemberProperties.getCustomerRoleId());

        BusinessAssertUtil.notNull(relationDO, ResponseCodeEnum.MC_MS_CORPORATION_CERTIFICATION_INFO_NOT_EXIST);

        MemberDO subMember = relationDO.getSubMember();
        if (Objects.nonNull(subMember.getCorporationId())) {
            List<Long> memberIds = memberRepository.findAllByCorporationId(subMember.getCorporationId()).stream().map(MemberDO::getId).collect(Collectors.toList());
            if (Objects.equals(req.getMemberId(), sysUser.getMemberId()) && !memberIds.contains(sysUser.getMemberId())) {
                throw new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED);
            }
        }

        // 优时客户不允许在商城修改
        if (!Objects.equals(subMember.getDataSource(), DataSourceEnum.MALL.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_YS_CUSTOMER_ALLOW_UPDATE_AT_MALL);
        }

        // 复用原来的入库资料
        Set<MemberDepositoryDetailDO> depositDetails = relationDO.getDepositDetails();
        Map<String, String> memberDepositoryDetailDOMap = depositDetails.stream().collect(Collectors.toMap(MemberDepositoryDetailDO::getFieldName, MemberDepositoryDetailDO::getDetail, (k1, k2) -> k1));

        Map<String, Object> detail = MapUtil.<String, Object>builder()
                .put(MemberRegisterDetailConfigConstant.MEMBER_NAME, req.getName())
                .put(MemberRegisterDetailConfigConstant.CORPORATION_NAME, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.CORPORATION_NAME))
                .put(MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME))
                .put(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE))
                .put(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE))
                .put(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME))
                .put(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME))
                .put(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO))
                .put(MemberRegisterDetailConfigConstant.LEGAL_PERSON_PHONE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_PHONE))
                .put(MemberRegisterDetailConfigConstant.ID_CARD_FRONT, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.ID_CARD_FRONT))
                .put(MemberRegisterDetailConfigConstant.ID_CARD_BACK, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.ID_CARD_BACK))
                .put(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER))
                .put(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE))
                .put(MemberRegisterDetailConfigConstant.FINANCE_MANAGER, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER))
                .put(MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE, memberDepositoryDetailDOMap.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE))
                .build();

        subMember.setRegisteredTrademark(req.getRegisteredTrademark());

        // 非平台会员才可以修改入库资料
        if (!Objects.equals(relationDO.getSubMemberLevelTypeEnum(), MemberLevelTypeEnum.MERCHANT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_MERCHANT_OR_CHANNEL_LEVEL_TYPE);
        }

        List<Integer> outerStatusList = Stream.of(MemberOuterStatusEnum.DEPOSITORY_PASSED, MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED, MemberOuterStatusEnum.MODIFY_PASSED, MemberOuterStatusEnum.MODIFY_NOT_PASSED).map(MemberOuterStatusEnum::getCode).collect(Collectors.toList());
        if(!outerStatusList.contains(relationDO.getOuterStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        try {
            baseMemberDepositDetailService.checkAndUpdateMemberDepositoryDetail(relationDO, detail, RoleTagEnum.MEMBER.getCode());
        } finally {
            ThreadLocalUtils.clear();
        }
    }

    @Override
    public List<MemberBranchResp> listMemberBranch(UserLoginCacheDTO sysUser) {
        UserDO userDO = userRepository.findById(sysUser.getUserId()).orElse(null);
        if(userDO==null){
            return Collections.emptyList();
        }
        return Optional.ofNullable(userDO.getBranches()).map(v -> v.stream().map(branch -> new MemberBranchResp(branch.getId(), branch.getName())).collect(Collectors.toList())).orElse(Lists.newArrayList());
    }

    /**
     * 检查是否为优时存量客户
     * 通过企业数据源判断，优时存量客户的数据源是EOS，而不是MALL
     *
     * @param memberId 会员ID
     * @return true-优时存量客户，false-非优时存量客户
     */
    private boolean checkIfYouShiCustomer(Long memberId) {
        try {
            log.debug("检查会员是否为优时存量客户，memberId: {}", memberId);

            // 查询会员信息
            MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
            if (memberDO == null) {
                log.debug("会员不存在，memberId: {}", memberId);
                return false;
            }

            // 如果会员有企业ID，查询企业信息
            if (memberDO.getCorporationId() != null) {
                CorporationDO corporationDO = corporationRepository.findById(memberDO.getCorporationId()).orElse(null);
                if (corporationDO != null) {
                    // 判断企业数据源是否为EOS（优时系统）
                    boolean isYouShi = Objects.equals(corporationDO.getDataSource(), DataSourceEnum.EOS.getCode());
                    log.debug("企业数据源判断结果，corporationId: {}, dataSource: {}, isYouShi: {}",
                        corporationDO.getId(), corporationDO.getDataSource(), isYouShi);
                    return isYouShi;
                }
            }

            log.debug("会员无企业信息或企业不存在，按非优时客户处理，memberId: {}", memberId);
            return false;
        } catch (Exception e) {
            log.error("检查优时存量客户状态失败，memberId: {}", memberId, e);
            // 异常时默认按非优时客户处理，保持原有校验逻辑
            return false;
        }
    }

}
