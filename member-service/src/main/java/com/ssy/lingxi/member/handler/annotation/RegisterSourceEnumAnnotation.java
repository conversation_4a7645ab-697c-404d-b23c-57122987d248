package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.RegisterSourceValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 注册来源校验注解
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/26
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = RegisterSourceValidator.class)
public @interface RegisterSourceEnumAnnotation {
    String message() default "注册来源不在定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    RegisterSourceValidatorType type() default RegisterSourceValidatorType.ALL;

    enum RegisterSourceValidatorType{
        ALL,
        WEB,
        MOBILE
    }
}
