package com.ssy.lingxi.member.model.req.discount.web;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户工费优惠附体明细请求对象
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerProcessFeeDiscountFtReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（更新时需要）
     */
    private Long id;

    /**
     * skuId 商品id
     */
    private Long skuId;

    /**
     * 克工费优惠
     */
    private BigDecimal gramFeeDiscount;

    /**
     * 是否无基本工费
     */
    private Integer isNoBaseFee;

    /**
     * 指定克工费
     */
    private BigDecimal specifiedGramFee;

    /**
     * 材质编码
     */
    private String materialCode;

    /**
     * 材质名称
     */
    private String materialName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 优惠截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime discountEndTime;
}
