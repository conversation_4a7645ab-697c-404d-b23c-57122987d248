package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.PwdSteEstimatorValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 密码强度校验注解
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/8/29
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = PwdSteEstimatorValidator.class)
public @interface PwdSteEstimatorAnnotation {
    String message() default "当前密码强度弱，请重新设置密码";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
