package com.ssy.lingxi.member.serviceImpl.web.comment;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.comment.MemberTradeCommentHistoryDO;
import com.ssy.lingxi.member.model.req.comment.*;
import com.ssy.lingxi.member.model.resp.comment.MemberOrderTradeCommentPageResp;
import com.ssy.lingxi.member.model.resp.comment.MemberTradeCommentDetailResp;
import com.ssy.lingxi.member.model.resp.comment.PlatformMemberTradeCommentPageResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditCommentSummaryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditTradeHistoryResp;
import com.ssy.lingxi.member.repository.comment.MemberTradeCommentHistoryRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberDetailService;
import com.ssy.lingxi.member.service.web.comment.IPlatformMemberCommentService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 平台后台-评价管理服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
@Service
public class PlatformMemberCommentServiceImpl implements IPlatformMemberCommentService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseMemberDetailService baseMemberDetailService;

    @Resource
    private MemberTradeCommentHistoryRepository memberTradeCommentHistoryRepository;

    @Resource
    private JdbcTemplate jdbcTemplate;

    /**
     * 交易能力 - 评价管理 - 评价分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberOrderTradeCommentPageResp> pageMemberOrderTradeCommentHistory(HttpHeaders headers, MemberOrderTradeCommentDataReq pageVO) {
        memberCacheService.needLoginFromManagePlatform(headers);

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());
        Specification<MemberTradeCommentHistoryDO> specification = (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            if (!ObjectUtils.isEmpty(pageVO.getStar())) {
                predicateList.add(cb.equal(root.get("star"), pageVO.getStar()));
            }
            if (!ObjectUtils.isEmpty(pageVO.getComment())) {
                predicateList.add(cb.like(root.get("comment"), "%" + pageVO.getComment().trim() + "%"));
            }

            if (!ObjectUtils.isEmpty(pageVO.getDealTimeStart())) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get("dealTime").as(LocalDateTime.class), pageVO.getDealTimeStart()));
            }
            if (!ObjectUtils.isEmpty(pageVO.getDealTimeEnd())) {
                predicateList.add(cb.lessThanOrEqualTo(root.get("dealTime").as(LocalDateTime.class), pageVO.getDealTimeEnd()));
            }
            if (!ObjectUtils.isEmpty(pageVO.getCreateTimeStart())) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), pageVO.getCreateTimeStart()));
            }
            if (!ObjectUtils.isEmpty(pageVO.getCreateTimeEnd())) {
                predicateList.add(cb.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), pageVO.getCreateTimeEnd()));
            }
            if (!ObjectUtils.isEmpty(pageVO.getMemberName())) {
                predicateList.add(cb.like(root.get("memberName"), "%" + pageVO.getMemberName().trim() + "%"));
            }
            if (!ObjectUtils.isEmpty(pageVO.getSubMemberName())) {
                predicateList.add(cb.like(root.get("subMemberName"), "%" + pageVO.getSubMemberName().trim() + "%"));
            }
            if (!ObjectUtils.isEmpty(pageVO.getProduct())) {
                predicateList.add(cb.like(root.get("product"), "%" + pageVO.getProduct() + "%"));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        };


        Page<MemberTradeCommentHistoryDO> pageList = memberTradeCommentHistoryRepository.findAll(specification, page);

        List<MemberOrderTradeCommentPageResp> resultList = pageList.stream().map(e -> {
            MemberOrderTradeCommentPageResp commentPageVO = new MemberOrderTradeCommentPageResp();
            BeanUtils.copyProperties(e, commentPageVO);
            commentPageVO.setOrderNo(e.getOrderNo());
            return commentPageVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 交易能力 - 评价管理 - 评价详情
     * @param headers Http头部信息
     * @param tradeCommentIdVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberTradeCommentDetailResp getMemberTradeCommentHistory(HttpHeaders headers, MemberTradeCommentIdReq tradeCommentIdVO) {
        memberCacheService.needLoginFromManagePlatform(headers);
        MemberTradeCommentHistoryDO commentHistoryDO = memberTradeCommentHistoryRepository.findById(tradeCommentIdVO.getId()).orElse(null);
        if (ObjectUtils.isEmpty(commentHistoryDO)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_COMMENT_NOT_FOUND);
        }

        // 返回
        MemberTradeCommentDetailResp commentPageVO = new MemberTradeCommentDetailResp();
        commentPageVO.setId(commentHistoryDO.getId());
        commentPageVO.setMemberName(commentHistoryDO.getMemberName());
        commentPageVO.setStar(commentHistoryDO.getStar());
        commentPageVO.setComment(Optional.ofNullable(commentHistoryDO.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(commentHistoryDO.getComment()));
        commentPageVO.setProduct(commentHistoryDO.getProduct());
        commentPageVO.setOrderNo(commentHistoryDO.getOrderNo());
        commentPageVO.setDealTime(commentHistoryDO.getDealTime());
        commentPageVO.setPrice(commentHistoryDO.getPrice());
        commentPageVO.setTotalPrice(commentHistoryDO.getTotalPrice());
        commentPageVO.setPics(commentHistoryDO.getPics());
        commentPageVO.setProductImgUrl(commentHistoryDO.getProductImgUrl());
        commentPageVO.setPurchaseCount(commentHistoryDO.getDealCount());
        commentPageVO.setUnit(commentHistoryDO.getUnit());

        return commentPageVO;
    }

    /**
     * 平台后台 - 评价管理 - 会员评价查询分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<PlatformMemberTradeCommentPageResp> pagePlatformMemberTradeComment(HttpHeaders headers, PlatformMemberTradeCommentDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        //构建With语句，头和尾部
        String countFormat = "with tmp as (%s) select count(*) from tmp";

        //构建Native查询语句
        StringBuilder joinTableBuilder = new StringBuilder();
        joinTableBuilder.append("select mmmtch.sub_member_id as memberId , mmm.name as memberName , mmmtch.sub_role_id as roleId, mmr.role_name as roleName, mmr.member_type as memberType, mmmlr.level_tag as levelTag, round(avg(mmmtch.star)) as avgStar, count(*) as receiveCountTotal, sum(case when date_part('day',  localtimestamp - mmmtch.create_time) <= 7 then 1 else 0 end) as receiveCount7, sum(case when date_part('day',  localtimestamp - mmmtch.create_time) <= 180 then 1 else 0 end) as receiveCount180  from mem_member_trade_comment_history mmmtch \n" +
                "left outer join mem_member mmm on mmmtch.sub_member_id = mmm.id \n" +
                "left outer join mem_member_role mmr on mmmtch.sub_role_id = mmr.id \n" +
                "left outer join mem_member_level_right mmmlr on mmmtch.sub_member_id = mmmlr.sub_member_id and mmmtch.sub_role_id = mmmlr.sub_role_id \n");

        StringBuilder whereBuilder = new StringBuilder();
        whereBuilder.append(String.format("where mmmlr.member_id = %d and mmmlr.role_id = %d ", loginUser.getMemberId(), loginUser.getMemberRoleId()));

        String groupByStr = " group by mmmtch.sub_member_id , mmm.name, mmmtch.sub_role_id, mmr.role_name, mmr.member_type, mmmlr.level_tag ";

        String orderByStr = " order by mmmtch.sub_member_id , mmmtch.sub_member_id ";

        String limitStr = String.format(" limit %d offset %d", pageVO.getPageSize(), (pageVO.getCurrent() -1) * pageVO.getPageSize());

        StringBuilder havingBuilder = new StringBuilder();
        if(pageVO.getAvgStar() != null && !pageVO.getAvgStar().equals(0)) {
            havingBuilder.append(" having round(avg(mmmtch.star)) = ").append(pageVO.getAvgStar()).append(" ");
        }

        if (pageVO.getMemberType() != null && !pageVO.getMemberType().equals(0)) {
            whereBuilder.append(" and mmr.member_type = ").append(pageVO.getMemberType());
        }

        if(pageVO.getRoleId() != null && !pageVO.getRoleId().equals(0L)) {
            whereBuilder.append(" and mmmtch.sub_role_id = ").append(pageVO.getRoleId());
        }

        if(pageVO.getLevel() != null && !pageVO.getLevel().equals(0)) {
            whereBuilder.append(" and mmmlr.level = ").append(pageVO.getLevel());
        }

        if(StringUtils.hasLength(pageVO.getMemberName())) {
            whereBuilder.append(" and mmm.name like '%").append(pageVO.getMemberName().trim()).append("%'");
        }

        String nativeSqlStr = joinTableBuilder.append(whereBuilder).append(groupByStr).append(havingBuilder).append(orderByStr).toString();
        String nativeCountStr = String.format(countFormat, nativeSqlStr);

        //拼接完 count 语句后，才拼接分页查询条件
        nativeSqlStr += limitStr;

        System.out.println(nativeCountStr);
        System.out.println(nativeSqlStr);

        Long totalCount = jdbcTemplate.queryForObject(nativeCountStr, Long.class);
        if(totalCount == null) {
            totalCount = 0L;
        }

        List<PlatformMemberTradeCommentPageResp> resultList = new ArrayList<>();
        if(totalCount > 0) {
            resultList = jdbcTemplate.query(nativeSqlStr, new BeanPropertyRowMapper<>(PlatformMemberTradeCommentPageResp.class));
        }

        resultList.forEach(res -> res.setMemberTypeName(MemberTypeEnum.getName(res.getMemberType())));

        return new PageDataResp<>(totalCount, resultList);
    }

    @Override
    public void deletePlatformMemberTradeComment(HttpHeaders headers, MemberTradeCommentIdsReq tradeCommentIdsVO) {
        memberCacheService.needLoginFromManagePlatform(headers);

        List<Long> ids = tradeCommentIdsVO.getIds();
        memberTradeCommentHistoryRepository.deleteAllByIdIn(ids);

    }

    @Override
    public void updateStatusPlatformMemberTradeComment(HttpHeaders headers, MemberTradeCommentUpdateStatusReq updateStatusVO) {
        memberCacheService.needLoginFromManagePlatform(headers);

        MemberTradeCommentHistoryDO memberTradeCommentHistoryDO = memberTradeCommentHistoryRepository.findById(updateStatusVO.getId()).orElse(null);
        if (ObjectUtils.isEmpty(memberTradeCommentHistoryDO)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_COMMENT_NOT_FOUND);
        }

        memberTradeCommentHistoryDO.setStatus(updateStatusVO.getStatus());
        memberTradeCommentHistoryRepository.saveAndFlush(memberTradeCommentHistoryDO);


    }

    /**
     * 会员详情 - 会员信用 - 交易评价汇总（平台层面汇总）
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getAllMemberDetailCreditTradeCommentSummary(HttpHeaders headers, PlatformMemberTradeCommentSummaryReq pageVO) {
        memberCacheService.needLoginFromManagePlatform(headers);
        return baseMemberDetailService.getAllMemberDetailCreditTradeCommentSummary(pageVO.getMemberId(), pageVO.getRoleId());
    }

    /**
     * 平台后台-评价管理-分页查询交易评论历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditTradeHistoryResp> pagePlatformMemberTradeCommentHistory(HttpHeaders headers, PlatformMemberTradeCommentHistoryDataReq pageVO) {
        memberCacheService.needLoginFromManagePlatform(headers);
        return baseMemberDetailService.pageAllMemberDetailCreditTradeCommentHistory(pageVO.getMemberId(), pageVO.getRoleId(), pageVO.getStarLevel(), pageVO.getCurrent(), pageVO.getPageSize());
    }
}
