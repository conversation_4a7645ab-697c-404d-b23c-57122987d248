package com.ssy.lingxi.member.repository;


import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 业务平台 - 角色数据库操作JpaRepository
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-15
 */
@Repository
public interface MemberRoleRepository extends JpaRepository<MemberRoleDO, Long>, JpaSpecificationExecutor<MemberRoleDO> {
    boolean existsByRoleName(String roleName);

    boolean existsByRoleNameAndIdNot(String roleName, long id);

    List<MemberRoleDO> findFirstByRelTypeNotAndStatus(Integer relType, Integer status);

    List<MemberRoleDO> findAllByRelTypeNotAndRoleTypeAndStatus(Integer relType, Integer roleType, Integer status);

    List<MemberRoleDO> findAllByStatusAndRelType(Integer status, Integer relType);

    List<MemberRoleDO> findByMemberTypeAndStatus(Integer memberType, Integer status);

    List<MemberRoleDO> findByStatus(Integer status);

    List<MemberRoleDO> findByStatusAndIsShow(Integer status, Boolean isShow);

    MemberRoleDO findFirstByRelType(Integer relType);

    List<MemberRoleDO> findByRoleType(Integer roleType);

    MemberRoleDO findFirstByIdAndRelTypeNotAndStatus(Long id, Integer relType, Integer status);
}
