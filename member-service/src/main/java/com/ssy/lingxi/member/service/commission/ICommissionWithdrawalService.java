package com.ssy.lingxi.member.service.commission;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.commission.CommissionWithdrawalQueryReq;
import com.ssy.lingxi.member.model.req.commission.WithdrawalManagementQueryReq;
import com.ssy.lingxi.member.model.resp.commission.CommissionWithdrawalResp;
import com.ssy.lingxi.member.model.resp.commission.WithdrawalManagementResp;

/**
 * 佣金提现服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
public interface ICommissionWithdrawalService {

    /**
     * 分页查询佣金提现记录列表
     * @param loginUser 登录用户
     * @param request 查询请求
     * @return 分页结果
     */
    PageDataResp<CommissionWithdrawalResp> getCommissionWithdrawalPage(UserLoginCacheDTO loginUser, CommissionWithdrawalQueryReq request);

    /**
     * 分页查询提现管理列表
     * @param loginUser 登录用户
     * @param request 查询请求
     * @return 分页结果
     */
    PageDataResp<WithdrawalManagementResp> getWithdrawalManagementPage(UserLoginCacheDTO loginUser, WithdrawalManagementQueryReq request);

    /**
     * 审核提现申请
     * @param loginUser 登录用户
     * @param withdrawalId 提现记录id
     * @param approved 是否通过审核：true-通过，false-拒绝
     * @param remark 审核备注
     * @return 操作结果
     */
    Void approveWithdrawal(UserLoginCacheDTO loginUser, Long withdrawalId, Boolean approved, String remark);

    /**
     * 财务打款
     * @param loginUser 登录用户
     * @param withdrawalId 提现记录id
     * @param success 是否打款成功：true-成功，false-失败
     * @param remark 打款备注
     * @return 操作结果
     */
    Void processPayment(UserLoginCacheDTO loginUser, Long withdrawalId, Boolean success, String remark);

    /**
     * 用户申请提现
     * @param loginUser 登录用户
     * @param bankCardId 银行卡id
     * @param withdrawalAmount 提现金额
     * @return 操作结果
     */
    Void applyWithdrawal(UserLoginCacheDTO loginUser, Long bankCardId, java.math.BigDecimal withdrawalAmount);

    /**
     * 导出提现管理列表
     * @param loginUser 登录用户
     * @param request 查询请求
     * @param response HTTP响应
     */
    void exportWithdrawalManagement(UserLoginCacheDTO loginUser, WithdrawalManagementQueryReq request, javax.servlet.http.HttpServletResponse response);
}
