package com.ssy.lingxi.member.controller.web.supplier;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.MemberVisitAddOrUpdateRequest;
import com.ssy.lingxi.member.model.req.basic.MemberVisitListDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberVisitListResp;
import com.ssy.lingxi.member.model.resp.basic.MemberVisitTypeItemResp;
import com.ssy.lingxi.member.service.web.IMemberVisitService;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商能力 - 会员拜访相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-07
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/supplier/visit")
public class SupplierVisitController extends BaseController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.SUPPLIER.getCode();

    @Resource
    private IMemberVisitService memberVisitService;

    /**
     * 查询会员拜访列表
     * @param request
     * @return
     */
    @GetMapping("/list")
    public WrapperResp<PageDataResp<MemberVisitListResp>> memberVisitList(MemberVisitListDataReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(memberVisitService.memberVisitList(request,sysUser, roleTag));
    }

    /**
     * 查询会员拜访详情
     * @param id
     * @return
     */
    @GetMapping("/details")
    public WrapperResp<MemberVisitListResp> memberVisitDetails(Long id, HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(memberVisitService.memberVisitDetails(id, sysUser, roleTag));
    }

    /**
     * 查询全部会员拜访
     * @param httpServletRequest
     * @return
     */
    @GetMapping("/all")
    public WrapperResp<List<MemberVisitListResp>> memberVisitAll(HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(memberVisitService.memberVisitAll(sysUser));
    }

    /**
     * 添加/修改会员拜访
     * @param request
     * @return
     */
    @PostMapping("/addOrUpdate")
    public WrapperResp<Void> memberVisitAddOrUpdate(@RequestBody @Valid MemberVisitAddOrUpdateRequest request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
         memberVisitService.memberVisitAddOrUpdate(request,sysUser);
        return WrapperUtil.success();
    }

    /**
     * 删除会员拜访
     * @return
     */
    @PostMapping("/delete")
    public WrapperResp<Void> memberVisitDelete(@RequestBody @Validated CommonIdReq request) {
         memberVisitService.memberVisitDelete(request);
        return WrapperUtil.success();
    }

    /**
     * 查询拜访类型, 拜访级别枚举
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/visitType/items")
    public WrapperResp<MemberVisitTypeItemResp> getMemberVisitOfVisitTypeItems(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberVisitService.getMemberVisitOfVisitTypeItems(headers));
    }

}
