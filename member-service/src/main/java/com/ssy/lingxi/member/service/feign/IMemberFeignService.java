package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;

import java.util.List;

/**
 * 会员信息查询对外Feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-11
 */
public interface IMemberFeignService {

    /**
     * 新增会员支付策略 - 查询作为服务提供者的会员信息列表
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberFeignPayProviderResultResp> findPayProviderMembers(MemberFeignPayProviderDataReq pageVO);

    /**
     * 新增会员支付策略 - 选择适用会员
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberFeignPayProviderResultResp> pageNewPayProviderMembers(MemberFeignPayProviderExcludeDataReq pageVO);

    /**
     * 模板服务 - 模糊查询会员信息列表
     * @param listVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignListResultResp> findMembersFromTemplateService(MemberFeignListReq listVO);

    /**
     * 根据会员Id和角色Id，查询上属会员列表（不区分企业会员、渠道会员）
     * <p>其中等级为当前会员在其上级会员和上级会员角色下的等级</p>
     * @param feignVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignQueryResp> listUpperMembers(MemberFeignReq feignVO);

    /**
     * 根据会员Id和角色Id，查询下属“服务消费者”角色的会员列表（不区分企业会员、渠道会员）
     * <p>其中等级为下级会员和角色在当前会员和角色下的等级</p>
     * @param feignVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignQueryResp> listLowerMembers(MemberFeignSubReq feignVO);

    /**
     * 根据会员Id和角色Id，查询“审核通过”的下级会员列表（不区分会员类型、角色类型）
     * <p>返回结果中的等级为下级会员和角色在当前会员和角色下的等级</p>
     * @param feignVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignQueryResp> listAllLowerMembers(MemberFeignReq feignVO);

    /**
     * 根据会员Id和角色Id，查询“审核通过”的下级会员列表（角色类型为服务提供者）
     * <p>返回结果中的等级为下级会员和角色在当前会员和角色下的等级</p>
     * @param feignVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignQueryResp> listSubProviderLowerMembers(MemberFeignReq feignVO);

    /**
     * 根据会员Id、角色Id、上级会员Id、上级会员角色Id，查询会员信息
     * <p>其中等级为当前会员在其上级会员和上级会员角色下的等级</p>
     * @param feignVO 接口参数
     * @return 查询结果
     */
    MemberFeignQueryResp getMemberInfo(MemberRelationFeignReq feignVO);

    /**
     * 根据会员Id，查询会员注册信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberFeignRegisterQueryResp getMemberRegisterInfo(MemberFeignIdReq idVO);

    /**
     * 根据会员Id列表，查询具有“服务提供者”角色类型的会员信息
     * @param queryVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignBatchByIdQueryResp> batchFindMembersByIdList(MemberFeignBatchByIdReq queryVO);

    /**
     * 根据会员Id列表，查询具有“服务提供者”角色类型的“平台会员”信息
     * @param idsVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignShopQueryResp> batchFindServiceProviderMemberByIdList(MemberFeignIdsReq idsVO);

    /**
     * 根据会员名称，查询具有“服务提供者”角色类型的企业会员、企业个人会员的Id列表
     * @param nameVO 接口参数
     * @return 查询结果
     */
    MemberFeignIdsResultResp findMerchantAndProviderMemberIdListByName(MemberFeignNameReq nameVO);

    /**
     * 根据会员Id、角色Id查询平台会员信息
     * <p>其中等级为当前会员的平台等级</p>
     * @param memberFeignReqList 接口参数
     * @return 查询结果
     **/
    List<MemberFeignQueryResp> listPlatformMembers(List<MemberFeignReq> memberFeignReqList);

    /**
     * 根据会员Id、角色Id查询会员信息
     **/
    List<MemberFeignQueryResp> listMembers(List<MemberFeignReq> memberFeignReqList);

    /**
     * 根据会员Id和加密后的支付密码，校验支付密码
     * @param checkVO 接口参数
     * @return 查询结果
     */
    MemberFeignPayPswCheckResultResp checkMemberPayPassword(MemberFeignPayPswCheckReq checkVO);

    /**
     * 批量查询会员Logo
     * @param memberIds 会员id列表
     * @return 查询结果
     */
    List<MemberFeignLogoResp> getMemberLogos(List<Long> memberIds);

    /**
     * 批量查询会员角色名称
     * @param roleIds 会员角色Id列表
     * @return 查询结果
     */
    List<MemberFeignRoleResp> getRoles(List<Long> roleIds);

    /**
     * 根据会员ID批量查询IM客服用户
     * @param memberIds 会员id列表
     * @return 查询结果
     */
    List<MemberFeignImUserResp> getImUsers(List<Long> memberIds);

    /**
     * 根据用户ID批量查询用户信息
     * @param userIds 用户id列表
     * @return 查询结果
     */
    List<MemberFeignUserResp> getUsers(List<Long> userIds);

    /**
     * 用根据户ID查询用户信息
     * @return 查询结果
     */
    MemberFeignUserResp getUser(Long userId);

    /**
     * 订单服务，查询流程规则适用会员列表
     * @param feignVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignPageQueryResp> findPlatformMembers(List<MemberFeignReq> feignVO);

    /**
     * 订单服务，商户支付参数配置，查询平台服务提供者企业会员列表
     * @param paymentVO 接口参数
     * @return 查询结果
     **/
    List<MemberFeignPageQueryResp> findProviderMerchant(MemberFeignPaymentReq paymentVO);

    /**
     * 查询是否下级会员
     * @param feignVO 接口参数
     * @return 查询结果
     */
    Boolean isSubMember(MemberRelationFeignReq feignVO);

    /**
     * 能力中心 - 营销服务, 查询下级会员适用会员
     * @param memberAndUpperMembersReq 接口参数
     * @return 查询结果
     */
    List<AtSubMemberSuitableMemberResp> listAbilitySubMemberSuitableMember(MemberAndUpperMembersReq memberAndUpperMembersReq);

    /**
     * 平台后台 - 营销服务, 查询下级会员适用会员
     * @param memberFeignReq 接口参数
     * @return 查询结果
     */
    PfSubMemberSuitableMemberResp getPlatformSubMemberSuitableMember(MemberFeignReq memberFeignReq);

    /**
     * 所有服务通用 - 查询平台规则配置
     * @param feignVO 接口参数
     * @return 查询结果
     */
    List<MemberRuleDetailResp> findMemberRules(MemberRuleDetailFeignReq feignVO);

    /**
     * 根据会员关系，查询业务员Id
     * @param feignVO 接口参数
     * @return 查询结果
     */
    List<MemberSalesFeignResp> findMemberSales(List<MemberFeignRelationReq> feignVO);

    /**
     * 筛选角色类型为服务提供者的会员
     * @param members 会员Id和角色Id的缓存实体
     * @return 筛选结果
     */
    List<MemberAndRoleIdDTO> screenMemberUser(List<MemberAndRoleIdDTO> members);

    /**
     * 查找所有供应商
     */
    List<MemberAndRoleIdDTO> allSupplierList();

    /**
     * 合同能力- 查询会员合同模版所需参数
     * @param queryVOS 接口参数
     * @return 查询结果
     */
    List<MemberContractDetailResp> getMemberContractDetail(List<MemberContractQueryReq> queryVOS);

    /**
     * 查询指定会员的生命周期规则配置相关
     * @param memberGetLifecycleFeignReq 接口参数
     * @return 查询结果
     */
    MemberLifecycleRuleQueryResp getLifecycle(MemberGetLifecycleFeignReq memberGetLifecycleFeignReq);

    /**
     * （营销服务v3）优惠券发券 - 发券时分页查询会员列表
     * @param req 接口参数
     * @return 查询结果
     */
    PageDataResp<MarketingMemberFeignResp> pageMarketingCouponMembers(MarketingMemberFeignReq req);

    /**
     * （营销服务v3）优惠券发券 - 查询已经发券的会员列表
     * @param req 接口参数
     * @return 查询结果
     */
    List<MarketingMemberFeignResp> findMarketingCouponMembers(MarketingDistributeMemberFeignReq req);

    /**
     * 根据会员Id，查询会员名称
     */
    MemberNameResp getMemberName(MemberFeignIdReq req);

    /**
     * 查询商家会员id，和角色id
     * @return 查询结果
     */
    MemberFeignMerchantResp getMerchantMember();

    /**
     * 根据用户编号查询用户信息
     * @param code
     * @return
     */
    MemberFeignCodeRes findByCode(MemberFeignCodeReq memberFeignCodeReq);

    /**
     * 根据会员Id查询会员信息
     * @param getMemberByIdReq 接口参数
     * @return 查询结果
     */
    MemberFeignCodeRes findMemberById(GetMemberByIdReq getMemberByIdReq);

    /**
     * 根据会员id，查询会员信息
     * @param commonIdListReq 会员id
     * @return 查询结果
     */
    List<MemberFeignCodeRes> findMemberByIdList(CommonIdListReq commonIdListReq);

    /**
     * 查询商家信息和超级管理员信息
     * @return 查询结果
     */
    MemberFeignMerchantAndAdminResp getMerchantAndAdminInfo();

    /**
     * 根据企业Id列表查询会员信息
     * @param corporationIds
     * @return
     */
    List<MemberInfoResp> findByCorporationIds(List<Long> corporationIds);

    /**
     * 根据会员id，会员code
     *
     * @param memberId 会员id
     * @return 查询结果
     */
    String findByMemberId(CommonIdReq memberId);

    /**
     * 根据会员id，会员code
     *
     * @param commonIdListReq 会员id
     * @return 查询结果
     */
    List<MemberBrandInfoResp> findMemberBrandByIds(CommonIdListReq commonIdListReq);

    /**
     * 根据买家会员id，查询（营业证统一编码，手机号，邮箱）
     * @param memberId 会员id
     * @return 查询结果
     */
    MemberInsuredIdNoResp findBusinessInfoByMemberId(CommonIdReq memberId);

    /**
     * 根据会员id，会员code
     *
     * @param commonIdReq 店铺id
     * @return 查询结果
     */
    MemberBrandInfoResp findMemberBrandById(CommonIdReq commonIdReq);

}
