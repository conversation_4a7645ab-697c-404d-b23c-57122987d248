package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.EnterpriseCertificationDraftDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 企业认证 repository
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/4
 */
public interface EnterpriseCertificationDraftRepository extends JpaRepository<EnterpriseCertificationDraftDO, Long>, JpaSpecificationExecutor<EnterpriseCertificationDraftDO> {

    EnterpriseCertificationDraftDO findFirstByMemberIdOrderByIdDesc(Long memberId);

}
