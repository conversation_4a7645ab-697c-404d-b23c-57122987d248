package com.ssy.lingxi.member.serviceImpl.mobile;

import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.mobile.IBaseMobileService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;

/**
 * App - 辅助功能相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-29
 */
@Service
public class BaseMobileServiceImpl implements IBaseMobileService {
    @Resource
    private MemberRelationRepository relationRepository;

    /**
     * 根据用户判断是否具有“可登录”的服务消费者角色
     *
     * @param user 用户
     * @return true-是，false-否
     */
    @Override
    public Boolean hasNoConsumerRole(UserDO user) {
        MemberDO member = user.getMember();
        if(member == null) {
            return true;
        }

        Pageable pageable = PageRequest.of(0, 1);
        //登录会员上下级会员的关系列表
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), member.getId()));
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //查询服务消费者角色
            Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);
        return CollectionUtils.isEmpty(pageList.getContent());
    }
}
