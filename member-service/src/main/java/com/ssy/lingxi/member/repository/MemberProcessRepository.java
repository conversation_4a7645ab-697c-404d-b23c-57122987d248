package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.detail.MemberProcessDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员流程Jpa仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-14
 */
@Repository
public interface MemberProcessRepository extends JpaRepository<MemberProcessDO, Long>, JpaSpecificationExecutor<MemberProcessDO> {

    MemberProcessDO findFirstByProcessTypeAndIsDefault(Integer processType, Boolean isDefault);

    MemberProcessDO findFirstByProcessKey(String processKey);

    List<MemberProcessDO> findAllByProcessType(Integer processType);
}
