package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 会员生命周期流程更新状态
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
@Data
public class MemberCycleProcessUpdateStatusReq implements Serializable {

    private static final long serialVersionUID = 8904373628462478626L;

    /**
     * 流程规则配置id
     */
    @NotNull(message = "流程规则配置id要大于0")
    @Positive(message = "流程规则配置id要大于0")
    private Long processId;

    /**
     * 状态
     * 0 - 停用
     * 1 - 启用
     */
    @NotNull(message = "状态值枚举：0-停用，1-启用")
    @Min(value = 0, message = "状态值枚举：0-停用，1-启用")
    @Max(value = 1, message = "状态值枚举：0-停用，1-启用")
    private Integer status;

}
