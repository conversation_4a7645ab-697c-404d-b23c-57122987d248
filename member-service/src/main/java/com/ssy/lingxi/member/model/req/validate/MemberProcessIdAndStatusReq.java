package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.member.handler.annotation.EnableDisableStatusAnno;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 会员流程规则配置 - 修改流程规则状态接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-15
 */
@Data
public class MemberProcessIdAndStatusReq implements Serializable {
    private static final long serialVersionUID = -9203653484302020680L;

    /**
     * 流程规则Id
     */
    @NotNull(message = "流程规则Id要大于0")
    @Positive(message = "流程规则Id要大于0")
    private Long id;

    /**
     * 流程规则状态，0-无效，1-有效
     */
    @EnableDisableStatusAnno(message = "流程规则状态：0-无效，1-有效")
    private Integer status;
}
