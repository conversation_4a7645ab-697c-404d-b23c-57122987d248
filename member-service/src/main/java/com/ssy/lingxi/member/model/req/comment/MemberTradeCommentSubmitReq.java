package com.ssy.lingxi.member.model.req.comment;

import com.ssy.lingxi.member.api.model.req.MemberTradeCommentSubmitDetailReq;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 订单评价接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@Data
public class MemberTradeCommentSubmitReq implements Serializable {
    private static final long serialVersionUID = 1577411288889524742L;

    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空")
    private Long orderId;

    /**
     * 发表评价集合
     */
    @NotEmpty(message = "发表评价集合不能为空")
    @Valid
    private List<MemberTradeCommentSubmitDetailReq> commentSubmitDetailList;
}
