package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.MemberChangeRequestFormHistoryDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 供应商生命周期管理 - 变更申请单历史记录Repository
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
@Repository
public interface MemberChangeRequestFormHistoryRepository extends JpaRepository<MemberChangeRequestFormHistoryDO, Long>, JpaSpecificationExecutor<MemberChangeRequestFormHistoryDO> {
    List<MemberChangeRequestFormHistoryDO> findByChangeRequestFormId(Long appraisalId, Sort sort);
}
