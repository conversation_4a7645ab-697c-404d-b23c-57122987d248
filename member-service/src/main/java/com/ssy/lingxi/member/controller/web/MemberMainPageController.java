package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.MemberLogoReq;
import com.ssy.lingxi.member.model.req.basic.MemberUpdateRegisterDetailReq;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRegisterDetailResp;
import com.ssy.lingxi.member.service.web.IMemberMainPageService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * “首页”相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-01-23
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mainpage")
public class MemberMainPageController {

    @Resource
    private IMemberMainPageService memberMainPageService;

    /**
     * 新增或修改会员Logo
     * @param headers Http头部信息
     * @param logoVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/logo/add")
    public WrapperResp<Void> addMemberUserLogo(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLogoReq logoVO) {
         memberMainPageService.addMemberUserLogo(headers, logoVO);
        return WrapperUtil.success();
    }

    /**
     * “首页” - 审核不通过时，查询会员注册资料信息
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/detail/get")
    public WrapperResp<MemberRegisterDetailResp> getMemberRegisterDetail(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberMainPageService.getMemberRegisterDetail(headers));
    }

    /**
     * “首页” - 审核不通过时，修改会员注册资料
     * @param headers Http头部信息
     * @param detailVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/detail/update")
    public WrapperResp<Void> updateMemberRegisterDetail(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUpdateRegisterDetailReq detailVO) {
        memberMainPageService.updateMemberRegisterDetail(headers, detailVO);
        return WrapperUtil.success();
    }

    /**
     * 判断当前登录会员，是否指定会员的上级会员
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/upper/inquiry")
    public WrapperResp<Boolean> isUpperMember(@RequestHeader HttpHeaders headers, @Valid MemberAndRoleIdReq idVO) {
        return WrapperUtil.success(memberMainPageService.isUpperMember(headers, idVO));
    }
}
