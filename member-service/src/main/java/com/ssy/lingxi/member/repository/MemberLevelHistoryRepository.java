package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelHistoryDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员升级记录操作Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-14
 */
@Repository
public interface MemberLevelHistoryRepository extends JpaRepository<MemberLevelHistoryDO, Long>, JpaSpecificationExecutor<MemberLevelHistoryDO> {

    Page<MemberLevelHistoryDO> findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Pageable pageable);

    List<MemberLevelHistoryDO> findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId);

    boolean existsByMemberIdAndRoleIdAndSubMemberIdAndSubRoleIdAndRemark(Long memberId, Long roleId, Long subMemberId, Long subRoleId, String remark);

    @Transactional
    void deleteAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    @Transactional
    void deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);
}
