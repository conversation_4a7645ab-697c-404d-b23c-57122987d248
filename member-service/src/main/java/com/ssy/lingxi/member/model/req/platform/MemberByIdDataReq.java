package com.ssy.lingxi.member.model.req.platform;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 根据会员id分页查询会员列表VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-3-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberByIdDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -3635868919284431267L;

    /**
     * 会员id
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;
}
