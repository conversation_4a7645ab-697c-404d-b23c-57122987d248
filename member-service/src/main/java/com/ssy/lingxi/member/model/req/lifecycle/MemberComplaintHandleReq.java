package com.ssy.lingxi.member.model.req.lifecycle;

import com.ssy.lingxi.component.base.annotation.DateTimeStringFormatAnnotation;
import com.ssy.lingxi.member.model.req.basic.FileUploadReq;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 会员投诉与建议处理VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Data
public class MemberComplaintHandleReq implements Serializable {
    private static final long serialVersionUID = -6821550268402993584L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 处理人用户id
     */
    private Long handleUserId;

    /**
     * 处理人用户编辑名称
     */
    private String handleUserEditName;

    /**
     * 处理人用户编辑手机
     */
    private String handleUserEditPhone;

    /**
     * 处理结果
     */
    @NotBlank(message = "处理结果不能为空")
    private String handleResult;

    /**
     * 处理时间，格式为yyyy-MM-dd HH:mm:ss
     */
    @DateTimeStringFormatAnnotation(message = "事件时间不能为空")
    private String handleTime;

    /**
     * 处理附件
     */
    @Valid
    private List<FileUploadReq> handleAttachments;
}
