package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.PurchaseContractPayTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 采购合同付款方式校验注解
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = PurchaseContractPayTypeValidator.class)
public @interface PurchaseContractPayTypeAnnotation {
    String message() default "采购合同付款方式不在定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
