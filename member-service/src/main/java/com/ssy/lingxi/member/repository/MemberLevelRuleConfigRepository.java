package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRuleConfigDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员升级规则操作Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-07
 */
@Repository
public interface MemberLevelRuleConfigRepository extends JpaRepository<MemberLevelRuleConfigDO, Long>, JpaSpecificationExecutor<MemberLevelRuleConfigDO> {
    List<MemberLevelRuleConfigDO> findByMemberIdAndRoleId(Long memberId, Long roleId);

    Page<MemberLevelRuleConfigDO> findByMemberIdAndRoleId(Long memberId, Long roleId, Pageable pageable);

    @Transactional
    void deleteByMemberIdAndRoleId(Long memberId, Long roleId);
}
