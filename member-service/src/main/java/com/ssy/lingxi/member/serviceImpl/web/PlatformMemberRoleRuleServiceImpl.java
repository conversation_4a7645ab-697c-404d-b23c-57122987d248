package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleRuleDO;
import com.ssy.lingxi.member.model.req.basic.MemberIdReq;
import com.ssy.lingxi.member.model.req.platform.*;
import com.ssy.lingxi.member.model.resp.platform.*;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.repository.MemberRoleRepository;
import com.ssy.lingxi.member.repository.RoleRuleRepository;
import com.ssy.lingxi.member.service.base.IBaseSiteService;
import com.ssy.lingxi.member.service.web.IPlatformMemberRoleRuleService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台后台 - 会员角色规则配置相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-10
 **/
@Service
public class PlatformMemberRoleRuleServiceImpl implements IPlatformMemberRoleRuleService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private MemberRoleRepository roleRepository;

    @Resource
    private RoleRuleRepository roleRuleRepository;

    @Resource
    private IBaseSiteService siteService;

    /**
     * 根据名称，模糊、分页查询会员列表
     *
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberManageResp> pageMembers(PageMemberByNameDataReq pageVO) {
        List<MemberRoleRuleDO> roleRuleDOList = roleRuleRepository.findAll();
        Specification<MemberDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (StringUtils.hasLength(pageVO.getMemberName())) {
                list.add(criteriaBuilder.like(root.get("name").as(String.class), "%" + pageVO.getMemberName().trim() + "%"));
            }
            //去除已经配置了角色规则的会员
            if (!CollectionUtils.isEmpty(roleRuleDOList)) {
                list.add(criteriaBuilder.not(criteriaBuilder.in(root.get("id")).value(roleRuleDOList.stream().map(MemberRoleRuleDO::getMemberId).collect(Collectors.toList()))));
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Page<MemberDO> pageList = memberRepository.findAll(specification, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberDO -> {
            MemberManageResp memberManageResp = new MemberManageResp();
            memberManageResp.setMemberId(memberDO.getId());
            memberManageResp.setMemberName(memberDO.getName());
            return memberManageResp;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据名称，模糊、分页查询会员管理列表
     *
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberRoleRuleResp> pageRuleMembers(PageMemberByNameDataReq pageVO) {
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRoleRuleDO> pageList;
        if (StringUtils.hasLength(pageVO.getMemberName())) {
            pageList = roleRuleRepository.findAllByMemberNameLike("%" + pageVO.getMemberName().trim() + "%", page);
        } else {
            pageList = roleRuleRepository.findAll(page);
        }

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberRoleRuleDO -> {
            MemberRoleRuleResp memberRoleRuleResp = new MemberRoleRuleResp();
            memberRoleRuleResp.setRoleRuleId(memberRoleRuleDO.getId());
            memberRoleRuleResp.setMemberId(memberRoleRuleDO.getMemberId());
            memberRoleRuleResp.setMemberName(memberRoleRuleDO.getMemberName());
            return memberRoleRuleResp;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据名称，模糊、分页查询角色详情列表
     *
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<RoleRuleManageResp> pageRoles(PageRoleRuleDataReq pageVO) {
        Specification<MemberRoleDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //去掉平台角色
            list.add(criteriaBuilder.notEqual(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            //会员状态为启用
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
            //按名字做模糊查询
            if (StringUtils.hasLength(pageVO.getRoleName())) {
                list.add(criteriaBuilder.like(root.get("roleName").as(String.class), "%" + pageVO.getRoleName().trim() + "%"));
            }
            if (!CollectionUtils.isEmpty(pageVO.getCheckRoleIdList())) {
                list.add(criteriaBuilder.not(criteriaBuilder.in(root.get("id")).value(pageVO.getCheckRoleIdList())));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRoleDO> pageList = roleRepository.findAll(specification, page);

        return new PageDataResp<>(pageList.getTotalElements(), getRoleManageVOList(new HashSet<>(pageList.getContent())));
    }

    /**
     * 查询当前会员适用角色详情列表
     *
     * @param id 接口参数
     * @return 查询结果
     */
    @Override
    public List<RoleRuleManageResp> memberRoles(Long id) {
        MemberRoleRuleDO roleRuleDO = roleRuleRepository.findByMemberId(id);
        if (roleRuleDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_NO_SUITABLE_ROLE);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NO_SUITABLE_ROLE);
        }

        return getRoleManageVOList(roleRuleDO.getMemberRoleRule());
    }

    /**
     * 查询下级会员适用角色详情列表
     *
     * @param id 接口参数
     * @return 查询结果
     */
    @Override
    public List<RoleRuleManageResp> subMemberRoles(Long id) {
        MemberRoleRuleDO roleRuleDO = roleRuleRepository.findByMemberId(id);
        if (roleRuleDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_SUB_MEMBER_NO_SUITABLE_ROLE);
            throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_NO_SUITABLE_ROLE);
        }
        return getRoleManageVOList(roleRuleDO.getSubMemberRoleRule());
    }

    /**
     * 查询下级会员适用角色详情列表(注册)
     *
     * @param id 接口参数
     * @return 查询结果
     */
    @Override
    public List<RoleRuleRegisterResp> subMemberRolesRegister(Long id) {
        MemberRoleRuleDO roleRuleDO = roleRuleRepository.findByMemberId(id);
        if (roleRuleDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_NO_SUITABLE_ROLE);
        }
        return getRoleRuleRegisterVOList(roleRuleDO.getSubMemberRoleRule());
    }

    /**
     * 选择会员
     *
     * @param headers    Http头部信息
     * @param memberIdReq 接口参数
     */
    @Override
    public MemberSelectResp selectMember(HttpHeaders headers, MemberIdReq memberIdReq) {
        //判断是否开启SAAS多租户部署
        siteService.isEnableMultiTenancy(headers);

        MemberDO memberDO = memberRepository.findById(memberIdReq.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberSelectResp memberSelectResp = new MemberSelectResp();
        memberSelectResp.setMemberId(memberDO.getId());
        memberSelectResp.setMemberName(memberDO.getName());
        memberSelectResp.setRoleList(getRoleManageVOList(memberDO.getMemberRoles()));

        return memberSelectResp;
    }

    /**
     * 新增会员适用角色
     *
     * @param headers    Http头部信息
     * @param roleRuleVO 接口参数
     */
    @Override
    public void addRuleRoles(HttpHeaders headers, AddRoleRuleReq roleRuleVO) {
        //判断是否开启SAAS多租户部署
        siteService.isEnableMultiTenancy(headers);

        //查找会员
        MemberDO memberDO = memberRepository.findById(roleRuleVO.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        //先查出数据库对应的记录，如果有就用原来的，如果没有就创建一个
        MemberRoleRuleDO roleRuleDO = roleRuleRepository.findByMemberId(roleRuleVO.getMemberId());
        if (roleRuleDO == null) {
            roleRuleDO = new MemberRoleRuleDO();
            roleRuleDO.setMemberId(memberDO.getId());
            roleRuleDO.setMemberName(memberDO.getName());
        }

        //因为新增和删除都用这个接口，所以这边都是直接根据前端的入参，将数据存入数据库即可
        if (!CollectionUtils.isEmpty(roleRuleVO.getMemberRoleIdList())) {
            roleRuleDO.setMemberRoleRule(roleRuleVO.getMemberRoleIdList().stream().map(MemberRoleDO::new).collect(Collectors.toSet()));
        }
        if (!CollectionUtils.isEmpty(roleRuleVO.getSubMemberRoleIdList())) {
            roleRuleDO.setSubMemberRoleRule(roleRuleVO.getSubMemberRoleIdList().stream().map(MemberRoleDO::new).collect(Collectors.toSet()));
        }

        roleRuleRepository.saveAndFlush(roleRuleDO);
        //return WrapperUtil.success();
    }

    /**
     * 删除会员适用角色
     *
     * @param headers    Http头部信息
     * @param roleRuleVO 接口参数
     */
    @Override
    public void delRuleRoles(HttpHeaders headers, DelRoleRuleReq roleRuleVO) {
        //判断是否开启SAAS多租户部署
        siteService.isEnableMultiTenancy(headers);

        MemberRoleRuleDO roleRuleDO = roleRuleRepository.findByMemberId(roleRuleVO.getMemberId());
        if (roleRuleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        if (!CollectionUtils.isEmpty(roleRuleVO.getMemberRoleIdList())) {
            roleRuleDO.getMemberRoleRule().removeAll(roleRuleVO.getMemberRoleIdList().stream().map(MemberRoleDO::new).collect(Collectors.toSet()));
        }
        if (!CollectionUtils.isEmpty(roleRuleVO.getSubMemberRoleIdList())) {
            roleRuleDO.getSubMemberRoleRule().removeAll(roleRuleVO.getSubMemberRoleIdList().stream().map(MemberRoleDO::new).collect(Collectors.toSet()));
        }

        roleRuleRepository.saveAndFlush(roleRuleDO);
        //return WrapperUtil.success();
    }

    /**
     * 会员适用角色详情列表
     *
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberRoleRuleDetailResp getDetails(MemberDetailByIdReq idVO) {
        Long memberId = idVO.getMemberId();
        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        List<RoleRuleManageResp> memberRoleList = memberRoles(memberId);
        List<RoleRuleManageResp> subMemberRoleList = subMemberRoles(memberId);

        MemberRoleRuleDetailResp detailVO = new MemberRoleRuleDetailResp();
        detailVO.setMemberId(memberId);
        detailVO.setMemberName(memberDO.getName());
        detailVO.setMemberRoleList(memberRoleList);
        detailVO.setSubMemberRoleList(subMemberRoleList);

        return detailVO;
    }

    /**
     * 数据提取转换
     *
     * @param roleRule 角色集合
     * @return 查询结果
     */
    private List<RoleRuleManageResp> getRoleManageVOList(Set<MemberRoleDO> roleRule) {
        return roleRule.stream().filter(role -> !MemberRelationTypeEnum.PLATFORM.getCode().equals(role.getRelType()))
                .map(role -> {
                    RoleRuleManageResp roleManageVO = new RoleRuleManageResp();
                    roleManageVO.setRoleId(role.getId());
                    roleManageVO.setRoleName(role.getRoleName());
                    roleManageVO.setRoleTypeEnum(role.getRoleType());
                    roleManageVO.setRoleTypeName(RoleTypeEnum.getName(role.getRoleType()));
                    roleManageVO.setMemberType(role.getMemberType());
                    roleManageVO.setMemberTypeName(MemberTypeEnum.getName(role.getMemberType()));
                    roleManageVO.setRoleTag(role.getRoleTag() == null ? 0 : role.getRoleTag());
                    roleManageVO.setRoleTagName(role.getRoleTag() == null ? "" : RoleTagEnum.getName(role.getRoleTag()));
                    return roleManageVO;
                }).collect(Collectors.toList());
    }

    /**
     * 数据提取转换
     * 先按memberTypeId聚类，聚类后结果为 TreeMap<RoleRuleRegisterVO,List<RoleIdAndRoleNameVO>>
     * 然后再将该TreeMap中的value设置进RoleRuleRegisterVO的roleInfoList属性中
     *
     * @param roleRule 角色集合
     * @return 查询结果
     */
    private List<RoleRuleRegisterResp> getRoleRuleRegisterVOList(Set<MemberRoleDO> roleRule) {
        return roleRule.stream().filter(role -> !MemberRelationTypeEnum.PLATFORM.getCode().equals(role.getRelType()))
                .collect(Collectors.groupingBy(memberRoleDO -> {
                    RoleRuleRegisterResp roleRuleRegisterResp = new RoleRuleRegisterResp();
                    roleRuleRegisterResp.setMemberType(memberRoleDO.getMemberType());
                    roleRuleRegisterResp.setMemberTypeName(MemberTypeEnum.getName(memberRoleDO.getRoleType()));
                    return roleRuleRegisterResp;
                }, TreeMap::new, Collectors.mapping(memberRoleDO -> {
                    RoleIdAndRoleNameResp roleInfoList = new RoleIdAndRoleNameResp();
                    roleInfoList.setRoleId(memberRoleDO.getId());
                    roleInfoList.setRoleName(memberRoleDO.getRoleName());
                    return roleInfoList;
                }, Collectors.toList()))).entrySet().stream().map(entry -> entry.getKey().quickSetMemberBusinessList(entry.getValue())).collect(Collectors.toList());
    }
}