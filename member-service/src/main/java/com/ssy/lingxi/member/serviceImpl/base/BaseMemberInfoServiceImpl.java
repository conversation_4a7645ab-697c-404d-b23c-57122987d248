package com.ssy.lingxi.member.serviceImpl.base;

import cn.hutool.core.collection.CollectionUtil;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.DataSourceEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.dto.AreaDTO;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.manage.api.feign.IAreaFeign;
import com.ssy.lingxi.manage.api.model.resp.area.AreaCodeResp;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleStagesRuleCheckFeignReq;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.constant.MemberRegisterDetailConfigConstant;
import com.ssy.lingxi.member.entity.bo.LevelBO;
import com.ssy.lingxi.member.entity.bo.ProcessBO;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.detail.MemberClassificationDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifecycleStagesDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.dto.CommentSummaryDTO;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.branch.MemberBranchSaveOrUpdateReq;
import com.ssy.lingxi.member.model.req.info.MemberInfoAddRoleReq;
import com.ssy.lingxi.member.model.req.info.MemberInfoUpdateDepositDetailReq;
import com.ssy.lingxi.member.model.req.info.MemberInfoUpdateRegisterDetailReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberDetailCreditHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.mobile.MobileUpdateDepositDetailReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.SubMemberDetailResp;
import com.ssy.lingxi.member.model.resp.basic.UpperMemberShowResp;
import com.ssy.lingxi.member.model.resp.info.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordRectifyResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.mobile.MobileInfoApplyButtonResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileUpdateDepositDetailQueryResp;
import com.ssy.lingxi.member.model.resp.platform.MemberManageResp;
import com.ssy.lingxi.member.model.resp.platform.RoleRuleManageResp;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;
import com.ssy.lingxi.member.model.resp.validate.WorkFlowStepResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.repository.comment.MemberTradeCommentHistoryRepository;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.IMemberLifeCycleStageRuleFeignService;
import com.ssy.lingxi.member.service.web.IMemberAppraisalService;
import com.ssy.lingxi.member.service.web.IMemberBranchService;
import com.ssy.lingxi.member.service.web.IMemberComplaintService;
import com.ssy.lingxi.member.service.web.IMemberRectifyService;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员信息管理查询服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-05
 */
@Service
public class BaseMemberInfoServiceImpl implements IBaseMemberInfoService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberDetailService baseMemberDetailService;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private IBasePlatformProcessService basePlatformProcessService;

    @Resource
    private MemberTradeCommentHistoryRepository memberTradeCommentHistoryRepository;

    @Resource
    private MemberAfterSaleHistoryRepository memberAfterSaleHistoryRepository;

    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private IBaseMemberInnerService memberInnerService;

    @Resource
    private IBaseMemberQualificationService baseMemberQualificationService;

    @Resource
    private IMemberAppraisalService memberAppraisalService;

    @Resource
    private IMemberRectifyService memberRectifyService;

    @Resource
    private IMemberComplaintService memberComplaintService;

    @Resource
    private RoleRuleRepository roleRuleRepository;

    @Resource
    private MemberLevelConfigRepository memberLevelConfigRepository;

    @Resource
    private IMemberLifeCycleStageRuleFeignService memberLifeCycleStageRuleFeignService;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    @Resource
    private CorporationRepository corporationRepository;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private IMemberBranchService memberBranchService;

    @Resource
    private IAreaFeign areaFeign;

    /**
     * 获取分页查询页面下拉框内容
     * @return 下拉框内容
     */
    @Override
    public MemberInfoSearchConditionResp getPageSearchConditions() {
        MemberInfoSearchConditionResp conditionVO = new MemberInfoSearchConditionResp();
        List<DropdownItemResp> itemList = MemberOuterStatusEnum.toDropdownList();
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setOuterStatus(itemList);
        return conditionVO;
    }

    /**
     * 分页、模糊查询归属会员列表（带积分权益）
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param current     当前页
     * @param pageSize    每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileInfoLevelRightResp> pageUpperMemberLevelRights(Long subMemberId, Long subRoleId, int current, int pageSize) {
        Pageable pageable = PageRequest.of(current - 1, pageSize, Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findBySubMemberIdAndSubRoleId(subMemberId, subRoleId, pageable);

        //注意！！！这里返回的是下级会员的memberId，所以都是一样的
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MobileInfoLevelRightResp infoVO = new MobileInfoLevelRightResp();
            infoVO.setValidateId(relationDO.getId());
            infoVO.setUpperMemberId(relationDO.getMemberId());
            infoVO.setUpperRoleId(relationDO.getRoleId());
            infoVO.setLogo(relationDO.getMember().getLogo());
            infoVO.setUpperMemberName(relationDO.getMember().getName());
            infoVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
            infoVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            infoVO.setScore(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getScore());
            LevelBO levelBO = baseMemberLevelConfigService.findNextLevel(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleId(), relationDO.getLevelRight().getLevel());
            infoVO.setNextLevelTag(levelBO.getNextTag());
            infoVO.setNextScore(levelBO.getNextScore());
            infoVO.setSumReturnMoney(relationDO.getLevelRight() == null ? BigDecimal.ZERO : relationDO.getLevelRight().getSumReturnMoney());
            infoVO.setCurrentPoint(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getCurrentPoint());
            infoVO.setStatus(relationDO.getStatus());
            infoVO.setStatusName(MemberStatusEnum.getCodeMessage(infoVO.getStatus()));

            return infoVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 分页、模糊查询归属会员列表
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param name 上级会员名称
     * @param startDate 申请起始日期，格式为yyyy-MM-dd
     * @param endDate   申请结束日期，格式为yyyy-MM-dd
     * @param outerStatus 审核状态（外部）
     * @param current 当前页（从1开始）
     * @param pageSize 每页行数
     * @param enableMultiTenancy 是否开启SAAS多租户部署
     * @param roleTag 标签类型
     * @return 查询结果
     */
    @Override
    public PageDataResp<UpperMemberInfoResp> pageUpperMembers(Long subMemberId, Long subRoleId, String name, String startDate, String endDate, Integer outerStatus, int current, int pageSize, Boolean enableMultiTenancy, Long categoryId, Integer currencyType, String code, Map<String,Object> memberConfigs, Integer roleTag) {
        MemberDO subMemberDO = memberRepository.findById(subMemberId).orElse(null);
        if (subMemberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        Boolean platformLevelConfig = memberLevelConfigRepository.existsByLevelTypeAndSubRoleId(MemberLevelTypeEnum.PLATFORM.getCode(), subRoleId);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), subMemberId));

            if (!platformLevelConfig) {
                list.add(criteriaBuilder.notEqual(root.get("subMemberLevelTypeEnum").as(Integer.class), MemberLevelTypeEnum.PLATFORM.getCode()));
            }

            if(NumberUtil.notNullOrZero(subRoleId)) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), subRoleId));
            }

            if (StringUtils.hasLength(startDate)) {
                LocalDateTime startDateTime = LocalDateTime.parse(startDate.concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), startDateTime));
            }

            if (StringUtils.hasLength(endDate)) {
                LocalDateTime endDateTime = LocalDateTime.parse(endDate.concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), endDateTime));
            }

            if (NumberUtil.notNullOrZero(outerStatus)) {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), outerStatus));
            }

            //会员名称
            if (StringUtils.hasLength(name)) {
                Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
                list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + name.trim() + "%"));
            }

            //如果开启SAAS多租户部署 就隐藏 会员等级类型 为 平台会员 的会员数据
            if(enableMultiTenancy){
                list.add(criteriaBuilder.notEqual(root.get("subMemberLevelTypeEnum").as(Integer.class), MemberLevelTypeEnum.PLATFORM.getCode()));
            }

            //币别
            if(NumberUtil.notNullOrZero(currencyType)) {
                list.add(criteriaBuilder.equal(root.get("classification").get("currencyType").as(Integer.class),currencyType));
            }

            //会员编码
            if(StringUtils.hasLength(code)) {
                list.add(criteriaBuilder.like(root.get("classification").get("code").as(String.class),"%" + code.trim() + "%"));
            }

            //主营品类---待优化
            if(NumberUtil.notNullOrZero(categoryId)) {
                Join<Object, Object> classificationJoin = root.join("classification", JoinType.LEFT);
                Join<Object, Object> categoriesJoin = classificationJoin.join("categories", JoinType.LEFT);
                list.add(criteriaBuilder.like(categoriesJoin.get("categories").as(String.class), "%" +"\"id\": "+ categoryId + "%"));
            }


            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("id").ascending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            UpperMemberInfoResp infoVO = new UpperMemberInfoResp();
            infoVO.setMemberId(relationDO.getMemberId());
            infoVO.setRoleId(relationDO.getRoleId());
            infoVO.setValidateId(relationDO.getId());
            infoVO.setName(relationDO.getMember().getName());
            infoVO.setLogo(StringUtils.hasLength(relationDO.getMember().getLogo()) ? relationDO.getMember().getLogo() : "");
            infoVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getRole().getMemberType()));
            infoVO.setRoleName(relationDO.getRole().getRoleName());
            infoVO.setSubMemberId(relationDO.getSubMemberId());
            infoVO.setSubMemberName(relationDO.getSubMember().getName());
            infoVO.setSubRoleId(relationDO.getSubRoleId());
            infoVO.setSubRoleName(relationDO.getSubRole().getRoleName());
            infoVO.setCustomerType(relationDO.getSubMemberTypeEnum() == null ? MemberTypeEnum.MERCHANT.getCode() : relationDO.getSubMemberTypeEnum());
            infoVO.setLevelTypeEnum(relationDO.getSubMemberLevelTypeEnum());
            infoVO.setLevelTypeName(MemberLevelTypeEnum.getCodeMsg(relationDO.getSubMemberLevelTypeEnum()));
            infoVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
            infoVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            infoVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            infoVO.setDepositTime(relationDO.getDepositTime() == null ? "" : relationDO.getDepositTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            infoVO.setStatus(relationDO.getStatus());
            infoVO.setStatusName(MemberStatusEnum.getCodeMessage(infoVO.getStatus()));
            infoVO.setOuterStatus(relationDO.getOuterStatus());
            infoVO.setCode(Optional.ofNullable(relationDO.getClassification()).map(MemberClassificationDO::getCode).orElse(""));
            infoVO.setLifecycleStagesName(Optional.ofNullable(relationDO.getMemberLifecycleStages()).map(MemberLifecycleStagesDO::getLifecycleStagesName).orElse(""));
            infoVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
            infoVO.setShowUpdate(false);
            infoVO.setShowModify(false);
            if(relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
                //平台审核通过或不通过，显示“修改”，不显示“变更”
                if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode()) || relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode())) {
                    infoVO.setShowUpdate(true);
                    infoVO.setShowModify(false);
                } else {
                    if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED.getCode())) {
                        infoVO.setShowUpdate(true);
                        infoVO.setShowModify(false);
                    } else if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode()) || relationDO.getOuterStatus().equals(MemberOuterStatusEnum.MODIFY_PASSED.getCode()) || relationDO.getOuterStatus().equals(MemberOuterStatusEnum.MODIFY_NOT_PASSED.getCode())){
                        infoVO.setShowUpdate(false);

                        Boolean showModify = true;
                        if (NumberUtil.notNullOrZero(roleTag)) {
                            MemberLifeCycleStagesRuleCheckFeignReq checkVO = new MemberLifeCycleStagesRuleCheckFeignReq();
                            checkVO.setMemberId(relationDO.getMemberId());
                            checkVO.setRoleId(relationDO.getRoleId());
                            checkVO.setSubMemberId(relationDO.getSubMemberId());
                            checkVO.setSubRoleId(relationDO.getSubRoleId());
                            checkVO.setLifeCycleStageRuleId(RoleTagEnum.CUSTOMER.getCode().equals(roleTag) ? MemberLifecycleStagesRuleEnum.CUSTOMER_CHANGE_OF_APPLICATION_INFORMATION_IS_ALLOWED.getCode() : MemberLifecycleStagesRuleEnum.SUPPLIER_ALLOWS_CHANGES_TO_INCOMING_DATA.getCode());
//                            checkVO.setSubRoleTag(roleTag);
                            showModify = memberLifeCycleStageRuleFeignService.checkMemberLifeCycleStagesRule(checkVO);
                        }
                        infoVO.setShowModify(showModify);
                    }
                }
            }

            return infoVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “申请会员”页面，查询申请按钮状态和文本
     *
     * @param loginUser     登录用户
     * @param shopType       商城类型
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级角色Id
     * @return 查询结果
     */
    @Override
    public MemberInfoApplyButtonResp getApplyCondition(UserLoginCacheDTO loginUser, Integer shopType, Long upperMemberId, Long upperRoleId) {
        if(loginUser.getMemberId().equals(upperMemberId)) {
            return new MemberInfoApplyButtonResp(MemberApplyButtonStatusEnum.DO_NOT_SHOW, ShopTypeEnum.parseCode(shopType), 0L);
        }

        MemberRelationDO upperRelation = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(upperMemberId, upperRoleId, MemberRelationTypeEnum.PLATFORM.getCode());
        Long validateId = 0L;
        MemberApplyButtonStatusEnum statusEnum = MemberApplyButtonStatusEnum.CAN_NOT_APPLY;

        // 上级会员平台审核没有通过，不能申请
        if(upperRelation == null || upperRelation.getVerified().equals(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode()) || !upperRelation.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            return new MemberInfoApplyButtonResp(statusEnum, ShopTypeEnum.parseCode(shopType), validateId);
        }

        // 等级类型不一致，不能申请
        Integer upperMemberLevelType = MemberLevelTypeEnum.MERCHANT.getCode();
        if(!loginUser.getMemberLevelType().equals(upperMemberLevelType)) {
            return new MemberInfoApplyButtonResp(statusEnum, ShopTypeEnum.parseCode(shopType), validateId);
        }

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperMemberId, upperRoleId, loginUser.getMemberId(), loginUser.getMemberRoleId());
        if(relationDO == null) {
            if(shopType.equals(ShopTypeEnum.PURCHASE.getCode())) {
                //如果是采购门户，需要判断申请的会员是否商户会员且为服务提供者
                if(loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode()) && loginUser.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
                    statusEnum = MemberApplyButtonStatusEnum.PROCEED;
                } else {
                    statusEnum = MemberApplyButtonStatusEnum.CAN_NOT_APPLY;
                }
            } else {
                statusEnum = MemberApplyButtonStatusEnum.PROCEED;
            }
        } else {
            if(relationDO.getStatus().equals(MemberStatusEnum.BLACK_LIST.getCode())) {
                statusEnum = MemberApplyButtonStatusEnum.BLACKLIST;
            } else if(relationDO.getStatus().equals(MemberStatusEnum.ELIMINATED.getCode())) {
                statusEnum = MemberApplyButtonStatusEnum.ELIMINATED;
            } else if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITING.getCode())){
                validateId = relationDO.getId();
                statusEnum = MemberApplyButtonStatusEnum.DEPOSITING;
            } else if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode())) {
                validateId = relationDO.getId();
                statusEnum = MemberApplyButtonStatusEnum.DEPOSIT_PASSED;
            } else {
                statusEnum = MemberApplyButtonStatusEnum.CAN_NOT_APPLY;
            }
        }

        return new MemberInfoApplyButtonResp(statusEnum, ShopTypeEnum.parseCode(shopType), validateId);
    }

    /**
     * App - “申请会员”页面，查询申请按钮状态和文本
     *
     * @param loginUser     登录用户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级角色Id
     * @return 查询结果
     */
    @Override
    public MobileInfoApplyButtonResp getMobileApplyCondition(UserLoginCacheDTO loginUser, Long upperMemberId, Long upperRoleId) {
        if(loginUser.getMemberId().equals(upperMemberId)) {
            return new MobileInfoApplyButtonResp(MobileApplyButtonStatusEnum.CAN_NOT_BE_SELF_SUB_MEMBER);
        }

        MemberRelationDO upperRelation = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(upperMemberId, upperRoleId, MemberRelationTypeEnum.PLATFORM.getCode());
        MobileApplyButtonStatusEnum statusEnum = MobileApplyButtonStatusEnum.DO_NOT_SHOW;

        // 上级会员平台审核没有通过，不能申请
        if(upperRelation == null || upperRelation.getVerified().equals(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode()) || !upperRelation.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            return new MobileInfoApplyButtonResp(statusEnum);
        }

        // 等级类型不一致，不能申请
        Integer upperMemberLevelType = MemberLevelTypeEnum.MERCHANT.getCode();
        if(!loginUser.getMemberLevelType().equals(upperMemberLevelType)) {
            return new MobileInfoApplyButtonResp(statusEnum);
        }

        String validateMsg = "";
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperMemberId, upperRoleId, loginUser.getMemberId(), loginUser.getMemberRoleId());
        if(relationDO == null) {
            statusEnum = MobileApplyButtonStatusEnum.PROCEED;
        } else {
            if(relationDO.getStatus().equals(MemberStatusEnum.BLACK_LIST.getCode())) {
                statusEnum = MobileApplyButtonStatusEnum.BLACKLIST;
            } else if(relationDO.getStatus().equals(MemberStatusEnum.ELIMINATED.getCode())) {
                statusEnum = MobileApplyButtonStatusEnum.ELIMINATED;
            } else if(relationDO.getStatus().equals(MemberStatusEnum.FROZEN.getCode())) {
                statusEnum = MobileApplyButtonStatusEnum.FROZEN;
            } else if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITING.getCode())){
                statusEnum = MobileApplyButtonStatusEnum.DEPOSITING;
            } else if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode())) {
                statusEnum = MobileApplyButtonStatusEnum.DEPOSIT_PASSED;
            } else if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED.getCode())) {
                validateMsg = relationDO.getValidateMsg();
                statusEnum = MobileApplyButtonStatusEnum.DEPOSIT_NOT_PASS;
            } else {
                statusEnum = MobileApplyButtonStatusEnum.DO_NOT_SHOW;
            }
        }

        return new MobileInfoApplyButtonResp(statusEnum, validateMsg);
    }

    /**
     * “邀请会员”页面，查询邀请按钮状态和文本
     *
     * @param loginUser     登录用户
     * @param shopType      商城类型
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级角色Id
     * @return 查询结果
     */
    @Override
    public MemberInfoInviteButtonResp getInviteCondition(UserLoginCacheDTO loginUser, Integer shopType, Long subMemberId, Long subRoleId) {
        if(loginUser.getMemberId().equals(subMemberId)) {
            return new MemberInfoInviteButtonResp(MemberInviteButtonStatusEnum.DO_NOT_SHOW, 0L);
        }

        MemberRelationDO subRelation = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(subMemberId, subRoleId, MemberRelationTypeEnum.PLATFORM.getCode());
        Long validateId = 0L;
        MemberInviteButtonStatusEnum statusEnum = MemberInviteButtonStatusEnum.CAN_NOT_INVITE;

        // 下级会员平台审核没有通过，不能邀请
        if(subRelation == null || subRelation.getVerified().equals(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode()) || !subRelation.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            return new MemberInfoInviteButtonResp(statusEnum, validateId);
        }

        // 等级类型不一致，不能申请
        Integer subMemberLevelType = MemberLevelTypeEnum.MERCHANT.getCode();
        if(!loginUser.getMemberLevelType().equals(subMemberLevelType)) {
            return new MemberInfoInviteButtonResp(statusEnum, validateId);
        }

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), subMemberId, subRoleId);
        if(relationDO == null) {
            statusEnum = MemberInviteButtonStatusEnum.PROCEED;
        } else {
            if(relationDO.getStatus().equals(MemberStatusEnum.BLACK_LIST.getCode())) {
                statusEnum = MemberInviteButtonStatusEnum.BLACKLIST;
            } else if(relationDO.getStatus().equals(MemberStatusEnum.ELIMINATED.getCode())) {
                statusEnum = MemberInviteButtonStatusEnum.ELIMINATED;
            } else if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITING.getCode())){
                validateId = subRelation.getId();
                statusEnum = MemberInviteButtonStatusEnum.DEPOSITING;
            } else if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode())) {
                validateId = subRelation.getId();
                statusEnum = MemberInviteButtonStatusEnum.DEPOSIT_PASSED;
            } else {
                statusEnum = MemberInviteButtonStatusEnum.CAN_NOT_INVITE;
            }
        }

        return new MemberInfoInviteButtonResp(statusEnum, validateId);
    }

    /**
     * “申请会员”页面，会员注册资料信息
     *
     * @param loginUser     登录用户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级会员角色Id
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberInfoApplyRegisterDetailResp getApplyRegisterDetail(UserLoginCacheDTO loginUser, Long upperMemberId, Long upperRoleId, Integer roleTag) {
        MemberDO member = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(member == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO upperRole = memberRoleRepository.findById(upperRoleId).orElse(null);
        if(upperRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        MemberInfoApplyRegisterDetailResp detailVO = new MemberInfoApplyRegisterDetailResp();
        detailVO.setProcessName(MemberStringEnum.MEMBER_DEPOSIT_OUTER_PROCESS.getName());
        detailVO.setOuterVerifySteps(baseMemberValidateService.getMemberDepositOuterSteps(upperRole.getRoleName(), loginUser.getMemberRoleName(), roleTag));
        detailVO.setCurrentOuterStep(1);
        detailVO.setRegisterDetails(baseMemberRegisterDetailService.groupMemberRegisterDetailText(member));

        return detailVO;
    }

    /**
     * “申请会员”页面，会员入库资料信息
     *
     * @param loginUser     登录用户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级会员角色Id
     * @return 查询结果
     */
    @Override
    public MemberInfoApplyDepositDetailResp getApplyDepositDetail(UserLoginCacheDTO loginUser, Long upperMemberId, Long upperRoleId) {
        MemberDO member = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(member == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO upperMemberRoleDO = memberRoleRepository.findById(upperRoleId).orElse(null);
        if(upperMemberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(loginUser.getMemberRoleId()).orElse(null);
        if(memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        MemberInfoApplyDepositDetailResp detailVO = new MemberInfoApplyDepositDetailResp();
        detailVO.setProcessName(MemberStringEnum.MEMBER_CHANGE_OUTER_PROCESS.getName());
        detailVO.setOuterVerifySteps(Stream.of(new WorkFlowStepResp(1, MemberStringEnum.MEMBER_CHANGE_OUTER_STEP_ONE.getName(), memberRoleDO.getRoleName()), new WorkFlowStepResp(2, MemberStringEnum.MEMBER_CHANGE_OUTER_STEP_TWO.getName(), upperMemberRoleDO.getRoleName())).collect(Collectors.toList()));
        detailVO.setCurrentOuterStep(1);
        detailVO.setDepositDetails(baseMemberDepositDetailService.groupMemberConfig(baseMemberDepositDetailService.findMemberDepositoryConfig(upperMemberId, upperRoleId, memberRoleDO)));
        return detailVO;
    }

    /**
     * 获取“修改会员信息”页面，会员注册资料信息
     * @param loginUser 登录用户
     * @param validateId 会员关系Id
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberInfoUpdateDetailResp getMemberRegisterDetail(UserLoginCacheDTO loginUser, Long validateId, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //平台会员才可以修改资料后重新提交审核
        if (!relationDO.getSubMemberLevelTypeEnum().equals(MemberLevelTypeEnum.PLATFORM.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_PLATFORM_LEVEL_TYPE);
        }

        //审核通过或不通过，才能修改信息
        if (!relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode()) && !relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        MemberInfoUpdateDetailResp detailVO = new MemberInfoUpdateDetailResp();
        detailVO.setProcessName(MemberStringEnum.MEMBER_VALIDATE_PROCESS.getName());
        //审核流程
        detailVO.setOuterVerifySteps(baseMemberValidateService.getPlatformValidateOuterSteps(relationDO.getSubRoleName(), roleTag));
        //外部审核步骤
        detailVO.setCurrentOuterStep(1);

        //基本信息
        detailVO.setMemberId(relationDO.getSubMemberId());
        detailVO.setValidateId(relationDO.getId());

        //详细信息分组内容
        detailVO.setGroups(baseMemberRegisterDetailService.groupMemberRegisterDetail(relationDO.getSubMember(), relationDO.getSubRole(), MemberDetailVersionEnum.USING));

        //外部历史记录（即历史流转记录）
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        return detailVO;
    }

    /**
     * App - 获取“修改会员信息”页面，会员注册资料信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 操作结果
     */
    @Override
    public MobileInfoUpdateDetailResp getMobileMemberRegisterDetail(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //会员等级类型为平台会员的审核不通过状态可以修改资料后重新提交审核
        if (!relationDO.getSubMemberLevelTypeEnum().equals(MemberLevelTypeEnum.PLATFORM.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_PLATFORM_LEVEL_TYPE);
        }

        MobileInfoUpdateDetailResp detailVO = new MobileInfoUpdateDetailResp();
        detailVO.setStatus(relationDO.getOuterStatus());
        detailVO.setValidateMsg(StringUtils.hasLength(relationDO.getValidateMsg()) ? relationDO.getValidateMsg() : "");

        //详细信息分组内容
        detailVO.setGroups(baseMemberRegisterDetailService.groupMemberRegisterDetail(relationDO.getSubMember(), relationDO.getSubRole(), MemberDetailVersionEnum.USING));
        return detailVO;
    }

    /**
     * 平台会员，修改会员信息
     * @param loginUser 登录用户
     * @param detailVO 接口参数
     * @return 修改结果
     */
    @Transactional
    @Override
    public void updateMemberRegisterDetail(UserLoginCacheDTO loginUser, MemberInfoUpdateRegisterDetailReq detailVO) {
        //修改注册信息后，强制重新提交平台审核
        //由于注册信息是所有角色共用的，所以要判断所有角色的平台会员
        List<MemberRelationDO> relationList = relationRepository.findBySubMemberIdAndRelType(loginUser.getMemberId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if(CollectionUtils.isEmpty(relationList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberRelationDO relationDO = relationList.stream().filter(relation -> relation.getId().equals(detailVO.getValidateId())).findFirst().orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //审核进行中（状态不为“通过”、“未通过”），不能修改信息
        MemberRelationDO otherRoleRelation = relationList.stream().filter(relation -> !relation.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode()) && !relation.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode())).findFirst().orElse(null);
        if(otherRoleRelation != null) {
            if(otherRoleRelation.getId().equals(relationDO.getId())){
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
            }else {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_HAS_ANOTHER_ROLE_IS_VERIFYING);
            }
        }
        baseMemberRegisterDetailService.updatePlatformMemberRegisterDetail(relationDO, "", detailVO.getDetail(), false,true);

    }

    /**
     * 获取“修改会员信息”页面，会员入库资料信息
     *
     * @param loginUser  登录用户
     * @param validateId 会员关系Id
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberInfoUpdateDepositDetailResp getMemberDepositDetail(UserLoginCacheDTO loginUser, Long validateId, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //非平台会员才可以修改入库资料
        if (!relationDO.getSubMemberLevelTypeEnum().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_MERCHANT_OR_CHANNEL_LEVEL_TYPE);
        }

        List<Integer> outerStatusList = Stream.of(MemberOuterStatusEnum.DEPOSITORY_PASSED, MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED, MemberOuterStatusEnum.MODIFY_PASSED, MemberOuterStatusEnum.MODIFY_NOT_PASSED).map(MemberOuterStatusEnum::getCode).collect(Collectors.toList());
        if(!outerStatusList.contains(relationDO.getOuterStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        MemberInfoUpdateDepositDetailResp detailVO = new MemberInfoUpdateDepositDetailResp();
        detailVO.setProcessName(MemberStringEnum.MEMBER_CHANGE_PROCESS.getName());
        //审核流程
        detailVO.setOuterVerifySteps(baseMemberValidateService.getMemberModifyOuterSteps(relationDO));
        //外部审核步骤
        detailVO.setCurrentOuterStep(1);

        //基本信息
        detailVO.setMemberId(relationDO.getSubMemberId());
        detailVO.setValidateId(relationDO.getId());

        //详细信息分组内容
        detailVO.setGroups(baseMemberDepositDetailService.switchMemberDepositoryDetail(relationDO));

        //资质证明文件
        detailVO.setQualities(baseMemberQualificationService.findMemberQualities(relationDO));

        //外部历史记录（即历史流转记录）
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        return detailVO;
    }

    /**
     * App - 获取“修改会员信息”页面，会员入库资料信息
     *
     * @param loginUser     登录用户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级会员角色Id
     * @return 查询结果
     */
    @Override
    public MobileUpdateDepositDetailQueryResp getMemberDepositDetail(UserLoginCacheDTO loginUser, Long upperMemberId, Long upperRoleId) {
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperMemberId, upperRoleId, loginUser.getMemberId(), loginUser.getMemberRoleId());
        if (relationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //非平台会员才可以修改入库资料
        if (!relationDO.getSubMemberLevelTypeEnum().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_MERCHANT_OR_CHANNEL_LEVEL_TYPE);
        }

        List<Integer> outerStatusList = Stream.of(MemberOuterStatusEnum.DEPOSITORY_PASSED, MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED, MemberOuterStatusEnum.MODIFY_PASSED, MemberOuterStatusEnum.MODIFY_NOT_PASSED).map(MemberOuterStatusEnum::getCode).collect(Collectors.toList());
        if(!outerStatusList.contains(relationDO.getOuterStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        MobileUpdateDepositDetailQueryResp detailVO = new MobileUpdateDepositDetailQueryResp();
        //详细信息分组内容
        detailVO.setGroups(baseMemberDepositDetailService.switchMemberDepositoryDetail(relationDO));

        //资质证明文件
        detailVO.setQualities(baseMemberQualificationService.findMemberQualities(relationDO));
        return detailVO;
    }

    /**
     * App - 修改会员入库信息
     *
     * @param loginUser 登录用户
     * @param detailVO  接口参数
     * @param roleTag 角色标签
     * @return 修改结果
     */
    @Override
    public void updateMobileDepositDetail(UserLoginCacheDTO loginUser, MobileUpdateDepositDetailReq detailVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(detailVO.getUpperMemberId(), detailVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if (relationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //非平台会员才可以修改入库资料.
        if (!relationDO.getSubMemberLevelTypeEnum().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_MERCHANT_OR_CHANNEL_LEVEL_TYPE);
        }

        List<Integer> outerStatusList = Stream.of(MemberOuterStatusEnum.DEPOSITORY_PASSED, MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED, MemberOuterStatusEnum.MODIFY_PASSED, MemberOuterStatusEnum.MODIFY_NOT_PASSED).map(MemberOuterStatusEnum::getCode).collect(Collectors.toList());
        if(!outerStatusList.contains(relationDO.getOuterStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        //先保存资质文件
        baseMemberQualificationService.checkAndSaveMemberQualities(relationDO, detailVO.getQualities());
        baseMemberDepositDetailService.checkAndUpdateMemberDepositoryDetail(relationDO, detailVO.getDepositDetails(), roleTag);

    }

    /**
     * 修改会员入库信息
     *
     * @param loginUser 登录用户
     * @param detailVO  接口参数
     * @param roleTag 角色标签
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateMemberDepositDetail(UserLoginCacheDTO loginUser, MemberInfoUpdateDepositDetailReq detailVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(detailVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //非平台会员才可以修改入库资料.
        if (!relationDO.getSubMemberLevelTypeEnum().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_MERCHANT_OR_CHANNEL_LEVEL_TYPE);
        }

        List<Integer> outerStatusList = Stream.of(MemberOuterStatusEnum.DEPOSITORY_PASSED, MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED, MemberOuterStatusEnum.MODIFY_PASSED, MemberOuterStatusEnum.MODIFY_NOT_PASSED).map(MemberOuterStatusEnum::getCode).collect(Collectors.toList());
        if(!outerStatusList.contains(relationDO.getOuterStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        // 优时客户不允许在商城修改
        if (!Objects.equals(relationDO.getSubMember().getDataSource(), DataSourceEnum.MALL.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_YS_CUSTOMER_ALLOW_UPDATE_AT_MALL);
        }

        // 企业客户名称不能重复
        if (Objects.equals(relationDO.getSubRoleId(), baiTaiMemberProperties.getCustomerRoleId()) && Objects.nonNull(relationDO.getSubMember().getCorporationId())) {
            boolean existFlag = corporationRepository.existsByNameAndIdNot((String) detailVO.getDetail().get(MemberRegisterDetailConfigConstant.CORPORATION_NAME), relationDO.getSubMember().getCorporationId());
            if (existFlag) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_EXIST);
            }
        }

        //先保存资质文件
        baseMemberQualificationService.checkAndSaveMemberQualities(relationDO, detailVO.getQualities());
        baseMemberDepositDetailService.checkAndUpdateMemberDepositoryDetail(relationDO, detailVO.getDetail(), roleTag);
    }

    /**
     * 会员详情 - 会员基本信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberInfoBasicDetailResp getMemberBasicDetail(UserLoginCacheDTO loginUser, Long validateId, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

//        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
//            return Wrapper.fail(ResponseCode.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
//        }

        SubMemberDetailResp subMemberDetailResp = baseMemberDetailService.getSubMemberDetail(relationDO, roleTag);

        MemberInfoBasicDetailResp detailVO = new MemberInfoBasicDetailResp();
        BeanUtils.copyProperties(subMemberDetailResp, detailVO);

        if(relationDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
            detailVO.setProcessName(MemberStringEnum.MEMBER_VALIDATE_PROCESS.getName());
            detailVO.setOuterVerifySteps(baseMemberValidateService.getPlatformValidateOuterSteps(loginUser.getMemberRoleName(), roleTag));
        } else {
            detailVO.setProcessName(MemberStringEnum.MEMBER_DEPOSIT_PROCESS.getName());
            detailVO.setOuterVerifySteps(baseMemberValidateService.getMemberDepositOuterSteps(relationDO, roleTag));
        }

        detailVO.setCurrentOuterStep(relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode()) ? 2 : 1);

        //详细信息分组内容
        detailVO.setGroups(baseMemberRegisterDetailService.groupMemberRegisterDetailText(relationDO.getSubMember()));

        //外部历史记录（即历史流转记录）
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        return detailVO;
    }

    /**
     * App - 会员基本信息
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public MobileInfoBasicDetailResp getMobileMemberBasicDetail(MemberRelationDO relationDO) {
        MemberDO subMember = relationDO.getSubMember();
        if (subMember == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        //查询所有平台关系，用于判断是否可以显示“修改注册资料”按钮
        List<MemberRelationDO> relationList = relationRepository.findBySubMemberIdAndRelType(relationDO.getSubMemberId(), MemberRelationTypeEnum.PLATFORM.getCode());

        MobileInfoBasicDetailResp detailVO = new MobileInfoBasicDetailResp();
        //是否可以修改注册资料（如果有其中一个正在平台审核，则不能修改)
        detailVO.setShowModify(relationList.stream().allMatch(r -> r.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode()) || r.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode())));
        //当前会员信息
        detailVO.setLogo(relationDO.getSubMember().getLogo());
        detailVO.setName(relationDO.getSubMember().getName());
        //会员基本信息
        detailVO.setOuterStatus(relationDO.getOuterStatus());
        detailVO.setOuterStatusName(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));
        detailVO.setAccount(relationDO.getSubMember().getAccount());
        detailVO.setPhone(relationDO.getSubMember().getPhone());
        detailVO.setEmail(relationDO.getSubMember().getEmail());
        detailVO.setCreateTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
        Integer memberLevelTypeEnum = relationDO.getSubMemberLevelTypeEnum();
        detailVO.setMemberLevelTypeEnum(memberLevelTypeEnum);

        //详细信息分组内容
        detailVO.setGroups(baseMemberRegisterDetailService.groupMemberRegisterDetail(subMember, relationDO.getSubRole(), MemberDetailVersionEnum.USING));

        return detailVO;
    }

    /**
     * 会员详情 - 会员档案信息（入库信息）
     *
     * @param loginUser  登录用户
     * @param validateId 会员关系Id
     * @return 查询结果
     */
    @Override
    public MemberInfoDepositDetailResp getMemberArchives(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberInfoDepositDetailResp detailVO = new MemberInfoDepositDetailResp();
        detailVO.setDepositDetails(baseMemberDepositDetailService.switchMemberDepositoryDetailText(relationDO));
        detailVO.setQualities(baseMemberQualificationService.findMemberQualities(relationDO));
        return detailVO;
    }

    /**
     * 会员详情- 会员档案 - 分页查询考评信息
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberAppraisalPageQueryResp> pageMemberAppraisal(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO) {
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }
        return memberAppraisalService.pageMemberAppraisal(relationDO.getMember(), relationDO.getRole(), relationDO.getSubMember(), relationDO.getSubRole(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员档案 - 分页查询整改信息
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberRecordRectifyResp> pageMemberRecordRectify(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO) {
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return memberRectifyService.pageMemberRecordRectify(relationDO.getMember(), relationDO.getRole(), relationDO.getSubMember(), relationDO.getSubRole(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员等级信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    @Override
    public MemberValidateDetailLevelResp getMemberDetailLevel(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailLevel(relationDO);
    }

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO, DateTimeFormatter formatter) {
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberLevelDetailHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize(), formatter);
    }

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     *
     * @param relationDO 会员关系
     * @param current    当前页
     * @param pageSize   每页行数
     * @param formatter  日期时间格式
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(MemberRelationDO relationDO, int current, int pageSize, DateTimeFormatter formatter) {
        return baseMemberDetailService.pageMemberLevelDetailHistory(relationDO, current, pageSize, formatter);
    }

    /**
     * 会员详情 - 会员权益信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailRightResp getMemberDetailRight(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailRight(relationDO);
    }

    /**
     * App - 会员信用 - 交易评价汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    @Override
    public MobileCommentSummaryResp getMobileMemberDetailTradeCommentSummary(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MobileCommentSummaryResp summaryVO = new MobileCommentSummaryResp();

        CommentSummaryDTO summaryDTO = memberTradeCommentHistoryRepository.groupByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        if(summaryDTO != null && summaryDTO.getTotal() > 0) {
            int goodPercent = Math.round((float) summaryDTO.getGood() / summaryDTO.getTotal() * 100);
            int mediumPercent = Math.round((float) summaryDTO.getMedium() / summaryDTO.getTotal() * 100);
            int badPercent = Math.round((float) summaryDTO.getBad() / summaryDTO.getTotal() * 100);

            if(goodPercent + mediumPercent + badPercent != 100) {
                badPercent = 100 - goodPercent - mediumPercent;
            }

            summaryVO.setTotal(summaryDTO.getTotal());
            summaryVO.setGood(goodPercent);
            summaryVO.setMedium(mediumPercent);
            summaryVO.setBad(badPercent);
        }

        return summaryVO;
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益获取记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO, DateTimeFormatter formatter) {
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberDetailRightHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize(), formatter);
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益使用记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO, DateTimeFormatter formatter) {
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberDetailRightSpendHistory(relationDO, pageVO.getCurrent(), pageVO.getPageSize(), formatter);
    }

    /**
     * 会员详情 - 会员信用信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditResp getMemberDetailCredit(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailCredit(relationDO);
    }

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailCreditTradeCommentSummary(relationDO);
    }

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(UserLoginCacheDTO loginUser, MemberDetailCreditHistoryPageDataReq pageVO) {
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberDetailCreditTradeCommentHistory(relationDO, pageVO.getStarLevel(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailCreditAfterSaleCommentSummary(relationDO);
    }

    /**
     * App - 会员信用 - 售后评价汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    @Override
    public MobileCommentSummaryResp getMobileMemberDetailCreditAfterSaleCommentSummary(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MobileCommentSummaryResp summaryVO = new MobileCommentSummaryResp();

        CommentSummaryDTO summaryDTO = memberAfterSaleHistoryRepository.groupByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        if(summaryDTO != null && summaryDTO.getTotal() > 0) {
            int goodPercent = Math.round((float) summaryDTO.getGood() / summaryDTO.getTotal() * 100);
            int mediumPercent = Math.round((float) summaryDTO.getMedium() / summaryDTO.getTotal() * 100);
            int badPercent = Math.round((float) summaryDTO.getBad() / summaryDTO.getTotal() * 100);

            if(goodPercent + mediumPercent + badPercent != 100) {
                badPercent = 100 - goodPercent - mediumPercent;
            }

            summaryVO.setTotal(summaryDTO.getTotal());
            summaryVO.setGood(goodPercent);
            summaryVO.setMedium(mediumPercent);
            summaryVO.setBad(badPercent);
        }

        return summaryVO;
    }

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(UserLoginCacheDTO loginUser, MemberDetailCreditHistoryPageDataReq pageVO) {
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageMemberDetailCreditAfterSaleCommentHistory(relationDO, pageVO.getStarLevel(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(UserLoginCacheDTO loginUser, Long validateId) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return memberComplaintService.summaryUpperMemberComplaints(relationDO.getMember(), relationDO.getRoleId(), relationDO.getSubMember(), relationDO.getSubRoleId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberCreditComplaintPageQueryResp> pageMemberDetailCreditComplainHistory(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO) {
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return memberComplaintService.pageUpperMemberComplaint(relationDO.getMember(), relationDO.getRoleId(), relationDO.getSubMember(), relationDO.getSubRoleId(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * “增加会员角色”功能，查询上级会员列表
     *
     * @param subMemberId        下级会员id
     * @param subRoleId          下降会员角色id
     * @param enableMultiTenancy 是否开启sass多租户配置
     * @return 查询结果
     */
    @Override
    public UpperMemberShowResp getUpperMemberInfo(Long subMemberId, Long subRoleId, Boolean enableMultiTenancy) {
        UpperMemberShowResp showVO = new UpperMemberShowResp();
        showVO.setShow(false);
        if (!enableMultiTenancy) {
            return showVO;
        }
        MemberRoleRuleDO roleRuleDO = roleRuleRepository.findByMemberId(subMemberId);

        //如果没有配置会员角色规则，则查找上级会员信息
        if (roleRuleDO == null) {
            //展示上级会员下拉框
            showVO.setShow(true);

            List<MemberRelationDO> relationDOList = relationRepository.findBySubMemberIdAndSubRoleIdAndSubMemberLevelTypeEnumNot(subMemberId, subRoleId, MemberLevelTypeEnum.PLATFORM.getCode());
            showVO.setUpperMemberList(relationDOList.stream().map(memberRelationDO -> new MemberManageResp(memberRelationDO.getMember().getId(), memberRelationDO.getMember().getName())).collect(Collectors.toList()));
            return showVO;
        }

        return showVO;
    }

    /**
     * “增加会员角色”功能，查询会员类型列表
     * @param memberTypeEnum 当前会员的会员类型
     * @return 查询结果
     */
    @Override
    public List<MemberTypeAndNameResp> getMemberTypeList(Integer memberTypeEnum) {
        //原企业会员增加会员角色，只能选择企业会员，原个人会员增加角色，只能选择个人会员
        return Collections.singletonList(new MemberTypeAndNameResp(MemberTypeEnum.toEnum(memberTypeEnum)));
    }

    /**
     * “增加会员角色”功能，查询会员适用类型列表（saas）
     *
     * @param memberTypeEnum   当前会员的会员类型
     * @param roleManageVOList 会员适用角色list
     * @return 查询结果
     */
    @Override
    public List<MemberTypeAndNameResp> getMemberTypeList(Integer memberTypeEnum, List<RoleRuleManageResp> roleManageVOList) {
        return roleManageVOList
                .stream()
                .filter(roleManageVO -> Objects.equals(roleManageVO.getMemberType(), memberTypeEnum))
                .map(roleManageVO -> new MemberTypeAndNameResp(roleManageVO.getMemberType(), roleManageVO.getMemberTypeName()))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * “增加会员角色”功能，根据会员类型Id查询角色列表，以及勾选的角色Id列表
     * @param subMemberId 下级会员Id
     * @param memberTypeReq    接口参数
     * @return 查询结果
     */
    @Override
    public MemberInfoRoleListResp getRoleListByMemberType(Long subMemberId, MemberTypeReq memberTypeReq) {
        MemberDO memberDO = memberRepository.findById(subMemberId).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        List<Long> roleIds = memberDO.getMemberRoles().stream().map(MemberRoleDO::getId).collect(Collectors.toList());
        MemberInfoRoleListResp listVO = new MemberInfoRoleListResp();
        listVO.setRoles(baseMemberValidateService.getRoleListByMemberType(memberTypeReq.getMemberType()));
        listVO.setCheckIds(roleIds.stream().filter(roleId -> listVO.getRoles().stream().anyMatch(roleIdAndNameVO -> roleIdAndNameVO.getRoleId().equals(roleId))).collect(Collectors.toList()));
        return listVO;
    }

    /**
     * “增加会员角色”功能，根据会员类型Id查询角色列表(saas)
     *
     * @param memberId         会员id
     * @param memberTypeReq             接口参数
     * @param roleManageVOList 会员适用角色list
     * @return 查询结果
     */
    @Override
    public MemberInfoRoleListResp getRoleListByMemberType(Long memberId, MemberTypeReq memberTypeReq, List<RoleRuleManageResp> roleManageVOList) {
        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }
        List<Long> roleIds = memberDO.getMemberRoles().stream().map(MemberRoleDO::getId).collect(Collectors.toList());
        MemberInfoRoleListResp listVO = new MemberInfoRoleListResp();

        listVO.setRoles(roleManageVOList.stream().filter(roleManageVO -> roleManageVO.getMemberType().equals(memberTypeReq.getMemberType())).map(roleManageVO -> new RoleIdAndNameResp(roleManageVO.getRoleId(), roleManageVO.getRoleName())).collect(Collectors.toList()));
        listVO.setCheckIds(roleIds.stream().filter(roleId -> listVO.getRoles().stream().anyMatch(roleIdAndNameVO -> roleIdAndNameVO.getRoleId().equals(roleId))).collect(Collectors.toList()));
        return listVO;
    }

    /**
     * “增加会员角色”功能，会获取员注册资料信息
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param roleIdReq 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberInfoUpdateDetailByRoleResp getMemberRegisterDetailAfterAddRole(Long subMemberId, Long subRoleId, RoleIdReq roleIdReq, Integer roleTag) {
        MemberDO memberDO = memberRepository.findById(subMemberId).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO loginUserRole = memberDO.getMemberRoles().stream().filter(role -> role.getId().equals(subRoleId)).findFirst().orElse(null);
        if (loginUserRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(roleIdReq.getRoleId()).orElse(null);
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //角色是否已经存在
        if (memberDO.getMemberRoles().stream().anyMatch(r -> r.getId().equals(roleIdReq.getRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_EXISTS);
        }

        //判断角色是否错误
        baseMemberValidateService.checkRoleWithMemberType(memberRoleDO, loginUserRole.getMemberType());

        MemberInfoUpdateDetailByRoleResp detailVO = new MemberInfoUpdateDetailByRoleResp();
        detailVO.setOuterVerifySteps(baseMemberValidateService.getPlatformValidateOuterSteps(memberRoleDO.getRoleName(), roleTag));
        detailVO.setCurrentOuterStep(1);

        //基本信息
        detailVO.setMemberId(memberDO.getId());

        //合并会员原来的，与角色的注册信息
        detailVO.setGroups(baseMemberRegisterDetailService.mergeMemberRegisterDetail(memberDO, MemberDetailVersionEnum.USING, memberRoleDO));

        //外部历史记录（即历史流转记录）
        detailVO.setOuterHistory(new ArrayList<>());
        return detailVO;
    }

    /**
     * “增加会员角色”功能，提交注册资料并新增角色
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param addRoleVO 接口参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMemberRole(Long subMemberId, Long subRoleId, MemberInfoAddRoleReq addRoleVO, UserLoginCacheDTO loginUser) {
        MemberDO memberDO = memberRepository.findById(subMemberId).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO loginUserRole = memberDO.getMemberRoles().stream().filter(role -> role.getId().equals(subRoleId)).findFirst().orElse(null);
        if (loginUserRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //不需要判断是否有未通过审核的角色
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(addRoleVO.getRoleId()).orElse(null);
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        baseMemberValidateService.checkRoleWithMemberType(memberRoleDO, loginUserRole.getMemberType());

        //角色是否已经存在
        if (memberDO.getMemberRoles().stream().anyMatch(r -> r.getId().equals(addRoleVO.getRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_EXISTS);
        }

        //会员管理员和管理员角色
        if (CollectionUtils.isEmpty(memberDO.getUserRoles())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        UserRoleDO adminUserRoleDO = memberDO.getUserRoles().stream().filter(userRoleDO -> userRoleDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())).findFirst().orElse(null);
        if (adminUserRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        if (CollectionUtils.isEmpty(memberDO.getUsers())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        UserDO adminUserDO = memberDO.getUsers().stream().filter(userDO -> userDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())).findFirst().orElse(null);
        if (adminUserDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        //平台管理员和角色
        MemberDO adminMemberDO = memberRepository.findPlatformMember();
        if (adminMemberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        if (CollectionUtils.isEmpty(adminMemberDO.getMemberRoles())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        MemberRoleDO adminMemberRoleDO = adminMemberDO.getMemberRoles().iterator().next();
        if (adminMemberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //从现有的注册资料进行检查
        Set<MemberRegisterConfigDO> memberConfigSet = memberDO.getRegisterDetails().stream().map(MemberRegisterDetailDO::getMemberConfig).collect(Collectors.toSet());
        memberConfigSet.addAll(memberRoleDO.getConfigs());

        List<MemberRegisterDetailDO> registerDetails = new ArrayList<>();
        //公司名称是否已被注册
        String memberName = baseMemberRegisterDetailService.checkMemberRegisterDetail(addRoleVO.getDetail(), new ArrayList<>(memberConfigSet), registerDetails, memberDO.getName());

        if (StringUtils.hasLength(memberName) && memberRepository.existsByNameAndIdNot(memberName, subMemberId)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
        }

        //查询角色的平台审核流程配置
        ProcessBO processResult = basePlatformProcessService.findRolePlatformProcess(memberRoleDO);
        MemberRelationDO platformMemberRelationDO = memberInnerService.addMemberRole(processResult, loginUserRole.getRoleName(), adminMemberDO, adminMemberRoleDO, memberDO, memberRoleDO, adminUserRoleDO, adminUserDO, memberName, registerDetails);

        //新增商家下的角色关联关系
        MemberDO upperMember = memberRepository.findById(baiTaiMemberProperties.getSelfMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));
        MemberRoleDO upperMemberRole = memberRoleRepository.findById(baiTaiMemberProperties.getSelfRoleId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST));
        memberInnerService.addMemberRelation(upperMember, upperMemberRole, platformMemberRelationDO, memberRoleDO, MemberTypeEnum.MERCHANT.getCode(), null, MemberLevelTypeEnum.MERCHANT.getCode(), RoleTagEnum.CUSTOMER.getCode(), false);

        //如果是企业认证，生成企业数据
        if (BooleanUtils.isTrue(addRoleVO.getEnterpriseCertificationFlag())) {
            String memberCode = "SCC" + redisUtils.getSerialNumberByDay(MemberRedisConstant.CORPORATION_CODE_PREFIX, 3, RedisConstant.REDIS_USER_INDEX);

            CorporationDO corporationDO = new CorporationDO();
            corporationDO.setMemberId(subMemberId);
            corporationDO.setCode(memberCode);
            corporationDO.setName((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.CORPORATION_NAME));
            corporationDO.setUnifiedSocialCode((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE));
            corporationDO.setBusinessLicense((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE));
            corporationDO.setBusinessLicenseValidEndTime((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME));
            corporationDO.setLegalPersonName((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME));
            corporationDO.setLegalPersonIdentityCardNo((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO));
            corporationDO.setIdCardFront((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.ID_CARD_FRONT));
            corporationDO.setIdCardBack((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.ID_CARD_BACK));
            corporationDO.setBusinessManager((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER));
            corporationDO.setBusinessManagerPhone((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE));
            corporationDO.setFinanceManager((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER));
            corporationDO.setFinanceManagerPhone((String) addRoleVO.getDetail().get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE));
            corporationDO.setGroupIdentifier(memberDO.getPhone());
            corporationDO.setAddress(addRoleVO.getAddress());
            corporationRepository.saveAndFlush(corporationDO);

            MemberDO subMember = platformMemberRelationDO.getSubMember();
            subMember.setCorporationId(corporationDO.getId());
            subMember.setMainFlag(true);
            memberRepository.saveAndFlush(subMember);
        }
    }
}
