package com.ssy.lingxi.member.controller.web.customer;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.NamePageDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.service.web.IMemberComplaintService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 客户能力 - 投诉建议
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/18
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/customer/complaint")
public class CustomerComplaintController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.CUSTOMER.getCode();

    @Resource
    private IMemberComplaintService memberComplaintService;

    /**
     * 状态下拉查询
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/statusList")
    public WrapperResp<List<StatusResp>> listMemberComplaintStatus(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberComplaintService.listMemberComplaintStatus(headers));
    }

    /**
     * 投诉建议 - 分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/upper/page")
    public WrapperResp<PageDataResp<MemberUpperComplaintPageQueryResp>> pageMemberComplaint(@RequestHeader HttpHeaders headers, @Valid MemberComplaintPageDataReq queryVO) {
        return WrapperUtil.success(memberComplaintService.pageUpperMemberComplaint(headers, queryVO, roleTag));
    }

    /**
     * 投诉建议 - 详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/upper/get")
    public WrapperResp<MemberComplaintUpperResp> getMemberComplaintResult(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberComplaintService.getUpperMemberComplaint(headers, idVO));
    }

    /**
     * 投诉建议 - 新增
     * @param headers Http头部信息
     * @param addVO 接口参数
     */
    @PostMapping("/upper/add")
    public WrapperResp<Void> addMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberComplaintUpperAddReq addVO) {
        memberComplaintService.addUpperMemberComplaint(headers, addVO);
        return WrapperUtil.success();
    }

    /**
     * 投诉建议 - 修改
     * @param headers Http头部信息
     * @param addVO 接口参数
     */
    @PostMapping("/upper/update")
    public WrapperResp<Void> updateMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberComplaintUpperUpdateReq addVO) {
        memberComplaintService.updateUpperMemberComplaint(headers, addVO);
        return WrapperUtil.success();
    }

    /**
     * 投诉建议 - 删除
     * @param headers Http头部信息
     * @param idVO 接口参数
     */
    @PostMapping("/upper/delete")
    public WrapperResp<Void> addMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idVO) {
        memberComplaintService.deleteMemberComplaint(headers, idVO);
        return WrapperUtil.success();
    }

    /**
     * 投诉建议 - 提交
     * @param headers Http头部信息
     * @param idVO 接口参数
     */
    @PostMapping("/upper/submit")
    public WrapperResp<Void> submitMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idVO) {
        memberComplaintService.submitMemberComplaint(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 投诉建议 - 处理
     * @param headers Http头部信息
     * @param handleVO 接口参数
     */
    @PostMapping("/upper/send")
    public WrapperResp<Void> handleMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberComplaintHandleReq handleVO) {
        memberComplaintService.handleMemberComplaint(headers, handleVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 投诉建议新增 - 选择上级会员
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/sub/members")
    public WrapperResp<PageDataResp<UpperMemberQueryResp>> pageMembers(@RequestHeader HttpHeaders headers, @Valid NamePageDataReq pageVO) {
        return WrapperUtil.success(memberComplaintService.pageMembers(headers, pageVO));
    }

    /**
     * 投诉建议管理 - 分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/sub/page")
    public WrapperResp<PageDataResp<MemberSubComplaintPageQueryResp>> pageSubMemberComplaint(@RequestHeader HttpHeaders headers, @Valid MemberComplaintPageDataReq queryVO) {
        return WrapperUtil.success(memberComplaintService.pageSubMemberComplaint(headers, queryVO));
    }

    /**
     * 投诉建议管理 - 详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/sub/get")
    public WrapperResp<MemberComplaintSubResp> getSubMemberComplaint(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberComplaintService.getSubMemberComplaint(headers, idVO));
    }

    /**
     * 投诉建议管理 - 新增
     * @param headers Http头部信息
     * @param addVO 接口参数
     */
    @PostMapping("/sub/add")
    public WrapperResp<Void> addMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberComplaintSubAddReq addVO) {
        memberComplaintService.addSubMemberComplaint(headers, addVO);
        return WrapperUtil.success();
    }

    /**
     * 投诉建议管理 - 修改
     * @param headers Http头部信息
     * @param addVO 接口参数
     */
    @PostMapping("/sub/update")
    public WrapperResp<Void> updateMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberComplaintSubUpdateReq addVO) {
        memberComplaintService.updateSubMemberComplaint(headers, addVO);
        return WrapperUtil.success();
    }

    /**
     * 投诉建议管理 - 删除
     * @param headers Http头部信息
     * @param idVO 接口参数
     */
    @PostMapping("/sub/delete")
    public WrapperResp<Void> deleteMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idVO) {
        memberComplaintService.deleteMemberComplaint(headers, idVO);
        return WrapperUtil.success();
    }

    /**
     * 投诉建议管理 - 提交
     * @param headers Http头部信息
     * @param idVO 接口参数
     */
    @PostMapping("/sub/submit")
    public WrapperResp<Void> submitSubMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idVO) {
        memberComplaintService.submitSubMemberComplaint(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 下级处理投诉建议
     *
     * @param headers  请求头
     * @param handleVO 入参
     * @return 返回
     */
    @PostMapping("/sub/send")
    public WrapperResp<Void> handleSubMemberComplaint(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberComplaintHandleReq handleVO) {
        memberComplaintService.handleSubMemberComplaint(headers, handleVO, roleTag);
        return WrapperUtil.success();
    }
}
