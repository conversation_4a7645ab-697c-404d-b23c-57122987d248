package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.IDCardNumberValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

import static java.lang.annotation.ElementType.*;

@Target({ElementType.METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = IDCardNumberValidator.class)
public @interface IDCardNumberAnnotation {
    boolean required() default true;

    String message() default "身份证号码不正确";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
