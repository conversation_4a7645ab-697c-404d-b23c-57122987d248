package com.ssy.lingxi.member.serviceImpl.base;

import com.alibaba.fastjson.JSON;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailHistoryDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberQualityDO;
import com.ssy.lingxi.member.enums.MemberChangeProjectNameEnum;
import com.ssy.lingxi.member.enums.MemberQualityChangeVersionEnum;
import com.ssy.lingxi.member.model.req.validate.MemberQualityReq;
import com.ssy.lingxi.member.model.resp.validate.MemberQualityResp;
import com.ssy.lingxi.member.repository.MemberDepositoryDetailHistoryRepository;
import com.ssy.lingxi.member.repository.MemberQualityRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberQualificationService;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员资质（文件）基础服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-22
 */
@Service
public class BaseMemberQualificationServiceImpl implements IBaseMemberQualificationService {
    @Resource
    private MemberQualityRepository memberQualityRepository;
    @Resource
    private MemberDepositoryDetailHistoryRepository memberDepositoryDetailHistoryRepository;

    /**
     * 检查会员资质，并更新relationDo关联的MemberQualityDO中的信息（已保存，调用方需要保存relationDO）
     *
     * @param relationDO  会员关系
     * @param qualityList 资质文件列表
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void checkAndSaveMemberQualities(MemberRelationDO relationDO, List<MemberQualityReq> qualityList) {
        //检查资质文件日期格式
        if (qualityList.stream().filter(memberQualityVO -> memberQualityVO.getPermanent().equals(EnableDisableStatusEnum.DISABLE.getCode())).anyMatch(memberQualityVO -> SecurityStringUtil.notDateString(memberQualityVO.getExpireDay())) && !CollectionUtils.isEmpty(qualityList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_QUALITY_FILE_DATE_FORMAT_ERROR);
        }

        if (!CollectionUtils.isEmpty(relationDO.getQualities())) {
            relationDO.getQualities().clear();
        }
        relationDO.setQualities(new HashSet<>());

        //查询当前正在使用的资质证明
        List<MemberQualityDO> qualityChanges = memberQualityRepository.findByRelationAndVersion(relationDO, MemberQualityChangeVersionEnum.USING.getCode());
        //删除旧数据
        memberQualityRepository.deleteByRelationAndVersion(relationDO, MemberQualityChangeVersionEnum.USED.getCode());

        if (!CollectionUtils.isEmpty(qualityChanges)) {
            //修改资质证明版本状态为变更前
            qualityChanges.forEach(q -> q.setVersion(MemberQualityChangeVersionEnum.USED.getCode()));
            memberQualityRepository.saveAll(qualityChanges);
        }

        if (!CollectionUtils.isEmpty(qualityList)) {
            List<MemberQualityDO> qualities = qualityList.stream().map(quality -> {
                MemberQualityDO qualityDO = new MemberQualityDO();
                qualityDO.setRelation(relationDO);
                qualityDO.setUrl(quality.getUrl());
                qualityDO.setName(quality.getName());
                qualityDO.setPermanent(quality.getPermanent());
                qualityDO.setVersion(MemberQualityChangeVersionEnum.USING.getCode());
                if (quality.getPermanent().equals(EnableDisableStatusEnum.ENABLE.getCode())) {
                    qualityDO.setExpireTime(LocalDateTime.now());
                } else {
                    qualityDO.setExpireTime(LocalDateTime.parse(quality.getExpireDay().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
                return qualityDO;
            }).collect(Collectors.toList());

            memberQualityRepository.saveAll(qualities);
            relationDO.setQualities(new HashSet<>(qualities));
        }

        //校验资质证明是否发生变更
        if (checkMemberQualitiesChange(qualityChanges, qualityList)) {
            //记录资质文件变更记录
            memberDepositoryDetailHistoryRepository.save(buildMemberQualityHistoryEntity(relationDO));
        }

    }

    /**
     * 检查资质证明是否发生变更
     *
     * @param qualitys       变更前的资质证明
     * @param newQualityList 变更后的资质证明
     * @return 布尔值
     */
    private Boolean checkMemberQualitiesChange(List<MemberQualityDO> qualitys, List<MemberQualityReq> newQualityList) {
        if (CollectionUtils.isEmpty(qualitys) && CollectionUtils.isEmpty(newQualityList)) {
            return false;
        }

        if (!CollectionUtils.isEmpty(qualitys) && !CollectionUtils.isEmpty(newQualityList)) {
            Map<String, MemberQualityReq> qualitysMap = qualitys.stream().map(memberQualityDO -> {
                MemberQualityReq memberQualityReq = new MemberQualityReq();
                memberQualityReq.setUrl(memberQualityDO.getUrl());
                memberQualityReq.setName(memberQualityDO.getName());
                memberQualityReq.setExpireDay(memberQualityDO.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                memberQualityReq.setPermanent(memberQualityDO.getPermanent());
                return memberQualityReq;
            }).collect(Collectors.toMap(MemberQualityReq::getUrl, memberQualityVO -> memberQualityVO));

            //遍历检查是否发生了新增、修改
            for (MemberQualityReq qualityVO : newQualityList) {
                if (!qualitysMap.containsKey(qualityVO.getUrl())) {
                    return true;
                }
                if (!Objects.equals(qualityVO.getPermanent(), qualitysMap.get(qualityVO.getUrl()).getPermanent())) {
                    return true;
                }
                if (!Objects.equals(qualityVO.getExpireDay(), qualitysMap.get(qualityVO.getUrl()).getExpireDay()) && NumberUtil.isNullOrZero(qualityVO.getPermanent()) && NumberUtil.isNullOrZero(qualitysMap.get(qualityVO.getUrl()).getPermanent())) {
                    return true;
                }
            }

            //遍历是否发生了删除
            Map<String, MemberQualityReq> newQualityMap = newQualityList.stream().collect(Collectors.toMap(MemberQualityReq::getUrl, memberQualityVO -> memberQualityVO));
            Set<Map.Entry<String, MemberQualityReq>> urls = qualitysMap.entrySet();
            for (Map.Entry<String, MemberQualityReq> url : urls) {
                if (!newQualityMap.containsKey(url.getKey())) {
                    return true;
                }
            }
            //检索完毕没有发生变更
            return false;
        }
        //两个集合中有且仅有一个为空时，必然发生了变更
        return true;
    }

    /**
     * 记录资质证明资料的变更历史
     *
     * @param relationDO 会员关系
     */
    public MemberDepositoryDetailHistoryDO buildMemberQualityHistoryEntity(MemberRelationDO relationDO) {
        List<MemberQualityReq> qualitys = memberQualityRepository.findByRelation(relationDO).stream().map(memberQualityDO -> {
            MemberQualityReq vo = new MemberQualityReq();
            vo.setUrl(memberQualityDO.getUrl());
            vo.setName(memberQualityDO.getName());
            vo.setExpireDay(memberQualityDO.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            vo.setPermanent(memberQualityDO.getPermanent());
            vo.setVersion(memberQualityDO.getVersion());
            return vo;
        }).collect(Collectors.toList());

        MemberDepositoryDetailHistoryDO historyDO = new MemberDepositoryDetailHistoryDO();
        if (CollectionUtils.isEmpty(qualitys)) {
            return historyDO;
        }
        //正在使用的版本
        List<MemberQualityReq> usingList = qualitys.stream().filter(quality -> Objects.equals(quality.getVersion(), MemberQualityChangeVersionEnum.USING.getCode())).collect(Collectors.toList());
        //变更后的版本
        List<MemberQualityReq> usedList = qualitys.stream().filter(quality -> Objects.equals(quality.getVersion(), MemberQualityChangeVersionEnum.USED.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(usedList) && CollectionUtils.isEmpty(usingList)) {
            return historyDO;
        }

        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setMemberId(relationDO.getMemberId());
        historyDO.setRoleId(relationDO.getRoleId());
        historyDO.setSubMemberId(relationDO.getSubMemberId());
        historyDO.setSubRoleId(relationDO.getSubRoleId());
        historyDO.setFieldLocalName(MemberChangeProjectNameEnum.MEMBER_QUALITY_PHOTO.getMessage());
        historyDO.setFieldValue(JSON.toJSONString(usingList));
        historyDO.setLastValue(JSON.toJSONString(usedList));

        return historyDO;
    }

    /**
     * 检查会员资质，调用方要设置MemberRelationDO并保存
     *
     * @param qualityList 资质文件接口参数列表
     * @return 资质文件
     */
    @Override
    public WrapperResp<List<MemberQualityDO>> checkMemberQualities(List<MemberQualityReq> qualityList) {
        if (CollectionUtils.isEmpty(qualityList)) {
            return WrapperUtil.success(new ArrayList<>());
        }

        //检查资质文件日期格式
        if (qualityList.stream().filter(memberQualityVO -> memberQualityVO.getPermanent().equals(EnableDisableStatusEnum.DISABLE.getCode())).anyMatch(memberQualityVO -> SecurityStringUtil.notDateString(memberQualityVO.getExpireDay()))) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_QUALITY_FILE_DATE_FORMAT_ERROR);
        }

        return WrapperUtil.success(qualityList.stream().map(quality -> {
            MemberQualityDO qualityDO = new MemberQualityDO();
            qualityDO.setUrl(quality.getUrl());
            qualityDO.setName(quality.getName());
            qualityDO.setPermanent(quality.getPermanent());
            if (quality.getPermanent().equals(EnableDisableStatusEnum.ENABLE.getCode())) {
                qualityDO.setExpireTime(LocalDateTime.now());
            } else {
                qualityDO.setExpireTime(LocalDateTime.parse(quality.getExpireDay().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            return qualityDO;
        }).collect(Collectors.toList()));
    }

    /**
     * 查询会员资质列表
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public List<MemberQualityResp> findMemberQualities(MemberRelationDO relationDO) {
        return memberQualityRepository.findByRelationAndVersion(relationDO, MemberQualityChangeVersionEnum.USING.getCode()).stream().map(memberQualityDO -> {
            MemberQualityResp queryVO = new MemberQualityResp();
            queryVO.setUrl(memberQualityDO.getUrl());
            queryVO.setName(memberQualityDO.getName());
            queryVO.setPermanent(memberQualityDO.getPermanent());
            queryVO.setExpireDay(memberQualityDO.getPermanent().equals(EnableDisableStatusEnum.ENABLE.getCode()) ? "" : memberQualityDO.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询会员会员变革的资质列表
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public List<MemberQualityResp> findMemberChangeQualities(MemberRelationDO relationDO) {
        List<MemberQualityDO> memberQualitys = memberQualityRepository.findByRelation(relationDO);

        Map<String, MemberQualityDO> usedMap = memberQualitys.stream().filter(memberQualityDO -> Objects.equals(memberQualityDO.getVersion(), MemberQualityChangeVersionEnum.USED.getCode())).collect(Collectors.toMap(MemberQualityDO::getUrl, memberQualityDO -> memberQualityDO));
        Map<String, MemberQualityDO> usingMap = memberQualitys.stream().filter(memberQualityDO -> Objects.equals(memberQualityDO.getVersion(), MemberQualityChangeVersionEnum.USING.getCode())).collect(Collectors.toMap(MemberQualityDO::getUrl, memberQualityDO -> memberQualityDO));
        //封装数据
        List<MemberQualityResp> memberQualityResps = memberQualitys.stream().filter(memberQualityDO -> Objects.equals(memberQualityDO.getVersion(), MemberQualityChangeVersionEnum.USING.getCode())).map(memberQualityDO -> {
            MemberQualityResp queryVO = new MemberQualityResp();
            queryVO.setUrl(memberQualityDO.getUrl());
            queryVO.setName(memberQualityDO.getName());
            queryVO.setPermanent(memberQualityDO.getPermanent());
            queryVO.setExpireDay(memberQualityDO.getPermanent().equals(EnableDisableStatusEnum.ENABLE.getCode()) ? "" : memberQualityDO.getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            if (usedMap.containsKey(queryVO.getUrl())) {
                queryVO.setLastUrl(queryVO.getUrl());
                queryVO.setLastName(usedMap.get(queryVO.getUrl()).getName());
                queryVO.setLastPermanent(usedMap.get(queryVO.getUrl()).getPermanent());
                queryVO.setLastExpireDay(usedMap.get(queryVO.getUrl()).getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            return queryVO;
        }).collect(Collectors.toList());

        Set<Map.Entry<String, MemberQualityDO>> userSet = usedMap.entrySet();
        for (Map.Entry<String, MemberQualityDO> memberQualityDOEntry : userSet) {
            if (!usingMap.containsKey(memberQualityDOEntry.getKey())) {
                MemberQualityResp queryVO = new MemberQualityResp();
                queryVO.setLastUrl(memberQualityDOEntry.getValue().getUrl());
                queryVO.setLastName(memberQualityDOEntry.getValue().getName());
                queryVO.setLastPermanent(memberQualityDOEntry.getValue().getPermanent());
                queryVO.setLastExpireDay(memberQualityDOEntry.getValue().getExpireTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                memberQualityResps.add(queryVO);
            }
        }

        return memberQualityResps;
    }
}
