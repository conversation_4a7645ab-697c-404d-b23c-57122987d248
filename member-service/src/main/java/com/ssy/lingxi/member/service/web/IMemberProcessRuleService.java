package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.member.MemberProcessTypeEnum;
import com.ssy.lingxi.member.entity.bo.ProcessBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.model.req.basic.NamePageDataReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.basic.BaseMemberProcessResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigNameResp;
import com.ssy.lingxi.member.model.resp.basic.RoleManageResp;
import com.ssy.lingxi.member.model.resp.validate.MemberProcessQueryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberProcessResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员管理流程规则配置相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-14
 */
public interface IMemberProcessRuleService {
    /**
     * 分页查询会员流程规则配置列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberProcessQueryResp> pageMemberProcessRules(HttpHeaders headers, NamePageDataReq pageVO);

    /**
     * 查询入库流程、变更流程列表
     * @param headers Http头部信息
     * @param roleTag 角色标签
     * @return 查询结果
     */
    List<BaseMemberProcessResp> listBaseMemberProcesses(HttpHeaders headers, Integer roleTag);

    /**
     * 查询角色列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<RoleManageResp> pageRoles(HttpHeaders headers, NamePageDataReq pageVO);

    /**
     * 查询角色列表
     * @param loginUser 登录会员信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<RoleManageResp> pageRoles(UserLoginCacheDTO loginUser, NamePageDataReq pageVO);

    /**
     * 查询平台默认注册资料
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberConfigNameResp> pageRoleConfigDetail(HttpHeaders headers, RoleIdPageDataReq pageVO);

    /**
     * 选择注册资料
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberConfigNameResp> pageConfigDetail(HttpHeaders headers, RoleIdPageDataReq pageVO);

    /**
     * 预览注册资料
     * @param headers Http头部信息
     * @param previewVO 接口参数
     * @return 预览结果
     */
    List<MemberConfigGroupResp> previewMemberDepositoryDetails(HttpHeaders headers, MemberProcessPreviewReq previewVO);

    /**
     * 新增流程规则配置
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 新增结果
     */
    void addMemberProcessRule(HttpHeaders headers, MemberProcessAddReq addVO);

    /**
     * 修改流程规则配置
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @return 修改结果
     */
    void updateMemberProcessRule(HttpHeaders headers, MemberProcessUpdateReq updateVO);

    /**
     * 查询流程规则配置详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberProcessResp getMemberProcessRule(HttpHeaders headers, MemberProcessIdReq idVO);

    /**
     * 分页查询流程规则配置关联的入库资料
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberConfigNameResp> pageMemberProcessDepositDetails(HttpHeaders headers, MemberProcessDepositDetailPageDataReq pageVO);

    /**
     * 修改流程规则状态
     * @param headers Http头部信息
     * @param statusVO 接口参数
     * @return 修改结果
     */
    void updateMemberProcessRuleStatus(HttpHeaders headers, MemberProcessIdAndStatusReq statusVO);

    /**
     * 删除流程规则
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 删除结果
     */
    void deleteMemberProcessRule(HttpHeaders headers, MemberProcessIdReq idVO);

    /**
     * 查找流程ProcessKey，如上级会员没有配置，返回默认的流程规则
     * @param relationDO 会员关系
     * @param processTypeEnum 流程类型枚举
     * @return 流程ProcessKey
     */
    ProcessBO findMemberProcessKey(MemberRelationDO relationDO, MemberProcessTypeEnum processTypeEnum);


    /**
     * 查找流程ProcessKey，如上级会员没有配置，返回默认的流程规则
     * @param memberId 上级会员Id
     * @param roleId 下级会员Id
     * @param subRole 下级会员角色
     * @param processTypeEnum 流程类型枚举
     * @return 流程ProcessKey
     */
    ProcessBO findMemberProcessKey(Long memberId, Long roleId, MemberRoleDO subRole, MemberProcessTypeEnum processTypeEnum);

    /**
     * 下级会员角色是否存在非空的入库资料配置
     * @param memberId 上级会员Id
     * @param roleId   上级会员角色Id
     * @param subRoles  下级会员角色列表
     * @return 是/否
     */
    boolean existDepositoryConfig(Long memberId, Long roleId, List<MemberRoleDO> subRoles);
}
