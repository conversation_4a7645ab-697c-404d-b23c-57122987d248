package com.ssy.lingxi.member.controller;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.ReportTodayResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.resp.MemberReportResp;
import com.ssy.lingxi.member.service.IMemberReportService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 统计报表
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/12/8
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/report")
public class MemberReportController extends BaseController {
    @Resource
    private IMemberReportService memberReportService;

    /**
     * 待办统计(供应商)--能力中心
     **/
    @GetMapping(value = "/getMember")
    public WrapperResp<MemberReportResp> getMember() {
        return WrapperUtil.success(memberReportService.getMember(getSysUser()));
    }

    /**
     * 今日新增--平台后台
     */
    @GetMapping(value = "/getTodayNew")
    WrapperResp<ReportTodayResp> getTodayNew() {
        return WrapperUtil.success(memberReportService.getTodayNew());
    }
}
