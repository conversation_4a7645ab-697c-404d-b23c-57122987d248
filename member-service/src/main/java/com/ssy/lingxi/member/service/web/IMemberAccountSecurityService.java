package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.login.EmailLoginSmsCode;
import com.ssy.lingxi.member.model.req.login.PhoneLoginSmsCode;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.req.platform.PlatformManageUpdateForgetPasswordReq;
import com.ssy.lingxi.member.model.req.platform.PlatformUpdateForgetPasswordReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberCancellationFailResp;
import com.ssy.lingxi.member.model.resp.maintenance.UserAccountAuthQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.UserAccountSecurityQueryResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformUpdateForgetPasswordResp;
import org.springframework.http.HttpHeaders;

/**
 * 系统能力 - 用户账号安全服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-06
 */
public interface IMemberAccountSecurityService {

    /**
     * “账户安全”页面，查询用户的手机号码和邮箱，以掩码方式返回
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    UserAccountSecurityQueryResp getPhoneAndEmail(HttpHeaders headers);

    /**
     * （通用）检查支付密码是否正确
     *
     * @param headers       Http头部信息
     * @param payPasswordReq 接口参数
     */
    void checkPayPassword(HttpHeaders headers, PayPasswordReq payPasswordReq);

    /**
     * 修改登录密码（手机校验码验证）时，发送手机短信验证码
     *
     * @param headers Http头部信息
     */
    void sendChangePasswordSmsCode(HttpHeaders headers);

    /**
     * 修改登录密码（手机校验码验证）时，检查手机短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    void checkChangePasswordSmsCode(HttpHeaders headers, SmsCodeReq smsCodeReq);

    /**
     * 修改登录密码（邮箱验证）时，发送邮件验证码
     *
     * @param headers Http头部信息
     */
    void sendChangePasswordEmailCode(HttpHeaders headers);

    /**
     * 修改登录密码（邮箱验证）时，检查邮件验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    void checkChangePasswordEmailCode(HttpHeaders headers, SmsCodeReq smsCodeReq);

    /**
     * 修改登录密码
     *
     * @param headers      Http头部信息
     * @param userManageVO 接口参数
     */
    void changePassword(HttpHeaders headers, UserUpdatePasswordReq userManageVO);

    /**
     * 修改邮箱（手机校验码验证）时，发送手机短信验证码
     *
     * @param headers Http头部信息
     */
    void sendChangeEmailSmsCode(HttpHeaders headers);

    /**
     * 修改邮箱（手机校验码验证）时，检查手机短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    void checkChangeEmailSmsCode(HttpHeaders headers, SmsCodeReq smsCodeReq);

    /**
     * 修改邮箱（邮箱验证）时，发送邮件验证码
     *
     * @param headers Http头部信息
     */
    void sendChangeEmailEmailCode(HttpHeaders headers);

    /**
     * 修改邮箱（邮箱验证）时，检查邮件验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    void checkChangeEmailEmailCode(HttpHeaders headers, SmsCodeReq smsCodeReq);

    /**
     * 修改邮箱时，向新邮箱发送邮件验证码
     *
     * @param headers Http头部信息
     * @param emailReq 接口参数
     */
    void sendChangeEmailEmailCodeToNew(HttpHeaders headers, EmailReq emailReq);

    /**
     * 修改邮箱
     *
     * @param headers      Http头部信息
     * @param userManageVO 接口参数
     */
    void changeEmail(HttpHeaders headers, UserUpdateEmailReq userManageVO);

    /**
     * 修改手机号码（手机校验码验证）时，发送手机短信验证码
     *
     * @param headers Http头部信息
     */
    void sendChangePhoneSmsCode(HttpHeaders headers);

    /**
     * 修改手机号码（手机校验码验证）时，检查手机短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    void checkPhoneSmsCode(HttpHeaders headers, SmsCodeReq smsCodeReq);

    /**
     * 修改手机号码（邮箱验证）时，发送邮件验证码
     *
     * @param headers Http头部信息
     */
    void sendChangePhoneEmailCode(HttpHeaders headers);

    /**
     * 修改手机号码（邮箱验证）时，检查邮件验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    void checkChangePhoneEmailCode(HttpHeaders headers, SmsCodeReq smsCodeReq);

    /**
     * 修改手机号码时，向新手机号码发送短信验证码
     *
     * @param headers Http头部信息
     * @param phoneReq 接口参数
     */
    void sendChangePhoneSmsCodeToNew(HttpHeaders headers, PhoneReq phoneReq);

    /**
     * 修改手机号码时，检查向新手机号码发送的短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeVO 接口参数
     */
    void checkNewPhoneSmsCode(HttpHeaders headers, PhoneSmsReq smsCodeVO);

    /**
     * 修改手机号
     *
     * @param headers      Http头部信息
     * @param userManageVO 接口参数
     */
    void changePhone(HttpHeaders headers, UserUpdatePhoneReq userManageVO);

    /**
     * 重置支付密码时，发送手机短信验证码
     *
     * @param headers Http头部信息
     */
    void sendChangePayPswSmsCode(HttpHeaders headers);

    /**
     * 重置支付密码时，检查手机短信验证码是否正确
     *
     * @param headers   Http头部信息
     * @param smsCodeReq 接口参数
     */
    void checkChangePayPswSmsCode(HttpHeaders headers, SmsCodeReq smsCodeReq);

    /**
     * 手机校验码验证后，修改支付密码
     *
     * @param headers      Http头部信息
     * @param userManageVO 接口参数
     */
    void changePayPassword(HttpHeaders headers, UserUpdatePayPasswordReq userManageVO);


    /**
     * 平台后台 - 更改用户密码
     *
     * @param headers    Http头部信息
     * @param passwordVO 接口参数
     */
    void updatePlatformUserPassword(HttpHeaders headers, PlatformUpdatePasswordReq passwordVO);

    /**
     * 账户安全-实名验证-显示已实名认证的信息
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    UserAccountAuthQueryResp getUserInfo(HttpHeaders headers);

    /**
     * 账户安全-实名验证-上传身份证信息
     *
     * @return 操作结果
     */
    UserAccountAuthQueryResp uploadIdCard(UserUploadIdCardReq userUploadIdCardReq);

    /**
     * 账户安全-实名验证-保存身份证信息
     * @param headers Http头部信息
     * @param userAuthInfoReq 实名信息请求体
     */
    void saveAuthInfo(HttpHeaders headers, UserAuthInfoReq userAuthInfoReq);

    /**
     * 会员注销 - 获取协议注销枚举
     * @param headers Http头部信息
     * @return 操作结果
     */
    Integer memberCancellationEnumType(HttpHeaders headers);

    /**
     * 会员注销 - 校验
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    MemberCancellationFailResp memberCancellationCheck(HttpHeaders headers);

    /**
     * 会员注销 - 获取验证码
     *
     * @param headers Http头部信息
     */
    void sendCancellationMemberSmsCode(HttpHeaders headers);

    /**
     * 会员注销 - 确认注销接口
     *
     * @param headers  Http头部信息
     * @param cancellationReq 接口参数
     * @return 操作结果
     */
    MemberCancellationFailResp cancellationMember(HttpHeaders headers, CancellationMemberReq cancellationReq);

    /**
     * 平台后台- 登陆页面忘记密码 - 发送短信验证码
     *
     * @param headers  Http头部信息
     * @param phoneReq 接口参数
     */
    void sendManageLoginPhoneSmsCode(HttpHeaders headers, PhoneLoginSmsCode phoneReq);

    /**
     * 平台后台- 登陆页面忘记密码 - 发送邮箱验证码
     *
     * @param headers  Http头部信息
     * @param emailReq 接口参数
     */
    void sendManageLoginEmailSmsCode(HttpHeaders headers, EmailLoginSmsCode emailReq);

    /**
     * 平台后台 - 更改用户密码
     *
     * @param headers    Http头部信息
     * @param passwordVO 接口参数
     * @return 修改结果
     */
    PlatformUpdateForgetPasswordResp updatePlatformUserForgetPsw(HttpHeaders headers, PlatformUpdateForgetPasswordReq passwordVO);

    /**
     * 平台后台 - 超管用户更改密码
     *
     * @param headers    Http头部信息
     * @param passwordVO 接口参数
     * @return 修改结果
     */
    void updatePlatformManageUserForgetPsw(HttpHeaders headers, PlatformManageUpdateForgetPasswordReq passwordVO);
}
