package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.ipMonitor.anno.PersonInfoIpMonitor;
import com.ssy.lingxi.component.ipMonitor.anno.RegisterIpMonitor;
import com.ssy.lingxi.component.ipMonitor.anno.SmsCodeIpMonitor;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.login.MemberRegisterReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordByEmailCodeReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordBySmsCodeReq;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterResultResp;
import com.ssy.lingxi.member.model.resp.login.MultiAccInfoResp;
import com.ssy.lingxi.member.service.web.IMemberRegisterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 企业商城门户 - 会员注册
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-03
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/register")
@Validated
@Slf4j
public class MemberRegisterController {
    @Resource
    private IMemberRegisterService memberRegisterService;

    /**
     * 检查手机号码是否被注册使用
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    @PersonInfoIpMonitor
    @PostMapping("/phone/check")
    public WrapperResp<Void> checkPhoneRegistered(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneReq phoneReq) {
        memberRegisterService.checkPhoneRegistered(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 检查邮箱是否被注册使用
     * @param headers Http头部信息
     * @param emailReq 邮箱
     * @return 检查结果
     */
    @PersonInfoIpMonitor
    @PostMapping("/email/check")
    public WrapperResp<Void> checkEmailRegistered(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailReq emailReq) {
        memberRegisterService.checkEmailRegistered(headers, emailReq);
        return WrapperUtil.success();
    }

    /**
     * 发送注册时短信验证码
     * @param headers Http头部信息
     * @param phoneVO 接口参数
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @RequestMapping("/sms")
    public WrapperResp<Void> sendRegisterSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid SmsPhoneReq phoneVO) {
        memberRegisterService.sendRegisterSmsCode(headers, phoneVO);
        return WrapperUtil.success();
    }

    /**
     * 发送手机号找回密码时的短信验证码
     * @param headers Http头部信息
     * @param phoneVO 手机号码
     * @return 发送结果
     */
    @PersonInfoIpMonitor
    @RequestMapping("/psw/sms")
    public WrapperResp<Void> sendResetPasswordSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid SmsPhoneReq phoneVO) {
        memberRegisterService.sendResetPasswordSmsCode(headers, phoneVO);
        return WrapperUtil.success();
    }

    /**
     * 校验手机号找回密码时的短信验证码是否正确
     * @param headers Http头部信息
     * @param phoneSmsReq 手机号码、验证码
     * @return 发送结果
     */
    @PersonInfoIpMonitor
    @PostMapping("/psw/sms/check")
    public WrapperResp<List<MultiAccInfoResp>> checkResetPasswordSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneSmsReq phoneSmsReq) {
        return WrapperUtil.success(memberRegisterService.checkResetPasswordSmsCode(headers, phoneSmsReq));
    }

    /**
     * 根据短信验证码重设密码
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/reset/sms")
    public WrapperResp<Void> resetPasswordBySmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid ResetPasswordBySmsCodeReq codeVO) {
        memberRegisterService.resetPasswordBySmsCode(headers, codeVO);
        return WrapperUtil.success();
    }

    /**
     * 发送邮箱找回密码时的邮件
     * @param headers Http头部信息
     * @param emailReq 邮箱地址
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @RequestMapping("/psw/email")
    public WrapperResp<Void> sendResetPasswordEmail(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailReq emailReq) {
        memberRegisterService.sendResetPasswordEmail(headers, emailReq);
        return WrapperUtil.success();
    }

    /**
     * 校验邮箱找回密码时的邮件验证码是否正确
     * @param headers Http头部信息
     * @param emailReq 邮箱地址
     * @return 发送结果
     */
    @PersonInfoIpMonitor
    @PostMapping("/psw/email/check")
    public WrapperResp<List<MultiAccInfoResp>> checkResetPasswordEmailCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailSmsReq emailReq) {
        return WrapperUtil.success(memberRegisterService.checkResetPasswordEmailCode(headers, emailReq));
    }

    /**
     * 根据邮箱验证码重设密码
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/reset/email")
    public WrapperResp<Void> resetPasswordByEmailCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid ResetPasswordByEmailCodeReq codeVO) {
        memberRegisterService.resetPasswordByEmailCode(headers, codeVO);
        return WrapperUtil.success();
    }

    /**
     * 会员注册
     * @param headers Http头部信息
     * @param registerVO 接口参数
     * @return 注册结果
     */
    @RegisterIpMonitor
    @PostMapping(value = "")
    public WrapperResp<MemberRegisterResultResp> registerMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberRegisterReq registerVO) {
        return WrapperUtil.success(memberRegisterService.registerPlatformMember(headers, registerVO));
    }


    /**
     * 校验邀请码是否存在
     * @param headers Http头部信息
     * @param invitationCodeReq 邀请码
     * @return 检查结果
     */
    @PostMapping("/invitation-code/check")
    public WrapperResp<Void> checkInvitationCodeExists(@RequestHeader HttpHeaders headers, @RequestBody @Valid InvitationCodeReq invitationCodeReq) {
        memberRegisterService.checkInvitationCodeExists(headers, invitationCodeReq);
        return WrapperUtil.success();
    }
}
