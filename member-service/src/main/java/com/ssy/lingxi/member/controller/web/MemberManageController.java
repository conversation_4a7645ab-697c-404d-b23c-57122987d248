package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.dataauth.annotation.member.MemberAuth;
import com.ssy.lingxi.member.api.model.resp.MemberManageQueryResp;
import com.ssy.lingxi.member.model.req.basic.MemberNameDataReq;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.basic.UserPageDataReq;
import com.ssy.lingxi.member.model.req.manage.*;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.UserQueryResp;
import com.ssy.lingxi.member.model.resp.manage.*;
import com.ssy.lingxi.member.service.web.IMemberManageService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台及业务平台 - 通用会员查询服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-05
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/manage")
public class MemberManageController {
    @Resource
    private IMemberManageService memberManageService;

    /**
     * 根据角色类型，查询角色列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/role/all")
    public WrapperResp<List<RoleIdAndNameResp>> allRoles(@RequestHeader HttpHeaders headers, @Valid MemberManageRoleTypeReq roleTypeVO) {
        return WrapperUtil.success(memberManageService.allRoles(headers, roleTypeVO));
    }

    /**
     * 根据当前登录会员，查询下级会员角色列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/role/sub/list")
    public WrapperResp<List<RoleIdAndNameResp>> getSubRoleListByMember(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberManageService.getSubRoleListByMember(headers));
    }

    /**
     * 根据会员名称、模糊分页查询下级会员列表
     * <p>平台规则设置 - 新增支付策略</p>
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/lower/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageMembersByName(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageLowerMembersByName(headers, pageVO));
    }

    /**
     * 根据下级会员名称、下级会员角色Id，模糊分页查询下级会员列表
     * @param headers  HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/lower/page/bynamerole")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageMembersByNameAndRole(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameAndRoleIdDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageLowerMembersByNameAndRole(headers, pageVO));
    }

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务提供者的下级会员列表
     * @param headers  HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/lower/provider/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageLowerProviderMembersByName(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberManageNameWithExcludePageDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageLowerProviderMembersByName(headers, pageVO));
    }

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务消费者的下级会员列表（带排除列表）
     * @param headers  HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/lower/consumer/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageLowerConsumerMembersByName(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberManageNameWithExcludePageDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageLowerConsumerMembersByName(headers, pageVO));
    }

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务消费者的下级会员列表
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/lower/consumer/member/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageLowerConsumerMembersByName(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageLowerConsumerMembersByName(headers, pageVO));
    }

    /**
     * 根据下级会员名称，模糊分页查询会员角色为服务提供者的下级“企业会员”列表
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/lower/merchant/provider/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageLowerMerchantProviderMembersByName(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageLowerMerchantProviderMembersByName(headers, pageVO));
    }

    /**
     * 根据会员名称等条件+商城类型分页查询下属会员列表
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/lower/bymall/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageMemberByNameAndMallType(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameAndMallTypeDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageMemberByNameAndMallType(headers, pageVO));
    }

    /**
     * 根据上级会员Id和上级会员角色Id，以及当前用户，查询价格权益参数设置
     * @param headers HttpHeaders信息
     * @param upperVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/upper/credit/param/get")
    public WrapperResp<MemberManageMemberCreditParameterResp> getUpperMemberCreditParameter(@RequestHeader HttpHeaders headers, @Valid MemberManageUpperMemberAndRoleReq upperVO) {
        return WrapperUtil.success(memberManageService.getUpperMemberCreditParameter(headers, upperVO));
    }

    /**
     * 根据当前用户（上级会员），查询下级会员的价格权益参数设置
     * @param headers HttpHeaders信息
     * @param subVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/lower/credit/param/get")
    public WrapperResp<MemberManageMemberCreditParameterResp> getLowerMemberCreditParameter(@RequestHeader HttpHeaders headers, @Valid SubMemberIdRoleIdDataReq subVO) {
        return WrapperUtil.success(memberManageService.getLowerMemberCreditParameter(headers, subVO));
    }

    /**
     * 根据下单类型和会员名称，模糊分页查询会员
     * @param headers HttpHeaders信息
     * @param typeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/all/page/byordertype")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageMembersByOrderTypeAndName(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByOrderTypeAndNameDataReq typeVO) {
        return WrapperUtil.success(memberManageService.pageMembersByOrderTypeAndName(headers, typeVO));
    }

    /**
     * 分页查询会员列表页面搜索条件内容
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @GetMapping("/pageitems")
    public WrapperResp<MemberManageSearchConditionResp> getPageSearchConditions(@RequestHeader HttpHeaders headers, MemberManageMemberItemReq itemVO) {
        return WrapperUtil.success(memberManageService.getPageSearchConditions(headers, itemVO));
    }

    /**
     * “新增仓位存储”：根据商城类型分页查询会员
     * @param headers HttpHeaders信息
     * @param typeVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/all/page/byshoptype")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageMembersByShopType(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberManagePageByShopTypeDataReq typeVO) {
        return WrapperUtil.success(memberManageService.pageMembersByShopType(headers, typeVO));
    }

    /**
     * 分页查询角色为“服务提供者”的平台会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/all/provider/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageMembersByServiceProviderRole(@RequestHeader HttpHeaders headers, @Valid MemberManagePageDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageMembersByServiceProviderRole(headers, pageVO));
    }

    /**
     * 分页查询角色为“服务消费者”的平台会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/all/consumer/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageMembersByServiceConsumerRole(@RequestHeader HttpHeaders headers, @Valid MemberManagePageDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageMembersByServiceConsumerRole(headers, pageVO));
    }

    /**
     * 根据会员名称，分页查询角色类型为服务提供者的平台商户会员
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/platform/provider/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pagePlatformServiceProviderMerchantMember(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pagePlatformServiceProviderMerchantMember(headers, pageVO));
    }

    /**
     * 根据会员名称，分页查询角色类型为服务提供者的平台企业会员（非企业个人会员）
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/platform/enterprise/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pagePlatformServiceProviderEnterpriseMember(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pagePlatformServiceProviderEnterpriseMember(headers, pageVO));
    }

    /**
     * 根据会员名称，分页查询角色为服务提供者的上级会员列表
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/upper/provider/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageUpperProviderMember(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageByNameVO) {
        return WrapperUtil.success(memberManageService.pageUpperProviderMember(headers, pageByNameVO));
    }

    /**
     * 根据会员名称，分页查询角色为服务提供者的上级会员列表(增加条件会员等级类型为商户会员)
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/upper/providerMerchant/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageUpperProviderMerchantMember(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageByNameVO) {
        return WrapperUtil.success(memberManageService.pageUpperProviderMerchantMember(headers, pageByNameVO));
    }

    /**
     * 根据会员名称，分页查询角色为当前会员所属的下属会员且角色类型为服务提供的会员
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/lower/providerMerchant/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageLowerProviderMerchantMember(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageByNameVO) {
        return WrapperUtil.success(memberManageService.pageLowerProviderMerchantMember(headers, pageByNameVO));
    }

    /**
     * 根据会员名称，分页查询上级会员列表
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/upper/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageUpperMember(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageByNameVO) {
        return WrapperUtil.success(memberManageService.pageUpperMember(headers, pageByNameVO));
    }


    /**
     * “售后能力 - 提交换货申请单” - 选择供应会员
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/aftersale/replace/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageAfterSaleMember(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageByNameVO) {
        return WrapperUtil.success(memberManageService.pageAfterSaleMember(headers, pageByNameVO));
    }

    /**
     * “物流能力 - 新增物流公司” - 选择物流服务商
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/logistics/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageLogisticSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberManagePageLogisticsDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageLogisticSubMember(headers, pageVO));
    }

    /**
     * 分页查询会员下属用户
     * @param headers Http头部新
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/users/page")
    public WrapperResp<PageDataResp<UserQueryResp>> pageUsers(@RequestHeader HttpHeaders headers, @Valid UserPageDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageUsers(headers, pageVO));
    }

    /**
     * "适用会员等级", 查询等级配置详情
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/marketing/suitable/level/config/page")
    public WrapperResp<PageDataResp<MemberSuitableLevelConfigResp>> pageMarketingSuitableLevelConfig(@RequestHeader HttpHeaders headers, @Valid MemberLevelDetailPageRespData pageVO) {
        return WrapperUtil.success(memberManageService.pageMarketingSuitableLevelConfig(headers, pageVO));
    }

    /**
     * “营销能力” - 获取适用会员查询条件
     * @param headers Http头部信息
     * @param typeVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/marketing/suitable/page/items")
    public WrapperResp<MemberSuitableConditionResp> getMarketingSuitableCondition(@RequestHeader HttpHeaders headers, @RequestBody(required = false) SuitableMemberTypeReq typeVO) {
        return WrapperUtil.success(memberManageService.getMarketingSuitableCondition(headers, typeVO));
    }

    /**
     * “营销能力” - 查询适用会员(分页)
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/marketing/suitable/page")
    public WrapperResp<PageDataResp<MemberSuitableResp>> pageMarketingSuitable(@RequestHeader HttpHeaders headers, @RequestBody @Valid MarketingSuitablePageDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageMarketingSuitable(headers, pageVO));
    }

    /**
     * “平台营销” - 获取适用会员查询条件
     * @param headers Http头部信息
     * @param ageTypeVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/platform/marketing/suitable/page/items")
    public WrapperResp<PlatformMemberSuitableConditionResp> getPlatformMarketingSuitableCondition(@RequestHeader HttpHeaders headers, @RequestBody(required = false) PlatformSuitableMemberTypeReq ageTypeVO) {
        return WrapperUtil.success(memberManageService.getPlatformMarketingSuitableCondition(headers, ageTypeVO));
    }

    /**
     * “平台营销” - 查询适用会员(分页)
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/platform/marketing/suitable/page")
    public WrapperResp<PageDataResp<MemberSuitableResp>> pagePlatformMarketingSuitable(@RequestHeader HttpHeaders headers, @RequestBody @Valid MarketingSuitablePageDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pagePlatformMarketingSuitable(headers, pageVO));
    }

    /**
     * “平台营销” - 获取邀请报名参加会员查询条件
     * @param headers Http头部信息
     * @return 查询结果
     */
    @PostMapping("/platform/marketing/invite/page/items")
    public WrapperResp<PlatformMemberInviteConditionResp> getPlatformMarketingInviteCondition(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberManageService.getPlatformMarketingInviteCondition(headers));
    }

    /**
     * “平台营销” - 查询邀请报名参加会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/platform/marketing/invite/page")
    public WrapperResp<PageDataResp<PlatformMarketingInvitePageResp>> pagePlatformMarketingInvite(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformMarketingInviteQueryDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pagePlatformMarketingInvite(headers, pageVO));
    }

    /**
     * “订单服务 - 代客下单” - 查询角色为服务消费者的平台会员列表
     * @param headers Http头部信息
     * @param nameVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/order/agent/members")
    public WrapperResp<List<MemberManageQueryResp>> findPlatformConsumerMembers(@RequestHeader HttpHeaders headers, @Valid MemberManageNameReq nameVO) {
        return WrapperUtil.success(memberManageService.findPlatformConsumerMembers(headers, nameVO));
    }

    /**
     * “订单服务 - 新增现货采购订单” - 查询会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/order/buyer/members")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageBuyerOrderMembers(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageVO) {
        return WrapperUtil.success(memberManageService.pageBuyerOrderMembers(headers, pageVO));
    }

    /**
     * 根据会员名称，分页查询角色为服务消费者的上级会员列表
     * @param headers Http头部信息
     * @param pageByNameVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/upper/consumerMerchant/page")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> pageUpperConsumerMerchantMember(@RequestHeader HttpHeaders headers, @Valid MemberManagePageByNameDataReq pageByNameVO) {
        return WrapperUtil.success(memberManageService.pageUpperConsumerMerchantMember(headers, pageByNameVO));
    }

    /**
     * 选择采购会员 功能
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 采购会员信息
     */
    @GetMapping("/buyerMember")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> buyerMemberInfo(@RequestHeader HttpHeaders headers, @Valid MemberNameDataReq nameVO) {
        return WrapperUtil.success(memberManageService.buyerMemberInfo(headers, nameVO));
    }

    /**
     * 选择采购会员 功能 - 用于配置流程引擎
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 采购会员信息
     */
    @GetMapping("/processBuyerMember")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> processBuyerMember(@RequestHeader HttpHeaders headers, @Valid MemberNameDataReq nameVO) {
        return WrapperUtil.success(memberManageService.processBuyerMember(headers, nameVO));
    }

    /**
     * 选择供应会员 功能
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 供应会员信息
     */
    @GetMapping("/supplyMember")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> supplyMemberInfo(@RequestHeader HttpHeaders headers, @Valid MemberNameDataReq nameVO) {
        return WrapperUtil.success(memberManageService.supplyMemberInfo(headers, nameVO));
    }

    /**
     * 选择客户功能 - 用于配置流程引擎
     * @param headers Http头部信息
     * @param nameVO  接口参数
     * @return 采购会员信息
     */
    @GetMapping("/customerList")
    public WrapperResp<PageDataResp<MemberManageQueryResp>> customerList(@RequestHeader HttpHeaders headers, @Valid MemberNameDataReq nameVO) {
        return WrapperUtil.success(memberManageService.customerList(headers, nameVO));
    }
}
