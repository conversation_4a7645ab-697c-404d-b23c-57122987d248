package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityAssignedMemberQueryDataReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberAssignedPageQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberAssignedSearchConditionVO;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员能力-会员管理-待分配会员相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-29
 **/
public interface IMemberAbilityAssignedService {
    /**
     * 获取分页查询会员列表页面中各个查询条件下拉选择框的内容
     *
     * @param headers HttpHeaders信息
     * @param loginUser 登录用户
     * @param roleTag 角色标签
     * @return 操作结果
     */
    MemberAssignedSearchConditionVO getPageCondition(HttpHeaders headers, UserLoginCacheDTO loginUser, Integer roleTag);

    /**
     * 分页、模糊查询会员
     *
     * @param loginUser 登录用户
     * @param queryVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAssignedPageQueryResp> pageMembers(UserLoginCacheDTO loginUser, MemberAbilityAssignedMemberQueryDataReq queryVO, Integer roleTag);

    /**
     * 领取会员
     *
     * @param loginUser     登录用户信息
     * @param subMemberList 接口参数
     * @return 操作结果
     */
    void bindOperator(UserLoginCacheDTO loginUser, List<SubMemberIdRoleIdDataReq> subMemberList);
}
