package com.ssy.lingxi.member.repository;


import com.ssy.lingxi.member.entity.do_.basic.MemberRoleRuleDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 平台后台 - 会员适用角色操作JpaRepository
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-11
 */
@Repository
public interface RoleRuleRepository extends JpaRepository<MemberRoleRuleDO, Long>, JpaSpecificationExecutor<MemberRoleRuleDO> {
    MemberRoleRuleDO findByMemberId(Long memberId);
    Page<MemberRoleRuleDO> findAllByMemberNameLike(String name, Pageable page);
    Page<MemberRoleRuleDO> findAllByMemberId(Long memberId, Pageable page);
}
