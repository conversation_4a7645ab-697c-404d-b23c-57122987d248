package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberProcessRuleDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员管理流程规则配置Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-14
 */
@Repository
public interface MemberProcessRuleRepository extends JpaRepository<MemberProcessRuleDO, Long>, JpaSpecificationExecutor<MemberProcessRuleDO> {

    boolean existsByMemberIdAndRoleIdAndSubRole(Long memberId, Long roleId, MemberRoleDO subRole);

    boolean existsByMemberIdAndRoleIdAndSubRoleAndIdNot(Long memberId, Long roleId, MemberRoleDO subRole, Long id);

    MemberProcessRuleDO findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(Long memberId, Long roleId, MemberRoleDO subRole, Integer status);

    boolean existsByMemberIdAndRoleIdAndSubRoleInAndBaseDepositoryProcessIdGreaterThanAndConfigsIsNotNull(Long memberId, Long roleId, List<MemberRoleDO> subRoles, Long processId);

    MemberProcessRuleDO findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(Long memberId, Long roleId, MemberRoleDO subRole, Integer status, Sort sort);
}
