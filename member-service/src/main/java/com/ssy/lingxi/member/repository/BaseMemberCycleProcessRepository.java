package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.BaseMemberCycleProcessDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

/**
 * 会员生命周期基础流程
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
public interface BaseMemberCycleProcessRepository extends JpaRepository<BaseMemberCycleProcessDO, Long>, JpaSpecificationExecutor<BaseMemberCycleProcessDO> {

    Optional<BaseMemberCycleProcessDO> findByProcessKeyAndProcessType(String processKey, Integer processType);

}
