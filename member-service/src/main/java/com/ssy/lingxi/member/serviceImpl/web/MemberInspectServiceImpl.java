package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.inspect.MemberInspectDO;
import com.ssy.lingxi.member.enums.MemberInspectSourceEnum;
import com.ssy.lingxi.member.enums.MemberInspectTypeEnum;
import com.ssy.lingxi.member.enums.MemberOuterStatusEnum;
import com.ssy.lingxi.member.model.req.basic.NamePageDataReq;
import com.ssy.lingxi.member.model.req.basic.UserPageDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.MemberInspectAddReq;
import com.ssy.lingxi.member.model.req.lifecycle.MemberInspectPageDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.MemberInspectUpdateReq;
import com.ssy.lingxi.member.model.resp.basic.UserQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberInspectPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberInspectResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordInspectPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.SubMemberQueryResp;
import com.ssy.lingxi.member.repository.MemberInspectRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.MemberRoleRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseUserDetailService;
import com.ssy.lingxi.member.service.web.IMemberInspectService;
import com.ssy.lingxi.member.util.FileObjectUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员考察服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Service
public class MemberInspectServiceImpl implements IMemberInspectService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberInspectRepository memberInspectRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private IBaseUserDetailService baseUserDetailService;

    @Resource
    private MemberRoleRepository memberRoleRepository;


    /**
     * 获取“考察类型”下拉框条件
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public List<DropdownItemResp> getPageConditions(HttpHeaders headers) {
        return MemberInspectTypeEnum.toDropdownItems();
    }

    /**
     * “新增会员审查” - 选择下级会员
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<SubMemberQueryResp> pageSubMembers(HttpHeaders headers, NamePageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), roleTag));
            }

            // 已入库会员
            List<Integer> outerStatusList = MemberOuterStatusEnum.depositOuterStatus().stream().map(MemberOuterStatusEnum::getCode).collect(Collectors.toList());
            list.add(criteriaBuilder.and(root.get("outerStatus").as(Integer.class).in(outerStatusList)));
            if(StringUtils.hasLength(pageVO.getName())) {
                Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.stream().map(p -> {
            SubMemberQueryResp queryVO = new SubMemberQueryResp();
            queryVO.setSubMemberId(p.getSubMemberId());
            queryVO.setSubRoleId(p.getSubRoleId());
            queryVO.setName(p.getSubMember().getName());
            queryVO.setRoleName(p.getSubRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getSubRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            queryVO.setDepositTime(p.getDepositTime() == null ? "" : p.getDepositTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setLifecycleStagesName(Objects.isNull(p.getMemberLifecycleStages()) ? "" : p.getMemberLifecycleStages().getLifecycleStagesName());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “新增或修改会员审查” - 选择用户
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<UserQueryResp> pageUsers(HttpHeaders headers, UserPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseUserDetailService.pageUsers(loginUser.getMemberId(), pageVO.getName(), pageVO.getOrgName(), pageVO.getJobTitle(), pageVO.getCurrent(), pageVO.getPageSize(), true);
    }

    /**
     * 分页查询会员考察
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberInspectPageQueryResp> pageMemberInspect(HttpHeaders headers, MemberInspectPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());

        List<Long> roleIds = new ArrayList<>();
        if (NumberUtil.notNullOrZero(roleTag)) {
            roleIds = memberRoleRepository.findAll((root, query, cb) -> {
                List<Predicate> predicateList = new ArrayList<>();
                predicateList.add(cb.equal(root.get("roleTag").as(Integer.class), roleTag));
                Predicate[] p = new Predicate[predicateList.size()];
                return cb.and(predicateList.toArray(p));
            }).stream().map(MemberRoleDO::getId).distinct().collect(Collectors.toList());
        }

        List<Long> finalRoleIds = roleIds;
        Page<MemberInspectDO> pageList = memberInspectRepository.findAll((Specification<MemberInspectDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("memberId"), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("roleId"), loginUser.getMemberRoleId()));
            if (NumberUtil.notNullOrZero(pageVO.getInspectType())) {
                predicateList.add(cb.equal(root.get("inspectType").as(Integer.class), pageVO.getInspectType()));
            }
            if (StringUtils.hasLength(pageVO.getSubject())) {
                predicateList.add(cb.like(root.get("subject").as(String.class), "%" + pageVO.getSubject().trim() + "%"));
            }

            if (NumberUtil.notNullOrZero(roleTag)) {
                predicateList.add(cb.in(root.get("subRoleId")).value(finalRoleIds));
            }

            if (StringUtils.hasLength(pageVO.getName())) {
                Join<MemberInspectDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                predicateList.add(cb.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName() + "%"));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        }, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberInspectDO -> {
            MemberInspectPageQueryResp queryVO = new MemberInspectPageQueryResp();
            queryVO.setId(memberInspectDO.getId());
            queryVO.setName(memberInspectDO.getSubMember().getName());
            queryVO.setSubject(memberInspectDO.getSubject());
            queryVO.setInspectType(memberInspectDO.getInspectType());
            queryVO.setInspectTypeName(MemberInspectTypeEnum.getMsgByCode(memberInspectDO.getInspectType()));
            queryVO.setInspectTime(DateTimeUtil.formatDate(memberInspectDO.getInspectTime()));
            //保留两位小数，小数部分是0的话保留整数，小数第一位不是0的话则保留一位
            queryVO.setScore(MemberConstant.BIG_DECIMAL_FORMAT.format(memberInspectDO.getScore()));
            queryVO.setResult(memberInspectDO.getResult());
            queryVO.setUpdateOrDel(memberInspectDO.getSource().equals(MemberInspectSourceEnum.BY_MEMBER_ABILITY.getCode()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员信息 - 会员详情 - 分页查询会员考察
     *
     * @param memberId  上级会员Id
     * @param roleId    上级会员角色Id
     * @param subMember 下级会员
     * @param subRoleId 下级会员角色Id
     * @param current   当前页
     * @param pageSize  每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberRecordInspectPageQueryResp> pageMemberInspect(Long memberId, Long roleId, MemberDO subMember, Long subRoleId, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("createTime").descending());
        Page<MemberInspectDO> pageList = memberInspectRepository.findAll((Specification<MemberInspectDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("memberId").as(Long.class), memberId));
            predicateList.add(cb.equal(root.get("roleId").as(Long.class), roleId));
            predicateList.add(cb.equal(root.get("subMember").as(MemberDO.class), subMember));
            predicateList.add(cb.equal(root.get("subRoleId").as(Long.class), subRoleId));
            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        }, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberInspectDO -> {
            MemberRecordInspectPageQueryResp queryVO = new MemberRecordInspectPageQueryResp();
            queryVO.setSubject(memberInspectDO.getSubject());
            queryVO.setInspectType(memberInspectDO.getInspectType());
            queryVO.setInspectTypeName(MemberInspectTypeEnum.getMsgByCode(memberInspectDO.getInspectType()));
            queryVO.setInspectTime(DateTimeUtil.formatDate(memberInspectDO.getInspectTime()));
            //保留两位小数，小数部分是0的话保留整数，小数第一位不是0的话则保留一位
            queryVO.setScore(memberInspectDO.getScore() == null ? "" : MemberConstant.BIG_DECIMAL_FORMAT.format(memberInspectDO.getScore()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 新增会员考察
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @param roleTag 角色标签
     * @return 新增结果
     */
    @Override
    public void addMemberInspect(HttpHeaders headers, MemberInspectAddReq addVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), addVO.getSubMemberId(), addVO.getSubRoleId());
        if(relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberInspectDO memberInspectDO = new MemberInspectDO();
        // 系统用户和编辑用户的差异保存
        if (!NumberUtil.isNullOrZero(addVO.getUserId())) {
            UserDO userDO = userRepository.findById(addVO.getUserId()).orElse(null);
            if (userDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }

            memberInspectDO.setByUser(userDO);
        } else {
            memberInspectDO.setByUserEditName(addVO.getUserEditName());
        }

        memberInspectDO.setCreateTime(LocalDateTime.now());
        memberInspectDO.setInspectTime(DateTimeUtil.parseConcatDateTime(addVO.getInspectDay()));
        memberInspectDO.setSource(MemberInspectSourceEnum.BY_MEMBER_ABILITY.getCode());
        memberInspectDO.setMemberId(loginUser.getMemberId());
        memberInspectDO.setRoleId(loginUser.getMemberRoleId());
        memberInspectDO.setSubMember(relationDO.getSubMember());
        memberInspectDO.setSubRoleId(relationDO.getSubRoleId());
        memberInspectDO.setSubject(addVO.getSubject().trim());
        memberInspectDO.setInspectType(addVO.getInspectType());
        memberInspectDO.setReason(StringUtils.hasText(addVO.getReason()) ? addVO.getReason().trim() : "");
        memberInspectDO.setAttachments(FileObjectUtil.toBOList(addVO.getAttachments()));
        memberInspectDO.setScore(addVO.getScore());
        memberInspectDO.setResult(addVO.getResult());
        memberInspectDO.setReports(FileObjectUtil.toBOList(addVO.getReports()));

        memberInspectRepository.saveAndFlush(memberInspectDO);

    }

    /**
     * 查询会员考察详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberInspectResp getMemberInspect(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberInspectDO memberInspectDO = memberInspectRepository.findById(idVO.getId()).orElse(null);
        if (memberInspectDO == null || !memberInspectDO.getMemberId().equals(loginUser.getMemberId()) || !memberInspectDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INSPECTION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag)) {
            MemberRoleDO memberRoleDO = memberRoleRepository.findById(memberInspectDO.getSubRoleId()).orElse(null);
            if (memberRoleDO == null || memberRoleDO.getRoleTag() == null || !roleTag.equals(memberRoleDO.getRoleTag())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INSPECTION_DOES_NOT_EXIST);
            }
        }

        MemberInspectResp memberInspectResp = new MemberInspectResp();
        memberInspectResp.setId(memberInspectDO.getId());
        memberInspectResp.setSubMemberId(memberInspectDO.getSubMember().getId());
        memberInspectResp.setSubRoleId(memberInspectDO.getSubRoleId());
        memberInspectResp.setName(memberInspectDO.getSubMember().getName());
        memberInspectResp.setSubject(memberInspectDO.getSubject());
        memberInspectResp.setInspectType(memberInspectDO.getInspectType());
        memberInspectResp.setInspectTypeName(MemberInspectTypeEnum.getMsgByCode(memberInspectDO.getInspectType()));
        memberInspectResp.setInspectDay(DateTimeUtil.formatDate(memberInspectDO.getInspectTime()));
        memberInspectResp.setUserId(memberInspectDO.getByUser() == null ? 0L : memberInspectDO.getByUser().getId());
        memberInspectResp.setUserName(memberInspectDO.getByUser() == null ? "" : memberInspectDO.getByUser().getName());
        memberInspectResp.setReason(memberInspectDO.getReason());
        memberInspectResp.setAttachments(FileObjectUtil.toVOList(memberInspectDO.getAttachments()));
        memberInspectResp.setScore(MemberConstant.BIG_DECIMAL_FORMAT.format(memberInspectDO.getScore()));
        memberInspectResp.setResult(memberInspectDO.getResult());
        memberInspectResp.setReports(FileObjectUtil.toVOList(memberInspectDO.getReports()));

        return memberInspectResp;
    }

    /**
     * 修改会员考察
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @param roleTag 角色标签
     * @return 修改结果
     */
    @Override
    public void updateMemberInspect(HttpHeaders headers, MemberInspectUpdateReq updateVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberInspectDO memberInspectDO = memberInspectRepository.findById(updateVO.getId()).orElse(null);
        if (Objects.isNull(memberInspectDO) || !memberInspectDO.getMemberId().equals(loginUser.getMemberId()) || !memberInspectDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INSPECTION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag)) {
            MemberRoleDO memberRoleDO = memberRoleRepository.findById(memberInspectDO.getSubRoleId()).orElse(null);
            if (memberRoleDO == null || memberRoleDO.getRoleTag() == null || !roleTag.equals(memberRoleDO.getRoleTag())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INSPECTION_DOES_NOT_EXIST);
            }
        }

        // 会员审核相关流程生成的数据不予许修改
        if(!memberInspectDO.getSource().equals(MemberInspectSourceEnum.BY_MEMBER_ABILITY.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INSPECTION_CAN_NOT_UPDATE);
        }

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), updateVO.getSubMemberId(), updateVO.getSubRoleId());
        if(relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        // 系统用户和编辑用户的差异保存
        if (!NumberUtil.isNullOrZero(updateVO.getUserId())) {
            UserDO userDO = userRepository.findById(updateVO.getUserId()).orElse(null);
            if (userDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }

            memberInspectDO.setByUser(userDO);
            memberInspectDO.setByUserEditName(null);
        } else {
            memberInspectDO.setByUser(null);
            memberInspectDO.setByUserEditName(updateVO.getUserEditName());
        }

        memberInspectDO.setCreateTime(LocalDateTime.now());
        memberInspectDO.setInspectTime(DateTimeUtil.parseConcatDateTime(updateVO.getInspectDay()));
        memberInspectDO.setSubMember(relationDO.getSubMember());
        memberInspectDO.setSubRoleId(relationDO.getSubRoleId());
        memberInspectDO.setSubject(updateVO.getSubject().trim());
        memberInspectDO.setInspectType(updateVO.getInspectType());
        memberInspectDO.setReason(StringUtils.hasText(updateVO.getReason()) ? updateVO.getReason().trim() : "");
        memberInspectDO.setAttachments(FileObjectUtil.toBOList(updateVO.getAttachments()));
        memberInspectDO.setScore(updateVO.getScore());
        memberInspectDO.setResult(updateVO.getResult());
        memberInspectDO.setReports(FileObjectUtil.toBOList(updateVO.getReports()));

        memberInspectRepository.saveAndFlush(memberInspectDO);

    }

    /**
     * 删除会员考察
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 删除结果
     */
    @Override
    public void deleteMemberInspect(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberInspectDO memberInspectDO = memberInspectRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberInspectDO) || !memberInspectDO.getMemberId().equals(loginUser.getMemberId()) || !memberInspectDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INSPECTION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag)) {
            MemberRoleDO memberRoleDO = memberRoleRepository.findById(memberInspectDO.getSubRoleId()).orElse(null);
            if (memberRoleDO == null || memberRoleDO.getRoleTag() == null || !roleTag.equals(memberRoleDO.getRoleTag())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INSPECTION_DOES_NOT_EXIST);
            }
        }

        // 会员审核相关流程生成的数据不予许删除
        if(!memberInspectDO.getSource().equals(MemberInspectSourceEnum.BY_MEMBER_ABILITY.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INSPECTION_CAN_NOT_DELETE);
        }

        memberInspectRepository.delete(memberInspectDO);

    }

}
