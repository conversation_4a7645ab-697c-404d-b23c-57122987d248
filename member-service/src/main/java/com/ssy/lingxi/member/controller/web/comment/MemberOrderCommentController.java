package com.ssy.lingxi.member.controller.web.comment;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.comment.WaitCommentOrderQueryDataReq;
import com.ssy.lingxi.member.model.resp.comment.WaitCommentOrderPageResp;
import com.ssy.lingxi.member.service.web.comment.IMemberOrderCommentService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 交易能力 - 会员订单评价
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/3
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/orderComment")
public class MemberOrderCommentController {

    @Resource
    private IMemberOrderCommentService memberOrderCommentService;

    /**
     * 待评价订单 - 订单类型
     * @return 查询结果
     */
    @GetMapping("/orderType/list")
    public WrapperResp<List<SelectItemResp>> listOrderType() {
        List<SelectItemResp> resultList = Stream.of(OrderTypeEnum.values()).map(e -> {
            SelectItemResp selectItemResp = new SelectItemResp();
            selectItemResp.setValue(e.getCode());
            selectItemResp.setName(e.getName());
            return selectItemResp;
        }).collect(Collectors.toList());

        return WrapperUtil.success(resultList);
    }

    /**
     * 采购会员 - 待评价订单
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/buyer/page")
    public WrapperResp<PageDataResp<WaitCommentOrderPageResp>> pageBuyerWaitCommentOrder(@RequestHeader HttpHeaders headers, @Valid WaitCommentOrderQueryDataReq pageVO) {
        return WrapperUtil.success(memberOrderCommentService.pageBuyerWaitCommentOrder(headers, pageVO));
    }

    /**
     * 供应会员 - 待评价订单
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/vendor/page")
    public WrapperResp<PageDataResp<WaitCommentOrderPageResp>> pageVendorWaitCommentOrder(@RequestHeader HttpHeaders headers, @Valid WaitCommentOrderQueryDataReq pageVO) {
        return WrapperUtil.success(memberOrderCommentService.pageVendorWaitCommentOrder(headers, pageVO));
    }
}
