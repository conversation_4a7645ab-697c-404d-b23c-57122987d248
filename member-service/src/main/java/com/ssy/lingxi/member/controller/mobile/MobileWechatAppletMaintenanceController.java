package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.dataauth.annotation.member.MemberAuth;
import com.ssy.lingxi.member.model.req.maintenance.MemberEliminateOrBlacklistReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberFreezeStatusReq;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityMaintenanceMemberQueryDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberMaintenanceDetailResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberMaintenancePageQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberMaintenanceSearchConditionResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilityMaintenanceService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 供应商能力-供应商管理-供应商信息相关接口 (App)
 * <AUTHOR>
 * @since 2022/5/24 17:41
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/maintenance")
public class MobileWechatAppletMaintenanceController {

    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IMemberAbilityMaintenanceService memberAbilityMaintenanceService;

    /**
     * 列表查询页面中各个下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/pageitems")
    public WrapperResp<MemberMaintenanceSearchConditionResp> getPageCondition(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityMaintenanceService.getPageCondition(headers, loginUser, roleTag));
    }

    /**
     * 分页、模糊查询供应商
     * @param headers Http头部信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @MemberAuth
    @PostMapping("/page")
    public WrapperResp<PageDataResp<MemberMaintenancePageQueryResp>> pageMembers(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityMaintenanceMemberQueryDataReq queryVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMembers(headers, queryVO, loginUser, roleTag));
    }

    /**
     * 会员处理页面 - 会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail")
    public WrapperResp<MemberMaintenanceDetailResp> getMemberBasicDetail(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityMaintenanceService.getSubMemberDetail(loginUser, idVO, roleTag));
    }

    /**
     * 供应商处理 - 供应商冻结、解冻
     * @param headers Http头部信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/freeze")
    public WrapperResp<Void> updateMemberRelationStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberFreezeStatusReq statusVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityMaintenanceService.updateMemberRelationFreezeStatus(loginUser, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 供应商处理 - 供应商解除关系
     * @param headers HttpHeaders信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @PostMapping("/eliminate")
    public WrapperResp<Void> updateMemberEliminatedStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberEliminateOrBlacklistReq vo) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityMaintenanceService.updateMemberEliminatedStatus(loginUser, vo);
        return WrapperUtil.success();
    }

    /**
     * 供应商处理 - 供应商黑名单
     * @param headers HttpHeaders信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @PostMapping("/blacklist")
    public WrapperResp<Void> updateMemberBlacklistStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberEliminateOrBlacklistReq vo) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityMaintenanceService.updateMemberBlacklistStatus(loginUser, vo);
        return WrapperUtil.success();
    }
    
}
