package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.model.req.maintenance.MemberDetailCreditHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberDetailService;
import com.ssy.lingxi.member.service.web.IPlatformMemberDetailCreditService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 平台后台 - 会员维护 - 会员详情 - 信用信息服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
@Service
public class PlatformMemberDetailCreditServiceImpl implements IPlatformMemberDetailCreditService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberDetailService baseMemberDetailService;

    /**
     * 查询会员详情 - 会员信用基本信息（平台会员信息）
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberDetailCreditResp getMemberDetailCredit(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getMemberDetailCredit(relationDO);
    }

    /**
     * 会员详情 - 会员信用 - 交易评价汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getAllMemberDetailCreditTradeCommentSummary(relationDO.getSubMemberId(), relationDO.getSubRoleId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageAllMemberDetailCreditTradeCommentHistory(relationDO.getSubMemberId(), relationDO.getSubRoleId(), pageVO.getStarLevel(), pageVO.getCurrent(), pageVO.getPageSize());
    }


    /**
     * 会员详情 - 会员信用 - 售后评价汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getAllMemberDetailCreditAfterSaleCommentSummary(relationDO.getSubMemberId(), relationDO.getSubRoleId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageAllMemberDetailCreditAfterSaleCommentHistory(relationDO.getSubMemberId(), relationDO.getSubRoleId(), pageVO.getStarLevel(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.getAllMemberDetailCreditComplainSummary(relationDO.getSubMemberId(), relationDO.getSubRoleId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberDetailCreditComplainHistoryResp> pageMemberDetailCreditComplainHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return baseMemberDetailService.pageAllMemberDetailCreditComplainHistory(relationDO.getSubMemberId(), relationDO.getSubRoleId(), pageVO.getCurrent(), pageVO.getPageSize());
    }
}
