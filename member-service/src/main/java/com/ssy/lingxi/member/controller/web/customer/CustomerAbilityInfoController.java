package com.ssy.lingxi.member.controller.web.customer;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.basic.UpperMemberIdRoleIdReq;
import com.ssy.lingxi.member.model.req.info.*;
import com.ssy.lingxi.member.model.req.maintenance.MemberDetailCreditHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.UpperMemberShowResp;
import com.ssy.lingxi.member.model.resp.info.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordRectifyResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;
import com.ssy.lingxi.member.service.web.IMemberAbilityInfoService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 客户能力 - 会员信息查询相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-07
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/customer/ability/info")
public class CustomerAbilityInfoController {
    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.CUSTOMER.getCode();

    @Resource
    private IMemberAbilityInfoService memberAbilityInfoService;

    /**
     * 获取分页查询页面下拉框内容
     * @param headers Http头部信息
     * @return 下拉框内容
     */
    @GetMapping("/pageitems")
    public WrapperResp<MemberInfoSearchConditionResp> getPageSearchConditions(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityInfoService.getPageSearchConditions(headers));
    }

    /**
     * 分页、模糊查询归属会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<UpperMemberInfoResp>> pageUpperMembers(@RequestHeader HttpHeaders headers, @Valid MemberInfoPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityInfoService.pageUpperMembers(headers, pageVO, roleTag));
    }

    /**
     * “申请会员”页面，查询按钮状态和文本
     * @param headers Http头部信息
     * @param conditionVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/apply/condition")
    public WrapperResp<MemberInfoApplyButtonResp> getApplyCondition(@RequestHeader HttpHeaders headers, @Valid MemberApplyConditionReq conditionVO) {
        return WrapperUtil.success(memberAbilityInfoService.getApplyCondition(headers, conditionVO));
    }

    /**
     * “邀请会员”页面，查询按钮状态和文本
     * @param headers Http头部信息
     * @param conditionVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/invite/condition")
    public WrapperResp<MemberInfoInviteButtonResp> getInviteCondition(@RequestHeader HttpHeaders headers, @Valid MemberInviteConditionDataReq conditionVO) {
        return WrapperUtil.success(memberAbilityInfoService.getInviteCondition(headers, conditionVO));
    }

    /**
     * “申请会员”页面，会员注册资料信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/apply/detail")
    public WrapperResp<MemberInfoApplyRegisterDetailResp> getApplyRegisterDetail(@RequestHeader HttpHeaders headers, @Valid UpperMemberIdRoleIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getApplyRegisterDetail(headers, idVO));
    }

    /**
     * “申请会员”页面，会员入库资料信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/apply/deposit/detail")
    public WrapperResp<MemberInfoApplyDepositDetailResp> getApplyDepositDetail(@RequestHeader HttpHeaders headers, @Valid UpperMemberIdRoleIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getApplyDepositDetail(headers, idVO));
    }

    /**
     * “申请会员” - 提交
     * @param headers Http头部信息
     * @param subVO 接口参数
     * @return 申请结果
     */
    @PostMapping("/apply")
    public WrapperResp<Void> applyToBeSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberInfoApplyForSubReq subVO) {
        memberAbilityInfoService.applyToBeSubMember(headers, subVO);
        return WrapperUtil.success();
    }

    /**
     * 获取“修改注册信息”页面，会员注册资料信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail")
    public WrapperResp<MemberInfoUpdateDetailResp> getMemberRegisterDetail(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberRegisterDetail(headers, idVO));
    }

    /**
     * 修改注册信息
     * @param headers Http头部信息
     * @param detailVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/detail/update")
    public WrapperResp<Void> updateMemberRegisterDetail(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberInfoUpdateRegisterDetailReq detailVO) {
        memberAbilityInfoService.updateMemberRegisterDetail(headers, detailVO);
        return WrapperUtil.success();
    }

    /**
     * 获取“修改入库信息”页面，会员入库资料信息
     * @param headers Http头部信息
     * @param idVO 会员关系Id
     * @return 查询结果
     */
    @GetMapping("/deposit/detail")
    public WrapperResp<MemberInfoUpdateDepositDetailResp> getMemberDepositDetail(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberDepositDetail(headers, idVO, roleTag));
    }

    /**
     * 修改会员入库信息
     * @param headers Http头部信息
     * @param detailVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/deposit/detail/update")
    public WrapperResp<Void> updateMemberDepositDetail(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberInfoUpdateDepositDetailReq detailVO) {
        memberAbilityInfoService.updateMemberDepositDetail(headers, detailVO, null);
        return WrapperUtil.success();
    }

    /**
     * 会员详情 - 会员基本信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/basic")
    public WrapperResp<MemberInfoBasicDetailResp> getMemberBasicDetail(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberBasicDetail(headers, idVO, roleTag));
    }

    /**
     * 会员详情 - 会员档案信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/archives")
    public WrapperResp<MemberInfoDepositDetailResp> getMemberArchives(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberArchives(headers, idVO));
    }

    /**
     * 会员详情- 会员档案 - 分页查询考评信息
     * @param headers HttpHeader信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/appraisal/page")
    public WrapperResp<PageDataResp<MemberAppraisalPageQueryResp>> pageMemberAppraisal(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityInfoService.pageMemberAppraisal(headers, pageVO));
    }

    /**
     * 会员详情 - 会员档案 - 分页查询会员整改
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/rectify/page")
    public WrapperResp<PageDataResp<MemberRecordRectifyResp>> pageMemberRecordRectify(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityInfoService.pageMemberRecordRectify(headers, pageVO));
    }

    /**
     * 会员详情 - 会员等级信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/level/basic")
    public WrapperResp<MemberValidateDetailLevelResp> getMemberLevelDetail(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberDetailLevel(headers, idVO));
    }

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/level/history/page")
    public WrapperResp<PageDataResp<MemberDetailLevelHistoryResp>> pageMemberLevelDetailHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityInfoService.pageMemberLevelDetailHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员权益信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/right/basic")
    public WrapperResp<MemberDetailRightResp> getMemberDetailRight(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberDetailRight(headers, idVO));
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益获取记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/right/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightHistoryResp>> pageMemberDetailRightHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityInfoService.pageMemberDetailRightHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益使用记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/right/spend/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightSpendHistoryResp>> pageMemberDetailRightSpendHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityInfoService.pageMemberDetailRightSpendHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员信用信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/credit/basic")
    public WrapperResp<MemberDetailCreditResp> getMemberDetailCredit(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberDetailCredit(headers, idVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/credit/trade/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> getMemberDetailCreditTradeCommentSummary(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberDetailCreditTradeCommentSummary(headers, idVO));
    }

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/credit/trade/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditTradeHistoryResp>> pageMemberDetailCreditTradeCommentHistory(@RequestHeader HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityInfoService.pageMemberDetailCreditTradeCommentHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/credit/aftersale/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> getMemberDetailCreditAfterSaleCommentSummary(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberDetailCreditAfterSaleCommentSummary(headers, idVO));
    }

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/credit/aftersale/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditAfterSaleHistoryResp>> pageMemberDetailCreditAfterSaleCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberDetailCreditHistoryPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityInfoService.pageMemberDetailCreditAfterSaleCommentHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/credit/complain/summary")
    public WrapperResp<MemberDetailCreditComplainSummaryResp> getMemberDetailCreditComplainSummary(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberDetailCreditComplainSummary(headers, idVO));
    }

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/credit/complain/history/page")
    public WrapperResp<PageDataResp<MemberCreditComplaintPageQueryResp>> pageMemberDetailCreditComplainHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityInfoService.pageMemberDetailCreditComplainHistory(headers, pageVO));
    }

    /**
     * “增加会员角色”功能，查询上级会员列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/upperMemberInfo")
    public WrapperResp<UpperMemberShowResp> getUpperMemberInfo(@RequestHeader HttpHeaders headers){
        return WrapperUtil.success(memberAbilityInfoService.getUpperMemberInfo(headers));
    }

    /**
     * “增加会员角色”功能，查询会员类型列表
     * @param headers Http头部信息
     * @param memberId 会员id
     * @return 查询结果
     */
    @GetMapping("/membertype/list")
    public WrapperResp<List<MemberTypeAndNameResp>> getMemberTypeList(@RequestHeader HttpHeaders headers, Long memberId) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberTypeList(headers, memberId));
    }

    /**
     * “增加会员角色”功能，根据会员类型Id查询角色列表
     * @param headers Http头部信息
     * @param memberTypeReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/role/list")
    public WrapperResp<MemberInfoRoleListResp> getRoleListByMemberType(@RequestHeader HttpHeaders headers, @Valid MemberTypeReq memberTypeReq) {
        return WrapperUtil.success(memberAbilityInfoService.getRoleListByMemberType(headers, memberTypeReq));
    }

    /**
     * “增加会员角色”功能，获取员注册资料信息
     * @param headers Http头部信息
     * @param roleIdReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/byrole")
    public WrapperResp<MemberInfoUpdateDetailByRoleResp> getMemberRegisterDetailAfterAddRole(@RequestHeader HttpHeaders headers, @Valid RoleIdReq roleIdReq) {
        return WrapperUtil.success(memberAbilityInfoService.getMemberRegisterDetailAfterAddRole(headers, roleIdReq, roleTag));
    }

    /**
     * “增加会员角色”功能，提交注册资料并新增角色
     * @param headers Http头部信息
     * @param addRoleVO 接口参数
     * @return 新增结果
     */
    @RequestMapping("/addrole")
    public WrapperResp<Void> addMemberRole(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberInfoAddRoleReq addRoleVO) {
        memberAbilityInfoService.addMemberRole(headers, addRoleVO);
        return WrapperUtil.success();
    }
}
