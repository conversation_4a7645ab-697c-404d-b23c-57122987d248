package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserQueryResp;
import com.ssy.lingxi.member.service.web.IPlatformMemberUserService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 平台后台-权限管理-用户管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-29
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/manage/user")
public class PlatformMemberUserController {
    @Resource
    private IPlatformMemberUserService platformMemberUserService;

    /**
     * 新增会员用户
     * @param headers HttpHeaders信息
     * @param memberUserVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/add")
    public WrapperResp<Void> addMemberUser(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserAddReq memberUserVO) {
         platformMemberUserService.addMemberUser(headers, memberUserVO);
        return WrapperUtil.success();
    }

    /**
     * 查询用户信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberUserGetResp> getMemberUser(@RequestHeader HttpHeaders headers, @Valid MemberUserIdReq idVO) {
        return WrapperUtil.success(platformMemberUserService.getMemberUser(headers, idVO));
    }

    /**
     * 修改会员用户
     * @param headers HttpHeaders信息
     * @param userUpdateReq 接口参数
     * @return 操作结果
     */
    @RequestMapping("/update")
    public WrapperResp<Void> updateMemberUser(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserUpdateReq userUpdateReq) {
         platformMemberUserService.updateMemberUser(headers, userUpdateReq);
        return WrapperUtil.success();
    }

    /**
     * 删除会员用户
     * @param headers HttpHeaders信息
     * @param memberUserIdReq 接口参数
     * @return 操作结果
     */
    @RequestMapping("/delete")
    public WrapperResp<Void> deleteMemberUser(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserIdReq memberUserIdReq) {
         platformMemberUserService.deleteMemberUser(headers, memberUserIdReq);
        return WrapperUtil.success();
    }

    /**
     * 更改用户状态
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/updatestatus")
    public WrapperResp<Void> updateMemberUserStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserUpdateStatusReq statusVO) {
         platformMemberUserService.updateMemberUserStatus(headers, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 分页查询会员用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberUserQueryResp>> pageMemberUser(@RequestHeader HttpHeaders headers, @Valid PageQueryMemberUserDataReq pageVO) {
        return WrapperUtil.success(platformMemberUserService.pageMemberUser(headers, pageVO));
    }
}
