package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.comment.MemberGetLifecycleReq;
import com.ssy.lingxi.member.model.resp.comment.MemberLifecycleStagesResp;
import com.ssy.lingxi.member.service.web.IMemberLifecycleRuleService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员能力-系统管理-生命周期阶段相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-07-19
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/lifecycle/stages")
public class MemberLifecycleStagesController {

    @Resource
    private IMemberLifecycleRuleService memberLifecycleStagesService;

    /**
     * 查询指定会员、指定角色标签的生命周期阶段
     * @param headers 请求头部信息
     * @return 返回指定会员的生命周期阶段
     */
    @GetMapping("/getLifecycleStages")
    public WrapperResp<List<MemberLifecycleStagesResp>> getLifecycleStages(@RequestHeader HttpHeaders headers, @Valid MemberGetLifecycleReq memberGetLifecycleReq) {
        return WrapperUtil.success(memberLifecycleStagesService.getLifecycleStages(headers, memberGetLifecycleReq.getProcessType()));
    }
}
