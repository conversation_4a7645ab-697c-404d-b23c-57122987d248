package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.model.TokenContext;
import com.ssy.lingxi.member.entity.bo.login.LoginContext;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * token相关功能管理类
 *
 * <AUTHOR>
 * @version 3.0.0
 * @date 2023/12/16
 */
public interface IBaseTokenManageService {
    /**
     * token续期时进行相关检查
     */
    TokenContext checkRefreshToken(HttpHeaders headers);

    /**
     * 获取可用token，并维护tokenContext和token相关缓存数据
     */
    void distUsableToken(LoginContext loginContext);

    /**
     * 获取accessToken过期时间（单位毫秒）
     */
    long getAccessTokenExpireMillis();

    /**
     * 获取accessToken过期时间（单位秒）
     */
    long getAccessTokenExpireSeconds();

    /**
     * 获取refreshToken过期时间（单位毫秒）
     */
    long getRefreshTokenExpireMillis(SystemSourceEnum loginSource);

    /**
     * 获取refreshToken过期时间（单位秒）
     */
    long getRefreshTokenExpireSeconds(SystemSourceEnum loginSource);

    /**
     * 构造登录缓存对象实体
     */
    UserLoginCacheDTO buildUserLoginCacheDTO(LoginContext loginContext);

    /**
     * 根据accessToken，删除缓存信息
     */
    void deleteAccessToken(String accessTokenRedisKey);

    /**
     * 根据refreshToken，删除缓存信息
     */
    void deleteRefreshToken(String refreshTokenRedisKey);

    /**
     * 清空指定用户的token
     */
    void userOffline(List<Long> userIdList);

    /**
     * 清空指定会员的token
     */
    void memberOffline(List<Long> memberIdList);

    /**
     * 登出
     * @param headers HttpHeaders信息
     */
    void logOut(HttpHeaders headers);
}
