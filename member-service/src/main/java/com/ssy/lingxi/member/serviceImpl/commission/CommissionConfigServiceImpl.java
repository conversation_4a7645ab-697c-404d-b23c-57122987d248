package com.ssy.lingxi.member.serviceImpl.commission;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionConfigDO;
import com.ssy.lingxi.member.model.bo.CommissionConfigBO;
import com.ssy.lingxi.member.model.req.commission.InvitationConfigReq;
import com.ssy.lingxi.member.model.req.commission.OrderCommissionConfigReq;
import com.ssy.lingxi.member.model.req.commission.WithdrawalConfigReq;
import com.ssy.lingxi.member.model.resp.commission.CommissionConfigResp;
import com.ssy.lingxi.member.repository.commission.CommissionConfigRepository;
import com.ssy.lingxi.member.service.commission.ICommissionConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * 分佣配置服务实现
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Slf4j
@Service
public class CommissionConfigServiceImpl implements ICommissionConfigService {

    @Resource
    private CommissionConfigRepository commissionConfigRepository;


    @Override
    public CommissionConfigResp getCommissionConfig() {
        CommissionConfigDO configDO = getOrCreateConfig();
        return convertToResp(configDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void updateInvitationConfig(UserLoginCacheDTO loginUser, InvitationConfigReq request) {
        CommissionConfigDO configDO = getOrCreateConfig();
        CommissionConfigBO configBO = configDO.getConfigBO();

        // 更新金额
        if (request.getInvitationRegistrationReward() != null) {
            configBO.setInvitationRegistrationReward(request.getInvitationRegistrationReward());
        }
        if (request.getInvitationVerificationReward() != null) {
            configBO.setInvitationVerificationReward(request.getInvitationVerificationReward());
        }
        if (request.getInvitationFirstOrderReward() != null) {
            configBO.setInvitationFirstOrderReward(request.getInvitationFirstOrderReward());
        }

        // 更新启用状态
        configBO.setInvitationRegistrationRewardEnabled(request.getInvitationRegistrationRewardEnabled());
        configBO.setInvitationVerificationRewardEnabled(request.getInvitationVerificationRewardEnabled());
        configBO.setInvitationFirstOrderRewardEnabled(request.getInvitationFirstOrderRewardEnabled());

        configDO.setConfigBO(configBO);
        commissionConfigRepository.saveAndFlush(configDO);

        log.info("更新邀请分佣配置成功，操作人：{}", loginUser != null ? loginUser.getUserName() : "system");
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void updateOrderCommissionConfig(UserLoginCacheDTO loginUser, OrderCommissionConfigReq request) {
        CommissionConfigDO configDO = getOrCreateConfig();
        CommissionConfigBO configBO = configDO.getConfigBO();

        // 更新金额
        if (request.getCommissionPerGram() != null) {
            configBO.setCommissionPerGram(request.getCommissionPerGram());
        }

        // 更新启用状态
        configBO.setOrderCommissionEnabled(request.getOrderCommissionEnabled());

        configDO.setConfigBO(configBO);
        commissionConfigRepository.saveAndFlush(configDO);

        log.info("更新下单分佣配置成功，操作人：{}", loginUser != null ? loginUser.getUserName() : "system");
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void updateWithdrawalConfig(UserLoginCacheDTO loginUser, WithdrawalConfigReq request) {
        CommissionConfigDO configDO = getOrCreateConfig();
        CommissionConfigBO configBO = configDO.getConfigBO();

        // 更新金额
        if (request.getMinWithdrawalAmount() != null) {
            configBO.setMinWithdrawalAmount(request.getMinWithdrawalAmount());
        }

        // 更新启用状态
        configBO.setWithdrawalEnabled(request.getWithdrawalEnabled());

        configDO.setConfigBO(configBO);
        commissionConfigRepository.saveAndFlush(configDO);

        log.info("更新佣金提现配置成功，操作人：{}", loginUser != null ? loginUser.getUserName() : "system");
        return null;
    }


    // ==================== 私有方法 ====================

    private CommissionConfigDO getOrCreateConfig() {
        Optional<CommissionConfigDO> configOpt = commissionConfigRepository.findFirstBy();
        if (configOpt.isPresent()) {
            return configOpt.get();
        }

        // 创建默认配置
        CommissionConfigDO configDO = new CommissionConfigDO();
        configDO.setType(1);
        configDO.setStatus(1);

        CommissionConfigBO configBO = new CommissionConfigBO();
        configBO.setInvitationRegistrationReward(BigDecimal.valueOf(10));
        configBO.setInvitationVerificationReward(BigDecimal.valueOf(20));
        configBO.setInvitationFirstOrderReward(BigDecimal.valueOf(50));
        configBO.setCommissionPerGram(BigDecimal.valueOf(0.1));
        configBO.setMinWithdrawalAmount(BigDecimal.valueOf(100));
        configBO.setInvitationRegistrationRewardEnabled(false);
        configBO.setInvitationVerificationRewardEnabled(false);
        configBO.setInvitationFirstOrderRewardEnabled(false);
        configBO.setOrderCommissionEnabled(false);
        configBO.setWithdrawalEnabled(false);

        configDO.setConfigBO(configBO);
        return commissionConfigRepository.saveAndFlush(configDO);
    }

    private CommissionConfigResp convertToResp(CommissionConfigDO configDO) {
        CommissionConfigResp resp = new CommissionConfigResp();
        resp.setId(configDO.getId());

        if (configDO.getConfigBO() != null) {
            BeanUtils.copyProperties(configDO.getConfigBO(), resp);
        }

        return resp;
    }
}
