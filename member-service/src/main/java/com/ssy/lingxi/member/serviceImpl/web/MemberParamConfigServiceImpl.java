package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.controller.web.FaceRecognitionFlagReq;
import com.ssy.lingxi.member.service.web.IMemberParamConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 会员参数配置
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/13
 */
@Service
public class MemberParamConfigServiceImpl implements IMemberParamConfigService {

    @Resource
    private IRedisUtils redisUtils;

    /**
     * 查看是否支持人脸识别
     *
     * @return 查询结果
     */
    @Override
    public Boolean getFaceRecognitionFlag(UserLoginCacheDTO sysUser) {
        return redisUtils.stringGet(MemberRedisConstant.FACE_RECOGNITION_FLAG, Boolean.class, RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 添加修改否支持人脸识别
     *
     * @param req 接口参数
     */
    @Override
    public void saveOrUpdateFaceRecognitionFlag(UserLoginCacheDTO sysUser, FaceRecognitionFlagReq req) {
        redisUtils.stringSet(MemberRedisConstant.FACE_RECOGNITION_FLAG, req.getEnableFlag(), RedisConstant.REDIS_USER_INDEX);
    }

}
