package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.BaseMemberRuleDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 平台规则配置Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-16
 */
@Repository
public interface BaseMemberRuleRepository extends JpaRepository<BaseMemberRuleDO, Long>, JpaSpecificationExecutor<BaseMemberRuleDO> {
    List<BaseMemberRuleDO> findByRuleTypeAndStatus(Integer ruleType, Integer status);
}
