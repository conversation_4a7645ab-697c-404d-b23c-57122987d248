package com.ssy.lingxi.member.model.req.comment;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会员订单评价查询VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/3
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WaitCommentOrderQueryDataReq extends PageDataReq {
    private static final long serialVersionUID = 5971505694590209664L;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单摘要
     */
    private String digest;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 下单时间开始
     */
    private String createTimeStart;

    /**
     * 下单时间结束
     */
    private String createTimeEnd;

    /**
     * 订单类型
     */
    private Integer orderType;
}
