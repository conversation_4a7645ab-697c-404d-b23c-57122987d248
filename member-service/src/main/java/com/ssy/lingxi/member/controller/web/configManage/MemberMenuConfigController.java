package com.ssy.lingxi.member.controller.web.configManage;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.configManage.*;
import com.ssy.lingxi.member.model.req.platform.MenuSourceReq;
import com.ssy.lingxi.member.model.resp.configManage.MenuButtonResp;
import com.ssy.lingxi.member.model.resp.configManage.MenuConfigDetailsResp;
import com.ssy.lingxi.member.model.resp.configManage.MenuConfigResp;
import com.ssy.lingxi.member.service.configManage.IManageMenuService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 会员菜单配置相关接口
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/3
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/menuConfig")
public class MemberMenuConfigController {
    private final IManageMenuService manageMenuService;

    /**
     * 查询权限菜单列表
     *
     * @param menuSourceReq 接口参数
     * @return 菜单配置列表
     */
    @GetMapping("/getMenuConfigList")
    public WrapperResp<List<MenuConfigResp>> getMenuConfigList(@Valid MenuSourceReq menuSourceReq) {
        return WrapperUtil.success(manageMenuService.getMenuConfigList(menuSourceReq));
    }

    /**
     * 查询菜单详情
     *
     * @param commonIdReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/getMenuConfigDetails")
    public WrapperResp<MenuConfigDetailsResp> getMenuConfigDetails(@Valid CommonIdReq commonIdReq) {
        return WrapperUtil.success(manageMenuService.getMenuConfigDetails(commonIdReq));
    }

    /**
     * 新增菜单
     *
     * @param addMenuReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/addMenu")
    public WrapperResp<MenuButtonResp> addMenu(@RequestBody @Valid MenuButtonAddReq addMenuReq) {
        return WrapperUtil.success(manageMenuService.addMenu(addMenuReq));
    }

    /**
     * 修改菜单
     *
     * @param updateReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/updateMenu")
    public WrapperResp<MenuButtonResp> updateMenu(@RequestBody @Valid MenuButtonUpdateReq updateReq) {
        return WrapperUtil.success(manageMenuService.updateMenu(updateReq));
    }

    /**
     * 删除菜单
     *
     * @param commonIdReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/deleteMenu")
    public WrapperResp<Void> deleteMenu(@RequestBody @Valid CommonIdReq commonIdReq) {
         manageMenuService.deleteMenu(commonIdReq);
        return WrapperUtil.success();
    }

    /**
     * 新增按钮
     *
     * @param addButtonReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/addButton")
    public WrapperResp<MenuButtonResp> addButton(@RequestBody @Valid ButtonAddReq addButtonReq) {
        return WrapperUtil.success(manageMenuService.addButton(addButtonReq));
    }

    /**
     * 修改按钮
     *
     * @param updateReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/updateButton")
    public WrapperResp<MenuButtonResp> updateButton(@RequestBody @Valid ButtonUpdateReq updateReq) {
        return WrapperUtil.success(manageMenuService.updateButton(updateReq));
    }

    /**
     * 删除按钮
     *
     * @param commonIdReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/deleteButton")
    public WrapperResp<Void> deleteButton(@RequestBody @Valid CommonIdReq commonIdReq) {
         manageMenuService.deleteButton(commonIdReq);
        return WrapperUtil.success();
    }

    /**
     * 菜单重排序
     *
     * @param menuResortReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/menuResort")
    public WrapperResp<Void> menuResort(@RequestBody @Valid MenuResortReq menuResortReq) {
         manageMenuService.menuResort(menuResortReq);
        return WrapperUtil.success();
    }
}
