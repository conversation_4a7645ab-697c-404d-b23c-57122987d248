package com.ssy.lingxi.member.model.req.platform;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 平台后台会员生命周期分页查询条件
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@Data
@EqualsAndHashCode(callSuper=false)
public class PlatformMemberCycleProcessPageQueryDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = 408544462165089550L;

    /**
     * 流程规则名称
     */
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
