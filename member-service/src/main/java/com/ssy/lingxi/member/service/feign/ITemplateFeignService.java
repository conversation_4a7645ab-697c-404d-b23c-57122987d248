package com.ssy.lingxi.member.service.feign;

/**
 * 调用店铺模板服务Feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-09
 */
public interface ITemplateFeignService {
    /**
     * 当平台会员注册信息改变时，通知店铺模板服务
     * @param memberId 平台会员Id
     * @param roleId   会员角色Id
     * @param memberName 会员名称
     * @param registeredCapital 注册资本
     * @param establishmentDate 成立日期
     * @param businessLicence 营业执照
     * @param registerArea 注册区域
     * @param registerAddress 注册地址
     */
    void notifyRegisterDetailChangedAsync(Long memberId, Long roleId, String memberName, String registeredCapital, String establishmentDate, String businessLicence, String registerArea, String registerAddress);


    /**
     * 当平台会员信用信息改变时，通知店铺模板服务
     * @param memberId 平台会员Id
     * @param roleId   会员角色Id
     * @param memberName 会员名称
     * @param creditPoint 当前信用积分
     * @param registerYears 注册年数
     * @param avgTradeCommentStar 满意度（交易评论平均星级）
     */
    void notifyCreditChangedAsync(Long memberId, Long roleId, String memberName, Integer creditPoint, Integer registerYears, Integer avgTradeCommentStar);

    /**
     * 当平台会员被禁用/启用时，通知店铺模板服务
     * @param memberId 平台会员Id
     * @param roleId    会员角色Id
     * @param status   状态
     */
    void notifyMemberStatusChangedAsync(Long memberId, Long roleId, Integer status);

    /**
     * 当会员角色被禁用/启用时，通知店铺模板服务
     * @param roleId    会员角色Id
     * @param status   状态
     */
    void notifyRoleStatusChangedAsync(Long roleId, Integer status);
}
