package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.enums.MemberInnerStatusEnum;
import com.ssy.lingxi.member.enums.MemberOuterStatusEnum;
import com.ssy.lingxi.member.model.req.validate.MemberModifyPageDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberModifyReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdsReq;
import com.ssy.lingxi.member.model.resp.basic.InnerStatusResp;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.validate.MemberModifyDetailResp;
import com.ssy.lingxi.member.model.resp.validate.MemberModifyPageQueryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberModifySearchConditionResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberAbilityModifyService;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员资料变更审核相关接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-25
 */
@Service
public class MemberAbilityModifyServiceImpl implements IMemberAbilityModifyService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    @Resource
    private IBaseMemberQualificationService baseMemberQualificationService;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private IBaseMemberValidateService baseMemberValidateService;


    /**
     * 获取会员变更各个步骤分页查询列表页面下拉框
     *
     * @param loginUser   当前登录人
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberModifySearchConditionResp getModifyPageConditions(UserLoginCacheDTO loginUser, Integer roleTag) {
        //规则：
        // 1). 上级会员角色为企业会员，下级会员角色为企业会员：无限制
        // 2). 上级会员角色为企业会员，下级会员角色为渠道会员：不允许创建
        // 3). 上级会员角色为渠道会员，下级会员角色为企业会员：不允许创建
        // 4). 上级会员角色为渠道会员，下级会员角色为渠道会员：判断下级会员是否有另一个服务消费者角色在关系树中

        MemberModifySearchConditionResp conditionVO = new MemberModifySearchConditionResp();
        //会员类型（这里返回的是Id）
        List<MemberTypeAndNameResp> memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType());
        memberTypeList.add(0, new MemberTypeAndNameResp(0, "所有"));
        conditionVO.setMemberTypes(memberTypeList);

        //会员角色（按照Id升序排序）
        List<RoleIdAndNameResp> roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), roleTag);
        roleList.add(0, new RoleIdAndNameResp(0L, MemberStringEnum.ALL.getName()));
        conditionVO.setRoles(roleList);

        // 内部状态
        List<InnerStatusResp> innerStatus = Stream.of(MemberInnerStatusEnum.TO_MODIFY_GRADE_ONE, MemberInnerStatusEnum.TO_MODIFY_GRADE_TWO, MemberInnerStatusEnum.TO_CONFIRM_MODIFY).map(innerStatusEnum -> new InnerStatusResp(innerStatusEnum.getCode(), innerStatusEnum.getMessage().replace("会员",""))).collect(Collectors.toList());
        conditionVO.setInnerStatus(innerStatus);

        return conditionVO;
    }

    /**
     * 分页查询待审核变更供应商列表
     * @param pageVO 接口参数
     * @param loginUser 登录用户
     * @return MemberModifyPageQueryVO
     */
    @Override
    public PageDataResp<MemberModifyPageQueryResp> pageToModifyGrade(MemberModifyPageDataReq pageVO, UserLoginCacheDTO loginUser) {
        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {

            List<Predicate> orList = new ArrayList<>();

            // 前端传内部状态
            if (Objects.nonNull(pageVO.getInnerStatus())){
                List<MemberInnerStatusEnum> innerStatusEnums = new ArrayList<>();
                MemberInnerStatusEnum instance = MemberInnerStatusEnum.getInstance(pageVO.getInnerStatus());
                if (Objects.equals(instance, MemberInnerStatusEnum.TO_CONFIRM_MODIFY)){
                    innerStatusEnums = Stream.of(
                            MemberInnerStatusEnum.MODIFY_GRADE_ONE_NOT_PASSED,
                            MemberInnerStatusEnum.MODIFY_GRADE_TWO_NOT_PASSED,
                            MemberInnerStatusEnum.TO_CONFIRM_MODIFY
                    ).collect(Collectors.toList());
                } else {
                    innerStatusEnums.add(instance);
                }
                orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), loginUser.getRoleTag()));
                return criteriaBuilder.and(orList.toArray(new Predicate[0]));
            }

            // 待一级审核
            List<MemberInnerStatusEnum> innerStatusEnums = Stream.of(MemberInnerStatusEnum.TO_MODIFY_GRADE_ONE).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), loginUser.getRoleTag()));

            // 待二级审核
            innerStatusEnums = Stream.of(MemberInnerStatusEnum.TO_MODIFY_GRADE_TWO).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), loginUser.getRoleTag()));

            // 待确认变更
            innerStatusEnums = Stream.of(
                    MemberInnerStatusEnum.MODIFY_GRADE_ONE_NOT_PASSED,
                    MemberInnerStatusEnum.MODIFY_GRADE_TWO_NOT_PASSED,
                    MemberInnerStatusEnum.TO_CONFIRM_MODIFY).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), loginUser.getRoleTag()));

            return criteriaBuilder.or(orList.toArray(new Predicate[0]));
        };

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> this.createQueryVO(relationDO, loginUser.getRoleTag())).collect(Collectors.toList()));
    }

    /**
     * 分页查询“待审核变更（一级）”会员列表
     *
     * @param loginUser 当前登录人
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberModifyPageQueryResp> pageToModifyGradeOne(UserLoginCacheDTO loginUser, MemberModifyPageDataReq pageVO, Integer roleTag) {
        return pageModifyRelations(MemberInnerStatusEnum.TO_MODIFY_GRADE_ONE, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag);
    }

    /**
     * “待审核变更（一级）” - 查询会员详情
     * @param loginUser 当前登录人
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberModifyDetailResp getToModifyGradeOne(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        return getMemberModifyDetails(loginUser, idVO.getValidateId(), roleTag);
    }

    /**
     * “待审核变更（一级）” - 审核会员
     *
     * @param loginUser 当前登录人
     * @param roleTag 角色标签
     * @return 审核结果
     */
    @Override
    public void toModifyGradeOne(UserLoginCacheDTO loginUser, MemberModifyReq modifyVO, Integer roleTag) {
        baseMemberValidateService.execMemberProcess(loginUser, modifyVO.getValidateId(), modifyVO.getAgree(), modifyVO.getReason(), MemberInnerStatusEnum.TO_MODIFY_GRADE_ONE, roleTag);
    }

    /**
     * “待审核变更（一级）” - 批量审核
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void batchToModifyGradeOne(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberValidateService.batchExecMemberProcess(loginUser, idVO.getValidateIds(), MemberInnerStatusEnum.TO_MODIFY_GRADE_ONE, roleTag);
    }

    /**
     * 分页查询“待审核变更（二级）”会员列表
     *
     * @param loginUser 当前登录人
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberModifyPageQueryResp> pageToModifyGradeTwo(UserLoginCacheDTO loginUser, MemberModifyPageDataReq pageVO, Integer roleTag) {
        return pageModifyRelations(MemberInnerStatusEnum.TO_MODIFY_GRADE_TWO, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag);
    }

    /**
     * “待审核变更（二级）” - 查询会员详情
     *
     * @param loginUser 当前登录人
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberModifyDetailResp getToModifyGradeTwo(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        return getMemberModifyDetails(loginUser, idVO.getValidateId(), roleTag);
    }

    /**
     * “待审核变更（二级）” - 审核会员
     *
     * @param loginUser   Http头部信息
     * @param modifyVO 接口参数
     * @param roleTag 角色标签
     * @return 审核结果
     */
    @Override
    public void toModifyGradeTwo(UserLoginCacheDTO loginUser, MemberModifyReq modifyVO, Integer roleTag) {
         baseMemberValidateService.execMemberProcess(loginUser, modifyVO.getValidateId(), modifyVO.getAgree(), modifyVO.getReason(), MemberInnerStatusEnum.TO_MODIFY_GRADE_TWO, roleTag);
    }

    /**
     * “待审核变更（二级）” - 批量审核
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void batchToModifyGradeTwo(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         baseMemberValidateService.batchExecMemberProcess(loginUser, idVO.getValidateIds(), MemberInnerStatusEnum.TO_MODIFY_GRADE_TWO, roleTag);
    }

    /**
     * 分页查询“待确认会员变更”会员列表
     *
     * @param loginUser 当前登录人
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberModifyPageQueryResp> pageToConfirmModify(UserLoginCacheDTO loginUser, MemberModifyPageDataReq pageVO, Integer roleTag) {
        List<MemberInnerStatusEnum> innerStatusEnums = Stream.of(
                MemberInnerStatusEnum.MODIFY_GRADE_ONE_NOT_PASSED,
                MemberInnerStatusEnum.MODIFY_GRADE_TWO_NOT_PASSED,
                MemberInnerStatusEnum.TO_CONFIRM_MODIFY
        ).collect(Collectors.toList());
        return pageModifyRelations(innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag);
    }

    /**
     * “待确认会员变更” - 查询会员详情
     *
     * @param loginUser 当前登录人
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberModifyDetailResp getToConfirmModify(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        return getMemberModifyDetails(loginUser, idVO.getValidateId(), roleTag);
    }

    /**
     * “待确认会员变更” - 审核会员
     *
     * @param loginUser 当前登录人
     * @param modifyVO 接口参数
     * @param roleTag 角色标签
     * @return 审核结果
     */
    @Override
    public void toConfirmModify(UserLoginCacheDTO loginUser, MemberModifyReq modifyVO, Integer roleTag) {
        List<MemberInnerStatusEnum> innerStatusEnums = Stream.of(
                MemberInnerStatusEnum.MODIFY_GRADE_ONE_NOT_PASSED,
                MemberInnerStatusEnum.MODIFY_GRADE_TWO_NOT_PASSED,
                MemberInnerStatusEnum.TO_CONFIRM_MODIFY
        ).collect(Collectors.toList());
        baseMemberValidateService.execMemberProcess(loginUser, modifyVO.getValidateId(), modifyVO.getAgree(), modifyVO.getReason(), innerStatusEnums, roleTag);
    }

    /**
     * “待确认会员变更” - 批量审核
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void batchToConfirmModify(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<MemberInnerStatusEnum> innerStatusEnums = Stream.of(
                MemberInnerStatusEnum.MODIFY_GRADE_ONE_NOT_PASSED,
                MemberInnerStatusEnum.MODIFY_GRADE_TWO_NOT_PASSED,
                MemberInnerStatusEnum.TO_CONFIRM_MODIFY
        ).collect(Collectors.toList());
         baseMemberValidateService.batchExecMemberProcess(loginUser, idVO.getValidateIds(), innerStatusEnums, roleTag);
    }

    /**
     * （重载）分页查询资料变更会员列表
     *
     * @param innerStatusEnum 内部状态
     * @param memberId        当前会员Id
     * @param roleId          当前会员角色Id
     * @param subMemberName   下级会员名称
     * @param subMemberTypeId 下级会员会员类型
     * @param subRoleId       下级会员角色Id
     * @param current         分页参数 - 当前页
     * @param pageSize        分页参数 - 每页行数
     * @param roleTag         角色标签
     * @return 查询结果
     */
    private PageDataResp<MemberModifyPageQueryResp> pageModifyRelations(MemberInnerStatusEnum innerStatusEnum, Long memberId, Long roleId, String subMemberName, Long subMemberTypeId, Long subRoleId, int current, int pageSize, Integer roleTag) {
        return pageModifyRelations(Stream.of(innerStatusEnum).collect(Collectors.toList()), memberId, roleId, subMemberName, subMemberTypeId, subRoleId, current, pageSize, roleTag);
    }

    /**
     * 获取查询条件
     * @param root must not be {@literal null}.
     * @param query must not be {@literal null}.
     * @param criteriaBuilder must not be {@literal null}.
     * @param innerStatusEnums 内部状态List
     * @param memberId 当前会员Id
     * @param roleId   当前会员角色Id
     * @param subMemberName  下级会员名称
     * @param subMemberTypeId 下级会员会员类型
     * @param subRoleId  下级会员角色Id
     * @return Predicate
     */
    private Predicate getPredicate(Root<?> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, List<MemberInnerStatusEnum> innerStatusEnums, Long memberId, Long roleId, String subMemberName, Long subMemberTypeId, Long subRoleId, Integer roleTag){

        List<Predicate> cond = new ArrayList<>();
        cond.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), memberId));
        cond.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), roleId));
        cond.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));

        // 内部状态
        if(innerStatusEnums.size() == 1) {
            cond.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), innerStatusEnums.get(0).getCode()));
        } else {
            cond.add(root.get("innerStatus").in(innerStatusEnums.stream().map(MemberInnerStatusEnum::getCode).collect(Collectors.toList())));
        }

        // 下级会员名称
        if(StringUtils.hasLength(subMemberName)) {
            Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
            cond.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + subMemberName.trim() + "%"));
        }

        // 下级会员类型
        if(NumberUtil.notNullOrZero(subMemberTypeId)) {
            Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.LEFT);
            cond.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), subMemberTypeId));
            if (NumberUtil.notNullOrZero(roleTag)) {
                List<Integer> tags = new ArrayList<>();
                if (RoleTagEnum.getCustomer().contains(roleTag)) {
                    tags = RoleTagEnum.getSupplier();
                } else if (RoleTagEnum.getSupplier().contains(roleTag)) {
                    tags = RoleTagEnum.getCustomer();
                }
                cond.add(criteriaBuilder.in(subRoleJoin.get("roleTag")).value(tags));
            }
        }

        // 下级会员角色
        if(NumberUtil.notNullOrZero(subRoleId)) {
            cond.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), subRoleId));
        }

        return criteriaBuilder.and(cond.toArray(new Predicate[0]));
    }

    /**
     * 分页查询变更会员列表
     *
     * @param innerStatusEnums 内部状态List
     * @param memberId         当前会员Id
     * @param roleId           当前会员角色Id
     * @param subMemberName    下级会员名称
     * @param subMemberTypeId  下级会员会员类型
     * @param subRoleId        下级会员角色Id
     * @param current          分页参数 - 当前页
     * @param pageSize         分页参数 - 每页行数
     * @param roleTag          角色标签
     * @return 查询结果
     */
    private PageDataResp<MemberModifyPageQueryResp> pageModifyRelations(List<MemberInnerStatusEnum> innerStatusEnums, Long memberId, Long roleId, String subMemberName, Long subMemberTypeId, Long subRoleId, int current, int pageSize, Integer roleTag) {
        Pageable pageable = PageRequest.of(current - 1, pageSize, Sort.by("createTime").descending());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), memberId));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), roleId));
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            if (innerStatusEnums.size() == 1) {
                list.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), innerStatusEnums.get(0).getCode()));
            } else {
                Expression<Integer> exp = root.get("innerStatus");
                list.add(exp.in(innerStatusEnums.stream().map(MemberInnerStatusEnum::getCode).collect(Collectors.toList())));
            }

            //下级会员名称
            if (StringUtils.hasLength(subMemberName)) {
                Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + subMemberName.trim() + "%"));
            }

            // 下级会员类型
            if(NumberUtil.notNullOrZero(subMemberTypeId)) {
                Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), subMemberTypeId));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), roleTag));
            }

            //下级会员角色
            if (NumberUtil.notNullOrZero(subRoleId)) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), subRoleId));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> this.createQueryVO(relationDO, roleTag)).collect(Collectors.toList()));
    }

    /**
     * 创建查询展示类
     * @param relationDO 会员上下级关系
     * @return MemberModifyPageQueryVO
     */
    private MemberModifyPageQueryResp createQueryVO(MemberRelationDO relationDO, Integer roleTag){
        MemberModifyPageQueryResp queryVO = new MemberModifyPageQueryResp();
        queryVO.setValidateId(relationDO.getId());
        queryVO.setMemberId(relationDO.getSubMemberId());
        queryVO.setName(relationDO.getSubMember().getName());
        queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
        queryVO.setRoleName(relationDO.getSubRole().getRoleName());
        queryVO.setInnerStatus(relationDO.getInnerStatus());
        queryVO.setInnerStatusName(SecurityStringUtil.replaceMemberPrefix(MemberInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()), roleTag));
        queryVO.setOuterStatus(relationDO.getOuterStatus());
        queryVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
        queryVO.setRegisterTime(relationDO.getSubMember().getRegisterTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
        return queryVO;
    }



    /**
     * 查询会员变更详细信息
     *
     * @param loginUser  登录用户
     * @param validateId 会员关系Id
     * @param roleTag    角色标签
     * @return 查询结果
     */
    private MemberModifyDetailResp getMemberModifyDetails(UserLoginCacheDTO loginUser, Long validateId, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        WorkflowTaskListBO result = workflowFeignService.listMemberProcessSteps(loginUser.getMemberId(), relationDO.getValidateTask().getProcessKey(), relationDO.getValidateTask().getTaskId());

        MemberModifyDetailResp detailVO = new MemberModifyDetailResp();
        detailVO.setOuterVerifySteps(baseMemberValidateService.getMemberModifyOuterSteps(relationDO));
        detailVO.setCurrentOuterStep(2);

        //内部流程
        result.getStepList().forEach(step -> {
            if (NumberUtil.notNullOrZero(roleTag)) {
                step.setStepName(step.getStepName().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag)));
            }
        });
        detailVO.setInnerVerifySteps(result.getStepList());
        detailVO.setCurrentInnerStep(result.getCurrentStep());

        detailVO.setValidateId(relationDO.getId());
        detailVO.setMemberId(relationDO.getSubMemberId());
        detailVO.setName(relationDO.getSubMember().getName());
        detailVO.setOuterStatus(relationDO.getOuterStatus());
        detailVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
        detailVO.setInnerStatus(relationDO.getInnerStatus());
        detailVO.setInnerStatusName(SecurityStringUtil.replaceMemberPrefix(MemberInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()), roleTag));
        detailVO.setStatus(relationDO.getStatus());
        detailVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        detailVO.setMemberTypeEnum(relationDO.getSubMemberTypeEnum());
        detailVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubMemberTypeEnum()));
        detailVO.setRoleName(relationDO.getSubRoleName());
        detailVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
        detailVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
        detailVO.setAccount(relationDO.getSubMember().getAccount());
        detailVO.setPhone(relationDO.getSubMember().getPhone());
        detailVO.setEmail(relationDO.getSubMember().getEmail());
        detailVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));

        //入库资料
        detailVO.setDepositDetails(baseMemberDepositDetailService.mergeMemberDepositoryDetailText(relationDO));

        //资质证明
        detailVO.setQualities(baseMemberQualificationService.findMemberChangeQualities(relationDO));

        //内、外部历史记录
        detailVO.setInnerHistory(baseMemberHistoryService.listMemberInnerHistory(relationDO, roleTag));
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        return detailVO;
    }
}
