package com.ssy.lingxi.member.serviceImpl.mobile;

import cn.hutool.core.util.ObjectUtil;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.model.req.MemberSalesFindUserIdReq;
import com.ssy.lingxi.member.api.model.resp.MemberSalesFeignPageQueryResp;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.model.resp.basic.MobileMemberSalesInformationResp;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.mobile.IMobileMemberSalesService;
import com.ssy.lingxi.member.util.MemberOrganizationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务员小程序--订单服务远程调用-业绩统计需要的会员信息-相关的接口
 *
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-24
 */
@Service
public class MobileMemberSalesServiceImpl implements IMobileMemberSalesService {
    private final static Logger logger = LoggerFactory.getLogger(MobileMemberSalesServiceImpl.class);

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    /**
     * 查看业务员详情
     *
     * @param headers 头部信息
     * @return 操作接口
     */
    @Override
    public MobileMemberSalesInformationResp getSalesInformation(HttpHeaders headers) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromMobile(headers);
        UserDO userDO = userRepository.findById(loginCacheDTO.getUserId()).orElse(null);

        if (ObjectUtil.isNull(userDO)) {
            return null;
        }
        List<MemberOrganizationDO> orgList = new ArrayList<>(userDO.getMember().getOrgs());
        MobileMemberSalesInformationResp queryVO = new MobileMemberSalesInformationResp();
        queryVO.setUserId(userDO.getId());
        queryVO.setName(userDO.getName());
        queryVO.setAccount(userDO.getAccount());
        queryVO.setRoleName(userDO.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(",")));
        queryVO.setTitle(MemberOrganizationUtil.joinTitleToString(userDO.getOrg() == null ? 0L : userDO.getOrg().getId(), orgList));
        return queryVO;
    }

    /**
     * 远程调用--查看业务员统计
     *
     * @param pageVO 查询条件
     * @return 返回下级会员信息
     */
    @Override
    public PageDataResp<MemberSalesFeignPageQueryResp> getSalesList(MemberSalesFindUserIdReq pageVO) {
        logger.info("业务员小程序业绩统计--进来了 ---会员ID:"+pageVO.getMemberId()+"角色Id:"+pageVO.getMemberRoleId()+"用户Id"+pageVO.getUserId());
        UserDO userDO = userRepository.findById(pageVO.getUserId()).orElse(null);
        if (userDO == null || userDO.getMember() == null || !userDO.getMember().getId().equals(pageVO.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (userDO.getIsSales() == null || userDO.getIsSales().equals(Boolean.valueOf(EnableDisableStatusEnum.DISABLE.getCode().toString()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_IS_NOT_SALES);
        }

        // 找到全部的下级会员
        List<MemberUserChannelDO> channels = userDO.getUserAuth().getChannels().stream().filter(channel -> channel.getRoleId().equals(pageVO.getMemberRoleId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(channels)) {
            logger.info("该业务员没有下级会员------------------------");
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        // 关系Id
        List<Long> relationIds = channels.stream().map(MemberUserChannelDO::getMemberRelationId).collect(Collectors.toList());

        // 根据关系Id结合用户输入查询条件 找到所有的下级会员并返回VO对象
        if (ObjectUtil.isNotNull(relationIds)) {

            QMemberUserChannelDO qMemberUserChannelDO = QMemberUserChannelDO.memberUserChannelDO;
            QMemberRelationDO qMemberRelationDO = QMemberRelationDO.memberRelationDO;
            QUserDO qUserDO= QUserDO.userDO;
            QMemberRoleDO qMemberRoleDO= QMemberRoleDO.memberRoleDO;
            JPAQuery<MemberSalesFeignPageQueryResp> query =
                    jpaQueryFactory.select(Projections.constructor(MemberSalesFeignPageQueryResp.class, qMemberUserChannelDO.memberRelationId,qMemberUserChannelDO.subMemberId, qMemberRelationDO.subMember.name, qMemberRelationDO.subRoleId, qMemberRelationDO.subRole.roleName))
                            .from(qMemberUserChannelDO)
                            .leftJoin(qUserDO).on(qMemberUserChannelDO.userId.eq(qUserDO.id))
                            .leftJoin(qMemberRelationDO).on(qMemberUserChannelDO.memberRelationId.eq(qMemberRelationDO.id))
                            .leftJoin(qMemberRoleDO).on(qMemberRelationDO.subRoleId.eq(qMemberRoleDO.id))
                            .where(qMemberRoleDO.roleType.eq(RoleTypeEnum.SERVICE_CONSUMER.getCode()))
                            .where(qMemberUserChannelDO.userId.eq(pageVO.getUserId()))
                            .where(qMemberRelationDO.id.in(relationIds))
                            .groupBy(qMemberUserChannelDO.memberRelationId,qMemberUserChannelDO.subMemberId, qMemberRelationDO.subMember.name, qMemberRelationDO.subRoleId, qMemberRelationDO.subRole.roleName);

            if (StringUtils.hasLength(pageVO.getMemberName())) {
                query.where(qMemberRelationDO.subMember.name.eq(pageVO.getMemberName()));
            }

            //总数
            long totalCount = query.fetch().size();
            logger.info("该业务员下级会员总数---"+totalCount);
            if (ObjectUtil.isNotNull(pageVO.getCurrent()) && ObjectUtil.isNotNull(pageVO.getPageSize())) {
                query.limit(pageVO.getPageSize()).offset((long) (pageVO.getCurrent() - 1) * pageVO.getPageSize());
            }
            // 所有下级会员信息
            List<MemberSalesFeignPageQueryResp> result = query.orderBy(qMemberUserChannelDO.memberRelationId.desc()).fetch();
            return new PageDataResp<>(totalCount, result);
        }

        return new PageDataResp<>(0L, new ArrayList<>());
    }

}
