package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员注册资料仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-09
 */
@Repository
public interface MemberRegisterDetailRepository extends JpaRepository<MemberRegisterDetailDO, Long>, JpaSpecificationExecutor<MemberRegisterDetailDO> {

    List<MemberRegisterDetailDO> findByMemberConfig(MemberRegisterConfigDO memberConfig);

    List<MemberRegisterDetailDO> findByMemberAndVersion(MemberDO member, Integer version);

    List<MemberRegisterDetailDO> findByMember(MemberDO member);

    List<MemberRegisterDetailDO> findByMemberAndStatus(MemberDO member,Integer status);

    @Transactional
    void deleteByMember(MemberDO member);

    @Transactional
    void deleteByMemberConfig(MemberRegisterConfigDO memberRegisterConfigDO);

    List<MemberRegisterDetailDO> findByParentIdAndVersion(Long id, Integer code);

    List<MemberRegisterDetailDO> findByParentId(Long id);

    List<MemberRegisterDetailDO> findByParentIdIn(List<Long> ids);

    @Transactional
    void deleteByParentIdIn(List<Long> listIds);

    @Transactional
    void deleteByIdIn(List<Long> ids);
}
