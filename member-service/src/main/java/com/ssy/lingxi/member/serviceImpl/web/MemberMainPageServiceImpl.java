package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.commodity.api.feign.ICountryAreaFeign;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.enums.MemberOuterStatusEnum;
import com.ssy.lingxi.member.enums.PlatformInnerStatusEnum;
import com.ssy.lingxi.member.model.req.basic.MemberLogoReq;
import com.ssy.lingxi.member.model.req.basic.MemberUpdateRegisterDetailReq;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRegisterDetailResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberRegisterDetailService;
import com.ssy.lingxi.member.service.web.IMemberMainPageService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * “首页” - 相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-01-23
 */
@Service
public class MemberMainPageServiceImpl implements IMemberMainPageService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private ICountryAreaFeign countryAreaFeign;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    /**
     * 新增或修改会员Logo
     *
     * @param headers Http头部信息
     * @param logoVO  接口参数
     * @return 新增结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void addMemberUserLogo(HttpHeaders headers, MemberLogoReq logoVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if(userDO == null || userDO.getMember() == null || !userDO.getMember().getId().equals(loginUser.getMemberId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        userDO.setLogo(logoVO.getLogo());
        userRepository.saveAndFlush(userDO);

        if(userDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            MemberDO memberDO = userDO.getMember();
            memberDO.setLogo(logoVO.getLogo());
            memberRepository.saveAndFlush(memberDO);
        }


    }

    /**
     * “首页” - 审核不通过时，查询会员注册资料信息
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public MemberRegisterDetailResp getMemberRegisterDetail(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberRelationDO relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if (relationDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //审核不为“审核不通过”，即审核未完成，不能修改信息
        if (!relationDO.getInnerStatus().equals(PlatformInnerStatusEnum.VERIFY_NOT_PASSED.getCode())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        MemberRegisterDetailResp detailVO = new MemberRegisterDetailResp();
        detailVO.setMemberId(relationDO.getSubMemberId());
        detailVO.setName(relationDO.getSubMember().getName());
        detailVO.setOuterStatus(relationDO.getOuterStatus());
        detailVO.setOuterStatusName(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));
        detailVO.setStatus(relationDO.getStatus());
        detailVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        detailVO.setMemberTypeEnum(relationDO.getSubRole().getMemberType());
        detailVO.setMemberType(relationDO.getSubRole().getMemberType());
        detailVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
        detailVO.setRoleId(relationDO.getSubRoleId());
        detailVO.setRoleName(relationDO.getSubRole().getRoleName());
        detailVO.setLevelId(relationDO.getLevelRight() == null ? 0L : (relationDO.getLevelRight().getLevelConfig() == null ? 0L : relationDO.getLevelRight().getLevelConfig().getId()));
        detailVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
        detailVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
        detailVO.setAccount(relationDO.getSubMember().getAccount());

        WrapperUtil.throwWhenDataIsNull(countryAreaFeign.getCountryAreaByTelCode(relationDO.getSubMember().getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);
        detailVO.setTelCode(relationDO.getSubMember().getTelCode());
        detailVO.setPhone(relationDO.getSubMember().getPhone());
        detailVO.setEmail(relationDO.getSubMember().getEmail());
        detailVO.setCreateTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
        detailVO.setGroups(baseMemberRegisterDetailService.switchMemberRegisterDetail(relationDO.getSubMember()));

        return detailVO;
    }

    /**
     * “首页” - 审核不通过时，修改会员注册资料
     *
     * @param headers  Http头部信息
     * @param detailVO 接口参数
     */
    @Override
    public void updateMemberRegisterDetail(HttpHeaders headers, MemberUpdateRegisterDetailReq detailVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        //审核不通过时，修改信息，修改后自动提交审核
        MemberRelationDO relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if (relationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode()) || relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_HAS_ALREADY_COMMIT_TO_VALIDATE);
        }

        if(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_HAS_COMPLETED);
        }

        baseMemberRegisterDetailService.updatePlatformMemberRegisterDetail(relationDO, detailVO.getEmail(), detailVO.getDetail(), true, true);
    }

    /**
     * 判断当前登录会员，是否指定会员的上级会员
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public Boolean isUpperMember(HttpHeaders headers, MemberAndRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return relationRepository.existsByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), idVO.getMemberId(), idVO.getRoleId());
    }
}
