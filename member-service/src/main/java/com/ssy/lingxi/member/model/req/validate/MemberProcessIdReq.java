package com.ssy.lingxi.member.model.req.validate;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 查询会员流程规则配置详情接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-15
 */
@Data
public class MemberProcessIdReq implements Serializable {
    private static final long serialVersionUID = -6851870421869968669L;

    /**
     * 流程规则Id
     */
    @NotNull(message = "流程规则Id要大于0")
    @Positive(message = "流程规则Id要大于0")
    private Long id;
}
