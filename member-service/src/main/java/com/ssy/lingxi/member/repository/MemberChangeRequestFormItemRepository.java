package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.MemberChangeRequestFormDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberChangeRequestFormItemDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 供应商生命周期管理 - 变更申请单考评项Repository
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
public interface MemberChangeRequestFormItemRepository extends JpaRepository<MemberChangeRequestFormItemDO, Long>, JpaSpecificationExecutor<MemberChangeRequestFormItemDO> {
    void deleteByChangeRequestForm(MemberChangeRequestFormDO changeRequestFormDO);
}
