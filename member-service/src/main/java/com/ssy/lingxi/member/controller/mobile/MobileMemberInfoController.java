package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.mobile.*;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RegisterDetailGroupResp;
import com.ssy.lingxi.member.model.resp.basic.UserQueryResp;
import com.ssy.lingxi.member.model.resp.customer.*;
import com.ssy.lingxi.member.model.resp.info.MobileInfoBasicDetailResp;
import com.ssy.lingxi.member.model.resp.info.MobileInfoLevelRightResp;
import com.ssy.lingxi.member.model.resp.info.MobileLevelRightResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailLevelHistoryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailRightConfigResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailRightHistoryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailRightSpendHistoryResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileInfoApplyButtonResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileUpdateDepositDetailQueryResp;
import com.ssy.lingxi.member.service.mobile.IMobileMemberInfoService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * App - 会员信息查询相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-08
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/info")
public class MobileMemberInfoController extends BaseController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IMobileMemberInfoService mobileMemberInfoService;

    /**
     * “我的-卡包” - 分页查询归属会员列表
     *
     * @param headers     Http头部信息
     * @param pageDataReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/mine/page")
    public WrapperResp<PageDataResp<MobileInfoLevelRightResp>> pageUpperMemberLevelRights(@RequestHeader HttpHeaders headers, @Valid PageDataReq pageDataReq) {
        return WrapperUtil.success(mobileMemberInfoService.pageUpperMemberLevelRights(headers, pageDataReq));
    }

    /**
     * “会员中心” - 查询当前会员的等级、权益信息
     *
     * @param headers Http头部信息
     * @param shopVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/level/right/basic")
    public WrapperResp<MobileLevelRightResp> getMemberDetailLevelRight(@RequestHeader HttpHeaders headers, @Valid MobileShopReq shopVO) {
        return WrapperUtil.success(mobileMemberInfoService.getMemberDetailLevelRight(headers, shopVO));
    }

    /**
     * “会员中心”  - 分页查询会员权益获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/right/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightHistoryResp>> pageMemberDetailRightHistory(@RequestHeader HttpHeaders headers, @Valid MobileShopPageDataReq pageVO) {
        return WrapperUtil.success(mobileMemberInfoService.pageMemberDetailRightHistory(headers, pageVO));
    }

    /**
     * “会员中心”  - 分页查询会员权益使用记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/right/spend/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightSpendHistoryResp>> pageMemberDetailRightSpendHistory(@RequestHeader HttpHeaders headers, @Valid MobileShopPageDataReq pageVO) {
        return WrapperUtil.success(mobileMemberInfoService.pageMemberDetailRightSpendHistory(headers, pageVO));
    }

    /**
     * “会员中心”  - 分页查询活跃分获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/level/history/page")
    public WrapperResp<PageDataResp<MemberDetailLevelHistoryResp>> pageMemberLevelDetailHistory(@RequestHeader HttpHeaders headers, @Valid MobileShopPageDataReq pageVO) {
        return WrapperUtil.success(mobileMemberInfoService.pageMemberLevelDetailHistory(headers, pageVO));
    }

    /**
     * “店铺会员”页面，查询“申请会员”按钮状态和文本
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/apply/condition")
    public WrapperResp<MobileInfoApplyButtonResp> getMobileApplyCondition(@RequestHeader HttpHeaders headers, @Valid UpperMemberIdRoleIdReq upperMemberVO) {
        return WrapperUtil.success(mobileMemberInfoService.getMobileApplyCondition(headers, upperMemberVO));
    }

    /**
     * “店铺会员”页面，查询“入会享特权”的权益列表
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/apply/rights")
    public WrapperResp<List<MemberDetailRightConfigResp>> getUpperMemberRights(@RequestHeader HttpHeaders headers, @Valid UpperMemberIdRoleIdReq upperMemberVO) {
        return WrapperUtil.success(mobileMemberInfoService.getUpperMemberRights(headers, upperMemberVO));
    }

    /**
     * “店铺会员”页面，查询申请成为会员时，需要填写的入库资料
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/apply/deposit/detail")
    public WrapperResp<List<RegisterDetailGroupResp>> findMobileApplyDepositDetails(@RequestHeader HttpHeaders headers, @Valid UpperMemberIdRoleIdReq upperMemberVO) {
        return WrapperUtil.success(mobileMemberInfoService.findMobileApplyDepositDetails(headers, upperMemberVO));
    }

    /**
     * “店铺会员”页面 - 提交申请
     *
     * @param headers Http头部信息
     * @param subVO   接口参数
     * @return 申请结果
     */
    @PostMapping("/apply")
    public WrapperResp<Void> applyToBeSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobileApplyForSubReq subVO) {
        mobileMemberInfoService.applyToBeSubMember(headers, subVO);
        return WrapperUtil.success();
    }

    /**
     * “店铺会员”页面，修改入库资料时，查询入库资料
     *
     * @param headers       Http头部信息
     * @param upperMemberVO 上级会员信息
     * @return 查询结果
     */
    @GetMapping("/deposit/detail")
    public WrapperResp<MobileUpdateDepositDetailQueryResp> getMemberDepositDetail(@RequestHeader HttpHeaders headers, @Valid UpperMemberIdRoleIdReq upperMemberVO) {
        return WrapperUtil.success(mobileMemberInfoService.getMemberDepositDetail(headers, upperMemberVO));
    }

    /**
     * “店铺会员”页面，当审核不通过时，修改入库资料
     *
     * @param headers         Http头部信息
     * @param depositDetailVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/deposit/detail/update")
    public WrapperResp<Void> updateDepositDetail(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobileUpdateDepositDetailReq depositDetailVO) {
        mobileMemberInfoService.updateDepositDetail(headers, depositDetailVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “店铺会员” - 查询当前会员的等级、权益信息
     *
     * @param headers                Http头部信息
     * @param upperMemberIdRoleIdReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/shop/level/right/basic")
    public WrapperResp<MobileLevelRightResp> getShopMemberDetailLevelRight(@RequestHeader HttpHeaders headers, @Valid UpperMemberIdRoleIdReq upperMemberIdRoleIdReq) {
        return WrapperUtil.success(mobileMemberInfoService.getShopMemberDetailLevelRight(headers, upperMemberIdRoleIdReq));
    }

    /**
     * “店铺会员”  - 分页查询会员权益获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/shop/right/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightHistoryResp>> pageShopMemberDetailRightHistory(@RequestHeader HttpHeaders headers, @Valid MobileUpperPageDataReq pageVO) {
        return WrapperUtil.success(mobileMemberInfoService.pageShopMemberDetailRightHistory(headers, pageVO));
    }

    /**
     * “店铺会员”  - 分页查询会员权益使用记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/shop/right/spend/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightSpendHistoryResp>> pageShopMemberDetailRightSpendHistory(@RequestHeader HttpHeaders headers, @Valid MobileUpperPageDataReq pageVO) {
        return WrapperUtil.success(mobileMemberInfoService.pageShopMemberDetailRightSpendHistory(headers, pageVO));
    }

    /**
     * “店铺会员”  - 分页查询活跃分获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/shop/level/history/page")
    public WrapperResp<PageDataResp<MemberDetailLevelHistoryResp>> pageShopMemberLevelDetailHistory(@RequestHeader HttpHeaders headers, @Valid MobileUpperPageDataReq pageVO) {
        return WrapperUtil.success(mobileMemberInfoService.pageShopMemberLevelDetailHistory(headers, pageVO));
    }

    /**
     * “会员信息” - 会员基本信息
     *
     * @param headers Http头部信息
     * @param shopVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/basic")
    public WrapperResp<MobileInfoBasicDetailResp> getMemberBasicDetail(@RequestHeader HttpHeaders headers, @Valid MobileShopReq shopVO) {
        return WrapperUtil.success(mobileMemberInfoService.getMemberBasicDetail(headers, shopVO));
    }

    /**
     * “会员信息” - 修改会员注册资料时，查询会员注册资料
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/detail/update/get")
    public WrapperResp<List<RegisterDetailGroupResp>> getMemberRegisterDetail(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(mobileMemberInfoService.getMemberRegisterDetail(headers));
    }

    /**
     * “会员信息” - 修改会员注册资料
     *
     * @param headers  Http头部信息
     * @param detailVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/detail/update")
    public WrapperResp<Void> updateMemberRegisterDetail(@RequestHeader HttpHeaders headers, @RequestBody @Valid UpdateRegisterDetailReq detailVO) {
        mobileMemberInfoService.updateMemberRegisterDetail(headers, detailVO);
        return WrapperUtil.success();
    }

    /**
     * 分页查询会员下属用户
     *
     * @param headers Http头部新
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/users/page")
    public WrapperResp<PageDataResp<UserQueryResp>> pageUsers(@RequestHeader HttpHeaders headers, @Valid MobileUserPageDataReq pageVO) {
        return WrapperUtil.success(mobileMemberInfoService.pageUsers(headers, pageVO));
    }

    /**
     * 企业认证 - 获取会员注册资料
     *
     * @return 查询结果
     */
    @GetMapping("/corporation/certification/detail/get")
    public WrapperResp<List<MemberConfigGroupResp>> getCorporationCertificationDetail() {
        return WrapperUtil.success(mobileMemberInfoService.getCorporationCertificationDetail(getSysUser()));
    }

    /**
     * 保存企业认证信息草稿
     *
     * @param req 请求参数
     * @return 保存结果
     */
    @PostMapping("/corporation/certification/draft/save")
    public WrapperResp<Void> saveCorporationCertificationDraft(@RequestBody @Valid CorporationCertificationDraftReq req) {
        mobileMemberInfoService.saveCorporationCertificationDraft(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 获取会员企业认证信息草稿
     *
     * @return 会员企业认证信息草稿
     */
    @GetMapping("/corporation/certification/draft/get")
    public WrapperResp<Map<String, Object>> getCorporationCertificationDraft() {
        return WrapperUtil.success(mobileMemberInfoService.getCorporationCertificationDraft(getSysUser()));
    }

    /**
     * 企业认证
     *
     * @param req 请求参数
     * @return 认证结果
     */
    @PostMapping("/corporation/certification")
    public WrapperResp<Void> corporationCertification(@RequestBody @Valid CertificationCertificationReq req) {
        mobileMemberInfoService.corporationCertification(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 获取认证资料详情
     *
     * @param corporationId 企业认证id
     * @return 查询结果
     */
    @GetMapping("/member/corporation/certification/detail/get")
    public WrapperResp<List<RegisterDetailGroupResp>> getCorporationCertificationDetail(@RequestParam("corporationId") Long corporationId) {
        return WrapperUtil.success(mobileMemberInfoService.getMemberCorporationCertificationDetail(getSysUser(), corporationId));
    }

    /**
     * 企业认证变更
     *
     * @param req 接口参数
     * @return 修改结果
     */
    @PostMapping("/corporation/certification/update")
    public WrapperResp<Void> corporationCertificationUpdate(@RequestBody @Valid CertificationCertificationUpdateReq req) {
        mobileMemberInfoService.updateCorporationCertification(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 获取企业认证信息
     *
     * @return 企业认证信息
     */
    @GetMapping("/corporation/certification/info/get")
    public WrapperResp<CorporationResp> getCorporationCertificationInfo() {
        return WrapperUtil.success(mobileMemberInfoService.getCorporationCertificationInfo(getSysUser()));
    }

    /**
     * 根据企业id获取企业认证信息
     *
     * @param corporationId 企业id
     * @return 企业认证信息
     */
    @GetMapping("/corporation/certification/info/byId/get")
    public WrapperResp<CorporationResp> getCorporationCertificationInfoById(@RequestParam("corporationId") Long corporationId) {
        return WrapperUtil.success(mobileMemberInfoService.getCorporationCertificationInfoById(getSysUser(), corporationId));
    }

    /**
     * 企业主体列表
     *
     * @return 企业主体列表
     */
    @GetMapping("/corporation/list")
    public WrapperResp<CorporationInfoResp> listCorporation() {
        return WrapperUtil.success(mobileMemberInfoService.listCorporation(getSysUser()));
    }

    /**
     * 查询品牌客户列表
     * @return 品牌客户列表
     */
    @GetMapping("/brandCustomer/page")
    public WrapperResp<List<BrandResp>> pageBrandCustomer() {
        return WrapperUtil.success(mobileMemberInfoService.listBrandCustomer(getSysUser()));
    }

    /**
     * 根据企业主体查询品牌客户列表
     *
     * @return 品牌客户列表
     */
    @GetMapping("/brandCustomer/list")
    public WrapperResp<List<BrandCustomerResp>> listBrandCustomer(@RequestParam("corporationId") Long corporationId) {
        return WrapperUtil.success(mobileMemberInfoService.listBrandCustomerByCorporationId(getSysUser(), corporationId));
    }

    /**
     * 新增企业主体
     *
     * @param req 添加参数
     * @return 添加结果
     */
    @PostMapping("/corporation/add")
    public WrapperResp<Void> addCorporation(@RequestBody @Valid CorporationAddReq req) {
        mobileMemberInfoService.addCorporation(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 新增品牌会员
     *
     * @param req 请求参数
     * @return 保存结果
     */
    @PostMapping("/brand/member/add")
    public WrapperResp<Void> addBrandMember(@RequestBody @Valid BrandMemberAddReq req) {
        mobileMemberInfoService.addBrandMember(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 获取企业认证状态
     *
     * @return 企业认证状态
     */
    @GetMapping("/corporation/certification/status/get")
    public WrapperResp<CorporationCertificationStatusResp> getCorporationCertificationStatus() {
        return WrapperUtil.success(mobileMemberInfoService.getCorporationCertificationStatus(getSysUser()));
    }

    /**
     * 查询品牌会员
     *
     * @return 品牌会员
     */
    @GetMapping("/brand/member/get")
    public WrapperResp<BrandCustomerResp> getBrandMember(@RequestParam("memberId") Long memberId) {
        return WrapperUtil.success(mobileMemberInfoService.getBrandMember(getSysUser(), memberId));
    }

    /**
     * 品牌会员编辑
     *
     * @param req 接口参数
     * @return 修改结果
     */
    @PostMapping("/brand/member/update")
    public WrapperResp<Void> brandMemberUpdate(@RequestBody @Valid BrandMemberUpdateReq req) {
        mobileMemberInfoService.brandMemberUpdate(getSysUser(), req);
        return WrapperUtil.success();
    }

}
