package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.controller.web.FaceRecognitionFlagReq;

/**
 * 会员参数配置
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/13
 */
public interface IMemberParamConfigService {

    /**
     * 查看是否支持人脸识别
     *
     * @return 查询结果
     */
    Boolean getFaceRecognitionFlag(UserLoginCacheDTO sysUser);

    /**
     * 添加修改否支持人脸识别
     *
     * @param req 接口参数
     */
    void saveOrUpdateFaceRecognitionFlag(UserLoginCacheDTO sysUser, FaceRecognitionFlagReq req);

}
