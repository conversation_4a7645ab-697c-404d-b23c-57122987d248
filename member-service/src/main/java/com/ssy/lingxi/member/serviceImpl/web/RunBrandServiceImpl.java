package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.model.resp.RunBrandFeignResp;
import com.ssy.lingxi.member.entity.do_.basic.RunBrandDO;
import com.ssy.lingxi.member.model.req.basic.RunBrandAddReq;
import com.ssy.lingxi.member.model.resp.basic.RunBrandResp;
import com.ssy.lingxi.member.repository.RunBrandRepository;
import com.ssy.lingxi.member.service.web.IRunBrandService;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 经营店铺service实现类
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/5/26
 */
@Service
public class RunBrandServiceImpl implements IRunBrandService {

    @Resource
    private RunBrandRepository runBrandRepository;

    private List<RunBrandDO> findAll(){
        return runBrandRepository.findAll(Sort.by("sort").descending());
    }

    @Override
    public List<RunBrandResp> list(UserLoginCacheDTO sysUser) {
        List<RunBrandDO> list = findAll();
        return list.stream().map(o ->{
            RunBrandResp resp=new RunBrandResp();
            resp.setCode(o.getCode());
            resp.setId(o.getId());
            resp.setName(o.getName());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RunBrandFeignResp> listByFeign() {
        List<RunBrandDO> list = findAll();
        return list.stream().map(o ->{
            RunBrandFeignResp resp=new RunBrandFeignResp();
            resp.setCode(o.getCode());
            resp.setId(o.getId());
            resp.setName(o.getName());
            return resp;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(UserLoginCacheDTO sysUser, RunBrandAddReq req) {
        try{
            RunBrandDO runBrandDO=new RunBrandDO();
            runBrandDO.setCode(req.getCode());
            runBrandDO.setName(req.getName());
            runBrandDO.setName(req.getName());
            runBrandDO.setCreateUserId(sysUser.getUserId());
            runBrandRepository.saveAndFlush(runBrandDO);
            return runBrandDO.getId();
        }catch (DataIntegrityViolationException e){
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_REPEAT);
        }
    }
}
