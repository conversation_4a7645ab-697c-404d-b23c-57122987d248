package com.ssy.lingxi.member.repository.comment;

import com.ssy.lingxi.member.entity.do_.comment.MemberOrderProductCommentDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员评价订单商品Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/3
 */
@Repository
public interface MemberOrderProductCommentRepository extends JpaRepository<MemberOrderProductCommentDO, Long>, JpaSpecificationExecutor<MemberOrderProductCommentDO> {

    List<MemberOrderProductCommentDO> findAllByOrderId(Long orderId);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE MemberOrderProductCommentDO opc set opc.buyerCommentStatus = :status where opc.id in (:ids)")
    int updateBuyerCommentStatus(@Param("status") Integer status, @Param("ids") List<Long> ids);

    @Modifying(clearAutomatically = true)
    @Query("UPDATE MemberOrderProductCommentDO opc set opc.vendorCommentStatus = :status where opc.id in (:ids)")
    int updateVendorCommentStatus(@Param("status") Integer status, @Param("ids") List<Long> id);
}
