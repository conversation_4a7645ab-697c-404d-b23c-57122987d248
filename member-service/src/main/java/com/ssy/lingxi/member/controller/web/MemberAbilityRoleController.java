package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberRoleGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberRoleQueryResp;
import com.ssy.lingxi.member.model.resp.platform.MemberRoleResp;
import com.ssy.lingxi.member.service.web.IMemberAbilityRoleService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 系统能力-权限管理-角色管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-30
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/role")
public class MemberAbilityRoleController {
    @Resource
    private IMemberAbilityRoleService memberAbilityRoleService;

    /**
     * 分页、模糊查询用户角色
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberRoleQueryResp>> pageMemberRole(@RequestHeader HttpHeaders headers, @Valid MemberRolePageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityRoleService.pageMemberRole(headers, pageVO));
    }

    /**
     * 查询用户角色列表
     * @param headers HttpHeaders信息
     * @param memberRoleResp 接口参数
     * @return 查询结果
     */
    @GetMapping("/memberRoleList")
    public WrapperResp<List<MemberRoleQueryResp>> getMemberRoleList(@RequestHeader HttpHeaders headers, @Valid MemberRoleResp memberRoleResp) {
        return WrapperUtil.success(memberAbilityRoleService.getMemberRoleList(headers, memberRoleResp));
    }

    /**
     * 根据名称，分页、模糊查询用户角色
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/page/byname")
    WrapperResp<PageDataResp<MemberRoleQueryResp>> pageMemberRoleByName(@RequestHeader HttpHeaders headers, @Valid MemberRolePageByNameDataReq pageVO) {
        return WrapperUtil.success(memberAbilityRoleService.pageMemberRoleByName(headers, pageVO));
    }

    /**
     * 获取左侧权限菜单、组织机构列表
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @GetMapping("/authTree")
    public WrapperResp<AuthTreeResp> memberAuthTree(@RequestHeader HttpHeaders headers, @Valid UserRoleAuthTreeReq roleIdReq) {
        return WrapperUtil.success(memberAbilityRoleService.memberAuthTree(headers, roleIdReq));
    }

    /**
     * 新增用户角色
     * @param headers HttpHeaders信息
     * @param addVO 接口参数
     * @return 新增结果
     */
    @RequestMapping("/add")
    public WrapperResp<Void> addMemberRole(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityRoleAddReq addVO) {
        memberAbilityRoleService.addMemberRole(headers, addVO);
        return WrapperUtil.success();
    }

    /**
     * 查询单个用户角色的信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberRoleGetResp> getMemberRoleAuth(@RequestHeader HttpHeaders headers, @Valid UserRoleIdReq idVO) {
        return WrapperUtil.success(memberAbilityRoleService.getMemberRole(headers, idVO));
    }

    /**
     * 修改用户角色及菜单权限、数据权限
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 更新结果
     */
    @RequestMapping("/update")
    public WrapperResp<Void> updateMemberRole(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityRoleUpdateReq updateVO) {
        memberAbilityRoleService.updateMemberRole(headers, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 删除用户角色
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 删除结果
     */
    @RequestMapping("/delete")
    public WrapperResp<Void> deleteMemberRole(@RequestHeader HttpHeaders headers, @RequestBody @Valid UserRoleIdReq idVO) {
        memberAbilityRoleService.deleteMemberRole(headers, idVO);
        return WrapperUtil.success();
    }

    /**
     * 更新用户角色状态
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 更新结果
     */
    @RequestMapping("/updatestatus")
    public WrapperResp<Void> updateMemberRoleStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberRoleUpdateStatusReq statusVO) {
        memberAbilityRoleService.updateMemberRoleStatus(headers, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 登录用户其他角色列表
     * @param headers HttpHeaders信息
     * @return 角色信息
     */
    @RequestMapping("/other/roleList")
    public WrapperResp<List<MemberRoleResp>> otherRoleList(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityRoleService.otherRoleList(headers));
    }
}
