package com.ssy.lingxi.member.repository.commission;

import com.ssy.lingxi.member.entity.do_.commission.BankCardDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 银行卡Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Repository
public interface BankCardRepository extends JpaRepository<BankCardDO, Long>, JpaSpecificationExecutor<BankCardDO> {

    /**
     * 根据用户id查询银行卡列表
     * @param userId 用户id
     * @return 银行卡列表
     */
    List<BankCardDO> findByUserIdAndStatusOrderByCreateTimeDesc(Long userId, Integer status);

    /**
     * 根据用户id查询所有银行卡（包括已删除）
     * @param userId 用户id
     * @param pageable 分页参数
     * @return 银行卡列表
     */
    Page<BankCardDO> findByUserIdOrderByCreateTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据银行卡号查询银行卡
     * @param cardNumber 银行卡号
     * @return 银行卡
     */
    Optional<BankCardDO> findByCardNumber(String cardNumber);

    /**
     * 根据用户id和银行卡号查询银行卡
     * @param userId 用户id
     * @param cardNumber 银行卡号
     * @return 银行卡
     */
    Optional<BankCardDO> findByUserIdAndCardNumber(Long userId, String cardNumber);

    /**
     * 查询用户的默认银行卡
     * @param userId 用户id
     * @return 默认银行卡
     */
    Optional<BankCardDO> findByUserIdAndStatus(Long userId, Integer status);

    /**
     * 根据状态查询银行卡列表
     * @param status 状态
     * @param pageable 分页参数
     * @return 银行卡列表
     */
    Page<BankCardDO> findByStatusOrderByCreateTimeDesc(Integer status, Pageable pageable);

    /**
     * 根据银行名称查询银行卡列表
     * @param bankName 银行名称
     * @param pageable 分页参数
     * @return 银行卡列表
     */
    Page<BankCardDO> findByBankNameContainingOrderByCreateTimeDesc(String bankName, Pageable pageable);

    /**
     * 检查用户是否已有相同银行卡号的卡片
     * @param userId 用户id
     * @param cardNumber 银行卡号
     * @param status 状态
     * @return 是否存在
     */
    boolean existsByUserIdAndCardNumberAndStatus(Long userId, String cardNumber, Integer status);

    /**
     * 统计用户的银行卡数量
     * @param userId 用户id
     * @param status 状态
     * @return 银行卡数量
     */
    Long countByUserIdAndStatus(Long userId, Integer status);


    /**
     * 根据用户id列表查询银行卡列表
     * @param userIds 用户id列表
     * @return 银行卡列表
     */
    List<BankCardDO> findByUserIdInAndStatus(List<Long> userIds, Integer status);



    /**
     * 统计各银行的银行卡数量
     * @return 银行卡统计列表
     */
    @Query("SELECT bc.bankName, COUNT(bc) FROM BankCardDO bc WHERE bc.status = 1 GROUP BY bc.bankName ORDER BY COUNT(bc) DESC")
    List<Object[]> countByBankName();
}
