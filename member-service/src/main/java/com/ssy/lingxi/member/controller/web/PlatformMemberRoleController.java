package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberRoleGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberRoleQueryResp;
import com.ssy.lingxi.member.service.web.IPlatformMemberRoleService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 平台后台-权限管理-角色管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-30
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/manage/role")
public class PlatformMemberRoleController {
    @Resource
    private IPlatformMemberRoleService platformMemberRoleService;

    /**
     * 分页、模糊查询用户角色
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<PlatformMemberRoleQueryResp>> pageMemberRole(@RequestHeader HttpHeaders headers, @Valid MemberRolePageDataReq pageVO) {
        return WrapperUtil.success(platformMemberRoleService.pageMemberRole(headers, pageVO));
    }

    /**
     * 查询单个用户角色的信息
     * @param headers  HttpHeaders信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<PlatformMemberRoleGetResp> getMemberRoleAuth(@RequestHeader HttpHeaders headers, @Valid UserRoleIdReq idVO) {
        return WrapperUtil.success(platformMemberRoleService.getMemberRole(headers, idVO));
    }

    /**
     * 新增用户角色
     * @param headers  HttpHeaders信息
     * @param addVO 接口参数
     * @return 新增结果
     */
    @RequestMapping("/add")
    public WrapperResp<Void> addMemberRole(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformMemberRoleAddReq addVO) {
         platformMemberRoleService.addMemberRole(headers, addVO);
        return WrapperUtil.success();
    }

    /**
     * 更新用户角色
     * @param headers  HttpHeaders信息
     * @param updateVO 接口参数
     * @return 更新结果
     */
    @RequestMapping("/update")
    public WrapperResp<Void> updateMemberRole(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformMemberRoleUpdateReq updateVO) {
         platformMemberRoleService.updateMemberRole(headers, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 删除用户角色
     * @param headers  HttpHeaders信息
     * @param idVO 接口参数
     * @return 删除结果
     */
    @RequestMapping("/delete")
    public WrapperResp<Void> deleteMemberRole(@RequestHeader HttpHeaders headers, @RequestBody @Valid UserRoleIdReq idVO) {
         platformMemberRoleService.deleteMemberRole(headers, idVO);
        return WrapperUtil.success();
    }

    /**
     * 更新用户角色状态
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 更新结果
     */
    @RequestMapping("/updatestatus")
    public WrapperResp<Void> updateMemberRoleStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberRoleUpdateStatusReq statusVO) {
         platformMemberRoleService.updateMemberRoleStatus(headers, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 获取用户角色权限树
     * @param headers HttpHeaders信息
     * @param authTreeReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/authTree")
    public WrapperResp<AuthTreeResp> getMemberAuthTree(@RequestHeader HttpHeaders headers, @Valid UserRoleAuthTreeReq authTreeReq) {
        return WrapperUtil.success(platformMemberRoleService.getMemberAuthTree(headers, authTreeReq));
    }
}
