package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.points.MemberPointsBatchAddReq;
import com.ssy.lingxi.member.model.req.points.MemberPointsPageDataReq;
import com.ssy.lingxi.member.model.req.points.MemberPointsAddReq;
import com.ssy.lingxi.member.model.resp.points.MemberPointsPageDataResp;
import com.ssy.lingxi.member.model.resp.points.MemberPointsQuerySearchConditionResp;
import com.ssy.lingxi.member.model.resp.points.MemberPointsSelectResp;

/**
 * 会员积分服务接口
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2024/8/8
 */
public interface IMemberPointsService {

    /**
     * 获取筛选条件
     * @param sysUser 登录用户
     * @return 筛选条件
     */
    MemberPointsQuerySearchConditionResp getPageCondition(UserLoginCacheDTO sysUser);

    /**
     * 分页查询
     *
     * @param sysUser                 登录用户
     * @param memberPointsPageDataReq 筛选条件
     * @return 查询结果
     */
    PageDataResp<MemberPointsPageDataResp> page(UserLoginCacheDTO sysUser, MemberPointsPageDataReq memberPointsPageDataReq);

    /**
     * 添加会员积分 - 选择会员弹窗
     * @param sysUser                 登录用户
     * @param memberPointsPageDataReq 筛选条件
     * @return 查询结果
     */
    PageDataResp<MemberPointsSelectResp> selectMemberPage(UserLoginCacheDTO sysUser, MemberPointsPageDataReq memberPointsPageDataReq);

    /**
     * 新增数据or修改数据
     *
     * @param sysUser                     操作用户
     * @param batchAddReq 实例对象
     */
    void batchAdd(UserLoginCacheDTO sysUser, MemberPointsBatchAddReq batchAddReq);
}
