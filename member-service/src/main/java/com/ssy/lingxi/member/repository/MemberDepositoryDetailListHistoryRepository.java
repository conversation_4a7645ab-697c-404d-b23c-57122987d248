package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailListHistoryDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员入库资料列表历史记录Jpa仓库
 * <AUTHOR>
 * @version 1.0
 * @since 2022/7/8 9:16
 */
@Repository
public interface MemberDepositoryDetailListHistoryRepository extends JpaRepository<MemberDepositoryDetailListHistoryDO, Long>, JpaSpecificationExecutor<MemberDepositoryDetailListHistoryDO> {
}
