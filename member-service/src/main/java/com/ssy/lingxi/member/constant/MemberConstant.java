package com.ssy.lingxi.member.constant;

import lombok.Data;

import java.text.DecimalFormat;
import java.time.format.DateTimeFormatter;

/**
 * 会员服务固定配置
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-31
 */
@Data
public class MemberConstant {
    /**
     * 平台后台超级管理员默认账号
     */
    @Deprecated
    public static final String PLATFORM_SUPER_ADMIN_ACCOUNT = "admin";

    /**
     * 平台后台新增会员时，会员的默认密码
     */
    public static final String PLATFORM_ADD_MEMBER_DEFAULT_PASSWORD = "888888";

    /**
     * 默认的会员交易评价平均星级（总体满意度）：当会员没有被交易评价时，默认显示的总体满意度
     */
    public static final int DEFAULT_TRADE_COMMENT_STAR = 5;

    /**
     * Web页面默认时间格式
     */
    public static final DateTimeFormatter DEFAULT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * APP默认时间格式
     */
    public static final DateTimeFormatter APP_DEFAULT_DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 计算登录等级积分的时间间隔（分钟），离上一次登录超过这个时间限制，才计算登录积分
     */
    public static final long LOGIN_LEVEL_SCORE_CALCULATION_INTERVAL_MINUTES = 120;

    /**
     * 用于会员注册时手机号码、邮箱的幂等校验
     */
    public static final long REGISTER_PHONE_EMAIL_CACHE_SECONDS = 60;

    /**
     * “无需审核”的平台会员审核流程的Key
     */
    public static final String EMPTY_PLATFORM_VALIDATE_PROCESS_KEY = "member_register_directly";

    /**
     * “无需审核”的会员入库流程的Key
     */
    public static final String EMPTY_DEPOSITORY_PROCESS_KEY = "member_depository_directly";

    /**
     * 会员能力 - 会员考评工作流的processKey
     */
    public static final String MEMBER_APPRAISAL_PROCESS_KEY = "member_appraisal_verify";

    /**
     * 会员能力 - 会员整改工作流的processKey
     */
    public static final String MEMBER_RECTIFY_PROCESS_KEY = "member_rectify_verify_ext";

    /**
     * 保留两位小数，小数部分是0的话保留整数，小数第一位不是0的话则保留一位
     */
    public static final DecimalFormat BIG_DECIMAL_FORMAT = new DecimalFormat("###.##");

    /**
     * 变更申请单编号前缀
     */
    public static final String CHANGE_REQUEST_FORM_SERIAL_PREFIX = "CH";

    /**
     * 获取邀请码自旋次数
     */
    public static final int SPIN = 30;

    /**
     * 入库资料列类型：string、long、upload、checkbox、radio、select、area
     */
    public static final String FIELD_TYPE_STRING = "string";
    public static final String FIELD_TYPE_LONG = "long";
    public static final String FIELD_TYPE_UPLOAD = "upload";
    public static final String FIELD_TYPE_CHECKBOX = "checkbox";
    public static final String FIELD_TYPE_RADIO = "radio";
    public static final String FIELD_TYPE_SELECT = "select";
    public static final String FIELD_TYPE_AREA = "area";

    public static final String MEMBER_EXCEL_FIELD = "%s(必填)";

    /**
     * 入库资料类型
     */
    public static final String REGISTER_TYPE_PLATFORM = "platformRegister";
    public static final String REGISTER_TYPE_MEMBER_SUB = "platformMemBerSub";

    /**
     * 【GET】获取企业微信的accessToken
     * corpid是唯一的，corpsecret是每个应用的密钥，有多个，不同应用请求该接口获取的access_token是不同的
     */
    public static final String ACCESS_TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";

    /**
     * 【POST】发送文本卡片应用消息
     * 文本卡片消息的请求格式如下：
     * {
     * "touser" : "UserID1|UserID2|UserID3",
     * "toparty" : "PartyID1 | PartyID2",
     * "totag" : "TagID1 | TagID2",
     * "msgtype" : "textcard",
     * "agentid" : 1,
     * "textcard" : {
     * "title" : "领奖通知",
     * "description" : "<div class=\"gray\">2016年9月26日</div> <div class=\"normal\">恭喜你抽中iPhone 7一台，领奖码：xxxx</div><div class=\"highlight\">请于2016年10月10日前联系行政同事领取</div>",
     * "url" : "URL",
     * "btntxt":"更多"
     * },
     * "enable_id_trans": 0,
     * "enable_duplicate_check": 0,
     * "duplicate_check_interval": 1800
     * }
     * 其他类型的消息请求参数不同，具体参数请到企业微信官方文档查阅
     */
    public static final String APPLICATION_MSG_URL = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=%s";


}
