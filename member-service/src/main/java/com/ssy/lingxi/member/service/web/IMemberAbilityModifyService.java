package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.validate.MemberModifyPageDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberModifyReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdsReq;
import com.ssy.lingxi.member.model.resp.validate.MemberModifyDetailResp;
import com.ssy.lingxi.member.model.resp.validate.MemberModifyPageQueryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberModifySearchConditionResp;
import org.springframework.http.HttpHeaders;

/**
 * 会员资料变更审核相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-25
 */
public interface IMemberAbilityModifyService {

    /**
     * 获取会员变更各个步骤分页查询列表页面下拉框
     * @param loginUser 当前登录人
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberModifySearchConditionResp getModifyPageConditions(UserLoginCacheDTO loginUser, Integer roleTag);


    /**
     * 分页查询待审核变更供应商列表
     * @param pageVO 接口参数
     * @param loginUser 登录用户信息
     * @return 查询结果
     */
    PageDataResp<MemberModifyPageQueryResp> pageToModifyGrade(MemberModifyPageDataReq pageVO, UserLoginCacheDTO loginUser);

    /**
     * 分页查询“待审核变更（一级）”会员列表
     * @param loginUser 当前登录人
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberModifyPageQueryResp> pageToModifyGradeOne(UserLoginCacheDTO loginUser, MemberModifyPageDataReq pageVO, Integer roleTag);

    /**
     * “待审核变更（一级）” - 查询会员详情
     * @param loginUser 当前登录人
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberModifyDetailResp getToModifyGradeOne(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待审核变更（一级）” - 审核会员
     * @param loginUser 当前登录人
     * @param modifyVO 接口参数
     * @param roleTag 角色标签
     * @return 审核结果
     */
    void toModifyGradeOne(UserLoginCacheDTO loginUser, MemberModifyReq modifyVO, Integer roleTag);

    /**
     * “待审核变更（一级）” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void batchToModifyGradeOne(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag);

    /**
     * 分页查询“待审核变更（二级）”会员列表
     * @param loginUser 当前登录人
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberModifyPageQueryResp> pageToModifyGradeTwo(UserLoginCacheDTO loginUser, MemberModifyPageDataReq pageVO, Integer roleTag);

    /**
     * “待审核变更（二级）” - 查询会员详情
     * @param loginUser 当前登录人
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberModifyDetailResp getToModifyGradeTwo(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待审核变更（二级）” - 审核会员
     * @param loginUser 当前登录人
     * @param modifyVO 接口参数
     * @param roleTag 角色标签
     * @return 审核结果
     */
    void toModifyGradeTwo(UserLoginCacheDTO loginUser, MemberModifyReq modifyVO, Integer roleTag);

    /**
     * “待审核变更（二级）” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void batchToModifyGradeTwo(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag);

    /**
     * 分页查询“待确认会员变更”会员列表
     * @param loginUser 当前登录人
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberModifyPageQueryResp> pageToConfirmModify(UserLoginCacheDTO loginUser, MemberModifyPageDataReq pageVO, Integer roleTag);

    /**
     * “待确认会员变更” - 查询会员详情
     * @param loginUser 当前登录人
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberModifyDetailResp getToConfirmModify(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待确认会员变更” - 审核会员
     * @param loginUser 当前登录人
     * @param modifyVO 接口参数
     * @param roleTag 角色标签
     * @return 审核结果
     */
    void toConfirmModify(UserLoginCacheDTO loginUser, MemberModifyReq modifyVO, Integer roleTag);

    /**
     * “待确认会员变更” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void batchToConfirmModify(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag);
}
