package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberLoginHistoryDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员登录历史记录Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-11-25
 */
@Repository
public interface MemberLoginHistoryRepository extends JpaRepository<MemberLoginHistoryDO, Long>, JpaSpecificationExecutor<MemberLoginHistoryDO> {
    /**
     * 根据会员Id、角色Id查询最后一次的登录记录
     * @param memberId 会员Id
     * @param roleId 角色Id
     * @return 最后一次登录记录
     */
    MemberLoginHistoryDO findFirstByMemberIdAndRoleIdOrderByLoginTimeDesc(Long memberId, Long roleId);
}
