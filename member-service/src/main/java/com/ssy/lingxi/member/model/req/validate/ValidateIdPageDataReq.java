package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 会员能力 - 用户查询、审核接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ValidateIdPageDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -1503819322116497650L;

    /**
     * 审核内容Id
     */
    @NotNull(message = "审核内容Id要大于0")
    @Positive(message = "审核内容Id要大于0")
    private Long validateId;
}
