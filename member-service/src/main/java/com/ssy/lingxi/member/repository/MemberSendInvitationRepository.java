package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.invitation.MemberSendInvitationDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 发送邀请Repository
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/30 10:22
 */
public interface MemberSendInvitationRepository extends JpaRepository<MemberSendInvitationDO, Long>, JpaSpecificationExecutor<MemberSendInvitationDO> {

//    Boolean findByMemberIdAndRoleIdAndEmail(Long memberId, Long memberRoleId, String email);

    boolean existsByInvitationCode(String invitationCode);

    MemberSendInvitationDO findByMemberIdAndRoleIdAndEmail(Long memberId, Long memberRoleId, String email);

    MemberSendInvitationDO findByInvitationCode(String invitationCode);

    MemberSendInvitationDO findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);

    boolean existsBySubMemberName(String memberName);

    MemberSendInvitationDO findByMemberIdAndRoleIdAndSubMemberName(Long memberId, Long memberRoleId, String memberName);
}
