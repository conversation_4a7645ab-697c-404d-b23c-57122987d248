package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.MapResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.enums.BaiDuConfigEnum;
import com.ssy.lingxi.member.api.enums.RealNameTypeEnum;
import com.ssy.lingxi.member.api.model.req.DeleteRealNameConfigReq;
import com.ssy.lingxi.member.api.model.req.RealNameConfigReq;
import com.ssy.lingxi.member.model.resp.RealNameConfigResp;
import com.ssy.lingxi.member.service.web.IRealNameConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 实名验证配置类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/12
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/realName/config/")
public class RealNameConfigController {
    @Resource
    private IRealNameConfigService realNameConfigService;

    /**
     * 查询服务器参数配置信息列表
     */
    @GetMapping(value = "getConfigList")
    public WrapperResp<List<RealNameConfigResp>> getConfigList() {
        return WrapperUtil.success(realNameConfigService.getConfigList());
    }

    /**
     * 查询服务器参数配置-参数代码
     * @param serviceType 服务商类型: 1-百度
     */
    @GetMapping(value = "getParamCode")
    public WrapperResp<List<MapResp>> getParamCode(@RequestParam("serviceType") Integer serviceType) {
        if(serviceType.equals(RealNameTypeEnum.BAIDU.getCode())){
            return WrapperUtil.success(BaiDuConfigEnum.getCodes());
        }
        return WrapperUtil.success();
    }

    /**
     * 添加或修改实名验证公共参数
     * @param realNameConfigReq 实名验证公共参数
     * @return 操作结果
     */
    @PostMapping(value = "saveOrUpdateConfig")
    public WrapperResp<Boolean> saveOrUpdateConfig(@RequestBody @Valid RealNameConfigReq realNameConfigReq) {
        return WrapperUtil.success(realNameConfigService.saveOrUpdateConfig(realNameConfigReq));
    }

    /**
     * 删除实名验证公共参数
     * @param deleteRealNameConfigReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "deleteConfig")
    public WrapperResp<Boolean> deleteConfig(@RequestBody @Valid DeleteRealNameConfigReq deleteRealNameConfigReq) {
        return WrapperUtil.success(realNameConfigService.deleteConfig(deleteRealNameConfigReq));
    }

    /**
     * 清空实名验证内容
     * @return 是否成功
     */
    @PostMapping(value = "clearConfig")
    public WrapperResp<Boolean> clearConfig() {
        return WrapperUtil.success(realNameConfigService.clearConfig());
    }
}
