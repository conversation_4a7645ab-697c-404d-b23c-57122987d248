package com.ssy.lingxi.member.service.configManage;

import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigLabelDO;
import com.ssy.lingxi.member.model.req.configManage.MemberRegisterConfigPageDataReq;
import com.ssy.lingxi.member.model.req.configManage.MemberRegisterConfigReq;
import com.ssy.lingxi.member.model.resp.configManage.MemberConfigEnumResp;
import com.ssy.lingxi.member.model.resp.configManage.MemberConfigFieldTypeResp;
import com.ssy.lingxi.member.model.resp.configManage.MemberRegisterConfigPageResp;
import com.ssy.lingxi.member.model.resp.configManage.MemberRegisterConfigResp;

import java.util.List;

/**
 * 会员注册资料配置
 * <AUTHOR>
 * @since 2020-05-29
 * @version 2.0.0
 */
public interface IManageMemberRegisterConfigService {
    /**
     * 查询会员注册资料字段类型列表
     *
     * @return 标签列表
     */
    List<MemberConfigFieldTypeResp> getMemberConfigFieldTypeList();

    /**
     * 查询会员注册字段标签列表
     * @return 标签列表
     */
    List<MemberConfigEnumResp> getMemberConfigTagList();

    /**
     * 查询会员注册字段校验规则列表
     * @return 校验规则列表
     */
    List<MemberConfigEnumResp> getMemberConfigCheckRuleList();

    /**
     * 分页查询会员注册资料配置
     * @param registerConfigReq 接口参数
     * @return 注册资料配置分页列表
     */
    PageDataResp<MemberRegisterConfigPageResp> getRegisterConfigPage(MemberRegisterConfigPageDataReq registerConfigReq);

    /**
     * 查询会员注册资料配置详情
     * @param commonIdReq 接口参数
     * @return 注册资料配置详情
     */
    MemberRegisterConfigResp getRegisterConfigById(CommonIdReq commonIdReq);

    /**
     * 新增会员注册资料配置
     *
     * @param configReq 接口参数
     * @return 会员资料
     */
    MemberRegisterConfigResp addMemberConfig(MemberRegisterConfigReq configReq);

    /**
     * 保存会员注册资料
     * @param configVO 注册资料
     * @param parentId 父id(列表子字段需要存放父id)
     * @return
     */
    MemberRegisterConfigDO saveMemberConfig(MemberRegisterConfigReq configVO, Long parentId);

    /**
     * 修改会员注册资料配置
     *
     * @param configVO 接口参数
     * @return 会员注册资料
     */
    MemberRegisterConfigResp updateMemberConfig(MemberRegisterConfigReq configVO);

    /**
     * 修改会员注册资料配置
     * @param configVO 接口参数
     * @param memberRegisterConfigDO 会员资料
     * @param parentId 父id
     * @return
     */
    WrapperResp<List<MemberRegisterConfigLabelDO>> updateMemberConfig(MemberRegisterConfigReq configVO, MemberRegisterConfigDO memberRegisterConfigDO, Long parentId);

    /**
     * 分页查询会员注册资料配置列表
     * @param pageDataReq 接口参数
     * @return 注册资料列表
     */
    WrapperResp<PageDataResp<MemberRegisterConfigResp>> pageMemberConfig(PageDataReq pageDataReq);

    /**
     * 删除会员注册资料配置
     * @param configVO 接口参数
     * @return 操作结果
     */
    void deleteMemberConfig(MemberRegisterConfigReq configVO);

    /**
     * 删除会员配置资料
     * @param configDO 会员配置资料
     */
    void deleteMemberConfig(MemberRegisterConfigDO configDO);

    /**
     * 更改会员注册资料配置状态
     * @param configReq 接口参数
     * @return 操作结果
     */
    void updateMemberConfigStatus(MemberRegisterConfigReq configReq);
}
