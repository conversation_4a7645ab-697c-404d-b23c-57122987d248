package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskResultBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;

import java.util.List;

/**
 * 调用工作流服务Feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-14
 */
public interface IWorkflowFeignService {

    /**
     * 启动会员流程
     * @param relationDO 会员关系
     * @return 启动结果
     */
    WorkflowTaskResultBO startMemberProcess(MemberRelationDO relationDO);

    /**
     * 启动会员流程
     * @param processKey 流程的Key
     * @param memberId 执行流程的会员Id
     * @param roleId   执行流程的会员角色Id
     * @param dataId   审核数据Id
     * @return 启动结果
     */
    WorkflowTaskResultBO startMemberProcess(String processKey, Long memberId, Long roleId, Long dataId);

    /**
     * 执行会员流程
     * @param relationDO 会员关系
     * @param agree 审核意见
     * @return 执行结果
     */
    WorkflowTaskResultBO execMemberProcess(MemberRelationDO relationDO, Integer agree);

    /**
     * 执行会员流程
     * @param processKey 流程的Key
     * @param taskId 流程任务Id
     * @param memberId 执行流程的会员Id
     * @param roleId   执行流程的会员角色Id
     * @param dataId   审核数据Id
     * @param agree 审核结果
     * @return 启动结果
     */
    WorkflowTaskResultBO execMemberProcess(String processKey, String taskId, Long memberId, Long roleId, Long dataId, Integer agree);

    /**
     * 连续执行会员流程
     * @param processKey 流程的Key
     * @param taskId     流程任务Id
     * @param memberId   执行流程的会员Id
     * @param roleId     执行流程的会员角色Id
     * @param dataId     审核数据Id
     * @param execTimes  执行次数
     * @param agrees     审核结果数组
     * @return 启动结果
     */
    WorkflowTaskResultBO execMemberSerialProcess(String processKey, String taskId, Long memberId, Long roleId, Long dataId, Integer execTimes, List<Integer> agrees);

    /**
     * 查询会员流程步骤
     * @param relationDO 会员关系
     * @return 查询结果
     */
    WorkflowTaskListBO listMemberProcessSteps(MemberRelationDO relationDO);

    /**
     * 查询会员流程步骤
     * @param memberId 执行流程的会员Id
     * @param processKey 流程的ProcessKey
     * @param taskId 将要执行的工作流任务id
     * @return 查询结果
     */
    WorkflowTaskListBO listMemberProcessSteps(Long memberId, String processKey, String taskId);

    /**
     * 查询外部流程步骤
     * @param roleId 上级角色id
     * @param subRoleId 下级角色id
     * @param processKey 流程的ProcessKey
     * @param taskId 将要执行的工作流任务id
     * @return 查询结果
     */
    WrapperResp<WorkflowTaskListBO> listExternalProcessSteps(Long roleId, Long subRoleId, String processKey, String taskId);

//    /**
//     * 新增或修改会员角色时，关联工作流步骤
//     * @param memberId 会员Id
//     * @param memberRoleId 会员自定义角色Id
//     * @param memberRoleName 会员自定义角色名称
//     * @param authBOList 会员自定义角色权限列表
//     */
//    void upsertMemberRoleToProcessAsync(Long memberId, Long memberRoleId, String memberRoleName, List<AuthBO> authBOList);

    /**
     * 删除会员角色时，从关联的工作流步骤中移除会员角色
     * @param memberId 会员Id
     * @param memberRoleId 会员自定义角色Id
     */
    void removeMemberRoleInProcessAsync(Long memberId, Long memberRoleId);
}
