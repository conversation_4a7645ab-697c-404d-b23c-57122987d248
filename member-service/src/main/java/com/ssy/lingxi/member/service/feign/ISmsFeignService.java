package com.ssy.lingxi.member.service.feign;

/**
 * 调用短信服务Feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-09
 */
public interface ISmsFeignService {

    /**
     * 发送会员注册时的短信验证码
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param smsCode 短信验证码
     */
    void sendRegisterSms(String telCode, String phone, String smsCode);

    /**
     * 发送手机号登录的短信
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     * @param templateCode  短信模版
     */
    void sendPhoneLoginSms(String telCode, String phone, String smsCode, String templateCode);

    /**
     * 发送平台后台登陆页的忘记密码短信验证码
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param smsCode 短信验证码
     */
    void sendPlatformLoginForgetPwdSms(String telCode, String phone, String smsCode);

    /**
     * 发送通过手机号重设密码时的短信验证码
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param smsCode 短信验证码
     */
    void sendResetPasswordSms(String telCode, String phone, String smsCode);

    /**
     * 发送更改密码短信
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param smsCode 短信验证码
     */
    void sendChangePasswordSms(String telCode, String phone, String smsCode);

    /**
     * 发送更改邮箱短信
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param smsCode 短信验证码
     */
    void sendChangeEmailSms(String telCode, String phone, String smsCode);

    /**
     * 发送更改手机号码短信
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param smsCode 短信验证码
     */
    void sendChangePhoneSms(String telCode, String phone, String smsCode);

    /**
     * 发送更改支付密码短信
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param smsCode 短信验证码
     */
    void sendChangePayPasswordSms(String telCode, String phone, String smsCode);

    /**
     * 发送会员注销账号短信
     * @param telCode   手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     */
    void sendMemberCancellationSms(String telCode, String phone, String smsCode);

    /**
     * 发送会员注销成功短信 （仅通知）
     * @param telCode 手机号码前缀
     * @param phone       手机号码
     */
    void sendMemberCancellationSuccessSms(String telCode, String phone);

    /**
     * 发送会员注销失败短信
     * @param telCode 手机号码前缀
     * @param phone       手机号码
     * @param reason      审核失败原因
     */
    void sendMemberCancellationFailSms(String telCode, String phone, String reason);

    /**
     * 发送手机绑定短信
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param smsCode 短信验证码
     */
    void sendPhoneBindSms(String telCode, String phone, String smsCode);

}
