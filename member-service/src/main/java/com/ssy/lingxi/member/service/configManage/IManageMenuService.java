package com.ssy.lingxi.member.service.configManage;

import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.model.req.configManage.*;
import com.ssy.lingxi.member.model.req.platform.MenuSourceReq;
import com.ssy.lingxi.member.model.resp.configManage.MenuButtonResp;
import com.ssy.lingxi.member.model.resp.configManage.MenuConfigDetailsResp;
import com.ssy.lingxi.member.model.resp.configManage.MenuConfigResp;

import java.util.List;

/**
 * 业务平台 - 菜单服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-15
 */
public interface IManageMenuService {
    /**
     * 查询权限菜单列表
     * @param menuSourceReq 接口参数
     * @return 菜单配置列表
     */
    List<MenuConfigResp> getMenuConfigList(MenuSourceReq menuSourceReq);

    /**
     * 查询菜单详情
     *
     * @param commonIdReq 接口参数
     * @return 查询结果
     */
    MenuConfigDetailsResp getMenuConfigDetails(CommonIdReq commonIdReq);

    /**
     * 新增菜单
     *
     * @param addMenuReq 接口参数
     * @return 操作结果
     */
    MenuButtonResp addMenu(MenuButtonAddReq addMenuReq);

    /**
     * 新增按钮
     *
     * @param addButtonReq 接口参数
     * @return 操作结果
     */
    MenuButtonResp addButton(ButtonAddReq addButtonReq);

    /**
     * 修改菜单
     *
     * @param updateReq 接口参数
     * @return 操作结果
     */
    MenuButtonResp updateMenu(MenuButtonUpdateReq updateReq);

    /**
     * 删除菜单及关联按钮
     * @param deleteReq 接口参数
     * @return 操作结果
     */
    void deleteMenu(CommonIdReq deleteReq);

    /**
     * 修改按钮
     *
     * @param updateReq 接口参数
     * @return 操作结果
     */
    MenuButtonResp updateButton(ButtonUpdateReq updateReq);

    /**
     * 删除按钮
     *
     * @param commonIdReq 接口参数
     * @return 操作结果
     */
    void deleteButton(CommonIdReq commonIdReq);

    /**
     * 菜单重排序
     *
     * @param menuResortReq 接口参数
     * @return 操作结果
     */
    void menuResort(MenuResortReq menuResortReq);
}
