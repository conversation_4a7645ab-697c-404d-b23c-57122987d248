package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.visit.MemberVisitDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 基础信用信息配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-24
 */
@Repository
public interface MemberVisitRepository extends JpaRepository<MemberVisitDO, Long>, JpaSpecificationExecutor<MemberVisitDO> {


    List<MemberVisitDO> findByCreateMemberIdAndCreateMemberRoleIdOrderByUpdateTimeDesc(Long createMemberId, Long createMemberRoleId);
}
