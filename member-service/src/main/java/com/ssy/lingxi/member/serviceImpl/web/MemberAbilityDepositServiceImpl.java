package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.PlatformRuleTypeEnum;
import com.ssy.lingxi.component.base.enums.PurchaseContractPayTypeEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.FileBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.inspect.MemberInspectDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.req.basic.ProvinceCodeReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.basic.FileResp;
import com.ssy.lingxi.member.model.resp.basic.InnerStatusResp;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.validate.*;
import com.ssy.lingxi.member.repository.BaseMemberRuleRepository;
import com.ssy.lingxi.member.repository.MemberInspectRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberAbilityDepositService;
import com.ssy.lingxi.member.service.web.IMemberProcessRuleService;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员能力 - 会员审核入库、资料变更相关接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-19
 */
@Service
public class MemberAbilityDepositServiceImpl implements IMemberAbilityDepositService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberInspectRepository memberInspectRepository;

    @Resource
    private BaseMemberRuleRepository baseMemberRuleRepository;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    @Resource
    private IBaseMemberQualificationService baseMemberQualificationService;

    @Resource
    private IBaseMemberClassificationService baseMemberClassificationService;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private IMemberProcessRuleService memberProcessRuleService;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    /**
     * 获取会员审核入库各个步骤分页查询列表页面下拉框
     *
     * @param loginUser 登录用户
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberDepositSearchConditionResp getDepositPageConditions(UserLoginCacheDTO loginUser, Integer roleTag) {
        //规则：
        // 1). 上级会员角色为企业会员，下级会员角色为企业会员：无限制
        // 2). 上级会员角色为企业会员，下级会员角色为渠道会员：不允许创建
        // 3). 上级会员角色为渠道会员，下级会员角色为企业会员：不允许创建
        // 4). 上级会员角色为渠道会员，下级会员角色为渠道会员：判断下级会员是否有另一个服务消费者角色在关系树中

        MemberDepositSearchConditionResp conditionVO = new MemberDepositSearchConditionResp();
        //会员类型（这里返回的是Id）
        List<MemberTypeAndNameResp> memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType());
        memberTypeList.add(0, new MemberTypeAndNameResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setMemberTypes(memberTypeList);

        //会员角色（按照Id升序排序）
        List<RoleIdAndNameResp> roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), roleTag);
        roleList.add(0, new RoleIdAndNameResp(0L, MemberStringEnum.ALL.getName()));
        conditionVO.setRoles(roleList);

        //申请来源
        List<DropdownItemResp> itemList = MemberRegisterSourceEnum.toList().stream().map(s -> new DropdownItemResp(s, MemberRegisterSourceEnum.getCodeMessage(s))).collect(Collectors.toList());
        conditionVO.setSources(itemList);

        // 内部状态（待审核入库资料, 待审核入库资质, 待入库考察, 待入库分类, 待审核入库(一级), 待审核入库(二级), 待确认入库）
        List<InnerStatusResp> innerStatus = Stream.of(
                        MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL,
                        MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_QUALIFICATION,
                        MemberInnerStatusEnum.TO_INSPECT_DEPOSITORY,
                        MemberInnerStatusEnum.TO_CLASSIFY_DEPOSITORY,
                        MemberInnerStatusEnum.TO_DEPOSIT_GRADE_ONE,
                        MemberInnerStatusEnum.TO_DEPOSIT_GRADE_TWO,
                        MemberInnerStatusEnum.TO_CONFIRM_DEPOSITORY)
                .map(innerStatusEnum -> new InnerStatusResp(innerStatusEnum.getCode(), innerStatusEnum.getMessage())).collect(Collectors.toList());
        conditionVO.setInnerStatus(innerStatus);

        return conditionVO;
    }

    /**
     * 分页查询“待审核入库资料”会员列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDepositPageQueryResp> pageToVerifyDepositoryDetail(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag) {
        return pageValidateRelations(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag);
    }

    /**
     * “待审核入库资料” - 查询会员详情
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberToVerifyDepositDetailResp getToVerifyDepositoryDetail(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (relationDO.getValidateTask() == null || !StringUtils.hasLength(relationDO.getValidateTask().getProcessKey()) || !StringUtils.hasLength(relationDO.getValidateTask().getTaskId()) || !relationDO.getValidateTask().getProcessTypeEnum().equals(MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_PROCESS_DATA_ERROR);
        }

        WorkflowTaskListBO result = workflowFeignService.listMemberProcessSteps(loginUser.getMemberId(), relationDO.getValidateTask().getProcessKey(), relationDO.getValidateTask().getTaskId());

        MemberToVerifyDepositDetailResp detailVO = new MemberToVerifyDepositDetailResp();
        detailVO.setOuterVerifySteps(baseMemberValidateService.getMemberDepositOuterSteps(loginUser.getMemberRoleName(), relationDO.getSubRoleName(), roleTag));
        detailVO.setCurrentOuterStep(2);

        //内部流程
        result.getStepList().forEach(step -> {
            if (NumberUtil.notNullOrZero(roleTag)) {
                step.setStepName(step.getStepName().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag)));
            }
        });
        detailVO.setInnerVerifySteps(result.getStepList());
        detailVO.setCurrentInnerStep(result.getCurrentStep());

        detailVO.setMemberId(relationDO.getSubMemberId());
        detailVO.setValidateId(relationDO.getId());
        detailVO.setName(relationDO.getSubMember().getName());
        detailVO.setStatus(relationDO.getStatus());
        detailVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        detailVO.setInnerStatus(relationDO.getInnerStatus());
        detailVO.setInnerStatusName(SecurityStringUtil.replaceMemberPrefix(MemberInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()), roleTag));
        detailVO.setOuterStatus(relationDO.getOuterStatus());
        detailVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
        detailVO.setMemberTypeEnum(relationDO.getSubMemberTypeEnum());
        detailVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubMemberTypeEnum()));
        detailVO.setRoleName(relationDO.getSubRoleName());
        detailVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
        detailVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
        detailVO.setAccount(relationDO.getSubMember().getAccount());
        detailVO.setPhone(relationDO.getSubMember().getPhone());
        detailVO.setEmail(relationDO.getSubMember().getEmail());
        detailVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));

        //注册资料（平台审核通过后，将注册资料的版本设置为“Using"）
        detailVO.setRegisterDetails(baseMemberRegisterDetailService.groupMemberRegisterDetailText(relationDO.getSubMember(), MemberDetailVersionEnum.USING));
        //入库资料
        detailVO.setDepositDetails(baseMemberDepositDetailService.groupMemberDepositoryDetail(relationDO));
        detailVO.setDepositDetailTexts(baseMemberDepositDetailService.findMemberDepositoryDetailText(relationDO, MemberDetailVersionEnum.TO_BE_VALIDATE));
        //资质证明文件
        detailVO.setQualities(baseMemberQualificationService.findMemberQualities(relationDO));
        //内、外部历史记录
        detailVO.setInnerHistory(baseMemberHistoryService.listMemberInnerHistory(relationDO, roleTag));
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        return detailVO;
    }

    /**
     * “待审核入库资料” - 审核会员
     *
     * @param loginUser  登录用户信息
     * @param depositVO 接口参数
     * @param roleTag 角色标签
     * @return 审核结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void toVerifyDepositoryDetail(UserLoginCacheDTO loginUser, MemberToVerifyDepositReq depositVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(depositVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //检查入库状态
        if (!relationDO.getInnerStatus().equals(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_STATUS_INCORRECT);
        }

        //审核不通过的原因不能为空
        if (depositVO.getAgree().equals(MemberValidateAgreeEnum.DISAGREE.getCode()) && !StringUtils.hasLength(depositVO.getReason())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_DISAGREE_REASON_CAN_NOT_BE_EMPTY);
        }

        //检查入库资料
        baseMemberDepositDetailService.checkAndSaveMemberDepositoryDetail(relationDO, depositVO.getDepositDetails());

        //检查并保存资质文件
        baseMemberQualificationService.checkAndSaveMemberQualities(relationDO, depositVO.getQualities());

        //执行工作流任务
        baseMemberValidateService.execMemberProcess(loginUser, relationDO, depositVO.getAgree(), depositVO.getReason(), MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL, roleTag);

    }

    /**
     * “待审核入库资料” - 批量审核
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void batchVerifyDepositoryDetail(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<MemberRelationDO> relationList = relationRepository.findAllById(idVO.getValidateIds());
        if (idVO.getValidateIds().size() != relationList.size()) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //查询批量审核中，是否已经配置了入库资料的角色，如果有，返回提示
        List<MemberRoleDO> subRoles = relationList.stream().map(MemberRelationDO::getSubRole).collect(Collectors.toList());
        if (memberProcessRuleService.existDepositoryConfig(loginUser.getMemberId(), loginUser.getMemberRoleId(), subRoles)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CAN_NOT_BATCH_VALIDATE_CAUSE_OF_DEPOSIT_CONFIG_NOT_EMPTY);
        }

        baseMemberValidateService.batchExecMemberProcess(loginUser, idVO.getValidateIds(), MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL, roleTag);
    }

    /**
     * 分页查询“待审核入库资质”会员列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDepositPageQueryResp> pageToVerifyDepositoryQualification(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag) {
        return pageValidateRelations(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_QUALIFICATION, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag);
    }

    /**
     * “待审核入库资质” - 查询会员详情
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberDepositDetailResp getToVerifyDepositoryQualification(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        return getValidateMemberDetails(idVO.getValidateId(), loginUser, roleTag);
    }

    /**
     * “待审核入库资质” - 审核
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void toVerifyDepositoryQualification(UserLoginCacheDTO loginUser, ValidateAgreeReq idVO, Integer roleTag) {
        baseMemberValidateService.execMemberProcess(loginUser, idVO.getValidateId(), idVO.getAgree(), idVO.getReason(), MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_QUALIFICATION, roleTag);
    }

    /**
     * “待审核入库资质” - 批量审核
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void batchVerifyDepositoryQualifications(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberValidateService.batchExecMemberProcess(loginUser, idVO.getValidateIds(), MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_QUALIFICATION, roleTag);
    }

    /**
     * 分页查询“待入库考察”会员列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDepositPageQueryResp> pageToInspectDepository(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag) {
        return pageValidateRelations(MemberInnerStatusEnum.TO_INSPECT_DEPOSITORY, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag);
    }

    /**
     * “待入库考察” - 查询会员详情
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberDepositDetailResp getToInspectDepository(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        return getValidateMemberDetails(idVO.getValidateId(), loginUser, roleTag);
    }

    /**
     * “待入库考察” - 审核
     *
     * @param loginUser 登录用户信息
     * @param depositVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void toInspectDepository(UserLoginCacheDTO loginUser, MemberToInspectDepositReq depositVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(depositVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        MemberInspectDO memberInspectDO = new MemberInspectDO();
        memberInspectDO.setCreateTime(LocalDateTime.now());
        memberInspectDO.setInspectTime(LocalDateTime.parse(depositVO.getInspectDay().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        memberInspectDO.setSource(MemberInspectSourceEnum.BY_MEMBER_PROCESS.getCode());
        memberInspectDO.setMemberId(loginUser.getMemberId());
        memberInspectDO.setRoleId(loginUser.getMemberRoleId());
        memberInspectDO.setSubMember(relationDO.getSubMember());
        memberInspectDO.setSubRoleId(relationDO.getSubRoleId());
        memberInspectDO.setSubject(relationDO.getSubMember().getName().concat("入库考察"));
        memberInspectDO.setInspectType(MemberInspectTypeEnum.deposit.getCode());
        memberInspectDO.setByUser(userDO);
        memberInspectDO.setReason("");
        memberInspectDO.setAttachments(new ArrayList<>());
        memberInspectDO.setScore(depositVO.getScore());
        memberInspectDO.setResult(depositVO.getResult());
        memberInspectDO.setReports(CollectionUtils.isEmpty(depositVO.getReports()) ? new ArrayList<>() : depositVO.getReports().stream().map(fileUploadVO -> new FileBO(fileUploadVO.getName(), fileUploadVO.getUrl())).collect(Collectors.toList()));
        memberInspectRepository.saveAndFlush(memberInspectDO);

        baseMemberValidateService.execMemberProcess(loginUser, relationDO, depositVO.getAgree(), depositVO.getReason(), MemberInnerStatusEnum.TO_INSPECT_DEPOSITORY, roleTag);
    }

    /**
     * 分页查询“待入库分类”会员列表
     *
     * @param loginUser 登录用户信息
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDepositPageQueryResp> pageToClassifyDepository(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag) {
        return pageValidateRelations(MemberInnerStatusEnum.TO_CLASSIFY_DEPOSITORY, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag);
    }

    /**
     * “待入库分类” - 查询会员详情
     *
     * @param loginUser 登录用户信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberToClassifyDetailResp getToClassifyDepository(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return getToClassifyDetails(loginUser, relationDO, roleTag);
    }

    /**
     * “待入库分类” - “适用区域”-省列表
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public List<AreaCodeNameResp> getToClassifyProvinces(HttpHeaders headers) {
        return AreaUtil.listProvince();
    }

    /**
     * “待入库分类” - “适用区域”-根据省编码查询市列表
     *
     * @param headers Http头部信息
     * @param codeVO  接口参数
     * @return 查询结果
     */
    @Override
    public List<AreaCodeNameResp> getToClassifyCities(HttpHeaders headers, ProvinceCodeReq codeVO) {
        return AreaUtil.listCityByProvinceCode(codeVO.getProvinceCode());
    }

    /**
     * “待入库分类” - 品类信息 - 查询结算方式与发票类型
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public MemberClassifyCategoryItemResp getToClassifyCategoryItems(HttpHeaders headers) {
        MemberClassifyCategoryItemResp itemVO = new MemberClassifyCategoryItemResp();
        itemVO.setPayTypes(baseMemberRuleRepository.findByRuleTypeAndStatus(PlatformRuleTypeEnum.PURCHASE_CONTRACT.getCode(), EnableDisableStatusEnum.ENABLE.getCode()).stream().map(baseMemberRule -> new BusinessCategoryPayTypeResp(baseMemberRule.getMethodCode(), PurchaseContractPayTypeEnum.getNameByCode(baseMemberRule.getMethodCode()))).sorted(Comparator.comparingInt(BusinessCategoryPayTypeResp::getPayType)).collect(Collectors.toList()));
        itemVO.setInvoiceTypes(Arrays.stream(BusinessCategoryInvoiceTypeEnum.values()).map(e -> new BusinessCategoryInvoiceTypeResp(e.getCode(), e.getName())).sorted(Comparator.comparingInt(BusinessCategoryInvoiceTypeResp::getInvoiceType)).collect(Collectors.toList()));
        itemVO.setCurrencyTypes(Arrays.stream(CurrencyTypeEnum.values()).map(e -> new BusinessCurrencyTypeResp(e.getCode(), e.getMessage())).sorted(Comparator.comparingInt(BusinessCurrencyTypeResp::getCurrencyType)).collect(Collectors.toList()));
        itemVO.setSettlementDocuments(Arrays.stream(SettlementDocumentsEnum.values()).map(e -> new BusinessSettlementDocumentsTypeResp(e.getCode(), e.getMessage())).sorted(Comparator.comparingInt(BusinessSettlementDocumentsTypeResp::getSettlementDocumentsType)).collect(Collectors.toList()));
        itemVO.setPaymentTypes(Arrays.stream(PaymentTypeEnum.values()).map(e -> new BusinessPaymentTypeResp(e.getCode(), e.getName())).sorted(Comparator.comparingInt(BusinessPaymentTypeResp::getPaymentType)).collect(Collectors.toList()));
        itemVO.setAdvanceCharges(Arrays.stream(AdvanceChargeEnum.values()).map(e -> new BusinessAdvanceChargeTypeResp(e.getCode(), e.getMessage())).sorted(Comparator.comparingInt(BusinessAdvanceChargeTypeResp::getAdvanceChargeType)).collect(Collectors.toList()));
        return itemVO;
    }

    /**
     * “待入库分类” - 审核
     *
     * @param loginUser 登录用户信息
     * @param depositVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void toClassifyDepository(UserLoginCacheDTO loginUser, MemberToClassifyDepositReq depositVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(depositVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //检查并保存入库分类信息
        baseMemberClassificationService.saveMemberClassification(relationDO, depositVO.getRemark(), depositVO.getCurrencyType(), depositVO.getCode(), depositVO.getPartnerType(), depositVO.getMaxAmount(), depositVO.getAreaCodes(), depositVO.getCategories());

        baseMemberValidateService.execMemberProcess(loginUser, relationDO, depositVO.getAgree(), depositVO.getReason(), MemberInnerStatusEnum.TO_CLASSIFY_DEPOSITORY, roleTag);
    }

    @Override
    public PageDataResp<MemberDepositPageQueryResp> pageToDepositGrade(HttpHeaders headers, MemberDepositPageDataReq pageVO, UserLoginCacheDTO loginUser) {
        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {

            List<Predicate> orList = new ArrayList<>();

            // 前端传内部状态
            if (Objects.nonNull(pageVO.getInnerStatus())){
                List<MemberInnerStatusEnum> innerStatusEnums = new ArrayList<>();
                MemberInnerStatusEnum instance = MemberInnerStatusEnum.getInstance(pageVO.getInnerStatus());

                // 待确认入库
                if (Objects.equals(MemberInnerStatusEnum.TO_CONFIRM_DEPOSITORY, instance)){
                    innerStatusEnums.addAll(Stream.of(MemberInnerStatusEnum.DEPOSITORY_DETAIL_NOT_PASSED,
                            MemberInnerStatusEnum.DEPOSITORY_QUALIFICATION_NOT_PASSED,
                            MemberInnerStatusEnum.DEPOSITORY_INSPECTION_NOT_PASSED,
                            MemberInnerStatusEnum.DEPOSITORY_CLASSIFICATION_NOT_PASSED,
                            MemberInnerStatusEnum.DEPOSITORY_GRADE_ONE_NOT_PASSED,
                            MemberInnerStatusEnum.DEPOSITORY_GRADE_TWO_NOT_PASSED,
                            MemberInnerStatusEnum.TO_CONFIRM_DEPOSITORY).collect(Collectors.toList()));
                } else {
                    innerStatusEnums.add(instance);
                }

                orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), loginUser.getRoleTag()));
                return criteriaBuilder.and(orList.toArray(new Predicate[0]));
            }

            // 待审核入库资料
            List<MemberInnerStatusEnum> innerStatusEnums = Stream.of(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), loginUser.getRoleTag()));

            // 待审核入库资质
            innerStatusEnums = Stream.of(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_QUALIFICATION).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), loginUser.getRoleTag()));

            // 待入库考察
            innerStatusEnums = Stream.of(MemberInnerStatusEnum.TO_INSPECT_DEPOSITORY).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), loginUser.getRoleTag()));

            // 待入库分类
            innerStatusEnums = Stream.of(MemberInnerStatusEnum.TO_CLASSIFY_DEPOSITORY).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), loginUser.getRoleTag()));

            // 待一级审核
            innerStatusEnums = Stream.of(MemberInnerStatusEnum.TO_DEPOSIT_GRADE_ONE).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), loginUser.getRoleTag()));

            // 待二级审核
            innerStatusEnums = Stream.of(MemberInnerStatusEnum.TO_DEPOSIT_GRADE_TWO).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), loginUser.getRoleTag()));

            // 待审核入库
            innerStatusEnums = Stream.of(
                    MemberInnerStatusEnum.DEPOSITORY_DETAIL_NOT_PASSED,
                    MemberInnerStatusEnum.DEPOSITORY_QUALIFICATION_NOT_PASSED,
                    MemberInnerStatusEnum.DEPOSITORY_INSPECTION_NOT_PASSED,
                    MemberInnerStatusEnum.DEPOSITORY_CLASSIFICATION_NOT_PASSED,
                    MemberInnerStatusEnum.DEPOSITORY_GRADE_ONE_NOT_PASSED,
                    MemberInnerStatusEnum.DEPOSITORY_GRADE_TWO_NOT_PASSED,
                    MemberInnerStatusEnum.TO_CONFIRM_DEPOSITORY).collect(Collectors.toList());
            orList.add(getPredicate(root, query, criteriaBuilder, innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), loginUser.getRoleTag()));

            return criteriaBuilder.or(orList.toArray(new Predicate[0]));
        };
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> this.createQueryVO(relationDO, loginUser.getRoleTag())).collect(Collectors.toList()));
    }

    /**
     * 分页查询“待审核入库(一级)”会员列表
     *
     * @param loginUser 登录用户信息
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDepositPageQueryResp> pageToDepositGradeOne(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag) {
        return pageValidateRelations(MemberInnerStatusEnum.TO_DEPOSIT_GRADE_ONE, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag);
    }

    /**
     * “待审核入库(一级)” - 查询会员详情
     *
     * @param loginUser 登录用户信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberDepositGradeDetailResp getToDepositGradeOne(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return getDepositGradeDetails(loginUser, relationDO, roleTag);
    }

    /**
     * “待审核入库(一级)” - 审核
     *
     * @param loginUser 登录用户信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void toDepositGradeOne(UserLoginCacheDTO loginUser, ValidateAgreeReq idVO, Integer roleTag) {
        baseMemberValidateService.execMemberProcess(loginUser, idVO.getValidateId(), idVO.getAgree(), idVO.getReason(), MemberInnerStatusEnum.TO_DEPOSIT_GRADE_ONE, roleTag);
    }

    /**
     * “待审核入库(一级)” - 批量审核
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void batchDepositGradeOne(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberValidateService.batchExecMemberProcess(loginUser, idVO.getValidateIds(), MemberInnerStatusEnum.TO_DEPOSIT_GRADE_ONE, roleTag);
    }

    /**
     * 分页查询“待审核入库(二级)”会员列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDepositPageQueryResp> pageToDepositGradeTwo(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag) {
        return pageValidateRelations(MemberInnerStatusEnum.TO_DEPOSIT_GRADE_TWO, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag);
    }

    /**
     * “待审核入库(二级)” - 查询会员详情
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberDepositGradeDetailResp getToDepositGradeTwo(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return getDepositGradeDetails(loginUser, relationDO, roleTag);
    }

    /**
     * “待审核入库(二级)” - 审核
     *
     * @param loginUser 登录用户信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void toDepositGradeTwo(UserLoginCacheDTO loginUser, ValidateAgreeReq idVO, Integer roleTag) {
        baseMemberValidateService.execMemberProcess(loginUser, idVO.getValidateId(), idVO.getAgree(), idVO.getReason(), MemberInnerStatusEnum.TO_DEPOSIT_GRADE_TWO, roleTag);
    }

    /**
     * “待审核入库(二级)” - 批量审核
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void batchDepositGradeTwo(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberValidateService.batchExecMemberProcess(loginUser, idVO.getValidateIds(), MemberInnerStatusEnum.TO_DEPOSIT_GRADE_TWO, roleTag);
    }

    /**
     * 分页查询“待确认入库”会员列表
     *
     * @param loginUser 登录用户信息
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDepositPageQueryResp> pageToConfirmDepository(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag) {
        List<MemberInnerStatusEnum> innerStatusEnums = Stream.of(
                MemberInnerStatusEnum.DEPOSITORY_DETAIL_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_QUALIFICATION_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_INSPECTION_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_CLASSIFICATION_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_GRADE_ONE_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_GRADE_TWO_NOT_PASSED,
                MemberInnerStatusEnum.TO_CONFIRM_DEPOSITORY).collect(Collectors.toList());
        return pageValidateRelations(innerStatusEnums, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getMemberType(), pageVO.getRoleId(), pageVO.getSource(), pageVO.getCurrent(), pageVO.getPageSize(), roleTag, pageVO.getAccount());
    }

    /**
     * “待确认入库” - 查询会员详情
     *
     * @param loginUser 登录用户信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberDepositGradeDetailResp getToConfirmDepository(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return getDepositGradeDetails(loginUser, relationDO, roleTag);
    }

    /**
     * “待确认入库” - 审核
     *
     * @param loginUser 登录用户信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void toConfirmDepository(UserLoginCacheDTO loginUser, ValidateAgreeReq idVO, Integer roleTag) {
        List<MemberInnerStatusEnum> innerStatusEnums = Stream.of(
                MemberInnerStatusEnum.DEPOSITORY_DETAIL_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_QUALIFICATION_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_INSPECTION_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_CLASSIFICATION_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_GRADE_ONE_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_GRADE_TWO_NOT_PASSED,
                MemberInnerStatusEnum.TO_CONFIRM_DEPOSITORY).collect(Collectors.toList());
        baseMemberValidateService.execMemberProcess(loginUser, idVO.getValidateId(), idVO.getAgree(), idVO.getReason(), innerStatusEnums, roleTag, idVO.getBrandCode(), idVO.getSimpleMemberName());
    }

    /**
     * “待确认入库” - 批量审核
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public void batchConfirmDepositories(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<MemberInnerStatusEnum> innerStatusEnums = Stream.of(
                MemberInnerStatusEnum.DEPOSITORY_DETAIL_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_QUALIFICATION_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_INSPECTION_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_CLASSIFICATION_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_GRADE_ONE_NOT_PASSED,
                MemberInnerStatusEnum.DEPOSITORY_GRADE_TWO_NOT_PASSED,
                MemberInnerStatusEnum.TO_CONFIRM_DEPOSITORY).collect(Collectors.toList());
        baseMemberValidateService.batchExecMemberProcess(loginUser, idVO.getValidateIds(), innerStatusEnums, roleTag);
    }

    /**
     * （重载）分页查询入库会员列表
     *
     * @param innerStatusEnum 内部状态
     * @param memberId        当前会员Id
     * @param roleId          当前会员角色Id
     * @param subMemberName   下级会员名称
     * @param subMemberTypeId 下级会员会员类型
     * @param subRoleId       下级会员角色Id
     * @param source          会员注册来源
     * @param current         分页参数 - 当前页
     * @param pageSize        分页参数 - 每页行数
     * @param roleTag         角色标签
     * @return 查询结果
     */
    private PageDataResp<MemberDepositPageQueryResp> pageValidateRelations(MemberInnerStatusEnum innerStatusEnum, Long memberId, Long roleId, String subMemberName, Long subMemberTypeId, Long subRoleId, Integer source, int current, int pageSize, Integer roleTag) {
        return pageValidateRelations(Stream.of(innerStatusEnum).collect(Collectors.toList()), memberId, roleId, subMemberName, subMemberTypeId, subRoleId, source, current, pageSize, roleTag, null);
    }

    /**
     * 获取查询条件
     * @param root must not be {@literal null}.
     * @param query must not be {@literal null}.
     * @param criteriaBuilder must not be {@literal null}.
     * @param innerStatusEnums 内部状态List
     * @param memberId 当前会员Id
     * @param roleId   当前会员角色Id
     * @param subMemberName  下级会员名称
     * @param subMemberTypeId 下级会员会员类型
     * @param subRoleId  下级会员角色Id
     * @param source     会员注册来源
     * @return Predicate
     */
    private Predicate getPredicate(Root<?> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder, List<MemberInnerStatusEnum> innerStatusEnums, Long memberId, Long roleId, String subMemberName, Long subMemberTypeId, Long subRoleId, Integer source, Integer roleTag){

        List<Predicate> cond = new ArrayList<>();
        cond.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), memberId));
        cond.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), roleId));
        cond.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));

        // 内部状态
        if(innerStatusEnums.size() == 1) {
            cond.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), innerStatusEnums.get(0).getCode()));
        } else {
            cond.add(root.get("innerStatus").in(innerStatusEnums.stream().map(MemberInnerStatusEnum::getCode).collect(Collectors.toList())));
        }

        // 外部状态只能为“入库审核中”
        cond.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), MemberOuterStatusEnum.DEPOSITING.getCode()));

        // 下级会员名称
        if(StringUtils.hasLength(subMemberName)) {
            Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
            cond.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + subMemberName.trim() + "%"));
        }

        // 下级会员类型
        if(NumberUtil.notNullOrZero(subMemberTypeId)) {
            Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.LEFT);
            cond.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), subMemberTypeId));
            if (NumberUtil.notNullOrZero(roleTag)) {
                List<Integer> roleTagList = new ArrayList<>();
                if (RoleTagEnum.getCustomer().contains(roleTag)) {
                    roleTagList = RoleTagEnum.getSupplier();
                } else if (RoleTagEnum.getSupplier().contains(roleTag)) {
                    roleTagList = RoleTagEnum.getCustomer();
                }
                cond.add(criteriaBuilder.in(subRoleJoin.get("roleTag")).value(roleTagList));
            }
        }

        // 下级会员角色
        if(NumberUtil.notNullOrZero(subRoleId)) {
            cond.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), subRoleId));
        }

        // 下级会员注册来源
        if(NumberUtil.notNullOrZero(source)) {
            Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
            cond.add(criteriaBuilder.equal(subMemberJoin.get("source").as(Integer.class), source));
        }

        return criteriaBuilder.and(cond.toArray(new Predicate[0]));
    }

    /**
     * 分页查询入库会员列表
     *
     * @param innerStatusEnums 内部状态List
     * @param memberId         当前会员Id
     * @param roleId           当前会员角色Id
     * @param subMemberName    下级会员名称
     * @param subMemberTypeId  下级会员会员类型
     * @param subRoleId        下级会员角色Id
     * @param source           会员注册来源
     * @param current          分页参数 - 当前页
     * @param pageSize         分页参数 - 每页行数
     * @param roleTag          角色标签
     * @return 查询结果
     */
    private PageDataResp<MemberDepositPageQueryResp> pageValidateRelations(List<MemberInnerStatusEnum> innerStatusEnums, Long memberId, Long roleId, String subMemberName, Long subMemberTypeId, Long subRoleId, Integer source, int current, int pageSize, Integer roleTag, String account) {
        Pageable pageable = PageRequest.of(current - 1, pageSize, Sort.by("createTime").descending());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), memberId));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), roleId));
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));

            if (innerStatusEnums.size() == 1) {
                list.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), innerStatusEnums.get(0).getCode()));
            } else {
                Expression<Integer> exp = root.get("innerStatus");
                list.add(exp.in(innerStatusEnums.stream().map(MemberInnerStatusEnum::getCode).collect(Collectors.toList())));
            }

            //外部状态只能为“入库审核中”
            list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), MemberOuterStatusEnum.DEPOSITING.getCode()));

            //下级会员名称
            if (StringUtils.hasLength(subMemberName)) {
                Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + subMemberName.trim() + "%"));
            }

            //会员信息
            if (StringUtils.hasText(account)) {
                Join<MemberRelationDO, MemberDO> classificationJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(classificationJoin.get("account").as(String.class), "%" + account.trim() + "%"));
            }
            //下级会员类型
            if(NumberUtil.notNullOrZero(subMemberTypeId)) {
                Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), subMemberTypeId));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), roleTag));
            }

            //下级会员角色
            if (NumberUtil.notNullOrZero(subRoleId)) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), subRoleId));
            }

            //下级会员注册来源
            if (NumberUtil.notNullOrZero(source)) {
                Join<MemberRelationDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.equal(subMemberJoin.get("source").as(Integer.class), source));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> this.createQueryVO(relationDO, roleTag)).collect(Collectors.toList()));
    }

    /**
     * 创建展示类
     * @param relationDO  会员上下级关系
     * @return MemberDepositPageQueryVO
     */
    private MemberDepositPageQueryResp createQueryVO(MemberRelationDO relationDO, Integer roleTag){
        MemberDepositPageQueryResp queryVO = new MemberDepositPageQueryResp();
        queryVO.setValidateId(relationDO.getId());
        queryVO.setMemberId(relationDO.getSubMemberId());
        queryVO.setName(relationDO.getSubMember().getName());
        queryVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
        queryVO.setRoleName(relationDO.getSubRole().getRoleName());
        queryVO.setSourceName(MemberRegisterSourceEnum.getCodeMessage(relationDO.getSubMember().getSource()));
        queryVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
        queryVO.setInnerStatus(relationDO.getInnerStatus());
        queryVO.setInnerStatusName(NumberUtil.notNullOrZero(roleTag) ? MemberInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()).replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag))
                : MemberInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()));
        queryVO.setOuterStatus(relationDO.getOuterStatus());
        queryVO.setDepositTime(relationDO.getDepositTime() == null ? null : relationDO.getDepositTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
        queryVO.setStatus(relationDO.getStatus());
        queryVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        queryVO.setOuterStatusName(NumberUtil.notNullOrZero(roleTag) ? MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()).replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag))
                : MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));
        return queryVO;
    }

    /**
     * 查询会员详细信息
     *
     * @param validateId 审核内容Id
     * @param loginUser  登录用户
     * @param roleTag 角色标签
     * @return 查询结果
     */
    private MemberDepositDetailResp getValidateMemberDetails(Long validateId, UserLoginCacheDTO loginUser, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (relationDO.getValidateTask() == null || !StringUtils.hasLength(relationDO.getValidateTask().getProcessKey()) || !StringUtils.hasLength(relationDO.getValidateTask().getTaskId()) || !relationDO.getValidateTask().getProcessTypeEnum().equals(MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_PROCESS_DATA_ERROR);
        }

        return getValidateMemberDetails(relationDO, loginUser, roleTag);
    }

    /**
     * 查询“审核入库”（一级）和（二级）的会员详情
     *
     * @param loginUser  登录用户
     * @param relationDO 会员关系
     * @param roleTag 角色标签
     * @return 查询结果
     */
    private MemberDepositGradeDetailResp getDepositGradeDetails(UserLoginCacheDTO loginUser, MemberRelationDO relationDO, Integer roleTag) {
        MemberToClassifyDetailResp result = getToClassifyDetails(loginUser, relationDO, roleTag);

        MemberDepositGradeDetailResp detailVO = new MemberDepositGradeDetailResp();
        BeanUtils.copyProperties(result, detailVO);

        detailVO.setClassification(baseMemberClassificationService.getMemberClassification(relationDO));
        return detailVO;
    }

    /**
     * 查询“待入库分类”会员详情
     *
     * @param loginUser  登录用户
     * @param relationDO 会员关系
     * @param roleTag 角色标签
     * @return 查询结果
     */
    private MemberToClassifyDetailResp getToClassifyDetails(UserLoginCacheDTO loginUser, MemberRelationDO relationDO, Integer roleTag) {
        MemberInspectDO inspectDO = memberInspectRepository.findFirstByMemberIdAndRoleIdAndSubMemberAndSubRoleIdAndSource(loginUser.getMemberId(), loginUser.getMemberRoleId(), relationDO.getSubMember(), relationDO.getSubRoleId(), MemberInspectSourceEnum.BY_MEMBER_PROCESS.getCode());

        MemberDepositDetailResp result = getValidateMemberDetails(relationDO, loginUser, roleTag);


        MemberToClassifyDetailResp detailVO = new MemberToClassifyDetailResp();
        BeanUtils.copyProperties(result, detailVO);
        //在MemberToClassifyDetailVO的无参构造函数对字段做了初始化
        if (inspectDO != null) {
            MemberInspectResp inspectVO = new MemberInspectResp();
            inspectVO.setInspectDay(inspectDO.getInspectTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            inspectVO.setScore(MemberConstant.BIG_DECIMAL_FORMAT.format(inspectDO.getScore()));
            inspectVO.setResult(StringUtils.hasLength(inspectDO.getResult()) ? inspectDO.getResult() : "");
            inspectVO.setReports(CollectionUtils.isEmpty(inspectDO.getReports()) ? new ArrayList<>() : inspectDO.getReports().stream().map(fileBO -> new FileResp(fileBO.getName(), fileBO.getUrl())).collect(Collectors.toList()));
            detailVO.setInspection(inspectVO);
        }

        detailVO.setPartnerTypes(MemberPartnerTypeEnum.toDropdownList());

        return detailVO;
    }

    /**
     * 查询会员详细信息
     *
     * @param relationDO 会员关系
     * @param loginUser  登录用户
     * @param roleTag 角色标签
     * @return 查询结果
     */
    private MemberDepositDetailResp getValidateMemberDetails(MemberRelationDO relationDO, UserLoginCacheDTO loginUser, Integer roleTag) {
        WorkflowTaskListBO result = workflowFeignService.listMemberProcessSteps(loginUser.getMemberId(), relationDO.getValidateTask().getProcessKey(), relationDO.getValidateTask().getTaskId());

        MemberDepositDetailResp detailVO = new MemberDepositDetailResp();
        detailVO.setOuterVerifySteps(baseMemberValidateService.getMemberDepositOuterSteps(loginUser.getMemberRoleName(), relationDO.getSubRoleName(), roleTag));
        detailVO.setCurrentOuterStep(2);

        //内部流程
        result.getStepList().forEach(step -> {
            if (NumberUtil.notNullOrZero(roleTag)) {
                step.setStepName(step.getStepName().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag)));
            }
        });
        detailVO.setInnerVerifySteps(result.getStepList());
        detailVO.setCurrentInnerStep(result.getCurrentStep());

        detailVO.setValidateId(relationDO.getId());
        detailVO.setMemberId(relationDO.getSubMemberId());
        detailVO.setName(relationDO.getSubMember().getName());
        detailVO.setOuterStatus(relationDO.getOuterStatus());
        detailVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
        detailVO.setInnerStatus(relationDO.getInnerStatus());
        detailVO.setInnerStatusName(SecurityStringUtil.replaceMemberPrefix(MemberInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()), roleTag));
        detailVO.setStatus(relationDO.getStatus());
        detailVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        detailVO.setMemberTypeEnum(relationDO.getSubMemberTypeEnum());
        detailVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubMemberTypeEnum()));
        detailVO.setRoleName(relationDO.getSubRoleName());
        detailVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
        detailVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
        detailVO.setAccount(relationDO.getSubMember().getAccount());
        detailVO.setPhone(relationDO.getSubMember().getPhone());
        detailVO.setEmail(relationDO.getSubMember().getEmail());
        detailVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));

        //注册资料，由于会员入库流程之前必须是平台审核通过，而平台审核通过时注册资料的版本已经变为“Using"了，所以这里查询的版本是”Using"
        detailVO.setRegisterDetails(baseMemberRegisterDetailService.groupMemberRegisterDetailText(relationDO.getSubMember(), MemberDetailVersionEnum.USING));

        //入库资料
        detailVO.setDepositDetails(baseMemberDepositDetailService.mergeMemberDepositoryDetailText(relationDO));

        //资质证明
        detailVO.setQualities(baseMemberQualificationService.findMemberQualities(relationDO));

        //内、外部历史记录
        detailVO.setInnerHistory(baseMemberHistoryService.listMemberInnerHistory(relationDO, roleTag));
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        return detailVO;
    }
}
