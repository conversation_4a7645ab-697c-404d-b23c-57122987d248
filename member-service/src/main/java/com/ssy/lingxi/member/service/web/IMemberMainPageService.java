package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.member.model.req.basic.MemberLogoReq;
import com.ssy.lingxi.member.model.req.basic.MemberUpdateRegisterDetailReq;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRegisterDetailResp;
import org.springframework.http.HttpHeaders;

/**“首页” - 相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-01-23
 */
public interface IMemberMainPageService {

    /**
     * 新增或修改用户Logo
     * @param headers Http头部信息
     * @param logoVO 接口参数
     * @return 新增结果
     */
    void addMemberUserLogo(HttpHeaders headers, MemberLogoReq logoVO);

    /**
     * “首页” - 审核不通过时，查询会员注册资料信息
     * @param headers Http头部信息
     * @return 查询结果
     */
    MemberRegisterDetailResp getMemberRegisterDetail(HttpHeaders headers);

    /**
     * “首页” - 审核不通过时，修改会员注册资料
     * @param headers Http头部信息
     * @param detailVO 接口参数
     */
    void updateMemberRegisterDetail(HttpHeaders headers, MemberUpdateRegisterDetailReq detailVO);

    /**
     * 判断当前登录会员，是否指定会员的上级会员
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    Boolean isUpperMember(HttpHeaders headers, MemberAndRoleIdReq idVO);
}
