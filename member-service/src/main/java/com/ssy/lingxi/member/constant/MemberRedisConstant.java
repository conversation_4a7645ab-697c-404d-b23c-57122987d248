package com.ssy.lingxi.member.constant;

import lombok.Data;

/**
 * 会员服务redis相关常量
 *
 * <AUTHOR>
 * @version 3.0.0
 * @date 2024/1/11
 */
@Data
public class MemberRedisConstant {
    //**************************************************** 注册相关 ****************************************************
    /**
     * 会员注册：手机验证码的RedisKey前缀
     */
    public static final String REGISTER_BY_PHONE_REDIS_KEY_PREFIX = "REGISTER:PHONE:";

    /**
     * 会员注册：短信验证码的RedisKey前缀
     */
    public static final String REGISTER_BY_EMAIL_REDIS_KEY_PREFIX = "REGISTER:EMAIL:";

    /**
     * 会员注册：滑块信息的RedisKey前缀
     */
    public static final String REGISTER_CAPTCHA_IMG_REDIS_KEY_PREFIX = "REGISTER:CAPTCHA_IMG:";

    //**************************************************** 登录相关 ****************************************************
    /**
     * 用户登录：授权码相关RedisKey前缀
     */
    public static final String LOGIN_QR_AUTH_CODE_REDIS_KEY_PREFIX = "LOGIN_QR:AUTH_CODE:";
    public static final String LOGIN_QR_AUTH_INFO_REDIS_KEY_PREFIX = "LOGIN_QR:AUTH_INFO:";

    /**
     * 用户登录：失败次数的RedisKey前缀
     */
    public static final String LOGIN_FAILURES_REDIS_KEY_PREFIX = "LOGIN_FAILURES:USER:";

    /**
     * PC登录：手机验证码的RedisKey前缀
     */
    public static final String LOGIN_PC_BY_PHONE_REDIS_KEY_PREFIX = "LOGIN_PC:PHONE:";

    /**
     * PC登录：邮箱验证码的RedisKey前缀
     */
    public static final String LOGIN_PC_BY_EMAIL_REDIS_KEY_PREFIX = "LOGIN_PC:EMAIL:";

    /**
     * 移动端登录：手机验证短信的RedisKey前缀
     */
    public static final String LOGIN_MOBILE_BY_PHONE_REDIS_KEY_PREFIX = "LOGIN_MOBILE:PHONE:";

    /**
     * 移动端登录：邮箱验证码的RedisKey前缀
     */
    public static final String LOGIN_MOBILE_BY_EMAIL_REDIS_KEY_PREFIX = "LOGIN_MOBILE:EMAIL:";

    /**
     * 平台后台登录：手机验证码的RedisKey前缀
     */
    public static final String LOGIN_MANAGE_BY_PHONE_REDIS_KEY_PREFIX = "LOGIN_MANAGE:PHONE:";

    /**
     * 平台后台登录：邮箱验证码的RedisKey前缀
     */
    public static final String LOGIN_MANAGE_BY_EMAIL_REDIS_KEY_PREFIX = "LOGIN_MANAGE:EMAIL:";

    //************************************************** 修改信息相关 **************************************************
    /**
     * 未登录重制密码（非平台后台）：手机验证码的RedisKey前缀
     */
    public static final String RESET_PWD_BY_PHONE_REDIS_KEY_PREFIX = "RESET_PWD:PHONE:";

    /**
     * 未登录重制密码(非平台后台) ：邮箱验证码的RedisKey前缀
     */
    public static final String RESET_PWD_BY_EMAIL_REDIS_KEY_PREFIX = "RESET_PWD:EMAIL:";

    /**
     * 修改密码：手机验证码的RedisKey前缀
     */
    public static final String UPDATE_PWD_BY_PHONE_REDIS_KEY_PREFIX = "UPDATE_PWD:PHONE:USER:";

    /**
     * 修改密码：邮箱验证码的RedisKey前缀
     */
    public static final String UPDATE_PWD_BY_EMAIL_REDIS_KEY_PREFIX = "UPDATE_PWD:EMAIL:USER:";

    /**
     * 修改邮箱：手机验证码的RedisKey前缀
     */
    public static final String UPDATE_EMAIL_BY_PHONE_REDIS_KEY_PREFIX = "UPDATE_EMAIL:PHONE:USER:";

    /**
     * 修改邮箱：邮箱验证码的RedisKey前缀
     */
    public static final String UPDATE_EMAIL_BY_NEW_EMAIL_REDIS_KEY_PREFIX = "UPDATE_EMAIL:EMAIL:USER:";

    /**
     * 修改手机号：手机验证码的RedisKey前缀
     */
    public static final String UPDATE_PHONE_BY_NEW_PHONE_REDIS_KEY_PREFIX = "UPDATE_PHONE:PHONE:USER:";

    /**
     * 修改手机号：邮箱验证码的RedisKey前缀
     */
    public static final String UPDATE_PHONE_BY_EMAIL_REDIS_KEY_PREFIX = "UPDATE_PHONE:EMAIL:USER:";

    /**
     * 修改支付密码：手机验证码的RedisKey前缀
     */
    public static final String UPDATE_PAY_PWD_BY_PHONE_REDIS_KEY_PREFIX = "UPDATE_PAY_PWD:PHONE:USER:";

    /**
     * 修改支付密码：邮箱验证码的RedisKey前缀
     */
    public static final String UPDATE_PAY_PWD_BY_EMAIL_REDIS_KEY_PREFIX = "UPDATE_PAY_PWD:EMAIL:USER:";

    //**************************************************** 其他 ****************************************************
    /**
     * 会员注销：手机验证码的RedisKey前缀
     */
    public static final String MEMBER_CANCELLATION_BY_PHONE_REDIS_KEY_PREFIX = "MEMBER_CANCELLATION:PHONE:";

    /**
     * 变更申请单：流水号的RedisKey
     */
    public static final String CHANGE_REQUEST_FORM_SERIAL_REDIS_KEY = "CHANGE_REQUEST_SERIAL";

    /**
     * 验证码：安全后缀（用来防止短时间内重复发送验证码）
     */
    public static final String SMS_CODE_SECURITY_SUFFIX = "SMS_SECURITY";

    /**
     * 微信快速登录前缀
     */
    public static final String WX_LOGIN_PHONE_CODE_PREFIX  = "WX_LOGIN_PHONE_CODE:%s";

    /**
     * 手机号绑定前缀
     */
    public static final String PHONE_BIND_CODE_PREFIX = "PHONE_BIND_CODE:%s";

    /**
     * 会员编码前缀
     */
    public static final String MEMBER_CODE_PREFIX = "MEMBER_CODE";

    /**
     * 企业编码前缀
     */
    public static final String CORPORATION_CODE_PREFIX = "CORPORATION_CODE";

    /**
     * 实控人前缀
     */
    public static final String ACTUAL_CONTROLLER_PREFIX = "ACTUAL_CONTROLLER_PREFIX";

    /**
     * 人脸识别标识
     */
    public static final String FACE_RECOGNITION_FLAG = "FACE_RECOGNITION_FLAG";

}
