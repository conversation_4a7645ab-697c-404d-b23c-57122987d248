package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.AdvanceChargeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 预付款枚举参数校验注解
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {AdvanceChargeValidator.class})
public @interface AdvanceChargeAnnotation {
    String message() default "预付款参数值不在定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
