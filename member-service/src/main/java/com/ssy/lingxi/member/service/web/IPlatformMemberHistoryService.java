package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.basic.MemberHistoryPageDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberInnerHistoryDetailResp;
import com.ssy.lingxi.member.model.resp.basic.MemberOuterHitoryDetailResp;
import org.springframework.http.HttpHeaders;

/**
 * 平台后台 - 日志中心 - 会员内、外流转记录查询相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-03
 */
public interface IPlatformMemberHistoryService {
    /**
     * 分页查询外部流转记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberOuterHitoryDetailResp> findOuterHistories(HttpHeaders headers, MemberHistoryPageDataReq pageVO);

    /**
     * 分页查询内部流转记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberInnerHistoryDetailResp> findInnerHistories(HttpHeaders headers, MemberHistoryPageDataReq pageVO);
}
