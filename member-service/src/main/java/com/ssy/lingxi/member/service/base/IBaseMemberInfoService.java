package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.info.MemberInfoAddRoleReq;
import com.ssy.lingxi.member.model.req.info.MemberInfoUpdateDepositDetailReq;
import com.ssy.lingxi.member.model.req.info.MemberInfoUpdateRegisterDetailReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberDetailCreditHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.mobile.MobileUpdateDepositDetailReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.UpperMemberShowResp;
import com.ssy.lingxi.member.model.resp.info.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordRectifyResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.mobile.MobileInfoApplyButtonResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileUpdateDepositDetailQueryResp;
import com.ssy.lingxi.member.model.resp.platform.RoleRuleManageResp;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 会员信息管理查询服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-05
 */
public interface IBaseMemberInfoService {

    /**
     * 获取分页查询页面下拉框内容
     * @return 下拉框内容
     */
    MemberInfoSearchConditionResp getPageSearchConditions();

    /**
     * 分页、模糊查询归属会员列表（带积分权益）
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param current 当前页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MobileInfoLevelRightResp> pageUpperMemberLevelRights(Long subMemberId, Long subRoleId, int current, int pageSize);

    /**
     * 分页、模糊查询归属会员列表
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param name 上级会员名称
     * @param startDate 申请起始日期，格式为yyyy-MM-dd
     * @param endDate   申请结束日期，格式为yyyy-MM-dd
     * @param outerStatus 审核状态（外部）
     * @param current 当前页（从1开始）
     * @param pageSize 每页行数
     * @param enableMultiTenancy 是否开启SAAS多租户部署
     * @param roleTag 标签类型
     * @return 查询结果
     */
    PageDataResp<UpperMemberInfoResp> pageUpperMembers(Long subMemberId, Long subRoleId, String name, String startDate, String endDate, Integer outerStatus, int current, int pageSize, Boolean enableMultiTenancy, Long categoryId, Integer currencyType, String code, Map<String,Object> memberConfigs, Integer roleTag);

    /**
     * “申请会员”页面，查询申请按钮状态和文本
     * @param loginUser 登录用户
     * @param shopType 商城类型
     * @param upperMemberId 上级会员Id
     * @param upperRoleId 上级角色Id
     * @return 查询结果
     */
    MemberInfoApplyButtonResp getApplyCondition(UserLoginCacheDTO loginUser, Integer shopType, Long upperMemberId, Long upperRoleId);

    /**
     * App - “申请会员”页面，查询申请按钮状态和文本
     * @param loginUser 登录用户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId 上级角色Id
     * @return 查询结果
     */
    MobileInfoApplyButtonResp getMobileApplyCondition(UserLoginCacheDTO loginUser, Long upperMemberId, Long upperRoleId);

    /**
     * “邀请会员”页面，查询邀请按钮状态和文本
     * @param loginUser 登录用户
     * @param shopType 商城类型
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级角色Id
     * @return 查询结果
     */
    MemberInfoInviteButtonResp getInviteCondition(UserLoginCacheDTO loginUser, Integer shopType, Long subMemberId, Long subRoleId);

    /**
     * “申请会员”页面，会员注册资料信息
     * @param loginUser 登录用户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId 上级会员角色Id
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberInfoApplyRegisterDetailResp getApplyRegisterDetail(UserLoginCacheDTO loginUser, Long upperMemberId, Long upperRoleId, Integer roleTag);

    /**
     * “申请会员”页面，会员入库资料信息
     * @param loginUser 登录用户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId 上级会员角色Id
     * @return 查询结果
     */
    MemberInfoApplyDepositDetailResp getApplyDepositDetail(UserLoginCacheDTO loginUser, Long upperMemberId, Long upperRoleId);

    /**
     * 获取“修改会员信息”页面，会员注册资料信息
     * @param loginUser 登录用户
     * @param validateId 会员关系Id
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberInfoUpdateDetailResp getMemberRegisterDetail(UserLoginCacheDTO loginUser, Long validateId, Integer roleTag);

    /**
     * App - 获取“修改会员信息”页面，会员注册资料信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 操作结果
     */
    MobileInfoUpdateDetailResp getMobileMemberRegisterDetail(UserLoginCacheDTO loginUser, Long validateId);

    /**
     * 修改会员注册信息
     * @param loginUser 登录用户
     * @param detailVO 接口参数
     * @return 修改结果
     */
    void updateMemberRegisterDetail(UserLoginCacheDTO loginUser, MemberInfoUpdateRegisterDetailReq detailVO);

    /**
     * 获取“修改会员信息”页面，会员入库资料信息
     * @param loginUser 登录用户
     * @param validateId 会员关系Id
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberInfoUpdateDepositDetailResp getMemberDepositDetail(UserLoginCacheDTO loginUser, Long validateId, Integer roleTag);

    /**
     * App - 获取“修改会员信息”页面，会员入库资料信息
     * @param loginUser 登录用户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级会员角色Id
     * @return 查询结果
     */
    MobileUpdateDepositDetailQueryResp getMemberDepositDetail(UserLoginCacheDTO loginUser, Long upperMemberId, Long upperRoleId);

    /**
     * App - 修改会员入库信息
     * @param loginUser 登录用户
     * @param detailVO 接口参数
     * @param roleTag 角色标签
     * @return 修改结果
     */
    void updateMobileDepositDetail(UserLoginCacheDTO loginUser, MobileUpdateDepositDetailReq detailVO, Integer roleTag);

    /**
     * 修改会员入库信息
     * @param loginUser 登录用户
     * @param detailVO 接口参数
     * @param roleTag 角色标签
     * @return 修改结果
     */
    void updateMemberDepositDetail(UserLoginCacheDTO loginUser, MemberInfoUpdateDepositDetailReq detailVO, Integer roleTag);

    /**
     * 会员详情 - 会员基本信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberInfoBasicDetailResp getMemberBasicDetail(UserLoginCacheDTO loginUser, Long validateId, Integer roleTag);

    /**
     * App - 会员基本信息
     * @param relationDO 会员关系
     * @return 查询结果
     */
    MobileInfoBasicDetailResp getMobileMemberBasicDetail(MemberRelationDO relationDO);

    /**
     * 会员详情 - 会员档案信息（入库信息）
     * @param loginUser 登录用户
     * @param validateId 会员关系Id
     * @return 查询结果
     */
    MemberInfoDepositDetailResp getMemberArchives(UserLoginCacheDTO loginUser, Long validateId);

    /**
     * 会员详情- 会员档案 - 分页查询考评信息
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberAppraisalPageQueryResp> pageMemberAppraisal(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员档案 - 分页查询整改信息
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberRecordRectifyResp> pageMemberRecordRectify(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员等级信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    MemberValidateDetailLevelResp getMemberDetailLevel(UserLoginCacheDTO loginUser, Long validateId);

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO, DateTimeFormatter formatter);

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     * @param relationDO 会员关系
     * @param current 当前页
     * @param pageSize 每页行数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(MemberRelationDO relationDO, int current, int pageSize, DateTimeFormatter formatter);

    /**
     * 会员详情 - 会员权益信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    MemberDetailRightResp getMemberDetailRight(UserLoginCacheDTO loginUser, Long validateId);

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益获取记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO, DateTimeFormatter formatter);

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益使用记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO, DateTimeFormatter formatter);

    /**
     * 会员详情 - 会员信用信息
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    MemberDetailCreditResp getMemberDetailCredit(UserLoginCacheDTO loginUser, Long validateId);

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(UserLoginCacheDTO loginUser, Long validateId);

    /**
     * App - 会员信用 - 交易评价汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    MobileCommentSummaryResp getMobileMemberDetailTradeCommentSummary(UserLoginCacheDTO loginUser, Long validateId);

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(UserLoginCacheDTO loginUser, MemberDetailCreditHistoryPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(UserLoginCacheDTO loginUser, Long validateId);

    /**
     * App - 会员信用 - 售后评价汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    MobileCommentSummaryResp getMobileMemberDetailCreditAfterSaleCommentSummary(UserLoginCacheDTO loginUser, Long validateId);


    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(UserLoginCacheDTO loginUser, MemberDetailCreditHistoryPageDataReq pageVO);


    /**
     * 会员详情 - 会员信用 - 投诉汇总
     * @param loginUser 登录用户
     * @param validateId 接口参数
     * @return 查询结果
     */
    MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(UserLoginCacheDTO loginUser, Long validateId);


    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberCreditComplaintPageQueryResp> pageMemberDetailCreditComplainHistory(UserLoginCacheDTO loginUser, ValidateIdPageDataReq pageVO);

    /**
     * “增加会员角色”功能，查询上级会员列表
     * @param subMemberId        下级会员id
     * @param subRoleId          下降会员角色id
     * @param enableMultiTenancy 是否开启pass多租户配置
     * @return 查询结果
     */
    UpperMemberShowResp getUpperMemberInfo(Long subMemberId, Long subRoleId, Boolean enableMultiTenancy);

    /**
     * “增加会员角色”功能，查询会员类型列表
     * @param memberTypeEnum 当前会员的会员类型
     * @return 查询结果
     */
    List<MemberTypeAndNameResp> getMemberTypeList(Integer memberTypeEnum);

    /**
     * “增加会员角色”功能，查询会员适用类型列表（saas）
     * @param memberTypeEnum 当前会员的会员类型
     * @param roleManageVOList 会员适用角色list
     * @return 查询结果
     */
    List<MemberTypeAndNameResp> getMemberTypeList(Integer memberTypeEnum, List<RoleRuleManageResp> roleManageVOList);

    /**
     * “增加会员角色”功能，根据会员类型Id查询角色列表
     * @param subMemberId 下级会员Id
     * @param memberTypeReq 接口参数
     * @return 查询结果
     */
    MemberInfoRoleListResp getRoleListByMemberType(Long subMemberId, MemberTypeReq memberTypeReq);

    /**
     * “增加会员角色”功能，根据会员类型Id查询角色列表(saas)
     * @param memberId         会员id
     * @param memberTypeReq             接口参数
     * @param roleManageVOList 会员适用角色list
     * @return 查询结果
     */
    MemberInfoRoleListResp getRoleListByMemberType(Long memberId, MemberTypeReq memberTypeReq, List<RoleRuleManageResp> roleManageVOList);

    /**
     * “增加会员角色”功能，会获取员注册资料信息
     * @param subMemberId 下级会员Id
     * @param subRoleId  下级会员角色Id
     * @param roleIdReq 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberInfoUpdateDetailByRoleResp getMemberRegisterDetailAfterAddRole(Long subMemberId, Long subRoleId, RoleIdReq roleIdReq, Integer roleTag);

    /**
     * “增加会员角色”功能，提交注册资料并新增角色
     * @param subMemberId 下级会员Id
     * @param subRoleId  下级会员角色Id
     * @param addRoleVO 接口参数
     * @return 新增结果
     */
    void addMemberRole(Long subMemberId, Long subRoleId, MemberInfoAddRoleReq addRoleVO, UserLoginCacheDTO loginUser);
}
