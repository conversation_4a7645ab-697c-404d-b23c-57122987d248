package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.detail.MemberBusinessCategoryDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员主营品类信息Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-25
 */
@Repository
public interface MemberBusinessCategoryRepository extends JpaRepository<MemberBusinessCategoryDO, Long>, JpaSpecificationExecutor<MemberBusinessCategoryDO> {
}
