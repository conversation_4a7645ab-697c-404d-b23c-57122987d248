package com.ssy.lingxi.member.service.commission;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.commission.BindBankCardReq;
import com.ssy.lingxi.member.model.req.commission.CommissionAccountQueryReq;
import com.ssy.lingxi.member.model.req.commission.CommissionDetailQueryReq;
import com.ssy.lingxi.member.model.resp.commission.*;

import java.util.List;

/**
 * 客户分佣账户服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
public interface ICommissionAccountService {

    /**
     * 分页查询客户分佣账户列表
     * @param loginUser 登录用户
     * @param request 查询请求
     * @return 分页结果
     */
    PageDataResp<CommissionAccountResp> getCommissionAccountPage(UserLoginCacheDTO loginUser, CommissionAccountQueryReq request);

    /**
     * 查看客户分佣账户详情
     * @param loginUser 登录用户
     * @param accountId 账户id
     * @return 账户详情
     */
    CommissionAccountDetailResp getCommissionAccountDetail(UserLoginCacheDTO loginUser, Long accountId);

    /**
     * 冻结客户分佣账户
     * @param loginUser 登录用户
     * @param accountId 账户id
     * @return 操作结果
     */
    Void freezeCommissionAccount(UserLoginCacheDTO loginUser, Long accountId);

    /**
     * 解冻客户分佣账户
     * @param loginUser 登录用户
     * @param accountId 账户id
     * @return 操作结果
     */
    Void unfreezeCommissionAccount(UserLoginCacheDTO loginUser, Long accountId);

    /**
     * 分页查询佣金明细记录列表
     * @param loginUser 登录用户
     * @param request 查询请求
     * @return 分页结果
     */
    PageDataResp<CommissionDetailResp> getCommissionDetailPage(UserLoginCacheDTO loginUser, CommissionDetailQueryReq request);



    /**
     * 绑定银行卡
     * @param loginUser 登录用户
     * @param request 绑定银行卡请求
     * @return 操作结果
     */
    Long bindBankCard(UserLoginCacheDTO loginUser, BindBankCardReq request);

    /**
     * 查询银行卡列表
     * @param loginUser 登录用户
     * @param userId 用户id
     * @return 银行卡列表
     */
    List<BankCardResp> getBankCardList(UserLoginCacheDTO loginUser, Long userId);

    /**
     * 查询冻结记录列表
     * @param loginUser 登录用户
     * @param commissionAccountId 分佣账户id
     * @return 冻结记录列表
     */
    List<AccountFreezeRecordResp> getAccountFreezeRecordList(UserLoginCacheDTO loginUser, Long commissionAccountId);

    /**
     * 导出客户分佣账户列表
     * @param loginUser 登录用户
     * @param request 查询请求
     * @param response HTTP响应
     */
    void exportCommissionAccountList(UserLoginCacheDTO loginUser, CommissionAccountQueryReq request, javax.servlet.http.HttpServletResponse response);


}
