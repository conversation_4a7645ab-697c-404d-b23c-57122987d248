package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.util.StrUtil;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.DataSourceEnum;
import com.ssy.lingxi.common.enums.member.TokenStrategyEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.common.util.TreeUtils;
import com.ssy.lingxi.common.util.UUIDUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.LanguageEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.TokenContext;
import com.ssy.lingxi.component.base.model.dto.login.AccessTokenDTO;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.TokenUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.config.DefaultAuthConfig;
import com.ssy.lingxi.member.config.MemberRefreshConfig;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.entity.bo.*;
import com.ssy.lingxi.member.entity.bo.login.LoginContext;
import com.ssy.lingxi.member.entity.bo.login.ManageLoginBO;
import com.ssy.lingxi.member.entity.bo.login.MobileLoginBO;
import com.ssy.lingxi.member.entity.bo.login.WebLoginBO;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.enums.AuthCodeTypeEnum;
import com.ssy.lingxi.member.enums.LoginStrategyEnum;
import com.ssy.lingxi.member.handler.listener.event.RedisKeyRemoveEvent;
import com.ssy.lingxi.member.model.req.login.*;
import com.ssy.lingxi.member.model.resp.login.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.web.IMemberLoginService;
import com.ssy.lingxi.member.util.VerifyImageUtil;
import com.ssy.lingxi.support.api.enums.SmsTemplateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.roaringbitmap.RoaringBitmap;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Web客户端、平台后台用户登录服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-05
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MemberLoginServiceImpl implements IMemberLoginService {
    private final MemberRepository memberRepository;
    private final UserRepository userRepository;
    private final MemberRelationRepository relationRepository;
    private final IBaseMemberCacheService memberCacheService;
    private final IBaseMemberLevelAsyncService baseMemberLevelAsyncService;
    private final IBaseMemberCreditAsyncService baseMemberCreditAsyncService;
    private final IRedisUtils redisUtils;
    private final IBaseAuthService baseAuthService;
    private final MemberRefreshConfig memberRefreshConfig;
    private final IBaseLoginService baseLoginService;
    private final IBaseTokenManageService tokenManageService;
    private final ApplicationEventPublisher eventPublisher;
    private final CorporationRepository corporationRepository;
    private final IBaseMemberValidateService baseMemberValidateService;
    private final MemberRelationRepository memberRelationRepository;
    private final BaiTaiMemberProperties baiTaiMemberProperties;
    private final MemberBranchRepository memberBranchRepository;

    @Override
    public WebLoginBO accountOrPhoneLogin(HttpHeaders headers, MemberLoginReq loginReq) {
        memberCacheService.checkWebRequestHeader(headers);

        // 校验用户是否存在
        UserDO userDO = baseLoginService.getSpecifyUserDOIfNecessary(loginReq.getAccount(), loginReq.getUserId(), LoginStrategyEnum.PASSWORD.getCode());
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 校验会员是否存在
        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);

        // 判断登录失败次数是否超过限制并校验密码
        baseLoginService.checkLoginFailuresAndPwd(userDO.getId(), userDO.getPassword(), loginReq.getPassword());

        // 超管用户需要校验验证码
        baseLoginService.adminAccountSecurityCheck(userDO,
                loginReq.getPhoneSmsCode(),
                loginReq.getEmailSmsCode(),
                MemberRedisConstant.LOGIN_PC_BY_PHONE_REDIS_KEY_PREFIX,
                MemberRedisConstant.LOGIN_PC_BY_EMAIL_REDIS_KEY_PREFIX);

        // 查询登陆会员的平台关系
        List<MemberRelationDO> relDOList = relationRepository.findBySubMemberIdAndRelType(memberDO.getId(), MemberRelationTypeEnum.PLATFORM.getCode());
        BusinessAssertUtil.notEmpty(relDOList, ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);

        // 调用通用登陆层，并获取登陆上下文
        LoginContext loginContext = baseLoginService.baseLogin(relDOList, memberDO, userDO, null,
                new TokenContext(memberDO.getId(), userDO.getId(), SystemSourceEnum.BUSINESS_WEB, TokenStrategyEnum.LOGIN));

        // 调用通用token缓存层，维护token相关数据，并写缓存
        tokenManageService.distUsableToken(loginContext);

        // 异步计算会员登录获得的积分、会员注册年限获得的信用积分
        baseMemberLevelAsyncService.calculateMemberLoginScore(loginContext.getRelId(), SystemSourceEnum.BUSINESS_WEB.getCode());
        baseMemberCreditAsyncService.calculateMemberRegisterYearsCredit(loginContext.getRelId());

        // 异步记录最后一次登陆时间
        baseLoginService.updateUserLastLoginTimeAsync(userDO.getId());

        return new WebLoginBO(loginContext);
    }

    @Override
    public WebLoginBO switchMemberRole(HttpHeaders headers, MemberLoginSwitchMemberRoleReq roleReq) {
        // 获取登陆对象并获取旧token
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        // 校验用户是否存在
        UserDO userDO = userRepository.findByIdAndRelTypeAndStatus(loginUser.getUserId(), MemberRelationTypeEnum.OTHER.getCode(), EnableDisableStatusEnum.ENABLE.getCode())
                .orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));

        // 校验会员是否存在
        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        // 查询登陆会员的平台关系
        List<MemberRelationDO> relDOList = relationRepository.findBySubMemberIdAndRelType(memberDO.getId(), MemberRelationTypeEnum.PLATFORM.getCode());
        BusinessAssertUtil.notEmpty(relDOList, ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);

        // 解析accessToken
        AccessTokenDTO accessTokenDTO = TokenUtil.analysisAccessToken(headers.getFirst(Constant.ACCESS_TOKEN));

        // 调用通用登陆层，并获取登陆上下文
        LoginContext loginContext = baseLoginService.baseLogin(relDOList, memberDO, userDO, roleReq.getMemberRoleId(),
                new TokenContext(memberDO.getId(), roleReq.getMemberRoleId(), userDO.getId(), SystemSourceEnum.BUSINESS_WEB, TokenStrategyEnum.SWITCH_ROLES, accessTokenDTO.getRawToken()));

        // 调用通用token缓存层，维护token相关数据，并写缓存
        tokenManageService.distUsableToken(loginContext);

        return new WebLoginBO(loginContext);
    }

    @Override
    public MemberAuthCodeResp getLoginAuthCode(HttpHeaders headers) {
        // 校验web登录
        memberCacheService.checkWebRequestHeader(headers);

        // 生成授权码
        AuthCodeBO authCodeBO = memberCacheService.createAuthCode();

        // 授权信息
        MemberAuthCodeResp memberAuthCodeResp = new MemberAuthCodeResp();
        memberAuthCodeResp.setCodeSign(AuthCodeTypeEnum.LOGIN.getCode());
        memberAuthCodeResp.setAuthCode(authCodeBO.getAuthCode());
        memberAuthCodeResp.setExpireTime(authCodeBO.getExpire());
        return memberAuthCodeResp;
    }

    @Override
    public WebLoginBO getAuthCodeLoginInfo(HttpHeaders headers, ActivityMemberAuthCodeReq memberAuthCodeReq) {
        // 获取缓存的登录信息
        String cacheKey = MemberRedisConstant.LOGIN_QR_AUTH_INFO_REDIS_KEY_PREFIX + memberAuthCodeReq.getAuthCode();
        AuthCodeMemberBO authCodeMemberBO = JsonUtil.toObj(redisUtils.stringGet(cacheKey, RedisConstant.REDIS_USER_INDEX), AuthCodeMemberBO.class);
        BusinessAssertUtil.notNull(authCodeMemberBO, ResponseCodeEnum.MC_MS_APP_LOGIN_AUTH_CODE_ERROR);

        // 校验会员是否存在
        MemberDO memberDO = memberRepository.findById(authCodeMemberBO.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));

        // 校验用户是否存在
        UserDO userDO = userRepository.findById(authCodeMemberBO.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));

        // 查询登陆会员的平台关系
        List<MemberRelationDO> relDOList = relationRepository.findBySubMemberIdAndRelType(memberDO.getId(), MemberRelationTypeEnum.PLATFORM.getCode());
        BusinessAssertUtil.notEmpty(relDOList, ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);

        // 调用通用登陆层，并获取登陆上下文
        LoginContext loginContext = baseLoginService.baseLogin(relDOList, memberDO, userDO, null,
                new TokenContext(memberDO.getId(), userDO.getId(), SystemSourceEnum.BUSINESS_WEB, TokenStrategyEnum.LOGIN));

        // 调用通用token缓存层，维护token相关数据，并写缓存
        tokenManageService.distUsableToken(loginContext);

        // 异步计算会员登录获得的积分、会员注册年限获得的信用积分
        baseMemberLevelAsyncService.calculateMemberLoginScore(loginContext.getRelId(), SystemSourceEnum.BUSINESS_WEB.getCode());
        baseMemberCreditAsyncService.calculateMemberRegisterYearsCredit(loginContext.getRelId());

        // 异步记录最后一次登陆时间
        baseLoginService.updateUserLastLoginTimeAsync(userDO.getId());

        // 删除使用过的验证码
        eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Collections.singletonList(cacheKey)));

        return new WebLoginBO(loginContext);
    }

    @Override
    public CaptchaPicResp captcha(HttpHeaders headers, CaptchaReq captchaReq) {

            // 根据模板裁剪图片
        CaptchaPicResp picVO = null;
        try {
            picVO = VerifyImageUtil.pictureTemplatesCut(captchaReq.getWidth(), captchaReq.getHeight(), captchaReq.getSize());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 生成流水号，这里就使用时间戳代替
            String imgId = UUIDUtil.randomUUID();
            //入缓存
            redisUtils.stringSet(MemberRedisConstant.REGISTER_CAPTCHA_IMG_REDIS_KEY_PREFIX + imgId, String.valueOf(picVO.getWidth()), 60L, RedisConstant.REDIS_USER_INDEX);
            picVO.setImgId(imgId);
            return picVO;

    }

    @Override
    public void sendPhoneLoginSmsCode(HttpHeaders headers, PhoneLoginSmsCode phoneReq) {
        memberCacheService.checkWebRequestHeader(headers);

        // 查找用户
        UserDO userDO = userRepository.findFirstByTelCodeAndPhoneAndRelType(phoneReq.getTelCode(), phoneReq.getPhone(), MemberRelationTypeEnum.OTHER.getCode());
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 发送验证码
        baseLoginService.sendPhoneLoginSmsCode(phoneReq, userDO, MemberRedisConstant.LOGIN_PC_BY_PHONE_REDIS_KEY_PREFIX + userDO.getPhone(), SmsTemplateEnum.PC_LOGIN.getCode());
    }

    @Override
    public void sendEmailLoginSmsCode(HttpHeaders headers, EmailLoginSmsCode emailReq) {
        memberCacheService.checkWebRequestHeader(headers);

        // 查找用户
        UserDO userDO = userRepository.findFirstByEmailAndRelType(emailReq.getEmail(), MemberRelationTypeEnum.OTHER.getCode());
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);

        // 发送验证码
        baseLoginService.sendEmailLoginSmsCode(emailReq, userDO, MemberRedisConstant.LOGIN_PC_BY_EMAIL_REDIS_KEY_PREFIX + userDO.getEmail());
    }

    @Override
    public void sendManageLoginPhoneSmsCode(HttpHeaders headers, PhoneLoginSmsCode phoneReq) {
        memberCacheService.checkPlatformRequestHeader(headers);

        // 如果超管没有设置手机则报错
        // 超管设置的手机和入参手机不一致，则报错
        UserDO platformAdminUserDO = userRepository.findPlatformAdminUser().orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));
        BusinessAssertUtil.isTrue(StringUtils.hasLength(platformAdminUserDO.getPhone()) && platformAdminUserDO.getPhone().equals(phoneReq.getPhone()),
                ResponseCodeEnum.LS_DATA_PHONE_VALIDATE);

        // 发送验证码
        baseLoginService.sendPhoneLoginSmsCode(phoneReq, platformAdminUserDO, MemberRedisConstant.LOGIN_MANAGE_BY_PHONE_REDIS_KEY_PREFIX + platformAdminUserDO.getPhone(), SmsTemplateEnum.PLATFORM_MANAGER_LOGIN.getCode());
    }

    @Override
    public void sendManageLoginEmailSmsCode(HttpHeaders headers, EmailLoginSmsCode emailReq) {
        memberCacheService.checkPlatformRequestHeader(headers);

        // 如果超管没有设置邮箱则报错
        // 超管设置的邮箱和入参邮箱不一致，则报错
        UserDO platformAdminUserDO = userRepository.findPlatformAdminUser().orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));
        BusinessAssertUtil.isTrue(StringUtils.hasLength(platformAdminUserDO.getEmail()) && platformAdminUserDO.getEmail().equals(emailReq.getEmail()),
                ResponseCodeEnum.MC_MS_MANAGE_EMAIL_ERROR);

        // 发送验证码
        baseLoginService.sendEmailLoginSmsCode(emailReq, platformAdminUserDO, MemberRedisConstant.LOGIN_MANAGE_BY_EMAIL_REDIS_KEY_PREFIX + platformAdminUserDO.getEmail());
    }

    @Override
    public List<MultiAccCheckResp> multiAccCheck(HttpHeaders headers, MultiAccCheckReq multiAccCheckReq) {
        // 获取登陆来源
        String source = headers.getFirst(Constant.LOGIN_SOURCE);
        BusinessAssertUtil.notNull(source, ResponseCodeEnum.REQUEST_HEADER_ERROR);

        SystemSourceEnum systemSourceEnum = SystemSourceEnum.parseInt(Integer.parseInt(source));
        BusinessAssertUtil.notNull(systemSourceEnum, ResponseCodeEnum.REQUEST_HEADER_ERROR);

        // 根据登陆方式查询对应账号
        List<UserDO> userDOList = baseLoginService.getAllUserDOByLoginStrategy(multiAccCheckReq);

        // 如果没有查到则报错
        if (CollectionUtils.isEmpty(userDOList)) {
            throw new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);
        }

        // 校验用户是否被禁用
        userDOList = userDOList.stream().filter(userDO -> EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus())).collect(Collectors.toList());
        BusinessAssertUtil.notEmpty(userDOList, ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 判断登录失败次数是否超过限制,并校验密码或验证码
        if (LoginStrategyEnum.isPasswordLogin(multiAccCheckReq.getLoginType())) {
            userDOList = baseLoginService.checkLoginFailuresAndPwd(userDOList, multiAccCheckReq);
        } else if (LoginStrategyEnum.isSmsCodeLogin(multiAccCheckReq.getLoginType())) {
            userDOList = baseLoginService.checkLoginFailuresAndSmsCode(userDOList, multiAccCheckReq);
        }

        // 能力中心账户密码登陆才需要做超管验证码校验
        boolean needSecurityCheck = SystemSourceEnum.BUSINESS_WEB.equals(systemSourceEnum)
                && LoginStrategyEnum.isPasswordLogin(multiAccCheckReq.getLoginType());

        //  小程序登录，需要过滤供应商，仅采购商角色才能登录
        if(SystemSourceEnum.BUSINESS_MOBILE.equals(systemSourceEnum)) {
            log.info("会员过滤前：{}",userDOList.size());
            List<Long> roles = Stream.of(baiTaiMemberProperties.getCustomerRoleId(), baiTaiMemberProperties.getIndividualRoleId()).collect(Collectors.toList());
            //flt1:排除自营商家角色的用户，flt2:符合个人采购商/企业采购商保留
            userDOList=userDOList.stream().filter(e ->!e.getMember().getMemberRoles().stream().map(k ->k.getId()).collect(Collectors.toList()).contains(baiTaiMemberProperties.getSelfRoleId())).filter(o ->o.getMember().getMemberRoles().stream().map(k ->k.getId()).anyMatch(r ->roles.contains(r))).collect(Collectors.toList());
            log.info("会员过滤后：{}",userDOList.size());
        }

        // 构建返回数据
        return userDOList.stream()
                .map(userDO -> new MultiAccCheckResp(userDO.getMember().getId(),
                        userDO.getId(),
                        userDO.getMember().getName(),
                        userDO.getMember().getLogo(),
                        needSecurityCheck && UserTypeEnum.ADMIN.getCode().equals(userDO.getUserType()),
                        StringUtils.hasLength(userDO.getPhone())))
                .sorted(Comparator.comparing(MultiAccInfoResp::getMemberId))
                .collect(Collectors.toList());
    }

    @Override
    public LoginSecurityCheckResp accountSecurityCheck(HttpHeaders headers, LoginSecurityCheckReq securityCheckReq) {
        String source = headers.getFirst(Constant.LOGIN_SOURCE);
        BusinessAssertUtil.notNull(source, ResponseCodeEnum.REQUEST_HEADER_ERROR);

        // 查询该账号在平台后台的用户信息
        SystemSourceEnum systemSourceEnum = SystemSourceEnum.parseInt(Integer.parseInt(source));
        BusinessAssertUtil.notNull(systemSourceEnum, ResponseCodeEnum.REQUEST_HEADER_ERROR);

        // 根据登录来源查找用户
        UserDO userDO = null;
        switch (systemSourceEnum) {
            case BUSINESS_WEB:
            case BUSINESS_MOBILE:
                userDO = baseLoginService.getSpecifyUserDOIfNecessary(securityCheckReq.getAccount(), securityCheckReq.getUserId(), LoginStrategyEnum.PASSWORD.getCode());
                break;
            case BUSINESS_MANAGEMENT_PLATFORM:
                userDO = userRepository.findLoginUser(securityCheckReq.getAccount(), securityCheckReq.getAccount(), MemberRelationTypeEnum.PLATFORM.getCode());
        }

        // 校验用户
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 判断登录失败次数是否超过限制,并校验密码
        baseLoginService.checkLoginFailuresAndPwd(userDO.getId(), userDO.getPassword(), securityCheckReq.getPassword());

        // 如果是超管账号，手机号和邮箱不能同时为空
        boolean needCheck = memberRefreshConfig.getCheckSmsCode() && UserTypeEnum.ADMIN.getCode().equals(userDO.getUserType());
        if (needCheck) {
            BusinessAssertUtil.notAllBlank(Arrays.asList(userDO.getPhone(), userDO.getEmail()).toArray(new CharSequence[0])
                    , ResponseCodeEnum.MC_MS_CURRENT_ACCOUNT_HAS_RISKS_PLEASE_NOTIFY_THE_MANAGEMENT);
        }

        // 构造返回对象，有需要才返回
        LoginSecurityCheckResp loginSecurityCheckResp = new LoginSecurityCheckResp();
        loginSecurityCheckResp.setNeedCheck(needCheck);
        if (needCheck) {
            loginSecurityCheckResp.setPhone(userDO.getPhone());
            loginSecurityCheckResp.setEmail(userDO.getEmail());
        }

        return loginSecurityCheckResp;
    }

    @Override
    public ManageLoginBO manageAccountLogin(HttpHeaders headers, ManageLoginReq loginReq) {
        memberCacheService.checkPlatformRequestHeader(headers);

        // 查询该账号在平台后台的用户信息
        UserDO userDO = userRepository.findFirstByAccountAndRelType(loginReq.getAccount(), MemberRelationTypeEnum.PLATFORM.getCode()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT));

        // 校验用户是否冻结
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 判断登录失败次数是否超过限制
        baseLoginService.checkLoginFailuresAndPwd(userDO.getId(), userDO.getPassword(), loginReq.getPassword());

        // 超管用户需要校验验证码
        baseLoginService.adminAccountSecurityCheck(userDO,
                loginReq.getPhoneSmsCode(),
                loginReq.getEmailSmsCode(),
                MemberRedisConstant.LOGIN_MANAGE_BY_PHONE_REDIS_KEY_PREFIX,
                MemberRedisConstant.LOGIN_MANAGE_BY_EMAIL_REDIS_KEY_PREFIX);

        // 获取会员和角色信息
        MemberDO memberDO = userDO.getMember();
        MemberRoleDO memberRoleDO = memberDO.getMemberRoles().stream().findFirst().orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_NEED_LOGIN_WITH_PLATFORM_ROLE));

        // 获取平台后台登陆上下文
        LoginContext manageLoginContext = baseLoginService.getManageLoginContext(memberDO, memberRoleDO, userDO);

        // 调用通用token缓存层，维护token相关数据，并写缓存
        tokenManageService.distUsableToken(manageLoginContext);

        // 记录最后一次登录时间
        baseLoginService.updateUserLastLoginTimeAsync(userDO.getId());

        //返回结果
        return new ManageLoginBO(manageLoginContext);
    }

    /**
     * 是否具有支付及查看订单价格权限
     * @param headers HttpHeaders信息
     * @retrurn 是否具有权限
     */
    @Override
    public Boolean hasPayAndOrderPriceAuth(HttpHeaders headers) {
        // 获取登录用户信息
        UserLoginCacheDTO loginCacheDTO = memberCacheService.checkUserFromCache(headers);
        if (UserTypeEnum.ADMIN.getCode().equals(loginCacheDTO.getUserType())) {
            return true;
        }
        // 获取登录用户的权限
        UserDO userDO = userRepository.findById(loginCacheDTO.getUserId()).orElseThrow(() -> new BusinessException("用户不存在"));
        return userDO.isHasOrderAuth();
    }

    @Override
    public void logOut(HttpHeaders headers) {
        // 登出
        tokenManageService.logOut(headers);
    }

    @Override
    public List<LoginAuthResp> getAuthTree(HttpHeaders headers) {
        // 获取登录用户信息
        UserLoginCacheDTO loginCacheDTO = memberCacheService.checkUserFromCache(headers);

        // 平台后台按正常方式计算权限
        // 能力中心 当前登录角色 至少一次审核通过，按正常方式计算权限
        // 能力中心 当前登录角色 没有一次审核通过，只返回系统默认权限
        List<LoginAuthResp> loginAuthRespList = canGetNormalAuthTree(loginCacheDTO) ? getAuthTree(loginCacheDTO) : getDefaultAuth(loginCacheDTO);

        // 构建树型权限并返回
        return TreeUtils.transferToTree(
                        loginAuthRespList,
                        0L,
                        LoginAuthResp::getId,
                        LoginAuthResp::getParentId,
                        LoginAuthResp::setChildren,
                        Comparator.comparing(LoginAuthResp::getSort));
    }

    @Override
    public WrapperResp<?> refreshToken(HttpHeaders headers) {
        // 校验相关参数，并获取tokenContext对象
        TokenContext tokenContext = tokenManageService.checkRefreshToken(headers);

        // 校验用户是否存在
        UserDO userDO = userRepository.findByIdAndStatus(tokenContext.getUserId(), EnableDisableStatusEnum.ENABLE.getCode())
                .orElseThrow(() -> new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT));

        // 校验会员是否存在
        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        // 调用通用登陆层，并获取登陆上下文
        LoginContext loginContext;
        if (SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode().equals(tokenContext.getLoginSourceEnum().getCode())) {
            // 平台后台
            MemberRoleDO memberRoleDO = memberDO.getMemberRoles().stream().findFirst().orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_NEED_LOGIN_WITH_PLATFORM_ROLE));
            loginContext = baseLoginService.getManageLoginContext(memberDO, memberRoleDO, userDO);
        } else {
            // 能力中心
            List<MemberRelationDO> relDOList = relationRepository.findBySubMemberIdAndRelType(memberDO.getId(), MemberRelationTypeEnum.PLATFORM.getCode());
            BusinessAssertUtil.notEmpty(relDOList, ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            loginContext = baseLoginService.baseLogin(relDOList, memberDO, userDO, tokenContext.getRoleId(), tokenContext);
        }

        // 调用通用token缓存层，维护token相关数据，并写缓存
        tokenManageService.distUsableToken(loginContext);

        // 按登录来源返回续期结果
        return wrapperRespStrategy(loginContext);
    }

    /**
     * 发送手机号绑定的短信验证码
     *
     * @param req 请求参数
     */
    @Override
    public void sendPhoneBindSmsCode(PhoneBindSmsCodeReq req) {
        UserDO userDO = userRepository.findById(req.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT));
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 发送验证码
        baseLoginService.sendPhoneBindSmsCode(req, userDO, MemberRedisConstant.PHONE_BIND_CODE_PREFIX + userDO.getId());
    }

    /**
     * 绑定手机号
     *
     * @param req 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindPhone(PhoneBindReq req) {
        String phoneBindCode = redisUtils.stringGet(MemberRedisConstant.PHONE_BIND_CODE_PREFIX + req.getUserId(), RedisConstant.REDIS_USER_INDEX);
        if (!Objects.equals(phoneBindCode, req.getPhoneBindCode())) {
            throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
        }
        boolean phoneExistFlag = userRepository.existsByRelTypeAndPhone(MemberRelationTypeEnum.OTHER.getCode(), req.getPhone());
        if (phoneExistFlag) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }

        UserDO userDO = userRepository.findById(req.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));
        if (!StringUtils.hasLength(userDO.getPhone())) {
            userDO.setPhone(req.getPhone());
            userRepository.saveAndFlush(userDO);
            //判断是否超管账号
            if (Objects.equals(userDO.getUserType(), UserTypeEnum.ADMIN.getCode())) {
                MemberDO memberDO = userDO.getMember();
                memberDO.setPhone(req.getPhone());
                //判断数据来源EOS 且 判断是否是主品牌账号
                if(DataSourceEnum.EOS.getCode().equals(memberDO.getDataSource())&&memberDO.getMainFlag()==null){
                    log.info("【会员绑定手机号时】>准备初始化店铺：memberID:{},name:{}",memberDO.getId(),memberDO.getName());
                    CorporationDO firstByCode = corporationRepository.findById(memberDO.getCorporationId()).orElse(null);
                    if(firstByCode!=null&& StrUtil.isBlank(firstByCode.getGroupIdentifier())){
                        memberDO.setMainFlag(Boolean.TRUE);
                        firstByCode.setGroupIdentifier(memberDO.getPhone());
                        firstByCode.setMemberId(memberDO.getId());
                        corporationRepository.saveAndFlush(firstByCode);
                        log.info("【会员绑定手机号时】>绑定主品牌账号成功，memberID:{},phone:{},corporationID:{}",memberDO.getId(),memberDO.getPhone(),memberDO.getCorporationId());
                    }else{
                        log.warn("【会员绑定手机号时】>绑定主品牌账号失败！！！ 原因：未关联企业信息 ，memberID:{},phone:{},corporationID:{}",memberDO.getId(),memberDO.getPhone(),memberDO.getCorporationId());
                    }

                    //判断店铺是否初始化
                    boolean isExists = memberBranchRepository.existsByMemberIdAndMemberRoleId(memberDO.getId(), baiTaiMemberProperties.getCustomerRoleId());
                    if(!isExists) {
                        MemberRelationDO memberRelationDO = memberRelationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(memberDO.getId(), baiTaiMemberProperties.getCustomerRoleId(), MemberRelationTypeEnum.OTHER.getCode());
                        if (memberRelationDO != null) {
                            baseMemberValidateService.createInitShop(memberRelationDO, firstByCode);
                        } else {
                            log.warn("【会员绑定手机号时】>初始化店铺失败：原因会员关系未找到，memberID:{},roleID:{}", memberDO.getId(), baiTaiMemberProperties.getCustomerRoleId());
                        }
                    }else{
                        log.info("【会员绑定手机号时】>店铺已经存在：memberID:{},roleID:{}",memberDO.getId(), baiTaiMemberProperties.getCustomerRoleId());
                    }
                }
                memberRepository.saveAndFlush(memberDO);
            }
        }
    }

    /**
     * 平台后台：返回true
     * 能力中心：
     * 1.当前登录角色 至少1次平台审核通过 返回true
     * 2.当前登录角色 没有1次平台审核通过 返回false
     */
    private static boolean canGetNormalAuthTree(UserLoginCacheDTO loginCacheDTO) {
        return SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode().equals(loginCacheDTO.getLoginSource())
                || EnableDisableStatusEnum.ENABLE.getCode().equals(loginCacheDTO.getVerified());
    }

    private List<LoginAuthResp> getAuthTree(UserLoginCacheDTO loginCacheDTO) {
        // 异步查询对应端的菜单按钮信息
        String currentLanguage = LanguageEnum.getCurrentLanguage();
        CompletableFuture<Set<MenuAuthBO>> menuAuthBOSetFuture = CompletableFuture.supplyAsync(() -> baseAuthService.getMenuSetBySource(loginCacheDTO.getLoginSource(), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor);
        CompletableFuture<Set<ButtonAuthBO>> buttonAuthBOSetFuture = CompletableFuture.supplyAsync(() -> baseAuthService.getButtonSetBySource(loginCacheDTO.getLoginSource(), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor);

        // 平台后台登陆
        if (Objects.equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode(), loginCacheDTO.getLoginSource())) {
            // 平台后台超管，不做权限控制
            if (UserTypeEnum.ADMIN.getCode().equals(loginCacheDTO.getUserType())) {
                return assemblyData(menuAuthBOSetFuture.join(), buttonAuthBOSetFuture.join(), menuAuthBO -> true, buttonAuthBO -> true);
            } else {
                // 查询用户权限
                AuthBO userAuthBO = baseAuthService.getAuthByUserId(loginCacheDTO.getUserId());

                // 平台后台非超管，平台权限和登陆用户权限取交集
                return assemblyData(menuAuthBOSetFuture.join(), buttonAuthBOSetFuture.join(), getMenuAuthPredicate(BitMapUtil.toBitMap(userAuthBO.getMenuAuth())), getButtonAuthPredicate(BitMapUtil.toBitMap(userAuthBO.getButtonAuth())));
            }
        } else {//能力中心、app、小程序等
            // 查询用户与关系表权限
            CompletableFuture<AuthBO> userAuthBOFuture = CompletableFuture.supplyAsync(() -> baseAuthService.getAuthByUserId(loginCacheDTO.getUserId()), ThreadPoolConfig.asyncDefaultExecutor);
            AuthBO relAuthBO = baseAuthService.getAuthByRelId(loginCacheDTO.getRelId());

            // 登陆角色的关系表权限和登陆用户的权限取交集
            RoaringBitmap menuAuthBitmap = BitMapUtil.and(relAuthBO.getMenuAuth(), userAuthBOFuture.join().getMenuAuth(), BitMapUtil.ReturnType.RoaringBitmap);
            RoaringBitmap buttonAuthBitmap = BitMapUtil.and(relAuthBO.getButtonAuth(), userAuthBOFuture.join().getButtonAuth(), BitMapUtil.ReturnType.RoaringBitmap);

            // 组装返回信息
            return assemblyData(menuAuthBOSetFuture.join(), buttonAuthBOSetFuture.join(), getMenuAuthPredicate(menuAuthBitmap), getButtonAuthPredicate(buttonAuthBitmap));
        }
    }

    private List<LoginAuthResp> getDefaultAuth(UserLoginCacheDTO loginCacheDTO) {
        // 异步查询登陆用户对应端的菜单按钮信息
        String currentLanguage = LanguageEnum.getCurrentLanguage();
        CompletableFuture<Set<MenuAuthBO>> menuAuthBOSetFuture = CompletableFuture.supplyAsync(() -> baseAuthService.getMenuSetByPathSet(DefaultAuthConfig.getDefaultMenuAuth(loginCacheDTO.getRoleTag()), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor);
        CompletableFuture<Set<ButtonAuthBO>> buttonAuthBOSetFuture = CompletableFuture.supplyAsync(() -> baseAuthService.getButtonSetByPathSet(DefaultAuthConfig.getDefaultButtonAuth(loginCacheDTO.getRoleTag()), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor);

        // 组装返回信息
        return assemblyData(menuAuthBOSetFuture.join(), buttonAuthBOSetFuture.join(), menuAuthBO -> true, buttonAuthBO -> true);
    }

    private static List<LoginAuthResp> assemblyData(Set<MenuAuthBO> menuAuthBOSet,
                                                    Set<ButtonAuthBO> buttonAuthBOSet,
                                                    Predicate<MenuAuthBO> menuAuthPredicate,
                                                    Predicate<ButtonAuthBO> buttonAuthPredicate) {
        // 按菜单id聚合：<menuId, List<ButtonAuthBO>>
        Map<Long, List<ButtonAuthBO>> buttonAuthBOMap = buttonAuthBOSet.stream().collect(Collectors.groupingBy(ButtonAuthBO::getMenuId));

        // 组装数据
        return menuAuthBOSet.stream()
                .filter(menuAuthPredicate)
                .map(menuAuthBO -> {
                    LoginAuthResp loginAuthResp = new LoginAuthResp();
                    loginAuthResp.setId(menuAuthBO.getId());
                    loginAuthResp.setParentId(menuAuthBO.getParentId());
                    loginAuthResp.setName(menuAuthBO.getName());
                    loginAuthResp.setPath(menuAuthBO.getPath());
                    loginAuthResp.setSort(menuAuthBO.getSort());
                    loginAuthResp.setButtonList(buttonAuthBOMap.getOrDefault(menuAuthBO.getId(), new ArrayList<>())
                            .stream()
                            .filter(buttonAuthPredicate)
                            .map(buttonAuthBO -> {
                                LoginButtonAuthResp loginButtonAuthResp = new LoginButtonAuthResp();
                                loginButtonAuthResp.setName(buttonAuthBO.getName());
                                loginButtonAuthResp.setPath(buttonAuthBO.getPath());
                                return loginButtonAuthResp;
                            }).collect(Collectors.toList()));
                    return loginAuthResp;
                }).collect(Collectors.toList());
    }

    private static Predicate<MenuAuthBO> getMenuAuthPredicate(RoaringBitmap menuAuthBitmap) {
        return menuAuthBO -> menuAuthBitmap.contains(menuAuthBO.getId().intValue());
    }

    private static Predicate<ButtonAuthBO> getButtonAuthPredicate(RoaringBitmap buttonAuthBitmap) {
        return buttonAuthBO -> buttonAuthBitmap.contains(buttonAuthBO.getId().intValue());
    }

    private static WrapperResp<? extends Serializable> wrapperRespStrategy(LoginContext loginContext) {
        SystemSourceEnum loginSourceEnum = loginContext.getTokenContext().getLoginSourceEnum();
        if (SystemSourceEnum.BUSINESS_WEB.equals(loginSourceEnum)) {
            return WrapperUtil.success(new WebLoginBO(loginContext));// pc
        } else if (SystemSourceEnum.BUSINESS_MOBILE.equals(loginSourceEnum)) {
            return WrapperUtil.success(new MobileLoginBO(loginContext));// app
        } else {
            return WrapperUtil.success(new ManageLoginBO(loginContext));// 平台后台
        }
    }
}
