package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员入库资料Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-20
 */
@Repository
public interface MemberDepositoryDetailRepository extends JpaRepository<MemberDepositoryDetailDO, Long>, JpaSpecificationExecutor<MemberDepositoryDetailDO> {
    List<MemberDepositoryDetailDO> findByRelation(MemberRelationDO relationDO);

    List<MemberDepositoryDetailDO> findByRelationAndVersion(MemberRelationDO relationDO, Integer version);

    @Transactional
    void deleteByMemberConfig(MemberRegisterConfigDO configDO);

    @Transactional
    void deleteByRelation(MemberRelationDO relationDO);

    @Transactional
    void deleteByRelationAndVersion(MemberRelationDO relationDO, Integer version);

    List<MemberDepositoryDetailDO> findAllByParentId(Long id);

    List<MemberDepositoryDetailDO> findAllByParentIdIn(List<Long> parentIds);

    void deleteByParentId(Long parentId);
}
