package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskResultBO;
import com.ssy.lingxi.member.entity.do_.appraisal.MemberAppraisalDO;
import com.ssy.lingxi.member.entity.do_.appraisal.MemberAppraisalItemDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.enums.MemberAppraisalItemStatusEnum;
import com.ssy.lingxi.member.enums.MemberAppraisalStatusEnum;
import com.ssy.lingxi.member.enums.MemberValidateHistoryOperationEnum;
import com.ssy.lingxi.member.model.dto.MemberAppraisalMessageDTO;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.repository.MemberAppraisalItemRepository;
import com.ssy.lingxi.member.repository.MemberAppraisalRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberHistoryService;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberAppraisalService;
import com.ssy.lingxi.member.util.CodeUtil;
import com.ssy.lingxi.member.util.FileObjectUtil;
import com.ssy.lingxi.workflow.api.enums.ProcessTaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员考评服务实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Slf4j
@Service
public class MemberAppraisalServiceImpl implements IMemberAppraisalService {
    @Resource
    private IBaseMemberCacheService memberCacheService;
    @Resource
    private IBaseMemberHistoryService memberHistoryService;
    @Resource
    private IWorkflowFeignService workflowFeignService;
    @Resource
    private MemberAppraisalRepository memberAppraisalRepository;
    @Resource
    private MemberAppraisalItemRepository memberAppraisalItemRepository;
    @Resource
    private MemberRelationRepository memberRelationRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private IMessageFeignService messageFeignService;

    @Override
    public List<StatusResp> listMemberAppraisalStatus(HttpHeaders headers) {
        List<StatusResp> statusRespList = Stream.of(MemberAppraisalStatusEnum.values()).map(e -> new StatusResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
        statusRespList.add(0, new StatusResp(0, MemberStringEnum.ALL.getName()));

        return statusRespList;
    }

    @Override
    public PageDataResp<MemberAppraisalSubmitPageQueryResp> pageSummaryMemberAppraisal(HttpHeaders headers, MemberAppraisalSummaryPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Page<MemberAppraisalDO> pageList = baseMemberAppraisalPage(loginUser, NumberUtil.isNullOrZero(pageVO.getStatus()) ? Collections.emptyList() : Collections.singletonList(pageVO.getStatus()), pageVO, roleTag);

        List<MemberAppraisalSubmitPageQueryResp> resultList = pageList.stream().map(e -> {
            MemberAppraisalSubmitPageQueryResp pageQueryVO = new MemberAppraisalSubmitPageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setName(e.getSubMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            pageQueryVO.setTotalScore(e.getTotalScore());
            pageQueryVO.setStatus(e.getStatus());
            pageQueryVO.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(e.getStatus()));

            return pageQueryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public PageDataResp<MemberAppraisalSubmitPageQueryResp> pageSummaryMemberAppraisal(HttpHeaders headers, SubMemberIdRoleIdDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Page<MemberAppraisalDO> pageList = baseMemberAppraisalPage(loginUser, pageVO, roleTag);

        List<MemberAppraisalSubmitPageQueryResp> resultList = pageList.stream().map(e -> {
            MemberAppraisalSubmitPageQueryResp pageQueryVO = new MemberAppraisalSubmitPageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setName(e.getSubMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            pageQueryVO.setTotalScore(e.getTotalScore());
            pageQueryVO.setStatus(e.getStatus());
            pageQueryVO.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(e.getStatus()));
            return pageQueryVO;
        }).collect(Collectors.toList());
        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public PageDataResp<MemberAppraisalPageQueryResp> pageMemberAppraisal(MemberDO member, MemberRoleDO role, MemberDO subMember, MemberRoleDO subRole, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("createTime").descending());
        Page<MemberAppraisalDO> pageList = memberAppraisalRepository.findAll((Specification<MemberAppraisalDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), member));
            predicateList.add(cb.equal(root.get("role").as(MemberRoleDO.class), role));
            predicateList.add(cb.equal(root.get("subMember").as(MemberDO.class), subMember));
            predicateList.add(cb.equal(root.get("subRole").as(MemberRoleDO.class), subRole));
            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(e -> {
            MemberAppraisalPageQueryResp pageQueryVO = new MemberAppraisalPageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setName(e.getSubMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            pageQueryVO.setTotalScore(e.getTotalScore() == null ? "" : MemberConstant.BIG_DECIMAL_FORMAT.format(e.getTotalScore()));
            return pageQueryVO;
        }).collect(Collectors.toList()));
    }

    @Override
    public PageDataResp<MemberAppraisalAddPageQueryResp> pageWaitAddMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Page<MemberAppraisalDO> pageList = baseMemberAppraisalPage(loginUser, Collections.singletonList(MemberAppraisalStatusEnum.WAIT_GRADE.getCode()), pageVO, roleTag);

        List<MemberAppraisalAddPageQueryResp> resultList = pageList.stream().map(e -> {
            MemberAppraisalAddPageQueryResp pageQueryVO = new MemberAppraisalAddPageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setName(e.getSubMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            pageQueryVO.setStatus(e.getStatus());
            pageQueryVO.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(e.getStatus()));
            pageQueryVO.setUpdateOrDel(MemberAppraisalStatusEnum.WAIT_GRADE.getCode().equals(e.getStatus()));
//            pageQueryVO.setPublish(MemberAppraisalStatusEnum.WAIT_PUBLISH.getCode().equals(e.getStatus()));
            return pageQueryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public PageDataResp<MemberAppraisalGradePageQueryResp> pageWaitGradeMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());

        Page<MemberAppraisalDO> pageList = memberAppraisalRepository.findAll((Specification<MemberAppraisalDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            // 一对多关联, 查询记录数量为n倍, 以主表去重
            query.distinct(true);
            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("role").as(MemberRoleDO.class), loginUser.getMemberRoleId()));
            // 查询的是待提交考评结果的状态
            predicateList.add(cb.equal(root.get("status"), MemberAppraisalStatusEnum.WAIT_GRADE.getCode()));

            Join<MemberAppraisalDO, MemberAppraisalItemDO> appraisalItemJoin = root.join("items", JoinType.LEFT);
            predicateList.add(cb.equal(appraisalItemJoin.get("status"), MemberAppraisalItemStatusEnum.WAIT_GRADE.getCode()));
            predicateList.add(cb.equal(appraisalItemJoin.get("sendAppraisal"), EnableDisableStatusEnum.ENABLE.getCode()));

            Join<MemberAppraisalItemDO, UserDO> memberUserJoin = appraisalItemJoin.join("byUser", JoinType.LEFT);
            predicateList.add(cb.equal(memberUserJoin.get("id"), loginUser.getUserId()));

            if (StringUtils.isNotEmpty(pageVO.getName())) {
                Join<MemberAppraisalDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                predicateList.add(cb.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getSubject())) {
                predicateList.add(cb.like(root.get("subject"), "%" + pageVO.getSubject() + "%"));
            }

            if (Objects.nonNull(pageVO.getAppraisalDayStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateStart(pageVO.getAppraisalDayStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("appraisalTimeStart"), startDate));
            }

            if (Objects.nonNull(pageVO.getAppraisalDayEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateEnd(pageVO.getAppraisalDayEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("appraisalTimeEnd"), endDate));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
                predicateList.add(cb.equal(subRoleJoin.get("roleTag").as(Integer.class), roleTag));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<MemberAppraisalGradePageQueryResp> resultList = pageList.stream().map(e -> {
            MemberAppraisalGradePageQueryResp pageQueryVO = new MemberAppraisalGradePageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setName(e.getSubMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            // ===========用于展示============ start
            pageQueryVO.setStatus(MemberAppraisalStatusEnum.WAIT_GRADE.getCode());
            pageQueryVO.setStatusName(MemberAppraisalStatusEnum.WAIT_GRADE.getMessage());
            // ===========用于展示============ end
            pageQueryVO.setGrade(MemberAppraisalItemStatusEnum.WAIT_GRADE.getCode().equals(e.getStatus()));
            return pageQueryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public PageDataResp<MemberAppraisalSubmitPageQueryResp> pageWaitSubmitMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        List<Integer> statusLists = Arrays.asList(MemberAppraisalStatusEnum.WAIT_SUBMIT.getCode(),
                MemberAppraisalStatusEnum.WAIT_AUDIT_1_REJECT.getCode(),
                MemberAppraisalStatusEnum.WAIT_AUDIT_2_REJECT.getCode());

        Page<MemberAppraisalDO> pageList = baseMemberAppraisalPage(loginUser, statusLists, pageVO, roleTag);

        List<MemberAppraisalSubmitPageQueryResp> resultList = pageList.stream().map(e -> {
            MemberAppraisalSubmitPageQueryResp pageQueryVO = new MemberAppraisalSubmitPageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setName(e.getSubMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            pageQueryVO.setStatus(e.getStatus());
            pageQueryVO.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(e.getStatus()));
            pageQueryVO.setSubmitOrUpdate(statusLists.contains(e.getStatus()));
            return pageQueryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public PageDataResp<MemberAppraisalAuditPageQueryResp> pageWaitAuditOneMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Page<MemberAppraisalDO> pageList = baseMemberAppraisalPage(loginUser, Collections.singletonList(MemberAppraisalStatusEnum.WAIT_AUDIT_1.getCode()), pageVO, roleTag);

        List<MemberAppraisalAuditPageQueryResp> resultList = pageList.stream().map(e -> {
            MemberAppraisalAuditPageQueryResp pageQueryVO = new MemberAppraisalAuditPageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setName(e.getSubMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            pageQueryVO.setTotalScore(e.getTotalScore());
            pageQueryVO.setStatus(e.getStatus());
            pageQueryVO.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(e.getStatus()));
            pageQueryVO.setAudit(MemberAppraisalStatusEnum.WAIT_AUDIT_1.getCode().equals(e.getStatus()));
            return pageQueryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public PageDataResp<MemberAppraisalAuditPageQueryResp> pageWaitAuditTwoMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Page<MemberAppraisalDO> pageList = baseMemberAppraisalPage(loginUser, Collections.singletonList(MemberAppraisalStatusEnum.WAIT_AUDIT_2.getCode()), pageVO, roleTag);

        List<MemberAppraisalAuditPageQueryResp> resultList = pageList.stream().map(e -> {
            MemberAppraisalAuditPageQueryResp pageQueryVO = new MemberAppraisalAuditPageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setName(e.getSubMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            pageQueryVO.setTotalScore(e.getTotalScore());
            pageQueryVO.setStatus(e.getStatus());
            pageQueryVO.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(e.getStatus()));
            pageQueryVO.setAudit(MemberAppraisalStatusEnum.WAIT_AUDIT_2.getCode().equals(e.getStatus()));
            return pageQueryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public PageDataResp<MemberAppraisalNotificationPageQueryResp> pageWaitNotificationMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Page<MemberAppraisalDO> pageList = baseMemberAppraisalPage(loginUser, Collections.singletonList(MemberAppraisalStatusEnum.WAIT_NOTIFICATION.getCode()), pageVO, roleTag);

        List<MemberAppraisalNotificationPageQueryResp> resultList = pageList.stream().map(e -> {
            MemberAppraisalNotificationPageQueryResp pageQueryVO = new MemberAppraisalNotificationPageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setName(e.getSubMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            pageQueryVO.setTotalScore(e.getTotalScore());
            pageQueryVO.setStatus(e.getStatus());
            pageQueryVO.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(e.getStatus()));
            pageQueryVO.setNotification(MemberAppraisalStatusEnum.WAIT_NOTIFICATION.getCode().equals(e.getStatus()));
            return pageQueryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    private Page<MemberAppraisalDO> baseMemberAppraisalPage(UserLoginCacheDTO loginUser, List<Integer> statusList, MemberAppraisalPageDataReq pageVO, Integer roleTag) {
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());

        return memberAppraisalRepository.findAll((Specification<MemberAppraisalDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            if (!CollectionUtils.isEmpty(statusList)) {
                predicateList.add(cb.and(root.get("status").in(statusList)));
            }

            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("role").as(MemberRoleDO.class), loginUser.getMemberRoleId()));

            if (StringUtils.isNotEmpty(pageVO.getName())) {
                Join<MemberAppraisalDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                predicateList.add(cb.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getSubject())) {
                predicateList.add(cb.like(root.get("subject"), "%" + pageVO.getSubject() + "%"));
            }

            if (Objects.nonNull(pageVO.getAppraisalDayStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateStart(pageVO.getAppraisalDayStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("appraisalTimeStart"), startDate));
            }

            if (Objects.nonNull(pageVO.getAppraisalDayEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateEnd(pageVO.getAppraisalDayEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("appraisalTimeEnd"), endDate));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
                predicateList.add(cb.equal(subRoleJoin.get("roleTag").as(Integer.class), roleTag));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);
    }

    private Page<MemberAppraisalDO> baseMemberAppraisalPage(UserLoginCacheDTO loginUser, SubMemberIdRoleIdDataReq pageVO, Integer roleTag) {
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());

        return memberAppraisalRepository.findAll((Specification<MemberAppraisalDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("role").as(MemberRoleDO.class), loginUser.getMemberRoleId()));
            Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            predicateList.add(cb.equal(subMemberJoin.get("id").as(Long.class), pageVO.getSubMemberId()));
            predicateList.add(cb.equal(subRoleJoin.get("id").as(Long.class), pageVO.getSubRoleId()));

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                predicateList.add(cb.equal(subRoleJoin.get("roleTag").as(Integer.class), roleTag));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);
    }

    @Override
    public PageDataResp<MemberAppraisalSubResultPageQueryResp> pageSubResultMemberAppraisal(HttpHeaders headers, MemberAppraisalSubResultPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());

        Page<MemberAppraisalDO> pageList = memberAppraisalRepository.findAll((Specification<MemberAppraisalDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("notifyMember"), EnableDisableStatusEnum.ENABLE.getCode()));

            predicateList.add(cb.equal(root.get("subMember"), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("subRole"), loginUser.getMemberRoleId()));

            if (StringUtils.isNotEmpty(pageVO.getUpperName())) {
                Join<MemberAppraisalDO, MemberDO> upperMemberJoin = root.join("member", JoinType.LEFT);
                predicateList.add(cb.like(upperMemberJoin.get("subject"), "%" + pageVO.getUpperName() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getSubject())) {
                predicateList.add(cb.like(root.get("subject"), "%" + pageVO.getSubject() + "%"));
            }

            if (Objects.nonNull(pageVO.getAppraisalDayStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateStart(pageVO.getAppraisalDayStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("appraisalTimeStart"), startDate));
            }

            if (Objects.nonNull(pageVO.getAppraisalDayEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateEnd(pageVO.getAppraisalDayEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("appraisalTimeEnd"), endDate));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<MemberAppraisalSubResultPageQueryResp> resultList = pageList.stream().map(e -> {
            MemberAppraisalSubResultPageQueryResp pageQueryVO = new MemberAppraisalSubResultPageQueryResp();
            pageQueryVO.setId(e.getId());
            pageQueryVO.setAppraisalNo(e.getAppraisalNo());
            pageQueryVO.setSubject(e.getSubject());
            pageQueryVO.setUpperName(e.getMember().getName());
            pageQueryVO.setAppraisalDayStart(DateTimeUtil.formatDate(e.getAppraisalTimeStart()));
            pageQueryVO.setAppraisalDayEnd(DateTimeUtil.formatDate(e.getAppraisalTimeEnd()));
            pageQueryVO.setCompleteDay(DateTimeUtil.formatDate(e.getCompleteTime()));
            pageQueryVO.setTotalScore(e.getTotalScore());
            return pageQueryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public MemberAppraisalSubResultResp getSubMemberAppraisalResult(HttpHeaders headers, CommonIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberAppraisalDO memberAppraisalDO = memberAppraisalRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberAppraisalDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberAppraisalSubResultResp memberAppraisalResultVO = new MemberAppraisalSubResultResp();
        memberAppraisalResultVO.setId(memberAppraisalDO.getId());
        memberAppraisalResultVO.setAppraisalNo(memberAppraisalDO.getAppraisalNo());
        memberAppraisalResultVO.setStatus(memberAppraisalDO.getStatus());
        memberAppraisalResultVO.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()));
        memberAppraisalResultVO.setMemberId(memberAppraisalDO.getMember().getId());
        memberAppraisalResultVO.setRoleId(memberAppraisalDO.getRole().getId());
        memberAppraisalResultVO.setSubject(memberAppraisalDO.getSubject());
        memberAppraisalResultVO.setUpperName(memberAppraisalDO.getMember().getName());
        memberAppraisalResultVO.setAppraisalDayStart(DateTimeUtil.formatDate(memberAppraisalDO.getAppraisalTimeStart()));
        memberAppraisalResultVO.setAppraisalDayEnd(DateTimeUtil.formatDate(memberAppraisalDO.getAppraisalTimeEnd()));
        memberAppraisalResultVO.setCompleteDay(DateTimeUtil.formatDate(memberAppraisalDO.getCompleteTime()));
        memberAppraisalResultVO.setAttachments(FileObjectUtil.toVOList(memberAppraisalDO.getAttachments()));
        memberAppraisalResultVO.setTotalScore(Objects.nonNull(memberAppraisalDO.getTotalScore()) ? memberAppraisalDO.getTotalScore().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        memberAppraisalResultVO.setResult(memberAppraisalDO.getResult());
        memberAppraisalResultVO.setResultAttachments(FileObjectUtil.toVOList(memberAppraisalDO.getResultAttachments()));

        List<MemberAppraisalItemDO> memberAppraisalItems = memberAppraisalItemRepository.findAll((Specification<MemberAppraisalItemDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            Join<MemberAppraisalItemDO, MemberAppraisalDO> appraisalJoin = root.join("appraisal", JoinType.LEFT);
            predicateList.add(cb.equal(appraisalJoin.get("id"), memberAppraisalDO.getId()));

            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        });

        List<MemberAppraisalItemResp> items = memberAppraisalItems.stream().map(e -> {
            MemberAppraisalItemResp itemVO = new MemberAppraisalItemResp();
            itemVO.setId(e.getId());
            itemVO.setIndicatorGrouping(e.getIndicatorGrouping());
            itemVO.setStandardIndicator(e.getStandardIndicator());
            itemVO.setScoreMin(Objects.nonNull(e.getScoreMin()) ? e.getScoreMin().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScoreMax(Objects.nonNull(e.getScoreMax()) ? e.getScoreMax().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScoreStandard(e.getScoreStandard());
            itemVO.setWeight(Objects.nonNull(e.getWeight()) ? e.getWeight().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setUserId(e.getByUser().getId());
            itemVO.setUserName(e.getByUser().getName());
            itemVO.setSendAppraisal(e.getSendAppraisal());
            itemVO.setGrade(Objects.nonNull(e.getGrade()) ? e.getGrade().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScore(Objects.nonNull(e.getScore()) ? e.getScore().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setStatus(e.getStatus());
            itemVO.setReviewerFeedback(e.getReviewerFeedback());
            itemVO.setAppraisalAttachment(FileObjectUtil.toVOList(e.getAppraisalAttachment()));
            return itemVO;
        }).collect(Collectors.toList());
        memberAppraisalResultVO.setItems(items);

        return memberAppraisalResultVO;
    }

    @Override
    public MemberAppraisalResultResp getMemberAppraisalResult(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberAppraisalDO memberAppraisalDO = memberAppraisalRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberAppraisalDO)
                || Objects.isNull(memberAppraisalDO.getMember())
                || Objects.isNull(memberAppraisalDO.getRole())
                || !loginUser.getMemberId().equals(memberAppraisalDO.getMember().getId())
                || !loginUser.getMemberRoleId().equals(memberAppraisalDO.getRole().getId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberAppraisalDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberAppraisalDO.getSubRole().getRoleTag()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberAppraisalResultResp memberAppraisalResultResp = new MemberAppraisalResultResp();
        memberAppraisalResultResp.setId(memberAppraisalDO.getId());
        memberAppraisalResultResp.setAppraisalNo(memberAppraisalDO.getAppraisalNo());
        memberAppraisalResultResp.setStatus(memberAppraisalDO.getStatus());
        memberAppraisalResultResp.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()));
        memberAppraisalResultResp.setSubMemberId(memberAppraisalDO.getSubMember().getId());
        memberAppraisalResultResp.setSubRoleId(memberAppraisalDO.getSubRole().getId());
        memberAppraisalResultResp.setSubject(memberAppraisalDO.getSubject());
        memberAppraisalResultResp.setName(memberAppraisalDO.getSubMember().getName());
        memberAppraisalResultResp.setAppraisalDayStart(DateTimeUtil.formatDate(memberAppraisalDO.getAppraisalTimeStart()));
        memberAppraisalResultResp.setAppraisalDayEnd(DateTimeUtil.formatDate(memberAppraisalDO.getAppraisalTimeEnd()));
        memberAppraisalResultResp.setCompleteDay(DateTimeUtil.formatDate(memberAppraisalDO.getCompleteTime()));
        memberAppraisalResultResp.setAttachments(FileObjectUtil.toVOList(memberAppraisalDO.getAttachments()));
        memberAppraisalResultResp.setTotalScore(Objects.nonNull(memberAppraisalDO.getTotalScore()) ? memberAppraisalDO.getTotalScore().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        memberAppraisalResultResp.setResult(memberAppraisalDO.getResult());
        memberAppraisalResultResp.setNotifyMember(memberAppraisalDO.getNotifyMember());
        memberAppraisalResultResp.setResultAttachments(FileObjectUtil.toVOList(memberAppraisalDO.getResultAttachments()));

        List<MemberAppraisalItemDO> memberAppraisalItems = memberAppraisalDO.getItems();
        List<MemberAppraisalItemResp> items = memberAppraisalItems.stream().map(e -> {
            MemberAppraisalItemResp itemVO = new MemberAppraisalItemResp();
            itemVO.setId(e.getId());
            itemVO.setIndicatorGrouping(e.getIndicatorGrouping());
            itemVO.setStandardIndicator(e.getStandardIndicator());
            itemVO.setScoreMin(Objects.nonNull(e.getScoreMin()) ? e.getScoreMin().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScoreMax(Objects.nonNull(e.getScoreMax()) ? e.getScoreMax().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScoreStandard(e.getScoreStandard());
            itemVO.setWeight(Objects.nonNull(e.getWeight()) ? e.getWeight().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setUserId(Objects.nonNull(e.getByUser()) ? e.getByUser().getId() : null);
            itemVO.setUserName(Objects.nonNull(e.getByUser()) ? e.getByUser().getName() : "");
            itemVO.setSendAppraisal(e.getSendAppraisal());
            itemVO.setGrade(Objects.nonNull(e.getGrade()) ? e.getGrade().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScore(Objects.nonNull(e.getScore()) ? e.getScore().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setStatus(e.getStatus());
            itemVO.setReviewerFeedback(e.getReviewerFeedback());
            itemVO.setAppraisalAttachment(FileObjectUtil.toVOList(e.getAppraisalAttachment()));
            return itemVO;
        }).collect(Collectors.toList());
        memberAppraisalResultResp.setItems(items);


        WorkflowTaskListBO result = workflowFeignService.listMemberProcessSteps(memberAppraisalDO.getMember().getId(), MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId());
        
        if (StringUtils.isBlank(memberAppraisalDO.getTaskId()) && MemberAppraisalStatusEnum.SUBMIT_NOTIFICATION.getCode().equals(memberAppraisalDO.getStatus())) {
            memberAppraisalResultResp.setCurrentStep(7);
        } else {
            memberAppraisalResultResp.setCurrentStep((result.getCurrentStep()));
        }
        memberAppraisalResultResp.setVerifySteps(result.getStepList());
        memberAppraisalResultResp.setHistory(memberHistoryService.listMemberAppraisalHistory(memberAppraisalDO.getId()));

        return memberAppraisalResultResp;
    }

    @Override
    public MemberAppraisalResp getMemberAppraisal(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberAppraisalDO memberAppraisalDO = memberAppraisalRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberAppraisalDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberAppraisalDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberAppraisalDO.getSubRole().getRoleTag()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberAppraisalResp memberAppraisalResp = new MemberAppraisalResp();
        memberAppraisalResp.setId(memberAppraisalDO.getId());
        memberAppraisalResp.setAppraisalNo(memberAppraisalDO.getAppraisalNo());
        memberAppraisalResp.setStatus(memberAppraisalDO.getStatus());
        memberAppraisalResp.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()));
        memberAppraisalResp.setSubMemberId(memberAppraisalDO.getSubMember().getId());
        memberAppraisalResp.setSubRoleId(memberAppraisalDO.getSubRole().getId());
        memberAppraisalResp.setSubject(memberAppraisalDO.getSubject());
        memberAppraisalResp.setName(memberAppraisalDO.getSubMember().getName());
        memberAppraisalResp.setAppraisalDayStart(DateTimeUtil.formatDate(memberAppraisalDO.getAppraisalTimeStart()));
        memberAppraisalResp.setAppraisalDayEnd(DateTimeUtil.formatDate(memberAppraisalDO.getAppraisalTimeEnd()));
        memberAppraisalResp.setCompleteDay(DateTimeUtil.formatDate(memberAppraisalDO.getCompleteTime()));
        memberAppraisalResp.setAttachments(FileObjectUtil.toVOList(memberAppraisalDO.getAttachments()));

        List<MemberAppraisalItemDO> memberAppraisalItems = memberAppraisalDO.getItems();
        List<MemberAppraisalItemResp> items = memberAppraisalItems.stream().map(e -> {
            MemberAppraisalItemResp itemVO = new MemberAppraisalItemResp();
            itemVO.setId(e.getId());
            itemVO.setIndicatorGrouping(e.getIndicatorGrouping());
            itemVO.setStandardIndicator(e.getStandardIndicator());
            itemVO.setScoreMin(Objects.nonNull(e.getScoreMin()) ? e.getScoreMin().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScoreMax(Objects.nonNull(e.getScoreMax()) ? e.getScoreMax().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScoreStandard(e.getScoreStandard());
            itemVO.setWeight(Objects.nonNull(e.getWeight()) ? e.getWeight().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setUserId(Objects.nonNull(e.getByUser()) ? e.getByUser().getId() : null);
            itemVO.setUserName(Objects.nonNull(e.getByUser()) ? e.getByUser().getName() : "");
            itemVO.setSendAppraisal(e.getSendAppraisal());
            itemVO.setGrade(Objects.nonNull(e.getGrade()) ? e.getGrade().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScore(Objects.nonNull(e.getScore()) ? e.getScore().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setStatus(e.getStatus());
            itemVO.setReviewerFeedback(e.getReviewerFeedback());
            itemVO.setAppraisalAttachment(FileObjectUtil.toVOList(e.getAppraisalAttachment()));
            return itemVO;
        }).collect(Collectors.toList());
        memberAppraisalResp.setItems(items);


        WorkflowTaskListBO result = workflowFeignService.listMemberProcessSteps(memberAppraisalDO.getMember().getId(), MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId());
        
        if (StringUtils.isBlank(memberAppraisalDO.getTaskId()) && MemberAppraisalStatusEnum.SUBMIT_NOTIFICATION.getCode().equals(memberAppraisalDO.getStatus())) {
            memberAppraisalResp.setCurrentStep(7);
        } else {
            memberAppraisalResp.setCurrentStep((result.getCurrentStep()));
        }
        memberAppraisalResp.setVerifySteps(result.getStepList());
        memberAppraisalResp.setHistory(memberHistoryService.listMemberAppraisalHistory(memberAppraisalDO.getId()));

        return memberAppraisalResp;
    }

    @Override
    public MemberAppraisalResp getWaitGradeMemberAppraisal(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberAppraisalDO memberAppraisalDO = memberAppraisalRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberAppraisalDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberAppraisalDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberAppraisalDO.getSubRole().getRoleTag()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberAppraisalResp memberAppraisalResp = new MemberAppraisalResp();
        memberAppraisalResp.setId(memberAppraisalDO.getId());
        memberAppraisalResp.setAppraisalNo(memberAppraisalDO.getAppraisalNo());
        memberAppraisalResp.setStatus(memberAppraisalDO.getStatus());
        memberAppraisalResp.setStatusName(MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()));
        memberAppraisalResp.setSubMemberId(memberAppraisalDO.getSubMember().getId());
        memberAppraisalResp.setSubRoleId(memberAppraisalDO.getSubRole().getId());
        memberAppraisalResp.setSubject(memberAppraisalDO.getSubject());
        memberAppraisalResp.setName(memberAppraisalDO.getSubMember().getName());
        memberAppraisalResp.setAppraisalDayStart(DateTimeUtil.formatDate(memberAppraisalDO.getAppraisalTimeStart()));
        memberAppraisalResp.setAppraisalDayEnd(DateTimeUtil.formatDate(memberAppraisalDO.getAppraisalTimeEnd()));
        memberAppraisalResp.setCompleteDay(DateTimeUtil.formatDate(memberAppraisalDO.getCompleteTime()));
        memberAppraisalResp.setAttachments(FileObjectUtil.toVOList(memberAppraisalDO.getAttachments()));

        List<MemberAppraisalItemDO> memberAppraisalItems = memberAppraisalItemRepository.findAll((Specification<MemberAppraisalItemDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("appraisal").as(MemberAppraisalDO.class), memberAppraisalDO.getId()));
            predicateList.add(cb.equal(root.get("byUser").as(UserDO.class), loginUser.getUserId()));

            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        });

        List<MemberAppraisalItemResp> items = memberAppraisalItems.stream().map(e -> {
            MemberAppraisalItemResp itemVO = new MemberAppraisalItemResp();
            itemVO.setId(e.getId());
            itemVO.setIndicatorGrouping(e.getIndicatorGrouping());
            itemVO.setStandardIndicator(e.getStandardIndicator());
            itemVO.setScoreMin(Objects.nonNull(e.getScoreMin()) ? e.getScoreMin().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScoreMax(Objects.nonNull(e.getScoreMax()) ? e.getScoreMax().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScoreStandard(e.getScoreStandard());
            itemVO.setWeight(Objects.nonNull(e.getWeight()) ? e.getWeight().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setUserId(Objects.nonNull(e.getByUser()) ? e.getByUser().getId() : null);
            itemVO.setUserName(Objects.nonNull(e.getByUser()) ? e.getByUser().getName() : "");
            itemVO.setSendAppraisal(e.getSendAppraisal());
            itemVO.setGrade(Objects.nonNull(e.getGrade()) ? e.getGrade().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setScore(Objects.nonNull(e.getScore()) ? e.getScore().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            itemVO.setStatus(e.getStatus());
            itemVO.setReviewerFeedback(e.getReviewerFeedback());
            itemVO.setAppraisalAttachment(FileObjectUtil.toVOList(e.getAppraisalAttachment()));
            return itemVO;
        }).collect(Collectors.toList());
        memberAppraisalResp.setItems(items);


        WorkflowTaskListBO result = workflowFeignService.listMemberProcessSteps(memberAppraisalDO.getMember().getId(), MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId());
        
        if (StringUtils.isBlank(memberAppraisalDO.getTaskId()) && MemberAppraisalStatusEnum.SUBMIT_NOTIFICATION.getCode().equals(memberAppraisalDO.getStatus())) {
            memberAppraisalResp.setCurrentStep(7);
        } else {
            memberAppraisalResp.setCurrentStep((result.getCurrentStep()));
        }
        memberAppraisalResp.setVerifySteps(result.getStepList());
        memberAppraisalResp.setHistory(memberHistoryService.listMemberAppraisalHistory(memberAppraisalDO.getId()));

        return memberAppraisalResp;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addMemberAppraisal(HttpHeaders headers, MemberAppraisalAddReq addVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = memberRelationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), addVO.getSubMemberId(), addVO.getSubRoleId());
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        // 查询考评用户
        List<UserDO> users = Collections.emptyList();
        if (!CollectionUtils.isEmpty(addVO.getItems())) {
            // 过滤掉userId为0的数据
            Set<Long> userIds = addVO.getItems().stream().map(MemberAppraisalItemAddReq::getUserId).filter(userId -> userId != 0).collect(Collectors.toSet());
            users = userRepository.findAllById(userIds);
            if (users.size() != userIds.size()) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }
        }
        Map<Long, UserDO> userMap = users.stream().collect(Collectors.toMap(UserDO::getId, e -> e));

        //校验考评记录
        checkAppraisalRecords(loginUser, addVO.getItems(), addVO.getSubmitVO(), userMap);

        //保存考评表
        MemberAppraisalDO memberAppraisalDO = getMemberAppraisalList(relationDO, addVO);
        memberAppraisalRepository.saveAndFlush(memberAppraisalDO);
        memberAppraisalDO.setAppraisalNo("PA" + CodeUtil.digits32(memberAppraisalDO.getId(), 4));
        memberAppraisalRepository.saveAndFlush(memberAppraisalDO);

        //保存考评项
        memberAppraisalItemRepository.saveAll(getMemberAppraisalItemList(memberAppraisalDO, addVO.getItems(), userMap));

        // 启动工作流
        WorkflowTaskResultBO result = workflowFeignService.startMemberProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId());

        //如果考评记录表中【发送考评人打分】一项有勾选，则提交后将勾选的指标发送给对应用户形成待办，工作流流转至【待考评打分】环节
        //如果考评记录表中【发送考评人打分】每一项都是未勾选，需要用户填写【考评结果】栏，并提交给工作流的【待审核考评结果(一级)】进行处理
        long sendAppraisalCount = addVO.getItems().stream().map(MemberAppraisalItemAddReq::getSendAppraisal).filter(sendAppraisal -> sendAppraisal > 0).count();
        if (sendAppraisalCount == 0) {
            MemberAppraisalAddResultReq submitVO = addVO.getSubmitVO();
            memberAppraisalDO.setTotalScore(submitVO.getTotalScore());
            memberAppraisalDO.setResult(submitVO.getResult());
            memberAppraisalDO.setNotifyMember(submitVO.getNotifyMember());
            memberAppraisalDO.setResultAttachments(FileObjectUtil.toBOList(submitVO.getResultAttachments()));
            workflowFeignService.execMemberSerialProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, result.getTaskId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), 3, Arrays.asList(null, null, null));
        } else {
            workflowFeignService.execMemberProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, result.getTaskId(),
                    loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), null);
        }

        //工作流oper返回有些是 数字,有些是中文,这里做兼容
        String operation = result.getOperation();
        MemberValidateHistoryOperationEnum operationEnum = MemberValidateHistoryOperationEnum.parseString(operation);
        if (!MemberValidateHistoryOperationEnum.UNKNOWN.getCode().equals(operationEnum.getCode())) {
            operation = operationEnum.getMessage();
        }
        //保存历史流转记录
        memberHistoryService.saveMemberAppraisalHistory(loginUser, memberAppraisalDO.getId(), result.getInnerStatus(),
                MemberAppraisalStatusEnum.getCodeMessage(result.getInnerStatus()), operation, "");

        memberAppraisalDO.setStatus(result.getInnerStatus());
        memberAppraisalDO.setTaskId(result.getTaskId());
        memberAppraisalRepository.saveAndFlush(memberAppraisalDO);

        // 发送消息
        if (sendAppraisalCount == 0) {
            // 发送消息
            messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), null, roleTag);
        } else {
            // 通知待考评打分会员
            userMap.keySet().forEach(userId -> messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), userId, roleTag));
        }

    }

    private void checkAppraisalRecords(UserLoginCacheDTO loginUser, List<MemberAppraisalItemAddReq> appraisalItemList, MemberAppraisalAddResultReq appraisalAddResultVO, Map<Long, UserDO> userMap) {
        BigDecimal totalScoreWeight = BigDecimal.ZERO;
        long sendAppraisalCount = 0;
        for (MemberAppraisalItemAddReq item : appraisalItemList) {
            totalScoreWeight = totalScoreWeight.add(item.getWeight());
            sendAppraisalCount += item.getSendAppraisal();
            //如果有指定发送考评人
            if (EnableDisableStatusEnum.ENABLE.getCode().equals(item.getSendAppraisal())) {
                // 如果是需要发送用户考评，但状态却是已打分则报错
                if (MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getCode().equals(item.getStatus()) && EnableDisableStatusEnum.ENABLE.getCode().equals(item.getSendAppraisal())) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_REQUIRED_EVALUATION_BUT_STATUS_IS_SCORED);
                }
                // 发送考评人打分项不允许填写考评计分、考评人反馈、附件
                if (NumberUtil.isGteZero(item.getGrade()) || NumberUtil.isGteZero(item.getScore()) || StringUtils.isNotBlank(item.getReviewerFeedback()) || !CollectionUtils.isEmpty(item.getAppraisalAttachment())) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_BE_FILLED_WHEN_SENDING_APPRAISER_SCORING);
                }
                // 校验考评人是否存在
                if (NumberUtil.isNullOrLteZero(item.getUserId()) || Objects.isNull(userMap.get(item.getUserId()))) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_APPRAISER_SPECIFIED_SEND_DOES_NOT_EXIST);
                }
                // 如果存在指定用户打分，就不能直接提交汇总评分
                if (!Objects.isNull(appraisalAddResultVO) && NumberUtil.isGteZero(appraisalAddResultVO.getTotalScore())) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_SUBMIT_SUMMARY_SCORE_DIRECTLY);
                }
            } else {
                //如果没有指定发送考评人，则校验填写分数是否合理
                if (item.getScoreMin().compareTo(item.getGrade()) > 0 || item.getScoreMax().compareTo(item.getGrade()) < 0) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_SCORE_CANNOT_EXCEED_THE_RANGE);
                }
                //如果没有指定发送考评人，则用户id必须是登录用户
                if (!loginUser.getUserId().equals(item.getUserId())) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_SELF_APPRAISER_USER_MUST_BE_CURRENT_LOGIN_USER);
                }
            }
        }
        //如果没有发送考评人打分，则必须有考评结果
        if (sendAppraisalCount == 0 && (Objects.isNull(appraisalAddResultVO) || !NumberUtil.isGteZero(appraisalAddResultVO.getTotalScore()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_EVALUATION_RESULT_MUST_FILLED_FOR_SELF_EVALUATION);
        }
        //权重和相加必须等于100%
        if (totalScoreWeight.compareTo((new BigDecimal(100))) != 0) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_TOTAL_SCORE_WEIGHT);
        }
    }

    private MemberAppraisalDO getMemberAppraisalList(MemberRelationDO relationDO, MemberAppraisalAddReq addVO) {
        MemberAppraisalDO memberAppraisalDO = new MemberAppraisalDO();
        memberAppraisalDO.setStatus(MemberAppraisalStatusEnum.WAIT_GRADE.getCode());
        memberAppraisalDO.setSubject(addVO.getSubject());
        memberAppraisalDO.setSubMember(relationDO.getSubMember());
        memberAppraisalDO.setSubRole(relationDO.getSubRole());
        memberAppraisalDO.setAppraisalTimeStart(DateTimeUtil.parseConcatDateTime(addVO.getAppraisalDayStart()));
        memberAppraisalDO.setAppraisalTimeEnd(DateTimeUtil.parseConcatDateTime(addVO.getAppraisalDayEnd()));
        memberAppraisalDO.setCompleteTime(DateTimeUtil.parseConcatDateTime(addVO.getCompleteDay()));
        memberAppraisalDO.setAttachments(FileObjectUtil.toBOList(addVO.getAttachments()));
        memberAppraisalDO.setMember(relationDO.getMember());
        memberAppraisalDO.setRole(relationDO.getRole());
        memberAppraisalDO.setCreateTime(LocalDateTime.now());
        return memberAppraisalDO;
    }

    private List<MemberAppraisalItemDO> getMemberAppraisalItemList(MemberAppraisalDO memberAppraisalDO, List<MemberAppraisalItemAddReq> appraisalItemList, Map<Long, UserDO> userMap) {
        return appraisalItemList.stream().map(e -> {
            MemberAppraisalItemDO item = new MemberAppraisalItemDO();
            item.setIndicatorGrouping(e.getIndicatorGrouping());
            item.setStandardIndicator(e.getStandardIndicator());
            item.setScoreMin(e.getScoreMin());
            item.setScoreMax(e.getScoreMax());
            item.setScoreStandard(e.getScoreStandard());
            item.setWeight(e.getWeight());
            item.setGrade(EnableDisableStatusEnum.DISABLE.getCode().equals(e.getSendAppraisal()) ? e.getGrade() : BigDecimal.ZERO);
            item.setScore(EnableDisableStatusEnum.DISABLE.getCode().equals(e.getSendAppraisal()) ? e.getScore() : BigDecimal.ZERO);
            item.setReviewerFeedback(EnableDisableStatusEnum.DISABLE.getCode().equals(e.getSendAppraisal()) ? e.getReviewerFeedback() : "");
            item.setStatus(EnableDisableStatusEnum.DISABLE.getCode().equals(e.getSendAppraisal()) ? MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getCode() : MemberAppraisalItemStatusEnum.WAIT_GRADE.getCode());
            item.setSendAppraisal(Objects.isNull(e.getSendAppraisal()) ? EnableDisableStatusEnum.DISABLE.getCode() : e.getSendAppraisal());
            item.setAppraisalAttachment(FileObjectUtil.toBOList(e.getAppraisalAttachment()));
            item.setByUser(NumberUtil.notNullOrZero(e.getUserId()) ? userMap.get(e.getUserId()) : null);
            item.setAppraisal(memberAppraisalDO);
            return item;
        }).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMemberAppraisal(HttpHeaders headers, MemberAppraisalUpdateReq addVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberRelationDO relationDO = memberRelationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), addVO.getSubMemberId(), addVO.getSubRoleId());
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberAppraisalDO memberAppraisalDO = memberAppraisalRepository.findById(addVO.getId()).orElse(null);
        if (Objects.isNull(memberAppraisalDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberAppraisalStatusEnum.WAIT_GRADE.getCode().equals(memberAppraisalDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberAppraisalDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberAppraisalDO.getSubRole().getRoleTag()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 查询考评用户
        List<UserDO> users = Collections.emptyList();
        Set<Long> newUserIdSet = addVO.getItems().stream().map(MemberAppraisalItemAddReq::getUserId).filter(userId -> userId != 0).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(addVO.getItems())) {
            users = userRepository.findAllById(newUserIdSet);
            if (users.size() != newUserIdSet.size()) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }
        }
        Map<Long, UserDO> userMap = users.stream().collect(Collectors.toMap(UserDO::getId, e -> e));

        //校验考评记录
        checkAppraisalRecords(loginUser, addVO.getItems(), addVO.getSubmitVO(), userMap);

        //保存考评项 先删除再重新新增
        memberAppraisalItemRepository.deleteByAppraisalId(memberAppraisalDO.getId());
        memberAppraisalItemRepository.saveAll(getMemberAppraisalItemList(memberAppraisalDO, addVO.getItems(), userMap));


        //如果考评记录表中【发送考评人打分】一项有勾选，则提交后将勾选的指标发送给对应用户形成待办，工作流流转至【待考评打分】环节
        //如果考评记录表中【发送考评人打分】每一项都是未勾选，需要用户填写【考评结果】栏，并提交给工作流的【待审核考评结果(一级)】进行处理
        long sendAppraisalCount = addVO.getItems().stream().map(MemberAppraisalItemAddReq::getSendAppraisal).filter(sendAppraisal -> sendAppraisal > 0).count();
        if (sendAppraisalCount == 0) {
            MemberAppraisalAddResultReq submitVO = addVO.getSubmitVO();
            memberAppraisalDO.setTotalScore(submitVO.getTotalScore());
            memberAppraisalDO.setResult(submitVO.getResult());
            memberAppraisalDO.setNotifyMember(submitVO.getNotifyMember());
            memberAppraisalDO.setResultAttachments(FileObjectUtil.toBOList(submitVO.getResultAttachments()));
            WorkflowTaskResultBO result = workflowFeignService.execMemberSerialProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), 2, Arrays.asList(null, null));

            //工作流oper返回有些是数字,有些是中文,这里做兼容
            String operation = result.getOperation();
            MemberValidateHistoryOperationEnum operationEnum = MemberValidateHistoryOperationEnum.parseString(operation);
            if (!MemberValidateHistoryOperationEnum.UNKNOWN.getCode().equals(operationEnum.getCode())) {
                operation = operationEnum.getMessage();
            }

            //保存历史流转记录
            memberHistoryService.saveMemberAppraisalHistory(loginUser, memberAppraisalDO.getId(), memberAppraisalDO.getStatus(),
                    MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()), operation, "");

            memberAppraisalDO.setStatus(result.getInnerStatus());
            memberAppraisalDO.setTaskId(result.getTaskId());
            memberAppraisalRepository.saveAndFlush(memberAppraisalDO);
        }

        // 首页统计
        if (sendAppraisalCount == 0) {
            // 发送消息
            messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), null, roleTag);
        } else {
            //求新增的指定要发送消息的用户
            newUserIdSet.removeAll(users.stream().map(UserDO::getId).collect(Collectors.toSet()));

            // 批量通知待考评打分会员
            userMap.keySet().forEach(userId -> messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), userId, roleTag));
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMemberAppraisal(HttpHeaders headers, CommonIdListReq idsVO) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<MemberAppraisalDO> memberAppraisalDOList = memberAppraisalRepository.findAllById(idsVO.getIdList());
        //校验当前会员有删除权限
        if (CollectionUtils.isEmpty(memberAppraisalDOList) || memberAppraisalDOList.stream().anyMatch(appraisal -> !appraisal.getMember().getId().equals(loginCacheDTO.getMemberId()) || !appraisal.getRole().getId().equals(loginCacheDTO.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        for (MemberAppraisalDO memberAppraisalDO : memberAppraisalDOList) {
            if (!MemberAppraisalStatusEnum.WAIT_GRADE.getCode().equals(memberAppraisalDO.getStatus())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
            }
        }

        for (MemberAppraisalDO memberAppraisalDO : memberAppraisalDOList) {
            memberAppraisalItemRepository.deleteByAppraisalId(memberAppraisalDO.getId());
        }

        memberAppraisalRepository.deleteAll(memberAppraisalDOList);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void gradeMemberAppraisal(HttpHeaders headers, MemberAppraisalGradeReq gradeVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberAppraisalDO memberAppraisalDO = memberAppraisalRepository.findById(gradeVO.getId()).orElse(null);
        if (Objects.isNull(memberAppraisalDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberAppraisalDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberAppraisalDO.getSubRole().getRoleTag()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 前端提交的考评打分项目map
        Map<Long, MemberAppraisalItemGradeReq> itemGradeMap = gradeVO.getItems().stream().collect(Collectors.toMap(MemberAppraisalItemGradeReq::getId, e -> e, (e1, e2) -> e2));
        // 数据库中考评打分的项目
        List<MemberAppraisalItemDO> items = memberAppraisalDO.getItems();
        // 分组为属于用户打分的项目
        Map<Long, List<MemberAppraisalItemDO>> itemMap = items.stream().collect(Collectors.groupingBy(item -> item.getByUser().getId()));
        // 属于该用户打分的项目
        List<MemberAppraisalItemDO> waitGradeItems = itemMap.get(loginUser.getUserId());

        //校验该用户待考评的每一项是否都传了
        if (CollectionUtils.isEmpty(waitGradeItems) || waitGradeItems.stream().anyMatch(waitGradeItem -> !itemGradeMap.containsKey(waitGradeItem.getId()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_APPRAISAL_ITEM_NO_RELATION);
        }

        //校验考评记录是否合理
        for (MemberAppraisalItemGradeReq appraisalItemVO : gradeVO.getItems()) {
            MemberAppraisalItemDO memberAppraisalItemDO = waitGradeItems.stream().filter(appraisalItemDO -> appraisalItemDO.getId().equals(appraisalItemVO.getId())).findFirst().orElse(null);
            if (Objects.isNull(memberAppraisalItemDO)) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_APPRAISAL_ITEM_NO_RELATION);
            }
            //校验填写分数是否合理
            if (memberAppraisalItemDO.getScoreMin().compareTo(appraisalItemVO.getGrade()) > 0 || memberAppraisalItemDO.getScoreMax().compareTo(appraisalItemVO.getGrade()) < 0) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_SCORE_CANNOT_EXCEED_THE_RANGE);
            }
        }

        for (MemberAppraisalItemDO memberAppraisalItemDO : waitGradeItems) {
            MemberAppraisalItemGradeReq itemGradeVO = itemGradeMap.get(memberAppraisalItemDO.getId());
            memberAppraisalItemDO.setStatus(MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getCode());
            memberAppraisalItemDO.setGrade(itemGradeVO.getGrade());
            memberAppraisalItemDO.setScore(itemGradeVO.getScore());
            memberAppraisalItemDO.setReviewerFeedback(itemGradeVO.getReviewerFeedback());
            memberAppraisalItemDO.setAppraisalAttachment(FileObjectUtil.toBOList(itemGradeVO.getAppraisalAttachment()));
        }
        memberAppraisalItemRepository.saveAll(items);

        memberHistoryService.saveMemberAppraisalHistory(loginUser, memberAppraisalDO.getId(), MemberAppraisalStatusEnum.WAIT_GRADE.getCode(),
                MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getMessage(), "考评打分", "");

        //如果考评单每一项都打分完毕，工作流推进到待汇总评分
        if (items.stream().allMatch(appraisalItemDO -> MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getCode().equals(appraisalItemDO.getStatus()))) {
            WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId(),
                    loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), null);
            memberAppraisalDO.setStatus(result.getInnerStatus());
            memberAppraisalDO.setTaskId(result.getTaskId());
            memberAppraisalRepository.saveAndFlush(memberAppraisalDO);
            messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), null, roleTag);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitMemberAppraisal(HttpHeaders headers, MemberAppraisalSubmitReq submitVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberAppraisalDO memberAppraisalDO = memberAppraisalRepository.findById(submitVO.getId()).orElse(null);
        if (Objects.isNull(memberAppraisalDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberAppraisalDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberAppraisalDO.getSubRole().getRoleTag()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 状态校验
        List<Integer> statusLists = Arrays.asList(MemberAppraisalStatusEnum.WAIT_SUBMIT.getCode(),
                MemberAppraisalStatusEnum.WAIT_AUDIT_1_REJECT.getCode(),
                MemberAppraisalStatusEnum.WAIT_AUDIT_2_REJECT.getCode());
        if (!statusLists.contains(memberAppraisalDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 如果存在未打分的项目，不允许提交汇总考评结果
        if (memberAppraisalDO.getItems().stream().anyMatch(appraisalItemDO -> MemberAppraisalItemStatusEnum.WAIT_GRADE.getCode().equals(appraisalItemDO.getStatus()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_EXISTS_UNPRICED_ITEMS);
        }

        // 考评打分项目map
        Map<Long, MemberAppraisalItemSubmitReq> itemSubmitMap = submitVO.getItems().stream().collect(Collectors.toMap(MemberAppraisalItemSubmitReq::getId, e -> e, (e1, e2) -> e2));

        // 需要用户打分的项目 userId - itemDOList
//        Map<Long, List<MemberAppraisalItemDO>> userItemMap = memberAppraisalDO.getItems().stream().collect(Collectors.groupingBy(item -> item.getByUser().getId()));

        // 校验考评记录是否合理
        List<MemberAppraisalItemDO> saveItemList = new ArrayList<>();
        for (MemberAppraisalItemDO appraisalItemDO : memberAppraisalDO.getItems()) {
            MemberAppraisalItemSubmitReq appraisalItemSubmitVO = itemSubmitMap.get(appraisalItemDO.getId());
            //校验填写分数是否合理
            if (Objects.nonNull(appraisalItemSubmitVO)) {
                if (appraisalItemDO.getScoreMin().compareTo(appraisalItemSubmitVO.getGrade()) > 0 || appraisalItemDO.getScoreMax().compareTo(appraisalItemSubmitVO.getGrade()) < 0) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_SCORE_CANNOT_EXCEED_THE_RANGE);
                }
                appraisalItemDO.setGrade(appraisalItemSubmitVO.getGrade());
                appraisalItemDO.setScore(appraisalItemSubmitVO.getScore());
                appraisalItemDO.setReviewerFeedback(appraisalItemSubmitVO.getReviewerFeedback());
                appraisalItemDO.setAppraisalAttachment(FileObjectUtil.toBOList(appraisalItemSubmitVO.getAppraisalAttachment()));
                saveItemList.add(appraisalItemDO);
            }
        }

        // 更新的考评项目集合
        memberAppraisalItemRepository.saveAll(saveItemList);

        // 考评结果
        memberAppraisalDO.setTotalScore(submitVO.getTotalScore());
        memberAppraisalDO.setResult(submitVO.getResult());
        memberAppraisalDO.setNotifyMember(submitVO.getNotifyMember());
        memberAppraisalDO.setResultAttachments(FileObjectUtil.toBOList(submitVO.getResultAttachments()));
        memberAppraisalRepository.saveAndFlush(memberAppraisalDO);

        // 工作流
        WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId(),
                loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), ProcessTaskStatusEnum.GOTO_NEXT_STEP.getCode());

        memberAppraisalDO.setStatus(result.getInnerStatus());
        memberAppraisalDO.setTaskId(result.getTaskId());
        memberAppraisalRepository.saveAndFlush(memberAppraisalDO);

        // 发送消息
        messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), null, roleTag);

        memberHistoryService.saveMemberAppraisalHistory(loginUser, memberAppraisalDO.getId(), memberAppraisalDO.getStatus(),
                MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()), result.getOperation(), "");

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditOneMemberAppraisal(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberAppraisalDO memberAppraisalDO = memberAppraisalRepository.findById(agreeVO.getId()).orElse(null);
        if (Objects.isNull(memberAppraisalDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberAppraisalDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberAppraisalDO.getSubRole().getRoleTag()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }


        if (!MemberAppraisalStatusEnum.WAIT_AUDIT_1.getCode().equals(memberAppraisalDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 工作流
        WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId(),
                loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), agreeVO.getAgree());

        memberAppraisalDO.setStatus(result.getInnerStatus());
        memberAppraisalDO.setTaskId(result.getTaskId());
        memberAppraisalRepository.saveAndFlush(memberAppraisalDO);

        // 发送消息
        messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), null, roleTag);

        memberHistoryService.saveMemberAppraisalHistory(loginUser, memberAppraisalDO.getId(), memberAppraisalDO.getStatus(),
                MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()), result.getOperation(), agreeVO.getReason());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditOneBatchMemberAppraisal(HttpHeaders headers, CommonIdListReq idsVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<MemberAppraisalDO> memberAppraisalDOList = memberAppraisalRepository.findAllById(idsVO.getIdList());
        if (CollectionUtils.isEmpty(memberAppraisalDOList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        for (MemberAppraisalDO memberAppraisalDO : memberAppraisalDOList) {
            if (!MemberAppraisalStatusEnum.WAIT_AUDIT_1.getCode().equals(memberAppraisalDO.getStatus())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
            }
        }

        for (MemberAppraisalDO memberAppraisalDO : memberAppraisalDOList) {
            // 工作流
            WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId(),
                    loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), EnableDisableStatusEnum.ENABLE.getCode());

            memberAppraisalDO.setStatus(result.getInnerStatus());
            memberAppraisalDO.setTaskId(result.getTaskId());
            memberAppraisalRepository.saveAndFlush(memberAppraisalDO);

            // 发送消息
            messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), null, roleTag);

            memberHistoryService.saveMemberAppraisalHistory(loginUser, memberAppraisalDO.getId(), memberAppraisalDO.getStatus(),
                    MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()), result.getOperation(), "");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditTwoMemberAppraisal(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberAppraisalDO memberAppraisalDO = memberAppraisalRepository.findById(agreeVO.getId()).orElse(null);
        if (Objects.isNull(memberAppraisalDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) &&
                (memberAppraisalDO.getSubRole().getRoleTag() == null ||
                        !roleTag.equals(memberAppraisalDO.getSubRole().getRoleTag()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberAppraisalStatusEnum.WAIT_AUDIT_2.getCode().equals(memberAppraisalDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 工作流
        WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId(),
                loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), agreeVO.getAgree());

        memberAppraisalDO.setStatus(result.getInnerStatus());
        memberAppraisalDO.setTaskId(result.getTaskId());
        memberAppraisalRepository.saveAndFlush(memberAppraisalDO);

        // 发送消息
        messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), null, roleTag);

        memberHistoryService.saveMemberAppraisalHistory(loginUser, memberAppraisalDO.getId(), memberAppraisalDO.getStatus(),
                MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()), result.getOperation(), agreeVO.getReason());

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditTwoBatchMemberAppraisal(HttpHeaders headers, CommonIdListReq idsVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<MemberAppraisalDO> memberAppraisalDOList = memberAppraisalRepository.findAllById(idsVO.getIdList());
        if (CollectionUtils.isEmpty(memberAppraisalDOList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        for (MemberAppraisalDO memberAppraisalDO : memberAppraisalDOList) {
            if (!MemberAppraisalStatusEnum.WAIT_AUDIT_2.getCode().equals(memberAppraisalDO.getStatus())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
            }
        }

        for (MemberAppraisalDO memberAppraisalDO : memberAppraisalDOList) {
            // 工作流
            WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId(),
                    loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), EnableDisableStatusEnum.ENABLE.getCode());

            memberAppraisalDO.setStatus(result.getInnerStatus());
            memberAppraisalDO.setTaskId(result.getTaskId());
            memberAppraisalRepository.saveAndFlush(memberAppraisalDO);

            // 发送消息
            messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), null, roleTag);

            memberHistoryService.saveMemberAppraisalHistory(loginUser, memberAppraisalDO.getId(), memberAppraisalDO.getStatus(),
                    MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()), result.getOperation(), "");
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void notificationMemberAppraisal(HttpHeaders headers, CommonIdListReq idsVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<MemberAppraisalDO> memberAppraisalList = memberAppraisalRepository.findAllById(idsVO.getIdList());
        if (CollectionUtils.isEmpty(memberAppraisalList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 状态校验
        for (MemberAppraisalDO memberAppraisalDO : memberAppraisalList) {
            if (!MemberAppraisalStatusEnum.WAIT_NOTIFICATION.getCode().equals(memberAppraisalDO.getStatus())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
            }
        }

        for (MemberAppraisalDO memberAppraisalDO : memberAppraisalList) {
            // 工作流
            WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(MemberConstant.MEMBER_APPRAISAL_PROCESS_KEY, memberAppraisalDO.getTaskId(),
                    loginUser.getMemberId(), loginUser.getMemberRoleId(), memberAppraisalDO.getId(), ProcessTaskStatusEnum.GOTO_NEXT_STEP.getCode());
            memberAppraisalDO.setStatus(result.getInnerStatus());
            memberAppraisalDO.setTaskId(result.getTaskId());
            memberAppraisalRepository.saveAndFlush(memberAppraisalDO);

            if (EnableDisableStatusEnum.ENABLE.getCode().equals(memberAppraisalDO.getNotifyMember())) {
                messageFeignService.sendMemberAppraisalMessage(MemberAppraisalMessageDTO.appraisalTransform(memberAppraisalDO), null, roleTag);
            }

            memberHistoryService.saveMemberAppraisalHistory(loginUser, memberAppraisalDO.getId(), memberAppraisalDO.getStatus(),
                    MemberAppraisalStatusEnum.getCodeMessage(memberAppraisalDO.getStatus()), result.getOperation(), "");
        }

    }
}
