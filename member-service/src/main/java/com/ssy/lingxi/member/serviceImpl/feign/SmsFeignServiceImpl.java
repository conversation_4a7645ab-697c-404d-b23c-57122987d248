package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.service.feign.ISmsFeignService;
import com.ssy.lingxi.support.api.enums.SmsTemplateEnum;
import com.ssy.lingxi.support.api.feign.ISmsFeign;
import com.ssy.lingxi.support.api.model.req.SmsReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 调用短信服务Feign接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-09
 */
@Service
public class SmsFeignServiceImpl implements ISmsFeignService {

    /**
     * 短信服务提供的Feign接口
     */
    @Resource
    private ISmsFeign sendSmsFeign;

    /**
     * 发送会员注册时的短信验证码
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     */
    @Override
    public void sendRegisterSms(String telCode, String phone, String smsCode) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.REGISTER.getCode(), Collections.singletonList(smsCode));
    }

    /**
     * 发送手机号登录的短信
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     * @param templateCode  短信模版
     */
    @Override
    public void sendPhoneLoginSms(String telCode, String phone, String smsCode, String templateCode) {
        sendSmsViaFeign(telCode, phone, templateCode, Collections.singletonList(smsCode));
    }

    /**
     * 发送平台后台登陆页的忘记密码短信验证码
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     */
    @Override
    public void sendPlatformLoginForgetPwdSms(String telCode, String phone, String smsCode) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.PLATFORM_LOGIN_FORGET_PWD.getCode(), Collections.singletonList(smsCode));
    }

    /**
     * 发送通过手机号重设密码时的短信验证码
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     */
    @Override
    public void sendResetPasswordSms(String telCode, String phone, String smsCode) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.UPDATE_PWD.getCode(), Collections.singletonList(smsCode));
    }

    /**
     * 发送更改密码短信
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     */
    @Override
    public void sendChangePasswordSms(String telCode, String phone, String smsCode) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.UPDATE_PWD.getCode(), Collections.singletonList(smsCode));
    }

    /**
     * 发送更改邮箱短信
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     */
    @Override
    public void sendChangeEmailSms(String telCode, String phone, String smsCode) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.UPDATE_EMAIL.getCode(), Collections.singletonList(smsCode));
    }

    /**
     * 发送更改手机号码短信
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     */
    @Override
    public void sendChangePhoneSms(String telCode, String phone, String smsCode) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.UPDATE_PHONE.getCode(), Collections.singletonList(smsCode));
    }

    /**
     * 发送更改支付密码短信
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     */
    @Override
    public void sendChangePayPasswordSms(String telCode, String phone, String smsCode) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.UPDATE_PAY_PWD.getCode(), Collections.singletonList(smsCode));
    }

    /**
     * 发送删除会员账号短信
     *
     * @param telCode       手机号码前缀
     * @param phone         手机号码
     * @param smsCode       短信验证码
     */
    @Override
    public void sendMemberCancellationSms(String telCode, String phone, String smsCode) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.DELETE_MEMBER_ACCOUNT.getCode(), Collections.singletonList(smsCode));
    }

    /**
     * 发送会员注销成功短信 （仅通知）
     *
     * @param telCode 手机号码前缀
     * @param phone   手机号码
     */
    @Override
    public void sendMemberCancellationSuccessSms(String telCode, String phone) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.DELETE_MEMBER_ACCOUNT_SUCCESS.getCode(), Collections.emptyList());
    }

    /**
     * 发送会员注销失败短信
     *
     * @param telCode 手机号码前缀
     * @param phone   手机号码
     * @param reason  审核失败原因
     */
    @Override
    public void sendMemberCancellationFailSms(String telCode, String phone, String reason) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.DELETE_MEMBER_ACCOUNT_FAIL.getCode(), Collections.singletonList(reason));
    }

    /**
     * 发送手机绑定短信
     *
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param smsCode 短信验证码
     */
    @Override
    public void sendPhoneBindSms(String telCode, String phone, String smsCode) {
        sendSmsViaFeign(telCode, phone, SmsTemplateEnum.PHONE_BIND.getCode(), Collections.singletonList(smsCode));
    }

    /**
     * 发送短信
     *
     * @param telCode      手机号码前缀
     * @param phone        手机号码
     * @param templateCode 短信模板编码
     * @param paramList    参数集合
     */
    private void sendSmsViaFeign(String telCode, String phone, String templateCode, List<String> paramList) {
        SmsReq smsReq = new SmsReq();

        // 设置手机号
        List<SmsReq.PhoneReq> phonesList = new ArrayList<>();
        phonesList.add(new SmsReq.PhoneReq(telCode, phone));
        smsReq.setPhones(phonesList);

        // 设置短信类型（模板）
        smsReq.setCode(templateCode);

        // 设置参数
        smsReq.setTemplateParam(paramList);

        // 发送短信
        WrapperUtil.ofNullable(sendSmsFeign.sendSms(smsReq)).tryThrowWhenFail();
    }
}
