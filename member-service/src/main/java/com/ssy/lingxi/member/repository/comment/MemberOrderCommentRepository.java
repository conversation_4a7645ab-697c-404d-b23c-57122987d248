package com.ssy.lingxi.member.repository.comment;

import com.ssy.lingxi.member.entity.do_.comment.MemberOrderCommentDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员订单评价Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/3
 */
@Repository
public interface MemberOrderCommentRepository extends JpaRepository<MemberOrderCommentDO, Long>, JpaSpecificationExecutor<MemberOrderCommentDO> {

    List<MemberOrderCommentDO> findAllByBuyerCompleteCommentStatus(Integer completeCommentStatus);

    List<MemberOrderCommentDO> findAllByVendorCompleteCommentStatus(Integer completeCommentStatus);

    List<MemberOrderCommentDO> findByCompleteTimeLessThanAndBuyerCompleteCommentStatusNotAndVendorCompleteCommentStatusNot(LocalDateTime completeTime, Integer buyerCompleteCommentStatus, Integer vendorCompleteCommentStatus);

    @Modifying
    @Query("UPDATE MemberOrderCommentDO oc set oc.buyerCompleteCommentStatus = :status where oc.id = :id")
    int updateBuyerCompleteCommentStatus(@Param("status") Integer status, @Param("id") Long id);

    @Modifying
    @Query("UPDATE MemberOrderCommentDO oc set oc.vendorCompleteCommentStatus = :status where oc.id = :id")
    int updateVendorCompleteCommentStatus(@Param("status") Integer status, @Param("id") Long id);
}
