package com.ssy.lingxi.member.controller.web.customer;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.service.web.IMemberAppraisalService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 客户能力 - 会员考评
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/18
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/customer/appraisal")
public class CustomerAppraisalController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.CUSTOMER.getCode();

    @Resource
    private IMemberAppraisalService memberAppraisalService;

    /**
     * 状态下拉查询
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/statusList")
    public WrapperResp<List<StatusResp>> listMemberAppraisalStatus(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAppraisalService.listMemberAppraisalStatus(headers));
    }

    // ==================================会员考评查询===============================

    /**
     * 会员考评查询 - 会员考评分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/summary/page")
    public WrapperResp<PageDataResp<MemberAppraisalSubmitPageQueryResp>> pageSummaryMemberAppraisal(@RequestHeader HttpHeaders headers, @Valid MemberAppraisalSummaryPageDataReq queryVO) {
        return WrapperUtil.success(memberAppraisalService.pageSummaryMemberAppraisal(headers, queryVO, roleTag));
    }

    /**
     * 会员考评查询 - 会员考评分页列表(指定下级会员)
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/summary/appointPage")
    public WrapperResp<PageDataResp<MemberAppraisalSubmitPageQueryResp>> pageSummaryMemberAppraisal(@RequestHeader HttpHeaders headers, @Valid SubMemberIdRoleIdDataReq queryVO) {
        return WrapperUtil.success(memberAppraisalService.pageSummaryMemberAppraisal(headers, queryVO, roleTag));
    }

    /**
     * 会员考评查询 - 会员考评详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/summary/get")
    public WrapperResp<MemberAppraisalResultResp> getMemberAppraisalSummaryResult(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberAppraisalService.getMemberAppraisalResult(headers, idVO, roleTag));
    }

    // ==================================待新增考评单===============================

    /**
     * 待新增考评单 - 会员考评分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitPublish/page")
    public WrapperResp<PageDataResp<MemberAppraisalAddPageQueryResp>> pageWaitAddMemberAppraisal(@RequestHeader HttpHeaders headers, @Valid MemberAppraisalPageDataReq queryVO) {
        return WrapperUtil.success(memberAppraisalService.pageWaitAddMemberAppraisal(headers, queryVO, roleTag));
    }

    /**
     * 待新增考评单 - 会员考评详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitPublish/get")
    public WrapperResp<MemberAppraisalResp> getMemberAppraisal(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberAppraisalService.getMemberAppraisal(headers, idVO, roleTag));
    }

    /**
     * 待新增考评单 - 会员考评新增
     * @param headers Http头部信息
     * @param addVO 接口参数
     */
    @PostMapping("/waitPublish/add")
    public WrapperResp<Void> deleteMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAppraisalAddReq addVO) {
        memberAppraisalService.addMemberAppraisal(headers, addVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 待新增考评单 - 会员考评修改
     * @param headers Http头部信息
     * @param addVO 接口参数
     */
    @PostMapping("/waitPublish/update")
    public WrapperResp<Void> updateMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAppraisalUpdateReq addVO) {
        memberAppraisalService.updateMemberAppraisal(headers, addVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 待新增考评单 -会员考评删除
     * @param headers Http头部信息
     * @param idsVO 接口参数
     */
    @PostMapping("/delete")
    public WrapperResp<Void> deleteMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdListReq idsVO) {
        memberAppraisalService.deleteMemberAppraisal(headers, idsVO);
        return WrapperUtil.success();
    }

    // ==================================待考评打分===============================

    /**
     * 待考评打分 - 会员考评分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitGrade/page")
    public WrapperResp<PageDataResp<MemberAppraisalGradePageQueryResp>> pageWaitGradeMemberAppraisal(@RequestHeader HttpHeaders headers, MemberAppraisalPageDataReq queryVO) {
        return WrapperUtil.success(memberAppraisalService.pageWaitGradeMemberAppraisal(headers, queryVO, roleTag));
    }

    /**
     * 待考评打分 - 会员考评详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitGrade/get")
    public WrapperResp<MemberAppraisalResp> getWaitGradeMemberAppraisal(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberAppraisalService.getWaitGradeMemberAppraisal(headers, idVO, roleTag));
    }

    /**
     * 待考评打分 - 打分
     * @param headers Http头部信息
     * @param gradeVO 接口参数
     */
    @PostMapping("/waitGrade/grade")
    public WrapperResp<Void> gradeMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAppraisalGradeReq gradeVO) {
        memberAppraisalService.gradeMemberAppraisal(headers, gradeVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================待提交汇总考评结果===============================

    /**
     * 待提交汇总考评结果 - 会员考评分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitSubmit/page")
    public WrapperResp<PageDataResp<MemberAppraisalSubmitPageQueryResp>> pageWaitSubmitMemberAppraisal(@RequestHeader HttpHeaders headers, MemberAppraisalPageDataReq queryVO) {
        return WrapperUtil.success(memberAppraisalService.pageWaitSubmitMemberAppraisal(headers, queryVO, roleTag));
    }

    /**
     * 待提交汇总考评结果 - 会员考评详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitSubmit/get")
    public WrapperResp<MemberAppraisalResultResp> getMemberAppraisalSubmitResult(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberAppraisalService.getMemberAppraisalResult(headers, idVO, roleTag));
    }

    /**
     * 待提交汇总考评结果 - 提交
     * @param headers Http头部信息
     * @param submitVO 接口参数
     */
    @PostMapping("/waitSubmit/submit")
    public WrapperResp<Void> submitBatchMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAppraisalSubmitReq submitVO) {
        memberAppraisalService.submitMemberAppraisal(headers, submitVO, roleTag);
        return WrapperUtil.success();
    }


    // ==================================待审核考评结果一级===============================

    /**
     * 待审核考评结果一级 - 会员考评分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitAuditOne/page")
    public WrapperResp<PageDataResp<MemberAppraisalAuditPageQueryResp>> pageWaitAuditOneMemberAppraisal(@RequestHeader HttpHeaders headers, MemberAppraisalPageDataReq queryVO) {
        return WrapperUtil.success(memberAppraisalService.pageWaitAuditOneMemberAppraisal(headers, queryVO, roleTag));
    }

    /**
     * 待审核考评结果一级 - 会员考评详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitAuditOne/get")
    public WrapperResp<MemberAppraisalResultResp> getMemberAppraisalAuditOneResult(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberAppraisalService.getMemberAppraisalResult(headers, idVO, roleTag));
    }

    /**
     * 待审核考评结果一级 - 审核
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     */
    @PostMapping("/waitAuditOne/audit")
    public WrapperResp<Void> auditOneMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonAgreeReq agreeVO) {
        memberAppraisalService.auditOneMemberAppraisal(headers, agreeVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 待审核考评结果一级 - 批量审核
     * @param headers Http头部信息
     * @param idsVO 接口参数
     */
    @PostMapping("/waitAuditOne/auditBatch")
    public WrapperResp<Void> auditOneBatchMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdListReq idsVO) {
        memberAppraisalService.auditOneBatchMemberAppraisal(headers, idsVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================待审核考评结果二级===============================

    /**
     * 待审核考评结果二级 - 会员考评分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitAuditTwo/page")
    public WrapperResp<PageDataResp<MemberAppraisalAuditPageQueryResp>> pageWaitAuditTwoMemberAppraisal(@RequestHeader HttpHeaders headers, MemberAppraisalPageDataReq queryVO) {
        return WrapperUtil.success(memberAppraisalService.pageWaitAuditTwoMemberAppraisal(headers, queryVO, roleTag));
    }

    /**
     * 待审核考评结果二级 - 会员考评详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitAuditTwo/get")
    public WrapperResp<MemberAppraisalResultResp> getMemberAppraisalAuditTwoResult(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberAppraisalService.getMemberAppraisalResult(headers, idVO, roleTag));
    }

    /**
     * 待审核考评结果二级 - 审核
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     */
    @PostMapping("/waitAuditTwo/audit")
    public WrapperResp<Void> auditTwoMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonAgreeReq agreeVO) {
        memberAppraisalService.auditTwoMemberAppraisal(headers, agreeVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 待审核考评结果二级 - 批量审核
     * @param headers Http头部信息
     * @param idsVO 接口参数
     */
    @PostMapping("/waitAuditTwo/auditBatch")
    public WrapperResp<Void> auditTwoBatchMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdListReq idsVO) {
        memberAppraisalService.auditTwoBatchMemberAppraisal(headers, idsVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================待通报考评结果===============================

    /**
     * 待通报考评结果 - 会员考评分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitNotification/page")
    public WrapperResp<PageDataResp<MemberAppraisalNotificationPageQueryResp>> pageWaitNotificationMemberAppraisal(@RequestHeader HttpHeaders headers, MemberAppraisalPageDataReq queryVO) {
        return WrapperUtil.success(memberAppraisalService.pageWaitNotificationMemberAppraisal(headers, queryVO, roleTag));
    }

    /**
     * 待通报考评结果 - 会员考评详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitNotification/get")
    public WrapperResp<MemberAppraisalResultResp> getMemberAppraisalNotificationResult(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberAppraisalService.getMemberAppraisalResult(headers, idVO, roleTag));
    }

    /**
     * 待通报考评结果 - 通报
     * @param headers Http头部信息
     * @param idsVO 接口参数
     */
    @PostMapping("/waitNotification/notification")
    public WrapperResp<Void> notificationMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdListReq idsVO) {
        memberAppraisalService.notificationMemberAppraisal(headers, idsVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================考评结果查询===============================

    /**
     * 考评结果查询 - 会员考评分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/result/page")
    public WrapperResp<PageDataResp<MemberAppraisalSubResultPageQueryResp>> pageSubResultMemberAppraisal(@RequestHeader HttpHeaders headers, @Valid MemberAppraisalSubResultPageDataReq queryVO) {
        return WrapperUtil.success(memberAppraisalService.pageSubResultMemberAppraisal(headers, queryVO));
    }

    /**
     * 考评结果查询 - 会员考评详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/result/get")
    public WrapperResp<MemberAppraisalSubResultResp> getSubMemberAppraisalResult(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberAppraisalService.getSubMemberAppraisalResult(headers, idVO));
    }
}
