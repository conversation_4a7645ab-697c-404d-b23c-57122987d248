package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.api.model.resp.MemberLifecycleRuleQueryResp;
import com.ssy.lingxi.member.model.req.comment.MemberLifeCycleCheckOrderPermissionReq;
import com.ssy.lingxi.member.model.req.comment.MemberLifecycleRuleReq;
import com.ssy.lingxi.member.model.resp.comment.MemberLifecycleStagesResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员能力-系统管理-客户生命周期规则配置相关service
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 */
public interface IMemberLifecycleRuleService {

    /**
     * 保存指定会员、角色标签生命周期规则---生命周期阶段、生命周期阶段规则、入库审核后生命周期
     * @param headers
     * @param memberLifecycleRuleReq
     * @param roleTag
     * @return 执行结果
     */
    void addLifecycleRules(HttpHeaders headers, MemberLifecycleRuleReq memberLifecycleRuleReq, Integer roleTag);

    /**
     * 查询指定会员、角色标签的生命周期规则
     * @param headers
     * @param roleTag
     * @return 返回指定会员的生命周期规则
     */
    MemberLifecycleRuleQueryResp getLifecycleRules(HttpHeaders headers, Integer roleTag);

    /**
     * 当上级配置生命周期时，校验下级会员是否有下单权限
     *
     * @param loginCacheDTO 当前登录人
     * @param memberLifeCycleCheckOrderPermission 请求参数
     * @return 返回
     */
    Boolean checkSubMemberOrderPermission(UserLoginCacheDTO loginCacheDTO, MemberLifeCycleCheckOrderPermissionReq memberLifeCycleCheckOrderPermission);

    /**
     * 查询指定会员、指定角色标签的生命周期阶段
     * @param headers 请求头部信息
     * @param processType 下级会员角色标签
     * @return 返回指定会员的生命周期阶段
     */
    List<MemberLifecycleStagesResp> getLifecycleStages(HttpHeaders headers, Integer processType);
}
