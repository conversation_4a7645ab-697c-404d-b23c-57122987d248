package com.ssy.lingxi.member.model.req.lifecycle;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 会员考察新增VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberInspectUpdateReq extends MemberInspectAddReq implements Serializable {
    private static final long serialVersionUID = -5352390411750129021L;

    /**
     * 数据id
     */
    @NotNull(message = "数据Id要大于0")
    @Positive(message = "数据Id要大于0")
    private Long id;
}
