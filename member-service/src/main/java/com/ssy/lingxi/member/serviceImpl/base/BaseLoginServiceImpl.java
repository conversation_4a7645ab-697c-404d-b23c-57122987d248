package com.ssy.lingxi.member.serviceImpl.base;

import com.google.common.collect.Lists;
import com.ssy.lingxi.commodity.api.feign.ICountryAreaFeign;
import com.ssy.lingxi.commodity.api.model.resp.support.CountryAreaResp;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.member.TokenStrategyEnum;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.config.SmsConfig;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.TokenContext;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.PasswordUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.rest.model.req.wx.UserPhoneReq;
import com.ssy.lingxi.component.rest.model.resp.wx.PhoneNumberResp;
import com.ssy.lingxi.component.rest.service.WxApiService;
import com.ssy.lingxi.member.config.MemberRefreshConfig;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.entity.bo.login.LoginContext;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.enums.CorporationCertificationStatusEnum;
import com.ssy.lingxi.member.enums.LoginStrategyEnum;
import com.ssy.lingxi.member.enums.MemberOuterStatusEnum;
import com.ssy.lingxi.member.handler.listener.event.RedisKeyRemoveEvent;
import com.ssy.lingxi.member.model.req.login.EmailLoginSmsCode;
import com.ssy.lingxi.member.model.req.login.MultiAccCheckReq;
import com.ssy.lingxi.member.model.req.login.PhoneBindSmsCodeReq;
import com.ssy.lingxi.member.model.req.login.PhoneLoginSmsCode;
import com.ssy.lingxi.member.model.resp.branch.MemberBranchResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseLoginService;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.feign.ISmsFeignService;
import com.ssy.lingxi.member.util.MailUtil;
import com.ssy.lingxi.member.util.MemberOrganizationUtil;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 登陆通用层
 * 没有使用复杂的设计模式，没有使用beanCopy，都是为了方便二开改造（如果有好的建议，可以提回标品）
 * 登陆上下文主要分平台后台和能力中心，pc和mobile登陆上下文逻辑一致
 * 平台后台很少修改，二开基本都只改能力中心，为了好改，所以没有把两份合并
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/6
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class BaseLoginServiceImpl implements IBaseLoginService {
    private final JdbcTemplate jdbcTemplate;
    private final IRedisUtils redisUtils;
    private final SmsConfig smsConfig;
    private final MemberRefreshConfig memberRefreshConfig;
    private final ISmsFeignService smsFeignService;
    private final ICountryAreaFeign countryAreaFeign;
    private final UserRepository userRepository;
    private final IBaseMemberCacheService memberCacheService;
    private final ApplicationEventPublisher eventPublisher;
    private final WxApiService wxApiService;
    private final BaiTaiMemberProperties baiTaiMemberProperties;
    private final MemberRelationRepository relationRepository;

    @Override
    public void checkLoginFailures(String loginFailuresKey) {
        String failures = redisUtils.stringGet(loginFailuresKey, RedisConstant.REDIS_USER_INDEX);
        if (failures != null && Integer.parseInt(failures) >= memberRefreshConfig.getUserLoginMaxFailedTimes()) {
            throw new BusinessException(ResponseCodeEnum.LOGIN_FAILED_TIMES_EXCEEDED_MAXIMUM.getCode(),
                    ResponseCodeEnum.LOGIN_FAILED_TIMES_EXCEEDED_MAXIMUM.getMessage() + ",请" + memberRefreshConfig.getUserLimitLoginMinutes() + "分钟后再试");
        }
    }

    @Override
    public void checkLoginFailuresAndPwd(Long userId, String dbPwd, String loginPwd) {
        // 判断登录失败次数是否超过限制
        String loginFailuresKey = MemberRedisConstant.LOGIN_FAILURES_REDIS_KEY_PREFIX + userId;
        checkLoginFailures(loginFailuresKey);

        // 校验密码
        if (!PasswordUtil.checkPassword(dbPwd, loginPwd)) {
            redisUtils.stringIncrement(loginFailuresKey, memberRefreshConfig.getUserLimitLoginMinutes() * 60L, RedisConstant.REDIS_USER_INDEX);
            throw new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);
        }
    }

    @Override
    public void checkLoginFailuresAndSmsCode(Long userId, String smsCode, String smsCodeReq) {
        // 判断登录失败次数是否超过限制
        String loginFailuresKey = MemberRedisConstant.LOGIN_FAILURES_REDIS_KEY_PREFIX + userId;
        checkLoginFailures(loginFailuresKey);

        if (memberRefreshConfig.getCheckSmsCode()) {
            // 校验短信验证码
            if (!StringUtils.hasLength(smsCode)) {
                redisUtils.stringIncrement(loginFailuresKey, memberRefreshConfig.getUserLimitLoginMinutes() * 60L, RedisConstant.REDIS_USER_INDEX);
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!Objects.equals(smsCode, smsCodeReq)) {
                redisUtils.stringIncrement(loginFailuresKey, memberRefreshConfig.getUserLimitLoginMinutes() * 60L, RedisConstant.REDIS_USER_INDEX);
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }
    }

    @Override
    public List<UserDO> checkLoginFailures(List<UserDO> userDOList, List<String> loginFailuresKeyList) {
        // 判断登录失败次数是否超过限制
        Map<String, String> failuresMap = redisUtils.stringMulGet(loginFailuresKeyList, String.class, RedisConstant.REDIS_USER_INDEX);

        // 筛选出登陆失败次数未超过限制的用户
        userDOList = userDOList.stream()
                .filter(userDO -> {
                    String failures = failuresMap.get(MemberRedisConstant.LOGIN_FAILURES_REDIS_KEY_PREFIX + userDO.getId());
                    return failures == null || Integer.parseInt(failures) < memberRefreshConfig.getUserLoginMaxFailedTimes();
                }).collect(Collectors.toList());

        // 如果没有可用账号，说明被限制登录了
        if (CollectionUtils.isEmpty(userDOList)) {
            throw new BusinessException(ResponseCodeEnum.LOGIN_FAILED_TIMES_EXCEEDED_MAXIMUM.getCode(),
                    ResponseCodeEnum.LOGIN_FAILED_TIMES_EXCEEDED_MAXIMUM.getMessage() + ",请" + memberRefreshConfig.getUserLimitLoginMinutes() + "分钟后再试");
        }

        return userDOList;
    }

    @Override
    public List<UserDO> checkLoginFailuresAndPwd(List<UserDO> userDOList, MultiAccCheckReq multiAccCheckReq) {
        // 判断登录失败次数是否超过限制
        List<String> loginFailuresKeyList = userDOList.stream().map(UserDO::getId).map(userId -> MemberRedisConstant.LOGIN_FAILURES_REDIS_KEY_PREFIX + userId).collect(Collectors.toList());
        userDOList = checkLoginFailures(userDOList, loginFailuresKeyList);

        // 校验密码
        userDOList = userDOList.stream().filter(userDO -> PasswordUtil.checkPassword(userDO.getPassword(), multiAccCheckReq.getPassword())).collect(Collectors.toList());

        // 如果没有可用账号，说明账号密码全部不对
        if (CollectionUtils.isEmpty(userDOList)) {
            for (String loginFailuresKey : loginFailuresKeyList) {
                redisUtils.stringIncrement(loginFailuresKey, memberRefreshConfig.getUserLimitLoginMinutes() * 60L, RedisConstant.REDIS_USER_INDEX);
            }
            throw new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);
        }

        return userDOList;
    }

    @Override
    public List<UserDO> checkLoginFailuresAndSmsCode(List<UserDO> userDOList, MultiAccCheckReq multiAccCheckReq) {
        // 判断登录失败次数是否超过限制
        List<String> loginFailuresKeyList = userDOList.stream().map(UserDO::getId).map(userId -> MemberRedisConstant.LOGIN_FAILURES_REDIS_KEY_PREFIX + userId).collect(Collectors.toList());
        userDOList = checkLoginFailures(userDOList, loginFailuresKeyList);

        // 获取缓存的验证码
        String smsRedisKey = LoginStrategyEnum.getRedisKeyPrefix(multiAccCheckReq) + LoginStrategyEnum.getPhoneOrEmail(multiAccCheckReq);
        String smsCode = memberCacheService.getString(smsRedisKey);

        // 校验验证码
        if (memberRefreshConfig.getCheckSmsCode()) {
            // 校验验证码
            if (!StringUtils.hasLength(smsCode)) {
                for (String loginFailuresKey : loginFailuresKeyList) {
                    redisUtils.stringIncrement(loginFailuresKey, memberRefreshConfig.getUserLimitLoginMinutes() * 60L, RedisConstant.REDIS_USER_INDEX);
                }
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            if (!Objects.equals(smsCode, LoginStrategyEnum.getSmsCode(multiAccCheckReq))) {
                for (String loginFailuresKey : loginFailuresKeyList) {
                    redisUtils.stringIncrement(loginFailuresKey, memberRefreshConfig.getUserLimitLoginMinutes() * 60L, RedisConstant.REDIS_USER_INDEX);
                }
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_INCORRECT);
            }
        }

        return userDOList;
    }

    @Override
    public List<UserDO> getAllUserDOByLoginStrategy(MultiAccCheckReq multiAccCheckReq) {
        String mainField;
        if (LoginStrategyEnum.isPasswordLogin(multiAccCheckReq.getLoginType())) {
            // 账户密码登陆
            mainField = multiAccCheckReq.getAccount();
        } else if (LoginStrategyEnum.isPhoneLogin(multiAccCheckReq.getLoginType())) {
            // 手机验证码登陆
            mainField = multiAccCheckReq.getPhone();
        } else if (LoginStrategyEnum.isEmailLogin(multiAccCheckReq.getLoginType())) {
            // 邮箱验证码登陆
            mainField = multiAccCheckReq.getEmail();
        } else if (Objects.equals(multiAccCheckReq.getLoginType(), LoginStrategyEnum.MOBILE_WX.getCode())) {
            // 微信快速登陆
            BusinessAssertUtil.notNull(multiAccCheckReq.getWechatPhoneCode());
            mainField = multiAccCheckReq.getWechatPhoneCode();
        }  else {
            throw new BusinessException(ResponseCodeEnum.UNSUPPORTED_LOGIN_STRATEGY);
        }

        // 查找所有用户信息
        return getAllUserDOByLoginStrategy(mainField, multiAccCheckReq.getLoginType());
    }

    @Override
    public List<UserDO> getAllUserDOByLoginStrategy(String mainField, Integer loginStrategy) {
        // 根据登陆方式查询对应账号
        List<UserDO> userDOList;
        if (LoginStrategyEnum.isPasswordLogin(loginStrategy)) {
            // 账户密码登陆
            userDOList = userRepository.findAllLoginUser(mainField, mainField, MemberRelationTypeEnum.OTHER.getCode());
        } else if (LoginStrategyEnum.isPhoneLogin(loginStrategy)) {
            // 手机验证码登陆
            userDOList = userRepository.findAllByPhoneAndRelType(mainField, MemberRelationTypeEnum.OTHER.getCode());
        } else if (LoginStrategyEnum.isEmailLogin(loginStrategy)) {
            // 邮箱验证码登陆
            userDOList = userRepository.findAllByEmailAndRelType(mainField, MemberRelationTypeEnum.OTHER.getCode());
        } else if (Objects.equals(loginStrategy, LoginStrategyEnum.MOBILE_WX.getCode())) {
            // 微信快速登陆
            PhoneNumberResp userPhoneNumber = wxApiService.getUserPhoneNumber(new UserPhoneReq(mainField));
            log.info("多主体查询缓存：{} ======== {}", String.format(MemberRedisConstant.WX_LOGIN_PHONE_CODE_PREFIX, mainField), userPhoneNumber.getPhoneInfo().getPhoneNumber());
            redisUtils.stringSet(String.format(MemberRedisConstant.WX_LOGIN_PHONE_CODE_PREFIX, mainField), userPhoneNumber.getPhoneInfo().getPhoneNumber(), 5L * 60 - 10, RedisConstant.REDIS_USER_INDEX);
            userDOList = userRepository.findAllByPhoneAndRelType(userPhoneNumber.getPhoneInfo().getPhoneNumber(), MemberRelationTypeEnum.OTHER.getCode());
        }  else {
            throw new BusinessException(ResponseCodeEnum.UNSUPPORTED_LOGIN_STRATEGY);
        }
        return userDOList;
    }

    @Override
    public UserDO getSpecifyUserDOIfNecessary(String mainField, Long userId, Integer loginStrategy) {
        UserDO userDO = null;

        // 查找所有用户信息
        List<UserDO> userDOList = getAllUserDOByLoginStrategy(mainField, loginStrategy);

        // 如果有指定用户，或者是多主体的场景，则要筛选用户
        if (Objects.nonNull(userId) || userDOList.size() > 1) {
            userDO = userDOList.stream().filter(user -> user.getId().equals(userId)).findAny().orElseThrow(() -> new BusinessException(ResponseCodeEnum.MULTI_ACCOUNT_SPECIFIED_NOT_FOUND));
        } else if (userDOList.size() == 1) {
            // 如果没有指定用户，且只找到一个，则是常规场景，取当前获取到的用户
            userDO = userDOList.get(0);
        }

        return userDO;
    }

    @Override
    public Integer getUpdatePwdIntervalDays(LocalDateTime lastModifyPwdTime) {
        // 有配置要更新密码，需要计算更新密码时间
        // 最后更新密码时间为空或者已经达到更新密码的天数阈值，则需要更新
        int intervalDays = memberRefreshConfig.getUpdatePwdIntervalDays();
        int updatePwdIntervalDays = -1;
        if (!NumberUtil.isNullOrLteZero(intervalDays)
                && (Objects.isNull(lastModifyPwdTime) || lastModifyPwdTime.plusDays(intervalDays).isBefore(LocalDateTime.now()))) {
            updatePwdIntervalDays = intervalDays;
        }
        return updatePwdIntervalDays;
    }

    @Async
    @Override
    public void updateUserLastLoginTimeAsync(Long userId) {
        String sql = String.format("update mem_user set last_login_time = '%s' where id = %d", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), userId);
        jdbcTemplate.update(sql);
    }

    @Override
    public Specification<MemberRelationDO> getMobileRelationSpec(MemberDO memberDO, Boolean isSalesman) {
        // 登录会员下级会员的关系列表
        return (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(cb.equal(root.get("subMemberId").as(Long.class), memberDO.getId()));
            list.add(cb.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            Join<MemberRelationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.INNER);
            list.add(cb.equal(subRoleJoin.get("status").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
            if (Objects.nonNull(isSalesman)) {
                list.add(cb.equal(subRoleJoin.get("roleType").as(Integer.class), isSalesman ? RoleTypeEnum.SERVICE_PROVIDER.getCode() : RoleTypeEnum.SERVICE_CONSUMER.getCode()));
            }

            return cb.and(list.toArray(new Predicate[0]));
        };
    }

    @Override
    public void sendPhoneLoginSmsCode(PhoneLoginSmsCode phoneReq, UserDO userDO, String cacheKey, String templateCode) {
        // 如果验证码已经发送，则不再发送
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 如果没有设置手机号，或设置的手机号和入参手机不一致，则报错
        BusinessAssertUtil.isTrue(StringUtils.hasLength(userDO.getPhone()) && userDO.getPhone().equals(phoneReq.getPhone()),
                ResponseCodeEnum.LS_DATA_PHONE_VALIDATE);

        // 校验国家手机号代码
        CountryAreaResp countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(phoneReq.getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);

        // 校验手机号格式
        if (!StringUtils.hasLength(phoneReq.getPhone()) || !SecurityStringUtil.checkPhone(countryAreaResp.getTelCode(), phoneReq.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PHONE_FORMAT_INCORRECT);
        }

        // 先发送短信验证码验证码，成功后再存缓存
        String smsCode = SecurityStringUtil.getRandomSmsCode();
        smsFeignService.sendPhoneLoginSms(phoneReq.getTelCode(), phoneReq.getPhone(), smsCode, templateCode);
        redisUtils.stringSet(cacheKey, smsCode, smsConfig.getExpireSeconds(), RedisConstant.REDIS_USER_INDEX);
        redisUtils.stringSet(smsCodeSecurityKey, "", smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    @Override
    public void sendEmailLoginSmsCode(EmailLoginSmsCode emailReq, UserDO userDO, String cacheKey) {
        // 如果超管没有设置邮箱则报错
        // 超管设置的邮箱和入参邮箱不一致，则报错
        BusinessAssertUtil.isTrue(StringUtils.hasLength(userDO.getEmail()) && userDO.getEmail().equals(emailReq.getEmail()),
                ResponseCodeEnum.MC_MS_MANAGE_EMAIL_ERROR);

        // 发送邮箱验证码
        String smsCode = SecurityStringUtil.getRandomSmsCode();
        redisUtils.stringSet(cacheKey, smsCode, memberRefreshConfig.getMailCodeCachedTimeSeconds(), RedisConstant.REDIS_USER_INDEX);

        String title = MemberStringEnum.DYNAMIC_EMAIL_CODE_TITLE.getName();
        String content = MemberStringEnum.DYNAMIC_EMAIL_CODE_TEMPLATE.getName();

        MailUtil.sendTextMail(title, String.format(content, smsCode), userDO.getEmail());


    }

    @Override
    public void adminAccountSecurityCheck(UserDO userDO, String phoneSmsCode, String emailSmsCode, String phonePrefix, String emailPrefix) {
        // 启用手机号验证码校验且是超管用户 则需要校验验证码
        if (memberRefreshConfig.getCheckSmsCode() && UserTypeEnum.ADMIN.getCode().equals(userDO.getUserType())) {
            boolean checkSmsCode = false;
            String cacheKey = null;

            if (StringUtils.hasLength(phoneSmsCode)) {
                cacheKey = phonePrefix + userDO.getPhone();
                checkSmsCode = Objects.equals(redisUtils.stringGet(cacheKey, RedisConstant.REDIS_USER_INDEX), phoneSmsCode);
            } else if (StringUtils.hasLength(emailSmsCode)) {
                cacheKey = emailPrefix + userDO.getPhone();
                checkSmsCode = Objects.equals(redisUtils.stringGet(cacheKey, RedisConstant.REDIS_USER_INDEX), emailSmsCode);
            }

            // 如果校验失败，则登录失败次数+1
            if (!checkSmsCode) {
                redisUtils.stringIncrement(MemberRedisConstant.LOGIN_FAILURES_REDIS_KEY_PREFIX + userDO.getId(), memberRefreshConfig.getUserLimitLoginMinutes() * 60L, RedisConstant.REDIS_USER_INDEX);
                throw new BusinessException(ResponseCodeEnum.USER_SMSCODE_EXPIRED);
            }

            // 删除使用过的验证码
            if (StringUtils.hasLength(cacheKey)) {
                eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Collections.singletonList(cacheKey)));
            }
        }
    }

    /**
     * 手机号绑定发送短信验证码
     */
    @Override
    public void sendPhoneBindSmsCode(PhoneBindSmsCodeReq req, UserDO userDO, String cacheKey) {
        // 如果验证码已经  发送，则不再发送
        String smsCodeSecurityKey = SecurityStringUtil.getSmsCodeSecurityKey(cacheKey);
        BusinessAssertUtil.isFalse(redisUtils.keyExists(smsCodeSecurityKey, RedisConstant.REDIS_USER_INDEX), ResponseCodeEnum.SMS_CODE_ALREADY_EXISTS);

        // 校验国家手机号代码
        CountryAreaResp countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(req.getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);

        // 校验手机号格式
        if (!StringUtils.hasLength(req.getPhone()) || !SecurityStringUtil.checkPhone(countryAreaResp.getTelCode(), req.getPhone())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PHONE_FORMAT_INCORRECT);
        }

        // 先发送短信验证码验证码，成功后再存缓存
        String smsCode = SecurityStringUtil.getRandomSmsCode();

        log.info("发送手机号绑定短信：{}", smsCode);

        // TODO 暂时注释，模板未申请下来
//        smsFeignService.sendPhoneBindSms(req.getTelCode(), req.getPhone(), smsCode);
        redisUtils.stringSet(cacheKey, "123456", smsConfig.getExpireSeconds(), RedisConstant.REDIS_USER_INDEX);
        redisUtils.stringSet(smsCodeSecurityKey, userDO.getId(), smsConfig.getIntervalSeconds(), RedisConstant.REDIS_USER_INDEX);
    }

    @Override
    public LoginContext getLoginContext(List<MemberRelationDO> availableRelDOList, MemberRelationDO relDO, MemberDO memberDO, UserDO userDO, TokenContext tokenContext) {
        // 获取会员角色
        MemberRoleDO memberRoleDO = relDO.getSubRole();

        // 获取可用的用户角色
        List<UserRoleDO> availableUserRoleList = getUserRoleDOList(userDO);

        // 封装登陆上下文
        LoginContext loginContext = new LoginContext();

        //************************************** 会员相关信息 **************************************
        loginContext.setMemberId(memberDO.getId());
        loginContext.setMemberName(SecurityStringUtil.getOrDefault(memberDO.getName()));
        loginContext.setMemberRoleId(memberRoleDO.getId());
        loginContext.setRoles(getMemberRoleItems(availableRelDOList));
        loginContext.setMemberRoleName(SecurityStringUtil.getOrDefault(memberRoleDO.getRoleName()));
        loginContext.setMemberRoleType(memberRoleDO.getRoleType());
        loginContext.setMemberType(memberRoleDO.getMemberType());
        loginContext.setRoleTag(memberRoleDO.getRoleTag());
        loginContext.setMemberLevelType(MemberLevelTypeEnum.MERCHANT.getCode());
        loginContext.setLevel(relDO.getLevelRight().getLevel());
        loginContext.setLevelTag(relDO.getLevelRight().getLevelTag());
        loginContext.setScore(relDO.getLevelRight().getScore());
        loginContext.setCreditPoint(relDO.getCredit().getCreditPoint());
        loginContext.setVerified(relDO.getVerified());
        loginContext.setValidateStatus(relDO.getOuterStatus());
        loginContext.setValidateStatusDesc(MemberOuterStatusEnum.getCodeMsg(relDO.getOuterStatus()));
        loginContext.setValidateMsg(SecurityStringUtil.getOrDefault(relDO.getValidateMsg()));
        loginContext.setOuterStatus(relDO.getOuterStatus());
        loginContext.setRelId(relDO.getId());

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), memberDO.getId(), baiTaiMemberProperties.getCustomerRoleId());

        if (Objects.isNull(relationDO)) {
            loginContext.setCorporationCertificationStatus(CorporationCertificationStatusEnum.UN_CERTIFICATION.getCode());
        } else if (Objects.equals(relationDO.getVerified(), CommonBooleanEnum.YES.getCode())) {
            loginContext.setCorporationCertificationStatus(CorporationCertificationStatusEnum.CERTIFICATION_SUCCESS.getCode());
        } else if (Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED.getCode())) {
            loginContext.setCorporationCertificationStatus(CorporationCertificationStatusEnum.CERTIFICATION_NOT_PASSED.getCode());
        } else {
            loginContext.setCorporationCertificationStatus(CorporationCertificationStatusEnum.CERTIFICATION_PENDING.getCode());
        }

        //************************************** 用户相关信息 **************************************
        loginContext.setUserId(userDO.getId());
        loginContext.setAccount(SecurityStringUtil.getOrDefault(userDO.getAccount()));
        loginContext.setUserName(SecurityStringUtil.getOrDefault(userDO.getName()));
        loginContext.setIdCardNo(SecurityStringUtil.getOrDefault(userDO.getIdCardNo()));
        loginContext.setTelCode(userDO.getTelCode());
        loginContext.setPhone(SecurityStringUtil.getOrDefault(userDO.getPhone()));
        loginContext.setEmail(SecurityStringUtil.getOrDefault(userDO.getEmail()));
        loginContext.setJobTitle(SecurityStringUtil.getOrDefault(userDO.getJobTitle()));
        loginContext.setOrgId(safeGetOrgId(userDO));
        loginContext.setOrgName(MemberOrganizationUtil.joinTitleToString(safeGetOrgId(userDO), new ArrayList<>(memberDO.getOrgs())));
        loginContext.setUserRoleIds(availableUserRoleList.stream().map(UserRoleDO::getId).collect(Collectors.toList()));
        loginContext.setUserRoleName(availableUserRoleList.stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(",")));
        loginContext.setUserType(userDO.getUserType());
        loginContext.setLogo(SecurityStringUtil.getOrDefault(userDO.getLogo()));
        loginContext.setUpdatePwdIntervalDays(getUpdatePwdIntervalDays(userDO.getLastModifyPwdTime()));
        loginContext.setImFlag(userDO.getRoles().stream().map(UserRoleDO::getHasImAuth).anyMatch(imAuth -> EnableDisableStatusEnum.ENABLE.getCode().equals(imAuth)));
        loginContext.setBranches(Optional.ofNullable(userDO.getBranches()).map(v -> v.stream().map(branch -> new MemberBranchResp(branch.getId(), branch.getName())).collect(Collectors.toList())).orElse(Lists.newArrayList()));

        //************************************** 通用信息 **************************************
        tokenContext.setRoleId(memberRoleDO.getId());// 确定了登录角色以后，回写到tokenContext中
        loginContext.setTokenContext(tokenContext);
        loginContext.setSupplier(memberRoleDO.getId().equals(baiTaiMemberProperties.getVendorRoleId()));
        loginContext.setSelfMemberId(baiTaiMemberProperties.getSelfMemberId());
        loginContext.setIndividualRoleId(baiTaiMemberProperties.getIndividualRoleId());
        loginContext.setSelfRoleId(baiTaiMemberProperties.getSelfRoleId());
        loginContext.setSelfBuyerRoleId(baiTaiMemberProperties.getCustomerRoleId());
        loginContext.setVendorRoleId(baiTaiMemberProperties.getVendorRoleId());
        loginContext.setCustomerRoleId(baiTaiMemberProperties.getCustomerRoleId());

        return loginContext;
    }

    @Override
    public LoginContext getManageLoginContext(MemberDO memberDO, MemberRoleDO memberRoleDO, UserDO userDO) {
        // 获取可用的用户角色
        List<UserRoleDO> availableUserRoleList = getUserRoleDOList(userDO);

        // 封装平台后台登陆上下文
        LoginContext loginContext = new LoginContext();

        //************************************** 会员相关信息 **************************************
        loginContext.setMemberId(memberDO.getId());
        loginContext.setMemberName(SecurityStringUtil.getOrDefault(memberDO.getName()));
        loginContext.setMemberRoleId(memberRoleDO.getId());
        loginContext.setRoles(new ArrayList<>());
        loginContext.setMemberRoleName(memberRoleDO.getRoleName());
        loginContext.setMemberRoleType(memberRoleDO.getRoleType());
        loginContext.setMemberType(memberRoleDO.getMemberType());
        loginContext.setRoleTag(memberRoleDO.getRoleTag());
        loginContext.setMemberLevelType(MemberLevelTypeEnum.PLATFORM.getCode());
        loginContext.setImFlag(userDO.getRoles().stream().map(UserRoleDO::getHasImAuth).anyMatch(imAuth -> EnableDisableStatusEnum.ENABLE.getCode().equals(imAuth)));
        //************************************** 用户相关信息 **************************************
        loginContext.setUserId(userDO.getId());
        loginContext.setAccount(userDO.getAccount());
        loginContext.setUserName(userDO.getName());
        loginContext.setTelCode(userDO.getTelCode());
        loginContext.setPhone(userDO.getPhone());
        loginContext.setEmail(userDO.getEmail());
        loginContext.setJobTitle(userDO.getJobTitle());
        loginContext.setOrgId(safeGetOrgId(userDO));
        loginContext.setOrgName(MemberOrganizationUtil.joinTitleToString(safeGetOrgId(userDO), new ArrayList<>(memberDO.getOrgs())));
        loginContext.setUserRoleIds(availableUserRoleList.stream().map(UserRoleDO::getId).collect(Collectors.toList()));
        loginContext.setUserRoleName(availableUserRoleList.stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(",")));
        loginContext.setUserType(userDO.getUserType());
        loginContext.setLogo(userDO.getLogo());
        loginContext.setUpdatePwdIntervalDays(getUpdatePwdIntervalDays(userDO.getLastModifyPwdTime()));
        loginContext.setImFlag(userDO.getRoles().stream().map(UserRoleDO::getHasImAuth).anyMatch(imAuth -> EnableDisableStatusEnum.ENABLE.getCode().equals(imAuth)));

        //************************************** 通用信息 **************************************
        loginContext.setTokenContext(new TokenContext(memberDO.getId(), memberRoleDO.getId(), userDO.getId(), SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM, TokenStrategyEnum.LOGIN));

        return loginContext;
    }

    @Override
    public LoginContext baseLogin(List<MemberRelationDO> allPlatformRelDOList, MemberDO memberDO, UserDO userDO, Long specifyMemberRoleId, TokenContext tokenContext) {
        // 如果该账号存在注销审核状态，则提示【该账号已提交注销申请，待运营方审核中，暂时无法登录，如有疑问请联系客服处理】
        BusinessAssertUtil.isFalse(allPlatformRelDOList.stream().anyMatch(relDO -> Objects.equals(MemberStatusEnum.WAIT_CANCELLATION.getCode(), relDO.getStatus()))
                , ResponseCodeEnum.MC_MS_PENDING_CANCELLATION_AUDIT);

        // 筛选可用的平台关系，如果全是不可用的，则说明该账号被冻结了
        // 审核通过的平台关系优先排在前面，同审核情况下，按id升序
        List<MemberRelationDO> availableRelDOList = allPlatformRelDOList.stream()
                .filter(relationDO -> MemberStatusEnum.NORMAL.getCode().equals(relationDO.getStatus()))
                .filter(relationDO -> EnableDisableStatusEnum.ENABLE.getCode().equals(relationDO.getSubRole().getStatus()))
                .sorted(Comparator.comparingInt(MemberRelationDO::getVerified).reversed().thenComparingLong(MemberRelationDO::getSubRoleId))
                .collect(Collectors.toList());
        BusinessAssertUtil.notEmpty(availableRelDOList, ResponseCodeEnum.MC_MS_MEMBER_HAS_BEEN_FROZEN);

        // 检查企业认证状态，如果企业认证通过，则屏蔽个人采购商身份，优先选择企业采购商
        availableRelDOList = filterRelationsByCorpCertification(availableRelDOList, memberDO);

        // 如果有指定使用会员角色，则用指定的，如果没有指定，则取首个
        MemberRelationDO loginPlatformRelationDO = getLoginPlatformRelationDO(specifyMemberRoleId, allPlatformRelDOList, availableRelDOList);

        // 如果当前选用的平台关系还没审核通过1次，则只能登录pc端
        securityCheckForNonPcSource(loginPlatformRelationDO, tokenContext.getLoginSourceEnum());

        // 获取通用登陆上下文
        return getLoginContext(availableRelDOList, loginPlatformRelationDO, memberDO, userDO, tokenContext);
    }

    private static List<UserRoleDO> getUserRoleDOList(UserDO userDO) {
        return userDO.getRoles().stream()
                .filter(r -> EnableDisableStatusEnum.ENABLE.getCode().equals(r.getStatus()))
                .sorted(Comparator.comparing(UserRoleDO::getId))
                .collect(Collectors.toList());
    }

    private static MemberRelationDO getLoginPlatformRelationDO(Long specifyMemberRoleId, List<MemberRelationDO> allPlatformRelDOList, List<MemberRelationDO> availableRelDOList) {
        if (Objects.nonNull(specifyMemberRoleId)) {
            MemberRelationDO loginPlatformRelationDO = allPlatformRelDOList.stream()
                    .filter(relDO -> Objects.equals(specifyMemberRoleId, relDO.getSubRoleId()))
                    .findAny()
                    .orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));

            // 校验该平台关系是否可用
            BusinessAssertUtil.isTrue(MemberStatusEnum.NORMAL.getCode().equals(loginPlatformRelationDO.getStatus()), ResponseCodeEnum.MC_MS_MEMBER_HAS_BEEN_FROZEN);

            // 如果该角色已经停用，则不允许登录
            BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(loginPlatformRelationDO.getSubRole().getStatus()), ResponseCodeEnum.MC_MS_ROLE_HAS_BEEN_DISABLED);

            return loginPlatformRelationDO;
        }
        return availableRelDOList.get(0);
    }

    public static Long safeGetOrgId(UserDO userDO) {
        return Optional.ofNullable(userDO.getOrg())
                .map(MemberOrganizationDO::getId)
                .orElse(0L);
    }

    /**
     * 根据企业认证状态过滤会员关系列表
     * 未通过企业认证时只能有个人采购商角色，企业认证通过后只能有企业采购商角色
     *
     * @param availableRelDOList 可用的会员关系列表
     * @param memberDO 会员信息
     * @return 过滤后的会员关系列表
     */
    private List<MemberRelationDO> filterRelationsByCorpCertification(List<MemberRelationDO> availableRelDOList, MemberDO memberDO) {
        // 查询企业认证状态
        MemberRelationDO corporationRelation = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(
                baiTaiMemberProperties.getSelfMemberId(),
                baiTaiMemberProperties.getSelfRoleId(),
                memberDO.getId(),
                baiTaiMemberProperties.getCustomerRoleId());

        // 判断是否企业认证通过
        boolean isCorporationCertified = Objects.nonNull(corporationRelation)
                && Objects.equals(corporationRelation.getVerified(), CommonBooleanEnum.YES.getCode());

        if (isCorporationCertified) {
            // 企业认证通过，只允许企业会员类型的角色，屏蔽个人会员类型
            List<MemberRelationDO> enterpriseRelations = availableRelDOList.stream()
                    .filter(relationDO -> MemberTypeEnum.MERCHANT.getCode().equals(relationDO.getSubRole().getMemberType()))
                    .collect(Collectors.toList());

            // 如果有企业会员角色，则只返回企业会员角色；否则返回原列表
            return enterpriseRelations.isEmpty() ? availableRelDOList : enterpriseRelations;
        } else {
            // 企业认证未通过或未认证，只允许个人会员类型的角色
            List<MemberRelationDO> personalRelations = availableRelDOList.stream()
                    .filter(relationDO -> MemberTypeEnum.MERCHANT_PERSONAL.getCode().equals(relationDO.getSubRole().getMemberType()))
                    .collect(Collectors.toList());

            // 如果有个人会员角色，则只返回个人会员角色；否则返回原列表
            return personalRelations.isEmpty() ? availableRelDOList : personalRelations;
        }
    }

    private static List<MemberRoleItem> getMemberRoleItems(List<MemberRelationDO> availableRelDOList) {
        // 平台关系是未冻结 且 会员角色状态为启用
        return availableRelDOList.stream()
                .map(MemberRelationDO::getSubRole)
                .filter(memberRoleDO -> EnableDisableStatusEnum.ENABLE.getCode().equals(memberRoleDO.getStatus()))
                .map(r -> new MemberRoleItem(r.getId(), r.getRoleName(), r.getRoleType()))
                .collect(Collectors.toList());
    }

    /**
     * 如果当前选用的平台关系还没审核通过1次，则只能登录pc端（需要具体报错）
     */
    private static void securityCheckForNonPcSource(MemberRelationDO loginPlatformRelationDO, SystemSourceEnum loginSource) {
        if (!SystemSourceEnum.BUSINESS_WEB.equals(loginSource)
                && EnableDisableStatusEnum.DISABLE.getCode().equals(loginPlatformRelationDO.getVerified())) {
            if (MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode().equals(loginPlatformRelationDO.getOuterStatus())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_REGISTRATION_UNDER_REVIEW_TRY_AGAIN_LATER_OR_CONTACT_CUSTOMER);
            }
            if (MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode().equals(loginPlatformRelationDO.getOuterStatus())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_REGISTRATION_PLATFORM_HAS_NOT_BEEN_APPROVED_PLEASE_CONTACT_CUSTOMER_SERVICE);
            }
            throw new BusinessException(ResponseCodeEnum.MC_MS_ACCOUNT_IS_PENDING_APPROVAL_OR_HAS_NOT_BEEN_APPROVED);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MemberRoleItem implements Serializable {
        private static final long serialVersionUID = 6318747746358880289L;

        /**
         * 会员角色Id
         */
        private Long roleId;

        /**
         * 会员角色名称
         */
        private String roleName;

        /**
         * 会员角色类型，1-服务提供者，2-服务消费者
         */
        private Integer roleType;
    }
}
