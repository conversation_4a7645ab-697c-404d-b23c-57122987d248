package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.logistics.api.feign.ILogisticsCompanyFeign;
import com.ssy.lingxi.logistics.api.model.req.LogisticsCompanyAddReq;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.service.feign.ILogisticsFeignService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 调用物流服务Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-15
 */
@Service
public class LogisticsFeignServiceImpl implements ILogisticsFeignService {
    private static final Logger logger = LoggerFactory.getLogger(MessageFeignServiceImpl.class);

    @Resource
    private ILogisticsCompanyFeign logisticsCompanyFeign;

    /**
     * 平台会员审核通过后，通知物流服务，创建默认物流服务
     *
     * @param memberRelation 平台会员关系
     */
    @Async
    @Override
    public void initMemberLogisticsAsync(MemberRelationDO memberRelation) {
        Long memberId = memberRelation.getSubMemberId();
        Long roleId = memberRelation.getSubRoleId();
        try {
            if(!memberRelation.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode())) {
                return;
            }

            LogisticsCompanyAddReq request = new LogisticsCompanyAddReq();
            request.setMemberId(memberId);
            request.setRoleId(roleId);
            WrapperResp<Void> result = logisticsCompanyFeign.addLogisticsCompany(request);
            if(result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                logger.error("通知物流服务错误 => memberId:" + memberId + ", roleId:" + roleId + ", code:" + result.getCode() + ", msg:" + result.getMessage());
            }
        } catch (Exception e) {
            logger.error("通知物流服务异常 => memberId:" + memberId + ", roleId:" + roleId + ", msg:" + e.getMessage());
        }
    }
}
