package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.member.model.req.basic.ProvinceCodeReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.validate.*;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员能力 - 会员审核入库、资料变更相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-18
 */
public interface IMemberAbilityDepositService {

    /**
     * 获取会员审核入库各个步骤分页查询列表页面下拉框
     * @param loginUser 登录用户
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberDepositSearchConditionResp getDepositPageConditions(UserLoginCacheDTO loginUser, Integer roleTag);

    /**
     * 分页查询“待审核入库资料”会员列表
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberDepositPageQueryResp> pageToVerifyDepositoryDetail(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag);

    /**
     * “待审核入库资料” - 查询会员详情
     * @param loginUser 登录用户
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberToVerifyDepositDetailResp getToVerifyDepositoryDetail(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待审核入库资料” - 审核会员
     * @param loginUser 登录用户信息
     * @param depositVO 接口参数
     * @param roleTag 角色标签
     * @return 审核结果
     */
    void toVerifyDepositoryDetail(UserLoginCacheDTO loginUser, MemberToVerifyDepositReq depositVO, Integer roleTag);

    /**
     * “待审核入库资料” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void batchVerifyDepositoryDetail(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag);

    /**
     * 分页查询“待审核入库资质”会员列表
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberDepositPageQueryResp> pageToVerifyDepositoryQualification(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag);

    /**
     * “待审核入库资质” - 查询会员详情
     * @param loginUser 登录用户
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberDepositDetailResp getToVerifyDepositoryQualification(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待审核入库资质” - 审核
     * @param loginUser 登录用户信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void toVerifyDepositoryQualification(UserLoginCacheDTO loginUser, ValidateAgreeReq idVO, Integer roleTag);

    /**
     * “待审核入库资质” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void batchVerifyDepositoryQualifications(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag);

    /**
     * 分页查询“待入库考察”会员列表
     * @param loginUser 登录用户
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberDepositPageQueryResp> pageToInspectDepository(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag);

    /**
     * “待入库考察” - 查询会员详情
     * @param loginUser 登录用户
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberDepositDetailResp getToInspectDepository(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待入库考察” - 审核
     * @param loginUser 登录用户信息
     * @param depositVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void toInspectDepository(UserLoginCacheDTO loginUser, MemberToInspectDepositReq depositVO, Integer roleTag);

    /**
     * 分页查询“待入库分类”会员列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberDepositPageQueryResp> pageToClassifyDepository(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag);

    /**
     * “待入库分类” - 查询会员详情
     * @param loginUser 登录用户信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberToClassifyDetailResp getToClassifyDepository(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待入库分类” - “适用区域”-省列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<AreaCodeNameResp> getToClassifyProvinces(HttpHeaders headers);

    /**
     * “待入库分类” - “适用区域”-根据省编码查询市列表
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 查询结果
     */
    List<AreaCodeNameResp> getToClassifyCities(HttpHeaders headers, ProvinceCodeReq codeVO);

    /**
     * “待入库分类” - 品类信息 - 查询结算方式与发票类型
     * @param headers Http头部信息
     * @return 查询结果
     */
    MemberClassifyCategoryItemResp getToClassifyCategoryItems(HttpHeaders headers);

    /**
     * “待入库分类” - 审核
     * @param loginUser 登录用户信息
     * @param depositVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void toClassifyDepository(UserLoginCacheDTO loginUser, MemberToClassifyDepositReq depositVO, Integer roleTag);


    /**
     * 分页查询供应商列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDepositPageQueryResp> pageToDepositGrade(HttpHeaders headers, MemberDepositPageDataReq pageVO, UserLoginCacheDTO loginUser);

    /**
     * 分页查询“待审核入库(一级)”会员列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberDepositPageQueryResp> pageToDepositGradeOne(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag);

    /**
     * “待审核入库(一级)” - 查询会员详情
     * @param loginUser 登录用户信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberDepositGradeDetailResp getToDepositGradeOne(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待审核入库(一级)” - 审核
     * @param loginUser 登录用户信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void toDepositGradeOne(UserLoginCacheDTO loginUser, ValidateAgreeReq idVO, Integer roleTag);

    /**
     * “待审核入库(一级)” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void batchDepositGradeOne(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag);

    /**
     * 分页查询“待审核入库(二级)”会员列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberDepositPageQueryResp> pageToDepositGradeTwo(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag);

    /**
     * “待审核入库(二级)” - 查询会员详情
     * @param loginUser 登录用户信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberDepositGradeDetailResp getToDepositGradeTwo(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待审核入库(二级)” - 审核
     * @param loginUser 登录用户信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void toDepositGradeTwo(UserLoginCacheDTO loginUser, ValidateAgreeReq idVO, Integer roleTag);

    /**
     * “待审核入库(二级)” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void batchDepositGradeTwo(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag);

    /**
     * 分页查询“待确认入库”会员列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberDepositPageQueryResp> pageToConfirmDepository(UserLoginCacheDTO loginUser, MemberDepositPageDataReq pageVO, Integer roleTag);

    /**
     * “待确认入库” - 查询会员详情
     * @param loginUser 登录用户信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberDepositGradeDetailResp getToConfirmDepository(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * “待确认入库” - 审核
     * @param loginUser 登录用户信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void toConfirmDepository(UserLoginCacheDTO loginUser, ValidateAgreeReq idVO, Integer roleTag);

    /**
     * “待确认入库” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    void batchConfirmDepositories(HttpHeaders headers, ValidateIdsReq idVO, Integer roleTag);
}
