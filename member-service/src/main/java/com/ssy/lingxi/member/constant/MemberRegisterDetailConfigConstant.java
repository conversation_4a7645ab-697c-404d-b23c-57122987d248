package com.ssy.lingxi.member.constant;

/**
 * 注册资料字段名常量
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/14
 */
public interface MemberRegisterDetailConfigConstant {

    /**
     * 会员名称
     */
    String MEMBER_NAME = "hymc";

    /**
     * 企业名称
     */
    String CORPORATION_NAME = "qymc";

    /**
     * 企业简称
     */
    String CORPORATION_SIMPLE_NAME = "qyjc";

    /**
     * 统一社会代码
     */
    String UNIFIED_SOCIAL_CODE = "tyshdm";

    /**
     * 营业执照
     */
    String BUSINESS_LICENSE = "yyzz";

    /**
     * 有效期
     */
    String BUSINESS_LICENSE_VALID_END_TIME = "yxq";

    /**
     * 法人姓名
     */
    String LEGAL_PERSON_NAME = "frxm";

    /**
     * 法人身份证号
     */
    String LEGAL_PERSON_IDENTITY_CARD_NO = "frsfzh";

    /**
     * 法人手机号
     */
    String LEGAL_PERSON_PHONE = "frsjh";

    /**
     * 身份证正面
     */
    String ID_CARD_FRONT = "sfzzm";

    /**
     * 身份证反面
     */
    String ID_CARD_BACK = "sfzfm";

    /**
     * 业务负责人
     */
    String BUSINESS_MANAGER = "ywfzr";

    /**
     * 业务负责人电话号码
     */
    String BUSINESS_MANAGER_PHONE = "ywfzrdhhm";

    /**
     * 财务负责人
     */
    String FINANCE_MANAGER = "cwfzr";

    /**
     * 财务负责人电话号码
     */
    String FINANCE_MANAGER_PHONE = "cwfzrdhhm";

    /**
     * 基本信息
     */
    String BASIC_INFO_GROUP = "基本信息";

    /**
     * 企业信息
     */
    String CORPORATION_INFO = "企业信息";

    /**
     * 相关联系人
     */
    String RELEVANT_CONTACT_PERSON = "相关联系人";

}
