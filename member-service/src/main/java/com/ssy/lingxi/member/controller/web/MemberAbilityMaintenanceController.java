package com.ssy.lingxi.member.controller.web;

import cn.hutool.core.collection.CollectionUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.dataauth.annotation.member.MemberAuth;
import com.ssy.lingxi.member.api.model.resp.MemberManageQueryResp;
import com.ssy.lingxi.member.model.req.basic.MemberNameDataReq;
import com.ssy.lingxi.member.model.req.basic.ProvinceCodeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityMaintenanceMemberQueryDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.DetailTextGroupResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordInspectPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordRectifyResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.MemberClassifyCategoryItemResp;
import com.ssy.lingxi.member.model.resp.validate.MemberClassifyQueryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilityMaintenanceService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员能力-会员管理-会员信息相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/ability/maintenance")
public class MemberAbilityMaintenanceController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IMemberAbilityMaintenanceService memberAbilityMaintenanceService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    /**
     * 列表查询页面中各个下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/pageitems")
    public WrapperResp<MemberMaintenanceSearchConditionResp> getPageCondition(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityMaintenanceService.getPageCondition(headers, loginUser, roleTag));
    }

    /**
     * 统计各个生命周期下级会员人数
     *
     * @param headers 请求头
     * @return 返回列表
     */
    @GetMapping("/statistic/lifecycle/count")
    public WrapperResp<List<MemberLifeCycleStatisticDetailResp>> statisticMemberLifeCycleCount(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityMaintenanceService.statisticMemberLifeCycleCount(loginUser, roleTag));
    }

    /**
     * 分页、模糊查询会员
     * @param headers Http头部信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @MemberAuth
    @PostMapping("/page")
    public WrapperResp<PageDataResp<MemberMaintenancePageQueryResp>> pageMembers(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityMaintenanceMemberQueryDataReq queryVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMembers(headers, queryVO, loginUser, roleTag));
    }

    /**
     * 查询当前会员可查询的会员注册资料信息
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/registerDetailByAllowSelect")
    public WrapperResp<List<MemberConfigResp>> registerDetailByAllowSelect(@RequestHeader HttpHeaders headers, @Valid RoleIdReq roleIdReq) {
        return WrapperUtil.success(memberAbilityMaintenanceService.registerDetailByAllowSelect(headers, roleIdReq));
    }

    /**
     * 分页、模糊查询平台会员信息列表 - 会员引入
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/platform/page")
    public WrapperResp<PageDataResp<PlatformPageQueryMemberResp>> pagePlatformMembers(@RequestHeader HttpHeaders headers, @Valid PlatformMemberQueryDataReq queryVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pagePlatformMembers(headers, queryVO, roleTag));
    }

    /**
     * 分页、模糊查询平台会员信息列表 - 会员发现
     * @param headers HttpHeaders信息
     * @param memberNameReq 接口参数
     * @return 操作结果
     */
    @GetMapping("/platform/queryByCategory")
    public WrapperResp<PageDataResp<MemberDiscoverQueryResp>> queryMembersByCategory(@RequestHeader HttpHeaders headers, @Valid MemberNameDataReq memberNameReq) {
        return WrapperUtil.success(memberAbilityMaintenanceService.queryMembersByCategory(headers, memberNameReq, roleTag));
    }

    /**
     * 会员处理页面 - 会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail")
    public WrapperResp<MemberMaintenanceDetailResp> getMemberBasicDetail(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityMaintenanceService.getSubMemberDetail(loginUser, idVO, roleTag));
    }

    /**
     * 会员处理 - 会员冻结、解冻
     * @param headers Http头部信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/freeze")
    public WrapperResp<Void> updateMemberRelationStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberFreezeStatusReq statusVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityMaintenanceService.updateMemberRelationFreezeStatus(loginUser, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 会员处理 - 会员淘汰（解除关系）
     * @param headers HttpHeaders信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @PostMapping("/eliminate")
    public WrapperResp<Void> updateMemberEliminatedStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberEliminateOrBlacklistReq vo) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityMaintenanceService.updateMemberEliminatedStatus(loginUser, vo);
        return WrapperUtil.success();
    }

    /**
     * 会员处理 - 会员黑名单
     * @param headers HttpHeaders信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @PostMapping("/blacklist")
    public WrapperResp<Void> updateMemberBlacklistStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberEliminateOrBlacklistReq vo) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityMaintenanceService.updateMemberBlacklistStatus(loginUser, vo);
        return WrapperUtil.success();
    }

    /**
     * 会员详情 - 会员基本信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/basic")
    public WrapperResp<MemberMaintenanceDetailBasicResp> getMemberDetailBasic(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        MemberMaintenanceDetailBasicResp memberDetailBasic = memberAbilityMaintenanceService.getMemberDetailBasic(headers, idVO, roleTag);
        return WrapperUtil.success(memberDetailBasic);
    }

    /**
     * 会员详情 - 会员档案信息
     * @param headers HttpHeader信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/record")
    public WrapperResp<MemberMaintenanceRecordResp> getMemberRecords(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getMemberRecords(headers, idVO));
    }

    /**
     * 会员详情 - 会员档案 - 查询入库分类信息（修改页面）
     * @param headers HttpHeader信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/record/classify")
    public WrapperResp<MemberClassifyQueryResp> getMemberClassification(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getMemberClassification(headers, idVO));
    }

    /**
     * 会员详情 - 会员档案 - 修改入库分类 - 省列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/detail/record/classify/province")
    public WrapperResp<List<AreaCodeNameResp>> getClassifyProvinces(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getClassifyProvinces(headers));
    }

    /**
     * 会员详情 - 会员档案 - 修改入库分类 - 查询市列表
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/record/classify/city")
    public WrapperResp<List<AreaCodeNameResp>> getClassifyCities(@RequestHeader HttpHeaders headers, @Valid ProvinceCodeReq codeVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getClassifyCities(headers, codeVO));
    }

    /**
     * 会员详情 - 会员档案 - 修改入库分类 - 查询结算方式与发票类型
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/detail/record/classify/category/items")
    public WrapperResp<MemberClassifyCategoryItemResp> getToClassifyCategoryItems(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getToClassifyCategoryItems(headers));
    }

    /**
     * 会员详情 - 会员档案 - 修改入库分类信息
     * @param headers HttpHeader信息
     * @param updateVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/detail/record/classify/update")
    public WrapperResp<Void> updateMemberClassification(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberMaintenanceClassificationUpdateReq updateVO) {
        memberAbilityMaintenanceService.updateMemberClassification(headers, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 会员详情 - 会员档案 - 分页查询考察信息
     * @param headers HttpHeader信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/inspect/page")
    public WrapperResp<PageDataResp<MemberRecordInspectPageQueryResp>> pageMemberInspect(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberInspect(headers, pageVO));
    }

    /**
     * 会员详情 - 会员档案 - 分页查询会员整改
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/rectify/page")
    public WrapperResp<PageDataResp<MemberRecordRectifyResp>> pageMemberRecordRectify(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberRecordRectify(headers, pageVO));
    }

    /**
     * 会员详情- 会员档案 - 分页查询考评信息
     * @param headers HttpHeader信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/appraisal/page")
    public WrapperResp<PageDataResp<MemberAppraisalPageQueryResp>> pageMemberAppraisal(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberAppraisal(headers, pageVO));
    }

    /**
     * 会员详情 - 会员等级信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/level/basic")
    public WrapperResp<MemberValidateDetailLevelResp> getMemberLevelDetail(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getMemberLevelDetail(headers, validateVO));
    }

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/level/history/page")
    public WrapperResp<PageDataResp<MemberDetailLevelHistoryResp>> pageMemberLevelDetailHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberLevelDetailHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员权益信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/right/basic")
    public WrapperResp<MemberDetailRightResp> getMemberDetailRight(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getMemberDetailRight(headers, validateVO));
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益获取记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/right/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightHistoryResp>> pageMemberDetailRightHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberDetailRightHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益使用记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/right/spend/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightSpendHistoryResp>> pageMemberDetailRightSpendHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberDetailRightSpendHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员信用信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/basic")
    public WrapperResp<MemberDetailCreditResp> getMemberDetailCredit(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getMemberDetailCredit(headers, validateVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/trade/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> getMemberDetailCreditTradeCommentSummary(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getMemberDetailCreditTradeCommentSummary(headers, validateVO));
    }

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/trade/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditTradeHistoryResp>> pageMemberDetailCreditTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberDetailCreditHistoryPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberDetailCreditTradeCommentHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/aftersale/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> getMemberDetailCreditAfterSaleCommentSummary(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getMemberDetailCreditAfterSaleCommentSummary(headers, validateVO));
    }

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/aftersale/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditAfterSaleHistoryResp>> pageMemberDetailCreditAfterSaleCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberDetailCreditHistoryPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberDetailCreditAfterSaleCommentHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/complain/summary")
    public WrapperResp<MemberDetailCreditComplainSummaryResp> getMemberDetailCreditComplainSummary(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.getMemberDetailCreditComplainSummary(headers, validateVO));
    }

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/complain/history/page")
    public WrapperResp<PageDataResp<MemberCreditComplaintPageQueryResp>> pageMemberDetailCreditComplainHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberDetailCreditComplainHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员变更 - 分页查询会员变更记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/deposit/history/page")
    public WrapperResp<PageDataResp<MemberDepositDetailHistoryResp>> pageMemberDepositDetailHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.pageMemberDepositDetailHistory(headers, pageVO));
    }

    /**
     * 根据会员id和角色id，查询会员角色为服务提供者的下级会员列表
     * @param headers HttpHeaders信息
     * @param nameVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/subOrdinate/member/list")
    WrapperResp<PageDataResp<MemberManageQueryResp>> subordinateMemberList(@RequestHeader HttpHeaders headers, @Valid MemberNameDataReq nameVO) {
        return WrapperUtil.success(memberAbilityMaintenanceService.subOrdinateMemberList(headers, nameVO));
    }
}
