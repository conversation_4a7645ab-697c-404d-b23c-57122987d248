package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifeCycleRuleConfigDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 生命周期阶段规则配置Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-07-05
 */
@Repository
public interface MemberLifeCycleRuleConfigRepository extends JpaRepository<MemberLifeCycleRuleConfigDO, Long>, JpaSpecificationExecutor<MemberLifeCycleRuleConfigDO> {

    List<MemberLifeCycleRuleConfigDO> findByRoleTag(Integer roleTag);
}
