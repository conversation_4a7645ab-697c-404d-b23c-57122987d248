package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.MemberLevelRuleTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 会员升级规则枚举校验注解
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MemberLevelRuleTypeValidator.class)
public @interface MemberLevelRuleTypeAnno {
    boolean required() default true;

    String message() default "会员升级规则枚举参数值不在枚举定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
