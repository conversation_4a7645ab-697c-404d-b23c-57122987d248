package com.ssy.lingxi.member.model.req.discount.web;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户工费优惠表体明细请求对象
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerProcessFeeDiscountBtReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（更新时需要）
     */
    private Long id;

    /**
     * 分类id
     */
    private Long categoryId;

    /**
     * 参与优惠克重费用  起始 (>)
     */
    private BigDecimal startGramFee;

    /**
     * 参与优惠克重费用 截止克工费(<=)
     */
    private BigDecimal endGramFee;

    /**
     * 克工费优惠(每克减)
     */
    private BigDecimal gramFeeDiscount;

    /**
     * 克工费折扣(每克折扣)
     */
    private BigDecimal gramFeeRate;

    /**
     * 参与优惠件工费  起始(>)
     */
    private BigDecimal startPieceFee;

    /**
     * 参与优惠件工费  截止(<=)
     */
    private BigDecimal endPieceFee;

    /**
     * 件工费优惠(每件减)
     */
    private BigDecimal pieceFeeDiscount;

    /**
     * 件工费折扣(每件折扣)
     */
    private BigDecimal pieceFeeRate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 优惠截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime discountEndTime;
}
