package com.ssy.lingxi.member.serviceImpl.mobile;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRightSpendTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.enums.MemberRightTypeEnum;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberRightHistoryDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberRightSpendHistoryDO;
import com.ssy.lingxi.member.enums.MemberRightScoreBehaviorEnum;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.req.mobile.MobileRightScoreHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.mobile.MobileShopRightScorePageDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRightScoreResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileRightScoreHistoryResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileRightScoreResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileShopRightScoreHistoryResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.MemberRightHistoryRepository;
import com.ssy.lingxi.member.repository.MemberRightSpendHistoryRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberLevelConfigService;
import com.ssy.lingxi.member.service.mobile.IMobileLrcService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * App - 会员等级、权益、信用相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-17
 */
@Service
public class MobileLrcServiceImpl implements IMobileLrcService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberRightHistoryRepository memberRightHistoryRepository;

    @Resource
    private MemberRightSpendHistoryRepository memberRightSpendHistoryRepository;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private JdbcTemplate jdbcTemplate;

    /**
     * “积分订单” - 查询平台通用和会员专有权益积分
     *
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberRightScoreResp getMemberRightScore(HttpHeaders headers, MemberAndRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberLevelConfigService.getMemberRightPoint(idVO.getMemberId(), idVO.getRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
    }

    /**
     * “我的积分” - 分页查询列表
     * @param headers Http头部信息
     * @param pageDataReq  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileRightScoreResp> pageRightScore(HttpHeaders headers, PageDataReq pageDataReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        Pageable page = PageRequest.of(pageDataReq.getCurrent() -1, pageDataReq.getPageSize(), Sort.by("id").ascending());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MobileRightScoreResp scoreVO = new MobileRightScoreResp();
            scoreVO.setValidateId(relationDO.getId());
            scoreVO.setMemberId(relationDO.getMemberId());
            scoreVO.setMemberName(relationDO.getMember().getName());
            scoreVO.setScore(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getCurrentPoint());
            return scoreVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “我的积分” - 分页查询权益积分历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileRightScoreHistoryResp> pageRightScoreHistory(HttpHeaders headers, MobileRightScoreHistoryPageDataReq pageVO) {
        memberCacheService.needLoginFromMobile(headers);

        MemberRelationDO relationDO = relationRepository.findById(pageVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getMemberId().equals(pageVO.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        switch (pageVO.getScope()) {
            case 1:
                return pageAllRightScoreHistory(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), pageVO.getCurrent(), pageVO.getPageSize());
            case 2:
                return pageRightScoreHistory(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), pageVO.getCurrent(), pageVO.getPageSize());
            case 3:
                return pageRightScoreSpendHistory(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), pageVO.getCurrent(), pageVO.getPageSize());
            default:
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED);
        }
    }

    /**
     * “找店铺 - 积分兑换” - 分页查询权益积分历史记录
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public MobileShopRightScoreHistoryResp pageShopRightScoreHistory(HttpHeaders headers, MobileShopRightScorePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(pageVO.getUpperMemberId(), pageVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if(relationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        Integer currentPoint = relationDO.getLevelRight().getCurrentPoint();

        Long totalCount;
        List<MobileRightScoreHistoryResp> dataList;

        if(pageVO.getType().equals(MemberRightScoreBehaviorEnum.Gain.getCode())) {
            Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());
            Page<MemberRightHistoryDO> pageList = memberRightHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleIdAndRightTypeEnum(pageVO.getUpperMemberId(), pageVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRightTypeEnum.SCORE_POINTS_RIGHT.getCode(), pageable);
            totalCount = pageList.getTotalElements();
            dataList = pageList.getContent().stream().map(memberRightHistoryDO -> {
                MobileRightScoreHistoryResp historyVO = new MobileRightScoreHistoryResp();
                historyVO.setOperation("成交订单");
                historyVO.setScore(memberRightHistoryDO.getPoint().intValue());
                historyVO.setDate(memberRightHistoryDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                return historyVO;
            }).collect(Collectors.toList());

        } else if(pageVO.getType().equals(MemberRightScoreBehaviorEnum.Spend.getCode())) {
            Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());
            Page<MemberRightSpendHistoryDO> pageList = memberRightSpendHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(pageVO.getUpperMemberId(), pageVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), pageable);
            totalCount = pageList.getTotalElements();
            dataList = pageList.getContent().stream().map(memberRightSpendHistoryDO -> {
                MobileRightScoreHistoryResp historyVO = new MobileRightScoreHistoryResp();
                historyVO.setOperation(MemberRightSpendTypeEnum.parse(memberRightSpendHistoryDO.getSpendTypeEnum()).getTypeName());
                historyVO.setScore(memberRightSpendHistoryDO.getPoint());
                historyVO.setDate(memberRightSpendHistoryDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                return historyVO;
            }).collect(Collectors.toList());
        } else {
            PageDataResp<MobileRightScoreHistoryResp> result =  pageAllRightScoreHistory(pageVO.getUpperMemberId(), pageVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getCurrent(), pageVO.getPageSize());

            totalCount = result.getTotalCount();
            dataList = result.getData();
        }

        MobileShopRightScoreHistoryResp historyVO = new MobileShopRightScoreHistoryResp();
        historyVO.setCurrentPoint(currentPoint);
        historyVO.setTotalCount(totalCount);
        historyVO.setData(dataList);

        return historyVO;
    }

    /**
     * 分页查询权益积分历史记录（包括获得的权益积分、消费的权益积分）
     * @param upperMemberId 上级会员Id
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param current 当前页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    private PageDataResp<MobileRightScoreHistoryResp> pageAllRightScoreHistory(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, int current, int pageSize) {
        String countSql = "select count(*) from (\n" +
                "select '成交订单' as operation, to_char(create_time, 'YYYY-MM-DD HH24:MI:SS') as date, point as score from mem_member_right_history\n" +
                "where right_type_enum = 3 and member_id = " + upperMemberId  + " and role_id = " + upperRoleId +" and sub_member_id = " + subMemberId + " and sub_role_id = " + subRoleId + " \n" +
                "union\n" +
                "select spend_type_enum::text as operation, to_char(create_time, 'YYYY-MM-DD HH24:MI:SS') as date, -point as score from mem_member_right_spend_history\n" +
                "where member_id = " + upperMemberId  + " and role_id = " + upperRoleId +" and sub_member_id = " + subMemberId + " and sub_role_id = " + subRoleId + " \n" +
                ") a";

        String pageSql = "select a.operation, left(a.date,10) as date, a.score from (\n" +
                "select '成交订单' as operation, to_char(create_time, 'YYYY-MM-DD HH24:MI:SS') as date, point as score from mem_member_right_history\n" +
                "where right_type_enum = 3 and member_id = " + upperMemberId  + " and role_id = " + upperRoleId +" and sub_member_id = " + subMemberId + " and sub_role_id = " + subRoleId + " \n" +
                "union\n" +
                "select spend_type_enum::text as operation, to_char(create_time, 'YYYY-MM-DD HH24:MI:SS') as date, -point as score from mem_member_right_spend_history\n" +
                "where member_id = " + upperMemberId  + " and role_id = " + upperRoleId +" and sub_member_id = " + subMemberId + " and sub_role_id = " + subRoleId + " \n" +
                ") a \n" +
                "order by a.date desc \n" +
                "limit " + pageSize + " offset " + (current - 1) * pageSize;

        Long totalCount = jdbcTemplate.queryForObject(countSql, Long.class);
        if(totalCount == null) {
            totalCount = 0L;
        }

        List<MobileRightScoreHistoryResp> resultList = new ArrayList<>();
        if(totalCount > 0) {
            resultList = jdbcTemplate.query(pageSql, new BeanPropertyRowMapper<>(MobileRightScoreHistoryResp.class));
            for (MobileRightScoreHistoryResp scoreHistoryResp : resultList) {
                if (scoreHistoryResp.getOperation().matches("\\d+")) {
                    scoreHistoryResp.setOperation(MemberRightSpendTypeEnum.parse(Integer.parseInt(scoreHistoryResp.getOperation())).getTypeName());
                }
            }
        }

        return new PageDataResp<>(totalCount, resultList);
    }

    /**
     * 分页查询权益积分获取历史记录
     * @param upperMemberId 上级会员Id
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param current 当前页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    private PageDataResp<MobileRightScoreHistoryResp> pageRightScoreHistory(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, int current, int pageSize) {
        Pageable pageable = PageRequest.of(current - 1, pageSize, Sort.by("createTime").descending());
        Page<MemberRightHistoryDO> pageList = memberRightHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleIdAndRightTypeEnum(upperMemberId, upperRoleId, subMemberId, subRoleId, MemberRightTypeEnum.SCORE_POINTS_RIGHT.getCode(), pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(history -> {
            MobileRightScoreHistoryResp historyVO = new MobileRightScoreHistoryResp();
            historyVO.setDate(history.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            historyVO.setOperation("成交订单");
            historyVO.setScore(history.getPoint().intValue());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 分页查询权益积分消费历史记录
     * @param upperMemberId 上级会员Id
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param current 当前页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    private PageDataResp<MobileRightScoreHistoryResp> pageRightScoreSpendHistory(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, int current, int pageSize) {
        Pageable pageable = PageRequest.of(current - 1, pageSize, Sort.by("createTime").descending());
        Page<MemberRightSpendHistoryDO> pageList = memberRightSpendHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperMemberId, upperRoleId, subMemberId, subRoleId, pageable);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(history -> {
            MobileRightScoreHistoryResp historyVO = new MobileRightScoreHistoryResp();
            historyVO.setDate(history.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            historyVO.setOperation(MemberRightSpendTypeEnum.parse(history.getSpendTypeEnum()).getTypeName());
            historyVO.setScore(history.getPoint());
            return historyVO;
        }).collect(Collectors.toList()));
    }
}
