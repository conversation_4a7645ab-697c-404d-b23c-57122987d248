package com.ssy.lingxi.member.serviceImpl.mobile;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.model.req.basic.MemberLogoReq;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRegisterTagResp;
import com.ssy.lingxi.member.model.resp.basic.MobileRegisterTagResp;
import com.ssy.lingxi.member.model.resp.basic.UserDetailResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberRegisterDetailService;
import com.ssy.lingxi.member.service.mobile.IMobileCommonBusinessService;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * App - 会员其他业务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-02-03
 */
@Service
public class MobileCommonBusinessServiceImpl implements IMobileCommonBusinessService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberRegisterDetailService memberRegisterDetailService;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    /**
     * 新增或修改用户Logo
     *
     * @param headers Http头部信息
     * @param logoVO  接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void addMemberUserLogo(HttpHeaders headers, MemberLogoReq logoVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if(userDO == null || userDO.getMember() == null || !userDO.getMember().getId().equals(loginUser.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        userDO.setLogo(logoVO.getLogo());
        userRepository.saveAndFlush(userDO);

        if(userDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            MemberDO memberDO = userDO.getMember();
            memberDO.setLogo(logoVO.getLogo());
            memberRepository.saveAndFlush(memberDO);
        }

    }

    /**
     * 查询会员标签注册资料
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MobileRegisterTagResp getMemberRegisterTagDetail(HttpHeaders headers, MemberAndRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        MemberRegisterTagResp result = memberRegisterDetailService.getMemberRegisterTagDetail(idVO.getMemberId());
        MobileRegisterTagResp tagVO = new MobileRegisterTagResp();
        BeanUtils.copyProperties(result, tagVO);

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(idVO.getMemberId(), idVO.getRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if(relationDO == null) {
            final Integer isNotSubMember = -1;
            tagVO.setOuterStatus(isNotSubMember);
        } else {
            tagVO.setOuterStatus(relationDO.getOuterStatus());
        }

        return tagVO;
    }

    /**
     * 查询用户注册资料
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public UserDetailResp getUserDetail(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return baseMemberRegisterDetailService.getUserDetail(loginUser.getUserId());
    }
}
