package com.ssy.lingxi.member.service.mobile;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.mobile.MobileMemberTradeCommentSaveReq;
import com.ssy.lingxi.member.model.resp.mobile.*;

/**
 * App - 会员评价服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/2/24
 */
public interface IMobileMemberCommentService {

    /**
     * 评价中心 - 待评价 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageDataReq 接口参数
     * @return 返回结果
     */
    PageDataResp<MobileWaitCommentOrderProductPageResp> pageWaitOrderComment(UserLoginCacheDTO loginUser, PageDataReq pageDataReq);

    /**
     * 评价中心 - 待评价 - 评价商品详情
     * @param loginUser 登录用户信息
     * @param commonIdReq 接口参数
     * @return 返回结果 查询结果
     */
    MobileCommentOrderProductResp getMemberOrderComment(UserLoginCacheDTO loginUser, CommonIdReq commonIdReq);

    /**
     * 评价中心 - 待评价 - 写评价
     * @param loginUser 登录用户信息
     * @param memberTradeCommentSaveVO 接口参数
     */
    void saveMemberComment(UserLoginCacheDTO loginUser, MobileMemberTradeCommentSaveReq memberTradeCommentSaveVO);

    /**
     * 评价中心 - 已评价 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageDataReq 接口参数
     * @return 返回结果
     */
    PageDataResp<MobileCompleteCommentPageResp> pageCompleteOrderComment(UserLoginCacheDTO loginUser, PageDataReq pageDataReq);

    /**
     * 评价中心 - 已评价 - 评价商品详情
     * @param loginUser 登录用户信息
     * @param commonIdReq 接口参数
     * @return 返回结果 查询结果
     */
    MobileCommentHistoryResp getMemberCommentHistory(UserLoginCacheDTO loginUser, CommonIdReq commonIdReq);

    /**
     * 评价中心 - 卖家对我的评价 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageDataReq 接口参数
     * @return 返回结果
     */
    PageDataResp<MobileReceiveCommentPageResp> pageReceiveOrderComment(UserLoginCacheDTO loginUser, PageDataReq pageDataReq);

}
