package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.complain.MemberComplainHistoryDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员投诉历史记录操作Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
@Repository
public interface MemberComplainHistoryRepository extends JpaRepository<MemberComplainHistoryDO, Long>, JpaSpecificationExecutor<MemberComplainHistoryDO> {

    List<MemberComplainHistoryDO> findAllBySubMemberIdAndSubRoleId(Long memberId, Long roleId);

    Page<MemberComplainHistoryDO> findAllBySubMemberIdAndSubRoleId(Long memberId, Long roleId, Pageable pageable);

    Page<MemberComplainHistoryDO> findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Pageable pageable);

    @Transactional
    void deleteAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    @Transactional
    void deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);
}
