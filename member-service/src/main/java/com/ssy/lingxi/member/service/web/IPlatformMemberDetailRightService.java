package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailRightHistoryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailRightResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailRightSpendHistoryResp;
import org.springframework.http.HttpHeaders;

/**
 * 平台后台 - 会员维护 - 会员详情 - 权益信息服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
public interface IPlatformMemberDetailRightService {
    /**
     * 查询会员详情 - 会员权益信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailRightResp getMemberDetailRight(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 查询会员详情 - 分页查询会员权益获取记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 查询会员详情 - 分页查询会员权益使用记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

}
