package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.util.List;

/**
 * 新增会员适用角色VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-11
 **/
@Data
public class AddRoleRuleReq implements Serializable {
    private static final long serialVersionUID = -8725670524966388820L;

    /*
     * 会员id
     */
    @NotNull(message = "会员Id要大于0")
    @Positive(message = "会员Id要大于0")
    private Long memberId;

    /**
     * 会员要增加的适用角色idList
     */
    private List<Long> memberRoleIdList;

    /**
     * 下属会员要增加的适用角色idList
     */
    private List<Long> subMemberRoleIdList;
}
