package com.ssy.lingxi.member.model.req.comment;

import lombok.Data;

import java.io.Serializable;

/**
 * 生命周期规则配置保存VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 */
@Data
public class MemberLifeCycleRuleConfigReq implements Serializable {
    private static final long serialVersionUID = 5329315749606790436L;

    /**
     * 生命周期阶段规则id
     */
    private Long lifeCycleRuleId;

    /**
     * 生命周期阶段规则--对应枚举的code
     * 枚举 在 MemberLifecycleStagesRuleEnum类中
     * 也在配置表中。后续如果要追加需要初始化数据到mem_member_lifecycle_config该表
     * 供应商的规则
     * 1  允许参与寻源
     * 2  允许签订合同
     * 3  允许创建该供应商的订单
     * 4  允许变更入库资料
     * 客户的规则
     * 5  允许发布商品询价
     * 6  允许签订合同
     * 7  允许创建订单
     * 8  允许变更申请资料
     */
    private Integer lifeCycleRuleEnum;

    /**
     * 生命周期规则名称--对应枚举的message
     */
    private String lifeCycleRuleName;

}
