package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.PlatformMemberCycleProcessDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 平台会员生命周期流程
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
public interface PlatformMemberCycleProcessRepository extends JpaRepository<PlatformMemberCycleProcessDO, Long>, JpaSpecificationExecutor<PlatformMemberCycleProcessDO> {

    PlatformMemberCycleProcessDO findFirstByProcessTypeAndIsDefaultAndSource(Integer processType, Integer isDefault, Integer source);

    List<PlatformMemberCycleProcessDO> findByIsDefaultAndSource(Integer isDefault, Integer source);

    List<PlatformMemberCycleProcessDO> findByProcessTypeAndIsDefaultAndSource(Integer processType, Integer isDefault, Integer source);

}
