package com.ssy.lingxi.member.controller.web.supplier;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.dataauth.annotation.member.MemberAuth;
import com.ssy.lingxi.member.model.req.basic.MemberTypeAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.basic.LevelAndTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameAndMemberTypeResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilityImportService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商能力 - 会员导入相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-27
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/supplier/ability/sub")
public class SupplierAbilityImportController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.SUPPLIER.getCode();

    @Resource
    private IMemberAbilityImportService memberAbilityImportService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    /**
     * 获取分页查询会员列表页面中各个查询条件下拉选择框的内容
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/pageitems")
    public WrapperResp<MemberImportSearchConditionResp> getPageCondition(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityImportService.getPageCondition(headers, loginUser, roleTag));
    }

    /**
     * 分页、模糊查询会员
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/page")
    @MemberAuth
    public WrapperResp<PageDataResp<MemberAbilityImportPageQueryResp>> pageMembers(@RequestHeader HttpHeaders headers, @Valid MemberAbilityImportMemberQueryDataReq queryVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityImportService.pageMembers(loginUser, queryVO, roleTag));
    }


    /**
     * 获取新增会员页面内容（审核步骤、会员类型、手机号前缀）
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/pageitems/basic")
    public WrapperResp<MemberAbilityAddMemberPageItemsResp> getAddMemberPageItems(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityImportService.getAddMemberPageItems(headers, loginUser));
    }

    /**
     * 新增会员页面，根据会员类型，查询角色列表
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/pageitems/role")
    public WrapperResp<List<RoleIdAndNameAndMemberTypeResp>> getAddMemberPageRoles(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityImportService.getAddMemberPageRolesByRoleTag(headers, roleTag));
    }

    /**
     * 新增会员页面，根据会员类型和角色，查询等级列表
     * @param headers HttpHeaders信息
     * @param typeAndRoleIdVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/pageitems/level")
    public WrapperResp<List<LevelAndTagResp>> getAddMemberPageLevels(@RequestHeader HttpHeaders headers, @Valid MemberTypeAndRoleIdReq typeAndRoleIdVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityImportService.getAddMemberPageLevels(loginUser, typeAndRoleIdVO));
    }

    /**
     * 新增会员页面，根据选择的角色，返回会员注册资料信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/pageitems/detail")
    public WrapperResp<List<MemberConfigGroupResp>> getAddMemberPageMemberConfigItems(@RequestHeader HttpHeaders headers, @Valid RoleIdReq idVO) {
        return WrapperUtil.success(memberAbilityImportService.getAddMemberPageMemberConfigItems(headers, idVO));
    }

    /**
     * 新增会员
     * @param headers HttpHeaders信息
     * @param memberVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/add")
    public WrapperResp<Void> addSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityAddMemberReq memberVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityImportService.addSubMember(loginUser, memberVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 引入单个/多个会员
     * @param headers HttpHeaders信息
     * @param memberListVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/introduce")
    public WrapperResp<Void> introduceSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityAddMemberListReq memberListVO) {
        memberAbilityImportService.introduceSubMember(headers, memberListVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 查询单个会员详细信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberAbilityImportMemberDetailResp> getSubMember(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityImportService.getSubMember(loginUser, validateVO, roleTag));
    }

    /**
     * 修改会员信息
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/update")
    public WrapperResp<Void> updateSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityUpdateMemberReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityImportService.updateSubMember(loginUser, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 删除会员
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/delete")
    public WrapperResp<Void> deleteSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityImportService.deleteSubMember(loginUser, validateVO);
        return WrapperUtil.success();
    }

    /**
     * 提交平台审核
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/commit")
    public WrapperResp<Void> commitSubMemberToValidate(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityImportService.commitSubMemberToValidate(loginUser, validateVO);
        return WrapperUtil.success();
    }

    /**
     * 会员详情
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail")
    public WrapperResp<MemberAbilityImportMemberTextDetailResp> getSubMemberDetail(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityImportService.getSubMemberDetail(loginUser, validateVO, roleTag));
    }

    /**
     * 会员可用角色查询(导出excel模版)
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/excel/role")
    public WrapperResp<List<RoleIdAndNameResp>> excelRoles(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityImportService.excelRoles(headers, roleTag));
    }

    /**
     * 导出会员导入模版（导出excel模版）
     * @param headers HttpHeaders信息
     * @param roleIdReq 角色id
     * @param request 请求
     * @param response 响应
     */
    @GetMapping("/excel/exportTemplate")
    public void exportExcelTemplate(@RequestHeader HttpHeaders headers, RoleIdReq roleIdReq, HttpServletRequest request, HttpServletResponse response) {
        memberAbilityImportService.exportExcelTemplate(headers, roleIdReq, request, response);
    }

    /**
     * 导入会员（excel导入）
     * @param headers HttpHeaders信息
     * @param file 会员导入文件
     * @return 操作结果
     */
    @PostMapping("/excel/importMembers")
    public WrapperResp<Void> importExcelMembers(@RequestHeader HttpHeaders headers, MultipartFile file, HttpServletRequest request) {
        memberAbilityImportService.importExcelMembers(headers, file, request);
        return WrapperUtil.success();
    }

    /**
     * 供应商发现 - 邀请
     * @param headers HttpHeaders信息
     * @param discoverVO 请求参数
     * @return 操作结果
     */
    @PostMapping("/discover/invitation")
    public WrapperResp<Void> discoverInvitation(@RequestHeader HttpHeaders headers, @RequestBody MemberDiscoverReq discoverVO) {
        memberAbilityImportService.discoverInvitation(headers, discoverVO, roleTag);
        return WrapperUtil.success();
    }
}
