package com.ssy.lingxi.member.model.req.comment;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 生命周期配置保存VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 */
@Data
public class MemberLifecycleStagesAndRuleReq implements Serializable {
    private static final long serialVersionUID = 5329315749606790437L;

    /**
     * 生命周期阶段Id(记录所属的会员生命周期阶段)
     */
    private Long lifecycleStagesId;

    /**
     * 生命周期阶段序号(记录所属的会员生命周期阶段)
     */
    private Integer lifecycleStagesNum;

    /**
     * 生命周期阶段名称(记录所属的生命周期阶段名称)
     */
    private String lifecycleStagesName;

    /**
     * 生命周期阶段对应的多个规则
     */
    //private List<MemberLifeCycleRuleConfigVo> memberLifeCycleRuleConfigVos;
    private  List<Long> lifecycleStagesRuleIds;

}
