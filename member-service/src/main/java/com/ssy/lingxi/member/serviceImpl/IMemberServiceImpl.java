package com.ssy.lingxi.member.serviceImpl;

import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.model.resp.MemberInfoResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.service.IMemberService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员服务service
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/16
 */
@Service
public class IMemberServiceImpl implements IMemberService {


    @Resource
    private MemberRepository memberRepository;


    /**
     * 根据企业Id列表查询会员信息
     *
     * @param corporationIds 企业Id列表
     * @return 会员信息列表
     */
    @Override
    public List<MemberInfoResp> findByCorporationIds(List<Long> corporationIds) {
        List<MemberDO> memberDOList = memberRepository.findByCorporationIdIn(corporationIds);
        if (CollectionUtils.isEmpty(memberDOList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }
        return memberDOList.stream().map(memberDO -> {
            MemberInfoResp memberInfoResp = new MemberInfoResp();
            memberInfoResp.setId(memberDO.getId());
            memberInfoResp.setName(memberDO.getName());
            memberInfoResp.setCode(memberDO.getCode());
            memberInfoResp.setCorporationId(memberDO.getCorporationId());
            memberInfoResp.setLogo(memberDO.getLogo());
            memberInfoResp.setEmail(memberDO.getEmail());
            memberInfoResp.setPhone(memberDO.getPhone());
            memberInfoResp.setAccount(memberDO.getAccount());
            memberInfoResp.setMainFlag(memberDO.getMainFlag());
            memberInfoResp.setRegisterTime(memberDO.getRegisterTime());
            memberInfoResp.setTelCode(memberDO.getTelCode());
            memberInfoResp.setRegisteredTrademark(memberDO.getRegisteredTrademark());
            return memberInfoResp;
        }).collect(Collectors.toList());
    }
}
