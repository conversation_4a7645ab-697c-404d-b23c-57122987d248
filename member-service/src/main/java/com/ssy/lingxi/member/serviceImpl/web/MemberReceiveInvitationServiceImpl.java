package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.constant.mq.MemberMqConstant;
import com.ssy.lingxi.common.enums.member.MemberConfigTagEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.RandomNumberUtil;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberReceiveInvitationDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberSendInvitationDO;
import com.ssy.lingxi.member.enums.InvitationCodeStateEnum;
import com.ssy.lingxi.member.enums.MemberConfigFieldTypeEnum;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.resp.basic.MemberInvitationCodeQueryResp;
import com.ssy.lingxi.member.model.resp.basic.MemberReceiveInvitationQueryResp;
import com.ssy.lingxi.member.model.resp.basic.MemberSendInvitationDetailResp;
import com.ssy.lingxi.member.model.resp.basic.MemberSendInvitationQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.StatusResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberReceiveInvitationService;
import com.ssy.lingxi.member.util.MailUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 接收邀请服务实现类
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/30 16:20
 */
@Service
public class MemberReceiveInvitationServiceImpl implements IMemberReceiveInvitationService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberReceiveInvitationRepository memberReceiveInvitationRepository;

    @Resource
    private MemberSendInvitationRepository memberSendInvitationRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private IMqUtils mqUtils;

    @Resource
    MemberRegisterConfigRepository memberRegisterConfigRepository;

    private final static Logger logger = LoggerFactory.getLogger(MemberReceiveInvitationServiceImpl.class);

    /**
     * 保存邀请
     * @param memberReceiveInvitationDO 接收邀请实体类
     */
    @Override
    public void saveInvitation(MemberReceiveInvitationDO memberReceiveInvitationDO) {
        MemberReceiveInvitationDO memberReceiveInvitation = memberReceiveInvitationRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(memberReceiveInvitationDO.getMemberId(), memberReceiveInvitationDO.getRoleId(), memberReceiveInvitationDO.getSubMemberId(), memberReceiveInvitationDO.getSubRoleId());
        if (Objects.nonNull(memberReceiveInvitation)) {
            memberReceiveInvitationDO.setId(memberReceiveInvitation.getId());
        }
        memberReceiveInvitationRepository.save(memberReceiveInvitationDO);
    }

    /**
     * 我收到的邀请信息 - 列表查询
     * @param headers  Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberReceiveInvitationQueryResp> receivePage(HttpHeaders headers, MemberReceiveInvitationPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable pageable = PageRequest.of(pageVO.getCurrent() -1, pageVO.getPageSize(), Sort.by("id").descending());
        Specification<MemberReceiveInvitationDO> specification = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(cb.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
            list.add(cb.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));

            list.add(cb.equal(root.get("state").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));

            // 邀请方客户名称
            if(StringUtils.hasLength(pageVO.getMemberName())) {
                Join<MemberReceiveInvitationDO, MemberDO> memberJoin = root.join("member", JoinType.LEFT);
                list.add(cb.like(memberJoin.get("name").as(String.class), "%" + pageVO.getMemberName() + "%"));
            }

            // 申请时间起始日期
            if (StringUtils.hasLength(pageVO.getStartDate())) {
                list.add(cb.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            }

            // 申请时间结束时间
            if (StringUtils.hasLength(pageVO.getEndDate())) {
                list.add(cb.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            }

            Predicate[] p = new Predicate[list.size()];
            return cb.and(list.toArray(p));
        };
        Page<MemberReceiveInvitationDO> pageList = memberReceiveInvitationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(invitation -> {
            MemberReceiveInvitationQueryResp queryVO = new MemberReceiveInvitationQueryResp();
            queryVO.setMemberName(invitation.getMember().getName());
            queryVO.setInvitationTime(invitation.getInvitationTime() == null ? null : invitation.getInvitationTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setSubMemberName(invitation.getSubMember().getName());
            queryVO.setRegisterAccount(invitation.getSubMember().getPhone());
            queryVO.setEmail(invitation.getSubMember().getEmail());
            queryVO.setRegisterTime(invitation.getSubMember().getRegisterTime() == null ? null : invitation.getSubMember().getRegisterTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setInvitationCode(invitation.getInvitationCode());
            queryVO.setUpperMemberId(invitation.getMemberId());
            queryVO.setUpperRoleId(invitation.getRoleId());
            // 未填写入库资料显示按钮
            queryVO.setFillInDepositoryDetail(CommonBooleanEnum.NO.getCode().equals(invitation.getFillInDepositoryDetail()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 客户/供应商邀请 - 列表查询
     * @param headers  Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSendInvitationQueryResp> sendPage(HttpHeaders headers, MemberSendInvitationPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable pageable = PageRequest.of(pageVO.getCurrent() -1, pageVO.getPageSize(), Sort.by("createTime").descending());
        Specification<MemberSendInvitationDO> specification = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(cb.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(cb.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));

            Join<MemberSendInvitationDO, MemberRoleDO> subRoleJoin = root.join("subRole", JoinType.INNER);
            list.add(cb.equal(subRoleJoin.get("roleTag").as(Integer.class), roleTag));

            // 邀请码状态
            if (NumberUtil.notNullOrZero(pageVO.getInvitationCodeState())) {
                list.add(cb.equal(root.get("invitationCodeState").as(Integer.class), pageVO.getInvitationCodeState()));
            }

            // 邀请时间起始日期
            if (StringUtils.hasLength(pageVO.getStartDate())) {
                list.add(cb.greaterThanOrEqualTo(root.get("invitationTime").as(LocalDateTime.class), LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            }

            // 邀请时间结束时间
            if (StringUtils.hasLength(pageVO.getEndDate())) {
                list.add(cb.lessThanOrEqualTo(root.get("invitationTime").as(LocalDateTime.class), LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            }

            Predicate[] p = new Predicate[list.size()];
            return cb.and(list.toArray(p));
        };

        Page<MemberSendInvitationDO> pageList = memberSendInvitationRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(invitation -> {
            MemberSendInvitationQueryResp queryVO = new MemberSendInvitationQueryResp();
            queryVO.setId(invitation.getId());
            queryVO.setInvitationCode(invitation.getInvitationCode());
            queryVO.setRoleId(invitation.getSubRoleId());
            queryVO.setRoleName(invitation.getSubRole().getRoleName());
            queryVO.setMemberName(invitation.getSubMemberName());
            queryVO.setEmail(invitation.getEmail());
            queryVO.setRegisterAccount(invitation.getAccount());
            queryVO.setInvitationValidityTime(invitation.getInvitationValidityTime() == null ? null : invitation.getInvitationValidityTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setInvitationTime(invitation.getInvitationTime() == null ? null : invitation.getInvitationTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setInvitationCodeState(invitation.getInvitationCodeState());
            queryVO.setInvitationCodeStateName(InvitationCodeStateEnum.getName(invitation.getInvitationCodeState()));
            boolean unsent = InvitationCodeStateEnum.UNSENT.getCode().equals(invitation.getInvitationCodeState());
            queryVO.setSendInvitationCode(unsent);
            queryVO.setDelete(unsent);
            queryVO.setUpdate(unsent);
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 客户/供应商邀请 - 新增
     * @param headers  Http头部信息
     * @param invitationVO 接口参数
     * @param roleTag 角色标签
     */
    @Override
    public void add(HttpHeaders headers, MemberSendInvitationReq invitationVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);


        MemberRoleDO subRole = memberRoleRepository.findById(invitationVO.getRoleId()).orElse(null);
        if(subRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (Objects.isNull(subRole.getRoleTag()) || !subRole.getRoleTag().equals(roleTag))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_TAG_NOT_MATCHING);
        }

        //判断邮箱（如果非空）是否存在
//        if(StringUtils.hasLength(invitationVO.getEmail()) && memberUserRepository.existsByRelTypeAndEmail(MemberRelationTypeEnum.OTHER.getCode(), invitationVO.getEmail().trim())) {
//            return Wrapper.fail(ResponseCode.MC_MS_MEMBER_USER_EMAIL_EXISTS);
//        }

        //会员名称是否已经注册
        if(StringUtils.hasLength(invitationVO.getMemberName()) && memberSendInvitationRepository.existsBySubMemberName(invitationVO.getMemberName())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
        }

        // 查询是否存在邀请
        MemberSendInvitationDO existsInvitation = memberSendInvitationRepository.findByMemberIdAndRoleIdAndSubMemberName(loginUser.getMemberId(), loginUser.getMemberRoleId(), invitationVO.getMemberName());
        if (Objects.nonNull(existsInvitation)) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_ALREADY_EXISTS);
        }

        // 邀请有效期
        LocalDateTime invitationValidityTime = LocalDateTime.parse(invitationVO.getInvitationValidityTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        int spin = 0;
        String invitationCode;
        while (memberSendInvitationRepository.existsByInvitationCode((invitationCode = RandomNumberUtil.randomUniqueNumber(6)))) {
            if (spin ++ > MemberConstant.SPIN) {
                break;
            }
        }

        // 自旋多次获取不到邀请码，考虑增长邀请码位数
        if (!StringUtils.hasLength(invitationCode)) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ERROR_IN_GETTING_INVITATION_CODE);
        }

        MemberSendInvitationDO memberSendInvitation = new MemberSendInvitationDO();
        memberSendInvitation.setMemberId(loginUser.getMemberId());
        memberSendInvitation.setRoleId(loginUser.getMemberRoleId());
        memberSendInvitation.setSubRoleId(subRole.getId());
        memberSendInvitation.setSubRole(subRole);
        memberSendInvitation.setSubMemberName(invitationVO.getMemberName());
        memberSendInvitation.setEmail(invitationVO.getEmail());
        memberSendInvitation.setCreateTime(LocalDateTime.now());
        memberSendInvitation.setInvitationValidityTime(invitationValidityTime);
        memberSendInvitation.setInvitationCode(invitationCode);
        memberSendInvitation.setInvitationCodeState(InvitationCodeStateEnum.UNSENT.getCode());
        memberSendInvitationRepository.save(memberSendInvitation);


    }

    /**
     * 客户/供应商邀请 - 详情
     * @param headers  Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberSendInvitationDetailResp detail(HttpHeaders headers, MemberSendInvitationEmailReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberSendInvitationDO sendInvitation = memberSendInvitationRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(sendInvitation) || (!sendInvitation.getMemberId().equals(loginUser.getMemberId()) || !sendInvitation.getRoleId().equals(loginUser.getMemberRoleId()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_NOT_EXISTS);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (Objects.isNull(sendInvitation.getSubRole().getRoleTag()) || !roleTag.equals(sendInvitation.getSubRole().getRoleTag()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_NOT_EXISTS);
        }

        MemberSendInvitationDetailResp memberSendInvitationDetailResp = new MemberSendInvitationDetailResp();
        memberSendInvitationDetailResp.setId(sendInvitation.getId());
        memberSendInvitationDetailResp.setRoleId(sendInvitation.getSubRoleId());
        memberSendInvitationDetailResp.setRoleName(sendInvitation.getSubRole().getRoleName());
        memberSendInvitationDetailResp.setMemberName(sendInvitation.getSubMemberName());
        memberSendInvitationDetailResp.setEmail(sendInvitation.getEmail());
        memberSendInvitationDetailResp.setInvitationValidityTime(sendInvitation.getInvitationValidityTime() == null ? null : sendInvitation.getInvitationValidityTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));

        return memberSendInvitationDetailResp;
    }

    /**
     * 客户/供应商邀请 - 更新
     * @param headers  Http头部信息
     * @param updateVO 接口参数
     */
    @Override
    public void update(HttpHeaders headers, MemberSendInvitationUpdateReq updateVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberSendInvitationDO sendInvitation = memberSendInvitationRepository.findById(updateVO.getId()).orElse(null);
        if (sendInvitation == null || (!sendInvitation.getMemberId().equals(loginUser.getMemberId()) || !sendInvitation.getRoleId().equals(loginUser.getMemberRoleId()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_NOT_EXISTS);
        }

        if (!InvitationCodeStateEnum.UNSENT.getCode().equals(sendInvitation.getInvitationCodeState())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_CODE_CANT_EDIT);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (Objects.isNull(sendInvitation.getSubRole().getRoleTag()) || !roleTag.equals(sendInvitation.getSubRole().getRoleTag()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_NOT_EXISTS);
        }

        MemberRoleDO subRole = memberRoleRepository.findById(updateVO.getRoleId()).orElse(null);
        if(subRole == null) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (Objects.isNull(subRole.getRoleTag()) || !subRole.getRoleTag().equals(roleTag))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_TAG_NOT_MATCHING);
        }

//        //判断邮箱（如果非空）是否存在
//        if(StringUtils.hasLength(updateVO.getEmail()) && memberUserRepository.existsByRelTypeAndEmail(MemberRelationTypeEnum.OTHER.getCode(), updateVO.getEmail().trim())) {
//            return Wrapper.fail(ResponseCode.MC_MS_MEMBER_USER_EMAIL_EXISTS);
//        }

        //会员名称是否已经注册
        if(StringUtils.hasLength(updateVO.getMemberName()) && (memberSendInvitationRepository.existsBySubMemberName(updateVO.getMemberName()) && !updateVO.getMemberName().equals(sendInvitation.getSubMemberName()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
        }

        // 查询是否存在邀请
        MemberSendInvitationDO existsInvitation = memberSendInvitationRepository.findByMemberIdAndRoleIdAndSubMemberName(loginUser.getMemberId(), loginUser.getMemberRoleId(), updateVO.getMemberName());
        if (Objects.nonNull(existsInvitation) && !existsInvitation.getId().equals(updateVO.getId())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_ALREADY_EXISTS);
        }

        // 邀请有效期
        LocalDateTime invitationValidityTime = LocalDateTime.parse(updateVO.getInvitationValidityTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        sendInvitation.setSubRoleId(subRole.getId());
        sendInvitation.setSubRole(subRole);
        sendInvitation.setSubMemberName(updateVO.getMemberName());
        sendInvitation.setEmail(updateVO.getEmail());
        sendInvitation.setUpdateTime(LocalDateTime.now());
        sendInvitation.setInvitationValidityTime(invitationValidityTime);
        memberSendInvitationRepository.save(sendInvitation);


    }

    /**
     * 客户/供应商邀请 - 发送邀请码
     * @param headers  Http头部信息
     * @param sendVO 接口参数
     * @return 查询结果
     */
    @Override
    public void send(HttpHeaders headers, MemberSendInvitationEmailReq sendVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberSendInvitationDO sendInvitation = memberSendInvitationRepository.findById(sendVO.getId()).orElse(null);
        if (sendInvitation == null || (!sendInvitation.getMemberId().equals(loginUser.getMemberId()) || !sendInvitation.getRoleId().equals(loginUser.getMemberRoleId()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_NOT_EXISTS);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (Objects.isNull(sendInvitation.getSubRole().getRoleTag()) || !roleTag.equals(sendInvitation.getSubRole().getRoleTag()))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_NOT_EXISTS);
        }

        if (!InvitationCodeStateEnum.UNSENT.getCode().equals(sendInvitation.getInvitationCodeState())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_CODE_STATE_IS_INVALID);
        }

        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(sendInvitation.getInvitationValidityTime())) {
            sendInvitation.setInvitationCodeState(InvitationCodeStateEnum.LOSE_EFFICACY.getCode());
            sendInvitation.setUpdateTime(now);
            memberSendInvitationRepository.save(sendInvitation);


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_CODE_EXPIRED);
        }

        String title = MemberStringEnum.SUPPLIER_EMAIL_REGISTER_TITLE.getName();
        String templete = MemberStringEnum.SUPPLIER_EMAIL_REGISTER_TEMPLATE.getName();
        if (RoleTagEnum.CUSTOMER.getCode().equals(roleTag)) {
            title = MemberStringEnum.CUSTOMER_EMAIL_REGISTER_TITLE.getName();
            templete = MemberStringEnum.CUSTOMER_EMAIL_REGISTER_TEMPLATE.getName();
        }
        // 发邮箱
        MailUtil.sendTextMail(String.format(title, loginUser.getMemberName()), String.format(templete, loginUser.getMemberName(), sendVO.getEmailMsg()), sendInvitation.getEmail());

        // 发送延时队列
        mqUtils.sendDelayMsg(MemberMqConstant.INVITATION_CODE_INVALID_DELAY_EXCHANGE, MemberMqConstant.INVITATION_CODE_INVALID_DELAY_ROUTINGKEY, sendInvitation.getId(), Duration.between(now, sendInvitation.getInvitationValidityTime()).toMillis());

        sendInvitation.setInvitationCodeState(InvitationCodeStateEnum.UNREGISTERED.getCode());
        sendInvitation.setInvitationTime(now);
        sendInvitation.setUpdateTime(now);
        memberSendInvitationRepository.save(sendInvitation);


    }

    @Override
    public void updateInvitationCode(Long invitationId) {
        logger.info("消费消息：" + invitationId);
        // 查询存在的邀请数据
        MemberSendInvitationDO sendInvitation = memberSendInvitationRepository.findById(invitationId).orElse(null);
        // 未注册则修改成失效状态
        if (Objects.nonNull(sendInvitation) && !InvitationCodeStateEnum.REGISTERED.getCode().equals(sendInvitation.getInvitationCodeState())) {
            logger.info("修改状态为失效");
            sendInvitation.setInvitationCodeState(InvitationCodeStateEnum.LOSE_EFFICACY.getCode());
            sendInvitation.setUpdateTime(LocalDateTime.now());
            memberSendInvitationRepository.save(sendInvitation);
        }
    }

    /**
     * 根据邀请码获取信息
     * @param headers  Http头部信息
     * @param invitationCodeVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberInvitationCodeQueryResp info(HttpHeaders headers, MemberInvitationCodeReq invitationCodeVO) {
        MemberSendInvitationDO sendInvitationDO = memberSendInvitationRepository.findByInvitationCode(invitationCodeVO.getInvitationCode());
        if (Objects.isNull(sendInvitationDO) || !InvitationCodeStateEnum.UNREGISTERED.getCode().equals(sendInvitationDO.getInvitationCodeState())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_CODE_STATE_IS_INVALID);
        }

        MemberRegisterConfigDO memberRegisterConfigDO = memberRegisterConfigRepository.findByTagEnumAndFieldTypeNotAndParentIdEquals(MemberConfigTagEnum.MEMBER_NAME.getCode(), MemberConfigFieldTypeEnum.LIST.getMessage(), 0L);
        MemberRoleDO subRole = memberRoleRepository.findById(sendInvitationDO.getSubRoleId()).orElse(new MemberRoleDO());

        MemberInvitationCodeQueryResp queryVO = new MemberInvitationCodeQueryResp();
        queryVO.setMemberType(subRole.getMemberType());
        queryVO.setRoleId(subRole.getId());
        queryVO.setInvitationCode(sendInvitationDO.getInvitationCode());
        queryVO.setEmail(sendInvitationDO.getEmail());
        queryVO.setMemberNameKey(memberRegisterConfigDO.getFieldName());
        queryVO.setMemberNameValue(sendInvitationDO.getSubMemberName());

        return queryVO;
    }

    /**
     * 邀请状态下拉查询
     * @return 查询结果
     */
    @Override
    public List<StatusResp> stateList() {
        return Stream.of(InvitationCodeStateEnum.values()).map(e -> {
            StatusResp statusResp = new StatusResp();
            statusResp.setCode(e.getCode());
            statusResp.setMessage(e.getName());
            return statusResp;
        }).collect(Collectors.toList());
    }

    @Override
    public void delete(HttpHeaders headers, MemberSendInvitationIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberSendInvitationDO sendInvitation = memberSendInvitationRepository.findById(idVO.getId()).orElse(null);
        if (sendInvitation == null || (!sendInvitation.getMemberId().equals(loginUser.getMemberId()) || !sendInvitation.getRoleId().equals(loginUser.getMemberRoleId()))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_NOT_EXISTS);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (Objects.isNull(sendInvitation.getSubRole().getRoleTag()) || !roleTag.equals(sendInvitation.getSubRole().getRoleTag()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_NOT_EXISTS);
        }

        if (!InvitationCodeStateEnum.UNSENT.getCode().equals(sendInvitation.getInvitationCodeState())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_CODE_STATE_IS_INVALID);
        }

        memberSendInvitationRepository.delete(sendInvitation);


    }
}
