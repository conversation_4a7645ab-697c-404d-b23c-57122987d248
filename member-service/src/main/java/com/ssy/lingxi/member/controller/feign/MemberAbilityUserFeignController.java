package com.ssy.lingxi.member.controller.feign;

import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberAbilityUserFeign;
import com.ssy.lingxi.member.api.model.req.ListQueryMemberUserReq;
import com.ssy.lingxi.member.api.model.resp.MemberUserFeignResp;
import com.ssy.lingxi.member.service.web.IMemberAbilityUserService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员用户
 * <AUTHOR>
 * @since 2022/7/18 11:07
 * @ignore 不需要提交到Yapi
 **/
@RestController
public class MemberAbilityUserFeignController implements IMemberAbilityUserFeign {

    @Resource
    private IMemberAbilityUserService memberAbilityUserService;

    /**
     * 查询会员用户
     * @return 操作结果
     */
    @Override
    public WrapperResp<List<MemberUserFeignResp>> listMemberUser(@RequestBody ListQueryMemberUserReq listQueryMemberUserReq) {
        return WrapperUtil.success(memberAbilityUserService.listMemberUser(listQueryMemberUserReq));
    }

    @Override
    public WrapperResp<List<MemberUserFeignResp>> getListByIds(CommonIdListReq idListReq) {
        return WrapperUtil.success(memberAbilityUserService.getListByIds(idListReq));
    }


}
