package com.ssy.lingxi.member.service.mobile;

import com.ssy.lingxi.member.entity.bo.login.MobileLoginBO;
import com.ssy.lingxi.member.model.dto.MobileLoginDTO;
import com.ssy.lingxi.member.model.dto.MobilePhoneLoginDTO;
import com.ssy.lingxi.member.model.req.login.MobileShopTypeReq;
import com.ssy.lingxi.member.model.req.login.MobileSwitchRoleReq;
import com.ssy.lingxi.member.model.req.login.PhoneLoginSmsCode;
import com.ssy.lingxi.member.model.req.mobile.MobileMemberAuthCodeReq;
import com.ssy.lingxi.member.model.req.mobile.MobileWxLoginReq;
import org.springframework.http.HttpHeaders;

/**
 * App - 用户登录接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-05
 */
public interface IMobileLoginService {

    /**
     * 账号或手机号登录
     * @param headers Http头部信息
     * @param loginReq 接口参数
     * @return 登录用户信息
     */
    MobileLoginBO accountOrPhoneLogin(HttpHeaders headers, MobileLoginDTO loginReq);


    /**
     * 发送“手机号登录”的短信验证码
     * @param headers Http头部信息
     * @param phoneReq 接口参数
     */
    void sendPhoneLoginSmsCode(HttpHeaders headers, PhoneLoginSmsCode phoneReq);

    /**
     * 手机号登录
     * @param headers Http头部信息
     * @param loginReq 接口参数
     * @return 登录用户信息
     */
    MobileLoginBO phoneLogin(HttpHeaders headers, MobilePhoneLoginDTO loginReq);

    /**
     * 重新获取登录用户信息
     * @param headers Http头部信息
     * @return 登录用户信息
     */
    MobileLoginBO loginWithToken(HttpHeaders headers, MobileShopTypeReq shopTypeReq);

    /**
     * 会员登录后，选择角色
     * @param headers Http头部信息
     * @param roleReq 接口参数
     * @return 登录用户信息
     */
    MobileLoginBO switchLoginRole(HttpHeaders headers, MobileSwitchRoleReq roleReq);

    /**
     * 业务平台 - 授权码确认登录
     * @param headers HttpHeaders信息
     * @param authCodeReq 接口参数
     */
    void authCodeActive(HttpHeaders headers, MobileMemberAuthCodeReq authCodeReq);

    /**
     * 微信登录
     *
     * @param headers Http头部信息
     * @param req     接口参数
     * @return 登录用户信息
     */
    MobileLoginBO wxLogin(HttpHeaders headers, MobileWxLoginReq req);

}
