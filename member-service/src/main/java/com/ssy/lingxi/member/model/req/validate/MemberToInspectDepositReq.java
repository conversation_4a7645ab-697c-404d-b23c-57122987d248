package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.member.handler.annotation.DateStringFormatAnnotation;
import com.ssy.lingxi.member.model.req.basic.FileUploadReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 会员“入库考察”审核接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberToInspectDepositReq extends MemberDepositReq implements Serializable {
    private static final long serialVersionUID = 8802055093172615779L;

    /**
     * 考察日期，格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation(message = "考察日期格式错误")
    private String inspectDay;

    /**
     * 考察评分
     */
    @NotNull(message = "考察评分最低分0分,最高分100分")
    @Min(value = 0, message = "考察评分最低分0分,最高分100分")
    @Max(value = 100, message = "考察评分最低分0分,最高分100分")
    private BigDecimal score;

    /**
     * 考察结果
     */
    @NotBlank(message = "考察结果不能为空")
    @Size(max = 60, message = "考察结果最长60个字符")
    private String result;

    /**
     * 考察报告
     */
    @NotEmpty(message = "考察报告不能为空")
    @Valid
    private List<FileUploadReq> reports;
}
