package com.ssy.lingxi.member.model.req.validate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 会员入库分类审核 - 主营品类接口参数 - 品类信息
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-16
 */
@Data
public class BusinessCategoryDetailReq implements Serializable {
    private static final long serialVersionUID = 5439043638808177464L;

    /**
     * 品类层级
     */
    private Integer level;

    /**
     * 品类Id
     */
    private Long categoryId;

    /**
     * 品类名称
     */
    private String name;

    /**
     * 品类父id
     */
    private Long parentId;

    private List<BusinessCategoryDetailReq> children;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BusinessCategoryDetailReq that = (BusinessCategoryDetailReq) o;
        return Objects.equals(categoryId, that.categoryId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(categoryId);
    }
}
