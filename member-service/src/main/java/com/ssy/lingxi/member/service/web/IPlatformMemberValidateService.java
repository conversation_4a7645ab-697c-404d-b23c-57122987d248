package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.model.req.validate.MemberValidateCommitReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateUpdateAuthReq;
import com.ssy.lingxi.member.model.req.validate.PlatformMemberValidateQueryDataReq;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberQuerySearchConditionResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformPageQueryMemberResp;
import com.ssy.lingxi.member.model.resp.validate.PlatformMemberValidateDetailResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 业务平台管理后台 - 会员审核接口类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
public interface IPlatformMemberValidateService {

    /**
     * 获取会员权限树
     * @param headers Http头部信息
     * @param validateReq 接口参数
     * @return 操作结果
     */
    AuthTreeResp getAuthTree(HttpHeaders headers, MemberValidateReq validateReq);

    /**
     * 修改审核会员的左侧菜单权限
     * @param headers Http头部信息
     * @param authVO 接口参数
     * @return 操作结果
     */
    void updateValidateAuth(HttpHeaders headers, MemberValidateUpdateAuthReq authVO);

    /**
     * 获取“待提交审核会员”页面中各个查询条件下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    PlatformMemberQuerySearchConditionResp getToBeCommitPageCondition(HttpHeaders headers);

    /**
     * 分页查询“待提交审核会员”列表
     * @param headers Http头部信息
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    PageDataResp<PlatformPageQueryMemberResp> pageToBeCommitMembers(HttpHeaders headers, PlatformMemberValidateQueryDataReq memberQueryVO);

    /**
     * 获取“提交审核会员”页面会员信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    PlatformMemberValidateDetailResp getToBeCommitMemberDetail(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 提交审核会员
     * @param headers HttpHeaders信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    void commitMemberValidate(HttpHeaders headers, MemberValidateCommitReq commitVO);

    /**
     * 批量提交审核会员
     * @param headers HttpHeaders信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    void batchCommitMemberValidate(HttpHeaders headers, List<MemberValidateReq> validateVOList);

    /**
     * 获取“待审核会员(一级)”页面中各个查询条件下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    PlatformMemberQuerySearchConditionResp getToBeValidateStep1PageCondition(HttpHeaders headers);

    /**
     * 分页查询“待审核会员(一级)”列表
     * @param headers Http头部信息
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    PageDataResp<PlatformPageQueryMemberResp> pageToBeValidateStep1Members(HttpHeaders headers, PlatformMemberValidateQueryDataReq memberQueryVO);

    /**
     * 获取“审核会员(一级)”页面会员信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    PlatformMemberValidateDetailResp getValidateStep1MemberDetail(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 批量审核会员(一级)
     * @param headers HttpHeaders信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    void batchValidateMemberStep1(HttpHeaders headers, List<MemberValidateReq> validateVOList);

    /**
     * 审核会员(一级)
     * @param headers HttpHeaders信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    void validateMemberStep1(HttpHeaders headers, MemberValidateCommitReq commitVO);

    /**
     * 获取“待审核会员(二级)”页面中各个查询条件下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    PlatformMemberQuerySearchConditionResp getToBeValidateStep2PageCondition(HttpHeaders headers);

    /**
     * 分页查询“待审核会员(二级)”列表
     * @param headers Http头部信息
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    PageDataResp<PlatformPageQueryMemberResp> pageToBeValidateStep2Members(HttpHeaders headers, PlatformMemberValidateQueryDataReq memberQueryVO);

    /**
     * 获取“审核会员(二级)”页面会员信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    PlatformMemberValidateDetailResp getValidateStep2MemberDetail(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 审核会员(二级)
     * @param headers HttpHeaders信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    void validateMemberStep2(HttpHeaders headers, MemberValidateCommitReq commitVO);

    /**
     * 批量审核会员(二级)
     * @param headers HttpHeaders信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    void batchValidateMemberStep2(HttpHeaders headers, List<MemberValidateReq> validateVOList);

    /**
     * 获取“待确认会员审核结果”页面中各个查询条件下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    PlatformMemberQuerySearchConditionResp getToBeConfirmPageCondition(HttpHeaders headers);

    /**
     * 分页查询“待确认会员审核结果”列表
     * @param headers Http头部信息
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    PageDataResp<PlatformPageQueryMemberResp> pageToBeConfirmMembers(HttpHeaders headers, PlatformMemberValidateQueryDataReq memberQueryVO);

    /**
     * 获取“确认会员审核结果”页面会员信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    PlatformMemberValidateDetailResp getConfirmValidateMemberDetail(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 确认会员审核结果
     * @param headers HttpHeaders信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    void confirmMemberValidate(HttpHeaders headers, MemberValidateCommitReq commitVO);

    /**
     * 批量确认会员审核结果
     * @param headers HttpHeaders信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    void batchConfirmMemberValidate(HttpHeaders headers, List<MemberValidateReq> validateVOList);

    /**
     * 根据平台会员审核的结果，更新会员能力关系的状态
     * @param platformRelation 平台会员关系
     * @return 操作结果
     */
    WrapperResp<Void> updateMemberRelationValidateStatus(MemberRelationDO platformRelation);
}
