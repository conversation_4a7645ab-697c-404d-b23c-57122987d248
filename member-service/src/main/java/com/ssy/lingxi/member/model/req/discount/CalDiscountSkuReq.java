package com.ssy.lingxi.member.model.req.discount;

import lombok.Data;

import java.io.Serializable;

/**
 * 工费优惠计算sku
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/5/29
 */
@Data
public class CalDiscountSkuReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品skuId
     */
    private Long skuId;


    /**
     * 单件商品码
     */
    private String singleCode;


}
