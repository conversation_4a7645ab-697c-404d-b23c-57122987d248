package com.ssy.lingxi.member.controller;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.SelectLongResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.service.ISelectService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 通用下拉框管理
 * 会员服务的所有下拉框，后面都会慢慢迁移到这个接口
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/13
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/select")
public class SelectController {
    private final ISelectService selectService;

    /**
     * 会员角色下拉框查询
     *
     * @return 查询结果
     */
    @GetMapping("/getMemberRoleList")
    public WrapperResp<List<SelectLongResp>> getMemberRoleList() {
        return WrapperUtil.success(selectService.getMemberRoleList());
    }
}
