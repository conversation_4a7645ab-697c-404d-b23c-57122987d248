package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.rectify.MemberRectifyInnerHistoryDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员整改内部单据流转历史记录Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Repository
public interface MemberRectifyInnerHistoryRepository extends JpaRepository<MemberRectifyInnerHistoryDO, Long>, JpaSpecificationExecutor<MemberRectifyInnerHistoryDO> {

    List<MemberRectifyInnerHistoryDO> findByRectifyId(Long rectifyId, Sort sort);

}
