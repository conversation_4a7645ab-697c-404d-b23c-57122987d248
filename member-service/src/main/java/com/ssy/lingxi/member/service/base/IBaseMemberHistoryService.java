package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.enums.MemberValidateHistoryOperationEnum;
import com.ssy.lingxi.member.model.resp.basic.MemberInnerHistoryResp;
import com.ssy.lingxi.member.model.resp.basic.MemberOuterHistoryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalHistoryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberChangeRequestFormHistoryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRectifyInnerHistoryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRectifyOuterHistoryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDepositDetailHistoryResp;

import java.util.List;

/**
 * 会员历史记录基础服务
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-20
 */
public interface IBaseMemberHistoryService {

    /**
     * 查询外部单据流转记录
     *
     * @param relationDO 会员关系
     * @param roleTag    角色标签
     * @return 查询结果
     */
    List<MemberOuterHistoryResp> listMemberOuterHistory(MemberRelationDO relationDO, Integer roleTag);

    /**
     * 查询外部单据流转记录
     *
     * @param memberId    上级会员Id
     * @param roleId      上级会角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param roleTag     角色标签
     * @return 查询结果
     */
    List<MemberOuterHistoryResp> listMemberOuterHistory(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Integer roleTag);

    /**
     * 查询内部单据流转记录
     *
     * @param relationDO 会员关系
     * @param roleTag    角色标签
     * @return 查询结果
     */
    List<MemberInnerHistoryResp> listMemberInnerHistory(MemberRelationDO relationDO, Integer roleTag);


    /**
     * 查询内部单据流转记录
     *
     * @param memberId    上级会员Id
     * @param roleId      上级会角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param roleTag     角色标签
     * @return 查询结果
     */
    List<MemberInnerHistoryResp> listMemberInnerHistory(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Integer roleTag);

    /**
     * 保存会员内部审核历史记录
     *
     * @param relationDO    会员关系
     * @param loginUser     登录用户
     * @param operationEnum 操作类型枚举
     * @param remark        备注信息
     */
    void saveMemberInnerHistory(MemberRelationDO relationDO, UserLoginCacheDTO loginUser, MemberValidateHistoryOperationEnum operationEnum, String remark);

    /**
     * 保存会员内部审核历史记录
     *
     * @param relationDO       会员关系
     * @param userId           用户Id
     * @param userName         用户姓名
     * @param userRoleName     用户角色名称
     * @param organizationName 用户组织机构名称
     * @param jobTitle         用户职位
     * @param operationEnum    操作类型枚举
     * @param remark           备注信息
     */
    void saveMemberInnerHistory(MemberRelationDO relationDO, Long userId, String userName, String userRoleName, String organizationName, String jobTitle, MemberValidateHistoryOperationEnum operationEnum, String remark);

    /**
     * 保存会员外部审核历史记录
     *
     * @param relationDO    会员关系
     * @param operationEnum 操作方法枚举
     * @param remark        备注信息
     */
    void saveMemberOuterHistory(MemberRelationDO relationDO, MemberValidateHistoryOperationEnum operationEnum, String remark);

    /**
     * 保存会员外部审核历史记录
     *
     * @param relationDO       会员关系
     * @param operatorRoleName 操作角色
     * @param operationEnum    操作方法枚举
     * @param outerStatusName  指定状态名称
     * @param remark           备注信息
     */
    void saveMemberOuterHistory(MemberRelationDO relationDO, String operatorRoleName, MemberValidateHistoryOperationEnum operationEnum, String outerStatusName, String remark);

    /**
     * 分页查询会员入库资料历史记录
     *
     * @param relationDO 会员关系
     * @param current    当前页
     * @param pageSize   每页行数
     * @return 查询结果
     */
    PageDataResp<MemberDepositDetailHistoryResp> pageMemberDepositDetailHistory(MemberRelationDO relationDO, int current, int pageSize);

    /**
     * 更改内部、外部历史记录的下级会员Id
     *
     * @param subMemberId      下级会员Id
     * @param oldSubRoleId     旧的下级会员角色Id
     * @param newSubRoleId     新的下级会员角色Id
     * @param newSubRoleName   新的下级会员角色名称
     * @param newSubMemberName 新的下级会员名称
     */
    void updateHistorySubRoleId(Long subMemberId, Long oldSubRoleId, Long newSubRoleId, String newSubRoleName, String newSubMemberName);

    /**
     * 平台删除会员时，删除下级会员的内、外历史记录
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     */
    void deleteHistoryBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    /**
     * 删除会员关系时，删除下级会员的内、外历史记录
     *
     * @param memberId    上级会员Id
     * @param roleId      上级会员角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     */
    void deleteHistoryByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);

    /**
     * 保存会员考评历史记录
     *
     * @param loginUser   登录用户
     * @param appraisalId 考评id
     * @param status      状态
     * @param statusName  状态名称
     * @param operation   操作说明
     * @param remark      备注信息
     */
    void saveMemberAppraisalHistory(UserLoginCacheDTO loginUser, Long appraisalId, Integer status, String statusName, String operation, String remark);

    /**
     * 查询会员考评单据流转记录
     *
     * @param appraisalId 考评id
     * @return 查询结果
     */
    List<MemberAppraisalHistoryResp> listMemberAppraisalHistory(Long appraisalId);

    /**
     * 保存会员整改外部历史记录
     *
     * @param loginUser   登录用户
     * @param rectifyId   整改id
     * @param subMemberId 下级会员id
     * @param subRoleId   下级会员角色id
     * @param outerStatus 状态
     * @param operation   操作说明
     * @param remark      备注信息
     */
    void saveMemberRectifyOuterHistory(UserLoginCacheDTO loginUser, Long rectifyId, Long subMemberId, Long subRoleId, Integer outerStatus, String operation, String remark);

    /**
     * 保存会员整改内部历史记录
     *
     * @param loginUser   登录用户
     * @param rectifyId   整改id
     * @param subMemberId 下级会员id
     * @param subRoleId   下级会员角色id
     * @param outerStatus 外部状态
     * @param operation   操作说明
     * @param remark      备注信息
     */
    void saveMemberRectifyInnerHistory(UserLoginCacheDTO loginUser, Long rectifyId, Long subMemberId, Long subRoleId, Integer outerStatus, String operation, String remark);

    /**
     * 查询会员整改外部单据流转记录
     *
     * @param rectifyId 整改id
     * @return 查询结果
     */
    List<MemberRectifyOuterHistoryResp> listMemberRectifyOuterHistory(Long rectifyId);

    /**
     * 查询会员整改内部单据流转记录
     *
     * @param rectifyId 整改id
     * @return 查询结果
     */
    List<MemberRectifyInnerHistoryResp> listMemberRectifyInnerHistory(Long rectifyId);

    /**
     * 查询会员变更申请单流转记录
     *
     * @param changeRequestFormId 变更申请单
     * @return 查询结果
     */
    List<MemberChangeRequestFormHistoryResp> listMemberChangeRequestFormHistory(Long changeRequestFormId);

    /**
     * 保存会员变更申请单历史记录
     *
     * @param loginUser   登录用户
     * @param appraisalId 考评id
     * @param status      状态
     * @param statusName  状态名称
     * @param operation   操作说明
     * @param remark      备注信息
     */
    void saveMemberChangeRequestFormHistory(UserLoginCacheDTO loginUser, Long appraisalId, Integer status, String statusName, String operation, String remark);

}
