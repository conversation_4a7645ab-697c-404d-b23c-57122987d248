package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigNameDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 3.0.0
 * @since 2024/5/13
 */
@Repository
public interface MemberRegisterConfigNameRepository extends JpaRepository<MemberRegisterConfigNameDO, Long>, JpaSpecificationExecutor<MemberRegisterConfigNameDO> {
    boolean existsByLanguageAndNameAndFieldTypeAndRegisterConfigParentIdAndMemberRegisterConfigIdNot(String language, String name, Integer fieldType, Long parentId, Long id);
    void deleteAllByMemberRegisterConfigId(Long registerConfigId);
}
