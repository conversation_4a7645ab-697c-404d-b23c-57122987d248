package com.ssy.lingxi.member.model.req.discount.web;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户工费优惠分页查询请求对象
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerProcessFeeDiscountPageReq extends PageDataReq {


    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 状态：0-停用，1-启用
     */
    private Integer status;

} 