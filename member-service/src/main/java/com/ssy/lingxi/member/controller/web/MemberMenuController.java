package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.MemberDetailReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterTypeMenuResp;
import com.ssy.lingxi.member.service.web.IMemberRegisterService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 企业商城门户 - 会员注册页面内容接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-05-30
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/menu/register")
public class MemberMenuController {

    @Resource
    private IMemberRegisterService memberRegisterService;

    /**
     * 注册页面 - 获取用户类型、业务类型
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/type")
    public WrapperResp<List<MemberRegisterTypeMenuResp>> getRegisterTypeMenu(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberRegisterService.getRegisterTypePageContent(headers));
    }

    /**
     * 注册页面 - 获取完善用户资料页面内容
     * @param headers Http头部信息
     * @param detailVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail")
    public WrapperResp<List<MemberConfigGroupResp>> getRegisterDetailMenu(@RequestHeader HttpHeaders headers, @Valid MemberDetailReq detailVO) {
        return WrapperUtil.success(memberRegisterService.getRegisterDetailPageContent(headers, detailVO));
    }
}
