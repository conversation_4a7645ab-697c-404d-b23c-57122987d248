package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.validate.MemberValidateCommitReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateUpdateAuthReq;
import com.ssy.lingxi.member.model.req.validate.PlatformMemberValidateQueryDataReq;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberQuerySearchConditionResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformPageQueryMemberResp;
import com.ssy.lingxi.member.model.resp.validate.PlatformMemberValidateDetailResp;
import com.ssy.lingxi.member.service.web.IPlatformMemberValidateService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 平台会员管理 - 会员审核接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/validate")
public class PlatformMemberValidateController {

    @Resource
    private IPlatformMemberValidateService memberVerifyService;

    /**
     * 获取“待提交审核会员”页面中各个查询条件下拉选择框的内容
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/commit/pageitems")
    public WrapperResp<PlatformMemberQuerySearchConditionResp> getToBeCommitPageCondition(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberVerifyService.getToBeCommitPageCondition(headers));
    }

    /**
     * 分页查询“待提交审核会员”列表
     * @param headers Http头部信息
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/commit/page")
    public WrapperResp<PageDataResp<PlatformPageQueryMemberResp>> pageToBeCommitMembers(@RequestHeader HttpHeaders headers, @Valid PlatformMemberValidateQueryDataReq memberQueryVO) {
        return WrapperUtil.success(memberVerifyService.pageToBeCommitMembers(headers, memberQueryVO));
    }

    /**
     * 获取“提交审核会员”页面会员信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/commit/detail")
    public WrapperResp<PlatformMemberValidateDetailResp> getToBeCommitMemberDetail(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberVerifyService.getToBeCommitMemberDetail(headers, validateVO));
    }

    /**
     * 获取会员权限树
     * @param headers Http头部信息
     * @param validateReq 接口参数
     * @return 操作结果
     */
    @GetMapping("/authTree")
    public WrapperResp<AuthTreeResp> getAuthTree(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateReq) {
        return WrapperUtil.success(memberVerifyService.getAuthTree(headers, validateReq));
    }

    /**
     * 修改审核会员的左侧菜单权限
     * @param headers Http头部信息
     * @param authVO 接口参数
     * @return 操作结果
     */
    @RequestMapping(value = "/commit/updateAuth")
    public WrapperResp<Void> updateValidateAuth(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateUpdateAuthReq authVO) {
         memberVerifyService.updateValidateAuth(headers, authVO);
        return WrapperUtil.success();
    }

    /**
     * 提交审核会员
     * @param headers Http头部信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/commit/submit")
    public WrapperResp<Void> commitMemberValidate(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateCommitReq commitVO) {
         memberVerifyService.commitMemberValidate(headers, commitVO);
        return WrapperUtil.success();
    }

    /**
     * 批量提交审核会员
     * @param headers Http头部信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    @RequestMapping("/commit/batch")
    public WrapperResp<Void> batchCommitMemberValidate(@RequestHeader HttpHeaders headers, @RequestBody @Valid List<MemberValidateReq> validateVOList) {
         memberVerifyService.batchCommitMemberValidate(headers, validateVOList);
        return WrapperUtil.success();
    }


    /**
     * 获取“待审核会员(一级)”页面中各个查询条件下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/step1/pageitems")
    public WrapperResp<PlatformMemberQuerySearchConditionResp> getToBeValidateStep1PageCondition(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberVerifyService.getToBeValidateStep1PageCondition(headers));
    }

    /**
     * 分页查询“待审核会员(一级)”列表
     * @param headers Http头部信息
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/step1/page")
    public WrapperResp<PageDataResp<PlatformPageQueryMemberResp>> pageToBeValidateStep1Members(@RequestHeader HttpHeaders headers, @Valid PlatformMemberValidateQueryDataReq memberQueryVO) {
        return WrapperUtil.success(memberVerifyService.pageToBeValidateStep1Members(headers, memberQueryVO));
    }

    /**
     * 获取“审核会员(一级)”页面会员信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/step1/detail")
    public WrapperResp<PlatformMemberValidateDetailResp> getValidateStep1MemberDetail(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberVerifyService.getValidateStep1MemberDetail(headers, validateVO));
    }

    /**
     * 审核会员(一级)
     * @param headers Http头部信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/step1/submit")
    public WrapperResp<Void> validateMemberStep1(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateCommitReq commitVO) {
         memberVerifyService.validateMemberStep1(headers, commitVO);
        return WrapperUtil.success();
    }

    /**
     * 批量审核会员(一级)
     * @param headers Http头部信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    @RequestMapping("/step1/batch")
    public WrapperResp<Void> batchValidateMemberStep1(@RequestHeader HttpHeaders headers, @RequestBody @Valid List<MemberValidateReq> validateVOList) {
         memberVerifyService.batchValidateMemberStep1(headers, validateVOList);
        return WrapperUtil.success();
    }

    /**
     * 获取“待审核会员(二级)”页面中各个查询条件下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/step2/pageitems")
    public WrapperResp<PlatformMemberQuerySearchConditionResp> getToBeValidateStep2PageCondition(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberVerifyService.getToBeValidateStep2PageCondition(headers));
    }

    /**
     * 分页查询“待审核会员(二级)”列表
     * @param headers Http头部信息
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/step2/page")
    public WrapperResp<PageDataResp<PlatformPageQueryMemberResp>> pageToBeValidateStep2Members(@RequestHeader HttpHeaders headers, @Valid PlatformMemberValidateQueryDataReq memberQueryVO) {
        return WrapperUtil.success(memberVerifyService.pageToBeValidateStep2Members(headers, memberQueryVO));
    }

    /**
     * 获取“审核会员(二级)”页面会员信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/step2/detail")
    public WrapperResp<PlatformMemberValidateDetailResp> getValidateStep2MemberDetail(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberVerifyService.getValidateStep2MemberDetail(headers, validateVO));
    }

    /**
     * 审核会员(二级)
     * @param headers Http头部信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    @RequestMapping(value = "/step2/submit")
    public WrapperResp<Void> validateMemberStep2(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateCommitReq commitVO) {
         memberVerifyService.validateMemberStep2(headers, commitVO);
        return WrapperUtil.success();
    }

    /**
     * 批量审核会员(二级)
     * @param headers Http头部信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    @RequestMapping(value = "/step2/batch")
    public WrapperResp<Void> batchValidateMemberStep2(@RequestHeader HttpHeaders headers, @RequestBody @Valid List<MemberValidateReq> validateVOList) {
         memberVerifyService.batchValidateMemberStep2(headers, validateVOList);
        return WrapperUtil.success();
    }

    /**
     * 获取“待确认会员审核结果”页面中各个查询条件下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/confirm/pageitems")
    public WrapperResp<PlatformMemberQuerySearchConditionResp> getToBeConfirmPageCondition(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberVerifyService.getToBeConfirmPageCondition(headers));
    }

    /**
     * 分页查询“待确认会员审核结果”列表
     * @param headers Http头部信息
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/confirm/page")
    public WrapperResp<PageDataResp<PlatformPageQueryMemberResp>> pageToBeConfirmMembers(@RequestHeader HttpHeaders headers, @Valid PlatformMemberValidateQueryDataReq memberQueryVO) {
        return WrapperUtil.success(memberVerifyService.pageToBeConfirmMembers(headers, memberQueryVO));
    }


    /**
     * 获取“确认会员审核结果”页面会员信息
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/confirm/detail")
    public WrapperResp<PlatformMemberValidateDetailResp> getConfirmValidateMemberDetail(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberVerifyService.getConfirmValidateMemberDetail(headers, validateVO));
    }

    /**
     * 确认会员审核结果
     * @param headers Http头部信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    @RequestMapping(value = "/confirm/submit")
    public WrapperResp<Void> confirmMemberValidate(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateCommitReq commitVO) {
         memberVerifyService.confirmMemberValidate(headers, commitVO);
        return WrapperUtil.success();
    }

    /**
     * 批量确认会员审核结果
     * @param headers Http头部信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    @RequestMapping(value = "/confirm/batch")
    public WrapperResp<Void> batchConfirmMemberValidate(@RequestHeader HttpHeaders headers, @RequestBody @Valid List<MemberValidateReq> validateVOList) {
         memberVerifyService.batchConfirmMemberValidate(headers, validateVOList);
        return WrapperUtil.success();
    }
}
