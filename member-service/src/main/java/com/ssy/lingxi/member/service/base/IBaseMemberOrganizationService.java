package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrgTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrganizationQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberSelectOrgQueryResp;

import java.util.List;

/**
 * 会员组织机构服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
public interface IBaseMemberOrganizationService {

    /**
     * 新增会员组织架构菜单
     * @param memberId 会员Id
     * @param addVO    接口参数
     * @return 操作结果
     */
    void addMemberOrg(Long memberId, MemberOrganizationAddReq addVO);

    /**
     * 根据菜单Id，更新组织机构信息
     * @param memberId 会员Id
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateMemberOrg(Long memberId, MemberOrganizationUpdateReq updateVO);

    /**
     * 删除一个会员组织架构
     * @param memberId 会员Id
     * @param deleteVO 接口参数
     * @return 操作结果
     */
    void deleteMemberOrg(Long memberId, MemberOrganizationDeleteReq deleteVO);

    /**
     * 查询一个会员组织架构
     * @param memberId 会员Id
     * @param getVO    接口参数
     * @return 操作结果
     */
    MemberOrganizationQueryResp getMemberOrg(Long memberId, MemberOrganizationGetReq getVO);

    /**
     * 查询所有组织机构信息，以非树形菜单的形式返回
     * 返回组织机构和该组织机构的上一级组织机构信息
     * @param memberId 会员Id
     * @param getVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberSelectOrgQueryResp> selectOrg(Long memberId, MemberSelectOrgGetDataReq getVO);

    /**
     * 查询会员的所有组织架构，以树形菜单的形式返回
     * @param memberId 会员Id
     * @return 操作结果
     */
    List<MemberOrgTreeResp> treeMemberOrg(Long memberId);

    /**
     * 查询会员的（非门店）所有组织架构，以树形菜单的形式返回
     * @param memberId 会员Id
     * @return 查询结果
     */
    List<MemberOrgTreeResp> nonStoreTreeMemberOrg(Long memberId);
}
