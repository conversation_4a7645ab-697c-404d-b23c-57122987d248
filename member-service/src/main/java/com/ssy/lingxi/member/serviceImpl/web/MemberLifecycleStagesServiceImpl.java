package com.ssy.lingxi.member.serviceImpl.web;


import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.enums.MemberCycleProcessTypeEnum;
import com.ssy.lingxi.member.api.model.resp.MemberLifeCycleRuleConfigResp;
import com.ssy.lingxi.member.api.model.resp.MemberLifecycleRuleQueryResp;
import com.ssy.lingxi.member.api.model.resp.MemberLifecycleStagesAndRuleResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberAfterApprovalLifecycleStagesDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberChangeRequestFormDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifeCycleRuleConfigDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifecycleStagesDO;
import com.ssy.lingxi.member.enums.MemberChangeRequestFormStatusEnum;
import com.ssy.lingxi.member.enums.MemberLifecycleStagesRuleEnum;
import com.ssy.lingxi.member.model.req.comment.MemberLifeCycleCheckOrderPermissionReq;
import com.ssy.lingxi.member.model.req.comment.MemberLifecycleRuleReq;
import com.ssy.lingxi.member.model.req.comment.MemberLifecycleStagesAndRuleReq;
import com.ssy.lingxi.member.model.resp.comment.MemberLifecycleStagesResp;
import com.ssy.lingxi.member.repository.MemberAfterApprovalLifecycleStagesRepository;
import com.ssy.lingxi.member.repository.MemberLifeCycleRuleConfigRepository;
import com.ssy.lingxi.member.repository.MemberLifecycleStagesRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberLifecycleRuleService;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员能力-系统管理-生命周期规则配置相关接口基础实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 */
@Service
public class MemberLifecycleStagesServiceImpl implements IMemberLifecycleRuleService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberLifecycleStagesRepository memberLifecycleStagesRepository;

    @Resource
    private MemberAfterApprovalLifecycleStagesRepository memberAfterApprovalLifecycleStagesRepository;

    @Resource
    private MemberLifeCycleRuleConfigRepository memberLifeCycleRuleConfigRepository;

    @Resource
    private MemberRelationRepository memberRelationRepository;

    /**
     * 保存指定会员、角色标签生命周期规则---生命周期阶段、生命周期阶段规则、入库审核后生命周期
     * @param headers 请求头部信息
     * @param memberLifecycleRuleReq 请求参数vo
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    @Transactional
    public void addLifecycleRules(HttpHeaders headers, MemberLifecycleRuleReq memberLifecycleRuleReq, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        //先拿到后台该会员的旧数据 生命周期阶段id
        List<MemberLifecycleStagesDO> oldMemberLifecycleStagesDOS = memberLifecycleStagesRepository.findByMemberIdAndRoleIdAndRoleTag(loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);

        //得到每个生命周期阶段的  MemberRelationDO  set   用于校验是否能删除
        Set<MemberRelationDO> MemberRelationDOS = oldMemberLifecycleStagesDOS.stream().flatMap(memberLifecycleStagesDO -> memberLifecycleStagesDO.getRelationSet().stream()).collect(Collectors.toSet());

        //得到生命周期阶段配置表  目标生命周期  用于校验是否能删除
        ArrayList<Integer> memberChangeRequestFormStatusEnumCode = new ArrayList<>();
        memberChangeRequestFormStatusEnumCode.add(MemberChangeRequestFormStatusEnum.WAIT_SUBMIT_REJECT.getCode());
        memberChangeRequestFormStatusEnumCode.add(MemberChangeRequestFormStatusEnum.WAIT_CONFIRMED_PASSED.getCode());
        memberChangeRequestFormStatusEnumCode.add(MemberChangeRequestFormStatusEnum.WAIT_CONFIRMED_REJECT.getCode());
        Set<MemberChangeRequestFormDO> memberChangeRequestFormDOS = oldMemberLifecycleStagesDOS.stream().flatMap(
                memberLifecycleStagesDO -> memberLifecycleStagesDO.getMemberChangeRequestFormSet().stream()).filter(
                memberChangeRequestFormDO -> memberChangeRequestFormStatusEnumCode.contains(memberChangeRequestFormDO.getStatus()))
                .collect(Collectors.toSet());

        //如果保存的生命周期为空，并且删除的生命周期阶段都没被使用，则是删除
        if (ObjectUtils.isEmpty(memberLifecycleRuleReq.getLifecycle()) && ObjectUtils.isEmpty(MemberRelationDOS) && ObjectUtils.isEmpty(memberChangeRequestFormDOS)) {
            memberLifecycleStagesRepository.deleteByMemberIdAndRoleIdAndRoleTag(loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);
            memberAfterApprovalLifecycleStagesRepository.deleteByMemberIdAndRoleIdAndRoleTag(loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);
            return ;
        }

        //根据新的生命周期id和旧的生命周期id做对比
        Set<Long> oldLifecycleIdS = oldMemberLifecycleStagesDOS.stream().map(MemberLifecycleStagesDO::getId).collect(Collectors.toSet());

        //vo前端传进来的生命周期阶段id
        Set<Long> newLifecycleIdS = memberLifecycleRuleReq.getLifecycle().stream().map(MemberLifecycleStagesAndRuleReq::getLifecycleStagesId).collect(Collectors.toSet());

        //交集 vo的id和do的id一样的   用于更新
        Set<Long> intersectionLifecycleIdS = oldLifecycleIdS.stream().filter(newLifecycleIdS::contains).collect(Collectors.toSet());

        //差集 do-vo   do里面有的vo里面没有的  用于删除
        Set<Long> differenceLifecycleIdS = oldLifecycleIdS.stream().filter(item -> !newLifecycleIdS.contains(item)).collect(Collectors.toSet());

        //用于存放新的生命周期阶段list集合
        Set<MemberLifecycleStagesDO> newMemberLifecycleStagesDOS = new HashSet<>();

        //用于存放新的生命周期规则配置set集合对象
        HashSet<MemberLifeCycleRuleConfigDO> memberLifeCycleRuleConfigDOS = new HashSet<>();

        for (MemberLifecycleStagesAndRuleReq memberLifecycleStagesAndRuleReq : memberLifecycleRuleReq.getLifecycle()) {
            //新增 筛选出vo对象里面生命周期阶段id为null的 就是新增
            if (ObjectUtils.isEmpty(memberLifecycleStagesAndRuleReq.getLifecycleStagesId())) {
                MemberLifecycleStagesDO memberLifecycleStagesDO = new MemberLifecycleStagesDO();
                memberLifecycleStagesDO.setMemberId(loginUser.getMemberId());
                memberLifecycleStagesDO.setRoleId(loginUser.getMemberRoleId());
                memberLifecycleStagesDO.setRoleTag(roleTag);
                memberLifecycleStagesDO.setLifecycleStagesNum(memberLifecycleStagesAndRuleReq.getLifecycleStagesNum());
                memberLifecycleStagesDO.setLifecycleStagesName(memberLifecycleStagesAndRuleReq.getLifecycleStagesName());
                memberLifecycleStagesDO.setCreateTime(DateTimeUtil.parseDateTime(DateTimeUtil.formatDateTime(LocalDateTime.now())));
                for (Long lifeCycleRuleId : memberLifecycleStagesAndRuleReq.getLifecycleStagesRuleIds()) {
                    MemberLifeCycleRuleConfigDO memberLifeCycleRuleConfigDO = new MemberLifeCycleRuleConfigDO();
                    memberLifeCycleRuleConfigDO.setId(lifeCycleRuleId);
                    memberLifecycleStagesDO.setMemberLifeCycleRuleConfigDOSet(memberLifeCycleRuleConfigDOS);
                    memberLifecycleStagesDO.getMemberLifeCycleRuleConfigDOSet().add(memberLifeCycleRuleConfigDO);
                }
                newMemberLifecycleStagesDOS.add(memberLifecycleStagesDO);
            }
        }

        for (MemberLifecycleStagesDO oldMemberLifecycleStagesDO : oldMemberLifecycleStagesDOS) {
            if (differenceLifecycleIdS.contains(oldMemberLifecycleStagesDO.getId())) {
                //删除旧的不在使用的生命周期阶段，如果有关联则抛异常不能删除在使用的生命周期阶段
                Set<MemberChangeRequestFormDO> requestFormSet = oldMemberLifecycleStagesDO.getMemberChangeRequestFormSet();
                List<Integer> statusList = requestFormSet.stream().map(MemberChangeRequestFormDO::getStatus).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(oldMemberLifecycleStagesDO.getRelationSet()) &&(CollectionUtils.isEmpty(statusList) || CollectionUtils.containsAny(statusList,memberChangeRequestFormStatusEnumCode))){
                    memberLifecycleStagesRepository.deleteById(oldMemberLifecycleStagesDO.getId());
                }else {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_LIFECYCLE_IN_USE);
                }
            } else if (intersectionLifecycleIdS.contains(oldMemberLifecycleStagesDO.getId())) {
                //如果旧的生命周期规则中id再交集里面  那么就是更新
                //先把中间表的原先的关联关系删除掉
                oldMemberLifecycleStagesDO.getMemberLifeCycleRuleConfigDOSet().removeAll(oldMemberLifecycleStagesDO.getMemberLifeCycleRuleConfigDOSet());
                for (MemberLifecycleStagesAndRuleReq memberLifecycleStagesAndRuleReq : memberLifecycleRuleReq.getLifecycle()) {
                    if (Objects.equals(memberLifecycleStagesAndRuleReq.getLifecycleStagesId(),oldMemberLifecycleStagesDO.getId())) {
                        oldMemberLifecycleStagesDO.setMemberId(loginUser.getMemberId());
                        oldMemberLifecycleStagesDO.setRoleId(loginUser.getMemberRoleId());
                        oldMemberLifecycleStagesDO.setRoleTag(roleTag);
                        oldMemberLifecycleStagesDO.setLifecycleStagesNum(memberLifecycleStagesAndRuleReq.getLifecycleStagesNum());
                        oldMemberLifecycleStagesDO.setLifecycleStagesName(memberLifecycleStagesAndRuleReq.getLifecycleStagesName());
                        oldMemberLifecycleStagesDO.setCreateTime(DateTimeUtil.parseDateTime(DateTimeUtil.formatDateTime(LocalDateTime.now())));
                        for (Long lifeCycleRuleId : memberLifecycleStagesAndRuleReq.getLifecycleStagesRuleIds()) {
                            MemberLifeCycleRuleConfigDO memberLifeCycleRuleConfigDO = new MemberLifeCycleRuleConfigDO();
                            memberLifeCycleRuleConfigDO.setId(lifeCycleRuleId);
                            //加给对应的生命周期阶段。
                            //if (Objects.equals(memberLifecycleStagesAndRuleVO.getLifecycleStagesId(), oldMemberLifecycleStagesDO.getId())) {
                                oldMemberLifecycleStagesDO.getMemberLifeCycleRuleConfigDOSet().add(memberLifeCycleRuleConfigDO);
                            //}
                        }
                        newMemberLifecycleStagesDOS.add(oldMemberLifecycleStagesDO);
                    }
                }
            }
        }

        //将该会员的最新的生命周期规则保存入库
        newMemberLifecycleStagesDOS.forEach(memberLifecycleStagesDO -> memberLifecycleStagesRepository.saveAndFlush(memberLifecycleStagesDO));

        //保存入库审核通过后生命周期规则相关
        //如果入库审核后生命周期阶段不传则为空，需要删除
        if (ObjectUtils.isEmpty(memberLifecycleRuleReq.getAfterApprovalLifecycleStagesNum())) {
            memberAfterApprovalLifecycleStagesRepository.deleteByMemberIdAndRoleIdAndRoleTag(loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);
            return ;
        }

        //校验，如果有id的根据id去生命周期阶段查询保存对应的序号。没有则保存传过来的序号
        if (!ObjectUtils.isEmpty(memberLifecycleRuleReq.getAfterApprovalLifecycleStagesId())) {
            MemberLifecycleStagesAndRuleReq lifecycleStagesAndRuleVO = memberLifecycleRuleReq.getLifecycle().stream().filter(memberLifecycleStagesAndRuleReq ->
                    Objects.equals(memberLifecycleStagesAndRuleReq.getLifecycleStagesId(), memberLifecycleRuleReq.getAfterApprovalLifecycleStagesId())).findFirst().orElse(null);
            if (lifecycleStagesAndRuleVO != null) {
                memberLifecycleRuleReq.setAfterApprovalLifecycleStagesNum(lifecycleStagesAndRuleVO.getLifecycleStagesNum());
            }
        }

        MemberAfterApprovalLifecycleStagesDO oldAfterApprovalLifecycleStagesDO = memberAfterApprovalLifecycleStagesRepository.findByMemberIdAndAndRoleIdAndRoleTag(loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);
        MemberAfterApprovalLifecycleStagesDO newAfterApprovalLifecycleStagesDO = new MemberAfterApprovalLifecycleStagesDO();
        //如果为空说明原先没有，就是保存操作。id为空    有就是更新
        newAfterApprovalLifecycleStagesDO.setId(ObjectUtils.isEmpty(oldAfterApprovalLifecycleStagesDO) ? null : oldAfterApprovalLifecycleStagesDO.getId());
        newAfterApprovalLifecycleStagesDO.setMemberId(loginUser.getMemberId());
        newAfterApprovalLifecycleStagesDO.setRoleId(loginUser.getMemberRoleId());
        newAfterApprovalLifecycleStagesDO.setRoleTag(roleTag);
        newAfterApprovalLifecycleStagesDO.setLifecycleStagesId(memberLifecycleRuleReq.getAfterApprovalLifecycleStagesId());
        newAfterApprovalLifecycleStagesDO.setLifecycleStagesNum(memberLifecycleRuleReq.getAfterApprovalLifecycleStagesNum());
        memberAfterApprovalLifecycleStagesRepository.saveAndFlush(newAfterApprovalLifecycleStagesDO);

    }

    /**
     * 查询指定会员、角色标签的生命周期规则
     * @param headers 请求头部信息
     * @param roleTag 角色标签
     * @return 生命周期规则以及入库后的生命周期规则
     */
    @Override
    public MemberLifecycleRuleQueryResp getLifecycleRules(HttpHeaders headers, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberLifecycleRuleQueryResp queryVO = new MemberLifecycleRuleQueryResp();
        //拿到生命周期阶段以及规则
        List<MemberLifecycleStagesDO> memberLifecycleStagesDOS = memberLifecycleStagesRepository.findByMemberIdAndRoleIdAndRoleTag(loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);
        //memberChangeRequestFormSet 不为空的时候，以下几种状态也是为无关联的，因为单据流程已经审核完了
        ArrayList<Integer> memberChangeRequestFormStatusEnumCode = new ArrayList<>();
        memberChangeRequestFormStatusEnumCode.add(MemberChangeRequestFormStatusEnum.WAIT_SUBMIT_REJECT.getCode());
        memberChangeRequestFormStatusEnumCode.add(MemberChangeRequestFormStatusEnum.WAIT_CONFIRMED_PASSED.getCode());
        memberChangeRequestFormStatusEnumCode.add(MemberChangeRequestFormStatusEnum.WAIT_CONFIRMED_REJECT.getCode());

        ArrayList<MemberLifecycleStagesAndRuleResp> memberLifecycleStagesAndRuleVOS = new ArrayList<>();
        for (MemberLifecycleStagesDO memberLifecycleStagesDO : memberLifecycleStagesDOS) {
            MemberLifecycleStagesAndRuleResp lifecycleStagesAndRuleQueryVO = new MemberLifecycleStagesAndRuleResp();
            lifecycleStagesAndRuleQueryVO.setLifecycleStagesId(memberLifecycleStagesDO.getId());
            lifecycleStagesAndRuleQueryVO.setLifecycleStagesNum(memberLifecycleStagesDO.getLifecycleStagesNum());
            lifecycleStagesAndRuleQueryVO.setLifecycleStagesName(memberLifecycleStagesDO.getLifecycleStagesName());
            //得到对应的生命周期规则
            lifecycleStagesAndRuleQueryVO.setLifecycleStagesRuleIds(memberLifecycleStagesDO.getMemberLifeCycleRuleConfigDOSet().stream().map(t -> t.getId()).collect(Collectors.toList()));
            //lifecycleStagesAndRuleQueryVO.setLifecycleStagesRuleIds(memberLifecycleStagesDO.getLifecycleStagesRuleIds());
            //查询是否关联返回给前端
            lifecycleStagesAndRuleQueryVO.setRelevance(CommonBooleanEnum.YES.getCode());
            Set<MemberChangeRequestFormDO> requestFormSet = memberLifecycleStagesDO.getMemberChangeRequestFormSet();
            List<Integer> statusList = requestFormSet.stream().map(MemberChangeRequestFormDO::getStatus).collect(Collectors.toList());
            if ((CollectionUtils.isEmpty(memberLifecycleStagesDO.getRelationSet()))
                    && (CollectionUtils.isEmpty(statusList) || CollectionUtils.containsAny(statusList,memberChangeRequestFormStatusEnumCode))){
                lifecycleStagesAndRuleQueryVO.setRelevance(CommonBooleanEnum.NO.getCode());
            }
            memberLifecycleStagesAndRuleVOS.add(lifecycleStagesAndRuleQueryVO);
        }
        //返回按照lifecycleStagesNum顺序的数据
        queryVO.setLifecycle(memberLifecycleStagesAndRuleVOS.stream().sorted(Comparator.comparing(MemberLifecycleStagesAndRuleResp::getLifecycleStagesNum)).collect(Collectors.toList()));

        //拿到入库审核通过后的生命周期阶段
        MemberAfterApprovalLifecycleStagesDO memberAfterApprovalLifecycleStagesDO = memberAfterApprovalLifecycleStagesRepository.findByMemberIdAndAndRoleIdAndRoleTag(loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);
        if (null != memberAfterApprovalLifecycleStagesDO) {
            queryVO.setAfterApprovalLifecycleStagesId(memberAfterApprovalLifecycleStagesDO.getLifecycleStagesId());
            queryVO.setAfterApprovalLifecycleStagesNum(memberAfterApprovalLifecycleStagesDO.getLifecycleStagesNum());
        }

        //拿到生命周期阶段规则配置表数据
        List<MemberLifeCycleRuleConfigDO> memberLifeCycleRuleConfigRepositoryAll = memberLifeCycleRuleConfigRepository.findByRoleTag(roleTag);
        List<MemberLifeCycleRuleConfigResp> memberLifeCycleRuleConfigResps = memberLifeCycleRuleConfigRepositoryAll.stream().map(memberLifeCycleRuleConfigDO -> {
            MemberLifeCycleRuleConfigResp memberLifeCycleRuleConfigResp = new MemberLifeCycleRuleConfigResp();
            memberLifeCycleRuleConfigResp.setLifeCycleRuleId(memberLifeCycleRuleConfigDO.getId());
            memberLifeCycleRuleConfigResp.setLifeCycleRuleEnum(memberLifeCycleRuleConfigDO.getLifeCycleRuleEnum());
            memberLifeCycleRuleConfigResp.setLifeCycleRuleName(MemberLifecycleStagesRuleEnum.getName(memberLifeCycleRuleConfigDO.getLifeCycleRuleEnum()));
            memberLifeCycleRuleConfigResp.setRoleTag(memberLifeCycleRuleConfigDO.getRoleTag());
            return memberLifeCycleRuleConfigResp;
        }).collect(Collectors.toList());
        queryVO.setMemberLifeCycleRuleConfigDOList(memberLifeCycleRuleConfigResps);
        return queryVO;
    }

    /**
     * 当上级配置生命周期时，校验下级会员是否有下单权限
     *
     * @param loginCacheDTO 当前登录人
     * @param memberLifeCycleCheckOrderPermission 请求参数
     * @return 返回
     */
    @Override
    public Boolean checkSubMemberOrderPermission(UserLoginCacheDTO loginCacheDTO, MemberLifeCycleCheckOrderPermissionReq memberLifeCycleCheckOrderPermission) {
        List<MemberLifecycleStagesDO> memberLifecycleStagesDOList = memberLifecycleStagesRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            List<Predicate> orList = new ArrayList<>();
            memberLifeCycleCheckOrderPermission.getMemberList().forEach(member -> {
                orList.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("memberId").as(Long.class), member.getMemberId()),
                        criteriaBuilder.equal(root.get("roleId").as(Long.class), member.getRoleId()),
                        criteriaBuilder.equal(root.get("roleTag").as(Integer.class), loginCacheDTO.getRoleTag())));
            });

            Predicate[] orPredicate = new Predicate[orList.size()];
            list.add(criteriaBuilder.or(orList.toArray(orPredicate)));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        });

        if (CollectionUtils.isEmpty(memberLifecycleStagesDOList)) {
            // 如果上级都没配置生命周期，下级会员是有下单权限
            return true;
        }

        List<MemberRelationDO> memberRelationList = memberRelationRepository.findAll(getSpecification(memberLifeCycleCheckOrderPermission, loginCacheDTO));

        // 如有一个上级没配置下单权限，则下级没有下单权限
        List<Long> memberLifecycleStageIds = memberRelationList.stream()
                .filter(memberRelationDO -> Objects.nonNull(memberRelationDO.getMemberLifecycleStages()))
                .map(memberRelationDO -> memberRelationDO.getMemberLifecycleStages().getId())
                .collect(Collectors.toList());

        if (memberLifecycleStageIds.size() != memberLifeCycleCheckOrderPermission.getMemberList().size()) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CURRENT_LIFE_CYCLE_STAGE_IS_NOT_ALLOWED_TO_SUBMIT_ORDER);
        }

        return true;
    }

    /**
     * 构造条件
     */
    private Specification<MemberRelationDO> getSpecification(MemberLifeCycleCheckOrderPermissionReq memberLifeCycleCheckOrderPermission, UserLoginCacheDTO loginCacheDTO) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginCacheDTO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginCacheDTO.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), loginCacheDTO.getRoleTag()));


            Join<Object, Object> memberLifecycleStageJoin = root.join("memberLifecycleStages", JoinType.LEFT);
            Join<Object, Object> memberLifeCycleRuleConfigJoin = memberLifecycleStageJoin.join("memberLifeCycleRuleConfigDOSet", JoinType.LEFT);
            list.add(criteriaBuilder.equal(memberLifeCycleRuleConfigJoin.get("lifeCycleRuleEnum").as(Integer.class), MemberLifecycleStagesRuleEnum.CUSTOMER_ALLOW_ORDER_CREATION.getCode()));


            List<Predicate> orList = new ArrayList<>();
            memberLifeCycleCheckOrderPermission.getMemberList().forEach(member -> {
                orList.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), member.getMemberId()));
                orList.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), member.getRoleId()));
            });

            Predicate[] orPredicate = new Predicate[orList.size()];
            list.add(criteriaBuilder.or(orList.toArray(orPredicate)));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
    }

    /**
     * 查询指定会员、指定角色标签的生命周期阶段
     * @param headers 请求头部信息
     * @param processType 下级会员角色标签
     * @return 查询结果
     */
    @Override
    public List<MemberLifecycleStagesResp> getLifecycleStages(HttpHeaders headers, Integer processType) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        //查看标识，因为流程引擎配置枚举与RoleTagEnum枚举不一致。所以需要转换一下
        if (Objects.equals(processType, MemberCycleProcessTypeEnum.CUSTOMER.getCode())) {
            //取客户的
            processType = RoleTagEnum.CUSTOMER.getCode();
        }else if (Objects.equals(processType , MemberCycleProcessTypeEnum.SUPPLIER.getCode())) {
            //取供应商的
            processType = RoleTagEnum.SUPPLIER.getCode();
        }

        List<MemberLifecycleStagesDO> lifecycleStagesDOS = memberLifecycleStagesRepository.findByMemberIdAndRoleIdAndRoleTag(loginUser.getMemberId(), loginUser.getMemberRoleId(), processType);

        List<MemberLifecycleStagesResp> lifecycleStagesQueryVOList = lifecycleStagesDOS.stream().map(memberLifecycleStagesDO -> {
            MemberLifecycleStagesResp memberLifecycleStagesResp = new MemberLifecycleStagesResp();
            memberLifecycleStagesResp.setLifecycleStagesId(memberLifecycleStagesDO.getId());
            memberLifecycleStagesResp.setLifecycleStagesNum(memberLifecycleStagesDO.getLifecycleStagesNum());
            memberLifecycleStagesResp.setLifecycleStagesName(memberLifecycleStagesDO.getLifecycleStagesName());
            return memberLifecycleStagesResp;
        }).collect(Collectors.toList());

        return lifecycleStagesQueryVOList;
    }
}
