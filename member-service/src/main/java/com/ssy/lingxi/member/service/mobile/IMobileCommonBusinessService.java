package com.ssy.lingxi.member.service.mobile;

import com.ssy.lingxi.member.model.req.basic.MemberLogoReq;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.resp.basic.MobileRegisterTagResp;
import com.ssy.lingxi.member.model.resp.basic.UserDetailResp;
import org.springframework.http.HttpHeaders;

/**
 * App - 会员其他业务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-02-03
 */
public interface IMobileCommonBusinessService {

    /**
     * 新增或修改用户Logo
     * @param headers Http头部信息
     * @param logoVO 接口参数
     */
    void addMemberUserLogo(HttpHeaders headers, MemberLogoReq logoVO);

    /**
     * 查询会员标签注册资料
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MobileRegisterTagResp getMemberRegisterTagDetail(HttpHeaders headers, MemberAndRoleIdReq idVO);

    /**
     * 查询用户注册资料
     * @param headers Http头部信息
     * @return 查询结果
     */
    UserDetailResp getUserDetail(HttpHeaders headers);
}
