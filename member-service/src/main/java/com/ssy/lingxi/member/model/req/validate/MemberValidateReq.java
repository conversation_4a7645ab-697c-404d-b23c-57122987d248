package com.ssy.lingxi.member.model.req.validate;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 用户审核接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
@Data
public class MemberValidateReq implements Serializable {
    private static final long serialVersionUID = -8038264341917164595L;

    /**
     * 会员Id
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;

    /**
     * 审核内容Id
     */
    @NotNull(message = "审核内容Id要大于0")
    @Positive(message = "审核内容Id要大于0")
    private Long validateId;

    /**
     * 资料版本:0-新增的、待审核的版本，1-正在使用的版本
     */
    private Integer detailVersionType;
}
