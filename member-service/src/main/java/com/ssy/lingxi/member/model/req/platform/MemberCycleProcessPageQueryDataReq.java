package com.ssy.lingxi.member.model.req.platform;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 会员生命周期变更流程规则
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberCycleProcessPageQueryDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = 6024916336354523120L;

    /**
     * 流程规则名称
     */
    private String name;
}
