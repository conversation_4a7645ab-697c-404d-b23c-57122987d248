package com.ssy.lingxi.member.serviceImpl;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.ReportItemResp;
import com.ssy.lingxi.common.model.resp.ReportTodayResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.component.base.enums.member.MemberLevelTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.manage.api.feign.IInitConfigFeign;
import com.ssy.lingxi.member.entity.do_.appraisal.QMemberAppraisalDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.QMemberRelationDO;
import com.ssy.lingxi.member.entity.do_.rectify.MemberRectifyDO;
import com.ssy.lingxi.member.entity.do_.rectify.QMemberRectifyDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.enums.report.*;
import com.ssy.lingxi.member.model.dto.MemberReportDTO;
import com.ssy.lingxi.member.model.resp.MemberReportResp;
import com.ssy.lingxi.member.repository.MemberLevelConfigRepository;
import com.ssy.lingxi.member.repository.MemberRectifyRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.IMemberReportService;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 首页-会员中心
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/12/8
 */
@Service
public class MemberReportServiceImpl implements IMemberReportService {
    @Resource
    private MemberRectifyRepository memberRectifyRepository;
    @Resource
    private MemberLevelConfigRepository memberLevelConfigRepository;
    @Resource
    private MemberRelationRepository memberRelationRepository;
    @Resource
    private JPAQueryFactory jpaQueryFactory;
    @Resource
    private IInitConfigFeign initConfigFeign;

    /**
     * 待办统计
     * @param sysUser 登录用户
     **/
    @Override
    public MemberReportResp getMember(UserLoginCacheDTO sysUser) {
        MemberReportDTO memberReportDTO = new MemberReportDTO();
        memberReportDTO.setMemberId(sysUser.getMemberId());
        memberReportDTO.setMemberRoleId(sysUser.getMemberRoleId());

        MemberReportResp memberReportResp = new MemberReportResp();
        //入库统计
        memberReportResp.setImportList(getImportList(memberReportDTO));
        //会员变更
        memberReportResp.setChangeList(getChangeList(memberReportDTO));
        //会员考评
        memberReportResp.setKpiList(getKpiList(memberReportDTO));
        //会员整改
        memberReportResp.setRectifyList(getRectifyList(memberReportDTO));
        //会员整改通知
        memberReportResp.setRectifyNoticeList(getRectifyNoticeList(memberReportDTO));
        //平台会员审核
        memberReportResp.setPlatformList(getPlatformList());
        //待进行变更
        memberReportResp.setMemberImportChangeList(getMemberImportChangeList(memberReportDTO, WrapperUtil.getDataOrThrow(initConfigFeign.enableMultiTenancy())));

        return memberReportResp;
    }

    /**
     * 今日新增--平台后台
     */
    @Override
    public ReportTodayResp getTodayNew() {
        long todayCount = memberRelationRepository.countByOuterStatusNotAndRelTypeAndInnerStatusNotAndCreateTimeBetween(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode(), MemberRelationTypeEnum.PLATFORM.getCode(), PlatformInnerStatusEnum.REGISTERING.getCode(), DateTimeUtil.getTodayBeginLocal(), DateTimeUtil.getTodayEndLocal());
        long yesterdayCount = memberRelationRepository.countByOuterStatusNotAndRelTypeAndInnerStatusNotAndCreateTimeBetween(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode(), MemberRelationTypeEnum.PLATFORM.getCode(), PlatformInnerStatusEnum.REGISTERING.getCode(), DateTimeUtil.getYesterdayBeginLocal(), DateTimeUtil.getYesterdayEndLocal());

        //计算增长率(相比昨天同比增长率)
        BigDecimal rate = BigDecimal.ZERO;
        if(yesterdayCount > 0){
            //（今日新增-昨日新增）/ 昨日新增 * 100
            rate = BigDecimal.valueOf(todayCount - yesterdayCount).divide(BigDecimal.valueOf(yesterdayCount), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }else{
            if(todayCount > 0){
                rate = BigDecimal.valueOf(100);
            }
        }

        ReportTodayResp reportTodayResp = new ReportTodayResp();
        reportTodayResp.setTodayCount(todayCount);
        reportTodayResp.setRate(rate);
        return reportTodayResp;
    }

    /**
     * 会员入库统计 - 供应商
     * @param memberReportDTO 参数
     */
    private List<ReportItemResp> getImportList(MemberReportDTO memberReportDTO) {
        List<Integer> innerState = Stream.of(
                MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL.getCode(), //待审核入库资料
                MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_QUALIFICATION.getCode(), //待审核入库资质
                MemberInnerStatusEnum.TO_INSPECT_DEPOSITORY.getCode(), //待入库考察
                MemberInnerStatusEnum.TO_CLASSIFY_DEPOSITORY.getCode(), //待入库分类
                MemberInnerStatusEnum.TO_DEPOSIT_GRADE_ONE.getCode(), //待入库审查(一级)
                MemberInnerStatusEnum.TO_DEPOSIT_GRADE_TWO.getCode(), //待审核入库(二级)
                //待确认入库
                MemberInnerStatusEnum.DEPOSITORY_DETAIL_NOT_PASSED.getCode(),
                MemberInnerStatusEnum.DEPOSITORY_QUALIFICATION_NOT_PASSED.getCode(),
                MemberInnerStatusEnum.DEPOSITORY_INSPECTION_NOT_PASSED.getCode(),
                MemberInnerStatusEnum.DEPOSITORY_CLASSIFICATION_NOT_PASSED.getCode(),
                MemberInnerStatusEnum.DEPOSITORY_GRADE_ONE_NOT_PASSED.getCode(),
                MemberInnerStatusEnum.DEPOSITORY_GRADE_TWO_NOT_PASSED.getCode(),
                MemberInnerStatusEnum.TO_CONFIRM_DEPOSITORY.getCode()
        ).collect(Collectors.toList());

        QMemberRelationDO memberRelationDO = QMemberRelationDO.memberRelationDO;
        BooleanBuilder predicates = new BooleanBuilder();
        predicates.and(memberRelationDO.roleId.eq(memberReportDTO.getMemberRoleId()));
        predicates.and(memberRelationDO.memberId.eq(memberReportDTO.getMemberId()));
        predicates.and(memberRelationDO.relType.eq(MemberRelationTypeEnum.OTHER.getCode()));
        predicates.and(memberRelationDO.innerStatus.in(innerState));
        predicates.and(memberRelationDO.outerStatus.eq(MemberOuterStatusEnum.DEPOSITING.getCode()));
        predicates.and(memberRelationDO.subRoleTag.eq(RoleTagEnum.SUPPLIER.getCode())); //供应商
        JPAQuery<Tuple> query = jpaQueryFactory.select(memberRelationDO.innerStatus, memberRelationDO.innerStatus.count())
                .from(memberRelationDO).where(predicates).groupBy(memberRelationDO.innerStatus);

        LinkedHashMap<Integer, ReportItemResp> reportMap = new LinkedHashMap<>();

        ReportItemResp tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberImportOperateTypeEnum.TO_BE_VALIFY_INFO.getName());
        tallyReportItem.setLink(MemberImportOperateTypeEnum.TO_BE_VALIFY_INFO.getLink());
        reportMap.put(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberImportOperateTypeEnum.TO_BE_VALIFY_QUALIFICATION.getName());
        tallyReportItem.setLink(MemberImportOperateTypeEnum.TO_BE_VALIFY_QUALIFICATION.getLink());
        reportMap.put(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_QUALIFICATION.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberImportOperateTypeEnum.TO_BE_IMPORT_EXPLORE.getName());
        tallyReportItem.setLink(MemberImportOperateTypeEnum.TO_BE_IMPORT_EXPLORE.getLink());
        reportMap.put(MemberInnerStatusEnum.TO_INSPECT_DEPOSITORY.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberImportOperateTypeEnum.TO_BE_IMPORT_CLASSIFY.getName());
        tallyReportItem.setLink(MemberImportOperateTypeEnum.TO_BE_IMPORT_CLASSIFY.getLink());
        reportMap.put(MemberInnerStatusEnum.TO_CLASSIFY_DEPOSITORY.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberImportOperateTypeEnum.TO_BE_VALIFY_STEP1.getName());
        tallyReportItem.setLink(MemberImportOperateTypeEnum.TO_BE_VALIFY_STEP1.getLink());
        reportMap.put(MemberInnerStatusEnum.TO_DEPOSIT_GRADE_ONE.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberImportOperateTypeEnum.TO_BE_VALIFY_STEP2.getName());
        tallyReportItem.setLink(MemberImportOperateTypeEnum.TO_BE_VALIFY_STEP2.getLink());
        reportMap.put(MemberInnerStatusEnum.TO_DEPOSIT_GRADE_TWO.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberImportOperateTypeEnum.TO_BE_CONFIRM.getName());
        tallyReportItem.setLink(MemberImportOperateTypeEnum.TO_BE_CONFIRM.getLink());
        int defaultCode = -100;
        reportMap.put(defaultCode, tallyReportItem);

        query.fetch().forEach(t -> {
            Integer state = t.get(memberRelationDO.innerStatus);
            Long count = t.get(memberRelationDO.innerStatus.count());
            ReportItemResp reportItemResp = Optional.ofNullable(reportMap.get(state)).orElse(reportMap.get(defaultCode));
            reportItemResp.setCount(Objects.isNull(count) ? reportItemResp.getCount() : reportItemResp.getCount() + count);
        });

        return new ArrayList<>(reportMap.values());
    }

    /**
     * 待确认入库资料变更统计 - 供应商
     * @param memberReportDTO 参数
     */
    private List<ReportItemResp> getChangeList(MemberReportDTO memberReportDTO) {
        List<Integer> innerState = Stream.of(
                MemberInnerStatusEnum.TO_MODIFY_GRADE_ONE.getCode(), //待审核入库资料变更(一级)
                MemberInnerStatusEnum.TO_MODIFY_GRADE_TWO.getCode(), //待审核入库资料变更(二级)
                //待确认入库资料变更
                MemberInnerStatusEnum.MODIFY_GRADE_ONE_NOT_PASSED.getCode(),
                MemberInnerStatusEnum.MODIFY_GRADE_TWO_NOT_PASSED.getCode(),
                MemberInnerStatusEnum.TO_CONFIRM_MODIFY.getCode()
        ).collect(Collectors.toList());

        QMemberRelationDO memberRelationDO = QMemberRelationDO.memberRelationDO;
        BooleanBuilder predicates = new BooleanBuilder();
        predicates.and(memberRelationDO.memberId.eq(memberReportDTO.getMemberId()));
        predicates.and(memberRelationDO.roleId.eq(memberReportDTO.getMemberRoleId()));
        predicates.and(memberRelationDO.subRoleTag.eq(RoleTagEnum.SUPPLIER.getCode())); //供应商
        predicates.and(memberRelationDO.relType.eq(MemberRelationTypeEnum.OTHER.getCode()));
        predicates.and(memberRelationDO.innerStatus.in(innerState));
        JPAQuery<Tuple> query = jpaQueryFactory.select(memberRelationDO.innerStatus, memberRelationDO.innerStatus.count())
                .from(memberRelationDO).where(predicates).groupBy(memberRelationDO.innerStatus);

        LinkedHashMap<Integer, ReportItemResp> reportMap = new LinkedHashMap<>();
        ReportItemResp tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberChangeOperateTypeEnum.TO_BE_VALIFY_STEP1.getName());
        tallyReportItem.setLink(MemberChangeOperateTypeEnum.TO_BE_VALIFY_STEP1.getLink());
        reportMap.put(MemberInnerStatusEnum.TO_MODIFY_GRADE_ONE.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberChangeOperateTypeEnum.TO_BE_VALIFY_STEP2.getName());
        tallyReportItem.setLink(MemberChangeOperateTypeEnum.TO_BE_VALIFY_STEP2.getLink());
        reportMap.put(MemberInnerStatusEnum.TO_MODIFY_GRADE_TWO.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberChangeOperateTypeEnum.TO_BE_CONFIRM.getName());
        tallyReportItem.setLink(MemberChangeOperateTypeEnum.TO_BE_CONFIRM.getLink());
        int defaultCode = -100;
        reportMap.put(defaultCode, tallyReportItem);

        query.fetch().forEach(t -> {
            Integer state = t.get(memberRelationDO.innerStatus);
            Long count = t.get(memberRelationDO.innerStatus.count());
            ReportItemResp reportItemResp = Optional.ofNullable(reportMap.get(state)).orElse(reportMap.get(defaultCode));
            reportItemResp.setCount(Objects.isNull(count) ? reportItemResp.getCount() : reportItemResp.getCount() + count);
        });

        return new ArrayList<>(reportMap.values());
    }

    /**
     * 会员考评统计 - 供应商
     * @param memberReportDTO 参数
     */
    private List<ReportItemResp> getKpiList(MemberReportDTO memberReportDTO) {
        List<Integer> innerState = Stream.of(
                MemberAppraisalStatusEnum.WAIT_PUBLISH.getCode(), //待发布考评表
                MemberAppraisalStatusEnum.WAIT_GRADE.getCode(), //待考评打分--
                MemberAppraisalStatusEnum.WAIT_AUDIT_1.getCode(), //待审核考评结果一级
                MemberAppraisalStatusEnum.WAIT_AUDIT_2.getCode(), //待审核考评结果一级
                MemberAppraisalStatusEnum.WAIT_NOTIFICATION.getCode(),  //待通报考评结果
                //待提交汇总考评结果
                MemberAppraisalStatusEnum.WAIT_SUBMIT.getCode(),
                MemberAppraisalStatusEnum.WAIT_AUDIT_1_REJECT.getCode(),
                MemberAppraisalStatusEnum.WAIT_AUDIT_2_REJECT.getCode()
        ).collect(Collectors.toList());

        QMemberAppraisalDO memberAppraisalDO = QMemberAppraisalDO.memberAppraisalDO;
        BooleanBuilder predicates = new BooleanBuilder();
        predicates.and(memberAppraisalDO.member.id.eq(memberReportDTO.getMemberId()));
        predicates.and(memberAppraisalDO.role.id.eq(memberReportDTO.getMemberRoleId()));
        predicates.and(memberAppraisalDO.status.in(innerState));
        predicates.and(memberAppraisalDO.subRole.roleTag.eq(RoleTagEnum.SUPPLIER.getCode()));

        JPAQuery<Tuple> query = jpaQueryFactory.select(memberAppraisalDO.status, memberAppraisalDO.status.count())
                .from(memberAppraisalDO).where(predicates).groupBy(memberAppraisalDO.status);

        LinkedHashMap<Integer, ReportItemResp> reportMap = new LinkedHashMap<>();
        ReportItemResp tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberKpiOperateTypeEnum.TO_BE_PUBLISH_COUNT.getName());
        tallyReportItem.setLink(MemberKpiOperateTypeEnum.TO_BE_PUBLISH_COUNT.getLink());
        reportMap.put(MemberAppraisalStatusEnum.WAIT_PUBLISH.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberKpiOperateTypeEnum.TO_BE_SCORING_COUNT.getName());
        tallyReportItem.setLink(MemberKpiOperateTypeEnum.TO_BE_SCORING_COUNT.getLink());
        reportMap.put(MemberAppraisalStatusEnum.WAIT_GRADE.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP1_COUNT.getName());
        tallyReportItem.setLink(MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP1_COUNT.getLink());
        reportMap.put(MemberAppraisalStatusEnum.WAIT_AUDIT_1.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP2_COUNT.getName());
        tallyReportItem.setLink(MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP2_COUNT.getLink());
        reportMap.put(MemberAppraisalStatusEnum.WAIT_AUDIT_2.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberKpiOperateTypeEnum.TO_BE_NOTICE_COUNT.getName());
        tallyReportItem.setLink(MemberKpiOperateTypeEnum.TO_BE_NOTICE_COUNT.getLink());
        reportMap.put(MemberAppraisalStatusEnum.WAIT_NOTIFICATION.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberKpiOperateTypeEnum.TO_BE_COMMIT_COUNT.getName());
        tallyReportItem.setLink(MemberKpiOperateTypeEnum.TO_BE_COMMIT_COUNT.getLink());
        int defaultCode = -100;
        reportMap.put(defaultCode, tallyReportItem);

        query.fetch().forEach(t -> {
            Integer state = t.get(memberAppraisalDO.status);
            Long count = t.get(memberAppraisalDO.status.count());
            ReportItemResp reportItemResp = Optional.ofNullable(reportMap.get(state)).orElse(reportMap.get(defaultCode));
            reportItemResp.setCount(Objects.isNull(count) ? reportItemResp.getCount() : reportItemResp.getCount() + count);
        });

        return new ArrayList<>(reportMap.values());
    }

    /**
     * 会员整改统计 - 供应商
     * @param memberReportDTO 参数
     */
    private List<ReportItemResp> getRectifyList(MemberReportDTO memberReportDTO) {
        List<Integer> innerState = Stream.of(
                MemberRectifyStatusEnum.WAIT_SEND.getCode(), //待发送整改通知
                MemberRectifyStatusEnum.WAIT_CONFIRM.getCode() // 待确认整改结果
        ).collect(Collectors.toList());

        QMemberRectifyDO memberRectifyDO = QMemberRectifyDO.memberRectifyDO;
        BooleanBuilder predicates = new BooleanBuilder();
        predicates.and(memberRectifyDO.member.id.eq(memberReportDTO.getMemberId()));
        predicates.and(memberRectifyDO.role.id.eq(memberReportDTO.getMemberRoleId()));
        predicates.and(memberRectifyDO.outerStatus.in(innerState));
        predicates.and(memberRectifyDO.subRole.roleTag.eq(RoleTagEnum.SUPPLIER.getCode()));
        JPAQuery<Tuple> query = jpaQueryFactory.select(memberRectifyDO.outerStatus, memberRectifyDO.outerStatus.count())
                .from(memberRectifyDO).where(predicates).groupBy(memberRectifyDO.outerStatus);

        LinkedHashMap<Integer, ReportItemResp> reportMap = new LinkedHashMap<>();
        ReportItemResp tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberRectifyOperateTypeEnum.TO_BE_SEND_NOTICE_COUNT.getName());
        tallyReportItem.setLink(MemberRectifyOperateTypeEnum.TO_BE_SEND_NOTICE_COUNT.getLink());
        reportMap.put(MemberRectifyStatusEnum.WAIT_SEND.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberRectifyOperateTypeEnum.TO_BE_SEND_NOTICE_COUNT.getName());
        tallyReportItem.setLink(MemberRectifyOperateTypeEnum.TO_BE_SEND_NOTICE_COUNT.getLink());
        reportMap.put(MemberRectifyStatusEnum.WAIT_CONFIRM.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberRectifyOperateTypeEnum.TO_BE_CONFIRM_NOTICE_COUNT.getName());
        tallyReportItem.setLink(MemberRectifyOperateTypeEnum.TO_BE_CONFIRM_NOTICE_COUNT.getLink());
        int defaultCode = -100;
        reportMap.put(defaultCode, tallyReportItem);

        query.fetch().forEach(t -> {
            Integer state = t.get(memberRectifyDO.outerStatus);
            Long count = t.get(memberRectifyDO.outerStatus.count());
            ReportItemResp reportItemResp = Optional.ofNullable(reportMap.get(state)).orElse(reportMap.get(defaultCode));
            reportItemResp.setCount(Objects.isNull(count) ? reportItemResp.getCount() : reportItemResp.getCount() + count);
        });

        return new ArrayList<>(reportMap.values());
    }

    /**
     * 会员整改通知统计 - 供应商
     * @param memberReportDTO 参数
     */
    private List<ReportItemResp> getRectifyNoticeList(MemberReportDTO memberReportDTO) {
        Specification<MemberRectifyDO> specification = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(cb.equal(root.get("subMember").get("id"), memberReportDTO.getMemberId()));
            list.add(cb.equal(root.get("subRole").get("id"), memberReportDTO.getMemberRoleId()));
            list.add(cb.equal(root.get("subRole").get("roleTag"), RoleTagEnum.SUPPLIER.getCode()));
            Predicate[] p = new Predicate[list.size()];
            return cb.and(list.toArray(p));
        };

        long count = memberRectifyRepository.count(specification);
        List<ReportItemResp> importList = new ArrayList<>();
        ReportItemResp tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(count);
        tallyReportItem.setName(VendorRectifyNoticeOperateTypeEnum.TO_BE_RECTIFY_COUNT.getName());
        tallyReportItem.setLink(VendorRectifyNoticeOperateTypeEnum.TO_BE_RECTIFY_COUNT.getLink());
        importList.add(tallyReportItem);
        return importList;
    }

    /**
     * 会员整改统计 - 供应商
     */
    private List<ReportItemResp> getPlatformList() {
        List<Integer> innerState = Stream.of(
                PlatformInnerStatusEnum.TO_BE_COMMIT.getCode(), //待提交审核
                PlatformInnerStatusEnum.TO_BE_VERIFY_STEP1.getCode(), // 待一级审核
                PlatformInnerStatusEnum.TO_BE_VERIFY_STEP2.getCode(), // 待二级审核
                //待确认审核结果
                PlatformInnerStatusEnum.COMMIT_NOT_PASSED.getCode(),
                PlatformInnerStatusEnum.VERIFY_STEP1_NOT_PASSED.getCode(),
                PlatformInnerStatusEnum.VERIFY_STEP2_NOT_PASSED.getCode(),
                PlatformInnerStatusEnum.TO_CONFIRM.getCode()
        ).collect(Collectors.toList());

        QMemberRelationDO memberRelationDO = QMemberRelationDO.memberRelationDO;
        BooleanBuilder predicates = new BooleanBuilder();
        predicates.and(memberRelationDO.relType.eq(MemberRelationTypeEnum.PLATFORM.getCode()));
        predicates.and(memberRelationDO.innerStatus.in(innerState));
        predicates.and(memberRelationDO.subRoleTag.eq(RoleTagEnum.SUPPLIER.getCode())); //供应商
        JPAQuery<Tuple> query = jpaQueryFactory.select(memberRelationDO.innerStatus, memberRelationDO.innerStatus.count())
                .from(memberRelationDO).where(predicates).groupBy(memberRelationDO.innerStatus);

        LinkedHashMap<Integer, ReportItemResp> reportMap = new LinkedHashMap<>();
        ReportItemResp tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberOperateTypeEnum.TO_BE_COMMIT_VALIFY.getName());
        tallyReportItem.setLink(MemberOperateTypeEnum.TO_BE_COMMIT_VALIFY.getLink());
        reportMap.put(PlatformInnerStatusEnum.TO_BE_COMMIT.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberOperateTypeEnum.TO_BE_VALIFY_STEP1.getName());
        tallyReportItem.setLink(MemberOperateTypeEnum.TO_BE_VALIFY_STEP1.getLink());
        reportMap.put(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP1.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberOperateTypeEnum.TO_BE_VALIFY_STEP2.getName());
        tallyReportItem.setLink(MemberOperateTypeEnum.TO_BE_VALIFY_STEP2.getLink());
        reportMap.put(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP2.getCode(), tallyReportItem);

        tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(0L);
        tallyReportItem.setName(MemberOperateTypeEnum.TO_BE_CONFIRM.getName());
        tallyReportItem.setLink(MemberOperateTypeEnum.TO_BE_CONFIRM.getLink());
        int defaultCode = -100;
        reportMap.put(defaultCode, tallyReportItem);

        query.fetch().forEach(t -> {
            Integer state = t.get(memberRelationDO.innerStatus);
            Long count = t.get(memberRelationDO.innerStatus.count());
            ReportItemResp reportItemResp = Optional.ofNullable(reportMap.get(state)).orElse(reportMap.get(defaultCode));
            reportItemResp.setCount(Objects.isNull(count) ? reportItemResp.getCount() : reportItemResp.getCount() + count);
        });

        return new ArrayList<>(reportMap.values());
    }

    /**
     * 待进行变更 - 供应商
     * @param enableMultiTenancy 是否开启SAAS多租户部署
     */
    private List<ReportItemResp> getMemberImportChangeList(MemberReportDTO memberReportDTO, boolean enableMultiTenancy) {
        boolean platformLevelConfig = memberLevelConfigRepository.existsByLevelTypeAndSubRoleId(MemberLevelTypeEnum.PLATFORM.getCode(), memberReportDTO.getMemberRoleId());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId"), memberReportDTO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), memberReportDTO.getMemberRoleId()));
            if (!platformLevelConfig) {
                list.add(criteriaBuilder.notEqual(root.get("subMemberLevelTypeEnum").as(Integer.class), MemberLevelTypeEnum.PLATFORM.getCode()));
            }
            //如果开启SAAS多租户部署 就隐藏 会员等级类型 为 平台会员 的会员数据
            if (enableMultiTenancy) {
                list.add(criteriaBuilder.notEqual(root.get("subMemberLevelTypeEnum").as(Integer.class), MemberLevelTypeEnum.PLATFORM.getCode()));
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        long count = memberRelationRepository.count(specification);
        List<ReportItemResp> importList = new ArrayList<>();
        ReportItemResp tallyReportItem = new ReportItemResp();
        tallyReportItem.setCount(count);
        tallyReportItem.setName(MemberImportChangeOperateTypeEnum.TO_BE_CHANGE_ING.getName());
        tallyReportItem.setLink(MemberImportChangeOperateTypeEnum.TO_BE_CHANGE_ING.getLink());
        importList.add(tallyReportItem);
        return importList;
    }

}
