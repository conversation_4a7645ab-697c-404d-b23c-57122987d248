package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.member.entity.bo.DetailCheckBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import com.ssy.lingxi.member.enums.MemberDetailVersionEnum;
import com.ssy.lingxi.member.model.resp.basic.*;

import java.util.List;
import java.util.Map;

/**
 * 会员注册信息查询相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-01-14
 */
public interface IBaseMemberRegisterDetailService {

    /**
     * 将会员注册资料转换为前端注册页面、新增会员页面内容
     * @param memberConfigs 会员注册资料
     * @return 转换结果
     */
    List<MemberConfigGroupResp> groupMemberConfig(List<MemberRegisterConfigDO> memberConfigs);

    /**
     * 会员注册时，校验注册资料，并返回会员名称
     * @param detail 注册资料
     * @param memberConfigList 角色关联的会员注册资料配置
     * @param registerDetailList 返回注册资料，其版本为“待审核”
     * @param defaultName 如果注册资料为空，用手机号作为公司名称
     * @return 校验结果
     */
    String checkMemberRegisterDetail(Map<String, Object> detail, List<MemberRegisterConfigDO> memberConfigList, List<MemberRegisterDetailDO> registerDetailList, String defaultName);

    /**
     * 比较两个版本的注册资料，是否有修改
     * @param member 会员
     * @param toValidateDetails 新增的注册资料
     * @return 是否有修改
     */
    boolean isDetailModified(MemberDO member, List<MemberRegisterDetailDO> toValidateDetails);

    /**
     * 查询会员注册资料（指定版本）
     * @param member 会员
     * @param memberRoleDO 角色
     * @param versionEnum 注册资料版本
     * @return 查询结果
     */
    List<RegisterDetailGroupResp> groupMemberRegisterDetail(MemberDO member, MemberRoleDO memberRoleDO, MemberDetailVersionEnum versionEnum);

    /**
     * 查询会员注册资料（Using版本优先，如无则查询正在审核的版本）
     * @param member 会员
     * @return 查询结果
     */
    List<RegisterDetailGroupResp> switchMemberRegisterDetail(MemberDO member);

    /**
     * 查询会员注册资料（两个版本合并）
     * @param member 会员
     * @return 查询结果
     */
    List<RegisterDetailGroupResp> groupMemberRegisterDetail(MemberDO member);

    /**
     * 查询会员注册资料（查询指定版本）
     * @param member 会员
     * @param versionEnum 指定版本
     * @return 查询结果
     */
    List<DetailTextGroupResp> groupMemberRegisterDetailText(MemberDO member, MemberDetailVersionEnum versionEnum);

    /**
     * 查询会员注册资料（Using版本优先，如果没有则查询正在审核的版本）
     * @param member 会员
     * @return 查询结果
     */
    List<DetailTextGroupResp> switchMemberRegisterDetailText(MemberDO member);

    /**
     * 查询会员注册资料（文本显示，两个版本合并）
     * @param member 会员
     * @return 查询结果
     */
    List<DetailTextGroupResp> groupMemberRegisterDetailText(MemberDO member);

    /**
     * 新增角色时，合并现有的注册资料与新增角色的注册资料
     * @param member 会员
     * @param versionEnum 注册资料的版本
     * @param memberRoleDO 新增的角色
     * @return 合并结果
     */
    List<RegisterDetailGroupResp> mergeMemberRegisterDetail(MemberDO member, MemberDetailVersionEnum versionEnum, MemberRoleDO memberRoleDO);


    /**
     * 会员审核通过后，将注册资料的版本更新为正在使用的版本
     * @param member 会员
     */
    void updateMemberRegisterDetailToUsing(MemberDO member);

    /**
     * 查询会员标签注册资料
     * @param memberId 会员Id
     * @return 查询结果
     */
    MemberRegisterTagResp getMemberRegisterTagDetail(Long memberId);

    /**
     * 查询会员标签注册资料
     * @param member 会员
     * @return 查询结果
     */
    MemberRegisterTagResp getMemberRegisterTagDetail(MemberDO member);

    /**
     * 修改平台会员注册资料
     * @param platformRelation 平台会员关系
     * @param email 邮箱
     * @param detail 前端传递的修改结果
     * @param updateEmail 是否检查并更新Email
     * @param forcePlatformValidate 是否强制平台审核，如果不强制，则根据注册资料配置判断
     */
    void updatePlatformMemberRegisterDetail(MemberRelationDO platformRelation, String email, Map<String, Object> detail, boolean updateEmail, boolean forcePlatformValidate);

    /**
     * 查询用户注册资料
     * @param userId 用户Id
     * @return 查询结果
     */
    UserDetailResp getUserDetail(Long userId);

    /**
     * 检查注册资料、入库资料
     * @param configDO 会员注册资料
     * @param value  新增、修改后的值
     * @return 检查结果
     */
    DetailCheckBO checkDetail(MemberRegisterConfigDO configDO, String value);

    /**
     * 检查注册资料、入库资料的字段是否齐全
     * @param memberConfigList 注册资料列表
     * @param detailMap 前端传递的注册、入库资料
     * @return 检查结果
     */
    void checkDetailFields(List<MemberRegisterConfigDO> memberConfigList, Map<String, Object> detailMap);
}
