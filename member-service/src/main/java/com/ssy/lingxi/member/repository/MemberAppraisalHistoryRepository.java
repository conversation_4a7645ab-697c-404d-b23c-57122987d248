package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.appraisal.MemberAppraisalHistoryDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员考评历史记录Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Repository
public interface MemberAppraisalHistoryRepository extends JpaRepository<MemberAppraisalHistoryDO, Long>, JpaSpecificationExecutor<MemberAppraisalHistoryDO> {

    List<MemberAppraisalHistoryDO> findByAppraisalId(Long appraisalId, Sort sort);
}
