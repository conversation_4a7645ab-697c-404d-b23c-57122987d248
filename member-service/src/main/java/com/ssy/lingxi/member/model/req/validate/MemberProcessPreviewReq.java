package com.ssy.lingxi.member.model.req.validate;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.util.List;

/**
 * 会员流程规则配置 - 预览注册资料接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-15
 */
@Data
public class MemberProcessPreviewReq implements Serializable {
    private static final long serialVersionUID = 8244657106043751701L;

    @NotNull(message = "角色Id要大于0")
    @Positive(message = "角色Id要大于0")
    private Long roleId;

    /**
     * 注册资料Id列表
     */
    private List<Long> configIds;
}
