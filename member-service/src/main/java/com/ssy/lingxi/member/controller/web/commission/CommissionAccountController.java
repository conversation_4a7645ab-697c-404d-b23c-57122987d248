package com.ssy.lingxi.member.controller.web.commission;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.commission.BindBankCardReq;
import com.ssy.lingxi.member.model.req.commission.CommissionAccountQueryReq;
import com.ssy.lingxi.member.model.req.commission.CommissionDetailQueryReq;
import com.ssy.lingxi.member.model.req.commission.FreezeAccountReq;
import com.ssy.lingxi.member.model.req.commission.UnfreezeAccountReq;
import com.ssy.lingxi.member.model.resp.commission.*;

import java.util.List;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.commission.ICommissionAccountService;
import com.ssy.lingxi.member.service.commission.IInvitationCommissionService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.servlet.http.HttpServletResponse;

/**
 * 会员能力 - 客户分佣账户管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/commission/account")
public class CommissionAccountController {

    @Resource
    private ICommissionAccountService commissionAccountService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IInvitationCommissionService invitationCommissionService;

    // ==================================客户分佣账户列表===============================

    /**
     * 分页查询客户分佣账户列表
     * @param headers Http头部信息
     * @param request 查询请求
     * @return 分页结果
     */
    @PostMapping("/getPage")
    public WrapperResp<PageDataResp<CommissionAccountResp>> getCommissionAccountPage(@RequestHeader HttpHeaders headers, 
                                                                                    @RequestBody @Valid CommissionAccountQueryReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        PageDataResp<CommissionAccountResp> result = commissionAccountService.getCommissionAccountPage(loginUser, request);
        return WrapperUtil.success(result);
    }

    /**
     * 导出客户分佣账户列表
     * @param headers Http头部信息
     * @param request 查询请求
     * @param response HTTP响应
     */
    @GetMapping("/export")
    public void exportCommissionAccountList(@RequestHeader HttpHeaders headers,
                                           CommissionAccountQueryReq request,
                                           HttpServletResponse response) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionAccountService.exportCommissionAccountList(loginUser, request, response);
    }

    // ==================================客户分佣账户详情===============================

    /**
     * 查看客户分佣账户详情
     * @param headers Http头部信息
     * @param accountId 账户id
     * @return 账户详情
     */
    @GetMapping("/getDetail")
    public WrapperResp<CommissionAccountDetailResp> getCommissionAccountDetail(@RequestHeader HttpHeaders headers, 
                                                                              @RequestParam @NotNull(message = "账户id不能为空") Long accountId) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        CommissionAccountDetailResp result = commissionAccountService.getCommissionAccountDetail(loginUser, accountId);
        return WrapperUtil.success(result);
    }

    // ==================================客户分佣账户操作===============================

    /**
     * 冻结客户分佣账户
     * @param headers Http头部信息
     * @param request 冻结账户请求
     * @return 操作结果
     */
    @PostMapping("/freeze")
    public WrapperResp<Void> freezeCommissionAccount(@RequestHeader HttpHeaders headers,
                                                     @RequestBody @Valid FreezeAccountReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionAccountService.freezeCommissionAccount(loginUser, request.getAccountId());
        return WrapperUtil.success();
    }

    /**
     * 解冻客户分佣账户
     * @param headers Http头部信息
     * @param request 解冻账户请求
     * @return 操作结果
     */
    @PostMapping("/unfreeze")
    public WrapperResp<Void> unfreezeCommissionAccount(@RequestHeader HttpHeaders headers,
                                                       @RequestBody @Valid UnfreezeAccountReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionAccountService.unfreezeCommissionAccount(loginUser, request.getAccountId());
        return WrapperUtil.success();
    }

    // ==================================佣金明细记录===============================

    /**
     * 分页查询佣金明细记录列表
     * @param headers Http头部信息
     * @param request 查询请求
     * @return 分页结果
     */
    @PostMapping("/getCommissionDetailPage")
    public WrapperResp<PageDataResp<CommissionDetailResp>> getCommissionDetailPage(@RequestHeader HttpHeaders headers,
                                                                                  @RequestBody @Valid CommissionDetailQueryReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        PageDataResp<CommissionDetailResp> result = commissionAccountService.getCommissionDetailPage(loginUser, request);
        return WrapperUtil.success(result);
    }



    // ==================================银行卡管理===============================

    /**
     * 绑定银行卡
     * @param headers Http头部信息
     * @param request 绑定银行卡请求
     * @return 操作结果
     */
    @PostMapping("/bindBankCard")
    public WrapperResp<Long> bindBankCard(@RequestHeader HttpHeaders headers,
                                         @RequestBody @Valid BindBankCardReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Long bankCardId = commissionAccountService.bindBankCard(loginUser, request);
        return WrapperUtil.success(bankCardId);
    }

    /**
     * 查询银行卡列表
     * @param headers Http头部信息
     * @param userId 用户id
     * @return 银行卡列表
     */
    @GetMapping("/getBankCardList")
    public WrapperResp<List<BankCardResp>> getBankCardList(@RequestHeader HttpHeaders headers,
                                                          @RequestParam @NotNull(message = "用户id不能为空") Long userId) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<BankCardResp> result = commissionAccountService.getBankCardList(loginUser, userId);
        return WrapperUtil.success(result);
    }

    // ==================================冻结记录列表===============================

    /**
     * 查询冻结记录列表
     * @param headers Http头部信息
     * @param commissionAccountId 分佣账户id
     * @return 冻结记录列表
     */
    @GetMapping("/getAccountFreezeRecordList")
    public WrapperResp<List<AccountFreezeRecordResp>> getAccountFreezeRecordList(@RequestHeader HttpHeaders headers,
                                                                                @RequestParam @NotNull(message = "分佣账户id不能为空") Long commissionAccountId) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<AccountFreezeRecordResp> result = commissionAccountService.getAccountFreezeRecordList(loginUser, commissionAccountId);
        return WrapperUtil.success(result);
    }

    // ==================================历史账号处理===============================

    /**
     * 批量处理历史账号：为没有邀请码的用户生成邀请码，为所有用户创建分佣账户
     * @param headers Http头部信息
     * @return 处理结果统计
     */
    @PostMapping("/processHistoricalAccounts")
    public WrapperResp<String> processHistoricalAccounts(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        String result = invitationCommissionService.processHistoricalAccounts();
        return WrapperUtil.success(result);
    }


}
