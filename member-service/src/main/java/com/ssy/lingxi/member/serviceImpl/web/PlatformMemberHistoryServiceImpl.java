package com.ssy.lingxi.member.serviceImpl.web;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.member.entity.do_.validate.QMemberInnerValidateHistoryDO;
import com.ssy.lingxi.member.entity.do_.validate.QMemberValidateHistoryDO;
import com.ssy.lingxi.member.model.req.basic.MemberHistoryPageDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberInnerHistoryDetailResp;
import com.ssy.lingxi.member.model.resp.basic.MemberOuterHitoryDetailResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IPlatformMemberHistoryService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 平台后台 - 日志中心 - 会员内、外流转记录查询相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-03
 */
@Service
public class PlatformMemberHistoryServiceImpl implements IPlatformMemberHistoryService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    /**
     * 分页查询外部流转记录
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberOuterHitoryDetailResp> findOuterHistories(HttpHeaders headers, MemberHistoryPageDataReq pageVO) {
        memberCacheService.needLoginFromManagePlatform(headers);

        QMemberValidateHistoryDO qMemberValidateHistory = QMemberValidateHistoryDO.memberValidateHistoryDO;

        JPAQuery<MemberOuterHitoryDetailResp> query = jpaQueryFactory
                .select(Projections.constructor(MemberOuterHitoryDetailResp.class, qMemberValidateHistory.memberId, qMemberValidateHistory.subMemberId, qMemberValidateHistory.relType, qMemberValidateHistory.createTime, qMemberValidateHistory.roleName, qMemberValidateHistory.outerStatus, qMemberValidateHistory.operationCode, qMemberValidateHistory.operation, qMemberValidateHistory.remark))
                .from(qMemberValidateHistory)
                .where(qMemberValidateHistory.relType.eq(pageVO.getType()));

        //会员Id
        if(NumberUtil.notNullOrZero(pageVO.getMemberId())) {
            if(pageVO.getType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
                query.where(qMemberValidateHistory.subMemberId.eq(pageVO.getMemberId()));
            } else {
                query.where(qMemberValidateHistory.memberId.eq(pageVO.getMemberId()));
            }
        }

        //起始时间
        if(StringUtils.hasText(pageVO.getStartDate())) {
            query.where(qMemberValidateHistory.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        //结束时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            query.where(qMemberValidateHistory.createTime.before(LocalDateTime.parse(pageVO.getStartDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        //Step 4: 倒序排序、分页、总数
        query.orderBy(qMemberValidateHistory.createTime.desc());
        query.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());
        long totalCount = query.fetchCount();

        return new PageDataResp<>(totalCount, query.fetch());
    }

    /**
     * 分页查询内部流转记录
     *
     * @param headers HttpHeaders信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberInnerHistoryDetailResp> findInnerHistories(HttpHeaders headers, MemberHistoryPageDataReq pageVO) {
        memberCacheService.needLoginFromManagePlatform(headers);

        QMemberInnerValidateHistoryDO qMemberInnerValidateHistory = QMemberInnerValidateHistoryDO.memberInnerValidateHistoryDO;

        JPAQuery<MemberInnerHistoryDetailResp> query = jpaQueryFactory
                .select(Projections.constructor(MemberInnerHistoryDetailResp.class, qMemberInnerValidateHistory.memberId, qMemberInnerValidateHistory.subMemberId, qMemberInnerValidateHistory.relType, qMemberInnerValidateHistory.createTime, qMemberInnerValidateHistory.operatorName, qMemberInnerValidateHistory.operatorOrgName, qMemberInnerValidateHistory.operatorJobTitle, qMemberInnerValidateHistory.outerStatus, qMemberInnerValidateHistory.operateCode, qMemberInnerValidateHistory.operation, qMemberInnerValidateHistory.remark))
                .from(qMemberInnerValidateHistory)
                .where(qMemberInnerValidateHistory.relType.eq(pageVO.getType()));

        //下级会员Id
        if(NumberUtil.notNullOrZero(pageVO.getMemberId())) {
            if(pageVO.getType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
                query.where(qMemberInnerValidateHistory.subMemberId.eq(pageVO.getMemberId()));
            } else {
                query.where(qMemberInnerValidateHistory.memberId.eq(pageVO.getMemberId()));
            }
        }

        //起始时间
        if(StringUtils.hasText(pageVO.getStartDate())) {
            query.where(qMemberInnerValidateHistory.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        //结束时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            query.where(qMemberInnerValidateHistory.createTime.before(LocalDateTime.parse(pageVO.getStartDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        //Step 4: 倒序排序、分页、总数
        query.orderBy(qMemberInnerValidateHistory.createTime.desc());
        query.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());
        long totalCount = query.fetchCount();

        return new PageDataResp<>(totalCount, query.fetch());
    }
}
