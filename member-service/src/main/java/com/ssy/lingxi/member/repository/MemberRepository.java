package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员注册信息Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-03
 */
@Repository
public interface MemberRepository extends JpaRepository<MemberDO,Long>, JpaSpecificationExecutor<MemberDO> {
    boolean existsByName(String name);

    boolean existsByNameAndIdNot(String name, Long id);

    MemberDO findFirstByAccount(String account);

    List<MemberDO> findByNameContainsAndIdIn(String name, List<Long> memberIds);

    Page<MemberDO> findAllByNameLike(String name, Pageable pageable);

    @Query(value = "select member_role_id from mem_member_role_relation where member_id = :memberId",nativeQuery = true)
    List<Long> findByRoleIdByMemberId(Long memberId);

    @Query(value = "select * from mem_member where source = 99", nativeQuery = true)
    MemberDO findPlatformMember();

    MemberDO findFirstByCode(String memberCode);

    List<MemberDO> findAllByCorporationId(Long corporationId);

    List<MemberDO> findAllByCodeIn(List<String> codes);

    List<MemberDO> findByCorporationIdIn(List<Long> corporationIds);

    List<MemberDO> findBySourceNotAndIdNot(Integer source, Long id);
}
