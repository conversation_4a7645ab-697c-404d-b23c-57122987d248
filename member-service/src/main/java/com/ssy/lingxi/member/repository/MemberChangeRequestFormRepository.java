package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.MemberChangeRequestFormDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * 供应商生命周期管理 - 变更申请单Repository
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
@Repository
public interface MemberChangeRequestFormRepository extends JpaRepository<MemberChangeRequestFormDO, Long>, JpaSpecificationExecutor<MemberChangeRequestFormDO> {
    MemberChangeRequestFormDO findFirstByCreateTimeBetween(LocalDateTime beginTime, LocalDateTime endTime, Sort sort);
}
