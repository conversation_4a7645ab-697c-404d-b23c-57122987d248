package com.ssy.lingxi.member.model.req.comment;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 订单评价接口查询参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderProductTradeCommentQueryDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -8835297738355211553L;


    /**
     * 商城类型
     */
    private Integer shopType;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 评论级别 - 0或Null：全部， 1-差评（1-2星），2-中评（3星）， 3-好评（4-5星）
     */
    private Integer starLevel;
}
