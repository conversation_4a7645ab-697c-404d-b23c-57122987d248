package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.req.mobile.MobileRightScoreHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.mobile.MobileShopRightScorePageDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRightScoreResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileRightScoreHistoryResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileRightScoreResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileShopRightScoreHistoryResp;
import com.ssy.lingxi.member.service.mobile.IMobileLrcService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * App - 会员等级、权益、信用相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-17
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/lrc")
public class MobileLrcController {

    @Resource
    private IMobileLrcService mobileLrcService;

    /**
     * “我的积分” - 分页查询列表
     * @param headers Http头部信息
     * @param pageDataReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/right/page")
    public WrapperResp<PageDataResp<MobileRightScoreResp>> pageRightScore(@RequestHeader HttpHeaders headers, @Valid PageDataReq pageDataReq) {
        return WrapperUtil.success(mobileLrcService.pageRightScore(headers, pageDataReq));
    }

    /**
     * “我的积分” - 分页查询权益积分历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/right/detail/page")
    public WrapperResp<PageDataResp<MobileRightScoreHistoryResp>> pageRightScoreHistory(@RequestHeader HttpHeaders headers, @Valid MobileRightScoreHistoryPageDataReq pageVO) {
        return WrapperUtil.success(mobileLrcService.pageRightScoreHistory(headers, pageVO));
    }

    /**
     * “找店铺 - 积分兑换” - 分页查询权益积分历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/right/shop/detail/page")
    public WrapperResp<MobileShopRightScoreHistoryResp> pageShopRightScoreHistory(@RequestHeader HttpHeaders headers, @Valid MobileShopRightScorePageDataReq pageVO) {
        return WrapperUtil.success(mobileLrcService.pageShopRightScoreHistory(headers, pageVO));
    }

    /**
     * “积分订单” - 查询平台通用和会员专有权益积分
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/right/point/get")
    public WrapperResp<MemberRightScoreResp> getMemberRightScore(@RequestHeader HttpHeaders headers, @Valid MemberAndRoleIdReq idVO) {
        return WrapperUtil.success(mobileLrcService.getMemberRightScore(headers, idVO));
    }
}
