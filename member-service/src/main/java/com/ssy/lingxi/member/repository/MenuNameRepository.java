package com.ssy.lingxi.member.repository;


import com.ssy.lingxi.member.entity.do_.menuAuth.MenuNameDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 菜单多语言名称表
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2024/3/7
 */
@Repository
public interface MenuNameRepository extends JpaRepository<MenuNameDO, Long>, JpaSpecificationExecutor<MenuNameDO> {
    List<MenuNameDO> findByMenuId(Long menuId);

    void deleteByMenuId(Long menuId);

    void deleteAllByMenuIdIn(Collection<Long> menuIdList);

    List<MenuNameDO> findBySource(Integer source);

    Boolean existsByLanguageAndNameAndParentMenuIdAndSource(String language, String name, Long pid, Integer source);

    Boolean existsByLanguageAndNameAndParentMenuIdAndMenuIdNotAndSource(String language, String name, Long parentMenuId, Long menuId, Integer source);
}
