package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.util.ObjectUtil;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.CollectionPageUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.LanguageEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.ProcessBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.detail.*;
import com.ssy.lingxi.member.enums.MemberRegisterConfigNameEnum;
import com.ssy.lingxi.member.model.req.basic.NamePageDataReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.basic.BaseMemberProcessResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigNameResp;
import com.ssy.lingxi.member.model.resp.basic.RoleManageResp;
import com.ssy.lingxi.member.model.resp.platform.RoleRuleManageResp;
import com.ssy.lingxi.member.model.resp.validate.MemberProcessQueryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberProcessResp;
import com.ssy.lingxi.member.repository.MemberProcessRepository;
import com.ssy.lingxi.member.repository.MemberProcessRuleRepository;
import com.ssy.lingxi.member.repository.MemberRegisterConfigRepository;
import com.ssy.lingxi.member.repository.MemberRoleRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberRegisterDetailService;
import com.ssy.lingxi.member.service.base.IBaseSiteService;
import com.ssy.lingxi.member.service.web.IMemberProcessRuleService;
import com.ssy.lingxi.member.service.web.IPlatformMemberRoleRuleService;
import com.ssy.lingxi.member.util.RgConfigUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员管理流程规则配置相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-15
 */
@Service
public class MemberProcessRuleServiceImpl implements IMemberProcessRuleService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRoleRepository roleRepository;

    @Resource
    private MemberProcessRepository memberProcessRepository;

    @Resource
    private MemberProcessRuleRepository memberProcessRuleRepository;

    @Resource
    private MemberRegisterConfigRepository memberRegisterConfigRepository;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseSiteService siteService;

    @Resource
    private IPlatformMemberRoleRuleService roleRuleService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    /**
     * 分页查询会员流程规则配置列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberProcessQueryResp> pageMemberProcessRules(HttpHeaders headers, NamePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable pageable = PageRequest.of(pageVO.getCurrent() -1, pageVO.getPageSize(), Sort.by("id").descending());
        Specification<MemberProcessRuleDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            if(StringUtils.hasLength(pageVO.getName())) {
                list.add(criteriaBuilder.like(root.get("ruleName").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberProcessRuleDO> pageList = memberProcessRuleRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberProcessRuleDO -> {
            MemberProcessQueryResp queryVO = new MemberProcessQueryResp();
            queryVO.setId(memberProcessRuleDO.getId());
            // 这里做一下判断，因为下级会员有可能为空
            queryVO.setCreateTime(memberProcessRuleDO.getCreateTime() == null ? "" : memberProcessRuleDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setRuleName(memberProcessRuleDO.getRuleName());
            queryVO.setRoleName(memberProcessRuleDO.getSubRole() == null ? null : memberProcessRuleDO.getSubRole().getRoleName());
            queryVO.setRoleTypeName((ObjectUtil.isNotNull(memberProcessRuleDO.getSubRole()) && ObjectUtil.isNotNull(memberProcessRuleDO.getSubRole().getRoleType())) ? RoleTypeEnum.getName(memberProcessRuleDO.getSubRole().getRoleType()) : null);
            queryVO.setMemberTypeName((ObjectUtil.isNotNull(memberProcessRuleDO.getSubRole()) && ObjectUtil.isNotNull(memberProcessRuleDO.getSubRole().getMemberType())) ? MemberTypeEnum.getName(memberProcessRuleDO.getSubRole().getMemberType()) : null);
            queryVO.setStatus(memberProcessRuleDO.getStatus());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 查询入库流程、变更流程列表
     *
     * @param headers Http头部信息
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public List<BaseMemberProcessResp> listBaseMemberProcesses(HttpHeaders headers, Integer roleTag) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        return memberProcessRepository.findAll()
                .stream().filter(v -> !Arrays.asList(6L,7L,8L,9L,10L).contains(v.getId()))
                .map(memberProcessDO -> {
                    BaseMemberProcessResp processVO = new BaseMemberProcessResp();
                    processVO.setId(memberProcessDO.getId());
                    processVO.setProcessName(MemberProcessEnum.getMessage(memberProcessDO.getProcessCode()));
                    processVO.setProcessType(memberProcessDO.getProcessType());
                    processVO.setProcessTypeName(MemberProcessTypeEnum.getMsg(memberProcessDO.getProcessType()));
                    processVO.setDescription(MemberProcessEnum.getDescription(memberProcessDO.getProcessCode()));
                    return processVO;
                }).sorted(Comparator.comparing(BaseMemberProcessResp::getId))
                .collect(Collectors.toList());
    }

    /**
     * 查询角色列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<RoleManageResp> pageRoles(HttpHeaders headers, NamePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Boolean wrapperResp = siteService.isEnableMultiTenancy(headers);
        if (!wrapperResp) {//如果没有开启saas多租户部署
            return pageRoles(loginUser, pageVO);
        } else {//如果开启saas多租户部署
            //按照指定会员id取当前会员配置的适用类型
            List<RoleRuleManageResp> manageVOList = roleRuleService.subMemberRoles(loginUser.getMemberId());
            if (manageVOList == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_NO_SUITABLE_ROLE);
            }
            //因为数据量很少，所以在内存做筛选
            //计算分页参数
            int size = manageVOList.size();
            int fromIndex = (pageVO.getCurrent() - 1) * pageVO.getPageSize();
            if (fromIndex > size) {
                return new PageDataResp<>((long) size, new ArrayList<>());
            }
            int toIndex = pageVO.getCurrent() * pageVO.getPageSize();
            if (toIndex > size) {
                toIndex = size;
            }

            manageVOList = manageVOList.stream()
                    .filter(roleRuleManageVO -> {
                        if (StringUtils.hasLength(pageVO.getName())) {
                            return roleRuleManageVO.getRoleName().equals(pageVO.getName());
                        }
                        return true;
                    })
                    .sorted(Comparator.comparing(RoleRuleManageResp::getRoleId))
                    .collect(Collectors.toList())
                    .subList(fromIndex, toIndex);

            List<RoleManageResp> roleManageRespList = manageVOList.stream().map(roleRuleManageVO -> {
                RoleManageResp roleManageResp = new RoleManageResp();
                roleManageResp.setRoleId(roleRuleManageVO.getRoleId());
                roleManageResp.setRoleName(roleRuleManageVO.getRoleName());
                roleManageResp.setRoleTypeName(roleRuleManageVO.getRoleTypeName());
                roleManageResp.setMemberTypeName(roleRuleManageVO.getMemberTypeName());
                roleManageResp.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
                return roleManageResp;
            }).collect(Collectors.toList());
            return new PageDataResp<>((long) size, roleManageRespList);
        }
    }

    /**
     * 查询角色列表
     * @param loginUser 登录会员信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<RoleManageResp> pageRoles(UserLoginCacheDTO loginUser, NamePageDataReq pageVO) {
        //规则：
        // 1). 上级会员角色为企业会员，下级会员角色为企业会员：无限制
        // 2). 上级会员角色为企业会员，下级会员角色为渠道会员：不允许创建
        // 3). 上级会员角色为渠道会员，下级会员角色为企业会员：不允许创建
        // 4). 上级会员角色为渠道会员，下级会员角色为渠道会员：判断下级会员是否有另一个服务消费者角色在关系树中

        Pageable pageable = PageRequest.of(pageVO.getCurrent() -1, pageVO.getPageSize(), Sort.by("id").ascending());
        Specification<MemberRoleDO> specification;
        if(loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || loginUser.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode())) {
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
                list.add(criteriaBuilder.notEqual(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
                if(StringUtils.hasLength(pageVO.getName())) {
                    list.add(criteriaBuilder.like(root.get("roleName").as(String.class), "%" + pageVO.getName().trim() + "%"));
                }
                list.add(criteriaBuilder.in(root.get("memberType")).value(Arrays.asList(MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };
        } else {
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
                list.add(criteriaBuilder.notEqual(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
                if (StringUtils.hasLength(pageVO.getName())) {
                    list.add(criteriaBuilder.like(root.get("roleName").as(String.class), "%" + pageVO.getName().trim() + "%"));
                }
                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };
        }

        Page<MemberRoleDO> pageList = roleRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberRoleDO -> {
            RoleManageResp manageVO = new RoleManageResp();
            manageVO.setRoleId(memberRoleDO.getId());
            manageVO.setRoleName(memberRoleDO.getRoleName());
            manageVO.setRoleTypeName(RoleTypeEnum.getName(memberRoleDO.getRoleType()));
            manageVO.setMemberTypeName(MemberTypeEnum.getName(memberRoleDO.getMemberType()));
            manageVO.setStatus(memberRoleDO.getStatus());
            return manageVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 查询平台默认注册资料
     *
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberConfigNameResp> pageRoleConfigDetail(HttpHeaders headers, RoleIdPageDataReq pageVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberRoleDO memberRoleDO = roleRepository.findById(pageVO.getRoleId()).orElse(null);
        if(memberRoleDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        Set<Long> configIdSet = memberRoleDO.getConfigs().stream().map(MemberRegisterConfigDO::getId).collect(Collectors.toSet());

        QMemberRegisterConfigDO qMemberRegisterConfigDO = QMemberRegisterConfigDO.memberRegisterConfigDO;
        QMemberRegisterConfigNameDO qMemberRegisterConfigNameDO = QMemberRegisterConfigNameDO.memberRegisterConfigNameDO;

        JPAQuery<MemberRegisterConfigDO> jpaQuery = jpaQueryFactory.select(qMemberRegisterConfigDO)
                .from(qMemberRegisterConfigDO)
                .where(qMemberRegisterConfigDO.parentId.isNull().or(qMemberRegisterConfigDO.parentId.eq(0L)))
                .where(qMemberRegisterConfigDO.fieldStatus.eq(EnableDisableStatusEnum.ENABLE.getCode()))
                .where(qMemberRegisterConfigDO.id.in(configIdSet));

        if (StringUtils.hasLength(pageVO.getName())) {
            jpaQuery.where(qMemberRegisterConfigDO.id.in(
                    JPAExpressions.select(qMemberRegisterConfigNameDO.memberRegisterConfig.id)
                            .from(qMemberRegisterConfigNameDO)
                            .where(qMemberRegisterConfigNameDO.name.like("%" + pageVO.getName().trim() + "%")
                                    .and(qMemberRegisterConfigNameDO.fieldType.eq(MemberRegisterConfigNameEnum.FIELD_LOCAL_NAME.getCode()))
                                    .and(qMemberRegisterConfigNameDO.language.eq(LanguageEnum.getCurrentLanguage())))));
        }

        jpaQuery.limit(pageVO.getPageSize())
                .offset((long) (pageVO.getCurrent() - 1) * pageVO.getPageSize())
                .orderBy(qMemberRegisterConfigDO.id.asc());

        return new PageDataResp<>(jpaQuery.fetchCount(), jpaQuery.fetch().stream().map(memberConfigDO -> {
            MemberConfigNameResp nameVO = new MemberConfigNameResp();
            nameVO.setId(memberConfigDO.getId());
            nameVO.setFieldLocalName(RgConfigUtil.getFieldLocalName(memberConfigDO));
            nameVO.setGroupName(RgConfigUtil.getFieldGroupName(memberConfigDO));
            return nameVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 选择注册资料
     *
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberConfigNameResp> pageConfigDetail(HttpHeaders headers, RoleIdPageDataReq pageVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberRoleDO memberRoleDO = roleRepository.findById(pageVO.getRoleId()).orElse(null);
        if(memberRoleDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //根据当前会员角色关联的注册资料进行排除
//        List<Long> configIds = memberRoleDO.getConfigs().stream().map(MemberRegisterConfigDO::getId).collect(Collectors.toList());

        QMemberRegisterConfigDO qMemberRegisterConfigDO = QMemberRegisterConfigDO.memberRegisterConfigDO;
        QMemberRegisterConfigNameDO qMemberRegisterConfigNameDO = QMemberRegisterConfigNameDO.memberRegisterConfigNameDO;

        JPAQuery<MemberRegisterConfigDO> jpaQuery = jpaQueryFactory.select(qMemberRegisterConfigDO)
                .from(qMemberRegisterConfigDO)
                .where(qMemberRegisterConfigDO.parentId.isNull().or(qMemberRegisterConfigDO.parentId.eq(0L)))
                .where(qMemberRegisterConfigDO.fieldStatus.eq(EnableDisableStatusEnum.ENABLE.getCode()));

//        if(!CollectionUtils.isEmpty(configIds)) {
//            jpaQuery.where(qMemberRegisterConfigDO.id.notIn(configIds));
//        }

        if (StringUtils.hasLength(pageVO.getName())) {
            jpaQuery.where(qMemberRegisterConfigDO.id.in(
                    JPAExpressions.select(qMemberRegisterConfigNameDO.memberRegisterConfig.id)
                            .from(qMemberRegisterConfigNameDO)
                            .where(qMemberRegisterConfigNameDO.name.like("%" + pageVO.getName().trim() + "%")
                                    .and(qMemberRegisterConfigNameDO.fieldType.eq(MemberRegisterConfigNameEnum.FIELD_LOCAL_NAME.getCode()))
                                    .and(qMemberRegisterConfigNameDO.language.eq(LanguageEnum.getCurrentLanguage())))));
        }

        jpaQuery.limit(pageVO.getPageSize())
                .offset((long) (pageVO.getCurrent() - 1) * pageVO.getPageSize())
                .orderBy(qMemberRegisterConfigDO.id.asc());

        return new PageDataResp<>(jpaQuery.fetchCount(), jpaQuery.fetch().stream().map(memberConfigDO -> {
            MemberConfigNameResp nameVO = new MemberConfigNameResp();
            nameVO.setAllowSelect(memberConfigDO.getAllowSelect());
            nameVO.setId(memberConfigDO.getId());
            nameVO.setFieldLocalName(RgConfigUtil.getFieldLocalName(memberConfigDO));
            nameVO.setGroupName(RgConfigUtil.getFieldGroupName(memberConfigDO));
            nameVO.setFieldTypeName(RgConfigUtil.getFieldTypeName(memberConfigDO));
            nameVO.setValidate(memberConfigDO.getValidate());
            nameVO.setAllowSelect(memberConfigDO.getAllowSelect());
            return nameVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 预览注册资料
     *
     * @param headers   Http头部信息
     * @param previewVO 接口参数
     * @return 预览结果
     */
    @Override
    public List<MemberConfigGroupResp> previewMemberDepositoryDetails(HttpHeaders headers, MemberProcessPreviewReq previewVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRoleDO memberRoleDO = roleRepository.findById(previewVO.getRoleId()).orElse(null);
        if(memberRoleDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        if(CollectionUtils.isEmpty(previewVO.getConfigIds())) {
            return new ArrayList<>();
        }

        List<MemberRegisterConfigDO> memberConfigs = memberRegisterConfigRepository.findByFieldStatusAndIdIn(EnableDisableStatusEnum.ENABLE.getCode(), previewVO.getConfigIds());

        return baseMemberRegisterDetailService.groupMemberConfig(memberConfigs);
    }

    /**
     * 新增流程规则配置
     *
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 新增结果
     */
    @Override
    public void addMemberProcessRule(HttpHeaders headers, MemberProcessAddReq addVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        if(NumberUtil.isNullOrZero(addVO.getDepositoryProcessId()) && NumberUtil.isNullOrZero(addVO.getChangedProcessId())) {


            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_PROCESS_RULE_CAN_NOT_EMPLY);
        }

        MemberRoleDO subRole = roleRepository.findById(addVO.getRoleId()).orElse(null);
        if(subRole == null) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //判断角色流程是否已经存在
        if(memberProcessRuleRepository.existsByMemberIdAndRoleIdAndSubRole(loginUser.getMemberId(), loginUser.getMemberRoleId(), subRole)) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_PROCESS_RULE_EXIST);
        }

        MemberProcessDO baseDepositoryProcess = null;
        if(NumberUtil.notNullOrZero(addVO.getDepositoryProcessId())) {
            baseDepositoryProcess = memberProcessRepository.findById(addVO.getDepositoryProcessId()).orElse(null);
            if(baseDepositoryProcess == null || !baseDepositoryProcess.getProcessType().equals(MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode())) {


                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_PROCESS_RULE_NOT_EXIST);
            }
        }

        MemberProcessDO baseDetailChangeProcess = null;
        if(NumberUtil.notNullOrZero(addVO.getChangedProcessId())) {
            baseDetailChangeProcess = memberProcessRepository.findById(addVO.getChangedProcessId()).orElse(null);
            if(baseDetailChangeProcess == null || !baseDetailChangeProcess.getProcessType().equals(MemberProcessTypeEnum.MEMBER_MODIFICATION.getCode())) {


                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DETAIL_CHANGE_PROCESS_RULE_NOT_EXIST);
            }
        }

        List<MemberRegisterConfigDO> memberConfigs = new ArrayList<>();
        if(!CollectionUtils.isEmpty(addVO.getConfigIds())) {
            memberConfigs = memberRegisterConfigRepository.findByFieldStatusAndIdIn(EnableDisableStatusEnum.ENABLE.getCode(), addVO.getConfigIds());
            if(addVO.getConfigIds().size() != memberConfigs.size()) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_DETAIL_NOT_EXIST);
            }
        }

        MemberProcessRuleDO ruleDO = new MemberProcessRuleDO();
        ruleDO.setCreateTime(LocalDateTime.now());
        ruleDO.setMemberId(loginUser.getMemberId());
        ruleDO.setRoleId(loginUser.getMemberRoleId());
        ruleDO.setSubRole(subRole);
        ruleDO.setRuleName(addVO.getRuleName());
        ruleDO.setBaseDepositoryProcessId(baseDepositoryProcess == null ? 0L : baseDepositoryProcess.getId());
        ruleDO.setBaseDepositoryProcessKey(baseDepositoryProcess == null ? "" : baseDepositoryProcess.getProcessKey());
        ruleDO.setEmptyDepositoryProcess(baseDepositoryProcess == null ? null : baseDepositoryProcess.getEmptyProcess());
        ruleDO.setBaseDetailChangeProcessId(baseDetailChangeProcess == null ? 0L : baseDetailChangeProcess.getId());
        ruleDO.setBaseDetailChangeProcessKey(baseDetailChangeProcess == null ? "" : baseDetailChangeProcess.getProcessKey());
        ruleDO.setEmptyDetailChangeProcess(baseDetailChangeProcess == null ? null : baseDetailChangeProcess.getEmptyProcess());
        ruleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        ruleDO.setConfigs(new HashSet<>(memberConfigs));
        memberProcessRuleRepository.saveAndFlush(ruleDO);

    }

    /**
     * 修改流程规则配置
     *
     * @param headers  Http头部信息
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @Override
    public void updateMemberProcessRule(HttpHeaders headers, MemberProcessUpdateReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        if(NumberUtil.isNullOrZero(updateVO.getDepositoryProcessId()) && NumberUtil.isNullOrZero(updateVO.getChangedProcessId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_PROCESS_RULE_CAN_NOT_EMPLY);
        }

        MemberProcessRuleDO ruleDO = memberProcessRuleRepository.findById(updateVO.getId()).orElse(null);
        if(ruleDO == null || !ruleDO.getMemberId().equals(loginUser.getMemberId()) || !ruleDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_PROCESS_RULE_NOT_EXIST);
        }

        MemberRoleDO subRole = roleRepository.findById(updateVO.getRoleId()).orElse(null);
        if(subRole == null) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //判断角色流程是否已经存在
        if(memberProcessRuleRepository.existsByMemberIdAndRoleIdAndSubRoleAndIdNot(loginUser.getMemberId(), loginUser.getMemberRoleId(), subRole, updateVO.getId())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_PROCESS_RULE_EXIST);


        }

        MemberProcessDO baseDepositoryProcess = null;
        if(NumberUtil.notNullOrZero(updateVO.getDepositoryProcessId())) {
            baseDepositoryProcess = memberProcessRepository.findById(updateVO.getDepositoryProcessId()).orElse(null);
            if(baseDepositoryProcess == null || !baseDepositoryProcess.getProcessType().equals(MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode())) {

                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_PROCESS_RULE_NOT_EXIST);
            }
        }

        MemberProcessDO baseDetailChangeProcess = null;
        if(NumberUtil.notNullOrZero(updateVO.getChangedProcessId())) {
            baseDetailChangeProcess = memberProcessRepository.findById(updateVO.getChangedProcessId()).orElse(null);
            if(baseDetailChangeProcess == null || !baseDetailChangeProcess.getProcessType().equals(MemberProcessTypeEnum.MEMBER_MODIFICATION.getCode())) {

                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DETAIL_CHANGE_PROCESS_RULE_NOT_EXIST);
            }
        }

        List<MemberRegisterConfigDO> memberConfigs = new ArrayList<>();
        if(!CollectionUtils.isEmpty(updateVO.getConfigIds())) {
            memberConfigs = memberRegisterConfigRepository.findByFieldStatusAndIdIn(EnableDisableStatusEnum.ENABLE.getCode(), updateVO.getConfigIds());
            if (updateVO.getConfigIds().size() != memberConfigs.size()) {


                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_DETAIL_NOT_EXIST);
            }
        }

        //删除之前的入库资料
        if(!CollectionUtils.isEmpty(ruleDO.getConfigs())) {
            ruleDO.getConfigs().clear();
        }

        ruleDO.setCreateTime(LocalDateTime.now());
        ruleDO.setSubRole(subRole);
        ruleDO.setRuleName(updateVO.getRuleName());
        ruleDO.setBaseDepositoryProcessId(baseDepositoryProcess == null ? 0L : baseDepositoryProcess.getId());
        ruleDO.setBaseDepositoryProcessKey(baseDepositoryProcess == null ? "" : baseDepositoryProcess.getProcessKey());
        ruleDO.setEmptyDepositoryProcess(baseDepositoryProcess == null ? null : baseDepositoryProcess.getEmptyProcess());
        ruleDO.setBaseDetailChangeProcessId(baseDetailChangeProcess == null ? 0L : baseDetailChangeProcess.getId());
        ruleDO.setBaseDetailChangeProcessKey(baseDetailChangeProcess == null ? "" : baseDetailChangeProcess.getProcessKey());
        ruleDO.setEmptyDetailChangeProcess(baseDetailChangeProcess == null ? null : baseDetailChangeProcess.getEmptyProcess());
        ruleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        ruleDO.getConfigs().clear();
        ruleDO.setConfigs(new HashSet<>(memberConfigs));
        memberProcessRuleRepository.saveAndFlush(ruleDO);

    }

    /**
     * 查询流程规则配置详情
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public MemberProcessResp getMemberProcessRule(HttpHeaders headers, MemberProcessIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberProcessRuleDO ruleDO = memberProcessRuleRepository.findById(idVO.getId()).orElse(null);
        if(ruleDO == null || !ruleDO.getMemberId().equals(loginUser.getMemberId()) || !ruleDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_PROCESS_RULE_NOT_EXIST);
        }

        MemberProcessResp processVO = new MemberProcessResp();
        processVO.setId(ruleDO.getId());
        processVO.setRuleName(ruleDO.getRuleName());
        processVO.setRoleId(ruleDO.getSubRole().getId());
        processVO.setRoleName(ruleDO.getSubRole().getRoleName());
        processVO.setRoleTypeName(RoleTypeEnum.getName(ruleDO.getSubRole().getRoleType()));
        processVO.setMemberTypeName(MemberTypeEnum.getName(ruleDO.getSubRole().getMemberType()));
        processVO.setDepositoryProcessId(ruleDO.getBaseDepositoryProcessId());
        processVO.setChangedProcessId(ruleDO.getBaseDetailChangeProcessId());

        processVO.setDetails(ruleDO.getConfigs().stream().map(config -> {
            MemberConfigNameResp nameVO = new MemberConfigNameResp();
            nameVO.setId(config.getId());
            nameVO.setFieldLocalName(RgConfigUtil.getFieldLocalName(config));
            nameVO.setGroupName(RgConfigUtil.getFieldGroupName(config));
            nameVO.setAllowSelect(config.getAllowSelect());
            nameVO.setValidate(config.getValidate());
            nameVO.setFieldTypeName(RgConfigUtil.getFieldTypeName(config));
            return nameVO;
        }).sorted(Comparator.comparingLong(MemberConfigNameResp::getId)).collect(Collectors.toList()));

        return processVO;
    }

    /**
     * 分页查询流程规则配置关联的入库资料
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberConfigNameResp> pageMemberProcessDepositDetails(HttpHeaders headers, MemberProcessDepositDetailPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberProcessRuleDO ruleDO = memberProcessRuleRepository.findById(pageVO.getId()).orElse(null);
        if(ruleDO == null || !ruleDO.getMemberId().equals(loginUser.getMemberId()) || !ruleDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_PROCESS_RULE_NOT_EXIST);
        }

        List<MemberRegisterConfigDO> configList = StringUtils.hasLength(pageVO.getName()) ? ruleDO.getConfigs().stream().sorted(Comparator.comparingLong(MemberRegisterConfigDO::getId)).filter(memberConfigDO -> RgConfigUtil.getFieldLocalName(memberConfigDO).equalsIgnoreCase(pageVO.getName().trim())).collect(Collectors.toList()) : ruleDO.getConfigs().stream().sorted(Comparator.comparingLong(MemberRegisterConfigDO::getId)).collect(Collectors.toList());

        long totalCount = configList.size();
        List<MemberRegisterConfigDO> pageList = CollectionPageUtil.pageList(configList, pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(totalCount, pageList.stream().map(config -> {
            MemberConfigNameResp nameVO = new MemberConfigNameResp();
            nameVO.setId(config.getId());
            nameVO.setFieldLocalName(RgConfigUtil.getFieldLocalName(config));
            nameVO.setGroupName(RgConfigUtil.getFieldGroupName(config));
            return nameVO;
        }).collect(Collectors.toList()));
    }


    /**
     * 修改流程规则状态
     *
     * @param headers  Http头部信息
     * @param statusVO 接口参数
     * @return 修改结果
     */
    @Override
    public void updateMemberProcessRuleStatus(HttpHeaders headers, MemberProcessIdAndStatusReq statusVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberProcessRuleDO ruleDO = memberProcessRuleRepository.findById(statusVO.getId()).orElse(null);
        if(ruleDO == null || !ruleDO.getMemberId().equals(loginUser.getMemberId()) || !ruleDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_PROCESS_RULE_NOT_EXIST);
        }

        ruleDO.setStatus(statusVO.getStatus());
        memberProcessRuleRepository.saveAndFlush(ruleDO);


    }

    /**
     * 删除流程规则
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 删除结果
     */
    @Override
    public void deleteMemberProcessRule(HttpHeaders headers, MemberProcessIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberProcessRuleDO ruleDO = memberProcessRuleRepository.findById(idVO.getId()).orElse(null);
        if(ruleDO == null || !ruleDO.getMemberId().equals(loginUser.getMemberId()) || !ruleDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_PROCESS_RULE_NOT_EXIST);
        }

        memberProcessRuleRepository.delete(ruleDO);


    }

    /**
     * 查找流程ProcessKey，如上级会员没有配置，返回默认的流程规则
     *
     * @param relationDO      会员关系
     * @param processTypeEnum 流程类型枚举
     * @return 流程ProcessKey
     */
    @Override
    public ProcessBO findMemberProcessKey(MemberRelationDO relationDO, MemberProcessTypeEnum processTypeEnum) {
        return findMemberProcessKey(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRole(), processTypeEnum);
    }

    /**
     * 查找流程ProcessKey
     *
     * @param memberId        上级会员Id
     * @param roleId          下级会员Id
     * @param subRole         下级会员角色
     * @param processTypeEnum 流程类型枚举
     * @return 流程ProcessKey
     */
    @Override
    public ProcessBO findMemberProcessKey(Long memberId, Long roleId, MemberRoleDO subRole, MemberProcessTypeEnum processTypeEnum) {
        String processKey = "";
        Boolean emptyProcess = true;
        switch (processTypeEnum) {
            case MEMBER_DEPOSITORY:
                MemberProcessRuleDO depositoryProcessRuleDO = memberProcessRuleRepository.findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(memberId, roleId, subRole, EnableDisableStatusEnum.ENABLE.getCode());
                if(depositoryProcessRuleDO != null && StringUtils.hasLength(depositoryProcessRuleDO.getBaseDepositoryProcessKey())) {
                    processKey = depositoryProcessRuleDO.getBaseDepositoryProcessKey();
                    emptyProcess = depositoryProcessRuleDO.getEmptyDepositoryProcess() != null && depositoryProcessRuleDO.getEmptyDepositoryProcess();
                } else {
                    MemberProcessDO memberProcessDO = memberProcessRepository.findFirstByProcessTypeAndIsDefault(processTypeEnum.getCode(), true);
                    if(memberProcessDO != null) {
                        processKey = memberProcessDO.getProcessKey();
                        emptyProcess = memberProcessDO.getEmptyProcess();
                    }
                }
                break;
            case MEMBER_MODIFICATION:
                MemberProcessRuleDO detailChangeProcessRuleDO = memberProcessRuleRepository.findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(memberId, roleId, subRole, EnableDisableStatusEnum.ENABLE.getCode());
                if(detailChangeProcessRuleDO != null && StringUtils.hasLength(detailChangeProcessRuleDO.getBaseDetailChangeProcessKey())) {
                    processKey = detailChangeProcessRuleDO.getBaseDetailChangeProcessKey();
                    emptyProcess = detailChangeProcessRuleDO.getEmptyDetailChangeProcess() != null && detailChangeProcessRuleDO.getEmptyDetailChangeProcess();
                } else {
                    MemberProcessDO memberProcessDO = memberProcessRepository.findFirstByProcessTypeAndIsDefault(processTypeEnum.getCode(), true);
                    if(memberProcessDO != null) {
                        processKey = memberProcessDO.getProcessKey();
                        emptyProcess = memberProcessDO.getEmptyProcess();
                    }
                }
                break;
            default:
                break;
        }

        return new ProcessBO(processKey, emptyProcess);
    }

    /**
     * 下级会员角色是否存在非空的入库资料配置
     *
     * @param memberId        上级会员Id
     * @param roleId          上级会员角色Id
     * @param subRoles        下级会员角色列表
     * @return 是/否
     */
    @Override
    public boolean existDepositoryConfig(Long memberId, Long roleId, List<MemberRoleDO> subRoles) {
        return memberProcessRuleRepository.existsByMemberIdAndRoleIdAndSubRoleInAndBaseDepositoryProcessIdGreaterThanAndConfigsIsNotNull(memberId, roleId, subRoles, 0L);
    }
}
