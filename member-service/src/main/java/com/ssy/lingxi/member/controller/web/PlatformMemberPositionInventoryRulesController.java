package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberCustomerQueryReq;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberMaterialInventoryUpdateReq;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberPositionInventoryRulesQueryDataReq;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberPositionInventoryUpdateReq;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberCustomerResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformPageQueryPositionInventoryRulesMemberResp;
import com.ssy.lingxi.member.service.web.IPlatformMemberPositionInventoryRulesService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 平台规则 - 会员仓位同步物料库存规则
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/25
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/positionInventory/rules")
public class PlatformMemberPositionInventoryRulesController {

    @Resource
    private IPlatformMemberPositionInventoryRulesService platformMemberPositionInventoryRulesService;

    /**
     * 分页、模糊查询会员信息列表
     *
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<PlatformPageQueryPositionInventoryRulesMemberResp>> pageMembers(@RequestHeader HttpHeaders headers, @Valid PlatformMemberPositionInventoryRulesQueryDataReq queryVO) {
        return WrapperUtil.success(platformMemberPositionInventoryRulesService.pageMembers(headers, queryVO));
    }

    /**
     * 设置仓位仓库模式
     *
     * @param headers  HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/updatePositionInventory")
    public WrapperResp<Void> updatePositionInventory(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformMemberPositionInventoryUpdateReq updateVO) {
         platformMemberPositionInventoryRulesService.updatePositionInventory(headers, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 设置物料仓库模式
     *
     * @param headers  HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/updateMaterialInventory")
    public WrapperResp<Void> updateMaterialInventory(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformMemberMaterialInventoryUpdateReq updateVO) {
         platformMemberPositionInventoryRulesService.updateMaterialInventory(headers, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 获取会员全部服务消费者类型名称
     *
     * @param headers  HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/list")
    public WrapperResp<List<PlatformMemberCustomerResp>> listMembersCustomer(@RequestHeader HttpHeaders headers, @Valid PlatformMemberCustomerQueryReq queryVO) {
        return WrapperUtil.success(platformMemberPositionInventoryRulesService.listMembersCustomer(headers, queryVO));
    }

}
