package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 保存默认流程
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-06
 **/
@Data
public class SaveDefaultReq implements Serializable {

    private static final long serialVersionUID = 4400657387842194227L;

    /**
     * 流程ID
     */
    @NotNull(message = "流程ID不能为空")
    private Long processId;

}
