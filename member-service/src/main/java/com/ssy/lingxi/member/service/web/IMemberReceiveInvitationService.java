package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;

import com.ssy.lingxi.member.entity.do_.invitation.MemberReceiveInvitationDO;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.resp.basic.MemberInvitationCodeQueryResp;
import com.ssy.lingxi.member.model.resp.basic.MemberReceiveInvitationQueryResp;
import com.ssy.lingxi.member.model.resp.basic.MemberSendInvitationDetailResp;
import com.ssy.lingxi.member.model.resp.basic.MemberSendInvitationQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.StatusResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 接收邀请服务类
 * <AUTHOR>
 * @since 2022/6/30 16:15
 * @version 1.0
 */
public interface IMemberReceiveInvitationService {

    /**
     * 保存邀请
     * @param memberReceiveInvitationDO 接收邀请实体类
     */
    void saveInvitation(MemberReceiveInvitationDO memberReceiveInvitationDO);

    /**
     * 我收到的邀请信息 - 列表查询
     * @param headers  Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberReceiveInvitationQueryResp> receivePage(HttpHeaders headers, MemberReceiveInvitationPageDataReq pageVO);

    /**
     * 客户/供应商邀请 - 列表查询
     * @param headers  Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberSendInvitationQueryResp> sendPage(HttpHeaders headers, MemberSendInvitationPageDataReq pageVO, Integer roleTag);

    /**
     * 客户/供应商邀请 - 新增
     * @param headers  Http头部信息
     * @param invitationVO 接口参数
     * @param roleTag 角色标签
     */
    void add(HttpHeaders headers, MemberSendInvitationReq invitationVO, Integer roleTag);

    /**
     * 客户/供应商邀请 - 详情
     * @param headers  Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberSendInvitationDetailResp detail(HttpHeaders headers, MemberSendInvitationEmailReq idVO, Integer roleTag);

    /**
     * 客户/供应商邀请 - 更新
     * @param headers  Http头部信息
     * @param updateVO 接口参数
     */
    void update(HttpHeaders headers, MemberSendInvitationUpdateReq updateVO, Integer roleTag);

    /**
     * 客户/供应商邀请 - 发送邀请码
     * @param headers  Http头部信息
     * @param sendVO 接口参数
     * @return 查询结果
     */
    void send(HttpHeaders headers, MemberSendInvitationEmailReq sendVO, Integer roleTag);

    /**
     * 更新邀请码状态
     * @param invitationId 邀请id
     */
    void updateInvitationCode(Long invitationId);

    /**
     * 根据邀请码获取信息
     * @param headers  Http头部信息
     * @param invitationCodeVO 接口参数
     * @return 查询结果
     */
    MemberInvitationCodeQueryResp info(HttpHeaders headers, MemberInvitationCodeReq invitationCodeVO);

    /**
     * 邀请状态下拉查询
     * @return 查询结果
     */
    List<StatusResp> stateList();

    /**
     * 供应商邀请 - 删除
     * @param headers  Http头部信息
     * @param idVO 接口参数
     */
    void delete(HttpHeaders headers, MemberSendInvitationIdReq idVO, Integer roleTag);
}
