package com.ssy.lingxi.member.service.web.comment;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderCommentDO;
import com.ssy.lingxi.member.enums.MemberTradeTypeEnum;
import com.ssy.lingxi.member.model.req.comment.*;
import com.ssy.lingxi.member.model.resp.comment.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditCommentSummaryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditTradeHistoryResp;

/**
 * 会员评价服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
public interface IMemberCommentService {

    /**
     * 交易能力 - 会员评价管理 - 订单商品评价详情
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param commonIdReq 接口参数
     * @return 查询结果
     */
    MemberOrderCommentDetailResp getMemberOrderCommentDetail(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, CommonIdReq commonIdReq);

    /**
     * 交易能力 - 会员评价管理 - 发表评价
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param commentSubmitVO 接口参数
     * @return 查询结果
     */
    void submitMemberTradeComment(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, MemberTradeCommentSubmitReq commentSubmitVO);

    /**
     * 系统定时自动评价
     *
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param memberOrderCommentDO 被评价的订单
     * @param commentSubmitVO 订单的评价
     * @return 查询结果
     */
    void submitAutoMemberTradeComment(MemberTradeTypeEnum memberTradeTypeEnum, MemberOrderCommentDO memberOrderCommentDO, MemberTradeCommentSubmitReq commentSubmitVO);

    /**
     * 交易能力 - 会员评价管理 - 收到评价分页列表
     * 如果采购会员查看, 评价方角色是供应会员
     * 如果供应会员查看, 评价方角色是采购会员
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberReceiveTradeCommentPageResp> pageMemberReceiveTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, MemberReceiveTradeCommentDataReq pageVO);

    /**
     * 交易能力 - 会员评价管理 - 发出评价分页列表
     * 如果采购会员查看, 评价方角色是采购会员
     * 如果供应会员查看, 评价方角色是供应会员
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberSendTradeCommentPageResp> pageMemberSendTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, MemberSendTradeCommentDataReq pageVO);

    /**
     * 交易能力 - 采购会员评价管理 - 修改评价详情
     * @param tradeCommentSubmitVO 接口参数
     */
    void updateMemberTradeCommentHistory(MemberTradeCommentUpdateDetailReq tradeCommentSubmitVO);

    /**
     * 交易能力 - 评价管理 - 评价详情
     *
     * @param loginUser 登录用户信息
     * @param tradeCommentIdVO 接口参数
     * @return 查询结果
     */
    MemberTradeCommentDetailResp getMemberTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeCommentIdReq tradeCommentIdVO);

    /**
     * 商城能力 - 店铺渠道商城 - 现货商品详情 - 商户总体满意度
     * @param pageVO 接口参数
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp pageOrderProductTradeCommentSummary(OrderProductTradeCommentReq pageVO);

    /**
     * 商城能力 - 店铺渠道商城 - 现货商品详情 - 交易评价分页列表(只显示采购商的评价)
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<ProductOrderTradeCommentPageResp> pageOrderProductTradeCommentHistory(OrderProductTradeCommentQueryDataReq pageVO);

    /**
     * 交易能力 - 会员评价管理 - 交易评价汇总
     * 如果采购会员查看, 评价方角色是供应会员
     * 如果供应会员查看, 评价方角色是采购会员
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getSubMemberTradeCommentSummary(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum);

    /**
     * 交易能力 - 会员评价管理 - 分页查询交易评论历史记录
     * 如果采购会员查看, 评价方角色是供应会员
     * 如果供应会员查看, 评价方角色是采购会员
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditTradeHistoryResp> pageSubMemberTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, MemberHistoryPageDataReq pageVO);

    /**
     * 交易能力-供应会员评价管理-收到评价-解释回复
     * @param loginUser 登录用户信息
     * @param memberTradeCommentSubmitVO 接口参数
     * @return 查询结果
     */
    void replyReceiveMemberTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeReceiveCommentReplyReq memberTradeCommentSubmitVO);

    /**
     * 交易能力-供应会员评价管理-收到评价-修改是否显示商品评价
     * @param updateVO 接口参数
     * @return 查询结果
     */
    void updateSupplyReceiveShowEvaluation(MemberTradeCommentUpdateStatusReq updateVO);
}
