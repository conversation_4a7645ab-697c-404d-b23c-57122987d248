package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.member.model.req.basic.UpperRelationIdReq;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 会员入库 - 根据省编码查询市列表接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-19
 */
public class MemberDepositGetCitiesReq extends UpperRelationIdReq implements Serializable {
    private static final long serialVersionUID = 3643916125941906068L;

    /**
     * 省编码
     */
//    @NotBlank(message = "省编码不能为空")
    private String provinceCode;

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }
}
