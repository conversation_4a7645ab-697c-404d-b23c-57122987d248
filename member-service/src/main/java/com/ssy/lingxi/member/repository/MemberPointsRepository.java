package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.MemberPointsDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * (MemberPoints)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-08-08 10:25:36
 */
@Repository
public interface MemberPointsRepository extends JpaRepository<MemberPointsDO,Long>, JpaSpecificationExecutor<MemberPointsDO>, QuerydslPredicateExecutor<MemberPointsDO> {

}
