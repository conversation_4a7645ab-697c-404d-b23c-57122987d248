package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 查询流程规则配置添加的入库资料接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-06-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberProcessDepositDetailPageDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -1782104568320219133L;

    /**
     * 流程规则Id
     */
    @NotNull(message = "流程规则Id要大于0")
    @Positive(message = "流程规则Id要大于0")
    private Long id;

    /**
     * 入库资料名称
     */
    private String name;
}
