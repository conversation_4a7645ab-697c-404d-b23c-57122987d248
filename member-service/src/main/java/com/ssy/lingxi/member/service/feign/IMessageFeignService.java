package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.component.rabbitMQ.model.req.SystemMessageReq;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberReceiveInvitationDO;
import com.ssy.lingxi.member.model.dto.MemberAppraisalMessageDTO;
import com.ssy.lingxi.member.model.dto.MemberChangeRequestFormMessageDTO;

import java.util.List;

/**
 * 调用消息服务Feign接口
 * <AUTHOR>
 * @since 2021/6/23 15:47
 **/
public interface IMessageFeignService {

    /**
     * 会员消息
     */
    SystemMessageReq buildSystemMessage(Long memberId, Long roleId, String messageCode, List<String> params);

    /**
     * 会员用户消息
     */
    SystemMessageReq buildUserSystemMessage(Long memberId, Long roleId, Long userId, String messageCode, List<String> params);

    /**
     * 发送会员消息
     * @param memberId 会员id
     * @param roleId 角色id
     * @param messageCode 消息编码
     * @param params 参数
     */
    void sendSystemMessage(Long memberId, Long roleId, String messageCode, List<String> params);

    /**
     * 发送会员消息(批量)
     * @param request 接口参数
     */
    void batchSendSystemMessage(List<SystemMessageReq> request);

    /**
     * 发送会员用户消息
     * @param memberId 会员id
     * @param roleId 角色id
     * @param userId 用戶id
     * @param messageCode 消息编码
     * @param params 参数
     */
    void sendUserSystemMessage(Long memberId, Long roleId, Long userId, String messageCode, List<String> params);

    /**
     * 平台会员审核、会员入库审核时，发送消息
     * @param relation 会员关系
     * @param memberId 消息接收方会员Id
     * @param roleId   消息接收方会员角色Id
     */
    void sendMemberValidateMessage(MemberRelationDO relation, Long memberId, Long roleId);

    /**
     * 平台会员审核、供应商入库审核时，发送消息
     *
     * @param relation 供应商关系
     * @param memberId 会员ID
     * @param roleId 角色ID
     */
    void sendSupplierValidateMessage(MemberRelationDO relation, Long memberId, Long roleId);

    /**
     * 平台会员审核、会员入库审核时，发送消息
     *
     * @param relation 会员关系
     * @param roleTag  角色标签
     */
    void sendMemberValidateMessage(MemberRelationDO relation, Integer roleTag);

    /**
     * 平台会员审核、客户入库审核时，发送消息
     *
     * @param relation 供应商关系
     * @param memberId 会员ID
     * @param roleId 角色ID
     */
    void sendCustomerValidateMessage(MemberRelationDO relation, Long memberId, Long roleId);

    /**
     * 发送邀请消息
     * @param memberReceiveInvitation 邀请消息实体
     * @param roleTag 角色标签
     */
    void sendMemberInvitationMessage(MemberReceiveInvitationDO memberReceiveInvitation, Integer roleTag);

    /**
     * 发送考评消息
     *
     * @param appraisalDTO 会员考评DTO
     * @param userId       用户id （为空时代表发送系统消息）
     * @param roleTag      角色标签
     */
    void sendMemberAppraisalMessage(MemberAppraisalMessageDTO appraisalDTO, Long userId, Integer roleTag);

    /**
     * 发送生命周期变更申请消息
     *
     * @param changeRequestFormDTO 会员考评DTO
     * @param userId               用户id （为空时代表发送系统消息）
     * @param roleTag              角色标签
     */
    void sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO changeRequestFormDTO, Long userId, Integer roleTag);
}
