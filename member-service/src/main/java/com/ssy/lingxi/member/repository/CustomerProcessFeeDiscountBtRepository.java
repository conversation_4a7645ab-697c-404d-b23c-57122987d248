package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.discount.CustomerProcessFeeDiscountBtDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 客户工费优惠表体明细Jpa仓库
 */
@Repository
public interface CustomerProcessFeeDiscountBtRepository extends JpaRepository<CustomerProcessFeeDiscountBtDO, Long>, JpaSpecificationExecutor<CustomerProcessFeeDiscountBtDO> {
}

