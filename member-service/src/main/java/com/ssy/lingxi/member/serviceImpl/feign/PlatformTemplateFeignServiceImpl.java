package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.commodity.api.feign.IStoreFeign;
import com.ssy.lingxi.commodity.api.model.dto.StoreDTO;
import com.ssy.lingxi.commodity.api.model.dto.UpdateShopStatusDTO;
import com.ssy.lingxi.member.service.feign.ITemplateFeignService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 调用店铺模板服务Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-09
 */
@Service
public class PlatformTemplateFeignServiceImpl implements ITemplateFeignService {
    private static final Logger logger = LoggerFactory.getLogger(PlatformTemplateFeignServiceImpl.class);

    @Resource
    private IStoreFeign storeFeign;

    /**
     * 当平台会员信用信息改变时，通知店铺模板服务
     *
     * @param memberId          平台会员Id
     * @param roleId            会员角色Id
     * @param memberName        会员名称
     * @param registeredCapital 注册资本
     * @param establishmentDate 成立日期
     * @param businessLicence   营业执照
     * @param registerArea      注册区域
     * @param registerAddress   注册地址
     */
    @Async
    @Override
    public void notifyRegisterDetailChangedAsync(Long memberId, Long roleId, String memberName, String registeredCapital, String establishmentDate, String businessLicence, String registerArea, String registerAddress) {
        try {
            StoreDTO dto = new StoreDTO();
            dto.setMemberId(memberId);
            dto.setRoleId(roleId);
            dto.setMemberName(memberName);
            dto.setRegisteredCapital(registeredCapital);
            dto.setEstablishmentDate(establishmentDate);
            dto.setBusinessLicence(businessLicence);
            dto.setRegisterArea(registerArea);
            dto.setRegisterAddress(registerAddress);
            storeFeign.updateShop(dto);
        } catch (Exception e) {
            logger.error("平台会员注册资料改变时，通知店铺模板服务错误", e);
        }
    }

    /**
     * 当平台会员信用信息改变时，通知店铺模板服务
     * @param memberId            平台会员Id
     * @param roleId              会员角色Id
     * @param memberName          会员名称
     * @param creditPoint         当前信用积分
     * @param registerYears       注册年数
     * @param avgTradeCommentStar 满意度（交易评论平均星级）
     */
    @Async
    @Override
    public void notifyCreditChangedAsync(Long memberId, Long roleId, String memberName, Integer creditPoint, Integer registerYears, Integer avgTradeCommentStar) {
        try {
            StoreDTO dto = new StoreDTO();
            dto.setMemberId(memberId);
            dto.setRoleId(roleId);
            dto.setMemberName(memberName);
            dto.setCreditPoint(creditPoint);
            dto.setRegisterYears(registerYears);
            dto.setAvgTradeCommentStar(avgTradeCommentStar);
            storeFeign.updateShop(dto);
        } catch (Exception e) {
            logger.error("平台会员信用信息改变时，通知店铺模板服务错误", e);
        }
    }

    /**
     * 当平台会员被禁用/启用时，通知店铺模板服务
     *
     * @param memberId 平台会员Id
     * @param roleId   会员角色Id
     * @param status   状态
     */
    @Async
    @Override
    public void notifyMemberStatusChangedAsync(Long memberId, Long roleId, Integer status) {
        try {
            UpdateShopStatusDTO statusDTO = new UpdateShopStatusDTO();
            statusDTO.setMemberId(memberId);
            statusDTO.setRoleId(roleId);
            statusDTO.setStatus(status);
            storeFeign.updateShopStatus(statusDTO);
        } catch (Exception e) {
            logger.error("平台会员被禁用/启用时，通知店铺模板服务错误", e);
        }
    }

    /**
     * 当会员角色被禁用/启用时，通知店铺模板服务
     *
     * @param roleId 会员角色Id
     * @param status 状态
     */
    @Async
    @Override
    public void notifyRoleStatusChangedAsync(Long roleId, Integer status) {
        try {
            UpdateShopStatusDTO statusDTO = new UpdateShopStatusDTO();
            statusDTO.setRoleId(roleId);
            statusDTO.setStatus(status);
            storeFeign.updateShopStatus(statusDTO);
        } catch (Exception e) {
            logger.error("会员角色被禁用/启用时，通知店铺模板服务错误", e);
        }
    }

}
