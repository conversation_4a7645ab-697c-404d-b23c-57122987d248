package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.discount.CustomerProcessFeeDiscountDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 客户工费优惠主表Jpa仓库
 */
@Repository
public interface CustomerProcessFeeDiscountRepository extends JpaRepository<CustomerProcessFeeDiscountDO, Long>, JpaSpecificationExecutor<CustomerProcessFeeDiscountDO> {

    /**
     * 根据单据流水号查询客户工费优惠记录
     * @param documentSerialNumber 单据流水号
     * @return 客户工费优惠记录
     */
    Optional<CustomerProcessFeeDiscountDO> findByDocumentSerialNumber(Long documentSerialNumber);


    /**
     * 根据客户ID、状态和启用状态查询工费优惠配置
     * @param customerId 客户ID
     * @param status 状态
     * @return 工费优惠配置列表
     */
    List<CustomerProcessFeeDiscountDO> findByCustomerIdAndStatusOrderByCreateTimeDesc(Long customerId, Integer status);

    /**
     * 根据客户ID和启用状态查询工费优惠配置
     * @param customerId 客户ID
     * @return 工费优惠配置列表
     */
    List<CustomerProcessFeeDiscountDO> findByCustomerIdAndStatus(Long customerId, Integer status);

    /**
     * 检查客户是否已有启用的工费优惠配置（排除指定ID）
     * @param customerId 客户ID
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsByCustomerIdAndStatusAndIdNot(Long customerId,Integer status, Long excludeId);


    boolean existsByCustomerIdAndStatus(Long customerId, Integer status);
}
