package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Sets;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.idGenerate.IIdGenerate;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.PasswordUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.model.req.ListQueryMemberUserReq;
import com.ssy.lingxi.member.api.model.resp.MemberUserFeignResp;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.branch.MemberBranchDO;
import com.ssy.lingxi.member.model.dto.MemberUserDTO;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.branch.MemberBranchResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserQueryResp;
import com.ssy.lingxi.member.repository.MemberBranchRepository;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseAuthService;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseTokenManageService;
import com.ssy.lingxi.member.service.web.IMemberAbilityUserService;
import com.ssy.lingxi.member.service.commission.IInvitationCommissionService;
import com.ssy.lingxi.member.util.CodeUtil;
import com.ssy.lingxi.support.api.feign.ITencentIMFeign;
import com.ssy.lingxi.support.api.model.req.ImportIMAccountReq;
import com.ssy.lingxi.support.api.model.resp.ImportIMAccountResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 会员能力 - 会员用户操作接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-29
 */
@Service
@Slf4j
public class MemberAbilityUserServiceImpl implements IMemberAbilityUserService {

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseAuthService baseAuthService;

    @Resource
    private IIdGenerate idGenerate;

    @Resource
    private IBaseTokenManageService tokenManageService;

    @Resource
    private ITencentIMFeign tencentIMFeign;

    @Resource
    private MemberBranchRepository memberBranchRepository;

    @Resource
    private IInvitationCommissionService invitationCommissionService;

    /**
     * 新增用户
     * @param headers HttpHeaders信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void addMemberUser(HttpHeaders headers, MemberUserAddReq addVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        //幂等校验
        if(memberCacheService.existRegisterKey(addVO.getPhone(), false)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }

        if(StringUtils.hasLength(addVO.getEmail()) && memberCacheService.existRegisterKey(addVO.getEmail(), false)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
        }

        if(StringUtils.hasLength(addVO.getAccount()) && memberCacheService.existRegisterKey(addVO.getAccount(), false)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_ACCOUNT_EXISTS);
        }

        memberCacheService.setRegisterKey(addVO.getPhone(), addVO.getEmail(), addVO.getAccount(), false);

        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(memberDO == null) {
            memberCacheService.deleteRegisterKey(addVO.getPhone(), addVO.getEmail(), addVO.getAccount(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        // 平台超管账号
        UserDO platformAdminUserDO = userRepository.findPlatformAdminUser().orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));

        //不允许注册“admin”账号
        if (platformAdminUserDO.getAccount().trim().equalsIgnoreCase(addVO.getAccount())) {
            memberCacheService.deleteRegisterKey(addVO.getPhone(), addVO.getEmail(), addVO.getAccount(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_ACCOUNT_OR_PHONE_EXISTS);
        }

        Specification<UserDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.or(criteriaBuilder.equal(criteriaBuilder.lower(root.get("account").as(String.class)), addVO.getAccount()),
                    criteriaBuilder.equal(root.get("phone").as(String.class), addVO.getPhone())));
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            list.add(criteriaBuilder.equal(root.get("member").get("id").as(Long.class), memberDO.getId()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        if (userRepository.count(specification) > 0) {
            memberCacheService.deleteRegisterKey(addVO.getPhone(), addVO.getEmail(), addVO.getAccount(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_ACCOUNT_OR_PHONE_EXISTS);
        }

        if(StringUtils.hasLength(addVO.getEmail())) {
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.equal(root.get("email").as(String.class), addVO.getEmail().trim()));
                list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
                list.add(criteriaBuilder.equal(root.get("member").get("id").as(Long.class), memberDO.getId()));
                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };

            if (userRepository.count(specification) > 0) {
                memberCacheService.deleteRegisterKey(addVO.getPhone(), addVO.getEmail(), addVO.getAccount(), false);
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
            }
        }

        MemberOrganizationDO memberOrganizationDO = memberDO.getOrgs().stream().filter(c -> c.getId().equals(addVO.getOrgId())).findFirst().orElse(null);
        if(memberOrganizationDO == null) {
            memberCacheService.deleteRegisterKey(addVO.getPhone(), addVO.getEmail(), addVO.getAccount(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
        }

        //设置权限
        Set<UserRoleDO> userRoleDOSet = new HashSet<>();
        for(Long roleId: addVO.getMemberRoleIds()) {
            UserRoleDO memberRoleDO = memberDO.getUserRoles().stream().filter(r -> r.getId().equals(roleId)).findFirst().orElse(null);
            if(memberRoleDO == null) {
                memberCacheService.deleteRegisterKey(addVO.getPhone(), addVO.getEmail(), addVO.getAccount(), false);
                throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
            }

            if(memberRoleDO.getStatus().equals(EnableDisableStatusEnum.DISABLE.getCode())) {
                memberCacheService.deleteRegisterKey(addVO.getPhone(), addVO.getEmail(), addVO.getAccount(), false);
                throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_HAS_BEEN_DISABLED);
            }

            userRoleDOSet.add(memberRoleDO);
        }

        UserDO userDO = new UserDO();
        userDO.setCode(idGenerate.getId());
        userDO.setCreateTime(LocalDateTime.now());
        userDO.setLastModifyPwdTime(LocalDateTime.now());
        userDO.setAccount(addVO.getAccount());
        userDO.setName(addVO.getName());
        userDO.setTelCode(addVO.getTelCode());
        userDO.setPhone(addVO.getPhone());

        String encryptPassword;
        try {
            encryptPassword = PasswordUtil.tryEncrypt(addVO.getPassword());
        } catch (Exception e) {
            memberCacheService.deleteRegisterKey(addVO.getPhone(), addVO.getEmail(), addVO.getAccount(), false);
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR);
        }

        userDO.setPassword(encryptPassword);
        userDO.setEmail(StringUtils.hasLength(addVO.getEmail()) ? addVO.getEmail().trim() : "");
        userDO.setJobTitle(StringUtils.hasLength(addVO.getJobTitle()) ? addVO.getJobTitle().trim() : "");
        userDO.setIdCardNo(StringUtils.hasLength(addVO.getIdCardNo()) ? addVO.getIdCardNo().trim() : "");
        userDO.setLogo("");
        userDO.setMember(memberDO);
        userDO.setOrg(memberOrganizationDO);
        userDO.setRoles(userRoleDOSet);

        // 兼容旧的渠道权限等
        MemberUserAuthDO userAuth = new MemberUserAuthDO();
        userAuth.setUser(userDO);
        userAuth.setDataAuth(new ArrayList<>());
        userAuth.setChannelAuth(new ArrayList<>());
        userAuth.setChannels(new HashSet<>());
        userDO.setUserAuth(userAuth);

        //合并权限
        userDO.setMenuAuth(BitMapUtil.or(userRoleDOSet.stream().map(UserRoleDO::getMenuAuth).collect(Collectors.toList()), BitMapUtil.ReturnType.ByteArray));
        userDO.setButtonAuth(BitMapUtil.or(userRoleDOSet.stream().map(UserRoleDO::getButtonAuth).collect(Collectors.toList()), BitMapUtil.ReturnType.ByteArray));
        userDO.setApiAuth(BitMapUtil.emptyByteArray());


        //用户是平台用户，还是会员用户
        userDO.setRelType(MemberRelationTypeEnum.OTHER.getCode());
        //创建的用户为普通用户
        userDO.setUserType(UserTypeEnum.NORMAL.getCode());
        userDO.setIsSales(Boolean.FALSE);
        userDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userDO.setHasOrderAuth(addVO.isHasOrderAuth());

        // 生成唯一的邀请码
        String invitationCode = generateUniqueInvitationCode();
        userDO.setInvitationCode(invitationCode);

        if (!CollectionUtils.isEmpty(addVO.getMemberBranchIds())) {
            List<MemberBranchDO> memberBranchDOList = memberBranchRepository.findAllById(addVO.getMemberBranchIds());
            userDO.setBranches(Sets.newHashSet(memberBranchDOList));
        }

        userRepository.saveAndFlush(userDO);

        // 为新用户创建分佣账户
        try {
            invitationCommissionService.createCommissionAccountForUser(userDO.getId());
        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.error("为用户 {},创建分佣账户失败:{}",  userDO.getId(),e.getMessage());
        }

        //有im权限，则异步调用feign接口开通
        if (userDO.getRoles().stream().map(UserRoleDO::getHasImAuth).anyMatch(imAuth -> EnableDisableStatusEnum.ENABLE.getCode().equals(imAuth))) {
            CompletableFuture.runAsync(() -> tencentIMFeign.importAccount(new ImportIMAccountReq(userDO.getId(), userDO.getName(), userDO.getLogo())), ThreadPoolConfig.asyncDefaultExecutor);
        }
    }

    /**
     * 查询用户信息
     *
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberUserGetResp getMemberUser(HttpHeaders headers, MemberUserIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        if(CollectionUtils.isEmpty(memberDO.getUsers())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        UserDO userDO = memberDO.getUsers().stream().filter(u -> u.getId().equals(idVO.getUserId())).findFirst().orElse(null);
        if(userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        MemberUserGetResp getVO = new MemberUserGetResp();
        getVO.setUserId(userDO.getId());
        getVO.setAccount(userDO.getAccount());
        getVO.setName(userDO.getName());
        getVO.setTelCode(StringUtils.hasLength(userDO.getTelCode()) ? userDO.getTelCode() : "");
        getVO.setPhone(userDO.getPhone());
        getVO.setEmail(StringUtils.hasLength(userDO.getEmail()) ? userDO.getEmail() : "");
        getVO.setIdCardNo(StringUtils.hasLength(userDO.getIdCardNo()) ? userDO.getIdCardNo() : "");
        getVO.setJobTitle(StringUtils.hasLength(userDO.getJobTitle()) ? userDO.getJobTitle() : "");
        if(userDO.getOrg() == null) {
            getVO.setOrgId(0L);
        } else {
            getVO.setOrgId(userDO.getOrg().getId());
        }

        if(CollectionUtils.isEmpty(userDO.getRoles())) {
            getVO.setMemberRoleIds(new ArrayList<>());
            getVO.setMemberRoleNames(new ArrayList<>());
        } else {
            getVO.setMemberRoleIds(userDO.getRoles().stream().map(UserRoleDO::getId).collect(Collectors.toList()));
            getVO.setMemberRoleNames(userDO.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.toList()));
        }

        if (!CollectionUtils.isEmpty(userDO.getBranches())) {
            getVO.setBranches(userDO.getBranches().stream().map(v -> {
                MemberBranchResp memberBranchResp = new MemberBranchResp();
                memberBranchResp.setId(v.getId());
                memberBranchResp.setName(v.getName());
                return memberBranchResp;
            }).collect(Collectors.toList()));
        }
        getVO.setHasOrderAuth(userDO.isHasOrderAuth());

        return getVO;
    }

    /**
     * 更新用户
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateMemberUser(HttpHeaders headers, MemberUserUpdateReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserDO userDO = userRepository.findFirstByMemberAndId(memberDO, updateVO.getUserId());
        if(userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        //不能修改超级管理员账号
        if(userDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELETE_OR_UPDATE_MEMBER_USER);
        }

        //不能修改当前登录的账号
        if(userDO.getId().equals(loginUser.getUserId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELTE_OR_UPDATE_CURRENT_LOGIN_USER);
        }

        MemberOrganizationDO memberOrganizationDO = memberDO.getOrgs().stream().filter(c -> c.getId().equals(updateVO.getOrgId())).findFirst().orElse(null);
        if(memberOrganizationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
        }

        //检查账号和手机号
        Specification<UserDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.notEqual(root.get("id").as(Long.class), userDO.getId()));
            list.add(criteriaBuilder.or(criteriaBuilder.equal(criteriaBuilder.lower(root.get("account").as(String.class)), updateVO.getAccount()),
                    criteriaBuilder.equal(root.get("phone").as(String.class), updateVO.getPhone())));
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), userDO.getRelType()));
            list.add(criteriaBuilder.equal(root.get("member").get("id").as(Long.class), memberDO.getId()));// 在同一家公司中不能重复
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        if (userRepository.count(specification) > 0) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_ACCOUNT_OR_PHONE_EXISTS);
        }

        if(StringUtils.hasLength(updateVO.getEmail())) {
            specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.notEqual(root.get("id").as(Long.class), userDO.getId()));
                list.add(criteriaBuilder.equal(root.get("email").as(String.class), updateVO.getEmail().trim()));
                list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
                list.add(criteriaBuilder.equal(root.get("member").get("id").as(Long.class), memberDO.getId()));// 在同一家公司中不能重复
                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };

            if (userRepository.count(specification) > 0) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
            }
        }

        //设置权限
        Set<UserRoleDO> userRoleDOSet = new HashSet<>();
        for(long roleId: updateVO.getMemberRoleIds()) {
            UserRoleDO memberRoleDO = memberDO.getUserRoles().stream().filter(r -> r.getId() == roleId).findFirst().orElse(null);
            if(memberRoleDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
            }

            if(memberRoleDO.getStatus().equals(EnableDisableStatusEnum.DISABLE.getCode())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_HAS_BEEN_DISABLED);
            }

            userRoleDOSet.add(memberRoleDO);
        }

        userDO.setAccount(updateVO.getAccount());
        userDO.setName(updateVO.getName());
        userDO.setTelCode(updateVO.getTelCode());
        userDO.setPhone(updateVO.getPhone());
        //不修改密码
        userDO.setEmail(StringUtils.hasLength(updateVO.getEmail()) ? updateVO.getEmail().trim() : "");
        userDO.setJobTitle(StringUtils.hasLength(updateVO.getJobTitle()) ? updateVO.getJobTitle().trim() : "");
        userDO.setIdCardNo(StringUtils.hasLength(updateVO.getIdCardNo()) ? updateVO.getIdCardNo().trim() : "");
        userDO.setOrg(memberOrganizationDO);
        userDO.setRoles(userRoleDOSet);

        //合并权限
        userDO.setMenuAuth(BitMapUtil.or(userRoleDOSet.stream().map(UserRoleDO::getMenuAuth).collect(Collectors.toList()), BitMapUtil.ReturnType.ByteArray));
        userDO.setButtonAuth(BitMapUtil.or(userRoleDOSet.stream().map(UserRoleDO::getButtonAuth).collect(Collectors.toList()), BitMapUtil.ReturnType.ByteArray));
        userDO.setApiAuth(BitMapUtil.or(userRoleDOSet.stream().map(UserRoleDO::getApiAuth).collect(Collectors.toList()), BitMapUtil.ReturnType.ByteArray));
        userDO.setHasOrderAuth(updateVO.isHasOrderAuth());

        if (!CollectionUtils.isEmpty(updateVO.getMemberBranchIds())) {
            List<MemberBranchDO> memberBranchDOList = memberBranchRepository.findAllById(updateVO.getMemberBranchIds());
            userDO.setBranches(Sets.newHashSet(memberBranchDOList));
        }

        userRepository.saveAndFlush(userDO);

        //有im权限，则异步调用feign接口开通
        if (userDO.getRoles().stream().map(UserRoleDO::getHasImAuth).anyMatch(imAuth -> EnableDisableStatusEnum.ENABLE.getCode().equals(imAuth))) {
            CompletableFuture.runAsync(() -> tencentIMFeign.importAccount(new ImportIMAccountReq(userDO.getId(), userDO.getName(), userDO.getLogo())), ThreadPoolConfig.asyncDefaultExecutor);
        }
    }

    /**
     * 删除用户
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @Override
    public void deleteMemberUser(HttpHeaders headers, MemberUserIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserDO userDO = userRepository.findFirstByMemberAndId(memberDO, idVO.getUserId());
        if(userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        //不能修改会员创建时，自动创建的超级管理员用户
        if(userDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELETE_OR_UPDATE_MEMBER_USER);
        }

        //不能删除账号本身
        if(userDO.getId().equals(loginUser.getUserId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELTE_OR_UPDATE_CURRENT_LOGIN_USER);
        }

        //如果是业务员账号，要先在渠道管理功能（MemberAbilitySalesController）中删除业务员
        if(userDO.getIsSales()) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NEED_DELETE_SALES_BEFORE_DELETE_USER);
        }

        userRepository.delete(userDO);
    }

    /**
     * 更改用户状态
     *
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @Override
    public void updateMemberUserStatus(HttpHeaders headers, MemberUserUpdateStatusReq statusVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserDO userDO = userRepository.findFirstByMemberAndId(memberDO, statusVO.getUserId());
        if(userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        //不能修改会员创建时，自动创建的超级管理员用户
        if(userDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELETE_OR_UPDATE_MEMBER_USER);
        }

        //不能修改当前登录的账号
        if(userDO.getId().equals(loginUser.getUserId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELTE_OR_UPDATE_CURRENT_LOGIN_USER);
        }

        userDO.setStatus(statusVO.getStatus());
        userRepository.saveAndFlush(userDO);

        if (EnableDisableStatusEnum.DISABLE.getCode().equals(userDO.getStatus())) {
            //删除指定用户的所有token
            CompletableFuture.runAsync(() -> tokenManageService.userOffline(Collections.singletonList(userDO.getId())), ThreadPoolConfig.asyncDefaultExecutor);
        }
    }

    /**
     * 分页查询用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberUserQueryResp> pageMemberUser(HttpHeaders headers, PageQueryMemberUserDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<UserDO> result = userRepository.findAll((Specification<UserDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), memberDO.getId()));
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getAccount())) {
                predicateList.add(cb.like(root.get("account"), "%" + pageVO.getAccount() + "%"));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getName())) {
                predicateList.add(cb.like(root.get("name"), "%" + pageVO.getName() + "%"));
            }

            if (Objects.nonNull(pageVO.getStatus())) {
                predicateList.add(cb.equal(root.get("status"), pageVO.getStatus()));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getPhone())) {
                predicateList.add(cb.equal(root.get("phone"), "%" + pageVO.getPhone() + "%"));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getJobTitle())) {
                predicateList.add(cb.like(root.get("jobTitle"), "%" + pageVO.getJobTitle() + "%"));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getOrg())) {
                Join<UserDO, MemberOrganizationDO> memberJoin = root.join("org", JoinType.LEFT);
                predicateList.add(cb.like(memberJoin.get("title").as(String.class), "%" + pageVO.getOrg().trim() + "%"));
            }
            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<MemberUserQueryResp> resultList = result.getContent().stream().map(u -> {
            MemberUserQueryResp queryVO = new MemberUserQueryResp();
            queryVO.setUserId(u.getId());
            queryVO.setAccount(u.getAccount());
            queryVO.setTelCode(u.getTelCode());
            queryVO.setLastLoginTime(u.getLastLoginTime() == null ? "" : u.getLastLoginTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setName(u.getName());
            queryVO.setPhone(u.getPhone());
            queryVO.setEmail(u.getEmail());
            queryVO.setOrgName(u.getOrg() == null ? "" : u.getOrg().getTitle());
            String roleNames = "";
            if (!CollectionUtils.isEmpty(u.getRoles())) {
                roleNames = u.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(","));
            }
            queryVO.setRoleName(roleNames);
            queryVO.setStatus(u.getStatus());
            queryVO.setJobTitle(u.getJobTitle());
            queryVO.setBranches(u.getBranches().stream().map(v -> new MemberBranchResp(v.getId(), v.getName())).collect(Collectors.toList()));
            return queryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }

    /**
     * 8D整改- 分页查询会员下的用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberUserQueryResp> eightMemberUser(HttpHeaders headers, PageQueryMemberUserDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<UserDO> result = userRepository.findAll((Specification<UserDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), memberDO.getId()));
            predicateList.add(cb.equal(root.get("status"), CommonBooleanEnum.YES.getCode()));
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getPhone())) {
                predicateList.add(cb.like(root.get("phone"), "%" + pageVO.getPhone() + "%"));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getName())) {
                predicateList.add(cb.like(root.get("name"), "%" + pageVO.getName() + "%"));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getJobTitle())) {
                predicateList.add(cb.like(root.get("jobTitle"), "%" + pageVO.getJobTitle() + "%"));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getOrg())) {
                Join<UserDO, MemberOrganizationDO> memberJoin = root.join("org", JoinType.LEFT);
                predicateList.add(cb.like(memberJoin.get("title").as(String.class), "%" + pageVO.getOrg().trim() + "%"));
            }

            if (!CollectionUtils.isEmpty(pageVO.getUserIds())){
                predicateList.add(cb.not(root.get("id").in(pageVO.getUserIds())));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<MemberUserQueryResp> resultList = result.getContent().stream().map(u -> {
            MemberUserQueryResp queryVO = new MemberUserQueryResp();
            queryVO.setUserId(u.getId());
            queryVO.setTelCode(u.getTelCode());
            queryVO.setName(u.getName());
            queryVO.setPhone(u.getPhone());
            queryVO.setEmail(u.getEmail());
            queryVO.setOrgName(u.getOrg() == null ? "" : u.getOrg().getTitle());
            queryVO.setJobTitle(u.getJobTitle());
            return queryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }

    /**
     * 分页查询用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberUserQueryResp> pageMemberUserByRoleId(HttpHeaders headers, PageQueryMemberUserDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if(memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Page<UserDO> result = userRepository.findAll((Specification<UserDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), memberDO.getId()));
            predicateList.add(cb.equal(root.get("status"), EnableDisableStatusEnum.ENABLE.getCode()));
            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getAccount())) {
                predicateList.add(cb.like(root.get("account"), "%" + pageVO.getAccount() + "%"));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getName())) {
                predicateList.add(cb.like(root.get("name"), "%" + pageVO.getName() + "%"));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getJobTitle())) {
                predicateList.add(cb.like(root.get("jobTitle"), "%" + pageVO.getJobTitle() + "%"));
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(pageVO.getOrg())) {
                Join<UserDO, MemberOrganizationDO> memberJoin = root.join("org", JoinType.LEFT);
                predicateList.add(cb.like(memberJoin.get("title").as(String.class), "%" + pageVO.getOrg().trim() + "%"));
            }

            if (Objects.nonNull(pageVO.getRoleId())) {
                Join<UserDO, UserRoleDO> roleJoin = root.join("roles", JoinType.LEFT);
                predicateList.add(cb.equal(roleJoin.get("id"), pageVO.getRoleId()));
            }
            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<MemberUserQueryResp> resultList = result.getContent().stream().map(u -> {
            MemberUserQueryResp queryVO = new MemberUserQueryResp();
            queryVO.setUserId(u.getId());
            queryVO.setAccount(u.getAccount());
            queryVO.setTelCode(u.getTelCode());
            queryVO.setLastLoginTime(u.getLastLoginTime() == null ? "" : u.getLastLoginTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setName(u.getName());
            queryVO.setPhone(u.getPhone());
            queryVO.setOrgName(u.getOrg() == null ? "" : u.getOrg().getTitle());
            String roleNames = "";
            String roleIds = "";
            if (!CollectionUtils.isEmpty(u.getRoles())) {
                roleNames = u.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(","));
                roleIds = u.getRoles().stream().map(map -> String.valueOf(map.getId())).collect(Collectors.joining(","));
            }
            queryVO.setRoleName(roleNames);
            queryVO.setRoleId(roleIds);
            queryVO.setStatus(u.getStatus());
            queryVO.setJobTitle(u.getJobTitle());
            return queryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }

    /**
     * 查询用户信息
     *
     * @param userId 用户Id
     * @return 用户信息DTO
     */
    @Override
    public MemberUserDTO findMemberUser(Long userId) {
        if(userId == null) {
            return new MemberUserDTO();
        }

        UserDO userDO = userRepository.findById(userId).orElse(null);
        if(userDO == null) {
            return new MemberUserDTO();
        }

        MemberUserDTO userDTO = new MemberUserDTO();
        userDTO.setUserId(userDO.getId());
        userDTO.setUserName(StringUtils.hasLength(userDO.getName()) ? userDO.getName() : "");
        if(!CollectionUtils.isEmpty(userDO.getRoles())) {
            userDO.getRoles().stream().findFirst().ifPresent(memberRoleDO -> userDTO.setUserRoleName(memberRoleDO.getRoleName()));
        }
        userDTO.setUserOrgName(userDO.getOrg() == null ? "" : userDO.getOrg().getTitle());
        userDTO.setUserJobTitle(StringUtils.hasLength(userDO.getJobTitle()) ? userDO.getJobTitle() : "");

        return userDTO;
    }

    /**
     * 分页查询会员用户（业务员）
     *
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @Override
    public List<MemberUserQueryResp> pageMemberSalesMan(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        List<UserDO> result = userRepository.findAll((Specification<UserDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), memberDO.getId()));
            predicateList.add(cb.equal(root.get("isSales").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        },Sort.by("id").ascending());

        List<MemberUserQueryResp> resultList = result.stream().map(u -> {
            MemberUserQueryResp queryVO = new MemberUserQueryResp();
            queryVO.setUserId(u.getId());
            queryVO.setAccount(u.getAccount());
            queryVO.setTelCode(u.getTelCode());
            queryVO.setLastLoginTime(u.getLastLoginTime() == null ? "" : u.getLastLoginTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setName(u.getName());
            queryVO.setPhone(u.getPhone());
            queryVO.setOrgName(u.getOrg() == null ? "" : u.getOrg().getTitle());
            String roleNames = "";
            if (!CollectionUtils.isEmpty(u.getRoles())) {
                roleNames = u.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(","));
            }
            queryVO.setRoleName(roleNames);
            queryVO.setStatus(u.getStatus());
            queryVO.setJobTitle(u.getJobTitle());
            return queryVO;
        }).collect(Collectors.toList());

        return resultList;
    }

    /**
     * 查询会员用户
     * @return 会员用户
     */
    @Override
    public List<MemberUserFeignResp> listMemberUser(ListQueryMemberUserReq listQueryMemberUserReq) {
        Long memberId = listQueryMemberUserReq.getMemberId();
        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        List<UserDO> memberUserDOList = userRepository.findAll((Specification<UserDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), memberDO.getId()));
            predicateList.add(cb.equal(root.get("status"), EnableDisableStatusEnum.ENABLE.getCode()));

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        });

        return memberUserDOList.stream().map(u -> {
            MemberUserFeignResp queryVO = new MemberUserFeignResp();
            queryVO.setUserId(u.getId());
            queryVO.setAccount(u.getAccount());
            queryVO.setTelCode(u.getTelCode());
            queryVO.setLastLoginTime(u.getLastLoginTime() == null ? "" : u.getLastLoginTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            queryVO.setName(u.getName());
            queryVO.setPhone(u.getPhone());
            queryVO.setEmail(u.getEmail());
            queryVO.setOrgName(u.getOrg() == null ? "" : u.getOrg().getTitle());
            String roleNames = "";
            if (!CollectionUtils.isEmpty(u.getRoles())) {
                roleNames = u.getRoles().stream().map(UserRoleDO::getRoleName).collect(Collectors.joining(","));
            }
            queryVO.setRoleName(roleNames);
            queryVO.setStatus(u.getStatus());
            queryVO.setJobTitle(u.getJobTitle());
            return queryVO;
        }).collect(Collectors.toList());
    }

    @Override
    public WrapperResp<Void> registerTencentIM(Long userId) {
        //有im权限，则异步调用feign接口开通
        UserDO userDO = userRepository.findById(userId).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST));
        if (userDO.getRoles().stream().map(UserRoleDO::getHasImAuth).anyMatch(imAuth -> EnableDisableStatusEnum.ENABLE.getCode().equals(imAuth))) {
            ImportIMAccountResp importIMAccountResp = WrapperUtil.getData(tencentIMFeign.importAccount(new ImportIMAccountReq(userDO.getId(), userDO.getName(), userDO.getLogo())));
            if (importIMAccountResp == null) {
                return WrapperUtil.fail("自动注册IM用户失败，调用IM注册服务异常");
            } else {
                if (importIMAccountResp.getCode() == 0) {
                    return WrapperUtil.success();
                } else {
                    return WrapperUtil.fail("自动注册IM用户失败，错误码为【" + importIMAccountResp.getCode() + "】");
                }
            }
        }
        return WrapperUtil.fail("自动注册IM用户失败，该用户无IM权限");
    }

    @Override
    public List<MemberUserFeignResp> getListByIds(CommonIdListReq idListReq) {
        List<UserDO> userDOS = userRepository.findAllById(idListReq.getIdList());
        if(CollUtil.isNotEmpty(userDOS)){
            return userDOS.stream().map(userDO -> {
                MemberUserFeignResp userFeignResp = BeanUtil.copyProperties(userDO, MemberUserFeignResp.class);
                userFeignResp.setUserId(userDO.getId());
                return userFeignResp;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 生成唯一的邀请码
     * @return 唯一的邀请码
     */
    private String generateUniqueInvitationCode() {
        String invitationCode;
        int maxRetries = 10; // 最大重试次数
        int retries = 0;

        do {
            invitationCode = CodeUtil.generateInvitationCode();
            retries++;
        } while (userRepository.existsByInvitationCode(invitationCode) && retries < maxRetries);

        if (retries >= maxRetries) {
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR, "生成邀请码失败，请重试");
        }

        return invitationCode;
    }
}
