package com.ssy.lingxi.member.model.req.comment;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 会员评价接口查询参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformMemberTradeCommentHistoryDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -5181756804350087167L;

    /**
     * 会员id
     */
    @NotNull(message = "会员Id要大于0")
    @Positive(message = "会员Id要大于0")
    private Long memberId;

    /**
     * 会员角色id
     */
    @NotNull(message = "会员角色Id要大于0")
    @Positive(message = "会员角色Id要大于0")
    private Long roleId;

    /**
     * 评论级别 - 0或Null：全部， 1-差评（1-2星），2-中评（3星）， 3-好评（4-5星）
     */
    private Integer starLevel;
}
