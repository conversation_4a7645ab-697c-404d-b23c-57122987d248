package com.ssy.lingxi.member.serviceImpl.commission;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionAccountDO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionDetailDO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionWithdrawalDO;
import com.ssy.lingxi.member.entity.do_.commission.InvitationRecordDO;
import com.ssy.lingxi.member.enums.commission.CommissionTradeTypeEnum;
import com.ssy.lingxi.member.model.req.commission.CommissionDetailQueryReq;
import com.ssy.lingxi.member.model.req.commission.CommissionWithdrawalQueryReq;
import com.ssy.lingxi.member.model.req.commission.InvitedCustomerQueryReq;
import com.ssy.lingxi.member.model.resp.commission.*;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.repository.commission.CommissionAccountRepository;
import com.ssy.lingxi.member.repository.commission.CommissionDetailRepository;
import com.ssy.lingxi.member.repository.commission.CommissionWithdrawalRepository;
import com.ssy.lingxi.member.repository.commission.InvitationRecordRepository;
import com.ssy.lingxi.member.service.commission.IMiniProgramCommissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小程序佣金服务实现
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Slf4j
@Service
public class MiniProgramCommissionServiceImpl implements IMiniProgramCommissionService {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private CommissionAccountRepository commissionAccountRepository;

    @Resource
    private CommissionDetailRepository commissionDetailRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private InvitationRecordRepository invitationRecordRepository;

    @Resource
    private CommissionWithdrawalRepository commissionWithdrawalRepository;

    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");

    @Override
    public MiniProgramAccountInfoResp getAccountInfo(UserLoginCacheDTO loginUser) {
        // 查询用户的分佣账户
        CommissionAccountDO account = commissionAccountRepository.findByUserId(loginUser.getUserId())
                .orElseThrow(() -> new BusinessException("分佣账户不存在"));

        MiniProgramAccountInfoResp resp = new MiniProgramAccountInfoResp();

        // 设置邀请码（从用户信息中获取，这里暂时模拟）
        resp.setInviteCode(generateInviteCode(loginUser.getUserId()));

        // 设置账户金额信息
        resp.setWithdrawableCommission(account.getWithdrawableBalance() != null ? account.getWithdrawableBalance() : BigDecimal.ZERO);
        resp.setAccountBalance(account.getAccountBalance() != null ? account.getAccountBalance() : BigDecimal.ZERO);
        resp.setWithdrawingAmount(account.getWithdrawingAmount() != null ? account.getWithdrawingAmount() : BigDecimal.ZERO);

        // 计算待收益金额
        resp.setPendingAmount(calculatePendingAmount(loginUser.getUserId()));
        resp.setId(account.getId());

        // 计算我的佣金客户人数
        resp.setMyCommissionCustomer(calculateTotalCustomers(loginUser.getUserId()));


        return resp;
    }

    @Override
    public MyCommissionCustomersResp getMyCommissionCustomers(UserLoginCacheDTO loginUser) {
        MyCommissionCustomersResp resp = new MyCommissionCustomersResp();

        // 计算累计佣金
        resp.setTotalCommission(calculateTotalCommission(loginUser.getUserId()));

        // 计算邀请客户总数
        resp.setTotalCustomers(calculateTotalCustomers(loginUser.getUserId()));

        // 计算本月新增客户数
        resp.setMonthlyNewCustomers(calculateMonthlyNewCustomers(loginUser.getUserId()));

        // 计算本月佣金收益
        resp.setMonthlyCommission(calculateMonthlyCommission(loginUser.getUserId()));

        return resp;
    }

    @Override
    public PageDataResp<InvitedCustomerResp> getInvitedCustomerPage(UserLoginCacheDTO loginUser, InvitedCustomerQueryReq request) {
        // 通过邀请记录表查询被邀请的客户

        // 构建查询条件
        Specification<InvitationRecordDO> spec = buildInvitationRecordSpecification(loginUser.getUserId(), request);

        // 分页参数
        Pageable pageable = PageRequest.of(
                request.getCurrent() - 1,
                request.getPageSize(),
                Sort.by(Sort.Direction.DESC, "registrationTime")
        );

        // 执行分页查询
        Page<InvitationRecordDO> page = invitationRecordRepository.findAll(spec, pageable);

        // 转换为响应对象
        List<InvitedCustomerResp> list = page.getContent().stream()
                .map(this::convertInvitationRecordToResp)
                .collect(Collectors.toList());

        return new PageDataResp<>(page.getTotalElements(), list);
    }

    @Override
    public InvitedCustomerStatisticsResp getInvitedCustomerStatistics(UserLoginCacheDTO loginUser, InvitedCustomerQueryReq request) {
        // 使用与getInvitedCustomerPage相同的查询条件
        Specification<InvitationRecordDO> spec = buildInvitationRecordSpecification(loginUser.getUserId(), request);

        // 查询所有符合条件的邀请记录（不分页）
        List<InvitationRecordDO> invitationRecords = invitationRecordRepository.findAll(spec);

        // 统计总人数
        Integer totalCount = invitationRecords.size();

        // 计算累计佣金金额
        BigDecimal totalCommissionAmount = BigDecimal.ZERO;

        // 获取所有被邀请用户的ID
        List<Long> inviteeUserIds = invitationRecords.stream()
                .map(InvitationRecordDO::getInviteeUserId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (!inviteeUserIds.isEmpty()) {
            // 查询这些用户产生的佣金总额
            // 构建佣金查询条件：邀请人为当前用户，被邀请人在列表中
            Specification<CommissionDetailDO> commissionSpec = (root, query, criteriaBuilder) -> {
                List<Predicate> predicates = new ArrayList<>();

                // 佣金账户为当前用户
                predicates.add(criteriaBuilder.equal(root.get("commissionAccountId"), loginUser.getUserId()));

                // 佣金来源为邀请奖励（假设有类型字段，这里需要根据实际字段调整）
                // predicates.add(criteriaBuilder.equal(root.get("commissionType"), CommissionTypeEnum.INVITATION_REWARD.getCode()));

                return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
            };

            List<CommissionDetailDO> commissionDetails = commissionDetailRepository.findAll(commissionSpec);

            // 计算总佣金金额（排除在途金额，只统计已确认的佣金）
            totalCommissionAmount = commissionDetails.stream()
                    .filter(detail -> {
                        // 排除在途的佣金（待收益状态）
                        Integer tradeType = detail.getTradeType();
                        return !CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING.getCode().equals(tradeType);
                    })
                    .map(CommissionDetailDO::getChangeAmount)
                    .filter(Objects::nonNull)
                    .filter(amount -> amount.compareTo(BigDecimal.ZERO) > 0) // 只统计正数（收入）
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 构建响应对象
        InvitedCustomerStatisticsResp response = new InvitedCustomerStatisticsResp();
        response.setTotalCount(totalCount);
        response.setTotalCommissionAmount(totalCommissionAmount);

        log.info("查询邀请客户统计完成，用户：{}，总人数：{}，累计佣金：{}",
                loginUser.getUserId(), totalCount, totalCommissionAmount);

        return response;
    }

    @Override
    public List<CustomerCommissionDetailResp> getCustomerDetail(UserLoginCacheDTO loginUser, Long customerId, Integer year) {
        // 查询指定年份的佣金明细
        long startTime = LocalDateTime.of(year, 1, 1, 0, 0, 0).toEpochSecond(ZoneOffset.ofHours(8)) * 1000;
        long endTime = LocalDateTime.of(year, 12, 31, 23, 59, 59).toEpochSecond(ZoneOffset.ofHours(8)) * 1000;

        // 查询该客户在指定年份的所有佣金明细
        // 使用Specification查询，因为没有直接的时间范围查询方法
        Specification<CommissionDetailDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("commissionAccountId"), loginUser.getUserId()));
            predicates.add(criteriaBuilder.between(root.get("createTime"), startTime, endTime));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        List<CommissionDetailDO> commissionDetails = commissionDetailRepository.findAll(spec);

        // 过滤出与指定客户相关的记录（这里暂时返回所有记录）
        List<CommissionDetailDO> customerDetails = commissionDetails;

        // 按月份分组
        Map<String, List<CommissionDetailDO>> monthlyDetailsMap = customerDetails.stream()
                .collect(Collectors.groupingBy(detail -> {
                    LocalDateTime dateTime = LocalDateTime.ofEpochSecond(detail.getCreateTime() / 1000, 0, ZoneOffset.ofHours(8));
                    return dateTime.format(MONTH_FORMATTER);
                }));

        // 转换为响应对象
        List<CustomerCommissionDetailResp> result = new ArrayList<>();
        for (int month = 1; month <= 12; month++) {
            String monthKey = String.format("%d-%02d", year, month);
            List<CommissionDetailDO> monthlyDetails = monthlyDetailsMap.getOrDefault(monthKey, new ArrayList<>());

            CustomerCommissionDetailResp monthDetail = new CustomerCommissionDetailResp();
            monthDetail.setMonth(monthKey);

            // 计算月度统计
            BigDecimal monthlyCommission = monthlyDetails.stream()
                    .map(CommissionDetailDO::getChangeAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            monthDetail.setMonthlyCommission(monthlyCommission);

            // 这里需要关联订单表来获取订单金额和数量，暂时设置为0
            monthDetail.setMonthlyOrderAmount(BigDecimal.ZERO);
            monthDetail.setMonthlyOrderCount(monthlyDetails.size());

            // 转换明细列表
            List<CustomerCommissionDetailResp.CommissionDetailItem> detailItems = monthlyDetails.stream()
                    .map(this::convertToCommissionDetailItem)
                    .collect(Collectors.toList());
            monthDetail.setCommissionDetails(detailItems);

            result.add(monthDetail);
        }

        return result;
    }

    @Override
    public PageDataResp<CommissionIncomeRecordResp> getCommissionIncomeRecords(UserLoginCacheDTO loginUser, CommissionDetailQueryReq request) {
        // 构建查询条件
        Specification<CommissionDetailDO> spec = buildCommissionDetailSpecification(loginUser.getUserId(), request);

        // 分页参数
        Pageable pageable = PageRequest.of(
                request.getCurrent() - 1,
                request.getPageSize(),
                Sort.by(Sort.Direction.DESC, "createTime")
        );

        // 执行分页查询
        Page<CommissionDetailDO> page = commissionDetailRepository.findAll(spec, pageable);

        // 转换为响应对象
        List<CommissionIncomeRecordResp> list = page.getContent().stream()
                .map(this::convertToCommissionIncomeRecordResp)
                .collect(Collectors.toList());

        return new PageDataResp<>(page.getTotalElements(), list);
    }

    @Override
    public PageDataResp<PendingCommissionRecordResp> getPendingCommissionRecords(UserLoginCacheDTO loginUser, CommissionDetailQueryReq request) {
        // 查询待收益的佣金记录
        // 这里需要根据实际业务逻辑来查询待收益状态的佣金记录

        Specification<CommissionDetailDO> spec = buildPendingCommissionSpecification(loginUser.getUserId(), request);

        // 分页参数
        Pageable pageable = PageRequest.of(
                request.getCurrent() - 1,
                request.getPageSize(),
                Sort.by(Sort.Direction.DESC, "createTime")
        );

        // 执行分页查询
        Page<CommissionDetailDO> page = commissionDetailRepository.findAll(spec, pageable);

        // 转换为响应对象
        List<PendingCommissionRecordResp> list = page.getContent().stream()
                .map(this::convertToPendingCommissionRecordResp)
                .collect(Collectors.toList());

        return new PageDataResp<>(page.getTotalElements(), list);
    }

    @Override
    public PageDataResp<MiniProgramWithdrawalResp> getWithdrawalRecords(UserLoginCacheDTO loginUser, CommissionWithdrawalQueryReq request) {
        // 构建查询条件
        Specification<CommissionWithdrawalDO> spec = buildWithdrawalSpecification(loginUser.getUserId(), request);

        // 分页参数
        Pageable pageable = PageRequest.of(
                request.getCurrent() - 1,
                request.getPageSize(),
                Sort.by(Sort.Direction.DESC, "createTime")
        );

        // 执行分页查询
        Page<CommissionWithdrawalDO> page = commissionWithdrawalRepository.findAll(spec, pageable);

        // 转换为响应对象
        List<MiniProgramWithdrawalResp> list = page.getContent().stream()
                .map(this::convertToMiniProgramWithdrawalResp)
                .collect(Collectors.toList());

        return new PageDataResp<>(page.getTotalElements(), list);
    }

    // ==================== 私有方法 ====================

    /**
     * 获取用户邀请码
     */
    private String generateInviteCode(Long userId) {
        // 从UserDO中获取真实的邀请码
        Optional<UserDO> userOpt = userRepository.findById(userId);
        if (userOpt.isPresent() && userOpt.get().getInvitationCode() != null) {
            return userOpt.get().getInvitationCode();
        }

        // 如果没有邀请码，返回默认格式
        return "INV" + String.format("%06d", userId);
    }

    /**
     * 计算待收益金额
     */
    private BigDecimal calculatePendingAmount(Long userId) {
        // 查询待收益的佣金记录
        // 使用Specification查询
        Specification<CommissionDetailDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("commissionAccountId"), userId));
            predicates.add(criteriaBuilder.equal(root.get("tradeType"), 4));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        List<CommissionDetailDO> pendingDetails = commissionDetailRepository.findAll(spec);

        return pendingDetails.stream()
                .filter(detail -> isPendingStatus(detail))
                .map(CommissionDetailDO::getChangeAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算累计收益
     */
    private BigDecimal calculateTotalIncome(Long userId) {
        // 查询用户所有已到账的佣金收益
        Specification<CommissionDetailDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("commissionAccountId"), userId));
            // 排除类型2（下级消费分佣转成待收益）的记录，这些不应该统计到累计收益中
            predicates.add(criteriaBuilder.notEqual(root.get("tradeType"), CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING.getCode()));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        List<CommissionDetailDO> incomeDetails = commissionDetailRepository.findAll(spec);

        return incomeDetails.stream()
                .filter(detail -> detail.getChangeAmount().compareTo(BigDecimal.ZERO) > 0)
                .map(CommissionDetailDO::getChangeAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算累计提现
     */
    private BigDecimal calculateTotalWithdrawn(Long userId) {
        // 查询用户所有成功提现的金额
        Pageable pageable = PageRequest.of(0, Integer.MAX_VALUE);
        Page<CommissionWithdrawalDO> withdrawalPage = commissionWithdrawalRepository.findByUserIdOrderByCreateTimeDesc(userId, pageable);

        return withdrawalPage.getContent().stream()
                .filter(withdrawal -> Integer.valueOf(3).equals(withdrawal.getWithdrawalStatus()))
                .map(CommissionWithdrawalDO::getWithdrawalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算累计佣金
     */
    private BigDecimal calculateTotalCommission(Long userId) {
        // 查询用户累计获得的佣金
        Specification<CommissionDetailDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("commissionAccountId"), userId));
            // 排除类型2（下级消费分佣转成待收益）的记录，这些不应该统计到累计佣金中
            predicates.add(criteriaBuilder.notEqual(root.get("tradeType"), CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING.getCode()));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        List<CommissionDetailDO> commissionDetails = commissionDetailRepository.findAll(spec);

        return commissionDetails.stream()
                .filter(detail -> detail.getChangeAmount().compareTo(BigDecimal.ZERO) > 0)
                .map(CommissionDetailDO::getChangeAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算邀请客户总数
     */
    private Integer calculateTotalCustomers(Long userId) {
        // 从邀请记录表中查询用户邀请的客户总数
        // 使用Repository中现成的方法统计邀请人的邀请数量
        Long totalCount = invitationRecordRepository.countByInviterUserId(userId);

        return Math.toIntExact(totalCount);
    }

    /**
     * 计算本月新增客户数
     */
    private Integer calculateMonthlyNewCustomers(Long userId) {
        // 查询本月新增的邀请客户数（基于注册时间）
        LocalDateTime now = LocalDateTime.now();
        long startTime = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).toEpochSecond(ZoneOffset.ofHours(8)) * 1000;
        long endTime = now.toEpochSecond(ZoneOffset.ofHours(8)) * 1000;

        // 构建查询条件：查询本月注册的被邀请用户
        Specification<InvitationRecordDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 邀请人是当前用户
            predicates.add(criteriaBuilder.equal(root.get("inviterUserId"), userId));

            // 本月注册的用户（基于注册时间）
            predicates.add(criteriaBuilder.between(root.get("registrationTime"), startTime, endTime));

            // 确保有注册时间（已注册的用户）
            predicates.add(criteriaBuilder.isNotNull(root.get("registrationTime")));

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        // 统计本月新增的邀请客户数量
        long monthlyCount = invitationRecordRepository.count(spec);

        return Math.toIntExact(monthlyCount);
    }

    /**
     * 计算本月佣金收益
     */
    private BigDecimal calculateMonthlyCommission(Long userId) {
        // 查询本月的佣金收益
        LocalDateTime now = LocalDateTime.now();
        long startTime = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).toEpochSecond(ZoneOffset.ofHours(8)) * 1000;
        long endTime = now.toEpochSecond(ZoneOffset.ofHours(8)) * 1000;

        Specification<CommissionDetailDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(criteriaBuilder.equal(root.get("commissionAccountId"), userId));
            predicates.add(criteriaBuilder.between(root.get("createTime"), startTime, endTime));
            // 排除类型2（下级消费分佣转成待收益）的记录，这些不应该统计到本月佣金收益中
            predicates.add(criteriaBuilder.notEqual(root.get("tradeType"), CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING.getCode()));
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        List<CommissionDetailDO> monthlyDetails = commissionDetailRepository.findAll(spec);

        return monthlyDetails.stream()
                .filter(detail -> detail.getChangeAmount().compareTo(BigDecimal.ZERO) > 0)
                .map(CommissionDetailDO::getChangeAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    /**
     * 构建佣金明细查询条件（过滤掉待收益记录）
     */
    private Specification<CommissionDetailDO> buildCommissionDetailSpecification(Long userId, CommissionDetailQueryReq request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 用户id过滤
            predicates.add(criteriaBuilder.equal(root.get("userId"), userId));

            // 过滤掉待收益记录（交易类型为2：下级消费分佣转成待收益）
            predicates.add(criteriaBuilder.notEqual(root.get("tradeType"), CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING.getCode()));

            Long relatedUserId = request.getRelatedUserId();
            if (Objects.nonNull(relatedUserId)) {
                predicates.add(criteriaBuilder.equal(root.get("relatedUserId"), relatedUserId));
            }

            // 时间范围过滤
            if (request.getStartTime() != null && request.getEndTime() != null) {
                predicates.add(criteriaBuilder.between(root.get("createTime"), request.getStartTime(), request.getEndTime()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 构建提现记录查询条件
     */
    private Specification<CommissionWithdrawalDO> buildWithdrawalSpecification(Long userId, CommissionWithdrawalQueryReq request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 用户id过滤
            predicates.add(criteriaBuilder.equal(root.get("userId"), userId));

            // 时间范围过滤
            if (request.getStartTime() != null && request.getEndTime() != null) {
                predicates.add(criteriaBuilder.between(root.get("createTime"), request.getStartTime(), request.getEndTime()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 转换为佣金收益记录响应对象
     */
    private CommissionIncomeRecordResp convertToCommissionIncomeRecordResp(CommissionDetailDO detail) {
        CommissionIncomeRecordResp resp = new CommissionIncomeRecordResp();
        resp.setId(detail.getId());
        resp.setTradeCode(detail.getTradeCode());
        resp.setTradeType(detail.getTradeType());
        resp.setTradeTypeName(getTradeTypeName(detail.getTradeType()));
        resp.setCommissionAmount(detail.getChangeAmount());
        resp.setRelatedOrderNo(detail.getRemark()); // 使用备注字段代替

        // 获取被邀请客户名称
        if (detail.getRelatedUserId() != null) {
            UserDO relatedUser = userRepository.findById(detail.getRelatedUserId()).orElse(null);
            if (relatedUser != null) {
                resp.setInvitedCustomerName(relatedUser.getName());
            } else {
                resp.setInvitedCustomerName("客户" + detail.getRelatedUserId());
            }
        } else {
            resp.setInvitedCustomerName("未知客户");
        }

        resp.setIncomeTime(formatTime(detail.getCreateTime()));
        resp.setRemark(detail.getRemark());

        return resp;
    }

    /**
     * 转换为小程序提现记录响应对象
     */
    private MiniProgramWithdrawalResp convertToMiniProgramWithdrawalResp(CommissionWithdrawalDO withdrawal) {
        MiniProgramWithdrawalResp resp = new MiniProgramWithdrawalResp();
        resp.setId(withdrawal.getId());
        resp.setWithdrawalTradeCode(withdrawal.getWithdrawalTradeCode());
        resp.setWithdrawalAmount(withdrawal.getWithdrawalAmount());
        resp.setServiceFee(BigDecimal.ZERO); // 暂时设为0
        resp.setActualAmount(withdrawal.getWithdrawalAmount()); // 暂时等于提现金额
        resp.setWithdrawalStatus(withdrawal.getWithdrawalStatus());
        resp.setWithdrawalStatusName(getWithdrawalStatusName(withdrawal.getWithdrawalStatus()));
        resp.setBankCardNumber(maskCardNumber(withdrawal.getBankCardNumber()));
        resp.setBankName(withdrawal.getBankName());
        resp.setCardHolderName(withdrawal.getBankCardHolderName());
        resp.setApplyTime(formatTime(withdrawal.getCreateTime()));
        resp.setProcessTime(formatTime(withdrawal.getProcessTime()));
        resp.setFailureReason(withdrawal.getFailureReason());

        return resp;
    }

    /**
     * 获取交易类型名称
     */
    private String getTradeTypeName(Integer tradeType) {
        if (tradeType == null) {
            return "";
        }
        switch (tradeType) {
            case 1:
                return "邀请注册奖励";
            case 2:
                return "下单分佣";
            case 3:
                return "下单消费待收益";
            case 4:
                return "下单取消扣减分佣";
            case 5:
                return "提现扣减";
            case 6:
                return "邀请认证奖励";
            case 7:
                return "邀请首单奖励";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取提现状态名称
     */
    private String getWithdrawalStatusName(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1:
                return "申请中";
            case 2:
                return "处理中";
            case 3:
                return "提现成功";
            case 4:
                return "提现失败";
            case 5:
                return "已取消";
            default:
                return "未知状态";
        }
    }

    /**
     * 银行卡号脱敏
     */
    private String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 4) {
            return cardNumber;
        }
        return "****" + cardNumber.substring(cardNumber.length() - 4);
    }

    /**
     * 格式化时间戳
     */
    private String formatTime(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.ofHours(8))
                .format(DATE_TIME_FORMATTER);
    }

    /**
     * 格式化LocalDateTime
     */
    private String formatLocalDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATE_TIME_FORMATTER);
    }

    /**
     * 判断是否为待收益状态
     */
    private boolean isPendingStatus(CommissionDetailDO detail) {
        // 这里需要根据实际业务逻辑来判断是否为待收益状态
        // 暂时假设所有交易类型为4的记录都是待收益
        return detail.getTradeType() != null && detail.getTradeType() == 4;
    }


    /**
     * 构建待收益佣金查询条件
     */
    private Specification<CommissionDetailDO> buildPendingCommissionSpecification(Long userId, CommissionDetailQueryReq request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 分佣账户id过滤
            predicates.add(criteriaBuilder.equal(root.get("commissionAccountId"), userId));

            // 只查询待收益的记录（交易类型为4）
            predicates.add(criteriaBuilder.equal(root.get("tradeType"), CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING.getCode()));

            // 时间范围过滤
            if (request.getStartTime() != null && request.getEndTime() != null) {
                predicates.add(criteriaBuilder.between(root.get("createTime"), request.getStartTime(), request.getEndTime()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }


    /**
     * 转换为佣金明细项
     */
    private CustomerCommissionDetailResp.CommissionDetailItem convertToCommissionDetailItem(CommissionDetailDO detail) {
        CustomerCommissionDetailResp.CommissionDetailItem item = new CustomerCommissionDetailResp.CommissionDetailItem();
        item.setOrderNo(detail.getRemark()); // 使用备注字段作为订单号
        item.setOrderTime(formatTime(detail.getCreateTime()));
        item.setOrderAmount(BigDecimal.ZERO); // 需要关联订单表查询
        item.setCommissionAmount(detail.getChangeAmount());
        item.setCommissionType(getTradeTypeName(detail.getTradeType()));
        item.setCommissionStatus("已到账");

        // 注意：CommissionDetailItem 没有 customerName 字段，这里注释掉
        // 如果需要客户名称，可以在调用方处理或扩展 CommissionDetailItem 类
        /*
        if (detail.getRelatedUserId() != null) {
            UserDO relatedUser = userRepository.findById(detail.getRelatedUserId()).orElse(null);
            if (relatedUser != null) {
                // item.setCustomerName(relatedUser.getName());
            } else {
                // item.setCustomerName("客户" + detail.getRelatedUserId());
            }
        } else {
            // item.setCustomerName("未知客户");
        }
        */

        return item;
    }

    /**
     * 转换为待收益记录响应对象
     */
    private PendingCommissionRecordResp convertToPendingCommissionRecordResp(CommissionDetailDO detail) {
        PendingCommissionRecordResp resp = new PendingCommissionRecordResp();
        resp.setId(detail.getId());
        resp.setTradeCode(detail.getTradeCode());
        resp.setTradeType(detail.getTradeType());
        resp.setTradeTypeName(getTradeTypeName(detail.getTradeType()));
        resp.setPendingAmount(detail.getChangeAmount());
        resp.setRelatedOrderNo(detail.getRemark()); // 使用备注字段作为订单号

        // 获取被邀请客户名称
        if (detail.getRelatedUserId() != null) {
            UserDO relatedUser = userRepository.findById(detail.getRelatedUserId()).orElse(null);
            if (relatedUser != null) {
                resp.setInvitedCustomerName(relatedUser.getName());
            } else {
                resp.setInvitedCustomerName("客户" + detail.getRelatedUserId());
            }
        } else {
            resp.setInvitedCustomerName("未知客户");
        }

        resp.setCreateTime(formatTime(detail.getCreateTime()));
        resp.setExpectedTime(formatTime(detail.getCreateTime() + 7 * 24 * 60 * 60 * 1000)); // 7天后到账
        resp.setRemark("订单完成后7天到账");

        return resp;
    }

    /**
     * 构建邀请记录查询条件
     */
    private Specification<InvitationRecordDO> buildInvitationRecordSpecification(Long userId, InvitedCustomerQueryReq request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 查询当前用户作为邀请人的记录
            predicates.add(criteriaBuilder.equal(root.get("inviterUserId"), userId));

            // 只查询已注册的用户（有注册时间）
            predicates.add(criteriaBuilder.isNotNull(root.get("registrationTime")));

            // 时间范围查询（基于注册时间）
            if (request.getStartTime() != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(
                        root.get("registrationTime"),
                        request.getStartTime()
                ));
            }

            if (request.getEndTime() != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(
                        root.get("registrationTime"),
                        request.getEndTime()
                ));
            }

            // 客户名称查询（关联用户表，只使用name字段）
            if (request.getCustomerName() != null && !request.getCustomerName().trim().isEmpty()) {
                // 创建与用户表的关联
                Join<InvitationRecordDO, UserDO> userJoin = root.join("inviteeUser", JoinType.LEFT);

                // 模糊查询用户姓名
                String customerNamePattern = "%" + request.getCustomerName().trim() + "%";
                Predicate namePredicate = criteriaBuilder.like(
                        criteriaBuilder.lower(userJoin.get("name")),
                        customerNamePattern.toLowerCase()
                );

                predicates.add(namePredicate);

                log.debug("添加客户名称查询条件：{}", request.getCustomerName());
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 转换邀请记录为客户响应对象
     */
    private InvitedCustomerResp convertInvitationRecordToResp(InvitationRecordDO invitationRecord) {
        InvitedCustomerResp resp = new InvitedCustomerResp();

        // 查询被邀请用户的基本信息
        if (invitationRecord.getInviteeUserId() != null) {
            UserDO inviteeUser = userRepository.findById(invitationRecord.getInviteeUserId()).orElse(null);
            if (inviteeUser != null) {
                resp.setCustomerId(invitationRecord.getInviteeUserId());
                resp.setCustomerName(inviteeUser.getName());
                resp.setCustomerCode(inviteeUser.getCode());
                resp.setCustomerAvatar(""); // UserDO中没有avatar字段，设为空字符串
            } else {
                // 如果用户不存在，使用邀请记录中的信息
                resp.setCustomerId(invitationRecord.getInviteeUserId());
                resp.setCustomerName("未知用户");
                resp.setCustomerCode(invitationRecord.getInviteeUserCode());
                resp.setCustomerAvatar("");
            }
        } else {
            // 如果没有被邀请用户ID，使用邀请记录中的编码
            resp.setCustomerId(0L);
            resp.setCustomerName("未注册用户");
            resp.setCustomerCode(invitationRecord.getInviteeUserCode());
            resp.setCustomerAvatar("");
        }

        // 邀请时间（使用注册时间，如果没有则使用邀请时间）
        if (invitationRecord.getRegistrationTime() != null) {
            resp.setInviteTime(formatTime(invitationRecord.getRegistrationTime()));
        } else if (invitationRecord.getInvitationTime() != null) {
            resp.setInviteTime(formatTime(invitationRecord.getInvitationTime()));
        } else {
            resp.setInviteTime("");
        }

        // 计算该客户的佣金统计信息
        if (invitationRecord.getInviteeUserId() != null) {
            calculateCustomerCommissionStats(resp, invitationRecord.getInviteeUserId());
        } else {
            // 未注册用户没有佣金信息
            resp.setTotalCommission(BigDecimal.ZERO);
            resp.setTotalOrderAmount(BigDecimal.ZERO);
            resp.setOrderCount(0);
            resp.setLastOrderTime("");
        }

        return resp;
    }

    /**
     * 基于佣金明细列表计算客户的佣金统计信息
     */
    private void calculateCustomerCommissionStatsFromDetails(InvitedCustomerResp resp, List<CommissionDetailDO> commissionDetails) {
        if (commissionDetails == null || commissionDetails.isEmpty()) {
            resp.setTotalCommission(BigDecimal.ZERO);
            resp.setTotalOrderAmount(BigDecimal.ZERO);
            resp.setOrderCount(0);
            resp.setLastOrderTime("");
            return;
        }

        // 计算总佣金
        BigDecimal totalCommission = commissionDetails.stream()
                .map(CommissionDetailDO::getChangeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        resp.setTotalCommission(totalCommission);

        // 计算订单数量（佣金明细数量）
        resp.setOrderCount(commissionDetails.size());

        List<CommissionDetailDO> orderCommission = commissionDetails.stream().filter(o -> o.getRelatedOrderNo() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderCommission)) {
            resp.setTotalOrderAmount(BigDecimal.ZERO);
            resp.setLastOrderTime("");
            return;
        }
        // 估算订单总金额（假设佣金比例为5%）
        BigDecimal estimatedOrderAmount = orderCommission.stream()
                .map(CommissionDetailDO::getChangeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        resp.setTotalOrderAmount(estimatedOrderAmount);

        // 最后一次订单时间
        Optional<Long> lastOrderTime = orderCommission.stream()
                .map(CommissionDetailDO::getCreateTime)
                .filter(Objects::nonNull)
                .max(Long::compareTo);

        if (lastOrderTime.isPresent()) {
            resp.setLastOrderTime(formatTime(lastOrderTime.get()));
        } else {
            resp.setLastOrderTime("");
        }
    }

    /**
     * 计算客户的佣金统计信息（保留原方法以兼容其他调用）
     */
    private void calculateCustomerCommissionStats(InvitedCustomerResp resp, Long customerId) {
        if (customerId == null) {
            resp.setTotalCommission(BigDecimal.ZERO);
            resp.setTotalOrderAmount(BigDecimal.ZERO);
            resp.setOrderCount(0);
            resp.setLastOrderTime("");
            return;
        }

        // 查询该客户相关的佣金明细（查询所有记录）
        Pageable allRecords = Pageable.unpaged();
        Page<CommissionDetailDO> commissionPage = commissionDetailRepository.findByRelatedUserIdOrderByTradeTimeDesc(customerId, allRecords);
        List<CommissionDetailDO> allCommissionDetails = commissionPage.getContent();

        // 过滤掉类型2（下级消费分佣转成待收益）的记录，这些不应该统计到客户佣金中
        List<CommissionDetailDO> commissionDetails = allCommissionDetails.stream()
                .filter(detail -> !CommissionTradeTypeEnum.ORDER_COMMISSION_PENDING.getCode().equals(detail.getTradeType()))
                .collect(Collectors.toList());

        calculateCustomerCommissionStatsFromDetails(resp, commissionDetails);
    }
}
