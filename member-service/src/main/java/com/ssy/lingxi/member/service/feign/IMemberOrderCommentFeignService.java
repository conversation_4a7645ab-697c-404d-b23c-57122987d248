package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.api.model.req.MemberOrderCommentReq;

import java.util.List;

/**
 * 会员订单评价feign服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/3
 */
public interface IMemberOrderCommentFeignService {

    /**
     * 保存订单数据
     * @param memberOrderCommentReq 接口参数
     * @return 返回结果
     */
    void saveMemberOrderComment(MemberOrderCommentReq memberOrderCommentReq);

    /**
     * 根据订单id集合查询待评价订单
     * @param orderIdList 订单id集合
     * @return 查询结果
     */
    Integer findWailtCommentByOrderIdList(List<Long> orderIdList, Integer type, UserLoginCacheDTO loginUser);
}
