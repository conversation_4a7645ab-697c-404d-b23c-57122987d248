package com.ssy.lingxi.member.controller.web.supplier;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.resp.basic.MemberInvitationCodeQueryResp;
import com.ssy.lingxi.member.model.resp.basic.MemberReceiveInvitationQueryResp;
import com.ssy.lingxi.member.model.resp.basic.MemberSendInvitationDetailResp;
import com.ssy.lingxi.member.model.resp.basic.MemberSendInvitationQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.StatusResp;
import com.ssy.lingxi.member.service.web.IMemberReceiveInvitationService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商能力 - 我收到的邀请信息相关接口
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/30 14:56
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/supplier/invitation")
public class SupplierInvitationController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.SUPPLIER.getCode();

    @Resource
    private IMemberReceiveInvitationService memberReceiveInvitationService;

    /**
     * 邀请状态下拉查询
     * @return 查询结果
     */
    @GetMapping("/stateList")
    public WrapperResp<List<StatusResp>> stateList() {
        return WrapperUtil.success(memberReceiveInvitationService.stateList());
    }

    /**
     * 我收到的邀请信息 - 列表查询
     * @param headers  Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/receive/page")
    public WrapperResp<PageDataResp<MemberReceiveInvitationQueryResp>> receivePage(@RequestHeader HttpHeaders headers, @Valid MemberReceiveInvitationPageDataReq pageVO) {
        return WrapperUtil.success(memberReceiveInvitationService.receivePage(headers, pageVO));
    }

    /**
     * 供应商邀请 - 列表查询
     * @param headers  Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/send/page")
    public WrapperResp<PageDataResp<MemberSendInvitationQueryResp>> sendPage(@RequestHeader HttpHeaders headers, @Valid MemberSendInvitationPageDataReq pageVO) {
        return WrapperUtil.success(memberReceiveInvitationService.sendPage(headers, pageVO, roleTag));
    }

    /**
     * 供应商邀请 - 新增
     * @param headers  Http头部信息
     * @param invitationVO 接口参数
     */
    @PostMapping("/add")
    public WrapperResp<Void> add(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberSendInvitationReq invitationVO) {
         memberReceiveInvitationService.add(headers, invitationVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 供应商邀请 - 详情
     * @param headers  Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail")
    public WrapperResp<MemberSendInvitationDetailResp> detail(@RequestHeader HttpHeaders headers, @Valid MemberSendInvitationEmailReq idVO) {
        return WrapperUtil.success(memberReceiveInvitationService.detail(headers, idVO, roleTag));
    }

    /**
     * 供应商邀请 - 更新
     * @param headers  Http头部信息
     * @param updateVO 接口参数
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberSendInvitationUpdateReq updateVO) {
         memberReceiveInvitationService.update(headers, updateVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 供应商邀请 - 删除
     * @param headers  Http头部信息
     * @param idVO 接口参数
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delete(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberSendInvitationIdReq idVO) {
         memberReceiveInvitationService.delete(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 供应商邀请 - 发送邀请码
     * @param headers  Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/send")
    public WrapperResp<Void> send(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberSendInvitationEmailReq idVO) {
         memberReceiveInvitationService.send(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 根据邀请码获取信息
     * @param headers  Http头部信息
     * @param invitationCodeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/info")
    public WrapperResp<MemberInvitationCodeQueryResp> info(@RequestHeader HttpHeaders headers, @Valid MemberInvitationCodeReq invitationCodeVO) {
        return WrapperUtil.success(memberReceiveInvitationService.info(headers, invitationCodeVO));
    }

}
