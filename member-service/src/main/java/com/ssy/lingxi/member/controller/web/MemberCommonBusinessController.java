package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.OrganizationPageDataReq;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRegisterTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberRightScoreResp;
import com.ssy.lingxi.member.model.resp.basic.UserDetailResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrganizationQueryResp;
import com.ssy.lingxi.member.service.web.IMemberCommonBusinessService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员业务能力其他接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/business")
public class MemberCommonBusinessController {

    @Resource
    private IMemberCommonBusinessService memberCommonBusinessService;

    /**
     * “积分兑换支付” - 查询会员平台通用和专有账户权益积分
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/lrc/right/point/get")
    public WrapperResp<MemberRightScoreResp> getMemberRightPoint(@RequestHeader HttpHeaders headers, @Valid MemberAndRoleIdReq idVO) {
        return WrapperUtil.success(memberCommonBusinessService.getMemberRightPoint(headers, idVO));
    }

    /**
     * “电子签章 - 企业认证” - 查询会员注册资料
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/detail/tag/get")
    public WrapperResp<MemberRegisterTagResp> getMemberRegisterTagDetail(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberCommonBusinessService.getMemberRegisterTagDetail(headers));
    }

    /**
     * “电子签章 - 个人认证” - 查询用户注册资料
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/user/detail/get")
    public WrapperResp<UserDetailResp> getUserDetail(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberCommonBusinessService.getUserDetail(headers));
    }

    /**
     * 分页查询用户组织机构
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/organization/page")
    public WrapperResp<PageDataResp<MemberOrganizationQueryResp>> pageMemberOrganizations(@RequestHeader HttpHeaders headers, OrganizationPageDataReq pageVO) {
        return WrapperUtil.success(memberCommonBusinessService.pageMemberOrganizations(headers, pageVO));
    }
}
