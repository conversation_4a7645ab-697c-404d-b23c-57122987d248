package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.BaseRightConfigDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-23
 */
@Repository
public interface BaseRightConfigRepository extends JpaRepository<BaseRightConfigDO, Long>, JpaSpecificationExecutor<BaseRightConfigDO> {
    List<BaseRightConfigDO> findByRightTypeEnumIn(List<Integer> typeEnums);

    BaseRightConfigDO findFirstByRightTypeEnum(Integer typeEnum);

    List<BaseRightConfigDO> findByStatus(Integer status);
}
