package com.ssy.lingxi.member.model.req.validate;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * 会员审核时，修改会员菜单权限接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-11
 */
@Data
public class MemberValidateUpdateAuthReq implements Serializable {
    private static final long serialVersionUID = -3368543820955023152L;

    /**
     * 会员Id
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;

    /**
     * 审核内容Id
     */
    @NotNull(message = "审核内容Id要大于0")
    @Positive(message = "审核内容Id要大于0")
    private Long validateId;

    /**
     * 勾选的菜单id集合
     */
    private Set<Long> menuIdList = new HashSet<>();

    /**
     * 勾选的按钮id集合
     */
    private Set<Long> buttonIdList = new HashSet<>();
}
