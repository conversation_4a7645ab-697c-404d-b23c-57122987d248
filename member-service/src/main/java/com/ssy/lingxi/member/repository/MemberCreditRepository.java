package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.MemberCreditDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员信用记录操作类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-20
 */
@Repository
public interface MemberCreditRepository extends JpaRepository<MemberCreditDO, Long>, JpaSpecificationExecutor<MemberCreditDO> {
    List<MemberCreditDO> findBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    @Transactional
    void deleteAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);
}
