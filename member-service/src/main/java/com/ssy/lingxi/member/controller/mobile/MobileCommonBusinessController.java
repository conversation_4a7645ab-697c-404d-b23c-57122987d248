package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.MemberLogoReq;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.resp.basic.MobileRegisterTagResp;
import com.ssy.lingxi.member.model.resp.basic.UserDetailResp;
import com.ssy.lingxi.member.service.mobile.IMobileCommonBusinessService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * App - 会员其他业务相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-02-03
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/business")
public class MobileCommonBusinessController {

    @Resource
    private IMobileCommonBusinessService mobileCommonBusinessService;

    /**
     * 新增或修改用户Logo
     * @param headers Http头部信息
     * @param logoVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/logo/add")
    public WrapperResp<Void> addMemberUserLogo(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLogoReq logoVO) {
        mobileCommonBusinessService.addMemberUserLogo(headers, logoVO);
        return WrapperUtil.success();
    }

    /**
     * 查询会员标签注册资料（电子签章 - 企业认证）
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail/tag")
    public WrapperResp<MobileRegisterTagResp> getMemberRegisterTagDetail(@RequestHeader HttpHeaders headers, @Valid MemberAndRoleIdReq idVO) {
        return WrapperUtil.success(mobileCommonBusinessService.getMemberRegisterTagDetail(headers, idVO));
    }

    /**
     * “电子签章 - 个人认证” - 查询用户注册资料
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/user/detail/get")
    public WrapperResp<UserDetailResp> getUserDetail(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(mobileCommonBusinessService.getUserDetail(headers));
    }
}
