package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.branch.MemberBranchAgentSaveOrUpdateReq;
import com.ssy.lingxi.member.model.req.branch.MemberBranchPageReq;
import com.ssy.lingxi.member.model.req.branch.MemberBranchSaveOrUpdateReq;
import com.ssy.lingxi.member.model.resp.branch.*;
import com.ssy.lingxi.member.service.web.IMemberBranchService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员店铺控制层
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/5/26
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/branch")
public class MemberBranchController extends BaseController {

    @Resource
    private IMemberBranchService memberBranchService;

    /**
     * 分页查询会员店铺
     *
     * @param pageReq 查询参数
     * @return 响应结果
     */
    @PostMapping("/page")
    public WrapperResp<PageDataResp<MemberBranchPageResp>> page(@RequestBody MemberBranchPageReq pageReq) {
        return WrapperUtil.success(memberBranchService.page(getSysUser(), pageReq));
    }

    /**
     * 获取会员店铺详情
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @PostMapping("/get")
    public WrapperResp<MemberBranchDetailResp> get(@RequestBody CommonIdReq req) {
        return WrapperUtil.success(memberBranchService.get(req));
    }

    /**
     * 代创建保存或更新店铺
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @PostMapping("/agentSaveOrUpdate")
    public WrapperResp<Void> agentSaveOrUpdate(@RequestBody @Valid MemberBranchAgentSaveOrUpdateReq req) {
        memberBranchService.agentSaveOrUpdate(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 保存或更新店铺
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @PostMapping("/saveOrUpdate")
    public WrapperResp<Void> saveOrUpdate(@RequestBody @Valid MemberBranchSaveOrUpdateReq req) {
        memberBranchService.saveOrUpdate(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 删除店铺
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delete(@RequestBody CommonIdReq req) {
        memberBranchService.delete(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 获取会员店铺列表
     *
     * @return 会员店铺列表
     */
    @PostMapping("/list")
    public WrapperResp<List<MemberBranchResp>> listMemberBranch() {
        return WrapperUtil.success(memberBranchService.listMemberBranch(getSysUser()));
    }

    /**
     * 会员分页查询
     *
     * @param pageReq 会员名称
     * @return 会员信息
     */
    @GetMapping("/memberPage")
    public WrapperResp<PageDataResp<MemberSimpleResp>> memberPage(@Valid MemberPageReq pageReq) {
        return WrapperUtil.success(memberBranchService.memberPage(pageReq));
    }

    /**
     * 查询字典数据
     */
    @GetMapping("/getDictDataList")
    public WrapperResp<MemberBranchSelectResp> getDictDataList() {
        return WrapperUtil.success(memberBranchService.getDictDataList());
    }

    /**
     * 绑定关系
     * @param req 请求参数
     * @return 操作结果
     */
    @PostMapping("/bindRelation")
    public WrapperResp<Void> bindRelation(@RequestBody @Valid MemberBranchRelationBindReq req) {
        memberBranchService.bindRelation(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 解除绑定关系
     *
     * @return 操作结果
     */
    @PostMapping("/unBindRelation")
    public WrapperResp<Void> unBindRelation(@RequestBody CommonIdReq req) {
        memberBranchService.unBindRelation(getSysUser(), req);
        return WrapperUtil.success();
    }

}
