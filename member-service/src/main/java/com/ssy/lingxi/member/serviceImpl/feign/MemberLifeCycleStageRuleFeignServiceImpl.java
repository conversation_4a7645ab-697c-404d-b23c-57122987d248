package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.member.api.model.req.MemberFeignWithLifeCycleRuleReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleRuleCheckReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleStagesRuleCheckBatchFeignReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleStagesRuleCheckFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLifeCycleRuleResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifeCycleRuleConfigDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifecycleStagesDO;
import com.ssy.lingxi.member.repository.MemberLifecycleStagesRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.feign.IMemberLifeCycleStageRuleFeignService;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/6/30 9:40
 */

@Service
public class MemberLifeCycleStageRuleFeignServiceImpl implements IMemberLifeCycleStageRuleFeignService {

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberLifecycleStagesRepository memberLifecycleStagesRepository;

    /**
     * 校验是否有配置会员某生命周期阶段状态,如果没配置生命周期做限制
     *
     * @param ruleCheckVO 接口参数
     * @return 返回
     */
    @Override
    public Boolean checkMemberLifeCycleStagesRule(MemberLifeCycleStagesRuleCheckFeignReq ruleCheckVO) {

//        // 签订合同
//        if (Objects.equals(ruleCheckVO.getLifeCycleStageRuleId(), MemberLifecycleStagesRuleEnum.SUPPLIER_ALLOW_CONTRACT_TO_BE_SIGNED.getCode())){
//
//            // 匹配客户供应商规则
//            boolean isMate = checkMemberLifeCycleStagesRule(ruleCheckVO.getMemberId(), ruleCheckVO.getRoleId(), ruleCheckVO.getSubMemberId(), ruleCheckVO.getSubRoleId(), MemberLifecycleStagesRuleEnum.SUPPLIER_ALLOW_CONTRACT_TO_BE_SIGNED.getCode(), ruleCheckVO.getRoleTag(), ruleCheckVO.getSubRoleTag());
//
//            // 匹配供应商客户规则
//            //return isMate && checkMemberLifeCycleStagesRule(ruleCheckVO.getSubMemberId(), ruleCheckVO.getSubRoleId(), ruleCheckVO.getMemberId(), ruleCheckVO.getRoleId(), MemberLifecycleStagesRuleEnum.CUSTOMER_ALLOW_CONTRACT_TO_BE_SIGNED.getCode(), ruleCheckVO.getRoleTag(), ruleCheckVO.getSubRoleTag());
//
//        }
//        else if (Objects.equals(ruleCheckVO.getLifeCycleStageRuleId(), MemberLifecycleStagesRuleEnum.CUSTOMER_ALLOW_CONTRACT_TO_BE_SIGNED.getCode())){
//
//            // 匹配供应商客户规则
//            boolean isMate = checkMemberLifeCycleStagesRule(ruleCheckVO.getMemberId(), ruleCheckVO.getRoleId(), ruleCheckVO.getSubMemberId(), ruleCheckVO.getSubRoleId(), MemberLifecycleStagesRuleEnum.CUSTOMER_ALLOW_CONTRACT_TO_BE_SIGNED.getCode(), ruleCheckVO.getRoleTag(), ruleCheckVO.getSubRoleTag());
//
//            // 匹配客户供应商规则
//            //return isMate && checkMemberLifeCycleStagesRule(ruleCheckVO.getSubMemberId(), ruleCheckVO.getSubRoleId(), ruleCheckVO.getMemberId(), ruleCheckVO.getRoleId(), MemberLifecycleStagesRuleEnum.SUPPLIER_ALLOW_CONTRACT_TO_BE_SIGNED.getCode(), ruleCheckVO.getRoleTag(), ruleCheckVO.getSubRoleTag());
//        }

        return checkMemberLifeCycleStagesRule(ruleCheckVO.getMemberId(), ruleCheckVO.getRoleId(), ruleCheckVO.getSubMemberId(), ruleCheckVO.getSubRoleId(), ruleCheckVO.getLifeCycleStageRuleId(), ruleCheckVO.getRoleTag(), ruleCheckVO.getSubRoleTag());
    }

    /**
     * 匹配生命周期规则，如果没配置生命周期做限制
     * @param memberId      上级会员
     * @param roleId        上级会员角色
     * @param subMemberId   下级会员
     * @param subRoleId     下级会员角色
     * @param rule           规则
     * @param roleTag        角色标识
     * @param subRoleTag     下级角色标签
     * @return Boolean
     */
    private Boolean checkMemberLifeCycleStagesRule(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Integer rule, Integer roleTag, Integer subRoleTag){

        List<MemberLifecycleStagesDO> stagesList = memberLifecycleStagesRepository.findByMemberIdAndRoleId(memberId, roleId);
        if (CollectionUtils.isEmpty(stagesList)) {
            return true;
        }

        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification(memberId, roleId, subMemberId, subRoleId, roleTag, subRoleTag));

        // 没有找到关联关系则返回true
        if (CollectionUtils.isEmpty(relationDOList) || Objects.isNull(relationDOList.get(0))) {
            return true;
        }

        MemberRelationDO memberRelation = relationDOList.get(0);

        // 匹配到指定的生命周期，返回true
        if (Objects.nonNull(memberRelation.getMemberLifecycleStages())) {
            List<Integer> ruleList = memberRelation.getMemberLifecycleStages().getMemberLifeCycleRuleConfigDOSet().stream().map(MemberLifeCycleRuleConfigDO::getLifeCycleRuleEnum).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ruleList)){
                return false;
            }
            return ruleList.contains(rule);
        }
        return false;
    }

    /**
     * 匹配相关生命周期规则,如果没配置生命周期不做限制
     *
     * @param ruleCheckVO 入参
     * @return true 符合校验规则 false不符合校验规则
     */
    @Override
    public Boolean checkMemberLifeCycleStagesRuleOrNotAstrict(MemberLifeCycleStagesRuleCheckFeignReq ruleCheckVO) {

        return checkMemberLifeCycleStagesRuleOrNotAstrict(ruleCheckVO.getMemberId(), ruleCheckVO.getRoleId(), ruleCheckVO.getSubMemberId(), ruleCheckVO.getSubRoleId(), ruleCheckVO.getLifeCycleStageRuleId(), ruleCheckVO.getRoleTag(), ruleCheckVO.getSubRoleTag());

    }

    /**
     * 匹配生命周期规则，如果没配置生命周期不做限制
     * @param memberId      上级会员
     * @param roleId        上级会员角色
     * @param subMemberId   下级会员
     * @param subRoleId     下级会员角色
     * @param rule           规则
     * @param roleTag        角色标识
     * @param subRoleTag     下级角色标签
     * @return Boolean
     */
    private Boolean checkMemberLifeCycleStagesRuleOrNotAstrict(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Integer rule, Integer roleTag, Integer subRoleTag){

        List<MemberLifecycleStagesDO> stagesList = memberLifecycleStagesRepository.findByMemberIdAndRoleId(memberId, roleId);
        if (CollectionUtils.isEmpty(stagesList)) {
            return true;
        }

        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification(memberId, roleId, subMemberId, subRoleId, roleTag, subRoleTag));

        // 没有找到关联关系则返回true
        if (CollectionUtils.isEmpty(relationDOList) || Objects.isNull(relationDOList.get(0))) {
            return true;
        }

        MemberRelationDO memberRelation = relationDOList.get(0);

        //如果配置生命周期阶段不做限制
        if (ObjectUtils.isEmpty(memberRelation.getMemberLifecycleStages()) && CollectionUtils.isEmpty(stagesList)) {
            return true;
        }

        // 匹配到指定的生命周期，返回true
        if (Objects.nonNull(memberRelation.getMemberLifecycleStages())) {
            List<Integer> ruleList = memberRelation.getMemberLifecycleStages().getMemberLifeCycleRuleConfigDOSet().stream().map(MemberLifeCycleRuleConfigDO::getLifeCycleRuleEnum).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ruleList)){
                return false;
            }
            return ruleList.contains(rule);
        }
        return false;
    }

    /**
     * 根据上级会员和生命周期规则ID查询该规则下所有下级会员
     *
     * @param cycleRuleVO 请求对象
     * @return 返回MemberFeignLifeCycleRuleVO对象
     */
    @Override
    public List<MemberFeignLifeCycleRuleResp> getSubMemberWithLifecycleRule(MemberFeignWithLifeCycleRuleReq cycleRuleVO) {
        List<MemberRelationDO> relationDOList = relationRepository.findAll(getSpecification(cycleRuleVO));
        if (CollectionUtils.isEmpty(relationDOList)) {
            return Collections.emptyList();
        }

        return relationDOList.stream().map(relation -> MemberFeignLifeCycleRuleResp.builder()
                .memberId(relation.getSubMemberId())
                .roleId(relation.getSubRoleId())
                .build()).collect(Collectors.toList());
    }

    /**
     * 校验会员是否配置某生命周期规则阶段
     *
     * @param memberLifeCycleRuleCheckReq 入参
     * @return 返回结果
     */
    @Override
    public Boolean checkMemberHasConfigureLifeCycleRule(MemberLifeCycleRuleCheckReq memberLifeCycleRuleCheckReq) {
        List<MemberLifecycleStagesDO> lifecycleStagesDOList = memberLifecycleStagesRepository.findByMemberIdAndRoleId(memberLifeCycleRuleCheckReq.getMemberId(), memberLifeCycleRuleCheckReq.getRoleId());
        if (!CollectionUtils.isEmpty(lifecycleStagesDOList)) {
            for (MemberLifecycleStagesDO memberLifecycleStagesDO : lifecycleStagesDOList) {
                Set<MemberLifeCycleRuleConfigDO> memberLifeCycleRuleConfigDOSet = memberLifecycleStagesDO.getMemberLifeCycleRuleConfigDOSet();
                if (!CollectionUtils.isEmpty(memberLifeCycleRuleConfigDOSet)) {
                    if (memberLifeCycleRuleConfigDOSet.stream().anyMatch(memberLifeCycleRuleConfigDO ->
                            memberLifeCycleRuleConfigDO.getLifeCycleRuleEnum().equals(memberLifeCycleRuleCheckReq.getLifeCycleStageRuleId()))) {
                        return true;
                    }

                }
            }
            return false;
        }

        return true;
    }

    /**
     * 查询条件
     * @return 返回条件
     */
    private Specification<MemberRelationDO> specification(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Integer roleTag, Integer subRoleTag) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), memberId));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), roleId));
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), subMemberId));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), subRoleId));
            if (NumberUtil.notNullOrZero(roleTag)) {
                list.add(criteriaBuilder.equal(root.get("roleTag").as(Integer.class), roleTag));
            }
            if (NumberUtil.notNullOrZero(subRoleTag)) {
                list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), subRoleTag));
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
    }

    /**
     * 查询条件
     *
     * @param cycleRuleVO 入参对象
     * @return 返回条件
     */
    private Specification<MemberRelationDO> getSpecification(MemberFeignWithLifeCycleRuleReq cycleRuleVO) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), cycleRuleVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), cycleRuleVO.getRoleId()));

            Join<Object, Object> lifecycleStageJoin = root.join("memberLifecycleStages", JoinType.LEFT);
            Join<Object, Object> ruleConfigJoin = lifecycleStageJoin.join("memberLifeCycleRuleConfigDOSet", JoinType.LEFT);
            list.add(criteriaBuilder.equal(ruleConfigJoin.get("lifeCycleRuleEnum").as(Integer.class), cycleRuleVO.getLifeCycleStageRuleId()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
    }

    /**
     * 批量校验是否允许创建请购订单合同
     *
     * @param memberLifeCycleStagesRuleCheckBatchFeignReq 入参
     * @return 返回结果
     */
    @Override
    public List<String> checkBatchMemberLifeCycleStagesRule(MemberLifeCycleStagesRuleCheckBatchFeignReq memberLifeCycleStagesRuleCheckBatchFeignReq) {
        List<MemberLifeCycleStagesRuleCheckFeignReq> voList = memberLifeCycleStagesRuleCheckBatchFeignReq.getMemberLifeCycleStagesRuleCheckFeignReqList();

        List<String> requisitionNoList = voList.stream()
                .filter(vo -> !checkMemberLifeCycleStagesRuleOrNotAstrict(vo.getMemberId(), vo.getRoleId(),
                        vo.getSubMemberId(), vo.getSubRoleId(), vo.getLifeCycleStageRuleId(),
                        vo.getRoleTag(), vo.getSubRoleTag()))
                .map(MemberLifeCycleStagesRuleCheckFeignReq::getRequisitionNo).collect(Collectors.toList());

        if (ObjectUtils.isEmpty(requisitionNoList)){
            return new ArrayList<>();
        }else {
            return requisitionNoList;
        }
    }
}
