package com.ssy.lingxi.member.model.req.platform;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 根据角色名称分页查询角色列表VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class PageRoleRuleDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = 5847827715528568764L;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 已经拥有的会员角色
     */
    private List<Long> checkRoleIdList;
}
