package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberCustomerQueryReq;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberMaterialInventoryUpdateReq;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberPositionInventoryRulesQueryDataReq;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberPositionInventoryUpdateReq;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberCustomerResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformPageQueryPositionInventoryRulesMemberResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 平台后台 - 平台规则 - 会员仓位同步物料库存规则相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/25
 */
public interface IPlatformMemberPositionInventoryRulesService {

    /**
     * 分页、模糊查询会员信息列表
     *
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    PageDataResp<PlatformPageQueryPositionInventoryRulesMemberResp> pageMembers(HttpHeaders headers, PlatformMemberPositionInventoryRulesQueryDataReq queryVO);

    /**
     * 修改仓位仓库模式
     *
     * @param headers  HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updatePositionInventory(HttpHeaders headers, PlatformMemberPositionInventoryUpdateReq updateVO);

    /**
     * 设置物料仓库模式
     *
     * @param headers  HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateMaterialInventory(HttpHeaders headers, PlatformMemberMaterialInventoryUpdateReq updateVO);

    /**
     * 获取会员全部服务消费者类型名称
     *
     * @param headers  HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    List<PlatformMemberCustomerResp> listMembersCustomer(HttpHeaders headers, PlatformMemberCustomerQueryReq queryVO);
}
