package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.menuAuth.ButtonNameDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version 3.0.0
 * @date 2024/4/10
 */
public interface ButtonNameRepository extends JpaRepository<ButtonNameDO, Long>, JpaSpecificationExecutor<ButtonNameDO> {
    List<ButtonNameDO> findAllByMenuId(Long menuId);

    void deleteByButtonId(Long buttonId);

    void deleteAllByMenuIdIn(Collection<Long> menuIdList);

    void deleteAllByButtonIdIn(Collection<Long> buttonIdList);

    Boolean existsByLanguageAndNameAndMenuId(String language, String name, Long menuId);

    Boolean existsByLanguageAndNameAndMenuIdAndButtonIdNot(String language, String name, Long menuId, Long buttonId);
}
