package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.PlatformUpdatePasswordReq;
import com.ssy.lingxi.member.model.req.login.EmailLoginSmsCode;
import com.ssy.lingxi.member.model.req.login.PhoneLoginSmsCode;
import com.ssy.lingxi.member.model.req.platform.PlatformManageUpdateForgetPasswordReq;
import com.ssy.lingxi.member.model.req.platform.PlatformUpdateForgetPasswordReq;
import com.ssy.lingxi.member.model.resp.platform.PlatformUpdateForgetPasswordResp;
import com.ssy.lingxi.member.service.web.IMemberAccountSecurityService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 平台后台 - 账户安全相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-10
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/manage/security")
public class PlatformAccountSecurityController {

    @Resource
    private IMemberAccountSecurityService accountSecurityService;

    /**
     * 平台后台- 登陆页面忘记密码 - 发送短信验证码
     *
     * @param headers  Http头部信息
     * @param phoneReq 接口参数
     * @return 发送结果
     */
    @PostMapping("/phoneSms")
    public WrapperResp<Void> sendManageLoginPhoneSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneLoginSmsCode phoneReq) {
        accountSecurityService.sendManageLoginPhoneSmsCode(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 平台后台- 登陆页面忘记密码 - 发送邮箱验证码
     *
     * @param headers  Http头部信息
     * @param emailReq 接口参数
     * @return 发送结果
     */
    @PostMapping("/emailSms")
    public WrapperResp<Void> sendManageLoginEmailSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailLoginSmsCode emailReq) {
        accountSecurityService.sendManageLoginEmailSmsCode(headers, emailReq);
        return WrapperUtil.success();
    }

    /**
     * 平台后台 - 普通用户更改密码
     *
     * @param headers    Http头部信息
     * @param passwordVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/forgetPsw/update")
    public WrapperResp<PlatformUpdateForgetPasswordResp> updatePlatformUserForgetPsw(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformUpdateForgetPasswordReq passwordVO) {
        return WrapperUtil.success(accountSecurityService.updatePlatformUserForgetPsw(headers, passwordVO));
    }

    /**
     * 平台后台 - 超管用户更改密码
     *
     * @param headers    Http头部信息
     * @param passwordVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/forgetPsw/managerUpdate")
    public WrapperResp<Void> updatePlatformManageUserForgetPsw(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformManageUpdateForgetPasswordReq passwordVO) {
        accountSecurityService.updatePlatformManageUserForgetPsw(headers, passwordVO);
        return WrapperUtil.success();
    }

    /**
     * 平台后台 - 更改用户密码（登陆状态下）
     *
     * @param headers    Http头部信息
     * @param passwordVO 接口参数
     * @return 修改结果
     */
    @RequestMapping("/psw/update")
    public WrapperResp<Void> updatePlatformUserPassword(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformUpdatePasswordReq passwordVO) {
        accountSecurityService.updatePlatformUserPassword(headers, passwordVO);
        return WrapperUtil.success();
    }
}
