package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.enums.MemberChangeRequestFormStatusEnum;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityMaintenanceMemberQueryDataReq;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 供应商能力 - 供应商生命周期管理服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
public interface IMemberLifecycleService {

    /**
     * 列表查询页面中各个下拉选择框的内容（档案管理查询）
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    MemberArchivesManagementConditionResp getPageCondition(HttpHeaders headers, Integer roleTag);

    /**
     * 状态下拉查询（变更申请单查询）
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<StatusResp> listMemberChangeRequestStatus(HttpHeaders headers);

    /**
     * 供应商档案管理 - 分页列表
     *
     * @param headers Http头部信息
     * @param queryVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberArchivesManagementQueryResp> memberArchivesManagementPage(HttpHeaders headers, MemberAbilityMaintenanceMemberQueryDataReq queryVO, Integer roleTag);

    /**
     * 变更申请单查询 - 分页列表
     *
     * @param headers       Http头部信息
     * @param pageVO        接口参数
     * @param currentStatus 当前状态
     * @param statusList    状态参数
     * @param roleTag       角色标签
     * @return 查询结果
     */
    PageDataResp<MemberChangeRequestSummaryPageQueryResp> baseMemberChangeRequestFormPage(HttpHeaders headers, MemberChangeRequestBasicPageDataReq pageVO, MemberChangeRequestFormStatusEnum currentStatus, List<Integer> statusList, Integer roleTag);

    /**
     * 变更申请单查询 - 详情
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberChangeRequestFormDetailQueryResp summaryMemberChangeRequestFormDetail(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 待新增变更申请单 - 新增
     *
     * @param headers Http头部信息
     * @param addVO   接口参数
     * @param roleTag 角色标签
     */
    void addMemberChangeRequestForm(HttpHeaders headers, MemberChangeRequestFormAddReq addVO, Integer roleTag);

    /**
     * 待新增变更申请单 - 修改
     *
     * @param headers  Http头部信息
     * @param updateVO 接口参数
     * @param roleTag  角色标签
     */
    void updateAddMemberChangeRequestForm(HttpHeaders headers, MemberChangeRequestFormUpdateReq updateVO, Integer roleTag);

    /**
     * 待新增变更申请单 -删除
     *
     * @param headers Http头部信息
     * @param idsVO   接口参数
     */
    void deleteMemberCheckRequestForm(HttpHeaders headers, CommonIdListReq idsVO);

    /**
     * 待评分人评分 - 分页列表
     *
     * @param pageVO        接口参数
     * @param headers       Http头部信息
     * @param currentStatus 当前状态
     * @param statusList    状态参数
     * @param roleTag       角色标签
     * @return 查询结果
     */
    PageDataResp<MemberChangeRequestSummaryPageQueryResp> waitGradeChangeRequestFormPage(HttpHeaders headers, MemberChangeRequestBasicPageDataReq pageVO, MemberChangeRequestFormStatusEnum currentStatus, List<Integer> statusList, Integer roleTag);

    /**
     * 待评分人评分 - 评分
     *
     * @param headers Http头部信息
     * @param gradeVO 接口参数
     * @param roleTag 角色标签
     */
    void gradeChangeRequestForm(HttpHeaders headers, MemberChangeRequestGradeReq gradeVO, Integer roleTag);

    /**
     * 待评分人评分 - 详情
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberChangeRequestFormDetailQueryResp waitGradeMemberChangeRequestFormDetail(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 待提交汇总评分结果 - 提交
     *
     * @param headers  Http头部信息
     * @param submitVO 接口参数
     * @param roleTag  角色标签
     */
    void submitMemberChangeRequestForm(HttpHeaders headers, MemberChangeRequestFormSubmitReq submitVO, Integer roleTag);

    /**
     * 待审核考评结果一级 - 审核
     *
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     * @param roleTag 角色标签
     */
    void auditOneMemberChangeRequestForm(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag);

    /**
     * 待审核考评结果二级 - 审核
     *
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     * @param roleTag 角色标签
     */
    void auditTwoMemberChangeRequestForm(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag);

    /**
     * 待确认变更申请单 - 确认变更申请单
     *
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     * @param roleTag 角色标签
     */
    void waitConfirmMemberChangeRequestFormPage(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag);
}
