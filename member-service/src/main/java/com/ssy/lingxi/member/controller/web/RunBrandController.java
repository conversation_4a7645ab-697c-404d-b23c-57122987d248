package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.config.BaiTaiMemberConfig;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.RunBrandAddReq;
import com.ssy.lingxi.member.model.resp.basic.RunBrandResp;
import com.ssy.lingxi.member.service.web.IRunBrandService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 经营品牌控制层
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/8/2
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/run/brand")
public class RunBrandController extends BaseController {

    @Resource
    private IRunBrandService runBrandService;
    @Resource
    private BaiTaiMemberConfig baiTaiMemberConfig;

    /**
     * 查询经营品牌列表
     *
     * @return 响应结果
     */
    @GetMapping("/list")
    public WrapperResp<List<RunBrandResp>> list() {
        return WrapperUtil.success(runBrandService.list(getSysUser()));
    }

    /**
     * 添加
     *
     * @param req 请求参数
     * @return 响应结果
     */
    @PostMapping("/add")
    public WrapperResp<Long> add(@RequestBody  @Valid  RunBrandAddReq req) {
        UserLoginCacheDTO sysUser = getSysUser();
        if(!sysUser.getMemberId().equals(baiTaiMemberConfig.baiTaiMemberProperties().getSelfMemberId())){
            return WrapperUtil.fail(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED);
        }
        return WrapperUtil.success(runBrandService.add(getSysUser(),req));
    }

}
