package com.ssy.lingxi.member.model.req.comment;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 订单评价接口查询参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberOrderTradeCommentDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = 1415845638818484475L;

    /**
     * 评价方名称
     */
    private String memberName;

    /**
     * 被评价方名称
     */
    private String subMemberName;

    /**
     * 评价星级（1-5）
     */
    private Integer star;

    /**
     * 评价内容
     */
    private String comment;

    /**
     * 评价时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    /**
     * 评价时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

    /**
     * 商品名称
     */
    private String product;

    /**
     * 交易时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dealTimeStart;

    /**
     * 交易时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dealTimeEnd;
}
