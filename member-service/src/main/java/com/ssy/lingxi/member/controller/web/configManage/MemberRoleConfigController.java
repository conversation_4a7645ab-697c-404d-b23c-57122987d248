package com.ssy.lingxi.member.controller.web.configManage;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.handler.annotation.MemberTypeAnno;
import com.ssy.lingxi.member.model.req.configManage.*;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameAndMemberTypeResp;
import com.ssy.lingxi.member.model.resp.configManage.*;
import com.ssy.lingxi.member.service.configManage.IManageRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 会员角色配置相关接口
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/2
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/memberRoleConfig")
@Validated
public class MemberRoleConfigController {
    private final IManageRoleService manageRoleService;

    /**
     * 查询会员角色类型
     *
     * @return 注册结果
     */
    @GetMapping("getMemberRoleTypeList")
    public WrapperResp<List<MemberConfigEnumResp>> getMemberRoleTypeList() {
        return WrapperUtil.success(manageRoleService.getMemberRoleTypeList());
    }

    /**
     * 查询会员类型
     *
     * @return 注册结果
     */
    @GetMapping("getMemberTypeList")
    public WrapperResp<List<MemberConfigEnumResp>> getMemberTypeList() {
        return WrapperUtil.success(manageRoleService.getMemberTypeList());
    }

    /**
     * 查询会员角色标签
     *
     * @return 注册结果
     */
    @GetMapping("getMemberRoleTagList")
    public WrapperResp<List<MemberConfigEnumResp>> getMemberRoleTagList() {
        return WrapperUtil.success(manageRoleService.getMemberRoleTagList());
    }

    /**
     * 基于会员类型查询会员角色
     *
     * @param memberType 会员类型
     * @return 注册结果
     */
    @GetMapping("getMemberRoleListByMemberType")
    public WrapperResp<List<RoleIdAndNameAndMemberTypeResp>> getMemberRoleListByMemberType(@MemberTypeAnno(required = false) Integer memberType) {
        return WrapperUtil.success(manageRoleService.getMemberRoleListByMemberType(memberType));
    }

    /**
     * 分页查询会员角色列表
     *
     * @param memberRolePageReq 接口参数
     * @return 分页查询结果
     */
    @GetMapping("/getMemberRolePage")
    public WrapperResp<PageDataResp<MemberRolePageResp>> getMemberRolePage(@Valid MemberRolePageDataReq memberRolePageReq) {
        return WrapperUtil.success(manageRoleService.getMemberRolePage(memberRolePageReq));
    }

    /**
     * 查询角色详情
     *
     * @param commonIdReq 接口参数
     * @return 会员角色信息
     */
    @GetMapping("/getMemberRoleById")
    public WrapperResp<MemberRoleResp> getMemberRoleById(@Valid CommonIdReq commonIdReq) {
        return WrapperUtil.success(manageRoleService.getMemberRoleById(commonIdReq));
    }

    /**
     * 查询角色关联的注册资料
     *
     * @param commonIdReq 接口参数
     * @return 注册资料
     */
    @GetMapping("/getRegisterConfigByMemberRoleId")
    public WrapperResp<List<MemberRoleConfigResp>> getRegisterConfigByMemberRoleId(@Valid CommonIdReq commonIdReq) {
        return WrapperUtil.success(manageRoleService.getRegisterConfigByMemberRoleId(commonIdReq));
    }

    /**
     * 新增角色
     *
     * @param manageMemberRoleReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/add")
    public WrapperResp<Long> addRole(@RequestBody @Validated(ManageMemberRoleReq.Add.class) ManageMemberRoleReq manageMemberRoleReq) {
        return WrapperUtil.success(manageRoleService.addRole(manageMemberRoleReq));
    }

    /**
     * 删除角色
     *
     * @param manageMemberRoleReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> deleteRole(@RequestBody @Validated(ManageMemberRoleReq.Delete.class) ManageMemberRoleReq manageMemberRoleReq) {
         manageRoleService.deleteRole(manageMemberRoleReq);
        return WrapperUtil.success();
    }

    /**
     * 更新角色
     *
     * @param manageMemberRoleReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> updateRole(@RequestBody @Validated(ManageMemberRoleReq.Update.class) ManageMemberRoleReq manageMemberRoleReq) {
         manageRoleService.updateRole(manageMemberRoleReq);
        return WrapperUtil.success();
    }

    /**
     * 更改角色状态
     *
     * @param manageMemberRoleReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/status")
    public WrapperResp<Void> updateRoleStatus(@RequestBody @Validated(ManageMemberRoleReq.UpdateStatus.class) ManageMemberRoleReq manageMemberRoleReq) {
         manageRoleService.updateRoleStatus(manageMemberRoleReq);
        return WrapperUtil.success();
    }

    /**
     * 设置（新增、修改、删除）会员角色关联的会员配置资料
     *
     * @param roleConfigReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/setRoleConfig")
    public WrapperResp<Void> setRoleConfig(@RequestBody @Valid ManageMemberRoleConfigReq roleConfigReq) {
         manageRoleService.setRoleConfig(roleConfigReq);
        return WrapperUtil.success();
    }

    /**
     * 查询会员角色关联的审核流程
     *
     * @param commonIdReq 接口参数
     * @return 已配置的流程Id，以及所有会员审核（注册）流程列表
     */
    @GetMapping("/findRegisterProcessByRoleId")
    public WrapperResp<MemberRoleRegisterProcessResp> findMemberRoleRegisterProcess(@Valid CommonIdReq commonIdReq) {
        return WrapperUtil.success(manageRoleService.findMemberRoleRegisterProcess(commonIdReq));
    }

    /**
     * 新增或修改会员角色关联的审核流程
     *
     * @param processRuleReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/setRegisterProcess")
    public WrapperResp<Void> setMemberRoleRegisterProcess(@RequestBody @Valid ProcessRuleReq processRuleReq) {
         manageRoleService.setMemberRoleRegisterProcess(processRuleReq);
        return WrapperUtil.success();
    }

    // *******************************************    角色菜单相关接口    *******************************************

    /**
     * 查询角色权限树（树形方式返回）
     *
     * @param authMenuReq 接口参数
     * @return 操作结果
     */
    @GetMapping("/authTree")
    public WrapperResp<AuthTreeResp> getMemberRoleAuth(@Valid MemberRoleAuthTreeReq authMenuReq) {
        return WrapperUtil.success(manageRoleService.getMemberRoleAuth(authMenuReq));
    }

    /**
     * 设置角色菜单按钮权限（并重制系统权限）
     *
     * @param menuButtonAuthReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/setRoleAuth")
    public WrapperResp<Void> setMemberRoleMenuButtonAuth(@RequestBody @Valid MemberRoleMenuButtonAuthReq menuButtonAuthReq) {
         manageRoleService.setMemberRoleMenuButtonAuth(menuButtonAuthReq);
        return WrapperUtil.success();
    }

    /**
     * 设置菜单及所有关联的按钮权限
     *
     * @param commonIdReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/setAllMenuAndButtons")
    public WrapperResp<Void> setAllMenuAndButtons(@Valid @RequestBody CommonIdReq commonIdReq) {
         manageRoleService.setAllMenuAndButtons(commonIdReq);
        return WrapperUtil.success();
    }

    /**
     * 平台会员角色+超管帐号权限初始化接口
     */
    @PostMapping("/initPlatformAdminAuth")
    public WrapperResp<Void> initPlatformAdminAuth() {
         manageRoleService.initPlatformAdminAuth();
        return WrapperUtil.success();
    }
}
