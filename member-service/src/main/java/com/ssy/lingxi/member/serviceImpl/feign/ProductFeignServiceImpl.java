package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.member.service.feign.IProductFeignService;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.model.req.CommodityCreditScoreReq;
import com.ssy.lingxi.product.api.model.req.feign.OffPublishCommodityReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 调用商品服务Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-01-24
 */
@Service
public class ProductFeignServiceImpl implements IProductFeignService {
    private static final Logger logger = LoggerFactory.getLogger(ProductFeignServiceImpl.class);

    @Resource
    private ICommodityFeign commodityFeign;

    /**
     * 平台会员被冻结时，通知商品服务下架所有商品
     *
     * @param memberId   会员Id
     * @param roleId     角色Id
     * @param memberTypeEnum 会员类型枚举
     */
    @Async
    @Override
    public void asyncNotifyToOffProducts(Long memberId, Long roleId, Integer memberTypeEnum) {
        try {
            OffPublishCommodityReq request = new OffPublishCommodityReq();
            request.setMemberId(memberId);
            request.setMemberRoleId(roleId);
            WrapperResp<Boolean> result = commodityFeign.offPublishAllCommodity(request);
            if(result == null) {
                logger.error("会员被冻结时，异步通知商品服务下架会员商品错误, 返回结果为null");
                return;
            }

            if(result.getCode() != ResponseCodeEnum.SUCCESS.getCode() || !result.getData()) {
                logger.error("会员被冻结时，异步通知商品服务下架会员商品错误, code:" + result.getCode() + ", data:" + result.getData());
            }

        } catch (Exception e) {
            logger.error("会员被冻结时，异步通知商品服务下架会员商品错误", e);
        }
    }

    /**
     * 更新商品对应的店铺平台信用积分
     * @param memberId        平台会员Id
     * @param roleId     平台会员角色Id
     * @param memberTypeEnum 会员类型枚举
     * @param plusCreditScore 平台会员信用积分变动的分数
     */
    @Override
    public void updateCommodityCreditScoreAsync(Long memberId, Long roleId, Integer memberTypeEnum, Integer plusCreditScore) {
        try {
            CommodityCreditScoreReq creditScore = new CommodityCreditScoreReq();
            creditScore.setMemberId(memberId);
            creditScore.setMemberRoleId(roleId);
            creditScore.setCreditScore(plusCreditScore);
            commodityFeign.updateCommodityCreditScore(creditScore);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("更新商品对应的店铺积分错误,MemberId:" + memberId + ", roleId:" + roleId, e);
        }
    }
}
