package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 平台后台会员生命周期更新
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformMemberCycleProcessUpdateReq extends PlatformMemberCycleProcessReq implements Serializable {

    private static final long serialVersionUID = 2546384007997393584L;

    /**
     * 流程规则配置Id
     */
    @NotNull(message = "流程规则配置id要大于0")
    @Positive(message = "流程规则配置id要大于0")
    private Long processId;
}
