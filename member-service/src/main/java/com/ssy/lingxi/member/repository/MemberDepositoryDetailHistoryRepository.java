package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailHistoryDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员入库资料历史记录Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-06-02
 */
@Repository
public interface MemberDepositoryDetailHistoryRepository extends JpaRepository<MemberDepositoryDetailHistoryDO, Long>, JpaSpecificationExecutor<MemberDepositoryDetailHistoryDO> {
    Page<MemberDepositoryDetailHistoryDO> findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Pageable pageable);
}
