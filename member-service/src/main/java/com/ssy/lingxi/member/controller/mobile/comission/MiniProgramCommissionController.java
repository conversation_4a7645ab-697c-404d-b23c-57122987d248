package com.ssy.lingxi.member.controller.mobile.comission;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.commission.ApplyWithdrawalReq;
import com.ssy.lingxi.member.model.req.commission.BindBankCardReq;
import com.ssy.lingxi.member.model.req.commission.CommissionDetailQueryReq;
import com.ssy.lingxi.member.model.req.commission.CommissionWithdrawalQueryReq;
import com.ssy.lingxi.member.model.req.commission.InvitedCustomerQueryReq;
import com.ssy.lingxi.member.model.resp.commission.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.commission.ICommissionAccountService;
import com.ssy.lingxi.member.service.commission.ICommissionConfigService;
import com.ssy.lingxi.member.service.commission.ICommissionWithdrawalService;
import com.ssy.lingxi.member.service.commission.IMiniProgramCommissionService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 小程序 - 佣金管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/commission")
public class MiniProgramCommissionController {

    @Resource
    private IMiniProgramCommissionService miniProgramCommissionService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private ICommissionWithdrawalService commissionWithdrawalService;

    @Resource
    private ICommissionAccountService commissionAccountService;

    @Resource
    private ICommissionConfigService commissionConfigService;

    /**
     * 查看分佣配置
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<CommissionConfigResp> getCommissionConfig(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(commissionConfigService.getCommissionConfig());
    }

    // ==================================佣金账户信息===============================

    /**
     * 获取佣金账户信息
     * @param headers Http头部信息
     * @return 佣金账户信息
     */
    @GetMapping("/getAccountInfo")
    public WrapperResp<MiniProgramAccountInfoResp> getAccountInfo(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        MiniProgramAccountInfoResp result = miniProgramCommissionService.getAccountInfo(loginUser);
        return WrapperUtil.success(result);
    }

    // ==================================我的佣金客户===============================

    /**
     * 分页查询邀请客户列表
     * @param headers Http头部信息
     * @param request 查询请求
     * @return 分页结果
     */
    @PostMapping("/getInvitedCustomerPage")
    public WrapperResp<PageDataResp<InvitedCustomerResp>> getInvitedCustomerPage(@RequestHeader HttpHeaders headers,
                                                                                @RequestBody @Valid InvitedCustomerQueryReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        PageDataResp<InvitedCustomerResp> result = miniProgramCommissionService.getInvitedCustomerPage(loginUser, request);
        return WrapperUtil.success(result);
    }

    /**
     * 查询邀请客户统计信息
     * @param headers Http头部信息
     * @param request 查询请求（与getInvitedCustomerPage相同的查询条件）
     * @return 统计结果（总人数、累计佣金金额）
     */
    @PostMapping("/getInvitedCustomerStatistics")
    public WrapperResp<InvitedCustomerStatisticsResp> getInvitedCustomerStatistics(@RequestHeader HttpHeaders headers,
                                                                                   @RequestBody @Valid InvitedCustomerQueryReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        InvitedCustomerStatisticsResp result = miniProgramCommissionService.getInvitedCustomerStatistics(loginUser, request);
        return WrapperUtil.success(result);
    }




    // ==================================佣金收益记录===============================

    /**
     * 分页查询佣金收益记录列表
     * @param headers Http头部信息
     * @param request 查询请求
     * @return 分页结果
     */
    @PostMapping("/getCommissionIncomeRecords")
    public WrapperResp<PageDataResp<CommissionIncomeRecordResp>> getCommissionIncomeRecords(@RequestHeader HttpHeaders headers,
                                                                                           @RequestBody @Valid CommissionDetailQueryReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        PageDataResp<CommissionIncomeRecordResp> result = miniProgramCommissionService.getCommissionIncomeRecords(loginUser, request);
        return WrapperUtil.success(result);
    }

    // ==================================佣金待收益记录===============================

    /**
     * 分页查询佣金待收益记录列表
     * @param headers Http头部信息
     * @param request 查询请求
     * @return 分页结果
     */
    @PostMapping("/getPendingCommissionRecords")
    public WrapperResp<PageDataResp<PendingCommissionRecordResp>> getPendingCommissionRecords(@RequestHeader HttpHeaders headers,
                                                                                             @RequestBody @Valid CommissionDetailQueryReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        PageDataResp<PendingCommissionRecordResp> result = miniProgramCommissionService.getPendingCommissionRecords(loginUser, request);
        return WrapperUtil.success(result);
    }

    // ==================================提现记录===============================

    /**
     * 分页查询提现记录列表
     * @param headers Http头部信息
     * @param request 查询请求
     * @return 分页结果
     */
    @PostMapping("/getWithdrawalRecords")
    public WrapperResp<PageDataResp<MiniProgramWithdrawalResp>> getWithdrawalRecords(@RequestHeader HttpHeaders headers,
                                                                                    @RequestBody @Valid CommissionWithdrawalQueryReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        PageDataResp<MiniProgramWithdrawalResp> result = miniProgramCommissionService.getWithdrawalRecords(loginUser, request);
        return WrapperUtil.success(result);
    }

    /**
     * 用户申请提现
     *
     * @param headers Http头部信息
     * @param request 申请提现请求
     * @return 操作结果
     */
    @PostMapping("/apply")
    public WrapperResp<Void> applyWithdrawal(@RequestHeader HttpHeaders headers,
                                             @RequestBody @Valid ApplyWithdrawalReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        commissionWithdrawalService.applyWithdrawal(loginUser, request.getBankCardId(), request.getWithdrawalAmount());
        return WrapperUtil.success();
    }

    // ==================================银行卡管理===============================

    /**
     * 绑定银行卡
     * @param headers Http头部信息
     * @param request 绑定银行卡请求
     * @return 操作结果
     */
    @PostMapping("/bindBankCard")
    public WrapperResp<Long> bindBankCard(@RequestHeader HttpHeaders headers,
                                         @RequestBody @Valid BindBankCardReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        Long bankCardId = commissionAccountService.bindBankCard(loginUser, request);
        return WrapperUtil.success(bankCardId);
    }

    /**
     * 查询银行卡列表
     * @param headers Http头部信息
     * @return 银行卡列表
     */
    @GetMapping("/getBankCardList")
    public WrapperResp<List<BankCardResp>> getBankCardList(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        Long userId = loginUser.getUserId();
        List<BankCardResp> result = commissionAccountService.getBankCardList(loginUser, userId);
        return WrapperUtil.success(result);
    }
}
