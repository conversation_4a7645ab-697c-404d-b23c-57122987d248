package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.bo.LevelBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelConfigDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRightDO;
import com.ssy.lingxi.member.model.resp.basic.LevelAndTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberRightScoreResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailRightConfigResp;

import java.util.List;

/**
 * 会员等级配置（内部工具类）相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-23
 */
public interface IBaseMemberLevelConfigService {
    /**
     * 是否存在等级配置
     * @param memberId 上级会员Id
     * @param roleId 上级会员角色Id
     * @param subRoleId 下级会员角色Id
     * @param level 等级
     * @return 存在-ture，不存在-false
     */
    Boolean existLevel(Long memberId, Long roleId, Long subRoleId, Integer level);

    /**
     * 查询指定等级
     * @param relationDO 会员关系
     * @param specificLevel 指定的等级
     * @return 指定等级，如果没有返回Null
     */
    MemberLevelConfigDO findLevel(MemberRelationDO relationDO, Integer specificLevel);

    /**
     * 查询指定等级
     * @param memberId 上级会员Id
     * @param roleId   上级会员角色Id
     * @param subRoleId 下级会员角色Id
     * @param specificLevel 指定的等级
     * @return 指定等级，如果没有返回Null
     */
    MemberLevelConfigDO findLevel(Long memberId, Long roleId, Long subRoleId, Integer specificLevel);

    /**
     * 查询最小等级配置
     * @param relationDO 会员关系
     * @return 最小等级配置，如没有配置返回Null
     */
    MemberLevelConfigDO findFirstLevel(MemberRelationDO relationDO);

    /**
     * 查询最小等级配置
     * @param memberId 上级会员Id
     * @param roleId   上级会员角色Id
     * @param subRoleId 下级会员角色Id
     * @return 最小等级配置，如没有配置返回Null
     */
    MemberLevelConfigDO findFirstLevel(Long memberId, Long roleId, Long subRoleId);

    /**
     * 查询下级会员的下一等级标签
     * @param relationDO 会员关系
     * @param levelDO    下级会员当前等级
     * @return 下一等级标签，如无下一等级返回空字符串
     */
    String findNextLevelTag(MemberRelationDO relationDO, MemberLevelRightDO levelDO);

    /**
     * 查询下级会员的下一等级标签
     * @param memberId 上级会员Id
     * @param roleId   上级会员角色Id
     * @param subRoleId 下级会员角色Id
     * @param currentLevel 下级会员当前等级
     * @return 下一等级标签，如无下一等级返回空字符串
     */
    String findNextLevelTag(Long memberId, Long roleId, Long subRoleId, Integer currentLevel);

    /**
     * 查询下级会员的下一等级标签及升级阈值
     * @param memberId 上级会员Id
     * @param roleId   上级会员角色Id
     * @param subRoleId 下级会员角色Id
     * @param currentLevel 下级会员当前等级
     * @return 下一等级标签，如无下一等级返回空字符串
     */
    LevelBO findNextLevel(Long memberId, Long roleId, Long subRoleId, Integer currentLevel);

    /**
     * 查询下级会员等级列表，转换为前端下拉框选项
     * @param memberId 上级会员Id
     * @param roleId   上级会员角色Id
     * @return 下拉框选项
     */
    List<LevelAndTagResp> listSubMemberLevels(Long memberId, Long roleId);

    /**
     * 查询下级会员等级列表，转换为前端下拉框选项
     * @param memberId 上级会员Id
     * @param roleId   上级会员角色Id
     * @param subRoleId 下级会员角色Id
     * @return 下拉框选项
     */
    List<LevelAndTagResp> listSubMemberLevels(Long memberId, Long roleId, Long subRoleId);

    /**
     * （新增下级会员时）查询下级会员等级列表，转换为前端下拉框选项
     * @param memberId 上级会员Id
     * @param roleId   上级会员角色Id
     * @param subRoleId 下级会员角色Id
     * @param memberType 会员类型Id（不是枚举）
     * @return 下拉框选项
     */
    List<LevelAndTagResp> listSubMemberLevels(Long memberId, Long roleId, Long subRoleId, Integer memberType);

    /**
     * 查询会员平台通用、会员专有的权益积分
     *
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级会员角色Id
     * @param subMemberId   下级会员Id
     * @param subRoleId     下级会员角色Id
     * @return 查询结果
     */
    MemberRightScoreResp getMemberRightPoint(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId);

    /**
     * 查询会员最低等级的权益列表
     *
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级会员角色Id
     * @param subRoleId     下级会员角色Id
     * @return 查询结果
     */
    List<MemberDetailRightConfigResp> findSubMemberRights(Long upperMemberId, Long upperRoleId, Long subRoleId);

    /**
     * 查询会员等级配置的下级会员角色Id和角色名称列表（去重）
     * @param memberId 上级会员Id
     * @param roleId 上级会员角色Id
     * @return 查询结果
     */
    List<RoleIdAndNameResp> findLevelConfigRoles(Long memberId, Long roleId);
}
