package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.MemberAfterApprovalLifecycleStagesDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 入库审核通过后、会员所处生命周期阶段Repository
 */
@Repository
public interface MemberAfterApprovalLifecycleStagesRepository extends JpaRepository<MemberAfterApprovalLifecycleStagesDO, Long>, JpaSpecificationExecutor<MemberAfterApprovalLifecycleStagesDO> {

    void deleteByMemberIdAndRoleIdAndRoleTag(Long memberId, Long memberRoleId, Integer roleTag);

    MemberAfterApprovalLifecycleStagesDO findByMemberIdAndAndRoleIdAndRoleTag(Long memberId, Long memberRoleId, Integer roleTag);
}
