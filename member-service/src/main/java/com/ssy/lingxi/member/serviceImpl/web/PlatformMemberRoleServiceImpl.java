package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.LanguageEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.basic.UserRoleDO;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberRoleGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberRoleQueryResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.repository.UserRoleRepository;
import com.ssy.lingxi.member.service.base.IBaseAuthService;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseTokenManageService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IPlatformMemberRoleService;
import com.ssy.lingxi.support.api.feign.ITencentIMFeign;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 平台后台 - 会员自定义角色服务接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-30
 */
@Service
public class PlatformMemberRoleServiceImpl implements IPlatformMemberRoleService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private ITencentIMFeign tencentIMFeign;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseAuthService baseAuthService;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private MemberRelationRepository memberRelationRepository;

    @Resource
    private IBaseTokenManageService tokenManageService;

    @Override
    public PageDataResp<PlatformMemberRoleQueryResp> pageMemberRole(HttpHeaders headers, MemberRolePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Specification<UserRoleDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
            list.add(criteriaBuilder.equal(memberJoin.get("id").as(Long.class), loginUser.getMemberId()));

            // 过滤超管角色
            list.add(criteriaBuilder.notEqual(root.get("userType").as(Integer.class), UserTypeEnum.ADMIN.getCode()));

            if(pageVO.getStatus() != null) {
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), pageVO.getStatus()));
            }

            if(StringUtils.hasLength(pageVO.getRoleName())) {
                list.add(criteriaBuilder.like(root.get("roleName").as(String.class), "%" + pageVO.getRoleName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<UserRoleDO> result = userRoleRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(r -> {
            PlatformMemberRoleQueryResp queryVO = new PlatformMemberRoleQueryResp();
            queryVO.setId(r.getId());
            queryVO.setRoleName(r.getRoleName());
            queryVO.setRemark(r.getRemark());
            queryVO.setStatus(r.getStatus());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addMemberRole(HttpHeaders headers, PlatformMemberRoleAddReq addVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        if (userRoleRepository.existsByMemberAndRoleName(memberDO, addVO.getRoleName())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_EXISTS);
        }

        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setMember(memberDO);
        userRoleDO.setUserType(UserTypeEnum.NORMAL.getCode());
        userRoleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userRoleDO.setUsers(new HashSet<>());
        userRoleDO.setRoleName(addVO.getRoleName());
        userRoleDO.setRemark(StringUtils.hasLength(addVO.getRemark()) ? addVO.getRemark().trim() : "");
        //设置权限
        userRoleDO.setMenuAuth(BitMapUtil.toByteArray(addVO.getMenuIdList()));
        userRoleDO.setButtonAuth(BitMapUtil.toByteArray(addVO.getButtonIdList()));
        userRoleDO.setApiAuth(BitMapUtil.emptyByteArray());
        userRoleDO.setHasImAuth(addVO.getImFlag() ? EnableDisableStatusEnum.ENABLE.getCode() : EnableDisableStatusEnum.DISABLE.getCode());
        userRoleDO.setAuthConfig(new HashSet<>());

        userRoleRepository.saveAndFlush(userRoleDO);

        // TODO: 2023/8/10 是否兼容工作流的authBO
//        workflowFeignService.upsertMemberRoleToProcessAsync(loginUser.getMemberId(), loginUser.getMemberRoleId(), memberRoleDO.getRoleName(), roleAuthList);
    }

    @Override
    public PlatformMemberRoleGetResp getMemberRole(HttpHeaders headers, UserRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserRoleDO memberRoleDO = userRoleRepository.findFirstByMemberAndId(memberDO, idVO.getRoleId());
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        PlatformMemberRoleGetResp result = new PlatformMemberRoleGetResp();
        result.setId(memberRoleDO.getId());
        result.setRoleName(memberRoleDO.getRoleName());
        result.setRemark(memberRoleDO.getRemark());
        result.setStatus(memberRoleDO.getStatus());
        result.setImFlag(memberRoleDO.getHasImAuth());

        return result;
    }

    @Override
    public void deleteMemberRole(HttpHeaders headers, UserRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserRoleDO memberRoleDO = userRoleRepository.findFirstByMemberAndId(memberDO, idVO.getRoleId());
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        //不能更改会员“超级管理员”用户
        if (memberRoleDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELTE_OR_UPDATE_MEMBER_ROLE);
        }

        //如果已经有用户，不能删除
        if (!CollectionUtils.isEmpty(memberRoleDO.getUsers())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_CANNOT_DELETE_WHEN_USER_EXISTS);
        }

        userRoleRepository.delete(memberRoleDO);

        workflowFeignService.removeMemberRoleInProcessAsync(loginUser.getMemberId(), idVO.getRoleId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMemberRole(HttpHeaders headers, PlatformMemberRoleUpdateReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserRoleDO userRoleDO = userRoleRepository.findFirstByMemberAndId(memberDO, updateVO.getRoleId());
        if (userRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        if (userRoleRepository.existsByMemberAndRoleNameAndIdNot(memberDO, updateVO.getRoleName(), userRoleDO.getId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_EXISTS);
        }

        //不能更改会员“超级管理员”用户
        if (userRoleDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELTE_OR_UPDATE_MEMBER_ROLE);
        }

        userRoleDO.setHasImAuth(updateVO.getImFlag() ? EnableDisableStatusEnum.ENABLE.getCode() : EnableDisableStatusEnum.DISABLE.getCode());
        userRoleDO.setRoleName(updateVO.getRoleName());
        userRoleDO.setRemark(StringUtils.hasLength(updateVO.getRemark()) ? updateVO.getRemark().trim() : "");
        userRoleDO.setMenuAuth(BitMapUtil.toByteArray(updateVO.getMenuIdList()));
        userRoleDO.setButtonAuth(BitMapUtil.toByteArray(updateVO.getButtonIdList()));
        userRoleRepository.saveAndFlush(userRoleDO);

        //更新使用该角色的相关用户权限
        baseAuthService.updateUserAuthByUserRole(userRoleDO);

        //删除指定用户的所有token
        CompletableFuture.runAsync(() -> tokenManageService.userOffline(userRoleDO.getUsers().stream().map(UserDO::getId).collect(Collectors.toList())), ThreadPoolConfig.asyncDefaultExecutor);

        // TODO: 2023/8/10 是否兼容工作流的authBO
//        workflowFeignService.upsertMemberRoleToProcessAsync(loginUser.getMemberId(), loginUser.getMemberRoleId(), memberRoleDO.getRoleName(), memberRoleDO.getAuth());
    }

    @Override
    public void updateMemberRoleStatus(HttpHeaders headers, MemberRoleUpdateStatusReq statusVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserRoleDO memberRoleDO = userRoleRepository.findFirstByMemberAndId(memberDO, statusVO.getId());
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        //不能更改会员“超级管理员”用户
        if (memberRoleDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELTE_OR_UPDATE_MEMBER_ROLE);
        }

        //如果已经有用户，不能修改状态
        if (!CollectionUtils.isEmpty(memberRoleDO.getUsers())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_CANNOT_DELETE_WHEN_USER_EXISTS);
        }

        memberRoleDO.setStatus(statusVO.getStatus());
        userRoleRepository.saveAndFlush(memberRoleDO);
    }

    @Override
    public AuthTreeResp getMemberAuthTree(HttpHeaders headers, UserRoleAuthTreeReq authTreeReq) {
        memberCacheService.needLoginFromManagePlatform(headers);

        byte[] menuAuth = null;
        byte[] buttonAuth = null;
        if (Objects.nonNull(authTreeReq.getRoleId())) {
            UserRoleDO userRoleDO = userRoleRepository.findById(authTreeReq.getRoleId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));
            menuAuth = userRoleDO.getMenuAuth();
            buttonAuth = userRoleDO.getButtonAuth();
        }

        // 可视菜单按钮权限范围不超过平台后台的权限范围
        String currentLanguage = LanguageEnum.getCurrentLanguage();
        return baseAuthService.getAuthTree(
                menuAuth,
                buttonAuth,
                CompletableFuture.supplyAsync(() -> baseAuthService.getMenuSetBySource(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode(), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor),
                CompletableFuture.supplyAsync(() -> baseAuthService.getButtonSetBySource(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode(), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor));
    }
}
