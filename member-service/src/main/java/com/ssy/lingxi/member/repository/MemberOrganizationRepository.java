package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberOrganizationDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员自定义组织机构操作类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
@Repository
public interface MemberOrganizationRepository extends JpaRepository<MemberOrganizationDO, Long>, JpaSpecificationExecutor<MemberOrganizationDO> {
    boolean existsByMemberAndTitle(MemberDO memberDO, String title);

    List<MemberOrganizationDO> findByMemberAndKeyStartsWith(MemberDO memberDO, String key);

}
