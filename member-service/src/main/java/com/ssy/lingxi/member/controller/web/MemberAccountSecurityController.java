package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberCancellationFailResp;
import com.ssy.lingxi.member.model.resp.maintenance.UserAccountAuthQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.UserAccountSecurityQueryResp;
import com.ssy.lingxi.member.service.web.IMemberAccountSecurityService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 系统能力 - 账户安全
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-06
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/security")
public class MemberAccountSecurityController {
    @Resource
    private IMemberAccountSecurityService userAccountSecurityService;

    /**
     * 账户安全-实名验证-保存身份证信息
     * @param headers Http头部信息
     * @param userAuthInfoReq 实名信息请求体
     * @return 操作结果
     */
    @PostMapping("/saveAuthInfo")
    public WrapperResp<Void> saveAuthInfo(@RequestHeader HttpHeaders headers, @RequestBody @Valid UserAuthInfoReq userAuthInfoReq) {
        userAccountSecurityService.saveAuthInfo(headers, userAuthInfoReq);
        return WrapperUtil.success();
    }

    /**
     * 账户安全-实名验证-上传身份证信息
     *
     * @return 操作结果
     */
    @PostMapping("/uploadIdCard")
    public WrapperResp<UserAccountAuthQueryResp> uploadIdCard(@RequestBody @Valid UserUploadIdCardReq userUploadIdCardReq) {
        return WrapperUtil.success(userAccountSecurityService.uploadIdCard(userUploadIdCardReq));
    }

    /**
     * 账户安全-实名验证-显示已实名认证的信息
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/getUserInfo")
    public WrapperResp<UserAccountAuthQueryResp> getUserInfo(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(userAccountSecurityService.getUserInfo(headers));
    }

    /**
     * “账户安全”页面，查询用户的手机号码和邮箱，以掩码方式返回
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/get")
    public WrapperResp<UserAccountSecurityQueryResp> getPhoneAndEmail(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(userAccountSecurityService.getPhoneAndEmail(headers));
    }

    /**
     * （通用）检查支付密码是否正确
     * @param headers Http头部信息
     * @param payPasswordReq 接口参数
     * @return 操作结果
     */
    @RequestMapping("/pay/check")
    public WrapperResp<Void> checkPayPassword(@RequestHeader HttpHeaders headers, @RequestBody @Valid PayPasswordReq payPasswordReq) {
        userAccountSecurityService.checkPayPassword(headers, payPasswordReq);
        return WrapperUtil.success();
    }

    /**
     * 修改登录密码（手机校验码验证）时，发送手机短信验证码
     * @param headers Http头部信息
     * @return 操作结果
     */
    @PostMapping("/psw/sms")
    public WrapperResp<Void> sendChangePasswordSmsCode(@RequestHeader HttpHeaders headers) {
        userAccountSecurityService.sendChangePasswordSmsCode(headers);
        return WrapperUtil.success();
    }

    /**
     * 修改登录密码（手机校验码验证）时，检查手机短信验证码是否正确
     * @param headers Http头部信息
     * @param smsCodeReq 接口参数
     * @return 操作结果
     */
    @RequestMapping("/psw/sms/check")
    public WrapperResp<Void> checkChangePasswordSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid SmsCodeReq smsCodeReq) {
        userAccountSecurityService.checkChangePasswordSmsCode(headers, smsCodeReq);
        return WrapperUtil.success();
    }

    /**
     * 修改登录密码（邮箱验证）时，发送邮件验证码
     * @param headers Http头部信息
     * @return 发送结果
     */
    @PostMapping("/psw/email")
    public WrapperResp<Void> sendChangePasswordEmailCode(@RequestHeader HttpHeaders headers) {
        userAccountSecurityService.sendChangePasswordEmailCode(headers);
        return WrapperUtil.success();
    }

    /**
     * 修改登录密码（邮箱验证）时，检查邮件验证码是否正确
     * @param headers Http头部信息
     * @param smsCodeReq 接口参数
     * @return 发送结果
     */
    @RequestMapping("/psw/email/check")
    public WrapperResp<Void> checkChangePasswordEmailCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid SmsCodeReq smsCodeReq) {
        userAccountSecurityService.checkChangePasswordEmailCode(headers, smsCodeReq);
        return WrapperUtil.success();
    }

    /**
     * 修改登录密码
     * @param userManageReq 接口参数
     * @return 操作结果
     */
    @RequestMapping("/psw/update")
    public WrapperResp<Void> changePassword(@RequestHeader HttpHeaders headers, @RequestBody @Valid UserUpdatePasswordReq userManageReq) {
        userAccountSecurityService.changePassword(headers, userManageReq);
        return WrapperUtil.success();
    }

    /**
     * 修改邮箱（手机校验码验证）时，发送手机短信验证码
     * @param headers Http头部信息
     * @return 操作结果
     */
    @PostMapping("/email/sms")
    public WrapperResp<Void> sendChangeEmailSmsCode(@RequestHeader HttpHeaders headers) {
        userAccountSecurityService.sendChangeEmailSmsCode(headers);
        return WrapperUtil.success();
    }

    /**
     * 修改邮箱（手机校验码验证）时，检查手机短信验证码是否正确
     * @param headers Http头部信息
     * @param smsCodeReq 接口参数
     * @return 验证结果
     */
    @RequestMapping("/email/sms/check")
    public WrapperResp<Void> checkChangeEmailSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid SmsCodeReq smsCodeReq) {
        userAccountSecurityService.checkChangeEmailSmsCode(headers, smsCodeReq);
        return WrapperUtil.success();
    }

    /**
     * 修改邮箱（邮箱验证）时，发送邮件验证码
     * @param headers Http头部信息
     * @return 发送结果
     */
    @PostMapping("/email/email")
    public WrapperResp<Void> sendChangeEmailEmailCode(@RequestHeader HttpHeaders headers) {
        userAccountSecurityService.sendChangeEmailEmailCode(headers);
        return WrapperUtil.success();
    }

    /**
     * 修改邮箱（邮箱验证）时，检查邮件验证码是否正确
     * @param headers Http头部信息
     * @param smsCodeReq 接口参数
     * @return 发送结果
     */
    @PostMapping("/email/email/check")
    public WrapperResp<Void> checkChangeEmailEmailCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid SmsCodeReq smsCodeReq) {
        userAccountSecurityService.checkChangeEmailEmailCode(headers, smsCodeReq);
        return WrapperUtil.success();
    }

    /**
     * 修改邮箱时，向新邮箱发送邮件验证码
     * @param headers Http头部信息
     * @param emailReq 接口参数
     * @return 发送结果
     */
    @PostMapping("/email/email/tonew")
    public WrapperResp<Void> sendChangeEmailEmailCodeToNew(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailReq emailReq) {
        userAccountSecurityService.sendChangeEmailEmailCodeToNew(headers, emailReq);
        return WrapperUtil.success();
    }

    /**
     * 修改邮箱
     * @param headers Http头部信息
     * @param userManageVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/email/update")
    public WrapperResp<Void> changeEmail(@RequestHeader HttpHeaders headers, @RequestBody @Valid UserUpdateEmailReq userManageVO) {
        userAccountSecurityService.changeEmail(headers, userManageVO);
        return WrapperUtil.success();
    }

    /**
     * 修改手机号码（手机校验码验证）时，发送手机短信验证码
     * @param headers Http头部信息
     * @return 操作结果
     */
    @PostMapping("/phone/sms")
    public WrapperResp<Void> sendChangePhoneSmsCode(@RequestHeader HttpHeaders headers) {
        userAccountSecurityService.sendChangePhoneSmsCode(headers);
        return WrapperUtil.success();
    }

    /**
     * 修改手机号码（手机校验码验证）时，检查手机短信验证码是否正确
     * @param headers Http头部信息
     * @param smsCodeReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/phone/sms/check")
    public WrapperResp<Void> checkChangePhoneSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid SmsCodeReq smsCodeReq) {
        userAccountSecurityService.checkPhoneSmsCode(headers, smsCodeReq);
        return WrapperUtil.success();
    }

    /**
     * 修改手机号码（邮箱验证）时，发送邮件验证码
     * @param headers Http头部信息
     * @return 操作结果
     */
    @PostMapping("/phone/email")
    public WrapperResp<Void> sendChangePhoneEmailCode(@RequestHeader HttpHeaders headers) {
        userAccountSecurityService.sendChangePhoneEmailCode(headers);
        return WrapperUtil.success();
    }

    /**
     * 修改手机号码（邮箱验证）时，检查邮件验证码是否正确
     * @param headers Http头部信息
     * @param smsCodeReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/phone/email/check")
    public WrapperResp<Void> checkChangePhoneEmailCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid SmsCodeReq smsCodeReq) {
        userAccountSecurityService.checkChangePhoneEmailCode(headers, smsCodeReq);
        return WrapperUtil.success();
    }

    /**
     * 修改手机号码时，向新手机号码发送短信验证码
     * @param headers Http头部信息
     * @param phoneReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/phone/sms/tonew")
    public WrapperResp<Void> sendChangePhoneSmsCodeToNew(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneReq phoneReq) {
        userAccountSecurityService.sendChangePhoneSmsCodeToNew(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 修改手机号
     * @param headers Http头部信息
     * @param userManageVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/phone/update")
    public WrapperResp<Void> changePhone(@RequestHeader HttpHeaders headers, @RequestBody @Valid UserUpdatePhoneReq userManageVO) {
        userAccountSecurityService.changePhone(headers, userManageVO);
        return WrapperUtil.success();
    }

    /**
     * 重置支付密码时，发送手机短信验证码
     * @param headers Http头部信息
     * @return 操作结果
     */
    @PostMapping("/pay/sms")
    public WrapperResp<Void> sendChangePayPswSmsCode(@RequestHeader HttpHeaders headers) {
        userAccountSecurityService.sendChangePayPswSmsCode(headers);
        return WrapperUtil.success();
    }

    /**
     * 重置支付密码时，检查手机短信验证码是否正确
     * @param headers Http头部信息
     * @param smsCodeReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/pay/sms/check")
    public WrapperResp<Void> checkChangePayPswSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid SmsCodeReq smsCodeReq) {
        userAccountSecurityService.checkChangePayPswSmsCode(headers, smsCodeReq);
        return WrapperUtil.success();
    }

    /**
     * 修改支付密码
     * @param headers Http头部信息
     * @param userManageVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/pay/update")
    public WrapperResp<Void> changePayPassword(@RequestHeader HttpHeaders headers, @RequestBody @Valid UserUpdatePayPasswordReq userManageVO) {
        userAccountSecurityService.changePayPassword(headers, userManageVO);
        return WrapperUtil.success();
    }

    /**
     * 会员注销 - 校验
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/cancellation/check")
    public WrapperResp<MemberCancellationFailResp> memberAccountDeleteCheck(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(userAccountSecurityService.memberCancellationCheck(headers));
    }

    /**
     * 会员注销 - 获取验证码
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/cancellation/sms")
    public WrapperResp<Void> sendDeleteMemberAccountSmsCode(@RequestHeader HttpHeaders headers) {
        userAccountSecurityService.sendCancellationMemberSmsCode(headers);
        return WrapperUtil.success();
    }

    /**
     * 会员注销 - 确认注销接口
     * @param headers  Http头部信息
     * @param deleteReq 接口参数
     * @return 操作结果
     */
    @PostMapping("/cancellation")
    public WrapperResp<MemberCancellationFailResp> deleteMemberAccount(@RequestHeader HttpHeaders headers, @RequestBody @Valid CancellationMemberReq deleteReq) {
        return WrapperUtil.success(userAccountSecurityService.cancellationMember(headers, deleteReq));
    }
}
