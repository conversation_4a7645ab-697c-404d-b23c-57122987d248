package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.MemberAfterSaleHistoryDO;
import com.ssy.lingxi.member.model.dto.CommentSummaryDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员售后评论历史记录操作Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
@Repository
public interface MemberAfterSaleHistoryRepository extends JpaRepository<MemberAfterSaleHistoryDO, Long>, JpaSpecificationExecutor<MemberAfterSaleHistoryDO> {

    List<MemberAfterSaleHistoryDO> findAllBySubMemberIdAndSubRoleId(Long memberId, Long roleId);

    List<MemberAfterSaleHistoryDO> findAllByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId);

    @Transactional(rollbackFor = BusinessException.class)
    void deleteAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    @Transactional(rollbackFor = BusinessException.class)
    void deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);

    @Query(value = "select new com.ssy.lingxi.member.model.dto.CommentSummaryDTO(count(mth.id), sum(case when (mth.star = 5 or mth.star = 4) then 1 else 0 end), sum(case when mth.star=3 then 1 else 0 end), sum(case when (mth.star = 2 or mth.star=1) then 1 else 0 end)) from MemberAfterSaleHistoryDO mth where mth.memberId = :memberId and mth.roleId = :roleId and mth.subMemberId = :subMemberId and mth.subRoleId = :subRoleId group by mth.memberId, mth.roleId, mth.subMemberId, mth.subRoleId")
    CommentSummaryDTO groupByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);
}
