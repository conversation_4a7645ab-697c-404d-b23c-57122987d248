package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberQualityDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员资质Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-20
 */
@Repository
public interface MemberQualityRepository extends JpaRepository<MemberQualityDO, Long>, JpaSpecificationExecutor<MemberQualityDO> {

    List<MemberQualityDO> findByRelation(MemberRelationDO relationDO);

    List<MemberQualityDO> findByRelationAndVersion(MemberRelationDO relationDO, Integer version);

    @Transactional
    void deleteByRelationAndVersion(MemberRelationDO relationDO, Integer version);
}
