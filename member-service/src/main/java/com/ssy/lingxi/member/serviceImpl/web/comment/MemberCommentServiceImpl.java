package com.ssy.lingxi.member.serviceImpl.web.comment;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.enums.TradeCommentHistoryStatusEnum;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderCommentDO;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderProductCommentDO;
import com.ssy.lingxi.member.entity.do_.comment.MemberTradeCommentHistoryDO;
import com.ssy.lingxi.member.enums.MemberTradeTypeEnum;
import com.ssy.lingxi.member.model.req.comment.*;
import com.ssy.lingxi.member.model.resp.comment.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditCommentSummaryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditTradeHistoryResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.comment.MemberOrderCommentRepository;
import com.ssy.lingxi.member.repository.comment.MemberOrderProductCommentRepository;
import com.ssy.lingxi.member.repository.comment.MemberTradeCommentHistoryRepository;
import com.ssy.lingxi.member.service.feign.IManageFeignService;
import com.ssy.lingxi.member.service.web.comment.IMemberCommentService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员评论服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
@Service
public class MemberCommentServiceImpl extends BaseMemberCommentServiceImpl implements IMemberCommentService {
    @Resource
    private MemberTradeCommentHistoryRepository memberTradeCommentHistoryRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IManageFeignService platformManageFeignService;

    @Resource
    private MemberOrderCommentRepository memberOrderCommentRepository;

    @Resource
    private MemberOrderProductCommentRepository memberOrderProductCommentRepository;

    /**
     * 交易能力 - 会员评价管理 - 订单商品评价详情
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param commonIdReq 接口参数
     * @return 查询结果
     */
    @Override
    public MemberOrderCommentDetailResp getMemberOrderCommentDetail(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, CommonIdReq commonIdReq) {
        // 订单
        MemberOrderCommentDO memberOrderCommentDO = memberOrderCommentRepository.findById(commonIdReq.getId()).orElse(null);
        if (Objects.isNull(memberOrderCommentDO)) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 会员数据关联
        if (MemberTradeTypeEnum.BUYER.equals(memberTradeTypeEnum)) {
            if (!memberOrderCommentDO.getBuyerMemberId().equals(loginUser.getMemberId()) || !memberOrderCommentDO.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
                //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);

                throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
            }
        } else {
            if (!memberOrderCommentDO.getVendorMemberId().equals(loginUser.getMemberId()) || !memberOrderCommentDO.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
                //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);

                throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
            }
        }

        // 订单商品
        List<MemberOrderProductCommentDO> memberOrderProductCommentDOList = memberOrderProductCommentRepository.findAllByOrderId(commonIdReq.getId());
        if (CollectionUtils.isEmpty(memberOrderProductCommentDOList)) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 订单商品评价记录
        List<MemberTradeCommentHistoryDO> memberTradeCommentHistoryDOList = memberTradeCommentHistoryRepository.findAllByOrderIdAndTradeType(commonIdReq.getId(), memberTradeTypeEnum.getTypeEnum());
        Map<Long, MemberTradeCommentHistoryDO> productCommentMap = memberTradeCommentHistoryDOList.stream().collect(Collectors.toMap(MemberTradeCommentHistoryDO::getProductId, e -> e, (e1, e2) -> e2));

        // 汇总订单商品和评价数据
        List<MemberOrderProductCommentResp> orderProductCommentList = memberOrderProductCommentDOList.stream().map(e -> {
            // 从构造的映射中取, 可能为空
            MemberTradeCommentHistoryDO commentHistoryDO = productCommentMap.get(e.getProductId());
            MemberOrderProductCommentResp memberOrderProductCommentResp = new MemberOrderProductCommentResp();
            memberOrderProductCommentResp.setOrderProductId(e.getId());
            memberOrderProductCommentResp.setProductId(e.getProductId());
            memberOrderProductCommentResp.setSkuId(e.getSkuId());
            memberOrderProductCommentResp.setProductNo(e.getProductNo());
            memberOrderProductCommentResp.setName(e.getName());
            memberOrderProductCommentResp.setCategory(e.getCategory());
            memberOrderProductCommentResp.setBrand(e.getBrand());
            memberOrderProductCommentResp.setSpec(e.getSpec());
            memberOrderProductCommentResp.setUnit(e.getUnit());
            memberOrderProductCommentResp.setLogo(e.getLogo());
            memberOrderProductCommentResp.setPrice(e.getPrice());
            memberOrderProductCommentResp.setQuantity(e.getQuantity());
            memberOrderProductCommentResp.setAmount(e.getAmount());
            memberOrderProductCommentResp.setStar(Optional.ofNullable(commentHistoryDO).map(MemberTradeCommentHistoryDO::getStar).orElse(null));
            memberOrderProductCommentResp.setComment(Optional.ofNullable(commentHistoryDO).map(MemberTradeCommentHistoryDO::getComment).orElse(""));
            memberOrderProductCommentResp.setPics(Optional.ofNullable(commentHistoryDO).map(MemberTradeCommentHistoryDO::getPics).orElse(Collections.emptyList()));
            memberOrderProductCommentResp.setCommentStatus(MemberTradeTypeEnum.BUYER.getTypeEnum().equals(memberTradeTypeEnum.getTypeEnum()) ? e.getBuyerCommentStatus() : e.getVendorCommentStatus());
            return memberOrderProductCommentResp;
        }).collect(Collectors.toList());

        // 返回实体
        MemberOrderCommentDetailResp memberOrderCommentDetailResp = new MemberOrderCommentDetailResp();
        memberOrderCommentDetailResp.setOrderId(memberOrderCommentDO.getId());
        memberOrderCommentDetailResp.setOrderNo(memberOrderCommentDO.getOrderNo());
        memberOrderCommentDetailResp.setOrderType(memberOrderCommentDO.getOrderType());
        memberOrderCommentDetailResp.setOrderTypeName(OrderTypeEnum.getNameByCode(memberOrderCommentDO.getOrderType()));
        memberOrderCommentDetailResp.setCreateTime(DateTimeUtil.formatDateTime(memberOrderCommentDO.getCreateTime()));
        if (!MemberTradeTypeEnum.BUYER.getTypeEnum().equals(memberTradeTypeEnum.getTypeEnum())) {
            memberOrderCommentDetailResp.setMemberId(memberOrderCommentDO.getBuyerMemberId());
            memberOrderCommentDetailResp.setRoleIdId(memberOrderCommentDO.getBuyerRoleId());
            memberOrderCommentDetailResp.setMemberName(memberOrderCommentDO.getBuyerMemberName());
        } else {
            memberOrderCommentDetailResp.setMemberId(memberOrderCommentDO.getVendorMemberId());
            memberOrderCommentDetailResp.setRoleIdId(memberOrderCommentDO.getVendorRoleId());
            memberOrderCommentDetailResp.setMemberName(memberOrderCommentDO.getVendorMemberName());
        }
        memberOrderCommentDetailResp.setShopId(memberOrderCommentDO.getShopId());
        memberOrderCommentDetailResp.setShopName(memberOrderCommentDO.getShopName());
        memberOrderCommentDetailResp.setShopName(memberOrderCommentDO.getShopName());
        memberOrderCommentDetailResp.setOrderProductCommentList(orderProductCommentList);

        return memberOrderCommentDetailResp;
    }

    /**
     * 交易能力-会员评价管理-发表评价
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param commentSubmitVO 接口参数
     * @return 查询结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitMemberTradeComment(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, MemberTradeCommentSubmitReq commentSubmitVO) {
        // 订单
        MemberOrderCommentDO memberOrderCommentDO = memberOrderCommentRepository.findById(commentSubmitVO.getOrderId()).orElse(null);
        if (Objects.isNull(memberOrderCommentDO)) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 会员数据关联
        if (MemberTradeTypeEnum.BUYER.equals(memberTradeTypeEnum)) {
            if (!memberOrderCommentDO.getBuyerMemberId().equals(loginUser.getMemberId()) || !memberOrderCommentDO.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
                //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
                throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
            }
        } else {
            if (!memberOrderCommentDO.getVendorMemberId().equals(loginUser.getMemberId()) || !memberOrderCommentDO.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
                //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
                throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
            }
        }

        this.baseSubmitMemberTradeComment(memberTradeTypeEnum, memberOrderCommentDO, commentSubmitVO.getCommentSubmitDetailList(), true);
    }

    /**
     * 系统定时自动评价
     *
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param commentSubmitVO 接口参数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitAutoMemberTradeComment(MemberTradeTypeEnum memberTradeTypeEnum, MemberOrderCommentDO memberOrderCommentDO, MemberTradeCommentSubmitReq commentSubmitVO) {
        this.baseSubmitMemberTradeComment(memberTradeTypeEnum, memberOrderCommentDO, commentSubmitVO.getCommentSubmitDetailList(), false);
    }

    /**
     * 交易能力 - 会员评价管理 - 收到评价分页列表
     * 如果采购会员查看, 评价方角色是供应会员
     * 如果供应会员查看, 评价方角色是采购会员
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberReceiveTradeCommentPageResp> pageMemberReceiveTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, MemberReceiveTradeCommentDataReq pageVO) {
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by(MemberTradeCommentHistoryDO.Fields.dealTime).descending());

        Page<MemberTradeCommentHistoryDO> pageList = memberTradeCommentHistoryRepository.findAll((Specification<MemberTradeCommentHistoryDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.subMemberId), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.subRoleId), loginUser.getMemberRoleId()));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.tradeType), memberTradeTypeEnum.getTypeEnum()));
            if (Objects.nonNull(pageVO.getStar())) {
                predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.star), pageVO.getStar()));
            }
            if (Objects.nonNull(pageVO.getDealTimeStart())) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get(MemberTradeCommentHistoryDO.Fields.dealTime), pageVO.getDealTimeStart()));
            }
            if (Objects.nonNull(pageVO.getDealTimeEnd())) {
                predicateList.add(cb.lessThanOrEqualTo(root.get(MemberTradeCommentHistoryDO.Fields.dealTime), pageVO.getDealTimeEnd()));
            }
            if (StringUtils.isNotBlank(pageVO.getMemberName())) {
                predicateList.add(cb.like(root.get(MemberTradeCommentHistoryDO.Fields.memberName), "%" + pageVO.getMemberName() + "%"));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<MemberReceiveTradeCommentPageResp> resultList = pageList.stream().map(e -> {
            MemberReceiveTradeCommentPageResp commentPageVO = new MemberReceiveTradeCommentPageResp();
            commentPageVO.setId(e.getId());
            commentPageVO.setStar(e.getStar());
            commentPageVO.setComment(Optional.ofNullable(e.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(e.getComment()));
            commentPageVO.setProduct(e.getProduct());
            commentPageVO.setDealTime(e.getDealTime());
            commentPageVO.setMemberName(e.getMemberName());
            commentPageVO.setOrderNo(e.getOrderNo());
            commentPageVO.setPrice(e.getPrice());
            commentPageVO.setPurchaseCount(e.getDealCount());
            commentPageVO.setOrderId(e.getOrderId());
            commentPageVO.setStatus(e.getStatus());
            // 供应收到采购的评价可以回复, 采购收到的供应评价没有回复, 30天内可回复
            Integer replyStatus = MemberTradeTypeEnum.BUYER.getTypeEnum().equals(e.getTradeType())
                    && LocalDateTime.now().isBefore(e.getCreateTime().plusDays(30L))
                    && EnableDisableStatusEnum.DISABLE.getCode().equals(e.getReplyStatus())
                    ? EnableDisableStatusEnum.DISABLE.getCode() : EnableDisableStatusEnum.ENABLE.getCode();
            commentPageVO.setReplyStatus(replyStatus);
            return commentPageVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 交易能力 - 会员评价管理 - 发出评价分页列表
     * 如果采购会员查看, 评价方角色是采购会员
     * 如果供应会员查看, 评价方角色是供应会员
     *
     * @param loginUser 登录用户信息
     * @param memberTradeTypeEnum 评价方角色枚举 1-供应会员，2-采购会员
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSendTradeCommentPageResp> pageMemberSendTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, MemberSendTradeCommentDataReq pageVO) {
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by(MemberTradeCommentHistoryDO.Fields.dealTime).descending());

        Page<MemberTradeCommentHistoryDO> pageList = memberTradeCommentHistoryRepository.findAll((Specification<MemberTradeCommentHistoryDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.memberId), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.roleId), loginUser.getMemberRoleId()));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.tradeType), memberTradeTypeEnum.getTypeEnum()));
            if (Objects.nonNull(pageVO.getStar())) {
                predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.star), pageVO.getStar()));
            }
            if (Objects.nonNull(pageVO.getDealTimeStart())) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get(MemberTradeCommentHistoryDO.Fields.dealTime), pageVO.getDealTimeStart()));
            }
            if (Objects.nonNull(pageVO.getDealTimeEnd())) {
                predicateList.add(cb.lessThanOrEqualTo(root.get(MemberTradeCommentHistoryDO.Fields.dealTime), pageVO.getDealTimeEnd()));
            }
            if (StringUtils.isNotBlank(pageVO.getSubMemberName())) {
                predicateList.add(cb.like(root.get(MemberTradeCommentHistoryDO.Fields.subMemberName), "%" + pageVO.getSubMemberName() + "%"));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<MemberSendTradeCommentPageResp> resultList = pageList.stream().map(e -> {
            MemberSendTradeCommentPageResp commentPageVO = new MemberSendTradeCommentPageResp();
            commentPageVO.setId(e.getId());
            commentPageVO.setStar(e.getStar());
            commentPageVO.setComment(Optional.ofNullable(e.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(e.getComment()));
            commentPageVO.setProduct(e.getProduct());
            commentPageVO.setDealTime(e.getDealTime());
            commentPageVO.setSubMemberName(e.getSubMemberName());
            commentPageVO.setPrice(e.getPrice());
            commentPageVO.setPurchaseCount(e.getDealCount());
            commentPageVO.setOrderId(e.getOrderId());
            return commentPageVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 交易能力 - 采购会员评价管理 - 修改评价详情
     * @param tradeCommentSubmitVO 接口参数
     */
    @Override
    public void updateMemberTradeCommentHistory(MemberTradeCommentUpdateDetailReq tradeCommentSubmitVO) {
        MemberTradeCommentHistoryDO commentHistoryDO = memberTradeCommentHistoryRepository.findById(tradeCommentSubmitVO.getId()).orElse(null);
        if (Objects.isNull(commentHistoryDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_COMMENT_NOT_FOUND);
        }

        // 敏感词过滤
        String filterResult = platformManageFeignService.filterSensitiveWord(tradeCommentSubmitVO.getComment());
        commentHistoryDO.setStar(tradeCommentSubmitVO.getStar());
        commentHistoryDO.setComment(filterResult);
        commentHistoryDO.setPics(tradeCommentSubmitVO.getPics());

        memberTradeCommentHistoryRepository.saveAndFlush(commentHistoryDO);

    }

    /**
     * 交易能力 - 评价管理 - 评价详情
     *
     * @param loginUser 登录用户信息
     * @param tradeCommentIdVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberTradeCommentDetailResp getMemberTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeCommentIdReq tradeCommentIdVO) {
        MemberTradeCommentHistoryDO commentHistoryDO = memberTradeCommentHistoryRepository.findById(tradeCommentIdVO.getId()).orElse(null);
        if (Objects.isNull(commentHistoryDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_COMMENT_NOT_FOUND);
        }

        // 返回
        MemberTradeCommentDetailResp commentPageVO = new MemberTradeCommentDetailResp();
        commentPageVO.setId(commentHistoryDO.getId());
        commentPageVO.setMemberName(commentHistoryDO.getMemberName());
        commentPageVO.setStar(commentHistoryDO.getStar());
        commentPageVO.setComment(Optional.ofNullable(commentHistoryDO.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(commentHistoryDO.getComment()));
        commentPageVO.setProduct(commentHistoryDO.getProduct());
        commentPageVO.setOrderNo(commentHistoryDO.getOrderNo());
        commentPageVO.setOrderType(commentHistoryDO.getOrderType());
        commentPageVO.setDealTime(commentHistoryDO.getDealTime());
        commentPageVO.setPrice(commentHistoryDO.getPrice());
        commentPageVO.setTotalPrice(commentHistoryDO.getTotalPrice());
        commentPageVO.setPics(commentHistoryDO.getPics());
        commentPageVO.setProductImgUrl(commentHistoryDO.getProductImgUrl());
        commentPageVO.setPurchaseCount(commentHistoryDO.getDealCount());
        commentPageVO.setUnit(commentHistoryDO.getUnit());
        commentPageVO.setReplyContent(commentHistoryDO.getReplyContent());
        commentPageVO.setReplyTime(commentHistoryDO.getReplyTime());
        return commentPageVO;
    }

    /**
     * 商城能力 - 店铺渠道商城 - 现货商品详情 - 商户总体满意度
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp pageOrderProductTradeCommentSummary(OrderProductTradeCommentReq pageVO) {
        MemberDetailCreditCommentSummaryResp summaryVO = new MemberDetailCreditCommentSummaryResp();

        List<MemberTradeCommentHistoryDO> historyDOList = memberTradeCommentHistoryRepository.findAll((Specification<MemberTradeCommentHistoryDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            List<Integer> orderTypeList = Arrays.asList(
                    OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode(),
                    OrderTypeEnum.NEED_TO_PURCHASE.getCode(),
                    OrderTypeEnum.SPOT_PURCHASING.getCode(),
                    OrderTypeEnum.COLLECTIVE_PURCHASE.getCode(),
                    OrderTypeEnum.CREDITS_EXCHANGE.getCode()
            );
            predicateList.add(cb.and(root.get(MemberTradeCommentHistoryDO.Fields.orderType).in(orderTypeList)));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.status), TradeCommentHistoryStatusEnum.SHOW.getCode()));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.productId), pageVO.getProductId()));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.tradeType), MemberTradeTypeEnum.BUYER.getTypeEnum()));
            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        });

        //四舍五入计算平均星级
        double avgStarD = historyDOList.stream().mapToInt(MemberTradeCommentHistoryDO::getStar).average().orElse(0);
        Integer avgStar = BigDecimal.valueOf(Math.round(avgStarD)).intValue();
        summaryVO.setAvgStar(avgStar);

        //循环计算
        LocalDateTime last7days = LocalDateTime.now().minusDays(7);
        LocalDateTime last30days = LocalDateTime.now().minusDays(30);
        LocalDateTime last180days = LocalDateTime.now().minusDays(180);

        List<MemberDetailCreditCommentSummaryResp.TableRow> rows = Stream.of(5, 4, 3, 2, 1).map(star -> {
            MemberDetailCreditCommentSummaryResp.TableRow tableRow = new MemberDetailCreditCommentSummaryResp.TableRow();
            List<MemberTradeCommentHistoryDO> subList = historyDOList.stream().filter(h -> h.getStar().equals(star)).collect(Collectors.toList());

            long countIn7Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last7days)).count();
            long countIn30Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last30days)).count();
            long countIn180Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last180days)).count();
            long countBefore180Days = subList.stream().filter(h -> h.getCreateTime().isBefore(last180days)).count();
            long sum = countIn180Days + countBefore180Days;

            tableRow.setStar(star);
            tableRow.setLast7days(countIn7Days);
            tableRow.setLast30days(countIn30Days);
            tableRow.setLast180days(countIn180Days);
            tableRow.setBefore180days(countBefore180Days);
            tableRow.setSum(sum);
            return tableRow;
        }).collect(Collectors.toList());

        summaryVO.setRows(rows);

        return summaryVO;
    }

    /**
     * 商城能力 - 店铺渠道商城 - 现货商品详情 - 交易评价分页列表(只显示采购商的评价)
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<ProductOrderTradeCommentPageResp> pageOrderProductTradeCommentHistory(OrderProductTradeCommentQueryDataReq pageVO) {
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").ascending());

        final Integer allStarsLevel = 0;
        final Integer oneOrTwoStarsLevel = 1;
        final Integer threeStarsLevel = 2;

        Page<MemberTradeCommentHistoryDO> pageList = memberTradeCommentHistoryRepository.findAll((Specification<MemberTradeCommentHistoryDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            List<Integer> orderTypeList = Arrays.asList(
                    OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode(),
                    OrderTypeEnum.NEED_TO_PURCHASE.getCode(),
                    OrderTypeEnum.SPOT_PURCHASING.getCode(),
                    OrderTypeEnum.COLLECTIVE_PURCHASE.getCode(),
                    OrderTypeEnum.CREDITS_EXCHANGE.getCode()
            );
            predicateList.add(cb.and(root.get(MemberTradeCommentHistoryDO.Fields.orderType).in(orderTypeList)));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.status), TradeCommentHistoryStatusEnum.SHOW.getCode()));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.productId), pageVO.getProductId()));
            predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.tradeType), MemberTradeTypeEnum.BUYER.getTypeEnum()));
            // startLevel不为空且不等于0
            if (Objects.nonNull(pageVO.getStarLevel()) && !allStarsLevel.equals(pageVO.getStarLevel())) {
                if (oneOrTwoStarsLevel.equals(pageVO.getStarLevel())) {
                    predicateList.add(cb.or(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 1), cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 2)));
                } else if (threeStarsLevel.equals(pageVO.getStarLevel())) {
                    predicateList.add(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 3));
                } else {
                    predicateList.add(cb.or(cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 4), cb.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 5)));
                }

            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<ProductOrderTradeCommentPageResp> resultList = pageList.stream().map(e -> {
            ProductOrderTradeCommentPageResp commentPageVO = new ProductOrderTradeCommentPageResp();
            commentPageVO.setId(e.getId());
            commentPageVO.setComment(Optional.ofNullable(e.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(e.getComment()));
            commentPageVO.setStar(e.getStar());
            // 会员等级
            MemberRelationDO memberRelationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(e.getMemberId(), e.getRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
            commentPageVO.setLevelTag(memberRelationDO.getLevelRight().getLevelTag());
            commentPageVO.setMemberName(e.getMemberName());
            commentPageVO.setPics(e.getPics());
            commentPageVO.setCreateTime(e.getCreateTime());
            commentPageVO.setReplyContent(e.getReplyContent());
            commentPageVO.setLogo(memberRelationDO.getSubMember().getLogo());
            return commentPageVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 交易能力 - 会员评价管理 - 交易评价汇总
     * 如果采购会员查看, 评价方角色是供应会员
     * 如果供应会员查看, 评价方角色是采购会员
     *
     * @param loginUser 登录用户信息
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getSubMemberTradeCommentSummary(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum) {
        MemberDetailCreditCommentSummaryResp summaryVO = new MemberDetailCreditCommentSummaryResp();

        Specification<MemberTradeCommentHistoryDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            // 被评价会员id和角色id
            list.add(criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.subMemberId).as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.subRoleId).as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.tradeType), memberTradeTypeEnum.getTypeEnum()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberTradeCommentHistoryDO> historyDOList = memberTradeCommentHistoryRepository.findAll(specification);

        //四舍五入计算平均星级
        double avgStarD = historyDOList.stream().mapToInt(MemberTradeCommentHistoryDO::getStar).average().orElse(0);
        Integer avgStar = BigDecimal.valueOf(Math.round(avgStarD)).intValue();
        summaryVO.setAvgStar(avgStar);

        //循环计算
        LocalDateTime last7days = LocalDateTime.now().minusDays(7);
        LocalDateTime last30days = LocalDateTime.now().minusDays(30);
        LocalDateTime last180days = LocalDateTime.now().minusDays(180);

        List<MemberDetailCreditCommentSummaryResp.TableRow> rows = Stream.of(5, 4, 3, 2, 1).map(star -> {
            MemberDetailCreditCommentSummaryResp.TableRow tableRow = new MemberDetailCreditCommentSummaryResp.TableRow();
            List<MemberTradeCommentHistoryDO> subList = historyDOList.stream().filter(h -> h.getStar().equals(star)).collect(Collectors.toList());

            long countIn7Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last7days)).count();
            long countIn30Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last30days)).count();
            long countIn180Days = subList.stream().filter(h -> h.getCreateTime().isAfter(last180days)).count();
            long countBefore180Days = subList.stream().filter(h -> h.getCreateTime().isBefore(last180days)).count();
            long sum = countIn180Days + countBefore180Days;

            tableRow.setStar(star);
            tableRow.setLast7days(countIn7Days);
            tableRow.setLast30days(countIn30Days);
            tableRow.setLast180days(countIn180Days);
            tableRow.setBefore180days(countBefore180Days);
            tableRow.setSum(sum);
            return tableRow;
        }).collect(Collectors.toList());

        summaryVO.setRows(rows);

        return summaryVO;
    }

    /**
     * 交易能力 - 会员评价管理 - 分页查询交易评论历史记录
     * 如果采购会员查看, 评价方角色是供应会员
     * 如果供应会员查看, 评价方角色是采购会员
     *
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditTradeHistoryResp> pageSubMemberTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeTypeEnum memberTradeTypeEnum, MemberHistoryPageDataReq pageVO) {
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by(MemberTradeCommentHistoryDO.Fields.dealTime).descending());

        //评价星级，1星或2星为差评，3星为中评，4星或5星为好评
        final Integer oneOrTwoStarsLevel = 1;
        final Integer threeStarsLevel = 2;

        Specification<MemberTradeCommentHistoryDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            // 被评价会员id和角色id
            list.add(criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.subMemberId).as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.subRoleId).as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.tradeType), memberTradeTypeEnum.getTypeEnum()));

            if (pageVO.getStarLevel() != null) {
                if (pageVO.getStarLevel().equals(oneOrTwoStarsLevel)) {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 1), criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 2)));
                } else if (pageVO.getStarLevel().equals(threeStarsLevel)) {
                    list.add(criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 3));
                } else {
                    list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 4), criteriaBuilder.equal(root.get(MemberTradeCommentHistoryDO.Fields.star).as(Integer.class), 5)));
                }
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberTradeCommentHistoryDO> result = memberTradeCommentHistoryRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(h -> {
            MemberDetailCreditTradeHistoryResp historyVO = new MemberDetailCreditTradeHistoryResp();
            historyVO.setId(h.getId());
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setDealTime(h.getDealTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setStar(h.getStar());
            historyVO.setByMemberName(h.getMemberName());
            historyVO.setComment(Optional.ofNullable(h.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(h.getComment()));
            historyVO.setProduct(h.getProduct());
            historyVO.setOrderNo(h.getOrderNo());
            historyVO.setPrice(h.getPrice());
            historyVO.setPurchaseCount(h.getDealCount());
            historyVO.setOrderId(h.getOrderId());
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 交易能力-供应会员评价管理-收到评价-解释回复
     * @param loginUser 登录用户信息
     * @param memberTradeCommentSubmitVO 接口参数
     */
    @Override
    public void replyReceiveMemberTradeCommentHistory(UserLoginCacheDTO loginUser, MemberTradeReceiveCommentReplyReq memberTradeCommentSubmitVO) {
        MemberTradeCommentHistoryDO commentHistoryDO = memberTradeCommentHistoryRepository.findById(memberTradeCommentSubmitVO.getId()).orElse(null);
        if (Objects.isNull(commentHistoryDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_COMMENT_NOT_FOUND);
        }
        // 采购会员评价的才能回复
        if (!MemberTradeTypeEnum.BUYER.getTypeEnum().equals(commentHistoryDO.getTradeType())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 收到评价超过30天不能回复
        if (LocalDateTime.now().isAfter(commentHistoryDO.getCreateTime().plusDays(30L))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_COMMENT_REPLY);
        }

        commentHistoryDO.setReplyStatus(EnableDisableStatusEnum.ENABLE.getCode());
        commentHistoryDO.setReplyContent(memberTradeCommentSubmitVO.getContent());
        commentHistoryDO.setReplyTime(LocalDateTime.now());
        memberTradeCommentHistoryRepository.saveAndFlush(commentHistoryDO);

    }

    /**
     * 交易能力-供应会员评价管理-收到评价-修改是否显示商品评价
     * @param updateVO 接口参数
     */
    @Override
    public void updateSupplyReceiveShowEvaluation(MemberTradeCommentUpdateStatusReq updateVO) {
        MemberTradeCommentHistoryDO memberTradeCommentHistoryDO = memberTradeCommentHistoryRepository.findById(updateVO.getId()).orElse(null);
        if (memberTradeCommentHistoryDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_COMMENT_NOT_FOUND);
        }
        memberTradeCommentHistoryDO.setStatus(updateVO.getStatus());
        memberTradeCommentHistoryRepository.save(memberTradeCommentHistoryDO);
    }
}
