package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.member.handler.annotation.DateStringFormatAnnotation;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 平台后台 - 会员审核步骤 查询会员信息列表 接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformMemberValidateQueryDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -7652901816690296707L;

    /**
     * 会员名称
     */
    private String name;

    /**
     * 申请开始时间， 格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation
    private String startDate;

    /**
     * 申请结束时间，格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation
    private String endDate;

    /**
     * 内部状态
     */
    private Integer innerStatus;


    /**
     * 外部状态
     */
    private Integer outerStatus;

    /**
     * 会员类型， 0或Null-所有 1-企业会员 2-企业个人会员 3-渠道企业会员 4-渠道个人会员
     */
    private Long memberType;

    /**
     * 角色， 0或Null-所有， 其他枚举从接口下拉菜单字段中获取
     */
    private Long roleId;

    /**
     * 等级， 0或Null-所有，其他枚举从接口下拉菜单字段中获取
     */
    private Integer level;

    /**
     * 注册来源， 0或Null-所有， 其他枚举从下拉菜单接口中获得
     */
    private Integer source;
}
