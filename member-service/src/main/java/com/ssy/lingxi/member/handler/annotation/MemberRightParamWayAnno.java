package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.MemberRightParamWayValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 会员权益参数设置方式校验注解
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MemberRightParamWayValidator.class)
public @interface MemberRightParamWayAnno {
    boolean required() default true;

    String message() default "会员权益参数设置方式参数值不在枚举定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
