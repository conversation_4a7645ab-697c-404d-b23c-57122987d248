package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberRoleAuthConfigDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员自定义角色关联的数据权限、IM权限操作类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-04-02
 */
@Repository
public interface MemberRoleAuthConfigRepository extends JpaRepository<MemberRoleAuthConfigDO, Long>, JpaSpecificationExecutor<MemberRoleAuthConfigDO> {
    List<MemberRoleAuthConfigDO> findByMemberIdAndHasDataAuth(Long memberId, Integer hasDataAuth);
}
