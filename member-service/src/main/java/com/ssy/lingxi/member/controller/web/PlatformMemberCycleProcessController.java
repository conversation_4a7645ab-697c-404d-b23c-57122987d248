package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.platform.*;
import com.ssy.lingxi.member.model.resp.platform.BaseMemberCycleProcessResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformMemberCycleProcessDetailResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformMemberCycleProcessMemberResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformMemberCycleProcessPageResp;
import com.ssy.lingxi.member.service.web.IPlatformMemberCycleProcessService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台-会员生命周期变更流程规则
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/platform/lifeCycle/process")
public class PlatformMemberCycleProcessController extends BaseController {

    @Resource
    private IPlatformMemberCycleProcessService service;

    /**
     * 分页查询流程规则配置
     * @param request HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<PlatformMemberCycleProcessPageResp>> pageProcess(HttpServletRequest request, @Valid PlatformMemberCycleProcessPageQueryDataReq pageVO) {
        return WrapperUtil.success(service.pageProcess(getSysUser(request) , pageVO));
    }

    /**
     * 新增流程页面 - 查询基础流程列表
     * @param request Http头部信息
     * @return 查询结果
     */
    @GetMapping("/base/list")
    public WrapperResp<List<BaseMemberCycleProcessResp>> listBaseProcess(HttpServletRequest request, @Valid ProcessQueryReq queryRequest) {
        return WrapperUtil.success(service.listBaseProcess(getSysUser(request), queryRequest));
    }

    /**
     * 设置默认流程
     * @param defaultRequest 接口参数
     * @return Void
     */
    @PostMapping("/saveDefault")
    public WrapperResp<Void> saveDefault(@RequestBody @Valid SaveDefaultReq defaultRequest) {
         service.saveDefault(defaultRequest);
        return WrapperUtil.success();
    }

    /**
     * 新增流程规则
     * @param request HttpHeaders信息
     * @param saveVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/save")
    public WrapperResp<Void> save(HttpServletRequest request, @RequestBody @Valid PlatformMemberCycleProcessReq saveVO) {
         service.save(getSysUser(request), saveVO);
        return WrapperUtil.success();
    }

    /**
     * 查询流程规则详情
     * @param request HttpHeaders信息
     * @param processId 流程Id
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<PlatformMemberCycleProcessDetailResp> getInfo(HttpServletRequest request, @RequestParam("processId") Long processId) {
        return WrapperUtil.success(service.getInfo(getSysUser(request), processId));
    }

    /**
     * 查询流程规则适用会员列表
     * @param request HttpHeaders信息
     * @param queryVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/member/page")
    public WrapperResp<List<PlatformMemberCycleProcessMemberResp>> listProcessMembers(HttpServletRequest request, @Valid PlatformMemberCycleProcessMemberQueryReq queryVO) {
        return WrapperUtil.success(service.listProcessMembers(getSysUser(request), queryVO));
    }

    /**
     * 修改流程规则
     * @param request HttpHeaders信息
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(HttpServletRequest request, @RequestBody @Valid PlatformMemberCycleProcessUpdateReq updateVO) {
         service.update(getSysUser(request), updateVO);
        return WrapperUtil.success();
    }

    /**
     * 修改流程规则状态
     * @param request HttpHeaders信息
     * @param updateStatusVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/status/update")
    public WrapperResp<Void> updateStatus(HttpServletRequest request, @RequestBody @Valid PlatformMemberCycleProcessUpdateStatusReq updateStatusVO) {
         service.updateStatus(getSysUser(request), updateStatusVO);
        return WrapperUtil.success();
    }

    /**
     * 删除流程规则
     * @param request HttpHeaders信息
     * @param processId 流程规则Id
     * @return 删除结果
     */
    @GetMapping("/delete")
    public WrapperResp<Void> delete(HttpServletRequest request, @RequestParam("processId") Long processId) {
         service.delete(getSysUser(request), processId);
        return WrapperUtil.success();
    }

}
