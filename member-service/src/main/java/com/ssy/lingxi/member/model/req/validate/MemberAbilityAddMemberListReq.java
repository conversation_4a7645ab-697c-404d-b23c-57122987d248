package com.ssy.lingxi.member.model.req.validate;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 会员能力 - 会员引入接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-18
 */
@Data
public class MemberAbilityAddMemberListReq implements Serializable {
    private static final long serialVersionUID = -7117536522555225885L;

    /**
     * 会员类型枚举值
     */
    private List<MemberAbilityAddMemberReq> list;

}
