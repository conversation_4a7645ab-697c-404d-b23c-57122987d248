package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 会员下属用户操作类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
@Repository
public interface UserRepository extends JpaRepository<UserDO, Long>, JpaSpecificationExecutor<UserDO> {
    Optional<UserDO> findByIdAndStatus(Long id, Integer status);

    Optional<UserDO> findByIdAndRelTypeAndStatus(Long id, Integer relType, Integer status);

    Optional<UserDO> findFirstByAccountAndRelType(String account, Integer relType);

    List<UserDO> findAllByAccountAndRelType(String account, Integer relType);

    Page<UserDO> findByMemberAndAccountContainsAndNameContainsAndStatusIn(MemberDO memberDO, String account, String name, List<Integer> statusList, Pageable pageable);

    List<UserDO> findByMemberAndUserType(MemberDO member, Integer userType);

    List<UserDO> findByMemberIdAndUserType(Long memberId, Integer userType);

    UserDO findFirstByMemberIdAndUserType(Long memberId, Integer userType);

    UserDO findFirstByMemberAndUserTypeOrderByIdAsc(MemberDO member, Integer typeEnum);

    UserDO findFirstByMemberAndId(MemberDO memberDO, Long id);

    UserDO findFirstByMemberNotAndPhone(MemberDO memberDO, String phone);

    UserDO findFirstByMemberNotAndEmail(MemberDO memberDO, String email);

    UserDO findFirstByEmailAndRelType(String email, Integer relType);

    UserDO findFirstByMemberAndUserType(MemberDO memberDO, Integer userType);

    @Query(value = "select * from mem_user where rel_type = :relType and (account = :account or phone = :phone) limit 1", nativeQuery = true)
    UserDO findLoginUser(@Param("account") String account, @Param("phone") String phone, @Param("relType") Integer relType);

    @Query(value = "select * from mem_user where rel_type = :relType and (account = :account or phone = :phone)", nativeQuery = true)
    List<UserDO> findAllLoginUser(@Param("account") String account, @Param("phone") String phone, @Param("relType") Integer relType);

    UserDO findFirstByPhoneAndRelType(String phone, Integer relType);

    List<UserDO> findAllByPhoneAndRelType(String phone, Integer relType);

    List<UserDO> findAllByEmailAndRelType(String phone, Integer relType);

    UserDO findFirstByTelCodeAndPhoneAndRelType(String countryCode, String phone, Integer relType);

    List<UserDO> findByMemberIn(List<MemberDO> memberDOList);

    List<UserDO> findAllByMemberId(Long memberId);

    boolean existsByRelTypeAndEmail(Integer relType, String email);

    boolean existsByRelTypeAndEmailAndIdNot(Integer relType, String email, Long id);

    boolean existsByRelTypeAndPhone(Integer relType, String phone);

    boolean existsByRelTypeAndPhoneAndIdNot(Integer relType, String phone, Long id);

    boolean existsByRelTypeAndPhoneAndMemberIdAndIdNot(Integer relType, String phone, Long memberId, Long id);

    List<UserDO> findByMember(MemberDO memberDO);

    @Query(value = "select * from mem_user where user_type = 1 and rel_type = 0", nativeQuery = true)
    Optional<UserDO> findPlatformAdminUser();

    @Query(value = "select distinct mu.* from mem_user mu left join mem_user_role_relation murr on mu.id = murr.user_id left join mem_user_role mur on mur.id = murr.user_role_id where rel_type = 0 and mur.has_im_auth = 1 and mur.status = 1", nativeQuery = true)
    List<UserDO> findHasImAuthPlatfromUser();


    @Query(value = "select distinct mu.* from mem_user mu " +
            "left join mem_user_role_relation murr on mu.id = murr.user_id " +
            "left join mem_user_role mur on mur.id = murr.user_role_id " +
            "where rel_type = 0 and mur.has_im_auth = 1 " +
            "and mur.status = 1 " +
            "and mu.id = ?1", nativeQuery = true)
    UserDO findPlatfromImUser(Long userId);

    /**
     * 检查邀请码是否已存在
     * @param invitationCode 邀请码
     * @return 是否存在
     */
    boolean existsByInvitationCode(String invitationCode);

    /**
     * 查询没有邀请码的用户列表
     * @return 没有邀请码的用户列表
     */
    @Query("SELECT u FROM UserDO u WHERE u.invitationCode IS NULL")
    List<UserDO> findUsersWithoutInvitationCode();

    /**
     * 分页查询没有邀请码的用户列表
     * @param pageable 分页参数
     * @return 没有邀请码的用户列表
     */
    @Query("SELECT u FROM UserDO u WHERE u.invitationCode IS NULL")
    Page<UserDO> findUsersWithoutInvitationCode(Pageable pageable);
}
