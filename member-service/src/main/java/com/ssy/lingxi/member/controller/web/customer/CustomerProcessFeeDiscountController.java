package com.ssy.lingxi.member.controller.web.customer;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.api.member.CustomerProcessFeeDiscountSyncReq;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.discount.web.CustomerProcessFeeDiscountPageReq;
import com.ssy.lingxi.member.model.req.discount.web.CustomerProcessFeeDiscountReq;
import com.ssy.lingxi.member.model.resp.customer.CustomerProcessFeeDiscountDetailResp;
import com.ssy.lingxi.member.model.resp.customer.CustomerProcessFeeDiscountPageResp;
import com.ssy.lingxi.member.service.web.ICustomerProcessFeeDiscountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 客户工费优惠相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-26
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/customer/discount")
@Slf4j
public class CustomerProcessFeeDiscountController {

    @Resource
    private ICustomerProcessFeeDiscountService customerProcessFeeDiscountService;

    /**
     * 同步客户工费优惠数据（外部系统调用）
     *
     * @param req 客户工费优惠请求参数
     * @return 操作结果
     */
    @PostMapping("/sync")
    public WrapperResp<Boolean> syncProcessFeeDiscount(@RequestBody @Valid CustomerProcessFeeDiscountSyncReq req) {
        String jsonStr = JsonUtil.toJson(req);
        log.info("客户工费优惠同步接口接收到数据，{}", jsonStr);
        //将客户工费优惠信息存入消息队列中
        return WrapperUtil.success(customerProcessFeeDiscountService.sync(req));
    }

    /**
     * 客户工费优惠 - 分页列表查询
     *
     * @param headers HTTP头部信息
     * @param pageReq 分页查询请求参数
     * @return 分页查询结果
     */
    @PostMapping("/page")
    public WrapperResp<PageDataResp<CustomerProcessFeeDiscountPageResp>> getPageList(@RequestHeader HttpHeaders headers, @RequestBody @Valid CustomerProcessFeeDiscountPageReq pageReq) {
        log.info("客户工费优惠分页查询，参数：{}", JsonUtil.toJson(pageReq));
        return WrapperUtil.success(customerProcessFeeDiscountService.getPageList(headers, pageReq));
    }

    /**
     * 客户工费优惠 - 查看详情
     *
     * @param headers HTTP头部信息
     * @param idReq   ID请求参数
     * @return 详情信息
     */
    @GetMapping("/detail")
    public WrapperResp<CustomerProcessFeeDiscountDetailResp> getDetail(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idReq) {
        log.info("查看客户工费优惠详情，ID：{}", idReq.getId());
        return WrapperUtil.success(customerProcessFeeDiscountService.getDetail(headers, idReq));
    }

    /**
     * 客户工费优惠 - 新增
     *
     * @param headers HTTP头部信息
     * @param req     新增请求参数
     * @return 新增结果
     */
    @PostMapping("/add")
    public WrapperResp<Boolean> add(@RequestHeader HttpHeaders headers, @RequestBody @Valid CustomerProcessFeeDiscountReq req) {
        log.info("新增客户工费优惠，参数：{}", JsonUtil.toJson(req));
        return WrapperUtil.success(customerProcessFeeDiscountService.add(headers, req));
    }

    /**
     * 客户工费优惠 - 编辑
     *
     * @param headers HTTP头部信息
     * @param req     编辑请求参数
     * @return 编辑结果
     */
    @PostMapping("/update")
    public WrapperResp<Boolean> update(@RequestHeader HttpHeaders headers, @RequestBody @Valid CustomerProcessFeeDiscountReq req) {
        log.info("编辑客户工费优惠，参数：{}", JsonUtil.toJson(req));
        return WrapperUtil.success(customerProcessFeeDiscountService.update(headers, req));
    }

    /**
     * 客户工费优惠 - 删除
     *
     * @param headers HTTP头部信息
     * @param idReq   ID请求参数
     * @return 删除结果
     */
    @PostMapping("/delete")
    public WrapperResp<Boolean> delete(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idReq) {
        log.info("删除客户工费优惠，ID：{}", idReq.getId());
        return WrapperUtil.success(customerProcessFeeDiscountService.delete(headers, idReq));
    }

    /**
     * 客户工费优惠 - 批量启用
     *
     * @param headers   HTTP头部信息
     * @param idListReq ID列表请求参数
     * @return 启用结果
     */
    @PostMapping("/enable")
    public WrapperResp<Boolean> enable(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdListReq idListReq) {
        log.info("批量启用客户工费优惠，ID列表：{}", JsonUtil.toJson(idListReq.getIdList()));
        return WrapperUtil.success(customerProcessFeeDiscountService.enable(headers, idListReq));
    }

    /**
     * 客户工费优惠 - 批量停用
     *
     * @param headers   HTTP头部信息
     * @param idListReq ID列表请求参数
     * @return 停用结果
     */
    @PostMapping("/disable")
    public WrapperResp<Boolean> disable(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdListReq idListReq) {
        log.info("批量停用客户工费优惠，ID列表：{}", JsonUtil.toJson(idListReq.getIdList()));
        return WrapperUtil.success(customerProcessFeeDiscountService.disable(headers, idListReq));
    }
}
