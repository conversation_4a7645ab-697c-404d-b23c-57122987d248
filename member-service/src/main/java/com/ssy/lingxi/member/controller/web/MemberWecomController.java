package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.rest.model.req.wecom.SendMsgReq;
import com.ssy.lingxi.component.rest.model.resp.wecom.ApplicationMsgResp;
import com.ssy.lingxi.component.rest.service.IWeComService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 会员能力 - 企业微信相关接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/15
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/wecom")
public class MemberWecomController extends BaseController {

    @Resource
    private IWeComService weComService;

    /**
     * 发送企业微信消息
     */
    @PostMapping("/sendMessage")
    public ApplicationMsgResp sendMessage(@RequestBody SendMsgReq sendMsgReq) {
        return weComService.sendAppMsg(sendMsgReq);
    }

}
