package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.manage.MessageNoticeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.service.IMessageService;
import com.ssy.lingxi.component.base.util.AopProxyUtil;
import com.ssy.lingxi.component.rabbitMQ.model.req.SystemMessageReq;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberReceiveInvitationDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.dto.MemberAppraisalMessageDTO;
import com.ssy.lingxi.member.model.dto.MemberChangeRequestFormMessageDTO;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 调用消息服务Feign接口
 * <AUTHOR>
 * @since 2021/6/23 15:47
 **/
@Service
public class MessageFeignServiceImpl implements IMessageFeignService {
    @Resource
    private IMessageService messageService;

    @Override
    public SystemMessageReq buildSystemMessage(Long memberId, Long roleId, String messageCode, List<String> params) {
        SystemMessageReq request = new SystemMessageReq();
        request.setMemberId(memberId);
        request.setRoleId(roleId);
        request.setParams(params);
        request.setMessageNotice(messageCode);
        return request;
    }

    @Override
    public SystemMessageReq buildUserSystemMessage(Long memberId, Long roleId, Long userId, String messageCode, List<String> params) {
        SystemMessageReq request = new SystemMessageReq();
        request.setMemberId(memberId);
        request.setRoleId(roleId);
        request.setUserId(userId);
        request.setParams(params);
        request.setMessageNotice(messageCode);
        return request;
    }

    @Override
    public void sendSystemMessage(Long memberId, Long roleId, String messageCode, List<String> params) {
        if (!StringUtils.hasLength(messageCode)) {
            return;
        }

        //将消息存入队列中
        SystemMessageReq systemMessageReq = this.buildSystemMessage(memberId, roleId, messageCode, params);
        messageService.sendSystemMessage(systemMessageReq);
    }

    @Override
    public void batchSendSystemMessage(List<SystemMessageReq> systemMessageReqList) {
        if (CollectionUtils.isEmpty(systemMessageReqList)) {
            return;
        }
        //将消息存入队列中
        messageService.sendBatchSystemMessage(systemMessageReqList);
    }

    @Override
    public void sendUserSystemMessage(Long memberId, Long roleId, Long userId, String messageCode, List<String> params) {
        SystemMessageReq systemMessageReq = this.buildUserSystemMessage(memberId, roleId, userId, messageCode, params);
        //将消息存入队列中
        messageService.sendSystemMessage(systemMessageReq);
    }

    /**
     * 平台会员审核、会员入库审核时，发送消息
     *
     * @param relation 会员关系
     * @param memberId 消息接收方会员Id
     * @param roleId 消息接收方会员角色Id
     */
    @Async
    @Override
    public void sendMemberValidateMessage(MemberRelationDO relation, Long memberId, Long roleId) {
        String messageCode;
        List<String> parameters = Stream.of(relation.getSubRoleName(), relation.getSubMember().getName()).collect(Collectors.toList());
        if (relation.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
            PlatformInnerStatusEnum innerStatusEnum = PlatformInnerStatusEnum.parse(relation.getInnerStatus());
            switch (innerStatusEnum) {
                case TO_BE_COMMIT:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_SUBMIT.getCode();
                    break;
                case TO_BE_VERIFY_STEP1:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_AUDIT_STEP_1.getCode();
                    break;
                case TO_BE_VERIFY_STEP2:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_AUDIT_STEP_2.getCode();
                    break;
                case COMMIT_NOT_PASSED:
                case VERIFY_STEP1_NOT_PASSED:
                case VERIFY_STEP2_NOT_PASSED:
                case TO_CONFIRM:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_AUDIT_CONFIRM.getCode();
                    break;
                case VERIFY_PASSED:
                    messageCode = MessageNoticeEnum.PLATFORM_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getSubRoleName(), CheckStatusEnum.AGREE.getMessage()).collect(Collectors.toList());
                    break;
                case VERIFY_NOT_PASSED:
                    messageCode = MessageNoticeEnum.PLATFORM_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getSubRoleName(), CheckStatusEnum.REFUSE.getMessage()).collect(Collectors.toList());
                    break;
                default:
                    messageCode = "";
            }
        } else {
            MemberInnerStatusEnum innerStatusEnum = MemberInnerStatusEnum.parse(relation.getInnerStatus());
            switch (innerStatusEnum) {
                case TO_VERIFY_DEPOSITORY_DETAIL:
                    messageCode = MessageNoticeEnum.MC_STORAGE_INFO_AUDIT.getCode();
                    break;
                case TO_VERIFY_DEPOSITORY_QUALIFICATION:
                    messageCode = MessageNoticeEnum.MC_STORAGE_QUALIFICATION_AUDIT.getCode();
                    break;
                case TO_INSPECT_DEPOSITORY:
                    messageCode = MessageNoticeEnum.MC_STORAGE_INSPECT.getCode();
                    break;
                case TO_CLASSIFY_DEPOSITORY:
                    messageCode = MessageNoticeEnum.MC_STORAGE_CLASSIFY.getCode();
                    break;
                case TO_DEPOSIT_GRADE_ONE:
                    messageCode = MessageNoticeEnum.MC_STORAGE_AUDIT_STEP_1.getCode();
                    break;
                case TO_DEPOSIT_GRADE_TWO:
                    messageCode = MessageNoticeEnum.MC_STORAGE_AUDIT_STEP_2.getCode();
                    break;
                case DEPOSITORY_DETAIL_NOT_PASSED:
                case DEPOSITORY_QUALIFICATION_NOT_PASSED:
                case DEPOSITORY_INSPECTION_NOT_PASSED:
                case DEPOSITORY_CLASSIFICATION_NOT_PASSED:
                case DEPOSITORY_GRADE_ONE_NOT_PASSED:
                case DEPOSITORY_GRADE_TWO_NOT_PASSED:
                case TO_CONFIRM_DEPOSITORY:
                    messageCode = MessageNoticeEnum.MC_STORAGE_CONFIRM.getCode();
                    break;
                    // 入库审核通过/不通过是向下级发送消息
                case VERIFY_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.UP_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.AGREE.getMessage()).collect(Collectors.toList());
                    break;
                case VERIFY_NOT_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.UP_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.REFUSE.getMessage()).collect(Collectors.toList());
                    break;
                case TO_MODIFY_GRADE_ONE:
                    messageCode = MessageNoticeEnum.MC_MODIFICATION_STEP_1.getCode();
                    break;
                case TO_MODIFY_GRADE_TWO:
                    messageCode = MessageNoticeEnum.MC_MODIFICATION_STEP_2.getCode();
                    break;
                case MODIFY_GRADE_ONE_NOT_PASSED:
                case MODIFY_GRADE_TWO_NOT_PASSED:
                case TO_CONFIRM_MODIFY:
                    messageCode = MessageNoticeEnum.MC_MODIFICATION_CONFIRM.getCode();
                    break;
                default:
                    messageCode = "";
                    break;
            }
        }

        if (!StringUtils.hasLength(messageCode)) {
            return;
        }

        SystemMessageReq systemMessageReq = new SystemMessageReq();
        systemMessageReq.setMemberId(memberId);
        systemMessageReq.setRoleId(roleId);
        systemMessageReq.setMessageNotice(messageCode);
        systemMessageReq.setParams(parameters);
        //将消息存入队列中
        messageService.sendSystemMessage(systemMessageReq);
    }

    /**
     * 会员入库审核时，发送消息
     *
     * @param relation 会员关系
     * @param roleTag  角色标签
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sendMemberValidateMessage(MemberRelationDO relation, Integer roleTag) {
        MessageFeignServiceImpl currentProxy = AopProxyUtil.getCurrentProxy(this.getClass());
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        switch (roleTagEnum) {
            case MEMBER:
                currentProxy.sendMemberValidateMessage(relation, relation.getMemberId(), relation.getRoleId());
                break;
            case SUPPLIER:
                currentProxy.sendSupplierValidateMessage(relation, relation.getMemberId(), relation.getRoleId());
                break;
            case CUSTOMER:
                currentProxy.sendCustomerValidateMessage(relation, relation.getMemberId(), relation.getRoleId());
                break;
            default:
                break;
        }
    }

    /**
     * 平台会员审核、客户入库审核时，发送消息
     *
     * @param relation 供应商关系
     * @param memberId 会员ID
     * @param roleId 角色ID
     */
    @Async
    @Override
    public void sendCustomerValidateMessage(MemberRelationDO relation, Long memberId, Long roleId) {
        String messageCode;
        List<String> parameters = Stream.of(relation.getSubRoleName(), relation.getSubMember().getName()).collect(Collectors.toList());
        if (relation.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
            PlatformInnerStatusEnum innerStatusEnum = PlatformInnerStatusEnum.parse(relation.getInnerStatus());
            switch (innerStatusEnum) {
                case TO_BE_COMMIT:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_SUBMIT.getCode();
                    break;
                case TO_BE_VERIFY_STEP1:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_AUDIT_STEP_1.getCode();
                    break;
                case TO_BE_VERIFY_STEP2:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_AUDIT_STEP_2.getCode();
                    break;
                case COMMIT_NOT_PASSED:
                case VERIFY_STEP1_NOT_PASSED:
                case VERIFY_STEP2_NOT_PASSED:
                case TO_CONFIRM:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_AUDIT_CONFIRM.getCode();
                    break;
                case VERIFY_PASSED:
                    messageCode = MessageNoticeEnum.PLATFORM_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getSubRoleName(), CheckStatusEnum.AGREE.getMessage()).collect(Collectors.toList());
                    break;
                case VERIFY_NOT_PASSED:
                    messageCode = MessageNoticeEnum.PLATFORM_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getSubRoleName(), CheckStatusEnum.REFUSE.getMessage()).collect(Collectors.toList());
                    break;
                default:
                    messageCode = "";
                    break;
            }
        } else {
            MemberInnerStatusEnum innerStatusEnum = MemberInnerStatusEnum.parse(relation.getInnerStatus());
            switch (innerStatusEnum) {
                case TO_VERIFY_DEPOSITORY_DETAIL:
                    messageCode = MessageNoticeEnum.CUSTOMER_VERIFY_DEPOSITORY_DETAIL.getCode();
                    break;
                case TO_VERIFY_DEPOSITORY_QUALIFICATION:
                    messageCode = MessageNoticeEnum.CUSTOMER_VERIFY_DEPOSITORY_QUALIFICATION.getCode();
                    break;
                case TO_INSPECT_DEPOSITORY:
                    messageCode = MessageNoticeEnum.CUSTOMER_INSPECT_DEPOSITORY.getCode();
                    break;
                case TO_CLASSIFY_DEPOSITORY:
                    messageCode = MessageNoticeEnum.CUSTOMER_CLASSIFY_DEPOSITORY.getCode();
                    break;
                case TO_DEPOSIT_GRADE_ONE:
                    messageCode = MessageNoticeEnum.CUSTOMER_DEPOSIT_GRADE_ONE.getCode();
                    break;
                case TO_DEPOSIT_GRADE_TWO:
                    messageCode = MessageNoticeEnum.CUSTOMER_DEPOSIT_GRADE_TWO.getCode();
                    break;
                case DEPOSITORY_DETAIL_NOT_PASSED:
                case DEPOSITORY_QUALIFICATION_NOT_PASSED:
                case DEPOSITORY_INSPECTION_NOT_PASSED:
                case DEPOSITORY_CLASSIFICATION_NOT_PASSED:
                case DEPOSITORY_GRADE_ONE_NOT_PASSED:
                case DEPOSITORY_GRADE_TWO_NOT_PASSED:
                case TO_CONFIRM_DEPOSITORY:
                    messageCode = MessageNoticeEnum.CUSTOMER_CONFIRM_DEPOSITORY.getCode();
                    break;
                // 入库审核通过/不通过是向下级发送消息
                case VERIFY_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.CUSTOMER_UP_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.AGREE.getMessage()).collect(Collectors.toList());
                    break;
                case VERIFY_NOT_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.CUSTOMER_UP_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.REFUSE.getMessage()).collect(Collectors.toList());
                    break;
                case TO_MODIFY_GRADE_ONE:
                    messageCode = MessageNoticeEnum.CUSTOMER_CHANGE_DEPOSITORY_ONE.getCode();
                    break;
                case TO_MODIFY_GRADE_TWO:
                    messageCode = MessageNoticeEnum.CUSTOMER_CHANGE_DEPOSITORY_TWO.getCode();
                    break;
                case MODIFY_GRADE_ONE_NOT_PASSED:
                case MODIFY_GRADE_TWO_NOT_PASSED:
                case TO_CONFIRM_MODIFY:
                    messageCode = MessageNoticeEnum.CUSTOMER_CHANGE_DEPOSITORY_CONFIRM.getCode();
                    break;
                case MODIFY_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.CUSTOMER_UP_MEMBER_CONFIRM.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.AGREE.getMessage()).collect(Collectors.toList());
                    break;
                case MODIFY_NOT_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.CUSTOMER_UP_MEMBER_CONFIRM.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.REFUSE.getMessage()).collect(Collectors.toList());
                    break;
                default:
                    messageCode = "";
                    break;
            }
        }

        // 发送消息
        sendSystemMessage(memberId, roleId, messageCode, parameters);
    }

    /**
     * 发送邀请消息
     * @param memberReceiveInvitation 邀请消息实体
     * @param roleTag 角色标签
     */
    @Override
    public void sendMemberInvitationMessage(MemberReceiveInvitationDO memberReceiveInvitation, Integer roleTag) {
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        String messageNotice;
        switch (roleTagEnum) {
            case MEMBER:
                messageNotice = MessageNoticeEnum.MEMBER_BE_INVITED.getCode();
                break;
            case SUPPLIER:
                messageNotice = MessageNoticeEnum.VENDOR_BE_INVITED.getCode();
                break;
            case CUSTOMER:
                messageNotice = MessageNoticeEnum.CUSTOMER_BE_INVITED.getCode();
                break;
            default:
                messageNotice = "";
                break;
        }
        List<String> parameters = Stream.of(memberReceiveInvitation.getMember().getName(), memberReceiveInvitation.getSubMember().getName(), memberReceiveInvitation.getInvitationTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER)).collect(Collectors.toList());
        SystemMessageReq request = new SystemMessageReq();
        request.setMemberId(memberReceiveInvitation.getSubMemberId());
        request.setRoleId(memberReceiveInvitation.getSubRoleId());
        request.setParams(parameters);
        request.setMessageNotice(messageNotice);
        //将消息存入队列中
        messageService.sendSystemMessage(request);
    }

    /**
     * 供应商入库审核时，发送消息
     *
     * @param relation 供应商关系
     * @param memberId 会员ID
     * @param roleId 角色ID
     */
    @Async
    @Override
    public void sendSupplierValidateMessage(MemberRelationDO relation, Long memberId, Long roleId) {
        String messageCode;
        List<String> parameters = Stream.of(relation.getSubRoleName(), relation.getSubMember().getName()).collect(Collectors.toList());
        if (relation.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
            PlatformInnerStatusEnum innerStatusEnum = PlatformInnerStatusEnum.parse(relation.getInnerStatus());
            switch (innerStatusEnum) {
                case TO_BE_COMMIT:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_SUBMIT.getCode();
                    break;
                case TO_BE_VERIFY_STEP1:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_AUDIT_STEP_1.getCode();
                    break;
                case TO_BE_VERIFY_STEP2:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_AUDIT_STEP_2.getCode();
                    break;
                case COMMIT_NOT_PASSED:
                case VERIFY_STEP1_NOT_PASSED:
                case VERIFY_STEP2_NOT_PASSED:
                case TO_CONFIRM:
                    messageCode = MessageNoticeEnum.MEMBER_PLATFORM_AUDIT_CONFIRM.getCode();
                    break;
                case VERIFY_PASSED:
                    messageCode = MessageNoticeEnum.PLATFORM_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getSubRoleName(), CheckStatusEnum.AGREE.getMessage()).collect(Collectors.toList());
                    break;
                case VERIFY_NOT_PASSED:
                    messageCode = MessageNoticeEnum.PLATFORM_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getSubRoleName(), CheckStatusEnum.REFUSE.getMessage()).collect(Collectors.toList());
                    break;
                default:
                    messageCode = "";
                    break;
            }
        } else {
            MemberInnerStatusEnum innerStatusEnum = MemberInnerStatusEnum.parse(relation.getInnerStatus());
            parameters = Collections.singletonList(relation.getSubMember().getName());
            switch (innerStatusEnum) {
                case TO_VERIFY_DEPOSITORY_DETAIL:
                    messageCode = MessageNoticeEnum.VENDOR_VERIFY_DEPOSITORY_INFO.getCode();
                    break;
                case TO_VERIFY_DEPOSITORY_QUALIFICATION:
                    messageCode = MessageNoticeEnum.VENDOR_VERIFY_DEPOSITORY_QUALIFICATION.getCode();
                    break;
                case TO_INSPECT_DEPOSITORY:
                    messageCode = MessageNoticeEnum.VENDOR_INSPECT_DEPOSITORY.getCode();
                    break;
                case TO_CLASSIFY_DEPOSITORY:
                    messageCode = MessageNoticeEnum.VENDOR_CLASSIFY_DEPOSITORY.getCode();
                    break;
                case TO_DEPOSIT_GRADE_ONE:
                    messageCode = MessageNoticeEnum.VENDOR_VERIFY_DEPOSIT_STEP_1.getCode();
                    break;
                case TO_DEPOSIT_GRADE_TWO:
                    messageCode = MessageNoticeEnum.VENDOR_VERIFY_DEPOSIT_STEP_2.getCode();
                    break;
                case DEPOSITORY_DETAIL_NOT_PASSED:
                case DEPOSITORY_QUALIFICATION_NOT_PASSED:
                case DEPOSITORY_INSPECTION_NOT_PASSED:
                case DEPOSITORY_CLASSIFICATION_NOT_PASSED:
                case DEPOSITORY_GRADE_ONE_NOT_PASSED:
                case DEPOSITORY_GRADE_TWO_NOT_PASSED:
                case TO_CONFIRM_DEPOSITORY:
                    messageCode = MessageNoticeEnum.VENDOR_CONFIRM_DEPOSITORY.getCode();
                    break;
                case TO_MODIFY_GRADE_ONE:
                    messageCode = MessageNoticeEnum.VENDOR_CHANGE_VERIFY_DEPOSIT_STEP_1.getCode();
                    break;
                case TO_MODIFY_GRADE_TWO:
                    messageCode = MessageNoticeEnum.VENDOR_CHANGE_VERIFY_DEPOSIT_STEP_2.getCode();
                    break;
                case MODIFY_GRADE_ONE_NOT_PASSED:
                case MODIFY_GRADE_TWO_NOT_PASSED:
                case TO_CONFIRM_MODIFY:
                    messageCode = MessageNoticeEnum.VENDOR_CHANGE_DEPOSITORY_CONFIRM.getCode();
                    break;
                // 入库审核通过/不通过是向下级发送消息
                case VERIFY_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.VENDOR_UP_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.AGREE.getMessage()).collect(Collectors.toList());
                    break;
                case VERIFY_NOT_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.VENDOR_UP_MEMBER_AUDIT.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.REFUSE.getMessage()).collect(Collectors.toList());
                    break;
                case MODIFY_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.VENDOR_UP_MEMBER_CONFIRM.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.AGREE.getMessage()).collect(Collectors.toList());
                    break;
                case MODIFY_NOT_PASSED:
                    memberId = relation.getSubMemberId();
                    roleId = relation.getSubRoleId();
                    messageCode = MessageNoticeEnum.VENDOR_UP_MEMBER_CONFIRM.getCode();
                    parameters = Stream.of(relation.getMember().getName(), relation.getSubRoleName(), CheckStatusEnum.REFUSE.getMessage()).collect(Collectors.toList());
                    break;
                default:
                    messageCode = "";
                    break;
            }
        }

        // 发送消息
        sendSystemMessage(memberId, roleId, messageCode, parameters);
    }

    /**
     * 发送考评消息
     *
     * @param appraisalDTO 会员考评DTO
     * @param userId       用户id （为空时代表发送系统消息）
     * @param roleTag      角色标签
     */
    @Override
    @Async
    public void sendMemberAppraisalMessage(MemberAppraisalMessageDTO appraisalDTO, Long userId, Integer roleTag) {
        Long memberId = appraisalDTO.getMemberId();
        Long roleId = appraisalDTO.getRoleId();
        String messageCode = "";
        List<String> parameters = Stream.of(appraisalDTO.getSubMemberName()).collect(Collectors.toList());
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        MemberAppraisalStatusEnum appraisalStatusEnum = MemberAppraisalStatusEnum.parse(appraisalDTO.getStatus());
        switch (roleTagEnum) {
            case MEMBER:
                switch (appraisalStatusEnum) {
                    case WAIT_PUBLISH:
                        messageCode = MessageNoticeEnum.MC_APPRAISAL_PUBLISH.getCode();
                        break;
                    case WAIT_GRADE:
                        messageCode = MessageNoticeEnum.MC_APPRAISAL_GRADE.getCode();
                        break;
                    case WAIT_SUBMIT:
                        messageCode = MessageNoticeEnum.MC_APPRAISAL_SUMMARY_RESULT.getCode();
                        break;
                    case WAIT_AUDIT_1:
                        messageCode = MessageNoticeEnum.MC_APPRAISAL_RESULT_AUDIT_STEP_1.getCode();
                        break;
                    case WAIT_AUDIT_2:
                        messageCode = MessageNoticeEnum.MC_APPRAISAL_RESULT_AUDIT_STEP_2.getCode();
                        break;
                    case WAIT_NOTIFICATION:
                        messageCode = MessageNoticeEnum.MC_APPRAISAL_NOTIFICATION.getCode();
                        break;
                    case SUBMIT_NOTIFICATION:
                        memberId = appraisalDTO.getSubMemberId();
                        roleId = appraisalDTO.getSubRoleId();
                        messageCode = MessageNoticeEnum.MC_APPRAISAL_RESULT.getCode();
                        parameters = Arrays.asList(appraisalDTO.getMemberName(), appraisalDTO.getSubject());
                        break;
                }
                break;
            case CUSTOMER:
                switch (appraisalStatusEnum) {
                    case WAIT_PUBLISH:
                        messageCode = MessageNoticeEnum.CUSTOMER_APPRAISAL_PUBLISH.getCode();
                        break;
                    case WAIT_GRADE:
                        messageCode = MessageNoticeEnum.CUSTOMER_APPRAISAL_GRADE.getCode();
                        break;
                    case WAIT_SUBMIT:
                        messageCode = MessageNoticeEnum.CUSTOMER_APPRAISAL_SUMMARY_RESULT.getCode();
                        break;
                    case WAIT_AUDIT_1:
                        messageCode = MessageNoticeEnum.CUSTOMER_APPRAISAL_RESULT_AUDIT_STEP_1.getCode();
                        break;
                    case WAIT_AUDIT_2:
                        messageCode = MessageNoticeEnum.CUSTOMER_APPRAISAL_RESULT_AUDIT_STEP_2.getCode();
                        break;
                    case WAIT_NOTIFICATION:
                        messageCode = MessageNoticeEnum.CUSTOMER_APPRAISAL_NOTIFICATION.getCode();
                        break;
                    case SUBMIT_NOTIFICATION:
                        memberId = appraisalDTO.getSubMemberId();
                        roleId = appraisalDTO.getSubRoleId();
                        messageCode = MessageNoticeEnum.CUSTOMER_APPRAISAL_RESULT.getCode();
                        parameters = Arrays.asList(appraisalDTO.getMemberName(), appraisalDTO.getSubject());
                        break;
                }
                break;
            case SUPPLIER:
                switch (appraisalStatusEnum) {
                    case WAIT_PUBLISH:
                        messageCode = MessageNoticeEnum.VENDOR_APPRAISAL_PUBLISH.getCode();
                        break;
                    case WAIT_GRADE:
                        messageCode = MessageNoticeEnum.VENDOR_APPRAISAL_GRADE.getCode();
                        break;
                    case WAIT_SUBMIT:
                        messageCode = MessageNoticeEnum.VENDOR_APPRAISAL_SUMMARY_RESULT.getCode();
                        break;
                    case WAIT_AUDIT_1:
                        messageCode = MessageNoticeEnum.VENDOR_MC_APPRAISAL_RESULT_AUDIT_STEP_1.getCode();
                        break;
                    case WAIT_AUDIT_2:
                        messageCode = MessageNoticeEnum.VENDOR_APPRAISAL_RESULT_AUDIT_STEP_2.getCode();
                        break;
                    case WAIT_NOTIFICATION:
                        messageCode = MessageNoticeEnum.VENDOR_APPRAISAL_NOTIFICATION.getCode();
                        break;
                    case SUBMIT_NOTIFICATION:
                        memberId = appraisalDTO.getSubMemberId();
                        roleId = appraisalDTO.getSubRoleId();
                        messageCode = MessageNoticeEnum.VENDOR_APPRAISAL_RESULT.getCode();
                        parameters = Arrays.asList(appraisalDTO.getMemberName(), appraisalDTO.getSubject());
                        break;
                }
                break;
            default:
                messageCode = "";
                break;
        }

        // 发送消息
        if (NumberUtil.notNullOrZero(userId)) {
            sendUserSystemMessage(memberId, roleId, userId, messageCode, parameters);
        } else {
            sendSystemMessage(memberId, roleId, messageCode, parameters);
        }
    }

    /**
     * 发送生命周期变更申请消息
     *
     * @param changeRequestFormDTO 会员考评DTO
     * @param userId               用户id （为空时代表发送系统消息）
     * @param roleTag              角色标签
     */
    @Async
    @Override
    public void sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO changeRequestFormDTO, Long userId, Integer roleTag) {
        Long memberId = changeRequestFormDTO.getMemberId();
        Long roleId = changeRequestFormDTO.getRoleId();
        String messageCode = "";
        List<String> parameters = Stream.of(changeRequestFormDTO.getChangeRequestFormNo(), changeRequestFormDTO.getChangeRequestSummary()).collect(Collectors.toList());
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        MemberChangeRequestFormStatusEnum appraisalStatusEnum = MemberChangeRequestFormStatusEnum.parse(changeRequestFormDTO.getStatus());
        switch (roleTagEnum) {
            case MEMBER:
            case CUSTOMER:
                switch (appraisalStatusEnum) {
                    case SUBMIT_FORM:
                        messageCode = MessageNoticeEnum.CUSTOMER_LIFE_CYCLE_MANAGEMENT_SUBMIT.getCode();
                        break;
                    case WAIT_GRADE:
                        messageCode = MessageNoticeEnum.CUSTOMER_MC_APPRAISAL_GRADE.getCode();
                        break;
                    case WAIT_SUBMIT:
                        messageCode = MessageNoticeEnum.CUSTOMER_AGGREGATED_SCORING_RESULTS.getCode();
                        break;
                    case WAIT_AUDIT_1:
                        messageCode = MessageNoticeEnum.CUSTOMER_CHANGE_REQUEST_ONE.getCode();
                        break;
                    case WAIT_AUDIT_2:
                        messageCode = MessageNoticeEnum.CUSTOMER_CHANGE_REQUEST_TWO.getCode();
                        break;
                    case WAIT_CONFIRMED:
                        messageCode = MessageNoticeEnum.CUSTOMER_LIFE_CYCLE_MANAGEMENT_CONFIRM.getCode();
                        break;
                    case WAIT_CONFIRMED_PASSED:
                        memberId = changeRequestFormDTO.getSubMemberId();
                        roleId = changeRequestFormDTO.getSubRoleId();
                        if (StringUtils.hasText(changeRequestFormDTO.getCurrentLifecycleStagesName())) {
                            messageCode = MessageNoticeEnum.CUSTOMER_LIFE_CYCLE_MANAGEMENT_CHANGE_PASSED.getCode();
                            parameters = Arrays.asList(changeRequestFormDTO.getMemberName(),
                                    changeRequestFormDTO.getCurrentLifecycleStagesName(),
                                    changeRequestFormDTO.getTargetLifecycleStagesName());
                        } else {
                            messageCode = MessageNoticeEnum.CUSTOMER_LIFE_CYCLE_MANAGEMENT_CONFIRM_CHANGE.getCode();
                            parameters = Arrays.asList(changeRequestFormDTO.getMemberName(),
                                    changeRequestFormDTO.getTargetLifecycleStagesName());
                        }
                        break;
                }
                break;
            case SUPPLIER:
                switch (appraisalStatusEnum) {
                    case SUBMIT_FORM:
                        messageCode = MessageNoticeEnum.VENDOR_LIFE_CYCLE_MANAGEMENT_SUBMIT.getCode();
                        break;
                    case WAIT_GRADE:
                        messageCode = MessageNoticeEnum.VENDOR_MC_APPRAISAL_GRADE.getCode();
                        break;
                    case WAIT_SUBMIT:
                        messageCode = MessageNoticeEnum.VENDOR_AGGREGATED_SCORING_RESULTS.getCode();
                        break;
                    case WAIT_AUDIT_1:
                        messageCode = MessageNoticeEnum.VENDOR_CHANGE_REQUEST_ONE.getCode();
                        break;
                    case WAIT_AUDIT_2:
                        messageCode = MessageNoticeEnum.VENDOR_CHANGE_REQUEST_TWO.getCode();
                        break;
                    case WAIT_CONFIRMED:
                        messageCode = MessageNoticeEnum.VENDOR_LIFE_CYCLE_MANAGEMENT_CONFIRM.getCode();
                        break;
                    case WAIT_CONFIRMED_PASSED:
                        memberId = changeRequestFormDTO.getSubMemberId();
                        roleId = changeRequestFormDTO.getSubRoleId();
                        if (StringUtils.hasText(changeRequestFormDTO.getCurrentLifecycleStagesName())) {
                            messageCode = MessageNoticeEnum.VENDOR_LIFE_CYCLE_MANAGEMENT_CHANGE_PASSED.getCode();
                            parameters = Arrays.asList(changeRequestFormDTO.getMemberName(),
                                    changeRequestFormDTO.getCurrentLifecycleStagesName(),
                                    changeRequestFormDTO.getTargetLifecycleStagesName());
                        } else {
                            messageCode = MessageNoticeEnum.VENDOR_LIFE_CYCLE_MANAGEMENT_CONFIRM_CHANGE.getCode();
                            parameters = Arrays.asList(changeRequestFormDTO.getMemberName(),
                                    changeRequestFormDTO.getTargetLifecycleStagesName());
                        }
                        break;
                }
                break;
            default:
                messageCode = "";
                break;
        }

        // 发送消息
        if (NumberUtil.notNullOrZero(userId)) {
            sendUserSystemMessage(memberId, roleId, userId, messageCode, parameters);
        } else {
            sendSystemMessage(memberId, roleId, messageCode, parameters);
        }
    }
}
