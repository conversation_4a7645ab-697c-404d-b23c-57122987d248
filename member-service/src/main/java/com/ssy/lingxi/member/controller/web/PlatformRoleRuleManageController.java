package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.MemberIdReq;
import com.ssy.lingxi.member.model.req.platform.*;
import com.ssy.lingxi.member.model.resp.platform.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseSiteService;
import com.ssy.lingxi.member.service.web.IPlatformMemberRoleRuleService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 设置会员适用角色规则相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-10
 **/
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/platform/roleRule")
public class PlatformRoleRuleManageController {
    @Resource
    private IPlatformMemberRoleRuleService roleRuleService;

    @Resource
    private IBaseMemberCacheService baseMemberCacheService;

    @Resource
    private IBaseSiteService baseSiteService;

    /**
     * 校验是否开启SAAS多租户部署
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/isEnableMultiTenancy")
    public WrapperResp<Boolean> isEnableMultiTenancy(@RequestHeader HttpHeaders headers) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
        return WrapperUtil.success(baseSiteService.isEnableMultiTenancy(headers));
    }

    /**
     * 根据会员名称，模糊、分页查询会员列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/memberPage")
    public WrapperResp<PageDataResp<MemberManageResp>> pageMembers(@RequestHeader HttpHeaders headers, @Valid PageMemberByNameDataReq pageVO) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
        return WrapperUtil.success(roleRuleService.pageMembers(pageVO));
    }

    /**
     * 根据会员名称，模糊、分页查询会员管理列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/memberRulePage")
    public WrapperResp<PageDataResp<MemberRoleRuleResp>> pageRuleMembers(@RequestHeader HttpHeaders headers, @Valid PageMemberByNameDataReq pageVO) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
        return WrapperUtil.success(roleRuleService.pageRuleMembers(pageVO));
    }

    /**
     * 根据角色名称，模糊、分页查询角色详情列表
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @PostMapping("/rolePage")
    public WrapperResp<PageDataResp<RoleRuleManageResp>> pageRoles(@RequestHeader HttpHeaders headers, @Valid @RequestBody PageRoleRuleDataReq pageVO) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
        return WrapperUtil.success(roleRuleService.pageRoles(pageVO));
    }

    /**
     * 查询下级会员适用角色详情列表(注册)
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/register/subMemberRole")
    public WrapperResp<List<RoleRuleRegisterResp>> pageRoles(@RequestHeader HttpHeaders headers, @Valid MemberIdReq idVO) {
        baseMemberCacheService.checkWebRequestHeader(headers);
        Boolean enableMultiTenancy = baseSiteService.isEnableMultiTenancy(headers);
        if (enableMultiTenancy) {
            return WrapperUtil.success(roleRuleService.subMemberRolesRegister(idVO.getMemberId()));
        } else {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_SY_SITE_NOT_ENABLED_MULTI_TENANCY);
        }
    }

    /**
     * 查询当前会员适用角色详情列表
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @GetMapping("/memberRole")
    public WrapperResp<List<RoleRuleManageResp>> memberRoles(@RequestHeader HttpHeaders headers, @Valid MemberByIdDataReq idVO) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
        return WrapperUtil.success(roleRuleService.memberRoles(idVO.getMemberId()));
    }

    /**
     * 查询下级会员适用角色详情列表
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @GetMapping("/subMemberRole")
    public WrapperResp<List<RoleRuleManageResp>> subMemberRoles(@RequestHeader HttpHeaders headers, @Valid MemberByIdDataReq idVO) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
        return WrapperUtil.success(roleRuleService.subMemberRoles(idVO.getMemberId()));
    }

    /**
     * “新增会员适用角色” - 选择会员
     *
     * @param headers    Http头部信息
     * @param memberIdReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/selectMember")
    public WrapperResp<MemberSelectResp> selectMember(@RequestHeader HttpHeaders headers, @Valid MemberIdReq memberIdReq) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
        return WrapperUtil.success(roleRuleService.selectMember(headers, memberIdReq));
    }

    /**
     * “新增会员适用角色” - 增加角色
     *
     * @param headers    Http头部信息
     * @param roleRuleVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/add")
    public WrapperResp<Void> addRuleRoles(@RequestHeader HttpHeaders headers, @Valid @RequestBody AddRoleRuleReq roleRuleVO) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
         roleRuleService.addRuleRoles(headers, roleRuleVO);
        return WrapperUtil.success();
    }

    /**
     * 删除会员适用角色
     *
     * @param headers    Http头部信息
     * @param roleRuleVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delRuleRoles(@RequestHeader HttpHeaders headers, @Valid @RequestBody DelRoleRuleReq roleRuleVO) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
         roleRuleService.delRuleRoles(headers, roleRuleVO);
        return WrapperUtil.success();
    }

    /**
     * 会员适用角色详情列表
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @GetMapping("/detail")
    public WrapperResp<MemberRoleRuleDetailResp> getDetails(@RequestHeader HttpHeaders headers, @Valid MemberDetailByIdReq idVO) {
        baseMemberCacheService.needLoginFromManagePlatform(headers);
        return WrapperUtil.success(roleRuleService.getDetails(idVO));
    }
}
