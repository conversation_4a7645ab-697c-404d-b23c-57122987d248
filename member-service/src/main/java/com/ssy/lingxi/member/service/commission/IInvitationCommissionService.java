package com.ssy.lingxi.member.service.commission;

import com.ssy.lingxi.member.model.dto.commission.MemberRegistrationSuccessDTO;

/**
 * 邀请分佣服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
public interface IInvitationCommissionService {

    /**
     * 处理会员注册成功后的邀请分佣
     * @param registrationDTO 注册成功消息
     */
    void processRegistrationCommission(MemberRegistrationSuccessDTO registrationDTO);

    /**
     * 处理会员企业认证成功后的邀请分佣
     * @param userId 用户ID
     * @param memberId 会员ID
     */
    void processCertificationCommission(Long userId, Long memberId);

    /**
     * 处理会员首次下单后的邀请分佣
     * @param userId 用户ID
     * @param memberId 会员ID
     * @param orderAmount 订单金额
     */
    void processFirstOrderCommission(Long userId, Long memberId, java.math.BigDecimal orderAmount);

    /**
     * 批量处理历史账号：为没有邀请码的用户生成邀请码，为所有用户创建分佣账户
     * @return 处理结果统计
     */
    String processHistoricalAccounts();

    /**
     * 为用户创建分佣账户
     * @param userId 用户ID
     */
    void createCommissionAccountForUser(Long userId);
}
