package com.ssy.lingxi.member.controller.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberOrderCommentFeign;
import com.ssy.lingxi.member.api.model.req.MemberOrderCommentReportReq;
import com.ssy.lingxi.member.api.model.req.MemberOrderCommentReq;
import com.ssy.lingxi.member.service.feign.IMemberOrderCommentFeignService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员订单评价feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/3
 * @ignore 不需要提交到Yapi
 */
@RestController
public class MemberOrderCommentFeignController implements IMemberOrderCommentFeign {

    @Resource
    private IMemberOrderCommentFeignService memberOrderCommentFeignService;

    /**
     * 保存订单数据
     * @param memberOrderCommentReq 接口参数
     * @return 返回结果
     */
    @Override
    public WrapperResp<Void> saveMemberOrderComment(@RequestBody @Valid MemberOrderCommentReq memberOrderCommentReq) {
        memberOrderCommentFeignService.saveMemberOrderComment(memberOrderCommentReq);
        return WrapperUtil.success();
    }

    /**
     * 根据订单id集合查询待评价订单
     * @param reportVo 订单id集合
     * @return 返货结果
     */
    @Override
    public WrapperResp<Integer> findWaitCommentOrder(@RequestBody @Valid MemberOrderCommentReportReq reportVo) {
        return WrapperUtil.success(memberOrderCommentFeignService.findWailtCommentByOrderIdList(reportVo.getOrderListId(), reportVo.getType(), reportVo.getLoginUser()));
    }
}
