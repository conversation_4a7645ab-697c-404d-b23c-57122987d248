package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.model.resp.basic.SubMemberDetailResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;

import java.time.format.DateTimeFormatter;

/**
 * (内部)会员详细信息查询服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-07
 */
public interface IBaseMemberDetailService {

    /**
     * 查询下级会员详细信息
     * @param relationDO 会员关系
     * @return 查询结果
     */
    SubMemberDetailResp getSubMemberDetail(MemberRelationDO relationDO, Integer roleTag);

    /**
     * 查询会员详情 - 会员等级信息
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    MemberValidateDetailLevelResp getMemberDetailLevel(MemberRelationDO relationDO);

    /**
     * 会员详情 - 分页查询会员等级历史记录
     * @param relationDO 会员上下级关系
     * @param current 当前分页
     * @param pageSize 每页行数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(MemberRelationDO relationDO, int current, int pageSize, DateTimeFormatter formatter);

    /**
     * 会员详情 - 会员权益信息
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    MemberDetailRightResp getMemberDetailRight(MemberRelationDO relationDO);

    /**
     * 会员详情 - 分页查询会员权益获取记录
     * @param relationDO 会员上下级关系
     * @param current 当前分页
     * @param pageSize 每页行数
     * @param formatter 时间日期格式
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(MemberRelationDO relationDO, int current, int pageSize, DateTimeFormatter formatter);

    /**
     * 会员详情 - 分页查询会员权益使用记录
     * @param relationDO 会员上下级关系
     * @param current 当前分页
     * @param pageSize 每页行数
     * @param formatter 日期时间格式
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(MemberRelationDO relationDO, int current, int pageSize, DateTimeFormatter formatter);

    /**
     * 会员详情 - 会员信用信息
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    MemberDetailCreditResp getMemberDetailCredit(MemberRelationDO relationDO);

    /**
     * 会员详情 - 会员信用信息(平台层面汇总)
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @return 查询结果
     */
    WrapperResp<MemberDetailCreditResp> getAllMemberDetailCredit(Long subMemberId, Long subRoleId);

    /**
     * 会员详情 - 会员信用 - 交易评价汇总
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(MemberRelationDO relationDO);


    /**
     * 会员详情 - 会员信用 - 交易评价汇总（平台层面汇总）
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getAllMemberDetailCreditTradeCommentSummary(Long subMemberId, Long subRoleId);

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     * @param relationDO 会员上下级关系
     * @param starLevel 评论级别
     * @param current 当前分页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(MemberRelationDO relationDO, Integer starLevel, int current, int pageSize);

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录（平台层面汇总）
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param starLevel 评论级别
     * @param current 当前分页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditTradeHistoryResp> pageAllMemberDetailCreditTradeCommentHistory(Long subMemberId, Long subRoleId, Integer starLevel, int current, int pageSize);

    /**
     * 会员详情 - 会员信用 - 售后评价汇总
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(MemberRelationDO relationDO);

    /**
     * 会员详情 - 会员信用 - 售后评价汇总（平台层面汇总）
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getAllMemberDetailCreditAfterSaleCommentSummary(Long subMemberId, Long subRoleId);

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     * @param relationDO 会员上下级关系
     * @param starLevel 评论级别
     * @param current 当前分页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(MemberRelationDO relationDO, Integer starLevel, int current, int pageSize);

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录（平台层面汇总）
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param starLevel 评论级别
     * @param current 当前分页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageAllMemberDetailCreditAfterSaleCommentHistory(Long subMemberId, Long subRoleId, Integer starLevel, int current, int pageSize);

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     * @param relationDO 会员上下级关系
     * @return 查询结果
     */
    WrapperResp<MemberDetailCreditComplainSummaryResp> getMemberDetailCreditComplainSummary(MemberRelationDO relationDO);

    /**
     * 会员详情 - 会员信用 - 投诉汇总（平台层面汇总）
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @return 查询结果
     */
    MemberDetailCreditComplainSummaryResp getAllMemberDetailCreditComplainSummary(Long subMemberId, Long subRoleId);


    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     * @param relationDO 会员上下级关系
     * @param current 当前分页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    WrapperResp<PageDataResp<MemberDetailCreditComplainHistoryResp>> pageMemberDetailCreditComplainHistory(MemberRelationDO relationDO, int current, int pageSize);

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录(平台层面汇总)
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param current 当前分页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditComplainHistoryResp> pageAllMemberDetailCreditComplainHistory(Long subMemberId, Long subRoleId, int current, int pageSize);

    /**
     * 分页查询会员组织机构
     * @param memberId 会员Id
     * @param code 组织机构编码
     * @param title 组织机构名称
     * @param current 当前分页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberOrganizationQueryResp> pageMemberOrganizations(Long memberId, String code, String title, int current, int pageSize);
}
