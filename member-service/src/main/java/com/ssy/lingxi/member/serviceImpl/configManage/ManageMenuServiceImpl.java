package com.ssy.lingxi.member.serviceImpl.configManage;

import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.commodity.api.feign.ILanguageFeign;
import com.ssy.lingxi.commodity.api.model.resp.support.LanguageInfoResp;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.TreeUtils;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.idGenerate.IIdGenerate;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.req.TranslateReq;
import com.ssy.lingxi.component.base.model.resp.TranslateResp;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.entity.do_.menuAuth.*;
import com.ssy.lingxi.member.model.req.configManage.*;
import com.ssy.lingxi.member.model.req.platform.MenuSourceReq;
import com.ssy.lingxi.member.model.resp.configManage.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.configManage.IManageMenuService;
import groovy.lang.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务平台 - 菜单服务接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-15
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ManageMenuServiceImpl implements IManageMenuService {
    private final MenuRepository menuRepository;
    private final MenuButtonRepository buttonRepository;
    private final MenuNameRepository menuNameRepository;
    private final ButtonNameRepository buttonNameRepository;
    private final MenuButtonRepository menuButtonRepository;
    private final IIdGenerate idGenerate;
    private final UserRepository userRepository;
    private final JPAQueryFactory jpaQueryFactory;
    private final MemberRoleRepository memberRoleRepository;
    private final UserRoleRepository userRoleRepository;
    private final IBaseMemberCacheService memberCacheService;

    @Resource
    private ILanguageFeign languageFeign;

    @Override
    public List<MenuConfigResp> getMenuConfigList(MenuSourceReq menuSourceReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查询全部菜单
        List<MenuDO> menuDOList = menuRepository.findBySource(menuSourceReq.getSource());

        // 查询菜单名称
        Map<Long, List<MenuNameDO>> menuNameDOMap = menuNameRepository.findBySource(menuSourceReq.getSource()).stream().collect(Collectors.groupingBy(MenuNameDO::getMenuId));

        // 查询系统语言列表
        Map<String, Integer> languageInfoMap = getLanguageInfo();

        // 转换menuDO为MenuConfigResp
        List<MenuConfigResp> menuConfigRespList = menuDOList.stream().map(menuDO -> {
            MenuConfigResp menuConfigResp = new MenuConfigResp();
            menuConfigResp.setId(menuDO.getId());
            menuConfigResp.setParentId(menuDO.getParentId());
            menuConfigResp.setMenuNameList(getMenuNameList(menuDO.getId(), menuNameDOMap, languageInfoMap));
            menuConfigResp.setCode(menuDO.getCode());
            menuConfigResp.setPath(menuDO.getPath());
            menuConfigResp.setSort(menuDO.getSort());
            menuConfigResp.setSource(menuDO.getSource());
            menuConfigResp.setConfig(menuDO.getConfig());
            return menuConfigResp;
        }).collect(Collectors.toList());

        return TreeUtils.transferToTree(menuConfigRespList,
                        0L,
                        MenuConfigResp::getId,
                        MenuConfigResp::getParentId,
                        MenuConfigResp::setChildren,
                        Comparator.comparing(MenuConfigResp::getSort));
    }

    @Override
    public MenuConfigDetailsResp getMenuConfigDetails(CommonIdReq commonIdReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查询菜单
        MenuDO menuDO = menuRepository.findById(commonIdReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MENU_DOES_NOT_EXIST));

        // 组装该菜单+按钮的返回信息
        MenuConfigDetailsResp menuConfigDetailsResp = new MenuConfigDetailsResp();
        menuConfigDetailsResp.setId(menuDO.getId());
        menuConfigDetailsResp.setCode(menuDO.getCode());
        menuConfigDetailsResp.setPath(menuDO.getPath());
        menuConfigDetailsResp.setParentId(menuDO.getParentId());
        menuConfigDetailsResp.setSort(menuDO.getSort());
        menuConfigDetailsResp.setSource(menuDO.getSource());
        menuConfigDetailsResp.setConfig(menuDO.getConfig());

        // 查询系统语言列表
        Map<String, Integer> languageInfoMap = getLanguageInfo();

        // 菜单多语言名称
        menuConfigDetailsResp.setMenuNameList(getMenuNameList(menuDO.getId(), languageInfoMap));

        // 按钮信息
        Map<Long, List<ButtonNameDO>> buttonNameMap = buttonNameRepository.findAllByMenuId(menuDO.getId()).stream().collect(Collectors.groupingBy(ButtonNameDO::getButtonId));

        menuConfigDetailsResp.setButtons(Optional.ofNullable(menuDO.getButtons())
                .map(buttonDOList -> buttonDOList.stream()
                        .map(menuButtonDO -> {
                            ButtonConfigResp buttonConfigResp = new ButtonConfigResp();
                            buttonConfigResp.setId(menuButtonDO.getId());
                            buttonConfigResp.setButtonNameList(getButtonNameList(menuButtonDO.getId(), buttonNameMap, languageInfoMap));
                            buttonConfigResp.setPath(menuButtonDO.getPath());
                            buttonConfigResp.setCode(menuButtonDO.getCode());
                            buttonConfigResp.setConfig(menuButtonDO.getConfig());
                            return buttonConfigResp;
                        }).collect(Collectors.toList()))
                .orElse(new ArrayList<>()));

        return menuConfigDetailsResp;
    }

    /**
     * 新增菜单
     *
     * @param addMenuReq 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public MenuButtonResp addMenu(MenuButtonAddReq addMenuReq) {
        memberCacheService.needLoginFromManagePlatform();

        // path同端不允许重复
        BusinessAssertUtil.isFalse(menuRepository.existsByPathAndSource(addMenuReq.getPath(), addMenuReq.getSource()), ResponseCodeEnum.MC_MS_MENU_PATH_EXISTS);

        // 查找上级菜单
        MenuDO parentMenuDO = null;
        if (NumberUtil.notNullOrZero(addMenuReq.getParentId())) {
            parentMenuDO = menuRepository.findById(addMenuReq.getParentId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_PARENT_MENU_NOT_EXISTS));
        }

        // 名称同级下不能重复
        List<TranslateReq> menuNameReqList = addMenuReq.getMenuNameList();
        for (TranslateReq translateReq : menuNameReqList) {
            BusinessAssertUtil.isFalse(menuNameRepository.existsByLanguageAndNameAndParentMenuIdAndSource(translateReq.getLanguage(), translateReq.getValue(), parentMenuDO == null ? 0L : parentMenuDO.getId(), addMenuReq.getSource()), ResponseCodeEnum.MC_MS_DUPLICATE_NAMES_ARE_NOT_ALLOWED_UNDER_THE_SAME_LEVEL);
        }

        // 新增菜单
        MenuDO menuDO = new MenuDO();
        menuDO.setParentId(parentMenuDO == null ? 0L : parentMenuDO.getId());
        menuDO.setPath(addMenuReq.getPath());
        menuDO.setCode(idGenerate.getId());
        menuDO.setSort(getMaxSort(parentMenuDO) + 1);
        menuDO.setSource(addMenuReq.getSource());
        menuDO.setDataAuthConfig(0);
        menuDO.setConfig(addMenuReq.getConfig() == null ? new HashMap<>() : addMenuReq.getConfig());
        menuDO.setLevel(parentMenuDO == null ? 1 : parentMenuDO.getLevel() + 1);
        menuRepository.save(menuDO);

        // 保存菜单多语言翻译
        List<MenuNameDO> menuNameDOList = menuNameReqList.stream()
                .map(translateReq -> {
                    MenuNameDO menuNameDO = new MenuNameDO();
                    menuNameDO.setMenuId(menuDO.getId());
                    menuNameDO.setParentMenuId(addMenuReq.getParentId());
                    menuNameDO.setSource(menuDO.getSource());
                    menuNameDO.setLanguage(translateReq.getLanguage());
                    menuNameDO.setName(translateReq.getValue());
                    return menuNameDO;
                }).collect(Collectors.toList());
        menuNameRepository.saveAll(menuNameDOList);

        // 返回菜单
        return getMenuButtonResp(menuDO);
    }

    /**
     * 修改菜单
     *
     * @param updateReq 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public MenuButtonResp updateMenu(MenuButtonUpdateReq updateReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查找要修改的菜单
        MenuDO menuDO = menuRepository.findById(updateReq.getMenuId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_PARENT_MENU_NOT_EXISTS));

        // 查找上级菜单
        MenuDO parentMenuDO = null;
        if (NumberUtil.notNullOrZero(updateReq.getParentId())) {
            parentMenuDO = menuRepository.findById(updateReq.getParentId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_PARENT_MENU_NOT_EXISTS));
        }

        // 不允许更换父菜单
        BusinessAssertUtil.isTrue(Objects.equals(menuDO.getParentId(), updateReq.getParentId()), ResponseCodeEnum.MC_MS_REPLACING_THE_PARENT_MENU_IS_NOT_ALLOWED);

        // path同端不允许重复
        BusinessAssertUtil.isFalse(menuRepository.existsByPathAndSourceAndIdNot(updateReq.getPath(), updateReq.getSource(), menuDO.getId()), ResponseCodeEnum.MC_MS_MENU_PATH_EXISTS);

        // 名称同级下不能重复
        List<TranslateReq> menuNameReqList = updateReq.getMenuNameList();
        for (TranslateReq translateReq : menuNameReqList) {
            BusinessAssertUtil.isFalse(
                    menuNameRepository.existsByLanguageAndNameAndParentMenuIdAndMenuIdNotAndSource(
                            translateReq.getLanguage(),
                            translateReq.getValue(),
                            parentMenuDO == null ? 0L : parentMenuDO.getId(),
                            menuDO.getId(),
                            updateReq.getSource()),
                    ResponseCodeEnum.MC_MS_DUPLICATE_NAMES_ARE_NOT_ALLOWED_UNDER_THE_SAME_LEVEL);
        }

        // 更新菜单信息
        menuDO.setPath(updateReq.getPath());
        menuDO.setConfig(updateReq.getConfig() == null ? new HashMap<>() : updateReq.getConfig());
        menuRepository.saveAndFlush(menuDO);

        // 保存菜单多语言翻译(先删除再增加)
        menuNameRepository.deleteByMenuId(menuDO.getId());
        List<MenuNameDO> menuNameDOList = menuNameReqList.stream()
                .map(translateReq -> {
                    MenuNameDO menuNameDO = new MenuNameDO();
                    menuNameDO.setMenuId(menuDO.getId());
                    menuNameDO.setParentMenuId(updateReq.getParentId());
                    menuNameDO.setSource(menuDO.getSource());
                    menuNameDO.setLanguage(translateReq.getLanguage());
                    menuNameDO.setName(translateReq.getValue());
                    return menuNameDO;
                }).collect(Collectors.toList());
        menuNameRepository.saveAll(menuNameDOList);

        return getMenuButtonResp(menuDO);
    }

    /**
     * 删除菜单
     *
     * @param commonIdReq 接口参数
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMenu(CommonIdReq commonIdReq) {
        memberCacheService.needLoginFromManagePlatform();

        MenuDO menuDO = menuRepository.findById(commonIdReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MENU_DOES_NOT_EXIST));

        // 删除当前菜单，并递归删除该菜单下的所有子菜单，所有相应按钮也要删除
        Tuple2<Set<Long>, Set<Long>> authTuple = recursionDeleteMenu(menuDO);

        // 删除菜单关联的多语言名称
        menuNameRepository.deleteAllByMenuIdIn(authTuple.getFirst());

        // 删除按钮关联的多语言名称
        buttonNameRepository.deleteAllByMenuIdIn(authTuple.getFirst());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MenuButtonResp addButton(ButtonAddReq addButtonReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查找菜单
        MenuDO menuDO = menuRepository.findById(addButtonReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MENU_DOES_NOT_EXIST));

        // 按钮path同级不能重复
        BusinessAssertUtil.isFalse(buttonRepository.existsByPathAndMenuId(addButtonReq.getPath(), menuDO.getId()), ResponseCodeEnum.MC_MS_DUPLICATE_PATHS_ARE_NOT_ALLOWED_UNDER_THE_SAME_LEVEL);

        // 按钮名称同级不能重复
        List<TranslateReq> buttonNameReqList = addButtonReq.getButtonNameList();
        for (TranslateReq translateReq : buttonNameReqList) {
            BusinessAssertUtil.isFalse(buttonNameRepository.existsByLanguageAndNameAndMenuId(translateReq.getLanguage(), translateReq.getValue(), menuDO.getId()), ResponseCodeEnum.MC_MS_DUPLICATE_NAMES_ARE_NOT_ALLOWED_UNDER_THE_SAME_LEVEL);
        }

        // 新增按钮
        ButtonDO buttonDO = new ButtonDO();
        buttonDO.setCode(idGenerate.getId());
        buttonDO.setPath(addButtonReq.getPath());
        buttonDO.setConfig(addButtonReq.getConfig());
        buttonDO.setMenu(menuDO);
        menuButtonRepository.save(buttonDO);

        // 保存按钮多语言翻译
        List<ButtonNameDO> buttonNameDOList = buttonNameReqList.stream()
                .map(translateReq -> {
                    ButtonNameDO buttonNameDO = new ButtonNameDO();
                    buttonNameDO.setButtonId(buttonDO.getId());
                    buttonNameDO.setMenuId(menuDO.getId());
                    buttonNameDO.setLanguage(translateReq.getLanguage());
                    buttonNameDO.setName(translateReq.getValue());
                    return buttonNameDO;
                }).collect(Collectors.toList());
        buttonNameRepository.saveAll(buttonNameDOList);

        return getMenuButtonResp(menuDO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MenuButtonResp updateButton(ButtonUpdateReq updateReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查找按钮是否存在
        ButtonDO buttonDO = buttonRepository.findById(updateReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_BUTTON_DOES_NOT_EXIST));

        // 校验菜单是否存在
        MenuDO menuDO = buttonDO.getMenu();
        BusinessAssertUtil.notNull(menuDO, ResponseCodeEnum.MC_MS_MENU_DOES_NOT_EXIST);

        // 按钮path同级不能重复
        BusinessAssertUtil.isFalse(buttonRepository.existsByPathAndMenuIdAndIdNot(updateReq.getPath(), menuDO.getId(), buttonDO.getId()), ResponseCodeEnum.MC_MS_DUPLICATE_PATHS_ARE_NOT_ALLOWED_UNDER_THE_SAME_LEVEL);

        // 按钮名称同级不能重复
        List<TranslateReq> buttonNameReqList = updateReq.getButtonNameList();
        for (TranslateReq translateReq : buttonNameReqList) {
            BusinessAssertUtil.isFalse(buttonNameRepository.existsByLanguageAndNameAndMenuIdAndButtonIdNot(translateReq.getLanguage(), translateReq.getValue(), menuDO.getId(), buttonDO.getId()), ResponseCodeEnum.MC_MS_DUPLICATE_NAMES_ARE_NOT_ALLOWED_UNDER_THE_SAME_LEVEL);
        }

        // 更新按钮
        buttonDO.setPath(updateReq.getPath());
        buttonDO.setConfig(updateReq.getConfig());
        menuButtonRepository.save(buttonDO);

        // 保存按钮多语言翻译(先删除再增加)
        buttonNameRepository.deleteByButtonId(buttonDO.getId());
        List<ButtonNameDO> buttonNameDOList = buttonNameReqList.stream()
                .map(translateReq -> {
                    ButtonNameDO buttonNameDO = new ButtonNameDO();
                    buttonNameDO.setButtonId(buttonDO.getId());
                    buttonNameDO.setMenuId(menuDO.getId());
                    buttonNameDO.setLanguage(translateReq.getLanguage());
                    buttonNameDO.setName(translateReq.getValue());
                    return buttonNameDO;
                }).collect(Collectors.toList());
        buttonNameRepository.saveAll(buttonNameDOList);

        return getMenuButtonResp(menuDO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteButton(CommonIdReq commonIdReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查找按钮是否存在
        ButtonDO buttonDO = buttonRepository.findById(commonIdReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_BUTTON_DOES_NOT_EXIST));

        // 删除按钮
        buttonNameRepository.deleteByButtonId(buttonDO.getId());
        buttonRepository.deleteById(buttonDO.getId());
    }

    private Tuple2<Set<Long>, Set<Long>> recursionDeleteMenu(MenuDO menuDO) {
        // 查找所有子菜单
        List<MenuDO> childMenuDOList = menuRepository.findAllByParentId(menuDO.getId());

        // 菜单按钮元祖，first是菜单，second是按钮
        Tuple2<Set<Long>, Set<Long>> authTuple = new Tuple2<>(new HashSet<>(), new HashSet<>());

        // 递归删除每一个子菜单
        for (MenuDO childMenuDO : childMenuDOList) {
            Tuple2<Set<Long>, Set<Long>> childAuthTuple = recursionDeleteMenu(childMenuDO);
            authTuple.getFirst().addAll(childAuthTuple.getFirst());
            authTuple.getSecond().addAll(childAuthTuple.getSecond());
        }

        // 删除当前根菜单（菜单删除会级联删除该菜单对应的按钮）
        menuRepository.delete(menuDO);

        // 存入当前菜单的菜单和按钮id，后面要删除
        authTuple.getFirst().add(menuDO.getId());
        authTuple.getSecond().addAll(menuDO.getButtons().stream().map(ButtonDO::getId).collect(Collectors.toSet()));

        return authTuple;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void menuResort(MenuResortReq menuResortReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 如果父菜单不是0，则校验父菜单是否存在
        if (!menuResortReq.getParentId().equals(0L)) {
            BusinessAssertUtil.isTrue(menuRepository.existsById(menuResortReq.getParentId()), ResponseCodeEnum.MC_MS_MENU_DOES_NOT_EXIST);
        }

        // 查找所有父菜单的子菜单，并转成map<菜单id，菜单DO>
        Map<Long, MenuDO> childMenuDOMap = menuRepository.findAllByParentId(menuResortReq.getParentId()).stream().collect(Collectors.toMap(MenuDO::getId, Function.identity()));

        // 完全按照前端传入的顺序来重排菜单，对于那些没传入的菜单，不修改他们的排序
        // 如果前端传入的子菜单列表中存在不属于该父菜单的子菜单，这个是错误的，但在这不处理
        // 如果前端漏传了某子菜单的id，在这也不处理，排序值重复也没事
        List<Long> childIdListReq = menuResortReq.getChildIdList();
        for (int i = 0; i < childIdListReq.size(); i++) {
            Long childIdReq = childIdListReq.get(i);
            MenuDO menuDO = childMenuDOMap.get(childIdReq);
            if (Objects.nonNull(menuDO) && Objects.equals(menuDO.getId(), childIdReq)) {
                menuDO.setSort(i + 1);
                menuRepository.save(menuDO);
            }
        }


    }

    /**
     * 获取父菜单下的最大排序值，如果是第一个菜单，则返回0
     */
    private Integer getMaxSort(MenuDO parentMenu) {
        QMenuDO qMenuDO = QMenuDO.menuDO;
        return Optional.ofNullable(
                        jpaQueryFactory.select(qMenuDO.sort.max())
                                .from(qMenuDO)
                                .where(qMenuDO.parentId.eq(Optional.ofNullable(parentMenu).map(MenuDO::getId).orElse(0L)))
                                .fetchFirst())
                .orElse(0);
    }

    /**
     * 返回菜单按钮信息
     */
    private MenuButtonResp getMenuButtonResp(MenuDO menuDO) {
        // 查询系统语言列表
        Map<String, Integer> languageInfoMap = getLanguageInfo();

        MenuButtonResp menuButtonResp = new MenuButtonResp();
        menuButtonResp.setId(menuDO.getId());
        menuButtonResp.setParentId(menuDO.getParentId());
        menuButtonResp.setMenuNameList(getMenuNameList(menuDO.getId(), languageInfoMap));
        menuButtonResp.setPath(menuDO.getPath());
        menuButtonResp.setCode(menuDO.getCode());
        menuButtonResp.setSource(menuDO.getSource());
        menuButtonResp.setSort(menuDO.getSort());
        menuButtonResp.setLevel(menuDO.getLevel());
        menuButtonResp.setDataAuthConfig(menuDO.getDataAuthConfig());
        menuButtonResp.setConfig(menuDO.getConfig());

        // 查找按钮信息
        Map<Long, List<ButtonNameDO>> buttonNameMap = buttonNameRepository.findAllByMenuId(menuDO.getId()).stream().collect(Collectors.groupingBy(ButtonNameDO::getButtonId));

        // 设置按钮信息
        menuButtonResp.setButtons(menuButtonRepository.findAllByMenu(menuDO)
                .stream()
                .map(buttonDO -> {
                    ButtonResp buttonResp = new ButtonResp();
                    buttonResp.setId(buttonDO.getId());
                    buttonResp.setCode(buttonDO.getCode());
                    buttonResp.setButtonNameList(getButtonNameList(buttonDO.getId(), buttonNameMap, languageInfoMap));
                    buttonResp.setPath(buttonDO.getPath());
                    buttonResp.setConfig(buttonDO.getConfig());
                    return buttonResp;
                }).collect(Collectors.toSet()));
        return menuButtonResp;
    }

    private Map<String, Integer> getLanguageInfo() {
        // 查询系统语言列表
        return WrapperUtil.ofNullable(languageFeign.getLanguageList())
                .tryThrowWhenDataIsEmpty()
                .getData()
                .stream()
                .collect(Collectors.toMap(LanguageInfoResp::getValue, LanguageInfoResp::getSort));
    }

    private String getMenuName(Long menuId, Map<Long, List<MenuNameDO>> menuNameDOMap, String language) {
        return menuNameDOMap.getOrDefault(menuId, Collections.emptyList())
                .stream()
                .filter(menuNameDO -> menuNameDO.getLanguage().equals(language))
                .map(MenuNameDO::getName)
                .findAny()
                .orElse("");
    }

    private List<TranslateResp> getMenuNameList(Long menuId, Map<String, Integer> languageInfoMap) {
        return menuNameRepository.findByMenuId(menuId)
                .stream()
                .map(menuNameDO -> new TranslateResp(menuNameDO.getLanguage(), menuNameDO.getName()))
                .sorted(Comparator.comparing(translateResp -> languageInfoMap.getOrDefault(translateResp.getLanguage(), Integer.MAX_VALUE)))
                .collect(Collectors.toList());
    }

    private List<TranslateResp> getMenuNameList(Long menuId, Map<Long, List<MenuNameDO>> menuNameDOMap, Map<String, Integer> languageInfoMap) {
        return menuNameDOMap.getOrDefault(menuId, Collections.emptyList())
                .stream()
                .map(menuNameDO -> new TranslateResp(menuNameDO.getLanguage(), menuNameDO.getName()))
                .sorted(Comparator.comparing(translateResp -> languageInfoMap.getOrDefault(translateResp.getLanguage(), Integer.MAX_VALUE)))
                .collect(Collectors.toList());
    }

    private List<TranslateResp> getButtonNameList(Long buttonId, Map<Long, List<ButtonNameDO>> buttonNameMap, Map<String, Integer> languageInfoMap) {
        return buttonNameMap.getOrDefault(buttonId, Collections.emptyList())
                .stream()
                .map(buttonNameDO -> new TranslateResp(buttonNameDO.getLanguage(), buttonNameDO.getName()))
                .sorted(Comparator.comparing(translateResp -> languageInfoMap.getOrDefault(translateResp.getLanguage(), Integer.MAX_VALUE)))
                .collect(Collectors.toList());
    }
}
