package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.MemberScoringIndicatorDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 评分模板Repository
 * <AUTHOR>
 * @since 2022/6/26 21:01
 * @version 1.0
 */
@Repository
public interface MemberScoringIndicatorRepository extends JpaRepository<MemberScoringIndicatorDO, Long>, JpaSpecificationExecutor<MemberScoringIndicatorDO> {

    List<MemberScoringIndicatorDO> findAllByMemberIdAndRoleIdAndRoleTag(Long memberId, Long memberRoleId, Integer roleTag);

    void deleteByIdIn(List<Long> delIds);
}
