package com.ssy.lingxi.member.controller.web.commission;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.commission.CommissionWithdrawalQueryReq;
import com.ssy.lingxi.member.model.req.commission.WithdrawalManagementQueryReq;
import com.ssy.lingxi.member.model.req.commission.ApplyWithdrawalReq;
import com.ssy.lingxi.member.model.req.commission.ApproveWithdrawalReq;
import com.ssy.lingxi.member.model.req.commission.ProcessPaymentReq;
import com.ssy.lingxi.member.model.resp.commission.CommissionWithdrawalResp;
import com.ssy.lingxi.member.model.resp.commission.WithdrawalManagementResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.commission.ICommissionWithdrawalService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;


/**
 * 会员能力 - 佣金提现管理
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/commission/withdrawal")
public class CommissionWithdrawalController {

    @Resource
    private ICommissionWithdrawalService commissionWithdrawalService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    // ==================================佣金提现记录===============================

    /**
     * 分页查询佣金提现记录列表
     *
     * @param headers Http头部信息
     * @param request 查询请求
     * @return 分页结果
     */
    @PostMapping("/getCommissionWithdrawalPage")
    public WrapperResp<PageDataResp<CommissionWithdrawalResp>> getCommissionWithdrawalPage(@RequestHeader HttpHeaders headers,
                                                                                           @RequestBody @Valid CommissionWithdrawalQueryReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        PageDataResp<CommissionWithdrawalResp> result = commissionWithdrawalService.getCommissionWithdrawalPage(loginUser, request);
        return WrapperUtil.success(result);
    }

    // ==================================提现管理===============================

    /**
     * 分页查询提现管理列表
     *
     * @param headers Http头部信息
     * @param request 查询请求
     * @return 分页结果
     */
    @PostMapping("/getWithdrawalManagementPage")
    public WrapperResp<PageDataResp<WithdrawalManagementResp>> getWithdrawalManagementPage(@RequestHeader HttpHeaders headers,
                                                                                           @RequestBody @Valid WithdrawalManagementQueryReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        PageDataResp<WithdrawalManagementResp> result = commissionWithdrawalService.getWithdrawalManagementPage(loginUser, request);
        return WrapperUtil.success(result);
    }

    /**
     * 导出提现管理列表
     *
     * @param headers  Http头部信息
     * @param request  查询请求
     * @param response HTTP响应
     */
    @GetMapping("/export")
    public void exportWithdrawalManagement(@RequestHeader HttpHeaders headers,
                                           WithdrawalManagementQueryReq request,
                                           HttpServletResponse response) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionWithdrawalService.exportWithdrawalManagement(loginUser, request, response);
    }

    // ==================================用户申请提现===============================

    /**
     * 用户申请提现
     *
     * @param headers Http头部信息
     * @param request 申请提现请求
     * @return 操作结果
     */
    @PostMapping("/apply")
    public WrapperResp<Void> applyWithdrawal(@RequestHeader HttpHeaders headers,
                                             @RequestBody @Valid ApplyWithdrawalReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionWithdrawalService.applyWithdrawal(loginUser, request.getBankCardId(), request.getWithdrawalAmount());
        return WrapperUtil.success();
    }

    // ==================================提现审核和打款===============================

    /**
     * 审核提现申请
     *
     * @param headers Http头部信息
     * @param request 审核提现申请请求
     * @return 操作结果
     */
    @PostMapping("/approve")
    public WrapperResp<Void> approveWithdrawal(@RequestHeader HttpHeaders headers,
                                               @RequestBody @Valid ApproveWithdrawalReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionWithdrawalService.approveWithdrawal(loginUser, request.getWithdrawalId(), request.getApproved(), request.getRemark());
        return WrapperUtil.success();
    }

    /**
     * 财务打款
     *
     * @param headers Http头部信息
     * @param request 财务打款请求
     * @return 操作结果
     */
    @PostMapping("/payment")
    public WrapperResp<Void> processPayment(@RequestHeader HttpHeaders headers,
                                            @RequestBody @Valid ProcessPaymentReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionWithdrawalService.processPayment(loginUser, request.getWithdrawalId(), request.getSuccess(), request.getRemark());
        return WrapperUtil.success();
    }
}
