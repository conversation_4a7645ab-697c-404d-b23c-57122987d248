package com.ssy.lingxi.member.serviceImpl.web;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.CollectionPageUtil;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.enums.MemberLevelRuleTypeEnum;
import com.ssy.lingxi.member.api.enums.MemberRightTypeEnum;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleRuleDO;
import com.ssy.lingxi.member.entity.do_.basic.QMemberRoleDO;
import com.ssy.lingxi.member.entity.do_.levelRight.*;
import com.ssy.lingxi.member.model.req.lrc.*;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.RoleQueryResp;
import com.ssy.lingxi.member.model.resp.lrc.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseSiteService;
import com.ssy.lingxi.member.service.web.IMemberLevelConfigService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员等级配置服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-21
 */
@Service
public class MemberLevelConfigServiceImpl implements IMemberLevelConfigService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberRoleRepository roleRepository;

    @Resource
    private MemberLevelRuleConfigRepository memberLevelRuleConfigRepository;

    @Resource
    private MemberLevelConfigRepository memberLevelConfigRepository;

    @Resource
    private BaseRightConfigRepository baseRightConfigRepository;

    @Resource
    private MemberRightConfigRepository memberRightConfigRepository;

    @Resource
    private RoleRuleRepository roleRuleRepository;

    @Resource
    private MemberLevelRightRepository memberLevelRightRepository;

    @Resource
    private IBaseSiteService baseSiteService;

    @Resource
    private BaseLevelRuleRepository baseLevelRuleRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    /**
     * 分页查询会员升级规则
     *
     * @param headers HttpHeaders信息
     * @param pageDataReq  接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberLevelRuleConfigQueryResp> pageMemberLevelRuleConfig(HttpHeaders headers, PageDataReq pageDataReq) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);


        Specification<MemberLevelRuleConfigDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if (!SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode().equals(loginUser.getLoginSource())) {
                Join<MemberLevelRuleConfigDO, BaseLevelRuleDO> ruleJoin = root.join("rule", JoinType.LEFT);
                list.add(criteriaBuilder.notEqual(ruleJoin.get("ruleTypeEnum").as(Integer.class), MemberLevelRuleTypeEnum.BY_LOGIN.getCode()));
            }
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize(), Sort.by("id").ascending());
        Page<MemberLevelRuleConfigDO> result = memberLevelRuleConfigRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(c -> {
            MemberLevelRuleConfigQueryResp configVO = new MemberLevelRuleConfigQueryResp();
            configVO.setId(c.getId());
            configVO.setRuleName(MemberLevelRuleTypeEnum.getCodeMsg(c.getRule().getRuleTypeEnum()));
            configVO.setRuleTypeEnum(c.getRule().getRuleTypeEnum());
            if (c.getRule().getRuleTypeEnum().equals(MemberLevelRuleTypeEnum.BY_TRADE.getCode())) {
                //交易规则按小数保存，前端页面按百分比显示，所以要乘以100返回给前端
                configVO.setScore(c.getScore().multiply(new BigDecimal(100)).intValue());
            } else {
                configVO.setScore(c.getScore().intValue());
            }
            configVO.setLevelTypeName(MemberLevelTypeEnum.getCodeMsg(c.getRule().getMemberLevelTypeEnum()));
            configVO.setRemark(MemberLevelRuleTypeEnum.getRemark(c.getRule().getRuleTypeEnum()));
            return configVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 修改会员升级规则可获取的分值
     *
     * @param headers HttpHeaders信息
     * @param ruleVO  接口参数
     * @return 操作结果
     */
    @Override
    public void updateMemberLevelRuleConfigScore(HttpHeaders headers, MemberLevelRuleConfigUpdateScoreReq ruleVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        List<MemberLevelRuleConfigDO> configDOList = memberLevelRuleConfigRepository.findByMemberIdAndRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId());
        List<MemberLevelRuleConfigDO> saveList = new ArrayList<>();
        for (MemberLevelRuleConfigUpdateScoreReq.ScoreItem item : ruleVO.getItems()) {
            if (item.getId() == null || item.getId() <= 0) {

                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED);
            }

            MemberLevelRuleConfigDO configDO = configDOList.stream().filter(c -> c.getId().equals(item.getId())).findFirst().orElse(null);
            if (configDO == null) {

                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_RULE_DOES_NOT_EXIST);
            }

            if (item.getScore() == null || item.getScore().compareTo(new BigDecimal(0)) < 0) {

                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_RULE_PARAMETER_MUST_BE_POSITIVE_OR_ZERO);
            }

            if (configDO.getRule().getRuleTypeEnum().equals(MemberLevelRuleTypeEnum.BY_TRADE.getCode())) {
                if (item.getScore().compareTo(new BigDecimal(100)) > 0) {

                    throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_TRADE_LEVEL_RULE_MUST_LESSTHAN_OR_EQUAL_100);
                }
                configDO.setScore(item.getScore().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
            } else {
                configDO.setScore(item.getScore());
            }

            saveList.add(configDO);
        }

        if (!CollectionUtils.isEmpty(saveList)) {
            memberLevelRuleConfigRepository.saveAll(saveList);
        }


    }

    /**
     * 分页查询会员等级配置
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public PageDataResp<MemberLevelQueryResp> pageMemberLevels(HttpHeaders headers, MemberLevelPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        QMemberLevelConfigDO qMemberLevelConfig = QMemberLevelConfigDO.memberLevelConfigDO;
        QMemberRoleDO qMemberRoleDO= QMemberRoleDO.memberRoleDO;

        JPAQuery<MemberLevelQueryResp> query = jpaQueryFactory.select(Projections.constructor(MemberLevelQueryResp.class, qMemberLevelConfig.id, qMemberLevelConfig.level, qMemberLevelConfig.levelTag, qMemberLevelConfig.levelType, qMemberLevelConfig.scoreTag, qMemberLevelConfig.subRoleId, qMemberRoleDO.roleName, qMemberRoleDO.roleType, qMemberRoleDO.memberType, qMemberLevelConfig.point, qMemberLevelConfig.status))
                .from(qMemberLevelConfig)
                .leftJoin(qMemberRoleDO).on(qMemberLevelConfig.subRoleId.eq(qMemberRoleDO.id))
                .where(qMemberLevelConfig.memberId.eq(loginUser.getMemberId()).and(qMemberLevelConfig.roleId.eq(loginUser.getMemberRoleId())))
                .orderBy(qMemberLevelConfig.id.desc())
                .limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        if(StringUtils.hasText(pageVO.getLevelTag())) {
            query.where(qMemberLevelConfig.levelTag.like("%" + pageVO.getLevelTag().trim() + "%"));
        }

        if(StringUtils.hasText(pageVO.getRoleName())) {
            query.where(qMemberRoleDO.roleName.like("%" + pageVO.getRoleName().trim() + "%"));
        }

        return new PageDataResp<>(query.fetchCount(), query.fetch());
    }

    @Override
    public PageDataResp<MemberLevelQueryResp> pageMemberLevelByConsumer(HttpHeaders headers, MemberLevelPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        QMemberLevelConfigDO qMemberLevelConfig = QMemberLevelConfigDO.memberLevelConfigDO;
        QMemberRoleDO qMemberRoleDO= QMemberRoleDO.memberRoleDO;

        JPAQuery<MemberLevelQueryResp> query = jpaQueryFactory.select(Projections.constructor(MemberLevelQueryResp.class, qMemberLevelConfig.id, qMemberLevelConfig.level, qMemberLevelConfig.levelTag, qMemberLevelConfig.levelType, qMemberLevelConfig.scoreTag, qMemberLevelConfig.subRoleId, qMemberRoleDO.roleName, qMemberRoleDO.roleType, qMemberRoleDO.memberType, qMemberLevelConfig.point, qMemberLevelConfig.status))
                .from(qMemberLevelConfig)
                .leftJoin(qMemberRoleDO).on(qMemberLevelConfig.subRoleId.eq(qMemberRoleDO.id))
                .where(qMemberLevelConfig.memberId.eq(loginUser.getMemberId()).and(qMemberLevelConfig.roleId.eq(loginUser.getMemberRoleId())))
                .where(qMemberLevelConfig.status.eq(EnableDisableStatusEnum.ENABLE.getCode()))
                .where(qMemberRoleDO.roleType.eq(RoleTypeEnum.SERVICE_CONSUMER.getCode()))
                .where(qMemberRoleDO.memberType.eq(MemberTypeEnum.MERCHANT.getCode()).or(qMemberRoleDO.memberType.eq(MemberTypeEnum.MERCHANT_PERSONAL.getCode())))
                .orderBy(qMemberLevelConfig.id.desc())
                .limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());

        if(StringUtils.hasText(pageVO.getLevelTag())) {
            query.where(qMemberLevelConfig.levelTag.like("%" + pageVO.getLevelTag().trim() + "%"));
        }

        if(StringUtils.hasText(pageVO.getRoleName())) {
            query.where(qMemberRoleDO.roleName.like("%" + pageVO.getRoleName().trim() + "%"));
        }

        return new PageDataResp<>(query.fetchCount(), query.fetch());
    }

    /**
     * 新增或修改会员等级时，查询等级类型列表
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public List<MemberLevelTypeResp> findMemberLevelTypes(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        List<MemberLevelTypeEnum> levelTypeEnums = loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode()) ? Stream.of(MemberLevelTypeEnum.PLATFORM).collect(Collectors.toList()) : Stream.of(MemberLevelTypeEnum.MERCHANT).collect(Collectors.toList());
        return levelTypeEnums.stream().map(levelType -> new MemberLevelTypeResp(levelType.getCode(), levelType.getName())).sorted(Comparator.comparingInt(MemberLevelTypeResp::getLevelType)).collect(Collectors.toList());
    }

    /**
     * 新增或修改会员等级时，分页查询会员角色列表
     *
     * @param headers Http头部信息
     * @param pageDataReq  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public PageDataResp<RoleQueryResp> pageRoles(HttpHeaders headers, PageDataReq pageDataReq, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        QMemberRoleDO qMemberRoleDO= QMemberRoleDO.memberRoleDO;

        long totalCount = 0L;
        List<RoleQueryResp> result = new ArrayList<>();

        if (loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode())) {
            JPAQuery<RoleQueryResp> query = jpaQueryFactory.select(Projections.constructor(RoleQueryResp.class, qMemberRoleDO.id, qMemberRoleDO.roleName, qMemberRoleDO.roleType, qMemberRoleDO.memberType))
                    .from(qMemberRoleDO)
                    .where(qMemberRoleDO.status.eq(EnableDisableStatusEnum.ENABLE.getCode()))
                    .where(qMemberRoleDO.relType.ne(MemberRelationTypeEnum.PLATFORM.getCode()));
            if (NumberUtil.notNullOrZero(roleTag)) {
                query.where(qMemberRoleDO.roleTag.eq(roleTag));
            }

            query.orderBy(qMemberRoleDO.id.asc())
                    .limit(pageDataReq.getPageSize()).offset(pageDataReq.getCurrentOffset());
            totalCount = query.fetchCount();
            result = query.fetch();
        } else {
            //1、如果【PAAS-站点管理】未勾选【SAAS多租户部署】且当前会员类型是企业会员或个人会员则会员角色数据从PAAS【会员角色权限设置--会员角色设置】中取会员类型为企业会员或个人会员且状态为有效的会员角色、角色类型，会员类型数据
            //2、如果【PAAS-站点管理】未勾选【SAAS多租户部署】且当前会员类型是渠道企业会员或渠道个人会员则会员角色数据从PAAS【会员角色权限设置--会员角色设置】中取会员类型为渠道企业会员或渠道个人会员且状态为有效的会员角色、角色类型，会员类型数据
            //3、如果【PAAS-站点管理】有勾选【SAAS多租户部署】，则会员角色数据从【平台后台--系统管理--平台规则--会员角色规则配置】中取【下属会员适用会员角色】的会员角色数据
            Boolean tenantResult = baseSiteService.isEnableMultiTenancy(headers);
//            if(tenantResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
//                throw new BusinessException(tenantResult.getCode(), tenantResult.getMessage());
//            }

            if (tenantResult) {
                MemberRoleRuleDO roleRule = roleRuleRepository.findByMemberId(loginUser.getMemberId());
                if (roleRule != null && !CollectionUtils.isEmpty(roleRule.getSubMemberRoleRule())) {
                    totalCount = roleRule.getSubMemberRoleRule().stream().filter(role -> NumberUtil.isNullOrZero(roleTag) || roleTag.equals(Optional.ofNullable(role.getRoleTag()).orElse(RoleTagEnum.MEMBER.getCode()))).count();
                    result = CollectionPageUtil.pageList(
                                    roleRule.getSubMemberRoleRule()
                                            .stream()
                                            .filter(role -> role.getStatus().equals(EnableDisableStatusEnum.ENABLE.getCode()))
                                            .filter(role -> NumberUtil.isNullOrZero(roleTag) || roleTag.equals(Optional.ofNullable(role.getRoleTag()).orElse(RoleTagEnum.MEMBER.getCode())))
                                            .sorted(Comparator.comparingLong(MemberRoleDO::getId))
                                            .collect(Collectors.toList()), pageDataReq.getCurrent(), pageDataReq.getPageSize())
                            .stream()
                            .map(role -> new RoleQueryResp(role.getId(), role.getRoleName(), role.getRoleType(), role.getMemberType()))
                            .collect(Collectors.toList());
                }
            } else {
                JPAQuery<RoleQueryResp> query = jpaQueryFactory.select(Projections.constructor(RoleQueryResp.class, qMemberRoleDO.id, qMemberRoleDO.roleName, qMemberRoleDO.roleType, qMemberRoleDO.memberType))
                        .from(qMemberRoleDO)
                        .where(qMemberRoleDO.status.eq(EnableDisableStatusEnum.ENABLE.getCode()))
                        .where(qMemberRoleDO.relType.ne(MemberRelationTypeEnum.PLATFORM.getCode()));

                if (NumberUtil.notNullOrZero(roleTag)) {
                    query.where(qMemberRoleDO.roleTag.eq(roleTag));
                }

                query.orderBy(qMemberRoleDO.id.asc())
                        .limit(pageDataReq.getPageSize()).offset(pageDataReq.getCurrentOffset());

                if(loginUser.getMemberLevelType().equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
                    query.where(qMemberRoleDO.memberType.eq(MemberTypeEnum.MERCHANT.getCode()).or(qMemberRoleDO.memberType.eq(MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
                }

                totalCount = query.fetchCount();
                result = query.fetch();
            }
        }

        return new PageDataResp<>(totalCount, result);
    }

    /**
     * 新增或修改会员等级
     *
     * @param headers Http头部信息
     * @param levelVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void createMemberLevel(HttpHeaders headers, MemberLevelReq levelVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        //如果角色Id列表为空，返回
        if(CollectionUtils.isEmpty(levelVO.getRoleIds())) {
            return;
        }

        //判断角色是否存在
        List<MemberRoleDO> roles = roleRepository.findAllById(levelVO.getRoleIds());
        if(roles.size() != levelVO.getRoleIds().size()) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //根据等级、下级会员角色Id 判断是否已经存在
        if(memberLevelConfigRepository.existsByMemberIdAndRoleIdAndLevelAndSubRoleIdIn(loginUser.getMemberId(), loginUser.getMemberRoleId(), levelVO.getLevel(), levelVO.getRoleIds())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CONFIG_EXISTS);
        }

        List<MemberLevelConfigDO> levelConfigs = levelVO.getRoleIds().stream().map(roleId -> {
            MemberLevelConfigDO levelConfig = new MemberLevelConfigDO();
            levelConfig.setMemberId(loginUser.getMemberId());
            levelConfig.setRoleId(loginUser.getMemberRoleId());
            levelConfig.setSubRoleId(roleId);
            levelConfig.setLevel(levelVO.getLevel());
            levelConfig.setLevelTag(levelVO.getLevelTag());
            levelConfig.setScoreTag(levelVO.getScoreTag());
            levelConfig.setPoint(0);
            levelConfig.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
            levelConfig.setRights(new HashSet<>());
            levelConfig.setLevelType(levelVO.getLevelType());
            levelConfig.setRemark(StringUtils.hasText(levelVO.getRemark()) ? levelVO.getRemark().trim() : "");
            return levelConfig;
        }).collect(Collectors.toList());

        memberLevelConfigRepository.saveAll(levelConfigs);


    }

    /**
     * 修改会员等级
     *
     * @param headers  Http头部信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateMemberLevel(HttpHeaders headers, MemberLevelUpdateReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        MemberLevelConfigDO levelConfig = memberLevelConfigRepository.findById(updateVO.getLevelId()).orElse(null);
        if(levelConfig == null || !levelConfig.getMemberId().equals(loginUser.getMemberId()) || !levelConfig.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CONFIG_DOES_NOT_EXISTS);
        }

        //根据等级、下级角色Id 判断等级是否已经存在
        if(memberLevelConfigRepository.existsByMemberIdAndRoleIdAndLevelAndSubRoleIdAndIdNot(loginUser.getMemberId(), loginUser.getMemberRoleId(), updateVO.getLevel(), levelConfig.getSubRoleId(), updateVO.getLevelId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CONFIG_EXISTS);
        }

        //只修改名称等信息
        levelConfig.setLevel(updateVO.getLevel());
        levelConfig.setLevelTag(updateVO.getLevelTag());
        levelConfig.setScoreTag(updateVO.getScoreTag());
        levelConfig.setRemark(StringUtils.hasText(updateVO.getRemark()) ? updateVO.getRemark().trim() : "");

        memberLevelConfigRepository.saveAndFlush(levelConfig);


    }

    /**
     * 查询会员等级详情
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public MemberLevelDetailResp findMemberLevel(HttpHeaders headers, MemberLevelIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberLevelConfigDO levelConfig = memberLevelConfigRepository.findById(idVO.getLevelId()).orElse(null);
        if(levelConfig == null || !levelConfig.getMemberId().equals(loginUser.getMemberId()) || !levelConfig.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CONFIG_DOES_NOT_EXISTS);
        }

        MemberRoleDO role = roleRepository.findById(levelConfig.getSubRoleId()).orElse(null);
        if(role == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        MemberLevelDetailResp detailVO = new MemberLevelDetailResp();
        detailVO.setLevelId(levelConfig.getId());
        detailVO.setLevel(levelConfig.getLevel());
        detailVO.setLevelTag(levelConfig.getLevelTag());
        detailVO.setLevelType(levelConfig.getLevelType());
        detailVO.setLevelTypeName(MemberLevelTypeEnum.getCodeMsg(levelConfig.getLevelType()));
        detailVO.setScoreTag(levelConfig.getScoreTag());
        detailVO.setStatus(levelConfig.getStatus());
        detailVO.setStatusName(EnableDisableStatusEnum.getNameByCode(levelConfig.getStatus()));
        detailVO.setRemark(levelConfig.getRemark());
        detailVO.setRoles(Stream.of(new RoleQueryResp(role.getId(), role.getRoleName(), role.getRoleType(), role.getMemberType())).collect(Collectors.toList()));

        return detailVO;
    }

    /**
     * 修改会员等级状态
     *
     * @param headers  Http头部信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateMemberLevelStatus(HttpHeaders headers, MemberLevelStatusReq statusVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberLevelConfigDO levelConfig = memberLevelConfigRepository.findById(statusVO.getLevelId()).orElse(null);
        if(levelConfig == null || !levelConfig.getMemberId().equals(loginUser.getMemberId()) || !levelConfig.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CONFIG_DOES_NOT_EXISTS);
        }

        //如果已经被关联，不能停用
        if(statusVO.getStatus().equals(EnableDisableStatusEnum.DISABLE.getCode()) && memberLevelRightRepository.existsByMemberIdAndRoleIdAndLevelConfig(loginUser.getMemberId(), loginUser.getMemberRoleId(), levelConfig)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CAN_NOT_DISABLED);
        }

        levelConfig.setStatus(statusVO.getStatus());

        memberLevelConfigRepository.saveAndFlush(levelConfig);

    }

    /**
     * 删除会员等级
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void deleteMemberLevel(HttpHeaders headers, MemberLevelIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberLevelConfigDO levelConfig = memberLevelConfigRepository.findById(idVO.getLevelId()).orElse(null);
        if(levelConfig == null || !levelConfig.getMemberId().equals(loginUser.getMemberId()) || !levelConfig.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CONFIG_DOES_NOT_EXISTS);
        }

        //状态为有效时不能删除
        if(levelConfig.getStatus().equals(EnableDisableStatusEnum.ENABLE.getCode())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CAN_NOT_DELETE);
        }

        memberLevelConfigRepository.delete(levelConfig);

    }

    /**
     * 权益与升级阈值 - 选择会员权益
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public List<BaseMemberRightQueryResp> findMemberLevelRights(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        QBaseRightConfigDO qBaseRightConfig = QBaseRightConfigDO.baseRightConfigDO;
        JPAQuery<BaseMemberRightQueryResp> query = jpaQueryFactory.select(Projections.constructor(BaseMemberRightQueryResp.class, qBaseRightConfig.id, qBaseRightConfig.rightTypeEnum, qBaseRightConfig.acquireWayEnum, qBaseRightConfig.paramWayEnum))
                .from(qBaseRightConfig)
                .where(qBaseRightConfig.status.eq(EnableDisableStatusEnum.ENABLE.getCode()))
                .orderBy(qBaseRightConfig.rightTypeEnum.asc());

        //平台后台没有价格权益
        if(loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode())) {
            query.where(qBaseRightConfig.rightTypeEnum.ne(MemberRightTypeEnum.PRICE_RIGHT.getCode()));
        }

        return query.fetch();
    }

    /**
     * 权益与升级阈值 - 查询详情
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public MemberLevelRightDetailResp findMemberLevelRightDetail(HttpHeaders headers, MemberLevelIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberLevelConfigDO levelConfig = memberLevelConfigRepository.findById(idVO.getLevelId()).orElse(null);
        if(levelConfig == null || !levelConfig.getMemberId().equals(loginUser.getMemberId()) || !levelConfig.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CONFIG_DOES_NOT_EXISTS);
        }

        MemberRoleDO role = roleRepository.findById(levelConfig.getSubRoleId()).orElse(null);
        if(role == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        return new MemberLevelRightDetailResp(levelConfig.getId(), levelConfig.getLevel(), levelConfig.getLevelTag(), levelConfig.getLevelType(), levelConfig.getScoreTag(), levelConfig.getRemark(), levelConfig.getSubRoleId(), role.getRoleName(), role.getRoleType(), role.getMemberType(), levelConfig.getPoint(), levelConfig.getRights().stream().map(right -> new MemberRightDetailResp(right.getId(), right.getRightType(), right.getAcquireWay(), right.getParamWay(), right.getParameter(), right.getStatus())).collect(Collectors.toList()));
    }

    /**
     * 修改升级阈值、权益列表
     *
     * @param headers Http头部信息
     * @param pointVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateMemberLevelRight(HttpHeaders headers, MemberLevelRightUpdateReq pointVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);

        //升级阈值要大于前一个等级，小于后一个等级
        List<MemberLevelConfigDO> levelConfigs = memberLevelConfigRepository.findByMemberIdAndRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId());
        MemberLevelConfigDO levelConfig = levelConfigs.stream().filter(config -> config.getId().equals(pointVO.getLevelId())).findFirst().orElse(null);
        if(levelConfig == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_CONFIG_DOES_NOT_EXISTS);
        }

        //查找前一个等级
        MemberLevelConfigDO lastLevelConfig = levelConfigs.stream().filter(config -> config.getSubRoleId().equals(levelConfig.getSubRoleId()) && config.getLevel().compareTo(levelConfig.getLevel()) < 0).max(Comparator.comparingInt(MemberLevelConfigDO::getLevel)).orElse(null);
        if(Objects.nonNull(lastLevelConfig) && lastLevelConfig.getPoint().compareTo(pointVO.getPoint()) >= 0) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_POINT_MUST_GREATER_THAN_LOWER_LEVEL);
        }

        //查找下一个等级
        MemberLevelConfigDO nextLevelConfig = levelConfigs.stream().filter(config -> config.getSubRoleId().equals(levelConfig.getSubRoleId()) && config.getLevel().compareTo(levelConfig.getLevel()) > 0).min(Comparator.comparingInt(MemberLevelConfigDO::getLevel)).orElse(null);
        if(Objects.nonNull(nextLevelConfig) && nextLevelConfig.getPoint().compareTo(pointVO.getPoint()) <= 0) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_POINT_MUST_LESS_THAN_UPPER_LEVEL);
        }

        //如果前端删除掉所有权益，则删除权益
        if(CollectionUtils.isEmpty(pointVO.getRights())) {
            memberRightConfigRepository.deleteAll(levelConfig.getRights());
            levelConfig.setRights(new HashSet<>());
            levelConfig.setPoint(pointVO.getPoint());
            memberLevelConfigRepository.saveAndFlush(levelConfig);
            //return WrapperUtil.success();
        }

        List<BaseRightConfigDO> baseRightConfigs = baseRightConfigRepository.findByRightTypeEnumIn(pointVO.getRights().stream().map(MemberRightTypeParamReq::getRightType).collect(Collectors.toList()));
        if(baseRightConfigs.size() != pointVO.getRights().size()) {
            //throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RIGHT_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RIGHT_DOES_NOT_EXIST);
        }

        //删除前端去掉的
        List<MemberRightConfigDO> memberRights = new ArrayList<>(levelConfig.getRights());
        memberRights.removeIf(right -> pointVO.getRights().stream().noneMatch(r -> r.getRightType().equals(right.getRightType())));

        //更改参数
        memberRights.forEach(right -> pointVO.getRights().stream().filter(r -> r.getRightType().equals(right.getRightType())).findFirst().ifPresent(r -> right.setParameter(r.getParameter().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP))));

        //新增（这里要用 Collectors.toList()， 如果用 Collectors.toSet()，只会新增一条
        List<MemberRightConfigDO> newMemberRightConfigs = pointVO.getRights().stream().filter(right -> memberRights.stream().noneMatch(exist -> exist.getRightType().equals(right.getRightType()))).map(right -> {
            BaseRightConfigDO baseRightConfig = baseRightConfigs.stream().filter(b -> b.getRightTypeEnum().equals(right.getRightType())).findFirst().orElse(null);
            if(baseRightConfig == null) {
                return null;
            }

            MemberRightConfigDO rightConfig = new MemberRightConfigDO();
            rightConfig.setMemberId(loginUser.getMemberId());
            rightConfig.setRoleId(loginUser.getMemberRoleId());
            rightConfig.setRightType(baseRightConfig.getRightTypeEnum());
            rightConfig.setAcquireWay(baseRightConfig.getAcquireWayEnum());
            rightConfig.setParamWay(baseRightConfig.getParamWayEnum());
            rightConfig.setParameter(right.getParameter().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
            rightConfig.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
            return rightConfig;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        memberRights.addAll(newMemberRightConfigs);
        memberRightConfigRepository.saveAll(memberRights);

        levelConfig.setRights(new HashSet<>(memberRights));
        levelConfig.setPoint(pointVO.getPoint());
        memberLevelConfigRepository.saveAndFlush(levelConfig);


    }

    /**
     * 修改权益参数
     *
     * @param headers Http头部信息
     * @param paramVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateRightParameter(HttpHeaders headers, MemberRightParamReq paramVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberRightConfigDO memberRightConfig = memberRightConfigRepository.findById(paramVO.getRightId()).orElse(null);
        if(memberRightConfig == null || !memberRightConfig.getMemberId().equals(loginUser.getMemberId()) || !memberRightConfig.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RIGHT_DOES_NOT_EXIST);
        }

        memberRightConfig.setParameter(paramVO.getParameter().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
        memberRightConfigRepository.saveAndFlush(memberRightConfig);

    }

    /**
     * 修改权益状态
     *
     * @param headers  Http头部新
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateRightStatus(HttpHeaders headers, MemberRightStatusReq statusVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberRightConfigDO memberRightConfig = memberRightConfigRepository.findById(statusVO.getRightId()).orElse(null);
        if(memberRightConfig == null || !memberRightConfig.getMemberId().equals(loginUser.getMemberId()) || !memberRightConfig.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RIGHT_DOES_NOT_EXIST);
        }

        memberRightConfig.setStatus(statusVO.getStatus());
        memberRightConfigRepository.saveAndFlush(memberRightConfig);

    }

    /**
     * 将所有没有配置会员等级的下级会员，初始化为最小会员等级
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void rebuildMemberLevelRight(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        //Step 1: 查询下级会员当前等级配置中，没有关联MemberLevelConfigDO的
        Specification<MemberLevelRightDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.isNotNull(root.get("relation").as(MemberRelationDO.class)));
            list.add(criteriaBuilder.isNull(root.get("levelConfig").as(MemberLevelConfigDO.class)));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        //如果没有，表示已经全部设置了等级，返回成功
        List<MemberLevelRightDO> levelRights = memberLevelRightRepository.findAll(specification);
        if(CollectionUtils.isEmpty(levelRights)) {

        }

        //Step 2: 查找当前会员所有等级配置，如果没有则返回成功
        List<MemberLevelConfigDO> levelConfigs = memberLevelConfigRepository.findByMemberIdAndRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId());
        if(CollectionUtils.isEmpty(levelConfigs)) {

        }

        //Step 3: 循环每一个下级会员关系进行修改
        List<MemberLevelRightDO> updateList = new ArrayList<>();
        for (MemberLevelRightDO levelRight : levelRights) {
            //根据下级会员角色查询最小等级，如果没有，不修改
            MemberLevelConfigDO firstLevelConfig = levelConfigs.stream().filter(config -> config.getSubRoleId().equals(levelRight.getSubRoleId()) && config.getStatus().equals(EnableDisableStatusEnum.ENABLE.getCode())).min(Comparator.comparingInt(MemberLevelConfigDO::getLevel)).orElse(null);
            if(firstLevelConfig == null) {
                continue;
            }

            levelRight.setLevel(firstLevelConfig.getLevel());
            levelRight.setLevelTag(firstLevelConfig.getLevelTag());
            levelRight.setLevelConfig(firstLevelConfig);
            updateList.add(levelRight);
        }

        if(!CollectionUtils.isEmpty(updateList)) {
            memberLevelRightRepository.saveAll(updateList);
        }


    }

    /**
     * 查询平台会员的等级、权益、信用积分
     * @param headers Http头部信息
     * @param levelVO 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    @Override
    public MemberLrcResp getPlatformMemberLrc(HttpHeaders headers, MemberIdAndRoleIdReq levelVO) {
        MemberRelationDO relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(levelVO.getMemberId(), levelVO.getRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if(relationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberLrcResp lrcVO = new MemberLrcResp();
        lrcVO.setLevel(0);
        lrcVO.setLevelTag("");
        lrcVO.setScore(0);
        lrcVO.setSumReturnMoney(new BigDecimal(0));
        lrcVO.setSumPoint(0);
        lrcVO.setCurrentPoint(0);
        lrcVO.setSumUsedPoint(0);
        lrcVO.setCreditPoint(0);
        lrcVO.setTradeCommentPoint(0);
        lrcVO.setAfterSaleCommentPoint(0);
        lrcVO.setComplainPoint(0);
        lrcVO.setRegisterYearsPoint(0);
        lrcVO.setRegisterYears(DateTimeUtil.diffYears(relationDO.getCreateTime(), LocalDateTime.now()));

        MemberLevelRightDO levelRightDO = relationDO.getLevelRight();
        if(levelRightDO != null) {
            lrcVO.setLevel(levelRightDO.getLevel());
            lrcVO.setLevelTag(levelRightDO.getLevelTag());
            lrcVO.setScore(levelRightDO.getScore());
            lrcVO.setSumReturnMoney(levelRightDO.getSumReturnMoney());
            lrcVO.setSumPoint(levelRightDO.getSumPoint());
            lrcVO.setCurrentPoint(levelRightDO.getCurrentPoint());
            lrcVO.setSumUsedPoint(levelRightDO.getSumUsedPoint());
        }

        return lrcVO;
    }

    /**
     * 新增平台会员时，创建平台会员的升级规则配置
     * @param memberId 会员Id
     * @param roleId   角色Id
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void createMemberLevelRuleConfig(Long memberId, Long roleId) {
        List<BaseLevelRuleDO> baseLevelRuleDOList = baseLevelRuleRepository.findByMemberLevelTypeEnumNotAndStatus(MemberLevelTypeEnum.PLATFORM.getCode(), EnableDisableStatusEnum.ENABLE.getCode());
        if (CollectionUtils.isEmpty(baseLevelRuleDOList)) {
            return;
        }

        List<MemberLevelRuleConfigDO> memberLevelRuleConfigDOList = memberLevelRuleConfigRepository.findByMemberIdAndRoleId(memberId, roleId);

        List<MemberLevelRuleConfigDO> insertList = new ArrayList<>();

        for (BaseLevelRuleDO baseLevelRuleDO : baseLevelRuleDOList) {
            MemberLevelRuleConfigDO memberLevelRuleConfigDO = memberLevelRuleConfigDOList.stream().filter(config -> config.getRule().getId().equals(baseLevelRuleDO.getId())).findFirst().orElse(null);
            if (memberLevelRuleConfigDO == null) {
                memberLevelRuleConfigDO = new MemberLevelRuleConfigDO();
                memberLevelRuleConfigDO.setMemberId(memberId);
                memberLevelRuleConfigDO.setRoleId(roleId);
                memberLevelRuleConfigDO.setRule(baseLevelRuleDO);
                memberLevelRuleConfigDO.setScore(new BigDecimal(0));
                memberLevelRuleConfigDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());

                insertList.add(memberLevelRuleConfigDO);

            }
        }

        memberLevelRuleConfigRepository.saveAll(insertList);
    }

    /**
     * 查询是否配置会员等级
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 是否配置
     */
    @Override
    public Boolean isConfiguration(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return memberLevelConfigRepository.existsByMemberIdAndRoleIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleId());
    }

    /**
     * 查询上级配置当前会员等级
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 是否配置
     */
    @Override
    public Boolean subConfiguration(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        MemberRelationDO relationDO = relationRepository.findById(idVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getSubMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return memberLevelConfigRepository.existsByMemberIdAndRoleIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleId());
    }

}
