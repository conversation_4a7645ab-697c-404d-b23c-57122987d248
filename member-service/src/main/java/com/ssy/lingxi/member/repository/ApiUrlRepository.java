package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.menuAuth.ApiUrlDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 接口权限Repository
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/30
 */
@Repository
public interface ApiUrlRepository extends JpaRepository<ApiUrlDO, Long>, JpaSpecificationExecutor<ApiUrlDO> {
}
