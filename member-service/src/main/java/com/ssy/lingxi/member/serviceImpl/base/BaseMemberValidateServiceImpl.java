package com.ssy.lingxi.member.serviceImpl.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.dto.AreaDTO;
import com.ssy.lingxi.component.base.util.AopProxyUtil;
import com.ssy.lingxi.component.base.util.TokenUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.rest.model.req.eos.CorporationPushReq;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.manage.api.feign.IAreaFeign;
import com.ssy.lingxi.manage.api.model.resp.area.AreaCodeResp;
import com.ssy.lingxi.member.constant.MemberRegisterDetailConfigConstant;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskResultBO;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberAfterApprovalLifecycleStagesDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifecycleStagesDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.req.branch.MemberBranchSaveOrUpdateReq;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.UpperRelationIdAndNameResp;
import com.ssy.lingxi.member.model.resp.platform.RoleRuleManageResp;
import com.ssy.lingxi.member.model.resp.validate.WorkFlowStepResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberDepositDetailService;
import com.ssy.lingxi.member.service.base.IBaseMemberHistoryService;
import com.ssy.lingxi.member.service.base.IBaseMemberValidateService;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.feign.IPayFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.model.dto.commission.MemberCertificationSuccessDTO;
import com.ssy.lingxi.common.constant.mq.MemberMqConstant;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.member.service.web.IMemberBranchService;
import com.ssy.lingxi.member.service.web.IRunBrandService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员审核、资料入库基础服务接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-14
 */
@Slf4j
@Service
public class BaseMemberValidateServiceImpl implements IBaseMemberValidateService {
    @Resource
    private MemberRoleRepository roleRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private IPayFeignService payFeignService;

    @Resource
    private IMessageFeignService messageFeignService;

    @Resource
    private MemberAfterApprovalLifecycleStagesRepository memberAfterApprovalLifecycleStagesRepository;

    @Resource
    private MemberLifecycleStagesRepository memberLifecycleStagesRepository;

    @Resource
    private EosApiService eosApiService;

    @Resource
    private CorporationRepository corporationRepository;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private IMqUtils mqUtils;

    @Resource
    private UserRepository userRepository;

    @Resource
    private IMemberBranchService memberBranchService;

    @Resource
    private IAreaFeign areaFeign;

    @Resource
    private RunBrandRepository runBrandRepository;

    @Resource
    private MemberRepository memberRepository;

    /**
     * 根据创建下级会员的规则，查询下级会员的会员类型列表
     *
     * @param memberTypeEnum 上级会员的会员类型枚举
     * @return 查询结果
     */
    @Override
    public List<MemberTypeAndNameResp> getSubMemberTypeList(Integer memberTypeEnum) {
        return Stream.of(new MemberTypeAndNameResp(MemberTypeEnum.toEnum(memberTypeEnum))).collect(Collectors.toList());
    }

    /**
     * 根据创建下级会员的规则，查询下级会员的会员类型列表
     *
     * @param memberTypeEnumList 上级会员的会员类型枚举列表
     * @return 查询结果
     */
    @Override
    public List<MemberTypeAndNameResp> getSubMemberTypeList(List<Integer> memberTypeEnumList) {
        return memberTypeEnumList.stream()
                .map(this::getSubMemberTypeList)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    /**
     * 根据创建下级会员的规则，查询下级会员的会员类型列表(SASS)
     *
     * @param memberTypeEnum   上级会员的会员类型枚举
     * @param roleManageVOList 会员适用角色list
     * @return 查询结果
     */
    @Override
    public List<MemberTypeAndNameResp> getSubMemberTypeList(Integer memberTypeEnum, List<RoleRuleManageResp> roleManageVOList) {
        return roleManageVOList
                .stream()
                .filter(roleManageVO -> roleManageVO.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode())
                        || roleManageVO.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))
                .map(roleManageVO -> new MemberTypeAndNameResp(roleManageVO.getMemberType(), roleManageVO.getMemberTypeName()))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 平台会员审核外部流程定义
     *
     * @param roleName 角色名称
     * @param roleTag  角色标签
     * @return 步骤列表
     */
    @Override
    public List<WorkFlowStepResp> getPlatformValidateOuterSteps(String roleName, Integer roleTag) {
        String memberApplyName = MemberStringEnum.MEMBER_APPLY.getName();
        String memberValidateName = MemberStringEnum.MEMBER_VALIDATE.getName();
        if (NumberUtil.notNullOrZero(roleTag)) {
            memberApplyName = MemberStringEnum.MEMBER_APPLY.getName().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag));
            memberValidateName = MemberStringEnum.MEMBER_VALIDATE.getName().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag));
        }
        return Stream.of(new WorkFlowStepResp(1, memberApplyName, roleName),
                new WorkFlowStepResp(2, memberValidateName, MemberStringEnum.PLATFORM_ROLE_NAME.getName())).collect(Collectors.toList());
    }

    /**
     * 会员入库审核外部流程定义
     *
     * @param relationDO 会员关系
     * @param roleTag    角色标签
     * @return 步骤列表
     */
    @Override
    public List<WorkFlowStepResp> getMemberDepositOuterSteps(MemberRelationDO relationDO, Integer roleTag) {
        //如果是会员创建的下级会员，申请角色和审核角色都是上级会员角色
        return relationDO.getRelSource().equals(MemberRelationSourceEnum.MEMBER_CREATE.getCode()) ? getMemberDepositOuterSteps(relationDO.getRole().getRoleName(), relationDO.getRole().getRoleName(), roleTag) : getMemberDepositOuterSteps(relationDO.getRole().getRoleName(), relationDO.getSubRoleName(), roleTag);
    }

    /**
     * 会员入库审核外部流程定义
     *
     * @param roleName    上级角色名称
     * @param subRoleName 下级角色名称
     * @param roleTag     角色标签
     * @return 步骤列表
     */
    @Override
    public List<WorkFlowStepResp> getMemberDepositOuterSteps(String roleName, String subRoleName, Integer roleTag) {
        String applyForDepository = MemberStringEnum.APPLY_FOR_DEPOSITORY.getName();
        String validateDepositoryName = MemberStringEnum.VALIDATE_DEPOSITORY.getName();
        if (NumberUtil.notNullOrZero(roleTag)) {
            applyForDepository = applyForDepository.replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag));
            validateDepositoryName = validateDepositoryName.replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag));
        }
        return Stream.of(new WorkFlowStepResp(1, applyForDepository, subRoleName),
                new WorkFlowStepResp(2, validateDepositoryName, roleName)).collect(Collectors.toList());
    }

    /**
     * 会员变更审核外部流程定义
     *
     * @param relationDO 会员关系
     * @return 步骤列表
     */
    @Override
    public List<WorkFlowStepResp> getMemberModifyOuterSteps(MemberRelationDO relationDO) {
        return Stream.of(new WorkFlowStepResp(1, MemberStringEnum.APPLY_FOR_CHANGE.getName(), relationDO.getSubRoleName()), new WorkFlowStepResp(2, MemberStringEnum.VALIDATE_CHANGE.getName(), relationDO.getRole().getRoleName())).collect(Collectors.toList());
    }

    /**
     * 添加角色时，检验角色是否符合规则定义
     *
     * @param memberRoleDO   被检验的角色
     * @param memberTypeEnum 会员类型枚举，定义在MemberTypeEnum和MemberTypeDO中
     */
    @Override
    public void checkRoleWithMemberType(MemberRoleDO memberRoleDO, Integer memberTypeEnum) {
        switch (MemberTypeEnum.toEnum(memberTypeEnum)) {
            case MERCHANT:
                if (!memberRoleDO.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode())) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_MERCHANT_CAN_ADD_MERCHANT_OR_CHANNEL_ROLE);
                }
                break;
            case MERCHANT_PERSONAL:
//                if (!memberRoleDO.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode())) {
//                    throw new BusinessException(ResponseCodeEnum.MC_MS_MERCHANT_PERSONAL_CAN_ADD_MERCHANT_PERSONAL_OR_CHANNEL_PERSONAL_ROLE);
//                }
                break;
            default:
                break;
        }

    }

    /**
     * 查询流程步骤定义
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public WorkflowTaskListBO getMemberProcessSteps(MemberRelationDO relationDO) {
        if (relationDO.getValidateTask() == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_PROCESS_DATA_ERROR);
        }

        WorkflowTaskListBO taskListBO = new WorkflowTaskListBO();

        WorkflowTaskListBO result = workflowFeignService.listMemberProcessSteps(relationDO.getMemberId(), relationDO.getValidateTask().getProcessKey(), relationDO.getValidateTask().getTaskId());

        taskListBO.setStepList(result.getStepList());

        if (StringUtils.hasLength(relationDO.getValidateTask().getTaskId())) {
            taskListBO.setCurrentStep(result.getCurrentStep());
        } else {
            result.getStepList().stream().max(Comparator.comparingInt(WorkFlowStepResp::getStep)).ifPresent(workFlowStepVO -> taskListBO.setCurrentStep(workFlowStepVO.getStep()));
        }

        if (relationDO.getValidateTask().getProcessTypeEnum().equals(MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode())) {
            taskListBO.setProcessName(MemberStringEnum.MEMBER_DEPOSIT_INNER_PROCESS.getName());
        }

        if (relationDO.getValidateTask().getProcessTypeEnum().equals(MemberProcessTypeEnum.MEMBER_MODIFICATION.getCode())) {
            taskListBO.setProcessName(MemberStringEnum.MEMBER_CHANGE_INNER_PROCESS.getName());
        }

        return taskListBO;
    }

    /**
     * 根据创建下级会员的规则，查询下级会员的角色列表
     *
     * @param memberTypeEnum 上级会员的会员类型枚举
     * @param roleTag        角色标签
     * @return 查询结果
     */
    @Override
    public List<RoleIdAndNameResp> getSubRoleList(Integer memberTypeEnum, Integer roleTag) {
        if (memberTypeEnum.equals(MemberTypeEnum.MERCHANT.getCode()) || memberTypeEnum.equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode())) {
            Specification<MemberRoleDO> specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();
                list.add(criteriaBuilder.notEqual(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
                list.add(criteriaBuilder.in(root.get("memberType")).value(Arrays.asList(MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));
                if (NumberUtil.notNullOrZero(roleTag)) {
                    list.add(criteriaBuilder.equal(root.get("roleTag").as(Integer.class), roleTag));
                }
                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };
            return roleRepository.findAll(specification, Sort.by("id").ascending()).stream().map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 根据创建下级会员的规则，查询下级会员的角色列表(SAAS)
     *
     * @param memberTypeEnum   上级会员的会员类型枚举
     * @param roleManageVOList 会员适用角色list
     * @return 查询结果
     */
    @Override
    public List<RoleIdAndNameResp> getSubRoleList(Integer memberTypeEnum, List<RoleRuleManageResp> roleManageVOList) {
        return roleManageVOList
                .stream()
                .filter(roleManageVO -> roleManageVO.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode())
                        || roleManageVO.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))
                .map(roleManageVO -> new RoleIdAndNameResp(roleManageVO.getRoleId(), roleManageVO.getRoleName()))
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 查询上级会员Id和名称列表
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @return 查询结果
     */
    @Override
    public List<UpperRelationIdAndNameResp> getUpperRelationList(Long subMemberId, Long subRoleId) {
        List<MemberRelationDO> relationList = relationRepository.findBySubMemberIdAndSubRoleIdAndRelType(subMemberId, subRoleId, MemberRelationTypeEnum.OTHER.getCode());
        return CollectionUtils.isEmpty(relationList) ? Stream.of(new UpperRelationIdAndNameResp(0L, "")).collect(Collectors.toList()) : relationList.stream().map(relationDO -> new UpperRelationIdAndNameResp(relationDO.getId(), relationDO.getMember().getName().concat("(").concat(relationDO.getRole().getRoleName()).concat(")"))).sorted(Comparator.comparingLong(UpperRelationIdAndNameResp::getUpperRelationId)).collect(Collectors.toList());
    }


    /**
     * 根据会员类型，查询角色列表（排除“平台”角色）
     *
     * @param memberType 会员类型Id
     * @return 查询结果
     */
    @Override
    public List<RoleIdAndNameResp> getRoleListByMemberType(Integer memberType) {
        Specification<MemberRoleDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.notEqual(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
            list.add(criteriaBuilder.equal(root.get("memberType").as(Integer.class), memberType));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return roleRepository.findAll(specification, Sort.by("id").ascending()).stream().map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).collect(Collectors.toList());
    }

    /**
     * 批量执行审核
     *
     * @param loginUser       登录用户
     * @param validateIds     审核内容Id列表
     * @param innerStatusEnum 匹配的内部状态
     * @param roleTag         角色标签
     * @return 审核结果
     */
    @Override
    public void batchExecMemberProcess(UserLoginCacheDTO loginUser, List<Long> validateIds, MemberInnerStatusEnum innerStatusEnum, Integer roleTag) {
        batchExecMemberProcess(loginUser, validateIds, Stream.of(innerStatusEnum).collect(Collectors.toList()), roleTag);
    }

    /**
     * 批量入库、资料审核
     *
     * @param loginUser        登录用户
     * @param validateIds      审核内容Id列表
     * @param innerStatusEnums 匹配的内部状态列表
     * @param roleTag          角色标签
     * @return 审核结果
     */
    @Override
    public void batchExecMemberProcess(UserLoginCacheDTO loginUser, List<Long> validateIds, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag) {
        if (CollectionUtils.isEmpty(validateIds)) {
            return;
        }

        List<MemberRelationDO> relationList = relationRepository.findAllById(validateIds);
        if (relationList.size() != validateIds.size()) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && relationList.stream().anyMatch(r -> !roleTag.equals(r.getSubRoleTag()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (relationList.stream().anyMatch(relationDO -> relationDO.getValidateTask() == null || !StringUtils.hasLength(relationDO.getValidateTask().getProcessKey()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_PROCESS_DATA_ERROR);
        }

        //TaskId如果为空，说明流程已经结束
        if (relationList.stream().anyMatch(relationDO -> !StringUtils.hasLength(relationDO.getValidateTask().getTaskId()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_HAS_COMPLETED);
        }

        //检查状态
        List<Integer> innerStatusCodes = innerStatusEnums.stream().map(MemberInnerStatusEnum::getCode).collect(Collectors.toList());
        if (relationList.stream().anyMatch(relationDO -> !innerStatusCodes.contains(relationDO.getInnerStatus()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_STATUS_INCORRECT);
        }

        for (MemberRelationDO relationDO : relationList) {
            AopProxyUtil.getCurrentProxy(this.getClass()).execMemberProcess(loginUser, relationDO, MemberValidateAgreeEnum.AGREE.getCode(), "", innerStatusEnums, roleTag);

        }

    }

    /**
     * 执行入库、资料变更流程
     *
     * @param loginUser  登录用户
     * @param validateId 会员关系Id
     * @param agree      审核结果 0-不通过， 1-通过
     * @param reason     审核意见
     * @param roleTag    角色标签
     * @return 执行结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void execMemberProcess(UserLoginCacheDTO loginUser, Long validateId, Integer agree, String reason, MemberInnerStatusEnum innerStatusEnum, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (relationDO.getValidateTask() == null || !StringUtils.hasLength(relationDO.getValidateTask().getProcessKey())) {
            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_PROCESS_DATA_ERROR);
        }

        //审核不通过的原因不能为空
        if (agree.equals(MemberValidateAgreeEnum.DISAGREE.getCode()) && !StringUtils.hasLength(reason)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_DISAGREE_REASON_CAN_NOT_BE_EMPTY);
        }

        //TaskId如果为空，说明流程已经结束
        if (!StringUtils.hasLength(relationDO.getValidateTask().getTaskId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_HAS_COMPLETED);
        }

        //检查状态
        if (!innerStatusEnum.getCode().equals(relationDO.getInnerStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_STATUS_INCORRECT);
        }

        execMemberProcess(loginUser, relationDO, agree, reason, Stream.of(innerStatusEnum).collect(Collectors.toList()), roleTag);
    }

    /**
     * 执行入库、资料变更流程
     *
     * @param loginUser        登录用户
     * @param validateId       会员关系Id
     * @param agree            审核结果 0-不通过， 1-通过
     * @param reason           审核意见
     * @param innerStatusEnums 匹配的内部状态列表
     * @param roleTag          角色标签
     * @return 执行结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void execMemberProcess(UserLoginCacheDTO loginUser, Long validateId, Integer agree, String reason, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag) {
        execMemberProcess(loginUser,validateId,agree,reason,innerStatusEnums,roleTag,null);
    }

    /**
     * 执行入库、资料变更流程
     *
     * @param loginUser        登录用户
     * @param validateId       会员关系Id
     * @param agree            审核结果 0-不通过， 1-通过
     * @param reason           审核意见
     * @param innerStatusEnums 匹配的内部状态列表
     * @param roleTag          角色标签
     * @return 执行结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void execMemberProcess(UserLoginCacheDTO loginUser, Long validateId, Integer agree, String reason, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag,String brandCode) {
        execMemberProcess(loginUser,validateId,agree,reason,innerStatusEnums,roleTag,brandCode,null);
    }

    /**
     * 执行入库、资料变更流程
     *
     * @param loginUser        登录用户
     * @param validateId       会员关系Id
     * @param agree            审核结果 0-不通过， 1-通过
     * @param reason           审核意见
     * @param innerStatusEnums 匹配的内部状态列表
     * @param roleTag          角色标签
     * @return 执行结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void execMemberProcess(UserLoginCacheDTO loginUser, Long validateId, Integer agree, String reason, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag,String brandCode,String simpleMemberName) {
        MemberRelationDO relationDO = relationRepository.findById(validateId).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (relationDO.getValidateTask() == null || !StringUtils.hasLength(relationDO.getValidateTask().getProcessKey())) {
            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_PROCESS_DATA_ERROR);
        }

        //审核不通过的原因不能为空
        RunBrandDO firstByCode=null;
        if (agree.equals(MemberValidateAgreeEnum.DISAGREE.getCode()) && !StringUtils.hasText(reason)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_DISAGREE_REASON_CAN_NOT_BE_EMPTY);
        }else{
            if(StringUtils.hasLength(brandCode)){
                //如果为其它品牌
                if(brandCode.equals("OTH")){
                    throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_NO_SUPPORT);
                }
                firstByCode = runBrandRepository.findFirstByCode(brandCode);
                if(firstByCode==null){
                    throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_NOT_EXISTS);
                }
            }
        }

        //TaskId如果为空，说明流程已经结束
        if (!StringUtils.hasLength(relationDO.getValidateTask().getTaskId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_HAS_COMPLETED);
        }

        //检查状态
        if (innerStatusEnums.stream().noneMatch(innerStatusEnum -> innerStatusEnum.getCode().equals(relationDO.getInnerStatus()))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_STATUS_INCORRECT);
        }

        //更新会员经营品牌
        if(firstByCode!=null) {
            MemberDO memberDO = memberRepository.findById(relationDO.getSubMemberId()).orElse(null);
            //设置会员经营品牌
            memberDO.setBrandCode(firstByCode.getCode());
            memberDO.setBrandName(firstByCode.getName());
            memberDO.setName(IRunBrandService.buildMemberName(memberDO.getName(),firstByCode.getCode()));
            if(StringUtils.hasLength(simpleMemberName)){
                memberDO.setSimpleMemberName(simpleMemberName);
            }
            memberRepository.saveAndFlush(memberDO);
        }

        execMemberProcess(loginUser, relationDO, agree, reason, innerStatusEnums, roleTag);
    }

    /**
     * 执行入库、资料变更流程
     *
     * @param loginUser       登录用户
     * @param relationDO      会员关系
     * @param agree           审核结果 0-不通过， 1-通过
     * @param reason          不通过的原因
     * @param innerStatusEnum 匹配的内部状态
     * @return 执行结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void execMemberProcess(UserLoginCacheDTO loginUser, MemberRelationDO relationDO, Integer agree, String reason, MemberInnerStatusEnum innerStatusEnum, Integer roleTag) {
        if (relationDO.getValidateTask() == null || !StringUtils.hasLength(relationDO.getValidateTask().getProcessKey())) {
            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_PROCESS_DATA_ERROR);
        }

        //审核不通过的原因不能为空
        if (agree.equals(MemberValidateAgreeEnum.DISAGREE.getCode()) && !StringUtils.hasLength(reason)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_DISAGREE_REASON_CAN_NOT_BE_EMPTY);
        }

        //TaskId如果为空，说明流程已经结束
        if (!StringUtils.hasLength(relationDO.getValidateTask().getTaskId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_HAS_COMPLETED);
        }

        //检查状态
        if (!innerStatusEnum.getCode().equals(relationDO.getInnerStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_STATUS_INCORRECT);
        }

        execMemberProcess(loginUser, relationDO, agree, reason, Stream.of(innerStatusEnum).collect(Collectors.toList()), roleTag);
    }

    /**
     * 执行入库、资料变更流程
     *
     * @param loginUser        登录用户
     * @param relationDO       会员关系
     * @param agree            审核结果 0-不通过， 1-通过
     * @param reason           不通过的原因
     * @param innerStatusEnums 匹配的内部状态列表
     * @param roleTag          角色标签
     * @return 执行结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void execMemberProcess(UserLoginCacheDTO loginUser, MemberRelationDO relationDO, Integer agree, String reason, List<MemberInnerStatusEnum> innerStatusEnums, Integer roleTag) {
        //记录执行之前的内部状态
        int lastInnerStatus = relationDO.getInnerStatus();
        //执行流程
        WorkflowTaskResultBO taskResult = workflowFeignService.execMemberProcess(relationDO.getValidateTask().getProcessKey(), relationDO.getValidateTask().getTaskId(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId(), agree);

        relationDO.setInnerStatus(taskResult.getInnerStatus());
        relationDO.getValidateTask().setTaskId(taskResult.getTaskId());

        //如果入库审核通过或不通过，修改外部状态
        if (taskResult.getInnerStatus().equals(MemberInnerStatusEnum.VERIFY_PASSED.getCode()) || taskResult.getInnerStatus().equals(MemberInnerStatusEnum.VERIFY_NOT_PASSED.getCode())) {
            relationDO.setOuterStatus(taskResult.getInnerStatus().equals(MemberInnerStatusEnum.VERIFY_PASSED.getCode()) ? MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode() : MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED.getCode());

            //入库时间
            relationDO.setDepositTime(taskResult.getInnerStatus().equals(MemberInnerStatusEnum.VERIFY_PASSED.getCode()) ? LocalDateTime.now() : null);

            //如果入库审核通过，表示“曾经审核通过”
            if (taskResult.getInnerStatus().equals(MemberInnerStatusEnum.VERIFY_PASSED.getCode())) {
                relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
            }

            //如果入库审核不通过，记录审核不通过原因
            if (taskResult.getInnerStatus().equals(MemberInnerStatusEnum.VERIFY_NOT_PASSED.getCode())) {
                relationDO.setValidateMsg(reason);
            }

            //通知支付服务
            if (taskResult.getInnerStatus().equals(MemberInnerStatusEnum.VERIFY_PASSED.getCode())) {
                //入库审核通过，将入库资料的版本改为“Using”，否则不处理（继续修改）
                baseMemberDepositDetailService.updateDepositDetailToUsing(relationDO);

                payFeignService.notifyMemberCredit(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
                payFeignService.notifyMemberAssetAccount(relationDO.getMemberId(), relationDO.getMember().getName(), relationDO.getRoleId(), relationDO.getRole().getRoleName(), relationDO.getSubMemberId(), relationDO.getSubMember().getCode(), relationDO.getSubMember().getName(), relationDO.getSubRoleId(), relationDO.getSubRoleName(), relationDO.getSubMemberLevelTypeEnum(), relationDO.getSubRole().getMemberType(), relationDO.getStatus());

                //推送客户
                pushCustomerToEos(relationDO);
            }

            //记录外部历史记录
            baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.parseString(taskResult.getOperation()), MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), reason);

        }

        //如果资料变更审核通过或不通过，修改外部状态
        if (taskResult.getInnerStatus().equals(MemberInnerStatusEnum.MODIFY_PASSED.getCode()) || taskResult.getInnerStatus().equals(MemberInnerStatusEnum.MODIFY_NOT_PASSED.getCode())) {
            relationDO.setOuterStatus(taskResult.getInnerStatus().equals(MemberInnerStatusEnum.MODIFY_PASSED.getCode()) ? MemberOuterStatusEnum.MODIFY_PASSED.getCode() : MemberOuterStatusEnum.MODIFY_NOT_PASSED.getCode());

            //变更审核通过，首先要比较入库资料是否变更，记录变更记录；
            // 再将入库资料的版本改为“Using”，否则不处理（继续修改）
            if (taskResult.getInnerStatus().equals(MemberInnerStatusEnum.MODIFY_PASSED.getCode())) {
                baseMemberDepositDetailService.saveDepositDetailHistory(relationDO);
                baseMemberDepositDetailService.updateDepositDetailToUsing(relationDO);
            }

            //记录外部历史记录
            baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.parseString(taskResult.getOperation()), MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), reason);

            //推送客户
            pushCustomerToEos(relationDO);
        }

        // 审核通过，回填生命周期
        if (MemberInnerStatusEnum.VERIFY_PASSED.getCode().equals(relationDO.getInnerStatus())) {
            MemberAfterApprovalLifecycleStagesDO lifecycleStagesDO = memberAfterApprovalLifecycleStagesRepository.findByMemberIdAndAndRoleIdAndRoleTag(relationDO.getMemberId(), relationDO.getRoleId(), roleTag);
            if (Objects.nonNull(lifecycleStagesDO)) {
                MemberLifecycleStagesDO memberLifecycleStagesDO = memberLifecycleStagesRepository.findByMemberIdAndRoleIdAndRoleTagAndLifecycleStagesNum(relationDO.getMemberId(), relationDO.getRoleId(), roleTag, lifecycleStagesDO.getLifecycleStagesNum());
                relationDO.setMemberLifecycleStages(memberLifecycleStagesDO);
            }
        }

        relationRepository.saveAndFlush(relationDO);

        // 主品牌账号(第一次认证通过的),创建店铺
        try {
            extracted(relationDO, taskResult);
        } catch (Exception e) {
            log.error("创建店铺失败: {}", e.getMessage(), e);
        }

        //记录内部审核记录
        baseMemberHistoryService.saveMemberInnerHistory(relationDO, loginUser, MemberValidateHistoryOperationEnum.parseString(taskResult.getOperation()), reason);

        messageFeignService.sendMemberValidateMessage(relationDO, roleTag);
    }

    /**
     * 创建店铺
     * @param relationDO 会员关系
     * @param taskResult 工作流任务结果
     */
    private void extracted(MemberRelationDO relationDO, WorkflowTaskResultBO taskResult) {
        if (taskResult.getInnerStatus().equals(MemberInnerStatusEnum.VERIFY_PASSED.getCode()) && ObjectUtil.isNotEmpty(relationDO.getSubMember().getMainFlag()) && relationDO.getSubMember().getMainFlag()) {
            Optional<CorporationDO> corporationDO = corporationRepository.findById(relationDO.getSubMember().getCorporationId());
            if (corporationDO.isPresent()) {
                createInitShop(relationDO,corporationDO.get());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createInitShop(MemberRelationDO relationDO,CorporationDO corporationDO){
        // 创建店铺
        MemberBranchSaveOrUpdateReq memberBranchReq = new MemberBranchSaveOrUpdateReq();
        memberBranchReq.setName(relationDO.getSubMember().getName());
        memberBranchReq.setSimpleName(relationDO.getSubMember().getName());
        memberBranchReq.setOperateBrand(relationDO.getSubMember().getName());
        memberBranchReq.setBrandId(relationDO.getSubMember().getId());
        // 省市区
        if (StringUtils.hasLength(corporationDO.getAddress())) {
            Pattern pattern = Pattern.compile("^([\\u4e00-\\u9fa5]+省)?([\\u4e00-\\u9fa5]+市)?([\\u4e00-\\u9fa5]+区)?([\\u4e00-\\u9fa5]+街道)?.*");
            Matcher matcher = pattern.matcher(corporationDO.getAddress().trim());
            if (matcher.find() && StringUtils.hasLength(matcher.group(1)) && StringUtils.hasLength(matcher.group(2))) {
                WrapperResp<List<AreaCodeResp>> areaByName = areaFeign.findAreaByName(Stream.of(matcher.group(1), matcher.group(2)).collect(Collectors.toList()));
                if (WrapperUtil.isOk(areaByName) && CollectionUtil.isNotEmpty(areaByName.getData())) {
                    Optional<AreaCodeResp> codeResp = areaByName.getData().stream().filter(s -> s.getName().equals(matcher.group(1))).findFirst();
                    Optional<AreaCodeResp> codeTwoResp = areaByName.getData().stream().filter(s -> s.getName().equals(matcher.group(2))).findFirst();
                    if (codeResp.isPresent() && codeTwoResp.isPresent()) {
                        List<AreaDTO> areaList = new ArrayList<>();
                        AreaDTO areaDTO = new AreaDTO();
                        areaDTO.setProvince(codeResp.get().getName());
                        areaDTO.setProvinceCode(codeResp.get().getCode());
                        areaDTO.setCity(codeTwoResp.get().getName());
                        areaDTO.setCityCode(codeTwoResp.get().getCode());
                        areaList.add(areaDTO);
                        memberBranchReq.setBusinessAreas(areaList);
                    }
                }
            }
        }
        UserLoginCacheDTO loginUser = new UserLoginCacheDTO();
        loginUser.setMemberId(relationDO.getSubMemberId());
        loginUser.setMemberRoleId(relationDO.getSubRoleId());
        UserDO userDO = userRepository.findFirstByMemberIdAndUserType(relationDO.getSubMemberId(), UserTypeEnum.ADMIN.getCode());
        loginUser.setUserId(userDO.getId());
        loginUser.setUserName(userDO.getName());
        memberBranchService.saveOrUpdate(loginUser, memberBranchReq);
    }

    private void pushCustomerToEos(MemberRelationDO relationDO) {
        // 如果是企业客户入库通过，需推送到优时
        if (Objects.equals(relationDO.getSubRoleId(), baiTaiMemberProperties.getCustomerRoleId())) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    Long relationId = relationDO.getId();

                    CompletableFuture.runAsync(() -> {
                        // 企业客户认证通过
                        if (Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode())) {
                            //log.info("企业认证通过，清除登录token");
                            //redisUtils.keyDelByPre(TokenUtil.getMemberAccessTokenPreRedisKey(relationDO.getSubMemberId()), RedisConstant.REDIS_USER_INDEX);
                            //redisUtils.keyDelByPre(TokenUtil.getMemberRefreshTokenPreRedisKey(relationDO.getSubMemberId()), RedisConstant.REDIS_USER_INDEX);

                            // 发送企业认证成功分佣处理MQ消息
                            sendCertificationSuccessMessage(relationDO);
                        }

                        MemberRelationDO relationDO = relationRepository.findById(relationId).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST));
                        MemberDO subMember = relationDO.getSubMember();
                        Long corporationId = subMember.getCorporationId();

                        log.info("推送企业客户：{}", subMember.getId());
                        if (Objects.nonNull(corporationId)) {
                            CorporationDO corporationDO = corporationRepository.findById(subMember.getCorporationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));

                            CorporationPushReq corporationPushReq = new CorporationPushReq();
                            corporationPushReq.setDwbm(corporationDO.getCode());
                            corporationPushReq.setDwmc(corporationDO.getName());
                            corporationPushReq.setFr(corporationDO.getLegalPersonName());
                            corporationPushReq.setSjxqmc("广东省");
                            corporationPushReq.setDjsmc("广州市");
                            corporationPushReq.setYyzzh(corporationDO.getUnifiedSocialCode());

                            CorporationPushReq.CustomerPushReq customerPushReq = new CorporationPushReq.CustomerPushReq();
                            customerPushReq.setSfb("广东省");
                            customerPushReq.setKhidb(subMember.getCode());
                            customerPushReq.setKhmcb(subMember.getName());
                            customerPushReq.setBrandName(subMember.getBrandName());
                            customerPushReq.setBrandCode(subMember.getBrandCode());
                            corporationPushReq.setData(Lists.newArrayList(customerPushReq));
                            String result = eosApiService.pushCorporation(corporationPushReq);
                        }
                    });
                }
            });
        }
    }

    /**
     * 发送企业认证成功分佣处理MQ消息
     * @param relationDO 会员关系
     */
    private void sendCertificationSuccessMessage(MemberRelationDO relationDO) {
        try {
            MemberDO subMember = relationDO.getSubMember();

            // 获取用户信息
            UserDO userDO = userRepository.findFirstByMemberIdAndUserType(subMember.getId(), UserTypeEnum.ADMIN.getCode());
            if (Objects.isNull(userDO)) {
                log.warn("未找到会员对应的用户信息，跳过发送企业认证成功分佣消息，会员ID：{}", subMember.getId());
                return;
            }

            // 获取企业信息
            CorporationDO corporationDO = null;
            if (Objects.nonNull(subMember.getCorporationId())) {
                corporationDO = corporationRepository.findById(subMember.getCorporationId()).orElse(null);
            }

            // 构建消息DTO
            MemberCertificationSuccessDTO certificationDTO = new MemberCertificationSuccessDTO();
            certificationDTO.setUserId(userDO.getId());
            certificationDTO.setUserCode(userDO.getCode());
            certificationDTO.setMemberId(subMember.getId());
            certificationDTO.setMemberCode(subMember.getCode());
            if (Objects.nonNull(corporationDO)) {
                certificationDTO.setCorporationId(corporationDO.getId());
                certificationDTO.setCorporationCode(corporationDO.getCode());
                certificationDTO.setCorporationName(corporationDO.getName());
            }
            certificationDTO.setCertificationTime(System.currentTimeMillis());

            // 发送MQ消息
            String messageBody = SerializeUtil.serialize(certificationDTO);
            mqUtils.sendMsg(MemberMqConstant.MEMBER_CERTIFICATION_COMMISSION_EXCHANGE,
                           MemberMqConstant.MEMBER_CERTIFICATION_COMMISSION_ROUTING_KEY,
                           messageBody);

            log.info("发送企业认证成功分佣处理MQ消息成功，用户ID：{}，会员ID：{}，企业ID：{}",
                    userDO.getId(), subMember.getId(), certificationDTO.getCorporationId());
        } catch (Exception e) {
            log.error("发送企业认证成功分佣处理MQ消息失败，会员关系ID：{}", relationDO.getId(), e);
            // 这里不抛出异常，避免影响认证流程
        }
    }
}
