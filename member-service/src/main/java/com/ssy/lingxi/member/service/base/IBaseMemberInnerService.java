package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.bo.AddMemberBO;
import com.ssy.lingxi.member.entity.bo.ProcessBO;
import com.ssy.lingxi.member.entity.bo.UpdateMemberRelationBO;
import com.ssy.lingxi.member.entity.bo.UpdatePlatformMemberBO;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import com.ssy.lingxi.member.model.req.validate.MemberQualityReq;

import java.util.List;
import java.util.Map;

/**
 * 会员、用户内部服务类接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-06
 */
public interface IBaseMemberInnerService {
    /**
     * 新增平台会员
     * @param memberBO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    MemberRelationDO addPlatformMember(AddMemberBO memberBO, Integer roleTag);

    /**
     * 平台新增会员异步通知报表服务、实时消息服务、支付服务
     * @param memberRelationDOList 会员上下级关系列表
     */
    void addPlatformMemberInform(List<MemberRelationDO> memberRelationDOList);

    /**
     * 新增平台会员(循环入库的情况下，先不做远程调用)
     * @param memberBO 接口参数
     * @return 操作结果
     */
    MemberRelationDO addPlatformMembers(AddMemberBO memberBO);


    /**
     * 新增会员上下级关系（非平台会员）
     * @param upperMember 上级会员
     * @param upperRole   上级会员角色
     * @param subMemberPlatformRelation   下级会员的平台会员关系
     * @param subRole     下级会员角色
     * @param subMemberType 下级会员的会员类型
     * @param subMemberLevel  下级会员的等级
     * @param subMemberLevelTypeEnum 下级会员的等级类型
     * @param roleTag 角色标签
     * @param outerFlag 外部系统标识
     * @return 操作结果
     */
    void addMemberRelation(MemberDO upperMember, MemberRoleDO upperRole, MemberRelationDO subMemberPlatformRelation, MemberRoleDO subRole, Integer subMemberType, Integer subMemberLevel, Integer subMemberLevelTypeEnum, Integer roleTag, Boolean outerFlag);

    /**
     * 新增会员异步通知报表服务、实时消息服务、支付服务
     * @param memberList 会员上下级关系列表
     */
    void addMemberRelationInform(List<MemberRelationDO> memberList);

    /**
     * 新增会员上下级关系（非平台会员, 循环入库的情况下，先不做远程调用）
     * @param upperMember 上级会员
     * @param upperRole   上级会员角色
     * @param subMemberPlatformRelation   下级会员的平台会员关系
     * @param subRole     下级会员角色
     * @param subMemberType 下级会员的会员类型
     * @param subMemberLevel  下级会员的等级
     * @param subMemberLevelTypeEnum 下级会员的等级类型
     * @return 操作结果
     */
    MemberRelationDO addMemberRelations(MemberDO upperMember, MemberRoleDO upperRole, MemberRelationDO subMemberPlatformRelation, MemberRoleDO subRole, Integer subMemberType, Integer subMemberLevel, Integer subMemberLevelTypeEnum);


    /**
     * 修改平台会员信息
     * @param updateBO 接口参数
     * @return 操作结果
     */
    void updatePlatformMember(UpdatePlatformMemberBO updateBO);

    /**
     * 修改会员上下级关系
     * @param updateRelationBO 接口参数
     * @return 操作结果
     */
    void updateMemberRelation(UpdateMemberRelationBO updateRelationBO);

    /**
     * 删除平台会员
     * @param memberDO 要删除的会员
     * @param relationDO 要删除的与平台会员的上下级关系
     * @return 操作结果
     */
    void deletePlatformMember(MemberDO memberDO, MemberRelationDO relationDO);

    /**
     * 删除上下级关系
     * @param relationDO 上下级关系
     * @return 操作结果
     */
    void deleteMemberRelation(MemberRelationDO relationDO);

    /**
     * 删除平台会员和角色
     * @param relationDO 平台会员上下级关系
     * @return 删除结果
     */
    void deletePlatformMemberAndRole(MemberRelationDO relationDO);

    /**
     * 会员只有一个角色的情况下，彻底删除平台会员和角色
     * @param memberDO 要删除的会员
     * @param memberRoleDO 要删除的角色
     * @return 删除结果
     */
    void deleteMemberCompletely(MemberDO memberDO, MemberRoleDO memberRoleDO);

    /**
     * 新增角色
     * @param processBO 角色关联的平台会员审核流程
     * @param operatorRoleName 登录用户的当前会员角色
     * @param adminMemberDO 平台管理员
     * @param adminRole 平台管理员的角色
     * @param memberDO 会员
     * @param memberRoleDO 新增的角色
     * @param memberAdminRole 会员下属的管理员角色
     * @param memberAdminUser 会员下属的管理员
     * @param memberName 会员名称
     * @param memberRegisterDetails 注册资料
     */
    MemberRelationDO addMemberRole(ProcessBO processBO, String operatorRoleName, MemberDO adminMemberDO, MemberRoleDO adminRole, MemberDO memberDO, MemberRoleDO memberRoleDO, UserRoleDO memberAdminRole, UserDO memberAdminUser, String memberName, List<MemberRegisterDetailDO> memberRegisterDetails);

    /**
     * 下级会员申请成为上级会员的下级
     * @param upperMemberId 上级会员Id
     * @param upperRoleId    上级会员角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param depositDetails 入库资料
     * @param qualities 资质证明文件
     * @return 操作结果
     */
    WrapperResp<Void> applyToBeSubMember(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Map<String, Object> depositDetails, List<MemberQualityReq> qualities);

    /**
     * 会员被淘汰后，重新申请为下级会员
     * @param relationDO 被淘汰的会员关系
     * @return 操作结果
     */
    WrapperResp<Void> reApplyForSubMember(MemberRelationDO relationDO);
}
