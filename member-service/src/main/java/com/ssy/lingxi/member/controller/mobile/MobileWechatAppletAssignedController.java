package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityAssignedMemberQueryDataReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberAssignedPageQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberAssignedSearchConditionVO;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilityAssignedService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商能力-供应商管理-待分配供应商相关接口（App）
 * <AUTHOR>
 * @since 2022/5/24 14:12
 */

@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/assigned")
public class MobileWechatAppletAssignedController {

    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IMemberAbilityAssignedService memberAbilityAssignedService;

    /**
     * 列表查询页面中各个下拉选择框的内容
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/pageitems")
    public WrapperResp<MemberAssignedSearchConditionVO> getPageCondition(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityAssignedService.getPageCondition(headers, loginUser, roleTag));
    }

    /**
     * 分页、模糊查询供应商
     *
     * @param headers Http头部信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberAssignedPageQueryResp>> pageMembers(@RequestHeader HttpHeaders headers, @Valid MemberAbilityAssignedMemberQueryDataReq queryVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityAssignedService.pageMembers(loginUser, queryVO, roleTag));
    }

    /**
     * 操作用户领取供应商
     *
     * @param headers Http头部信息
     * @param subMemberList  接口参数
     * @return 操作结果
     */
    @PostMapping("/bind")
    public WrapperResp<Void> bindOperator(@RequestHeader HttpHeaders headers, @RequestBody @Valid List<SubMemberIdRoleIdDataReq> subMemberList) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
         memberAbilityAssignedService.bindOperator(loginUser, subMemberList);
        return WrapperUtil.success();
    }
}
