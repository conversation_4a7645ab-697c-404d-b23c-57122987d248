package com.ssy.lingxi.member.serviceImpl;

import com.ssy.lingxi.common.model.resp.select.SelectLongResp;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.repository.MemberRoleRepository;
import com.ssy.lingxi.member.service.ISelectService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/13
 */
@RequiredArgsConstructor
@Service
public class SelectServiceImpl implements ISelectService {
    private final MemberRoleRepository memberRoleRepository;

    @Override
    public List<SelectLongResp> getMemberRoleList() {
        return memberRoleRepository.findAllByStatusAndRelType(EnableDisableStatusEnum.ENABLE.getCode(), MemberRelationTypeEnum.OTHER.getCode())
                .stream()
                .sorted(Comparator.comparing(MemberRoleDO::getId))
                .map(role -> new SelectLongResp(role.getId(), role.getRoleName()))
                .collect(Collectors.toList());
    }
}
