package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifecycleStagesDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 生命周期阶段配置Repository
 */
@Repository
public interface MemberLifecycleStagesRepository extends JpaRepository<MemberLifecycleStagesDO, Long>, JpaSpecificationExecutor<MemberLifecycleStagesDO> {


    List<MemberLifecycleStagesDO> findByMemberIdAndRoleIdAndRoleTag(Long memberId, Long memberRoleId, Integer roleTag);

    void deleteByMemberIdAndRoleIdAndRoleTag(Long memberId, Long memberRoleId, Integer roleTag);

    MemberLifecycleStagesDO findByMemberIdAndRoleIdAndRoleTagAndLifecycleStagesNum(Long memberId, Long roleId, Integer roleTag, Integer lifecycleStagesNum);

    List<MemberLifecycleStagesDO> findByMemberIdAndRoleId(Long memberId, Long memberRoleId);
}
