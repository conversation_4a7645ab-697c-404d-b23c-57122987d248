package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.resp.basic.UserQueryResp;

/**
 * (内部)用户详细信息查询服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-27
 */
public interface IBaseUserDetailService {

    /**
     * 分页查询用户列表
     * @param memberId 会员Id
     * @param name     用户姓名
     * @param orgName  组织机构名称
     * @param jobTitle 职位
     * @param current  当前页
     * @param pageSize 每页行数
     * @param includeSuperAdmin 是否包含会员超级管理员
     * @return 查询结果
     */
    PageDataResp<UserQueryResp> pageUsers(Long memberId, String name, String orgName, String jobTitle, int current, int pageSize, boolean includeSuperAdmin);

    /**
     * 分页查询用户列表
     * @param memberId          会员Id
     * @param keyword           用户姓名/组织机构名称
     * @param current           当前页
     * @param pageSize          每页行数
     * @param includeSuperAdmin 是否包含会员超级管理员
     * @return 查询结果
     */
   PageDataResp<UserQueryResp> pageUsers(Long memberId, String keyword, int current, int pageSize, boolean includeSuperAdmin);
}
