package com.ssy.lingxi.member.serviceImpl.mobile;

import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.member.TokenStrategyEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.TokenContext;
import com.ssy.lingxi.component.base.model.dto.login.AccessTokenDTO;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.TokenUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.config.MemberRefreshConfig;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.entity.bo.login.LoginContext;
import com.ssy.lingxi.member.entity.bo.login.MobileLoginBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.enums.LoginStrategyEnum;
import com.ssy.lingxi.member.handler.listener.event.RedisKeyRemoveEvent;
import com.ssy.lingxi.member.model.dto.MobileLoginDTO;
import com.ssy.lingxi.member.model.dto.MobilePhoneLoginDTO;
import com.ssy.lingxi.member.model.req.login.MobileShopTypeReq;
import com.ssy.lingxi.member.model.req.login.MobileSwitchRoleReq;
import com.ssy.lingxi.member.model.req.login.PhoneLoginSmsCode;
import com.ssy.lingxi.member.model.req.mobile.MobileMemberAuthCodeReq;
import com.ssy.lingxi.member.model.req.mobile.MobileWxLoginReq;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.ISmsFeignService;
import com.ssy.lingxi.member.service.mobile.IMobileLoginService;
import com.ssy.lingxi.support.api.enums.SmsTemplateEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpHeaders;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * App - 用户登录接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-05
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class MobileLoginServiceImpl implements IMobileLoginService {
    private final UserRepository userRepository;
    private final MemberRelationRepository relationRepository;
    private final IBaseMemberCacheService memberCacheService;
    private final IBaseMemberLevelAsyncService baseMemberLevelAsyncService;
    private final IBaseMemberCreditAsyncService baseMemberCreditAsyncService;
    private final ISmsFeignService smsFeignService;
    private final JdbcTemplate jdbcTemplate;
    private final IRedisUtils redisUtils;
    private final MemberRefreshConfig memberRefreshConfig;
    private final IBaseLoginService baseLoginService;
    private final IBaseTokenManageService tokenManageService;
    private final ApplicationEventPublisher eventPublisher;

    @Override
    public MobileLoginBO accountOrPhoneLogin(HttpHeaders headers, MobileLoginDTO loginReq) {
        memberCacheService.checkMobileRequestHeader(headers);

        // 检查用户是否存在
        UserDO userDO = baseLoginService.getSpecifyUserDOIfNecessary(loginReq.getAccount(), loginReq.getUserId(), LoginStrategyEnum.PASSWORD.getCode());
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 检查是否业务员
        if (loginReq.getCheckSalesMan()) {
            BusinessAssertUtil.isTrue(userDO.getIsSales(), ResponseCodeEnum.MC_MS_MEMBER_USER_IS_NOT_SALES);
        }

        // 检查会员是否存在
        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        // 判断登录失败次数是否超过限制并校验密码
        baseLoginService.checkLoginFailuresAndPwd(userDO.getId(), userDO.getPassword(), loginReq.getPassword());

        // 如果关系为空，表示没有合法角色（小程序相关需要消费者角色，业务员需要服务提供者角色）
        List<MemberRelationDO> relDOList = relationRepository.findAll(baseLoginService.getMobileRelationSpec(memberDO, loginReq.getCheckSalesMan()));
        BusinessAssertUtil.notEmpty(relDOList, ResponseCodeEnum.MC_MS_USER_HAS_NO_AVAILABLE_CONSUMER_ROLE);

        // 调用通用登陆层，并获取登陆上下文
        LoginContext loginContext = baseLoginService.baseLogin(relDOList, memberDO, userDO, null,
                new TokenContext(memberDO.getId(), userDO.getId(), SystemSourceEnum.BUSINESS_MOBILE, TokenStrategyEnum.LOGIN));

        // 调用通用token缓存层，维护token相关数据，并写缓存
        tokenManageService.distUsableToken(loginContext);

        // 异步计算会员登录获得的积分、会员注册年限获得的信用积分
        baseMemberLevelAsyncService.calculateMemberLoginScore(loginContext.getRelId(), SystemSourceEnum.BUSINESS_MOBILE.getCode());
        baseMemberCreditAsyncService.calculateMemberRegisterYearsCredit(loginContext.getRelId());

        // 异步记录最后一次登陆时间
        baseLoginService.updateUserLastLoginTimeAsync(userDO.getId());

        return new MobileLoginBO(loginContext);
    }

    @Override
    public void sendPhoneLoginSmsCode(HttpHeaders headers, PhoneLoginSmsCode phoneReq) {
        memberCacheService.checkMobileRequestHeader(headers);

        UserDO userDO = userRepository.findFirstByTelCodeAndPhoneAndRelType(phoneReq.getTelCode(), phoneReq.getPhone(), MemberRelationTypeEnum.OTHER.getCode());
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 发送验证码
        baseLoginService.sendPhoneLoginSmsCode(phoneReq, userDO, MemberRedisConstant.LOGIN_MOBILE_BY_PHONE_REDIS_KEY_PREFIX + userDO.getPhone(), SmsTemplateEnum.MOBILE_LOGIN.getCode());
    }

    @Override
    public MobileLoginBO phoneLogin(HttpHeaders headers, MobilePhoneLoginDTO loginReq) {
        memberCacheService.checkMobileRequestHeader(headers);

        // 从用户表中读取用户信息
        UserDO userDO = baseLoginService.getSpecifyUserDOIfNecessary(loginReq.getPhone(), loginReq.getUserId(), LoginStrategyEnum.MOBILE_PHONE.getCode());
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 检查是否业务员
        if (loginReq.getCheckSalesMan()) {
            BusinessAssertUtil.isTrue(userDO.getIsSales(), ResponseCodeEnum.MC_MS_MEMBER_USER_IS_NOT_SALES);
        }

        // 检查会员是否存在
        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        // 获取缓存的短信验证码
        String smsRedisKey = MemberRedisConstant.LOGIN_MOBILE_BY_PHONE_REDIS_KEY_PREFIX + userDO.getPhone();
        String smsCode = memberCacheService.getString(smsRedisKey);

        // 判断登录失败次数是否超过限制并校验验证码
        baseLoginService.checkLoginFailuresAndSmsCode(userDO.getId(), smsCode, loginReq.getSmsCode());

        // 如果关系为空，表示没有合法角色（小程序相关需要消费者角色，业务员需要服务提供者角色）
        List<MemberRelationDO> relDOList = relationRepository.findAll(baseLoginService.getMobileRelationSpec(memberDO, loginReq.getCheckSalesMan()));
        BusinessAssertUtil.notEmpty(relDOList, ResponseCodeEnum.MC_MS_USER_HAS_NO_AVAILABLE_CONSUMER_ROLE);

        // 调用通用登陆层，并获取登陆上下文
        LoginContext loginContext = baseLoginService.baseLogin(relDOList, memberDO, userDO, null,
                new TokenContext(memberDO.getId(), userDO.getId(), SystemSourceEnum.BUSINESS_MOBILE, TokenStrategyEnum.LOGIN));

        // 调用通用token缓存层，维护token相关数据，并写缓存
        tokenManageService.distUsableToken(loginContext);

        // 异步计算会员登录获得的积分、会员注册年限获得的信用积分
        baseMemberLevelAsyncService.calculateMemberLoginScore(loginContext.getRelId(), SystemSourceEnum.BUSINESS_MOBILE.getCode());
        baseMemberCreditAsyncService.calculateMemberRegisterYearsCredit(loginContext.getRelId());

        // 异步记录最后一次登陆时间
        baseLoginService.updateUserLastLoginTimeAsync(userDO.getId());

        // 删除使用过的验证码
        eventPublisher.publishEvent(new RedisKeyRemoveEvent(this, Collections.singletonList(smsRedisKey)));

        return new MobileLoginBO(loginContext);
    }

    @Override
    public MobileLoginBO loginWithToken(HttpHeaders headers, MobileShopTypeReq shopTypeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        //登录来源
        if (!loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MOBILE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.NEED_LOGIN_FROM_BUSINESS_APP);
        }

        String accessToken = headers.getFirst(Constant.ACCESS_TOKEN);

        return reLoginWithTokenOrSpecifyRoleId(accessToken, loginUser.getUserId(), loginUser.getMemberRoleId());
    }

    @Override
    public MobileLoginBO switchLoginRole(HttpHeaders headers, MobileSwitchRoleReq roleReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);

        //登录来源
        if (!loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MOBILE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.NEED_LOGIN_FROM_BUSINESS_APP);
        }

        String accessToken = headers.getFirst(Constant.ACCESS_TOKEN);

        return reLoginWithTokenOrSpecifyRoleId(accessToken, loginUser.getUserId(), roleReq.getRoleId());
    }

    private MobileLoginBO reLoginWithTokenOrSpecifyRoleId(String accessToken, Long userId, Long roleId) {
        //从数据库中读取用户信息
        UserDO userDO = userRepository.findById(userId).orElseThrow(() -> new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT));

        // 检查会员是否存在
        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        // 如果关系为空，表示没有可用角色
        List<MemberRelationDO> relDOList = relationRepository.findAll(baseLoginService.getMobileRelationSpec(memberDO, null));
        BusinessAssertUtil.notEmpty(relDOList, ResponseCodeEnum.MC_MS_USER_HAS_NO_AVAILABLE_CONSUMER_ROLE);

        // 解析accessToken
        AccessTokenDTO accessTokenDTO = TokenUtil.analysisAccessToken(accessToken);

        // 调用通用登陆层，并获取登陆上下文
        LoginContext loginContext = baseLoginService.baseLogin(relDOList, memberDO, userDO, roleId,
                new TokenContext(memberDO.getId(), roleId, userDO.getId(), SystemSourceEnum.BUSINESS_MOBILE, TokenStrategyEnum.SWITCH_ROLES, accessTokenDTO.getRawToken()));

        // 调用通用token缓存层，维护token相关数据，并写缓存
        tokenManageService.distUsableToken(loginContext);

        return new MobileLoginBO(loginContext);
    }

    @Override
    public void authCodeActive(HttpHeaders headers, MobileMemberAuthCodeReq authCodeReq) {
        // 检查手机是否登录
        UserLoginCacheDTO userLoginCacheDTO = memberCacheService.needLoginFromMobile(headers);

        memberCacheService.activeAuthCode(authCodeReq.getAuthCode(), userLoginCacheDTO);
    }

    /**
     * 微信登录
     *
     * @param headers Http头部信息
     * @param req     接口参数
     * @return 登录用户信息
     */
    @Override
    public MobileLoginBO wxLogin(HttpHeaders headers, MobileWxLoginReq req) {
        String userPhone = redisUtils.stringGet(String.format(MemberRedisConstant.WX_LOGIN_PHONE_CODE_PREFIX, req.getPhoneCode()), RedisConstant.REDIS_USER_INDEX);

        log.info("微信快速登录：{} ======= {}", String.format(MemberRedisConstant.WX_LOGIN_PHONE_CODE_PREFIX, req.getPhoneCode()), userPhone);

        BusinessAssertUtil.notBlank(userPhone, ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);

        memberCacheService.checkMobileRequestHeader(headers);

        // 从用户表中读取用户信息
        UserDO userDO = baseLoginService.getSpecifyUserDOIfNecessary(userPhone, req.getUserId(), LoginStrategyEnum.MOBILE_PHONE.getCode());
        BusinessAssertUtil.notNull(userDO, ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 检查是否业务员
        if (BooleanUtils.isTrue(req.getCheckSalesMan())) {
            BusinessAssertUtil.isTrue(userDO.getIsSales(), ResponseCodeEnum.MC_MS_MEMBER_USER_IS_NOT_SALES);
        }

        // 检查会员是否存在
        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        // 如果关系为空，表示没有合法角色（小程序相关需要消费者角色，业务员需要服务提供者角色）
        List<MemberRelationDO> relDOList = relationRepository.findAll(baseLoginService.getMobileRelationSpec(memberDO, req.getCheckSalesMan()));
        BusinessAssertUtil.notEmpty(relDOList, ResponseCodeEnum.MC_MS_USER_HAS_NO_AVAILABLE_CONSUMER_ROLE);

        // 调用通用登陆层，并获取登陆上下文
        LoginContext loginContext = baseLoginService.baseLogin(relDOList, memberDO, userDO, null,
                new TokenContext(memberDO.getId(), userDO.getId(), SystemSourceEnum.BUSINESS_MOBILE, TokenStrategyEnum.LOGIN));

        // 调用通用token缓存层，维护token相关数据，并写缓存
        tokenManageService.distUsableToken(loginContext);

        // 异步计算会员登录获得的积分、会员注册年限获得的信用积分
        baseMemberLevelAsyncService.calculateMemberLoginScore(loginContext.getRelId(), SystemSourceEnum.BUSINESS_MOBILE.getCode());
        baseMemberCreditAsyncService.calculateMemberRegisterYearsCredit(loginContext.getRelId());

        // 异步记录最后一次登陆时间
        baseLoginService.updateUserLastLoginTimeAsync(userDO.getId());

        return new MobileLoginBO(loginContext);
    }

}
