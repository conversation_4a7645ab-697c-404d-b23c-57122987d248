package com.ssy.lingxi.member.service.web.comment;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.comment.WaitCommentOrderQueryDataReq;
import com.ssy.lingxi.member.model.resp.comment.WaitCommentOrderPageResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员订单评价服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/3
 */
public interface IMemberOrderCommentService {

    /**
     * 采购会员 - 待评价订单
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<WaitCommentOrderPageResp> pageBuyerWaitCommentOrder(HttpHeaders headers, WaitCommentOrderQueryDataReq pageVO);

    /**
     * 供应会员 - 待评价订单
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<WaitCommentOrderPageResp> pageVendorWaitCommentOrder(HttpHeaders headers, WaitCommentOrderQueryDataReq pageVO);

    /**
     * 根据订单id集合查询待评价数量
     * @param orderIdList 订单集合Id
     * @return 查询结果
     */
    Integer findWaitCommentOrder(List<Long> orderIdList, Integer type, UserLoginCacheDTO loginUser);
}
