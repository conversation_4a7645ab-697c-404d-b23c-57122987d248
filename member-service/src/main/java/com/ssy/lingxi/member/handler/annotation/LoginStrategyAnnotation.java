package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.enums.LoginStrategyEnum;
import com.ssy.lingxi.member.handler.validator.LoginStrategyValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 登陆策略枚举值校验器
 *
 * <AUTHOR>
 * @version 3.0.0
 * @see LoginStrategyEnum
 * @since 2024/9/11
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = LoginStrategyValidator.class)
public @interface LoginStrategyAnnotation {
    String message() default "非法的登陆模式";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
