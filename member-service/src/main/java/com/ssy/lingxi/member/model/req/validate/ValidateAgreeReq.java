package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.member.handler.annotation.MemberValidateAgreeAnnotation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 会员审核 - 审核接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-22
 */
@Data
public class ValidateAgreeReq implements Serializable {
    private static final long serialVersionUID = -5946919880894902726L;

    /**
     * 审核内容Id
     */
    @NotNull(message = "审核内容Id要大于0")
    @Positive(message = "审核内容Id要大于0")
    private Long validateId;

    /**
     * 提交审批的状态：0-不通过；1-通过
     */
    @NotNull(message = "审批状态不能为空：0-不通过 1-通过")
    @MemberValidateAgreeAnnotation
    private Integer agree;

    /**
     * 审核原因
     */
    @Size(max = 120, message = "不通过原因最大120个字符")
    private String reason;

    /**
     * 经营品牌代码
     */
    @Size(max = 20, message = "经营品牌代码最大20个字符")
    private String brandCode;

    /**
     * 客户简称
     */
    @Size(max = 20, message = "客户简称最大20个字符")
    private String simpleMemberName;
}
