package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.dto.engine.EngineResultDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.PolicyResultResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.common.model.resp.engine.RuleEngineConfigResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.EngineRuleUtil;
import com.ssy.lingxi.component.base.util.JpaUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.engine.api.enums.ProcessTypeDetailEnum;
import com.ssy.lingxi.engine.api.feign.IProcessEngineRuleFeign;
import com.ssy.lingxi.engine.api.feign.IRuleEngineConfigFeign;
import com.ssy.lingxi.engine.api.model.req.PolicyReq;
import com.ssy.lingxi.engine.api.model.req.RuleEngineByPredicateReq;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskResultBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberChangeRequestFormDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberChangeRequestFormItemDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifecycleStagesDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.dto.MemberChangeRequestFormDTO;
import com.ssy.lingxi.member.model.dto.MemberChangeRequestFormMessageDTO;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityMaintenanceMemberQueryDataReq;
import com.ssy.lingxi.member.model.resp.basic.RegisterAreaResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberHistoryService;
import com.ssy.lingxi.member.service.base.IBaseMemberValidateService;
import com.ssy.lingxi.member.service.base.IBaseSiteService;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberLifecycleService;
import com.ssy.lingxi.member.service.web.IPlatformMemberRoleRuleService;
import com.ssy.lingxi.member.util.FileObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 供应商能力 - 供应商生命周期管理服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
@Slf4j
@Service
public class MemberLifecycleServiceImpl implements IMemberLifecycleService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseMemberHistoryService memberHistoryService;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private MemberRelationRepository memberRelationRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberChangeRequestFormRepository memberChangeRequestFormRepository;

    @Resource
    private MemberChangeRequestFormItemRepository memberChangeRequestFormItemRepository;

    @Resource
    private MemberLifecycleStagesRepository memberLifecycleStagesRepository;

    @Resource
    private IMessageFeignService messageFeignService;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private IPlatformMemberRoleRuleService roleRuleService;

    @Resource
    private IBaseSiteService siteService;

    @Resource
    private MemberProcessRuleRepository memberProcessRuleRepository;

    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    @Resource
    private IRuleEngineConfigFeign ruleEngineConfigFeign;

    @Resource
    private IProcessEngineRuleFeign processEngineRuleFeign;

    private final List<MemberOuterStatusEnum> outerStatusEnums = Stream.of(
            MemberOuterStatusEnum.DEPOSITING,
            MemberOuterStatusEnum.DEPOSITORY_PASSED,
            MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED,
            MemberOuterStatusEnum.MODIFYING,
            MemberOuterStatusEnum.MODIFY_PASSED,
            MemberOuterStatusEnum.MODIFY_NOT_PASSED
    ).collect(Collectors.toList());

    @Override
    public MemberArchivesManagementConditionResp getPageCondition(HttpHeaders headers, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberArchivesManagementConditionResp conditionVO = new MemberArchivesManagementConditionResp();

        //获取是否开启saas多租户部署
        Boolean enableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        List<RoleIdAndNameResp> roleList;
        if (enableMultiTenancy) {//有开启
            //会员角色（按照Id升序排序）
            roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.subMemberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
        } else {//未开启
            //会员角色（按照Id升序排序）
            roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), roleTag);
        }
        roleList.add(0, new RoleIdAndNameResp(0L, MemberStringEnum.ALL.getName()));
        conditionVO.setRoles(roleList);

        //内部状态
        List<DropdownItemResp> itemList = MemberInnerStatusEnum.toDropdownItemResps();
        itemList.forEach(d -> {
            if (NumberUtil.notNullOrZero(roleTag)) {
                d.setText(d.getText().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag)));
            }
        });
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setInnerStatus(itemList);

        //外部状态
        itemList = outerStatusEnums.stream().map(e -> new DropdownItemResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
        itemList.forEach(d -> {
            if (NumberUtil.notNullOrZero(roleTag)) {
                d.setText(d.getText().replace(RoleTagEnum.MEMBER.getName(), RoleTagEnum.getName(roleTag)));
            }
        });
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setOuterStatus(itemList);

        //币别
        itemList = CurrencyTypeEnum.toEditableDropdownList();
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setCurrencyType(itemList);

        return conditionVO;
    }

    @Override
    public List<StatusResp> listMemberChangeRequestStatus(HttpHeaders headers) {
        List<StatusResp> statusRespList = Stream.of(MemberChangeRequestFormStatusEnum.values()).map(e -> new StatusResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
        statusRespList.add(0, new StatusResp(0, MemberStringEnum.ALL.getName()));
        return statusRespList;
    }

    @Override
    public PageDataResp<MemberArchivesManagementQueryResp> memberArchivesManagementPage(HttpHeaders headers, MemberAbilityMaintenanceMemberQueryDataReq queryVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Specification<MemberRelationDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            //必须是至少审核通过一次的状态
            list.add(criteriaBuilder.equal(root.get("verified").as(Long.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));

            //注册开始时间
            if (org.springframework.util.StringUtils.hasLength(queryVO.getStartDate())) {
                LocalDateTime startDate = LocalDateTime.parse(queryVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), startDate));
            }

            //注册结束时间
            if (org.springframework.util.StringUtils.hasLength(queryVO.getEndDate())) {
                LocalDateTime endDate = LocalDateTime.parse(queryVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), endDate));
            }

            //内部状态
            if (NumberUtil.isNullOrZero(queryVO.getInnerStatus())) {
                list.add(criteriaBuilder.notEqual(root.get("innerStatus").as(Integer.class), MemberInnerStatusEnum.NEW.getCode()));
            } else {
                list.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), queryVO.getInnerStatus()));
            }

            //外部状态
            if (NumberUtil.isNullOrZero(queryVO.getOuterStatus())) {
                list.add(root.get("outerStatus").as(Integer.class).in(outerStatusEnums.stream().map(MemberOuterStatusEnum::getCode).collect(Collectors.toList())));
            } else {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), queryVO.getOuterStatus()));
            }

            //会员角色
            if (NumberUtil.notNullOrZero(queryVO.getRoleId())) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), queryVO.getRoleId()));
            }

            //会员名称
            if (org.springframework.util.StringUtils.hasLength(queryVO.getName()) || NumberUtil.notNullOrZero(queryVO.getSource())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                if (org.springframework.util.StringUtils.hasLength(queryVO.getName())) {
                    list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + queryVO.getName().trim() + "%"));
                }
            }

            if (!CollectionUtils.isEmpty(queryVO.getMemberConfigs()) && NumberUtil.notNullOrZero(queryVO.getRoleId())) {
                MemberRoleDO memberRoleDO = memberRoleRepository.findById(queryVO.getRoleId()).orElse(null);
                List<MemberRegisterConfigDO> memberRegisterConfigDOList = memberProcessRuleRepository
                        .findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(loginUser.getMemberId(), loginUser.getMemberRoleId(),
                                memberRoleDO, EnableDisableStatusEnum.ENABLE.getCode(), Sort.by("id").ascending()).getConfigs().stream().filter(memberConfigDO -> CommonBooleanEnum.YES.getCode().equals(memberConfigDO.getAllowSelect())).collect(Collectors.toList());
                Join<Object, Object> depositDetailsJoin = root.join("depositDetails", JoinType.LEFT);
                Join<Object, Object> depositoryDetailSelectJoin = root.join("depositoryDetailSelect", JoinType.LEFT);
                List<Predicate> memberConfigList = new ArrayList<>();

                for (Map.Entry<String, Object> entry : queryVO.getMemberConfigs().entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue() instanceof HashMap ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());
                    if (org.springframework.util.StringUtils.hasLength(value)) {
                        MemberRegisterConfigDO configDO = memberRegisterConfigDOList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
                        if (!Objects.isNull(configDO)) {
                            if (MemberConfigFieldTypeEnum.AREA.getMessage().equals(configDO.getFieldType())) {
                                RegisterAreaResp area = SerializeUtil.deserialize(value, RegisterAreaResp.class);
                                if (!Objects.isNull(area)) {
                                    String cityCode = area.getCityCode();
                                    String districtCode = area.getDistrictCode();
                                    String provinceCode = area.getProvinceCode();
                                    if (org.springframework.util.StringUtils.hasLength(provinceCode) && org.springframework.util.StringUtils.hasLength(cityCode) && org.springframework.util.StringUtils.hasLength(districtCode)) {
                                        memberConfigList.add(criteriaBuilder.and(criteriaBuilder.equal(depositDetailsJoin.get("fieldName").as(String.class), key), criteriaBuilder.equal(depositDetailsJoin.get("provinceCode").as(String.class), provinceCode), criteriaBuilder.equal(depositDetailsJoin.get("cityCode").as(String.class), cityCode), criteriaBuilder.equal(depositDetailsJoin.get("districtCode").as(String.class), districtCode)));
                                    } else if (org.springframework.util.StringUtils.hasLength(provinceCode) && org.springframework.util.StringUtils.hasLength(cityCode)) {
                                        memberConfigList.add(criteriaBuilder.and(criteriaBuilder.equal(depositDetailsJoin.get("fieldName").as(String.class), key), criteriaBuilder.equal(depositDetailsJoin.get("provinceCode").as(String.class), provinceCode), criteriaBuilder.equal(depositDetailsJoin.get("cityCode").as(String.class), cityCode)));
                                    } else if (org.springframework.util.StringUtils.hasLength(provinceCode)) {
                                        memberConfigList.add(criteriaBuilder.and(criteriaBuilder.equal(depositDetailsJoin.get("fieldName").as(String.class), key), criteriaBuilder.equal(depositDetailsJoin.get("provinceCode").as(String.class), provinceCode)));
                                    }
                                }
                            } else {
                                memberConfigList.add(criteriaBuilder.and(criteriaBuilder.like(depositoryDetailSelectJoin.get("detail").as(String.class), "%" + key + ":" + value.trim() + "%")));
                            }
                        }
                    }
                }
                if (memberConfigList.size() > 0) {
                    Predicate[] orP = new Predicate[memberConfigList.size()];
                    list.add(criteriaBuilder.and(memberConfigList.toArray(orP)));
                }
            }

            //币别
            if (NumberUtil.notNullOrZero(queryVO.getCurrencyType())) {
                list.add(criteriaBuilder.equal(root.get("classification").get("currencyType").as(Integer.class), queryVO.getCurrencyType()));
            }

            //会员编码
            if (org.springframework.util.StringUtils.hasLength(queryVO.getCode())) {
                list.add(criteriaBuilder.like(root.get("classification").get("code").as(String.class), "%" + queryVO.getCode().trim() + "%"));
            }

            //主营品类
            List<Long> categoryId = queryVO.getCategoryId();
            if (!CollectionUtils.isEmpty(categoryId)) {
                Join<Object, Object> classificationJoin = root.join("classification", JoinType.LEFT);
                Join<Object, Object> categoriesJoin = classificationJoin.join("categories", JoinType.LEFT);
                list.add(criteriaBuilder.like(categoriesJoin.get("categoryIdList").as(String.class), "%:" + categoryId.get(categoryId.size() - 1) + "}%"));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), roleTag));
            }

            // 下级会员ID
            if (NumberUtil.notNullOrZero(queryVO.getSubMemberId())) {
                list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), queryVO.getSubMemberId()));
            }

            // 生命周期阶段Id
            if (NumberUtil.notNullOrZero(queryVO.getLifeCycleStageId())) {
                Join<Object, Object> lifeCycleStageJoin = root.join("memberLifecycleStages", JoinType.LEFT);
                list.add(criteriaBuilder.equal(lifeCycleStageJoin.get("id").as(Long.class), queryVO.getLifeCycleStageId()));
            }

            query.groupBy(root.get("subMember")
                    , root.get("id")
                    , root.get("subRole")
                    , root.get("subRoleId")
                    , root.get("subRoleName")
                    , root.get("createTime")
                    , root.get("levelRight")
                    , root.get("status")
                    , root.get("innerStatus")
                    , root.get("outerStatus"));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(queryVO.getCurrent() - 1, queryVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = memberRelationRepository.findAll(spec, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberArchivesManagementQueryResp memberVO = new MemberArchivesManagementQueryResp();
            memberVO.setMemberId(relationDO.getSubMember().getId());
            memberVO.setValidateId(relationDO.getId());
            memberVO.setName(relationDO.getSubMember().getName());
            memberVO.setRoleId(relationDO.getSubRoleId());
            memberVO.setRoleName(relationDO.getSubRoleName());
            memberVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            memberVO.setMemberCode(Objects.isNull(relationDO.getClassification()) ? "" : relationDO.getClassification().getCode());
            memberVO.setLifeCycleStageName(Objects.isNull(relationDO.getMemberLifecycleStages()) ? "" : relationDO.getMemberLifecycleStages().getLifecycleStagesName());
            memberVO.setLifeCycleStageId(Objects.isNull(relationDO.getMemberLifecycleStages()) ? null : relationDO.getMemberLifecycleStages().getId());
            memberVO.setDepositTime(relationDO.getDepositTime() == null ? null : relationDO.getDepositTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            return memberVO;
        }).collect(Collectors.toList()));
    }

    @Override
    public PageDataResp<MemberChangeRequestSummaryPageQueryResp> baseMemberChangeRequestFormPage(HttpHeaders headers, MemberChangeRequestBasicPageDataReq pageVO, MemberChangeRequestFormStatusEnum currentStatus, List<Integer> statusList, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Page<MemberChangeRequestFormDO> pageList = baseMemberChangeRequestFormPage(loginUser, currentStatus, statusList, pageVO, roleTag);

        List<MemberChangeRequestSummaryPageQueryResp> resultList = pageList.stream().map(MemberLifecycleServiceImpl::getMemberChangeRequestSummaryPageQueryVOList).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public MemberChangeRequestFormDetailQueryResp summaryMemberChangeRequestFormDetail(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberChangeRequestFormDO changeRequestFormDO = memberChangeRequestFormRepository.findById(idVO.getId()).orElse(null);
        if (verifyPermissions(changeRequestFormDO, loginUser)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (changeRequestFormDO.getSubRole().getRoleTag() == null || !roleTag.equals(changeRequestFormDO.getSubRole().getRoleTag()))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        //变更申请单
        MemberChangeRequestFormDetailQueryResp changeRequestFormDetailVO = getMemberChangeRequestFormDetailQueryVO(changeRequestFormDO);
        changeRequestFormDetailVO.setTotalScore(changeRequestFormDO.getTotalScore());
        changeRequestFormDetailVO.setScoringResult(changeRequestFormDO.getScoringResult());
        changeRequestFormDetailVO.setScoringResultContent(changeRequestFormDO.getScoringResultContent());
        changeRequestFormDetailVO.setNotifyMember(changeRequestFormDO.getNotifyMember());
        changeRequestFormDetailVO.setResultAttachments(FileObjectUtil.toVOList(changeRequestFormDO.getResultAttachments()));

        //变更申请单评分项
        List<MemberChangeRequestFormItemDO> changeRequestFormItemDOList = changeRequestFormDO.getItems();
        List<MemberChangeRequestFormItemResp> items = changeRequestFormItemDOList.stream().map(MemberLifecycleServiceImpl::changeRequestFormDOToVO).collect(Collectors.toList());
        changeRequestFormDetailVO.setItems(items);

        //工作流步骤
        WorkflowTaskListBO result = workflowFeignService.listMemberProcessSteps(changeRequestFormDO.getMember().getId(), changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId());
       // WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员变更申请单查询详情, 调用工作流失败: {}", WrapperUtil.safeGetMessage(result));

        if (StringUtils.isBlank(changeRequestFormDO.getTaskId())) {
            changeRequestFormDetailVO.setCurrentStep(MemberChangeRequestFormStatusEnum.getFinalStep());
        } else {
            changeRequestFormDetailVO.setCurrentStep((result.getCurrentStep()));
        }
        changeRequestFormDetailVO.setVerifySteps(result.getStepList());
        changeRequestFormDetailVO.setChangeRequestFormHistory(memberHistoryService.listMemberChangeRequestFormHistory(changeRequestFormDO.getId()));

        return changeRequestFormDetailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addMemberChangeRequestForm(HttpHeaders headers, MemberChangeRequestFormAddReq addVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = memberRelationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), addVO.getSubMemberId(), addVO.getSubRoleId());
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        // 查询评分用户
        List<UserDO> users = Collections.emptyList();
        if (!CollectionUtils.isEmpty(addVO.getItems())) {
            // 过滤掉userId为0的数据
            Set<Long> userIds = addVO.getItems().stream().map(MemberChangeRequestFormItemAddReq::getUserId).filter(userId -> userId != 0).collect(Collectors.toSet());
            users = userRepository.findAllById(userIds);
            if (users.size() != userIds.size()) {

                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }
        }
        Map<Long, UserDO> userMap = users.stream().collect(Collectors.toMap(UserDO::getId, e -> e));

        //校验评分记录
        checkChangeRequestFormRecords(loginUser, addVO.getItems(), addVO.getSubmitVO(), userMap);


        //保存评分表
        MemberChangeRequestFormDO changeRequestFormDO = getMemberChangeRequestForm(relationDO, addVO);
        memberChangeRequestFormRepository.saveAndFlush(changeRequestFormDO);

        //保存评分项
        memberChangeRequestFormItemRepository.saveAll(getMemberAppraisalItemList(changeRequestFormDO, addVO.getItems(), userMap));

        //获取processKey
        String processKey = getProcessKey(loginUser, changeRequestFormDO, roleTag);

        // 启动工作流
        WorkflowTaskResultBO result = workflowFeignService.startMemberProcess(processKey, loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId());
    //    WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员变更申请单新增, 调用工作流失败: {}", WrapperUtil.safeGetMessage(result));

        // 1.如果评分记录表中【发送评分人打分】一项有勾选，则提交后将勾选的指标发送给对应用户形成待办，工作流流转至【待评分人评分】环节
        // 2.如果评分记录表中【发送评分人打分】一项都是未勾选，需要用户填写【评分结果】栏，
        //   如果评分结果为同意变更，则提交给工作流的【待审核变更申请单(一级)】进行处理；如果不同意变更，则内部状态为变更不通过，则流程结束
        long sendAppraisalCount = addVO.getItems().stream().map(MemberChangeRequestFormItemAddReq::getSendAppraisal).filter(sendAppraisal -> sendAppraisal > 0).count();
        if (sendAppraisalCount == 0) {
            MemberChangeRequestFormAddResultReq submitVO = addVO.getSubmitVO();
            changeRequestFormDO.setTotalScore(submitVO.getTotalScore());
            changeRequestFormDO.setScoringResult(submitVO.getScoringResult());
            changeRequestFormDO.setScoringResultContent(submitVO.getScoringResultContent());
            changeRequestFormDO.setNotifyMember(submitVO.getNotifyMember());
            changeRequestFormDO.setResultAttachments(FileObjectUtil.toBOList(submitVO.getResultAttachments()));
            if (EnableDisableStatusEnum.ENABLE.getCode().equals(addVO.getSubmitVO().getScoringResult())) {
                result = workflowFeignService.execMemberSerialProcess(processKey, result.getTaskId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), 3, Arrays.asList(null, null, 1));
            } else {
                changeRequestFormDO.setCompleteTime(LocalDateTime.now());
                result = workflowFeignService.execMemberSerialProcess(processKey, result.getTaskId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), 3, Arrays.asList(null, null, 0));
            }
        } else {
            result = workflowFeignService.execMemberProcess(processKey, result.getTaskId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), null);
        }
    //    WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员变更申请单新增, 调用工作流失败: {}", WrapperUtil.safeGetMessage(result));

        //保存历史流转记录
        memberHistoryService.saveMemberChangeRequestFormHistory(loginUser, changeRequestFormDO.getId(), result.getInnerStatus(), MemberChangeRequestFormStatusEnum.getCodeMessage(result.getInnerStatus()), MemberValidateHistoryOperationEnum.getMsgByCode(Integer.parseInt(result.getOperation())), "");

        changeRequestFormDO.setStatus(result.getInnerStatus());
        changeRequestFormDO.setProcessKey(processKey);
        changeRequestFormDO.setTaskId(result.getTaskId());
        memberChangeRequestFormRepository.saveAndFlush(changeRequestFormDO);

        // 发送消息
        if (sendAppraisalCount == 0) {
            // 当前会员的待审核评分结果(一级)+1
//            reportFeignService.executeIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP1_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId());
            // 发送消息
            messageFeignService.sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO.changeRequestFormTransform(changeRequestFormDO), null, roleTag);
        } else {
            // 首页统计
//            reportFeignService.executeIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_SCORING_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId(), userMap.keySet().size());

            // 通知待考评打分会员
            userMap.keySet().forEach(userId -> messageFeignService.sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO.changeRequestFormTransform(changeRequestFormDO), userId, roleTag));
        }


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateAddMemberChangeRequestForm(HttpHeaders headers, MemberChangeRequestFormUpdateReq updateVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberRelationDO relationDO = memberRelationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), updateVO.getSubMemberId(), updateVO.getSubRoleId());
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberChangeRequestFormDO changeRequestFormDO = memberChangeRequestFormRepository.findById(updateVO.getId()).orElse(null);
        if (verifyPermissions(changeRequestFormDO, loginUser)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberChangeRequestFormStatusEnum.WAIT_GRADE.getCode().equals(changeRequestFormDO.getStatus())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (changeRequestFormDO.getSubRole().getRoleTag() == null || !roleTag.equals(changeRequestFormDO.getSubRole().getRoleTag()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 查询评分用户
        List<UserDO> users = Collections.emptyList();
        Set<Long> newUserIdSet = updateVO.getItems().stream().map(MemberChangeRequestFormItemUpdateReq::getUserId).filter(userId -> userId != 0).collect(Collectors.toSet());
        if (!CollectionUtils.isEmpty(updateVO.getItems())) {
            users = userRepository.findAllById(newUserIdSet);
            if (users.size() != newUserIdSet.size()) {

                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }
        }
        Map<Long, UserDO> userMap = users.stream().collect(Collectors.toMap(UserDO::getId, e -> e));

        //校验评分记录
        List<MemberChangeRequestFormItemAddReq> changeRequestFormItemAddVOList = copyList(updateVO.getItems());
        checkChangeRequestFormRecords(loginUser, changeRequestFormItemAddVOList, updateVO.getSubmitVO(), userMap);
//        if (checkChangeRequestFormRecordsWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
//            checkChangeRequestFormRecordsWrapperResp;
//        }

        //保存评分项 先删除再重新新增
        memberChangeRequestFormItemRepository.deleteByChangeRequestForm(changeRequestFormDO);
        memberChangeRequestFormItemRepository.saveAll(getMemberAppraisalItemList(changeRequestFormDO, changeRequestFormItemAddVOList, userMap));


        // 1.如果评分记录表中【发送评分人打分】一项有勾选，则提交后将勾选的指标发送给对应用户形成待办，工作流流转至【待评分人评分】环节
        // 2.如果评分记录表中【发送评分人打分】一项都是未勾选，需要用户填写【评分结果】栏，
        //   如果评分结果为同意变更，则提交给工作流的【待审核变更申请单(一级)】进行处理；如果不同意变更，则内部状态为变更不通过，则流程结束
        long sendAppraisalCount = updateVO.getItems().stream().map(MemberChangeRequestFormItemUpdateReq::getSendAppraisal).filter(sendAppraisal -> sendAppraisal > 0).count();
        if (sendAppraisalCount == 0) {
            MemberChangeRequestFormAddResultReq submitVO = updateVO.getSubmitVO();
            changeRequestFormDO.setTotalScore(submitVO.getTotalScore());
            changeRequestFormDO.setScoringResult(submitVO.getScoringResult());
            changeRequestFormDO.setScoringResultContent(submitVO.getScoringResultContent());
            changeRequestFormDO.setNotifyMember(submitVO.getNotifyMember());
            changeRequestFormDO.setResultAttachments(FileObjectUtil.toBOList(submitVO.getResultAttachments()));
            WorkflowTaskResultBO result;
            if (EnableDisableStatusEnum.ENABLE.getCode().equals(updateVO.getSubmitVO().getScoringResult())) {
                result = workflowFeignService.execMemberSerialProcess(changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), 2, Arrays.asList(null, 1));
            } else {
                changeRequestFormDO.setCompleteTime(LocalDateTime.now());
                result = workflowFeignService.execMemberSerialProcess(changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), 2, Arrays.asList(null, 0));
            }
        //    WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员变更申请单新增, 调用工作流失败: {}", WrapperUtil.safeGetMessage(result));

            //保存历史流转记录
            memberHistoryService.saveMemberChangeRequestFormHistory(loginUser, changeRequestFormDO.getId(), changeRequestFormDO.getStatus(), MemberChangeRequestFormStatusEnum.getCodeMessage(changeRequestFormDO.getStatus()), MemberValidateHistoryOperationEnum.getMsgByCode(Integer.parseInt(result.getOperation())), "");

            changeRequestFormDO.setStatus(result.getInnerStatus());
            changeRequestFormDO.setTaskId(result.getTaskId());
            memberChangeRequestFormRepository.saveAndFlush(changeRequestFormDO);
        }

        // 首页统计
        if (sendAppraisalCount == 0) {
            // 待审核考评结果(一级)+1 待考评打分-1
//            reportFeignService.executeReduceAndIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_SCORING_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId(),
//                    OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP1_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId());
            // 发送消息
            messageFeignService.sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO.changeRequestFormTransform(changeRequestFormDO), null, roleTag);
        } else {
            //求新增的指定要发送消息的用户
            newUserIdSet.removeAll(users.stream().map(UserDO::getId).collect(Collectors.toSet()));

            // 首页统计
//            reportFeignService.executeIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_SCORING_COUNT.getCode(),
//                    changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId(), newUserIdSet.size());
            // 批量通知待考评打分会员
            userMap.keySet().forEach(userId -> messageFeignService.sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO.changeRequestFormTransform(changeRequestFormDO), userId, roleTag));
        }


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMemberCheckRequestForm(HttpHeaders headers, CommonIdListReq idsVO) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);
        List<MemberChangeRequestFormDO> changeRequestFormDOList = memberChangeRequestFormRepository.findAllById(idsVO.getIdList());
        //校验当前会员有删除权限
        if (CollectionUtils.isEmpty(changeRequestFormDOList) || changeRequestFormDOList.stream().anyMatch(appraisal -> !appraisal.getMember().getId().equals(loginCacheDTO.getMemberId()) || !appraisal.getRole().getId().equals(loginCacheDTO.getMemberRoleId()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        for (MemberChangeRequestFormDO changeRequestFormDO : changeRequestFormDOList) {
            if (!MemberChangeRequestFormStatusEnum.WAIT_GRADE.getCode().equals(changeRequestFormDO.getStatus())) {

                throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
            }
        }

        for (MemberChangeRequestFormDO changeRequestFormDO : changeRequestFormDOList) {
            memberChangeRequestFormItemRepository.deleteByChangeRequestForm(changeRequestFormDO);
        }

        memberChangeRequestFormRepository.deleteAll(changeRequestFormDOList);

//        for (MemberChangeRequestFormDO changeRequestFormDO : changeRequestFormDOList) {
//            // 首页统计
//            reportFeignService.executeReduceReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_SCORING_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId());
//        }


    }

    @Override
    public PageDataResp<MemberChangeRequestSummaryPageQueryResp> waitGradeChangeRequestFormPage(HttpHeaders headers, MemberChangeRequestBasicPageDataReq pageVO, MemberChangeRequestFormStatusEnum currentStatus, List<Integer> statusList, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());

        Page<MemberChangeRequestFormDO> pageList = memberChangeRequestFormRepository.findAll((Specification<MemberChangeRequestFormDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            // 一对多关联, 查询记录数量为n倍, 以主表去重
            query.distinct(true);
            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("role").as(MemberRoleDO.class), loginUser.getMemberRoleId()));

            Join<MemberChangeRequestFormDO, MemberChangeRequestFormItemDO> changeRequestFormItemJoin = root.join("items", JoinType.LEFT);
            predicateList.add(cb.equal(changeRequestFormItemJoin.get("status"), MemberAppraisalItemStatusEnum.WAIT_GRADE.getCode()));
            predicateList.add(cb.equal(changeRequestFormItemJoin.get("sendAppraisal"), EnableDisableStatusEnum.ENABLE.getCode()));

            Join<MemberChangeRequestFormItemDO, UserDO> memberUserJoin = changeRequestFormItemJoin.join("byUser", JoinType.LEFT);
            predicateList.add(cb.equal(memberUserJoin.get("id"), loginUser.getUserId()));

            if (StringUtils.isNotEmpty(pageVO.getChangeRequestFormNo())) {
                predicateList.add(cb.like(root.get("changeRequestFormNo"), "%" + pageVO.getChangeRequestFormNo() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getChangeRequestSummary())) {
                predicateList.add(cb.like(root.get("changeRequestSummary"), "%" + pageVO.getChangeRequestSummary() + "%"));
            }

            if (Objects.nonNull(pageVO.getChangeRequestFromTimeStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateTime(pageVO.getChangeRequestFromTimeStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), startDate));
            }

            if (Objects.nonNull(pageVO.getChangeRequestFromTimeEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateTime(pageVO.getChangeRequestFromTimeEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("createTime"), endDate));
            }

            if (StringUtils.isNotEmpty(pageVO.getName())) {
                Join<MemberChangeRequestFormDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                predicateList.add(cb.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (!CollectionUtils.isEmpty(statusList)) {
                predicateList.add(cb.and(root.get("status").in(statusList)));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
                predicateList.add(cb.equal(subRoleJoin.get("roleTag").as(Integer.class), roleTag));
            }

            // 调用规则引擎服务拼接查询条件(超级管理员不需要拼接)
            if (!UserTypeEnum.ADMIN.getCode().equals(loginUser.getUserType()) && Objects.nonNull(currentStatus)) {
                predicateList.add(getEnginePredicate(root, cb, loginUser, roleTag, currentStatus));
            }

            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        }, page);

        List<MemberChangeRequestSummaryPageQueryResp> resultList = pageList.stream().map(MemberLifecycleServiceImpl::getMemberChangeRequestSummaryPageQueryVOList).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public MemberChangeRequestFormDetailQueryResp waitGradeMemberChangeRequestFormDetail(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberChangeRequestFormDO changeRequestFormDO = memberChangeRequestFormRepository.findById(idVO.getId()).orElse(null);
        if (verifyPermissions(changeRequestFormDO, loginUser)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (changeRequestFormDO.getSubRole().getRoleTag() == null || !roleTag.equals(changeRequestFormDO.getSubRole().getRoleTag()))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        //变更申请单
        MemberChangeRequestFormDetailQueryResp changeRequestFormDetailVO = getMemberChangeRequestFormDetailQueryVO(changeRequestFormDO);

        //变更申请单评分项
        List<MemberChangeRequestFormItemDO> changeRequestFormItemDOList = changeRequestFormDO.getItems();
        List<MemberChangeRequestFormItemResp> items = changeRequestFormItemDOList.stream()
                .filter(e -> loginUser.getUserId().equals(e.getByUser().getId()))
                .map(MemberLifecycleServiceImpl::changeRequestFormDOToVO).collect(Collectors.toList());
        changeRequestFormDetailVO.setItems(items);

        //工作流步骤
        WorkflowTaskListBO result = workflowFeignService.listMemberProcessSteps(changeRequestFormDO.getMember().getId(), changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId());
    //    WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员变更申请单评分人评分详情, 调用工作流失败: {}", WrapperUtil.safeGetMessage(result));

        if (StringUtils.isBlank(changeRequestFormDO.getTaskId())) {
            changeRequestFormDetailVO.setCurrentStep(MemberChangeRequestFormStatusEnum.getFinalStep());
        } else {
            changeRequestFormDetailVO.setCurrentStep((result.getCurrentStep()));
        }
        changeRequestFormDetailVO.setVerifySteps(result.getStepList());
        changeRequestFormDetailVO.setChangeRequestFormHistory(memberHistoryService.listMemberChangeRequestFormHistory(changeRequestFormDO.getId()));

        return changeRequestFormDetailVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void gradeChangeRequestForm(HttpHeaders headers, MemberChangeRequestGradeReq gradeVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberChangeRequestFormDO changeRequestFormDO = memberChangeRequestFormRepository.findById(gradeVO.getId()).orElse(null);
        if (verifyPermissions(changeRequestFormDO, loginUser)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (changeRequestFormDO.getSubRole().getRoleTag() == null || !roleTag.equals(changeRequestFormDO.getSubRole().getRoleTag()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);

        }

        // 前端提交的评分打分项目map
        Map<Long, MemberChangeRequestFormItemGradeReq> itemGradeMap = gradeVO.getItems().stream().collect(Collectors.toMap(MemberChangeRequestFormItemGradeReq::getId, e -> e, (e1, e2) -> e2));
        // 数据库中评分打分的项目
        List<MemberChangeRequestFormItemDO> items = changeRequestFormDO.getItems();
        // 分组为属于用户打分的项目
        Map<Long, List<MemberChangeRequestFormItemDO>> itemMap = items.stream().collect(Collectors.groupingBy(item -> item.getByUser().getId()));
        // 属于该用户还未打分的项目
        List<MemberChangeRequestFormItemDO> waitGradeItems = itemMap.get(loginUser.getUserId());

        //校验该用户待评分的每一项是否都传了
        if (CollectionUtils.isEmpty(waitGradeItems) || waitGradeItems.stream().anyMatch(waitGradeItem -> !itemGradeMap.containsKey(waitGradeItem.getId()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_APPRAISAL_ITEM_NO_RELATION);
        }

        //校验评分记录是否合理
        for (MemberChangeRequestFormItemGradeReq changeRequestFormItemGradeVO : gradeVO.getItems()) {
            MemberChangeRequestFormItemDO changeRequestFormItemDO = waitGradeItems.stream().filter(itemDO -> itemDO.getId().equals(changeRequestFormItemGradeVO.getId())).findFirst().orElse(null);
            if (Objects.isNull(changeRequestFormItemDO)) {


                throw new BusinessException(ResponseCodeEnum.MC_MS_APPRAISAL_ITEM_NO_RELATION);
            }
            //校验填写分数是否合理
            if (changeRequestFormItemDO.getScoreMin().compareTo(changeRequestFormItemGradeVO.getGrade()) > 0 || changeRequestFormItemDO.getScoreMax().compareTo(changeRequestFormItemGradeVO.getGrade()) < 0) {


                throw new BusinessException(ResponseCodeEnum.MC_MS_SCORE_CANNOT_EXCEED_THE_RANGE);
            }
        }

        for (MemberChangeRequestFormItemDO changeRequestFormItemDO : waitGradeItems) {
            MemberChangeRequestFormItemGradeReq itemGradeVO = itemGradeMap.get(changeRequestFormItemDO.getId());
            changeRequestFormItemDO.setStatus(MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getCode());
            changeRequestFormItemDO.setGrade(itemGradeVO.getGrade());
            changeRequestFormItemDO.setScore(itemGradeVO.getScore());
            changeRequestFormItemDO.setReviewerFeedback(itemGradeVO.getReviewerFeedback());
            changeRequestFormItemDO.setAppraisalAttachment(FileObjectUtil.toBOList(itemGradeVO.getAppraisalAttachment()));
        }
        memberChangeRequestFormItemRepository.saveAll(items);
        memberHistoryService.saveMemberChangeRequestFormHistory(loginUser, changeRequestFormDO.getId(), MemberChangeRequestFormStatusEnum.WAIT_GRADE.getCode(), MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getMessage(), MemberValidateHistoryOperationEnum.getMsgByCode(changeRequestFormDO.getStatus()), "");

        //如果申请单每一项都打分完毕，工作流推进到待汇总评分
        if (items.stream().allMatch(appraisalItemDO -> MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getCode().equals(appraisalItemDO.getStatus()))) {
            WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), null);
        //    WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员变更申请单评分人评分, 调用工作流失败: {}", WrapperUtil.safeGetMessage(result));

            changeRequestFormDO.setStatus(result.getInnerStatus());
            changeRequestFormDO.setTaskId(result.getTaskId());
            memberChangeRequestFormRepository.saveAndFlush(changeRequestFormDO);
            messageFeignService.sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO.changeRequestFormTransform(changeRequestFormDO), null, roleTag);
        }

        // 首页统计
//        reportFeignService.executeReduceAndIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_SCORING_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId(), OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_COMMIT_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId());


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitMemberChangeRequestForm(HttpHeaders headers, MemberChangeRequestFormSubmitReq submitVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberChangeRequestFormDO changeRequestFormDO = memberChangeRequestFormRepository.findById(submitVO.getId()).orElse(null);
        if (verifyPermissions(changeRequestFormDO, loginUser)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (changeRequestFormDO.getSubRole().getRoleTag() == null || !roleTag.equals(changeRequestFormDO.getSubRole().getRoleTag()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 状态校验
        List<Integer> statusLists = Collections.singletonList(MemberChangeRequestFormStatusEnum.WAIT_SUBMIT.getCode());
        if (!statusLists.contains(changeRequestFormDO.getStatus())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 如果存在未打分的项目，不允许提交汇总评分结果
        if (changeRequestFormDO.getItems().stream().anyMatch(itemDO -> MemberChangeRequestFormStatusEnum.WAIT_GRADE.getCode().equals(itemDO.getStatus()))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_EXISTS_UNPRICED_ITEMS);
        }

        // 评分打分项目map
        Map<Long, MemberChangeRequestFormItemSubmitReq> itemSubmitMap = submitVO.getItems().stream().collect(Collectors.toMap(MemberChangeRequestFormItemSubmitReq::getId, e -> e, (e1, e2) -> e2));

        // 需要用户打分的项目 userId - itemDOList
//        Map<Long, List<MemberAppraisalItemDO>> userItemMap = changeRequestFormDO.getItems().stream().collect(Collectors.groupingBy(item -> item.getByUser().getId()));

        // 校验评分记录是否合理
        List<MemberChangeRequestFormItemDO> saveItemList = new ArrayList<>();
        for (MemberChangeRequestFormItemDO changeRequestFormItemDO : changeRequestFormDO.getItems()) {
            MemberChangeRequestFormItemSubmitReq changeRequestItemSubmitVO = itemSubmitMap.get(changeRequestFormItemDO.getId());
            //校验填写分数是否合理
            if (Objects.nonNull(changeRequestItemSubmitVO)) {
                if (changeRequestFormItemDO.getScoreMin().compareTo(changeRequestItemSubmitVO.getGrade()) > 0 || changeRequestFormItemDO.getScoreMax().compareTo(changeRequestItemSubmitVO.getGrade()) < 0) {
                    //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_SCORE_CANNOT_EXCEED_THE_RANGE);
                    throw new BusinessException(ResponseCodeEnum.MC_MS_SCORE_CANNOT_EXCEED_THE_RANGE);
                }
                changeRequestFormItemDO.setGrade(changeRequestItemSubmitVO.getGrade());
                changeRequestFormItemDO.setScore(changeRequestItemSubmitVO.getScore());
                changeRequestFormItemDO.setReviewerFeedback(changeRequestItemSubmitVO.getReviewerFeedback());
                changeRequestFormItemDO.setAppraisalAttachment(FileObjectUtil.toBOList(changeRequestItemSubmitVO.getAppraisalAttachment()));
                saveItemList.add(changeRequestFormItemDO);
            }
        }
        // 更新的评分项目集合
        memberChangeRequestFormItemRepository.saveAll(saveItemList);

        // 评分结果
        changeRequestFormDO.setTotalScore(submitVO.getSubmitVO().getTotalScore());
        changeRequestFormDO.setScoringResult(submitVO.getSubmitVO().getScoringResult());
        changeRequestFormDO.setScoringResultContent(submitVO.getSubmitVO().getScoringResultContent());
        changeRequestFormDO.setNotifyMember(submitVO.getSubmitVO().getNotifyMember());
        changeRequestFormDO.setResultAttachments(FileObjectUtil.toBOList(submitVO.getSubmitVO().getResultAttachments()));
        memberChangeRequestFormRepository.saveAndFlush(changeRequestFormDO);

        // 是否审核通过标致
        boolean isPass = CheckStatusEnum.AGREE.getCode().equals(submitVO.getSubmitVO().getScoringResult());

        // 引擎决策(判断是否跳过流程)
        PolicyResultResp policy = this.policy(loginUser, changeRequestFormDO.getProcessKey(), MemberChangeRequestFormStatusEnum.WAIT_SUBMIT.getCode(), changeRequestFormDO.getId());

        // 如果审核通过，且有跳过流程，则走连续执行工作流；否则正常执行到下一步
        WorkflowTaskResultBO result;
        if (isPass && Objects.nonNull(policy) && policy.getSkip()) {
            result = workflowFeignService.execMemberSerialProcess(changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId(),
                    loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), policy.getStepWidth(), getAgrees(policy));
        } else {
            result = workflowFeignService.execMemberProcess(changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId(),
                    loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), submitVO.getSubmitVO().getScoringResult());
        }
    //    WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员变更申请单提交汇总评分结果,调用工作流失败: {}", WrapperUtil.safeGetMessage(result));

        changeRequestFormDO.setStatus(result.getInnerStatus());
        changeRequestFormDO.setTaskId(result.getTaskId());
        if (EnableDisableStatusEnum.DISABLE.getCode().equals(submitVO.getSubmitVO().getScoringResult())) {
            changeRequestFormDO.setCompleteTime(LocalDateTime.now());
        }
        memberChangeRequestFormRepository.saveAndFlush(changeRequestFormDO);

//        // 首页统计
//        reportFeignService.executeReduceAndIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_COMMIT_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId(),
//                OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP1_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId());
//
        // 发送消息
        messageFeignService.sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO.changeRequestFormTransform(changeRequestFormDO), null, roleTag);

        memberHistoryService.saveMemberChangeRequestFormHistory(loginUser, changeRequestFormDO.getId(), changeRequestFormDO.getStatus(),
                MemberChangeRequestFormStatusEnum.getCodeMessage(changeRequestFormDO.getStatus()), MemberValidateHistoryOperationEnum.getMsgByCode(Integer.parseInt(result.getOperation())), "");


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditOneMemberChangeRequestForm(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberChangeRequestFormDO changeRequestFormDO = memberChangeRequestFormRepository.findById(agreeVO.getId()).orElse(null);
        if (verifyPermissions(changeRequestFormDO, loginUser)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (changeRequestFormDO.getSubRole().getRoleTag() == null || !roleTag.equals(changeRequestFormDO.getSubRole().getRoleTag()))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberChangeRequestFormStatusEnum.WAIT_AUDIT_1.getCode().equals(changeRequestFormDO.getStatus())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }


        // 是否审核通过标致
        boolean isPass = CheckStatusEnum.AGREE.getCode().equals(agreeVO.getAgree());

        // 引擎决策(判断是否跳过流程)
        PolicyResultResp policy = this.policy(loginUser, changeRequestFormDO.getProcessKey(), MemberChangeRequestFormStatusEnum.WAIT_AUDIT_1.getCode(), changeRequestFormDO.getId());

        // 如果审核通过，且有跳过流程，则走连续执行工作流；否则正常执行到下一步
        WorkflowTaskResultBO result;
        if (isPass && Objects.nonNull(policy) && policy.getSkip()) {
            result = workflowFeignService.execMemberSerialProcess(changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId(),
                    loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), policy.getStepWidth(), getAgrees(policy));
        } else {
            result = workflowFeignService.execMemberProcess(changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId(),
                    loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), agreeVO.getAgree());
        }
     //   WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员变更申请单一级审核,调用工作流失败: {}", result);

        changeRequestFormDO.setStatus(result.getInnerStatus());
        changeRequestFormDO.setTaskId(result.getTaskId());
        memberChangeRequestFormRepository.saveAndFlush(changeRequestFormDO);

//        if (EnableDisableStatus.ENABLE.getCode().equals(agreeVO.getAgree())) {
//            // 首页统计
//            reportFeignService.executeReduceAndIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP1_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId(),
//                    OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP2_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId());
//        } else {
//            // 首页统计
//            reportFeignService.executeReduceAndIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP1_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId(),
//                    OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_COMMIT_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId());
//        }

        // 发送消息
        messageFeignService.sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO.changeRequestFormTransform(changeRequestFormDO), null, roleTag);

        memberHistoryService.saveMemberChangeRequestFormHistory(loginUser, changeRequestFormDO.getId(), changeRequestFormDO.getStatus(),
                MemberChangeRequestFormStatusEnum.getCodeMessage(changeRequestFormDO.getStatus()), MemberValidateHistoryOperationEnum.getMsgByCode(Integer.parseInt(result.getOperation())), agreeVO.getReason());


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditTwoMemberChangeRequestForm(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberChangeRequestFormDO changeRequestFormDO = memberChangeRequestFormRepository.findById(agreeVO.getId()).orElse(null);
        if (verifyPermissions(changeRequestFormDO, loginUser)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (changeRequestFormDO.getSubRole().getRoleTag() == null || !roleTag.equals(changeRequestFormDO.getSubRole().getRoleTag()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberChangeRequestFormStatusEnum.WAIT_AUDIT_2.getCode().equals(changeRequestFormDO.getStatus())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 工作流
        WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId(),
                loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), agreeVO.getAgree());
    //    WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员变更申请单二级审核,调用工作流失败: {}", WrapperUtil.safeGetMessage(result));

        changeRequestFormDO.setStatus(result.getInnerStatus());
        changeRequestFormDO.setTaskId(result.getTaskId());
        memberChangeRequestFormRepository.saveAndFlush(changeRequestFormDO);

//        if (EnableDisableStatus.ENABLE.getCode().equals(agreeVO.getAgree())) {
//            // 首页统计
//            reportFeignService.executeReduceAndIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP2_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId(),
//                    OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_NOTICE_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId());
//        } else {
//            // 首页统计
//            reportFeignService.executeReduceAndIncreaseReport(OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_VALIFY_STEP2_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId(),
//                    OperateDataSourceEnum.MEMBER_KPI.getCode(), MemberKpiOperateTypeEnum.TO_BE_COMMIT_COUNT.getCode(), changeRequestFormDO.getMember().getId(), changeRequestFormDO.getRole().getId());
//        }

        // 发送消息
        messageFeignService.sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO.changeRequestFormTransform(changeRequestFormDO), null, roleTag);

        memberHistoryService.saveMemberChangeRequestFormHistory(loginUser, changeRequestFormDO.getId(), changeRequestFormDO.getStatus(),
                MemberChangeRequestFormStatusEnum.getCodeMessage(changeRequestFormDO.getStatus()), MemberValidateHistoryOperationEnum.getMsgByCode(Integer.parseInt(result.getOperation())), agreeVO.getReason());


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void waitConfirmMemberChangeRequestFormPage(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberChangeRequestFormDO changeRequestFormDO = memberChangeRequestFormRepository.findById(agreeVO.getId()).orElse(null);
        if (verifyPermissions(changeRequestFormDO, loginUser)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberRelationDO relationDO = memberRelationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getSubMember().getId(), changeRequestFormDO.getSubRole().getId());
        if (Objects.isNull(relationDO) || !(changeRequestFormDO.getMember().getId().equals(relationDO.getMemberId()) && changeRequestFormDO.getRole().getId().equals(relationDO.getRoleId()))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && (changeRequestFormDO.getSubRole().getRoleTag() == null || !roleTag.equals(changeRequestFormDO.getSubRole().getRoleTag()))) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        // 状态校验
        if (!MemberChangeRequestFormStatusEnum.WAIT_CONFIRMED.getCode().equals(changeRequestFormDO.getStatus())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 工作流
        WorkflowTaskResultBO result = workflowFeignService.execMemberProcess(changeRequestFormDO.getProcessKey(), changeRequestFormDO.getTaskId(),
                loginUser.getMemberId(), loginUser.getMemberRoleId(), changeRequestFormDO.getId(), agreeVO.getAgree());
    //    WrapperUtil.throwWhenDataIsNullAndLog(result, null, log, "会员确认变更申请单,调用工作流失败: {}", WrapperUtil.safeGetMessage(result));

        changeRequestFormDO.setStatus(result.getInnerStatus());
        changeRequestFormDO.setTaskId(result.getTaskId());
        changeRequestFormDO.setCompleteTime(LocalDateTime.now());
        memberChangeRequestFormRepository.saveAndFlush(changeRequestFormDO);

        memberHistoryService.saveMemberChangeRequestFormHistory(loginUser, changeRequestFormDO.getId(), changeRequestFormDO.getStatus(),
                MemberChangeRequestFormStatusEnum.getCodeMessage(changeRequestFormDO.getStatus()), MemberValidateHistoryOperationEnum.getMsgByCode(Integer.parseInt(result.getOperation())), agreeVO.getReason());

        if (EnableDisableStatusEnum.ENABLE.getCode().equals(agreeVO.getAgree())) {
            relationDO.setMemberLifecycleStages(changeRequestFormDO.getTargetLifecycleStage());
            memberRelationRepository.saveAndFlush(relationDO);

            //发送消息（如果勾选通知下级会员，需要通知变更结果）
            if (EnableDisableStatusEnum.ENABLE.getCode().equals(changeRequestFormDO.getNotifyMember())) {
                messageFeignService.sendMemberChangeRequestFormMessage(MemberChangeRequestFormMessageDTO.changeRequestFormTransform(changeRequestFormDO), null, roleTag);
            }
        }


    }

    /**
     * 通用权限校验
     */
    private boolean verifyPermissions(MemberChangeRequestFormDO changeRequestFormDO, UserLoginCacheDTO loginUser) {
        return Objects.isNull(changeRequestFormDO)
                || Objects.isNull(changeRequestFormDO.getMember())
                || Objects.isNull(changeRequestFormDO.getRole())
                || !loginUser.getMemberId().equals(changeRequestFormDO.getMember().getId())
                || !loginUser.getMemberRoleId().equals(changeRequestFormDO.getRole().getId());
    }

    private MemberChangeRequestFormDetailQueryResp getMemberChangeRequestFormDetailQueryVO(MemberChangeRequestFormDO changeRequestFormDO) {
        MemberChangeRequestFormDetailQueryResp changeRequestFormDetailVO = new MemberChangeRequestFormDetailQueryResp();
        changeRequestFormDetailVO.setId(changeRequestFormDO.getId());
        changeRequestFormDetailVO.setChangeRequestFormNo(changeRequestFormDO.getChangeRequestFormNo());
        changeRequestFormDetailVO.setSubMemberId(changeRequestFormDO.getSubMember().getId());
        changeRequestFormDetailVO.setSubRoleId(changeRequestFormDO.getSubRole().getId());
        changeRequestFormDetailVO.setSubMemberName(changeRequestFormDO.getSubMember().getName());
        changeRequestFormDetailVO.setCurrentLifecycleStageId(Objects.nonNull(changeRequestFormDO.getCurrentLifecycleStage()) ? changeRequestFormDO.getCurrentLifecycleStage().getId() : null);
        changeRequestFormDetailVO.setCurrentLifecycleStage(Objects.nonNull(changeRequestFormDO.getCurrentLifecycleStage()) ? changeRequestFormDO.getCurrentLifecycleStage().getLifecycleStagesName() : "");
        changeRequestFormDetailVO.setTargetLifecycleStageId(Objects.nonNull(changeRequestFormDO.getTargetLifecycleStage()) ? changeRequestFormDO.getTargetLifecycleStage().getId() : null);
        changeRequestFormDetailVO.setTargetLifecycleStage(Objects.nonNull(changeRequestFormDO.getTargetLifecycleStage()) ? changeRequestFormDO.getTargetLifecycleStage().getLifecycleStagesName() : "");
        changeRequestFormDetailVO.setChangeRequestSummary(changeRequestFormDO.getChangeRequestSummary());
        changeRequestFormDetailVO.setRemark(changeRequestFormDO.getRemark());
        changeRequestFormDetailVO.setStatus(changeRequestFormDO.getStatus());
        changeRequestFormDetailVO.setStatusName(MemberChangeRequestFormStatusEnum.getCodeMessage(changeRequestFormDO.getStatus()));
        changeRequestFormDetailVO.setCreateTime(DateTimeUtil.formatDate(changeRequestFormDO.getCreateTime()));
        return changeRequestFormDetailVO;
    }

    private static MemberChangeRequestSummaryPageQueryResp getMemberChangeRequestSummaryPageQueryVOList(MemberChangeRequestFormDO e) {
        MemberChangeRequestSummaryPageQueryResp pageQueryVO = new MemberChangeRequestSummaryPageQueryResp();
        pageQueryVO.setId(e.getId());
        pageQueryVO.setChangeRequestFormNo(e.getChangeRequestFormNo());
        pageQueryVO.setChangeRequestSummary(e.getChangeRequestSummary());
        pageQueryVO.setCurrentLifecycleStage(Objects.nonNull(e.getCurrentLifecycleStage()) ? e.getCurrentLifecycleStage().getLifecycleStagesName() : "");
        pageQueryVO.setTargetLifecycleStage(Objects.nonNull(e.getTargetLifecycleStage()) ? e.getTargetLifecycleStage().getLifecycleStagesName() : "");
        pageQueryVO.setMemberName(e.getSubMember().getName());
        pageQueryVO.setCreateTime(DateTimeUtil.formatDate(e.getCreateTime()));
        pageQueryVO.setStatus(e.getStatus());
        pageQueryVO.setStatusName(MemberChangeRequestFormStatusEnum.getCodeMessage(e.getStatus()));
        return pageQueryVO;
    }

    private static MemberChangeRequestFormItemResp changeRequestFormDOToVO(MemberChangeRequestFormItemDO changeRequestFormItemDO) {
        MemberChangeRequestFormItemResp itemVO = new MemberChangeRequestFormItemResp();
        itemVO.setId(changeRequestFormItemDO.getId());
        itemVO.setIndicatorGrouping(changeRequestFormItemDO.getIndicatorGrouping());
        itemVO.setStandardIndicator(changeRequestFormItemDO.getStandardIndicator());
        itemVO.setScoreMin(Objects.nonNull(changeRequestFormItemDO.getScoreMin()) ? changeRequestFormItemDO.getScoreMin().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        itemVO.setScoreMax(Objects.nonNull(changeRequestFormItemDO.getScoreMax()) ? changeRequestFormItemDO.getScoreMax().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        itemVO.setScoreStandard(changeRequestFormItemDO.getScoreStandard());
        itemVO.setWeight(Objects.nonNull(changeRequestFormItemDO.getWeight()) ? changeRequestFormItemDO.getWeight().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        itemVO.setUserId(Objects.nonNull(changeRequestFormItemDO.getByUser()) ? changeRequestFormItemDO.getByUser().getId() : null);
        itemVO.setUserName(Objects.nonNull(changeRequestFormItemDO.getByUser()) ? changeRequestFormItemDO.getByUser().getName() : "");
        itemVO.setSendAppraisal(changeRequestFormItemDO.getSendAppraisal());
        itemVO.setGrade(Objects.nonNull(changeRequestFormItemDO.getGrade()) ? changeRequestFormItemDO.getGrade().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        itemVO.setScore(Objects.nonNull(changeRequestFormItemDO.getScore()) ? changeRequestFormItemDO.getScore().setScale(1, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        itemVO.setStatus(changeRequestFormItemDO.getStatus());
        itemVO.setReviewerFeedback(changeRequestFormItemDO.getReviewerFeedback());
        itemVO.setAppraisalAttachment(FileObjectUtil.toVOList(changeRequestFormItemDO.getAppraisalAttachment()));
        return itemVO;
    }

    private Page<MemberChangeRequestFormDO> baseMemberChangeRequestFormPage(UserLoginCacheDTO loginUser, MemberChangeRequestFormStatusEnum currentStatus, List<Integer> statusList, MemberChangeRequestBasicPageDataReq pageVO, Integer roleTag) {
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());

        return memberChangeRequestFormRepository.findAll((Specification<MemberChangeRequestFormDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("member").as(MemberDO.class), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("role").as(MemberRoleDO.class), loginUser.getMemberRoleId()));

            if (StringUtils.isNotEmpty(pageVO.getChangeRequestFormNo())) {
                predicateList.add(cb.like(root.get("changeRequestFormNo"), "%" + pageVO.getChangeRequestFormNo() + "%"));
            }

            if (StringUtils.isNotEmpty(pageVO.getChangeRequestSummary())) {
                predicateList.add(cb.like(root.get("changeRequestSummary"), "%" + pageVO.getChangeRequestSummary() + "%"));
            }

            if (Objects.nonNull(pageVO.getChangeRequestFromTimeStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateTime(pageVO.getChangeRequestFromTimeStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), startDate));
            }

            if (Objects.nonNull(pageVO.getChangeRequestFromTimeEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateTime(pageVO.getChangeRequestFromTimeEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("createTime"), endDate));
            }

            if (StringUtils.isNotEmpty(pageVO.getName())) {
                Join<MemberChangeRequestFormDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                predicateList.add(cb.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (!CollectionUtils.isEmpty(statusList)) {
                predicateList.add(cb.and(root.get("status").in(statusList)));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
                predicateList.add(cb.equal(subRoleJoin.get("roleTag").as(Integer.class), roleTag));
            }

            // 调用规则引擎服务拼接查询条件(超级管理员不需要拼接)
            if (!UserTypeEnum.ADMIN.getCode().equals(loginUser.getUserType()) && Objects.nonNull(currentStatus)) {
                predicateList.add(getEnginePredicate(root, cb, loginUser, roleTag, currentStatus));
            }

            return query.where(predicateList.toArray(new Predicate[0])).getRestriction();
        }, page);
    }

    private List<MemberChangeRequestFormItemAddReq> copyList(List<MemberChangeRequestFormItemUpdateReq> items) {
        return items.stream().map(item -> {
            MemberChangeRequestFormItemAddReq temp = new MemberChangeRequestFormItemAddReq();
            BeanUtils.copyProperties(item, temp);
            return temp;
        }).collect(Collectors.toList());
    }

    private void checkChangeRequestFormRecords(UserLoginCacheDTO loginUser, List<MemberChangeRequestFormItemAddReq> itemVOList, MemberChangeRequestFormAddResultReq resultVO, Map<Long, UserDO> userMap) {
        BigDecimal totalScoreWeight = BigDecimal.ZERO;
        long sendAppraisalCount = 0;
        for (MemberChangeRequestFormItemAddReq item : itemVOList) {
            totalScoreWeight = totalScoreWeight.add(item.getWeight());
            sendAppraisalCount += item.getSendAppraisal();
            //如果有指定发送评分人
            if (EnableDisableStatusEnum.ENABLE.getCode().equals(item.getSendAppraisal())) {
                // 如果是需要发送用户评分，但状态却是已打分则报错
                if (MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getCode().equals(item.getStatus()) && EnableDisableStatusEnum.ENABLE.getCode().equals(item.getSendAppraisal())) {
                    //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_REQUIRED_EVALUATION_BUT_STATUS_IS_SCORED);
                }
                // 发送评分人打分项不允许填写评分计分、评分人反馈、附件
                if (NumberUtil.isGteZero(item.getGrade()) || NumberUtil.isGteZero(item.getScore()) || StringUtils.isNotBlank(item.getReviewerFeedback()) || !CollectionUtils.isEmpty(item.getAppraisalAttachment())) {
                    //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_CANNOT_BE_FILLED_WHEN_SENDING_APPRAISER_SCORING);
                }
                // 校验评分人是否存在
                if (NumberUtil.isNullOrLteZero(item.getUserId()) || Objects.isNull(userMap.get(item.getUserId()))) {
                    //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_APPRAISER_SPECIFIED_SEND_DOES_NOT_EXIST);
                }
                // 如果存在指定用户打分，就不能直接提交汇总评分
                if (!Objects.isNull(resultVO) && NumberUtil.isGteZero(resultVO.getTotalScore())) {
                    // WrapperUtil.fail(ResponseCodeEnum.MC_MS_CANNOT_SUBMIT_SUMMARY_SCORE_DIRECTLY);
                }
            } else {
                //如果没有指定发送评分人，则校验填写分数是否合理
                if (item.getScoreMin().compareTo(item.getGrade()) > 0 || item.getScoreMax().compareTo(item.getGrade()) < 0) {
                    //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_SCORE_CANNOT_EXCEED_THE_RANGE);
                }
                //如果没有指定发送评分人，考评人必须为当前登录用户
                if (!loginUser.getUserId().equals(item.getUserId())) {
                    //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_SELF_APPRAISER_USER_MUST_BE_CURRENT_LOGIN_USER);
                }
            }
        }
        //如果没有发送评分人打分，则必须有评分结果
        if (sendAppraisalCount == 0 && (Objects.isNull(resultVO) || !NumberUtil.isGteZero(resultVO.getTotalScore()) || Objects.isNull(resultVO.getScoringResult()))) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_EVALUATION_RESULT_MUST_FILLED_FOR_SELF_EVALUATION);
        }
        //权重和相加必须等于100%
        if (totalScoreWeight.compareTo((new BigDecimal(100))) != 0) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_TOTAL_SCORE_WEIGHT);
        }

    }

    private MemberChangeRequestFormDO getMemberChangeRequestForm(MemberRelationDO relationDO, MemberChangeRequestFormAddReq addVO) {
        MemberLifecycleStagesDO memberLifecycleStagesDO = memberLifecycleStagesRepository.findById(addVO.getTargetLifecycleStageId()).orElse(null);
        if (Objects.isNull(memberLifecycleStagesDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_TARGET_LIFECYCLE_STAGE_NOT_EXIST);
        }
        MemberChangeRequestFormDO changeRequestFormDO = new MemberChangeRequestFormDO();
        changeRequestFormDO.setChangeRequestFormNo(generateChangeRequestFormNo());//生成申请单号
        changeRequestFormDO.setMember(relationDO.getMember());
        changeRequestFormDO.setRole(relationDO.getRole());
        changeRequestFormDO.setSubMember(relationDO.getSubMember());
        changeRequestFormDO.setSubRole(relationDO.getSubRole());
        changeRequestFormDO.setCurrentLifecycleStage(relationDO.getMemberLifecycleStages());
        changeRequestFormDO.setTargetLifecycleStage(memberLifecycleStagesDO);
        changeRequestFormDO.setChangeRequestSummary(addVO.getChangeRequestSummary());
        changeRequestFormDO.setRemark(addVO.getRemark());
        changeRequestFormDO.setCreateTime(LocalDateTime.now(ZoneId.systemDefault()));
        changeRequestFormDO.setStatus(MemberChangeRequestFormStatusEnum.WAIT_GRADE.getCode());
        return changeRequestFormDO;
    }

    private List<MemberChangeRequestFormItemDO> getMemberAppraisalItemList(MemberChangeRequestFormDO changeRequestFormDO, List<MemberChangeRequestFormItemAddReq> changeRequestFormItemAddVOList, Map<Long, UserDO> userMap) {
        return changeRequestFormItemAddVOList.stream().map(e -> {
            MemberChangeRequestFormItemDO item = new MemberChangeRequestFormItemDO();
            item.setIndicatorGrouping(e.getIndicatorGrouping());
            item.setStandardIndicator(e.getStandardIndicator());
            item.setScoreMin(e.getScoreMin());
            item.setScoreMax(e.getScoreMax());
            item.setScoreStandard(e.getScoreStandard());
            item.setWeight(e.getWeight());
            item.setGrade(EnableDisableStatusEnum.DISABLE.getCode().equals(e.getSendAppraisal()) ? e.getGrade() : BigDecimal.ZERO);
            item.setScore(EnableDisableStatusEnum.DISABLE.getCode().equals(e.getSendAppraisal()) ? e.getScore() : BigDecimal.ZERO);
            item.setReviewerFeedback(EnableDisableStatusEnum.DISABLE.getCode().equals(e.getSendAppraisal()) ? e.getReviewerFeedback() : "");
            item.setStatus(EnableDisableStatusEnum.DISABLE.getCode().equals(e.getSendAppraisal()) ? MemberAppraisalItemStatusEnum.GRADE_COMPLETE.getCode() : MemberAppraisalItemStatusEnum.WAIT_GRADE.getCode());
            item.setSendAppraisal(Objects.isNull(e.getSendAppraisal()) ? EnableDisableStatusEnum.DISABLE.getCode() : e.getSendAppraisal());
            item.setAppraisalAttachment(FileObjectUtil.toBOList(e.getAppraisalAttachment()));
            item.setByUser(NumberUtil.notNullOrZero(e.getUserId()) ? userMap.get(e.getUserId()) : null);
            item.setChangeRequestForm(changeRequestFormDO);
            return item;
        }).collect(Collectors.toList());
    }

    /**
     * 生成申请单号
     */
    private String generateChangeRequestFormNo() {
        //流水号自增1位，存入redis（到凌晨key会自动失效）
        return MemberConstant.CHANGE_REQUEST_FORM_SERIAL_PREFIX + LocalDateTime.now(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("yyyyMMdd")) + generateSerialNumber();
    }

    /**
     * 生成流水号（需要兼顾并发，保证原子性）
     * 如果redis中存在流水号，则直接自增并返回结果
     * 如果redis中不存在，则可能是当天第一次生成流水号或redis宕机
     * 读取数据库的今日变更申请单中流水号最大的那个：
     * 如果读取到了，则说明是redis宕机，直接把值存入redis，再自增
     * 如果今日还未有变更申请单，则说明是今日第一次新增，则直接自增获取流水号
     */
    private String generateSerialNumber() {
        if (Objects.isNull(redisUtils.stringGet(MemberRedisConstant.CHANGE_REQUEST_FORM_SERIAL_REDIS_KEY, RedisConstant.REDIS_USER_INDEX))) {
            MemberChangeRequestFormDO changeRequestFormDO = memberChangeRequestFormRepository.findFirstByCreateTimeBetween(LocalDateTime.of(LocalDate.now(ZoneId.systemDefault()), LocalTime.MIN), LocalDateTime.of(LocalDate.now(ZoneId.systemDefault()), LocalTime.MAX), Sort.by("createTime").descending());
            if (Objects.isNull(changeRequestFormDO)) {
                return String.format("%05d", redisUtils.stringIncrement(MemberRedisConstant.CHANGE_REQUEST_FORM_SERIAL_REDIS_KEY, DateTimeUtil.getNowToNextDaySeconds(), RedisConstant.REDIS_USER_INDEX));
            } else {
                redisUtils.stringSetNX(MemberRedisConstant.CHANGE_REQUEST_FORM_SERIAL_REDIS_KEY, String.valueOf(Integer.parseInt(changeRequestFormDO.getChangeRequestFormNo().substring(10))), DateTimeUtil.getNowToNextDaySeconds(), RedisConstant.REDIS_USER_INDEX);
            }
        }
        //生成5位流水号，位数不足则左补零
        return String.format("%05d", redisUtils.stringIncrement(MemberRedisConstant.CHANGE_REQUEST_FORM_SERIAL_REDIS_KEY, DateTimeUtil.getNowToNextDaySeconds(), RedisConstant.REDIS_USER_INDEX));
    }

    /**
     * 获取流程key
     */
    @NonNull
    private String getProcessKey(UserLoginCacheDTO loginUser, MemberChangeRequestFormDO changeRequestFormDO, Integer roleTag) {
        EngineResultDTO engineResultDTO = getEngineResult(transformToDTO(changeRequestFormDO), loginUser.getMemberId(), loginUser.getMemberRoleId(), getProcessTypeDetail(roleTag).getCode());
        return Optional.ofNullable(engineResultDTO)
                .map(EngineResultDTO::getProcessKey)
                .orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CHANGE_REQUEST_FORM_NOT_EXISTS_PROCESS));
    }

    /**
     * 获取引擎返回流程
     */
    private EngineResultDTO getEngineResult(MemberChangeRequestFormDTO changeRequestFormDTO, Long memberId, Long memberRoleId, Integer type) {
        WrapperResp<List<ProcessEngineRuleResp>> wrapperResp = processEngineRuleFeign.getEngineRuleList(new EngineRuleQueryReq(type, memberId, memberRoleId));
        return WrapperUtil.ofNullable(wrapperResp)
                .tryThrowWhenDataIsEmpty(ResponseCodeEnum.MC_MS_CHANGE_REQUEST_FORM_NOT_EXISTS_PROCESS)
                .transferData((w) -> new EngineRuleUtil<>(changeRequestFormDTO, w.getData()).meta())
                .getData();
    }

    /**
     * 因为DO对象是代理对象，异步时可能会有懒加载的session关闭问题，所以在此转DTO
     */
    private MemberChangeRequestFormDTO transformToDTO(MemberChangeRequestFormDO changeRequestFormDO) {
        MemberChangeRequestFormDTO changeRequestFormDTO = new MemberChangeRequestFormDTO();
        BeanUtil.copyProperties(changeRequestFormDO, changeRequestFormDTO);
        return changeRequestFormDTO;
    }

    /**
     * 规则决策
     */
    private PolicyResultResp policy(UserLoginCacheDTO loginUser, String processKey, Integer processStep, Long id) {
        PolicyReq request = new PolicyReq();
        request.setMemberId(loginUser.getMemberId());
        request.setMemberRoleId(loginUser.getMemberRoleId());
        request.setProcessId(processKey);
        request.setProcessStep(processStep);
        request.setUserRoleList(loginUser.getUserRoleIds());
        request.setEntityId(id);
        WrapperResp<PolicyResultResp> wrapperResp = ruleEngineConfigFeign.policy(request);
        return WrapperUtil.ofNullable(wrapperResp)
                .transferData(WrapperResp::getData)
                .getData(() -> new PolicyResultResp(Boolean.FALSE, 0, new ArrayList<>()));
    }

    private List<Integer> getAgrees(PolicyResultResp policy) {
        List<Integer> agrees = new ArrayList<>();
        for (int i = 0; i < policy.getStepWidth(); i++) {
            agrees.add(1);
        }
        return agrees;
    }

    /**
     * 构造规则引擎拼接查询条件
     * 因为供应商和客户是共用一套工作流，同一个工作流程的processKey一样，所以下面都默认传供应商的processKey
     */
    @NonNull
    private Predicate getEnginePredicate(Root<MemberChangeRequestFormDO> root, CriteriaBuilder cb, UserLoginCacheDTO loginUser, Integer roleTag, @NonNull MemberChangeRequestFormStatusEnum currentStatus) {
        List<MemberCycleProcessEnum> cycleProcessEnumList;
        switch (currentStatus) {
            case SUBMIT_FORM://提交变更申请单
            case WAIT_GRADE://待评分人评分
            case WAIT_SUBMIT://待汇总评分结果
            case WAIT_CONFIRMED://待确认变更申请单
                cycleProcessEnumList = Arrays.asList(MemberCycleProcessEnum.SUPPLIER_LIFE_CYCLE_UPDATE_EMPTY_PROCESS,
                        MemberCycleProcessEnum.SUPPLIER_LIFE_CYCLE_UPDATE_PROCESS_1,
                        MemberCycleProcessEnum.SUPPLIER_LIFE_CYCLE_UPDATE_WHOLE_PROCESS);
                break;
            case WAIT_AUDIT_1://待审核变更申请单(一级)
                cycleProcessEnumList = Arrays.asList(MemberCycleProcessEnum.SUPPLIER_LIFE_CYCLE_UPDATE_PROCESS_1,
                        MemberCycleProcessEnum.SUPPLIER_LIFE_CYCLE_UPDATE_WHOLE_PROCESS);
                break;
            case WAIT_AUDIT_2://待审核变更申请单(二级)
                cycleProcessEnumList = Collections.singletonList(MemberCycleProcessEnum.SUPPLIER_LIFE_CYCLE_UPDATE_WHOLE_PROCESS);
                break;
            default:
                return cb.conjunction();
        }
        List<Predicate> orPredicates = getPredicates(root, cb, loginUser, roleTag, currentStatus, cycleProcessEnumList);
        return CollectionUtils.isEmpty(orPredicates) ? cb.conjunction() : cb.or(orPredicates.toArray(new Predicate[0]));
    }

    private List<Predicate> getPredicates(Root<MemberChangeRequestFormDO> root, CriteriaBuilder cb, UserLoginCacheDTO loginUser, Integer roleTag, MemberChangeRequestFormStatusEnum currentStatus, List<MemberCycleProcessEnum> cycleProcessEnumList) {
        return cycleProcessEnumList.stream().map(processEnum -> this.getPredicate(root, cb, loginUser, roleTag, currentStatus, processEnum)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private Predicate getPredicate(Root<MemberChangeRequestFormDO> root, CriteriaBuilder cb, UserLoginCacheDTO loginUser, Integer roleTag, MemberChangeRequestFormStatusEnum currentStatus, MemberCycleProcessEnum cycleProcessEnum) {
        List<RuleEngineConfigResp> ruleEngineConfigList = addDataPermissions(cycleProcessEnum.getProcessKey(), currentStatus.getStep(), loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);
        return Optional.ofNullable(ruleEngineConfigList).map(rule -> JpaUtil.getRuleEngineConfigPredicate(cb, root, MemberChangeRequestFormDO.class, rule, loginUser.getUserRoleIds())).orElse(null);
    }

    /**
     * 添加数据权限
     */
    @Nullable
    private List<RuleEngineConfigResp> addDataPermissions(String processId, Integer step, Long memberId, Long memberRoleId, Integer roleTag) {
        RuleEngineByPredicateReq request = new RuleEngineByPredicateReq();
        request.setProcessType(getProcessTypeDetail(roleTag).getType());
        request.setProcessId(processId);
        request.setProcessStep(step);
        request.setMemberId(memberId);
        request.setMemberRoleId(memberRoleId);
        WrapperResp<List<RuleEngineConfigResp>> ruleEngineCondition = ruleEngineConfigFeign.getRuleEngineCondition(request);
        return WrapperUtil.ofNullable(ruleEngineCondition)
                .tryThrowWhenFail(null)
                .getData();
    }

    /**
     * 获取流程类型明细枚举
     */
    @NonNull
    private ProcessTypeDetailEnum getProcessTypeDetail(@NonNull Integer roleTag) {
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        switch (roleTagEnum) {
            case MEMBER:
            case CUSTOMER:
                return ProcessTypeDetailEnum.CUSTOMER_LOFT_CYCLE;
            case SUPPLIER:
                return ProcessTypeDetailEnum.SUPPLER_LOFT_CYCLE;
            default:
                throw new BusinessException(ResponseCodeEnum.MC_MS_CHANGE_REQUEST_FORM_NOT_EXISTS_PROCESS);
        }
    }
}
