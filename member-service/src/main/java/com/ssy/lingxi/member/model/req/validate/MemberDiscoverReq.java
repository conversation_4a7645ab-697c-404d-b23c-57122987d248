package com.ssy.lingxi.member.model.req.validate;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 会员能力 - 会员发现参数VO
 * <AUTHOR>
 * @since 2022/7/2 21:05
 * @version 1.0
 */
@Setter
@Getter
@ToString
public class MemberDiscoverReq implements Serializable {

    private static final long serialVersionUID = 6185530457996687421L;
    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 角色id
     */
    private Long roleId;

}
