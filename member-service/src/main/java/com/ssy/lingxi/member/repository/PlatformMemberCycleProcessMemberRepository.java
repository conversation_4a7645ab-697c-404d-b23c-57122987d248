package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.lifecycle.PlatformMemberCycleProcessMemberDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 生命周期变更流程关联会员
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
public interface PlatformMemberCycleProcessMemberRepository extends JpaRepository<PlatformMemberCycleProcessMemberDO, Long>, JpaSpecificationExecutor<PlatformMemberCycleProcessMemberDO> {
}
