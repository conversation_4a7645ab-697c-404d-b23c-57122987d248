package com.ssy.lingxi.member.serviceImpl.configManage;

import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.enums.member.MemberConfigTagEnum;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.LanguageEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.detail.*;
import com.ssy.lingxi.member.enums.MemberConfigCheckRuleEnum;
import com.ssy.lingxi.member.enums.MemberConfigFieldTypeEnum;
import com.ssy.lingxi.member.enums.MemberConfigStatusEnum;
import com.ssy.lingxi.member.enums.MemberRegisterConfigNameEnum;
import com.ssy.lingxi.member.model.req.configManage.MemberRegisterConfigLabelReq;
import com.ssy.lingxi.member.model.req.configManage.MemberRegisterConfigPageDataReq;
import com.ssy.lingxi.member.model.req.configManage.MemberRegisterConfigReq;
import com.ssy.lingxi.member.model.resp.configManage.MemberConfigEnumResp;
import com.ssy.lingxi.member.model.resp.configManage.MemberConfigFieldTypeResp;
import com.ssy.lingxi.member.model.resp.configManage.MemberRegisterConfigPageResp;
import com.ssy.lingxi.member.model.resp.configManage.MemberRegisterConfigResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.configManage.IManageMemberRegisterConfigService;
import com.ssy.lingxi.member.util.MemberConfigLabelUtil;
import com.ssy.lingxi.member.util.RgConfigUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 会员注册资料配置接口实现（代码搬迁自原pass）
 * <AUTHOR>
 * @since 2020-05-29
 * @version 2.0.0
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ManageMemberRegisterConfigServiceImpl implements IManageMemberRegisterConfigService {
    private final MemberRoleRepository memberRoleRepository;
    private final MemberRegisterConfigRepository memberRegisterConfigRepository;
    private final MemberRegisterConfigLabelRepository memberRegisterConfigLabelRepository;
    private final MemberRegisterDetailRepository memberRegisterDetailRepository;
    private final MemberDepositoryDetailRepository memberDepositoryDetailRepository;
    private final BaseMemberRuleRepository baseMemberRuleRepository;
    private final MemberRegisterConfigNameRepository registerConfigNameRepository;
    private final IBaseMemberCacheService memberCacheService;
    private final JPAQueryFactory jpaQueryFactory;

    @Override
    public List<MemberConfigFieldTypeResp> getMemberConfigFieldTypeList() {
        memberCacheService.needLoginFromManagePlatform();

        return Arrays.stream(MemberConfigFieldTypeEnum.values())
                .sorted(Comparator.comparingInt(MemberConfigFieldTypeEnum::getCode))
                .map(memberConfigFieldTypeEnum -> new MemberConfigFieldTypeResp(memberConfigFieldTypeEnum.getCode(), memberConfigFieldTypeEnum.getMessage(), memberConfigFieldTypeEnum.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<MemberConfigEnumResp> getMemberConfigTagList() {
        memberCacheService.needLoginFromManagePlatform();

        return Arrays.stream(MemberConfigTagEnum.values())
                .sorted(Comparator.comparingInt(MemberConfigTagEnum::getCode))
                .map(memberConfigTagEnum -> new MemberConfigEnumResp(memberConfigTagEnum.getCode(), memberConfigTagEnum.getTagName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<MemberConfigEnumResp> getMemberConfigCheckRuleList() {
        memberCacheService.needLoginFromManagePlatform();

        return Arrays.stream(MemberConfigCheckRuleEnum.values())
                .sorted(Comparator.comparingInt(MemberConfigCheckRuleEnum::getCode))
                .map(memberConfigCheckRuleEnum -> new MemberConfigEnumResp(memberConfigCheckRuleEnum.getCode(), memberConfigCheckRuleEnum.getRuleName()))
                .collect(Collectors.toList());
    }

    @Override
    public PageDataResp<MemberRegisterConfigPageResp> getRegisterConfigPage(MemberRegisterConfigPageDataReq registerConfigReq) {
        memberCacheService.needLoginFromManagePlatform();

        String currentLanguage = LanguageEnum.getCurrentLanguage();

        QMemberRegisterConfigDO qMemberRegisterConfigDO = QMemberRegisterConfigDO.memberRegisterConfigDO;
        QMemberRegisterConfigNameDO qMemberRegisterConfigNameDO = QMemberRegisterConfigNameDO.memberRegisterConfigNameDO;

        JPAQuery<MemberRegisterConfigDO> jpaQuery = jpaQueryFactory.select(qMemberRegisterConfigDO)
                .from(qMemberRegisterConfigDO)
                .where(qMemberRegisterConfigDO.parentId.isNull().or(qMemberRegisterConfigDO.parentId.eq(0L)));

        if (StringUtils.hasLength(registerConfigReq.getFieldName())) {
            jpaQuery.where(qMemberRegisterConfigDO.fieldName.like("%" + registerConfigReq.getFieldName().trim() + "%"));
        }

        if (StringUtils.hasLength(registerConfigReq.getFieldLocalName())) {
            jpaQuery.where(qMemberRegisterConfigDO.id.in(
                    JPAExpressions.select(qMemberRegisterConfigNameDO.memberRegisterConfig.id)
                            .from(qMemberRegisterConfigNameDO)
                            .where(qMemberRegisterConfigNameDO.name.like("%" + registerConfigReq.getFieldLocalName().trim() + "%")
                                    .and(qMemberRegisterConfigNameDO.fieldType.eq(MemberRegisterConfigNameEnum.FIELD_LOCAL_NAME.getCode()))
                                    .and(qMemberRegisterConfigNameDO.language.eq(currentLanguage)))));
        }

        if (StringUtils.hasLength(registerConfigReq.getFieldGroupName())) {
            jpaQuery.where(qMemberRegisterConfigDO.id.in(
                    JPAExpressions.select(qMemberRegisterConfigNameDO.memberRegisterConfig.id)
                            .from(qMemberRegisterConfigNameDO)
                            .where(qMemberRegisterConfigNameDO.name.like("%" + registerConfigReq.getFieldGroupName().trim() + "%")
                                    .and(qMemberRegisterConfigNameDO.fieldType.eq(MemberRegisterConfigNameEnum.FIELD_GROUP_NAME.getCode()))
                                    .and(qMemberRegisterConfigNameDO.language.eq(currentLanguage)))));
        }

        if (Objects.nonNull(registerConfigReq.getStatus())) {
            jpaQuery.where(qMemberRegisterConfigDO.fieldStatus.eq(registerConfigReq.getStatus()));
        }

        jpaQuery.limit(registerConfigReq.getPageSize())
                .offset((long) (registerConfigReq.getCurrent() - 1) * registerConfigReq.getPageSize())
                .orderBy(qMemberRegisterConfigDO.id.desc());

        return new PageDataResp<>(jpaQuery.fetchCount(), jpaQuery.fetch()
                .stream()
                .map(ManageMemberRegisterConfigServiceImpl::getMemberConfigPageResp)
                .collect(Collectors.toList()));
    }

    @Override
    public MemberRegisterConfigResp getRegisterConfigById(CommonIdReq commonIdReq) {
        memberCacheService.needLoginFromManagePlatform();

        MemberRegisterConfigDO memberRegisterConfigDO = memberRegisterConfigRepository.findById(commonIdReq.getId()).orElse(null);
        BusinessAssertUtil.notNull(memberRegisterConfigDO, ResponseCodeEnum.MC_MS_MEMBER_CONFIG_DOES_NOT_EXIST);

        // 初始化memberConfigResp
        MemberRegisterConfigResp memberRegisterConfigResp = getMemberConfigResp(memberRegisterConfigDO);

        return memberRegisterConfigResp;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MemberRegisterConfigResp addMemberConfig(MemberRegisterConfigReq configVO) {
        memberCacheService.needLoginFromManagePlatform();

        // 检查会员注册资料
        WrapperResp<MemberRegisterConfigResp> checkResult = checkMemberConfig(configVO);
        if(ResponseCodeEnum.SUCCESS.getCode() != checkResult.getCode()){
            //return WrapperUtil.fail(checkResult.getCode(), checkResult.getMessage());
            throw new BusinessException(checkResult.getCode(), checkResult.getMessage());
        }

        // 保存会员注册资料
        MemberRegisterConfigDO memberRegisterConfigDO = saveMemberConfig(configVO, 0L);

        // 如果是列表类型，列表子项有任意一个必填，该列表才是必填
        resetFieldEmpty(memberRegisterConfigDO);

        // 初始化会员注册资料配置返回的VO
        return getMemberRegisterConfigResp(memberRegisterConfigDO);
    }

    private void resetFieldEmpty(MemberRegisterConfigDO memberRegisterConfigDO) {
        if (Objects.equals(MemberConfigFieldTypeEnum.LIST.getMessage(), memberRegisterConfigDO.getFieldType())) {
            memberRegisterConfigDO.setFieldEmpty(
                    Optional.ofNullable(memberRegisterConfigDO.getConfigs())
                            .map(configs -> configs.stream().anyMatch(registerConfigDO -> Objects.equals(EnableDisableStatusEnum.DISABLE.getCode(), registerConfigDO.getFieldEmpty())))
                            .filter(requiredComplete -> requiredComplete)
                            .map(requiredComplete -> EnableDisableStatusEnum.DISABLE.getCode())
                            .orElse(EnableDisableStatusEnum.ENABLE.getCode()));
        }
    }

    private MemberRegisterConfigResp getMemberRegisterConfigResp(MemberRegisterConfigDO memberRegisterConfigDO) {
        MemberRegisterConfigResp registerConfigResp = new MemberRegisterConfigResp();
        registerConfigResp.setId(memberRegisterConfigDO.getId());
        registerConfigResp.setFieldName(memberRegisterConfigDO.getFieldName());
        registerConfigResp.setFieldLocalName(RgConfigUtil.getConfigFieldResp(memberRegisterConfigDO, MemberRegisterConfigNameEnum.FIELD_LOCAL_NAME));
        registerConfigResp.setFieldType(memberRegisterConfigDO.getFieldType());
        registerConfigResp.setAttr(memberRegisterConfigDO.getAttr());
        registerConfigResp.setFieldLength(memberRegisterConfigDO.getFieldLength());
        registerConfigResp.setFieldEmpty(memberRegisterConfigDO.getFieldEmpty());
        registerConfigResp.setFieldOrder(memberRegisterConfigDO.getFieldOrder());
        registerConfigResp.setFieldGroupName(RgConfigUtil.getConfigFieldResp(memberRegisterConfigDO, MemberRegisterConfigNameEnum.FIELD_GROUP_NAME));
        registerConfigResp.setFieldRemark(RgConfigUtil.getConfigFieldResp(memberRegisterConfigDO, MemberRegisterConfigNameEnum.FIELD_REMARK));
        registerConfigResp.setTagEnum(memberRegisterConfigDO.getTagEnum());
        registerConfigResp.setFieldStatus(memberRegisterConfigDO.getFieldStatus());
        registerConfigResp.setValidate(memberRegisterConfigDO.getValidate());
        registerConfigResp.setAllowSelect(memberRegisterConfigDO.getAllowSelect());
        registerConfigResp.setRuleEnum(memberRegisterConfigDO.getRuleEnum());
        registerConfigResp.setFieldEnum(RgConfigUtil.getLabelValues(memberRegisterConfigDO.getLabels()));

        List<MemberRegisterConfigResp> list = new ArrayList<>();
        for (MemberRegisterConfigDO config : memberRegisterConfigDO.getConfigs()) {
            list.add(getMemberRegisterConfigResp(config));
        }
        registerConfigResp.setConfigs(list);

        return registerConfigResp;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MemberRegisterConfigDO saveMemberConfig(MemberRegisterConfigReq configVO, Long parentId) {
        memberCacheService.needLoginFromManagePlatform();

        MemberRegisterConfigDO memberRegisterConfigDO = new MemberRegisterConfigDO();
        memberRegisterConfigDO.setFieldName(configVO.getFieldName());
        memberRegisterConfigDO.setFieldType(Optional.ofNullable(configVO.getFieldType()).map(String::trim).orElse(""));
        memberRegisterConfigDO.setFieldTypeName(MemberConfigFieldTypeEnum.getConfigFieldTypeName(configVO.getFieldType()).getName());
        memberRegisterConfigDO.setAttr(configVO.getAttr());
        memberRegisterConfigDO.setFieldLength(NumberUtil.isNullOrLteZero(configVO.getFieldLength()) ? 300 : configVO.getFieldLength());
        memberRegisterConfigDO.setFieldEmpty(configVO.getFieldEmpty());
        memberRegisterConfigDO.setFieldOrder(configVO.getFieldOrder());
        //新增时默认为启用状态
        memberRegisterConfigDO.setFieldStatus(MemberConfigStatusEnum.ENABLED.getCode());
        memberRegisterConfigDO.setTagEnum(Optional.ofNullable(configVO.getTagEnum()).orElse(0));
        memberRegisterConfigDO.setTagName(MemberConfigTagEnum.findTagName(configVO.getTagEnum()));
        memberRegisterConfigDO.setValidate(Optional.ofNullable(configVO.getValidate()).orElse(0));
        memberRegisterConfigDO.setRuleEnum(Optional.ofNullable(configVO.getRuleEnum()).orElse(0));
        memberRegisterConfigDO.setAllowSelect(Optional.ofNullable(configVO.getAllowSelect()).orElse(0));
        memberRegisterConfigDO.setParentId(parentId);
        //写入注册时间
        memberRegisterConfigDO.setCreateTime(LocalDateTime.now());
        memberRegisterConfigDO.setUpdateTime(LocalDateTime.now());

        //保存会员注册资料
        memberRegisterConfigRepository.save(memberRegisterConfigDO);

        //保存注册资料国际化翻译
        List<MemberRegisterConfigNameDO> memberRegisterConfigNameDOList = setRegisterConfigNameI18n(memberRegisterConfigDO, configVO);
        registerConfigNameRepository.saveAll(memberRegisterConfigNameDOList);

        //保存会员注册资料
        memberRegisterConfigDO.setConfigNameSet(new HashSet<>(memberRegisterConfigNameDOList));
        memberRegisterConfigRepository.save(memberRegisterConfigDO);

        // 保存下拉框值
        List<MemberRegisterConfigLabelReq> fieldEnumList = configVO.getFieldEnum();
        List<MemberRegisterConfigLabelDO> itemList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(fieldEnumList)) {
            for (int i = 1; i <= fieldEnumList.size(); i++) {
                MemberRegisterConfigLabelDO configLabelDO = new MemberRegisterConfigLabelDO();
                configLabelDO.setLabelOrder(i);
                configLabelDO.setMemberRegisterConfig(memberRegisterConfigDO);
                memberRegisterConfigLabelRepository.save(configLabelDO);

                // 保存下拉框多语言
                MemberRegisterConfigLabelReq memberRegisterConfigLabelReq = fieldEnumList.get(i - 1);
                List<MemberRegisterConfigNameDO> memberRegisterConfigLabelNameDOList = setRegisterLabelConfigI18n(configLabelDO, memberRegisterConfigLabelReq);
                registerConfigNameRepository.saveAll(memberRegisterConfigLabelNameDOList);

                //再保存一次下拉框值
                configLabelDO.setConfigNameSet(new HashSet<>(memberRegisterConfigLabelNameDOList));
                memberRegisterConfigLabelRepository.save(configLabelDO);

                itemList.add(configLabelDO);
            }
        }

        //再保存一次会员注册资料
        memberRegisterConfigDO.setLabels(new HashSet<>(itemList));
        memberRegisterConfigDO.setConfigNameSet(new HashSet<>(memberRegisterConfigNameDOList));
        memberRegisterConfigRepository.save(memberRegisterConfigDO);

        // 列表类型相关逻辑
        List<MemberRegisterConfigDO> list = new ArrayList<>();
        for (MemberRegisterConfigReq config : configVO.getConfigs()) {
            list.add(saveMemberConfig(config, memberRegisterConfigDO.getId()));
        }
        memberRegisterConfigDO.setConfigs(list);

        return memberRegisterConfigDO;
    }

    /**
     * 注册资料要针对列表做调整
     * 1.列表内名称要唯一
     * 2.列表内名称不影响列表外名称
     */
    private WrapperResp<MemberRegisterConfigResp> checkMemberConfig(MemberRegisterConfigReq configVO) {
        // 查找字段名称是否已经存在
        if (configVO.getId() != null) {
            // 修改时根据id查询
            BusinessAssertUtil.isFalse(checkFieldNameExists(configVO, true)
                            || checkFieldLocalNameExists(configVO, true),
                    ResponseCodeEnum.MC_MS_FIELD_NAME_EXISTS);
        } else {
            // 新增根据字段名称查询
            BusinessAssertUtil.isFalse(checkFieldNameExists(configVO, false)
                            || checkFieldLocalNameExists(configVO, false),
                    ResponseCodeEnum.MC_MS_FIELD_NAME_EXISTS);
        }

        // 字符串、数字、文件类型需要校验字段长度
        if ((MemberConfigFieldTypeEnum.STRING.getMessage().equals(configVO.getFieldType())
                || MemberConfigFieldTypeEnum.NUMBER.getMessage().equals(configVO.getFieldType())
                || MemberConfigFieldTypeEnum.FILE.getMessage().equals(configVO.getFieldType()))
                && NumberUtil.isNullOrZero(configVO.getFieldLength())) {
            return WrapperUtil.fail(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "字段长度要大于1");
        }

        if(!NumberUtil.isNullOrLteZero(configVO.getTagEnum())) {
            if(!MemberConfigTagEnum.isEnumCode(configVO.getTagEnum())) {
                return WrapperUtil.fail(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "字段标签枚举不在定义范围内");
            }

            // 判断是否被定义
            if (Objects.nonNull(configVO.getId()) && memberRegisterConfigRepository.existsByTagEnumAndIdNotAndParentIdEquals(configVO.getTagEnum(), configVO.getId(), 0L)) {
                return WrapperUtil.fail(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "字段标签：" + MemberConfigTagEnum.findTagName(configVO.getTagEnum()) + " 已经定义");
            } else if (Objects.isNull(configVO.getId()) && memberRegisterConfigRepository.existsByTagEnumAndParentIdEquals(configVO.getTagEnum(), 0L)) {
                return WrapperUtil.fail(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "字段标签：" + MemberConfigTagEnum.findTagName(configVO.getTagEnum()) + " 已经定义");
            }
        }

        if (!CollectionUtils.isEmpty(configVO.getConfigs())) {
            // 列表里存在重复字段
            if (configVO.getConfigs().size() != configVO.getConfigs().stream().distinct().count()) {
                return WrapperUtil.fail(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "列表里存在重复字段");
            }

            if (configVO.getConfigs().size() != configVO.getConfigs().stream().map(MemberRegisterConfigReq::getFieldOrder).distinct().count()) {
                return WrapperUtil.fail(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "列表子字段排序重复");
            }

            //列表字段暂无需详细校验
//            for (MemberRegisterConfigReq config : configVO.getConfigs()) {
//                Wrapper<MemberRegisterConfigResp> checkResult = checkMemberConfig(config);
//                if(ResponseCode.SUCCESS.getCode() != checkResult.getCode()){
//                    return Wrapper.fail(checkResult.getCode(), checkResult.getNameByCode());
//                }
//            }
        }
        return WrapperUtil.success();
    }

    /**
     * 判断fieldName是否已经存在
     */
    private boolean checkFieldNameExists(MemberRegisterConfigReq configVO, boolean filterSelf) {
        return memberRegisterConfigRepository.existsByFieldNameAndParentIdAndIdNot(configVO.getFieldName(), 0L, filterSelf ? configVO.getId() : 0L);
    }

    /**
     * 判断fieldLocalName是否已经存在
     */
    private boolean checkFieldLocalNameExists(MemberRegisterConfigReq configVO, boolean filterSelf) {
        return configVO.getFieldLocalName()
                .stream()
                .anyMatch(fieldLocalName -> registerConfigNameRepository
                        .existsByLanguageAndNameAndFieldTypeAndRegisterConfigParentIdAndMemberRegisterConfigIdNot(
                                fieldLocalName.getLanguage(),
                                fieldLocalName.getValue(),
                                MemberRegisterConfigNameEnum.FIELD_LOCAL_NAME.getCode(),
                                0L,
                                filterSelf ? configVO.getId() : 0L
                        ));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MemberRegisterConfigResp updateMemberConfig(MemberRegisterConfigReq configVO) {
        memberCacheService.needLoginFromManagePlatform();

        //检查会员资料配置是否存在
        MemberRegisterConfigDO memberRegisterConfigDO = memberRegisterConfigRepository.findById(configVO.getId()).orElse(null);
        if(memberRegisterConfigDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_DETAIL_DOES_NOT_EXIST);
        }

        //如果注册资料正在使用中，就不允许修改
        if (!CollectionUtils.isEmpty(memberRegisterConfigDO.getRoles())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_IN_USE_REGISTER_CONFIG_CANNOT_BE_MODIFIED);
        }

        // 检查会员注册资料
        WrapperResp<MemberRegisterConfigResp> checkResult = checkMemberConfig(configVO);
        if(ResponseCodeEnum.SUCCESS.getCode() != checkResult.getCode()){

            throw new BusinessException(checkResult.getCode(), checkResult.getMessage());
        }

        WrapperResp<List<MemberRegisterConfigLabelDO>> updateResult = updateMemberConfig(configVO, memberRegisterConfigDO, 0L);
        if(ResponseCodeEnum.SUCCESS.getCode() != updateResult.getCode()){


            throw new BusinessException(updateResult.getCode(), updateResult.getMessage());
        }

        // 如果是列表类型，列表子项有任意一个必填，该列表才是必填
        resetFieldEmpty(memberRegisterConfigDO);

        //更新所有会员注册资料配置
        List<MemberRegisterDetailDO> registerDetails = memberRegisterDetailRepository.findByMemberConfig(memberRegisterConfigDO);
        if(!CollectionUtils.isEmpty(registerDetails)) {
            registerDetails.forEach(registerDetail -> {
                registerDetail.setFieldType(memberRegisterConfigDO.getFieldType());
                registerDetail.setAttr(memberRegisterConfigDO.getAttr());
                registerDetail.setValidate(memberRegisterConfigDO.getValidate());
                registerDetail.setStatus(memberRegisterConfigDO.getFieldStatus());
                MemberConfigLabelUtil.removeOrUpdateIdByValue(registerDetail.getLabels(), updateResult.getData());
            });
            memberRegisterDetailRepository.saveAll(registerDetails);
        }

        // 初始化会员注册资料配置返回的VO
        return getMemberRegisterConfigResp(memberRegisterConfigDO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public WrapperResp<List<MemberRegisterConfigLabelDO>> updateMemberConfig(MemberRegisterConfigReq configVO, MemberRegisterConfigDO memberRegisterConfigDO, Long parentId) {
        memberCacheService.needLoginFromManagePlatform();

        memberRegisterConfigDO.setFieldName(configVO.getFieldName());
        memberRegisterConfigDO.setFieldType(configVO.getFieldType());
        memberRegisterConfigDO.setFieldTypeName(MemberConfigFieldTypeEnum.getConfigFieldTypeName(configVO.getFieldType()).getName());
        memberRegisterConfigDO.setAttr(configVO.getAttr());
        memberRegisterConfigDO.setFieldLength(NumberUtil.isNullOrLteZero(configVO.getFieldLength()) ? 300 : configVO.getFieldLength());
        memberRegisterConfigDO.setFieldEmpty(configVO.getFieldEmpty());
        memberRegisterConfigDO.setFieldOrder(configVO.getFieldOrder());
        memberRegisterConfigDO.setTagEnum(Optional.ofNullable(configVO.getTagEnum()).orElse(0));
        memberRegisterConfigDO.setTagName(MemberConfigTagEnum.findTagName(configVO.getTagEnum()));
        memberRegisterConfigDO.setRuleEnum(Optional.ofNullable(configVO.getRuleEnum()).orElse(0));
        memberRegisterConfigDO.setValidate(configVO.getValidate());
        //修改信息后，会自动将状态设置为“有效”
        memberRegisterConfigDO.setFieldStatus(EnableDisableStatusEnum.ENABLE.getCode());
        memberRegisterConfigDO.setAllowSelect(Optional.ofNullable(configVO.getAllowSelect()).orElse(0));
        memberRegisterConfigDO.setParentId(parentId);
        memberRegisterConfigDO.setUpdateTime(LocalDateTime.now());
        memberRegisterConfigRepository.save(memberRegisterConfigDO);

        //删除旧的多语言，按最新的重新新增
        registerConfigNameRepository.deleteAllByMemberRegisterConfigId(memberRegisterConfigDO.getId());
        List<MemberRegisterConfigNameDO> memberRegisterConfigNameDOList = setRegisterConfigNameI18n(memberRegisterConfigDO, configVO);
        registerConfigNameRepository.saveAll(memberRegisterConfigNameDOList);

        //删除旧的标签，按最新的重新新增
        if (!CollectionUtils.isEmpty(memberRegisterConfigDO.getLabels())) {
            memberRegisterConfigLabelRepository.deleteAll(memberRegisterConfigDO.getLabels());
        }

        // 保存下拉框值
        List<MemberRegisterConfigLabelReq> fieldEnumList = configVO.getFieldEnum();
        List<MemberRegisterConfigLabelDO> items = new ArrayList<>();
        if (!CollectionUtils.isEmpty(fieldEnumList)) {
            for (int i = 0; i < fieldEnumList.size(); i++) {
                MemberRegisterConfigLabelDO configLabelDO = new MemberRegisterConfigLabelDO();
                configLabelDO.setLabelOrder(i + 1);
                configLabelDO.setMemberRegisterConfig(memberRegisterConfigDO);
                memberRegisterConfigLabelRepository.save(configLabelDO);

                // 保存下拉框多语言
                MemberRegisterConfigLabelReq memberRegisterConfigLabelReq = fieldEnumList.get(i);
                registerConfigNameRepository.saveAll(setRegisterLabelConfigI18n(configLabelDO, memberRegisterConfigLabelReq));

                //再保存一次下拉框值
                configLabelDO.setConfigNameSet(new HashSet<>(memberRegisterConfigNameDOList));
                memberRegisterConfigLabelRepository.save(configLabelDO);

                items.add(configLabelDO);
            }
        }

        //再保存一次会员注册资料
        memberRegisterConfigDO.setLabels(new HashSet<>(items));
        memberRegisterConfigDO.setConfigNameSet(new HashSet<>(memberRegisterConfigNameDOList));
        memberRegisterConfigRepository.save(memberRegisterConfigDO);

        List<MemberRegisterConfigDO> configs = new ArrayList<>();
        // 查询当前资料是否有子字段
        List<MemberRegisterConfigDO> childConfigs = memberRegisterConfigRepository.findAllByParentId(memberRegisterConfigDO.getId());

        // 筛选出待新增、更新
        List<Long> updIds = Optional.ofNullable(configVO.getConfigs())
                .map(configReqList -> configReqList.stream().map(MemberRegisterConfigReq::getId).filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        List<MemberRegisterConfigReq> saveConfigs = Optional.ofNullable(configVO.getConfigs())
                .map(configReqList -> configReqList.stream().filter(c -> c.getId() == null || updIds.contains(c.getId()))
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());

        // 更新或新增
        for (MemberRegisterConfigReq config : saveConfigs) {
            //检查会员资料配置是否存在
            MemberRegisterConfigDO memberConfig = Optional.ofNullable(config.getId()).map(id -> memberRegisterConfigRepository.findById(id)).flatMap(mrf -> mrf).orElse(new MemberRegisterConfigDO());
            WrapperResp<List<MemberRegisterConfigLabelDO>> updateResult = updateMemberConfig(config, memberConfig, memberRegisterConfigDO.getId());
            //更新所有会员注册资料配置
            List<MemberRegisterDetailDO> registerDetails = memberRegisterDetailRepository.findByMemberConfig(memberConfig);
            if(!CollectionUtils.isEmpty(registerDetails)) {
                registerDetails.forEach(registerDetail -> {
                    registerDetail.setFieldType(memberConfig.getFieldType());
                    registerDetail.setAttr(memberConfig.getAttr());
                    registerDetail.setValidate(memberConfig.getValidate());
                    registerDetail.setStatus(memberConfig.getFieldStatus());
                    MemberConfigLabelUtil.removeOrUpdateIdByValue(registerDetail.getLabels(), updateResult.getData());
                });
                memberRegisterDetailRepository.saveAll(registerDetails);
            }
            configs.add(memberConfig);
        }

        // 筛选出待删除
        List<MemberRegisterConfigDO> deleteConfigs = childConfigs.stream().filter(c -> !updIds.contains(c.getId())).collect(Collectors.toList());
        // 删除会员配置资料
        deleteConfigs.forEach(this::deleteMemberConfig);

        memberRegisterConfigDO.setConfigs(configs);

        return WrapperUtil.success(items);
    }

    @Override
    public WrapperResp<PageDataResp<MemberRegisterConfigResp>> pageMemberConfig(PageDataReq pageDataReq) {
        memberCacheService.needLoginFromManagePlatform();

        // "id"是实体类的主键，记住一定要是实体类的属性，而不能是数据库的字段
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize(), Sort.by("id").ascending());
        Page<MemberRegisterConfigDO> result = memberRegisterConfigRepository.findAll(page);

        List<MemberRegisterConfigResp> resultList = result.getContent().stream().map(r -> {
            MemberRegisterConfigResp registerConfigResp = new MemberRegisterConfigResp();
            BeanUtils.copyProperties(r, registerConfigResp);
            return registerConfigResp;
        }).collect(Collectors.toList());

        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(),resultList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMemberConfig(MemberRegisterConfigReq configReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 检查待删除数据
        WrapperResp<MemberRegisterConfigDO> checkResult = checkToDelete(configReq.getId());
        if (ResponseCodeEnum.SUCCESS.getCode() != checkResult.getCode()) {

            throw new BusinessException(checkResult.getCode(), checkResult.getMessage());
        }
        MemberRegisterConfigDO configDO = checkResult.getData();

        // 删除会员配置资料
        deleteMemberConfig(configDO);


    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMemberConfig(MemberRegisterConfigDO configDO) {
        memberCacheService.needLoginFromManagePlatform();

        // 循环、递归删除
        List<MemberRegisterConfigDO> childConfig = memberRegisterConfigRepository.findAllByParentId(configDO.getId());
        childConfig.forEach(this::deleteMemberConfig);

        //从角色关联中删除
        List<MemberRoleDO> roleList = memberRoleRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            Join<MemberRoleDO, MemberRegisterConfigDO> configJoin = root.join("configs", JoinType.LEFT);
            list.add(criteriaBuilder.equal(configJoin.get("id").as(Long.class), configDO.getId()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        });
        if(!CollectionUtils.isEmpty(roleList)) {
            roleList.forEach(memberRoleDO -> memberRoleDO.getConfigs().removeIf(config -> config.getId().equals(configDO.getId())));
            memberRoleRepository.saveAll(roleList);
        }

        //删除已经存在的会员注册资料
        memberRegisterDetailRepository.deleteByMemberConfig(configDO);

        //删除已经存在的入库资料
        memberDepositoryDetailRepository.deleteByMemberConfig(configDO);

        //删除当前实体（此操作删除当前实体）
        memberRegisterConfigRepository.delete(configDO);
    }

    private WrapperResp<MemberRegisterConfigDO> checkToDelete(Long id) {
        MemberRegisterConfigDO configDO = memberRegisterConfigRepository.findById(id).orElse(null);
        if(configDO == null) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_DETAIL_DOES_NOT_EXIST);
        }
        //如果注册资料正在使用中，就不允许更改状态
        if (!CollectionUtils.isEmpty(configDO.getRoles())) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_IN_USE_REGISTER_CONFIG_CANNOT_BE_MODIFIED);
        }
        // 递归校验
        List<MemberRegisterConfigDO> childConfig = memberRegisterConfigRepository.findAllByParentId(id);
        for (MemberRegisterConfigDO config : childConfig) {
            WrapperResp<MemberRegisterConfigDO> checkResult = checkToDelete(config.getId());
            if (ResponseCodeEnum.SUCCESS.getCode() != checkResult.getCode()) {
                return WrapperUtil.fail(checkResult.getCode(), checkResult.getMessage());
            }
        }
        return WrapperUtil.success(configDO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMemberConfigStatus(MemberRegisterConfigReq configReq) {
        memberCacheService.needLoginFromManagePlatform();

        //检查会员资料配置是否存在
        MemberRegisterConfigDO memberRegisterConfigDO = memberRegisterConfigRepository.findById(configReq.getId()).orElse(null);
        BusinessAssertUtil.notNull(memberRegisterConfigDO, ResponseCodeEnum.MC_MS_DETAIL_DOES_NOT_EXIST);

        //如果注册资料正在使用中，就不允许更改状态
        Set<MemberRoleDO> memberRoleDOSet = memberRegisterConfigDO.getRoles();
        if (!CollectionUtils.isEmpty(memberRoleDOSet)) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_IN_USE_REGISTER_CONFIG_CANNOT_BE_MODIFIED,
                    "该字段已被" + memberRoleDOSet.stream().findFirst().map(MemberRoleDO::getRoleName).orElse("") + "关联为注册资料，暂不可设为无效");
        }

        memberRegisterConfigDO.setFieldStatus(configReq.getFieldStatus());
        memberRegisterConfigRepository.saveAndFlush(memberRegisterConfigDO);

        //更新已经存在的会员注册资料的状态
        List<MemberRegisterDetailDO> registerDetails = memberRegisterDetailRepository.findByMemberConfig(memberRegisterConfigDO);
        if(!CollectionUtils.isEmpty(registerDetails)) {
            registerDetails.forEach(registerDetail -> registerDetail.setStatus(memberRegisterConfigDO.getFieldStatus()));
            memberRegisterDetailRepository.saveAll(registerDetails);
        }


    }

    public MemberRegisterConfigResp getMemberConfigResp(MemberRegisterConfigDO memberConfig) {
        MemberRegisterConfigResp memberRegisterConfigResp = new MemberRegisterConfigResp();
        memberRegisterConfigResp.setId(memberConfig.getId());
        memberRegisterConfigResp.setFieldName(memberConfig.getFieldName());
        memberRegisterConfigResp.setFieldLocalName(RgConfigUtil.getConfigFieldResp(memberConfig, MemberRegisterConfigNameEnum.FIELD_LOCAL_NAME));
        memberRegisterConfigResp.setFieldType(memberConfig.getFieldType());
        memberRegisterConfigResp.setAttr(memberConfig.getAttr());
        memberRegisterConfigResp.setAllowSelect(memberConfig.getAllowSelect() == null ? 0 : memberConfig.getAllowSelect());
        memberRegisterConfigResp.setFieldLength(memberConfig.getFieldLength() == null ? 0 : memberConfig.getFieldLength());
        memberRegisterConfigResp.setFieldEmpty(memberConfig.getFieldEmpty() == null ? 0 : memberConfig.getFieldEmpty());
        memberRegisterConfigResp.setFieldGroupName(RgConfigUtil.getConfigFieldResp(memberConfig, MemberRegisterConfigNameEnum.FIELD_GROUP_NAME));
        memberRegisterConfigResp.setFieldOrder(memberConfig.getFieldOrder());
        memberRegisterConfigResp.setFieldRemark(RgConfigUtil.getConfigFieldResp(memberConfig, MemberRegisterConfigNameEnum.FIELD_REMARK));
        memberRegisterConfigResp.setTagEnum(memberConfig.getTagEnum());
        memberRegisterConfigResp.setValidate(memberConfig.getValidate());
        memberRegisterConfigResp.setRuleEnum(memberConfig.getRuleEnum());
        memberRegisterConfigResp.setParentId(memberConfig.getParentId());

        List<MemberRegisterConfigLabelDO> labels = memberRegisterConfigLabelRepository.findByMemberRegisterConfigId(memberConfig.getId());
        memberRegisterConfigResp.setFieldEnum(RgConfigUtil.getLabelValues(labels));

        if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(memberConfig.getFieldType())) {
            memberRegisterConfigResp.setConfigs(memberRegisterConfigRepository.findAllByParentId(memberConfig.getId()).stream().map(this::getMemberConfigResp).collect(Collectors.toList()));
        }
        return memberRegisterConfigResp;
    }

    public static MemberRegisterConfigPageResp getMemberConfigPageResp(MemberRegisterConfigDO memberRegisterConfigDO) {
        MemberRegisterConfigPageResp configPageResp = new MemberRegisterConfigPageResp();
        configPageResp.setId(memberRegisterConfigDO.getId());
        configPageResp.setFieldName(memberRegisterConfigDO.getFieldName());
        configPageResp.setFieldLocalName(RgConfigUtil.getConfigFieldResp(memberRegisterConfigDO, MemberRegisterConfigNameEnum.FIELD_LOCAL_NAME));
        configPageResp.setFieldType(memberRegisterConfigDO.getFieldType());
        configPageResp.setFieldTypeName(memberRegisterConfigDO.getFieldTypeName());
        configPageResp.setAttr(memberRegisterConfigDO.getAttr());
        configPageResp.setFieldLength(memberRegisterConfigDO.getFieldLength());
        configPageResp.setFieldEmpty(memberRegisterConfigDO.getFieldEmpty());
        configPageResp.setFieldGroupName(RgConfigUtil.getConfigFieldResp(memberRegisterConfigDO, MemberRegisterConfigNameEnum.FIELD_GROUP_NAME));
        configPageResp.setFieldOrder(memberRegisterConfigDO.getFieldOrder());
        configPageResp.setFieldStatus(memberRegisterConfigDO.getFieldStatus());
        configPageResp.setFieldRemark(RgConfigUtil.getConfigFieldResp(memberRegisterConfigDO, MemberRegisterConfigNameEnum.FIELD_REMARK));
        configPageResp.setCreateTime(DateTimeUtil.formatDateTime(memberRegisterConfigDO.getCreateTime()));
        configPageResp.setUpdateTime(DateTimeUtil.formatDateTime(memberRegisterConfigDO.getUpdateTime()));
        configPageResp.setTagEnum(memberRegisterConfigDO.getTagEnum());
        configPageResp.setRuleEnum(memberRegisterConfigDO.getRuleEnum());
        configPageResp.setValidate(memberRegisterConfigDO.getValidate());
        configPageResp.setAllowSelect(memberRegisterConfigDO.getAllowSelect());
        configPageResp.setConfigs(Optional.ofNullable(memberRegisterConfigDO.getConfigs())
                .map(childMemberConfigDO -> childMemberConfigDO
                        .stream()
                        .map(ManageMemberRegisterConfigServiceImpl::getMemberConfigPageResp)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>()));
        return configPageResp;
    }

    private List<MemberRegisterConfigNameDO> setRegisterConfigNameI18n(MemberRegisterConfigDO memberRegisterConfigDO, MemberRegisterConfigReq configReq) {
        List<MemberRegisterConfigNameDO> memberRegisterConfigNameDOList = new ArrayList<>();

        memberRegisterConfigNameDOList.addAll(configReq.getFieldLocalName().stream().map(nameReq -> {
            MemberRegisterConfigNameDO registerConfigNameDO = new MemberRegisterConfigNameDO();
            registerConfigNameDO.setMemberRegisterConfig(memberRegisterConfigDO);
            registerConfigNameDO.setRegisterConfigParentId(memberRegisterConfigDO.getParentId());
            registerConfigNameDO.setLanguage(nameReq.getLanguage());
            registerConfigNameDO.setName(nameReq.getValue());
            registerConfigNameDO.setFieldType(MemberRegisterConfigNameEnum.FIELD_LOCAL_NAME.getCode());
            return registerConfigNameDO;
        }).collect(Collectors.toList()));

        memberRegisterConfigNameDOList.addAll(configReq.getFieldGroupName().stream().map(nameReq -> {
            MemberRegisterConfigNameDO registerConfigNameDO = new MemberRegisterConfigNameDO();
            registerConfigNameDO.setMemberRegisterConfig(memberRegisterConfigDO);
            registerConfigNameDO.setRegisterConfigParentId(memberRegisterConfigDO.getParentId());
            registerConfigNameDO.setLanguage(nameReq.getLanguage());
            registerConfigNameDO.setName(nameReq.getValue());
            registerConfigNameDO.setFieldType(MemberRegisterConfigNameEnum.FIELD_GROUP_NAME.getCode());
            return registerConfigNameDO;
        }).collect(Collectors.toList()));

        memberRegisterConfigNameDOList.addAll(configReq.getFieldRemark().stream().map(nameReq -> {
            MemberRegisterConfigNameDO registerConfigNameDO = new MemberRegisterConfigNameDO();
            registerConfigNameDO.setMemberRegisterConfig(memberRegisterConfigDO);
            registerConfigNameDO.setRegisterConfigParentId(memberRegisterConfigDO.getParentId());
            registerConfigNameDO.setLanguage(nameReq.getLanguage());
            registerConfigNameDO.setName(nameReq.getValue());
            registerConfigNameDO.setFieldType(MemberRegisterConfigNameEnum.FIELD_REMARK.getCode());
            return registerConfigNameDO;
        }).collect(Collectors.toList()));

        return memberRegisterConfigNameDOList;
    }

    private List<MemberRegisterConfigNameDO> setRegisterLabelConfigI18n(MemberRegisterConfigLabelDO configLabelDO, MemberRegisterConfigLabelReq configReq) {
        return configReq.getItems().stream().map(nameReq -> {
            MemberRegisterConfigNameDO registerConfigNameDO = new MemberRegisterConfigNameDO();
            registerConfigNameDO.setMemberRegisterConfigLabel(configLabelDO);
            registerConfigNameDO.setRegisterConfigParentId(configLabelDO.getMemberRegisterConfig().getId());
            registerConfigNameDO.setLanguage(nameReq.getLanguage());
            registerConfigNameDO.setName(nameReq.getValue());
            registerConfigNameDO.setFieldType(MemberRegisterConfigNameEnum.LABEL_VALUE.getCode());
            return registerConfigNameDO;
        }).collect(Collectors.toList());
    }
}
