package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.member.handler.annotation.DateStringFormatAnnotation;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 会员能力 - 会员维护 - 会员分页查询接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberAbilityMaintenanceMemberQueryDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -3617293909137040335L;
    /**
     * 会员名称
     */
    private String name;

    /**
     * 申请开始时间， 格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation
    private String startDate;

    /**
     * 申请结束时间，格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation
    private String endDate;

    /**
     * 内部状态
     */
    private Integer innerStatus;

    /**
     * 外部状态
     */
    private Integer outerStatus;

    /**
     * 会员状态， Null或0-所有； 1-正常 2-冻结
     */
    private Integer status;

    /**
     * 会员类型， 0或Null-所有 1-企业会员 2-企业个人会员 3-渠道企业会员 4-渠道个人会员
     */
    private Long memberType;

    /**
     * 角色， 0或Null-所有， 其他枚举从接口下拉菜单字段中获取
     */
    private Long roleId;

    /**
     * 等级， 0或Null-所有，其他枚举从接口下拉菜单字段中获取
     */
    private Integer level;

    /**
     * 注册来源， 0或Null-所有， 其他枚举从下拉菜单接口中获得
     */
    private Integer source;

    /**
     * 会员注册资料筛选项
     */
    private Map<String,Object> memberConfigs;

    /**
     * 会员编码
     */
    private String code;

    /**
     * 币别枚举
     * 1: CNY-人民币
     * 2: USD-美元
     * 3: JPY-日元
     * 4: EUR-欧元
     */
    private Integer currencyType;

    /**
     * 品类id
     */
    private List<Long> categoryId;

    /**
     * 会员ID
     */
    private Long subMemberId;

    /**
     * 会员账户
     */
    private String account;

    /**
     * 生命周期阶段ID
     */
    private Long lifeCycleStageId;

    /**
     * 判断是否审核通过(审核通过，0-否，1-是)
     */
    private Integer verified;
}
