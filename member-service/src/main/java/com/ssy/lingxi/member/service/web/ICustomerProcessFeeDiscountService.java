package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.api.member.CustomerProcessFeeDiscountSyncReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.api.model.req.CustomerCalReq;
import com.ssy.lingxi.member.model.req.discount.web.CustomerProcessFeeDiscountPageReq;
import com.ssy.lingxi.member.model.req.discount.web.CustomerProcessFeeDiscountReq;
import com.ssy.lingxi.member.model.resp.customer.CustomerProcessFeeDiscountDetailResp;
import com.ssy.lingxi.member.model.resp.customer.CustomerProcessFeeDiscountPageResp;
import com.ssy.lingxi.member.api.model.resp.MobileCustomerFeeDiscountResp;
import org.springframework.http.HttpHeaders;

import javax.validation.Valid;
import java.util.List;

/**
 * 客户工费优惠服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-26
 */
public interface ICustomerProcessFeeDiscountService {


    /**
     * 同步客户工费优惠数据
     *
     * @param customerProcessFeeDiscountSyncReq 同步请求参数
     * @return 同步结果
     */
    Boolean sync(CustomerProcessFeeDiscountSyncReq customerProcessFeeDiscountSyncReq);

    /**
     * 分页查询客户工费优惠列表
     *
     * @param headers HTTP请求头信息
     * @param pageReq 分页查询请求参数
     * @return 分页查询结果
     */
    PageDataResp<CustomerProcessFeeDiscountPageResp> getPageList(HttpHeaders headers, CustomerProcessFeeDiscountPageReq pageReq);

    /**
     * 查看客户工费优惠详情
     *
     * @param headers HTTP请求头信息
     * @param idReq   ID请求参数
     * @return 详情信息
     */
    CustomerProcessFeeDiscountDetailResp getDetail(HttpHeaders headers, CommonIdReq idReq);

    /**
     * 新增客户工费优惠
     *
     * @param headers HTTP请求头信息
     * @param req     新增请求参数
     * @return 新增结果
     */
    Boolean add(HttpHeaders headers, CustomerProcessFeeDiscountReq req);

    /**
     * 编辑客户工费优惠
     *
     * @param headers HTTP请求头信息
     * @param req     编辑请求参数
     * @return 编辑结果
     */
    Boolean update(HttpHeaders headers, CustomerProcessFeeDiscountReq req);

    /**
     * 删除客户工费优惠
     *
     * @param headers HTTP请求头信息
     * @param idReq   ID请求参数
     * @return 删除结果
     */
    Boolean delete(HttpHeaders headers, CommonIdReq idReq);

    /**
     * 启用客户工费优惠
     *
     * @param headers   HTTP请求头信息
     * @param idListReq ID列表请求参数
     * @return 启用结果
     */
    Boolean enable(HttpHeaders headers, CommonIdListReq idListReq);

    /**
     * 停用客户工费优惠
     *
     * @param headers   HTTP请求头信息
     * @param idListReq ID列表请求参数
     * @return 停用结果
     */
    Boolean disable(HttpHeaders headers, CommonIdListReq idListReq);


    /**
     * 计算工费优惠（内部接口）
     *
     * @param customerCalReq 计算请求参数
     * @return 工费计算结果列表
     */
    List<MobileCustomerFeeDiscountResp> calculateDiscount(@Valid CustomerCalReq customerCalReq);
}

