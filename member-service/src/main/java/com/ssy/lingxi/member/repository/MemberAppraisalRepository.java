package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.appraisal.MemberAppraisalDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员考评Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Repository
public interface MemberAppraisalRepository extends JpaRepository<MemberAppraisalDO, Long>, JpaSpecificationExecutor<MemberAppraisalDO> {

}
