package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;

/**
 * 会员权益相关计算异步接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-11-18
 */
public interface IBaseMemberRightAsyncService {

    /**
     * 交易完成后，同时计算下级会员的交易返现金额、权益积分，再计算等级积分
     * @param upperMemberId 上级会员Id（卖方）
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId 下级会员Id（买方）
     * @param subRoleId 下级会员角色Id
     * @param tradeAmount 交易金额
     * @param orderNo 订单号
     */
    void calculateMemberTradeRightThenLevelScore(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, BigDecimal tradeAmount, String orderNo);


    /**
     * 交易完成后，同时计算下级会员的交易返现金额、权益积分
     * @param upperMemberId 上级会员Id（卖方）
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId 下级会员Id（买方）
     * @param subRoleId 下级会员角色Id
     * @param tradeAmount 交易金额
     * @param orderNo 订单号
     */
    void calculateMemberTradeRight(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, BigDecimal tradeAmount, String orderNo);

    /**
     * 交易完成后，计算下级会员的交易返现金额
     * @param upperMemberId 上级会员Id（卖方）
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId 下级会员Id（买方）
     * @param subRoleId 下级会员角色Id
     * @param tradeAmount 交易金额
     * @param orderNo 订单号
     * @return 异步计算结果
     */
    CompletableFuture<Boolean> calculateMemberReturnMoney(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, BigDecimal tradeAmount, String orderNo);

    /**
     * 交易完成后，计算下级会员的权益积分
     * @param upperMemberId 上级会员Id（卖方）
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId 下级会员Id（买方）
     * @param subRoleId 下级会员角色Id
     * @param tradeAmount 交易金额
     * @param orderNo 订单号
     * @return 异步计算结果
     */
    CompletableFuture<Boolean> calculateMemberRightScore(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, BigDecimal tradeAmount, String orderNo);

    /**
     * 积分支付完成后，计算下级会员的权益消费积分
     * @param relationDO 会员关系
     * @param spentAmount 消费的积分
     * @param spentType  消费的项目枚举
     * @param orderNo  订单号
     */
    void calculateMemberRightSpendScore(MemberRelationDO relationDO, Integer spentAmount, Integer spentType, String orderNo);

    /**
     * 取消订单-返还-抵扣订单金额的积分
     * @param memberId 采购会员Id（买方）
     * @param roleId 采购会员角色Id
     * @param spentType MemberRightSpendTypeEnum
     * @param orderNo 订单号
     */
    void returnMemberRightPoints(Long memberId, Long roleId, Integer spentType, String orderNo, String remark);

}
