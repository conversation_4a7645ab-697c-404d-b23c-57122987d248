package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.member.api.model.resp.MemberManageQueryResp;
import com.ssy.lingxi.member.model.req.basic.MemberNameDataReq;
import com.ssy.lingxi.member.model.req.basic.ProvinceCodeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityMaintenanceMemberQueryDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordInspectPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordRectifyResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.MemberClassifyCategoryItemResp;
import com.ssy.lingxi.member.model.resp.validate.MemberClassifyQueryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 会员能力 - 会员维护相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
public interface IMemberAbilityMaintenanceService {
    /**
     * 获取分页查询会员列表页面中各个查询条件下拉选择框的内容
     * @param headers HttpHeaders信息
     * @param loginUser 登录用户
     * @param roleTag 角色标签
     * @return 操作结果
     */
    MemberMaintenanceSearchConditionResp getPageCondition(HttpHeaders headers, UserLoginCacheDTO loginUser, Integer roleTag);

    /**
     * 分页、模糊查询会员
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @param loginUser 登录用户
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberMaintenancePageQueryResp> pageMembers(HttpHeaders headers, MemberAbilityMaintenanceMemberQueryDataReq queryVO, UserLoginCacheDTO loginUser, Integer roleTag);


    /**
     * 会员处理页面 - 会员详情
     * @param loginUser 登录用户
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    MemberMaintenanceDetailResp getSubMemberDetail(UserLoginCacheDTO loginUser, ValidateIdReq idVO, Integer roleTag);

    /**
     * 会员处理 - 会员冻结、解冻
     * @param loginUser 登录用户
     * @param statusVO 接口参数
     * @return 操作结果
     */
    void updateMemberRelationFreezeStatus(UserLoginCacheDTO loginUser, MemberFreezeStatusReq statusVO);

    /**
     * 会员处理 - 会员淘汰（解除关系）
     * @param loginUser 登录用户信息
     * @param vo 接口参数
     * @return 操作结果
     */
    void updateMemberEliminatedStatus(UserLoginCacheDTO loginUser, MemberEliminateOrBlacklistReq vo);

    /**
     * 会员处理 - 会员黑名单
     * @param loginUser 登录用户信息
     * @param vo 接口参数
     * @return 操作结果
     */
    void updateMemberBlacklistStatus(UserLoginCacheDTO loginUser, MemberEliminateOrBlacklistReq vo);

    /**
     * 会员详情 - 会员基本信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberMaintenanceDetailBasicResp getMemberDetailBasic(HttpHeaders headers, ValidateIdReq idVO, Integer roleTag);

    /**
     * 会员详情 - 会员档案信息
     * @param headers HttpHeader信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberMaintenanceRecordResp getMemberRecords(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员档案 - 查询入库分类信息（修改页面）
     * @param headers HttpHeader信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberClassifyQueryResp getMemberClassification(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * “修改入库分类信息” - “适用区域”-省列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<AreaCodeNameResp> getClassifyProvinces(HttpHeaders headers);

    /**
     * “修改入库分类” - “适用区域”-根据省编码查询市列表
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 查询结果
     */
    List<AreaCodeNameResp> getClassifyCities(HttpHeaders headers, ProvinceCodeReq codeVO);

    /**
     * “修改入库分类” - 品类信息 - 查询结算方式与发票类型
     * @param headers Http头部信息
     * @return 查询结果
     */
    MemberClassifyCategoryItemResp getToClassifyCategoryItems(HttpHeaders headers);

    /**
     * 会员详情 - 会员档案 - 修改入库分类信息
     * @param headers HttpHeader信息
     * @param updateVO 接口参数
     * @return 查询结果
     */
    void updateMemberClassification(HttpHeaders headers, MemberMaintenanceClassificationUpdateReq updateVO);

    /**
     * 会员详情 - 会员档案 - 分页查询考察信息
     * @param headers HttpHeader信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberRecordInspectPageQueryResp> pageMemberInspect(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情- 会员档案 - 分页查询考评信息
     * @param headers HttpHeader信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberAppraisalPageQueryResp> pageMemberAppraisal(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员档案 - 分页查询会员整改
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberRecordRectifyResp> pageMemberRecordRectify(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员等级信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberValidateDetailLevelResp getMemberLevelDetail(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员权益信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailRightResp getMemberDetailRight(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益获取记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益使用记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailCreditResp getMemberDetailCredit(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberCreditComplaintPageQueryResp> pageMemberDetailCreditComplainHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员变更 - 分页查询会员变更记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDepositDetailHistoryResp> pageMemberDepositDetailHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 分页、模糊查询平台会员信息列表
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<PlatformPageQueryMemberResp> pagePlatformMembers(HttpHeaders headers, PlatformMemberQueryDataReq queryVO, Integer roleTag);

    /**
     * 根据会员id和角色id，查询会员角色为服务提供者的下级会员列表
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    PageDataResp<MemberManageQueryResp> subOrdinateMemberList(@RequestHeader HttpHeaders headers, MemberNameDataReq nameVO);

    /**
     * 查询当前会员可查询的会员注册资料信息
     * @return 查询结果
     */
    List<MemberConfigResp> registerDetailByAllowSelect(HttpHeaders headers, RoleIdReq roleIdReq);

    /**
     * 分页、模糊查询平台会员信息列表 - 会员发现
     * @param headers HttpHeaders信息
     * @param memberNameReq 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberDiscoverQueryResp> queryMembersByCategory(HttpHeaders headers, MemberNameDataReq memberNameReq, Integer roleTag);

    /**
     * 统计各个生命周期下级会员人数
     *
     * @param loginUser 当前登录用户
     * @param roleTag   角色标签
     * @return 返回列表
     */
    List<MemberLifeCycleStatisticDetailResp> statisticMemberLifeCycleCount(UserLoginCacheDTO loginUser, Integer roleTag);
}
