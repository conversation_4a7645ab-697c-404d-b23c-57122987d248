package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.member.api.model.req.DeleteRealNameConfigReq;
import com.ssy.lingxi.member.api.model.req.RealNameConfigReq;
import com.ssy.lingxi.member.entity.do_.RealNameConfigDO;
import com.ssy.lingxi.member.model.resp.RealNameConfigResp;

import java.util.List;

/**
 * 实名验证设置类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/12
 */
public interface IRealNameConfigService {

    /**
     * 查询服务器参数配置信息列表
     */
    List<RealNameConfigResp> getConfigList();

    /**
     * 查询生效的配置信息
     *
     * @return MemberAuthDO
     */
    RealNameConfigDO findEnableAuthConfig();

    /**
     * 添加或修改实名验证公共参数
     *
     * @param realNameConfigReq 实名验证公共参数
     * @return 操作结果
     */
    Boolean saveOrUpdateConfig(RealNameConfigReq realNameConfigReq);

    /**
     * 删除实名验证公共参数
     *
     * @param deleteRealNameConfigReq 请求参数
     * @return 操作结果
     */
    Boolean deleteConfig(DeleteRealNameConfigReq deleteRealNameConfigReq);

    /**
     * 清空实名验证内容
     *
     * @return 是否成功
     */
    Boolean clearConfig();
}
