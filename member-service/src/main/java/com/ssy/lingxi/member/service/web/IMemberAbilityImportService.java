package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.bo.MemberImportBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.model.req.basic.MemberTypeAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.basic.LevelAndTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameAndMemberTypeResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import org.springframework.http.HttpHeaders;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 会员能力 - 会员导入相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-18
 */
public interface IMemberAbilityImportService {

    /**
     * 获取分页查询会员列表页面中各个查询条件下拉选择框的内容
     * @param headers HttpHeader信息
     * @param loginUser 登录用户信息
     * @param roleTag 角色标签
     * @return 操作结果
     */
    MemberImportSearchConditionResp getPageCondition(HttpHeaders headers, UserLoginCacheDTO loginUser, Integer roleTag);

    /**
     * 分页、模糊查询会员
     * @param loginUser 登录用户信息
     * @param queryVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAbilityImportPageQueryResp> pageMembers(UserLoginCacheDTO loginUser, MemberAbilityImportMemberQueryDataReq queryVO, Integer roleTag);


    /**
     * 获取新增会员页面内容
     * @param headers HttpHeader信息
     * @param loginUser 登录用户信息
     * @return 操作结果
     */
    MemberAbilityAddMemberPageItemsResp getAddMemberPageItems(HttpHeaders headers, UserLoginCacheDTO loginUser);

    /**
     * 根据会员类型，查询角色列表
     * @param headers HttpHeader信息
     * @param loginUser 用户信息
     * @param memberTypeReq 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    List<RoleIdAndNameResp> getAddMemberPageRoles(HttpHeaders headers, UserLoginCacheDTO loginUser, MemberTypeReq memberTypeReq, Integer roleTag);


    /**
     * 根据角色标签，查询角色列表
     * @param headers HttpHeader信息
     * @param roleTag 角色标签
     * @return 操作结果
     */
    List<RoleIdAndNameAndMemberTypeResp> getAddMemberPageRolesByRoleTag(HttpHeaders headers, Integer roleTag);
    /**
     * 新增会员页面，根据会员类型和角色，查询等级列表
     * @param loginUser 登录用户信息
     * @param typeAndRoleIdVO 接口参数
     * @return 操作结果
     */
    List<LevelAndTagResp> getAddMemberPageLevels(UserLoginCacheDTO loginUser, MemberTypeAndRoleIdReq typeAndRoleIdVO);

    /**
     * 新增会员页面，根据选择的角色，返回会员注册资料信息
     * @param headers HttpHeader信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    List<MemberConfigGroupResp> getAddMemberPageMemberConfigItems(HttpHeaders headers, RoleIdReq idVO);

    /**
     * 新增会员
     * @param loginUser 登录用户信息
     * @param memberVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void addSubMember(UserLoginCacheDTO loginUser, MemberAbilityAddMemberReq memberVO, Integer roleTag);

    /**
     * 查询单个会员详细信息（用于修改页面）
     * @param loginUser 登录用户信息
     * @param validateVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    MemberAbilityImportMemberDetailResp getSubMember(UserLoginCacheDTO loginUser, MemberValidateReq validateVO, Integer roleTag);

    /**
     * 查询单个会员详细信息（用于查询页面）
     * @param loginUser 登录用户信息
     * @param validateVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    MemberAbilityImportMemberTextDetailResp getSubMemberDetail(UserLoginCacheDTO loginUser, MemberValidateReq validateVO, Integer roleTag);

    /**
     * 修改会员信息
     * @param loginUser 登录用户信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateSubMember(UserLoginCacheDTO loginUser, MemberAbilityUpdateMemberReq updateVO);

    /**
     * 删除会员
     * @param loginUser 登录用户信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    void deleteSubMember(UserLoginCacheDTO loginUser, MemberValidateReq validateVO);

    /**
     * 提交平台审核
     * @param loginUser 登录用户信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    void commitSubMemberToValidate(UserLoginCacheDTO loginUser, MemberValidateReq validateVO);

    /**
     * 引入单个/多个会员
     * @param headers HttpHeader信息
     * @param memberListVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void introduceSubMember(HttpHeaders headers, MemberAbilityAddMemberListReq memberListVO, Integer roleTag);

    /**
     * 会员可用角色查询(excel导出模版)
     * @param headers HttpHeaders信息
     * @param roleTag 角色标签
     * @return 操作结果
     */
    List<RoleIdAndNameResp> excelRoles(HttpHeaders headers, Integer roleTag);

    /**
     * 导出会员导入模版（导出excel模版）
     * @param headers HttpHeaders信息
     * @param roleIdReq 角色id
     * @param request 请求
     * @param response 响应
     * @return 操作结果
     */
    void exportExcelTemplate(HttpHeaders headers, RoleIdReq roleIdReq, HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入会员（excel导入）
     * @param headers HttpHeaders信息
     * @param file 会员导入文件
     * @return 操作结果
     */
    void importExcelMembers(HttpHeaders headers, MultipartFile file, HttpServletRequest request);

    void batchAddSubMember(MemberRoleDO newRoleDO, UserLoginCacheDTO loginUser, List<MemberImportBO> memberImportBOList);

    /**
     * 会员发现 - 邀请
     * @param headers HttpHeaders信息
     * @param discoverVO 请求参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void discoverInvitation(HttpHeaders headers, MemberDiscoverReq discoverVO, Integer roleTag);
}
