package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberRoleGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberRoleQueryResp;
import com.ssy.lingxi.member.model.resp.platform.MemberRoleResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员能力 - 用户角色服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-30
 */
public interface IMemberAbilityRoleService {

    /**
     * 分页、模糊查询用户角色
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberRoleQueryResp> pageMemberRole(HttpHeaders headers, MemberRolePageDataReq pageVO);

    /**
     * 查询用户角色列表
     * @param headers       HttpHeaders信息
     * @param memberRoleResp  接口参数
     * @return  操作结果
     */
    List<MemberRoleQueryResp> getMemberRoleList(HttpHeaders headers, MemberRoleResp memberRoleResp);

    /**
     * 根据名称，分页、模糊查询用户角色
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberRoleQueryResp> pageMemberRoleByName(HttpHeaders headers, MemberRolePageByNameDataReq pageVO);

    /**
     * 获取左侧权限菜单、组织机构、省市区域
     * @param headers HttpHeaders信息
     * @param roleIdReq    用户角色Id
     * @return 操作结果
     */
    AuthTreeResp memberAuthTree(HttpHeaders headers, UserRoleAuthTreeReq roleIdReq);

    /**
     * 新增用户角色
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void addMemberRole(HttpHeaders headers, MemberAbilityRoleAddReq addVO);

    /**
     * 查询单个用户角色的信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    MemberRoleGetResp getMemberRole(HttpHeaders headers, UserRoleIdReq idVO);

    /**
     * 更新用户角色及左侧菜单权限
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateMemberRole(HttpHeaders headers, MemberAbilityRoleUpdateReq updateVO);

    /**
     * 删除用户角色
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    void deleteMemberRole(HttpHeaders headers, UserRoleIdReq idVO);

    /**
     * 更新用户角色状态
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    void updateMemberRoleStatus(HttpHeaders headers, MemberRoleUpdateStatusReq statusVO);

    /**
     * 登录用户其他角色列表
     * @param headers HttpHeaders信息
     * @return 角色信息
     */
    List<MemberRoleResp> otherRoleList(HttpHeaders headers);
}
