package com.ssy.lingxi.member.controller.feign;

import com.alibaba.fastjson.JSONObject;
import com.ssy.lingxi.common.model.req.api.member.CustomerProcessFeeDiscountSyncReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberCustomerProcessFeeDiscountFeign;
import com.ssy.lingxi.member.api.model.req.CustomerCalReq;
import com.ssy.lingxi.member.api.model.resp.MobileCustomerFeeDiscountResp;
import com.ssy.lingxi.member.service.web.ICustomerProcessFeeDiscountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 客户工费内部调用
 *
 * <AUTHOR>
 * @version 2.02.18
 * @since 2025-05-27
 */
@Slf4j
@RestController
public class MemberCustomerProcessFeeDiscountController implements IMemberCustomerProcessFeeDiscountFeign {

    @Resource
    private ICustomerProcessFeeDiscountService customerProcessFeeDiscountService;


    @Override
    public WrapperResp<Boolean> sync(CustomerProcessFeeDiscountSyncReq customerProcessFeeDiscountSyncReq) {
        return WrapperUtil.success(customerProcessFeeDiscountService.sync(customerProcessFeeDiscountSyncReq));
    }

    @Override
    public WrapperResp<List<MobileCustomerFeeDiscountResp>> calculateDiscount(CustomerCalReq customerCalReq) {
        log.info("开始计算客户工费优惠，客户ID：{}，商品数量：{}",
                customerCalReq.getCustomerId(), customerCalReq.getSkuIdList().size());

        List<MobileCustomerFeeDiscountResp> result = customerProcessFeeDiscountService.calculateDiscount(customerCalReq);

        log.info("客户工费优惠计算完成，客户ID：{}，返回结果为：{}",
                customerCalReq.getCustomerId(), JSONObject.toJSONString(result));

        return WrapperUtil.success(result);
    }
}
