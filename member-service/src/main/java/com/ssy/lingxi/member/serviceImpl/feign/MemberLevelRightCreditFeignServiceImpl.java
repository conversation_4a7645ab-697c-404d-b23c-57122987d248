package com.ssy.lingxi.member.serviceImpl.feign;

import com.querydsl.core.types.ConstructorExpression;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.PasswordUtil;
import com.ssy.lingxi.member.api.enums.MemberRightTypeEnum;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.levelRight.*;
import com.ssy.lingxi.member.enums.MemberInnerStatusEnum;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.enums.PlatformInnerStatusEnum;
import com.ssy.lingxi.member.repository.MemberLevelConfigRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.MemberRoleRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCreditAsyncService;
import com.ssy.lingxi.member.service.base.IBaseMemberRightAsyncService;
import com.ssy.lingxi.member.service.feign.IMemberLevelRightCreditFeignService;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员等级（Level）、权益（Right）、信用（Credit）对外Feign服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-20
 */
@Service
public class MemberLevelRightCreditFeignServiceImpl implements IMemberLevelRightCreditFeignService {
    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberRightAsyncService baseMemberRightAsyncService;

    @Resource
    private IBaseMemberCreditAsyncService baseMemberCreditAsyncService;

    @Resource
    private MemberLevelConfigRepository memberLevelConfigRepository;

    @Resource
    private MemberRoleRepository roleRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;


    /**
     * 查询平台会员的等级、权益、信用积分（多个）
     * @param memberIdList 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    @Override
    public List<MemberFeignLrcResp> getPlatformMemberLrcBatch(List<Long> memberIdList) {
        List<MemberRelationDO> relationDOList = relationRepository.findAllBySubMemberIdInAndRelType(memberIdList, MemberRelationTypeEnum.PLATFORM.getCode());
        if(CollectionUtils.isEmpty(relationDOList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        return relationDOList.stream().map(a -> {
            MemberFeignLrcResp lrcVO = new MemberFeignLrcResp();
            lrcVO.setLevel(0);
            lrcVO.setLevelTag("");
            lrcVO.setScore(0);
            lrcVO.setSumReturnMoney(new BigDecimal(0));
            lrcVO.setSumPoint(0);
            lrcVO.setCurrentPoint(0);
            lrcVO.setSumUsedPoint(0);
            lrcVO.setCreditPoint(0);
            lrcVO.setTradeCommentPoint(0);
            lrcVO.setAfterSaleCommentPoint(0);
            lrcVO.setComplainPoint(0);
            lrcVO.setRegisterYearsPoint(0);
            lrcVO.setRegisterYears(DateTimeUtil.diffYears(a.getCreateTime(), LocalDateTime.now()));
            lrcVO.setMemberId(a.getSubMemberId());
            lrcVO.setRoleId(a.getSubRoleId());

            MemberLevelRightDO levelRightDO = a.getLevelRight();
            if (levelRightDO != null) {
                lrcVO.setLevel(levelRightDO.getLevel());
                lrcVO.setLevelTag(levelRightDO.getLevelTag());
                lrcVO.setScore(levelRightDO.getScore());
                lrcVO.setSumReturnMoney(levelRightDO.getSumReturnMoney());
                lrcVO.setSumPoint(levelRightDO.getSumPoint());
                lrcVO.setCurrentPoint(levelRightDO.getCurrentPoint());
                lrcVO.setSumUsedPoint(levelRightDO.getSumUsedPoint());
            }

            MemberCreditDO creditDO = a.getCredit();
            if (creditDO != null) {
                lrcVO.setCreditPoint(creditDO.getCreditPoint());
                lrcVO.setTradeCommentPoint(creditDO.getTradeCommentPoint());
                lrcVO.setAfterSaleCommentPoint(creditDO.getAfterSaleCommentPoint());
                lrcVO.setComplainPoint(creditDO.getComplainPoint());
                lrcVO.setRegisterYearsPoint(creditDO.getRegisterYearsPoint());
            }

            return lrcVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询下级会员的价格权益
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public MemberFeignRightResp getMemberPriceRight(MemberFeignRelationReq relationVO) {
        return getSubMemberRight(MemberRightTypeEnum.PRICE_RIGHT, relationVO, BigDecimal.valueOf(1));
    }

    /**
     * 查询平台会员id
     * @return 查询结果
     */
    public MemberRelationDO getPlatformMemberRelationDO(){
        return relationRepository.findFirstByRelType(MemberRelationTypeEnum.PLATFORM.getCode());
    }

    public List<MemberFeignRightByOrderResp> batchMemberPriceRightForOrder(MemberFeignBatchReq batchVO) {

        List<MemberFeignRightByOrderResp> collect=null;
        //查询平台会员id与角色id
        MemberRelationDO platformMemberRelation = getPlatformMemberRelationDO();
        MemberFeignUpperMemberReq ptMember = new MemberFeignUpperMemberReq();
        ptMember.setUpperMemberId(platformMemberRelation.getMemberId());
        ptMember.setUpperRoleId(platformMemberRelation.getRoleId());
        batchVO.getUpperMembers().add(ptMember);


        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), batchVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), batchVO.getRoleId()));

            List<Predicate> orList = new ArrayList<>();
            batchVO.getUpperMembers().forEach(upperMember -> orList.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("memberId").as(Long.class), upperMember.getUpperMemberId()), criteriaBuilder.equal(root.get("roleId").as(Long.class), upperMember.getUpperRoleId()))));
            Predicate[] orPredicates = new Predicate[orList.size()];
            list.add(criteriaBuilder.or(orList.toArray(orPredicates)));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        List<MemberRelationDO> relations = relationRepository.findAll(specification);

        if(!CollectionUtils.isEmpty(relations)){
            collect = relations.stream().map(o -> {
                MemberFeignRightByOrderResp resp = new MemberFeignRightByOrderResp();
                resp.setUpperMemberId(o.getMemberId());
                resp.setUpperRoleId(o.getRoleId());
                resp.setRelType(o.getRelType());
                resp.setCurrentPoint(o.getLevelRight().getCurrentPoint());
                resp.setUpperLogo(o.getMember().getLogo());
                resp.setUpperName(o.getMember().getName());
                return resp;
            }).collect(Collectors.toList());
        }
        return collect;
    }

    /**
     * 批量查询下级会员的价格权益
     *
     * @param batchVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public List<MemberFeignRightDetailResp> batchMemberPriceRight(MemberFeignBatchReq batchVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), batchVO.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), batchVO.getRoleId()));

            List<Predicate> orList = new ArrayList<>();
            batchVO.getUpperMembers().forEach(upperMember -> orList.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("memberId").as(Long.class), upperMember.getUpperMemberId()), criteriaBuilder.equal(root.get("roleId").as(Long.class), upperMember.getUpperRoleId()))));
            Predicate[] orPredicates = new Predicate[orList.size()];
            list.add(criteriaBuilder.or(orList.toArray(orPredicates)));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> relations = relationRepository.findAll(specification);

        return batchVO.getUpperMembers().stream().map(upperMember -> {
            MemberFeignRightDetailResp detailVO = new MemberFeignRightDetailResp();
            detailVO.setUpperMemberId(upperMember.getUpperMemberId());
            detailVO.setUpperRoleId(upperMember.getUpperRoleId());
            detailVO.setRightTypeEnum(MemberRightTypeEnum.PRICE_RIGHT.getCode());
            detailVO.setParameter(BigDecimal.ONE);

            relations.stream().filter(r -> r.getMemberId().equals(upperMember.getUpperMemberId()) && r.getRoleId().equals(upperMember.getUpperRoleId()) && r.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode()) && r.getStatus().equals(MemberStatusEnum.NORMAL.getCode())).findFirst().ifPresent(relation -> {
                detailVO.setSubMemberLevelType(relation.getSubMemberLevelTypeEnum());
                if(relation.getLevelRight() != null) {
                    if(relation.getLevelRight().getLevelConfig() != null) {
                        if(!CollectionUtils.isEmpty(relation.getLevelRight().getLevelConfig().getRights())) {
                            relation.getLevelRight().getLevelConfig().getRights().stream().filter(memberRightConfigDO -> memberRightConfigDO.getStatus().equals(EnableDisableStatusEnum.ENABLE.getCode()) && memberRightConfigDO.getRightType().equals(MemberRightTypeEnum.PRICE_RIGHT.getCode())).findFirst().ifPresent(memberRightConfigDO -> detailVO.setParameter(memberRightConfigDO.getParameter()));
                        }
                    }
                }
            });
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * 批量查询下级会员的价格权益
     *
     * @param feignRelations 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    @Override
    public List<MemberFeignRelationRightDetailResp> batchMemberPriceRight(List<MemberFeignRelationReq> feignRelations) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = feignRelations.stream().map(batchVO ->
                criteriaBuilder.and(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), batchVO.getSubMemberId()),
                criteriaBuilder.equal(root.get("subRoleId").as(Long.class), batchVO.getSubRoleId()),
                criteriaBuilder.equal(root.get("memberId").as(Long.class), batchVO.getUpperMemberId()),
                criteriaBuilder.equal(root.get("roleId").as(Long.class), batchVO.getUpperRoleId()))
            ).collect(Collectors.toList());

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.or(list.toArray(p));
        };

        List<MemberRelationDO> relationList = relationRepository.findAll(specification);
        return feignRelations.stream().map(feignRelation -> {
            MemberFeignRelationRightDetailResp detailVO = new MemberFeignRelationRightDetailResp();
            detailVO.setUpperMemberId(feignRelation.getUpperMemberId());
            detailVO.setUpperRoleId(feignRelation.getUpperRoleId());
            detailVO.setSubMemberId(feignRelation.getSubMemberId());
            detailVO.setSubRoleId(feignRelation.getSubRoleId());
            detailVO.setRightTypeEnum(MemberRightTypeEnum.PRICE_RIGHT.getCode());
            detailVO.setParameter(BigDecimal.ONE);
            MemberRelationDO relationDO = relationList.stream().filter(r -> r.getMemberId().equals(feignRelation.getUpperMemberId()) && r.getRoleId().equals(feignRelation.getUpperRoleId()) && r.getSubMemberId().equals(feignRelation.getSubMemberId()) && r.getSubRoleId().equals(feignRelation.getSubRoleId())).findFirst().orElse(null);
            if(relationDO != null && relationDO.getLevelRight() != null) {
                if(relationDO.getLevelRight().getLevelConfig() != null) {
                    if(!CollectionUtils.isEmpty(relationDO.getLevelRight().getLevelConfig().getRights())) {
                        relationDO.getLevelRight().getLevelConfig().getRights().stream().filter(memberRightConfigDO -> memberRightConfigDO.getStatus().equals(EnableDisableStatusEnum.ENABLE.getCode()) && memberRightConfigDO.getRightType().equals(MemberRightTypeEnum.PRICE_RIGHT.getCode())).findFirst().ifPresent(memberRightConfigDO -> detailVO.setParameter(memberRightConfigDO.getParameter()));
                    }
                }
            }
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询下级会员的返现权益设置
     *
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的返现权益
     */
    @Override
    public MemberFeignRightResp getMemberReturnRight(MemberFeignRelationReq relationVO) {
        return getSubMemberRight(MemberRightTypeEnum.RETURN_MONEY_RIGHT, relationVO, BigDecimal.ZERO);
    }

    /**
     * 查询下级会员在平台后台、上级会员下的返现权益设置
     *
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的返现权益
     */
    @Override
    public List<MemberFeignRightDetailResp> findMemberReturnRight(MemberFeignRelationReq relationVO) {
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("verified").as(Integer.class), MemberValidateStatusEnum.VERIFY_PASSED.getCode()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), relationVO.getSubMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), relationVO.getSubRoleId()));

            List<Predicate> orList = new ArrayList<>();
            orList.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("memberId").as(Long.class), relationVO.getUpperMemberId()), criteriaBuilder.equal(root.get("roleId").as(Long.class), relationVO.getUpperRoleId())));
            orList.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            Predicate[] orP = new Predicate[orList.size()];
            list.add(criteriaBuilder.or(orList.toArray(orP)));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<MemberRelationDO> relationList = relationRepository.findAll(specification);

        return relationList.stream().map(relationDO -> {
            MemberFeignRightDetailResp detailVO = new MemberFeignRightDetailResp();
            detailVO.setUpperMemberId(relationDO.getMemberId());
            detailVO.setUpperRoleId(relationDO.getRoleId());
            detailVO.setSubMemberLevelType(relationDO.getSubMemberLevelTypeEnum());
            detailVO.setRightTypeEnum(MemberRightTypeEnum.RETURN_MONEY_RIGHT.getCode());
            detailVO.setParameter(BigDecimal.ZERO);

            if(relationDO.getLevelRight() == null || relationDO.getLevelRight().getLevelConfig() == null || CollectionUtils.isEmpty(relationDO.getLevelRight().getLevelConfig().getRights())) {
                return detailVO;
            }

            MemberRightConfigDO rightConfigDO = relationDO.getLevelRight().getLevelConfig().getRights().stream().filter(right -> right.getRightType().equals(MemberRightTypeEnum.RETURN_MONEY_RIGHT.getCode())).findFirst().orElse(null);
            if(rightConfigDO == null || rightConfigDO.getParameter().compareTo(BigDecimal.ZERO) == 0) {
                return detailVO;
            }

            detailVO.setParameter(rightConfigDO.getParameter());
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询下级会员的等级、权益、信用积分
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的等级、权益、信用积分
     */
    @Override
    public MemberFeignLrcResp getMemberLrc(MemberFeignRelationReq relationVO) {
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationVO.getUpperMemberId(), relationVO.getUpperRoleId(), relationVO.getSubMemberId(), relationVO.getSubMemberId());
        if(relationDO == null || !relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode()) || !relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode())) {
            return null;
        }

        MemberFeignLrcResp lrcVO = new MemberFeignLrcResp();
        lrcVO.setLevel(0);
        lrcVO.setLevelTag("");
        lrcVO.setScore(0);
        lrcVO.setSumReturnMoney(new BigDecimal(0));
        lrcVO.setSumPoint(0);
        lrcVO.setCurrentPoint(0);
        lrcVO.setSumUsedPoint(0);
        lrcVO.setCreditPoint(0);
        lrcVO.setTradeCommentPoint(0);
        lrcVO.setAfterSaleCommentPoint(0);
        lrcVO.setComplainPoint(0);
        lrcVO.setRegisterYearsPoint(0);
        lrcVO.setRegisterYears(DateTimeUtil.diffYears(relationDO.getCreateTime(), LocalDateTime.now()));

        MemberLevelRightDO levelRightDO = relationDO.getLevelRight();
        if(levelRightDO != null) {
            lrcVO.setLevel(levelRightDO.getLevel());
            lrcVO.setLevelTag(levelRightDO.getLevelTag());
            lrcVO.setScore(levelRightDO.getScore());
            lrcVO.setSumReturnMoney(levelRightDO.getSumReturnMoney());
            lrcVO.setSumPoint(levelRightDO.getSumPoint());
            lrcVO.setCurrentPoint(levelRightDO.getCurrentPoint());
            lrcVO.setSumUsedPoint(levelRightDO.getSumUsedPoint());
        }

        MemberCreditDO creditDO = relationDO.getCredit();
        if(creditDO != null) {
            lrcVO.setCreditPoint(creditDO.getCreditPoint());
            lrcVO.setTradeCommentPoint(creditDO.getTradeCommentPoint());
            lrcVO.setAfterSaleCommentPoint(creditDO.getAfterSaleCommentPoint());
            lrcVO.setComplainPoint(creditDO.getComplainPoint());
            lrcVO.setRegisterYearsPoint(creditDO.getRegisterYearsPoint());
        }

        return lrcVO;
    }

    /**
     * 查询平台会员的等级、权益、信用积分
     * @param feignVO 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    @Override
    public MemberFeignLrcResp getPlatformMemberLrc(MemberFeignReq feignVO) {
        MemberRelationDO relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(feignVO.getMemberId(), feignVO.getRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if(relationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberFeignLrcResp lrcVO = new MemberFeignLrcResp();
        lrcVO.setLevel(0);
        lrcVO.setLevelTag("");
        lrcVO.setScore(0);
        lrcVO.setSumReturnMoney(new BigDecimal(0));
        lrcVO.setSumPoint(0);
        lrcVO.setCurrentPoint(0);
        lrcVO.setSumUsedPoint(0);
        lrcVO.setCreditPoint(0);
        lrcVO.setTradeCommentPoint(0);
        lrcVO.setAfterSaleCommentPoint(0);
        lrcVO.setComplainPoint(0);
        lrcVO.setRegisterYearsPoint(0);
        lrcVO.setRegisterYears(DateTimeUtil.diffYears(relationDO.getCreateTime(), LocalDateTime.now()));

        MemberLevelRightDO levelRightDO = relationDO.getLevelRight();
        if(levelRightDO != null) {
            lrcVO.setLevel(levelRightDO.getLevel());
            lrcVO.setLevelTag(levelRightDO.getLevelTag());
            lrcVO.setScore(levelRightDO.getScore());
            lrcVO.setSumReturnMoney(levelRightDO.getSumReturnMoney());
            lrcVO.setSumPoint(levelRightDO.getSumPoint());
            lrcVO.setCurrentPoint(levelRightDO.getCurrentPoint());
            lrcVO.setSumUsedPoint(levelRightDO.getSumUsedPoint());
        }

        MemberCreditDO creditDO = relationDO.getCredit();
        if(creditDO != null) {
            lrcVO.setCreditPoint(creditDO.getCreditPoint());
            lrcVO.setTradeCommentPoint(creditDO.getTradeCommentPoint());
            lrcVO.setAfterSaleCommentPoint(creditDO.getAfterSaleCommentPoint());
            lrcVO.setComplainPoint(creditDO.getComplainPoint());
            lrcVO.setRegisterYearsPoint(creditDO.getRegisterYearsPoint());
        }

        return lrcVO;
    }

    /**
     * 获取下级会员的权益信息
     * @param rightTypeEnum 会员权益枚举，定义在MemberRightTypeEnum中
     * @param relationVO 来自OpenFeign对外接口的参数
     * @param defaultParameter 如果权益为null或参数为0（未设置），返回的默认参数值
     * @return 查询结果
     */
    private MemberFeignRightResp getSubMemberRight(MemberRightTypeEnum rightTypeEnum, MemberFeignRelationReq relationVO, BigDecimal defaultParameter) {
        MemberFeignRightResp rightVO = new MemberFeignRightResp();
        rightVO.setName(rightTypeEnum.getMessage());
        rightVO.setRightTypeEnum(rightTypeEnum.getCode());
        rightVO.setParameter(defaultParameter);
        rightVO.setStatus(EnableDisableStatusEnum.DISABLE.getCode());

        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationVO.getUpperMemberId(), relationVO.getUpperRoleId(), relationVO.getSubMemberId(), relationVO.getSubRoleId());
        if(relationDO == null || !relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode()) || !relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            return rightVO;
        }

        MemberLevelRightDO levelRight = relationDO.getLevelRight();
        if(levelRight == null) {
            return rightVO;
        }

        MemberLevelConfigDO levelConfig = levelRight.getLevelConfig();
        if(levelConfig == null) {
            return rightVO;
        }

        if(CollectionUtils.isEmpty(levelConfig.getRights())) {
            return rightVO;
        }

        MemberRightConfigDO rightConfigDO = levelConfig.getRights().stream().filter(right -> right.getRightType().equals(rightTypeEnum.getCode())).findFirst().orElse(null);
        if(rightConfigDO == null || rightConfigDO.getParameter().compareTo(BigDecimal.ZERO) == 0) {
            return rightVO;
        }

        rightVO.setStatus(rightConfigDO.getStatus());
        rightVO.setParameter(rightConfigDO.getParameter());

        return rightVO;
    }

    /**
     * 订单完成后，计算等级积分、积分权益
     * @param orderVO 接口参数
     * @return 计算结果
     */
    @Override
    public MemberFeignReturnRightResp calculateMemberLrcByOrder(MemberFeignOrderReq orderVO) {
        BigDecimal plusPoint = BigDecimal.ZERO;
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(orderVO.getUpperMemberId(), orderVO.getUpperRoleId(), orderVO.getSubMemberId(), orderVO.getSubRoleId());
        if(relationDO != null && relationDO.getLevelRight() != null && relationDO.getLevelRight().getLevelConfig() != null && !CollectionUtils.isEmpty(relationDO.getLevelRight().getLevelConfig().getRights())) {
            MemberRightConfigDO memberRightConfigDO = relationDO.getLevelRight().getLevelConfig().getRights().stream().filter(right -> MemberRightTypeEnum.RETURN_MONEY_RIGHT.getCode().equals(right.getRightType())).findFirst().orElse(null);
            if(memberRightConfigDO != null && memberRightConfigDO.getStatus().equals(EnableDisableStatusEnum.ENABLE.getCode()) && memberRightConfigDO.getParameter().compareTo(new BigDecimal(0)) > 0) {
                plusPoint = BigDecimal.valueOf(Math.round(memberRightConfigDO.getParameter().multiply(orderVO.getAmount()).doubleValue()));
            }
        }

        //由于如果升级后，关联的权益会发生变化，所以要先计算权益，再计算等级
        baseMemberRightAsyncService.calculateMemberTradeRightThenLevelScore(orderVO.getUpperMemberId(), orderVO.getUpperRoleId(), orderVO.getSubMemberId(), orderVO.getSubRoleId(), orderVO.getAmount(), orderVO.getOrderNo());

//        //异步计算等级积分
//        baseMemberLevelAsyncService.calculateSubMemberTradeScore(orderVO.getUpperMemberId(), orderVO.getUpperRoleId(), orderVO.getSubMemberId(), orderVO.getSubRoleId(), orderVO.getAmount(), orderVO.getOrderNo());
//
//        //异步计算交易权益积分
//        baseMemberRightAsyncService.calculateMemberTradeRight(orderVO.getUpperMemberId(), orderVO.getUpperRoleId(), orderVO.getSubMemberId(), orderVO.getSubRoleId(), orderVO.getAmount(), orderVO.getOrderNo());

        return new MemberFeignReturnRightResp(plusPoint);
    }


    /**
     * 积分支付订单，校验可用信用积分、支付密码，再异步计算下级会员的权益积分
     * @param spendVO 接口参数
     * @return 操作结果
     */
    @Override
    public void calculateMemberUsedRightPoint(MemberFeignRightSpendReq spendVO) {
        MemberRelationDO relationDO;
        if(spendVO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
            relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(spendVO.getSubMemberId(), spendVO.getSubRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        } else {
            relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(spendVO.getUpperMemberId(), spendVO.getUpperRoleId(), spendVO.getSubMemberId(), spendVO.getSubRoleId());
        }

        if(relationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //校验支付密码
        MemberDO memberDO = relationDO.getSubMember();
        if(memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        if(!StringUtils.hasLength(memberDO.getPayPassword())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PAY_PASSWORD_NOT_SET);
        }

        if(!PasswordUtil.checkPassword(memberDO.getPayPassword(), spendVO.getPayPassword())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PAY_PASSWORD_INCORRECT);
        }

        //检查可用信用积分
        MemberLevelRightDO levelRight = relationDO.getLevelRight();
        if(levelRight == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
        }

        if(levelRight.getCurrentPoint().compareTo(spendVO.getUsedPoint()) < 0) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RIGHT_POINT_IS_NOT_ENOUGH);
        }

        baseMemberRightAsyncService.calculateMemberRightSpendScore(relationDO, spendVO.getUsedPoint(), spendVO.getSpentType(), spendVO.getOrderNo());

    }

    /**
     * 售后评论成功后，计算会员信用积分
     * @param commentVO 接口参数
     */
    @Override
    public void calculateMemberAfterSaleCreditPoint(MemberFeignAfterSaleCommentReq commentVO) {
        //异步计算积分，历史记录
        baseMemberCreditAsyncService.calculateMemberAfterSaleCommentCredit(commentVO.getCommentMemberId(), commentVO.getCommentRoleId(), commentVO.getReceivedMemberId(), commentVO.getReceivedRoleId(), commentVO.getAfterSaleTime(),commentVO.getStar(),commentVO.getProduct(),commentVO.getComment(),commentVO.getOrderNo());
    }

    /**
     * 根据memberLevelConfigId查询会员等级配置信息
     * @param memberLevelConfigId 接口参数
     * @return 返回结果
     */
    @Override
    public List<MemberFeignLevelConfigResp> getMemberLevelConfigBatch(List<Long> memberLevelConfigId) {
        if (CollectionUtils.isEmpty(memberLevelConfigId)) {
            return Collections.emptyList();
        }

        List<MemberLevelConfigDO> memberLevelConfigDOList = memberLevelConfigRepository.findAllById(memberLevelConfigId);
        List<MemberRoleDO> roles = roleRepository.findAllById(memberLevelConfigDOList.stream().map(MemberLevelConfigDO::getSubRoleId).collect(Collectors.toList()));

        return memberLevelConfigDOList.stream().map(levelConfig -> {
            MemberFeignLevelConfigResp levelConfigVO = new MemberFeignLevelConfigResp();
            levelConfigVO.setId(levelConfig.getId());
            levelConfigVO.setLevel(levelConfig.getLevel());
            levelConfigVO.setLevelTag(levelConfig.getLevelTag());
            levelConfigVO.setMemberLevelType(levelConfig.getLevelType());
            levelConfigVO.setMemberLevelTypeName(MemberLevelTypeEnum.getCodeMsg(levelConfig.getLevelType()));
            levelConfigVO.setRoleId(levelConfig.getSubRoleId());

            roles.stream().filter(role -> role.getId().equals(levelConfig.getSubRoleId())).findFirst().ifPresent(role -> {
                levelConfigVO.setRoleName(role.getRoleName());
                levelConfigVO.setRoleType(role.getRoleType());
                levelConfigVO.setRoleTypeName(RoleTypeEnum.getName(role.getRoleType()));
                levelConfigVO.setMemberType(role.getMemberType());
                levelConfigVO.setMemberTypeName(MemberTypeEnum.getName(role.getMemberType()));

            });
            return levelConfigVO;
        }).collect(Collectors.toList());

    }

    /**
     * 积分抵扣订单金额，校验可用信用积分、支付密码，扣除积分
     * @param deductionVO 接口参数
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void calculateMemberDeductionRightPoint(MemberFeignRightDeductionReq deductionVO) {
        List<MemberRelationDO> relationDO=new ArrayList<>();
        Map<String, MemberFeignRightDeductionItemReq> reqMap = deductionVO.getItemList().stream().collect(Collectors.toMap(k -> k.getUpperMemberId() + ":" + k.getUpperRoleId(), v -> v, (v1, v2) -> v2));
        if(deductionVO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
            relationDO.add(relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(deductionVO.getSubMemberId(), deductionVO.getSubRoleId(), MemberRelationTypeEnum.PLATFORM.getCode()));
        } else {
            List<MemberRelationDO> bySubMemberIdAndRelType = relationRepository.findBySubMemberIdAndSubRoleIdAndRelType(deductionVO.getSubMemberId(),deductionVO.getSubRoleId(), MemberRelationTypeEnum.OTHER.getCode());
            if(CollectionUtils.isEmpty(bySubMemberIdAndRelType)) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            }
            relationDO=bySubMemberIdAndRelType.stream().filter(k ->reqMap.get(k.getMemberId() + ":" + k.getRoleId())!=null).collect(Collectors.toList());
        }

        if(CollectionUtils.isEmpty(relationDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //校验支付密码
        MemberDO memberDO = relationDO.get(0).getSubMember();
        if(memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        //检查可用信用积分
        for(MemberRelationDO relation:relationDO){
            MemberLevelRightDO levelRight = relation.getLevelRight();
            if(levelRight == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
            }

            MemberFeignRightDeductionItemReq deductionItemVO = reqMap.get(relation.getMemberId() + ":" + relation.getRoleId());
            if(deductionItemVO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
            }

            if(levelRight.getCurrentPoint().compareTo(deductionItemVO.getUsedPoint()) < 0) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RIGHT_POINT_IS_NOT_ENOUGH);
            }

            baseMemberRightAsyncService.calculateMemberRightSpendScore(relation, deductionItemVO.getUsedPoint(), MemberRightSpendTypeEnum.DEDUCTION_AMOUNT.getTypeEnum(), deductionItemVO.getOrderNo());
        }
    }


    /**
     * 返还-抵扣订单金额的积分
     * @param returnVO 接口参数
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void returnMemberRightPoint(MemberFeignRightReturnReq returnVO) {
        baseMemberRightAsyncService.returnMemberRightPoints(returnVO.getSubMemberId(),returnVO.getSubRoleId(),MemberRightSpendTypeEnum.DEDUCTION_AMOUNT.getTypeEnum(),returnVO.getOrderNo(),"取消订单"+returnVO.getOrderNo());
    }

    /**
     * 根据上级会员Id和上级会员角色Id，以及当前用户，查询价格权益参数设置
     *
     * @param upperVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberFeignManageMemberCreditParameterResp getUpperMemberCreditParameter(MemberFeignManageUpperMemberAndRoleReq upperVO) {
        MemberFeignManageMemberCreditParameterResp parameterVO = new MemberFeignManageMemberCreditParameterResp();
        parameterVO.setParameter(new BigDecimal(1));
        //查询上下级关系
        MemberRelationDO relationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperVO.getParentMemberId(), upperVO.getParentMemberRoleId(), upperVO.getSubMemberId(), upperVO.getSubMemberRoleId());
        if (relationDO == null || !relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode()) || !relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            return parameterVO;
        }

        MemberLevelRightDO levelRight = relationDO.getLevelRight();
        if (levelRight == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
        }

        MemberLevelConfigDO levelConfig = levelRight.getLevelConfig();
        if (levelConfig == null) {
            return parameterVO;
        }

        if (CollectionUtils.isEmpty(levelConfig.getRights())) {
            return parameterVO;
        }

        MemberRightConfigDO configDO = levelConfig.getRights().stream().filter(config -> MemberRightTypeEnum.PRICE_RIGHT.getCode().equals(config.getRightType())).findFirst().orElse(null);
        if (configDO == null || configDO.getStatus().equals(EnableDisableStatusEnum.DISABLE.getCode()) || configDO.getParameter().compareTo(BigDecimal.ZERO) == 0) {
            return parameterVO;
        }

        parameterVO.setParameter(configDO.getParameter());

        return parameterVO;
    }

    /**
     * 查询下级会员在上级会员下的等级
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Transactional
    @Override
    public List<MemberFeignLevelDetailResp> findSubMemberLevels(MemberFeignReq feignVO) {
        QMemberRelationDO qMemberRelation = QMemberRelationDO.memberRelationDO;
        QMemberLevelRightDO qMemberLevelRight = QMemberLevelRightDO.memberLevelRightDO;
        QMemberLevelConfigDO qMemberLevelConfig = QMemberLevelConfigDO.memberLevelConfigDO;

        ConstructorExpression<MemberFeignLevelDetailResp> projection = Projections.constructor(MemberFeignLevelDetailResp.class,
                qMemberRelation.memberId, qMemberRelation.roleId, qMemberRelation.relType, qMemberLevelConfig.id, qMemberLevelRight.level, qMemberLevelRight.levelTag
                );

        return jpaQueryFactory.select(projection)
                .from(qMemberRelation)
                .leftJoin(qMemberLevelRight).on(qMemberRelation.id.eq(qMemberLevelRight.relation.id))
                .leftJoin(qMemberLevelConfig).on(qMemberLevelRight.levelConfig.id.eq(qMemberLevelConfig.id))
                .where(qMemberRelation.subMemberId.eq(feignVO.getMemberId()).and(qMemberRelation.subRoleId.eq(feignVO.getRoleId())))
                .where(qMemberRelation.status.eq(MemberStatusEnum.NORMAL.getCode()))
                .where(qMemberRelation.verified.eq(MemberValidateStatusEnum.VERIFY_PASSED.getCode()))
                .fetch();
    }

    /**
     * 查询会员等级配置
     *
     * @param req 接口参数
     * @return 等级配置列表
     */
    @Override
    public List<MemberFeignLevelResp> findMemberLevelConfigs(MemberFeignLevelReq req) {
        QMemberLevelConfigDO qMemberLevelConfig = QMemberLevelConfigDO.memberLevelConfigDO;
        QMemberRoleDO qMemberRole = QMemberRoleDO.memberRoleDO;

        ConstructorExpression<MemberFeignLevelResp> projection = Projections.constructor(MemberFeignLevelResp.class,
                qMemberLevelConfig.id, qMemberRole.roleName, qMemberLevelConfig.level, qMemberLevelConfig.levelTag);

        List<MemberFeignLevelResp> result;

        if (req.getPlatform()) {
            //查询平台等级配置
            QMemberRoleDO qPlatformRole = QMemberRoleDO.memberRoleDO;
            result = jpaQueryFactory.select(projection)
                    .from(qMemberLevelConfig)
                    .leftJoin(qPlatformRole).on(qMemberLevelConfig.roleId.eq(qPlatformRole.id))
                    .leftJoin(qMemberRole).on(qMemberLevelConfig.subRoleId.eq(qMemberRole.id))
                    .where(qPlatformRole.relType.eq(MemberRelationTypeEnum.PLATFORM.getCode()))
                    .fetch();
        } else {
            //查询会员等级配置
            result = jpaQueryFactory.select(projection)
                    .from(qMemberLevelConfig)
                    .leftJoin(qMemberRole).on(qMemberLevelConfig.subRoleId.eq(qMemberRole.id))
                    .where(qMemberLevelConfig.memberId.eq(req.getMemberId()).and(qMemberLevelConfig.roleId.eq(req.getRoleId())))
                    .fetch();
        }

        return result;
    }

    /**
     * V3 - 订单、营销服务查询供应商信息
     *
     * @param req 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberFeignCalcResp> findVendors(MemberFeignCalcReq req) {
        QMemberDO qMember = QMemberDO.memberDO;
        QMemberRelationDO qMemberRelation = QMemberRelationDO.memberRelationDO;
        QMemberLevelRightDO qMemberLevelRight = QMemberLevelRightDO.memberLevelRightDO;
        QMemberLevelConfigDO qMemberLevelConfig = QMemberLevelConfigDO.memberLevelConfigDO;
        QMemberRightConfigDO qMemberRightConfig = QMemberRightConfigDO.memberRightConfigDO;

        JPAQuery<MemberRelationDO> query = jpaQueryFactory.selectDistinct(qMemberRelation)
                .from(qMemberRelation)
                //使用fetchJoin()，强制加载关联实体
                .leftJoin(qMemberRelation.member, qMember).fetchJoin()
                .leftJoin(qMemberRelation.levelRight, qMemberLevelRight).fetchJoin()
                .leftJoin(qMemberLevelRight.levelConfig, qMemberLevelConfig).fetchJoin()
                .leftJoin(qMemberLevelConfig.rights, qMemberRightConfig).fetchJoin();

        //固定下级会员为采购商
        query.where(qMemberRelation.subMemberId.eq(req.getBuyerMemberId()).and(qMemberRelation.subRoleId.eq(req.getBuyerRoleId())));
        //查询平台会员，或指定上级会员为供应商
        query.where(ExpressionUtils.anyOf(
                //查询平台会员
                qMemberRelation.relType.eq(MemberRelationTypeEnum.PLATFORM.getCode()),
                ExpressionUtils.anyOf(
                        req.getVendors().stream().map(vendor -> qMemberRelation.memberId.eq(vendor.getMemberId()).and(qMemberRelation.roleId.eq(vendor.getRoleId()))).toArray(com.querydsl.core.types.Predicate[]::new)
                )
        ));

        List<MemberRelationDO> contents = query.fetch();
        if (CollectionUtils.isEmpty(contents)) {
            return new ArrayList<>();
        }

        return contents.stream().filter(rel -> {
            //是否审核通过，并且状态正常
            if (rel.getRelType().equals(MemberRelationTypeEnum.OTHER.getCode())) {
                return rel.getStatus().equals(MemberStatusEnum.NORMAL.getCode()) && (rel.getInnerStatus().equals(MemberInnerStatusEnum.VERIFY_PASSED.getCode()) || rel.getInnerStatus().equals(MemberInnerStatusEnum.MODIFY_PASSED.getCode()));
            } else {
                return rel.getStatus().equals(MemberStatusEnum.NORMAL.getCode()) && rel.getInnerStatus().equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode());
            }
        }).map(relation -> {
            MemberFeignCalcResp resp = new MemberFeignCalcResp();
            resp.setVendorMemberId(relation.getMemberId());
            resp.setVendorRoleId(relation.getRoleId());
            resp.setPlatform(Objects.nonNull(relation.getRelType()) && relation.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode()));
            if (Objects.nonNull(relation.getMember())) {
                resp.setVendorMemberName(relation.getMember().getName());
                resp.setVendorLogo(relation.getMember().getLogo());
            }

            if (Objects.nonNull(relation.getLevelRight())) {
                resp.setLevel(Objects.nonNull(relation.getLevelRight().getLevel()) ? relation.getLevelRight().getLevel() : 0);
                resp.setCurrentPoint(Objects.nonNull(relation.getLevelRight().getCurrentPoint()) ? relation.getLevelRight().getCurrentPoint() : 0);
                if (Objects.nonNull(relation.getLevelRight().getLevelConfig())) {
                    resp.setMemberLevelId(relation.getLevelRight().getLevelConfig().getId());
                    if (!CollectionUtils.isEmpty(relation.getLevelRight().getLevelConfig().getRights())) {
                        relation.getLevelRight().getLevelConfig().getRights().stream().filter(right -> right.getRightType().equals(MemberRightTypeEnum.PRICE_RIGHT.getCode()) && right.getStatus().equals(EnableDisableStatusEnum.ENABLE.getCode()) && NumberUtil.notNullAndPositive(right.getParameter())).findFirst().ifPresent(right -> resp.setDiscount(right.getParameter()));
                    }
                }
            }
            return resp;
        }).collect(Collectors.toList());
    }
}
