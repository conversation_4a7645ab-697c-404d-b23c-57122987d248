package com.ssy.lingxi.member.service.mobile;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.api.model.req.MemberSalesFindUserIdReq;
import com.ssy.lingxi.member.api.model.resp.MemberSalesFeignPageQueryResp;
import com.ssy.lingxi.member.model.resp.basic.MobileMemberSalesInformationResp;
import org.springframework.http.HttpHeaders;

/**
 * 业务员小程序--订单服务远程调用-业绩统计需要的会员信息-相关的接口
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-24
 */
public interface IMobileMemberSalesService {

    /**
     * 查看业务员详情
     *
     * @param headers 头部信息
     * @return 操作接口
     */
    MobileMemberSalesInformationResp getSalesInformation(HttpHeaders headers);


    /**
     * 远程调用--查看业务员统计
     *
     * @param pageVO 查询条件
     * @return 返回下级会员信息
     */
    PageDataResp<MemberSalesFeignPageQueryResp> getSalesList(MemberSalesFindUserIdReq pageVO);

}
