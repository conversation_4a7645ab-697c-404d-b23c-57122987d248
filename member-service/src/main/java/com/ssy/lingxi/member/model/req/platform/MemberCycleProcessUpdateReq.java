package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 会员生命周期变更流程规则
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberCycleProcessUpdateReq extends MemberCycleProcessReq {

    private static final long serialVersionUID = -5758025397894604836L;

    /**
     * 流程规则id
     */
    @NotNull(message = "流程规则id要大于0")
    @Positive(message = "流程规则id要大于0")
    private Long processId;
}
