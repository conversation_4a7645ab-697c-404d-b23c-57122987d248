package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.MemberRightHistoryDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员权益获取记录操作Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-15
 */
@Repository
public interface MemberRightHistoryRepository extends JpaRepository<MemberRightHistoryDO, Long>, JpaSpecificationExecutor<MemberRightHistoryDO> {

    Page<MemberRightHistoryDO> findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Pageable pageable);

    Page<MemberRightHistoryDO> findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleIdAndRightTypeEnum(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Integer rightTypeEnum, Pageable pageable);

    @Transactional
    void deleteAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    @Transactional
    void deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);

    MemberRightHistoryDO findFirstByBusinessNoAndRightTypeEnum(String businessNo, Integer rightTypeEnum);
}
