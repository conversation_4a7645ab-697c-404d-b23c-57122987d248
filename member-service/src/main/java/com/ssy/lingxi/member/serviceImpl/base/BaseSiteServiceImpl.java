package com.ssy.lingxi.member.serviceImpl.base;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.manage.api.feign.IInitConfigFeign;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseSiteService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 站点信息相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-14
 **/
@Service
public class BaseSiteServiceImpl implements IBaseSiteService {
    @Resource
    private IBaseMemberCacheService baseMemberCacheService;

    @Resource
    private IInitConfigFeign initConfigFeign;

    /**
     * 校验是否开启SAAS多租户部署
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public Boolean isEnableMultiTenancy(HttpHeaders headers) {
        WrapperResp<Boolean> wrapperResp = initConfigFeign.enableMultiTenancy();
        if (wrapperResp == null || wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_MANAGE_ERROR);
        }
        return wrapperResp.getData();
    }
}
