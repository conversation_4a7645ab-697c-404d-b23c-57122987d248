package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigLabelDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员注册资料关联的下拉框值Jpa仓库
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-08
 */
@Repository
public interface MemberRegisterConfigLabelRepository extends JpaRepository<MemberRegisterConfigLabelDO, Long>, JpaSpecificationExecutor<MemberRegisterConfigLabelDO> {
    List<MemberRegisterConfigLabelDO> findByMemberRegisterConfigId(Long memberRegisterConfigId);

}
