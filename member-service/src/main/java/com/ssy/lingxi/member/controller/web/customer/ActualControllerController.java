package com.ssy.lingxi.member.controller.web.customer;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.maintenance.ActualControllerBindOrChangeReq;
import com.ssy.lingxi.member.model.req.maintenance.ActualControllerPageReq;
import com.ssy.lingxi.member.model.req.maintenance.ActualControllerSaveOrUpdateReq;
import com.ssy.lingxi.member.model.req.maintenance.EditActualControllerLineOfCreditReq;
import com.ssy.lingxi.member.model.resp.customer.ActualControllerDetailResp;
import com.ssy.lingxi.member.model.resp.customer.ActualControllerPageResp;
import com.ssy.lingxi.member.model.resp.customer.ActualControllerParticularsResp;
import com.ssy.lingxi.member.service.web.IActualControllerService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 实控人控制层
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/9
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/actual/controller")
public class ActualControllerController extends BaseController {

    @Resource
    private IActualControllerService actualControllerService;

    /**
     * 分页查询实控人
     *
     * @param pageReq 请求参数
     * @return 实控人
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<ActualControllerPageResp>> page(@Valid ActualControllerPageReq pageReq) {
        return WrapperUtil.success(actualControllerService.page(getSysUser(), pageReq));
    }

    /**
     * 修改实控人信息
     *
     * @param req 接口参数
     * @return 修改结果
     */
    @PostMapping("/saveOrUpdate")
    public WrapperResp<Void> update(@RequestBody @Valid ActualControllerSaveOrUpdateReq req) {
        actualControllerService.saveOrUpdate(getSysUser(), req);
        return WrapperUtil.success();
    }

    /**
     * 获取实控人明细
     *
     * @param req 实控人ID
     * @return 实控人明细
     */
    @GetMapping("/getParticulars")
    public WrapperResp<ActualControllerParticularsResp> getParticulars(@Valid CommonIdReq req) {
        return WrapperUtil.success(actualControllerService.getParticulars(getSysUser(), req));
    }

    /**
     * 绑定或变更实控人
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @PostMapping("/bindOrChange")
    public WrapperResp<Void> bindOrChange(@RequestBody ActualControllerBindOrChangeReq req) {
        actualControllerService.bindOrChange(getSysUser(), req);
        return WrapperUtil.success();
    }


    /**
     * 编辑实控人授信额度
     *
     * @param
     * @return 实控人明细
     */
    @PostMapping("/editActualControllerLineOfCredit")
    public WrapperResp<Boolean> editActualControllerLineOfCredit(@RequestBody @Valid EditActualControllerLineOfCreditReq editActualControllerLineOfCreditReq) {
        return WrapperUtil.success(actualControllerService.editActualControllerLineOfCredit(editActualControllerLineOfCreditReq));
    }


    /**
     * 编辑会员授信额度
     *
     * @param
     * @return 实控人明细
     */
    @PostMapping("/editMemberLineOfCredit")
    public WrapperResp<Boolean> editLineOfCreditForMember(@RequestBody @Valid EditActualControllerLineOfCreditReq editActualControllerLineOfCreditReq) {
        return WrapperUtil.success(actualControllerService.editMemberLineOfCredit(editActualControllerLineOfCreditReq));
    }





}
