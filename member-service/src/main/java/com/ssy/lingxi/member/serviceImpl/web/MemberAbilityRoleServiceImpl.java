package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.LanguageEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberRoleGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberRoleQueryResp;
import com.ssy.lingxi.member.model.resp.platform.MemberRoleResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseAuthService;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseTokenManageService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberAbilityRoleService;
import com.ssy.lingxi.support.api.feign.ITencentIMFeign;
import com.ssy.lingxi.support.api.model.req.ImportIMAccountReq;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 会员自定义角色服务接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-30
 */
@Service
public class MemberAbilityRoleServiceImpl implements IMemberAbilityRoleService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberOrganizationRepository memberOrganizationRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private ITencentIMFeign tencentIMFeign;

    @Resource
    private MemberRoleAuthConfigRepository memberRoleAuthConfigRepository;

    @Resource
    private MenuRepository menuRepository;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseAuthService baseAuthService;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private MemberRelationRepository memberRelationRepository;

    @Resource
    private IBaseTokenManageService tokenManageService;

    @Override
    public PageDataResp<MemberRoleQueryResp> pageMemberRole(HttpHeaders headers, MemberRolePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Specification<UserRoleDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
            list.add(criteriaBuilder.equal(memberJoin.get("id").as(Long.class), loginUser.getMemberId()));

            if (pageVO.getStatus() != null) {
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), pageVO.getStatus()));
            }

            if (StringUtils.hasLength(pageVO.getRoleName())) {
                list.add(criteriaBuilder.like(root.get("roleName").as(String.class), "%" + pageVO.getRoleName().trim() + "%"));
            }

            if (Objects.nonNull(pageVO.getTypeEnum())){
                list.add(criteriaBuilder.equal(root.get("userType").as(Integer.class), pageVO.getTypeEnum()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<UserRoleDO> result = userRoleRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(this::createMemberRoleQueryVO).collect(Collectors.toList()));
    }

    @Override
    public List<MemberRoleQueryResp> getMemberRoleList(HttpHeaders headers, MemberRoleResp memberRoleResp) {

        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        Specification<UserRoleDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
            list.add(criteriaBuilder.equal(memberJoin.get("id").as(Long.class), loginUser.getMemberId()));

            if (Objects.nonNull(memberRoleResp.getStatus())) {
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), memberRoleResp.getStatus()));
            }

            if (Objects.nonNull(memberRoleResp.getTypeEnum())){
                list.add(criteriaBuilder.equal(root.get("userType").as(Integer.class), memberRoleResp.getTypeEnum()));
            }

            if (StringUtils.hasLength(memberRoleResp.getRoleName())) {
                list.add(criteriaBuilder.like(root.get("roleName").as(String.class), "%" + memberRoleResp.getRoleName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<UserRoleDO> result = userRoleRepository.findAll(specification, Sort.by("id").ascending());

        return result.stream().map(this::createMemberRoleQueryVO).collect(Collectors.toList());
    }

    private MemberRoleQueryResp createMemberRoleQueryVO(UserRoleDO memberRole){
        MemberRoleQueryResp queryVO = new MemberRoleQueryResp();
        queryVO.setId(memberRole.getId());
        queryVO.setRoleName(memberRole.getRoleName());
        queryVO.setRemark(memberRole.getRemark());
        queryVO.setStatus(memberRole.getStatus());
        queryVO.setImFlag(memberRole.getHasImAuth());
        queryVO.setTypeEnum(memberRole.getUserType());
        return queryVO;
    }

    @Override
    public PageDataResp<MemberRoleQueryResp> pageMemberRoleByName(HttpHeaders headers, MemberRolePageByNameDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Specification<UserRoleDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            Join<Object, Object> memberJoin = root.join("member", JoinType.LEFT);
            list.add(criteriaBuilder.equal(memberJoin.get("id").as(Long.class), loginUser.getMemberId()));

            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));

            if (StringUtils.hasLength(pageVO.getRoleName())) {
                list.add(criteriaBuilder.like(root.get("roleName").as(String.class), "%" + pageVO.getRoleName().trim() + "%"));
            }

            if (Objects.nonNull(pageVO.getTypeEnum())) {
                list.add(criteriaBuilder.equal(root.get("userType").as(Integer.class), pageVO.getTypeEnum()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<UserRoleDO> result = userRoleRepository.findAll(specification, page);

        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(this::createMemberRoleQueryVO).collect(Collectors.toList()));
    }

    @Override
    public AuthTreeResp memberAuthTree(HttpHeaders headers, UserRoleAuthTreeReq roleIdReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        // 获取当前登陆会员的所有状态为正常且至少审核通过过1次的平台权限
        List<MemberRelationDO> relDOList = memberRelationRepository.findBySubMemberIdAndRelTypeAndStatusAndVerified(loginUser.getMemberId(), MemberRelationTypeEnum.PLATFORM.getCode(), MemberStatusEnum.NORMAL.getCode(), MemberValidateStatusEnum.VERIFY_PASSED.getCode());

        // 收集权限位图集合
        List<byte[]> menuAuthList = new ArrayList<>();
        List<byte[]> buttonAuthList = new ArrayList<>();
        for (MemberRelationDO relDO : relDOList) {
            menuAuthList.add(relDO.getMenuAuth());
            buttonAuthList.add(relDO.getButtonAuth());
        }

        // 获取已勾选权限
        byte[] menuAuth = null;
        byte[] buttonAuth = null;
        if (Objects.nonNull(roleIdReq.getRoleId())) {
            UserRoleDO userRoleDO = userRoleRepository.findById(roleIdReq.getRoleId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));
            menuAuth = userRoleDO.getMenuAuth();
            buttonAuth = userRoleDO.getButtonAuth();
        }

        // 可视菜单按钮权限范围不超过其会员平台关系的权限范围（会员平台关系要过滤待审核和冻结的状态）
        String currentLanguage = LanguageEnum.getCurrentLanguage();
        return baseAuthService.getAuthTree(
                menuAuth,
                buttonAuth,
                CompletableFuture.supplyAsync(() -> baseAuthService.getMenuSetByIdSet(BitMapUtil.or(menuAuthList, BitMapUtil.ReturnType.IdSet), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor),
                CompletableFuture.supplyAsync(() -> baseAuthService.getButtonSetByIdSet(BitMapUtil.or(buttonAuthList, BitMapUtil.ReturnType.IdSet), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor));
    }

    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void addMemberRole(HttpHeaders headers, MemberAbilityRoleAddReq addVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        if (userRoleRepository.existsByMemberAndRoleName(memberDO, addVO.getRoleName())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_EXISTS);
        }

        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setMember(memberDO);
        userRoleDO.setUserType(UserTypeEnum.NORMAL.getCode());
        userRoleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userRoleDO.setHasImAuth(addVO.getImFlag() ? EnableDisableStatusEnum.ENABLE.getCode() : EnableDisableStatusEnum.DISABLE.getCode());
        userRoleDO.setUsers(new HashSet<>());
        userRoleDO.setRoleName(addVO.getRoleName());
        userRoleDO.setRemark(StringUtils.hasLength(addVO.getRemark()) ? addVO.getRemark().trim() : "");
        userRoleDO.setAuthConfig(new HashSet<>());
        //设置权限
        userRoleDO.setMenuAuth(BitMapUtil.toByteArray(addVO.getMenuIdList()));
        userRoleDO.setButtonAuth(BitMapUtil.toByteArray(addVO.getButtonIdList()));
        userRoleDO.setApiAuth(BitMapUtil.emptyByteArray());

        userRoleRepository.saveAndFlush(userRoleDO);

        //通知工作流服务
        // TODO: 2023/8/10 是否兼容工作流的authBO
//        workflowFeignService.upsertMemberRoleToProcessAsync(loginUser.getMemberId(), memberRoleDO.getId(), memberRoleDO.getRoleName(), roleAuthList);
    }

    @Override
    public MemberRoleGetResp getMemberRole(HttpHeaders headers, UserRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserRoleDO memberRoleDO = userRoleRepository.findFirstByMemberAndId(memberDO, idVO.getRoleId());
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        MemberRoleGetResp result = new MemberRoleGetResp();
        result.setId(memberRoleDO.getId());
        result.setRoleName(memberRoleDO.getRoleName());
        result.setRemark(memberRoleDO.getRemark());
        result.setStatus(memberRoleDO.getStatus());
        result.setImFlag(memberRoleDO.getHasImAuth());

        return result;
    }

    @Override
    @Transactional(rollbackFor = BusinessException.class)
    public void updateMemberRole(HttpHeaders headers, MemberAbilityRoleUpdateReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserRoleDO userRoleDO = userRoleRepository.findById(updateVO.getRoleId()).orElse(null);
        if (userRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        if (userRoleRepository.existsByMemberAndRoleNameAndIdNot(memberDO, updateVO.getRoleName(), userRoleDO.getId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_EXISTS);
        }

        //不能更改会员“超级管理员”用户角色
        if (userRoleDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELTE_OR_UPDATE_MEMBER_ROLE);
        }

        userRoleDO.setHasImAuth(updateVO.getImFlag() ? EnableDisableStatusEnum.ENABLE.getCode() : EnableDisableStatusEnum.DISABLE.getCode());
        userRoleDO.setRoleName(updateVO.getRoleName());
        userRoleDO.setRemark(StringUtils.hasLength(updateVO.getRemark()) ? updateVO.getRemark().trim() : "");
        userRoleDO.setMenuAuth(BitMapUtil.toByteArray(updateVO.getMenuIdList()));
        userRoleDO.setButtonAuth(BitMapUtil.toByteArray(updateVO.getButtonIdList()));
        userRoleRepository.save(userRoleDO);

        //更新使用该角色的相关用户权限
        baseAuthService.updateUserAuthByUserRole(userRoleDO);

        //删除指定用户的所有token
        CompletableFuture.runAsync(() -> tokenManageService.userOffline(userRoleDO.getUsers().stream().map(UserDO::getId).collect(Collectors.toList())), ThreadPoolConfig.asyncDefaultExecutor);

        //有im权限，则注册im
        if (updateVO.getImFlag()) {
            CompletableFuture.runAsync(() -> tencentIMFeign.batchImportAccount(userRoleDO.getUsers().stream().map(userDO -> new ImportIMAccountReq(userDO.getId(), userDO.getName(), userDO.getLogo())).collect(Collectors.toList())), ThreadPoolConfig.asyncDefaultExecutor);
        }

        //通知工作流服务，关联流程
        // TODO: 2023/8/10 是否兼容工作流的authBO
//        workflowFeignService.upsertMemberRoleToProcessAsync(loginUser.getMemberId(), userRoleDO.getId(), userRoleDO.getRoleName(), userRoleDO.getAuth());
    }

    @Override
    public void deleteMemberRole(HttpHeaders headers, UserRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserRoleDO memberRoleDO = userRoleRepository.findFirstByMemberAndId(memberDO, idVO.getRoleId());
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        //不能更改会员“超级管理员”用户
        if (memberRoleDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELTE_OR_UPDATE_MEMBER_ROLE);
        }

        //如果已经有用户，不能删除
        if (!CollectionUtils.isEmpty(memberRoleDO.getUsers())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_CANNOT_DELETE_WHEN_USER_EXISTS);
        }

        List<MemberRoleAuthConfigDO> authConfigDOList = new ArrayList<>(memberRoleDO.getAuthConfig());
        if(!CollectionUtils.isEmpty(authConfigDOList)) {
            authConfigDOList.forEach(authConfig -> authConfig.getOrgs().clear());

            memberRoleAuthConfigRepository.deleteAll(memberRoleDO.getAuthConfig());
        }

        userRoleRepository.delete(memberRoleDO);

        //通知工作流服务，移除会员自定义角色的关联
        // TODO: 2023/8/10 是否兼容工作流的authBO
//        workflowFeignService.removeMemberRoleInProcessAsync(loginUser.getMemberId(), idVO.getRoleId());

    }

    @Override
    public void updateMemberRoleStatus(HttpHeaders headers, MemberRoleUpdateStatusReq statusVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserRoleDO memberRoleDO = userRoleRepository.findFirstByMemberAndId(memberDO, statusVO.getId());
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        //不能更改会员“超级管理员”用户
        if (memberRoleDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_FORBID_DELTE_OR_UPDATE_MEMBER_ROLE);
        }

        //如果已经有用户，不能修改状态
        if (!CollectionUtils.isEmpty(memberRoleDO.getUsers())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_CANNOT_DELETE_WHEN_USER_EXISTS);
        }

        memberRoleDO.setStatus(statusVO.getStatus());
        userRoleRepository.saveAndFlush(memberRoleDO);

        // TODO: 2023/8/10 是否兼容工作流的authBO
//        if (memberRoleDO.getStatus().equals(EnableDisableStatus.ENABLE.getCode())) {
//            workflowFeignService.upsertMemberRoleToProcessAsync(loginUser.getMemberId(), memberRoleDO.getId(), memberRoleDO.getRoleName(), memberRoleDO.getAuth());
//        } else {
//            workflowFeignService.removeMemberRoleInProcessAsync(loginUser.getMemberId(), memberRoleDO.getId());
//        }

    }

    @Override
    public List<MemberRoleResp> otherRoleList(HttpHeaders headers) {

        // 校验登录用户信息
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        // 获取会员信息
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));

        // 转换格式
        return memberDO.getMemberRoles().stream().filter(role -> !Objects.equals(role.getId(), loginUser.getMemberRoleId())).map(role -> new MemberRoleResp(role.getId(), role.getRoleName())).collect(Collectors.toList());
    }
}
