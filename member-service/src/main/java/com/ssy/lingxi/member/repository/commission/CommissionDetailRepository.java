package com.ssy.lingxi.member.repository.commission;

import com.ssy.lingxi.member.entity.do_.commission.CommissionDetailDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 佣金明细Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Repository
public interface CommissionDetailRepository extends JpaRepository<CommissionDetailDO, Long>, JpaSpecificationExecutor<CommissionDetailDO> {

    /**
     * 根据分佣账户id查询佣金明细列表
     * @param commissionAccountId 分佣账户id
     * @param pageable 分页参数
     * @return 佣金明细列表
     */
    Page<CommissionDetailDO> findByCommissionAccountIdOrderByTradeTimeDesc(Long commissionAccountId, Pageable pageable);

    /**
     * 根据用户id查询佣金明细列表
     * @param userId 用户id
     * @param pageable 分页参数
     * @return 佣金明细列表
     */
    Page<CommissionDetailDO> findByUserIdOrderByTradeTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据交易流水号查询佣金明细
     * @param tradeCode 交易流水号
     * @return 佣金明细
     */
    Optional<CommissionDetailDO> findByTradeCode(String tradeCode);

    /**
     * 检查交易流水号是否存在
     * @param tradeCode 交易流水号
     * @return 是否存在
     */
    boolean existsByTradeCode(String tradeCode);

    /**
     * 根据交易类型查询佣金明细列表
     * @param tradeType 交易类型
     * @param pageable 分页参数
     * @return 佣金明细列表
     */
    Page<CommissionDetailDO> findByTradeTypeOrderByTradeTimeDesc(Integer tradeType, Pageable pageable);

    /**
     * 根据用户id和交易类型查询佣金明细列表
     * @param userId 用户id
     * @param tradeType 交易类型
     * @param pageable 分页参数
     * @return 佣金明细列表
     */
    Page<CommissionDetailDO> findByUserIdAndTradeTypeOrderByTradeTimeDesc(Long userId, Integer tradeType, Pageable pageable);

    /**
     * 根据关联订单号查询佣金明细列表
     * @param relatedOrderNo 关联订单号
     * @return 佣金明细列表
     */
    List<CommissionDetailDO> findByRelatedOrderNo(String relatedOrderNo);

    /**
     * 根据关联用户id查询佣金明细列表
     * @param relatedUserId 关联用户id
     * @param pageable 分页参数
     * @return 佣金明细列表
     */
    Page<CommissionDetailDO> findByRelatedUserIdOrderByTradeTimeDesc(Long relatedUserId, Pageable pageable);

    /**
     * 查询指定时间范围内的佣金明细
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 佣金明细列表
     */
    @Query("SELECT cd FROM CommissionDetailDO cd WHERE cd.tradeTime >= :startTime AND cd.tradeTime <= :endTime ORDER BY cd.tradeTime DESC")
    Page<CommissionDetailDO> findByTradeTimeBetween(@Param("startTime") Long startTime, @Param("endTime") Long endTime, Pageable pageable);

    /**
     * 统计用户总收益
     * @param userId 用户id
     * @return 总收益
     */
    @Query("SELECT COALESCE(SUM(cd.changeAmount), 0) FROM CommissionDetailDO cd WHERE cd.userId = :userId AND cd.changeAmount > 0")
    BigDecimal sumTotalIncomeByUserId(@Param("userId") Long userId);

    /**
     * 根据关联订单号和交易类型查询佣金明细
     * @param relatedOrderNo 关联订单号
     * @param tradeType 交易类型
     * @return 佣金明细
     */
    Optional<CommissionDetailDO> findByRelatedOrderNoAndTradeType(String relatedOrderNo, Integer tradeType);

    /**
     * 根据关联用户id和交易类型统计数量
     * @param relatedUserId 关联用户id
     * @param tradeType 交易类型
     * @return 数量
     */
    long countByRelatedUserIdAndTradeType(Long relatedUserId, Integer tradeType);

}
