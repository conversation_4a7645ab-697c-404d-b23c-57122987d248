package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.validate.MemberInnerValidateHistoryDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 会员审核内部订单历史记录操作Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-17
 */
@Repository
public interface MemberInnerValidateHistoryRepository extends JpaRepository<MemberInnerValidateHistoryDO, Long>, JpaSpecificationExecutor<MemberInnerValidateHistoryDO> {

    List<MemberInnerValidateHistoryDO> findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Sort sort);

    List<MemberInnerValidateHistoryDO> findBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    @Transactional
    void deleteAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    @Transactional
    void deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);
}
