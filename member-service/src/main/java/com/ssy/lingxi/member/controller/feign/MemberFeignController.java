package com.ssy.lingxi.member.controller.feign;

import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;
import com.ssy.lingxi.member.service.feign.IMemberFeignService;
import com.ssy.lingxi.member.service.web.IRunBrandService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 内部接口 - 会员查询服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-11
 * @ignore 不需要提交到Yapi
 */
@RestController
public class MemberFeignController implements IMemberFeign {

    @Resource
    private IMemberFeignService memberFeignService;
    @Resource
    private IRunBrandService runBrandService;

    /**
     * 新增会员支付策略 - 查询作为服务提供者的会员信息列表
     * 由于接口参数有嵌套类，Feign发送Get请求的时候反序列化接口参数会报错，所以改为Post请求
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<PageDataResp<MemberFeignPayProviderResultResp>> findPayProviderMembers(@RequestBody @Valid MemberFeignPayProviderDataReq pageVO) {
        return WrapperUtil.success(memberFeignService.findPayProviderMembers(pageVO));
    }

    /**
     * 新增会员支付策略 - 选择适用会员
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<PageDataResp<MemberFeignPayProviderResultResp>> pageNewPayProviderMembers(@RequestBody @Valid MemberFeignPayProviderExcludeDataReq pageVO) {
        return WrapperUtil.success(memberFeignService.pageNewPayProviderMembers(pageVO));
    }

    /**
     * 模板服务 - 模糊查询会员信息列表
     * @param listVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<List<MemberFeignListResultResp>> findMembersFromTemplateService(@Valid MemberFeignListReq listVO) {
        return WrapperUtil.success(memberFeignService.findMembersFromTemplateService(listVO));
    }

    /**
     * 根据会员Id和角色Id，查询上属会员列表（不区分企业会员、渠道会员）
     * <p>其中等级为当前会员在其上级会员和上级会员角色下的等级</p>
     * @param feignVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<List<MemberFeignQueryResp>> listUpperMembers(@RequestBody @Valid MemberFeignReq feignVO) {
        return WrapperUtil.success(memberFeignService.listUpperMembers(feignVO));
    }

    /**
     * 根据会员Id和角色Id，查询下属角色类型为“服务消费者”的会员列表（不区分企业会员、渠道会员）
     * <p>其中等级为下级会员和角色在当前会员和角色下的等级</p>
     * @param feignVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<List<MemberFeignQueryResp>> listLowerMembers(@RequestBody @Valid MemberFeignSubReq feignVO) {
        return WrapperUtil.success(memberFeignService.listLowerMembers(feignVO));
    }

    /**
     * 根据会员Id和角色Id，查询“审核通过”的下级会员列表（不区分会员类型、角色类型）
     * <p>返回结果中的等级为下级会员和角色在当前会员和角色下的等级</p>
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignQueryResp>> listAllLowerMembers(@RequestBody @Valid MemberFeignReq feignVO) {
        return WrapperUtil.success(memberFeignService.listAllLowerMembers(feignVO));
    }

    /**
     * 根据会员Id和角色Id，查询“审核通过”的下级会员列表（角色类型为服务提供者）
     * <p>返回结果中的等级为下级会员和角色在当前会员和角色下的等级</p>
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignQueryResp>> listSubProviderLowerMembers(@RequestBody @Valid MemberFeignReq feignVO) {
        return WrapperUtil.success(memberFeignService.listSubProviderLowerMembers(feignVO));
    }

    /**
     * 根据会员Id、角色Id、上级会员Id、上级会员角色Id，查询会员信息
     * <p>其中等级为当前会员在其上级会员和上级会员角色下的等级</p>
     * @param feignVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<MemberFeignQueryResp> getMemberInfo(@RequestBody @Valid MemberRelationFeignReq feignVO) {
        return WrapperUtil.success(memberFeignService.getMemberInfo(feignVO));
    }

    /**
     * 根据会员Id，查询会员注册信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<MemberFeignRegisterQueryResp> getMemberRegisterInfo(@RequestBody @Valid MemberFeignIdReq idVO) {
        return WrapperUtil.success(memberFeignService.getMemberRegisterInfo(idVO));
    }

    @Override
    public WrapperResp<MemberNameResp> getMemberName(MemberFeignIdReq req) {
        return WrapperUtil.success(memberFeignService.getMemberName(req));
    }

    /**
     * 根据会员Id列表，查询具有“服务提供者”角色类型的会员信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @Override
    public WrapperResp<List<MemberFeignBatchByIdQueryResp>> batchFindMembersByIdList(@RequestBody @Valid MemberFeignBatchByIdReq queryVO) {
        return WrapperUtil.success(memberFeignService.batchFindMembersByIdList(queryVO));
    }

    /**
     * 根据会员Id列表，查询具有“服务提供者”角色类型的“平台会员”信息
     * @param idsVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignShopQueryResp>> batchFindServiceProviderMemberByIdList(@RequestBody @Valid MemberFeignIdsReq idsVO) {
        return WrapperUtil.success(memberFeignService.batchFindServiceProviderMemberByIdList(idsVO));
    }

    /**
     * 根据会员名称，查询具有“服务提供者”角色类型的企业会员、企业个人会员的Id列表
     * @param nameVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberFeignIdsResultResp> findMerchantAndProviderMemberIdListByName(@RequestBody @Valid MemberFeignNameReq nameVO) {
        return WrapperUtil.success(memberFeignService.findMerchantAndProviderMemberIdListByName(nameVO));
    }

    /**
     * 根据会员Id、角色Id查询平台会员信息
     * <p>其中等级为当前会员的平台等级</p>
     * @param memberFeignReqList 接口参数
     * @return 查询结果
     **/
    @Override
    public WrapperResp<List<MemberFeignQueryResp>> listPlatformMembers(@RequestBody @Valid List<MemberFeignReq> memberFeignReqList) {
        return WrapperUtil.success(memberFeignService.listPlatformMembers(memberFeignReqList));
    }

    /**
     * 根据会员Id、角色Id查询会员信息
     **/
    @Override
    public WrapperResp<List<MemberFeignQueryResp>> listMembers(@RequestBody @Valid List<MemberFeignReq> memberFeignReqList) {
        return WrapperUtil.success(memberFeignService.listMembers(memberFeignReqList));
    }

    /**
     * 根据会员Id和Aes加密后支付密码，校验支付密码
     * @param checkVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberFeignPayPswCheckResultResp> checkMemberPayPassword(@RequestBody @Valid MemberFeignPayPswCheckReq checkVO) {
        return WrapperUtil.success(memberFeignService.checkMemberPayPassword(checkVO));
    }

    /**
     * 批量查询会员Logo
     * @param memberIds 会员id列表
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignLogoResp>> getMemberLogos(@RequestBody List<Long> memberIds) {
        return WrapperUtil.success(memberFeignService.getMemberLogos(memberIds));
    }

    /**
     * 根据会员ID批量查询IM客服用户
     * @param memberIds 会员id列表
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignImUserResp>> getImUsers(@RequestBody List<Long> memberIds) {
        return WrapperUtil.success(memberFeignService.getImUsers(memberIds));
    }

    /**
     * 根据用户ID批量查询用户信息
     * @param userIds 用户id列表
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignUserResp>> getUsers(@RequestBody List<Long> userIds) {
        return WrapperUtil.success(memberFeignService.getUsers(userIds));
    }

    @Override
    public WrapperResp<MemberFeignUserResp> getUser(Long userId) {
        return WrapperUtil.success(memberFeignService.getUser(userId));
    }

    /**
     * 批量查询角色名称
     * @param roleIds 角色id列表
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignRoleResp>> getRoles(@RequestBody List<Long> roleIds) {
        return WrapperUtil.success(memberFeignService.getRoles(roleIds));
    }

    /**
     * 订单服务，查询流程规则适用会员列表
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignPageQueryResp>> findPlatformMembers(@RequestBody @Valid List<MemberFeignReq> feignVO) {
        return WrapperUtil.success(memberFeignService.findPlatformMembers(feignVO));
    }

    /**
     * 订单服务，商户支付参数配置，查询平台服务提供者企业会员列表
     * @param paymentVO 接口参数
     * @return 查询结果
     **/
    @Override
    public WrapperResp<List<MemberFeignPageQueryResp>> findProviderMerchant(@RequestBody @Valid MemberFeignPaymentReq paymentVO) {
        return WrapperUtil.success(memberFeignService.findProviderMerchant(paymentVO));
    }

    /**
     * 查询是否下级会员
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<Boolean> isSubMember(@RequestBody @Valid MemberRelationFeignReq feignVO) {
        return WrapperUtil.success(memberFeignService.isSubMember(feignVO));
    }

    /**
     * 能力中心 - 营销服务, 查询下级会员适用会员
     * @param memberAndUpperMembersReq 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<AtSubMemberSuitableMemberResp>> listAbilitySubMemberSuitableMember(@RequestBody @Valid MemberAndUpperMembersReq memberAndUpperMembersReq) {
        return WrapperUtil.success(memberFeignService.listAbilitySubMemberSuitableMember(memberAndUpperMembersReq));
    }

    /**
     * 平台后台 - 营销服务, 查询下级会员适用会员
     * @param memberFeignReq 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<PfSubMemberSuitableMemberResp> getPlatformSubMemberSuitableMember(@RequestBody @Valid MemberFeignReq memberFeignReq) {
        return WrapperUtil.success(memberFeignService.getPlatformSubMemberSuitableMember(memberFeignReq));
    }

    /**
     * 所有服务通用 - 查询平台规则配置
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberRuleDetailResp>> findMemberRules(@RequestBody MemberRuleDetailFeignReq feignVO) {
        return WrapperUtil.success(memberFeignService.findMemberRules(feignVO));
    }

    /**
     * 根据会员关系，查询业务员Id
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberSalesFeignResp>> findMemberSales(@RequestBody List<MemberFeignRelationReq> feignVO) {
        return WrapperUtil.success(memberFeignService.findMemberSales(feignVO));
    }

    /**
     * 筛选角色类型为服务提供者的会员
     * @param members 会员Id和角色Id的缓存实体
     * @return 筛选结果
     */
    @Override
    public WrapperResp<List<MemberAndRoleIdDTO>> screenMemberUser(@RequestBody List<MemberAndRoleIdDTO> members) {
        return WrapperUtil.success(memberFeignService.screenMemberUser(members));
    }

    /**
     * 查询指定会员的生命周期规则配置相关
     * @param memberGetLifecycleFeignReq 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberLifecycleRuleQueryResp> getLifecycle(@RequestBody MemberGetLifecycleFeignReq memberGetLifecycleFeignReq) {
        return WrapperUtil.success(memberFeignService.getLifecycle(memberGetLifecycleFeignReq));
    }

    /**
     * 查找角色类型为服务提供者的所有会员
     */
    @Override
    public WrapperResp<List<MemberAndRoleIdDTO>> allSupplier() {
        return WrapperUtil.success(memberFeignService.allSupplierList());
    }

    /**
     * 合同能力- 查询会员合同模版所需参数
     * @param queryVOS 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberContractDetailResp>> getMemberContractDetail(@RequestBody List<MemberContractQueryReq> queryVOS){
        if (CollUtil.isEmpty(queryVOS)){
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_PARAMETER_CANNOT_BE_EMPTY);
        }
        return WrapperUtil.success(memberFeignService.getMemberContractDetail(queryVOS));
    }

    /**
     * （营销服务v3）优惠券发券 - 发券时查询会员列表
     * @param req 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<PageDataResp<MarketingMemberFeignResp>> pageMarketingCouponMembers(@RequestBody MarketingMemberFeignReq req) {
        return WrapperUtil.success(memberFeignService.pageMarketingCouponMembers(req));
    }

    /**
     * （营销服务v3）优惠券发券 - 查询已经发券的会员列表
     * @param req 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MarketingMemberFeignResp>> findMarketingCouponMembers(@RequestBody MarketingDistributeMemberFeignReq req) {
        return WrapperUtil.success(memberFeignService.findMarketingCouponMembers(req));
    }

    /**
     * 查询商家会员id，和角色id
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberFeignMerchantResp> getMerchantMember() {
        return WrapperUtil.success(memberFeignService.getMerchantMember());
    }

    @Override
    public WrapperResp<MemberFeignCodeRes> findByCode(MemberFeignCodeReq memberFeignCodeReq) {
        return WrapperUtil.success(memberFeignService.findByCode(memberFeignCodeReq));
    }

    @Override
    public WrapperResp<MemberFeignCodeRes> findMemberById(GetMemberByIdReq getMemberByIdReq) {
        return WrapperUtil.success(memberFeignService.findMemberById(getMemberByIdReq));
    }

    /**
     * 查询商家信息和超级管理员信息
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberFeignMerchantAndAdminResp> getMerchantAndAdmin() {
        return WrapperUtil.success(memberFeignService.getMerchantAndAdminInfo());
    }

    @Override
    public WrapperResp<List<MemberInfoResp>> findByCorporationIds(List<Long> corporationIds) {
        return WrapperUtil.success(memberFeignService.findByCorporationIds(corporationIds));
    }

    /**
     * 根据会员id，会员code
     *
     * @param memberId 会员id
     * @return 查询结果
     */
    @Override
    public WrapperResp<String> findByMemberId(CommonIdReq memberId) {
        return WrapperUtil.success(memberFeignService.findByMemberId(memberId));
    }

    /**
     * 根据买家会员id，查询（营业证统一编码，手机号，邮箱）
     * @param memberId 会员id
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberInsuredIdNoResp> findByMemberIdAndRoleId(CommonIdReq memberId) {
        return WrapperUtil.success(memberFeignService.findBusinessInfoByMemberId(memberId));
    }

    /**
     * 根据会员id，查询会员信息
     * @param memberIdList 会员id
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignCodeRes>> findMemberById(CommonIdListReq memberIdList) {
        return WrapperUtil.success(memberFeignService.findMemberByIdList(memberIdList));
    }

    /**
     * 根据会员id，查询会员信息
     * @param  commonIdListReq 品牌会员id
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberBrandInfoResp>> findMemberBrandByIds(@RequestBody CommonIdListReq commonIdListReq) {
        return WrapperUtil.success(memberFeignService.findMemberBrandByIds(commonIdListReq));
    }

    @Override
    public WrapperResp<MemberBrandInfoResp> findMemberBrandById(@RequestBody CommonIdReq commonIdReq){
        return WrapperUtil.success(memberFeignService.findMemberBrandById(commonIdReq));
    }

    @Override
    public WrapperResp<List<RunBrandFeignResp>> findRunBrandList() {
        return WrapperUtil.success(runBrandService.listByFeign());
    }
}
