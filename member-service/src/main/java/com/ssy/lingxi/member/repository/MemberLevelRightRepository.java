package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelConfigDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRightDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员等级权益操作类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-10
 */
@Repository
public interface MemberLevelRightRepository extends JpaRepository<MemberLevelRightDO, Long>, JpaSpecificationExecutor<MemberLevelRightDO> {
    @Transactional
    void deleteAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    Boolean existsByMemberIdAndRoleIdAndLevelConfig(Long memberId, Long roleId, MemberLevelConfigDO levelConfigDO);
}
