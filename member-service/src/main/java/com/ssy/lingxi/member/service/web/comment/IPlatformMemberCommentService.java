package com.ssy.lingxi.member.service.web.comment;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.comment.*;
import com.ssy.lingxi.member.model.resp.comment.MemberOrderTradeCommentPageResp;
import com.ssy.lingxi.member.model.resp.comment.MemberTradeCommentDetailResp;
import com.ssy.lingxi.member.model.resp.comment.PlatformMemberTradeCommentPageResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditCommentSummaryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditTradeHistoryResp;
import org.springframework.http.HttpHeaders;

/**
 * 平台后台-评价管理服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
public interface IPlatformMemberCommentService {

    /**
     * 交易能力 - 评价管理 - 评价分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberOrderTradeCommentPageResp> pageMemberOrderTradeCommentHistory(HttpHeaders headers, MemberOrderTradeCommentDataReq pageVO);

    /**
     * 交易能力 - 评价管理 - 评价详情
     * @param headers Http头部信息
     * @param tradeCommentIdVO 接口参数
     * @return 查询结果
     */
    MemberTradeCommentDetailResp getMemberTradeCommentHistory(HttpHeaders headers, MemberTradeCommentIdReq tradeCommentIdVO);

    /**
     * 平台后台 - 评价管理 - 会员评价查询分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<PlatformMemberTradeCommentPageResp> pagePlatformMemberTradeComment(HttpHeaders headers, PlatformMemberTradeCommentDataReq pageVO);

    /**
     * 平台后台-评价管理-批量删除评价
     * @param headers Http头部信息
     * @param tradeCommentIdsVO 接口参数
     * @return 操作结果
     */
    void deletePlatformMemberTradeComment(HttpHeaders headers, MemberTradeCommentIdsReq tradeCommentIdsVO);

    /**
     * 平台后台-评价管理-屏蔽/显示评价
     * @param headers Http头部信息
     * @param updateStatusVO 接口参数
     * @return 操作结果
     */
    void updateStatusPlatformMemberTradeComment(HttpHeaders headers, MemberTradeCommentUpdateStatusReq updateStatusVO);

    /**
     * 会员详情 - 会员信用 - 交易评价汇总（平台层面汇总）
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getAllMemberDetailCreditTradeCommentSummary(HttpHeaders headers, PlatformMemberTradeCommentSummaryReq pageVO);

    /**
     * 平台后台-评价管理-分页查询交易评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditTradeHistoryResp> pagePlatformMemberTradeCommentHistory(HttpHeaders headers, PlatformMemberTradeCommentHistoryDataReq pageVO);
}
