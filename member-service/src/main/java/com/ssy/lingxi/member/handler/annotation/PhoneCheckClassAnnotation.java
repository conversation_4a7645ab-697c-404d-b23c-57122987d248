package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.PhoneCheckClassValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-04
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = PhoneCheckClassValidator.class)
public @interface PhoneCheckClassAnnotation {

    //手机号码前缀的字段名称
    String telCodeFieldName() default "telCode";

    String countryCode() default "CN";

    //手机号码的字段名称
    String phoneFieldName() default "phone";

    String message() default "请输入正确的手机号码";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    // 是否略过统一校验 选择true的话将交由业务进行校验
    boolean ignore() default false;
}
