package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.SelectLongResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRightSpendTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.PasswordUtil;
import com.ssy.lingxi.member.api.enums.MemberRightTypeEnum;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRightDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberPointsDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberRightHistoryDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberRightSpendHistoryDO;
import com.ssy.lingxi.member.enums.MemberPointsTypeEnum;
import com.ssy.lingxi.member.enums.PlatformInnerStatusEnum;
import com.ssy.lingxi.member.model.req.points.MemberPointsAddReq;
import com.ssy.lingxi.member.model.req.points.MemberPointsBatchAddReq;
import com.ssy.lingxi.member.model.req.points.MemberPointsPageDataReq;
import com.ssy.lingxi.member.model.resp.points.MemberPointsPageDataResp;
import com.ssy.lingxi.member.model.resp.points.MemberPointsQuerySearchConditionResp;
import com.ssy.lingxi.member.model.resp.points.MemberPointsSelectResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.web.IMemberPointsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (MemberPoints)服务实现类
 *
 * <AUTHOR>
 * @since 2024-08-08
 */
@Slf4j
@Service
public class MemberPointsServiceImpl implements IMemberPointsService {

    @Resource
    private MemberPointsRepository memberPointsRepository;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private MemberRelationRepository memberRelationRepository;

    @Resource
    private MemberRightHistoryRepository memberRightHistoryRepository;

    @Resource
    private MemberLevelRightRepository memberLevelRightRepository;

    @Resource
    private MemberRightSpendHistoryRepository memberRightSpendHistoryRepository;

    @Resource
    private UserRepository userRepository;

    @Override
    public MemberPointsQuerySearchConditionResp getPageCondition(UserLoginCacheDTO sysUser) {
        // 构建查询条件
        // 条件1: 角色类型是服务消费者
        Specification<MemberRoleDO> specification = (root, query, builder) -> {
            Predicate predicate = builder.conjunction();
            List<Expression<Boolean>> expressions = predicate.getExpressions();
            expressions.add(builder.equal(root.get("roleType"), RoleTypeEnum.SERVICE_CONSUMER.getCode()));
            return query.where(predicate).getRestriction();
        };

        List<SelectLongResp> roleIdAndName = memberRoleRepository.findAll(specification)
                .stream()
                .map(memberRoleDO -> new SelectLongResp(memberRoleDO.getId(), memberRoleDO.getRoleName()))
                .collect(Collectors.toList());

        MemberPointsQuerySearchConditionResp memberPointsQueryConditionResp = new MemberPointsQuerySearchConditionResp();
        memberPointsQueryConditionResp.setRoleIdAndName(roleIdAndName);

        return memberPointsQueryConditionResp;
    }

    /**
     * 分页查询
     *
     * @param sysUser 登录用户
     * @param pageReq 筛选条件
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberPointsPageDataResp> page(UserLoginCacheDTO sysUser, MemberPointsPageDataReq pageReq) {
        // 创建分页
        Pageable page = PageRequest.of(pageReq.getCurrent() - 1, pageReq.getPageSize(), Sort.by(MemberPointsDO.Fields.createTime).descending());

        Page<MemberPointsDO> pageList = memberPointsRepository.findAll((root, query, builder) -> {
            Predicate conjunction = builder.conjunction();
            List<Expression<Boolean>> expressions = conjunction.getExpressions();

            // 请求参数会员名称，不为空模糊查询
            Optional.ofNullable(pageReq.getSubMemberName())
                    .filter(StringUtils::hasText)
                    .ifPresent(subMemberName -> expressions.add(builder.like(root.get(MemberPointsDO.Fields.subMemberName), "%" + subMemberName + "%")));
            // 请求参数会员角色，存在，精准匹配
            Optional.ofNullable(pageReq.getSubRoleId())
                    .ifPresent(subRoleId -> expressions.add(builder.equal(root.get(MemberPointsDO.Fields.subRoleId), subRoleId)));

            return query.where(conjunction).getRestriction();
        }, page);

        // 分装数据
        List<MemberPointsPageDataResp> resultList = pageList.stream().map(this::builderDataRespByDO).collect(Collectors.toList());
        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public PageDataResp<MemberPointsSelectResp> selectMemberPage(UserLoginCacheDTO sysUser, MemberPointsPageDataReq pageReq) {

        // 创建分页
        Pageable page = PageRequest.of(pageReq.getCurrent() - 1, pageReq.getPageSize(), Sort.by(MemberPointsDO.Fields.createTime).descending());


        Specification<MemberRelationDO> specification = (root, query, builder) -> {
            Predicate predicate = builder.conjunction();
            List<Expression<Boolean>> expressions = predicate.getExpressions();

            // 角色类型是服务消费者
            Join<MemberRelationDO, MemberRoleDO>  subRole = root.join("subRole", JoinType.INNER);
            expressions.add(builder.equal(subRole.get("roleType"), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            //上级为平台
            expressions.add(builder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            //不查询由会员创建的，未提交平台审核的数据（此时数据的内部状态为 MemberInnerStatusEnum.REGISTERING.getCode()
            expressions.add(builder.notEqual(root.get("innerStatus").as(Integer.class), PlatformInnerStatusEnum.REGISTERING.getCode()));


            // 请求参数会员名称，不为空模糊查询
            Optional.ofNullable(pageReq.getSubMemberName())
                    .filter(StringUtils::hasText)
                    .ifPresent(subMemberName -> {
                        Join<MemberRelationDO, MemberDO> subMember = root.join("subMember", JoinType.INNER);
                        expressions.add(builder.like(subMember.get("name"), "%" + subMemberName + "%"));
                    });

            // 请求参数会员角色，存在，精准匹配
             Optional.ofNullable(pageReq.getSubRoleId())
                    .ifPresent(subRoleId -> expressions.add(builder.equal(root.get("subRoleId"), subRoleId)));

             return query.where(predicate).getRestriction();
        };

        Page<MemberRelationDO> pageList = memberRelationRepository.findAll(specification, page);

        List<MemberPointsSelectResp> resultList = pageList.stream().map(this::buildMemberPointsSelectResp).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 构建添加会员积分 - 选择会员弹窗分页数据
     * @param memberRelationDO 会员关系数据
     * @return 构建添加会员积分 - 选择会员弹窗分页数据
     */
    public  MemberPointsSelectResp buildMemberPointsSelectResp(MemberRelationDO memberRelationDO) {
        MemberDO subMember = memberRelationDO.getSubMember();
        MemberPointsSelectResp memberPointsSelectResp = new MemberPointsSelectResp();
        memberPointsSelectResp.setMemberRelationId(memberRelationDO.getId());
        memberPointsSelectResp.setSubMemberId(memberRelationDO.getSubMemberId());
        memberPointsSelectResp.setSubMemberName(subMember.getName());
        memberPointsSelectResp.setSubAccount(subMember.getAccount());
        memberPointsSelectResp.setSubRoleName(memberRelationDO.getSubRoleName());
        return memberPointsSelectResp;
    }

    /**
     * 构建分页数据
     *
     * @param memberPointsDO 数据
     * @return 数据
     */
    private MemberPointsPageDataResp builderDataRespByDO(MemberPointsDO memberPointsDO) {
        MemberPointsPageDataResp memberPointsPageDataResp = new MemberPointsPageDataResp();
        memberPointsPageDataResp.setId(memberPointsDO.getId());
        memberPointsPageDataResp.setSubMemberId(memberPointsDO.getSubMemberId());
        memberPointsPageDataResp.setSubMemberName(memberPointsDO.getSubMemberName());
        memberPointsPageDataResp.setSubAccount(memberPointsDO.getSubAccount());
        memberPointsPageDataResp.setSubRoleName(memberPointsDO.getSubRoleName());
        memberPointsPageDataResp.setPoints(Optional.ofNullable(memberPointsDO.getPoints()).orElse(BigDecimal.ZERO));
        memberPointsPageDataResp.setRemark(memberPointsDO.getRemark());
        memberPointsPageDataResp.setCreateTime(DateTimeUtil.formatDateTime(memberPointsDO.getCreateTime()));
        memberPointsPageDataResp.setAccount(memberPointsDO.getAccount());

        return memberPointsPageDataResp;
    }

    @Transactional(rollbackFor =Exception.class)
    @Override
    public void batchAdd(UserLoginCacheDTO sysUser, MemberPointsBatchAddReq batchAddReq) {
        // todo 校验密码
        // 根据登用户账号
        Specification<UserDO> specification = (root, query, builder) -> {
            Predicate predicate = builder.conjunction();
            List<Expression<Boolean>> expressions = predicate.getExpressions();
            expressions.add(builder.equal(root.get("account"), sysUser.getAccount()));
            return query.where(predicate).getRestriction();
        };
        UserDO userDO = userRepository.findAll(specification).get(0);
        if (!PasswordUtil.checkPassword(userDO.getPassword(), batchAddReq.getPassword())) {
            throw new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT);
        }

        // 获取请求参数
        // 积分类型 ｜｜ 积分 ｜｜ 备注 ｜｜ 会员列表
        Integer pointsType = batchAddReq.getPointsType();
        Integer points = batchAddReq.getPoints();
        // 实际积分
        // 积分类型是 抵扣类型，积分取反， 否则取正
        BigDecimal realPoints = pointsType.equals(MemberPointsTypeEnum.DEDUCTION.getCode()) ? BigDecimal.valueOf(points).negate() : BigDecimal.valueOf(points);

        String remark = batchAddReq.getRemark();
        Set<MemberPointsAddReq> members = batchAddReq.getMembers();

        // 查询会员关系数据
        // 获取查询对应的会员关系id集合
        List<Long> memberRelationIds = members.stream().map(MemberPointsAddReq::getMemberRelationId).collect(Collectors.toList());
        // 查询数据库
        List<MemberRelationDO> memberRelationDOList = memberRelationRepository.findAllById(memberRelationIds);

        // 数据校验
        // 非空校验
        BusinessAssertUtil.notEmpty(memberRelationDOList, ResponseCodeEnum.MC_MS_MEMBER_POINTS_REQUEST_MEMBER_LIST_PARAMETER_MEMBER_INFO_NOT_EXIST);
        // 查询数量校验
        BusinessAssertUtil.isTrue(memberRelationDOList.size() == memberRelationIds.size(), ResponseCodeEnum.MC_MS_MEMBER_POINTS_REQUEST_MEMBER_LIST_PARAMETER_MEMBER_INFO_NOT_EXIST);
        // 会员关系数据上级会员、角色必须是当前登录用户的会员、角色
        BusinessAssertUtil.isTrue(memberRelationDOList.stream()
                .allMatch(memberRelationDO -> Objects.equals(memberRelationDO.getMemberId(), sysUser.getMemberId())
                        && Objects.equals(memberRelationDO.getRoleId(), sysUser.getMemberRoleId())),
                ResponseCodeEnum.MC_MS_MEMBER_POINTS_REQUEST_MEMBER_LIST_PARAMETER_MEMBER_INFO_NOT_EXIST);

        // 会员关系数据转map结构，key = id， value = value
        Map<Long, MemberRelationDO> memberRelationDOMap = memberRelationDOList.stream().collect(Collectors.toMap(MemberRelationDO::getId, Function.identity()));

        // 创建实际
        LocalDateTime now = LocalDateTime.now();
        // 封装保存数据
        List<MemberPointsDO> memberPointsDOList = members.stream()
                .map(memberPointsAddReq -> buildDO(memberPointsAddReq, memberRelationDOMap.get(memberPointsAddReq.getMemberRelationId()), sysUser, realPoints, remark, now))
                .collect(Collectors.toList());

        // 保存数据获取设置对应数据库的ID
        memberPointsRepository.saveAll(memberPointsDOList);

        // 根据会员积分类型
        // 更新会员当前登记与权益信息表
        Set<MemberLevelRightDO> memberLevelRightDOList = new HashSet<>();
        // 新增会员积分处理，添加权益历史记录， 更新会员当前登记与权益信息表
        if (pointsType.equals(MemberPointsTypeEnum.ADDITION.getCode())) {
            // 新增权益历史记录
            List<MemberRightHistoryDO> memberRightHistoryDOList = new ArrayList<>();
            // 遍历会员积分数据
            memberPointsDOList.forEach(memberPointsDO -> {
                // 获取会员关系表
                MemberRelationDO memberRelationDO = memberRelationDOMap.get(memberPointsDO.getRelationId());
                // 根据会员关系表获取会员角色当前等级与权益表
                MemberLevelRightDO memberLevelRightDO = memberRelationDO.getLevelRight();

                // 组装新增权益历史记录表数据
                MemberRightHistoryDO memberRightHistoryDO = buildMemberRightHistoryDO(memberPointsDO,memberRelationDO, realPoints, remark, now);

                memberRightHistoryDOList.add(memberRightHistoryDO);

                // 更新会员当前登记与权益信息表
                memberLevelRightDO.setSumPoint(memberLevelRightDO.getSumPoint() + points);
                memberLevelRightDO.setCurrentPoint(memberLevelRightDO.getCurrentPoint() + points);

                memberLevelRightDOList.add(memberLevelRightDO);
            });

            memberRightHistoryRepository.saveAll(memberRightHistoryDOList);

            memberLevelRightRepository.saveAll(memberLevelRightDOList);
        }
        // 减分处理，添加会员权益（积分）消费历史记录， 更新会员当前登记与权益信息表
        else if (pointsType.equals(MemberPointsTypeEnum.DEDUCTION.getCode())) {
            // 新增权益会员权益（积分）消费历史记录
            List<MemberRightSpendHistoryDO> memberRightSpendHistoryDOList = new ArrayList<>();

            // 遍历会员积分数据
            memberPointsDOList.forEach(memberPointsDO -> {
                // 获取会员关系表
                MemberRelationDO memberRelationDO = memberRelationDOMap.get(memberPointsDO.getRelationId());
                // 根据会员关系表获取会员角色当前等级与权益表
                MemberLevelRightDO memberLevelRightDO = memberRelationDO.getLevelRight();

                // 组装权益会员权益（积分）消费历史记录
                MemberRightSpendHistoryDO memberRightSpendHistoryDO = buildMemberRightSpendHistoryDO(memberPointsDO,memberRelationDO, points, remark, now);

                memberRightSpendHistoryDOList.add(memberRightSpendHistoryDO);

                // 更新会员当前登记与权益信息表
                memberLevelRightDO.setSumUsedPoint(memberLevelRightDO.getSumUsedPoint() + points);
                memberLevelRightDO.setCurrentPoint(memberLevelRightDO.getCurrentPoint() - points);
                memberLevelRightDOList.add(memberLevelRightDO);
            });

            memberRightSpendHistoryRepository.saveAll(memberRightSpendHistoryDOList);
            memberLevelRightRepository.saveAll(memberLevelRightDOList);

        }
    }

    /**
     * 构建会员积分数据
     * @param memberPointsAddReq   会员积分数据
     * @param memberRelationDO     会员关系表
     * @param sysUser              用户信息
     * @param realPoints           实际积分
     * @param remark                备注
     * @param now                  当前时间
     * @return 会员积分数据
     */
    public MemberPointsDO buildDO(MemberPointsAddReq memberPointsAddReq,
                                  MemberRelationDO memberRelationDO,
                                  UserLoginCacheDTO sysUser,
                                  BigDecimal realPoints,
                                  String remark,
                                  LocalDateTime now) {
        MemberPointsDO memberPointsDO = new MemberPointsDO();

        memberPointsDO.setPoints(realPoints);
        memberPointsDO.setRemark(remark);
        memberPointsDO.setCreateTime(now);

        memberPointsDO.setRelationId(memberPointsAddReq.getMemberRelationId());

        memberPointsDO.setMemberId(sysUser.getMemberId());
        memberPointsDO.setRoleId(sysUser.getMemberRoleId());
        memberPointsDO.setUserId(sysUser.getUserId());
        memberPointsDO.setAccount(sysUser.getAccount());

        memberPointsDO.setSubMemberName(memberPointsAddReq.getSubMemberName());

        memberPointsDO.setSubMemberId(memberRelationDO.getSubMemberId());
        memberPointsDO.setSubRoleId(memberRelationDO.getSubRoleId());
        memberPointsDO.setSubRoleName(memberRelationDO.getSubRoleName());
        memberPointsDO.setSubUserId(memberRelationDO.getSubUserId());
        memberPointsDO.setSubAccount(memberPointsAddReq.getSubAccount());

        return memberPointsDO;
    }

    /**
     * 构建权益历史记录
     * @param memberPointsDO 会员积分数据
     * @param memberRelationDO 会员关系表
     * @param realPoints 实际积分
     * @param remark 备注
     * @param now 当前时间
     * @return 会员权益历史记录
     */
    public MemberRightHistoryDO buildMemberRightHistoryDO(MemberPointsDO memberPointsDO,
                                                          MemberRelationDO memberRelationDO,
                                                          BigDecimal realPoints,
                                                          String remark,
                                                          LocalDateTime now) {
        MemberRightHistoryDO memberRightHistoryDO = new MemberRightHistoryDO();
        memberRightHistoryDO.setRelType(memberRelationDO.getRelType());
        memberRightHistoryDO.setMemberId(memberPointsDO.getMemberId());
        memberRightHistoryDO.setRoleId(memberPointsDO.getRoleId());
        memberRightHistoryDO.setSubMemberId(memberPointsDO.getSubMemberId());
        memberRightHistoryDO.setSubRoleId(memberPointsDO.getSubRoleId());
        memberRightHistoryDO.setRightTypeEnum(MemberRightTypeEnum.SCORE_POINTS_RIGHT.getCode());
        memberRightHistoryDO.setRightTypeName(MemberRightTypeEnum.SCORE_POINTS_RIGHT.getMessage());
        memberRightHistoryDO.setPoint(realPoints);
        memberRightHistoryDO.setRemark(remark);
        memberRightHistoryDO.setCreateTime(now);
        memberRightHistoryDO.setBusinessNo(memberPointsDO.getId().toString());
        memberRightHistoryDO.setMemberPointsId(memberPointsDO.getId());
        return memberRightHistoryDO;
    }

    /**
     * 构建会员权益消费历史记录
     * @param memberPointsDO 会员积分数据
     * @param memberRelationDO 会员关系表
     * @param points 消费积分
     * @param remark 备注
     * @param now 当前时间
     * @return 会员权益消费历史记录
     */
    public MemberRightSpendHistoryDO buildMemberRightSpendHistoryDO(MemberPointsDO memberPointsDO,
                                                                    MemberRelationDO memberRelationDO,
                                                                    Integer points,
                                                                    String remark,
                                                                    LocalDateTime now) {
        MemberRightSpendHistoryDO memberRightSpendHistoryDO = new MemberRightSpendHistoryDO();
        memberRightSpendHistoryDO.setCreateTime(now);
        memberRightSpendHistoryDO.setRelType(memberRelationDO.getRelType());
        memberRightSpendHistoryDO.setMemberId(memberPointsDO.getMemberId());
        memberRightSpendHistoryDO.setRoleId(memberPointsDO.getRoleId());
        memberRightSpendHistoryDO.setSubMemberId(memberPointsDO.getSubMemberId());
        memberRightSpendHistoryDO.setSubRoleId(memberPointsDO.getSubRoleId());
        memberRightSpendHistoryDO.setRightTypeEnum(MemberRightTypeEnum.SCORE_POINTS_RIGHT.getCode());
        memberRightSpendHistoryDO.setRightTypeName(MemberRightTypeEnum.SCORE_POINTS_RIGHT.getMessage());
        memberRightSpendHistoryDO.setSpendTypeEnum(MemberRightSpendTypeEnum.PLATFORM_DEDUCTION.getTypeEnum());
        memberRightSpendHistoryDO.setPoint(points);
        memberRightSpendHistoryDO.setRemark(remark);
        memberRightSpendHistoryDO.setBusinessNo(memberPointsDO.getId().toString());
        memberRightSpendHistoryDO.setMemberPointsId(memberPointsDO.getId());

        return memberRightSpendHistoryDO;
    }
}

