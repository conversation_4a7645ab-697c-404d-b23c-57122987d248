package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.MemberLevelTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.TokenUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.basic.UserRoleDO;
import com.ssy.lingxi.member.enums.MemberOuterStatusEnum;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.model.req.basic.MemberIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.basic.UpperMemberIdRoleIdReq;
import com.ssy.lingxi.member.model.req.info.*;
import com.ssy.lingxi.member.model.req.maintenance.MemberDetailCreditHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberUserIdListReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberUserIdReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.UpperMemberShowResp;
import com.ssy.lingxi.member.model.resp.info.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordRectifyResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberInfoService;
import com.ssy.lingxi.member.service.base.IBaseMemberInnerService;
import com.ssy.lingxi.member.service.base.IBaseSiteService;
import com.ssy.lingxi.member.service.web.IMemberAbilityInfoService;
import com.ssy.lingxi.member.service.web.IPlatformMemberRoleRuleService;
import com.ssy.lingxi.member.serviceImpl.base.BaseLoginServiceImpl;
import com.ssy.lingxi.member.util.MemberOrganizationUtil;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.ssy.lingxi.member.serviceImpl.base.BaseLoginServiceImpl.safeGetOrgId;

/**
 * 会员信息管理查询服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-05
 */
@Service
public class MemberAbilityInfoServiceImpl implements IMemberAbilityInfoService {

    private final static Logger logger = LoggerFactory.getLogger(MemberAbilityInfoServiceImpl.class);

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseMemberInfoService baseMemberInfoService;

    @Resource
    private IBaseMemberInnerService baseMemberInnerService;

    @Resource
    private IBaseSiteService siteService;

    @Resource
    private IPlatformMemberRoleRuleService roleRuleService;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private MemberRelationRepository memberRelationRepository;

    /**
     * 获取分页查询页面下拉框内容
     *
     * @param headers Http头部信息
     * @return 下拉框内容
     */
    @Override
    public MemberInfoSearchConditionResp getPageSearchConditions(HttpHeaders headers) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getPageSearchConditions();
    }

    /**
     * 分页、模糊查询归属会员列表
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public PageDataResp<UpperMemberInfoResp> pageUpperMembers(HttpHeaders headers, MemberInfoPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Boolean wrapperResp = siteService.isEnableMultiTenancy(headers);
        return baseMemberInfoService.pageUpperMembers(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getName(), pageVO.getStartDate(),pageVO.getEndDate(),pageVO.getOuterStatus(), pageVO.getCurrent(), pageVO.getPageSize(), wrapperResp,pageVO.getCategoryId(),pageVO.getCurrencyType(),pageVO.getCode(),null, roleTag);
    }

    /**
     * “申请会员”页面，查询按钮状态和文本
     *
     * @param headers Http头部信息
     * @param conditionVO    接口参数
     * @return 查询结果
     */
    @Override
    public MemberInfoApplyButtonResp getApplyCondition(HttpHeaders headers, MemberApplyConditionReq conditionVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getApplyCondition(loginUser, conditionVO.getShopType(), conditionVO.getUpperMemberId(), conditionVO.getUpperRoleId());
    }

    /**
     * “邀请会员”页面，查询按钮状态和文本
     *
     * @param headers     Http头部信息
     * @param conditionVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberInfoInviteButtonResp getInviteCondition(HttpHeaders headers, MemberInviteConditionDataReq conditionVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getInviteCondition(loginUser, conditionVO.getShopType(), conditionVO.getSubMemberId(), conditionVO.getSubRoleId());
    }

    /**
     * “申请会员”页面，会员注册资料信息
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public MemberInfoApplyRegisterDetailResp getApplyRegisterDetail(HttpHeaders headers, UpperMemberIdRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getApplyRegisterDetail(loginUser, idVO.getUpperMemberId(), idVO.getUpperRoleId(), roleTag);
    }

    /**
     * “申请会员”页面，会员入库资料信息
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public MemberInfoApplyDepositDetailResp getApplyDepositDetail(HttpHeaders headers, UpperMemberIdRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getApplyDepositDetail(loginUser, idVO.getUpperMemberId(), idVO.getUpperRoleId());
    }

    /**
     * “申请会员” - 提交
     *
     * @param headers Http头部信息
     * @param subVO   接口参数
     */
    @Override
    public void applyToBeSubMember(HttpHeaders headers, MemberInfoApplyForSubReq subVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberInnerService.applyToBeSubMember(subVO.getUpperMemberId(), subVO.getUpperRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId(), subVO.getDepositDetails(), subVO.getQualities());
    }

    /**
     * 获取“修改会员信息”页面，会员注册资料信息
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberInfoUpdateDetailResp getMemberRegisterDetail(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberRegisterDetail(loginUser, idVO.getValidateId(), roleTag);
    }

    /**
     * 修改会员注册信息
     * @param headers  Http头部信息
     * @param detailVO 接口参数
     */
    @Transactional
    @Override
    public void updateMemberRegisterDetail(HttpHeaders headers, MemberInfoUpdateRegisterDetailReq detailVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberInfoService.updateMemberRegisterDetail(loginUser, detailVO);
    }

    /**
     * 获取“修改入库信息”页面，会员入库资料信息
     *
     * @param headers Http头部信息
     * @param idVO    会员关系Id
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberInfoUpdateDepositDetailResp getMemberDepositDetail(HttpHeaders headers, ValidateIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberDepositDetail(loginUser, idVO.getValidateId(), roleTag);
    }

    /**
     * 修改会员入库信息
     *
     * @param headers  Http头部信息
     * @param detailVO 接口参数
     * @param roleTag 角色标签
     */
    @Override
    public void updateMemberDepositDetail(HttpHeaders headers, MemberInfoUpdateDepositDetailReq detailVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberInfoService.updateMemberDepositDetail(loginUser, detailVO, roleTag);
    }

    /**
     * 会员详情 - 会员基本信息
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberInfoBasicDetailResp getMemberBasicDetail(HttpHeaders headers, ValidateIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberBasicDetail(loginUser, idVO.getValidateId(), roleTag);
    }

    /**
     * 会员详情 - 会员档案信息
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public MemberInfoDepositDetailResp getMemberArchives(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberArchives(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情- 会员档案 - 分页查询考评信息
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberAppraisalPageQueryResp> pageMemberAppraisal(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.pageMemberAppraisal(loginUser, pageVO);
    }

    /**
     * 会员详情 - 会员档案 - 分页查询整改信息
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberRecordRectifyResp> pageMemberRecordRectify(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.pageMemberRecordRectify(loginUser, pageVO);
    }

    /**
     * 会员详情 - 会员等级信息
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberValidateDetailLevelResp getMemberDetailLevel(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberDetailLevel(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.pageMemberLevelDetailHistory(loginUser, pageVO, MemberConstant.DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 会员详情 - 会员权益信息
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailRightResp getMemberDetailRight(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberDetailRight(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益获取记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.pageMemberDetailRightHistory(loginUser, pageVO, MemberConstant.DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益使用记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.pageMemberDetailRightSpendHistory(loginUser, pageVO, MemberConstant.DEFAULT_DATETIME_FORMATTER);
    }

    /**
     * 会员详情 - 会员信用信息
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditResp getMemberDetailCredit(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberDetailCredit(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberDetailCreditTradeCommentSummary(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.pageMemberDetailCreditTradeCommentHistory(loginUser, pageVO);
    }

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberDetailCreditAfterSaleCommentSummary(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.pageMemberDetailCreditAfterSaleCommentHistory(loginUser, pageVO);
    }

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     *
     * @param headers    Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(HttpHeaders headers, ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberDetailCreditComplainSummary(loginUser, idVO.getValidateId());
    }

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberCreditComplaintPageQueryResp> pageMemberDetailCreditComplainHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.pageMemberDetailCreditComplainHistory(loginUser, pageVO);
    }

    /**
     * “增加会员角色”功能，查询上级会员列表
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public UpperMemberShowResp getUpperMemberInfo(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Boolean wrapperResp = siteService.isEnableMultiTenancy(headers);
        return baseMemberInfoService.getUpperMemberInfo(loginUser.getMemberId(), loginUser.getMemberRoleId(), wrapperResp);
    }

    /**
     * “增加会员角色”功能，查询会员类型列表
     *
     * @param headers  Http头部信息
     * @param memberId 会员id
     * @return 查询结果
     */
    @Override
    public List<MemberTypeAndNameResp> getMemberTypeList(HttpHeaders headers, Long memberId) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        if (memberId == null) {
            Boolean wrapperResp = siteService.isEnableMultiTenancy(headers);
            if (!wrapperResp) {//如果没有开启saas多租户部署
                return baseMemberInfoService.getMemberTypeList(loginUser.getMemberType());
            } else {//如果开启saas多租户部署
                //按照指定会员id取当前会员配置的适用类型
                return baseMemberInfoService.getMemberTypeList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.memberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
            }
        }
        //如果有传会员id，则按照指定会员id取适用类型
        return baseMemberInfoService.getMemberTypeList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.subMemberRoles(memberId)).orElse(new ArrayList<>()));
    }

    /**
     * “增加会员角色”功能，根据会员类型Id查询角色列表，以及勾选的角色Id列表
     *
     * @param headers Http头部信息
     * @param memberTypeReq    接口参数
     * @return 查询结果
     */
    @Override
    public MemberInfoRoleListResp getRoleListByMemberType(HttpHeaders headers, MemberTypeReq memberTypeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        if (memberTypeReq.getMemberId() == null) {
            Boolean wrapperResp = siteService.isEnableMultiTenancy(headers);
            if (!wrapperResp) {//如果没有开启saas多租户部署
                return baseMemberInfoService.getRoleListByMemberType(loginUser.getMemberId(), memberTypeReq);
            } else {//如果开启saas多租户部署
                //取当前会员配置的适用角色
                return baseMemberInfoService.getRoleListByMemberType(loginUser.getMemberId(), memberTypeReq, Optional.ofNullable(roleRuleService.memberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
            }
        }
        //如果有传会员id，则按照指定会员id取适用角色
        return baseMemberInfoService.getRoleListByMemberType(loginUser.getMemberId(), memberTypeReq, Optional.ofNullable(roleRuleService.subMemberRoles(memberTypeReq.getMemberId())).orElse(new ArrayList<>()));
    }

    /**
     * “增加会员角色”功能，会获取员注册资料信息
     *
     * @param headers Http头部信息
     * @param roleIdReq 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public MemberInfoUpdateDetailByRoleResp getMemberRegisterDetailAfterAddRole(HttpHeaders headers, RoleIdReq roleIdReq, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberInfoService.getMemberRegisterDetailAfterAddRole(loginUser.getMemberId(), loginUser.getMemberRoleId(), roleIdReq, roleTag);
    }

    /**
     * “增加会员角色”功能，提交注册资料并新增角色
     *
     * @param headers   Http头部信息
     * @param addRoleVO 接口参数
     */
    @Override
    public void addMemberRole(HttpHeaders headers, MemberInfoAddRoleReq addRoleVO) {
        logger.info("===addMemberRole==={}", SerializeUtil.serialize(addRoleVO));
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberInfoService.addMemberRole(loginUser.getMemberId(), loginUser.getMemberRoleId(), addRoleVO, loginUser);
    }

    /**
     * 根据会员id查询下属用户的im权限
     *
     * @param memberIdReq 请求参数
     * @return 拥有im权限的用户id列表
     */
    @Override
    public List<MemberImAuthUsersResp> getHasImAuthUsers(MemberIdReq memberIdReq) {
        MemberDO memberDO = memberRepository.findById(memberIdReq.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST));
        return memberDO.getUserRoles()
                .stream()
                .filter(userRoleDO -> EnableDisableStatusEnum.ENABLE.getCode().equals(userRoleDO.getHasImAuth()))
                .map(UserRoleDO::getUsers)
                .flatMap(Collection::stream)
                .map(userDO -> new MemberImAuthUsersResp(userDO.getId(), memberDO.getName(), userDO.getName()))
                .distinct()
                .sorted(Comparator.comparing(MemberImAuthUsersResp::getUserId))
                .collect(Collectors.toList());
    }

    @Override
    public List<MemberImAuthUsersResp> getPlatformImUsers() {
        List<UserDO> userDOS = userRepository.findHasImAuthPlatfromUser();
        return userDOS.stream()
                .map(userDO -> new MemberImAuthUsersResp(userDO.getId(), userDO.getMember().getName(), userDO.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public Boolean checkPlatformImUser(MemberUserIdReq userIdReq) {
        return userRepository.findPlatfromImUser(userIdReq.getUserId()) != null;
    }

    @Override
    public Boolean checkHasImAuth(MemberUserIdReq userIdReq) {
        //从数据库中读取用户信息
        UserDO userDO = userRepository.findById(userIdReq.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT));

        return userDO.getRoles().stream().map(UserRoleDO::getHasImAuth).anyMatch(hasImAuth -> hasImAuth.equals(EnableDisableStatusEnum.ENABLE.getCode()));
    }

    @Override
    public List<MemberUserImAuthInfoResp> getImAuthInfo(MemberUserIdListReq userIdListReq) {
        return userRepository.findAllById(userIdListReq.getUserIdList())
                .stream()
                .map(userDO -> {
                    MemberDO member = userDO.getMember();
                    // 查询登陆会员的平台关系
                    List<MemberRelationDO> allPlatformRelDOList = memberRelationRepository.findBySubMemberIdAndRelType(member.getId(), MemberRelationTypeEnum.PLATFORM.getCode());
                    List<MemberRelationDO> availableRelDOList = allPlatformRelDOList.stream()
                            .filter(relationDO -> relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode()))
                            .sorted(Comparator.comparingInt(MemberRelationDO::getVerified).reversed().thenComparingLong(MemberRelationDO::getSubRoleId))
                            .collect(Collectors.toList());
                    List<BaseLoginServiceImpl.MemberRoleItem> memberRoleItems = getMemberRoleItems(availableRelDOList);
                    List<Integer> roleTypes = memberRoleItems.stream().map(BaseLoginServiceImpl.MemberRoleItem::getRoleType).collect(Collectors.toList());
                    return new MemberUserImAuthInfoResp(member.getId(), member.getName(), userDO.getId(), userDO.getName(), roleTypes);
                })
                .collect(Collectors.toList());
    }

    @Override
    public MemberLoginInfoResp getLoginInfoByToken(HttpHeaders headers) {
        // 校验请求头中的token
        String accessToken = headers.getFirst(Constant.ACCESS_TOKEN);
        BusinessAssertUtil.notBlank(accessToken, ResponseCodeEnum.TOKEN_EXPIRE);

        // 从redis中查询登陆用户信息
        String tokenStr = redisUtils.stringGet(TokenUtil.generateAccessTokenRedisKey(accessToken), RedisConstant.REDIS_USER_INDEX);
        BusinessAssertUtil.notBlank(tokenStr, ResponseCodeEnum.TOKEN_EXPIRE);
        UserLoginCacheDTO loginCacheDTO = JsonUtil.toObj(tokenStr, UserLoginCacheDTO.class);
        BusinessAssertUtil.notNull(loginCacheDTO, ResponseCodeEnum.TOKEN_EXPIRE);

        // 平台后台不能使用
        BusinessAssertUtil.isFalse(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode().equals(loginCacheDTO.getLoginSource()), ResponseCodeEnum.NEED_LOGIN_FROM_BUSINESS_CENTER);

        // 校验用户是否存在
        UserDO userDO = userRepository.findById(loginCacheDTO.getUserId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.USER_ACCOUNT_OR_PASSWORD_INCORRECT));
        BusinessAssertUtil.isTrue(EnableDisableStatusEnum.ENABLE.getCode().equals(userDO.getStatus()), ResponseCodeEnum.USER_ACCOUNT_HAS_BEEN_FROZEN);

        // 校验会员是否存在
        MemberDO memberDO = userDO.getMember();
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);

        // 查询平台关系
        MemberRelationDO relDO = memberRelationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelTypeAndVerified(loginCacheDTO.getMemberId(), loginCacheDTO.getMemberRoleId(), MemberRelationTypeEnum.PLATFORM.getCode(), MemberValidateStatusEnum.VERIFY_PASSED.getCode());
        BusinessAssertUtil.notNull(memberDO, ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);

        // 返回会员基本信息
        MemberLoginInfoResp memberLoginInfoResp = new MemberLoginInfoResp();

        //************************************** 会员相关信息 **************************************
        memberLoginInfoResp.setMemberId(memberDO.getId());
        memberLoginInfoResp.setMemberName(SecurityStringUtil.getOrDefault(memberDO.getName()));
        memberLoginInfoResp.setMemberRoleId(relDO.getSubRole().getId());
        memberLoginInfoResp.setMemberRoleName(SecurityStringUtil.getOrDefault(relDO.getSubRole().getRoleName()));
        memberLoginInfoResp.setMemberRoleType(relDO.getSubRole().getRoleType());
        memberLoginInfoResp.setMemberType(relDO.getSubRole().getMemberType());
        memberLoginInfoResp.setRoleTag(relDO.getSubRole().getRoleTag());
        memberLoginInfoResp.setMemberLevelType(MemberLevelTypeEnum.MERCHANT.getCode());
        memberLoginInfoResp.setLevel(relDO.getLevelRight().getLevel());
        memberLoginInfoResp.setLevelTag(relDO.getLevelRight().getLevelTag());
        memberLoginInfoResp.setScore(relDO.getLevelRight().getScore());
        memberLoginInfoResp.setCreditPoint(relDO.getCredit().getCreditPoint());
        memberLoginInfoResp.setVerified(relDO.getVerified());
        memberLoginInfoResp.setValidateStatus(relDO.getOuterStatus());
        memberLoginInfoResp.setValidateStatusDesc(MemberOuterStatusEnum.getCodeMsg(relDO.getOuterStatus()));
        memberLoginInfoResp.setValidateMsg(SecurityStringUtil.getOrDefault(relDO.getValidateMsg()));
        memberLoginInfoResp.setOuterStatus(relDO.getOuterStatus());
        memberLoginInfoResp.setRelId(relDO.getId());

        //************************************** 用户相关信息 **************************************
        memberLoginInfoResp.setUserId(userDO.getId());
        memberLoginInfoResp.setAccount(SecurityStringUtil.getOrDefault(userDO.getAccount()));
        memberLoginInfoResp.setUserName(SecurityStringUtil.getOrDefault(userDO.getName()));
        memberLoginInfoResp.setIdCardNo(SecurityStringUtil.getOrDefault(userDO.getIdCardNo()));
        memberLoginInfoResp.setTelCode(userDO.getTelCode());
        memberLoginInfoResp.setPhone(SecurityStringUtil.getOrDefault(userDO.getPhone()));
        memberLoginInfoResp.setEmail(SecurityStringUtil.getOrDefault(userDO.getEmail()));
        memberLoginInfoResp.setJobTitle(SecurityStringUtil.getOrDefault(userDO.getJobTitle()));
        memberLoginInfoResp.setOrgId(safeGetOrgId(userDO));
        memberLoginInfoResp.setOrgName(MemberOrganizationUtil.joinTitleToString(safeGetOrgId(userDO), new ArrayList<>(memberDO.getOrgs())));
        memberLoginInfoResp.setUserType(userDO.getUserType());
        memberLoginInfoResp.setLogo(SecurityStringUtil.getOrDefault(userDO.getLogo()));
        memberLoginInfoResp.setImFlag(userDO.getRoles().stream().map(UserRoleDO::getHasImAuth).anyMatch(imAuth -> EnableDisableStatusEnum.ENABLE.getCode().equals(imAuth)));

        return memberLoginInfoResp;
    }

    private static List<BaseLoginServiceImpl.MemberRoleItem> getMemberRoleItems(List<MemberRelationDO> availableRelDOList) {
        // 平台关系是未冻结 且 会员角色状态为启用
        return availableRelDOList.stream()
                .map(MemberRelationDO::getSubRole)
                .filter(memberRoleDO -> EnableDisableStatusEnum.ENABLE.getCode().equals(memberRoleDO.getStatus()))
                .map(r -> new BaseLoginServiceImpl.MemberRoleItem(r.getId(), r.getRoleName(), r.getRoleType()))
                .collect(Collectors.toList());
    }
}
