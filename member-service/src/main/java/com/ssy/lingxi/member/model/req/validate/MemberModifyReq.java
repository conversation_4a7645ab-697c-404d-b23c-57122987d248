package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.member.handler.annotation.MemberValidateAgreeAnnotation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 会员能力 - 会员变更审核接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-19
 */
@Data
public class MemberModifyReq implements Serializable {
    private static final long serialVersionUID = 8537308155988896356L;

    /**
     * 审核内容Id
     */
    @NotNull(message = "审核内容Id要大于0")
    @Positive(message = "审核内容Id要大于0")
    private Long validateId;

    /**
     * 提交审批的状态：0-不同意；1-同意
     */
    @MemberValidateAgreeAnnotation(message = "审批状态不能为空：0-不通过 1-通过")
    private Integer agree;

    /**
     * 审核原因
     */
    @Size(max = 120, message = "不通过原因最大120个字符")
    private String reason;
}
