package com.ssy.lingxi.member.controller.web.supplier;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityAssignedMemberQueryDataReq;
import com.ssy.lingxi.member.model.resp.maintenance.MemberAssignedPageQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberAssignedSearchConditionVO;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilityAssignedService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商能力-会员管理-待分配会员相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-29
 **/
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/supplier/ability/assigned")
public class SupplierAbilityAssignedController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.SUPPLIER.getCode();

    @Resource
    private IMemberAbilityAssignedService memberAbilityAssignedService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    /**
     * 列表查询页面中各个下拉选择框的内容
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/pageitems")
    public WrapperResp<MemberAssignedSearchConditionVO> getPageCondition(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityAssignedService.getPageCondition(headers, loginUser, roleTag));
    }

    /**
     * 分页、模糊查询会员
     *
     * @param headers Http头部信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberAssignedPageQueryResp>> pageMembers(@RequestHeader HttpHeaders headers, @Valid MemberAbilityAssignedMemberQueryDataReq queryVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityAssignedService.pageMembers(loginUser, queryVO, roleTag));
    }

    /**
     * 操作用户领取会员
     *
     * @param headers Http头部信息
     * @param subMemberList  接口参数
     * @return 操作结果
     */
    @PostMapping("/bind")
    public WrapperResp<Void> pageMembers(@RequestHeader HttpHeaders headers, @RequestBody @Valid List<SubMemberIdRoleIdDataReq> subMemberList) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityAssignedService.bindOperator(loginUser, subMemberList);
        return WrapperUtil.success();
    }
}
