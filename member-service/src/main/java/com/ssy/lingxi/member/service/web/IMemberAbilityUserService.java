package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.api.model.req.ListQueryMemberUserReq;
import com.ssy.lingxi.member.api.model.resp.MemberUserFeignResp;
import com.ssy.lingxi.member.model.dto.MemberUserDTO;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserQueryResp;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 会员能力 - 会员下属用户服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-29
 */
public interface IMemberAbilityUserService {

    /**
     * 新增用户
     * @param headers HttpHeaders信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void addMemberUser(HttpHeaders headers, MemberUserAddReq addVO);

    /**
     * 查询用户信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    MemberUserGetResp getMemberUser(HttpHeaders headers, MemberUserIdReq idVO);

    /**
     * 更新用户
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateMemberUser(HttpHeaders headers, MemberUserUpdateReq updateVO);

    /**
     * 删除用户
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    void deleteMemberUser(HttpHeaders headers, MemberUserIdReq idVO);

    /**
     * 更改用户状态
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    void updateMemberUserStatus(HttpHeaders headers, MemberUserUpdateStatusReq statusVO);

    /**
     * 分页查询用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberUserQueryResp> pageMemberUser(HttpHeaders headers, PageQueryMemberUserDataReq pageVO);

    /**
     * 8D整改- 分页查询会员下的用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberUserQueryResp> eightMemberUser(HttpHeaders headers, PageQueryMemberUserDataReq pageVO);

    /**
     * 查询用户信息
     * @param userId 用户Id
     * @return 用户信息DTO
     */
    MemberUserDTO findMemberUser(Long userId);
    /**
     * 分页查询会员用户（业务员）
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    List<MemberUserQueryResp> pageMemberSalesMan(@RequestHeader HttpHeaders headers);

    /**
     * 根据角色id分页查询会员用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberUserQueryResp> pageMemberUserByRoleId(HttpHeaders headers, PageQueryMemberUserDataReq pageVO);

    /**
     * 查询会员用户
     *
     * @return 会员用户
     */
    List<MemberUserFeignResp> listMemberUser(ListQueryMemberUserReq listQueryMemberUserReq);

    /**
     * 注册腾讯IM用户
     *
     * @param userId
     * @return
     */
    WrapperResp<Void> registerTencentIM(Long userId);

    /**
     * 根据用户ID批量查询用户信息
     * @param idListReq 参数
     * @return 结果
     */
    List<MemberUserFeignResp> getListByIds(CommonIdListReq idListReq);
}
