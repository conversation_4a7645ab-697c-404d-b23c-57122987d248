package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.ipMonitor.anno.LoginIpMonitor;
import com.ssy.lingxi.component.ipMonitor.anno.SmsCodeIpMonitor;
import com.ssy.lingxi.member.entity.bo.login.ManageLoginBO;
import com.ssy.lingxi.member.entity.bo.login.WebLoginBO;
import com.ssy.lingxi.member.model.req.login.*;
import com.ssy.lingxi.member.model.resp.login.*;
import com.ssy.lingxi.member.service.web.IMemberLoginService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 业务中台、平台后台 -- 用户登录接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-05
 */
@RequiredArgsConstructor
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX)
public class MemberLoginController {
    private final IMemberLoginService loginService;

    //****************************************************** 通用 ******************************************************

    /**
     * 多主体检测
     *
     * @param headers          HttpHeaders信息
     * @param multiAccCheckReq 接口参数
     * @return 检测结果
     */
    @LoginIpMonitor
    @PostMapping("/multiAccCheck")
    public WrapperResp<List<MultiAccCheckResp>> multiAccCheck(@RequestHeader HttpHeaders headers, @RequestBody @Valid MultiAccCheckReq multiAccCheckReq) {
        return WrapperUtil.success(loginService.multiAccCheck(headers, multiAccCheckReq));
    }

    /**
     * 安全检测（账号密码模式）
     *
     * @param headers          HttpHeaders信息
     * @param securityCheckReq 接口参数
     * @return 登录结果
     */
    @LoginIpMonitor
    @PostMapping("/securityCheck")
    public WrapperResp<LoginSecurityCheckResp> securityCheck(@RequestHeader HttpHeaders headers, @RequestBody @Valid LoginSecurityCheckReq securityCheckReq) {
        return WrapperUtil.success(loginService.accountSecurityCheck(headers, securityCheckReq));
    }

    /**
     * 登出
     *
     * @param headers HttpHeaders信息
     * @return 登出结果
     */
    @GetMapping("/logOut")
    public WrapperResp<Void> logOut(@RequestHeader HttpHeaders headers) {
        loginService.logOut(headers);
        return WrapperUtil.success();
    }

    /**
     * 获取权限树
     *
     * @param headers HttpHeaders信息
     * @return 权限树
     */
    @GetMapping("/authTree")
    public WrapperResp<List<LoginAuthResp>> getAuthTree(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(loginService.getAuthTree(headers));
    }

    /**
     * toke续期
     *
     * @param headers HttpHeaders信息
     * @return 续期信息
     */
    @GetMapping("/refreshToken")
    public WrapperResp<?> refreshToken(@RequestHeader HttpHeaders headers) {
        return loginService.refreshToken(headers);
    }

    //****************************************************** 业务平台 ******************************************************

    /**
     * 业务平台 - 登陆时发送短信验证码
     *
     * @param headers  Http头部信息
     * @param phoneReq 接口参数
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @PostMapping("/login/phoneSms")
    public WrapperResp<Void> sendPhoneLoginSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneLoginSmsCode phoneReq) {
        loginService.sendPhoneLoginSmsCode(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 业务平台 - 登陆时发送邮箱验证码
     *
     * @param headers  Http头部信息
     * @param emailReq 接口参数
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @PostMapping("/login/emailSms")
    public WrapperResp<Void> sendEmailLoginSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailLoginSmsCode emailReq) {
        loginService.sendEmailLoginSmsCode(headers, emailReq);
        return WrapperUtil.success();
    }

    /**
     * 业务平台 - 账号或手机号登录
     *
     * @param headers  HttpHeaders信息
     * @param loginReq 接口参数
     * @return 登录结果
     */
    @RequestMapping("/login")
    @LoginIpMonitor
    public WrapperResp<WebLoginBO> accountOrPhoneLogin(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLoginReq loginReq) {
        return WrapperUtil.success(loginService.accountOrPhoneLogin(headers, loginReq));
    }

    /**
     * 业务平台 - 切换登录用户的会员角色
     *
     * @param headers HttpHeaders信息
     * @param roleReq 接口参数
     * @return 操作结果
     */
    @RequestMapping("/login/switchrole")
    public WrapperResp<WebLoginBO> switchMemberRole(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLoginSwitchMemberRoleReq roleReq) {
        return WrapperUtil.success(loginService.switchMemberRole(headers, roleReq));
    }

    /**
     * 业务平台 - 获取登录授权码
     *
     * @param headers HttpHeaders信息
     * @return 授权码
     */
    @GetMapping("/login/authCode")
    public WrapperResp<MemberAuthCodeResp> getLoginAuthCode(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(loginService.getLoginAuthCode(headers));
    }

    /**
     * 业务平台 - 验证扫码登录(轮询)
     *
     * @param headers           HttpHeaders信息
     * @param memberAuthCodeReq 接口参数
     * @return 返回结果
     */
    @PostMapping("/loginInfo")
    public WrapperResp<WebLoginBO> loginInfo(@RequestHeader HttpHeaders headers, @RequestBody @Validated ActivityMemberAuthCodeReq memberAuthCodeReq) {
        return WrapperUtil.success(loginService.getAuthCodeLoginInfo(headers, memberAuthCodeReq));
    }

    /**
     * 获取滑块验证码
     *
     * @param headers    HttpHeaders信息
     * @param captchaReq 接口参数
     * @return 验证码信息
     */
    @GetMapping("/captcha")
    public WrapperResp<CaptchaPicResp> captcha(@RequestHeader HttpHeaders headers, @Valid CaptchaReq captchaReq) {
        return WrapperUtil.success(loginService.captcha(headers, captchaReq));
    }

    /**
     * 发送手机号绑定的短信验证码
     *
     * @param req 请求参数
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @RequestMapping(value = "/sendPhoneBindSmsCode", method = RequestMethod.POST)
    public WrapperResp<Void> sendPhoneBindSmsCode(@RequestBody @Valid PhoneBindSmsCodeReq req) {
        loginService.sendPhoneBindSmsCode(req);
        return WrapperUtil.success();
    }

    /**
     * 绑定手机号
     *
     * @param req 请求参数
     * @return 绑定结果
     */
    @RequestMapping(value = "/bindPhone", method = RequestMethod.POST)
    public WrapperResp<Void> bindPhone(@RequestBody @Valid PhoneBindReq req) {
        loginService.bindPhone(req);
        return WrapperUtil.success();
    }

    //****************************************************** 平台后台 ******************************************************

    /**
     * 平台后台 - 登陆时发送短信验证码
     *
     * @param headers  Http头部信息
     * @param phoneReq 接口参数
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @PostMapping("/manage/phoneSms")
    public WrapperResp<Void> sendManageLoginPhoneSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneLoginSmsCode phoneReq) {
        loginService.sendManageLoginPhoneSmsCode(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 平台后台 - 登陆时发送邮箱验证码
     *
     * @param headers  Http头部信息
     * @param emailReq 接口参数
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @PostMapping("/manage/emailSms")
    public WrapperResp<Void> sendManageLoginEmailSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailLoginSmsCode emailReq) {
        loginService.sendManageLoginEmailSmsCode(headers, emailReq);
        return WrapperUtil.success();
    }

    /**
     * 平台后台 - 账号登录
     *
     * @param headers  HttpHeaders信息
     * @param loginReq 接口参数
     * @return 登录结果
     */
    @LoginIpMonitor
    @PostMapping("/manage/login")
    public WrapperResp<ManageLoginBO> manageAccountLogin(@RequestHeader HttpHeaders headers, @RequestBody @Valid ManageLoginReq loginReq) {
        return WrapperUtil.success(loginService.manageAccountLogin(headers, loginReq));
    }

    /**
     * 是否具有支付及查看订单价格权限
     *
     * @param headers HttpHeaders信息
     * @retrurn 是否具有权限
     */
    @GetMapping("/manage/hasPayAuth")
    public WrapperResp<Boolean> hasPayAuth(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(loginService.hasPayAndOrderPriceAuth(headers));
    }
}
