package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.ipMonitor.anno.LoginIpMonitor;
import com.ssy.lingxi.component.ipMonitor.anno.SmsCodeIpMonitor;
import com.ssy.lingxi.member.entity.bo.login.MobileLoginBO;
import com.ssy.lingxi.member.model.dto.MobileLoginDTO;
import com.ssy.lingxi.member.model.dto.MobilePhoneLoginDTO;
import com.ssy.lingxi.member.model.req.login.*;
import com.ssy.lingxi.member.model.req.mobile.MobileMemberAuthCodeReq;
import com.ssy.lingxi.member.model.req.mobile.MobileWxLoginReq;
import com.ssy.lingxi.member.service.mobile.IMobileLoginService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * App - 用户登录相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-03
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile")
public class MobileLoginController {
    @Resource
    private IMobileLoginService mobileLoginService;

    /**
     * 账号或手机号登录
     * @param headers Http头部信息
     * @param loginReq 接口参数
     * @return 登录用户信息
     */
    @LoginIpMonitor
    @PostMapping("/login")
    public WrapperResp<MobileLoginBO> accountOrPhoneLogin(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobileLoginReq loginReq) {
        return WrapperUtil.success(mobileLoginService.accountOrPhoneLogin(headers, new MobileLoginDTO(loginReq, false)));
    }

    /**
     * 发送“手机号登录”的短信验证码
     * @param headers Http头部信息
     * @param phoneReq 接口参数
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @PostMapping("/login/sendSms")
    public WrapperResp<Void> sendPhoneLoginSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneLoginSmsCode phoneReq) {
        mobileLoginService.sendPhoneLoginSmsCode(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 验证码登录
     * @param headers Http头部信息
     * @param loginReq 接口参数
     * @return 登录用户信息
     */
    @LoginIpMonitor
    @PostMapping("/login/phone")
    public WrapperResp<MobileLoginBO> phoneLogin(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobilePhoneLoginReq loginReq) {
        return WrapperUtil.success(mobileLoginService.phoneLogin(headers, new MobilePhoneLoginDTO(loginReq, false)));
    }

    /**
     * 根据token，重新获取登录用户信息
     * @param headers Http头部信息
     * @return 登录用户信息
     */
    @GetMapping("/login/reget")
    public WrapperResp<MobileLoginBO> loginWithToken(@RequestHeader HttpHeaders headers, @Valid MobileShopTypeReq shopTypeReq) {
        return WrapperUtil.success(mobileLoginService.loginWithToken(headers, shopTypeReq));
    }

    /**
     * 会员登录后，选择角色
     * @param headers Http头部信息
     * @param roleReq 接口参数
     * @return 登录用户信息
     */
    @PostMapping("/login/role/switch")
    public WrapperResp<MobileLoginBO> switchLoginRole(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobileSwitchRoleReq roleReq) {
        return WrapperUtil.success(mobileLoginService.switchLoginRole(headers, roleReq));
    }

    /**
     * 业务平台 - 授权码确认登录
     * @param headers HttpHeaders信息
     * @param authCodeReq 接口参数
     * @return 登录结果
     */
    @PostMapping("/authCode/active")
    public WrapperResp<Void> authCodeActive(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobileMemberAuthCodeReq authCodeReq) {
        mobileLoginService.authCodeActive(headers, authCodeReq);
        return WrapperUtil.success();
    }

    /**
     * 微信登录
     *
     * @param headers Http头部信息
     * @param req     接口参数
     * @return 登录用户信息
     */
    @LoginIpMonitor
    @RequestMapping(value = "/wxLogin", method = RequestMethod.POST)
    public WrapperResp<MobileLoginBO> wxLogin(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobileWxLoginReq req) {
        return WrapperUtil.success(mobileLoginService.wxLogin(headers, req));
    }

}
