package com.ssy.lingxi.member.controller.feign;

import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberLifeCycleStageRuleFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignWithLifeCycleRuleReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleRuleCheckReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleStagesRuleCheckBatchFeignReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleStagesRuleCheckFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLifeCycleRuleResp;
import com.ssy.lingxi.member.service.feign.IMemberLifeCycleStageRuleFeignService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员生命周期规则配置内部Feign服务接口
 * <AUTHOR>
 * @since 2022/6/30 9:35
 * @ignore 不需要提交到Yapi
 */
@RestController
public class MemberLifeCycleStageRuleFeignController implements IMemberLifeCycleStageRuleFeign {

    @Resource
    private IMemberLifeCycleStageRuleFeignService memberLifeCycleStageRuleFeignService;

    /**
     * 校验是否有配置会员某生命周期阶段状态,没有配置生命周期做限制
     *
     * @param memberLifeCycleStagesRuleCheckVO 接口参数
     * @return 返回
     */
    @Override
    public WrapperResp<Boolean> checkMemberLifeCycleStagesRule(@RequestBody @Valid MemberLifeCycleStagesRuleCheckFeignReq memberLifeCycleStagesRuleCheckVO) {
        return WrapperUtil.success(memberLifeCycleStageRuleFeignService.checkMemberLifeCycleStagesRule(memberLifeCycleStagesRuleCheckVO));
    }

    /**
     * 校验是否有配置会员某生命周期阶段状态,没有配置生命周期不做限制
     *
     * @param memberLifeCycleStagesRuleCheckVO 接口参数
     * @return 返回
     */
    @Override
    public WrapperResp<Boolean> checkMemberLifeCycleStagesRuleOrNotAstrict(@RequestBody @Valid MemberLifeCycleStagesRuleCheckFeignReq memberLifeCycleStagesRuleCheckVO) {
        return WrapperUtil.success(memberLifeCycleStageRuleFeignService.checkMemberLifeCycleStagesRuleOrNotAstrict(memberLifeCycleStagesRuleCheckVO));
    }

    /**
     * 根据上级会员和生命周期规则ID查询该规则下所有下级会员
     *
     * @param cycleRuleVO 请求对象
     * @return 返回MemberFeignLifeCycleRuleVO对象
     */
    @Override
    public WrapperResp<List<MemberFeignLifeCycleRuleResp>> getSubMemberWithLifecycleRule(@RequestBody @Valid MemberFeignWithLifeCycleRuleReq cycleRuleVO) {
        return WrapperUtil.success(memberLifeCycleStageRuleFeignService.getSubMemberWithLifecycleRule(cycleRuleVO));
    }

    /**
     * 校验会员是否配置某生命周期规则阶段
     *
     * @param memberLifeCycleRuleCheckReq 入参
     * @return 返回结果
     */
    @Override
    public WrapperResp<Boolean> checkMemberHasConfigureLifeCycleRule(@RequestBody @Valid MemberLifeCycleRuleCheckReq memberLifeCycleRuleCheckReq) {
        return WrapperUtil.success(memberLifeCycleStageRuleFeignService.checkMemberHasConfigureLifeCycleRule(memberLifeCycleRuleCheckReq));
    }

    /**
     * 批量校验是否允许创建请购订单合同
     *
     * @param memberLifeCycleStagesRuleCheckBatchFeignReq 入参
     * @return 返回结果
     */
    @Override
    public WrapperResp<List<String>> checkBatchMemberLifeCycleStagesRule(@RequestBody @Valid MemberLifeCycleStagesRuleCheckBatchFeignReq memberLifeCycleStagesRuleCheckBatchFeignReq) {
        List<String> resultList = memberLifeCycleStageRuleFeignService.checkBatchMemberLifeCycleStagesRule(memberLifeCycleStagesRuleCheckBatchFeignReq);
        if(CollUtil.isNotEmpty(resultList)){
            return WrapperUtil.fail(ResponseCodeEnum.CONTRACT_PROCESS_ORDERS_IS_NOT_ALLOWED,resultList);
        }
        return WrapperUtil.success();
    }
}
