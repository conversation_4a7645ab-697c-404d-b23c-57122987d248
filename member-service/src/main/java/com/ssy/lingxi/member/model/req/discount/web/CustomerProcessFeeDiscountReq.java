package com.ssy.lingxi.member.model.req.discount.web;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户工费优惠请求对象（新增和更新通用）
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-26
 */
@Data
public class CustomerProcessFeeDiscountReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（更新时必填，新增时为空）
     */
    private Long id;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 优惠截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime discountEndTime;

    /**
     * 是否启用 0-否 1-是
     */
    private Integer status;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 分类明细
     */
    private List<CustomerProcessFeeDiscountBtReq> btContent;

    /**
     * 附体明细
     */
    private List<CustomerProcessFeeDiscountFtReq> ftContent;

    /**
     * 千足基础工费优惠
     */
    private BigDecimal baseDiscountThousand;

    /**
     * 万足基础工费优惠
     */
    private BigDecimal baseDiscountTenThousand;

    /**
     * 五个9基础工费优惠
     */
    private BigDecimal baseDiscountFiveNine;

    /**
     * 优惠申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime discountApplyTime;
}
