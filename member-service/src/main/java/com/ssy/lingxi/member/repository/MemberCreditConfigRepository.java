package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.levelRight.MemberCreditConfigDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员信用评估操作Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-09
 */
@Repository
public interface MemberCreditConfigRepository extends JpaRepository<MemberCreditConfigDO, Long>, JpaSpecificationExecutor<MemberCreditConfigDO> {

}
