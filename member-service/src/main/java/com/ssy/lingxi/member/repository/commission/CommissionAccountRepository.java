package com.ssy.lingxi.member.repository.commission;

import com.ssy.lingxi.member.entity.do_.commission.CommissionAccountDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 分佣账户Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Repository
public interface CommissionAccountRepository extends JpaRepository<CommissionAccountDO, Long>, JpaSpecificationExecutor<CommissionAccountDO> {

    /**
     * 根据用户id查询分佣账户
     * @param userId 用户id
     * @return 分佣账户
     */
    Optional<CommissionAccountDO> findByUserId(Long userId);


    /**
     * 根据账户状态查询分佣账户列表
     * @param accountStatus 账户状态
     * @param pageable 分页参数
     * @return 分佣账户列表
     */
    Page<CommissionAccountDO> findByAccountStatus(Integer accountStatus, Pageable pageable);

    /**
     * 根据用户id列表查询分佣账户列表
     * @param userIds 用户id列表
     * @return 分佣账户列表
     */
    List<CommissionAccountDO> findByUserIdIn(List<Long> userIds);

    /**
     * 查询账户余额大于指定金额的账户
     * @param minBalance 最小余额
     * @param pageable 分页参数
     * @return 分佣账户列表
     */
    @Query("SELECT ca FROM CommissionAccountDO ca WHERE ca.accountBalance > :minBalance")
    Page<CommissionAccountDO> findByAccountBalanceGreaterThan(@Param("minBalance") java.math.BigDecimal minBalance, Pageable pageable);

    /**
     * 查询可提现余额大于指定金额的账户
     * @param minWithdrawableBalance 最小可提现余额
     * @param pageable 分页参数
     * @return 分佣账户列表
     */
    @Query("SELECT ca FROM CommissionAccountDO ca WHERE ca.withdrawableBalance > :minWithdrawableBalance")
    Page<CommissionAccountDO> findByWithdrawableBalanceGreaterThan(@Param("minWithdrawableBalance") java.math.BigDecimal minWithdrawableBalance, Pageable pageable);

    /**
     * 统计账户总数
     * @return 账户总数
     */
    @Query("SELECT COUNT(ca) FROM CommissionAccountDO ca")
    Long countAllAccounts();

    /**
     * 统计指定状态的账户数量
     * @param accountStatus 账户状态
     * @return 账户数量
     */
    Long countByAccountStatus(Integer accountStatus);
}
