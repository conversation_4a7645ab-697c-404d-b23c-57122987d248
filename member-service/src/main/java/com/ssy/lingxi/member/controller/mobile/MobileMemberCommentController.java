package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.mobile.MobileMemberTradeCommentSaveReq;
import com.ssy.lingxi.member.model.resp.mobile.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.mobile.IMobileMemberCommentService;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * App - 评价中心
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/3
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/comment")
public class MobileMemberCommentController {
    @Resource
    private IMobileMemberCommentService mobileMemberCommentService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    /**
     * 评价中心 - 待评价 - 分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/wait/page")
    public WrapperResp<PageDataResp<MobileWaitCommentOrderProductPageResp>> pageWaitOrderComment(@RequestHeader HttpHeaders headers, @Validated PageDataReq pageDataReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(mobileMemberCommentService.pageWaitOrderComment(loginUser, pageDataReq));
    }

    /**
     * 评价中心 - 待评价 - 评价商品详情
     * @param headers Http头部信息
     * @param commonIdReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/order/product/get")
    public WrapperResp<MobileCommentOrderProductResp> getMemberOrderComment(@RequestHeader HttpHeaders headers, @Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(mobileMemberCommentService.getMemberOrderComment(loginUser, commonIdReq));
    }

    /**
     * 评价中心 - 待评价 - 写评价
     * @param headers Http头部信息
     * @param memberTradeCommentSaveVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/save")
    public WrapperResp<Void> saveMemberComment(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobileMemberTradeCommentSaveReq memberTradeCommentSaveVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        mobileMemberCommentService.saveMemberComment(loginUser, memberTradeCommentSaveVO);
        return WrapperUtil.success();
    }

    /**
     * 评价中心 - 已评价 - 分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/complete/page")
    public WrapperResp<PageDataResp<MobileCompleteCommentPageResp>> pageCompleteOrderComment(@RequestHeader HttpHeaders headers, @Validated PageDataReq pageDataReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(mobileMemberCommentService.pageCompleteOrderComment(loginUser, pageDataReq));
    }

    /**
     * 评价中心 - 已评价 - 评价商品详情
     * @param headers Http头部信息
     * @param commonIdReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/product/comment/get")
    public WrapperResp<MobileCommentHistoryResp> getMemberCommentHistory(@RequestHeader HttpHeaders headers, @Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(mobileMemberCommentService.getMemberCommentHistory(loginUser, commonIdReq));
    }

    /**
     * 评价中心 - 卖家对我的评价 - 分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/receive/page")
    public WrapperResp<PageDataResp<MobileReceiveCommentPageResp>> pageReceiveOrderComment(@RequestHeader HttpHeaders headers, @Validated PageDataReq pageDataReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(mobileMemberCommentService.pageReceiveOrderComment(loginUser, pageDataReq));
    }
}
