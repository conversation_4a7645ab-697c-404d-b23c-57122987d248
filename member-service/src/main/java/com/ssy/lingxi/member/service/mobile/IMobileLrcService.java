package com.ssy.lingxi.member.service.mobile;

import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.req.mobile.MobileRightScoreHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.mobile.MobileShopRightScorePageDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRightScoreResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileRightScoreHistoryResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileRightScoreResp;
import com.ssy.lingxi.member.model.resp.mobile.MobileShopRightScoreHistoryResp;
import org.springframework.http.HttpHeaders;

/**
 * App - 会员等级、权益、信用相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-17
 */
public interface IMobileLrcService {

    /**
     * “积分订单” - 查询平台通用和会员专有权益积分
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberRightScoreResp getMemberRightScore(HttpHeaders headers, MemberAndRoleIdReq idVO);

    /**
     * “我的积分” - 分页查询列表
     * @param headers Http头部信息
     * @param pageDataReq 接口参数
     * @return 查询结果
     */
    PageDataResp<MobileRightScoreResp> pageRightScore(HttpHeaders headers, PageDataReq pageDataReq);

    /**
     * “我的积分” - 分页查询权益积分历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MobileRightScoreHistoryResp> pageRightScoreHistory(HttpHeaders headers, MobileRightScoreHistoryPageDataReq pageVO);

    /**
     * “找店铺 - 积分兑换” - 分页查询权益积分历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    MobileShopRightScoreHistoryResp pageShopRightScoreHistory(HttpHeaders headers, MobileShopRightScorePageDataReq pageVO);
}
