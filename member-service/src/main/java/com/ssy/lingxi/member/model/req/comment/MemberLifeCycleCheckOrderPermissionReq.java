package com.ssy.lingxi.member.model.req.comment;

import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 下级会员校验下单权限VO
 * <AUTHOR>
 * @since 2022/7/15 14:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberLifeCycleCheckOrderPermissionReq implements Serializable {

    private static final long serialVersionUID = -6363752554795236755L;

    List<MemberFeignReq> memberList;

}
