package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.basic.UserPageDataReq;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 渠道能力 - 业务员管理服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-04-14
 */
public interface IMemberAbilitySalesService {

    /**
     * 分页查询已经绑定渠道的业务员列表
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberSalesPageQueryResp> pageMemberSales(HttpHeaders headers, MemberSalesPageDataReq pageVO);

    /**
     * “选择业务员” - 分页查询业务员列表
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberSalesSelectQueryResp> pageSelectMemberSales(HttpHeaders headers, UserPageDataReq pageVO);

    /**
     * 新增业务员
     *
     * @param headers HttpHeader信息
     * @param idVO    接口参数
     * @return 操作结果
     */
    MemberSalesGetResp addMemberSales(HttpHeaders headers, MemberUserIdReq idVO);

    /**
     * 查询业务员基本信息
     *
     * @param headers HttpHeader信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    MemberSalesGetResp getMemberSales(HttpHeaders headers, MemberUserIdReq idVO);

    /**
     * 分页查询渠道会员列表
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberSalesBindChannelQueryResp> pageChannels(HttpHeaders headers, MemberSalesSelectPageDataReq pageVO);

    /**
     * 查询业务员信息、分页查询绑定的渠道列表
     *
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    MemberSalesChannelQueryResp getSalesAndPageChannels(HttpHeaders headers, MemberSalesChannelPageDataReq pageVO);

    /**
     * 业务员绑定渠道列表
     *
     * @param headers       HttpHeader信息
     * @param bindChannelVO 接口参数
     * @return 操作结果
     */
    void bindSalesChannels(HttpHeaders headers, MemberSalesBindChannelReq bindChannelVO);

    /**
     * 解除业务员与渠道的绑定
     *
     * @param headers         HttpHeader信息
     * @param unBindChannelVO 接口参数
     * @return 操作结果
     */
    void unBindSalesChannel(HttpHeaders headers, MemberSalesUnBindChannelReq unBindChannelVO);

    /**
     * 删除业务员
     *
     * @param headers HttpHeader信息
     * @param idVO    接口参数
     * @return 操作结果
     */
    void deleteSales(HttpHeaders headers, MemberUserIdReq idVO);

    /**
     * 返回组织机构
     *
     * @param headers 头部信息
     * @return 组织机构列表
     */
    List<String> getMemberOrganizationList(HttpHeaders headers);

}
