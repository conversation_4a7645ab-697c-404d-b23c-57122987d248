package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.model.req.lrc.*;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.RoleQueryResp;
import com.ssy.lingxi.member.model.resp.lrc.*;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 平台后台、能力中心 - 会员等级配置服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-21
 */
public interface IMemberLevelConfigService {
    /**
     * 分页查询会员升级规则
     * @param headers HttpHeaders信息
     * @param pageDataReq 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberLevelRuleConfigQueryResp> pageMemberLevelRuleConfig(HttpHeaders headers, PageDataReq pageDataReq);

    /**
     * 修改会员升级规则可获取的分值
     * @param headers HttpHeaders信息
     * @param ruleVO 接口参数
     * @return  操作结果
     */
    void updateMemberLevelRuleConfigScore(HttpHeaders headers, MemberLevelRuleConfigUpdateScoreReq ruleVO);

    /**
     * 分页查询会员等级配置
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberLevelQueryResp> pageMemberLevels(HttpHeaders headers, MemberLevelPageDataReq pageVO);

    /**
     * 分页查询会员等级配置
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    PageDataResp<MemberLevelQueryResp> pageMemberLevelByConsumer(HttpHeaders headers, MemberLevelPageDataReq pageVO);

    /**
     * 新增或修改会员等级时，查询等级类型列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<MemberLevelTypeResp> findMemberLevelTypes(HttpHeaders headers);

    /**
     * 新增或修改会员等级时，分页查询会员角色列表
     * @param headers Http头部信息
     * @param pageDataReq 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<RoleQueryResp> pageRoles(HttpHeaders headers, PageDataReq pageDataReq, Integer roleTag);

    /**
     * 新增会员等级
     * @param headers Http头部信息
     * @param levelVO 接口参数
     * @return 操作结果
     */
    void createMemberLevel(HttpHeaders headers, MemberLevelReq levelVO);

    /**
     * 修改会员等级
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateMemberLevel(HttpHeaders headers, MemberLevelUpdateReq updateVO);

    /**
     * 查询会员等级详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberLevelDetailResp findMemberLevel(HttpHeaders headers, MemberLevelIdReq idVO);

    /**
     * 修改会员等级状态
     * @param headers Http头部信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    void updateMemberLevelStatus(HttpHeaders headers, MemberLevelStatusReq statusVO);

    /**
     * 删除会员等级
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    void deleteMemberLevel(HttpHeaders headers, MemberLevelIdReq idVO);

    /**
     * 权益与升级阈值 - 选择会员权益
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<BaseMemberRightQueryResp> findMemberLevelRights(HttpHeaders headers);

    /**
     * 权益与升级阈值 - 查询详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberLevelRightDetailResp findMemberLevelRightDetail(HttpHeaders headers, MemberLevelIdReq idVO);

    /**
     * 修改升级阈值、权益列表
     * @param headers Http头部信息
     * @param pointVO 接口参数
     * @return 操作结果
     */
    void updateMemberLevelRight(HttpHeaders headers, MemberLevelRightUpdateReq pointVO);

    /**
     * 修改权益参数
     * @param headers Http头部信息
     * @param paramVO 接口参数
     * @return 操作结果
     */
    void updateRightParameter(HttpHeaders headers, MemberRightParamReq paramVO);

    /**
     * 修改权益状态
     * @param headers Http头部新
     * @param statusVO 接口参数
     * @return 操作结果
     */
    void updateRightStatus(HttpHeaders headers, MemberRightStatusReq statusVO);

    /**
     * 将所有没有配置会员等级的下级会员，初始化为最小会员等级
     * @param headers Http头部信息
     * @return 操作结果
     */
    void rebuildMemberLevelRight(HttpHeaders headers);

    /**
     * 查询平台会员的等级、权益、信用积分
     * @param headers Http头部信息
     * @param levelVO 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    MemberLrcResp getPlatformMemberLrc(HttpHeaders headers, MemberIdAndRoleIdReq levelVO);

    /**
     * 新增平台会员时，创建平台会员的升级规则配置
     * @param memberId 会员Id
     * @param roleId   角色Id
     */
    void createMemberLevelRuleConfig(Long memberId, Long roleId);

    /**
     * 查询是否配置会员等级
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 是否配置
     */
    Boolean isConfiguration(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 查询上级配置当前会员等级
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 是否配置
     */
    Boolean subConfiguration(HttpHeaders headers, ValidateIdReq idVO);
}
