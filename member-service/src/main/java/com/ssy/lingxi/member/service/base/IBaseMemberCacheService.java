package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.entity.bo.AuthCodeBO;
import com.ssy.lingxi.member.service.mobile.IMobileLoginService;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletRequest;

/**
 * 会员用户缓存工具类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-03
 */
public interface IBaseMemberCacheService {

    /**
     * 获取用户信息
     * @param headers HttpHeaders信息
     * @return 缓存的用户信息
     */
    UserLoginCacheDTO checkUserFromCache(HttpHeaders headers);

    /**
     * 是否从平台后台登录
     * @return 缓存的用户信息
     */
    UserLoginCacheDTO needLoginFromManagePlatform();

    /**
     * 是否从平台后台登录
     * @param headers HttpHeaders信息
     * @return 缓存的用户信息
     */
    UserLoginCacheDTO needLoginFromManagePlatform(HttpHeaders headers);

    /**
     * 是否从业务平台Web客户端登录
     * @param headers HttpHeaders信息
     * @return 缓存的用户信息
     */
    UserLoginCacheDTO needLoginFromBusinessPlatform(HttpHeaders headers);

    /**
     * 是否从App客户端登录
     * @param headers HttpHeaders信息
     * @return 缓存的用户信息
     */
    UserLoginCacheDTO needLoginFromMobile(HttpHeaders headers);

    /**
     * 校验Web端请求的HttpHeader参数
     */
    void checkWebRequestHeader();

    /**
     * 获取登录来源
     */
    String getSource();

    /**
     * 获取HttpServletRequest
     */
    HttpServletRequest getHttpServletRequest();

    /**
     * 校验Web端请求的HttpHeader参数
     * @param headers HttpHeaders信息
     */
    void checkWebRequestHeader(HttpHeaders headers);

    /**
     * 校验移动端请求的HttpHeader参数是否合法
     */
    void checkMobileRequestHeader();

    /**
     * 校验移动端请求的HttpHeader参数是否合法
     * @param headers HttpHeaders信息
     */
    void checkMobileRequestHeader(HttpHeaders headers);

    /**
     * 校验平台后台请求的HttpHeader中是否包含source = 99
     */
    void checkPlatformRequestHeader();

    /**
     * 校验平台后台请求的HttpHeader中是否包含source = 99
     * @param headers HttpHeaders信息
     */
    void checkPlatformRequestHeader(HttpHeaders headers);

    /**
     * 从Http头部获取Long类型的字段值
     * @param headers HttpHeaders信息
     * @param key     HttpHeaders中的Key
     * @return Long类型值
     */
    Long getLongValueFromRequestHeader(HttpHeaders headers, String key);

    /**
     * 校验请求头的HttpHeader中是否包含Long类型的必要信息 如：站点id 或 商城id等
     *
     * @param headers HttpHeaders信息
     * @return 请求头中是否有合理的Long类型的字段
     */
    Boolean checkLongValue(HttpHeaders headers, String... keyNames);

    /**
     * 缓存字符串类型的内容
     * @param key  缓存的Key
     * @param content 缓存的内容
     * @param timeoutSeconds 缓存时间，单位：秒
     */
    void setString(String key, String content, Long timeoutSeconds);

    /**
     * 读取缓存的字符串
     * @param key  缓存的Key
     * @return 如果缓存内容不存在，返回空字符串，否则返回缓存内容
     */
    String getString(String key);

    /**
     * 删除缓存的字符串
     * @param key 缓存的字符串
     */
    void deleteString(String key);


    /**
     * 会员/用户注册时，缓存手机号或邮箱
     * @param phone 手机号
     * @param email 邮箱
     * @param isPlatformMember 是否平台后台会员
     */
    void setRegisterKey(String phone, String email, Boolean isPlatformMember);

    /**
     * 会员/用户注册时，缓存手机号或邮箱或账号
     * @param phone 手机号
     * @param email 邮箱
     * @param account 账号
     * @param isPlatformMember 是否平台后台会员
     */
    void setRegisterKey(String phone, String email, String account, Boolean isPlatformMember);


    /**
     * 缓存的Key是否存在
     * @param phoneOrEmail 缓存的手机号或邮箱
     * @param isPlatformMember 是否平台后台会员
     * @return true/false
     */
    Boolean existRegisterKey(String phoneOrEmail, Boolean isPlatformMember);

    /**
     * 删除缓存的用户手机号或邮箱
     * @param phone 缓存的手机号
     * @param email 缓存的邮箱
     * @param isPlatformMember 是否平台后台会员
     */
    void deleteRegisterKey(String phone, String email, Boolean isPlatformMember);

    /**
     * 删除缓存的用户手机号或邮箱或账号
     * @param phone 缓存的手机号
     * @param email 缓存的邮箱
     * @param account 缓存的账号
     * @param isPlatformMember 是否平台后台会员
     */
    void deleteRegisterKey(String phone, String email, String account, Boolean isPlatformMember);

    /**
     * 生成授权码
     *
     * 手机app扫web的二维码功能
     * 1 依据oauth2.0的获取令牌方式authorization-code做了方法定义, 方便后续改为以认证中心来获取授权码
     * 2.1 前端用string生成二维码,app扫描后解析获取string, 用以请求登录接口, 登录接口校验string
     * 2.2 前端用string生成二维码,app扫描后解析获取string, 用以认证中心的授权页面, 授权页面请求登录接口, 登录接口校验string
     * 2.2.1 以钉钉的登录二维码为例, 解析后得到的string为授权页面的url且附带着授权码, 即为url?code=xxx
     * -- 目前没有认证中心, 所以采用2.1方法做功能
     * 3.1 生成一个code返回, web将code渲染为二维码
     * 3.2 app扫描二维码, 解析得到code, 并请求接口校验
     * 3.3 web提交code轮询接口
     * @see IBaseMemberCacheService#createAuthCode
     * @see IBaseMemberCacheService#activeAuthCode
     * @see IMobileLoginService#authCodeActive
     * @return 授权码
     */
    AuthCodeBO createAuthCode();

    /**
     * 激活授权码
     * @param authCode 授权码
     * @param userLoginCacheDTO 用户缓存信息
     */
    void activeAuthCode(String authCode, UserLoginCacheDTO userLoginCacheDTO);
}
