package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.enums.MemberDetailVersionEnum;
import com.ssy.lingxi.member.model.resp.basic.DetailTextGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RegisterDetailGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RegisterDetailResp;

import java.util.List;
import java.util.Map;

/**
 * 会员入库资料基础服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-20
 */
public interface IBaseMemberDepositDetailService {

    /**
     * 查询会员入库资料配置列表
     * @param memberId 会员Id
     * @param roleId   角色Id
     * @param subRole  下级会员角色
     * @return 查询结果
     */
    List<MemberRegisterConfigDO> findMemberDepositoryConfig(Long memberId, Long roleId, MemberRoleDO subRole);

    /**
     * 将注册资料转换为分组内容
     * @param memberConfigs 注册资料列表
     * @return 分组内容
     */
    List<RegisterDetailGroupResp> groupMemberConfig(List<MemberRegisterConfigDO> memberConfigs);

    /**
     * 查询该字段包含的子字段
     * @param id 父id
     * @return 子列表集
     */
    List<RegisterDetailResp> initMemberConfig(Long id);

    /**
     * 入库审核过程中，新增或修改入库资料时，分组展示“待审核”的入库资料
     * @param relationDO 会员关系
     * @return 分组内容
     */
    List<RegisterDetailGroupResp> groupMemberDepositoryDetail(MemberRelationDO relationDO);

    /**
     * 查询该字段包含的子字段
     * @param id 父id
     * @return 子列表集
     */
    List<List<RegisterDetailResp>> initDepositoryDetail(Long id);

    /**
     * 更新入库资料，保存入库，判断是否启动会员变更流程
     * @param relationDO 会员关系
     * @param detailMap 修改后的入库资料
     * @param roleTag 角色标签
     * @return 操作结果
     */
    WrapperResp<Void> checkAndUpdateMemberDepositoryDetail(MemberRelationDO relationDO, Map<String, Object> detailMap, Integer roleTag);

    /**
     * 变更审核通过后，比较入库资料版本，记录资料变更历史记录
     * @param relationDO 会员关系
     */
    void saveDepositDetailHistory(MemberRelationDO relationDO);

    /**
     * 检查并保存，入库资料，保存的版本为“待审核”
     * @param relationDO 会员关系
     * @param detailMap 新增的会员入库资料
     * @return 检查结果
     */
    void checkAndSaveMemberDepositoryDetail(MemberRelationDO relationDO, Map<String, Object> detailMap);

    /**
     * 循环、递归删除MemberDepositoryDetailDO
     * @param validatingDetails 已存在待审核入库资料
     */
    void deleteDepositDetails(List<MemberDepositoryDetailDO> validatingDetails);

    /**
     * 申请成为会员时，检查入库资料，调用方要设置MemberRelationDO并保存
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级会员角色Id
     * @param subRole       下级会员角色
     * @param detailMap     入库资料
     * @return 入库资料列表
     */
    List<MemberDepositoryDetailDO> checkMemberDepositoryDetail(Long upperMemberId, Long upperRoleId, MemberRoleDO subRole, Map<String, Object> detailMap);

    /**
     * 检查并生成指定版本的入库资料
     * @param relationDO 入库资料关联的会员关系
     * @param memberConfigList 注册资料列表
     * @param detailMap        前端传递的入库资料Map
     * @param versionEnum      指定版本
     * @return 入库资料列表
     */
    List<MemberDepositoryDetailDO> checkMemberDepositoryDetail(MemberRelationDO relationDO, List<MemberRegisterConfigDO> memberConfigList, Map<String, Object> detailMap, MemberDetailVersionEnum versionEnum);


    /**
     * 查询会员入库资料（指定版本）
     * @param relationDO 会员关系
     * @param versionEnum 版本
     * @return 查询结果
     */
    List<RegisterDetailGroupResp> findMemberDepositoryDetail(MemberRelationDO relationDO, MemberDetailVersionEnum versionEnum);

    /**
     * 查询会员入库资料（先查询“正在使用”的版本，如无则查询“正在审核”的版本）
     * @param relationDO 会员关系
     * @return 查询结果
     */
    List<RegisterDetailGroupResp> switchMemberDepositoryDetail(MemberRelationDO relationDO);


    /**
     * 查询会员入库资料（合并两个版本）
     * @param relationDO 会员关系
     * @return 查询结果
     */
    List<DetailTextGroupResp> mergeMemberDepositoryDetailText(MemberRelationDO relationDO);

    /**
     * 查询会员入库资料（指定版本）
     * @param relationDO 会员关系
     * @param versionEnum 版本
     * @return 查询结果
     */
    List<DetailTextGroupResp> findMemberDepositoryDetailText(MemberRelationDO relationDO, MemberDetailVersionEnum versionEnum);

    /**
     * 查询会员入库资料（先查找审核通过的版本，如果没有则查找正在审核的版本）
     * @param relationDO 会员关系
     * @return 查询结果
     */
    List<DetailTextGroupResp> switchMemberDepositoryDetailText(MemberRelationDO relationDO);

    /**
     * 入库审核通过后，修改入库资料版本为“Using”
     * @param relationDO 会员关系
     */
    void updateDepositDetailToUsing(MemberRelationDO relationDO);

    /**
     * 检查注册资料、入库资料的字段是否齐全
     * @param depositoryDetailList 入库资料列表
     * @param detailMap 前端传递的入库资料
     * @return 检查结果
     */
    WrapperResp<Void> checkDetailFields(List<MemberDepositoryDetailDO> depositoryDetailList, Map<String, Object> detailMap);

    /**
     * 更新入库资料搜索项数据
     * @param relationDO 会员关系
     */
    void updateMemberDepositoryDetailSelect(MemberRelationDO relationDO,List<MemberDepositoryDetailDO> depositoryDetailList);

    /**
     * 更新入库资料搜索字段
     */
    void updateDepositoryDetailAllowSelect(List<MemberDepositoryDetailDO> depositoryDetailList);

    /**
     * 保存入库资料
     */
    void saveDepositoryDetail(MemberRelationDO relationDO, Integer outerStatus);

}
