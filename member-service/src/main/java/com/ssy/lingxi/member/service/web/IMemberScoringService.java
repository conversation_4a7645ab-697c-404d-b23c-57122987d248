package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberScoringTemplateDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberScoringTemplateIndicatorDO;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberScoringIndicatorGroupResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberScoringIndicatorResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberScoringTemplateDetailResp;
import com.ssy.lingxi.member.model.resp.lifecycle.StatusResp;

import java.util.List;

/**
 * 评分模板服务类
 * <AUTHOR>
 * @since 2022/6/26 21:01
 * @version 1.0
 */
public interface IMemberScoringService {

    /**
     * 指标标准定义 - 评分标准提交
     * @param loginUser 登录用户信息
     * @param addVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void submitScoringIndicator(UserLoginCacheDTO loginUser, List<MemberScoringIndicatorAddReq> addVO, Integer roleTag);

    /**
     * 保存评分标准
     * @param saveOrUpdateList 待新增或更新列表
     * @param memberId 会员id
     * @param roleId 角色id
     * @param roleTag 角色标签
     */
    void saveScoringIndicator(List<MemberScoringIndicatorAddReq> saveOrUpdateList, Long memberId, Long roleId, Integer roleTag);

    /**
     * 保存评分模板标准
     * @param memberScoringTemplateDO 评分模板
     * @param addVO 待保存列表
     * @param memberId 会员id
     * @param roleId 角色id
     * @param roleTag 角色标签
     */
    void saveScoringTemplate(MemberScoringTemplateDO memberScoringTemplateDO, MemberScoringTemplateReq addVO, Long memberId, Long roleId, Integer roleTag);

    /**
     * 保存评分模板标准
     * @param saveOrUpdateList 待新增或更新列表
     * @param memberId 会员id
     * @param roleId 角色id
     * @param roleTag 角色标签
     * @param scoringTemplateDO 评分模板
     * @return 操作结果
     */
    List<MemberScoringTemplateIndicatorDO> saveScoringTemplateIndicator(List<MemberScoringTemplateIndicatorSubmitReq> saveOrUpdateList, Long memberId, Long roleId, Integer roleTag, MemberScoringTemplateDO scoringTemplateDO);

    /**
     * 指标标准定义 - 评分标准列表
     * @param loginUser 登录用户信息
     * @param roleTag 角色标签
     * @return 操作结果
     */
    List<MemberScoringIndicatorGroupResp> pageScoringIndicator(UserLoginCacheDTO loginUser, Integer roleTag);

    /**
     * 指标标准定义 - 未配置评分项目
     * @param loginUser 登录用户信息
     * @param queryVO 请求参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    List<MemberScoringIndicatorResp> toAssignedPageScoringIndicator(UserLoginCacheDTO loginUser, MemberScoringIndicatorPageReq queryVO, Integer roleTag);

    /**
     * 评分模板配置 - 新增评分模板
     * @param loginUser 登录用户信息
     * @param addVO 请求参数VO
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void addTemplate(UserLoginCacheDTO loginUser, MemberScoringTemplateReq addVO, Integer roleTag);

    /**
     * 评分模板配置 - 修改评分模板
     * @param loginUser 登录用户信息
     * @param vo 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void updateTemplate(UserLoginCacheDTO loginUser, MemberScoringTemplateReq vo, Integer roleTag);

    /**
     * 评分模板配置 - 详情
     * @param loginUser 登录用户信息
     * @param vo 接口参数
     * @return 操作结果
     */
    MemberScoringTemplateDetailResp templateDetail(UserLoginCacheDTO loginUser, TemplateIdReq vo, Integer roleTag);

    /**
     * 评分模板配置 - 列表查询
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    List<MemberScoringTemplateDetailResp> templatePage(UserLoginCacheDTO loginUser, MemberScoringTemplatePageReq pageVO, Integer roleTag);

    /**
     * 评分模板配置 - 停用/启用
     * @param loginUser 登录用户信息
     * @param vo 接口参数
     * @return 操作结果
     */
    void startOrStopTemplate(UserLoginCacheDTO loginUser, ScoringTemplateStartOrStopReq vo, Integer roleTag);

    /**
     * 评分模板配置 - 删除评分模板
     * @param loginUser 登录用户信息
     * @param vo 接口参数
     * @return 操作结果
     */
     void deleteTemplate(UserLoginCacheDTO loginUser, MemberScoringTemplateDeleteReq vo, Integer roleTag);

    /**
     * 模板类型下拉查询
     * @param roleTag 角色标签
     * @return 查询结果
     */
    List<StatusResp> templateTypeList(Integer roleTag);
}
