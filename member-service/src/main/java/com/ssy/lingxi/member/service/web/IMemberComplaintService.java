package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.model.req.basic.NamePageDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditComplainSummaryResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员投诉与建议服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
public interface IMemberComplaintService {

    /**
     * 状态下拉查询
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<StatusResp> listMemberComplaintStatus(HttpHeaders headers);

    /**
     * 投诉建议新增 - 选择上级会员
     * @param headers Http头部信息 Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<UpperMemberQueryResp> pageMembers(HttpHeaders headers, NamePageDataReq pageVO);

    /**
     * 投诉建议分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberUpperComplaintPageQueryResp> pageUpperMemberComplaint(HttpHeaders headers, MemberComplaintPageDataReq pageVO, Integer roleTag);

    /**
     * 会员信息 - 会员信用 - 会员投诉记录汇总
     * @param upperMember 上级会员
     * @param roleId 上级会员角色Id
     * @param subMember 下级会员
     * @param subRoleId 下级会员角色Id
     * @return 汇总结果
     */
    MemberDetailCreditComplainSummaryResp summaryUpperMemberComplaints(MemberDO upperMember, Long roleId, MemberDO subMember, Long subRoleId);

    /**
     * 会员信息 - 会员信用 - 分页查询会员投诉记录
     * @param upperMember 上级会员
     * @param roleId 上级会员角色Id
     * @param subMember 下级会员
     * @param subRoleId 下级会员角色Id
     * @param current 当前页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberCreditComplaintPageQueryResp> pageUpperMemberComplaint(MemberDO upperMember, Long roleId, MemberDO subMember, Long subRoleId, int current, int pageSize);

    /**
     * 投诉建议分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberSubComplaintPageQueryResp> pageSubMemberComplaint(HttpHeaders headers, MemberComplaintPageDataReq pageVO);

    /**
     * 投诉建议查询
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    MemberComplaintUpperResp getUpperMemberComplaint(HttpHeaders headers, CommonIdReq idVO);

    /**
     * 投诉建议查询
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    MemberComplaintSubResp getSubMemberComplaint(HttpHeaders headers, CommonIdReq idVO);

    /**
     * 投诉建议新增
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void addUpperMemberComplaint(HttpHeaders headers, MemberComplaintUpperAddReq addVO);

    /**
     * 投诉建议新增
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void addSubMemberComplaint(HttpHeaders headers, MemberComplaintSubAddReq addVO);

    /**
     * 投诉建议修改
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void updateUpperMemberComplaint(HttpHeaders headers, MemberComplaintUpperUpdateReq addVO);

    /**
     * 投诉建议修改
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void updateSubMemberComplaint(HttpHeaders headers, MemberComplaintSubUpdateReq addVO);

    /**
     * 投诉建议删除
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    void deleteMemberComplaint(HttpHeaders headers, CommonIdReq idVO);

    /**
     * 投诉建议提交
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void submitMemberComplaint(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 投诉建议处理
     *
     * @param headers  Http头部信息
     * @param handleVO 接口参数
     * @param roleTag  角色标签
     * @return 操作结果
     */
    void handleMemberComplaint(HttpHeaders headers, MemberComplaintHandleReq handleVO, Integer roleTag);

    /**
     * 下级提交投诉建议
     *
     * @param headers 请求头
     * @param idVO    通用idVO
     * @param roleTag 角色标签
     * @return 返回
     */
    void submitSubMemberComplaint(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 下级处理投诉建议
     *
     * @param headers  请求头
     * @param handleVO 入参
     * @param roleTag 角色标签
     * @return 返回
     */
    void handleSubMemberComplaint(HttpHeaders headers, MemberComplaintHandleReq handleVO, Integer roleTag);
}
