package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.BusinessCategoryInvoiceTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 会员入库分类 - 发票类型枚举校验注解
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = BusinessCategoryInvoiceTypeValidator.class)
public @interface BusinessCategoryInvoiceTypeAnnotation {
    String message() default "发票类型不在定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
