package com.ssy.lingxi.member.controller.web.supplier;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.model.resp.MemberLifecycleRuleQueryResp;
import com.ssy.lingxi.member.model.req.comment.MemberLifecycleRuleReq;
import com.ssy.lingxi.member.service.web.IMemberLifecycleRuleService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员能力-系统管理-供应商角色标签生命周期相关规则配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/supplier/lifecycle/rule")
public class SupplierLifecycleRulesController {

    /**
     * 角色标签-------供应商接口
     */
    private final Integer roleTag = RoleTagEnum.SUPPLIER.getCode();

    @Resource
    private IMemberLifecycleRuleService memberLifecycleStagesService;

    /**
     * 保存指定会员、客户角色标签生命周期规则
     * @param headers 头部信息
     * @param memberLifecycleRuleReq 请求参数
     * @return 执行结果
     */
    @PostMapping("/add")
    public WrapperResp<Void> addLifecycleRules(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLifecycleRuleReq memberLifecycleRuleReq) {
        memberLifecycleStagesService.addLifecycleRules(headers, memberLifecycleRuleReq, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 查询指定会员、客户角色标签的生命周期规则
     * @param headers 请求头部信息
     * @return 返回指定会员的生命周期规则
     */
    @PostMapping("/get")
    public WrapperResp<MemberLifecycleRuleQueryResp> getLifecycleRules(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberLifecycleStagesService.getLifecycleRules(headers, roleTag));
    }

}
