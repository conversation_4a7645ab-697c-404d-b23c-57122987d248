package com.ssy.lingxi.member.serviceImpl.base;

import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberProcessTypeEnum;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.member.entity.bo.ProcessBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberProcessDO;
import com.ssy.lingxi.member.repository.MemberProcessRepository;
import com.ssy.lingxi.member.service.base.IBasePlatformProcessService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 角色关联的平台会员审核（会员注册）流程相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-11-10
 */
@Service
public class BasePlatformProcessServiceImpl implements IBasePlatformProcessService {
    @Resource
    private MemberProcessRepository memberProcessRepository;

    /**
     * 查询角色关联的平台会员审核（会员注册）流程
     *
     * @param memberRoleDO 会员角色
     * @return 流程的processKey
     */
    @Override
    public ProcessBO findRolePlatformProcess(MemberRoleDO memberRoleDO) {
        MemberProcessDO registerProcess = memberRoleDO.getRegisterProcess();
        if (registerProcess == null) {
            registerProcess = memberProcessRepository.findFirstByProcessTypeAndIsDefault(MemberProcessTypeEnum.PLATFORM_VALIDATION.getCode(), true);
            BusinessAssertUtil.notNull(registerProcess, ResponseCodeEnum.MC_MS_ROLE_PLATFORM_PROCESS_DOES_NOT_EXIST);
        }

        return new ProcessBO(registerProcess.getProcessKey(), registerProcess.getEmptyProcess());
    }
}
