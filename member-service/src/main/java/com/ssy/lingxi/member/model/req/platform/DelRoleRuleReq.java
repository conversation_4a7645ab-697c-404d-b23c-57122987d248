package com.ssy.lingxi.member.model.req.platform;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 删除会员适用角色VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-11
 **/
@Data
public class DelRoleRuleReq implements Serializable {
    private static final long serialVersionUID = 4595912888583061719L;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空")
    private Long memberId;

    /**
     * 会员要删除的适用角色idList
     */
    private List<Long> memberRoleIdList;

    /**
     * 当前会员要删除的适用角色idList
     */
    private List<Long> subMemberRoleIdList;
}
