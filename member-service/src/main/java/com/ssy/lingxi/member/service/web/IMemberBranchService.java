package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.branch.MemberBranchAgentSaveOrUpdateReq;
import com.ssy.lingxi.member.model.req.branch.MemberBranchPageReq;
import com.ssy.lingxi.member.model.req.branch.MemberBranchSaveOrUpdateReq;
import com.ssy.lingxi.member.model.resp.branch.*;

import java.util.List;

/**
 * 会员店铺service
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/5/26
 */
public interface IMemberBranchService {

    /**
     * 分页查询会员店铺
     *
     * @param loginUser 登录用户
     * @param pageReq 查询参数
     * @return 响应结果
     */
    PageDataResp<MemberBranchPageResp> page(UserLoginCacheDTO loginUser, MemberBranchPageReq pageReq);

    /**
     * 获取会员店铺详情
     *
     * @param req 请求参数
     * @return 响应结果
     */
    MemberBranchDetailResp get(CommonIdReq req);

    /**
     * 代创建保存或更新店铺
     *
     * @param req 请求参数
     */
    void agentSaveOrUpdate(UserLoginCacheDTO loginUser, MemberBranchAgentSaveOrUpdateReq req);

    /**
     * 保存或更新店铺
     *
     * @param loginUser 登录用户
     * @param req 请求参数
     */
    void saveOrUpdate(UserLoginCacheDTO loginUser, MemberBranchSaveOrUpdateReq req);

    /**
     * 删除店铺
     *
     * @param req 请求参数
     */
    void delete(UserLoginCacheDTO sysUser, CommonIdReq req);

    /**
     * 获取会员店铺列表
     *
     * @return 会员店铺列表
     */
    List<MemberBranchResp> listMemberBranch(UserLoginCacheDTO loginUser);

    /**
     * 会员分页查询
     *
     * @param pageReq 会员名称
     * @return 会员信息
     */
    PageDataResp<MemberSimpleResp> memberPage(MemberPageReq pageReq);

    /**
     * 查询字典数据
     */
    MemberBranchSelectResp getDictDataList();

    /**
     * 绑定关系
     * @param req 请求参数
     */
    void bindRelation(UserLoginCacheDTO loginUser, MemberBranchRelationBindReq req);

    /**
     * 解除绑定关系
     *
     * @param loginUser 登录用户
     * @param req 请求参数
     */
    void unBindRelation(UserLoginCacheDTO loginUser, CommonIdReq req);


}
