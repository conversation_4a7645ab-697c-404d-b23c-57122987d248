package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.engine.ProcessEngineReq;
import com.ssy.lingxi.common.model.req.engine.RuleEngineProcessReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.engine.RuleEngineProcessResp;
import com.ssy.lingxi.member.entity.do_.lifecycle.BaseMemberCycleProcessDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberCycleProcessDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.PlatformMemberCycleProcessDO;
import com.ssy.lingxi.member.model.req.platform.*;
import com.ssy.lingxi.member.model.resp.platform.BaseMemberCycleProcessResp;
import com.ssy.lingxi.member.model.resp.platform.MemberCycleProcessDetailResp;
import com.ssy.lingxi.member.model.resp.platform.MemberCycleProcessPageResp;

import java.util.List;

/**
 * 会员生命周期变更流程服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
public interface IMemberCycleProcessService {

    /**
     * 保存默认流程
     * @param baseProcess 基础流程
     */
    void saveDefaultProcess(BaseMemberCycleProcessDO baseProcess, ProcessEngineReq engineBO);

    /**
     * 设置默认工作流
     * @param platformProcess 平台流程
     */
    void defaultProcess(PlatformMemberCycleProcessDO platformProcess);

    /**
     * 获取会员流程
     * @param memberId      会员ID
     * @param roleId        角色ID
     * @param processType   流程类型
     * @return MemberCycleProcessDO
     */
    List<MemberCycleProcessDO> getMemberProcess(Long memberId, Long roleId, Integer processType);

    /**
     * 分页查询流程规则
     * @param loginUser 登录用户信息
     * @param pageVO 分页查询条件
     * @return 查询结果
     */
    PageDataResp<MemberCycleProcessPageResp> pageProcesses(UserLoginCacheDTO loginUser, MemberCycleProcessPageQueryDataReq pageVO, Integer status);

    /**
     * 分页查询流程规则
     * @param loginUser 登录用户信息
     * @param pageVO 分页查询条件
     * @return 查询结果
     */
    PageDataResp<MemberCycleProcessPageResp> processPage(UserLoginCacheDTO loginUser, MemberCycleProcessPageQueryDataReq pageVO, Integer status);

    /**
     * 查询基础流程列表
     * @param loginUser 登录用户信息
     * @return 查询结果
     */
    List<BaseMemberCycleProcessResp> listBaseProcesses(UserLoginCacheDTO loginUser, ProcessQueryReq queryRequest);


    /**
     * 设置默认流程
     * @param loginUser 登录用户信息
     * @param defaultRequest 接口参数
     * @return Void
     */
    void saveDefault(UserLoginCacheDTO loginUser, SaveDefaultReq defaultRequest);

    /**
     * 新增流程规则
     * @param loginUser 登录用户信息
     * @param saveVO 流程规则
     * @return 新增结果
     */
    void save(UserLoginCacheDTO loginUser, MemberCycleProcessReq saveVO);

    /**
     * 查询流程规则详情
     * @param loginUser 登录用户信息
     * @param processId 流程id
     * @return 查询结果
     */
    MemberCycleProcessDetailResp getInfo(UserLoginCacheDTO loginUser, Long processId);

    /**
     * 修改流程规则
     * @param loginUser 登录用户信息
     * @param updateVO 修改数据
     * @return 修改结果
     */
    void update(UserLoginCacheDTO loginUser, MemberCycleProcessUpdateReq updateVO);

    /**
     * 修改流程状态
     * @param loginUser 登录用户信息
     * @param updateStatusVO 修改状态
     * @return 修改结果
     */
    void updateStatus(UserLoginCacheDTO loginUser, MemberCycleProcessUpdateStatusReq updateStatusVO);

    /**
     * 删除流程规则
     * @param loginUser 登录用户信息
     * @param processId 要删除的流程规则id
     * @return 删除结果
     */
    void delete(UserLoginCacheDTO loginUser, Long processId);

    /**
     * 查询流程规则引擎详情
     * @param loginUser 登录用户信息
     * @param request   查询参数
     * @return 查询结果
     */
    RuleEngineProcessResp getMemberCycleProcesses(UserLoginCacheDTO loginUser, RuleEngineProcessReq request);
}
