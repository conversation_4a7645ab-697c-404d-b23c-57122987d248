package com.ssy.lingxi.member.model.req.comment;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 会员评价接口查询参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@Data
public class PlatformMemberTradeCommentSummaryReq implements Serializable {
    private static final long serialVersionUID = 1000947942143905595L;

    /**
     * 会员id
     */
    @NotNull(message = "会员Id要大于0")
    @Positive(message = "会员Id要大于0")
    private Long memberId;

    /**
     * 会员角色id
     */
    @NotNull(message = "会员角色Id要大于0")
    @Positive(message = "会员角色Id要大于0")
    private Long roleId;
}
