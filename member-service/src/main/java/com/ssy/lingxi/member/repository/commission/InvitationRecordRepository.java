package com.ssy.lingxi.member.repository.commission;

import com.ssy.lingxi.member.entity.do_.commission.InvitationRecordDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 邀请记录Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Repository
public interface InvitationRecordRepository extends JpaRepository<InvitationRecordDO, Long>, JpaSpecificationExecutor<InvitationRecordDO> {

    /**
     * 根据邀请人用户id查询邀请记录列表
     * @param inviterUserId 邀请人用户id
     * @param pageable 分页参数
     * @return 邀请记录列表
     */
    Page<InvitationRecordDO> findByInviterUserIdOrderByCreateTimeDesc(Long inviterUserId, Pageable pageable);

    /**
     * 根据被邀请人用户id查询邀请记录
     * @param inviteeUserId 被邀请人用户id
     * @return 邀请记录
     */
    Optional<InvitationRecordDO> findByInviteeUserId(Long inviteeUserId);


    /**
     * 根据邀请状态查询邀请记录列表
     * @param status 邀请状态
     * @param pageable 分页参数
     * @return 邀请记录列表
     */
    Page<InvitationRecordDO> findByStatusOrderByCreateTimeDesc(Integer status, Pageable pageable);

    /**
     * 根据邀请人用户id和邀请状态查询邀请记录列表
     * @param inviterUserId 邀请人用户id
     * @param status 邀请状态
     * @param pageable 分页参数
     * @return 邀请记录列表
     */
    Page<InvitationRecordDO> findByInviterUserIdAndStatusOrderByCreateTimeDesc(Long inviterUserId, Integer status, Pageable pageable);

    /**
     * 根据邀请渠道查询邀请记录列表
     * @param invitationChannel 邀请渠道
     * @param pageable 分页参数
     * @return 邀请记录列表
     */
    Page<InvitationRecordDO> findByInvitationChannelOrderByCreateTimeDesc(Integer invitationChannel, Pageable pageable);

    /**
     * 查询指定时间范围内的邀请记录
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 邀请记录列表
     */
    @Query("SELECT ir FROM InvitationRecordDO ir WHERE ir.invitationTime >= :startTime AND ir.invitationTime <= :endTime ORDER BY ir.invitationTime DESC")
    Page<InvitationRecordDO> findByInvitationTimeBetween(@Param("startTime") Long startTime, @Param("endTime") Long endTime, Pageable pageable);

    /**
     * 查询未发放奖励的邀请记录
     * @param pageable 分页参数
     * @return 邀请记录列表
     */
    @Query("SELECT ir FROM InvitationRecordDO ir WHERE ir.status = 3 AND ir.rewardIssued = false")
    Page<InvitationRecordDO> findUnrewardedActivatedInvitations(Pageable pageable);

    /**
     * 统计邀请人的邀请数量
     * @param inviterUserId 邀请人用户id
     * @return 邀请数量
     */
    Long countByInviterUserId(Long inviterUserId);

    /**
     * 统计邀请人指定状态的邀请数量
     * @param inviterUserId 邀请人用户id
     * @param status 邀请状态
     * @return 邀请数量
     */
    Long countByInviterUserIdAndStatus(Long inviterUserId, Integer status);

    /**
     * 统计邀请人已激活的邀请数量
     * @param inviterUserId 邀请人用户id
     * @return 已激活的邀请数量
     */
    @Query("SELECT COUNT(ir) FROM InvitationRecordDO ir WHERE ir.inviterUserId = :inviterUserId AND ir.status = 3")
    Long countActivatedInvitationsByInviter(@Param("inviterUserId") Long inviterUserId);

    /**
     * 统计指定时间范围内的邀请数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 邀请数量
     */
    @Query("SELECT COUNT(ir) FROM InvitationRecordDO ir WHERE ir.invitationTime >= :startTime AND ir.invitationTime <= :endTime")
    Long countByInvitationTimeBetween(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 统计指定时间范围内的注册数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 注册数量
     */
    @Query("SELECT COUNT(ir) FROM InvitationRecordDO ir WHERE ir.registrationTime >= :startTime AND ir.registrationTime <= :endTime")
    Long countByRegistrationTimeBetween(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 查询邀请人的下级用户列表
     * @param inviterUserId 邀请人用户id
     * @return 下级用户id列表
     */
    @Query("SELECT ir.inviteeUserId FROM InvitationRecordDO ir WHERE ir.inviterUserId = :inviterUserId AND ir.status >= 2")
    List<Long> findInviteeUserIdsByInviter(@Param("inviterUserId") Long inviterUserId);
}
