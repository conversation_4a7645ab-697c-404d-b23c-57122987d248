package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.basic.MemberIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.basic.UpperMemberIdRoleIdReq;
import com.ssy.lingxi.member.model.req.info.*;
import com.ssy.lingxi.member.model.req.maintenance.MemberDetailCreditHistoryPageDataReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberUserIdListReq;
import com.ssy.lingxi.member.model.req.maintenance.MemberUserIdReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.UpperMemberShowResp;
import com.ssy.lingxi.member.model.resp.info.*;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberCreditComplaintPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRecordRectifyResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;
import org.springframework.http.HttpHeaders;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员能力 - 会员信息管理查询服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-05
 */
public interface IMemberAbilityInfoService {

    /**
     * 获取分页查询页面下拉框内容
     * @param headers Http头部信息
     * @return 下拉框内容
     */
    MemberInfoSearchConditionResp getPageSearchConditions(HttpHeaders headers);

    /**
     * 分页、模糊查询归属会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<UpperMemberInfoResp> pageUpperMembers(HttpHeaders headers, MemberInfoPageDataReq pageVO, Integer roleTag);

    /**
     * “申请会员”页面，查询按钮状态和文本
     * @param headers Http头部信息
     * @param conditionVO 接口参数
     * @return 查询结果
     */
    MemberInfoApplyButtonResp getApplyCondition(HttpHeaders headers, MemberApplyConditionReq conditionVO);

    /**
     * “邀请会员”页面，查询按钮状态和文本
     * @param headers Http头部信息
     * @param conditionVO 接口参数
     * @return 查询结果
     */
    MemberInfoInviteButtonResp getInviteCondition(HttpHeaders headers, MemberInviteConditionDataReq conditionVO);

    /**
     * “申请会员”页面，会员注册资料信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberInfoApplyRegisterDetailResp getApplyRegisterDetail(HttpHeaders headers, UpperMemberIdRoleIdReq idVO);

    /**
     * “申请会员”页面，会员入库资料信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberInfoApplyDepositDetailResp getApplyDepositDetail(HttpHeaders headers, UpperMemberIdRoleIdReq idVO);

    /**
     * “申请会员” - 提交
     * @param headers Http头部信息
     * @param subVO 接口参数
     */
    void applyToBeSubMember(HttpHeaders headers, MemberInfoApplyForSubReq subVO);

    /**
     * 获取“修改注册信息”页面，会员注册资料信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberInfoUpdateDetailResp getMemberRegisterDetail(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 修改会员注册信息
     * @param headers Http头部信息
     * @param detailVO 接口参数
     */
    void updateMemberRegisterDetail(HttpHeaders headers, MemberInfoUpdateRegisterDetailReq detailVO);

    /**
     * 获取“修改入库信息”页面，会员入库资料信息
     * @param headers Http头部信息
     * @param idVO 会员关系Id
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberInfoUpdateDepositDetailResp getMemberDepositDetail(HttpHeaders headers, ValidateIdReq idVO, Integer roleTag);

    /**
     * 修改会员入库信息
     * @param headers Http头部信息
     * @param detailVO 接口参数
     * @param roleTag 角色标签
     */
    void updateMemberDepositDetail(HttpHeaders headers, MemberInfoUpdateDepositDetailReq detailVO, Integer roleTag);

    /**
     * 会员详情 - 会员基本信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag
     * @return 查询结果
     */
    MemberInfoBasicDetailResp getMemberBasicDetail(HttpHeaders headers, ValidateIdReq idVO, Integer roleTag);

    /**
     * 会员详情 - 会员档案信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberInfoDepositDetailResp getMemberArchives(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情- 会员档案 - 分页查询考评信息
     * @param headers HttpHeader信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberAppraisalPageQueryResp> pageMemberAppraisal(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员档案 - 分页查询整改信息
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberRecordRectifyResp> pageMemberRecordRectify(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员等级信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberValidateDetailLevelResp getMemberDetailLevel(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员等级信息 - 分页查询交易分获取记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailLevelHistoryResp> pageMemberLevelDetailHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员权益信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberDetailRightResp getMemberDetailRight(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益获取记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightHistoryResp> pageMemberDetailRightHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员权益信息 - 分页查询会员权益使用记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailRightSpendHistoryResp> pageMemberDetailRightSpendHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberDetailCreditResp getMemberDetailCredit(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditTradeCommentSummary(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员信用 - 分页查询交易评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditTradeHistoryResp> pageMemberDetailCreditTradeCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberDetailCreditCommentSummaryResp getMemberDetailCreditAfterSaleCommentSummary(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员信用 - 分页查询售后评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberDetailCreditAfterSaleHistoryResp> pageMemberDetailCreditAfterSaleCommentHistory(HttpHeaders headers, MemberDetailCreditHistoryPageDataReq pageVO);

    /**
     * 会员详情 - 会员信用 - 投诉汇总
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberDetailCreditComplainSummaryResp getMemberDetailCreditComplainSummary(HttpHeaders headers, ValidateIdReq idVO);

    /**
     * 会员详情 - 会员信用 - 分页查询投诉历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberCreditComplaintPageQueryResp> pageMemberDetailCreditComplainHistory(HttpHeaders headers, ValidateIdPageDataReq pageVO);

    /**
     * “增加会员角色”功能，查询上级会员列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    UpperMemberShowResp getUpperMemberInfo(HttpHeaders headers);

    /**
     * “增加会员角色”功能，查询会员类型列表
     * @param headers Http头部信息
     * @param memberId 会员id
     * @return 查询结果
     */
    List<MemberTypeAndNameResp> getMemberTypeList(HttpHeaders headers, Long memberId);

    /**
     * “增加会员角色”功能，根据会员类型Id查询角色列表
     * @param headers Http头部信息
     * @param memberTypeReq 接口参数
     * @return 查询结果
     */
    MemberInfoRoleListResp getRoleListByMemberType(HttpHeaders headers, MemberTypeReq memberTypeReq);

    /**
     * “增加会员角色”功能，会获取员注册资料信息
     * @param headers Http头部信息
     * @param roleIdReq 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberInfoUpdateDetailByRoleResp getMemberRegisterDetailAfterAddRole(HttpHeaders headers, RoleIdReq roleIdReq, Integer roleTag);

    /**
     * “增加会员角色”功能，提交注册资料并新增角色
     * @param headers Http头部信息
     * @param addRoleVO 接口参数
     * @return 新增结果
     */
    void addMemberRole(HttpHeaders headers, MemberInfoAddRoleReq addRoleVO);

    /**
     * 根据会员id查询下属用户的im权限
     *
     * @param memberIdReq 请求参数
     * @return 拥有im权限的用户id列表
     */
    List<MemberImAuthUsersResp> getHasImAuthUsers(MemberIdReq memberIdReq);

    /**
     * 根据userId查询是否可以发起im聊天
     *
     * @param userIdReq 请求参数
     * @return 拥有im权限的用户id列表
     */
    Boolean checkHasImAuth(@Valid MemberUserIdReq userIdReq);

    /**
     * 根据userIdList查询相关信息
     *
     * @param userIdListReq 请求参数
     * @return 拥有im权限的用户id列表
     */
    List<MemberUserImAuthInfoResp> getImAuthInfo(@Valid MemberUserIdListReq userIdListReq);

    /**
     * 根据token查询登陆相关信息
     *
     * @param headers Http头部信息
     * @return 登陆用户相关信息
     */
    MemberLoginInfoResp getLoginInfoByToken(HttpHeaders headers);

    /**
     * 获取所有的平台客服用户
     *
     * @return 获取所有的平台客服用户列表
     */
    List<MemberImAuthUsersResp> getPlatformImUsers();

    /**
     * 判断指定用户是否为平台客服
     *
     * @param userIdReq
     * @return
     */
    Boolean checkPlatformImUser(MemberUserIdReq userIdReq);
}
