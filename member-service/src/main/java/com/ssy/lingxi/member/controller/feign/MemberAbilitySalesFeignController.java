package com.ssy.lingxi.member.controller.feign;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberAbilitySalesFeign;
import com.ssy.lingxi.member.api.model.req.MemberSalesCountFeignReq;
import com.ssy.lingxi.member.api.model.req.MemberSalesFindMemberReq;
import com.ssy.lingxi.member.api.model.req.MemberSalesFindUserIdReq;
import com.ssy.lingxi.member.api.model.resp.*;
import com.ssy.lingxi.member.service.feign.IMemberAbilitySalesFeignService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 业务员相关内部Feign接口
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-14
 * @ignore 不需要提交到Yapi
 */
@RestController
public class MemberAbilitySalesFeignController implements IMemberAbilitySalesFeign {

    @Resource
    private IMemberAbilitySalesFeignService memberAbilitySalesService;

    /**
     * 业绩统计
     *
     * @param pageVO 接口参数
     * @return 返回符合条件的业务员列表和下级会员
     */
    @Override
    public WrapperResp<PageDataResp<MemberFeignSalesCountResp>> memberSalesList(@RequestBody @Valid MemberSalesCountFeignReq pageVO) {
        return WrapperUtil.success(memberAbilitySalesService.getMemberSalesList(pageVO));
    }

    /**
     * 获取角色类型为消费者的角色Id
     *
     * @return 返回角色类型为消费者的角色Id
     */
    @Override
    public WrapperResp<List<Long>> roleIds() {
        return WrapperUtil.success(memberAbilitySalesService.getRoleIds());
    }

    /**
     * 根据业务员Id查询下级会员信息
     *
     * @param pageVO 查询条件
     * @return 返回下级会员信息
     */
    @Override
    public WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> findByUserId(@RequestBody @Valid MemberSalesFindUserIdReq pageVO) {
        return WrapperUtil.success(memberAbilitySalesService.findByUserId(pageVO));
    }

    /**
     * 根据当前账号以及搜索条件查找下级渠道会员
     *
     * @param pageVO 查询条件
     * @return 返回业务员和下级会员信息
     */
    @Override
    public WrapperResp<List<MemberSalesFindMemberQueryResp>> findMemberSales(@RequestBody @Valid MemberSalesFindMemberReq pageVO) {
        return WrapperUtil.success(memberAbilitySalesService.findMemberSales(pageVO));
    }

    /**
     * 订单能力-业务员业绩详情-查看业务员详情
     *
     * @param headers 头部信息
     * @param userId  业务员Id
     * @return 返回业务员信息
     */
    @Override
    public WrapperResp<MemberSalesChannelFindUserIdQueryResp> getChannelInformation(@RequestHeader HttpHeaders headers, @NotNull Long userId) {
        return WrapperUtil.success(memberAbilitySalesService.getChannelInformation(headers, userId));
    }

    /**
     * 根据数据权限找出符合条件的业务员信息Id
     *
     * @param pageVO 头部信息
     * @return 返回业务员Id和名字
     */
    @Override
    public WrapperResp<List<MemberUserResp>> getUserIds(@RequestBody @Valid MemberSalesCountFeignReq pageVO) {
        return WrapperUtil.success(memberAbilitySalesService.getUserIds(pageVO));
    }
}
