package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.model.req.basic.MemberIdAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.resp.basic.LevelAndTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import org.springframework.http.HttpHeaders;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 平台后台 - 会员维护相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-07
 */
public interface IPlatformMemberMaintenanceService {

    /**
     * 获取分页查询页面中各个查询条件下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    PlatformMemberQuerySearchConditionResp getPageCondition(HttpHeaders headers);

    /**
     * 获取会员导入Excel模板
     * @param request Http请求
     * @param response Http响应（文件流）
     */
    void getMemberImportFile(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入会员
     * @param headers Http头部信息
     * @param excelFile 上传的文件流
     * @return 操作结果
     */
    void importMembers(HttpHeaders headers, MultipartFile excelFile);

    /**
     * 查询导入批次列表
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    WrapperResp<List<String>> getImportBatchNo(HttpHeaders headers);

    /**
     * 根据批次号，删除批量导入的会员
     * @param headers HttpHeaders信息
     * @param memberVO 接口参数
     * @return 操作结果
     */
    void deleteMembersByBatchNo(HttpHeaders headers, BatchDeleteMemberReq memberVO);

    /**
     * 分页查询会员
     * @param headers HttpHeaders信息
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    PageDataResp<PlatformPageQueryMemberResp> pageMembers(HttpHeaders headers, PlatformMemberQueryDataReq memberQueryVO);

    /**
     * 获取新增会员页面内容
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    PlatformAddMemberPageItemsResp getAddMemberPageItems(HttpHeaders headers);

    /**
     * 根据会员类型，查询角色列表
     * @param headers HttpHeader信息
     * @param memberTypeReq 接口参数
     * @return 操作结果
     */
    List<RoleIdAndNameResp> getAddMemberPageRoles(HttpHeaders headers, MemberTypeReq memberTypeReq);

    /**
     * 新增会员页面，根据会员类型和角色，查询等级列表
     * @param headers HttpHeader信息
     * @param typeAndRoleIdVO 接口参数
     * @return 操作结果
     */
    List<LevelAndTagResp> getAddMemberPageLevels(HttpHeaders headers, MemberTypeAndRoleIdReq typeAndRoleIdVO);

    /**
     * 新增会员页面，根据选择的角色，返回会员注册资料信息
     * @param headers HttpHeader信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    List<MemberConfigGroupResp> getAddMemberPageMemberConfigItems(HttpHeaders headers, RoleIdReq idVO);

    /**
     * 新增会员
     * @param headers HttpHeaders信息
     * @param memberVO 接口参数
     * @return 新增结果
     */
    void addMember(HttpHeaders headers, PlatformAddMemberReq memberVO);

    /**
     * 新增会员页面，查询用户基本信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 会员基本信息
     */
    PlatformMemberMaintenanceMemberDetailResp getMemberDetail(HttpHeaders headers, MemberValidateReq validateVO);

    /**
     * 会员详情 - 会员信息查询
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    MemberDetailResp getMemberDetail(HttpHeaders headers, MemberIdAndRoleIdReq memberIdAndRoleIdReq);

    /**
     * 修改会员
     * @param headers Http头部信息
     * @param memberVO 接口参数
     * @return 修改结果
     */
    void updateMemberDetail(HttpHeaders headers, PlatformUpdateMemberReq memberVO);

    /**
     * 冻结/解冻会员
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    void changeMemberStatus(HttpHeaders headers, ChangeMemberStatusReq statusVO);

    /**
     * 删除会员
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 删除结果
     */
    void deleteMember(HttpHeaders headers, MemberValidateReq validateVO);
}
