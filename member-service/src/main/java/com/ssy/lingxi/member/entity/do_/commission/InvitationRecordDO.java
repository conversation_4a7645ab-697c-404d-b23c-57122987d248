package com.ssy.lingxi.member.entity.do_.commission;

import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 邀请记录表
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Data
@Entity
@FieldNameConstants
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MEMBER_SERVICE + "invitation_record")
public class InvitationRecordDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 邀请人用户id
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long inviterUserId;


    /**
     * 被邀请人用户id
     */
    @Column(columnDefinition = "int8")
    private Long inviteeUserId;

    /**
     * 被邀请人用户编码
     */
    @Column(columnDefinition = "varchar(200)")
    private String inviteeUserCode;

    /**
     * 被邀请用户关联（用于查询）
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inviteeUserId", referencedColumnName = "id", insertable = false, updatable = false)
    private UserDO inviteeUser;

    /**
     * 邀请渠道: 1-直接注册, 2-邀请链接, 3-二维码, 4-其他
     */
    @Column(columnDefinition = "int", nullable = false)
    private Integer invitationChannel = 1;

    /**
     * 邀请状态: 1-已邀请已注册, 2-已注册已认证, 3-已认证已下单
     */
    @Column(columnDefinition = "int", nullable = false)
    private Integer status = 1;

    /**
     * 邀请时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long invitationTime;

    /**
     * 注册时间
     */
    @Column(columnDefinition = "int8")
    private Long registrationTime;

    /**
     * 邀请奖励是否已发放: true-已发放, false-未发放
     */
    @Column(columnDefinition = "boolean", nullable = false)
    private Boolean rewardIssued = false;


    /**
     * 备注
     */
    @Column(columnDefinition = "varchar(500)")
    private String remark;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(columnDefinition = "int8", nullable = false)
    private Long updateTime;
}
