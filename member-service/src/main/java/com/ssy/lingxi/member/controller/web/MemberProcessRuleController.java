package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.NamePageDataReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdPageDataReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.basic.BaseMemberProcessResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigNameResp;
import com.ssy.lingxi.member.model.resp.basic.RoleManageResp;
import com.ssy.lingxi.member.model.resp.validate.MemberProcessQueryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberProcessResp;
import com.ssy.lingxi.member.service.web.IMemberProcessRuleService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员能力 - 会员管理流程规则配置相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-17
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/process/rule")
public class MemberProcessRuleController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IMemberProcessRuleService memberProcessRuleService;

    /**
     * 分页查询会员流程规则配置列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberProcessQueryResp>> pageMemberProcessRules(@RequestHeader HttpHeaders headers, @Valid NamePageDataReq pageVO) {
        return WrapperUtil.success(memberProcessRuleService.pageMemberProcessRules(headers, pageVO));
    }

    /**
     * 查询入库流程、变更流程列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/base/list")
    public WrapperResp<List<BaseMemberProcessResp>> listBaseMemberProcesses(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberProcessRuleService.listBaseMemberProcesses(headers, roleTag));
    }

    /**
     * 查询角色列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/role/page")
    public WrapperResp<PageDataResp<RoleManageResp>> pageRoles(@RequestHeader HttpHeaders headers, @Valid NamePageDataReq pageVO) {
        return WrapperUtil.success(memberProcessRuleService.pageRoles(headers, pageVO));
    }

    /**
     * 查询平台默认注册资料
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/role/config/page")
    public WrapperResp<PageDataResp<MemberConfigNameResp>> pageRoleConfigDetail(@RequestHeader HttpHeaders headers, @Valid RoleIdPageDataReq pageVO) {
        return WrapperUtil.success(memberProcessRuleService.pageRoleConfigDetail(headers, pageVO));
    }

    /**
     * 选择注册资料
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/config/page")
    public WrapperResp<PageDataResp<MemberConfigNameResp>> pageConfigDetail(@RequestHeader HttpHeaders headers, @Valid RoleIdPageDataReq pageVO) {
        return WrapperUtil.success(memberProcessRuleService.pageConfigDetail(headers, pageVO));
    }

    /**
     * 预览注册资料
     * @param headers Http头部信息
     * @param previewVO 接口参数
     * @return 预览结果
     */
    @PostMapping("/config/preview")
    public WrapperResp<List<MemberConfigGroupResp>> previewMemberDepositoryDetails(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberProcessPreviewReq previewVO) {
        return WrapperUtil.success(memberProcessRuleService.previewMemberDepositoryDetails(headers, previewVO));
    }

    /**
     * 新增流程规则配置
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/add")
    public WrapperResp<Void> addMemberProcessRule(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberProcessAddReq addVO) {

        memberProcessRuleService.addMemberProcessRule(headers, addVO);
        return WrapperUtil.success();
    }

    /**
     * 修改流程规则配置
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> updateMemberProcessRule(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberProcessUpdateReq updateVO) {
        memberProcessRuleService.updateMemberProcessRule(headers, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 查询流程规则配置详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberProcessResp> getMemberProcessRule(@RequestHeader HttpHeaders headers, @Valid MemberProcessIdReq idVO) {
        return WrapperUtil.success(memberProcessRuleService.getMemberProcessRule(headers, idVO));
    }

    /**
     * 分页查询流程规则配置关联的入库资料
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/deposit/detail/page")
    public WrapperResp<PageDataResp<MemberConfigNameResp>> pageMemberProcessDepositDetails(@RequestHeader HttpHeaders headers, @Valid MemberProcessDepositDetailPageDataReq pageVO) {
        return WrapperUtil.success(memberProcessRuleService.pageMemberProcessDepositDetails(headers, pageVO));
    }


    /**
     * 修改流程规则状态
     * @param headers Http头部信息
     * @param statusVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/update/status")
    public WrapperResp<Void> updateMemberProcessRuleStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberProcessIdAndStatusReq statusVO) {
         memberProcessRuleService.updateMemberProcessRuleStatus(headers, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 删除流程规则
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 删除结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> deleteMemberProcessRule(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberProcessIdReq idVO) {
         memberProcessRuleService.deleteMemberProcessRule(headers, idVO);
        return WrapperUtil.success();
    }
}
