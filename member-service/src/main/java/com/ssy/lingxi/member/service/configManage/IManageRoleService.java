package com.ssy.lingxi.member.service.configManage;

import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.configManage.*;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameAndMemberTypeResp;
import com.ssy.lingxi.member.model.resp.configManage.*;

import java.util.List;


/**
 * 业务平台 - 角色管理服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-13
 */
public interface IManageRoleService {
    /**
     * 查询会员角色类型
     *
     * @return 注册结果
     */
    List<MemberConfigEnumResp> getMemberRoleTypeList();

    /**
     * 查询会员类型
     *
     * @return 注册结果
     */
    List<MemberConfigEnumResp> getMemberTypeList();

    /**
     * 查询会员角色标签
     *
     * @return 注册结果
     */
    List<MemberConfigEnumResp> getMemberRoleTagList();

    /**
     * 基于会员类型查询会员角色
     *
     * @param memberType 会员类型
     * @return 注册结果
     */
    List<RoleIdAndNameAndMemberTypeResp> getMemberRoleListByMemberType(Integer memberType);

    /**
     * 分页查询会员角色列表
     *
     * @param memberRolePageReq 接口参数
     * @return 分页查询结果
     */
    PageDataResp<MemberRolePageResp> getMemberRolePage(MemberRolePageDataReq memberRolePageReq);

    /**
     * 查询角色详情
     *
     * @param commonIdReq 接口参数
     * @return 会员角色信息
     */
    MemberRoleResp getMemberRoleById(CommonIdReq commonIdReq);

    /**
     * 查询角色关联的注册资料
     *
     * @param commonIdReq 接口参数
     * @return 注册资料
     */
    List<MemberRoleConfigResp> getRegisterConfigByMemberRoleId(CommonIdReq commonIdReq);

    /**
     * 新增角色
     *
     * @param manageMemberRoleReq 接口参数
     * @return 操作结果
     */
    Long addRole(ManageMemberRoleReq manageMemberRoleReq);

    /**
     * 更新角色
     *
     * @param manageMemberRoleReq 接口参数
     * @return 操作结果
     */
    void updateRole(ManageMemberRoleReq manageMemberRoleReq);

    /**
     * 删除角色
     *
     * @param manageMemberRoleReq 接口参数
     * @return 操作结果
     */
    void deleteRole(ManageMemberRoleReq manageMemberRoleReq);

    /**
     * 更改角色状态
     *
     * @param manageMemberRoleReq 接口参数
     * @return 操作结果
     */
    void updateRoleStatus(ManageMemberRoleReq manageMemberRoleReq);

    /**
     * 设置角色关联的配置资料
     *
     * @param roleConfigReq 接口参数
     * @return 操作结果
     */
    void setRoleConfig(ManageMemberRoleConfigReq roleConfigReq);

    /**
     * 新增或修改会员角色关联的审核流程
     *
     * @param processRuleReq 接口参数
     * @return 操作结果
     */
    void setMemberRoleRegisterProcess(ProcessRuleReq processRuleReq);

    /**
     * 查询会员角色关联的审核流程
     *
     * @param commonIdReq 接口参数
     * @return 已配置的流程Id，以及所有会员审核（注册）流程列表
     */
    MemberRoleRegisterProcessResp findMemberRoleRegisterProcess(CommonIdReq commonIdReq);

    // *******************************************    角色菜单相关接口    *******************************************

    /**
     * 查询角色权限树（树形方式返回）
     *
     * @param authMenuReq 接口参数
     * @return 操作结果
     */
    AuthTreeResp getMemberRoleAuth(MemberRoleAuthTreeReq authMenuReq);

    /**
     * 设置角色菜单按钮权限
     *
     * @param menuButtonAuthReq 接口参数
     * @return 操作结果
     */
    void setMemberRoleMenuButtonAuth(MemberRoleMenuButtonAuthReq menuButtonAuthReq);

    /**
     * 设置菜单及所有关联的按钮权限
     *
     * @param commonIdReq 接口参数
     * @return 操作结果
     */
    void setAllMenuAndButtons(CommonIdReq commonIdReq);

    /**
     * 平台后台权限初始化接口
     */
    void initPlatformAdminAuth();
}
