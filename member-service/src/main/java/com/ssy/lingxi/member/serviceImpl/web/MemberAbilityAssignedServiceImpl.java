package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.enums.MemberInnerStatusEnum;
import com.ssy.lingxi.member.enums.MemberOuterStatusEnum;
import com.ssy.lingxi.member.enums.MemberRegisterSourceEnum;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityAssignedMemberQueryDataReq;
import com.ssy.lingxi.member.model.resp.basic.LevelAndTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberTypeAndNameResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberAssignedPageQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberAssignedSearchConditionVO;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberLevelConfigService;
import com.ssy.lingxi.member.service.base.IBaseMemberValidateService;
import com.ssy.lingxi.member.service.base.IBaseSiteService;
import com.ssy.lingxi.member.service.web.IMemberAbilityAssignedService;
import com.ssy.lingxi.member.service.web.IPlatformMemberRoleRuleService;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员能力-会员管理-待分配会员相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-29
 **/
@Service
public class MemberAbilityAssignedServiceImpl implements IMemberAbilityAssignedService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private IPlatformMemberRoleRuleService roleRuleService;

    @Resource
    private IBaseSiteService siteService;


    private final List<MemberOuterStatusEnum> outerStatusEnums = Stream.of(
            MemberOuterStatusEnum.DEPOSITING,
            MemberOuterStatusEnum.DEPOSITORY_PASSED,
            MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED,
            MemberOuterStatusEnum.MODIFYING,
            MemberOuterStatusEnum.MODIFY_PASSED,
            MemberOuterStatusEnum.MODIFY_NOT_PASSED
    ).collect(Collectors.toList());

    /**
     * 获取分页查询会员列表页面中各个查询条件下拉选择框的内容
     *
     * @param headers HttpHeaders信息
     * @param loginUser 登录用户
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public MemberAssignedSearchConditionVO getPageCondition(HttpHeaders headers, UserLoginCacheDTO loginUser, Integer roleTag) {
        //规则：
        MemberAssignedSearchConditionVO conditionVO = new MemberAssignedSearchConditionVO();
        //内部状态
        List<DropdownItemResp> itemList = MemberInnerStatusEnum.toDropdownItemResps();
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setInnerStatus(itemList);

        //外部状态
        itemList = outerStatusEnums.stream().map(e -> new DropdownItemResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setOuterStatus(itemList);

        //注册来源
        itemList = MemberRegisterSourceEnum.toAbilityDropdownList();
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setSources(itemList);

        //会员状态
        itemList = Arrays.stream(MemberStatusEnum.values()).sorted(Comparator.comparingInt(MemberStatusEnum::getCode)).map(e -> new DropdownItemResp(e.getCode(), e.getName())).collect(Collectors.toList());
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setStatus(itemList);

        //获取是否开启saas多租户部署
        Boolean enableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        List<MemberTypeAndNameResp> memberTypeList;
        List<RoleIdAndNameResp> roleList;
        if (enableMultiTenancy) {//有开启
            //会员类型（这里返回的是Id）
            memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.subMemberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
            //会员角色（按照Id升序排序）
            roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.subMemberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
        } else {//未开启
            //会员类型（这里返回的是Id）
            memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType());
            //会员角色（按照Id升序排序）
            roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), roleTag);
        }
        memberTypeList.add(0, new MemberTypeAndNameResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setMemberTypes(memberTypeList);
        roleList.add(0, new RoleIdAndNameResp(0L, MemberStringEnum.ALL.getName()));
        conditionVO.setRoles(roleList);

        //会员等级
        List<LevelAndTagResp> levelList = baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId());
        levelList.add(0, new LevelAndTagResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setLevels(levelList);

        return conditionVO;
    }

    /**
     * 分页、模糊查询会员
     *
     * @param loginUser 登录用户
     * @param queryVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberAssignedPageQueryResp> pageMembers(UserLoginCacheDTO loginUser, MemberAbilityAssignedMemberQueryDataReq queryVO, Integer roleTag) {
        Specification<MemberRelationDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));
            //需要筛选出当前还没有被领取的下级会员数据   即上级用户id为null或者为0
            list.add(criteriaBuilder.or(root.get("userId").as(Long.class).isNull(), criteriaBuilder.equal(root.get("userId").as(Long.class), 0L)));

            //注册开始时间
            if (StringUtils.hasLength(queryVO.getStartDate())) {
                LocalDateTime startDate = LocalDateTime.parse(queryVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), startDate));
            }

            //注册结束时间
            if (StringUtils.hasLength(queryVO.getEndDate())) {
                LocalDateTime endDate = LocalDateTime.parse(queryVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), endDate));
            }

            //内部状态
            if (NumberUtil.isNullOrZero(queryVO.getInnerStatus())) {
                list.add(criteriaBuilder.notEqual(root.get("innerStatus").as(Integer.class), MemberInnerStatusEnum.NEW.getCode()));
            } else {
                list.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), queryVO.getInnerStatus()));
            }

            //外部状态
            if (NumberUtil.isNullOrZero(queryVO.getOuterStatus())) {
                list.add(root.get("outerStatus").as(Integer.class).in(outerStatusEnums.stream().map(MemberOuterStatusEnum::getCode).collect(Collectors.toList())));
            } else {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), queryVO.getOuterStatus()));
            }


            //会员状态
            if (NumberUtil.notNullOrZero(queryVO.getStatus())) {
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), queryVO.getStatus()));
            }

            //会员角色
            if (NumberUtil.notNullOrZero(queryVO.getRoleId())) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), queryVO.getRoleId()));
            }

            //会员名称、注册来源
            if (StringUtils.hasLength(queryVO.getName()) || NumberUtil.notNullOrZero(queryVO.getSource())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                if (StringUtils.hasLength(queryVO.getName())) {
                    list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + queryVO.getName().trim() + "%"));
                }

                if (NumberUtil.notNullOrZero(queryVO.getSource())) {
                    list.add(criteriaBuilder.equal(subMemberJoin.get("source").as(Integer.class), queryVO.getSource()));
                }
            }

            //会员等级
            if (NumberUtil.notNullOrZero(queryVO.getLevel())) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), queryVO.getLevel()));
            }

            // 会员类型
            if (NumberUtil.notNullOrZero(queryVO.getMemberType())) {
                Join<Object, Object> memberTypeJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(memberTypeJoin.get("memberType"), queryVO.getMemberType()));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), roleTag));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(queryVO.getCurrent() - 1, queryVO.getPageSize(), Sort.by("createTime").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(spec, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberAssignedPageQueryResp memberVO = new MemberAssignedPageQueryResp();
            memberVO.setMemberId(relationDO.getSubMember().getId());
            memberVO.setValidateId(relationDO.getId());
            memberVO.setName(relationDO.getSubMember().getName());
            memberVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            memberVO.setRoleId(relationDO.getSubRoleId());
            memberVO.setRoleName(relationDO.getSubRoleName());
            memberVO.setSourceName(MemberRegisterSourceEnum.getCodeMessage(relationDO.getSubMember().getSource()));
            memberVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            memberVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
            memberVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            memberVO.setStatus(relationDO.getStatus());
            memberVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
            memberVO.setInnerStatus(relationDO.getInnerStatus());
            memberVO.setInnerStatusName(SecurityStringUtil.replaceMemberPrefix(MemberInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()), roleTag));
            memberVO.setOuterStatus(relationDO.getOuterStatus());
            memberVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
            memberVO.setDepositTime(relationDO.getDepositTime() == null ? "" : relationDO.getDepositTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            return memberVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 操作用户领取会员
     *
     * @param loginUser     登录用户信息
     * @param subMemberList 接口参数
     * @return 操作结果
     */
    @Override
    @Transactional
    public void bindOperator(UserLoginCacheDTO loginUser, List<SubMemberIdRoleIdDataReq> subMemberList) {
        UserDO userDO = userRepository.findById(loginUser.getUserId()).orElse(null);
        if (userDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        if (userDO.getUserAuth() == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_USER_AUTH_DOES_NOT_EXIST);
        }

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));

            List<Predicate> orList = new ArrayList<>();
            subMemberList.forEach(subMember -> orList.add(criteriaBuilder.and(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), subMember.getSubMemberId()), criteriaBuilder.equal(root.get("subRoleId").as(Long.class), subMember.getSubRoleId()))));
            Predicate[] orPredicates = new Predicate[orList.size()];
            list.add(criteriaBuilder.or(orList.toArray(orPredicates)));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        List<MemberRelationDO> relationDOList = relationRepository.findAll(specification);
        relationDOList = relationDOList.stream().peek(relationDO -> relationDO.setUserId(loginUser.getUserId())).collect(Collectors.toList());
        relationRepository.saveAll(relationDOList);


    }
}
