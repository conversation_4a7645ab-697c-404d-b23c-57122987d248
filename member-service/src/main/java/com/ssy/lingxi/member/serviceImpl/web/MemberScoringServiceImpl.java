package com.ssy.lingxi.member.serviceImpl.web;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.entity.do_.lifecycle.*;
import com.ssy.lingxi.member.enums.ScoringTemplateStateEnum;
import com.ssy.lingxi.member.enums.ScoringTemplateTypeEnum;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.repository.MemberScoringIndicatorRepository;
import com.ssy.lingxi.member.repository.MemberScoringTemplateIndicatorRepository;
import com.ssy.lingxi.member.repository.MemberScoringTemplateRepository;
import com.ssy.lingxi.member.service.web.IMemberScoringService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 评分模板服务实现类
 * <AUTHOR>
 * @since 2022/6/26 21:01
 * @version 1.0
 */
@Service
public class MemberScoringServiceImpl implements IMemberScoringService {

    @Resource
    private MemberScoringIndicatorRepository memberScoringIndicatorRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private MemberScoringTemplateIndicatorRepository memberScoringTemplateIndicatorRepository;

    @Resource
    private MemberScoringTemplateRepository memberScoringTemplateRepository;

    private static final String WEIGHT_THRESHOLD = "100.0";

    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#.0");

    /**
     * 指标标准定义 - 评分标准提交
     * @param loginUser 登录用户信息
     * @param addVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Transactional
    @Override
    public void submitScoringIndicator(UserLoginCacheDTO loginUser, List<MemberScoringIndicatorAddReq> addVO, Integer roleTag) {
        for (MemberScoringIndicatorAddReq vo : addVO) {
            if (vo.getScoreMin() > vo.getScoreMax()) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_INDICATOR_SCORE_MAX_LESS_THAN);
            }
        }

        if (addVO.stream().distinct().count() != addVO.size()) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_INDICATOR_SCORE_REPEAT);
        }

        List<MemberScoringIndicatorDO> memberScoringIndicatorDOList = getMemberScoringIndicatorList(loginUser, roleTag);

        // 待更新id
        List<Long> updIds = addVO.stream().map(MemberScoringIndicatorAddReq::getId).filter(Objects::nonNull).collect(Collectors.toList());

        // 待删除
        List<Long> delIds = memberScoringIndicatorDOList.stream().map(MemberScoringIndicatorDO::getId).filter(id -> !updIds.contains(id)).collect(Collectors.toList());

        // 待新增或更新
        List<MemberScoringIndicatorAddReq> saveOrUpdateList = addVO.stream().filter(s -> !delIds.contains(s.getId())).collect(Collectors.toList());

        // 保存评分标准
        saveScoringIndicator(saveOrUpdateList, loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);

        // 删除评分标准
        memberScoringIndicatorRepository.deleteByIdIn(delIds);


    }

    /**
     * 查询当前用户的评分配置
     * @param loginUser 登录用户信息
     * @param roleTag 角色标签
     * @return 查询结果
     */
    private List<MemberScoringIndicatorDO> getMemberScoringIndicatorList(UserLoginCacheDTO loginUser, Integer roleTag) {
        return Optional.of(memberScoringIndicatorRepository.findAll((root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));

            if (NumberUtil.notNullOrZero(roleTag)) {
                predicateList.add(cb.equal(root.get("roleTag").as(Integer.class), roleTag));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        })).orElse(new ArrayList<>());
    }

    /**
     * 保存评分标准
     * @param saveOrUpdateList 待新增或更新列表
     * @param memberId 会员id
     * @param roleId 角色id
     * @param roleTag 角色标签
     */
    @Override
    public void saveScoringIndicator(List<MemberScoringIndicatorAddReq> saveOrUpdateList, Long memberId, Long roleId, Integer roleTag) {
        List<MemberScoringIndicatorDO> memberScoringIndicatorDOList = new ArrayList<>();
        // 排序
        int index = 1;
        for (MemberScoringIndicatorAddReq vo : saveOrUpdateList) {
            MemberScoringIndicatorDO memberScoringIndicatorDO = new MemberScoringIndicatorDO();
            memberScoringIndicatorDO.setId(vo.getId());
            memberScoringIndicatorDO.setMemberId(memberId);
            memberScoringIndicatorDO.setRoleId(roleId);
            memberScoringIndicatorDO.setRoleTag(roleTag);
            memberScoringIndicatorDO.setIndicatorGrouping(vo.getIndicatorGrouping());
            memberScoringIndicatorDO.setStandardIndicator(vo.getStandardIndicator());
            memberScoringIndicatorDO.setScoreMin(vo.getScoreMin());
            memberScoringIndicatorDO.setScoreMax(vo.getScoreMax());
            memberScoringIndicatorDO.setIndicatorDescribe(vo.getIndicatorDescribe());
            memberScoringIndicatorDO.setSort(index ++);
            if (vo.getId() == null) {
                memberScoringIndicatorDO.setCreateTime(System.currentTimeMillis());
            } else {
                memberScoringIndicatorDO.setUpdateTime(System.currentTimeMillis());
            }
            memberScoringIndicatorDOList.add(memberScoringIndicatorDO);
        }
        memberScoringIndicatorRepository.saveAll(memberScoringIndicatorDOList);
    }

    /**
     * 指标标准定义 - 评分标准列表
     * @param loginUser 登录用户信息
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public List<MemberScoringIndicatorGroupResp> pageScoringIndicator(UserLoginCacheDTO loginUser, Integer roleTag) {

        List<MemberScoringIndicatorDO> memberScoringIndicatorDOList = getMemberScoringIndicatorList(loginUser, roleTag);

        // 结果集为空
        if (CollectionUtils.isEmpty(memberScoringIndicatorDOList)) {
            return new ArrayList<>();
        }

        Map<String, List<MemberScoringIndicatorDO>> groupList = memberScoringIndicatorDOList.stream().collect(Collectors.groupingBy(MemberScoringIndicatorDO::getIndicatorGrouping));

        List<MemberScoringIndicatorGroupResp> collect = groupList.entrySet().stream().map(e -> {
            MemberScoringIndicatorGroupResp scoringIndicatorGroupVO = new MemberScoringIndicatorGroupResp();
            scoringIndicatorGroupVO.setGroupName(e.getKey());

            List<MemberScoringIndicatorResp> elements = e.getValue().stream().map(s -> {
                MemberScoringIndicatorResp scoringIndicatorVO = new MemberScoringIndicatorResp();
                scoringIndicatorVO.setId(s.getId());
                scoringIndicatorVO.setIndicatorGrouping(s.getIndicatorGrouping());
                scoringIndicatorVO.setStandardIndicator(s.getStandardIndicator());
                scoringIndicatorVO.setScoreMin(s.getScoreMin());
                scoringIndicatorVO.setScoreMax(s.getScoreMax());
                scoringIndicatorVO.setIndicatorDescribe(s.getIndicatorDescribe());
                scoringIndicatorVO.setSort(s.getSort());
                return scoringIndicatorVO;
            }).sorted(Comparator.comparingInt(MemberScoringIndicatorResp::getSort)).collect(Collectors.toList());

            scoringIndicatorGroupVO.setElements(elements);
            return scoringIndicatorGroupVO;
        }).sorted(Comparator.comparingInt(e -> e.getElements().get(0).getSort())).collect(Collectors.toList());
        return collect;
    }

    /**
     * 指标标准定义 - 未配置评分项目
     * @param loginUser 登录用户信息
     * @param queryVO 请求参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public List<MemberScoringIndicatorResp> toAssignedPageScoringIndicator(UserLoginCacheDTO loginUser, MemberScoringIndicatorPageReq queryVO, Integer roleTag) {
        QMemberScoringIndicatorDO si = QMemberScoringIndicatorDO.memberScoringIndicatorDO;
        QMemberScoringTemplateDO st = QMemberScoringTemplateDO.memberScoringTemplateDO;
        QMemberScoringTemplateIndicatorDO sti = QMemberScoringTemplateIndicatorDO.memberScoringTemplateIndicatorDO;
        JPAQuery<MemberScoringIndicatorDO> query = jpaQueryFactory.selectFrom(si)
                .from(si);

        //拼接查询条件
        BooleanBuilder builder = new BooleanBuilder();

        JPQLQuery<MemberScoringTemplateDO> childQuery = JPAExpressions.selectFrom(st).leftJoin(sti).on(st.id.eq(sti.scoringTemplate.id));
        //拼接查询条件
        BooleanBuilder childBuilder = new BooleanBuilder();
        childBuilder.and(sti.indicatorGrouping.eq(si.indicatorGrouping))
                .and(sti.standardIndicator.eq(si.standardIndicator))
                .and(st.memberId.eq(loginUser.getMemberId()))
                .and(st.roleId.eq(loginUser.getMemberRoleId()));
        if (NumberUtil.notNullOrZero(roleTag)) {
            childBuilder.and(st.roleTag.eq(roleTag));
        }

        builder.and(childQuery.where(childBuilder).notExists())
            .and(si.memberId.eq(loginUser.getMemberId()))
            .and(si.roleId.eq(loginUser.getMemberRoleId()));
        if (NumberUtil.notNullOrZero(roleTag)) {
            builder.and(si.roleTag.eq(roleTag));
        }

        if (StringUtils.hasLength(queryVO.getIndicatorGrouping())) {
            builder.and(si.indicatorGrouping.like("%" + queryVO.getIndicatorGrouping() + "%"));
        }

        if (StringUtils.hasLength(queryVO.getStandardIndicator())) {
            builder.and(si.standardIndicator.like("%" + queryVO.getStandardIndicator() + "%"));
        }

        List<MemberScoringIndicatorDO> memberScoringIndicators = query.where(builder).fetch();

        List<MemberScoringIndicatorResp> result = memberScoringIndicators.stream().map(memberScoringIndicatorDO -> {
            MemberScoringIndicatorResp memberScoringIndicatorResp = new MemberScoringIndicatorResp();
            memberScoringIndicatorResp.setId(memberScoringIndicatorDO.getId());
            memberScoringIndicatorResp.setIndicatorGrouping(memberScoringIndicatorDO.getIndicatorGrouping());
            memberScoringIndicatorResp.setStandardIndicator(memberScoringIndicatorDO.getStandardIndicator());
            memberScoringIndicatorResp.setScoreMin(memberScoringIndicatorDO.getScoreMin());
            memberScoringIndicatorResp.setScoreMax(memberScoringIndicatorDO.getScoreMax());
            memberScoringIndicatorResp.setIndicatorDescribe(memberScoringIndicatorDO.getIndicatorDescribe());
            return memberScoringIndicatorResp;
        }).collect(Collectors.toList());
        return result;
    }

    /**
     * 评分模板配置 - 新增评分模板
     * @param loginUser 登录用户信息
     * @param addVO 请求参数VO
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Transactional
    @Override
    public void addTemplate(UserLoginCacheDTO loginUser, MemberScoringTemplateReq addVO, Integer roleTag) {
        String templateName = ScoringTemplateTypeEnum.getName(addVO.getTemplateType());
        if (!StringUtils.hasLength(templateName)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_TYPE_UNDEFINED);
        }

        // 评分模板已存在
        MemberScoringTemplateDO memberScoringTemplate = getMemberScoringTemplate(loginUser, roleTag, addVO.getTemplateName(), addVO.getTemplateType());
        if (Objects.nonNull(memberScoringTemplate)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_TYPE_EXISTS);
        }

        if (CollectionUtils.isEmpty(addVO.getTemplateIndicatorSubmitList())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_INDICATOR_NOT_EMPTY);
        }

        // 评分标准重复
        List<MemberScoringTemplateIndicatorSubmitReq> templateIndicatorList = addVO.getTemplateIndicatorSubmitList();
        if (templateIndicatorList.stream().distinct().count() != templateIndicatorList.size()) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_INDICATOR_SCORE_REPEAT);
        }

        // 判断评分标准的权重和, 等于100
        BigDecimal scoreWeight = templateIndicatorList.stream().map(MemberScoringTemplateIndicatorSubmitReq::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!DECIMAL_FORMAT.format(scoreWeight).equals(WEIGHT_THRESHOLD)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_TOTAL_SCORE_WEIGHT);
        }

        // 保存评分模板
        MemberScoringTemplateDO scoringTemplateDO = new MemberScoringTemplateDO();
        saveScoringTemplate(scoringTemplateDO, addVO, loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);

        // 保存评分模板标准定义
        List<MemberScoringTemplateIndicatorDO> memberScoringTemplateIndicators = saveScoringTemplateIndicator(addVO.getTemplateIndicatorSubmitList(), loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag, scoringTemplateDO);

        scoringTemplateDO.setTemplateIndicators(new HashSet<>(memberScoringTemplateIndicators));

        // 关联评分模板标准
        memberScoringTemplateRepository.saveAndFlush(scoringTemplateDO);



    }

    /**
     * 评分模板配置 - 修改评分模板
     * @param loginUser Http头部信息
     * @param vo 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Transactional
    @Override
    public void updateTemplate(UserLoginCacheDTO loginUser, MemberScoringTemplateReq vo, Integer roleTag) {
        String templateName = ScoringTemplateTypeEnum.getName(vo.getTemplateType());
        if (!StringUtils.hasLength(templateName)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_TYPE_UNDEFINED);
        }

        WrapperResp<MemberScoringTemplateDO> memberScoringTemplateExists = memberScoringTemplateExists(loginUser, vo.getId(), roleTag);
        if (memberScoringTemplateExists.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {

            throw new BusinessException(memberScoringTemplateExists.getCode(),memberScoringTemplateExists.getMessage());
        }

        MemberScoringTemplateDO toUpdateTemplateDO = memberScoringTemplateExists.getData();

        // 评分模板已存在
        MemberScoringTemplateDO memberScoringTemplate = getMemberScoringTemplate(loginUser, roleTag, vo.getTemplateName(), vo.getTemplateType());
        if (Objects.nonNull(memberScoringTemplate) && !memberScoringTemplate.getId().equals(vo.getId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_TYPE_EXISTS);
        }

        if (CollectionUtils.isEmpty(vo.getTemplateIndicatorSubmitList())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_INDICATOR_NOT_EMPTY);
        }

        // 评分标准重复
        List<MemberScoringTemplateIndicatorSubmitReq> templateIndicatorList = vo.getTemplateIndicatorSubmitList();
        if (templateIndicatorList.stream().distinct().count() != templateIndicatorList.size()) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_INDICATOR_SCORE_REPEAT);
        }

        // 判断评分标准的权重和, 等于100
        BigDecimal scoreWeight = templateIndicatorList.stream().map(MemberScoringTemplateIndicatorSubmitReq::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!DECIMAL_FORMAT.format(scoreWeight).equals(WEIGHT_THRESHOLD)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_TOTAL_SCORE_WEIGHT);
        }

        if (!toUpdateTemplateDO.getState().equals(ScoringTemplateStateEnum.DISABLE.getCode())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_STATE_NOT_STOP);
        }

        // 删除就模板关联的评分标准列表
        toUpdateTemplateDO.getTemplateIndicators().clear();
        memberScoringTemplateIndicatorRepository.deleteByScoringTemplate(toUpdateTemplateDO);

        // 更新评分模板
        saveScoringTemplate(toUpdateTemplateDO, vo, loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag);

        // 保存评分模板标准定义
        List<MemberScoringTemplateIndicatorDO> memberScoringTemplateIndicators = saveScoringTemplateIndicator(vo.getTemplateIndicatorSubmitList(), loginUser.getMemberId(), loginUser.getMemberRoleId(), roleTag, toUpdateTemplateDO);

        // 关联评分模板标准
        toUpdateTemplateDO.getTemplateIndicators().addAll(memberScoringTemplateIndicators);

        memberScoringTemplateRepository.saveAndFlush(toUpdateTemplateDO);


    }

    /**
     * 评分模板配置 - 详情
     * @param loginUser 登录用户信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @Override
    public MemberScoringTemplateDetailResp templateDetail(UserLoginCacheDTO loginUser, TemplateIdReq vo, Integer roleTag) {
        MemberScoringTemplateDO memberScoringTemplateDO = memberScoringTemplateRepository.findById(vo.getTemplateId()).orElse(null);
        if (Objects.isNull(memberScoringTemplateDO)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_UPDATE_SCORING_TEMPLATE_NOT_EXISTS);
        }

        MemberScoringTemplateDetailResp memberScoringTemplateDetailResp = new MemberScoringTemplateDetailResp();
        memberScoringTemplateDetailResp.setId(memberScoringTemplateDO.getId());
        memberScoringTemplateDetailResp.setTemplateName(memberScoringTemplateDO.getTemplateName());
        memberScoringTemplateDetailResp.setTemplateType(memberScoringTemplateDO.getTemplateType());
        memberScoringTemplateDetailResp.setTemplateTypeName(ScoringTemplateTypeEnum.getName(memberScoringTemplateDO.getTemplateType()));
        memberScoringTemplateDetailResp.setTemplateDescribe(memberScoringTemplateDO.getTemplateDescribe());
        memberScoringTemplateDetailResp.setState(memberScoringTemplateDO.getState());
        memberScoringTemplateDetailResp.setStateName(ScoringTemplateStateEnum.getName(memberScoringTemplateDO.getState()));

        Set<MemberScoringTemplateIndicatorDO> templateIndicators = memberScoringTemplateDO.getTemplateIndicators();
        if (!CollectionUtils.isEmpty(templateIndicators)) {
            Map<String, List<MemberScoringTemplateIndicatorDO>> groupList = templateIndicators.stream().collect(Collectors.groupingBy(MemberScoringTemplateIndicatorDO::getIndicatorGrouping));

            List<MemberTemplateIndicatorGroupResp> collect = groupList.entrySet().stream().map(e -> {
                MemberTemplateIndicatorGroupResp memberTemplateIndicatorGroupResp = new MemberTemplateIndicatorGroupResp();
                memberTemplateIndicatorGroupResp.setGroupName(e.getKey());

                List<MemberTemplateIndicatorDetailResp> elements = e.getValue().stream().map(s -> {
                    MemberTemplateIndicatorDetailResp memberTemplateIndicatorDetailResp = new MemberTemplateIndicatorDetailResp();
                    memberTemplateIndicatorDetailResp.setId(s.getId());
                    memberTemplateIndicatorDetailResp.setIndicatorGrouping(s.getIndicatorGrouping());
                    memberTemplateIndicatorDetailResp.setStandardIndicator(s.getStandardIndicator());
                    memberTemplateIndicatorDetailResp.setScoreMin(s.getScoreMin());
                    memberTemplateIndicatorDetailResp.setScoreMax(s.getScoreMax());
                    memberTemplateIndicatorDetailResp.setScoreStandard(s.getScoreStandard());
                    memberTemplateIndicatorDetailResp.setWeight(s.getWeight());
                    memberTemplateIndicatorDetailResp.setIndicatorDescribe(s.getIndicatorDescribe());
                    memberTemplateIndicatorDetailResp.setSort(s.getSort());
                    return memberTemplateIndicatorDetailResp;
                }).sorted(Comparator.comparingInt(MemberTemplateIndicatorDetailResp::getSort)).collect(Collectors.toList());

                memberTemplateIndicatorGroupResp.setElements(elements);
                return memberTemplateIndicatorGroupResp;
            }).sorted(Comparator.comparingInt(e -> e.getElements().get(0).getSort())).collect(Collectors.toList());
            memberScoringTemplateDetailResp.setTemplateIndicatorGroups(collect);
        }
        return memberScoringTemplateDetailResp;
    }

    /**
     * 评分模板配置 - 列表查询
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<MemberScoringTemplateDetailResp> templatePage(UserLoginCacheDTO loginUser, MemberScoringTemplatePageReq pageVO, Integer roleTag) {
        List<MemberScoringTemplateDO> templateDOList = memberScoringTemplateRepository.findAll((root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));

            if (NumberUtil.notNullOrZero(roleTag)) {
                predicateList.add(cb.equal(root.get("roleTag").as(Integer.class), roleTag));
            }

            if (NumberUtil.notNullOrZero(pageVO.getId())) {
                predicateList.add(cb.equal(root.get("id").as(Long.class), pageVO.getId()));
            }

            if (StringUtils.hasLength(pageVO.getTemplateName())) {
                predicateList.add(cb.like(root.get("templateName"), "%" + pageVO.getTemplateName() + "%"));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        });

        List<MemberScoringTemplateDetailResp> templateDetailVOList = templateDOList.stream().map(template -> {
            MemberScoringTemplateDetailResp memberScoringTemplateDetailResp = new MemberScoringTemplateDetailResp();
            memberScoringTemplateDetailResp.setId(template.getId());
            memberScoringTemplateDetailResp.setTemplateName(template.getTemplateName());
            memberScoringTemplateDetailResp.setTemplateType(template.getTemplateType());
            memberScoringTemplateDetailResp.setTemplateTypeName(ScoringTemplateTypeEnum.getName(template.getTemplateType()));
            memberScoringTemplateDetailResp.setTemplateDescribe(template.getTemplateDescribe());
            memberScoringTemplateDetailResp.setState(template.getState());
            memberScoringTemplateDetailResp.setStateName(ScoringTemplateStateEnum.getName(template.getState()));
            return memberScoringTemplateDetailResp;
        }).collect(Collectors.toList());
        return templateDetailVOList;
    }

    /**
     * 评分模板配置 - 停用/启用
     * @param loginUser 登录用户信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @Override
    public void startOrStopTemplate(UserLoginCacheDTO loginUser, ScoringTemplateStartOrStopReq vo, Integer roleTag) {
        WrapperResp<MemberScoringTemplateDO> memberScoringTemplateExists = memberScoringTemplateExists(loginUser, vo.getId(), roleTag);
        if (memberScoringTemplateExists.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {

            throw new BusinessException(memberScoringTemplateExists.getCode(),memberScoringTemplateExists.getMessage());
        }

        MemberScoringTemplateDO toUpdateTemplateDO = memberScoringTemplateExists.getData();

        if (!StringUtils.hasLength(ScoringTemplateStateEnum.getName(vo.getState()))) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_SCORING_TEMPLATE_STATE_UNDEFINED);
        }

        toUpdateTemplateDO.setState(vo.getState());
        memberScoringTemplateRepository.save(toUpdateTemplateDO);

    }

    /**
     * 评分模板配置 - 删除评分模板
     * @param loginUser 登录用户信息
     * @param vo 接口参数
     * @return 操作结果
     */
    @Override
    public void deleteTemplate(UserLoginCacheDTO loginUser, MemberScoringTemplateDeleteReq vo, Integer roleTag) {
        WrapperResp<MemberScoringTemplateDO> memberScoringTemplateExists = memberScoringTemplateExists(loginUser, vo.getId(), roleTag);
        if (memberScoringTemplateExists.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {

            throw new BusinessException(memberScoringTemplateExists.getCode(),memberScoringTemplateExists.getMessage());
        }

        MemberScoringTemplateDO memberScoringTemplateDO = memberScoringTemplateExists.getData();

        memberScoringTemplateRepository.delete(memberScoringTemplateDO);

    }

    /**
     * 模板类型下拉查询
     * @param roleTag 角色标签
     * @return 查询结果
     */
    @Override
    public List<StatusResp> templateTypeList(Integer roleTag) {
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        Stream<ScoringTemplateTypeEnum> typeEnumStream;
        switch (roleTagEnum) {
            case CUSTOMER:
                typeEnumStream = Stream.of(
                        ScoringTemplateTypeEnum.SCORING_TEMPLATE_TYPE_THREE,
                        ScoringTemplateTypeEnum.SCORING_TEMPLATE_TYPE_FOUR);
                break;
            case SUPPLIER:
                typeEnumStream = Stream.of(
                        ScoringTemplateTypeEnum.SCORING_TEMPLATE_TYPE_ONE,
                        ScoringTemplateTypeEnum.SCORING_TEMPLATE_TYPE_TWO);
                break;
            default:
                typeEnumStream = Stream.of(ScoringTemplateTypeEnum.values());
                break;
        }
        List<StatusResp> statusRespList = typeEnumStream.map(e -> {
            StatusResp statusResp = new StatusResp();
            statusResp.setCode(e.getCode());
            statusResp.setMessage(e.getName());
            return statusResp;
        }).collect(Collectors.toList());
        return statusRespList;
    }

    /**
     * 模板是否存在
     * @param loginUser 登录用户信息
     * @param id id
     * @param roleTag 角色标签
     * @return 查询结果
     */
    private WrapperResp<MemberScoringTemplateDO> memberScoringTemplateExists(UserLoginCacheDTO loginUser, Long id, Integer roleTag) {
        MemberScoringTemplateDO memberScoringTemplateDO;
        if (NumberUtil.isNullOrZero(id) || Objects.isNull(memberScoringTemplateDO = memberScoringTemplateRepository.findById(id).orElse(null))) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_UPDATE_SCORING_TEMPLATE_NOT_EXISTS);
        }

        if (!memberScoringTemplateDO.getMemberId().equals(loginUser.getMemberId())
                || !memberScoringTemplateDO.getRoleId().equals(loginUser.getMemberRoleId())
                || !memberScoringTemplateDO.getRoleTag().equals(roleTag)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_UPDATE_SCORING_TEMPLATE_NOT_EXISTS);
        }
        return WrapperUtil.success(memberScoringTemplateDO);
    }

    /**
     * 保存评分模板标准
     * @param memberScoringTemplateDO 评分模板
     * @param vo 待保存列表
     * @param memberId 会员id
     * @param roleId 角色id
     * @param roleTag 角色标签
     */
    @Override
    public void saveScoringTemplate(MemberScoringTemplateDO memberScoringTemplateDO, MemberScoringTemplateReq vo, Long memberId, Long roleId, Integer roleTag) {
        if (NumberUtil.notNullOrZero(vo.getId())) {
            memberScoringTemplateDO.setUpdateTime(System.currentTimeMillis());
        } else {
            memberScoringTemplateDO.setCreateTime(System.currentTimeMillis());
        }
        memberScoringTemplateDO.setMemberId(memberId);
        memberScoringTemplateDO.setRoleId(roleId);
        memberScoringTemplateDO.setRoleTag(roleTag);
        memberScoringTemplateDO.setTemplateName(vo.getTemplateName());
        memberScoringTemplateDO.setTemplateType(vo.getTemplateType());
        memberScoringTemplateDO.setTemplateDescribe(vo.getTemplateDescribe());
        memberScoringTemplateDO.setState(ScoringTemplateStateEnum.ENABLE.getCode());
        memberScoringTemplateRepository.save(memberScoringTemplateDO);
    }

    /**
     * 保存评分模板标准
     * @param saveOrUpdateList 待新增或更新列表
     * @param memberId 会员id
     * @param roleId 角色id
     * @param roleTag 角色标签
     * @param scoringTemplateDO 评分模板
     */
    @Override
    public List<MemberScoringTemplateIndicatorDO> saveScoringTemplateIndicator(List<MemberScoringTemplateIndicatorSubmitReq> saveOrUpdateList, Long memberId, Long roleId, Integer roleTag, MemberScoringTemplateDO scoringTemplateDO) {
        List<MemberScoringTemplateIndicatorDO> memberScoringIndicatorDOList = new ArrayList<>();
        // 排序
        int index = 1;
        for (MemberScoringTemplateIndicatorSubmitReq vo : saveOrUpdateList) {
            MemberScoringTemplateIndicatorDO scoringTemplateIndicatorDO = new MemberScoringTemplateIndicatorDO();
            scoringTemplateIndicatorDO.setId(vo.getId());
            scoringTemplateIndicatorDO.setScoringTemplate(scoringTemplateDO);
            scoringTemplateIndicatorDO.setIndicatorGrouping(vo.getIndicatorGrouping());
            scoringTemplateIndicatorDO.setStandardIndicator(vo.getStandardIndicator());
            scoringTemplateIndicatorDO.setScoreMin(vo.getScoreMin());
            scoringTemplateIndicatorDO.setScoreMax(vo.getScoreMax());
            scoringTemplateIndicatorDO.setScoreStandard(vo.getScoreStandard());
            scoringTemplateIndicatorDO.setWeight(vo.getWeight());
            scoringTemplateIndicatorDO.setIndicatorDescribe(vo.getIndicatorDescribe());
            scoringTemplateIndicatorDO.setSort(index ++);
            memberScoringIndicatorDOList.add(scoringTemplateIndicatorDO);
        }
        return memberScoringTemplateIndicatorRepository.saveAll(memberScoringIndicatorDOList);
    }

    /**
     * 查询当前用户的评分模板
     * @param loginUser 登录用户信息
     * @param roleTag 角色标签
     * @param templateName 模板名称
     * @param templateType 模板类型
     * @return 查询结果
     */
    private MemberScoringTemplateDO getMemberScoringTemplate(UserLoginCacheDTO loginUser, Integer roleTag, String templateName, Integer templateType) {
        return memberScoringTemplateRepository.findOne((root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));

            if (NumberUtil.notNullOrZero(roleTag)) {
                predicateList.add(cb.equal(root.get("roleTag").as(Integer.class), roleTag));
            }

            if (StringUtils.hasLength(templateName)) {
                predicateList.add(cb.equal(root.get("templateName").as(String.class), templateName));
            }

            if (NumberUtil.notNullOrZero(templateType)) {
                predicateList.add(cb.equal(root.get("templateType").as(Integer.class), templateType));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }).orElse(null);
    }
}
