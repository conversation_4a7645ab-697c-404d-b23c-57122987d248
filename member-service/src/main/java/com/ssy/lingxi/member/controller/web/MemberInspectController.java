package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.NamePageDataReq;
import com.ssy.lingxi.member.model.req.basic.UserPageDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.MemberInspectAddReq;
import com.ssy.lingxi.member.model.req.lifecycle.MemberInspectPageDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.MemberInspectUpdateReq;
import com.ssy.lingxi.member.model.resp.basic.UserQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberInspectPageQueryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberInspectResp;
import com.ssy.lingxi.member.model.resp.lifecycle.SubMemberQueryResp;
import com.ssy.lingxi.member.service.web.IMemberInspectService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员能力 - 会员考察
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/18
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/inspect")
public class MemberInspectController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IMemberInspectService memberInspectService;

    /**
     * 获取“考察类型”下拉框条件
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/types")
    public WrapperResp<List<DropdownItemResp>> getPageConditions(@RequestHeader HttpHeaders headers) {
        List<DropdownItemResp> itemList = memberInspectService.getPageConditions(headers);
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        return WrapperUtil.success(itemList);
    }

    /**
     * 获取可用“考察类型”下拉框条件
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/availableTypes")
    public WrapperResp<List<DropdownItemResp>> getAvailableTypes(@RequestHeader HttpHeaders headers) {
        List<DropdownItemResp> itemList = memberInspectService.getPageConditions(headers);
        return WrapperUtil.success(itemList);
    }

    /**
     * “新增会员审查” - 选择下级会员
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/members")
    public WrapperResp<PageDataResp<SubMemberQueryResp>> pageSubMembers(@RequestHeader HttpHeaders headers, @Valid NamePageDataReq pageVO) {
        return WrapperUtil.success(memberInspectService.pageSubMembers(headers, pageVO, roleTag));
    }

    /**
     * “新增或修改会员审查” - 选择用户
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/users")
    public WrapperResp<PageDataResp<UserQueryResp>> pageUsers(@RequestHeader HttpHeaders headers, @Valid UserPageDataReq pageVO) {
        return WrapperUtil.success(memberInspectService.pageUsers(headers, pageVO));
    }

    /**
     * 分页查询会员考察
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberInspectPageQueryResp>> pageMemberInspect(@RequestHeader HttpHeaders headers, @Valid MemberInspectPageDataReq queryVO) {
        return WrapperUtil.success(memberInspectService.pageMemberInspect(headers, queryVO, roleTag));
    }

    /**
     * 新增会员考察
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/add")
    public WrapperResp<Void> addMemberInspect(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberInspectAddReq addVO) {
        memberInspectService.addMemberInspect(headers, addVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 查询会员考察详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberInspectResp> getMemberInspect(@RequestHeader HttpHeaders headers, @Valid CommonIdReq commonIdReq) {
        return WrapperUtil.success(memberInspectService.getMemberInspect(headers, commonIdReq, roleTag));
    }


    /**
     * 修改会员考察修改
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> updateMemberInspect(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberInspectUpdateReq updateVO) {
        memberInspectService.updateMemberInspect(headers, updateVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 删除会员考察
     * @param headers Http头部信息
     * @param commonIdReq 接口参数
     * @return 查询结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> addMemberInspect(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq commonIdReq) {
        memberInspectService.deleteMemberInspect(headers, commonIdReq, roleTag);
        return WrapperUtil.success();
    }

}
