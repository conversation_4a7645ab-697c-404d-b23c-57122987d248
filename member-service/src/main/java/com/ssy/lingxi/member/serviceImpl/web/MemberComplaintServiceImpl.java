package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.MessageNoticeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.complain.MemberComplaintDO;
import com.ssy.lingxi.member.entity.do_.rectify.MemberRectifyDO;
import com.ssy.lingxi.member.enums.MemberComplaintClassifyEnum;
import com.ssy.lingxi.member.enums.MemberComplaintComplaintTypeEnum;
import com.ssy.lingxi.member.enums.MemberComplaintStatusEnum;
import com.ssy.lingxi.member.enums.MemberComplaintTypeEnum;
import com.ssy.lingxi.member.model.req.basic.NamePageDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditComplainSummaryResp;
import com.ssy.lingxi.member.repository.MemberComplaintRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.MemberRoleRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.web.IMemberComplaintService;
import com.ssy.lingxi.member.util.FileObjectUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员投诉与建议服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Service
public class MemberComplaintServiceImpl implements IMemberComplaintService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberComplaintRepository memberComplaintRepository;

    @Resource
    private MemberRelationRepository memberRelationRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private IMessageFeignService messageFeignService;

    @Override
    public List<StatusResp> listMemberComplaintStatus(HttpHeaders headers) {
        return Stream.of(MemberComplaintStatusEnum.values()).map(e -> {
            StatusResp statusResp = new StatusResp();
            statusResp.setCode(e.getCode());
            statusResp.setMessage(e.getMessage());
            return statusResp;
        }).collect(Collectors.toList());

    }

    @Override
    public PageDataResp<UpperMemberQueryResp> pageMembers(HttpHeaders headers, NamePageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").ascending());
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.notEqual(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));
            if (StringUtils.hasLength(pageVO.getName())) {
                Join<MemberRelationDO, MemberDO> memberJoin = root.join("member", JoinType.LEFT);
                list.add(criteriaBuilder.like(memberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<MemberRelationDO> pageList = memberRelationRepository.findAll(specification, page);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.stream().map(p -> {
            UpperMemberQueryResp queryVO = new UpperMemberQueryResp();
            queryVO.setMemberId(p.getMemberId());
            queryVO.setRoleId(p.getRoleId());
            queryVO.setUpperName(p.getMember().getName());
            queryVO.setRoleName(p.getRole().getRoleName());
            queryVO.setMemberTypeName(MemberTypeEnum.getName(p.getRole().getMemberType()));
            queryVO.setLevel(p.getLevelRight().getLevel());
            queryVO.setLevelTag(p.getLevelRight().getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    @Override
    public PageDataResp<MemberUpperComplaintPageQueryResp> pageUpperMemberComplaint(HttpHeaders headers, MemberComplaintPageDataReq pageVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());

//        List<Long> finalRoleIds = roleIds;
        Page<MemberComplaintDO> pageList = memberComplaintRepository.findAll((Specification<MemberComplaintDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("roleId"), loginUser.getMemberRoleId()));
            Join<MemberComplaintDO, MemberDO> memberJoin = root.join("member", JoinType.LEFT);
            predicateList.add(cb.equal(memberJoin.get("id"), loginUser.getMemberId()));

            // 判断当前请求是发起投诉还是收到投诉
            if (MemberComplaintComplaintTypeEnum.UPPER.getCode().equals(pageVO.getComplaintType())) {
                // 发起投诉建议
                predicateList.add(cb.equal(root.get("complaintType").as(Integer.class), MemberComplaintComplaintTypeEnum.UPPER.getCode()));
            } else if (MemberComplaintComplaintTypeEnum.SUB.getCode().equals(pageVO.getComplaintType())) {
                // 收到投诉建议
                predicateList.add(cb.equal(root.get("complaintType").as(Integer.class), MemberComplaintComplaintTypeEnum.SUB.getCode()));
                predicateList.add(cb.in(root.get("status")).value(Stream.of(MemberComplaintStatusEnum.WAIT_HANDLE.getCode(), MemberComplaintStatusEnum.COMPLETE_HANDLE.getCode()).collect(Collectors.toList())));
            }

            /*if (NumberUtil.notNullOrZero(roleTag)) {
                predicateList.add(cb.in(root.get("subRoleId")).value(finalRoleIds));
            }*/

            if (StringUtils.hasLength(pageVO.getName())) {
                Join<MemberComplaintDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
                predicateList.add(cb.like(subMemberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (StringUtils.hasLength(pageVO.getSubject())) {
                predicateList.add(cb.like(root.get("subject"), "%" + pageVO.getSubject() + "%"));
            }

            if (Objects.nonNull(pageVO.getEventTimeStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateTime(pageVO.getEventTimeStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("eventTime"), startDate));
            }

            if (Objects.nonNull(pageVO.getEventTimeEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateTime(pageVO.getEventTimeEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("eventTime"), endDate));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        }, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberComplaintDO -> {
            MemberUpperComplaintPageQueryResp queryVO = new MemberUpperComplaintPageQueryResp();
            queryVO.setId(memberComplaintDO.getId());
            queryVO.setType(memberComplaintDO.getType());
            queryVO.setTypeName(MemberComplaintTypeEnum.getCodeMessage(memberComplaintDO.getType()));
            queryVO.setClassify(memberComplaintDO.getClassify());
            queryVO.setClassifyName(MemberComplaintClassifyEnum.getCodeMessage(memberComplaintDO.getClassify()));
            queryVO.setStatus(memberComplaintDO.getStatus());
            queryVO.setStatusName(MemberComplaintStatusEnum.getCodeMessage(memberComplaintDO.getStatus()));
            queryVO.setSubject(memberComplaintDO.getSubject());
            queryVO.setSubMemberId(memberComplaintDO.getSubMember().getId());
            queryVO.setSubRoleId(memberComplaintDO.getSubRoleId());
            queryVO.setName(memberComplaintDO.getSubMember().getName());
            queryVO.setByUserId(Objects.isNull(memberComplaintDO.getByUser()) ? 0L : memberComplaintDO.getByUser().getId());
            queryVO.setByUserName(Objects.isNull(memberComplaintDO.getByUser()) ? memberComplaintDO.getByUserEditName() : memberComplaintDO.getByUser().getName());
            queryVO.setByUserPhone(Objects.isNull(memberComplaintDO.getByUser()) ? memberComplaintDO.getByUserEditPhone() : memberComplaintDO.getByUser().getPhone());
            queryVO.setEventTime(DateTimeUtil.formatDateTime(memberComplaintDO.getEventTime()));
            queryVO.setHandleTime(DateTimeUtil.formatDateTime(memberComplaintDO.getHandleTime()));
            queryVO.setSubmitOrUpdateOrDelete(MemberComplaintStatusEnum.WAIT_SUBMIT.getCode().equals(memberComplaintDO.getStatus()));
            queryVO.setHandle(MemberComplaintStatusEnum.WAIT_HANDLE.getCode().equals(memberComplaintDO.getStatus()));

            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 会员信息 - 会员信用 - 会员投诉记录汇总
     *
     * @param upperMember 上级会员
     * @param roleId      上级会员角色Id
     * @param subMember   下级会员
     * @param subRoleId   下级会员角色Id
     * @return 汇总结果
     */
    @Override
    public MemberDetailCreditComplainSummaryResp summaryUpperMemberComplaints(MemberDO upperMember, Long roleId, MemberDO subMember, Long subRoleId) {
        List<MemberComplaintDO> complaintDOList = memberComplaintRepository.findAll((Specification<MemberComplaintDO>) (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(cb.equal(root.get("member").as(MemberDO.class), upperMember));
            list.add(cb.equal(root.get("roleId").as(Long.class), roleId));
            list.add(cb.equal(root.get("subMember").as(MemberDO.class), subMember));
            list.add(cb.equal(root.get("subRoleId").as(Long.class), subRoleId));
            Predicate[] p = new Predicate[list.size()];
            return cb.and(list.toArray(p));
        });

        MemberDetailCreditComplainSummaryResp summaryVO = new MemberDetailCreditComplainSummaryResp();
        for (MemberComplaintDO memberComplaintDO : complaintDOList) {
            long diffDays = DateTimeUtil.diffDays(memberComplaintDO.getEventTime(), LocalDateTime.now());
            if (diffDays <= 7) {
                summaryVO.setLast7days(summaryVO.getLast7days() + 1);
            }

            if (diffDays <= 30) {
                summaryVO.setLast30days(summaryVO.getLast30days() + 1);
            }

            if (diffDays <= 180) {
                summaryVO.setLast180days(summaryVO.getLast180days() + 1);
            }

            if (diffDays > 180) {
                summaryVO.setBefore180days(summaryVO.getBefore180days() + 1);
            }
        }

        summaryVO.setSum(summaryVO.getLast180days() + summaryVO.getBefore180days());

        return summaryVO;
    }

    /**
     * 会员信息 - 会员信用 - 分页查询会员投诉记录
     *
     * @param upperMember 上级会员
     * @param roleId      上级会员角色Id
     * @param subMember   下级会员
     * @param subRoleId   下级会员角色Id
     * @param current     当前页
     * @param pageSize    每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberCreditComplaintPageQueryResp> pageUpperMemberComplaint(MemberDO upperMember, Long roleId, MemberDO subMember, Long subRoleId, int current, int pageSize) {
        Pageable page = PageRequest.of(current - 1, pageSize, Sort.by("createTime").descending());
        Page<MemberComplaintDO> pageList = memberComplaintRepository.findAll((Specification<MemberComplaintDO>) (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(cb.equal(root.get("member").as(MemberDO.class), upperMember));
            list.add(cb.equal(root.get("roleId").as(Long.class), roleId));
            list.add(cb.equal(root.get("subMember").as(MemberDO.class), subMember));
            list.add(cb.equal(root.get("subRoleId").as(Long.class), subRoleId));
            Predicate[] p = new Predicate[list.size()];
            return cb.and(list.toArray(p));
        }, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberComplaintDO -> {
            MemberCreditComplaintPageQueryResp queryVO = new MemberCreditComplaintPageQueryResp();
            queryVO.setId(memberComplaintDO.getId());
            queryVO.setType(memberComplaintDO.getType());
            queryVO.setTypeName(MemberComplaintTypeEnum.getCodeMessage(memberComplaintDO.getType()));
            queryVO.setClassify(memberComplaintDO.getClassify());
            queryVO.setClassifyName(MemberComplaintClassifyEnum.getCodeMessage(memberComplaintDO.getClassify()));
            queryVO.setSubject(memberComplaintDO.getSubject());
            queryVO.setName(memberComplaintDO.getMember().getName());
            queryVO.setEventTime(DateTimeUtil.formatDateTime(memberComplaintDO.getEventTime()));
            queryVO.setHandleTime(memberComplaintDO.getHandleTime() == null ? "" : DateTimeUtil.formatDateTime(memberComplaintDO.getHandleTime()));
            queryVO.setResult(StringUtils.hasLength(memberComplaintDO.getHandleResult()) ? memberComplaintDO.getHandleResult() : "");
            return queryVO;
        }).collect(Collectors.toList()));
    }

    @Override
    public PageDataResp<MemberSubComplaintPageQueryResp> pageSubMemberComplaint(HttpHeaders headers, MemberComplaintPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("createTime").descending());

        Page<MemberComplaintDO> pageList = memberComplaintRepository.findAll((Specification<MemberComplaintDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("subRoleId"), loginUser.getMemberRoleId()));
            /*Predicate andPre = cb.and(cb.equal(root.get("status"), MemberComplaintStatusEnum.COMPLETE_HANDLE.getCode()), cb.equal(root.get("complaintType"), MemberComplaintComplaintTypeEnum.UPPER.getCode()));
            Predicate orPre = cb.or(andPre, cb.equal(root.get("complaintType"), MemberComplaintComplaintTypeEnum.SUB.getCode()));
            predicateList.add(orPre);*/

            Join<MemberComplaintDO, MemberDO> subMemberJoin = root.join("subMember", JoinType.LEFT);
            predicateList.add(cb.equal(subMemberJoin.get("id"), loginUser.getMemberId()));


            if (StringUtils.hasLength(pageVO.getName())) {
                Join<MemberRectifyDO, MemberDO> memberJoin = root.join("member", JoinType.LEFT);
                predicateList.add(cb.like(memberJoin.get("name").as(String.class), "%" + pageVO.getName().trim() + "%"));
            }

            if (StringUtils.hasLength(pageVO.getSubject())) {
                predicateList.add(cb.like(root.get("subject"), "%" + pageVO.getSubject() + "%"));
            }

            if (Objects.nonNull(pageVO.getEventTimeStart())) {
                LocalDateTime startDate = DateTimeUtil.parseDateTime(pageVO.getEventTimeStart());
                predicateList.add(cb.greaterThanOrEqualTo(root.get("eventTime"), startDate));
            }

            if (Objects.nonNull(pageVO.getEventTimeEnd())) {
                LocalDateTime endDate = DateTimeUtil.parseDateTime(pageVO.getEventTimeEnd());
                predicateList.add(cb.lessThanOrEqualTo(root.get("eventTime"), endDate));
            }

            // 判断当前请求是发起投诉还是收到投诉
            if (MemberComplaintComplaintTypeEnum.UPPER.getCode().equals(pageVO.getComplaintType())) {
                // 发起投诉建议
                predicateList.add(cb.equal(root.get("complaintType"), MemberComplaintComplaintTypeEnum.SUB.getCode()));
            } else if (MemberComplaintComplaintTypeEnum.SUB.getCode().equals(pageVO.getComplaintType())) {
                // 收到投诉建议
                predicateList.add(cb.equal(root.get("complaintType"), MemberComplaintComplaintTypeEnum.UPPER.getCode()));
                predicateList.add(cb.in(root.get("status")).value(Stream.of(MemberComplaintStatusEnum.WAIT_HANDLE.getCode(), MemberComplaintStatusEnum.COMPLETE_HANDLE.getCode()).collect(Collectors.toList())));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return cb.and(predicateList.toArray(p));
        }, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(memberComplaintDO -> {
            MemberSubComplaintPageQueryResp queryVO = new MemberSubComplaintPageQueryResp();
            queryVO.setId(memberComplaintDO.getId());
            queryVO.setType(memberComplaintDO.getType());
            queryVO.setTypeName(MemberComplaintTypeEnum.getCodeMessage(memberComplaintDO.getType()));
            queryVO.setClassify(memberComplaintDO.getClassify());
            queryVO.setClassifyName(MemberComplaintClassifyEnum.getCodeMessage(memberComplaintDO.getClassify()));
            queryVO.setStatus(memberComplaintDO.getStatus());
            queryVO.setStatusName(MemberComplaintStatusEnum.getCodeMessage(memberComplaintDO.getStatus()));
            queryVO.setSubject(memberComplaintDO.getSubject());
            queryVO.setMemberId(memberComplaintDO.getMember().getId());
            queryVO.setRoleId(memberComplaintDO.getRoleId());
            queryVO.setUpperName(memberComplaintDO.getMember().getName());
            queryVO.setByUserId(Objects.isNull(memberComplaintDO.getByUser()) ? 0L : memberComplaintDO.getByUser().getId());
            queryVO.setByUserName(Objects.isNull(memberComplaintDO.getByUser()) ? memberComplaintDO.getByUserEditName() : memberComplaintDO.getByUser().getName());
            queryVO.setByUserPhone(Objects.isNull(memberComplaintDO.getByUser()) ? memberComplaintDO.getByUserEditPhone() : memberComplaintDO.getByUser().getPhone());
            queryVO.setEventTime(DateTimeUtil.formatDateTime(memberComplaintDO.getEventTime()));
            queryVO.setHandleTime(DateTimeUtil.formatDateTime(memberComplaintDO.getHandleTime()));
            queryVO.setSubmitOrUpdateOrDelete(MemberComplaintStatusEnum.WAIT_SUBMIT.getCode().equals(memberComplaintDO.getStatus()));
            queryVO.setHandle(MemberComplaintStatusEnum.WAIT_HANDLE.getCode().equals(memberComplaintDO.getStatus()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    @Override
    public MemberComplaintUpperResp getUpperMemberComplaint(HttpHeaders headers, CommonIdReq idVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberComplaintDO memberComplaintDO = memberComplaintRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberComplaintDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberComplaintUpperResp memberComplaintUpperResp = new MemberComplaintUpperResp();
        memberComplaintUpperResp.setId(memberComplaintDO.getId());
        memberComplaintUpperResp.setType(memberComplaintDO.getType());
        memberComplaintUpperResp.setTypeName(MemberComplaintTypeEnum.getCodeMessage(memberComplaintDO.getType()));
        memberComplaintUpperResp.setClassify(memberComplaintDO.getClassify());
        memberComplaintUpperResp.setClassifyName(MemberComplaintClassifyEnum.getCodeMessage(memberComplaintDO.getClassify()));
        memberComplaintUpperResp.setSubject(memberComplaintDO.getSubject());
        memberComplaintUpperResp.setSubMemberId(memberComplaintDO.getSubMember().getId());
        memberComplaintUpperResp.setSubRoleId(memberComplaintDO.getSubRoleId());
        memberComplaintUpperResp.setName(memberComplaintDO.getSubMember().getName());
        memberComplaintUpperResp.setByUserId(Objects.isNull(memberComplaintDO.getByUser()) ? 0L : memberComplaintDO.getByUser().getId());
        memberComplaintUpperResp.setByUserName(Objects.isNull(memberComplaintDO.getByUser()) ? memberComplaintDO.getByUserEditName() : memberComplaintDO.getByUser().getName());
        memberComplaintUpperResp.setByUserPhone(Objects.isNull(memberComplaintDO.getByUser()) ? memberComplaintDO.getByUserEditPhone() : memberComplaintDO.getByUser().getPhone());
        memberComplaintUpperResp.setEventTime(DateTimeUtil.formatDateTime(memberComplaintDO.getEventTime()));
        memberComplaintUpperResp.setEventDesc(memberComplaintDO.getEventDesc());
        memberComplaintUpperResp.setEventSuggest(memberComplaintDO.getEventSuggest());
        memberComplaintUpperResp.setAttachments(FileObjectUtil.toVOList(memberComplaintDO.getAttachments()));
        memberComplaintUpperResp.setHandleUserId(Objects.isNull(memberComplaintDO.getHandleUser()) ? 0L : memberComplaintDO.getHandleUser().getId());
        memberComplaintUpperResp.setHandleUserName(Objects.isNull(memberComplaintDO.getHandleUser()) ? memberComplaintDO.getHandleUserEditName() : memberComplaintDO.getHandleUser().getName());
        memberComplaintUpperResp.setHandleUserPhone(Objects.isNull(memberComplaintDO.getHandleUser()) ? memberComplaintDO.getHandleUserEditPhone() : memberComplaintDO.getHandleUser().getPhone());
        memberComplaintUpperResp.setHandleResult(memberComplaintDO.getHandleResult());
        memberComplaintUpperResp.setHandleTime(DateTimeUtil.formatDateTime(memberComplaintDO.getHandleTime()));
        memberComplaintUpperResp.setHandleAttachments(FileObjectUtil.toVOList(memberComplaintDO.getHandleAttachments()));

        return memberComplaintUpperResp;
    }

    @Override
    public MemberComplaintSubResp getSubMemberComplaint(HttpHeaders headers, CommonIdReq idVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberComplaintDO memberComplaintDO = memberComplaintRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberComplaintDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberComplaintSubResp memberComplaintUpperVO = new MemberComplaintSubResp();
        memberComplaintUpperVO.setId(memberComplaintDO.getId());
        memberComplaintUpperVO.setType(memberComplaintDO.getType());
        memberComplaintUpperVO.setTypeName(MemberComplaintTypeEnum.getCodeMessage(memberComplaintDO.getType()));
        memberComplaintUpperVO.setClassify(memberComplaintDO.getClassify());
        memberComplaintUpperVO.setClassifyName(MemberComplaintClassifyEnum.getCodeMessage(memberComplaintDO.getClassify()));
        memberComplaintUpperVO.setSubject(memberComplaintDO.getSubject());
        memberComplaintUpperVO.setMemberId(memberComplaintDO.getMember().getId());
        memberComplaintUpperVO.setRoleId(memberComplaintDO.getRoleId());
        memberComplaintUpperVO.setUpperName(memberComplaintDO.getMember().getName());
        memberComplaintUpperVO.setByUserId(Objects.isNull(memberComplaintDO.getByUser()) ? 0L : memberComplaintDO.getByUser().getId());
        memberComplaintUpperVO.setByUserName(Objects.isNull(memberComplaintDO.getByUser()) ? memberComplaintDO.getByUserEditName() : memberComplaintDO.getByUser().getName());
        memberComplaintUpperVO.setByUserPhone(Objects.isNull(memberComplaintDO.getByUser()) ? memberComplaintDO.getByUserEditPhone() : memberComplaintDO.getByUser().getPhone());
        memberComplaintUpperVO.setEventTime(DateTimeUtil.formatDateTime(memberComplaintDO.getEventTime()));
        memberComplaintUpperVO.setEventDesc(memberComplaintDO.getEventDesc());
        memberComplaintUpperVO.setEventSuggest(memberComplaintDO.getEventSuggest());
        memberComplaintUpperVO.setAttachments(FileObjectUtil.toVOList(memberComplaintDO.getAttachments()));
        memberComplaintUpperVO.setHandleUserId(Objects.isNull(memberComplaintDO.getHandleUser()) ? 0L : memberComplaintDO.getHandleUser().getId());
        memberComplaintUpperVO.setHandleUserName(Objects.isNull(memberComplaintDO.getHandleUser()) ? memberComplaintDO.getHandleUserEditName() : memberComplaintDO.getHandleUser().getName());
        memberComplaintUpperVO.setHandleUserPhone(Objects.isNull(memberComplaintDO.getHandleUser()) ? memberComplaintDO.getHandleUserEditPhone() : memberComplaintDO.getHandleUser().getPhone());
        memberComplaintUpperVO.setHandleResult(memberComplaintDO.getHandleResult());
        memberComplaintUpperVO.setHandleTime(DateTimeUtil.formatDateTime(memberComplaintDO.getHandleTime()));
        memberComplaintUpperVO.setHandleAttachments(FileObjectUtil.toVOList(memberComplaintDO.getHandleAttachments()));

        return memberComplaintUpperVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addUpperMemberComplaint(HttpHeaders headers, MemberComplaintUpperAddReq addVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = memberRelationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), addVO.getSubMemberId(), addVO.getSubRoleId());
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberComplaintDO memberComplaintDO = new MemberComplaintDO();

        // 系统用户和编辑用户的差异保存
        if (!NumberUtil.isNullOrZero(addVO.getByUserId())) {
            UserDO userDO = userRepository.findById(addVO.getByUserId()).orElse(null);
            if (userDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }
            memberComplaintDO.setByUser(userDO);
        } else {
            memberComplaintDO.setByUserEditName(addVO.getByUserEditName());
            memberComplaintDO.setByUserEditPhone(addVO.getByUserEditPhone());
        }

        memberComplaintDO.setStatus(MemberComplaintStatusEnum.WAIT_SUBMIT.getCode());
        memberComplaintDO.setType(addVO.getType());
        memberComplaintDO.setComplaintType(MemberComplaintComplaintTypeEnum.UPPER.getCode());
        memberComplaintDO.setClassify(addVO.getClassify());
        memberComplaintDO.setSubject(addVO.getSubject());
        memberComplaintDO.setMember(relationDO.getMember());
        memberComplaintDO.setRoleId(relationDO.getRoleId());
        memberComplaintDO.setSubMember(relationDO.getSubMember());
        memberComplaintDO.setSubRoleId(relationDO.getSubRoleId());
        memberComplaintDO.setEventTime(DateTimeUtil.parseDateTime(addVO.getEventTime()));
        memberComplaintDO.setEventDesc(addVO.getEventDesc());
        memberComplaintDO.setEventSuggest(addVO.getEventSuggest());
        memberComplaintDO.setAttachments(FileObjectUtil.toBOList(addVO.getAttachments()));
        memberComplaintDO.setCreateTime(LocalDateTime.now());

        memberComplaintRepository.saveAndFlush(memberComplaintDO);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addSubMemberComplaint(HttpHeaders headers, MemberComplaintSubAddReq addVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberRelationDO relationDO = memberRelationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(addVO.getMemberId(), addVO.getRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if (relationDO == null || !relationDO.getSubMember().getId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberComplaintDO memberComplaintDO = new MemberComplaintDO();

        // 系统用户和编辑用户的差异保存
        if (!NumberUtil.isNullOrZero(addVO.getByUserId())) {
            UserDO userDO = userRepository.findById(addVO.getByUserId()).orElse(null);
            if (userDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }

            memberComplaintDO.setByUser(userDO);
        } else {
            memberComplaintDO.setByUserEditName(addVO.getByUserEditName());
            memberComplaintDO.setByUserEditPhone(addVO.getByUserEditPhone());
        }

        memberComplaintDO.setStatus(MemberComplaintStatusEnum.WAIT_SUBMIT.getCode());
        memberComplaintDO.setType(addVO.getType());
        memberComplaintDO.setComplaintType(MemberComplaintComplaintTypeEnum.SUB.getCode());
        memberComplaintDO.setClassify(addVO.getClassify());
        memberComplaintDO.setSubject(addVO.getSubject());
        memberComplaintDO.setMember(relationDO.getMember());
        memberComplaintDO.setRoleId(relationDO.getRoleId());
        memberComplaintDO.setSubMember(relationDO.getSubMember());
        memberComplaintDO.setSubRoleId(relationDO.getSubRoleId());
        memberComplaintDO.setEventTime(DateTimeUtil.parseDateTime(addVO.getEventTime()));
        memberComplaintDO.setEventDesc(addVO.getEventDesc());
        memberComplaintDO.setEventSuggest(addVO.getEventSuggest());
        memberComplaintDO.setAttachments(FileObjectUtil.toBOList(addVO.getAttachments()));
        memberComplaintDO.setCreateTime(LocalDateTime.now());

        memberComplaintRepository.saveAndFlush(memberComplaintDO);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUpperMemberComplaint(HttpHeaders headers, MemberComplaintUpperUpdateReq addVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberComplaintDO memberComplaintDO = memberComplaintRepository.findById(addVO.getId()).orElse(null);
        if (Objects.isNull(memberComplaintDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberComplaintStatusEnum.WAIT_SUBMIT.getCode().equals(memberComplaintDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        MemberRelationDO relationDO = memberRelationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(loginUser.getMemberId(), loginUser.getMemberRoleId(), addVO.getSubMemberId(), addVO.getSubRoleId());
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        // 系统用户和编辑用户的差异保存
        if (!NumberUtil.isNullOrZero(addVO.getByUserId())) {
            UserDO userDO = userRepository.findById(addVO.getByUserId()).orElse(null);
            if (userDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }

            memberComplaintDO.setByUser(userDO);
            memberComplaintDO.setByUserEditName(null);
            memberComplaintDO.setByUserEditPhone(null);
        } else {
            memberComplaintDO.setByUser(null);
            memberComplaintDO.setByUserEditName(addVO.getByUserEditName());
            memberComplaintDO.setByUserEditPhone(addVO.getByUserEditPhone());
        }

        memberComplaintDO.setType(addVO.getType());
        memberComplaintDO.setClassify(addVO.getClassify());
        memberComplaintDO.setSubject(addVO.getSubject());
        memberComplaintDO.setMember(relationDO.getMember());
        memberComplaintDO.setRoleId(relationDO.getRoleId());
        memberComplaintDO.setSubMember(relationDO.getSubMember());
        memberComplaintDO.setSubRoleId(relationDO.getSubRoleId());
        memberComplaintDO.setEventTime(DateTimeUtil.parseDateTime(addVO.getEventTime()));
        memberComplaintDO.setEventDesc(addVO.getEventDesc());
        memberComplaintDO.setEventSuggest(addVO.getEventSuggest());
        memberComplaintDO.setAttachments(FileObjectUtil.toBOList(addVO.getAttachments()));

        memberComplaintRepository.saveAndFlush(memberComplaintDO);

    }

    @Override
    public void updateSubMemberComplaint(HttpHeaders headers, MemberComplaintSubUpdateReq addVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberComplaintDO memberComplaintDO = memberComplaintRepository.findById(addVO.getId()).orElse(null);
        if (Objects.isNull(memberComplaintDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberComplaintStatusEnum.WAIT_SUBMIT.getCode().equals(memberComplaintDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        MemberRelationDO relationDO = memberRelationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(addVO.getMemberId(), addVO.getRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
        if (relationDO == null || !relationDO.getSubMember().getId().equals(loginUser.getMemberId()) || !relationDO.getSubRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        // 系统用户和编辑用户的差异保存
        if (!NumberUtil.isNullOrZero(addVO.getByUserId())) {
            UserDO userDO = userRepository.findById(addVO.getByUserId()).orElse(null);
            if (userDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }

            memberComplaintDO.setByUser(userDO);
            memberComplaintDO.setByUserEditName(null);
            memberComplaintDO.setByUserEditPhone(null);
        } else {
            memberComplaintDO.setByUser(null);
            memberComplaintDO.setByUserEditName(addVO.getByUserEditName());
            memberComplaintDO.setByUserEditPhone(addVO.getByUserEditPhone());
        }

        memberComplaintDO.setType(addVO.getType());
        memberComplaintDO.setClassify(addVO.getClassify());
        memberComplaintDO.setSubject(addVO.getSubject());
        memberComplaintDO.setMember(relationDO.getMember());
        memberComplaintDO.setRoleId(relationDO.getRoleId());
        memberComplaintDO.setSubMember(relationDO.getSubMember());
        memberComplaintDO.setSubRoleId(relationDO.getSubRoleId());
        memberComplaintDO.setEventTime(DateTimeUtil.parseDateTime(addVO.getEventTime()));
        memberComplaintDO.setEventDesc(addVO.getEventDesc());
        memberComplaintDO.setEventSuggest(addVO.getEventSuggest());
        memberComplaintDO.setAttachments(FileObjectUtil.toBOList(addVO.getAttachments()));

        memberComplaintRepository.saveAndFlush(memberComplaintDO);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMemberComplaint(HttpHeaders headers, CommonIdReq idVO) {
        memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberComplaintDO memberComplaintDO = memberComplaintRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberComplaintDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberComplaintStatusEnum.WAIT_SUBMIT.getCode().equals(memberComplaintDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        memberComplaintRepository.deleteById(memberComplaintDO.getId());

    }

    /**
     * 上级投诉建议提交
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void submitMemberComplaint(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberComplaintDO memberComplaintDO = memberComplaintRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberComplaintDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberComplaintStatusEnum.WAIT_SUBMIT.getCode().equals(memberComplaintDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        memberComplaintDO.setStatus(MemberComplaintStatusEnum.WAIT_HANDLE.getCode());
        memberComplaintRepository.saveAndFlush(memberComplaintDO);

        // 提交后发送一条消息给对应的消息模板
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        switch (roleTagEnum) {
            case CUSTOMER:
                messageFeignService.sendSystemMessage(memberComplaintDO.getSubMember().getId(), memberComplaintDO.getSubRoleId(),
                        MessageNoticeEnum.CUSTOMER_COMPLAIN_SUGGEST.getCode(), Collections.singletonList(loginCacheDTO.getUserName()));
                break;
            case SUPPLIER:
                messageFeignService.sendSystemMessage(memberComplaintDO.getSubMember().getId(), memberComplaintDO.getSubRoleId(),
                        MessageNoticeEnum.VENDOR_COMPLAIN_SUGGEST.getCode(), Collections.singletonList(loginCacheDTO.getUserName()));
                break;
            default:
                break;
        }

    }

    /**
     * 投诉建议处理
     *
     * @param headers  Http头部信息
     * @param handleVO 接口参数
     * @param roleTag  角色标签
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handleMemberComplaint(HttpHeaders headers, MemberComplaintHandleReq handleVO, Integer roleTag) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberComplaintDO memberComplaintDO = memberComplaintRepository.findById(handleVO.getId()).orElse(null);
        if (Objects.isNull(memberComplaintDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberComplaintStatusEnum.WAIT_HANDLE.getCode().equals(memberComplaintDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 系统用户和编辑用户的差异保存
        if (!NumberUtil.isNullOrZero(handleVO.getHandleUserId())) {
            UserDO userDO = userRepository.findById(handleVO.getHandleUserId()).orElse(null);
            if (userDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }

            memberComplaintDO.setHandleUser(userDO);
            memberComplaintDO.setHandleUserEditName(null);
            memberComplaintDO.setHandleUserEditPhone(null);
        } else {
            memberComplaintDO.setHandleUser(null);
            memberComplaintDO.setHandleUserEditName(handleVO.getHandleUserEditName());
            memberComplaintDO.setHandleUserEditPhone(handleVO.getHandleUserEditPhone());
        }

        memberComplaintDO.setStatus(MemberComplaintStatusEnum.COMPLETE_HANDLE.getCode());
        memberComplaintDO.setHandleTime(DateTimeUtil.parseDateTime(handleVO.getHandleTime()));
        memberComplaintDO.setHandleResult(handleVO.getHandleResult());
        memberComplaintDO.setHandleAttachments(FileObjectUtil.toBOList(handleVO.getHandleAttachments()));

        memberComplaintRepository.saveAndFlush(memberComplaintDO);

        // 处理完发送一条消息给对应供应商/客户
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        switch (roleTagEnum) {
            case SUPPLIER:
                messageFeignService.sendSystemMessage(memberComplaintDO.getSubMember().getId(), memberComplaintDO.getSubRoleId(),
                        MessageNoticeEnum.VENDOR_COMPLAIN_SUGGEST_FINISH.getCode(), Collections.singletonList(loginCacheDTO.getUserName()));
                break;
            case CUSTOMER:
                messageFeignService.sendSystemMessage(memberComplaintDO.getSubMember().getId(), memberComplaintDO.getSubRoleId(),
                        MessageNoticeEnum.CUSTOMER_COMPLAIN_SUGGEST_FINISH.getCode(), Collections.singletonList(loginCacheDTO.getUserName()));
                break;
            default:
                break;
        }

    }

    /**
     * 下级提交投诉建议
     *
     * @param headers 请求头
     * @param idVO    通用idVO
     * @param roleTag 角色标签
     * @return 返回
     */
    @Override
    public void submitSubMemberComplaint(HttpHeaders headers, CommonIdReq idVO, Integer roleTag) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberComplaintDO memberComplaintDO = memberComplaintRepository.findById(idVO.getId()).orElse(null);
        if (Objects.isNull(memberComplaintDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberComplaintStatusEnum.WAIT_SUBMIT.getCode().equals(memberComplaintDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        memberComplaintDO.setStatus(MemberComplaintStatusEnum.WAIT_HANDLE.getCode());
        memberComplaintRepository.saveAndFlush(memberComplaintDO);

        // 提交后发送一条消息给对应的消息模板
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        switch (roleTagEnum) {
            case CUSTOMER:
                messageFeignService.sendSystemMessage(memberComplaintDO.getMember().getId(), memberComplaintDO.getRoleId(),
                        MessageNoticeEnum.CUSTOMER_MC_COMPLAIN_SUGGEST.getCode(), Collections.singletonList(loginCacheDTO.getUserName()));
                break;
            case SUPPLIER:
                messageFeignService.sendSystemMessage(memberComplaintDO.getMember().getId(), memberComplaintDO.getRoleId(),
                        MessageNoticeEnum.VENDOR_MC_COMPLAIN_SUGGEST.getCode(), Collections.singletonList(loginCacheDTO.getUserName()));
                break;
            default:
                break;
        }

    }

    /**
     * 下级处理投诉建议
     *
     * @param headers  请求头
     * @param handleVO 入参
     * @param roleTag 角色标签
     * @return 返回
     */
    @Override
    public void handleSubMemberComplaint(HttpHeaders headers, MemberComplaintHandleReq handleVO, Integer roleTag) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);
        MemberComplaintDO memberComplaintDO = memberComplaintRepository.findById(handleVO.getId()).orElse(null);
        if (Objects.isNull(memberComplaintDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        if (!MemberComplaintStatusEnum.WAIT_HANDLE.getCode().equals(memberComplaintDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }

        // 系统用户和编辑用户的差异保存
        if (!NumberUtil.isNullOrZero(handleVO.getHandleUserId())) {
            UserDO userDO = userRepository.findById(handleVO.getHandleUserId()).orElse(null);
            if (userDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            }

            memberComplaintDO.setHandleUser(userDO);
            memberComplaintDO.setHandleUserEditName(null);
            memberComplaintDO.setHandleUserEditPhone(null);
        } else {
            memberComplaintDO.setHandleUser(null);
            memberComplaintDO.setHandleUserEditName(handleVO.getHandleUserEditName());
            memberComplaintDO.setHandleUserEditPhone(handleVO.getHandleUserEditPhone());
        }

        memberComplaintDO.setStatus(MemberComplaintStatusEnum.COMPLETE_HANDLE.getCode());
        memberComplaintDO.setHandleTime(DateTimeUtil.parseDateTime(handleVO.getHandleTime()));
        memberComplaintDO.setHandleResult(handleVO.getHandleResult());
        memberComplaintDO.setHandleAttachments(FileObjectUtil.toBOList(handleVO.getHandleAttachments()));

        memberComplaintRepository.saveAndFlush(memberComplaintDO);

        // 处理完发送一条消息给对应供应商/客户
        RoleTagEnum roleTagEnum = RoleTagEnum.getRoleTagEnumByCode(roleTag);
        switch (roleTagEnum) {
            case SUPPLIER:
                messageFeignService.sendSystemMessage(memberComplaintDO.getMember().getId(), memberComplaintDO.getRoleId(),
                        MessageNoticeEnum.VENDOR_MC_COMPLAIN_SUGGEST_FINISH.getCode(), Collections.singletonList(loginCacheDTO.getUserName()));
                break;
            case CUSTOMER:
                messageFeignService.sendSystemMessage(memberComplaintDO.getMember().getId(), memberComplaintDO.getRoleId(),
                        MessageNoticeEnum.CUSTOMER_MC_COMPLAIN_SUGGEST_FINISH.getCode(), Collections.singletonList(loginCacheDTO.getUserName()));
                break;
            default:
                break;
        }

    }
}
