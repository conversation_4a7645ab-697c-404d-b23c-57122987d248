package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrgTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrganizationQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberSelectOrgQueryResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberOrganizationService;
import com.ssy.lingxi.member.service.web.IMemberAbilityOrganizationService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 会员能力 - 会员组织机构服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
@Service
public class MemberAbilityOrganizationServiceImpl implements IMemberAbilityOrganizationService {

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseMemberOrganizationService baseMemberOrganizationService;

    @Override
    public void addMemberOrg(HttpHeaders headers, MemberOrganizationAddReq addVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberOrganizationService.addMemberOrg(loginUser.getMemberId(), addVO);
    }

    /**
     * 根据菜单Id，更新组织机构信息
     *
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @Override
    public void updateMemberOrg(HttpHeaders headers, MemberOrganizationUpdateReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberOrganizationService.updateMemberOrg(loginUser.getMemberId(), updateVO);
    }

    /**
     * 删除一个会员组织架构
     *
     * @param headers HttpHeaders信息
     * @param deleteVO 接口参数
     * @return 操作结果
     */
    @Override
    public void deleteMemberOrg(HttpHeaders headers, MemberOrganizationDeleteReq deleteVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        baseMemberOrganizationService.deleteMemberOrg(loginUser.getMemberId(), deleteVO);
    }

    /**
     * 查询一个会员组织架构
     *
     * @param headers HttpHeaders信息
     * @param getVO 接口参数
     * @return 操作结果
     */
    @Override
    public MemberOrganizationQueryResp getMemberOrg(HttpHeaders headers, MemberOrganizationGetReq getVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberOrganizationService.getMemberOrg(loginUser.getMemberId(), getVO);
    }

    /**
     * 查询所有组织机构信息，以非树形菜单的形式返回
     * 返回组织机构和该组织机构的上一级组织机构信息
     * @param headers HttpHeaders信息
     * @param getVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSelectOrgQueryResp> selectOrg(HttpHeaders headers, MemberSelectOrgGetDataReq getVO) {
        UserLoginCacheDTO loginUser = memberCacheService.checkUserFromCache(headers);
        return baseMemberOrganizationService.selectOrg(loginUser.getMemberId(),getVO);
    }

    /**
     * 查询会员的所有组织架构，以树形菜单的形式返回
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @Override
    public List<MemberOrgTreeResp> treeMemberOrg(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberOrganizationService.treeMemberOrg(loginUser.getMemberId());
    }

    /**
     * 查询会员的（非门店）所有组织架构，以树形菜单的形式返回
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @Override
    public List<MemberOrgTreeResp> nonStoreTreeMemberOrg(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberOrganizationService.nonStoreTreeMemberOrg(loginUser.getMemberId());
    }
}
