package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberUserChannelDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户绑定的渠道Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-04-14
 */
@Repository
public interface MemberUserChannelRepository extends JpaRepository<MemberUserChannelDO, Long>, JpaSpecificationExecutor<MemberUserChannelDO> {

    List<MemberUserChannelDO> findByMemberIdAndRoleId(Long memberId, Long roleId);

    /**
     * 根据会员Id和用户Id查找业务员的角色
     * @param memberId 会员Id
     * @param userId 用户Id
     * @return 返回业务员信息
     */
    List<MemberUserChannelDO> findByMemberIdAndUserId(Long memberId, Long userId);

    boolean existsByMemberId(Long memberId);

    List<MemberUserChannelDO> findByMemberIdAndRoleIdAndUserIdAndMemberRelationIdIn(Long memberId, Long roleId, Long userId, List<Long> relationIds);

    boolean existsByMemberIdAndRoleIdAndMemberRelationIdIn(Long memberId, Long roleId, List<Long> relationIds);
}
