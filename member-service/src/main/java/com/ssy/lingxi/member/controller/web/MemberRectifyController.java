package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.service.web.IMemberRectifyService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员能力 - 会员整改
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/18
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/rectify")
public class MemberRectifyController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IMemberRectifyService memberRectifyService;

    /**
     * 会员整改查询 - 外部状态下拉查询(上级会员)
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/statusList")
    public WrapperResp<List<StatusResp>> listMemberRectifyOuterStatus(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberRectifyService.listMemberRectifyOuterStatus(headers));
    }

    /**
     * 会员信息查询 - 外部状态下拉查询(下级会员)
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/manage/statusList")
    public WrapperResp<List<StatusResp>> listSubMemberRectifyOuterStatus(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberRectifyService.listSubMemberRectifyOuterStatus(headers));
    }

    // ==================================会员整改查询==================================

    /**
     * 会员整改查询 - 会员整改分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/summary/page")
    public WrapperResp<PageDataResp<MemberRectifySummaryPageQueryResp>> pageMemberRectify(@RequestHeader HttpHeaders headers, @Valid MemberAddRectifySummaryPageDataReq queryVO) {
        return WrapperUtil.success(memberRectifyService.pageSummaryMemberRectify(headers, queryVO, roleTag));
    }

    /**
     * 会员整改查询 - 会员整改详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/summary/get")
    public WrapperResp<MemberRectifyResultResp> getMemberRectifyResult(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberRectifyService.getMemberRectifyResult(headers, idVO, roleTag));

    }


    // ==================================待新增整改通知单==================================

    /**
     * 待新增整改通知单 - 会员整改分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitAdd/page")
    public WrapperResp<PageDataResp<MemberRectifyAddPageQueryResp>> pageWaitAddMemberRectify(@RequestHeader HttpHeaders headers, @Valid MemberAddRectifyPageDataReq queryVO) {
        return WrapperUtil.success(memberRectifyService.pageAddMemberRectify(headers, queryVO, roleTag));
    }

    /**
     * 待新增整改通知单 - 会员整改详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitAdd/get")
    public WrapperResp<MemberRectifyResp> getMemberRectify(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberRectifyService.getMemberRectify(headers, idVO, roleTag));
    }

    /**
     * 待确认整改结果 - 会员整改新增
     * @param headers Http头部信息
     * @param addVO 接口参数
     */
    @PostMapping("/waitAdd/add")
    public WrapperResp<Void> addMemberRectify(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberRectifyAddReq addVO) {
         memberRectifyService.addMemberRectify(headers, addVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 待确认整改结果 - 会员整改修改
     * @param headers Http头部信息
     * @param addVO 接口参数
     */
    @PostMapping("/waitAdd/update")
    public WrapperResp<Void> updateMemberRectify(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberRectifyUpdateReq addVO) {
         memberRectifyService.updateMemberRectify(headers, addVO);
        return WrapperUtil.success();
    }

    /**
     * 待确认整改结果 - 会员整改删除
     * @param headers Http头部信息
     * @param idVO 接口参数
     */
    @PostMapping("/waitAdd/delete")
    public WrapperResp<Void> addMemberRectify(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idVO) {
         memberRectifyService.deleteMemberRectify(headers, idVO);
        return WrapperUtil.success();
    }

    /**
     * 待确认整改结果 - 发送
     * @param headers Http头部信息
     * @param idVO 接口参数
     */
    @PostMapping("/waitAdd/send")
    public WrapperResp<Void> sendMemberRectify(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idVO) {
         memberRectifyService.sendMemberRectify(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================待确认整改结果==================================

    /**
     * 待确认整改结果 - 会员整改分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitConfirm/page")
    public WrapperResp<PageDataResp<MemberRectifyConfirmPageQueryResp>> pageWaitConfirmMemberRectify(@RequestHeader HttpHeaders headers, @Valid MemberAddRectifyPageDataReq queryVO) {
        return WrapperUtil.success(memberRectifyService.pageConfirmMemberRectify(headers, queryVO, roleTag));
    }

    /**
     * 待确认整改结果 - 会员整改详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/waitConfirm/get")
    public WrapperResp<MemberRectifyResultResp> getConfirmMemberRectify(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberRectifyService.getMemberRectifyResult(headers, idVO, roleTag));

    }


    /**
     * 待确认整改结果 - 确认
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     */
    @PostMapping("/waitConfirm/confirm")
    public WrapperResp<Void> confirmMemberRectify(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonAgreeReq agreeVO) {
         memberRectifyService.confirmMemberRectify(headers, agreeVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================会员整改管理==================================

    /**
     * 会员整改管理 - 会员整改分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/manage/page")
    public WrapperResp<PageDataResp<MemberRectifyManagePageQueryResp>> pageManageMemberRectify(@RequestHeader HttpHeaders headers, @Valid MemberAddRectifySummaryPageDataReq queryVO) {
        return WrapperUtil.success(memberRectifyService.pageManageMemberRectify(headers, queryVO));
    }

    /**
     * 待新增整改通知单 - 会员整改详情
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/manage/get")
    public WrapperResp<MemberRectifyManageResp> getManageMemberRectify(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
         memberRectifyService.getMemberRectifyManage(headers, idVO);
        return WrapperUtil.success();
    }


    /**
     * 会员整改管理 - 进行整改
     * @param headers Http头部信息
     * @param reportVO 接口参数
     */
    @PostMapping("/manage/updateReport")
    public WrapperResp<Void> updateReportMemberRectify(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberRectifyReportReq reportVO) {
         memberRectifyService.updateReportMemberRectify(headers, reportVO);
        return WrapperUtil.success();
    }

    /**
     * 会员整改管理 - 提交整改
     * @param headers Http头部信息
     * @param idVO 接口参数
     */
    @PostMapping("/manage/rectify")
    public WrapperResp<Void> rectifyMemberRectify(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idVO) {
         memberRectifyService.rectifyMemberRectify(headers, idVO, roleTag);
        return WrapperUtil.success();
    }
}
