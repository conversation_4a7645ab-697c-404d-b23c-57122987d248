package com.ssy.lingxi.member.controller.web.commission;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.commission.InvitationConfigReq;
import com.ssy.lingxi.member.model.req.commission.OrderCommissionConfigReq;
import com.ssy.lingxi.member.model.req.commission.WithdrawalConfigReq;
import com.ssy.lingxi.member.model.resp.commission.CommissionConfigResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.commission.ICommissionConfigService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员能力 - 分佣配置管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/commission/config")
public class CommissionConfigController {

    @Resource
    private ICommissionConfigService commissionConfigService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    // ==================================分佣配置管理===============================

    /**
     * 查看分佣配置
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<CommissionConfigResp> getCommissionConfig(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(commissionConfigService.getCommissionConfig());
    }

    // ==================================邀请分佣配置===============================

    /**
     * 更新邀请分佣配置
     * @param headers Http头部信息
     * @param request 接口参数
     * @return 操作结果
     */
    @PostMapping("/updateInvitationConfig")
    public WrapperResp<Void> updateInvitationConfig(@RequestHeader HttpHeaders headers, @RequestBody @Valid InvitationConfigReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionConfigService.updateInvitationConfig(loginUser, request);
        return WrapperUtil.success();
    }

    // ==================================下单分佣配置===============================

    /**
     * 更新下单分佣配置
     * @param headers Http头部信息
     * @param request 接口参数
     * @return 操作结果
     */
    @PostMapping("/updateOrderCommissionConfig")
    public WrapperResp<Void> updateOrderCommissionConfig(@RequestHeader HttpHeaders headers, @RequestBody @Valid OrderCommissionConfigReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionConfigService.updateOrderCommissionConfig(loginUser, request);
        return WrapperUtil.success();
    }

    // ==================================佣金提现配置===============================

    /**
     * 更新佣金提现配置
     * @param headers Http头部信息
     * @param request 接口参数
     * @return 操作结果
     */
    @PostMapping("/updateWithdrawalConfig")
    public WrapperResp<Void> updateWithdrawalConfig(@RequestHeader HttpHeaders headers, @RequestBody @Valid WithdrawalConfigReq request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        commissionConfigService.updateWithdrawalConfig(loginUser, request);
        return WrapperUtil.success();
    }
}
