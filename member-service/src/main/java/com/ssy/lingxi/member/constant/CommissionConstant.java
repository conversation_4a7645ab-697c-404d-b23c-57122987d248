package com.ssy.lingxi.member.constant;

import java.math.BigDecimal;

/**
 * 分佣相关常量
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
public class CommissionConstant {

    // ==================== 默认配置值 ====================
    
    /**
     * 默认邀请注册奖励金额
     */
    public static final BigDecimal DEFAULT_INVITATION_REGISTRATION_REWARD = BigDecimal.valueOf(10);

    /**
     * 默认邀请认证奖励金额
     */
    public static final BigDecimal DEFAULT_INVITATION_VERIFICATION_REWARD = BigDecimal.valueOf(20);

    /**
     * 默认邀请首单奖励金额
     */
    public static final BigDecimal DEFAULT_INVITATION_FIRST_ORDER_REWARD = BigDecimal.valueOf(50);

    /**
     * 默认单克分佣金额
     */
    public static final BigDecimal DEFAULT_COMMISSION_PER_GRAM = BigDecimal.valueOf(0.1);

    /**
     * 默认分佣比例（1%）
     */
    public static final BigDecimal DEFAULT_COMMISSION_RATE = BigDecimal.valueOf(1);

    /**
     * 默认最大分佣金额
     */
    public static final BigDecimal DEFAULT_MAX_COMMISSION_AMOUNT = BigDecimal.valueOf(1000);

    /**
     * 默认最小分佣金额
     */
    public static final BigDecimal DEFAULT_MIN_COMMISSION_AMOUNT = BigDecimal.valueOf(0.01);

    /**
     * 默认提现最低限额
     */
    public static final BigDecimal DEFAULT_MIN_WITHDRAWAL_AMOUNT = BigDecimal.valueOf(100);

    /**
     * 默认提现手续费率（0.5%）
     */
    public static final BigDecimal DEFAULT_WITHDRAWAL_FEE_RATE = BigDecimal.valueOf(0.5);

    /**
     * 默认单日最大提现次数
     */
    public static final Integer DEFAULT_MAX_DAILY_WITHDRAWALS = 3;

    /**
     * 默认单次最大提现金额
     */
    public static final BigDecimal DEFAULT_MAX_SINGLE_WITHDRAWAL_AMOUNT = BigDecimal.valueOf(10000);

    /**
     * 默认账户余额转可提现余额的延迟天数
     */
    public static final Integer DEFAULT_BALANCE_TO_WITHDRAWABLE_DELAY_DAYS = 0;

    /**
     * 默认邀请码有效期（天数）
     */
    public static final Integer DEFAULT_INVITATION_CODE_VALIDITY_DAYS = 365;

    // ==================== 业务常量 ====================
    
    /**
     * 分佣流水号前缀
     */
    public static final String COMMISSION_TRADE_CODE_PREFIX = "COM";

    /**
     * 提现流水号前缀
     */
    public static final String WITHDRAWAL_TRADE_CODE_PREFIX = "WTH";

    /**
     * 分佣流水号时间格式
     */
    public static final String COMMISSION_TRADE_CODE_DATE_FORMAT = "yyyyMMddHHmmss";

    /**
     * 分佣流水号位数
     */
    public static final Integer COMMISSION_TRADE_CODE_NUM_LEN = 6;

    /**
     * 提现流水号位数
     */
    public static final Integer WITHDRAWAL_TRADE_CODE_NUM_LEN = 6;

    // ==================== 状态常量 ====================
    
    /**
     * 配置状态 - 停用
     */
    public static final Integer CONFIG_STATUS_DISABLED = 0;

    /**
     * 配置状态 - 启用
     */
    public static final Integer CONFIG_STATUS_ENABLED = 1;

    /**
     * 账户状态 - 正常
     */
    public static final Integer ACCOUNT_STATUS_NORMAL = 1;

    /**
     * 账户状态 - 已冻结
     */
    public static final Integer ACCOUNT_STATUS_FROZEN = 2;

    /**
     * 提现状态 - 申请中
     */
    public static final Integer WITHDRAWAL_STATUS_APPLYING = 1;

    /**
     * 提现状态 - 处理中
     */
    public static final Integer WITHDRAWAL_STATUS_PROCESSING = 2;

    /**
     * 提现状态 - 提现成功
     */
    public static final Integer WITHDRAWAL_STATUS_SUCCESS = 3;

    /**
     * 提现状态 - 提现失败
     */
    public static final Integer WITHDRAWAL_STATUS_FAILED = 4;

    /**
     * 提现状态 - 已取消
     */
    public static final Integer WITHDRAWAL_STATUS_CANCELLED = 5;

    /**
     * 银行卡状态 - 正常
     */
    public static final Integer BANK_CARD_STATUS_NORMAL = 1;

    /**
     * 银行卡状态 - 已停用
     */
    public static final Integer BANK_CARD_STATUS_DISABLED = 2;

    /**
     * 银行卡状态 - 已删除
     */
    public static final Integer BANK_CARD_STATUS_DELETED = 3;

    /**
     * 邀请状态 - 已邀请未注册
     */
    public static final Integer INVITATION_STATUS_INVITED_NOT_REGISTERED = 1;

    /**
     * 邀请状态 - 已注册未激活
     */
    public static final Integer INVITATION_STATUS_REGISTERED_NOT_ACTIVATED = 2;

    /**
     * 邀请状态 - 已激活
     */
    public static final Integer INVITATION_STATUS_ACTIVATED = 3;

    // ==================== 交易类型常量 ====================
    
    /**
     * 交易类型 - 邀请注册奖励
     */
    public static final Integer TRADE_TYPE_INVITATION_REWARD = 1;

    /**
     * 交易类型 - 下级消费分佣
     */
    public static final Integer TRADE_TYPE_SUBORDINATE_CONSUMPTION_COMMISSION = 2;

    /**
     * 交易类型 - 提现扣减
     */
    public static final Integer TRADE_TYPE_WITHDRAWAL_DEDUCTION = 3;

    /**
     * 交易类型 - 系统调整
     */
    public static final Integer TRADE_TYPE_SYSTEM_ADJUSTMENT = 4;

    /**
     * 交易类型 - 冻结扣减
     */
    public static final Integer TRADE_TYPE_FREEZE_DEDUCTION = 5;

    /**
     * 交易类型 - 解冻返还
     */
    public static final Integer TRADE_TYPE_UNFREEZE_RETURN = 6;

    // ==================== 操作类型常量 ====================
    
    /**
     * 冻结操作类型 - 冻结
     */
    public static final Integer FREEZE_OPERATION_TYPE_FREEZE = 1;

    /**
     * 冻结操作类型 - 解冻
     */
    public static final Integer FREEZE_OPERATION_TYPE_UNFREEZE = 2;

    /**
     * 操作人角色 - 系统管理员
     */
    public static final Integer OPERATOR_ROLE_SYSTEM_ADMIN = 1;

    /**
     * 操作人角色 - 财务人员
     */
    public static final Integer OPERATOR_ROLE_FINANCE_STAFF = 2;

    /**
     * 操作人角色 - 客服人员
     */
    public static final Integer OPERATOR_ROLE_CUSTOMER_SERVICE = 3;

    /**
     * 操作人角色 - 系统自动
     */
    public static final Integer OPERATOR_ROLE_SYSTEM_AUTO = 4;

    // ==================== 邀请渠道常量 ====================
    
    /**
     * 邀请渠道 - 直接注册
     */
    public static final Integer INVITATION_CHANNEL_DIRECT_REGISTRATION = 1;

    /**
     * 邀请渠道 - 邀请链接
     */
    public static final Integer INVITATION_CHANNEL_INVITATION_LINK = 2;

    /**
     * 邀请渠道 - 二维码
     */
    public static final Integer INVITATION_CHANNEL_QR_CODE = 3;

    /**
     * 邀请渠道 - 其他
     */
    public static final Integer INVITATION_CHANNEL_OTHER = 4;

    // ==================== 银行卡类型常量 ====================
    
    /**
     * 银行卡类型 - 储蓄卡
     */
    public static final Integer BANK_CARD_TYPE_SAVINGS_CARD = 1;

    /**
     * 银行卡类型 - 信用卡
     */
    public static final Integer BANK_CARD_TYPE_CREDIT_CARD = 2;

    // ==================== 限制常量 ====================
    
    /**
     * 用户最大银行卡数量
     */
    public static final Integer MAX_BANK_CARDS_PER_USER = 5;

    /**
     * 邀请码最大重试生成次数
     */
    public static final Integer MAX_INVITATION_CODE_RETRY_TIMES = 10;

    /**
     * 分佣金额精度（小数位数）
     */
    public static final Integer COMMISSION_AMOUNT_SCALE = 2;

    /**
     * 分佣比例精度（小数位数）
     */
    public static final Integer COMMISSION_RATE_SCALE = 2;

    /**
     * 重量精度（小数位数）
     */
    public static final Integer WEIGHT_SCALE = 4;
}
