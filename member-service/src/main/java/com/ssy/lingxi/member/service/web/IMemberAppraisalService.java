package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.model.req.basic.SubMemberIdRoleIdDataReq;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员考评服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
public interface IMemberAppraisalService {

    /**
     * 状态下拉查询
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<StatusResp> listMemberAppraisalStatus(HttpHeaders headers);

    /**
     * 会员考评查询 - 会员考评分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAppraisalSubmitPageQueryResp> pageSummaryMemberAppraisal(HttpHeaders headers, MemberAppraisalSummaryPageDataReq pageVO, Integer roleTag);

    /**
     * 会员考评查询 - 会员考评分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAppraisalSubmitPageQueryResp> pageSummaryMemberAppraisal(HttpHeaders headers, SubMemberIdRoleIdDataReq pageVO, Integer roleTag);


    /**
     * 会员信息 - 会员档案 - 分页查询考评列表
     * @param member 上级会员
     * @param role 上级会员角色
     * @param subMember 下级会员
     * @param subRole 下级会员角色
     * @param current 当前页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberAppraisalPageQueryResp> pageMemberAppraisal(MemberDO member, MemberRoleDO role, MemberDO subMember, MemberRoleDO subRole, int current, int pageSize);

    /**
     * 待新增考评单 - 会员考评分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAppraisalAddPageQueryResp> pageWaitAddMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag);

    /**
     * 待考评打分 - 会员考评分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAppraisalGradePageQueryResp> pageWaitGradeMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag);

    /**
     * 待提交汇总考评结果 - 会员考评分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAppraisalSubmitPageQueryResp> pageWaitSubmitMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag);

    /**
     * 待审核考评结果一级 - 会员考评分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAppraisalAuditPageQueryResp> pageWaitAuditOneMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag);

    /**
     * 待审核考评结果二级 - 会员考评分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAppraisalAuditPageQueryResp> pageWaitAuditTwoMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag);

    /**
     * 待通报考评结果 - 会员考评分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    PageDataResp<MemberAppraisalNotificationPageQueryResp> pageWaitNotificationMemberAppraisal(HttpHeaders headers, MemberAppraisalPageDataReq pageVO, Integer roleTag);

    /**
     * 考评结果查询 - 会员考评分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberAppraisalSubResultPageQueryResp> pageSubResultMemberAppraisal(HttpHeaders headers, MemberAppraisalSubResultPageDataReq pageVO);

    /**
     * 考评结果查询 - 会员考评查询
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberAppraisalSubResultResp getSubMemberAppraisalResult(HttpHeaders headers, CommonIdReq idVO);

    /**
     * 会员考评 - 会员考评结果查询
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberAppraisalResultResp getMemberAppraisalResult(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 会员考评 - 会员考评查询
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberAppraisalResp getMemberAppraisal(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 待考评打分 - 会员考评查询
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    MemberAppraisalResp getWaitGradeMemberAppraisal(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 待新增考评单 - 会员考评新增
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void addMemberAppraisal(HttpHeaders headers, MemberAppraisalAddReq addVO, Integer roleTag);

    /**
     * 待新增考评单 - 会员考评修改
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void updateMemberAppraisal(HttpHeaders headers, MemberAppraisalUpdateReq addVO, Integer roleTag);

    /**
     * 待新增考评单 - 会员考评删除
     * @param headers Http头部信息
     * @param idsVO 接口参数
     * @return 操作结果
     */
    void deleteMemberAppraisal(HttpHeaders headers, CommonIdListReq idsVO);

    /**
     * 待考评打分 - 打分
     * @param headers Http头部信息
     * @param gradeVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void gradeMemberAppraisal(HttpHeaders headers, MemberAppraisalGradeReq gradeVO, Integer roleTag);

    /**
     * 待提交汇总考评结果 - 提交
     * @param headers Http头部信息
     * @param submitVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void submitMemberAppraisal(HttpHeaders headers, MemberAppraisalSubmitReq submitVO, Integer roleTag);

    /**
     * 待审核考评结果一级 - 审核
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void auditOneMemberAppraisal(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag);

    /**
     * 待审核考评结果一级 - 批量审核
     * @param headers Http头部信息
     * @param idsVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void auditOneBatchMemberAppraisal(HttpHeaders headers, CommonIdListReq idsVO, Integer roleTag);

    /**
     * 待审核考评结果二级 - 审核
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void auditTwoMemberAppraisal(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag);

    /**
     * 待审核考评结果二级 - 批量审核
     * @param headers Http头部信息
     * @param idsVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void auditTwoBatchMemberAppraisal(HttpHeaders headers, CommonIdListReq idsVO, Integer roleTag);

    /**
     * 通报考评结果 - 通报
     * @param headers Http头部信息
     * @param idsVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void notificationMemberAppraisal(HttpHeaders headers, CommonIdListReq idsVO, Integer roleTag);
}
