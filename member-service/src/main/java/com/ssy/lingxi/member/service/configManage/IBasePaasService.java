package com.ssy.lingxi.member.service.configManage;

import com.ssy.lingxi.common.model.resp.WrapperResp;

/**
 * （内部临时服务）系统初始化服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-15
 */
public interface IBasePaasService {

    /**
     * 创建平台后台会员及管理员
     * @return 操作结果
     */
    WrapperResp<Void> createManagePlatformAdmin();

    /**
     * 系统初始化时，创建“平台”角色、平台后台管理员账号
     */
    void initPlatformAdmin();

    /**
     * 禁用菜单可配置数据权限时，删除已有的数据权限配置
     * @param menuId 菜单Id
     * @param path 菜单Path
     * @param source 菜单source
     */
    void removeDataAuthAsync(Long menuId, String path, Integer source);
}
