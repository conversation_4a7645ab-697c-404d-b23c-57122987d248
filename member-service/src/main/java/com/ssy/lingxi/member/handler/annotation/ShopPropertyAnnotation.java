package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.ShopPropertyValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 商城属性校验注解
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = ShopPropertyValidator.class)
public @interface ShopPropertyAnnotation {
    String message() default "商城属性不在定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
