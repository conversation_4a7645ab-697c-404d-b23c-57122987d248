package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.model.resp.MemberSalesChannelFindUserIdQueryResp;
import com.ssy.lingxi.member.model.req.basic.UserPageDataReq;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.basic.ChannelListResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.service.feign.IMemberAbilitySalesFeignService;
import com.ssy.lingxi.member.service.web.IMemberAbilitySalesService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 渠道能力 - 业务员管理相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-04-14
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/ability/sales")
public class MemberAbilitySalesController {

    @Resource
    private IMemberAbilitySalesService memberAbilitySalesService;
    @Resource
    private IMemberAbilitySalesFeignService memberAbilityFeignSalesService;

    /**
     * 分页查询已经绑定渠道的业务员列表
     * @param headers HttpHeader信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberSalesPageQueryResp>> pageMemberSales(@RequestHeader HttpHeaders headers, @Valid MemberSalesPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilitySalesService.pageMemberSales(headers, pageVO));
    }

    /**
     * “选择业务员” - 分页查询业务员列表
     * @param headers HttpHeader信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/select")
    public WrapperResp<PageDataResp<MemberSalesSelectQueryResp>> pageSelectMemberSales(@RequestHeader HttpHeaders headers, @Valid UserPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilitySalesService.pageSelectMemberSales(headers, pageVO));
    }

    /**
     * 新增业务员
     * @param headers HttpHeader信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/add")
    public WrapperResp<MemberSalesGetResp> addMemberSales(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserIdReq idVO) {
        return WrapperUtil.success(memberAbilitySalesService.addMemberSales(headers, idVO));
    }

    /**
     * 查询业务员基本信息
     * @param headers HttpHeader信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberSalesGetResp> getMemberSales(@RequestHeader HttpHeaders headers, @Valid MemberUserIdReq idVO) {
        return WrapperUtil.success(memberAbilitySalesService.getMemberSales(headers, idVO));
    }

    /**
     * 分页查询渠道会员列表
     * @param headers HttpHeader信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @PostMapping("/channel/page")
    public WrapperResp<PageDataResp<MemberSalesBindChannelQueryResp>> pageChannels(@RequestHeader HttpHeaders headers, @Valid @RequestBody MemberSalesSelectPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilitySalesService.pageChannels(headers, pageVO));
    }

    /**
     * 新增业务员，绑定/解绑渠道
     * @param headers HttpHeader信息
     * @param bindChannelVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/channel/bind")
    public WrapperResp<Void> bindSalesChannels(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberSalesBindChannelReq bindChannelVO) {
        memberAbilitySalesService.bindSalesChannels(headers, bindChannelVO);
        return WrapperUtil.success();
    }

    /**
     * 解除业务员与渠道的绑定
     * @param headers HttpHeader信息
     * @param unBindChannelVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/channel/unbind")
    public WrapperResp<Void> unBindSalesChannel(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberSalesUnBindChannelReq unBindChannelVO) {
        memberAbilitySalesService.unBindSalesChannel(headers, unBindChannelVO);
        return WrapperUtil.success();
    }

    /**
     * 查询业务员信息、分页查询绑定的渠道列表
     * @param headers HttpHeader信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/channel")
    public WrapperResp<MemberSalesChannelQueryResp> getSalesAndPageChannels(@RequestHeader HttpHeaders headers, @Valid MemberSalesChannelPageDataReq pageVO) {
        return WrapperUtil.success(memberAbilitySalesService.getSalesAndPageChannels(headers, pageVO));
    }

    /**
     * 删除业务员
     * @param headers HttpHeader信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> deleteSales(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberUserIdReq idVO) {
        memberAbilitySalesService.deleteSales(headers, idVO);
        return WrapperUtil.success();
    }

    /**
     * 订单能力-业绩统计-业务员业绩统计-所属机构
     *
     * @param headers 头部信息
     * @return 返回机构信息
     */
    @GetMapping("/organizationList")
    public WrapperResp<List<String>> getMemberOrganizationList(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilitySalesService.getMemberOrganizationList(headers));
    }

    /**
     * 订单能力-业绩统计-订单明细-业务员列表
     *
     * @param headers 头部信息
     * @return 返回业务员列表
     */
    @GetMapping("/channel/list")
    public WrapperResp<List<ChannelListResp>> getChannelList(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityFeignSalesService.getChannelList(headers));
    }

    /**
     * 订单能力-业务员业绩详情-查看业务员详情
     *
     * @param headers 头部信息
     * @param userId  业务员Id
     * @return 返回业务员信息
     */
    @GetMapping(value = "/channel/information")
    public WrapperResp<MemberSalesChannelFindUserIdQueryResp> getChannelInformation(@RequestHeader HttpHeaders headers, @NotNull Long userId) {
        return WrapperUtil.success(memberAbilityFeignSalesService.getChannelInformation(headers, userId));
    }

}
