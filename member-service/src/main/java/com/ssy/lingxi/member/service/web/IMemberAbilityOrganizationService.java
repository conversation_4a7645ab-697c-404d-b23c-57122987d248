package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrgTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrganizationQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberSelectOrgQueryResp;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员能力 - 会员组织机构服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
public interface IMemberAbilityOrganizationService {

    /**
     * 新增会员组织架构菜单
     * @param headers HttpHeaders信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void addMemberOrg(HttpHeaders headers, MemberOrganizationAddReq addVO);

    /**
     * 根据菜单Id，更新组织机构信息
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateMemberOrg(HttpHeaders headers, MemberOrganizationUpdateReq updateVO);

    /**
     * 删除一个会员组织架构
     * @param headers Http头部信息
     * @param deleteVO 接口参数
     * @return 操作结果
     */
    void deleteMemberOrg(HttpHeaders headers, MemberOrganizationDeleteReq deleteVO);

    /**
     * 查询一个会员组织架构
     * @param headers Http头部信息
     * @param getVO 接口参数
     * @return 操作结果
     */
    MemberOrganizationQueryResp getMemberOrg(HttpHeaders headers, MemberOrganizationGetReq getVO);

    /**
     * 查询所有组织机构信息，以非树形菜单的形式返回
     * 返回组织机构和该组织机构的上一级组织机构信息
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    PageDataResp<MemberSelectOrgQueryResp> selectOrg(@RequestHeader HttpHeaders headers, @Valid MemberSelectOrgGetDataReq getVO);

    /**
     * 查询会员的所有组织架构，以树形菜单的形式返回
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    List<MemberOrgTreeResp> treeMemberOrg(HttpHeaders headers);

    /**
     * 查询会员的（非门店）所有组织架构，以树形菜单的形式返回
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    List<MemberOrgTreeResp> nonStoreTreeMemberOrg(HttpHeaders headers);
}
