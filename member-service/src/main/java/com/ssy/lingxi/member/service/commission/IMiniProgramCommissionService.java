package com.ssy.lingxi.member.service.commission;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.commission.CommissionDetailQueryReq;
import com.ssy.lingxi.member.model.req.commission.CommissionWithdrawalQueryReq;
import com.ssy.lingxi.member.model.req.commission.InvitedCustomerQueryReq;
import com.ssy.lingxi.member.model.resp.commission.*;

import java.util.List;

/**
 * 小程序佣金服务接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
public interface IMiniProgramCommissionService {

    /**
     * 获取佣金账户信息
     *
     * @param loginUser 登录用户
     * @return 佣金账户信息
     */
    MiniProgramAccountInfoResp getAccountInfo(UserLoginCacheDTO loginUser);

    /**
     * 获取我的佣金客户概览
     *
     * @param loginUser 登录用户
     * @return 佣金客户概览
     */
    MyCommissionCustomersResp getMyCommissionCustomers(UserLoginCacheDTO loginUser);

    /**
     * 分页查询邀请客户列表
     *
     * @param loginUser 登录用户
     * @param request   查询请求
     * @return 分页结果
     */
    PageDataResp<InvitedCustomerResp> getInvitedCustomerPage(UserLoginCacheDTO loginUser, InvitedCustomerQueryReq request);

    /**
     * 查询邀请客户统计信息
     *
     * @param loginUser 登录用户
     * @param request   查询请求（与getInvitedCustomerPage相同的查询条件）
     * @return 统计结果（总人数、累计佣金金额）
     */
    InvitedCustomerStatisticsResp getInvitedCustomerStatistics(UserLoginCacheDTO loginUser, InvitedCustomerQueryReq request);

    /**
     * 查看客户明细（按月份返回佣金明细）
     *
     * @param loginUser  登录用户
     * @param customerId 客户id
     * @param year       年份
     * @return 客户明细
     */
    List<CustomerCommissionDetailResp> getCustomerDetail(UserLoginCacheDTO loginUser, Long customerId, Integer year);

    /**
     * 分页查询佣金收益记录列表
     *
     * @param loginUser 登录用户
     * @param request   查询请求
     * @return 分页结果
     */
    PageDataResp<CommissionIncomeRecordResp> getCommissionIncomeRecords(UserLoginCacheDTO loginUser, CommissionDetailQueryReq request);

    /**
     * 分页查询佣金待收益记录列表
     *
     * @param loginUser 登录用户
     * @param request   查询请求
     * @return 分页结果
     */
    PageDataResp<PendingCommissionRecordResp> getPendingCommissionRecords(UserLoginCacheDTO loginUser, CommissionDetailQueryReq request);

    /**
     * 分页查询提现记录列表
     *
     * @param loginUser 登录用户
     * @param request   查询请求
     * @return 分页结果
     */
    PageDataResp<MiniProgramWithdrawalResp> getWithdrawalRecords(UserLoginCacheDTO loginUser, CommissionWithdrawalQueryReq request);
}
