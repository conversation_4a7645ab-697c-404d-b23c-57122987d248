package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;

import java.util.List;

/**
 * 会员、用户信息相关内部Feign服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-06-03
 */
public interface IMemberDetailFeignService {

    /**
     * 查询用户Web端菜单权限Url列表
     * @param userId 用户Id
     * @param memberId 会员id
     * @param roleId 会员角色id
     * @return 查询结果
     */
    List<String> getUserWebAuthUrls(Long userId, Long memberId, Long roleId);

    /**
     * 根据会员id查询用户
     * @param memberIds 会员id
     * @return 查询结果
     */
    List<MemberFeignMsgByMemberIdResp> getUserByMemberIds(List<Long> memberIds);

    /**
     * 根据角色id查询用户
     * @param roleIds 角色id
     * @return 查询结果
     */
    List<MemberFeignMsgByRoleIdResp> getUserByRoleIds(List<Long> roleIds);

    /**
     * 结算能力 - 查询入库分类信息中，主营品类及结算方式
     * @param relationVO 接口参数
     * @return 查询结果，如无配置返回空列表
     */
    List<MemberCategoryFeignResp> findMemberBusinessCategories(MemberFeignRelationReq relationVO);

    /**
     * 根据用户Id查询用户信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberFeignUserDetailResp findMemberUser(MemberFeignUserIdReq idVO);

    /**
     * 根据会员id和角色id，查询会员角色为服务提供者的下级会员列表
     * @param memberVO 上级会员信息
     * @return 查询结果
     */
    List<MemberManageQueryResp> subordinateMemberList(MemberFeignSubordinateMemberReq memberVO);

    /**
     * 根据会员id和角色id，查询会员角色为服务消费者的上级会员列表
     * @param memberVO 下级会员信息
     * @return 查询结果
     */
    List<MemberManageQueryResp> superiorMemberList(MemberFeignSuperiorMemberReq memberVO);

    /**
     * 是否具有支付及查看订单价格权限
     * @param req 请求参数
     * @return 查询结果
     */
    Boolean hasOrderAuth(UserIdFeignReq req);

}
