package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrgTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrganizationQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberSelectOrgQueryResp;
import com.ssy.lingxi.member.service.web.IMemberAbilityOrganizationService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 系统能力 - 权限管理 - 组织机构管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-29
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/org")
public class MemberAbilityOrganizationController {

    @Resource
    private IMemberAbilityOrganizationService memberAbilityOrganizationService;

    /**
     * 新增会员组织架构菜单
     * @param headers  HttpHeaders信息
     * @param addVO 接口参数
     * @return 新增结果
     */
    @RequestMapping("/add")
    public WrapperResp<Void> addMemberOrg(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberOrganizationAddReq addVO) {
         memberAbilityOrganizationService.addMemberOrg(headers, addVO);
        return WrapperUtil.success();
    }

    /**
     * 根据菜单Id，更新组织机构信息
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 更新结果
     */
    @RequestMapping("/update")
    public WrapperResp<Void> updateMemberOrg(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberOrganizationUpdateReq updateVO) {
         memberAbilityOrganizationService.updateMemberOrg(headers, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 删除一个组织机构信息
     * @param headers HttpHeaders信息
     * @param deleteVO 接口参数
     * @return 删除结果
     */
    @RequestMapping("/delete")
    public WrapperResp<Void> deleteMemberOrg(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberOrganizationDeleteReq deleteVO) {
         memberAbilityOrganizationService.deleteMemberOrg(headers, deleteVO);
        return WrapperUtil.success();
    }

    /**
     * 查询一个组织机构信息
     * @param headers HttpHeaders信息
     * @param getVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberOrganizationQueryResp> getMemberOrg(@RequestHeader HttpHeaders headers, @Valid MemberOrganizationGetReq getVO) {
        return WrapperUtil.success(memberAbilityOrganizationService.getMemberOrg(headers, getVO));
    }

    /**
     * 查询所有组织机构信息，以非树形菜单的形式返回
     * 返回组织机构和该组织机构的上一级组织机构信息
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @GetMapping("/selectOrg")
    public WrapperResp<PageDataResp<MemberSelectOrgQueryResp>> selectOrg(@RequestHeader HttpHeaders headers, @Valid MemberSelectOrgGetDataReq getVO) {
        return WrapperUtil.success(memberAbilityOrganizationService.selectOrg(headers, getVO));
    }

    /**
     * 查询所有组织机构信息，以树形菜单的形式返回
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @GetMapping("/tree")
    public WrapperResp<List<MemberOrgTreeResp>> getMemberOrgTreeByMemberId(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityOrganizationService.treeMemberOrg(headers));
    }

    /**
     * 查询会员的（非门店）所有组织架构，以树形菜单的形式返回
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @GetMapping("/non/store/tree")
    public WrapperResp<List<MemberOrgTreeResp>> nonStoreTreeMemberOrg(@RequestHeader HttpHeaders headers){
        return WrapperUtil.success(memberAbilityOrganizationService.nonStoreTreeMemberOrg(headers));
    }
}
