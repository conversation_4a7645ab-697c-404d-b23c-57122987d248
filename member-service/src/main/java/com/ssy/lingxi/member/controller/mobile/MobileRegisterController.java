package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.model.req.AreaCodeReq;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.ipMonitor.anno.PersonInfoIpMonitor;
import com.ssy.lingxi.component.ipMonitor.anno.RegisterIpMonitor;
import com.ssy.lingxi.component.ipMonitor.anno.SmsCodeIpMonitor;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.login.MobileRegisterReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordByEmailCodeReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordBySmsCodeReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterResultResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterTypeMenuResp;
import com.ssy.lingxi.member.model.resp.login.MultiAccInfoResp;
import com.ssy.lingxi.member.service.mobile.IMobileRegisterService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * App - 会员注册相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-02
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/register")
public class MobileRegisterController {

    @Resource
    private IMobileRegisterService mobileRegisterService;

    /**
     * “账号注册” - 检查手机号码是否被注册使用
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    @PersonInfoIpMonitor
    @PostMapping("/phone/check")
    public WrapperResp<Void> checkPhoneRegistered(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneReq phoneReq) {
        mobileRegisterService.checkPhoneRegistered(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 检查邮箱是否被注册使用
     * @param headers Http头部信息
     * @param emailReq 邮箱
     * @return 检查结果
     */
    @PersonInfoIpMonitor
    @PostMapping("/email/check")
    public WrapperResp<Void> checkEmailRegistered(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailReq emailReq) {
        mobileRegisterService.checkEmailRegistered(headers, emailReq);
        return WrapperUtil.success();
    }

    /**
     * 校验邀请码是否存在
     * @param headers Http头部信息
     * @param invitationCodeReq 邀请码
     * @return 检查结果
     */
    @PostMapping("/invitation-code/check")
    public WrapperResp<Void> checkInvitationCodeExists(@RequestHeader HttpHeaders headers, @RequestBody @Valid InvitationCodeReq invitationCodeReq) {
        mobileRegisterService.checkInvitationCodeExists(headers, invitationCodeReq);
        return WrapperUtil.success();
    }

    /**
     * 发送注册时短信验证码
     * @param headers Http头部信息
     * @param phoneReq 接口参数
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @PostMapping("/sms")
    public WrapperResp<Void> sendRegisterSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneReq phoneReq) {
        mobileRegisterService.sendRegisterSmsCode(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * “手机号找回密码” - 检查手机号码是否存在
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    @PersonInfoIpMonitor
    @PostMapping("/psw/phone/check")
    public WrapperResp<Void> checkPhoneExistsByMobile(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneReq phoneReq) {
        mobileRegisterService.checkPhoneExistsByMobile(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 发送手机号找回密码时的短信验证码
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @PostMapping("/psw/sms")
    public WrapperResp<Void> sendResetPasswordSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneReq phoneReq) {
        mobileRegisterService.sendResetPasswordSmsCode(headers, phoneReq);
        return WrapperUtil.success();
    }

    /**
     * 校验手机号找回密码时的短信验证码是否正确
     * @param headers Http头部信息
     * @param phoneSmsReq 手机号码、验证码
     * @return 发送结果
     */
    @PersonInfoIpMonitor
    @PostMapping("/psw/sms/check")
    public WrapperResp<List<MultiAccInfoResp>> checkResetPasswordSmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid PhoneSmsReq phoneSmsReq) {
        return WrapperUtil.success(mobileRegisterService.checkResetPasswordSmsCode(headers, phoneSmsReq));
    }

    /**
     * 根据短信验证码重设密码
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/reset/sms")
    public WrapperResp<Void> resetPasswordBySmsCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid ResetPasswordBySmsCodeReq codeVO) {
        mobileRegisterService.resetPasswordBySmsCode(headers, codeVO);
        return WrapperUtil.success();
    }

    /**
     * 发送邮箱找回密码时的邮件
     * @param headers Http头部信息
     * @param emailReq 邮箱地址
     * @return 发送结果
     */
    @SmsCodeIpMonitor
    @PostMapping("/psw/email")
    public WrapperResp<Void> sendResetPasswordEmail(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailReq emailReq) {
        mobileRegisterService.sendResetPasswordEmail(headers, emailReq);
        return WrapperUtil.success();
    }

    /**
     * 校验邮箱找回密码时的邮件验证码是否正确
     * @param headers Http头部信息
     * @param emailVO 邮箱地址
     * @return 发送结果
     */
    @PersonInfoIpMonitor
    @PostMapping("/psw/email/check")
    public WrapperResp<List<MultiAccInfoResp>> checkResetPasswordEmailCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid EmailSmsReq emailVO) {
        return WrapperUtil.success(mobileRegisterService.checkResetPasswordEmailCode(headers, emailVO));
    }

    /**
     * 根据邮箱验证码重设密码
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/reset/email")
    public WrapperResp<Void> resetPasswordByEmailCode(@RequestHeader HttpHeaders headers, @RequestBody @Valid ResetPasswordByEmailCodeReq codeVO) {
        mobileRegisterService.resetPasswordByEmailCode(headers, codeVO);
        return WrapperUtil.success();
    }

    /**
     * 注册页面 - 获取会员类型、业务类型
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/type")
    public WrapperResp<List<MemberRegisterTypeMenuResp>> getRegisterTypeMenu(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(mobileRegisterService.getRegisterTypePageContent(headers));
    }

    /**
     * 注册页面 - 获取完善会员资料页面内容
     * @param headers Http头部信息
     * @param detailVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/detail")
    public WrapperResp<List<MemberConfigGroupResp>> getRegisterDetailMenu(@RequestHeader HttpHeaders headers, MemberDetailReq detailVO) {
        return WrapperUtil.success(mobileRegisterService.getRegisterDetailPageContent(headers, detailVO));
    }

    /**
     * 注册页面 - 查询省列表
     * @return 查询结果
     */
    @GetMapping("/province")
    public WrapperResp<List<AreaCodeNameResp>> listProvince(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(mobileRegisterService.listProvince(headers));
    }

    /**
     * 注册页面 - 根据省编码，查询市列表
     * @param codeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/city")
    public WrapperResp<List<AreaCodeNameResp>> listCitiesByProvinceCode(@RequestHeader HttpHeaders headers, @RequestParam @Valid AreaCodeReq codeVO) {
        return WrapperUtil.success(mobileRegisterService.listCitiesByProvinceCode(headers, codeVO));
    }

    /**
     * 注册页面 - 根据市编码，查询区列表
     * @param codeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/district")
    public WrapperResp<List<AreaCodeNameResp>> listDistrictsByCityCode(@RequestHeader HttpHeaders headers, @Valid AreaCodeReq codeVO) {
        return WrapperUtil.success(mobileRegisterService.listDistrictsByCityCode(headers, codeVO));
    }

    /**
     * 会员注册
     * @param headers Http头部信息
     * @param registerVO 接口参数
     * @return 注册结果
     */
    @RegisterIpMonitor
    @RequestMapping(value = "", method = RequestMethod.POST)
    public WrapperResp<MemberRegisterResultResp> registerMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MobileRegisterReq registerVO) {
        return WrapperUtil.success(mobileRegisterService.registerPlatformMember(headers, registerVO));
    }
}
