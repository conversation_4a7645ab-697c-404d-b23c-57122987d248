package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.api.member.ActualControllerSyncReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.maintenance.ActualControllerPageReq;
import com.ssy.lingxi.member.model.req.maintenance.ActualControllerBindOrChangeReq;
import com.ssy.lingxi.member.model.req.maintenance.ActualControllerSaveOrUpdateReq;
import com.ssy.lingxi.member.model.req.maintenance.EditActualControllerLineOfCreditReq;
import com.ssy.lingxi.member.model.resp.customer.ActualControllerDetailResp;
import com.ssy.lingxi.member.model.resp.customer.ActualControllerPageResp;
import com.ssy.lingxi.member.model.resp.customer.ActualControllerParticularsResp;

import javax.validation.Valid;

/**
 * 实控人服务接口
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/7
 */
public interface IActualControllerService {

    /**
     * 分页查询实控人
     *
     * @param loginUser 当前登录用户
     * @param pageReq 请求参数
     * @return 实控人
     */
    PageDataResp<ActualControllerPageResp> page(UserLoginCacheDTO loginUser, ActualControllerPageReq pageReq);

    /**
     * 修改实控人信息
     *
     * @param req 接口参数
     */
    void saveOrUpdate(UserLoginCacheDTO sysUser, ActualControllerSaveOrUpdateReq req);

    /**
     * 保存或更新实控人信息
     * @param loginUser 登录用户
     * @param req 请求参数
     */
    void bindOrChange(UserLoginCacheDTO loginUser, ActualControllerBindOrChangeReq req);

    /**
     * 同步实控人信息
     * @param req 实控人信息
     */
    void syncActualController(ActualControllerSyncReq req);

    /**
     * 获取实控人明细
     *
     * @param sysUser 当前登录用户
     * @param req 实控人ID
     * @return 实控人明细
     */
    ActualControllerParticularsResp getParticulars(UserLoginCacheDTO sysUser, @Valid CommonIdReq req);

    /**
     * 编辑实控人授信额度
     *
     * @param editActualControllerLineOfCreditReq 请求参数
     * @return 实控人授信额度编辑结果
     */
    Boolean editActualControllerLineOfCredit(EditActualControllerLineOfCreditReq editActualControllerLineOfCreditReq);

    /**
     * 编辑会员授信额度
     * @param req
     * @return
     */
    Boolean editMemberLineOfCredit(EditActualControllerLineOfCreditReq req);
}
