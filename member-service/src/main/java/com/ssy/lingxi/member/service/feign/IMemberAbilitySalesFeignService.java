package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.api.model.req.MemberSalesCountFeignReq;
import com.ssy.lingxi.member.api.model.req.MemberSalesFindMemberReq;
import com.ssy.lingxi.member.api.model.req.MemberSalesFindUserIdReq;
import com.ssy.lingxi.member.api.model.resp.*;
import com.ssy.lingxi.member.model.resp.basic.ChannelListResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-29
 */
public interface IMemberAbilitySalesFeignService {

    /**
     * 远程调用 - 分页查询已经绑定渠道的业务员列表
     *
     * @param pageVO 接口参数
     * @return 返回符合条件的业务员列表和下级会员
     */
    PageDataResp<MemberFeignSalesCountResp> getMemberSalesList(MemberSalesCountFeignReq pageVO);

    /**
     * 获取角色类型为消费者的角色Id
     *
     * @return 返回角色类型为消费者的角色Id
     */
    List<Long> getRoleIds();

    /**
     * 远程调用 - 分页查询该业务员下面的渠道会员
     *
     * @param pageVO 下级会员搜索条件
     * @return 下级会员信息
     */
    PageDataResp<MemberSalesFeignPageQueryResp> findByUserId(MemberSalesFindUserIdReq pageVO);

    /**
     * 远程调用 - 查询已经绑定渠道的业务员列表
     *
     * @param pageVO 接口参数
     * @return 返回下级会员信息
     */
    List<MemberSalesFindMemberQueryResp> findMemberSales(MemberSalesFindMemberReq pageVO);


    /**
     * 查看业务员详情
     *
     * @param headers 头部信息
     * @param userId  业务员逐渐Id
     * @return 操作接口
     */
    MemberSalesChannelFindUserIdQueryResp getChannelInformation(HttpHeaders headers, Long userId);


    /**
     * 订单能力-业绩统计-订单明细-业务员列表
     *
     * @param headers 头部信息
     * @return 返回业务员列表
     */
    List<ChannelListResp> getChannelList(HttpHeaders headers);

    /**
     * 根据数据权限展示业务员
     *
     * @param pageVO 查询条件
     * @return 返回符合条件的业务员
     */
    List<MemberUserResp> getUserIds(MemberSalesCountFeignReq pageVO);
}
