package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberUserAuthDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户权限Jpa仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-13
 */
@Repository
public interface MemberUserAuthRepository extends JpaRepository<MemberUserAuthDO, Long>, JpaSpecificationExecutor<MemberUserAuthDO> {
    List<MemberUserAuthDO> findByUserIn(List<UserDO> users);

    List<MemberUserAuthDO> findFirstByUser(UserDO user);
}
