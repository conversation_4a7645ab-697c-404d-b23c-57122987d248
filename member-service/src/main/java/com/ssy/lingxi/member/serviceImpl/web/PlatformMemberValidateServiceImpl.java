package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.LanguageEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.AopProxyUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskListBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskResultBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberReceiveInvitationDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRightDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.dto.MemberUserDTO;
import com.ssy.lingxi.member.model.req.validate.MemberValidateCommitReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateUpdateAuthReq;
import com.ssy.lingxi.member.model.req.validate.PlatformMemberValidateQueryDataReq;
import com.ssy.lingxi.member.model.resp.basic.LevelAndTagResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberQuerySearchConditionResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformPageQueryMemberResp;
import com.ssy.lingxi.member.model.resp.validate.PlatformMemberValidateDetailResp;
import com.ssy.lingxi.member.model.resp.validate.WorkFlowStepResp;
import com.ssy.lingxi.member.repository.MemberLevelConfigRepository;
import com.ssy.lingxi.member.repository.MemberReceiveInvitationRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.repository.MemberRoleRepository;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.ILogisticsFeignService;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.feign.IPayFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberAbilityUserService;
import com.ssy.lingxi.member.service.web.IMemberReceiveInvitationService;
import com.ssy.lingxi.member.service.web.IPlatformMemberValidateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 业务平台管理后台 -会员审核接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
@Slf4j
@Service
public class PlatformMemberValidateServiceImpl implements IPlatformMemberValidateService {
    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberLevelConfigRepository levelConfigRepository;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseAuthService baseAuthService;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private IPayFeignService payFeignService;

    @Resource
    private IMessageFeignService messageFeignService;

    @Resource
    private ILogisticsFeignService logisticsFeignService;

    @Resource
    private IMemberAbilityUserService memberAbilityUserService;

    @Resource
    private MemberReceiveInvitationRepository memberReceiveInvitationRepository;

    @Resource
    private IMemberReceiveInvitationService memberReceiveInvitationService;

    @Resource
    private IBaseTokenManageService tokenManageService;

    /**
     * 获取会员权限树
     * @param headers HttpHeaders信息
     * @param validateReq 接口参数
     * @return 操作结果
     */
    @Override
    public AuthTreeResp getAuthTree(HttpHeaders headers, MemberValidateReq validateReq) {
        //检验是否使用平台后台账号登录
        memberCacheService.needLoginFromManagePlatform(headers);

        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateReq.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(validateReq.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        // 平台会员权限树的管理，可视菜单按钮权限范围不超过其角色的权限范围
        String currentLanguage = LanguageEnum.getCurrentLanguage();
        return baseAuthService.getAuthTree(
                        relationDO.getMenuAuth(),
                        relationDO.getButtonAuth(),
                        CompletableFuture.supplyAsync(() -> baseAuthService.getMenuSetByIdSet(BitMapUtil.toIdSet(relationDO.getSubRole().getMenuAuth()), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor),
                        CompletableFuture.supplyAsync(() -> baseAuthService.getButtonSetByIdSet(BitMapUtil.toIdSet(relationDO.getSubRole().getButtonAuth()), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor));
    }

    /**
     * 修改审核会员的左侧菜单权限
     * @param headers HttpHeaders信息
     * @param updateAuthReq 接口参数
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateValidateAuth(HttpHeaders headers, MemberValidateUpdateAuthReq updateAuthReq) {
        //检验是否使用平台后台账号登录
        memberCacheService.needLoginFromManagePlatform(headers);

        MemberRelationDO relationDO = relationRepository.findById(updateAuthReq.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(updateAuthReq.getMemberId()) || !relationDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberRoleDO memberRoleDO = relationDO.getSubRole();
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        // 更新关系表权限
        relationDO.setMenuAuth(BitMapUtil.toByteArray(updateAuthReq.getMenuIdList()));
        relationDO.setButtonAuth(BitMapUtil.toByteArray(updateAuthReq.getButtonIdList()));
        relationRepository.save(relationDO);

        // 更新该会员的用户和用户角色权限
        baseAuthService.rebuildMemberAuth(relationDO.getSubMember());

        //删除当前会员的所有token
        CompletableFuture.runAsync(() -> tokenManageService.memberOffline(Collections.singletonList(relationDO.getSubMemberId())), ThreadPoolConfig.asyncDefaultExecutor);
    }

    /**
     * 获取“待提交审核会员”页面中各个查询条件下拉选择框的内容
     *
     * @return 操作结果
     */
    @Override
    public PlatformMemberQuerySearchConditionResp getToBeCommitPageCondition(HttpHeaders headers) {
        //检验是否使用平台后台账号登录
        memberCacheService.needLoginFromManagePlatform(headers);

        PlatformMemberQuerySearchConditionResp conditionVO = getPageConditionWithoutInnerOuterStatus();
        //内部状态
        List<DropdownItemResp> itemList = new ArrayList<>();
        itemList.add(new DropdownItemResp(0, "内部状态(全部)"));
        itemList.add(new DropdownItemResp(PlatformInnerStatusEnum.TO_BE_COMMIT.getCode(), PlatformInnerStatusEnum.TO_BE_COMMIT.getMessage()));
        conditionVO.setInnerStatus(itemList);

        //外部状态
        itemList = new ArrayList<>();
        itemList.add(new DropdownItemResp(0, "外部状态(全部)"));
        itemList.add(new DropdownItemResp(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode(), MemberOuterStatusEnum.PLATFORM_VERIFYING.getMessage()));
        conditionVO.setOuterStatus(itemList);

        return conditionVO;
    }

    /**
     * 分页查询“待提交审核会员”列表
     *
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<PlatformPageQueryMemberResp> pageToBeCommitMembers(HttpHeaders headers, PlatformMemberValidateQueryDataReq memberQueryVO) {
        //检验是否使用平台后台账号登录
        memberCacheService.needLoginFromManagePlatform(headers);
        return findBySpecification(PlatformInnerStatusEnum.TO_BE_COMMIT, memberQueryVO);
    }

    /**
     * 获取“提交审核会员”页面会员信息
     *
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public PlatformMemberValidateDetailResp getToBeCommitMemberDetail(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        return findValidateMemberDetail(loginUser, validateVO, PlatformInnerStatusEnum.TO_BE_COMMIT);
    }

    /**
     * 提交审核会员
     * @param headers HttpHeaders信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    @Override
    public void commitMemberValidate(HttpHeaders headers, MemberValidateCommitReq commitVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        execValidateStep(loginUser, commitVO, Stream.of(PlatformInnerStatusEnum.TO_BE_COMMIT).collect(Collectors.toList()));
    }

    /**
     * 批量提交审核会员
     *
     * @param headers HttpHeaders信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    @Override
    public void batchCommitMemberValidate(HttpHeaders headers, List<MemberValidateReq> validateVOList) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        batchExecValidateStep(loginUser, validateVOList, Stream.of(PlatformInnerStatusEnum.TO_BE_COMMIT).collect(Collectors.toList()));
    }


    /**
     * 获取“待审核会员(一级)”页面中各个查询条件下拉选择框的内容
     *
     * @return 操作结果
     */
    @Override
    public PlatformMemberQuerySearchConditionResp getToBeValidateStep1PageCondition(HttpHeaders headers) {
        //检验是否使用平台后台账号登录
        memberCacheService.needLoginFromManagePlatform(headers);
        PlatformMemberQuerySearchConditionResp conditionVO = getPageConditionWithoutInnerOuterStatus();
        //内部状态
        List<DropdownItemResp> itemList = new ArrayList<>();
        itemList.add(new DropdownItemResp(0, "内部状态(全部)"));
        itemList.add(new DropdownItemResp(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP1.getCode(), PlatformInnerStatusEnum.TO_BE_VERIFY_STEP1.getMessage()));
        conditionVO.setInnerStatus(itemList);

        //外部状态
        itemList = new ArrayList<>();
        itemList.add(new DropdownItemResp(0, "外部状态(全部)"));
        itemList.add(new DropdownItemResp(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode(), MemberOuterStatusEnum.PLATFORM_VERIFYING.getMessage()));
        conditionVO.setOuterStatus(itemList);

        return conditionVO;
    }

    /**
     * 分页查询“待审核会员(一级)”列表
     *
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<PlatformPageQueryMemberResp> pageToBeValidateStep1Members(HttpHeaders headers, PlatformMemberValidateQueryDataReq memberQueryVO) {
        memberCacheService.needLoginFromManagePlatform(headers);
        return findBySpecification(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP1, memberQueryVO);
    }

    /**
     * 获取“审核会员(一级)”页面会员信息
     *
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public PlatformMemberValidateDetailResp getValidateStep1MemberDetail(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        return findValidateMemberDetail(loginUser, validateVO, PlatformInnerStatusEnum.TO_BE_VERIFY_STEP1);
    }

    /**
     * 审核会员(一级)
     * @param headers HttpHeaders信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    @Override
    public void validateMemberStep1(HttpHeaders headers, MemberValidateCommitReq commitVO) {
        //检验是否使用平台后台账号登录
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
         execValidateStep(loginUser, commitVO, Stream.of(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP1).collect(Collectors.toList()));
    }

    /**
     * 批量审核会员(一级)
     *
     * @param headers HttpHeaders信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    @Override
    public void batchValidateMemberStep1(HttpHeaders headers, List<MemberValidateReq> validateVOList) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
         batchExecValidateStep(loginUser, validateVOList, Stream.of(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP1).collect(Collectors.toList()));
    }

    /**
     * 获取“待审核会员(二级)”页面中各个查询条件下拉选择框的内容
     *
     * @return 操作结果
     */
    @Override
    public PlatformMemberQuerySearchConditionResp getToBeValidateStep2PageCondition(HttpHeaders headers) {
        //检验是否使用平台后台账号登录
        memberCacheService.needLoginFromManagePlatform(headers);
        PlatformMemberQuerySearchConditionResp conditionVO = getPageConditionWithoutInnerOuterStatus();
        //内部状态
        List<DropdownItemResp> itemList = new ArrayList<>();
        itemList.add(new DropdownItemResp(0, "内部状态(全部)"));
        itemList.add(new DropdownItemResp(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP2.getCode(), PlatformInnerStatusEnum.TO_BE_VERIFY_STEP2.getMessage()));
        conditionVO.setInnerStatus(itemList);

        //外部状态
        itemList = new ArrayList<>();
        itemList.add(new DropdownItemResp(0, "外部状态(全部)"));
        itemList.add(new DropdownItemResp(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode(), MemberOuterStatusEnum.PLATFORM_VERIFYING.getMessage()));
        conditionVO.setOuterStatus(itemList);

        return conditionVO;
    }

    /**
     * 分页查询“待审核会员(二级)”列表
     *
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<PlatformPageQueryMemberResp> pageToBeValidateStep2Members(HttpHeaders headers, PlatformMemberValidateQueryDataReq memberQueryVO) {
        memberCacheService.needLoginFromManagePlatform(headers);
        return findBySpecification(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP2, memberQueryVO);
    }

    /**
     * 获取“审核会员(二级)”页面会员信息
     *
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public PlatformMemberValidateDetailResp getValidateStep2MemberDetail(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        return findValidateMemberDetail(loginUser, validateVO, PlatformInnerStatusEnum.TO_BE_VERIFY_STEP2);
    }

    /**
     * 审核会员(二级)
     * @param headers HttpHeaders信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    @Override
    public void validateMemberStep2(HttpHeaders headers, MemberValidateCommitReq commitVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
         execValidateStep(loginUser, commitVO, Stream.of(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP2).collect(Collectors.toList()));
    }

    /**
     * 批量审核会员(二级)
     *
     * @param headers HttpHeaders信息
     * @param validateVOList 接口参数List
     * @return 操作结果
     */
    @Override
    public void batchValidateMemberStep2(HttpHeaders headers, List<MemberValidateReq> validateVOList) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
         batchExecValidateStep(loginUser, validateVOList, Stream.of(PlatformInnerStatusEnum.TO_BE_VERIFY_STEP2).collect(Collectors.toList()));
    }

    /**
     * 获取“待确认会员审核结果”页面中各个查询条件下拉选择框的内容
     *
     * @return 操作结果
     */
    @Override
    public PlatformMemberQuerySearchConditionResp getToBeConfirmPageCondition(HttpHeaders headers) {
        //检验是否使用平台后台账号登录
        memberCacheService.needLoginFromManagePlatform(headers);
        PlatformMemberQuerySearchConditionResp conditionVO = getPageConditionWithoutInnerOuterStatus();
        //内部状态
        List<DropdownItemResp> itemList = new ArrayList<>();
        itemList.add(new DropdownItemResp(0, "内部状态(全部)"));
        itemList.add(new DropdownItemResp(PlatformInnerStatusEnum.COMMIT_NOT_PASSED.getCode(), PlatformInnerStatusEnum.COMMIT_NOT_PASSED.getMessage()));
        itemList.add(new DropdownItemResp(PlatformInnerStatusEnum.VERIFY_STEP1_NOT_PASSED.getCode(), PlatformInnerStatusEnum.VERIFY_STEP1_NOT_PASSED.getMessage()));
        itemList.add(new DropdownItemResp(PlatformInnerStatusEnum.VERIFY_STEP2_NOT_PASSED.getCode(), PlatformInnerStatusEnum.VERIFY_STEP2_NOT_PASSED.getMessage()));
        itemList.add(new DropdownItemResp(PlatformInnerStatusEnum.TO_CONFIRM.getCode(), PlatformInnerStatusEnum.TO_CONFIRM.getMessage()));
        conditionVO.setInnerStatus(itemList);

        //外部状态
        itemList = new ArrayList<>();
        itemList.add(new DropdownItemResp(0, "外部状态(全部)"));
        itemList.add(new DropdownItemResp(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode(), MemberOuterStatusEnum.PLATFORM_VERIFYING.getMessage()));
        conditionVO.setOuterStatus(itemList);

        return conditionVO;
    }

    /**
     * 分页查询“待确认会员审核结果”列表
     *
     * @param memberQueryVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<PlatformPageQueryMemberResp> pageToBeConfirmMembers(HttpHeaders headers, PlatformMemberValidateQueryDataReq memberQueryVO) {
        memberCacheService.needLoginFromManagePlatform(headers);
        Integer innerStatus = memberQueryVO.getInnerStatus();
        List<PlatformInnerStatusEnum> innerStatusEnums = (innerStatus == null || innerStatus == 0) ? Stream.of(PlatformInnerStatusEnum.COMMIT_NOT_PASSED, PlatformInnerStatusEnum.VERIFY_STEP1_NOT_PASSED, PlatformInnerStatusEnum.VERIFY_STEP2_NOT_PASSED, PlatformInnerStatusEnum.TO_CONFIRM).collect(Collectors.toList()) : Stream.of(PlatformInnerStatusEnum.parse(innerStatus)).collect(Collectors.toList());
        return findBySpecification(innerStatusEnums, memberQueryVO);
    }

    /**
     * 获取“确认会员审核结果”页面会员信息
     *
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Override
    public PlatformMemberValidateDetailResp getConfirmValidateMemberDetail(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        List<PlatformInnerStatusEnum> innerStatusEnums = Stream.of(PlatformInnerStatusEnum.COMMIT_NOT_PASSED, PlatformInnerStatusEnum.VERIFY_STEP1_NOT_PASSED, PlatformInnerStatusEnum.VERIFY_STEP2_NOT_PASSED, PlatformInnerStatusEnum.TO_CONFIRM).collect(Collectors.toList());
        return findValidateMemberDetail(loginUser, validateVO, innerStatusEnums);
    }

    /**
     * 确认会员审核结果
     * @param headers HttpHeaders信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void confirmMemberValidate(HttpHeaders headers, MemberValidateCommitReq commitVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
         execValidateStep(loginUser, commitVO, Stream.of(PlatformInnerStatusEnum.COMMIT_NOT_PASSED, PlatformInnerStatusEnum.VERIFY_STEP1_NOT_PASSED, PlatformInnerStatusEnum.VERIFY_STEP2_NOT_PASSED, PlatformInnerStatusEnum.TO_CONFIRM).collect(Collectors.toList()));
    }

    /**
     * 批量确认会员审核结果
     *
     * @param headers HttpHeaders信息
     * @param validateVOList 接口参数
     * @return 操作结果
     */
    @Override
    public void batchConfirmMemberValidate(HttpHeaders headers, List<MemberValidateReq> validateVOList) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        batchExecValidateStep(loginUser, validateVOList, Stream.of(PlatformInnerStatusEnum.COMMIT_NOT_PASSED, PlatformInnerStatusEnum.VERIFY_STEP1_NOT_PASSED, PlatformInnerStatusEnum.VERIFY_STEP2_NOT_PASSED, PlatformInnerStatusEnum.TO_CONFIRM).collect(Collectors.toList()));
    }


    /**
     * 构造查询页面下拉菜单内容
     * @return 菜单内容
     */
    private PlatformMemberQuerySearchConditionResp getPageConditionWithoutInnerOuterStatus() {
        PlatformMemberQuerySearchConditionResp conditionVO = new PlatformMemberQuerySearchConditionResp();

        //会员状态
        List<DropdownItemResp> itemList = new ArrayList<>();
        for(Integer key : MemberStatusEnum.toCodeList()) {
            itemList.add(new DropdownItemResp(key, MemberStatusEnum.getCodeMessage(key)));
        }
        conditionVO.setStatus(itemList);

        //会员类型
        conditionVO.setMemberTypes(baseMemberValidateService.getSubMemberTypeList(Arrays.asList(MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

        //会员角色（按照Id升序排序）
        conditionVO.setMemberRoles(memberRoleRepository.findAll().stream().filter(r -> !r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).sorted(Comparator.comparingLong(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList()));

        //注册来源
        itemList = new ArrayList<>();
        for(Integer key : MemberRegisterSourceEnum.toList()) {
            itemList.add(new DropdownItemResp(key, MemberRegisterSourceEnum.getCodeMessage(key)));
        }
        conditionVO.setSource(itemList);

        //平台会员的等级
        List<LevelAndTagResp> levelList = levelConfigRepository.findByLevelTypeAndStatus(MemberLevelTypeEnum.PLATFORM.getCode(), EnableDisableStatusEnum.ENABLE.getCode())
                .stream()
                .map(memberLevelConfigDO -> new LevelAndTagResp(memberLevelConfigDO.getLevel(), memberLevelConfigDO.getLevelTag(), memberLevelConfigDO.getSubRoleId()))
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(LevelAndTagResp::getLevel))), ArrayList::new))
                .stream()
                .sorted(Comparator.comparing(LevelAndTagResp::getLevel))
                .collect(Collectors.toList());
        conditionVO.setMemberLevels(levelList);

        return conditionVO;
    }

    /**
     * 分页查询审核会员列表
     * @param innerStatusEnum 内部状态
     * @param queryVO 接口参数
     * @return 查询结果
     */
    private PageDataResp<PlatformPageQueryMemberResp> findBySpecification(PlatformInnerStatusEnum innerStatusEnum, PlatformMemberValidateQueryDataReq queryVO) {
        return findBySpecification(Stream.of(innerStatusEnum).collect(Collectors.toList()), queryVO);
    }

    /**
     * 分页查询审核会员的列表
     * @param innerStatusEnums 内部状态列表
     * @param queryVO 接口参数
     * @return 查询结果
     */
    private PageDataResp<PlatformPageQueryMemberResp> findBySpecification(List<PlatformInnerStatusEnum> innerStatusEnums, PlatformMemberValidateQueryDataReq queryVO) {
        Specification<MemberRelationDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //平台会员关系
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            //内部状态
            if(innerStatusEnums.size() == 1) {
                list.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), innerStatusEnums.get(0).getCode()));
            } else {
                Expression<Integer> exp = root.get("innerStatus").as(Integer.class);
                list.add(exp.in(innerStatusEnums.stream().map(PlatformInnerStatusEnum::getCode).collect(Collectors.toList())));
            }

            //注册起始时间
            if(StringUtils.hasLength(queryVO.getStartDate())) {
                LocalDateTime startDate = LocalDateTime.parse(queryVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), startDate));
            }

            //注册结束日期
            if(StringUtils.hasLength(queryVO.getEndDate())) {
                LocalDateTime endDate = LocalDateTime.parse(queryVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), endDate));
            }

            //会员角色
            if(NumberUtil.notNullOrZero(queryVO.getRoleId())) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), queryVO.getRoleId()));
            }

            //外部状态
            if(NumberUtil.notNullOrZero(queryVO.getOuterStatus())) {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), queryVO.getOuterStatus()));
            }

            //会员名称
            Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
            if(StringUtils.hasLength(queryVO.getName())) {
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + queryVO.getName().trim() + "%"));
            }

            //注册来源
            if(NumberUtil.notNullOrZero(queryVO.getSource())) {
                list.add(criteriaBuilder.equal(subMemberJoin.get("source").as(Integer.class), queryVO.getSource()));
            }

            //会员等级
            if(NumberUtil.notNullOrZero(queryVO.getLevel())) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), queryVO.getLevel()));
            }

            //会员类型
            if(NumberUtil.notNullOrZero(queryVO.getMemberType())) {
                Join<Object, Object> memberTypeJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(memberTypeJoin.get("memberType"), queryVO.getMemberType()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(queryVO.getCurrent() - 1, queryVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList =  relationRepository.findAll(spec, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            PlatformPageQueryMemberResp memberVO = new PlatformPageQueryMemberResp();
            memberVO.setMemberId(relationDO.getSubMember().getId());
            memberVO.setValidateId(relationDO.getId());
            memberVO.setName(relationDO.getSubMember().getName());
            memberVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            memberVO.setRoleId(relationDO.getSubRoleId());
            memberVO.setRoleName(relationDO.getSubRole().getRoleName());
            memberVO.setSource(relationDO.getSubMember().getSource());
            memberVO.setSourceName(MemberRegisterSourceEnum.getCodeMessage(relationDO.getSubMember().getSource()));
            memberVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            memberVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
            memberVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            memberVO.setStatus(relationDO.getStatus());
            memberVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
            memberVO.setInnerStatus(relationDO.getInnerStatus());
            memberVO.setInnerStatusName(PlatformInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()));
            memberVO.setOuterStatus(relationDO.getOuterStatus());
            memberVO.setOuterStatusName(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));
            return memberVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 查询审核会员详情
     * @param loginUser 登录用户
     * @param validateVO 接口参数
     * @param innerStatusEnum 平台审核内部状态
     * @return 查询结果
     */
    private PlatformMemberValidateDetailResp findValidateMemberDetail(UserLoginCacheDTO loginUser, MemberValidateReq validateVO, PlatformInnerStatusEnum innerStatusEnum) {
        return findValidateMemberDetail(loginUser, validateVO, Stream.of(innerStatusEnum).collect(Collectors.toList()));
    }

    /**
     * 查询审核会员详情
     * @param loginUser 登录用户
     * @param validateVO 接口参数
     * @param innerStatusEnums 平台内部审核状态列表
     * @return 操作结果
     */
    private PlatformMemberValidateDetailResp findValidateMemberDetail(UserLoginCacheDTO loginUser, MemberValidateReq validateVO, List<PlatformInnerStatusEnum> innerStatusEnums) {
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId()) || !relationDO.getMemberId().equals(loginUser.getMemberId())) {
           // return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //判断审核状态
        if(innerStatusEnums.stream().noneMatch(innerStatusEnum -> innerStatusEnum.getCode().equals(relationDO.getInnerStatus()))) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_STATUS_INCORRECT);

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_STATUS_INCORRECT);
        }

        MemberDO memberDO = relationDO.getSubMember();
        if(memberDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO memberRoleDO = relationDO.getSubRole();
        if(memberRoleDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }


        MemberLevelRightDO levelRightDO = relationDO.getLevelRight();
        PlatformMemberValidateDetailResp detailVO = new PlatformMemberValidateDetailResp();
        detailVO.setOuterVerifySteps(baseMemberValidateService.getPlatformValidateOuterSteps(relationDO.getRole().getRoleName(), RoleTagEnum.MEMBER.getCode()));
        detailVO.setCurrentOuterStep(2);

        //设置内外审核步骤
        //从工作流服务获得内部审核流程信息
        WorkflowTaskListBO stepResult = workflowFeignService.listMemberProcessSteps(validateVO.getMemberId(), relationDO.getValidateTask().getProcessKey(), relationDO.getValidateTask().getTaskId());

        List<WorkFlowStepResp> innerStepList = stepResult.getStepList().stream().map(s -> {
            WorkFlowStepResp flowStepVO = new WorkFlowStepResp();
            flowStepVO.setStep(s.getStep());
            flowStepVO.setRoleName(s.getRoleName());
            flowStepVO.setStepName(s.getStepName());
            return flowStepVO;
        }).collect(Collectors.toList());
        detailVO.setInnerVerifySteps(innerStepList);
        detailVO.setCurrentInnerStep(stepResult.getCurrentStep());

        //基本信息
        detailVO.setValidateId(relationDO.getId());
        detailVO.setMemberId(memberDO.getId());
        detailVO.setName(StringUtils.hasLength(memberDO.getName()) ? memberDO.getName() : "");
        detailVO.setOuterStatus(relationDO.getOuterStatus());
        detailVO.setOuterStatusName(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));
        detailVO.setInnerStatus(relationDO.getInnerStatus());
        detailVO.setInnerStatusName(PlatformInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()));
        detailVO.setMemberTypeName(MemberTypeEnum.getName(memberRoleDO.getMemberType()));
        detailVO.setRoleName(memberRoleDO.getRoleName());
        detailVO.setLevelTag(levelRightDO == null ? "" : levelRightDO.getLevelTag());
        detailVO.setAccount(memberDO.getAccount());
        detailVO.setPhone(memberDO.getPhone());
        detailVO.setEmail(StringUtils.hasLength(memberDO.getEmail()) ? memberDO.getEmail() : "");
        detailVO.setCreateTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
        detailVO.setStatus(relationDO.getStatus());
        detailVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        detailVO.setGroups(baseMemberRegisterDetailService.groupMemberRegisterDetailText(memberDO, MemberDetailVersionEnum.TO_BE_VALIDATE));

        //历史流转记录
        detailVO.setHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, RoleTagEnum.MEMBER.getCode()));

        //内部订单流转记录
        detailVO.setInnerHistory(baseMemberHistoryService.listMemberInnerHistory(relationDO, RoleTagEnum.MEMBER.getCode()));

        return detailVO;
    }

    /**
     * 批量执行审核
     * @param loginUser 登录用户
     * @param validateVOList 接口参数
     * @return 审核结果
     */
    public void batchExecValidateStep(UserLoginCacheDTO loginUser, List<MemberValidateReq> validateVOList, List<PlatformInnerStatusEnum> innerStatusEnums) {
        if(CollectionUtils.isEmpty(validateVOList)) {
            //return WrapperUtil.fail(ResponseCodeEnum.REQUEST_LIST_PARAM_CHECK_FAILED);
            throw new BusinessException(ResponseCodeEnum.REQUEST_LIST_PARAM_CHECK_FAILED);
        }

        List<Long> relationIds = validateVOList.stream().map(MemberValidateReq::getValidateId).collect(Collectors.toList());
        List<MemberRelationDO> relationList = relationRepository.findAllById(relationIds);
        if(relationIds.size() != relationList.size() || relationList.stream().anyMatch(relationDO -> !relationDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode()))) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        for (MemberRelationDO relationDO : relationList) {
            MemberValidateReq validateVO = validateVOList.stream().filter(v -> v.getValidateId().equals(relationDO.getId())).findFirst().orElse(null);
            if(validateVO == null || !validateVO.getMemberId().equals(relationDO.getSubMemberId()))  {
                //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            }
        }

        for (MemberRelationDO relationDO : relationList) {
            AopProxyUtil.getCurrentProxy(this.getClass()).execValidateStep(loginUser, relationDO, MemberValidateAgreeEnum.AGREE.getCode(), "", innerStatusEnums);
//            if(result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
//                return result;
//            }
        }

        //return WrapperUtil.success();
    }

    /**
     * 执行审核步骤
     * @param loginUser 登录用户信息
     * @param commitVO 接口参数
     * @return 操作结果
     */
    public void execValidateStep(UserLoginCacheDTO loginUser, MemberValidateCommitReq commitVO, List<PlatformInnerStatusEnum> innerStatusEnums) {
        MemberRelationDO relationDO = relationRepository.findById(commitVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getSubMemberId().equals(commitVO.getMemberId()) || !relationDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        AopProxyUtil.getCurrentProxy(this.getClass()).execValidateStep(loginUser, relationDO, commitVO.getAgree(), commitVO.getReason(), innerStatusEnums);
    }

    /**
     * 执行审核步骤
     * @param loginUser 登录用户
     * @param relationDO 会员关系
     * @param agree  审核结果
     * @param reason 审核意见
     * @param innerStatusEnums 内部审核状态列表
     * @return 执行结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    public void execValidateStep(UserLoginCacheDTO loginUser, MemberRelationDO relationDO, Integer agree, String reason, List<PlatformInnerStatusEnum> innerStatusEnums) {
        //审核不通过原因
        if(agree.equals(MemberValidateAgreeEnum.DISAGREE.getCode()) && !StringUtils.hasLength(reason)) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_DISAGREE_REASON_CAN_NOT_BE_EMPTY);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_DISAGREE_REASON_CAN_NOT_BE_EMPTY);
        }

        //TaskId如果为空，说明流程已经结束
        if(!StringUtils.hasLength(relationDO.getValidateTask().getTaskId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_HAS_COMPLETED);

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_HAS_COMPLETED);
        }

        //检查状态
        if(innerStatusEnums.stream().noneMatch(innerStatusEnum -> innerStatusEnum.getCode().equals(relationDO.getInnerStatus()))) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_STATUS_INCORRECT);

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_VALIDATE_STATUS_INCORRECT);
        }

        //记录执行工作流任务之前的内部状态，用于向报表服务发送消息
        int lastInnerStatus = relationDO.getInnerStatus();
        //调用工作流接口
        log.info("===执行审核工作流 start， relationId:{}, 内部状态：{}", relationDO.getId(), relationDO.getInnerStatus());
        WorkflowTaskResultBO taskResult = workflowFeignService.execMemberProcess(relationDO, agree);
        relationDO.getValidateTask().setTaskId(taskResult.getTaskId());
        relationDO.setInnerStatus(taskResult.getInnerStatus());
        relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode());
        log.info("===执行审核工作流 end， relationId:{}, 内部状态：{}", relationDO.getId(), relationDO.getInnerStatus());

        //根据返回的taskId是否为空来判断流程是否结束
        if(!StringUtils.hasLength(taskResult.getTaskId())) {
            if(taskResult.getInnerStatus().equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode())) {
                relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode());
                relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
                relationDO.setValidateMsg("");
                relationDO.setDepositTime(LocalDateTime.now());

                //如果审核通过，通知支付服务，新增资金账户
                payFeignService.notifyMemberAssetAccount(relationDO.getMemberId(), relationDO.getMember().getName(), relationDO.getRoleId(), relationDO.getRole().getRoleName(), relationDO.getSubMemberId(), relationDO.getSubMember().getCode(), relationDO.getSubMember().getName(), relationDO.getSubRoleId(), relationDO.getSubRoleName(), MemberLevelTypeEnum.PLATFORM.getCode(), relationDO.getSubRole().getMemberType(), relationDO.getStatus());
            } else {
                relationDO.setValidateMsg(StringUtils.hasLength(reason) ? reason : "");
                relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode());
            }

            //无论是否审核通过，记录外部历史记录
            baseMemberHistoryService.saveMemberOuterHistory(relationDO, MemberValidateHistoryOperationEnum.parseString(taskResult.getOperation()), reason);

            //无论是否审核通过，更新注册资料版本
            baseMemberRegisterDetailService.updateMemberRegisterDetailToUsing(relationDO.getSubMember());
        }

        relationRepository.saveAndFlush(relationDO);

        // 根据注册来源修改会员能力审核的状态
        WrapperResp<Void> updateResult = updateMemberRelationValidateStatus(relationDO.getRole().getRoleName(), relationDO.getRelSource(), relationDO.getOuterStatus(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), reason);
        if(updateResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            //return WrapperUtil.fail(updateResult.getCode(), updateResult.getMessage());

            throw new BusinessException(updateResult.getCode(), updateResult.getMessage());
        }

        //记录内部操作记录
        baseMemberHistoryService.saveMemberInnerHistory(relationDO, loginUser, MemberValidateHistoryOperationEnum.parseString(taskResult.getOperation()), reason);

        //异步实时消息服务，物流服务
        messageFeignService.sendMemberValidateMessage(relationDO, RoleTagEnum.MEMBER.getCode());
        logisticsFeignService.initMemberLogisticsAsync(relationDO);

        if (PlatformInnerStatusEnum.VERIFY_PASSED.getCode().equals(relationDO.getInnerStatus())) {
            MemberReceiveInvitationDO memberReceiveInvitation = memberReceiveInvitationRepository.findBySubMemberIdAndSubRoleIdAndInviteType(relationDO.getSubMemberId(), relationDO.getSubRoleId(), MemberInviteTypeEnum.INVITE.getCode());
            if (Objects.nonNull(memberReceiveInvitation)) {
                memberReceiveInvitation.setState(EnableDisableStatusEnum.ENABLE.getCode());
                memberReceiveInvitation.setInvitationTime(LocalDateTime.now());
                memberReceiveInvitationService.saveInvitation(memberReceiveInvitation);
                messageFeignService.sendMemberInvitationMessage(memberReceiveInvitation, Optional.ofNullable(memberReceiveInvitation.getSubRoleTag()).orElse(RoleTagEnum.MEMBER.getCode()));
            }
        }
        //return WrapperUtil.success();
    }

    /**
     * 根据平台会员审核的结果，更新会员能力关系的状态
     *
     * @param platformRelation 平台会员关系
     * @return 操作结果
     */
    @Override
    public WrapperResp<Void> updateMemberRelationValidateStatus(MemberRelationDO platformRelation) {
        return updateMemberRelationValidateStatus(platformRelation.getRole().getRoleName(), platformRelation.getRelSource(), platformRelation.getOuterStatus(), platformRelation.getSubMemberId(), platformRelation.getSubRoleId(), "");
    }


    /**
     * 根据平台会员审核的结果，更新会员能力关系的状态
     * @param platformRoleName 平台会员角色名称
     * @param relationSource  平台会员关系的注册来源
     * @param platformOuterStatus 平台会员关系的外部状态
     * @param subMemberId     下级会员Id
     * @param subRoleId       下级角色Id
     * @param reason          平台会员的审核意见
     */
    private WrapperResp<Void> updateMemberRelationValidateStatus(String platformRoleName, Integer relationSource, Integer platformOuterStatus, Long subMemberId, Long subRoleId, String reason) {
        if(!relationSource.equals(MemberRelationSourceEnum.MEMBER_CREATE.getCode())) {
            return WrapperUtil.success();
        }

        if(!platformOuterStatus.equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode()) && !platformOuterStatus.equals(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode())) {
            return WrapperUtil.success();
        }

        MemberRelationDO relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(subMemberId, subRoleId, MemberRelationTypeEnum.OTHER.getCode());
        if(relationDO == null) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //根据“是否曾经审核通过”来判断，平台会员审核流程是由会员导入功能发起的（需要入库流程），还是由会员信息查询中修改了注册资料后发起的（不需要入库流程）
        if(relationDO.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode())) {
            return WrapperUtil.success();
        }

        //如果是“无需审核”流程，直接设置状态
        if(relationDO.getValidateTask().getProcessKey().equals(MemberConstant.EMPTY_PLATFORM_VALIDATE_PROCESS_KEY)) {
            int lastInnerStatus = relationDO.getInnerStatus();

            relationDO.setDepositTime(LocalDateTime.now());
            relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
            relationDO.setInnerStatus(MemberInnerStatusEnum.VERIFY_PASSED.getCode());
            relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode());

            relationRepository.saveAndFlush(relationDO);

            //入库审核通过，将入库资料的版本改为“Using”，否则不处理（继续修改）
            baseMemberDepositDetailService.updateDepositDetailToUsing(relationDO);

            payFeignService.notifyMemberCredit(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
            payFeignService.notifyMemberAssetAccount(relationDO.getMemberId(), relationDO.getMember().getName(), relationDO.getRoleId(), relationDO.getRole().getRoleName(), relationDO.getSubMemberId(), relationDO.getSubMember().getCode(), relationDO.getSubMember().getName(), relationDO.getSubRoleId(), relationDO.getSubRoleName(), relationDO.getSubMemberLevelTypeEnum(), relationDO.getSubRole().getMemberType(), relationDO.getStatus());

            //记录外部历史记录
            baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.VALIDATE_PASS, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");

            MemberUserDTO userDTO = memberAbilityUserService.findMemberUser(relationDO.getUserId());

            //记录内部审核记录
            baseMemberHistoryService.saveMemberInnerHistory(relationDO, userDTO.getUserId(), userDTO.getUserName(), userDTO.getUserRoleName(), userDTO.getUserOrgName(), userDTO.getUserJobTitle(), MemberValidateHistoryOperationEnum.VALIDATE_PASS, "");

            //发送实时消息
            messageFeignService.sendMemberValidateMessage(relationDO, Optional.ofNullable(relationDO.getSubRoleTag()).orElse(RoleTagEnum.MEMBER.getCode()));

        } else {
            log.info("===执行会员审核工作流 start， relationId:{}, 内部状态：{}", relationDO.getId(), relationDO.getInnerStatus());
            //启动会员入库流程
            WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(relationDO);

            relationDO.setInnerStatus(taskResult.getInnerStatus());
            log.info("===执行会员审核工作流 start， relationId:{}, 内部状态：{}", relationDO.getId(), relationDO.getInnerStatus());
            relationDO.getValidateTask().setTaskId(taskResult.getTaskId());
            //平台会员审核通过后，会进入到会员入库审核，所以将外部状态置为“待审核会员入库”
            //平台会员审核不通过，将外部状态置为“平台审核不通过”
            relationDO.setOuterStatus(platformOuterStatus.equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode()) ? MemberOuterStatusEnum.DEPOSITING.getCode() : MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode());

            relationRepository.saveAndFlush(relationDO);

            //外部流转记录
            baseMemberHistoryService.saveMemberOuterHistory(relationDO, platformRoleName, MemberValidateHistoryOperationEnum.PLATFORM_VALIDATE, MemberOuterStatusEnum.getCodeMsg(platformOuterStatus), StringUtils.hasLength(reason) ? reason : "");
        }

        return WrapperUtil.success();
    }
}
