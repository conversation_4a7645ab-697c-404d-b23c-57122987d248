package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.manage.api.model.resp.parameter.PlatformParameterManageResp;

import java.util.List;

/**
 * 调用平台管理服务Feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-03
 */
public interface IManageFeignService {

    /**
     * 敏感词过滤
     * @param sentence 要过滤的语句
     * @return 过滤后的语句
     */
    String filterSensitiveWord(String sentence);

    /**
     * 敏感词过滤
     * @param sentenceList 要过滤的语句列表
     * @return 过滤后的语句列表
     */
    List<String> filterSensitiveWordList(List<String> sentenceList);

    /**
     * 查询平台参数配置
     * @param code 参数编码
     * @return 返回结果
     */
    PlatformParameterManageResp parameterManageDetails(String code);

    /**
     * 判断是否开启SAAS多租户(内部接口)
     * @return 是否开启
     */
    Boolean enableMultiTenancy();
}
