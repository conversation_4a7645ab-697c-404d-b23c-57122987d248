package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.basic.OrganizationPageDataReq;
import com.ssy.lingxi.member.model.req.manage.MemberAndRoleIdReq;
import com.ssy.lingxi.member.model.resp.basic.MemberRegisterTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberRightScoreResp;
import com.ssy.lingxi.member.model.resp.basic.UserDetailResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrganizationQueryResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseMemberDetailService;
import com.ssy.lingxi.member.service.base.IBaseMemberLevelConfigService;
import com.ssy.lingxi.member.service.base.IBaseMemberRegisterDetailService;
import com.ssy.lingxi.member.service.web.IMemberCommonBusinessService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 会员“首页”服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
@Service
public class MemberCommonBusinessServiceImpl implements IMemberCommonBusinessService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private IBaseMemberDetailService baseMemberDetailService;

    /**
     * 查询会员平台权益积分、上级会员下的权益积分
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public MemberRightScoreResp getMemberRightPoint(HttpHeaders headers, MemberAndRoleIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberLevelConfigService.getMemberRightPoint(idVO.getMemberId(), idVO.getRoleId(), loginUser.getMemberId(), loginUser.getMemberRoleId());
    }

    /**
     * 查询会员标签注册资料
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public MemberRegisterTagResp getMemberRegisterTagDetail(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberRegisterDetailService.getMemberRegisterTagDetail(loginUser.getMemberId());
    }

    /**
     * 查询用户注册资料
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @Override
    public UserDetailResp getUserDetail(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberRegisterDetailService.getUserDetail(loginUser.getUserId());
    }

    /**
     * 分页查询会员组织机构
     *
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberOrganizationQueryResp> pageMemberOrganizations(HttpHeaders headers, OrganizationPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return baseMemberDetailService.pageMemberOrganizations(loginUser.getMemberId(), pageVO.getCode(), pageVO.getTitle(), pageVO.getCurrent(), pageVO.getPageSize());
    }
}
