package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.UserRoleDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员自定义角色操作类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
@Repository
public interface UserRoleRepository extends JpaRepository<UserRoleDO, Long>, JpaSpecificationExecutor<UserRoleDO> {
    UserRoleDO findFirstByMemberAndId(MemberDO memberDO, Long id);

    boolean existsByMemberAndRoleName(MemberDO memberDO, String roleName);

    boolean existsByMemberAndRoleNameAndIdNot(MemberDO memberDO, String roleName, Long id);

    List<UserRoleDO> findByMemberAndUserType(MemberDO member, Integer userType);

    List<UserRoleDO> findAllByMemberId(Long memberId);
}
