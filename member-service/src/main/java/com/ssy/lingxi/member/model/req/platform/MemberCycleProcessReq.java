package com.ssy.lingxi.member.model.req.platform;

import com.ssy.lingxi.engine.api.model.req.ProcessEngineRuleReq;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 会员生命周期变更流程规则
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-30
 **/
@Data
public class MemberCycleProcessReq implements Serializable {

    private static final long serialVersionUID = -6360858043234279824L;

    /**
     * 流程规则名称
     */
    @NotBlank(message = "请填写流程规则名称")
    @Size(max = 200, message = "流程规则名称最长200个字符")
    private String name;

    /**
     * 基础流程id
     */
    @NotNull(message = "请选择流程规则")
    @Positive(message = "请选择流程规则")
    private Long baseProcessId;

    /**
     * 流程引擎规则
     */
    @Valid
    private List<ProcessEngineRuleReq> engineRuleList;
}
