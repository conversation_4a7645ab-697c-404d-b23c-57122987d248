package com.ssy.lingxi.member.model.req.validate;

import lombok.Data;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.List;

/**
 * 会员流程规则配置 - 新增流程规则接口参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-15
 */
@Data
public class MemberProcessAddReq implements Serializable {
    private static final long serialVersionUID = -1607608917878381881L;

    /**
     * 流程规则名称
     */
    @NotBlank(message = "流程规则名称不能为空")
    @Size(max = 48, message = "流程规则名称最长48个字符")
    private String ruleName;

    /**
     * 角色Id
     */
    @NotNull(message = "角色Id要大于0")
    @Positive(message = "角色Id要大于0")
    private Long roleId;

    /**
     * 会员入库流程Id，如果大于0则新增
     */
    @NotNull(message = "会员入库流程Id要大于等于0")
    @PositiveOrZero(message = "会员入库流程Id要大于等于0")
    private Long depositoryProcessId;

    /**
     * 资料变更流程Id，如果大于0则新增
     */
    @NotNull(message = "资料变更流程Id要大于等于0")
    @PositiveOrZero(message = "资料变更流程Id要大于等于0")
    private Long changedProcessId;

    /**
     * 会员注册资料Id列表，空或Null-表示不选
     */
    private List<Long> configIds;
}
