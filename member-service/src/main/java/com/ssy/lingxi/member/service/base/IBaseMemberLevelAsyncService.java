package com.ssy.lingxi.member.service.base;

import java.math.BigDecimal;

/**
 * 会员等级相关计算异步接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-11-17
 */
public interface IBaseMemberLevelAsyncService {

    /**
     * 用户登录后，计算会员等级积分
     * @param relId           平台会员关系id
     * @param loginSourceEnum 登录来源
     */
    void calculateMemberLoginScore(Long relId, Integer loginSourceEnum);

    /**
     * 交易订单完成后，计算买方（下级会员）的等级积分
     * @param upperMemberId 上级会员Id（卖方）
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId 下级会员Id（买方）
     * @param subRoleId 下级会员角色Id
     * @param amount 订单金额
     * @param orderNo 订单号
     */
    void calculateSubMemberTradeScore(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, BigDecimal amount, String orderNo);

    /**
     * 交易评价完成，计算评论方（下级会员）的等级积分
     * @param upperMemberId 上级会员Id（被评论方）
     * @param upperRoleId 上级会员角色Id
     * @param subMemberId 下级会员Id（评论方）
     * @param subRoleId 下级会员角色Id
     * @param orderNo 订单号
     */
    void calculateSubMemberCommentScore(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, String orderNo);
}
