package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.dataauth.annotation.member.MemberAuth;
import com.ssy.lingxi.member.model.req.validate.MemberModifyPageDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberModifyReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdsReq;
import com.ssy.lingxi.member.model.resp.validate.MemberModifyDetailResp;
import com.ssy.lingxi.member.model.resp.validate.MemberModifyPageQueryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberModifySearchConditionResp;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilityModifyService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员能力 - 会员变更审核相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-27
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/modify")
public class MemberAbilityModifyController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IMemberAbilityModifyService memberAbilityModifyService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    /**
     * 获取会员变更各个步骤分页查询列表页面下拉框
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/page/conditions")
    public WrapperResp<MemberModifySearchConditionResp> getModifyPageConditions(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityModifyService.getModifyPageConditions(loginUser, roleTag));
    }


    /**
     * 分页查询“待审核变更（一级）”会员列表
     * @param headers Http头部新
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/grade/one/page")
    public WrapperResp<PageDataResp<MemberModifyPageQueryResp>> pageToModifyGradeOne(@RequestHeader HttpHeaders headers, @Valid MemberModifyPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityModifyService.pageToModifyGradeOne(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核变更（一级）” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/grade/one/detail")
    public WrapperResp<MemberModifyDetailResp> getToModifyGradeOne(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityModifyService.getToModifyGradeOne(loginUser, idVO, roleTag));
    }

    /**
     * “待审核变更（一级）” - 审核会员
     * @param headers Http头部信息
     * @param modifyVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/grade/one")
    public WrapperResp<Void> toModifyGradeOne(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberModifyReq modifyVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberAbilityModifyService.toModifyGradeOne(loginUser, modifyVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待审核变更（一级）” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/grade/one/batch")
    public WrapperResp<Void> batchToModifyGradeOne(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateIdsReq idVO) {
         memberAbilityModifyService.batchToModifyGradeOne(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 分页查询“待审核变更（二级）”会员列表
     * @param headers Http头部新
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/grade/two/page")
    public WrapperResp<PageDataResp<MemberModifyPageQueryResp>> pageToModifyGradeTwo(@RequestHeader HttpHeaders headers, @Valid MemberModifyPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityModifyService.pageToModifyGradeTwo(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核变更（二级）” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/grade/two/detail")
    public WrapperResp<MemberModifyDetailResp> getToModifyGradeTwo(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityModifyService.getToModifyGradeTwo(loginUser, idVO, roleTag));
    }

    /**
     * “待审核变更（二级）” - 审核会员
     * @param headers Http头部信息
     * @param modifyVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/grade/two")
    public WrapperResp<Void> toModifyGradeTwo(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberModifyReq modifyVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberAbilityModifyService.toModifyGradeTwo(loginUser, modifyVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待审核变更（二级）” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/grade/two/batch")
    public WrapperResp<Void> batchToModifyGradeTwo(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateIdsReq idVO) {
         memberAbilityModifyService.batchToModifyGradeTwo(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 分页查询“待确认会员变更”会员列表
     * @param headers Http头部新
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/confirm/page")
    public WrapperResp<PageDataResp<MemberModifyPageQueryResp>> pageToConfirmModify(@RequestHeader HttpHeaders headers, @Valid MemberModifyPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityModifyService.pageToConfirmModify(loginUser, pageVO, roleTag));
    }

    /**
     * “待确认会员变更” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/confirm/detail")
    public WrapperResp<MemberModifyDetailResp> getToConfirmModify(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityModifyService.getToConfirmModify(loginUser, idVO, roleTag));
    }

    /**
     * “待确认会员变更” - 审核会员
     * @param headers Http头部信息
     * @param modifyVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/confirm")
    public WrapperResp<Void> toConfirmModify(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberModifyReq modifyVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
         memberAbilityModifyService.toConfirmModify(loginUser, modifyVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待确认会员变更” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/confirm/batch")
    public WrapperResp<Void> batchToConfirmModify(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateIdsReq idVO) {
         memberAbilityModifyService.batchToConfirmModify(headers, idVO, roleTag);
        return WrapperUtil.success();
    }
}
