package com.ssy.lingxi.member.serviceImpl.base;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberProcessTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.AopProxyUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.constant.MemberRegisterDetailConfigConstant;
import com.ssy.lingxi.member.entity.bo.*;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.detail.*;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.resp.basic.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberDepositDetailService;
import com.ssy.lingxi.member.service.base.IBaseMemberHistoryService;
import com.ssy.lingxi.member.service.base.IBaseMemberRegisterDetailService;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberProcessRuleService;
import com.ssy.lingxi.member.util.RgConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 会员入库资料基础服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-20
 */
@Slf4j
@Service
public class BaseMemberDepositDetailServiceImpl implements IBaseMemberDepositDetailService {
    @Resource
    private MemberProcessRuleRepository memberProcessRuleRepository;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private MemberDepositoryDetailRepository memberDepositoryDetailRepository;

    @Resource
    private MemberDepositoryDetailHistoryRepository memberDepositoryDetailHistoryRepository;

    @Resource
    private MemberDepositoryDetailListHistoryRepository memberDepositoryDetailListHistoryRepository;

    @Resource
    private IMemberProcessRuleService memberProcessRuleService;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IMessageFeignService messageFeignService;

    @Resource
    private MemberDepositoryDetailSelectRepository memberDepositoryDetailSelectRepository;

    @Resource
    private MemberRegisterConfigRepository memberRegisterConfigRepository;

    @Resource
    private MemberRegisterDetailRepository memberRegisterDetailRepository;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    @Resource
    private CorporationRepository corporationRepository;

    /**
     * 查询会员入库资料配置列表
     *
     * @param memberId 会员Id
     * @param roleId   角色Id
     * @param subRole  下级会员角色
     * @return 查询结果
     */
    @Override
    public List<MemberRegisterConfigDO> findMemberDepositoryConfig(Long memberId, Long roleId, MemberRoleDO subRole) {
        MemberProcessRuleDO processRule = memberProcessRuleRepository.findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(memberId, roleId, subRole, EnableDisableStatusEnum.ENABLE.getCode());
        return processRule == null || CollectionUtils.isEmpty(processRule.getConfigs()) ? new ArrayList<>() : new ArrayList<>(processRule.getConfigs());
    }

    /**
     * 将注册资料转换为分组内容
     *
     * @param memberConfigs 注册资料列表
     * @return 分组内容
     */
    @Override
    public List<RegisterDetailGroupResp> groupMemberConfig(List<MemberRegisterConfigDO> memberConfigs) {
        if(CollectionUtils.isEmpty(memberConfigs)) {
            return new ArrayList<>();
        }

        Map<String, List<MemberRegisterConfigDO>> groupList = memberConfigs.stream().filter(c -> c.getFieldStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(RgConfigUtil::getFieldGroupName))
                .entrySet().stream()
                .sorted(Comparator.comparingInt(
                        entry -> entry.getValue().get(0).getFieldOrder()
                )).collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));

        return groupList.entrySet().stream().map(e -> {
            RegisterDetailGroupResp detailGroup = new RegisterDetailGroupResp();
            detailGroup.setGroupName(e.getKey());

            List<RegisterDetailResp> elements = e.getValue().stream().map(c -> {
                RegisterDetailResp detail = new RegisterDetailResp();
                detail.setFieldName(c.getFieldName());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c));
                detail.setFieldType(c.getFieldType());
                detail.setAttr(c.getAttr());
                detail.setFieldEmpty(c.getFieldEmpty());
                detail.setFieldLength(c.getFieldLength());
                detail.setFieldOrder(c.getFieldOrder());
                detail.setFieldRemark(RgConfigUtil.getFieldRemark(c));
                detail.setRuleEnum(c.getRuleEnum());
                detail.setPattern(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getRuleEnum()));
                detail.setMsg(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getRuleEnum()));
                detail.setFieldEnum(CollectionUtils.isEmpty(c.getLabels()) ? new ArrayList<>() : c.getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));

                DetailValueBO valueBO = getDetailValue(c);
                detail.setFieldValue(valueBO.getFieldValue());
                detail.setLastValue(valueBO.getLastValue());
//                detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getNameByCode().equals(c.getFieldType()) ? initDepositoryDetail(c.getId()) : new ArrayList<>());
                detail.setConfigs(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initMemberConfig(c.getId()) : new ArrayList<>());

                return detail;
            }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 查询该字段包含的子字段
     * @param id 父id
     * @return 子列表集
     */
    @Override
    public List<RegisterDetailResp> initMemberConfig(Long id) {
        List<MemberRegisterConfigDO> configs = memberRegisterConfigRepository.findAllByParentId(id);
        return configs.stream().filter(config -> config.getFieldStatus().equals(MemberConfigStatusEnum.ENABLED.getCode())).map(c -> {
            RegisterDetailResp detail = new RegisterDetailResp();
            detail.setFieldName(c.getFieldName());
            detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c));
            detail.setFieldType(c.getFieldType());
            detail.setAttr(c.getAttr());
            detail.setFieldEmpty(c.getFieldEmpty());
            detail.setFieldLength(c.getFieldLength());
            detail.setFieldOrder(c.getFieldOrder());
            detail.setFieldRemark(RgConfigUtil.getFieldRemark(c));
            detail.setRuleEnum(c.getRuleEnum());
            detail.setPattern(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getRuleEnum()));
            detail.setMsg(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getRuleEnum()));
            detail.setFieldEnum(CollectionUtils.isEmpty(c.getLabels()) ? new ArrayList<>() : c.getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));

            DetailValueBO valueBO = getDetailValue(c);
            detail.setFieldValue(valueBO.getFieldValue());
            detail.setLastValue(valueBO.getLastValue());
//            detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getNameByCode().equals(c.getFieldType()) ? initDepositoryDetail(c.getId()) : new ArrayList<>());
            detail.setConfigs(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initMemberConfig(c.getId()) : new ArrayList<>());

            return detail;
        }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());
    }

    /**
     * 入库审核过程中，新增或修改入库资料时，分组展示“待审核”的入库资料
     *
     * @param relationDO 会员关系
     * @return 分组内容
     */
    @Override
    public List<RegisterDetailGroupResp> groupMemberDepositoryDetail(MemberRelationDO relationDO) {
        List<MemberDepositoryDetailDO> depositoryDetails = memberDepositoryDetailRepository.findByRelationAndVersion(relationDO, MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());

        if(!CollectionUtils.isEmpty(depositoryDetails)) {
            Map<String, List<MemberDepositoryDetailDO>> groupList = depositoryDetails.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                    .collect(Collectors.groupingBy(MemberDepositoryDetailDO::getGroupName));

            return groupList.entrySet().stream().map(e -> {
                RegisterDetailGroupResp detailGroup = new RegisterDetailGroupResp();
                detailGroup.setGroupName(e.getKey());

                List<RegisterDetailResp> elements = e.getValue().stream().map(c -> {
                    RegisterDetailResp detail = new RegisterDetailResp();
                    detail.setFieldName(c.getFieldName());
                    detail.setFieldLocalName(c.getFieldLocalName());
                    detail.setFieldType(c.getFieldType());
                    detail.setAttr(c.getAttr());
                    detail.setFieldEmpty(c.getMemberConfig().getFieldEmpty());
                    detail.setFieldLength(c.getMemberConfig().getFieldLength());
                    detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                    detail.setFieldRemark(RgConfigUtil.getFieldRemark(c.getMemberConfig()));
                    detail.setRuleEnum(c.getMemberConfig().getRuleEnum());
                    detail.setPattern(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getMemberConfig().getRuleEnum()));
                    detail.setMsg(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getMemberConfig().getRuleEnum()));
                    detail.setFieldEnum(CollectionUtils.isEmpty(c.getMemberConfig().getLabels()) ? new ArrayList<>() : c.getMemberConfig().getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));

                    DetailValueBO valueBO = getDetailValue(c);
                    detail.setFieldValue(valueBO.getFieldValue());
                    detail.setLastValue(valueBO.getLastValue());
                    detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepositoryDetail(c.getId()) : new ArrayList<>());
                    detail.setConfigs(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initMemberConfig(c.getMemberConfigId()) : new ArrayList<>());
                    return detail;
                }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());

                detailGroup.setElements(elements);
                return detailGroup;
            }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
        }

        return groupMemberConfig(findMemberDepositoryConfig(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRole()));
    }

    /**
     * 查询该字段包含的子字段
     * @param id 父id
     * @return 子列表集
     */
    @Override
    public List<List<RegisterDetailResp>> initDepositoryDetail(Long id) {
        List<MemberDepositoryDetailDO> depositoryDetailDOS = memberDepositoryDetailRepository.findAllByParentId(id);
        Map<Integer, List<MemberDepositoryDetailDO>> groupList = depositoryDetailDOS.stream().filter(detail -> MemberConfigStatusEnum.ENABLED.getCode().equals(detail.getStatus()))
                .collect(Collectors.groupingBy(MemberDepositoryDetailDO::getGroupIndex));
        List<List<RegisterDetailResp>> result = new ArrayList<>();
        groupList.forEach((key, value) -> {
            List<RegisterDetailResp> collect = value.stream().map(c -> {
                RegisterDetailResp detail = new RegisterDetailResp();
                detail.setFieldName(c.getFieldName());
                detail.setFieldLocalName(c.getFieldLocalName());
                detail.setFieldType(c.getFieldType());
                detail.setAttr(c.getAttr());
                detail.setFieldEmpty(c.getMemberConfig().getFieldEmpty());
                detail.setFieldLength(c.getMemberConfig().getFieldLength());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldRemark(RgConfigUtil.getFieldRemark(c.getMemberConfig()));
                detail.setRuleEnum(c.getMemberConfig().getRuleEnum());
                detail.setPattern(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getMemberConfig().getRuleEnum()));
                detail.setMsg(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getMemberConfig().getRuleEnum()));
                detail.setFieldEnum(CollectionUtils.isEmpty(c.getMemberConfig().getLabels()) ? new ArrayList<>() : c.getMemberConfig().getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));

                DetailValueBO valueBO = getDetailValue(c);
                detail.setFieldValue(valueBO.getFieldValue());
                detail.setLastValue(valueBO.getLastValue());
                detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepositoryDetail(c.getId()) : new ArrayList<>());
                detail.setConfigs(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initMemberConfig(c.getMemberConfigId()) : new ArrayList<>());
                return detail;
            }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());
            result.add(key - 1, collect);
        });
        return result;
    }

    /**
     * 更新入库资料，保存入库，判断是否启动会员变更流程
     *
     * @param relationDO 会员关系
     * @param detailMap  修改后的入库资料
     * @param roleTag 角色标签
     * @return 是否需要重新入库审核
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public WrapperResp<Void> checkAndUpdateMemberDepositoryDetail(MemberRelationDO relationDO, Map<String, Object> detailMap, Integer roleTag) {
        switch (MemberOuterStatusEnum.parseCode(relationDO.getOuterStatus())) {
            //入库审核、变更审核通过后，比对正在使用的版本，启动变更流程
            case DEPOSITORY_PASSED:
            case MODIFY_PASSED:
                return updateValidatePassedDetails(relationDO, detailMap, roleTag);
            //入库审核不通过，比对正在使用的版本，强制重启入库流程
            case DEPOSITORY_NOT_PASSED:
                return updateDepositNotPassedDetails(relationDO, detailMap);
            //变更审核不通过，比对审核中的版本，强制重启变更流程
            case MODIFY_NOT_PASSED:
                return updateModifyNotPassedDetails(relationDO, detailMap);
            default:
                return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }
    }

    /**
     * 入库审核、变更审核通过后，修改入库资料，比对“正在使用”的版本，判断是否需要启动变更流程
     * @param relationDO 会员关系
     * @param detailMap 入库资料
     * @param roleTag
     * @return 操作结果
     */
    private WrapperResp<Void> updateValidatePassedDetails(MemberRelationDO relationDO, Map<String, Object> detailMap, Integer roleTag) {
        List<MemberDepositoryDetailDO> existList = Optional.of(memberDepositoryDetailRepository.findByRelation(relationDO)).orElse(new ArrayList<>());
        //记录执行之前的内部状态
        int lastInnerStatus = relationDO.getInnerStatus();
        //入库审核、变更审核通过后，入库资料只会存在“正在使用”的版本
        List<MemberDepositoryDetailDO> usingList = existList.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        // 需求变更，以实际查出来的配置资料为主
//        if(CollectionUtils.isEmpty(usingList)) {
//            return Wrapper.fail(ResponseCode.MC_MS_MEMBER_DEPOSITORY_DETAIL_NOT_EXIST);
//        }

        //判断入库资料字段是否齐全
//        Wrapper<Void> checkFieldResult = checkDetailFields(usingList, detailMap);
//        if(checkFieldResult.getCode() != ResponseCode.SUCCESS.getCode()) {
//            return Wrapper.fail(checkFieldResult.getCode(), checkFieldResult.getNameByCode());
//        }

        // 会员入库资料
        List<MemberRegisterConfigDO> memberConfigList = findMemberDepositoryConfig(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRole());

        if(!CollectionUtils.isEmpty(memberConfigList) && CollectionUtils.isEmpty(detailMap)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_DETAIL_IS_MISSING);
        }

        List<MemberDepositoryDetailDO> depositoryDetails = new ArrayList<>();
        //资料是否变更
        boolean detailChanged = false;
        //判断是否需要重新审核
        boolean needValidate = false;
        for (Map.Entry<String, Object> entry : detailMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() instanceof HashMap || entry.getValue() instanceof ArrayList ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());

            MemberDepositoryDetailDO usingDetailDO = usingList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
//            if (usingDetailDO == null) {
//                return Wrapper.fail(ResponseCode.MC_MS_MEMBER_DEPOSITORY_DETAIL_NOT_EXIST);
//            }

//            MemberConfigDO configDO = usingDetailDO.getMemberConfig();
            MemberRegisterConfigDO configDO = memberConfigList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
            if(configDO == null) {
                return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_CONFIG_DOES_NOT_EXIST);
            }

            DetailCheckBO checkResult = baseMemberRegisterDetailService.checkDetail(configDO, value);

            //判断是否需要重新审核
            if(usingDetailDO != null && !usingDetailDO.getDetail().equals(checkResult.getDetail())) {
                detailChanged = true;
                needValidate = needValidate || NumberUtil.notNullOrZero(configDO.getValidate());
            }

            // 已存在的入库资料不包含该字段
            if (usingDetailDO == null && StringUtils.hasLength(checkResult.getDetail())) {
                detailChanged = true;
                needValidate = needValidate || NumberUtil.notNullOrZero(configDO.getValidate());
            }

            MemberDepositoryDetailDO memberDepositoryDetailDO = new MemberDepositoryDetailDO();
            memberDepositoryDetailDO.setCreateTime(LocalDateTime.now());
            memberDepositoryDetailDO.setAllowSelect(configDO.getAllowSelect());
            memberDepositoryDetailDO.setMemberConfig(configDO);
            memberDepositoryDetailDO.setGroupName(RgConfigUtil.getFieldGroupName(configDO));
            memberDepositoryDetailDO.setRelation(relationDO);
            memberDepositoryDetailDO.setFieldName(configDO.getFieldName());
            memberDepositoryDetailDO.setFieldLocalName(RgConfigUtil.getFieldLocalName(configDO));
            memberDepositoryDetailDO.setFieldType(configDO.getFieldType());
            memberDepositoryDetailDO.setAttr(configDO.getAttr());
            memberDepositoryDetailDO.setTagEnum(configDO.getTagEnum());
            memberDepositoryDetailDO.setStatus(configDO.getFieldStatus());
            memberDepositoryDetailDO.setValidate(configDO.getValidate());
            memberDepositoryDetailDO.setDetail(MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) ? "" : checkResult.getDetail());
            memberDepositoryDetailDO.setProvinceCode(checkResult.getProvinceCode());
            memberDepositoryDetailDO.setProvinceName(checkResult.getProvinceName());
            memberDepositoryDetailDO.setCityCode(checkResult.getCityCode());
            memberDepositoryDetailDO.setCityName(checkResult.getCityName());
            memberDepositoryDetailDO.setDistrictCode(checkResult.getDistrictCode());
            memberDepositoryDetailDO.setDistrictName(checkResult.getDistrictName());
            memberDepositoryDetailDO.setLabels(checkResult.getLabels());
            // 是列表字段
            if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) && StringUtils.hasLength(checkResult.getDetail())) {
                List<MemberRegisterConfigDO> memberConfigs = memberRegisterConfigRepository.findAllByParentId(configDO.getId());
                List<Map<String, Object>> details = JSONObject.parseObject(checkResult.getDetail(), new TypeReference<List<Map<String, Object>>>(){});
               List<MemberDepositoryDetailDO> registerResult = foreachDepositorys(details, memberConfigs);

                memberDepositoryDetailDO.setDepositorys(registerResult);
            }
            depositoryDetails.add(memberDepositoryDetailDO);
        }

        //资料没有发生变更，返回
        if(!detailChanged) {
            return WrapperUtil.success();
        }

        //如果需要重新审核，新的资料为“正在审核版本”，启动变更流程
        //如果不需要重新审核，新的资料为“正在使用”的版本，直接更改
        //查询变更流程
        ProcessBO modifyProcess = memberProcessRuleService.findMemberProcessKey(relationDO, MemberProcessTypeEnum.MEMBER_MODIFICATION);
        if(needValidate && !modifyProcess.getEmptyProcess()) {
            WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(modifyProcess.getProcessKey(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId());

            relationDO.setInnerStatus(taskResult.getInnerStatus());
            relationDO.setOuterStatus(MemberOuterStatusEnum.MODIFYING.getCode());
            relationDO.getValidateTask().setTaskId(taskResult.getTaskId());
            relationDO.getValidateTask().setProcessKey(modifyProcess.getProcessKey());
            relationDO.getValidateTask().setProcessTypeEnum(MemberProcessTypeEnum.MEMBER_MODIFICATION.getCode());

            //如果之前有“审核中”的版本，删除
            List<MemberDepositoryDetailDO> deleteList = existList.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(deleteList)) {
                relationDO.getDepositDetails().removeIf(detail -> detail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode()));
                // 筛选出列表字段
                List<Long> listIds = deleteList.stream().filter(detail -> MemberConfigFieldTypeEnum.LIST.getMessage().equals(detail.getFieldType())).map(MemberDepositoryDetailDO::getId).collect(Collectors.toList());
                listIds.forEach(id -> memberDepositoryDetailRepository.deleteByParentId(id));
                memberDepositoryDetailRepository.deleteAll(deleteList);
            }

            //保存修改后的入库资料
            depositoryDetails.forEach(detail -> detail.setVersion(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode()));
            memberDepositoryDetailRepository.saveAll(depositoryDetails);
            // 兼容list字段，修改成循环保存
            for (MemberDepositoryDetailDO depositoryDetail : depositoryDetails) {
                if (!CollectionUtils.isEmpty(depositoryDetail.getDepositorys())) {
                    List<MemberDepositoryDetailDO> depositorys = depositoryDetail.getDepositorys();
                    depositorys.forEach(depository -> depository.setParentId(depositoryDetail.getId()));
                    memberDepositoryDetailRepository.saveAll(depositorys);
                }
            }
            //添加
            relationDO.getDepositDetails().addAll(depositoryDetails);

            //发送消息
            // 如果roleTag有传则使用(APP端)，否则使用上下级关系的下级角色标签
            messageFeignService.sendMemberValidateMessage(relationDO, roleTag == null ? Optional.ofNullable(relationDO.getSubRoleTag()).orElse(RoleTagEnum.MEMBER.getCode()) : roleTag);
            //记录外部记录
            baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getSubRoleName(), MemberValidateHistoryOperationEnum.MODIFY_DEPOSIT_DETAIL, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");
        } else {
            List<Long> listIds = relationDO.getDepositDetails().stream().filter(detail -> MemberConfigFieldTypeEnum.LIST.getMessage().equals(detail.getFieldType())).map(MemberDepositoryDetailDO::getId).collect(Collectors.toList());
            relationDO.getDepositDetails().clear();
            //删除之前所有的
            listIds.forEach(id -> memberDepositoryDetailRepository.deleteByParentId(id));
            memberDepositoryDetailRepository.deleteByRelation(relationDO);

            depositoryDetails.forEach(detail -> detail.setVersion(MemberDetailVersionEnum.USING.getCode()));
            memberDepositoryDetailRepository.saveAll(depositoryDetails);
            // 兼容list字段，修改成循环保存
            for (MemberDepositoryDetailDO depositoryDetail : depositoryDetails) {
                if (!CollectionUtils.isEmpty(depositoryDetail.getDepositorys())) {
                    List<MemberDepositoryDetailDO> depositorys = depositoryDetail.getDepositorys();
                    depositorys.forEach(depository -> depository.setParentId(depositoryDetail.getId()));
                    memberDepositoryDetailRepository.saveAll(depositorys);
                }
            }
            relationDO.setDepositDetails(new HashSet<>(depositoryDetails));
        }

        relationRepository.saveAndFlush(relationDO);

        return WrapperUtil.success();
    }

    /**
     * 入库审核不通过，修改入库资料，比对“审核中”的版本，强制重启入库流程
     * @param relationDO 会员关系
     * @param detailMap 入库资料
     * @return 操作结果
     */
    private WrapperResp<Void> updateDepositNotPassedDetails(MemberRelationDO relationDO, Map<String, Object> detailMap) {
        //入库审核不通过后，入库资料必须存在“审核中”的版本，不存在“正在使用”的版本
        //即使没有入库资料，也要强制重启流程
        List<MemberDepositoryDetailDO> usingList = memberDepositoryDetailRepository.findByRelationAndVersion(relationDO, MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());

        // 需求变更，以实际查出来的配置资料为主
        //判断入库资料字段是否齐全
//        Wrapper<Void> checkFieldResult = checkDetailFields(usingList, detailMap);
//        if(checkFieldResult.getCode() != ResponseCode.SUCCESS.getCode()) {
//            return Wrapper.fail(checkFieldResult.getCode(), checkFieldResult.getNameByCode());
//        }

        // 会员入库资料
        List<MemberRegisterConfigDO> memberConfigList = findMemberDepositoryConfig(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRole());

        if(!CollectionUtils.isEmpty(memberConfigList) && CollectionUtils.isEmpty(detailMap)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_DETAIL_IS_MISSING);
        }

        List<MemberDepositoryDetailDO> depositoryDetails = new ArrayList<>();
        for (Map.Entry<String, Object> entry : detailMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() instanceof HashMap || entry.getValue() instanceof ArrayList ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());

//            MemberDepositoryDetailDO usingDetailDO = usingList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
//            if (usingDetailDO == null) {
//                return Wrapper.fail(ResponseCode.MC_MS_MEMBER_DEPOSITORY_DETAIL_NOT_EXIST);
//            }

//            MemberConfigDO configDO = usingDetailDO.getMemberConfig();
            MemberRegisterConfigDO configDO = memberConfigList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
            if(configDO == null) {
                return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_CONFIG_DOES_NOT_EXIST);
            }

            DetailCheckBO checkResult = baseMemberRegisterDetailService.checkDetail(configDO, value);

            MemberDepositoryDetailDO memberDepositoryDetailDO = new MemberDepositoryDetailDO();
            memberDepositoryDetailDO.setAllowSelect(configDO.getAllowSelect());
            memberDepositoryDetailDO.setCreateTime(LocalDateTime.now());
            memberDepositoryDetailDO.setMemberConfig(configDO);
            memberDepositoryDetailDO.setGroupName(RgConfigUtil.getFieldGroupName(configDO));
            memberDepositoryDetailDO.setRelation(relationDO);
            memberDepositoryDetailDO.setFieldName(configDO.getFieldName());
            memberDepositoryDetailDO.setFieldLocalName(RgConfigUtil.getFieldLocalName(configDO));
            memberDepositoryDetailDO.setFieldType(configDO.getFieldType());
            memberDepositoryDetailDO.setAttr(configDO.getAttr());
            memberDepositoryDetailDO.setTagEnum(configDO.getTagEnum());
            memberDepositoryDetailDO.setStatus(configDO.getFieldStatus());
            memberDepositoryDetailDO.setValidate(configDO.getValidate());
            memberDepositoryDetailDO.setDetail(MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) ? "" : checkResult.getDetail());
            memberDepositoryDetailDO.setProvinceCode(checkResult.getProvinceCode());
            memberDepositoryDetailDO.setProvinceName(checkResult.getProvinceName());
            memberDepositoryDetailDO.setCityCode(checkResult.getCityCode());
            memberDepositoryDetailDO.setCityName(checkResult.getCityName());
            memberDepositoryDetailDO.setDistrictCode(checkResult.getDistrictCode());
            memberDepositoryDetailDO.setDistrictName(checkResult.getDistrictName());
            memberDepositoryDetailDO.setLabels(checkResult.getLabels());
            //版本
            memberDepositoryDetailDO.setVersion(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());
            // 是列表字段
            if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) && StringUtils.hasLength(checkResult.getDetail())) {
                List<MemberRegisterConfigDO> memberConfigs = memberRegisterConfigRepository.findAllByParentId(configDO.getId());
                List<Map<String, Object>> details = JSONObject.parseObject(checkResult.getDetail(), new TypeReference<List<Map<String, Object>>>(){});
                List<MemberDepositoryDetailDO> registerResult = foreachDepositorys(details, memberConfigs);

                memberDepositoryDetailDO.setDepositorys(registerResult);
            }
            depositoryDetails.add(memberDepositoryDetailDO);
        }

        //强制重启入库流程
        ProcessBO depositProcess = memberProcessRuleService.findMemberProcessKey(relationDO, MemberProcessTypeEnum.MEMBER_DEPOSITORY);
        if(depositProcess.getEmptyProcess()) {
            //删除之前所有版本，将修改后的版本设置为“正在使用的版本”
            List<Long> listIds = relationDO.getDepositDetails().stream().filter(detail -> MemberConfigFieldTypeEnum.LIST.getMessage().equals(detail.getFieldType())).map(MemberDepositoryDetailDO::getId).collect(Collectors.toList());
            relationDO.getDepositDetails().clear();
            listIds.forEach(id -> memberDepositoryDetailRepository.deleteByParentId(id));
            memberDepositoryDetailRepository.deleteByRelation(relationDO);
            depositoryDetails.forEach(detail -> detail.setVersion(MemberDetailVersionEnum.USING.getCode()));
        } else {
            if (!StringUtils.hasLength(relationDO.getValidateTask().getProcessKey()) || !relationDO.getValidateTask().getProcessTypeEnum().equals(MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode())) {
                relationDO.getValidateTask().setProcessTypeEnum(MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode());
                relationDO.getValidateTask().setProcessKey(depositProcess.getProcessKey());
            }

            WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(relationDO.getValidateTask().getProcessKey(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId());

            relationDO.setInnerStatus(taskResult.getInnerStatus());
            relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITING.getCode());
            relationDO.getValidateTask().setTaskId(taskResult.getTaskId());

            //如果之前有“审核中”的版本，删除
            List<Long> listIds = relationDO.getDepositDetails().stream().filter(detail -> MemberDetailVersionEnum.TO_BE_VALIDATE.getCode().equals(detail.getVersion())
                    && MemberConfigFieldTypeEnum.LIST.getMessage().equals(detail.getFieldType())).map(MemberDepositoryDetailDO::getId).collect(Collectors.toList());
            relationDO.getDepositDetails().removeIf(detail -> detail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode()));
            listIds.forEach(id -> memberDepositoryDetailRepository.deleteByParentId(id));
            memberDepositoryDetailRepository.deleteByRelationAndVersion(relationDO, MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());
        }

        memberDepositoryDetailRepository.saveAll(depositoryDetails);
        // 兼容list字段，修改成循环保存
        for (MemberDepositoryDetailDO depositoryDetail : depositoryDetails) {
//            memberDepositoryDetailRepository.save(depositoryDetail);
            if (!CollectionUtils.isEmpty(depositoryDetail.getDepositorys())) {
                List<MemberDepositoryDetailDO> depositorys = depositoryDetail.getDepositorys();
                depositorys.forEach(depository -> depository.setParentId(depositoryDetail.getId()));
                memberDepositoryDetailRepository.saveAll(depositorys);
            }
        }
        relationDO.getDepositDetails().addAll(depositoryDetails);

        relationRepository.saveAndFlush(relationDO);

        //外部记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getSubRoleName(), MemberValidateHistoryOperationEnum.APPLY_FOR_SUB_MEMBER, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");

        return WrapperUtil.success();
    }

    /**
     * 变更审核不通过，修改入库资料，比对“审核中”的版本，强制重启变更流程
     * @param relationDO 会员关系
     * @param detailMap 入库资料
     * @return 操作结果
     */
    private WrapperResp<Void> updateModifyNotPassedDetails(MemberRelationDO relationDO, Map<String, Object> detailMap) {
        //变更审核不通过后，入库资料必须存在“审核中”的版本，必须存在“正在使用”的版本
        List<MemberDepositoryDetailDO> usingList = memberDepositoryDetailRepository.findByRelationAndVersion(relationDO, MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());
        // 需求变更，以实际查出来的配置资料为主
//        if(CollectionUtils.isEmpty(usingList)) {
//            return Wrapper.fail(ResponseCode.MC_MS_MEMBER_DEPOSITORY_DETAIL_NOT_EXIST);
//        }

        //判断入库资料字段是否齐全
//        Wrapper<Void> checkFieldResult = checkDetailFields(usingList, detailMap);
//        if(checkFieldResult.getCode() != ResponseCode.SUCCESS.getCode()) {
//            return Wrapper.fail(checkFieldResult.getCode(), checkFieldResult.getNameByCode());
//        }

        // 会员入库资料
        List<MemberRegisterConfigDO> memberConfigList = findMemberDepositoryConfig(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRole());

        if(!CollectionUtils.isEmpty(memberConfigList) && CollectionUtils.isEmpty(detailMap)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_DETAIL_IS_MISSING);
        }

        List<MemberDepositoryDetailDO> depositoryDetails = new ArrayList<>();
        for (Map.Entry<String, Object> entry : detailMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() instanceof HashMap || entry.getValue() instanceof ArrayList ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());

//            MemberDepositoryDetailDO usingDetailDO = usingList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
//            if (usingDetailDO == null) {
//                return Wrapper.fail(ResponseCode.MC_MS_MEMBER_DEPOSITORY_DETAIL_NOT_EXIST);
//            }

//            MemberConfigDO configDO = usingDetailDO.getMemberConfig();
            MemberRegisterConfigDO configDO = memberConfigList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
            if(configDO == null) {
                return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_CONFIG_DOES_NOT_EXIST);
            }

            DetailCheckBO checkResult = baseMemberRegisterDetailService.checkDetail(configDO, value);

            MemberDepositoryDetailDO memberDepositoryDetailDO = new MemberDepositoryDetailDO();
            memberDepositoryDetailDO.setAllowSelect(configDO.getAllowSelect());
            memberDepositoryDetailDO.setCreateTime(LocalDateTime.now());
            memberDepositoryDetailDO.setMemberConfig(configDO);
            memberDepositoryDetailDO.setGroupName(RgConfigUtil.getFieldGroupName(configDO));
            memberDepositoryDetailDO.setRelation(relationDO);
            memberDepositoryDetailDO.setFieldName(configDO.getFieldName());
            memberDepositoryDetailDO.setFieldLocalName(RgConfigUtil.getFieldLocalName(configDO));
            memberDepositoryDetailDO.setFieldType(configDO.getFieldType());
            memberDepositoryDetailDO.setAttr(configDO.getAttr());
            memberDepositoryDetailDO.setTagEnum(configDO.getTagEnum());
            memberDepositoryDetailDO.setStatus(configDO.getFieldStatus());
            memberDepositoryDetailDO.setValidate(configDO.getValidate());
            memberDepositoryDetailDO.setDetail(MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) ? "" : checkResult.getDetail());
            memberDepositoryDetailDO.setProvinceCode(checkResult.getProvinceCode());
            memberDepositoryDetailDO.setProvinceName(checkResult.getProvinceName());
            memberDepositoryDetailDO.setCityCode(checkResult.getCityCode());
            memberDepositoryDetailDO.setCityName(checkResult.getCityName());
            memberDepositoryDetailDO.setDistrictCode(checkResult.getDistrictCode());
            memberDepositoryDetailDO.setDistrictName(checkResult.getDistrictName());
            memberDepositoryDetailDO.setLabels(checkResult.getLabels());
            //版本
            memberDepositoryDetailDO.setVersion(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());
            // 是列表字段
            if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) && StringUtils.hasLength(checkResult.getDetail())) {
                List<MemberRegisterConfigDO> memberConfigs = memberRegisterConfigRepository.findAllByParentId(configDO.getId());
                List<Map<String, Object>> details = JSONObject.parseObject(checkResult.getDetail(), new TypeReference<List<Map<String, Object>>>(){});
                List<MemberDepositoryDetailDO> registerResult = foreachDepositorys(details, memberConfigs);

                memberDepositoryDetailDO.setDepositorys(registerResult);
            }
            depositoryDetails.add(memberDepositoryDetailDO);
        }

        //强制重启变更流程
        ProcessBO modifyProcess = memberProcessRuleService.findMemberProcessKey(relationDO, MemberProcessTypeEnum.MEMBER_MODIFICATION);
        if(modifyProcess.getEmptyProcess()) {
            //删除之前所有版本，将修改后的版本设置为“正在使用的版本”
            List<Long> listIds = relationDO.getDepositDetails().stream().filter(detail -> MemberConfigFieldTypeEnum.LIST.getMessage().equals(detail.getFieldType())).map(MemberDepositoryDetailDO::getId).collect(Collectors.toList());
            relationDO.getDepositDetails().clear();
            listIds.forEach(id -> memberDepositoryDetailRepository.deleteByParentId(id));
            memberDepositoryDetailRepository.deleteByRelation(relationDO);
            depositoryDetails.forEach(detail -> detail.setVersion(MemberDetailVersionEnum.USING.getCode()));

        } else {
            if (!StringUtils.hasLength(relationDO.getValidateTask().getProcessKey()) || !relationDO.getValidateTask().getProcessTypeEnum().equals(MemberProcessTypeEnum.MEMBER_MODIFICATION.getCode())) {
                relationDO.getValidateTask().setProcessTypeEnum(MemberProcessTypeEnum.MEMBER_MODIFICATION.getCode());
                relationDO.getValidateTask().setProcessKey(modifyProcess.getProcessKey());
            }

            WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(relationDO.getValidateTask().getProcessKey(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId());

            relationDO.setInnerStatus(taskResult.getInnerStatus());
            relationDO.setOuterStatus(MemberOuterStatusEnum.MODIFYING.getCode());
            relationDO.getValidateTask().setTaskId(taskResult.getTaskId());

            //如果之前有“审核中”的版本，删除
            List<Long> listIds = relationDO.getDepositDetails().stream().filter(detail -> MemberDetailVersionEnum.TO_BE_VALIDATE.getCode().equals(detail.getVersion())
                    && MemberConfigFieldTypeEnum.LIST.getMessage().equals(detail.getFieldType())).map(MemberDepositoryDetailDO::getId).collect(Collectors.toList());
            relationDO.getDepositDetails().removeIf(detail -> detail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode()));
            listIds.forEach(id -> memberDepositoryDetailRepository.deleteByParentId(id));
            memberDepositoryDetailRepository.deleteByRelationAndVersion(relationDO, MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());
        }
        memberDepositoryDetailRepository.saveAll(depositoryDetails);
        // 兼容list字段，修改成循环保存
        for (MemberDepositoryDetailDO depositoryDetail : depositoryDetails) {
//            memberDepositoryDetailRepository.save(depositoryDetail);
            if (!CollectionUtils.isEmpty(depositoryDetail.getDepositorys())) {
                List<MemberDepositoryDetailDO> depositorys = depositoryDetail.getDepositorys();
                depositorys.forEach(depository -> depository.setParentId(depositoryDetail.getId()));
                memberDepositoryDetailRepository.saveAll(depositorys);
            }
        }
        relationDO.getDepositDetails().addAll(depositoryDetails);

        relationRepository.saveAndFlush(relationDO);

        //外部记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getSubRoleName(), MemberValidateHistoryOperationEnum.MODIFY_DEPOSIT_DETAIL, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");

        return WrapperUtil.success();
    }

    /**
     * 变更审核通过后，比较入库资料版本，记录资料变更历史记录
     *
     * @param relationDO 会员关系
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void saveDepositDetailHistory(MemberRelationDO relationDO) {
        List<MemberDepositoryDetailDO> details = memberDepositoryDetailRepository.findByRelation(relationDO);
        if(CollectionUtils.isEmpty(details)) {
            return;
        }

        List<MemberDepositoryDetailDO> usingList = details.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());

        //只有配置了“变更需要审核”的字段，才需要记录
        List<MemberDepositoryDetailDO> validateList = details.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode()) && NumberUtil.notNullOrZero(detail.getValidate())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(validateList)) {
            return;
        }

        List<MemberDepositoryDetailHistoryDO> historyList = new ArrayList<>();
        for (MemberDepositoryDetailDO validateDetail : validateList) {
            MemberDepositoryDetailDO usingDetail = usingList.stream().filter(c -> c.getFieldName().equals(validateDetail.getFieldName())).findFirst().orElse(null);
            if (usingDetail == null) {
                continue;
            }

            if(!validateDetail.getDetail().equals(usingDetail.getDetail())) {
                MemberDepositoryDetailHistoryDO historyDO = new MemberDepositoryDetailHistoryDO();
                historyDO.setCreateTime(LocalDateTime.now());
                historyDO.setMemberId(relationDO.getMemberId());
                historyDO.setRoleId(relationDO.getRoleId());
                historyDO.setSubMemberId(relationDO.getSubMemberId());
                historyDO.setSubRoleId(relationDO.getSubRoleId());
                historyDO.setFieldLocalName(validateDetail.getFieldLocalName());
                historyDO.setFieldType(validateDetail.getFieldType());
                historyDO.setFieldValue(validateDetail.getDetail());
                historyDO.setLastValue(usingDetail.getDetail());
                if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(validateDetail.getFieldType())) {
                    MemberDepositoryDetailListHistoryDO listHistory = new MemberDepositoryDetailListHistoryDO();
                    listHistory.setFieldValue(SerializeUtil.serialize(initDepository(validateDetail.getId())));
                    listHistory.setLastValue(SerializeUtil.serialize(initDepository(usingDetail.getId())));
                    historyDO.setMemberDepositoryDetailListHistoryDO(memberDepositoryDetailListHistoryRepository.save(listHistory));
                }
                historyList.add(historyDO);
            }
        }

        if(!CollectionUtils.isEmpty(historyList)) {
            memberDepositoryDetailHistoryRepository.saveAll(historyList);
        }
    }

    /**
     * 检查入库资料，并保存。调用方要再保存一次MemberRelationDO
     *
     * @param relationDO 会员关系
     * @param detailMap     新增的会员入库资料
     * @return 检查是否通过
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void checkAndSaveMemberDepositoryDetail(MemberRelationDO relationDO, Map<String, Object> detailMap) {
        //如果已经存在“待审核”的入库资料，用作比对；否则用流程规则配置中角色关联的入库资料做比对
        List<MemberDepositoryDetailDO> validatingDetails = memberDepositoryDetailRepository.findByRelationAndVersion(relationDO, MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());
        List<MemberRegisterConfigDO> memberConfigList;
        if(CollectionUtils.isEmpty(validatingDetails)) {
            memberConfigList = findMemberDepositoryConfig(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRole());
        } else {
            memberConfigList = validatingDetails.stream().map(MemberDepositoryDetailDO::getMemberConfig).collect(Collectors.toList());
        }

        //检查并生成“待审核”的入库资料
        List<MemberDepositoryDetailDO> depositoryDetails = checkMemberDepositoryDetail(relationDO, memberConfigList, detailMap, MemberDetailVersionEnum.TO_BE_VALIDATE);

        //删除已经存在的“待审核”的入库资料
        if(!CollectionUtils.isEmpty(validatingDetails)) {
            relationDO.getDepositDetails().removeIf(detail -> detail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode()));
//            memberDepositoryDetailRepository.deleteAll(validatingDetails);
            deleteDepositDetails(validatingDetails);
        }
        memberDepositoryDetailRepository.saveAll(depositoryDetails);
        // 兼容list字段，修改成循环保存
        for (MemberDepositoryDetailDO depositoryDetail : depositoryDetails) {
//            memberDepositoryDetailRepository.save(depositoryDetail);
            if (!CollectionUtils.isEmpty(depositoryDetail.getDepositorys())) {
                List<MemberDepositoryDetailDO> depositorys = depositoryDetail.getDepositorys();
                depositorys.forEach(depository -> depository.setParentId(depositoryDetail.getId()));
                memberDepositoryDetailRepository.saveAll(depositorys);
            }
        }
        relationDO.getDepositDetails().addAll(depositoryDetails);

    }

    /**
     * 循环、递归删除MemberDepositoryDetailDO
     * @param validatingDetails 已存在待审核入库资料
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void deleteDepositDetails(List<MemberDepositoryDetailDO> validatingDetails) {
        for (MemberDepositoryDetailDO validatingDetail : validatingDetails) {
            List<MemberDepositoryDetailDO> details = memberDepositoryDetailRepository.findAllByParentId(validatingDetail.getId());
            if (!CollectionUtils.isEmpty(details)) {
                deleteDepositDetails(details);
            }
        }
        memberDepositoryDetailRepository.deleteAll(validatingDetails);
    }

    /**
     * 申请成为会员时，检查入库资料，调用方要设置MemberRelationDO并保存
     *
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级会员角色Id
     * @param subRole 下级会员角色
     * @param detailMap     入库资料
     * @return 入库资料列表
     */
    @Override
    public List<MemberDepositoryDetailDO> checkMemberDepositoryDetail(Long upperMemberId, Long upperRoleId, MemberRoleDO subRole, Map<String, Object> detailMap) {
        List<MemberRegisterConfigDO> memberConfigList = findMemberDepositoryConfig(upperMemberId, upperRoleId, subRole);

        return checkMemberDepositoryDetail(null, memberConfigList, detailMap, MemberDetailVersionEnum.TO_BE_VALIDATE);
    }

    /**
     * 检查并生成指定版本的入库资料
     *
     * @param relationDO       入库资料关联的会员关系，如果为Null，调用方要设置MemberRelationDO并保存
     * @param memberConfigList 注册资料列表
     * @param detailMap        前端传递的入库资料Map
     * @param versionEnum      指定版本
     * @return 入库资料列表
     */
    @Override
    public List<MemberDepositoryDetailDO> checkMemberDepositoryDetail(MemberRelationDO relationDO, List<MemberRegisterConfigDO> memberConfigList, Map<String, Object> detailMap, MemberDetailVersionEnum versionEnum) {
        if(CollectionUtils.isEmpty(memberConfigList)) {
            return new ArrayList<>();
        }

        //判断入库资料字段是否齐全
        baseMemberRegisterDetailService.checkDetailFields(memberConfigList, detailMap);

        List<MemberDepositoryDetailDO> depositoryDetails = new ArrayList<>();

        for (Map.Entry<String, Object> entry : detailMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() instanceof HashMap || entry.getValue() instanceof ArrayList ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());

            MemberRegisterConfigDO configDO = memberConfigList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
            if (configDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DETAIL_FIELD_DOES_NOT_EXIST);
            }

            DetailCheckBO checkResult = baseMemberRegisterDetailService.checkDetail(configDO, value);

            MemberDepositoryDetailDO memberDepositoryDetailDO = new MemberDepositoryDetailDO();
            memberDepositoryDetailDO.setAllowSelect(configDO.getAllowSelect());
            memberDepositoryDetailDO.setCreateTime(LocalDateTime.now());
            memberDepositoryDetailDO.setMemberConfig(configDO);
            memberDepositoryDetailDO.setGroupName(RgConfigUtil.getFieldGroupName(configDO));
            memberDepositoryDetailDO.setRelation(relationDO);
            //版本
            memberDepositoryDetailDO.setVersion(versionEnum.getCode());
            memberDepositoryDetailDO.setFieldName(configDO.getFieldName());
            memberDepositoryDetailDO.setFieldLocalName(RgConfigUtil.getFieldLocalName(configDO));
            memberDepositoryDetailDO.setFieldType(configDO.getFieldType());
            memberDepositoryDetailDO.setAttr(configDO.getAttr());
            memberDepositoryDetailDO.setTagEnum(configDO.getTagEnum());
            memberDepositoryDetailDO.setStatus(configDO.getFieldStatus());
            memberDepositoryDetailDO.setValidate(configDO.getValidate());
            memberDepositoryDetailDO.setDetail(MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) ? "" : checkResult.getDetail());
            memberDepositoryDetailDO.setProvinceCode(checkResult.getProvinceCode());
            memberDepositoryDetailDO.setProvinceName(checkResult.getProvinceName());
            memberDepositoryDetailDO.setCityCode(checkResult.getCityCode());
            memberDepositoryDetailDO.setCityName(checkResult.getCityName());
            memberDepositoryDetailDO.setDistrictCode(checkResult.getDistrictCode());
            memberDepositoryDetailDO.setDistrictName(checkResult.getDistrictName());
            memberDepositoryDetailDO.setLabels(checkResult.getLabels());
            // 是列表字段
            if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) && StringUtils.hasLength(checkResult.getDetail())) {
                List<MemberRegisterConfigDO> memberConfigs = memberRegisterConfigRepository.findAllByParentId(configDO.getId());
                List<Map<String, Object>> details = JSONObject.parseObject(checkResult.getDetail(), new TypeReference<List<Map<String, Object>>>(){});
                List<MemberDepositoryDetailDO> registerResult = foreachDepositorys(details, memberConfigs);

                memberDepositoryDetailDO.setDepositorys(registerResult);
            }
            depositoryDetails.add(memberDepositoryDetailDO);
        }

        return depositoryDetails;
    }

    /**
     * 循环获取子字段资料
     * @param detailMaps 前端传递的入库资料Map
     * @param memberConfigList 字段的子字段列表
     * @return 操作结果
     */
    private List<MemberDepositoryDetailDO> foreachDepositorys(List<Map<String, Object>> detailMaps, List<MemberRegisterConfigDO> memberConfigList) {
        List<MemberDepositoryDetailDO> depositoryDetailList = new ArrayList<>();
        int index = 1;
        for (Map<String, Object> detailMap : detailMaps) {
            for (Map.Entry<String, Object> entry : detailMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue() instanceof HashMap || entry.getValue() instanceof ArrayList ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());

                MemberRegisterConfigDO configDO = memberConfigList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
                if (configDO == null) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DETAIL_FIELD_DOES_NOT_EXIST);
                }

                DetailCheckBO checkResult = baseMemberRegisterDetailService.checkDetail(configDO, value);

                MemberDepositoryDetailDO memberDepositoryDetailDO = new MemberDepositoryDetailDO();
                memberDepositoryDetailDO.setAllowSelect(configDO.getAllowSelect());
                memberDepositoryDetailDO.setCreateTime(LocalDateTime.now());
                memberDepositoryDetailDO.setMemberConfig(configDO);
                memberDepositoryDetailDO.setGroupName(RgConfigUtil.getFieldGroupName(configDO));
                // 与列表字段关联，不与会员关系关联
//                memberDepositoryDetailDO.setRelation(relationDO);
                // 与列表字段关联，不区分版本
//                memberDepositoryDetailDO.setVersion(code);
                memberDepositoryDetailDO.setFieldName(configDO.getFieldName());
                memberDepositoryDetailDO.setFieldLocalName(RgConfigUtil.getFieldLocalName(configDO));
                memberDepositoryDetailDO.setFieldType(configDO.getFieldType());
                memberDepositoryDetailDO.setAttr(configDO.getAttr());
                memberDepositoryDetailDO.setTagEnum(configDO.getTagEnum());
                memberDepositoryDetailDO.setStatus(configDO.getFieldStatus());
                memberDepositoryDetailDO.setValidate(configDO.getValidate());
                memberDepositoryDetailDO.setDetail(MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) ? "" : checkResult.getDetail());
                memberDepositoryDetailDO.setProvinceCode(checkResult.getProvinceCode());
                memberDepositoryDetailDO.setProvinceName(checkResult.getProvinceName());
                memberDepositoryDetailDO.setCityCode(checkResult.getCityCode());
                memberDepositoryDetailDO.setCityName(checkResult.getCityName());
                memberDepositoryDetailDO.setDistrictCode(checkResult.getDistrictCode());
                memberDepositoryDetailDO.setDistrictName(checkResult.getDistrictName());
                memberDepositoryDetailDO.setLabels(checkResult.getLabels());
                // 是列表字段
                if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) && StringUtils.hasLength(checkResult.getDetail())) {
                    List<MemberRegisterConfigDO> memberConfigs = memberRegisterConfigRepository.findAllByParentId(configDO.getId());
                    List<Map<String, Object>> details = JSONObject.parseObject(checkResult.getDetail(), new TypeReference<List<Map<String, Object>>>(){});
                    List<MemberDepositoryDetailDO> registerResult = foreachDepositorys(details, memberConfigs);

                    memberDepositoryDetailDO.setDepositorys(registerResult);
                }
                memberDepositoryDetailDO.setGroupIndex(index);
                depositoryDetailList.add(memberDepositoryDetailDO);
            }
            index++;
        }
        return depositoryDetailList;
    }

    /**
     * 查询会员入库资料
     *
     * @param relationDO  会员关系
     * @param versionEnum 版本
     * @return 查询结果
     */
    @Override
    public List<RegisterDetailGroupResp> findMemberDepositoryDetail(MemberRelationDO relationDO, MemberDetailVersionEnum versionEnum) {
        List<MemberDepositoryDetailDO> depositDetails = memberDepositoryDetailRepository.findByRelationAndVersion(relationDO, versionEnum.getCode());
        if (CollectionUtils.isEmpty(depositDetails)) {
            return new ArrayList<>();
        }

        //筛选出状态为 “启用” 的配置，按照groupName进行分组
        Map<String, List<MemberDepositoryDetailDO>> groupList = depositDetails.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(MemberDepositoryDetailDO::getGroupName));

        return groupList.entrySet().stream().map(e -> {
            RegisterDetailGroupResp detailGroup = new RegisterDetailGroupResp();
            detailGroup.setGroupName(e.getKey());

            List<RegisterDetailResp> elements = e.getValue().stream().map(c -> {
                RegisterDetailResp detail = new RegisterDetailResp();
                detail.setFieldName(c.getFieldName());
                detail.setFieldLocalName(c.getFieldLocalName());
                detail.setFieldType(c.getFieldType());
                detail.setAttr(c.getAttr());
                detail.setFieldEmpty(c.getMemberConfig().getFieldEmpty());
                detail.setFieldLength(c.getMemberConfig().getFieldLength());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldRemark(RgConfigUtil.getFieldRemark(c.getMemberConfig()));
                detail.setRuleEnum(c.getMemberConfig().getRuleEnum());
                detail.setPattern(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getMemberConfig().getRuleEnum()));
                detail.setMsg(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getMemberConfig().getRuleEnum()));
                detail.setFieldEnum(CollectionUtils.isEmpty(c.getMemberConfig().getLabels()) ? new ArrayList<>() : c.getMemberConfig().getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));

                DetailValueBO valueBO = getDetailValue(c);
                detail.setFieldValue(valueBO.getFieldValue());
                detail.setLastValue(valueBO.getLastValue());

                return detail;
            }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 获取填写资料
     * @param memberDepositoryDetails 填写资料集合
     * @param configId                配置Id
     * @return MemberDepositoryDetailDO
     */
    private MemberDepositoryDetailDO getMemberDepositoryDetail(List<MemberDepositoryDetailDO> memberDepositoryDetails, Long configId){
        return memberDepositoryDetails.stream().filter(detail -> Objects.equals(detail.getMemberConfig().getId(), configId)).findFirst().orElse(null);
    }

    /**
     * 查询会员管理流程规则配置
     * @param memberId  上级会员Id
     * @param roleId    上级角色ID
     * @param subRole   下级角色
     * @return MemberProcessRuleDO
     */
    private MemberProcessRuleDO getMemberProcessRule(Long memberId, Long roleId, MemberRoleDO subRole){
        return memberProcessRuleRepository.findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(memberId, roleId, subRole, EnableDisableStatusEnum.ENABLE.getCode());
    }

    /**
     * 查询会员入库资料（先查询“正在使用”的版本，如无则查询“正在审核”的版本）
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public List<RegisterDetailGroupResp> switchMemberDepositoryDetail(MemberRelationDO relationDO) {
        List<MemberDepositoryDetailDO> depositDetailList = Optional.ofNullable(memberDepositoryDetailRepository.findByRelation(relationDO)).orElse(new ArrayList<>());

        List<MemberDepositoryDetailDO> depositDetails = depositDetailList.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(depositDetails)) {
            depositDetails = depositDetailList.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());
        }

        //筛选出状态为 “启用” 的配置，按照groupName进行分组
        Map<String, List<MemberDepositoryDetailDO>> groupList = depositDetails.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(MemberDepositoryDetailDO::getGroupName));

        // 查询会员流程规则
        MemberProcessRuleDO memberProcessRule = getMemberProcessRule(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRole());

        // 入库资料
        Set<MemberRegisterConfigDO> memberConfigs = Optional.ofNullable(memberProcessRule).map(MemberProcessRuleDO::getConfigs).orElse(new HashSet<>());
        //筛选出状态为 “启用” 的配置，按照groupName进行分组
        Map<String, List<MemberRegisterConfigDO>> memberConfigGroups = memberConfigs.stream().filter(c -> c.getFieldStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(RgConfigUtil::getFieldGroupName))
                .entrySet().stream()
                .sorted(Comparator.comparingInt(
                        entry -> entry.getValue().get(0).getFieldOrder()
                )).collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));

        // 填充资料
        return memberConfigGroups.entrySet().stream().map(map -> {

            RegisterDetailGroupResp detailGroup = new RegisterDetailGroupResp();
            detailGroup.setGroupName(map.getKey());

            // 已填写资料
            List<MemberDepositoryDetailDO> memberDepositoryDetails = Optional.ofNullable(groupList.get(map.getKey())).orElse(new ArrayList<>());

            List<RegisterDetailResp> elements = map.getValue().stream().map(c -> {
                RegisterDetailResp detail = new RegisterDetailResp();
                detail.setFieldName(c.getFieldName());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c));
                detail.setFieldType(c.getFieldType());
                detail.setAttr(c.getAttr());
                detail.setFieldEmpty(c.getFieldEmpty());
                detail.setFieldLength(c.getFieldLength());
                detail.setFieldOrder(c.getFieldOrder());
                detail.setFieldRemark(RgConfigUtil.getFieldRemark(c));
                detail.setRuleEnum(c.getRuleEnum());
                detail.setPattern(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getRuleEnum()));
                detail.setMsg(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getRuleEnum()));
                detail.setFieldEnum(CollectionUtils.isEmpty(c.getLabels()) ? new ArrayList<>() : c.getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));
                detail.setConfigs(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initMemberConfig(c.getId()) : new ArrayList<>());

                // 查询填写资料
                MemberDepositoryDetailDO memberDetail = getMemberDepositoryDetail(memberDepositoryDetails, c.getId());
                if (Objects.nonNull(memberDetail)){
                    DetailValueBO valueBO = getDetailValue(memberDetail);
                    detail.setFieldValue(valueBO.getFieldValue());
                    detail.setLastValue(valueBO.getLastValue());
                    detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepositoryDetail(memberDetail.getId()) : new ArrayList<>());
                } else {
                    DetailValueBO valueBO = getDetailValue(c);
                    detail.setFieldValue(valueBO.getFieldValue());
                    detail.setLastValue(valueBO.getLastValue());
                    detail.setRegisters(new ArrayList<>());
                }

                return detail;
            }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 查询会员入库资料（合并版本）
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    @Override
    public List<DetailTextGroupResp> mergeMemberDepositoryDetailText(MemberRelationDO relationDO) {
        List<MemberDepositoryDetailDO> depositoryDetails = memberDepositoryDetailRepository.findByRelation(relationDO);
        if(CollectionUtils.isEmpty(depositoryDetails)) {
            return new ArrayList<>();
        }

        List<MemberDepositoryDetailDO> usingList = depositoryDetails.stream().filter(depositoryDetail -> depositoryDetail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        List<MemberDepositoryDetailDO> modifiedList = depositoryDetails.stream().filter(depositoryDetail -> depositoryDetail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(modifiedList)) {
            //按照groupName进行分组
            Map<String, List<MemberDepositoryDetailDO>> groupList = usingList.stream().collect(Collectors.groupingBy(MemberDepositoryDetailDO::getGroupName));

            return groupList.entrySet().stream().map(e -> {
                DetailTextGroupResp detailGroup = new DetailTextGroupResp();
                detailGroup.setGroupName(e.getKey());
                List<DetailTextResp> elements = e.getValue().stream().map(c -> {
                    DetailTextResp detail = new DetailTextResp();
                    detail.setFieldType(c.getFieldType());
                    detail.setFieldLocalName(c.getFieldLocalName());
                    detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                    detail.setFieldValue(c.getDetail());
                    detail.setLastValue("");
                    detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                    detail.setLastRegisters(new ArrayList<>());
                    return detail;
                }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());

                detailGroup.setElements(elements);
                return detailGroup;
            }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
        } else {
            //按照groupName进行分组
            Map<String, List<MemberDepositoryDetailDO>> groupList = modifiedList.stream().collect(Collectors.groupingBy(MemberDepositoryDetailDO::getGroupName));

            return groupList.entrySet().stream().map(e -> {
                DetailTextGroupResp detailGroup = new DetailTextGroupResp();
                detailGroup.setGroupName(e.getKey());
                List<DetailTextResp> elements = e.getValue().stream().map(c -> {
                    MemberDepositoryDetailDO usingDetail = usingList.stream().filter(d -> d.getFieldName().equals(c.getFieldName())).findFirst().orElse(null);
                    DetailTextResp detail = new DetailTextResp();
                    detail.setFieldType(c.getFieldType());
                    detail.setFieldLocalName(c.getFieldLocalName());
                    detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                    detail.setFieldValue(c.getDetail());
                    detail.setLastValue(usingDetail == null ? "" : usingDetail.getDetail());
                    detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                    detail.setLastRegisters(usingDetail == null ? new ArrayList<>() : MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(usingDetail.getId()) : new ArrayList<>());
                    return detail;
                }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());

                detailGroup.setElements(elements);
                return detailGroup;
            }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
        }
    }

    /**
     * 查询会员入库资料（指定版本）
     *
     * @param relationDO  会员关系
     * @param versionEnum 版本
     * @return 查询结果
     */
    @Override
    public List<DetailTextGroupResp> findMemberDepositoryDetailText(MemberRelationDO relationDO, MemberDetailVersionEnum versionEnum) {
        List<MemberDepositoryDetailDO> depositoryDetails = memberDepositoryDetailRepository.findByRelationAndVersion(relationDO, versionEnum.getCode());
        if(CollectionUtils.isEmpty(depositoryDetails)) {
            return new ArrayList<>();
        }

        //按照groupName进行分组
        Map<String, List<MemberDepositoryDetailDO>> groupList = depositoryDetails.stream().collect(Collectors.groupingBy(MemberDepositoryDetailDO::getGroupName));

        return groupList.entrySet().stream().map(e -> {
            DetailTextGroupResp detailGroup = new DetailTextGroupResp();
            detailGroup.setGroupName(e.getKey());
            List<DetailTextResp> elements = e.getValue().stream().map(c -> {
                DetailTextResp detail = new DetailTextResp();
                detail.setFieldType(c.getFieldType());
                detail.setFieldLocalName(c.getFieldLocalName());
                detail.setFieldName(c.getFieldName());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldValue(c.getDetail());
                detail.setLastValue("");
                detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                return detail;
            }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 查询该字段包含的子字段
     * @param id 父id
     * @return 子列表集
     */
    private List<List<DetailTextResp>> initDepository(Long id) {
        List<MemberDepositoryDetailDO> depositoryDetailDOS = memberDepositoryDetailRepository.findAllByParentId(id);
        Map<Integer, List<MemberDepositoryDetailDO>> groupList = depositoryDetailDOS.stream().filter(detail -> MemberConfigStatusEnum.ENABLED.getCode().equals(detail.getStatus()))
                .collect(Collectors.groupingBy(MemberDepositoryDetailDO::getGroupIndex));
        List<List<DetailTextResp>> result = new ArrayList<>();
        groupList.forEach((key, value) -> {
            List<DetailTextResp> collect = value.stream().map(c -> {
                DetailTextResp detail = new DetailTextResp();
                detail.setFieldType(c.getFieldType());
                detail.setFieldLocalName(c.getFieldLocalName());
                detail.setFieldName(c.getFieldName());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldValue(c.getDetail());
                detail.setLastValue("");
                detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                return detail;
            }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());
            result.add(key - 1, collect);
        });
        return result;
    }

    /**
     * 查询会员入库资料（先查找审核通过的版本，如果没有则查找正在审核的版本）
     *
     * @param relationDO  会员关系
     * @return 查询结果
     */
    @Override
    public List<DetailTextGroupResp> switchMemberDepositoryDetailText(MemberRelationDO relationDO) {
        List<MemberDepositoryDetailDO> depositoryDetailList = memberDepositoryDetailRepository.findByRelation(relationDO);
        if(CollectionUtils.isEmpty(depositoryDetailList)) {
            return new ArrayList<>();
        }

        List<MemberDepositoryDetailDO> depositoryDetails = depositoryDetailList.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(depositoryDetails)) {
            depositoryDetails = depositoryDetailList.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());
        }

        if(CollectionUtils.isEmpty(depositoryDetails)) {
            return new ArrayList<>();
        }

        //按照groupName进行分组
        Map<String, List<MemberDepositoryDetailDO>> groupList = depositoryDetails.stream().collect(Collectors.groupingBy(MemberDepositoryDetailDO::getGroupName));

        return groupList.entrySet().stream().map(e -> {
            DetailTextGroupResp detailGroup = new DetailTextGroupResp();
            detailGroup.setGroupName(e.getKey());
            List<DetailTextResp> elements = e.getValue().stream().map(c -> {
                DetailTextResp detail = new DetailTextResp();
                detail.setFieldType(c.getFieldType());
                detail.setFieldLocalName(c.getFieldLocalName());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldValue(c.getDetail());
                detail.setLastValue("");
                detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                detail.setLastRegisters(new ArrayList<>());
                return detail;
            }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 入库审核通过后，修改入库资料版本为“Using”
     *
     * @param relationDO 会员关系
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateDepositDetailToUsing(MemberRelationDO relationDO) {
        List<MemberDepositoryDetailDO> details = memberDepositoryDetailRepository.findByRelation(relationDO);

        List<MemberDepositoryDetailDO> updateList = details.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(updateList)) {
            return;
        }

        List<MemberDepositoryDetailDO> deleteList = details.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(deleteList)) {
            List<Long> listIds = deleteList.stream().filter(del -> MemberConfigFieldTypeEnum.LIST.getMessage().equals(del.getFieldType())).map(MemberDepositoryDetailDO::getId).collect(Collectors.toList());
            listIds.forEach(id -> memberDepositoryDetailRepository.deleteByParentId(id));
            memberDepositoryDetailRepository.deleteAll(deleteList);
        }

        updateList.forEach(detail -> detail.setVersion(MemberDetailVersionEnum.USING.getCode()));

        memberDepositoryDetailRepository.saveAll(updateList);

        // 如果是企业采购商入库通过
        if (Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode()) && Objects.equals(relationDO.getSubRoleId(), baiTaiMemberProperties.getCustomerRoleId())) {
            AopProxyUtil.getCurrentProxy(this.getClass()).updateCompanyName(relationDO, updateList);

            MemberDO subMember = relationDO.getSubMember();
            if (Objects.nonNull(subMember.getCorporationId())) {
                CorporationDO corporationDO = corporationRepository.findById(subMember.getCorporationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));
                corporationDO.setPassedDate(LocalDateTime.now());
                corporationRepository.saveAndFlush(corporationDO);
            }
        }

        // 覆盖平台注册资料
        if (Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode()) || Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.MODIFY_PASSED.getCode())) {
            AopProxyUtil.getCurrentProxy(this.getClass()).updateCompanyName(relationDO, updateList);

            AopProxyUtil.getCurrentProxy(this.getClass()).coverRegisterDetail(relationDO.getSubMember(), updateList);

            AopProxyUtil.getCurrentProxy(this.getClass()).updateCorporationInfo(relationDO, updateList);

            updateOtherMemberDepositDetail(relationDO);
        }

    }

    private void updateOtherMemberDepositDetail(MemberRelationDO relationDO) {
        if (Objects.equals(relationDO.getOuterStatus(), MemberOuterStatusEnum.MODIFY_PASSED.getCode())) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    // 更新同一个企业下的其他会员信息
                    Long relationId = relationDO.getId();
                    CompletableFuture.runAsync(() -> {
                        try {
                            MemberRelationDO relationDO = relationRepository.findById(relationId).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST));
                            MemberDO subMember = relationDO.getSubMember();
                            Map<String, String> depositDetailMap = relationDO.getDepositDetails().stream().collect(Collectors.toMap(MemberDepositoryDetailDO::getFieldName, MemberDepositoryDetailDO::getDetail));
                            if (Objects.nonNull(subMember.getCorporationId())) {
                                List<MemberDO> otherMemberDOList = memberRepository.findAllByCorporationId(subMember.getCorporationId()).stream().filter(memberDO -> !memberDO.getId().equals(subMember.getId())).collect(Collectors.toList());
                                if (!CollectionUtils.isEmpty(otherMemberDOList)) {
                                    log.info("开始更新同一个企业下其他会员的资料：{}", subMember.getCorporationId());
                                    List<Long> otherSubMemberIds = otherMemberDOList.stream().map(MemberDO::getId).collect(Collectors.toList());
                                    List<MemberRelationDO> memberRelationDOList = relationRepository.findAllByMemberIdAndRoleIdAndSubMemberIdInAndSubRoleId(baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getSelfRoleId(), otherSubMemberIds, baiTaiMemberProperties.getCustomerRoleId());
                                    memberRelationDOList.forEach(memberRelationDO -> {
                                        Set<MemberDepositoryDetailDO> depositDetails = memberRelationDO.getDepositDetails();
                                        depositDetails.forEach(memberDepositoryDetailDO -> {
                                            convertOriginDepositDetail(memberDepositoryDetailDO, depositDetailMap);
                                        });
                                        memberDepositoryDetailRepository.saveAll(depositDetails);

                                        AopProxyUtil.getCurrentProxy(BaseMemberDepositDetailServiceImpl.class).coverRegisterDetail(memberRelationDO.getSubMember(), Lists.newArrayList(depositDetails));
                                    });
                                }
                            }
                        } catch (Exception e) {
                            log.error("更新同一个企业下其他会员的资料异常：", e);
                        }
                    });
                }
            });
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCorporationInfo(MemberRelationDO relationDO, List<MemberDepositoryDetailDO> updateList) {
        MemberDO subMember = relationDO.getSubMember();
        if (Objects.nonNull(subMember.getCorporationId())) {
            Map<String, String> detaiMap = updateList.stream().collect(Collectors.toMap(MemberDepositoryDetailDO::getFieldName, MemberDepositoryDetailDO::getDetail, (v1, v2) -> v1));
            CorporationDO corporationDO = corporationRepository.findById(subMember.getCorporationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));
            corporationDO.setName(detaiMap.get(MemberRegisterDetailConfigConstant.CORPORATION_NAME));
            corporationDO.setUnifiedSocialCode(detaiMap.get(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE));
            corporationDO.setBusinessLicense(detaiMap.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE));
            corporationDO.setBusinessLicenseValidEndTime(detaiMap.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME));
            corporationDO.setLegalPersonName(detaiMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME));
            corporationDO.setLegalPersonIdentityCardNo(detaiMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO));
            corporationDO.setIdCardFront(detaiMap.get(MemberRegisterDetailConfigConstant.ID_CARD_FRONT));
            corporationDO.setIdCardBack(detaiMap.get(MemberRegisterDetailConfigConstant.ID_CARD_BACK));
            corporationDO.setBusinessManager(detaiMap.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER));
            corporationDO.setBusinessManagerPhone(detaiMap.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE));
            corporationDO.setFinanceManager(detaiMap.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER));
            corporationDO.setFinanceManagerPhone(detaiMap.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE));
            corporationRepository.saveAndFlush(corporationDO);
        }
    }

    private static void convertOriginDepositDetail(MemberDepositoryDetailDO memberDepositoryDetailDO, Map<String, String> depositDetailMap) {
        if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.MEMBER_NAME)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.MEMBER_NAME));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.CORPORATION_NAME)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.CORPORATION_NAME));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.CORPORATION_SIMPLE_NAME));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.UNIFIED_SOCIAL_CODE));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_LICENSE)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.BUSINESS_LICENSE_VALID_END_TIME));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_NAME));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_IDENTITY_CARD_NO));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.LEGAL_PERSON_PHONE)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.LEGAL_PERSON_PHONE));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.ID_CARD_FRONT)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.ID_CARD_FRONT));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.ID_CARD_BACK)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.ID_CARD_BACK));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_MANAGER)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.BUSINESS_MANAGER_PHONE));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.FINANCE_MANAGER)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER));
        } else if (Objects.equals(memberDepositoryDetailDO.getFieldName(), MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE)) {
            memberDepositoryDetailDO.setDetail(depositDetailMap.get(MemberRegisterDetailConfigConstant.FINANCE_MANAGER_PHONE));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCompanyName(MemberRelationDO relationDO, List<MemberDepositoryDetailDO> updateDepositDetails) {
        log.info("修改会员名称：{}", relationDO.getSubMemberId());
        Optional<MemberDepositoryDetailDO> optional = updateDepositDetails.stream().filter(detail -> MemberRegisterDetailConfigConstant.MEMBER_NAME.equals(detail.getFieldName())).findFirst();
        if (!optional.isPresent()) {
            return;
        }

        String companyName = optional.get().getDetail();

        MemberDO subMember = relationDO.getSubMember();
        if (subMember.getName().equals(companyName)) {
            return;
        }

        if (memberRepository.existsByName(companyName)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
        }
        subMember.setName(companyName);
        memberRepository.saveAndFlush(subMember);
        Optional<UserDO> userOptional = subMember.getUsers().stream().filter(e -> UserTypeEnum.ADMIN.getCode().equals(e.getUserType())).findFirst();
        if (!userOptional.isPresent()){
            return;
        }
        UserDO userDO = userOptional.get();
        userDO.setName(companyName);
        userRepository.saveAndFlush(userDO);
    }

    /**
     * 覆盖平台注册资料
     */
    @Transactional(rollbackFor = Exception.class)
    public void coverRegisterDetail(MemberDO memberDO, List<MemberDepositoryDetailDO> depositoryDetailDOS) {

        List<MemberRegisterDetailDO> registerDetails = memberRegisterDetailRepository.findByMember(memberDO);

        List<MemberRegisterDetailDO> deleteRegisterList = registerDetails.stream().filter(detail -> detail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteRegisterList)) {
            List<Long> listIds = deleteRegisterList.stream().filter(del -> MemberConfigFieldTypeEnum.LIST.getMessage().equals(del.getFieldType())).map(MemberRegisterDetailDO::getId).collect(Collectors.toList());
            memberRegisterDetailRepository.deleteByParentIdIn(listIds);
            memberRegisterDetailRepository.deleteAll(deleteRegisterList);
        }

        Map<Long, List<MemberDepositoryDetailDO>> memberDepositoryDetailMap = new HashMap<>();
        List<MemberDepositoryDetailDO> memberDepositoryDetailDOList = depositoryDetailDOS.stream().filter(v -> Objects.equals(v.getFieldType(), MemberConfigFieldTypeEnum.LIST.getMessage())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(memberDepositoryDetailDOList)) {
            List<MemberDepositoryDetailDO> subMemberDepositoryDetailDOList = memberDepositoryDetailRepository.findAllByParentIdIn(memberDepositoryDetailDOList.stream().map(MemberDepositoryDetailDO::getId).collect(Collectors.toList()));
            memberDepositoryDetailMap = subMemberDepositoryDetailDOList.stream().collect(Collectors.groupingBy(MemberDepositoryDetailDO::getParentId));
        }

        Map<Long, List<MemberDepositoryDetailDO>> finalMemberDepositoryDetailMap = memberDepositoryDetailMap;
        List<MemberRegisterDetailDO> updateRegisterList = depositoryDetailDOS.stream().map(depositoryDetailDO -> {
            MemberRegisterDetailDO registerDetail = buildMemberRegisterDetailDO(depositoryDetailDO);

            // 非子字段才设置会员
            registerDetail.setMember(memberDO);

            if (Objects.equals(MemberConfigFieldTypeEnum.LIST.getMessage(), depositoryDetailDO.getFieldType())) {
                // 设置子字段
                registerDetail.setRegisters(finalMemberDepositoryDetailMap.get(depositoryDetailDO.getId()).stream().map(BaseMemberDepositDetailServiceImpl::buildMemberRegisterDetailDO).collect(Collectors.toList()));
            }

            return registerDetail;
        }).collect(Collectors.toList());

        memberRegisterDetailRepository.saveAll(updateRegisterList);

        // 兼容list字段，修改成循环保存
        for (MemberRegisterDetailDO memberRegisterDetail : updateRegisterList) {
            if (!CollectionUtils.isEmpty(memberRegisterDetail.getRegisters())) {
                List<MemberRegisterDetailDO> registers = memberRegisterDetail.getRegisters();
                registers.forEach(registerDetail -> registerDetail.setParentId(memberRegisterDetail.getId()));
                memberRegisterDetailRepository.saveAll(registers);
            }
        }
    }

    private static MemberRegisterDetailDO buildMemberRegisterDetailDO(MemberDepositoryDetailDO memberDepositoryDetailDO) {
        MemberRegisterDetailDO subMemberRegisterDetailDO = new MemberRegisterDetailDO();
        subMemberRegisterDetailDO.setVersion(memberDepositoryDetailDO.getVersion());
        subMemberRegisterDetailDO.setFieldType(memberDepositoryDetailDO.getFieldType());
        subMemberRegisterDetailDO.setAttr(memberDepositoryDetailDO.getAttr());
        subMemberRegisterDetailDO.setTagEnum(memberDepositoryDetailDO.getTagEnum());
        subMemberRegisterDetailDO.setStatus(memberDepositoryDetailDO.getStatus());
        subMemberRegisterDetailDO.setAllowSelect(memberDepositoryDetailDO.getAllowSelect());
        subMemberRegisterDetailDO.setDetail(memberDepositoryDetailDO.getDetail());
        subMemberRegisterDetailDO.setProvinceCode(memberDepositoryDetailDO.getProvinceCode());
        subMemberRegisterDetailDO.setProvinceName(memberDepositoryDetailDO.getProvinceName());
        subMemberRegisterDetailDO.setCityCode(memberDepositoryDetailDO.getCityCode());
        subMemberRegisterDetailDO.setCityName(memberDepositoryDetailDO.getCityName());
        subMemberRegisterDetailDO.setDistrictCode(memberDepositoryDetailDO.getDistrictCode());
        subMemberRegisterDetailDO.setDistrictName(memberDepositoryDetailDO.getDistrictName());
        subMemberRegisterDetailDO.setLabels(memberDepositoryDetailDO.getLabels());
        subMemberRegisterDetailDO.setGroupIndex(memberDepositoryDetailDO.getGroupIndex());
        subMemberRegisterDetailDO.setMemberConfig(memberDepositoryDetailDO.getMemberConfig());
        return subMemberRegisterDetailDO;
    }

    /**
     * 检查注册资料、入库资料的字段是否齐全
     *
     * @param depositoryDetailList 入库资料列表
     * @param detailMap            前端传递的入库资料
     * @return 检查结果
     */
    @Override
    public WrapperResp<Void> checkDetailFields(List<MemberDepositoryDetailDO> depositoryDetailList, Map<String, Object> detailMap) {
        if(CollectionUtils.isEmpty(depositoryDetailList)) {
            return WrapperUtil.success();
        }

        if(CollectionUtils.isEmpty(detailMap)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_DETAIL_IS_MISSING);
        }

        MemberDepositoryDetailDO depositoryDetail = depositoryDetailList.stream().filter(detail -> detailMap.keySet().stream().noneMatch(key -> key.equals(detail.getFieldName()))).findFirst().orElse(null);
        if(depositoryDetail != null) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_DETAIL_IS_MISSING, MemberStringEnum.FILL_IN.getName() + depositoryDetail.getFieldLocalName());
        }

        return WrapperUtil.success();
    }

    /**
     * 更新入库资料搜索数据
     * @param relationDO 会员关系
     */
    @Async
    @Override
    public void updateMemberDepositoryDetailSelect(MemberRelationDO relationDO, List<MemberDepositoryDetailDO> depositoryDetailList) {
        StringBuilder detail = new StringBuilder();
        depositoryDetailList.stream().filter(d -> d.getAllowSelect().equals(CommonBooleanEnum.YES.getCode())).forEach(depositoryDetail -> {
            if ("checkbox".equals(depositoryDetail.getFieldType())){
                depositoryDetail.getLabels().forEach( label ->{
                    detail.append(","+depositoryDetail.getFieldName());
                    detail.append(":"+label.getLabelValue());
                });
            }else{
                detail.append(","+depositoryDetail.getFieldName());
                detail.append(":"+depositoryDetail.getDetail());
            }
        });
        memberDepositoryDetailSelectRepository.deleteByRelation(relationDO);
        if(StringUtils.hasLength(detail)){
            MemberDepositoryDetailSelectDO memberDepositoryDetailSelectDO = new MemberDepositoryDetailSelectDO();
            memberDepositoryDetailSelectDO.setRelation(relationDO);
            memberDepositoryDetailSelectDO.setDetail(String.valueOf(detail));
            memberDepositoryDetailSelectRepository.save(memberDepositoryDetailSelectDO);
        }
    }

    @Async
    @Override
    public void updateDepositoryDetailAllowSelect(List<MemberDepositoryDetailDO> depositoryDetailList) {
        Map<Long, MemberRegisterConfigDO> memberConfigs = memberRegisterConfigRepository.findAll().stream().collect(Collectors.toMap(MemberRegisterConfigDO::getId, v -> v));
        depositoryDetailList.forEach( d -> {
            MemberRegisterConfigDO memberRegisterConfigDO = memberConfigs.get(d.getMemberConfigId());
            if(!Objects.isNull(memberRegisterConfigDO)){
                d.setAllowSelect(memberRegisterConfigDO.getAllowSelect());
            }
        });
        memberDepositoryDetailRepository.saveAll(depositoryDetailList);
    }

    @Override
    public void saveDepositoryDetail(MemberRelationDO relationDO, Integer outerStatus) {
        List<MemberDepositoryDetailDO> depositoryDetails = buildMemberDepositoryDetailList(relationDO, outerStatus);
        if (CollectionUtil.isEmpty(depositoryDetails)) {
            return;
        }
        memberDepositoryDetailRepository.saveAll(depositoryDetails);

        // 兼容list字段，修改成循环保存
        for (MemberDepositoryDetailDO depositoryDetail : depositoryDetails) {
            if (!CollectionUtils.isEmpty(depositoryDetail.getDepositorys())) {
                List<MemberDepositoryDetailDO> depositories = depositoryDetail.getDepositorys();
                depositories.forEach(depository -> depository.setParentId(depositoryDetail.getId()));
                memberDepositoryDetailRepository.saveAll(depositories);
            }
        }
        relationDO.setDepositDetails(Sets.newHashSet(depositoryDetails));
    }

    /**
     * 获取入库资料信息
     */
    public List<MemberDepositoryDetailDO> buildMemberDepositoryDetailList(MemberRelationDO relationDO, Integer outerStatus) {
        List<MemberRegisterDetailDO> registerDetailDOS = memberRegisterDetailRepository.findByMemberAndVersion(relationDO.getSubMember(), MemberDetailVersionEnum.USING.getCode());

        Map<Long, List<MemberRegisterDetailDO>> memberRegisterDetailMap = new HashMap<>();
        List<MemberRegisterDetailDO> memberRegisterDetailDOList = registerDetailDOS.stream().filter(v -> Objects.equals(v.getFieldType(), MemberConfigFieldTypeEnum.LIST.getMessage())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(memberRegisterDetailDOList)) {
            List<MemberRegisterDetailDO> subMemberRegisterDetailDOS = memberRegisterDetailRepository.findByParentIdIn(memberRegisterDetailDOList.stream().map(MemberRegisterDetailDO::getId).collect(Collectors.toList()));
            memberRegisterDetailMap = subMemberRegisterDetailDOS.stream().collect(Collectors.groupingBy(MemberRegisterDetailDO::getParentId));
        }

        List<MemberDepositoryDetailDO> depositoryDetails = new ArrayList<>();
        for (MemberRegisterDetailDO registerDetailDO : registerDetailDOS) {
            MemberDepositoryDetailDO memberDepositoryDetailDO = buildMemberDepositoryDetailDO(registerDetailDO, outerStatus);

            // 非子字段才设置关系
            memberDepositoryDetailDO.setRelation(relationDO);

            if (Objects.equals(registerDetailDO.getFieldType(), MemberConfigFieldTypeEnum.LIST.getMessage())) {
                List<MemberRegisterDetailDO> memberRegisterDetailDOS = memberRegisterDetailMap.get(registerDetailDO.getId());

                if (CollectionUtils.isEmpty(memberRegisterDetailDOS)) {
                    continue;
                }
                // 设置子资料项
                List<MemberDepositoryDetailDO> subMemberDepositoryDetailDOList = memberRegisterDetailDOS.stream().map(v -> buildMemberDepositoryDetailDO(v, outerStatus)).collect(Collectors.toList());

                memberDepositoryDetailDO.setDepositorys(subMemberDepositoryDetailDOList);
            }
            depositoryDetails.add(memberDepositoryDetailDO);
        }

        return depositoryDetails;
    }

    private static MemberDepositoryDetailDO buildMemberDepositoryDetailDO(MemberRegisterDetailDO memberRegisterDetailDO, Integer outerStatus) {
        MemberDepositoryDetailDO memberDepositoryDetailDO = new MemberDepositoryDetailDO();
        memberDepositoryDetailDO.setCreateTime(LocalDateTime.now());
        memberDepositoryDetailDO.setMemberConfig(memberRegisterDetailDO.getMemberConfig());
        memberDepositoryDetailDO.setVersion(Objects.equals(outerStatus, MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode()) ? MemberDetailVersionEnum.USING.getCode() : MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());
        memberDepositoryDetailDO.setGroupName(RgConfigUtil.getFieldGroupName(memberRegisterDetailDO.getMemberConfig()));
        memberDepositoryDetailDO.setFieldName(memberRegisterDetailDO.getMemberConfig().getFieldName());
        memberDepositoryDetailDO.setFieldLocalName(RgConfigUtil.getFieldLocalName(memberRegisterDetailDO.getMemberConfig()));
        memberDepositoryDetailDO.setFieldType(memberRegisterDetailDO.getFieldType());
        memberDepositoryDetailDO.setAttr(memberRegisterDetailDO.getAttr());
        memberDepositoryDetailDO.setTagEnum(memberRegisterDetailDO.getTagEnum());
        memberDepositoryDetailDO.setStatus(memberRegisterDetailDO.getStatus());
        memberDepositoryDetailDO.setValidate(memberRegisterDetailDO.getValidate());
        memberDepositoryDetailDO.setDetail(memberRegisterDetailDO.getDetail());
        memberDepositoryDetailDO.setProvinceCode(memberRegisterDetailDO.getProvinceCode());
        memberDepositoryDetailDO.setProvinceName(memberRegisterDetailDO.getProvinceName());
        memberDepositoryDetailDO.setCityCode(memberRegisterDetailDO.getCityCode());
        memberDepositoryDetailDO.setCityName(memberRegisterDetailDO.getCityName());
        memberDepositoryDetailDO.setDistrictCode(memberRegisterDetailDO.getDistrictCode());
        memberDepositoryDetailDO.setDistrictName(memberRegisterDetailDO.getDistrictName());
        memberDepositoryDetailDO.setLabels(memberRegisterDetailDO.getLabels());
        memberDepositoryDetailDO.setAllowSelect(memberRegisterDetailDO.getAllowSelect());
        memberDepositoryDetailDO.setGroupIndex(memberRegisterDetailDO.getGroupIndex());
        return memberDepositoryDetailDO;
    }

    /**
     * 查询入库资料时，根据入库资料的FieldType，获取前端显示的值
     *
     * @param detailDO 入库资料
     * @return 字段值
     */
    private DetailValueBO getDetailValue(MemberDepositoryDetailDO detailDO) {
        DetailValueBO detail = new DetailValueBO();
        //省、市、区下拉框
        if ("area".equals(detailDO.getFieldType())) {
            detail.setFieldValue(new RegisterAreaResp(detailDO.getProvinceCode(), detailDO.getCityCode(), detailDO.getDistrictCode()));
            detail.setLastValue(new RegisterAreaResp());
        } else if ("select".equals(detailDO.getFieldType()) || "radio".equals(detailDO.getFieldType())) {
            MemberDetailLabelBO labelBO = detailDO.getLabels().stream().findFirst().orElse(null);
            detail.setFieldValue(labelBO == null ? null : labelBO.getLabelId());
            detail.setLastValue(null);
        } else if ("checkbox".equals(detailDO.getFieldType())) {
            detail.setFieldValue(detailDO.getLabels().stream().map(MemberDetailLabelBO::getLabelId).collect(Collectors.toList()));
            detail.setLastValue(new ArrayList<>());
        } else {
            detail.setFieldValue(detailDO.getDetail());
            detail.setLastValue("");
        }

        return detail;
    }

    /**
     * 查询入库资料时，根据入库资料的FieldType，获取前端显示的值
     *
     * @param detailDO 入库资料
     * @return 字段值
     */
    private DetailValueBO getDetailValue(MemberRegisterConfigDO detailDO) {
        DetailValueBO detail = new DetailValueBO();
        //省、市、区下拉框
        if ("area".equals(detailDO.getFieldType())) {
            detail.setFieldValue(new RegisterAreaResp());
            detail.setLastValue(new RegisterAreaResp());
        } else if ("select".equals(detailDO.getFieldType()) || "radio".equals(detailDO.getFieldType())) {
            detail.setFieldValue(null);
            detail.setLastValue(null);
        } else if ("checkbox".equals(detailDO.getFieldType())) {
            detail.setFieldValue(new ArrayList<>());
            detail.setLastValue(new ArrayList<>());
        } else {
            detail.setFieldValue("");
            detail.setLastValue("");
        }

        return detail;
    }
}
