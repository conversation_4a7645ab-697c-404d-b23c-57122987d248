package com.ssy.lingxi.member.model.req.lifecycle;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 会员考评打分VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Data
public class MemberAppraisalGradeReq implements Serializable {
    private static final long serialVersionUID = 1830941838746529993L;

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空")
    private Long id;

    /**
     * 考评项目
     */
    @Valid
    private List<MemberAppraisalItemGradeReq> items;
}
