package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.basic.MemberVisitAddOrUpdateRequest;
import com.ssy.lingxi.member.model.req.basic.MemberVisitListDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberVisitListResp;
import com.ssy.lingxi.member.model.resp.basic.MemberVisitTypeItemResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员能力 - 会员拜访
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-18
 */
public interface IMemberVisitService {

    /**
     * 查询会员拜访列表
     * <AUTHOR>
     * @since 2020/6/19
     */
    PageDataResp<MemberVisitListResp> memberVisitList(MemberVisitListDataReq request, UserLoginCacheDTO sysUser, Integer roleTag);

    /**
     * 查询会员拜访详情
     * <AUTHOR>
     * @since 2020/8/5
     */
    MemberVisitListResp memberVisitDetails(Long id, UserLoginCacheDTO sysUser, Integer roleTag);

    /**
     * 查询全部会员拜访
     * <AUTHOR>
     * @since 2020/8/5
     */
    List<MemberVisitListResp> memberVisitAll(UserLoginCacheDTO sysUser );

    /**
     * 添加/修改会员拜访
     * <AUTHOR>
     * @since 2020/6/19
     */
    void memberVisitAddOrUpdate(MemberVisitAddOrUpdateRequest request, UserLoginCacheDTO sysUser);

    /**
     * 删除会员拜访
     * <AUTHOR>
     * @since 2020/6/19
     */
    void memberVisitDelete(CommonIdReq request);

    /**
     * 查询拜访类型, 拜访级别枚举
     * @param headers
     * @return
     */
    MemberVisitTypeItemResp getMemberVisitOfVisitTypeItems(HttpHeaders headers);
}
