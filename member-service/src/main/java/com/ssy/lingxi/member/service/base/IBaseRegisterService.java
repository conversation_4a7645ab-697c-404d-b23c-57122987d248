package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.req.api.member.CustomerSyncReq;
import com.ssy.lingxi.common.model.req.api.member.CorporationSyncReq;
import com.ssy.lingxi.member.enums.MemberRegisterSourceEnum;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.login.ResetPasswordByEmailCodeReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordBySmsCodeReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterResultResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterTypeMenuResp;
import com.ssy.lingxi.member.model.resp.login.MultiAccInfoResp;

import java.util.List;
import java.util.Map;

/**
 * 会员注册相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-02
 */
public interface IBaseRegisterService {
    /**
     * 检查手机号码是否被注册使用
     * @param phoneReq 手机号码
     */
    void checkPhoneRegistered(PhoneReq phoneReq);

    /**
     * 移动端 - 手机找回密码页面，检查手机号码是否被注册使用
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    void checkPhoneExistsByMobile(PhoneReq phoneReq);

    /**
     * 检查邮箱是否被注册使用
     * @param emailReq 邮箱
     * @return 检查结果
     */
    void checkEmailRegistered(EmailReq emailReq);

    /**
     * 校验邀请码是否存在
     * @param invitationCodeReq 邀请码
     * @return 检查结果
     */
    void checkInvitationCodeExists(InvitationCodeReq invitationCodeReq);

    /**
     * 发送注册时短信验证码（无滑块验证）
     * @param phoneReq 手机号码
     * @return 发送结果
     */
    void sendRegisterSmsCode(PhoneReq phoneReq);

    /**
     * 发送注册时短信验证码
     * @param phoneVO 手机号码
     * @return 发送结果
     */
    void sendRegisterSmsCode(SmsPhoneReq phoneVO);

    /**
     * 发送手机号找回密码时的短信验证码
     * @param phoneVO 手机号码
     * @return 发送结果
     */
    void sendResetPasswordSmsCode(SmsPhoneReq phoneVO);

    /**
     * App - 发送手机号找回密码时的短信验证码
     * @param phoneReq 手机号码
     * @return 发送结果
     */
    void sendMobileResetPasswordSmsCode(PhoneReq phoneReq);

    /**
     * 校验手机号找回密码时的短信验证码是否正确
     * @param phoneSmsReq 手机号码、验证码
     * @return 发送结果
     */
    List<MultiAccInfoResp> checkResetPasswordSmsCode(PhoneSmsReq phoneSmsReq);

    /**
     * 根据短信验证码重设密码
     * @param codeVO 接口参数
     * @return 操作结果
     */
    void resetPasswordBySmsCode(ResetPasswordBySmsCodeReq codeVO);

    /**
     * 发送邮箱找回密码时的邮件
     * @param emailReq 邮箱地址
     * @param fromMobile 是否来自移动客户端（如果是，则要判断有无服务消费者角色）
     * @return 发送结果
     */
    void sendResetPasswordEmail(EmailReq emailReq, Boolean fromMobile);

    /**
     * 校验发送邮箱找回密码时的邮件中的验证码是否正确
     * @param emailVO 邮箱地址
     * @return 发送结果
     */
    List<MultiAccInfoResp> checkResetPasswordEmailCode(EmailSmsReq emailVO);

    /**
     * 根据邮箱验证码重设密码
     * @param codeVO 接口参数
     * @return 操作结果
     */
    void resetPasswordByEmailCode(ResetPasswordByEmailCodeReq codeVO);

    /**
     * 获取会员注册页面-会员类型、商业类型页面内容（第二页）
     * @return 操作结果
     */
    List<MemberRegisterTypeMenuResp> getRegisterTypePageContent();

    /**
     * 获取App客户端会员注册页面-会员类型、商业类型页面内容（第二页）
     * @return 操作结果
     */
    List<MemberRegisterTypeMenuResp> getMobileRegisterTypePageContent();

    /**
     * 获取会员注册页面-详细信息页面内容（第三页）
     * @param detailVO 接口参数
     * @return 注册第三页的内容
     */
    List<MemberConfigGroupResp> getRegisterDetailPageContent(MemberDetailReq detailVO);

    /**
     * 平台会员注册
     * @param registerSourceEnum 注册来源枚举
     * @param smsCode  短信验证码
     * @param telCode 手机号码前缀
     * @param phone 手机号码
     * @param userPassword 用户密码（Aes加密后的密码）
     * @param email 邮箱
     * @param memberType 会员类型
     * @param memberRoleId 会员角色Id
     * @param detail 注册资料
     * @param invitationCode 邀请码
     * @param groupIdentifier 实控人分组标识
     * @return 注册结果
     */
    MemberRegisterResultResp registerPlatformMember(MemberRegisterSourceEnum registerSourceEnum, String smsCode, String telCode, String phone, String userPassword, String email, Integer memberType, Long memberRoleId, Map<String, Object> detail, String invitationCode, String groupIdentifier);

    /**
     * 同步企业
     * @param corporationSyncReq 请求参数
     */
    void syncCorporation(CorporationSyncReq corporationSyncReq);

    /**
     * 同步客户
     * @param customerSyncReq 请求参数
     */
    void syncCustomer(CustomerSyncReq customerSyncReq);

}
