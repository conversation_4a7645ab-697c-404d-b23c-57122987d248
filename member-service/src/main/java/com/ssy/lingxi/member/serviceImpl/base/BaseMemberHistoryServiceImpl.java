package com.ssy.lingxi.member.serviceImpl.base;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.AopProxyUtil;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.appraisal.MemberAppraisalHistoryDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailHistoryDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailListHistoryDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberChangeRequestFormHistoryDO;
import com.ssy.lingxi.member.entity.do_.rectify.MemberRectifyInnerHistoryDO;
import com.ssy.lingxi.member.entity.do_.rectify.MemberRectifyOuterHistoryDO;
import com.ssy.lingxi.member.entity.do_.validate.MemberInnerValidateHistoryDO;
import com.ssy.lingxi.member.entity.do_.validate.MemberValidateHistoryDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.resp.basic.DetailTextResp;
import com.ssy.lingxi.member.model.resp.basic.MemberInnerHistoryResp;
import com.ssy.lingxi.member.model.resp.basic.MemberOuterHistoryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberAppraisalHistoryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberChangeRequestFormHistoryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRectifyInnerHistoryResp;
import com.ssy.lingxi.member.model.resp.lifecycle.MemberRectifyOuterHistoryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDepositDetailHistoryResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberHistoryService;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 会员历史记录基础服务接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-20
 */
@Service
public class BaseMemberHistoryServiceImpl implements IBaseMemberHistoryService {
    @Resource
    private MemberValidateHistoryRepository memberValidateHistoryRepository;

    @Resource
    private MemberInnerValidateHistoryRepository memberInnerValidateHistoryRepository;

    @Resource
    private MemberDepositoryDetailHistoryRepository memberDepositoryDetailHistoryRepository;

    @Resource
    private MemberAppraisalHistoryRepository memberAppraisalHistoryRepository;

    @Resource
    private MemberChangeRequestFormHistoryRepository memberChangeRequestFormHistoryRepository;

    @Resource
    private MemberRectifyOuterHistoryRepository memberRectifyOuterHistoryRepository;

    @Resource
    private MemberRectifyInnerHistoryRepository memberRectifyInnerHistoryRepository;


    /**
     * 查询外部单据流转记录
     *
     * @param relationDO 会员关系
     * @param roleTag    角色标签
     * @return 查询结果
     */
    @Override
    public List<MemberOuterHistoryResp> listMemberOuterHistory(MemberRelationDO relationDO, Integer roleTag) {
        return listMemberOuterHistory(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), roleTag);
    }

    /**
     * 查询外部单据流转记录
     *
     * @param memberId    上级会员Id
     * @param roleId      上级会角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param roleTag     角色标签
     * @return 查询结果
     */
    @Override
    public List<MemberOuterHistoryResp> listMemberOuterHistory(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Integer roleTag) {
        return memberValidateHistoryRepository.findAllByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(memberId, roleId, subMemberId, subRoleId, Sort.by("id").descending()).stream().map(h -> {
            MemberOuterHistoryResp historyVO = new MemberOuterHistoryResp();
            historyVO.setId(h.getId());
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setOuterStatus(h.getOuterStatus());
            historyVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(h.getOuterStatus()), roleTag));
            historyVO.setOperation(NumberUtil.isNullOrZero(h.getOperationCode()) ? h.getOperation() : MemberValidateHistoryOperationEnum.getMsgByCode(h.getOperationCode()));
            historyVO.setOperatorRoleName(h.getOperatorRoleName());
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询内部单据流转记录
     *
     * @param relationDO 会员关系
     * @param roleTag    角色标签
     * @return 查询结果
     */
    @Override
    public List<MemberInnerHistoryResp> listMemberInnerHistory(MemberRelationDO relationDO, Integer roleTag) {
        return listMemberInnerHistory(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), roleTag);
    }

    /**
     * 查询内部单据流转记录
     *
     * @param memberId    上级会员Id
     * @param roleId      上级会角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     * @param roleTag     角色标签
     * @return 查询结果
     */
    @Override
    public List<MemberInnerHistoryResp> listMemberInnerHistory(Long memberId, Long roleId, Long subMemberId, Long subRoleId, Integer roleTag) {
        return memberInnerValidateHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(memberId, roleId, subMemberId, subRoleId, Sort.by("id").descending()).stream().map(h -> {
            MemberInnerHistoryResp historyVO = new MemberInnerHistoryResp();
            historyVO.setId(h.getId());
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setOperatorName(h.getOperatorName());
            historyVO.setOperatorOrgName(h.getOperatorOrgName());
            historyVO.setOperatorJobTitle(h.getOperatorJobTitle());
            historyVO.setOperation(NumberUtil.isNullOrZero(h.getOperateCode()) ? h.getOperation() : MemberValidateHistoryOperationEnum.getMsgByCode(h.getOperateCode()));
            historyVO.setInnerStatus(h.getInnerStatus());
            String platformInnerStatusName = SecurityStringUtil.replaceMemberPrefix(PlatformInnerStatusEnum.getCodeMsg(h.getInnerStatus()), roleTag);
            String memberInnerStatusName = SecurityStringUtil.replaceMemberPrefix(MemberInnerStatusEnum.getCodeMsg(h.getInnerStatus()), roleTag);
            historyVO.setInnerStatusName(h.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode()) ? platformInnerStatusName : memberInnerStatusName);
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList());
    }

    /**
     * 保存会员内部审核历史记录
     *
     * @param relationDO    会员关系
     * @param loginUser     登录用户
     * @param operationEnum 操作类型枚举
     * @param remark        备注信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMemberInnerHistory(MemberRelationDO relationDO, UserLoginCacheDTO loginUser, MemberValidateHistoryOperationEnum operationEnum, String remark) {
        MemberInnerValidateHistoryDO historyDO = new MemberInnerValidateHistoryDO();
        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setRelType(relationDO.getRelType());
        historyDO.setMemberId(relationDO.getMemberId());
        historyDO.setRoleId(relationDO.getRoleId());
        historyDO.setMemberName(relationDO.getMember().getName());
        historyDO.setRoleName(relationDO.getRole().getRoleName());
        historyDO.setSubMemberId(relationDO.getSubMemberId());
        historyDO.setSubRoleId(relationDO.getSubRoleId());
        historyDO.setRelType(relationDO.getRelType());
        historyDO.setOperatorId(loginUser.getUserId());
        historyDO.setOperatorName(loginUser.getUserName());
        historyDO.setOperatorRoleName(loginUser.getUserRoleName());
        historyDO.setOperatorOrgName(loginUser.getOrgName());
        historyDO.setOperatorJobTitle(loginUser.getJobTitle());
        historyDO.setOperateCode(operationEnum.getCode());
        historyDO.setOperation(operationEnum.getMessage());
        historyDO.setInnerStatus(relationDO.getInnerStatus());
        historyDO.setOuterStatus(relationDO.getOuterStatus());
        historyDO.setStatus(relationDO.getStatus());
        historyDO.setRemark(StringUtils.hasLength(remark) ? remark : "");
        memberInnerValidateHistoryRepository.saveAndFlush(historyDO);
    }

    /**
     * 保存会员内部审核历史记录
     *
     * @param relationDO       会员关系
     * @param userId           用户Id
     * @param userName         用户姓名
     * @param userRoleName     用户角色名称
     * @param organizationName 用户组织机构名称
     * @param jobTitle         用户职位
     * @param operationEnum    操作类型枚举
     * @param remark           备注信息
     */
    @Override
    public void saveMemberInnerHistory(MemberRelationDO relationDO, Long userId, String userName, String userRoleName, String organizationName, String jobTitle, MemberValidateHistoryOperationEnum operationEnum, String remark) {
        MemberInnerValidateHistoryDO historyDO = new MemberInnerValidateHistoryDO();
        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setRelType(relationDO.getRelType());
        historyDO.setMemberId(relationDO.getMemberId());
        historyDO.setRoleId(relationDO.getRoleId());
        historyDO.setMemberName(relationDO.getMember().getName());
        historyDO.setRoleName(relationDO.getRole().getRoleName());
        historyDO.setSubMemberId(relationDO.getSubMemberId());
        historyDO.setSubRoleId(relationDO.getSubRoleId());
        historyDO.setRelType(relationDO.getRelType());
        historyDO.setOperatorId(userId);
        historyDO.setOperatorName(userName);
        historyDO.setOperatorRoleName(userRoleName);
        historyDO.setOperatorOrgName(organizationName);
        historyDO.setOperatorJobTitle(jobTitle);
        historyDO.setOperateCode(operationEnum.getCode());
        historyDO.setOperation(operationEnum.getMessage());
        historyDO.setInnerStatus(relationDO.getInnerStatus());
        historyDO.setOuterStatus(relationDO.getOuterStatus());
        historyDO.setStatus(relationDO.getStatus());
        historyDO.setRemark(StringUtils.hasLength(remark) ? remark : "");
        memberInnerValidateHistoryRepository.saveAndFlush(historyDO);
    }

    /**
     * 保存会员外部审核历史记录
     *
     * @param relationDO    会员关系
     * @param operationEnum 操作方法枚举
     * @param remark        备注信息
     */
    @Override
    public void saveMemberOuterHistory(MemberRelationDO relationDO, MemberValidateHistoryOperationEnum operationEnum, String remark) {
        AopProxyUtil.getCurrentProxy(this.getClass()).saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), operationEnum, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), remark);
    }

    /**
     * 保存会员外部审核历史记录
     *
     * @param relationDO      会员关系
     * @param operationEnum   操作方法枚举
     * @param outerStatusName 指定状态名称
     * @param remark          备注信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMemberOuterHistory(MemberRelationDO relationDO, String operatorRoleName, MemberValidateHistoryOperationEnum operationEnum, String outerStatusName, String remark) {
        MemberValidateHistoryDO historyDO = new MemberValidateHistoryDO();
        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setMemberId(relationDO.getMemberId());
        historyDO.setMemberName(relationDO.getMember().getName());
        historyDO.setRoleId(relationDO.getRoleId());
        historyDO.setRoleName(relationDO.getRole().getRoleName());
        historyDO.setSubMemberId(relationDO.getSubMemberId());
        historyDO.setSubMemberName(relationDO.getSubMember().getName());
        historyDO.setRelType(relationDO.getRelType());
        historyDO.setSubRoleId(relationDO.getSubRoleId());
        historyDO.setSubRoleName(relationDO.getSubRoleName());
        historyDO.setOuterStatus(relationDO.getOuterStatus());
        historyDO.setOperatorRoleName(operatorRoleName);
        historyDO.setOperationCode(operationEnum.getCode());
        historyDO.setOperation(operationEnum.getMessage());
        historyDO.setRemark(StringUtils.hasLength(remark) ? remark.trim() : "");
        memberValidateHistoryRepository.saveAndFlush(historyDO);
    }

    /**
     * 分页查询会员入库资料历史记录
     *
     * @param relationDO 会员关系
     * @param current    当前页
     * @param pageSize   每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberDepositDetailHistoryResp> pageMemberDepositDetailHistory(MemberRelationDO relationDO, int current, int pageSize) {
        Pageable pageable = PageRequest.of(current - 1, pageSize, Sort.by("id").descending());
        Page<MemberDepositoryDetailHistoryDO> pageList = memberDepositoryDetailHistoryRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(historyDO -> {
            MemberDepositDetailHistoryResp historyVO = new MemberDepositDetailHistoryResp();
            historyVO.setId(historyDO.getId());
            historyVO.setCreateTime(historyDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            historyVO.setFieldLocalName(historyDO.getFieldLocalName());
            historyVO.setFieldValue(historyDO.getFieldValue());
            historyVO.setLastValue(historyDO.getLastValue());
            historyVO.setFieldType(historyDO.getFieldType());
            if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(historyDO.getFieldType())) {
                String fieldValue = Optional.ofNullable(historyDO.getMemberDepositoryDetailListHistoryDO()).map(MemberDepositoryDetailListHistoryDO::getFieldValue).orElse("[]");
                String lastValue = Optional.ofNullable(historyDO.getMemberDepositoryDetailListHistoryDO()).map(MemberDepositoryDetailListHistoryDO::getLastValue).orElse("[]");
                historyVO.setFieldValue(JSONObject.parseObject(fieldValue, new TypeReference<List<List<DetailTextResp>>>(){}));
                historyVO.setLastValue(JSONObject.parseObject(lastValue, new TypeReference<List<List<DetailTextResp>>>(){}));
            }
            return historyVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 更改内部、外部历史记录的下级会员Id
     *
     * @param subMemberId      下级会员Id
     * @param oldSubRoleId     旧的下级会员角色Id
     * @param newSubRoleId     新的下级会员角色Id
     * @param newSubRoleName   新的下级会员角色名称
     * @param newSubMemberName 新的下级会员名称
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateHistorySubRoleId(Long subMemberId, Long oldSubRoleId, Long newSubRoleId, String newSubRoleName, String newSubMemberName) {
        //更改内部审核记录
        List<MemberInnerValidateHistoryDO> innerHistory = memberInnerValidateHistoryRepository.findBySubMemberIdAndSubRoleId(subMemberId, oldSubRoleId);
        if (!CollectionUtils.isEmpty(innerHistory)) {
            innerHistory.forEach(h -> h.setSubRoleId(newSubRoleId));
            memberInnerValidateHistoryRepository.saveAll(innerHistory);
        }

        //更改外部审核记录
        List<MemberValidateHistoryDO> outerHistory = memberValidateHistoryRepository.findBySubMemberIdAndSubRoleId(subMemberId, oldSubRoleId);
        if (!CollectionUtils.isEmpty(outerHistory)) {
            outerHistory.forEach(h -> {
                h.setSubMemberName(newSubMemberName);
                h.setSubRoleId(newSubRoleId);
                h.setSubRoleName(newSubRoleName);
            });
            memberValidateHistoryRepository.saveAll(outerHistory);
        }
    }

    /**
     * 删除下级会员的内、外历史记录
     *
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void deleteHistoryBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId) {
        memberValidateHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(subMemberId, subRoleId);
        memberInnerValidateHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(subMemberId, subRoleId);
    }

    /**
     * 删除会员关系时，删除下级会员的内、外历史记录
     *
     * @param memberId    上级会员Id
     * @param roleId      上级会员角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void deleteHistoryByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId) {
        memberValidateHistoryRepository.deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(memberId, roleId, subMemberId, subRoleId);
        memberInnerValidateHistoryRepository.deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(memberId, roleId, subMemberId, subRoleId);
    }

    /**
     * 保存会员考评历史记录
     *
     * @param loginUser   登录用户
     * @param appraisalId 考评id
     * @param status      状态
     * @param statusName  状态名称
     * @param operation   操作说明
     * @param remark      备注信息
     */
    @Override
    public void saveMemberAppraisalHistory(UserLoginCacheDTO loginUser, Long appraisalId, Integer status, String statusName, String operation, String remark) {
        MemberAppraisalHistoryDO historyDO = new MemberAppraisalHistoryDO();
        historyDO.setAppraisalId(appraisalId);
        historyDO.setOperatorName(loginUser.getUserName());
        historyDO.setOperatorRoleName(loginUser.getUserRoleName());
        historyDO.setOperatorOrgName(loginUser.getOrgName());
        historyDO.setOperatorJobTitle(loginUser.getJobTitle());
        historyDO.setOperation(operation);
        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setRemark(remark);
        historyDO.setOperation(operation);
        historyDO.setStatus(status);
        historyDO.setStatusName(statusName);
        historyDO.setRemark(StringUtils.hasLength(remark) ? remark.trim() : "");

        memberAppraisalHistoryRepository.saveAndFlush(historyDO);
    }

    /**
     * 查询会员考评单据流转记录
     *
     * @param appraisalId 考评id
     * @return 查询结果
     */
    @Override
    public List<MemberAppraisalHistoryResp> listMemberAppraisalHistory(Long appraisalId) {
        return memberAppraisalHistoryRepository.findByAppraisalId(appraisalId, Sort.by("id").descending()).stream().map(h -> {
            MemberAppraisalHistoryResp historyVO = new MemberAppraisalHistoryResp();
            historyVO.setId(h.getId());
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setOperatorName(h.getOperatorName());
            historyVO.setOperatorOrgName(h.getOperatorOrgName());
            historyVO.setOperatorJobTitle(h.getOperatorJobTitle());
            historyVO.setOperation(h.getOperation());
            historyVO.setStatus(h.getStatus());
            historyVO.setStatusName(h.getStatusName());
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void saveMemberRectifyOuterHistory(UserLoginCacheDTO loginUser, Long rectifyId, Long subMemberId, Long subRoleId, Integer outerStatus, String operation, String remark) {
        MemberRectifyOuterHistoryDO historyDO = new MemberRectifyOuterHistoryDO();
        historyDO.setRectifyId(rectifyId);
        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setMemberId(subMemberId);
        historyDO.setMemberName(loginUser.getMemberName());
        historyDO.setRoleId(subRoleId);
        historyDO.setRoleName(loginUser.getMemberRoleName());
        historyDO.setSubMemberId(subMemberId);
        historyDO.setSubRoleId(subRoleId);
        historyDO.setInnerStatus(outerStatus);
        historyDO.setOuterStatus(outerStatus);
        historyDO.setInnerStatusName(MemberRectifyStatusEnum.getCodeMessage(outerStatus));
        historyDO.setOuterStatusName(MemberRectifyStatusEnum.getCodeMessage(outerStatus));

        historyDO.setOperation(operation);
        historyDO.setRemark(StringUtils.hasLength(remark) ? remark.trim() : "");

        memberRectifyOuterHistoryRepository.saveAndFlush(historyDO);
    }

    @Override
    public void saveMemberRectifyInnerHistory(UserLoginCacheDTO loginUser, Long rectifyId, Long subMemberId, Long subRoleId, Integer outerStatus, String operation, String remark) {
        MemberRectifyInnerHistoryDO historyDO = new MemberRectifyInnerHistoryDO();
        historyDO.setRectifyId(rectifyId);
        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setMemberId(loginUser.getMemberId());
        historyDO.setMemberName(loginUser.getMemberName());
        historyDO.setRoleId(loginUser.getMemberRoleId());
        historyDO.setRoleName(loginUser.getMemberRoleName());
        historyDO.setSubMemberId(subMemberId);
        historyDO.setSubRoleId(subRoleId);
        historyDO.setOperatorName(loginUser.getUserName());
        historyDO.setOperatorRoleName(loginUser.getUserRoleName());
        historyDO.setOperatorOrgName(loginUser.getOrgName());
        historyDO.setOperatorJobTitle(loginUser.getJobTitle());
        historyDO.setInnerStatus(outerStatus);
        historyDO.setOuterStatus(outerStatus);
        historyDO.setInnerStatusName(MemberRectifyStatusEnum.getCodeMessage(outerStatus));
        historyDO.setOuterStatusName(MemberRectifyStatusEnum.getCodeMessage(outerStatus));
        historyDO.setOperation(operation);
        historyDO.setRemark(StringUtils.hasLength(remark) ? remark.trim() : "");
        memberRectifyInnerHistoryRepository.saveAndFlush(historyDO);
    }

    @Override
    public List<MemberRectifyOuterHistoryResp> listMemberRectifyOuterHistory(Long rectifyId) {
        return memberRectifyOuterHistoryRepository.findByRectifyId(rectifyId, Sort.by("id").descending()).stream().map(h -> {
            MemberRectifyOuterHistoryResp historyVO = new MemberRectifyOuterHistoryResp();
            historyVO.setId(h.getId());
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setOuterStatus(h.getOuterStatus());
            historyVO.setOuterStatusName(h.getOuterStatusName());
            historyVO.setOperation(h.getOperation());
            historyVO.setOperatorRoleName(h.getRoleName());
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<MemberRectifyInnerHistoryResp> listMemberRectifyInnerHistory(Long rectifyId) {
        return memberRectifyInnerHistoryRepository.findByRectifyId(rectifyId, Sort.by("id").descending()).stream().map(h -> {
            MemberRectifyInnerHistoryResp historyVO = new MemberRectifyInnerHistoryResp();
            historyVO.setId(h.getId());
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setOperatorName(h.getOperatorName());
            historyVO.setOperatorOrgName(h.getOperatorOrgName());
            historyVO.setOperatorJobTitle(h.getOperatorJobTitle());
            historyVO.setOperation(h.getOperation());
            historyVO.setInnerStatus(h.getInnerStatus());
            historyVO.setInnerStatusName(h.getInnerStatusName());
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<MemberChangeRequestFormHistoryResp> listMemberChangeRequestFormHistory(Long changeRequestFormId) {
        return memberChangeRequestFormHistoryRepository.findByChangeRequestFormId(changeRequestFormId, Sort.by("id").descending()).stream().map(h -> {
            MemberChangeRequestFormHistoryResp historyVO = new MemberChangeRequestFormHistoryResp();
            historyVO.setId(h.getId());
            historyVO.setCreateTime(h.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            historyVO.setOperatorName(h.getOperatorName());
            historyVO.setOperatorOrgName(h.getOperatorOrgName());
            historyVO.setOperatorJobTitle(h.getOperatorJobTitle());
            historyVO.setOperation(h.getOperation());
            historyVO.setStatus(h.getStatus());
            historyVO.setStatusName(h.getStatusName());
            historyVO.setRemark(h.getRemark());
            return historyVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void saveMemberChangeRequestFormHistory(UserLoginCacheDTO loginUser, Long appraisalId, Integer status, String statusName, String operation, String remark) {
        MemberChangeRequestFormHistoryDO historyDO = new MemberChangeRequestFormHistoryDO();
        historyDO.setChangeRequestFormId(appraisalId);
        historyDO.setOperatorName(loginUser.getUserName());
        historyDO.setOperatorRoleName(loginUser.getUserRoleName());
        historyDO.setOperatorOrgName(loginUser.getOrgName());
        historyDO.setOperatorJobTitle(loginUser.getJobTitle());
        historyDO.setOperation(operation);
        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setRemark(remark);
        historyDO.setOperation(operation);
        historyDO.setStatus(status);
        historyDO.setStatusName(statusName);
        historyDO.setRemark(StringUtils.hasLength(remark) ? remark.trim() : "");

        memberChangeRequestFormHistoryRepository.saveAndFlush(historyDO);
    }
}
