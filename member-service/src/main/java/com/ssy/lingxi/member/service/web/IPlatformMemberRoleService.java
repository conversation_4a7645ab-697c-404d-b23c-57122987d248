package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberRoleGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberRoleQueryResp;
import org.springframework.http.HttpHeaders;

/**
 * 平台后台 - 会员自定义角色服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-30
 */
public interface IPlatformMemberRoleService {
    /**
     * 分页、模糊查询用户角色
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<PlatformMemberRoleQueryResp> pageMemberRole(HttpHeaders headers, MemberRolePageDataReq pageVO);

    /**
     * 查询单个用户角色的信息
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    PlatformMemberRoleGetResp getMemberRole(HttpHeaders headers, UserRoleIdReq idVO);

    /**
     * 新增用户角色
     * @param headers Http头部信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void addMemberRole(HttpHeaders headers, PlatformMemberRoleAddReq addVO);

    /**
     * 更新用户角色
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateMemberRole(HttpHeaders headers, PlatformMemberRoleUpdateReq updateVO);

    /**
     * 删除用户角色
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    void deleteMemberRole(HttpHeaders headers, UserRoleIdReq idVO);

    /**
     * 更新用户角色状态
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    void updateMemberRoleStatus(HttpHeaders headers, MemberRoleUpdateStatusReq statusVO);

    /**
     * 获取会员菜单按钮权限
     *
     * @param headers HttpHeaders信息
     * @param authTreeReq 接口参数
     * @return 操作结果
     */
    AuthTreeResp getMemberAuthTree(HttpHeaders headers, UserRoleAuthTreeReq authTreeReq);
}
