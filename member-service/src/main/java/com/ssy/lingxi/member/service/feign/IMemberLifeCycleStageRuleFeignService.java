package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.member.api.model.req.MemberFeignWithLifeCycleRuleReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleRuleCheckReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleStagesRuleCheckBatchFeignReq;
import com.ssy.lingxi.member.api.model.req.MemberLifeCycleStagesRuleCheckFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLifeCycleRuleResp;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/30 9:39
 */
public interface IMemberLifeCycleStageRuleFeignService {

    /**
     * 校验是否有配置会员某生命周期阶段状态,没有配置生命周期做限制
     *
     * @param memberLifeCycleStagesRuleCheckVO 接口参数
     * @return 返回
     */
    Boolean checkMemberLifeCycleStagesRule(MemberLifeCycleStagesRuleCheckFeignReq memberLifeCycleStagesRuleCheckVO);

    /**
     * 根据上级会员和生命周期规则ID查询该规则下所有下级会员
     *
     * @param cycleRuleVO 请求对象
     * @return 返回MemberFeignLifeCycleRuleVO对象
     */
    List<MemberFeignLifeCycleRuleResp> getSubMemberWithLifecycleRule(MemberFeignWithLifeCycleRuleReq cycleRuleVO);

    /**
     * 校验会员是否配置某生命周期规则阶段
     *
     * @param memberLifeCycleRuleCheckReq 入参
     * @return 返回结果
     */
    Boolean checkMemberHasConfigureLifeCycleRule(MemberLifeCycleRuleCheckReq memberLifeCycleRuleCheckReq);

    /**
     * 批量校验是否允许创建请购订单合同
     *
     * @param memberLifeCycleStagesRuleCheckBatchFeignReq 入参
     * @return 返回结果
     */
    List<String> checkBatchMemberLifeCycleStagesRule(MemberLifeCycleStagesRuleCheckBatchFeignReq memberLifeCycleStagesRuleCheckBatchFeignReq);

    /**
     * 校验是否有配置会员某生命周期阶段状态,没有配置生命周期不做限制
     *
     * @param memberLifeCycleStagesRuleCheckVO 入参
     * @return 返回结果
     */
    Boolean checkMemberLifeCycleStagesRuleOrNotAstrict(MemberLifeCycleStagesRuleCheckFeignReq memberLifeCycleStagesRuleCheckVO);
}
