package com.ssy.lingxi.member.controller.web.supplier;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.dataauth.annotation.member.MemberAuth;
import com.ssy.lingxi.member.model.req.basic.ProvinceCodeReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.validate.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilityDepositService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商能力 - 会员入库审核相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-25
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/supplier/deposit")
public class SupplierAbilityDepositController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.SUPPLIER.getCode();

    @Resource
    private IMemberAbilityDepositService memberAbilityDepositService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    /**
     * 获取会员审核入库各个步骤分页查询列表页面下拉框
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/page/conditions")
    public WrapperResp<MemberDepositSearchConditionResp> getDepositPageConditions(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.getDepositPageConditions(loginUser, roleTag));
    }

    /**
     * 分页查询“待审核入库资料”会员列表
     * @param headers Http头部新
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/verify/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToVerifyDepositoryDetail(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToVerifyDepositoryDetail(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核入库资料” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/verify/detail")
    public WrapperResp<MemberToVerifyDepositDetailResp> getToVerifyDepositoryDetail(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToVerifyDepositoryDetail(loginUser, idVO, roleTag));
    }

    /**
     * “待审核入库资料” - 审核会员
     * @param headers Http头部信息
     * @param depositVO 接口参数
     * @return 审核结果
     */
    @PostMapping("/verify")
    public WrapperResp<Void> toVerifyDepositoryDetail(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberToVerifyDepositReq depositVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityDepositService.toVerifyDepositoryDetail(loginUser, depositVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待审核入库资料” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/verify/batch")
    public WrapperResp<Void> batchVerifyDepositoryDetail(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateIdsReq idVO) {
        memberAbilityDepositService.batchVerifyDepositoryDetail(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 分页查询“待审核入库资质”会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/qualify/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToVerifyDepositoryQualification(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToVerifyDepositoryQualification(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核入库资质” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/qualify/detail")
    public WrapperResp<MemberDepositDetailResp> getToVerifyDepositoryQualification(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToVerifyDepositoryQualification(loginUser, idVO, roleTag));
    }

    /**
     * “待审核入库资质” - 审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/qualify")
    public WrapperResp<Void> toVerifyDepositoryQualification(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateAgreeReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityDepositService.toVerifyDepositoryQualification(loginUser, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待审核入库资质” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/qualify/batch")
    public WrapperResp<Void> batchVerifyDepositoryQualifications(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateIdsReq idVO) {
        memberAbilityDepositService.batchVerifyDepositoryQualifications(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 分页查询“待入库考察”会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/inspect/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToInspectDepository(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToInspectDepository(loginUser, pageVO, roleTag));
    }

    /**
     * “待入库考察” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/inspect/detail")
    public WrapperResp<MemberDepositDetailResp> getToInspectDepository(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToInspectDepository(loginUser, idVO, roleTag));
    }

    /**
     * “待入库考察” - 审核
     * @param headers Http头部信息
     * @param depositVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/inspect")
    public WrapperResp<Void> toInspectDepository(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberToInspectDepositReq depositVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityDepositService.toInspectDepository(loginUser, depositVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 分页查询“待入库分类”会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/classify/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToClassifyDepository(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToClassifyDepository(loginUser, pageVO, roleTag));
    }

    /**
     * “待入库分类” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/classify/detail")
    public WrapperResp<MemberToClassifyDetailResp> getToClassifyDepository(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToClassifyDepository(loginUser, idVO, roleTag));
    }

    /**
     * “待入库分类” - “适用区域”-省列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/classify/province")
    public WrapperResp<List<AreaCodeNameResp>> getToClassifyProvinces(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityDepositService.getToClassifyProvinces(headers));
    }

    /**
     * “待入库分类” - “适用区域”-根据省编码查询市列表
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/classify/city")
    public WrapperResp<List<AreaCodeNameResp>> getToClassifyCities(@RequestHeader HttpHeaders headers, @Valid ProvinceCodeReq codeVO) {
        return WrapperUtil.success(memberAbilityDepositService.getToClassifyCities(headers, codeVO));
    }

    /**
     * “待入库分类” - 品类信息 - 查询结算方式，发票类型，币别，结算单据，付款方式
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/classify/category/items")
    public WrapperResp<MemberClassifyCategoryItemResp> getToClassifyCategoryItems(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberAbilityDepositService.getToClassifyCategoryItems(headers));
    }

    /**
     * “待入库分类” - 审核
     * @param headers Http头部信息
     * @param depositVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/classify")
    public WrapperResp<Void> toClassifyDepository(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberToClassifyDepositReq depositVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityDepositService.toClassifyDepository(loginUser, depositVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 分页查询“待审核入库(一级)”会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/grade/one/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToDepositGradeOne(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToDepositGradeOne(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核入库(一级)” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/grade/one/detail")
    public WrapperResp<MemberDepositGradeDetailResp> getToDepositGradeOne(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToDepositGradeOne(loginUser, idVO, roleTag));
    }

    /**
     * “待审核入库(一级)” - 审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/grade/one")
    public WrapperResp<Void> toDepositGradeOne(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateAgreeReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityDepositService.toDepositGradeOne(loginUser, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待审核入库(一级)” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/grade/one/batch")
    public WrapperResp<Void> batchDepositGradeOne(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateIdsReq idVO) {
        memberAbilityDepositService.batchDepositGradeOne(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 分页查询“待审核入库(二级)”会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/grade/two/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToDepositGradeTwo(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToDepositGradeTwo(loginUser, pageVO, roleTag));
    }

    /**
     * “待审核入库(二级)” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/grade/two/detail")
    public WrapperResp<MemberDepositGradeDetailResp> getToDepositGradeTwo(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToDepositGradeTwo(loginUser, idVO, roleTag));
    }

    /**
     * “待审核入库(二级)” - 审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/grade/two")
    public WrapperResp<Void> toDepositGradeTwo(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateAgreeReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityDepositService.toDepositGradeTwo(loginUser, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待审核入库(二级)” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/grade/two/batch")
    public WrapperResp<Void> batchDepositGradeTwo(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateIdsReq idVO) {
        memberAbilityDepositService.batchDepositGradeTwo(headers, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 分页查询“待确认入库”会员列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @MemberAuth
    @GetMapping("/confirm/page")
    public WrapperResp<PageDataResp<MemberDepositPageQueryResp>> pageToConfirmDepository(@RequestHeader HttpHeaders headers, @Valid MemberDepositPageDataReq pageVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.pageToConfirmDepository(loginUser, pageVO, roleTag));
    }

    /**
     * “待确认入库” - 查询会员详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/confirm/detail")
    public WrapperResp<MemberDepositGradeDetailResp> getToConfirmDepository(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        return WrapperUtil.success(memberAbilityDepositService.getToConfirmDepository(loginUser, idVO, roleTag));
    }

    /**
     * “待确认入库” - 审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/confirm")
    public WrapperResp<Void> toConfirmDepository(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateAgreeReq idVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberAbilityDepositService.toConfirmDepository(loginUser, idVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * “待确认入库” - 批量审核
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/confirm/batch")
    public WrapperResp<Void> batchConfirmDepositories(@RequestHeader HttpHeaders headers, @RequestBody @Valid ValidateIdsReq idVO) {
        memberAbilityDepositService.batchConfirmDepositories(headers, idVO, roleTag);
        return WrapperUtil.success();
    }


}
