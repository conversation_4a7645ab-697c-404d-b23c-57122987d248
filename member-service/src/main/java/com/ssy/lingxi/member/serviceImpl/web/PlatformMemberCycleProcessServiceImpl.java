package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.req.engine.ProcessEngineReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.engine.api.enums.ProcessDefaultEnum;
import com.ssy.lingxi.engine.api.enums.ProcessSourceEnum;
import com.ssy.lingxi.member.api.enums.MemberCycleProcessTypeEnum;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignPageQueryResp;
import com.ssy.lingxi.member.entity.do_.lifecycle.BaseMemberCycleProcessDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberCycleProcessDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.PlatformMemberCycleProcessDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.PlatformMemberCycleProcessMemberDO;
import com.ssy.lingxi.member.enums.MemberCycleProcessEnum;
import com.ssy.lingxi.member.model.req.platform.*;
import com.ssy.lingxi.member.model.resp.platform.BaseMemberCycleProcessResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformMemberCycleProcessDetailResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformMemberCycleProcessMemberResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformMemberCycleProcessPageResp;
import com.ssy.lingxi.member.repository.BaseMemberCycleProcessRepository;
import com.ssy.lingxi.member.repository.MemberCycleProcessRepository;
import com.ssy.lingxi.member.repository.PlatformMemberCycleProcessMemberRepository;
import com.ssy.lingxi.member.repository.PlatformMemberCycleProcessRepository;
import com.ssy.lingxi.member.service.feign.IMemberFeignService;
import com.ssy.lingxi.member.service.web.IMemberCycleProcessService;
import com.ssy.lingxi.member.service.web.IPlatformMemberCycleProcessService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台会员生命周期变更流程服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@Service
public class PlatformMemberCycleProcessServiceImpl implements IPlatformMemberCycleProcessService {

    @Resource
    private PlatformMemberCycleProcessRepository platformMemberCycleProcessRepository;

    @Resource
    private BaseMemberCycleProcessRepository baseMemberCycleProcessRepository;

    @Resource
    private PlatformMemberCycleProcessMemberRepository platformMemberCycleProcessMemberRepository;

    @Resource
    private IMemberFeignService iMemberFeignService;

    @Resource
    private MemberCycleProcessRepository memberCycleProcessRepository;

    @Resource
    private IMemberCycleProcessService iMemberCycleProcessService;

    @Override
    public void saveBaseProcess(ProcessEngineReq engineBO) {

        // 查询基础工作流
        BaseMemberCycleProcessDO baseProcess = baseMemberCycleProcessRepository.findByProcessKeyAndProcessType(engineBO.getProcessKey(), engineBO.getProcessType()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_PROCESS_INVALID));

        // 基础工作流关联流程引擎
        baseProcess.setEngineId(engineBO.getEngineId());
        baseProcess.setProcessName(engineBO.getProcessName());
        baseProcess.setIsDefault(engineBO.getIsDefault());
        baseProcess.setProcessImage(engineBO.getProcessImage());
        baseProcess.setDescription(engineBO.getDescription());
        baseMemberCycleProcessRepository.saveAndFlush(baseProcess);

        // 创建平台默认工作流
        this.createDefaultProcess(baseProcess, engineBO);

        // 创建能力中心默认工作流
        iMemberCycleProcessService.saveDefaultProcess(baseProcess, engineBO);

    }

    /**
     * 创建平台默认工作流
     * @param baseProcess   基础流程
     * @param engineBO      流程引擎
     */
    private void createDefaultProcess(BaseMemberCycleProcessDO baseProcess, ProcessEngineReq engineBO){

        // 查询当前类型的默认流程
        PlatformMemberCycleProcessDO defaultProcess = platformMemberCycleProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode());

        // 不存在则创建默认流程
        if (Objects.equals(engineBO.getIsDefault(), ProcessDefaultEnum.YES.getCode()) && Objects.isNull(defaultProcess)){
            createDefault(baseProcess);
            return;
        }

        // 非默认流程不处理
        if (Objects.isNull(defaultProcess)){
            return;
        }

        // 取消默认则删除
        if (Objects.equals(ProcessDefaultEnum.NO.getCode(), engineBO.getIsDefault())){
            platformMemberCycleProcessRepository.delete(defaultProcess);
            return;
        }

        // 更新默认工作流
        defaultProcess.setEngineId(baseProcess.getEngineId());
        defaultProcess.setName(baseProcess.getProcessName());
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        defaultProcess.setProcess(baseProcess);
        platformMemberCycleProcessRepository.save(defaultProcess);
    }

    /**
     * 创建默认工作流
     * @param baseProcess   基础流程
     */
    private void createDefault(BaseMemberCycleProcessDO baseProcess) {
        PlatformMemberCycleProcessDO defaultProcess = new PlatformMemberCycleProcessDO();
        defaultProcess.setEngineId(baseProcess.getEngineId());
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        defaultProcess.setName(baseProcess.getProcessName());
        defaultProcess.setMembers(new HashSet<>());
        defaultProcess.setAllMembers(Boolean.TRUE);
        defaultProcess.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        defaultProcess.setCreateTime(LocalDateTime.now());
        defaultProcess.setSource(ProcessSourceEnum.PAAS.getCode());
        defaultProcess.setIsDefault(ProcessDefaultEnum.YES.getCode());
        defaultProcess.setProcess(baseProcess);
        defaultProcess.setProcessType(baseProcess.getProcessType());
        platformMemberCycleProcessRepository.saveAndFlush(defaultProcess);
    }

    @Override
    public List<ProcessEngineRuleResp> getMemberProcess(EngineRuleQueryReq engineRuleQueryReq) {

        // 查询默认流程
        List<MemberCycleProcessDO> memberProcessList = iMemberCycleProcessService.getMemberProcess(engineRuleQueryReq.getMemberId(), engineRuleQueryReq.getMemberRoleId(), engineRuleQueryReq.getType());

        if (memberProcessList.isEmpty()) {
            return new ArrayList<>();
        }

        // 组装数据
        return memberProcessList.stream().map(memberProcess -> {
            ProcessEngineRuleResp engineRuleVO = new ProcessEngineRuleResp();
            engineRuleVO.setProcessRuleId(memberProcess.getId());
            engineRuleVO.setIsDefault(memberProcess.getIsDefault());
            engineRuleVO.setProcessKey(memberProcess.getProcessKey());
            return engineRuleVO;
        }).collect(Collectors.toList());

    }

    @Override
    public PageDataResp<PlatformMemberCycleProcessPageResp> pageProcess(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessPageQueryDataReq pageVO) {

        // 查询平台默认
        List<Integer> types = platformMemberCycleProcessRepository.findByIsDefaultAndSource(ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.SYSTEM.getCode()).stream().map(PlatformMemberCycleProcessDO::getProcessType).collect(Collectors.toList());

        // 平台未存在的默认流程类型
        List<Integer> typeList = Arrays.stream(MemberCycleProcessTypeEnum.values()).map(MemberCycleProcessTypeEnum::getCode).filter(code -> !types.contains(code)).collect(Collectors.toList());

        // 构建查询条件
        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("isDefault", "createTime").descending());

        // 查询条件
        Specification<PlatformMemberCycleProcessDO> spec = (root, query, builder) -> {

            // 平台流程
            List<Predicate> cond = new ArrayList<>();
            cond.add(builder.equal(root.get("source"), ProcessSourceEnum.SYSTEM.getCode()));
            if (StringUtils.hasText(pageVO.getName())) {
                cond.add(builder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }

            if (typeList.isEmpty()){
                return builder.and(cond.toArray(new Predicate[0]));
            }

            // 系统默认
            List<Predicate> orList = new ArrayList<>();
            orList.add(builder.equal(root.get("source"), ProcessSourceEnum.PAAS.getCode()));
            orList.add(builder.in(root.get("processType")).value(typeList));
            if (StringUtils.hasText(pageVO.getName())) {
                orList.add(builder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }

            // 组合查询
            return builder.or( builder.and(cond.toArray(new Predicate[0])), builder.and(orList.toArray(new Predicate[0])));
        };

        // 查询数据
        Page<PlatformMemberCycleProcessDO> pageList = platformMemberCycleProcessRepository.findAll(spec, pageable);

        // 组装数据
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(this::createProcessPageVO).collect(Collectors.toList()));
    }

    /**
     * 创建VO类
     * @param entity 流程
     * @return VO类
     */
    private PlatformMemberCycleProcessPageResp createProcessPageVO(PlatformMemberCycleProcessDO entity){
        PlatformMemberCycleProcessPageResp queryVO = new PlatformMemberCycleProcessPageResp();
        queryVO.setProcessId(entity.getId());
        queryVO.setCreateTime(entity.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        if (Objects.equals(ProcessDefaultEnum.YES.getCode(), entity.getIsDefault())){
            queryVO.setName(Optional.ofNullable(entity.getProcess().getProcessName()).orElse(entity.getProcess().getName()));
        }else {
            queryVO.setName(entity.getName());
        }
        queryVO.setStatus(entity.getStatus());
        queryVO.setStatusName(EnableDisableStatusEnum.getNameByCode(entity.getStatus()));
        queryVO.setProcessName(Optional.ofNullable(entity.getProcess().getProcessName()).orElse(entity.getProcess().getName()));
        queryVO.setIsDefault(Optional.ofNullable(entity.getIsDefault()).orElse(0));
        queryVO.setProcessType(entity.getProcessType());
        queryVO.setBaseProcess(builderBaseMemberCycleProcessVO(entity.getProcess()));
        return queryVO;
    }

    @Override
    public List<BaseMemberCycleProcessResp> listBaseProcess(UserLoginCacheDTO loginUser, ProcessQueryReq queryRequest) {
        List<BaseMemberCycleProcessResp> result = baseMemberCycleProcessRepository.findAll(Sort.by("id").ascending()).stream().map(this::builderBaseMemberCycleProcessVO).collect(Collectors.toList());
        if (Objects.nonNull(queryRequest.getProcessType())){
            return result.stream().filter(value -> Objects.equals(value.getProcessType(), queryRequest.getProcessType())).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public void saveDefault(SaveDefaultReq defaultRequest) {

        // 查询当前规则
        BaseMemberCycleProcessDO baseProcess = baseMemberCycleProcessRepository.findById(defaultRequest.getProcessId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_PROCESS_INVALID));

        // 查询默认流程
        PlatformMemberCycleProcessDO defaultProcess = platformMemberCycleProcessRepository.findByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.SYSTEM.getCode()).stream().findFirst().orElse(null);

        if (Objects.isNull(defaultProcess)){

            // 系统默认流程
            PlatformMemberCycleProcessDO defaultProcessPaas = Optional.ofNullable(platformMemberCycleProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_DEFAULT_PROCESS_NON_EXISTENT));

            // 保存默认流程
            defaultProcess = BeanUtil.copyProperties(defaultProcessPaas, PlatformMemberCycleProcessDO.class);
            defaultProcess.setId(null);
            defaultProcess.setProcessKey(baseProcess.getProcessKey());
            defaultProcess.setName(Optional.ofNullable(baseProcess.getProcessName()).orElse(baseProcess.getName()));
            defaultProcess.setProcess(baseProcess);
            defaultProcess.setSource(ProcessSourceEnum.SYSTEM.getCode());
            platformMemberCycleProcessRepository.save(defaultProcess);

        } else {

            // 更新默认流程
            defaultProcess.setProcessKey(baseProcess.getProcessKey());
            defaultProcess.setName(Optional.ofNullable(baseProcess.getProcessName()).orElse(baseProcess.getName()));
            defaultProcess.setProcessType(baseProcess.getProcessType());
            defaultProcess.setProcess(baseProcess);
            platformMemberCycleProcessRepository.save(defaultProcess);
        }

        // 设置能力中心默认流程
        iMemberCycleProcessService.defaultProcess(defaultProcess);


    }

    /**
     * 创建基础流程展示类
     * @param process 基础流程
     * @return 基础流程展示类
     */
    private BaseMemberCycleProcessResp builderBaseMemberCycleProcessVO(BaseMemberCycleProcessDO process) {
        BaseMemberCycleProcessResp processVO = new BaseMemberCycleProcessResp();
        processVO.setBaseProcessId(process.getId());
        processVO.setProcessName(Optional.ofNullable(process.getProcessName()).orElse(MemberCycleProcessEnum.getNameByCode(process.getCode())));
        processVO.setProcessType(process.getProcessType());
        processVO.setProcessTypeName(MemberCycleProcessTypeEnum.getMessage(process.getProcessType()));
        processVO.setDescription(Optional.ofNullable(process.getDescription()).orElse(MemberCycleProcessEnum.getRemarkByCode(process.getCode())));
        processVO.setProcessImage(process.getProcessImage());
        return processVO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessReq saveVO) {
        // step 1:校验
        WrapperResp<BaseMemberCycleProcessDO> checkWrapperResp = checkSave(saveVO);
        if (checkWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            return ;
        }
        BaseMemberCycleProcessDO baseProcess = checkWrapperResp.getData();
        // step 2:组装数据
        PlatformMemberCycleProcessDO entity = builderEntity(saveVO, baseProcess);
        // step 3:保存
        platformMemberCycleProcessRepository.saveAndFlush(entity);

    }

    /**
     * 根据新增数据构建保存数据库数据
     * @param saveVO 前端传输数据
     * @param baseProcess 基础流程
     * @return 保存数据库的格式
     */
    private PlatformMemberCycleProcessDO builderEntity(PlatformMemberCycleProcessReq saveVO, BaseMemberCycleProcessDO baseProcess) {
        PlatformMemberCycleProcessDO entity = new PlatformMemberCycleProcessDO();
        entity.setName(saveVO.getName());
        entity.setProcessKey(baseProcess.getProcessKey());
        entity.setProcessType(baseProcess.getProcessType());
        entity.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        entity.setAllMembers(saveVO.getAllMembers());
        entity.setProcess(baseProcess);
        Set<PlatformMemberCycleProcessMemberDO> members = builderPlatformProcessMemberDO(entity, saveVO);
        entity.setMembers(members);
        entity.setIsDefault(ProcessDefaultEnum.NO.getCode());
        entity.setSource(ProcessSourceEnum.SYSTEM.getCode());
        return entity;
    }

    /**
     * 构建关联平台流程会员数据
     * @param entity 平台流程
     * @param saveVO 保存流程数据
     * @return 关联平台流程会员数据
     */
    private Set<PlatformMemberCycleProcessMemberDO> builderPlatformProcessMemberDO(PlatformMemberCycleProcessDO entity, PlatformMemberCycleProcessReq saveVO) {
        if (CollectionUtils.isEmpty(saveVO.getMembers())) {
            return new HashSet<>();
        }
        List<PlatformMemberCycleProcessMemberDO> members = saveVO.getMembers().stream().map(map -> {
            PlatformMemberCycleProcessMemberDO target = new PlatformMemberCycleProcessMemberDO();
            target.setProcess(entity);
            target.setMemberId(map.getMemberId());
            target.setRoleId(map.getRoleId());
            return target;
        }).collect(Collectors.toList());
        return new HashSet<>(new ArrayList<>(members));
    }

    /**
     * 保存校验，同时获取基础流程
     * @param saveVO 保存数据
     * @return 基础流程
     */
    private WrapperResp<BaseMemberCycleProcessDO> checkSave(PlatformMemberCycleProcessReq saveVO) {

        // 判断基础流程
        BaseMemberCycleProcessDO baseProcess = baseMemberCycleProcessRepository.findById(saveVO.getBaseProcessId()).orElse(null);
        if (Objects.isNull(baseProcess)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_BASE_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }

        // 查询当前有效非默认流程类型数据
        Specification<PlatformMemberCycleProcessDO> spec = (root, query, builder) -> {
            List<Predicate> condition = new ArrayList<>();
            condition.add(builder.equal(root.get("process").as(BaseMemberCycleProcessDO.class), baseProcess));
            condition.add(builder.equal(root.get("isDefault").as(Integer.class), ProcessDefaultEnum.NO.getCode()));
            condition.add(builder.equal(root.get("status").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
            return builder.and(condition.toArray(new Predicate[0]));
        };
        List<PlatformMemberCycleProcessDO> all = platformMemberCycleProcessRepository.findAll(spec);

        // 所有会员共享流程已存在
        if (saveVO.getAllMembers() && !CollectionUtils.isEmpty(all)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_EXISTS);
        }

        // 非共享流程请配置会员信息
        if (!saveVO.getAllMembers() && CollectionUtils.isEmpty(saveVO.getMembers())) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_MEMBER_IS_EMPTY);
        }

        // 已有会员
        List<Long> memberList = all.stream().flatMap(object -> object.getMembers().stream()).map(PlatformMemberCycleProcessMemberDO::getMemberId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(saveVO.getMembers()) && saveVO.getMembers().stream().anyMatch(object -> memberList.contains(object.getMemberId()))){
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_EXISTS);
        }

        return WrapperUtil.success(baseProcess);
    }

    @Override
    public PlatformMemberCycleProcessDetailResp getInfo(UserLoginCacheDTO loginUser, Long processId) {
        // step 1: 校验数据
        PlatformMemberCycleProcessDO entity = platformMemberCycleProcessRepository.findById(processId).orElse(null);
        if (Objects.isNull(entity)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        // step 2: 组装数据
        PlatformMemberCycleProcessDetailResp target = new PlatformMemberCycleProcessDetailResp();
        target.setProcessId(entity.getId());
        target.setName(entity.getName());
        target.setStatus(entity.getStatus());
        target.setStatusName(EnableDisableStatusEnum.getNameByCode(entity.getStatus()));
        target.setBaseProcessId(Optional.ofNullable(entity.getProcess()).map(BaseMemberCycleProcessDO::getId).orElse(0L));
        target.setAllMembers(entity.getAllMembers());
        target.setBaseProcess(builderBaseMemberCycleProcessVO(entity.getProcess()));
        target.setIsDefault(Optional.ofNullable(entity.getIsDefault()).orElse(0));
        return target;
    }

    @Override
    public List<PlatformMemberCycleProcessMemberResp> listProcessMembers(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessMemberQueryReq queryVO) {
        // step 1:校验数据
        PlatformMemberCycleProcessDO entity = platformMemberCycleProcessRepository.findById(queryVO.getProcessId()).orElse(null);
        if (Objects.isNull(entity)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        if (entity.getAllMembers()) {
            return new ArrayList<>();
        }
        Specification<PlatformMemberCycleProcessMemberDO> spec = (root, query, builder) -> builder.equal(root.get("process").as(PlatformMemberCycleProcessDO.class), entity);
        List<PlatformMemberCycleProcessMemberDO> members = platformMemberCycleProcessMemberRepository.findAll(spec, Sort.by("id").descending());
        if (CollectionUtils.isEmpty(members)) {
            return new ArrayList<>();
        }

        // step 2:从会员服务查询
        List<MemberFeignReq> feignList = members.stream().map(map -> {
            MemberFeignReq feignVO = new MemberFeignReq();
            feignVO.setMemberId(map.getMemberId());
            feignVO.setRoleId(map.getRoleId());
            return feignVO;
        }).collect(Collectors.toList());

        WrapperResp<List<PlatformMemberCycleProcessMemberResp>> feignResult = findPlatformMembers(queryVO.getName(), feignList);
        if(feignResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {

            throw new BusinessException(feignResult.getCode(), feignResult.getMessage());
        }
        if(CollectionUtils.isEmpty(feignResult.getData())) {
            return new ArrayList<>();
        }
        return feignResult.getData();
    }

    /**
     * 查询会员信息
     * @param name      会员名称
     * @param members   会员信息
     * @return PlatformMemberCycleProcessMemberDTO
     */
    private WrapperResp<List<PlatformMemberCycleProcessMemberResp>> findPlatformMembers(String name, List<MemberFeignReq> members) {
        List<MemberFeignPageQueryResp> feignResult = iMemberFeignService.findPlatformMembers(members);

        if (CollectionUtils.isEmpty(feignResult)) {
            return WrapperUtil.success(new ArrayList<>());
        }
        return WrapperUtil.success(feignResult.stream().map(map -> {
            PlatformMemberCycleProcessMemberResp target = new PlatformMemberCycleProcessMemberResp();
            target.setMemberId(map.getMemberId());
            target.setRoleId(map.getRoleId());
            target.setName(map.getName());
            target.setRoleName(map.getRoleName());
            target.setMemberTypeName(map.getMemberTypeName());
            target.setLevel(map.getLevel());
            target.setLevelTag(map.getLevelTag());
            return target;
        }).collect(Collectors.toList()).stream().filter(f -> {
            if (StringUtils.hasLength(name)) {
                return f.getName().contains(name.trim());
            } else {
                return true;
            }
        }).sorted(Comparator.comparingLong(PlatformMemberCycleProcessMemberResp::getMemberId).thenComparingLong(PlatformMemberCycleProcessMemberResp::getRoleId)).collect(Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessUpdateReq updateVO) {
        // step 1: 修改校验
        WrapperResp<PlatformMemberCycleProcessDO> checkWrapperResp = checkUpdate(updateVO);
        if (checkWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            return;
        }

        // step 2:删除历史关联会员数据
        Set<PlatformMemberCycleProcessMemberDO> oldMembers = platformMemberCycleProcessRepository.findById(updateVO.getProcessId()).map(PlatformMemberCycleProcessDO::getMembers).orElse(null);
        if (!CollectionUtils.isEmpty(oldMembers)) {
            platformMemberCycleProcessMemberRepository.deleteInBatch(oldMembers);
        }

        // step 3:组装数据
        PlatformMemberCycleProcessDO entity = checkWrapperResp.getData();
        entity.setName(updateVO.getName());
        entity.setProcessKey(entity.getProcess().getProcessKey());
        entity.setProcessType(entity.getProcess().getProcessType());
        entity.setStatus(entity.getStatus());
        entity.setAllMembers(updateVO.getAllMembers());
        entity.setMembers(builderPlatformProcessMemberDO(entity, updateVO));
        // step 4:保存数据
        platformMemberCycleProcessRepository.saveAndFlush(entity);

    }

    /**
     * 校验修改平台流程
     * @param updateVO 更新平台流程
     * @return 修改结果
     */
    private WrapperResp<PlatformMemberCycleProcessDO> checkUpdate(PlatformMemberCycleProcessUpdateReq updateVO) {
        // step 1:判断基础流程
        BaseMemberCycleProcessDO baseMemberCycleProcessDO = baseMemberCycleProcessRepository.findById(updateVO.getBaseProcessId()).orElse(null);
        if (Objects.isNull(baseMemberCycleProcessDO)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_BASE_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }

        Specification<PlatformMemberCycleProcessDO> spec = (root, query, builder) -> query.where(builder.equal(root.get("process").as(BaseMemberCycleProcessDO.class), baseMemberCycleProcessDO),builder.notEqual(root.get("id").as(Long.class), updateVO.getProcessId())).getRestriction();
        List<PlatformMemberCycleProcessDO> all = platformMemberCycleProcessRepository.findAll(spec);
        if (!CollectionUtils.isEmpty(all)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }

        // step 3:校验是否存在流程
        PlatformMemberCycleProcessDO entity = platformMemberCycleProcessRepository.findById(updateVO.getProcessId()).orElse(null);
        if(Objects.isNull(entity)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }

        if (Objects.equals(entity.getStatus(), EnableDisableStatusEnum.ENABLE.getCode())) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_NOT_UPDATE_PERMISSION);
        }
        // step 4:校验、保存关联的会员
        if (!updateVO.getAllMembers() && CollectionUtils.isEmpty(updateVO.getMembers())) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_MEMBER_IS_EMPTY);
        }
        entity.setProcess(baseMemberCycleProcessDO);
        return WrapperUtil.success(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessUpdateStatusReq updateStatusVO) {
        // step 1:校验数据是否存在
        PlatformMemberCycleProcessDO entity = platformMemberCycleProcessRepository.findById(updateStatusVO.getProcessId()).orElse(null);
        if (Objects.isNull(entity)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        // step 2: 冻结时
        if (Objects.equals(updateStatusVO.getStatus(), EnableDisableStatusEnum.DISABLE.getCode())) {
            // 查看是否默认所有会员
            if (entity.getAllMembers()) {
                Specification<MemberCycleProcessDO> spec = (root, query, builder) -> builder.equal(root.get("processKey").as(String.class), entity.getProcessKey());
                List<MemberCycleProcessDO> materialProcessDOList = memberCycleProcessRepository.findAll(spec);
                if (!CollectionUtils.isEmpty(materialProcessDOList)) {


                    throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_NOT_UPDATE_PERMISSION);
                }
            } else {
                Specification<MemberCycleProcessDO> spec = (root, query, builder) -> {
                    Predicate processKey = builder.equal(root.get("processKey").as(String.class), entity.getProcessKey());
                    Predicate[] predicates = entity.getMembers().stream().map(map -> {
                        Predicate memberId = builder.equal(root.get("memberId").as(Long.class), map.getMemberId());
                        Predicate roleId = builder.equal(root.get("roleId").as(Long.class), map.getRoleId());
                        return builder.and(memberId, roleId);
                    }).toArray(Predicate[]::new);
                    Predicate or = builder.or(predicates);
                    return query.where(processKey, or).getRestriction();
                };
                List<MemberCycleProcessDO> materialProcessDOList = memberCycleProcessRepository.findAll(spec);
                if (!CollectionUtils.isEmpty(materialProcessDOList)) {


                    throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_NOT_UPDATE_PERMISSION);
                }
            }
        }
        // step 3:修改状态
        entity.setStatus(updateStatusVO.getStatus());
        entity.setCreateTime(LocalDateTime.now());
        platformMemberCycleProcessRepository.saveAndFlush(entity);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(UserLoginCacheDTO loginUser, Long processId) {
        // step 1:校验删除数据
        PlatformMemberCycleProcessDO entity = platformMemberCycleProcessRepository.findById(processId).orElse(null);
        if(Objects.isNull(entity)) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        if (Objects.equals(entity.getStatus(), EnableDisableStatusEnum.ENABLE.getCode())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_NOT_DELETE_PERMISSION);
        }
        platformMemberCycleProcessRepository.deleteById(processId);

    }
}
