package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.req.engine.ProcessEngineReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.member.model.req.platform.*;
import com.ssy.lingxi.member.model.resp.platform.BaseMemberCycleProcessResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformMemberCycleProcessDetailResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformMemberCycleProcessMemberResp;
import com.ssy.lingxi.member.model.resp.platform.PlatformMemberCycleProcessPageResp;

import java.util.List;

/**
 * 平台会员生命周期变更流程服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
public interface IPlatformMemberCycleProcessService {

    /**
     * 保存基础流程
     * @param engineBO 基础流程
     */
    void saveBaseProcess(ProcessEngineReq engineBO);

    /**
     * 查询会员流程
     * @param engineRuleQueryReq 查询流程
     * @return ProcessEngineRuleVO
     */
    List<ProcessEngineRuleResp> getMemberProcess(EngineRuleQueryReq engineRuleQueryReq);

    /**
     * 分页查询流程规则配置
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<PlatformMemberCycleProcessPageResp> pageProcess(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessPageQueryDataReq pageVO);

    /**
     * 新增流程页面 - 查询基础流程列表
     * @param loginUser 登录用户信息
     * @return 查询结果
     */
    List<BaseMemberCycleProcessResp> listBaseProcess(UserLoginCacheDTO loginUser, ProcessQueryReq queryRequest);

    /**
     * 设置默认流程
     * @param defaultRequest 接口参数
     * @return Void
     */
    void saveDefault(SaveDefaultReq defaultRequest);

    /**
     * 新增流程规则
     * @param loginUser 登录用户信息
     * @param saveVO 接口参数
     * @return 新增结果
     */
    void save(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessReq saveVO);

    /**
     * 查询流程规则详情
     * @param loginUser 登录用户信息
     * @param processId 流程Id
     * @return 查询结果
     */
    PlatformMemberCycleProcessDetailResp getInfo(UserLoginCacheDTO loginUser, Long processId);

    /**
     * 查询流程规则适用会员列表
     * @param loginUser 登录用户信息
     * @param queryVO  接口参数
     * @return 查询结果
     */
    List<PlatformMemberCycleProcessMemberResp> listProcessMembers(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessMemberQueryReq queryVO);

    /**
     * 修改流程规则
     * @param loginUser 登录用户信息
     * @param updateVO 接口参数
     * @return 修改结果
     */
    void update(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessUpdateReq updateVO);

    /**
     * 修改流程规则状态
     * @param loginUser 登录用户信息
     * @param updateStatusVO 接口参数
     * @return 修改结果
     */
    void updateStatus(UserLoginCacheDTO loginUser, PlatformMemberCycleProcessUpdateStatusReq updateStatusVO);

    /**
     * 删除流程规则
     * @param loginUser 登录用户信息
     * @param processId 流程规则Id
     * @return 删除结果
     */
    void delete(UserLoginCacheDTO loginUser, Long processId);
}
