package com.ssy.lingxi.member.service.mobile;

import com.ssy.lingxi.component.base.model.req.AreaCodeReq;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.member.model.req.basic.*;
import com.ssy.lingxi.member.model.req.login.MobileRegisterReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordByEmailCodeReq;
import com.ssy.lingxi.member.model.req.login.ResetPasswordBySmsCodeReq;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterResultResp;
import com.ssy.lingxi.member.model.resp.login.MemberRegisterTypeMenuResp;
import com.ssy.lingxi.member.model.resp.login.MultiAccInfoResp;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * App - 会员注册相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-12-02
 */
public interface IMobileRegisterService {
    /**
     * “会员注册” - 检查手机号码是否被注册使用
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    void checkPhoneRegistered(HttpHeaders headers, PhoneReq phoneReq);

    /**
     * “手机号找回密码” - 检查手机号码是否存在
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 检查结果
     */
    void checkPhoneExistsByMobile(HttpHeaders headers, PhoneReq phoneReq);

    /**
     * 检查邮箱是否被注册使用
     * @param headers Http头部信息
     * @param emailReq 邮箱
     * @return 检查结果
     */
    void checkEmailRegistered(HttpHeaders headers, EmailReq emailReq);

    /**
     * 校验邀请码是否存在
     * @param headers Http头部信息
     * @param invitationCodeReq 邀请码
     * @return 检查结果
     */
    void checkInvitationCodeExists(HttpHeaders headers, InvitationCodeReq invitationCodeReq);

    /**
     * 发送注册时短信验证码
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 发送结果
     */
    void sendRegisterSmsCode(HttpHeaders headers, PhoneReq phoneReq);

    /**
     * 发送手机号找回密码时的短信验证码
     * @param headers Http头部信息
     * @param phoneReq 手机号码
     * @return 发送结果
     */
    void sendResetPasswordSmsCode(HttpHeaders headers, PhoneReq phoneReq);

    /**
     * 校验手机号找回密码时的短信验证码是否正确
     * @param headers Http头部信息
     * @param phoneSmsReq 手机号码、验证码
     * @return 发送结果
     */
    List<MultiAccInfoResp> checkResetPasswordSmsCode(HttpHeaders headers, PhoneSmsReq phoneSmsReq);

    /**
     * 根据短信验证码重设密码
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 操作结果
     */
    void resetPasswordBySmsCode(HttpHeaders headers, ResetPasswordBySmsCodeReq codeVO);

    /**
     * 发送邮箱找回密码时的邮件
     * @param headers Http头部信息
     * @param emailReq 邮箱地址
     * @return 发送结果
     */
    void sendResetPasswordEmail(HttpHeaders headers, EmailReq emailReq);

    /**
     * 发送邮箱找回密码时的邮件
     * @param headers Http头部信息
     * @param emailVO 邮箱地址
     * @return 发送结果
     */
    List<MultiAccInfoResp> checkResetPasswordEmailCode(HttpHeaders headers, EmailSmsReq emailVO);

    /**
     * 根据邮箱验证码重设密码
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 操作结果
     */
    void resetPasswordByEmailCode(HttpHeaders headers, ResetPasswordByEmailCodeReq codeVO);

    /**
     * 获取会员注册页面-会员类型、商业类型页面内容（第二页）
     * @param headers Http头部信息
     * @return 操作结果
     */
    List<MemberRegisterTypeMenuResp> getRegisterTypePageContent(HttpHeaders headers);

    /**
     * 获取会员注册页面-详细信息页面内容（第三页）
     * @param headers Http头部信息
     * @param detailVO 接口参数
     * @return 注册第三页的内容
     */
    List<MemberConfigGroupResp> getRegisterDetailPageContent(HttpHeaders headers, MemberDetailReq detailVO);

    /**
     * 查询省列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<AreaCodeNameResp> listProvince(HttpHeaders headers);

    /**
     * 根据省编码，查询市列表
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 查询结果
     */
    List<AreaCodeNameResp> listCitiesByProvinceCode(HttpHeaders headers, AreaCodeReq codeVO);


    /**
     * 根据市编码，查询区列表
     * @param headers Http头部信息
     * @param codeVO 接口参数
     * @return 查询结果
     */
    List<AreaCodeNameResp> listDistrictsByCityCode(HttpHeaders headers, AreaCodeReq codeVO);

    /**
     * 平台会员注册
     * @param registerVO 接口参数
     * @param headers Http头部信息
     * @return 操作结果
     */
    MemberRegisterResultResp registerPlatformMember(HttpHeaders headers, MobileRegisterReq registerVO);
}
