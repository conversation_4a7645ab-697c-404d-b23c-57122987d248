package com.ssy.lingxi.member.serviceImpl.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.enums.MemberValidateStatusEnum;
import com.ssy.lingxi.member.enums.MerchantMemberStringEnum;
import com.ssy.lingxi.member.service.feign.IPayFeignService;
import com.ssy.lingxi.pay.api.enums.AccountTypeEnum;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.api.feign.ICreditFeign;
import com.ssy.lingxi.pay.api.model.req.CreditAddReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.BalanceCashbackReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.MemberAssetAccountAddReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.MemberAssetAccountUpdateReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 调用支付服务Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-15
 */
@Service
public class PayFeignServiceImpl implements IPayFeignService {
    private static final Logger logger = LoggerFactory.getLogger(PayFeignServiceImpl.class);

    @Resource
    private ICreditFeign creditControllerFeign;

    @Resource
    private IAssetAccountFeign assetAccountFeign;

    /**
     * 下级会员审核通过后，通知支付服务，新增支付授信
     *
     * @param memberRelation 会员关系
     */
    @Override
    public void notifyMemberCredit(MemberRelationDO memberRelation) {
        if(!memberRelation.getRelType().equals(MemberRelationTypeEnum.OTHER.getCode()) || !memberRelation.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode())) {
            return;
        }

        notifyMemberCredit(memberRelation.getMemberId(), memberRelation.getRoleId(), memberRelation.getSubMemberId(), memberRelation.getSubRoleId());
    }

    /**
     * 下级会员审核通过后，通知支付服务，新增支付授信
     * @param memberId    上级会员Id
     * @param roleId      上级会员角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId   下级会员角色Id
     */
    @Async
    @Override
    public void notifyMemberCredit(Long memberId, Long roleId, Long subMemberId, Long subRoleId) {
        try {
            CreditAddReq creditAddReq = new CreditAddReq();
            creditAddReq.setParentMemberId(memberId);
            creditAddReq.setParentMemberRoleId(roleId);
            creditAddReq.setMemberId(subMemberId);
            creditAddReq.setRoleId(subRoleId);
            creditControllerFeign.add(creditAddReq);
        } catch (Exception e) {
            logger.error("通知支付服务，新增支付授信错误", e);
        }
    }

    /**
     * 下级会员审核通过后，通知支付服务，新增会员资金账户
     *
     * @param memberRelation 会员关系
     */
    @Override
    public void notifyMemberAssetAccount(MemberRelationDO memberRelation) {
        if(!memberRelation.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode())) {
            return;
        }

        notifyMemberAssetAccount(memberRelation.getMemberId(), memberRelation.getMember().getName(), memberRelation.getRoleId(), memberRelation.getRole().getRoleName(), memberRelation.getSubMemberId(), memberRelation.getSubMember().getCode(), memberRelation.getSubMember().getName(), memberRelation.getSubRoleId(), memberRelation.getSubRoleName(), memberRelation.getSubMemberLevelTypeEnum(), memberRelation.getSubRole().getMemberType(), memberRelation.getStatus());
    }

    /**
     * 下级会员审核通过后，通知支付服务，新增会员资金账户
     *
     * @param memberId               上级会员Id
     * @param memberName             上级会员名称
     * @param roleId                 上级角色Id
     * @param roleName               上级角色名称
     * @param subMemberId            下级会员Id
     * @param subMemberName          下级会员名称
     * @param subRoleId              下级角色Id
     * @param subRoleName            下级角色名称
     * @param subMemberLevelTypeEnum 下级会员等级类型枚举
     * @param subMemberTypeEnum      下级会员会员类型枚举
     * @param subMemberStatus        下级会员状态
     */
    @Async
    @Override
    public void notifyMemberAssetAccount(Long memberId, String memberName, Long roleId, String roleName, Long subMemberId, String subMemberCode, String subMemberName, Long subRoleId, String subRoleName, Integer subMemberLevelTypeEnum, Integer subMemberTypeEnum, Integer subMemberStatus) {
        try {
            List<MemberAssetAccountAddReq> requestList = new ArrayList<>();
            MemberAssetAccountAddReq request = new MemberAssetAccountAddReq();
            request.setMemberId(subMemberId);
            request.setMemberCode(subMemberCode);
            request.setMemberName(subMemberName);
            request.setMemberRoleId(subRoleId);
            request.setMemberRoleName(subRoleName);
            request.setParentMemberId(memberId);
            request.setParentMemberName(memberName);
            request.setParentMemberRoleId(roleId);
            request.setParentMemberRoleName(roleName);
            request.setMemberLevelType(subMemberLevelTypeEnum);
            request.setMemberType(subMemberTypeEnum);
            request.setMemberStatus(subMemberStatus);

            requestList.add(request);

            logger.info("新增会员资金账户参数 => " + SerializeUtil.serialize(request));
            WrapperResp<String> result = assetAccountFeign.saveMemberAssetAccount(requestList);
            logger.info("新增会员资金账户返回, code:" + result.getCode() + ", msg:" + result.getMessage() + ", data:" + result.getData());
        } catch (Exception e) {
            logger.error("通知支付服务，新增会员资金账户错误", e);
        }
    }

    /**
     * 冻结/解冻会员时，通知支付服务，冻结/解冻会员资金账户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级角色Id
     * @param relType   关联关系
     * @param subMemberStatus 会员状态
     */
    @Async
    @Override
    public void notifyUpdateMemberAssetAccount(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Integer relType, Integer subMemberStatus) {
        try {

            MemberAssetAccountUpdateReq request = new MemberAssetAccountUpdateReq();
            request.setMemberId(subMemberId);
            request.setMemberRoleId(subRoleId);
            request.setParentMemberId(upperMemberId);
            request.setParentMemberRoleId(upperRoleId);
            request.setMemberType(relType.equals(MemberRelationTypeEnum.PLATFORM.getCode()) ? AccountTypeEnum.Platform.getCode() : AccountTypeEnum.Member.getCode());
            request.setMemberStatus(subMemberStatus);

            assetAccountFeign.updateMemberAssetAccount(request);

        } catch (Exception e) {
            logger.error("通知支付服务，冻结/解冻会员资金账户", e);
        }
    }

    /**
     * 淘汰、黑名单会员时，通知支付服务，冻结会员资金账户
     *
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级角色Id
     * @param subMemberId   下级会员Id
     * @param subRoleId     下级角色Id
     * @param relType       关联关系
     */
    @Override
    public void notifyToFreezeAssetAccount(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Integer relType) {
        try {

            MemberAssetAccountUpdateReq request = new MemberAssetAccountUpdateReq();
            request.setMemberId(subMemberId);
            request.setMemberRoleId(subRoleId);
            request.setParentMemberId(upperMemberId);
            request.setParentMemberRoleId(upperRoleId);
            request.setMemberType(relType.equals(MemberRelationTypeEnum.PLATFORM.getCode()) ? AccountTypeEnum.Platform.getCode() : AccountTypeEnum.Member.getCode());
            request.setMemberStatus(2);

            assetAccountFeign.updateMemberAssetAccount(request);

        } catch (Exception e) {
            logger.error("通知支付服务，冻结/解冻会员资金账户", e);
        }
    }

    /**
     * 交易完成后，如果返现金额大于0，通知支付服务
     *
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级会员角色Id
     * @param subMemberId   下级会员Id
     * @param subRoleId     下级会员角色Id
     * @param returnType    返现账号类型: 1-平台账户; 2-会员账户;
     * @param returnMoney   返现金额
     * @param orderNo       订单号
     */
    @Async
    @Override
    public void notifyAssetAccountReturnMoney(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Integer returnType, BigDecimal returnMoney, String orderNo) {
        try {

            BalanceCashbackReq request = new BalanceCashbackReq();
            request.setMemberId(subMemberId);
            request.setMemberRoleId(subRoleId);
            request.setParentMemberId(upperMemberId);
            request.setParentMemberRoleId(upperRoleId);
            request.setCashbackType(returnType);
            request.setCashbackMoney(returnMoney);
            request.setOrderCode(orderNo);
            //request.setRemark("");
            request.setRemark(MerchantMemberStringEnum.STR_1.getMessage().concat(orderNo));

            assetAccountFeign.balanceCashback(request);
        } catch (Exception e) {
            logger.error("通知支付服务，增加账户返现金额", e);
        }
    }
}
