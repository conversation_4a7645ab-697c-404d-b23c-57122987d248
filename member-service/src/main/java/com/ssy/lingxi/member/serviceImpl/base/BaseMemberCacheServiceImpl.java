package com.ssy.lingxi.member.serviceImpl.base;

import cn.hutool.core.util.IdUtil;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.TokenUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.config.MemberRefreshConfig;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.entity.bo.AuthCodeBO;
import com.ssy.lingxi.member.entity.bo.AuthCodeMemberBO;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Enumeration;
import java.util.Optional;

/**
 * 会员用户缓存工具类实现
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-03
 */
@Service
@Slf4j
public class BaseMemberCacheServiceImpl implements IBaseMemberCacheService {
    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private MemberRefreshConfig memberRefreshConfig;

    /**
     * 缓存会员注册手机号，邮箱的后缀
     */
    private final String MEMBER_REGISTER_KEY_SUFFIX = "_reg";

    /**
     * 获取登录来源
     */
    @Override
    public String getSource() {
        return getHttpServletRequest().getHeader(Constant.LOGIN_SOURCE);
    }

    /**
     * 获取HttpServletRequest
     */
    @Override
    public HttpServletRequest getHttpServletRequest() {
        return Optional.ofNullable((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .map(ServletRequestAttributes::getRequest)
                .orElseThrow(() -> new BusinessException(ResponseCodeEnum.REQUEST_HEADER_ERROR));
    }

    /**
     * 获取用户信息
     *
     * @param headers Http头部信息
     * @return 用户信息实体
     */
    @Override
    public UserLoginCacheDTO checkUserFromCache(HttpHeaders headers) {
        //HTTP header是否包含了token字段
        if (!headers.containsKey(Constant.ACCESS_TOKEN)) {
            throw new BusinessException(ResponseCodeEnum.TOKEN_EXPIRE);
        }

        //HTTP header是否包含了source字段
        if (!headers.containsKey(Constant.LOGIN_SOURCE)) {
            throw new BusinessException(ResponseCodeEnum.REQUEST_HEADER_ERROR);
        }

        //HTTP header的token字段是否为空
        String accessToken = headers.getFirst(Constant.ACCESS_TOKEN);
        if (!StringUtils.hasLength(accessToken)) {
            throw new BusinessException(ResponseCodeEnum.TOKEN_EXPIRE);
        }

        //HTTP header的source字段是否为空
        String source = headers.getFirst(Constant.LOGIN_SOURCE);
        if (!StringUtils.hasLength(source) || !SystemSourceEnum.toStringList().contains(source)) {
            throw new BusinessException(ResponseCodeEnum.REQUEST_HEADER_ERROR);
        }

        //redis中是否有token
        String tokenStr = redisUtils.stringGet(TokenUtil.generateAccessTokenRedisKey(accessToken), RedisConstant.REDIS_USER_INDEX);
        if (!StringUtils.hasLength(tokenStr)) {
            throw new BusinessException(ResponseCodeEnum.TOKEN_EXPIRE);
        }

        UserLoginCacheDTO cacheDTO = JsonUtil.toObj(tokenStr, UserLoginCacheDTO.class);

        //用户信息反序列化是否成功
        if (cacheDTO == null) {
            throw new BusinessException(ResponseCodeEnum.TOKEN_EXPIRE);
        }

        // source字段是否与登录后缓存的loginFrom字段相同
        if (!cacheDTO.getLoginSource().equals(Integer.valueOf(source))) {
            throw new BusinessException(ResponseCodeEnum.LOGIN_SOURCE_NOT_EQUALS_TO_HTTP_HEADER_SOURCE);
        }

        return cacheDTO;
    }

    /**
     * 未来这整个类都会删掉，平台后台也无性能问题，临时先这样写
     */
    @Override
    public UserLoginCacheDTO needLoginFromManagePlatform() {
        HttpServletRequest httpServletRequest = getHttpServletRequest();

        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headerNames = httpServletRequest.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = httpServletRequest.getHeader(headerName);
            headers.add(headerName, headerValue);
        }

        return needLoginFromManagePlatform(headers);
    }

    /**
     * 是否从平台后台登录
     * @param headers HttpHeaders信息
     * @return 用户信息
     */
    @Override
    public UserLoginCacheDTO needLoginFromManagePlatform(HttpHeaders headers) {
        UserLoginCacheDTO loginCacheDTO = checkUserFromCache(headers);
        if (loginCacheDTO.getLoginSource() == null || !loginCacheDTO.getLoginSource().equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode())) {
            throw new BusinessException(ResponseCodeEnum.NEED_LOGIN_FROM_MANAGE_PLATFORM);
        }
        return loginCacheDTO;
    }

    /**
     * 是否从业务平台登录
     * @param headers HttpHeaders信息
     * @return 用户信息
     */
    @Override
    public UserLoginCacheDTO needLoginFromBusinessPlatform(HttpHeaders headers) {
        UserLoginCacheDTO loginCacheDTO = checkUserFromCache(headers);
        if (loginCacheDTO.getLoginSource() == null || !loginCacheDTO.getLoginSource().equals(SystemSourceEnum.BUSINESS_WEB.getCode())) {
            throw new BusinessException(ResponseCodeEnum.NEED_LOGIN_FROM_BUSINESS_CENTER);
        }
        return loginCacheDTO;
    }

    /**
     * 是否从App客户端登录
     *
     * @param headers HttpHeaders信息
     * @return 缓存的用户信息
     */
    @Override
    public UserLoginCacheDTO needLoginFromMobile(HttpHeaders headers) {
        UserLoginCacheDTO loginCacheDTO = checkUserFromCache(headers);
        if (loginCacheDTO.getLoginSource() == null || !loginCacheDTO.getLoginSource().equals(SystemSourceEnum.BUSINESS_MOBILE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.NEED_LOGIN_FROM_BUSINESS_APP);
        }
        return loginCacheDTO;
    }

    /**
     * 校验Web端请求的HttpHeader参数
     */
    @Override
    public void checkWebRequestHeader() {
        String source = getSource();
        if (!StringUtils.hasLength(source) || !source.equals(String.valueOf(SystemSourceEnum.BUSINESS_WEB.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.REQUEST_HEADER_ERROR);
        }
    }

    /**
     * 校验Web端请求的HttpHeader参数是否合法
     * @param headers HttpHeaders信息
     */
    @Override
    public void checkWebRequestHeader(HttpHeaders headers) {
        String source = headers.getFirst(Constant.LOGIN_SOURCE);
        //HttpHeader中是否包含source = 1
        //web端要校验请求头的站点id
        if (!StringUtils.hasLength(source) || !source.equals(String.valueOf(SystemSourceEnum.BUSINESS_WEB.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.REQUEST_HEADER_ERROR);
        }
    }

    /**
     * 校验移动端请求的HttpHeader参数是否合法
     */
    @Override
    public void checkMobileRequestHeader() {
        String source = getSource();
        if (!StringUtils.hasLength(source) || !source.equals(String.valueOf(SystemSourceEnum.BUSINESS_MOBILE.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.REQUEST_HEADER_ERROR);
        }
    }

    /**
     * 校验移动端请求的HttpHeader参数是否合法
     * @param headers HttpHeaders信息
     */
    @Override
    public void checkMobileRequestHeader(HttpHeaders headers) {
        String source = headers.getFirst(Constant.LOGIN_SOURCE);
        //HttpHeader中是否包含source = 2
        //移动端要校验请求头的站点id
        if (!StringUtils.hasLength(source) || !source.equals(String.valueOf(SystemSourceEnum.BUSINESS_MOBILE.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.REQUEST_HEADER_ERROR);
        }
    }

    /**
     * 校验平台后台请求的HttpHeader中是否包含source = 99
     */
    @Override
    public void checkPlatformRequestHeader() {
        String source = getSource();
        if (!StringUtils.hasLength(source) || !source.equals(String.valueOf(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.REQUEST_HEADER_ERROR);
        }
    }

    /**
     * 校验平台后台请求的HttpHeader参数是否合法
     * @param headers HttpHeaders信息
     */
    @Override
    public void checkPlatformRequestHeader(HttpHeaders headers) {
        String source = headers.getFirst(Constant.LOGIN_SOURCE);
        //HttpHeader中是否包含source = 99
        //平台后台要校验请求头的站点id
        if (!StringUtils.hasLength(source) || !source.equals(String.valueOf(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.REQUEST_HEADER_ERROR);
        }
    }

    /**
     * 从Http头部获取Long类型的字段值
     *
     * @param headers HttpHeaders信息
     * @param key     HttpHeaders中的Key
     * @return Long类型值
     */
    @Override
    public Long getLongValueFromRequestHeader(HttpHeaders headers, String key) {
        return NumberUtil.tryParseLong(headers.getFirst(key));
    }

    /**
     * 校验请求头的HttpHeader中是否包含Long类型的必要信息
     *
     * @param headers HttpHeaders信息
     * @return 请求头中是否有合理的Long类型的字段
     */
    @Override
    public Boolean checkLongValue(HttpHeaders headers, String... keyNames) {
        if (keyNames != null && keyNames.length > 0) {
            for (String keyName : keyNames) {
                Long value = NumberUtil.tryParseLong(headers.getFirst(keyName));
                if (NumberUtil.isNullOrLteZero(value)) {
                    return true;
                }
            }
            return false;
        }
        return true;
    }

    /**
     * 缓存字符串类型的内容
     *
     * @param key            缓存的Key
     * @param content        缓存的内容
     * @param timeoutSeconds 缓存时间，单位：秒
     */
    @Override
    public void setString(String key, String content, Long timeoutSeconds) {
        redisUtils.stringSet(key, content, timeoutSeconds, RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 读取缓存的字符串
     *
     * @param key 缓存的Key
     * @return 如果缓存内容不存在，返回空字符串，否则返回缓存内容
     */
    @Override
    public String getString(String key) {
        if (!redisUtils.keyExists(key, RedisConstant.REDIS_USER_INDEX)) {
            return "";
        }

        Object obj = redisUtils.stringGet(key, RedisConstant.REDIS_USER_INDEX);
        return obj == null ? "" : String.valueOf(obj);
    }

    /**
     * 删除缓存的字符串
     * @param key 缓存的字符串
     */
    @Override
    public void deleteString(String key) {
        redisUtils.keyDel(key, RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 会员/用户注册时，或创建用户时，缓存手机号或邮箱
     * @param phone 手机号
     * @param email 邮箱
     * @param isPlatformMember 是否平台后台会员
     */
    @Override
    public void setRegisterKey(String phone, String email, Boolean isPlatformMember) {
        if (StringUtils.hasLength(phone)) {
            String key = phone.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            boolean isSuccess = redisUtils.stringSet(key, "1", MemberConstant.REGISTER_PHONE_EMAIL_CACHE_SECONDS, RedisConstant.REDIS_USER_INDEX);
            if (!isSuccess) {
                return;
            }
        }

        if (StringUtils.hasLength(email)) {
            String key = email.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            redisUtils.stringSet(key, "1", MemberConstant.REGISTER_PHONE_EMAIL_CACHE_SECONDS, RedisConstant.REDIS_USER_INDEX);
        }

    }

    /**
     * 会员/用户注册时，缓存手机号或邮箱或账号
     * @param phone 手机号
     * @param email 邮箱
     * @param account 账号
     * @param isPlatformMember 是否平台后台会员
     */
    @Override
    public void setRegisterKey(String phone, String email, String account, Boolean isPlatformMember) {
        if (StringUtils.hasLength(phone)) {
            String key = phone.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            boolean isSuccess = redisUtils.stringSet(key, "1", MemberConstant.REGISTER_PHONE_EMAIL_CACHE_SECONDS, RedisConstant.REDIS_USER_INDEX);
            if (!isSuccess) {
                return;
            }
        }

        if (StringUtils.hasLength(account)) {
            String key = account.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            boolean isSuccess = redisUtils.stringSet(key, "1", MemberConstant.REGISTER_PHONE_EMAIL_CACHE_SECONDS, RedisConstant.REDIS_USER_INDEX);
            if (!isSuccess) {
                return;
            }
        }

        if (StringUtils.hasLength(email)) {
            String key = email.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            redisUtils.stringSet(key, "1", MemberConstant.REGISTER_PHONE_EMAIL_CACHE_SECONDS, RedisConstant.REDIS_USER_INDEX);
        }
    }


    /**
     * 缓存的Key是否存在
     *
     * @param phoneOrEmail 手机号或邮箱
     * @param isPlatformMember 是否平台后台会员
     * @return true/false
     */
    @Override
    public Boolean existRegisterKey(String phoneOrEmail, Boolean isPlatformMember) {
        String key = phoneOrEmail.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
        return redisUtils.keyExists(key, RedisConstant.REDIS_USER_INDEX);
    }

    /**
     * 删除缓存的用户手机号或邮箱
     * @param phone 缓存的手机号
     * @param email 缓存的邮箱
     * @param isPlatformMember 是否平台后台会员
     */
    @Override
    public void deleteRegisterKey(String phone, String email, Boolean isPlatformMember) {
        if (StringUtils.hasLength(phone)) {
            String key = phone.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            redisUtils.keyDel(key, RedisConstant.REDIS_USER_INDEX);
        }

        if (StringUtils.hasLength(email)) {
            String key = email.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            redisUtils.keyDel(key, RedisConstant.REDIS_USER_INDEX);
        }
    }

    /**
     * 删除缓存的用户手机号或邮箱或账号
     * @param phone 缓存的手机号
     * @param email 缓存的邮箱
     * @param account 缓存的账号
     * @param isPlatformMember 是否平台后台会员
     */
    @Override
    public void deleteRegisterKey(String phone, String email, String account, Boolean isPlatformMember) {
        if (StringUtils.hasLength(phone)) {
            String key = phone.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            redisUtils.keyDel(key, RedisConstant.REDIS_USER_INDEX);
        }

        if (StringUtils.hasLength(email)) {
            String key = email.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            redisUtils.keyDel(key, RedisConstant.REDIS_USER_INDEX);
        }

        if (StringUtils.hasLength(account)) {
            String key = account.concat(isPlatformMember ? "_1" : "_0").concat(MEMBER_REGISTER_KEY_SUFFIX);
            redisUtils.keyDel(key, RedisConstant.REDIS_USER_INDEX);
        }
    }

    @Override
    public AuthCodeBO createAuthCode() {
        // 生成授权码
        String authCode = IdUtil.simpleUUID();

        // 缓存
        String key = MemberRedisConstant.LOGIN_QR_AUTH_CODE_REDIS_KEY_PREFIX + authCode;
        if (redisUtils.keyExists(key, RedisConstant.REDIS_USER_INDEX)) {
            authCode = IdUtil.simpleUUID();
        }

        long expireTime = memberRefreshConfig.getQrAuthCodeExpireSeconds();
        redisUtils.stringSet(key, key, expireTime, RedisConstant.REDIS_USER_INDEX);

        return new AuthCodeBO(authCode, expireTime);
    }

    @Override
    public void activeAuthCode(String authCode, UserLoginCacheDTO userLoginCacheDTO) {
        // 校验authCode
        String authCodeKey = MemberRedisConstant.LOGIN_QR_AUTH_CODE_REDIS_KEY_PREFIX + authCode;
        if (!redisUtils.keyExists(authCodeKey, RedisConstant.REDIS_USER_INDEX)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_APP_LOGIN_AUTH_CODE_EXPIRE);
        }

        AuthCodeMemberBO authCodeMemberBO = new AuthCodeMemberBO();
        authCodeMemberBO.setMemberId(userLoginCacheDTO.getMemberId());
        authCodeMemberBO.setUserId(userLoginCacheDTO.getUserId());

        // 授权信息
        String authInfoKey = MemberRedisConstant.LOGIN_QR_AUTH_INFO_REDIS_KEY_PREFIX + authCode;
        redisUtils.stringSet(authInfoKey, JsonUtil.toJson(authCodeMemberBO), memberRefreshConfig.getQrAuthCodeExpireSeconds(), RedisConstant.REDIS_USER_INDEX);

        // authCode只能使用一次, 删除authCode
        redisUtils.keyDel(authCodeKey, RedisConstant.REDIS_USER_INDEX);
    }
}
