package com.ssy.lingxi.member.controller.web.customer;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.lrc.*;
import com.ssy.lingxi.member.model.req.validate.ValidateIdReq;
import com.ssy.lingxi.member.model.resp.basic.RoleQueryResp;
import com.ssy.lingxi.member.model.resp.lrc.*;
import com.ssy.lingxi.member.service.web.IMemberLevelConfigService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 客户能力 - 会员升级规则、等级、权益相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-24
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/customer/ability")
public class CustomerLevelRightCreditServiceController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.CUSTOMER.getCode();

    @Resource
    private IMemberLevelConfigService memberLevelConfigService;

    /**
     * 分页查询会员升级规则
     * @param headers Http头部信息
     * @param pageDataReq 接口参数
     * @return 操作结果
     */
    @GetMapping("/level/rule/page")
    public WrapperResp<PageDataResp<MemberLevelRuleConfigQueryResp>> pageMemberLevelRuleConfig(@RequestHeader HttpHeaders headers, @Valid PageDataReq pageDataReq) {
        return WrapperUtil.success(memberLevelConfigService.pageMemberLevelRuleConfig(headers, pageDataReq));
    }

    /**
     * 修改会员升级规则可获取的分值
     * @param headers Http头部信息
     * @param ruleVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/level/rule/updatescore")
    public WrapperResp<Void> updateMemberLevelRuleConfigScore(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLevelRuleConfigUpdateScoreReq ruleVO) {
         memberLevelConfigService.updateMemberLevelRuleConfigScore(headers, ruleVO);
        return WrapperUtil.success();
    }

    /**
     * 分页、模糊查询会员等级列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/level/page")
    public WrapperResp<PageDataResp<MemberLevelQueryResp>> pageMemberLevelConfig(@RequestHeader HttpHeaders headers, @Valid MemberLevelPageDataReq pageVO) {
        return WrapperUtil.success(memberLevelConfigService.pageMemberLevels(headers, pageVO));
    }

    /**
     * 分页、模糊查询会员等级中为企业会员和个人会员且会员角色为服务消费的会员等级列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/level/consumer/page")
    public WrapperResp<PageDataResp<MemberLevelQueryResp>> pageMemberLevelByConsumer(@RequestHeader HttpHeaders headers, @Valid MemberLevelPageDataReq pageVO) {
        return WrapperUtil.success(memberLevelConfigService.pageMemberLevelByConsumer(headers, pageVO));
    }

    /**
     * 新增或修改会员等级时，查询等级类型列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/level/types")
    public WrapperResp<List<MemberLevelTypeResp>> findMemberLevelTypes(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberLevelConfigService.findMemberLevelTypes(headers));
    }

    /**
     * 新增会员等级时，分页查询会员角色列表
     * @param headers Http头部信息
     * @param pageDataReq 接口参数
     * @return 查询结果
     */
    @GetMapping("/level/role/page")
    public WrapperResp<PageDataResp<RoleQueryResp>> pageRoles(@RequestHeader HttpHeaders headers, @Valid PageDataReq pageDataReq) {
        return WrapperUtil.success(memberLevelConfigService.pageRoles(headers, pageDataReq, roleTag));
    }

    /**
     * 新增会员等级
     * @param headers Http头部信息
     * @param levelVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/level/create")
    public WrapperResp<Void> createMemberLevel(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLevelReq levelVO) {
         memberLevelConfigService.createMemberLevel(headers, levelVO);
        return WrapperUtil.success();
    }

    /**
     * 修改会员等级
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/level/update")
    public WrapperResp<Void> updateMemberLevel(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLevelUpdateReq updateVO) {
         memberLevelConfigService.updateMemberLevel(headers, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 查询会员等级详细信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/level/get")
    public WrapperResp<MemberLevelDetailResp> findMemberLevel(@RequestHeader HttpHeaders headers, @Valid MemberLevelIdReq idVO) {
        return WrapperUtil.success(memberLevelConfigService.findMemberLevel(headers, idVO));
    }

    /**
     * 更改会员等级状态
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/level/status")
    public WrapperResp<Void> updateMemberLevelStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLevelStatusReq statusVO) {
         memberLevelConfigService.updateMemberLevelStatus(headers, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 删除会员等级
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/level/delete")
    public WrapperResp<Void> deleteMemberLevel(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLevelIdReq idVO) {
         memberLevelConfigService.deleteMemberLevel(headers, idVO);
        return WrapperUtil.success();
    }

    /**
     * 权益与升级阈值 - 查询详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/right/detail")
    public WrapperResp<MemberLevelRightDetailResp> findMemberLevelRightDetail(@RequestHeader HttpHeaders headers, @Valid MemberLevelIdReq idVO) {
         memberLevelConfigService.findMemberLevelRightDetail(headers, idVO);
        return WrapperUtil.success();
    }

    /**
     * 权益与升级阈值 - 选择会员权益
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/right/find")
    public WrapperResp<List<BaseMemberRightQueryResp>> findMemberLevelRights(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberLevelConfigService.findMemberLevelRights(headers));
    }

    /**
     * 修改升级阈值、权益列表
     * @param headers Http头部信息
     * @param pointVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/right/update")
    public WrapperResp<Void> updateMemberLevelRight(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberLevelRightUpdateReq pointVO) {
         memberLevelConfigService.updateMemberLevelRight(headers, pointVO);
        return WrapperUtil.success();
    }

    /**
     * 更改会员权益参数值
     * @param headers Http头部信息
     * @param paramVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/right/parameter/update")
    public WrapperResp<Void> updateRightParameter(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberRightParamReq paramVO) {
         memberLevelConfigService.updateRightParameter(headers, paramVO);
        return WrapperUtil.success();
    }

    /**
     * 更改会员权益状态
     * @param headers Http头部新
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @PostMapping("/right/status")
    public WrapperResp<Void> updateRightStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberRightStatusReq statusVO) {
         memberLevelConfigService.updateRightStatus(headers, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 初始化会员等级与权益
     * @param headers Http头部信息
     * @return 操作结果
     */
    @PostMapping("/level/rebuild")
    public WrapperResp<Void> rebuildMemberLevelRight(@RequestHeader HttpHeaders headers) {
         memberLevelConfigService.rebuildMemberLevelRight(headers);
        return WrapperUtil.success();
    }

    /**
     * 查询平台会员的等级、权益、信用积分
     * @param headers Http头部信息
     * @param levelVO 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    @GetMapping("/level/platform/get")
    public WrapperResp<MemberLrcResp> getPlatformMemberLrc(@RequestHeader HttpHeaders headers, @Valid MemberIdAndRoleIdReq levelVO) {
        return WrapperUtil.success(memberLevelConfigService.getPlatformMemberLrc(headers, levelVO));
    }

    /**
     * 查询是否配置会员等级
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 是否配置
     */
    @GetMapping("/level/isConfiguration")
    public WrapperResp<Boolean> getPlatformMemberLrc(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberLevelConfigService.isConfiguration(headers, idVO));
    }

    /**
     * 查询上级配置当前会员等级
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 是否配置
     */
    @GetMapping("/level/subConfiguration")
    public WrapperResp<Boolean> subConfiguration(@RequestHeader HttpHeaders headers, @Valid ValidateIdReq idVO) {
        return WrapperUtil.success(memberLevelConfigService.subConfiguration(headers, idVO));
    }

}
