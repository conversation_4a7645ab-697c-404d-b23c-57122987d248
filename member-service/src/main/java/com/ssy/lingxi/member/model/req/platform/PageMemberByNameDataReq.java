package com.ssy.lingxi.member.model.req.platform;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 根据会员名称分页查询会员列表VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-3-10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageMemberByNameDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -131769202446324718L;

    /**
     * 会员名称
     */
    private String memberName;
}
