package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.member.handler.annotation.DateStringFormatAnnotation;
import com.ssy.lingxi.member.handler.annotation.EnableDisableStatusAnno;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 会员入库资料 - 资质证明文件接口参数Vo
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-19
 */
@Data
public class MemberQualityReq implements Serializable {
    private static final long serialVersionUID = -7190733829668515580L;

    /**
     * 文件Url
     */
    @NotBlank(message = "资质文件Url不能为空")
    @Size(max = 400, message = "资质文件Url最长400个字符")
    private String url;

    /**
     * 文件名称
     */
    @NotBlank(message = "资质文件名称不能为空")
    @Size(max = 255, message = "资质文件名称最长255个字符")
    private String name;

    /**
     * 有效日期，格式为yyyy-MM-dd，当permanent=0时必填
     */
    @DateStringFormatAnnotation(message = "资质文件有效日期格式错误")
    private String expireDay;

    /**
     * 是否长期有效，0-否，1-是
     */
    @EnableDisableStatusAnno(message = "'是否长期有效'取值范围为0-1")
    private Integer permanent;

    /**
     * （后端使用）会员资质证明版本，定义在MemberDetailVersionEnum中，0-新增的待审核通过的版本，1-正在使用的版本
     */
    private Integer version;
}
