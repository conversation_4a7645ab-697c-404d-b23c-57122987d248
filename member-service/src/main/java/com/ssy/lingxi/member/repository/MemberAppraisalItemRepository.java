package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.appraisal.MemberAppraisalItemDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员考评项目Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Repository
public interface MemberAppraisalItemRepository extends JpaRepository<MemberAppraisalItemDO, Long>, JpaSpecificationExecutor<MemberAppraisalItemDO> {

    void deleteByAppraisalId(Long id);

    List<MemberAppraisalItemDO> findAllByAppraisalId(Long id);
}
