package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberQualityDO;
import com.ssy.lingxi.member.model.req.validate.MemberQualityReq;
import com.ssy.lingxi.member.model.resp.validate.MemberQualityResp;

import java.util.List;

/**
 * 会员资质（文件）基础服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-22
 */
public interface IBaseMemberQualificationService {
    /**
     * 检查会员资质，并更新relationDo关联的MemberQualityDO中的信息（已保存，调用方需要保存relationDO）
     * @param relationDO 会员关系
     * @param qualityList 资质文件列表
     */
    void checkAndSaveMemberQualities(MemberRelationDO relationDO, List<MemberQualityReq> qualityList);

    /**
     * 检查会员资质，调用方要设置MemberRelationDO并保存
     * @param qualityList 资质文件接口参数列表
     * @return 资质文件
     */
    WrapperResp<List<MemberQualityDO>> checkMemberQualities(List<MemberQualityReq> qualityList);

    /**
     * 查询会员资质列表
     * @param relationDO 会员关系
     * @return 查询结果
     */
    List<MemberQualityResp> findMemberQualities(MemberRelationDO relationDO);

    /**
     * 查询会员会员变革的资质列表
     *
     * @param relationDO 会员关系
     * @return 查询结果
     */
    List<MemberQualityResp> findMemberChangeQualities(MemberRelationDO relationDO);
}
