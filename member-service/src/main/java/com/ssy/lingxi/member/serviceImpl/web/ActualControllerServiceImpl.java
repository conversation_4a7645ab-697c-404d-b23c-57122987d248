package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.DataSourceEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.api.member.ActualControllerSyncReq;
import com.ssy.lingxi.common.model.req.api.member.CustomerCreditSync;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.BigDecimalUtil;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.rest.model.req.eos.PushCorporationAndLineOfCreditReq;
import com.ssy.lingxi.component.rest.model.req.eos.PushLinfoOfCreditReq;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.member.api.model.resp.MemberInfoResp;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.entity.do_.basic.ActualControllerDO;
import com.ssy.lingxi.member.entity.do_.basic.CorporationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.model.req.maintenance.ActualControllerBindOrChangeReq;
import com.ssy.lingxi.member.model.req.maintenance.ActualControllerPageReq;
import com.ssy.lingxi.member.model.req.maintenance.ActualControllerSaveOrUpdateReq;
import com.ssy.lingxi.member.model.req.maintenance.EditActualControllerLineOfCreditReq;
import com.ssy.lingxi.member.model.resp.customer.ActualControllerPageResp;
import com.ssy.lingxi.member.model.resp.customer.ActualControllerParticularsResp;
import com.ssy.lingxi.member.model.resp.customer.CorporationDetailResp;
import com.ssy.lingxi.member.repository.ActualControllerRepository;
import com.ssy.lingxi.member.repository.CorporationRepository;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.IMemberService;
import com.ssy.lingxi.member.service.web.IActualControllerService;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.api.model.req.MemberIdsReq;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.EditLineOfCreditReq;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.MemberLineOfCreditResp;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 实控人服务接口
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/7
 */
@Slf4j
@Service
public class ActualControllerServiceImpl implements IActualControllerService {

    @Resource
    private ActualControllerRepository actualControllerRepository;

    @Resource
    private CorporationRepository corporationRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    @Resource
    private IMemberService memberService;

    @Resource
    private IAssetAccountFeign assetAccountFeign;

    @Resource
    private EosApiService eosApiService;


    /**
     * 分页查询实控人
     *
     * @param pageReq 请求参数
     * @return 实控人
     */
    @Override
    public PageDataResp<ActualControllerPageResp> page(UserLoginCacheDTO loginUser, ActualControllerPageReq pageReq) {
        Specification<ActualControllerDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            if (StringUtils.hasLength(pageReq.getName())) {
                list.add(criteriaBuilder.like(root.get(ActualControllerDO.Fields.name).as(String.class), "%" + pageReq.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(pageReq.getCurrent() - 1, pageReq.getPageSize(), Sort.by("id").descending());
        Page<ActualControllerDO> pageData = actualControllerRepository.findAll(spec, page);

        if (pageData.getTotalElements() <= 0) {
            return new PageDataResp<>();
        }

        List<ActualControllerDO> resultList = pageData.getContent();

        Map<Long, List<String>> actualControllerCorporationMap = corporationRepository.findAllByActualControllerIdIn(resultList.stream().map(ActualControllerDO::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(CorporationDO::getActualControllerId, Collectors.mapping(CorporationDO::getName, Collectors.toList())));

        return new PageDataResp<>(pageData.getTotalElements(), resultList.stream().map(v -> buildActualControllerPageResp(v, actualControllerCorporationMap, pageReq.getSimpleQueryFlag())).collect(Collectors.toList()));
    }

    /**
     * 修改实控人信息
     *
     * @param req 接口参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdate(UserLoginCacheDTO sysUser, ActualControllerSaveOrUpdateReq req) {
        if (Objects.isNull(req.getId())) {
            String actualControllerCode = "SCA" + redisUtils.getSerialNumberByDay(MemberRedisConstant.ACTUAL_CONTROLLER_PREFIX, 4, RedisConstant.REDIS_USER_INDEX);

            ActualControllerDO actualControllerDO = new ActualControllerDO();
            actualControllerDO.setCode(actualControllerCode);
            actualControllerDO.setName(req.getName());
            actualControllerDO.setPhone(req.getPhone());
            actualControllerDO.setIdCardNo(req.getIdCardNo());
            actualControllerDO.setIdCardFront(req.getIdCardFront());
            actualControllerDO.setIdCardBack(req.getIdCardBack());
            actualControllerRepository.saveAndFlush(actualControllerDO);
            return;
        }
        ActualControllerDO actualControllerDO = actualControllerRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));
        actualControllerDO.setName(req.getName());
        actualControllerDO.setPhone(req.getPhone());
        actualControllerDO.setIdCardNo(req.getIdCardNo());
        actualControllerDO.setIdCardFront(req.getIdCardFront());
        actualControllerDO.setIdCardBack(req.getIdCardBack());
        List<MemberLineOfCreditResp> assetAccountResps = null;
        List<MemberInfoResp> memberInfoResps = null;
        List<EditLineOfCreditReq> editLineOfCreditReqs = null;
        List<CorporationDO> corporationDOS = null;
        ActualControllerParticularsResp actualControllerParticularsResp = BeanUtil.copyProperties(actualControllerDO, ActualControllerParticularsResp.class);
            if(!actualControllerDO.getIdCardNo().equals(req.getIdCardFront())){
                actualControllerDO.setCreditQuota(BigDecimal.ZERO);
                corporationDOS = corporationRepository.findAllByActualControllerId(actualControllerDO.getId());
                if(!CollectionUtils.isEmpty(corporationDOS)) {
                    actualControllerParticularsResp.setCorporationDetailResps(null);
                    List<Long> corporationIds = corporationDOS.stream().map(CorporationDO::getId).collect(Collectors.toList());
                    //3、获取企业下的客户信息
                    memberInfoResps = memberService.findByCorporationIds(corporationIds);
                    List<Long> memberIds = memberInfoResps.stream().map(MemberInfoResp::getId).collect(Collectors.toList());
                    MemberIdsReq memberIdsReq = new MemberIdsReq();
                    memberIdsReq.setMemberIds(memberIds);
                    WrapperResp<List<MemberLineOfCreditResp>> assetAccountRespWrapperResp = assetAccountFeign.findByMemberIds(memberIdsReq);
                    assetAccountResps = WrapperUtil.getDataOrThrow(assetAccountRespWrapperResp);
                    //先修改每个会员的授信额度
                    assetAccountResps.stream().forEach(assetAccountResp -> {
                        assetAccountResp.setLineOfCredit(BigDecimal.ZERO);
                        assetAccountResp.setOnlineUnusedRatio(0);
                    });
                    editLineOfCreditReqs = BeanUtil.copyToList(assetAccountResps, EditLineOfCreditReq.class);
                    editLineOfCreditReqs.stream().forEach(editLineOfCreditReq -> {
                        editLineOfCreditReq.setLineOfCredit(BigDecimal.ZERO);
                        editLineOfCreditReq.setOnlineUnusedRatio(0);
                    });
            }
        }
        //如果身份证号码有变更，就认为是变更了实控人
        pushActualControllerAndCreditToEos(actualControllerDO, corporationDOS, memberInfoResps, assetAccountResps, actualControllerDO.getName()+",实控人变更");
        assetAccountFeign.editLineOfCredit(editLineOfCreditReqs);
        actualControllerRepository.saveAndFlush(actualControllerDO);
    }

    /**
     * 保存或更新实控人信息
     * @param loginUser 登录用户
     * @param req 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindOrChange(UserLoginCacheDTO loginUser, ActualControllerBindOrChangeReq req) {
        log.info("绑定或变更实控人：{}", JsonUtil.toJson(req));

        MemberRelationDO relationDO = relationRepository.findById(req.getRelationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST));

        MemberDO memberDO = relationDO.getSubMember();

        if (Objects.isNull(memberDO.getCorporationId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST);
        }
        CorporationDO corporationDO = corporationRepository.findById(memberDO.getCorporationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST));
        List<CorporationDO> corporationDOList = null;
        List<MemberLineOfCreditResp> assetAccountResps = null;
        List<MemberInfoResp> memberInfoResps = null;
        List<EditLineOfCreditReq> editLineOfCreditReqs = null;
        ActualControllerDO actualControllerDO = null;
        // 新实控人
        if (Objects.isNull(req.getActualControllerId())) {
            String actualControllerCode = "SCA" + redisUtils.getSerialNumberByDay(MemberRedisConstant.ACTUAL_CONTROLLER_PREFIX, 4, RedisConstant.REDIS_USER_INDEX);
            actualControllerDO = new ActualControllerDO();
            actualControllerDO.setCode(actualControllerCode);
            actualControllerDO.setName(req.getName());
            actualControllerDO.setPhone(req.getPhone());
            actualControllerDO.setIdCardNo(req.getIdCardNo());
            actualControllerDO.setIdCardFront(req.getIdCardFront());
            actualControllerDO.setIdCardBack(req.getIdCardBack());

            corporationDO.setActualControllerId(actualControllerDO.getId());
            corporationDO.setActualControllerCode(actualControllerCode);
        }else{
            // 绑定已存在实控人
            actualControllerDO  = actualControllerRepository.findById(req.getActualControllerId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ACTUAL_CONTROLLER_NOT_EXIST));
            corporationDO.setActualControllerId(actualControllerDO.getId());
            corporationDO.setActualControllerCode(actualControllerDO.getCode());
        }
        // 绑定同一个实控组的其他企业
        corporationDOList = corporationRepository.findAllByGroupIdentifier(corporationDO.getGroupIdentifier()).stream().filter(v -> !v.getId().equals(corporationDO.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(corporationDOList)) {
            for (CorporationDO corporation : corporationDOList) {
                corporation.setActualControllerId(actualControllerDO.getId());
                corporation.setActualControllerCode(actualControllerDO.getCode());
            }
            List<Long> corporationIds = corporationDOList.stream().map(CorporationDO::getId).collect(Collectors.toList());
            memberInfoResps = memberService.findByCorporationIds(corporationIds);
            //获取被变更企业的会员id列表
            //List<Long> memberIds = memberInfoResps.stream().filter(memberInfoResp -> corporationDO.getId().equals(memberInfoResp.getCorporationId())).map(MemberInfoResp::getId).collect(Collectors.toList());
            List<Long> memberIds = memberInfoResps.stream().map(MemberInfoResp::getId).collect(Collectors.toList());
            //只要被变更的企业下的会员
            List<Long> changesMemberIds = memberInfoResps.stream().map(MemberInfoResp::getId).collect(Collectors.toList());
            MemberIdsReq memberIdsReq = new MemberIdsReq();
            memberIdsReq.setMemberIds(memberIds);
            WrapperResp<List<MemberLineOfCreditResp>> assetAccountRespWrapperResp = assetAccountFeign.findByMemberIds(memberIdsReq);
            assetAccountResps = WrapperUtil.getDataOrThrow(assetAccountRespWrapperResp);
            //先修改每个会员的授信额度
            for (MemberLineOfCreditResp assetAccountResp : assetAccountResps) {
                if(changesMemberIds.contains(assetAccountResp.getMemberId())) {
                    assetAccountResp.setLineOfCredit(BigDecimal.ZERO);
                    assetAccountResp.setOnlineUnusedRatio(null);
                }
            }
            editLineOfCreditReqs = BeanUtil.copyToList(assetAccountResps, EditLineOfCreditReq.class);
        }
        pushActualControllerAndCreditToEos(actualControllerDO, corporationDOList, memberInfoResps, assetAccountResps, actualControllerDO.getName()+", 企业变更实控人");
        actualControllerRepository.saveAndFlush(actualControllerDO);
        corporationRepository.saveAll(corporationDOList);
        if(!CollectionUtils.isEmpty(memberInfoResps)) {
            assetAccountFeign.editLineOfCredit(editLineOfCreditReqs);
        }
    }

    private void pushActualController(ActualControllerDO actualControllerDO) {
        // 推送数据
        PushCorporationAndLineOfCreditReq corporationPushReq = new PushCorporationAndLineOfCreditReq();
        corporationPushReq.setDjh(actualControllerDO.getCode());
        corporationPushReq.setSjkzrsfzh(actualControllerDO.getIdCardNo());
        corporationPushReq.setSjkzrsfzzmurl(actualControllerDO.getIdCardBack());
        corporationPushReq.setSjkzrsfzfmurl(actualControllerDO.getIdCardFront());
        corporationPushReq.setSjkzrlxdh(actualControllerDO.getPhone());
        corporationPushReq.setSjkzrxm(actualControllerDO.getName());
        eosApiService.pushCorporationAndLineOfCredit(corporationPushReq);
    }

    /**
     * 同步实控人信息
     * @param req 实控人信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncActualController(ActualControllerSyncReq req) {
        log.info("开始同步实控人信息：{}", req);
        List<String> corporationCodes = req.getCustomerCreditSyncs().stream().map(CustomerCreditSync::getCorporationCode).collect(Collectors.toList());

        List<CorporationDO> corporationDOList = corporationRepository.findAllByCodeIn(corporationCodes);

        Set<String> existCorporationCodes = corporationDOList.stream().map(CorporationDO::getCode).collect(Collectors.toSet());
        if (existCorporationCodes.size() != corporationCodes.size()) {
            Collection<String> notExistCorporationCodes = CollUtil.subtract(corporationCodes, existCorporationCodes);
            throw new BusinessException("主客户编码不存在" + notExistCorporationCodes);
        }

        ActualControllerDO actualControllerDO = actualControllerRepository.findFirstByCode(req.getCode());
        actualControllerDO = Optional.ofNullable(actualControllerDO).orElse(new ActualControllerDO());
        actualControllerDO.setCode(req.getCode());
        actualControllerDO.setName(req.getName());
        actualControllerDO.setPhone(req.getPhone());
        actualControllerDO.setIdCardNo(req.getIdCardNo());
        actualControllerDO.setIdCardFront(req.getIdCardFront());
        actualControllerDO.setIdCardBack(req.getIdCardBack());
        if(req.getCreditQuota() != null){
            actualControllerDO.setCreditQuota(req.getCreditQuota().multiply(BigDecimal.valueOf(1000)));
        }
        actualControllerDO.setValidStartTime(DateTimeUtil.parseDate(req.getValidStartTime()).atTime(LocalTime.MIN));
        actualControllerDO.setValidEndTime(DateTimeUtil.parseDate(req.getValidEndTime()).atTime(LocalTime.MIN));
        actualControllerDO.setDataSource(DataSourceEnum.EOS.getCode());

        actualControllerRepository.saveAndFlush(actualControllerDO);

        ActualControllerDO finalActualControllerDO = actualControllerDO;
        corporationDOList.forEach(corporationDO -> {
            corporationDO.setActualControllerId(finalActualControllerDO.getId());
            corporationDO.setActualControllerCode(finalActualControllerDO.getCode());
        });
        corporationRepository.saveAll(corporationDOList);

        List<CustomerCreditSync> customerCreditSyncs = req.getCustomerCreditSyncs();
        if (!CollectionUtils.isEmpty(customerCreditSyncs)) {
            // 同步客户授信
            WrapperResp<Boolean> memberCreditSyncWrapperResp = assetAccountFeign.lineOfCreditSync(req);
            WrapperUtil.getDataOrThrow(memberCreditSyncWrapperResp);
        }
    }


    private ActualControllerPageResp buildActualControllerPageResp(ActualControllerDO actualControllerDO, Map<Long, List<String>> actualControllerCorporationMap, Integer simpleQueryFlag) {
        ActualControllerPageResp actualControllerPageResp = new ActualControllerPageResp();
        actualControllerPageResp.setId(actualControllerDO.getId());
        actualControllerPageResp.setName(actualControllerDO.getName());
        if (!Objects.equals(simpleQueryFlag, CommonBooleanEnum.YES.getCode())) {
            List<String> corporationNames = actualControllerCorporationMap.get(actualControllerDO.getId());
            if (!CollectionUtils.isEmpty(corporationNames)) {
                actualControllerPageResp.setCertifiedCorporationNames(String.join("/", corporationNames));
            }
            actualControllerPageResp.setIdCardNo(actualControllerDO.getIdCardNo());
            actualControllerPageResp.setPhone(actualControllerDO.getPhone());
            actualControllerPageResp.setCreditQuota(actualControllerDO.getCreditQuota());
            actualControllerPageResp.setValidStartTime(DateTimeUtil.formatDateTime(actualControllerDO.getValidStartTime()));
            actualControllerPageResp.setValidEndTime(DateTimeUtil.formatDateTime(actualControllerDO.getValidEndTime()));
            actualControllerPageResp.setIdCardFront(actualControllerDO.getIdCardFront());
            actualControllerPageResp.setIdCardBack(actualControllerDO.getIdCardBack());
        }
        return actualControllerPageResp;
    }


    @Override
    public ActualControllerParticularsResp getParticulars(UserLoginCacheDTO sysUser, CommonIdReq req) {
        //1、获取实控人信息
        //2、获取企业信息
        ActualControllerDO actualControllerDO = actualControllerRepository.findById(req.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));
        ActualControllerParticularsResp actualControllerParticularsResp = BeanUtil.copyProperties(actualControllerDO, ActualControllerParticularsResp.class);
        List<CorporationDO> corporationDOS = corporationRepository.findAllByActualControllerId(actualControllerDO.getId());
        if(CollectionUtils.isEmpty(corporationDOS)) {
            actualControllerParticularsResp.setCorporationDetailResps(null);
            return actualControllerParticularsResp;
        }
        List<Long> corporationIds = corporationDOS.stream().map(CorporationDO::getId).collect(Collectors.toList());
        Map<Long, CorporationDO> corporationDOMap = corporationDOS.stream().collect(Collectors.toMap(CorporationDO::getId, Function.identity(), (v1, v2) -> v2));
        //3、获取企业下的客户信息
        List<MemberInfoResp> memberInfoResps = memberService.findByCorporationIds(corporationIds);
        List<Long> memberIds = memberInfoResps.stream().map(MemberInfoResp::getId).collect(Collectors.toList());
        MemberIdsReq memberIdsReq = new MemberIdsReq();
        memberIdsReq.setMemberIds(memberIds);
        WrapperResp<List<MemberLineOfCreditResp>> assetAccountRespWrapperResp = assetAccountFeign.findByMemberIds(memberIdsReq);
        Map<Long,Long> corporationIdAndMemberIdMap = memberInfoResps.stream().collect(Collectors.toMap(MemberInfoResp::getId,MemberInfoResp::getCorporationId, (v1, v2) -> v2));
        List<MemberLineOfCreditResp> assetAccountResps = WrapperUtil.getDataOrThrow(assetAccountRespWrapperResp);
        Map<Long, BigDecimal> corporationLineOfCreditMap = assetAccountResps.stream()
                .filter(resp -> corporationIdAndMemberIdMap.containsKey(resp.getMemberId()))
                .collect(Collectors.toMap(
                        resp -> corporationIdAndMemberIdMap.get(resp.getMemberId()),
                        resp -> Optional.ofNullable(resp.getLineOfCredit())
                                .orElse(BigDecimal.ZERO),
                        BigDecimal::add,
                        HashMap::new));
        List<CorporationDetailResp> corporationDetailRespList = corporationDOS.stream().map(corporationDO -> {
            CorporationDetailResp corporationDetailResp = BeanUtil.copyProperties(corporationDO, CorporationDetailResp.class);
            corporationDetailResp.setLinfOfCreditTotalQuota(corporationLineOfCreditMap.get(corporationDetailResp.getId()));
            return corporationDetailResp;
        }).collect(Collectors.toList());
        BigDecimal lineOfCredit = assetAccountResps.stream().map(MemberLineOfCreditResp::getLineOfCredit).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add);
        Map<Long, CorporationDO> memberIdAndcorporationDOMap = memberInfoResps.stream().filter(e->{return e.getCorporationId() != null;}).collect(Collectors.toMap(e -> e.getId(), e -> corporationDOMap.get(e.getCorporationId()), (v1, v2) -> v2));
        assetAccountResps.stream().forEach(e -> {
            CorporationDO corporationDO = memberIdAndcorporationDOMap.get(e.getMemberId());
            if(corporationDO != null) {
                e.setUnifiedSocialCode(corporationDO.getUnifiedSocialCode());
                e.setCorporationName(corporationDO.getName());
                e.setCorporationId(corporationDO.getId());
            }
        });
        actualControllerParticularsResp.setCorporationDetailResps(corporationDetailRespList);
        actualControllerParticularsResp.setMemberLineOfCreditResps(assetAccountResps);
        actualControllerParticularsResp.setUnuseCreditQuota(BigDecimalUtil.subtract(actualControllerDO.getCreditQuota(), lineOfCredit));
        return actualControllerParticularsResp;
    }

    /**
     * 编辑实控人授信额度
     * @param req
     * @return
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Boolean editActualControllerLineOfCredit(EditActualControllerLineOfCreditReq req) {
        if(BigDecimalUtil.isNegative(req.getCreditQuota())){
            throw new BusinessException(ResponseCodeEnum.MC_MS_ACTUAL_CONTROLLER_CREDIT_QUOTA_LESS_THAN_ZERO);
        }
        ActualControllerDO actualControllerDO = actualControllerRepository.findById(req.getActualControllerId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));
        List<CorporationDO> corporationDOS = corporationRepository.findAllByActualControllerId(actualControllerDO.getId());
        if(CollectionUtils.isEmpty(corporationDOS)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST);
        }
        List<Long> corporationIds = corporationDOS.stream().map(CorporationDO::getId).collect(Collectors.toList());
        List<MemberInfoResp> memberInfoResps = memberService.findByCorporationIds(corporationIds);
        List<Long> memberIds = memberInfoResps.stream().map(MemberInfoResp::getId).collect(Collectors.toList());
        MemberIdsReq memberIdsReq = new MemberIdsReq();
        memberIdsReq.setMemberIds(memberIds);
;       WrapperResp<List<MemberLineOfCreditResp>> assetAccountRespWrapperResp = assetAccountFeign.findByMemberIds(memberIdsReq);
        List<MemberLineOfCreditResp> assetAccountResps = WrapperUtil.getDataOrThrow(assetAccountRespWrapperResp);
        //分配后的额度是否大于总额
        BigDecimal totalAllocatedCredit = assetAccountResps.stream()
                .map(MemberLineOfCreditResp::getLineOfCredit)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if(BigDecimalUtil.isGreaterThan(totalAllocatedCredit, req.getCreditQuota())){
            throw new BusinessException(ResponseCodeEnum.MC_MS_ACTUAL_CONTROLLER_CREDIT_QUOTA_LESS_THAN_ALLOCATED);
        }
        //组装数据
        PushCorporationAndLineOfCreditReq corporationPushReq = new PushCorporationAndLineOfCreditReq();
        corporationPushReq.setBz(req.getRemark());
        corporationPushReq.setDjh(actualControllerDO.getCode());
        corporationPushReq.setSjkzrsfzh(actualControllerDO.getIdCardNo());
        corporationPushReq.setSjkzrsfzzmurl(actualControllerDO.getIdCardBack());
        corporationPushReq.setSjkzrsfzfmurl(actualControllerDO.getIdCardFront());
        corporationPushReq.setSjkzrlxdh(actualControllerDO.getPhone());
        if(req.getCreditQuota() != null){
            //g转kg
            corporationPushReq.setSxed(req.getCreditQuota().divide(BigDecimal.valueOf(1000)));
        }
        corporationPushReq.setSxyxqq(DateTimeUtil.formatDate(req.getValidStartTime()));
        corporationPushReq.setSxyxqz(DateTimeUtil.formatDate(req.getValidEndTime()));
        corporationPushReq.setSjkzrxm(actualControllerDO.getName());
        Map<Long, CorporationDO> corporationIdAndCorporationMap = corporationDOS.stream().collect(Collectors.toMap(CorporationDO::getId, Function.identity(), (v1, v2) -> v2));
        Map<Long, CorporationDO> memberIdAndCorporationMap = memberInfoResps.stream().collect(Collectors.toMap(MemberInfoResp::getId, memberInfoResp -> {
            return corporationIdAndCorporationMap.get(memberInfoResp.getCorporationId());
        }, (v1, v2) -> v2));
        List<PushLinfoOfCreditReq> pushLinfoOfCreditReqs = assetAccountResps.stream().map(e -> {
            PushLinfoOfCreditReq pushLinfoOfCreditReq = new PushLinfoOfCreditReq();
            pushLinfoOfCreditReq.setKhbmb(e.getMemberCode());
            pushLinfoOfCreditReq.setKhmcb(e.getMemberName());
            //g 转 kg
            if(e.getLineOfCredit() != null){
                pushLinfoOfCreditReq.setSxedb(e.getLineOfCredit().divide(BigDecimal.valueOf(1000)));
            }
            pushLinfoOfCreditReq.setZkhbmb(memberIdAndCorporationMap.get(e.getMemberId()).getCode());
            pushLinfoOfCreditReq.setZkhmcb(memberIdAndCorporationMap.get(e.getMemberId()).getName());
            return pushLinfoOfCreditReq;
        }).collect(Collectors.toList());
        corporationPushReq.setData(pushLinfoOfCreditReqs);
        eosApiService.pushCorporationAndLineOfCredit(corporationPushReq);
        //如果修改了有效期，则需要修改每个会员的授信信息
        actualControllerDO.setCreditQuota(req.getCreditQuota());
        if(req.getValidStartTime() != null && req.getValidEndTime() != null) {
            actualControllerDO.setValidStartTime(req.getValidStartTime().atTime(LocalTime.MIN));
            actualControllerDO.setValidEndTime(req.getValidEndTime().atTime(LocalTime.MIN));
            List<EditLineOfCreditReq> editLineOfCreditReqs = BeanUtil.copyToList(assetAccountResps, EditLineOfCreditReq.class);
            editLineOfCreditReqs.stream().forEach(editLineOfCreditReq -> {
                editLineOfCreditReq.setLineOfCreditUseStartTime(req.getValidStartTime());
                editLineOfCreditReq.setLineOfCreditUseEndTime(req.getValidEndTime());
            });
            assetAccountFeign.editLineOfCredit(editLineOfCreditReqs);
        }

        actualControllerRepository.saveAndFlush(actualControllerDO);
        return true;
    }


    /**
     * 编辑会员授信额度
     * @param req
     * @return
     */
    @Transactional
    @Override
    public Boolean editMemberLineOfCredit(EditActualControllerLineOfCreditReq req) {
        ActualControllerDO actualControllerDO = actualControllerRepository.findById(req.getActualControllerId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED));
        actualControllerDO.setCreditQuota(req.getCreditQuota());
        List<CorporationDO> corporationDOS = corporationRepository.findAllByActualControllerId(actualControllerDO.getId());
        if(CollectionUtils.isEmpty(corporationDOS)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CORPORATION_DOES_NOT_EXIST);
        }
        List<Long> corporationIds = corporationDOS.stream().map(CorporationDO::getId).collect(Collectors.toList());
        List<MemberInfoResp> memberInfoResps = memberService.findByCorporationIds(corporationIds);
        List<Long> memberIds = memberInfoResps.stream().map(MemberInfoResp::getId).collect(Collectors.toList());
        MemberIdsReq memberIdsReq = new MemberIdsReq();
        memberIdsReq.setMemberIds(memberIds);
        WrapperResp<List<MemberLineOfCreditResp>> assetAccountRespWrapperResp = assetAccountFeign.findByMemberIds(memberIdsReq);
        List<MemberLineOfCreditResp> assetAccountResps = WrapperUtil.getDataOrThrow(assetAccountRespWrapperResp);
        //先修改每个会员的授信额度
        Map<Long, MemberLineOfCreditResp> memberLineOfCreditRespMap = assetAccountResps.stream().collect(Collectors.toMap(MemberLineOfCreditResp::getMemberId, Function.identity(), (v1, v2) -> v2));
        req.getEditMemberLineOfCreditReqs().stream().forEach(editMemberLineOfCreditReq -> {
            MemberLineOfCreditResp memberLineOfCreditResp = memberLineOfCreditRespMap.get(editMemberLineOfCreditReq.getMemberId());
            if(memberLineOfCreditResp == null){
                throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
            }
            if(BigDecimalUtil.isNegative(editMemberLineOfCreditReq.getCreditQuota())){
                throw new BusinessException(ResponseCodeEnum.MC_MS_ACTUAL_CONTROLLER_CREDIT_QUOTA_LESS_THAN_ZERO);
            }
            BigDecimal lineOfCredit = BigDecimalUtil.nullToZero(memberLineOfCreditResp.getLineOfCredit());
            BigDecimal memberCreditQuota = BigDecimalUtil.nullToZero(editMemberLineOfCreditReq.getCreditQuota());
            BigDecimal unUsedLineOfCredit = BigDecimalUtil.add(lineOfCredit, memberLineOfCreditResp.getUsedlinfeOfCredit());
            BigDecimal maxCreditQuota = BigDecimalUtil.add(memberLineOfCreditResp.getUsedlinfeOfCredit(), unUsedLineOfCredit);
            //如果编辑的额度小于当前额度，并且已使用额度为0或负数，则不允许编辑
            if(BigDecimalUtil.isGreaterThan(lineOfCredit, memberCreditQuota) && BigDecimalUtil.isNegative(maxCreditQuota)){
                throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_CREDIT_QUOTA_LESS_THAN_USED);
            }
            memberLineOfCreditResp.setLineOfCredit(editMemberLineOfCreditReq.getCreditQuota());
            memberLineOfCreditResp.setOnlineUnusedRatio(editMemberLineOfCreditReq.getOnlineUsedRatio());
            if(actualControllerDO.getValidStartTime() != null){
                memberLineOfCreditResp.setLineOfCreditUseStartTime(actualControllerDO.getValidStartTime().toLocalDate());
            }
            if(actualControllerDO.getValidEndTime() != null){
                memberLineOfCreditResp.setLineOfCreditUseEndTime(actualControllerDO.getValidEndTime().toLocalDate());
            }
        });
        //分配后的额度是否大于总额
        BigDecimal totalAllocatedCredit = assetAccountResps.stream()
                .map(MemberLineOfCreditResp::getLineOfCredit)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if(BigDecimalUtil.isGreaterThan(totalAllocatedCredit, req.getCreditQuota())){
            throw new BusinessException(ResponseCodeEnum.MC_MS_ACTUAL_CONTROLLER_CREDIT_QUOTA_LESS_THAN_ALLOCATED);
        }
        pushActualControllerAndCreditToEos(actualControllerDO, corporationDOS, memberInfoResps, assetAccountResps, actualControllerDO.getName()+",会员授信额度调整");
        List<EditLineOfCreditReq> editLineOfCreditReqs = BeanUtil.copyToList(memberLineOfCreditRespMap.values(), EditLineOfCreditReq.class);
        WrapperUtil.getDataOrThrow(assetAccountFeign.editLineOfCredit(editLineOfCreditReqs));
        return true;
    }

    private void pushActualControllerAndCreditToEos(ActualControllerDO actualControllerDO, List<CorporationDO> corporationDOS, List<MemberInfoResp> memberInfoResps, List<MemberLineOfCreditResp> assetAccountResps,String remark) {
        //组装数据
        PushCorporationAndLineOfCreditReq corporationPushReq = new PushCorporationAndLineOfCreditReq();
        corporationPushReq.setBz(remark);
        corporationPushReq.setDjh(actualControllerDO.getCode());
        corporationPushReq.setSjkzrsfzh(actualControllerDO.getIdCardNo());
        corporationPushReq.setSjkzrsfzzmurl(actualControllerDO.getIdCardBack());
        corporationPushReq.setSjkzrsfzfmurl(actualControllerDO.getIdCardFront());
        corporationPushReq.setSjkzrlxdh(actualControllerDO.getPhone());
        if(actualControllerDO.getCreditQuota() != null){
            corporationPushReq.setSxed(actualControllerDO.getCreditQuota().divide(BigDecimal.valueOf(1000)));
        }
        corporationPushReq.setSxyxqq(DateTimeUtil.formatDate(actualControllerDO.getValidStartTime()));
        corporationPushReq.setSxyxqz(DateTimeUtil.formatDate(actualControllerDO.getValidEndTime()));
        corporationPushReq.setSjkzrxm(actualControllerDO.getName());
        if(!CollectionUtils.isEmpty(corporationDOS)){
            Map<Long, CorporationDO> corporationIdAndCorporationMap = corporationDOS.stream().collect(Collectors.toMap(CorporationDO::getId, Function.identity(), (v1, v2) -> v2));
            if(!CollectionUtils.isEmpty(memberInfoResps)){
                Map<Long, CorporationDO> memberIdAndCorporationMap = memberInfoResps.stream().collect(Collectors.toMap(MemberInfoResp::getId, memberInfoResp -> {
                    return corporationIdAndCorporationMap.get(memberInfoResp.getCorporationId());
                }, (v1, v2) -> v2));
                List<PushLinfoOfCreditReq> pushLinfoOfCreditReqs = assetAccountResps.stream().map(e -> {
                    PushLinfoOfCreditReq pushLinfoOfCreditReq = new PushLinfoOfCreditReq();
                    pushLinfoOfCreditReq.setKhbmb(e.getMemberCode());
                    pushLinfoOfCreditReq.setKhmcb(e.getMemberName());
                    if(e.getLineOfCredit() != null){
                        pushLinfoOfCreditReq.setSxedb(e.getLineOfCredit().divide(BigDecimal.valueOf(1000)));
                    }
                    pushLinfoOfCreditReq.setZkhbmb(memberIdAndCorporationMap.get(e.getMemberId()).getCode());
                    pushLinfoOfCreditReq.setZkhmcb(memberIdAndCorporationMap.get(e.getMemberId()).getName());
                    return pushLinfoOfCreditReq;
                }).collect(Collectors.toList());
                corporationPushReq.setData(pushLinfoOfCreditReqs);
            }
        }
        eosApiService.pushCorporationAndLineOfCredit(corporationPushReq);
    }

}
