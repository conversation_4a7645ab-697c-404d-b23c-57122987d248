package com.ssy.lingxi.member.controller.web;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 是否开启人脸识别参数
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/13
 */
@Data
public class FaceRecognitionFlagReq implements Serializable {
    private static final long serialVersionUID = -6834201113982597410L;

    /**
     * 是否启用人脸识别
     */
    @NotNull(message = "是否启用人脸识别不能为空")
    private Boolean enableFlag;

}
