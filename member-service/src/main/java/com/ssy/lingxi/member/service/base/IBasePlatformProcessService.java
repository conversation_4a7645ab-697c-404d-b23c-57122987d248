package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.member.entity.bo.ProcessBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;

/**
 * 角色关联的平台会员审核（会员注册）流程相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-11-10
 */
public interface IBasePlatformProcessService {
    /**
     * 查询角色关联的平台会员审核（会员注册）流程
     * @param role 会员角色
     * @return 流程的processKey
     */
    ProcessBO findRolePlatformProcess(MemberRoleDO role);
}
