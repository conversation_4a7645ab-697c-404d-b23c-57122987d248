package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.enums.MemberOuterStatusEnum;
import com.ssy.lingxi.member.enums.PlatformInnerStatusEnum;
import com.ssy.lingxi.member.enums.PlatformMemberMaterialInventoryModeEnum;
import com.ssy.lingxi.member.enums.PlatformMemberPositionInventoryModeEnum;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberCustomerQueryReq;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberMaterialInventoryUpdateReq;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberPositionInventoryRulesQueryDataReq;
import com.ssy.lingxi.member.model.req.maintenance.PlatformMemberPositionInventoryUpdateReq;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformMemberCustomerResp;
import com.ssy.lingxi.member.model.resp.maintenance.PlatformPageQueryPositionInventoryRulesMemberResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IPlatformMemberPositionInventoryRulesService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 平台后台 - 平台规则 - 会员仓位同步物料库存规则相关业务实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/25
 */
@Service
public class PlatformMemberPositionInventoryRulesServiceImpl implements IPlatformMemberPositionInventoryRulesService {

    @Resource
    private IBaseMemberCacheService memberCacheService;
    @Resource
    private MemberRelationRepository relationRepository;

    /**
     * 分页、模糊查询会员信息列表
     *
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<PlatformPageQueryPositionInventoryRulesMemberResp> pageMembers(HttpHeaders headers, PlatformMemberPositionInventoryRulesQueryDataReq queryVO) {
        //检验用户登录源
        memberCacheService.needLoginFromManagePlatform(headers);

        Pageable page = PageRequest.of(queryVO.getCurrent() - 1, queryVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(getSpecification(queryVO), page);

        List<MemberRelationDO> content = pageList.getContent();
        //如果没数据则直接返回
        if (CollectionUtils.isEmpty(content)) {
            return new PageDataResp<>(pageList.getTotalElements(), new ArrayList<>());

        }
        //封装响应参数
        List<PlatformPageQueryPositionInventoryRulesMemberResp> collect = content.stream().map(relationDO -> {
            PlatformPageQueryPositionInventoryRulesMemberResp vo = new PlatformPageQueryPositionInventoryRulesMemberResp();
            vo.setMemberId(relationDO.getSubMemberId());
            vo.setValidateId(relationDO.getId());
            vo.setRoleId(relationDO.getSubRoleId());
            vo.setName(relationDO.getSubMember().getName());
            vo.setMemberType(relationDO.getSubRole().getMemberType());
            vo.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            vo.setRoleName(relationDO.getSubRole().getRoleName());
            vo.setPositionInventoryMode(relationDO.getPositionInventoryMode());
            vo.setPositionInventoryName(relationDO.getPositionInventoryMode() ? PlatformMemberPositionInventoryModeEnum.OPEN.getMessage() : PlatformMemberPositionInventoryModeEnum.CLOSE.getMessage());
            vo.setMaterialInventoryMode(relationDO.getMaterialInventoryMode());
            vo.setMaterialInventoryName(relationDO.getMaterialInventoryMode() ? PlatformMemberMaterialInventoryModeEnum.SYNCHRONOUS.getMessage() : PlatformMemberMaterialInventoryModeEnum.OUT_OF_SYNC.getMessage());
            if (relationDO.getMaterialInventoryMode()) {
                vo.setSynchronousRoleId(relationDO.getSynchronousRoleId());
                vo.setSynchronousRoleName(relationDO.getSynchronousRoleName());
            }
            //从关联的角色查找等级
            vo.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
            return vo;
        }).collect(Collectors.toList());
        return new PageDataResp<>(pageList.getTotalElements(), collect);
    }

    /**
     * 构建查询条件
     *
     * @param queryVO 请求参数
     * @return 返回条件
     */
    private Specification<MemberRelationDO> getSpecification(PlatformMemberPositionInventoryRulesQueryDataReq queryVO) {
        //使用Specification构造模糊查询条件并查询
        return (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级为平台
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            //不查询由会员创建的，未提交平台审核的数据（此时数据的内部状态为 MemberInnerStatusEnum.REGISTERING.getCode()
            list.add(criteriaBuilder.notEqual(root.get("innerStatus").as(Integer.class), PlatformInnerStatusEnum.REGISTERING.getCode()));

            //外部状态审核通过的
            list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode()));

            //会员状态为正常的
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), MemberStatusEnum.NORMAL.getCode()));

            //会员角色类型为供应商
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_PROVIDER.getCode()));

            //会员名称
            Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
            if (StringUtils.hasLength(queryVO.getName())) {
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + queryVO.getName().trim() + "%"));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
    }

    /**
     * 修改仓位仓库模式
     *
     * @param headers  HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @Override
    public void updatePositionInventory(HttpHeaders headers, PlatformMemberPositionInventoryUpdateReq updateVO) {
        //检验用户登录源
        memberCacheService.needLoginFromManagePlatform(headers);

        MemberRelationDO relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(updateVO.getMemberId(), updateVO.getRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if (Objects.isNull(relationDO)) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }
        relationDO.setPositionInventoryMode(updateVO.getPositionInventoryMode());
        relationRepository.saveAndFlush(relationDO);
        //return WrapperUtil.success();
    }

    /**
     * 设置物料仓库模式
     *
     * @param headers  HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @Override
    public void updateMaterialInventory(HttpHeaders headers, PlatformMemberMaterialInventoryUpdateReq updateVO) {
        //检验用户登录源
        memberCacheService.needLoginFromManagePlatform(headers);

        MemberRelationDO relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(updateVO.getMemberId(), updateVO.getRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if (Objects.isNull(relationDO)) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }
        relationDO.setMaterialInventoryMode(updateVO.getMaterialInventoryMode());
        relationDO.setSynchronousRoleId(updateVO.getSynchronousRoleId());
        relationDO.setSynchronousRoleName(updateVO.getSynchronousRoleName());
        relationRepository.saveAndFlush(relationDO);
        //return WrapperUtil.success();
    }

    /**
     * 获取会员全部服务消费者类型名称
     *
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @Override
    public List<PlatformMemberCustomerResp> listMembersCustomer(HttpHeaders headers, PlatformMemberCustomerQueryReq queryVO) {
        //检验用户登录源
        memberCacheService.needLoginFromManagePlatform(headers);

        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            //上级为平台
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            //会员id
            if (NumberUtil.notNullOrZero(queryVO.getMemberId())) {
                list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), queryVO.getMemberId()));
            }
            //会员角色类型为供应商
            Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
            list.add(criteriaBuilder.equal(subRoleJoin.get("roleType").as(Integer.class), RoleTypeEnum.SERVICE_CONSUMER.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        //查询结果
        List<MemberRelationDO> memberRelationDOS = relationRepository.findAll(specification);
        if (!CollectionUtils.isEmpty(memberRelationDOS)) {
            return memberRelationDOS.stream().map(memberRelationDO -> {
                PlatformMemberCustomerResp vo = new PlatformMemberCustomerResp();
                vo.setMemberId(memberRelationDO.getSubMemberId());
                vo.setSynchronousRoleId(memberRelationDO.getSubRoleId());
                vo.setSynchronousRoleName(memberRelationDO.getSubRoleName());
                return vo;
            }).collect(Collectors.toList());

        }
        return new ArrayList<>();
    }
}
