package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.util.ObjectUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.visit.MemberVisitDO;
import com.ssy.lingxi.member.enums.VisitLevelEnum;
import com.ssy.lingxi.member.enums.VisitTypeEnum;
import com.ssy.lingxi.member.model.req.basic.MemberVisitAddOrUpdateRequest;
import com.ssy.lingxi.member.model.req.basic.MemberVisitListDataReq;
import com.ssy.lingxi.member.model.resp.basic.MemberVisitListResp;
import com.ssy.lingxi.member.model.resp.basic.MemberVisitTypeItemResp;
import com.ssy.lingxi.member.model.resp.basic.VisitLevelResp;
import com.ssy.lingxi.member.model.resp.basic.VisitTypeResp;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.repository.MemberVisitRepository;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.service.web.IMemberVisitService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员拜访管理
 * <AUTHOR>
 * @since 2020/6/19
 */
@Service
public class MemberVisitImpl implements IMemberVisitService {
    @Resource
    MemberVisitRepository memberVisitRepository;

    @Resource
    UserRepository userRepository;

    @Resource
    MemberRepository memberRepository;

    @Override
    public PageDataResp<MemberVisitListResp> memberVisitList(MemberVisitListDataReq request, UserLoginCacheDTO sysUser, Integer roleTag) {
        Pageable page = PageRequest.of(request.getCurrent() - 1, request.getPageSize(), Sort.by("updateTime").descending());

        List<Long> memberIds = new ArrayList<>();
        if (NumberUtil.notNullOrZero(roleTag)) {
            memberIds = memberRepository.findAll((root, query, cb) -> {
                List<Predicate> predicateList = new ArrayList<>();
                Join<MemberDO, MemberRoleDO> roleJoin = root.join("memberRoles", JoinType.LEFT);
                predicateList.add(cb.equal(roleJoin.get("roleTag").as(Integer.class), roleTag));
                Predicate[] p = new Predicate[predicateList.size()];
                return query.where(predicateList.toArray(p)).getRestriction();
            }).stream().map(MemberDO::getId).distinct().collect(Collectors.toList());
        }

        List<Long> finalMemberIds = memberIds;
        Page<MemberVisitDO> pageList = memberVisitRepository.findAll((Specification<MemberVisitDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("createMemberId"), sysUser.getMemberId()));
            predicateList.add(cb.equal(root.get("createMemberRoleId"), sysUser.getMemberRoleId()));

            if (StringUtils.isNotEmpty(request.getMemberName()) || NumberUtil.notNullOrZero(roleTag)) {
                Join<MemberVisitDO, MemberDO> memberJoin = root.join("member", JoinType.LEFT);
                if (StringUtils.isNotEmpty(request.getMemberName())) {
                    predicateList.add(cb.like(memberJoin.get("name").as(String.class), "%" + request.getMemberName().trim() + "%"));
                }
                if (NumberUtil.notNullOrZero(roleTag)) {
                    predicateList.add(cb.in(memberJoin.get("id")).value(finalMemberIds));
                }
            }

            if (StringUtils.isNotEmpty(request.getVisitTheme())) {
                predicateList.add(cb.like(root.get("visitTheme"), "%" + request.getVisitTheme() + "%"));
            }

            if (NumberUtil.notNullOrZero(request.getVisitType())) {
                predicateList.add(cb.equal(root.get("visitType"), request.getVisitType()));
            }

            if (NumberUtil.notNullOrZero(request.getVisitLevel())) {
                predicateList.add(cb.equal(root.get("visitLevel"), request.getVisitLevel()));
            }

            if (StringUtils.isNotEmpty(request.getVisitor())) {
                Join<MemberVisitDO, UserDO> memberJoin = root.join("memberUser", JoinType.LEFT);
                predicateList.add(cb.like(memberJoin.get("name").as(String.class), "%" + request.getVisitor().trim() + "%"));
            }

            if (StringUtils.isNotEmpty(request.getPeer())) {
                predicateList.add(cb.like(root.get("peer"), "%" + request.getPeer() + "%"));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<MemberVisitListResp> resultList = pageList.stream().map(memberVisitList -> {
            MemberVisitListResp response = new MemberVisitListResp();
            BeanUtils.copyProperties(memberVisitList, response);
            response.setMemberId(Optional.ofNullable(memberVisitList.getMember()).map(MemberDO::getId).orElse(null));
            response.setMemberName(Optional.ofNullable(memberVisitList.getMember()).map(MemberDO::getName).orElse(""));
            response.setVisitorId(Optional.ofNullable(memberVisitList.getMemberUser()).map(UserDO::getId).orElse(null));
            response.setVisitor(Optional.ofNullable(memberVisitList.getMemberUser()).map(UserDO::getName).orElse(""));
            response.setVisitTypeName(VisitTypeEnum.getNameByCode(NumberUtil.isNullOrZero(memberVisitList.getVisitType()) ? 0 : memberVisitList.getVisitType()));
            response.setVisitLevelName(VisitLevelEnum.getNameByCode(NumberUtil.isNullOrZero(memberVisitList.getVisitLevel()) ? 0 : memberVisitList.getVisitLevel()));
            return response;
        }).collect(Collectors.toList());
        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public MemberVisitListResp memberVisitDetails(Long id, UserLoginCacheDTO sysUser, Integer roleTag) {
        MemberVisitDO memberVisitDO = memberVisitRepository.findById(id).orElse(null);
        if(ObjectUtil.isNull(memberVisitDO)){

            throw new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag)) {
            List<Long> memberIds = memberRepository.findAll((root, query, cb) -> {
                List<Predicate> predicateList = new ArrayList<>();
                Join<MemberDO, MemberRoleDO> roleJoin = root.join("memberRoles", JoinType.LEFT);
                predicateList.add(cb.equal(roleJoin.get("roleTag").as(Integer.class), roleTag));
                Predicate[] p = new Predicate[predicateList.size()];
                return query.where(predicateList.toArray(p)).getRestriction();
            }).stream().map(MemberDO::getId).distinct().collect(Collectors.toList());
            if (!memberIds.contains(memberVisitDO.getMember().getId())) {

                throw new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST);
            }
        }

        MemberVisitListResp response = new MemberVisitListResp();
        BeanUtils.copyProperties(memberVisitDO, response);
        response.setMemberId(Optional.ofNullable(memberVisitDO.getMember()).map(MemberDO::getId).orElse(null));
        response.setMemberName(Optional.ofNullable(memberVisitDO.getMember()).map(MemberDO::getName).orElse(""));
        response.setVisitorId(Optional.ofNullable(memberVisitDO.getMemberUser()).map(UserDO::getId).orElse(null));
        response.setVisitor(Optional.ofNullable(memberVisitDO.getMemberUser()).map(UserDO::getName).orElse(""));
        response.setVisitTypeName(VisitTypeEnum.getNameByCode(NumberUtil.isNullOrZero(memberVisitDO.getVisitType()) ? 0 : memberVisitDO.getVisitType()));
        response.setVisitLevelName(VisitLevelEnum.getNameByCode(NumberUtil.isNullOrZero(memberVisitDO.getVisitLevel()) ? 0 : memberVisitDO.getVisitLevel()));
        return response;
    }

    @Override
    public List<MemberVisitListResp> memberVisitAll(UserLoginCacheDTO sysUser) {
        Long memberId = sysUser.getMemberId();
        Long memberRoleId = sysUser.getMemberRoleId();
        List<MemberVisitDO> list = memberVisitRepository.findByCreateMemberIdAndCreateMemberRoleIdOrderByUpdateTimeDesc(memberId, memberRoleId);
        List<MemberVisitListResp> resultList = list.stream().map(memberVisitList -> {
            MemberVisitListResp response = new MemberVisitListResp();
            BeanUtils.copyProperties(memberVisitList, response);
            response.setMemberId(Optional.ofNullable(memberVisitList.getMember()).map(MemberDO::getId).orElse(null));
            response.setMemberName(Optional.ofNullable(memberVisitList.getMember()).map(MemberDO::getName).orElse(""));
            response.setVisitorId(Optional.ofNullable(memberVisitList.getMemberUser()).map(UserDO::getId).orElse(null));
            response.setVisitor(Optional.ofNullable(memberVisitList.getMemberUser()).map(UserDO::getName).orElse(""));
            response.setVisitTypeName(Optional.ofNullable(memberVisitList.getMemberUser()).map(UserDO::getName).orElse(""));
            return response;
        }).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public void memberVisitAddOrUpdate(MemberVisitAddOrUpdateRequest request, UserLoginCacheDTO sysUser) {
        MemberDO memberDO = memberRepository.findById(request.getMemberId()).orElse(null);
        if (null == memberDO) {
            throw new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST);
        }
        UserDO userDO = userRepository.findById(request.getVisitorId()).orElse(null);
        if (null == userDO) {
            throw new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST);
        }
        MemberVisitDO MemberVisitDO = new MemberVisitDO();
        if(ObjectUtil.isNotNull(request.getId())){
            MemberVisitDO = memberVisitRepository.findById(request.getId()).orElse(null);
            if(ObjectUtil.isNull(MemberVisitDO)){

                throw new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST);
            }
        }else{
            MemberVisitDO.setCreateMemberId(sysUser.getMemberId());
            MemberVisitDO.setCreateMemberRoleId(sysUser.getMemberRoleId());
            MemberVisitDO.setCreateTime(System.currentTimeMillis());
        }
        BeanUtils.copyProperties(request, MemberVisitDO);
        MemberVisitDO.setMember(memberRepository.findById(request.getMemberId()).orElse(null));
        MemberVisitDO.setMemberUser(userRepository.findById(request.getVisitorId()).orElse(null));
        MemberVisitDO.setUpdateTime(System.currentTimeMillis());
        memberVisitRepository.saveAndFlush(MemberVisitDO);

    }

    @Override
    public void memberVisitDelete(CommonIdReq request) {
        MemberVisitDO memberVisitDO = memberVisitRepository.findById(request.getId()).orElse(null);
        if(ObjectUtil.isNull(memberVisitDO)){

            throw new BusinessException(ResponseCodeEnum.RECORDS_DON_T_EXIST);
        }
        memberVisitRepository.deleteById(request.getId());

    }

    @Override
    public MemberVisitTypeItemResp getMemberVisitOfVisitTypeItems(HttpHeaders headers) {
        MemberVisitTypeItemResp itemVO = new MemberVisitTypeItemResp();
        itemVO.setVisitTypes(Arrays.stream(VisitTypeEnum.values()).map(e -> new VisitTypeResp(e.getCode(), e.getMessage())).sorted(Comparator.comparingInt(VisitTypeResp::getVisitType)).collect(Collectors.toList()));
        itemVO.setVisitLevels(Arrays.stream(VisitLevelEnum.values()).map(e -> new VisitLevelResp(e.getCode(), e.getMessage())).sorted(Comparator.comparingInt(VisitLevelResp::getVisitLevel)).collect(Collectors.toList()));
        return itemVO;
    }
}
