package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.member.handler.annotation.MemberValidateAgreeAnnotation;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 提交会员审核请求接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-29
 */
@Data
public class MemberValidateCommitReq implements Serializable {
    private static final long serialVersionUID = 7276465545351685748L;

    /**
     * 会员Id
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;

    /**
     * 审核内容Id
     */
    @NotNull(message = "审核内容Id要大于0")
    @Positive(message = "审核内容Id要大于0")
    private Long validateId;

    /**
     * 提交审批的状态：0-不同意；1-同意
     */
    @NotNull(message = "审批状态不能为空：0-不通过 1-通过")
    @MemberValidateAgreeAnnotation
    private Integer agree;

    /**
     * 审核原因
     */
    @Size(max = 120, message = "不通过原因最大120个字符")
    private String reason;
}
