package com.ssy.lingxi.member.model.req.lifecycle;

import com.ssy.lingxi.member.handler.annotation.DateStringFormatAnnotation;
import com.ssy.lingxi.member.model.req.basic.FileUploadReq;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 会员考察新增VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Data
public class MemberInspectAddReq implements Serializable {
    private static final long serialVersionUID = -5352390411750129021L;

    /**
     * 下级会员Id
     */
    @NotNull(message = "下级会员Id要大于0")
    @Positive(message = "下级会员Id要大于0")
    private Long subMemberId;

    /**
     * 下级会员角色Id
     */
    @NotNull(message = "下级会员角色Id要大于0")
    @Positive(message = "下级会员角色Id要大于0")
    private Long subRoleId;

    /**
     * 考察主题
     */
    @NotBlank(message = "考察主题能为空")
    private String subject;

    /**
     * 考察类型 1-入库考察 2-整改考察 3-计划考察 4-其他考察
     */
    @NotNull(message = "考察类型不能为空")
    private Integer inspectType;

    /**
     * 考察日期，格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation(message = "考察日期格式错误")
    private String inspectDay;

    /**
     * 考察用户Id（考察代表）
     */
    @NotNull(message = "考察代表(用户Id)要大于等于0")
    @PositiveOrZero(message = "考察代表(用户Id)要大于等于0")
    private Long userId;

    /**
     * 考察用户编辑名称
     */
    private String userEditName;

    /**
     * 考察用户编辑手机
     */
    private String userEditPhone;

    /**
     * 考察原因
     */
    @Size(max = 120, message = "考察原因最长120个字符")
    private String reason;

    /**
     * 考察附件
     */
    @Valid
    private List<FileUploadReq> attachments;

    /**
     * 考察评分
     */
    @NotNull(message = "考察评分不能为空")
    @Min(value = 0, message = "考察评分为0-100")
    @Max(value = 100, message = "考察评分为0-100")
    private BigDecimal score;

    /**
     * 考察结果
     */
    @NotBlank(message = "考察结果不能为空")
    @Size(max = 60, message = "考察结果最长60个字符")
    private String result;

    /**
     * 考察报告
     */
    @NotEmpty(message = "考察报告不能为空")
    @Valid
    private List<FileUploadReq> reports;
}
