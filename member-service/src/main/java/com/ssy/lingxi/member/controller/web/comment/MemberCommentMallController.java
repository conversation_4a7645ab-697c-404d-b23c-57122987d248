package com.ssy.lingxi.member.controller.web.comment;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.comment.OrderProductTradeCommentQueryDataReq;
import com.ssy.lingxi.member.model.req.comment.OrderProductTradeCommentReq;
import com.ssy.lingxi.member.model.resp.comment.ProductOrderTradeCommentPageResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditCommentSummaryResp;
import com.ssy.lingxi.member.service.web.comment.IMemberCommentService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 商城能力-店铺渠道商城-现货商品详情
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/comment")
public class MemberCommentMallController {
    @Resource
    private IMemberCommentService memberCommentService;

    /**
     * 商城能力-店铺渠道商城-现货商品详情-商户总体满意度
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/mall/trade/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> pageOrderProductTradeCommentSummary(@RequestHeader HttpHeaders headers, @Valid OrderProductTradeCommentReq pageVO) {
        return WrapperUtil.success(memberCommentService.pageOrderProductTradeCommentSummary(pageVO));
    }

    /**
     * 商城能力-店铺渠道商城-现货商品详情-交易评价分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/mall/trade/history/page")
    public WrapperResp<PageDataResp<ProductOrderTradeCommentPageResp>> pageOrderProductTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid OrderProductTradeCommentQueryDataReq pageVO) {
        return WrapperUtil.success(memberCommentService.pageOrderProductTradeCommentHistory(pageVO));
    }
}
