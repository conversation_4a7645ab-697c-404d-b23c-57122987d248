package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.model.req.ProvinceCityCodeReq;
import com.ssy.lingxi.member.api.model.resp.MemberCategoryFeignResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.model.req.validate.BusinessCategoryReq;
import com.ssy.lingxi.member.model.resp.validate.MemberClassifyQueryResp;
import com.ssy.lingxi.member.model.resp.validate.MemberClassifyResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员入库分类信息基础服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-31
 */
public interface IBaseMemberClassificationService {

    /**
     * 保存会员入库信息
     *
     * @param relationDO      会员关系
     * @param code            会员编码
     * @param partnerType 会员合作类型枚举
     * @param maxAmount       合作金额
     * @param areaCodes       适用区域
     * @param categories      主营品类列表
     * @return 保存结果
     */
    void saveMemberClassification(MemberRelationDO relationDO, String remark, Integer currencyType, String code, Integer partnerType, BigDecimal maxAmount, List<ProvinceCityCodeReq> areaCodes, List<BusinessCategoryReq> categories);

    /**
     * 查询会员入库分类信息（不可编辑）
     * @param relationDO 会员关系
     * @return 查询结果
     */
    MemberClassifyResp getMemberClassification(MemberRelationDO relationDO);

    /**
     * 查询会员入库分类信息（可编辑）
     * @param relationDO 会员关系
     * @return 查询结果
     */
    MemberClassifyQueryResp findMemberClassification(MemberRelationDO relationDO);

    /**
     * 查询会员入库分类信息 - 主营品类信息
     * @param relationDO 会员关系
     * @return 查询结果
     */
    List<MemberCategoryFeignResp> findMemberBusinessCategories(MemberRelationDO relationDO);

    /**
     * 更新入库资料品类id搜索项
     */
    void updateCategoryIds();
}
