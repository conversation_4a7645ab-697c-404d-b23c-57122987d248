package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.MobileShopTypeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * App - 商城类型校验注解
 */
@Target({ElementType.TYPE, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = MobileShopTypeValidator.class)
public @interface MobileShopTypeAnnotation {
    String message() default "商城类型不在定义范围内";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
