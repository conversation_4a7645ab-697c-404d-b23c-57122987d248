package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.commodity.api.feign.ICountryAreaFeign;
import com.ssy.lingxi.commodity.api.model.resp.support.CountryAreaResp;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.constant.BaseConstant;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.idGenerate.IIdGenerate;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.model.resp.AreaResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.PasswordUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.manage.api.feign.IMaterialLibraryFeign;
import com.ssy.lingxi.manage.api.model.req.MaterialFileInfoReq;
import com.ssy.lingxi.manage.api.model.resp.MaterialFileFeignResp;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.entity.bo.*;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberProcessDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberProcessRuleDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberReceiveInvitationDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.req.basic.MemberTypeAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.validate.*;
import com.ssy.lingxi.member.model.resp.basic.*;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.platform.RoleRuleManageResp;
import com.ssy.lingxi.member.model.resp.validate.WorkFlowStepResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberAbilityImportService;
import com.ssy.lingxi.member.service.web.IMemberReceiveInvitationService;
import com.ssy.lingxi.member.service.web.IPlatformMemberRoleRuleService;
import com.ssy.lingxi.member.util.ExcelReactData;
import com.ssy.lingxi.member.util.ExcelUtil;
import com.ssy.lingxi.member.util.RgConfigUtil;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员能力 - 会员导入相关接口实现
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-18
 */
@Slf4j
@Service
public class MemberAbilityImportServiceImpl implements IMemberAbilityImportService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private IBasePlatformProcessService basePlatformProcessService;

    @Resource
    private IBaseMemberInnerService memberInnerService;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private ICountryAreaFeign countryAreaFeign;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    @Resource
    private IWorkflowFeignService baseWorkflowService;

    @Resource
    private IBaseSiteService siteService;

    @Resource
    private IPlatformMemberRoleRuleService roleRuleService;

    @Resource
    private MemberRegisterConfigRepository memberRegisterConfigRepository;

    @Resource
    private MemberProcessRuleRepository memberProcessRuleRepository;

    @Resource
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    @Resource
    private IMaterialLibraryFeign materialLibraryFeign;

    @Resource
    private IMemberReceiveInvitationService memberReceiveInvitationService;

    @Resource
    private IMessageFeignService messageFeignService;

    @Resource
    private MemberReceiveInvitationRepository memberReceiveInvitationRepository;

    @Resource
    private IIdGenerate idGenerate;

    @Resource
    private IRedisUtils redisUtils;

    /**
     * 会员导入功能所需要的外部状态列表
     */
    private final List<MemberOuterStatusEnum> memberImportOuterStatus = Stream.of(
                    MemberOuterStatusEnum.TO_PLATFORM_VERIFY,
                    MemberOuterStatusEnum.PLATFORM_VERIFYING,
                    MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED,
                    MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED,
                    MemberOuterStatusEnum.DEPOSITING,
                    MemberOuterStatusEnum.DEPOSITORY_PASSED,
                    MemberOuterStatusEnum.DEPOSITORY_NOT_PASSED,
                    MemberOuterStatusEnum.MODIFYING,
                    MemberOuterStatusEnum.MODIFY_PASSED,
                    MemberOuterStatusEnum.MODIFY_NOT_PASSED)
            .collect(Collectors.toList());

    /**
     * 获取分页查询会员列表页面中各个查询条件下拉选择框的内容
     *
     * @param headers HttpHeaders信息
     * @param loginUser 登录用户信息
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public MemberImportSearchConditionResp getPageCondition(HttpHeaders headers, UserLoginCacheDTO loginUser, Integer roleTag) {
        //规则：
        // 1). 上级会员角色为企业会员，下级会员角色为企业会员：无限制
        // 2). 上级会员角色为企业会员，下级会员角色为渠道会员：不允许创建
        // 3). 上级会员角色为渠道会员，下级会员角色为企业会员：不允许创建
        // 4). 上级会员角色为渠道会员，下级会员角色为渠道会员：判断下级会员是否有另一个服务消费者角色在关系树中

        MemberImportSearchConditionResp conditionVO = new MemberImportSearchConditionResp();

        //外部状态
        List<DropdownItemResp> itemList = memberImportOuterStatus.stream().map(e -> new DropdownItemResp(e.getCode(), e.getMessage())).collect(Collectors.toList());
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setOuterStatus(itemList);

        //状态
        itemList = MemberStatusEnum.toCodeList().stream().map(status -> new DropdownItemResp(status, MemberStatusEnum.getCodeMessage(status))).collect(Collectors.toList());
        itemList.add(0, new DropdownItemResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setStatus(itemList);

        //获取是否开启saas多租户部署
        Boolean enableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        List<MemberTypeAndNameResp> memberTypeList;
        List<RoleIdAndNameResp> roleList;
        if (enableMultiTenancy) {//有开启
            //会员类型（这里返回的是Id）
            memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.subMemberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
            //会员角色（按照Id升序排序）
            roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.subMemberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
        } else {//未开启
            //会员类型（这里返回的是Id）
            memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType());
            //会员角色（按照Id升序排序）
            roleList = baseMemberValidateService.getSubRoleList(loginUser.getMemberType(), roleTag);
        }
        memberTypeList.add(0, new MemberTypeAndNameResp(0, MemberStringEnum.ALL.getName()));
        conditionVO.setMemberTypes(memberTypeList);
        roleList.add(0, new RoleIdAndNameResp(0L, MemberStringEnum.ALL.getName()));
        conditionVO.setMemberRoles(roleList);

        //会员等级
        List<LevelAndTagResp> levelList = baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId());
        conditionVO.setMemberLevels(levelList);

        return conditionVO;
    }

    /**
     * 分页、模糊查询会员
     *
     * @param loginUser 登录用户信息
     * @param queryVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberAbilityImportPageQueryResp> pageMembers(UserLoginCacheDTO loginUser, MemberAbilityImportMemberQueryDataReq queryVO, Integer roleTag) {
        Specification<MemberRelationDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级会员id和角色Id
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));

            //注册来源要为“商户代录入注册”
            list.add(criteriaBuilder.or(criteriaBuilder.equal(root.get("relSource").as(Integer.class), MemberRelationSourceEnum.MEMBER_CREATE.getCode())));

            //会员名称
            if (StringUtils.hasLength(queryVO.getName())) {
                Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + queryVO.getName().trim() + "%"));
            }

            //注册起始日期
            if (StringUtils.hasLength(queryVO.getStartDate())) {
                LocalDateTime startDate = LocalDateTime.parse(queryVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), startDate));
            }

            //注册结束日期
            if (StringUtils.hasLength(queryVO.getEndDate())) {
                LocalDateTime endDate = LocalDateTime.parse(queryVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), endDate));
            }

            //外部状态
            if (NumberUtil.isNullOrZero(queryVO.getOuterStatus())) {
//                list.add(root.get("outerStatus").as(Integer.class).in(memberImportOuterStatus.stream().map(MemberOuterStatusEnum::getCode).collect(Collectors.toList())));
            } else {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), queryVO.getOuterStatus()));
            }

            //状态
            if (NumberUtil.notNullOrZero(queryVO.getStatus())) {
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), queryVO.getStatus()));
            }

            //会员角色
            if (NumberUtil.notNullOrZero(queryVO.getRoleId())) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), queryVO.getRoleId()));
            }

            //会员等级
            if (NumberUtil.notNullOrZero(queryVO.getLevel())) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), queryVO.getLevel()));
            }

            // 角色标签
            if (NumberUtil.notNullOrZero(roleTag)) {
                list.add(criteriaBuilder.equal(root.get("subRoleTag").as(Integer.class), roleTag));
            }

            // 会员类型
            if(NumberUtil.notNullOrZero(queryVO.getMemberType())) {
                Join<Object, Object> subRoleJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(subRoleJoin.get("memberType"), queryVO.getMemberType()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // 按注册时间倒序排列
        Pageable page = PageRequest.of(queryVO.getCurrent() - 1, queryVO.getPageSize(), Sort.by("createTime").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(spec, page);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(relationDO -> {
            MemberAbilityImportPageQueryResp memberVO = new MemberAbilityImportPageQueryResp();
            memberVO.setMemberId(relationDO.getSubMember().getId());
            memberVO.setValidateId(relationDO.getId());
            memberVO.setName(relationDO.getSubMember().getName());
            memberVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            memberVO.setRoleName(relationDO.getSubRoleName());
            memberVO.setSourceName(MemberRegisterSourceEnum.getCodeMessage(relationDO.getSubMember().getSource()));
            memberVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));
            memberVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
            memberVO.setStatus(relationDO.getStatus());
            memberVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
            memberVO.setOuterStatus(relationDO.getOuterStatus());
            memberVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
            //前端按钮显示
            memberVO.setShowCommit(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode()));
            memberVO.setShowUpdate(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode()) || relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode()));
            memberVO.setShowDelete(relationDO.getOuterStatus().equals(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode()));
            memberVO.setAccount(relationDO.getSubMember().getAccount());
            memberVO.setEmail(relationDO.getSubMember().getEmail());

            return memberVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 获取新增会员页面内容(审核步骤、会员类型）
     *
     * @param headers   HttpHeaders信息
     * @param loginUser 登录用户信息
     * @return 操作结果
     */
    @Override
    public MemberAbilityAddMemberPageItemsResp getAddMemberPageItems(HttpHeaders headers, UserLoginCacheDTO loginUser) {
        MemberDO memberDO = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (memberDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO memberRoleDO = memberDO.getMemberRoles().stream().filter(role -> role.getId().equals(loginUser.getMemberRoleId())).findFirst().orElse(null);
        if (memberRoleDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        MemberAbilityAddMemberPageItemsResp itemsVO = new MemberAbilityAddMemberPageItemsResp();

        //固定审核流程
        List<WorkFlowStepResp> steps = new ArrayList<>();
        WorkFlowStepResp stepVO = new WorkFlowStepResp();
        stepVO.setStep(1);
        stepVO.setRoleName(MemberStringEnum.MEMBER.getName());
        stepVO.setStepName(MemberStringEnum.MEMBER_APPLY.getName());
        steps.add(stepVO);

        stepVO = new WorkFlowStepResp();
        stepVO.setStep(2);
        stepVO.setRoleName(MemberStringEnum.PLATFORM.getName());
        stepVO.setStepName(MemberStringEnum.MEMBER_VALIDATE.getName());
        steps.add(stepVO);
        itemsVO.setVerifySteps(steps);

        itemsVO.setCurrentStep(1);

        //获取是否开启saas多租户部署
        Boolean enableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        //会员类型
        List<MemberTypeAndNameResp> memberTypeList;

        if (enableMultiTenancy) {//有开启
            //会员类型（这里返回的是Id）
            memberTypeList = baseMemberValidateService.getSubMemberTypeList(loginUser.getMemberType(), Optional.ofNullable(roleRuleService.subMemberRoles(loginUser.getMemberId())).orElse(new ArrayList<>()));
        } else {//没有开启
            memberTypeList = baseMemberValidateService.getSubMemberTypeList(Arrays.asList(MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode()));
        }

        itemsVO.setMemberTypes(memberTypeList);

        return itemsVO;
    }

    /**
     * 根据会员类型，查询角色列表
     *
     * @param headers HttpHeaders信息
     * @param loginUser 用户信息
     * @param memberTypeReq    接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public List<RoleIdAndNameResp> getAddMemberPageRoles(HttpHeaders headers, UserLoginCacheDTO loginUser, MemberTypeReq memberTypeReq, Integer roleTag) {
        //获取是否开启saas多租户部署
        Boolean enableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        if (enableMultiTenancy) {
            List<RoleRuleManageResp> roleManageVOList = roleRuleService.subMemberRoles(loginUser.getMemberId());
            if (CollectionUtils.isEmpty(roleManageVOList)) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_NO_SUITABLE_ROLE);
            }
            return roleManageVOList.stream().filter(roleManageVO -> {
                if (!NumberUtil.isNullOrZero(roleTag)) {
                    return Objects.equals(roleManageVO.getMemberType(), memberTypeReq.getMemberType()) && roleManageVO.getRoleTag() != null &&
                            roleTag.equals(roleManageVO.getRoleTag());
                }
                return Objects.equals(roleManageVO.getMemberType(), memberTypeReq.getMemberType());
            }).map(roleManageVO -> new RoleIdAndNameResp(roleManageVO.getRoleId(), roleManageVO.getRoleName())).sorted(Comparator.comparing(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList());
        } else {
            //只返回该会员类型相关的角色
            List<MemberRoleDO> roleDOList = memberRoleRepository.findByMemberTypeAndStatus(memberTypeReq.getMemberType(), EnableDisableStatusEnum.ENABLE.getCode());
            return roleDOList.stream().filter(memberRoleDO -> {
                if (!NumberUtil.isNullOrZero(roleTag)) {
                    return !memberRoleDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode()) &&
                            !ObjectUtils.isEmpty(memberRoleDO.getRoleTag()) && roleTag.equals(memberRoleDO.getRoleTag());
                }
                return !memberRoleDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode()) && !loginUser.getMemberRoleType().equals(memberRoleDO.getRoleType());
            }).map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).sorted(Comparator.comparingLong(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList());
        }
    }

    /**
     * 根据角色标签，查询角色列表
     * @param headers HttpHeader信息
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public List<RoleIdAndNameAndMemberTypeResp> getAddMemberPageRolesByRoleTag(HttpHeaders headers, Integer roleTag) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);

        //获取是否开启saas多租户部署
        Boolean enableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        if (enableMultiTenancy) {
            List<RoleRuleManageResp> roleManageVOList = roleRuleService.subMemberRoles(loginCacheDTO.getMemberId());
            if (CollectionUtils.isEmpty(roleManageVOList)) {

                throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_NO_SUITABLE_ROLE);
            }
            return roleManageVOList.stream().filter(roleManageVO -> !ObjectUtils.isEmpty(roleManageVO.getRoleTag()) && roleTag.equals(roleManageVO.getRoleTag())).map(roleManageVO -> new RoleIdAndNameAndMemberTypeResp(roleManageVO.getRoleId(), roleManageVO.getRoleName(), roleManageVO.getMemberType())).sorted(Comparator.comparing(RoleIdAndNameAndMemberTypeResp::getRoleId)).collect(Collectors.toList());
        } else {
            // 全量查询
            List<MemberRoleDO> roleDOList = memberRoleRepository.findAll();
            return roleDOList.stream().filter(memberRoleDO -> !memberRoleDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode()) &&
                    !ObjectUtils.isEmpty(memberRoleDO.getRoleTag()) && roleTag.equals(memberRoleDO.getRoleTag())).map(memberRoleDO -> new RoleIdAndNameAndMemberTypeResp(memberRoleDO.getId(), memberRoleDO.getRoleName(), memberRoleDO.getMemberType())).sorted(Comparator.comparingLong(RoleIdAndNameAndMemberTypeResp::getRoleId)).collect(Collectors.toList());
        }
    }

    /**
     * 新增会员页面，根据会员类型和角色，查询等级列表
     *
     * @param loginUser       登录用户信息
     * @param typeAndRoleIdVO 接口参数
     * @return 操作结果
     */
    @Override
    public List<LevelAndTagResp> getAddMemberPageLevels(UserLoginCacheDTO loginUser, MemberTypeAndRoleIdReq typeAndRoleIdVO) {
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(typeAndRoleIdVO.getRoleId()).orElse(null);
        if (memberRoleDO == null || !memberRoleDO.getMemberType().equals(typeAndRoleIdVO.getMemberType())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //从基础等级配置表中查
        List<LevelAndTagResp> result = baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId(), typeAndRoleIdVO.getRoleId());

        if (CollectionUtils.isEmpty(result)) {
            return Stream.of(new LevelAndTagResp(0, "")).collect(Collectors.toList());
        }

        return result;
    }

    /**
     * 新增会员页面，根据选择的角色，返回会员注册资料信息
     *
     * @param headers   HttpHeaders信息
     * @param idVO      接口参数
     * @return 操作结果
     */
    @Override
    public List<MemberConfigGroupResp> getAddMemberPageMemberConfigItems(HttpHeaders headers, RoleIdReq idVO) {
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(idVO.getRoleId()).orElse(null);
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        return baseMemberRegisterDetailService.groupMemberConfig(new ArrayList<>(memberRoleDO.getConfigs()));
    }

    /**
     * 新增会员
     *
     * @param loginUser 登录用户信息
     * @param memberVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void addSubMember(UserLoginCacheDTO loginUser, MemberAbilityAddMemberReq memberVO, Integer roleTag) {
        //规则：
        // 1). 上级会员角色为企业会员，下级会员角色为企业会员：无限制
        // 2). 上级会员角色为企业会员，下级会员角色为渠道会员：不允许创建
        // 3). 上级会员角色为渠道会员，下级会员角色为企业会员：不允许创建
        // 4). 上级会员角色为渠道会员，下级会员角色为渠道会员：判断下级会员是否有另一个服务消费者角色在关系树中

        //查询平台会员关系
        MemberRelationDO platformRelationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if (platformRelationDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //查询当前会员，即上级会员
        MemberDO upperMember = platformRelationDO.getSubMember();
        if (upperMember == null) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO upperRole = platformRelationDO.getSubRole();
        if (upperRole == null) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //校验规则：如果上级会员未审核通过，不能创建下级会员
        if (!platformRelationDO.getVerified().equals(EnableDisableStatusEnum.ENABLE.getCode())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_UPPER_MEMBER_VALIDATE_NOT_PASSED);
        }

        //查询平台会员和平台角色
        MemberDO platformMemberDO = memberRepository.findPlatformMember();
        if (platformMemberDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRoleDO platformRoleDO = platformMemberDO.getMemberRoles().stream().filter(r -> r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).findFirst().orElse(null);
        if (platformRoleDO == null) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        //检查手机号是否已经存在
        if (userRepository.existsByRelTypeAndPhone(MemberRelationTypeEnum.OTHER.getCode(), memberVO.getPhone())) {


            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }

        //判断邮箱（如果非空）是否存在
        if (StringUtils.hasLength(memberVO.getEmail()) && userRepository.existsByRelTypeAndEmail(MemberRelationTypeEnum.OTHER.getCode(), memberVO.getEmail().trim())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
        }

        //校验会员类型
        BusinessAssertUtil.isTrue(MemberTypeEnum.contains(memberVO.getMemberType()), ResponseCodeEnum.MC_MS_MEMBER_TYPE_DOES_NOT_EXIST);

        Integer subMemberLevelTypeEnum = MemberLevelTypeEnum.MERCHANT.getCode();

        //校验角色：通过角色Id查找角色，通过会员类型校验角色
        MemberRoleDO newRoleDO = memberRoleRepository.findById(memberVO.getRoleId()).orElse(null);
        if (newRoleDO == null || !newRoleDO.getMemberType().equals(memberVO.getMemberType())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //校验基础等级配置：通过角色、等级类型校验等级
        //这里要与获得等级列表接口中的对应
        if (NumberUtil.notNullOrZero(memberVO.getLevel()) && !baseMemberLevelConfigService.existLevel(loginUser.getMemberId(), loginUser.getMemberRoleId(), memberVO.getRoleId(), memberVO.getLevel())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
        }

        //校验手机号码前缀
        CountryAreaResp countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(memberVO.getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);

        //校验手机号码长度
        Integer phoneLength = countryAreaResp.getTelLength();
        String regex = "^\\d{" + phoneLength + "}$";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(memberVO.getPhone());
        if (!m.matches()) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PHONE_FORMAT_INCORRECT);
        }

        //校验注册资料和会员名称
        List<MemberRegisterDetailDO> registerDetails = new ArrayList<>();
        //会员名称是否已经注册
        String memberName = baseMemberRegisterDetailService.checkMemberRegisterDetail(memberVO.getDetail(), new ArrayList<>(newRoleDO.getConfigs()), registerDetails, memberVO.getPhone());

        if (StringUtils.hasLength(memberName) && memberRepository.existsByName(memberName)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
        }

        // 供应商/客户新建需要输入密码
        if (NumberUtil.notNullOrZero(roleTag) && !StringUtils.hasLength(memberVO.getPassword())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PASSWORD_NOT_NULL);
        }

        //根据角色查找平台会员审核流程的Key
        ProcessBO processResult = basePlatformProcessService.findRolePlatformProcess(newRoleDO);

        //新增平台会员
        AddMemberBO addMemberBO = new AddMemberBO();
        addMemberBO.setPlatformProcess(processResult);
        addMemberBO.setUpperMember(platformMemberDO);
        addMemberBO.setUpperRole(platformRoleDO);
        addMemberBO.setMemberRoleDO(newRoleDO);
        addMemberBO.setMemberTypeEnum(MemberTypeEnum.toEnum(memberVO.getMemberType()));
        addMemberBO.setRegisterDetails(registerDetails);
        String memberCode = "B2B" + redisUtils.getSerialNumberByDay(MemberRedisConstant.MEMBER_CODE_PREFIX, 4, RedisConstant.REDIS_USER_INDEX);
        addMemberBO.setCode(memberCode);
        addMemberBO.setName(memberName);
        addMemberBO.setTelCode(countryAreaResp.getTelCode());
        addMemberBO.setPhone(memberVO.getPhone());
        //设置内部状态（如果为“申请注册状态”则不自动向工作流发起启动审核步骤命令）
        addMemberBO.setInnerStatus(processResult.getEmptyProcess() ? PlatformInnerStatusEnum.TO_BE_COMMIT.getCode() : PlatformInnerStatusEnum.REGISTERING.getCode());
        //会员手机号即账号
        addMemberBO.setAccount(memberVO.getPhone());
        addMemberBO.setPassword(PasswordUtil.tryEncrypt(NumberUtil.notNullOrZero(roleTag) ? memberVO.getPassword() : MemberConstant.PLATFORM_ADD_MEMBER_DEFAULT_PASSWORD));
        addMemberBO.setEmail(StringUtils.hasLength(memberVO.getEmail()) ? memberVO.getEmail().trim() : "");
        //会员创建下级会员，外部审核记录的操作角色为上级会员角色
        addMemberBO.setOperatorRoleName(upperRole.getRoleName());

        //根据会员类型判断注册来源为：商户代录入注册
        addMemberBO.setSource(MemberRegisterSourceEnum.FROM_MERCHANT_IMPORT);


        //会员等级设置为Null，查询最小等级
        addMemberBO.setLevel(null);
        MemberRelationDO result = memberInnerService.addPlatformMember(addMemberBO, RoleTagEnum.MEMBER.getCode());

        //新增当前会员的下级会员（上下级关系）
         memberInnerService.addMemberRelation(upperMember, upperRole, result, newRoleDO, memberVO.getMemberType(), memberVO.getLevel(), subMemberLevelTypeEnum, roleTag, false);
    }

    /**
     * 引入单个/多个会员
     *
     * @param headers      Http头部信息
     * @param memberListVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void introduceSubMember(HttpHeaders headers, MemberAbilityAddMemberListReq memberListVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        memberListVO.getList().forEach(memberVO -> {
            //规则：
            // 1). 上级会员角色为企业会员，下级会员角色为企业会员：无限制
            // 2). 上级会员角色为企业会员，下级会员角色为渠道会员：不允许创建
            // 3). 上级会员角色为渠道会员，下级会员角色为企业会员：不允许创建
            // 4). 上级会员角色为渠道会员，下级会员角色为渠道会员：判断下级会员是否有另一个服务消费者角色在关系树中

            //查询平台会员关系
            MemberRelationDO platformRelationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
            if (platformRelationDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            }

            //查询当前会员，即上级会员
            MemberDO upperMember = platformRelationDO.getSubMember();
            if (upperMember == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            }

            MemberRoleDO upperRole = platformRelationDO.getSubRole();
            if (upperRole == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
            }

            //校验规则：如果上级会员未审核通过，不能创建下级会员
            if (!platformRelationDO.getVerified().equals(EnableDisableStatusEnum.ENABLE.getCode())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_UPPER_MEMBER_VALIDATE_NOT_PASSED);
            }

            //查询平台会员和平台角色
            MemberDO platformMemberDO = memberRepository.findPlatformMember();
            if (platformMemberDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
            }

            MemberRoleDO platformRoleDO = platformMemberDO.getMemberRoles().stream().filter(r -> r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).findFirst().orElse(null);
            if (platformRoleDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
            }
            //校验会员类型
            BusinessAssertUtil.isTrue(MemberTypeEnum.contains(memberVO.getMemberType()), ResponseCodeEnum.MC_MS_MEMBER_TYPE_DOES_NOT_EXIST);

            Integer subMemberLevelTypeEnum = MemberLevelTypeEnum.MERCHANT.getCode();;

            //校验角色：通过角色Id查找角色，通过会员类型校验角色
            MemberRoleDO newRoleDO = memberRoleRepository.findById(memberVO.getRoleId()).orElse(null);
            if (newRoleDO == null || !newRoleDO.getMemberType().equals(memberVO.getMemberType())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
            }

            //校验基础等级配置：通过角色、等级类型校验等级
            //这里要与获得等级列表接口中的对应
//            if(NumberUtil.notNullOrZero(memberVO.getLevel()) && !baseMemberLevelConfigService.existLevel(loginUser.getMemberId(), loginUser.getMemberRoleId(), memberVO.getRoleId(), memberVO.getLevel())) {
//                throw new BusinessException(ResponseCode.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
//            }

            //手机号码前缀
            WrapperUtil.throwWhenDataIsNull(countryAreaFeign.getCountryAreaByTelCode(memberVO.getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);

            // 已经是平台会员，无需校验
            //校验注册资料和会员名称
//            List<MemberRegisterDetailDO> registerDetails = new ArrayList<>();
//            Wrapper<String> checkResult = baseMemberRegisterDetailService.checkMemberRegisterDetail(memberVO.getDetail(), new ArrayList<>(newRoleDO.getConfigs()), registerDetails, memberVO.getPhone());
//            if(checkResult.getCode() != ResponseCode.SUCCESS.getCode()) {
//                throw new BusinessException(checkResult.getCode(), checkResult.getNameByCode());
//            }

            //根据角色查找平台会员审核流程的Key
            ProcessBO processResult = basePlatformProcessService.findRolePlatformProcess(newRoleDO);

            //查询新导入的会员(也就是下级)与平台会员关系
            MemberRelationDO subPlatformRelationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(memberVO.getMemberId(), memberVO.getRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());

            //新增当前会员的下级会员（上下级关系）
            memberInnerService.addMemberRelation(upperMember, upperRole, subPlatformRelationDO, newRoleDO, memberVO.getMemberType(), memberVO.getLevel(), subMemberLevelTypeEnum, roleTag, false);

            MemberReceiveInvitationDO receiveInvitationDO = memberReceiveInvitationRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperMember.getId(), upperRole.getId(), subPlatformRelationDO.getSubMemberId(), subPlatformRelationDO.getSubRoleId());
            if (Objects.nonNull(receiveInvitationDO)) {
                receiveInvitationDO.setRegisterTime(LocalDateTime.now());
                receiveInvitationDO.setFillInDepositoryDetail(CommonBooleanEnum.YES.getCode());
                memberReceiveInvitationRepository.save(receiveInvitationDO);
            }
        });
    }

    /**
     * 查询单个会员详细信息（用于修改页面）
     *
     * @param loginUser  登录用户信息
     * @param validateVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public MemberAbilityImportMemberDetailResp getSubMember(UserLoginCacheDTO loginUser, MemberValidateReq validateVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberDO subMember = relationDO.getSubMember();
        if (subMember == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO subRole = relationDO.getSubRole();
        if (subRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        CountryAreaResp countryAreaResp = null;
        if (StringUtils.hasLength(relationDO.getSubMember().getTelCode())) {
            countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(relationDO.getSubMember().getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);
        }

        MemberAbilityImportMemberDetailResp detailVO = new MemberAbilityImportMemberDetailResp();
        //审核流程
        detailVO.setVerifySteps(baseMemberValidateService.getPlatformValidateOuterSteps(subRole.getRoleName(), roleTag));

        //当前步骤
        detailVO.setCurrentStep(2);

        //基本信息
        detailVO.setMemberId(subMember.getId());
        detailVO.setName(subMember.getName());
        detailVO.setStatus(relationDO.getStatus());
        detailVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        detailVO.setOuterStatus(relationDO.getOuterStatus());
        detailVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
        detailVO.setMemberTypeEnum(relationDO.getSubMemberTypeEnum());
        detailVO.setMemberType(subRole.getMemberType());
        detailVO.setMemberTypeName(MemberTypeEnum.getName(subRole.getMemberType()));
        detailVO.setRoleId(relationDO.getSubRoleId());
        detailVO.setRoleName(relationDO.getSubRoleName());
        detailVO.setLevelId(relationDO.getLevelRight() == null ? 0L : (relationDO.getLevelRight().getLevelConfig() == null ? 0L : relationDO.getLevelRight().getLevelConfig().getId()));
        detailVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
        detailVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
        detailVO.setAccount(relationDO.getSubMember().getAccount());
        detailVO.setTelCode(countryAreaResp == null ? "" : countryAreaResp.getTelCode());
        detailVO.setPhone(relationDO.getSubMember().getPhone());
        detailVO.setEmail(StringUtils.hasLength(relationDO.getSubMember().getEmail()) ? relationDO.getSubMember().getEmail() : "");
        detailVO.setCreateTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));

        //详细信息分组内容
        detailVO.setGroups(baseMemberRegisterDetailService.switchMemberRegisterDetail(subMember));

        //外部审核历史记录
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        return detailVO;
    }

    /**
     * 查询单个会员详细信息（用于查询页面）
     *
     * @param loginUser  登录用户信息
     * @param validateVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public MemberAbilityImportMemberTextDetailResp getSubMemberDetail(UserLoginCacheDTO loginUser, MemberValidateReq validateVO, Integer roleTag) {
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if (NumberUtil.notNullOrZero(roleTag) && !roleTag.equals(relationDO.getSubRoleTag())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberDO subMember = relationDO.getSubMember();
        if (subMember == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO subRole = relationDO.getSubRole();
        if (subRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        CountryAreaResp countryAreaResp = null;
        if (StringUtils.hasLength(relationDO.getSubMember().getTelCode())) {
            countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(relationDO.getSubMember().getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);
        }

        MemberAbilityImportMemberTextDetailResp detailVO = new MemberAbilityImportMemberTextDetailResp();
        //审核流程
        detailVO.setVerifySteps(baseMemberValidateService.getPlatformValidateOuterSteps(subRole.getRoleName(), roleTag));

        //当前步骤
        detailVO.setCurrentStep(2);

        //基本信息
        detailVO.setMemberId(subMember.getId());
        detailVO.setName(subMember.getName());
        detailVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
        detailVO.setOuterStatus(relationDO.getOuterStatus());
        detailVO.setOuterStatusName(SecurityStringUtil.replaceMemberPrefix(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), roleTag));
        detailVO.setMemberTypeEnum(relationDO.getSubMemberTypeEnum());
        detailVO.setMemberTypeName(MemberTypeEnum.getName(subRole.getMemberType()));
        detailVO.setRoleName(relationDO.getSubRoleName());
        detailVO.setLevel(relationDO.getLevelRight().getLevel());
        detailVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
        detailVO.setAccount(relationDO.getSubMember().getAccount());
        detailVO.setTelCode(countryAreaResp == null ? "" : countryAreaResp.getTelCode());
        detailVO.setPhone(relationDO.getSubMember().getPhone());
        detailVO.setEmail(StringUtils.hasLength(relationDO.getSubMember().getEmail()) ? relationDO.getSubMember().getEmail() : "");
        detailVO.setCreateTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));

        //详细信息分组内容
        detailVO.setGroups(baseMemberRegisterDetailService.switchMemberRegisterDetailText(subMember));

        //外部审核历史记录
        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, roleTag));

        return detailVO;
    }

    /**
     * 修改会员信息
     *
     * @param loginUser 登录用户
     * @param updateVO  接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateSubMember(UserLoginCacheDTO loginUser, MemberAbilityUpdateMemberReq updateVO) {
        MemberRelationDO relationDO = relationRepository.findById(updateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //平台与会员的关系
        MemberRelationDO platformMemberRelationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(relationDO.getSubMemberId(), relationDO.getSubRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if (platformMemberRelationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //审核不通过、待提交审核状态的会员都可以修改，修改后状态为待提交审核
        if (!platformMemberRelationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode()) && !platformMemberRelationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_UPDATE_OR_DELETE_SUB_MEMBER_HAS_BEEN_COMMIT);
        }

        //查询当前会员，即上级会员
        MemberDO upperMember = memberRepository.findById(loginUser.getMemberId()).orElse(null);
        if (upperMember == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO upperRole = memberRoleRepository.findById(loginUser.getMemberRoleId()).orElse(null);
        if (upperRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //要修改的会员
        MemberDO subMember = relationDO.getSubMember();
        if (subMember == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        //如果注册来源不是“商户代录入注册“，不允许修改
        if (!subMember.getSource().equals(MemberRegisterSourceEnum.FROM_MERCHANT_IMPORT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_REGISTER_FROM_OTHER_SOURCE);
        }

        //如果是其他会员的下级会员，不能修改或删除
        if (relationRepository.existsBySubMemberIdAndSubRoleIdAndRelTypeAndIdNot(relationDO.getSubMemberId(), relationDO.getSubRoleId(), MemberRelationTypeEnum.OTHER.getCode(), relationDO.getId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_DELETE_OTHER_SUB_MEMBER);
        }

        //要修改的会员的管理员账号（用于判断手机号是否已经注册）
        UserDO userDO = subMember.getUsers().stream().filter(memberUserDO -> memberUserDO.getUserType().equals(UserTypeEnum.ADMIN.getCode())).findFirst().orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        //判断手机号码是否已经被注册
        if (!updateVO.getPhone().equals(userDO.getPhone()) && userRepository.existsByRelTypeAndPhoneAndIdNot(MemberRelationTypeEnum.OTHER.getCode(), updateVO.getPhone(), userDO.getId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }

        //校验会员类型
        BusinessAssertUtil.isTrue(MemberTypeEnum.contains(updateVO.getMemberType()), ResponseCodeEnum.MC_MS_MEMBER_TYPE_DOES_NOT_EXIST);

        //校验角色：通过角色Id查找角色，通过会员类型校验角色
        MemberRoleDO newRoleDO = memberRoleRepository.findById(updateVO.getRoleId()).orElse(null);
        if (newRoleDO == null || !newRoleDO.getMemberType().equals(updateVO.getMemberType())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //校验等级：通过角色、等级类型校验等级
        if (!NumberUtil.isNullOrZero(updateVO.getLevel()) && !baseMemberLevelConfigService.existLevel(loginUser.getMemberId(), loginUser.getMemberRoleId(), updateVO.getRoleId(), updateVO.getLevel())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
        }

        //校验手机号码前缀
        CountryAreaResp countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(updateVO.getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);

//        //校验注册资料和会员名称
//        List<MemberRegisterDetailDO> registerDetails = new ArrayList<>();
//        Wrapper<String> checkResult = baseMemberRegisterDetailService.checkMemberRegisterDetail(updateVO.getDetail(), new ArrayList<>(newRoleDO.getConfigs()), registerDetails, subMember.getName());
//        if(checkResult.getCode() != ResponseCode.SUCCESS.getCode()) {
//            return Wrapper.fail(checkResult.getCode(), checkResult.getNameByCode());
//        }

//        String memberName = checkResult.getData();
//        //会员名称是否已经注册
//        if(StringUtils.hasLength(memberName) && memberRepository.existsByNameAndIdNot(memberName, updateVO.getMemberId())) {
//            return Wrapper.fail(ResponseCode.MC_MS_MEMBER_NAME_REGISTERED);
//        }

        //修改注册资料
        baseMemberRegisterDetailService.updatePlatformMemberRegisterDetail(platformMemberRelationDO, updateVO.getEmail(), updateVO.getDetail(), true, false);

        String memberName = platformMemberRelationDO.getSubMember().getName();

        //修改平台会员关系
        UpdatePlatformMemberBO updateBO = new UpdatePlatformMemberBO();
        updateBO.setRelationDO(platformMemberRelationDO);
        updateBO.setMemberRoleDO(newRoleDO);
        updateBO.setMemberType(updateVO.getMemberType());
        updateBO.setRegisterDetails(new ArrayList<>());
        //不能修改平台会员的等级
        updateBO.setLevel(platformMemberRelationDO.getLevelRight().getLevel());
        updateBO.setName(memberName);
        updateBO.setPhone(updateVO.getPhone());
        updateBO.setTelCode(countryAreaResp.getTelCode());
        updateBO.setEmail(StringUtils.hasLength(updateVO.getEmail()) ? updateVO.getEmail() : "");
        memberInnerService.updatePlatformMember(updateBO);
//        if (updatePlatformMemberResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
//            return updatePlatformMemberResult;
//        }

        //修改上下级关系
        UpdateMemberRelationBO updateRelationBO = new UpdateMemberRelationBO();
        updateRelationBO.setUpperMember(upperMember);
        updateRelationBO.setUpperRole(upperRole);
        updateRelationBO.setSubMember(subMember);
        updateRelationBO.setSubRoleDO(newRoleDO);
        updateRelationBO.setRelationDO(relationDO);
        updateRelationBO.setMemberType(updateVO.getMemberType());
        updateRelationBO.setName(memberName);
        updateRelationBO.setLevel(updateVO.getLevel());

        memberInnerService.updateMemberRelation(updateRelationBO);
    }

    /**
     * 删除会员
     *
     * @param loginUser  登录用户信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void deleteSubMember(UserLoginCacheDTO loginUser, MemberValidateReq validateVO) {
        MemberDO memberDO = memberRepository.findById(validateVO.getMemberId()).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //如果已经提交审核，不允许修改
        if (!relationDO.getOuterStatus().equals(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_UPDATE_OR_DELETE_SUB_MEMBER_HAS_BEEN_COMMIT);
        }

        //如果注册来源不是“商户代录入注册，不允许修改
        if (!memberDO.getSource().equals(MemberRegisterSourceEnum.FROM_MERCHANT_IMPORT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_REGISTER_FROM_OTHER_SOURCE);
        }

        //查询平台会员和平台角色
        MemberDO platformMemberDO = memberRepository.findPlatformMember();
        if (platformMemberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRoleDO platformRoleDO = platformMemberDO.getMemberRoles().stream().filter(r -> r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).findFirst().orElse(null);
        if (platformRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRelationDO platformRelationDO = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(platformMemberDO.getId(), platformRoleDO.getId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        if (platformRelationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //如果是其他会员的下级会员，仅删除上下级关系，否则全部删除
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.notEqual(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.notEqual(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            list.add(criteriaBuilder.equal(root.get("subMemberId").as(Long.class), relationDO.getSubMemberId()));
            list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), relationDO.getSubRoleId()));
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        if (relationRepository.count(specification) > 0) {
            memberInnerService.deleteMemberRelation(relationDO);
        } else {
            memberInnerService.deletePlatformMember(memberDO, platformRelationDO);

            memberInnerService.deleteMemberRelation(relationDO);
        }
    }

    /**
     * 提交平台审核
     *
     * @param loginUser  登录用户信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void commitSubMemberToValidate(UserLoginCacheDTO loginUser, MemberValidateReq validateVO) {
        //当前会员的上下级关系
        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //提交平台审核后，要将外部状态置为“待平台审核”，以此判断是否已经提交过了
        if (!relationDO.getOuterStatus().equals(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_HAS_BEEN_COMMIT_TO_VALIDATE);
        }

        //与平台会员的上下级关系
        MemberRelationDO platformRelationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(relationDO.getSubMemberId(), relationDO.getSubRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if (platformRelationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //启动平台审核的工作流
        //如果平台审核流程是“无需审核”，直接设置审核通过状态，不需要调用工作流
        if (platformRelationDO.getValidateTask().getProcessTypeEnum().equals(MemberProcessTypeEnum.PLATFORM_VALIDATION.getCode()) && platformRelationDO.getValidateTask().getProcessKey().equals(MemberConstant.EMPTY_PLATFORM_VALIDATE_PROCESS_KEY)) {
            relationDO.setInnerStatus(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL.getCode());
            relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITING.getCode());

            //更新会员上下级关系
            relationRepository.saveAndFlush(relationDO);
        } else {
            WorkflowTaskResultBO taskResult = baseWorkflowService.startMemberProcess(platformRelationDO);

            String taskId = taskResult.getTaskId();
            Integer innerStatus = taskResult.getInnerStatus();

            platformRelationDO.getValidateTask().setTaskId(taskId);
            platformRelationDO.setInnerStatus(innerStatus);
            if (innerStatus.equals(PlatformInnerStatusEnum.VERIFY_NOT_PASSED.getCode())) {
                platformRelationDO.setInnerStatus(PlatformInnerStatusEnum.VERIFY_NOT_PASSED.getCode());
                platformRelationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode());

                relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode());
            } else if (innerStatus.equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode())) {
                platformRelationDO.setInnerStatus(PlatformInnerStatusEnum.VERIFY_PASSED.getCode());
                platformRelationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode());
                platformRelationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
                platformRelationDO.setDepositTime(LocalDateTime.now());

                relationDO.setInnerStatus(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL.getCode());
                relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITING.getCode());
            } else {
                innerStatus = PlatformInnerStatusEnum.TO_BE_COMMIT.getCode();
                platformRelationDO.setInnerStatus(innerStatus);
                platformRelationDO.setOuterStatus(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode());

                relationDO.setInnerStatus(MemberInnerStatusEnum.TO_VERIFY_DEPOSITORY_DETAIL.getCode());
                relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode());
            }

            //更新平台上下级关系、会员上下级关系
            relationRepository.saveAll(Stream.of(platformRelationDO, relationDO).collect(Collectors.toList()));
        }

        //记录内部记录
        baseMemberHistoryService.saveMemberInnerHistory(relationDO, loginUser, MemberValidateHistoryOperationEnum.COMMIT_TO_VALIDATE, "");

    }

    /**
     * 会员可用角色查询(excel导出模版)
     *
     * @param headers HttpHeaders信息
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public List<RoleIdAndNameResp> excelRoles(HttpHeaders headers, Integer roleTag) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);

        //获取是否开启saas多租户部署
        Boolean enableMultiTenancy = siteService.isEnableMultiTenancy(headers);

        if (enableMultiTenancy) {
            List<RoleRuleManageResp> roleManageVOList = roleRuleService.subMemberRoles(loginCacheDTO.getMemberId());
            if (CollectionUtils.isEmpty(roleManageVOList)) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_SUB_MEMBER_NO_SUITABLE_ROLE);
            }
            return roleManageVOList.stream().filter(r -> !NumberUtil.notNullOrZero(roleTag) || (NumberUtil.notNullOrZero(r.getRoleTag()) && roleTag.equals(r.getRoleTag())))
                    .map(roleManageVO -> new RoleIdAndNameResp(roleManageVO.getRoleId(), roleManageVO.getRoleName())).sorted(Comparator.comparing(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList());
        } else {
            List<MemberRoleDO> roleDOList = memberRoleRepository.findAllByStatusAndRelType(EnableDisableStatusEnum.ENABLE.getCode(), MemberRelationTypeEnum.OTHER.getCode());
            return roleDOList.stream().filter(r -> !NumberUtil.notNullOrZero(roleTag) || (Objects.nonNull(r.getRoleTag()) && roleTag.equals(r.getRoleTag())))
                    .map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).sorted(Comparator.comparingLong(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList());
        }
    }

    /**
     * 导出会员导入模版（导出excel模版）
     *
     * @param headers  HttpHeaders信息
     * @param roleIdReq 角色id类
     */
    @Override
    public void exportExcelTemplate(HttpHeaders headers, RoleIdReq roleIdReq, HttpServletRequest request, HttpServletResponse response) {
        UserLoginCacheDTO loginCacheDTO = memberCacheService.needLoginFromBusinessPlatform(headers);

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(roleIdReq.getRoleId()).orElse(null);
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }
        // 列名
        List<String> column = Stream.of(ColumnNameEnum.PHONE_PREFIX.getName(), ColumnNameEnum.PHONE.getName(), ColumnNameEnum.EMAIL.getName()).collect(Collectors.toList());
        // 手机号前缀
        List<CountryAreaResp> countryAreaRespList = WrapperUtil.getDataOrThrow(countryAreaFeign.getAllCountryArea());
//        List<String> codeList = countryAreaRespList.stream().map(bean -> bean.getTelCode() + Constant.UNDERLINE_STR + bean.getId()).collect(Collectors.toList());
        List<String> codeList = countryAreaRespList.stream().map(CountryAreaResp::getTelCode).collect(Collectors.toList());
        //用于生成excel表格中的所有下拉框
        Map<Integer, List<String>> map = new HashMap<>(16);
        //用于生成excel表格中的所有级联关联
        List<ExcelReactData> reactDataMap = new ArrayList<>();
        //省市的对应关系（被关联列有数据）
        Map<String, List<String>> provCityMap = new HashMap<>(16);
        // 市和区域的对应关系（属性关联属性值的对应关系，需要被关联列选择才会出数据）
        Map<String, List<String>> cityDistrictMap = new HashMap<>(16);
        map.put(1, codeList);
        // 列，程序补充前三个字段，所以下表从3开始
        AtomicReference<Integer> col = new AtomicReference<>(3);
        //从基础等级配置表中查会员等级
        List<LevelAndTagResp> result = baseMemberLevelConfigService.listSubMemberLevels(loginCacheDTO.getMemberId(), loginCacheDTO.getMemberRoleId(), memberRoleDO.getId());
        // 如果存在，增加会员等级列
        if (!CollectionUtils.isEmpty(result)) {
            int i = col.get() + 1;
            column.add(ColumnNameEnum.LEVEL.getName());
            List<String> levelList = result.stream().map(level -> level.getLevelTag() + Constant.UNDERLINE_STR + level.getLevel()).collect(Collectors.toList());
            map.put(i, levelList);
            col.set(i);
        }
        // 查询平台注册资料
        List<MemberRegisterConfigDO> platform = getPlatFormConfig(roleIdReq.getRoleId());
        if (!CollectionUtils.isEmpty(platform)) {
            platform.forEach(bean -> {
                if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(bean.getFieldType())) {
                    return;
                }
                col.getAndSet(col.get() + 1);
                String fieldLocalName = RgConfigUtil.getFieldLocalName(bean);
                if (bean.getFieldEmpty() == 0) {
                    fieldLocalName = String.format(MemberConstant.MEMBER_EXCEL_FIELD, fieldLocalName);
                }
                paramBinding(column, map, reactDataMap, provCityMap, cityDistrictMap, col, bean);
                if (!MemberConstant.FIELD_TYPE_AREA.equals(bean.getFieldType())) {
                    column.add(fieldLocalName);
                }
            });
        }
        // 查询入库资料
        MemberProcessRuleDO processRuleDO = memberProcessRuleRepository
                .findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(loginCacheDTO.getMemberId(), loginCacheDTO.getMemberRoleId(),
                        memberRoleDO, EnableDisableStatusEnum.ENABLE.getCode(), Sort.by("id").ascending());
        if (processRuleDO != null && !CollectionUtils.isEmpty(processRuleDO.getConfigs())) {
            processRuleDO.getConfigs().forEach(bean -> {
                if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(bean.getFieldType())) {
                    return;
                }
                col.getAndSet(col.get() + 1);
                String fieldLocalName = RgConfigUtil.getFieldLocalName(bean);
                if (bean.getFieldEmpty() == 0) {
                    fieldLocalName = String.format(MemberConstant.MEMBER_EXCEL_FIELD, fieldLocalName);
                }
                paramBinding(column, map, reactDataMap, provCityMap, cityDistrictMap, col, bean);
                if (!MemberConstant.FIELD_TYPE_AREA.equals(bean.getFieldType())) {
                    column.add(fieldLocalName);
                }
            });
        }
        //生成excel文档
        ExcelUtil.createExcelTemplate("memberTemplate", column, map, reactDataMap, request, response);
    }

    /**
     * 根据角色id查询平台注册资料
     *
     * @param roleId 角色id
     * @return 平台注册资料
     */
    private List<MemberRegisterConfigDO> getPlatFormConfig(Long roleId) {
        Specification<MemberRegisterConfigDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            Join<MemberRegisterConfigDO, MemberRoleDO> roleJoin = root.join("roles", JoinType.LEFT);
            list.add(criteriaBuilder.equal(roleJoin.get("id").as(Long.class), roleId));
            list.add(criteriaBuilder.equal(root.get("fieldStatus").as(Integer.class), EnableDisableStatusEnum.ENABLE.getCode()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };
        // 查询注册资料
        return memberRegisterConfigRepository.findAll(specification, Sort.by("id").ascending());
    }

    /**
     * 字段参数绑定
     *
     * @param column          列集合
     * @param map             下拉框数据集
     * @param reactDataMap    用于生成excel表格中的所有级联关联
     * @param provCityMap     省和市关联
     * @param cityDistrictMap 市和区关联
     * @param col             下标对象
     * @param bean            入库资料实体
     */
    private void paramBinding(List<String> column, Map<Integer, List<String>> map, List<ExcelReactData> reactDataMap, Map<String, List<String>> provCityMap, Map<String, List<String>> cityDistrictMap, AtomicReference<Integer> col, MemberRegisterConfigDO bean) {
        String fieldType = bean.getFieldType();
        // 复选、单选、下拉选；先按下拉单选处理
        if (MemberConstant.FIELD_TYPE_CHECKBOX.equals(fieldType) || MemberConstant.FIELD_TYPE_RADIO.equals(fieldType) || MemberConstant.FIELD_TYPE_SELECT.equals(fieldType)) {
            List<String> labels = bean.getLabels().stream().map(label -> RgConfigUtil.getLabelValue(label) + Constant.UNDERLINE_STR + label.getId()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(labels)) {
                map.put(col.get(), labels);
            }
        }
        if (MemberConstant.FIELD_TYPE_AREA.equals(fieldType)) {
            area(column, map, reactDataMap, provCityMap, cityDistrictMap, col, bean.getFieldEmpty() == 0);
        }
    }

    /**
     * 区域字段处理
     *
     * @param column          列集合
     * @param map             下拉框数据集
     * @param reactDataMap    用于生成excel表格中的所有级联关联
     * @param provCityMap     省和市关联
     * @param cityDistrictMap 市和区关联
     * @param col 下标对象
     * @param notEmpty 不能为空
     */
    private void area(List<String> column, Map<Integer, List<String>> map, List<ExcelReactData> reactDataMap, Map<String, List<String>> provCityMap, Map<String, List<String>> cityDistrictMap, AtomicReference<Integer> col, Boolean notEmpty) {
        // 省份索引
        Integer provIndex = col.get();
        column.add(notEmpty ? String.format(MemberConstant.MEMBER_EXCEL_FIELD, ColumnNameEnum.PROVINCE.getName()) : ColumnNameEnum.PROVINCE.getName());
        // 市级索引
        int cityIndex = col.get() + 1;
        col.set(cityIndex);
        column.add(notEmpty ? String.format(MemberConstant.MEMBER_EXCEL_FIELD, ColumnNameEnum.CITY.getName()) : ColumnNameEnum.CITY.getName());
        // 市级索引
        int distIndex = col.get() + 1;
        col.set(distIndex);
        column.add(notEmpty ? String.format(MemberConstant.MEMBER_EXCEL_FIELD, ColumnNameEnum.DISTRICT.getName()) : ColumnNameEnum.DISTRICT.getName());

        List<AreaCodeNameResp> provinceData = AreaUtil.findByParentCode(BaseConstant.COUNTRY_AREA_TOP_CODE);
        List<AreaResp> cityData = AreaUtil.findAllByLevel(BaseConstant.COUNTRY_AREA_CITY_LEVEL);
        List<AreaResp> districtData = AreaUtil.findAllByLevel(BaseConstant.COUNTRY_AREA_DISTRICT_LEVEL);
        // 市分组
        Map<String, List<AreaResp>> cityGroup = cityData.stream().collect(Collectors.groupingBy(AreaResp::getPcode));
        // 区分组
        Map<String, List<AreaResp>> districtGroup = districtData.stream().collect(Collectors.groupingBy(AreaResp::getPcode));
        List<String> provinces = new ArrayList<>();
        provinceData.forEach(province -> {
            // 组装省份
            String provCode = province.getCode();
            String provinceStr = province.getName() + Constant.UNDERLINE_STR + provCode;
            provinces.add(provinceStr);
            // 根据省判断是否有关联市
            List<AreaResp> cityDataList = cityGroup.get(provCode);
            if (!CollectionUtils.isEmpty(cityDataList)) {
                List<String> cityTempList = new ArrayList<>();
                cityDataList.forEach(city -> {
                    String cityCode = city.getCode();
                    String cityKey = city.getName() + Constant.UNDERLINE_STR + cityCode;
                    cityTempList.add(cityKey);
                    // 根据市判断是否有关联区域
                    List<AreaResp> countryAreaDOS = districtGroup.get(cityCode);
                    if (!CollectionUtils.isEmpty(countryAreaDOS)) {
                        List<String> distTempList = new ArrayList<>();
                        countryAreaDOS.forEach(dist -> distTempList.add(dist.getName() + Constant.UNDERLINE_STR + dist.getCode()));
                        cityDistrictMap.put(cityKey, distTempList);
                    }
                });
                provCityMap.put(provinceStr, cityTempList);
            }
        });
        map.put(provIndex, provinces);
        // 省和市关联
        ExcelReactData reactData = new ExcelReactData();
        reactData.setKeyIndex(provIndex);
        reactData.setValueIndex(cityIndex);
        reactData.setDataMap(provCityMap);
        reactDataMap.add(reactData);

        // 市和区关联
        ExcelReactData reactData1 = new ExcelReactData();
        reactData1.setKeyIndex(cityIndex);
        reactData1.setValueIndex(distIndex);
        reactData1.setDataMap(cityDistrictMap);
        reactDataMap.add(reactData1);
    }

    /**
     * 导入会员（excel导入）
     *
     * @param headers HttpHeaders信息
     * @param file    会员导入文件
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void importExcelMembers(HttpHeaders headers, MultipartFile file, HttpServletRequest request) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);
        //下级角色id为空
        String subRoleId = request.getParameter("subRoleId");
        if (!StringUtils.hasLength(subRoleId)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_ID_CANNOT_BE_EMPTY);
        }

        Long roleId = Long.valueOf(subRoleId);
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(roleId).orElse(null);
        if (memberRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST);
        }

        // 查询角色的平台流程配置
        if (!MemberConstant.EMPTY_PLATFORM_VALIDATE_PROCESS_KEY.equals(Optional.ofNullable(memberRoleDO.getRegisterProcess()).map(MemberProcessDO::getProcessKey).orElse(""))) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_NOT_WITHOUT_AN_AUDIT_PROCESS);
        }

        // 列组装
        List<Object> column = Stream.of(ColumnNameEnum.PHONE_PREFIX.getCode(), ColumnNameEnum.PHONE.getCode(), ColumnNameEnum.EMAIL.getCode()).collect(Collectors.toList());
        // 用于表头校验
        List<String> header = Stream.of(ColumnNameEnum.PHONE_PREFIX.getName(), ColumnNameEnum.PHONE.getName(), ColumnNameEnum.EMAIL.getName()).collect(Collectors.toList());

        //从基础等级配置表中查会员等级
        List<LevelAndTagResp> levelList = baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId(), memberRoleDO.getId());
        // 列，程序补充前三个字段，所以下表从3开始
        AtomicReference<Integer> col = new AtomicReference<>(3);
        if (!CollectionUtils.isEmpty(levelList)) {
            col.set(col.get() + 1);
            column.add(ColumnNameEnum.LEVEL.getCode());
            header.add(ColumnNameEnum.LEVEL.getName());
        }

        // 查询平台注册资料
        List<MemberRegisterConfigDO> platform = getPlatFormConfig(roleId);
        Map<Integer, String> indexMap = new HashMap<>(16);
        // area被拆成多字段，缓存area是存在注册资料还是入库资料
        AreaCacheBO areaCache = new AreaCacheBO();
        platform.forEach(config -> {
            if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(config.getFieldType())){
                return;
            }
            configBinding(column, header, indexMap, col, areaCache, config, MemberConstant.REGISTER_TYPE_PLATFORM);
        });

        // 查询入库资料
        MemberProcessRuleDO processRuleDO = memberProcessRuleRepository
                .findFirstByMemberIdAndRoleIdAndSubRoleAndStatus(loginUser.getMemberId(), loginUser.getMemberRoleId(),
                        memberRoleDO, EnableDisableStatusEnum.ENABLE.getCode(), Sort.by("id").ascending());
        if (processRuleDO != null && !CollectionUtils.isEmpty(processRuleDO.getConfigs())) {
            processRuleDO.getConfigs().forEach(config -> {
                if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(config.getFieldType())){
                    return;
                }
                configBinding(column, header, indexMap, col, areaCache, config, MemberConstant.REGISTER_TYPE_MEMBER_SUB);
            });
        }

        // 会员导入数据接收列表
        List<MemberImportBO> memberImportBOList = new ArrayList<>();
        List<String> phoneCache = new ArrayList<>();
        List<String> emailCache = new ArrayList<>();

        //获取素材库的数据
        List<String> materialFileNameList = new ArrayList<>();
        Map<String, String> imageMap = new HashMap<>(16);
        MaterialFileInfoReq materialFileInfoDTO = new MaterialFileInfoReq();
        materialFileInfoDTO.setMemberId(loginUser.getMemberId());
        materialFileInfoDTO.setMemberRoleId(loginUser.getMemberRoleId());
        WrapperResp<List<MaterialFileFeignResp>> materialFileWrapperResp = materialLibraryFeign.findFileByMemberIdAndRoleId(materialFileInfoDTO);
        if (ResponseCodeEnum.SUCCESS.getCode() == materialFileWrapperResp.getCode()) {
            List<MaterialFileFeignResp> materialFileDataList = materialFileWrapperResp.getData();
            if (!CollectionUtils.isEmpty(materialFileDataList)) {
                materialFileDataList.forEach(materialFileData -> {
                    materialFileNameList.add(materialFileData.getFileName());
                    imageMap.put(materialFileData.getFileName(), materialFileData.getUrl());
                });
            }
        }

        // 获取excel表中的数据
        analysisData(file, column, indexMap, areaCache, memberImportBOList, header, phoneCache, emailCache, materialFileNameList, imageMap);

        // 批量新增下级会员
        batchAddSubMember(memberRoleDO, loginUser, memberImportBOList);

        // 执行结束，清缓存
        phoneCache.forEach(phone -> memberCacheService.deleteRegisterKey(phone, "", false));
        emailCache.forEach(email -> memberCacheService.deleteRegisterKey("", email, false));
    }

    /**
     * 批量新增下级会员
     *
     * @param newRoleDO          角色
     * @param loginUser          当前用户信息
     * @param memberImportBOList 待新增会员列表
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void batchAddSubMember(MemberRoleDO newRoleDO, UserLoginCacheDTO loginUser, List<MemberImportBO> memberImportBOList) {
        //规则：
        // 1). 上级会员角色为企业会员，下级会员角色为企业会员：无限制
        // 2). 上级会员角色为企业会员，下级会员角色为渠道会员：不允许创建
        // 3). 上级会员角色为渠道会员，下级会员角色为企业会员：不允许创建
        // 4). 上级会员角色为渠道会员，下级会员角色为渠道会员：判断下级会员是否有另一个服务消费者角色在关系树中

        //查询平台会员关系
        MemberRelationDO platformRelationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if (platformRelationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //查询当前会员，即上级会员
        MemberDO upperMember = platformRelationDO.getSubMember();
        if (upperMember == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO upperRole = platformRelationDO.getSubRole();
        if (upperRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //校验规则：如果上级会员未审核通过，不能创建下级会员
        if (!platformRelationDO.getVerified().equals(EnableDisableStatusEnum.ENABLE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_UPPER_MEMBER_VALIDATE_NOT_PASSED);
        }

        //查询平台会员和平台角色
        MemberDO platformMemberDO = memberRepository.findPlatformMember();
        if (platformMemberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRoleDO platformRoleDO = platformMemberDO.getMemberRoles().stream().filter(r -> r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).findFirst().orElse(null);
        if (platformRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        // 校验会员类型
        Integer subMemberLevelTypeEnum = MemberLevelTypeEnum.MERCHANT.getCode();

        // 获取加密后的密码
        String md5Password = PasswordUtil.tryEncrypt(MemberConstant.PLATFORM_ADD_MEMBER_DEFAULT_PASSWORD);

        //根据角色查找平台会员审核流程的Key
        ProcessBO processResult = basePlatformProcessService.findRolePlatformProcess(newRoleDO);

        List<CountryAreaResp> countryAreaRespList = WrapperUtil.getDataOrThrow(countryAreaFeign.getAllCountryArea());
        Set<String> telCodeSet = countryAreaRespList.stream().map(CountryAreaResp::getTelCode).collect(Collectors.toSet());
        BusinessAssertUtil.isTrue(memberImportBOList.stream().anyMatch(importBO -> telCodeSet.contains(importBO.getTelCode())), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);

        // 存储校验成功的注册资料
        Map<String, List<MemberRegisterDetailDO>> registerMap = new HashMap<>();

        // 存储会员名称
        Map<String, String> memberNameMap = new HashMap<>();

        // 循环检验，全部通过再一起入库
        foreachVerify(newRoleDO, loginUser, memberImportBOList, registerMap, memberNameMap);

        List<MemberRelationDO> platformMemberList = new ArrayList<>();
        List<MemberRelationDO> memberList = new ArrayList<>();

        // 会员类型
        MemberTypeEnum memberTypeEnum = MemberTypeEnum.toEnum(newRoleDO.getMemberType());

        // 循环入库
        for (MemberImportBO memberImport : memberImportBOList) {
            //新增平台会员
            String phone = memberImport.getPhone();
            AddMemberBO addMemberBO = new AddMemberBO();
            addMemberBO.setPlatformProcess(processResult);
            addMemberBO.setUpperMember(platformMemberDO);
            addMemberBO.setUpperRole(platformRoleDO);
            addMemberBO.setMemberRoleDO(newRoleDO);
            addMemberBO.setMemberTypeEnum(memberTypeEnum);
            addMemberBO.setRegisterDetails(registerMap.get(phone));
            String memberCode = "B2B" + redisUtils.getSerialNumberByDay(MemberRedisConstant.MEMBER_CODE_PREFIX, 4, RedisConstant.REDIS_USER_INDEX);
            addMemberBO.setCode(memberCode);
            addMemberBO.setName(memberNameMap.get(phone));
            addMemberBO.setTelCode(memberImport.getTelCode());
            addMemberBO.setPhone(phone);
            //设置内部状态（如果为“申请注册状态”则不自动向工作流发起启动审核步骤命令）
            addMemberBO.setInnerStatus(processResult.getEmptyProcess() ? PlatformInnerStatusEnum.TO_BE_COMMIT.getCode() : PlatformInnerStatusEnum.REGISTERING.getCode());
            //会员手机号即账号
            addMemberBO.setAccount(memberImport.getPhone());
            addMemberBO.setPassword(md5Password);
            addMemberBO.setEmail(StringUtils.hasLength(memberImport.getEmail()) ? memberImport.getEmail().trim() : "");
            //会员创建下级会员，外部审核记录的操作角色为上级会员角色
            addMemberBO.setOperatorRoleName(upperRole.getRoleName());

            //商户代录入注册
            addMemberBO.setSource(MemberRegisterSourceEnum.FROM_MERCHANT_IMPORT);

            //会员等级设置为Null，查询最小等级
            addMemberBO.setLevel(null);
            // 平台会员与下级会员
            MemberRelationDO subMemberPlatformRelation = memberInnerService.addPlatformMembers(addMemberBO);

            platformMemberList.add(subMemberPlatformRelation);

            //新增当前会员的下级会员（上下级关系）
            MemberRelationDO memberRelation = memberInnerService.addMemberRelations(upperMember, upperRole, subMemberPlatformRelation, newRoleDO, newRoleDO.getMemberType(), memberImport.getLevel(), subMemberLevelTypeEnum);

            // 当前会员和新增会员的Relation
            // 避免保存入库资料出现空指针异常
            memberRelation.setDepositDetails(new HashSet<>());
            memberList.add(memberRelation);

            // 保存入库资料
            // 检查并保存入库资料
            baseMemberDepositDetailService.checkAndSaveMemberDepositoryDetail(memberRelation, memberImport.getDepositDetails());

        }
        // 新增平台会员后，循环做远程通知
        memberInnerService.addPlatformMemberInform(platformMemberList);
        // 新增会员后，循环做远程通知
        memberInnerService.addMemberRelationInform(memberList);
    }

    /**
     * 会员发现 - 邀请
     * @param headers HttpHeaders信息
     * @param discoverVO 请求参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Override
    public void discoverInvitation(HttpHeaders headers, MemberDiscoverReq discoverVO, Integer roleTag) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromBusinessPlatform(headers);

        //规则：
        // 1). 上级会员角色为企业会员，下级会员角色为企业会员：无限制
        // 2). 上级会员角色为企业会员，下级会员角色为渠道会员：不允许创建
        // 3). 上级会员角色为渠道会员，下级会员角色为企业会员：不允许创建
        // 4). 上级会员角色为渠道会员，下级会员角色为渠道会员：判断下级会员是否有另一个服务消费者角色在关系树中

        //查询平台会员关系
        MemberRelationDO platformRelationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(loginUser.getMemberId(), loginUser.getMemberRoleId(), MemberRelationTypeEnum.PLATFORM.getCode());
        if(platformRelationDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //查询当前会员，即上级会员
        MemberDO upperMember = platformRelationDO.getSubMember();
        if(upperMember == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO upperRole = platformRelationDO.getSubRole();
        if(upperRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //校验规则：如果上级会员未审核通过，不能创建下级会员
        if(!platformRelationDO.getVerified().equals(EnableDisableStatusEnum.ENABLE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_UPPER_MEMBER_VALIDATE_NOT_PASSED);
        }

        //查询平台会员和平台角色
        MemberDO platformMemberDO = memberRepository.findPlatformMember();
        if (platformMemberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRoleDO platformRoleDO = platformMemberDO.getMemberRoles().stream().filter(r -> r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).findFirst().orElse(null);
        if (platformRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        // 判断会员关系是否存在
        MemberRelationDO currentRelation = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(platformRelationDO.getSubMemberId(), platformRelationDO.getSubRoleId(), discoverVO.getMemberId(), discoverVO.getRoleId());
        if (currentRelation != null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_EXIST);
        }

        MemberDO subMember = memberRepository.findById(discoverVO.getMemberId()).orElse(null);
        if(subMember == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        // 下级会员角色
        MemberRoleDO subRole = memberRoleRepository.findById(discoverVO.getRoleId()).orElse(null);
        if(subRole == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        // 查询是否存在邀请信息
        MemberReceiveInvitationDO receiveInvitationDO = memberReceiveInvitationRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(platformRelationDO.getSubMemberId(), platformRelationDO.getSubRoleId(), discoverVO.getMemberId(), discoverVO.getRoleId());
        if (Objects.nonNull(receiveInvitationDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_INVITATION_ALREADY_EXISTS);
        }

        // 生成邀请消息
        MemberReceiveInvitationDO receiveInvitation = new MemberReceiveInvitationDO();
        LocalDateTime now = LocalDateTime.now();
        receiveInvitation.setMemberId(loginUser.getMemberId());
        receiveInvitation.setRoleId(loginUser.getMemberRoleId());
        receiveInvitation.setMember(upperMember);
        receiveInvitation.setRole(upperRole);
        receiveInvitation.setRoleTag(Optional.ofNullable(upperRole.getRoleTag()).orElse(0));
        receiveInvitation.setSubMemberId(subMember.getId());
        receiveInvitation.setSubRoleId(subRole.getId());
        receiveInvitation.setSubRole(subRole);
        receiveInvitation.setSubRoleTag(Optional.ofNullable(subRole.getRoleTag()).orElse(0));
        receiveInvitation.setSubMember(subMember);
        receiveInvitation.setCreateTime(now);
        receiveInvitation.setInvitationTime(now);
        receiveInvitation.setState(EnableDisableStatusEnum.ENABLE.getCode());
        receiveInvitation.setInviteType(MemberInviteTypeEnum.INTRODUCE.getCode());
        receiveInvitation.setFillInDepositoryDetail(CommonBooleanEnum.NO.getCode());
        memberReceiveInvitationService.saveInvitation(receiveInvitation);

        // 发送邀请消息
        messageFeignService.sendMemberInvitationMessage(receiveInvitation, roleTag);

    }

    /**
     * 循环校验
     *
     * @param newRoleDO          新角色实体类
     * @param loginUser          登录用户信息
     * @param memberImportBOList 待数据校验的列表
     * @param registerMap        会员注册资料缓存
     * @param memberNameMap      会员名称缓存
     * @return 校验结果
     */
    private void foreachVerify(MemberRoleDO newRoleDO, UserLoginCacheDTO loginUser, List<MemberImportBO> memberImportBOList, Map<String, List<MemberRegisterDetailDO>> registerMap, Map<String, String> memberNameMap) {
        // 循环校验
        for (MemberImportBO memberImport : memberImportBOList) {

            //校验基础等级配置：通过角色、等级类型校验等级
            //这里要与获得等级列表接口中的对应
            String phone = memberImport.getPhone();
            if (NumberUtil.notNullOrZero(memberImport.getLevel()) && !baseMemberLevelConfigService.existLevel(loginUser.getMemberId(), loginUser.getMemberRoleId(), newRoleDO.getId(), memberImport.getLevel())) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
            }

            //校验注册资料和会员名称
            List<MemberRegisterDetailDO> registerDetails = new ArrayList<>();
            String memberName = baseMemberRegisterDetailService.checkMemberRegisterDetail(memberImport.getDetail(), new ArrayList<>(newRoleDO.getConfigs()), registerDetails, phone);
            registerMap.put(phone, registerDetails);
            //会员名称是否已经注册
            if (StringUtils.hasLength(memberName) && memberRepository.existsByName(memberName)) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
            }
            memberNameMap.put(phone, memberName);
        }
    }

    /**
     * 列与会员资料绑定
     *
     * @param column    表头列数据
     * @param header    表头列名
     * @param indexMap  存储下表对应的资料类型
     * @param col       列值
     * @param areaCache area信息缓存类
     * @param config    注册资料配置
     * @param type      类型：平台注册资料-platform，入库资料-processRule
     */
    private void configBinding(List<Object> column, List<String> header, Map<Integer, String> indexMap, AtomicReference<Integer> col, AreaCacheBO areaCache, MemberRegisterConfigDO config, String type) {
        String fieldType = config.getFieldType();
        boolean notEmpty = config.getFieldEmpty() == 0;
        // 省市区需要拆分成多个列
        if (MemberConstant.FIELD_TYPE_AREA.equals(fieldType)) {
            areaCache.setType(type);
            areaCache.setConfigDO(config);
            col.set(col.get() + 1);
            indexMap.put(col.get(), type);
            header.add(notEmpty ? String.format(MemberConstant.MEMBER_EXCEL_FIELD, ColumnNameEnum.PROVINCE.getName()) : ColumnNameEnum.PROVINCE.getName());
            col.set(col.get() + 1);
            indexMap.put(col.get(), type);
            header.add(notEmpty ? String.format(MemberConstant.MEMBER_EXCEL_FIELD, ColumnNameEnum.CITY.getName()) : ColumnNameEnum.CITY.getName());
            col.set(col.get() + 1);
            indexMap.put(col.get(), type);
            header.add(notEmpty ? String.format(MemberConstant.MEMBER_EXCEL_FIELD, ColumnNameEnum.DISTRICT.getName()) : ColumnNameEnum.DISTRICT.getName());
            // 区域字段占三列，为了保持一致，补充三个列数据
            column.add(config);
            column.add(config);
            column.add(config);
        } else {
            col.set(col.get() + 1);
            indexMap.put(col.get(), type);
            column.add(config);
            // 与列生成规则导入代码保持一直
            String fieldLocalName = RgConfigUtil.getFieldLocalName(config);
            if (config.getFieldEmpty() == 0) {
                fieldLocalName = String.format(MemberConstant.MEMBER_EXCEL_FIELD, fieldLocalName);
            }
            header.add(fieldLocalName);
        }
    }

    /**
     * 解析excel文档
     *
     * @param file               会员导入文件
     * @param column             列
     * @param indexMap           下表map(下表与表头的对应)
     * @param areaCache          区域缓存实体
     * @param memberImportBOList 会员导入信息接收列表
     * @param phoneCache         手机号缓存
     * @param emailCache         邮箱缓存
     * @param imageMap           图片信息map
     * @return 解析结果
     */
    private void analysisData(MultipartFile file, List<Object> column, Map<Integer, String> indexMap, AreaCacheBO areaCache, List<MemberImportBO> memberImportBOList, List<String> header, List<String> phoneCache, List<String> emailCache, List<String> materialFileNameList, Map<String, String> imageMap) {
        Workbook wb;

        try {
            //读取文件流
            InputStream iso = file.getInputStream();
            //判断是03版还是07版
            String originalFileName = file.getOriginalFilename();
            if (!org.apache.commons.lang3.StringUtils.isEmpty(originalFileName)) {
                if (ExcelUtil.isExcel2003(originalFileName)) {
                    wb = new HSSFWorkbook(iso);
                } else if (ExcelUtil.isExcel2007(originalFileName)) {
                    wb = new XSSFWorkbook(iso);
                } else {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_IMPORT_TABLE_EXCEPTION);
                }
            } else {
                throw new BusinessException(ResponseCodeEnum.MC_MS_IMPORT_TABLE_EXCEPTION);
            }
        } catch (IOException e) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_IMPORT_TABLE_ANALYSIS_EXCEPTION);
        }
        //只接收第一个sheet
        Sheet sheet = wb.getSheetAt(0);
        //一次最多只能导入50行
        int lastRowNum = sheet.getLastRowNum();
        if (lastRowNum > 51) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_IMPORT_TABLE_SIZE_EXCEPTION);
        }
        sheet.getRow(0);
        // 表头校验
        headerVerify(header, sheet.getRow(0));

        //从第二行开始解析
        StringBuilder error = new StringBuilder();
        for (int i = 1; i <= lastRowNum; i++) {
            Row row = sheet.getRow(i);
            if (null == row) {
                continue;
            }
            MemberImportBO memberImportBO = new MemberImportBO();
            String telCode = null;
            String phone = null;
            String email = null;
            Integer level = null;
            // 会员注册资料
            Map<String, Object> detail = new HashMap<>(16);
            // 会员入库资料
            Map<String, Object> depositDetails = new HashMap<>(16);
            // 省市区有值
            MemberRegisterConfigDO areaConfig = areaCache.getConfigDO();
            for (int j = 0; j < column.size(); ) {
                Object obj = column.get(j);
                // 单元格值
                String tempValue = ExcelUtil.getCellValue(row.getCell(j)) == null ? "" : ExcelUtil.getCellValue(row.getCell(j));
                if (obj instanceof String) {
                    String key = obj.toString();
                    ColumnNameEnum columnNameEnum = ColumnNameEnum.getColumnNameEnumByCode(key);
                    switch (Objects.requireNonNull(columnNameEnum)) {
                        case PHONE_PREFIX:
                            // 手机号前缀不为空时校验是否存在
                            if (checkIsEmpty(error, i, tempValue, "手机号前缀", true)) {
                                WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode((telCode = tempValue)), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);
                            }
                            break;
                        case PHONE:
                            if (checkIsEmpty(error, i, tempValue, "手机号", true)) {
                                if (phoneCache.contains(tempValue) || StringUtils.hasLength(memberCacheService.getString(tempValue))) {
                                    error.append("第" + (i + 1) + "行手机号:" + tempValue + "重复\r\n");
                                    // 存在表里
                                } else if (userRepository.existsByRelTypeAndPhone(MemberRelationTypeEnum.OTHER.getCode(), tempValue)) {
                                    error.append("第" + (i + 1) + "行手机号:" + tempValue + "已被注册\r\n");
                                }
                                // 不重复，缓存
                                phoneCache.add(tempValue);
                                memberCacheService.setRegisterKey(tempValue, "", false);
                            }
                            phone = tempValue;
                            break;
                        case EMAIL:
                            //判断邮箱（如果非空）是否存在
                            if (StringUtils.hasLength(tempValue)) {
                                if ((emailCache.contains(tempValue) || StringUtils.hasLength(memberCacheService.getString(tempValue)))) {
                                    error.append("第" + (i + 1) + "行邮箱:" + tempValue + "重复\r\n");
                                } else if (userRepository.existsByRelTypeAndEmail(MemberRelationTypeEnum.OTHER.getCode(), tempValue)) {
                                    error.append("第" + (i + 1) + "行邮箱:" + tempValue + "已被注册\r\n");
                                }
                                emailCache.add(tempValue);
                                memberCacheService.setRegisterKey("", tempValue, false);
                            }
                            email = tempValue;
                            break;
                        case LEVEL:
                            if (StringUtils.hasLength(tempValue)) {
                                String tempVal = splitValue(tempValue);
                                if (StringUtils.hasLength(tempVal) && NumberUtil.tryParseInteger(tempVal) != 0) {
                                    level = NumberUtil.tryParseInteger(tempVal);
                                }
                            }
                            break;
                        default:
                            break;
                    }
                } else if (obj instanceof MemberRegisterConfigDO) {
                    String type = indexMap.get(j + 1);
                    MemberRegisterConfigDO configDO = (MemberRegisterConfigDO) obj;
                    if (tempValue.length() > configDO.getFieldLength()) {
                        error.append("第" + (i + 1) + "行," + RgConfigUtil.getFieldLocalName(configDO) + ":" + tempValue + " " + MemberStringEnum.EXCEED_LENGTH_LIMIT.getName() + "\r\n");
                    }
                    // 注册资料
                    if (MemberConstant.REGISTER_TYPE_PLATFORM.equals(type)) {
                        putValue(detail, configDO, error, i, materialFileNameList, imageMap, row, j);
                    } else if (MemberConstant.REGISTER_TYPE_MEMBER_SUB.equals(type)) {
                        putValue(depositDetails, configDO, error, i, materialFileNameList, imageMap, row, j);
                    }
                }
                if (obj instanceof MemberRegisterConfigDO && MemberConstant.FIELD_TYPE_AREA.equals(((MemberRegisterConfigDO) obj).getFieldType())) {
                    j = j + 3;
                } else {
                    j++;
                }
            }
            detail.entrySet().removeIf(entry -> entry.getValue() == null);
            depositDetails.entrySet().removeIf(entry -> entry.getValue() == null);
            memberImportBO.setTelCode(telCode);
            memberImportBO.setPhone(phone);
            memberImportBO.setEmail(email);
            memberImportBO.setDetail(detail);
            memberImportBO.setLevel(level);
            memberImportBO.setDepositDetails(depositDetails);
            memberImportBOList.add(memberImportBO);
        }
        //如果表格没数据直接提示异常
        if (CollectionUtils.isEmpty(memberImportBOList)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_IMPORT_TABLE_IS_NULL);
        }
        //如果出现错误信息，代表校验不通过
        if (error.length() > 0) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_IMPORT_TABLE_DATA_EXCEPTION, error.toString());
        }
    }

    /**
     * 表头校验
     *
     * @param header 表头
     * @param row    第一行数据
     * @return 校验结果
     */
    private void headerVerify(List<String> header, Row row) {
        List<String> allCellValue = ExcelUtil.getAllCellValue(row);
        log.info("===header:{}==\n==allCellValue=={}", header, allCellValue);
        if (header.size() != allCellValue.size()) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_IMPORT_TABLE_COLUMN_NAMES_DO_NOT_MATCH);
        }
        for (int i = 0; i < header.size(); i++) {
            if (!header.get(i).equals(allCellValue.get(i))) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_IMPORT_TABLE_COLUMN_NAMES_DO_NOT_MATCH);
            }
        }
    }

    /**
     * 保存资料结果集
     * @param map 存储值
     * @param config 资料配置实体类
     * @param error 异常字符串
     * @param index 行索引
     * @param materialFileNameList url列表
     * @param imageMap 图片信息map
     * @param row      excel行
     * @param j        列下标
     */
    public void putValue(Map<String, Object> map, MemberRegisterConfigDO config, StringBuilder error, int index, List<String> materialFileNameList, Map<String, String> imageMap, Row row, int j) {
        String fieldLocalName = RgConfigUtil.getFieldLocalName(config);
        String fieldType = config.getFieldType();
        String fieldName = config.getFieldName();
        // 单元格值
        String value = ExcelUtil.getCellValue(row.getCell(j)) == null ? "" : ExcelUtil.getCellValue(row.getCell(j));
        // 检测必填项
        if (checkIsEmpty(error, index, value, fieldLocalName, (config.getFieldEmpty() == 0))) {
            // 下拉选项判断是否有值
            switch (fieldType) {
                case MemberConstant.FIELD_TYPE_AREA:
                    String province = "", city = "", district = "";
                    if (checkIsEmpty(error, index, value, "省", config.getFieldEmpty() == 0) && checkSelect(error, index, value, "省")) {
                        province = splitValue(value);
                    }
                    value = ExcelUtil.getCellValue(row.getCell(j ++)) == null ? "" : ExcelUtil.getCellValue(row.getCell(j));
                    if (checkIsEmpty(error, index, value, "市", config.getFieldEmpty() == 0) && checkSelect(error, index, value, "市")) {
                        city = splitValue(value);
                    }
                    value = ExcelUtil.getCellValue(row.getCell(j ++)) == null ? "" : ExcelUtil.getCellValue(row.getCell(j));
                    if (checkIsEmpty(error, index, value, "区", config.getFieldEmpty() == 0) && checkSelect(error, index, value, "区")) {
                        district = splitValue(value);
                    }
                    if ((StringUtils.hasLength(province) && StringUtils.hasLength(city) && StringUtils.hasLength(district))) {
                        List<String> codeList = Stream.of(province, city, district).collect(Collectors.toList());
                        List<String> prefixList = codeList.stream().map(code -> code.substring(0, 2)).collect(Collectors.toList());
                        if (prefixList.stream().distinct().count() != 1) {
                            error.append(error.append("第" + (index + 1) + "行省、市、区编码不匹配\r\n"));
                        }

                        List<AreaCodeNameResp> countryAreas = AreaUtil.findByCodeIn(codeList);
                        if (CollectionUtils.isEmpty(countryAreas) || countryAreas.size() != 3) {
                            error.append(error.append("第" + (index + 1) + "行" + ResponseCodeEnum.MC_MS_COUNTRY_AREA_DOES_NOT_EXIST + "\r\n"));
                        }
                        Map<String, Object> areaMap = new HashMap<>();
                        areaMap.put("provinceCode", province);
                        areaMap.put("cityCode", city);
                        areaMap.put("districtCode", district);
                        map.put(fieldName, areaMap);
                    }
                    break;
                case MemberConstant.FIELD_TYPE_CHECKBOX:
                case MemberConstant.FIELD_TYPE_RADIO:
                case MemberConstant.FIELD_TYPE_SELECT:
                    Integer val = null;
                    if (checkSelect(error, index, value, fieldLocalName)) {
                        String tempVal = splitValue(value);
                        if (StringUtils.hasLength(tempVal)) {
                            val = NumberUtil.tryParseInteger(tempVal);
                        }
                    }
                    if (!ObjectUtils.isEmpty(val)) {
                        // 复选框需要包装在列表
                        if (MemberConstant.FIELD_TYPE_CHECKBOX.equals(fieldType)) {
                            List<Integer> collect = Stream.of(val).collect(Collectors.toList());
                            map.put(fieldName, collect);
                        } else {
                            map.put(fieldName, val);
                        }
                    }
                    break;
                case MemberConstant.FIELD_TYPE_LONG:
                    map.put(fieldName, checkLong(error, index, value, fieldLocalName));
                    break;
                case MemberConstant.FIELD_TYPE_UPLOAD:
                    if (StringUtils.hasLength(value)) {
                        checkFile(error, index, value, fieldLocalName, materialFileNameList);
                        value = imageMap.get(value);
                    }
                case MemberConstant.FIELD_TYPE_STRING:
                    map.put(config.getFieldName(), value);
                    break;
                default:
                    break;
            }
        }
        // 补充默认值
//        map.putIfAbsent(fieldName, "");
    }

    /**
     * 校验图片和视频是否存在素材库
     *
     * @param errorStr     异常字符串
     * @param index        行索引
     * @param value        单元格值
     * @param columnName   列名
     * @param fileNameList 文件名列表
     */
    private void checkFile(StringBuilder errorStr, int index, String value, String columnName, List<String> fileNameList) {
        if (StringUtils.hasLength(value)) {
            if (!CollectionUtils.isEmpty(fileNameList)) {
                if (!fileNameList.contains(value)) {
                    String error = "第" + (index + 1) + "行," + columnName + ":" + value + ",没有在素材库中匹配到\r\n";
                    errorStr.append(error);
                }
            } else {
                String error = "第" + (index + 1) + "行," + columnName + ":" + value + ",没有在素材库中匹配到\r\n";
                errorStr.append(error);
            }
        }
    }

    /**
     * 检验是否为Long类型
     *
     * @param error      异常字符串
     * @param index      行索引
     * @param value      单元格值
     * @param columnName 列名
     */
    private Long checkLong(StringBuilder error, int index, String value, String columnName) {
        try {
            Long v = null;
            if (StringUtils.hasLength(value)) {
                v = Long.parseLong(value);
            }
            return v;
        } catch (Exception e) {
            error.append("第" + (index + 1) + "行," + columnName + ":" + value + ",必须为数字类型\r\n");
            return null;
        }
    }

    /**
     * 根据下划线截取值
     *
     * @param value 待截取字符串
     * @return 结果
     */
    private String splitValue(String value) {
        if (value != null) {
            String[] split = value.split(Constant.UNDERLINE_STR);
            if (split.length > 1) {
                return split[1];
            }
        }
        return "";
    }

    /**
     * 校验下拉框
     *
     * @param errorStr   异常字符串
     * @param index      行索引
     * @param value      值
     * @param columnName 列名
     * @return 结果
     */
    private Boolean checkSelect(StringBuilder errorStr, int index, String value, String columnName) {
        if (StringUtils.hasLength(value) && !value.contains(Constant.UNDERLINE_STR)) {
            String error = "第" + (index + 1) + "行," + columnName + ":" + value + ",不符合\r\n";
            errorStr.append(error);
            return false;
        }
        return true;
    }

    /**
     * 校验必填项
     *
     * @param errorStr   异常字符串
     * @param index      行索引
     * @param value      值
     * @param columnName 列名
     * @param bl         是否必填
     * @return 结果
     */
    private Boolean checkIsEmpty(StringBuilder errorStr, int index, String value, String columnName, Boolean bl) {
        if (bl && !StringUtils.hasLength(value)) {
            errorStr.append("第" + (index + 1) + "行," + columnName + "必填项为空\r\n");
            return false;
        }
        return true;
    }


}
