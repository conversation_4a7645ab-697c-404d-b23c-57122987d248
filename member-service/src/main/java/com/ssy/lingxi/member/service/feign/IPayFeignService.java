package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;

import java.math.BigDecimal;

/**
 * 调用支付服务Feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-15
 */
public interface IPayFeignService {
    /**
     * 下级会员审核通过后，通知支付服务，新增支付授信
     * @param memberRelation 会员关系
     */
    void notifyMemberCredit(MemberRelationDO memberRelation);

    /**
     * 下级会员审核通过后，通知支付服务，新增支付授信
     * @param memberId 上级会员Id
     * @param roleId 上级会员角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     */
    void notifyMemberCredit(Long memberId, Long roleId, Long subMemberId, Long subRoleId);

    /**
     * 下级会员审核通过后，通知支付服务，新增会员资金账户
     * @param memberRelation 会员关系
     */
    void notifyMemberAssetAccount(MemberRelationDO memberRelation);

    /**
     * 下级会员审核通过后，通知支付服务，新增会员资金账户
     * @param memberId 上级会员Id
     * @param memberName 上级会员名称
     * @param roleId 上级角色Id
     * @param roleName 上级角色名称
     * @param subMemberId 下级会员Id
     * @param subMemberCode 下级会员会员编号
     * @param subMemberName 下级会员名称
     * @param subRoleId 下级角色Id
     * @param subRoleName 下级角色名称
     * @param subMemberLevelTypeEnum 下级会员等级类型枚举
     * @param subMemberTypeEnum 下级会员会员类型枚举
     * @param subMemberStatus 下级会员状态
     */
    void notifyMemberAssetAccount(Long memberId, String memberName, Long roleId, String roleName, Long subMemberId, String subMemberCode, String subMemberName, Long subRoleId, String subRoleName, Integer subMemberLevelTypeEnum, Integer subMemberTypeEnum, Integer subMemberStatus);

    /**
     * 冻结/解冻会员时，通知支付服务，冻结/解冻会员资金账户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级角色Id
     * @param relType   关联关系
     * @param subMemberStatus 会员状态
     */
    void notifyUpdateMemberAssetAccount(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Integer relType, Integer subMemberStatus);

    /**
     * 淘汰、黑名单会员时，通知支付服务，冻结会员资金账户
     * @param upperMemberId 上级会员Id
     * @param upperRoleId   上级角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级角色Id
     * @param relType   关联关系
     */
    void notifyToFreezeAssetAccount(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Integer relType);

    /**
     * 交易完成后，如果返现金额大于0，通知支付服务
     * @param upperMemberId 上级会员Id
     * @param upperRoleId  上级会员角色Id
     * @param subMemberId   下级会员Id
     * @param subRoleId    下级会员角色Id
     * @param returnType   账号类型
     * @param returnMoney  返现金额
     * @param orderNo      订单号
     */
    void notifyAssetAccountReturnMoney(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Integer returnType, BigDecimal returnMoney, String orderNo);
}
