package com.ssy.lingxi.member.repository.commission;

import com.ssy.lingxi.member.entity.do_.commission.CommissionConfigDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 分佣配置Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Repository
public interface CommissionConfigRepository extends JpaRepository<CommissionConfigDO, Long> {

    /**
     * 查询第一个配置记录
     * @return 配置信息
     */
    Optional<CommissionConfigDO> findFirstBy();
}
