package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.RealNameConfigDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 实名验证配置持久化层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/13
 */
public interface RealNameConfigRepository extends JpaRepository<RealNameConfigDO, Long>, JpaSpecificationExecutor<RealNameConfigDO> {
    RealNameConfigDO findByStatus(Boolean code);

    RealNameConfigDO findTopByServiceTypeOrderByIdDesc(Integer serviceType);

    List<RealNameConfigDO> findByIdNot(Long id);
}
