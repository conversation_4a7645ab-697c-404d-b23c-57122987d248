package com.ssy.lingxi.member.repository;


import com.ssy.lingxi.member.entity.do_.basic.RunBrandDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 业务平台 - 经营品牌数据库操作JpaRepository
 * <AUTHOR>
 * @since 2025-08-2
 * @version 2.0.0
 */
@Repository
public interface RunBrandRepository extends JpaRepository<RunBrandDO, Long>, JpaSpecificationExecutor<RunBrandDO> {

    RunBrandDO findFirstByCode(String code);
}
