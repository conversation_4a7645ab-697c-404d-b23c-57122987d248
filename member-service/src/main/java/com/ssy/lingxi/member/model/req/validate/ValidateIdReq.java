package com.ssy.lingxi.member.model.req.validate;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 会员关系Id作为接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-19
 */
@Data
public class ValidateIdReq implements Serializable {
    private static final long serialVersionUID = 672904248569959237L;

    /**
     * 审核内容Id
     */
    @NotNull(message = "审核内容Id要大于0")
    @Positive(message = "审核内容Id要大于0")
    private Long validateId;
}
