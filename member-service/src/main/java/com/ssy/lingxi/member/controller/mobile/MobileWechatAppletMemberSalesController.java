package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.resp.basic.MobileMemberSalesInformationResp;
import com.ssy.lingxi.member.service.mobile.IMobileMemberSalesService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 微信小程序-业务员相关接口
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-24
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/wechat/applet")
public class MobileWechatAppletMemberSalesController {

    @Resource
    private IMobileMemberSalesService memberSalesService;

    /**
     * 查看业务员详情
     */
    @GetMapping("/sales/information")
    public WrapperResp<MobileMemberSalesInformationResp> getSalesInformation(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberSalesService.getSalesInformation(headers));
    }
}
