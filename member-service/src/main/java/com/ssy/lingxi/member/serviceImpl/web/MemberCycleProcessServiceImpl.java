package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.enums.workflow.WorkflowProcessKindEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.engine.ProcessEngineReq;
import com.ssy.lingxi.common.model.req.engine.RuleEngineProcessReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.RuleEngineProcessResp;
import com.ssy.lingxi.common.model.resp.engine.SimpleProcessResp;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.engine.api.enums.ProcessDefaultEnum;
import com.ssy.lingxi.engine.api.enums.ProcessSourceEnum;
import com.ssy.lingxi.engine.api.enums.ProcessTypeEnum;
import com.ssy.lingxi.engine.api.feign.IEngineFeign;
import com.ssy.lingxi.engine.api.feign.IProcessEngineRuleFeign;
import com.ssy.lingxi.engine.api.feign.IProcessRuleConfigFeign;
import com.ssy.lingxi.engine.api.model.dto.ProcessEngineRuleSaveDTO;
import com.ssy.lingxi.engine.api.model.req.*;
import com.ssy.lingxi.engine.api.model.resp.ProcessRuleConfigResp;
import com.ssy.lingxi.member.api.enums.MemberCycleProcessTypeEnum;
import com.ssy.lingxi.member.entity.do_.lifecycle.BaseMemberCycleProcessDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberCycleProcessDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.PlatformMemberCycleProcessDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.PlatformMemberCycleProcessMemberDO;
import com.ssy.lingxi.member.enums.MemberCycleProcessEnum;
import com.ssy.lingxi.member.model.req.platform.*;
import com.ssy.lingxi.member.model.resp.platform.BaseMemberCycleProcessResp;
import com.ssy.lingxi.member.model.resp.platform.MemberCycleProcessDetailResp;
import com.ssy.lingxi.member.model.resp.platform.MemberCycleProcessPageResp;
import com.ssy.lingxi.member.repository.BaseMemberCycleProcessRepository;
import com.ssy.lingxi.member.repository.MemberCycleProcessRepository;
import com.ssy.lingxi.member.repository.PlatformMemberCycleProcessRepository;
import com.ssy.lingxi.member.service.web.IMemberCycleProcessService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员生命周期变更流程服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@Service
public class MemberCycleProcessServiceImpl implements IMemberCycleProcessService {

    @Resource
    private MemberCycleProcessRepository memberCycleProcessRepository;

    @Resource
    private PlatformMemberCycleProcessRepository platformMemberCycleProcessRepository;

    @Resource
    private BaseMemberCycleProcessRepository baseMemberCycleProcessRepository;

    @Resource
    private IProcessEngineRuleFeign processEngineRuleFeign;

    @Resource
    private IEngineFeign engineFeign;

    @Resource
    private IProcessRuleConfigFeign processRuleConfigFeign;

    /**
     * 保存默认流程
     * @param baseProcess 基础流程
     */
    @Override
    public void saveDefaultProcess(BaseMemberCycleProcessDO baseProcess, ProcessEngineReq engineBO) {

        // 查询当前类型的默认流程
        MemberCycleProcessDO defaultProcess = memberCycleProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode());

        // 创建默认流程
        if (Objects.equals(engineBO.getIsDefault(), ProcessDefaultEnum.YES.getCode()) && Objects.isNull(defaultProcess)) {
            createDefault(baseProcess);
            return;
        }

        // 非默认流程不处理
        if (Objects.isNull(defaultProcess)) {
            return;
        }

        // 取消默认则删除
        if (Objects.equals(ProcessDefaultEnum.NO.getCode(), engineBO.getIsDefault())) {
            memberCycleProcessRepository.delete(defaultProcess);
            return;
        }

        // 更新默认工作流
        defaultProcess.setBaseProcess(baseProcess);
        defaultProcess.setName(engineBO.getProcessName());
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        memberCycleProcessRepository.save(defaultProcess);
    }

    /**
     * 设置默认工作流
     * @param platformProcess 平台流程
     */
    @Override
    public void defaultProcess(PlatformMemberCycleProcessDO platformProcess) {

        // 查询当前类型的默认流程
        MemberCycleProcessDO defaultProcess = memberCycleProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(platformProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode());

        // 创建默认流程
        if (Objects.isNull(defaultProcess)) {
            createDefault(platformProcess.getProcess());
            return;
        }

        // 更新默认工作流
        defaultProcess.setBaseProcess(platformProcess.getProcess());
        defaultProcess.setName(platformProcess.getName());
        defaultProcess.setProcessKey(platformProcess.getProcessKey());
        memberCycleProcessRepository.save(defaultProcess);
    }


    /**
     * 创建默认流程
     * @param baseProcess 基础流程
     */
    private void createDefault(BaseMemberCycleProcessDO baseProcess) {
        MemberCycleProcessDO defaultProcess = new MemberCycleProcessDO();
        defaultProcess.setSource(ProcessSourceEnum.PAAS.getCode());
        defaultProcess.setRoleId(0L);
        defaultProcess.setMemberId(0L);
        defaultProcess.setName(baseProcess.getProcessName());
        defaultProcess.setStatus(baseProcess.getStatus());
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        defaultProcess.setProcessType(baseProcess.getProcessType());
        defaultProcess.setBaseProcess(baseProcess);
        defaultProcess.setIsDefault(ProcessDefaultEnum.YES.getCode());
        defaultProcess.setCreateTime(LocalDateTime.now());
        memberCycleProcessRepository.save(defaultProcess);
    }

    /**
     * 获取会员流程
     * @param memberId      会员ID
     * @param roleId        角色ID
     * @param processType   流程类型
     * @return MemberCycleProcessDO
     */
    @Override
    public List<MemberCycleProcessDO> getMemberProcess(Long memberId, Long roleId, Integer processType) {

        // 查询会员流程
        List<MemberCycleProcessDO> result = memberCycleProcessRepository.findByMemberIdAndRoleIdAndProcessTypeAndStatus(memberId, roleId, processType, EnableDisableStatusEnum.ENABLE.getCode());

        // 查询系统默认流程
        List<MemberCycleProcessDO> defaultList = memberCycleProcessRepository.findByMemberIdAndRoleIdAndProcessTypeAndStatus(0L, 0L, processType, EnableDisableStatusEnum.ENABLE.getCode());

        List<MemberCycleProcessDO> defaultResult = result.stream().filter(DO -> Objects.equals(DO.getIsDefault(), ProcessDefaultEnum.YES.getCode())).collect(Collectors.toList());

        // 会员自定义默认流程为空
        if (defaultResult.isEmpty()) {
            result.addAll(defaultList);
        }

        return result;
    }

    /**
     * 分页查询流程规则
     * @param loginUser 登录用户信息
     * @param pageVO 分页查询条件
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberCycleProcessPageResp> pageProcesses(UserLoginCacheDTO loginUser, MemberCycleProcessPageQueryDataReq pageVO, Integer status) {

        // 查询平台默认
        List<Integer> types = memberCycleProcessRepository.findByMemberIdAndRoleIdAndIsDefaultAndSource(loginUser.getMemberId(), loginUser.getMemberRoleId(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.SYSTEM.getCode()).stream().map(MemberCycleProcessDO::getProcessType).distinct().collect(Collectors.toList());

        // 平台未存在的默认流程类型
        List<Integer> typeList = Arrays.stream(MemberCycleProcessTypeEnum.values()).map(MemberCycleProcessTypeEnum::getCode).filter(code -> !types.contains(code)).collect(Collectors.toList());

        // step 1:组装查询条件
        // 20220620测试要求将排序改为根据id降序排序
        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("isDefault", "id").descending());
        Specification<MemberCycleProcessDO> spec = (root, query, builder) -> {
            List<Predicate> conditions = new ArrayList<>();
            conditions.add(builder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            conditions.add(builder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            if (Objects.nonNull(status)){
                conditions.add(builder.equal(root.get("status").as(Integer.class), status));
            }
            if (StringUtils.hasText(pageVO.getName())) {
                conditions.add(builder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }
            if (typeList.isEmpty()){
                return query.where(conditions.toArray(new Predicate[0])).getRestriction();
            }

            // 查询系统默认流程
            List<Predicate> orList = new ArrayList<>();
            orList.add(builder.equal(root.get("memberId").as(Long.class), 0L));
            orList.add(builder.equal(root.get("roleId").as(Long.class), 0L));
            orList.add(builder.in(root.get("processType")).value(typeList));
            if (StringUtils.hasText(pageVO.getName())) {
                orList.add(builder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }

            return builder.or( builder.and(conditions.toArray(new Predicate[0])), builder.and(orList.toArray(new Predicate[0])));
        };

        // step 2:查询数据库
        Page<MemberCycleProcessDO> pageData = memberCycleProcessRepository.findAll(spec, pageable);
        // step 3: 组装数据
        return new PageDataResp<>(pageData.getTotalElements(), pageData.getContent().stream().map(this::builderMemberCycleProcessPageVO).collect(Collectors.toList()));
    }

    /**
     * 分页查询流程规则
     * @param loginUser 登录用户信息
     * @param pageVO 分页查询条件
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberCycleProcessPageResp> processPage(UserLoginCacheDTO loginUser, MemberCycleProcessPageQueryDataReq pageVO, Integer status) {
        // step 1:组装查询条件
        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Specification<MemberCycleProcessDO> spec = (root, query, builder) -> {
            List<Predicate> conditions = new ArrayList<>();
            conditions.add(builder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
            conditions.add(builder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            if (Objects.nonNull(status)){
                conditions.add(builder.equal(root.get("status").as(Integer.class), status));
            }
            if (StringUtils.hasText(pageVO.getName())) {
                conditions.add(builder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }
            return query.where(conditions.toArray(new Predicate[0])).getRestriction();
        };
        // step 2:查询数据库
        Page<MemberCycleProcessDO> pageData = memberCycleProcessRepository.findAll(spec, pageable);
        // step 3: 组装数据
        return new PageDataResp<>(pageData.getTotalElements(), pageData.getContent().stream().map(this::builderMemberCycleProcessPageVO).collect(Collectors.toList()));
    }

    /**
     * 创建VO类
     * @param entity 流程信息
     * @return VO类
     */
    private MemberCycleProcessPageResp builderMemberCycleProcessPageVO(MemberCycleProcessDO entity){
        MemberCycleProcessPageResp target = new MemberCycleProcessPageResp();
        target.setProcessId(entity.getId());
        if (Objects.equals(ProcessDefaultEnum.YES.getCode(), entity.getIsDefault())){
            target.setName(Optional.ofNullable(entity.getBaseProcess().getProcessName()).orElse(entity.getBaseProcess().getName()));
        }else {
            target.setName(entity.getName());
        }
        target.setProcessName(entity.getBaseProcess().getProcessName());
        target.setStatus(entity.getStatus());
        target.setStatusName(EnableDisableStatusEnum.getNameByCode(entity.getStatus()));
        target.setCreateTime(entity.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        target.setIsDefault(Optional.ofNullable(entity.getIsDefault()).orElse(0));
        target.setProcessType(entity.getProcessType());
        target.setProcessKind(WorkflowProcessKindEnum.INTERNAL.getCode());
        return target;
    }

    /**
     * 查询基础流程列表
     * @param loginUser 登录用户信息
     * @return 查询结果
     */
    @Override
    public List<BaseMemberCycleProcessResp> listBaseProcesses(UserLoginCacheDTO loginUser, ProcessQueryReq queryRequest) {
        Specification<PlatformMemberCycleProcessDO> spec = (root, query, builder) -> {
            Predicate field1 = builder.equal(root.get("allMembers").as(Boolean.class), true);
            Join<PlatformMemberCycleProcessDO, PlatformMemberCycleProcessMemberDO> members = root.join("members", JoinType.LEFT);
            Predicate field2 = builder.equal(members.get("memberId").as(Long.class), loginUser.getMemberId());
            Predicate field3 = builder.equal(members.get("roleId").as(Long.class), loginUser.getMemberRoleId());
            return query.where(builder.or(field1, builder.and(field2, field3))).getRestriction();
        };
        List<PlatformMemberCycleProcessDO> list = platformMemberCycleProcessRepository.findAll(spec);
        return list.stream().filter(f -> isFilter(f, queryRequest)).map(PlatformMemberCycleProcessDO::getProcess).sorted(Comparator.comparingLong(BaseMemberCycleProcessDO::getId)).map(this::builderBaseMemberCycleProcessVO).distinct().collect(Collectors.toList());
    }

    /**
     * 过滤数据
     * @param process       流程
     * @param queryRequest  请求参数
     * @return Boolean
     */
    private Boolean isFilter(PlatformMemberCycleProcessDO process, ProcessQueryReq queryRequest){

        // 有效的
        boolean isFilter = Objects.equals(process.getStatus(), EnableDisableStatusEnum.ENABLE.getCode());

        // 非默认的
        if (Objects.isNull(queryRequest.getProcessType())){
            isFilter = isFilter && !Objects.equals(process.getIsDefault(), ProcessDefaultEnum.YES.getCode());
        }

        // 指定类型
        if (Objects.nonNull(queryRequest.getProcessType())) {
            return isFilter && Objects.equals(process.getProcessType(), queryRequest.getProcessType());
        }

        return isFilter;
    }

    private BaseMemberCycleProcessResp builderBaseMemberCycleProcessVO(BaseMemberCycleProcessDO baseProcess) {
        BaseMemberCycleProcessResp target = new BaseMemberCycleProcessResp();
        target.setBaseProcessId(baseProcess.getId());
        target.setProcessName(Optional.ofNullable(baseProcess.getProcessName()).orElse(baseProcess.getName()));
        target.setProcessType(baseProcess.getProcessType());
        target.setProcessTypeName(MemberCycleProcessTypeEnum.getMessage(baseProcess.getProcessType()));
        target.setDescription(Optional.ofNullable(baseProcess.getDescription()).orElse(MemberCycleProcessEnum.getRemarkByCode(baseProcess.getCode())));
        target.setProcessImage(baseProcess.getProcessImage());
        target.setEngineId(baseProcess.getEngineId());
        return target;
    }

    /**
     * 设置默认流程
     * @param loginUser 登录用户信息
     * @param defaultRequest 接口参数
     * @return Void
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveDefault(UserLoginCacheDTO loginUser, SaveDefaultReq defaultRequest) {

        // 查询当前规则
        BaseMemberCycleProcessDO process = baseMemberCycleProcessRepository.findById(defaultRequest.getProcessId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_PROCESS_INVALID));

        // 查询会员默认流程
        MemberCycleProcessDO defaultProcess = memberCycleProcessRepository.findFirstByMemberIdAndRoleIdAndProcessTypeAndIsDefault(loginUser.getMemberId(), loginUser.getMemberRoleId(), process.getProcessType(), ProcessDefaultEnum.YES.getCode());

        if (Objects.isNull(defaultProcess)) {

            // 查询系统默认流程
            MemberCycleProcessDO defaultProcessPaas = Optional.ofNullable(memberCycleProcessRepository.findFirstByMemberIdAndRoleIdAndProcessTypeAndIsDefault(0L, 0L, process.getProcessType(), ProcessDefaultEnum.YES.getCode())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_DEFAULT_PROCESS_NON_EXISTENT));

            // 创建会员默认流程
            defaultProcess = BeanUtil.copyProperties(defaultProcessPaas, MemberCycleProcessDO.class);
            defaultProcess.setId(null);
            defaultProcess.setProcessKey(process.getProcessKey());
            defaultProcess.setName(Optional.ofNullable(process.getProcessName()).orElse(process.getName()));
            defaultProcess.setBaseProcess(process);
            defaultProcess.setSource(ProcessSourceEnum.SYSTEM.getCode());
            defaultProcess.setMemberId(loginUser.getMemberId());
            defaultProcess.setRoleId(loginUser.getMemberRoleId());
            memberCycleProcessRepository.save(defaultProcess);
            return ;
        }

        defaultProcess.setProcessKey(process.getProcessKey());
        defaultProcess.setName(Optional.ofNullable(process.getProcessName()).orElse(process.getName()));
        defaultProcess.setBaseProcess(process);
        memberCycleProcessRepository.save(defaultProcess);

    }

    /**
     * 新增流程规则
     * @param loginUser 登录用户信息
     * @param saveVO 流程规则
     * @return 新增结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(UserLoginCacheDTO loginUser, MemberCycleProcessReq saveVO) {
        // step 1: 新增校验
        BaseMemberCycleProcessDO checkWrapperResp = checkSave( saveVO);

        // step 2: 组装数据
        MemberCycleProcessDO entity = builderEntity(loginUser, checkWrapperResp, saveVO);

        // step 3: 保存数据库
        memberCycleProcessRepository.saveAndFlush(entity);

        // step 4: 调用规则引擎服务->保存流程引擎规则
        processEngineRuleFeign.saveEngineRule(builderEngineRuleSaveDTO(entity, saveVO.getEngineRuleList()));

    }

    /**
     * 封装请求参数
     *
     * @param entity               物料流程规则配置实体
     * @param processEngineRuleReqs 请求参数
     * @return 操作结果
     */
    private ProcessEngineRuleSaveDTO builderEngineRuleSaveDTO(MemberCycleProcessDO entity, List<ProcessEngineRuleReq> processEngineRuleReqs) {
        ProcessEngineRuleSaveDTO engineRuleSaveDTO = new ProcessEngineRuleSaveDTO();
        engineRuleSaveDTO.setProcessEngineId(entity.getBaseProcess().getEngineId());
        engineRuleSaveDTO.setProcessRuleId(entity.getId());
        engineRuleSaveDTO.setMemberId(entity.getMemberId());
        engineRuleSaveDTO.setMemberRoleId(entity.getRoleId());
        engineRuleSaveDTO.setEngineRuleList(processEngineRuleReqs);
        return engineRuleSaveDTO;
    }

    /**
     * 校验保存信息
     * @param saveVO    保存信息
     * @return  BaseMemberCycleProcessDO
     */
    private BaseMemberCycleProcessDO checkSave(MemberCycleProcessReq saveVO) {
        // step 1: 判断基础流程
        BaseMemberCycleProcessDO baseProcess = baseMemberCycleProcessRepository.findById(saveVO.getBaseProcessId()).orElse(null);
        if (Objects.isNull(baseProcess)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_BASE_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        return baseProcess;
    }

    /**
     * 构建会员流程数据
     * @param sysUser       登录用户
     * @param baseProcess   基础流程
     * @param saveVO        保存信息
     * @return  MemberCycleProcessDO
     */
    private MemberCycleProcessDO builderEntity(UserLoginCacheDTO sysUser, BaseMemberCycleProcessDO baseProcess, MemberCycleProcessReq saveVO) {
        MemberCycleProcessDO entity = new MemberCycleProcessDO();
        entity.setMemberId(sysUser.getMemberId());
        entity.setRoleId(sysUser.getMemberRoleId());
        entity.setName(saveVO.getName());
        entity.setBaseProcess(baseProcess);
        entity.setProcessType(baseProcess.getProcessType());
        entity.setProcessKey(baseProcess.getProcessKey());
        entity.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        entity.setIsDefault(ProcessDefaultEnum.NO.getCode());
        entity.setSource(ProcessSourceEnum.SYSTEM.getCode());
        return entity;
    }

    /**
     * 查询流程规则详情
     * @param loginUser 登录用户信息
     * @param processId 流程id
     * @return 查询结果
     */
    @Override
    public MemberCycleProcessDetailResp getInfo(UserLoginCacheDTO loginUser, Long processId) {
        MemberCycleProcessDO entity = memberCycleProcessRepository.findById(processId).orElse(null);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        MemberCycleProcessDetailResp target = new MemberCycleProcessDetailResp();
        target.setProcessId(entity.getId());
        BaseMemberCycleProcessDO baseProcess = entity.getBaseProcess();
        target.setBaseProcessId(baseProcess.getId());
        BaseMemberCycleProcessResp baseProcessVO = builderBaseMemberCycleProcessVO(baseProcess);
        target.setBaseProcess(baseProcessVO);
        target.setProcessType(baseProcess.getProcessType());
        target.setProcessTypeName(MemberCycleProcessTypeEnum.getMessage(baseProcess.getProcessType()));
        target.setName(entity.getName());
        target.setStatus(entity.getStatus());
        target.setStatusName(EnableDisableStatusEnum.getNameByCode(entity.getStatus()));
        target.setIsDefault(entity.getIsDefault());

        // 调用规则引擎服务->获取流程引擎规则
        ProcessEngineRuleQueryReq engineRuleQuery = new ProcessEngineRuleQueryReq();
        engineRuleQuery.setProcessRuleId(processId);
        engineRuleQuery.setProcessType(ProcessTypeEnum.MEMBER_LOFT_CYCLE.getCode());
        engineRuleQuery.setMemberId(loginUser.getMemberId());
        engineRuleQuery.setMemberRoleId(loginUser.getMemberRoleId());
        WrapperResp<List<ProcessEngineRuleReq>> engineRuleWrapperResp = processEngineRuleFeign.getEngineRule(engineRuleQuery);
        if (engineRuleWrapperResp.getCode() != WrapperUtil.success().getCode()) {
            throw new BusinessException(engineRuleWrapperResp.getCode(), engineRuleWrapperResp.getMessage());
        }
        target.setEngineRuleList(engineRuleWrapperResp.getData());

        return target;
    }

    /**
     * 修改流程规则
     * @param loginUser 登录用户信息
     * @param updateVO 修改数据
     * @return 修改结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(UserLoginCacheDTO loginUser, MemberCycleProcessUpdateReq updateVO) {
        // step 1:校验
        MemberCycleProcessDO entity = checkUpdate(updateVO);
        // step 2:组装数据
        entity.setCreateTime(LocalDateTime.now());
        entity.setMemberId(loginUser.getMemberId());
        entity.setRoleId(loginUser.getMemberRoleId());
        entity.setName(updateVO.getName());
        entity.setProcessType(entity.getBaseProcess().getProcessType());
        entity.setProcessKey(entity.getBaseProcess().getProcessKey());
        entity.setStatus(entity.getStatus());

        // step 3:修改数据库
        memberCycleProcessRepository.saveAndFlush(entity);

        // step 4: 调用规则引擎服务->保存流程引擎规则
        processEngineRuleFeign.saveEngineRule(builderEngineRuleSaveDTO(entity, updateVO.getEngineRuleList()));

    }

    /**
     * 校验更新信息
     * @param updateVO  更新信息
     * @return MemberCycleProcessDO
     */
    private MemberCycleProcessDO checkUpdate(MemberCycleProcessUpdateReq updateVO) {
        // step 1: 查询物料流程是否存在
        MemberCycleProcessDO entity = memberCycleProcessRepository.findById(updateVO.getProcessId()).orElse(null);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        if(Objects.equals(EnableDisableStatusEnum.ENABLE.getCode(), entity.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_NOT_UPDATE_PERMISSION);
        }
        // step 2: 查询基础流程是否存在
        BaseMemberCycleProcessDO baseProcess = baseMemberCycleProcessRepository.findById(updateVO.getBaseProcessId()).orElse(null);
        if (Objects.isNull(baseProcess)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_BASE_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        entity.setBaseProcess(baseProcess);
        return entity;
    }

    /**
     * 修改流程状态
     * @param loginUser 登录用户信息
     * @param updateStatusVO 修改状态
     * @return 修改结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatus(UserLoginCacheDTO loginUser, MemberCycleProcessUpdateStatusReq updateStatusVO) {
        // step 1: 查询物料流程是否存在
        MemberCycleProcessDO entity = memberCycleProcessRepository.findById(updateStatusVO.getProcessId()).orElse(null);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        entity.setStatus(updateStatusVO.getStatus());
        entity.setCreateTime(LocalDateTime.now());
        memberCycleProcessRepository.saveAndFlush(entity);

        // 更新引擎状态
        UpdateEngineReq updateEngine = new UpdateEngineReq();
        updateEngine.setProcessKey(entity.getProcessKey());
        updateEngine.setProcessRuleId(entity.getId());
        updateEngine.setMemberId(loginUser.getMemberId());
        updateEngine.setMemberRoleId(loginUser.getMemberRoleId());
        updateEngine.setState(updateStatusVO.getStatus());
        WrapperResp<Void> wrapperResp = engineFeign.updateEngineState(updateEngine);
        if (WrapperUtil.isFail(wrapperResp.getCode())) {
            throw new BusinessException(wrapperResp.getCode(), wrapperResp.getMessage());
        }

    }

    /**
     * 删除流程规则
     * @param loginUser 登录用户信息
     * @param processId 要删除的流程规则id
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(UserLoginCacheDTO loginUser, Long processId) {
        MemberCycleProcessDO entity = memberCycleProcessRepository.findById(processId).orElse(null);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }
        if(Objects.equals(EnableDisableStatusEnum.ENABLE.getCode(), entity.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_NOT_DELETE_PERMISSION);
        }
        memberCycleProcessRepository.delete(entity);

        // 删除引擎
        DeleteEngineReq deleteEngine = new DeleteEngineReq();
        deleteEngine.setProcessKey(entity.getProcessKey());
        deleteEngine.setProcessRuleId(entity.getId());
        deleteEngine.setMemberId(loginUser.getMemberId());
        deleteEngine.setMemberRoleId(loginUser.getMemberRoleId());
        WrapperResp<Void> wrapperResp = engineFeign.deleteEngine(deleteEngine);
        if (WrapperUtil.isFail(wrapperResp.getCode())) {
            throw new BusinessException(wrapperResp.getCode(), wrapperResp.getMessage());
        }

    }

    /**
     * 查询流程规则引擎详情
     * @param loginUser 登录用户信息
     * @param request   查询参数
     * @return 查询结果
     */
    @Override
    public RuleEngineProcessResp getMemberCycleProcesses(UserLoginCacheDTO loginUser, RuleEngineProcessReq request) {

        // 查询流程
        MemberCycleProcessDO process = memberCycleProcessRepository.findByMemberIdAndRoleIdAndId(loginUser.getMemberId(), loginUser.getMemberRoleId(), request.getProcessId());

        // 查询系统默认流程
        if (Objects.isNull(process)) {
            process = memberCycleProcessRepository.findByMemberIdAndRoleIdAndId(0L, 0L, request.getProcessId());
        }

        if (Objects.isNull(process)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_PLATFORM_MEMBER_CYCLE_PROCESS_DOES_NOT_EXIST);
        }

        // 创建返回对象
        RuleEngineProcessResp response = new RuleEngineProcessResp();
        response.setProcessId(request.getProcessId());
        response.setProcessKey(process.getProcessKey());
        response.setProcessType(process.getProcessType());

        // 获取规则引擎配置
        SimpleProcessReq simpleProcessReq = new SimpleProcessReq();
        simpleProcessReq.setProcessId(process.getProcessKey());
        WrapperResp<List<ProcessRuleConfigResp>> wrapperResp = processRuleConfigFeign.getProcessRuleConfig(simpleProcessReq);
        if (WrapperUtil.isFail(wrapperResp.getCode())){
            throw new BusinessException(wrapperResp.getCode(), wrapperResp.getMessage());
        }

        // 封装流程步骤
        List<ProcessRuleConfigResp> data = wrapperResp.getData();
        if (!CollectionUtils.isEmpty(data)) {
            List<SimpleProcessResp> simpleProcessRespons = data.stream().filter(Objects::nonNull).map(engine -> {
                SimpleProcessResp processResponse = new SimpleProcessResp();
                processResponse.setTaskStep(engine.getProcessStep());
                processResponse.setTaskName(engine.getProcessStepName());
                return processResponse;
            }).collect(Collectors.toList());
            response.setResponses(simpleProcessRespons);
            response.setProcessName(data.get(0).getProcessName());
        }

        return response;
    }
}
