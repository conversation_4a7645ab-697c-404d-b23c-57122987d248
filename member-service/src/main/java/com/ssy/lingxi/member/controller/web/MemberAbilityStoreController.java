//package com.ssy.lingxi.member.controller.web;
//
//import com.ssy.lingxi.common.model.resp.PageDataResp;
//import com.ssy.lingxi.common.model.resp.WrapperResp;
//import com.ssy.lingxi.member.model.req.lifecycle.CommonIdReq;
//import com.ssy.lingxi.member.model.req.maintenance.MemberStoreAddReq;
//import com.ssy.lingxi.member.model.req.maintenance.MemberStorePageDataReq;
//import com.ssy.lingxi.member.model.req.maintenance.MemberStoreUpdateReq;
//import com.ssy.lingxi.member.model.resp.maintenance.MemberStoreDetailResp;
//import com.ssy.lingxi.member.model.resp.maintenance.MemberStorePageQueryResp;
//import com.ssy.lingxi.member.service.web.IMemberAbilityStoreService;
//import org.springframework.http.HttpHeaders;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.validation.Valid;
//
///**
// * 系统能力 - 权限管理 - 门店管理
// * <AUTHOR>
// * @version 2.0.0
// * @since 2022/02/10
// */
//@RestController
//@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/store")
//public class MemberAbilityStoreController {
//
//    @Resource
//    private IMemberAbilityStoreService memberAbilityStoreService;
//
//
//    /**
//     * 门店管理分页查询列表
//     * @param headers HttpHeaders信息
//     * @param pageVO  接口参数
//     * @return 查询结果
//     */
//    @GetMapping("/page")
//    public WrapperResp<PageDataResp<MemberStorePageQueryResp>> memberStorePages(@RequestHeader HttpHeaders headers, @Valid MemberStorePageDataReq pageVO) {
//        return memberAbilityStoreService.memberStorePages(headers, pageVO);
//    }
//
//    /**
//     * 查看门店详情
//     * @param headers HttpHeaders信息
//     * @param idVO    接口参数
//     * @return 查询结果
//     */
//    @GetMapping("/detail")
//    public WrapperResp<MemberStoreDetailResp> memberStoreDetail(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
//        return memberAbilityStoreService.memberStoreDetail(headers, idVO);
//    }
//
//    /**
//     * 新增门店
//     * @param headers HttpHeaders信息
//     * @param addVO   接口参数
//     * @return 操作结果
//     */
//    @PostMapping("/add")
//    public WrapperResp<Void> addMemberStore(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberStoreAddReq addVO) {
//        return memberAbilityStoreService.addMemberStore(headers, addVO);
//    }
//
//    /**
//     * 修改门店
//     * @param headers  HttpHeaders信息
//     * @param updateVO 接口参数
//     * @return 操作结果
//     */
//    @PostMapping("/update")
//    public WrapperResp<Void> updateMemberStore(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberStoreUpdateReq updateVO) {
//        return memberAbilityStoreService.updateMemberStore(headers, updateVO);
//    }
//
//    /**
//     * 删除门店
//     * @param headers HttpHeaders信息
//     * @param idVO    接口参数
//     * @return 操作结果
//     */
//    @PostMapping("/delete")
//    public WrapperResp<Void> deleteMemberStore(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idVO) {
//        return memberAbilityStoreService.deleteMemberStore(headers, idVO);
//    }
//
//    /**
//     * 启用/停用门店
//     * @param headers HttpHeaders信息
//     * @param idVO    接口参数
//     * @return 操作结果
//     */
//    @PostMapping("/enable")
//    public WrapperResp<Void> enableMemberStore(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdReq idVO) {
//        return memberAbilityStoreService.enableMemberStore(headers, idVO);
//    }
//}
