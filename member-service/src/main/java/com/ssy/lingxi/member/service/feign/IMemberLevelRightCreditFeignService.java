package com.ssy.lingxi.member.service.feign;

import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;

import java.util.List;

/**
 * 会员等级（Level）、权益（Right）、信用（Credit）对外Feign服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-20
 */
public interface IMemberLevelRightCreditFeignService {

    /**
     * 查询平台会员的等级、权益、信用积分（多个）
     * @param memberIdList 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    List<MemberFeignLrcResp> getPlatformMemberLrcBatch(List<Long> memberIdList);

    /**
     * 查询下级会员的价格权益设置
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    MemberFeignRightResp getMemberPriceRight(MemberFeignRelationReq relationVO);

    /**
     * 批量查询下级会员与平台的积分（积分抵扣订单金额用）
     * @param batchVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignRightByOrderResp> batchMemberPriceRightForOrder(MemberFeignBatchReq batchVO);

    /**
     * 批量查询下级会员的价格权益
     * @param batchVO 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    List<MemberFeignRightDetailResp> batchMemberPriceRight(MemberFeignBatchReq batchVO);

    /**
     * 批量查询下级会员的价格权益
     * @param feignRelations 接口参数
     * @return 下级会员在上级会员下的价格权益
     */
    List<MemberFeignRelationRightDetailResp> batchMemberPriceRight(List<MemberFeignRelationReq> feignRelations);

    /**
     * 查询下级会员的返现权益设置
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的返现权益
     */
    MemberFeignRightResp getMemberReturnRight(MemberFeignRelationReq relationVO);

    /**
     * 查询下级会员在平台后台、上级会员下的返现权益设置
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的返现权益
     */
    List<MemberFeignRightDetailResp> findMemberReturnRight(MemberFeignRelationReq relationVO);

    /**
     * 查询下级会员的等级、权益、信用积分
     * @param relationVO 接口参数
     * @return 下级会员在上级会员下的等级、权益、信用积分
     */
    MemberFeignLrcResp getMemberLrc(MemberFeignRelationReq relationVO);

    /**
     * 查询平台会员的等级、权益、信用积分
     * @param feignVO 接口参数
     * @return 平台会员的等级、权益、信用积分
     */
    MemberFeignLrcResp getPlatformMemberLrc(MemberFeignReq feignVO);

    /**
     * 订单完成后，计算等级、权益、信用等信息
     * @param orderVO 接口参数
     * @return 计算结果
     */
    MemberFeignReturnRightResp calculateMemberLrcByOrder(MemberFeignOrderReq orderVO);

    /**
     * 积分支付订单，校验可用信用积分、支付密码，再异步计算下级会员的权益积分
     * @param spendVO 接口参数
     * @return 操作结果
     */
    void calculateMemberUsedRightPoint(MemberFeignRightSpendReq spendVO);

    /**
     * 售后评论成功后，计算会员信用积分
     * @param commentVO 接口参数
     * @return 操作结果
     */
    void calculateMemberAfterSaleCreditPoint(MemberFeignAfterSaleCommentReq commentVO);

    /**
     * 根据memberLevelConfigId查询会员等级配置信息
     * @param memberLevelConfigId 接口参数
     * @return 返回结果
     */
    List<MemberFeignLevelConfigResp> getMemberLevelConfigBatch(List<Long> memberLevelConfigId);

    /**
     * 积分抵扣订单金额，校验可用信用积分、支付密码，扣除积分
     * @param spendVO 接口参数
     * @return 操作结果
     */
    void calculateMemberDeductionRightPoint(MemberFeignRightDeductionReq spendVO);

    /**
     * 返还-抵扣订单金额的积分
     * @param returnVO 接口参数
     * @return 查询结果
     */
    void returnMemberRightPoint(MemberFeignRightReturnReq returnVO);

    /**
     * 根据上级会员Id和上级会员角色Id，以及当前用户，查询价格权益参数设置
     * @param upperVO 接口参数
     * @return 查询结果
     */
    MemberFeignManageMemberCreditParameterResp getUpperMemberCreditParameter(MemberFeignManageUpperMemberAndRoleReq upperVO);

    /**
     * 查询下级会员在上级会员下的等级
     * @param feignVO 接口参数
     * @return 查询结果
     */
    List<MemberFeignLevelDetailResp> findSubMemberLevels(MemberFeignReq feignVO);

    /**
     * 查询会员等级配置
     * @param req 接口参数
     * @return 等级配置列表
     */
    List<MemberFeignLevelResp> findMemberLevelConfigs(MemberFeignLevelReq req);

    /**
     * V3 - 订单、营销服务查询供应商信息
     * @param req 接口参数
     * @return 查询结果
     */
    List<MemberFeignCalcResp> findVendors(MemberFeignCalcReq req);
}
