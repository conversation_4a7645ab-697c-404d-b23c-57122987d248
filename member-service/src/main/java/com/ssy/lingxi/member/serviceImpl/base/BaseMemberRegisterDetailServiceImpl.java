package com.ssy.lingxi.member.serviceImpl.base;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ssy.lingxi.common.enums.member.MemberConfigTagEnum;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.component.base.util.ThreadLocalUtils;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.DetailCheckBO;
import com.ssy.lingxi.member.entity.bo.DetailValueBO;
import com.ssy.lingxi.member.entity.bo.MemberDetailLabelBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskResultBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigLabelDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.resp.basic.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseMemberDepositDetailService;
import com.ssy.lingxi.member.service.base.IBaseMemberRegisterDetailService;
import com.ssy.lingxi.member.service.feign.ITemplateFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.util.RgConfigUtil;
import com.ssy.lingxi.member.util.SecurityStringUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 会员注册信息查询相关接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-01-14
 */
@Service
public class BaseMemberRegisterDetailServiceImpl implements IBaseMemberRegisterDetailService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberRegisterDetailRepository memberRegisterDetailRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private ITemplateFeignService platformTemplateFeignService;

    @Resource
    private MemberRegisterConfigRepository memberRegisterConfigRepository;

    @Resource
    @Lazy
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    /**
     * 将会员注册资料转换为前端注册页面、新增会员页面内容
     *
     * @param memberConfigs 会员注册资料
     * @return 转换结果
     */
    @Override
    public List<MemberConfigGroupResp> groupMemberConfig(List<MemberRegisterConfigDO> memberConfigs) {
        if (CollectionUtils.isEmpty(memberConfigs)) {
            return new ArrayList<>();
        }

        //筛选出状态为 “启用” 的配置，按照groupName进行分组
        Map<String, List<MemberRegisterConfigDO>> groupList = memberConfigs.stream().filter(c -> c.getFieldStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(RgConfigUtil::getFieldGroupName))
                .entrySet().stream()
                .sorted(Comparator.comparingInt(
                        entry -> entry.getValue().get(0).getFieldOrder()
                )).collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));

        return groupList.entrySet().stream().map(e -> {
            MemberConfigGroupResp configGroup = new MemberConfigGroupResp();
            configGroup.setGroupName(e.getKey());

            List<ConfigDetailResp> elements = e.getValue().stream().map(c -> {
                ConfigDetailResp detail = new ConfigDetailResp();
                detail.setId(c.getId());
                detail.setFieldName(c.getFieldName());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c));
                detail.setFieldType(c.getFieldType());
                detail.setAttr(c.getAttr());
                detail.setFieldEmpty(c.getFieldEmpty());
                detail.setFieldLength(c.getFieldLength());
                detail.setFieldOrder(c.getFieldOrder());
                detail.setFieldRemark(RgConfigUtil.getFieldRemark(c));
                detail.setRuleEnum(c.getRuleEnum());
                detail.setPattern(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getRuleEnum()));
                detail.setMsg(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getRuleEnum()));
                detail.setFieldEnum(CollectionUtils.isEmpty(c.getLabels()) ? new ArrayList<>() : c.getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));
                detail.setConfigs(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initConfigs(c.getId()) : new ArrayList<>());
                return detail;
            }).sorted(Comparator.comparingInt(ConfigDetailResp::getFieldOrder)).collect(Collectors.toList());

            configGroup.setElements(elements);
            return configGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 查询该字段包含的子字段
     * @param id 父id
     * @return 子列表集
     */
    private List<ConfigDetailResp> initConfigs(Long id) {
        List<MemberRegisterConfigDO> configs = memberRegisterConfigRepository.findAllByParentId(id);
        return configs.stream().filter(config -> config.getFieldStatus().equals(MemberConfigStatusEnum.ENABLED.getCode())).map(c -> {
            ConfigDetailResp detail = new ConfigDetailResp();
            detail.setId(c.getId());
            detail.setFieldName(c.getFieldName());
            detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c));
            detail.setFieldType(c.getFieldType());
            detail.setAttr(c.getAttr());
            detail.setFieldEmpty(c.getFieldEmpty());
            detail.setFieldLength(c.getFieldLength());
            detail.setFieldOrder(c.getFieldOrder());
            detail.setFieldRemark(RgConfigUtil.getFieldRemark(c));
            detail.setRuleEnum(c.getRuleEnum());
            detail.setPattern(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getRuleEnum()));
            detail.setMsg(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getRuleEnum()));
            detail.setFieldEnum(CollectionUtils.isEmpty(c.getLabels()) ? new ArrayList<>() : c.getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));
            detail.setConfigs(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initConfigs(c.getId()) : new ArrayList<>());
            return detail;
        }).sorted(Comparator.comparingInt(ConfigDetailResp::getFieldOrder)).collect(Collectors.toList());
    }

    /**
     * 会员注册时，校验注册资料，并返回会员名称
     *
     * @param detailMap          注册资料
     * @param memberConfigList   角色关联的会员注册资料配置
     * @param registerDetailList 返回的注册资料，其版本为“待审核”
     * @param defaultName        如果注册资料为空，用手机号作为公司名称
     * @return 校验结果
     */
    @Override
    public String checkMemberRegisterDetail(Map<String, Object> detailMap, List<MemberRegisterConfigDO> memberConfigList, List<MemberRegisterDetailDO> registerDetailList, String defaultName) {
        if(CollectionUtils.isEmpty(memberConfigList)) {
            return defaultName;
        }

        if(CollectionUtils.isEmpty(detailMap)) {
            throw new BusinessException(ResponseCodeEnum.MC_MC_MEMBER_DETAIL_CAN_NOT_BE_EMPTY);
        }

        String memberName = "";
        for (Map.Entry<String, Object> entry : detailMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() instanceof HashMap || entry.getValue() instanceof ArrayList ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());

            MemberRegisterConfigDO configDO = memberConfigList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
            if (configDO == null) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DETAIL_FIELD_DOES_NOT_EXIST);
            }

            if (configDO.getFieldStatus() == 0) {
                continue;
            }


            DetailCheckBO checkResult = checkDetail(configDO, value);
            if(MemberConfigTagEnum.MEMBER_NAME.getCode().equals(checkResult.getTagEnum())) {
                memberName = checkResult.getMemberName();
            }

            MemberRegisterDetailDO memberRegisterDetailDO = new MemberRegisterDetailDO();
            memberRegisterDetailDO.setMemberConfig(configDO);
//            memberRegisterDetailDO.setFieldGroupName(configDO.getFieldGroupName());
            //版本
            memberRegisterDetailDO.setVersion(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());
//            memberRegisterDetailDO.setFieldName(configDO.getFieldName());
//            memberRegisterDetailDO.setFieldLocalName(configDO.getFieldLocalName());
            memberRegisterDetailDO.setFieldType(configDO.getFieldType());
            memberRegisterDetailDO.setAttr(configDO.getAttr());
            memberRegisterDetailDO.setTagEnum(configDO.getTagEnum());
            memberRegisterDetailDO.setStatus(configDO.getFieldStatus());
            memberRegisterDetailDO.setValidate(configDO.getValidate());
            memberRegisterDetailDO.setAllowSelect(configDO.getAllowSelect());
            memberRegisterDetailDO.setDetail(MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) ? "" : checkResult.getDetail());
            memberRegisterDetailDO.setProvinceCode(checkResult.getProvinceCode());
            memberRegisterDetailDO.setProvinceName(checkResult.getProvinceName());
            memberRegisterDetailDO.setCityCode(checkResult.getCityCode());
            memberRegisterDetailDO.setCityName(checkResult.getCityName());
            memberRegisterDetailDO.setDistrictCode(checkResult.getDistrictCode());
            memberRegisterDetailDO.setDistrictName(checkResult.getDistrictName());
            memberRegisterDetailDO.setLabels(checkResult.getLabels());
            // 是列表字段
            if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) && StringUtils.hasLength(checkResult.getDetail())) {
                List<MemberRegisterConfigDO> memberConfigs = memberRegisterConfigRepository.findAllByParentId(configDO.getId());
                List<Map<String, Object>> details = JSONObject.parseObject(checkResult.getDetail(), new TypeReference<List<Map<String, Object>>>(){});
                List<MemberRegisterDetailDO> registerResult = foreachRegisters(details, memberConfigs);

                memberRegisterDetailDO.setRegisters(registerResult);
            }
            registerDetailList.add(memberRegisterDetailDO);
        }

        return StringUtils.hasLength(memberName) ? memberName : defaultName;
    }

    private List<MemberRegisterDetailDO> foreachRegisters(List<Map<String, Object>> detailMaps, List<MemberRegisterConfigDO> memberConfigList) {
        List<MemberRegisterDetailDO> registerDetailList = new ArrayList<>();
        int index = 1;
        for (Map<String, Object> detailMap : detailMaps) {
            for (Map.Entry<String, Object> entry : detailMap.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue() instanceof HashMap || entry.getValue() instanceof ArrayList ? SerializeUtil.serialize(entry.getValue()) : String.valueOf(entry.getValue());

                MemberRegisterConfigDO configDO = memberConfigList.stream().filter(c -> c.getFieldName().equals(key)).findFirst().orElse(null);
                if (configDO == null) {
                    throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DETAIL_FIELD_DOES_NOT_EXIST);
                }

                DetailCheckBO checkResult = checkDetail(configDO, value);

                MemberRegisterDetailDO memberRegisterDetailDO = new MemberRegisterDetailDO();
                memberRegisterDetailDO.setMemberConfig(configDO);
//                memberRegisterDetailDO.setFieldGroupName(configDO.getFieldGroupName());
                //版本 子字段与父字段绑定，不区分版本
//                memberRegisterDetailDO.setVersion(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode());
//                memberRegisterDetailDO.setFieldName(configDO.getFieldName());
//                memberRegisterDetailDO.setFieldLocalName(configDO.getFieldLocalName());
                memberRegisterDetailDO.setFieldType(configDO.getFieldType());
                memberRegisterDetailDO.setAttr(configDO.getAttr());
                memberRegisterDetailDO.setTagEnum(configDO.getTagEnum());
                memberRegisterDetailDO.setStatus(configDO.getFieldStatus());
                memberRegisterDetailDO.setValidate(configDO.getValidate());
                memberRegisterDetailDO.setAllowSelect(configDO.getAllowSelect());
                memberRegisterDetailDO.setDetail(MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) ? "" : checkResult.getDetail());
                memberRegisterDetailDO.setProvinceCode(checkResult.getProvinceCode());
                memberRegisterDetailDO.setProvinceName(checkResult.getProvinceName());
                memberRegisterDetailDO.setCityCode(checkResult.getCityCode());
                memberRegisterDetailDO.setCityName(checkResult.getCityName());
                memberRegisterDetailDO.setDistrictCode(checkResult.getDistrictCode());
                memberRegisterDetailDO.setDistrictName(checkResult.getDistrictName());
                memberRegisterDetailDO.setLabels(checkResult.getLabels());
                // 是列表字段
                if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType()) && StringUtils.hasLength(checkResult.getDetail())) {
                    List<MemberRegisterConfigDO> memberConfigs = memberRegisterConfigRepository.findAllByParentId(configDO.getId());
                    List<Map<String, Object>> details = JSONObject.parseObject(checkResult.getDetail(), new TypeReference<List<Map<String, Object>>>(){});
                    List<MemberRegisterDetailDO> registerResult = foreachRegisters(details, memberConfigs);

                    memberRegisterDetailDO.setRegisters(registerResult);
                }
                memberRegisterDetailDO.setGroupIndex(index);
                registerDetailList.add(memberRegisterDetailDO);
            }
            index++;
        }
        return registerDetailList;
    }

    /**
     * 比较两个版本的注册资料，是否有修改
     *
     * @param member            会员
     * @param toValidateDetails 新增的注册资料
     * @return 是否有修改
     */
    @Override
    public boolean isDetailModified(MemberDO member, List<MemberRegisterDetailDO> toValidateDetails) {
        List<MemberRegisterDetailDO> usingDetails = memberRegisterDetailRepository.findByMemberAndVersion(member, MemberDetailVersionEnum.USING.getCode());
        if(CollectionUtils.isEmpty(usingDetails) && CollectionUtils.isEmpty(toValidateDetails)) {
            return false;
        }

        if(CollectionUtils.isEmpty(usingDetails) && !CollectionUtils.isEmpty(toValidateDetails)) {
            return true;
        }

        if(!CollectionUtils.isEmpty(usingDetails) && CollectionUtils.isEmpty(toValidateDetails)) {
            return true;
        }

        if(usingDetails.size() != toValidateDetails.size()) {
            return true;
        }

        //比较字段、值
        return toValidateDetails.stream().anyMatch(toValidateDetail -> usingDetails.stream().noneMatch(usingDetail -> usingDetail.getMemberConfig().getFieldName().equals(toValidateDetail.getMemberConfig().getFieldName()) && usingDetail.getDetail().equals(toValidateDetail.getDetail())));
    }

    /**
     * 查询会员注册资料
     *
     * @param member      会员
     * @param versionEnum 注册资料版本
     * @param memberRoleDO 角色
     * @return 查询结果
     */
    @Override
    public List<RegisterDetailGroupResp> groupMemberRegisterDetail(MemberDO member, MemberRoleDO memberRoleDO, MemberDetailVersionEnum versionEnum) {
        List<MemberRegisterDetailDO> registerDetails = Optional.ofNullable(memberRegisterDetailRepository.findByMemberAndVersion(member, versionEnum.getCode())).orElse(new ArrayList<>());

        //筛选出状态为 “启用” 的配置，按照groupName进行分组
        Map<String, List<MemberRegisterDetailDO>> groupList = registerDetails.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(memberRegisterConfigDO -> RgConfigUtil.getFieldGroupName(memberRegisterConfigDO.getMemberConfig())));

        if (memberRoleDO == null) {
            return new ArrayList<>();
        }

        // 注册资料分组
        Map<String, List<MemberRegisterConfigDO>> memberConfigGroups = memberRoleDO.getConfigs().stream().collect(Collectors.groupingBy(RgConfigUtil::getFieldGroupName))
                .entrySet().stream()
                .sorted(Comparator.comparingInt(
                        entry -> entry.getValue().get(0).getFieldOrder()
                )).collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new
                ));

        return memberConfigGroups.entrySet().stream().map(map -> {
            RegisterDetailGroupResp detailGroup = new RegisterDetailGroupResp();
            detailGroup.setGroupName(map.getKey());

            List<MemberRegisterDetailDO> registerDetailDOS = Optional.ofNullable(groupList.get(map.getKey())).orElse(new ArrayList<>());

            List<RegisterDetailResp> elements = map.getValue().stream().map(c -> {
                RegisterDetailResp detail = new RegisterDetailResp();
                detail.setFieldName(c.getFieldName());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c));
                detail.setFieldType(c.getFieldType());
                detail.setAttr(c.getAttr());
                detail.setFieldEmpty(c.getFieldEmpty());
                detail.setFieldLength(c.getFieldLength());
                detail.setFieldOrder(c.getFieldOrder());
                detail.setFieldRemark(RgConfigUtil.getFieldRemark(c));
                detail.setRuleEnum(c.getRuleEnum());
                detail.setPattern(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getRuleEnum()));
                detail.setMsg(c.getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getRuleEnum()));
                detail.setFieldEnum(CollectionUtils.isEmpty(c.getLabels()) ? new ArrayList<>() : c.getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));
                detail.setConfigs(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? baseMemberDepositDetailService.initMemberConfig(c.getId()) : new ArrayList<>());

                MemberRegisterDetailDO memberRegisterDetailDO = registerDetailDOS.stream().filter(registerDetail -> Objects.equals(registerDetail.getMemberConfig().getId(), c.getId())).findFirst().orElse(null);
                if (Objects.nonNull(memberRegisterDetailDO)){
                    DetailValueBO valueBO = getDetailValue(memberRegisterDetailDO);
                    detail.setFieldValue(valueBO.getFieldValue());
                    detail.setLastValue(valueBO.getLastValue());
                } else {
                    DetailValueBO valueBO = getDetailValue(c);
                    detail.setFieldValue(valueBO.getFieldValue());
                    detail.setLastValue(valueBO.getLastValue());
                }

                detail.setRegisters(detail.getFieldValue());
                detail.setLastRegisters(detail.getLastValue());

                return detail;
            }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 查询会员注册资料（Using版本优先，如无则查询正在审核的版本）
     *
     * @param member 会员
     * @return 查询结果
     */
    @Override
    public List<RegisterDetailGroupResp> switchMemberRegisterDetail(MemberDO member) {
        List<MemberRegisterDetailDO> registerDetailList = memberRegisterDetailRepository.findByMember(member);
        if (CollectionUtils.isEmpty(registerDetailList)) {
            return new ArrayList<>();
        }

        List<MemberRegisterDetailDO> registerDetails = registerDetailList.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(registerDetails)) {
            registerDetails = registerDetailList.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());
        }

        //筛选出状态为 “启用” 的配置，按照groupName进行分组
        Map<String, List<MemberRegisterDetailDO>> groupList = registerDetails.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(memberRegisterConfigDO -> RgConfigUtil.getFieldGroupName(memberRegisterConfigDO.getMemberConfig())));

        return groupList.entrySet().stream().map(e -> {
            RegisterDetailGroupResp detailGroup = new RegisterDetailGroupResp();
            detailGroup.setGroupName(e.getKey());

            List<RegisterDetailResp> elements = e.getValue().stream().map(c -> {
                RegisterDetailResp detail = new RegisterDetailResp();
                detail.setFieldName(c.getMemberConfig().getFieldName());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                detail.setFieldType(c.getFieldType());
                detail.setAttr(c.getAttr());
                detail.setFieldEmpty(c.getMemberConfig().getFieldEmpty());
                detail.setFieldLength(c.getMemberConfig().getFieldLength());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldRemark(RgConfigUtil.getFieldRemark(c.getMemberConfig()));
                detail.setRuleEnum(c.getMemberConfig().getRuleEnum());
                detail.setPattern(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getMemberConfig().getRuleEnum()));
                detail.setMsg(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getMemberConfig().getRuleEnum()));
                detail.setFieldEnum(CollectionUtils.isEmpty(c.getMemberConfig().getLabels()) ? new ArrayList<>() : c.getMemberConfig().getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));

                DetailValueBO valueBO = getDetailValue(c);
                detail.setFieldValue(valueBO.getFieldValue());
                detail.setLastValue(valueBO.getLastValue());
                detail.setRegisters(valueBO.getFieldValue());

                return detail;
            }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 查询会员注册资料（Using版本优先，如无则查询正在审核的版本）
     *
     * @param id 资料id
     * @return 查询结果
     */
    private List<List<RegisterDetailResp>> switchMemberRegister(Long id) {
        List<MemberRegisterDetailDO> registerDetailDOS = memberRegisterDetailRepository.findByParentId(id);
        Map<Integer, List<MemberRegisterDetailDO>> groupList = registerDetailDOS.stream().filter(detail -> MemberConfigStatusEnum.ENABLED.getCode().equals(detail.getStatus()))
                .collect(Collectors.groupingBy(MemberRegisterDetailDO::getGroupIndex));
        List<List<RegisterDetailResp>> result = new ArrayList<>();
        groupList.forEach((key, value) -> {
            List<RegisterDetailResp> collect = value.stream().map(c -> {
                RegisterDetailResp detail = new RegisterDetailResp();
                detail.setFieldName(c.getMemberConfig().getFieldName());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                detail.setFieldType(c.getFieldType());
                detail.setAttr(c.getAttr());
                detail.setFieldEmpty(c.getMemberConfig().getFieldEmpty());
                detail.setFieldLength(c.getMemberConfig().getFieldLength());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldRemark(RgConfigUtil.getFieldRemark(c.getMemberConfig()));
                detail.setRuleEnum(c.getMemberConfig().getRuleEnum());
                detail.setPattern(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getMemberConfig().getRuleEnum()));
                detail.setMsg(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getMemberConfig().getRuleEnum()));
                detail.setFieldEnum(CollectionUtils.isEmpty(c.getMemberConfig().getLabels()) ? new ArrayList<>() : c.getMemberConfig().getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));

                DetailValueBO valueBO = getDetailValue(c);
                detail.setFieldValue(valueBO.getFieldValue());
                detail.setLastValue(valueBO.getLastValue());
                detail.setRegisters(valueBO.getFieldValue());

                return detail;
            }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());
            result.add(key - 1, collect);
        });
        return result;
    }

    /**
     * 查询会员注册资料（两个版本合并）
     *
     * @param member 会员
     * @return 查询结果
     */
    @Override
    public List<RegisterDetailGroupResp> groupMemberRegisterDetail(MemberDO member) {
        List<MemberRegisterDetailDO> registerDetails = memberRegisterDetailRepository.findByMember(member);
        if (CollectionUtils.isEmpty(registerDetails)) {
            return new ArrayList<>();
        }

        List<MemberRegisterDetailDO> usingList = registerDetails.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        List<MemberRegisterDetailDO> validateList = registerDetails.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(validateList)) {
            //正在使用的版本
            Map<String, List<MemberRegisterDetailDO>> groupList = usingList.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                    .collect(Collectors.groupingBy(memberRegisterConfigDO -> RgConfigUtil.getFieldGroupName(memberRegisterConfigDO.getMemberConfig())));

            return groupList.entrySet().stream().map(e -> {
                RegisterDetailGroupResp detailGroup = new RegisterDetailGroupResp();
                detailGroup.setGroupName(e.getKey());

                List<RegisterDetailResp> elements = e.getValue().stream().map(c -> {
                    RegisterDetailResp detail = new RegisterDetailResp();
                    detail.setFieldName(c.getMemberConfig().getFieldName());
                    detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                    detail.setFieldType(c.getFieldType());
                    detail.setAttr(c.getAttr());
                    detail.setFieldEmpty(c.getMemberConfig().getFieldEmpty());
                    detail.setFieldLength(c.getMemberConfig().getFieldLength());
                    detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                    detail.setFieldRemark(RgConfigUtil.getFieldRemark(c.getMemberConfig()));
                    detail.setRuleEnum(c.getMemberConfig().getRuleEnum());
                    detail.setPattern(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getMemberConfig().getRuleEnum()));
                    detail.setMsg(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getMemberConfig().getRuleEnum()));
                    detail.setFieldEnum(CollectionUtils.isEmpty(c.getMemberConfig().getLabels()) ? new ArrayList<>() : c.getMemberConfig().getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));

                    DetailValueBO valueBO = getDetailValue(c);
                    detail.setFieldValue(valueBO.getFieldValue());
                    detail.setLastValue(valueBO.getLastValue());
                    detail.setRegisters(detail.getFieldValue());
                    detail.setLastRegisters(detail.getLastValue());
                    return detail;
                }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());

                detailGroup.setElements(elements);
                return detailGroup;
            }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
        } else {
            //待审核的版本
            Map<String, List<MemberRegisterDetailDO>> groupList = validateList.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                    .collect(Collectors.groupingBy(memberRegisterConfigDO -> RgConfigUtil.getFieldGroupName(memberRegisterConfigDO.getMemberConfig())));

            return groupList.entrySet().stream().map(e -> {
                RegisterDetailGroupResp detailGroup = new RegisterDetailGroupResp();
                detailGroup.setGroupName(e.getKey());

                List<RegisterDetailResp> elements = e.getValue().stream().map(c -> {
                    MemberRegisterDetailDO usingDetail = usingList.stream().filter(u -> u.getMemberConfig().getFieldName().equals(c.getMemberConfig().getFieldName())).findFirst().orElse(null);

                    RegisterDetailResp detail = new RegisterDetailResp();
                    detail.setFieldName(c.getMemberConfig().getFieldName());
                    detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                    detail.setFieldType(c.getFieldType());
                    detail.setAttr(c.getAttr());
                    detail.setFieldEmpty(c.getMemberConfig().getFieldEmpty());
                    detail.setFieldLength(c.getMemberConfig().getFieldLength());
                    detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                    detail.setFieldRemark(RgConfigUtil.getFieldRemark(c.getMemberConfig()));
                    detail.setRuleEnum(c.getMemberConfig().getRuleEnum());
                    detail.setPattern(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getMemberConfig().getRuleEnum()));
                    detail.setMsg(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getMemberConfig().getRuleEnum()));
                    detail.setFieldEnum(CollectionUtils.isEmpty(c.getMemberConfig().getLabels()) ? new ArrayList<>() : c.getMemberConfig().getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));
                    if(usingDetail == null) {
                        DetailValueBO valueBO = getDetailValue(c);
                        detail.setFieldValue(valueBO.getFieldValue());
                        detail.setLastValue(valueBO.getLastValue());
                    } else {
                        detail.setFieldValue(getSingleDetailValue(c));
                        detail.setLastValue(getSingleDetailValue(usingDetail));
                    }
                    detail.setRegisters(detail.getFieldValue());
                    detail.setLastRegisters(detail.getLastValue());

                    return detail;
                }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());

                detailGroup.setElements(elements);
                return detailGroup;
            }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
        }

    }

    /**
     * 查询会员注册资料（查询指定版本）
     *
     * @param member      会员
     * @param versionEnum 指定版本
     * @return 查询结果
     */
    @Override
    public List<DetailTextGroupResp> groupMemberRegisterDetailText(MemberDO member, MemberDetailVersionEnum versionEnum) {
        List<MemberRegisterDetailDO> registerDetails = memberRegisterDetailRepository.findByMemberAndVersion(member, versionEnum.getCode());
        if (CollectionUtils.isEmpty(registerDetails)) {
            return new ArrayList<>();
        }

        //筛选出状态为 “启用” 的配置，按照groupName进行分组
        Map<String, List<MemberRegisterDetailDO>> groupList = registerDetails.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(memberRegisterConfigDO -> RgConfigUtil.getFieldGroupName(memberRegisterConfigDO.getMemberConfig())));

        return groupList.entrySet().stream().map(e -> {
            DetailTextGroupResp detailGroup = new DetailTextGroupResp();
            detailGroup.setGroupName(e.getKey());

            List<DetailTextResp> elements = e.getValue().stream().map(c -> {
                DetailTextResp detail = new DetailTextResp();
                detail.setFieldType(c.getFieldType());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldValue(c.getDetail());
                detail.setLastValue("");
                detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                detail.setLastRegisters(new ArrayList<>());
                return detail;
            }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 查询该字段包含的子字段
     * @param id 父id
     * @return 子列表集
     */
    private List<List<DetailTextResp>> initDepository(Long id) {
        List<MemberRegisterDetailDO> registerDetailDOS = memberRegisterDetailRepository.findByParentId(id);
        Map<Integer, List<MemberRegisterDetailDO>> groupList = registerDetailDOS.stream().filter(detail -> MemberConfigStatusEnum.ENABLED.getCode().equals(detail.getStatus()))
                .collect(Collectors.groupingBy(MemberRegisterDetailDO::getGroupIndex));
        List<List<DetailTextResp>> result = new ArrayList<>();
        groupList.forEach((key, value) -> {
            List<DetailTextResp> collect = value.stream().map(c -> {
                DetailTextResp detail = new DetailTextResp();
                detail.setFieldType(c.getFieldType());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                detail.setFieldName(c.getMemberConfig().getFieldName());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldValue(c.getDetail());
                detail.setLastValue("");
                detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                return detail;
            }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());
            result.add(key - 1, collect);
        });
        return result;
    }

    /**
     * 查询会员注册资料（Using版本优先，如果没有则查询正在审核的版本）
     *
     * @param member 会员
     * @return 查询结果
     */
    @Override
    public List<DetailTextGroupResp> switchMemberRegisterDetailText(MemberDO member) {
        List<MemberRegisterDetailDO> registerDetailList = memberRegisterDetailRepository.findByMember(member);
        if (CollectionUtils.isEmpty(registerDetailList)) {
            return new ArrayList<>();
        }

        List<MemberRegisterDetailDO> registerDetails = registerDetailList.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(registerDetails)) {
            registerDetails = registerDetailList.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());
        }

        //筛选出状态为 “启用” 的配置，按照groupName进行分组
        Map<String, List<MemberRegisterDetailDO>> groupList = registerDetails.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(memberRegisterConfigDO -> RgConfigUtil.getFieldGroupName(memberRegisterConfigDO.getMemberConfig())));

        return groupList.entrySet().stream().map(e -> {
            DetailTextGroupResp detailGroup = new DetailTextGroupResp();
            detailGroup.setGroupName(e.getKey());

            List<DetailTextResp> elements = e.getValue().stream().map(c -> {
                DetailTextResp detail = new DetailTextResp();
                detail.setFieldType(c.getFieldType());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                detail.setFieldName(c.getMemberConfig().getFieldName());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldValue(c.getDetail());
                detail.setLastValue("");
                detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                return detail;
            }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 查询会员注册资料（文本显示，两个版本合并）
     *
     * @param member 会议
     * @return 查询结果
     */
    @Override
    public List<DetailTextGroupResp> groupMemberRegisterDetailText(MemberDO member) {
        List<MemberRegisterDetailDO> registerDetails = memberRegisterDetailRepository.findByMember(member);
        if (CollectionUtils.isEmpty(registerDetails)) {
            return new ArrayList<>();
        }

        List<MemberRegisterDetailDO> validateList = registerDetails.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());

        List<MemberRegisterDetailDO> usingList = registerDetails.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(validateList)) {
            //筛选出状态为 “启用” 的配置，按照groupName进行分组
            Map<String, List<MemberRegisterDetailDO>> groupList = usingList.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                    .collect(Collectors.groupingBy(memberRegisterConfigDO -> RgConfigUtil.getFieldGroupName(memberRegisterConfigDO.getMemberConfig())));

            return groupList.entrySet().stream().map(e -> {
                DetailTextGroupResp detailGroup = new DetailTextGroupResp();
                detailGroup.setGroupName(e.getKey());

                List<DetailTextResp> elements = e.getValue().stream().map(c -> {
                    DetailTextResp detail = new DetailTextResp();
                    detail.setFieldType(c.getFieldType());
                    detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                    detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                    detail.setFieldValue(c.getDetail());
                    detail.setLastValue("");
                    detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                    detail.setLastRegisters(new ArrayList<>());
                    return detail;
                }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());

                detailGroup.setElements(elements);
                return detailGroup;
            }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
        } else {
            Map<String, List<MemberRegisterDetailDO>> groupList = validateList.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                    .collect(Collectors.groupingBy(memberRegisterConfigDO -> RgConfigUtil.getFieldGroupName(memberRegisterConfigDO.getMemberConfig())));

            return groupList.entrySet().stream().map(e -> {
                DetailTextGroupResp detailGroup = new DetailTextGroupResp();
                detailGroup.setGroupName(e.getKey());

                List<DetailTextResp> elements = e.getValue().stream().map(c -> {
                    DetailTextResp detail = new DetailTextResp();
                    detail.setFieldType(c.getFieldType());
                    detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                    detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                    detail.setFieldValue(c.getDetail());

                    MemberRegisterDetailDO usingDetail = usingList.stream().filter(u -> u.getMemberConfig().getFieldName().equals(c.getMemberConfig().getFieldName())).findFirst().orElse(null);
                    detail.setLastValue(usingDetail == null ? "" : usingDetail.getDetail());
                    detail.setRegisters(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(c.getId()) : new ArrayList<>());
                    detail.setLastRegisters(usingDetail == null ? new ArrayList<>() : MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? initDepository(usingDetail.getId()) : new ArrayList<>());
                    return detail;
                }).sorted(Comparator.comparingInt(DetailTextResp::getFieldOrder)).collect(Collectors.toList());

                detailGroup.setElements(elements);
                return detailGroup;
            }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
        }
    }

    /**
     * 新增角色时，合并现有的注册资料与新增角色的注册资料
     *
     * @param member      会员
     * @param versionEnum 注册资料的版本
     * @param memberRoleDO      新增的角色
     * @return 合并结果
     */
    @Override
    public List<RegisterDetailGroupResp> mergeMemberRegisterDetail(MemberDO member, MemberDetailVersionEnum versionEnum, MemberRoleDO memberRoleDO) {
        List<MemberRegisterConfigDO> memberConfigs = new ArrayList<>(memberRoleDO.getConfigs());
        if (CollectionUtils.isEmpty(memberConfigs)) {
            return groupMemberRegisterDetail(member, memberRoleDO, versionEnum);
        }

        List<MemberRegisterDetailDO> registerDetails = memberRegisterDetailRepository.findByMemberAndVersion(member, versionEnum.getCode());

        List<MemberRegisterDetailDO> plusList = memberConfigs.stream().filter(memberConfig -> registerDetails.stream().noneMatch(registerDetail -> registerDetail.getMemberConfig().getId().equals(memberConfig.getId()))).map(memberConfig -> {
            MemberRegisterDetailDO detailDO = new MemberRegisterDetailDO();
//            detailDO.setFieldGroupName(memberConfig.getFieldGroupName());
            detailDO.setMemberConfig(memberConfig);
            detailDO.setFieldType(memberConfig.getFieldType());
//            detailDO.setFieldName(memberConfig.getFieldName());
            detailDO.setAllowSelect(memberConfig.getAllowSelect());
//            detailDO.setFieldLocalName(memberConfig.getFieldLocalName());
            detailDO.setAttr(memberConfig.getAttr());
            detailDO.setDetail("");
            detailDO.setProvinceCode("");
            detailDO.setCityCode("");
            detailDO.setDistrictCode("");
            detailDO.setLabels(new ArrayList<>());
            detailDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
            return detailDO;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(plusList)) {
            return groupMemberRegisterDetail(member, memberRoleDO, versionEnum);
        }

        registerDetails.addAll(plusList);

        //筛选出状态为 “启用” 的配置，按照groupName进行分组
        Map<String, List<MemberRegisterDetailDO>> groupList = registerDetails.stream().filter(c -> c.getStatus().equals(MemberConfigStatusEnum.ENABLED.getCode()))
                .collect(Collectors.groupingBy(memberRegisterConfigDO -> RgConfigUtil.getFieldGroupName(memberRegisterConfigDO.getMemberConfig())));

        return groupList.entrySet().stream().map(e -> {
            RegisterDetailGroupResp detailGroup = new RegisterDetailGroupResp();
            detailGroup.setGroupName(e.getKey());

            List<RegisterDetailResp> elements = e.getValue().stream().map(c -> {
                RegisterDetailResp detail = new RegisterDetailResp();
                detail.setFieldName(c.getMemberConfig().getFieldName());
                detail.setFieldLocalName(RgConfigUtil.getFieldLocalName(c.getMemberConfig()));
                detail.setFieldType(c.getFieldType());
                detail.setAttr(c.getAttr());
                detail.setFieldEmpty(c.getMemberConfig().getFieldEmpty());
                detail.setFieldLength(c.getMemberConfig().getFieldLength());
                detail.setFieldOrder(c.getMemberConfig().getFieldOrder());
                detail.setFieldRemark(RgConfigUtil.getFieldRemark(c.getMemberConfig()));
                detail.setRuleEnum(c.getMemberConfig().getRuleEnum());
                detail.setPattern(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getJsPattern(c.getMemberConfig().getRuleEnum()));
                detail.setMsg(c.getMemberConfig().getRuleEnum().equals(0) ? "" : MemberConfigCheckRuleEnum.getMsg(c.getMemberConfig().getRuleEnum()));
                detail.setFieldEnum(CollectionUtils.isEmpty(c.getMemberConfig().getLabels()) ? new ArrayList<>() : c.getMemberConfig().getLabels().stream().sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(label -> new ConfigDetailLabelResp(label.getId(), RgConfigUtil.getLabelValue(label))).collect(Collectors.toList()));

                DetailValueBO valueBO = getDetailValue(c);
                detail.setFieldValue(valueBO.getFieldValue());
                detail.setLastValue(valueBO.getLastValue());
                detail.setConfigs(MemberConfigFieldTypeEnum.LIST.getMessage().equals(c.getFieldType()) ? baseMemberDepositDetailService.initMemberConfig(c.getMemberConfig().getId()) : new ArrayList<>());
                detail.setRegisters(valueBO.getFieldValue());
                detail.setLastRegisters(valueBO.getLastValue());

                return detail;
            }).sorted(Comparator.comparingInt(RegisterDetailResp::getFieldOrder)).collect(Collectors.toList());

            detailGroup.setElements(elements);
            return detailGroup;
        }).sorted(Comparator.comparingInt(a -> a.getElements().get(0).getFieldOrder())).collect(Collectors.toList());
    }

    /**
     * 会员审核通过后（将注册资料的版本更新为正在使用的版本
     *
     * @param member 会员
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void updateMemberRegisterDetailToUsing(MemberDO member) {
        Set<MemberRegisterDetailDO> registerDetails = member.getRegisterDetails();
        if (CollectionUtils.isEmpty(registerDetails)) {
            return;
        }

        //删除正在使用的版本
        List<MemberRegisterDetailDO> deleteList = registerDetails.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.USING.getCode())).collect(Collectors.toList());
        memberRegisterDetailRepository.deleteAll(deleteList);

        List<MemberRegisterDetailDO> updateList = registerDetails.stream().filter(registerDetail -> registerDetail.getVersion().equals(MemberDetailVersionEnum.TO_BE_VALIDATE.getCode())).collect(Collectors.toList());
        updateList.forEach(registerDetail -> registerDetail.setVersion(MemberDetailVersionEnum.USING.getCode()));
        memberRegisterDetailRepository.saveAll(updateList);
    }


    /**
     * 查询会员标签注册资料
     *
     * @param memberId 会员Id
     * @return 查询结果
     */
    @Override
    public MemberRegisterTagResp getMemberRegisterTagDetail(Long memberId) {
        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        return getMemberRegisterTagDetail(memberDO);
    }

    /**
     * 查询会员标签注册资料
     *
     * @param member 会员
     * @return 查询结果
     */
    @Override
    public MemberRegisterTagResp getMemberRegisterTagDetail(MemberDO member) {
        List<MemberRegisterDetailDO> registerDetails = memberRegisterDetailRepository.findByMemberAndVersion(member, MemberDetailVersionEnum.USING.getCode());

        MemberRegisterTagResp queryVO = new MemberRegisterTagResp();
        queryVO.setMemberId(member.getId());
        queryVO.setName(member.getName());
        queryVO.setPhone(member.getPhone());
        queryVO.setLogo(member.getLogo());

        if (!CollectionUtils.isEmpty(registerDetails)) {
            for (MemberRegisterDetailDO detail : registerDetails) {
                if (detail.getTagEnum().equals(MemberConfigTagEnum.MEMBER_IDENTITY_CARD.getCode())) {
                    queryVO.setIdentityCard(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.MEMBER_IDENTITY_CARD_FRONT.getCode())) {
                    queryVO.setIdentityCardFront(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.MEMBER_IDENTITY_CARD_BACK.getCode())) {
                    queryVO.setIdentityCardBack(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.LEGAL_PERSON_NAME.getCode())) {
                    queryVO.setLegalPersonName(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.LEGAL_PERSON_PHONE.getCode())) {
                    queryVO.setLegalPersonPhone(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.LEGAL_PERSON_IDENTITY_CARD.getCode())) {
                    queryVO.setLegalPersonIdentityCardNo(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.LEGAL_PERSON_IDENTITY_CARD_FRONT.getCode())) {
                    queryVO.setLegalPersonIdentityCardFront(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.LEGAL_PERSON_IDENTITY_CARD_BACK.getCode())) {
                    queryVO.setLegalPersonIdentityCardBack(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.UNIFIED_CREDIT_CODE.getCode())) {
                    queryVO.setUnifiedCreditCode(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.REGISTERED_CAPITAL.getCode())) {
                    queryVO.setRegisteredCapital(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.ESTABLISHMENT_DATE.getCode())) {
                    queryVO.setEstablishmentDate(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.BUSINESS_LICENCE.getCode())) {
                    queryVO.setBusinessLicence(detail.getDetail());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.REGISTER_AREA.getCode())) {
                    queryVO.setRegisterArea(detail.getDetail());
                    queryVO.setProvinceName(detail.getProvinceName());
                    queryVO.setCityName(detail.getCityName());
                } else if (detail.getTagEnum().equals(MemberConfigTagEnum.REGISTER_ADDRESS.getCode())) {
                    queryVO.setRegisterAddress(detail.getDetail());
                }
            }
        }

        return queryVO;
    }


    /**
     * 修改平台会员注册资料
     *
     * @param platformRelation      平台会员关系
     * @param email                 邮箱
     * @param detail                前端传递的修改结果
     * @param updateEmail           是否检查并更新Email
     * @param forcePlatformValidate 是否强制平台审核，如果不强制，则根据注册资料配置判断
     */
    @Transactional
    @Override
    public void updatePlatformMemberRegisterDetail(MemberRelationDO platformRelation, String email, Map<String, Object> detail, boolean updateEmail, boolean forcePlatformValidate) {
        MemberDO memberDO = platformRelation.getSubMember();
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        UserDO userDO = userRepository.findFirstByMemberAndUserType(memberDO, UserTypeEnum.ADMIN.getCode());
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        Set<MemberRegisterConfigDO> memberConfigSet = memberDO.getMemberRoles().stream().flatMap(memberRoleDO -> memberRoleDO.getConfigs().stream()).collect(Collectors.toSet());
        List<MemberRegisterDetailDO> registerDetails = new ArrayList<>();
        String memberName = checkMemberRegisterDetail(detail, new ArrayList<>(memberConfigSet), registerDetails, memberDO.getName());

        //公司名称是否已被注册
        if (StringUtils.hasLength(memberName) && memberRepository.existsByNameAndIdNot(memberName, memberDO.getId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
        }

        //邮箱是否已被注册
        if (updateEmail && StringUtils.hasLength(email) && userRepository.existsByRelTypeAndEmailAndIdNot(MemberRelationTypeEnum.OTHER.getCode(), email, userDO.getId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
        }

        String registeredCapital = "", establishmentDate = "", businessLicence = "", registerArea = "", registerAddress = "";

        for (MemberRegisterDetailDO registerDetail : registerDetails) {
            registerDetail.setMember(memberDO);
            MemberConfigTagEnum tagEnum = MemberConfigTagEnum.parseCode(registerDetail.getMemberConfig().getTagEnum());
            if(tagEnum == null) {
                continue;
            }

            switch (tagEnum) {
                case REGISTERED_CAPITAL:
                    registeredCapital = registerDetail.getDetail();
                    break;
                case ESTABLISHMENT_DATE:
                    establishmentDate = registerDetail.getDetail();
                    break;
                case BUSINESS_LICENCE:
                    businessLicence = registerDetail.getDetail();
                    break;
                case REGISTER_AREA:
                    registerArea = registerDetail.getDetail();
                    break;
                case REGISTER_ADDRESS:
                    registerAddress = registerDetail.getDetail();
                    break;
                default:
                    break;
            }
        }

        // 空流程则版本为USING
        Integer version =
                MemberConstant.EMPTY_PLATFORM_VALIDATE_PROCESS_KEY.equals(platformRelation.getValidateTask().getProcessKey()) ?
                        MemberDetailVersionEnum.USING.getCode() : MemberDetailVersionEnum.TO_BE_VALIDATE.getCode();
        // 删除一份待审核的版本，新增一份待审核的版本
        List<MemberRegisterDetailDO> delMemberRegisters = memberRegisterDetailRepository.findByMemberAndVersion(memberDO, version);
        List<Long> listIds = delMemberRegisters.stream().filter(memberRegister -> MemberConfigFieldTypeEnum.LIST.getMessage().equals(memberRegister.getFieldType())).map(MemberRegisterDetailDO::getId).collect(Collectors.toList());
        List<Long> ids = delMemberRegisters.stream().map(MemberRegisterDetailDO::getId).collect(Collectors.toList());
        memberRegisterDetailRepository.deleteByParentIdIn(listIds);
        memberRegisterDetailRepository.deleteByIdIn(ids);
        registerDetails.forEach(registerDetail -> registerDetail.setVersion(version));
        memberRegisterDetailRepository.saveAll(registerDetails);
        // 兼容list字段，修改成循环保存
        for (MemberRegisterDetailDO memberRegisterDetail : registerDetails) {
            if (!CollectionUtils.isEmpty(memberRegisterDetail.getRegisters())) {
                List<MemberRegisterDetailDO> registers = memberRegisterDetail.getRegisters();
                registers.forEach(register -> register.setParentId(memberRegisterDetail.getId()));
                memberRegisterDetailRepository.saveAll(registers);
            }
        }


        if (updateEmail) {
            memberDO.setEmail(StringUtils.hasLength(email) ? email : "");
            userDO.setEmail(StringUtils.hasLength(email) ? email : "");
        }

        memberDO.setName(memberName);
        memberRepository.saveAndFlush(memberDO);

        userDO.setName(memberName);
        userRepository.saveAndFlush(userDO);

        //提交平台审核
        if (forcePlatformValidate && !platformRelation.getValidateTask().getProcessKey().equals(MemberConstant.EMPTY_PLATFORM_VALIDATE_PROCESS_KEY)) {
            //如果注册资料配置为“修改后提交平台审核”，且流程不是“无需审核”流程，自动提交审核
            WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(platformRelation);

            platformRelation.getValidateTask().setTaskId(taskResult.getTaskId());
            platformRelation.setInnerStatus(taskResult.getInnerStatus());
            platformRelation.setOuterStatus(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode());

            relationRepository.saveAndFlush(platformRelation);
        }

        //通知店铺模板服务，更改会员注册资料
        platformTemplateFeignService.notifyRegisterDetailChangedAsync(platformRelation.getSubMemberId(), platformRelation.getSubRoleId(), memberName, registeredCapital, establishmentDate, businessLicence, registerArea, registerAddress);

    }

    /**
     * 查询用户注册资料
     *
     * @param userId 用户Id
     * @return 查询结果
     */
    @Override
    public UserDetailResp getUserDetail(Long userId) {
        UserDO userDO = userRepository.findById(userId).orElse(null);
        if (userDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }
        UserDetailResp detailVO = new UserDetailResp();
        detailVO.setUserId(userDO.getId());
        detailVO.setUserName(userDO.getName());
        detailVO.setTelCode(userDO.getTelCode());
        detailVO.setPhone(userDO.getPhone());
        detailVO.setIdCardNo(userDO.getIdCardNo());
        return detailVO;
    }

    /**
     * 检查注册资料、入库资料
     *
     * @param configDO 会员注册资料
     * @param value    新增、修改后的值
     * @return 检查结果
     */
    @Override
    public DetailCheckBO checkDetail(MemberRegisterConfigDO configDO, String value) {
        List<MemberDetailLabelBO> labels = new ArrayList<>();
        Integer tagEnum;
        String memberName = "", detail, provinceCode = "", provinceName = "", cityCode = "", cityName = "", districtCode = "", districtName = "";

        //先检查枚举类型
        if ("select".equals(configDO.getFieldType()) || "radio".equals(configDO.getFieldType()) || "checkbox".equals(configDO.getFieldType()) || "area".equals(configDO.getFieldType())) {
            //枚举类型不能为空
            if (!StringUtils.hasLength(value)) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, MemberStringEnum.CHOOSE.getName() + RgConfigUtil.getFieldLocalName(configDO));
            }
        }

        //省、市、区下拉框
        if ("area".equals(configDO.getFieldType())) {
            RegisterAreaResp area = SerializeUtil.deserialize(value, RegisterAreaResp.class);
            if (area == null) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "省市区下拉框参数格式不正确");
            }

            if (!StringUtils.hasLength(area.getProvinceCode()) || !area.getProvinceCode().endsWith("0000")) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "省编码格式错误");
            }

            if (!StringUtils.hasLength(area.getCityCode()) || !area.getCityCode().endsWith("00")) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "市编码格式错误");
            }

            if (!StringUtils.hasLength(area.getDistrictCode())) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "区编码格式错误");
            }

            List<String> codeList = Stream.of(area.getProvinceCode(), area.getCityCode(), area.getDistrictCode()).collect(Collectors.toList());
            List<String> prefixList = codeList.stream().map(code -> code.substring(0, 2)).collect(Collectors.toList());
            if (prefixList.stream().distinct().count() != 1) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "省、市、区编码不匹配");
            }

            List<AreaCodeNameResp> areaCodeNameRespList = AreaUtil.findByCodeIn(codeList);
            if (CollectionUtils.isEmpty(areaCodeNameRespList) || areaCodeNameRespList.size() != 3) {
                throw new BusinessException(ResponseCodeEnum.MC_MS_COUNTRY_AREA_DOES_NOT_EXIST);
            }

            provinceCode = area.getProvinceCode();
            cityCode = area.getCityCode();
            districtCode = area.getDistrictCode();
            for (AreaCodeNameResp areaCodeNameResp : areaCodeNameRespList) {
                if (areaCodeNameResp.getCode().equals(provinceCode)) {
                    provinceName = areaCodeNameResp.getName();
                } else if (areaCodeNameResp.getCode().equals(cityCode)) {
                    cityName = areaCodeNameResp.getName();
                } else {
                    districtName = areaCodeNameResp.getName();
                }
            }

            detail = provinceName.concat(cityName).concat(districtName);
        } else if ("select".equals(configDO.getFieldType()) || "radio".equals(configDO.getFieldType())) {
            if(StringUtils.hasLength(value) && value.startsWith("[")) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, RgConfigUtil.getFieldLocalName(configDO) + "为单项选择，参数为整数类型");
            }

            Long labelId = SecurityStringUtil.convertStringToLong(value);
            detail = "";
            if (!NumberUtil.isNullOrZero(labelId)) {
                MemberRegisterConfigLabelDO labelDO = configDO.getLabels().stream().filter(label -> label.getId().equals(labelId)).findFirst().orElse(null);
                if (labelDO == null) {
                    throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, RgConfigUtil.getFieldLocalName(configDO).concat("标签不存在"));
                }

                labels = Stream.of(new MemberDetailLabelBO(labelDO.getId(), RgConfigUtil.getLabelValue(labelDO))).collect(Collectors.toList());
                detail = RgConfigUtil.getLabelValue(labelDO);
            }
        } else if ("checkbox".equals(configDO.getFieldType())) {
            if(StringUtils.hasLength(value) && !value.startsWith("[")) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, RgConfigUtil.getFieldLocalName(configDO) + "为多项选择，参数为整数数组");
            }

            detail = "";
            //其他枚举类型
            List<Long> labelIds = SecurityStringUtil.convertStringToLongList(value);
            if (!CollectionUtils.isEmpty(labelIds)) {
                if (labelIds.stream().anyMatch(labelId -> configDO.getLabels().stream().noneMatch(label -> label.getId().equals(labelId)))) {
                    throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, "标签不存在");
                }

                labels = configDO.getLabels().stream().filter(label -> labelIds.contains(label.getId())).map(label -> {
                    MemberDetailLabelBO detailLabel = new MemberDetailLabelBO();
                    detailLabel.setLabelId(label.getId());
                    detailLabel.setLabelValue(RgConfigUtil.getLabelValue(label));
                    return detailLabel;
                }).collect(Collectors.toList());

                detail = configDO.getLabels().stream().filter(label -> labelIds.contains(label.getId())).sorted(Comparator.comparingInt(MemberRegisterConfigLabelDO::getLabelOrder)).map(RgConfigUtil::getLabelValue).collect(Collectors.joining(","));
            }
        } else if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(configDO.getFieldType())) {
            detail = value;
        } else if (MemberConfigFieldTypeEnum.NUMBER.getMessage().equals(configDO.getFieldType())) {
            try {
                if(StringUtils.hasLength(value)){
                    Long.parseLong(value);
                }
            } catch (Exception e){
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, RgConfigUtil.getFieldLocalName(configDO) + MemberStringEnum.MUST_BE_OF_NUMERIC.getName());
            }
            detail = StringUtils.hasLength(value) ? value : "";
        } else {
            Object obj = ThreadLocalUtils.get();
            //检查是否为空
            if (Objects.isNull(obj) && configDO.getFieldEmpty().equals(EnableDisableStatusEnum.DISABLE.getCode()) && !StringUtils.hasLength(value)) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, MemberStringEnum.FILL_IN.getName() + RgConfigUtil.getFieldLocalName(configDO));
            }

            //检查字段长度(文件类型不检查)
            if (Objects.isNull(obj) && value != null && !MemberConfigFieldTypeEnum.FILE.getMessage().equals(configDO.getFieldType()) && value.length() > configDO.getFieldLength()) {
                throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, RgConfigUtil.getFieldLocalName(configDO) + MemberStringEnum.EXCEED_LENGTH_LIMIT.getName());
            }

            //文件类型
            if(configDO.getFieldType().toLowerCase().equals(MemberConfigFieldTypeEnum.FILE.getMessage())) {
                if(Objects.isNull(obj) && configDO.getFieldEmpty().equals(EnableDisableStatusEnum.DISABLE.getCode()) && !StringUtils.hasLength(value)) {
                    throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, MemberStringEnum.UPLOAD.getName() + RgConfigUtil.getFieldLocalName(configDO));
                }

                if(StringUtils.hasLength(value) && !value.toLowerCase().startsWith("http://") && !value.toLowerCase().startsWith("https://")) {
                    throw new BusinessException(ResponseCodeEnum.REQUEST_PARAM_CHECK_FAILED, RgConfigUtil.getFieldLocalName(configDO) + " " + MemberStringEnum.URL_FORMAT_MISMATCH.getName());
                }
            }

            detail = StringUtils.hasLength(value) ? value : "";
        }

        tagEnum = configDO.getTagEnum() == null ? 0 : configDO.getTagEnum();
        if (tagEnum.equals(MemberConfigTagEnum.MEMBER_NAME.getCode())) {
            memberName = detail;
        }

        return new DetailCheckBO(tagEnum, memberName, detail, provinceCode, provinceName, cityCode, cityName, districtCode, districtName, labels);
    }

    /**
     * 检查注册资料、入库资料的字段是否齐全
     *
     * @param memberConfigList 注册资料列表
     * @param detailMap    前端传递的注册、入库资料
     * @return 检查结果
     */
    @Override
    public void checkDetailFields(List<MemberRegisterConfigDO> memberConfigList, Map<String, Object> detailMap) {
        if(CollectionUtils.isEmpty(memberConfigList) || CollectionUtils.isEmpty(memberConfigList.stream()
                .filter(config -> !MemberConfigFieldTypeEnum.LIST.getMessage().equals(config.getFieldType())).collect(Collectors.toList()))) {
            return ;
        }

//        if(CollectionUtils.isEmpty(detailMap)) {
//            return Wrapper.fail(ResponseCode.MC_MS_MEMBER_DEPOSITORY_DETAIL_IS_MISSING);
//        }

        Object obj = ThreadLocalUtils.get();
        MemberRegisterConfigDO memberConfig = memberConfigList.stream()
                .filter(config -> !ObjectUtils.isEmpty(config.getFieldEmpty()) && config.getFieldEmpty() == 0)
                .filter(config -> !MemberConfigFieldTypeEnum.LIST.getMessage().equals(config.getFieldType()))
                .filter(config -> detailMap.keySet().stream().noneMatch(key -> key.equals(config.getFieldName()))).findFirst().orElse(null);
        if(Objects.isNull(obj) && memberConfig != null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DEPOSITORY_DETAIL_IS_MISSING, MemberStringEnum.FILL_IN.getName() + RgConfigUtil.getFieldLocalName(memberConfig));
        }

    }

    /**
     * 查询注册资料时，根据注册资料的FieldType，获取前端显示的值
     *
     * @param detailDO 注册资料
     * @return 字段值
     */
    private DetailValueBO getDetailValue(MemberRegisterDetailDO detailDO) {
        DetailValueBO detail = new DetailValueBO();
        //省、市、区下拉框
        if (MemberConfigFieldTypeEnum.AREA.getMessage().equals(detailDO.getFieldType())) {
            detail.setFieldValue(new RegisterAreaResp(detailDO.getProvinceCode(), detailDO.getCityCode(), detailDO.getDistrictCode()));
            detail.setLastValue(new RegisterAreaResp());
        } else if (MemberConfigFieldTypeEnum.SELECT.getMessage().equals(detailDO.getFieldType()) || MemberConfigFieldTypeEnum.RADIO.getMessage().equals(detailDO.getFieldType())) {
            MemberDetailLabelBO labelBO = detailDO.getLabels().stream().findFirst().orElse(null);
            detail.setFieldValue(labelBO == null ? null : labelBO.getLabelId());
            detail.setLastValue(null);
        } else if (MemberConfigFieldTypeEnum.CHECKBOX.getMessage().equals(detailDO.getFieldType())) {
            detail.setFieldValue(detailDO.getLabels().stream().map(MemberDetailLabelBO::getLabelId).collect(Collectors.toList()));
            detail.setLastValue(new ArrayList<>());
        } else if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(detailDO.getFieldType())) {
            detail.setFieldValue(NumberUtil.isNullOrZero(detailDO.getId()) ? new ArrayList<>() : switchMemberRegister(detailDO.getId()));
            detail.setLastValue(new ArrayList<>());
        } else {
            detail.setFieldValue(detailDO.getDetail());
            detail.setLastValue("");
        }

        return detail;
    }

    /**
     * 查询入库资料时，根据入库资料的FieldType，获取前端显示的值
     *
     * @param detailDO 入库资料
     * @return 字段值
     */
    private DetailValueBO getDetailValue(MemberRegisterConfigDO detailDO) {
        DetailValueBO detail = new DetailValueBO();
        //省、市、区下拉框
        List<Object> list = new ArrayList<>();
        RegisterAreaResp registerAreaResp = new RegisterAreaResp();
        if (MemberConfigFieldTypeEnum.AREA.getMessage().equals(detailDO.getFieldType())) {
            detail.setFieldValue(registerAreaResp);
            detail.setLastValue(registerAreaResp);
        } else if (MemberConfigFieldTypeEnum.SELECT.getMessage().equals(detailDO.getFieldType()) || MemberConfigFieldTypeEnum.RADIO.getMessage().equals(detailDO.getFieldType())) {
            detail.setFieldValue(null);
            detail.setLastValue(null);
        } else if (MemberConfigFieldTypeEnum.CHECKBOX.getMessage().equals(detailDO.getFieldType())) {
            detail.setFieldValue(list);
            detail.setLastValue(list);
        } else if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(detailDO.getFieldType())) {
            detail.setFieldValue(list);
            detail.setLastValue(list);
        } else {
            detail.setFieldValue("");
            detail.setLastValue("");
        }

        return detail;
    }

    /**
     * 查询注册资料时，根据注册资料的FieldType，获取前端显示的值
     *
     * @param detailDO 注册资料
     * @return 字段值
     */
    private Object getSingleDetailValue(MemberRegisterDetailDO detailDO) {
        //省、市、区下拉框
        if ("area".equals(detailDO.getFieldType())) {
            return new RegisterAreaResp(detailDO.getProvinceCode(), detailDO.getCityCode(), detailDO.getDistrictCode());
        } else if ("select".equals(detailDO.getFieldType()) || "radio".equals(detailDO.getFieldType())) {
            MemberDetailLabelBO labelBO = detailDO.getLabels().stream().findFirst().orElse(null);
            if(labelBO != null) {
                return labelBO.getLabelId();
            } else {
                return null;
            }
        } else if (MemberConfigFieldTypeEnum.LIST.getMessage().equals(detailDO.getFieldType())) {
            return NumberUtil.isNullOrZero(detailDO.getId()) ? new ArrayList<>() : switchMemberRegister(detailDO.getId());
        } else if ("checkbox".equals(detailDO.getFieldType())) {
            return detailDO.getLabels().stream().map(MemberDetailLabelBO::getLabelId).collect(Collectors.toList());
        } else {
            return detailDO.getDetail();
        }
    }
}
