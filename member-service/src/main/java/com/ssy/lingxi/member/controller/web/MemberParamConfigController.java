package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.service.web.IMemberParamConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 系统能力 - 会员参数配置
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/13
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/param/config")
public class MemberParamConfigController extends BaseController {

    @Resource
    private IMemberParamConfigService memberParamConfigService;

    /**
     * 查看是否支持人脸识别
     *
     * @return 查询结果
     */
    @GetMapping("/face/recognition/flag/get")
    public WrapperResp<Boolean> getFaceRecognitionFlag() {
        return WrapperUtil.success(memberParamConfigService.getFaceRecognitionFlag(getSysUser()));
    }

    /**
     * 添加修改否支持人脸识别
     *
     * @param req 接口参数
     * @return 修改结果
     */
    @PostMapping("/face/recognition/flag/saveOrUpdate")
    public WrapperResp<Void> saveOrUpdateFaceRecognitionFlag(@RequestBody @Valid FaceRecognitionFlagReq req) {
        memberParamConfigService.saveOrUpdateFaceRecognitionFlag(getSysUser(), req);
        return WrapperUtil.success();
    }

}

