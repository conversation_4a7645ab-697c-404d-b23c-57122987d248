package com.ssy.lingxi.member.model.req.validate;

import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.member.handler.annotation.DateStringFormatAnnotation;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 会员能力 - 会员导入 - 会员分页查询接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-07
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberAbilityImportMemberQueryDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = 6071800736966487457L;
    /**
     * 会员名称
     */
    private String name;

    /**
     * 申请开始时间， 格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation
    private String startDate;

    /**
     * 申请结束时间，格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation
    private String endDate;

    /**
     * 外部状态
     */
    private Integer outerStatus;

    /**
     * 会员状态
     */
    private Integer status;


    /**
     * 会员类型， 0或Null-所有 1-企业会员 2-企业个人会员 3-渠道企业会员 4-渠道个人会员
     */
    private Long memberType;

    /**
     * 角色， 0或Null-所有， 其他枚举从接口下拉菜单字段中获取
     */
    private Long roleId;

    /**
     * 等级， 0或Null-所有，其他枚举从接口下拉菜单字段中获取
     */
    private Integer level;
}
