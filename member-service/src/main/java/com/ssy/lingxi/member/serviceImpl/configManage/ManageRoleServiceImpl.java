package com.ssy.lingxi.member.serviceImpl.configManage;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.LanguageEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.idGenerate.IIdGenerate;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.basic.UserRoleDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberProcessDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterConfigDO;
import com.ssy.lingxi.member.entity.do_.menuAuth.ButtonDO;
import com.ssy.lingxi.member.entity.do_.menuAuth.MenuDO;
import com.ssy.lingxi.member.enums.MemberRegisterConfigNameEnum;
import com.ssy.lingxi.member.model.req.configManage.*;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameAndMemberTypeResp;
import com.ssy.lingxi.member.model.resp.configManage.*;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseAuthService;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.configManage.IManageRoleService;
import com.ssy.lingxi.member.util.RgConfigUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.roaringbitmap.RoaringBitmap;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.ssy.lingxi.member.serviceImpl.base.BaseAuthServiceImpl.getAuthByUserType;

/**
 * 业务平台 - 角色管理接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-13
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ManageRoleServiceImpl implements IManageRoleService {
    private final MemberRoleRepository memberRoleRepository;
    private final MemberRegisterConfigRepository memberRegisterConfigRepository;
    private final MenuRepository menuRepository;
    private final MenuButtonRepository buttonRepository;
    private final MemberProcessRepository memberProcessRepository;
    private final IBaseAuthService baseAuthService;
    private final ObjectMapper objectMapper;
    private final IBaseMemberCacheService memberCacheService;
    private final IIdGenerate idGenerate;
    private final MemberRepository memberRepository;
    private final UserRepository userRepository;
    private final UserRoleRepository userRoleRepository;


    @Override
    public List<MemberConfigEnumResp> getMemberRoleTypeList() {
        memberCacheService.needLoginFromManagePlatform();

        return Arrays.stream(RoleTypeEnum.values())
                .map(mt -> new MemberConfigEnumResp(mt.getCode(), mt.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<MemberConfigEnumResp> getMemberTypeList() {
        return Arrays.stream(MemberTypeEnum.values())
                .map(mt -> new MemberConfigEnumResp(mt.getCode(), mt.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<MemberConfigEnumResp> getMemberRoleTagList() {
        memberCacheService.needLoginFromManagePlatform();

        return Arrays.stream(RoleTagEnum.values())
                .filter(roleTagEnum -> roleTagEnum.getCode().equals(RoleTagEnum.CUSTOMER.getCode())
                        || roleTagEnum.getCode().equals(RoleTagEnum.SUPPLIER.getCode()))// 系统正常只使用两种角色标签
                .map(mt -> new MemberConfigEnumResp(mt.getCode(), mt.getName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<RoleIdAndNameAndMemberTypeResp> getMemberRoleListByMemberType(Integer memberType) {
        if (Objects.nonNull(memberType)){
            return memberRoleRepository.findByMemberTypeAndStatus(memberType, EnableDisableStatusEnum.ENABLE.getCode())
                    .stream()
                    .filter(roleDO -> !MemberRelationTypeEnum.PLATFORM.getCode().equals(roleDO.getRelType()))
                    .map(roleDO -> new RoleIdAndNameAndMemberTypeResp(roleDO.getId(), roleDO.getRoleName(), roleDO.getMemberType()))
                    .collect(Collectors.toList());
        }else {
            return memberRoleRepository.findByStatusAndIsShow(EnableDisableStatusEnum.ENABLE.getCode(), Boolean.TRUE)
                    .stream()
                    .filter(roleDO -> !MemberRelationTypeEnum.PLATFORM.getCode().equals(roleDO.getRelType()))
                    .map(roleDO -> new RoleIdAndNameAndMemberTypeResp(roleDO.getId(), roleDO.getRoleName(), roleDO.getMemberType()))
                    .collect(Collectors.toList());
        }
    }

    @Override
    public PageDataResp<MemberRolePageResp> getMemberRolePage(MemberRolePageDataReq memberRolePageReq) {
        memberCacheService.needLoginFromManagePlatform();

        Specification<MemberRoleDO> spec = (root, query, cb) -> {
            List<Predicate> list = new ArrayList<>();
            if (StringUtils.hasText(memberRolePageReq.getName())) {
                list.add(cb.like(root.get("roleName").as(String.class), "%" + memberRolePageReq.getName() + "%"));
            }
            list.add(cb.notEqual(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));// 不返回平台会员
            return cb.and(list.toArray(new Predicate[0]));
        };

        Pageable page = PageRequest.of(memberRolePageReq.getCurrent() - 1, memberRolePageReq.getPageSize(), Sort.by("id").descending());

        Page<MemberRoleDO> pageData = memberRoleRepository.findAll(spec, page);

        return new PageDataResp<>(pageData.getTotalElements(),
                pageData.getContent()
                        .stream()
                        .map(memberRoleDO -> {
                            MemberRolePageResp memberRolePageResp = new MemberRolePageResp();
                            memberRolePageResp.setId(memberRoleDO.getId());
                            memberRolePageResp.setMemberRoleName(memberRoleDO.getRoleName());
                            memberRolePageResp.setRoleType(memberRoleDO.getRoleType());
                            memberRolePageResp.setRoleTypeName(RoleTypeEnum.getName(memberRoleDO.getRoleType()));
                            memberRolePageResp.setMemberType(memberRoleDO.getMemberType());
                            memberRolePageResp.setMemberTypeName(MemberTypeEnum.getName(memberRoleDO.getMemberType()));
                            memberRolePageResp.setRoleTag(memberRoleDO.getRoleTag());
                            memberRolePageResp.setRoleTagName(RoleTagEnum.getName(memberRoleDO.getRoleTag()));
                            memberRolePageResp.setStatus(memberRoleDO.getStatus());
                            return memberRolePageResp;
                        })
                        .collect(Collectors.toList()));
    }

    @Override
    public MemberRoleResp getMemberRoleById(CommonIdReq commonIdReq) {
        memberCacheService.needLoginFromManagePlatform();

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(commonIdReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST));

        MemberRoleResp memberRoleResp = new MemberRoleResp();
        memberRoleResp.setId(memberRoleDO.getId());
        memberRoleResp.setRoleName(memberRoleDO.getRoleName());
        memberRoleResp.setRoleType(memberRoleDO.getRoleType());
        memberRoleResp.setRoleTypeName(RoleTypeEnum.getName(memberRoleDO.getRoleType()));
        memberRoleResp.setMemberType(memberRoleDO.getMemberType());
        memberRoleResp.setMemberTypeName(MemberTypeEnum.getName(memberRoleDO.getMemberType()));
        memberRoleResp.setRoleTag(memberRoleDO.getRoleTag());
        memberRoleResp.setRoleTagName(RoleTagEnum.getName(memberRoleDO.getRoleTag()));
        memberRoleResp.setStatus(memberRoleDO.getStatus());

        return memberRoleResp;
    }

    @Override
    public List<MemberRoleConfigResp> getRegisterConfigByMemberRoleId(CommonIdReq commonIdReq) {
        memberCacheService.needLoginFromManagePlatform();

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(commonIdReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST));
        return Optional.ofNullable(memberRoleDO.getConfigs())
                .map(configList -> configList.stream()
                        .map(memberConfigDO -> {
                            MemberRoleConfigResp memberRoleConfigResp = new MemberRoleConfigResp();
                            memberRoleConfigResp.setId(memberConfigDO.getId());
                            memberRoleConfigResp.setFieldName(memberConfigDO.getFieldName());
                            memberRoleConfigResp.setFieldLocalName(RgConfigUtil.getConfigFieldResp(memberConfigDO, MemberRegisterConfigNameEnum.FIELD_LOCAL_NAME));
                            memberRoleConfigResp.setFieldType(memberConfigDO.getFieldType());
                            memberRoleConfigResp.setFieldLength(memberConfigDO.getFieldLength());
                            memberRoleConfigResp.setFieldEmpty(memberConfigDO.getFieldEmpty());
                            memberRoleConfigResp.setFieldGroupName(RgConfigUtil.getConfigFieldResp(memberConfigDO, MemberRegisterConfigNameEnum.FIELD_GROUP_NAME));
                            memberRoleConfigResp.setFieldOrder(memberConfigDO.getFieldOrder());
                            memberRoleConfigResp.setValidate(memberConfigDO.getValidate());
                            return memberRoleConfigResp;
                        })
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    @Override
    public Long addRole(ManageMemberRoleReq manageMemberRoleReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 校验入参
        BusinessAssertUtil.isTrue(RoleTypeEnum.contains(manageMemberRoleReq.getRoleTypeEnum()), ResponseCodeEnum.MC_MS_ROLE_TYPE_DOES_NOT_EXIST);
        BusinessAssertUtil.isTrue(MemberTypeEnum.contains(manageMemberRoleReq.getMemberTypeEnum()), ResponseCodeEnum.MC_MS_MEMBER_TYPE_DOES_NOT_EXIST);
        BusinessAssertUtil.isTrue(RoleTagEnum.contains(manageMemberRoleReq.getRoleTypeEnum()), ResponseCodeEnum.MC_MS_ROLE_TAG_DOES_NOT_EXIST);
        BusinessAssertUtil.isFalse(memberRoleRepository.existsByRoleName(manageMemberRoleReq.getRoleName()), ResponseCodeEnum.MC_MS_ROLE_EXISTS);

        // 创建角色
        MemberRoleDO memberRoleDO = new MemberRoleDO();
        memberRoleDO.setRoleName(manageMemberRoleReq.getRoleName());
        memberRoleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        memberRoleDO.setRoleType(manageMemberRoleReq.getRoleTypeEnum());
        memberRoleDO.setRoleTag(manageMemberRoleReq.getRoleTagEnum());
        memberRoleDO.setMemberType(manageMemberRoleReq.getMemberTypeEnum());
        memberRoleDO.setRelType(MemberRelationTypeEnum.OTHER.getCode());
        //初始权限为空
        memberRoleDO.setMenuAuth(BitMapUtil.emptyByteArray());
        memberRoleDO.setButtonAuth(BitMapUtil.emptyByteArray());
        memberRoleDO.setApiAuth(BitMapUtil.emptyByteArray());

        memberRoleRepository.saveAndFlush(memberRoleDO);

        return memberRoleDO.getId();
    }

    /**
     * 更新角色
     *
     * @param manageMemberRoleReq 接口参数
     * @return 更新结果
     */
    @Override
    public void updateRole(ManageMemberRoleReq manageMemberRoleReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 校验入参
        BusinessAssertUtil.isTrue(RoleTypeEnum.contains(manageMemberRoleReq.getRoleTypeEnum()), ResponseCodeEnum.MC_MS_ROLE_TYPE_DOES_NOT_EXIST);
        BusinessAssertUtil.isTrue(MemberTypeEnum.contains(manageMemberRoleReq.getMemberTypeEnum()), ResponseCodeEnum.MC_MS_MEMBER_TYPE_DOES_NOT_EXIST);
        BusinessAssertUtil.isTrue(RoleTagEnum.contains(manageMemberRoleReq.getRoleTypeEnum()), ResponseCodeEnum.MC_MS_ROLE_TAG_DOES_NOT_EXIST);

        // 校验角色是否存在
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(manageMemberRoleReq.getId()).orElse(null);
        BusinessAssertUtil.notNull(memberRoleDO, ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);

        // 使用中的角色不允许修改
        BusinessAssertUtil.isEmpty(memberRoleDO.getMembers(), ResponseCodeEnum.MC_MS_ROLE_CAN_NOT_UPDATE_OR_DELETE);

        // 如果已经存在同名的角色，返回角色已经存在的错误
        BusinessAssertUtil.isFalse(memberRoleRepository.existsByRoleNameAndIdNot(manageMemberRoleReq.getRoleName(), manageMemberRoleReq.getId()), ResponseCodeEnum.MC_MS_ROLE_EXISTS);

        // 不修改原来的状态
        memberRoleDO.setRoleName(manageMemberRoleReq.getRoleName());
        memberRoleDO.setRoleTag(manageMemberRoleReq.getRoleTagEnum());
        memberRoleDO.setRoleType(manageMemberRoleReq.getRoleTypeEnum());
        memberRoleDO.setMemberType(manageMemberRoleReq.getMemberTypeEnum());

        memberRoleRepository.saveAndFlush(memberRoleDO);


    }

    /**
     * 删除角色
     *
     * @param manageMemberRoleReq 接口参数
     * @return 删除结果
     */
    @Override
    public void deleteRole(ManageMemberRoleReq manageMemberRoleReq) {
        memberCacheService.needLoginFromManagePlatform();

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(manageMemberRoleReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));

        if (!CollectionUtils.isEmpty(memberRoleDO.getMembers())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_CAN_NOT_UPDATE_OR_DELETE);
        }

        memberRoleRepository.delete(memberRoleDO);

    }

    /**
     * 更改角色状态
     *
     * @param manageMemberRoleReq 接口参数
     * @return 操作结果
     */
    @Override
    public void updateRoleStatus(ManageMemberRoleReq manageMemberRoleReq) {
        memberCacheService.needLoginFromManagePlatform();

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(manageMemberRoleReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));

        if (!CollectionUtils.isEmpty(memberRoleDO.getMembers())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_CAN_NOT_UPDATE_OR_DELETE);
        }

        memberRoleDO.setStatus(manageMemberRoleReq.getStatus());

        memberRoleRepository.saveAndFlush(memberRoleDO);


    }

    /**
     * 设置角色关联的配置资料
     *
     * @param roleConfigReq 接口参数
     * @return 操作结果
     */
    @Override
    public void setRoleConfig(ManageMemberRoleConfigReq roleConfigReq) {
        memberCacheService.needLoginFromManagePlatform();

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(roleConfigReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));

        if (CollectionUtils.isEmpty(roleConfigReq.getConfigIds())) {
            memberRoleDO.getConfigs().clear();
        } else {
            List<MemberRegisterConfigDO> configDOList = memberRegisterConfigRepository.findAllById(roleConfigReq.getConfigIds());
            if (configDOList.size() != roleConfigReq.getConfigIds().size()) {

                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_CONFIG_DOES_NOT_EXIST);
            }

            memberRoleDO.setConfigs(new HashSet<>(configDOList));
        }

        memberRoleRepository.saveAndFlush(memberRoleDO);


    }

    @Override
    public void setMemberRoleRegisterProcess(ProcessRuleReq processRuleReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查询会员角色
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(processRuleReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));
        //添加展示角色状态
        memberRoleDO.setIsShow(processRuleReq.getIsShow());

        // 查询平台注册流程
        MemberProcessDO memberRegisterProcessDO = memberProcessRepository.findFirstByProcessKey(processRuleReq.getProcessKey());
        BusinessAssertUtil.notNull(memberRegisterProcessDO, ResponseCodeEnum.MC_MS_BASE_PLATFORM_PROCESS_DOES_NOT_EXIST);

        // 更新会员角色绑定的平台注册流程
        memberRoleDO.setRegisterProcess(memberRegisterProcessDO);
        memberRoleRepository.saveAndFlush(memberRoleDO);

    }

    @Override
    public MemberRoleRegisterProcessResp findMemberRoleRegisterProcess(CommonIdReq commonIdReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查询角色
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(commonIdReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST));

        // 查询角色对应的流程
        MemberProcessDO registerProcessDO = memberRoleDO.getRegisterProcess();

        // 返回数据
        MemberRoleRegisterProcessResp processResp = new MemberRoleRegisterProcessResp();
        processResp.setProcessId(Optional.ofNullable(registerProcessDO).map(MemberProcessDO::getId).orElse(0L));
        processResp.setProcessKey(Optional.ofNullable(registerProcessDO).map(MemberProcessDO::getProcessKey).orElse(""));
        processResp.setProcesses(memberProcessRepository.findAllByProcessType(MemberProcessTypeEnum.PLATFORM_VALIDATION.getCode())
                .stream()
                .map(processDO -> {
                    MemberRegisterProcessDetailResp detailResp = new MemberRegisterProcessDetailResp();
                    detailResp.setProcessId(processDO.getId());
                    detailResp.setProcessKey(processDO.getProcessKey());
                    detailResp.setProcessName(MemberProcessEnum.getMessage(processDO.getProcessCode()));
                    detailResp.setProcessType(processDO.getProcessType());
                    detailResp.setProcessTypeName(MemberProcessTypeEnum.getMsg(processDO.getProcessType()));
                    detailResp.setDescription(MemberProcessEnum.getDescription(processDO.getProcessCode()));
                    return detailResp;
                })
                .sorted(Comparator.comparing(MemberRegisterProcessDetailResp::getProcessId))
                .collect(Collectors.toList()));
        processResp.setIsShow(memberRoleDO.getIsShow());
        return processResp;
    }

    // *******************************************    角色菜单相关接口    *******************************************

    @Override
    public AuthTreeResp getMemberRoleAuth(MemberRoleAuthTreeReq authMenuReq) {
        memberCacheService.needLoginFromManagePlatform();

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(authMenuReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));

        // 会员角色权限可视菜单按钮为对应端的菜单按钮
        String currentLanguage = LanguageEnum.getCurrentLanguage();
        return baseAuthService.getAuthTree(
                        memberRoleDO.getMenuAuth(),
                        memberRoleDO.getButtonAuth(),
                        CompletableFuture.supplyAsync(() -> baseAuthService.getMenuSetBySource(authMenuReq.getSource(), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor),
                        CompletableFuture.supplyAsync(() -> baseAuthService.getButtonSetBySource(authMenuReq.getSource(), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor));
    }

    @Override
    public void setMemberRoleMenuButtonAuth(MemberRoleMenuButtonAuthReq menuButtonAuthReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查找会员角色
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(menuButtonAuthReq.getRoleId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST));

        // 更新角色权限
        memberRoleDO.setMenuAuth(BitMapUtil.toByteArray(menuButtonAuthReq.getMenuIdList()));
        memberRoleDO.setButtonAuth(BitMapUtil.toByteArray(menuButtonAuthReq.getButtonIdList()));
        memberRoleDO.setApiAuth(BitMapUtil.emptyByteArray());// 接口权限暂时没做，所以都先存空
        memberRoleRepository.save(memberRoleDO);

        //更新平台会员关系、会员、会员角色、用户的权限（在角色权限更新commit以后触发）
        baseAuthService.rebuildMemberAuthByUpdateRoleAuth(memberRoleDO);
    }

    @Override
    public void setAllMenuAndButtons(CommonIdReq commonIdReq) {
        memberCacheService.needLoginFromManagePlatform();

        // 查询会员角色
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(commonIdReq.getId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ROLE_DOES_NOT_EXIST));

        // 获取该会员角色对应的所有菜单id
        Set<Long> menuIdSet = menuRepository.findAllById(BitMapUtil.toIdSet(memberRoleDO.getMenuAuth()))
                .stream()
                .map(MenuDO::getId)
                .collect(Collectors.toSet());

        // 获取菜单对应的所有按钮
        Set<Long> buttonIdSet = buttonRepository.findAllById(menuIdSet)
                .stream()
                .map(ButtonDO::getId)
                .collect(Collectors.toSet());

        // 存储最新的菜单和按钮权限
        memberRoleDO.setMenuAuth(BitMapUtil.toByteArray(menuIdSet));
        memberRoleDO.setButtonAuth(BitMapUtil.toByteArray(buttonIdSet));
        memberRoleRepository.save(memberRoleDO);


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initPlatformAdminAuth() {
//        memberCacheService.needLoginFromManagePlatform();

        // 获取对应来源的菜单按钮权限
        Set<Long> menuIdSet = baseAuthService.getMenuIdSetBySource(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode());
        Set<Long> buttonIdSet = baseAuthService.getButtonIdSetByMenuSource(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode());

        if (CollectionUtils.isEmpty(menuIdSet)) {
            log.info("平台菜单按钮权限为空，无须初始化");
            return;
        }

        // 转换权限
        RoaringBitmap menuAuthBitMap = BitMapUtil.toBitMap(menuIdSet);
        RoaringBitmap buttonAuthBitMap = BitMapUtil.toBitMap(buttonIdSet);
        RoaringBitmap apiAuthBitMap = new RoaringBitmap();

        byte[] menuAuth = BitMapUtil.toByteArray(menuAuthBitMap);
        byte[] buttonAuth = BitMapUtil.toByteArray(buttonAuthBitMap);
        byte[] apiAuth = BitMapUtil.toByteArray(apiAuthBitMap);

        // 设置平台会员
        MemberDO memberDO = memberRepository.findPlatformMember();

        // 设置平台会员角色
        MemberRoleDO adminRoleDO = memberRoleRepository.findFirstByRelType(MemberRelationTypeEnum.PLATFORM.getCode());
        adminRoleDO.setMenuAuth(menuAuth);
        adminRoleDO.setButtonAuth(buttonAuth);
        adminRoleDO.setApiAuth(apiAuth);
        memberRoleRepository.save(adminRoleDO);

        // 清空平台会员的所有非超管用户的权限，重制超管权限
        List<UserDO> userDOList = userRepository.findAllByMemberId(memberDO.getId());
        userDOList.forEach(userDO -> {
            userDO.setMenuAuth(getAuthByUserType(menuAuthBitMap, BitMapUtil.emptyByteArray(), userDO.getUserType(), BitMapUtil.ReturnType.ByteArray));
            userDO.setButtonAuth(getAuthByUserType(buttonAuthBitMap, BitMapUtil.emptyByteArray(), userDO.getUserType(), BitMapUtil.ReturnType.ByteArray));
            userDO.setApiAuth(getAuthByUserType(apiAuthBitMap, BitMapUtil.emptyByteArray(), userDO.getUserType(), BitMapUtil.ReturnType.ByteArray));
            userRepository.save(userDO);
        });

        // 清空平台会员的所有用户角色权限，重制超管角色权限
        List<UserRoleDO> userRoleDOList = userRoleRepository.findAllByMemberId(memberDO.getId());
        userRoleDOList.forEach(userRoleDO -> {
            userRoleDO.setMenuAuth(getAuthByUserType(menuAuthBitMap, BitMapUtil.emptyByteArray(), userRoleDO.getUserType(), BitMapUtil.ReturnType.ByteArray));
            userRoleDO.setButtonAuth(getAuthByUserType(buttonAuthBitMap, BitMapUtil.emptyByteArray(), userRoleDO.getUserType(), BitMapUtil.ReturnType.ByteArray));
            userRoleDO.setApiAuth(getAuthByUserType(apiAuthBitMap, BitMapUtil.emptyByteArray(), userRoleDO.getUserType(), BitMapUtil.ReturnType.ByteArray));
            userRoleRepository.save(userRoleDO);
        });


    }
}
