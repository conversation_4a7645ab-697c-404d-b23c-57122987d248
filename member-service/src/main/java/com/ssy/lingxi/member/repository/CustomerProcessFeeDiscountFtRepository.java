package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.discount.CustomerProcessFeeDiscountFtDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 客户工费优惠附体明细Jpa仓库
 */
@Repository
public interface CustomerProcessFeeDiscountFtRepository extends JpaRepository<CustomerProcessFeeDiscountFtDO, Long>, JpaSpecificationExecutor<CustomerProcessFeeDiscountFtDO> {
}

