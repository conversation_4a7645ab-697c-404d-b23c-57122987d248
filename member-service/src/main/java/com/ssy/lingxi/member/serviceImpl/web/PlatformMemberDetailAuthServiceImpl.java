package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.component.base.enums.LanguageEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.model.req.maintenance.RoleUpdateReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.repository.MemberRelationRepository;
import com.ssy.lingxi.member.service.base.IBaseAuthService;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.base.IBaseTokenManageService;
import com.ssy.lingxi.member.service.web.IPlatformMemberDetailAuthService;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

/**
 * 平台后台 - 会员维护 - 会员详情 - 权限信息服务接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
@Service
public class PlatformMemberDetailAuthServiceImpl implements IPlatformMemberDetailAuthService {
    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseAuthService baseAuthService;

    @Resource
    private IBaseTokenManageService tokenManageService;

    @Override
    public AuthTreeResp getMemberDetailAuth(HttpHeaders headers, MemberValidateReq validateReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(validateReq.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(validateReq.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        // 平台会员权限树的管理，可视菜单按钮权限范围不超过其角色的权限范围
        String currentLanguage = LanguageEnum.getCurrentLanguage();
        return baseAuthService.getAuthTree(
                        relationDO.getMenuAuth(),
                        relationDO.getButtonAuth(),
                        CompletableFuture.supplyAsync(() -> baseAuthService.getMenuSetByIdSet(BitMapUtil.toIdSet(relationDO.getSubRole().getMenuAuth()), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor),
                        CompletableFuture.supplyAsync(() -> baseAuthService.getButtonSetByIdSet(BitMapUtil.toIdSet(relationDO.getSubRole().getButtonAuth()), currentLanguage), ThreadPoolConfig.asyncDefaultExecutor));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setRelationAuth(HttpHeaders headers, RoleUpdateReq roleUpdateReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        MemberRelationDO relationDO = relationRepository.findById(roleUpdateReq.getValidateId()).orElse(null);
        if (relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(roleUpdateReq.getMemberId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        MemberRoleDO memberRoleDO = relationDO.getSubRole();
        if(memberRoleDO == null) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        // 更新关系表权限
        relationDO.setMenuAuth(BitMapUtil.toByteArray(roleUpdateReq.getMenuIdList()));
        relationDO.setButtonAuth(BitMapUtil.toByteArray(roleUpdateReq.getButtonIdList()));
        relationRepository.save(relationDO);

        // 更新该会员的用户和用户角色权限
        baseAuthService.rebuildMemberAuth(relationDO.getSubMember());

        //删除当前会员的所有token
        CompletableFuture.runAsync(() -> tokenManageService.memberOffline(Collections.singletonList(relationDO.getSubMemberId())), ThreadPoolConfig.asyncDefaultExecutor);
    }
}
