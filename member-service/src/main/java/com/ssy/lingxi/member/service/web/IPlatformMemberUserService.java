package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserGetResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberUserQueryResp;
import org.springframework.http.HttpHeaders;

/**
 * 平台后台 - 用户操作接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-29
 */
public interface IPlatformMemberUserService {

    /**
     * 新增用户
     * @param headers HttpHeaders信息
     * @param addVO 接口参数
     * @return 操作结果
     */
    void addMemberUser(HttpHeaders headers, MemberUserAddReq addVO);

    /**
     * 查询用户信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    MemberUserGetResp getMemberUser(HttpHeaders headers, MemberUserIdReq idVO);

    /**
     * 更新用户
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    void updateMemberUser(HttpHeaders headers, MemberUserUpdateReq updateVO);

    /**
     * 删除用户
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    void deleteMemberUser(HttpHeaders headers, MemberUserIdReq idVO);

    /**
     * 更改用户状态
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    void updateMemberUserStatus(HttpHeaders headers, MemberUserUpdateStatusReq statusVO);

    /**
     * 分页查询用户
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    PageDataResp<MemberUserQueryResp> pageMemberUser(HttpHeaders headers, PageQueryMemberUserDataReq pageVO);


}
