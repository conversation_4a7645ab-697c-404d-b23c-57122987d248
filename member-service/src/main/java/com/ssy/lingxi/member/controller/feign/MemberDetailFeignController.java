package com.ssy.lingxi.member.controller.feign;

import com.ssy.lingxi.common.model.req.api.member.ActualControllerSyncReq;
import com.ssy.lingxi.common.model.req.api.member.CorporationSyncReq;
import com.ssy.lingxi.common.model.req.api.member.CustomerSyncReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberDetailFeign;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;
import com.ssy.lingxi.member.service.base.IBaseRegisterService;
import com.ssy.lingxi.member.service.feign.IMemberDetailFeignService;
import com.ssy.lingxi.member.service.web.IActualControllerService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员、用户信息相关内部Feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-06-03
 * @ignore 不需要提交到Yapi
 */
@RestController
public class MemberDetailFeignController implements IMemberDetailFeign {

    @Resource
    private IMemberDetailFeignService memberDetailFeignService;

    @Resource
    private IBaseRegisterService baseRegisterService;

    @Resource
    private IActualControllerService actualControllerService;


    /**
     * 查询用户Web端菜单权限Url列表
     * @param userIdVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<String>> getUserWebAuthUrls(@RequestBody @Valid MemberFeignUserIdReq userIdVO) {
        return WrapperUtil.success(memberDetailFeignService.getUserWebAuthUrls(userIdVO.getUserId(), userIdVO.getMemberId(),userIdVO.getRoleId()));
    }

    /**
     * 根据会员id查询用户
     * @param memberIds 会员id
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignMsgByMemberIdResp>> getUserByMemberIds(@RequestBody List<Long> memberIds) {
        return WrapperUtil.success(memberDetailFeignService.getUserByMemberIds(memberIds));
    }

    /**
     * 根据角色id查询用户
     * @param roleIds 角色id
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberFeignMsgByRoleIdResp>> getUserByRoleIds(@RequestBody List<Long> roleIds) {
        return WrapperUtil.success(memberDetailFeignService.getUserByRoleIds(roleIds));
    }

    /**
     * 结算能力 - 查询下级会员入库分类信息中，主营品类及结算方式
     * @param relationVO 接口参数
     * @return 查询结果，如无配置返回空列表
     */
    @Override
    public WrapperResp<List<MemberCategoryFeignResp>> findMemberBusinessCategories(@RequestBody MemberFeignRelationReq relationVO) {
        return WrapperUtil.success(memberDetailFeignService.findMemberBusinessCategories(relationVO));
    }

    /**
     * 根据用户Id查询用户信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<MemberFeignUserDetailResp> findMemberUser(@RequestBody MemberFeignUserIdReq idVO) {
        return WrapperUtil.success(memberDetailFeignService.findMemberUser(idVO));
    }


    /**
     * 根据会员id和角色id，查询会员角色为服务提供者的下级会员列表
     * @param memberVO 上级会员信息
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberManageQueryResp>> subordinateMemberList(@RequestBody MemberFeignSubordinateMemberReq memberVO) {
        return WrapperUtil.success(memberDetailFeignService.subordinateMemberList(memberVO));
    }

    /**
     * 根据会员id和角色id，查询会员角色为服务消费者的上级会员列表
     * @param memberVO 下级会员信息
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<MemberManageQueryResp>> superiorMemberList(@RequestBody MemberFeignSuperiorMemberReq memberVO) {
        return WrapperUtil.success(memberDetailFeignService.superiorMemberList(memberVO));
    }

    /**
     * 是否具有支付及查看订单价格权限
     * @param req 请求参数
     * @return 查询结果
     */
    @Override
    public WrapperResp<Boolean> hasOrderAuth(UserIdFeignReq req) {
        return WrapperUtil.success(memberDetailFeignService.hasOrderAuth(req));
    }

    /**
     * 同步企业
     * @param corporationSyncReq 同步企业
     * @return 响应结果
     */
    @Override
    public WrapperResp<Void> syncCorporation(CorporationSyncReq corporationSyncReq) {
        baseRegisterService.syncCorporation(corporationSyncReq);
        return WrapperUtil.success();
    }

    /**
     * 同步客户
     * @param customerSyncReq 同步企业
     * @return 响应结果
     */
    @Override
    public WrapperResp<Void> syncCustomer(CustomerSyncReq customerSyncReq) {
        baseRegisterService.syncCustomer(customerSyncReq);
        return WrapperUtil.success();
    }

    /**
     * 同步实控人
     * @param req 同步实控人
     * @return 响应结果
     */
    @Override
    public WrapperResp<Void> syncActualController(ActualControllerSyncReq req) {
        actualControllerService.syncActualController(req);
        return WrapperUtil.success();
    }

}
