package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.member.model.req.maintenance.RoleUpdateReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import org.springframework.http.HttpHeaders;

/**
 * 平台后台 - 会员维护 - 会员详情 - 权限信息服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-16
 */
public interface IPlatformMemberDetailAuthService {
    /**
     * 查询会员详情 - 会员权限信息 - 获取会员权限树
     * @param headers     HttpHeaders信息
     * @param validateReq 接口参数
     * @return 操作结果
     */
    AuthTreeResp getMemberDetailAuth(HttpHeaders headers, MemberValidateReq validateReq);

    /**
     * 更新权限菜单关联按钮
     * @param headers HttpHeaders信息
     * @param roleUpdateReq 接口参数
     * @return 操作结果
     */
    void setRelationAuth(HttpHeaders headers, RoleUpdateReq roleUpdateReq);
}
