package com.ssy.lingxi.member.handler.annotation;

import com.ssy.lingxi.member.handler.validator.MemberClassifyCodeValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 会员入库分类 - 会员编码格式校验
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-05-25
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {MemberClassifyCodeValidator.class})
public @interface MemberClassifyCodeAnnotation {

    boolean required() default true;

    int max_length() default 10;

    String message() default "会员编码最长10个字符，只允许英文字母、数字，下划线和中划线";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
