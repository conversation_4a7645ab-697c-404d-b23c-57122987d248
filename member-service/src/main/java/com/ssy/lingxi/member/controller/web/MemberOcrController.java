package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.rest.model.req.ocr.OcrBankCardReq;
import com.ssy.lingxi.component.rest.model.req.ocr.OcrIdCardReq;
import com.ssy.lingxi.component.rest.model.req.ocr.OcrLicenseReq;
import com.ssy.lingxi.component.rest.service.TencentOCRService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.models.BankCardOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.BizLicenseOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.IDCardOCRResponse;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 会员能力 - 会员OCR相关接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/15
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/ocr")
public class MemberOcrController {

    @Resource
    private TencentOCRService tencentOCRService;

    /**
     * 身份证识别接口
     *
     * @param ocrIdCardReq 身份证识别请求参数
     * @return 识别结果
     * @throws TencentCloudSDKException 识别异常
     */
    @PostMapping("/idCard")
    public WrapperResp<IDCardOCRResponse> recognizeIDCardByImageBase64(@RequestBody OcrIdCardReq ocrIdCardReq) throws TencentCloudSDKException {
        return WrapperUtil.success(tencentOCRService.recognizeIDCardByImageBase64(ocrIdCardReq));
    }

    /**
     * 银行卡识别接口
     *
     * @param ocrBankCardReq 银行卡识别请求参数
     * @return 识别结果
     * @throws TencentCloudSDKException 识别异常
     */
    @PostMapping("/bankCard")
    public WrapperResp<BankCardOCRResponse> recognizeBankCardByImageBase64(@RequestBody OcrBankCardReq ocrBankCardReq) throws TencentCloudSDKException {
        return WrapperUtil.success(tencentOCRService.recognizeBankCardByImageBase64(ocrBankCardReq));
    }

    /**
     * 企业营业执照
     *
     * @param ocrLicenseReq 营业执照识别请求参数
     * @return 识别结果
     */
    @PostMapping("/businessLicense")
    public WrapperResp<BizLicenseOCRResponse> recognizeBusinessLicenseByImageBase64(@RequestBody OcrLicenseReq ocrLicenseReq) throws TencentCloudSDKException {
        return WrapperUtil.success(tencentOCRService.recognizeBusinessLicenseByImageBase64(ocrLicenseReq));
    }


}
