package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.enums.MemberChangeRequestFormStatusEnum;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityMaintenanceMemberQueryDataReq;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import com.ssy.lingxi.member.service.web.IMemberLifecycleService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 会员能力 - 会员生命周期管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-07-04
 **/
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/lifecycle")
public class MemberLifecycleController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IMemberLifecycleService memberLifecycleService;

    /**
     * 状态下拉查询（档案管理）
     *
     * @param headers Http头部信息
     * @return 操作结果
     */
    @GetMapping("/pageItems")
    public WrapperResp<MemberArchivesManagementConditionResp> getPageCondition(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberLifecycleService.getPageCondition(headers, roleTag));
    }

    /**
     * 状态下拉查询（变更申请单查询）
     *
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/statusList")
    public WrapperResp<List<StatusResp>> listMemberChangeRequestStatus(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberLifecycleService.listMemberChangeRequestStatus(headers));
    }

    // ===================================档案管理=================================

    /**
     * 供应商档案管理 - 分页列表
     *
     * @param headers Http头部信息
     * @param queryVO 接口参数
     * @return 查询结果
     */
    @PostMapping("/archivesManagement/page")
    public WrapperResp<PageDataResp<MemberArchivesManagementQueryResp>> memberArchivesManagementPage(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityMaintenanceMemberQueryDataReq queryVO) {
        return WrapperUtil.success(memberLifecycleService.memberArchivesManagementPage(headers, queryVO, roleTag));
    }

    // ==================================变更申请单查询===============================

    /**
     * 变更申请单查询 - 分页列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/summary/page")
    public WrapperResp<PageDataResp<MemberChangeRequestSummaryPageQueryResp>> summaryMemberChangeRequestFormPage(@RequestHeader HttpHeaders headers, @Valid MemberChangeRequestBasicPageDataReq pageVO) {
        List<Integer> statusList = NumberUtil.isNullOrZero(pageVO.getStatus()) ? Collections.emptyList() : Collections.singletonList(pageVO.getStatus());
        return WrapperUtil.success(memberLifecycleService.baseMemberChangeRequestFormPage(headers, pageVO, null, statusList, roleTag));
    }

    /**
     * 变更申请单查询 - 详情
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @GetMapping("/summary/detail")
    public WrapperResp<MemberChangeRequestFormDetailQueryResp> summaryMemberChangeRequestFormDetail(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberLifecycleService.summaryMemberChangeRequestFormDetail(headers, idVO, roleTag));
    }

    // ==================================待提交变更申请单===============================

    /**
     * 待新增变更申请单 - 分页列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/waitAdd/page")
    public WrapperResp<PageDataResp<MemberChangeRequestSummaryPageQueryResp>> waitAddMemberChangeRequestFormPage(@RequestHeader HttpHeaders headers, @Valid MemberChangeRequestBasicPageDataReq pageVO) {
        MemberChangeRequestFormStatusEnum currentStatus = MemberChangeRequestFormStatusEnum.SUBMIT_FORM;
        List<Integer> statusList = Arrays.asList(MemberChangeRequestFormStatusEnum.SUBMIT_FORM.getCode(), MemberChangeRequestFormStatusEnum.WAIT_GRADE.getCode());
        return WrapperUtil.success(memberLifecycleService.baseMemberChangeRequestFormPage(headers, pageVO, currentStatus, statusList, roleTag));
    }

    /**
     * 待新增变更申请单 - 新增
     *
     * @param headers Http头部信息
     * @param addVO   接口参数
     */
    @PostMapping("/waitAdd/add")
    public WrapperResp<Void> addMemberChangeRequestForm(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberChangeRequestFormAddReq addVO) {
         memberLifecycleService.addMemberChangeRequestForm(headers, addVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 待新增变更申请单 - 修改
     *
     * @param headers  Http头部信息
     * @param updateVO 接口参数
     */
    @PostMapping("/waitAdd/update")
    public WrapperResp<Void> updateAddMemberChangeRequestForm(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberChangeRequestFormUpdateReq updateVO) {
         memberLifecycleService.updateAddMemberChangeRequestForm(headers, updateVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 待新增变更申请单 -删除
     *
     * @param headers Http头部信息
     * @param idsVO   接口参数
     */
    @PostMapping("/waitAdd/delete")
    public WrapperResp<Void> deleteMemberCheckRequestForm(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonIdListReq idsVO) {
         memberLifecycleService.deleteMemberCheckRequestForm(headers, idsVO);
        return WrapperUtil.success();
    }

    // ==================================待评分人评分===============================

    /**
     * 待评分人评分 - 分页列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/waitGrade/page")
    public WrapperResp<PageDataResp<MemberChangeRequestSummaryPageQueryResp>> waitGradeChangeRequestFormPage(@RequestHeader HttpHeaders headers, @Valid MemberChangeRequestBasicPageDataReq pageVO) {
        MemberChangeRequestFormStatusEnum currentStatus = MemberChangeRequestFormStatusEnum.WAIT_GRADE;
        return WrapperUtil.success(memberLifecycleService.waitGradeChangeRequestFormPage(headers, pageVO, currentStatus, Collections.singletonList(currentStatus.getCode()), roleTag));
    }

    /**
     * 待评分人评分 - 详情
     *
     * @param headers Http头部信息
     * @param idVO    接口参数
     * @return 查询结果
     */
    @GetMapping("/waitGrade/detail")
    public WrapperResp<MemberChangeRequestFormDetailQueryResp> waitGradeMemberChangeRequestFormDetail(@RequestHeader HttpHeaders headers, @Valid CommonIdReq idVO) {
        return WrapperUtil.success(memberLifecycleService.waitGradeMemberChangeRequestFormDetail(headers, idVO, roleTag));
    }

    /**
     * 待评分人评分 - 评分
     *
     * @param headers Http头部信息
     * @param gradeVO 接口参数
     */
    @PostMapping("/waitGrade/grade")
    public WrapperResp<Void> gradeChangeRequestForm(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberChangeRequestGradeReq gradeVO) {
         memberLifecycleService.gradeChangeRequestForm(headers, gradeVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================待汇总评分结果===============================

    /**
     * 待提交汇总评分结果 - 列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/waitSubmit/page")
    public WrapperResp<PageDataResp<MemberChangeRequestSummaryPageQueryResp>> waitSubmitMemberChangeRequestFormPage(@RequestHeader HttpHeaders headers, @Valid MemberChangeRequestBasicPageDataReq pageVO) {
        MemberChangeRequestFormStatusEnum currentStatus = MemberChangeRequestFormStatusEnum.WAIT_SUBMIT;
        return WrapperUtil.success(memberLifecycleService.baseMemberChangeRequestFormPage(headers, pageVO, currentStatus, Collections.singletonList(currentStatus.getCode()), roleTag));
    }

    /**
     * 待提交汇总评分结果 - 提交
     *
     * @param headers  Http头部信息
     * @param submitVO 接口参数
     */
    @PostMapping("/waitSubmit/submit")
    public WrapperResp<Void> submitMemberChangeRequestForm(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberChangeRequestFormSubmitReq submitVO) {
         memberLifecycleService.submitMemberChangeRequestForm(headers, submitVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================待审核变更申请单(一级)===============================

    /**
     * 待审核考评结果一级 - 会员考评分页列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/waitAuditOne/page")
    public WrapperResp<PageDataResp<MemberChangeRequestSummaryPageQueryResp>> waitAuditOneMemberChangeRequestFormPage(@RequestHeader HttpHeaders headers, @Valid MemberChangeRequestBasicPageDataReq pageVO) {
        MemberChangeRequestFormStatusEnum currentStatus = MemberChangeRequestFormStatusEnum.WAIT_AUDIT_1;
        return WrapperUtil.success(memberLifecycleService.baseMemberChangeRequestFormPage(headers, pageVO, currentStatus, Collections.singletonList(currentStatus.getCode()), roleTag));
    }

    /**
     * 待审核考评结果一级 - 审核
     *
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     */
    @PostMapping("/waitAuditOne/audit")
    public WrapperResp<Void> auditOneMemberAppraisal(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonAgreeReq agreeVO) {
         memberLifecycleService.auditOneMemberChangeRequestForm(headers, agreeVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================待审核变更申请单(二级)===============================

    /**
     * 待审核考评结果二级 - 会员考评分页列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/waitAuditTwo/page")
    public WrapperResp<PageDataResp<MemberChangeRequestSummaryPageQueryResp>> waitAuditTwoMemberChangeRequestFormPage(@RequestHeader HttpHeaders headers, @Valid MemberChangeRequestBasicPageDataReq pageVO) {
        MemberChangeRequestFormStatusEnum currentStatus = MemberChangeRequestFormStatusEnum.WAIT_AUDIT_2;
        return WrapperUtil.success(memberLifecycleService.baseMemberChangeRequestFormPage(headers, pageVO, currentStatus, Collections.singletonList(currentStatus.getCode()), roleTag));
    }

    /**
     * 待审核考评结果二级 - 审核
     *
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     */
    @PostMapping("/waitAuditTwo/audit")
    public WrapperResp<Void> auditTwoMemberChangeRequestForm(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonAgreeReq agreeVO) {
         memberLifecycleService.auditTwoMemberChangeRequestForm(headers, agreeVO, roleTag);
        return WrapperUtil.success();
    }

    // ==================================待确认变更申请单===============================

    /**
     * 待确认变更申请单 - 分页列表
     *
     * @param headers Http头部信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/waitConfirm/page")
    public WrapperResp<PageDataResp<MemberChangeRequestSummaryPageQueryResp>> waitConfirmMemberChangeRequestFormPage(@RequestHeader HttpHeaders headers, @Valid MemberChangeRequestBasicPageDataReq pageVO) {
        List<Integer> statusList = Arrays.asList(MemberChangeRequestFormStatusEnum.WAIT_AUDIT_1_REJECT.getCode(), MemberChangeRequestFormStatusEnum.WAIT_AUDIT_2_REJECT.getCode(), MemberChangeRequestFormStatusEnum.WAIT_CONFIRMED.getCode());
        MemberChangeRequestFormStatusEnum currentStatus = MemberChangeRequestFormStatusEnum.WAIT_CONFIRMED;
        return WrapperUtil.success(memberLifecycleService.baseMemberChangeRequestFormPage(headers, pageVO, currentStatus, statusList, roleTag));
    }

    /**
     * 待确认变更申请单 - 确认变更申请单
     *
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     */
    @PostMapping("/waitConfirm/confirm")
    public WrapperResp<Void> waitConfirmMemberChangeRequestFormPage(@RequestHeader HttpHeaders headers, @RequestBody @Valid CommonAgreeReq agreeVO) {
         memberLifecycleService.waitConfirmMemberChangeRequestFormPage(headers, agreeVO, roleTag);
        return WrapperUtil.success();
    }
}
