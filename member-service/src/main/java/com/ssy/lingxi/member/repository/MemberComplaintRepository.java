package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.complain.MemberComplaintDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员投诉与建议Repository
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Repository
public interface MemberComplaintRepository extends JpaRepository<MemberComplaintDO, Long>, JpaSpecificationExecutor<MemberComplaintDO> {
    List<MemberComplaintDO> findByMemberAndType(MemberDO member, Integer type);
}
