package com.ssy.lingxi.member.service.commission;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.model.req.commission.InvitationConfigReq;
import com.ssy.lingxi.member.model.req.commission.OrderCommissionConfigReq;
import com.ssy.lingxi.member.model.req.commission.WithdrawalConfigReq;
import com.ssy.lingxi.member.model.resp.commission.CommissionConfigResp;

import java.math.BigDecimal;

/**
 * 分佣配置服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
public interface ICommissionConfigService {


    /**
     * 查看分佣配置（无需登录）
     * @return 配置信息
     */
    CommissionConfigResp getCommissionConfig();

    /**
     * 更新邀请分佣配置
     * @param loginUser 登录用户
     * @param request 配置请求
     * @return 操作结果
     */
    Void updateInvitationConfig(UserLoginCacheDTO loginUser, InvitationConfigReq request);

    /**
     * 更新下单分佣配置
     * @param loginUser 登录用户
     * @param request 配置请求
     * @return 操作结果
     */
    Void updateOrderCommissionConfig(UserLoginCacheDTO loginUser, OrderCommissionConfigReq request);

    /**
     * 更新佣金提现配置
     * @param loginUser 登录用户
     * @param request 配置请求
     * @return 操作结果
     */
    Void updateWithdrawalConfig(UserLoginCacheDTO loginUser, WithdrawalConfigReq request);


}
