package com.ssy.lingxi.member.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.member.RoleTagEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.dataauth.annotation.member.MemberAuth;
import com.ssy.lingxi.member.model.req.basic.MemberTypeAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityAddMemberReq;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityImportMemberQueryDataReq;
import com.ssy.lingxi.member.model.req.validate.MemberAbilityUpdateMemberReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.resp.basic.LevelAndTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.service.base.IBaseMemberCacheService;
import com.ssy.lingxi.member.service.web.IMemberAbilityImportService;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 供应商能力 - 供应商录入（App）
 * <AUTHOR>
 * @since 2022/5/24 16:33
 */

@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/mobile/import")
public class MobileWechatAppletImportController {

    /**
     * 角色标签
     */
    private final Integer roleTag = RoleTagEnum.MEMBER.getCode();

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private IMemberAbilityImportService memberAbilityImportService;

    /**
     * 获取分页查询供应商列表页面中各个查询条件下拉选择框的内容
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/pageitems")
    public WrapperResp<MemberImportSearchConditionResp> getPageCondition(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityImportService.getPageCondition(headers, loginUser, roleTag));
    }

    /**
     * 分页、模糊查询供应商
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/page")
    @MemberAuth
    public WrapperResp<PageDataResp<MemberAbilityImportPageQueryResp>> pageMembers(@RequestHeader HttpHeaders headers, @Valid MemberAbilityImportMemberQueryDataReq queryVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityImportService.pageMembers(loginUser, queryVO, roleTag));
    }

    /**
     * 获取新增供应商页面内容（审核步骤、供应商类型、手机号前缀）
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/pageitems/basic")
    public WrapperResp<MemberAbilityAddMemberPageItemsResp> getAddMemberPageItems(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityImportService.getAddMemberPageItems(headers, loginUser));
    }

    /**
     * 新增供应商页面，根据供应商类型，查询角色列表
     * @param headers HttpHeaders信息
     * @param memberTypeReq 接口参数
     * @return 操作结果
     */
    @GetMapping("/pageitems/role")
    public WrapperResp<List<RoleIdAndNameResp>> getAddMemberPageRoles(@RequestHeader HttpHeaders headers, @Valid MemberTypeReq memberTypeReq) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityImportService.getAddMemberPageRoles(headers, loginUser, memberTypeReq, roleTag));
    }

    /**
     * 新增供应商页面，根据供应商类型和角色，查询等级列表
     * @param headers HttpHeaders信息
     * @param typeAndRoleIdVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/pageitems/level")
    public WrapperResp<List<LevelAndTagResp>> getAddMemberPageLevels(@RequestHeader HttpHeaders headers, @Valid MemberTypeAndRoleIdReq typeAndRoleIdVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityImportService.getAddMemberPageLevels(loginUser, typeAndRoleIdVO));
    }

    /**
     * 新增供应商页面，根据选择的角色，返回供应商注册资料信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/pageitems/detail")
    public WrapperResp<List<MemberConfigGroupResp>> getAddMemberPageMemberConfigItems(@RequestHeader HttpHeaders headers, @Valid RoleIdReq idVO) {
        return WrapperUtil.success(memberAbilityImportService.getAddMemberPageMemberConfigItems(headers, idVO));
    }

    /**
     * 会员详情
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail")
    public WrapperResp<MemberAbilityImportMemberTextDetailResp> getSubMemberDetail(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityImportService.getSubMemberDetail(loginUser, validateVO, roleTag));
    }

    /**
     * 新增供应商
     * @param headers HttpHeaders信息
     * @param memberVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/add")
    public WrapperResp<Void> addSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityAddMemberReq memberVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityImportService.addSubMember(loginUser, memberVO, roleTag);
        return WrapperUtil.success();
    }

    /**
     * 修改供应商信息
     * @param headers HttpHeaders信息
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/update")
    public WrapperResp<Void> updateSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberAbilityUpdateMemberReq updateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityImportService.updateSubMember(loginUser, updateVO);
        return WrapperUtil.success();
    }

    /**
     * 删除供应商
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/delete")
    public WrapperResp<Void> deleteSubMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityImportService.deleteSubMember(loginUser, validateVO);
        return WrapperUtil.success();
    }

    /**
     * 查询单个供应商详细信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberAbilityImportMemberDetailResp> getSubMember(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        return WrapperUtil.success(memberAbilityImportService.getSubMember(loginUser, validateVO, roleTag));
    }

    /**
     * 提交平台审核
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/commit")
    public WrapperResp<Void> commitSubMemberToValidate(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromMobile(headers);
        memberAbilityImportService.commitSubMemberToValidate(loginUser, validateVO);
        return WrapperUtil.success();
    }

}
