package com.ssy.lingxi.member.serviceImpl.mobile;

import cn.hutool.core.util.StrUtil;
import com.ssy.lingxi.common.enums.order.OrderSourceKindEnum;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.model.req.MemberTradeCommentSubmitDetailReq;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderCommentDO;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderProductCommentDO;
import com.ssy.lingxi.member.entity.do_.comment.MemberTradeCommentHistoryDO;
import com.ssy.lingxi.member.enums.MemberTradeTypeEnum;
import com.ssy.lingxi.member.model.req.mobile.MobileMemberTradeCommentSaveReq;
import com.ssy.lingxi.member.model.resp.mobile.*;
import com.ssy.lingxi.member.repository.comment.MemberOrderCommentRepository;
import com.ssy.lingxi.member.repository.comment.MemberOrderProductCommentRepository;
import com.ssy.lingxi.member.repository.comment.MemberTradeCommentHistoryRepository;
import com.ssy.lingxi.member.service.mobile.IMobileMemberCommentService;
import com.ssy.lingxi.member.serviceImpl.web.comment.BaseMemberCommentServiceImpl;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderFeignIdsReq;
import com.ssy.lingxi.order.api.model.resp.OrderVendorLogoFeignResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * App - 会员评价服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/2/24
 */
@Service
public class MobileMemberCommentServiceImpl extends BaseMemberCommentServiceImpl implements IMobileMemberCommentService {

    @Resource
    private MemberOrderCommentRepository memberOrderCommentRepository;

    @Resource
    private MemberOrderProductCommentRepository memberOrderProductCommentRepository;

    @Resource
    private MemberTradeCommentHistoryRepository memberTradeCommentHistoryRepository;

    @Resource
    private IOrderProcessFeign orderFeignService;

    @Override
    public PageDataResp<MobileWaitCommentOrderProductPageResp> pageWaitOrderComment(UserLoginCacheDTO loginUser, PageDataReq pageDataReq) {
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize(), Sort.by("order.createTime").descending());
        Page<MemberOrderProductCommentDO> pageList = memberOrderProductCommentRepository.findAll((Specification<MemberOrderProductCommentDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            // 未评价
            predicateList.add(cb.equal(root.get("buyerCommentStatus"), EnableDisableStatusEnum.DISABLE.getCode()));

            Join<Object, Object> orderJoin = root.join("order", JoinType.LEFT);
            predicateList.add(cb.equal(orderJoin.get("buyerMemberId"), loginUser.getMemberId()));
            predicateList.add(cb.equal(orderJoin.get("buyerRoleId"), loginUser.getMemberRoleId()));

            // 非SRM订单(SRM订单含有物料,不能评价)
            List<Integer> orderSourceKinds = Arrays.asList(
                    OrderSourceKindEnum.BUYER.getCode(),
                    OrderSourceKindEnum.B2B.getCode(),
                    OrderSourceKindEnum.MOBILE_BUYER.getCode(),
                    OrderSourceKindEnum.RIGHT_POINT.getCode(),
                    OrderSourceKindEnum.AGENT.getCode(),
                    OrderSourceKindEnum.MOBILE_RIGHT_POINT.getCode(),
                    OrderSourceKindEnum.GROUP.getCode()
            );
            predicateList.add(cb.in(orderJoin.get("orderKind")).value(orderSourceKinds));

            // app只显示非合同类型的订单
            List<Integer> orderTypeList = Arrays.asList(
                    OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode(),
                    OrderTypeEnum.NEED_TO_PURCHASE.getCode(),
                    OrderTypeEnum.SPOT_PURCHASING.getCode(),
                    OrderTypeEnum.COLLECTIVE_PURCHASE.getCode(),
                    OrderTypeEnum.CREDITS_EXCHANGE.getCode());
            predicateList.add(cb.in(orderJoin.get("orderType")).value(orderTypeList));

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        List<MobileWaitCommentOrderProductPageResp> resultList = pageList.stream().map(e -> {
            MobileWaitCommentOrderProductPageResp waitCommentOrderPageVO = new MobileWaitCommentOrderProductPageResp();
            waitCommentOrderPageVO.setOrderProductId(e.getId());
            waitCommentOrderPageVO.setProductId(e.getProductId());
            waitCommentOrderPageVO.setSkuId(e.getSkuId());
            waitCommentOrderPageVO.setProductNo(e.getProductNo());
            waitCommentOrderPageVO.setName(e.getName());
            waitCommentOrderPageVO.setCategory(e.getCategory());
            waitCommentOrderPageVO.setBrand(e.getBrand());
            waitCommentOrderPageVO.setSpec(e.getSpec());
            waitCommentOrderPageVO.setUnit(e.getUnit());
            waitCommentOrderPageVO.setLogo(e.getLogo());
            waitCommentOrderPageVO.setPrice(e.getPrice());
            waitCommentOrderPageVO.setQuantity(e.getQuantity());
            waitCommentOrderPageVO.setAmount(e.getAmount());
            waitCommentOrderPageVO.setBuyerCommentStatus(e.getBuyerCommentStatus());
            return waitCommentOrderPageVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public MobileCommentOrderProductResp getMemberOrderComment(UserLoginCacheDTO loginUser, CommonIdReq commonIdReq) {
        MemberOrderProductCommentDO orderProductCommentDO = memberOrderProductCommentRepository.findById(commonIdReq.getId()).orElse(null);
        if (Objects.isNull(orderProductCommentDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MobileCommentOrderProductResp response = new MobileCommentOrderProductResp();
        response.setOrderProductId(orderProductCommentDO.getId());
        response.setProductId(orderProductCommentDO.getProductId());
        response.setSkuId(orderProductCommentDO.getSkuId());
        response.setProductNo(orderProductCommentDO.getProductNo());
        response.setName(orderProductCommentDO.getName());
        response.setCategory(orderProductCommentDO.getCategory());
        response.setBrand(orderProductCommentDO.getBrand());
        response.setSpec(orderProductCommentDO.getSpec());
        response.setUnit(orderProductCommentDO.getUnit());
        response.setLogo(orderProductCommentDO.getLogo());
        response.setPrice(orderProductCommentDO.getPrice());
        response.setQuantity(orderProductCommentDO.getQuantity());
        response.setAmount(orderProductCommentDO.getAmount());

        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveMemberComment(UserLoginCacheDTO loginUser, MobileMemberTradeCommentSaveReq memberTradeCommentSaveVO) {
        MemberOrderProductCommentDO orderProductCommentDO = memberOrderProductCommentRepository.findById(memberTradeCommentSaveVO.getOrderProductId()).orElse(null);
        if (Objects.isNull(orderProductCommentDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberOrderCommentDO memberOrderCommentDO = memberOrderCommentRepository.findById(orderProductCommentDO.getOrder().getId()).orElse(null);
        if (Objects.isNull(memberOrderCommentDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MemberTradeCommentSubmitDetailReq submitDetailVO = new MemberTradeCommentSubmitDetailReq();
        submitDetailVO.setOrderProductId(memberTradeCommentSaveVO.getOrderProductId());
        submitDetailVO.setStar(memberTradeCommentSaveVO.getStar());
        submitDetailVO.setComment(memberTradeCommentSaveVO.getComment());
        submitDetailVO.setPics(memberTradeCommentSaveVO.getPics());

        this.baseSubmitMemberTradeComment(MemberTradeTypeEnum.BUYER, memberOrderCommentDO, Collections.singletonList(submitDetailVO), true);
    }

    @Override
    public PageDataResp<MobileCompleteCommentPageResp> pageCompleteOrderComment(UserLoginCacheDTO loginUser, PageDataReq pageDataReq) {
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize(), Sort.by("createTime").descending());

        Page<MemberTradeCommentHistoryDO> pageList = memberTradeCommentHistoryRepository.findAll((Specification<MemberTradeCommentHistoryDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("memberId"), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("roleId"), loginUser.getMemberRoleId()));
            predicateList.add(cb.equal(root.get("tradeType"), MemberTradeTypeEnum.BUYER.getTypeEnum()));

            // app只显示非合同类型的订单
            List<Integer> orderTypeList = Arrays.asList(
                    OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode(),
                    OrderTypeEnum.NEED_TO_PURCHASE.getCode(),
                    OrderTypeEnum.SPOT_PURCHASING.getCode(),
                    OrderTypeEnum.COLLECTIVE_PURCHASE.getCode(),
                    OrderTypeEnum.CREDITS_EXCHANGE.getCode());
            predicateList.add(cb.in(root.get("orderType")).value(orderTypeList));

            // 最近一年的数据
            predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), LocalDateTime.now().minusYears(1L)));

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        // 被评价会员列表
        Set<MemberAndRoleIdDTO> memberRoleSet = pageList.stream().map(e -> new MemberAndRoleIdDTO(e.getSubMemberId(), e.getSubRoleId())).collect(Collectors.toSet());

        // 店铺头像map
        List<Long> orderIds = pageList.stream().map(MemberTradeCommentHistoryDO::getOrderId).collect(Collectors.toList());
        Map<Long, String> memberShopLogoMap = getMemberShopLogoMap(orderIds);

        List<MobileCompleteCommentPageResp> resultList = pageList.stream().map(e -> {
            MobileCompleteCommentPageResp mobileTradeCommentPageVO = new MobileCompleteCommentPageResp();
            mobileTradeCommentPageVO.setId(e.getId());
            mobileTradeCommentPageVO.setStar(e.getStar());
            mobileTradeCommentPageVO.setComment(Optional.ofNullable(e.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(e.getComment()));
            mobileTradeCommentPageVO.setShopId(e.getShopId());
            mobileTradeCommentPageVO.setProductId(e.getProductId());
            mobileTradeCommentPageVO.setSkuId(e.getSkuId());
            mobileTradeCommentPageVO.setProduct(e.getProduct());
            mobileTradeCommentPageVO.setOrderId(e.getOrderId());
            mobileTradeCommentPageVO.setOrderType(e.getOrderType());
            mobileTradeCommentPageVO.setOrderNo(e.getOrderNo());
            mobileTradeCommentPageVO.setDealTime(e.getDealTime());
            mobileTradeCommentPageVO.setCreateTime(e.getCreateTime());
            mobileTradeCommentPageVO.setPrice(e.getPrice());
            mobileTradeCommentPageVO.setTotalPrice(e.getTotalPrice());
            mobileTradeCommentPageVO.setPics(e.getPics());
            mobileTradeCommentPageVO.setProductImgUrl(e.getProductImgUrl());
            mobileTradeCommentPageVO.setDealCount(e.getDealCount());
            mobileTradeCommentPageVO.setUnit(e.getUnit());
            mobileTradeCommentPageVO.setReplyContent(e.getReplyContent());

            if (MemberTypeEnum.MERCHANT.getCode().equals(loginUser.getMemberType())
                    || MemberTypeEnum.MERCHANT_PERSONAL.getCode().equals(loginUser.getMemberType())) {
                // 企业, 个人
                mobileTradeCommentPageVO.setSubMemberName(e.getSubMemberName());
                String logo = memberShopLogoMap.get(e.getOrderId());
                mobileTradeCommentPageVO.setSubMemberLogo(StringUtils.isNotEmpty(logo) ? logo : "");
            }
            return mobileTradeCommentPageVO;
        }).collect(Collectors.toList());
        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    @Override
    public MobileCommentHistoryResp getMemberCommentHistory(UserLoginCacheDTO loginUser, CommonIdReq commonIdReq) {
        MemberTradeCommentHistoryDO commentHistoryDO = memberTradeCommentHistoryRepository.findById(commonIdReq.getId()).orElse(null);
        if (Objects.isNull(commentHistoryDO)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_RECORDS_DON_T_EXIST);
        }

        MobileCommentHistoryResp commentOrderProductVO = new MobileCommentHistoryResp();
        commentOrderProductVO.setId(commentHistoryDO.getId());
        commentOrderProductVO.setOrderProductId(commentHistoryDO.getOrderProductId());
        commentOrderProductVO.setOrderProductId(commentHistoryDO.getOrderProductId());
        commentOrderProductVO.setProductId(commentHistoryDO.getProductId());
        commentOrderProductVO.setSkuId(commentHistoryDO.getSkuId());
        commentOrderProductVO.setProductNo(commentHistoryDO.getOrderNo());
        commentOrderProductVO.setName(commentHistoryDO.getProduct());
        commentOrderProductVO.setCategory(commentHistoryDO.getCategory());
        commentOrderProductVO.setBrand(commentHistoryDO.getBrand());
        commentOrderProductVO.setSpec(commentHistoryDO.getSpec());
        commentOrderProductVO.setUnit(commentHistoryDO.getUnit());
        commentOrderProductVO.setLogo(commentHistoryDO.getProductImgUrl());
        commentOrderProductVO.setPrice(commentHistoryDO.getPrice());
        commentOrderProductVO.setQuantity(commentHistoryDO.getDealCount());
        commentOrderProductVO.setAmount(commentHistoryDO.getTotalPrice());

        return commentOrderProductVO;
    }

    @Override
    public PageDataResp<MobileReceiveCommentPageResp> pageReceiveOrderComment(UserLoginCacheDTO loginUser, PageDataReq pageDataReq) {
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize(), Sort.by("createTime").descending());

        Page<MemberTradeCommentHistoryDO> pageList = memberTradeCommentHistoryRepository.findAll((Specification<MemberTradeCommentHistoryDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("subMemberId"), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("subRoleId"), loginUser.getMemberRoleId()));
            predicateList.add(cb.equal(root.get("tradeType"), MemberTradeTypeEnum.SELLER.getTypeEnum()));

            // app只显示非合同类型的订单
            List<Integer> orderTypeList = Arrays.asList(
                    OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode(),
                    OrderTypeEnum.NEED_TO_PURCHASE.getCode(),
                    OrderTypeEnum.SPOT_PURCHASING.getCode(),
                    OrderTypeEnum.COLLECTIVE_PURCHASE.getCode(),
                    OrderTypeEnum.CREDITS_EXCHANGE.getCode());
            predicateList.add(cb.in(root.get("orderType")).value(orderTypeList));

            // 最近一年的数据
            predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), LocalDateTime.now().minusYears(1L)));

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);

        // 评价会员列表
        Set<MemberAndRoleIdDTO> memberRoleSet = pageList.stream().map(e -> new MemberAndRoleIdDTO(e.getMemberId(), e.getRoleId())).collect(Collectors.toSet());

        // 店铺头像map
        List<Long> orderIds = pageList.stream().map(MemberTradeCommentHistoryDO::getOrderId).collect(Collectors.toList());
        Map<Long, String> memberShopLogoMap = getMemberShopLogoMap(orderIds);

        List<MobileReceiveCommentPageResp> resultList = pageList.stream().map(e -> {
            MobileReceiveCommentPageResp mobileTradeCommentPageVO = new MobileReceiveCommentPageResp();
            mobileTradeCommentPageVO.setId(e.getId());
            mobileTradeCommentPageVO.setStar(e.getStar());
            mobileTradeCommentPageVO.setComment(Optional.ofNullable(e.getCommentCode()).flatMap(v -> Optional.ofNullable(MemberStringEnum.findMemberStringByCode(v)).map(MemberStringEnum::getName)).orElse(e.getComment()));
            mobileTradeCommentPageVO.setShopId(e.getShopId());
            mobileTradeCommentPageVO.setProductId(e.getProductId());
            mobileTradeCommentPageVO.setSkuId(e.getSkuId());
            mobileTradeCommentPageVO.setProduct(e.getProduct());
            mobileTradeCommentPageVO.setOrderId(e.getOrderId());
            mobileTradeCommentPageVO.setOrderType(e.getOrderType());
            mobileTradeCommentPageVO.setOrderNo(e.getOrderNo());
            mobileTradeCommentPageVO.setDealTime(e.getDealTime());
            mobileTradeCommentPageVO.setPrice(e.getPrice());
            mobileTradeCommentPageVO.setTotalPrice(e.getTotalPrice());
            mobileTradeCommentPageVO.setPics(e.getPics());
            mobileTradeCommentPageVO.setProductImgUrl(e.getProductImgUrl());
            mobileTradeCommentPageVO.setDealCount(e.getDealCount());
            mobileTradeCommentPageVO.setUnit(e.getUnit());
            // 企业, 个人
            mobileTradeCommentPageVO.setMemberName(e.getMemberName());
            String logo = memberShopLogoMap.get(e.getOrderId());
            mobileTradeCommentPageVO.setMemberLogo(StringUtils.isNotEmpty(logo) ? logo : "");

            return mobileTradeCommentPageVO;
        }).collect(Collectors.toList());
        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 获取店铺logoMap
     * @param orderIds 订单ID
     * @return 返回结果
     */
    private Map<Long, String> getMemberShopLogoMap(List<Long> orderIds) {
        List<OrderVendorLogoFeignResp> shopListInnerVOS = Collections.emptyList();
        OrderFeignIdsReq feignVO = new OrderFeignIdsReq();
        feignVO.setOrderIds(orderIds);
        WrapperResp<List<OrderVendorLogoFeignResp>> listWrapperResp = orderFeignService.findOrderVendorLogo(feignVO);
        if (ResponseCodeEnum.SUCCESS.getCode() == listWrapperResp.getCode()) {
            shopListInnerVOS = listWrapperResp.getData();
        }

        return shopListInnerVOS.stream().peek(vo -> {
            if (StrUtil.isBlank(vo.getLogo())) {
                vo.setLogo("");
            }
        }).collect(Collectors.toMap(OrderVendorLogoFeignResp::getOrderId, OrderVendorLogoFeignResp::getLogo, (e1, e2) -> e2));
    }
}
