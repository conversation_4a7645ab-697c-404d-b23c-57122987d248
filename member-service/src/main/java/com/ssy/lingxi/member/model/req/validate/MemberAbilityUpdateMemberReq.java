package com.ssy.lingxi.member.model.req.validate;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**会员能力 - 修改会员接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-02
 */
public class MemberAbilityUpdateMemberReq extends MemberAbilityAddMemberReq implements Serializable {
    private static final long serialVersionUID = -3793941993769511435L;

    /**
     * 会员Id
     */
    @NotNull(message = "会员Id要大于0")
    @Positive(message = "会员Id要大于0")
    private Long memberId;

    /**
     * 审核内容Id
     */
    @NotNull(message = "审核内容Id要大于0")
    @Positive(message = "审核内容Id要大于0")
    private Long validateId;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getValidateId() {
        return validateId;
    }

    public void setValidateId(Long validateId) {
        this.validateId = validateId;
    }
}
