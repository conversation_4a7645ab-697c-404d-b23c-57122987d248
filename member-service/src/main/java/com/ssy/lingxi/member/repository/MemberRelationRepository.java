package com.ssy.lingxi.member.repository;

import com.ssy.lingxi.member.api.model.resp.MemberFeignLifeCycleRuleResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 会员关系操作
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-18
 */
@Repository
public interface MemberRelationRepository extends JpaRepository<MemberRelationDO, Long>, JpaSpecificationExecutor<MemberRelationDO> {
    List<MemberRelationDO> findAllBySubMemberId(Long subMemberId);

    List<MemberRelationDO> findByMemberIdAndRoleId(Long upperMemberId, Long upperRoleId);

    List<MemberRelationDO> findBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    MemberRelationDO findFirstBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    List<MemberRelationDO> findBySubMemberIdAndSubRoleIdAndSubMemberLevelTypeEnumNot(Long subMemberId, Long subRoleId,Integer levelTypeEnum);

    List<MemberRelationDO> findBySubMemberIdAndSubRoleIdAndVerified(Long subMemberId, Long subRoleId, Integer verified);

    Page<MemberRelationDO> findBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId, Pageable pageable);

    Page<MemberRelationDO> findBySubMemberIdAndSubRoleIdAndRelType(Long subMemberId, Long subRoleId, Integer relType, Pageable pageable);

    Page<MemberRelationDO> findBySubMemberIdAndSubRoleIdAndRelTypeAndVerified(Long subMemberId, Long subRoleId, Integer relType, Integer verified, Pageable pageable);

    List<MemberRelationDO> findBySubMemberIdAndSubRoleIdAndRelType(Long subMemberId, Long subRoleId, Integer relType);

    MemberRelationDO findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);

    List<MemberRelationDO> findAllByRelType(Integer relType);

    List<MemberRelationDO> findByRelTypeAndSubMemberIdIn(Integer relType, List<Long> subMemberIds);

    MemberRelationDO findFirstBySubMemberIdAndSubRoleIdAndRelType(Long subMemberId, Long subRoleId, Integer relType);

    MemberRelationDO findFirstBySubMemberIdAndSubRoleIdAndRelTypeAndVerified(Long subMemberId, Long subRoleId, Integer relType, Integer verified);

    List<MemberRelationDO> findAllBySubMemberIdInAndRelType(List<Long> subMemberId, Integer relType);

    List<MemberRelationDO> findBySubMemberIdAndRelType(Long subMemberId, Integer relType);

    List<MemberRelationDO> findBySubMemberIdAndRelTypeAndStatusAndVerified(Long subMemberId, Integer relType, Integer status, Integer verified);

    boolean existsBySubMemberIdAndSubRoleIdAndRelType(Long subMemberId, Long subRoleId, Integer relType);

    boolean existsBySubMemberIdAndSubRoleIdAndRelTypeAndIdNot(Long subMemberId, Long subRoleId, Integer relType, Long id);

    boolean existsByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(Long memberId, Long roleId, Long subMemberId, Long subRoleId);

    @Transactional
    void deleteAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);

    MemberRelationDO findFirstByRelType(Integer relType);

    List<MemberRelationDO> findAllByMemberIdAndRoleIdAndRelType(Long memberId, Long memberRoleId, Integer relType);

    boolean existsBySubRoleIdAndRelType(Long subRoleId, Integer relType);

    @Query(value = "select * from mem_member_relation where member_id = :memberId and sub_role_tag = :subRoleTag and role_id in (:roleIds)", nativeQuery = true)
    List<MemberRelationDO> findByMemberIdAndSubRoleTag(Long memberId, Integer subRoleTag, List<Long> roleIds);

    @Query(value = "SELECT mr.member_id,mr.role_id FROM mem_member_relation mr LEFT JOIN mem_member_lifecycle_stages lc ON mr.member_lifecycle_stages_id = lc.id \n" +
            "WHERE mr.member_id = :memberId AND mr.role_id = :roleId AND mr.role_tag = :roleTag AND jsonb_contains(lc.lifecycle_stages_rule_ids, ':lifeCycleStageRuleId')", nativeQuery = true)
    List<MemberFeignLifeCycleRuleResp> findSubMemberWithLifecycleRule(Long memberId, Long roleId, Integer roleTag, Long lifeCycleStageRuleId);

    List<MemberRelationDO> findByIdIn(List<Long> relationIds);

    @Query(value = "select max(r1.id) as id, r1.sub_member_id as member_id, r1.sub_role_id as role_id from mem_member_relation r1\n" +
            "left join mem_member m on m.id = r1.sub_member_id\n" +
            "left outer join mem_member_classification mc on (r1.classification_id = mc.id) left outer join mem_member_business_category bc on (mc.id = bc.business_category_id)\n" +
            "where not (exists(select 1 from mem_member_relation r2\n" +
            "where r2.member_id = :memberId and r2.role_id = :roleId and r1.sub_member_id = r2.sub_member_id and r1.sub_role_id = r2.sub_role_id))\n" +
            "and not (exists(select 1 from mem_member_relation r3\n" +
            "where r3.sub_member_id = :memberId and r3.sub_role_id = :roleId and r1.id = r3.id))\n" +
            "and not (exists(select 1 from mem_member_receive_invitation ri\n" +
            "where ri.member_id = :memberId and ri.role_id = :roleId and r1.sub_member_id = ri.sub_member_id and r1.sub_role_id = ri.sub_role_id))\n" +
            "and (r1.sub_member_type_enum in (:memberTypeEnum1, :memberTypeEnum2)) and r1.inner_status in (:innerStatus1, :innerStatus2)\n" +
            "and r1.outer_status in (:outerStatus1, :outerStatus2) and (case when :roleTag != 0 then r1.sub_role_tag = :roleTag else true end)\n" +
            "and (case when :memberName != '' then m.name like :memberName else true end)\n" +
            "group by r1.sub_member_id, r1.sub_role_id\n" +
            "order by r1.sub_member_id desc",
            countQuery = "select count(*)\n" +
                    "from (select max(r1.id) as id, r1.sub_member_id as member_id, r1.sub_role_id as role_id from mem_member_relation r1\n" +
                    "left join mem_member m on m.id = r1.sub_member_id\n" +
                    "left outer join mem_member_classification mc on (r1.classification_id = mc.id)\n" +
                    "left outer join mem_member_business_category bc on (mc.id = bc.business_category_id)\n" +
                    "where not (exists(select 1 from mem_member_relation r2\n" +
                    "where r2.member_id = :memberId and r2.role_id = :roleId and r1.sub_member_id = r2.sub_member_id and r1.sub_role_id = r2.sub_role_id))\n" +
                    "and not (exists(select 1 from mem_member_relation r3\n" +
                    "where r3.sub_member_id = :memberId and r3.sub_role_id = :roleId and r1.id = r3.id))\n" +
                    "and not (exists(select 1 from mem_member_receive_invitation ri\n" +
                    "where ri.member_id = :memberId and ri.role_id = :roleId and r1.sub_member_id = ri.sub_member_id and r1.sub_role_id = ri.sub_role_id))\n" +
                    "and (r1.sub_member_type_enum in (:memberTypeEnum1, :memberTypeEnum2)) and r1.inner_status in (:innerStatus1, :innerStatus2)\n" +
                    "and r1.outer_status in (:outerStatus1, :outerStatus2) and (case when :roleTag != 0 then r1.sub_role_tag = :roleTag else true end)\n" +
                    "and (case when :memberName != '' then m.name like :memberName else true end)\n" +
                    "group by r1.sub_member_id, r1.sub_role_id\n" +
                    "order by r1.sub_member_id desc)temp",
            nativeQuery = true)
    Page<Map<String, Object>> findMembersByCategory(@Param("memberId") Long memberId, @Param("roleId") Long roleId, @Param("memberTypeEnum1") Integer memberTypeEnum1, @Param("memberTypeEnum2") Integer memberTypeEnum2,
                                    @Param("innerStatus1") Integer innerStatus1, @Param("innerStatus2") Integer innerStatus2, @Param("outerStatus1") Integer outerStatus1, @Param("outerStatus2") Integer outerStatus2,
                                    @Param("roleTag") Integer roleTag, @Param("memberName") String memberName,
                                    Pageable page);

    long countByOuterStatusNotAndRelTypeAndInnerStatusNotAndCreateTimeBetween(Integer outerStatus, Integer relType, Integer innerStatus, LocalDateTime startTime, LocalDateTime endTime);

    // 查询商家会员关系
    MemberRelationDO findFirstByIsMerchant(Boolean isMerchant);

    List<MemberRelationDO> findAllByMemberIdAndRoleIdAndSubMemberIdInAndSubRoleId(Long memberId, Long roleId, List<Long> subMemberIds, Long subRoleId);

}
