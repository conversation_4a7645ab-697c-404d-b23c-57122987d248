package com.ssy.lingxi.member.model.req.lifecycle;

import com.ssy.lingxi.member.model.req.basic.FileUploadReq;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 会员整改VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Data
public class MemberRectifyReportReq implements Serializable {
    private static final long serialVersionUID = 7042253194436017391L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 整改报告摘要
     */
    private String reportDigest;

    /**
     * 整改报告附件
     */
    @Valid
    private List<FileUploadReq> reportAttachments;
}
