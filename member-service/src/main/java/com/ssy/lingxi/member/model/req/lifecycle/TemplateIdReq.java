package com.ssy.lingxi.member.model.req.lifecycle;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 评分模板Id作为接口参数VO
 * <AUTHOR>
 * @version 1.0
 * @since 2022/6/27 19:39
 */
@Data
public class TemplateIdReq implements Serializable {
    private static final long serialVersionUID = -6449422222145324404L;

    /**
     * 评分模板Id
     */
    @NotNull(message = "评分模板Id要大于0")
    @Positive(message = "评分模板Id要大于0")
    private Long templateId;
}
