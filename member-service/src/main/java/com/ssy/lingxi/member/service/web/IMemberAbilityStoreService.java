//package com.ssy.lingxi.member.service.web;
//
//import com.ssy.lingxi.common.model.resp.PageDataResp;
//import com.ssy.lingxi.common.model.resp.WrapperResp;
//import com.ssy.lingxi.member.model.req.lifecycle.CommonIdReq;
//import com.ssy.lingxi.member.model.req.maintenance.MemberStoreAddReq;
//import com.ssy.lingxi.member.model.req.maintenance.MemberStorePageDataReq;
//import com.ssy.lingxi.member.model.req.maintenance.MemberStoreUpdateReq;
//import com.ssy.lingxi.member.model.resp.maintenance.MemberStoreDetailResp;
//import com.ssy.lingxi.member.model.resp.maintenance.MemberStorePageQueryResp;
//import org.springframework.http.HttpHeaders;
//
///**
// * 系统能力 - 权限管理 - 门店管理服务接口
// * <AUTHOR>
// * @version 2.0.0
// * @since 2022/02/10
// */
//public interface IMemberAbilityStoreService {
//    /**
//     * 门店管理分页查询列表
//     * @param headers HttpHeaders信息
//     * @param pageVO  接口参数
//     * @return 查询结果
//     */
//    WrapperResp<PageDataResp<MemberStorePageQueryResp>> memberStorePages(HttpHeaders headers, MemberStorePageDataReq pageVO);
//
//    /**
//     * 查看门店详情
//     * @param headers HttpHeaders信息
//     * @param idVO    接口参数
//     * @return 查询结果
//     */
//    WrapperResp<MemberStoreDetailResp> memberStoreDetail(HttpHeaders headers, CommonIdReq idVO);
//
//    /**
//     * 新增门店
//     * @param headers HttpHeaders信息
//     * @param addVO   接口参数
//     * @return 操作结果
//     */
//    WrapperResp<Void> addMemberStore(HttpHeaders headers, MemberStoreAddReq addVO);
//
//    /**
//     * 修改门店
//     * @param headers  HttpHeaders信息
//     * @param updateVO 接口参数
//     * @return 操作结果
//     */
//    WrapperResp<Void> updateMemberStore(HttpHeaders headers, MemberStoreUpdateReq updateVO);
//
//    /**
//     * 删除门店
//     * @param headers HttpHeaders信息
//     * @param idVO    接口参数
//     * @return 操作结果
//     */
//    WrapperResp<Void> deleteMemberStore(HttpHeaders headers, CommonIdReq idVO);
//
//    /**
//     * 启用/停用门店
//     * @param headers HttpHeaders信息
//     * @param idVO    接口参数
//     * @return 操作结果
//     */
//    WrapperResp<Void> enableMemberStore(HttpHeaders headers, CommonIdReq idVO);
//}
