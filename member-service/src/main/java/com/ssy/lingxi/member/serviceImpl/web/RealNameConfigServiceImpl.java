package com.ssy.lingxi.member.serviceImpl.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.api.enums.BaiDuConfigEnum;
import com.ssy.lingxi.member.api.enums.RealNameTypeEnum;
import com.ssy.lingxi.member.api.model.req.DeleteRealNameConfigReq;
import com.ssy.lingxi.member.api.model.req.RealNameConfigReq;
import com.ssy.lingxi.member.entity.do_.RealNameConfigDO;
import com.ssy.lingxi.member.model.resp.RealNameConfigResp;
import com.ssy.lingxi.member.repository.RealNameConfigRepository;
import com.ssy.lingxi.member.service.web.IRealNameConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实名验证配置类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/13
 */
@Service
public class RealNameConfigServiceImpl implements IRealNameConfigService {
    @Resource
    private IRedisUtils redisUtils;
    @Resource
    private RealNameConfigRepository realNameConfigRepository;

    /**
     * 查询服务器参数配置信息列表
     */
    @Override
    public List<RealNameConfigResp> getConfigList() {
        List<RealNameConfigDO> smsConfigDOList = realNameConfigRepository.findAll();
        return smsConfigDOList.stream().map(realNameConfigDO -> BeanUtil.copyProperties(realNameConfigDO, RealNameConfigResp.class)).collect(Collectors.toList());
    }

    /**
     * 查询生效的配置信息
     *
     * @return MemberAuthDO
     */
    @Override
    public RealNameConfigDO findEnableAuthConfig() {
        RealNameConfigDO realNameConfigDO = null;
        //先从redis缓存中查找配置信息
        String json = redisUtils.stringGet(RedisConstant.REDIS_REAL_NAME_CONFIG, RedisConstant.REDIS_USER_INDEX);
        if (StringUtils.isNotEmpty(json)) {
            realNameConfigDO = JSONUtil.toBean(json, RealNameConfigDO.class);
        }
        //从redis缓存中获取不到配置信息,则从数据库中查找
        if (realNameConfigDO == null) {
            realNameConfigDO = realNameConfigRepository.findByStatus(Boolean.TRUE);
        }
        return realNameConfigDO;
    }

    /**
     * 添加或修改实名验证公共参数
     *
     * @param realNameConfigReq 实名验证公共参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public Boolean saveOrUpdateConfig(RealNameConfigReq realNameConfigReq) {

        Integer serviceType = realNameConfigReq.getServiceType();
        String code = realNameConfigReq.getCode();
        String value = realNameConfigReq.getValue();
        String remark = realNameConfigReq.getRemark();
        //校验参数代码是否合法
        boolean flag = checkLegal(serviceType, code);
        if (!flag) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_AUTH_CONFIG_ILLEGAL);
        }
        //查询数据库是否存在该数据
        HashMap<String, Object> configMap;
        RealNameConfigDO realNameConfigDO = realNameConfigRepository.findTopByServiceTypeOrderByIdDesc(serviceType);
        if (realNameConfigDO == null) {
            configMap = new HashMap<>();
            realNameConfigDO = new RealNameConfigDO();
            realNameConfigDO.setServiceType(serviceType);
        } else {
            configMap = realNameConfigDO.getConfig();
        }
        if(StringUtils.isNotEmpty(remark)){
            configMap.put(code, value + "|" + remark);
        }else{
            configMap.put(code, value);
        }
        realNameConfigDO.setConfig(configMap);
        realNameConfigDO.setStatus(true);
        //数据库持久化对象
        realNameConfigRepository.saveAndFlush(realNameConfigDO);
        //将其他实名验证状态改为false
        List<RealNameConfigDO> realNameConfigDOList = realNameConfigRepository.findByIdNot(realNameConfigDO.getId());
        if (!realNameConfigDOList.isEmpty()) {
            realNameConfigDOList.forEach(p -> p.setStatus(Boolean.FALSE));
            realNameConfigRepository.saveAll(realNameConfigDOList);
            realNameConfigRepository.flush();
        }
        //只有在启用的情况才需要将最新配置存入redis缓存中
        boolean status = realNameConfigDO.getStatus();
        if (status) {
            redisUtils.stringSet(RedisConstant.REDIS_REAL_NAME_CONFIG, JsonUtil.toJson(realNameConfigDO), RedisConstant.REDIS_USER_INDEX);
        }
        return true;
    }

    /**
     * 删除实名验证公共参数
     *
     * @param deleteRealNameConfigReq 请求参数
     * @return 操作结果
     */
    @Override
    public Boolean deleteConfig(DeleteRealNameConfigReq deleteRealNameConfigReq) {
        Integer serviceType = deleteRealNameConfigReq.getServiceType();
        String code = deleteRealNameConfigReq.getCode();
        //检查参数代码是否合法
        boolean flag = checkLegal(serviceType, code);
        if (!flag) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_AUTH_CONFIG_ILLEGAL);
        }
        //查询数据库是否存在该数据
        RealNameConfigDO realNameConfigDO = realNameConfigRepository.findTopByServiceTypeOrderByIdDesc(serviceType);
        if (realNameConfigDO != null) {
            HashMap<String, Object> configMap = realNameConfigDO.getConfig();
            if (configMap != null && configMap.containsKey(code)) {
                configMap.remove(code);
                realNameConfigDO.setConfig(configMap);
                //数据库持久化对象
                realNameConfigRepository.saveAndFlush(realNameConfigDO);
                //只有启用的情况才需要将最新配置存入redis缓存中
                boolean status = realNameConfigDO.getStatus();
                if (status) {
                    redisUtils.stringSet(RedisConstant.REDIS_REAL_NAME_CONFIG, JsonUtil.toJson(realNameConfigDO), RedisConstant.REDIS_USER_INDEX);
                }
                return true;
            } else {
                throw new BusinessException(ResponseCodeEnum.MC_MS_AUTH_CONFIG_CODE_NOT_EXIST);
            }
        } else {
            throw new BusinessException(ResponseCodeEnum.MC_MS_AUTH_CONFIG_NOT_EXIST);
        }
    }
    /**
     * 清空实名验证内容
     *
     * @return 是否成功
     */
    @Override
    public Boolean clearConfig() {
        realNameConfigRepository.deleteAllInBatch();
        return true;
    }

    /**
     * 校验参数代码是否正确
     *
     * @param authType 实名验证类型: 1-百度
     * @param code     配置参数名
     * @return 是否正确
     */
    private Boolean checkLegal(Integer authType, String code) {
        if (authType.equals(RealNameTypeEnum.BAIDU.getCode())) {
            return BaiDuConfigEnum.getMessage(code);
        }
        return false;
    }
}
