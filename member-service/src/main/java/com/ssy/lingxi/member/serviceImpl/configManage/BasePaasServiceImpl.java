package com.ssy.lingxi.member.serviceImpl.configManage;

import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.PasswordUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.levelRight.BaseLevelRuleDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRuleConfigDO;
import com.ssy.lingxi.member.entity.do_.menuAuth.MenuDO;
import com.ssy.lingxi.member.enums.MemberRegisterSourceEnum;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.IBaseAuthService;
import com.ssy.lingxi.member.service.base.IBaseAuthService.AuthTuple;
import com.ssy.lingxi.member.service.configManage.IBasePaasService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * （内部临时服务）系统初始化服务接口实现
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-15
 */
@Deprecated // TODO 仅仅保留了用来看初始化逻辑，后期基本使用脚本初始化，不通过代码（过一两个版本，稳定了就删除）
@Service
public class BasePaasServiceImpl implements IBasePaasService {

    private static final Logger logger = LoggerFactory.getLogger(BasePaasServiceImpl.class);

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private MemberRoleAuthConfigRepository memberRoleAuthConfigRepository;

    @Resource
    private BaseLevelRuleRepository baseLevelRuleRepository;

    @Resource
    private MemberLevelRuleConfigRepository memberLevelRuleConfigRepository;

    @Resource
    private MenuRepository menuRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberUserAuthRepository memberUserAuthRepository;

    @Resource
    private IBaseAuthService baseAuthService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;


    /**
     * 创建平台后台会员及用户
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public WrapperResp<Void> createManagePlatformAdmin() {
        MemberRoleDO memberRoleDO = memberRoleRepository.findFirstByRelType(MemberRelationTypeEnum.PLATFORM.getCode());
        if(memberRoleDO == null) {
            return WrapperUtil.success();
        }

        MemberDO memberDO = memberRepository.findPlatformMember();
        if(memberDO == null) {
            memberDO = new MemberDO();
        }
        //设置会员编码
        memberDO.setCode(MemberConstant.PLATFORM_SUPER_ADMIN_ACCOUNT);
        //设置会员名称
        memberDO.setName(MemberStringEnum.PLATFORM_SUPER_ADMIN_MEMBER_NAME.getName());
        //设置邮箱
        memberDO.setEmail("");
        //设置会员详细信息
        memberDO.setRegisterDetails(new HashSet<>());
        //设置支付密码为空
        memberDO.setPayPassword("");
        //注册时间
        memberDO.setRegisterTime(LocalDateTime.now());
        //注册来源为“管理平台”（与其他会员的区别）
        memberDO.setSource(MemberRegisterSourceEnum.FROM_MANAGE_PLATFORM.getCode());
        //头像logo
        memberDO.setLogo("");

        //手机号即账号
        memberDO.setTelCode("");
        memberDO.setPhone("");
        memberDO.setAccount(MemberConstant.PLATFORM_SUPER_ADMIN_ACCOUNT);


        //设置角色和权限
        Set<MemberRoleDO> memberRoleDOSet = new HashSet<>();
        memberRoleDOSet.add(memberRoleDO);
        memberDO.setMemberRoles(memberRoleDOSet);
        //平台会员和角色不需要等级信息、信用信息
        memberRepository.saveAndFlush(memberDO);

        //Step 2: 首先创建“超级管理员”角色 与 超级管理员用户
        UserRoleDO userRoleDO;
        if(CollectionUtils.isEmpty(memberDO.getUserRoles())) {
            userRoleDO = new UserRoleDO();
        } else {
            userRoleDO = memberDO.getUserRoles().stream().filter(r -> r.getUserType().equals(UserTypeEnum.ADMIN.getCode())).findFirst().orElse(null);
            if(userRoleDO == null) {
                userRoleDO = new UserRoleDO();
            }
        }

        userRoleDO.setRoleName(MemberStringEnum.PLATFORM_SUPER_ADMIN_MEMBER_ROLE_NAME.getName());
        userRoleDO.setUserType(UserTypeEnum.ADMIN.getCode());
        //平台后台不具有IM权限和数据权限
        userRoleDO.setHasImAuth(EnableDisableStatusEnum.DISABLE.getCode());
        userRoleDO.setAuthConfig(new HashSet<>());

        //权限设置
        userRoleDO.setRemark(MemberStringEnum.MEMBER_ADMIN_ROLE_DEFAULT_REMARK.getName());
        userRoleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userRoleDO.setMember(memberDO);
        userRoleDO = userRoleRepository.save(userRoleDO);

        if(CollectionUtils.isEmpty(memberDO.getUserRoles())) {
            Set<UserRoleDO> memberRoleDoSet = new HashSet<>();
            memberRoleDoSet.add(userRoleDO);
            memberDO.setUserRoles(memberRoleDoSet);
        } else {
            if(memberDO.getUserRoles().stream().noneMatch(r -> r.getUserType().equals(UserTypeEnum.ADMIN.getCode()))) {
                memberDO.getUserRoles().add(userRoleDO);
            }
        }

        //Step 2: 创建 超级管理员用户
        UserDO userDO;
        if(CollectionUtils.isEmpty(memberDO.getUsers())) {
            userDO = new UserDO();
            userDO.setCreateTime(LocalDateTime.now());
        } else {
            userDO = memberDO.getUsers().stream().filter(u -> Objects.equals(UserTypeEnum.ADMIN.getCode(), u.getUserType())).findFirst().orElse(null);
            if (userDO == null) {
                userDO = new UserDO();
                userDO.setCreateTime(LocalDateTime.now());
            }
        }

        userDO.setLastModifyPwdTime(LocalDateTime.now());
        userDO.setCode(memberDO.getCode());
        userDO.setAccount(memberDO.getAccount());
        userDO.setPhone(memberDO.getPhone());
        userDO.setPassword(PasswordUtil.tryEncrypt(MemberConstant.PLATFORM_ADD_MEMBER_DEFAULT_PASSWORD));
        userDO.setTelCode(memberDO.getTelCode());
        userDO.setName(memberDO.getName());
        userDO.setEmail(memberDO.getEmail());
        userDO.setIdCardNo("");
        userDO.setLogo("");
        userDO.setJobTitle(MemberStringEnum.PLATFORM_SUPER_ADMIN_JOB_TITLE.getName());
        userDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userDO.setRelType(MemberRelationTypeEnum.PLATFORM.getCode());
        userDO.setUserType(UserTypeEnum.ADMIN.getCode());
        userDO.setIsSales(Boolean.FALSE);
        userDO.setMember(memberDO);
        if(CollectionUtils.isEmpty(userDO.getRoles())) {
            Set<UserRoleDO> memberRoleSet = new HashSet<>();
            memberRoleSet.add(userRoleDO);
            userDO.setRoles(memberRoleSet);
        } else {
            if(userDO.getRoles().stream().noneMatch(r -> r.getUserType().equals(UserTypeEnum.ADMIN.getCode()))) {
                userDO.getRoles().add(userRoleDO);
            }
        }

        //权限
        MemberUserAuthDO userAuthDO = new MemberUserAuthDO();
        userAuthDO.setDataAuth(new ArrayList<>());
        userAuthDO.setChannelAuth(new ArrayList<>());
        userAuthDO.setChannels(new HashSet<>());
        userAuthDO.setUser(userDO);
        userDO.setUserAuth(userAuthDO);
        userRepository.save(userDO);

        if(CollectionUtils.isEmpty(memberDO.getUsers())) {
            Set<UserDO> userDOSet = new HashSet<>();
            userDOSet.add(userDO);
            memberDO.setUsers(userDOSet);
        } else {
            if(memberDO.getUsers().stream().noneMatch(u -> Objects.equals(UserTypeEnum.ADMIN.getCode(), u.getUserType()))) {
                memberDO.getUsers().add(userDO);
            }
        }

        memberRepository.saveAndFlush(memberDO);

        //创建平台会员的升级规则
        List<BaseLevelRuleDO> baseLevelRuleDOList = baseLevelRuleRepository.findAll().stream().filter(rule -> rule.getMemberLevelTypeEnum().equals(MemberLevelTypeEnum.PLATFORM.getCode()) && rule.getStatus().equals(EnableDisableStatusEnum.ENABLE.getCode())).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(baseLevelRuleDOList)) {
            List<MemberLevelRuleConfigDO> memberLevelRuleConfigDOList = memberLevelRuleConfigRepository.findByMemberIdAndRoleId(memberDO.getId(), memberRoleDO.getId());
            for (BaseLevelRuleDO baseLevelRuleDO : baseLevelRuleDOList) {
                MemberLevelRuleConfigDO memberLevelRuleConfigDO = memberLevelRuleConfigDOList.stream().filter(config -> config.getRule().getId().equals(baseLevelRuleDO.getId())).findFirst().orElse(null);
                if(memberLevelRuleConfigDO == null) {
                    memberLevelRuleConfigDO = new MemberLevelRuleConfigDO();
                    memberLevelRuleConfigDO.setMemberId(memberDO.getId());
                    memberLevelRuleConfigDO.setRoleId(memberRoleDO.getId());
                    memberLevelRuleConfigDO.setRule(baseLevelRuleDO);
                    memberLevelRuleConfigDO.setScore(new BigDecimal(0));
                    memberLevelRuleConfigDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
                    memberLevelRuleConfigRepository.saveAndFlush(memberLevelRuleConfigDO);
                }
            }
        }

        return WrapperUtil.success();
    }

    /**
     * 系统初始化时，创建“平台”角色、平台后台管理员账号
     */
    @Override
    public void initPlatformAdmin() {
        // 获取菜单和按钮权限
        AuthTuple<byte[], byte[]> authTuple =  baseAuthService.getMenuAndButtonAuth(menuRepository.findBySource(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode()));

        MemberRoleDO memberRoleDO = new MemberRoleDO();
        memberRoleDO.setRoleName(MemberStringEnum.PLATFORM_ROLE_NAME.getName());
        memberRoleDO.setRelType(MemberRelationTypeEnum.PLATFORM.getCode());
        memberRoleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        memberRoleDO.setMemberType(MemberTypeEnum.MERCHANT.getCode());
        memberRoleDO.setRoleType(RoleTypeEnum.SERVICE_PROVIDER.getCode());
        memberRoleDO.setRoleTag(RoleTagEnum.MEMBER.getCode());
        memberRoleDO.setMenuAuth(authTuple.getMenuAuth());
        memberRoleDO.setButtonAuth(authTuple.getButtonAuth());
        memberRoleDO.setApiAuth(BitMapUtil.emptyByteArray());
        memberRoleDO.setMembers(new HashSet<>());
        memberRoleDO.setConfigs(new HashSet<>());
        memberRoleRepository.saveAndFlush(memberRoleDO);

        MemberDO memberDO = new MemberDO();
        memberDO.setName(MemberStringEnum.PLATFORM_SUPER_ADMIN_MEMBER_NAME.getName());
        memberDO.setCode(MemberConstant.PLATFORM_SUPER_ADMIN_ACCOUNT);
        memberDO.setEmail("");
        memberDO.setRegisterDetails(new HashSet<>());
        memberDO.setPayPassword("");
        memberDO.setRegisterTime(LocalDateTime.now());
        //注册来源为“管理平台”（与其他会员的区别）
        memberDO.setSource(MemberRegisterSourceEnum.FROM_MANAGE_PLATFORM.getCode());
        memberDO.setLogo("");
        memberDO.setTelCode("");
        memberDO.setPhone("");
        memberDO.setAccount(MemberConstant.PLATFORM_SUPER_ADMIN_ACCOUNT);
        //设置角色
        Set<MemberRoleDO> memberRoleDOSet = new HashSet<>();
        memberRoleDOSet.add(memberRoleDO);
        memberDO.setMemberRoles(memberRoleDOSet);
        memberRepository.saveAndFlush(memberDO);

        //Step 2: 首先创建“超级管理员”角色 与 超级管理员用户
        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setRoleName(MemberStringEnum.PLATFORM_SUPER_ADMIN_MEMBER_ROLE_NAME.getName());
        userRoleDO.setUserType(UserTypeEnum.ADMIN.getCode());
        userRoleDO.setMenuAuth(authTuple.getMenuAuth());
        userRoleDO.setButtonAuth(authTuple.getButtonAuth());
        userRoleDO.setApiAuth(BitMapUtil.emptyByteArray());
        //平台后台不具有IM权限和数据权限
        userRoleDO.setHasImAuth(EnableDisableStatusEnum.DISABLE.getCode());
        userRoleDO.setAuthConfig(new HashSet<>());
        userRoleDO.setRemark(MemberStringEnum.MEMBER_ADMIN_ROLE_DEFAULT_REMARK.getName());
        userRoleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userRoleDO.setMember(memberDO);
        userRoleRepository.save(userRoleDO);

        memberDO.setUserRoles(new HashSet<>(Stream.of(userRoleDO).collect(Collectors.toList())));

        //Step 2: 创建 超级管理员用户
        UserDO userDO = new UserDO();
        userDO.setCode(MemberConstant.PLATFORM_SUPER_ADMIN_ACCOUNT);
        userDO.setCreateTime(LocalDateTime.now());
        userDO.setLastModifyPwdTime(LocalDateTime.now());
        userDO.setAccount(memberDO.getAccount());
        userDO.setPhone(memberDO.getPhone());
        userDO.setPassword(PasswordUtil.tryEncrypt(MemberConstant.PLATFORM_ADD_MEMBER_DEFAULT_PASSWORD));
        userDO.setTelCode(memberDO.getTelCode());
        userDO.setName(memberDO.getName());
        userDO.setEmail(memberDO.getEmail());
        userDO.setIdCardNo("");
        userDO.setLogo("");
        userDO.setJobTitle(MemberStringEnum.PLATFORM_SUPER_ADMIN_JOB_TITLE.getName());
        userDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userDO.setRelType(MemberRelationTypeEnum.PLATFORM.getCode());
        userDO.setUserType(UserTypeEnum.ADMIN.getCode());
        userDO.setIsSales(Boolean.FALSE);
        userDO.setMember(memberDO);
        userDO.setRoles(new HashSet<>(Stream.of(userRoleDO).collect(Collectors.toList())));

        //权限
        MemberUserAuthDO userAuthDO = new MemberUserAuthDO();
        userAuthDO.setDataAuth(new ArrayList<>());
        userAuthDO.setChannelAuth(new ArrayList<>());
        userAuthDO.setChannels(new HashSet<>());
        userAuthDO.setUser(userDO);
        userDO.setUserAuth(userAuthDO);
        userRepository.save(userDO);

        memberDO.setUsers(new HashSet<>(Stream.of(userDO).collect(Collectors.toList())));
        memberRepository.saveAndFlush(memberDO);

        //创建平台会员的升级规则
        List<BaseLevelRuleDO> baseLevelRuleDOList = baseLevelRuleRepository.findByMemberLevelTypeEnumAndStatus(MemberLevelTypeEnum.PLATFORM.getCode(), EnableDisableStatusEnum.ENABLE.getCode());
        List<MemberLevelRuleConfigDO> memberLevelRuleConfigList = baseLevelRuleDOList.stream().map(baseLevelRuleDO -> {
            MemberLevelRuleConfigDO configDO = new MemberLevelRuleConfigDO();
            configDO.setMemberId(memberDO.getId());
            configDO.setRoleId(memberRoleDO.getId());
            configDO.setRule(baseLevelRuleDO);
            configDO.setScore(new BigDecimal(0));
            configDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
            return configDO;
        }).collect(Collectors.toList());
        memberLevelRuleConfigRepository.saveAll(memberLevelRuleConfigList);
    }

    /**
     * 禁用菜单可配置数据权限时，删除已有的数据权限配置
     * @param menuId 菜单Id
     * @param path 菜单path
     * @param source 菜单Source
     */
    @Async
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void removeDataAuthAsync(Long menuId, String path, Integer source) {
        try {
            Specification<MemberRoleAuthConfigDO> specification = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();

                Join<Object, Object> menuJoin = root.join("menu", JoinType.LEFT);
                list.add(criteriaBuilder.equal(menuJoin.get("id").as(Long.class), menuId));
                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };

            List<MemberRoleAuthConfigDO> authConfigs = memberRoleAuthConfigRepository.findAll(specification);

            if(CollectionUtils.isEmpty(authConfigs)) {
                return;
            }

            //删除所有用户数据权限中的此菜单
            Specification<UserDO> specUser = (root, query, criteriaBuilder) -> {
                List<Predicate> list = new ArrayList<>();

                Join<UserDO, UserRoleDO> roleJoin = root.join("memberRoles", JoinType.LEFT);
                Join<UserRoleDO, MemberRoleAuthConfigDO> authJoin = roleJoin.join("authConfig", JoinType.LEFT);
                Join<MemberRoleAuthConfigDO, MenuDO> menuJoin = authJoin.join("menu", JoinType.LEFT);
                list.add(criteriaBuilder.equal(menuJoin.get("id").as(Long.class), menuId));
                Predicate[] p = new Predicate[list.size()];
                return criteriaBuilder.and(list.toArray(p));
            };

            List<UserDO> userDOList = userRepository.findAll(specUser);
            if(!CollectionUtils.isEmpty(userDOList)) {
                List<MemberUserAuthDO> memberUserAuthList = memberUserAuthRepository.findByUserIn(userDOList);
                memberUserAuthList.forEach(memberUserAuth -> memberUserAuth.getDataAuth().removeIf(dataAuthBO -> dataAuthBO.getPath().equals(path) && dataAuthBO.getSource().equals(source)));

                memberUserAuthRepository.saveAll(memberUserAuthList);
            }

            //删除此菜单
            for (MemberRoleAuthConfigDO authConfig : authConfigs) {
                authConfig.getOrgs().clear();
                memberRoleAuthConfigRepository.saveAndFlush(authConfig);
            }

            memberRoleAuthConfigRepository.deleteAll(authConfigs);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            logger.error("Paas修改数据权限错误", e);
        }
    }
}
