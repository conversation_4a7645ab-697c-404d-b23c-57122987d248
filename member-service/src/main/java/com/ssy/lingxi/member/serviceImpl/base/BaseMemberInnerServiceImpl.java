package com.ssy.lingxi.member.serviceImpl.base;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.BitMapUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.entity.bo.*;
import com.ssy.lingxi.member.entity.do_.basic.*;
import com.ssy.lingxi.member.entity.do_.detail.MemberDepositoryDetailDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberQualityDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberReceiveInvitationDO;
import com.ssy.lingxi.member.entity.do_.invitation.MemberSendInvitationDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberCreditDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelConfigDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRightDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberAfterApprovalLifecycleStagesDO;
import com.ssy.lingxi.member.entity.do_.lifecycle.MemberLifecycleStagesDO;
import com.ssy.lingxi.member.entity.do_.validate.MemberValidateTaskDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.req.validate.MemberQualityReq;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.repository.comment.MemberTradeCommentHistoryRepository;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.ILogisticsFeignService;
import com.ssy.lingxi.member.service.feign.IMessageFeignService;
import com.ssy.lingxi.member.service.feign.IPayFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IMemberLevelConfigService;
import com.ssy.lingxi.member.service.web.IMemberProcessRuleService;
import com.ssy.lingxi.member.service.web.IMemberReceiveInvitationService;
import com.ssy.lingxi.member.service.commission.IInvitationCommissionService;
import com.ssy.lingxi.member.util.CodeUtil;
import com.ssy.lingxi.support.api.feign.ITencentIMFeign;
import com.ssy.lingxi.support.api.model.req.ImportIMAccountReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 会员、用户内部服务类实现
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-08-06
 */
@Slf4j
@Service
public class BaseMemberInnerServiceImpl implements IBaseMemberInnerService {
    @Resource
    private MemberRepository memberRepository;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private MemberRegisterDetailRepository memberRegisterDetailRepository;

    @Resource
    private MemberDepositoryDetailRepository memberDepositoryDetailRepository;

    @Resource
    private MemberQualityRepository memberQualityRepository;

    @Resource
    private UserRoleRepository userRoleRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private IBaseMemberDepositDetailService baseMemberDepositDetailService;

    @Resource
    private IBaseMemberQualificationService baseMemberQualificationService;

    @Resource
    private MemberLevelConfigRepository memberLevelConfigRepository;

    @Resource
    private MemberLevelRuleConfigRepository memberLevelRuleConfigRepository;

    @Resource
    private MemberRightConfigRepository memberRightConfigRepository;

    @Resource
    private MemberLevelRightRepository memberLevelRightRepository;

    @Resource
    private MemberCreditRepository memberCreditRepository;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private MemberAfterSaleHistoryRepository memberAfterSaleHistoryRepository;

    @Resource
    private MemberComplainHistoryRepository memberComplainHistoryRepository;

    @Resource
    private MemberTradeCommentHistoryRepository memberTradeCommentHistoryRepository;

    @Resource
    private MemberLevelHistoryRepository memberLevelHistoryRepository;

    @Resource
    private MemberRightHistoryRepository memberRightHistoryRepository;

    @Resource
    private MemberRightSpendHistoryRepository memberRightSpendHistoryRepository;

    @Resource
    private MemberOrganizationRepository memberOrganizationRepository;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private IMemberProcessRuleService memberProcessRuleService;

    @Resource
    private IMemberLevelConfigService memberLevelConfigService;

    @Resource
    private IWorkflowFeignService workflowFeignService;

    @Resource
    private IMessageFeignService messageFeignService;

    @Resource
    private IPayFeignService payFeignService;

    @Resource
    private ILogisticsFeignService logisticsFeignService;

    @Resource
    private MemberReceiveInvitationRepository memberReceiveInvitationRepository;

    @Resource
    private IMemberReceiveInvitationService memberReceiveInvitationService;

    @Resource
    private MemberSendInvitationRepository memberSendInvitationRepository;

    @Resource
    private MemberAfterApprovalLifecycleStagesRepository memberAfterApprovalLifecycleStagesRepository;

    @Resource
    private MemberLifecycleStagesRepository memberLifecycleStagesRepository;

    @Resource
    private IBaseAuthService baseAuthService;

    @Resource
    private IBaseTokenManageService tokenManageService;

    @Resource
    private ITencentIMFeign tencentIMFeign;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    @Resource
    private IInvitationCommissionService invitationCommissionService;

    /**
     * 新增会员
     * @param memberBO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public MemberRelationDO addPlatformMember(AddMemberBO memberBO, Integer roleTag) {
        //新增会员
        MemberDO memberDO = new MemberDO();
        //注册时间
        memberDO.setRegisterTime(LocalDateTime.now());
        //设置会员编码
        memberDO.setCode(memberBO.getCode());
        //设置会员名称
        memberDO.setName(memberBO.getName());
        //设置邮箱
        memberDO.setEmail(memberBO.getEmail());
        //设置支付密码为空
        memberDO.setPayPassword("");
        //注册来源
        memberDO.setSource(memberBO.getSource().getCode());
        //会员头像
        memberDO.setLogo("");
        //通过注册页面的都是商家，手机号即账号
        memberDO.setTelCode(memberBO.getTelCode());
        memberDO.setPhone(memberBO.getPhone());
        memberDO.setAccount(Optional.ofNullable(memberBO.getAccount()).orElse(memberBO.getPhone()));
        //设置角色和权限
        Set<MemberRoleDO> memberRoleDOSet = new HashSet<>();
        memberRoleDOSet.add(memberBO.getMemberRoleDO());
        memberDO.setMemberRoles(memberRoleDOSet);

        if (Objects.nonNull(memberBO.getDataSource())) {
            memberDO.setDataSource(memberBO.getDataSource());
        }

        //由于关联关系是CascadeType.DETACH，所以要先保存一下注册信息，再更新
        List<MemberRegisterDetailDO> memberRegisterDetails = memberBO.getRegisterDetails();
        memberRegisterDetailRepository.saveAll(memberRegisterDetails);
        // 兼容list字段，修改成循环保存
        for (MemberRegisterDetailDO memberRegisterDetail : memberRegisterDetails) {
//            memberRegisterDetailRepository.save(memberRegisterDetail);
            if (!CollectionUtils.isEmpty(memberRegisterDetail.getRegisters())) {
                List<MemberRegisterDetailDO> registers = memberRegisterDetail.getRegisters();
                registers.forEach(register -> register.setParentId(memberRegisterDetail.getId()));
                memberRegisterDetailRepository.saveAll(registers);
            }
        }

        memberDO.setRegisterDetails(new HashSet<>(memberRegisterDetails));
        memberRepository.saveAndFlush(memberDO);

        memberRegisterDetails.forEach(memberRegisterDetailDO -> {
            memberRegisterDetailDO.setMember(memberDO);
            // 列表子字段
            List<MemberRegisterDetailDO> registers = memberRegisterDetailDO.getRegisters();
            //如果是“无需审核”流程，将注册资料版本设置为“正在使用”
            if(memberBO.getPlatformProcess().getEmptyProcess()) {
                memberRegisterDetailDO.setVersion(MemberDetailVersionEnum.USING.getCode());
            }
            if (!CollectionUtils.isEmpty(registers)) {
                // 子字段绑定父id, 不与memberId绑定
//                registers.forEach(memberRegisterDetail -> memberRegisterDetail.setMember(memberDO));
                if(memberBO.getPlatformProcess().getEmptyProcess()) {
                    registers.forEach(memberRegisterDetail -> memberRegisterDetail.setVersion(MemberDetailVersionEnum.USING.getCode()));
                }
                memberRegisterDetailRepository.saveAll(memberRegisterDetailDO.getRegisters());
            }
        });

        memberRegisterDetailRepository.saveAll(memberRegisterDetails);

        //第二步骤：创建上下级关系，以及审核状态
        MemberRelationDO relationDO = new MemberRelationDO();
        relationDO.setCreateTime(LocalDateTime.now());
        relationDO.setMemberId(memberBO.getUpperMember().getId());
        relationDO.setMember(memberBO.getUpperMember());
        relationDO.setRoleId(memberBO.getUpperRole().getId());
        relationDO.setRole(memberBO.getUpperRole());
        relationDO.setRoleTag(Optional.ofNullable(memberBO.getUpperRole().getRoleTag()).orElse(0));
        relationDO.setSubMemberId(memberDO.getId());
        relationDO.setSubMember(memberDO);
        relationDO.setSubMemberTypeEnum(memberBO.getMemberTypeEnum().getCode());
        //上级为平台，所以等级类型为平台会员
        relationDO.setSubMemberLevelTypeEnum(MemberLevelTypeEnum.PLATFORM.getCode());
        relationDO.setSubRoleId(memberBO.getMemberRoleDO().getId());
        relationDO.setSubRole(memberBO.getMemberRoleDO());
        relationDO.setSubRoleTag(Optional.ofNullable(memberBO.getMemberRoleDO().getRoleTag()).orElse(0));
        relationDO.setSubRoleName(memberBO.getMemberRoleDO().getRoleName());
        relationDO.setRelType(MemberRelationTypeEnum.PLATFORM.getCode());
        relationDO.setRelSource(MemberRelationSourceEnum.parseFromRegisterSource(memberBO.getSource()).getCode());
        //初始的审核状态、外部状态
        relationDO.setVerified(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode());
        relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode());

        relationDO.setStatus(MemberStatusEnum.NORMAL.getCode());
        relationDO.setValidateMsg("");

        //权限
        relationDO.setMenuAuth(memberBO.getMemberRoleDO().getMenuAuth());
        relationDO.setButtonAuth(memberBO.getMemberRoleDO().getButtonAuth());
        relationDO.setApiAuth(memberBO.getMemberRoleDO().getApiAuth());

        //由于工作流需要dataId（不能为空或0），所以要先保存一下MemberRelationDO
        relationRepository.saveAndFlush(relationDO);

        //第二步骤：调用工作流模块
        String taskId = "";
        Integer innerStatus = memberBO.getInnerStatus();
        if(!memberBO.getInnerStatus().equals(PlatformInnerStatusEnum.REGISTERING.getCode())) {
            //如果是无需审核流程，直接设置最终状态
            if(memberBO.getPlatformProcess().getEmptyProcess()) {
                innerStatus = PlatformInnerStatusEnum.VERIFY_PASSED.getCode();
                relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode());
                relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
                relationDO.setDepositTime(LocalDateTime.now());
            } else {
                //内部状态从工作流模块获得，内部审核流程使用memberId，roleId两个参数
                WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(memberBO.getPlatformProcess().getProcessKey(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId());
                taskId = taskResult.getTaskId();
                innerStatus = taskResult.getInnerStatus();

                //最终审核状态
                if (!StringUtils.hasLength(taskId)) {
                    relationDO.setOuterStatus(innerStatus.equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode()) ? MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode() : MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode());
                    relationDO.setVerified(innerStatus.equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode()) ? MemberValidateStatusEnum.VERIFY_PASSED.getCode() : MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode());
                    relationDO.setDepositTime(innerStatus.equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode()) ? LocalDateTime.now() : null);
                }
            }
        }

        //内部状态、工作流任务
        relationDO.setInnerStatus(innerStatus);
        relationDO.setValidateTask(new MemberValidateTaskDO(taskId, memberBO.getPlatformProcess().getProcessKey(), MemberProcessTypeEnum.PLATFORM_VALIDATION.getCode()));

        relationRepository.saveAndFlush(relationDO);

        //如果level是Null，查询最小等级；否则查询指定等级
        MemberLevelConfigDO upperMemberLevelConfigDO;
        if(NumberUtil.isNullOrZero(memberBO.getLevel())) {
            upperMemberLevelConfigDO = baseMemberLevelConfigService.findFirstLevel(relationDO);
        } else {
            upperMemberLevelConfigDO = baseMemberLevelConfigService.findLevel(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleId(), memberBO.getLevel());
        }

        MemberLevelRightDO levelRightDO = new MemberLevelRightDO();
        levelRightDO.setMemberId(memberBO.getUpperMember().getId());
        levelRightDO.setRoleId(memberBO.getUpperRole().getId());
        levelRightDO.setSubMemberId(memberDO.getId());
        levelRightDO.setSubRoleId(memberBO.getMemberRoleDO().getId());
        levelRightDO.setLevel(upperMemberLevelConfigDO == null ? 0 : upperMemberLevelConfigDO.getLevel());
        levelRightDO.setLevelTag(upperMemberLevelConfigDO == null ? "" : upperMemberLevelConfigDO.getLevelTag());
        levelRightDO.setLevelConfig(upperMemberLevelConfigDO);
        levelRightDO.setScore(0);
        levelRightDO.setCurrentPoint(0);
        levelRightDO.setSumPoint(0);
        levelRightDO.setSumReturnMoney(new BigDecimal(0));
        levelRightDO.setSumUsedPoint(0);
        levelRightDO.setRelation(relationDO);

        //设置信用信息
        MemberCreditDO creditDO = new MemberCreditDO();
        creditDO.setMemberId(memberBO.getUpperMember().getId());
        creditDO.setRoleId(memberBO.getUpperRole().getId());
        creditDO.setSubMemberId(memberDO.getId());
        creditDO.setSubRoleId(memberBO.getMemberRoleDO().getId());
        creditDO.setAfterSaleCommentPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setCreditPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setRegisterYearsPoint(0);
        creditDO.setTradeCommentPoint(0);
        creditDO.setRelation(relationDO);
        creditDO.setRegisterPointUpdateTime(LocalDateTime.now());
        creditDO.setTradeCommentStars(0);
        creditDO.setTradeCommentCount(0);
        creditDO.setAvgTradeCommentStar(MemberConstant.DEFAULT_TRADE_COMMENT_STAR);

        //关联到会员关系
        relationDO.setLevelRight(levelRightDO);
        relationDO.setCredit(creditDO);
        relationRepository.saveAndFlush(relationDO);

        //第四步骤: 创建“超级管理员”角色 与 超级管理员用户
        //由于注册后就可以登录首页，所以注册的同时要创建用户
        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setRoleName(UserTypeEnum.ADMIN.getName());
        userRoleDO.setUserType(UserTypeEnum.ADMIN.getCode());
        //默认具有IM即时通信权限
        userRoleDO.setHasImAuth(EnableDisableStatusEnum.ENABLE.getCode());
        //会员超级管理员角色不需要设置数据权限
        userRoleDO.setAuthConfig(new HashSet<>());
        //菜单权限设置
        userRoleDO.setMenuAuth(memberBO.getMemberRoleDO().getMenuAuth());
        userRoleDO.setButtonAuth(memberBO.getMemberRoleDO().getButtonAuth());
        userRoleDO.setApiAuth(memberBO.getMemberRoleDO().getApiAuth());
        userRoleDO.setRemark(MemberStringEnum.MEMBER_ADMIN_ROLE_DEFAULT_REMARK.getName());
        userRoleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userRoleDO.setMember(memberDO);
        userRoleRepository.saveAndFlush(userRoleDO);

        Set<UserRoleDO> userRoleDOSet = new HashSet<>();
        userRoleDOSet.add(userRoleDO);
        memberDO.setUserRoles(userRoleDOSet);

        //Step 2: 创建 超级管理员用户
        UserDO userDO = new UserDO();
        userDO.setCreateTime(LocalDateTime.now());
        userDO.setLastModifyPwdTime(LocalDateTime.now());
        userDO.setCode(memberDO.getCode());
        userDO.setAccount(memberDO.getAccount());
        userDO.setPhone(memberDO.getPhone());
        userDO.setPassword(memberBO.getPassword());
        userDO.setTelCode(memberDO.getTelCode());
        userDO.setName(memberDO.getName());
        userDO.setEmail(memberDO.getEmail());
        userDO.setIdCardNo("");
        userDO.setJobTitle(MemberStringEnum.PLATFORM_SUPER_ADMIN_JOB_TITLE.getName());
        userDO.setLogo("");
        userDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userDO.setRelType(MemberRelationTypeEnum.OTHER.getCode());
        userDO.setUserType(UserTypeEnum.ADMIN.getCode());
        userDO.setIsSales(Boolean.FALSE);

        // 生成唯一的邀请码
        String invitationCode = generateUniqueInvitationCode();
        userDO.setInvitationCode(invitationCode);
        userDO.setMember(memberDO);
        userDO.setRoles(userRoleDOSet);
        userDO.setMenuAuth(memberBO.getMemberRoleDO().getMenuAuth());
        userDO.setButtonAuth(memberBO.getMemberRoleDO().getButtonAuth());
        userDO.setApiAuth(memberBO.getMemberRoleDO().getApiAuth());
        //超级管理员用户不需要设置数据权限、渠道权限
        MemberUserAuthDO userAuthDO = new MemberUserAuthDO();
        userAuthDO.setUser(userDO);
        userAuthDO.setDataAuth(new ArrayList<>());
        userAuthDO.setChannelAuth(new ArrayList<>());
        userAuthDO.setChannels(new HashSet<>());
        userDO.setUserAuth(userAuthDO);

        userRepository.saveAndFlush(userDO);

        // 为新用户创建分佣账户
        try {
            invitationCommissionService.createCommissionAccountForUser(userDO.getId());
        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.warn("为用户 {} 创建分佣账户失败: {}", userDO.getId(), e.getMessage());
        }

        Set<UserDO> userDOSet = new HashSet<>();
        userDOSet.add(userDO);
        memberDO.setUsers(userDOSet);
        memberRepository.saveAndFlush(memberDO);

        //创建会员的升级规则配置
        memberLevelConfigService.createMemberLevelRuleConfig(relationDO.getSubMemberId(), relationDO.getSubRoleId());

        //第六步骤：“注册”的审核记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, memberBO.getOperatorRoleName(), MemberValidateHistoryOperationEnum.REGISTER, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");

        //第七步骤：异步实时消息服务、支付服务
        messageFeignService.sendMemberValidateMessage(relationDO, roleTag);
        payFeignService.notifyMemberAssetAccount(relationDO);
        payFeignService.notifyMemberCredit(relationDO);
        logisticsFeignService.initMemberLogisticsAsync(relationDO);

        //超管默认注册im
        CompletableFuture.runAsync(() -> tencentIMFeign.importAccount(new ImportIMAccountReq(userDO.getId(), userDO.getName(), userDO.getLogo())), ThreadPoolConfig.asyncDefaultExecutor);

        return relationDO;
    }

    /**
     * 平台新增会员异步实时消息服务、支付服务
     * @param memberRelationDOList 会员上下级关系列表
     */
    @Override
    public void addPlatformMemberInform(List<MemberRelationDO> memberRelationDOList) {
        memberRelationDOList.forEach(relationDO -> {
            messageFeignService.sendMemberValidateMessage(relationDO, RoleTagEnum.MEMBER.getCode());
            payFeignService.notifyMemberAssetAccount(relationDO);
            payFeignService.notifyMemberCredit(relationDO);
            logisticsFeignService.initMemberLogisticsAsync(relationDO);
        });
    }

    /**
     * 新增会员
     * @param memberBO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public MemberRelationDO addPlatformMembers(AddMemberBO memberBO) {
        //新增会员
        MemberDO memberDO = new MemberDO();
        //注册时间
        memberDO.setRegisterTime(LocalDateTime.now());
        //设置会员编码
        memberDO.setCode(memberBO.getCode());
        //设置会员名称
        memberDO.setName(memberBO.getName());
        //设置邮箱
        memberDO.setEmail(memberBO.getEmail());
        //设置支付密码为空
        memberDO.setPayPassword("");
        //注册来源
        memberDO.setSource(memberBO.getSource().getCode());
        //会员头像
        memberDO.setLogo("");
        //通过注册页面的都是商家，手机号即账号
        memberDO.setTelCode(memberBO.getTelCode());
        memberDO.setPhone(memberBO.getPhone());
        memberDO.setAccount(memberBO.getPhone());
        //设置角色和权限
        Set<MemberRoleDO> memberRoleDOSet = new HashSet<>();
        memberRoleDOSet.add(memberBO.getMemberRoleDO());
        memberDO.setMemberRoles(memberRoleDOSet);

        //由于关联关系是CascadeType.DETACH，所以要先保存一下注册信息，再更新
        List<MemberRegisterDetailDO> memberRegisterDetails = memberBO.getRegisterDetails();
        memberRegisterDetailRepository.saveAll(memberRegisterDetails);

        memberDO.setRegisterDetails(new HashSet<>(memberRegisterDetails));
        memberRepository.saveAndFlush(memberDO);

        memberRegisterDetails.forEach(memberRegisterDetailDO -> {
            memberRegisterDetailDO.setMember(memberDO);
            //如果是“无需审核”流程，将注册资料版本设置为“正在使用”
            if(memberBO.getPlatformProcess().getEmptyProcess()) {
                memberRegisterDetailDO.setVersion(MemberDetailVersionEnum.USING.getCode());
            }
        });

        memberRegisterDetailRepository.saveAll(memberRegisterDetails);

        //第二步骤：创建上下级关系，以及审核状态
        MemberRelationDO relationDO = new MemberRelationDO();
        relationDO.setCreateTime(LocalDateTime.now());
        relationDO.setMemberId(memberBO.getUpperMember().getId());
        relationDO.setMember(memberBO.getUpperMember());
        relationDO.setRoleId(memberBO.getUpperRole().getId());
        relationDO.setRole(memberBO.getUpperRole());
        relationDO.setRoleTag(Optional.ofNullable(memberBO.getUpperRole().getRoleTag()).orElse(0));
        relationDO.setSubMemberId(memberDO.getId());
        relationDO.setSubMember(memberDO);
        relationDO.setSubMemberTypeEnum(memberBO.getMemberTypeEnum().getCode());
        //上级为平台，所以等级类型为平台会员
        relationDO.setSubMemberLevelTypeEnum(MemberLevelTypeEnum.PLATFORM.getCode());
        relationDO.setSubRoleId(memberBO.getMemberRoleDO().getId());
        relationDO.setSubRole(memberBO.getMemberRoleDO());
        relationDO.setSubRoleName(memberBO.getMemberRoleDO().getRoleName());
        relationDO.setSubRoleTag(Optional.ofNullable(memberBO.getMemberRoleDO().getRoleTag()).orElse(0));
        relationDO.setRelType(MemberRelationTypeEnum.PLATFORM.getCode());
        relationDO.setRelSource(MemberRelationSourceEnum.parseFromRegisterSource(memberBO.getSource()).getCode());
        //初始的审核状态、外部状态
        relationDO.setVerified(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode());
        relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode());

        relationDO.setStatus(MemberStatusEnum.NORMAL.getCode());
        relationDO.setValidateMsg("");

        //权限
        relationDO.setMenuAuth(memberBO.getMemberRoleDO().getMenuAuth());
        relationDO.setButtonAuth(memberBO.getMemberRoleDO().getButtonAuth());
        relationDO.setApiAuth(memberBO.getMemberRoleDO().getApiAuth());

        //由于工作流需要dataId（不能为空或0），所以要先保存一下MemberRelationDO
        relationRepository.saveAndFlush(relationDO);

        //第二步骤：调用工作流模块
        String taskId = "";
        Integer innerStatus = memberBO.getInnerStatus();
        if(!memberBO.getInnerStatus().equals(PlatformInnerStatusEnum.REGISTERING.getCode())) {
            //如果是无需审核流程，直接设置最终状态
            if(memberBO.getPlatformProcess().getEmptyProcess()) {
                innerStatus = PlatformInnerStatusEnum.VERIFY_PASSED.getCode();
                relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode());
                relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
                relationDO.setDepositTime(LocalDateTime.now());
            } else {
                //内部状态从工作流模块获得，内部审核流程使用memberId，roleId两个参数
                WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(memberBO.getPlatformProcess().getProcessKey(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId());
                taskId = taskResult.getTaskId();
                innerStatus = taskResult.getInnerStatus();

                //最终审核状态
                if (!StringUtils.hasLength(taskId)) {
                    relationDO.setOuterStatus(innerStatus.equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode()) ? MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode() : MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode());
                    relationDO.setVerified(innerStatus.equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode()) ? MemberValidateStatusEnum.VERIFY_PASSED.getCode() : MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode());
                    relationDO.setDepositTime(innerStatus.equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode()) ? LocalDateTime.now() : null);
                }
            }
        }

        //内部状态、工作流任务
        relationDO.setInnerStatus(innerStatus);
        relationDO.setValidateTask(new MemberValidateTaskDO(taskId, memberBO.getPlatformProcess().getProcessKey(), MemberProcessTypeEnum.PLATFORM_VALIDATION.getCode()));

        relationRepository.saveAndFlush(relationDO);

        //如果level是Null，查询最小等级；否则查询指定等级
        MemberLevelConfigDO upperMemberLevelConfigDO;
        if(NumberUtil.isNullOrZero(memberBO.getLevel())) {
            upperMemberLevelConfigDO = baseMemberLevelConfigService.findFirstLevel(relationDO);
        } else {
            upperMemberLevelConfigDO = baseMemberLevelConfigService.findLevel(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleId(), memberBO.getLevel());
        }

        MemberLevelRightDO levelRightDO = new MemberLevelRightDO();
        levelRightDO.setMemberId(memberBO.getUpperMember().getId());
        levelRightDO.setRoleId(memberBO.getUpperRole().getId());
        levelRightDO.setSubMemberId(memberDO.getId());
        levelRightDO.setSubRoleId(memberBO.getMemberRoleDO().getId());
        levelRightDO.setLevel(upperMemberLevelConfigDO == null ? 0 : upperMemberLevelConfigDO.getLevel());
        levelRightDO.setLevelTag(upperMemberLevelConfigDO == null ? "" : upperMemberLevelConfigDO.getLevelTag());
        levelRightDO.setLevelConfig(upperMemberLevelConfigDO);
        levelRightDO.setScore(0);
        levelRightDO.setCurrentPoint(0);
        levelRightDO.setSumPoint(0);
        levelRightDO.setSumReturnMoney(new BigDecimal(0));
        levelRightDO.setSumUsedPoint(0);
        levelRightDO.setRelation(relationDO);

        //设置信用信息
        MemberCreditDO creditDO = new MemberCreditDO();
        creditDO.setMemberId(memberBO.getUpperMember().getId());
        creditDO.setRoleId(memberBO.getUpperRole().getId());
        creditDO.setSubMemberId(memberDO.getId());
        creditDO.setSubRoleId(memberBO.getMemberRoleDO().getId());
        creditDO.setAfterSaleCommentPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setCreditPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setRegisterYearsPoint(0);
        creditDO.setTradeCommentPoint(0);
        creditDO.setRelation(relationDO);
        creditDO.setRegisterPointUpdateTime(LocalDateTime.now());
        creditDO.setTradeCommentStars(0);
        creditDO.setTradeCommentCount(0);
        creditDO.setAvgTradeCommentStar(MemberConstant.DEFAULT_TRADE_COMMENT_STAR);

        //关联到会员关系
        relationDO.setLevelRight(levelRightDO);
        relationDO.setCredit(creditDO);
        relationRepository.saveAndFlush(relationDO);

        //第四步骤: 创建“超级管理员”角色 与 超级管理员用户
        //由于注册后就可以登录首页，所以注册的同时要创建用户
        UserRoleDO userRoleDO = new UserRoleDO();
        userRoleDO.setRoleName(UserTypeEnum.ADMIN.getName());
        userRoleDO.setUserType(UserTypeEnum.ADMIN.getCode());
        //默认具有IM即时通信权限
        userRoleDO.setHasImAuth(EnableDisableStatusEnum.ENABLE.getCode());
        //会员超级管理员角色不需要设置数据权限
        userRoleDO.setAuthConfig(new HashSet<>());
        //菜单权限设置
        userRoleDO.setMenuAuth(memberBO.getMemberRoleDO().getMenuAuth());
        userRoleDO.setButtonAuth(memberBO.getMemberRoleDO().getButtonAuth());
        userRoleDO.setApiAuth(memberBO.getMemberRoleDO().getApiAuth());
        userRoleDO.setRemark(MemberStringEnum.MEMBER_ADMIN_ROLE_DEFAULT_REMARK.getName());
        userRoleDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userRoleDO.setMember(memberDO);
        userRoleRepository.saveAndFlush(userRoleDO);

        Set<UserRoleDO> userRoleDOSet = new HashSet<>();
        userRoleDOSet.add(userRoleDO);
        memberDO.setUserRoles(userRoleDOSet);

        //Step 2: 创建 超级管理员用户
        UserDO userDO = new UserDO();
        userDO.setCreateTime(LocalDateTime.now());
        userDO.setLastModifyPwdTime(LocalDateTime.now());
        userDO.setCode(memberDO.getCode());
        userDO.setAccount(memberDO.getAccount());
        userDO.setPhone(memberDO.getPhone());
        userDO.setPassword(memberBO.getPassword());
        userDO.setTelCode(memberDO.getTelCode());
        userDO.setName(memberDO.getName());
        userDO.setEmail(memberDO.getEmail());
        userDO.setIdCardNo("");
        userDO.setJobTitle(MemberStringEnum.PLATFORM_SUPER_ADMIN_JOB_TITLE.getName());
        userDO.setLogo("");
        userDO.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        userDO.setRelType(MemberRelationTypeEnum.OTHER.getCode());
        userDO.setUserType(UserTypeEnum.ADMIN.getCode());
        userDO.setIsSales(Boolean.FALSE);

        // 生成唯一的邀请码
        String invitationCode = generateUniqueInvitationCode();
        userDO.setInvitationCode(invitationCode);
        userDO.setMember(memberDO);
        userDO.setRoles(userRoleDOSet);
        userDO.setMenuAuth(memberBO.getMemberRoleDO().getMenuAuth());
        userDO.setButtonAuth(memberBO.getMemberRoleDO().getButtonAuth());
        userDO.setApiAuth(memberBO.getMemberRoleDO().getApiAuth());
        //超级管理员用户不需要设置数据权限、渠道权限
        MemberUserAuthDO userAuthDO = new MemberUserAuthDO();
        userAuthDO.setUser(userDO);
        userAuthDO.setDataAuth(new ArrayList<>());
        userAuthDO.setChannelAuth(new ArrayList<>());
        userAuthDO.setChannels(new HashSet<>());
        userDO.setUserAuth(userAuthDO);

        userRepository.saveAndFlush(userDO);

        // 为新用户创建分佣账户
        try {
            invitationCommissionService.createCommissionAccountForUser(userDO.getId());
        } catch (Exception e) {
            // 记录日志但不影响主流程
            log.warn("为用户 {} 创建分佣账户失败: {}", userDO.getId(), e.getMessage());
        }

        Set<UserDO> userDOSet = new HashSet<>();
        userDOSet.add(userDO);
        memberDO.setUsers(userDOSet);
        memberRepository.saveAndFlush(memberDO);

        //第六步骤：“注册”的审核记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, memberBO.getOperatorRoleName(), MemberValidateHistoryOperationEnum.REGISTER, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");

        //超管默认注册im
        CompletableFuture.runAsync(() -> tencentIMFeign.importAccount(new ImportIMAccountReq(userDO.getId(), userDO.getName(), userDO.getLogo())), ThreadPoolConfig.asyncDefaultExecutor);

        return relationDO;
    }

    /**
     * 会员能力 - 新增会员上下级关系（非平台会员）
     * @param upperMember 上级会员
     * @param upperRole   上级会员角色
     * @param subMemberPlatformRelation   下级会员的平台会员关系
     * @param subRole     下级会员角色
     * @param subMemberType 下级会员的会员类型
     * @param subMemberLevel  下级会员的等级
     * @param subMemberLevelTypeEnum 下级会员的等级类型
     * @param roleTag 角色标签
     * @param outerFlag 外部系统标识
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addMemberRelation(MemberDO upperMember, MemberRoleDO upperRole, MemberRelationDO subMemberPlatformRelation, MemberRoleDO subRole, Integer subMemberType, Integer subMemberLevel, Integer subMemberLevelTypeEnum, Integer roleTag, Boolean outerFlag) {
        MemberDO subMember = subMemberPlatformRelation.getSubMember();
        //创建上下级关系，以及审核状态
        MemberRelationDO relationDO = new MemberRelationDO();
        LocalDateTime now = LocalDateTime.now();
        relationDO.setCreateTime(now);
        relationDO.setMemberId(upperMember.getId());
        relationDO.setMember(upperMember);
        relationDO.setUserId(0L);
        relationDO.setRoleId(upperRole.getId());
        relationDO.setRole(upperRole);
        relationDO.setRoleTag(Optional.ofNullable(upperRole.getRoleTag()).orElse(0));
        relationDO.setSubMemberId(subMember.getId());
        relationDO.setSubMember(subMember);
        relationDO.setSubRoleId(subRole.getId());
        relationDO.setSubRole(subRole);
        relationDO.setSubRoleTag(Optional.ofNullable(subRole.getRoleTag()).orElse(0));
        relationDO.setSubRoleName(subRole.getRoleName());
        relationDO.setSubMemberTypeEnum(subMemberType);
        relationDO.setSubMemberLevelTypeEnum(subMemberLevelTypeEnum);
        relationDO.setRelType(MemberRelationTypeEnum.OTHER.getCode());
        relationDO.setRelSource(MemberRelationSourceEnum.MEMBER_CREATE.getCode());
        relationDO.setVerified(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode());

        relationDO.setInnerStatus(MemberInnerStatusEnum.NEW.getCode());
        relationDO.setOuterStatus(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode());
        relationDO.setValidateMsg("");
        relationDO.setStatus(MemberStatusEnum.NORMAL.getCode());
        //权限（只有平台才能修改会员权限，所以这里不需要设置权限）
        relationDO.setMenuAuth(BitMapUtil.emptyByteArray());
        relationDO.setButtonAuth(BitMapUtil.emptyByteArray());
        relationDO.setApiAuth(BitMapUtil.emptyByteArray());

        // 查询是否存在发送邀请消息，存在回写邀请码
        MemberSendInvitationDO sendInvitationDO = memberSendInvitationRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperMember.getId(), upperRole.getId(), subMember.getId(), subRole.getId());
        if (Objects.nonNull(sendInvitationDO)) {
            relationDO.setInvitationCode(sendInvitationDO.getInvitationCode());
        }

        //工作流任务，查找入库流程
        ProcessBO depositProcess;
        if (!BooleanUtils.isTrue(outerFlag) && !Objects.equals(subRole.getId(), baiTaiMemberProperties.getIndividualRoleId())) {
            depositProcess = memberProcessRuleService.findMemberProcessKey(relationDO, MemberProcessTypeEnum.MEMBER_DEPOSITORY);
            relationDO.setValidateTask(new MemberValidateTaskDO("", depositProcess.getProcessKey(), MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode()));
        } else {
            depositProcess = new ProcessBO(MemberConstant.EMPTY_DEPOSITORY_PROCESS_KEY, true);
        }

        relationRepository.saveAndFlush(relationDO);

        //创建上级会员的等级配置
        MemberLevelConfigDO memberLevelConfigDO =baseMemberLevelConfigService.findLevel(relationDO, subMemberLevel);
        //第二步骤：设置“下级”的等级、权益、信用等信息
        MemberLevelRightDO levelRightDO = new MemberLevelRightDO();
        levelRightDO.setMemberId(upperMember.getId());
        levelRightDO.setRoleId(upperRole.getId());
        levelRightDO.setSubMemberId(subMember.getId());
        levelRightDO.setSubRoleId(subRole.getId());
        levelRightDO.setLevelConfig(memberLevelConfigDO);
        levelRightDO.setLevel(memberLevelConfigDO == null ? 0 : memberLevelConfigDO.getLevel());
        levelRightDO.setLevelTag(memberLevelConfigDO == null ? "" : memberLevelConfigDO.getLevelTag());
        levelRightDO.setScore(0);
        levelRightDO.setCurrentPoint(0);
        levelRightDO.setSumPoint(0);
        levelRightDO.setSumReturnMoney(new BigDecimal(0));
        levelRightDO.setSumUsedPoint(0);
        levelRightDO.setRelation(relationDO);

        //设置信用信息
        MemberCreditDO creditDO = new MemberCreditDO();
        creditDO.setMemberId(upperMember.getId());
        creditDO.setRoleId(upperRole.getId());
        creditDO.setSubMemberId(subMember.getId());
        creditDO.setSubRoleId(subRole.getId());
        creditDO.setAfterSaleCommentPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setCreditPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setRegisterYearsPoint(0);
        creditDO.setTradeCommentPoint(0);
        creditDO.setRelation(relationDO);
        creditDO.setRegisterPointUpdateTime(LocalDateTime.now());
        creditDO.setTradeCommentStars(0);
        creditDO.setTradeCommentCount(0);
        creditDO.setAvgTradeCommentStar(MemberConstant.DEFAULT_TRADE_COMMENT_STAR);

        //关联到会员关系
        relationDO.setLevelRight(levelRightDO);
        relationDO.setCredit(creditDO);
        relationRepository.saveAndFlush(relationDO);

        //将平台后台的下级会员成为当前用户的下级会员
        //MemberRelationDO firstBySubMemberIdAndSubRoleIdAndRelType = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(subMember.getId(), subRole.getId(), MemberRelationTypeEnum.PLATFORM.getCode());

        //第六步骤：外部审核记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.CREATE_SUB_MEMBER, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");

        //由于平台会员可以“无需审核”，所以这里要判断是否执行会员入库流程
        if(subMemberPlatformRelation.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode())) {
            if(depositProcess.getEmptyProcess()) {
                relationDO.setDepositTime(LocalDateTime.now());
                relationDO.setInnerStatus(MemberInnerStatusEnum.VERIFY_PASSED.getCode());
                relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode());
                relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
                relationDO.setDepositTime(LocalDateTime.now());
            } else {
                //启动会员入库流程
                WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(relationDO);
                relationDO.setInnerStatus(taskResult.getInnerStatus());
                relationDO.getValidateTask().setTaskId(taskResult.getTaskId());
                relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITING.getCode());
            }

            //根据注册资料同步入库资料
            baseMemberDepositDetailService.saveDepositoryDetail(relationDO, relationDO.getOuterStatus());

            relationRepository.saveAndFlush(relationDO);

            //外部流转记录
            baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.PLATFORM_VALIDATE, MemberOuterStatusEnum.getCodeMsg(subMemberPlatformRelation.getOuterStatus()), "");

            //实时消息
            messageFeignService.sendMemberValidateMessage(relationDO, roleTag);
            // 审核通过，回填生命周期
            if (MemberInnerStatusEnum.VERIFY_PASSED.getCode().equals(relationDO.getInnerStatus())) {
                MemberAfterApprovalLifecycleStagesDO lifecycleStagesDO = memberAfterApprovalLifecycleStagesRepository.findByMemberIdAndAndRoleIdAndRoleTag(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleTag());
                if (Objects.nonNull(lifecycleStagesDO)) {
                    MemberLifecycleStagesDO memberLifecycleStagesDO = memberLifecycleStagesRepository.findByMemberIdAndRoleIdAndRoleTagAndLifecycleStagesNum(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleTag(), lifecycleStagesDO.getLifecycleStagesNum());
                    relationDO.setMemberLifecycleStages(memberLifecycleStagesDO);
                }
                relationRepository.saveAndFlush(relationDO);
            }
        }

        //如果审核通过，通知支付服务创建资金账户，创建会员授信
        payFeignService.notifyMemberAssetAccount(relationDO);
        payFeignService.notifyMemberCredit(relationDO);

    }

    /**
     * 通知报表服务，通知支付服务创建资金账户，创建会员授信
     * @param memberList 会员上下级关系列表
     */
    @Override
    public void addMemberRelationInform(List<MemberRelationDO> memberList) {
        memberList.forEach(relationDO -> {
            //实时消息
            messageFeignService.sendMemberValidateMessage(relationDO, RoleTagEnum.MEMBER.getCode());

            //如果审核通过，通知支付服务创建资金账户，创建会员授信
            payFeignService.notifyMemberAssetAccount(relationDO);
            payFeignService.notifyMemberCredit(relationDO);
        });
    }

    /**
     * 会员能力 - 新增会员上下级关系（非平台会员）
     * @param upperMember 上级会员
     * @param upperRole   上级会员角色
     * @param subMemberPlatformRelation   下级会员的平台会员关系
     * @param subRole     下级会员角色
     * @param subMemberType 下级会员的会员类型
     * @param subMemberLevel  下级会员的等级
     * @param subMemberLevelTypeEnum 下级会员的等级类型
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public MemberRelationDO addMemberRelations(MemberDO upperMember, MemberRoleDO upperRole, MemberRelationDO subMemberPlatformRelation, MemberRoleDO subRole, Integer subMemberType, Integer subMemberLevel, Integer subMemberLevelTypeEnum) {
        MemberDO subMember = subMemberPlatformRelation.getSubMember();
        //创建上下级关系，以及审核状态
        MemberRelationDO relationDO = new MemberRelationDO();
        LocalDateTime now = LocalDateTime.now();
        relationDO.setCreateTime(now);
        relationDO.setMemberId(upperMember.getId());
        relationDO.setMember(upperMember);
        relationDO.setUserId(0L);
        relationDO.setRoleId(upperRole.getId());
        relationDO.setRole(upperRole);
        relationDO.setRoleTag(Optional.ofNullable(upperRole.getRoleTag()).orElse(0));
        relationDO.setSubMemberId(subMember.getId());
        relationDO.setSubMember(subMember);
        relationDO.setSubRoleId(subRole.getId());
        relationDO.setSubRole(subRole);
        relationDO.setSubRoleName(subRole.getRoleName());
        relationDO.setSubRoleTag(Optional.ofNullable(subRole.getRoleTag()).orElse(0));
        relationDO.setSubMemberTypeEnum(subMemberType);
        relationDO.setSubMemberLevelTypeEnum(subMemberLevelTypeEnum);
        relationDO.setRelType(MemberRelationTypeEnum.OTHER.getCode());
        relationDO.setRelSource(MemberRelationSourceEnum.MEMBER_CREATE.getCode());
        relationDO.setVerified(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode());

        relationDO.setInnerStatus(MemberInnerStatusEnum.NEW.getCode());
        relationDO.setOuterStatus(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode());
        relationDO.setValidateMsg("");
        relationDO.setStatus(MemberStatusEnum.NORMAL.getCode());
        //权限（只有平台才能修改会员权限，所以这里不需要设置权限）
        relationDO.setMenuAuth(BitMapUtil.emptyByteArray());
        relationDO.setButtonAuth(BitMapUtil.emptyByteArray());
        relationDO.setApiAuth(BitMapUtil.emptyByteArray());

        //工作流任务，查找入库流程
        ProcessBO depositProcess = memberProcessRuleService.findMemberProcessKey(relationDO, MemberProcessTypeEnum.MEMBER_DEPOSITORY);
        relationDO.setValidateTask(new MemberValidateTaskDO("", depositProcess.getProcessKey(), MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode()));

        relationRepository.saveAndFlush(relationDO);

        //创建上级会员的等级配置
        MemberLevelConfigDO memberLevelConfigDO =baseMemberLevelConfigService.findLevel(relationDO, subMemberLevel);
        //第二步骤：设置“下级”的等级、权益、信用等信息
        MemberLevelRightDO levelRightDO = new MemberLevelRightDO();
        levelRightDO.setMemberId(upperMember.getId());
        levelRightDO.setRoleId(upperRole.getId());
        levelRightDO.setSubMemberId(subMember.getId());
        levelRightDO.setSubRoleId(subRole.getId());
        levelRightDO.setLevelConfig(memberLevelConfigDO);
        levelRightDO.setLevel(memberLevelConfigDO == null ? 0 : memberLevelConfigDO.getLevel());
        levelRightDO.setLevelTag(memberLevelConfigDO == null ? "" : memberLevelConfigDO.getLevelTag());
        levelRightDO.setScore(0);
        levelRightDO.setCurrentPoint(0);
        levelRightDO.setSumPoint(0);
        levelRightDO.setSumReturnMoney(new BigDecimal(0));
        levelRightDO.setSumUsedPoint(0);
        levelRightDO.setRelation(relationDO);

        //设置信用信息
        MemberCreditDO creditDO = new MemberCreditDO();
        creditDO.setMemberId(upperMember.getId());
        creditDO.setRoleId(upperRole.getId());
        creditDO.setSubMemberId(subMember.getId());
        creditDO.setSubRoleId(subRole.getId());
        creditDO.setAfterSaleCommentPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setCreditPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setRegisterYearsPoint(0);
        creditDO.setTradeCommentPoint(0);
        creditDO.setRelation(relationDO);
        creditDO.setRegisterPointUpdateTime(LocalDateTime.now());
        creditDO.setTradeCommentStars(0);
        creditDO.setTradeCommentCount(0);
        creditDO.setAvgTradeCommentStar(MemberConstant.DEFAULT_TRADE_COMMENT_STAR);

        //关联到会员关系
        relationDO.setLevelRight(levelRightDO);
        relationDO.setCredit(creditDO);
        relationRepository.saveAndFlush(relationDO);

        //将平台后台的下级会员成为当前用户的下级会员
        //MemberRelationDO firstBySubMemberIdAndSubRoleIdAndRelType = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(subMember.getId(), subRole.getId(), MemberRelationTypeEnum.PLATFORM.getCode());

        //第六步骤：外部审核记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), MemberValidateHistoryOperationEnum.CREATE_SUB_MEMBER, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");

        //由于平台会员可以“无需审核”，所以这里要判断是否执行会员入库流程
        if(subMemberPlatformRelation.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode())) {
            if(depositProcess.getEmptyProcess()) {
                relationDO.setDepositTime(LocalDateTime.now());
                relationDO.setInnerStatus(MemberInnerStatusEnum.VERIFY_PASSED.getCode());
                relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode());
                relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
                relationDO.setDepositTime(LocalDateTime.now());
            } else {
                //启动会员入库流程
                WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(relationDO);
                relationDO.setInnerStatus(taskResult.getInnerStatus());
                relationDO.getValidateTask().setTaskId(taskResult.getTaskId());
                relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITING.getCode());
            }
            relationRepository.saveAndFlush(relationDO);

            //外部流转记录
            baseMemberHistoryService.saveMemberOuterHistory(relationDO, subMemberPlatformRelation.getRole().getRoleName(), MemberValidateHistoryOperationEnum.PLATFORM_VALIDATE, MemberOuterStatusEnum.getCodeMsg(subMemberPlatformRelation.getOuterStatus()), "");
        }

        // 审核通过，回填生命周期
        if (MemberInnerStatusEnum.VERIFY_PASSED.getCode().equals(relationDO.getInnerStatus())) {
            MemberAfterApprovalLifecycleStagesDO lifecycleStagesDO = memberAfterApprovalLifecycleStagesRepository.findByMemberIdAndAndRoleIdAndRoleTag(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleTag());
            if (Objects.nonNull(lifecycleStagesDO)) {
                MemberLifecycleStagesDO memberLifecycleStagesDO = memberLifecycleStagesRepository.findByMemberIdAndRoleIdAndRoleTagAndLifecycleStagesNum(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubRoleTag(), lifecycleStagesDO.getLifecycleStagesNum());
                relationDO.setMemberLifecycleStages(memberLifecycleStagesDO);
            }
            relationRepository.saveAndFlush(relationDO);
        }

        return relationDO;
    }


    /**
     * 修改平台会员信息
     * 只能在会员新建下级会员后，才能修改下级会员的信息
     * @param updateBO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePlatformMember(UpdatePlatformMemberBO updateBO) {
        //更新会员信息
        Long oldRoleId = updateBO.getRelationDO().getSubRoleId();
        boolean isRoleUpdate = !oldRoleId.equals(updateBO.getMemberRoleDO().getId());

        //平台会员的上下级关系
        MemberRelationDO platformMemberRelationDO = updateBO.getRelationDO();
        if(platformMemberRelationDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //会员的管理员用户
        UserDO userDO = platformMemberRelationDO.getSubMember().getUsers().stream().filter(user -> user.getUserType().equals(UserTypeEnum.ADMIN.getCode())).findFirst().orElse(null);
        if(userDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        //修改平台会员的上下级关系
        platformMemberRelationDO.setSubMemberTypeEnum(updateBO.getMemberType());
        platformMemberRelationDO.setSubRoleId(updateBO.getMemberRoleDO().getId());
        platformMemberRelationDO.setSubRole(updateBO.getMemberRoleDO());
        platformMemberRelationDO.setSubRoleName(updateBO.getMemberRoleDO().getRoleName());
        platformMemberRelationDO.setSubRoleTag(Optional.ofNullable(updateBO.getMemberRoleDO().getRoleTag()).orElse(0));

        //修改等级
        //由于平台的下属会员都是“平台会员”，所以不需要根据会员等级类型进行判断
        MemberLevelConfigDO upperMemberLevelConfigDO = baseMemberLevelConfigService.findLevel(platformMemberRelationDO, updateBO.getLevel());
        MemberLevelRightDO levelDO = platformMemberRelationDO.getLevelRight();
        levelDO.setMemberId(platformMemberRelationDO.getMemberId());
        levelDO.setRoleId(platformMemberRelationDO.getRoleId());
        levelDO.setSubMemberId(platformMemberRelationDO.getSubMemberId());
        levelDO.setSubRoleId(updateBO.getMemberRoleDO().getId());
        levelDO.setLevelConfig(upperMemberLevelConfigDO);
        levelDO.setLevel(upperMemberLevelConfigDO == null ? 0 : upperMemberLevelConfigDO.getLevel());
        levelDO.setLevelTag(upperMemberLevelConfigDO == null ? "" : upperMemberLevelConfigDO.getLevelTag());
        memberLevelRightRepository.saveAndFlush(levelDO);

        //保存平台上下级关系
        relationRepository.saveAndFlush(platformMemberRelationDO);

        //修改信用信息
        MemberCreditDO creditDO = platformMemberRelationDO.getCredit();
        creditDO.setSubMemberId(updateBO.getRelationDO().getSubMemberId());
        creditDO.setSubRoleId(updateBO.getMemberRoleDO().getId());
        memberCreditRepository.saveAndFlush(creditDO);

        //如果更改了角色，更改内、外部审核记录
        if(isRoleUpdate) {
            baseMemberHistoryService.updateHistorySubRoleId(updateBO.getRelationDO().getSubMemberId(), oldRoleId, updateBO.getMemberRoleDO().getId(), updateBO.getMemberRoleDO().getRoleName(), updateBO.getName());
        }

        //修改会员信息，合并权限
        MemberDO memberDO = updateBO.getRelationDO().getSubMember();
        memberDO.setAccount(updateBO.getPhone());
        memberDO.setPhone(updateBO.getPhone());
        memberDO.setTelCode(updateBO.getTelCode());
        memberDO.setName(updateBO.getName());
        memberDO.setEmail(updateBO.getEmail());

        //修改管理员用户的账号
        userDO.setName(updateBO.getName());
        userDO.setAccount(updateBO.getPhone());
        userDO.setPhone(updateBO.getPhone());
        userDO.setEmail(updateBO.getEmail());
        userDO.setTelCode(updateBO.getTelCode());
        userRepository.saveAndFlush(userDO);

        // 如果角色有变更，更新平台关系、用户角色、用户的权限
        if (isRoleUpdate) {
            platformMemberRelationDO.setMenuAuth(updateBO.getMemberRoleDO().getMenuAuth());
            platformMemberRelationDO.setButtonAuth(updateBO.getMemberRoleDO().getButtonAuth());
            platformMemberRelationDO.setApiAuth(updateBO.getMemberRoleDO().getApiAuth());
            relationRepository.save(platformMemberRelationDO);

            // 移除会员角色
            memberDO.getMemberRoles().removeIf(memberRoleDO -> memberRoleDO.getId().equals(oldRoleId));
            memberDO.getMemberRoles().add(updateBO.getMemberRoleDO());
            memberRepository.save(memberDO);

            // 更新该会员的用户和用户角色权限
            baseAuthService.rebuildMemberAuth(platformMemberRelationDO.getSubMember());

            // 删除当前会员的所有token
            CompletableFuture.runAsync(() -> tokenManageService.memberOffline(Collections.singletonList(memberDO.getId())), ThreadPoolConfig.asyncDefaultExecutor);
        }
    }

    /**
     * 修改会员上下级关系
     *
     * @param updateRelationBO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMemberRelation(UpdateMemberRelationBO updateRelationBO) {
        //创建上下级关系，以及审核状态
        MemberRelationDO relationDO = updateRelationBO.getRelationDO();
        relationDO.setSubRoleId(updateRelationBO.getSubRoleDO().getId());
        relationDO.setSubRole(updateRelationBO.getSubRoleDO());
        relationDO.setSubRoleTag(Optional.ofNullable(updateRelationBO.getSubRoleDO().getRoleTag()).orElse(0));
        relationDO.setSubRoleName(updateRelationBO.getSubRoleDO().getRoleName());
        relationDO.setSubMemberTypeEnum(updateRelationBO.getMemberType());
        //非平台会员没有权限
        relationDO.setMenuAuth(BitMapUtil.emptyByteArray());
        relationDO.setButtonAuth(BitMapUtil.emptyByteArray());
        relationDO.setApiAuth(BitMapUtil.emptyByteArray());

        //修改后状态为“待提交审核”（手动再提交审核）
        relationDO.setInnerStatus(MemberInnerStatusEnum.NEW.getCode());
        relationDO.setOuterStatus(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode());

        //保存上下级关系
        relationRepository.saveAndFlush(relationDO);

        MemberLevelConfigDO memberLevelConfigDO = baseMemberLevelConfigService.findLevel(relationDO, updateRelationBO.getLevel());
        //更改“下级”的等级、权益、信用等信息
        MemberLevelRightDO levelDO = relationDO.getLevelRight();
        if(levelDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
        }
        levelDO.setSubMemberId(updateRelationBO.getSubMember().getId());
        levelDO.setSubRoleId(updateRelationBO.getSubRoleDO().getId());
        levelDO.setLevelConfig(memberLevelConfigDO);
        levelDO.setLevel(memberLevelConfigDO == null ? 0 : memberLevelConfigDO.getLevel());
        levelDO.setLevelTag(memberLevelConfigDO == null ? "" : memberLevelConfigDO.getLevelTag());
        levelDO.setRelation(relationDO);
        memberLevelRightRepository.saveAndFlush(levelDO);

        //更改信用信息
        MemberCreditDO creditDO = relationDO.getCredit();
        if(creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_CREDIT_CONFIG_DOES_NOT_EXIST);
        }

        creditDO.setSubMemberId(updateRelationBO.getSubMember().getId());
        creditDO.setSubRoleId(updateRelationBO.getSubRoleDO().getId());
        creditDO.setRelation(relationDO);
        memberCreditRepository.saveAndFlush(creditDO);

    }

    /**
     * 删除平台会员
     * @param memberDO 要删除的会员
     * @param relationDO 要删除的与平台会员的上下级关系
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deletePlatformMember(MemberDO memberDO, MemberRelationDO relationDO) {
        //删除与MemberDO相关的
        //删除角色与用户、组织
        userRoleRepository.deleteAll(memberDO.getUserRoles());
        userRepository.deleteAll(memberDO.getUsers());
        memberOrganizationRepository.deleteAll(memberDO.getOrgs());

        //删除下属的升级规则
        memberLevelRuleConfigRepository.deleteByMemberIdAndRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberRightConfigRepository.deleteByMemberIdAndRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberLevelConfigRepository.deleteByMemberIdAndRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());

        //删除权益、信用、历史记录等
        baseMemberHistoryService.deleteHistoryBySubMemberIdAndSubRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberAfterSaleHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberComplainHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberTradeCommentHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberLevelHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberRightHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberRightSpendHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(relationDO.getSubMemberId(), relationDO.getSubRoleId());

        relationRepository.delete(relationDO);
        memberRepository.delete(memberDO);
    }

    /**
     * 删除上下级关系
     *
     * @param relationDO 上下级关系
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMemberRelation(MemberRelationDO relationDO) {
        //各种历史记录
        baseMemberHistoryService.deleteHistoryByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberAfterSaleHistoryRepository.deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberComplainHistoryRepository.deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberTradeCommentHistoryRepository.deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberLevelHistoryRepository.deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberRightHistoryRepository.deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        memberRightSpendHistoryRepository.deleteByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());


        relationRepository.delete(relationDO);
    }

    /**
     * 删除平台会员和角色
     * @param relationDO 平台会员上下级关系
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deletePlatformMemberAndRole(MemberRelationDO relationDO) {
        // 平台超管会员
        MemberDO platformMemberDO = memberRepository.findPlatformMember();

        MemberDO memberDO = relationDO.getSubMember();
        if (Objects.equals(memberDO.getAccount(), platformMemberDO.getAccount())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberRoleDO memberRoleDO = relationDO.getSubRole();
        if(memberDO.getMemberRoles().size() == 1 && memberDO.getMemberRoles().iterator().next().getId().equals(memberRoleDO.getId())) {
            deleteMemberCompletely(memberDO, memberRoleDO);

        }

        Long deleteMemberId = memberDO.getId();
        Long deleteRoleId = memberRoleDO.getId();

        //删除与MemberDO相关的角色
        memberDO.getMemberRoles().remove(memberRoleDO);
        memberRepository.saveAndFlush(memberDO);

        relationRepository.deleteAllBySubMemberIdAndSubRoleId(memberDO.getId(), memberRoleDO.getId());

        //删除下属的升级规则
        memberLevelRuleConfigRepository.deleteByMemberIdAndRoleId(deleteMemberId, deleteRoleId);
        memberRightConfigRepository.deleteByMemberIdAndRoleId(deleteMemberId, deleteRoleId);
        memberLevelConfigRepository.deleteByMemberIdAndRoleId(deleteMemberId, deleteRoleId);

        //删除权益、信用、历史记录等
        memberLevelRightRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberCreditRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        baseMemberHistoryService.deleteHistoryBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberAfterSaleHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberComplainHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberTradeCommentHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberLevelHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberRightHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberRightSpendHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);


    }

    /**
     * 会员只有一个角色的情况下，彻底删除平台会员和角色
     * @param memberDO 要删除的会员
     * @param memberRoleDO 要删除的角色
     * @return 删除结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMemberCompletely(MemberDO memberDO, MemberRoleDO memberRoleDO) {
        // 平台超管会员
        MemberDO platformMemberDO = memberRepository.findPlatformMember();

        if (Objects.equals(memberDO.getAccount(), platformMemberDO.getAccount())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        Long deleteMemberId = memberDO.getId();
        Long deleteRoleId = memberRoleDO.getId();

        //删除与MemberDO相关的
        //删除角色与用户、组织
        userRoleRepository.deleteAll(memberDO.getUserRoles());
        userRepository.deleteAll(memberDO.getUsers());
        memberOrganizationRepository.deleteAll(memberDO.getOrgs());

        //删除上下级关系
        relationRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        //移除角色关联关系、会员
        memberDO.getMemberRoles().clear();
        memberRepository.delete(memberDO);

        //删除下属的升级规则
        memberLevelRuleConfigRepository.deleteByMemberIdAndRoleId(deleteMemberId, deleteRoleId);
        memberRightConfigRepository.deleteByMemberIdAndRoleId(deleteMemberId, deleteRoleId);
        memberLevelConfigRepository.deleteByMemberIdAndRoleId(deleteMemberId, deleteRoleId);

        //删除权益、信用、历史记录等
        memberLevelRightRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberCreditRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        baseMemberHistoryService.deleteHistoryBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberAfterSaleHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberComplainHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberTradeCommentHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberLevelHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberRightHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);
        memberRightSpendHistoryRepository.deleteAllBySubMemberIdAndSubRoleId(deleteMemberId, deleteRoleId);


    }

    /**
     * 平台会员，新增角色
     * @param processBO        角色关联的平台会员审核流程
     * @param operatorRoleName 登录用户的当前会员角色
     * @param adminMemberDO 平台管理员
     * @param adminRole 平台管理员的角色
     * @param memberDO 会员
     * @param memberRoleDO   新增的角色
     * @param memberAdminRole 会员下属的管理员角色
     * @param memberAdminUser 会员下属的管理员
     * @param memberName 会员名称
     * @param memberRegisterDetails 注册资料
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public MemberRelationDO addMemberRole(ProcessBO processBO, String operatorRoleName, MemberDO adminMemberDO, MemberRoleDO adminRole, MemberDO memberDO, MemberRoleDO memberRoleDO, UserRoleDO memberAdminRole, UserDO memberAdminUser, String memberName, List<MemberRegisterDetailDO> memberRegisterDetails) {
        //Step 1: 删除旧的注册资料，保存新的注册资料
        memberRegisterDetailRepository.deleteByMember(memberDO);

        memberRegisterDetails.forEach(memberRegisterDetailDO -> {
            memberRegisterDetailDO.setMember(memberDO);
            //如果是无需审核流程，将注册资料版本设置为“正在使用”
            if(processBO.getEmptyProcess()) {
                memberRegisterDetailDO.setVersion(MemberDetailVersionEnum.USING.getCode());
            }
        });
        memberRegisterDetailRepository.saveAll(memberRegisterDetails);
        // 兼容list字段，修改成循环保存
        for (MemberRegisterDetailDO memberRegisterDetail : memberRegisterDetails) {
//            memberRegisterDetailRepository.save(memberRegisterDetail);
            if (!CollectionUtils.isEmpty(memberRegisterDetail.getRegisters())) {
                List<MemberRegisterDetailDO> registers = memberRegisterDetail.getRegisters();
                registers.forEach(register -> register.setParentId(memberRegisterDetail.getId()));
                memberRegisterDetailRepository.saveAll(registers);
            }
        }

        memberDO.setRegisterDetails(new HashSet<>(memberRegisterDetails));

        //Step 2:设置新的角色和权限
        if(StringUtils.hasLength(memberName)) {
            memberDO.setName(memberName);
        }

        memberDO.getMemberRoles().add(memberRoleDO);
        //由于存在循环关联实体，所以要先保存一下Member信息
        memberRepository.saveAndFlush(memberDO);

        //Step 3: 更改超级管理员角色和用户的权限
        byte[] menuAuth = BitMapUtil.or(memberAdminRole.getMenuAuth(), memberRoleDO.getMenuAuth(), BitMapUtil.ReturnType.ByteArray);
        byte[] buttonAuth = BitMapUtil.or(memberAdminRole.getButtonAuth(), memberRoleDO.getButtonAuth(), BitMapUtil.ReturnType.ByteArray);
        byte[] apiAuth = BitMapUtil.or(memberAdminRole.getApiAuth(), memberRoleDO.getApiAuth(), BitMapUtil.ReturnType.ByteArray);

        memberAdminRole.setMenuAuth(menuAuth);
        memberAdminRole.setButtonAuth(buttonAuth);
        memberAdminRole.setApiAuth(apiAuth);
        userRoleRepository.saveAndFlush(memberAdminRole);

        memberAdminUser.setMenuAuth(menuAuth);
        memberAdminUser.setButtonAuth(buttonAuth);
        memberAdminUser.setApiAuth(apiAuth);
        userRepository.saveAndFlush(memberAdminUser);

        //Step 4：创建上下级关系，以及审核状态
        MemberRelationDO relationDO = new MemberRelationDO();
        relationDO.setCreateTime(LocalDateTime.now());
        relationDO.setMemberId(adminMemberDO.getId());
        relationDO.setMember(adminMemberDO);
        relationDO.setRoleId(adminRole.getId());
        relationDO.setRole(adminRole);
        relationDO.setRoleTag(Optional.ofNullable(adminRole.getRoleTag()).orElse(0));
        relationDO.setSubMemberId(memberDO.getId());
        relationDO.setSubMember(memberDO);
        relationDO.setSubMemberTypeEnum(memberRoleDO.getMemberType());
        //上级为平台，所以等级类型为平台会员
        relationDO.setSubMemberLevelTypeEnum(MemberLevelTypeEnum.PLATFORM.getCode());
        relationDO.setSubRoleId(memberRoleDO.getId());
        relationDO.setSubRole(memberRoleDO);
        relationDO.setSubRoleName(memberRoleDO.getRoleName());
        relationDO.setSubRoleTag(Optional.ofNullable(memberRoleDO.getRoleTag()).orElse(0));
        relationDO.setRelType(MemberRelationTypeEnum.PLATFORM.getCode());
        relationDO.setRelSource(MemberRelationSourceEnum.ADD_ROLE.getCode());
        relationDO.setVerified(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode());
        relationDO.setValidateMsg("");
        relationDO.setStatus(MemberStatusEnum.NORMAL.getCode());
        //新角色的平台权限
        relationDO.setMenuAuth(memberRoleDO.getMenuAuth());
        relationDO.setButtonAuth(memberRoleDO.getButtonAuth());
        relationDO.setApiAuth(memberRoleDO.getApiAuth());

        //由于工作流需要dataId（不能为空或0），所以要先保存一下MemberRelationDO
        relationRepository.saveAndFlush(relationDO);

        //Step 5：调用工作流模块
        if(processBO.getEmptyProcess()) {
            relationDO.setValidateTask(new MemberValidateTaskDO("", processBO.getProcessKey(), MemberProcessTypeEnum.PLATFORM_VALIDATION.getCode()));
            relationDO.setInnerStatus(PlatformInnerStatusEnum.VERIFY_PASSED.getCode());
            relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode());
            relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
            relationDO.setDepositTime(LocalDateTime.now());
        } else {
            WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(processBO.getProcessKey(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId());

            relationDO.setInnerStatus(taskResult.getInnerStatus());
            //工作流任务
            relationDO.setValidateTask(new MemberValidateTaskDO(taskResult.getTaskId(), processBO.getProcessKey(), MemberProcessTypeEnum.PLATFORM_VALIDATION.getCode()));

            if (taskResult.getInnerStatus().equals(PlatformInnerStatusEnum.VERIFY_NOT_PASSED.getCode())) {
                relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode());
            } else if (taskResult.getInnerStatus().equals(PlatformInnerStatusEnum.VERIFY_PASSED.getCode())) {
                relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode());
                relationDO.setDepositTime(LocalDateTime.now());
            } else {
                relationDO.setOuterStatus(MemberOuterStatusEnum.PLATFORM_VERIFYING.getCode());
            }
        }

        //Step 6：设置“平台会员”的等级、权益、信用等信息
        //由于平台的下属会员都是“平台会员”，所以不需要根据会员等级类型进行判断
        MemberLevelConfigDO upperMemberLevelConfigDO = baseMemberLevelConfigService.findFirstLevel(relationDO);

        MemberLevelRightDO levelDO = new MemberLevelRightDO();
        levelDO.setMemberId(adminMemberDO.getId());
        levelDO.setRoleId(adminRole.getId());
        levelDO.setSubMemberId(memberDO.getId());
        levelDO.setSubRoleId(memberRoleDO.getId());
        levelDO.setLevel(upperMemberLevelConfigDO == null ? 0 : upperMemberLevelConfigDO.getLevel());
        levelDO.setLevelTag(upperMemberLevelConfigDO == null ? "" : upperMemberLevelConfigDO.getLevelTag());
        levelDO.setLevelConfig(upperMemberLevelConfigDO);
        levelDO.setScore(0);
        levelDO.setCurrentPoint(0);
        levelDO.setSumPoint(0);
        levelDO.setSumReturnMoney(new BigDecimal(0));
        levelDO.setSumUsedPoint(0);
        levelDO.setRelation(relationDO);

        //设置信用信息
        MemberCreditDO creditDO = new MemberCreditDO();
        creditDO.setMemberId(adminMemberDO.getId());
        creditDO.setRoleId(adminRole.getId());
        creditDO.setSubMemberId(memberDO.getId());
        creditDO.setSubRoleId(memberRoleDO.getId());
        creditDO.setAfterSaleCommentPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setCreditPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setRegisterYearsPoint(0);
        creditDO.setTradeCommentPoint(0);
        creditDO.setRelation(relationDO);
        creditDO.setRegisterPointUpdateTime(LocalDateTime.now());
        creditDO.setTradeCommentStars(0);
        creditDO.setTradeCommentCount(0);
        creditDO.setAvgTradeCommentStar(MemberConstant.DEFAULT_TRADE_COMMENT_STAR);

        //关联到会员关系
        relationDO.setLevelRight(levelDO);
        relationDO.setCredit(creditDO);
        relationRepository.saveAndFlush(relationDO);

        //Step 7：“注册”的审核记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, operatorRoleName, MemberValidateHistoryOperationEnum.REGISTER, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");

        //如果审核通过，通知支付服务创建资金账户、授信账户，通知物流服务创建初始化数据
        payFeignService.notifyMemberAssetAccount(relationDO);
        payFeignService.notifyMemberCredit(relationDO);
        logisticsFeignService.initMemberLogisticsAsync(relationDO);

        return relationDO;
    }

    /**
     * 下级会员申请成为上级会员的下级
     * @param upperMemberId 上级会员Id
     * @param upperRoleId    上级会员角色Id
     * @param subMemberId 下级会员Id
     * @param subRoleId 下级会员角色Id
     * @param depositDetails 入库资料
     * @param qualities 资质证明文件
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public WrapperResp<Void> applyToBeSubMember(Long upperMemberId, Long upperRoleId, Long subMemberId, Long subRoleId, Map<String, Object> depositDetails, List<MemberQualityReq> qualities) {
        //规则：
        //1. 渠道会员可以申请成为渠道会员的下级会员
        //2. 商户会员可以申请成为商户会员的下级会员
        //3. 如果下级会员是渠道会员，且下级会员已经有另一个服务消费者角色在会员树中，不允许创建

        //会员不能成为自己的下级会员
        if(subMemberId.equals(upperMemberId)) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_CAN_NOT_BE_SUB_MEMBER_OF_SELF);
        }

        //当前会员没有审核通过，不能申请
        MemberRelationDO subRelation = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(subMemberId, subRoleId, MemberRelationTypeEnum.PLATFORM.getCode());
        if(subRelation == null) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if(!subRelation.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode())) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_VERIFYING);
        }

        if(!subRelation.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_HAS_BEEN_FROZEN);
        }

        //判断是否已经是下级会员了，如果是被淘汰的，可以申请，状态改为正常
        MemberRelationDO existRelation = relationRepository.findFirstByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(upperMemberId, upperRoleId, subMemberId, subRoleId);
        if(existRelation != null) {
            if(existRelation.getStatus().equals(MemberStatusEnum.ELIMINATED.getCode())) {
                return reApplyForSubMember(existRelation);
            } else if(existRelation.getStatus().equals(MemberStatusEnum.BLACK_LIST.getCode())) {
                return WrapperUtil.fail(ResponseCodeEnum.MC_MS_BLACKLIST_MEMBER_CAN_NOT_APPLY_FOR_SUB_MEMBER);
            } else {
                return WrapperUtil.fail(ResponseCodeEnum.MC_MS_IS_ALREADY_SUB_MEMBER);
            }
        }

        //判断上级会员
        MemberRelationDO upperRelation = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelType(upperMemberId, upperRoleId, MemberRelationTypeEnum.PLATFORM.getCode());
        if(upperRelation == null) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        if(!upperRelation.getVerified().equals(MemberValidateStatusEnum.VERIFY_PASSED.getCode())) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_UPPER_MEMBER_VALIDATE_NOT_PASSED);
        }

        if(!upperRelation.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            return WrapperUtil.fail(ResponseCodeEnum.MC_MS_UPPER_MEMBER_STATUS_IS_NOT_NORMAL);
        }

        MemberDO upperMemberDO = upperRelation.getSubMember();
        MemberRoleDO upperRoleDO = upperRelation.getSubRole();
        MemberDO subMemberDO = subRelation.getSubMember();
        MemberRoleDO subRoleDO = subRelation.getSubRole();

        //检查入库资料
        List<MemberDepositoryDetailDO> depositCheckResult = baseMemberDepositDetailService.checkMemberDepositoryDetail(upperMemberId, upperRoleId, subRoleDO, depositDetails);

        //检查资质文件
        WrapperResp<List<MemberQualityDO>> qualityCheckResult = baseMemberQualificationService.checkMemberQualities(qualities);
        if(qualityCheckResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            return WrapperUtil.fail(qualityCheckResult.getCode(), qualityCheckResult.getMessage());
        }

        //规则1、2
        //上级会员等级类型
        Integer upperMemberLevelType = MemberLevelTypeEnum.MERCHANT.getCode();
        //下级会员等级类型
        Integer subMemberLevelTypeEnum = MemberLevelTypeEnum.MERCHANT.getCode();
        if(!upperMemberLevelType.equals(subMemberLevelTypeEnum)) {
            if(upperMemberLevelType.equals(MemberLevelTypeEnum.MERCHANT.getCode())) {
                return WrapperUtil.fail(ResponseCodeEnum.MC_MS_NEED_MERCHANT_MEMBER_TYPE);
            } else {
                return WrapperUtil.fail(ResponseCodeEnum.MC_MS_NEED_CHANNEL_MEMBER_TYPE);
            }
        }

        //如果下级会员（当前会员）是渠道会员，判断是否已经有服务消费者角色在上下级关系树中
        if(subRoleDO.getRoleType().equals(RoleTypeEnum.SERVICE_CONSUMER.getCode())) {
            List<MemberRelationDO> relationDOList = relationRepository.findAllByRelType(MemberRelationTypeEnum.OTHER.getCode());
            List<MemberRelationDO> upperRelationList = findUpperMemberRelationList(relationDOList, upperMemberId, upperRoleId, subMemberId);
            if (!CollectionUtils.isEmpty(upperRelationList)) {
                //判断是否有消费者角色存在于关系中
                if (upperRelationList.stream().anyMatch(relationDO ->
                        (relationDO.getMemberId().equals(upperMemberId) && relationDO.getRole().getRoleType().equals(RoleTypeEnum.SERVICE_CONSUMER.getCode()))
                                || (relationDO.getSubMemberId().equals(subMemberId) && relationDO.getSubRole().getRoleType().equals(RoleTypeEnum.SERVICE_CONSUMER.getCode())))) {
                    return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_HAS_CONSUMER_ROLE_IN_UPPER_MEMBER_RELATIONS);
                }
            }
        }

        //上级会员的等级配置
        MemberLevelConfigDO memberLevelConfigDO = baseMemberLevelConfigService.findFirstLevel(upperMemberId, upperRoleId, subRoleId);

        //创建上下级关系，以及审核状态
        MemberRelationDO relationDO = new MemberRelationDO();
        relationDO.setCreateTime(LocalDateTime.now());
        relationDO.setMemberId(upperMemberDO.getId());
        relationDO.setMember(upperMemberDO);
        relationDO.setRoleId(upperRoleDO.getId());
        relationDO.setRole(upperRoleDO);
        relationDO.setRoleTag(Optional.ofNullable(upperRoleDO.getRoleTag()).orElse(0));
        relationDO.setSubMemberId(subMemberId);
        relationDO.setSubMember(subMemberDO);
        relationDO.setSubRoleId(subRoleDO.getId());
        relationDO.setSubRole(subRoleDO);
        relationDO.setSubRoleName(subRoleDO.getRoleName());
        relationDO.setSubRoleTag(Optional.ofNullable(subRoleDO.getRoleTag()).orElse(0));
        relationDO.setSubMemberTypeEnum(subRoleDO.getMemberType());
        relationDO.setSubMemberLevelTypeEnum(subMemberLevelTypeEnum);
        relationDO.setRelType(MemberRelationTypeEnum.OTHER.getCode());
        relationDO.setRelSource(MemberRelationSourceEnum.APPLY_TO_BE.getCode());
        relationDO.setVerified(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode());
        relationDO.setValidateMsg("");
        relationDO.setStatus(MemberStatusEnum.NORMAL.getCode());
        relationDO.setUserId(0L);
        //权限（只有平台才能修改会员权限，所以这里不需要设置权限）
        relationDO.setMenuAuth(BitMapUtil.emptyByteArray());
        relationDO.setButtonAuth(BitMapUtil.emptyByteArray());
        relationDO.setApiAuth(BitMapUtil.emptyByteArray());

        MemberValidateTaskDO taskDO = new MemberValidateTaskDO("", "", MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode());
        relationDO.setValidateTask(taskDO);
        //保存上下级关系
        relationRepository.saveAndFlush(relationDO);

        //入库资料
        List<MemberDepositoryDetailDO> depositoryDetailList = depositCheckResult;

        //判断是否需要入库审核，启动入库工作流
        ProcessBO depositProcess = memberProcessRuleService.findMemberProcessKey(upperMemberId, upperRoleId, subRoleDO, MemberProcessTypeEnum.MEMBER_DEPOSITORY);
        if(depositProcess.getEmptyProcess()) {
            relationDO.getValidateTask().setProcessKey(depositProcess.getProcessKey());
            //内外状态
            relationDO.setInnerStatus(MemberInnerStatusEnum.VERIFY_PASSED.getCode());
            relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode());
            relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
            //入库时间
            relationDO.setDepositTime(LocalDateTime.now());

            //入库资料设置为“正在使用”的版本
            depositoryDetailList.forEach(detail -> detail.setVersion(MemberDetailVersionEnum.USING.getCode()));
        } else {
            WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(depositProcess.getProcessKey(), upperMemberId, upperRoleId, relationDO.getId());

            relationDO.getValidateTask().setTaskId(taskResult.getTaskId());
            relationDO.getValidateTask().setProcessKey(depositProcess.getProcessKey());

            //内外状态
            relationDO.setInnerStatus(taskResult.getInnerStatus());
            relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITING.getCode());
            relationDO.setVerified(MemberValidateStatusEnum.VERIFY_NOT_PASSED.getCode());
        }

        //入库资料设置关联的MemberRelationDO
        depositoryDetailList.forEach(detail -> detail.setRelation(relationDO));
        memberDepositoryDetailRepository.saveAll(depositoryDetailList);
        // 兼容list字段，修改成循环保存
        for (MemberDepositoryDetailDO depositoryDetail : depositoryDetailList) {
//            memberDepositoryDetailRepository.save(depositoryDetail);
            if (!CollectionUtils.isEmpty(depositoryDetail.getDepositorys())) {
                List<MemberDepositoryDetailDO> depositorys = depositoryDetail.getDepositorys();
                depositorys.forEach(depository -> depository.setParentId(depositoryDetail.getId()));
                memberDepositoryDetailRepository.saveAll(depositorys);
            }
        }

        //资质文件
        List<MemberQualityDO> qualityList = qualityCheckResult.getData();
        qualityList.forEach(quality -> quality.setRelation(relationDO));
        memberQualityRepository.saveAll(qualityList);

        relationDO.setDepositDetails(new HashSet<>(depositoryDetailList));
        relationDO.setQualities(new HashSet<>(qualityList));

        //第二步骤：设置“下级”的等级、权益、信用等信息
        MemberLevelRightDO levelDO = new MemberLevelRightDO();
        levelDO.setMemberId(upperMemberDO.getId());
        levelDO.setRoleId(upperRoleDO.getId());
        levelDO.setSubMemberId(subMemberDO.getId());
        levelDO.setSubRoleId(subRoleDO.getId());
        levelDO.setLevelConfig(memberLevelConfigDO);
        levelDO.setLevel(memberLevelConfigDO == null ? 0 : memberLevelConfigDO.getLevel());
        levelDO.setLevelTag(memberLevelConfigDO == null ? "" : memberLevelConfigDO.getLevelTag());
        levelDO.setScore(0);
        levelDO.setCurrentPoint(0);
        levelDO.setSumPoint(0);
        levelDO.setSumReturnMoney(new BigDecimal(0));
        levelDO.setSumUsedPoint(0);
        levelDO.setRelation(relationDO);

        //设置信用信息
        MemberCreditDO creditDO = new MemberCreditDO();
        creditDO.setMemberId(upperMemberDO.getId());
        creditDO.setRoleId(upperRoleDO.getId());
        creditDO.setSubMemberId(subMemberDO.getId());
        creditDO.setSubRoleId(subRoleDO.getId());
        creditDO.setAfterSaleCommentPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setCreditPoint(0);
        creditDO.setComplainPoint(0);
        creditDO.setRegisterYearsPoint(0);
        creditDO.setTradeCommentPoint(0);
        creditDO.setRelation(relationDO);
        creditDO.setRegisterPointUpdateTime(LocalDateTime.now());
        creditDO.setTradeCommentStars(0);
        creditDO.setTradeCommentCount(0);
        creditDO.setAvgTradeCommentStar(MemberConstant.DEFAULT_TRADE_COMMENT_STAR);

        //关联到会员关系
        relationDO.setLevelRight(levelDO);
        relationDO.setCredit(creditDO);

        // 存在邀请消息则更新
        MemberReceiveInvitationDO receiveInvitation = memberReceiveInvitationRepository.findByMemberIdAndRoleIdAndSubMemberIdAndSubRoleId(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId());
        Integer roleTag = RoleTagEnum.MEMBER.getCode();
        if (Objects.nonNull(receiveInvitation)) {
            roleTag = relationDO.getSubRoleTag();
            receiveInvitation.setRegisterTime(relationDO.getCreateTime());
            receiveInvitation.setFillInDepositoryDetail(CommonBooleanEnum.YES.getCode());
            memberReceiveInvitationService.saveInvitation(receiveInvitation);
        }

        relationRepository.saveAndFlush(relationDO);

        //第六步骤：外部审核记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, subRoleDO.getRoleName(),  MemberValidateHistoryOperationEnum.APPLY_FOR_SUB_MEMBER,MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()),MemberValidateHistoryOperationEnum.APPLY_FOR_MEMBER.getMessage());

        //向上级会员发送消息
        messageFeignService.sendMemberValidateMessage(relationDO, roleTag );

        //如果审核通过，通知支付服务创建资金账户
        payFeignService.notifyMemberAssetAccount(relationDO);
        payFeignService.notifyMemberCredit(relationDO);

        return WrapperUtil.success();
    }

    /**
     * 会员被淘汰后，重新申请为下级会员
     * @param relationDO 被淘汰的会员关系
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public WrapperResp<Void> reApplyForSubMember(MemberRelationDO relationDO) {
        //查找、启动会员入库流程
        ProcessBO depositProcess = memberProcessRuleService.findMemberProcessKey(relationDO, MemberProcessTypeEnum.MEMBER_DEPOSITORY);
        relationDO.getValidateTask().setProcessKey(depositProcess.getProcessKey());
        relationDO.getValidateTask().setProcessTypeEnum(MemberProcessTypeEnum.MEMBER_DEPOSITORY.getCode());

        //重置状态为“正常”
        relationDO.setValidateMsg("");
        relationDO.setStatus(MemberStatusEnum.NORMAL.getCode());

        if(depositProcess.getEmptyProcess()) {
            relationDO.setVerified(MemberValidateStatusEnum.VERIFY_PASSED.getCode());
            relationDO.setInnerStatus(MemberInnerStatusEnum.VERIFY_PASSED.getCode());
            relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITORY_PASSED.getCode());
            relationDO.setDepositTime(LocalDateTime.now());
        } else {
            WorkflowTaskResultBO taskResult = workflowFeignService.startMemberProcess(relationDO);
            relationDO.setInnerStatus(taskResult.getInnerStatus());
            relationDO.getValidateTask().setTaskId(taskResult.getTaskId());
            relationDO.setOuterStatus(MemberOuterStatusEnum.DEPOSITING.getCode());
            relationRepository.saveAndFlush(relationDO);
        }

        //外部历史记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getSubRoleName(), MemberValidateHistoryOperationEnum.APPLY_FOR_SUB_MEMBER, MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()), "");

        //如果审核通过，通知支付服务创建资金账户
        payFeignService.notifyMemberAssetAccount(relationDO);
        payFeignService.notifyMemberCredit(relationDO);

        return WrapperUtil.success();
    }

    /**
     * 查询上级会员的所有上下级关系，并筛选出包含下级会员Id的关系
     * @param relationDOList 所有会员关系
     * @param memberId 上级会员Id
     * @param roleId 上级会员角色Id
     * @param subMemberId 下级会员Id
     * @return 上级会员的上下级关系中包含下级会员Id的所有关系
     */
    private List<MemberRelationDO> findUpperMemberRelationList(List<MemberRelationDO> relationDOList, Long memberId, Long roleId, Long subMemberId) {
        if(CollectionUtils.isEmpty(relationDOList)) {
            return new ArrayList<>();
        }

        List<MemberRelationDO> upperMemberRelationDOList = new ArrayList<>();
        //查询所有上级
        List<MemberRelationDO> upperList = relationDOList.stream().filter(relation -> relation.getSubMemberId().equals(memberId) && relation.getSubRoleId().equals(roleId)).collect(Collectors.toList());
        while (!CollectionUtils.isEmpty(upperList)) {
            upperMemberRelationDOList.addAll(upperList);

            //再次查询上级
            List<MemberRelationDO> finalUpperList = upperList;
            upperList = relationDOList.stream().filter(relationDO -> finalUpperList.stream().anyMatch(upper -> relationDO.getSubMemberId().equals(upper.getMemberId()) && relationDO.getSubRoleId().equals(upper.getRoleId()))).collect(Collectors.toList());
            upperList.removeIf(u -> upperMemberRelationDOList.stream().anyMatch(exist -> exist.getMemberId().equals(u.getMemberId()) && exist.getRoleId().equals(u.getRoleId()) && exist.getSubMemberId().equals(u.getSubMemberId()) && exist.getSubRoleId().equals(u.getSubRoleId())));
        }

        //查询所有下级
        List<MemberRelationDO> subList = relationDOList.stream().filter(relation -> relation.getMemberId().equals(memberId) && relation.getRoleId().equals(roleId)).collect(Collectors.toList());
        while (!CollectionUtils.isEmpty(subList)) {
            upperMemberRelationDOList.addAll(subList);

            //再次查询下级
            List<MemberRelationDO> finalSubList = subList;
            subList = relationDOList.stream().filter(relationDO -> finalSubList.stream().anyMatch(sub -> relationDO.getMemberId().equals(sub.getSubMemberId()) && relationDO.getRoleId().equals(sub.getSubRoleId()))).collect(Collectors.toList());
            subList.removeIf(u -> upperMemberRelationDOList.stream().anyMatch(exist -> exist.getMemberId().equals(u.getMemberId()) && exist.getRoleId().equals(u.getRoleId()) && exist.getSubMemberId().equals(u.getSubMemberId()) && exist.getSubRoleId().equals(u.getSubRoleId())));
        }

        return upperMemberRelationDOList.stream().filter(relationDO -> relationDO.getMemberId().equals(subMemberId) || relationDO.getSubMemberId().equals(subMemberId)).collect(Collectors.toList());
    }

    /**
     * 生成唯一的邀请码
     * @return 唯一的邀请码
     */
    private String generateUniqueInvitationCode() {
        String invitationCode;
        int maxRetries = 10; // 最大重试次数
        int retries = 0;

        do {
            invitationCode = CodeUtil.generateInvitationCode();
            retries++;
        } while (userRepository.existsByInvitationCode(invitationCode) && retries < maxRetries);

        if (retries >= maxRetries) {
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR, "生成邀请码失败，请重试");
        }

        return invitationCode;
    }
}
