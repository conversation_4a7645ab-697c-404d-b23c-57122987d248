package com.ssy.lingxi.member.serviceImpl.commission;

import cn.hutool.core.util.StrUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.commission.BankCardDO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionWithdrawalDO;
import com.ssy.lingxi.member.entity.do_.commission.CommissionAccountDO;
import com.ssy.lingxi.member.repository.commission.CommissionAccountRepository;
import com.ssy.lingxi.member.enums.commission.WithdrawalExternalStatusEnum;
import com.ssy.lingxi.member.enums.commission.WithdrawalInternalStatusEnum;
import com.ssy.lingxi.member.model.req.commission.CommissionWithdrawalQueryReq;
import com.ssy.lingxi.member.model.req.commission.WithdrawalManagementQueryReq;
import com.ssy.lingxi.member.model.resp.commission.CommissionWithdrawalResp;
import com.ssy.lingxi.member.model.resp.commission.WithdrawalManagementResp;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.repository.commission.BankCardRepository;
import com.ssy.lingxi.member.repository.commission.CommissionWithdrawalRepository;
import com.ssy.lingxi.member.service.commission.ICommissionWithdrawalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ssy.lingxi.component.base.model.BusinessException;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.ServletOutputStream;
import java.io.IOException;
import java.net.URLEncoder;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 佣金提现服务实现
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Slf4j
@Service
public class CommissionWithdrawalServiceImpl implements ICommissionWithdrawalService {

    @Resource
    private CommissionWithdrawalRepository commissionWithdrawalRepository;

    @Resource
    private BankCardRepository bankCardRepository;

    @Resource
    private CommissionAccountRepository commissionAccountRepository;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public PageDataResp<CommissionWithdrawalResp> getCommissionWithdrawalPage(UserLoginCacheDTO loginUser, CommissionWithdrawalQueryReq request) {
        // 构建查询条件
        Specification<CommissionWithdrawalDO> spec = buildCommissionWithdrawalSpecification(request);
        
        // 分页参数，按创建时间倒序
        Pageable pageable = PageRequest.of(
            request.getCurrent() - 1, 
            request.getPageSize(),
            Sort.by(Sort.Direction.DESC, "createTime")
        );

        // 执行分页查询
        Page<CommissionWithdrawalDO> page = commissionWithdrawalRepository.findAll(spec, pageable);
        
        // 转换为响应对象
        List<CommissionWithdrawalResp> list = page.getContent().stream()
                .map(this::convertToCommissionWithdrawalResp)
                .collect(Collectors.toList());

        return new PageDataResp<>(page.getTotalElements(), list);
    }

    @Override
    public PageDataResp<WithdrawalManagementResp> getWithdrawalManagementPage(UserLoginCacheDTO loginUser, WithdrawalManagementQueryReq request) {
        // 构建查询条件
        Specification<CommissionWithdrawalDO> spec = buildWithdrawalManagementSpecification(request);
        
        // 分页参数，按创建时间倒序
        Pageable pageable = PageRequest.of(
            request.getCurrent() - 1, 
            request.getPageSize(),
            Sort.by(Sort.Direction.DESC, "createTime")
        );

        // 执行分页查询
        Page<CommissionWithdrawalDO> page = commissionWithdrawalRepository.findAll(spec, pageable);
        
        // 批量获取关联数据
        List<Long> userIds = page.getContent().stream()
                .map(CommissionWithdrawalDO::getUserId)
                .collect(Collectors.toList());
        
        Map<Long, MemberDO> memberMap = getMemberMap(userIds);

        // 转换为响应对象
        List<WithdrawalManagementResp> list = page.getContent().stream()
                .map(withdrawal -> convertToWithdrawalManagementResp(withdrawal, memberMap))
                .collect(Collectors.toList());

        return new PageDataResp<>(page.getTotalElements(), list);
    }

    // ==================== 私有方法 ====================

    /**
     * 构建佣金提现记录查询条件
     */
    private Specification<CommissionWithdrawalDO> buildCommissionWithdrawalSpecification(CommissionWithdrawalQueryReq request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 分佣账户id过滤
            if (request.getCommissionAccountId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("commissionAccountId"), request.getCommissionAccountId()));
            }

            // 时间范围过滤
            if (request.getStartTime() != null && request.getEndTime() != null) {
                predicates.add(criteriaBuilder.between(root.get("createTime"), request.getStartTime(), request.getEndTime()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 构建提现管理查询条件
     */
    private Specification<CommissionWithdrawalDO> buildWithdrawalManagementSpecification(WithdrawalManagementQueryReq request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 时间范围过滤
            if (request.getStartTime() != null && request.getEndTime() != null) {
                predicates.add(criteriaBuilder.between(root.get("createTime"), request.getStartTime(), request.getEndTime()));
            }

            // 外部状态过滤（通过内部状态映射）
            if (request.getExternalStatus() != null) {
                List<Integer> internalStatuses = getInternalStatusesByExternalStatus(request.getExternalStatus());
                if (!internalStatuses.isEmpty()) {
                    predicates.add(root.get("withdrawalStatus").in(internalStatuses));
                }
            }

            // 内部状态过滤
            if (request.getInternalStatus() != null) {
                Integer withdrawalStatus = mapInternalStatusToWithdrawalStatus(request.getInternalStatus());
                if (withdrawalStatus != null) {
                    predicates.add(criteriaBuilder.equal(root.get("withdrawalStatus"), withdrawalStatus));
                }
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 批量获取会员信息
     */
    private Map<Long, MemberDO> getMemberMap(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }
        // 由于MemberRepository没有findByUserIdIn方法，这里暂时返回空Map
        // 实际应该根据业务需求添加相应的查询方法
        return Collections.emptyMap();
    }

    /**
     * 转换为佣金提现记录响应对象
     */
    private CommissionWithdrawalResp convertToCommissionWithdrawalResp(CommissionWithdrawalDO withdrawal) {
        CommissionWithdrawalResp resp = new CommissionWithdrawalResp();
        resp.setId(withdrawal.getId());
        resp.setCommissionAccountId(withdrawal.getCommissionAccountId());
        resp.setUserId(withdrawal.getUserId());
        resp.setBankCardId(withdrawal.getBankCardId());
        resp.setWithdrawalCode("WD" + withdrawal.getId()); // 生成提现编码
        resp.setWithdrawalAmount(withdrawal.getWithdrawalAmount());
        resp.setServiceFee(BigDecimal.ZERO); // 需要根据实际字段调整
        resp.setActualAmount(withdrawal.getWithdrawalAmount()); // 暂时等于提现金额
        resp.setWithdrawalStatus(withdrawal.getWithdrawalStatus());
        resp.setFailureReason(withdrawal.getFailureReason());
        resp.setRemark(withdrawal.getRemark());

        // 设置提现状态名称
        resp.setWithdrawalStatusName(getWithdrawalStatusName(withdrawal.getWithdrawalStatus()));

        // 查询银行卡信息
        if (withdrawal.getBankCardId() != null) {
            Optional<BankCardDO> bankCardOpt = bankCardRepository.findById(withdrawal.getBankCardId());
            if (bankCardOpt.isPresent()) {
                BankCardDO bankCard = bankCardOpt.get();
                resp.setBankCardNumber(maskCardNumberToLast4(bankCard.getCardNumber()));
                resp.setBankName(bankCard.getBankName());
                resp.setCardHolderName(bankCard.getCardHolderName());
            }
        }

        // 格式化时间
        resp.setCreateTime(formatTime(withdrawal.getCreateTime()));
        resp.setProcessTime(formatTime(withdrawal.getProcessTime()));

        return resp;
    }

    /**
     * 转换为提现管理响应对象
     */
    private WithdrawalManagementResp convertToWithdrawalManagementResp(CommissionWithdrawalDO withdrawal, Map<Long, MemberDO> memberMap) {
        WithdrawalManagementResp resp = new WithdrawalManagementResp();
        resp.setId(withdrawal.getId());
        resp.setCommissionAccountId(withdrawal.getCommissionAccountId());
        resp.setUserId(withdrawal.getUserId());
        resp.setWithdrawalTradeCode(withdrawal.getWithdrawalTradeCode());
        resp.setWithdrawalAmount(withdrawal.getWithdrawalAmount());
        resp.setBankCardHolderName(withdrawal.getBankCardHolderName());
        resp.setBankCardNumber(maskCardNumberToLast4(withdrawal.getBankCardNumber()));
        resp.setBankName(withdrawal.getBankName());
        resp.setBranchName(withdrawal.getBranchName());
        resp.setFailureReason(withdrawal.getFailureReason());
        resp.setProcessUserName(withdrawal.getProcessUserName());
        resp.setRemark(withdrawal.getRemark());

        // 设置会员信息（暂时使用模拟数据）
        MemberDO member = memberMap.get(withdrawal.getUserId());
        if (member != null) {
            resp.setMemberId(member.getId());
            resp.setCustomerName(member.getName());
            resp.setCustomerCode(member.getCode());
            resp.setCustomerType(1); // 默认企业会员，实际应该通过会员角色获取
        } else {
            // 模拟数据
            resp.setMemberId(1L);
            resp.setCustomerName("客户" + withdrawal.getUserId());
            resp.setCustomerCode("C" + String.format("%06d", withdrawal.getUserId()));
            resp.setCustomerType(1);
        }

        // 设置客户类型名称
        if (resp.getCustomerType() != null) {
            resp.setCustomerTypeName(resp.getCustomerType() == 1 ? "企业会员" : "个人会员");
        }

        // 根据原有状态映射内部状态和外部状态
        WithdrawalInternalStatusEnum internalStatusEnum = WithdrawalInternalStatusEnum.getByWithdrawalStatus(withdrawal.getWithdrawalStatus());
        if (internalStatusEnum != null) {
            resp.setInternalStatus(internalStatusEnum.getCode());
            resp.setInternalStatusName(internalStatusEnum.getMessage());
        }

        WithdrawalExternalStatusEnum externalStatusEnum = WithdrawalExternalStatusEnum.getByInternalStatus(resp.getInternalStatus());
        if (externalStatusEnum != null) {
            resp.setExternalStatus(externalStatusEnum.getCode());
            resp.setExternalStatusName(externalStatusEnum.getMessage());
        }

        // 格式化时间
        resp.setCreateTime(formatTime(withdrawal.getCreateTime()));
        resp.setProcessTime(formatTime(withdrawal.getProcessTime()));
        resp.setSuccessTime(formatTime(withdrawal.getSuccessTime()));

        return resp;
    }

    /**
     * 根据外部状态获取对应的内部状态列表
     */
    private List<Integer> getInternalStatusesByExternalStatus(Integer externalStatus) {
        List<Integer> internalStatuses = new ArrayList<>();
        if (externalStatus == null) {
            return internalStatuses;
        }

        switch (externalStatus) {
            case 1: // 申请提现
                internalStatuses.add(1); // 待审核
                break;
            case 2: // 审核中
                internalStatuses.add(2); // 待打款
                break;
            case 3: // 提现完成
                internalStatuses.add(3); // 已打款
                break;
            case 4: // 提现失败
                internalStatuses.add(4); // 打款失败
                internalStatuses.add(5); // 审核失败
                break;
        }

        return internalStatuses;
    }

    /**
     * 将内部状态映射到原有的提现状态
     */
    private Integer mapInternalStatusToWithdrawalStatus(Integer internalStatus) {
        if (internalStatus == null) {
            return null;
        }

        switch (internalStatus) {
            case 1: // 待审核
                return 1; // 申请中
            case 2: // 待打款
                return 2; // 处理中
            case 3: // 已打款
                return 3; // 提现成功
            case 4: // 打款失败
                return 4; // 提现失败
            case 5: // 审核失败
                return 5; // 已取消
            default:
                return null;
        }
    }

    /**
     * 银行卡号只显示后4位
     */
    private String maskCardNumberToLast4(String cardNumber) {
        if (StrUtil.isBlank(cardNumber) || cardNumber.length() < 4) {
            return cardNumber;
        }
        return "****" + cardNumber.substring(cardNumber.length() - 4);
    }

    /**
     * 获取提现状态名称
     */
    private String getWithdrawalStatusName(Integer status) {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 1: return "申请中";
            case 2: return "处理中";
            case 3: return "提现成功";
            case 4: return "提现失败";
            case 5: return "已取消";
            default: return "未知状态";
        }
    }

    /**
     * 格式化时间戳
     */
    private String formatTime(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return LocalDateTime.ofEpochSecond(timestamp / 1000, 0, ZoneOffset.ofHours(8))
                .format(DATE_TIME_FORMATTER);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void approveWithdrawal(UserLoginCacheDTO loginUser, Long withdrawalId, Boolean approved, String remark) {
        // 查询提现记录
        CommissionWithdrawalDO withdrawal = commissionWithdrawalRepository.findById(withdrawalId)
                .orElseThrow(() -> new BusinessException("提现记录不存在"));

        // 检查当前状态是否可以审核
        if (!Integer.valueOf(1).equals(withdrawal.getWithdrawalStatus())) {
            throw new BusinessException("当前状态不允许审核操作");
        }

        // 更新状态和相关信息
        if (approved) {
            // 审核通过 -> 待打款
            withdrawal.setWithdrawalStatus(2);
        } else {
            // 审核拒绝 -> 审核失败
            withdrawal.setWithdrawalStatus(5);
            withdrawal.setFailureReason(remark);

            // 审核拒绝时，恢复可提现余额，扣减提现中金额
            CommissionAccountDO account = commissionAccountRepository.findById(withdrawal.getCommissionAccountId())
                    .orElseThrow(() -> new BusinessException("分佣账户不存在"));

            BigDecimal withdrawalAmount = withdrawal.getWithdrawalAmount();
            account.setWithdrawableBalance(account.getWithdrawableBalance().add(withdrawalAmount));
            if (account.getWithdrawingAmount() != null) {
                account.setWithdrawingAmount(account.getWithdrawingAmount().subtract(withdrawalAmount));
            }
            account.setUpdateTime(System.currentTimeMillis());

            commissionAccountRepository.saveAndFlush(account);

            log.info("审核拒绝，已恢复可提现余额：{}，用户id：{}", withdrawalAmount, account.getUserId());
        }

        // 设置处理信息
        withdrawal.setProcessUserName(loginUser.getUserName());
        withdrawal.setProcessTime(System.currentTimeMillis());
        withdrawal.setRemark(remark);

        // 保存更新
        commissionWithdrawalRepository.saveAndFlush(withdrawal);

        log.info("提现审核操作完成，提现id：{}，审核结果：{}，操作人：{}",
                withdrawalId, approved ? "通过" : "拒绝", loginUser.getUserName());

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void processPayment(UserLoginCacheDTO loginUser, Long withdrawalId, Boolean success, String remark) {
        // 查询提现记录
        CommissionWithdrawalDO withdrawal = commissionWithdrawalRepository.findById(withdrawalId)
                .orElseThrow(() -> new BusinessException("提现记录不存在"));

        // 检查当前状态是否可以打款
        if (!Integer.valueOf(2).equals(withdrawal.getWithdrawalStatus())) {
            throw new BusinessException("当前状态不允许打款操作");
        }

        // 查询分佣账户
        CommissionAccountDO account = commissionAccountRepository.findById(withdrawal.getCommissionAccountId())
                .orElseThrow(() -> new BusinessException("分佣账户不存在"));

        BigDecimal withdrawalAmount = withdrawal.getWithdrawalAmount();

        // 更新状态和相关信息
        if (success) {
            // 打款成功 -> 已打款
            withdrawal.setWithdrawalStatus(3);
            withdrawal.setSuccessTime(System.currentTimeMillis());

            // 扣减提现中金额（钱已经打出去了）
            if (account.getWithdrawingAmount() != null) {
                account.setWithdrawingAmount(account.getWithdrawingAmount().subtract(withdrawalAmount));
            }
        } else {
            // 打款失败 -> 打款失败
            withdrawal.setWithdrawalStatus(4);
            withdrawal.setFailureReason(remark);

            // 恢复可提现余额，扣减提现中金额
            account.setWithdrawableBalance(account.getWithdrawableBalance().add(withdrawalAmount));
            if (account.getWithdrawingAmount() != null) {
                account.setWithdrawingAmount(account.getWithdrawingAmount().subtract(withdrawalAmount));
            }
        }

        // 设置处理信息
        withdrawal.setProcessUserName(loginUser.getUserName());
        withdrawal.setProcessTime(System.currentTimeMillis());
        withdrawal.setRemark(remark);

        // 更新账户时间
        account.setUpdateTime(System.currentTimeMillis());

        // 保存更新
        commissionWithdrawalRepository.saveAndFlush(withdrawal);
        commissionAccountRepository.saveAndFlush(account);

        log.info("提现打款操作完成，提现id：{}，打款结果：{}，操作人：{}，提现金额：{}",
                withdrawalId, success ? "成功" : "失败", loginUser.getUserName(), withdrawalAmount);

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void applyWithdrawal(UserLoginCacheDTO loginUser, Long bankCardId, BigDecimal withdrawalAmount) {
        // 1. 参数校验
        if (bankCardId == null) {
            throw new BusinessException("请选择银行卡");
        }
        if (withdrawalAmount == null || withdrawalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("提现金额必须大于0");
        }

        // 2. 查询用户的分佣账户
        CommissionAccountDO account = commissionAccountRepository.findByUserId(loginUser.getUserId())
                .orElseThrow(() -> new BusinessException("分佣账户不存在"));

        // 3. 校验账户状态
        if (!Integer.valueOf(1).equals(account.getAccountStatus())) {
            throw new BusinessException("账户已冻结，无法提现");
        }

        // 4. 校验可提现余额
        if (account.getWithdrawableBalance() == null ||
            account.getWithdrawableBalance().compareTo(withdrawalAmount) < 0) {
            throw new BusinessException("可提现余额不足");
        }

        // 5. 查询并校验银行卡
        BankCardDO bankCard = bankCardRepository.findById(bankCardId)
                .orElseThrow(() -> new BusinessException("银行卡不存在"));


        // 校验银行卡状态
        if (!Integer.valueOf(1).equals(bankCard.getStatus())) {
            throw new BusinessException("银行卡状态异常，无法提现");
        }

        // 6. 创建提现记录
        CommissionWithdrawalDO withdrawal = new CommissionWithdrawalDO();
        withdrawal.setCommissionAccountId(account.getId());
        withdrawal.setUserId(loginUser.getUserId());
        withdrawal.setBankCardId(bankCardId);
        withdrawal.setWithdrawalAmount(withdrawalAmount);
        withdrawal.setWithdrawalStatus(1); // 待审核状态
        withdrawal.setWithdrawalTradeCode(generateWithdrawalTradeCode());

        // 设置银行卡信息（冗余存储，避免银行卡信息变更影响提现记录）
        withdrawal.setBankCardHolderName(bankCard.getCardHolderName());
        withdrawal.setBankCardNumber(bankCard.getCardNumber());
        withdrawal.setBankName(bankCard.getBankName());
        withdrawal.setBranchName(bankCard.getBranchName());

        // 设置时间
        long currentTime = System.currentTimeMillis();
        withdrawal.setCreateTime(currentTime);
        withdrawal.setUpdateTime(currentTime);

        // 7. 更新账户余额（扣减可提现余额，增加提现中金额）
        account.setWithdrawableBalance(account.getWithdrawableBalance().subtract(withdrawalAmount));
        if (account.getWithdrawingAmount() == null) {
            account.setWithdrawingAmount(withdrawalAmount);
        } else {
            account.setWithdrawingAmount(account.getWithdrawingAmount().add(withdrawalAmount));
        }
        account.setUpdateTime(currentTime);

        // 8. 保存数据
        commissionWithdrawalRepository.saveAndFlush(withdrawal);
        commissionAccountRepository.saveAndFlush(account);

        log.info("用户申请提现成功，用户id：{}，提现金额：{}，银行卡id：{}",
                loginUser.getUserId(), withdrawalAmount, bankCardId);

        return null;
    }

    /**
     * 生成提现流水号
     */
    private String generateWithdrawalTradeCode() {
        // 格式：WD + yyyyMMdd + 6位随机数
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        int randomNum = (int) (Math.random() * 900000) + 100000; // 6位随机数
        return "WD" + dateStr + randomNum;
    }

    @Override
    public void exportWithdrawalManagement(UserLoginCacheDTO loginUser, WithdrawalManagementQueryReq request, HttpServletResponse response) {
        // 构建查询条件（不分页，查询所有符合条件的数据）
        Specification<CommissionWithdrawalDO> spec = buildWithdrawalManagementSpecification(request);

        // 按创建时间倒序查询所有数据
        List<CommissionWithdrawalDO> withdrawalList = commissionWithdrawalRepository.findAll(spec,
                Sort.by(Sort.Direction.DESC, "createTime"));

        // 批量获取关联数据
        List<Long> userIds = withdrawalList.stream()
                .map(CommissionWithdrawalDO::getUserId)
                .collect(Collectors.toList());

        Map<Long, MemberDO> memberMap = getMemberMap(userIds);

        // 转换为导出数据
        List<Map<String, Object>> dataList = withdrawalList.stream()
                .map(withdrawal -> convertToExportData(withdrawal, memberMap))
                .collect(Collectors.toList());

        // 定义表头
        List<String> headers = Arrays.asList(
                "提现流水号", "客户名称", "客户编码", "客户类型", "提现金额（元）",
                "提现银行卡姓名", "提现银行卡账号", "提现银行", "提现支行",
                "提现发起时间", "外部状态", "内部状态", "失败原因",
                "处理人姓名", "处理时间", "备注"
        );

        try {
            // 生成文件名
            String fileName = "提现管理列表_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));

            // 使用hutool的ExcelWriter导出
            ExcelWriter writer = ExcelUtil.getBigWriter();

            // 设置表头
            for (int i = 0; i < headers.size(); i++) {
                writer.addHeaderAlias(headers.get(i), headers.get(i));
            }

            // 写入数据
            writer.write(dataList, true);

            // 设置响应头
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));

            // 输出到响应流
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
            out.close();

            log.info("导出提现管理列表成功，操作人：{}，导出条数：{}", loginUser.getUserName(), dataList.size());
        } catch (Exception e) {
            log.error("导出提现管理列表失败，操作人：{}，错误信息：{}", loginUser.getUserName(), e.getMessage(), e);
            throw new BusinessException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 转换为导出数据
     */
    private Map<String, Object> convertToExportData(CommissionWithdrawalDO withdrawal, Map<Long, MemberDO> memberMap) {
        Map<String, Object> data = new LinkedHashMap<>();

        // 基础信息
        data.put("提现流水号", withdrawal.getWithdrawalTradeCode());

        // 客户信息
        MemberDO member = memberMap.get(withdrawal.getUserId());
        if (member != null) {
            data.put("客户名称", member.getName());
            data.put("客户编码", member.getCode());
            data.put("客户类型", "企业会员"); // 默认企业会员，实际应该通过会员角色获取
        } else {
            // 模拟数据
            data.put("客户名称", "客户" + withdrawal.getUserId());
            data.put("客户编码", "C" + String.format("%06d", withdrawal.getUserId()));
            data.put("客户类型", "企业会员");
        }

        // 提现信息
        data.put("提现金额（元）", withdrawal.getWithdrawalAmount());
        data.put("提现银行卡姓名", withdrawal.getBankCardHolderName());
        data.put("提现银行卡账号", maskCardNumberToLast4(withdrawal.getBankCardNumber()));
        data.put("提现银行", withdrawal.getBankName());
        data.put("提现支行", withdrawal.getBranchName());

        // 时间信息
        data.put("提现发起时间", formatTime(withdrawal.getCreateTime()));

        // 状态信息
        WithdrawalInternalStatusEnum internalStatusEnum = WithdrawalInternalStatusEnum.getByWithdrawalStatus(withdrawal.getWithdrawalStatus());
        if (internalStatusEnum != null) {
            data.put("内部状态", internalStatusEnum.getMessage());

            WithdrawalExternalStatusEnum externalStatusEnum = WithdrawalExternalStatusEnum.getByInternalStatus(internalStatusEnum.getCode());
            if (externalStatusEnum != null) {
                data.put("外部状态", externalStatusEnum.getMessage());
            } else {
                data.put("外部状态", "");
            }
        } else {
            data.put("外部状态", "");
            data.put("内部状态", "");
        }

        // 其他信息
        data.put("失败原因", withdrawal.getFailureReason() != null ? withdrawal.getFailureReason() : "");
        data.put("处理人姓名", withdrawal.getProcessUserName() != null ? withdrawal.getProcessUserName() : "");
        data.put("处理时间", formatTime(withdrawal.getProcessTime()));
        data.put("备注", withdrawal.getRemark() != null ? withdrawal.getRemark() : "");

        return data;
    }
}
