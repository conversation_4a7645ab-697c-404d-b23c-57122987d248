package com.ssy.lingxi.member.serviceImpl.web;

import com.ssy.lingxi.commodity.api.feign.ICountryAreaFeign;
import com.ssy.lingxi.commodity.api.model.resp.support.CountryAreaResp;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.*;
import com.ssy.lingxi.component.base.idGenerate.IIdGenerate;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.PasswordUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.config.ThreadPoolConfig;
import com.ssy.lingxi.member.constant.MemberConstant;
import com.ssy.lingxi.member.constant.MemberRedisConstant;
import com.ssy.lingxi.member.entity.bo.AddMemberBO;
import com.ssy.lingxi.member.entity.bo.ProcessBO;
import com.ssy.lingxi.member.entity.bo.UpdatePlatformMemberBO;
import com.ssy.lingxi.member.entity.bo.WorkflowTaskResultBO;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRelationDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.detail.MemberRegisterDetailDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelConfigDO;
import com.ssy.lingxi.member.entity.do_.levelRight.MemberLevelRightDO;
import com.ssy.lingxi.member.enums.*;
import com.ssy.lingxi.member.model.req.basic.MemberIdAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.resp.basic.LevelAndTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.WorkFlowStepResp;
import com.ssy.lingxi.member.repository.*;
import com.ssy.lingxi.member.service.base.*;
import com.ssy.lingxi.member.service.feign.IPayFeignService;
import com.ssy.lingxi.member.service.feign.IProductFeignService;
import com.ssy.lingxi.member.service.feign.ITemplateFeignService;
import com.ssy.lingxi.member.service.feign.IWorkflowFeignService;
import com.ssy.lingxi.member.service.web.IPlatformMemberMaintenanceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 平台后台 - 会员维护相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-07
 */
@Service
public class PlatformMemberMaintenanceServiceImpl implements IPlatformMemberMaintenanceService {
    @Resource
    private IBaseMemberValidateService baseMemberValidateService;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private IBaseMemberCacheService memberCacheService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private MemberRoleRepository memberRoleRepository;

    @Resource
    private MemberLevelConfigRepository levelConfigRepository;

    @Resource
    private ICountryAreaFeign countryAreaFeign;

    @Resource
    private IBasePlatformProcessService basePlatformProcessService;

    @Resource
    private IBaseMemberHistoryService baseMemberHistoryService;

    @Resource
    private MemberRelationRepository relationRepository;

    @Resource
    private IBaseMemberRegisterDetailService baseMemberRegisterDetailService;

    @Resource
    private IBaseMemberInnerService memberInnerService;

    @Resource
    private IWorkflowFeignService baseWorkflowService;

    @Resource
    private IPayFeignService payFeignService;

    @Resource
    private ITemplateFeignService platformTemplateFeignService;

    @Resource
    private IBaseMemberLevelConfigService baseMemberLevelConfigService;

    @Resource
    private IProductFeignService productFeignService;

    @Resource
    private IIdGenerate idGenerate;

    @Resource
    private IBaseTokenManageService tokenManageService;

    @Resource
    private IRedisUtils redisUtils;

    /**
     * 获取分页查询页面中各个查询条件下拉选择框的内容
     * @param headers Http头部信息
     * @return 操作结果
     */
    @Override
    public PlatformMemberQuerySearchConditionResp getPageCondition(HttpHeaders headers) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        PlatformMemberQuerySearchConditionResp conditionVO = new PlatformMemberQuerySearchConditionResp();
        //内部状态
        conditionVO.setInnerStatus(PlatformInnerStatusEnum.toDropdownList());

        //外部状态
        conditionVO.setOuterStatus(Stream.of(MemberOuterStatusEnum.TO_PLATFORM_VERIFY, MemberOuterStatusEnum.PLATFORM_VERIFYING, MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED, MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED, MemberOuterStatusEnum.CANCELLATION).map(e -> new DropdownItemResp(e.getCode(), e.getMessage())).collect(Collectors.toList()));

        //会员状态
        conditionVO.setStatus(Stream.of(MemberStatusEnum.NORMAL, MemberStatusEnum.FROZEN, MemberStatusEnum.CANCELLATION).map(s -> new DropdownItemResp(s.getCode(), s.getName())).collect(Collectors.toList()));

        //会员类型
        conditionVO.setMemberTypes(baseMemberValidateService.getSubMemberTypeList(Arrays.asList(MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

        //会员角色（按照Id升序排序）
        conditionVO.setMemberRoles(memberRoleRepository.findAll().stream().filter(r -> !r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).sorted(Comparator.comparingLong(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList()));

        //注册来源
        conditionVO.setSource(MemberRegisterSourceEnum.toPlatformDropdownList());

        //会员等级
        conditionVO.setMemberLevels(baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId()));

        return conditionVO;
    }

    /**
     * 平台后台 - 会员维护 - 分页模糊查询会员审核信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<PlatformPageQueryMemberResp> pageMembers(HttpHeaders headers, PlatformMemberQueryDataReq queryVO) {
        memberCacheService.needLoginFromManagePlatform(headers);

        //使用Specification构造模糊查询条件并查询
        Specification<MemberRelationDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            //上级为平台
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.PLATFORM.getCode()));

            //不查询由会员创建的，未提交平台审核的数据（此时数据的内部状态为 MemberInnerStatusEnum.REGISTERING.getCode()
            list.add(criteriaBuilder.notEqual(root.get("innerStatus").as(Integer.class), PlatformInnerStatusEnum.REGISTERING.getCode()));

            //会员角色
            if(NumberUtil.notNullOrZero(queryVO.getRoleId())) {
                list.add(criteriaBuilder.equal(root.get("subRoleId").as(Long.class), queryVO.getRoleId()));
            }

            //内部状态
            if(NumberUtil.notNullOrZero(queryVO.getInnerStatus())) {
                list.add(criteriaBuilder.equal(root.get("innerStatus").as(Integer.class), queryVO.getInnerStatus()));
            }

            //外部状态
            if(NumberUtil.notNullOrZero(queryVO.getOuterStatus())) {
                list.add(criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), queryVO.getOuterStatus()));
            }

            //会员状态
            if(NumberUtil.notNullOrZero(queryVO.getStatus())) {
                list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), queryVO.getStatus()));
            }

            //注册起始时间
            if(StringUtils.hasLength(queryVO.getStartDate())) {
                LocalDateTime startDate = LocalDateTime.parse(queryVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), startDate));
            }

            //注册结束时间
            if(StringUtils.hasLength(queryVO.getEndDate())) {
                LocalDateTime endDate = LocalDateTime.parse(queryVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), endDate));
            }

            //会员名称
            Join<Object, Object> subMemberJoin = root.join("subMember", JoinType.LEFT);
            if(StringUtils.hasLength(queryVO.getName())) {
                list.add(criteriaBuilder.like(subMemberJoin.get("name").as(String.class), "%" + queryVO.getName().trim() + "%"));
            }

            //注册来源
            if(NumberUtil.notNullOrZero(queryVO.getSource())) {
                list.add(criteriaBuilder.equal(subMemberJoin.get("source").as(Integer.class), queryVO.getSource()));
            }

            //会员等级
            if(NumberUtil.notNullOrZero(queryVO.getLevel())) {
                Join<Object, Object> levelRightJoin = root.join("levelRight", JoinType.LEFT);
                list.add(criteriaBuilder.equal(levelRightJoin.get("level").as(Integer.class), queryVO.getLevel()));
            }

            //会员类型
            if(NumberUtil.notNullOrZero(queryVO.getMemberType())) {
                Join<Object, Object> memberTypeJoin = root.join("subRole", JoinType.LEFT);
                list.add(criteriaBuilder.equal(memberTypeJoin.get("memberType").as(Long.class), queryVO.getMemberType()));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Pageable page = PageRequest.of(queryVO.getCurrent() - 1, queryVO.getPageSize(), Sort.by("id").descending());
        Page<MemberRelationDO> pageList = relationRepository.findAll(specification, page);

        List<MemberRelationDO> content = pageList.getContent();
        return new PageDataResp<>(pageList.getTotalElements(), content.stream().map(relationDO -> {
            PlatformPageQueryMemberResp memberVO = new PlatformPageQueryMemberResp();
            memberVO.setMemberId(relationDO.getSubMemberId());
            memberVO.setValidateId(relationDO.getId());
            memberVO.setRoleId(relationDO.getSubRoleId());
            memberVO.setName(relationDO.getSubMember().getName());
            memberVO.setMemberType(relationDO.getSubRole().getMemberType());
            memberVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
            memberVO.setRoleName(relationDO.getSubRole().getRoleName());
            memberVO.setSource(relationDO.getSubMember().getSource());
            memberVO.setSourceName(MemberRegisterSourceEnum.getCodeMessage(relationDO.getSubMember().getSource()));
            memberVO.setRegisterTime(relationDO.getCreateTime().format(MemberConstant.DEFAULT_DATETIME_FORMATTER));

            //从关联的角色查找等级
            memberVO.setLevel(relationDO.getLevelRight().getLevel());
            memberVO.setLevelTag(relationDO.getLevelRight().getLevelTag());
            memberVO.setStatus(relationDO.getStatus());
            memberVO.setStatusName(MemberStatusEnum.getCodeMessage(relationDO.getStatus()));
            memberVO.setInnerStatus(relationDO.getInnerStatus());
            memberVO.setInnerStatusName(PlatformInnerStatusEnum.getCodeMsg(relationDO.getInnerStatus()));
            memberVO.setOuterStatus(relationDO.getOuterStatus());
            memberVO.setOuterStatusName(MemberOuterStatusEnum.getCodeMsg(relationDO.getOuterStatus()));
            return memberVO;
        }).collect(Collectors.toList()));
    }


    /**
     * 获取会员导入Excel模板
     * @param request Http请求
     * @param response Http响应（文件流）
     */
    @Override
    public void getMemberImportFile(HttpServletRequest request, HttpServletResponse response) {
        try {
            response.setCharacterEncoding("utf-8");
            response.sendError(404, "此功能尚未完成");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入会员
     * @param headers Http头部信息
     * @param excelFile 上传的文件流
     * @return 操作结果
     */
    @Override
    public void importMembers(HttpHeaders headers, MultipartFile excelFile) {
        WrapperUtil.fail(ResponseCodeEnum.MC_MS_FEATURE_NOT_COMPLETE);
    }

    /**
     * 查询导入批次列表
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<String>> getImportBatchNo(HttpHeaders headers) {
        return WrapperUtil.fail(ResponseCodeEnum.MC_MS_FEATURE_NOT_COMPLETE);
    }

    /**
     * 根据批次号，删除批量导入的会员
     * @param headers HttpHeaders信息
     * @param memberVO 接口参数
     * @return 操作结果
     */
    @Override
    public void deleteMembersByBatchNo(HttpHeaders headers, BatchDeleteMemberReq memberVO) {
         WrapperUtil.fail(ResponseCodeEnum.MC_MS_FEATURE_NOT_COMPLETE);
    }

    /**
     * 获取新增会员页面内容
     * @param headers HttpHeaders信息
     * @return 查询结果
     */
    @Override
    public PlatformAddMemberPageItemsResp getAddMemberPageItems(HttpHeaders headers) {
        memberCacheService.checkUserFromCache(headers);
        PlatformAddMemberPageItemsResp itemsVO = new PlatformAddMemberPageItemsResp();

        //会员类型
        itemsVO.setMemberTypes(baseMemberValidateService.getSubMemberTypeList(Arrays.asList(MemberTypeEnum.MERCHANT.getCode(), MemberTypeEnum.MERCHANT_PERSONAL.getCode())));

        //固定审核流程
        List<WorkFlowStepResp> steps = new ArrayList<>();
        WorkFlowStepResp stepVO = new WorkFlowStepResp();
        stepVO.setStep(1);
        stepVO.setRoleName(MemberStringEnum.BUSINESS_ROLE.getName());
        stepVO.setStepName(MemberStringEnum.APPLY_FOR_REGISTER.getName());
        steps.add(stepVO);

        stepVO = new WorkFlowStepResp();
        stepVO.setStep(2);
        stepVO.setRoleName(MemberStringEnum.PLATFORM_ROLE_NAME.getName());
        stepVO.setStepName(MemberStringEnum.MEMBER_VALIDATE.getName());
        steps.add(stepVO);
        itemsVO.setVerifySteps(steps);

        itemsVO.setCurrentStep(1);

        return itemsVO;
    }

    /**
     * 根据会员类型，查询角色列表
     * @param headers HttpHeader信息
     * @param memberTypeReq 接口参数
     * @return 操作结果
     */
    @Override
    public List<RoleIdAndNameResp> getAddMemberPageRoles(HttpHeaders headers, MemberTypeReq memberTypeReq) {
        List<MemberRoleDO> roleDOList = memberRoleRepository.findByMemberTypeAndStatus(memberTypeReq.getMemberType(), EnableDisableStatusEnum.ENABLE.getCode());

        return roleDOList.stream().filter(memberRoleDO -> !memberRoleDO.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).map(memberRoleDO -> new RoleIdAndNameResp(memberRoleDO.getId(), memberRoleDO.getRoleName())).sorted(Comparator.comparingLong(RoleIdAndNameResp::getRoleId)).collect(Collectors.toList());
    }

    /**
     * 新增会员页面，根据会员类型和角色，查询等级列表
     * @param headers         HttpHeader信息
     * @param typeAndRoleIdVO 接口参数
     * @return 操作结果
     */
    @Override
    public List<LevelAndTagResp> getAddMemberPageLevels(HttpHeaders headers, MemberTypeAndRoleIdReq typeAndRoleIdVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        List<LevelAndTagResp> result = baseMemberLevelConfigService.listSubMemberLevels(loginUser.getMemberId(), loginUser.getMemberRoleId(), typeAndRoleIdVO.getRoleId(), typeAndRoleIdVO.getMemberType());

        if(CollectionUtils.isEmpty(result)) {
            return Stream.of(new LevelAndTagResp(0, "")).collect(Collectors.toList());
        }

        return result;
    }

    /**
     * 新增会员页面，根据选择的角色，返回会员注册资料信息
     *
     * @param headers HttpHeader信息
     * @param idVO    接口参数
     * @return 操作结果
     */
    @Override
    public List<MemberConfigGroupResp> getAddMemberPageMemberConfigItems(HttpHeaders headers, RoleIdReq idVO) {
        memberCacheService.needLoginFromManagePlatform(headers);
        MemberRoleDO memberRoleDO = memberRoleRepository.findById(idVO.getRoleId()).orElse(null);
        if(memberRoleDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        return baseMemberRegisterDetailService.groupMemberConfig(new ArrayList<>(memberRoleDO.getConfigs()));
    }

    /**
     * 新增会员
     * @param headers HttpHeaders信息
     * @param memberVO 接口参数
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMember(HttpHeaders headers, PlatformAddMemberReq memberVO) {
        memberCacheService.needLoginFromManagePlatform(headers);

        //幂等校验
        if(memberCacheService.existRegisterKey(memberVO.getPhone(), false)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }

        if(StringUtils.hasLength(memberVO.getEmail()) && memberCacheService.existRegisterKey(memberVO.getEmail(), false)) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
        }

        memberCacheService.setRegisterKey(memberVO.getPhone(), memberVO.getEmail(), false);

        //查询平台会员和平台角色
        MemberDO platformMemberDO = memberRepository.findPlatformMember();
        if (platformMemberDO == null) {
            memberCacheService.deleteRegisterKey(memberVO.getPhone(), memberVO.getEmail(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRoleDO platformRoleDO = platformMemberDO.getMemberRoles().stream().filter(r -> r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).findFirst().orElse(null);
        if (platformRoleDO == null) {
            memberCacheService.deleteRegisterKey(memberVO.getPhone(), memberVO.getEmail(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(memberVO.getRoleId()).orElse(null);
        if(memberRoleDO == null) {
            memberCacheService.deleteRegisterKey(memberVO.getPhone(), memberVO.getEmail(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        CountryAreaResp countryAreaResp = WrapperUtil.getData(countryAreaFeign.getCountryAreaByTelCode(memberVO.getTelCode()));
        if(Objects.isNull(countryAreaResp)) {
            memberCacheService.deleteRegisterKey(memberVO.getPhone(), memberVO.getEmail(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);
        }

        //检查手机号是否已经存在
        if(userRepository.existsByRelTypeAndPhone(MemberRelationTypeEnum.OTHER.getCode(), memberVO.getPhone())) {
            memberCacheService.deleteRegisterKey(memberVO.getPhone(), memberVO.getEmail(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }

        //会员名称和详细信息
        List<MemberRegisterDetailDO> registerDetails = new ArrayList<>();
        //检查名称是否已经存在
        String memberName = baseMemberRegisterDetailService.checkMemberRegisterDetail(memberVO.getDetail(), new ArrayList<>(memberRoleDO.getConfigs()), registerDetails, memberVO.getPhone());

        if(StringUtils.hasLength(memberName) && memberRepository.existsByName(memberName)) {
            memberCacheService.deleteRegisterKey(memberVO.getPhone(), memberVO.getEmail(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_NAME_REGISTERED);
        }

        //判断邮箱（如果非空）是否存在
        if(StringUtils.hasLength(memberVO.getEmail()) && userRepository.existsByRelTypeAndEmail(MemberRelationTypeEnum.OTHER.getCode(), memberVO.getEmail())) {
            memberCacheService.deleteRegisterKey(memberVO.getPhone(), memberVO.getEmail(), false);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_EMAIL_EXISTS);
        }

        String md5Password;
        try {
            md5Password = PasswordUtil.tryEncrypt(MemberConstant.PLATFORM_ADD_MEMBER_DEFAULT_PASSWORD);
        } catch (Exception e) {
            memberCacheService.deleteRegisterKey(memberVO.getPhone(), memberVO.getEmail(), false);
            throw new BusinessException(ResponseCodeEnum.MD5_ERROR);
        }

        //根据角色查找平台会员审核流程的Key
        ProcessBO processResult = basePlatformProcessService.findRolePlatformProcess(memberRoleDO);

        //新增会员
        AddMemberBO memberBO = new AddMemberBO();
        memberBO.setPlatformProcess(processResult);
        memberBO.setUpperMember(platformMemberDO);
        memberBO.setUpperRole(platformRoleDO);
        memberBO.setMemberRoleDO(memberRoleDO);
        memberBO.setMemberTypeEnum(MemberTypeEnum.toEnum(memberVO.getMemberType()));
        memberBO.setRegisterDetails(registerDetails);
        String memberCode = "B2B" + redisUtils.getSerialNumberByDay(MemberRedisConstant.MEMBER_CODE_PREFIX, 4, RedisConstant.REDIS_USER_INDEX);
        memberBO.setCode(memberCode);
        memberBO.setName(memberName);
        memberBO.setTelCode(countryAreaResp.getTelCode());
        memberBO.setPhone(memberVO.getPhone());
        //通过注册添加的都是商家，手机号即账号
        memberBO.setAccount(memberVO.getPhone());
        memberBO.setPassword(md5Password);
        memberBO.setEmail(StringUtils.hasLength(memberVO.getEmail()) ? memberVO.getEmail() : "");
        //平台创建下级会员，外部审核记录的操作角色为平台角色
        memberBO.setOperatorRoleName(platformRoleDO.getRoleName());

        memberBO.setSource(MemberRegisterSourceEnum.FROM_PLATFORM_IMPORT);
        memberBO.setInnerStatus(PlatformInnerStatusEnum.TO_BE_COMMIT.getCode());
        memberBO.setLevel(memberVO.getLevel());

        memberInnerService.addPlatformMember(memberBO, RoleTagEnum.MEMBER.getCode());

    }

    /**
     * 新增会员页面，查询用户基本信息
     *
     * @param headers    HttpHeaders信息
     * @param validateVO 接口参数
     * @return 会员基本信息
     */
    @Override
    public PlatformMemberMaintenanceMemberDetailResp getMemberDetail(HttpHeaders headers, MemberValidateReq validateVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        PlatformMemberMaintenanceMemberDetailResp detailVO = new PlatformMemberMaintenanceMemberDetailResp();
        List<WorkFlowStepResp> outerValidateSteps = new ArrayList<>();
        WorkFlowStepResp stepVO = new WorkFlowStepResp();
        stepVO.setStep(1);
        stepVO.setStepName("申请会员");
        stepVO.setRoleName(relationDO.getSubRoleName());
        outerValidateSteps.add(stepVO);

        stepVO = new WorkFlowStepResp();
        stepVO.setStep(2);
        stepVO.setStepName("审核会员");
        stepVO.setRoleName(loginUser.getMemberRoleName());
        outerValidateSteps.add(stepVO);
        Integer currentOuterSteps = (relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_NOT_PASSED.getCode()) || relationDO.getOuterStatus().equals(MemberOuterStatusEnum.PLATFORM_VERIFY_PASSED.getCode())) ? 2 : 1;
        detailVO.setOuterVerifySteps(outerValidateSteps);
        detailVO.setCurrentOuterStep(currentOuterSteps);

        detailVO.setMemberId(relationDO.getSubMemberId());
        detailVO.setValidateId(relationDO.getId());
        detailVO.setName(relationDO.getSubMember().getName());
        detailVO.setMemberType(relationDO.getSubRole().getMemberType());
        detailVO.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
        detailVO.setRoleId(relationDO.getSubRoleId());
        detailVO.setRoleName(relationDO.getSubRoleName());
        detailVO.setLevelId(relationDO.getLevelRight() == null ? 0L : (relationDO.getLevelRight().getLevelConfig() == null ? 0L : relationDO.getLevelRight().getLevelConfig().getId()));
        detailVO.setLevel(relationDO.getLevelRight() == null ? 0 : relationDO.getLevelRight().getLevel());
        detailVO.setLevelTag(relationDO.getLevelRight() == null ? "" : relationDO.getLevelRight().getLevelTag());
        detailVO.setTelCode(relationDO.getSubMember().getTelCode());

        CountryAreaResp countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(detailVO.getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);
        detailVO.setTelCode(countryAreaResp.getTelCode());
        detailVO.setPhone(relationDO.getSubMember().getPhone());
        detailVO.setEmail(relationDO.getSubMember().getEmail());

        //detailVO.setGroups(baseMemberRegisterDetailService.groupMemberRegisterDetail(relationDO.getSubMember(), MemberDetailVersionEnum.USING));
        detailVO.setGroups(baseMemberRegisterDetailService.groupMemberRegisterDetail(relationDO.getSubMember()));


        detailVO.setOuterHistory(baseMemberHistoryService.listMemberOuterHistory(relationDO, RoleTagEnum.MEMBER.getCode()));

        return detailVO;
    }

    /**
     * 会员详情 - 会员信息查询
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @Override
    public MemberDetailResp getMemberDetail(HttpHeaders headers, MemberIdAndRoleIdReq memberIdAndRoleIdReq) {
        memberCacheService.needLoginFromManagePlatform(headers);

        MemberRelationDO relationDO = relationRepository.findFirstBySubMemberIdAndSubRoleIdAndRelTypeAndVerified(memberIdAndRoleIdReq.getMemberId(), memberIdAndRoleIdReq.getRoleId(), MemberRelationTypeEnum.PLATFORM.getCode(), MemberValidateStatusEnum.VERIFY_PASSED.getCode());
        BusinessAssertUtil.notNull(relationDO, ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);

        MemberDetailResp memberDetailResp = new MemberDetailResp();
        memberDetailResp.setMemberId(relationDO.getSubMemberId());
        memberDetailResp.setMemberName(relationDO.getSubMember().getName());
        memberDetailResp.setMemberType(relationDO.getSubRole().getMemberType());
        memberDetailResp.setMemberTypeName(MemberTypeEnum.getName(relationDO.getSubRole().getMemberType()));
        memberDetailResp.setRoleId(relationDO.getSubRole().getId());
        memberDetailResp.setRoleName(relationDO.getSubRole().getRoleName());
        memberDetailResp.setLevelId(Optional.ofNullable(relationDO.getLevelRight()).map(MemberLevelRightDO::getId).orElse(0L));
        memberDetailResp.setLevel(Optional.ofNullable(relationDO.getLevelRight()).map(MemberLevelRightDO::getLevel).orElse(0));
        memberDetailResp.setLevelTag(Optional.ofNullable(relationDO.getLevelRight()).map(MemberLevelRightDO::getLevelTag).orElse(""));
        memberDetailResp.setTelCode(relationDO.getSubMember().getTelCode());
        memberDetailResp.setPhone(relationDO.getSubMember().getPhone());
        memberDetailResp.setEmail(relationDO.getSubMember().getEmail());
        return memberDetailResp;
    }

    /**
     * 修改会员
     *
     * @param headers  Http头部信息
     * @param memberVO 接口参数
     * @return 修改结果
     */
    @Override
    public void updateMemberDetail(HttpHeaders headers, PlatformUpdateMemberReq memberVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);
        MemberRelationDO relationDO = relationRepository.findById(memberVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getMemberId().equals(loginUser.getMemberId()) || !relationDO.getSubMemberId().equals(memberVO.getMemberId())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //只有待提交审核、审核不通过才能修改信息
        if(!(relationDO.getInnerStatus().equals(PlatformInnerStatusEnum.VERIFY_NOT_PASSED.getCode()) || relationDO.getInnerStatus().equals(PlatformInnerStatusEnum.TO_BE_COMMIT.getCode()))) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_CANNOT_UPDATE_OR_DELETE_SUB_MEMBER_HAS_BEEN_COMMIT);
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_UPDATE_OR_DELETE_SUB_MEMBER_HAS_BEEN_COMMIT);
        }

        MemberRoleDO memberRoleDO = memberRoleRepository.findById(memberVO.getRoleId()).orElse(null);
        if(memberRoleDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ROLE_DOES_NOT_EXIST);
        }

        //如果已经有相同角色
        if(!memberRoleDO.getId().equals(relationDO.getSubRoleId()) && relationDO.getSubMember().getMemberRoles().stream().anyMatch(r -> r.getId().equals(memberRoleDO.getId()))) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_ALREADY_HAVE_THE_ROLE);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ALREADY_HAVE_THE_ROLE);
        }

        // 查询手机前缀
        CountryAreaResp countryAreaResp = WrapperUtil.getDataOrThrow(countryAreaFeign.getCountryAreaByTelCode(memberVO.getTelCode()), ResponseCodeEnum.MC_MS_COUNTRY_CODE_DOES_NOT_EXIST);

        //通过level查询等级配置
        if(memberVO.getLevel() != null && !memberVO.getLevel().equals(0)) {
            List<MemberLevelConfigDO> levelConfigDOList = levelConfigRepository.findByLevelTypeAndStatusAndSubRoleId(MemberLevelTypeEnum.PLATFORM.getCode(), EnableDisableStatusEnum.ENABLE.getCode(), memberRoleDO.getId());
            if (!CollectionUtils.isEmpty(levelConfigDOList) && levelConfigDOList.stream().noneMatch(baseLevelConfigDO -> baseLevelConfigDO.getLevel().equals(memberVO.getLevel()))) {
                //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
                throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_LEVEL_DOES_NOT_EXIST);
            }
        }

        //要修改的会员的管理员账号（用于判断手机号是否已经注册）
        UserDO userDO = relationDO.getSubMember().getUsers().stream().filter(user -> user.getUserType().equals(UserTypeEnum.ADMIN.getCode())).findFirst().orElse(null);
        if(userDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_DOES_NOT_EXIST);
        }

        //检查手机号是否已经存在
        Specification<UserDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.notEqual(root.get("member").as(MemberDO.class), relationDO.getMember()));
            list.add(criteriaBuilder.notEqual(root.get("id").as(Long.class), userDO.getId()));
            list.add(criteriaBuilder.equal(root.get("phone").as(String.class), memberVO.getPhone()));
            list.add(criteriaBuilder.equal(root.get("relType").as(Integer.class), MemberRelationTypeEnum.OTHER.getCode()));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        if(userRepository.count(specification) > 0) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_USER_PHONE_EXISTS);
        }

        //修改注册资料
        baseMemberRegisterDetailService.updatePlatformMemberRegisterDetail(relationDO, memberVO.getEmail(), memberVO.getDetail(), true, false);

        String memberName = relationDO.getSubMember().getName();

        //修改完之后，变为待提交状态
        if(relationDO.getInnerStatus().equals(PlatformInnerStatusEnum.VERIFY_NOT_PASSED.getCode())) {
            WorkflowTaskResultBO taskResult = baseWorkflowService.startMemberProcess(relationDO.getValidateTask().getProcessKey(), relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getId());

            relationDO.setInnerStatus(taskResult.getInnerStatus());
            relationDO.setOuterStatus(MemberOuterStatusEnum.TO_PLATFORM_VERIFY.getCode());
            relationDO.getValidateTask().setTaskId(taskResult.getTaskId());
            relationRepository.saveAndFlush(relationDO);
        }

        //修改会员
        UpdatePlatformMemberBO updatePlatformMemberBO = new UpdatePlatformMemberBO();
        updatePlatformMemberBO.setRelationDO(relationDO);
        updatePlatformMemberBO.setMemberRoleDO(memberRoleDO);
        updatePlatformMemberBO.setMemberType(memberVO.getMemberType());
        updatePlatformMemberBO.setLevel(memberVO.getLevel());
        updatePlatformMemberBO.setRegisterDetails(new ArrayList<>());
        updatePlatformMemberBO.setName(memberName);
        updatePlatformMemberBO.setTelCode(countryAreaResp.getTelCode());
        updatePlatformMemberBO.setPhone(memberVO.getPhone());
        updatePlatformMemberBO.setEmail(memberVO.getEmail());

        memberInnerService.updatePlatformMember(updatePlatformMemberBO);
    }


    /**
     * 冻结/解冻会员
     *
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void changeMemberStatus(HttpHeaders headers, ChangeMemberStatusReq statusVO) {
        UserLoginCacheDTO loginUser = memberCacheService.needLoginFromManagePlatform(headers);

        //查询平台会员和平台角色
        MemberDO platformMemberDO = memberRepository.findPlatformMember();
        if (platformMemberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRoleDO platformRoleDO = platformMemberDO.getMemberRoles().stream().filter(r -> r.getRelType().equals(MemberRelationTypeEnum.PLATFORM.getCode())).findFirst().orElse(null);
        if (platformRoleDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_REGISTER_BEFORE_PLATFORM_MEMBER_CREATE);
        }

        MemberRelationDO relationDO = relationRepository.findById(statusVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getSubMemberId().equals(statusVO.getMemberId())) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        relationDO.setStatus(statusVO.getStatus());
        relationRepository.saveAndFlush(relationDO);

        //记录外部操作记录
        baseMemberHistoryService.saveMemberOuterHistory(relationDO, relationDO.getRole().getRoleName(), statusVO.getStatus().equals(MemberStatusEnum.FROZEN.getCode()) ? MemberValidateHistoryOperationEnum.FREEZE_MEMBER : MemberValidateHistoryOperationEnum.UNFREEZE_MEMBER, MemberStatusEnum.getCodeMessage(relationDO.getStatus()), statusVO.getRemark());

        //通知支付服务，冻结-解冻资金账户
        payFeignService.notifyUpdateMemberAssetAccount(relationDO.getMemberId(), relationDO.getRoleId(), relationDO.getSubMemberId(), relationDO.getSubRoleId(), relationDO.getRelType(), relationDO.getStatus());

        //如果是冻结会员，通知商品服务，下架所有商品
        if(relationDO.getStatus().equals(MemberStatusEnum.FROZEN.getCode())) {
            productFeignService.asyncNotifyToOffProducts(relationDO.getSubMemberId(), relationDO.getSubRoleId(), relationDO.getSubRole().getMemberType());

            //删除指定会员的所有token
            CompletableFuture.runAsync(() -> tokenManageService.memberOffline(Collections.singletonList(relationDO.getSubMemberId())), ThreadPoolConfig.asyncDefaultExecutor);
        }

        //通知店铺模板服务
        int templateServiceStatus;
        if(relationDO.getStatus().equals(MemberStatusEnum.NORMAL.getCode())) {
            templateServiceStatus = 1;
        } else {
            templateServiceStatus = 0;
        }
        platformTemplateFeignService.notifyMemberStatusChangedAsync(relationDO.getSubMemberId(), relationDO.getSubRoleId(), templateServiceStatus);
    }

    /**
     * 删除会员
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 删除结果
     */
    @Override
    public void deleteMember(HttpHeaders headers, MemberValidateReq validateVO) {
        memberCacheService.needLoginFromManagePlatform(headers);

        MemberRelationDO relationDO = relationRepository.findById(validateVO.getValidateId()).orElse(null);
        if(relationDO == null || !relationDO.getSubMemberId().equals(validateVO.getMemberId())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RELATION_DOES_NOT_EXIST);
        }

        //todo 确认删除条件
        if(relationRepository.existsBySubMemberIdAndSubRoleIdAndRelType(relationDO.getSubMemberId(), relationDO.getSubRoleId(), MemberRelationTypeEnum.OTHER.getCode())) {

            throw new BusinessException(ResponseCodeEnum.MC_MS_CANNOT_DELETE_OTHER_SUB_MEMBER);
        }

        memberInnerService.deletePlatformMemberAndRole(relationDO);
    }
}
