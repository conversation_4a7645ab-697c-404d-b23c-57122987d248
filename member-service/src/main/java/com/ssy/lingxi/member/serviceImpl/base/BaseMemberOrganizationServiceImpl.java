package com.ssy.lingxi.member.serviceImpl.base;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberOrganizationDO;
import com.ssy.lingxi.member.entity.do_.basic.QMemberOrganizationDO;
import com.ssy.lingxi.member.entity.do_.basic.QUserDO;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrgTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberOrganizationQueryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberSelectOrgQueryResp;
import com.ssy.lingxi.member.repository.MemberOrganizationRepository;
import com.ssy.lingxi.member.repository.MemberRepository;
import com.ssy.lingxi.member.service.base.IBaseMemberOrganizationService;
import com.ssy.lingxi.member.util.MemberOrganizationUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员组织机构服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-28
 */
@Service
public class BaseMemberOrganizationServiceImpl implements IBaseMemberOrganizationService {

    @Resource
    private MemberOrganizationRepository memberOrganizationRepository;

    @Resource
    private MemberRepository memberRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;


    @Override
    public void addMemberOrg(Long memberId, MemberOrganizationAddReq addVO) {
        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberOrganizationDO upperOrgDO = null;
        if (!addVO.getParentId().equals(0L)) {
            upperOrgDO = memberOrganizationRepository.findById(addVO.getParentId()).orElse(null);
            if (upperOrgDO == null) {
                //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
                throw new BusinessException(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
            }
        }

        if (memberOrganizationRepository.existsByMemberAndTitle(memberDO, addVO.getTitle())) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_ORGANIZATION_NAME_EXISTS);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ORGANIZATION_NAME_EXISTS);
        }


        MemberOrganizationDO organizationDO = new MemberOrganizationDO();
        organizationDO.setMember(memberDO);
        organizationDO.setTitle(addVO.getTitle());
        organizationDO.setRemark(StringUtils.hasLength(addVO.getRemark()) ? addVO.getRemark().trim() : "");
        organizationDO.setCode(StringUtils.hasLength(addVO.getCode()) ? addVO.getCode().trim() : "");
        organizationDO.setParentId(addVO.getParentId());

        //层级
        Integer level = upperOrgDO == null ? 1 : upperOrgDO.getLevel() + 1;
        organizationDO.setLevel(level);

        //在同父节点中的顺序
        int maxOrder = 0;
        if (!CollectionUtils.isEmpty(memberDO.getOrgs())) {
            if (upperOrgDO == null) {
                maxOrder = memberDO.getOrgs().stream().filter(org -> org.getLevel().equals(level)).mapToInt(MemberOrganizationDO::getOrder).max().orElse(0);
            } else {
                MemberOrganizationDO finalUpperOrgDO = upperOrgDO;
                maxOrder = memberDO.getOrgs().stream().filter(org -> org.getParentId().equals(finalUpperOrgDO.getId())).mapToInt(MemberOrganizationDO::getOrder).max().orElse(0);
            }
        }
        organizationDO.setOrder(maxOrder + 1);

        //生成Key
        String key = upperOrgDO == null ? String.valueOf(maxOrder + 1) : upperOrgDO.getKey().concat("-").concat(String.valueOf(maxOrder + 1));
        organizationDO.setKey(key);

        memberOrganizationRepository.save(organizationDO);

        //return WrapperUtil.success();
    }

    /**
     * 根据菜单Id，更新组织机构信息
     * @param memberId 会员Id
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @Override
    public void updateMemberOrg(Long memberId, MemberOrganizationUpdateReq updateVO) {
        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberOrganizationDO organizationDO = memberDO.getOrgs().stream().filter(org -> org.getId().equals(updateVO.getId())).findFirst().orElse(null);
        if (organizationDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
        }

        organizationDO.setTitle(StringUtils.hasLength(updateVO.getTitle()) ? updateVO.getTitle() : "");
        organizationDO.setCode(StringUtils.hasLength(updateVO.getCode()) ? updateVO.getCode().trim() : "");
        organizationDO.setRemark(StringUtils.hasLength(updateVO.getRemark()) ? updateVO.getRemark().trim() : "");

        memberOrganizationRepository.saveAndFlush(organizationDO);


    }

    /**
     * 删除一个会员组织架构
     * @param memberId 会员Id
     * @param deleteVO 接口参数
     * @return 操作结果
     */
    @Override
    public void deleteMemberOrg(Long memberId, MemberOrganizationDeleteReq deleteVO) {

        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberOrganizationDO organizationDO = memberDO.getOrgs().stream().filter(org -> org.getId().equals(deleteVO.getId())).findFirst().orElse(null);
        if (organizationDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
        }

        List<MemberOrganizationDO> subOrgList = memberOrganizationRepository.findByMemberAndKeyStartsWith(memberDO, organizationDO.getKey() + "-");
        List<Long> subOrgIdList = subOrgList.stream().map(MemberOrganizationDO::getId).collect(Collectors.toList());
        subOrgIdList.add(organizationDO.getId());
        subOrgList.add(organizationDO);

        //如果有用户已经关联了组织机构，不能删除
        if (memberDO.getUsers().stream().anyMatch(userDO -> userDO.getOrg() != null && subOrgIdList.contains(userDO.getOrg().getId()))) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_ORG_CANNOT_DELETE_WHEN_USER_EXISTS);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_ORG_CANNOT_DELETE_WHEN_USER_EXISTS);
        }

        memberOrganizationRepository.deleteInBatch(subOrgList);

        //return WrapperUtil.success();
    }

    /**
     * 查询一个会员组织架构
     * @param memberId 会员Id
     * @param getVO    接口参数
     * @return 操作结果
     */
    @Override
    public MemberOrganizationQueryResp getMemberOrg(Long memberId, MemberOrganizationGetReq getVO) {
        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        MemberOrganizationDO organizationDO = memberDO.getOrgs().stream().filter(org -> org.getId().equals(getVO.getId())).findFirst().orElse(null);
        if (organizationDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_ORGANIZATION_DOES_NOT_EXIST);
        }

        MemberOrganizationQueryResp queryVO = new MemberOrganizationQueryResp();
        queryVO.setId(organizationDO.getId());
        queryVO.setTitle(organizationDO.getTitle());
        queryVO.setCode(organizationDO.getCode());
        queryVO.setRemark(organizationDO.getRemark());

        return queryVO;
    }

    /**
     * 查询所有组织机构信息，以非树形菜单的形式返回
     * 返回组织机构和该组织机构的上一级组织机构信息
     * @param memberId 会员Id
     * @param getVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberSelectOrgQueryResp> selectOrg(Long memberId, MemberSelectOrgGetDataReq getVO) {
        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        QMemberOrganizationDO qMemberOrganizationDO = QMemberOrganizationDO.memberOrganizationDO;
        QUserDO qUserDO= QUserDO.userDO;
        JPAQuery<MemberOrganizationDO> query = jpaQueryFactory.select(qMemberOrganizationDO)
                .from(qMemberOrganizationDO)
                .leftJoin(qUserDO)
                .on(qMemberOrganizationDO.id.eq(qUserDO.org.id));
        //拼接查询条件
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qMemberOrganizationDO.member.id.eq(memberDO.getId()));
        if (StringUtils.hasLength(getVO.getTitle())) {
            String title = "%" + getVO.getTitle().trim() + "%";
            builder.and(
                    qMemberOrganizationDO.title.like(title)
                            .or(qUserDO.name.like(title))
                            .or(qUserDO.jobTitle.like(title))
            );
        }
        //分页排序
        query.where(builder).offset(getVO.getCurrentOffset()).limit(getVO.getPageSize());
        //获取总数
        long count = query.distinct().fetchCount();
        //获取查询结果
        List<MemberOrganizationDO> result = query.distinct().fetch();

        Set<MemberOrganizationDO> orgSet = memberDO.getOrgs();
        if (CollectionUtils.isEmpty(orgSet)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }
        Map<Long, List<MemberOrganizationDO>> orgMap = orgSet.stream().collect(Collectors.groupingBy(MemberOrganizationDO::getId));

        return new PageDataResp<>(count, result.stream().map(org -> {
            MemberOrganizationDO orgDO = Optional.ofNullable(orgMap.get(org.getParentId())).map(orgList -> orgList.get(0)).orElse(null);
            if (orgDO == null) {
                return new MemberSelectOrgQueryResp(org.getId(), org.getTitle(), org.getCode(), org.getRemark(), 0L, "", "", "");
            }
            return new MemberSelectOrgQueryResp(org.getId(), org.getTitle(), org.getCode(), org.getRemark(), orgDO.getId(), orgDO.getTitle(), orgDO.getCode(), orgDO.getRemark());
        }).sorted(Comparator.comparing(MemberSelectOrgQueryResp::getId)).collect(Collectors.toList()));
    }

    /**
     * 查询会员的所有组织架构，以树形菜单的形式返回
     * @param memberId 会员Id
     * @return 操作结果
     */
    @Override
    public List<MemberOrgTreeResp> treeMemberOrg(Long memberId) {
        MemberDO memberDO = memberRepository.findById(memberId).orElse(null);
        if (memberDO == null) {
            //return WrapperUtil.fail(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_DOES_NOT_EXIST);
        }

        return MemberOrganizationUtil.transferToTree(new ArrayList<>(memberDO.getOrgs()));
    }

    /**
     * 查询会员的（非门店）所有组织架构，以树形菜单的形式返回
     * @param memberId 会员Id
     * @return 查询结果
     */
    @Override
    public List<MemberOrgTreeResp> nonStoreTreeMemberOrg(Long memberId) {
        QMemberOrganizationDO organizationDO = QMemberOrganizationDO.memberOrganizationDO;
        List<MemberOrganizationDO> orgList = jpaQueryFactory.select(organizationDO).from(organizationDO).where(organizationDO.member.id.eq(memberId)).fetch();

        return MemberOrganizationUtil.transferToTree(orgList);
    }
}
