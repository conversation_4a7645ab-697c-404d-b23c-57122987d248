package com.ssy.lingxi.member.controller.web.comment;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.comment.*;
import com.ssy.lingxi.member.model.resp.comment.MemberOrderTradeCommentPageResp;
import com.ssy.lingxi.member.model.resp.comment.MemberTradeCommentDetailResp;
import com.ssy.lingxi.member.model.resp.comment.PlatformMemberTradeCommentPageResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditCommentSummaryResp;
import com.ssy.lingxi.member.model.resp.maintenance.MemberDetailCreditTradeHistoryResp;
import com.ssy.lingxi.member.service.web.comment.IPlatformMemberCommentService;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 平台后台-评价管理接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/platform/comment")
public class PlatformMemberCommentController {
    @Resource
    private IPlatformMemberCommentService platformMemberCommentService;

    /**
     * 平台后台-评价管理-会员评价查询分页列表
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/trade/page")
    public WrapperResp<PageDataResp<PlatformMemberTradeCommentPageResp>> pagePlatformMemberTradeComment(@RequestHeader HttpHeaders headers, @Valid PlatformMemberTradeCommentDataReq pageVO) {
        return WrapperUtil.success(platformMemberCommentService.pagePlatformMemberTradeComment(headers, pageVO));
    }

    /**
     * 平台后台-评价管理-交易评价汇总
     * @param headers Http头部信息
     * @return 查询结果
     */
    @GetMapping("/trade/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> pagePlatformMemberTradeCommentSummary(@RequestHeader HttpHeaders headers, @Valid PlatformMemberTradeCommentSummaryReq pageVO) {
        return WrapperUtil.success(platformMemberCommentService.getAllMemberDetailCreditTradeCommentSummary(headers, pageVO));
    }

    /**
     * 平台后台-评价管理-分页查询交易评论历史记录
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/trade/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditTradeHistoryResp>> pagePlatformMemberTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid PlatformMemberTradeCommentHistoryDataReq pageVO) {
        return WrapperUtil.success(platformMemberCommentService.pagePlatformMemberTradeCommentHistory(headers, pageVO));
    }

    /**
     * 平台后台-评价管理-评价分页列表
     * @param headers Http头部信息
     * @param orderTradeCommentQueryVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/order/trade/history/page")
    public WrapperResp<PageDataResp<MemberOrderTradeCommentPageResp>> pageMemberOrderTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberOrderTradeCommentDataReq orderTradeCommentQueryVO) {
        return WrapperUtil.success(platformMemberCommentService.pageMemberOrderTradeCommentHistory(headers, orderTradeCommentQueryVO));
    }

    /**
     * 平台后台-评价管理-评价详情
     * @param headers Http头部信息
     * @param tradeCommentIdVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/order/trade/history/get")
    public WrapperResp<MemberTradeCommentDetailResp> getMemberTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberTradeCommentIdReq tradeCommentIdVO) {
        return WrapperUtil.success(platformMemberCommentService.getMemberTradeCommentHistory(headers, tradeCommentIdVO));
    }

    /**
     * 平台后台-评价管理-批量删除评价
     * @param headers Http头部信息
     * @param tradeCommentIdsVO 接口参数
     */
    @PostMapping("/order/trade/history/delete")
    public WrapperResp<Void> getMemberTradeCommentHistory(@RequestHeader HttpHeaders headers, @RequestBody @Validated MemberTradeCommentIdsReq tradeCommentIdsVO) {
         platformMemberCommentService.deletePlatformMemberTradeComment(headers, tradeCommentIdsVO);
        return WrapperUtil.success();
    }

    /**
     * 平台后台-评价管理-屏蔽/显示评价
     * @param headers Http头部信息
     * @param updateStatusVO 接口参数
     */
    @PostMapping("/order/trade/history/update/status")
    public WrapperResp<Void> updateStatusPlatformMemberTradeComment(@RequestHeader HttpHeaders headers, @RequestBody @Validated MemberTradeCommentUpdateStatusReq updateStatusVO) {
         platformMemberCommentService.updateStatusPlatformMemberTradeComment(headers, updateStatusVO);
        return WrapperUtil.success();
    }
}
