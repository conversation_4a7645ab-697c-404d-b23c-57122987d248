package com.ssy.lingxi.member.serviceImpl.web.comment;

import cn.hutool.core.convert.Convert;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.enums.manage.PlatformParameterEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStringEnum;
import com.ssy.lingxi.manage.api.model.resp.parameter.PlatformParameterManageResp;
import com.ssy.lingxi.member.api.model.req.MemberTradeCommentSubmitDetailReq;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderCommentDO;
import com.ssy.lingxi.member.entity.do_.comment.MemberOrderProductCommentDO;
import com.ssy.lingxi.member.enums.MemberTradeTypeEnum;
import com.ssy.lingxi.member.model.req.comment.MemberTradeCommentSubmitReq;
import com.ssy.lingxi.member.repository.comment.MemberOrderCommentRepository;
import com.ssy.lingxi.member.repository.comment.MemberOrderProductCommentRepository;
import com.ssy.lingxi.member.service.feign.IManageFeignService;
import com.ssy.lingxi.member.service.web.comment.IMemberCommentScheduleService;
import com.ssy.lingxi.member.service.web.comment.IMemberCommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 会员评论服务接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-23
 */
@Slf4j
@Service
public class MemberCommentScheduleServiceImpl implements IMemberCommentScheduleService {
    @Resource
    private IManageFeignService platformManageFeignService;

    @Resource
    private MemberOrderCommentRepository memberOrderCommentRepository;

    @Resource
    private MemberOrderProductCommentRepository memberOrderProductCommentRepository;

    @Resource
    private IMemberCommentService memberCommentService;

//    @Async
//    public void asyncOrderAutoComment(MemberOrderCommentReq memberOrderCommentReq) {
//        Wrapper<PlatformParameterManageResp> parameterManageWrapper = platformManageFeignService.parameterManageDetails(ServiceConfig.ORDER_AUTO_COMMENT_PARAMETER_CODE);
//        if (ResponseCode.SUCCESS.getCode() != parameterManageWrapper.getCode()) {
//            log.error("设置定时评价, 查询自动评价时间失败: {}", parameterManageWrapper.getMessage());
//            return;
//        }
//
//        PlatformParameterManageResp parameter = parameterManageWrapper.getData();
//        if (Objects.isNull(parameter)
//                || EnableDisableStatus.DISABLE.getCode().equals(parameter.getState())
//                || parameter.getParameterValue().compareTo(BigDecimal.ZERO) < 0
//        ) {
//            log.error("自动评价订单, 查询自动评价时间有误");
//            return;
//        }
//
//        // 需要执行的数据
//        List<Long> dataIds = Collections.singletonList(memberOrderCommentReq.getId());
//        // 保留两位精度, 最小值24(小时) * 0.01 = 0.24(小时), 即14.4(分钟)
//        BigDecimal day = parameter.getParameterValue().setScale(2, RoundingMode.HALF_UP);
//        if (BigDecimal.ZERO.compareTo(day) >= 0) {
//            // 如果设置精度, 导致值太小或为0,则取最小时间
//            day = BigDecimal.valueOf(0.01);
//        }
//        long commentTimeMillis = day.multiply(BigDecimal.valueOf(Constants.DAY_TO_MILLISECONDS)).longValue();
//        long executeTime = System.currentTimeMillis() + commentTimeMillis;
//
//        // 采购评价
//        HashMap<String, Object> buyerTaskAttributes = new HashMap<>();
//        buyerTaskAttributes.put(ServiceConfig.ORDER_COMMENT_ATTR, MemberTradeTypeEnum.BUYER.getTypeEnum());
//        Wrapper<Long> buyerWrapper = scheduleFeignService.createScheduleTask(dataIds, executeTime, ServiceConfig.ORDER_COMMENT_CALLBACK_URL,
//                buyerTaskAttributes, memberOrderCommentReq.getBuyerMemberId(), memberOrderCommentReq.getBuyerRoleId());
//        if (buyerWrapper.getCode() != ResponseCode.SUCCESS.getCode()) {
//            log.error("采购自动评价调用定时服务失败:{}", buyerWrapper.getMessage());
//        }
//
//        // 供应商评价(延迟1秒执行)
//        HashMap<String, Object> vendorTaskAttributes = new HashMap<>();
//        vendorTaskAttributes.put(ServiceConfig.ORDER_COMMENT_ATTR, MemberTradeTypeEnum.SELLER.getTypeEnum());
//        Wrapper<Long> vendorWrapper = scheduleFeignService.createScheduleTask(dataIds, executeTime + 1000, ServiceConfig.ORDER_COMMENT_CALLBACK_URL,
//                vendorTaskAttributes, memberOrderCommentReq.getVendorMemberId(), memberOrderCommentReq.getVendorRoleId());
//        if (buyerWrapper.getCode() != ResponseCode.SUCCESS.getCode()) {
//            log.error("供应自动评价调用定时服务失败:{}", vendorWrapper.getMessage());
//        }
//    }

    /**
     * 自动评价订单
     */
    @Override
    public void autoComment() {
        PlatformParameterManageResp parameter = platformManageFeignService.parameterManageDetails(PlatformParameterEnum.TRADE_EVALUATE_TIME.getCode());
        BigDecimal parameterValue = new BigDecimal(parameter.getParameterValue());
        if (Objects.isNull(parameter)
                || EnableDisableStatusEnum.DISABLE.getCode().equals(parameter.getState())
                || parameterValue.compareTo(BigDecimal.ZERO) < 0
        ) {
            log.error("自动评价订单, 查询自动评价时间有误");
            return;
        }

        // 保留两位精度, 最小值24(小时) * 0.01 = 0.24(小时), 即14.4(分钟)
        BigDecimal day = parameterValue.setScale(2, RoundingMode.HALF_UP);
        if (BigDecimal.ZERO.compareTo(day) >= 0) {
            // 如果设置精度, 导致值太小或为0,则取最小时间
            day = BigDecimal.valueOf(0.01);
        }
        long commentTimeMillis = day.multiply(BigDecimal.valueOf(Constant.DAY_TO_MILLISECONDS)).longValue();
        long executeTime = System.currentTimeMillis() - commentTimeMillis;

        //查询订单信息
        List<MemberOrderCommentDO> memberOrderCommentDOList = memberOrderCommentRepository.findByCompleteTimeLessThanAndBuyerCompleteCommentStatusNotAndVendorCompleteCommentStatusNot(Convert.toLocalDateTime(executeTime), EnableDisableStatusEnum.ENABLE.getCode(), EnableDisableStatusEnum.ENABLE.getCode());
        if (CollectionUtils.isEmpty(memberOrderCommentDOList)) {
            return;
        }
        //遍历
        memberOrderCommentDOList.forEach(memberOrderCommentDO -> {
            Long orderId = memberOrderCommentDO.getId();
            Integer buyerCompleteCommentStatus = memberOrderCommentDO.getBuyerCompleteCommentStatus();
            Integer vendorCompleteCommentStatus = memberOrderCommentDO.getVendorCompleteCommentStatus();

            // 查询订单商品
            List<MemberOrderProductCommentDO> orderProductCommentDOList = memberOrderProductCommentRepository.findAllByOrderId(orderId);

            //采购商未评价
            if(EnableDisableStatusEnum.DISABLE.getCode().equals(buyerCompleteCommentStatus)){
                //买方（采购会员）评价
                List<MemberTradeCommentSubmitDetailReq> commentSubmitDetailVOList = orderProductCommentDOList.stream().
                        filter(e -> EnableDisableStatusEnum.DISABLE.getCode().equals(e.getBuyerCommentStatus()))
                        .map(e -> {
                            MemberTradeCommentSubmitDetailReq commentSubmitDetailVO = new MemberTradeCommentSubmitDetailReq();
                            commentSubmitDetailVO.setOrderProductId(e.getId());
                            commentSubmitDetailVO.setStar(5);
                            commentSubmitDetailVO.setComment(MemberStringEnum.AUTO_COMMENT.getName());
                            commentSubmitDetailVO.setCommentCode(MemberStringEnum.AUTO_COMMENT.getCode());
                            return commentSubmitDetailVO;
                        }).collect(Collectors.toList());

                // 提价评价
                MemberTradeCommentSubmitReq commentSubmitVO = new MemberTradeCommentSubmitReq();
                commentSubmitVO.setOrderId(orderId);
                commentSubmitVO.setCommentSubmitDetailList(commentSubmitDetailVOList);
                memberCommentService.submitAutoMemberTradeComment(MemberTradeTypeEnum.BUYER, memberOrderCommentDO, commentSubmitVO);
            }

            //供应商未评价
            if(EnableDisableStatusEnum.DISABLE.getCode().equals(vendorCompleteCommentStatus)) {
                //卖方（供应会员）评价
                List<MemberTradeCommentSubmitDetailReq> commentSubmitDetailVOList = orderProductCommentDOList.stream().
                        filter(e -> EnableDisableStatusEnum.DISABLE.getCode().equals(e.getVendorCommentStatus()))
                        .map(e -> {
                            MemberTradeCommentSubmitDetailReq commentSubmitDetailVO = new MemberTradeCommentSubmitDetailReq();
                            commentSubmitDetailVO.setOrderProductId(e.getId());
                            commentSubmitDetailVO.setStar(5);
                            commentSubmitDetailVO.setComment(MemberStringEnum.AUTO_COMMENT.getName());
                            commentSubmitDetailVO.setCommentCode(MemberStringEnum.AUTO_COMMENT.getCode());
                            return commentSubmitDetailVO;
                        }).collect(Collectors.toList());

                // 提价评价
                MemberTradeCommentSubmitReq commentSubmitVO = new MemberTradeCommentSubmitReq();
                commentSubmitVO.setOrderId(orderId);
                commentSubmitVO.setCommentSubmitDetailList(commentSubmitDetailVOList);
                memberCommentService.submitAutoMemberTradeComment(MemberTradeTypeEnum.SELLER, memberOrderCommentDO, commentSubmitVO);
            }
        });

    }
}
