package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.basic.MemberIdAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeAndRoleIdReq;
import com.ssy.lingxi.member.model.req.basic.MemberTypeReq;
import com.ssy.lingxi.member.model.req.basic.RoleIdReq;
import com.ssy.lingxi.member.model.req.maintenance.*;
import com.ssy.lingxi.member.model.req.validate.MemberCancellationValidateReq;
import com.ssy.lingxi.member.model.req.validate.MemberValidateReq;
import com.ssy.lingxi.member.model.req.validate.ValidateIdPageDataReq;
import com.ssy.lingxi.member.model.resp.basic.LevelAndTagResp;
import com.ssy.lingxi.member.model.resp.basic.MemberConfigGroupResp;
import com.ssy.lingxi.member.model.resp.basic.RoleIdAndNameResp;
import com.ssy.lingxi.member.model.resp.configManage.AuthTreeResp;
import com.ssy.lingxi.member.model.resp.maintenance.*;
import com.ssy.lingxi.member.model.resp.validate.MemberValidateDetailLevelResp;
import com.ssy.lingxi.member.service.web.*;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 平台会员管理 - 会员维护
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-13
 */
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/maintenance")
public class PlatformMemberMaintenanceController {

    @Resource
    private IPlatformMemberMaintenanceService memberMaintenanceService;

    @Resource
    private IPlatformMemberDetailBasicService memberDetailBasicService;

    @Resource
    private IPlatformMemberDetailLevelService memberDetailLevelService;

    @Resource
    private IPlatformMemberDetailAuthService memberDetailAuthService;

    @Resource
    private IPlatformMemberDetailRightService memberDetailRightService;

    @Resource
    private IPlatformMemberDetailCreditService memberDetailCreditService;

    /**
     * 获取模糊分页查询会员页面的查询下拉框内容
     * @return 操作结果
     */
    @GetMapping("/pageitems")
    public WrapperResp<PlatformMemberQuerySearchConditionResp> getPageCondition(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberMaintenanceService.getPageCondition(headers));
    }

    /**
     * 分页、模糊查询会员信息列表
     * @param headers HttpHeaders信息
     * @param queryVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<PlatformPageQueryMemberResp>> pageMembers(@RequestHeader HttpHeaders headers, @Valid PlatformMemberQueryDataReq queryVO) {
        return WrapperUtil.success(memberMaintenanceService.pageMembers(headers, queryVO));
    }

    /**
     * 获取新增会员页面的内容
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/addpageitems")
    public WrapperResp<PlatformAddMemberPageItemsResp> getAddMemberPageItems(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberMaintenanceService.getAddMemberPageItems(headers));
    }

    /**
     * 新增会员页面，根据会员类型，查询角色列表
     * @param headers HttpHeader信息
     * @param memberTypeReq 接口参数
     * @return 操作结果
     */
    @GetMapping("/addpageitems/role")
    public WrapperResp<List<RoleIdAndNameResp>> getAddMemberPageRoles(@RequestHeader HttpHeaders headers, @Valid MemberTypeReq memberTypeReq) {
        return WrapperUtil.success(memberMaintenanceService.getAddMemberPageRoles(headers, memberTypeReq));
    }

    /**
     * 新增会员页面，根据会员类型和角色，查询等级列表
     * @param headers HttpHeader信息
     * @param typeAndRoleIdVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/addpageitems/level")
    public WrapperResp<List<LevelAndTagResp>> getAddMemberPageLevels(@RequestHeader HttpHeaders headers, @Valid MemberTypeAndRoleIdReq typeAndRoleIdVO) {
        return WrapperUtil.success(memberMaintenanceService.getAddMemberPageLevels(headers, typeAndRoleIdVO));
    }

    /**
     * 新增会员页面，根据选择的角色，返回会员注册资料信息
     * @param headers HttpHeaders信息
     * @param idVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/addpageitems/detail")
    public WrapperResp<List<MemberConfigGroupResp>> getAddMemberPageMemberConfigItems(@RequestHeader HttpHeaders headers, @Valid RoleIdReq idVO) {
        return WrapperUtil.success(memberMaintenanceService.getAddMemberPageMemberConfigItems(headers, idVO));
    }

    /**
     * 新增会员
     * @param headers HttpHeaders信息
     * @param memberVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/addmember")
    public WrapperResp<Void> addMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformAddMemberReq memberVO) {
         memberMaintenanceService.addMember(headers, memberVO);
        return WrapperUtil.success();
    }

    /**
     * 新增会员页面，查询用户基本信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 会员基本信息
     */
    @GetMapping("/getmember")
    public WrapperResp<PlatformMemberMaintenanceMemberDetailResp> getMemberDetail(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberMaintenanceService.getMemberDetail(headers, validateVO));
    }

    /**
     * 会员详情 - 会员信息查询
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @PostMapping("/getMemberDetail")
    public WrapperResp<MemberDetailResp> getMemberDetail(@RequestHeader HttpHeaders headers, @Valid @RequestBody MemberIdAndRoleIdReq memberIdAndRoleIdReq) {
        return WrapperUtil.success(memberMaintenanceService.getMemberDetail(headers, memberIdAndRoleIdReq));
    }

    /**
     * 修改会员
     * @param headers Http头部信息
     * @param memberVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/updatemember")
    public WrapperResp<Void> updateMemberDetail(@RequestHeader HttpHeaders headers, @RequestBody @Valid PlatformUpdateMemberReq memberVO) {
         memberMaintenanceService.updateMemberDetail(headers, memberVO);
        return WrapperUtil.success();
    }

    /**
     * 更改会员状态（冻结、解冻会员）
     * @param headers HttpHeaders信息
     * @param statusVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/status")
    public WrapperResp<Void> changeMemberStatus(@RequestHeader HttpHeaders headers, @RequestBody @Valid ChangeMemberStatusReq statusVO) {
         memberMaintenanceService.changeMemberStatus(headers, statusVO);
        return WrapperUtil.success();
    }

    /**
     * 删除会员
     * @param headers Http头部信息
     * @param validateVO 接口参数
     * @return 删除结果
     */
    @RequestMapping("/delete")
    public WrapperResp<Void> deleteMember(@RequestHeader HttpHeaders headers, @RequestBody @Valid MemberValidateReq validateVO) {
         memberMaintenanceService.deleteMember(headers, validateVO);
        return WrapperUtil.success();
    }

    /**
     * 获取会员批量导入模板下载Url
     * @param request 客户端请求
     * @param response 客户端响应
     */
    @GetMapping("/gettemplate")
    public void getMemberImportTemplateFile(HttpServletRequest request, HttpServletResponse response) {
        memberMaintenanceService.getMemberImportFile(request, response);
    }

    /**
     * 批量导入会员信息
     * @param headers HttpHeaders信息
     * @param excelFile 上传的文件
     * @return 操作结果
     */
    @RequestMapping("/import")
    public WrapperResp<Void> importMembers(@RequestHeader HttpHeaders headers, @RequestParam("memberImport") MultipartFile excelFile) {
         memberMaintenanceService.importMembers(headers, excelFile);
        return WrapperUtil.success();
    }

    /**
     * 查询批量导入的批次号列表
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @GetMapping("/import/getbatch")
    public WrapperResp<List<String>> getImportBatchNo(@RequestHeader HttpHeaders headers) {
        return memberMaintenanceService.getImportBatchNo(headers);
    }

    /**
     * 根据批次号，删除批量导入的会员
     * @param headers HttpHeaders信息
     * @param memberVO 接口参数
     * @return 操作结果
     */
    @RequestMapping("/import/delbatch")
    public WrapperResp<Void> deleteMembersByBatchNo(@RequestHeader HttpHeaders headers, @RequestBody @Valid BatchDeleteMemberReq memberVO) {
         memberMaintenanceService.deleteMembersByBatchNo(headers, memberVO);
        return WrapperUtil.success();
    }

    /**
     * 会员详情 - 基本信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/basic")
    public WrapperResp<PlatformMemberDetailBasicResp> getMemberDetailBasic(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberDetailBasicService.getMemberDetailBasic(headers, validateVO));
    }

    /**
     * 会员详情 - 会员权限信息 - 获取会员权限树
     * @param headers     HttpHeaders信息
     * @param validateReq 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/authTree")
    public WrapperResp<AuthTreeResp> getMemberDetailAuth(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateReq) {
        return WrapperUtil.success(memberDetailAuthService.getMemberDetailAuth(headers, validateReq));
    }

    /**
     * 会员详情 - 会员权限信息 - 更新权限
     * @param headers HttpHeaders信息
     * @param roleUpdateReq 接口参数
     * @return 操作结果
     */
    @RequestMapping("/detail/setRelationAuth")
    public WrapperResp<Void> setRelationAuth(@RequestHeader HttpHeaders headers, @RequestBody @Valid RoleUpdateReq roleUpdateReq) {
         memberDetailAuthService.setRelationAuth(headers, roleUpdateReq);
        return WrapperUtil.success();
    }

    /**
     * 会员详情 - 等级信息 - 会员等级
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/level/basic")
    public WrapperResp<MemberValidateDetailLevelResp> getMemberDetailLevel(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberDetailLevelService.getMemberDetailLevel(headers, validateVO));
    }

    /**
     * 会员详情 - 等级信息 - 分页查询会员等级历史记录（活跃分获取记录）
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/level/history/page")
    public WrapperResp<PageDataResp<MemberDetailLevelHistoryResp>> pageMemberLevelDetailHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberDetailLevelService.pageMemberLevelDetailHistory(headers, pageVO));
    }


    /**
     * 会员详情 - 权益信息 - 权益基本信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/right/basic")
    public WrapperResp<MemberDetailRightResp> getMemberDetailRight(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberDetailRightService.getMemberDetailRight(headers, validateVO));
    }

    /**
     * 会员详情 - 权益信息 - 分页查询会员权益获取记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/right/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightHistoryResp>> pageMemberDetailRightHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberDetailRightService.pageMemberDetailRightHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 权益信息 - 分页查询会员权益使用记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/right/spend/history/page")
    public WrapperResp<PageDataResp<MemberDetailRightSpendHistoryResp>> pageMemberDetailRightSpendHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberDetailRightService.pageMemberDetailRightSpendHistory(headers, pageVO));
    }


    /**
     * 会员详情 - 会员信用信息 - 基本信息
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/basic")
    public WrapperResp<MemberDetailCreditResp> getMemberDetailCredit(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberDetailCreditService.getMemberDetailCredit(headers, validateVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 交易评价汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/trade/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> getMemberDetailCreditTradeCommentSummary(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberDetailCreditService.getMemberDetailCreditTradeCommentSummary(headers, validateVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 分页查询交易评价历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/trade/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditTradeHistoryResp>> pageMemberDetailCreditTradeCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberDetailCreditHistoryPageDataReq pageVO) {
        return WrapperUtil.success(memberDetailCreditService.pageMemberDetailCreditTradeCommentHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 售后评价汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/aftersale/summary")
    public WrapperResp<MemberDetailCreditCommentSummaryResp> getMemberDetailCreditAfterSaleCommentSummary(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberDetailCreditService.getMemberDetailCreditAfterSaleCommentSummary(headers, validateVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 分页查询售后评价历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/aftersale/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditAfterSaleHistoryResp>> pageMemberDetailCreditAfterSaleCommentHistory(@RequestHeader HttpHeaders headers, @Valid MemberDetailCreditHistoryPageDataReq pageVO) {
        return WrapperUtil.success(memberDetailCreditService.pageMemberDetailCreditAfterSaleCommentHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 投诉汇总
     * @param headers HttpHeaders信息
     * @param validateVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/complain/summary")
    public WrapperResp<MemberDetailCreditComplainSummaryResp> getMemberDetailCreditComplainSummary(@RequestHeader HttpHeaders headers, @Valid MemberValidateReq validateVO) {
        return WrapperUtil.success(memberDetailCreditService.getMemberDetailCreditComplainSummary(headers, validateVO));
    }

    /**
     * 会员详情 - 会员信用信息 - 分页查询投诉历史记录
     * @param headers HttpHeaders信息
     * @param pageVO 接口参数
     * @return 操作结果
     */
    @GetMapping("/detail/credit/complain/history/page")
    public WrapperResp<PageDataResp<MemberDetailCreditComplainHistoryResp>> pageMemberDetailCreditComplainHistory(@RequestHeader HttpHeaders headers, @Valid ValidateIdPageDataReq pageVO) {
        return WrapperUtil.success(memberDetailCreditService.pageMemberDetailCreditComplainHistory(headers, pageVO));
    }

    /**
     * 会员详情 - 会员注销审核列表查询下拉框内容
     * @return 操作结果
     */
    @GetMapping("/cancellation/pageItems")
    public WrapperResp<MemberCancellationPageConditionResp> getCancellationPageCondition(@RequestHeader HttpHeaders headers) {
        return WrapperUtil.success(memberDetailBasicService.getCancellationPageCondition(headers));
    }

    /**
     * 会员详情 - 会员注销审核列表查询
     * @param headers HttpHeaders信息
     * @param pageReq 接口参数
     * @return 操作结果
     */
    @GetMapping("/cancellation/page")
    public WrapperResp<PageDataResp<MemberCancellationPageResp>> getCancellationPage(@RequestHeader HttpHeaders headers, @Valid MemberCancellationPageDataReq pageReq) {
        return WrapperUtil.success(memberDetailBasicService.getCancellationPage(headers, pageReq));
    }

    /**
     * 会员详情 - 会员注销审核
     * @param headers HttpHeaders信息
     * @return 操作结果
     */
    @PostMapping("/cancellation/auth")
    public WrapperResp<Void> cancellationAuth(@RequestHeader HttpHeaders headers, @Valid @RequestBody MemberCancellationValidateReq validateReq) {
         memberDetailBasicService.cancellationAuth(headers, validateReq);
        return WrapperUtil.success();
    }
}
