package com.ssy.lingxi.member.serviceImpl.commission;

import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.member.entity.do_.basic.UserDO;
import com.ssy.lingxi.member.entity.do_.commission.*;
import com.ssy.lingxi.member.model.dto.commission.MemberRegistrationSuccessDTO;
import com.ssy.lingxi.member.repository.UserRepository;
import com.ssy.lingxi.member.repository.commission.*;
import com.ssy.lingxi.member.service.commission.IInvitationCommissionService;
import com.ssy.lingxi.member.util.CodeUtil;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 邀请分佣服务实现
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-19
 */
@Slf4j
@Service
public class InvitationCommissionServiceImpl implements IInvitationCommissionService {

    @Resource
    private UserRepository userRepository;

    @Resource
    private InvitationRecordRepository invitationRecordRepository;

    @Resource
    private CommissionAccountRepository commissionAccountRepository;

    @Resource
    private CommissionDetailRepository commissionDetailRepository;

    @Resource
    private CommissionConfigRepository commissionConfigRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processRegistrationCommission(MemberRegistrationSuccessDTO registrationDTO) {
        log.info("开始处理注册成功分佣，用户ID：{}，邀请码：{}", registrationDTO.getUserId(), registrationDTO.getInvitationCode());

        // 如果没有邀请码，直接返回
        if (!StringUtils.hasText(registrationDTO.getInvitationCode())) {
            log.info("用户注册无邀请码，跳过分佣处理，用户ID：{}", registrationDTO.getUserId());
            return;
        }

        // 根据邀请码查找邀请人
        // 注意：UserRepository中没有findByInvitationCode方法，需要使用自定义查询
        List<UserDO> inviterList = userRepository.findAll((root, query, criteriaBuilder) ->
            criteriaBuilder.equal(root.get("invitationCode"), registrationDTO.getInvitationCode()));

        if (inviterList.isEmpty()) {
            log.warn("邀请码无效，邀请码：{}，用户ID：{}", registrationDTO.getInvitationCode(), registrationDTO.getUserId());
            return;
        }

        UserDO inviter = inviterList.get(0);

        // 检查是否已经存在邀请记录
        Optional<InvitationRecordDO> existingRecordOpt = invitationRecordRepository.findByInviteeUserId(registrationDTO.getUserId());
        if (existingRecordOpt.isPresent()) {
            log.warn("邀请记录已存在，跳过处理，邀请人ID：{}，被邀请人ID：{}", inviter.getId(), registrationDTO.getUserId());
            return;
        }

        // 创建邀请记录
        InvitationRecordDO invitationRecord = new InvitationRecordDO();
        invitationRecord.setInviterUserId(inviter.getId());
        invitationRecord.setInviteeUserId(registrationDTO.getUserId());
        invitationRecord.setInviteeUserCode(registrationDTO.getUserCode());
        invitationRecord.setInvitationChannel(1); // 1-直接注册
        invitationRecord.setStatus(1); // 1-已邀请已注册
        invitationRecord.setInvitationTime(System.currentTimeMillis());
        invitationRecord.setRegistrationTime(registrationDTO.getRegistrationTime());
        invitationRecord.setRewardIssued(false);
        invitationRecord.setRemark("注册邀请");
        invitationRecord.setCreateTime(System.currentTimeMillis());
        invitationRecord.setUpdateTime(System.currentTimeMillis());

        invitationRecordRepository.saveAndFlush(invitationRecord);

        // 处理注册奖励分佣
        processRegistrationReward(inviter.getId(), registrationDTO.getUserId(), invitationRecord.getId());

        log.info("注册成功分佣处理完成，邀请人ID：{}，被邀请人ID：{}，邀请记录ID：{}", 
                inviter.getId(), registrationDTO.getUserId(), invitationRecord.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processCertificationCommission(Long userId, Long memberId) {
        log.info("开始处理企业认证成功分佣，用户ID：{}，会员ID：{}", userId, memberId);

        // 查找邀请记录
        Optional<InvitationRecordDO> invitationRecordOpt = invitationRecordRepository.findByInviteeUserId(userId);
        if (!invitationRecordOpt.isPresent()) {
            log.info("未找到邀请记录，跳过认证分佣处理，用户ID：{}", userId);
            return;
        }

        InvitationRecordDO invitationRecord = invitationRecordOpt.get();

        // 更新邀请记录状态
        invitationRecord.setStatus(2); // 2-已注册已认证
        invitationRecord.setUpdateTime(System.currentTimeMillis());
        invitationRecordRepository.saveAndFlush(invitationRecord);

        // 处理认证奖励分佣
        processVerificationReward(invitationRecord.getInviterUserId(), userId, invitationRecord.getId());

        log.info("企业认证成功分佣处理完成，邀请人ID：{}，被邀请人ID：{}", invitationRecord.getInviterUserId(), userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processFirstOrderCommission(Long userId, Long memberId, BigDecimal orderAmount) {
        log.info("开始处理首次下单分佣，用户ID：{}，会员ID：{}，订单金额：{}", userId, memberId, orderAmount);

        // 查找邀请记录
        Optional<InvitationRecordDO> invitationRecordOpt = invitationRecordRepository.findByInviteeUserId(userId);
        if (!invitationRecordOpt.isPresent()) {
            log.info("未找到邀请记录，跳过首单分佣处理，用户ID：{}", userId);
            return;
        }

        InvitationRecordDO invitationRecord = invitationRecordOpt.get();

        // 更新邀请记录状态
        invitationRecord.setStatus(3); // 3-已认证已下单
        invitationRecord.setUpdateTime(System.currentTimeMillis());
        invitationRecordRepository.saveAndFlush(invitationRecord);

        // 处理首单奖励分佣
        processFirstOrderReward(invitationRecord.getInviterUserId(), userId, invitationRecord.getId(), orderAmount);

        log.info("首次下单分佣处理完成，邀请人ID：{}，被邀请人ID：{}", invitationRecord.getInviterUserId(), userId);
    }

    /**
     * 处理注册奖励分佣
     */
    private void processRegistrationReward(Long inviterUserId, Long inviteeUserId, Long invitationRecordId) {
        // 获取分佣配置
        CommissionConfigDO config = getCommissionConfig();
        if (Objects.isNull(config) || Objects.isNull(config.getConfigBO()) || 
            !config.getConfigBO().getInvitationRegistrationRewardEnabled()) {
            log.info("注册奖励分佣未启用，跳过处理");
            return;
        }

        BigDecimal rewardAmount = config.getConfigBO().getInvitationRegistrationReward();
        if (Objects.isNull(rewardAmount) || rewardAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("注册奖励金额为0，跳过处理");
            return;
        }

        // 发放分佣
        issueCommission(inviterUserId, rewardAmount, 1, "邀请注册奖励", invitationRecordId,inviteeUserId);
    }

    /**
     * 处理认证奖励分佣
     */
    private void processVerificationReward(Long inviterUserId, Long inviteeUserId, Long invitationRecordId) {
        // 获取分佣配置
        CommissionConfigDO config = getCommissionConfig();
        if (Objects.isNull(config) || Objects.isNull(config.getConfigBO()) || 
            !config.getConfigBO().getInvitationVerificationRewardEnabled()) {
            log.info("认证奖励分佣未启用，跳过处理");
            return;
        }

        BigDecimal rewardAmount = config.getConfigBO().getInvitationVerificationReward();
        if (Objects.isNull(rewardAmount) || rewardAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("认证奖励金额为0，跳过处理");
            return;
        }

        // 发放分佣
        issueCommission(inviterUserId, rewardAmount, 6, "邀请认证奖励", invitationRecordId, inviteeUserId);
    }

    /**
     * 处理首单奖励分佣
     */
    private void processFirstOrderReward(Long inviterUserId, Long inviteeUserId, Long invitationRecordId, BigDecimal orderAmount) {
        // 获取分佣配置
        CommissionConfigDO config = getCommissionConfig();
        if (Objects.isNull(config) || Objects.isNull(config.getConfigBO()) || 
            !config.getConfigBO().getInvitationFirstOrderRewardEnabled()) {
            log.info("首单奖励分佣未启用，跳过处理");
            return;
        }

        BigDecimal rewardAmount = config.getConfigBO().getInvitationFirstOrderReward();
        if (Objects.isNull(rewardAmount) || rewardAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.info("首单奖励金额为0，跳过处理");
            return;
        }

        // 发放分佣
        issueCommission(inviterUserId, rewardAmount, 3, "邀请首单奖励", invitationRecordId, inviteeUserId);
    }

    /**
     * 发放分佣
     */
    private void issueCommission(Long userId, BigDecimal amount, Integer commissionType, String description, Long invitationRecordId, Long inviteeUserId) {
        // 获取或创建分佣账户
        CommissionAccountDO account = getOrCreateCommissionAccount(userId);

        // 更新账户余额
        if (account.getAccountBalance() == null) {
            account.setAccountBalance(amount);
        } else {
            account.setAccountBalance(account.getAccountBalance().add(amount));
        }

        if (account.getWithdrawableBalance() == null) {
            account.setWithdrawableBalance(amount);
        } else {
            account.setWithdrawableBalance(account.getWithdrawableBalance().add(amount));
        }

        account.setUpdateTime(System.currentTimeMillis());
        commissionAccountRepository.saveAndFlush(account);

        // 创建分佣明细记录
        CommissionDetailDO detail = new CommissionDetailDO();
        detail.setCommissionAccountId(account.getId());
        detail.setUserId(userId);
        detail.setTradeCode("CD" + System.currentTimeMillis() + userId);
        detail.setTradeTime(System.currentTimeMillis());
        detail.setOriginalAmount(account.getAccountBalance().subtract(amount));
        detail.setChangeAmount(amount);
        detail.setCurrentAmount(account.getAccountBalance());
        detail.setTradeType(commissionType);
        detail.setRelatedUserId(inviteeUserId);
        detail.setRemark(description);
        detail.setCreateTime(System.currentTimeMillis());

        commissionDetailRepository.saveAndFlush(detail);

        log.info("分佣发放成功，用户ID：{}，金额：{}，类型：{}，描述：{}", userId, amount, commissionType, description);
    }

    /**
     * 获取或创建分佣账户
     */
    private CommissionAccountDO getOrCreateCommissionAccount(Long userId) {
        Optional<CommissionAccountDO> accountOpt = commissionAccountRepository.findByUserId(userId);
        CommissionAccountDO account;
        if (!accountOpt.isPresent()) {
            account = new CommissionAccountDO();
            account.setUserId(userId);
            account.setAccountStatus(1); // 1-正常
            account.setAccountBalance(BigDecimal.ZERO);
            account.setWithdrawableBalance(BigDecimal.ZERO);
            account.setToBeEarningsAmount(BigDecimal.ZERO);
            account.setWithdrawingAmount(BigDecimal.ZERO);
            account.setCreateTime(System.currentTimeMillis());
            account.setUpdateTime(System.currentTimeMillis());
            account = commissionAccountRepository.saveAndFlush(account);
        } else {
            account = accountOpt.get();
        }
        return account;
    }

    /**
     * 获取分佣配置
     */
    private CommissionConfigDO getCommissionConfig() {
        Optional<CommissionConfigDO> configOpt = commissionConfigRepository.findFirstBy();
        return configOpt.orElse(null);
    }

    /**
     * 批量处理历史账号：为没有邀请码的用户生成邀请码，为所有用户创建分佣账户
     * @return 处理结果统计
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String processHistoricalAccounts() {
        log.info("开始批量处理历史账号");

        int invitationCodeCount = 0;
        int commissionAccountCount = 0;
        int pageSize = 100;
        int pageNumber = 0;

        try {
            // 1. 处理没有邀请码的用户
            Pageable pageable = PageRequest.of(pageNumber, pageSize);
            Page<UserDO> usersWithoutInvitationCode;

            do {
                usersWithoutInvitationCode = userRepository.findUsersWithoutInvitationCode(pageable);

                for (UserDO user : usersWithoutInvitationCode.getContent()) {
                    // 生成唯一邀请码
                    String invitationCode = generateUniqueInvitationCode();
                    user.setInvitationCode(invitationCode);
                    userRepository.save(user);
                    invitationCodeCount++;

                    log.debug("为用户 {} 生成邀请码：{}", user.getId(), invitationCode);
                }

                pageNumber++;
                pageable = PageRequest.of(pageNumber, pageSize);

            } while (usersWithoutInvitationCode.hasNext());

            // 2. 为所有用户创建分佣账户（如果不存在）
            pageNumber = 0;
            pageable = PageRequest.of(pageNumber, pageSize);
            Page<UserDO> allUsers;

            do {
                allUsers = userRepository.findAll(pageable);

                for (UserDO user : allUsers.getContent()) {
                    // 检查是否已有分佣账户
                    Optional<CommissionAccountDO> existingAccount = commissionAccountRepository.findByUserId(user.getId());
                    if (!existingAccount.isPresent()) {
                        // 创建分佣账户
                        createCommissionAccountForUser(user.getId());
                        commissionAccountCount++;

                        log.debug("为用户 {} 创建分佣账户", user.getId());
                    }
                }

                pageNumber++;
                pageable = PageRequest.of(pageNumber, pageSize);

            } while (allUsers.hasNext());

            String result = String.format("批量处理完成：生成邀请码 %d 个，创建分佣账户 %d 个",
                    invitationCodeCount, commissionAccountCount);
            log.info(result);
            return result;

        } catch (Exception e) {
            log.error("批量处理历史账号失败", e);
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR, "批量处理历史账号失败：" + e.getMessage());
        }
    }

    /**
     * 为用户创建分佣账户
     * @param userId 用户ID
     */
    public void createCommissionAccountForUser(Long userId) {
        CommissionAccountDO account = new CommissionAccountDO();
        account.setUserId(userId);
        account.setAccountStatus(1); // 1-正常
        account.setAccountBalance(BigDecimal.ZERO);
        account.setWithdrawableBalance(BigDecimal.ZERO);
        account.setToBeEarningsAmount(BigDecimal.ZERO);
        account.setWithdrawingAmount(BigDecimal.ZERO);
        account.setCreateTime(System.currentTimeMillis());
        account.setUpdateTime(System.currentTimeMillis());
        commissionAccountRepository.saveAndFlush(account);
    }

    /**
     * 生成唯一的邀请码
     * @return 唯一的邀请码
     */
    private String generateUniqueInvitationCode() {
        String invitationCode;
        int maxRetries = 10; // 最大重试次数
        int retries = 0;

        do {
            invitationCode = CodeUtil.generateInvitationCode();
            retries++;
        } while (userRepository.existsByInvitationCode(invitationCode) && retries < maxRetries);

        if (retries >= maxRetries) {
            throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR, "生成邀请码失败，请重试");
        }

        return invitationCode;
    }
}
