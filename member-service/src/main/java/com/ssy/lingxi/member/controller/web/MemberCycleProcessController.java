package com.ssy.lingxi.member.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.model.req.platform.*;
import com.ssy.lingxi.member.model.resp.platform.BaseMemberCycleProcessResp;
import com.ssy.lingxi.member.model.resp.platform.MemberCycleProcessDetailResp;
import com.ssy.lingxi.member.model.resp.platform.MemberCycleProcessPageResp;
import com.ssy.lingxi.member.service.web.IMemberCycleProcessService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员能力-会员生命周期变更流程规则
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-29
 **/
@RestController
@RequestMapping(ServiceModuleConstant.MEMBER_PATH_PREFIX + "/lifeCycle/process")
public class MemberCycleProcessController extends BaseController {

    @Resource
    private IMemberCycleProcessService service;

    /**
     * 分页查询流程规则
     * @param request 请求
     * @param pageVO 分页查询条件
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MemberCycleProcessPageResp>> pageProcesses(HttpServletRequest request, @Valid MemberCycleProcessPageQueryDataReq pageVO) {
        return WrapperUtil.success(service.pageProcesses(getSysUser(request), pageVO, null));
    }

    /**
     * 基础流程列表
     * @param request 请求域
     * @return 查询结果
     */
    @GetMapping("/base/list")
    public WrapperResp<List<BaseMemberCycleProcessResp>> listBaseProcesses(HttpServletRequest request, @Valid ProcessQueryReq queryRequest) {
        return WrapperUtil.success(service.listBaseProcesses(getSysUser(request), queryRequest));
    }

    /**
     * 设置默认流程
     * @param request HttpHeaders信息
     * @param defaultRequest 接口参数
     * @return Void
     */
    @PostMapping("/saveDefault")
    public WrapperResp<Void> saveDefault(HttpServletRequest request, @RequestBody @Valid SaveDefaultReq defaultRequest) {
        service.saveDefault(getSysUser(request), defaultRequest);
        return WrapperUtil.success();
    }

    /**
     * 新增流程规则
     * @param request 请求域
     * @param saveVO 流程规则
     * @return 新增结果
     */
    @PostMapping("/save")
    public WrapperResp<Void> save(HttpServletRequest request, @RequestBody @Valid MemberCycleProcessReq saveVO) {
        service.save(getSysUser(request), saveVO);
        return WrapperUtil.success();
    }

    /**
     * 查询流程规则详情
     * @param request 请求域
     * @param processId 流程id
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<MemberCycleProcessDetailResp> getInfo(HttpServletRequest request, @RequestParam("processId") Long processId) {
        return WrapperUtil.success(service.getInfo(getSysUser(request), processId));
    }

    /**
     * 修改流程规则
     * @param request 请求域
     * @param updateVO 修改数据
     * @return 修改结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(HttpServletRequest request, @RequestBody @Valid MemberCycleProcessUpdateReq updateVO) {
        service.update(getSysUser(request), updateVO);
        return WrapperUtil.success();
    }

    /**
     * 修改流程状态
     * @param request 请求域
     * @param updateStatusVO 修改状态
     * @return 修改结果
     */
    @PostMapping("/update/status")
    public WrapperResp<Void> updateStatus(HttpServletRequest request, @RequestBody @Valid MemberCycleProcessUpdateStatusReq updateStatusVO) {
        service.updateStatus(getSysUser(request), updateStatusVO);
        return WrapperUtil.success();
    }

    /**
     * 删除流程规则
     * @param request 请求域
     * @param processId 要删除的流程规则id
     * @return 删除结果
     */
    @GetMapping("/delete")
    public WrapperResp<Void> delete(HttpServletRequest request, @RequestParam("processId") Long processId) {
        service.delete(getSysUser(request), processId);
        return WrapperUtil.success();
    }

}
