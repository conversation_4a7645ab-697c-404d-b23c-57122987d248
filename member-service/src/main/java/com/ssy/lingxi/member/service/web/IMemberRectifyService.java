package com.ssy.lingxi.member.service.web;

import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.member.entity.do_.basic.MemberDO;
import com.ssy.lingxi.member.entity.do_.basic.MemberRoleDO;
import com.ssy.lingxi.member.model.req.lifecycle.*;
import com.ssy.lingxi.member.model.resp.lifecycle.*;
import org.springframework.http.HttpHeaders;

import java.util.List;

/**
 * 会员整改服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
public interface IMemberRectifyService {

    /**
     * 外部状态下拉查询(上级会员)
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<StatusResp> listMemberRectifyOuterStatus(HttpHeaders headers);

    /**
     * 外部状态下拉查询(下级级会员)
     * @param headers Http头部信息
     * @return 查询结果
     */
    List<StatusResp> listSubMemberRectifyOuterStatus(HttpHeaders headers);

    /**
     * 会员整改查询 - 会员整改分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberRectifySummaryPageQueryResp> pageSummaryMemberRectify(HttpHeaders headers, MemberAddRectifySummaryPageDataReq pageVO, Integer roleTag);

    /**
     * 会员信息 - 会员详情 - 分页查询会员整改
     * @param member 上级会员
     * @param role 上级会员角色
     * @param subMember 下级会员
     * @param subRole 下级会员角色
     * @param current 当前页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    PageDataResp<MemberRecordRectifyResp> pageMemberRecordRectify(MemberDO member, MemberRoleDO role, MemberDO subMember, MemberRoleDO subRole, int current, int pageSize);

    /**
     * 待新增整改通知单 - 会员整改分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberRectifyAddPageQueryResp> pageAddMemberRectify(HttpHeaders headers, MemberAddRectifyPageDataReq pageVO, Integer roleTag);

    /**
     * 待确认整改结果 - 会员整改分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    PageDataResp<MemberRectifyConfirmPageQueryResp> pageConfirmMemberRectify(HttpHeaders headers, MemberAddRectifyPageDataReq pageVO, Integer roleTag);

    /**
     * 会员整改管理 - 会员整改分页列表
     * @param headers Http头部信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    PageDataResp<MemberRectifyManagePageQueryResp> pageManageMemberRectify(HttpHeaders headers, MemberAddRectifySummaryPageDataReq pageVO);

    /**
     * 待确认整改结果 - 会员整改详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberRectifyResp getMemberRectify(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 会员整改查询 - 会员整改详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 查询结果
     */
    MemberRectifyResultResp getMemberRectifyResult(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 查询会员整改详情
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 查询结果
     */
    MemberRectifyManageResp getMemberRectifyManage(HttpHeaders headers, CommonIdReq idVO);

    /**
     * 会员整改管理 - 会员整改修改
     * @param headers Http头部信息
     * @param reportVO 接口参数
     */
    void updateReportMemberRectify(HttpHeaders headers, MemberRectifyReportReq reportVO);

    /**
     * 待确认整改结果 - 会员整改新增
     *
     * @param headers Http头部信息
     * @param addVO   接口参数
     * @param roleTag 角色标签
     * @return 新增结果
     */
    void addMemberRectify(HttpHeaders headers, MemberRectifyAddReq addVO, Integer roleTag);

    /**
     * 确认整改结果 - 会员整改修改
     * @param headers Http头部信息
     * @param updateVO 接口参数
     * @return 修改结果
     */
    void updateMemberRectify(HttpHeaders headers, MemberRectifyUpdateReq updateVO);

    /**
     * 待确认整改结果 - 会员整改删除
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @return 删除结果
     */
    void deleteMemberRectify(HttpHeaders headers, CommonIdReq idVO);

    /**
     * 待确认整改结果 - 发送
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void sendMemberRectify(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 待确认整改结果 - 整改
     * @param headers Http头部信息
     * @param idVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void rectifyMemberRectify(HttpHeaders headers, CommonIdReq idVO, Integer roleTag);

    /**
     * 待确认整改结果 - 确认
     * @param headers Http头部信息
     * @param agreeVO 接口参数
     * @param roleTag 角色标签
     * @return 操作结果
     */
    void confirmMemberRectify(HttpHeaders headers, CommonAgreeReq agreeVO, Integer roleTag);
}
