package com.ssy.lingxi.member.model.req.lifecycle;

import com.ssy.lingxi.member.handler.annotation.DateStringFormatAnnotation;
import com.ssy.lingxi.member.model.req.basic.FileUploadReq;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;
import java.util.List;

/**
 * 会员考评新增VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/5/17
 */
@Data
public class MemberAppraisalAddReq implements Serializable {
    private static final long serialVersionUID = 1830941838746529993L;

    /**
     * 下级会员Id
     */
    @NotNull(message = "下级会员Id要大于0")
    @Positive(message = "下级会员Id要大于0")
    private Long subMemberId;

    /**
     * 下级会员角色Id
     */
    @NotNull(message = "下级会员角色Id要大于0")
    @Positive(message = "下级会员角色Id要大于0")
    private Long subRoleId;

    /**
     * 考评主题
     */
    @NotBlank(message = "考评主题不能为空")
    private String subject;

    /**
     * 考评时间开始, 格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation(message = "考评时间开始格式错误")
    private String appraisalDayStart;

    /**
     * 考评时间结束, 格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation(message = "考评时间结束格式错误")
    private String appraisalDayEnd;

    /**
     * 考评完成时间, 格式为yyyy-MM-dd
     */
    @DateStringFormatAnnotation(message = "考评完成时间格式错误")
    private String completeDay;

    /**
     * 考察附件
     */
    @Valid
    private List<FileUploadReq> attachments;

    /**
     * 考评项目
     */
    @Valid
    @NotNull(message = "考评项目不允许为空")
    private List<MemberAppraisalItemAddReq> items;

    /**
     * 考评结果
     */
    @Valid
    private MemberAppraisalAddResultReq submitVO;
}
