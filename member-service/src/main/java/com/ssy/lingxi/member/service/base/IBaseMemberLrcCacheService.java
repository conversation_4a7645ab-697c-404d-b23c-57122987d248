package com.ssy.lingxi.member.service.base;

import com.ssy.lingxi.common.model.dto.MemberLrcCacheDTO;

import java.math.BigDecimal;

/**
 * 平台会员等级、权益、信用信息缓存接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-11-23
 */
public interface IBaseMemberLrcCacheService {

    /**
     * 系统启动完成后，初始化所有平台会员的等级、权益、信用信息
     */
    void initMemberLrcCache();

    /**
     * 获取缓存中的平台会员等级、权益、信用信息
     * @param memberId 会员Id
     * @param roleId 角色Id
     * @return 缓存信息实体
     */
    MemberLrcCacheDTO getLrc(Long memberId, Long roleId);

    /**
     * 缓存会员的等级、权益、信用信息
     * @param memberId 会员Id
     * @param roleId   角色Id
     * @param cacheDTO 等级、权益、信用信息
     */
    void setLrc(Long memberId, Long roleId, MemberLrcCacheDTO cacheDTO);

    /**
     * 缓存会员等级信息
     * @param memberId 会员Id
     * @param roleId 角色Id
     * @param level 会员等级
     * @param levelTag 等级名称
     * @param score 等级积分
     */
    void cacheMemberLevel(Long memberId, Long roleId, Integer level, String levelTag, Integer score);

    /**
     * 缓存会员权益信息
     * @param memberId 会员Id
     * @param roleId 角色Id
     * @param sumReturnMoney 累计返现金额
     * @param sumPoint 累计获得的权益积分
     * @param currentPoint 当前可用的权益积分 = 累计获得的权益积分 - 累计已用权益积分
     * @param sumUsedPoint 累计使用的权益积分
     */
    void cacheMemberRight(Long memberId, Long roleId, BigDecimal sumReturnMoney, Integer sumPoint, Integer currentPoint, Integer sumUsedPoint);

    /**
     * 缓存会员信用信息
     * @param memberId 会员Id
     * @param roleId 角色Id
     * @param creditPoint 信用积分 = 交易评价积分 + 售后评价积分 + 投诉扣分 + 入驻年数积分
     * @param tradeCommentPoint 交易评价信用积分
     * @param afterSaleCommentPoint 售后评价信用积分
     * @param complainPoint 投诉扣分
     * @param registerYearsPoint 入驻年数积分
     * @param avgTradeCommentStar 交易评价平均星级（总体满意度）
     * @param registerYears 注册年数
     */
    void cacheMemberCredit(Long memberId, Long roleId, Integer creditPoint, Integer tradeCommentPoint, Integer afterSaleCommentPoint, Integer complainPoint, Integer registerYearsPoint, Integer avgTradeCommentStar, Integer registerYears);
}
