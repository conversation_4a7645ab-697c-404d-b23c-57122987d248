package com.ssy.lingxi.member.model.req.validate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberCancellationValidateReq implements Serializable {
    private static final long serialVersionUID = 4024980867548829334L;

    /**
     * 会员Id
     */
    @NotNull(message = "会员id要大于0")
    @Positive(message = "会员id要大于0")
    private Long memberId;

    /**
     * 审核通过：true-通过，false-不通过
     */
    @NotNull(message = "审核是否通过不能为空")
    private Boolean verify;

    /**
     * 审核意见
     */
    @Size(max = 120, message = "审核意见最长120个字符")
    private String cancellationComments;
}
