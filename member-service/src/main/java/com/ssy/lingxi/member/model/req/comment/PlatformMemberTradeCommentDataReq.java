package com.ssy.lingxi.member.model.req.comment;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 会员评价接口查询参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/14
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformMemberTradeCommentDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -7613755024237924638L;

    /**
     * 会员类型
     */
    private Integer memberType;

    /**
     * 会员角色id
     */
    private Long roleId;

    /**
     * 会员等级
     */
    private Integer level;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 总体满意度
     */
    private Integer avgStar;
}
