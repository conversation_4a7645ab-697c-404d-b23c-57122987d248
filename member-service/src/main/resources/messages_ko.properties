CountryAreaFilterRuleEnum.0=상위 영역은 하위 영역을 완전히 포함해야 합니다.
CountryAreaFilterRuleEnum.1=상위 영역과 하위 영역은 교차점만 있으면 됩니다.

MemberApplyButtonStatusEnum.0=저희 스토어 회원가입 신청
MemberApplyButtonStatusEnum.1=회원 신청
MemberApplyButtonStatusEnum.2=회원가입
MemberApplyButtonStatusEnum.3=인바운드 회원 신청
MemberApplyButtonStatusEnum.4=인바운드 회원 신청
MemberApplyButtonStatusEnum.5=저희 스토어 회원가입 신청
MemberApplyButtonStatusEnum.6=

MemberApplyButtonStatusEnum.0.c=저희 스토어 회원가입 신청
MemberApplyButtonStatusEnum.1.c=창고 공급자 신청
MemberApplyButtonStatusEnum.2.c=창고 공급업체가 되었습니다.
MemberApplyButtonStatusEnum.3.c=창고 공급업체 신청
MemberApplyButtonStatusEnum.4.c=창고 공급업체 신청
MemberApplyButtonStatusEnum.5.c=저희 스토어 회원가입 신청
MemberApplyButtonStatusEnum.6.c=

MemberAppraisalItemStatusEnum.0=평가 예정
MemberAppraisalItemStatusEnum.1=평가 및 채점

MemberAppraisalItemTypeEnum.0=사용자 정의
MemberAppraisalItemTypeEnum.1=정시 배송율
MemberAppraisalItemTypeEnum.2=보급 자격 비율

MemberAppraisalStatusEnum.1=발행 예정인 평가서
MemberAppraisalStatusEnum.2=평가 예정
MemberAppraisalStatusEnum.3=제출할 요약 평가 결과
MemberAppraisalStatusEnum.4=평가 결과 보류 중(레벨 1)
MemberAppraisalStatusEnum.5=평가 결과 검토 실패(레벨 1)
MemberAppraisalStatusEnum.6=평가 결과 보류 중(레벨 2)
MemberAppraisalStatusEnum.7=평가 결과 검토 실패(레벨 2)
MemberAppraisalStatusEnum.8=평가 결과 통보
MemberAppraisalStatusEnum.9=평가 결과 알림

MemberChangeRequestFormStatusEnum.1=변경 신청서 제출
MemberChangeRequestFormStatusEnum.2=채점 대기자 채점
MemberChangeRequestFormStatusEnum.3=요약 대기 평가 결과
MemberChangeRequestFormStatusEnum.4=합산 평가 결과가 통과되지 않음
MemberChangeRequestFormStatusEnum.5=보류 중인 변경 신청서(1급)
MemberChangeRequestFormStatusEnum.6=감사 변경이 통과되지 않음 (1 레벨)
MemberChangeRequestFormStatusEnum.7=보류 중인 변경 신청서(2급)
MemberChangeRequestFormStatusEnum.8=감사 변경이 통과되지 않음 (2 레벨)
MemberChangeRequestFormStatusEnum.9=확인 대기 변경 신청서
MemberChangeRequestFormStatusEnum.10=변경 신청 통과
MemberChangeRequestFormStatusEnum.11=변경 신청이 통과되지 않다

MemberComplaintClassifyEnum.1=제품 정보
MemberComplaintClassifyEnum.2=주문 정보
MemberComplaintClassifyEnum.3=배송 정보
MemberComplaintClassifyEnum.4=판매 후 정보
MemberComplaintClassifyEnum.5=서비스 정보
MemberComplaintClassifyEnum.6=기타

MemberComplaintComplaintTypeEnum.1=상급자가 시작함
MemberComplaintComplaintTypeEnum.2=하위 시작됨

MemberComplaintStatusEnum.1=제출할 불만 사항 제안
MemberComplaintStatusEnum.2=불만 제안 대기 중
MemberComplaintStatusEnum.3=불만 제안 처리됨

MemberComplaintTypeEnum.1=불만
MemberComplaintTypeEnum.2=제안

MemberConfigStatusEnum.0=비활성화됨
MemberConfigStatusEnum.1=활성화

MemberDepositoryStatusEnum.0=저장소 데이터 추가
MemberDepositoryStatusEnum.1=검토할 데이터
MemberDepositoryStatusEnum.2=검토할 예탁금 자격
MemberDepositoryStatusEnum.3=검사 예정
MemberDepositoryStatusEnum.4=저장소 분류 보류 중
MemberDepositoryStatusEnum.5=저장을 위해 검토됨(레벨 1)
MemberDepositoryStatusEnum.6=저장을 위해 검토됨(레벨 2)
MemberDepositoryStatusEnum.7=저장 확인 예정

MemberDetailVersionEnum.0=추가됨, 보류 중인 버전
MemberDetailVersionEnum.1=사용 중인 버전

MemberInnerStatusEnum.0=하위 멤버를 새로 만들거나 추가했습니다.
MemberInnerStatusEnum.1=검토할 수신 데이터
MemberInnerStatusEnum.2=인바운드 데이터 검토 실패
MemberInnerStatusEnum.3=저장 자격 검토 예정
MemberInnerStatusEnum.4=창고 적격성 검토 실패
MemberInnerStatusEnum.5=검사 예정
MemberInnerStatusEnum.6=재고 검사 실패
MemberInnerStatusEnum.7=재고 분류 대기 중
MemberInnerStatusEnum.8=인바운드 분류 실패
MemberInnerStatusEnum.9=검토 및 저장(레벨 1)
MemberInnerStatusEnum.10=인바운드 검토 실패(레벨 1)
MemberInnerStatusEnum.11=검토 및 보관(레벨 2)
MemberInnerStatusEnum.12=인바운드 검토 실패(레벨 2)
MemberInnerStatusEnum.13=창고 확인 예정
MemberInnerStatusEnum.14=인바운드 검토 통과
MemberInnerStatusEnum.15=인바운드 검토 실패
MemberInnerStatusEnum.16=검토 변경 보류 중(레벨 1)
MemberInnerStatusEnum.17=변경 검토 실패(레벨 1)
MemberInnerStatusEnum.18=검토할 변경 사항(레벨 2)
MemberInnerStatusEnum.19=변경 검토 실패(레벨 2)
MemberInnerStatusEnum.20=확인할 변경 사항
MemberInnerStatusEnum.21=변경 승인됨
MemberInnerStatusEnum.22=변경 검토 실패
MemberInnerStatusEnum.23=로그아웃됨

MemberInspectSourceEnum.0=멤버 기능 기능을 통해 생성
MemberInspectSourceEnum.1=회원 검토 관련 절차를 통해 생성

MemberInspectTypeEnum.0=모두
MemberInspectTypeEnum.1=검사
MemberInspectTypeEnum.2=정정 검사
MemberInspectTypeEnum.3=검사 계획
MemberInspectTypeEnum.4=기타 검사

MemberInviteButtonStatusEnum.0=이 회사는 아직 내 회원이 아닙니다. 지금 내 회원이 되도록 초대하십시오.
MemberInviteButtonStatusEnum.1=이 회사는 이미 내 회원입니다. 지금 주문하십시오.
MemberInviteButtonStatusEnum.2=이 회사는 이미 내 회원입니다. 지금 주문하십시오.
MemberInviteButtonStatusEnum.3=이 회사는 아직 내 회원이 아닙니다. 지금 내 회원이 되도록 초대하십시오.
MemberInviteButtonStatusEnum.4=이 회사는 이미 블랙리스트에 등록된 회원입니다!
MemberInviteButtonStatusEnum.5=이 회사는 아직 내 회원이 아닙니다. 지금 내 회원이 되도록 초대하십시오.
MemberInviteButtonStatusEnum.6=

MemberManageOrderTypeEnum.5=구매 주문 배치
MemberManageOrderTypeEnum.6=수동 주문
MemberManageOrderTypeEnum.7=주문을 위한 견적문의
MemberManageOrderTypeEnum.8=주문하려면 견적 요청
MemberManageOrderTypeEnum.9=주문을 병합하려면 주문하십시오.
MemberManageOrderTypeEnum.10=주문을 위한 채널 직접 구매 주문
MemberManageOrderTypeEnum.11=채널 직접 구매 수동 주문
MemberManageOrderTypeEnum.12=주문을 위한 채널 현물 구매 주문
MemberManageOrderTypeEnum.13=채널 스팟에서 수동 주문
MemberManageOrderTypeEnum.99=알 수 없음

MemberOuterStatusEnum.1=검토를 위해 제출 예정
MemberOuterStatusEnum.2=플랫폼에서 검토 예정
MemberOuterStatusEnum.3=플랫폼이 감사를 통과했습니다.
MemberOuterStatusEnum.4=플랫폼 검토 실패
MemberOuterStatusEnum.5=검토 및 저장
MemberOuterStatusEnum.6=인바운드 검토 통과
MemberOuterStatusEnum.7=인바운드 검토 실패
MemberOuterStatusEnum.8=보류 중인 변경 사항
MemberOuterStatusEnum.9=변경 승인됨
MemberOuterStatusEnum.10=변경 검토 실패
MemberOuterStatusEnum.11=로그아웃됨
MemberOuterStatusEnum.99=알 수 없음

MemberPartnerTypeEnum.1=전략적
MemberPartnerTypeEnum.2=코어 유형
MemberPartnerTypeEnum.3=적격 유형
MemberPartnerTypeEnum.4=잠재 유형

MemberRectifyAgreeResultEnum.0=수정 실패
MemberRectifyAgreeResultEnum.1=수정 통과

MemberRectifyStatusEnum.1=보류 중인 수정 알림
MemberRectifyStatusEnum.2=수정 예정
MemberRectifyStatusEnum.3=수정 결과 확인 예정
MemberRectifyStatusEnum.4=수정 결과 확인 실패
MemberRectifyStatusEnum.5=수정 결과 통과 확인

MemberRegisterSourceEnum.1=WEB 엔터프라이즈 몰 애플리케이션
MemberRegisterSourceEnum.2=H5 엔터프라이즈 몰 애플리케이션
MemberRegisterSourceEnum.3=APP 기업몰 신청
MemberRegisterSourceEnum.4=애플릿 기업 쇼핑몰 신청
MemberRegisterSourceEnum.5=WEB 포인트 쇼핑몰 신청
MemberRegisterSourceEnum.6=H5 포인트 쇼핑몰 신청
MemberRegisterSourceEnum.7=APP 포인트 쇼핑몰 신청
MemberRegisterSourceEnum.8=애플릿 포인트 쇼핑몰 신청
MemberRegisterSourceEnum.9= 플랫폼 대리 입력 등록
MemberRegisterSourceEnum.10=사업자 대리 입력 등록
MemberRegisterSourceEnum.99=플랫폼 백엔드
MemberRegisterSourceEnum.100=알 수 없음

MemberRelationSourceEnum.0=웹 등록을 통해 하위 회원이 되십시오.
MemberRelationSourceEnum.1=모바일 단말기를 통해 하위 회원으로 등록
MemberRelationSourceEnum.2=새 역할을 추가하여 하위 구성원이 되십시오.
MemberRelationSourceEnum.3=플랫폼 백엔드를 통해 생성
MemberRelationSourceEnum.4=구성원 기능에 의해 생성됨
MemberRelationSourceEnum.5=하위 멤버가 되기 위해 적극적으로 지원
MemberRelationSourceEnum.99=알 수 없음

MemberRightScoreBehaviorEnum.0=모두
MemberRightScoreBehaviorEnum.1=포인트 받기
MemberRightScoreBehaviorEnum.2=포인트 사용

MemberTradeTypeEnum.1=구매 회원
MemberTradeTypeEnum.2=회원 공급

MemberValidateAgreeEnum.1=동의
MemberValidateAgreeEnum.0=동의하지 않음

MemberValidateHistoryOperationEnum.1=등록 신청
MemberValidateHistoryOperationEnum.2=데이터 변경
MemberValidateHistoryOperationEnum.3=플랫폼 검토를 위해 제출
MemberValidateHistoryOperationEnum.4=구성원 확인
MemberValidateHistoryOperationEnum.5=항목
MemberValidateHistoryOperationEnum.6=멤버 스토리지 신청
MemberValidateHistoryOperationEnum.7=회원 변경 신청
MemberValidateHistoryOperationEnum.8=회원 동결
MemberValidateHistoryOperationEnum.9=멤버 고정 해제
MemberValidateHistoryOperationEnum.10=해제 관계
MemberValidateHistoryOperationEnum.11=블랙리스트 회원
MemberValidateHistoryOperationEnum.12=등록 정보 수정
MemberValidateHistoryOperationEnum.13=제출할 평가 결과 요약
MemberValidateHistoryOperationEnum.14=평가 양식 게시
MemberValidateHistoryOperationEnum.15=수정 알림 보내기
MemberValidateHistoryOperationEnum.16=멤버십 변경 검토 실패
MemberValidateHistoryOperationEnum.17=멤버십 변경 검토 실패(레벨 2)
MemberValidateHistoryOperationEnum.18=멤버십 변경 검토 실패(레벨 1)
MemberValidateHistoryOperationEnum.19=회원 변경 승인 통과
MemberValidateHistoryOperationEnum.20=정정 결과 확인
MemberValidateHistoryOperationEnum.21=인바운드 분류
MemberValidateHistoryOperationEnum.22=인바운드 분류 실패
MemberValidateHistoryOperationEnum.23=검사
MemberValidateHistoryOperationEnum.24=인바운드 검사 실패
MemberValidateHistoryOperationEnum.25=인바운드 감사 실패(레벨 2)
MemberValidateHistoryOperationEnum.26=창고 감사 실패(레벨 1)
MemberValidateHistoryOperationEnum.27=인바운드 데이터 검토 실패
MemberValidateHistoryOperationEnum.28=승인 실패
MemberValidateHistoryOperationEnum.29=멤버십 확인(레벨 2)
MemberValidateHistoryOperationEnum.30=멤버십 확인(레벨 1)
MemberValidateHistoryOperationEnum.31=회원 변경 확인
MemberValidateHistoryOperationEnum.32=멤버십 변경 사항 확인(레벨 2)
MemberValidateHistoryOperationEnum.33=멤버십 변경 확인(레벨 1)
MemberValidateHistoryOperationEnum.34=멤버십 승인 실패
MemberValidateHistoryOperationEnum.35=멤버십 승인 실패(레벨 2)
MemberValidateHistoryOperationEnum.36=멤버십 승인 실패(레벨 1)
MemberValidateHistoryOperationEnum.37=승인된 회원
MemberValidateHistoryOperationEnum.38=평가 결과 확인
MemberValidateHistoryOperationEnum.39=재고 확인
MemberValidateHistoryOperationEnum.40=재고 확인(레벨 2)
MemberValidateHistoryOperationEnum.41=재고 확인(레벨 1)
MemberValidateHistoryOperationEnum.42=저장 데이터 검토
MemberValidateHistoryOperationEnum.43=창고 자격 확인
MemberValidateHistoryOperationEnum.44=승인됨
MemberValidateHistoryOperationEnum.45=데이터 저장 감사
MemberValidateHistoryOperationEnum.46=요약 평가 결과 제출
MemberValidateHistoryOperationEnum.47=제출 검토 실패
MemberValidateHistoryOperationEnum.48=검토를 위해 회원 제출
MemberValidateHistoryOperationEnum.49=수정 제출
MemberValidateHistoryOperationEnum.50=평가 결과 통보
MemberValidateHistoryOperationEnum.51=인바운드 감사 실패
MemberValidateHistoryOperationEnum.52=인벤토리 자격 검토 실패
MemberValidateHistoryOperationEnum.53=회원 가입을 신청하다
MemberValidateHistoryOperationEnum.54=변경 신청서 제출
MemberValidateHistoryOperationEnum.55=채점자
MemberValidateHistoryOperationEnum.56=합산 평가 결과 통과
MemberValidateHistoryOperationEnum.57=변경 신청 1급 심사 통과
MemberValidateHistoryOperationEnum.58=변경 신청 1 급 심사 불통과
MemberValidateHistoryOperationEnum.59=변경 신청 2급 심사 통과
MemberValidateHistoryOperationEnum.60=변경 신청 2급 심사 불통과
MemberValidateHistoryOperationEnum.61=변경 신청 통과
MemberValidateHistoryOperationEnum.62=변경 신청이 통과되지 않다
MemberValidateHistoryOperationEnum.63=로그아웃 신청
MemberValidateHistoryOperationEnum.64=로그아웃 승인 불통과
MemberValidateHistoryOperationEnum.65=로그아웃 승인
MemberValidateHistoryOperationEnum.99=알 수 없음

MemberValidateStatusEnum.0=플랫폼 또는 회원 검토 실패
MemberValidateStatusEnum.1=플랫폼 또는 회원이 검토를 통과했습니다.

MobileApplyButtonStatusEnum.0=인증 확인 및 스토어 회원가입
MobileApplyButtonStatusEnum.1=승인 확인 및 저희 스토어 회원가입(검토중)
MobileApplyButtonStatusEnum.2=
MobileApplyButtonStatusEnum.3=인증 확인 및 저희 스토어 회원가입(검토 실패)
MobileApplyButtonStatusEnum.4=
MobileApplyButtonStatusEnum.5=인증 확인 및 스토어 회원가입
MobileApplyButtonStatusEnum.6=
MobileApplyButtonStatusEnum.7=
MobileApplyButtonStatusEnum.8=자신의 계열사가 될 수 없습니다.

PlatformInnerStatusEnum.0=등록 신청
PlatformInnerStatusEnum.1=플랫폼 검토를 위해 제출 예정
PlatformInnerStatusEnum.2=제출 검토 실패
PlatformInnerStatusEnum.3=플랫폼에서 검토 예정(레벨 1)
PlatformInnerStatusEnum.4=회원 승인 실패(레벨 1)
PlatformInnerStatusEnum.5=플랫폼에서 검토 예정(레벨 2)
PlatformInnerStatusEnum.6=멤버십 감사 실패(레벨 2)
PlatformInnerStatusEnum.7=보류 중인 멤버십
PlatformInnerStatusEnum.8=플랫폼 감사 실패
PlatformInnerStatusEnum.9=플랫폼 감사 통과
PlatformInnerStatusEnum.10=로그아웃됨

MemberConfigTagEnum.1=회원 이름
MemberConfigTagEnum.2=회원 휴대폰 번호
MemberConfigTagEnum.3=회원 ID 번호
MemberConfigTagEnum.4=회원 ID 카드 앞면
MemberConfigTagEnum.5=회원 ID 카드의 뒷면
MemberConfigTagEnum.6=실명
MemberConfigTagEnum.7=법정 전화번호
MemberConfigTagEnum.8=법인 ID 번호
MemberConfigTagEnum.9=법인 신분증 앞면
MemberConfigTagEnum.10=법인 신분증 뒷면
MemberConfigTagEnum.11=균일 신용 코드
MemberConfigTagEnum.12=등록 자본
MemberConfigTagEnum.13=설립 날짜
MemberConfigTagEnum.14=사업 허가증
MemberConfigTagEnum.15=등록된 주소(도, 시, 구)
MemberConfigTagEnum.16=등록 주소(상세 주소)

MemberRightTypeEnum.1=가격 자산
MemberRightTypeEnum.2=캐쉬백 혜택
MemberRightTypeEnum.3=포인트 혜택

MemberRightTypeEnum.1.r=거래의 한쪽이 다른 쪽의 가격 할인을 받을 수 있습니다.
MemberRightTypeEnum.2.r=거래 당사자 중 한쪽이 상대방의 거래 캐시백을 받을 수 있습니다.
MemberRightTypeEnum.3.r=거래의 한쪽은 다른 쪽의 포인트를 얻을 수 있습니다

MemberCreditTypeEnum.1=거래 등급
MemberCreditTypeEnum.2=불만
MemberCreditTypeEnum.3=판매 후 평가
MemberCreditTypeEnum.4=멤버십 기간

MemberCreditTypeEnum.1.r=거래 완료 후 평가가 5점일 때 신용 점수 증액
MemberCreditTypeEnum.2.r=크레딧 포인트를 차감하는 것으로 확인된 불만 제기
MemberCreditTypeEnum.3.r=판매 완료 후 평가가 5점일 때 크레딧 포인트 증가
MemberCreditTypeEnum.4.r=회원 신청 후 매년 크레딧 포인트 증액

MemberRightAcquireWayEnum.1=무역 취득

MemberRightParamWayEnum.1=거래 금액의 비율에 따라 설정

MemberLevelRuleTypeEnum.1=거래
MemberLevelRuleTypeEnum.2=로그인
MemberLevelRuleTypeEnum.3=평가

MemberLevelRuleTypeEnum.1.r=거래 주문 완료 후 거래 주문 금액의 일정 비율이 획득한 업그레이드 포인트에 적립됩니다.
MemberLevelRuleTypeEnum.2.r=각 로그인 성공 후 획득한 업그레이드 점수는 설정 점수에 따라 기록됩니다
MemberLevelRuleTypeEnum.3.r=각 평가 성공 후 설정된 점수에 따라 획득한 업그레이드 점수에 반영됩니다.

MemberRightSpendTypeEnum.1=포인트 주문
MemberRightSpendTypeEnum.2=쿠폰 사용을 위한 포인트
MemberRightSpendTypeEnum.3=포인트 차감 주문 금액
MemberRightSpendTypeEnum.4=플랫폼 포인트 공제

MerchantMemberStringEnum.1=주문 번호:

CheckStatusEnum.1=지나가 다
CheckStatusEnum.2=불합격

VisitTypeEnum.1=연례 방문
VisitTypeEnum.2=분기별 방문
VisitTypeEnum.3=월간 방문
VisitTypeEnum.4=주문 방문
VisitTypeEnum.5=방문 도입
VisitTypeEnum.6=담당자 변경 방문
VisitTypeEnum.7=기타 방문

SettlementDocumentsEnum.1=주문
SettlementDocumentsEnum.2=물류 명세서
SettlementDocumentsEnum.3=생산 고지서
SettlementDocumentsEnum.4=송장
SettlementDocumentsEnum.5=청구서
SettlementDocumentsEnum.6=청구서
SettlementDocumentsEnum.7=청구서 + 청구서

CurrencyTypeEnum.1=CNY- 인민폐
CurrencyTypeEnum.2=USD-달러
CurrencyTypeEnum.3=JPY-엔
CurrencyTypeEnum.4=EUR- 유로

AdvanceChargeEnum.1=선불 불필요
AdvanceChargeEnum.2=선불 필요

VisitLevelEnum.1=일반
VisitLevelEnum.2=중요

MemberChangeProjectNameEnum.0=자격 증명 사진

ScoringTemplateTypeEnum.1=공급업체 수명 주기 변경 요청
ScoringTemplateTypeEnum.2=공급업체 점수
ScoringTemplateTypeEnum.3=고객 수명 주기 변경 요청
ScoringTemplateTypeEnum.4=고객 점수

ScoringTemplateStateEnum.0=비활성화
ScoringTemplateStateEnum.1=활성화

MemberInviteTypeEnum.1=등록
MemberInviteTypeEnum.2=발견하다
MemberInviteTypeEnum.3=초대

InvitationCodeStateEnum.1=전송되지 않음
InvitationCodeStateEnum.2=등록되지 않음
InvitationCodeStateEnum.3=등록됨
InvitationCodeStateEnum.4=무효

MemberCycleProcessEnum.0=공급자 변경 요청에 대한 검토 프로세스 없음
MemberCycleProcessEnum.1=공급자 변경 요청 프로세스 수준 1
MemberCycleProcessEnum.2=공급자 변경 신청의 전 과정
MemberCycleProcessEnum.0.r=변경 요청 양식 제출 -> 채점자별 채점 -> 집계 점수 결과 -> 확인 변경 요청 양식
MemberCycleProcessEnum.1.r=변경 요청 양식 제출 -> 채점자별 채점 -> 집계 점수 결과 -> 검토 변경 요청 양식(레벨 1) -> 확인된 변경 요청 양식
MemberCycleProcessEnum.2.r=변경 요청 양식 제출 -> 채점자에 의해 채점 -> 점수 결과 집계 -> 변경 요청 양식 검토(레벨 1) -> 변경 요청 양식 검토(레벨 2) -> 변경 확인 하나 요청
MemberCycleProcessEnum.3=고객의 변경 요청에 대한 검토 프로세스가 없습니다.
MemberCycleProcessEnum.4=고객 변경 요청 프로세스 수준 1
MemberCycleProcessEnum.5=고객 변경 신청의 전 과정
MemberCycleProcessEnum.3.r=변경 요청 양식 제출 -> 채점자 채점 -> 집계 점수 결과 -> 확인 변경 요청 양식
MemberCycleProcessEnum.4.r=변경 요청 양식 제출 -> 채점자별 채점 -> 집계 점수 결과 -> 검토 변경 요청 양식(레벨 1) -> 확인된 변경 요청 양식
MemberCycleProcessEnum.5.r=변경 요청 양식 제출 -> 평가자 채점 -> 점수 결과 집계 -> 변경 요청 양식 검토(레벨 1) -> 변경 요청 양식 검토(레벨 2) -> 변경 확인 하나 요청

MemberLifecycleStagesRuleEnum.1=소스 찾기 참여 허용
MemberLifecycleStagesRuleEnum.2=계약 체결을 허락하다
MemberLifecycleStagesRuleEnum.3=공급업체의 주문 작성 허용
MemberLifecycleStagesRuleEnum.4=입고 자료 변경 허용
MemberLifecycleStagesRuleEnum.5=상품 문의 게시 허용
MemberLifecycleStagesRuleEnum.6=계약 체결을 허락하다
MemberLifecycleStagesRuleEnum.7=주문 작성 허용
MemberLifecycleStagesRuleEnum.8=변경 요청 자료 허용

MemberCancellationEnum.0=모두
MemberCancellationEnum.1=로그아웃 보류
MemberCancellationEnum.2=로그아웃 승인
MemberCancellationEnum.3=로그아웃 승인 실패

MemberChangeOperateTypeEnum.1=멤버 변경 보류(레벨 1)
MemberChangeOperateTypeEnum.2=멤버 변경 보류 중(레벨 2)
MemberChangeOperateTypeEnum.3=멤버 변경 보류 중

MemberImportChangeOperateTypeEnum.1=변경 예정

MemberImportOperateTypeEnum.1=검토할 수신 데이터
MemberImportOperateTypeEnum.2=저장 자격 검토 예정
MemberImportOperateTypeEnum.3=검사 대상
MemberImportOperateTypeEnum.4=저장할 분류
MemberImportOperateTypeEnum.5=검토 및 보관(레벨 1)
MemberImportOperateTypeEnum.6=검토되어 저장소에 보관됨(레벨 2)
MemberImportOperateTypeEnum.7=저장 확인 예정

MemberKpiOperateTypeEnum.1=출시 예정 평가 양식
MemberKpiOperateTypeEnum.2=평가 및 채점 예정
MemberKpiOperateTypeEnum.3=제출할 평가 결과 요약
MemberKpiOperateTypeEnum.4=보류 중인 평가 결과(레벨 1)
MemberKpiOperateTypeEnum.5=보류 중인 평가 결과(레벨 2)
MemberKpiOperateTypeEnum.6=평가 결과 알림

MemberOperateTypeEnum.1=검토를 위해 제출 예정
MemberOperateTypeEnum.2=레벨 1 검토 보류 중
MemberOperateTypeEnum.3=레벨 2 검토 보류 중
MemberOperateTypeEnum.4=감사 결과 확인 예정

MemberRectifyOperateTypeEnum.1=보류 중인 수정 알림
MemberRectifyOperateTypeEnum.2=수정 결과 확인 예정

VendorRectifyNoticeOperateTypeEnum.1=수정 예정

MemberPointsTypeEnum.0=공제
MemberPointsTypeEnum.1=새로 추가