CountryAreaFilterRuleEnum.0=The superior area should completely contain the inferior area
CountryAreaFilterRuleEnum.1=Superior area and subordinate area only need to have intersection

MemberApplyButtonStatusEnum.0=Apply to become a member of our store
MemberApplyButtonStatusEnum.1=Apply for a member
MemberApplyButtonStatusEnum.2=Became a member
MemberApplyButtonStatusEnum.3=Apply to become an inbound member
MemberApplyButtonStatusEnum.4=Apply to become an inbound member
MemberApplyButtonStatusEnum.5=Apply to become a member of our store
MemberApplyButtonStatusEnum.6=

MemberApplyButtonStatusEnum.0.c=Apply to become a member of our store
MemberApplyButtonStatusEnum.1.c=Apply for warehousing supplier
MemberApplyButtonStatusEnum.2.c=Has become a warehousing supplier
MemberApplyButtonStatusEnum.3.c=Apply to become a warehousing supplier
MemberApplyButtonStatusEnum.4.c=Apply to become a warehousing supplier
MemberApplyButtonStatusEnum.5.c=Apply to become a member of our store
MemberApplyButtonStatusEnum.6.c=

MemberAppraisalItemStatusEnum.0=To be evaluated
MemberAppraisalItemStatusEnum.1=Evaluated and scored

MemberAppraisalItemTypeEnum.0=Custom
MemberAppraisalItemTypeEnum.1=On-time delivery rate
MemberAppraisalItemTypeEnum.2=Supply qualification rate

MemberAppraisalStatusEnum.1=Appraisal form to be published
MemberAppraisalStatusEnum.2=To be evaluated
MemberAppraisalStatusEnum.3=Summary evaluation results to be submitted
MemberAppraisalStatusEnum.4=Pending evaluation result (Level 1)
MemberAppraisalStatusEnum.5=Appraisal result review failed (Level 1)
MemberAppraisalStatusEnum.6=Pending evaluation result (level 2)
MemberAppraisalStatusEnum.7=Appraisal result review failed (level 2)
MemberAppraisalStatusEnum.8=Appraisal result to be notified
MemberAppraisalStatusEnum.9=Assessment result notified

MemberChangeRequestFormStatusEnum.1=Submit change request
MemberChangeRequestFormStatusEnum.2=To be rated
MemberChangeRequestFormStatusEnum.3=To be aggregated scoring results
MemberChangeRequestFormStatusEnum.4=Aggregate scoring results fail
MemberChangeRequestFormStatusEnum.5=Change application form to be reviewed (level 1)
MemberChangeRequestFormStatusEnum.6=Failed to approve changes (Level 1)
MemberChangeRequestFormStatusEnum.7=Change application form to be reviewed (level 2)
MemberChangeRequestFormStatusEnum.8=Review change failed (level 2)
MemberChangeRequestFormStatusEnum.9=Change application form to be confirmed
MemberChangeRequestFormStatusEnum.10=Change application approved
MemberChangeRequestFormStatusEnum.11=Change application failed

MemberComplaintClassifyEnum.1=About Product
MemberComplaintClassifyEnum.2=About the order
MemberComplaintClassifyEnum.3=About Shipping
MemberComplaintClassifyEnum.4=About after sales
MemberComplaintClassifyEnum.5=About Services
MemberComplaintClassifyEnum.6=Other

MemberComplaintComplaintTypeEnum.1=Initiated by superior
MemberComplaintComplaintTypeEnum.2=Subordinate initiated

MemberComplaintStatusEnum.1=Complaint suggestion to be submitted
MemberComplaintStatusEnum.2=Pending complaint suggestion
MemberComplaintStatusEnum.3=Complaint suggestion processed

MemberComplaintTypeEnum.1=Complaint
MemberComplaintTypeEnum.2=Suggestion

MemberConfigStatusEnum.0=Disabled
MemberConfigStatusEnum.1=Enable

MemberDepositoryStatusEnum.0=Add repository data
MemberDepositoryStatusEnum.1=Data to be reviewed
MemberDepositoryStatusEnum.2=Depository qualification to be reviewed
MemberDepositoryStatusEnum.3=To be inspected
MemberDepositoryStatusEnum.4=Pending storage classification
MemberDepositoryStatusEnum.5=to be reviewed for storage (level 1)
MemberDepositoryStatusEnum.6=to be reviewed for storage (level 2)
MemberDepositoryStatusEnum.7=To be confirmed for storage

MemberDetailVersionEnum.0=Added, pending version
MemberDetailVersionEnum.1=Version in use

MemberInnerStatusEnum.0=Newly created or added subordinate member
MemberInnerStatusEnum.1=Incoming data to be reviewed
MemberInnerStatusEnum.2=Inbound data review failed
MemberInnerStatusEnum.3=to be reviewed for storage qualification
MemberInnerStatusEnum.4=The warehousing qualification review failed
MemberInnerStatusEnum.5=To be inspected
MemberInnerStatusEnum.6=The in-stock inspection failed
MemberInnerStatusEnum.7=Pending inventory classification
MemberInnerStatusEnum.8=Inbound classification failed
MemberInnerStatusEnum.9=to be reviewed and put into storage (level 1)
MemberInnerStatusEnum.10=Inbound review failed (Level 1)
MemberInnerStatusEnum.11=To be reviewed and put into storage (level 2)
MemberInnerStatusEnum.12=Inbound review failed (level 2)
MemberInnerStatusEnum.13=To be confirmed for warehousing
MemberInnerStatusEnum.14=Inbound review passed
MemberInnerStatusEnum.15=Inbound review failed
MemberInnerStatusEnum.16=Pending Review Changes (Level 1)
MemberInnerStatusEnum.17=Change review failed (Level 1)
MemberInnerStatusEnum.18=Changes to be reviewed (Level 2)
MemberInnerStatusEnum.19=Change review failed (level 2)
MemberInnerStatusEnum.20=Changes to be confirmed
MemberInnerStatusEnum.21=Change approved
MemberInnerStatusEnum.22=Change review failed
MemberInnerStatusEnum.23=Logged out

MemberInspectSourceEnum.0=Create via Member Capability function
MemberInspectSourceEnum.1=Create through member review related process

MemberInspectTypeEnum.0=All
MemberInspectTypeEnum.1=Inspection
MemberInspectTypeEnum.2=Rectification inspection
MemberInspectTypeEnum.3=Plan inspection
MemberInspectTypeEnum.4=Other inspections

MemberInviteButtonStatusEnum.0=This company is not my member yet, invite me to become my member now
MemberInviteButtonStatusEnum.1=This company is already my member, send orders now
MemberInviteButtonStatusEnum.2=This company is already my member, send orders now
MemberInviteButtonStatusEnum.3=This company is not my member yet, invite me to become my member now
MemberInviteButtonStatusEnum.4=This company is already a blacklisted member!
MemberInviteButtonStatusEnum.5=This company is not my member yet, invite me to become my member now
MemberInviteButtonStatusEnum.6=

MemberManageOrderTypeEnum.5=Purchase Order Placement
MemberManageOrderTypeEnum.6=Manual order
MemberManageOrderTypeEnum.7=Inquiry for quotation to place an order
MemberManageOrderTypeEnum.8=Demand quotation to place an order
MemberManageOrderTypeEnum.9=Merge order to place an order
MemberManageOrderTypeEnum.10=Channel direct purchase order to place an order
MemberManageOrderTypeEnum.11=Manual order for channel direct purchase
MemberManageOrderTypeEnum.12=Channel spot purchase order to place an order
MemberManageOrderTypeEnum.13=Manual order from channel spot
MemberManageOrderTypeEnum.99=Unknown

MemberOuterStatusEnum.1=To be submitted for review
MemberOuterStatusEnum.2=To be reviewed by the platform
MemberOuterStatusEnum.3=The platform has passed the audit
MemberOuterStatusEnum.4=The platform review failed
MemberOuterStatusEnum.5=to be reviewed and put into storage
MemberOuterStatusEnum.6=Inbound review passed
MemberOuterStatusEnum.7=Inbound review failed
MemberOuterStatusEnum.8=Pending changes
MemberOuterStatusEnum.9=Change approved
MemberOuterStatusEnum.10=Change review failed
MemberOuterStatusEnum.11=Logged out
MemberOuterStatusEnum.99=Unknown

MemberPartnerTypeEnum.1=Strategic
MemberPartnerTypeEnum.2=Core type
MemberPartnerTypeEnum.3=Qualified Type
MemberPartnerTypeEnum.4=Potential Type

MemberRectifyAgreeResultEnum.0=Rectification failed
MemberRectifyAgreeResultEnum.1=Rectification passed

MemberRectifyStatusEnum.1=Pending Rectification Notification
MemberRectifyStatusEnum.2=To be rectified
MemberRectifyStatusEnum.3=To be confirmed rectification result
MemberRectifyStatusEnum.4=Confirm rectification result failed
MemberRectifyStatusEnum.5=Confirm rectification result passed

MemberRegisterSourceEnum.1=WEB Enterprise Mall Application
MemberRegisterSourceEnum.2=H5 Enterprise Mall Application
MemberRegisterSourceEnum.3=APP Enterprise Mall Application
MemberRegisterSourceEnum.4=Applet Enterprise Mall Application
MemberRegisterSourceEnum.5=WEB points store application
MemberRegisterSourceEnum.6=H5 points store application
MemberRegisterSourceEnum.7=App points store application
MemberRegisterSourceEnum.8=App Points Mall Application
MemberRegisterSourceEnum.9=Platform agent registration
MemberRegisterSourceEnum.10=Merchant enters registration on behalf of
MemberRegisterSourceEnum.99=Platform backend
MemberRegisterSourceEnum.100=Unknown

MemberRelationSourceEnum.0=Become a lower-level member through web registration
MemberRelationSourceEnum.1=Register as a subordinate member through the mobile terminal
MemberRelationSourceEnum.2=Become a subordinate member by adding a new role
MemberRelationSourceEnum.3=Create via platform backend
MemberRelationSourceEnum.4=Created by Member Capability
MemberRelationSourceEnum.5=Actively apply to become a subordinate member
MemberRelationSourceEnum.99=Unknown

MemberRightScoreBehaviorEnum.0=all
MemberRightScoreBehaviorEnum.1=Get points
MemberRightScoreBehaviorEnum.2=Use points

MemberTradeTypeEnum.1=Purchasing Member
MemberTradeTypeEnum.2=Supply Member

MemberValidateAgreeEnum.1=Agree
MemberValidateAgreeEnum.0=Disagree

MemberValidateHistoryOperationEnum.1=Apply for registration
MemberValidateHistoryOperationEnum.2=Change data
MemberValidateHistoryOperationEnum.3=Submit for platform review
MemberValidateHistoryOperationEnum.4=Verify members
MemberValidateHistoryOperationEnum.5=Entry
MemberValidateHistoryOperationEnum.6=Apply for member storage
MemberValidateHistoryOperationEnum.7=Apply for membership change
MemberValidateHistoryOperationEnum.8=Freeze Member
MemberValidateHistoryOperationEnum.9=Unfreeze Member
MemberValidateHistoryOperationEnum.10=Release relationship
MemberValidateHistoryOperationEnum.11=Blacklist member
MemberValidateHistoryOperationEnum.12=Modify registration information
MemberValidateHistoryOperationEnum.13=Summary evaluation results to be submitted
MemberValidateHistoryOperationEnum.14=Publish Assessment Form
MemberValidateHistoryOperationEnum.15=Send rectification notice
MemberValidateHistoryOperationEnum.16=The membership change review failed
MemberValidateHistoryOperationEnum.17=The membership change review failed (level 2)
MemberValidateHistoryOperationEnum.18=The membership change review failed (level 1)
MemberValidateHistoryOperationEnum.19=Member change approval passed
MemberValidateHistoryOperationEnum.20=Confirm rectification result
MemberValidateHistoryOperationEnum.21=Inbound classification
MemberValidateHistoryOperationEnum.22=Inbound classification failed
MemberValidateHistoryOperationEnum.23=Inspection
MemberValidateHistoryOperationEnum.24=Inbound inspection failed
MemberValidateHistoryOperationEnum.25=Inbound audit failed (level 2)
MemberValidateHistoryOperationEnum.26=The warehousing audit failed (level 1)
MemberValidateHistoryOperationEnum.27=Inbound data review failed
MemberValidateHistoryOperationEnum.28=Approval failed
MemberValidateHistoryOperationEnum.29=Check Membership (Level 2)
MemberValidateHistoryOperationEnum.30=Check Membership (Level 1)
MemberValidateHistoryOperationEnum.31=Verify membership changes
MemberValidateHistoryOperationEnum.32=Check membership changes (level 2)
MemberValidateHistoryOperationEnum.33=Check Membership Change (Level 1)
MemberValidateHistoryOperationEnum.34=Approval of membership failed
MemberValidateHistoryOperationEnum.35=Failed to approve membership (level 2)
MemberValidateHistoryOperationEnum.36=Failed to approve membership (level 1)
MemberValidateHistoryOperationEnum.37=Approved membership
MemberValidateHistoryOperationEnum.38=Check evaluation results
MemberValidateHistoryOperationEnum.39=Check Inventory
MemberValidateHistoryOperationEnum.40=Check Inventory (Level 2)
MemberValidateHistoryOperationEnum.41=Check Inventory (Level 1)
MemberValidateHistoryOperationEnum.42=Review storage data
MemberValidateHistoryOperationEnum.43=Check warehousing qualification
MemberValidateHistoryOperationEnum.44=Approved
MemberValidateHistoryOperationEnum.45=Audit data storage
MemberValidateHistoryOperationEnum.46=Submit summary assessment results
MemberValidateHistoryOperationEnum.47=Submission review failed
MemberValidateHistoryOperationEnum.48=Submit Member for Review
MemberValidateHistoryOperationEnum.49=Submit rectification
MemberValidateHistoryOperationEnum.50=Notify evaluation result
MemberValidateHistoryOperationEnum.51=Inbound audit failed
MemberValidateHistoryOperationEnum.52=Inventory qualification review failed
MemberValidateHistoryOperationEnum.53=Apply for membership
MemberValidateHistoryOperationEnum.54=Submit Change Request
MemberValidateHistoryOperationEnum.55=Rater Rating
MemberValidateHistoryOperationEnum.56=Summarize Scoring Results
MemberValidateHistoryOperationEnum.57=Change Request Validate Pass Level One
MemberValidateHistoryOperationEnum.58=Change Request Validate Not Pass Level One
MemberValidateHistoryOperationEnum.59=Change Request Validate Pass Level Two
MemberValidateHistoryOperationEnum.60=Change Request Validate Not Pass Level Two
MemberValidateHistoryOperationEnum.61=Change Request Passed
MemberValidateHistoryOperationEnum.62=Change Request Not Passed
MemberValidateHistoryOperationEnum.63=Apply for cancellation
MemberValidateHistoryOperationEnum.64=Cancellation review failed
MemberValidateHistoryOperationEnum.65=Cancellation review passed
MemberValidateHistoryOperationEnum.99=Unknown

MemberValidateStatusEnum.0=The platform or member review failed
MemberValidateStatusEnum.1=The platform or member has passed the review

MobileApplyButtonStatusEnum.0=Confirm authorization and become a member of the store
MobileApplyButtonStatusEnum.1=Confirm authorization and become a member of our store (under review)
MobileApplyButtonStatusEnum.2=
MobileApplyButtonStatusEnum.3=Confirm authorization and become a member of our store (review failed)
MobileApplyButtonStatusEnum.4=
MobileApplyButtonStatusEnum.5=Confirm authorization and become a member of the store
MobileApplyButtonStatusEnum.6=
MobileApplyButtonStatusEnum.7=
MobileApplyButtonStatusEnum.8=You cannot become your own affiliate

PlatformInnerStatusEnum.0=Apply for registration
PlatformInnerStatusEnum.1=To be submitted for platform review
PlatformInnerStatusEnum.2=Submission review failed
PlatformInnerStatusEnum.3=To be reviewed by the platform (Level 1)
PlatformInnerStatusEnum.4=Approval Member Failed (Level 1)
PlatformInnerStatusEnum.5=To be reviewed by the platform (level 2)
PlatformInnerStatusEnum.6=Audit membership failed (level 2)
PlatformInnerStatusEnum.7=Pending Membership
PlatformInnerStatusEnum.8=Platform audit failed
PlatformInnerStatusEnum.9=Platform audit passed
PlatformInnerStatusEnum.10=Logged out

MemberConfigTagEnum.1=Member Name
MemberConfigTagEnum.2=Member Mobile Number
MemberConfigTagEnum.3=Member ID number
MemberConfigTagEnum.4=Member ID card front
MemberConfigTagEnum.5=Reverse side of member ID card
MemberConfigTagEnum.6=Legal name
MemberConfigTagEnum.7=Legal phone number
MemberConfigTagEnum.8=Legal person ID number
MemberConfigTagEnum.9=Front of legal person ID card
MemberConfigTagEnum.10=Reverse side of legal person ID card
MemberConfigTagEnum.11=Uniform Credit Code
MemberConfigTagEnum.12=Registered capital
MemberConfigTagEnum.13=Foundation Date
MemberConfigTagEnum.14=Business License
MemberConfigTagEnum.15=Registered address (province, city, district)
MemberConfigTagEnum.16=Registration address (detailed address)

MemberRightTypeEnum.1=Price Equity
MemberRightTypeEnum.2=Cashback benefits
MemberRightTypeEnum.3=Points benefit

MemberRightTypeEnum.1.r=One side of the transaction can get the other side's price discount
MemberRightTypeEnum.2.r=One party to the transaction can get the other party's transaction cashback
MemberRightTypeEnum.3.r=One side of the transaction can get the other side's points

MemberCreditTypeEnum.1=Transaction Rating
MemberCreditTypeEnum.2=Complaint
MemberCreditTypeEnum.3=After-sale evaluation
MemberCreditTypeEnum.4=Years of membership

MemberCreditTypeEnum.1.r=Increase credit points when the evaluation is 5 points after the transaction is completed
MemberCreditTypeEnum.2.r=Complaint confirmed to be substantiated to deduct credit points
MemberCreditTypeEnum.3.r=Increase credit points when the evaluation is 5 points after the sale is completed
MemberCreditTypeEnum.4.r=Increase credit points every year after applying for membership

MemberRightAcquireWayEnum.1=Trade Acquisition

MemberRightParamWayEnum.1=Set according to the proportion of transaction amount

MemberLevelRuleTypeEnum.1=Transaction
MemberLevelRuleTypeEnum.2=Login
MemberLevelRuleTypeEnum.3=Evaluation

MemberLevelRuleTypeEnum.1.r=After completing the transaction order, the percentage of the transaction order amount will be credited to the obtained upgrade points
MemberLevelRuleTypeEnum.2.r=After each successful login, the obtained upgrade score will be recorded according to the set score
MemberLevelRuleTypeEnum.3.r=After each successful evaluation, it will be credited to the obtained upgrade score according to the set score

MemberRightSpendTypeEnum.1=Point Order
MemberRightSpendTypeEnum.2=Points to redeem coupons
MemberRightSpendTypeEnum.3=Points deduct order amount
MemberRightSpendTypeEnum.4=Platform Deduction Points

MerchantMemberStringEnum.1=order number:

CheckStatusEnum.1=Agree
CheckStatusEnum.2=Refuse

VisitTypeEnum.1=Annual Visit
VisitTypeEnum.2=Quarterly Visit
VisitTypeEnum.3=Monthly Visit
VisitTypeEnum.4=Order Visit
VisitTypeEnum.5=Introducing Visits
VisitTypeEnum.6=Responsible person change visit
VisitTypeEnum.7=Other Visits

SettlementDocumentsEnum.1=Order
SettlementDocumentsEnum.2=Logistics Document
SettlementDocumentsEnum.3=Production Notice
SettlementDocumentsEnum.4=Shipment Note
SettlementDocumentsEnum.5=Receipt
SettlementDocumentsEnum.6=Invoice
SettlementDocumentsEnum.7=Receipt+Invoice

CurrencyTypeEnum.1=CNY - Chinese Yuan
CurrencyTypeEnum.2=USD - USD
CurrencyTypeEnum.3=JPY - Japanese yen
CurrencyTypeEnum.4=EUR - Euro

AdvanceChargeEnum.1=No prepayment required
AdvanceChargeEnum.2=Advance payment required

VisitLevelEnum.1=General
VisitLevelEnum.2=Important

MemberChangeProjectNameEnum.0=Qualification Certificate Photo

ScoringTemplateTypeEnum.1=Supplier Lifecycle Change Request
ScoringTemplateTypeEnum.2=Supplier Scoring
ScoringTemplateTypeEnum.3=Customer Lifecycle Change Request
ScoringTemplateTypeEnum.4=Customer Scoring

ScoringTemplateStateEnum.0=Disable
ScoringTemplateStateEnum.1=Enable

MemberInviteTypeEnum.1=Register
MemberInviteTypeEnum.2=discover
MemberInviteTypeEnum.3=Invite

InvitationCodeStateEnum.1=Not Sent
InvitationCodeStateEnum.2=Not registered
InvitationCodeStateEnum.3=Registered
InvitationCodeStateEnum.4=Invalidated

MemberCycleProcessEnum.0=No review process for supplier change request
MemberCycleProcessEnum.1=Supplier Change Request Process Level 1
MemberCycleProcessEnum.2=The whole process of supplier change application
MemberCycleProcessEnum.0.r=Submit change request form -> to be graded by grader -> to be aggregated score results -> to be confirmed change request form
MemberCycleProcessEnum.1.r=Submit change request form -> to be graded by grader -> to be aggregated score results -> to be reviewed change request form (level 1) -> to be confirmed change request form
MemberCycleProcessEnum.2.r=Submit Change Request Form -> To Be Scored by Scorers -> To Aggregate Score Results -> To Review Change Request Form (Level 1) -> To Review Change Request Form (Level 2) -> To Confirm Change Request one
MemberCycleProcessEnum.3=There is no review process for the customer's change request
MemberCycleProcessEnum.4=Customer Change Request Process Level 1
MemberCycleProcessEnum.5=The whole process of customer change application
MemberCycleProcessEnum.3.r=Submit change request form -> to be graded by graders -> to be aggregated score results -> to be confirmed change request form
MemberCycleProcessEnum.4.r=Submit change request form -> to be graded by grader -> to be aggregated score results -> to be reviewed change request form (level 1) -> to be confirmed change request form
MemberCycleProcessEnum.5.r=Submit Change Request Form -> To Be Scored by Raters -> To Aggregate Score Results -> To Review Change Request Form (Level 1) -> To Review Change Request Form (Level 2) -> To Confirm Change Request one

MemberLifecycleStagesRuleEnum.1=Allow participation in source hunting
MemberLifecycleStagesRuleEnum.2=Allow contract to be signed
MemberLifecycleStagesRuleEnum.3=Allows the creation of an order for this vendor
MemberLifecycleStagesRuleEnum.4=Allows changes to incoming data
MemberLifecycleStagesRuleEnum.5=Allow to publish commodity inquiries
MemberLifecycleStagesRuleEnum.6=Allow contract to be signed
MemberLifecycleStagesRuleEnum.7=Allow order creation
MemberLifecycleStagesRuleEnum.8=Change of application information is allowed

MemberCancellationEnum.0=ALL
MemberCancellationEnum.1=WaitCancellation
MemberCancellationEnum.2=CancellationSuccess
MemberCancellationEnum.3=CancellationFail

MemberChangeOperateTypeEnum.1=Pending Member Change (Level 1)
MemberChangeOperateTypeEnum.2=Pending Member Change (Level 2)
MemberChangeOperateTypeEnum.3=Pending Member Change

MemberImportChangeOperateTypeEnum.1=To be changed

MemberImportOperateTypeEnum.1=Incoming data to be reviewed
MemberImportOperateTypeEnum.2=to be reviewed for storage qualification
MemberImportOperateTypeEnum.3=To be inspected
MemberImportOperateTypeEnum.4=Classification to be stored
MemberImportOperateTypeEnum.5=To be reviewed and put into storage (level 1)
MemberImportOperateTypeEnum.6=to be reviewed and put into storage (level 2)
MemberImportOperateTypeEnum.7=To be confirmed for storage

MemberKpiOperateTypeEnum.1=To-be-published assessment form
MemberKpiOperateTypeEnum.2=To be evaluated and scored
MemberKpiOperateTypeEnum.3=Summary evaluation results to be submitted
MemberKpiOperateTypeEnum.4=Pending evaluation result (level 1)
MemberKpiOperateTypeEnum.5=Pending evaluation result (level 2)
MemberKpiOperateTypeEnum.6=To be notified of evaluation results

MemberOperateTypeEnum.1=To be submitted for review
MemberOperateTypeEnum.2=Pending Level 1 Review
MemberOperateTypeEnum.3=Pending Level 2 Review
MemberOperateTypeEnum.4=To be confirmed audit result

MemberRectifyOperateTypeEnum.1=Pending Rectification Notification
MemberRectifyOperateTypeEnum.2=To be confirmed rectification result

VendorRectifyNoticeOperateTypeEnum.1=To be rectified

MemberPointsTypeEnum.0=Deduction
MemberPointsTypeEnum.1=New