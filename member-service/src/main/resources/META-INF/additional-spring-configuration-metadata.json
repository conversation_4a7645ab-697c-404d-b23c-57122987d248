{"properties": [{"name": "lingxi.member.login.token-expire-minutes", "type": "java.lang.String", "description": "用户登录后，Token等信息在缓存中保存的时长（分钟）."}, {"name": "lingxi.member.login.auth-url", "type": "java.lang.String", "description": "二维码授权码认证链接"}, {"name": "lingxi.member.security.smscode-expire-minutes", "type": "java.lang.String", "description": "用户更改密码时，短信验证码的缓存时间（分钟）."}, {"name": "lingxi.member.security.mailcode-expire-minutes", "type": "java.lang.String", "description": "用户更改密码时，邮件验证码的缓存时间（分钟）."}, {"name": "lingxi.member.file.path", "type": "java.lang.String", "description": "临时文件路径."}, {"name": "lingxi.member.mailserver.smtphost", "type": "java.lang.String", "description": "Smtp邮件服务器域名."}, {"name": "lingxi.member.mailserver.smtpport", "type": "java.lang.String", "description": "Smtp邮件服务器端口."}, {"name": "lingxi.member.mailserver.account", "type": "java.lang.String", "description": "发送邮件的账号."}, {"name": "lingxi.member.mailserver.username", "type": "java.lang.String", "description": "发送邮件的用户名."}, {"name": "lingxi.member.mailserver.password", "type": "java.lang.String", "description": "发送邮件账号的密码."}]}