plugins {
    id 'org.springframework.boot' version '2.4.4'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'java'
    id 'org.sonarqube' version '3.2.0'
}

// 项目坐标
group = 'com.ssy.lingxi.marketing'
version = '3.0.0'
sourceCompatibility = '1.8'

// 仓库
repositories {
    mavenLocal()
    maven {
        url 'http://10.0.0.21:8081/repository/maven-public/'
    }
    maven {
        url 'https://maven.aliyun.com/repository/public'
    }
    mavenCentral()
}

// 引入依赖
dependencies {
    //引用公共组件
    implementation project(':component:base-spring-boot-starter')
    annotationProcessor project(':component:base-spring-boot-starter')

    //引用其他组件
    implementation project(':component:aop-log-spring-boot-starter')
    implementation project(':component:redis-spring-boot-starter')
    implementation project(':component:rabbitMQ-spring-boot-starter')
    implementation project(':component:skywalking-spring-boot-starter')
    implementation project(':component:rest-client-spring-boot-starter')

    implementation project(':marketing-service:marketing-service-api')
    implementation project(':workflow-service:workflow-service-api')
    implementation project(':manage-service:manage-service-api')
    implementation project(':member-service:member-service-api')
    implementation project(':product-service:product-service-api')
    implementation project(':scheduler-service:scheduler-service-api')
    implementation project(':order-service:order-service-api')
    implementation project(':aftersales-service:aftersales-service-api')
    implementation project(':report-service:report-service-api')
    implementation project(':commodity-service:commodity-service-api')
    implementation project(':support-service:support-service-api')

    //spring cloud
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.cloud:spring-cloud-loadbalancer'

    //springboot
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }

    //nacos
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap'
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery'
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config'

    //sentinel
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel'

    //seata
    implementation('com.alibaba.cloud:spring-cloud-starter-alibaba-seata') {
        exclude group: 'io.seata', module: 'seata-spring-boot-starter'
    }
    implementation('io.seata:seata-spring-boot-starter:1.6.0'){
        exclude group: 'com.alibaba', module: 'druid'
    }

    //arthas
    implementation 'com.taobao.arthas:arthas-spring-boot-starter:3.6.7'

    //prometheus
    implementation 'io.micrometer:micrometer-registry-prometheus'

    // postgresql驱动
    runtimeOnly 'org.postgresql:postgresql'

    //QueryDSL
    implementation("com.querydsl:querydsl-core:4.4.0")
    implementation("com.querydsl:querydsl-jpa:4.4.0")
    annotationProcessor("com.querydsl:querydsl-apt:4.4.0:jpa"
            ,"org.hibernate.javax.persistence:hibernate-jpa-2.1-api:1.0.2.Final"
            ,"javax.annotation:javax.annotation-api:1.3.2")

    // 用于编译期间生成DO to DTO等转换工具类
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    annotationProcessor "org.projectlombok:lombok-mapstruct-binding:0.2.0"
}

// 自定义属性, 可以在gradle文件中使用
ext {
    set('springCloudVersion', "2020.0.2")
    set('springCloudAlibabaVersion', "2021.1")
}

// 依赖jar包进行版本管理的管理器
dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
        mavenBom "com.alibaba.cloud:spring-cloud-alibaba-dependencies:${springCloudAlibabaVersion}"
    }
}

sonarqube {
    properties {
        property "sonar.host.url", project.properties['sonarHostUrl']
        property "sonar.login", project.properties['sonarUserName']
        property "sonar.password", project.properties['sonarUserPassword']
        property "sonar.projectKey", project.properties['projectName'] + "." + project.name
        property "sonar.projectName", project.properties['projectName'] + "." + project.name
        property "sonar.projectVersion", version
        property "sonar.sources", "src/main/java"
        property "sonar.sourceEncoding", "UTF-8"
    }
}