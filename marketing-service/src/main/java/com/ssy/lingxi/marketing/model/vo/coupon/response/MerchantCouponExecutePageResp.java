package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 商家优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantCouponExecutePageResp extends MerchantCouponPageResp implements Serializable {

    private static final long serialVersionUID = 4682885804362757129L;

    /**
     * 已领取数量
     */
    private Integer obtainQuantity;

    /**
     * 已使用数量
     */
    private Integer useQuantity;

    /**
     * 已过期数量
     */
    private Integer dueQuantity;

    /**
     * 发券按钮
     */
    private boolean release;
}
