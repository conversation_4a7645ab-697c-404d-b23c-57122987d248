package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 活动优惠选项响应类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024/12/10
 */
@Getter
@Setter
public class ActivityDiscountOptionResp implements Serializable {

    private static final long serialVersionUID = -1284106139690376940L;

    /**
     * 优惠类型：MARKETING_ACTIVITY(营销活动优惠), WORK_FEE_DISCOUNT(工费优惠)
     */
    private String discountType;

    /**
     * 优惠类型描述
     */
    private String discountTypeDescription;

    /**
     * 活动ID（营销活动优惠时有值）
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型编码
     */
    private Integer activityTypeCode;

    /**
     * 活动类型名称
     */
    private String activityTypeName;

    /**
     * 原始总金额
     */
    private BigDecimal originalTotalAmount;

    /**
     * 优惠后总金额
     */
    private BigDecimal discountedTotalAmount;

    /**
     * 总优惠金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 优惠率（0-1之间）
     */
    private BigDecimal discountRate;

    /**
     * 优惠规则描述
     */
    private String ruleDescription;


    /**
     * 商品维度优惠详情（包含分摊到每个SKU的优惠）
     */
    private List<GoodsDiscountDetailResp> goodsDiscountDetails;

}