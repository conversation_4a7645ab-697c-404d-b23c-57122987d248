package com.ssy.lingxi.marketing.controller.coupon;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ActivityPageCouponDetailReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ActivityPageMerchantCouponDataReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityPageCouponDetailResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityPageCouponPageResp;
import com.ssy.lingxi.marketing.service.IActivityPageCouponService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 *
 * 商家活动页 - 优惠券
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/28
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/coupon/activityPage")
public class MerchantCouponActivityPageController extends BaseController {

    @Resource
    private IActivityPageCouponService activityPageCouponService;

    /**
     * 商家营销活动页 - 选择商家优惠券
     * @return 返回结果
     */
    @GetMapping("/select/page")
    public WrapperResp<PageDataResp<ActivityPageCouponPageResp>> pageMerchantActivityPageMerchantCoupon(@Valid ActivityPageMerchantCouponDataReq request) {
        return WrapperUtil.success(activityPageCouponService.pageMerchantActivityPageMerchantCoupon(request, getSysUser()));
    }

    /**
     * 商家活动页 - 优惠券详情
     * @return 返回结果
     */
    @PostMapping("/select/detail")
    public WrapperResp<List<ActivityPageCouponDetailResp>> listMerchantActivityPageCouponDetail(@RequestBody @Valid ActivityPageCouponDetailReq request) {
        return WrapperUtil.success(activityPageCouponService.listMerchantActivityPageCouponDetail(request, getSysUser()));
    }
}
