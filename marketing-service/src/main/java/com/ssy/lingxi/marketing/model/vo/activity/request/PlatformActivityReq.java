package com.ssy.lingxi.marketing.model.vo.activity.request;

import java.io.Serializable;

/**
 * 平台活动通知报表服务VO
 * <AUTHOR>
 * @version 2.06.18
 * @since 2022-05-27
 */
public class PlatformActivityReq implements Serializable {

    private static final long serialVersionUID = -7758961214479528408L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 适用的会员ID
     * */
    private Long memberId;

    /**
     * 适用的角色ID
     * */
    private Long roleId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    @Override
    public String toString() {
        return "platformActivityVO{" +
                "id=" + id +
                ", activityName='" + activityName + '\'' +
                ", memberId=" + memberId +
                ", roleId=" + roleId +
                '}';
    }
}
