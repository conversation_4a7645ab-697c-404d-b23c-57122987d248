package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.model.vo.common.request.CommonAgreeReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.*;
import com.ssy.lingxi.marketing.model.vo.coupon.response.*;

/**
 * 平台优惠券服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
public interface IPlatformCouponService {

    /**
     * 平台优惠券查询 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<PlatformCouponSummaryPageResp> pageSummaryPlatformCoupon(PlatformCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核平台优惠券 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<PlatformCouponSubmitAuditPageResp> pageSubmitAuditPlatformCoupon(PlatformCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核平台优惠券(一级) - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<PlatformCouponAuditPageResp> pageAuditOnePlatformCoupon(PlatformCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核平台优惠券(二级) - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<PlatformCouponAuditPageResp> pageAuditTwoPlatformCoupon(PlatformCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台优惠券执行 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<PlatformCouponSubmitPageResp> pageSubmitPlatformCoupon(PlatformCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交平台优惠券 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<PlatformCouponExecutePageResp> pageExecutePlatformCoupon(PlatformCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台优惠券详情
     * @param request 接口参数
     * @param loginUser 登录用户
     * @return 返回结果
     */
    PlatformCouponResp getPlatformCoupon(CommonIdReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台优惠券发券详情
     * @param request 接口参数
     * @param loginUser 登录用户
     * @return 返回结果
     */
    PlatformCouponGrantResp getGrantPlatformCoupon(CommonIdReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核平台优惠券 - 新增
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void addPlatformCoupon(PlatformCouponAddReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核平台优惠券 - 修改
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void updatePlatformCoupon(PlatformCouponUpdateReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核平台优惠券 - 删除
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void deletePlatformCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核平台优惠券 - 提交审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void submitAuditPlatformCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核平台优惠券(一级) - 提交审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void auditOnePlatformCoupon(CommonAgreeReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核平台优惠券(一级) - 批量提交审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void batchAuditOnePlatformCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核平台优惠券(二级) - 审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void auditTwoPlatformCoupon(CommonAgreeReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核平台优惠券(二级) - 批量审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void batchAuditTwoPlatformCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交平台优惠券 - 批量提交
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void submitPlatformCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台优惠券执行 - 发券
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void grantPlatformCoupon(PlatformCouponGrantReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台优惠券查询 - 修改
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void modificationPlatformCoupon(PlatformCouponModificationReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台优惠券查询 - 终止
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void stopPlatformCoupon(PlatformCouponOperationReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台优惠券查询 - 重启
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void restartPlatformCoupon(PlatformCouponOperationReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台优惠券查询 - 取消
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    Void cancelPlatformCoupon(PlatformCouponOperationReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台优惠劵执行明细 - 查询条件
     * @param request 接口参数
     * @return 返回结果
     */
    PlatformCouponDetailConditionResp getExecutePlatformCouponDetailCondition(CommonIdReq request);

    /**
     * 平台优惠劵执行明细 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<PlatformCouponDetailPageResp> pageExecutePlatformCouponDetail(PlatformCouponDetailPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 营销活动 - 附属商品 - 选择优惠券
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<ActivityGoodsCouponResp> selectSubsidiaryCouponList(ActivityPlatformCouponDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 营销活动 - 奖品 - 选择0元抵扣券
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<ActivityPrizeCouponResp> selectPrizeCouponList(ActivityPlatformCouponDataReq request, UserLoginCacheDTO loginUser);
}
