package com.ssy.lingxi.marketing.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.CommonStatusReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.entity.activity.ActivityPageDO;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.SaveActivityPageReq;
import com.ssy.lingxi.marketing.service.IActivityPageWebService;
import com.ssy.lingxi.scheduler.api.model.req.ScheduleTaskCallbackReq;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * web - 活动页装修
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/08/06
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/web/activityPage")
public class ActivityPageController extends BaseController {

    @Resource
    private IActivityPageWebService activityPageWebService;

    /**
     * 详情
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @GetMapping(value = "/getDetail")
    public WrapperResp<ActivityPageDO> getDetail(@Valid CommonIdReq dto) {
        return WrapperUtil.success(activityPageWebService.getDetail(dto));
    }

    /**
     * 列表
     *
     * @param qo 请求参数
     * @return 操作结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<ActivityPageDO>> page(@Valid ActivityPageDataReq qo) {
        return WrapperUtil.success(activityPageWebService.page(qo, getSysUser()));
    }

    /**
     * 详情
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @GetMapping(value = "/get")
    public WrapperResp<ActivityPageDO> get(@Valid CommonIdReq dto) {
        return WrapperUtil.success(activityPageWebService.get(dto));
    }

    /**
     * 新增
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/add")
    public WrapperResp<Long> add(@RequestBody @Validated({SaveActivityPageReq.Add.class}) SaveActivityPageReq dto) {
        return WrapperUtil.success(activityPageWebService.add(dto, getSysUser()));
    }

    /**
     * 修改
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/update")
    public WrapperResp<Void> update(@RequestBody @Validated({SaveActivityPageReq.Update.class}) SaveActivityPageReq dto) {
        activityPageWebService.update(dto, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 删除
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/delete")
    public WrapperResp<Void> delete(@RequestBody @Valid CommonIdReq dto) {
        activityPageWebService.delete(dto, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 上下线
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/openOffLine")
    public WrapperResp<Void> openOffLine(@RequestBody @Valid CommonStatusReq dto) {
        activityPageWebService.openOffLine(dto, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 装修
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/adorn")
    public WrapperResp<Void> adorn(@RequestBody @Validated({SaveActivityPageReq.Adorn.class}) SaveActivityPageReq dto) {
        activityPageWebService.adorn(dto, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 定时服务回调 - 开始活动页
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/start")
    public WrapperResp<Void> start(@RequestBody ScheduleTaskCallbackReq dto) {
        activityPageWebService.start(dto);
        return WrapperUtil.success();
    }

    /**
     * 定时服务回调 - 结束活动页
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/end")
    public WrapperResp<Void> end(@RequestBody ScheduleTaskCallbackReq dto) {
        activityPageWebService.end(dto);
        return WrapperUtil.success();
    }

    /**
     * 列表（装修）
     *
     * @param qo 请求参数
     * @return 操作结果
     */
    @GetMapping("/listAdorn")
    public WrapperResp<PageDataResp<ActivityPageDO>> listAdorn(@Valid ActivityPageDataReq qo) {
        return WrapperUtil.success(activityPageWebService.listAdorn(qo, getSysUser()));
    }
}
