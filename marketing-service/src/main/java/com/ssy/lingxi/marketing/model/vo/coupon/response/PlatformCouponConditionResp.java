package com.ssy.lingxi.marketing.model.vo.coupon.response;

import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 平台优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@Data
public class PlatformCouponConditionResp implements Serializable {
    private static final long serialVersionUID = 220503912244539221L;

    /**
     * 平台优惠券类型
     */
    private List<SelectItemResp> typeList;

    /**
     * 领取方式
     */
    private List<SelectItemResp> getWayList;

    /**
     * 内部状态
     */
    private List<SelectItemResp> statusList;

}
