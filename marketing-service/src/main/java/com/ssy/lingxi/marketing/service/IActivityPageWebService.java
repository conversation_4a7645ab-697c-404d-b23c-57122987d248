package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.CommonStatusReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.api.model.request.ActivityTypeReq;
import com.ssy.lingxi.marketing.entity.activity.ActivityPageDO;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.SaveActivityPageReq;
import com.ssy.lingxi.scheduler.api.model.req.ScheduleTaskCallbackReq;

/**
 * web - 活动页 - 业务处理层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/08/06
 */
public interface IActivityPageWebService {

    PageDataResp<ActivityPageDO> page(ActivityPageDataReq qo, UserLoginCacheDTO user);

    ActivityPageDO get(CommonIdReq dto);

    Long add(SaveActivityPageReq dto, UserLoginCacheDTO user);

    void update(SaveActivityPageReq dto, UserLoginCacheDTO user);

    void delete(CommonIdReq dto, UserLoginCacheDTO user);

    void openOffLine(CommonStatusReq dto, UserLoginCacheDTO user);

    void adorn(SaveActivityPageReq dto, UserLoginCacheDTO user);

    void start(ScheduleTaskCallbackReq dto);

    void end(ScheduleTaskCallbackReq dto);

    PageDataResp<ActivityPageDO> listAdorn(ActivityPageDataReq qo, UserLoginCacheDTO user);

    ActivityPageDO getDetail(CommonIdReq dto);

    /**
     * 根据活动类型和活动id移除装修页装修的活动
     *
     * @param activityTypeReq 请求参数
     */
    void updateActivityPage(ActivityTypeReq activityTypeReq);
}
