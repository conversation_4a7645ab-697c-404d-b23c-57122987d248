package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 平台活动 - 审核报名 - DTO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Data
public class PlatformActivityExamineSignUpReq implements Serializable {

    private static final long serialVersionUID = 523011323950066783L;
    /**
     * 活动报名id
     * */
    @NotNull(message = "活动报名id不能为空")
        private Long signUpId;

    /**
     * 是否通过：0-否，1-是
     */
    @NotNull(message = "是否通过不能为空")
    private Integer isPass;

    /**
     * 审核意见
     */
    private String opinion;
}
