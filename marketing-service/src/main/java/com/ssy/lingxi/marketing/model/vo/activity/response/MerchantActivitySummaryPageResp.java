package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商家活动列表返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantActivitySummaryPageResp extends MerchantActivityPageResp {

    private static final long serialVersionUID = -2060897018981768373L;

    /**
     * 修改按钮
     */
    private boolean update;

    /**
     * 终止按钮
     */
    private boolean stop;

    /**
     * 重启按钮
     */
    private boolean restart;
}
