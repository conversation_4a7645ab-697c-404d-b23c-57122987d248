package com.ssy.lingxi.marketing.entity.activity;


import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 活动适用商城实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/17
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_shop",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_shop_activity_id_idx", columnList = "activityId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_shop_belong_type_idx", columnList = "belongType")})
public class ActivityShopDO implements Serializable {

    private static final long serialVersionUID = 5955678270989297851L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 活动id
     */
    @Column
    private Long activityId;

    /**
     * 所属类型 1-平台 2-商家
     */
    @Column
    private Integer belongType;

    /**
     * 商城id.
     */
    @Column(columnDefinition = "int8")
    private Long shopId;
    /**
     * 商城名称
     */
    @Column(columnDefinition = "varchar(20)")
    private String shopName;

    /**
     * logo图片路径
     */
    @Column(columnDefinition = "varchar(250)")
    private String logo;
    /**
     * 商城环境:1.web 2.H5 3.小程序 4.IOS 5.安卓
     */
    @Column
    private Integer environment;
}
