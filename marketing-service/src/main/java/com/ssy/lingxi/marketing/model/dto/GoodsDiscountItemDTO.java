package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品优惠计算项DTO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024/12/10
 */
@Getter
@Setter
public class GoodsDiscountItemDTO implements Serializable {

    private static final long serialVersionUID = -1284106139690376926L;

    /**
     * 商品skuId
     */
    @NotNull(message = "商品skuId不能为空")
    private Long skuId;

    /**
     * 商品数量
     */
    @NotNull(message = "商品数量不能为空")
    @Min(value = 1, message = "商品数量必须大于0")
    private Integer quantity;


}