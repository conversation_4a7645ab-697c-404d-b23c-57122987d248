package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.marketing.api.model.request.ActivityGoodsQuantityCheckReq;
import com.ssy.lingxi.marketing.api.model.request.CartActivityPriceReq;
import com.ssy.lingxi.marketing.api.model.response.CartActivityPriceFeignResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.CartActivityPriceResp;

import java.math.BigDecimal;
import java.util.List;

/**
 * 活动商品计算服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/2
 */
public interface IActivityPriceCalculateService {
    /**
     * 计算购物车-商品活动-到手价
     * @param req 接口参数
     * @return 计算结果
     */
    List<CartActivityPriceResp> calculateActivityPrice(UserLoginCacheDTO loginUser, List<CartActivityPriceReq> req);

    /**
     * 计算购物车-商品活动-到手价(内部调用)
     * @param req 接口参数
     * @return 计算结果
     */
    List<CartActivityPriceFeignResp> calculateActivityPriceByFeign(UserLoginCacheDTO loginUser, List<CartActivityPriceReq> req);

    /**
     * 检测添加到购物车的商品的数量
     * @param sysUser 登录用户
     * @param request 接口参数
     * @return 计算结果
     */
    BigDecimal checkQuantityByAddCart(UserLoginCacheDTO sysUser, ActivityGoodsQuantityCheckReq request);
}
