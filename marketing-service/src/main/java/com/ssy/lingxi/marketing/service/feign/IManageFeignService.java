package com.ssy.lingxi.marketing.service.feign;

import com.ssy.lingxi.commodity.api.model.resp.shop.ShopDetailResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;

import java.util.List;
import java.util.Map;

/**
 * 平台管理内部接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/7/19
 */
public interface IManageFeignService {

    /**
     * 根据商城ID集合获取商城
     * @param idList id集合
     * @return 返回结果
     */
    WrapperResp<List<ShopDetailResp>> shopByIdList(List<Long> idList);

    /**
     * 查询所有商城信息
     * @return 返回结果
     */
    WrapperResp<List<ShopDetailResp>> shopAll();


    /**
     * 根据商城ID集合获取商城
     * @param idList id集合
     * @return 返回结果
     */
    Map<Long, ShopDetailResp> shopMapByIdList(List<Long> idList);
}
