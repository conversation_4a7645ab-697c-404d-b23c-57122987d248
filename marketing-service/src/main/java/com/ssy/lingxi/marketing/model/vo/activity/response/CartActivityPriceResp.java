package com.ssy.lingxi.marketing.model.vo.activity.response;

import com.ssy.lingxi.marketing.api.model.response.CartActivityPriceBaseResp;
import com.ssy.lingxi.marketing.api.model.response.SkuJoinActivityFrontResp;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 *  返回购物车商品活动到手价
 * <AUTHOR>
 * @since 2021/10/14
 * @version 2.0.0
 */
@Getter
@Setter
public class CartActivityPriceResp extends CartActivityPriceBaseResp implements Serializable {


    private static final long serialVersionUID = -9156192724768147776L;

    /**
     * 进货单id[前端透传用]
     */
    private Long purchaseId;

    /**
     * 参与的活动
     */
    private List<SkuJoinActivityFrontResp> activityList;
}
