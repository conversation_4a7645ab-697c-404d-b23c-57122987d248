package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 商家优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantCouponSummaryPageResp extends MerchantCouponPageResp implements Serializable {

    private static final long serialVersionUID = -691738648935107958L;

    /**
     * 修改按钮
     */
    private boolean update;

    /**
     * 取消按钮
     */
    private boolean cancel;

    /**
     * 终止按钮
     */
    private boolean stop;

    /**
     * 重启按钮
     */
    private boolean restart;

}
