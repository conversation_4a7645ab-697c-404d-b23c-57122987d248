package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动内部流转记录返回实体
 * <AUTHOR> yzc
 * @version 2.0.0
 * @since 2021/8/20
 */
@Data
public class ActivityInnerRecordResp implements Serializable {

    private static final long serialVersionUID = 387338062175075903L;
    /**
     * 操作者
     */
    private String operator;

    /**
     * 部门
     */
    private String department;

    /**
     * 职位
     */
    private String jobTitle;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 操作枚举code
     */
    private Integer operateCode;

    /**
     * 操作
     */
    private String operate;

    /**
     * 操作时间(yyyy-MM-dd HH:mm)
     */
    private String operateTime;

    /**
     * 审核意见
     */
    private String opinion;
}
