package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.marketing.model.bo.CanReceiveCouponBO;
import com.ssy.lingxi.marketing.model.bo.MobileMerchantActivityGoodsCouponBO;
import com.ssy.lingxi.marketing.model.bo.ReceiveCouponBelongTypeBO;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MobileCouponResp;

import java.util.List;

/**
 * 优惠券领取服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/6
 */
public interface ICouponReceiveService {

    /**
     * 是否可領取(平台优惠券, 商家优惠券)
     * @param loginUser 登录用户信息
     * @param couponBelongTypeBO 请求参数
     * @return 返回结果
     */
    List<CanReceiveCouponBO> checkReceiveCoupon(UserLoginCacheDTO loginUser, ReceiveCouponBelongTypeBO couponBelongTypeBO);

    /**
     * 是否可領取(平台优惠券)
     * @param loginUser 登录用户信息
     * @param shopId 商城id
     * @param ids 优惠券id
     * @return 返回结果
     */
    List<CanReceiveCouponBO> checkPlatformCouponDetailReceive(UserLoginCacheDTO loginUser, Long shopId, List<Long> ids);

    /**
     * 是否可領取(商家优惠券, 平台优惠券)
     * @param loginUser 登录用户信息
     * @param shopId 商城id
     * @param ids 优惠券id
     * @return 返回结果
     */
    List<CanReceiveCouponBO> checkMerchantCouponDetailReceive(UserLoginCacheDTO loginUser, Long shopId, List<Long> ids);

    /**
     * 查询活动商品赠品优惠券(有规则校验)
     * @param loginUser 登录用户信息
     * @param shopId 商城id
     * @param ids 优惠券id
     * @return 返回结果
     */
    List<MobileMerchantActivityGoodsCouponBO> listMerchantActivityGoodsCoupon(UserLoginCacheDTO loginUser, Long shopId, List<Long> ids);

    /**
     * 查询活动商品赠品优惠券(无规则校验)
     * @param ids 优惠券id
     * @return 返回结果
     */
    List<MobileMerchantActivityGoodsCouponBO> listMerchantActivityGoodsCoupon(List<Long> ids);

    /**
     * 平台可领取优惠券
     * @param loginUser 登录用户信息
     * @param shopId 商城id
     * @return 返回结果
     */
    List<MobileCouponResp> listPlatformCouponCanReceiveComplete(UserLoginCacheDTO loginUser, Long shopId);

    /**
     * 商家可领取优惠券
     * @param loginUser 登录用户信息
     * @param shopId 商城id
     * @param memberId 店铺会员id
     * @param roleId 店铺角色id
     * @return 返回结果
     */
    List<MobileCouponResp> listMerchantCouponCanReceiveComplete(UserLoginCacheDTO loginUser, Long shopId, Long memberId, Long roleId);

    /**
     * 商家可领取优惠券(商品适用)
     * @param loginUser 登录用户信息
     * @param shopId 商城id
     * @param memberId 店铺会员id
     * @param roleId 店铺角色id
     * @param skuId 商品skuId
     * @param categoryId 商品分类id
     * @param brandId 商品品牌id
     * @return 返回结果
     */
    List<MobileCouponResp> listGoodsMerchantCoupon(UserLoginCacheDTO loginUser, Long shopId, Long memberId, Long roleId, Long skuId, Long categoryId, Long brandId);
}
