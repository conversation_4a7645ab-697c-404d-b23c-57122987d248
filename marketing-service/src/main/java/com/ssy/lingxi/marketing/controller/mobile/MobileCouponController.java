package com.ssy.lingxi.marketing.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.coupon.request.*;
import com.ssy.lingxi.marketing.model.vo.coupon.response.*;
import com.ssy.lingxi.marketing.service.IMobileCouponService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * App - 优惠券
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/6
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/mobile/coupon")
public class MobileCouponController extends BaseController {

    @Resource
    private IMobileCouponService mobileCouponService;

    /**
     * 我的优惠券 - 分页列表
     **/
    @GetMapping("/detail/page")
    public WrapperResp<PageDataResp<MobileCouponDetailResp>> pageCouponDetail(@Valid MobileCouponPageDataReq request) {
        return WrapperUtil.success(mobileCouponService.pageCouponDetail(getSysUser(), request));
    }

    /**
     * 我的优惠券 - 数量统计
     **/
    @GetMapping("/detail/count")
    public WrapperResp<MobileCouponDetailCountResp> getCouponCount(@Valid MobileCouponDetailCountReq request) {
        return WrapperUtil.success(mobileCouponService.getCouponCount(getSysUser(), request));
    }

    /**
     * 进货单领券 - 店铺优惠券列表
     */
    @GetMapping("/list/by/shop")
    public WrapperResp<List<MobileCouponResp>> listShopCoupon(@Valid MobileShopCouponListReq request) {
        return WrapperUtil.success(mobileCouponService.listShopCoupon(getSysUser(), request));
    }

    /**
     * 活动页 - 自动领取优惠券
     **/
    @PostMapping("/auto/receive")
    public WrapperResp<List<MobileCouponDetailResp>> autoReceiveCoupon(@Valid @RequestBody AutoReceiveCouponReq request) {
        return WrapperUtil.success(mobileCouponService.autoReceiveCoupon(getSysUser(), request));
    }

    /**
     * 用户领取优惠券
     **/
    @PostMapping("/receive")
    public WrapperResp<ReceiveCouponResp> receiveCoupon(@Valid @RequestBody ReceiveCouponReq request) {
        return WrapperUtil.success(mobileCouponService.receiveCoupon(getSysUser(), request));
    }

    /**
     * 提交订单 - 选择的优惠券列表
     **/
    @PostMapping("/list/by/order")
    public WrapperResp<List<MobileCouponDetailCanUseResp>> listOrderCouponDetail(@Valid @RequestBody MobileGoodsCartReq request) {
        return WrapperUtil.success(mobileCouponService.listOrderCouponDetail(getSysUser(), request));
    }

    /**
     * 查询优惠券所关联的商品信息
     **/
    @PostMapping("/goods/list")
    public WrapperResp<List<MobileActivityRelationGoodsResp>> listMerchantCouponRelationGoods(@Valid @RequestBody MobileCouponGoodsRelationReq request) {
        return WrapperUtil.success(mobileCouponService.listMerchantCouponRelationGoodsSku(getSysUser(), request));
    }

}
