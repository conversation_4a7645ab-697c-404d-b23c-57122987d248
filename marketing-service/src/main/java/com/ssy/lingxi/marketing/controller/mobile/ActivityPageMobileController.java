package com.ssy.lingxi.marketing.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.entity.activity.ActivityPageDO;
import com.ssy.lingxi.marketing.service.IActivityPageMobileService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * mobile - 活动页
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/09/07
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/mobile/activityPage")
public class ActivityPageMobileController extends BaseController {

    @Resource
    private IActivityPageMobileService activityPageMobileService;

    /**
     * 详情
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @GetMapping(value = "/get")
    public WrapperResp<ActivityPageDO> get(@Valid CommonIdReq dto) {
        return WrapperUtil.success(activityPageMobileService.get(dto));
    }
}
