package com.ssy.lingxi.marketing.serviceImpl.base.activity;

import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberLevelTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.marketing.entity.activity.ActivityMemberLevelDO;
import com.ssy.lingxi.marketing.entity.activity.ActivityShopDO;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityMemberLevelReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityShopReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityMemberLevelResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityShopResp;
import com.ssy.lingxi.marketing.repository.ActivityMemberLevelRepository;
import com.ssy.lingxi.marketing.repository.ActivityShopRepository;
import com.ssy.lingxi.marketing.util.ValidatorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基础活动规则服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/16
 */
@Slf4j
@Service
public class BaseActivityRuleService {

    @Resource
    private ActivityMemberLevelRepository memberLevelRepository;
    @Resource
    private ActivityShopRepository shopRepository;

    /**
     * 校验会员等级
     * @param memberLevelList 会员等级
     */
    public void checkMemberLevel(List<ActivityMemberLevelReq> memberLevelList) {
        if (CollectionUtils.isEmpty(memberLevelList)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_MEMBER_LEVEL);
        }
        for (ActivityMemberLevelReq activityMemberLevelReq : memberLevelList) {
            ValidatorUtil.validate(activityMemberLevelReq);
        }
    }

    /**
     * 查询活动适用会员等级
     *
     * @param belongTypeEnum 所属类型
     * @param activityId     活动id
     * @return 返回结果
     */
    public List<ActivityMemberLevelResp> listMemberLevel(BelongTypeEnum belongTypeEnum, Long activityId) {
        List<ActivityMemberLevelDO> memberLevelDOList = memberLevelRepository.findByBelongTypeAndActivityId(belongTypeEnum.getCode(), activityId);
        List<ActivityMemberLevelResp> memberLevelRespList = memberLevelDOList.stream().map(e -> {
            ActivityMemberLevelResp activityMemberLevelResp = new ActivityMemberLevelResp();
            activityMemberLevelResp.setId(e.getId());
            activityMemberLevelResp.setActivityId(e.getId());
            activityMemberLevelResp.setMemberLevelId(e.getMemberLevelId());
            activityMemberLevelResp.setMemberType(e.getMemberType());
            activityMemberLevelResp.setMemberTypeName(MemberTypeEnum.getName(e.getMemberType()));
            activityMemberLevelResp.setRoleType(e.getRoleType());
            activityMemberLevelResp.setRoleTypeName(RoleTypeEnum.getName(e.getRoleType()));
            activityMemberLevelResp.setRoleId(e.getRoleId());
            activityMemberLevelResp.setRoleName(e.getRoleName());
            activityMemberLevelResp.setLevelType(e.getLevelType());
            activityMemberLevelResp.setLevelTypeName(MemberLevelTypeEnum.getCodeMsg(e.getMemberType()));
            activityMemberLevelResp.setLevel(e.getLevel());
            activityMemberLevelResp.setLevelTag(e.getLevelTag());
            return activityMemberLevelResp;
        }).collect(Collectors.toList());

        return memberLevelRespList;
    }

    /**
     * 保存适用会员等级
     *
     * @param belongType      所属类型
     * @param activityId      活动id
     * @param memberLevelList 会员等级
     */
    public void saveMemberLevel(BelongTypeEnum belongType, Long activityId, List<ActivityMemberLevelReq> memberLevelList) {
        if (CollectionUtils.isEmpty(memberLevelList)) {
            return;
        }
        // 适用会员等级
        List<ActivityMemberLevelDO> memberLevelDOList = memberLevelList.stream().map(e -> {
            ActivityMemberLevelDO memberLevelDO = new ActivityMemberLevelDO();
            memberLevelDO.setActivityId(activityId);
            memberLevelDO.setBelongType(belongType.getCode());
            memberLevelDO.setMemberLevelId(e.getMemberLevelId());
            memberLevelDO.setMemberType(e.getMemberType());
            memberLevelDO.setMemberTypeName(MemberTypeEnum.getName(e.getMemberType()));
            memberLevelDO.setRoleId(e.getRoleId());
            memberLevelDO.setRoleName(e.getRoleName());
            memberLevelDO.setRoleType(e.getRoleType());
            memberLevelDO.setRoleTypeName(RoleTypeEnum.getName(e.getRoleType()));
            memberLevelDO.setLevel(e.getLevel());
            memberLevelDO.setLevelType(e.getLevelType());
            memberLevelDO.setLevelTypeName(MemberLevelTypeEnum.getCodeMsg(e.getMemberType()));
            memberLevelDO.setLevelTag(e.getLevelTag());
            return memberLevelDO;
        }).collect(Collectors.toList());
        memberLevelRepository.saveAll(memberLevelDOList);
    }

    /**
     * 删除适用会员等级
     * @param belongType 所属类型
     * @param activityId 活动id
     */
    public void deleteMemberLevel(BelongTypeEnum belongType, Long activityId) {
        memberLevelRepository.deleteByBelongTypeAndActivityId(belongType.getCode(), activityId);
    }

    /**
     * 校验商城
     * @param shopList 商城
     */
    public void checkShop(List<ActivityShopReq> shopList){
        if (CollectionUtils.isEmpty(shopList)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_SHOP);
        }
        for (ActivityShopReq activityShopReq : shopList) {
            ValidatorUtil.validate(activityShopReq);
        }
    }

    /**
     * 查询活动适用商城
     *
     * @param belongTypeEnum 所属类型
     * @param activityId     活动id
     * @return 返回结果
     */
    public List<ActivityShopResp> listShop(BelongTypeEnum belongTypeEnum, Long activityId) {
        List<ActivityShopDO> shopDOList = shopRepository.findByBelongTypeAndActivityId(belongTypeEnum.getCode(), activityId);
        List<ActivityShopResp> shopRespList = shopDOList.stream().map(e -> {
            ActivityShopResp activityShopResp = new ActivityShopResp();
            activityShopResp.setId(e.getId());
            activityShopResp.setActivityId(e.getId());
            activityShopResp.setShopId(e.getShopId());
            activityShopResp.setShopName(e.getShopName());
            activityShopResp.setLogo(e.getLogo());
            return activityShopResp;
        }).collect(Collectors.toList());

        return shopRespList;
    }

    /**
     * 保存适用商城
     *
     * @param belongType 所属类型
     * @param activityId 活动id
     * @param shopList   商城
     */
    public void saveShop(BelongTypeEnum belongType, Long activityId, List<ActivityShopReq> shopList) {
        if (CollectionUtils.isEmpty(shopList)) {
            return;
        }
        List<ActivityShopDO> shopDOList = shopList.stream().map(e -> {
            ActivityShopDO memberLevelDO = new ActivityShopDO();
            memberLevelDO.setActivityId(activityId);
            memberLevelDO.setBelongType(belongType.getCode());
            memberLevelDO.setShopId(e.getShopId());
            memberLevelDO.setShopName(e.getShopName());
            memberLevelDO.setLogo(e.getLogo());
            memberLevelDO.setEnvironment(e.getEnvironment());
            return memberLevelDO;
        }).collect(Collectors.toList());
        shopRepository.saveAll(shopDOList);
    }

    /**
     * 删除适用商城
     * @param belongType 所属类型
     * @param activityId 活动id
     */
    public void deleteShop(BelongTypeEnum belongType, Long activityId) {
        shopRepository.deleteByBelongTypeAndActivityId(belongType.getCode(), activityId);
    }

}