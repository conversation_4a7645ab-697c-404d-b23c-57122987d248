package com.ssy.lingxi.marketing.model.dto;

import com.ssy.lingxi.marketing.model.bo.ActivityGoodsBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 附属商品[赠商品或优惠券]dto
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/16
 */
@Getter
@Setter
public class ActivityGoodsSubsidiaryDTO implements Serializable {
    private static final long serialVersionUID = 2960655034457613839L;
    /**
     * 换购门槛/优惠门槛数量或金额
     */
    private BigDecimal limitValue;

    /**
     * 换购价格
     * */
    private BigDecimal swapPrice;

    /**
     * 允许换购数量/赠送数量/搭配数量
     */
    private BigDecimal num;

    /**
     * 所参与的活动
     */
    private ActivityGoodsBO activityGoodsBO;
}
