package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MobileCouponResp extends MobileBaseCouponResp {

    private static final long serialVersionUID = -5151710346320282173L;

    /**
     * 品牌id集合(品牌优惠券才有)
     */
    private List<Long> brandIds;

    /**
     * 品类id集合(品类优惠券才有)
     */
    private List<Long> categoryIds;

    /**
     * 商品Id集合(商品优惠券才有)
     */
    private List<Long> productIds;

    /**
     * 可领取状态 0-未登录 1-不符合条件 2-可领取 3-已领取
     */
    private Integer completeReceive;
}
