package com.ssy.lingxi.marketing.controller.activity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.activity.request.*;
import com.ssy.lingxi.marketing.model.vo.activity.response.*;
import com.ssy.lingxi.marketing.model.vo.common.response.PageItemResp;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ActivityMerchantPrizeCouponDataReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ActivityMerchantSubsidiaryCouponDataReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.FilterSkuIdReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityGoodsCouponResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityPrizeCouponResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.FilterSkuIdResp;
import com.ssy.lingxi.marketing.service.ICommonService;
import com.ssy.lingxi.marketing.service.IMerchantActivityService;
import com.ssy.lingxi.marketing.service.IMerchantCouponService;
import com.ssy.lingxi.marketing.serviceImpl.base.activity.BaseActivityRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 营销能力 - 商家营销活动管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/24
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/merchant/activity")
public class MerchantActivityController extends BaseController {
    @Resource
    private IMerchantActivityService merchantActivityService;
    @Resource
    private ICommonService commonService;
    @Resource
    private BaseActivityRecordService activityRecordService;
    @Resource
    private IMerchantCouponService merchantCouponService;

    /**
     * 商家营销活动查询 - 内部状态列表
     */
    @GetMapping("/getInnerStatusList")
    public WrapperResp<List<PageItemResp>> getStatusList() {
        return WrapperUtil.success(commonService.getMerchantActivityInnerStatuses());
    }

    /**
     * 商家营销活动查询 - 活动类型列表
     */
    @GetMapping("/getActivityTypeList")
    public WrapperResp<List<PageItemResp>> getActivityTypeList() {
        return WrapperUtil.success(commonService.getActivityTypes());
    }

    /**
     * 商家营销活动查询 - 分页列表
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MerchantActivitySummaryPageResp>> pageSummary(@Valid MerchantActivityCommonPageDataReq pageReq) {
        return WrapperUtil.success(merchantActivityService.pageSummary(getSysUser(), pageReq));
    }

    /**
     * 商家营销活动查询 - 活动详情
     */
    @GetMapping("/detail")
    public WrapperResp<MerchantActivityDetailResp> detail(@Valid CommonIdReq req) {
        return WrapperUtil.success(merchantActivityService.detail(getSysUser(), req));
    }

    /**
     * 商家营销活动查询 - 活动详情 - 活动商品(分页)
     */
    @GetMapping("/detail/goods/page")
    public WrapperResp<PageDataResp<McActivityGoodsPageResp>> pageByActivityGoods(@Valid PfActivityGoodsPageDataReq req) {
        return WrapperUtil.success(merchantActivityService.pageByActivityGoods(getSysUser(), req));
    }

    /**
     * 商家营销活动查询 - 活动详情 - 活动商品 - 选择附属优惠券查询条件
     * @return 返回结果
     */
    @GetMapping("/detail/goods/coupon/select/condition")
    public WrapperResp<List<SelectItemResp>> selectSubsidiaryCouponCondition() {
        return WrapperUtil.success(merchantCouponService.selectSubsidiaryCouponCondition());
    }

    /**
     * 商家营销活动查询 - 活动详情 - 活动商品 - 选择附属优惠券
     **/
    @GetMapping("/detail/goods/coupon/select")
    public WrapperResp<PageDataResp<ActivityGoodsCouponResp>> selectSubsidiaryCouponList(@Valid ActivityMerchantSubsidiaryCouponDataReq request) {
        return WrapperUtil.success(merchantCouponService.selectSubsidiaryCouponList(request, getSysUser()));
    }

    /**
     * 商家营销活动查询 - 活动详情 - 奖品 - 选择0元购买抵扣券
     **/
    @GetMapping("/detail/prize/coupon/select")
    public WrapperResp<PageDataResp<ActivityPrizeCouponResp>> selectPrizeCouponList(@Valid ActivityMerchantPrizeCouponDataReq request) {
        return WrapperUtil.success(merchantCouponService.selectPrizeCouponList(request, getSysUser()));
    }

    /**
     * 商家营销活动查询 - 活动详情 - 内部流转记录(分页)
     */
    @GetMapping("/inner/record/page")
    public WrapperResp<PageDataResp<ActivityInnerRecordResp>> pageInnerRecordList(@Valid ActivityInnerRecordPageDataReq pageReq) {
        return WrapperUtil.success(activityRecordService.pageMerchantActivityInnerRecord(getSysUser(), pageReq));
    }

    /**
     * 商家营销活动查询 - 终止
     */
    @PostMapping("/stop")
    public WrapperResp<Void> stop(@RequestBody @Valid ActivityActionReq stopReq) {
        return WrapperUtil.success(merchantActivityService.stop(getSysUser(), stopReq));
    }

    /**
     * 商家营销活动查询 - 重新启动
     */
    @PostMapping("/restart")
    public WrapperResp<Void> restart(@RequestBody @Valid ActivityActionReq restartReq) {
        return WrapperUtil.success(merchantActivityService.restart(getSysUser(), restartReq));
    }

    /**
     * 待提交审核商家营销活动 - 分页列表
     */
    @GetMapping("/page/tobe/submit/exam")
    public WrapperResp<PageDataResp<MerchantActivitySubmitExamPageResp>> pageByToBeSubmitExam(@Valid MerchantActivityCommonPageDataReq pageReq) {
        return WrapperUtil.success(merchantActivityService.pageByToBeSubmitExam(getSysUser(), pageReq));
    }

    /**
     * 待新增商家营销活动 - 新增
     */
    @PostMapping("/save")
    public WrapperResp<Void> save(@RequestBody @Valid MerchantActivityAddReq req) {
        return WrapperUtil.success(merchantActivityService.saveMerchantActivity(getSysUser(), req));
    }

    /**
     * 待新增商家营销活动 - 修改
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(@RequestBody @Valid MerchantActivityUpdateReq req) {
        return WrapperUtil.success(merchantActivityService.updateMerchantActivity(getSysUser(), req));
    }

    /**
     * 待新增商家营销活动 - 查询活动商品过滤的skuId
     */
    @PostMapping("/getFilterSkuId")
    public WrapperResp<FilterSkuIdResp> getFilterSkuId(@RequestBody @Valid FilterSkuIdReq req) {
        return WrapperUtil.success(merchantActivityService.getFilterSkuId(getSysUser(), req));
    }

    /**
     * 待新增商家营销活动 - 提交审核
     */
    @PostMapping("/submit/examine")
    public WrapperResp<Void> submitExamine(@Valid @RequestBody ActivitySubmitReq req) {
        return WrapperUtil.success(merchantActivityService.submitExamine(getSysUser(), Collections.singletonList(req.getId())));
    }

    /**
     * 待新增商家营销活动 - 批量提交审核
     */
    @PostMapping("/submit/examine/batch")
    public WrapperResp<Void> batchSubmitExamine(@Valid @RequestBody CommonIdListReq req) {
        return WrapperUtil.success(merchantActivityService.submitExamine(getSysUser(), req.getIdList()));
    }

    /**
     * 待新增商家营销活动 - 删除
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delete(@Valid @RequestBody ActivitySubmitReq req) {
        return WrapperUtil.success(merchantActivityService.batchDelete(getSysUser(), Collections.singletonList(req.getId())));
    }

    /**
     * 待新增商家营销活动 - 批量删除
     */
    @PostMapping("/delete/batch")
    public WrapperResp<Void> batchDelete(@Valid @RequestBody CommonIdListReq req) {
        return WrapperUtil.success(merchantActivityService.batchDelete(getSysUser(), req.getIdList()));
    }

    /**
     * 待审核商家营销活动(一级) - 分页列表
     */
    @GetMapping("/page/examine/step1")
    public WrapperResp<PageDataResp<MerchantActivityExamPageResp>> pageByExamineStep1(@Valid MerchantActivityCommonPageDataReq pageReq) {
        return WrapperUtil.success(merchantActivityService.pageByExamineStep1(getSysUser(), pageReq));
    }

    /**
     * 待审核商家营销活动(一级) - 审核
     */
    @PostMapping("/examine/step1")
    public WrapperResp<Void> examineStep1Update(@RequestBody @Valid ActivityExamineReq req) {
        return WrapperUtil.success(merchantActivityService.examineStep1Update(getSysUser(), req));
    }

    /**
     * 待审核商家营销活动(一级) - 批量审核
     */
    @PostMapping("/examine/step1/batch")
    public WrapperResp<Void> batchExamineStep1Update(@RequestBody @Valid CommonIdListReq req) {
        return WrapperUtil.success(merchantActivityService.batchExamineStep1Update(getSysUser(), req));
    }

    /**
     * 待审核商家营销活动(二级) - 分页列表
     */
    @GetMapping("/page/examine/step2")
    public WrapperResp<PageDataResp<MerchantActivityExamPageResp>> pageByExamineStep2(@Valid MerchantActivityCommonPageDataReq pageReq) {
        return WrapperUtil.success(merchantActivityService.pageByExamineStep2(getSysUser(), pageReq));
    }

    /**
     * 待审核商家营销活动(二级) - 审核
     */
    @PostMapping("/examine/step2")
    public WrapperResp<Void> examineStep2Update(@RequestBody @Valid ActivityExamineReq req) {
        return WrapperUtil.success(merchantActivityService.examineStep2Update(getSysUser(), req));
    }

    /**
     * 待审核商家营销活动(二级) - 批量审核
     */
    @PostMapping("/examine/step2/batch")
    public WrapperResp<Void> batchExamineStep2Update(@RequestBody @Valid CommonIdListReq req) {
        return WrapperUtil.success(merchantActivityService.batchExamineStep2Update(getSysUser(), req));
    }

    /**
     * 待提交营销活动 - 分页列表
     */
    @GetMapping("/page/tobe/submit")
    public WrapperResp<PageDataResp<MerchantActivitySubmitPageResp>> pageByToBeSubmit(@Valid MerchantActivityCommonPageDataReq pageReq) {
        return WrapperUtil.success(merchantActivityService.pageByToBeSubmit(getSysUser(), pageReq));
    }

    /**
     * 待提交营销活动 - 提交
     */
    @PostMapping("/submit")
    public WrapperResp<Void> submit(@RequestBody @Valid ActivitySubmitReq req) {
        return WrapperUtil.success(merchantActivityService.submit(getSysUser(), Collections.singletonList(req.getId())));
    }

    /**
     * 待提交营销活动 - 批量提交
     */
    @PostMapping("/submit/batch")
    public WrapperResp<Void> batchSubmit(@RequestBody @Valid CommonIdListReq req) {
        return WrapperUtil.success(merchantActivityService.submit(getSysUser(), req.getIdList()));
    }

    /**
     * 待上线商家营销活动 - 分页列表
     */
    @GetMapping("/page/tobe/online")
    public WrapperResp<PageDataResp<MerchantActivityOnlinePageResp>> pageByToBeOnline(@Valid MerchantActivityCommonPageDataReq pageReq) {
        return WrapperUtil.success(merchantActivityService.pageByToBeOnline(getSysUser(), pageReq));
    }

    /**
     * 待上线商家营销活动 - 上线活动
     */
    @PostMapping("/online")
    public WrapperResp<Void> online(@RequestBody @Valid ActivitySubmitReq req) {
        return WrapperUtil.success(merchantActivityService.onlineUpdate(getSysUser(), Collections.singletonList(req.getId())));
    }

    /**
     * 待上线商家营销活动 - 批量上线活动
     */
    @PostMapping("/online/batch")
    public WrapperResp<Void> batchOnline(@RequestBody @Valid CommonIdListReq req) {
        return WrapperUtil.success(merchantActivityService.onlineUpdate(getSysUser(), req.getIdList()));
    }
}
