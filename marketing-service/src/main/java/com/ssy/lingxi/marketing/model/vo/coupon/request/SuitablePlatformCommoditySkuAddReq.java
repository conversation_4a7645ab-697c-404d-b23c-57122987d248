package com.ssy.lingxi.marketing.model.vo.coupon.request;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * sku商品VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/7/16
 */
@Data
public class SuitablePlatformCommoditySkuAddReq implements Serializable {

    private static final long serialVersionUID = 3844178818823875937L;

    /**
     * 商品skuId
     */
    private Long id;

    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 商品编码
     */
    private String code;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品图片
     */
    private String mainPic;

    /**
     * 会员分类名称
     */
    private String customerCategoryName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 单位名称
     */
    private String unitName;

    /**
     * 商品单价
     */
    private Map<String, BigDecimal> unitPrice;

}
