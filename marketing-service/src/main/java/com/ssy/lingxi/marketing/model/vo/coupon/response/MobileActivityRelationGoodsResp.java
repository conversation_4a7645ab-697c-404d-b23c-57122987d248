package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 活动/优惠券关联的商品信息
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/24
 */
@Setter
@Getter
public class MobileActivityRelationGoodsResp {
    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品主图
     */
    private String mainPic;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品原价
     */
    private BigDecimal originalPrice;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 计量单位id
     */
    private String unitName;

    /**
     * 产品定价：1-现货价格, 2-价格需要询价, 3-积分兑换商品
     */
    private Integer priceType;

    /**
     * 商品标语
     */
    private String slogan;

    /**
     * 商品卖点
     */
    private List<String> sellingPoint;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员角色id
     */
    private Long memberRoleId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 已售数量
     */
    private BigDecimal sold = BigDecimal.ZERO;

    /**
     * 商品活动标签
     */
    private List<String> tagList;

    /**
     * skuId
     */
    private Long skuId;
    /**
     * 换购门槛/优惠门槛数量或金额
     */
    private BigDecimal limitValue;

    // ==================== 工费优惠相关字段 ====================
    
    /**
     * 原始工费
     */
    private BigDecimal originalWorkFee;

    /**
     * 优惠后工费
     */
    private BigDecimal discountedWorkFee;

    /**
     * 工费优惠金额
     */
    private BigDecimal workFeeDiscountAmount;

    /**
     * 工费优惠比例 (0-1之间，如0.8表示8折)
     */
    private BigDecimal workFeeDiscountRate;

    /**
     * 优惠类型描述 (如：工价8折、满100元工费减10元等)
     */
    private String discountTypeDescription;

    /**
     * 是否满足活动条件
     */
    private Boolean activitySatisfied;

    /**
     * 是否需要达到门槛条件
     */
    private Boolean needThreshold;

    /**
     * 门槛描述 (如：满100元、满2件等)
     */
    private String thresholdDescription;

    /**
     * 距离门槛还差多少
     */
    private BigDecimal thresholdGap;
}