package com.ssy.lingxi.marketing.model.vo.activity.response;

import cn.hutool.json.JSONObject;
import com.ssy.lingxi.marketing.model.vo.common.response.TaskStepResp;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *  平台活动-详情--响应VO
 * <AUTHOR>
 * @since 2021/6/19
 * @version 2.0.0
 */
@Data
public class BasePlatformActivityDetailResp implements Serializable {

    private static final long serialVersionUID = -517031506594871700L;

    /**
     * 活动id
     */
    private Long id;

    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     *         ActivityTypeEnum.class
     * */
    private Integer activityType;

    /**
     * 活动类型名称
     * */
    private String activityTypeName;

    /**
     * 活动参与类型（1：商家报名活动；2：平台自建活动(仅抽奖)）
     */
    private Integer activitySignUpType;

    /**
     * 活动参与类型名称
     */
    private String activitySignUpTypeName;

    /**
     * 活动名称
     * */
    private String activityName;

    /**
     * 活动开始时间
     * */
    private Long startTime;

    /**
     * 活动结束时间
     * */
    private Long endTime;

    /**
     * 报名开始时间
     * */
    private Long signUpStartTime;

    /**
     * 报名结束时间
     * */
    private Long signUpEndTime;

    /**
     * 创建时间
     * */
    private Long createTime;

    /**
     * 企业会员 0-否 1-是
     * */
    private Integer enterpriseMember;

    /**
     * 个人会员 0-否 1-是
     * */
    private Integer personalMember;

    /**
     * 新会员(平台会员) 0-否 1-是
     * */
    private Integer newMember;

    /**
     * 老会员(平台会员) 0-否 1-是
     * */
    private Integer oldMember;

    /**
     * 外部状态
     * */
    private Integer outerStatus;

    /**
     * 外部状态名称
     * */
    private String outerStatusName;

    /**
     * 外部工作流当前步骤
     * */
    private Integer outerTaskStep;

    /**
     * 内部状态
     * */
    private Integer innerStatus;

    /**
     * 内部状态名称
     * */
    private String innerStatusName;

    /**
     * 内部工作流当前步骤
     * */
    private Integer innerTaskStep;

    /**
     * 活动定义.
     * */
    private JSONObject activityDefined;

    /**
     * 适用会员等级：
     */
    private List<ActivityMemberLevelResp> memberLevelList;

    /**
     * 适用商城
     */
    private List<ActivityShopResp> shopList;

    /**
     * 外部工作流程
     * */
    private List<TaskStepResp> outerTaskList;

    /**
     * 内部工作流程
     * */
    private List<TaskStepResp> innerTaskList;

    /**
     * 外部流转记录
     * */
    private List<ActivityOuterRecordResp> outerRecordDOList;

    /**
     * 内部工作流程
     * */
    private List<ActivityInnerRecordResp> innerRecordDOList;
}
