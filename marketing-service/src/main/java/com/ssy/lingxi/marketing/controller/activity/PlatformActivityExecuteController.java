package com.ssy.lingxi.marketing.controller.activity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityExecutePageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.PfActivityGoodsPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.PlatformActivityPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityExecuteDetailPageResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityExecuteGoodsPageResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.PlatformActivityDetailResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.PlatformActivityExecutePageResp;
import com.ssy.lingxi.marketing.service.IActivityExecuteService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 平台后台 - 平台营销活动执行
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/23
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/platform/activity/execute")
public class PlatformActivityExecuteController extends BaseController {

    @Resource
    private IActivityExecuteService activityExecuteService;

    /**
     * 分页列表
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<PlatformActivityExecutePageResp>> pagePlatform(@Valid PlatformActivityPageDataReq pageReq) {
        return WrapperUtil.success(activityExecuteService.pagePlatform(getSysUser(), pageReq));
    }

    /**
     * 活动详情
     */
    @GetMapping("/detail")
    public WrapperResp<PlatformActivityDetailResp> detailPlatform(@Valid CommonIdReq req) {
        return WrapperUtil.success(activityExecuteService.detailPlatform(getSysUser(), req));
    }

    /**
     * 活动详情 - 活动商品(分页)
     */
    @GetMapping("/detail/goods/page")
    public WrapperResp<PageDataResp<ActivityExecuteGoodsPageResp>> pageByActivityGoodsPlatform(@Valid PfActivityGoodsPageDataReq req) {
        return WrapperUtil.success(activityExecuteService.pageByActivityGoodsPlatform(getSysUser(), req));
    }

    /**
     * 活动详情 - 活动商品-执行明细(分页)
     */
    @GetMapping("/detail/goods/execute/detail/page")
    public WrapperResp<PageDataResp<ActivityExecuteDetailPageResp>> pagePlatformExecuteDetailOfAbility(@Valid ActivityExecutePageDataReq req) {
        req.setBelongType(BelongTypeEnum.PLATFORM.getCode());
        return WrapperUtil.success(activityExecuteService.pageMerchantExecuteDetailOfAbility(getSysUser(), req));
    }
}
