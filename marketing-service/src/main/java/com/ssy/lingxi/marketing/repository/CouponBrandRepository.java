package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.coupon.CouponBrandDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 优惠券适用品牌仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/26
 */
public interface CouponBrandRepository extends JpaRepository<CouponBrandDO, Long>, JpaSpecificationExecutor<CouponBrandDO> {

    boolean existsByCouponId(Long id);

    List<CouponBrandDO> findAllByCouponId(Long id);

    List<CouponBrandDO> findAllByCouponIdIn(List<Long> ids);

    void deleteByCouponId(Long id);

    void deleteByCouponIdIn(List<Long> ids);
}
