package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.PlatformActivityDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 平台活动 - 实体仓库
 * <AUTHOR>
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface PlatformActivityRepository extends JpaRepository<PlatformActivityDO, Long>, JpaSpecificationExecutor<PlatformActivityDO> {

    List<PlatformActivityDO> findAllByIdInAndOuterStatus(List<Long> idList, Integer outerStatus);

    List<PlatformActivityDO> findAllByIdInAndOuterStatusAndStartTimeLessThanEqualAndEndTimeGreaterThanEqual(List<Long> idList, Integer outerStatus, Long startTime, Long endTime);

    List<PlatformActivityDO> findAllByIdInAndInnerStatusAndStartTimeLessThanEqualAndEndTimeGreaterThanEqual(List<Long> collect, Integer code, long currentTimeMillis, long currentTimeMillis1);

}
