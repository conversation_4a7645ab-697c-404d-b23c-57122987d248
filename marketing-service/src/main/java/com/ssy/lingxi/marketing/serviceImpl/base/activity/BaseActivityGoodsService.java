package com.ssy.lingxi.marketing.serviceImpl.base.activity;

import cn.hutool.core.util.NumberUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.marketing.ActivityTypeEnum;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.enums.product.CommodityStatusEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.marketing.entity.activity.*;
import com.ssy.lingxi.marketing.enums.ActivityGoodsAuditStatusEnum;
import com.ssy.lingxi.marketing.enums.GiveGiftTypeEnum;
import com.ssy.lingxi.marketing.model.bo.*;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityGoodsReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityGoodsSubsidiaryGroupReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.McActivityGoodsPageResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.PfActivitySignUpGoodsPageResp;
import com.ssy.lingxi.marketing.repository.ActivityGoodsCouponRepository;
import com.ssy.lingxi.marketing.repository.ActivityGoodsRepository;
import com.ssy.lingxi.marketing.repository.ActivityGoodsSubsidiaryRepository;
import com.ssy.lingxi.marketing.service.feign.IProductFeignService;
import com.ssy.lingxi.marketing.serviceImpl.component.activity.ActivityGoodsHandleComponent;
import com.ssy.lingxi.marketing.util.ValidatorUtil;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuStockResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础活动商品服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/16
 */
@Slf4j
@Service
public class BaseActivityGoodsService {

    @Resource
    private ActivityGoodsRepository goodsRepository;
    @Resource
    private ActivityGoodsSubsidiaryRepository goodsSubsidiaryRepository;
    @Resource
    private ActivityGoodsCouponRepository goodsCouponRepository;
    @Resource
    private ActivityGoodsHandleComponent goodsHandleComponent;
    @Resource
    private IProductFeignService productFeignService;

    //========================公共===============================

    /**
     * 保存活动商品
     * @param loginUser 登录用户信息
     * @param activityId 活动id
     * @param belongType 所属类型
     * @param activityType 活动类型
     * @param activityDefined 活动定义
     * @param signUpId 报名id
     * @param productList 活动商品
     */
    public void baseSaveActivityGoods(UserLoginCacheDTO loginUser, Long activityId, BelongTypeEnum belongType,
                                      Integer activityType, ActivityDefinedBO activityDefined, Long signUpId,
                                      List<ActivityGoodsReq> productList,Map<Long, CommoditySkuStockResp> skuMap) {
        productList.forEach(vo -> {
            ActivityGoodsDO activityGoodsDO = new ActivityGoodsDO();
            activityGoodsDO.setBelongType(belongType.getCode());
            activityGoodsDO.setActivityId(activityId);
            Integer auditStatus = ActivityGoodsAuditStatusEnum.NO_AUDIT.getCode();
            if(BelongTypeEnum.MERCHANT.getCode().equals(belongType.getCode())){
                //商家活动默认通过
                auditStatus=ActivityGoodsAuditStatusEnum.AGREE.getCode();
            }
            activityGoodsDO.setAuditStatus(auditStatus);
            activityGoodsDO.setProductId(vo.getProductId());
            activityGoodsDO.setSkuId(vo.getSkuId());
            activityGoodsDO.setProductName(vo.getProductName());
            activityGoodsDO.setType(vo.getType());
            activityGoodsDO.setCategory(vo.getCategory());
            activityGoodsDO.setBrand(vo.getBrand());
            activityGoodsDO.setUnit(vo.getUnit());
            activityGoodsDO.setPrice(vo.getPrice());
            activityGoodsDO.setPreSelPrice(vo.getPreSelPrice());
            activityGoodsDO.setPlummetPrice(vo.getPlummetPrice());
            activityGoodsDO.setActivityPrice(vo.getActivityPrice());
            activityGoodsDO.setDeductionPrice(vo.getDeductionPrice());
            activityGoodsDO.setDiscount(vo.getDiscount());
            activityGoodsDO.setRestrictNum(vo.getRestrictNum());
            activityGoodsDO.setRestrictTotalNum(vo.getRestrictTotalNum());
            activityGoodsDO.setProductImgUrl(vo.getProductImgUrl());
            activityGoodsDO.setMemberId(loginUser.getMemberId());
            activityGoodsDO.setRoleId(loginUser.getMemberRoleId());
            activityGoodsDO.setMemberName(loginUser.getMemberName());
            activityGoodsDO.setRoleName(loginUser.getUserRoleName());
            activityGoodsDO.setMemberType(loginUser.getMemberType());
            activityGoodsDO.setSignUpId(signUpId);
            CommoditySkuStockResp commoditySkuStockResp = skuMap.get(vo.getSkuId());
            if(commoditySkuStockResp !=null){
                activityGoodsDO.setType(commoditySkuStockResp.getCommodityAttribute());
            }
            goodsRepository.saveAndFlush(activityGoodsDO);

            // 判断是否包含附属信息(赠送促销、换购、套餐)
            if (ActivityTypeEnum.containSubsidiary(activityType)) {
                // 赠送促销
                if (ActivityTypeEnum.GIVE.getCode().equals(activityType)) {
                    GiveBO giveBO = (GiveBO) activityDefined;
                    activityGoodsDO.setGiveType(giveBO.getGiveType());
                    // 赠券
                    if (GiveGiftTypeEnum.COUPON.getCode().equals(giveBO.getGiftType())) {
                        List<PfActivitySignUpGoodsCouponBO> couponReqList = goodsHandleComponent.flatGoodsCoupon(activityType, vo.getCouponGroupList());
                        List<ActivityGoodsCouponDO> couponDOS = couponReqList.stream().map(c -> {
                            ActivityGoodsCouponDO couponDO = new ActivityGoodsCouponDO();
                            couponDO.setCouponId(c.getCouponId());
                            couponDO.setActivityGoods(activityGoodsDO);
                            couponDO.setCouponId(c.getCouponId());
                            couponDO.setCouponName(c.getCouponName());
                            couponDO.setGroupNo(c.getGroupNo());
                            couponDO.setLimitValue(c.getLimitValue());
                            couponDO.setNum(c.getNum());
                            return couponDO;
                        }).collect(Collectors.toList());
                        goodsCouponRepository.saveAll(couponDOS);
                    } else {
                        // 赠商品
                        List<PfActivitySignUpGoodsSubsidiaryBO> signUpGoodsBOList = goodsHandleComponent.flatGoodsSubsidiary(activityType, vo.getGoodsSubsidiaryGroupList());
                        List<ActivityGoodsSubsidiaryDO> goodsSubsidiaryDOS = signUpGoodsBOList.stream().map(g -> {
                            ActivityGoodsSubsidiaryDO goodsSubsidiaryDO = new ActivityGoodsSubsidiaryDO();
                            goodsSubsidiaryDO.setId(g.getId());
                            goodsSubsidiaryDO.setActivityGoods(activityGoodsDO);
                            goodsSubsidiaryDO.setMemberId(loginUser.getMemberId());
                            goodsSubsidiaryDO.setRoleId(loginUser.getMemberRoleId());
                            goodsSubsidiaryDO.setMemberType(loginUser.getMemberType());
                            goodsSubsidiaryDO.setProductId(g.getProductId());
                            goodsSubsidiaryDO.setSkuId(g.getSkuId());
                            goodsSubsidiaryDO.setProductName(g.getProductName());
                            goodsSubsidiaryDO.setCategory(g.getCategory());
                            goodsSubsidiaryDO.setBrand(g.getBrand());
                            goodsSubsidiaryDO.setUnit(g.getUnit());
                            goodsSubsidiaryDO.setGroupNo(g.getGroupNo());
                            goodsSubsidiaryDO.setLimitValue(g.getLimitValue());
                            goodsSubsidiaryDO.setPrice(g.getPrice());
                            goodsSubsidiaryDO.setSwapPrice(g.getSwapPrice());
                            goodsSubsidiaryDO.setNum(g.getNum());
                            goodsSubsidiaryDO.setGroupPrice(g.getGroupPrice());
                            goodsSubsidiaryDO.setProductImgUrl(g.getProductImgUrl());
                            CommoditySkuStockResp subCommodity = skuMap.get(g.getSkuId());
                            if(subCommodity!=null){
                                goodsSubsidiaryDO.setType(subCommodity.getCommodityAttribute());
                            }
                            return goodsSubsidiaryDO;
                        }).collect(Collectors.toList());
                        goodsSubsidiaryRepository.saveAll(goodsSubsidiaryDOS);
                    }
                } else {
                    // 换购或套餐商品
                    List<PfActivitySignUpGoodsSubsidiaryBO> signUpGoodsBOList = goodsHandleComponent.flatGoodsSubsidiary(activityType, vo.getGoodsSubsidiaryGroupList());
                    List<ActivityGoodsSubsidiaryDO> goodsSubsidiaryDOS = signUpGoodsBOList.stream()
                            .filter(o ->!o.getSkuId().equals(vo.getSkuId()))//换购与套餐过滤本品
                            .map(g -> {
                        ActivityGoodsSubsidiaryDO goodsSubsidiaryDO = new ActivityGoodsSubsidiaryDO();
                        goodsSubsidiaryDO.setId(g.getId());
                        goodsSubsidiaryDO.setActivityGoods(activityGoodsDO);
                        goodsSubsidiaryDO.setMemberId(loginUser.getMemberId());
                        goodsSubsidiaryDO.setRoleId(loginUser.getMemberRoleId());
                        goodsSubsidiaryDO.setMemberType(loginUser.getMemberType());
                        goodsSubsidiaryDO.setProductId(g.getProductId());
                        goodsSubsidiaryDO.setSkuId(g.getSkuId());
                        goodsSubsidiaryDO.setProductName(g.getProductName());
                        goodsSubsidiaryDO.setCategory(g.getCategory());
                        goodsSubsidiaryDO.setBrand(g.getBrand());
                        goodsSubsidiaryDO.setUnit(g.getUnit());
                        goodsSubsidiaryDO.setGroupNo(g.getGroupNo());
                        goodsSubsidiaryDO.setLimitValue(g.getLimitValue());
                        goodsSubsidiaryDO.setPrice(g.getPrice());
                        goodsSubsidiaryDO.setSwapPrice(g.getSwapPrice());
                        goodsSubsidiaryDO.setNum(g.getNum());
                        goodsSubsidiaryDO.setGroupPrice(g.getGroupPrice());
                        goodsSubsidiaryDO.setProductImgUrl(g.getProductImgUrl());
                        CommoditySkuStockResp subCommodity = skuMap.get(g.getSkuId());
                        if(subCommodity!=null){
                            goodsSubsidiaryDO.setType(subCommodity.getCommodityAttribute());
                        }
                        return goodsSubsidiaryDO;
                    }).collect(Collectors.toList());
                    goodsSubsidiaryRepository.saveAll(goodsSubsidiaryDOS);
                }
            }
        });
    }

    /**
     * 校验活动商品
     * @param activityType 活动类型
     * @param activityDefined 活动定义
     * @param productList 活动商品
     */
    public void checkActivityGoods(Integer activityType, ActivityDefinedBO activityDefined, List<ActivityGoodsReq> productList) {
        // 校验活动商品必填
        if (CollectionUtils.isEmpty(productList)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_NOT_EMPTY);
        }

        // 活动商品(sku商品)重复校验
        if (!productList.stream().map(ActivityGoodsReq::getSkuId).allMatch(new HashSet<>()::add)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_NOT_REPEAT);
        }

        // 校验对应活动类型的参数
        if (ActivityTypeEnum.SPECIAL_OFFER.getCode().equals(activityType)) {
            // 特价促销
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 活动价
                if (!ValidatorUtil.isPositive(activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_ACTIVITY_PRICE_POSITIVE);
                }
                if (!ValidatorUtil.isGreater(activityGoodsReq.getPrice(), activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_ACTIVITY_PRICE_GE_PRODUCE_PRICE_ERROR);
                }

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.PLUMMET.getCode().equals(activityType)) {
            // 直降促销
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 直降价
                if (!ValidatorUtil.isPositive(activityGoodsReq.getPlummetPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_PLUMMET_PRICE_POSITIVE);
                }
                // 活动价
                BigDecimal activityPrice = NumberUtil.sub(activityGoodsReq.getPrice(), activityGoodsReq.getPlummetPrice()).setScale(4, RoundingMode.HALF_UP);
                if (!ValidatorUtil.isGreater(activityGoodsReq.getPrice(), activityPrice)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_ACTIVITY_PRICE_GE_PRODUCE_PRICE_ERROR);
                }
                activityGoodsReq.setActivityPrice(activityPrice);

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.DISCOUNT.getCode().equals(activityType)) {
            // 折扣促销
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 折扣
                if (!ValidatorUtil.isGreater(activityGoodsReq.getDiscount(), BigDecimal.ZERO)
                        || !ValidatorUtil.isLess(activityGoodsReq.getDiscount(), BigDecimal.valueOf(100))) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_DISCOUNT_RANGE);
                }
                // 活动价
                BigDecimal activityPrice = NumberUtil.mul(activityGoodsReq.getPrice(), NumberUtil.div(activityGoodsReq.getDiscount(), BigDecimal.valueOf(100)));
                if (!ValidatorUtil.isGreater(activityGoodsReq.getPrice(), activityPrice)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_ACTIVITY_PRICE_GE_PRODUCE_PRICE_ERROR);
                }
                activityGoodsReq.setActivityPrice(activityPrice);

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.FULL_QUANTITY.getCode().equals(activityType)) {
            // 满量促销
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }

        } else if (ActivityTypeEnum.FULL_MONEY.getCode().equals(activityType)) {
            // 满额促销
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.GIVE.getCode().equals(activityType)) {
            // 赠送促销
            GiveBO giveBO = (GiveBO) activityDefined;
            for (ActivityGoodsReq activityGoodsReq : productList) {
                if (GiveGiftTypeEnum.GOODS.getCode().equals(giveBO.getGiftType())) {
                    // 赠商品
                    List<PfActivitySignUpGoodsSubsidiaryBO> subsidiaryReqList = goodsHandleComponent.flatGoodsSubsidiary(activityType, activityGoodsReq.getGoodsSubsidiaryGroupList());
                    if (CollectionUtils.isEmpty(subsidiaryReqList)) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SUBSIDIARY_NOT_EMPTY);
                    }
                    //不能赠相同的skuId
                    if (!subsidiaryReqList.stream().map(PfActivitySignUpGoodsSubsidiaryBO::getSkuId).allMatch(new HashSet<>()::add)) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GIVE_GOODS_NOT_REPEAT);
                    }

                    for (PfActivitySignUpGoodsSubsidiaryBO goodsSubsidiaryBO : subsidiaryReqList) {
                        // 优惠门槛
                        if (!ValidatorUtil.isPositive(goodsSubsidiaryBO.getLimitValue())) {
                            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_LIMIT_VALUE_POSITIVE);
                        }
                    }
                } else {
                    // 赠优惠券
                    List<PfActivitySignUpGoodsCouponBO> couponReqList = goodsHandleComponent.flatGoodsCoupon(activityType, activityGoodsReq.getCouponGroupList());
                    if (CollectionUtils.isEmpty(couponReqList)) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_COUPON_NOT_EMPTY);
                    }
                    //不能赠相同的couponId
                    if (!couponReqList.stream().map(PfActivitySignUpGoodsCouponBO::getCouponId).allMatch(new HashSet<>()::add)) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GIVE_COUPON_NOT_REPEAT);
                    }

                    for (PfActivitySignUpGoodsCouponBO goodsCouponBO : couponReqList) {
                        // 优惠门槛
                        if (!ValidatorUtil.isPositive(goodsCouponBO.getLimitValue())) {
                            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_LIMIT_VALUE_POSITIVE);
                        }
                    }
                }

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }

        } else if (ActivityTypeEnum.MORE_PIECE.getCode().equals(activityType)) {
            // 多件促销
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.COMBINATION.getCode().equals(activityType)) {
            // 组合促销
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.GROUP_PURCHASE.getCode().equals(activityType)) {
            // 拼团
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 拼团价
                if (!ValidatorUtil.isPositive(activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_GROUP_PURCHASE_PRICE_POSITIVE);
                }
                if (!ValidatorUtil.isGreater(activityGoodsReq.getPrice(), activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_ACTIVITY_PRICE_GE_PRODUCE_PRICE_ERROR);
                }

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.LOTTERY.getCode().equals(activityType)) {
            // 抽奖 ignore

        } else if (ActivityTypeEnum.BARGAIN.getCode().equals(activityType)) {
            // 砍价
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 起始价/底价
                if (!ValidatorUtil.isPositive(activityGoodsReq.getPlummetPrice())
                        || !ValidatorUtil.isPositive(activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_BARGAIN_PRICE_POSITIVE);
                }
                // 起始价要大于砍价底价
                if (!ValidatorUtil.isGreater(activityGoodsReq.getPlummetPrice(), activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_PLUMMET_PRICE_GE_ACTIVITY_PRICE_ERROR);
                }

                if (!ValidatorUtil.isGreater(activityGoodsReq.getPrice(), activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_ACTIVITY_PRICE_GE_PRODUCE_PRICE_ERROR);
                }

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.SEC_KILL.getCode().equals(activityType)) {
            // 秒杀
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 秒杀价
                if (!ValidatorUtil.isPositive(activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SEC_KILL_PRICE_POSITIVE);
                }
                if (!ValidatorUtil.isGreater(activityGoodsReq.getPrice(), activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_ACTIVITY_PRICE_GE_PRODUCE_PRICE_ERROR);
                }

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.SWAP.getCode().equals(activityType)) {
            // 换购
            for (ActivityGoodsReq activityGoodsReq : productList) {
                List<PfActivitySignUpGoodsSubsidiaryBO> SubsidiaryReqList = goodsHandleComponent.flatGoodsSubsidiary(activityType, activityGoodsReq.getGoodsSubsidiaryGroupList());
                if (CollectionUtils.isEmpty(SubsidiaryReqList)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_COUPON_NOT_EMPTY);
                }
                for (PfActivitySignUpGoodsSubsidiaryBO goodsSubsidiaryBO : SubsidiaryReqList) {
                    if (!ValidatorUtil.isPositive(goodsSubsidiaryBO.getLimitValue())) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_LIMIT_VALUE_POSITIVE);
                    }
                    if (!ValidatorUtil.isPositive(goodsSubsidiaryBO.getPrice())) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SUBSIDIARY_PRICE_POSITIVE);
                    }
                    if (!ValidatorUtil.isPositive(goodsSubsidiaryBO.getSwapPrice())) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SWAP_PRICE_POSITIVE);
                    }
                    if (!ValidatorUtil.isGreaterOrEqual(goodsSubsidiaryBO.getPrice(), goodsSubsidiaryBO.getSwapPrice())) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SWAP_GE_PRODUCT_PRICE_ERROR);
                    }
                }

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.PRE_SALE.getCode().equals(activityType)) {
            // 预售
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 预售价格
                if (!ValidatorUtil.isPositive(activityGoodsReq.getPreSelPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_PRE_SALE_PRICE_POSITIVE);
                }
                // 单位定金
                if (!ValidatorUtil.isPositive(activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_PRE_SALE_PRICE_POSITIVE);
                }
                // 定金抵扣
                if (!ValidatorUtil.isPositive(activityGoodsReq.getDeductionPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_PRE_SALE_PRICE_POSITIVE);
                }

                // 预售价要大于单位定金
                if (!ValidatorUtil.isGreater(activityGoodsReq.getPreSelPrice(), activityGoodsReq.getActivityPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SET_MEAL_PRE_GE_ACT_ERROR);
                }

                // 预售价要大于定金抵扣单价
                if (!ValidatorUtil.isGreater(activityGoodsReq.getPreSelPrice(), activityGoodsReq.getDeductionPrice())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SET_MEAL_PRE_GE_DED_ERROR);
                }

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }

        } else if (ActivityTypeEnum.SET_MEAL.getCode().equals(activityType)) {
            // 套餐
            for (ActivityGoodsReq activityGoodsReq : productList) {
                List<PfActivitySignUpGoodsSubsidiaryBO> SubsidiaryReqList = goodsHandleComponent.flatGoodsSubsidiary(activityType, activityGoodsReq.getGoodsSubsidiaryGroupList());
                if (CollectionUtils.isEmpty(SubsidiaryReqList)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_COUPON_NOT_EMPTY);
                }
                for (PfActivitySignUpGoodsSubsidiaryBO goodsSubsidiaryBO : SubsidiaryReqList) {
                    if (!ValidatorUtil.isPositive(goodsSubsidiaryBO.getPrice())) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SUBSIDIARY_PRICE_POSITIVE);
                    }
                    if (!ValidatorUtil.isPositive(goodsSubsidiaryBO.getGroupPrice())) {
                        throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SET_MEAL_PRICE_POSITIVE);
                    }
                }

                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        } else if (ActivityTypeEnum.ATTEMPT.getCode().equals(activityType)) {
            // 试用
            for (ActivityGoodsReq activityGoodsReq : productList) {
                // 活动个人限购数量和总限购数量
                if (!ValidatorUtil.isGreaterOrEqual(activityGoodsReq.getRestrictTotalNum(), activityGoodsReq.getRestrictNum())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_RESTRICT_GE_TOTAL_RESTRICT_ERROR);
                }
            }
        }
    }

    //========================商家活动===============================

    /**
     * 商家活动商品公共分页列表
     * @param memberId 会员id
     * @param roleId 角色id
     * @param activityId 活动id
     * @param current 当前页
     * @param pageSize 页大小
     * @return 返回结果
     */
    private PageDataResp<McActivityGoodsPageResp> basePageMerchantActivityGoodsCommon(Long memberId, Long roleId, Long activityId, int current, int pageSize, Integer activityType) {
        // 组装分页参数
        Pageable page = PageRequest.of(current - 1, pageSize);

        // 组装查询条件
        Specification<ActivityGoodsDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("activityId").as(Long.class), activityId));
            list.add(criteriaBuilder.equal(root.get("belongType").as(Integer.class), BelongTypeEnum.MERCHANT.getCode()));
            if (Objects.nonNull(memberId)) {
                list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), memberId));
            }
            if (Objects.nonNull(memberId)) {
                list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), roleId));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<ActivityGoodsDO> pageList = goodsRepository.findAll(spec, page);

        List<McActivityGoodsPageResp> resultList = pageList.stream().map(o -> {
            McActivityGoodsPageResp resp = new McActivityGoodsPageResp();
            resp.setId(o.getId());
            resp.setActivityId(o.getActivityId());
            resp.setMemberId(o.getMemberId());
            resp.setMemberName(o.getMemberName());
            resp.setRoleId(o.getRoleId());
            resp.setRoleName(o.getRoleName());
            resp.setProductId(o.getProductId());
            resp.setSkuId(o.getSkuId());
            resp.setProductName(o.getProductName());
            resp.setType(o.getType());
            resp.setCategory(o.getCategory());
            resp.setBrand(o.getBrand());
            resp.setUnit(o.getUnit());
            resp.setPrice(o.getPrice());
            resp.setPreSelPrice(o.getPreSelPrice());
            resp.setPlummetPrice(o.getPlummetPrice());
            resp.setActivityPrice(o.getActivityPrice());
            resp.setDeductionPrice(o.getDeductionPrice());
            resp.setDiscount(o.getDiscount());
            resp.setRestrictNum(o.getRestrictNum());
            resp.setRestrictTotalNum(o.getRestrictTotalNum());
            resp.setProductImgUrl(o.getProductImgUrl());
            resp.setSalesNum(com.ssy.lingxi.common.util.NumberUtil.setZeroOfNull(o.getSalesNum()));
            resp.setSalesAmount(com.ssy.lingxi.common.util.NumberUtil.setZeroOfNull(o.getSalesAmount()));
            resp.setRefundNum(com.ssy.lingxi.common.util.NumberUtil.setZeroOfNull(o.getRefundNum()));
            resp.setRefundAmount(com.ssy.lingxi.common.util.NumberUtil.setZeroOfNull(o.getRefundAmount()));
            //是否查询子表信息

                List<Long> activityGoodsIds = pageList.stream().map(ActivityGoodsDO::getId).collect(Collectors.toList());

                // 配套商品
                // 根据活动商品获取配套商品, (赠品、换购商品、套餐)才有
                if(ActivityTypeEnum.containSubsidiary(activityType)) {
                    Map<Long, List<ActivityGoodsSubsidiaryGroupBO>> activityGoodsSubsidiaryListMap = goodsHandleComponent.getActivityGoodsSubsidiaryListMap(activityGoodsIds);
                    List<ActivityGoodsSubsidiaryGroupBO> goodsSubList = Optional.ofNullable(activityGoodsSubsidiaryListMap.get(o.getId())).orElse(Collections.emptyList());
                    resp.setGoodsSubsidiaryGroupList(goodsSubList);
                }

                // 配套优惠券
                // 根据活动商品获取配套优惠券, (赠品)才有
                if(ActivityTypeEnum.GIVE.getCode().equals(activityType)) {
                    Map<Long, List<ActivityGoodsCouponGroupBO>> activityGoodsCouponListMap = goodsHandleComponent.getActivityGoodsCouponListMap(activityGoodsIds);
                    List<ActivityGoodsCouponGroupBO> goodsCouponList = Optional.ofNullable(activityGoodsCouponListMap.get(o.getId())).orElse(Collections.emptyList());
                    resp.setCouponGroupList(goodsCouponList);
                }
            return resp;
        }).collect(Collectors.toList());
        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }
    /**
     * 商家活动商品公共分页列表
     * @param memberId 会员id
     * @param roleId 角色id
     * @param activityId 活动id
     * @param current 当前页
     * @param pageSize 页大小
     * @return 返回结果
     */
    public PageDataResp<McActivityGoodsPageResp> basePageMerchantActivityGoods(Long memberId, Long roleId, Long activityId, int current, int pageSize, Integer activityType) {
        return basePageMerchantActivityGoodsCommon(memberId, roleId, activityId, current, pageSize,activityType);
    }

    /**
     * 保存商家活动商品
     *
     * @param loginUser       登录用户信息
     * @param activityId      活动gid
     * @param activityType    活动类型
     * @param activityDefined 活动json
     * @param productList     商品
     */
    public void saveMerchantActivityGoods(UserLoginCacheDTO loginUser, Long activityId,
                                          Integer activityType, ActivityDefinedBO activityDefined,
                                          List<ActivityGoodsReq> productList,Map<Long, CommoditySkuStockResp> skuMap) {
        // 抽奖没有适用商品列表
        if (ActivityTypeEnum.LOTTERY.getCode().equals(activityType)) {
            return;
        }
        this.baseSaveActivityGoods(loginUser, activityId, BelongTypeEnum.MERCHANT, activityType, activityDefined, null, productList,skuMap);
    }

    /**
     * 删除商家活动商品
     * 循环删除数据, 避免锁表, in可能会被数据库执行计划优化全表扫描
     * @param merchantActivityDO 商家活动
     */
    public void deleteActivityGoods(MerchantActivityDO merchantActivityDO) {
        Long activityId = merchantActivityDO.getId();
        Integer activityType = merchantActivityDO.getActivityType();
        ActivityDefinedBO activityDefined = merchantActivityDO.getActivityDefined();
        // 抽奖没有适用商品列表
        if (ActivityTypeEnum.LOTTERY.getCode().equals(activityType)) {
            return;
        }

        List<ActivityGoodsDO> activityGoodsDOList = goodsRepository.findByBelongTypeAndActivityId(BelongTypeEnum.MERCHANT.getCode(), activityId);

        // 判断是否包含附属信息(赠送促销、换购、套餐)
        if (ActivityTypeEnum.containSubsidiary(activityType)) {
            // 赠送促销
            if (ActivityTypeEnum.GIVE.getCode().equals(activityType)) {
                GiveBO giveBO = (GiveBO) activityDefined;
                // 赠券
                if (GiveGiftTypeEnum.COUPON.getCode().equals(giveBO.getGiftType())) {
                    activityGoodsDOList.forEach(e -> {
                        goodsCouponRepository.deleteByActivityGoodsId(e.getId());
                    });
                } else {
                    // 赠商品
                    activityGoodsDOList.forEach(e -> {
                        goodsSubsidiaryRepository.deleteByActivityGoodsId(e.getId());
                    });
                }
            } else {
                // 换购或套餐商品
                activityGoodsDOList.forEach(e -> {
                    goodsSubsidiaryRepository.deleteByActivityGoodsId(e.getId());
                });
            }
        }

        goodsRepository.deleteByBelongTypeAndActivityId(BelongTypeEnum.MERCHANT.getCode(), activityId);
    }

    //========================平台活动===============================

    /**
     * 平台活动报名商品分页列表
     * @param memberId 报名会员id(当前登录会员id)
     * @param roleId 报名角色id(当前登录角色id)
     * @param signUpId 报名id
     * @param current 当前页
     * @param pageSize 页大小
     * @return 返回结果
     */
    public PageDataResp<PfActivitySignUpGoodsPageResp> basePageSignUpActivityGoods(Long memberId, Long roleId, Long signUpId, int current, int pageSize) {
        // 组装分页参数
        Pageable page = PageRequest.of(current - 1, pageSize);

        // 组装查询条件
        Specification<ActivityGoodsDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("signUpId").as(Long.class), signUpId));
            if (Objects.nonNull(memberId)) {
                list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), memberId));
            }
            if (Objects.nonNull(roleId)) {
                list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), roleId));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<ActivityGoodsDO> pageList = goodsRepository.findAll(spec, page);
        if (CollectionUtils.isEmpty(pageList.getContent())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        // 根据活动商品获取配套商品, (赠品、换购商品、套餐)才有
        List<Long> activityGoodsIds = pageList.stream().map(ActivityGoodsDO::getId).collect(Collectors.toList());
        Map<Long, List<ActivityGoodsSubsidiaryGroupBO>> activityGoodsSubsidiaryListMap = goodsHandleComponent.getActivityGoodsSubsidiaryListMap(activityGoodsIds);
        // 根据活动商品获取配套优惠券, (赠品)才有
        Map<Long, List<ActivityGoodsCouponGroupBO>> activityGoodsCouponListMap = goodsHandleComponent.getActivityGoodsCouponListMap(activityGoodsIds);

        List<PfActivitySignUpGoodsPageResp> resultList = pageList.getContent().stream().map(o -> {
            PfActivitySignUpGoodsPageResp resp = new PfActivitySignUpGoodsPageResp();
            resp.setId(o.getId());
            resp.setActivityId(o.getId());
            resp.setSignUpId(o.getSignUpId());
            resp.setMemberId(o.getMemberId());
            resp.setMemberName(o.getMemberName());
            resp.setRoleId(o.getRoleId());
            resp.setRoleName(o.getRoleName());
            resp.setProductId(o.getProductId());
            resp.setSkuId(o.getSkuId());
            resp.setProductName(o.getProductName());
            resp.setType(o.getType());
            resp.setCategory(o.getCategory());
            resp.setBrand(o.getBrand());
            resp.setUnit(o.getUnit());
            resp.setPrice(o.getPrice());
            resp.setPreSelPrice(o.getPreSelPrice());
            resp.setPlummetPrice(o.getPlummetPrice());
            resp.setActivityPrice(o.getActivityPrice());
            resp.setDeductionPrice(o.getDeductionPrice());
            resp.setDiscount(o.getDiscount());
            resp.setRestrictNum(o.getRestrictNum());
            resp.setRestrictTotalNum(o.getRestrictTotalNum());
            resp.setProductImgUrl(o.getProductImgUrl());
            // 配套商品
            List<ActivityGoodsSubsidiaryGroupBO> goodsSubList = Optional.ofNullable(activityGoodsSubsidiaryListMap.get(o.getId())).orElse(Collections.emptyList());
            resp.setGoodsSubsidiaryGroupList(goodsSubList);
            // 配套优惠券
            List<ActivityGoodsCouponGroupBO> goodsCouponList = Optional.ofNullable(activityGoodsCouponListMap.get(o.getId())).orElse(Collections.emptyList());
            resp.setCouponGroupList(goodsCouponList);
            return resp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 平台活动活动商品分页列表(已审核通过)
     * @param activityId 活动id
     * @param current 当前页
     * @param pageSize 页大小
     * @return 返回结果
     */
    public PageDataResp<PfActivitySignUpGoodsPageResp> basePagePlatformActivityGoods(UserLoginCacheDTO loginUser, Long activityId, int current, int pageSize, Integer activityType) {
        // 组装分页参数
        Pageable page = PageRequest.of(current - 1, pageSize);

        // 组装查询条件
        Specification<ActivityGoodsDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();

            list.add(criteriaBuilder.equal(root.get("activityId").as(Long.class), activityId));
            list.add(criteriaBuilder.equal(root.get("belongType").as(Integer.class), BelongTypeEnum.PLATFORM.getCode()));
            // 审核通过的活动商品
            list.add(criteriaBuilder.equal(root.get("auditStatus").as(Integer.class), ActivityGoodsAuditStatusEnum.AGREE.getCode()));
            //商户查询报名商品活动执行
            if(loginUser!=null){
                list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
                list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };


        Page<ActivityGoodsDO> pageList = goodsRepository.findAll(spec, page);


        List<PfActivitySignUpGoodsPageResp> resultList = pageList.getContent().stream().map(o -> {
            PfActivitySignUpGoodsPageResp resp = new PfActivitySignUpGoodsPageResp();
            resp.setId(o.getId());
            resp.setActivityId(o.getActivityId());
            resp.setSignUpId(o.getSignUpId());
            resp.setMemberId(o.getMemberId());
            resp.setMemberName(o.getMemberName());
            resp.setRoleId(o.getRoleId());
            resp.setRoleName(o.getRoleName());
            resp.setProductId(o.getProductId());
            resp.setSkuId(o.getSkuId());
            resp.setProductName(o.getProductName());
            resp.setType(o.getType());
            resp.setCategory(o.getCategory());
            resp.setBrand(o.getBrand());
            resp.setUnit(o.getUnit());
            resp.setPrice(o.getPrice());
            resp.setPreSelPrice(o.getPreSelPrice());
            resp.setPlummetPrice(o.getPlummetPrice());
            resp.setActivityPrice(o.getActivityPrice());
            resp.setDeductionPrice(o.getDeductionPrice());
            resp.setDiscount(o.getDiscount());
            resp.setRestrictNum(o.getRestrictNum());
            resp.setRestrictTotalNum(o.getRestrictTotalNum());
            resp.setProductImgUrl(o.getProductImgUrl());
            resp.setSalesNum(com.ssy.lingxi.common.util.NumberUtil.setZeroOfNull(o.getSalesNum()));
            resp.setSalesAmount(com.ssy.lingxi.common.util.NumberUtil.setZeroOfNull(o.getSalesAmount()));
            resp.setRefundNum(com.ssy.lingxi.common.util.NumberUtil.setZeroOfNull(o.getRefundNum()));
            resp.setRefundAmount(com.ssy.lingxi.common.util.NumberUtil.setZeroOfNull(o.getRefundAmount()));

            List<Long> activityGoodsIds = pageList.stream().map(ActivityGoodsDO::getId).collect(Collectors.toList());

            // 配套商品
            if(ActivityTypeEnum.containSubsidiary(activityType)) {
                // 根据活动商品获取配套商品, (赠品、换购商品、套餐)才有
                Map<Long, List<ActivityGoodsSubsidiaryGroupBO>> activityGoodsSubsidiaryListMap = goodsHandleComponent.getActivityGoodsSubsidiaryListMap(activityGoodsIds);
                List<ActivityGoodsSubsidiaryGroupBO> goodsSubList = Optional.ofNullable(activityGoodsSubsidiaryListMap.get(o.getId())).orElse(Collections.emptyList());
                resp.setGoodsSubsidiaryGroupList(goodsSubList);
            }

            // 配套优惠券
            if(ActivityTypeEnum.GIVE.getCode().equals(activityType)){
                // 根据活动商品获取配套优惠券, (赠品)才有
                Map<Long, List<ActivityGoodsCouponGroupBO>> activityGoodsCouponListMap = goodsHandleComponent.getActivityGoodsCouponListMap(activityGoodsIds);
                List<ActivityGoodsCouponGroupBO> goodsCouponList = Optional.ofNullable(activityGoodsCouponListMap.get(o.getId())).orElse(Collections.emptyList());
                resp.setCouponGroupList(goodsCouponList);
            }
            return resp;
        }).collect(Collectors.toList());
        return new PageDataResp<>(pageList.getTotalElements(), resultList);
    }

    /**
     * 保存平台活动商品
     *
     * @param loginUser       登录用户信息
     * @param activityId      活动gid
     * @param activityType    活动类型
     * @param activityDefined 活动json
     * @param productList     商品
     */
    public void savePlatformSignUpActivityGoods(UserLoginCacheDTO loginUser, Long activityId,
                                                Integer activityType, ActivityDefinedBO activityDefined, Long signUpId,
                                                List<ActivityGoodsReq> productList,Map<Long, CommoditySkuStockResp> skuMap) {
        // 抽奖没有适用商品列表
        if (ActivityTypeEnum.LOTTERY.getCode().equals(activityType)) {
            return;
        }
        this.baseSaveActivityGoods(loginUser, activityId, BelongTypeEnum.PLATFORM, activityType, activityDefined, signUpId, productList,skuMap);
    }

    /**
     * 删除平台活动报名商品
     * 循环删除数据, 避免锁表, in可能会被数据库执行计划优化全表扫描
     * @param platformActivityDO 平台活动
     * @param signUpId 活动报名id
     */
    public void deleteSignUpActivityGoods(PlatformActivityDO platformActivityDO, Long signUpId) {
        Long activityId = platformActivityDO.getId();
        Integer activityType = platformActivityDO.getActivityType();
        ActivityDefinedBO activityDefined = platformActivityDO.getActivityDefined();

        // 抽奖没有适用商品列表
        if (ActivityTypeEnum.LOTTERY.getCode().equals(activityType)) {
            return;
        }

        List<ActivityGoodsDO> activityGoodsDOList = goodsRepository.findByBelongTypeAndActivityId(BelongTypeEnum.PLATFORM.getCode(), activityId);

        // 判断是否包含附属信息(赠送促销、换购、套餐)
        if (ActivityTypeEnum.containSubsidiary(activityType)) {
            // 赠送促销
            if (ActivityTypeEnum.GIVE.getCode().equals(activityType)) {
                GiveBO giveBO = (GiveBO) activityDefined;
                // 赠券
                if (GiveGiftTypeEnum.COUPON.getCode().equals(giveBO.getGiftType())) {
                    activityGoodsDOList.forEach(e -> {
                        goodsCouponRepository.deleteByActivityGoodsId(e.getId());
                    });
                } else {
                    // 赠商品
                    activityGoodsDOList.forEach(e -> {
                        goodsSubsidiaryRepository.deleteByActivityGoodsId(e.getId());
                    });
                }
            } else {
                // 换购或套餐商品
                activityGoodsDOList.forEach(e -> {
                    goodsSubsidiaryRepository.deleteByActivityGoodsId(e.getId());
                });
            }
        }

        goodsRepository.deleteByBelongTypeAndActivityIdAndSignUpId(BelongTypeEnum.PLATFORM.getCode(), activityId, signUpId);
    }

    /**
     * 批量查询sku商品信息
     * @param productList 活动商品列表
     * @param activityType 活动类型
     * @param memberId 会员Id
     * @param roleId 角色Id
     * @return key-skuId， value-商品信息
     */
    public Map<Long, CommoditySkuStockResp> getReqSkuMap(List<ActivityGoodsReq> productList, Integer activityType, Long memberId, Long roleId){
        //活动商品的skuIds
        List<Long> actSkuIds = productList.stream().map(ActivityGoodsReq::getSkuId).collect(Collectors.toList());
        //如果活动含搭配商品
        if(ActivityTypeEnum.containSubsidiary(activityType)){
            for(ActivityGoodsReq req :productList){
                List<ActivityGoodsSubsidiaryGroupReq> goodsSubsidiaryGroupList = req.getGoodsSubsidiaryGroupList();
                if(!CollectionUtils.isEmpty(goodsSubsidiaryGroupList)){
                    List<Long> subSkuIds = goodsSubsidiaryGroupList.stream().flatMap(o -> o.getList().stream().map(v -> v.getSkuId())).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(subSkuIds)){
                        actSkuIds.addAll(subSkuIds);
                    }
                }
            }
        }
        //查询sku商品列表
        Map<Long, CommoditySkuStockResp> commoditySkuList = productFeignService.getCommoditySkuMap(actSkuIds, memberId, roleId, CommodityStatusEnum.ON_SHELF.getCode());
        //校验
        if(!CollectionUtils.isEmpty(commoditySkuList)){
            boolean isNotExists = actSkuIds.stream().anyMatch(o -> commoditySkuList.get(o) == null);
            if(isNotExists){
                throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_GOODS_SKU_ERROR);
            }
        }
        return commoditySkuList;
    }

    /**
     * 查询审核通过的活动商品列表
     * @param belongType  活动归属类型
     * @param activityId  活动Id
     * @param productName 商品名称
     * @return 活动商品列表
     */
    public List<ActivityGoodsDO> getActivityGoodsDOList(Integer belongType,Long activityId,String productName){
        // 其他活动显示该活动所有参与商品
        return goodsRepository.findAll((Specification<ActivityGoodsDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            predicateList.add(cb.equal(root.get("belongType"), belongType));
            predicateList.add(cb.equal(root.get("activityId"), activityId));
            predicateList.add(cb.equal(root.get("auditStatus"), ActivityGoodsAuditStatusEnum.AGREE.getCode()));
            if (StringUtils.hasLength(productName)) {
                predicateList.add(cb.like(root.get("productName"), "%" + productName + "%"));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        });
    }

}