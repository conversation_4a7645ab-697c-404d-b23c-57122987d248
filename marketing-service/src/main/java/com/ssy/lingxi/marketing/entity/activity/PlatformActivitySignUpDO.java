package com.ssy.lingxi.marketing.entity.activity;


import com.fasterxml.jackson.annotation.JsonBackReference;
import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 *  平台活动会员报名记录表-DO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@EqualsAndHashCode
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_activity_sign_up",
        indexes = {@Index(name=TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_activity_sign_up_activity_id_idx", columnList = "activity_id")})
public class PlatformActivitySignUpDO implements Serializable {

    private static final long serialVersionUID = 1049061942856865018L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 多对一双向关联活动
     */
    @JsonBackReference
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "activity_id", referencedColumnName = "id")
    private PlatformActivityDO platformActivity;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 会员名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String memberName;

    /**
     * 会员角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 外部扩展状态  4：报名审核通过，5：报名审核不通过 （仅保存4,5）
     * PlatformActivitySignUpOuterStatusEnum
     */
    @Column(columnDefinition = "int4")
    private Integer outerExtendStatus;

    /**
     * 内部状态
     * PlatformActivitySignUpInnerStatusEnum
     */
    @Column(columnDefinition = "int4")
    private Integer status;

    /**
     * 任务ID
     */
    @Column(columnDefinition = "varchar(50)")
    private String taskId;

    /**
     * 工作流key
     */
    @Column(columnDefinition = "varchar(50)")
    private String processKey;

    /**
     * 报名时间
     */
    @Column(columnDefinition = "int8")
    private Long createTime;

    /**
     * 更新时间
     */
    @Column(columnDefinition = "int8")
    private Long updateTime;
}
