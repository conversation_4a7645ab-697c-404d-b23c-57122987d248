package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *  平台营销活动报名-待审核报名资料-审核-接口参数VO
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@Getter
@Setter
public class PfActivityExamineSubmitReq implements Serializable {

    private static final long serialVersionUID = 6433258770451489999L;
    /**
     * 报名id
     */
    @NotNull(message = "报名id不能为空")
    @Range(min = 1,message = "报名id必须大于0")
    private Long id;

}
