package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.MerchantActivityDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 营销活动主实体仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/17
 */
public interface MerchantActivityRepository extends JpaRepository<MerchantActivityDO, Long>, JpaSpecificationExecutor<MerchantActivityDO> {

    List<MerchantActivityDO> findAllByIdInAndInnerStatus(List<Long> id, Integer outerStatus);

    List<MerchantActivityDO> findAllByIdInAndInnerStatusAndStartTimeLessThanEqualAndEndTimeGreaterThanEqual(List<Long> activityIdList, Integer outerStatus, Long startTime, long endTime);
}
