package com.ssy.lingxi.marketing.entity.activity;


import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  活动[会员]商品-赠优惠券表-DO
 *  包含[赠送促销-赠券]
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_goods_coupon",
        indexes = {@Index(name=TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_goods_coupon_activity_goods_id_idx", columnList = "activity_goods_id")})
public class ActivityGoodsCouponDO implements Serializable {

    private static final long serialVersionUID = -7585565352360417120L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 活动商品
     */
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "activity_goods_id", referencedColumnName = "id")
    private ActivityGoodsDO activityGoods;

    /**
     * 优惠券id
     */
    @Column(columnDefinition = "int8")
    private Long couponId;

    /**
     * 优惠券名称
     */
    @Column(columnDefinition = "varchar(100)")
    private String couponName;

    /**
     * 优惠阶梯
     */
    @Column(columnDefinition = "int2")
    private Integer groupNo;

    /**
     * 优惠门槛数量或金额
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal limitValue;

    /**
     * 赠送数量/搭配数量
     */
    @Column(columnDefinition = "int2")
    private BigDecimal num;
}
