package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品活动优惠信息返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024/12/10
 */
@Getter
@Setter
public class MobileGoodsActivityDiscountResp implements Serializable {

    private static final long serialVersionUID = -1284106139690376924L;

    /**
     * 商品skuId
     */
    private Long skuId;

    /**
     * 命中的活动ID
     */
    private Long activityId;

    /**
     * 命中的活动名称
     */
    private String activityName;

    /**
     * 活动类型 (18-满额促销, 19-满量促销)
     */
    private Integer activityType;

    /**
     * 活动类型名称
     */
    private String activityTypeName;

    /**
     * 商品原价（优惠前价格）
     */
    private BigDecimal originalPrice;

    /**
     * 商品优惠后价格
     */
    private BigDecimal discountedPrice;

    /**
     * 分摊到该商品的优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 是否参与了活动
     */
    private Boolean participated;

    /**
     * 活动规则描述
     */
    private String ruleDescription;
    
    /**
     * 原始工费
     */
    private BigDecimal originalWorkFee;
    
    /**
     * 优惠后工费
     */
    private BigDecimal discountedWorkFee;
    
    /**
     * 工费优惠金额
     */
    private BigDecimal workFeeDiscountAmount;
    
    /**
     * 商品总价（原价 * 数量）
     */
    private BigDecimal originalTotalPrice;

}