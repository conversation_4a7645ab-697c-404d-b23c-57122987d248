package com.ssy.lingxi.marketing.repository;

import com.ssy.lingxi.marketing.entity.activity.ActivityPageDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface ActivityPageRepository extends JpaRepository<ActivityPageDO, Long> {

    List<ActivityPageDO> findAllByTypeAndStatusNot(Integer type, Integer code);
}
