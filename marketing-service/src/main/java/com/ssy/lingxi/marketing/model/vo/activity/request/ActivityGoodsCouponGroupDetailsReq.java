package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品-附属优惠券信息-组明细-接口参数VO
 * <AUTHOR> yzc
 * @since 2021/08/16
 * @version 2.0.0
 */
@Getter
@Setter
public class ActivityGoodsCouponGroupDetailsReq implements Serializable {
    private static final long serialVersionUID = 6423368351388229655L;

    /**
     * id
     * */
    private Long id;

    /**
     * 活动商品id
     */
    private Long activityGoodsId;

    /**
     * 优惠券id
     */
//    @NotNull(message = "优惠券id必填")
//    @Range(min = 1, message = "优惠券id必须大于0")
    private Long couponId;

    /**
     * 优惠券名称
     */
//    @NotNull(message = "优惠券名称必填")
    private String couponName;

    /**
     * 赠送数量
     */
//    @NotNull(message = "优惠门槛必填")
//    @Range(min = 1, message = "赠送数量必须大于0")
    private BigDecimal num;
}
