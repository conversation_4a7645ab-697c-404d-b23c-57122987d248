package com.ssy.lingxi.marketing.serviceImpl.base.coupon;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.MessageNoticeEnum;
import com.ssy.lingxi.component.base.enums.marketing.MerchantCouponTypeEnum;
import com.ssy.lingxi.component.base.enums.member.AbilitySuitableMemberEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.service.IMessageService;
import com.ssy.lingxi.component.rabbitMQ.model.req.SystemMessageReq;
import com.ssy.lingxi.marketing.constant.MarketingConstant;
import com.ssy.lingxi.marketing.entity.coupon.MerchantCouponDO;
import com.ssy.lingxi.marketing.entity.coupon.MerchantCouponHistoryDO;
import com.ssy.lingxi.marketing.enums.CouponStrOperateEnum;
import com.ssy.lingxi.marketing.enums.MerchantCouponEffectiveTypeEnum;
import com.ssy.lingxi.marketing.enums.MerchantCouponGetWayEnum;
import com.ssy.lingxi.marketing.enums.MerchantCouponStatusEnum;
import com.ssy.lingxi.marketing.model.vo.coupon.request.MerchantCouponBaseReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.MerchantCouponPageDataReq;
import com.ssy.lingxi.marketing.repository.MerchantCouponHistoryRepository;
import com.ssy.lingxi.marketing.repository.MerchantCouponRepository;
import com.ssy.lingxi.marketing.service.IProcessFeignService;
import com.ssy.lingxi.marketing.service.IScheduleFeignService;
import com.ssy.lingxi.marketing.serviceImpl.component.SuitableFieldComponent;
import com.ssy.lingxi.workflow.api.enums.ProcessTaskStatusEnum;
import com.ssy.lingxi.workflow.api.model.req.TaskExecuteReq;
import com.ssy.lingxi.workflow.api.model.resp.SimpleTaskCompleteResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 基础商家优惠券服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/30
 */
@Slf4j
@Service
public class BaseMerchantCouponService extends BaseCouponService {

    @Resource
    private MerchantCouponRepository merchantCouponRepository;
    @Resource
    private MerchantCouponHistoryRepository merchantCouponHistoryRepository;
    @Resource
    private IProcessFeignService processFeignService;
    @Resource
    private IScheduleFeignService scheduleFeignService;
    @Resource
    private IMessageService messageService;

    /**
     * 校验优惠券参数
     * @param request 请求参数
     * @return 返回结果
     */
    protected void checkMerchantCouponParam(MerchantCouponBaseReq request) {
        long currentTimeMillis = System.currentTimeMillis();
        // 券发放时间
        this.checkReleaseTime(currentTimeMillis, request.getReleaseTimeStart(), request.getReleaseTimeEnd());
        
        // 券有效时间
        if (MerchantCouponEffectiveTypeEnum.FIXED.getCode().equals(request.getEffectiveType())) {
            this.checkEffectiveTime(request.getReleaseTimeEnd(), request.getEffectiveTimeStart(), request.getEffectiveTimeEnd());
        } else {
            if (Objects.isNull(request.getInvalidDay())) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_EFFECTIVE_TIME_NOT_NULL);
            }
        }
        // 优惠券类型
        if (MerchantCouponTypeEnum.ZERO_DISCOUNT.getCode().equals(request.getType())) {
            // 0元抵扣券
            // 领取方式只能是营销活动用券
            if (!MerchantCouponGetWayEnum.MARKETING.getCode().equals(request.getGetWay())) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_ZERO_DISCOUNT_MUST_MARKETING);
            }
            // 只能有一个适用商品
            if (CollectionUtils.isEmpty(request.getSuitableCommoditySkuList()) || request.getSuitableCommoditySkuList().size() != 1) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_ZERO_DISCOUNT_COMMODITY_NOT_EMPTY);
            }
        } else {
            // 非0元抵扣券
            // 券面额不能为0
            if (BigDecimal.ZERO.equals(request.getDenomination())) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_DENOMINATION_NOT_ZERO);
            }
            // 使用条件订单金额不能为0
            if (BigDecimal.ZERO.equals(request.getUseConditionMoney())) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_USER_CONDITION_MONEY_NOT_ZERO);
            }
        }
        // 领取方式为前台用户领券, 必有领取条件
        if (MerchantCouponGetWayEnum.FRONT.getCode().equals(request.getGetWay())) {
            if (Objects.isNull(request.getConditionGetTotal()) || Objects.isNull(request.getConditionGetDay())) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_GET_WAY_POSITIVE);
            }
            if (request.getConditionGetTotal() <= 0 || request.getConditionGetDay() <= 0) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_GET_WAY_POSITIVE);
            }
            if (request.getConditionGetTotal() < request.getConditionGetDay()) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_GET_TOTAL_GE_GET_DAY);
            }
        }
        // 领券方式为指定会员发劵、会员运营用劵,适用用户为非平台会员(下级商户会员)
        if (Arrays.asList(MerchantCouponGetWayEnum.APPOINT.getCode(), MerchantCouponGetWayEnum.OPERATION.getCode()).contains(request.getGetWay())) {
            boolean flag = request.getSuitableMemberTypes().contains(AbilitySuitableMemberEnum.NEW_MEMBER.getCode())
                    || request.getSuitableMemberTypes().contains(AbilitySuitableMemberEnum.OLD_MEMBER.getCode());
            if (!flag) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_GET_WAY_SUITABLE_MEMBER_TYPE);
            }

            // 数据校验与清洗(防止提交非法数据)
            List<Integer> retainList = Arrays.asList(AbilitySuitableMemberEnum.NEW_MEMBER.getCode(), AbilitySuitableMemberEnum.OLD_MEMBER.getCode());
            request.setSuitableMemberTypes(SuitableFieldComponent.checkAndClean(retainList, request.getSuitableMemberTypes()));
        }
        // 使用条件金额要大于券面额
        if (request.getUseConditionMoney().compareTo(request.getDenomination()) <= 0) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_USER_AMOUNT_CONDITION_GREATER);
        }
    }

    /**
     * 公共商家优惠券分页列表
     * @param request 请求参数
     * @param statusList 状态
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    protected Page<MerchantCouponDO> basePageMerchantCoupon(MerchantCouponPageDataReq request, List<Integer> statusList, UserLoginCacheDTO loginUser) {
        Pageable page = PageRequest.of(request.getCurrent() - 1, request.getPageSize(), Sort.by(request.getSortName()).descending());

        return merchantCouponRepository.findAll((Specification<MerchantCouponDO>) (root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();

            predicateList.add(cb.equal(root.get("memberId"), loginUser.getMemberId()));
            predicateList.add(cb.equal(root.get("roleId"), loginUser.getMemberRoleId()));

            if (Objects.nonNull(request.getId())) {
                predicateList.add(cb.equal(root.get("id"), request.getId()));
            }

            if (Objects.nonNull(request.getType())) {
                predicateList.add(cb.equal(root.get("type"), request.getType()));
            }

            if (Objects.nonNull(request.getGetWay())) {
                predicateList.add(cb.equal(root.get("getWay"), request.getGetWay()));
            }

            if (Objects.nonNull(request.getStatus())) {
                predicateList.add(cb.equal(root.get("status"), request.getStatus()));
            }

            if (!CollectionUtils.isEmpty(statusList)) {
                predicateList.add(cb.and(root.get("status").in(statusList)));
            }

            if (StringUtils.isNotEmpty(request.getName())) {
                predicateList.add(cb.like(root.get("name"), "%" + request.getName() + "%"));
            }

            if (Objects.nonNull(request.getReleaseTimeStart())) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get("releaseTimeStart"), request.getReleaseTimeStart()));
            }

            if (Objects.nonNull(request.getReleaseTimeEnd())) {
                predicateList.add(cb.lessThanOrEqualTo(root.get("releaseTimeEnd"), request.getReleaseTimeEnd()));
            }

            if (Objects.nonNull(request.getEffectiveTimeStart())) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get("effectiveTimeStart"), request.getEffectiveTimeStart()));
            }

            if (Objects.nonNull(request.getEffectiveTimeEnd())) {
                predicateList.add(cb.lessThanOrEqualTo(root.get("effectiveTimeEnd"), request.getEffectiveTimeEnd()));
            }

            Predicate[] p = new Predicate[predicateList.size()];
            return query.where(predicateList.toArray(p)).getRestriction();
        }, page);
    }

    /**
     * 待审核商家优惠劵(一级) - 提交审核
     * @param loginUser 登录用户信息
     * @param merchantCouponDOList 商家活动
     * @param agree 是否同意
     * @param remark 审核意见
     */
    protected void baseAuditOneMerchantCoupon(UserLoginCacheDTO loginUser, List<MerchantCouponDO> merchantCouponDOList, Integer agree, String remark) {
        for (MerchantCouponDO merchantCouponDO : merchantCouponDOList) {
            TaskExecuteReq executeVO = new TaskExecuteReq();
            executeVO.setProcessKey(MarketingConstant.MERCHANT_COUPON_KEY);
            executeVO.setTaskId(merchantCouponDO.getTaskId());
            executeVO.setMemberId(loginUser.getMemberId());
            executeVO.setRoleId(loginUser.getMemberRoleId());
            executeVO.setAgree(agree);
            executeVO.setDataId(merchantCouponDO.getId());

            // 工作流
            WrapperResp<SimpleTaskCompleteResp> result = processFeignService.completeSimpleTask(executeVO);
            if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                log.error("商家优惠券审核(一级),调用工作流失败: {}", result.getMessage());
                throw new BusinessException(result.getMessage());
            }

            merchantCouponDO.setStatus(result.getData().getStatus());
            merchantCouponDO.setTaskId(result.getData().getTaskId());
            merchantCouponRepository.saveAndFlush(merchantCouponDO);

            // 流转记录
            MerchantCouponHistoryDO merchantCouponHistoryDO = new MerchantCouponHistoryDO();
            merchantCouponHistoryDO.setCouponId(merchantCouponDO.getId());
            merchantCouponHistoryDO.setCreateTime(System.currentTimeMillis());
            merchantCouponHistoryDO.setMemberId(loginUser.getMemberId());
            merchantCouponHistoryDO.setMemberName(loginUser.getMemberName());
            merchantCouponHistoryDO.setRoleId(loginUser.getMemberRoleId());
            merchantCouponHistoryDO.setRoleName(loginUser.getMemberRoleName());
            merchantCouponHistoryDO.setOperatorId(loginUser.getUserId());
            merchantCouponHistoryDO.setOperatorName(loginUser.getUserName());
            merchantCouponHistoryDO.setOperatorRoleName(loginUser.getUserRoleName());
            merchantCouponHistoryDO.setOperatorOrgName(loginUser.getOrgName());
            merchantCouponHistoryDO.setOperatorJobTitle(loginUser.getJobTitle());
            merchantCouponHistoryDO.setOperationCode(CouponStrOperateEnum.mc_inner_examine_step1.getCode());
            merchantCouponHistoryDO.setOperation(CouponStrOperateEnum.mc_inner_examine_step1.getName());
            merchantCouponHistoryDO.setStatus(result.getData().getStatus());
            merchantCouponHistoryDO.setStatusName(MerchantCouponStatusEnum.getNameByCode(result.getData().getStatus()));
            merchantCouponHistoryDO.setRemark(remark);
            merchantCouponHistoryRepository.saveAndFlush(merchantCouponHistoryDO);
            if(agree == 1){
                // 发送消息
                sendContractSystemMessageInner(merchantCouponDO, MessageNoticeEnum.MARKETING_MERCHANT_COUPON_AUDIT_STEP_2.getCode());
            }else {
                // 发送消息
                sendContractSystemMessageInner(merchantCouponDO, MessageNoticeEnum.MARKETING_MERCHANT_COUPON_COMMIT_AUDIT.getCode());
            }
        }
    }

    /**
     * 待审核商家优惠劵(二级) - 审核
     * @param loginUser 登录用户信息
     * @param merchantCouponDOList 商家活动
     * @param agree 是否同意
     * @param remark 审核意见
     */
    protected void baseAuditTwoMerchantCoupon(UserLoginCacheDTO loginUser, List<MerchantCouponDO> merchantCouponDOList, Integer agree, String remark) {
        for (MerchantCouponDO merchantCouponDO : merchantCouponDOList) {
            TaskExecuteReq executeVO = new TaskExecuteReq();
            executeVO.setProcessKey(MarketingConstant.MERCHANT_COUPON_KEY);
            executeVO.setTaskId(merchantCouponDO.getTaskId());
            executeVO.setMemberId(loginUser.getMemberId());
            executeVO.setRoleId(loginUser.getMemberRoleId());
            executeVO.setAgree(agree);
            executeVO.setDataId(merchantCouponDO.getId());

            // 工作流
            WrapperResp<SimpleTaskCompleteResp> result = processFeignService.completeSimpleTask(executeVO);
            if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                log.error("商家优惠券审核(二级),调用工作流失败: {}", result.getMessage());
                throw new BusinessException(ResponseCodeEnum.SERVICE_WORKFLOW_ERROR);
            }

            merchantCouponDO.setStatus(result.getData().getStatus());
            merchantCouponDO.setTaskId(result.getData().getTaskId());
            merchantCouponRepository.saveAndFlush(merchantCouponDO);

            // 流转记录
            MerchantCouponHistoryDO merchantCouponHistoryDO = new MerchantCouponHistoryDO();
            merchantCouponHistoryDO.setCouponId(merchantCouponDO.getId());
            merchantCouponHistoryDO.setCreateTime(System.currentTimeMillis());
            merchantCouponHistoryDO.setMemberId(loginUser.getMemberId());
            merchantCouponHistoryDO.setMemberName(loginUser.getMemberName());
            merchantCouponHistoryDO.setRoleId(loginUser.getMemberRoleId());
            merchantCouponHistoryDO.setRoleName(loginUser.getMemberRoleName());
            merchantCouponHistoryDO.setOperatorId(loginUser.getUserId());
            merchantCouponHistoryDO.setOperatorName(loginUser.getUserName());
            merchantCouponHistoryDO.setOperatorRoleName(loginUser.getUserRoleName());
            merchantCouponHistoryDO.setOperatorOrgName(loginUser.getOrgName());
            merchantCouponHistoryDO.setOperatorJobTitle(loginUser.getJobTitle());
            merchantCouponHistoryDO.setOperationCode(CouponStrOperateEnum.mc_inner_examine_step2.getCode());
            merchantCouponHistoryDO.setOperation(CouponStrOperateEnum.mc_inner_examine_step2.getName());
            merchantCouponHistoryDO.setStatus(result.getData().getStatus());
            merchantCouponHistoryDO.setStatusName(MerchantCouponStatusEnum.getNameByCode(result.getData().getStatus()));
            merchantCouponHistoryDO.setRemark(remark);
            merchantCouponHistoryRepository.saveAndFlush(merchantCouponHistoryDO);

            // 发送消息
            sendContractSystemMessageInner(merchantCouponDO, MessageNoticeEnum.MARKETING_MERCHANT_COUPON_COMMIT.getCode());
        }
    }

    /**
     * 待提交商家优惠劵 - 提交
     * @param loginUser 登录用户信息
     * @param merchantCouponDOList 商家活动
     */
    protected void baseSubmitMerchantCoupon(UserLoginCacheDTO loginUser, List<MerchantCouponDO> merchantCouponDOList) {
        long currentTimeMillis = System.currentTimeMillis();
        for (MerchantCouponDO merchantCouponDO : merchantCouponDOList) {
            TaskExecuteReq executeVO = new TaskExecuteReq();
            executeVO.setProcessKey(MarketingConstant.MERCHANT_COUPON_KEY);
            executeVO.setTaskId(merchantCouponDO.getTaskId());
            executeVO.setMemberId(loginUser.getMemberId());
            executeVO.setRoleId(loginUser.getMemberRoleId());
            executeVO.setAgree(ProcessTaskStatusEnum.GOTO_NEXT_STEP.getCode());
            executeVO.setDataId(merchantCouponDO.getId());

            // 工作流
            WrapperResp<SimpleTaskCompleteResp> result = processFeignService.completeSimpleTask(executeVO);
            if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                log.error("商家优惠券提交,调用工作流失败: {}", result.getMessage());
                throw new BusinessException(ResponseCodeEnum.SERVICE_WORKFLOW_ERROR);
            }

            merchantCouponDO.setStatus(result.getData().getStatus());
            merchantCouponDO.setTaskId(result.getData().getTaskId());
            merchantCouponRepository.saveAndFlush(merchantCouponDO);

            // 流转记录
            MerchantCouponHistoryDO merchantCouponHistoryDO = new MerchantCouponHistoryDO();
            merchantCouponHistoryDO.setCouponId(merchantCouponDO.getId());
            merchantCouponHistoryDO.setCreateTime(System.currentTimeMillis());
            merchantCouponHistoryDO.setMemberId(loginUser.getMemberId());
            merchantCouponHistoryDO.setMemberName(loginUser.getMemberName());
            merchantCouponHistoryDO.setRoleId(loginUser.getMemberRoleId());
            merchantCouponHistoryDO.setRoleName(loginUser.getMemberRoleName());
            merchantCouponHistoryDO.setOperatorId(loginUser.getUserId());
            merchantCouponHistoryDO.setOperatorName(loginUser.getUserName());
            merchantCouponHistoryDO.setOperatorRoleName(loginUser.getUserRoleName());
            merchantCouponHistoryDO.setOperatorOrgName(loginUser.getOrgName());
            merchantCouponHistoryDO.setOperatorJobTitle(loginUser.getJobTitle());
            merchantCouponHistoryDO.setOperationCode(CouponStrOperateEnum.mc_inner_submit.getCode());
            merchantCouponHistoryDO.setOperation(CouponStrOperateEnum.mc_inner_submit.getName());
            merchantCouponHistoryDO.setStatus(result.getData().getStatus());
            merchantCouponHistoryDO.setStatusName(MerchantCouponStatusEnum.getNameByCode(result.getData().getStatus()));
            merchantCouponHistoryRepository.saveAndFlush(merchantCouponHistoryDO);

            if (merchantCouponDO.getReleaseTimeStart() > currentTimeMillis) {
                // 定时发券开始
                scheduleFeignService.createMcCouponReleaseStartTask(loginUser, merchantCouponDO);

                // 定时发券结束
                scheduleFeignService.createMcCouponReleaseEndTask(loginUser, merchantCouponDO);
            } else if (merchantCouponDO.getReleaseTimeStart() < currentTimeMillis
                    && merchantCouponDO.getReleaseTimeEnd() > currentTimeMillis) {

                merchantCouponDO.setStatus(MerchantCouponStatusEnum.EXECUTING.getCode());
                merchantCouponRepository.saveAndFlush(merchantCouponDO);

                // 定时发券结束
                scheduleFeignService.createMcCouponReleaseEndTask(loginUser, merchantCouponDO);
            } else if (merchantCouponDO.getReleaseTimeEnd() < currentTimeMillis) {

                merchantCouponDO.setStatus(MerchantCouponStatusEnum.FINISH.getCode());
                merchantCouponRepository.saveAndFlush(merchantCouponDO);
            }
        }
    }

    /**
     * 发送商家营销活动模板消息-内部
     *
     * @param merchantCouponDO    商家优惠卷活动
     * @param messageTemplateCode 消息模板代码
     */
    private void sendContractSystemMessageInner(MerchantCouponDO merchantCouponDO, String messageTemplateCode) {
        SystemMessageReq request = new SystemMessageReq();
        request.setMemberId(merchantCouponDO.getMemberId());
        request.setRoleId(merchantCouponDO.getRoleId());
        request.setMessageNotice(messageTemplateCode);
        request.setParams(Stream.of(merchantCouponDO.getId().toString(), merchantCouponDO.getName()).collect(Collectors.toList()));
        messageService.sendSystemMessage(request);
    }
}