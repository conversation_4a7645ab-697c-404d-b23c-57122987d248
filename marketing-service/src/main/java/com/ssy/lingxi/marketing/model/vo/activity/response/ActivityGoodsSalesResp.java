package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  查询活动商品销量-接口参数VO
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@Getter
@Setter
public class ActivityGoodsSalesResp implements Serializable {


    private static final long serialVersionUID = 7222288302733422431L;
    /**
     * 总销量
     */
    private BigDecimal totalSales;
    /**
     * 会员购买数量
     */
    private BigDecimal memberNum;
}
