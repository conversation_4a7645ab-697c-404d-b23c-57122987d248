package com.ssy.lingxi.marketing.entity.activity;

import com.ssy.lingxi.common.constant.TableNameConstant;

import javax.persistence.*;
import java.io.Serializable;

/**
 *  平台活动报名内部记录
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_activity_sign_up_inner_record",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_activity_sign_up_inner_record_activity_id_idx", columnList = "activityId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_activity_sign_up_inner_record_sign_up_id_idx", columnList = "signUpId")})
public class PlatformActivitySignUpInnerRecordDO implements Serializable {

    private static final long serialVersionUID = -5403498777875174815L;
    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 平台活动id
     */
    @Column(columnDefinition = "int8")
    private Long activityId;

    /**
     * 活动报名id
     */
    @Column(columnDefinition = "int8")
    private Long signUpId;

    /**
     * 用户id
     */
    @Column(columnDefinition = "int8")
    private Long userId;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 操作者
     */
    @Column(columnDefinition = "varchar(50)")
    private String operator;

    /**
     * 部门
     */
    @Column(columnDefinition = "varchar(50)")
    private String department;

    /**
     * 职位
     */
    @Column(columnDefinition = "varchar(50)")
    private String jobTitle;

    /**
     * 状态:PlatformActivitySignUpInnerStatusEnum/PlatformActivityInnerStatusEnum
     */
    @Column(columnDefinition = "int")
    private Integer status;

    /**
     * 状态名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String statusName;

    /**
     * 操作枚举
     */
    @Column(columnDefinition = "int")
    private Integer operateCode;

    /**
     * 操作
     */
    @Column(columnDefinition = "varchar(50)")
    private String operate;

    /**
     * 操作时间
     */
    @Column(columnDefinition = "int8")
    private Long operateTime;

    /**
     * 审核意见
     */
    @Column(columnDefinition = "varchar(120)")
    private String opinion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getSignUpId() {
        return signUpId;
    }

    public void setSignUpId(Long signUpId) {
        this.signUpId = signUpId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public Integer getOperateCode() {
        return operateCode;
    }

    public void setOperateCode(Integer operateCode) {
        this.operateCode = operateCode;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public Long getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Long operateTime) {
        this.operateTime = operateTime;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }
}
