package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 会员等级VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/7/16
 */
@Data
public class SuitableMemberLevelResp implements Serializable {

    private static final long serialVersionUID = -1585634608205755353L;

    /**
     * 等级id
     */
    private Long id;

    /**
     * 会员类型
     */
    private Integer memberType;

    /**
     * 会员类型
     */
    private String memberTypeName;

    /**
     * 角色类型
     */
    private Integer roleType;

    /**
     * 角色类型名称
     */
    private String roleTypeName;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 等级类型
     */
    private Integer levelType;

    /**
     * 等级类型名称
     */
    private String memberLevelTypeName;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 等级标签
     */
    private String levelTag;
}
