package com.ssy.lingxi.marketing.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工费优惠策略工厂
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/25
 */
@Slf4j
@Component
public class WorkFeeDiscountStrategyFactory {

    @Autowired
    private List<WorkFeeDiscountStrategy> strategies;

    private final Map<Integer, WorkFeeDiscountStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (WorkFeeDiscountStrategy strategy : strategies) {
            Integer activityType = strategy.getSupportedActivityType();
            if (strategyMap.containsKey(activityType)) {
                log.warn("发现重复的活动类型策略: {}, 已存在: {}, 新策略: {}", 
                        activityType, 
                        strategyMap.get(activityType).getClass().getSimpleName(),
                        strategy.getClass().getSimpleName());
            }
            strategyMap.put(activityType, strategy);
            log.info("注册工费优惠策略: {} -> {}", activityType, strategy.getClass().getSimpleName());
        }
    }

    /**
     * 根据活动类型获取对应的策略
     * 
     * @param activityType 活动类型
     * @return 工费优惠策略，如果不支持则返回null
     */
    public WorkFeeDiscountStrategy getStrategy(Integer activityType) {
        WorkFeeDiscountStrategy strategy = strategyMap.get(activityType);
        if (strategy == null) {
            log.debug("不支持的活动类型: {}", activityType);
        }
        return strategy;
    }

    /**
     * 检查是否支持某个活动类型
     * 
     * @param activityType 活动类型
     * @return 是否支持
     */
    public boolean isSupported(Integer activityType) {
        return strategyMap.containsKey(activityType);
    }

    /**
     * 获取所有支持的活动类型
     * 
     * @return 支持的活动类型列表
     */
    public Integer[] getSupportedActivityTypes() {
        return strategyMap.keySet().toArray(new Integer[0]);
    }
}