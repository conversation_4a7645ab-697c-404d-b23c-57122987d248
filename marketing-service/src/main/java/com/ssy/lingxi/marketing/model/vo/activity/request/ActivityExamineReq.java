package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 活动审核请求类
 * <AUTHOR> yzc
 * @version 2.0.0
 * @since 2021/8/20
 */
@Data
public class ActivityExamineReq implements Serializable {

    private static final long serialVersionUID = 7347319066253579221L;
    /**
     * 活动报名id
     * */
    @NotNull(message = "活动报名id不能为空")
    private Long id;

    /**
     * 是否通过：0-否，1-是
     */
    @NotNull(message = "是否通过不能为空")
    private Integer isPass;

    /**
     * 审核意见
     */
    private String opinion;
}
