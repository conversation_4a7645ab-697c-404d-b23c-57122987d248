package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.model.vo.activity.request.MerchantActivityPlatformPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.PfActivityGoodsPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.McActivityGoodsPageResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.MerchantActivityDetailResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.PlatformMerchantActivityPageResp;

/**
 * 平台后台 - 商家活动服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/17
 */
public interface IPlatformMerchantActivityService {

    /**
     * 营销活动查询 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PlatformMerchantActivityPageResp> pageSummary(UserLoginCacheDTO loginUser, MerchantActivityPlatformPageDataReq pageVO);

    /**
     * 查询商家活动
     * @param request 接口参数
     * @return 返回结果
     */
    MerchantActivityDetailResp detail(UserLoginCacheDTO loginUser, CommonIdReq request);

    /**
     * 营销活动查询 - 活动详情 - 活动商品(分页)
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<McActivityGoodsPageResp> pageByActivityGoods(UserLoginCacheDTO loginUser, PfActivityGoodsPageDataReq pageVO);

}
