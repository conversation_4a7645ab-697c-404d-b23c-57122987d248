package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商家活动列表返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantActivityExamPageResp extends MerchantActivityPageResp {

    private static final long serialVersionUID = 4449929003296212121L;

    /**
     * 审核按钮
     */
    private boolean exam;
}
