package com.ssy.lingxi.marketing.model.vo.coupon.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 平台优惠券请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ActivityPlatformCouponDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = -5700306539711267911L;

    /**
     * 优惠券id
     */
    private Long id;

    /**
     * 优惠券名称
     */
    private String name;

}
