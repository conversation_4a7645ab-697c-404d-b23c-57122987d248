package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  商品-附属(赠品/换购/套餐商品)信息-组明细-接口参数VO
 * <AUTHOR> yzc
 * @since 2021/6/21
 * @version 2.0.0
 */
@Getter
@Setter
public class ActivityGoodsSubsidiaryGroupDetailsReq implements Serializable {
    private static final long serialVersionUID = 5936761420534102471L;

    /**
     * id
     */
    private Long id;

    /**
     * 商品id
     */
    @NotNull(message = "商品id必填")
    @Range(min = 1, message = "商品id必须大于0")
    private Long productId;

    /**
     * skuId
     */
    @NotNull(message = "skuId必填")
    @Range(min = 1, message = "skuId必须大于0")
    private Long skuId;

    /**
     * 商品名称
     */
    @NotNull(message = "商品名称必填")
    private String productName;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品价格
     */
    private BigDecimal price;

    /**
     * 换购价格
     */
    private BigDecimal swapPrice;

    /**
     * 允许换购数量/赠送数量/搭配数量
     */
    @NotNull(message = "允许换购数量/赠送数量/搭配数量必填")
    @Range(min = 1, message = "数量必须大于0")
    private BigDecimal num;

    /**
     * 赠品主图
     */
    private String productImgUrl;
}
