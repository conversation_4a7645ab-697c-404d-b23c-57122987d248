package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.ActivityMemberLevelDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 活动 - 适用会员等级-数据仓库
 * <AUTHOR>
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface ActivityMemberLevelRepository extends JpaRepository<ActivityMemberLevelDO, Long>, JpaSpecificationExecutor<ActivityMemberLevelDO> {

    /**
     * 根据活动删除
     * @param belongType 所属类型
     * @param id 活动id
     */
    void deleteByBelongTypeAndActivityId(Integer belongType, Long id);

    /**
     * 根据活动删除
     * @param belongType 所属类型
     * @param ids 活动id
     */
    void deleteByBelongTypeAndActivityIdIn(Integer belongType, List<Long> ids);

    /**
     * 根据活动查询
     * @param belongType 所属类型
     * @param id 活动id
     * @return 返回结果
     */
    List<ActivityMemberLevelDO> findByBelongTypeAndActivityId(Integer belongType, Long id);
}
