package com.ssy.lingxi.marketing.constant;

import com.ssy.lingxi.common.constant.RedisConstant;

import java.io.Serializable;

/**
 * 营销活动，redis key获取
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/22
 */
public class MarketingRedisKeyConstant implements Serializable {
    private static final long serialVersionUID = 3688713581188204844L;

    //限购缓存过期时长 4个月(活动最大有效期3个月)
    public static final Long CACHE_EXPIRED=3600*24*120L;
    //临时缓存
    public static final Long TEMP_EXPIRED=8*60*60L;

    private static String buildSalesPrefix(Integer belongType,Long activityId,Long skuId){
        return RedisConstant.REDIS_KEY_MARKETING_ACTIVITY_GOODS_SKU_SALES_STATISTICS_PREFIX
                + ":" + belongType
                + ":"+activityId
                +((skuId==null)?"":(":"+skuId));
    }

    /**
     * 获取商品总销量key
     * 注：使用buildSalesPrefix，可以一次搜索出同一活动下的所有sku的销量与客户参与数
     * @param belongType
     * @param activityId
     * @param skuId
     * @return
     */
    public static String getActivitySkuSalesKey(Integer belongType,Long activityId,Long skuId){
        return buildSalesPrefix(belongType,activityId,skuId)+":"+RedisConstant.REDIS_KEY_MARKETING_SKU_SALES;
    }

    /**
     * 活动商品参与客户数
     * 注：使用buildSalesPrefix，可以一次搜索出同一活动下的所有sku的销量与客户参与数
     * @param belongType
     * @param activityId
     * @param skuId
     * @return
     */
    public static String getActivitySkuCustomerNumKey(Integer belongType,Long activityId,Long skuId){
        return buildSalesPrefix(belongType,activityId,skuId)+":"+RedisConstant.REDIS_KEY_MARKETING_CUSTOMER_NUM;
    }

    /**
     * 活动参与客户数
     * 注：使用buildSalesPrefix，可以一次搜索出同一活动下的所有sku的销量与客户参与数
     * @param belongType
     * @param activityId
     * @return
     */
    /*由活动执行方法替代
    public static String getActivityCustomerNumKey(Integer belongType,Long activityId){
        return buildSalesPrefix(belongType,activityId,null)+":"+RedisConstant.REDIS_KEY_MARKETING_CUSTOMER_NUM;
    }*/

    /**
     * 活动商品会员已购数量
     * @param belongType
     * @param activityId
     * @return
     */
    public static String getActivitySkuMemberBuyNumKey(Integer belongType,Long activityId){
        return RedisConstant.REDIS_KEY_MARKETING_ACTIVITY_GOODS_MEMBER_BUY_NUM_PREFIX + ":"+belongType+ ":" + activityId;
    }

    /**
     * 活动订单key (field=skuId)
     * @param orderNo
     * @return
     */
    public static String getOrderSkuJoinActivitiesKey(String orderNo){
        return RedisConstant.REDIS_KEY_MARKETING_ACTIVITY_GOODS_SKU_SALES_STATISTICS_PREFIX + ":"+orderNo;
    }

    /**
     * 销量添加key
     * @param belongType
     * @param activityId
     * @param skuId
     * @return
     */
    public static String getActivityGoodsSalesAddKey(Integer belongType,Long activityId,Long skuId){
        return RedisConstant.REDIS_KEY_MARKETING_ACTIVITY_GOODS_SKU_SALES_ADD_PREFIX
                + ":" + belongType
                + ":"+activityId
                + ":"+skuId;
    }

    /**
     * 拼团时效key
     * @param groupPurchaseRecordId
     * @return
     */
    public static String getGroupPurchaseValidTimeKey(Long groupPurchaseRecordId){
        return RedisConstant.REDIS_KEY_MARKETING_ACTIVITY_GOODS_GP_PREFIX
                + ":" + groupPurchaseRecordId;
    }


    /**********************************活动执行****************************************/
    private static String buildExecutePrefix(String bussinessType,Object... objects){
        String key=RedisConstant.REDIS_KEY_MARKETING_ACTIVITY_EXECUTE_PREFIX
                + ":" + bussinessType;
        if(objects != null){
            for (Object obj:objects) {
                if(obj!=null){
                    key+=":"+obj;
                }
            }
        }
        return key;
    }

    /**
     * 活动执行-汇总-活动客户参与数
     * key=执行前缀+业务类型+活动类型+活动id
     * val=会员id+角色id
     * 数据结构 Set
     * @return
     */
    public static String getExecuteCustomerNumKey(Integer belongType,Long activityId){
        return buildExecutePrefix(RedisConstant.REDIS_KEY_MARKETING_ACTIVITY_EXECUTE_CUSTOMER_COUNT,belongType,activityId);
    }

    /**
     * 活动执行-汇总-活动[已执行订单单数][已执行订单金额]前缀
     * key=执行前缀+order+活动类型+活动id  field=order_count/order_amount
     * 数据结构 Hash
     * @return
     */
    public static String getExecuteOrderKeyPrefix(Integer belongType,Long activityId){
        return buildExecutePrefix("order",belongType,activityId);
    }

    /**
     * 活动执行-汇总-动客户参与数（商户参与平台活动）
     * key=执行前缀+业务类型+活动类型+活动id+会员id+角色id
     * val=会员id+角色id
     * 数据结构 Set
     * @param belongType
     * @param activityId
     * @param memberId 平台活动供应商
     * @param roleId
     * @return
     */
    public static String getPfExecuteCustomerNumKey(Integer belongType,Long activityId,Long memberId,Long roleId){
        return buildExecutePrefix(RedisConstant.REDIS_KEY_MARKETING_ACTIVITY_EXECUTE_CUSTOMER_COUNT,belongType,activityId,memberId,roleId);
    }

    /**
     * 活动执行-汇总-活动[已执行订单单数][已执行订单金额]前缀（商户参与平台活动）
     * key=执行前缀+order+活动类型+活动id+会员id+角色id  field=order_count/order_amount
     * 数据结构 Hash
     * @param belongType
     * @param activityId
     * @param memberId 平台活动供应商
     * @param roleId
     * @return
     */
    public static String getPfExecuteOrderKeyPrefix(Integer belongType,Long activityId,Long memberId,Long roleId){
        return buildExecutePrefix("order",belongType,activityId,memberId,roleId);
    }

    public static String buildField(Object... objects){
        String key="";
        if(objects != null){
            for (Object obj:objects) {
                if(obj!=null){
                    key+=":"+obj;
                }
            }
        }
        return key;
    }

    public static String getTempKeyPrefix(String moudle,Object... objects){
        return "temp:"+moudle+buildField(objects);
    }

    /**
     * 活动列表缓存key
     * @return
     */
    public static String getActivityListCacheKey(){
        return "activity:list:all";
    }
}
