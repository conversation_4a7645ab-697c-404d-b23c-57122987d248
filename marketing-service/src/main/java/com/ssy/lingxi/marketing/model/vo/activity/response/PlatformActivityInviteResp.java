package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 平台活动 - 被邀请的会员 - DTO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Data
public class PlatformActivityInviteResp implements Serializable {

    private static final long serialVersionUID = -7535431057439500546L;
    /**
     * id
     */
    private Long id;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员名称
     * */
    private String memberName;
    /**
     * 会员角色id
     */
    private Long roleId;

    /**
     * 会员角色名称
     * */
    private String roleName;
    /**
     * 会员等级
     */
    private Integer level;

    /**
     * 会员等级名称
     * */
    private String levelName;


}
