package com.ssy.lingxi.marketing.model.vo.activity.request;

import cn.hutool.json.JSONObject;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 会员活动 - 新增 - DTO
 * <AUTHOR>
 * @since 2021/06/17
 * @version 2.0.0
 */
@Data
public class MbActivityAddReq {

    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     * */
    @NotNull(message = "活动类型不能为空")
    private Integer activityType;

    /**
     * 活动名称
     * */
    @NotBlank(message = "活动名称不能为空")
    private String activityName;

    /**
     * 活动开始时间
     * */
    @NotNull(message = "活动开始时间不能为空")
    private Long startTime;

    /**
     * 活动结束时间
     * */
    @NotNull(message = "活动结束时间不能为空")
    private Long endTime;

    /**
     * 新用户(不包含会员) 0-否 1-是
     * */
    private Integer newUser = 0;

    /**
     * 老用户(不包含会员) 0-否 1-是
     * */
    private Integer oldUser = 0;

    /**
     * 新会员(仅会员用户) 0-否 1-是
     * */
    private Integer newMember = 0;

    /**
     * 老会员(仅会员用户) 0-否 1-是
     * */
    private Integer oldMember = 0;

    /**
     * 会员等级ID集合
     * */
    private List<Long> memberLevelIdList;

    /**
     * 商城ID集合
     * */
    private List<Long> shopIdList;

    /**
     * 活动
     * */
    private JSONObject activityDefined;
}
