package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 平台活动待审核报名返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformActivitySignUpDetailResp extends BasePlatformActivityDetailResp {

    private static final long serialVersionUID = -517031506594871700L;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 会员报名时间
     */
    private Long signUpTime;
}
