package com.ssy.lingxi.marketing.controller.activity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityExecutePageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.MerchantActivityCommonPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.PfActivityGoodsPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.PfActivitySignUpPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.*;
import com.ssy.lingxi.marketing.service.IActivityExecuteService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 营销能力 - 营销活动执行
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/23
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/ability/activity/execute")
public class MerchantActivityExecuteController extends BaseController {

    @Resource
    private IActivityExecuteService activityExecuteService;

    /**
     * 商家营销活动执行 - 分页列表
     */
    @GetMapping("/merchant/page")
    public WrapperResp<PageDataResp<MerchantActivityExecutePageResp>> pageMerchant(@Valid MerchantActivityCommonPageDataReq pageReq) {
        return WrapperUtil.success(activityExecuteService.pageMerchant(getSysUser(), pageReq));
    }

    /**
     * 商家营销活动执行 - 活动详情
     */
    @GetMapping("/merchant/detail")
    public WrapperResp<MerchantActivityDetailResp> detailMerchant(@Valid CommonIdReq req) {
        return WrapperUtil.success(activityExecuteService.detailMerchant(getSysUser(), req));
    }

    /**
     * 商家营销活动执行 - 活动详情 - 活动商品(分页)
     */
    @GetMapping("/merchant/detail/goods/page")
    public WrapperResp<PageDataResp<ActivityExecuteGoodsPageResp>> pageByActivityGoodsMerchant(@Valid PfActivityGoodsPageDataReq req) {
        return WrapperUtil.success(activityExecuteService.pageByActivityGoodsMerchant(getSysUser(), req));
    }

    /**
     * 商家营销活动执行 - 活动详情 - 活动商品-执行明细(分页)
     */
    @GetMapping("/merchant/detail/goods/execute/detail/page")
    public WrapperResp<PageDataResp<ActivityExecuteDetailPageResp>> pageMerchantExecuteDetailOfAbility(@Valid ActivityExecutePageDataReq req) {
        req.setBelongType(BelongTypeEnum.MERCHANT.getCode());
        return WrapperUtil.success(activityExecuteService.pageMerchantExecuteDetailOfAbility(getSysUser(), req));
    }

    /**
     * 平台营销活动执行 - 分页列表
     */
    @GetMapping("/platform/page")
    public WrapperResp<PageDataResp<PlatformActivityExecuteAbilityPageResp>> pagePlatformOfAbility(@Valid PfActivitySignUpPageDataReq pageReq) {
        return WrapperUtil.success(activityExecuteService.pagePlatformOfAbility(getSysUser(), pageReq));
    }

    /**
     * 平台营销活动执行 - 活动详情
     */
    @GetMapping(value = "/platform/detail")
    public WrapperResp<PlatformActivityDetailResp> detailPlatformOfAbility(CommonIdReq req) {
        return WrapperUtil.success(activityExecuteService.detailPlatformOfAbility(getSysUser(), req));
    }

    /**
     * 平台营销活动执行 - 活动详情 - 活动商品(分页)
     */
    @GetMapping("/platform/detail/goods/page")
    public WrapperResp<PageDataResp<ActivityExecuteGoodsPageResp>> pageByActivityGoodsPlatformOfAbility(@Valid PfActivityGoodsPageDataReq req) {
        return WrapperUtil.success(activityExecuteService.pageByActivityGoodsPlatformOfAbility(getSysUser(), req));
    }

    /**
     * 平台营销活动执行 - 活动详情 - 活动商品-执行明细(分页)
     */
    @GetMapping("/platform/detail/goods/execute/detail/page")
    public WrapperResp<PageDataResp<ActivityExecuteDetailPageResp>> pagePlatformExecuteDetailOfAbility(@Valid ActivityExecutePageDataReq req) {
        req.setBelongType(BelongTypeEnum.PLATFORM.getCode());
        return WrapperUtil.success(activityExecuteService.pageMerchantExecuteDetailOfAbility(getSysUser(), req));
    }
}
