package com.ssy.lingxi.marketing.model.dto;

import java.io.Serializable;

/**
 * 营销服务 - 订单拼团状态通知DTO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-11-29
 */
public class OrderGroupDTO implements Serializable {

    private static final long serialVersionUID = 5898035687734441206L;
    /**
     * 订单Id
     */
    private Long orderId;

    /**
     * 拼团状态，0-拼团失败，1-拼团成功
     */
    private Integer status;

    /**
     * 拼团id
     */
    private Long groupPurchaseId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getGroupPurchaseId() {
        return groupPurchaseId;
    }

    public void setGroupPurchaseId(Long groupPurchaseId) {
        this.groupPurchaseId = groupPurchaseId;
    }
}
