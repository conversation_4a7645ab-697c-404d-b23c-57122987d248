package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 附属商品[赠商品或优惠券]dto
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/16
 */
@Getter
@Setter
public class ActivityExecutePageDTO implements Serializable {


    private static final long serialVersionUID = 4728212585080465285L;

    /**
     * 单据类型 1-订单； 2-退货申请单 ActivityRecordTypeEnum
     */
    private Integer recordType;
    /**
     * 所属类型 1-平台 2-商家
     */
    private Integer belongType;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 商城id
     */
    private Long shopId;
    /**
     * 订单/退货申请单号
     */
    private String orderNo;
    /**
     * 客户名称
     */
    private String memberName;

    /**
     * 活动开始时间
     * */
    private LocalDate startTime;

    /**
     * 活动结束时间
     * */
    private LocalDate endTime;
}
