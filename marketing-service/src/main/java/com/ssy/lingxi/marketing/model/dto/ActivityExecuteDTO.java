package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 附属商品[赠商品或优惠券]dto
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/16
 */
@Getter
@Setter
public class ActivityExecuteDTO implements Serializable {

    private static final long serialVersionUID = 8757217116780223556L;
    /**
     * 单据类型 1-订单； 2-退货申请单 ActivityRecordTypeEnum
     */
    private Integer recordType;
    /**
     * 单据类型 1-订单； 2-退货申请单 ActivityRecordTypeEnum
     */
    private String recordTypeName;
    /**
     * 所属类型 1-平台 2-商家
     */
    private Integer belongType;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 商城名称
     */
    private String shopName;
    /**
     * 订单/退货申请单id
     */
    private Long orderId;
    /**
     * 订单/退货申请单号
     */
    private String orderNo;
    /**
     * 采购会员id
     */
    private Long memberId;
    /**
     * 采购角色id
     */
    private Long roleId;
    /**
     * 客户名称
     */
    private String memberName;
    /**
     * 单据时间（来源订单或售后退货）
     * */
    private Long orderTime;
    /**
     * 购买数量/退货数量
     */
    private BigDecimal quantity;
    /**
     * 实购/退款金额（订单商品购买价*数量）
     */
    private BigDecimal amount;
}
