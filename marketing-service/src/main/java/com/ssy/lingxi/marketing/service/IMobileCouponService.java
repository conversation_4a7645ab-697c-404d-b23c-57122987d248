package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.api.model.request.GiftCouponReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.*;
import com.ssy.lingxi.marketing.model.vo.coupon.response.*;

import java.util.List;

/**
 * App优惠券服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/13
 */
public interface IMobileCouponService {

    /**
     * 我的优惠券 - 数量统计
     * @param loginUser 登录用户信息
     * @param request 请求参数
     * @return 返回结果
     */
    MobileCouponDetailCountResp getCouponCount(UserLoginCacheDTO loginUser, MobileCouponDetailCountReq request);

    /**
     * 我的优惠券 - 分页列表
     * @param loginUser 登录用户信息
     * @param request 接口参数
     * @return 返回结果
     */
    PageDataResp<MobileCouponDetailResp> pageCouponDetail(UserLoginCacheDTO loginUser, MobileCouponPageDataReq request);
    /**
     * 我的优惠券 - 分页列表(web)
     * @param loginUser 登录用户信息
     * @param request 接口参数
     * @return 返回结果
     */
    PageDataResp<WebCouponDetailResp> pageCouponDetailByWeb(UserLoginCacheDTO loginUser, WebCouponPageDataReq request);

    /**
     * 提交订单 - 选择的优惠券列表
     * @param loginUser 登录用户信息
     * @param request 请求参数
     * @return 返回结果
     */
    List<MobileCouponDetailCanUseResp> listOrderCouponDetail(UserLoginCacheDTO loginUser, MobileGoodsCartReq request);

    /**
     * 进货单 - 店铺优惠券列表
     * @param loginUser 登录用户信息
     * @param request 接口参数
     * @return 返回结果
     */
    List<MobileCouponResp> listShopCoupon(UserLoginCacheDTO loginUser, MobileShopCouponListReq request);

    /**
     * 活动页 - 自动领取优惠券
     * @param loginUser 登录用户信息
     * @param request 请求参数
     * @return 返回结果
     */
    List<MobileCouponDetailResp> autoReceiveCoupon(UserLoginCacheDTO loginUser, AutoReceiveCouponReq request);

    /**
     * 活动页 - 领取优惠券
     * @param loginUser 登录用户信息
     * @param request 请求参数
     * @return 返回结果
     */
    ReceiveCouponResp receiveCoupon(UserLoginCacheDTO loginUser, ReceiveCouponReq request);

    /**
     * 消息队列 - 赠品活动赠优惠券
     * @param request 请求参数
     */
    void handleMqMsgReceiveCoupon(GiftCouponReq request);

    /**
     * 查询优惠券所关联的商品信息
     * @param loginUser 登录用户信息
     * @param request 请求参数
     * @return 返回结果
     */
    List<MobileActivityRelationGoodsResp> listMerchantCouponRelationGoods(UserLoginCacheDTO loginUser, MobileCouponGoodsRelationReq request);
    /**
     * 查询优惠券所关联的商品信息（到sku级别）
     * @param loginUser 登录用户信息
     * @param request 请求参数
     * @return 返回结果
     */
    List<MobileActivityRelationGoodsResp> listMerchantCouponRelationGoodsSku(UserLoginCacheDTO loginUser, MobileCouponGoodsRelationReq request);
}
