package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *  分页查询-待审核报名-列表
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformActivityTobeSignUpDataReq extends PlatformActivityCommonPageDataReq {

    private static final long serialVersionUID = 8063501926061527824L;

    /**
     * 报名会员
     */
    private String  memberName;

}
