package com.ssy.lingxi.marketing.handler.convert;

import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.marketing.model.bo.SuitableCategoryBO;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

/**
 * Jpa 将 Postgresql Json(或Jsonb)字段转换为List<SuitableCategoryBO>
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/7/16
 */
@Converter(autoApply = true)
public class JpaJsonToScBOListConverter implements AttributeConverter<List<SuitableCategoryBO>, String> {

    @Override
    public String convertToDatabaseColumn(List<SuitableCategoryBO> meta) {
        return JsonUtil.toJson(meta);
    }

    @Override
    public List<SuitableCategoryBO> convertToEntityAttribute(String dbData) {
        return JsonUtil.toList(dbData, SuitableCategoryBO.class);
    }
}
