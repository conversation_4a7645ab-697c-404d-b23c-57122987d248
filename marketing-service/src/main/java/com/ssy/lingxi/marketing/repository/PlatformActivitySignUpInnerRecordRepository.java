package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.PlatformActivitySignUpInnerRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 平台活动 - 报名内部流转记录-实体
 * <AUTHOR>
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface PlatformActivitySignUpInnerRecordRepository extends JpaRepository<PlatformActivitySignUpInnerRecordDO, Long>, JpaSpecificationExecutor<PlatformActivitySignUpInnerRecordDO> {

}
