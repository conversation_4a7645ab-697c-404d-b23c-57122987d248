package com.ssy.lingxi.marketing.service;

import cn.hutool.json.JSONObject;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.model.bo.MobileMerchantActivityGoodsCouponBO;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityCardAdornReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.GoodsAreaAdornDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityCardAdornResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.GoodsAreaAdornResp;

import java.util.List;
import java.util.Map;

/**
 * web - 活动服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/09/07
 */
public interface IWebActivityService {

    Map<String, Object> platformActivityPageAdorn(JSONObject req, Long shopId, UserLoginCacheDTO user);

    Map<String, Object> merchantActivityPageAdorn(JSONObject req, Long shopId, UserLoginCacheDTO user);

    List<ActivityCardAdornResp> activityCardAdorn(ActivityCardAdornReq req,Long shopId,UserLoginCacheDTO user);

    List<ActivityCardAdornResp> storeActivityCardAdorn(ActivityCardAdornReq req, Long shopId, UserLoginCacheDTO user);

    List<MobileMerchantActivityGoodsCouponBO> activityCardGiveCouponAdorn(UserLoginCacheDTO user, Long shopId, ActivityCardAdornReq req);

    PageDataResp<GoodsAreaAdornResp> goodsAreaAdorn(GoodsAreaAdornDataReq req);

}
