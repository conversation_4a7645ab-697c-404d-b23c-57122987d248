package com.ssy.lingxi.marketing.model.vo.coupon.request;

import com.ssy.lingxi.marketing.model.dto.GoodsDiscountItemDTO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 批量活动标签请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/1
 */
@Getter
@Setter
public class MobileGoodsBatchRelationTagReq implements Serializable {

    private static final long serialVersionUID = -1284106139690376922L;

    /**
     * 商城id
     */
    @NotNull(message = "商城id不能为空")
    private Long shopId;

    /**
     * 店铺会员id
     */
    @NotNull(message = "店铺会员id不能为空")
    private Long memberId;

    /**
     * 商品详细信息列表（用于优惠计算）
     */
    @Valid
    private List<GoodsDiscountItemDTO> goodsItems;

}