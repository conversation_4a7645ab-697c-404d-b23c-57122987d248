package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.api.model.request.*;
import com.ssy.lingxi.marketing.api.model.response.GroupPurchaseDetailResp;
import com.ssy.lingxi.marketing.api.model.response.GroupPurchaseListResp;
import com.ssy.lingxi.marketing.api.model.response.GroupPurchaseShareDetailResp;
import com.ssy.lingxi.marketing.api.model.response.OrderListGroupPurchaseResp;

import java.util.List;

/**
 * 活动订单相关
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/2
 */
public interface IActivityOrderService {

    /**
     * 查询拼团列表(状态:拼团中)
     */
    PageDataResp<GroupPurchaseListResp> getGroupPurchaseList(UserLoginCacheDTO sysUser, GroupPurchasePageDataReq request);

    /**
     * 查询拼团详情
     */
    GroupPurchaseDetailResp getGroupPurchaseDetail(UserLoginCacheDTO sysUser, GroupPurchaseDetailReq request);

    /**
     * 添加拼团
     */
    Long addGroupPurchase(GroupPurchaseAddReq req);

    /**
     * [拼团超时]更新拼团状态
     */
    void updateGroupPurchaseStatus(long parseLong);

    /**
     * 查询拼团详情（根据订单id）
     */
    GroupPurchaseDetailResp getOrderGroupPurchaseDetail(UserLoginCacheDTO sysUser, OrderGroupPurchaseDetailReq request);

    /**
     * 查询拼团列表信息（订单列表页呈现）
     */
    List<OrderListGroupPurchaseResp> getOrderListGroupPurchase(UserLoginCacheDTO sysUser, OrderListGroupPurchaseReq request);

    /**
     * 校验是否重复参与同一个拼团
     */
    Boolean isRepeatJoinGroupPurchase(GroupPurchaseCheckReq req);

    /**
     * 拼团信息（拼团分享相关信息）
     */
    GroupPurchaseShareDetailResp getOrderGroupPurchaseShareDetail(UserLoginCacheDTO sysUser, OrderGroupPurchaseDetailReq request);
}
