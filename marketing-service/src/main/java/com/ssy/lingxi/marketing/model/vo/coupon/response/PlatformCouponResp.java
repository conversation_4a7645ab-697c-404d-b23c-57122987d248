package com.ssy.lingxi.marketing.model.vo.coupon.response;

import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.marketing.model.vo.common.response.TaskStepResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 平台优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformCouponResp extends PlatformCouponBaseResp {

    private static final long serialVersionUID = 7428165125540508752L;

    /**
     * 适用商品
     */
    private List<SuitablePlatformCommoditySkuResp> suitableCommoditySkuList;

    /**
     * 适用新老会员 1-新会员(平台会员) 2-老会员(平台会员)
     * 当天平台审核通过的平台会员为新会员，非当天审核通过的平台会员为老会员
     */
    private List<SelectItemResp> suitableMemberTypes;

    /**
     * 适用会员类型 1-企业会员 2-个人会员
     *
     */
    private List<SelectItemResp> memberTypes;

    /**
     * 适用会员等级(会员等级id)
     * 当适用用户(会员) 类型有勾选新会员、老会员选型,该字段作用于这些数据
     */
    private List<SuitableMemberLevelResp> suitableMemberLevelTypes;

    /**
     * 适用商城
     */
    private List<SuitableMallResp> suitableMallTypes;

    /**
     * 流程流转
     */
    private List<TaskStepResp> taskSteps;

    /**
     * 单据流转记录
     */
    private List<PlatformCouponHistoryResp> history;
}
