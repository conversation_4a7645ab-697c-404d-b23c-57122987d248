package com.ssy.lingxi.marketing.serviceImpl.base.coupon;

import com.ssy.lingxi.commodity.api.model.resp.shop.ShopDetailResp;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberLevelTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.marketing.entity.coupon.CouponMemberLevelDO;
import com.ssy.lingxi.marketing.entity.coupon.CouponShopDO;
import com.ssy.lingxi.marketing.model.vo.coupon.response.SuitableMallResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.SuitableMemberLevelResp;
import com.ssy.lingxi.marketing.repository.CouponMemberLevelRepository;
import com.ssy.lingxi.marketing.repository.CouponShopRepository;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLevelConfigResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 基础优惠券规则服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/16
 */
@Slf4j
@Service
public class BaseCouponRuleService {

    @Resource
    private CouponMemberLevelRepository memberLevelRepository;
    @Resource
    private CouponShopRepository shopRepository;

    /**
     * 查询适用会员等级
     * @param belongType 所属类型
     * @param couponId 优惠券id
     */
    public List<SuitableMemberLevelResp> listMemberLevel(BelongTypeEnum belongType, Long couponId) {
        // 适用会员等级
        List<CouponMemberLevelDO> couponMemberLevelDOList = memberLevelRepository.findByBelongTypeAndCouponId(belongType.getCode(), couponId);
        return couponMemberLevelDOList.stream().map(e -> {
            SuitableMemberLevelResp suitableMemberLevelResp = new SuitableMemberLevelResp();
            suitableMemberLevelResp.setId(e.getMemberLevelId());
            suitableMemberLevelResp.setMemberType(e.getMemberType());
            suitableMemberLevelResp.setMemberTypeName(Optional.ofNullable(e.getMemberType()).flatMap(v -> Optional.ofNullable(MemberTypeEnum.toEnum(v)).map(MemberTypeEnum::getName)).orElse(e.getMemberTypeName()));
            suitableMemberLevelResp.setRoleType(e.getRoleType());
            suitableMemberLevelResp.setRoleTypeName(Optional.ofNullable(e.getRoleType()).flatMap(v -> Optional.ofNullable(RoleTypeEnum.getName(v))).orElse(e.getRoleTypeName()));
            suitableMemberLevelResp.setRoleId(e.getRoleId());
            suitableMemberLevelResp.setRoleName(e.getRoleName());
            suitableMemberLevelResp.setLevelType(e.getLevelType());
            suitableMemberLevelResp.setMemberLevelTypeName(Optional.ofNullable(e.getLevelType()).flatMap(v -> Optional.ofNullable(MemberLevelTypeEnum.getCodeMsg(v))).orElse(e.getMemberTypeName()));
            suitableMemberLevelResp.setLevel(e.getLevel());
            suitableMemberLevelResp.setLevelTag(e.getLevelTag());
            return suitableMemberLevelResp;
        }).collect(Collectors.toList());
    }

    /**
     * 查询适用会员等级
     * @param belongType 所属类型
     * @param couponId 优惠券id
     */
    public List<Long> listMemberLevelId(BelongTypeEnum belongType, Long couponId) {
        // 适用会员等级
        List<CouponMemberLevelDO> couponMemberLevelDOList = memberLevelRepository.findByBelongTypeAndCouponId(belongType.getCode(), couponId);
        return couponMemberLevelDOList.stream().map(CouponMemberLevelDO::getMemberLevelId).collect(Collectors.toList());
    }

    /**
     * 保存适用会员等级
     * @param belongType 所属类型
     * @param couponId 优惠券id
     * @param memberLevelList 会员等级
     */
    public void saveMemberLevel(BelongTypeEnum belongType, Long couponId, List<MemberFeignLevelConfigResp> memberLevelList) {
        if (CollectionUtils.isEmpty(memberLevelList)) {
            return;
        }
        // 适用会员等级
        List<CouponMemberLevelDO> memberLevelDOList = memberLevelList.stream().map(e -> {
            CouponMemberLevelDO memberLevelDO = new CouponMemberLevelDO();
            memberLevelDO.setCouponId(couponId);
            memberLevelDO.setBelongType(belongType.getCode());
            memberLevelDO.setMemberLevelId(e.getId());
            memberLevelDO.setMemberType(e.getMemberType());
            memberLevelDO.setMemberTypeName(e.getMemberTypeName());
            memberLevelDO.setRoleId(e.getRoleId());
            memberLevelDO.setRoleName(e.getRoleName());
            memberLevelDO.setRoleType(e.getRoleType());
            memberLevelDO.setRoleTypeName(e.getRoleTypeName());
            memberLevelDO.setLevel(e.getLevel());
            memberLevelDO.setLevelType(e.getMemberLevelType());
            memberLevelDO.setLevelTypeName(e.getMemberLevelTypeName());
            memberLevelDO.setLevelTag(e.getLevelTag());
            return memberLevelDO;
        }).collect(Collectors.toList());
        memberLevelRepository.saveAll(memberLevelDOList);
    }

    /**
     * 根据优惠券查询使用会员等级, 存在则删除
     * @param belongType 所属类型
     * @param couponId 优惠券id
     */
    public void existsAndDeleteMemberLevel(BelongTypeEnum belongType, Long couponId) {
        if (memberLevelRepository.existsByBelongTypeAndCouponId(belongType.getCode(), couponId)) {
            memberLevelRepository.deleteByBelongTypeAndCouponId(belongType.getCode(), couponId);
        }
    }

    /**
     * 查询适用商城
     * @param belongType 所属类型
     * @param couponId 优惠券id
     */
    public List<SuitableMallResp> listShop(BelongTypeEnum belongType, Long couponId) {
        List<CouponShopDO> couponShopDOList = shopRepository.findByBelongTypeAndCouponId(belongType.getCode(), couponId);
        return couponShopDOList.stream().map(e -> {
            SuitableMallResp suitableMallResp = new SuitableMallResp();
            suitableMallResp.setId(e.getShopId());
            suitableMallResp.setName(e.getShopName());
            suitableMallResp.setLogoUrl(e.getLogo());
            return suitableMallResp;
        }).collect(Collectors.toList());
    }

    /**
     * 保存适用商城
     * @param belongType 所属类型
     * @param couponId 优惠券id
     * @param shopList 商城
     */
    public void saveShop(BelongTypeEnum belongType, Long couponId, List<ShopDetailResp> shopList) {
        if (CollectionUtils.isEmpty(shopList)) {
            return;
        }
        List<CouponShopDO> shopDOList = shopList.stream().map(e -> {
            CouponShopDO memberLevelDO = new CouponShopDO();
            memberLevelDO.setCouponId(couponId);
            memberLevelDO.setBelongType(belongType.getCode());
            memberLevelDO.setShopId(e.getId());
            memberLevelDO.setShopName(e.getName());
            memberLevelDO.setLogo(e.getLogoUrl());
            memberLevelDO.setEnvironment(e.getEnvironment());
            return memberLevelDO;
        }).collect(Collectors.toList());
        shopRepository.saveAll(shopDOList);
    }

    /**
     * 根据优惠券删除商城
     * @param belongType 所属类型
     * @param couponId 优惠券id
     */
    public void deleteShop(BelongTypeEnum belongType, Long couponId) {
        shopRepository.deleteByBelongTypeAndCouponId(belongType.getCode(), couponId);
    }
}