package com.ssy.lingxi.marketing.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.api.model.request.GroupPurchaseDetailReq;
import com.ssy.lingxi.marketing.api.model.request.GroupPurchasePageDataReq;
import com.ssy.lingxi.marketing.api.model.request.OrderGroupPurchaseDetailReq;
import com.ssy.lingxi.marketing.api.model.request.OrderListGroupPurchaseReq;
import com.ssy.lingxi.marketing.api.model.response.GroupPurchaseDetailResp;
import com.ssy.lingxi.marketing.api.model.response.GroupPurchaseListResp;
import com.ssy.lingxi.marketing.api.model.response.GroupPurchaseShareDetailResp;
import com.ssy.lingxi.marketing.api.model.response.OrderListGroupPurchaseResp;
import com.ssy.lingxi.marketing.service.IActivityOrderService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Web - 活动订单(拼团)
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/23
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/web/activity/order")
public class WebActivityOrderController extends BaseController {

    @Resource
    private IActivityOrderService activityOrderService;
    /**
     * 拼团列表
     **/
    @PostMapping("/group/purchase/list")
    public WrapperResp<PageDataResp<GroupPurchaseListResp>> getGroupPurchaseList(@Valid @RequestBody GroupPurchasePageDataReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(activityOrderService.getGroupPurchaseList(loginUser, request));
    }

    /**
     * 拼团详情(拼团id)
     **/
    @PostMapping("/group/purchase/detail")
    public WrapperResp<GroupPurchaseDetailResp> getGroupPurchaseDetail(@Valid @RequestBody GroupPurchaseDetailReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(activityOrderService.getGroupPurchaseDetail(loginUser, request));
    }

    /**
     * 拼团详情（根据订单id）
     **/
    @PostMapping("/order/group/purchase/detail")
    public WrapperResp<GroupPurchaseDetailResp> getOrderGroupPurchaseDetail(@Valid @RequestBody OrderGroupPurchaseDetailReq request) {
        return WrapperUtil.success(activityOrderService.getOrderGroupPurchaseDetail(getSysUser(), request));
    }

    /**
     * 订单列表拼团信息（根据订单ids）
     **/
    @PostMapping("/order/list/group/purchase")
    public WrapperResp<List<OrderListGroupPurchaseResp>> getOrderListGroupPurchase(@Valid @RequestBody OrderListGroupPurchaseReq request) {
        return WrapperUtil.success(activityOrderService.getOrderListGroupPurchase(getSysUser(), request));
    }

    /**
     * 拼团分享信息查询（根据订单id）
     **/
    @PostMapping("/order/group/purchase/share/detail")
    public WrapperResp<GroupPurchaseShareDetailResp> getOrderGroupPurchaseShareDetail(@Valid @RequestBody OrderGroupPurchaseDetailReq request) {
        return WrapperUtil.success(activityOrderService.getOrderGroupPurchaseShareDetail(getSysUser(), request));
    }
}
