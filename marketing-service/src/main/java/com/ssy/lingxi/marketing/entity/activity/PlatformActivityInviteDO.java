package com.ssy.lingxi.marketing.entity.activity;


import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 *  平台活动邀请表-DO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_activity_invite",
        indexes = {@Index(name=TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_activity_invite_activity_id_idx", columnList = "activity_id")})
public class PlatformActivityInviteDO implements Serializable {

    private static final long serialVersionUID = 6884681381453153257L;

    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 多对一双向关联活动
     */
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "activity_id", referencedColumnName = "id")
    private PlatformActivityDO platformActivity;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 会员名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String memberName;

    /**
     * 会员角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 会员角色名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String roleName;

    /**
     * 会员等级
     */
    @Column(columnDefinition = "int4")
    private Integer level;

    /**
     * 会员等级名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String levelName;
}
