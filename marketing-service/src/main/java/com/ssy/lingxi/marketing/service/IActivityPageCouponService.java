package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ActivityPageCouponDetailReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ActivityPageMerchantCouponDataReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityPageCouponDetailResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityPageCouponPageResp;

import java.util.List;

/**
 * 平台、商家活动页优惠券服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/13
 */
public interface IActivityPageCouponService {
    /**
     * 平台营销活动页 - 选择平台优惠券
     * @return 返回结果
     */
    PageDataResp<ActivityPageCouponPageResp> pagePlatformActivityPagePlatformCoupon(ActivityPageMerchantCouponDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台营销活动页 - 选择商家优惠券
     * @return 返回结果
     */
    PageDataResp<ActivityPageCouponPageResp> pagePlatformActivityPageMerchantCoupon(ActivityPageMerchantCouponDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家营销活动页 - 选择商家优惠券
     * @return 返回结果
     */
    PageDataResp<ActivityPageCouponPageResp> pageMerchantActivityPageMerchantCoupon(ActivityPageMerchantCouponDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 平台活动页 - 优惠券详情
     * @return 返回结果
     */
    List<ActivityPageCouponDetailResp> listPlatformActivityPageCouponDetail(ActivityPageCouponDetailReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家活动页 - 优惠券详情
     * @return 返回结果
     */
    List<ActivityPageCouponDetailResp> listMerchantActivityPageCouponDetail(ActivityPageCouponDetailReq request, UserLoginCacheDTO loginUser);
}
