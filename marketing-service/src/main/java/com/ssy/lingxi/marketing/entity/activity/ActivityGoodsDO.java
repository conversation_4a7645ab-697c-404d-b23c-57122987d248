package com.ssy.lingxi.marketing.entity.activity;


import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *  平台活动[会员]商品表-DO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_goods",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_goods_activity_id_idx", columnList = "activityId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_goods_belong_type_idx", columnList = "belongType")})
public class ActivityGoodsDO implements Serializable {

    private static final long serialVersionUID = -202680548874384824L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 活动id
     */
    @Column
    private Long activityId;

    /**
     * 所属类型 1-平台 2-商家
     */
    @Column
    private Integer belongType;

    /**
     * 平台活动报名id
     */
    @Column(columnDefinition = "int8")
    private Long signUpId;

    /**
     * 平台审核状态 0-未审核 1-审核不通过 2-审核通过
     * 商家活动(简要流程[具体看流程图]): 商家新增提交活动 -> 平台审核 -> 商家活动上线
     * 平台活动(简要流程[具体看流程图]): 平台新增提交活动 -> 报名开始 -> 商家报名-> 平台审核 -> 报名结束 -> 平台活动上线
     */
    @Column
    private Integer auditStatus;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 会员名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String memberName;

    /**
     * 会员角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 会员角色名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String roleName;

    /**
     * 会员类型
     * @see MemberTypeEnum
     */
    @Column
    private Integer memberType;

    /**
     * 商品id
     */
    @Column(columnDefinition = "int8")
    private Long productId;

    /**
     * skuId
     */
    @Column(columnDefinition = "int8")
    private Long skuId;

    /**
     * 商品名称
     */
    @Column(columnDefinition = "varchar(128)")
    private String productName;

    /**
     * 规格
     */
    @Column(columnDefinition = "varchar(128)")
    private String type;

    /**
     * 品类
     */
    @Column(columnDefinition = "varchar(128)")
    private String category;

    /**
     * 品牌
     */
    @Column(columnDefinition = "varchar(128)")
    private String brand;

    /**
     * 单位
     */
    @Column(columnDefinition = "varchar(128)")
    private String unit;

    /**
     * 商品价格
     * */
    @Column(columnDefinition = "numeric")
    private BigDecimal price;

    /**
     * 预售价格
     * */
    @Column(columnDefinition = "numeric")
    private BigDecimal preSelPrice;

    /**
     * 直降价格/起始价格
     * */
    @Column(columnDefinition = "numeric")
    private BigDecimal plummetPrice;

    /**
     * 活动价格/团购价格/秒杀价格/单位定金/砍价底价
     * */
    @Column(columnDefinition = "numeric")
    private BigDecimal activityPrice;

    /**
     * 定金抵扣单价
     * */
    @Column(columnDefinition = "numeric")
    private BigDecimal deductionPrice;

    /**
     * 折扣（如85折，输入85，9折输入90）
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal discount;

    /**
     * 个人限购数量
     */
    @Column
    private Integer restrictNum;

    /**
     * 活动限购总数量
     * */
    @Column
    private Integer restrictTotalNum;

    /**
     * 销量
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal salesNum;
    /**
     * 退货数量
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal refundNum;
    /**
     * 销量金额
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal salesAmount;
    /**
     * 退货金额
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal refundAmount;

    /**
     * 商品主图
     */
    @Column(columnDefinition = "varchar(250)")
    private String productImgUrl;

    /**
     * 赠送促销类型：1-满额赠 2-买商品赠
     * */
    @Column(columnDefinition = "int2")
    private Integer giveType;

    /**
     * 附属商品信息(仅赠送促销、换购、套餐)
     */
    @JsonManagedReference
    @OneToMany(mappedBy = "activityGoods", cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    private List<ActivityGoodsSubsidiaryDO> goodsSubsidiaryList;

    /**
     * 附属优惠券信息（仅赠送促销）
     */
    @JsonManagedReference
    @OneToMany(mappedBy = "activityGoods", cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    private List<ActivityGoodsCouponDO> goodsCouponList;

    /**
     * 基础工费
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal baseLaborFee;

    /**
     * 附加工费
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal additionalLaborFee;

    /**
     * 件工费
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal pieceLaborFee;

}
