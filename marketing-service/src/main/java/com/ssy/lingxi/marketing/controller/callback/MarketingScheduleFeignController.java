package com.ssy.lingxi.marketing.controller.callback;

import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.constant.MarketingConstant;
import com.ssy.lingxi.marketing.enums.TaskTypeEnum;
import com.ssy.lingxi.marketing.service.IMarketingScheduleCallBackService;
import com.ssy.lingxi.scheduler.api.model.req.ScheduleTaskCallbackReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;

/**
 *  定时任务回调入口
 * <AUTHOR>
 * @since 2021/7/23
 * @version 2.0.0
 */
@Slf4j
@RestController
public class MarketingScheduleFeignController extends BaseController {

    @Resource
    private IMarketingScheduleCallBackService activityScheduleCallBackService;

    /**
     * 定时任务回调接口
     * */
    @PostMapping(value = MarketingConstant.TASK_CALLBACK_URL)
    public WrapperResp<Void> taskCallback(@RequestBody @Valid ScheduleTaskCallbackReq callbackVO) {
        log.info("MarketingFeignController:taskCallback:callbackVO:{}", JSONUtil.toJsonStr(callbackVO));
        Map<String, Object> taskAttributes = callbackVO.getTaskAttributes();
        if (taskAttributes != null) {
            Integer taskType = (Integer) taskAttributes.get(TaskTypeEnum.Constants.NAME);
            if (TaskTypeEnum.PF_ACTIVITY_SIGN_UP_START.getCode().equals(taskType)) {
                // 启动平台活动报名任务
                activityScheduleCallBackService.startPfActivitySignUpTask(callbackVO);
            } else if (TaskTypeEnum.PF_ACTIVITY_SIGN_UP_END.getCode().equals(taskType)) {
                // 结束平台活动报名任务
                activityScheduleCallBackService.endPfActivitySignUpTask(callbackVO);
            } else if (TaskTypeEnum.PF_ACTIVITY_END.getCode().equals(taskType)) {
                //  结束平台活动任务
                activityScheduleCallBackService.endPfActivityTask(callbackVO);
            } else if (TaskTypeEnum.MC_ACTIVITY_END.getCode().equals(taskType)) {
                // 结束商家活动任务
                activityScheduleCallBackService.endMcActivityTask(callbackVO);
            } else if (TaskTypeEnum.PF_COUPON_RELEASE_START.getCode().equals(taskType)) {
                // 开始平台优惠券发放任务
                activityScheduleCallBackService.startPfCouponReleaseTask(callbackVO);
            } else if (TaskTypeEnum.PF_COUPON_RELEASE_END.getCode().equals(taskType)) {
                // 结束平台优惠券发放任务
                activityScheduleCallBackService.endPfCouponReleaseTask(callbackVO);
            } else if (TaskTypeEnum.MC_COUPON_RELEASE_START.getCode().equals(taskType)) {
                // 开始商家优惠券发放任务
                activityScheduleCallBackService.startMcCouponReleaseTask(callbackVO);
            } else if (TaskTypeEnum.MC_COUPON_RELEASE_END.getCode().equals(taskType)) {
                // 结束商家优惠券发放任务
                activityScheduleCallBackService.endMcCouponReleaseTask(callbackVO);
            }
        }
        return WrapperUtil.success();
    }
}
