package com.ssy.lingxi.marketing.controller.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.api.feign.ICouponFeign;
import com.ssy.lingxi.marketing.api.model.request.CartOrderReq;
import com.ssy.lingxi.marketing.api.model.request.CouponConsumeReq;
import com.ssy.lingxi.marketing.api.model.request.CouponDetailConsumeReq;
import com.ssy.lingxi.marketing.api.model.request.CouponQueryReq;
import com.ssy.lingxi.marketing.api.model.response.CartOrderResp;
import com.ssy.lingxi.marketing.api.model.response.CouponInfoResp;
import com.ssy.lingxi.marketing.service.feign.ICouponFeignService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 优惠券feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/3
 * @ignore 不需要提交到Yapi
 */
@RestController
public class CouponFeignController implements ICouponFeign {

    @Resource
    private ICouponFeignService couponFeignService;

    /**
     * 内部接口 - 校验优惠券可用
     */
    @Override
    public WrapperResp<List<CartOrderResp>> checkCouponAvailable(CartOrderReq req) {
        return WrapperUtil.success(couponFeignService.checkCouponAvailable(req));
    }

    /**
     * 内部接口 - 提交订单后消耗优惠券
     **/
    @Override
    public WrapperResp<Void> consumeCoupon(CouponConsumeReq req) {
        return WrapperUtil.success(couponFeignService.consumeCoupon(req));
    }

    /**
     * 内部接口 - 取消订单后返还优惠券
     **/
    @Override
    public WrapperResp<Void> returnCoupon(List<CouponDetailConsumeReq> req) {
        return WrapperUtil.success(couponFeignService.returnCoupon(req));
    }

    /**
     * 内部接口 - 根据优惠券ids，查询券码
     **/
    @Override
    public WrapperResp<List<CouponInfoResp>> getCouponDetailListByIds(CouponQueryReq req){
        return WrapperUtil.success(couponFeignService.getCouponDetailListByIds(req));
    }
}
