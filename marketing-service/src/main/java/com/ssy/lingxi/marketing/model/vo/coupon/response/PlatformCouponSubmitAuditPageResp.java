package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 平台优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformCouponSubmitAuditPageResp extends PlatformCouponPageResp implements Serializable {

    private static final long serialVersionUID = 9178391241066493292L;

    /**
     * 提交按钮
     */
    private boolean submit;

    /**
     * 删除按钮
     */
    private boolean delete;

    /**
     * 修改按钮
     */
    private boolean update;

}
