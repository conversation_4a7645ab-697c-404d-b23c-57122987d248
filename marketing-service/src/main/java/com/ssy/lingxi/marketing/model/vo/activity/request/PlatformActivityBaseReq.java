package com.ssy.lingxi.marketing.model.vo.activity.request;

import cn.hutool.json.JSONObject;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 平台活动请求基础类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/20
 */
@Data
public class PlatformActivityBaseReq implements Serializable {

    private static final long serialVersionUID = -4309560527998405019L;

    /**
     * 活动名称
     * */
    @NotBlank(message = "活动名称不能为空")
    private String activityName;

    /**
     * 活动开始时间
     * */
    @NotNull(message = "活动开始时间不能为空")
    private Long startTime;

    /**
     * 活动结束时间
     * */
    @NotNull(message = "活动结束时间不能为空")
    private Long endTime;

    /**
     * 报名开始时间(活动参与类型=1时必填)
     * */
    private Long signUpStartTime;

    /**
     * 报名结束时间(活动参与类型=1时必填)
     * */
    private Long signUpEndTime;

    /**
     * 企业会员 0-否 1-是
     * */
    private Integer enterpriseMember = 0;

    /**
     * 个人会员 0-否 1-是
     * */
    private Integer personalMember = 0;

    /**
     * 新会员(平台会员) 0-否 1-是
     * */
    private Integer newMember = 0;

    /**
     * 老会员(平台会员) 0-否 1-是
     * */
    private Integer oldMember = 0;

    /**
     * 活动定义.
     * */
    private JSONObject activityDefined;

    /**
     * 会员等级集合
     * */
    @NotEmpty(message = "适用会员等级不能为空")
    @Valid
    private List<ActivityMemberLevelReq> memberLevelList;

    /**
     * 邀请全部会员 0-否 1-是
     */
    private Integer inviteType;

    /**
     * 邀请会员集合
     */
    @Valid
    private List<PlatformActivityInviteReq> inviteList;

    /**
     * 适用商城
     */
    @Size(min = 1, message = "请选择活动适用的商城")
    @Valid
    private List<ActivityShopReq> shopList;
}
