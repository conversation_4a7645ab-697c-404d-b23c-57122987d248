package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 批量活动优惠计算返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024/12/10
 */
@Getter
@Setter
public class MobileActivityDiscountBatchResp implements Serializable {

    private static final long serialVersionUID = -1284106139690376925L;

    /**
     * 商品优惠明细列表
     */
    private List<MobileGoodsActivityDiscountResp> goodsDiscountList;

    /**
     * 活动分组信息
     * key: 活动ID
     * value: 该活动下的商品列表
     */
    private Map<Long, List<MobileGoodsActivityDiscountResp>> activityGroupMap;

    /**
     * 优惠前总金额
     */
    private BigDecimal totalOriginalAmount;

    /**
     * 优惠后总金额
     */
    private BigDecimal totalDiscountedAmount;

    /**
     * 总优惠金额
     */
    private BigDecimal totalDiscountAmount;

    /**
     * 参与活动的商品数量
     */
    private Integer participatedCount;

    /**
     * 未参与活动的商品skuId列表
     */
    private List<Long> unparticipatedSkuIds;

    /**
     * 命中活动总数
     */
    private Integer hitActivityCount;

}