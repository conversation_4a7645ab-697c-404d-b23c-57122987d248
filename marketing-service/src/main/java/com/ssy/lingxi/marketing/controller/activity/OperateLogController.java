package com.ssy.lingxi.marketing.controller.activity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.common.request.OperateLogDataReq;
import com.ssy.lingxi.marketing.model.vo.common.response.OperateLogInnerResp;
import com.ssy.lingxi.marketing.model.vo.common.response.OperateLogOuterResp;
import com.ssy.lingxi.marketing.service.IOperateLogService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 平台后台-系统管理-营销-操作日志
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/operate/log")
public class OperateLogController extends BaseController {

    @Resource
    private IOperateLogService operateLogService;

    /**
     * 外部日志
     */
    @PostMapping("/outer/page")
    public WrapperResp<PageDataResp<OperateLogOuterResp>> pageOuterLog(@Valid @RequestBody OperateLogDataReq request) {
        return WrapperUtil.success(operateLogService.pageOuterLog(request));
    }

    /**
     * 内部日志
     */
    @PostMapping("/inner/page")
    public WrapperResp<PageDataResp<OperateLogInnerResp>> pageInnerLog(@Valid @RequestBody OperateLogDataReq request) {
        return WrapperUtil.success(operateLogService.pageInnerLog(request));
    }


}
