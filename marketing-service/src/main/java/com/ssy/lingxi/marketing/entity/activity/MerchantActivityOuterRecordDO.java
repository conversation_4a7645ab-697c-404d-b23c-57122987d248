package com.ssy.lingxi.marketing.entity.activity;

import com.ssy.lingxi.common.constant.TableNameConstant;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 商家活动外部记录实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "merchant_activity_outer_record",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "merchant_activity_outer_record_activity_id_idx", columnList = "activityId")})
public class MerchantActivityOuterRecordDO implements Serializable {

    private static final long serialVersionUID = 1435462042496991216L;
    /**
     * ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 平台活动id
     */
    @Column(columnDefinition = "int8")
    private Long activityId;

    /**
     * 用户id
     */
    @Column(columnDefinition = "int8")
    private Long userId;
    /**
     * 会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 角色名称
     */
    @Column(columnDefinition = "varchar(100)")
    private String roleName;

    /**
     * 外部状态：PlatformActivityOuterStatusEnum/PlatformActivitySignUpOuterStatusEnum
     */
    @Column(columnDefinition = "int")
    private Integer status;

    /**
     * 状态名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String statusName;

    /**
     * 操作
     */
    @Column(columnDefinition = "varchar(50)")
    private String operate;

    /**
     * 操作时间
     */
    @Column(columnDefinition = "int8")
    private Long operateTime;

    /**
     * 审核意见
     */
    @Column(columnDefinition = "varchar(120)")
    private String opinion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActivityId() {
        return activityId;
    }

    public void setActivityId(Long activityId) {
        this.activityId = activityId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public Long getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Long operateTime) {
        this.operateTime = operateTime;
    }

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }
}
