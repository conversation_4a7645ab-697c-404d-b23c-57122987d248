package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.coupon.CouponShopDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 优惠券适用商城仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/26
 */
public interface CouponShopRepository extends JpaRepository<CouponShopDO, Long>, JpaSpecificationExecutor<CouponShopDO> {

    /**
     * 根据活动删除
     * @param belongType 所属类型
     * @param id 优惠券id
     */
    void deleteByBelongTypeAndCouponId(Integer belongType, Long id);

    /**
     * 根据活动删除
     * @param belongType 所属类型
     * @param ids 优惠券id
     */
    void deleteByBelongTypeAndCouponIdIn(Integer belongType, List<Long> ids);

    /**
     * 根据活动查询
     * @param belongType 所属类型
     * @param id 优惠券id
     */
    List<CouponShopDO> findByBelongTypeAndCouponId(Integer belongType, Long id);

    /**
     * 根据活动查询
     * @param belongType 所属类型
     * @param id 优惠券id
     */
    List<CouponShopDO> findByBelongTypeAndCouponIdIn(Integer belongType, List<Long> id);

    /**
     * 根据活动查询
     * @param belongType 所属类型
     * @param couponId 优惠券id
     * @param shopId 商城id
     */
    boolean existsByBelongTypeAndCouponIdAndShopId(Integer belongType, Long couponId, Long shopId);
}
