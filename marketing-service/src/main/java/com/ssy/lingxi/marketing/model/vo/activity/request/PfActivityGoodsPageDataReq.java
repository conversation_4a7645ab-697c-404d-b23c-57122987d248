package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分页查询-平台活动商品查询-接口参数VO
 * <AUTHOR>
 * @since 2021/08/36
 * @version 2.0.0
 */
@Getter
@Setter
public class PfActivityGoodsPageDataReq extends PageDataReq implements Serializable {
    private static final long serialVersionUID = -7633420828200258217L;

    /**
     * 活动id
     */
    @NotNull(message = "活动id不能为空")
    private Long activityId;
}
