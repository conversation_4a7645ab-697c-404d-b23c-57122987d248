package com.ssy.lingxi.marketing.model.dto;

import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 活动 - 详情页- 外部流转记录 - VO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GroupPurchasePageDTOData extends PageDataReq implements Serializable {


    private static final long serialVersionUID = -644964869318372033L;

    /**
     * 商品id
     */
    private Long productId;
    /**
     * 订单ids.
     */
    private List<Long> orderIds;
    /**
     * 拼团状态
     */
    private Integer status;
    /**
     * 拼团开始时间
     */
    private Long startTime;
    /**
     * 会员条件过滤
     */
    private MemberAndRoleIdDTO memberDTO;
}
