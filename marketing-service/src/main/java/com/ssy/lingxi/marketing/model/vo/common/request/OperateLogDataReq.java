package com.ssy.lingxi.marketing.model.vo.common.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *  操作日志
 * <AUTHOR>
 * @since 2021/12/3
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OperateLogDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = -8680128987788836390L;

    /**
     * 操作类型 1-平台活动；2-商家活动；3-平台优惠劵；4-商家优惠券；
     */
    @NotNull(message = "操作类型必填")
    private Integer operateType;

    /**
     * 数据id
     */
    private Long id;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;
}
