package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Getter;
import lombok.Setter;

/**
 *  分页查询-平台营销活动报名-商品列表-响应VO
 * <AUTHOR>
 * @since 2021/6/29
 * @version 2.0.0
 */
@Getter
@Setter
public class PfActivitySignUpGoodsPageResp extends BaseActivityGoodsPageResp {

    private static final long serialVersionUID = -5115393559938029813L;

    /**
     * 平台活动报名id
     */
    private Long signUpId;

}
