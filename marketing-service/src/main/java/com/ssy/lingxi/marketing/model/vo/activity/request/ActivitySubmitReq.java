package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 活动提交审核请求实体
 * <AUTHOR> yzc
 * @version 2.0.0
 * @since 2021/8/20
 */
@Data
public class ActivitySubmitReq implements Serializable {

    private static final long serialVersionUID = -5273333998176230711L;
    /**
     * 活动id
     * */
    @NotNull(message = "活动id不能为空")
    private Long id;
}
