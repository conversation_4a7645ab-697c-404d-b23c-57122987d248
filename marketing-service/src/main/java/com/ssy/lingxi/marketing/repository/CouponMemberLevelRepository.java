package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.coupon.CouponMemberLevelDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 优惠券适用会员等级仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/26
 */
public interface CouponMemberLevelRepository extends JpaRepository<CouponMemberLevelDO, Long>, JpaSpecificationExecutor<CouponMemberLevelDO> {

    boolean existsByBelongTypeAndCouponId(Integer belongType, Long id);

    /**
     * 根据活动删除
     * @param belongType 所属类型
     * @param id 活动id
     */
    void deleteByBelongTypeAndCouponId(Integer belongType, Long id);

    /**
     * 根据活动删除
     * @param belongType 所属类型
     * @param ids 活动id
     */
    void deleteByBelongTypeAndCouponIdIn(Integer belongType, List<Long> ids);

    /**
     * 根据活动查询
     * @param belongType 所属类型
     * @param id 活动id
     * @return 返回结果
     */
    List<CouponMemberLevelDO> findByBelongTypeAndCouponId(Integer belongType, Long id);

    /**
     * 根据活动查询
     * @param belongType 所属类型
     * @param ids 活动id
     * @return 返回结果
     */
    List<CouponMemberLevelDO> findByBelongTypeAndCouponIdIn(Integer belongType, List<Long> ids);
}
