package com.ssy.lingxi.marketing.model.vo.coupon.response;

import java.io.Serializable;

/**
 * 优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/13
 */
public class MobileCouponDetailCountResp implements Serializable {
    private static final long serialVersionUID = 4035584976992044396L;

    public MobileCouponDetailCountResp() {
        this.receiveCount = 0;
        this.userCount = 0;
        this.expireCount = 0;
    }

    /**
     * 已领取数量(未使用)
     */
    private Integer receiveCount;

    /**
     * 已使用数量
     */
    private Integer userCount;

    /**
     * 已过期数量
     */
    private Integer expireCount;

    public Integer getReceiveCount() {
        return receiveCount;
    }

    public void setReceiveCount(Integer receiveCount) {
        this.receiveCount = receiveCount;
    }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    public Integer getExpireCount() {
        return expireCount;
    }

    public void setExpireCount(Integer expireCount) {
        this.expireCount = expireCount;
    }
}
