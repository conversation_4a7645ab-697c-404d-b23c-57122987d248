package com.ssy.lingxi.marketing.model.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品工费信息DTO
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ProductWorkFeeInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品价格
     */
    private BigDecimal productPrice;

    /**
     * 工费单价
     */
    private BigDecimal workFeeUnitPrice;

    /**
     * 工费总额 (工费单价 × 购买数量)
     */
    private BigDecimal totalWorkFee;

    /**
     * 购买数量
     */
    private BigDecimal purchaseQuantity;

    /**
     * 购买总金额
     */
    private BigDecimal totalAmount;

    /**
     * 工费占比 (工费/总价格)
     */
    private BigDecimal workFeeRatio;

    /**
     * 是否包含工费
     */
    private Boolean hasWorkFee;

    /**
     * 工费计算方式说明
     */
    private String workFeeCalculationMethod;
}