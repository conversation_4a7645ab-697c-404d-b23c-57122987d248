package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 活动 - 详情页- 外部流转记录 - VO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityOuterRecordPageDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = -2679554813556663831L;
    /**
     * 活动id
     */
    @NotNull(message = "活动id需大于0")
    @Positive(message = "活动id需大于0")
    private Long activityId;


}
