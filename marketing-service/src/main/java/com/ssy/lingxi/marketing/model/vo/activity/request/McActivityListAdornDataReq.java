package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.marketing.enums.MerchantActivityInnerStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商家营销活动列表（装修） - 请求
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/09/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class McActivityListAdornDataReq extends PageDataReq {
    private static final long serialVersionUID = -6504544656583708932L;

    /**
     * 商城ID
     */
    @NotNull(message = "商城ID不能空")
    private Long shopId;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 细分类型（满额、满量、赠送促销）：1.满量减/满额减/赠商品；2.满量折/满额折/赠优惠卷
     */
    private Integer minType;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 会员品类ID
     */
    private Long categoryId;

    /**
     * 会员活动内部状态列表
     *
     * @see MerchantActivityInnerStatusEnum
     */
    private List<Integer> innerStatusList;

}
