package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.ActivityGoodsCouponDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 活动 - 商品信息 - 赠优惠券信息 - 实体仓库
 * <AUTHOR>
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface ActivityGoodsCouponRepository extends JpaRepository<ActivityGoodsCouponDO, Long>, JpaSpecificationExecutor<ActivityGoodsCouponDO> {

    List<ActivityGoodsCouponDO> findByActivityGoodsIdIn(List<Long> activityGoodsIdList);

    List<ActivityGoodsCouponDO> findByActivityGoodsId(Long activityGoodsId);

    List<ActivityGoodsCouponDO> findByCouponIdIn(List<Long> ids);

    void deleteByActivityGoodsId(Long activityGoodsId);
}
