package com.ssy.lingxi.marketing.service.feign;

import com.ssy.lingxi.aftersales.api.model.resp.ReturnGoodsTaxResp;

import java.util.List;
import java.util.Map;

/**
 *  访问售后内部接口
 * <AUTHOR>
 * @since 2021/12/15
 * @version 2.0.0
 */
public interface IAfterSaleFeignService {

    /**
     * 根据订单编号、skuId list 查询订单信息[活动执行明细用]
     * @param orderIds
     * @param skuIds
     * @return
     */
    Map<String, ReturnGoodsTaxResp> getOrderInfoMapByExecute(List<Long> orderIds, List<Long> skuIds);

   }
