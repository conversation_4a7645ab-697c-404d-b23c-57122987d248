package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 商家活动列表返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@Data
public class MerchantActivityToBeExamPageResp implements Serializable {

    private static final long serialVersionUID = -3024181672582452749L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 活动类型名称
     */
    private String activityTypeName;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 外部状态
     */
    private Integer outerStatus;

    /**
     * 外部状态名称
     */
    private String outerStatusName;

    /**
     * 审核按钮
     */
    private boolean exam;
}
