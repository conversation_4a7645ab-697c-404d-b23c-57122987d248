package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 商家活动新增请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantActivityAddReq extends MerchantActivityBaseReq {

    private static final long serialVersionUID = 6745738952891220389L;

    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用，17-工价优惠，18-满额促销（新），19-满量促销（新）
     * */
    @NotNull(message = "活动类型不能为空")
    private Integer activityType;
}
