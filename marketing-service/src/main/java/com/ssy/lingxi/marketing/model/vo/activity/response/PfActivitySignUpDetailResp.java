package com.ssy.lingxi.marketing.model.vo.activity.response;

import cn.hutool.json.JSONObject;
import com.ssy.lingxi.marketing.model.vo.common.response.TaskStepResp;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 营销能力 - 平台营销活动报名 - 活动详情 - 响应
 * <AUTHOR>
 * @since 2021/09/10
 * @version 2.0.0
 */
@Getter
@Setter
public class PfActivitySignUpDetailResp implements Serializable {
    private static final long serialVersionUID = 1483908129824828936L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 活动类型名称
     */
    private String activityTypeName;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动参与类型
     */
    private String activitySignUpTypeName;

    /**
     * 报名开始时间
     * */
    private Long signUpStartTime;

    /**
     * 报名结束时间
     * */
    private Long signUpEndTime;

    /**
     * 外部状态
     * */
    private Integer outerStatus;

    /**
     * 外部状态名称
     * */
    private String outerStatusName;

    /**
     * 内部状态
     * */
    private Integer innerStatus;

    /**
     * 内部状态名称
     * */
    private String innerStatusName;

    /**
     * 企业会员 0-否 1-是
     * */
    private Integer enterpriseMember;

    /**
     * 个人会员 0-否 1-是
     * */
    private Integer personalMember;

    /**
     * 新会员(平台会员) 0-否 1-是
     * */
    private Integer newMember;

    /**
     * 老会员(平台会员) 0-否 1-是
     * */
    private Integer oldMember;

    /**
     * 活动定义
     * */
    private JSONObject activityDefined;

    /**
     * 适用会员等级
     */
    private List<ActivityMemberLevelResp> memberLevelList;

    /**
     * 活动适用的商城
     */
    private List<ActivityShopResp> shopList;

    /**
     * 外部流转步骤
     */
    private List<TaskStepResp> outerTaskStepList;

    /**
     * 内部流转步骤
     */
    private List<TaskStepResp> innerTaskStepList;

    /**
     * 外部流转记录
     * */
    private List<ActivityOuterRecordResp> outerRecordDOList;

    /**
     * 内部工作流程
     * */
    private List<ActivityInnerRecordResp> innerRecordDOList;

    /**
     * 过滤的skuId
     */
    private Set<Long> filterSkuId;
}
