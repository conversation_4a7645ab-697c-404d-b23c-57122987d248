package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 回显活动商品（装修） - 响应
 * <AUTHOR>
 * @since 2021/09/03
 * @version 2.0.0
 */
@Data
public class ActivityGoodsAdornResp implements Serializable {
    private static final long serialVersionUID = -2694111553260195487L;

    /**
     * 活动商品ID
     * */
    private Long id;

    /**
     * 活动ID
     * */
    private Long activityId;

    /**
     * 赠送促销类型：1-满额赠 2-买商品赠
     * */
    private Integer giveType;

    /**
     * 商品ID
     * */
    private Long productId;

    /**
     * skuId
     * */
    private Long skuId;

    /**
     * 商品名称
     * */
    private String productName;

    /**
     * 商品图片
     * */
    private String productImgUrl;

    /**
     * 规格
     * */
    private String type;

    /**
     * 品类
     * */
    private String category;

    /**
     * 品牌
     * */
    private String brand;

    /**
     * 单位
     * */
    private String unit;

    /**
     * 商品价格/预售价格
     * */
    private BigDecimal price;

    /**
     * 直降价格/起始价格
     * */
    private BigDecimal plummetPrice;

    /**
     * 活动价格/团购价格/秒杀价格/单位定金/砍价底价
     * */
    private BigDecimal activityPrice;

    /**
     * 定金抵扣单价
     * */
    private BigDecimal deductionPrice;

    /**
     * 折扣（如85折，输入85，9折输入90）
     * */
    private Integer discount;

    /**
     * 个人限购数量
     * */
    private Integer restrictNum;

    /**
     * 活动限购总数量
     * */
    private Integer restrictTotalNum;
    /**
     * 已售数量
     */
    private BigDecimal salesNum;

    /**
     * 所属活动（key：id、name、type、belongType。value：活动ID、活动名称、活动类型、所属类型）
     * */
    private List<Map<String, Object>> activityList = new ArrayList<>();

    /**
     * 赠品（优惠卷）
     * */
    private List<Map<String, Object>> giveCouponList;

    /**
     * 配套商品 - 组
     * */
    private List<ActivityGoodsSubsidiaryGroupResp> goodsSubsidiaryGroupList;

    @Data
    public static class ActivityGoodsSubsidiaryGroupResp implements Serializable {
        private static final long serialVersionUID = 6123765836903833297L;

        /**
         * 分组编号/优惠阶梯/换购阶梯
         * */
        private Integer groupNo;

        /**
         * 换购门槛/优惠门槛数量或金额
         * */
        private BigDecimal limitValue;

        /**
         * 套餐价格
         * */
        private BigDecimal groupPrice;

        /**
         * 配套商品 - 组明细
         * */
        private List<ActivityGoodsSubsidiaryGroupDetailsResp> goodsSubsidiaryGroupDetailsList;

        @Data
        public static class ActivityGoodsSubsidiaryGroupDetailsResp implements Serializable {
            private static final long serialVersionUID = -1250019751214660455L;

            /**
             * id
             * */
            private Long id;

            /**
             * 商品id
             * */
            private Long productId;

            /**
             * skuId
             * */
            private Long skuId;

            /**
             * 商品名称
             * */
            private String productName;

            /**
             * 品类
             * */
            private String category;

            /**
             * 品牌
             * */
            private String brand;

            /**
             * 单位
             * */
            private String unit;

            /**
             * 商品价格
             * */
            private BigDecimal price;

            /**
             * 换购价格
             * */
            private BigDecimal swapPrice;

            /**
             * 允许换购数量/赠送数量/搭配数量
             * */
            private BigDecimal num;

            /**
             * 赠品主图
             * */
            private String productImgUrl;
        }
    }
}
