package com.ssy.lingxi.marketing.model.vo.coupon.response;

import com.ssy.lingxi.marketing.api.model.response.TagDetailResp;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 活动商品BO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/1
 */
@Getter
@Setter
public class ActivityGoodsCartResp implements Serializable {

    private static final long serialVersionUID = 3921620965928831578L;

    /**
     * 店铺
     */
    private List<Store> storeList;

    @Data
    public static class Store implements Serializable {
        private static final long serialVersionUID = -6216717462862528805L;

        /**
         * 商品集合
         */
        @NotEmpty(message = "商品集合不能为空")
        @Valid
        private List<ActivityGoodsCartResp.Goods> goodsList;
    }

    @Data
    public static class Goods implements Serializable {
        private static final long serialVersionUID = 2664378142781155790L;

        /**
         * 商品id
         */
        @NotNull(message = "商品id不能为空")
        private Long productId;

        /**
         * sku商品集合
         */
        @NotEmpty(message = "sku商品集合不能为空")
        @Valid
        private List<ActivityGoodsCartResp.GoodsSku> goodsSkuList;
    }

    @Data
    public static class GoodsSku implements Serializable {
        private static final long serialVersionUID = 2615807733184458356L;

        /**
         * skuId
         */
        private Long skuId;

        /**
         * 最大个人限购数量
         */
        private Integer maxRestrictNum;

        /**
         * 活动标签集合
         */
        private List<TagDetailResp> tagDetailList;
    }
}
