package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.scheduler.api.model.req.ScheduleTaskCallbackReq;

/**
 * 平台、商家活动定时服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/25
 */
public interface IMarketingScheduleCallBackService {

    /**
     * 启动平台活动报名任务
     * [报名时间开始]触发状态变更为: 待报名 -> 待审核报名
     * @param callbackVO 接口参数
     */
    void startPfActivitySignUpTask(ScheduleTaskCallbackReq callbackVO);

    /**
     * 结束平台活动报名任务
     * [报名时间结束]触发状态变更为: 待审核报名 -> 待上线活动
     * @param callbackVO 接口参数
     */
    void endPfActivitySignUpTask(ScheduleTaskCallbackReq callbackVO);

    /**
     * 结束平台活动任务
     * @param callbackVO 接口参数
     */
    void endPfActivityTask(ScheduleTaskCallbackReq callbackVO);

    /**
     * 结束商家活动任务
     * @param callbackVO 接口参数
     */
    void endMcActivityTask(ScheduleTaskCallbackReq callbackVO);

    /**
     * 开始平台优惠券发放任务
     * @param callbackVO 接口参数
     */
    void startPfCouponReleaseTask(ScheduleTaskCallbackReq callbackVO);

    /**
     * 结束平台优惠券发放任务
     * @param callbackVO 接口参数
     */
    void endPfCouponReleaseTask(ScheduleTaskCallbackReq callbackVO);

    /**
     * 开始商家优惠券发放任务
     * @param callbackVO 接口参数
     */
    void startMcCouponReleaseTask(ScheduleTaskCallbackReq callbackVO);

    /**
     * 结束商家优惠券发放任务
     * @param callbackVO 接口参数
     */
    void endMcCouponReleaseTask(ScheduleTaskCallbackReq callbackVO);
}
