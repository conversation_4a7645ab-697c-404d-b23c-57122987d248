package com.ssy.lingxi.marketing.model.vo.coupon.response;

import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.marketing.model.vo.common.response.TaskStepResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 商家优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantCouponResp extends MerchantCouponBaseResp {

    private static final long serialVersionUID = -9168674300710952089L;

    /**
     * 适用商品
     */
    private List<SuitableCommoditySkuResp> suitableCommoditySkuList;

    /**
     * 适用品类, example: [[{"id: 1", "name": "a"}]]
     */
    private List<List<SuitableCategoryResp>> suitableCategoryList;

    /**
     * 适用品牌
     */
    private List<SuitableBrandResp> suitableBrandList;

    /**
     * 适用会员
     */
    private List<SelectItemResp> suitableMemberTypes;

    /**
     * 适用会员等级(会员等级id)
     * 当适用用户(会员) 类型有勾选新会员、老会员选型,该字段作用于这些数据
     */
    private List<SuitableMemberLevelResp> suitableMemberLevelTypes;

    /**
     * 适用商城
     */
    private List<SuitableMallResp> suitableMallTypes;

    /**
     * 流程流转
     */
    private List<TaskStepResp> taskSteps;

    /**
     * 单据流转记录
     */
    private List<MerchantCouponHistoryResp> history;
}
