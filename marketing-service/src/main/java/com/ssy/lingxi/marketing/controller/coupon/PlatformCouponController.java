package com.ssy.lingxi.marketing.controller.coupon;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.common.request.CommonAgreeReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.*;
import com.ssy.lingxi.marketing.model.vo.coupon.response.*;
import com.ssy.lingxi.marketing.service.ICommonService;
import com.ssy.lingxi.marketing.service.IPlatformCouponService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 *
 * 平台优惠劵管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/28
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/coupon/platform")
public class PlatformCouponController extends BaseController {

    @Resource
    private IPlatformCouponService platformCouponService;

    @Resource
    private ICommonService commonService;

    /**
     * 1-0元购买抵扣券 2-平台通用优惠券
     * @description 优惠券类型列表
     * @return 查询结果
     */
    @GetMapping("/typeList")
    public WrapperResp<List<SelectItemResp>> listPlatformCouponTypes() {
        return WrapperUtil.success(commonService.listPlatformCouponTypes());
    }

    /**
     * 1-前台用户领券 2-指定会员发券 3-营销活动用券 4-会员运营用券
     * @description 优惠券领取方式列表
     * @return 查询结果
     */
    @GetMapping("/getWayList")
    public WrapperResp<List<SelectItemResp>> listPlatformCouponAcquireWays() {
        return WrapperUtil.success(commonService.listCouponAcquireWays());
    }

    /**
     * 1-待提交审核 2-待审核(一级) 3-待审核不通过(一级) 4-待审核(二级) 5-待审核不通过(二级) 6-待提交 7-待执行 8-进行中 9-已终止 10-已取消 11-已结束
     * @description 优惠券状态列表
     * @return 查询结果
     */
    @GetMapping("statusList")
    public WrapperResp<List<SelectItemResp>> listPlatformCouponStatuses() {
        return WrapperUtil.success(commonService.listPlatformCouponStatuses());
    }


    /**
     * 1-企业会员 2-个人会员
     * @description 优惠券适用会员类型列表
     * @return 查询结果
     */
    @GetMapping("/memberTypeList")
    public WrapperResp<List<SelectItemResp>> listMemberTypes() {
        return WrapperUtil.success(commonService.listMemberTypes());
    }

    /**
     * 1-新会员 2-老会员
     * @description 优惠券适用会员列表
     * @return 查询结果
     */
    @GetMapping("suitableMemberTypeList")
    public WrapperResp<List<SelectItemResp>> listSuitableMemberTypes() {
        return WrapperUtil.success(commonService.listPlatformSuitableMemberTypes());
    }

    /**
     * 平台优惠券分页列表查询条件
     * @return 返回结果
     */
    @GetMapping("/page/condition")
    public WrapperResp<PlatformCouponConditionResp> listPlatformCouponConditions() {
        return WrapperUtil.success(commonService.listPlatformCouponConditions());
    }

    // =============================平台优惠劵查询=============================

    /**
     * 平台优惠劵查询 - 分页列表
     * @return 查询结果
     */
    @GetMapping("/summary/page")
    public WrapperResp<PageDataResp<PlatformCouponSummaryPageResp>> pageSummaryPlatformCoupon(@Valid PlatformCouponPageDataReq request) {
        return WrapperUtil.success(platformCouponService.pageSummaryPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 平台优惠劵查询 - 详情
     * @return 查询结果
     */
    @GetMapping("/summary/get")
    public WrapperResp<PlatformCouponResp> getSummaryPlatformCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(platformCouponService.getPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 平台优惠劵查询 - 修改
     * @param request 接口参数
     */

    @PostMapping("/waitAudit/modification")
    public WrapperResp<Void> modificationPlatformCoupon(@RequestBody @Valid PlatformCouponModificationReq request) {
        return WrapperUtil.success(platformCouponService.modificationPlatformCoupon(request, getPlatformUser()));
    }


    /**
     * 平台优惠劵查询 - 终止
     * @param request 接口参数
     */
    @PostMapping("/summary/stop")
    public WrapperResp<Void> stopPlatformCoupon(@RequestBody @Valid PlatformCouponOperationReq request) {
        return WrapperUtil.success(platformCouponService.stopPlatformCoupon(request, getPlatformUser()));
    }


    /**
     * 平台优惠劵查询 - 重启
     * @param request 接口参数
     */
    @PostMapping("/summary/restart")
    public WrapperResp<Void> restartPlatformCoupon(@RequestBody @Valid PlatformCouponOperationReq request) {
        return WrapperUtil.success(platformCouponService.restartPlatformCoupon(request, getPlatformUser()));
    }


    /**
     * 平台优惠劵查询 - 取消
     * @param request 接口参数
     */
    @PostMapping("/summary/cancel")
    public WrapperResp<Void> cancelPlatformCoupon(@RequestBody @Valid PlatformCouponOperationReq request) {
        return WrapperUtil.success(platformCouponService.cancelPlatformCoupon(request, getPlatformUser()));
    }


    // =============================待提交审核平台优惠劵=============================

    /**
     * 待提交审核平台优惠劵 - 分页列表
     * @return 查询结果
     */
    @GetMapping("/waitAudit/page")
    public WrapperResp<PageDataResp<PlatformCouponSubmitAuditPageResp>> pageWaitAuditPlatformCoupon(@Valid PlatformCouponPageDataReq request) {
        return WrapperUtil.success(platformCouponService.pageSubmitAuditPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待提交审核平台优惠劵 - 详情
     * @return 查询结果
     */
    @GetMapping("/waitAudit/get")
    public WrapperResp<PlatformCouponResp> getWaitAuditPlatformCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(platformCouponService.getPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待提交审核平台优惠劵 - 新增
     * @param request 接口参数
     */
    @PostMapping("/waitAudit/add")
    public WrapperResp<Void> addPlatformCoupon(@RequestBody @Valid PlatformCouponAddReq request) {
        return WrapperUtil.success(platformCouponService.addPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待提交审核平台优惠劵 - 修改
     * @param request 接口参数
     */
    @PostMapping("/waitAudit/update")
    public WrapperResp<Void> updatePlatformCoupon(@RequestBody @Valid PlatformCouponUpdateReq request) {
        return WrapperUtil.success(platformCouponService.updatePlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待提交审核平台优惠劵 - 删除
     * @param request 接口参数
     */
    @PostMapping("/waitAudit/delete")
    public WrapperResp<Void> deletePlatformCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(platformCouponService.deletePlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待提交审核平台优惠劵 - 提交审核
     * @param request 接口参数
     */
    @PostMapping("/waitAudit/submitBatch")
    public WrapperResp<Void> submitBatchAuditPlatformCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(platformCouponService.submitAuditPlatformCoupon(request, getPlatformUser()));
    }

    // =============================待审核平台优惠劵(一级)=============================

    /**
     * 待审核平台优惠劵(一级) - 分页列表
     * @return 查询结果
     */
    @GetMapping("/waitAuditOne/page")
    public WrapperResp<PageDataResp<PlatformCouponAuditPageResp>> pageAuditOnePlatformCoupon(@Valid PlatformCouponPageDataReq request) {
        return WrapperUtil.success(platformCouponService.pageAuditOnePlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待审核平台优惠劵(一级) - 详情
     * @return 查询结果
     */
    @GetMapping("/waitAuditOne/get")
    public WrapperResp<PlatformCouponResp> getAuditOnePlatformCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(platformCouponService.getPlatformCoupon(request, getPlatformUser()));
    }


    /**
     * 待审核平台优惠劵(一级) - 提交审核
     * @param request 接口参数
     */
    @PostMapping("/waitAuditOne/audit")
    public WrapperResp<Void> auditOnePlatformCoupon(@RequestBody @Valid CommonAgreeReq request) {
        return WrapperUtil.success(platformCouponService.auditOnePlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待审核平台优惠劵(一级) - 批量提交审核
     * @param request 接口参数
     */
    @PostMapping("/waitAuditOne/auditBatch")
    public WrapperResp<Void> auditOneBatchPlatformCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(platformCouponService.batchAuditOnePlatformCoupon(request, getPlatformUser()));
    }

    // =============================待审核平台优惠劵(二级)=============================

    /**
     * 待审核平台优惠劵(二级) - 分页列表
     * @return 查询结果
     */
    @GetMapping("/waitAuditTwo/page")
    public WrapperResp<PageDataResp<PlatformCouponAuditPageResp>> pageAuditTwoPlatformCoupon(@Valid PlatformCouponPageDataReq request) {
        return WrapperUtil.success(platformCouponService.pageAuditTwoPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待审核平台优惠劵(二级) - 详情
     * @return 查询结果
     */
    @GetMapping("/waitAuditTwo/get")
    public WrapperResp<PlatformCouponResp> getAuditTwoPlatformCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(platformCouponService.getPlatformCoupon(request, getPlatformUser()));
    }


    /**
     * 待审核平台优惠劵(二级) - 审核
     * @param request 接口参数
     */
    @PostMapping("/waitAuditTwo/audit")
    public WrapperResp<Void> auditTwoPlatformCoupon(@RequestBody @Valid CommonAgreeReq request) {
        return WrapperUtil.success(platformCouponService.auditTwoPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待审核平台优惠劵(二级) - 批量审核
     * @param request 接口参数
     */
    @PostMapping("/waitAuditTwo/auditBatch")
    public WrapperResp<Void> auditTwoBatchPlatformCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(platformCouponService.batchAuditTwoPlatformCoupon(request, getPlatformUser()));
    }

    // =============================待提交平台优惠劵=============================

    /**
     * 待提交平台优惠劵 - 分页列表
     * @return 查询结果
     */
    @GetMapping("/waitSubmit/page")
    public WrapperResp<PageDataResp<PlatformCouponSubmitPageResp>> pageSubmitPlatformCoupon(@Valid PlatformCouponPageDataReq request) {
        return WrapperUtil.success(platformCouponService.pageSubmitPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待提交平台优惠劵 - 详情
     * @return 查询结果
     */
    @GetMapping("/waitSubmit/get")
    public WrapperResp<PlatformCouponResp> getSubmitPlatformCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(platformCouponService.getPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 待提交平台优惠劵 - 批量提交
     * @param request 接口参数
     */
    @PostMapping("/waitSubmit/submitBatch")
    public WrapperResp<Void> submitBatchPlatformCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(platformCouponService.submitPlatformCoupon(request, getPlatformUser()));
    }

    // =============================平台优惠劵执行=============================

    /**
     * 平台优惠劵执行 - 分页列表
     * @return 查询结果
     */
    @GetMapping("/waiteExecute/page")
    public WrapperResp<PageDataResp<PlatformCouponExecutePageResp>> pageExecutePlatformCoupon(@Valid PlatformCouponPageDataReq request) {
        return WrapperUtil.success(platformCouponService.pageExecutePlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 平台优惠劵执行 - 详情
     * @return 查询结果
     */
    @GetMapping("/waiteExecute/get")
    public WrapperResp<PlatformCouponResp> getExecutePlatformCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(platformCouponService.getPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 平台优惠劵执行 - 发券
     * @param request 接口参数
     */
    @PostMapping("/waiteExecute/grant")
    public WrapperResp<Void> grantPlatformCoupon(@RequestBody @Valid PlatformCouponGrantReq request) {
        return WrapperUtil.success(platformCouponService.grantPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 平台优惠劵执行 - 发券详情
     * @return 返回结果
     */
    @GetMapping("/waiteExecute/grant/get")
    public WrapperResp<PlatformCouponGrantResp> getGrantPlatformCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(platformCouponService.getGrantPlatformCoupon(request, getPlatformUser()));
    }

    /**
     * 平台优惠劵执行明细 - 查询条件
     * @return 返回结果
     */
    @GetMapping("/waiteExecute/detail/page/condition")
    public WrapperResp<PlatformCouponDetailConditionResp> getExecutePlatformCouponDetailCondition(@Valid CommonIdReq request) {
        return WrapperUtil.success(platformCouponService.getExecutePlatformCouponDetailCondition(request));
    }

    /**
     * 平台优惠劵执行明细 - 分页列表
     * @return 返回结果
     */
    @GetMapping("/waiteExecute/detail/page")
    public WrapperResp<PageDataResp<PlatformCouponDetailPageResp>> pageExecutePlatformCouponDetail(@Valid PlatformCouponDetailPageDataReq request) {
        return WrapperUtil.success(platformCouponService.pageExecutePlatformCouponDetail(request, getPlatformUser()));
    }
}
