package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 活动适用会员等级VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/20
 */
@Data
public class ActivityMemberLevelReq implements Serializable {

    private static final long serialVersionUID = 6276449210222418586L;
    private Long id;
    /**
     * 会员等级id
     */
    @NotNull(message = "会员等级id不能为空")
    private Long memberLevelId;

    /**
     * 会员类型
     */
    @NotNull(message = "会员类型不能为空")
    private Integer memberType;

    /**
     * 会员类型名称
     * */
    @NotNull(message = "会员类型名称不能为空")
    private String memberTypeName;

    /**
     * 会员角色类型
     */
    @NotNull(message = "会员角色类型不能为空")
    private Integer roleType;

    /**
     * 会员角色id
     */
    @NotNull(message = "会员角色id不能为空")
    private Long roleId;

    /**
     * 会员角色名称
     */
    @NotEmpty(message = "会员角色名称不能为空")
    private String roleName;

    /**
     * 会员角色类型名称
     * */
    @NotNull(message = "会员角色类型名称不能为空")
    private String roleTypeName;

    /**
     * 会员等级
     */
    @NotNull(message = "会员等级不能为空")
    private Integer level;

    /**
     * 会员等级类型
     * */
    @NotNull(message = "会员等级类型不能为空")
    private Integer levelType;

    /**
     * 会员等级类型名称
     * */
    @NotNull(message = "会员等级类型名称不能为空")
    private String levelTypeName;

    /**
     * 会员等级标签
     * */
    @NotNull(message = "会员等级标签不能为空")
    private String levelTag;


}
