package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 平台活动待审核报名请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/25
 */
@Getter
@Setter
public class PlatformActivitySignUpDetailReq implements Serializable {


    private static final long serialVersionUID = 6939705346707102039L;

    /**
     * 活动报名id
     */
    @NotNull(message = "活动报名id不能为空")
    private Long signUpId;
}
