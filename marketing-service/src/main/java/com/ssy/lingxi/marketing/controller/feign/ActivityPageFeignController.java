package com.ssy.lingxi.marketing.controller.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.api.feign.IActivityPageFeign;
import com.ssy.lingxi.marketing.api.model.request.ActivityTypeReq;
import com.ssy.lingxi.marketing.service.IActivityPageWebService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * web - 活动页装修内部接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/24
 * @ignore 不需要提交到Yapi
 */
@RestController
public class ActivityPageFeignController implements IActivityPageFeign {

    @Resource
    private IActivityPageWebService activityPageWebService;

    /**
     * 根据活动类型和活动id移除装修页装修的活动
     *
     * @param activityTypeReq 请求参数
     * @return 无返回
     */
    @Override
   public WrapperResp<Void> updateActivityPage(@RequestBody @Valid ActivityTypeReq activityTypeReq) {
        activityPageWebService.updateActivityPage(activityTypeReq);
        return WrapperUtil.success();
    }
}
