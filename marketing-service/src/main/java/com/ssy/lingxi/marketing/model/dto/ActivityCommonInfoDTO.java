package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 活动相关属性dto
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/16
 */
@Getter
@Setter
public class ActivityCommonInfoDTO implements Serializable {
    private static final long serialVersionUID = 7010220116192806026L;

    /**
     * 活动id
     */
    private Long id;
    /**
     * 活动名称
     * */
    private String activityName;
    /**
     * 活动开始时间
     * */
    private Long startTime;

    /**
     * 活动结束时间
     * */
    private Long endTime;
    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     *         ActivityTypeEnum.class
     * */
    private Integer activityType;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 状态 是否上线
     */
    private Boolean online;
}
