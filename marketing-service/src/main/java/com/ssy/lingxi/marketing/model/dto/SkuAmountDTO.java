package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计算满额减优惠金额精度差问题（最后一条记录采用减法原则计算）
 * 主要包括活动[满额减][]
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/16
 */
@Getter
@Setter
public class SkuAmountDTO implements Serializable {
    private static final long serialVersionUID = -6628276816907088921L;
    /**
     * sku计次
     */
    private Integer num;

    /**
     * sku总金额
     */
    private BigDecimal amount;

    /**
     * 累计优惠金额
     */
    private BigDecimal discountAmount;
}
