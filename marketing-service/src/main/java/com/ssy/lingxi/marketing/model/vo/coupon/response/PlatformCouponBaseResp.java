package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 平台优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@Data
public class PlatformCouponBaseResp implements Serializable {

    private static final long serialVersionUID = -1392051019026631569L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券类型
     */
    private Integer type;

    /**
     * 优惠券类型名称
     */
    private String typeName;

    /**
     * 领(发)券起始时间
     */
    private Long releaseTimeStart;

    /**
     * 领(发)券结束时间
     */
    private Long releaseTimeEnd;

    /**
     * 券面额
     */
    private BigDecimal denomination;

    /**
     * 发券数量
     */
    private Integer quantity;

    /**
     * 领取方式
     */
    private Integer getWay;

    /**
     * 领取方式名称
     */
    private String getWayName;

    /**
     * 领取条件, 每会员ID共可领
     */
    private Integer conditionGetTotal;

    /**
     * 领取条件, 每会员ID每日可领
     */
    private Integer conditionGetDay;

    /**
     * 有效类型 1-固定有效时间 2-自领取开始时间
     */
    private Integer effectiveType;

    /**
     * 固定有效时间, 券有效起始时间
     */
    private Long effectiveTimeStart;

    /**
     * 固定有效时间, 券有效结束时间
     */
    private Long effectiveTimeEnd;

    /**
     * 自领取开始时间, 券多少天失效
     */
    private Integer invalidDay;

    /**
     * 使用条件, 满多少金额可用
     */
    private BigDecimal useConditionMoney;

    /**
     * 使用条件说明
     */
    private String useConditionDesc;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 创建时间
     */
    private Long createTime;
}
