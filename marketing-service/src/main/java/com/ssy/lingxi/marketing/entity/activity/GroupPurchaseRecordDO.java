package com.ssy.lingxi.marketing.entity.activity;

import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 拼团记录 - DO
 * <AUTHOR>
 * @since 2021/11/23
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "group_purchase_record")
public class GroupPurchaseRecordDO implements Serializable {


    private static final long serialVersionUID = 2088730127203953225L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8")
    private Long createTime;
    /**
     * 成团人数
     */
    @Column
    private Integer assembleNum;
    /**
     * 参团人数
     */
    @Column
    private Integer num;

    /**
     * 成团时效(单位小时)
     */
    @Column
    private Integer validTime;
    /**
     * 结束时间（=创建时间+成团时效）
     */
    @Column(columnDefinition = "int8")
    private Long endTime;

    /**
     * 状态（1：拼团中，2：拼团成功，3：拼团失败）
     */
    @Column
    private Integer status;

}