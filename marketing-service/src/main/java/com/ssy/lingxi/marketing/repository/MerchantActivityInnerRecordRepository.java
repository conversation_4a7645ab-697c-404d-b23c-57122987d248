package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.MerchantActivityInnerRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 商家活动内部记录仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
public interface MerchantActivityInnerRecordRepository extends JpaRepository<MerchantActivityInnerRecordDO, Long>, JpaSpecificationExecutor<MerchantActivityInnerRecordDO> {

}
