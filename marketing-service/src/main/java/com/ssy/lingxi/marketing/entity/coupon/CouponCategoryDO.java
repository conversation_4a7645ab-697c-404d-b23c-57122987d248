package com.ssy.lingxi.marketing.entity.coupon;


import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.marketing.handler.convert.JpaJsonToScBOListConverter;
import com.ssy.lingxi.marketing.model.bo.SuitableCategoryBO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * 优惠券适用品类实体类【产品暂弃用】
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/17
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_category",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_category_coupon_idx", columnList = "couponId")})
public class CouponCategoryDO implements Serializable {

    private static final long serialVersionUID = 2781751752141149990L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 商家优惠券id
     */
    @Column
    private Long couponId;

    /**
     * 品类整一级的json, 包括所有品类上级与最后一级品类
     */
    @Convert(converter = JpaJsonToScBOListConverter.class)
    @Column(columnDefinition = "jsonb")
    private List<SuitableCategoryBO> categoryList;

    /**
     * 品类id(最后一级)
     */
    @Column(columnDefinition = "int8")
    private Long categoryId;
}
