package com.ssy.lingxi.marketing.entity.coupon;


import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 优惠券适用品牌实体类【产品暂弃用】
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/17
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_brand",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_brand_coupon_idx", columnList = "couponId")})
public class CouponBrandDO implements Serializable {

    private static final long serialVersionUID = -8711840062737214243L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 商家优惠券id
     */
    @Column
    private Long couponId;

    /**
     * 品牌id
     */
    @Column(columnDefinition = "int8")
    private Long brandId;
    /**
     * 品牌名称
     */
    @Column(columnDefinition = "varchar(20)")
    private String brandName;

    /**
     * logo图片路径
     */
    @Column(columnDefinition = "varchar(250)")
    private String logo;
}
