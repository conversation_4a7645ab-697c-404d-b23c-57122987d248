package com.ssy.lingxi.marketing.model.vo.activity.response;

import com.ssy.lingxi.marketing.model.bo.ActivityGoodsCouponGroupBO;
import com.ssy.lingxi.marketing.model.bo.ActivityGoodsSubsidiaryGroupBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 营销活动商品列表基础类
 */
@Getter
@Setter
public class ActivityExecuteGoodsPageResp implements Serializable {


    private static final long serialVersionUID = -127809006013460227L;
    /**
     * 活动商品id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 会员角色id
     */
    private Long roleId;

    /**
     * 会员角色名称
     */
    private String roleName;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格
     */
    private String type;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * 活动商品图片
     */
    private String productImgUrl;

    /**
     * 商品价格/预售价格
     */
    private BigDecimal price;

    /**
     * 预售价格
     */
    private BigDecimal preSelPrice;

    /**
     * 直降价格/起始价格
     */
    private BigDecimal plummetPrice;

    /**
     * 活动价格/团购价格/秒杀价格/单位定金/砍价底价
     */
    private BigDecimal activityPrice;

    /**
     * 定金抵扣单价
     */
    private BigDecimal deductionPrice;

    /**
     * 折扣（如85折，输入85，9折输入90）
     */
    private BigDecimal discount;

    /**
     * 个人限购数量
     */
    private Integer restrictNum;

    /**
     * 活动限购总数量
     */
    private Integer restrictTotalNum;
    /**
     * 参与客户数
     */
    private Integer customerCount;
    /**
     * 实购数量
     */
    private BigDecimal salesNum;
    /**
     * 实购金额
     */
    private BigDecimal amount;
    /**
     * 商品赠品、换购商品、套餐商品（组）
     */
    private List<ActivityGoodsSubsidiaryGroupBO> goodsSubsidiaryGroupList;

    /**
     * 商品赠优惠券信息（组）
     */
    private List<ActivityGoodsCouponGroupBO> couponGroupList;
}
