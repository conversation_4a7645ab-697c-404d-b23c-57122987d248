package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 平台活动 - 新增 - DTO
 * <AUTHOR> yzc
 * @since 2021/6/18
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformActivityUpdateReq extends PlatformActivityBaseReq {

    private static final long serialVersionUID = 2509631101772329491L;

    /**
     * 活动id
     */
    @NotNull(message = "活动id不能为空")
    private Long id;
}
