package com.ssy.lingxi.marketing.serviceImpl.base.activity;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.marketing.ActivityTypeEnum;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.enums.marketing.PlatformCouponTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.marketing.constant.MarketingRedisKeyConstant;
import com.ssy.lingxi.marketing.entity.coupon.CouponGoodsDO;
import com.ssy.lingxi.marketing.entity.coupon.MerchantCouponDO;
import com.ssy.lingxi.marketing.entity.coupon.PlatformCouponDO;
import com.ssy.lingxi.marketing.enums.*;
import com.ssy.lingxi.marketing.model.bo.*;
import com.ssy.lingxi.marketing.model.dto.ActivityCommonInfoDTO;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityPageCouponDetailResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityPrizeCouponResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.SuitableProductResp;
import com.ssy.lingxi.marketing.repository.*;
import com.ssy.lingxi.marketing.service.feign.IManageFeignService;
import com.ssy.lingxi.marketing.util.ValidatorUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 基础营销活动服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/16
 */
@Slf4j
@Service
public class BaseActivityService {
    @Resource
    private MerchantCouponRepository merchantCouponRepository;
    @Resource
    private PlatformCouponRepository platformCouponRepository;
    @Resource
    private CouponGoodsRepository couponGoodsRepository;
    @Resource
    private IManageFeignService platformManageFeignService;
    @Resource
    private PlatformActivityRepository platformActivityRepository;
    @Resource
    private MerchantActivityRepository merchantActivityRepository;
    @Resource
    private IRedisUtils redisUtils;

    /**
     * 获取活动定义数据
     * @param belongTypeEnum 所属类型
     * @param activityType 活动类型
     * @param activityDefined 活动定义
     * @return 返回结果
     */
    public JSONObject getActivityDefine(BelongTypeEnum belongTypeEnum, Integer activityType, ActivityDefinedBO activityDefined) {
        JSONObject jsonObject = JSONUtil.parseObj(activityDefined);
        // 抽奖
        if (ActivityTypeEnum.LOTTERY.getCode().equals(activityType)) {
            LotteryBO lotteryBO = (LotteryBO) activityDefined;
            List<PrizeBO> prizeBOList = lotteryBO.getPrizeList();

            List<Long> couponIdList = prizeBOList.stream()
                    .filter(prizeBO -> PrizeTypeEnum.PRODUCT.getCode().equals(prizeBO.getType()) || PrizeTypeEnum.COUPON.getCode().equals(prizeBO.getType()))
                    .map(PrizeBO::getPrize)
                    .filter(prize -> Pattern.matches("^[1-9]*$", prize))
                    .map(Long::valueOf).collect(Collectors.toList());

            // 如果有奖品
            if (!CollectionUtils.isEmpty(couponIdList)) {
                if (BelongTypeEnum.PLATFORM.equals(belongTypeEnum)) {
                    Map<Long, PlatformCouponDO> platformCouponDOMap = platformCouponRepository.findAllById(couponIdList).stream().collect(Collectors.toMap(PlatformCouponDO::getId, e -> e, (e1, e2) -> e2));
                    JSONArray prizeList = jsonObject.getJSONArray("prizeList");
                    for (Object o : prizeList) {
                        JSONObject jo = (JSONObject) o;
                        Integer prizeType = jo.getInt("type");
                        if (PrizeTypeEnum.PRODUCT.getCode().equals(prizeType)) {
                            PlatformCouponDO couponDO = platformCouponDOMap.getOrDefault(jo.getLong("prize"), new PlatformCouponDO());
                            ActivityPrizeCouponResp couponDetail = new ActivityPrizeCouponResp();
                            couponDetail.setId(couponDO.getId());
                            couponDetail.setName(couponDO.getName());
                            couponDetail.setType(couponDO.getType());
                            couponDetail.setTypeName(PlatformCouponTypeEnum.getNameByCode(couponDO.getType()));
                            couponDetail.setDenomination(couponDO.getDenomination());
                            couponDetail.setGetWay(couponDO.getGetWay());
                            couponDetail.setGetWayName(PlatformCouponGetWayEnum.getNameByCode(couponDO.getGetWay()));
                            couponDetail.setUseConditionMoney(couponDO.getUseConditionMoney());
                            couponDetail.setEffectiveType(couponDO.getEffectiveType());
                            couponDetail.setEffectiveTypeName(PlatformCouponEffectiveTypeEnum.getNameByCode(couponDO.getEffectiveType()));
                            couponDetail.setEffectiveTimeStart(couponDO.getEffectiveTimeStart());
                            couponDetail.setEffectiveTimeEnd(couponDO.getEffectiveTimeEnd());
                            couponDetail.setInvalidDay(couponDO.getInvalidDay());
                            couponDetail.setCreateTime(couponDO.getCreateTime());
                            // 查询0元抵扣商品
                            CouponGoodsDO couponGoodsDO = couponGoodsRepository.findFirstByBelongTypeAndCouponId(belongTypeEnum.getCode(), couponDO.getId());
                            if (Objects.nonNull(couponGoodsDO)) {
                                SuitableProductResp suitableProductResp = new SuitableProductResp();
                                suitableProductResp.setProductId(couponGoodsDO.getProductId());
                                suitableProductResp.setSkuId(couponGoodsDO.getSkuId());
                                suitableProductResp.setProductName(couponGoodsDO.getProductName());
                                suitableProductResp.setType(couponGoodsDO.getType());
                                suitableProductResp.setCategory(couponGoodsDO.getCategory());
                                suitableProductResp.setBrand(couponGoodsDO.getBrand());
                                suitableProductResp.setUnit(couponGoodsDO.getUnit());
                                suitableProductResp.setPrice(couponGoodsDO.getPrice());
                                suitableProductResp.setProductImgUrl(couponGoodsDO.getProductImgUrl());
                                couponDetail.setSuitableProduct(suitableProductResp);
                            }

                            jo.set("coupon", couponDetail);
                        } else if (PrizeTypeEnum.COUPON.getCode().equals(prizeType)) {
                            PlatformCouponDO platformCouponDO = platformCouponDOMap.getOrDefault(jo.getLong("prize"), new PlatformCouponDO());
                            ActivityPageCouponDetailResp couponDetail = new ActivityPageCouponDetailResp();
                            couponDetail.setId(platformCouponDO.getId());
                            couponDetail.setName(platformCouponDO.getName());
                            couponDetail.setType(platformCouponDO.getType());
                            couponDetail.setTypeName(PlatformCouponTypeEnum.getNameByCode(platformCouponDO.getType()));
                            couponDetail.setReleaseTimeStart(platformCouponDO.getReleaseTimeStart());
                            couponDetail.setReleaseTimeEnd(platformCouponDO.getReleaseTimeEnd());
                            couponDetail.setDenomination(platformCouponDO.getDenomination());
                            couponDetail.setGetWay(platformCouponDO.getGetWay());
                            couponDetail.setGetWayName(PlatformCouponGetWayEnum.getNameByCode(platformCouponDO.getGetWay()));
                            couponDetail.setUseConditionMoney(platformCouponDO.getUseConditionMoney());
                            couponDetail.setCreateTime(platformCouponDO.getCreateTime());
                            couponDetail.setBelongType(belongTypeEnum.getCode());
                            couponDetail.setBelongName(belongTypeEnum.getName());
                            jo.set("coupon", couponDetail);
                        }
                    }
                } else if (BelongTypeEnum.MERCHANT.equals(belongTypeEnum)) {
                    Map<Long, MerchantCouponDO> merchantCouponDOMap = merchantCouponRepository.findAllById(couponIdList).stream().collect(Collectors.toMap(MerchantCouponDO::getId, e -> e, (e1, e2) -> e2));
                    JSONArray prizeList = jsonObject.getJSONArray("prizeList");
                    for (Object o : prizeList) {
                        JSONObject jo = (JSONObject) o;
                        Integer prizeType = jo.getInt("type");
                        if (PrizeTypeEnum.PRODUCT.getCode().equals(prizeType)) {
                            MerchantCouponDO couponDO = merchantCouponDOMap.getOrDefault(jo.getLong("prize"), new MerchantCouponDO());
                            ActivityPrizeCouponResp couponDetail = new ActivityPrizeCouponResp();
                            couponDetail.setId(couponDO.getId());
                            couponDetail.setName(couponDO.getName());
                            couponDetail.setType(couponDO.getType());
                            couponDetail.setTypeName(PlatformCouponTypeEnum.getNameByCode(couponDO.getType()));
                            couponDetail.setDenomination(couponDO.getDenomination());
                            couponDetail.setGetWay(couponDO.getGetWay());
                            couponDetail.setGetWayName(PlatformCouponGetWayEnum.getNameByCode(couponDO.getGetWay()));
                            couponDetail.setUseConditionMoney(couponDO.getUseConditionMoney());
                            couponDetail.setEffectiveType(couponDO.getEffectiveType());
                            couponDetail.setEffectiveTypeName(PlatformCouponEffectiveTypeEnum.getNameByCode(couponDO.getEffectiveType()));
                            couponDetail.setEffectiveTimeStart(couponDO.getEffectiveTimeStart());
                            couponDetail.setEffectiveTimeEnd(couponDO.getEffectiveTimeEnd());
                            couponDetail.setInvalidDay(couponDO.getInvalidDay());
                            couponDetail.setCreateTime(couponDO.getCreateTime());
                            // 查询0元抵扣商品
                            CouponGoodsDO couponGoodsDO = couponGoodsRepository.findFirstByBelongTypeAndCouponId(belongTypeEnum.getCode(), couponDO.getId());
                            if (Objects.nonNull(couponGoodsDO)) {
                                SuitableProductResp suitableProductResp = new SuitableProductResp();
                                suitableProductResp.setProductId(couponGoodsDO.getProductId());
                                suitableProductResp.setSkuId(couponGoodsDO.getSkuId());
                                suitableProductResp.setProductName(couponGoodsDO.getProductName());
                                suitableProductResp.setType(couponGoodsDO.getType());
                                suitableProductResp.setCategory(couponGoodsDO.getCategory());
                                suitableProductResp.setBrand(couponGoodsDO.getBrand());
                                suitableProductResp.setUnit(couponGoodsDO.getUnit());
                                suitableProductResp.setPrice(couponGoodsDO.getPrice());
                                suitableProductResp.setProductImgUrl(couponGoodsDO.getProductImgUrl());
                                couponDetail.setSuitableProduct(suitableProductResp);
                            }

                            jo.set("coupon", couponDetail);
                        } else if (PrizeTypeEnum.COUPON.getCode().equals(prizeType)) {
                            MerchantCouponDO couponDO = merchantCouponDOMap.getOrDefault(jo.getLong("prize"), new MerchantCouponDO());
                            ActivityPageCouponDetailResp couponDetail = new ActivityPageCouponDetailResp();
                            couponDetail.setId(couponDO.getId());
                            couponDetail.setName(couponDO.getName());
                            couponDetail.setType(couponDO.getType());
                            couponDetail.setTypeName(PlatformCouponTypeEnum.getNameByCode(couponDO.getType()));
                            couponDetail.setReleaseTimeStart(couponDO.getReleaseTimeStart());
                            couponDetail.setReleaseTimeEnd(couponDO.getReleaseTimeEnd());
                            couponDetail.setDenomination(couponDO.getDenomination());
                            couponDetail.setGetWay(couponDO.getGetWay());
                            couponDetail.setGetWayName(PlatformCouponGetWayEnum.getNameByCode(couponDO.getGetWay()));
                            couponDetail.setUseConditionMoney(couponDO.getUseConditionMoney());
                            couponDetail.setCreateTime(couponDO.getCreateTime());
                            couponDetail.setBelongType(belongTypeEnum.getCode());
                            couponDetail.setBelongName(belongTypeEnum.getName());
                            jo.set("coupon", couponDetail);
                        }
                    }
                }
            }
        }
        return jsonObject;
    }

    /**
     * 根据活动类型解析活动json
     *
     * @param activityType 活动类型
     * @param jsonObject   活动json
     * @return 返回结果
     */
    protected ActivityDefinedBO parseActivityDefinedBO(Integer activityType, JSONObject jsonObject) {
        ActivityTypeEnum activityTypeEnum = ActivityTypeEnum.findByCode(activityType);
        if (Objects.isNull(activityTypeEnum)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_NOT_EXISTS);
        }

        ActivityDefinedBO activityDefinedBO = null;
        switch (activityTypeEnum) {
            case SPECIAL_OFFER:
                activityDefinedBO = JSONUtil.toBean(jsonObject, SpecialOfferBO.class);
                break;
            case PLUMMET:
                activityDefinedBO = JSONUtil.toBean(jsonObject, PlummetBO.class);
                break;
            case DISCOUNT:
                activityDefinedBO = JSONUtil.toBean(jsonObject, DiscountBO.class);
                break;
            case FULL_QUANTITY:
                activityDefinedBO = JSONUtil.toBean(jsonObject, FullQuantityBO.class);
                break;
            case FULL_MONEY:
                activityDefinedBO = JSONUtil.toBean(jsonObject, FullMoneyBO.class);
                break;
            case GIVE:
                activityDefinedBO = JSONUtil.toBean(jsonObject, GiveBO.class);
                break;
            case MORE_PIECE:
                activityDefinedBO = JSONUtil.toBean(jsonObject, MorePieceBO.class);
                break;
            case COMBINATION:
                activityDefinedBO = JSONUtil.toBean(jsonObject, CombinationBO.class);
                break;
            case GROUP_PURCHASE:
                activityDefinedBO = JSONUtil.toBean(jsonObject, GroupPurchaseBO.class);
                break;
            case LOTTERY:
                activityDefinedBO = JSONUtil.toBean(jsonObject, LotteryBO.class);
                break;
            case BARGAIN:
                activityDefinedBO = JSONUtil.toBean(jsonObject, BargainBO.class);
                break;
            case SEC_KILL:
                activityDefinedBO = JSONUtil.toBean(jsonObject, SecKillBO.class);
                break;
            case SWAP:
                activityDefinedBO = JSONUtil.toBean(jsonObject, SwapBO.class);
                break;
            case PRE_SALE:
                activityDefinedBO = JSONUtil.toBean(jsonObject, PreSaleBO.class);
                break;
            case SET_MEAL:
                activityDefinedBO = JSONUtil.toBean(jsonObject, SetMealBO.class);
                break;
            case ATTEMPT:
                activityDefinedBO = JSONUtil.toBean(jsonObject, AttemptBO.class);
                break;
            case LABOR_DISCOUNT:
                activityDefinedBO = JSONUtil.toBean(jsonObject, LaborDiscountBO.class);
                break;
            case FULL_MONEY_NEW:
                activityDefinedBO = JSONUtil.toBean(jsonObject, FullMoneyNewBO.class);
                break;
            case FULL_QUANTITY_NEW:
                activityDefinedBO = JSONUtil.toBean(jsonObject, FullQuantityNewBO.class);
                break;
        }
        if (activityDefinedBO != null) {
            activityDefinedBO.setActivityType(activityType);
        }
        return activityDefinedBO;
    }

    /**
     * 判断活动时间
     * @param currentTimeMillis 当前时间
     * @param startTime 活动开始时间
     * @param endTime 活动结束时间
     */
    protected void checkActivityTime(Long currentTimeMillis, Long startTime, Long endTime) {
        // 判断活动时间
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_END_TIME_ERROR);
        }
        // 活动开始时间要大于当前时间
        if (currentTimeMillis >= startTime) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_START_TIME_ERROR);
        }
        // 活动结束时间要大于当前时间
        if (currentTimeMillis >= endTime) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_END_TIME_ERROR);
        }
        // 活动结束时间要大于活动开始时间
        if (startTime >= endTime) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_END_TIME_GE_ACTIVITY_START_TIME);
        }
    }

    /**
     * 校验各种活动类型对应的参数
     *
     * @param activityType      活动类型
     * @param activityDefinedBO 活动json
     */
    protected void checkActivityDefine(Integer activityType, JSONObject activityDefinedBO) {
        ActivityTypeEnum activityTypeEnum = ActivityTypeEnum.findByCode(activityType);
        if (Objects.isNull(activityTypeEnum)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_NOT_EXISTS);
        }

        switch (activityTypeEnum) {
            case SPECIAL_OFFER:
                // 特价促销
                SpecialOfferBO specialOfferBO = JSONUtil.toBean(activityDefinedBO, SpecialOfferBO.class);
                ValidatorUtil.validate(specialOfferBO);
                break;
            case PLUMMET:
                // 直降促销
                PlummetBO plummetBO = JSONUtil.toBean(activityDefinedBO, PlummetBO.class);
                ValidatorUtil.validate(plummetBO);
                break;
            case DISCOUNT:
                // 折扣促销
                DiscountBO discountBO = JSONUtil.toBean(activityDefinedBO, DiscountBO.class);
                ValidatorUtil.validate(discountBO);
                break;
            case FULL_QUANTITY:
                // 满量促销
                FullQuantityBO fullQuantityBO = JSONUtil.toBean(activityDefinedBO, FullQuantityBO.class);
                ValidatorUtil.validate(fullQuantityBO);

                // 阶梯校验
                List<FullQuantityBO.LadderBO> ladderList = fullQuantityBO.getLadderList();
                if (CollectionUtils.isEmpty(ladderList)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NOT_EMPTY);
                }
                if (!ladderList.stream().map(FullQuantityBO.LadderBO::getKey).allMatch(new HashSet<>()::add)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NO_REPEAT);
                }
                break;
            case FULL_MONEY:
                // 满额促销
                FullMoneyBO fullMoneyBO = JSONUtil.toBean(activityDefinedBO, FullMoneyBO.class);
                ValidatorUtil.validate(fullMoneyBO);

                // 阶梯校验
                if (CollectionUtils.isEmpty(fullMoneyBO.getLadderList())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NOT_EMPTY);
                }
                if (!fullMoneyBO.getLadderList().stream().map(FullMoneyBO.LadderBO::getKey).allMatch(new HashSet<>()::add)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NO_REPEAT);
                }
                break;
            case GIVE:
                // 赠送促销
                GiveBO giveBO = JSONUtil.toBean(activityDefinedBO, GiveBO.class);
                ValidatorUtil.validate(giveBO);
                break;
            case MORE_PIECE:
                // 多件促销
                MorePieceBO morePieceBO = JSONUtil.toBean(activityDefinedBO, MorePieceBO.class);
                ValidatorUtil.validate(morePieceBO);

                // 阶梯校验
                if (CollectionUtils.isEmpty(morePieceBO.getLadderList())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NOT_EMPTY);
                }
                if (!morePieceBO.getLadderList().stream().map(MorePieceBO.LadderBO::getNum).allMatch(new HashSet<>()::add)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NO_REPEAT);
                }
                if (morePieceBO.getLadderList().stream().map(MorePieceBO.LadderBO::getDiscount).anyMatch(o->o.compareTo(BigDecimal.ONE)<0)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NUM_MIN_ERROR);
                }
                break;
            case COMBINATION:
                // 组合促销
                CombinationBO combinationBO = JSONUtil.toBean(activityDefinedBO, CombinationBO.class);
                ValidatorUtil.validate(combinationBO);
                break;
            case GROUP_PURCHASE:
                // 拼团
                GroupPurchaseBO groupPurchaseBO = JSONUtil.toBean(activityDefinedBO, GroupPurchaseBO.class);
                ValidatorUtil.validate(groupPurchaseBO);

                if (Objects.isNull(groupPurchaseBO.getAssembleTime()) || 0 >= groupPurchaseBO.getAssembleTime()) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_ASSEMBLE_POSITIVE);
                }
                if(Objects.isNull(groupPurchaseBO.getAssembleNum())||groupPurchaseBO.getAssembleNum()<2){
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_ASSEMBLE_NUM_ERROR);
                }
                break;
            case LOTTERY:
                // 抽奖
                LotteryBO lotteryBO = JSONUtil.toBean(activityDefinedBO, LotteryBO.class);
                ValidatorUtil.validate(lotteryBO);

                LotteryTypeEnum lotteryTypeEnum = LotteryTypeEnum.parse(lotteryBO.getLotteryType());
                if (Objects.isNull(lotteryTypeEnum)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LOTTERY_TYPE_NOT_EXIST);
                }
                switch (lotteryTypeEnum) {
                    case ORDER:
                        if (Objects.isNull(lotteryBO.getOrderPrice()) || BigDecimal.ZERO.compareTo(lotteryBO.getOrderPrice()) >= 0) {
                            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LOTTERY_ORDER_POSITIVE);
                        }
                        break;
                    case SCORE:
                        if (Objects.isNull(lotteryBO.getIntegral()) || 0 >= lotteryBO.getIntegral()) {
                            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LOTTERY_INTEGRAL_POSITIVE);
                        }
                        break;
                    case BEHAVIOR:
                        if (Objects.isNull(lotteryBO.getBehavior())) {
                            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LOTTERY_INTEGRAL_POSITIVE_NOT_NULL);
                        }
                        break;
                    case ACTIVITY:
                        //ignore
                        break;
                }
                break;
            case BARGAIN:
                // 砍价
                BargainBO bargainBO = JSONUtil.toBean(activityDefinedBO, BargainBO.class);
                ValidatorUtil.validate(bargainBO);

                BargainTypeEnum bargainType = BargainTypeEnum.parse(bargainBO.getType());
                if (Objects.isNull(bargainType)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_BARGAIN_TYPE_NOT_EXIST);
                }

                switch (bargainType) {
                    case RANDOM:
                        if (Objects.isNull(bargainBO.getRandomStartPrice()) || Objects.isNull(bargainBO.getRandomEndPrice())) {
                            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_BARGAIN_RANDOM_PRICE_NOT_NULL);
                        }

                        if (BigDecimal.ZERO.compareTo(bargainBO.getRandomStartPrice()) >= 0 || bargainBO.getRandomStartPrice().compareTo(bargainBO.getRandomEndPrice()) >= 0) {
                            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_BARGAIN_RANDOM_PRICE_POSITIVE);
                        }
                        break;
                    case RESTRICT:
                        if (Objects.isNull(bargainBO.getRestrictPrice()) || BigDecimal.ZERO.compareTo(bargainBO.getRestrictPrice()) >= 0) {
                            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_BARGAIN_RESTRICT_PRICE_POSITIVE);
                        }
                        break;
                }
                break;
            case SEC_KILL:
                // 秒杀
                SecKillBO secKillBO = JSONUtil.toBean(activityDefinedBO, SecKillBO.class);
                ValidatorUtil.validate(secKillBO);

                if (secKillBO.getStartTime() >= secKillBO.getEndTime()) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_SEC_KILL_TIME_COMPARE);
                }
                break;
            case SWAP:
                // 换购
                SwapBO swapBO = JSONUtil.toBean(activityDefinedBO, SwapBO.class);
                ValidatorUtil.validate(swapBO);
                break;
            case PRE_SALE:
                // 预售
                PreSaleBO preSaleBO = JSONUtil.toBean(activityDefinedBO, PreSaleBO.class);
                ValidatorUtil.validate(preSaleBO);

                if (preSaleBO.getDepositPayStartTime() >= preSaleBO.getDepositPayEndTime()) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_PRE_SALE_DEPOSIT_TIME_COMPARE);
                }

                if (preSaleBO.getBalancePaymentPayStartTime() >= preSaleBO.getBalancePaymentPayEndTime()) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_PRE_SALE_BALANCE_TIME_COMPARE);
                }

                if (preSaleBO.getBalancePaymentPayEndTime() >= preSaleBO.getDeliverTime()) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_PRE_DELIVER_TIME_COMPARE);
                }
                break;
            case SET_MEAL:
                // 套餐
                SetMealBO setMealBO = JSONUtil.toBean(activityDefinedBO, SetMealBO.class);
                ValidatorUtil.validate(setMealBO);
                break;
            case ATTEMPT:
                // 试用
                AttemptBO attemptBO = JSONUtil.toBean(activityDefinedBO, AttemptBO.class);
                ValidatorUtil.validate(attemptBO);
                break;
            case LABOR_DISCOUNT:
                // 工价优惠
                LaborDiscountBO laborDiscountBO = JSONUtil.toBean(activityDefinedBO, LaborDiscountBO.class);
                ValidatorUtil.validate(laborDiscountBO);
                break;
            case FULL_MONEY_NEW:
                // 满额促销(新)
                FullMoneyNewBO fullMoneyNewBO = JSONUtil.toBean(activityDefinedBO, FullMoneyNewBO.class);
                ValidatorUtil.validate(fullMoneyNewBO);
                
                // 阶梯校验
                if (CollectionUtils.isEmpty(fullMoneyNewBO.getLadderList())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NOT_EMPTY);
                }
                if (!fullMoneyNewBO.getLadderList().stream().map(FullMoneyNewBO.LadderBO::getKey).allMatch(new HashSet<>()::add)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NO_REPEAT);
                }
                break;
            case FULL_QUANTITY_NEW:
                // 满量促销(新)
                FullQuantityNewBO fullQuantityNewBO = JSONUtil.toBean(activityDefinedBO, FullQuantityNewBO.class);
                ValidatorUtil.validate(fullQuantityNewBO);
                
                // 阶梯校验
                if (CollectionUtils.isEmpty(fullQuantityNewBO.getLadderList())) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NOT_EMPTY);
                }
                if (!fullQuantityNewBO.getLadderList().stream().map(FullQuantityNewBO.LadderBO::getKey).allMatch(new HashSet<>()::add)) {
                    throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_LADDER_NO_REPEAT);
                }
                break;
        }

        // 校验叠加活动是否超出指定范围
        JSONArray allowActivity = activityDefinedBO.getJSONArray("allowActivity");
        if (allowActivity != null) {
            List<Integer> items = allowActivity.toList(Integer.class);
            if (!ActivityTypeEnum.isValidSuperpositionItem(activityType, items)) {
                throw new BusinessException(ResponseCodeEnum.MARKETING_PLATFORM_ACTIVITY_SUPPERPOSITION_TYPE_NOT_ALLOW);
            }
        }
    }

    /**
     * 判断是否“不属于”当前会员
     *
     * @param loginUser 登录用户信息
     * @param memberId  记录的memberId
     * @param roleId    记录的roleId
     * @return true false
     */
    protected boolean notBelongCurrentMember(UserLoginCacheDTO loginUser, Long memberId, Long roleId) {
        return !loginUser.getMemberId().equals(memberId) || !loginUser.getMemberRoleId().equals(roleId);
    }

    /**
     * 获取minType
     *
     * @param activityType 活动类型
     * @param jsonObject   活动json
     * @return 返回结果
     */
    protected Integer getMinType(Integer activityType, JSONObject jsonObject) {
        ActivityTypeEnum activityTypeEnum = ActivityTypeEnum.findByCode(activityType);
        if (Objects.isNull(activityTypeEnum)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_ACTIVITY_NOT_EXISTS);
        }

        switch (activityTypeEnum) {
            case FULL_QUANTITY:
            case FULL_MONEY:
            case LABOR_DISCOUNT:
            case FULL_MONEY_NEW:
            case FULL_QUANTITY_NEW:
                return jsonObject.getInt("type");
            case GIVE:
                return jsonObject.getInt("giftType");
            case SWAP:
                return jsonObject.getInt("swapType");
            default:
                return null;
        }
    }

    /**
     * 获取allowCoupon
     */
    protected Integer getAllowCoupon(Integer activityType, JSONObject jsonObject) {
        Boolean value = jsonObject.getBool("allowCoupon");
        return Objects.isNull(value) ? null : (value ? CommonBooleanEnum.YES.getCode() : CommonBooleanEnum.NO.getCode());
    }

    /**
     * 查询活动相关属性
     * @param belongType 活动归属类型
     * @param activityId 活动Id
     * @return 活动相关属性Dto
     */
    public ActivityCommonInfoDTO getActivityInfoNoCache(Integer belongType, Long activityId){
        AtomicReference<ActivityCommonInfoDTO> dto = new AtomicReference<>(null);

        BelongTypeEnum belongTypeEnum = BelongTypeEnum.parse(belongType);
        switch (belongTypeEnum) {
            case PLATFORM:
                platformActivityRepository.findById(activityId).ifPresent(platformActivityDO -> dto.set(buildActivityCommonInfoDTO(platformActivityDO.getId(),
                        platformActivityDO.getActivityType(),
                        platformActivityDO.getStartTime(),
                        platformActivityDO.getEndTime(),
                        platformActivityDO.getActivityName(),
                        platformActivityDO.getCreateTime(),
                        PlatformActivityOuterStatusEnum.ONGOING.getCode().equals(platformActivityDO.getOuterStatus())
                )));
                break;
            case MERCHANT:
                merchantActivityRepository.findById(activityId).ifPresent(merchantActivityDO -> dto.set(buildActivityCommonInfoDTO(merchantActivityDO.getId(),
                        merchantActivityDO.getActivityType(),
                        merchantActivityDO.getStartTime(),
                        merchantActivityDO.getEndTime(),
                        merchantActivityDO.getActivityName(),
                        merchantActivityDO.getCreateTime(),
                        MerchantActivityInnerStatusEnum.ONGOING.getCode().equals(merchantActivityDO.getInnerStatus())
                )));
                break;
        }
        return dto.get();
    }

    private ActivityCommonInfoDTO buildActivityCommonInfoDTO(Long id,Integer activityType,Long startTime,Long endTime,String activityName,Long createTime,Boolean status){
        ActivityCommonInfoDTO dto = new ActivityCommonInfoDTO();
        dto.setId(id);
        dto.setActivityType(activityType);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setActivityName(activityName);
        dto.setCreateTime(createTime);
        dto.setOnline(status);
        return dto;
    }

    //查询活动数据（缓存8小时）
    public ActivityCommonInfoDTO getActivityInfo(Integer belongType, Long activityId){
        String cacheKey = MarketingRedisKeyConstant.getTempKeyPrefix("act", belongType, activityId);
        String act = redisUtils.stringGet(cacheKey, RedisConstant.REDIS_MARKETING_INDEX);
        if(StringUtils.hasLength(act)){
            return JSONUtil.toBean(act,ActivityCommonInfoDTO.class);
        }
        ActivityCommonInfoDTO dto=getActivityInfoNoCache(belongType,activityId);
        if(dto!=null){
            redisUtils.stringSet(cacheKey,JSONUtil.toJsonStr(dto), MarketingRedisKeyConstant.TEMP_EXPIRED, RedisConstant.REDIS_MARKETING_INDEX);
        }
        return dto;
    }
}