package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.model.vo.activity.request.*;
import com.ssy.lingxi.marketing.model.vo.activity.response.*;
import com.ssy.lingxi.marketing.model.vo.coupon.request.FilterSkuIdReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.FilterSkuIdResp;

import java.util.List;

/**
 * 商家活动服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/17
 */
public interface IMerchantActivityService {

    /**
     * 营销活动查询 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<MerchantActivitySummaryPageResp> pageSummary(UserLoginCacheDTO loginUser, MerchantActivityCommonPageDataReq pageVO);

    /**
     * 待提交审核营销活动 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<MerchantActivitySubmitExamPageResp> pageByToBeSubmitExam(UserLoginCacheDTO loginUser, MerchantActivityCommonPageDataReq pageVO);

    /**
     * 待审核营销活动(一级) - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<MerchantActivityExamPageResp> pageByExamineStep1(UserLoginCacheDTO loginUser, MerchantActivityCommonPageDataReq pageVO);

    /**
     * 待审核营销活动 (二级) - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<MerchantActivityExamPageResp> pageByExamineStep2(UserLoginCacheDTO loginUser, MerchantActivityCommonPageDataReq pageVO);

    /**
     * 待提交营销活动 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<MerchantActivitySubmitPageResp> pageByToBeSubmit(UserLoginCacheDTO loginUser, MerchantActivityCommonPageDataReq pageVO);

    /**
     * 待上线营销活动 - 分页列表
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<MerchantActivityOnlinePageResp> pageByToBeOnline(UserLoginCacheDTO loginUser, MerchantActivityCommonPageDataReq pageVO);

    /**
     * 查询商家活动
     * @param request 接口参数
     * @return 返回结果
     */
    MerchantActivityDetailResp detail(UserLoginCacheDTO loginUser, CommonIdReq request);

    /**
     * 查询商家活动基本信息
     * @param loginUser 登录用户
     * @param request 接口参数
     * @return 查询结果
     */
    MerchantActivityDetailResp getBaseDetail(UserLoginCacheDTO loginUser, CommonIdReq request);
    /**
     * 营销活动查询 - 活动详情 - 活动商品(分页)
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<McActivityGoodsPageResp> pageByActivityGoods(UserLoginCacheDTO loginUser, PfActivityGoodsPageDataReq pageVO);

    /**
     * 待新增商家营销活动 - 新增
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void saveMerchantActivity(UserLoginCacheDTO loginUser, MerchantActivityAddReq request);

    /**
     * 新增商家营销活动 - 修改
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void updateMerchantActivity(UserLoginCacheDTO loginUser, MerchantActivityUpdateReq request);

    /**
     * 待新增商家营销活动 - 查询活动商品过滤的skuId
     * @param loginUser 登录用户信息
     * @param request 接口参数
     * @return 返回结果
     */
    FilterSkuIdResp getFilterSkuId(UserLoginCacheDTO loginUser, FilterSkuIdReq request);

    /**
     * 待提交审核营销活动 - 批量删除
     * @param loginUser 登录用户信息
     * @param idList 活动id
     */
    Void batchDelete(UserLoginCacheDTO loginUser, List<Long> idList);

    /**
     * 待提交审核营销活动 - 提交审核
     * @param loginUser 登录用户信息
     */
    Void submitExamine(UserLoginCacheDTO loginUser, List<Long> idList);

    /**
     * 待审核营销活动 (一级) - 审核
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void examineStep1Update(UserLoginCacheDTO loginUser, ActivityExamineReq request);

    /**
     * 待审核营销活动 (一级) - 批量审核
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void batchExamineStep1Update(UserLoginCacheDTO loginUser, CommonIdListReq request);

    /**
     * 待审核营销活动 (二级) - 审核
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void examineStep2Update(UserLoginCacheDTO loginUser, ActivityExamineReq request);

    /**
     * 待审核营销活动 (二级) - 批量审核
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void batchExamineStep2Update(UserLoginCacheDTO loginUser, CommonIdListReq request);

    /**
     * 待提交营销活动 (二级) -  提交
     * @param loginUser 登录用户信息
     * @param idList 活动id
     */
    Void submit(UserLoginCacheDTO loginUser, List<Long> idList);

    /**
     * 待上线营销活动 - 上线活动
     * @param loginUser 登录用户信息
     * @param idList 活动id
     */
    Void onlineUpdate(UserLoginCacheDTO loginUser, List<Long> idList);

    /**
     * 营销活动查询 - 终止
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void stop(UserLoginCacheDTO loginUser, ActivityActionReq request);

    /**
     * 营销活动查询 - 重新启动
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void restart(UserLoginCacheDTO loginUser, ActivityActionReq request);
}
