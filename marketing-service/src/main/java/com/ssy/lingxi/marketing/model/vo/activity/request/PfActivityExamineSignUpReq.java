package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *  平台营销活动报名-待审核报名资料-审核-接口参数VO
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@Getter
@Setter
public class PfActivityExamineSignUpReq implements Serializable {

    private static final long serialVersionUID = -5653955772547179775L;
    /**
     * 报名id
     */
    @NotNull(message = "报名id不能为空")
    @Range(min = 1,message = "报名id必须大于0")
    private Long id;

    /**
     * 是否同意： 0：不同意，1：同意
     */
    @NotNull(message = "报名id不能为空")
    @Range(min = 0,max = 1,message = "是否同意取值范围： 0~1 ")
    private Integer agree;

    /**
     * 不通过原因
     */
    private String reason;
}
