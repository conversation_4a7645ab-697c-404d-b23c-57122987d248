package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;

import java.io.Serializable;

/**
 *  分页查询平台活动列表-响应VO
 * <AUTHOR>
 * @since 2021/6/19
 * @version 2.0.0
 */
@Data
public class PlatformActivityPageResp implements Serializable {

    private static final long serialVersionUID = 4710608149607694613L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 活动类型名称
     */
    private String activityTypeName;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 报名开始时间
     * */
    private Long signUpStartTime;

    /**
     * 报名结束时间
     * */
    private Long signUpEndTime;

    /**
     * 外部状态
     * */
    private Integer outerStatus;

    /**
     * 内部状态
     * */
    private Integer innerStatus;

    /**
     * 外部状态名称
     * */
    private String outerStatusName;

    /**
     * 内部状态名称
     * */
    private String innerStatusName;
}
