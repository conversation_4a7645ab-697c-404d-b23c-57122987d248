package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 能力中心-平台活动执行列表返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/25
 */
@Data
public class PlatformActivityExecuteAbilityPageResp implements Serializable {

    private static final long serialVersionUID = -468824117418137297L;
    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 活动类型名称
     */
    private String activityTypeName;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动报名时间
     */
    private Long signupTime;

    /**
     * 活动报名id
     */
    private Long signupId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 参与客户数
     */
    private Integer customerCount;
    /**
     * 已执行订单单数
     */
    private Integer orderCount;
    /**
     * 已执行订单金额
     */
    private BigDecimal orderAmount;
}
