package com.ssy.lingxi.marketing.controller.activity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.marketing.ActivityTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.activity.request.*;
import com.ssy.lingxi.marketing.model.vo.activity.response.*;
import com.ssy.lingxi.marketing.model.vo.common.response.PageItemResp;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ActivityMerchantPrizeCouponDataReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ActivityMerchantSubsidiaryCouponDataReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.FilterSkuIdReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityGoodsCouponResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityPrizeCouponResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.FilterSkuIdResp;
import com.ssy.lingxi.marketing.service.ICommonService;
import com.ssy.lingxi.marketing.service.IMerchantCouponService;
import com.ssy.lingxi.marketing.service.IPlatformActivitySignUpService;
import com.ssy.lingxi.marketing.serviceImpl.base.activity.BaseActivityRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 营销能力-平台营销活动报名
 * <AUTHOR> yzc
 * @since 2021/6/18
 * @version 2.0.0
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/platform/activity/signup")
public class PlatformActivitySignUpController extends BaseController {

    @Resource
    private IPlatformActivitySignUpService platformActivitySignUpService;
    @Resource
    private ICommonService commonService;
    @Resource
    private BaseActivityRecordService activityRecordService;
    @Resource
    private IMerchantCouponService merchantCouponService;

    /**
     * 平台营销活动查询 - 外部状态列表
     */
    @GetMapping("/getOuterStatusList")
    public WrapperResp<List<PageItemResp>> getOuterStatusList() {
        return WrapperUtil.success(commonService.getPlatformActivitySignUpOuterStatuses());
    }

    /**
     * 平台营销活动查询 - 内部状态列表
     */
    @GetMapping("/getInnerStatusList")
    public WrapperResp<List<PageItemResp>> getStatusList() {
        return WrapperUtil.success(commonService.getPlatformActivitySignUpInnerStatuses());
    }

    /**
     * 平台营销活动查询 - 活动类型列表
     */
    @GetMapping("/getActivityTypeList")
    public WrapperResp<List<PageItemResp>> getActivityTypeList() {
        return WrapperUtil.success(commonService.getActivityTypes(Collections.singletonList(ActivityTypeEnum.LOTTERY.getCode())));
    }

    /**
     * 平台营销活动查询 - 分页列表
     **/
    @GetMapping("/page")
    public WrapperResp<PageDataResp<PfActivitySignUpPageResp>> pageSummary(@Valid PfActivitySignUpPageDataReq pageVO) {
        return WrapperUtil.success(platformActivitySignUpService.pageSummary(getSysUser(), pageVO));
    }

    /**
     * 平台营销活动查询 - 活动详情
     **/
    @GetMapping("/detail")
    public WrapperResp<PfActivitySignUpDetailResp> detail(@RequestParam("activityId") Long activityId) {
        return WrapperUtil.success(platformActivitySignUpService.detail(getSysUser(), activityId));
    }

    /**
     * 平台营销活动查询 - 活动详情 - 活动商品(分页)
     **/
    @GetMapping("/detail/goods/page")
    public WrapperResp<PageDataResp<PfActivitySignUpGoodsPageResp>> activityGoodsPage(@Valid PfActivitySignUpGoodsPageDataReq pageVO) {
        return WrapperUtil.success(platformActivitySignUpService.pageByActivityGoods(getSysUser(), pageVO));
    }

    /**
     * 平台营销活动查询 - 活动详情 - 活动商品 - 选择附属优惠券查询条件
     * @return 返回结果
     */
    @GetMapping("/detail/goods/coupon/select/condition")
    public WrapperResp<List<SelectItemResp>> selectSubsidiaryCouponCondition() {
        return WrapperUtil.success(merchantCouponService.selectSubsidiaryCouponCondition());
    }

    /**
     * 平台营销活动查询 - 活动详情 - 活动商品 - 选择附属优惠券
     **/
    @GetMapping("/detail/goods/coupon/select")
    public WrapperResp<PageDataResp<ActivityGoodsCouponResp>> selectSubsidiaryCouponList(@Valid ActivityMerchantSubsidiaryCouponDataReq request) {
        return WrapperUtil.success(merchantCouponService.selectSubsidiaryCouponList(request, getSysUser()));
    }

    /**
     * 平台营销活动查询 - 活动详情 - 奖品 - 选择0元购买抵扣券
     **/
    @GetMapping("/detail/prize/coupon/select")
    public WrapperResp<PageDataResp<ActivityPrizeCouponResp>> selectPrizeCouponList(@Valid ActivityMerchantPrizeCouponDataReq request) {
        return WrapperUtil.success(merchantCouponService.selectPrizeCouponList(request, getSysUser()));
    }

    /**
     * 平台营销活动查询 - 详情 - 外部流转记录(分页)
     */
    @GetMapping("/detail/outer/record/page")
    public WrapperResp<PageDataResp<ActivityOuterRecordResp>> pageOuterRecordList(@Valid ActivityOuterRecordPageDataReq pageVO) {
        return WrapperUtil.success(activityRecordService.pagePlatformActivitySignUpOuterRecord(getSysUser(), pageVO));
    }

    /**
     * 平台营销活动查询 - 详情 - 内部流转记录(分页)
     */
    @GetMapping("/detail/inner/record/page")
    public WrapperResp<PageDataResp<ActivityInnerRecordResp>> pageInnerRecordList(@Valid ActivityInnerRecordPageDataReq pageVO) {
        return WrapperUtil.success(activityRecordService.pagePlatformActivitySignUpInnerRecord(getSysUser(), pageVO));
    }

    /**
     * 待提交审核报名资料 - 分页列表
     **/
    @GetMapping("/page/tobe/submit")
    public WrapperResp<PageDataResp<PfActivitySignUpSubmitExamPageResp>> pageByToBeSubmit(@Valid PfActivitySignUpPageDataReq pageVO) {
        return WrapperUtil.success(platformActivitySignUpService.pageByToBeSubmit(getSysUser(), pageVO));
    }

    /**
     * 待提交审核报名资料 - 填写报名资料
     */
    @PostMapping("/save")
    public WrapperResp<Void> save(@RequestBody @Valid PfActivitySignUpAddReq pageVO) {
        return WrapperUtil.success(platformActivitySignUpService.save(getSysUser(), pageVO));
    }

    /**
     * 待提交审核报名资料 - 修改报名资料
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(@RequestBody @Valid PfActivitySignUpUpdateReq pageVO) {
        return WrapperUtil.success(platformActivitySignUpService.update(getSysUser(), pageVO));
    }

    /**
     * 待提交审核报名资料 - 查询活动商品过滤的skuId
     */
    @PostMapping("/getFilterSkuId")
    public WrapperResp<FilterSkuIdResp> getFilterSkuId(@RequestBody @Valid FilterSkuIdReq req) {
        return WrapperUtil.success(platformActivitySignUpService.getFilterSkuId(getSysUser(), req));
    }

    /**
     * 待提交审核报名资料 - 提交审核
     **/
    @PostMapping("/submit")
    public WrapperResp<Void> submitExamine(@RequestBody @Valid PfActivityExamineSubmitReq submitReq) {
        return WrapperUtil.success(platformActivitySignUpService.submitExamine(getSysUser(), submitReq));
    }

    /**
     * 待提交审核报名资料 - 批量提交
     **/
    @PostMapping("/submit/batch")
    public WrapperResp<Void> submitExamineBatch(@RequestBody @Valid PfActivitySingUpIdsReq req) {
        return WrapperUtil.success(platformActivitySignUpService.submitExamineBatch(getSysUser(), req));
    }

    /**
     * 待审核报名资料(一级) - 分页列表
     **/
    @GetMapping("/page/tobe/examine/step1")
    public WrapperResp<PageDataResp<PfActivitySignUpExamPageResp>> pageByToBeExamineStep1(@Valid PfActivitySignUpPageDataReq pageVO) {
        return WrapperUtil.success(platformActivitySignUpService.pageByToBeExamineStep1(getSysUser(), pageVO));
    }

    /**
     * 审核报名资料(一级) - 审核
     **/
    @PostMapping("/examine/step1")
    public WrapperResp<Void> examineStep1(@RequestBody @Valid PfActivityExamineSignUpReq req) {
        return WrapperUtil.success(platformActivitySignUpService.examineStep1(getSysUser(), req));
    }

    /**
     * 审核报名资料(一级) - 批量审核
     **/
    @PostMapping("/examine/step1/batch")
    public WrapperResp<Void> examineStep1Batch(@RequestBody @Valid PfActivitySingUpIdsReq req) {
        return WrapperUtil.success(platformActivitySignUpService.examineStep1batch(getSysUser(), req));
    }

    /**
     * 待审核报名资料(二级)  - 分页列表
     **/
    @GetMapping("/page/tobe/examine/step2")
    public WrapperResp<PageDataResp<PfActivitySignUpExamPageResp>> pageByToBeExamineStep2(@Valid PfActivitySignUpPageDataReq pageVO) {
        return WrapperUtil.success(platformActivitySignUpService.pageByToBeExamineStep2(getSysUser(), pageVO));
    }

    /**
     * 审核报名资料(二级) - 审核
     **/
    @PostMapping("/examine/step2")
    public WrapperResp<Void> examineStep2(@RequestBody @Valid PfActivityExamineSignUpReq req) {
        return WrapperUtil.success(platformActivitySignUpService.examineStep2(getSysUser(), req));
    }

    /**
     * 审核报名资料(二级) - 批量审核
     **/
    @PostMapping("/examine/step2/batch")
    public WrapperResp<Void> examineStep2(@RequestBody @Valid PfActivitySingUpIdsReq req) {
        return WrapperUtil.success(platformActivitySignUpService.examineStep2batch(getSysUser(), req));
    }

    /**
     * 待提交报名资料  - 分页列表
     **/
    @GetMapping("/page/tobe/submit/sign/up")
    public WrapperResp<PageDataResp<PfActivitySignUpSubmitPageResp>> pageByToBeSubmitSignUp(@Valid PfActivitySignUpPageDataReq pageVO) {
        return WrapperUtil.success(platformActivitySignUpService.pageByToBeSubmitSignUp(getSysUser(), pageVO));
    }

    /**
     * 待提交报名资料 - 提交
     **/
    @PostMapping("/submit/sign/up")
    public WrapperResp<Void> submitSignUp(@RequestBody @Valid PfActivitySingUpIdReq req) {
        return WrapperUtil.success(platformActivitySignUpService.submitSignUp(getSysUser(), req.getId()));
    }

    /**
     * 待提交报名资料 - 批量提交
     */
    @PostMapping("/submit/sign/up/batch")
    public WrapperResp<Void> submitSignUpBatch(@Valid @RequestBody PfActivitySingUpIdsReq req) {
        return WrapperUtil.success(platformActivitySignUpService.submitSignUpBatch(getSysUser(), req.getIds()));
    }
}
