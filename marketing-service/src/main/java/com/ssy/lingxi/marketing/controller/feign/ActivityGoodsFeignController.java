package com.ssy.lingxi.marketing.controller.feign;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.api.feign.IActivityGoodsFeign;
import com.ssy.lingxi.marketing.api.model.request.*;
import com.ssy.lingxi.marketing.api.model.response.CartActivityPriceFeignResp;
import com.ssy.lingxi.marketing.api.model.response.GoodsCartResp;
import com.ssy.lingxi.marketing.api.model.response.ProductTagResp;
import com.ssy.lingxi.marketing.service.IActivityGoodsCacheService;
import com.ssy.lingxi.marketing.service.IActivityOrderService;
import com.ssy.lingxi.marketing.service.IActivityPriceCalculateService;
import com.ssy.lingxi.marketing.service.feign.IActivityGoodsFeignService;
import org.springframework.context.annotation.Primary;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 活动商品feign接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/2
 * @ignore 不需要提交到Yapi
 */
@Primary
@RestController
public class ActivityGoodsFeignController implements IActivityGoodsFeign {

    @Resource
    private IActivityPriceCalculateService activityPriceCalculateService;
    @Resource
    private IActivityGoodsCacheService activityGoodsCacheService;
    @Resource
    private IActivityGoodsFeignService activityGoodsFeignService;
    @Resource
    private IActivityOrderService activityOrderService;

    /**
     * 内部接口 - 查询活动商品活动标签
     *
     * @param req 接口參數
     */
    @Override
    public WrapperResp<List<ProductTagResp>> listActivityGoodsProductTag(ProductTagReq req) {
        return WrapperUtil.success(activityGoodsFeignService.listActivityGoodsProductTag(req));
    }

    /**
     * 内部接口 - 进货单获取sku商品活动
     **/
    @Override
    public WrapperResp<List<GoodsCartResp>> listActivityGoodsCart(@Valid @RequestBody GoodsCartReq request) {
        return WrapperUtil.success(activityGoodsFeignService.listActivityGoodsCart(request));
    }

    /**
     * 获取商品的最大优惠价格
     **/
    @Override
    public WrapperResp<List<ProductTagResp>> getPreferentialPrice(ProductTagReq req) {
        return WrapperUtil.success(activityGoodsFeignService.getPreferentialPrice(req));
    }

    /**
     * 更新营销活动商品销量[新增订单，取消订单，售后退货]
     */
    @Override
    public WrapperResp<?> updateGoodsSales(GoodsSalesReq req) {
        return WrapperUtil.success(activityGoodsCacheService.updateGoodsSales(req));
    }

    /**
     * 内部接口 - 计算活动到手价
     **/
    @Override
    public WrapperResp<List<CartActivityPriceFeignResp>> getPreferentialPrice(CartActivityPriceFeignReq request) {
        if(CollectionUtils.isEmpty(request.getSkuList())){return WrapperUtil.success();}
        UserLoginCacheDTO user=new UserLoginCacheDTO();
        user.setMemberId(request.getMemberId());
        user.setMemberRoleId(request.getRoleId());
        return WrapperUtil.success(activityPriceCalculateService.calculateActivityPriceByFeign(user,request.getSkuList()));
    }

    /**
     * 添加拼团
     **/
    @Override
    public WrapperResp<Long> addGroupPurchase(GroupPurchaseAddReq req) {
        return WrapperUtil.success(activityOrderService.addGroupPurchase(req));
    }

    /**
     * 校验是否重复参与同一个拼团
     */
    @Override
    public WrapperResp<Boolean> isRepeatJoinGroupPurchase(GroupPurchaseCheckReq req){
        return WrapperUtil.success(activityOrderService.isRepeatJoinGroupPurchase(req));
    }

}
