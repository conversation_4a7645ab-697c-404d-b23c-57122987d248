package com.ssy.lingxi.marketing.strategy.impl;

import com.ssy.lingxi.marketing.entity.activity.ActivityGoodsDO;
import com.ssy.lingxi.marketing.entity.activity.MerchantActivityDO;
import com.ssy.lingxi.marketing.model.dto.WorkFeeDiscountResult;
import com.ssy.lingxi.marketing.model.dto.ProductWorkFeeInfo;
import com.ssy.lingxi.marketing.strategy.WorkFeeDiscountStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 19-满量促销（新）策略实现
 * 满一定数量享受工费优惠
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/25
 */
@Slf4j
@Component
public class FullQuantityPromotionNewStrategy implements WorkFeeDiscountStrategy {

    private static final Integer ACTIVITY_TYPE = 19;

    @Override
    public Integer getSupportedActivityType() {
        return ACTIVITY_TYPE;
    }

    @Override
    public WorkFeeDiscountResult calculateWorkFeeDiscount(MerchantActivityDO activity, 
                                                          List<ActivityGoodsDO> activityGoods,
                                                          ProductWorkFeeInfo productWorkFeeInfo,
                                                          BigDecimal purchaseQuantity, 
                                                          BigDecimal purchaseAmount) {
        WorkFeeDiscountResult result = new WorkFeeDiscountResult();
        result.setNeedThreshold(true);
        
        if (productWorkFeeInfo == null || !productWorkFeeInfo.getHasWorkFee()) {
            log.warn("商品没有工费信息, skuId: {}", productWorkFeeInfo != null ? productWorkFeeInfo.getSkuId() : "null");
            return result;
        }

        // 使用商品服务查询到的工费信息
        BigDecimal totalOriginalWorkFee = productWorkFeeInfo.getTotalWorkFee();
        result.setOriginalWorkFee(totalOriginalWorkFee);

        if (totalOriginalWorkFee.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("工费为0或负数, activityId: {}, workFee: {}", activity.getId(), totalOriginalWorkFee);
            return result;
        }

        // 获取满量门槛和优惠设置
        BigDecimal thresholdQuantity = getThresholdQuantity(activityGoods);
        BigDecimal workFeeDiscountRate = getWorkFeeDiscountRate(activityGoods);
        
        result.setThresholdDescription("满" + thresholdQuantity.stripTrailingZeros().toPlainString() + "件");
        
        if (purchaseQuantity == null) {
            purchaseQuantity = BigDecimal.ZERO;
        }

        // 检查是否满足门槛条件
        if (purchaseQuantity.compareTo(thresholdQuantity) < 0) {
            // 未满足条件
            BigDecimal gap = thresholdQuantity.subtract(purchaseQuantity);
            result.setThresholdGap(gap);
            
            BigDecimal discountPercent = BigDecimal.ONE.subtract(workFeeDiscountRate).multiply(new BigDecimal("100"));
            result.setDiscountTypeDescription("满" + thresholdQuantity.stripTrailingZeros().toPlainString() + 
                "件工费享" + workFeeDiscountRate.multiply(BigDecimal.TEN).setScale(0) + "折");
            result.setActivityDescription(getActivityDescription(activity));
            
            log.info("未满足满量促销条件, activityId: {}, 购买数量: {}, 门槛: {}, 差额: {}", 
                    activity.getId(), purchaseQuantity, thresholdQuantity, gap);
            return result;
        }

        // 满足条件，计算优惠后的工费
        BigDecimal discountedWorkFee = totalOriginalWorkFee.multiply(workFeeDiscountRate)
            .setScale(2, RoundingMode.HALF_UP);
        BigDecimal workFeeDiscountAmount = totalOriginalWorkFee.subtract(discountedWorkFee);

        result.setSatisfied(true);
        result.setDiscountedWorkFee(discountedWorkFee);
        result.setWorkFeeDiscountAmount(workFeeDiscountAmount);
        result.setWorkFeeDiscountRate(workFeeDiscountRate);
        result.setDiscountTypeDescription("满" + thresholdQuantity.stripTrailingZeros().toPlainString() + 
            "件工费享" + workFeeDiscountRate.multiply(BigDecimal.TEN).setScale(0) + "折");
        result.setActivityDescription(getActivityDescription(activity));

        log.info("满量促销计算完成, activityId: {}, 购买数量: {}, 原工费: {}, 优惠后工费: {}, 优惠金额: {}", 
                activity.getId(), purchaseQuantity, totalOriginalWorkFee, discountedWorkFee, workFeeDiscountAmount);

        return result;
    }

    @Override
    public String getActivityDescription(MerchantActivityDO activity) {
        return "满量促销 - " + activity.getActivityName();
    }

    /**
     * 计算总工费
     */
    private BigDecimal calculateTotalWorkFee(List<ActivityGoodsDO> activityGoods, BigDecimal purchaseQuantity) {
        final BigDecimal finalQuantity = (purchaseQuantity == null || purchaseQuantity.compareTo(BigDecimal.ZERO) <= 0) 
            ? BigDecimal.ONE : purchaseQuantity;

        return activityGoods.stream()
            .filter(goods -> goods.getPrice() != null)
            .map(goods -> goods.getPrice().multiply(finalQuantity))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取满量门槛
     * 从活动商品的相关字段获取，这里假设使用restrictNum字段存储门槛数量
     */
    private BigDecimal getThresholdQuantity(List<ActivityGoodsDO> activityGoods) {
        return activityGoods.stream()
            .filter(goods -> goods.getRestrictNum() != null && goods.getRestrictNum() > 0)
            .findFirst()
            .map(goods -> new BigDecimal(goods.getRestrictNum()))
            .orElse(new BigDecimal("2")); // 默认满2件
    }

    /**
     * 获取工费折扣率
     * 从活动商品的折扣字段中获取
     */
    private BigDecimal getWorkFeeDiscountRate(List<ActivityGoodsDO> activityGoods) {
        return activityGoods.stream()
            .filter(goods -> goods.getDiscount() != null && goods.getDiscount().compareTo(BigDecimal.ZERO) > 0)
            .findFirst()
            .map(goods -> goods.getDiscount().divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP))
            .orElse(new BigDecimal("0.8")); // 默认8折
    }
}