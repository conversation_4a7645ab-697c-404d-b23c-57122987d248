package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 活动标签返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/1
 */
@Getter
@Setter
public class MobileGoodsRelationTagResp implements Serializable {

    private static final long serialVersionUID = -1284106139690376921L;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 所属类型
     */
    private Integer belongType;

    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     */
    private Integer activityType;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 商品id
     */
    private List<MobileActivityRelationGoodsResp> commodityList;

    /**
     * 活动描述
     */
    private String activityDescription;
    /**
     * 主商品
     */
    private MobileActivityRelationGoodsResp mainGoods;

    /**
     * 换购类型：1-满额换购 2-买商品换购
     * */
    private Integer swapType;
}
