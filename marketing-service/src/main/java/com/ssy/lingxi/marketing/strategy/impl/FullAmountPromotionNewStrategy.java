package com.ssy.lingxi.marketing.strategy.impl;

import com.ssy.lingxi.marketing.entity.activity.ActivityGoodsDO;
import com.ssy.lingxi.marketing.entity.activity.MerchantActivityDO;
import com.ssy.lingxi.marketing.model.dto.WorkFeeDiscountResult;
import com.ssy.lingxi.marketing.model.dto.ProductWorkFeeInfo;
import com.ssy.lingxi.marketing.strategy.WorkFeeDiscountStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 18-满额促销（新）策略实现
 * 满一定金额享受工费优惠
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/25
 */
@Slf4j
@Component
public class FullAmountPromotionNewStrategy implements WorkFeeDiscountStrategy {

    private static final Integer ACTIVITY_TYPE = 18;

    @Override
    public Integer getSupportedActivityType() {
        return ACTIVITY_TYPE;
    }

    @Override
    public WorkFeeDiscountResult calculateWorkFeeDiscount(MerchantActivityDO activity, 
                                                          List<ActivityGoodsDO> activityGoods,
                                                          ProductWorkFeeInfo productWorkFeeInfo,
                                                          BigDecimal purchaseQuantity, 
                                                          BigDecimal purchaseAmount) {
        WorkFeeDiscountResult result = new WorkFeeDiscountResult();
        result.setNeedThreshold(true);
        
        if (productWorkFeeInfo == null || !productWorkFeeInfo.getHasWorkFee()) {
            log.warn("商品没有工费信息, skuId: {}", productWorkFeeInfo != null ? productWorkFeeInfo.getSkuId() : "null");
            return result;
        }

        // 使用商品服务查询到的工费信息
        BigDecimal totalOriginalWorkFee = productWorkFeeInfo.getTotalWorkFee();
        result.setOriginalWorkFee(totalOriginalWorkFee);

        if (totalOriginalWorkFee.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("工费为0或负数, activityId: {}, workFee: {}", activity.getId(), totalOriginalWorkFee);
            return result;
        }

        // 获取满额门槛和优惠设置
        BigDecimal thresholdAmount = getThresholdAmount(activityGoods);
        BigDecimal workFeeDiscountAmount = getWorkFeeDiscountAmount(activityGoods);
        
        result.setThresholdDescription("满" + thresholdAmount + "元");
        
        if (purchaseAmount == null) {
            purchaseAmount = BigDecimal.ZERO;
        }

        // 检查是否满足门槛条件
        if (purchaseAmount.compareTo(thresholdAmount) < 0) {
            // 未满足条件
            BigDecimal gap = thresholdAmount.subtract(purchaseAmount);
            result.setThresholdGap(gap);
            result.setDiscountTypeDescription("满" + thresholdAmount + "元工费减" + workFeeDiscountAmount + "元");
            result.setActivityDescription(getActivityDescription(activity));
            
            log.info("未满足满额促销条件, activityId: {}, 购买金额: {}, 门槛: {}, 差额: {}", 
                    activity.getId(), purchaseAmount, thresholdAmount, gap);
            return result;
        }

        // 满足条件，计算优惠后的工费
        BigDecimal discountedWorkFee = totalOriginalWorkFee.subtract(workFeeDiscountAmount);
        if (discountedWorkFee.compareTo(BigDecimal.ZERO) < 0) {
            discountedWorkFee = BigDecimal.ZERO; // 工费不能为负数
            workFeeDiscountAmount = totalOriginalWorkFee;
        }

        result.setSatisfied(true);
        result.setDiscountedWorkFee(discountedWorkFee);
        result.setWorkFeeDiscountAmount(workFeeDiscountAmount);
        result.setWorkFeeDiscountRate(
            totalOriginalWorkFee.compareTo(BigDecimal.ZERO) > 0 
                ? discountedWorkFee.divide(totalOriginalWorkFee, 4, RoundingMode.HALF_UP)
                : BigDecimal.ONE
        );
        result.setDiscountTypeDescription("满" + thresholdAmount + "元工费减" + workFeeDiscountAmount + "元");
        result.setActivityDescription(getActivityDescription(activity));

        log.info("满额促销计算完成, activityId: {}, 购买金额: {}, 原工费: {}, 优惠后工费: {}, 优惠金额: {}", 
                activity.getId(), purchaseAmount, totalOriginalWorkFee, discountedWorkFee, workFeeDiscountAmount);

        return result;
    }

    @Override
    public String getActivityDescription(MerchantActivityDO activity) {
        return "满额促销 - " + activity.getActivityName();
    }

    /**
     * 计算总工费
     */
    private BigDecimal calculateTotalWorkFee(List<ActivityGoodsDO> activityGoods, BigDecimal purchaseQuantity) {
        final BigDecimal finalQuantity = (purchaseQuantity == null || purchaseQuantity.compareTo(BigDecimal.ZERO) <= 0) 
            ? BigDecimal.ONE : purchaseQuantity;

        return activityGoods.stream()
            .filter(goods -> goods.getPrice() != null)
            .map(goods -> goods.getPrice().multiply(finalQuantity))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取满额门槛
     * 从活动商品的相关字段获取，这里假设使用activityPrice字段存储门槛金额
     */
    private BigDecimal getThresholdAmount(List<ActivityGoodsDO> activityGoods) {
        return activityGoods.stream()
            .filter(goods -> goods.getActivityPrice() != null && goods.getActivityPrice().compareTo(BigDecimal.ZERO) > 0)
            .findFirst()
            .map(ActivityGoodsDO::getActivityPrice)
            .orElse(new BigDecimal("100")); // 默认满100元
    }

    /**
     * 获取工费优惠金额
     * 从活动商品的相关字段获取，这里假设使用plummetPrice字段存储工费优惠金额
     */
    private BigDecimal getWorkFeeDiscountAmount(List<ActivityGoodsDO> activityGoods) {
        return activityGoods.stream()
            .filter(goods -> goods.getPlummetPrice() != null && goods.getPlummetPrice().compareTo(BigDecimal.ZERO) > 0)
            .findFirst()
            .map(ActivityGoodsDO::getPlummetPrice)
            .orElse(new BigDecimal("10")); // 默认工费减10元
    }
}