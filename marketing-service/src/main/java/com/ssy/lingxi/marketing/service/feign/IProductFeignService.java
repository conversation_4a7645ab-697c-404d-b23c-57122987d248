package com.ssy.lingxi.marketing.service.feign;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.product.api.model.req.TemplateCommoditySearchReq;
import com.ssy.lingxi.product.api.model.req.feign.CommodityPriceReq;
import com.ssy.lingxi.product.api.model.resp.BrandResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuPublishResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuStockResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CustomerCategoryResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.api.model.resp.feign.CommodityPriceResp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品内部接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/7/19
 */
public interface IProductFeignService {

    /**
     * 查询品类信息
     * @param idList 品类id
     * @return 查询结果
     */
    WrapperResp<List<CustomerCategoryResp>> getCustomerCategoryById(List<Long> idList);

    /**
     * 查询品牌信息
     * @param idList 品牌id
     * @return 查询结果
     */
    WrapperResp<List<BrandResp>> getBrandById(List<Long> idList);

    /**
     * 查询商品信息列表--订单校验商品信息
     * @param commodityPriceReq 接口参数
     * @return 查询结果
     */
    List<CommoditySkuPublishResp> getCommodityListByOrder(CommodityPriceReq commodityPriceReq);

    /**
     * 查询商品信息
     * @param templateCommoditySearchReq 接口参数
     * @return 查询结果
     */
    WrapperResp<PageDataResp<EsCommodityResp>> searchCommodityList(TemplateCommoditySearchReq templateCommoditySearchReq);

    /**
     * 批量查询商品价格
     * @param shopId 商城id
     * @param memberId
     * @param roleId
     * @param skuIds
     * @return
     */
    Map<Long, Map<String, BigDecimal>> getCommodityPriceBatch(Long shopId, Long memberId, Long roleId, List<Long> skuIds);

    /**
     * 批量查询商品价格与是否允许会员折购买
     * @param shopId
     * @param memberId
     * @param roleId
     * @param skuIds
     * @return
     */
    Map<Long, CommodityPriceResp> getCommodityPriceAndMemberPriceBatch(Long shopId, Long memberId, Long roleId, List<Long> skuIds);

    /**
     * 批量查询sku商品信息
     * @param skuIds
     * @param memberId 商品归属会员id
     * @param roleId 商品归属角色id
     * @param status 审核状态: 1-待提交审核, 2-待审核, 3-审核不通过, 4-审核通过, 5-上架, 6-下架
     * @return
     */
    Map<Long, CommoditySkuStockResp> getCommoditySkuMap(List<Long> skuIds, Long memberId, Long roleId, Integer status);

    /**
     * 根据SKU ID查询商品详情
     * @param skuId 商品SKU ID
     * @return 商品详情信息
     */
    WrapperResp<CommoditySkuStockResp> getCommodityDetailBySkuId(Long skuId);
}
