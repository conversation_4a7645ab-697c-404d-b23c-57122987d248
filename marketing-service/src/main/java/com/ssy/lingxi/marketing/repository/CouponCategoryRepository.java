package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.coupon.CouponCategoryDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 优惠券适用品类仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/26
 */
public interface CouponCategoryRepository extends JpaRepository<CouponCategoryDO, Long>, JpaSpecificationExecutor<CouponCategoryDO> {

    boolean existsByCouponId(Long id);

    List<CouponCategoryDO> findAllByCouponId(Long id);

    List<CouponCategoryDO> findAllByCouponIdIn(List<Long> ids);

    @Transactional
    void deleteByCouponId(Long id);
}
