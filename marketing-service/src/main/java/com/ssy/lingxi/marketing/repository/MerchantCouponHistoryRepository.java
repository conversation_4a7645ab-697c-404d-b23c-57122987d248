package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.coupon.MerchantCouponHistoryDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 商家优惠券内部流转记录仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/28
 */
public interface MerchantCouponHistoryRepository extends JpaRepository<MerchantCouponHistoryDO, Long>, JpaSpecificationExecutor<MerchantCouponHistoryDO> {

    List<MerchantCouponHistoryDO> findAllByCouponId(Long id, Sort sort);
}
