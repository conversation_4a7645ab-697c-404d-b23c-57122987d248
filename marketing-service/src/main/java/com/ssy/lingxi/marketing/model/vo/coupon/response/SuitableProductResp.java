package com.ssy.lingxi.marketing.model.vo.coupon.response;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 优惠券适用商品VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/7/16
 */
@Data
public class SuitableProductResp implements Serializable {

    private static final long serialVersionUID = -98577380323283379L;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格
     */
    private String type;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品价格(阶梯价)
     * */
    private Map<String, BigDecimal> price;

    /**
     * 商品主图
     */
    private String productImgUrl;

}
