package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 平台优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformCouponAuditPageResp extends PlatformCouponPageResp implements Serializable {

    private static final long serialVersionUID = -6609709418888573425L;

    /**
     * 审核按钮
     */
    private boolean audit;

}
