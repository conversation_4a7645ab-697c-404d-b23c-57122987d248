package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 渠道商品列表（装修） - 请求
 * <AUTHOR>
 * @since 2021/09/08
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ChannelGoodsListAdornDataReq extends PageDataReq {
    private static final long serialVersionUID = -1085681034118316814L;

    /**
     * 商城id
     */
    @NotNull(message = "商城ID不能空")
    private Long shopId;

    /**
     * 会员品类id
     */
    private Long customerCategoryId;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 上架开始时间
     */
    private Long publishStartTime;

    /**
     * 上架结束时间
     */
    private Long publishEndTime;

    /**
     * 只查询该id集合数据
     */
    private List<Long> idInList;

    /**
     * 排除该id集合数据
     */
    private List<Long> idNotInList;

    /**
     * 商品归属的会员id
     */
    private Long memberId;

    /**
     * 商品归属的会员角色id
     */
    private Long memberRoleId;

    /**
     * 登录用户的会员id
     */
    private Long loginMemberId;

    /**
     * 登录用户的会员角色id
     */
    private Long loginMemberRoleId;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 城市的编码
     */
    private String cityCode;
}
