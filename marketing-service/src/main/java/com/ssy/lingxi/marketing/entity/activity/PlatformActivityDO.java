package com.ssy.lingxi.marketing.entity.activity;


import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.marketing.handler.convert.JpaJsonToActivityDefinedBOConverter;
import com.ssy.lingxi.marketing.model.bo.ActivityDefinedBO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;

/**
 * 平台活动实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/16
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_activity")
public class  PlatformActivityDO implements Serializable {

    private static final long serialVersionUID = -350933895506469004L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 活动参与类型（1：商家报名活动；2：平台自建活动(仅抽奖)）
     */
    @Column(columnDefinition = "int2")
    private Integer activitySignUpType;

    /**
     * 活动名称
     * */
    @Column(columnDefinition = "varchar(50)")
    private String activityName;

    /**
     * 活动开始时间
     * */
    @Column(columnDefinition = "int8")
    private Long startTime;

    /**
     * 活动结束时间
     * */
    @Column(columnDefinition = "int8")
    private Long endTime;

    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     *         ActivityTypeEnum.class
     * */
    @Column(columnDefinition = "int2")
    private Integer activityType;

    /**
     * 细分类型（满额、满量、赠送促销）：1.满量减/满额减/赠商品；2.满量折/满额折/赠优惠卷
     * */
    @Column(columnDefinition = "int2")
    private Integer minType;

    /**
     * 是否允许适用优惠券 0-否 1-是
     * 注意: 与activityDefined内定义的类型不一样
     */
    @Column(columnDefinition = "int2")
    private Integer allowCoupon;

    /**
     * 活动定义
     * */
    @Convert(converter = JpaJsonToActivityDefinedBOConverter.class)
    @Column(columnDefinition = "jsonb")
    private ActivityDefinedBO activityDefined;

    /**
     * 适用新老会员
     * 新会员(平台会员,当天注册, 上级为平台)
     * 老会员(平台会员,当天之前注册, 上级为平台)
     * 存储方式00, 一位为新会员, 二为老会员
     * 例子: 1. 选择新会员, 则值为10
     *      2. 全部都选择,则值为11
     *      3.全都不选择,则值为00
     */
    @Column(columnDefinition = "varchar(10)")
    private String suitableMember;

    /**
     * 适用会员类型
     * 企业会员
     * 个人会员
     * 存储方式00, 一位为企业会员, 二为个人会员
     * 例子: 1. 选择企业会员, 则值为10
     *      2. 全部都选择,则值为11
     *      3.全都不选择,则值为00
     */
    @Column(columnDefinition = "varchar(10)")
    private String suitableMemberType;

    /**
     * 报名开始时间
     * */
    @Column(columnDefinition = "int8")
    private Long signUpStartTime;

    /**
     * 报名结束时间
     * */
    @Column(columnDefinition = "int8")
    private Long signUpEndTime;

    /**
     * 是否邀请全部会员 0-否 1-是
     */
    @Column
    private Integer inviteType;

    /**
     * 一对多双向关联邀请会员
     * (冗余会员服务的会员)
     */
    @JsonManagedReference
    @OneToMany(mappedBy = "platformActivity", cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    private Set<PlatformActivityInviteDO> inviteMembers;

    /**
     * 一对多双向关报名会员
     * (冗余会员服务的会员)
     */
    @JsonManagedReference
    @OneToMany(mappedBy = "platformActivity", cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    private Set<PlatformActivitySignUpDO> signUpMembers;

    /**
     * 外部状态
     * */
    @Column(columnDefinition = "int4")
    private Integer outerStatus;

    /**
     * 外部任务ID
     * */
    @Column(columnDefinition = "varchar(50)")
    private String outerTaskId;

    /**
     * 外部工作流key
     * */
    @Column(columnDefinition = "varchar(50)")
    private String outerProcessKey;

    /**
     * 外部工作流当前步骤
     * */
    @Column(columnDefinition = "int4")
    private Integer outerTaskStep;

    /**
     * 内部状态
     * */
    @Column(columnDefinition = "int4")
    private Integer innerStatus;

    /**
     * 内部任务ID
     * */
    @Column(columnDefinition = "varchar(50)")
    private String innerTaskId;

    /**
     * 内部工作流类型
     * */
    @Column(columnDefinition = "varchar(50)")
    private String innerProcessKey;

    /**
     * 内部工作流当前步骤
     * */
    @Column(columnDefinition = "int4")
    private Integer innerTaskStep;

    /**
     * 会员ID
     * */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 角色ID
     * */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 创建时间
     * */
    @Column(columnDefinition = "int8")
    private Long createTime = System.currentTimeMillis();
}
