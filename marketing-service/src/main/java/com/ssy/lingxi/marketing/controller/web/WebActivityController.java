package com.ssy.lingxi.marketing.controller.web;

import cn.hutool.json.JSONObject;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.bo.MobileMerchantActivityGoodsCouponBO;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityCardAdornReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.GoodsAreaAdornDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityCardAdornResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.GoodsAreaAdornResp;
import com.ssy.lingxi.marketing.service.IWebActivityService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * web - 活动
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/09/03
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/web/activity")
public class WebActivityController extends BaseController {

    @Resource
    private IWebActivityService webActivityService;

    /**
     * 平台活动页（装修展示）
     **/
    @PostMapping("/platformActivityPageAdorn")
    public WrapperResp<Map<String, Object>> platformActivityPageAdorn(@RequestBody JSONObject req) {
        return WrapperUtil.success(webActivityService.platformActivityPageAdorn(req, getHeadersShopId(), isLogin() ? getSysUser() : null));
    }

    /**
     * 商家活动页（装修展示）
     **/
    @PostMapping("/merchantActivityPageAdorn")
    public WrapperResp<Map<String, Object>> merchantActivityPageAdorn(@RequestBody JSONObject req) {
        return WrapperUtil.success(webActivityService.merchantActivityPageAdorn(req, getHeadersShopId(), isLogin() ? getSysUser() : null));
    }

    /**
     * 活动卡片（装修展示）
     **/
    @GetMapping("/activityCardAdorn")
    public WrapperResp<List<ActivityCardAdornResp>> activityCardAdorn(@Valid ActivityCardAdornReq req) {
        return WrapperUtil.success(webActivityService.activityCardAdorn(req, getHeadersShopId(), isLogin() ? getSysUser() : null));
    }

    /**
     * 店铺活动卡片（装修展示）
     **/
    @GetMapping("/storeActivityCardAdorn")
    public WrapperResp<List<ActivityCardAdornResp>> storeActivityCardAdorn(@Valid ActivityCardAdornReq req) {
        return WrapperUtil.success(webActivityService.storeActivityCardAdorn(req,getHeadersShopId(), isLogin() ? getSysUser() : null));
    }

    /**
     * 活动卡片 - 赠送优惠卷（装修展示）
     **/
    @PostMapping("/activityCardGiveCouponAdorn")
    public WrapperResp<List<MobileMerchantActivityGoodsCouponBO>> activityCardGiveCouponAdorn(@RequestBody @Valid ActivityCardAdornReq req) {
        return WrapperUtil.success(webActivityService.activityCardGiveCouponAdorn(getSysUser(), getHeadersShopId(), req));
    }

    /**
     * 商品区（装修展示）
     **/
    @GetMapping("/goodsAreaAdorn")
    public WrapperResp<PageDataResp<GoodsAreaAdornResp>> goodsAreaAdorn(@Valid GoodsAreaAdornDataReq req) {
        return WrapperUtil.success(webActivityService.goodsAreaAdorn(req));
    }
}
