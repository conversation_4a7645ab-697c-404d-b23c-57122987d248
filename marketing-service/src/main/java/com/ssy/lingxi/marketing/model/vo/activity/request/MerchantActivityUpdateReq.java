package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 商家活动新增请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/17
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantActivityUpdateReq extends MerchantActivityBaseReq {

    private static final long serialVersionUID = -6800219908513815613L;

    /**
     * 活动id
     */
    @NotNull(message = "活动id不能为空")
    private Long id;

}
