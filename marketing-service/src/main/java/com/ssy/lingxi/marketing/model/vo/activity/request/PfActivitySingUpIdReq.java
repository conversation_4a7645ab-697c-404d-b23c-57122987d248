package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 平台活动报名id
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/12
 */
@Data
public class PfActivitySingUpIdReq implements Serializable {

    private static final long serialVersionUID = -4780727001953398109L;
    /**
     * 平台活动报名id
     */
    @NotNull(message = "平台活动报名id参数不能为空")
    private Long id;

}
