package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityGoodsAdornReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.GoodsListAdornDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.McActivityListAdornDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.PfActivityListAdornDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityGoodsAdornResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.GoodsListAdornResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.McActivityListAdornResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.PfActivityListAdornResp;

import java.util.List;

/**
 * 装修服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/23
 */
public interface IAdornService {

    PageDataResp<PfActivityListAdornResp> platformActivityListAdorn(UserLoginCacheDTO user, PfActivityListAdornDataReq req);

    PageDataResp<McActivityListAdornResp> merchantActivityListAdorn(UserLoginCacheDTO user, McActivityListAdornDataReq req);

    List<ActivityGoodsAdornResp> activityGoodsAdorn(ActivityGoodsAdornReq req);

    PageDataResp<GoodsListAdornResp> goodsListAdorn(GoodsListAdornDataReq req);
}
