package com.ssy.lingxi.marketing.entity.activity;


import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.component.base.enums.marketing.ActivityTypeEnum;
import com.ssy.lingxi.marketing.handler.convert.JpaJsonToActivityDefinedBOConverter;
import com.ssy.lingxi.marketing.model.bo.ActivityDefinedBO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 商家活动实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/16
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "merchant_activity")
public class MerchantActivityDO implements Serializable {

    private static final long serialVersionUID = 3623169796411727295L;

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 活动名称
     * */
    @Column(columnDefinition = "varchar(50)")
    private String activityName;

    /**
     * 活动开始时间
     * */
    @Column(columnDefinition = "int8")
    private Long startTime;

    /**
     * 活动结束时间
     * */
    @Column(columnDefinition = "int8")
    private Long endTime;

    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销 6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖 11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     * @see ActivityTypeEnum
     */
    @Column(columnDefinition = "int2")
    private Integer activityType;

    /**
     * 细分类型（满额、满量、赠送促销）：1.满量减/满额减/赠商品；2.满量折/满额折/赠优惠卷
     * */
    @Column(columnDefinition = "int2")
    private Integer minType;

    /**
     * 是否允许适用优惠券 0-否 1-是
     * 注意: 与activityDefined内定义的类型不一样
     */
    @Column(columnDefinition = "int2")
    private Integer allowCoupon;

    /**
     * 活动描述
     */
    @Column(columnDefinition = "text")
    private String describe;

    /**
     * 活动定义.
     * */
    @Convert(converter = JpaJsonToActivityDefinedBOConverter.class)
    @Column(columnDefinition = "jsonb")
    private ActivityDefinedBO activityDefined;

    /**
     * 适用用户
     * 新用户(平台会员,当天注册, 上级为平台)
     * 老用户(平台会员,当天之前注册, 上级为平台)
     * 新会员(商户会员/渠道会员, 当天注册, 上级为非平台)
     * 老会员(商户会员/渠道会员, 当天之前注册, 上级为非平台)
     * 存储方式0000, 一位为新会员, 二为老会员, 三位为新用户, 四位为老用户
     * 例子: 1. 选择新用户和老用户, 则值为1100
     *      2. 全部都选择,则值为1111
     *      3.全都不选择,则值为0000
     */
    @Column(columnDefinition = "varchar(10)")
    private String suitableUser;

    /**
     * 外部任务ID
     * */
    @Column(columnDefinition = "varchar(50)")
    private String outerTaskId;

    /**
     * 外部工作流key
     * */
    @Column(columnDefinition = "varchar(50)")
    private String outerProcessKey;

    /**
     * 外部工作流当前步骤
     * */
    @Column(columnDefinition = "int4")
    private Integer outerTaskStep;

    /**
     * 内部状态
     * */
    @Column(columnDefinition = "int4")
    private Integer innerStatus;

    /**
     * 内部任务ID
     * */
    @Column(columnDefinition = "varchar(50)")
    private String innerTaskId;

    /**
     * 内部工作流类型
     * */
    @Column(columnDefinition = "varchar(50)")
    private String innerProcessKey;

    /**
     * 内部工作流当前步骤
     * */
    @Column(columnDefinition = "int4")
    private Integer innerTaskStep;

    /**
     * 会员ID
     * */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 角色ID
     * */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 会员名称
     * */
    @Column(columnDefinition = "varchar(50)")
    private String memberName;

    /**
     * 创建时间
     * */
    @Column(columnDefinition = "int8")
    private Long createTime = System.currentTimeMillis();

    /**
     * 参与用户具体会员ID列表（JSON格式存储）
     * 例如: ["123", "456", "789"]
     */
    @Column(columnDefinition = "text")
    private String participantMemberIds;

    /**
     * 反向匹配开关
     * 0-正向匹配（仅指定会员可参与）
     * 1-反向匹配（除指定会员外都可参与）
     */
    @Column(columnDefinition = "int2")
    private Integer reverseMatch;
}
