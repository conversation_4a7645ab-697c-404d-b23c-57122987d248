package com.ssy.lingxi.marketing.model.vo.coupon.request;

import com.ssy.lingxi.marketing.model.dto.ActivityDiscountCalculateItemDTO;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 移动端活动优惠计算请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024/12/10
 */
@Getter
@Setter
public class MobileActivityDiscountCalculateReq implements Serializable {

    private static final long serialVersionUID = -1284106139690376931L;

    /**
     * 商城id
     */
    private Long shopId;

    /**
     * 店铺会员id
     */
    private Long memberId;

    /**
     * 商品列表
     */
    @NotEmpty(message = "商品列表不能为空")
    @Size(max = 100, message = "单次最多计算100个商品")
    @Valid
    private List<ActivityDiscountCalculateItemDTO> goodsItems;

}