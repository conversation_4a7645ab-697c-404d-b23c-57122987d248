package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 套餐商品
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/1
 */
@Getter
@Setter
public class SetmealGoodsResp implements Serializable {


    private static final long serialVersionUID = -630021833369219718L;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格
     */
    private String type;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品价格
     * */
    private BigDecimal price;
    /**
     * 分组编号/优惠阶梯
     */
    private Integer groupNo;
    /**
     * 套餐价格
     * */
    private BigDecimal groupPrice;

    /**
     * 赠品主图
     */
    private String productImgUrl;
    /**
     * 赠送数量/搭配数量
     */
    private BigDecimal num;
    /**
     * sku到手价*搭配数量
     * */
    private BigDecimal totalAmount;
    /**
     * sku节省金额
     * */
    private BigDecimal discountAmount;
}
