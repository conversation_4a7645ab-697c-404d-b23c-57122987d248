package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品优惠详情响应类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024/12/10
 */
@Getter
@Setter
public class GoodsDiscountDetailResp implements Serializable {

    private static final long serialVersionUID = -1284106139690376941L;

    /**
     * 商品skuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品数量
     */
    private Integer quantity;

    /**
     * 商品单价
     */
    private BigDecimal unitPrice;

    /**
     * 商品原始总价
     */
    private BigDecimal originalTotalPrice;

    /**
     * 商品优惠后总价
     */
    private BigDecimal discountedTotalPrice;

    /**
     * 商品优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 商品优惠后单价
     */
    private BigDecimal discountedUnitPrice;

    /**
     * 是否参与了该优惠活动
     */
    private Boolean participated;

    /**
     * 原始工费（如果有工费）
     */
    private BigDecimal originalWorkFee;

    /**
     * 优惠后工费（如果有工费）
     */
    private BigDecimal discountedWorkFee;

    /**
     * 工费优惠金额（如果有工费）
     */
    private BigDecimal workFeeDiscountAmount;

    /**
     * 是否包含工费
     */
    private Boolean hasWorkFee;

    /**
     * 单件编码
     */
    private String singleCode;

    /**
     * 商品重量（克）
     */
    private BigDecimal weight;

    /**
     * 金价（元/克）
     */
    private BigDecimal goldPrice;

    /**
     * 材料金额（重量 * 金价）
     */
    private BigDecimal materialAmount;

}