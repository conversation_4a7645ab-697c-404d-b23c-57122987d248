package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 活动内部流转记录返回实体
 * <AUTHOR> yzc
 * @version 2.0.0
 * @since 2021/8/20
 */
@Getter
@Setter
public class ActivityInnerRecordRespDTO implements Serializable {

    private static final long serialVersionUID = 7903304460911658544L;
    /**
     * 活动、优惠券id
     */
    private Long dataId;
    /**
     * 操作者
     */
    private String operator;

    /**
     * 部门
     */
    private String department;

    /**
     * 职位
     */
    private String jobTitle;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 操作枚举
     */
    private Integer operationCode;

    /**
     * 操作
     */
    private String operate;

    /**
     * 操作时间(yyyy-MM-dd HH:mm)
     */
    private String operateTime;

    /**
     * 审核意见
     */
    private String opinion;
}
