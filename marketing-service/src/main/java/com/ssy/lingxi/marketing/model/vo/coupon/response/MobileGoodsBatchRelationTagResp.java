package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 批量活动标签返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/1
 */
@Getter
@Setter
public class MobileGoodsBatchRelationTagResp implements Serializable {

    private static final long serialVersionUID = -1284106139690376923L;

    /**
     * 商品活动匹配结果
     * key: skuId
     * value: 该商品匹配到的活动信息
     */
    private Map<Long, MobileGoodsRelationTagResp> goodsActivityMap;

    /**
     * 未匹配到活动的商品skuId列表
     */
    private List<Long> unmatchedSkuIds;

    /**
     * 总查询商品数量
     */
    private Integer totalCount;

    /**
     * 匹配到活动的商品数量
     */
    private Integer matchedCount;

}