package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商家活动公共接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantActivityCommonPageDataReq extends PageDataReq {

    private static final long serialVersionUID = -5752647612114537947L;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 外部状态
     */
    private Integer outerStatus;

    /**
     * 内部状态
     */
    private Integer innerStatus;
}
