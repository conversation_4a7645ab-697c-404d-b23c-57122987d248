package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.ActivityOrderRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 活动订单-实体仓库(拼团订单)
 * <AUTHOR> yzc
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface ActivityOrderRecordRepository extends JpaRepository<ActivityOrderRecordDO, Long>, JpaSpecificationExecutor<ActivityOrderRecordDO> {


    List<ActivityOrderRecordDO> findByObjectIdAndActivityType(Long objectId,Integer activityType);

    List<ActivityOrderRecordDO> findByOrderId(Long orderId);

    List<ActivityOrderRecordDO> findByObjectIdAndActivityTypeAndMemberIdAndRoleId(Long objectId,Integer activityType,Long memberId,Long roleId);
}
