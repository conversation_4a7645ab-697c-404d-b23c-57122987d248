package com.ssy.lingxi.marketing.controller.activity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.activity.request.*;
import com.ssy.lingxi.marketing.model.vo.activity.response.*;
import com.ssy.lingxi.marketing.model.vo.common.response.PageItemResp;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ActivityPlatformCouponDataReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityGoodsCouponResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ActivityPrizeCouponResp;
import com.ssy.lingxi.marketing.service.ICommonService;
import com.ssy.lingxi.marketing.service.IPlatformActivityService;
import com.ssy.lingxi.marketing.service.IPlatformCouponService;
import com.ssy.lingxi.marketing.serviceImpl.base.activity.BaseActivityRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 平台后台-平台营销活动管理
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/platform/activity")
public class PlatformActivityController extends BaseController {

    @Resource
    private IPlatformActivityService platformActivityService;
    @Resource
    private ICommonService commonService;
    @Resource
    private BaseActivityRecordService activityRecordService;
    @Resource
    private IPlatformCouponService platformCouponService;

    /**
     * 平台营销活动查询 - 外部状态列表
     */
    @GetMapping("/getOuterStatusList")
    public WrapperResp<List<PageItemResp>> getOuterStatusList() {
        return WrapperUtil.success(commonService.getPlatformActivityOuterStatuses());
    }

    /**
     * 平台营销活动查询 - 内部状态列表
     */
    @GetMapping("/getInnerStatusList")
    public WrapperResp<List<PageItemResp>> getStatusList() {
        return WrapperUtil.success(commonService.getPlatformActivityInnerStatuses());
    }

    /**
     * 平台营销活动查询 - 活动类型列表
     */
    @GetMapping("/getActivityTypeList")
    public WrapperResp<List<PageItemResp>> getActivityTypeList() {
        return WrapperUtil.success(commonService.getActivityTypes());
    }

    /**
     * 平台营销活动查询 - 分页列表
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<PlatformActivitySummaryPageResp>> pageSummary(@Valid PlatformActivityPageDataReq pageReq) {
        return WrapperUtil.success(platformActivityService.pageSummary(getPlatformUser(), pageReq));
    }

    /**
     * 平台营销活动查询 - 活动详情
     */
    @GetMapping("/detail")
    public WrapperResp<PlatformActivityDetailResp> detail(@Valid CommonIdReq req) {
        return WrapperUtil.success(platformActivityService.detail(getPlatformUser(), req));
    }

    /**
     * 平台营销活动查询 - 活动详情 - 活动商品(分页)
     */
    @GetMapping("/detail/goods/page")
    public WrapperResp<PageDataResp<PfActivitySignUpGoodsPageResp>> pageByActivityGoods(@Valid PfActivityGoodsPageDataReq pageReq) {
        return WrapperUtil.success(platformActivityService.pageByActivityGoods(getPlatformUser(), pageReq));
    }

    /**
     * 平台营销活动查询 - 活动详情 - 活动商品 - 选择附属优惠券
     **/
    @GetMapping("/detail/goods/coupon/select")
    public WrapperResp<PageDataResp<ActivityGoodsCouponResp>> selectSubsidiaryCouponList(ActivityPlatformCouponDataReq request) {
        return WrapperUtil.success(platformCouponService.selectSubsidiaryCouponList(request, getPlatformUser()));
    }

    /**
     * 平台营销活动查询 - 活动详情 - 奖品 - 选择0元购买抵扣券
     **/
    @GetMapping("/detail/prize/coupon/select")
    public WrapperResp<PageDataResp<ActivityPrizeCouponResp>> selectPrizeCouponList(ActivityPlatformCouponDataReq request) {
        return WrapperUtil.success(platformCouponService.selectPrizeCouponList(request, getPlatformUser()));
    }

    /**
     * 平台营销活动查询 - 活动详情 - 外部流转记录(分页)
     */
    @GetMapping("/outer/record/page")
    public WrapperResp<PageDataResp<ActivityOuterRecordResp>> pageOuterRecordList(@Valid ActivityOuterRecordPageDataReq pageReq) {
        return WrapperUtil.success(activityRecordService.pagePlatformActivityOuterRecord(getPlatformUser(), pageReq));
    }

    /**
     * 平台营销活动查询 - 活动详情 - 内部流转记录(分页)
     */
    @GetMapping("/inner/record/page")
    public WrapperResp<PageDataResp<ActivityInnerRecordResp>> pageInnerRecordList(@Valid ActivityInnerRecordPageDataReq pageReq) {
        return WrapperUtil.success(activityRecordService.pagePlatformActivityInnerRecord(getPlatformUser(), pageReq));
    }

    /**
     * 平台营销活动查询 - 取消
     */
    @PostMapping("/cancel")
    public WrapperResp<Void> cancel(@RequestBody @Valid ActivityActionReq req) {
        return WrapperUtil.success(platformActivityService.cancel(getPlatformUser(), req));
    }

    /**
     * 平台营销活动查询 - 终止
     */
    @PostMapping("/stop")
    public WrapperResp<Void> stop(@RequestBody @Valid ActivityActionReq req) {
        return WrapperUtil.success(platformActivityService.stop(getPlatformUser(), req));
    }

    /**
     * 平台营销活动查询 - 重新启动
     */
    @PostMapping("/restart")
    public WrapperResp<Void> restart(@RequestBody @Valid ActivityActionReq req) {
        return WrapperUtil.success(platformActivityService.restart(getPlatformUser(), req));
    }

    /**
     * 平台营销活动查询 - 修改时间
     */
    @PostMapping("/update/time")
    public WrapperResp<Void> updateTime(@RequestBody @Valid PlatformActivityUpdateTimeReq req) {
        return WrapperUtil.success(platformActivityService.updateTime(getPlatformUser(), req));
    }

    /**
     * 待新增平台营销活动 - 分页列表
     */
    @GetMapping("/page/tobe/add")
    public WrapperResp<PageDataResp<PlatformActivityAddPageResp>> pageByToBeAdd(@Valid PlatformActivityPageDataReq pageReq) {
        return WrapperUtil.success(platformActivityService.pageByToBeAdd(getPlatformUser(), pageReq));
    }

    /**
     * 待新增平台营销活动 - 新增
     */
    @PostMapping("/save")
    public WrapperResp<Void> save(@RequestBody @Valid PlatformActivityAddReq req) {
        return WrapperUtil.success(platformActivityService.savePlatformActivity(getPlatformUser(), req));
    }

    /**
     * 待新增平台营销活动 - 修改
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(@RequestBody @Valid PlatformActivityUpdateReq req) {
        return WrapperUtil.success(platformActivityService.updatePlatformActivity(getPlatformUser(), req));
    }

    /**
     * 待新增平台营销活动 - 提交
     */
    @PostMapping("/submit")
    public WrapperResp<Void> submit(@Valid @RequestBody ActivitySubmitReq req) {
        return WrapperUtil.success(platformActivityService.submit(getPlatformUser(), req));
    }

    /**
     * 待新增平台营销活动 - 批量提交
     */
    @PostMapping("/submit/batch")
    public WrapperResp<Void> batchSubmit(@Valid @RequestBody CommonIdListReq req) {
        return WrapperUtil.success(platformActivityService.batchSubmit(getPlatformUser(), req));
    }

    /**
     * 待新增平台营销活动 - 删除
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delete(@Valid @RequestBody ActivitySubmitReq req) {
        return WrapperUtil.success(platformActivityService.batchDelete(getPlatformUser(), Collections.singletonList(req.getId())));
    }

    /**
     * 待新增平台营销活动 - 批量删除
     */
    @PostMapping("/delete/batch")
    public WrapperResp<Void> batchDelete(@Valid @RequestBody CommonIdListReq req) {
        return WrapperUtil.success(platformActivityService.batchDelete(getPlatformUser(), req.getIdList()));
    }

    /**
     * 待审核报名 - 分页列表
     */
    @GetMapping("/page/tobe/sign/up")
    public WrapperResp<PageDataResp<PlatformActivityTobeSignUpPageResp>> pageByToBeSignUp(@Valid PlatformActivityTobeSignUpDataReq pageReq) {
        return WrapperUtil.success(platformActivityService.pageByToBeSignUp(getPlatformUser(), pageReq));
    }

    /**
     * 待审核报名 - 活动详情
     */
    @GetMapping("/detail/tobe/sign/up")
    public WrapperResp<PlatformActivitySignUpDetailResp> detailSignUp(@Valid PlatformActivitySignUpDetailReq req) {
        return WrapperUtil.success(platformActivityService.detailSignUp(getPlatformUser(), req));
    }

    /**
     * 待审核报名- 活动详情 - 活动商品(分页)
     */
    @GetMapping("/detail/goods/page/tobe/sign/up")
    public WrapperResp<PageDataResp<PfActivitySignUpGoodsPageResp>> pageByActivityGoodsSignUp(@Valid PfActivitySignUpGoodsPageDataReq pageReq) {
        return WrapperUtil.success(platformActivityService.pageByActivityGoodsSignUp(getPlatformUser(), pageReq));
    }

    /**
     * 待审核报名 - 审核报名
     */
    @PostMapping("/examine/sign/up")
    public WrapperResp<Void> examineSignUp(@RequestBody @Valid PlatformActivityExamineSignUpReq req) {
        return WrapperUtil.success(platformActivityService.examineSignUpUpdate(getPlatformUser(), req));
    }

    /**
     * 待提交审核平台营销活动 - 分页列表
     */
    @GetMapping("/page/tobe/submit")
    public WrapperResp<PageDataResp<PlatformActivitySubmitPageResp>> pageByToBeSubmit(@Valid PlatformActivityToBeSubmitPageDataReq pageReq) {
        return WrapperUtil.success(platformActivityService.pageByToBeSubmit(getPlatformUser(), pageReq));
    }

    /**
     * 待提交审核平台营销活动 - 提交审核
     */
    @PostMapping("/submit/examine")
    public WrapperResp<Void> submitExamine(@RequestBody @Valid ActivitySubmitReq dto) {
        return WrapperUtil.success(platformActivityService.submitExamine(getPlatformUser(), dto));
    }

    /**
     * 待审核平台营销活动(一级) - 分页列表
     */
    @GetMapping("/page/examine/step1")
    public WrapperResp<PageDataResp<PlatformActivityExamResp>> pageByExamineStep1(@Valid PlatformActivityStep1PageDataReq pageReq) {
        return WrapperUtil.success(platformActivityService.pageByExamineStep1(getPlatformUser(), pageReq));
    }

    /**
     * 待审核平台营销活动(一级) - 审核
     */
    @PostMapping("/examine/step1")
    public WrapperResp<Void> pageByExamineStep1(@RequestBody @Valid ActivityExamineReq req) {
        return WrapperUtil.success(platformActivityService.examineStep1Update(getPlatformUser(), req));
    }

    /**
     * 待审核平台营销活动(二级) - 分页列表
     */
    @GetMapping("/page/examine/step2")
    public WrapperResp<PageDataResp<PlatformActivityExamResp>> pageByExamineStep2(@Valid PlatformActivityStep1PageDataReq pageReq) {
        return WrapperUtil.success(platformActivityService.pageByExamineStep2(getPlatformUser(), pageReq));
    }

    /**
     * 待审核平台营销活动(二级) - 审核
     */
    @PostMapping("/examine/step2")
    public WrapperResp<Void> pageByExamineStep2(@RequestBody @Valid ActivityExamineReq req) {
        return WrapperUtil.success(platformActivityService.examineStep2Update(getPlatformUser(), req));
    }

    /**
     * 待上线平台营销活动 - 分页列表
     */
    @GetMapping("/page/tobe/online")
    public WrapperResp<PageDataResp<PlatformActivityOnlineResp>> pageByToBeOnline(@Valid PlatformActivityStep1PageDataReq pageReq) {
        return WrapperUtil.success(platformActivityService.pageByToBeOnline(getPlatformUser(), pageReq));
    }

    /**
     * 待上线平台营销活动 - 上线活动
     */
    @PostMapping("/online")
    public WrapperResp<Void> online(@RequestBody @Valid ActivitySubmitReq req) {
        return WrapperUtil.success(platformActivityService.onlineUpdate(getPlatformUser(), req));
    }
}
