package com.ssy.lingxi.marketing.controller.activity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.activity.request.MerchantActivityPlatformPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.PfActivityGoodsPageDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.McActivityGoodsPageResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.MerchantActivityDetailResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.PlatformMerchantActivityPageResp;
import com.ssy.lingxi.marketing.model.vo.common.response.PageItemResp;
import com.ssy.lingxi.marketing.service.ICommonService;
import com.ssy.lingxi.marketing.service.IPlatformMerchantActivityService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 商家营销活动管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/24
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/platform/merchant/activity")
public class PlatformMerchantActivityController extends BaseController {
    @Resource
    private IPlatformMerchantActivityService merchantActivityService;
    @Resource
    private ICommonService commonService;

    /**
     * 商家营销活动查询 - 内部状态列表
     * @return 查询结果
     */
    @GetMapping("/getInnerStatusList")
    public WrapperResp<List<PageItemResp>> getStatusList() {
        return WrapperUtil.success(commonService.getMerchantActivityInnerStatuses());
    }

    /**
     * 商家营销活动查询 - 活动类型列表
     */
    @GetMapping("/getActivityTypeList")
    public WrapperResp<List<PageItemResp>> getActivityTypeList() {
        return WrapperUtil.success(commonService.getActivityTypes());
    }

    /**
     * 商家营销活动查询 - 分页列表
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<PlatformMerchantActivityPageResp>> pageSummary(@Valid MerchantActivityPlatformPageDataReq pageVO) {
        return WrapperUtil.success(merchantActivityService.pageSummary(getPlatformUser(), pageVO));
    }

    /**
     * 商家营销活动查询 - 活动详情
     */
    @GetMapping("/detail")
    public WrapperResp<MerchantActivityDetailResp> detail(@Valid CommonIdReq req) {
        return WrapperUtil.success(merchantActivityService.detail(getPlatformUser(), req));
    }

    /**
     * 商家营销活动查询 - 活动详情 - 活动商品(分页)
     */
    @GetMapping("/detail/goods/page")
    public WrapperResp<PageDataResp<McActivityGoodsPageResp>> activityGoodsPage(@Valid PfActivityGoodsPageDataReq req) {
        return WrapperUtil.success(merchantActivityService.pageByActivityGoods(getPlatformUser(), req));
    }
}
