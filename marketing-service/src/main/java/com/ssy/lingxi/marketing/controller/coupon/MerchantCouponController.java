package com.ssy.lingxi.marketing.controller.coupon;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.common.request.CommonAgreeReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.*;
import com.ssy.lingxi.marketing.model.vo.coupon.response.*;
import com.ssy.lingxi.marketing.service.ICommonService;
import com.ssy.lingxi.marketing.service.IMerchantCouponService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 *
 * 商家优惠劵管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/28
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/coupon")
public class MerchantCouponController extends BaseController {

    @Resource
    private IMerchantCouponService merchantCouponService;

    @Resource
    private ICommonService commonService;

    /**
     * 优惠券类型查询条件列表
     * @return 返回结果
     */
    @GetMapping("/typeList")
    public WrapperResp<List<SelectItemResp>> listMerchantCouponTypes() {
        return WrapperUtil.success(commonService.listMerchantCouponTypes());
    }

    /**
     * 优惠券领取方式查询条件列表
     * @return 返回结果
     */
    @GetMapping("/getWayList")
    public WrapperResp<List<SelectItemResp>> listMerchantCouponAcquireWay() {
        return WrapperUtil.success(commonService.listCouponAcquireWays());
    }

    /**
     * 优惠券状态查询条件列表
     * @return 返回结果
     */
    @GetMapping("statusList")
    public WrapperResp<List<SelectItemResp>> listMerchantCouponStatuses() {
        return WrapperUtil.success(commonService.listMerchantCouponStatuses());
    }

    /**
     * 1-新用户 2-老用户 3-新会员 4-老会员
     * @description 优惠券适用会员列表
     * @return 查询结果
     */
    @GetMapping("suitableMemberTypeList")
    public WrapperResp<List<SelectItemResp>> listSuitableMemberTypes() {
        return WrapperUtil.success(commonService.listSuitableMemberTypes());
    }

    /**
     * 商家优惠券分页列表查询条件
     * @return 返回结果
     */
    @GetMapping("/page/condition")
    public WrapperResp<MerchantCouponConditionResp> listMerchantCouponConditions() {
        return WrapperUtil.success(commonService.listMerchantCouponConditions());
    }

    /**
     * 商家优惠劵查询 - 分页列表
     * @return 返回结果
     */
    @GetMapping("/summary/page")
    public WrapperResp<PageDataResp<MerchantCouponSummaryPageResp>> pageSummaryMerchantCoupon(@Valid MerchantCouponPageDataReq request) {
        return WrapperUtil.success(merchantCouponService.pageSummaryMerchantCoupon(request, getSysUser()));
    }

    /**
     * 商家优惠劵查询 - 详情
     * @return 返回结果
     */
    @GetMapping("/summary/get")
    public WrapperResp<MerchantCouponResp> getSummaryMerchantCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(merchantCouponService.getMerchantCoupon(request, getSysUser()));
    }

    /**
     * 商家优惠劵查询 - 修改
     * @param request 接口参数
     */
    @PostMapping("/waitAudit/modification")
    public WrapperResp<Void> modificationMerchantCoupon(@RequestBody @Valid MerchantCouponModificationReq request) {
        return WrapperUtil.success(merchantCouponService.modificationMerchantCoupon(request, getSysUser()));
    }


    /**
     * 商家优惠劵查询 - 终止
     * @param request 接口参数
     */
    @PostMapping("/summary/stop")
    public WrapperResp<Void> stopMerchantCoupon(@RequestBody @Valid MerchantCouponOperationReq request) {
        return WrapperUtil.success(merchantCouponService.stopMerchantCoupon(request, getSysUser()));
    }


    /**
     * 商家优惠劵查询 - 重启
     * @param request 接口参数
     */
    @PostMapping("/summary/restart")
    public WrapperResp<Void> restartMerchantCoupon(@RequestBody @Valid MerchantCouponOperationReq request) {
        return WrapperUtil.success(merchantCouponService.restartMerchantCoupon(request, getSysUser()));
    }


    /**
     * 商家优惠劵查询 - 取消
     * @param request 接口参数
     */
    @PostMapping("/summary/cancel")
    public WrapperResp<Void> cancelMerchantCoupon(@RequestBody @Valid MerchantCouponOperationReq request) {
        return WrapperUtil.success(merchantCouponService.cancelMerchantCoupon(request, getSysUser()));
    }


    // =============================待提交审核商家优惠劵=============================

    /**
     * 待提交审核商家优惠劵 - 分页列表
     * @return 返回结果
     */
    @GetMapping("/waitAudit/page")
    public WrapperResp<PageDataResp<MerchantCouponSubmitAuditPageResp>> pageWaitAuditMerchantCoupon(@Valid MerchantCouponPageDataReq request) {
        return WrapperUtil.success(merchantCouponService.pageSubmitAuditMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待提交审核商家优惠劵 - 详情
     * @return 返回结果
     */
    @GetMapping("/waitAudit/get")
    public WrapperResp<MerchantCouponResp> getWaitAuditMerchantCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(merchantCouponService.getMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待提交审核商家优惠劵 - 新增
     * @param request 接口参数
     */
    @PostMapping("/waitAudit/add")
    public WrapperResp<Void> addMerchantCoupon(@RequestBody @Valid MerchantCouponAddReq request) {
        return WrapperUtil.success(merchantCouponService.addMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待提交审核商家优惠劵 - 修改
     * @param request 接口参数
     */
    @PostMapping("/waitAudit/update")
    public WrapperResp<Void> updateMerchantCoupon(@RequestBody @Valid MerchantCouponUpdateReq request) {
        return WrapperUtil.success(merchantCouponService.updateMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待提交审核商家优惠劵 - 删除
     * @param request 接口参数
     */
    @PostMapping("/waitAudit/delete")
    public WrapperResp<Void> deleteMerchantCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(merchantCouponService.deleteMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待提交审核商家优惠劵 - 提交审核
     * @param request 接口参数
     */
    @PostMapping("/waitAudit/submitBatch")
    public WrapperResp<Void> submitBatchAuditMerchantCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(merchantCouponService.submitAuditMerchantCoupon(request, getSysUser()));
    }

    // =============================待审核商家优惠劵(一级)=============================

    /**
     * 待审核商家优惠劵(一级) - 分页列表
     * @return 返回结果
     */
    @GetMapping("/waitAuditOne/page")
    public WrapperResp<PageDataResp<MerchantCouponAuditPageResp>> pageAuditOneMerchantCoupon(@Valid MerchantCouponPageDataReq request) {
        return WrapperUtil.success(merchantCouponService.pageAuditOneMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待审核商家优惠劵(一级) - 详情
     * @return 返回结果
     */
    @GetMapping("/waitAuditOne/get")
    public WrapperResp<MerchantCouponResp> getAuditOneMerchantCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(merchantCouponService.getMerchantCoupon(request, getSysUser()));
    }


    /**
     * 待审核商家优惠劵(一级) - 提交审核
     * @param request 接口参数
     */
    @PostMapping("/waitAuditOne/audit")
    public WrapperResp<Void> auditOneMerchantCoupon(@RequestBody @Valid CommonAgreeReq request) {
        return WrapperUtil.success(merchantCouponService.auditOneMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待审核商家优惠劵(一级) - 批量提交审核
     * @param request 接口参数
     */
    @PostMapping("/waitAuditOne/auditBatch")
    public WrapperResp<Void> auditOneBatchMerchantCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(merchantCouponService.batchAuditOneMerchantCoupon(request, getSysUser()));
    }

    // =============================待审核商家优惠劵(二级)=============================

    /**
     * 待审核商家优惠劵(二级) - 分页列表
     * @return 返回结果
     */
    @GetMapping("/waitAuditTwo/page")
    public WrapperResp<PageDataResp<MerchantCouponAuditPageResp>> pageAuditTwoMerchantCoupon(@Valid MerchantCouponPageDataReq request) {
        return WrapperUtil.success(merchantCouponService.pageAuditTwoMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待审核商家优惠劵(二级) - 详情
     * @return 返回结果
     */
    @GetMapping("/waitAuditTwo/get")
    public WrapperResp<MerchantCouponResp> getAuditTwoMerchantCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(merchantCouponService.getMerchantCoupon(request, getSysUser()));
    }


    /**
     * 待审核商家优惠劵(二级) - 审核
     * @param request 接口参数
     */
    @PostMapping("/waitAuditTwo/audit")
    public WrapperResp<Void> auditTwoMerchantCoupon(@RequestBody @Valid CommonAgreeReq request) {
        return WrapperUtil.success(merchantCouponService.auditTwoMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待审核商家优惠劵(二级) - 批量审核
     * @param request 接口参数
     */
    @PostMapping("/waitAuditTwo/auditBatch")
    public WrapperResp<Void> auditTwoBatchMerchantCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(merchantCouponService.batchAuditTwoMerchantCoupon(request, getSysUser()));
    }

    // =============================待提交商家优惠劵=============================

    /**
     * 待提交商家优惠劵 - 分页列表
     * @return 返回结果
     */
    @GetMapping("/waitSubmit/page")
    public WrapperResp<PageDataResp<MerchantCouponSubmitPageResp>> pageSubmitMerchantCoupon(@Valid MerchantCouponPageDataReq request) {
        return WrapperUtil.success(merchantCouponService.pageSubmitMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待提交商家优惠劵 - 详情
     * @return 返回结果
     */
    @GetMapping("/waitSubmit/get")
    public WrapperResp<MerchantCouponResp> getSubmitMerchantCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(merchantCouponService.getMerchantCoupon(request, getSysUser()));
    }

    /**
     * 待提交商家优惠劵 - 批量提交
     * @param request 接口参数
     */
    @PostMapping("/waitSubmit/submitBatch")
    public WrapperResp<Void> submitBatchMerchantCoupon(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(merchantCouponService.submitMerchantCoupon(request, getSysUser()));
    }

    // =============================商家优惠劵执行=============================

    /**
     * 商家优惠劵执行 - 分页列表
     * @return 返回结果
     */
    @GetMapping("/waiteExecute/page")
    public WrapperResp<PageDataResp<MerchantCouponExecutePageResp>> pageExecuteMerchantCoupon(@Valid MerchantCouponPageDataReq request) {
        return WrapperUtil.success(merchantCouponService.pageExecuteMerchantCoupon(request, getSysUser()));
    }

    /**
     * 商家优惠劵执行 - 详情
     * @return 返回结果
     */
    @GetMapping("/waiteExecute/get")
    public WrapperResp<MerchantCouponResp> getExecuteMerchantCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(merchantCouponService.getMerchantCoupon(request, getSysUser()));
    }

    /**
     * 商家优惠劵执行 - 发券
     * @param request 接口参数
     */
    @PostMapping("/waiteExecute/grant")
    public WrapperResp<Void> grantMerchantCoupon(@RequestBody @Valid MerchantCouponGrantReq request) {
        return WrapperUtil.success(merchantCouponService.grantMerchantCoupon(request, getSysUser()));
    }

    /**
     * 商家优惠劵执行 - 发券详情
     * @return 返回结果
     */
    @GetMapping("/waiteExecute/grant/get")
    public WrapperResp<MerchantCouponGrantResp> getGrantMerchantCoupon(@Valid CommonIdReq request) {
        return WrapperUtil.success(merchantCouponService.getGrantMerchantCoupon(request, getSysUser()));
    }

    /**
     * 商家优惠劵执行明细 - 查询条件
     * @return 返回结果
     */
    @GetMapping("/waiteExecute/detail/page/condition")
    public WrapperResp<MerchantCouponDetailConditionResp> getExecuteMerchantCouponDetailCondition(@Valid CommonIdReq request) {
        return WrapperUtil.success(merchantCouponService.getExecuteMerchantCouponDetailCondition(request));
    }

    /**
     * 商家优惠劵执行明细 - 分页列表
     * @return 返回结果
     */
    @GetMapping("/waiteExecute/detail/page")
    public WrapperResp<PageDataResp<MerchantCouponDetailPageResp>> pageExecuteMerchantCouponDetail(@Valid MerchantCouponDetailPageDataReq request) {
        return WrapperUtil.success(merchantCouponService.pageExecuteMerchantCouponDetail(request, getSysUser()));
    }
}
