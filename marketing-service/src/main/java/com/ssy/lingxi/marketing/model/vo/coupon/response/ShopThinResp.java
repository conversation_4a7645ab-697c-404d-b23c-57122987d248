package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 优惠券返回类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/13
 */
@Data
@NoArgsConstructor
public class ShopThinResp implements Serializable {

    private static final long serialVersionUID = -9093777954153244116L;
    /**
     * 商城id
     */
    private Long shopId;

    /**
     * 会员id(自营商城拥有者)
     */
    private Long memberId;

    /**
     * 会员角色id(自营商城拥有者)
     */
    private Long memberRoleId;

    /**
     * 是否为自营商城：0.否；1.是；
     */
    private Boolean isSelf;

    /**
     * 商城名称
     */
    private String shopName;

    /**
     * 商城url
     */
    private String url;

    public ShopThinResp(Long shopId) {
        this.shopId = shopId;
    }
}
