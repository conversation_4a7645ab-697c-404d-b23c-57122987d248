package com.ssy.lingxi.marketing.model.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动外部流转记录返回实体
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/20
 */
@Data
public class ActivityOuterRecordRespDTO implements Serializable {

    private static final long serialVersionUID = 77882069418657923L;
    /**
     * dataId
     */
    private Long dataId;
    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 操作
     */
    private String operate;

    /**
     * 操作时间(yyyy-MM-dd HH:mm)
     */
    private String operateTime;

    /**
     * 审核意见
     */
    private String opinion;
}
