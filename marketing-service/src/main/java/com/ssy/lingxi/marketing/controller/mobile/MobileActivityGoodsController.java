package com.ssy.lingxi.marketing.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.api.model.request.ActivityGoodsQuantityCheckReq;
import com.ssy.lingxi.marketing.api.model.request.CartActivityPriceReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.CartActivityPriceResp;
import com.ssy.lingxi.marketing.model.vo.coupon.request.MobileActivityGoodsPageDataReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.MobileGoodsRelationTagReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.MobileProductDetailTagReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MobileActivityGoodsPageResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MobileGoodsRelationTagResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MobileProductDetailTagResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MobileSetmealGoodsGroupResp;
import com.ssy.lingxi.marketing.service.IActivityPriceCalculateService;
import com.ssy.lingxi.marketing.service.IMobileActivityGoodsService;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * App - 活动商品
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/6
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/mobile/activity/goods")
public class MobileActivityGoodsController extends BaseController {

    @Resource
    private IMobileActivityGoodsService mobileActivityGoodsService;
    @Resource
    private IActivityPriceCalculateService activityPriceCalculateService;

    /**
     * 查询活动sku商品详情活动标签
     **/
    @PostMapping("/detail/tag")
    public WrapperResp<MobileProductDetailTagResp> getActivityGoodsProductDetailTag(@Valid @RequestBody MobileProductDetailTagReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        Long headersShopId = this.getHeadersShopId();
        request.setShopId(headersShopId);
        return WrapperUtil.success(mobileActivityGoodsService.getActivityGoodsProductDetailTag(loginUser, request));
    }

    /**
     * 查询sku商品所关联的活动商品及活动信息
     **/
    @PostMapping("/relation/tag")
    public WrapperResp<MobileGoodsRelationTagResp> getActivityRelationGoods(@Valid @RequestBody MobileGoodsRelationTagReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileActivityGoodsService.getActivityRelationGoods(loginUser, request));
    }

    /**
     * 查询sku商品所关联的活动商品及活动信息-新
     **/
    @PostMapping("/relation/goods/list")
    public WrapperResp<MobileGoodsRelationTagResp> getActivityRelationGoodsList(@Valid @RequestBody MobileGoodsRelationTagReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileActivityGoodsService.getActivityRelationGoodsList(loginUser, request));
    }

    /**
     * 活动商品分页列表
     **/
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MobileActivityGoodsPageResp>> pageActivityGoods(@Valid MobileActivityGoodsPageDataReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileActivityGoodsService.pageActivityGoods(loginUser, request));
    }

    /**
     * 计算购物车商品活动价
     **/
    @PostMapping("/price/calculate")
    public WrapperResp<List<CartActivityPriceResp>> priceCalculate(@Valid @RequestBody List<CartActivityPriceReq> request) {
        if (CollectionUtils.isEmpty(request)) {
            return WrapperUtil.success();
        }
        return WrapperUtil.success(activityPriceCalculateService.calculateActivityPrice(getSysUser(), request));
    }

    /**
     * 校验购买数量【加入购物车的商品】
     **/
    @PostMapping("/check/quantity")
    public WrapperResp<BigDecimal> checkQuantity(@Valid @RequestBody ActivityGoodsQuantityCheckReq request) {
        return WrapperUtil.success(activityPriceCalculateService.checkQuantityByAddCart(getSysUser(), request));
    }


    /**
     * 查询套餐图列表（商品详情页）
     **/
    @PostMapping("/setmeal/img/list")
    public WrapperResp<List<MobileSetmealGoodsGroupResp>> getSetmealImgList(@Valid @RequestBody MobileGoodsRelationTagReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileActivityGoodsService.getSetmealImgList(loginUser, request));
    }

    /**
     * 查询套餐列表
     **/
    @PostMapping("/setmeal/list")
    public WrapperResp<List<MobileSetmealGoodsGroupResp>> getSetmealList(@Valid @RequestBody MobileGoodsRelationTagReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileActivityGoodsService.getSetmealList(loginUser, request));
    }
}
