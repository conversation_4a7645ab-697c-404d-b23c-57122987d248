package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 平台活动 - 被邀请的会员 - DTO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Data
public class PlatformActivityInviteReq implements Serializable {

    private static final long serialVersionUID = -7535431057439500546L;
    /**
     * id
     */
    private Long id;

    /**
     * 会员id
     */
    @NotNull(message = "会员id不能为空")
    private Long memberId;

    /**
     * 会员名称
     * */
    @NotNull(message = "会员名称不能为空")
    private String memberName;
    /**
     * 会员角色id
     */
    @NotNull(message = "会员角色id不能为空")
    private Long roleId;

    /**
     * 会员角色名称
     * */
    @NotNull(message = "会员角色名称不能为空")
    private String roleName;
    /**
     * 会员等级
     */
    @NotNull(message = "会员等级不能为空")
    private Integer level;

    /**
     * 会员等级名称
     * */
    @NotNull(message = "会员等级名称不能为空")
    private String levelName;


}
