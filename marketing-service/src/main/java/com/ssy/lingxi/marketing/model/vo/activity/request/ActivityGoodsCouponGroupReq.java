package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品-附属优惠券信息-组-接口参数VO
 * <AUTHOR> yzc
 * @since 2021/08/16
 * @version 2.0.0
 */
@Getter
@Setter
public class ActivityGoodsCouponGroupReq implements Serializable {
    private static final long serialVersionUID = 4243928964191582718L;

    /**
     * 优惠阶梯
     */
//    @NotNull(message = "优惠阶梯必填")
    private Integer groupNo;

    /**
     * 优惠门槛数量或金额
     */
//    @NotNull(message = "优惠门槛必填")
    private BigDecimal limitValue;

    /**
     * 明细
     * */
//    @Valid
    private List<ActivityGoodsCouponGroupDetailsReq> list;
}
