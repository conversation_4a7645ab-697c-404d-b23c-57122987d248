package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.ActivityGoodsSubsidiaryDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 活动 - 会员报名商品-附属商品信息-实体仓库
 * (赠品、换购商品、套餐商品信息)
 * <AUTHOR>
 * @since 2021/06/17
 */
public interface ActivityGoodsSubsidiaryRepository extends JpaRepository<ActivityGoodsSubsidiaryDO, Long>, JpaSpecificationExecutor<ActivityGoodsSubsidiaryDO> {

    List<ActivityGoodsSubsidiaryDO> findByActivityGoodsIdIn(List<Long> activityGoodsIdList);

    List<ActivityGoodsSubsidiaryDO> findByActivityGoodsId(Long activityGoodsId);

    void deleteByActivityGoodsId(Long activityGoodsId);

    List<ActivityGoodsSubsidiaryDO> findByActivityGoodsIdAndGroupNo(Long activityGoodsId,Integer groupNo);

}
