package com.ssy.lingxi.marketing.model.vo.activity.response;

import com.ssy.lingxi.product.api.model.resp.CategoryBaseResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsBrandResp;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 渠道商品列表（装修） - 响应
 * <AUTHOR>
 * @since 2021/09/08
 * @version 2.0.0
 */
@Data
public class ChannelGoodsListAdornResp implements Serializable {
    private static final long serialVersionUID = 7687636386093652988L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品主图
     */
    private String mainPic;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品标语
     */
    private String slogan;

    /**
     * 商品卖点
     */
    private String[] sellingPoint;

    /**
     * 会员品类
     */
    private CategoryBaseResp customerCategory;

    /**
     * 品牌
     */
    private EsBrandResp brand;

    /**
     * 单位
     */
    private String unitName;

    /**
     * 最小值
     */
    private BigDecimal min;

    /**
     * 最大值
     */
    private BigDecimal max;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员角色id
     */
    private Long memberRoleId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 已售数量(渠道商品)
     */
    private double channelSold;

    /**
     * 所属活动（key：id、name、type、belongType。value：活动ID、活动名称、活动类型、所属类型）
     * */
    private List<Map<String, Object>> activityList = new ArrayList<>();
}
