package com.ssy.lingxi.marketing.entity.coupon;


import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.component.base.handler.converter.JpaJsonToMapStringBigDecimalConverter;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 *  平台活动[会员]商品表-DO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_goods",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_goods_coupon_id_idx", columnList = "couponId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_goods_belong_type_idx", columnList = "belongType")})
public class CouponGoodsDO implements Serializable {

    private static final long serialVersionUID = -3516500596530429677L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 优惠券id
     */
    @Column
    private Long couponId;

    /**
     * 所属类型 1-平台 2-商家
     */
    @Column
    private Integer belongType;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 会员名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String memberName;

    /**
     * 会员角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 会员角色名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String roleName;

    /**
     * 商品id
     */
    @Column(columnDefinition = "int8")
    private Long productId;

    /**
     * skuId
     */
    @Column(columnDefinition = "int8")
    private Long skuId;

    /**
     * 商品名称
     */
    @Column(columnDefinition = "varchar(128)")
    private String productName;

    /**
     * 规格
     */
    @Column(columnDefinition = "varchar(40)")
    private String type;

    /**
     * 品类
     */
    @Column(columnDefinition = "varchar(128)")
    private String category;

    /**
     * 品牌
     */
    @Column(columnDefinition = "varchar(128)")
    private String brand;

    /**
     * 单位
     */
    @Column(columnDefinition = "varchar(128)")
    private String unit;

    /**
     * 商品价格(阶梯价)
     * */
    @Convert(converter = JpaJsonToMapStringBigDecimalConverter.class)
    @Column(columnDefinition = "jsonb")
    private Map<String, BigDecimal> price;

    /**
     * 商品主图
     */
    @Column(columnDefinition = "varchar(250)")
    private String productImgUrl;

}
