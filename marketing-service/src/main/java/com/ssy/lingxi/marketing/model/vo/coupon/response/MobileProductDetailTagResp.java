package com.ssy.lingxi.marketing.model.vo.coupon.response;

import com.ssy.lingxi.marketing.api.model.response.TagDetailResp;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 活动商品价格和活动标签返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/1
 */
@Getter
@Setter
public class MobileProductDetailTagResp implements Serializable {

    private static final long serialVersionUID = 5781776047672118132L;

    /**
     * 最优惠skuId
     */
    private Long preferentialSkuId;

    /**
     * sku商品活动价(特价、直降、折扣的最低价)
     */
    private BigDecimal promotionPrice;

    /**
     * 最优惠sku价格
     */
    private BigDecimal preferentialPrice;

    /**
     * 秒杀开始时段(有秒杀活动 - 有值, 无秒杀活动 - 无值)
     */
    private Long seckillStartTime;
    /**
     * 秒杀结束时段(有秒杀活动 - 有值, 无秒杀活动 - 无值)
     */
    private Long seckillEndTime;

    /**
     * 是否可使用优惠券
     */
    private Integer canUseCoupon;

    /**
     * 活动标签集合
     */
    private List<String> tagList;

    /**
     * 活动标签集合
     */
    private List<TagDetailResp> tagDetailList;

    /**
     * 可领取优惠券集合
     */
    private List<MobileCouponResp> couponList;
}
