package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/16
 */
@Getter
@Setter
public class LadderDTO implements Serializable {
    private static final long serialVersionUID = 8590373395037243450L;
    /**
     * 优惠门槛 - 数量
     */
    private BigDecimal key;

    /**
     * 优惠值 - 金额或折扣
     */
    private BigDecimal value;
}
