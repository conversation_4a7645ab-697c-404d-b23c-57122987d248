package com.ssy.lingxi.marketing.handler.convert;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.marketing.model.bo.*;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

import static com.ssy.lingxi.component.base.enums.marketing.ActivityTypeEnum.*;

/**
 * Jpa 将 Postgresql Json(或Jsonb)字段转换为DiscountBO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Converter(autoApply = true)
public class JpaJsonToActivityDefinedBOConverter implements AttributeConverter<ActivityDefinedBO, String> {

    @Override
    public String convertToDatabaseColumn(ActivityDefinedBO meta) {
        return JsonUtil.toJson(meta);
    }

    @Override
    public ActivityDefinedBO convertToEntityAttribute(String dbData) {
        JSONObject jsonObject = JSONUtil.parseObj(dbData);
        Integer type = (Integer)jsonObject.get("activityType");
        return convert(type,dbData);
    }

    private ActivityDefinedBO convert(Integer type,String dbData) {
        ActivityDefinedBO pfDiscountBO = null;
        if(type==null){
            return null;
        }
        switch (type){
            case 1:
                pfDiscountBO = JsonUtil.toObj(dbData, SpecialOfferBO.class);
                break;
            case 2:
                pfDiscountBO = JsonUtil.toObj(dbData, PlummetBO.class);
                break;
            case 3:
                pfDiscountBO = JsonUtil.toObj(dbData, DiscountBO.class);
                break;
            case 4:
                pfDiscountBO = JsonUtil.toObj(dbData, FullQuantityBO.class);
                break;
            case 5:
                pfDiscountBO = JsonUtil.toObj(dbData, FullMoneyBO.class);
                break;
            case 6:
                pfDiscountBO = JsonUtil.toObj(dbData, GiveBO.class);
                break;
            case 7:
                pfDiscountBO = JsonUtil.toObj(dbData, MorePieceBO.class);
                break;
            case 8:
                pfDiscountBO = JsonUtil.toObj(dbData, CombinationBO.class);
                break;
            case 9:
                pfDiscountBO = JsonUtil.toObj(dbData, GroupPurchaseBO.class);
                break;
            case 10:
                pfDiscountBO = JsonUtil.toObj(dbData, LotteryBO.class);
                break;
            case 11:
                pfDiscountBO = JsonUtil.toObj(dbData, BargainBO.class);
                break;
            case 12:
                pfDiscountBO = JsonUtil.toObj(dbData, SecKillBO.class);
                break;
            case 13:
                pfDiscountBO = JsonUtil.toObj(dbData, SwapBO.class);
                break;
            case 14:
                pfDiscountBO = JsonUtil.toObj(dbData, PreSaleBO.class);
                break;
            case 15:
                pfDiscountBO = JsonUtil.toObj(dbData, SetMealBO.class);
                break;
            case 16:
                pfDiscountBO = JsonUtil.toObj(dbData, AttemptBO.class);
                break;
            case 17:
                pfDiscountBO = JsonUtil.toObj(dbData, LaborDiscountBO.class);
                break;
            case 18:
                pfDiscountBO = JsonUtil.toObj(dbData, FullMoneyNewBO.class);
                break;
            case 19:
                pfDiscountBO = JsonUtil.toObj(dbData, FullQuantityNewBO.class);
                break;
        }
        return pfDiscountBO;
    }
}
