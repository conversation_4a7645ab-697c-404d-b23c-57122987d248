package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.MerchantActivityOuterRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 商家活动外部记录仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
public interface MerchantActivityOuterRecordRepository extends JpaRepository<MerchantActivityOuterRecordDO, Long>, JpaSpecificationExecutor<MerchantActivityOuterRecordDO> {

}
