package com.ssy.lingxi.marketing.model.vo.activity.response;

import cn.hutool.json.JSONObject;
import com.ssy.lingxi.marketing.model.vo.common.response.TaskStepResp;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商家活动返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@Data
public class MerchantActivityDetailResp implements Serializable {

    private static final long serialVersionUID = 2876564500179704555L;

    /**
     * 活动id
     */
    private Long id;

    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     *         17-工价优惠 18-满额促销（新） 19-满量促销（新）
     *         ActivityTypeEnum.class
     * */
    private Integer activityType;

    /**
     * 活动类型名称
     * */
    private String activityTypeName;

    /**
     * 活动名称
     * */
    private String activityName;

    /**
     * 活动开始时间
     * */
    private Long startTime;

    /**
     * 活动结束时间
     * */
    private Long endTime;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 创建时间
     * */
    private Long createTime;

    /**
     * 新用户(不包含会员) 0-否 1-是
     * */
    private Integer newUser;

    /**
     * 老用户(不包含会员) 0-否 1-是
     * */
    private Integer oldUser;

    /**
     * 新会员(仅会员用户) 0-否 1-是
     * */
    private Integer newMember;

    /**
     * 老会员(仅会员用户) 0-否 1-是
     * */
    private Integer oldMember;

    /**
     * 内部状态
     * */
    private Integer innerStatus;

    /**
     * 内部状态名称
     * */
    private String innerStatusName;

    /**
     * 内部任务ID
     * */
    private String innerTaskId;

    /**
     * 内部工作流类型
     * */
    private String innerProcessKey;

    /**
     * 内部工作流当前步骤
     * */
    private Integer innerTaskStep;

    /**
     * 活动定义
     * */
    private JSONObject activityDefined;

    /**
     * 适用会员等级：
     */
    private List<ActivityMemberLevelResp> memberLevelList;

    /**
     * 适用商城
     */
    private List<ActivityShopResp> shopList;

    /**
     * 活动商品列表
     */
    private List<BaseActivityGoodsPageResp> productList;

    /**
     * 外部工作流程
     * */
    private List<TaskStepResp> outerTaskList;

    /**
     * 内部工作流程
     * */
    private List<TaskStepResp> innerTaskList;

    /**
     * 外部流转记录
     * */
    private List<ActivityOuterRecordResp> outerRecordDOList;

    /**
     * 内部工作流程
     * */
    private List<ActivityInnerRecordResp> innerRecordDOList;

    /**
     * 参与用户具体会员ID列表
     */
    private List<String> participantMemberIds;

    /**
     * 参与用户会员信息列表（包含会员ID和名称）
     */
    private List<ParticipantMemberResp> participantMembers;

    /**
     * 反向匹配开关
     * 0-正向匹配（仅指定会员可参与）
     * 1-反向匹配（除指定会员外都可参与）
     */
    private Integer reverseMatch;

    /**
     * 是否允许叠加优惠劵
     */
    private Boolean allowCoupon;

    /**
     * 活动描述
     */
    private String describe;
}
