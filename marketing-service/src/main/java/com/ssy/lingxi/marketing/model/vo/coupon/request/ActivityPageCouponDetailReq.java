package com.ssy.lingxi.marketing.model.vo.coupon.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 活动页优惠券请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
public class ActivityPageCouponDetailReq implements Serializable {

    private static final long serialVersionUID = -8592090004751652840L;

    /**
     * 优惠券集合
     */
    @Valid
    @NotEmpty(message = "优惠券集合不能为空")
    List<BelongTypeCouponReq> couponList;
}
