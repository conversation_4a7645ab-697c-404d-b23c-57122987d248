package com.ssy.lingxi.marketing.service.feign;

import com.ssy.lingxi.marketing.api.model.request.CartOrderReq;
import com.ssy.lingxi.marketing.api.model.request.CouponConsumeReq;
import com.ssy.lingxi.marketing.api.model.request.CouponDetailConsumeReq;
import com.ssy.lingxi.marketing.api.model.request.CouponQueryReq;
import com.ssy.lingxi.marketing.api.model.response.CartOrderResp;
import com.ssy.lingxi.marketing.api.model.response.CouponInfoResp;

import java.util.List;

/**
 * 优惠券内部接口服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/3
 */
public interface ICouponFeignService {

    /**
     * 内部接口 - 校验优惠券可用
     * @param req 请求参数
     * @return 返回结果
     */
    List<CartOrderResp> checkCouponAvailable(CartOrderReq req);

    /**
     * 内部接口 - 提交订单后消耗优惠券
     * @param req 请求参数
     * @return 返回结果
     */
    Void consumeCoupon(CouponConsumeReq req);

    /**
     * 内部接口 - 取消订单后返还优惠券
     * @param req 请求参数
     * @return 返回结果
     */
    Void returnCoupon(List<CouponDetailConsumeReq> req);

    /**
     * 内部接口 - 优惠券详细信息列表
     * @param req 请求参数
     * @return 返回结果
     */
    List<CouponInfoResp> getCouponDetailListByIds(CouponQueryReq req);
}
