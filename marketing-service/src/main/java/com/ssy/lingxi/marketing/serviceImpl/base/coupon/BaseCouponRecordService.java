package com.ssy.lingxi.marketing.serviceImpl.base.coupon;

import cn.hutool.core.date.DateUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.entity.coupon.MerchantCouponDO;
import com.ssy.lingxi.marketing.entity.coupon.MerchantCouponHistoryDO;
import com.ssy.lingxi.marketing.entity.coupon.PlatformCouponDO;
import com.ssy.lingxi.marketing.entity.coupon.PlatformCouponHistoryDO;
import com.ssy.lingxi.marketing.enums.CouponStrOperateEnum;
import com.ssy.lingxi.marketing.enums.MerchantCouponStatusEnum;
import com.ssy.lingxi.marketing.model.dto.ActivityInnerRecordRespDTO;
import com.ssy.lingxi.marketing.model.dto.ActivityRecordPageDataReqDTO;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MerchantCouponHistoryResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.PlatformCouponHistoryResp;
import com.ssy.lingxi.marketing.repository.MerchantCouponHistoryRepository;
import com.ssy.lingxi.marketing.repository.PlatformCouponHistoryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 基础优惠券记录服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/16
 */
@Slf4j
@Service
public class BaseCouponRecordService {

    @Resource
    private MerchantCouponHistoryRepository merchantCouponHistoryRepository;
    @Resource
    private PlatformCouponHistoryRepository platformCouponHistoryRepository;

    /**
     * 查询商家优惠券流转记录
     * @param merchantCouponDO 商家优惠券
     * @return 返回结果
     */
    public List<MerchantCouponHistoryResp> listMerchantCouponRecord(MerchantCouponDO merchantCouponDO) {
        List<MerchantCouponHistoryDO> historyList = merchantCouponHistoryRepository.findAllByCouponId(merchantCouponDO.getId(), Sort.by("id"));

        return historyList.stream().map(historyDO -> {
            MerchantCouponHistoryResp merchantCouponHistoryResp = new MerchantCouponHistoryResp();
            merchantCouponHistoryResp.setCreateTime(historyDO.getCreateTime());
            merchantCouponHistoryResp.setOperatorId(historyDO.getOperatorId());
            merchantCouponHistoryResp.setOperatorName(historyDO.getOperatorName());
            merchantCouponHistoryResp.setOperatorOrgName(historyDO.getOperatorOrgName());
            merchantCouponHistoryResp.setOperatorJobTitle(historyDO.getOperatorJobTitle());
            merchantCouponHistoryResp.setOperationCode(historyDO.getOperationCode());
            merchantCouponHistoryResp.setOperation(Optional.ofNullable(historyDO.getOperationCode()).flatMap(v -> Optional.ofNullable(CouponStrOperateEnum.getNameByCode(v))).orElse(historyDO.getOperation()));
            merchantCouponHistoryResp.setStatus(historyDO.getStatus());
            merchantCouponHistoryResp.setStatusName(Optional.ofNullable(historyDO.getStatus()).flatMap(v -> Optional.ofNullable(MerchantCouponStatusEnum.getNameByCode(v))).orElse(historyDO.getStatusName()));
            merchantCouponHistoryResp.setRemark(historyDO.getRemark());
            return merchantCouponHistoryResp;
        }).collect(Collectors.toList());
    }

    /**
     * 保存商家优惠券流转记录
     * @param loginUser 登录用户信息
     * @param merchantCouponDO 商家优惠券
     * @param operationEnum 操作枚举
     * @param status 状态
     * @param statusDesc 状态描述
     */
    @Transactional
    public void saveMerchantCouponRecord(UserLoginCacheDTO loginUser, MerchantCouponDO merchantCouponDO, CouponStrOperateEnum operationEnum, Integer status, String statusDesc) {
        MerchantCouponHistoryDO merchantCouponHistoryDO = new MerchantCouponHistoryDO();
        merchantCouponHistoryDO.setCouponId(merchantCouponDO.getId());
        merchantCouponHistoryDO.setCreateTime(System.currentTimeMillis());
        merchantCouponHistoryDO.setMemberId(loginUser.getMemberId());
        merchantCouponHistoryDO.setMemberName(loginUser.getMemberName());
        merchantCouponHistoryDO.setRoleId(loginUser.getMemberRoleId());
        merchantCouponHistoryDO.setRoleName(loginUser.getMemberRoleName());
        merchantCouponHistoryDO.setOperatorId(loginUser.getUserId());
        merchantCouponHistoryDO.setOperatorName(loginUser.getUserName());
        merchantCouponHistoryDO.setOperatorRoleName(loginUser.getUserRoleName());
        merchantCouponHistoryDO.setOperatorOrgName(loginUser.getOrgName());
        merchantCouponHistoryDO.setOperatorJobTitle(loginUser.getJobTitle());
        merchantCouponHistoryDO.setOperationCode(operationEnum.getCode());
        merchantCouponHistoryDO.setOperation(operationEnum.getName());
        merchantCouponHistoryDO.setStatus(status);
        merchantCouponHistoryDO.setStatusName(statusDesc);
        merchantCouponHistoryRepository.saveAndFlush(merchantCouponHistoryDO);
    }

    /**
     * 查询平台优惠券流转记录
     * @param platformCouponDO 商家优惠券
     * @return 返回结果
     */
    public List<PlatformCouponHistoryResp> listPlatformCouponRecord(PlatformCouponDO platformCouponDO) {
        List<PlatformCouponHistoryDO> historyList = platformCouponHistoryRepository.findAllByCouponId(platformCouponDO.getId(), Sort.by("id"));

        return historyList.stream().map(historyDO -> {
            PlatformCouponHistoryResp platformCouponHistoryResp = new PlatformCouponHistoryResp();
            platformCouponHistoryResp.setCreateTime(historyDO.getCreateTime());
            platformCouponHistoryResp.setOperatorId(historyDO.getOperatorId());
            platformCouponHistoryResp.setOperatorName(historyDO.getOperatorName());
            platformCouponHistoryResp.setOperatorOrgName(historyDO.getOperatorOrgName());
            platformCouponHistoryResp.setOperatorJobTitle(historyDO.getOperatorJobTitle());
            platformCouponHistoryResp.setOperation(historyDO.getOperation());
            platformCouponHistoryResp.setStatus(historyDO.getStatus());
            platformCouponHistoryResp.setStatusName(historyDO.getStatusName());
            platformCouponHistoryResp.setRemark(historyDO.getRemark());
            return platformCouponHistoryResp;
        }).collect(Collectors.toList());
    }

    /**
     * 保存商家优惠券流转记录
     * @param loginUser 登录用户信息
     * @param platformCouponDO 平台优惠券
     * @param operation 操作
     * @param status 状态
     * @param statusDesc 状态描述
     */
    @Transactional
    public void savePlatformCouponRecord(UserLoginCacheDTO loginUser, PlatformCouponDO platformCouponDO, String operation, Integer status, String statusDesc) {
        PlatformCouponHistoryDO platformCouponHistoryDO = new PlatformCouponHistoryDO();
        platformCouponHistoryDO.setCouponId(platformCouponDO.getId());
        platformCouponHistoryDO.setCreateTime(System.currentTimeMillis());
        platformCouponHistoryDO.setMemberId(loginUser.getMemberId());
        platformCouponHistoryDO.setMemberName(loginUser.getMemberName());
        platformCouponHistoryDO.setRoleId(loginUser.getMemberRoleId());
        platformCouponHistoryDO.setRoleName(loginUser.getMemberRoleName());
        platformCouponHistoryDO.setOperatorId(loginUser.getUserId());
        platformCouponHistoryDO.setOperatorName(loginUser.getUserName());
        platformCouponHistoryDO.setOperatorRoleName(loginUser.getUserRoleName());
        platformCouponHistoryDO.setOperatorOrgName(loginUser.getOrgName());
        platformCouponHistoryDO.setOperatorJobTitle(loginUser.getJobTitle());
        platformCouponHistoryDO.setOperation(operation);
        platformCouponHistoryDO.setStatus(status);
        platformCouponHistoryDO.setStatusName(statusDesc);
        platformCouponHistoryRepository.saveAndFlush(platformCouponHistoryDO);
    }

    public PageDataResp<ActivityInnerRecordRespDTO> pagePlatformActivityInnerRecordCommon(UserLoginCacheDTO loginUser, ActivityRecordPageDataReqDTO pageVO) {
        // 组装查询条件
        Specification<PlatformCouponHistoryDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if(pageVO.getDataId()!=null) {
                list.add(criteriaBuilder.equal(root.get("couponId").as(Long.class), pageVO.getDataId()));
            }
            if(loginUser!=null) {
                list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
                list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            }
            if(pageVO.getStartTime()!=null){
                list.add(criteriaBuilder.ge(root.get("operateTime").as(Long.class), pageVO.getStartTime()));
            }
            if(pageVO.getEndTime()!=null){
                list.add(criteriaBuilder.le(root.get("operateTime").as(Long.class), pageVO.getEndTime()));
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // 组装分页参数
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by(pageVO.getSort()? Sort.Direction.ASC:Sort.Direction.DESC,"createTime"));

        Page<PlatformCouponHistoryDO> recordList = platformCouponHistoryRepository.findAll(spec, page);
        List<ActivityInnerRecordRespDTO> resultList = recordList.stream().map(r -> {
            ActivityInnerRecordRespDTO entity = new ActivityInnerRecordRespDTO();
            entity.setOperator(r.getOperatorName());
            entity.setDepartment(r.getOperatorOrgName());
            entity.setJobTitle(r.getOperatorJobTitle());
            entity.setStatus(r.getStatus());
            entity.setStatusName(r.getStatusName());
            entity.setOperate(r.getOperation());
            entity.setOperateTime(DateUtil.format(DateUtil.date(r.getCreateTime()), "yyyy-MM-dd HH:mm"));
            entity.setOpinion(r.getRemark());
            entity.setDataId(r.getCouponId());
            return entity;
        }).collect(Collectors.toList());
        return new PageDataResp<>(recordList.getTotalElements(), resultList);
    }


    public PageDataResp<ActivityInnerRecordRespDTO> pageMerchantActivityInnerRecordCommon(UserLoginCacheDTO loginUser, ActivityRecordPageDataReqDTO pageVO) {
        // 组装查询条件
        Specification<MerchantCouponHistoryDO> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            if(pageVO.getDataId()!=null) {
                list.add(criteriaBuilder.equal(root.get("couponId").as(Long.class), pageVO.getDataId()));
            }
            if(loginUser!=null) {
                list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), loginUser.getMemberId()));
                list.add(criteriaBuilder.equal(root.get("roleId").as(Long.class), loginUser.getMemberRoleId()));
            }
            if(pageVO.getStartTime()!=null){
                list.add(criteriaBuilder.ge(root.get("createTime").as(Long.class), pageVO.getStartTime()));
            }
            if(pageVO.getEndTime()!=null){
                list.add(criteriaBuilder.le(root.get("createTime").as(Long.class), pageVO.getEndTime()));
            }
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // 组装分页参数
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by(pageVO.getSort()? Sort.Direction.ASC:Sort.Direction.DESC,"createTime"));

        Page<MerchantCouponHistoryDO> recordList = merchantCouponHistoryRepository.findAll(spec, page);
        List<ActivityInnerRecordRespDTO> resultList = recordList.stream().map(r -> {
            ActivityInnerRecordRespDTO entity = new ActivityInnerRecordRespDTO();
            entity.setOperator(r.getOperatorName());
            entity.setDepartment(r.getOperatorOrgName());
            entity.setJobTitle(r.getOperatorJobTitle());
            entity.setStatus(r.getStatus());
            entity.setStatusName(r.getStatusName());
            entity.setOperationCode(r.getOperationCode());
            entity.setOperate(Optional.ofNullable(r.getOperationCode()).flatMap(v -> Optional.ofNullable(CouponStrOperateEnum.getNameByCode(v))).orElse(r.getOperation()));
            entity.setOperateTime(DateUtil.format(DateUtil.date(r.getCreateTime()), "yyyy-MM-dd HH:mm"));
            entity.setOpinion(r.getRemark());
            entity.setDataId(r.getCouponId());
            return entity;
        }).collect(Collectors.toList());
        return new PageDataResp<>(recordList.getTotalElements(), resultList);
    }

}