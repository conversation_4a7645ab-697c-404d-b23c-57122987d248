package com.ssy.lingxi.marketing.entity.coupon;


import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 优惠券适用会员等级实体类
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_member_level",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_member_level_coupon_id_idx", columnList = "couponId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_member_level_belong_type_idx", columnList = "belongType")})
public class CouponMemberLevelDO implements Serializable {

    private static final long serialVersionUID = -8423924900867648015L;

    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 优惠券id
     */
    @Column
    private Long couponId;

    /**
     * 所属类型 1-平台 2-商家
     */
    @Column
    private Integer belongType;

    /**
     * 会员等级id
     */
    @Column(columnDefinition = "int8")
    private Long memberLevelId;

    /**
     * 会员类型
     */
    @Column(columnDefinition = "int4")
    private Integer memberType;

    /**
     * 会员类型名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String memberTypeName;

    /**
     * 角色id
     */
    @Column
    private Long roleId;

    /**
     * 角色名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String roleName;

    /**
     * 会员角色类型
     */
    @Column(columnDefinition = "int4")
    private Integer roleType;

    /**
     * 会员角色类型名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String roleTypeName;

    /**
     * 会员等级
     */
    @Column(columnDefinition = "int4")
    private Integer level;

    /**
     * 会员等级类型
     * */
    @Column(columnDefinition = "int4")
    private Integer levelType;

    /**
     * 会员等级类型名称
     * */
    @Column(columnDefinition = "varchar(30)")
    private String levelTypeName;

    /**
     * 会员等级标签
     * */
    @Column(columnDefinition = "varchar(30)")
    private String levelTag;
}
