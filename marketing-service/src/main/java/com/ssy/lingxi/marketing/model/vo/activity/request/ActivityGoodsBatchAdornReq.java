package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 回显活动商品批量（装修） - 请求
 * <AUTHOR>
 * @since 2021/09/03
 * @version 2.0.0
 */
@Data
public class ActivityGoodsBatchAdornReq implements Serializable {
    private static final long serialVersionUID = -987193027347469811L;

    /**
     * 活动商品ID集合
     */
    @NotEmpty(message = "活动商品ID集合不能为空")
    private List<List<Long>> ids;
}
