package com.ssy.lingxi.marketing.service.coupon;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.marketing.model.vo.coupon.request.AdornCouponListReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.AdornCouponListResp;

import java.util.List;

/**
 * 装修-优惠券
 *
 * <AUTHOR>
 */
public interface IAdornCouponService {

    /**
     * 根据券Id列表查询券列表
     */
    List<AdornCouponListResp> list(UserLoginCacheDTO user, Long shopId, List<AdornCouponListReq> adornCouponListReqList);

}
