package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 商品列表（装修） - 响应
 * <AUTHOR>
 * @since 2021/08/24
 * @version 2.0.0
 */
@Data
public class GoodsListAdornResp implements Serializable {
    private static final long serialVersionUID = 2119085566301185761L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 品类id(最后一级)
     */
    private Long customerCategoryId;

    /**
     * 品类全称id
     */
    private String customerCategoryFullId;

    /**
     * 品类名称(最后一级)
     */
    private String customerCategoryName;

    /**
     * 平台后台品类id(最后一级)
     */
    private Long categoryId;

    /**
     * 平台后台品类名称(最后一级)
     */
    private String categoryName;

    /**
     * 平台后台品类全称id
     */
    private String categoryFullId;

    /**
     * 平台后台品类图片url路径
     */
    private String categoryImageUrl;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 品牌logo
     */
    private String brandLogoUrl;

    /**
     * 商品主图
     */
    private String mainPic;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品标语
     */
    private String slogan;

    /**
     * 商品卖点
     */
    private String[] sellingPoint;

    /**
     * 单位
     */
    private String unitName;

    /**
     * 最小值
     */
    private BigDecimal min;

    /**
     * 最大值
     */
    private BigDecimal max;

    /**
     * 产品定价：1-现货价格, 2-价格需要询价, 3-积分兑换商品
     */
    private Integer priceType;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员角色id
     */
    private Long memberRoleId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 已售数量(会员商品)
     */
    private double sold;

    /**
     * 所属活动（key：id、name、type、belongType。value：活动ID、活动名称、活动类型、所属类型）
     * */
    private List<Map<String, Object>> activityList = new ArrayList<>();
}
