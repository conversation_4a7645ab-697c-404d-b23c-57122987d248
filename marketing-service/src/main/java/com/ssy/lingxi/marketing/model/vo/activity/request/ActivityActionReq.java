package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

/**
 *  取消/终止/重新启动基础参数VO
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@Data
public class ActivityActionReq implements Serializable {

    private static final long serialVersionUID = -7922588885076180743L;

    @NotNull(message = "活动id必须大于等于0")
    @PositiveOrZero(message = "活动id必须大于等于0")
    private Long id;

    @NotNull(message = "时间必须大于等于0")
    @PositiveOrZero(message = "取消时间必须大于等于0")
    private Long time;

    @NotNull(message = "原因必须填")
    private String reason;

}
