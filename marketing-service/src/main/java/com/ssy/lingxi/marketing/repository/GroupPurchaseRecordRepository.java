package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.GroupPurchaseRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * 拼团记录-实体仓库(拼团订单)
 * <AUTHOR> yzc
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface GroupPurchaseRecordRepository extends JpaRepository<GroupPurchaseRecordDO, Long>, JpaSpecificationExecutor<GroupPurchaseRecordDO> {


}
