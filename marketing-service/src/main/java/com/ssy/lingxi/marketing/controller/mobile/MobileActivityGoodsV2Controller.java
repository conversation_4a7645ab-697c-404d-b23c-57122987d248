package com.ssy.lingxi.marketing.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.coupon.request.MobileActivityDiscountCalculateReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MobileActivityDiscountCalculateResp;
import com.ssy.lingxi.marketing.service.IMobileActivityGoodsService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * App - 活动商品
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/6
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/mobile/activity/goods/v2")
public class MobileActivityGoodsV2Controller extends BaseController {

    @Resource
    private IMobileActivityGoodsService mobileActivityGoodsService;

    /**
     * 批量根据商品匹配活动并计算优惠分摊
     **/
//    @PostMapping("/batch/calculate/discount")
//    public WrapperResp<MobileActivityDiscountBatchResp> batchCalculateActivityDiscount(@Valid @RequestBody MobileGoodsBatchRelationTagReq request) {
//        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
//        return WrapperUtil.success(mobileActivityGoodsService.batchCalculateActivityDiscount(loginUser, request));
//    }

    /**
     * 计算活动优惠和工费优惠，返回两种选择方案
     **/
    @PostMapping("/calculate/discount/options")
    public WrapperResp<MobileActivityDiscountCalculateResp> calculateDiscountOptions(@Valid @RequestBody MobileActivityDiscountCalculateReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        Long shopId = this.getHeadersShopId();
        request.setShopId(shopId);
        return WrapperUtil.success(mobileActivityGoodsService.calculateActivityAndWorkFeeDiscount(loginUser, request));
    }

}
