package com.ssy.lingxi.marketing.model.vo.coupon.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 平台优惠券请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@Data
public class PlatformCouponGrantMemberReq implements Serializable {

    private static final long serialVersionUID = 8358891000418818220L;

    /**
     * 发放会员id(下级会员id)
     */
    private Long subMemberId;

    /**
     * 发放角色id(下级角色id)
     * 如果会员多角色, 取自于会员的第一个角色
     */
    private Long subRoleId;

    /**
     * 发放会员名称(下级会员名称)
     */
    private String subMemberName;

    /**
     * 适用会员类型
     */
    private Integer suitableMemberType;
}
