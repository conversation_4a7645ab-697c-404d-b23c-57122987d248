package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.model.vo.common.request.OperateLogDataReq;
import com.ssy.lingxi.marketing.model.vo.common.response.OperateLogInnerResp;
import com.ssy.lingxi.marketing.model.vo.common.response.OperateLogOuterResp;

/**
 *  平台后台-系统管理-操作日志
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/06/17
 */
public interface IOperateLogService {

    /**
     *
     * @param request 接口参数
     * @return 查询结果
     */
    PageDataResp<OperateLogOuterResp> pageOuterLog(OperateLogDataReq request);

    /**
     *
     * @param request 接口参数
     * @return 查询结果
     */
    PageDataResp<OperateLogInnerResp> pageInnerLog(OperateLogDataReq request);
}
