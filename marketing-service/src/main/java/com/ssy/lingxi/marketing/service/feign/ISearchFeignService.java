package com.ssy.lingxi.marketing.service.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.product.api.model.req.CommoditySkuReq;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommoditySkuResp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 *  feign搜索服务内部接口
 * <AUTHOR>
 * @since 2021/11/19
 * @version 2.0.0
 */
public interface ISearchFeignService {

    /**
     * 查询购物车单个商品数量[用于购买数校验]
     * @param shopId
     * @param memberId
     * @param memberRoleId
     * @param commoditySkuId
     * @return
     */
    WrapperResp<BigDecimal> getPurchaseCommodityCount(Long shopId, Long memberId, Long memberRoleId, Long commoditySkuId);

    /**
     * 批量查询商城下的指定skus列表
     * @param commoditySkuReq
     * @return
     */
    WrapperResp<List<EsCommoditySkuResp>> getCommoditySkuMap(CommoditySkuReq commoditySkuReq);

    /**
     * 批量查询商城下的指定skus列表
     * @param shopId
     * @param skuIdInList
     * @return
     */
    Map<Long, EsCommoditySkuResp> getCommoditySkuMap(Long shopId, List<Long> skuIdInList, Long loginMemberId, Long loginRoleId, String provinceCode, String cityCode);

}
