package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.marketing.model.vo.common.response.PageItemResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MerchantCouponConditionResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.PlatformCouponConditionResp;

import java.util.List;

/**
 * 公共服务类 - 业务处理层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/06/17
 */
public interface ICommonService {
    /**
     * 会员类型列表
     * @return 前端展示下拉框
     */
    List<SelectItemResp> listMemberTypes();

    /**
     * 营销活动类型列表
     * @return 前端展示下拉框
     */
    List<PageItemResp> getActivityTypes();

    /**
     * 营销活动类型列表
     * @param excludeCodes 排除的枚举值列表
     * @return 前端展示下拉框
     */
    List<PageItemResp> getActivityTypes(List<Integer> excludeCodes);

    /**
     * 会员活动内部状态列表
     * @return 前端展示下拉框
     */
    List<PageItemResp> getMerchantActivityInnerStatuses();

    /**
     * 平台活动报名内部状态列表
     * @return 前端展示下拉框
     */
    List<PageItemResp> getPlatformActivitySignUpInnerStatuses();

    /**
     * 平台活动报名外部状态列表
     * @return 前端展示下拉框
     */
    List<PageItemResp> getPlatformActivitySignUpOuterStatuses();

    /**
     * 平台营销活动内部状态列表
     * @return 前端展示下拉框
     */
    List<PageItemResp> getPlatformActivityInnerStatuses();

    /**
     * 平台营销活动外部状态列表
     * @return 前端展示下拉框
     */
    List<PageItemResp> getPlatformActivityOuterStatuses();

    /**
     * 查询商家优惠券类型列表
     * @return 前端展示下拉框
     */
    List<SelectItemResp> listMerchantCouponTypes();

    /**
     * 查询平台、商家优惠券领取方式
     * @return 前端展示下拉框
     */
    List<SelectItemResp> listCouponAcquireWays();

    /**
     * 查询商家优惠券状态列表
     * @return 前端展示下拉框
     */
    List<SelectItemResp> listMerchantCouponStatuses();

    /**
     * 查询适用会员列表
     * @return 前端展示下拉框
     */
    List<SelectItemResp> listSuitableMemberTypes();

    /**
     * 查询适用会员列表(平台)
     *
     * @return 前端展示下拉框
     */
    List<SelectItemResp> listPlatformSuitableMemberTypes();

    /**
     * 商家优惠券分页列表相关下拉框
     * @return 前端展示下拉框
     */
    MerchantCouponConditionResp listMerchantCouponConditions();

    /**
     * 平台优惠券类型列表
     * @return 前端展示下拉框
     */
    List<SelectItemResp> listPlatformCouponTypes();

    /**
     * 平台优惠券状态列表
     * @return 前端展示下拉框
     */
    List<SelectItemResp> listPlatformCouponStatuses();

    /**
     * 平台优惠券分页列表相关下拉框
     * @return 前端展示下拉框
     */
    PlatformCouponConditionResp listPlatformCouponConditions();
}
