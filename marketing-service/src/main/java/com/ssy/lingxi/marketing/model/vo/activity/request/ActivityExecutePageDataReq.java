package com.ssy.lingxi.marketing.model.vo.activity.request;

import cn.hutool.core.date.DatePattern;
import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 分页查询-平台活动商品查询-接口参数VO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/08/36
 */
@Getter
@Setter
public class ActivityExecutePageDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = -6311510903820938852L;
    /**
     * 所属类型 1-平台 2-商家
     */
    private Integer belongType;
    /**
     * 活动id
     */
    @NotNull(message = "活动id不能为空")
    private Long activityId;

    /**
     * 商品skuId
     */
    @NotNull(message = "商品skuId不能为空")
    private Long skuId;

    /**
     * 单据类型 1-订单； 2-退货申请单 ActivityRecordTypeEnum
     */
    private Integer recordType;
    /**
     * 商城id
     */
    private Long shopId;
    /**
     * 订单/退货申请单号
     */
    private String orderNo;
    /**
     * 客户名称
     */
    private String memberName;
    /**
     * 活动开始时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate startTime;
    /**
     * 活动结束时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATE_PATTERN)
    private LocalDate endTime;

}
