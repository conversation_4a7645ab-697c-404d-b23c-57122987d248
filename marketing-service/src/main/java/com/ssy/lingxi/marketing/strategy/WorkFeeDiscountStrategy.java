package com.ssy.lingxi.marketing.strategy;

import com.ssy.lingxi.marketing.entity.activity.ActivityGoodsDO;
import com.ssy.lingxi.marketing.entity.activity.MerchantActivityDO;
import com.ssy.lingxi.marketing.model.dto.WorkFeeDiscountResult;
import com.ssy.lingxi.marketing.model.dto.ProductWorkFeeInfo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 工费优惠计算策略接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/25
 */
public interface WorkFeeDiscountStrategy {

    /**
     * 支持的活动类型
     * 
     * @return 活动类型
     */
    Integer getSupportedActivityType();

    /**
     * 计算工费优惠
     * 
     * @param activity 营销活动
     * @param activityGoods 活动商品
     * @param productWorkFeeInfo 商品工费信息
     * @param purchaseQuantity 购买数量
     * @param purchaseAmount 购买金额
     * @return 工费优惠结果
     */
    WorkFeeDiscountResult calculateWorkFeeDiscount(MerchantActivityDO activity, 
                                                   List<ActivityGoodsDO> activityGoods,
                                                   ProductWorkFeeInfo productWorkFeeInfo,
                                                   BigDecimal purchaseQuantity, 
                                                   BigDecimal purchaseAmount);

    /**
     * 获取活动描述
     * 
     * @param activity 营销活动
     * @return 活动描述
     */
    String getActivityDescription(MerchantActivityDO activity);
}