package com.ssy.lingxi.marketing.handler.listener;

import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import com.ssy.lingxi.common.constant.mq.MarketingMqConstant;
import com.ssy.lingxi.component.rabbitMQ.model.dto.MarketingQueueDTO;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.component.redis.service.IRedissonUtils;
import com.ssy.lingxi.marketing.api.enums.MkQueueMessageTypeEnum;
import com.ssy.lingxi.marketing.api.model.request.GiftCouponReq;
import com.ssy.lingxi.marketing.api.model.request.GoodsSalesAddReq;
import com.ssy.lingxi.marketing.constant.MarketingRedisKeyConstant;
import com.ssy.lingxi.marketing.service.IActivityExecuteService;
import com.ssy.lingxi.marketing.service.IActivityOrderService;
import com.ssy.lingxi.marketing.service.IMobileCouponService;
import com.ssy.lingxi.marketing.serviceImpl.component.coupon.CouponDetailExpireComponent;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;

/**
 *  营销服务-消息队列监听类
 * <AUTHOR>
 * @since 2022/3/8
 * @version 2.0.0
 */
@Slf4j
@Component
public class MessageQueueListener {
    @Resource
    private IActivityExecuteService activityExecuteService;
    @Resource
    private IActivityOrderService activityOrderService;
    @Resource
    private CouponDetailExpireComponent couponDetailExpireComponent;
    @Resource
    private IMobileCouponService mobileCouponService;
    @Resource
    private IRedissonUtils redissonUtils;
    @Resource
    private IMqUtils mqUtils;

    /**
     * 迟队列消费者
     * @param message 消息
     * @param channel  消息通道
     */
    @RabbitListener(queues = MarketingMqConstant.MK_DELAY_QUEUE, ackMode = "MANUAL")
    public void delayQueue(Message message, Channel channel) {
        String jsonMessage = new String(message.getBody());
        log.info("内部延迟队列接收到消息 => {}", jsonMessage);
        try {
            MarketingQueueDTO queueDTO = JSONUtil.toBean(jsonMessage, MarketingQueueDTO.class);
            if(Objects.isNull(queueDTO) || Objects.isNull(queueDTO.getType()) || !StringUtils.hasLength(queueDTO.getMessage())) {
                log.info("内部延迟队列消息反序列化后错误 => {}", jsonMessage);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
                return;
            }

            //校验是否可以消费该条消息
            boolean flag = mqUtils.canConsume(MarketingMqConstant.MK_DELAY_EXCHANGE, MarketingMqConstant.MK_DELAY_ROUTING_KEY, JSONUtil.toJsonStr(queueDTO));
            if(!flag) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
                return;
            }

            MkQueueMessageTypeEnum messageType = MkQueueMessageTypeEnum.parse(queueDTO.getType());
            switch (messageType) {
                case GROUP_PURCHASE_TIMEOUT:
                    activityOrderService.updateGroupPurchaseStatus(Long.parseLong(queueDTO.getMessage()));
                    break;
                case COUPON_EXPIRE:
                    couponDetailExpireComponent.handleMqMsgCouponDetailExpire(queueDTO.getMessage());
                    break;
                default:
                    break;
            }
            //确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        } catch (Exception e){
            log.error("内部延迟队列接收消息错误：jsonMessage:{}",jsonMessage ,e);
        }
    }

    /**
     * 普通列消费者
     * @param message 消息
     * @param channel  消息通道
     */
    @RabbitListener(queues = MarketingMqConstant.MK_NORMAL_QUEUE, ackMode = "MANUAL")
    public void normalQueue(Message message, Channel channel) {
        String jsonMessage = new String(message.getBody());
        log.info("内部普通队列接收到消息 => {}", jsonMessage);
        try {
            MarketingQueueDTO queueDTO = JSONUtil.toBean(jsonMessage, MarketingQueueDTO.class);
            if(Objects.isNull(queueDTO) || Objects.isNull(queueDTO.getType()) || !StringUtils.hasLength(queueDTO.getMessage())) {
                log.info("内部普通队列消息反序列化后错误 => {}", jsonMessage);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
                return;
            }

            MkQueueMessageTypeEnum messageType = MkQueueMessageTypeEnum.parse(queueDTO.getType());
            if (Objects.requireNonNull(messageType) == MkQueueMessageTypeEnum.GIFT_COUPON) {
                mobileCouponService.handleMqMsgReceiveCoupon(JSONUtil.toBean(queueDTO.getMessage(), GiftCouponReq.class));
            }
            //确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), true);
        }catch (Exception e){
            log.error("内部普通队列接收消息错误：jsonMessage:{}",jsonMessage ,e);
        }
    }

    /**
     * 更新活动商品销量-redis锁
     * @param message   消息
     * @param channel   通道
     */
    @RabbitListener(queues = MarketingMqConstant.MK_GOODS_SALES_QUEUE,ackMode ="MANUAL")
    public void goodsSalesUpdate(Message message, Channel channel) throws IOException {
        String bodyStr = new String(message.getBody());
        GoodsSalesAddReq req = JSONUtil.toBean(bodyStr, GoodsSalesAddReq.class);
        String addKey=MarketingRedisKeyConstant.getActivityGoodsSalesAddKey(req.getBelongType(),req.getActivityId(),req.getSkuId());
        RLock lock = redissonUtils.getLock(addKey);
        try {
            log.info("活动商品销量监听,收到消息：{}",bodyStr);
            boolean isLock = redissonUtils.tryAndLock(lock, 5);
            if(isLock) {
                activityExecuteService.updateActivityGoodsSales(req);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }else{
                channel.basicRecover(true);
            }
        }catch (Exception e){
            log.error("处理退货处理队通知异常,body:{},异常消息：",bodyStr,e);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }finally {
            redissonUtils.unlock(lock);
        }
    }

}
