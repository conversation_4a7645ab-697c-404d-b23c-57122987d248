package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.coupon.PlatformCouponDetailDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 平台优惠券明细仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/28
 */
public interface PlatformCouponDetailRepository extends JpaRepository<PlatformCouponDetailDO, Long>, JpaSpecificationExecutor<PlatformCouponDetailDO> {

    List<PlatformCouponDetailDO> findAllByCouponId(Long couponId);

    List<PlatformCouponDetailDO> findAllByCouponIdIn(List<Long> couponIdList);

    List<PlatformCouponDetailDO> findAllByCouponIdAndSubMemberIdAndSubRoleId(Long couponId, Long subMemberId, Long subRoleId);

    List<PlatformCouponDetailDO> findAllBySubMemberIdAndSubRoleId(Long subMemberId, Long subRoleId);
}
