package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动适用商城返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@Data
public class ActivityShopResp implements Serializable {

    private static final long serialVersionUID = 9121796924767503300L;
    /**
     * 商城id
     */
    private Long id;
    /**
     * 活动id
     */
    private Long activityId;
    /**
     * 商城id.
     */
    private Long shopId;
    /**
     * 商城名称
     */
    private String shopName;
    /**
     * 商城Logo
     */
    private String logo;
}
