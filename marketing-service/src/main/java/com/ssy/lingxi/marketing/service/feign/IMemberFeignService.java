package com.ssy.lingxi.marketing.service.feign;

import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 会员内部接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/7/19
 */
public interface IMemberFeignService {

    /**
     * 根据会员Id、角色Id查询平台会员信息
     * <p>其中等级为当前会员的平台等级</p>
     * @param memberFeignReqList 接口参数
     * @return 查询结果
     **/
    WrapperResp<List<MemberFeignQueryResp>> listPlatformMembers(List<MemberFeignReq> memberFeignReqList);

    /**
     * 根据会员Id和角色Id，查询下属“服务消费者”角色的会员列表（不区分企业会员、渠道会员）
     * <p>其中等级为下级会员和角色在当前会员和角色下的等级</p>
     * @param feignVO 接口参数
     * @return 查询结果
     */
    WrapperResp<List<MemberFeignQueryResp>> listLowerMembers(MemberFeignSubReq feignVO);

    /**
     * 根据memberLevelConfigId查询会员等级配置信息
     * @param memberLevelConfigId 接口参数
     * @return 返回结果
     */
    WrapperResp<List<MemberFeignLevelConfigResp>> getMemberLevelConfigBatch(List<Long> memberLevelConfigId);

    /**
     * 能力中心 - 营销服务, 查询下级会员适用会员
     * @param memberAndUpperMembersReq 接口参数
     * @return 查询结果
     */
    WrapperResp<List<AtSubMemberSuitableMemberResp>> listAbilitySubMemberSuitableMember(MemberAndUpperMembersReq memberAndUpperMembersReq);

    /**
     * 平台后台 - 营销服务, 查询下级会员适用会员
     * @param memberFeignReq 接口参数
     * @return 查询结果
     */
    WrapperResp<PfSubMemberSuitableMemberResp> getPlatformSubMemberSuitableMember(MemberFeignReq memberFeignReq);

    /**
     * 查询下级会员的价格权益设置
     * @param relationVO
     * @return
     */
    WrapperResp<MemberFeignRightResp> getMemberPriceRight(MemberFeignRelationReq relationVO);

    /**
     * 批量查询下级会员的价格权益
     * @param batchVO 接口参数
     * @return 查询结果
     */
    WrapperResp<List<MemberFeignRightDetailResp>> batchMemberPriceRight(MemberFeignBatchReq batchVO);

    /**
     * 批量查询会员名称、logo
     * @param memberIds
     * @return
     */
    WrapperResp<List<MemberFeignLogoResp>> getMemberLogos(List<Long> memberIds);

    /**
     * 批量查询会员权益.
     * @param memberId
     * @param roleId
     * @param upMemberLis
     * @return
     */
    Map<String, BigDecimal> getMemberPriceRight(Long memberId, Long roleId, List<MemberAndRoleIdDTO> upMemberLis);

    /**
     * 判断是否下级会员
     * @param feignVO
     * @return
     */
    Boolean isSubMember(MemberRelationFeignReq feignVO);

    /**
     * 查询审核通过的下属会员列表
     * @param feignVO
     * @return
     */
    WrapperResp<List<MemberFeignQueryResp>> listAllLowerMembers(MemberFeignReq feignVO);
}
