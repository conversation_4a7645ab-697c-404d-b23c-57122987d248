package com.ssy.lingxi.marketing.entity.activity;

import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.component.base.handler.converter.JpaJsonToMapStringObjectConverter;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Map;

/**
 * 活动页 - DO
 * <AUTHOR>
 * @since 2021/08/05
 * @version 2.0.0
 */
@Getter
@Setter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = ActivityPageDO.TABLE_NAME, indexes = {
        @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_page_member_id_idx", columnList = "memberId"),
        @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_page_role_id_idx", columnList = "roleId"),
        @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_page_type_idx", columnList = "type")
})
public class ActivityPageDO {

    public static final String TABLE_NAME = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_page";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 会员ID
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 角色ID
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 会员名称
     */
    @Column(columnDefinition = "varchar(200)")
    private String memberName;

    /**
     * 类型: 1.平台 2.商家
     */
    @Column(columnDefinition = "int2")
    private Integer type;

    /**
     * 活动页名称
     */
    @Column(columnDefinition = "varchar(64)")
    private String name;

    /**
     * 开始时间
     */
    @Column(columnDefinition = "int8")
    private Long startTime;

    /**
     * 结束时间
     */
    @Column(columnDefinition = "int8")
    private Long endTime;

    /**
     * 适用环境: 1.WEB 2.H5 3.小程序 4.APP
     */
    @Column(columnDefinition = "int2")
    private Integer environment;

    /**
     * 适用环境（冗余）
     */
    @Column(columnDefinition = "varchar(20)")
    private String environmentName;

//    /**
//     * 活动模板ID
//     */
//    @Column(columnDefinition = "int8")
//    private Long templateId;
//
//    /**
//     * 活动模板名称
//     */
//    @Column(columnDefinition = "varchar(20)")
//    private String templateName;
//
//    /**
//     * 活动模板图片
//     */
//    @Column(columnDefinition = "varchar(500)")
//    private String templatePicUrl;

    /**
     * 商城ID
     */
    @Column(columnDefinition = "int8")
    private Long shopId;

    /**
     * 商城名称
     */
    @Column(columnDefinition = "varchar(20)")
    private String shopName;

    /**
     * 状态: 1.待上线 2.已上线 3.进行中 4.已下线 5.已结束
     */
    @Column(columnDefinition = "int2")
    private Integer status = 1;

    /**
     * 状态（冗余）
     */
    @Column(columnDefinition = "varchar(20)")
    private String statusName;

    /**
     * 装修内容
     */
    @Convert(converter = JpaJsonToMapStringObjectConverter.class)
    @Column(columnDefinition="jsonb")
    private Map<String, Object> adornContent;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8")
    private Long createTime = System.currentTimeMillis();

    /**
     * 开始时间的定时任务ID
     */
    @Column(columnDefinition = "int8")
    private Long startTimeTaskId;

    /**
     * 结束时间的定时任务ID
     */
    @Column(columnDefinition = "int8")
    private Long endTimeTaskId;

    /**
     * 商城子域名
     */
    @Transient
    private String url;

    /**
     * 是否为自营商城：false-否；true-是；
     */
    @Column(columnDefinition = "boolean")
    private Boolean isSelf;

}
