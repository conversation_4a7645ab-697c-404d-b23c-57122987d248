package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *  分页查询-平台营销活动报名列表-响应VO
 * <AUTHOR>
 * @since 2021/6/29
 * @version 2.0.0
 */
@Getter
@Setter
public class PfActivitySignUpPageResp implements Serializable {

    private static final long serialVersionUID = -8767142973948856950L;

    /**
     * 活动报名ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 活动类型名称
     */
    private String activityTypeName;

    /**
     * 活动开始时间
     */
    private Long startTime;

    /**
     * 活动结束时间
     */
    private Long endTime;

    /**
     * 报名开始时间
     * */
    private Long signUpStartTime;

    /**
     * 报名结束时间
     * */
    private Long signUpEndTime;

    /**
     * 报名时间
     * */
    private Long createTime;

    /**
     * 外部状态
     * */
    private Integer outerStatus;

    /**
     * 外部状态
     * */
    private String outerStatusName;

    /**
     * 内部状态
     * */
    private Integer innerStatus;

    /**
     * 内部状态
     * */
    private String innerStatusName;
}
