package com.ssy.lingxi.marketing.entity.activity;


import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 活动日汇总记录
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_execute_day",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_execute_day_belong_type_activity_id_idx", columnList = "belongType,activityId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_execute_day_create_time_idx", columnList = "createTime")})
public class ActivityExecuteDayDO implements Serializable {

    private static final long serialVersionUID = 5063889940275000766L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 所属类型 1-平台 2-商家
     */
    @Column
    private Integer belongType;
    /**
     * 活动id
     */
    @Column
    private Long activityId;
    /**
     * 参与客户数
     */
    @Column
    private Integer customerCount;
    /**
     * 已执行订单单数
     */
    @Column
    private Integer orderCount;
    /**
     * 已执行订单金额
     */
    @Column(columnDefinition = "numeric(15,4)")
    private BigDecimal orderAmount;
    /**
     * 创建时间
     * */
    @Column(columnDefinition = "int8")
    private Long createTime=System.currentTimeMillis();

    /**
     * 统计日（统计某日的数据）
     * */
    @Column(columnDefinition = "timestamp")
    private LocalDate day;
}
