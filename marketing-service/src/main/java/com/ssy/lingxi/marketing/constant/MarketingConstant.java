package com.ssy.lingxi.marketing.constant;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  营销公共常量
 * <AUTHOR>
 * @version 2.06.18
 * @since 2022-05-26
 */
public class MarketingConstant implements Serializable {
    private static final long serialVersionUID = -4491405249375077558L;

    /**
     * 系统操作通知数量(默认为1)
     */
    public static final Long SYS_OPERATE_NOTICE_COUNT = 1L;

    /**
     * 详情页日志最大记录数
     */
    public static final Integer DETAIL_LOG_MAX_COUNT = 100;

    /**
     * 套餐主商品默认数量1
     */
    public static final BigDecimal SET_MEAL_MAIN_GOODS_NUM = BigDecimal.ONE;

    /**
     * redis hash key
     */
    public static final String COUPON_QUANTITY_KEY_TEMPLATE = "coupon_qt_ob:bt:{}:id:{}";

    /**
     * 商家优惠券工作流key
     */
    public static final String MERCHANT_COUPON_KEY = "merchant_coupon_verify";

    /**
     * 平台优惠券工作流key
     */
    public static final String PLATFORM_COUPON_KEY = "platform_coupon_verify";

    /**
     * 平台活动外部工作流key
     */
    public static final String PLATFORM_ACTIVITY_OUTER_KEY = "platform_activity_outer_verify_ext";

    /**
     * 平台活动内部工作流key
     */
    public static final String PLATFORM_ACTIVITY_INNER_KEY = "platform_activity_inner_verify";

    /**
     * 商家活动内部工作流key
     */
    public static final String MERCHANT_ACTIVITY_INNER_KEY = "merchant_activity_inner_verify";

    /**
     * 活动定时任务回调url
     */
    public static final String TASK_CALLBACK_URL = "/marketing/feign/task/callback";
}
