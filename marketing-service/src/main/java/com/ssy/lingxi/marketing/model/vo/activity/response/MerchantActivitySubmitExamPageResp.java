package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商家活动列表返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantActivitySubmitExamPageResp extends MerchantActivityPageResp {

    private static final long serialVersionUID = 6525789635288676863L;

    /**
     * 提交按钮
     */
    private boolean submit;

    /**
     * 删除按钮
     */
    private boolean delete;

    /**
     * 修改按钮
     */
    private boolean update;
}
