package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *  商品信息-接口参数VO
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@Getter
@Setter
public class ActivityGoodsReq implements Serializable {

    private static final long serialVersionUID = -6168209320004825141L;

    /**
     * 商品id
     */
    @NotNull(message = "商品id必填")
    @Range(min = 1, message = "商品id必须大于0")
    private Long productId;

    /**
     * skuId
     * */
    @NotNull(message = "skuId必填")
    private Long skuId;

    /**
     * 商品名称
     */
    @NotNull(message = "商品名称必填")
    private String productName;

    /**
     * 规格
     */
    private String type;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品价格
     * */
//    @NotNull(message = "商品价格必填")
    private BigDecimal price;

    /**
     * 预售价格
     */
    private BigDecimal preSelPrice;

    /**
     * 直降价格/起始价格
     * */
    private BigDecimal plummetPrice;

    /**
     * 活动价格/团购价格/秒杀价格/单位定金/砍价底价
     * */
    private BigDecimal activityPrice;

    /**
     * 定金抵扣单价
     * */
    private BigDecimal deductionPrice;

    /**
     * 折扣（如85折，输入85，9折输入90）
     */
    private BigDecimal discount;

    /**
     * 个人限购数量
     */
//    @NotNull(message = "个人限购数量必填")
//    @Range(min = 1, message = "个人限购数量必须大于0")
    private Integer restrictNum;

    /**
     * 活动限购总数量
     * */
//    @NotNull(message = "活动限购总数量必填")
//    @Range(min = 1, message = "活动限购总数量必须大于0")
    private Integer restrictTotalNum;

    /**
     * 活动商品图片
     */
//    @NotNull(message = "活动商品图片地址不能为空")
    private String productImgUrl;

    /**
     * 商品赠品、换购商品、套餐商品（组）
     * */
    private List<ActivityGoodsSubsidiaryGroupReq> goodsSubsidiaryGroupList;

    /**
     * 商品赠优惠券信息（组）
     */
    private List<ActivityGoodsCouponGroupReq> couponGroupList;

    /**
     * 基础工费
     */
    private BigDecimal baseLaborFee;

    /**
     * 附加工费
     */
    private BigDecimal additionalLaborFee;

    /**
     * 件工费
     */
    private BigDecimal pieceLaborFee;
}
