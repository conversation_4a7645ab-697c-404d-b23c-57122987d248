package com.ssy.lingxi.marketing.model.vo.activity.response;

import com.ssy.lingxi.marketing.model.bo.ActivityGoodsSubsidiaryGroupBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 活动商品（装修） - 响应VO
 * <AUTHOR>
 * @since 2021/08/13
 * @version 2.0.0
 */
@Getter
@Setter
public class PfActivitySignUpGoodsAdornResp implements Serializable {
    private static final long serialVersionUID = -1140200946940008773L;

    /**
     * id
     * */
    private Long id;

    /**
     * 平台活动id
     * */
    private Long activityId;

    /**
     * 会员ID
     * */
    private Long memberId;

    /**
     * 会员名称
     * */
    private String memberName;

    /**
     * 会员角色id
     * */
    private Long roleId;

    /**
     * 商品ID
     * */
    private Long productId;

    /**
     * skuId
     * */
    private Long skuId;

    /**
     * 商品名称
     * */
    private String productName;

    /**
     * 商品图片
     * */
    private String productImgUrl;

    /**
     * 规格
     * */
    private String type;

    /**
     * 品类
     * */
    private String category;

    /**
     * 品牌
     * */
    private String brand;

    /**
     * 单位
     * */
    private String unit;

    /**
     * 商品价格/预售价格
     * */
    private BigDecimal price;

    /**
     * 直降价格/起始价格
     * */
    private BigDecimal plummetPrice;

    /**
     * 活动价格/团购价格/秒杀价格/单位定金/砍价底价
     * */
    private BigDecimal activityPrice;

    /**
     * 定金抵扣单价
     * */
    private BigDecimal deductionPrice;

    /**
     * 折扣（如85折，输入85，9折输入90）
     * */
    private Integer discount;

    /**
     * 个人限购数量
     * */
    private Integer restrictNum;

    /**
     * 活动限购总数量
     * */
    private Integer restrictTotalNum;

    /**
     * 所属活动（key：id、name、type。value：活动ID、活动名称、活动类型）
     * */
    private List<Map<String, Object>> activityList;

    /**
     * 配套商品
     * */
    private List<ActivityGoodsSubsidiaryGroupBO> goodsSubList;
}
