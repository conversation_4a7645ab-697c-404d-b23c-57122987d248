package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.ActivityGoodsDO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 活动 - 会员报名商品信息-实体仓库
 * <AUTHOR> yzc
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface ActivityGoodsRepository extends JpaRepository<ActivityGoodsDO, Long>, JpaSpecificationExecutor<ActivityGoodsDO> {

    List<ActivityGoodsDO> findByBelongTypeAndAuditStatusAndActivityIdInAndProductIdIn(Integer belongType, Integer auditStatus, List<Long> activityIdList, List<Long> goodsIdList, Sort sort);

    List<ActivityGoodsDO> findByBelongTypeAndActivityIdInAndProductIdIn(Integer belongType, List<Long> activityIdList, List<Long> goodsIdList, Sort sort);

    List<ActivityGoodsDO> findByBelongTypeAndAuditStatusAndActivityIdIn(Integer belongType, Integer auditStatus, List<Long> activityIdList, Sort sort);

    List<ActivityGoodsDO> findByBelongTypeAndActivityIdIn(Integer belongType, List<Long> activityIdList, Sort sort);

    List<ActivityGoodsDO> findByBelongTypeAndActivityIdIn(Integer belongType, List<Long> activityIdList);

    List<ActivityGoodsDO> findByBelongTypeAndActivityId(Integer belongType, Long activityId);

    List<ActivityGoodsDO> findByBelongTypeAndAuditStatusAndActivityId(Integer belongType, Integer auditStatus, Long activityId);

    List<ActivityGoodsDO> findBySignUpId(Long signUpId);

    List<ActivityGoodsDO> findAllByBelongTypeAndAuditStatusAndSkuIdIn(Integer belongType, Integer auditStatus, List<Long> skuIdList);

    List<ActivityGoodsDO> findAllByBelongTypeAndSkuIdIn(Integer belongType, List<Long> skuIdList);

    ActivityGoodsDO findAllByBelongTypeAndActivityIdAndSkuId(Integer belongType, Long activityId, Long skuId);

    List<ActivityGoodsDO> findAllByBelongTypeAndIdIn(Integer belongType, List<Long> idList);

    List<ActivityGoodsDO> findAllByProductIdIn(List<Long> productIdList);

    List<ActivityGoodsDO> findAllBySkuIdIn(List<Long> skuIdList);

    void deleteByBelongTypeAndActivityId(Integer belongType, Long activityId);

    void deleteByBelongTypeAndActivityIdAndSignUpId(Integer belongType, Long activityId, Long signUpId);

    List<ActivityGoodsDO> findAllByActivityIdInAndSkuIdIn(List<Long> activityId, List<Long> skuId);

    ActivityGoodsDO findFirstByBelongTypeAndActivityIdAndSkuId(Integer belongType, Long activityId, Long skuId);

    List<ActivityGoodsDO> findAllByBelongTypeAndAuditStatusAndSkuId(Integer code, Integer code1, Long skuId);
}
