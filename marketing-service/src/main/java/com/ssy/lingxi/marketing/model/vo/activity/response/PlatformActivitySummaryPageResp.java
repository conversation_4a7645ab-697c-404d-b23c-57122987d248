package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 平台活动列表返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformActivitySummaryPageResp extends PlatformActivityPageResp {

    private static final long serialVersionUID = -8511348081082947804L;

    /**
     * 活动参与类型 1-商家报名活动 2-平台自建活动(仅抽奖)
     */
    private Integer activitySignUpType;

    /**
     * 修改按钮
     */
    private boolean update;

    /**
     * 取消按钮
     */
    private boolean cancel;

    /**
     * 终止按钮
     */
    private boolean stop;

    /**
     * 重启按钮
     */
    private boolean restart;
}
