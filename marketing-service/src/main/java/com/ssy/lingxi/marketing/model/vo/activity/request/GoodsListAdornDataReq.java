package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 商品列表（装修） - 请求
 * <AUTHOR>
 * @since 2021/08/24
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsListAdornDataReq extends PageDataReq {
    private static final long serialVersionUID = -910473312306144670L;

    /**
     * 商城id
     */
    @NotNull(message = "商城ID不能空")
    private Long shopId;

    /**
     * 平台品类id
     */
    private Long categoryId;

    /**
     * 会员品类id
     */
    private Long customerCategoryId;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商家名称
     */
    private String memberName;

    /**
     * 上架开始时间
     */
    private Long publishStartTime;

    /**
     * 上架结束时间
     */
    private Long publishEndTime;

    /**
     * 只查询该id集合数据(逗号隔开)
     */
    private String idInList;

    /**
     * 排除该id集合数据(逗号隔开)
     */
    private String idNotInList;

    /**
     * 商品归属的会员id
     */
    private Long memberId;

    /**
     * 商品归属的会员角色id
     */
    private Long memberRoleId;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 城市的编码
     */
    private String cityCode;
}
