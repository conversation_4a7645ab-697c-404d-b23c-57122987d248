package com.ssy.lingxi.marketing.entity.coupon;

import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Data;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 商家优惠券实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@Data
@Entity
@FieldNameConstants
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "merchant_coupon_detail",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "merchant_coupon_detail_coupon_id_idx", columnList = "coupon_id"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "merchant_coupon_detail_sub_member_id_idx", columnList = "subMemberId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "merchant_coupon_detail_sub_role_id_idx", columnList = "subRoleId")})
public class MerchantCouponDetailDO {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 多对一单向关联商家优惠券
     */
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "coupon_id", referencedColumnName = "id")
    private MerchantCouponDO coupon;

    /**
     * 券码
     */
    @Column(columnDefinition = "varchar(20)")
    private String code;

    /**
     * 券状态 1-已领取 2-已使用 3-已过期
     */
    @Column
    private Integer status;

    /**
     * 店铺会员id(上级会员id)
     */
    @Column
    private Long memberId;

    /**
     * 店铺角色id(上级角色id)
     */
    @Column
    private Long roleId;

    /**
     * 发放会员id(下级会员id)
     */
    @Column
    private Long subMemberId;

    /**
     * 发放角色id(下级角色id)
     * 如果会员多角色, 取自于会员的第一个角色
     */
    @Column
    private Long subRoleId;

    /**
     * 发放会员名称
     */
    @Column
    private String subMemberName;

    /**
     * 适用会员类型
     */
    @Column
    private Integer suitableMemberType;

    /**
     * 有效时间开始
     */
    @Column(columnDefinition = "int8")
    private Long validTimeStart;

    /**
     * 有效时间结束
     */
    @Column(columnDefinition = "int8")
    private Long validTimeEnd;

    /**
     * 关联订单号
     */
    @Column
    private String orderNo;

    /**
     * 订单金额
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal amount;

    /**
     * 下单(使用)时间
     */
    @Column
    private Long useTime;

    /**
     * 商城id
     */
    @Column
    private Long shopId;

    /**
     * 买方（采购）会员Id
     */
    @Column
    private Long buyerMemberId;

    /**
     * 采购会员角色Id
     */
    @Column
    private Long buyerRoleId;

    /**
     * 采购会员名称
     */
    @Column(columnDefinition = "varchar(200)")
    private String buyerMemberName;

    /**
     * 商城名称
     */
    @Column
    private String shopName;

    /**
     * 领取时间
     */
    @Column
    private Long createTime;
}
