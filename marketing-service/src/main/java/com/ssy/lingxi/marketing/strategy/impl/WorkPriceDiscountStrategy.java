package com.ssy.lingxi.marketing.strategy.impl;

import com.ssy.lingxi.marketing.entity.activity.ActivityGoodsDO;
import com.ssy.lingxi.marketing.entity.activity.MerchantActivityDO;
import com.ssy.lingxi.marketing.model.dto.WorkFeeDiscountResult;
import com.ssy.lingxi.marketing.model.dto.ProductWorkFeeInfo;
import com.ssy.lingxi.marketing.strategy.WorkFeeDiscountStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 17-工价优惠策略实现
 * 直接对工费进行折扣优惠
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/25
 */
@Slf4j
@Component
public class WorkPriceDiscountStrategy implements WorkFeeDiscountStrategy {

    private static final Integer ACTIVITY_TYPE = 17;

    @Override
    public Integer getSupportedActivityType() {
        return ACTIVITY_TYPE;
    }

    @Override
    public WorkFeeDiscountResult calculateWorkFeeDiscount(MerchantActivityDO activity, 
                                                          List<ActivityGoodsDO> activityGoods,
                                                          ProductWorkFeeInfo productWorkFeeInfo,
                                                          BigDecimal purchaseQuantity, 
                                                          BigDecimal purchaseAmount) {
        WorkFeeDiscountResult result = new WorkFeeDiscountResult();
        
        if (productWorkFeeInfo == null || !productWorkFeeInfo.getHasWorkFee()) {
            log.warn("商品没有工费信息, skuId: {}", productWorkFeeInfo != null ? productWorkFeeInfo.getSkuId() : "null");
            return result;
        }

        // 使用商品服务查询到的工费信息
        BigDecimal totalOriginalWorkFee = productWorkFeeInfo.getTotalWorkFee();
        result.setOriginalWorkFee(totalOriginalWorkFee);

        if (totalOriginalWorkFee.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("工费为0或负数, activityId: {}, workFee: {}", activity.getId(), totalOriginalWorkFee);
            return result;
        }

        // 工价优惠：直接按照活动商品中设置的折扣率计算
        BigDecimal discountRate = getWorkFeeDiscountRate(activityGoods);
        if (discountRate.compareTo(BigDecimal.ZERO) <= 0 || discountRate.compareTo(BigDecimal.ONE) >= 0) {
            log.warn("工价优惠折扣率异常, activityId: {}, discountRate: {}", activity.getId(), discountRate);
            return result;
        }

        // 计算优惠后的工费
        BigDecimal discountedWorkFee = totalOriginalWorkFee.multiply(discountRate)
            .setScale(2, RoundingMode.HALF_UP);
        BigDecimal discountAmount = totalOriginalWorkFee.subtract(discountedWorkFee);

        result.setSatisfied(true);
        result.setDiscountedWorkFee(discountedWorkFee);
        result.setWorkFeeDiscountAmount(discountAmount);
        result.setWorkFeeDiscountRate(discountRate);
        result.setDiscountTypeDescription("工价" + discountRate.multiply(BigDecimal.TEN).setScale(0) + "折");
        result.setActivityDescription(getActivityDescription(activity));

        log.info("工价优惠计算完成, skuId: {}, activityId: {}, 原工费: {}, 优惠后工费: {}, 优惠金额: {}", 
                productWorkFeeInfo.getSkuId(), activity.getId(), totalOriginalWorkFee, discountedWorkFee, discountAmount);

        return result;
    }

    @Override
    public String getActivityDescription(MerchantActivityDO activity) {
        return "工价优惠 - " + activity.getActivityName();
    }

    /**
     * 计算总工费
     * 工费 = 商品单价 × 购买数量（这里假设商品价格就是工费单价）
     */
    private BigDecimal calculateTotalWorkFee(List<ActivityGoodsDO> activityGoods, BigDecimal purchaseQuantity) {
        final BigDecimal finalQuantity = (purchaseQuantity == null || purchaseQuantity.compareTo(BigDecimal.ZERO) <= 0) 
            ? BigDecimal.ONE : purchaseQuantity; // 默认数量为1

        return activityGoods.stream()
            .filter(goods -> goods.getPrice() != null)
            .map(goods -> goods.getPrice().multiply(finalQuantity))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取工费折扣率
     * 从活动商品的折扣字段中获取
     */
    private BigDecimal getWorkFeeDiscountRate(List<ActivityGoodsDO> activityGoods) {
        // 取第一个商品的折扣率作为工费折扣率
        return activityGoods.stream()
            .filter(goods -> goods.getDiscount() != null && goods.getDiscount().compareTo(BigDecimal.ZERO) > 0)
            .findFirst()
            .map(goods -> goods.getDiscount().divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP))
            .orElse(new BigDecimal("0.8")); // 默认8折
    }
}