package com.ssy.lingxi.marketing.config;

import com.ssy.lingxi.common.constant.mq.MarketingMqConstant;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.CustomExchange;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 *  营销服务配置
 * <AUTHOR>
 * @since 2022/3/8
 * @version 2.0.0
 */
@Configuration
public class MarketingMqConfig {

    @Resource
    private IMqUtils mqUtils;

    /*********************************** 营销活动-延时队列（拼团过期/） *****************************************/
    @Bean
    public CustomExchange mkDelayExchange() {
        return mqUtils.createDelayExchange(MarketingMqConstant.MK_DELAY_EXCHANGE);
    }

    @Bean
    public Queue mkDelayQueue() {
        return mqUtils.createDelayQueue(MarketingMqConstant.MK_DELAY_QUEUE);
    }

    @Bean
    public Binding mkDelayBinding() {
        return mqUtils.binding(mkDelayQueue(), mkDelayExchange(), MarketingMqConstant.MK_DELAY_ROUTING_KEY);
    }


    /*********************************** 营销活动-普通队列（优惠券过期/） *****************************************/
    @Bean
    public DirectExchange mkNormalExchange() {
        return mqUtils.createExchange(MarketingMqConstant.MK_NORMAL_EXCHANGE);
    }

    @Bean
    public Queue mkNormalQueue() {
        return mqUtils.createQueue(MarketingMqConstant.MK_NORMAL_QUEUE);
    }

    @Bean
    public Binding mkNormalBinding() {
        return mqUtils.binding(mkNormalQueue(), mkNormalExchange(), MarketingMqConstant.MK_NORMAL_ROUTING_KEY);
    }


    /*********************************** 营销活动-活动商品销量更新操作通知-加锁 *****************************************/

    @Bean
    DirectExchange mkGoodsSalesExchange() {
        return mqUtils.createExchange(MarketingMqConstant.MK_GOODS_SALES_EXCHANGE);
    }

    @Bean
    Queue mkGoodsSalesQueue() {
        return mqUtils.createQueue(MarketingMqConstant.MK_GOODS_SALES_QUEUE);
    }

    @Bean
    Binding mkGoodsSalesBinding() {
        return mqUtils.binding(mkGoodsSalesQueue(), mkGoodsSalesExchange(), MarketingMqConstant.MK_GOODS_SALESROUTINGKEY);
    }

}
