package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.marketing.api.model.request.GoodsSalesReq;
import com.ssy.lingxi.marketing.api.model.response.CartGiveResp;
import com.ssy.lingxi.marketing.model.dto.ActivityGoodsSubsidiaryDTO;
import com.ssy.lingxi.marketing.model.dto.SubsidiaryDTO;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityGoodsSalesReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityGoodsRestrictNumResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityGoodsSalesResp;

import java.util.List;
import java.util.Map;

/**
 * 活动缓存服务类
 * <AUTHOR> yzc
 * @version 2.0.0
 * @since 2021/8/19
 */
public interface IActivityGoodsCacheService {
    /**
     /**
     * 更新活动商品销量
     **/
    Void updateGoodsSales(GoodsSalesReq goodsSalesReq);

    /**
     * 查询活动sku总销量、会员总购买数量
     */
    ActivityGoodsSalesResp getGoodsSale(ActivityGoodsSalesReq salesReq);

    /**
     * 查询活动商品销量列表
     */
    void getActivityGoodsSale(Integer belongType,Long activityId);

    /**
     * 查询活动商品限购数量
     */
    Map<String, ActivityGoodsRestrictNumResp> getActivityGoodsRestrictNum(List<Long> activityIds, List<Long> skuIds);

    /**
     * 查询sku参与换购活动商品的阶梯条件
     */
    List<ActivityGoodsSubsidiaryDTO> selectSkuSwapActivityLadder(Integer belongType, Long activityId, Long skuId, Long subSkuId);

    /**
     * 查询sku参与赠送活动的赠商品
     */
    List<CartGiveResp> selectSkuGiveProductList(Integer belongType, Long activityId, Long skuId);

    /**
     * 查询sku参与赠送活动的赠优惠券
     */
    List<CartGiveResp> selectSkuGiveCouponList(Integer belongType, Long activityId, Long skuId);

    /**
     * 查询sku参与套餐活动的搭配商品列表
     */
    List<SubsidiaryDTO> selectSkuSetMealList(Integer belongType, Long activityId, Long skuId, Integer groupNo);
}
