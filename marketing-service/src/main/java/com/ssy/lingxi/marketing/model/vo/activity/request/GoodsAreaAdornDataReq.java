package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商品区（装修） - 请求
 * <AUTHOR>
 * @since 2021/09/14
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GoodsAreaAdornDataReq extends PageDataReq {
    private static final long serialVersionUID = 7299647617025946591L;

    /**
     * 商城id
     */
    @NotNull(message = "商城ID不能空")
    private Long shopId;

    /**
     * 类型为自定义商品时所传的ID
     */
    private List<Long> idInList;

    /**
     * 平台品类id
     */
    private Long categoryId;
    /**
     * 会员品类id
     */
    private Long customerCategoryId;

    /**
     * 商品归属的会员id
     */
    private Long memberId;

    /**
     * 商品归属的会员角色id
     */
    private Long memberRoleId;

    /**
     * 登录用户的会员id
     */
    private Long loginMemberId;

    /**
     * 登录用户的会员角色id
     */
    private Long loginMemberRoleId;

    /**
     * 排序方式：1-销量从高到低, 2-信用从高到低, 3-价格从高到低, 4-价格从低到高, 5-上架时间倒序
     */
    @NotNull(message = "排序方式不能为空")
    private Integer type;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 城市的编码
     */
    private String cityCode;
}
