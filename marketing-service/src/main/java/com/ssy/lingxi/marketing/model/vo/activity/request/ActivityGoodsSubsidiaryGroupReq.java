package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品-附属(赠品/换购/套餐商品)信息-组-接口参数VO
 * <AUTHOR> yzc
 * @since 2021/08/16
 * @version 2.0.0
 */
@Getter
@Setter
public class ActivityGoodsSubsidiaryGroupReq implements Serializable {
    private static final long serialVersionUID = -6598853372056393189L;

    /**
     * 分组编号/优惠阶梯/换购阶梯
     */
//    @NotNull(message = "分组编号/优惠阶梯/换购阶梯必填")
    private Integer groupNo;

    /**
     * 换购门槛/优惠门槛数量或金额
     */
    private BigDecimal limitValue;

    /**
     * 套餐价格
     * */
    private BigDecimal groupPrice;

    /**
     * 明细
     * */
//    @Valid
    private List<ActivityGoodsSubsidiaryGroupDetailsReq> list;
}
