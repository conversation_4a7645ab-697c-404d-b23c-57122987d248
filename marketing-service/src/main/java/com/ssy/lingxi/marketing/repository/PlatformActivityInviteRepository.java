package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.PlatformActivityInviteDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 平台活动-邀请
 * <AUTHOR>
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface PlatformActivityInviteRepository extends JpaRepository<PlatformActivityInviteDO, Long>, JpaSpecificationExecutor<PlatformActivityInviteDO> {

    /**
     * 删除平台活动报名信息
     * @param id 平台活动id
     */
    void deleteByPlatformActivityId(Long id);

    /**
     * 删除平台活动报名信息
     * @param id 平台活动id
     */
    void deleteByPlatformActivityIdIn(List<Long> id);

    /**
     * 查询平台活动报名信息
     * @param id 平台活动id
     */
    List<PlatformActivityInviteDO> findByPlatformActivityId(Long id);
}
