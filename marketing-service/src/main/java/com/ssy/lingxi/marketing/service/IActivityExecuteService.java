package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.api.model.request.GoodsSalesAddReq;
import com.ssy.lingxi.marketing.api.model.request.GoodsSalesItemActivityReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.*;
import com.ssy.lingxi.marketing.model.vo.activity.response.*;

import java.util.List;

/**
 * 活动执行服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/23
 */
public interface IActivityExecuteService {

    /**
     * 根据订单号与skuId查询参与的活动id
     * @param orderNo 订单编号
     * @param skuId  商品skuId
     * @return 查询结果
     */
    List<GoodsSalesItemActivityReq> getOrderJoinActivity(String orderNo, Long skuId);

    /**
     * 能力中心-查询商户活动执行列表
     * @param sysUser 登录用户
     * @param pageReq 接口参数
     * @return 查询结果
     */
    PageDataResp<MerchantActivityExecutePageResp> pageMerchant(UserLoginCacheDTO sysUser, MerchantActivityCommonPageDataReq pageReq);

    /**
     * 能力中心-查询商户活动执行详情
     * @param sysUser 登录用户
     * @param req 接口参数
     * @return 查询结果
     */
    MerchantActivityDetailResp detailMerchant(UserLoginCacheDTO sysUser, CommonIdReq req);

    /**
     * 能力中心-查询商户活动执行详情-商品分页列表
     * @param sysUser 登录用户
     * @param req 接口参数
     * @return 查询结果
     */
    PageDataResp<ActivityExecuteGoodsPageResp> pageByActivityGoodsMerchant(UserLoginCacheDTO sysUser, PfActivityGoodsPageDataReq req);

    /**
     * 能力中心-查询-平台活动执行列表
     * @param sysUser 登录用户
     * @param pageReq 接口参数
     * @return 查询结果
     */
    PageDataResp<PlatformActivityExecuteAbilityPageResp> pagePlatformOfAbility(UserLoginCacheDTO sysUser, PfActivitySignUpPageDataReq pageReq);

    /**
     * 能力中心-查询-平台活动执行详情
     * @param sysUser 登录用户
     * @param req 接口参数
     * @return 查询结果
     */
    PlatformActivityDetailResp detailPlatformOfAbility(UserLoginCacheDTO sysUser, CommonIdReq req);

    /**
     * 能力中心-查询-平台活动执行详情-商品分页列表
     * @param sysUser 登录用户
     * @param req 接口参数
     * @return 查询结果
     */
    PageDataResp<ActivityExecuteGoodsPageResp> pageByActivityGoodsPlatformOfAbility(UserLoginCacheDTO sysUser, PfActivityGoodsPageDataReq req);

    /**
     * 能力中心-查询-平台活动执行详情-商品分页列表-执行明细分页
     * @param sysUser 登录用户
     * @param req 接口参数
     * @return 查询结果
     */
    PageDataResp<ActivityExecuteDetailPageResp> pageMerchantExecuteDetailOfAbility(UserLoginCacheDTO sysUser, ActivityExecutePageDataReq req);
    /**
     * 平台后台-查询-平台活动执行列表
     * @param sysUser 登录用户
     * @param pageReq 接口参数
     * @return 查询结果
     */
    PageDataResp<PlatformActivityExecutePageResp> pagePlatform(UserLoginCacheDTO sysUser, PlatformActivityPageDataReq pageReq);

    /**
     * 平台后台-查询-平台活动执行详情
     * @param sysUser 登录用户
     * @param req 接口参数
     * @return 查询结果
     */
    PlatformActivityDetailResp detailPlatform(UserLoginCacheDTO sysUser, CommonIdReq req);

    /**
     * 平台后台-查询-平台活动执行详情-商品分页列表
     * @param sysUser 登录用户
     * @param req 接口参数
     * @return 查询结果
     */
    PageDataResp<ActivityExecuteGoodsPageResp> pageByActivityGoodsPlatform(UserLoginCacheDTO sysUser, PfActivityGoodsPageDataReq req);

    /**
     * 更新活动商品销量、添加活动执行记录
     * @param req 接口参数
     */
    void updateActivityGoodsSales(GoodsSalesAddReq req);
}
