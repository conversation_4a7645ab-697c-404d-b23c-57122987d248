package com.ssy.lingxi.marketing.entity.coupon;

import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Data;

import javax.persistence.*;

/**
 * 平台优惠券券内部流转记录类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@Data
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_coupon_history")
public class PlatformCouponHistoryDO {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 平台优惠券券id
     */
    @Column
    private Long couponId;

    /**
     * 操作时间
     */
    @Column(columnDefinition = "int8")
    private Long createTime;

    /**
     * 上级会员Id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 上级会员名称
     */
    @Column(columnDefinition = "varchar(200)")
    private String memberName;

    /**
     * 上级会员角色Id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 上级会员角色名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String roleName;

    /**
     * 操作人员用户Id
     */
    @Column(columnDefinition = "int8")
    private Long operatorId;

    /**
     * 操作人员姓名
     */
    @Column(columnDefinition = "varchar(50)")
    private String operatorName;

    /**
     * 操作员角色名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String operatorRoleName;

    /**
     * 操作人员组织机构名称
     */
    @Column(columnDefinition = "varchar(40)")
    private String operatorOrgName;

    /**
     * 操作人员职位
     */
    @Column(columnDefinition = "varchar(40)")
    private String operatorJobTitle;

    /**
     * 操作方法
     */
    @Column(columnDefinition = "varchar(20)")
    private String operation;

    /**
     * 会员考评内部状态
     */
    @Column(columnDefinition = "int2")
    private Integer status;

    /**
     * 内部状态描述
     */
    @Column(columnDefinition = "varchar(30)")
    private String statusName;

    /**
     * 操作说明
     */
    @Column(columnDefinition = "varchar(400)")
    private String remark;
}
