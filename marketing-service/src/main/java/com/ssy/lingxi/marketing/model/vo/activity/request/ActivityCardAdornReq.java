package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 活动卡片（装修） - 请求
 * <AUTHOR>
 * @since 2021/09/07
 * @version 2.0.0
 */
@Data
public class ActivityCardAdornReq implements Serializable {
    private static final long serialVersionUID = -7802587127933506555L;

    /**
     * 活动商品ID集合
     */
    @NotEmpty(message = "活动商品ID集不能为空")
    private List<Long> ids;
}