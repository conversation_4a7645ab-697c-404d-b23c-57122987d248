package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/13
 */
@Data
public class MobileBaseCouponDetailResp {

    private static final long serialVersionUID = -8351304301293444281L;

    /**
     * 领取记录id
     */
    private Long id;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 所属类型 1-平台 2-商家
     */
    private Integer belongType;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券类型
     * 如果所属类型为平台则有 1-0元抵扣券 2-平台通用优惠券
     * 如果所属类型为商家则有 1-0元抵扣券 2-商家通用优惠券 3-品类优惠券 4-品牌优惠券 5-商品优惠券
     */
    private Integer type;

    /**
     * 优惠券类型名称
     */
    private String typeName;

    /**
     * 券面额
     */
    private BigDecimal denomination;

    /**
     * 使用条件, 满多少金额可用
     */
    private BigDecimal useConditionMoney;

    /**
     * 有效时间开始
     */
    private Long validTimeStart;

    /**
     * 有效时间结束
     */
    private Long validTimeEnd;

    /**
     * 领取时间
     */
    private Long crateTime;
}
