package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;

/**
 *  修改时间-平台活动管理接口参数VO
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@Data
public class PlatformActivityUpdateTimeReq implements Serializable {

    private static final long serialVersionUID = -7922588885076180743L;
    @NotNull(message = "活动id必须大于等于0")
    @PositiveOrZero(message = "活动id必须大于等于0")
    private Long id;

    /**
     * 活动开始时间
     * */
    @NotNull(message = "活动开始时间不能为空")
    private Long startTime;

    /**
     * 活动结束时间
     * */
    @NotNull(message = "活动结束时间不能为空")
    private Long endTime;

    /**
     * 报名开始时间(活动参与类型=1时必填)
     * */
    private Long signUpStartTime;

    /**
     * 报名结束时间(活动参与类型=1时必填)
     * */
    private Long signUpEndTime;

}
