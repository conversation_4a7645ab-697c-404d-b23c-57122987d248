package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 *  平台活动-详情--响应VO
 * <AUTHOR>
 * @since 2021/6/19
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformActivityDetailResp extends BasePlatformActivityDetailResp {

    private static final long serialVersionUID = -517031506594871700L;

    /**
     * 邀请全部会员 0-否 1-是
     */
    private Integer inviteType;

    /**
     * 被邀请的会员
     */
    private List<PlatformActivityInviteResp> inviteList;
}
