package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import com.ssy.lingxi.marketing.model.vo.common.request.CommonAgreeReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.*;
import com.ssy.lingxi.marketing.model.vo.coupon.response.*;

import java.util.List;

/**
 * 商家优惠券服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
public interface IMerchantCouponService {

    /**
     * 商家优惠劵查询 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<MerchantCouponSummaryPageResp> pageSummaryMerchantCoupon(MerchantCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核商家优惠劵 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<MerchantCouponSubmitAuditPageResp> pageSubmitAuditMerchantCoupon(MerchantCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核商家优惠劵(一级) - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<MerchantCouponAuditPageResp> pageAuditOneMerchantCoupon(MerchantCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核商家优惠劵(二级) - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<MerchantCouponAuditPageResp> pageAuditTwoMerchantCoupon(MerchantCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家优惠劵执行 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<MerchantCouponSubmitPageResp> pageSubmitMerchantCoupon(MerchantCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交商家优惠劵 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<MerchantCouponExecutePageResp> pageExecuteMerchantCoupon(MerchantCouponPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家优惠券详情
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    MerchantCouponResp getMerchantCoupon(CommonIdReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家优惠券发券详情
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    MerchantCouponGrantResp getGrantMerchantCoupon(CommonIdReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核商家优惠劵 - 新增
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void addMerchantCoupon(MerchantCouponAddReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核商家优惠劵 - 修改
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void updateMerchantCoupon(MerchantCouponUpdateReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核商家优惠劵 - 删除
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void deleteMerchantCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交审核商家优惠劵 - 提交审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void submitAuditMerchantCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核商家优惠劵(一级) - 提交审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void auditOneMerchantCoupon(CommonAgreeReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核商家优惠劵(一级) - 批量提交审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void batchAuditOneMerchantCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核商家优惠劵(二级) - 审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void auditTwoMerchantCoupon(CommonAgreeReq request, UserLoginCacheDTO loginUser);

    /**
     * 待审核商家优惠劵(二级) - 批量审核
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void batchAuditTwoMerchantCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 待提交商家优惠劵 - 批量提交
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void submitMerchantCoupon(CommonIdListReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家优惠劵执行 - 发券
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void grantMerchantCoupon(MerchantCouponGrantReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家优惠劵查询 - 修改
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void modificationMerchantCoupon(MerchantCouponModificationReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家优惠劵查询 - 终止
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void stopMerchantCoupon(MerchantCouponOperationReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家优惠劵查询 - 重启
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void restartMerchantCoupon(MerchantCouponOperationReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家优惠劵查询 - 取消
     * @param request 接口参数
     * @param loginUser 登录用户信息
     */
    Void cancelMerchantCoupon(MerchantCouponOperationReq request, UserLoginCacheDTO loginUser);

    /**
     * 商家优惠劵执行明细 - 查询条件
     * @param request 接口参数
     * @return 返回结果
     */
    MerchantCouponDetailConditionResp getExecuteMerchantCouponDetailCondition(CommonIdReq request);

    /**
     * 商家优惠劵执行明细 - 分页列表
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<MerchantCouponDetailPageResp> pageExecuteMerchantCouponDetail(MerchantCouponDetailPageDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 营销活动 - 附属商品 - 选择附属优惠券查询条件
     * @return 返回结果
     */
    List<SelectItemResp> selectSubsidiaryCouponCondition();

    /**
     * 营销活动 - 附属商品 - 选择优惠券
     *
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<ActivityGoodsCouponResp> selectSubsidiaryCouponList(ActivityMerchantSubsidiaryCouponDataReq request, UserLoginCacheDTO loginUser);

    /**
     * 营销活动 - 奖品 - 选择0元抵扣券
     *
     * @param request 接口参数
     * @param loginUser 登录用户信息
     * @return 返回结果
     */
    PageDataResp<ActivityPrizeCouponResp> selectPrizeCouponList(ActivityMerchantPrizeCouponDataReq request, UserLoginCacheDTO loginUser);
}
