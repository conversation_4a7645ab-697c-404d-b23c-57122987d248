package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 商家优惠券历史记录返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@Data
public class MerchantCouponHistoryResp implements Serializable {

    private static final long serialVersionUID = -6034523216252912046L;

    /**
     * 操作时间
     */
    private Long createTime;

    /**
     * 操作人员用户Id
     */
    private Long operatorId;

    /**
     * 操作人员姓名
     */
    private String operatorName;

    /**
     * 操作人员组织机构名称
     */
    private String operatorOrgName;

    /**
     * 操作人员职位
     */
    private String operatorJobTitle;

    /**
     * 操作方法枚举
     */
    private Integer operationCode;

    /**
     * 操作方法
     */
    private String operation;

    /**
     * 会员考评内部状态
     */
    private Integer status;

    /**
     * 内部状态描述
     */
    private String statusName;

    /**
     * 操作说明
     */
    private String remark;

}
