package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.coupon.CouponGoodsDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 优惠券适用商品仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/26
 */
public interface CouponGoodsRepository extends JpaRepository<CouponGoodsDO, Long>, JpaSpecificationExecutor<CouponGoodsDO> {

    boolean existsByBelongTypeAndCouponId(Integer belongType, Long id);

    List<CouponGoodsDO> findAllByBelongTypeAndCouponId(Integer belongType, Long id);

    CouponGoodsDO findFirstByBelongTypeAndCouponId(Integer belongType, Long id);

    List<CouponGoodsDO> findAllByBelongTypeAndCouponIdIn(Integer belongType, List<Long> ids);

    @Transactional
    void deleteByBelongTypeAndCouponId(Integer belongType, Long id);
}
