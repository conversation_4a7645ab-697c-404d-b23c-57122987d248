package com.ssy.lingxi.marketing.model.vo.coupon.request;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商家优惠券请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@Data
public class MerchantCouponModificationReq implements Serializable {

    private static final long serialVersionUID = -7900704585687852322L;

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空")
    private Long id;

    /**
     * 领(发)券起始时间
     */
    @NotNull(message = "领(发)券起始时间不能为空")
    private Long releaseTimeStart;

    /**
     * 领(发)券结束时间
     */
    @NotNull(message = "领(发)券结束时间不能为空")
    private Long releaseTimeEnd;

    /**
     * 发券数量
     */
    @NotNull(message = "发券数量不能为空")
    @Min(value = 0, message = "发券数量要大于0")
    private Integer quantity;
}


