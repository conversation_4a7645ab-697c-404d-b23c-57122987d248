package com.ssy.lingxi.marketing.entity.activity;


import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 活动订单记录
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@FieldNameConstants
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_execute_record",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_execute_record_belong_type_activity_id_sku_id_idx", columnList = "belongType,activityId,skuId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_execute_record_order_id_idx", columnList = "orderId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_execute_record_order_no_idx", columnList = "orderNo"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_execute_record_order_time_idx", columnList = "orderTime")})
public class ActivityExecuteRecordDO implements Serializable {

    private static final long serialVersionUID = 5063889940275000766L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    /**
     * 单据类型 1-订单； 2-退货申请单 ActivityRecordTypeEnum
     */
    @Column
    private Integer recordType;
    /**
     * 所属类型 1-平台 2-商家
     */
    @Column
    private Integer belongType;
    /**
     * 活动id
     */
    @Column
    private Long activityId;
    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     *         ActivityTypeEnum.class
     * */
    @Column(columnDefinition = "int2")
    private Integer activityType;

    /**
     * 订单/退货申请单id
     */
    @Column(columnDefinition = "int8")
    private Long orderId;
    /**
     * 订单/退货申请单号
     */
    @Column(columnDefinition = "varchar(50)")
    private String orderNo;
    /**
     * 源订单编号（仅退货申请单存在）
     */
    @Column(columnDefinition = "varchar(50)")
    private String originOrderNo;
    /**
     * 采购会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;
    /**
     * 采购角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;
    /**
     * 采购会员名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String memberName;
    /**
     * 采购会员头像
     */
    @Column(columnDefinition = "varchar(512)")
    private String logo;
    /**
     * 商品id
     */
    @Column(columnDefinition = "int8")
    private Long productId;
    /**
     * skuId
     */
    @Column(columnDefinition = "int8")
    private Long skuId;
    /**
     * 购买数量/退货数量
     */
    @Column(columnDefinition = "numeric(15,4)")
    private BigDecimal quantity;
    /**
     * 实购/退款金额（订单商品购买价*数量）
     */
    @Column(columnDefinition = "numeric(15,4)")
    private BigDecimal amount;
    /**
     * 创建时间
     * */
    @Column(columnDefinition = "int8")
    private Long createTime=System.currentTimeMillis();

    /**
     * 商城id
     * */
    @Column(columnDefinition = "int8")
    private Long shopId;
    /**
     * 商城名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String shopName;
    /**
     * 单据时间（来源订单或售后退货）
     * */
    @Column(columnDefinition = "int8")
    private Long orderTime;

    /**
     * 订单状态：1=正常；0=已取消;
     */
    @Column
    private Integer status=1;
}
