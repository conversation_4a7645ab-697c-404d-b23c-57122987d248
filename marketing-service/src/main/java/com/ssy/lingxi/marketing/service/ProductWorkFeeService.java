package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.marketing.model.dto.ProductWorkFeeInfo;

import java.math.BigDecimal;

/**
 * 商品工费查询服务接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/25
 */
public interface ProductWorkFeeService {

    /**
     * 根据SKU ID查询商品工费信息
     * 
     * @param skuId 商品SKU ID
     * @param purchaseQuantity 购买数量
     * @return 商品工费信息
     */
    ProductWorkFeeInfo getProductWorkFeeInfo(Long skuId, BigDecimal purchaseQuantity);

    /**
     * 计算商品总工费
     * 
     * @param skuId 商品SKU ID  
     * @param purchaseQuantity 购买数量
     * @return 总工费
     */
    BigDecimal calculateTotalWorkFee(Long skuId, BigDecimal purchaseQuantity);

    /**
     * 检查商品是否包含工费
     * 
     * @param skuId 商品SKU ID
     * @return 是否包含工费
     */
    Boolean hasWorkFee(Long skuId);
}