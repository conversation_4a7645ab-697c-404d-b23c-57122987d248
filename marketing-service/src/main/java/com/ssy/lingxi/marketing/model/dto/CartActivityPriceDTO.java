package com.ssy.lingxi.marketing.model.dto;

import com.ssy.lingxi.marketing.api.model.response.CartGiveResp;
import com.ssy.lingxi.marketing.api.model.response.SkuJoinActivityResp;
import com.ssy.lingxi.marketing.model.bo.ActivityGoodsBO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 *  返回购物车商品活动到手价
 * <AUTHOR>
 * @since 2021/10/14
 * @version 2.0.0
 */
@Getter
@Setter
public class CartActivityPriceDTO implements Serializable {

    private static final long serialVersionUID = -4001769329159643964L;

    /**
     * 商城id
     */
    private Long shopId;
    /**
     * 商品id
     */
    private Long productId;
    /**
     * skuid
     */
    private Long skuId;
    /**
     * 商品类型: 1-会员商品; 2-渠道商品
     */
    private Integer commodityType;
    /**
     * 父级skuid（例如：置换商品）
     */
    private Long parentSkuId;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 供应商会员id.
     */
    private Long upperMemberId;
    /**
     * 供应商角色id
     */
    private Long upperRoleId;

    /**
     * 原价/策略价
     */
    private BigDecimal commodityPrice;
    /**
     * 叠加权益参数后的价格={ 单价(特价/直降/折扣||策略价 ||原价)*权益参数}
     */
    private BigDecimal basePrice;
    /**
     * 到手价（套餐主商品到手价）
     */
    private BigDecimal handPrice;
    /**
     * 套餐到手价
     */
    private BigDecimal groupHandPrice;
    /**
     * 允许参与的活动
     */
    private List<ActivityGoodsBO> enableActivityList;
    /**
     * 筛选后参与的活动
     */
    private List<SkuJoinActivityResp> activityList;

    /**
     * 商品是否设置了阶梯价
     */
    private boolean flag=true;
    /**
     * 赠品列表
     */
    private List<CartGiveResp> giveList;

    /**
     * 分组编号(套餐商品必填)
     */
    private Integer groupNo;
    /**
     * 套餐列表
     */
    private List<SubsidiaryDTO> setMealList;
    /**
     * 权益参数(会员折扣)
     */
    private BigDecimal parameter;
    /**
     * sku总优惠金额
     */
    private BigDecimal saleTotalAmount;

    /**
     * 是否允许使用会员折扣价购买
     */
    private Boolean isMemberPrice;

    /**
     * 购物车已添加数量
     */
    private BigDecimal cartQuantity;
    /**
     * 最大允许购买数量
     */
    private BigDecimal enableQuantity;
    /**
     * 不参与活动的类型
     */
    private List<CartNotJoinActivityDTO> notJoinList;
    /**
     * 是否参与拼团.
     */
    private Boolean joinGroup;
    /**
     * 进货单id[前端透传用]
     */
    private Long purchaseId;
}
