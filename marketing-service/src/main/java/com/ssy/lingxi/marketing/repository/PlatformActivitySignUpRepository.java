package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.PlatformActivitySignUpDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 平台活动-会员报名
 * <AUTHOR>
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface PlatformActivitySignUpRepository extends JpaRepository<PlatformActivitySignUpDO, Long>, JpaSpecificationExecutor<PlatformActivitySignUpDO> {

    /**
     * 根据活动id，查询会员报名记录
     * @param activityId
     */
    List<PlatformActivitySignUpDO> findByPlatformActivityId(Long activityId);

    /**
     * 根据活动id，查询会员报名记录
     * @param activityId
     */
    PlatformActivitySignUpDO findByPlatformActivityIdAndMemberIdAndRoleId(Long activityId, Long memberId, Long roleId);

    /**
     * 根据id，会员，角色查询报名记录
     * @param id
     * @param memberId
     * @param roleId
     * @return
     */
    PlatformActivitySignUpDO findByIdAndMemberIdAndRoleId(Long id, Long memberId, Long roleId);

    /**
     * 根据id，会员，角色查询报名记录
     * @param ids
     * @param memberId
     * @param roleId
     * @return
     */
    List<PlatformActivitySignUpDO> findByIdInAndMemberIdAndRoleId(List<Long> ids, Long memberId, Long roleId);
    /**
     * 根据活动ids，会员，角色查询报名记录
     * @param ids
     * @param memberId
     * @param roleId
     * @return
     */
    List<PlatformActivitySignUpDO> findByPlatformActivityIdInAndMemberIdAndRoleId(List<Long> ids, Long memberId, Long roleId);
}
