package com.ssy.lingxi.marketing.model.vo.coupon.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 平台优惠券执行明细请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlatformCouponDetailPageDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = 3491108258415274023L;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 券码
     */
    private String code;

    /**
     * 券状态
     */
    private Integer status;

    /**
     * 领取起始时间
     */
    private Long createTimeStart;

    /**
     * 领取结束时间
     */
    private Long createTimeEnd;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 优惠券类型
     */
    private Integer suitableMemberType;

    /**
     * 下单(使用)开始时间
     */
    private Long useTimeStart;

    /**
     * 下单(使用)结束时间
     */
    private Long useTimeEnd;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 商城id
     */
    private Long shopId;
}
