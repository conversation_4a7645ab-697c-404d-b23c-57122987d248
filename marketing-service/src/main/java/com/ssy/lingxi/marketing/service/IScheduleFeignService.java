package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.marketing.entity.activity.MerchantActivityDO;
import com.ssy.lingxi.marketing.entity.activity.PlatformActivityDO;
import com.ssy.lingxi.marketing.entity.coupon.MerchantCouponDO;
import com.ssy.lingxi.marketing.entity.coupon.PlatformCouponDO;
import com.ssy.lingxi.scheduler.api.model.req.PermanentTaskReq;
import com.ssy.lingxi.scheduler.api.model.req.ScheduleTaskQueryReq;
import com.ssy.lingxi.scheduler.api.model.req.ScheduleTaskReq;
import com.ssy.lingxi.scheduler.api.model.req.ScheduleTaskUpdateExecTimeReq;
import com.ssy.lingxi.scheduler.api.model.resp.ScheduleTaskDefinitionResp;

import java.util.List;

/**
 *  调用外部服务的接口
 * <AUTHOR>
 * @since 2021/7/23
 * @version 2.0.0
 */
public interface IScheduleFeignService {

    /**
     * 查询定时任务
     * @param queryVO 接口参数
     * @return 返回结果
     */
    WrapperResp<List<ScheduleTaskDefinitionResp>> listScheduleTask(ScheduleTaskQueryReq queryVO);

    /**
     * 修改定时任务
     * @param updateVO 接口参数
     * @return 返回结果
     */
    WrapperResp<Void> updateScheduleTaskExecTime(ScheduleTaskUpdateExecTimeReq updateVO);

    /**
     * 添加定时任务
     * @param scheduleTaskReq 接口参数
     * @return 返回结果
     */
    WrapperResp<Long> addSchedulerTask(ScheduleTaskReq scheduleTaskReq);

    /**
     * 创建永久任务
     * @param scheduleTaskVO 接口参数
     * @return 定时任务的Id
     */
    WrapperResp<Long> createPermanentTask(PermanentTaskReq scheduleTaskVO);

    /**
     * 创建平台活动报名开始任务
     * @param loginUser 登录用户信息
     * @param platformActivityDO 平台活动
     */
    void createSignUpStartTask(UserLoginCacheDTO loginUser, PlatformActivityDO platformActivityDO);

    /**
     * 创建平台活动报名结束任务
     * @param loginUser 登录用户信息
     * @param platformActivityDO 平台活动
     */
    void createSignUpEndTask(UserLoginCacheDTO loginUser, PlatformActivityDO platformActivityDO);

    /**
     * 创建平台活动结束任务
     * @param loginUser 登录用户信息
     * @param platformActivityDO 平台活动
     */
    void createPfActivityEndTask(UserLoginCacheDTO loginUser, PlatformActivityDO platformActivityDO);

    /**
     * 创建商家平台结束任务
     * @param loginUser 登录用户信息
     * @param merchantActivityDO 商家活动
     */
    void createMcActivityEndTask(UserLoginCacheDTO loginUser, MerchantActivityDO merchantActivityDO);

    /**
     * 创建平台优惠券发放开始任务
     * @param loginUser 登录用户信息
     * @param platformCouponDO 商家优惠券
     */
    void createPfCouponReleaseStartTask(UserLoginCacheDTO loginUser, PlatformCouponDO platformCouponDO);

    /**
     * 创建平台优惠券发放开始任务
     * @param loginUser 登录用户信息
     * @param platformCouponDO 商家优惠券
     */
    void createPfCouponReleaseEndTask(UserLoginCacheDTO loginUser, PlatformCouponDO platformCouponDO);

    /**
     * 创建商家优惠券发放开始任务
     * @param loginUser 登录用户信息
     * @param merchantCouponDO 商家优惠券
     */
    void createMcCouponReleaseStartTask(UserLoginCacheDTO loginUser, MerchantCouponDO merchantCouponDO);

    /**
     * 创建商家优惠券发放开始任务
     * @param loginUser 登录用户信息
     * @param merchantCouponDO 商家优惠券
     */
    void createMcCouponReleaseEndTask(UserLoginCacheDTO loginUser, MerchantCouponDO merchantCouponDO);
}
