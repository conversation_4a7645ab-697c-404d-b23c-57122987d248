package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  赠品活动
 * <AUTHOR>
 * @since 2021/10/19
 * @version 2.0.0
 */
@Getter
@Setter
public class GiveActivityResp implements Serializable {
    private static final long serialVersionUID = -3502710567311167157L;
    /**
     * 赠品类型：1-赠商品 2-赠优惠卷
     * */
    private Integer giftType;

    /**
     * 优惠阶梯
     */
    private Integer groupNo;

    /**
     * 赠品名称
     */
    private String name;
    /**
     * 赠品id(skuid或优惠券id)
     */
    private Long id;

    /**
     * 赠品数量
     */
    private BigDecimal limitValue;
}
