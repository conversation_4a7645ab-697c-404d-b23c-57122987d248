package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 附属商品[赠商品或优惠券]dto
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/16
 */
@Getter
@Setter
public class ActivityBaseInfoDTO implements Serializable {

    private static final long serialVersionUID = -5446170282398364383L;

    /**
     * 成团人数
     */
    private Integer assembleNum;

    /**
     * 成团时效(单位小时)
     */
    private Integer validTime;
}
