package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 活动页 - QO
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/08/06
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ActivityPageDataReq extends PageDataReq {
    private static final long serialVersionUID = 9090064708039097952L;

    /**
     * 类型: 1.平台 2.商家
     */
    private Integer type;

    /**
     * 活动页名称
     */
    private String name;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 适用环境: 1.WEB 2.H5 3.小程序 4.APP
     */
    private Integer environment;

    /**
     * 状态: 1.待上线 2.已上线 3.进行中 4.已下线 5.已结束
     */
    private Integer status;

    /**
     * 商城ID
     */
    private Long shopId;
}
