package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 *  分页查询-平台活动报名查询-接口参数VO
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@Getter
@Setter
public class PfActivitySignUpAddReq implements Serializable {

    private static final long serialVersionUID = -2201018151170677145L;

    /**
     * 活动Id
     */
    @NotNull(message = "活动Id不能为空")
    @Range(min = 1, message = "活动Id必须大于0")
    private Long activityId;

    /**
     * 活动商品
     */
    @Size(min = 1, message = "请选择活动商品")
    @Valid
    private List<ActivityGoodsReq> productList;
}
