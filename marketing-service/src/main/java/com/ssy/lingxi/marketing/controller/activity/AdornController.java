package com.ssy.lingxi.marketing.controller.activity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.activity.request.ActivityGoodsAdornReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.GoodsListAdornDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.McActivityListAdornDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.request.PfActivityListAdornDataReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.ActivityGoodsAdornResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.GoodsListAdornResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.McActivityListAdornResp;
import com.ssy.lingxi.marketing.model.vo.activity.response.PfActivityListAdornResp;
import com.ssy.lingxi.marketing.service.IAdornService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 装修
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/24
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/adorn")
public class AdornController extends BaseController {

    @Resource
    private IAdornService adornService;

    /**
     * 平台营销活动列表（装修）
     **/
    @GetMapping("/platformActivityListAdorn")
    public WrapperResp<PageDataResp<PfActivityListAdornResp>> platformActivityListAdorn(@Valid PfActivityListAdornDataReq req) {
        return WrapperUtil.success(adornService.platformActivityListAdorn(getPlatformUser(), req));
    }

    /**
     * 商家营销活动列表（装修）
     **/
    @GetMapping("/merchantActivityListAdorn")
    public WrapperResp<PageDataResp<McActivityListAdornResp>> merchantActivityListAdorn(@Valid McActivityListAdornDataReq req) {
        return WrapperUtil.success(adornService.merchantActivityListAdorn(getSysUser(), req));
    }

    /**
     * 回显活动商品（装修）
     **/
    @GetMapping("/activityGoodsAdorn")
    public WrapperResp<List<ActivityGoodsAdornResp>> activityGoodsAdorn(@Valid ActivityGoodsAdornReq req) {
        return WrapperUtil.success(adornService.activityGoodsAdorn(req));
    }

    /**
     * 回显商品列表（装修）
     **/
    @GetMapping("/goodsListAdorn")
    public WrapperResp<PageDataResp<GoodsListAdornResp>> goodsListAdorn(@Valid GoodsListAdornDataReq req) {
        return WrapperUtil.success(adornService.goodsListAdorn(req));
    }
}
