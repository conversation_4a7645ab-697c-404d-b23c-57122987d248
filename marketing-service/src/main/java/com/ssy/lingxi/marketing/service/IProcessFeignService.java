package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.marketing.entity.activity.PlatformActivityDO;
import com.ssy.lingxi.marketing.model.vo.common.response.TaskStepResp;
import com.ssy.lingxi.workflow.api.model.req.*;
import com.ssy.lingxi.workflow.api.model.resp.ComplexTaskCompleteResp;
import com.ssy.lingxi.workflow.api.model.resp.ComplexTaskDefResp;
import com.ssy.lingxi.workflow.api.model.resp.SimpleProcessDefResp;
import com.ssy.lingxi.workflow.api.model.resp.SimpleTaskCompleteResp;

import java.util.List;

/**
 *  新工作流feign
 * <AUTHOR>
 * @since 2021/5/21
 * @version 2.0.0
 */
public interface IProcessFeignService {

    /**
     * （各种流程通用）启动流程
     * @param startVO 接口参数
     * @return 启动结果
     */
    WrapperResp<ComplexTaskCompleteResp> startProcess(TaskStartReq startVO);

    /**
     * （各种流程通用）启动流程后，执行第一个Task
     * @param startVO 接口参数
     * @return 执行结果
     */
    WrapperResp<ComplexTaskCompleteResp> startProcessThenCompleteFirstTask(TaskStartReq startVO);

    /**
     * （各种流程通用）执行流程步骤
     * @param executeVO 接口参数
     * @return 执行结果
     */
    WrapperResp<ComplexTaskCompleteResp> completeTask(TaskExecuteReq executeVO);

    /**
     * 启动单一的外部流程或内部流程
     * @param startVO 接口参数
     * @return 启动结果
     */
    WrapperResp<SimpleTaskCompleteResp> startSimpleProcess(TaskStartReq startVO);

    /**
     * 启动单一的外部流程或内部流程，并执行第一个步骤
     * @param startVO 接口参数
     * @return 执行结果
     */
    WrapperResp<SimpleTaskCompleteResp> startSimpleProcessThenCompleteFirstTask(TaskStartReq startVO);

    /**
     * 执行单一的外部流程、或内部流程
     * @param executeVO 接口参数
     * @return 执行结果
     */
    WrapperResp<SimpleTaskCompleteResp> completeSimpleTask(TaskExecuteReq executeVO);

    /**
     * 查询流程参数(dataId)列表
     * @param queryVO 接口参数
     * @return 参数列表
     */
    WrapperResp<List<Long>> findProcessDataIds(ProcessDataQueryReq queryVO);

    /**
     * 查询复杂流程的任务定义
     * @param queryVO 接口参数
     * @return 查询结果
     */
    WrapperResp<ComplexTaskDefResp> findComplexTaskDefinitions(ComplexProcessQueryReq queryVO);

    /**
     * 查询单一的外部流程的定义
     * @param queryVO 接口参数
     * @return 查询结果
     */
    WrapperResp<SimpleProcessDefResp> findSimpleExternalTaskDefinitions(ExternalProcessQueryReq queryVO);

    /**
     * 查询单一的内部流程的定义
     * @param queryVO 接口参数
     * @return 查询结果
     */
    WrapperResp<SimpleProcessDefResp> findSimpleInternalTaskDefinitions(InternalProcessQueryReq queryVO);

    /**
     * 查询任务步骤（内部）
     * <AUTHOR>
     * @since 2021/5/21
     * @param processKey: 任务枚举值
     * @param taskId:任务id
     * @param memberId:会员id
     * @return 任务步骤
     **/
    List<TaskStepResp> listTaskStep(String processKey, String taskId, Long memberId);
    /**
     * 查询任务步骤（外部）
     * <AUTHOR>
     * @since 2021/5/21
     * @param processKey: 任务枚举值
     * @param taskId:任务id
     * @param subRoleId: 下级会员id（采购商）
     * @param roleId: 上级会员id（供应商）
     * @return 任务步骤
     **/
    List<TaskStepResp> listTaskStepOut(String processKey, String taskId, Long subRoleId, Long roleId);

    /**
     * 启动单一的外部流程或内部流程
     * @param memberId 会员Id
     * @param roleId 角色Id
     * @param processKey 流程Key
     * @param dataId 数据Id
     * @return 流程信息
     */
    SimpleTaskCompleteResp startSimpleProcess(Long memberId, Long roleId, String processKey, Long dataId);

    /**
     * [平台活动外部流程]执行单一的外部流程、或内部流程
     * @param entity: 平台活动
     * @param agree: 审批状态 -1：无条件进行下一步任务； 1：同意； 0：拒绝
     * @return 执行结果
     */
    PlatformActivityDO completeSimpleTaskByPlatformActivityOuter(PlatformActivityDO entity, Integer agree);

    /**
     * [平台活动内部流程]执行单一的外部流程、或内部流程
     * @param entity: 平台活动
     * @param agree: 审批状态 -1：无条件进行下一步任务； 1：同意； 0：拒绝
     * @return 执行结果
     */
    PlatformActivityDO completeSimpleTaskByPlatformActivityInner(PlatformActivityDO entity, Integer agree);
}
