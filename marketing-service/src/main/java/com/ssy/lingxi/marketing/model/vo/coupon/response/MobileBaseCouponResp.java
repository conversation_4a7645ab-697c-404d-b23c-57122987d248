package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MobileBaseCouponResp implements Serializable {

    private static final long serialVersionUID = -838470145865496764L;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 所属类型 1-平台 2-商家
     */
    private Integer belongType;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券类型
     * 如果所属类型为平台则有 1-0元抵扣券 2-平台通用优惠券
     * 如果所属类型为商家则有 1-0元抵扣券 2-商家通用优惠券 3-品类优惠券 4-品牌优惠券 5-商品优惠券
     */
    private Integer type;

    /**
     * 优惠券类型名称
     */
    private String typeName;

    /**
     * 券面额
     */
    private BigDecimal denomination;

    /**
     * 使用条件, 满多少金额可用
     */
    private BigDecimal useConditionMoney;

    /**
     * 有效类型 1-固定有效时间 2-自领取开始时间
     */
    private Integer effectiveType;

    /**
     * 有效类型名称
     */
    private String effectiveTypeName;

    /**
     * 固定有效时间, 券有效起始时间
     */
    private Long effectiveTimeStart;

    /**
     * 固定有效时间, 券有效结束时间
     */
    private Long effectiveTimeEnd;

    /**
     * 自领取开始时间, 券多少天失效
     */
    private Integer invalidDay;
}
