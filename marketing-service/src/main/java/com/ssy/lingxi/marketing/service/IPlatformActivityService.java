package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.entity.activity.PlatformActivityDO;
import com.ssy.lingxi.marketing.model.vo.activity.request.*;
import com.ssy.lingxi.marketing.model.vo.activity.response.*;

import java.util.List;

/**
 * 平台活动服务类
 * <AUTHOR> yzc
 * @version 2.0.0
 * @since 2021/8/23
 */
public interface IPlatformActivityService {

    /**
     * 平台营销活动查询 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PlatformActivitySummaryPageResp> pageSummary(UserLoginCacheDTO loginUser, PlatformActivityPageDataReq pageVO);

    /**
     * 待新增平台营销活动 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PlatformActivityAddPageResp> pageByToBeAdd(UserLoginCacheDTO loginUser, PlatformActivityPageDataReq pageVO);

    /**
     * 待审核报名 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PlatformActivityTobeSignUpPageResp> pageByToBeSignUp(UserLoginCacheDTO loginUser, PlatformActivityTobeSignUpDataReq pageVO);

    /**
     * 待提交审核平台营销活动 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PlatformActivitySubmitPageResp> pageByToBeSubmit(UserLoginCacheDTO loginUser, PlatformActivityToBeSubmitPageDataReq pageVO);

    /**
     * 待审核平台营销活动(一级) - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PlatformActivityExamResp> pageByExamineStep1(UserLoginCacheDTO loginUser, PlatformActivityStep1PageDataReq pageVO);

    /**
     * 待审核平台营销活动(二级) - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PlatformActivityExamResp> pageByExamineStep2(UserLoginCacheDTO loginUser, PlatformActivityStep1PageDataReq pageVO);

    /**
     * 待上线平台营销活动 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PlatformActivityOnlineResp> pageByToBeOnline(UserLoginCacheDTO loginUser, PlatformActivityStep1PageDataReq pageVO);

    /**
     * 平台营销活动查询 - 活动详情
     * @param loginUser 登录用户信息
     * @param request 接口参数
     * @return 返回结果
     */
    PlatformActivityDetailResp detail(UserLoginCacheDTO loginUser, CommonIdReq request);

    /**
     * 平台营销活动查询 - 活动基本详情(不查询配套信息)
     * @param loginUser
     * @param request
     * @return
     */
    PlatformActivityDetailResp getBasedetail(UserLoginCacheDTO loginUser, CommonIdReq request);

    /**
     * 待审核报名 - 活动详情
     * @param loginUser 登录用户信息
     * @param request 接口参数
     * @return 返回结果
     */
    PlatformActivitySignUpDetailResp detailSignUp(UserLoginCacheDTO loginUser, PlatformActivitySignUpDetailReq request);

    /**
     * 平台营销活动查询 - 活动详情 - 活动商品(分页)
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PfActivitySignUpGoodsPageResp> pageByActivityGoods(UserLoginCacheDTO loginUser, PfActivityGoodsPageDataReq pageVO);

    /**
     * 待审核报名- 活动详情 - 活动商品(分页)
     * @param loginUser 登录用户信息
     * @param pageVO 接口参数
     * @return 返回结果
     */
    PageDataResp<PfActivitySignUpGoodsPageResp> pageByActivityGoodsSignUp(UserLoginCacheDTO loginUser, PfActivitySignUpGoodsPageDataReq pageVO);

    /**
     * 待新增平台营销活动 - 新增
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void savePlatformActivity(UserLoginCacheDTO loginUser, PlatformActivityAddReq request);

    /**
     * 待新增平台营销活动 - 修改
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void updatePlatformActivity(UserLoginCacheDTO loginUser, PlatformActivityUpdateReq request);

    /**
     * 待新增平台营销活动 - 批量删除
     * @param loginUser 登录用户信息
     * @param idList 接口参数
     */
    Void batchDelete(UserLoginCacheDTO loginUser, List<Long> idList);

    /**
     * 平台营销活动查询 - 取消
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void cancel(UserLoginCacheDTO loginUser, ActivityActionReq request);

    /**
     * 平台营销活动查询 - 终止
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void stop(UserLoginCacheDTO loginUser, ActivityActionReq request);

    /**
     * 平台营销活动查询 - 重新启动
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void restart(UserLoginCacheDTO loginUser, ActivityActionReq request);

    /**
     * 平台营销活动查询 - 修改时间
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void updateTime(UserLoginCacheDTO loginUser, PlatformActivityUpdateTimeReq request);

    /**
     * 待新增平台营销活动 - 提交
     * @param loginUser 登录用户信息
     * @param req 接口参数
     */
    Void submit(UserLoginCacheDTO loginUser, ActivitySubmitReq req);

    /**
     * 待新增平台营销活动 - 批量提交
     * @param loginUser 登录用户信息
     * @param req 接口参数
     */
    Void batchSubmit(UserLoginCacheDTO loginUser, CommonIdListReq req);

    /**
     * 待审核报名 - 审核报名
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void examineSignUpUpdate(UserLoginCacheDTO loginUser, PlatformActivityExamineSignUpReq request);

    /**
     * 待提交审核平台营销活动 - 提交审核
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void submitExamine(UserLoginCacheDTO loginUser, ActivitySubmitReq request);

    /**
     * 待审核平台营销活动(一级) - 审核
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void examineStep1Update(UserLoginCacheDTO loginUser, ActivityExamineReq request);

    /**
     * 待审核平台营销活动(二级) - 审核
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void examineStep2Update(UserLoginCacheDTO loginUser, ActivityExamineReq request);

    /**
     * 待上线平台营销活动 - 上线活动
     * @param loginUser 登录用户信息
     * @param request 接口参数
     */
    Void onlineUpdate(UserLoginCacheDTO loginUser, ActivitySubmitReq request);

    /**
     * 遍历平台活动的报名资料, 如果平台都处理完则执行下一个流程
     * @param loginUser 登录用户信息
     * @param platformActivityDO 平台活动
     */
    Void updateByAllSignUp(UserLoginCacheDTO loginUser, PlatformActivityDO platformActivityDO);


}
