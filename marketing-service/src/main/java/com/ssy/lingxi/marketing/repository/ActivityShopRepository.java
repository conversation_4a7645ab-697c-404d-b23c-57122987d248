package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.ActivityShopDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 活动 - 适用商城
 * <AUTHOR>
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface ActivityShopRepository extends JpaRepository<ActivityShopDO, Long>, JpaSpecificationExecutor<ActivityShopDO> {

    /**
     * 根据活动删除
     * @param belongType 所属类型
     * @param id 活动id
     */
    void deleteByBelongTypeAndActivityId(Integer belongType, Long id);

    /**
     * 根据活动删除
     * @param belongType 所属类型
     * @param ids 活动id
     */
    void deleteByBelongTypeAndActivityIdIn(Integer belongType, List<Long> ids);

    /**
     * 根据活动查询
     * @param belongType 所属类型
     * @param id 活动id
     */
    List<ActivityShopDO> findByBelongTypeAndActivityId(Integer belongType, Long id);

    /**
     * 根据商城查询
     * @param belongType 所属类型
     * @param id 商城id
     */
    List<ActivityShopDO> findByBelongTypeAndShopId(Integer belongType, Long id);
}
