package com.ssy.lingxi.marketing.domain;

import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.marketing.model.bo.ReceiveCouponBelongTypeBO;
import com.ssy.lingxi.marketing.model.vo.coupon.request.BelongTypeCouponReq;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 优惠券相关逻辑
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class CouponDM {

    /**
     * 构建对象
     */
    public static ReceiveCouponBelongTypeBO buildReceiveCouponBelongTypeBO(Long shopId, List<Long> platformCouponIdList, List<Long> merchantCouponIdList) {
        List<BelongTypeCouponReq> belongTypeCouponReqList = CollUtil.unionAll(
                platformCouponIdList.stream().map(
                        couponId -> new BelongTypeCouponReq(BelongTypeEnum.PLATFORM.getCode(), couponId)
                ).collect(Collectors.toList()),
                merchantCouponIdList.stream().map(
                        couponId -> new BelongTypeCouponReq(BelongTypeEnum.MERCHANT.getCode(), couponId)
                ).collect(Collectors.toList())
        );
        return new ReceiveCouponBelongTypeBO(shopId, belongTypeCouponReqList);
    }

}
