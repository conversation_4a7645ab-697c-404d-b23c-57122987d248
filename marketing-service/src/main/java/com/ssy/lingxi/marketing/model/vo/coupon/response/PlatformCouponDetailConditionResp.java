package com.ssy.lingxi.marketing.model.vo.coupon.response;

import com.ssy.lingxi.common.model.resp.select.SelectItemResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 平台优惠券执行明细条件返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlatformCouponDetailConditionResp implements Serializable {

    private static final long serialVersionUID = 3982933465329267097L;

    /**
     * 券状态
     */
    List<SelectItemResp> statusList;

    /**
     * 适用会员
     */
    List<SelectItemResp> suitableMemberTypeList;

    /**
     * 商城
     */
    List<SelectItemResp> shopList;
}
