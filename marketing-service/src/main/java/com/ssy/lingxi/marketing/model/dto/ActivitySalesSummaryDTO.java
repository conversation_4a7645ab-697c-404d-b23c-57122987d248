package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 附属商品[赠商品或优惠券]dto
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/16
 */
@Getter
@Setter
public class ActivitySalesSummaryDTO implements Serializable {


    private static final long serialVersionUID = -7827085835226148628L;
    /**
     * 参与客户数
     */
    private Integer customerCount;
    /**
     * 已执行订单单数
     */
    private Integer orderCount;
    /**
     * 已执行订单金额
     */
    private BigDecimal orderAmount;
}
