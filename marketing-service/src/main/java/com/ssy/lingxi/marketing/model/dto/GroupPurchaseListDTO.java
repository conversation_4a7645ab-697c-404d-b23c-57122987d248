package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *  拼团列表
 * <AUTHOR>
 * @since 2021/10/14
 * @version 2.0.0
 */
@Getter
@Setter
public class GroupPurchaseListDTO implements Serializable {

    private static final long serialVersionUID = -843932158137182212L;
    /**
     * 拼团id
     */
    private Long id;
    /**
     * 采购会员名称
     */
    private String memberName;
    /**
     * 采购会员头像
     */
    private String logo;
    /**
     * 成团人数
     */
    private Integer assembleNum;
    /**
     * 参团人数
     */
    private Integer num;
    /**
     * 剩余秒数
     */
    private Long endTime;

    /**
     * 采购会员id
     */
    private Long memberId;
    /**
     * 采购角色id
     */
    private Long roleId;
    /**
     * 成团时效(单位小时)
     */
    private Integer validTime;
    /**
     * 订单id.
     */
    private Long orderId;
}
