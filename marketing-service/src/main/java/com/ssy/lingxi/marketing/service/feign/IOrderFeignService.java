package com.ssy.lingxi.marketing.service.feign;

import com.ssy.lingxi.order.api.model.resp.OrderAfterSaleStatusResp;
import com.ssy.lingxi.order.api.model.resp.OrderMarketingDetailResp;

import java.util.List;
import java.util.Map;

/**
 *  访问订单内部接口
 * <AUTHOR>
 * @since 2021/12/15
 * @version 2.0.0
 */
public interface IOrderFeignService {

    /**
     * 根据订单编号list 查询订单信息
     * @param orderNoList 接口参数
     * @return 查询结果
     **/
    Map<String, OrderAfterSaleStatusResp> getOrderInfoMap(List<String> orderNoList);

    /**
     * 根据订单编号、skuId list 查询订单信息[活动执行明细用]
     * @param orderIds
     * @param skuIds
     * @return
     */
    Map<String, OrderMarketingDetailResp> getOrderInfoMapByExecute(List<Long> orderIds, List<Long> skuIds);

   }
