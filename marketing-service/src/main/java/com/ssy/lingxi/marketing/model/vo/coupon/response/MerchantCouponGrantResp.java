package com.ssy.lingxi.marketing.model.vo.coupon.response;

import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 商家优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantCouponGrantResp extends MerchantCouponBaseResp {

    private static final long serialVersionUID = -7052115332063528098L;

    /**
     * 适用会员等级(会员等级id)
     * 当适用用户(会员) 类型有勾选新会员、老会员选型,该字段作用于这些数据
     */
    private List<Long> suitableMemberLevelTypes;

    /**
     * 选择的会员
     */
    private List<MemberAndRoleIdDTO> memberList;
}
