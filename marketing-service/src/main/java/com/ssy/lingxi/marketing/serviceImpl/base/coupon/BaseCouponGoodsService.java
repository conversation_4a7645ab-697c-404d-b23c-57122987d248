package com.ssy.lingxi.marketing.serviceImpl.base.coupon;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.marketing.entity.coupon.CouponBrandDO;
import com.ssy.lingxi.marketing.entity.coupon.CouponCategoryDO;
import com.ssy.lingxi.marketing.entity.coupon.CouponGoodsDO;
import com.ssy.lingxi.marketing.model.bo.SuitableCategoryBO;
import com.ssy.lingxi.marketing.model.vo.coupon.response.SuitableBrandResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.SuitableCategoryResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.SuitableCommoditySkuResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.SuitablePlatformCommoditySkuResp;
import com.ssy.lingxi.marketing.repository.CouponBrandRepository;
import com.ssy.lingxi.marketing.repository.CouponCategoryRepository;
import com.ssy.lingxi.marketing.repository.CouponGoodsRepository;
import com.ssy.lingxi.marketing.service.feign.IProductFeignService;
import com.ssy.lingxi.product.api.model.resp.BrandResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CustomerCategoryResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础优惠券商品服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/16
 */
@Slf4j
@Service
public class BaseCouponGoodsService {

    @Resource
    private CouponGoodsRepository goodsRepository;
    @Resource
    private CouponCategoryRepository categoryRepository;
    @Resource
    private CouponBrandRepository brandRepository;
    @Resource
    private IProductFeignService productFeignService;

    // ============================商家============================

    /**
     * 查询商家优惠券适用品类
     * @param id 优惠券id
     * @return 返回结果
     */
    public List<List<SuitableCategoryResp>> listMerchantCouponCategory(Long id) {
        List<CouponCategoryDO> categoryDOList = categoryRepository.findAllByCouponId(id);
        return categoryDOList.stream().map(e ->
                e.getCategoryList().stream().map(category -> {
                    SuitableCategoryResp suitableCategoryResp = new SuitableCategoryResp();
                    suitableCategoryResp.setId(category.getId());
                    suitableCategoryResp.setName(category.getName());
                    suitableCategoryResp.setImageUrl(category.getImageUrl());
                    return suitableCategoryResp;
                }).collect(Collectors.toList())
        ).collect(Collectors.toList());
    }

    /**
     * 查询商家优惠券适用品牌
     * @param id 优惠券id
     * @return 返回结果
     */
    public List<SuitableBrandResp> listMerchantCouponBrand(Long id) {
        List<CouponBrandDO> brandDOList = brandRepository.findAllByCouponId(id);
        return brandDOList.stream().map(e -> {
            SuitableBrandResp suitableBrandResp = new SuitableBrandResp();
            suitableBrandResp.setId(e.getBrandId());
            suitableBrandResp.setName(e.getBrandName());
            suitableBrandResp.setLogoUrl(e.getLogo());
            return suitableBrandResp;
        }).collect(Collectors.toList());
    }

    /**
     * 查询商家优惠券适用商品
     * @param id 优惠券id
     * @return 返回结果
     */
    public List<SuitableCommoditySkuResp> listMerchantCouponGoods(Long id) {
        List<CouponGoodsDO> goodsDOList = goodsRepository.findAllByBelongTypeAndCouponId(BelongTypeEnum.MERCHANT.getCode(), id);
        return goodsDOList.stream().map(e -> {
            SuitableCommoditySkuResp suitableCommoditySkuResp = new SuitableCommoditySkuResp();
            suitableCommoditySkuResp.setId(e.getSkuId());
            suitableCommoditySkuResp.setCommodityId(e.getProductId());
            suitableCommoditySkuResp.setName(e.getProductName());
            suitableCommoditySkuResp.setMainPic(e.getProductImgUrl());
            suitableCommoditySkuResp.setCustomerCategoryName(e.getCategory());
            suitableCommoditySkuResp.setBrandName(e.getBrand());
            suitableCommoditySkuResp.setUnitName(e.getUnit());
            suitableCommoditySkuResp.setUnitPrice(e.getPrice());
            return suitableCommoditySkuResp;
        }).collect(Collectors.toList());
    }

    /**
     * 查询商家优惠券适用品类信息
     * @param suitableCategoryList 适用品类
     * @return 返回结果
     */
    public List<CustomerCategoryResp> queryMerchantCouponCategory(List<List<Long>> suitableCategoryList) {
        // 每个集合取最后一个品类id, 最后一级品类不能重复
        if (!suitableCategoryList.stream()
                .map(customerCategoryList -> customerCategoryList.stream().skip(customerCategoryList.size() - 1L).findFirst().orElse(null))
                .filter(Objects::nonNull)
                .allMatch(new HashSet<>()::add)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_CATEGROY_NO_REPEAT);
        }

        // 品类id集合(去重)
        List<Long> customerCategoryIdList = suitableCategoryList.stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        // 查询品类数据
        WrapperResp<List<CustomerCategoryResp>> customerCategoryWrapperResp = productFeignService.getCustomerCategoryById(customerCategoryIdList);
        if (ResponseCodeEnum.SUCCESS.getCode() != customerCategoryWrapperResp.getCode()) {
            throw new BusinessException(customerCategoryWrapperResp.getCode(), customerCategoryWrapperResp.getMessage());
        }

        return customerCategoryWrapperResp.getData();
    }

    /**
     * 查询商家优惠券适用品牌信息
     * @param suitableBrandList 适用品类
     * @return 返回结果
     */
    public List<BrandResp> queryMerchantCouponBrand(List<Long> suitableBrandList) {
        // 品牌不能重复
        if (!suitableBrandList.stream().allMatch(new HashSet<>()::add)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_BRAND_NO_REPEAT);
        }

        // 查询品牌数据
        WrapperResp<List<BrandResp>> brandWrapperResp = productFeignService.getBrandById(suitableBrandList);
        if (ResponseCodeEnum.SUCCESS.getCode() != brandWrapperResp.getCode()) {
            throw new BusinessException(brandWrapperResp.getCode(), brandWrapperResp.getMessage());
        }

        return brandWrapperResp.getData();
    }

    /**
     * 保存商家优惠券适用品类信息
     * @param suitableCategoryList 适用品类id
     * @param categoryResponseList 适用品类信息
     */
    @Transactional
    public void saveMerchantCouponCategory(Long couponId, List<List<Long>> suitableCategoryList, List<CustomerCategoryResp> categoryResponseList) {
        if (Objects.isNull(couponId)) {
            return;
        }
        if (CollectionUtils.isEmpty(suitableCategoryList)) {
            return;
        }

        // categoryId - customerCategoryResponse
        Map<Long, CustomerCategoryResp> customerCategoryMap = categoryResponseList.stream().collect(Collectors.toMap(CustomerCategoryResp::getId, e -> e, (e1, e2) -> e2));

        // 保存的品类优惠券
        List<CouponCategoryDO> categoryDOList = new ArrayList<>();
        for (List<Long> list : suitableCategoryList) {
            Long lastCategoryId = list.get(list.size() - 1);
            CouponCategoryDO couponCategoryDO = new CouponCategoryDO();
            couponCategoryDO.setCouponId(couponId);
            couponCategoryDO.setCategoryId(lastCategoryId);

            // 处理多级品类的数据
            List<SuitableCategoryBO> suitableCategoryBOList = new ArrayList<>();
            for (Long categoryId : list) {
                CustomerCategoryResp cateGoryResp = customerCategoryMap.get(categoryId);
                SuitableCategoryBO suitableCategoryBO = new SuitableCategoryBO();
                suitableCategoryBO.setId(cateGoryResp.getId());
                suitableCategoryBO.setName(cateGoryResp.getName());
                suitableCategoryBO.setImageUrl(cateGoryResp.getImageUrl());
                suitableCategoryBOList.add(suitableCategoryBO);
            }
            couponCategoryDO.setCategoryList(suitableCategoryBOList);
            categoryDOList.add(couponCategoryDO);
        }
        categoryRepository.saveAll(categoryDOList);
    }

    /**
     * 保存商家优惠券适用品类信息
     * @param suitableBrandList 适用品牌id
     * @param brandRespList 适用品牌信息
     */
    @Transactional
    public void saveMerchantCouponBrand(Long couponId, List<Long> suitableBrandList, List<BrandResp> brandRespList) {
        if (Objects.isNull(couponId)) {
            return;
        }
        if (CollectionUtils.isEmpty(suitableBrandList)) {
            return;
        }

        // brandId - brandResponse
        Map<Long, BrandResp> brandMap = brandRespList.stream().collect(Collectors.toMap(BrandResp::getId, e -> e, (e1, e2) -> e2));

        List<CouponBrandDO> couponBrandDOList = new ArrayList<>();
        for (Long brandId : suitableBrandList) {
            BrandResp brandResp = brandMap.get(brandId);
            CouponBrandDO couponBrandDO = new CouponBrandDO();
            couponBrandDO.setId(brandId);
            couponBrandDO.setCouponId(couponId);
            couponBrandDO.setBrandId(brandId);
            couponBrandDO.setBrandName(brandResp.getName());
            couponBrandDO.setLogo(brandResp.getLogoUrl());
            couponBrandDOList.add(couponBrandDO);
        }
        brandRepository.saveAll(couponBrandDOList);
    }

    /**
     * 保存商家优惠券适用商品
     * @param loginUser 登录用户信息
     * @param couponId 优惠券id
     * @param suitableCommoditySkuList 适用商品
     */
    @Transactional
    public void saveMerchantCouponGoods(UserLoginCacheDTO loginUser, Long couponId,
                                        List<SuitableCommoditySkuResp> suitableCommoditySkuList) {
        if (Objects.isNull(couponId)) {
            return;
        }
        if (CollectionUtils.isEmpty(suitableCommoditySkuList)) {
            return;
        }

        List<CouponGoodsDO> couponGoodsDOList = suitableCommoditySkuList.stream().map(vo -> {
            CouponGoodsDO couponGoodsDO = new CouponGoodsDO();
            couponGoodsDO.setCouponId(couponId);
            couponGoodsDO.setBelongType(BelongTypeEnum.MERCHANT.getCode());
            couponGoodsDO.setMemberId(loginUser.getMemberId());
            couponGoodsDO.setMemberName(loginUser.getMemberName());
            couponGoodsDO.setRoleId(loginUser.getMemberRoleId());
            couponGoodsDO.setRoleName(loginUser.getMemberRoleName());
            couponGoodsDO.setProductId(vo.getCommodityId());
            couponGoodsDO.setSkuId(vo.getId());
            couponGoodsDO.setProductName(vo.getName());
            couponGoodsDO.setType(null);
            couponGoodsDO.setCategory(vo.getCustomerCategoryName());
            couponGoodsDO.setBrand(vo.getBrandName());
            couponGoodsDO.setUnit(vo.getUnitName());
            couponGoodsDO.setPrice(vo.getUnitPrice());
            couponGoodsDO.setProductImgUrl(vo.getMainPic());
            return couponGoodsDO;
        }).collect(Collectors.toList());

        goodsRepository.saveAll(couponGoodsDOList);
    }

    /**
     * 查询是否存在商家适用品类, 存在则删除
     * @param couponId 优惠券id
     */
    public void existsAndDeleteMerchantCouponCategory(Long couponId) {
        if (categoryRepository.existsByCouponId(couponId)) {
            categoryRepository.deleteByCouponId(couponId);
        }
    }

    /**
     * 查询是否存在商家适用品牌, 存在则删除
     * @param couponId 优惠券id
     */
    public void existsAndDeleteMerchantCouponBrand(Long couponId) {
        if (brandRepository.existsByCouponId(couponId)) {
            brandRepository.deleteByCouponId(couponId);
        }
    }

    /**
     * 查询是否存在商家适用商品, 存在则删除
     * @param couponId 优惠券id
     */
    public void existsAndDeleteCouponGoods(BelongTypeEnum belongType, Long couponId) {
        if (goodsRepository.existsByBelongTypeAndCouponId(belongType.getCode(), couponId)) {
            goodsRepository.deleteByBelongTypeAndCouponId(belongType.getCode(), couponId);
        }
    }

    // ============================平台============================

    /**
     * 查询平台优惠券适用商品
     * @param id 优惠券id
     * @return 返回结果
     */
    public List<SuitablePlatformCommoditySkuResp> listPlatformCouponGoods(Long id) {
        List<CouponGoodsDO> goodsDOList = goodsRepository.findAllByBelongTypeAndCouponId(BelongTypeEnum.PLATFORM.getCode(), id);
        return goodsDOList.stream().map(e -> {
            SuitablePlatformCommoditySkuResp suitableCommoditySkuVO = new SuitablePlatformCommoditySkuResp();
            suitableCommoditySkuVO.setId(e.getSkuId());
            suitableCommoditySkuVO.setCommodityId(e.getProductId());
            suitableCommoditySkuVO.setName(e.getProductName());
            suitableCommoditySkuVO.setMainPic(e.getProductImgUrl());
            suitableCommoditySkuVO.setCustomerCategoryName(e.getCategory());
            suitableCommoditySkuVO.setBrandName(e.getBrand());
            suitableCommoditySkuVO.setUnitName(e.getUnit());
            suitableCommoditySkuVO.setUnitPrice(e.getPrice());
            suitableCommoditySkuVO.setMemberId(e.getMemberId());
            suitableCommoditySkuVO.setRoleId(e.getRoleId());
            suitableCommoditySkuVO.setMemberName(e.getMemberName());
            return suitableCommoditySkuVO;
        }).collect(Collectors.toList());
    }


    /**
     * 保存平台优惠券适用商品
     * @param loginUser 登录用户信息
     * @param couponId 优惠券id
     * @param suitableCommoditySkuList 适用商品
     */
    public void savePlatformCouponGoods(UserLoginCacheDTO loginUser, Long couponId,
                                        List<SuitablePlatformCommoditySkuResp> suitableCommoditySkuList) {
        List<CouponGoodsDO> couponGoodsDOList = suitableCommoditySkuList.stream().map(vo -> {
            CouponGoodsDO goodsDO = new CouponGoodsDO();
            goodsDO.setCouponId(couponId);
            goodsDO.setBelongType(BelongTypeEnum.PLATFORM.getCode());
            goodsDO.setMemberId(vo.getMemberId());
            goodsDO.setMemberName(vo.getMemberName());
            goodsDO.setRoleId(vo.getRoleId());
            goodsDO.setProductId(vo.getCommodityId());
            goodsDO.setSkuId(vo.getId());
            goodsDO.setProductName(vo.getName());
            goodsDO.setType(null);
            goodsDO.setCategory(vo.getCustomerCategoryName());
            goodsDO.setBrand(vo.getBrandName());
            goodsDO.setUnit(vo.getUnitName());
            goodsDO.setPrice(vo.getUnitPrice());
            goodsDO.setProductImgUrl(vo.getMainPic());
            return goodsDO;
        }).collect(Collectors.toList());

        goodsRepository.saveAll(couponGoodsDOList);
    }
}