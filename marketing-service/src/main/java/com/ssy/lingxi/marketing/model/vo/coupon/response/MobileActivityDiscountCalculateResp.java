package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 移动端活动优惠计算响应类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2024/12/10
 */
@Getter
@Setter
public class MobileActivityDiscountCalculateResp implements Serializable {

    private static final long serialVersionUID = -1284106139690376942L;

    /**
     * 按SKU维度的活动匹配结果
     */
    private List<SkuDiscountResult> skuResults;

    /**
     * 活动分组结果
     */
    private List<ActivityGroup> activityGroups;


    /**
     * SKU折扣结果
     */
    @Getter
    @Setter
    public static class SkuDiscountResult implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * SKU ID
         */
        private Long skuId;

        /**
         * 单件编码
         */
        private String singleCode;

        /**
         * 商品数量
         */
        private Integer quantity;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * 商品单价
         */
        private BigDecimal unitPrice;

        /**
         * 商品总价（单价 * 数量）
         */
        private BigDecimal totalAmount;

        /**
         * 是否命中活动
         */
        private Boolean hitActivity;

        /**
         * 命中的活动ID（如果有）
         */
        private Long activityId;

        /**
         * 命中的活动名称（如果有）
         */
        private String activityName;

        /**
         * 活动类型（满减、折扣、买赠等）
         */
        private String activityType;

        /**
         * 该SKU在活动中的优惠金额
         */
        private BigDecimal activityDiscountAmount;

        /**
         * 客户工费优惠金额
         */
        private BigDecimal memberDiscountAmount;

        /**
         * 活动优惠后总工费
         */
        private BigDecimal afterActivityDiscountAmount;


        /**
         * 工费配置优惠后的总工费
         */
        private BigDecimal afterMemberDiscountAmount;

        /**
         * 商品净重（克）
         */
        private BigDecimal netWeight;

    }

    /**
     * 活动分组
     */
    @Getter
    @Setter
    public static class ActivityGroup implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 活动ID
         */
        private Long activityId;

        /**
         * 活动名称
         */
        private String activityName;

        /**
         * 活动类型
         */
        private String activityType;

        /**
         * 活动描述
         */
        private String activityDesc;

        /**
         * 参与该活动的SKU列表
         */
        private List<Long> skuIds;

        /**
         * 该活动组的活动总优惠金额
         */
        private BigDecimal totalDiscountAmount;

        /**
         * 该活动组总工费优惠金额
         */
        private BigDecimal memberDiscountAmount;

        /**
         * 参与商品数量
         */
        private Integer goodsCount;

        /**
         * 是否满足活动条件
         */
        private Boolean meetCondition;

        /**
         * 活动条件描述
         */
        private String conditionDesc;
    }
}

   