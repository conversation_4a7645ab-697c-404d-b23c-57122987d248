package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 *  分页查询平台活动公共接口参数VO
 * <AUTHOR>
 * @since 2021/6/19
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformActivityCommonPageDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = 4458930300649394020L;
    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动开始时间
     */
    private String startTime;

    /**
     * 活动结束时间
     */
    private String endTime;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 外部状态
     */
    private Integer outerStatus;
    /**
     * 内部状态
     */
    private Integer innerStatus;
}
