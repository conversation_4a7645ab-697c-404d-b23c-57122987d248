package com.ssy.lingxi.marketing.model.vo.coupon.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.enums.marketing.MerchantCouponTypeEnum;
import com.ssy.lingxi.component.base.enums.marketing.PlatformCouponTypeEnum;
import com.ssy.lingxi.component.base.handler.converter.LongToDateFormatSerializer;
import com.ssy.lingxi.marketing.enums.*;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 券列表响应
 *
 * <AUTHOR>
 */
@Data
public class AdornCouponListResp {

    /**
     * 优惠券id
     */
    private Long id;

    /**
     * 所属方类型
     *
     * @see BelongTypeEnum
     */
    private Integer belongType;

    /**
     * 是否可领取
     *
     * @see CanReceiveEnum
     */
    private Integer canReceive;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 优惠券类型
     *
     * @see PlatformCouponTypeEnum 平台{@link BelongTypeEnum}{@link AdornCouponListResp#belongType}
     * @see MerchantCouponTypeEnum 商家{@link BelongTypeEnum}{@link AdornCouponListResp#belongType}
     */
    private Integer type;

    /**
     * 优惠券类型名称
     *
     * @see PlatformCouponTypeEnum 平台{@link BelongTypeEnum}{@link AdornCouponListResp#belongType}
     * @see MerchantCouponTypeEnum 商家{@link BelongTypeEnum}{@link AdornCouponListResp#belongType}
     */
    private String typeName;

    /**
     * 领(发)券起始时间
     */
    @JsonSerialize(using = LongToDateFormatSerializer.class)
    private Long releaseTimeStart;

    /**
     * 领(发)券结束时间
     */
    @JsonSerialize(using = LongToDateFormatSerializer.class)
    private Long releaseTimeEnd;

    /**
     * 券面额
     */
    private BigDecimal denomination;

    /**
     * 领取方式
     *
     * @see PlatformCouponGetWayEnum 平台{@link BelongTypeEnum}{@link AdornCouponListResp#belongType}
     * @see MerchantCouponGetWayEnum 商家{@link BelongTypeEnum}{@link AdornCouponListResp#belongType}
     */
    private Integer getWay;

    /**
     * 使用条件, 满多少金额可用
     */
    private BigDecimal useConditionMoney;

    /**
     * 有效类型
     *
     * @see PlatformCouponEffectiveTypeEnum 平台{@link BelongTypeEnum}{@link AdornCouponListResp#belongType}
     * @see MerchantCouponEffectiveTypeEnum 商家{@link BelongTypeEnum}{@link AdornCouponListResp#belongType}
     */
    private Integer effectiveType;

    /**
     * 固定有效时间, 券有效起始时间
     */
    @JsonSerialize(using = LongToDateFormatSerializer.class)
    private Long effectiveTimeStart;

    /**
     * 固定有效时间, 券有效结束时间
     */
    @JsonSerialize(using = LongToDateFormatSerializer.class)
    private Long effectiveTimeEnd;

    /**
     * 自领取开始时间, 券多少天失效
     */
    private Integer invalidDay;

    /**
     * 创建时间
     */
    @JsonSerialize(using = LongToDateFormatSerializer.class)
    private Long createTime;

}
