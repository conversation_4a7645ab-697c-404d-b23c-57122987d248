package com.ssy.lingxi.marketing.model.dto;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 活动 - 详情页- 外部流转记录 - VO
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityRecordPageDataReqDTO extends PageDataReq implements Serializable {

    private static final long serialVersionUID = 3994952365344439884L;
    /**
     * 数据id
     */
    private Long dataId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 排序.(默认操作时间正序)
     */
    private Boolean sort=Boolean.TRUE;
}
