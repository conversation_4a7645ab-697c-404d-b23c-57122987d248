package com.ssy.lingxi.marketing.handler.convert;

import com.ssy.lingxi.commodity.api.model.resp.shop.ShopDetailResp;
import com.ssy.lingxi.marketing.enums.CanReceiveEnum;
import com.ssy.lingxi.marketing.model.bo.CanReceiveCouponBO;
import com.ssy.lingxi.marketing.model.vo.coupon.response.AdornCouponListResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ShopThinResp;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Optional;

/**
 * 响应类实体映射
 *
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RespConvert {

    /**
     * 实例
     */
    RespConvert INSTANCE = Mappers.getMapper(RespConvert.class);

    /**
     * 类转换
     */
    @Mapping(target = "id", source = "couponId")
    @Mapping(target = "canReceive", expression = "java(getCanReceiveCode(canReceiveCouponBO.getCanReceive()))")
    AdornCouponListResp toAdornCouponListResp(CanReceiveCouponBO canReceiveCouponBO);

    /**
     * 类转换
     */
    List<AdornCouponListResp> toAdornCouponListRespListByPlatform(List<CanReceiveCouponBO> canReceiveCouponBOList);

    /**
     * 类转换
     */
    List<AdornCouponListResp> toAdornCouponListRespListByMerchant(List<CanReceiveCouponBO> canReceiveCouponBOList);

    /**
     * 部分更新
     */
    @Mapping(target = "shopName", source = "name")
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void partialUpdate(ShopDetailResp shopDetailResp, @MappingTarget ShopThinResp shopThinResp);

    /**
     * 获取优惠券领取状态
     */
    default Integer getCanReceiveCode(CanReceiveEnum canReceiveEnum) {
        return Optional.ofNullable(canReceiveEnum).map(CanReceiveEnum::getCode).orElse(null);
    }

}
