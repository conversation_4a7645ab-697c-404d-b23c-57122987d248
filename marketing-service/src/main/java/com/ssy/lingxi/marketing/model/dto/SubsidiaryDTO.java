package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  赠品活动
 * <AUTHOR>
 * @since 2021/10/19
 * @version 2.0.0
 */
@Getter
@Setter
public class SubsidiaryDTO implements Serializable {
    private static final long serialVersionUID = 6700251901415960109L;
    /**
     * 活动商品-商品附属表id(ActivityGoodsSubsidiaryDO.id)
     */
    private Long subGoodsId;
    /**
     * 分组编号/优惠阶梯
     */
    private Integer groupNo;
    /**
     * 优惠门槛数量或金额
     */
    private BigDecimal limitValue;
    /**
     * 赠品名称（优惠券或商品名称）
     */
    private String name;

    /**
     * 赠品id(skuid或优惠券id)
     */
    private Long id;
    /**
     * 商品id
     */
    private Long productId;

    /**
     * 赠送数量/搭配数量
     */
    private BigDecimal num;
    /**
     * 商品价格
     * */
    private BigDecimal price;
    /**
     * 套餐价格
     * */
    private BigDecimal groupPrice;

    /**
     * 规格
     */
    private String type;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * 搭配商品到手价
     * */
    private BigDecimal handPrice;
    /**
     * 赠品主图
     */
    private String productImgUrl;
}
