package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商家活动公共接口参数VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantActivityPlatformPageDataReq extends MerchantActivityCommonPageDataReq {

    private static final long serialVersionUID = -7428427972865103897L;

    /**
     * 会员名称
     */
    private String memberName;
}
