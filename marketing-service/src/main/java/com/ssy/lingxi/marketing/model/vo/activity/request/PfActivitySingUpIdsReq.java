package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 平台活动报名ids
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/12
 */
@Data
public class PfActivitySingUpIdsReq implements Serializable {

    private static final long serialVersionUID = -4780727001953398109L;
    /**
     * 平台活动报名ids
     */
    @NotEmpty(message = "平台活动报名ids参数不能为空")
    private List<Long> ids;

}
