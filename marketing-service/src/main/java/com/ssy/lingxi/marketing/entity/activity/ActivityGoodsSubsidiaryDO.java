package com.ssy.lingxi.marketing.entity.activity;


import com.ssy.lingxi.common.constant.TableNameConstant;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  活动[会员]商品附属表-DO
 *  包含[赠送促销、换购、套餐相关附属商品或券]
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_goods_subsidiary",
        indexes = {@Index(name=TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_goods_subsidiary_activity_goods_id_idx", columnList = "activity_goods_id")})
public class ActivityGoodsSubsidiaryDO implements Serializable {

    private static final long serialVersionUID = -7585565352360417120L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 活动商品
     */
    @ManyToOne(cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
    @JoinColumn(name = "activity_goods_id", referencedColumnName = "id")
    private ActivityGoodsDO activityGoods;

    /**
     * 会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;

    /**
     * 会员角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;

    /**
     * 会员类型
     * @see MemberTypeEnum
     */
    @Column
    private Integer memberType;

    /**
     * 商品id
     */
    @Column(columnDefinition = "int8")
    private Long productId;

    /**
     * skuId
     */
    @Column(columnDefinition = "int8")
    private Long skuId;

    /**
     * 商品名称
     */
    @Column(columnDefinition = "varchar(128)")
    private String productName;

    /**
     * 规格
     */
    @Column(columnDefinition = "varchar(128)")
    private String type;

    /**
     * 品类
     */
    @Column(columnDefinition = "varchar(128)")
    private String category;

    /**
     * 品牌
     */
    @Column(columnDefinition = "varchar(128)")
    private String brand;

    /**
     * 单位
     */
    @Column(columnDefinition = "varchar(128)")
    private String unit;

    /**
     * 分组编号/优惠阶梯
     */
    @Column(columnDefinition = "int2")
    private Integer groupNo;

    /**
     * 换购门槛/优惠门槛数量或金额
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal limitValue;

    /**
     * 商品价格
     * */
    @Column(columnDefinition = "numeric")
    private BigDecimal price;

    /**
     * 换购价格
     * */
    @Column(columnDefinition = "numeric")
    private BigDecimal swapPrice;

    /**
     * 允许换购数量/赠送数量/搭配数量
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal num;

    /**
     * 套餐价格
     * */
    @Column(columnDefinition = "numeric")
    private BigDecimal groupPrice;

    /**
     * 赠品主图
     */
    @Column(columnDefinition = "varchar(250)")
    private String productImgUrl;
}
