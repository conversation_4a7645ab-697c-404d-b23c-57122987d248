package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 活动适用会员等级返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/19
 */
@Data
public class ActivityMemberLevelResp implements Serializable {

    private static final long serialVersionUID = -976807996791535153L;
    /**
     * 会员等级id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 会员等级id
     */
    private Long memberLevelId;

    /**
     * 会员类型
     */
    private Integer memberType;

    /**
     * 会员类型名称
     * */
    private String memberTypeName;

    /**
     * 会员角色类型
     */
    private Integer roleType;

    /**
     * 会员角色类型名称
     * */
    private String roleTypeName;

    /**
     * 会员角色id
     */
    private Long roleId;

    /**
     * 会员角色名称
     */
    private String roleName;

    /**
     * 会员等级类型
     * */
    private Integer levelType;

    /**
     * 会员等级类型名称
     * */
    private String levelTypeName;

    /**
     * 会员等级
     */
    private Integer level;

    /**
     * 会员等级标签
     * */
    private String levelTag;
}
