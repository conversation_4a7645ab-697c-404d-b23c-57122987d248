package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *  查询活动商品销量-接口参数VO
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@Getter
@Setter
public class ActivityGoodsSalesReq implements Serializable {


    private static final long serialVersionUID = 5060429859767337539L;
    /**
     * skuid
     */
    @NotNull(message = "skuid必填")
    @Range(min = 1, message = "skuid必须大于0")
    private Long skuId;
    /**
     * 所属类型 1-平台 2-商家
     */
    @NotNull(message = "活动所属类型必填")
    private Integer belongType;
    /**
     * 活动id
     * */
    @NotNull(message = "活动id必填")
    private Long activityId;
    /**
     * 会员id
     */
    private Long memberId;
    /**
     * 角色id
     */
    private Long roleId;
}
