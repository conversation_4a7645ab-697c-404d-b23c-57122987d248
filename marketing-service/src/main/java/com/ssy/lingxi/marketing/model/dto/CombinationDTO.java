package com.ssy.lingxi.marketing.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 组合优惠活动-精度差问题（最后一条记录采用减法原则计算）
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/16
 */
@Getter
@Setter
public class CombinationDTO implements Serializable {
    private static final long serialVersionUID = 8590373395037243450L;
    /**
     * 数量
     */
    private BigDecimal num;

    /**
     * 单sku总额
     */
    private BigDecimal skuAmount;

    /**
     * sku计次
     */
    private Integer skuNum;

    /**
     * 累计支付金额
     */
    private BigDecimal payAmount;
}
