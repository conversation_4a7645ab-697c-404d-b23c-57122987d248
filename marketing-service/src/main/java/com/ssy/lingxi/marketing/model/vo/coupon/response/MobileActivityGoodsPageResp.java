package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 活动商品列表返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/1
 */
@Getter
@Setter
public class MobileActivityGoodsPageResp implements Serializable {

    private static final long serialVersionUID = 676773254950650438L;

    /**
     * 活动商品id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 所属类型 1-平台 2-商家
     */
    private Integer belongType;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 会员角色id
     */
    private Long roleId;

    /**
     * 会员角色名称
     */
    private String roleName;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格
     */
    private String type;

    /**
     * 品类
     */
    private String category;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * 活动商品图片
     */
    private String productImgUrl;

}
