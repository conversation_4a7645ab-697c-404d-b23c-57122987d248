package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 营销活动商品列表-执行明细
 */
@Getter
@Setter
public class ActivityExecuteDetailPageResp implements Serializable {

    private static final long serialVersionUID = 5053681397253681140L;

    /**
     * 单据类型 1-订单； 2-退货申请单 ActivityRecordTypeEnum
     */
    private Integer recordType;
    /**
     * 单据类型 1-订单； 2-退货申请单 ActivityRecordTypeEnum
     */
    private String recordTypeName;
    /**
     * 所属类型 1-平台 2-商家
     */
    private Integer belongType;
    /**
     * skuId
     */
    private Long skuId;
    /**
     * 商城名称
     */
    private String shopName;
    /**
     * 订单/退货申请单id
     */
    private Long orderId;
    /**
     * 订单/退货申请单号
     */
    private String orderNo;
    /**
     * 采购会员id
     */
    private Long memberId;
    /**
     * 采购角色id
     */
    private Long roleId;
    /**
     * 客户名称
     */
    private String memberName;
    /**
     * 单据时间（来源订单或售后退货）
     * */
    private Long orderTime;
    /**
     * 购买数量/退货数量
     */
    private BigDecimal quantity;
    /**
     * 应付
     */
    private BigDecimal skuPrice;
    /**
     * 实付
     */
    private BigDecimal amount;

    /**
     * 单据状态.
     */
    private String statusName;
    /**
     * 是否含税：CommonBooleanEnum
     */
    private Integer isHasTax;
    /**
     *  税率
     */
    private BigDecimal taxRate;

}
