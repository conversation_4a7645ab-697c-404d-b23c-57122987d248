package com.ssy.lingxi.marketing.model.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 工费优惠计算结果
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/25
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class WorkFeeDiscountResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否满足活动条件
     */
    private Boolean satisfied = false;

    /**
     * 原始工费
     */
    private BigDecimal originalWorkFee = BigDecimal.ZERO;

    /**
     * 优惠后工费
     */
    private BigDecimal discountedWorkFee = BigDecimal.ZERO;

    /**
     * 工费优惠金额
     */
    private BigDecimal workFeeDiscountAmount = BigDecimal.ZERO;

    /**
     * 工费优惠比例 (0-1之间，如0.8表示8折)
     */
    private BigDecimal workFeeDiscountRate = BigDecimal.ONE;

    /**
     * 活动描述
     */
    private String activityDescription;

    /**
     * 优惠类型描述
     */
    private String discountTypeDescription;

    /**
     * 是否需要达到门槛条件
     */
    private Boolean needThreshold = false;

    /**
     * 门槛描述
     */
    private String thresholdDescription;

    /**
     * 距离门槛还差多少
     */
    private BigDecimal thresholdGap = BigDecimal.ZERO;
}