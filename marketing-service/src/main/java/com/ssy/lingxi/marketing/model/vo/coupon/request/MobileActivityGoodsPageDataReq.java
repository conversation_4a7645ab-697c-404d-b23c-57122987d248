package com.ssy.lingxi.marketing.model.vo.coupon.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * 活动商品列表请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/1
 */
@Getter
@Setter
public class MobileActivityGoodsPageDataReq extends PageDataReq {

    private static final long serialVersionUID = -2104614829538326600L;

    /**
     * 活动id
     */
    @NotNull(message = "活动id不能为空")
    private Long activityId;

    /**
     * 所属类型 1-平台 2-商家
     */
    @NotNull(message = "活动id不能为空")
    private Integer belongType;

}
