package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.coupon.MerchantCouponDetailDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 商家优惠券明细仓库类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/28
 */
public interface MerchantCouponDetailRepository extends JpaRepository<MerchantCouponDetailDO, Long>, JpaSpecificationExecutor<MerchantCouponDetailDO> {

    List<MerchantCouponDetailDO> findAllByCouponId(Long couponId);

    List<MerchantCouponDetailDO> findAllByCouponIdIn(List<Long> couponIdList);

    List<MerchantCouponDetailDO> findAllByCouponIdAndSubMemberIdAndSubRoleId(Long couponId, Long subMemberId, Long SubRoleId);

    List<MerchantCouponDetailDO> findAllBySubMemberIdAndSubRoleId(Long subMemberId, Long SubRoleId);

}
