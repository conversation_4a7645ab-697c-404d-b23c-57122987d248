package com.ssy.lingxi.marketing.model.vo.activity.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 平台活动列表返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PlatformActivityOnlineResp extends PlatformActivityPageResp {

    private static final long serialVersionUID = -694900651536249305L;

    /**
     * 上线活动
     */
    private boolean online;
}
