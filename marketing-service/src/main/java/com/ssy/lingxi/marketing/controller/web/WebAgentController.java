package com.ssy.lingxi.marketing.controller.web;

import cn.hutool.core.util.NumberUtil;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.api.model.request.ActivityGoodsQuantityCheckReq;
import com.ssy.lingxi.marketing.api.model.request.CartActivityPriceReq;
import com.ssy.lingxi.marketing.model.vo.activity.response.CartActivityPriceResp;
import com.ssy.lingxi.marketing.model.vo.coupon.request.MobileGoodsCartReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.MobileProductDetailTagReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.MobileShopCouponListReq;
import com.ssy.lingxi.marketing.model.vo.coupon.request.ReceiveCouponReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MobileCouponDetailCanUseResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MobileCouponResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.MobileProductDetailTagResp;
import com.ssy.lingxi.marketing.model.vo.coupon.response.ReceiveCouponResp;
import com.ssy.lingxi.marketing.service.IActivityPriceCalculateService;
import com.ssy.lingxi.marketing.service.IMobileActivityGoodsService;
import com.ssy.lingxi.marketing.service.IMobileCouponService;
import com.ssy.lingxi.marketing.service.feign.IMemberFeignService;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignQueryResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Web-代理下单相关
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/6
 */
@Slf4j
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/web/agent")
public class WebAgentController extends BaseController {

    @Resource
    private IMobileActivityGoodsService mobileActivityGoodsService;
    @Resource
    private IActivityPriceCalculateService activityPriceCalculateService;

    @Resource
    private IMobileCouponService mobileCouponService;
    @Resource
    private IMemberFeignService memberFeignService;


    /**
     * 校验会员关系以及身份对调
     * @param headers HttpHeaders
     * @return 登录用户缓存
     */
    private UserLoginCacheDTO buildAgentUser(HttpHeaders headers){
        UserLoginCacheDTO vendorUser=getSysUser();
        String memberId = headers.getFirst(Constant.MK_AGENT_MEMBER_ID);
        String roleId=headers.getFirst(Constant.MK_AGENT_ROLE_ID);
        if(!StringUtils.hasLength(memberId)||!StringUtils.hasLength(roleId)){
            throw new BusinessException(ResponseCodeEnum.PARAM_NOT_EXIST);
        }else if(!NumberUtil.isLong(memberId)||!NumberUtil.isLong(roleId)){
            throw new BusinessException(ResponseCodeEnum.PARAM_NOT_EXIST);
        }
        UserLoginCacheDTO agentUser=new UserLoginCacheDTO();
        agentUser.setMemberId(Long.parseLong(memberId));
        agentUser.setMemberRoleId(Long.parseLong(roleId));
        //校验供采关系
        MemberFeignReq feignVO=new MemberFeignReq();
        feignVO.setMemberId(vendorUser.getMemberId());
        feignVO.setRoleId(vendorUser.getMemberRoleId());
        WrapperResp<List<MemberFeignQueryResp>> listWrapperResp = memberFeignService.listAllLowerMembers(feignVO);
        if(listWrapperResp.getCode()!= ResponseCodeEnum.SUCCESS.getCode()|| CollectionUtils.isEmpty(listWrapperResp.getData())){
            throw new BusinessException(ResponseCodeEnum.PARAM_NOT_EXIST);
        }
        List<MemberFeignQueryResp> collect = listWrapperResp.getData().stream().filter(o -> o.getMemberId().equals(agentUser.getMemberId())&&o.getRoleId().equals(agentUser.getMemberRoleId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)) {
            throw new BusinessException(ResponseCodeEnum.PARAM_NOT_EXIST);
        }
        agentUser.setMemberName(collect.get(0).getMemberName());
        return agentUser;
    }

    private void isSelf(Long memberId,Long roleId){
        UserLoginCacheDTO vendorUser=getSysUser();
        if(!vendorUser.getMemberId().equals(memberId)||!vendorUser.getMemberRoleId().equals(roleId)){
            log.warn("代客下单权限不足,vendorUser:{}:{}, buyerUser:{}:{}",vendorUser.getMemberId(),vendorUser.getMemberRoleId(),memberId,roleId);
            throw new BusinessException(ResponseCodeEnum.DATA_AUTH_NOT_ALLOWED);
        }
    }


    /**
     * 校验购买数量【加入购物车的商品】
     **/
    @PostMapping("/activity/goods/check/quantity")
    public WrapperResp<BigDecimal> checkQuantity(@RequestHeader HttpHeaders headers, @Valid @RequestBody ActivityGoodsQuantityCheckReq request) {
        isSelf(request.getUpperMemberId(),request.getUpperRoleId());
        return WrapperUtil.success(activityPriceCalculateService.checkQuantityByAddCart(buildAgentUser(headers), request));
    }

    /**
     * 计算购物车商品活动价
     **/
    @PostMapping("/activity/goods/price/calculate")
    public WrapperResp<List<CartActivityPriceResp>> priceCalculate(@RequestHeader HttpHeaders headers, @Valid @RequestBody List<CartActivityPriceReq> request) {
        if (CollectionUtils.isEmpty(request)) {
            return WrapperUtil.success();
        }
        isSelf(request.get(0).getUpperMemberId(),request.get(0).getUpperRoleId());
        //代客下单过滤拼团
        request.forEach(o ->o.setJoinGroup(Boolean.FALSE));
        return WrapperUtil.success(activityPriceCalculateService.calculateActivityPrice(buildAgentUser(headers), request));
    }

    /**
     * 查询活动sku商品详情活动标签
     **/
    @PostMapping("/activity/goods/detail/tag")
    public WrapperResp<MobileProductDetailTagResp> getActivityGoodsProductDetailTag(@RequestHeader HttpHeaders headers, @Valid @RequestBody MobileProductDetailTagReq request) {
        isSelf(request.getMemberId(),request.getRoleId());
        //代客下单过滤拼团
        request.setFilterGroup(Boolean.TRUE);
        return WrapperUtil.success(mobileActivityGoodsService.getActivityGoodsProductDetailTag(buildAgentUser(headers), request));
    }


    /**
     * 提交订单 - 选择的优惠券列表
     **/
    @PostMapping("/coupon/list/by/order")
    public WrapperResp<List<MobileCouponDetailCanUseResp>> listOrderCouponDetail(@RequestHeader HttpHeaders headers, @Valid @RequestBody MobileGoodsCartReq request) {
        isSelf(request.getGoodsList().get(0).getMemberId(),request.getGoodsList().get(0).getRoleId());
        return WrapperUtil.success(mobileCouponService.listOrderCouponDetail(buildAgentUser(headers), request));
    }

    /**
     * 进货单领券 - 店铺优惠券列表
     */
    @GetMapping("/coupon/list/by/shop")
    public WrapperResp<List<MobileCouponResp>> listShopCoupon(@RequestHeader HttpHeaders headers, @Valid MobileShopCouponListReq request) {
        isSelf(request.getMemberId(),request.getRoleId());
        return WrapperUtil.success(mobileCouponService.listShopCoupon(buildAgentUser(headers), request));
    }

    /**
     * 用户领取优惠券
     **/
    @PostMapping("/coupon/receive")
    public WrapperResp<ReceiveCouponResp> receiveCoupon(@RequestHeader HttpHeaders headers, @Valid @RequestBody ReceiveCouponReq request) {
        return WrapperUtil.success(mobileCouponService.receiveCoupon(buildAgentUser(headers), request));
    }
}
