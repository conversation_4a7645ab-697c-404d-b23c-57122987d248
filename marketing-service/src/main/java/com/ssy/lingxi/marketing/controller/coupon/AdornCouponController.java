package com.ssy.lingxi.marketing.controller.coupon;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.coupon.request.AdornCouponListReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.AdornCouponListResp;
import com.ssy.lingxi.marketing.service.coupon.IAdornCouponService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 装修-优惠券
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/adorn/coupon")
public class AdornCouponController extends BaseController {

    private final IAdornCouponService adornCouponService;

    /**
     * 根据券Id列表查询券列表
     */
    @PostMapping("/list")
    public WrapperResp<List<AdornCouponListResp>> list(@RequestBody @Valid List<AdornCouponListReq> adornCouponListReqList) {
        Long shopId = getHeadersShopId();
        return WrapperUtil.success(adornCouponService.list(getSysUserOrNull(), shopId, adornCouponListReqList));
    }

}
