package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 商家优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MerchantCouponSubmitAuditPageResp extends MerchantCouponPageResp implements Serializable {

    private static final long serialVersionUID = 816567079089309277L;

    /**
     * 提交按钮
     */
    private boolean submit;

    /**
     * 删除按钮
     */
    private boolean delete;

    /**
     * 修改按钮
     */
    private boolean update;

}
