package com.ssy.lingxi.marketing.service.feign;

import com.ssy.lingxi.marketing.api.model.request.GoodsCartReq;
import com.ssy.lingxi.marketing.api.model.request.ProductTagReq;
import com.ssy.lingxi.marketing.api.model.response.GoodsCartResp;
import com.ssy.lingxi.marketing.api.model.response.ProductTagResp;

import java.util.List;

/**
 * 活动商品内部接口服务类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/2
 */
public interface IActivityGoodsFeignService {

    /**
     * 内部接口 - 查询活动商品活动标签
     * @param req 请求参数
     * @return 返回结果
     */
    List<ProductTagResp> listActivityGoodsProductTag(ProductTagReq req);

    /**
     * 内部接口 - 进货单获取sku商品活动
     * @param req 请求参数
     * @return 返回结果
     */
    List<GoodsCartResp> listActivityGoodsCart(GoodsCartReq req);

    /**
     * 获取商品的最大优惠价格
     * @param req 请求参数
     * @return 返回结果
     */
    List<ProductTagResp> getPreferentialPrice(ProductTagReq req);
}
