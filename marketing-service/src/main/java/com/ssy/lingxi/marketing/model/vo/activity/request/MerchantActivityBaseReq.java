package com.ssy.lingxi.marketing.model.vo.activity.request;

import cn.hutool.json.JSONObject;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 商家活动请求基础类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/20
 */
@Data
public class MerchantActivityBaseReq implements Serializable {

    private static final long serialVersionUID = -4309560527998405019L;

    /**
     * 活动名称
     * */
    @NotBlank(message = "活动名称不能为空")
    private String activityName;

    /**
     * 活动开始时间
     * */
    @NotNull(message = "活动开始时间不能为空")
    private Long startTime;

    /**
     * 活动结束时间
     * */
    @NotNull(message = "活动结束时间不能为空")
    private Long endTime;

    /**
     * 新用户(不包含会员) 0-否 1-是
     */
//    private Integer newUser = 0;
//
//    /**
//     * 老用户(不包含会员) 0-否 1-是
//     */
//    private Integer oldUser = 0;
//
//    /**
//     * 新会员(仅会员用户) 0-否 1-是
//     * */
//    private Integer newMember = 0;
//
//    /**
//     * 老会员(仅会员用户) 0-否 1-是
//     * */
//    private Integer oldMember = 0;

    /**
     * 活动定义.
     * */
    private JSONObject activityDefined;

    /**
     * 会员等级集合
     * */
//    private List<ActivityMemberLevelReq> memberLevelList;

    /**
     * 适用商城
     */
    @Size(min = 1, message = "请选择活动适用的商城")
    @Valid
    private List<ActivityShopReq> shopList;

    /**
     * 活动商品
     */
    @Size(min = 1,message = "请选择活动商品")
    @Valid
    private List<ActivityGoodsReq> productList;

    /**
     * 参与用户具体会员ID列表
     */
    private List<String> participantMemberIds;

    /**
     * 反向匹配开关
     * 0-正向匹配（仅指定会员可参与）
     * 1-反向匹配（除指定会员外都可参与）
     */
    private Integer reverseMatch;

    /**
     * 是否允许叠加优惠劵
     */
    @NotNull(message = "是否允许叠加优惠劵不能为空")
    private Boolean allowCoupon;

    /**
     * 活动描述
     */
    private String describe;
}
