package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Map;


/**
 * 活动页 - 新增/修改/装修 - DTO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/08/06
 */
@Data
public class SaveActivityPageReq {

    /**
     * ID
     */
    @NotNull(message = "主键id不能为空", groups = {Update.class, Adorn.class})
    private Long id;

    /**
     * 类型: 1.平台 2.商家
     */
    @NotNull(message = "类型不能为空", groups = {Add.class, Update.class})
    private Integer type;

    /**
     * 活动页名称
     */
    @NotBlank(message = "活动页名称不能为空", groups = {Add.class, Update.class})
    @Size(max = 60, message = "活动页名称最长60个字符", groups = {Add.class, Update.class})
    private String name;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空", groups = {Add.class, Update.class})
    private Long startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空", groups = {Add.class, Update.class})
    private Long endTime;

    /**
     * 适用环境: 1.WEB 2.H5 3.小程序 4.APP
     */
    @NotNull(message = "适用环境不能为空", groups = {Add.class, Update.class})
    private Integer environment;

//    /**
//     * 活动模板ID
//     */
//    @NotNull(message = "活动模板ID不能为空", groups = {Add.class, Update.class})
//    private Long templateId;
//
//    /**
//     * 活动模板名称
//     */
//    @NotBlank(message = "活动模板名称不能为空", groups = {Add.class, Update.class})
//    private String templateName;
//
//    /**
//     * 活动模板图片
//     */
////    @NotBlank(message = "活动模板图片不能为空", groups = {SaveActivityPageReq.Add.class, SaveActivityPageReq.Update.class})
//    private String templatePicUrl;

    /**
     * 商城ID
     */
    @NotNull(message = "商城ID不能为空", groups = {Add.class, Update.class})
    private Long shopId;

    /**
     * 商城名称
     */
    @NotBlank(message = "商城名称不能为空", groups = {Add.class, Update.class})
    private String shopName;

    /**
     * 装修内容
     */
    @NotEmpty(message = "装修内容不能为空", groups = {Adorn.class})
    private Map<String, Object> adornContent;

    public interface Add {}
    public interface Update {}
    public interface Adorn {}
}
