package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 优惠券返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MobileCouponDetailCanUseResp extends MobileBaseCouponDetailResp {


    /**
     * 店铺会员id
     */
    private Long memberId;

    /**
     * 店铺角色id
     */
    private Long roleId;

    /**
     * 适用于sku商品id
     */
    private List<Long> suitableSkuIdList;

    /**
     * 是否选中 0-否 1-是
     */
    private Integer select;
}
