package com.ssy.lingxi.marketing.model.vo.activity.response;

import com.ssy.lingxi.product.api.model.resp.CategoryBaseResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsBrandResp;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品区（装修） - 响应
 * <AUTHOR>
 * @since 2021/09/14
 * @version 2.0.0
 */
@Data
public class GoodsAreaAdornResp implements Serializable {
    private static final long serialVersionUID = -7629972213284083547L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品主图
     */
    private String mainPic;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 商品标语
     */
    private String slogan;

    /**
     * 商品卖点
     */
    private String[] sellingPoint;

    /**
     * 会员品类
     */
    private CategoryBaseResp customerCategory;

    /**
     * 品牌
     */
    private EsBrandResp brand;

    /**
     * 单位
     */
    private String unitName;

    /**
     * 最小值
     */
    private BigDecimal min;

    /**
     * 最大值
     */
    private BigDecimal max;

    /**
     * 最大优惠价
     */
    private BigDecimal price;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员角色id
     */
    private Long memberRoleId;

    /**
     * 会员名称
     */
    private String memberName;

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 已售数量(会员商品)
     */
    private BigDecimal sold;

    /**
     * 活动标签集合
     */
    private List<String> tagList;
}
