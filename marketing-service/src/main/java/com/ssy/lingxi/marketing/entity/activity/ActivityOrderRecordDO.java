package com.ssy.lingxi.marketing.entity.activity;


import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 活动订单记录[拼团]
 * <AUTHOR>
 * @since 2021/6/18
 * @version 2.0.0
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_order_record",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_order_record_activity_id_belong_type_idx", columnList = "belongType,activityId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_order_record_object_id_idx", columnList = "objectId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "activity_order_record_order_id_idx", columnList = "orderId")})
public class ActivityOrderRecordDO implements Serializable {

    private static final long serialVersionUID = 5063889940275000766L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 所属类型 1-平台 2-商家
     */
    @Column
    private Integer belongType;

    /**
     * 活动id
     */
    @Column
    private Long activityId;
    /**
     * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
     *         6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
     *         11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
     *         ActivityTypeEnum.class
     * */
    @Column(columnDefinition = "int2")
    private Integer activityType;

    /**
     * 订单id
     */
    @Column(columnDefinition = "int8")
    private Long orderId;
    /**
     * 采购会员id
     */
    @Column(columnDefinition = "int8")
    private Long memberId;
    /**
     * 采购会员名称
     */
    @Column(columnDefinition = "varchar(50)")
    private String memberName;
    /**
     * 采购会员头像
     */
    @Column(columnDefinition = "varchar(512)")
    private String logo;
    /**
     * 采购角色id
     */
    @Column(columnDefinition = "int8")
    private Long roleId;
    /**
     * 商品id
     */
    @Column(columnDefinition = "int8")
    private Long productId;
    /**
     * skuId
     */
    @Column(columnDefinition = "int8")
    private Long skuId;
    /**
     * 购买数量
     */
    @Column(columnDefinition = "numeric(15,4)")
    private BigDecimal quantity;

    /**
     * 状态（1：正常， 2：取消）
     */
    @Column
    private Integer status;
    /**
     * 是否拼主 （1：是， 0：否）
     */
    @Column
    private Integer isMaster;

    /**
     * 主表id(拼团记录id)
     */
    @Column(columnDefinition = "int8")
    private Long objectId;
}
