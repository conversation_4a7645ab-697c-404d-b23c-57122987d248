package com.ssy.lingxi.marketing.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.marketing.model.bo.PfActivitySignUpPageBO;
import com.ssy.lingxi.marketing.model.vo.activity.request.*;
import com.ssy.lingxi.marketing.model.vo.activity.response.*;
import com.ssy.lingxi.marketing.model.vo.coupon.request.FilterSkuIdReq;
import com.ssy.lingxi.marketing.model.vo.coupon.response.FilterSkuIdResp;

import java.util.List;

/**
 * 平台活动报名服务类
 * <AUTHOR> yzc
 * @version 2.0.0
 * @since 2021/8/22
 */
public interface IPlatformActivitySignUpService {

    /**
     * 平台营销活动查询 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 请求参数
     * @return 返回结果
     */
    PageDataResp<PfActivitySignUpPageResp> pageSummary(UserLoginCacheDTO loginUser, PfActivitySignUpPageDataReq pageVO);

    /**
     * 待提交审核报名资料 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 请求参数
     * @return 返回结果
     */
    PageDataResp<PfActivitySignUpSubmitExamPageResp> pageByToBeSubmit(UserLoginCacheDTO loginUser, PfActivitySignUpPageDataReq pageVO);

    /**
     * 待审核报名资料(一级) - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 请求参数
     * @return 返回结果
     */
    PageDataResp<PfActivitySignUpExamPageResp> pageByToBeExamineStep1(UserLoginCacheDTO loginUser, PfActivitySignUpPageDataReq pageVO);

    /**
     * 待审核报名资料(二级)  - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 请求参数
     * @return 返回结果
     */
    PageDataResp<PfActivitySignUpExamPageResp> pageByToBeExamineStep2(UserLoginCacheDTO loginUser, PfActivitySignUpPageDataReq pageVO);

    /**
     * 待提交报名资料  - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 请求参数
     * @return 返回结果
     */
    PageDataResp<PfActivitySignUpSubmitPageResp> pageByToBeSubmitSignUp(UserLoginCacheDTO loginUser, PfActivitySignUpPageDataReq pageVO);

    /**
     * 平台营销活动查询 - 活动详情
     * @param loginUser 登录用户信息
     * @param id 请求参数
     * @return 返回结果
     */
    PfActivitySignUpDetailResp detail(UserLoginCacheDTO loginUser, Long id);

    /**
     * 平台营销活动查询 - 活动详情 - 活动商品(分页)
     * @param loginUser 登录用户信息
     * @param pageVO 请求参数
     * @return 返回结果
     */
    PageDataResp<PfActivitySignUpGoodsPageResp> pageByActivityGoods(UserLoginCacheDTO loginUser, PfActivitySignUpGoodsPageDataReq pageVO);

    /**
     * 待提交审核报名资料 - 填写报名资料
     * @param loginUser 登录用户信息
     * @param pageVO 请求参数
     * @return 返回结果
     */
    Void save(UserLoginCacheDTO loginUser, PfActivitySignUpAddReq pageVO);

    /**
     * 待提交审核报名资料 - 修改报名资料
     * @param loginUser 登录用户信息
     * @param pageVO 请求参数
     * @return 返回结果
     */
    Void update(UserLoginCacheDTO loginUser, PfActivitySignUpUpdateReq pageVO);

    /**
     * 待提交审核报名资料 - 查询活动商品过滤的skuId
     * @param loginUser 登录用户信息
     * @param request 接口参数
     * @return 返回结果
     */
    FilterSkuIdResp getFilterSkuId(UserLoginCacheDTO loginUser, FilterSkuIdReq request);

    /**
     * 待提交审核报名资料 - 提交审核
     * @param loginUser 登录用户信息
     * @param submitReq 请求参数
     * @return 返回结果
     */
    Void submitExamine(UserLoginCacheDTO loginUser, PfActivityExamineSubmitReq submitReq);

    /**
     * 待提交审核报名资料 - 批量提交
     * @param loginUser 登录用户信息
     * @param req 请求参数
     * @return 返回结果
     */
    Void submitExamineBatch(UserLoginCacheDTO loginUser, PfActivitySingUpIdsReq req);

    /**
     * 审核报名资料(一级) - 审核
     * @param loginUser 登录用户信息
     * @param req 请求参数
     * @return 返回结果
     */
    Void examineStep1(UserLoginCacheDTO loginUser, PfActivityExamineSignUpReq req);

    /**
     * 审核报名资料(一级) - 批量审核
     * @param loginUser 登录用户信息
     * @param req 请求参数
     * @return 返回结果
     */
    Void examineStep1batch(UserLoginCacheDTO loginUser, PfActivitySingUpIdsReq req);

    /**
     * 审核报名资料(二级) - 审核
     * @param loginUser 登录用户信息
     * @param req 请求参数
     * @return 返回结果
     */
    Void examineStep2(UserLoginCacheDTO loginUser, PfActivityExamineSignUpReq req);

    /**
     * 审核报名资料(二级) - 批量审核
     * @param loginUser 登录用户信息
     * @param req 请求参数
     * @return 返回结果
     */
    Void examineStep2batch(UserLoginCacheDTO loginUser, PfActivitySingUpIdsReq req);

    /**
     * 待提交报名资料 - 提交
     * @param loginUser 登录用户信息
     * @param id 请求参数
     * @return 返回结果
     */
    Void submitSignUp(UserLoginCacheDTO loginUser, Long id);

    /**
     * 待提交报名资料 - 批量提交
     * @param loginUser 登录用户信息
     * @param ids 请求参数
     * @return 返回结果
     */
    Void submitSignUpBatch(UserLoginCacheDTO loginUser, List<Long> ids);
    /**
     * 平台营销活动执行 - 分页列表
     * @param loginUser 登录用户信息
     * @param pageVO 请求参数
     * @return 返回结果
     */
    PageDataResp<PfActivitySignUpPageBO> pageExecuteByMerchant(UserLoginCacheDTO loginUser, PfActivitySignUpPageDataReq pageVO);
}
