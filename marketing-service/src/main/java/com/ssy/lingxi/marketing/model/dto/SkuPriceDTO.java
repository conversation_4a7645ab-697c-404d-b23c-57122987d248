package com.ssy.lingxi.marketing.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2023-08-15
 */
public class SkuPriceDTO implements Serializable {
    private static final long serialVersionUID = -4986783035353678357L;
    /**
     * 商品SkuId
     */
    private Long skuId;

    /**
     * 单个营销活动计算后的价格
     */
    private BigDecimal price;

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
}
