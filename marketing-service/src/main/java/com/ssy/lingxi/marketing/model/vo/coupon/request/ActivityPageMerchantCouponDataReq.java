package com.ssy.lingxi.marketing.model.vo.coupon.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 商家优惠券请求类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ActivityPageMerchantCouponDataReq extends PageDataReq implements Serializable {

    private static final long serialVersionUID = 5745579732284047086L;

    /**
     * 优惠券id
     */
    private Long id;

    /**
     * 优惠券名称
     */
    private String name;

    /**
     * 商城id
     */
    @NotNull(message = "商城id不能为空")
    private Long shopId;

}
