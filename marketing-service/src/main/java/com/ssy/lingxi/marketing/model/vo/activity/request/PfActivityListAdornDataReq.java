package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 平台营销活动列表（装修） - 请求
 * <AUTHOR>
 * @since 2021/08/12
 * @version 2.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PfActivityListAdornDataReq extends PageDataReq {
    private static final long serialVersionUID = 7205637320233019817L;

    /**
     * 商城ID
     */
    @NotNull(message = "商城ID不能空")
    private Long shopId;

    /**
     * 活动ID
     * */
    private Long id;

    /**
     * 活动名称
     * */
    private String activityName;

    /**
     * 活动开始时间
     * */
    private Long startTime;

    /**
     * 活动结束时间
     * */
    private Long endTime;

    /**
     * 活动类型
     * */
    private Integer activityType;

    /**
     * 细分类型（满额、满量、赠送促销）：1.满量减/满额减/赠商品/满额换购；2.满量折/满额折/赠优惠卷/买商品换购
     * */
    private Integer minType;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 平台品类ID
     * */
    private Long categoryId;
}
