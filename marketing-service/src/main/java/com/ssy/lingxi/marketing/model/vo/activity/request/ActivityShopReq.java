package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 活动使用商城VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/20
 */
@Data
public class ActivityShopReq implements Serializable {

    private static final long serialVersionUID = 2297255766747245417L;
    private Long id;
    /**
     * 商城id.
     */
    @NotNull(message = "商城id不能为空")
    private Long shopId;
    /**
     * 商城名称
     */
    @NotNull(message = "商城名称不能为空")
    private String shopName;
    /**
     * 商城Logo
     */
    @NotNull(message = "商城Logo不能为空")
    private String logo;
    /**
     * 商城环境
     */
    @NotNull(message = "商城环境不能为空")
    private Integer environment;


}
