package com.ssy.lingxi.marketing.entity.coupon;

import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * 平台优惠券实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/6/25
 */
@Data
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_coupon",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_coupon_type_idx", columnList = "type"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_coupon_member_id_idx", columnList = "memberId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "platform_coupon_role_id_idx", columnList = "roleId")})
public class PlatformCouponDO {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 优惠券名称
     */
    @Column(columnDefinition = "varchar(60)")
    private String name;

    /**
     * 优惠券类型 1-0元购买抵扣券 2-平台通用优惠券
     */
    @Column
    private Integer type;

    /**
     * 领(发)券起始时间
     */
    @Column(columnDefinition = "int8")
    private Long releaseTimeStart;

    /**
     * 领(发)券结束时间
     */
    @Column(columnDefinition = "int8")
    private Long releaseTimeEnd;

    /**
     * 券面额
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal denomination;

    /**
     * 发券数量
     */
    @Column
    private Integer quantity;

    /**
     * 领取方式 1-前台用户领券 2-指定会员发券 3-营销活动用券 4-会员运营用券
     */
    @Column
    private Integer getWay;

    /**
     * 领取条件, 每会员ID共可领
     */
    @Column
    private Integer conditionGetTotal;

    /**
     * 领取条件, 每会员ID每日可领
     */
    @Column
    private Integer conditionGetDay;

    /**
     * 有效类型 1-固定有效时间 2-自领取开始时间
     */
    @Column
    private Integer effectiveType;

    /**
     * 固定有效时间, 券有效起始时间
     */
    @Column(columnDefinition = "int8")
    private Long effectiveTimeStart;

    /**
     * 固定有效时间, 券有效结束时间
     */
    @Column(columnDefinition = "int8")
    private Long effectiveTimeEnd;

    /**
     * 自领取开始时间, 券多少天失效
     */
    @Column
    private Integer invalidDay;

    /**
     * 使用条件, 满多少金额可用
     */
    @Column(columnDefinition = "numeric")
    private BigDecimal useConditionMoney;

    /**
     * 使用条件说明
     */
    @Column(columnDefinition = "varchar(512)")
    private String useConditionDesc;

    /**
     * 状态 1-待提交审核 2-待审核(一级) 3-待审核不通过(一级) 4-待审核(二级) 5-待审核不通过(二级) 6-待提交 7-待执行 8-进行中 9-已终止 10-已取消 11-已结束
     */
    @Column
    private Integer status;

    /**
     * 工作流任务Id
     */
    @Column
    private String taskId;

    /**
     * 适用新老会员
     * 新会员(平台会员,当天注册, 上级为平台)
     * 老会员(平台会员,当天之前注册, 上级为平台)
     * 存储方式00, 一位为新会员, 二为老会员
     * 例子: 1. 选择新会员, 则值为10
     *      2. 全部都选择,则值为11
     *      3.全都不选择,则值为00
     */
    @Column(columnDefinition = "varchar(10)")
    private String suitableMember;

    /**
     * 适用会员类型
     * 企业会员
     * 个人会员
     * 存储方式00, 一位为企业会员, 二为个人会员
     * 例子: 1. 选择企业会员, 则值为10
     *      2. 全部都选择,则值为11
     *      3.全都不选择,则值为00
     */
    @Column(columnDefinition = "varchar(10)")
    private String suitableMemberType;

    /**
     * 会员id
     */
    @Column
    private Long memberId;

    /**
     * 角色id
     */
    @Column
    private Long roleId;

    /**
     * 会员名称
     */
    @Column
    private String memberName;

    /**
     * 创建时间
     */
    @Column(columnDefinition = "int8")
    private Long createTime;

    /**
     * 乐观锁
     */
    @Version
    private Integer version;
}
