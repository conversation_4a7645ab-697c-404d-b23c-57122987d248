package com.ssy.lingxi.marketing.model.vo.activity.request;

import com.ssy.lingxi.common.model.req.PageDataReq;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *  分页查询-平台活动报名查询-接口参数VO
 * <AUTHOR>
 * @since 2021/6/21
 * @version 2.0.0
 */
@Getter
@Setter
public class PfActivitySignUpGoodsPageDataReq extends PageDataReq implements Serializable {


    private static final long serialVersionUID = 5299512033633163879L;

    /**
     * 活动报名id
     */
    @NotNull(message = "活动报名id不能为空")
    private Long signUpId;
}
