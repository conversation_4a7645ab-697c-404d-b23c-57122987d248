package com.ssy.lingxi.marketing.serviceImpl.base.coupon;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 基础优惠券服务实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/16
 */
@Slf4j
@Service
public class BaseCouponService {

    /**
     * 判断券发放时间
     * @param currentTimeMillis 当前时间
     * @param releaseTimeStart 发券发放开始时间
     * @param releaseTimeEnd 发券发放结束时间
     */
    protected void checkReleaseTime(Long currentTimeMillis, Long releaseTimeStart, Long releaseTimeEnd) {
        // 券发放结束时间要大于券发放开始始时间
        checkReleaseTime(releaseTimeStart, releaseTimeEnd);

        // 券发放开始时间要大于当前时间
        if (currentTimeMillis >= releaseTimeStart) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_RELEASE_START_TIME_ERROR);
        }
        // 券发放结束时间要大于当前时间
        if (currentTimeMillis >= releaseTimeEnd) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_RELEASE_END_TIME_ERROR);
        }

    }

    /**
     * 判断券发放时间
     * @param releaseTimeStart 发券发放开始时间
     * @param releaseTimeEnd 发券发放结束时间
     */
    protected void checkReleaseTime(Long releaseTimeStart, Long releaseTimeEnd) {
        // 判断券发放时间
        if (Objects.isNull(releaseTimeStart) || Objects.isNull(releaseTimeEnd)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_RELEASE_TIME_NOT_NULL);
        }
        //  券发放结束时间要大于券发放开始始时间
        if (releaseTimeStart >= releaseTimeEnd) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_RELEASE_END_TIME_GE_COUPON_RELEASE_START_TIME);
        }
    }

    /**
     * 判断券有效时间
     * @param releaseTimeEnd 发券发放结束时间
     * @param effectiveTimeStart 发券有效开始时间
     * @param effectiveTimeEnd 发券有效结束时间
     */
    protected void checkEffectiveTime(Long releaseTimeEnd, Long effectiveTimeStart, Long effectiveTimeEnd) {
        // 判断活动时间
        if (Objects.isNull(effectiveTimeStart) || Objects.isNull(effectiveTimeEnd)) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_EFFECTIVE_TIME_NOT_NULL);
        }

        //  券有效结束时间要大于券有效开始始时间
        if (effectiveTimeStart >= effectiveTimeEnd) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_EFFECTIVE_END_TIME_GE_COUPON_EFFECTIVE_START_TIME);
        }

        //  券有效结束时间要大于券发放结束时间
        if (releaseTimeEnd > effectiveTimeEnd) {
            throw new BusinessException(ResponseCodeEnum.MARKETING_COUPON_RELEASE_START_TIME_GE_COUPON_EFFECTIVE_END_TIME);
        }
    }

    /**
     * 判断是否“不属于”当前会员
     * @param loginUser 登录用户信息
     * @param memberId 记录的memberId
     * @param roleId 记录的roleId
     * @return true false
     */
    protected boolean notBelongCurrentMember(UserLoginCacheDTO loginUser, Long memberId, Long roleId) {
        return !loginUser.getMemberId().equals(memberId) || !loginUser.getMemberRoleId().equals(roleId);
    }
}
