package com.ssy.lingxi.marketing.repository;


import com.ssy.lingxi.marketing.entity.activity.ActivityExecuteRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * 活动 - 执行记录
 * <AUTHOR>
 * @since 2021/6/18
 * @since 2021/06/17
 */
public interface ActivityExecuteRecordRepository extends JpaRepository<ActivityExecuteRecordDO, Long>, JpaSpecificationExecutor<ActivityExecuteRecordDO> {

    List<ActivityExecuteRecordDO> findByOrderNoAndSkuId(String orderNo, Long skuId);

    ActivityExecuteRecordDO findByOrderNoAndSkuIdAndBelongTypeAndActivityId(String orderNo, Long skuId,Integer belongType,Long activityId);
}
