package com.ssy.lingxi.marketing.model.vo.coupon.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 平台优惠券执行明细条件返回类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class PlatformCouponDetailPageResp implements Serializable {

    private static final long serialVersionUID = 5956196878679884909L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 平台优惠券id
     */
    private Long platformCouponId;

    /**
     * 券码
     */
    private String code;

    /**
     * 券状态
     */
    private Integer status;

    /**
     * 券状态名称
     */
    private String statusName;

    /**
     * 发放会员id(下级会员id)
     */
    private Long subMemberId;

    /**
     * 发放角色id(下级角色id)
     * 如果会员多角色, 取自于会员的第一个角色
     */
    private Long subRoleId;

    /**
     * 发放会员名称
     */
    private String subMemberName;

    /**
     * 适用会员类型名称
     */
    private String suitableMemberTypeName;

    /**
     * 创建时间(领取时间)
     */
    private Long createTime;

    /**
     * 有效时间开始
     */
    private Long validTimeStart;

    /**
     * 有效时间结束
     */
    private Long validTimeEnd;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 下单(使用)时间
     */
    private Long useTime;

    /**
     * 商城名称
     */
    private String shopName;

    /**
     * 订单金额
     */
    private BigDecimal amount;

    /**
     * 采购会员名称
     */
    private String buyerMemberName;

    /**
     * 订单状态名称
     */
    private String orderStatusName;
}
