package com.ssy.lingxi.marketing.domain;

import cn.hutool.core.util.NumberUtil;
import com.ssy.lingxi.common.constant.MarketingConstant;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 活动领域模型
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class ActivityDM {

    /**
     * 换算折扣
     */
    public static String convertDiscount(BigDecimal discount) {
        return NumberUtil.div(discount, BigDecimal.valueOf(MarketingConstant.DISCOUNT_CONVERSION), MarketingConstant.DISCOUNT_SCALE).stripTrailingZeros().toPlainString();
    }

}
