package com.ssy.lingxi.marketing.controller.web;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.model.vo.coupon.request.*;
import com.ssy.lingxi.marketing.model.vo.coupon.response.*;
import com.ssy.lingxi.marketing.service.IMobileCouponService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * Web - 优惠券
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/6
 */
@RestController
@RequestMapping(ServiceModuleConstant.MARKETING_PATH_PREFIX + "/web/coupon")
public class WebCouponController extends BaseController {

    @Resource
    private IMobileCouponService mobileCouponService;

    /**
     * 我的优惠券 - 分页列表
     **/
    @GetMapping("/detail/page")
    public WrapperResp<PageDataResp<WebCouponDetailResp>> pageCouponDetail(@Valid WebCouponPageDataReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileCouponService.pageCouponDetailByWeb(loginUser, request));
    }

    /**
     * 我的优惠券 - 数量统计
     **/
    @GetMapping("/detail/count")
    public WrapperResp<MobileCouponDetailCountResp> getCouponCount(@Valid MobileCouponDetailCountReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileCouponService.getCouponCount(loginUser, request));
    }

    /**
     * 进货单领券 - 店铺优惠券列表
     */
    @GetMapping("/list/by/shop")
    public WrapperResp<List<MobileCouponResp>> listShopCoupon(@Valid MobileShopCouponListReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileCouponService.listShopCoupon(loginUser, request));
    }

    /**
     * 活动页 - 自动领取优惠券
     **/
    @PostMapping("/auto/receive")
    public WrapperResp<List<MobileCouponDetailResp>> autoReceiveCoupon(@Valid @RequestBody AutoReceiveCouponReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileCouponService.autoReceiveCoupon(loginUser, request));
    }

    /**
     * 用户领取优惠券
     **/
    @PostMapping("/receive")
    public WrapperResp<ReceiveCouponResp> receiveCoupon(@Valid @RequestBody ReceiveCouponReq request) {
        UserLoginCacheDTO loginUser = getSysUser();
        return WrapperUtil.success(mobileCouponService.receiveCoupon(loginUser, request));
    }

    /**
     * 提交订单 - 选择的优惠券列表
     **/
    @PostMapping("/list/by/order")
    public WrapperResp<List<MobileCouponDetailCanUseResp>> listOrderCouponDetail(@Valid @RequestBody MobileGoodsCartReq request) {
        UserLoginCacheDTO loginUser = getSysUser();
        return WrapperUtil.success(mobileCouponService.listOrderCouponDetail(loginUser, request));
    }

    /**
     * 查询优惠券所关联的商品信息
     **/
    @PostMapping("/goods/list")
    public WrapperResp<List<MobileActivityRelationGoodsResp>> listMerchantCouponRelationGoods(@Valid @RequestBody MobileCouponGoodsRelationReq request) {
        UserLoginCacheDTO loginUser = isLogin() ? getSysUser() : null;
        return WrapperUtil.success(mobileCouponService.listMerchantCouponRelationGoodsSku(loginUser, request));
    }

}
