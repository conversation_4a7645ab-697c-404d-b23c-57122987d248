package com.ssy.lingxi.marketing.model.vo.activity.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 回显活动商品（装修） - 请求
 * <AUTHOR>
 * @since 2021/09/03
 * @version 2.0.0
 */
@Data
public class ActivityGoodsAdornReq implements Serializable {
    private static final long serialVersionUID = 3852269699335085431L;

    /**
     * 活动商品ID集合
     */
    @NotEmpty(message = "活动商品ID集不能为空")
    private List<Long> ids;

    /**
     * 省份行政编号
     */
    private String provinceCode;

    /**
     * 城市行政编号
     */
    private String cityCode;

    /**
     * 登录用户的会员等级(没有登录的用户显示商品基础价格，有登录的用户按价格策略显示)
     */
    private Long loginMemberLevel;
}
