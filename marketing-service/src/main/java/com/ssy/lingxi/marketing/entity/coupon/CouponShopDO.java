package com.ssy.lingxi.marketing.entity.coupon;


import com.ssy.lingxi.common.constant.TableNameConstant;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 优惠券适用商城实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/17
 */
@Setter
@Getter
@Entity
@Table(schema = TableNameConstant.TABLE_SCHEMA, name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_shop",
        indexes = {@Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_shop_coupon_idx", columnList = "couponId"),
                @Index(name = TableNameConstant.TABLE_PRE_MARKETING_SERVICE + "coupon_shop_belong_type_idx", columnList = "belongType")})
public class CouponShopDO implements Serializable {

    private static final long serialVersionUID = 3044901557738365108L;
    /**
     * ID
     * */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 优惠券id
     */
    @Column
    private Long couponId;

    /**
     * 所属类型 1-平台 2-商家
     */
    @Column
    private Integer belongType;

    /**
     * 商城id
     */
    @Column(columnDefinition = "int8")
    private Long shopId;
    /**
     * 商城名称
     */
    @Column(columnDefinition = "varchar(20)")
    private String shopName;

    /**
     * logo图片路径
     */
    @Column(columnDefinition = "varchar(250)")
    private String logo;
    /**
     * 商城环境:1.web 2.H5 3.小程序 4.IOS 5.安卓
     */
    @Column
    private Integer environment;
}
