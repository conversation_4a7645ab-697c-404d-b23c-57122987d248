<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ssy.lingxi.commodity.repository.mapper.support.IUnitMapper">

    <resultMap id="BaseResultMap" type="com.ssy.lingxi.commodity.api.model.resp.support.UnitRep">
            <id property="id" column="id"/>
            <result property="status" column="status"/>
            <collection property="unitNameList" ofType="com.ssy.lingxi.component.base.model.resp.TranslateResp" select="selectUnitNameList" column="id">
                <result column="language" property="language"/>
                <result column="name" property="value"/>
            </collection>
    </resultMap>

    <sql id="Base_Column_List">
        pu.id, pu.status, pu.create_time, pu.update_time, pu.is_deleted, pu.version
    </sql>

    <select id="findPageByNameLikeOrderByIdDesc" parameterType="String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pro_unit pu
        <where>
            pu.is_deleted = 0 and
            pu.id in (
                select unit_id from pro_unit_name pun
                <where>
                    <if test="name != null and name.trim() != ''">
                        pun.name like '%' || #{name} || '%'
                    </if>
                </where>
            )
        </where>
        order by pu.id desc
    </select>

    <select id="findByStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pro_unit pu
        <where>
            pu.is_deleted = 0 and
            pu.id in (
            select unit_id from pro_unit_name pun
            <where>
                <if test="status != null">
                    and pu.status = #{status}
                </if>
            </where>
            )
        </where>
    </select>

    <!-- 查询单位名称 -->
    <select id="selectUnitNameList" resultType="com.ssy.lingxi.component.base.model.resp.TranslateResp">
        select language, name as value from pro_unit_name pun where pun.unit_id = #{id}
    </select>
</mapper>
