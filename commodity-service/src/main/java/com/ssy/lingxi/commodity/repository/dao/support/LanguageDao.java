package com.ssy.lingxi.commodity.repository.dao.support;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ssy.lingxi.commodity.entity.do_.support.LanguageDO;
import com.ssy.lingxi.commodity.model.req.support.LanguageListDataReq;
import com.ssy.lingxi.commodity.repository.mapper.support.ILanguageMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class LanguageDao extends ServiceImpl<ILanguageMapper, LanguageDO> {

    public Page<LanguageDO> findPageByNameLikeAndNameEnLikeOrderById(LanguageListDataReq languageListReq) {
        return this.page(Page.of(languageListReq.getCurrent(), languageListReq.getPageSize()),
                Wrappers.lambdaQuery(LanguageDO.class)
                        .like(StringUtils.isNotBlank(languageListReq.getName()), LanguageDO::getName, "%" + languageListReq.getName() + "%")
                        .like(StringUtils.isNotBlank(languageListReq.getNameEn()), LanguageDO::getNameEn, "%" + languageListReq.getNameEn() + "%")
                        .orderByAsc(LanguageDO::getId)
        );
    }

}
