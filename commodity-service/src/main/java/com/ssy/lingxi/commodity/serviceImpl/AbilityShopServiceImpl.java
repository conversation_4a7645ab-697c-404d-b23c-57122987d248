package com.ssy.lingxi.commodity.serviceImpl;

import com.ssy.lingxi.commodity.handler.component.cache.ShopCacheComponent;
import com.ssy.lingxi.commodity.model.dto.AbilitySelfShopListDTO;
import com.ssy.lingxi.commodity.model.req.shop.EnvironmentReq;
import com.ssy.lingxi.commodity.model.resp.shop.ShopListV2Resp;
import com.ssy.lingxi.commodity.repository.dao.shop.ShopDao;
import com.ssy.lingxi.commodity.repository.dao.shop.StoreDao;
import com.ssy.lingxi.commodity.service.IAbilityShopService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AbilityShopServiceImpl implements IAbilityShopService {

    private final ShopDao shopDao;
    private final StoreDao storeDao;
    private final ShopCacheComponent shopCacheComponent;

    @Override
    public List<ShopListV2Resp> selfShopList(EnvironmentReq environmentReq, UserLoginCacheDTO user) {
        AbilitySelfShopListDTO abilitySelfShopListDTO = AbilitySelfShopListDTO.of(environmentReq, user);
        return shopDao.abilitySelfShopList(abilitySelfShopListDTO);
    }

}
