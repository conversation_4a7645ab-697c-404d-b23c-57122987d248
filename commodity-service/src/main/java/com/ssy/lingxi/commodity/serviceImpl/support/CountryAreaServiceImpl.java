package com.ssy.lingxi.commodity.serviceImpl.support;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.enums.AssociationShopFieldEnum;
import com.ssy.lingxi.commodity.api.model.req.HasAssociationShopReq;
import com.ssy.lingxi.commodity.api.model.resp.support.CountryAreaResp;
import com.ssy.lingxi.commodity.entity.do_.support.CountryAreaDO;
import com.ssy.lingxi.commodity.model.req.support.CountryAreaListDataReq;
import com.ssy.lingxi.commodity.model.req.support.CountryAreaUpdateStatusReq;
import com.ssy.lingxi.commodity.repository.dao.support.CountryAreaDao;
import com.ssy.lingxi.commodity.service.IShopService;
import com.ssy.lingxi.commodity.service.support.ICountryAreaService;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.PhoneSelectResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 国家(地区)
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/5
 */
@Service
public class CountryAreaServiceImpl implements ICountryAreaService {

    @Resource
    private CountryAreaDao countryAreaDao;

    @Resource
    private IShopService shopService;


    @Override
    public List<CountryAreaResp> getCountryAreaSelectList(CountryAreaListDataReq countryAreaListReq) {
        return countryAreaDao.list().stream().sorted(Comparator.comparing(CountryAreaDO::getId)).map(countryArea -> BeanUtil.copyProperties(countryArea, CountryAreaResp.class)).collect(Collectors.toList());
    }

    /**
     * 查询列表
     *
     * @param countryAreaListReq 参数
     */
    @Override
    public PageDataResp<CountryAreaResp> getCountryAreaList(CountryAreaListDataReq countryAreaListReq) {
        Page<CountryAreaDO> countryAreaPage = countryAreaDao.findPageByCodeLikeAndNameLikeAndNameEnLikeOrderById(countryAreaListReq);
        List<CountryAreaResp> countryAreaRespList = countryAreaPage.getRecords().stream().map(countryArea -> BeanUtil.copyProperties(countryArea, CountryAreaResp.class)).collect(Collectors.toList());
        return new PageDataResp<>(countryAreaPage.getTotal(), countryAreaRespList);
    }

    /**
     * 启用/停用
     *
     * @param countryAreaUpdateStatusReq 参数
     */
    @Override
    public Boolean updateStatus(CountryAreaUpdateStatusReq countryAreaUpdateStatusReq) {
        Long id = countryAreaUpdateStatusReq.getId();
        Boolean status = countryAreaUpdateStatusReq.getStatus();
        CountryAreaDO countryArea = countryAreaDao.getById(id);
        if (Objects.isNull(countryArea)) {
            throw new BusinessException(ResponseCodeEnum.MAN_COUNTRY_AREA_NOT_EXIST);
        }

        if (Boolean.FALSE.equals(countryAreaUpdateStatusReq.getStatus())) {
            // 校验是否关联商城
            Boolean hasAssociationShop = shopService.hasAssociationShop(new HasAssociationShopReq(countryArea.getId(), AssociationShopFieldEnum.CURRENCY_ID));
            BusinessAssertUtil.isFalse(hasAssociationShop, ResponseCodeEnum.MAN_COUNTRY_AREA_ALREADY_ASSOCIATION_SHOP);
        }

        countryArea.setStatus(status);
        countryAreaDao.saveOrUpdate(countryArea);

        return true;
    }

    /**
     * 查询国家(地区)
     *
     * @param countryAreaId 国家(地区)id
     */
    @Override
    public CountryAreaResp getCountryArea(Long countryAreaId) {
        CountryAreaDO countryArea = countryAreaDao.getById(countryAreaId);
        if (Objects.nonNull(countryArea)) {
            return BeanUtil.copyProperties(countryArea, CountryAreaResp.class);
        }
        throw new BusinessException(ResponseCodeEnum.MAN_COUNTRY_AREA_NOT_EXIST);
    }

    /**
     * 查询国家(地区)
     *
     * @param countryAreaCode 国家(地区)编码
     */
    @Override
    public CountryAreaResp getCountryArea(String countryAreaCode) {
        CountryAreaDO countryArea = countryAreaDao.findFirstByCode(countryAreaCode);
        if (Objects.nonNull(countryArea)) {
            return BeanUtil.copyProperties(countryArea, CountryAreaResp.class);
        }
        throw new BusinessException(ResponseCodeEnum.MAN_COUNTRY_AREA_NOT_EXIST);
    }

    /**
     * 查询所有国家(地区)
     */
    @Override
    public List<CountryAreaResp> getAllCountryArea() {
        List<CountryAreaDO> countryAreaDOList = countryAreaDao.findAllByStatus(Boolean.TRUE);
        return countryAreaDOList.stream().map(countryAreaDO -> BeanUtil.copyProperties(countryAreaDO, CountryAreaResp.class)).collect(Collectors.toList());
    }

    /**
     * 查询国家(地区)
     *
     * @param telCode 手机号前缀
     */
    @Override
    public CountryAreaResp getCountryAreaByTelCode(String telCode) {
        CountryAreaDO countryAreaDO = countryAreaDao.findFirstByTelCodeAndStatus(telCode, Boolean.TRUE);
        return Optional.ofNullable(countryAreaDO).map(c -> BeanUtil.copyProperties(c, CountryAreaResp.class)).orElse(null);
    }

    /**
     * 查询手机区号
     */
    @Override
    public List<PhoneSelectResp> listAreaCode() {
        List<CountryAreaDO> countryAreaDOList = countryAreaDao.findAllByStatus(Boolean.TRUE);
        return countryAreaDOList.stream().sorted(Comparator.comparing(CountryAreaDO::getId)).map(countryAreaDO -> new PhoneSelectResp(countryAreaDO.getCode(), countryAreaDO.getTelCode(), countryAreaDO.getTelLength())).collect(Collectors.toList());
    }

}
