package com.ssy.lingxi.commodity.serviceImpl.support;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.model.req.support.UnitReq;
import com.ssy.lingxi.commodity.api.model.resp.support.UnitFeignRep;
import com.ssy.lingxi.commodity.api.model.resp.support.UnitRep;
import com.ssy.lingxi.commodity.entity.do_.support.UnitDO;
import com.ssy.lingxi.commodity.entity.do_.support.UnitNameDO;
import com.ssy.lingxi.commodity.repository.dao.support.UnitDao;
import com.ssy.lingxi.commodity.repository.dao.support.UnitNameDao;
import com.ssy.lingxi.commodity.service.support.IUnitService;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.SelectLongResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.resp.TranslateResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 单位实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
@Service
public class UnitServiceImpl implements IUnitService {
    @Resource
    private UnitDao unitDao;
    @Resource
    private UnitNameDao unitNameDao;
    @Resource
    private ICommodityFeign commodityFeign;

    /**
     * 查询单位列表
     */
    @Override
    public PageDataResp<UnitRep> getUnitList(PageDataReq pageDataReq, String name){
        Page<UnitRep> unitRepList = unitDao.findPageByNameLikeOrderByIdDesc(pageDataReq, name);
        return new PageDataResp<>(unitRepList.getTotal(), unitRepList.getRecords());
    }

    /**
     * 查询单位
     */
    @Override
    public UnitRep getUnit(Long id) {
        UnitDO unitDO = unitDao.getById(id);
        if (Objects.isNull(unitDO)) {
            throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NOT_EXIST);
        }

        //单位名称
        List<UnitNameDO> unitNameDOList = unitNameDao.getByUnitId(id);
        List<TranslateResp> unitNameRespList = unitNameDOList.stream().map(unitNameDO -> new TranslateResp(unitNameDO.getLanguage(), unitNameDO.getName())).collect(Collectors.toList());

        return new UnitRep(unitDO.getId(), unitNameRespList, unitDO.getStatus());
    }

    /**
     * 查询单位
     */
    @Override
    public UnitFeignRep getFeignUnit(Long id) {
        UnitDO unitDO = unitDao.getById(id);
        if (Objects.isNull(unitDO)) {
            throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NOT_EXIST);
        }

        //单位名称
        List<UnitNameDO> unitNameDOList = unitNameDao.getByUnitId(id);
        UnitNameDO unitNameDO = unitNameDOList.stream().filter(unitName -> LocaleContextHolder.getLocale().toLanguageTag().equals(unitName.getLanguage())).findFirst().orElse(null);
        return new UnitFeignRep(unitDO.getId(), Objects.nonNull(unitNameDO) ? unitNameDO.getName() : null, unitDO.getStatus());
    }

    /**
     * 查询单位
     */
    @Override
    public List<UnitFeignRep> getFeignUnitList(List<Long> unitIdList) {
        if (CollUtil.isEmpty(unitIdList)) {
            throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NOT_EXIST);
        }
        List<UnitDO> unitDOList = unitDao.findByIdList(unitIdList);
        if (CollUtil.isEmpty(unitDOList)) {
            throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NOT_EXIST);
        }

        Map<Long, UnitDO> idUnitDOMap = unitDOList.stream().collect(Collectors.toMap(UnitDO::getId, Function.identity(), (v1, v2) -> v1));

        //单位名称
        List<UnitNameDO> unitNameDOList = unitNameDao.getByUnitIdList(unitIdList);
        return unitNameDOList.stream().collect(Collectors.groupingBy(UnitNameDO::getUnitId)).entrySet().stream().map(entrySet -> {
            Long unitId = entrySet.getKey();
            List<UnitNameDO> groupUnitNameDOList = entrySet.getValue();
            UnitNameDO unitNameDO = groupUnitNameDOList.stream().filter(unitName -> LocaleContextHolder.getLocale().toLanguageTag().equals(unitName.getLanguage())).findFirst().orElse(null);
            return new UnitFeignRep(unitId, Objects.nonNull(unitNameDO) ? unitNameDO.getName() : null, Optional.ofNullable(idUnitDOMap.get(unitId)).map(UnitDO::getStatus).orElse(null));
        }).collect(Collectors.toList());
    }

    /**
     * 添加/修改单位
     */
    @Transactional
    @Override
    public boolean saveOrUpdateUnit(UnitReq unitReq) {
        //判断是新增还是修改
        if (unitReq.getId() != null && unitReq.getId() > 0) {
            //判断数据是否存在
            UnitDO currentUnit = unitDao.getById(unitReq.getId());
            if (Objects.isNull(currentUnit)) {
                throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NOT_EXIST);
            }

            //检查是否重名
            List<UnitNameDO> unitNameDOList = new ArrayList<>();
            unitReq.getUnitNameList().forEach(unitName -> {
                boolean flag = unitNameDao.existsByUnitIdNotAndLanguageAndName(unitReq.getId(), unitName.getLanguage(), unitName.getValue());
                if(flag){
                    throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NAME_EXIST);
                }
                unitNameDOList.add(new UnitNameDO(unitReq.getId(), unitName.getLanguage(), unitName.getValue()));
            });

            //删除旧数据
            boolean flag = unitNameDao.deleteByUnitId(unitReq.getId());
            if(!flag){
                return Boolean.FALSE;
            }

            return unitNameDao.saveOrUpdateBatch(unitNameDOList);
        } else {
            //检查是否重名
            List<UnitNameDO> unitNameDOList = new ArrayList<>();
            unitReq.getUnitNameList().forEach(unitName -> {
                boolean flag = unitNameDao.existsByUnitIdNotAndLanguageAndName(null, unitName.getLanguage(), unitName.getValue());
                if(flag){
                    throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NAME_EXIST);
                }
                unitNameDOList.add(new UnitNameDO(unitReq.getId(), unitName.getLanguage(), unitName.getValue()));
            });

            //保存单位主表
            UnitDO unit = new UnitDO();
            unit.setStatus(true);
            boolean flag = unitDao.saveOrUpdate(unit);
            if(!flag){
                return Boolean.FALSE;
            }

            //保存单位名称表
            unitNameDOList.forEach(unitNameDO -> unitNameDO.setUnitId(unit.getId()));
            return unitNameDao.saveOrUpdateBatch(unitNameDOList);
        }
    }

    /**
     * 删除单位
     */
    @Override
    public boolean deleteUnit(Long id) {
        //验证数据库中是否存在该数据
        UnitDO unitDO = unitDao.getById(id);
        if (Objects.isNull(unitDO)) {
            throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NOT_EXIST);
        }

        //判断是否可以删除
        boolean status = unitDO.getStatus();
        if (status) {
            throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NOT_DELETE);
        }

        //检查是否有商品还在使用，如在使用，不能删除
        boolean flag = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityUseUnit(id));
        if (flag) {
            throw new BusinessException(ResponseCodeEnum.MAN_UNIT_COMMODITY_USE_NOT_DELETE);
        }

        return unitDao.removeById(id);
    }

    /**
     * 启用/停用属性
     */
    @Override
    public boolean updateUnitStatus(long id, boolean status) {
        //验证数据库中是否存在该数据
        UnitDO unitDO = unitDao.getById(id);
        if (Objects.isNull(unitDO)) {
            throw new BusinessException(ResponseCodeEnum.MAN_UNIT_NOT_EXIST);
        }

        //检查是否有商品还在使用，如在使用，不能停用
        if (!status) {
            boolean flag = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityUseUnit(id));
            if (flag) {
                throw new BusinessException(ResponseCodeEnum.MAN_UNIT_COMMODITY_USE_NOT_DISABLE);
            }
        }

        //持久化数据
        unitDO.setStatus(status);
        return unitDao.saveOrUpdate(unitDO);
    }

    /**
     * 查询单位下拉框
     * @param name 单位名称
     */
    @Override
    public List<SelectLongResp> getSelectUnit(String name) {
        //根据条件查询
        List<UnitRep> unitRepList = unitDao.findByStatus(Boolean.TRUE);
        //转换SelectVO实体
        return unitRepList.stream().map(unitRep -> {
            SelectLongResp selectLongResp = new SelectLongResp();
            selectLongResp.setValue(unitRep.getId());
            //单位名称
            List<TranslateResp> unitNameList = unitRep.getUnitNameList();
            if(!CollectionUtils.isEmpty(unitNameList)){
                TranslateResp translateResp = unitNameList.stream().filter(unitName -> LocaleContextHolder.getLocale().toLanguageTag().equals(unitName.getLanguage())).findFirst().orElse(null);
                selectLongResp.setLabel(translateResp != null ? translateResp.getValue() : null);
            }
            return selectLongResp;
        }).collect(Collectors.toList());
    }

    /**
     * 查询所有单位
     */
    @Override
    public List<UnitFeignRep> getAllUnit() {
        List<UnitRep> unitRepList = unitDao.findByStatus(Boolean.TRUE);
        return unitRepList.stream().map(unitRep -> {
            TranslateResp translateResp = unitRep.getUnitNameList().stream().filter(unitName -> LocaleContextHolder.getLocale().toLanguageTag().equals(unitName.getLanguage())).findFirst().orElse(null);
            return new UnitFeignRep(unitRep.getId(), Objects.nonNull(translateResp) ? translateResp.getValue() : null, unitRep.getStatus());
        }).collect(Collectors.toList());
    }
}

