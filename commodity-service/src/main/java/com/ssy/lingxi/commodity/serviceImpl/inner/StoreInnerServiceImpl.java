package com.ssy.lingxi.commodity.serviceImpl.inner;

import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.commodity.api.model.dto.MerchantLogoInnerDTO;
import com.ssy.lingxi.commodity.api.model.dto.StoreDTO;
import com.ssy.lingxi.commodity.api.model.dto.StoreListInnerDTO;
import com.ssy.lingxi.commodity.api.model.dto.UpdateShopStatusDTO;
import com.ssy.lingxi.commodity.api.model.req.shop.SelfBusinessShopLogoReq;
import com.ssy.lingxi.commodity.api.model.resp.MerchantLogoInnerResp;
import com.ssy.lingxi.commodity.api.model.resp.StoreInnerResp;
import com.ssy.lingxi.commodity.api.model.resp.shop.SelfBusinessShopLogoResp;
import com.ssy.lingxi.commodity.entity.do_.door.MemberLogisticsDO;
import com.ssy.lingxi.commodity.entity.do_.door.MemberProcessDO;
import com.ssy.lingxi.commodity.entity.do_.door.MemberPurchaseDO;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopDO;
import com.ssy.lingxi.commodity.entity.do_.shop.StoreDO;
import com.ssy.lingxi.commodity.handler.async.PurchaseAsyncService;
import com.ssy.lingxi.commodity.repository.dao.MemberLogisticsDao;
import com.ssy.lingxi.commodity.repository.dao.MemberProcessDao;
import com.ssy.lingxi.commodity.repository.dao.MemberPurchaseDao;
import com.ssy.lingxi.commodity.repository.dao.shop.ShopDao;
import com.ssy.lingxi.commodity.repository.dao.shop.StoreDao;
import com.ssy.lingxi.commodity.service.inner.IStoreInnerService;
import com.ssy.lingxi.commodity.service.web.IShopWebService;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 会员店铺 - 内部接口 - 业务实现层
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/12/09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StoreInnerServiceImpl implements IStoreInnerService {

    private final ShopDao shopDao;

    @Resource
    private StoreDao storeDao;
    @Resource
    private MemberPurchaseDao memberPurchaseDao;
    @Resource
    private MemberProcessDao memberProcessDao;
    @Resource
    private MemberLogisticsDao memberLogisticsDao;
    @Resource
    private PurchaseAsyncService purchaseAsyncService;
    @Resource
    private IShopWebService shopWebService;

    /**
     * 更新店铺
     */
    @Override
    @Transactional
    public void updateShop(StoreDTO dto) {
        //根据会员ID和角色ID更新不为空的参数（会员名称、注册年数、信用积分、满意度）
        StoreDO storeDO = storeDao.findFirstByMemberIdAndRoleId(dto.getMemberId(), dto.getRoleId()).orElse(null);
        MemberPurchaseDO memberPurchase = memberPurchaseDao.findFirstByMemberIdAndRoleId(dto.getMemberId(), dto.getRoleId()).orElse(null);
        MemberProcessDO memberProcess = memberProcessDao.findFirstByMemberIdAndRoleId(dto.getMemberId(), dto.getRoleId()).orElse(null);
        MemberLogisticsDO memberLogistics = memberLogisticsDao.findFirstByMemberIdAndRoleId(dto.getMemberId(), dto.getRoleId()).orElse(null);

        if (storeDO != null) {
            storeDO.setMemberName(StringUtils.isNotEmpty(dto.getMemberName()) ? dto.getMemberName() : storeDO.getMemberName());
            storeDO.setRegisterYears(dto.getRegisterYears() != null ? dto.getRegisterYears() : storeDO.getRegisterYears());
            storeDO.setCreditPoint(dto.getCreditPoint() != null ? dto.getCreditPoint() : storeDO.getCreditPoint());
            storeDO.setAvgTradeCommentStar(dto.getAvgTradeCommentStar() != null ? dto.getAvgTradeCommentStar() : storeDO.getAvgTradeCommentStar());
            storeDO.setRegisteredCapital(StringUtils.isNotEmpty(dto.getRegisteredCapital()) ? dto.getRegisteredCapital() : storeDO.getRegisteredCapital());
            storeDO.setEstablishmentDate(StringUtils.isNotEmpty(dto.getEstablishmentDate()) ? dto.getEstablishmentDate() : storeDO.getEstablishmentDate());
            storeDO.setBusinessLicence(StringUtils.isNotEmpty(dto.getBusinessLicence()) ? dto.getBusinessLicence() : storeDO.getBusinessLicence());
            storeDO.setRegisterArea(StringUtils.isNotEmpty(dto.getRegisterArea()) ? dto.getRegisterArea() : storeDO.getRegisterArea());
            storeDO.setRegisterAddress(StringUtils.isNotEmpty(dto.getRegisterAddress()) ? dto.getRegisterAddress() : storeDO.getRegisterAddress());
            storeDao.saveOrUpdate(storeDO);
        }
        if (memberPurchase != null) {
            memberPurchase.setMemberName(StringUtils.isNotEmpty(dto.getMemberName()) ? dto.getMemberName() : memberPurchase.getMemberName());
            memberPurchase.setRegisterYears(dto.getRegisterYears() != null ? dto.getRegisterYears() : memberPurchase.getRegisterYears());
            memberPurchase.setCreditPoint(dto.getCreditPoint() != null ? dto.getCreditPoint() : memberPurchase.getCreditPoint());
            memberPurchase.setAvgTradeCommentStar(dto.getAvgTradeCommentStar() != null ? dto.getAvgTradeCommentStar() : memberPurchase.getAvgTradeCommentStar());
            memberPurchase.setRegisteredCapital(StringUtils.isNotEmpty(dto.getRegisteredCapital()) ? dto.getRegisteredCapital() : memberPurchase.getRegisteredCapital());
            memberPurchase.setEstablishmentDate(StringUtils.isNotEmpty(dto.getEstablishmentDate()) ? dto.getEstablishmentDate() : memberPurchase.getEstablishmentDate());
            memberPurchase.setBusinessLicence(StringUtils.isNotEmpty(dto.getBusinessLicence()) ? dto.getBusinessLicence() : memberPurchase.getBusinessLicence());
            memberPurchase.setRegisterArea(StringUtils.isNotEmpty(dto.getRegisterArea()) ? dto.getRegisterArea() : memberPurchase.getRegisterArea());
            memberPurchase.setRegisterAddress(StringUtils.isNotEmpty(dto.getRegisterAddress()) ? dto.getRegisterAddress() : memberPurchase.getRegisterAddress());
            memberPurchaseDao.saveOrUpdate(memberPurchase);
        }
        if (memberProcess != null) {
            memberProcess.setMemberName(StringUtils.isNotEmpty(dto.getMemberName()) ? dto.getMemberName() : memberProcess.getMemberName());
            memberProcess.setRegisterYears(dto.getRegisterYears() != null ? dto.getRegisterYears() : memberProcess.getRegisterYears());
            memberProcess.setCreditPoint(dto.getCreditPoint() != null ? dto.getCreditPoint() : memberProcess.getCreditPoint());
            memberProcess.setAvgTradeCommentStar(dto.getAvgTradeCommentStar() != null ? dto.getAvgTradeCommentStar() : memberProcess.getAvgTradeCommentStar());
            memberProcess.setRegisteredCapital(StringUtils.isNotEmpty(dto.getRegisteredCapital()) ? dto.getRegisteredCapital() : memberProcess.getRegisteredCapital());
            memberProcess.setEstablishmentDate(StringUtils.isNotEmpty(dto.getEstablishmentDate()) ? dto.getEstablishmentDate() : memberProcess.getEstablishmentDate());
            memberProcess.setBusinessLicence(StringUtils.isNotEmpty(dto.getBusinessLicence()) ? dto.getBusinessLicence() : memberProcess.getBusinessLicence());
            memberProcess.setRegisterArea(StringUtils.isNotEmpty(dto.getRegisterArea()) ? dto.getRegisterArea() : memberProcess.getRegisterArea());
            memberProcess.setRegisterAddress(StringUtils.isNotEmpty(dto.getRegisterAddress()) ? dto.getRegisterAddress() : memberProcess.getRegisterAddress());
            memberProcessDao.saveOrUpdate(memberProcess);
        }
        if (memberLogistics != null) {
            memberLogistics.setMemberName(StringUtils.isNotEmpty(dto.getMemberName()) ? dto.getMemberName() : memberLogistics.getMemberName());
            memberLogistics.setRegisterYears(dto.getRegisterYears() != null ? dto.getRegisterYears() : memberLogistics.getRegisterYears());
            memberLogistics.setCreditPoint(dto.getCreditPoint() != null ? dto.getCreditPoint() : memberLogistics.getCreditPoint());
            memberLogistics.setAvgTradeCommentStar(dto.getAvgTradeCommentStar() != null ? dto.getAvgTradeCommentStar() : memberLogistics.getAvgTradeCommentStar());
            memberLogistics.setRegisteredCapital(StringUtils.isNotEmpty(dto.getRegisteredCapital()) ? dto.getRegisteredCapital() : memberLogistics.getRegisteredCapital());
            memberLogistics.setEstablishmentDate(StringUtils.isNotEmpty(dto.getEstablishmentDate()) ? dto.getEstablishmentDate() : memberLogistics.getEstablishmentDate());
            memberLogistics.setBusinessLicence(StringUtils.isNotEmpty(dto.getBusinessLicence()) ? dto.getBusinessLicence() : memberLogistics.getBusinessLicence());
            memberLogistics.setRegisterArea(StringUtils.isNotEmpty(dto.getRegisterArea()) ? dto.getRegisterArea() : memberLogistics.getRegisterArea());
            memberLogistics.setRegisterAddress(StringUtils.isNotEmpty(dto.getRegisterAddress()) ? dto.getRegisterAddress() : memberLogistics.getRegisterAddress());
            memberLogisticsDao.saveOrUpdate(memberLogistics);
        }

        //同步各个门户后顺便更新采购服务的询价、招标、竞价, 根据会员ID和角色ID更新信用分
        if (dto.getCreditPoint() != null) {
            purchaseAsyncService.updateCreditPoint(dto.getMemberId(), dto.getRoleId(), dto.getCreditPoint());
        }

    }

    /**
     * 更新店铺状态（可以是一个角色的禁用与启用，也可以是一个会员下的某个角色的禁用与启用）
     */
    @Override
    @Transactional
    public void updateShopStatus(UpdateShopStatusDTO dto) {
        //根据会员ID和角色ID更新状态
        if (dto.getMemberId() != null) {
            StoreDO storeDO = storeDao.findFirstByMemberIdAndRoleId(dto.getMemberId(), dto.getRoleId()).orElse(null);
            MemberPurchaseDO memberPurchase = memberPurchaseDao.findFirstByMemberIdAndRoleId(dto.getMemberId(), dto.getRoleId()).orElse(null);
            MemberProcessDO memberProcess = memberProcessDao.findFirstByMemberIdAndRoleId(dto.getMemberId(), dto.getRoleId()).orElse(null);
            MemberLogisticsDO memberLogistics = memberLogisticsDao.findFirstByMemberIdAndRoleId(dto.getMemberId(), dto.getRoleId()).orElse(null);

            if (storeDO != null) {
                storeDO.setStatus(dto.getStatus());
                storeDao.saveOrUpdate(storeDO);
            }
            if (memberPurchase != null) {
                memberPurchase.setStatus(dto.getStatus());
                memberPurchaseDao.saveOrUpdate(memberPurchase);
            }
            if (memberProcess != null) {
                memberProcess.setStatus(dto.getStatus());
                memberProcessDao.saveOrUpdate(memberProcess);
            }
            if (memberLogistics != null) {
                memberLogistics.setStatus(dto.getStatus());
                memberLogisticsDao.saveOrUpdate(memberLogistics);
            }

            return;
        }

        //根据角色ID更新店铺状态
        List<StoreDO> storeList = storeDao.findByRoleId(dto.getRoleId());
        List<MemberPurchaseDO> memberPurchaseList = memberPurchaseDao.findByRoleId(dto.getRoleId());
        List<MemberProcessDO> memberProcessList = memberProcessDao.findByRoleId(dto.getRoleId());
        List<MemberLogisticsDO> memberLogisticsList = memberLogisticsDao.findByRoleId(dto.getRoleId());

        if (CollUtil.isNotEmpty(storeList)) {
            storeDao.saveOrUpdateBatch(storeList.stream().peek(o -> o.setStatus(dto.getStatus())).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(memberPurchaseList)) {
            memberPurchaseDao.saveOrUpdateBatch(memberPurchaseList.stream().peek(memberPurchase -> memberPurchase.setStatus(dto.getStatus())).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(memberProcessList)) {
            memberProcessDao.saveOrUpdateBatch(memberProcessList.stream().peek(memberProcess -> memberProcess.setStatus(dto.getStatus())).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(memberLogisticsList)) {
            memberLogisticsDao.saveOrUpdateBatch(memberLogisticsList.stream().peek(memberLogistics -> memberLogistics.setStatus(dto.getStatus())).collect(Collectors.toList()));
        }

    }

    /**
     * 店铺列表
     */
    @Override
    public List<StoreInnerResp> storeList(List<StoreListInnerDTO> dto) {
        //校验
        if (CollUtil.isEmpty(dto)) {
            return new ArrayList<>();
        }

        //根据会员ID获取所有会员店铺门户
        List<StoreDO> storeDOList = storeDao.findByMemberIdIn(
                dto.stream().map(StoreListInnerDTO::getMemberId).collect(Collectors.toList())
        );

        //组装
        return dto.stream().map(a -> {
            //根据会员id和角色id获取对应的店铺门户
            StoreDO storeDO = storeDOList.stream().filter(b ->
                    b.getMemberId().equals(a.getMemberId()) && b.getRoleId().equals(a.getRoleId())
            ).findFirst().orElse(null);

            //封装
            StoreInnerResp storeInnerResp = new StoreInnerResp();
            storeInnerResp.setMemberId(a.getMemberId());
            storeInnerResp.setRoleId(a.getRoleId());
            storeInnerResp.setLogo(storeDO != null ? storeDO.getLogo() : null);
            storeInnerResp.setId(storeDO != null ? storeDO.getId() : null);
            storeInnerResp.setName(storeDO != null ? storeDO.getName() : null);
            return storeInnerResp;
        }).collect(Collectors.toList());
    }

    /**
     * 根据商城ID获取商家LOGO
     */
    @Override
    public List<MerchantLogoInnerResp> findMerchantLogoByShopId(MerchantLogoInnerDTO dto) {
        //定义返回实体
        List<MerchantLogoInnerResp> merchantLogoInnerRespList;

        //调用平台后台服务 -> 根据商城ID获取商城
        ShopDO shopDO = Optional.ofNullable(shopDao.getById(dto.getShopId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PT_CAN_T_GET_THE_MALL_ACCORDING_TO_THE_MALL_ID));

        //case1: 企业商城;  case2: 渠道商城;  case3: 直接返回;
        if (shopDO.getType().equals(ShopTypeEnum.ENTERPRISE.getCode())) {
            //自营商城封装自营商城的LOGO和商城名称;  非自营商城封装店铺LOGO和店铺名称
            if (shopDO.getIsSelf()) {
                SelfBusinessShopLogoReq selfBusinessShopLogoDTO = new SelfBusinessShopLogoReq();
                selfBusinessShopLogoDTO.setShopId(dto.getShopId());
                selfBusinessShopLogoDTO.setList(
                        dto.getList().stream().map(a -> {
                            MemberAndRoleIdDTO memberAndRoleIdDTO = new MemberAndRoleIdDTO();
                            memberAndRoleIdDTO.setMemberId(a.getMemberId());
                            memberAndRoleIdDTO.setRoleId(a.getRoleId());
                            return memberAndRoleIdDTO;
                        }).collect(Collectors.toList())
                );
                // 获取自营商家的商城LOGO
                List<SelfBusinessShopLogoResp> selfBusinessShopLogoRespList = shopWebService.findSelfBusinessShopLogo(selfBusinessShopLogoDTO);

                //组装
                merchantLogoInnerRespList = dto.getList().stream().map(a -> {
                    //根据会员id和角色id获取对应的自营商家
                    SelfBusinessShopLogoResp selfBusinessShopLogoVO = selfBusinessShopLogoRespList.stream().filter(b ->
                            b.getMemberId().equals(a.getMemberId()) && b.getMemberRoleId().equals(a.getRoleId())
                    ).findFirst().orElse(null);

                    //封装
                    MerchantLogoInnerResp merchantLogoInnerResp = new MerchantLogoInnerResp();
                    merchantLogoInnerResp.setMemberId(a.getMemberId());
                    merchantLogoInnerResp.setRoleId(a.getRoleId());
                    merchantLogoInnerResp.setLogo(selfBusinessShopLogoVO != null ? selfBusinessShopLogoVO.getLogoUrl() : null);
                    merchantLogoInnerResp.setName(selfBusinessShopLogoVO != null ? selfBusinessShopLogoVO.getName() : null);
                    return merchantLogoInnerResp;
                }).collect(Collectors.toList());

            } else {
                //根据会员ID获取所有会员店铺门户
                List<StoreDO> storeDOList = storeDao.findByMemberIdIn(
                        dto.getList().stream().map(StoreListInnerDTO::getMemberId).collect(Collectors.toList())
                );

                //组装
                merchantLogoInnerRespList = dto.getList().stream().map(a -> {
                    //根据会员id和角色id获取对应的店铺门户
                    StoreDO storeDO = storeDOList.stream().filter(b ->
                            b.getMemberId().equals(a.getMemberId()) && b.getRoleId().equals(a.getRoleId())
                    ).findFirst().orElse(null);

                    //封装
                    MerchantLogoInnerResp merchantLogoInnerResp = new MerchantLogoInnerResp();
                    merchantLogoInnerResp.setMemberId(a.getMemberId());
                    merchantLogoInnerResp.setRoleId(a.getRoleId());
                    merchantLogoInnerResp.setLogo(storeDO != null ? storeDO.getLogo() : null);
                    merchantLogoInnerResp.setName(storeDO != null ? storeDO.getName() : null);
                    return merchantLogoInnerResp;
                }).collect(Collectors.toList());
            }
        } else {
            //查询不到
            throw new BusinessException(ResponseCodeEnum.PT_CAN_NOT_FIND_VENDOR_LOGO);
        }

        return merchantLogoInnerRespList;
    }

    /**
     * 根据店铺id列表查询店铺对象
     *
     * @param shopIds id列表
     * @return 返回结果
     */
    @Override
    public List<StoreInnerResp> getStoreListByIds(List<Long> shopIds) {
        return storeDao.findByIdIn(shopIds);
    }

    /**
     * 根据店铺id查询店铺对象
     * @param storeId 店铺id
     * @return 返回结果
     */
    @Override
    public StoreInnerResp getStoreById(Long storeId) {
        StoreDO storeDO = storeDao.getById(storeId);
        if(Objects.isNull(storeDO)){
            return null;
        }
        StoreInnerResp storeInnerResp = new StoreInnerResp();
        storeInnerResp.setId(storeDO.getId());
        storeInnerResp.setName(storeDO.getName());
        storeInnerResp.setLogo(storeDO.getLogo());
        storeInnerResp.setMemberId(storeDO.getMemberId());
        storeInnerResp.setRoleId(storeDO.getRoleId());
        return storeInnerResp;
    }
}
