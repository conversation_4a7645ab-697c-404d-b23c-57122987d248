package com.ssy.lingxi.commodity.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ssy.lingxi.commodity.entity.do_.door.MemberPurchaseDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IMemberPurchaseMapper extends BaseMapper<MemberPurchaseDO> {

    @Select(value = "SELECT * FROM " + MemberPurchaseDO.TABLE_NAME + " WHERE create_time > #{timeInMillis} ORDER BY create_time DESC limit 5")
    List<MemberPurchaseDO> findNewAddMemberPurchase(@Param("timeInMillis") long timeInMillis);
}
