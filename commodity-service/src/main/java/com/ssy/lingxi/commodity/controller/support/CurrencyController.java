package com.ssy.lingxi.commodity.controller.support;

import com.ssy.lingxi.commodity.api.model.resp.support.CurrencyResp;
import com.ssy.lingxi.commodity.model.req.support.CurrencyListDataReq;
import com.ssy.lingxi.commodity.model.req.support.CurrencyUpdateStatusReq;
import com.ssy.lingxi.commodity.service.support.ICurrencyService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * 币种
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/5
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(CurrencyController.PATH_PREFIX)
public class CurrencyController {

    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/currency/";

    private final ICurrencyService currencyService;

    /**
     * 查询列表
     * @param currencyListReq 参数
     */
    @GetMapping(value = "getCurrencyList")
    public WrapperResp<PageDataResp<CurrencyResp>> getCurrencyList(CurrencyListDataReq currencyListReq) {
        return WrapperUtil.success(currencyService.getCurrencyList(currencyListReq));
    }

    /**
     * 启用/停用
     * @param currencyUpdateStatusReq 参数
     */
    @PostMapping(value = "updateStatus")
    public WrapperResp<Boolean> updateStatus(@RequestBody @Valid CurrencyUpdateStatusReq currencyUpdateStatusReq) {
        return WrapperUtil.success(currencyService.updateStatus(currencyUpdateStatusReq));
    }
}
