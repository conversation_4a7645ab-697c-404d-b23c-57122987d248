package com.ssy.lingxi.commodity.repository.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ssy.lingxi.commodity.entity.do_.door.SeoDO;
import com.ssy.lingxi.commodity.repository.mapper.ISeoMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
*/
@Repository
public class SeoDao extends ServiceImpl<ISeoMapper, SeoDO> {

    public List<SeoDO> findByDoorTypeAndDoorIdAndStatus(Integer doorType, Long doorId, Integer status) {
        return this.list(
                Wrappers.lambdaQuery(SeoDO.class).eq(SeoDO::getDoorType, doorType).eq(SeoDO::getDoorId, doorId).eq(SeoDO::getStatus, status)
        );
    }

    public Page<SeoDO> findByDoorTypeAndDoorIdAndNameLikeOrderByCreateTimeDesc(Integer doorType, Long doorId, String name, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(SeoDO.class).eq(Objects.nonNull(doorType), SeoDO::getDoorType, doorType).eq(Objects.nonNull(doorId), SeoDO::getDoorId, doorId)
                        .like(StringUtils.isNotBlank(name), SeoDO::getName, name).orderByDesc(SeoDO::getCreateTime)
        );
    }

    public List<SeoDO> findByDoorIdAndDoorTypeAndType(Long doorId, Integer doorType, Integer type) {
        return this.list(
                Wrappers.lambdaQuery(SeoDO.class).eq(SeoDO::getDoorId, doorId).eq(SeoDO::getDoorType, doorType).eq(SeoDO::getType, type)
        );
    }

    public List<SeoDO> findByDoorIdAndDoorTypeAndTypeAndIdNot(Long doorId, Integer doorType, Integer type, Long id) {
        return this.list(
                Wrappers.lambdaQuery(SeoDO.class).eq(SeoDO::getDoorId, doorId).eq(SeoDO::getDoorType, doorType).eq(SeoDO::getType, type).ne(SeoDO::getId, id)
        );
    }
}




