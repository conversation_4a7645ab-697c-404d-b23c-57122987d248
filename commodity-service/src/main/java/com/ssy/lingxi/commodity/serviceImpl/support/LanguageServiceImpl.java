package com.ssy.lingxi.commodity.serviceImpl.support;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.enums.AssociationShopFieldEnum;
import com.ssy.lingxi.commodity.api.model.req.HasAssociationShopReq;
import com.ssy.lingxi.commodity.api.model.resp.support.LanguageInfoResp;
import com.ssy.lingxi.commodity.api.model.resp.support.LanguageResp;
import com.ssy.lingxi.commodity.entity.do_.support.LanguageDO;
import com.ssy.lingxi.commodity.model.req.support.LanguageListDataReq;
import com.ssy.lingxi.commodity.model.req.support.LanguageUpdateStatusReq;
import com.ssy.lingxi.commodity.repository.dao.support.LanguageDao;
import com.ssy.lingxi.commodity.service.IShopService;
import com.ssy.lingxi.commodity.service.support.ILanguageService;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 语言
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/5
 */
@Service
public class LanguageServiceImpl implements ILanguageService {

    @Resource
    private LanguageDao languageDao;

    @Resource
    private IShopService shopService;

    /**
     * 列表查询
     */
    @Override
    public List<LanguageInfoResp> getLanguageList() {
        AtomicInteger i = new AtomicInteger(1);
        return languageDao.list()
                .stream()
                .filter(LanguageDO::getStatus)
                .map(language -> new LanguageInfoResp(language.getName(), language.getNameEn(), i.getAndIncrement()))
                .collect(Collectors.toList());
    }

    /**
     * 查询列表
     * @param languageListReq 参数
     */
    @Override
    public PageDataResp<LanguageResp> getLanguagePage(LanguageListDataReq languageListReq) {
        Page<LanguageDO> languagePage = languageDao.findPageByNameLikeAndNameEnLikeOrderById(languageListReq);
        List<LanguageResp> languageRespList = languagePage.getRecords().stream().map(language -> BeanUtil.copyProperties(language, LanguageResp.class)).collect(Collectors.toList());
        return new PageDataResp<>(languagePage.getTotal(), languageRespList);
    }

    /**
     * 启用/停用
     * @param languageUpdateStatusReq 参数
     */
    @Override
    public Boolean updateStatus(LanguageUpdateStatusReq languageUpdateStatusReq) {
        Long id = languageUpdateStatusReq.getId();
        Boolean status = languageUpdateStatusReq.getStatus();
        LanguageDO language = languageDao.getById(id);
        if(Objects.isNull(language)){
            throw new BusinessException(ResponseCodeEnum.MAN_LANGUAGE_NOT_EXIST);
        }

        if (Boolean.FALSE.equals(languageUpdateStatusReq.getStatus())) {
            // 校验是否关联商城
            Boolean hasAssociationShop = shopService.hasAssociationShop(new HasAssociationShopReq(language.getId(), AssociationShopFieldEnum.CURRENCY_ID));
            BusinessAssertUtil.isFalse(hasAssociationShop, ResponseCodeEnum.MAN_LANGUAGE_ALREADY_ASSOCIATION_SHOP);
        }

        language.setStatus(status);
        languageDao.saveOrUpdate(language);
        return true;
    }

    /**
     * 查询语言
     * @param languageId 语言id
     */
    @Override
    public LanguageResp getLanguage(Long languageId) {
        LanguageDO language = languageDao.getById(languageId);
        if(Objects.nonNull(language)){
            return BeanUtil.copyProperties(language, LanguageResp.class);
        }
        throw new BusinessException(ResponseCodeEnum.MAN_LANGUAGE_NOT_EXIST);
    }

}
