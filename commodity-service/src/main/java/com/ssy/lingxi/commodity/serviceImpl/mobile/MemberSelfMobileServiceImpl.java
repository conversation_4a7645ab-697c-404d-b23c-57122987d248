package com.ssy.lingxi.commodity.serviceImpl.mobile;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.commodity.entity.do_.door.MemberSelfDO;
import com.ssy.lingxi.commodity.model.req.mobile.MemberSelfMainMobileReq;
import com.ssy.lingxi.commodity.model.resp.mobile.MemberSelfMainMobileResp;
import com.ssy.lingxi.commodity.repository.dao.MemberSelfDao;
import com.ssy.lingxi.commodity.service.mobile.IMemberSelfMobileService;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * mobile - 会员自营 - 业务实现层
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/19
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MemberSelfMobileServiceImpl implements IMemberSelfMobileService {

    private final MemberSelfDao memberSelfDao;

    /**
     * 会员自营主页
     *
     * @param memberSelfMainMobileReq 请求参数
     * @return 操作结果
     */
    @Override
    public MemberSelfMainMobileResp memberSelfMain(MemberSelfMainMobileReq memberSelfMainMobileReq) {
        //根据会员ID获取会员自营门户
        MemberSelfDO memberSelf = memberSelfDao.findFirstByMemberId(memberSelfMainMobileReq.getMemberId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PT_RECORDS_DON_T_EXIST));

        // 拷贝
        return BeanUtil.copyProperties(memberSelf, MemberSelfMainMobileResp.class);
    }

}
