package com.ssy.lingxi.commodity.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.adorn.SearchAdornDO;
import com.ssy.lingxi.commodity.model.req.adorn.SearchAdornPageReq;
import com.ssy.lingxi.commodity.model.resp.adorn.SearchAdornPageResp;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ISearchAdornMapper extends BaseMapper<SearchAdornDO> {
    /**
     * 分页列表
     */
    Page<SearchAdornPageResp> pageList(IPage<SearchAdornPageResp> page, SearchAdornPageReq adornTopic<PERSON>age<PERSON><PERSON><PERSON><PERSON><PERSON>, UserLoginCacheDTO user);

}
