package com.ssy.lingxi.commodity.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.adorn.AdornTopicPageDO;
import com.ssy.lingxi.commodity.model.req.adorn.AdornTopicPageListReq;
import com.ssy.lingxi.commodity.model.resp.adorn.AdornTopicPageListResp;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 装修专题页
 *
 * <AUTHOR>
 */
@Mapper
public interface IAdornTopicPageMapper extends BaseMapper<AdornTopicPageDO> {
    /**
     * 分页列表
     */
    Page<AdornTopicPageListResp> pageList(IPage<AdornTopicPageListResp> page, AdornTopicPageListReq adornTopic<PERSON><PERSON><PERSON>ist<PERSON><PERSON><PERSON>, UserLoginCacheDTO user);

}
