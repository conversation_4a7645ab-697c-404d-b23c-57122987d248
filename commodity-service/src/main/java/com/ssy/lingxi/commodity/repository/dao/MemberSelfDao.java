package com.ssy.lingxi.commodity.repository.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ssy.lingxi.commodity.entity.do_.door.MemberSelfDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IMemberSelfMapper;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
* <AUTHOR>
*/
@Repository
public class MemberSelfDao extends CommonServiceImpl<IMemberSelfMapper, MemberSelfDO> {

    public Optional<MemberSelfDO> findFirstByMemberId(Long memberId) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberSelfDO.class).eq(MemberSelfDO::getMemberId, memberId)
        );
    }
}




