package com.ssy.lingxi.commodity.serviceImpl.web;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.bo.AreaBO;
import com.ssy.lingxi.commodity.entity.bo.CategoryBO;
import com.ssy.lingxi.commodity.entity.do_.collect.MemberProcessCollectDO;
import com.ssy.lingxi.commodity.entity.do_.door.MemberProcessDO;
import com.ssy.lingxi.commodity.model.req.common.CollectReq;
import com.ssy.lingxi.commodity.model.req.member.MemberProcessDataReq;
import com.ssy.lingxi.commodity.model.req.web.SaveProcessReq;
import com.ssy.lingxi.commodity.model.resp.web.MemberProcessListResp;
import com.ssy.lingxi.commodity.model.resp.web.MemberProcessMainResp;
import com.ssy.lingxi.commodity.repository.dao.MemberProcessCollectDao;
import com.ssy.lingxi.commodity.repository.dao.MemberProcessDao;
import com.ssy.lingxi.commodity.service.web.IMemberProcessWebService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.CollectionPageUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.manage.api.feign.IAreaFeign;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.feign.IMemberLevelRightCreditFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignIdReq;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLrcResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignRegisterQueryResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * web - 会员加工门户 - 业务实现层
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/04/14
 */
@Slf4j
@Service
public class MemberProcessWebServiceImpl implements IMemberProcessWebService {

    @Resource
    private MemberProcessDao memberProcessDao;
    @Resource
    private IMemberLevelRightCreditFeign memberLevelRightCreditControllerFeign;
    @Resource
    private IMemberFeign memberInnerControllerFeign;
    @Resource
    private IAreaFeign IAreaFeign;
    @Resource
    private MemberProcessCollectDao memberProcessCollectDao;

    /**
     * 保存当前登录会员的加工门户
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional
    public void saveCurrMemberProcess(SaveProcessReq dto, UserLoginCacheDTO user) {
        //校验（只有是服务提供者且企业类型是企业会员或个人会员才能创建加工门户
        if (!user.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            throw new BusinessException("角色类型不是服务提供者, 不能创建加工门户");
        }
        if (!(user.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || user.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))) {
            throw new BusinessException("会员类型不是企业会员或个人会员, 不能创建加工门户");
        }

        //默认省、市编码都为0（所有/所有）
        List<String> provinceCodes = Stream.of("0").collect(toList());
        List<String> cityCodes = Stream.of("0").collect(toList());
        if (dto.getAreaBOList().stream().noneMatch(a -> a.getProvinceCode().equals("0"))) {
            provinceCodes = dto.getAreaBOList().stream().map(AreaBO::getProvinceCode).collect(Collectors.toList());
            cityCodes = dto.getAreaBOList().stream().filter(a -> !a.getCityCode().equals("0")).map(AreaBO::getCityCode).collect(Collectors.toList());

            List<String> feignCodes = dto.getAreaBOList().stream().filter(a -> a.getCityCode().equals("0")).map(AreaBO::getProvinceCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(feignCodes)) {
                WrapperResp<List<String>> cityCodesWrapperResp = IAreaFeign.findCityCodeByProvinceCode(feignCodes);
                if (cityCodesWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode() && !CollectionUtils.isEmpty(cityCodesWrapperResp.getData())) {
                    cityCodes.addAll(cityCodesWrapperResp.getData());
                }
            }
        }

        //调用会员服务 -> 根据会员ID和角色ID获取平台会员的等级、注册年数、信用积分
        MemberFeignReq memberFeignReq = new MemberFeignReq();
        memberFeignReq.setMemberId(user.getMemberId());
        memberFeignReq.setRoleId(user.getMemberRoleId());
        WrapperResp<MemberFeignLrcResp> platformMemberLrcWrapperResp = memberLevelRightCreditControllerFeign.getPlatformMemberLrc(memberFeignReq);
        if (platformMemberLrcWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", platformMemberLrcWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
        }
        //调用会员服务 -> 根据会员Id查询会员注册信息
        MemberFeignIdReq memberFeignIdReq = new MemberFeignIdReq();
        memberFeignIdReq.setMemberId(user.getMemberId());
        WrapperResp<MemberFeignRegisterQueryResp> memberRegisterInfoWrapperResp = memberInnerControllerFeign.getMemberRegisterInfo(memberFeignIdReq);
        if (memberRegisterInfoWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", memberRegisterInfoWrapperResp.getMessage());
        }

        //保存会员加工门户信息
        MemberProcessDO memberProcess = memberProcessDao.findFirstByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId()).orElseGet(MemberProcessDO::new);
        memberProcess.setMemberId(user.getMemberId());
        memberProcess.setRoleId(user.getMemberRoleId());
        memberProcess.setCategoryList(dto.getCategoryBOList());
        memberProcess.setYearProcessAmount(dto.getYearProcessAmount());
        memberProcess.setPlantArea(dto.getPlantArea());
        memberProcess.setStaffNum(dto.getStaffNum());
        memberProcess.setAreaList(dto.getAreaBOList());
        memberProcess.setLogo(dto.getLogo());
        memberProcess.setDescribe(dto.getDescribe());
        memberProcess.setSlideshowList(dto.getSlideshowList());
        memberProcess.setCompanyPics(dto.getCompanyPics());
        memberProcess.setHonorPics(dto.getHonorPics());
        memberProcess.setAlbumName(dto.getAlbumName());
        memberProcess.setAlbumUrl(dto.getAlbumUrl());
        memberProcess.setAboutSeo(dto.getAboutSeo());
        memberProcess.setAreas(dto.getAreaBOList().stream().map(a -> a.getProvince() + "/" + a.getCity()).collect(Collectors.joining("，")));
        memberProcess.setProvincesCodeList(String.join("，", provinceCodes));
        memberProcess.setCityCodeList(String.join("，", cityCodes));
        memberProcess.setMemberName(user.getMemberName());
        memberProcess.setLevelTag(platformMemberLrcWrapperResp.getData().getLevelTag());
        memberProcess.setRegisterYears(platformMemberLrcWrapperResp.getData().getRegisterYears());
        memberProcess.setCreditPoint(platformMemberLrcWrapperResp.getData().getCreditPoint());
        memberProcess.setRegisteredCapital(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisteredCapital() : null);
        memberProcess.setEstablishmentDate(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getEstablishmentDate() : null);
        memberProcess.setBusinessLicence(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getBusinessLicence() : null);
        memberProcess.setRegisterArea(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisterArea() : null);
        memberProcess.setRegisterAddress(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisterAddress() : null);
        memberProcessDao.saveOrUpdate(memberProcess);

    }

    /**
     * 获取当前登录会员的加工门户
     *
     * @return 操作结果
     */
    @Override
    public MemberProcessDO findCurrMemberProcess(UserLoginCacheDTO user) {
        return memberProcessDao.findFirstByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId()).orElse(null);
    }

    /**
     * 加工门户列表
     *
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberProcessListResp> memberProcessList(MemberProcessDataReq qo) {

        List<MemberProcessDO> list = memberProcessDao.findByYearProcessAmountAndProvincesCodeListLikeOrEqAndCityCodeListLikeOrEqOrderByCreateTimeDesc(
                qo.getYearProcessAmount(), qo.getProvinceCode(), "0", qo.getCityCode(), "0"
        );

        if (CollUtil.isEmpty(list)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //搜索条件 - 主要加工种类。
        if (qo.getCategoryId() != null) {
            list = list.stream().filter(a ->
                    a.getCategoryList().stream().anyMatch(b ->
                            (b.getFirstId() != null && b.getFirstId().equals(qo.getCategoryId())) ||
                                    (b.getSecondId() != null && b.getSecondId().equals(qo.getCategoryId())) ||
                                    (b.getThirdlyId() != null && b.getThirdlyId().equals(qo.getCategoryId()))
                    )
            ).collect(toList());
        }

        //获取总记录数
        long totalCount = list.size();

        //对集合数据进行分页
        list = CollectionPageUtil.pageList(list, qo.getCurrent(), qo.getPageSize());

        //封装
        List<MemberProcessListResp> memberProcessListVOList = list.stream().map(a -> {
            //拷贝
            MemberProcessListResp vo = new MemberProcessListResp();
            BeanUtils.copyProperties(a, vo);

            //格式化一级主营加工种类
            vo.setCategoryBOList(
                    a.getCategoryList().stream().map(CategoryBO::getFirstName).collect(Collectors.joining("|"))
            );

            //默认先满星
            if (a.getAvgTradeCommentStar() == null || a.getAvgTradeCommentStar() == 0) {
                a.setAvgTradeCommentStar(5);
            }

            return vo;
        }).collect(toList());

        return new PageDataResp<>(totalCount, memberProcessListVOList);
    }

    /**
     * 加工门户主页
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public MemberProcessMainResp memberProcessMain(CommonIdReq dto, UserLoginCacheDTO user) {
        //获取加工门户
        MemberProcessDO memberProcess = memberProcessDao.getById(dto.getId());
        if (memberProcess == null) {
            throw new BusinessException("根据ID找不到加工门户");
        }

        //拷贝
        MemberProcessMainResp memberProcessMainVO = new MemberProcessMainResp();
        BeanUtils.copyProperties(memberProcess, memberProcessMainVO);

        //收藏状态
        if (user != null) {
            memberProcessMainVO.setCollectStatus(memberProcessCollectDao.existsByProcessIdAndMemberIdAndUserId(memberProcess.getId(), user.getMemberId(), user.getUserId()));
        }

        //默认先满星
        if (memberProcessMainVO.getAvgTradeCommentStar() == null || memberProcessMainVO.getAvgTradeCommentStar() == 0) {
            memberProcessMainVO.setAvgTradeCommentStar(5);
        }

        return memberProcessMainVO;
    }

    /**
     * 根据加工门户ID集合获取加工门户
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public List<MemberProcessDO> findByIdIn(CommonIdListReq dto) {
        return memberProcessDao.listByIds(dto.getIdList()).stream().sorted(Comparator.comparingInt(a -> dto.getIdList().indexOf(a.getId()))).collect(toList());
    }

    /**
     * 收藏/取消收藏
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    @Transactional
    public void collect(CollectReq dto, UserLoginCacheDTO user) {
        if (dto.getStatus()) {
            if (memberProcessCollectDao.existsByProcessIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId())) {
                throw new BusinessException("不能重复收藏，请刷新页面");
            }
            MemberProcessCollectDO collect = new MemberProcessCollectDO();
            collect.setProcessId(dto.getId());
            collect.setMemberId(user.getMemberId());
            collect.setUserId(user.getUserId());
            memberProcessCollectDao.save(collect);
        } else {
            memberProcessCollectDao.deleteByProcessIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId());
        }

    }

    /**
     * 收藏列表
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberProcessDO> collectList(PageDataReq dto, UserLoginCacheDTO user) {
        //按收藏时间倒序获取当前用户收藏的加工门户ID
        Page<MemberProcessCollectDO> page = memberProcessCollectDao.findByMemberIdAndUserIdOrderByCreateTimeDesc(user.getMemberId(), user.getUserId(), dto);
        List<MemberProcessCollectDO> memberProcessCollectList = page.getRecords();
        if (CollUtil.isEmpty(memberProcessCollectList)) {
            log.info("当前用户没有收藏的加工门户");
            return new PageDataResp<>(0L, new ArrayList<>());
        }
        List<Long> memberProcessIdList = memberProcessCollectList.stream().map(MemberProcessCollectDO::getProcessId).collect(toList());

        //根据收藏的加工ID获取加工门户
        List<MemberProcessDO> memberProcessList = memberProcessDao.listByIds(memberProcessIdList);

        //组装收藏时间
        memberProcessList = memberProcessList.stream().peek(a -> a.setCreateTime(
                memberProcessCollectList.stream().filter(b ->
                        b.getProcessId().equals(a.getId())
                ).map(MemberProcessCollectDO::getCreateTime).findFirst().orElse(null)
        )).sorted(Comparator.comparingInt(a -> memberProcessIdList.indexOf(a.getId()))).collect(Collectors.toList());

        return new PageDataResp<>(page.getTotal(), memberProcessList);
    }
}
