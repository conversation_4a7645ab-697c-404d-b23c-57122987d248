package com.ssy.lingxi.commodity.serviceImpl.support;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.enums.AssociationShopFieldEnum;
import com.ssy.lingxi.commodity.api.model.req.HasAssociationShopReq;
import com.ssy.lingxi.commodity.api.model.resp.support.CurrencyResp;
import com.ssy.lingxi.commodity.entity.do_.support.CurrencyDO;
import com.ssy.lingxi.commodity.model.req.support.CurrencyListDataReq;
import com.ssy.lingxi.commodity.model.req.support.CurrencyUpdateStatusReq;
import com.ssy.lingxi.commodity.model.resp.CurrencySelectResp;
import com.ssy.lingxi.commodity.repository.dao.support.CurrencyDao;
import com.ssy.lingxi.commodity.service.IShopService;
import com.ssy.lingxi.commodity.service.support.ICurrencyService;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 币种
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/5
 */
@Service
public class CurrencyServiceImpl implements ICurrencyService {

    @Resource
    private CurrencyDao currencyDao;

    @Resource
    private IShopService shopService;

    /**
     * 查询列表
     *
     * @param currencyListReq 参数
     */
    @Override
    public PageDataResp<CurrencyResp> getCurrencyList(CurrencyListDataReq currencyListReq) {
        Page<CurrencyDO> currencyPage = currencyDao.findPageByNameLikeAndNameEnLikeOrderById(currencyListReq);
        List<CurrencyResp> currencyRespList = currencyPage.getRecords().stream().map(currency -> BeanUtil.copyProperties(currency, CurrencyResp.class)).collect(Collectors.toList());
        return new PageDataResp<>(currencyPage.getTotal(), currencyRespList);
    }

    /**
     * 启用/停用
     *
     * @param currencyUpdateStatusReq 参数
     */
    @Override
    public Boolean updateStatus(CurrencyUpdateStatusReq currencyUpdateStatusReq) {
        Long id = currencyUpdateStatusReq.getId();
        Boolean status = currencyUpdateStatusReq.getStatus();
        CurrencyDO currency = currencyDao.getById(id);
        if (Objects.isNull(currency)) {
            throw new BusinessException(ResponseCodeEnum.MAN_CURRENCY_NOT_EXIST);
        }

        if (Boolean.FALSE.equals(currencyUpdateStatusReq.getStatus())) {
            // 校验是否关联商城
            Boolean hasAssociationShop = shopService.hasAssociationShop(new HasAssociationShopReq(currency.getId(), AssociationShopFieldEnum.CURRENCY_ID));
            BusinessAssertUtil.isFalse(hasAssociationShop, ResponseCodeEnum.MAN_CURRENCY_ALREADY_ASSOCIATION_SHOP);
        }

        currency.setStatus(status);
        currencyDao.saveOrUpdate(currency);
        return true;
    }

    /**
     * 查询币种
     *
     * @param currencyId 币种id
     */
    @Override
    public CurrencyResp getCurrency(Long currencyId) {
        CurrencyDO currency = currencyDao.getById(currencyId);
        if (Objects.nonNull(currency)) {
            return BeanUtil.copyProperties(currency, CurrencyResp.class);
        }
        throw new BusinessException(ResponseCodeEnum.MAN_CURRENCY_NOT_EXIST);
    }

    @Override
    public List<CurrencySelectResp> getSelectCurrency(String name) {
        List<CurrencyDO> currencyDOList = currencyDao.findByNameLikeAndStatus(name, Boolean.TRUE);
        return currencyDOList.stream().map(currencyDO -> {
            CurrencySelectResp currencySelectResp = new CurrencySelectResp();
            currencySelectResp.setValue(String.valueOf(currencyDO.getId()));
            currencySelectResp.setLabel(currencyDO.getName());
            currencySelectResp.setSymbol(currencyDO.getSymbol());
            return currencySelectResp;
        }).collect(Collectors.toList());
    }

}
