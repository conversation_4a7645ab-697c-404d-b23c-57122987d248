package com.ssy.lingxi.commodity.repository.mapper.shop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ssy.lingxi.commodity.entity.do_.shop.SelfShopModelDO;
import com.ssy.lingxi.commodity.model.resp.shop.SelfShopModelListResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
*/
@Mapper
public interface ISelfShopModelMapper extends BaseMapper<SelfShopModelDO> {

    List<SelfShopModelListResp> findSelfShopModelListByEnvironmentOrderById(@Param("environment") Integer environment);

}




