package com.ssy.lingxi.commodity.repository.dao.support;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ssy.lingxi.commodity.entity.do_.support.CurrencyDO;
import com.ssy.lingxi.commodity.model.req.support.CurrencyListDataReq;
import com.ssy.lingxi.commodity.repository.mapper.support.ICurrencyMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class CurrencyDao extends ServiceImpl<ICurrencyMapper, CurrencyDO> {

    public Page<CurrencyDO> findPageByNameLikeAndNameEnLikeOrderById(CurrencyListDataReq currencyListReq) {
        return this.page(Page.of(currencyListReq.getCurrent(), currencyListReq.getPageSize()),
                Wrappers.lambdaQuery(CurrencyDO.class)
                        .like(StringUtils.isNotBlank(currencyListReq.getName()), CurrencyDO::getName, "%" + currencyListReq.getName() + "%")
                        .like(StringUtils.isNotBlank(currencyListReq.getNameEn()), CurrencyDO::getNameEn, "%" + currencyListReq.getNameEn() + "%")
                        .orderByAsc(CurrencyDO::getId)
        );
    }

    public List<CurrencyDO> findByNameLikeAndStatus(String name, Boolean status) {
        return this.list(
                Wrappers.lambdaQuery(CurrencyDO.class).like(StringUtils.isNotBlank(name), CurrencyDO::getName, "%" + name + "%")
                        .eq(CurrencyDO::getStatus, status)
        );
    }
}
