package com.ssy.lingxi.commodity.repository.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.door.MemberPurchaseDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IMemberPurchaseMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
* <AUTHOR>
*/
@Repository
public class MemberPurchaseDao extends CommonServiceImpl<IMemberPurchaseMapper, MemberPurchaseDO> {

    public Optional<MemberPurchaseDO> findFirstByMemberIdAndRoleId(Long memberId, Long roleId) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberPurchaseDO.class).eq(MemberPurchaseDO::getMemberId, memberId).eq(MemberPurchaseDO::getRoleId, roleId)
        );
    }

    public List<MemberPurchaseDO> findByRoleId(Long roleId) {
        return this.list(
                Wrappers.lambdaQuery(MemberPurchaseDO.class).eq(MemberPurchaseDO::getRoleId, roleId)
        );
    }

    public Page<MemberPurchaseDO> findByMemberIdInAndMemberNameLikeAndProvinceCodeLikeOrEqAndCityCodeLikeOrEqOrderByCreditPointAndCreateTimeDesc(List<Long> memberIdList, String memberName, String provinceCode, String provinceCodeOr, String cityCode, String cityCodeOr, String sortCreditPoint, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberPurchaseDO.class).in(CollUtil.isNotEmpty(memberIdList), MemberPurchaseDO::getMemberId, memberIdList)
                        .like(StringUtils.isNotBlank(memberName), MemberPurchaseDO::getMemberName, "%" + memberName + "%")
                        .and(StringUtils.isNotBlank(provinceCode), wrapper -> wrapper.like(MemberPurchaseDO::getProvincesCodeList, "%" + provinceCode + "%").or().eq(MemberPurchaseDO::getProvincesCodeList, provinceCodeOr))
                        .and(StringUtils.isNotBlank(cityCode), wrapper -> wrapper.like(MemberPurchaseDO::getCityCodeList, "%" + cityCode + "%").or().eq(MemberPurchaseDO::getCityCodeList, cityCodeOr))
                        .orderBy(StringUtils.isNotBlank(sortCreditPoint), Objects.equals(sortCreditPoint, "ASC"), MemberPurchaseDO::getCreditPoint)
                        .orderByDesc(MemberPurchaseDO::getCreateTime)
        );
    }

    public List<MemberPurchaseDO> findNewAddMemberPurchase(long timeInMillis) {
        return getBaseMapper().findNewAddMemberPurchase(timeInMillis);
    }

    public Boolean existsByMemberIdAndRoleId(Long memberId, Long roleId) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberPurchaseDO.class).select(MemberPurchaseDO::getId)
                        .eq(MemberPurchaseDO::getMemberId, memberId).eq(MemberPurchaseDO::getRoleId, roleId)
        ).map(MemberPurchaseDO::getId).orElse(0L) > 0;
    }
}




