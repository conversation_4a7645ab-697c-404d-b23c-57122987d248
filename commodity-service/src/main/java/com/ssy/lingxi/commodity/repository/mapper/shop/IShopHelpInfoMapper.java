package com.ssy.lingxi.commodity.repository.mapper.shop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopHelpInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商城帮助信息
 *
 * <AUTHOR>
 */
@Mapper
public interface IShopHelpInfoMapper extends BaseMapper<ShopHelpInfoDO> {

    /**
     * 通过ID列表更新排序
     */
    void updateSortByIdIn(@Param("idList") List<Long> idList);

}
