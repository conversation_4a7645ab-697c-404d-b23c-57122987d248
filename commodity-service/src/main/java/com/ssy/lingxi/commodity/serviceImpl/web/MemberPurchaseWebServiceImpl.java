package com.ssy.lingxi.commodity.serviceImpl.web;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.model.dto.MemberPurchaseInnerDTO;
import com.ssy.lingxi.commodity.entity.bo.AreaBO;
import com.ssy.lingxi.commodity.entity.do_.collect.MemberPurchaseCollectDO;
import com.ssy.lingxi.commodity.entity.do_.door.MemberPurchaseDO;
import com.ssy.lingxi.commodity.entity.do_.door.SeoDO;
import com.ssy.lingxi.commodity.enums.SeoDoorTypeEnum;
import com.ssy.lingxi.commodity.enums.SeoTypeEnum;
import com.ssy.lingxi.commodity.model.req.common.CollectReq;
import com.ssy.lingxi.commodity.model.req.common.MemberIdAndRoleIdAndAdornIdReq;
import com.ssy.lingxi.commodity.model.req.member.MemberPurchaseDataReq;
import com.ssy.lingxi.commodity.model.req.web.SavePurchaseReq;
import com.ssy.lingxi.commodity.model.resp.web.MemberPurchaseListResp;
import com.ssy.lingxi.commodity.model.resp.web.MemberPurchaseMainResp;
import com.ssy.lingxi.commodity.repository.dao.MemberPurchaseCollectDao;
import com.ssy.lingxi.commodity.repository.dao.MemberPurchaseDao;
import com.ssy.lingxi.commodity.repository.dao.SeoDao;
import com.ssy.lingxi.commodity.service.web.IMemberPurchaseWebService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.manage.api.feign.IAreaFeign;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.feign.IMemberLevelRightCreditFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignIdReq;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLrcResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignRegisterQueryResp;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.model.req.MemberReq;
import com.ssy.lingxi.product.api.model.resp.store.StoreResp;
import com.ssy.lingxi.report.api.feign.IReportFeign;
import com.ssy.lingxi.report.api.model.req.PurchasePortalReq;
import com.ssy.lingxi.report.api.model.resp.ActiveMerchantResp;
import com.ssy.lingxi.report.api.model.resp.PurchasePortalStatisticsResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * web - 会员采购门户 - 业务实现层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/04/14
 */
@Slf4j
@Service
public class MemberPurchaseWebServiceImpl implements IMemberPurchaseWebService {

    @Resource
    private MemberPurchaseDao memberPurchaseDao;
    @Resource
    private IMemberLevelRightCreditFeign memberLevelRightCreditControllerFeign;
    @Resource
    private IMemberFeign memberInnerControllerFeign;
    @Resource
    private IAreaFeign areaFeign;
    @Resource
    private ICommodityFeign commodityFeign;
    @Resource
    private MemberPurchaseCollectDao memberPurchaseCollectDao;
    @Resource
    private SeoDao seoDao;
    @Resource
    private IReportFeign reportFeign;

    /**
     * 保存当前登录会员的采购门户
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    @Transactional
    public void saveCurrMemberPurchase(SavePurchaseReq dto, UserLoginCacheDTO user) {
        //校验（只有是服务消费者且企业类型是企业会员或个人会员才能创建采购门户）
        if (!user.getMemberRoleType().equals(RoleTypeEnum.SERVICE_CONSUMER.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ROLE_TYPE_NOT_SERVICE_PROVIDER_NO_PERMISSION_CREATE_PURCHASE_PORTAL);
        }
        if (!(user.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || user.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ROLE_TYPE_NOT_CORPORATE_OR_INDIVIDUAL_NO_PERMISSION_CREATE_PURCHASE_PORTAL);
        }

        //默认省、市编码都为0（所有/所有）
        List<String> provinceCodes = Stream.of("0").collect(toList());
        List<String> cityCodes = Stream.of("0").collect(toList());
        if (dto.getAreaBOList().stream().noneMatch(a -> a.getProvinceCode().equals("0"))) {
            provinceCodes = dto.getAreaBOList().stream().map(AreaBO::getProvinceCode).collect(Collectors.toList());
            cityCodes = dto.getAreaBOList().stream().filter(a -> !a.getCityCode().equals("0")).map(AreaBO::getCityCode).collect(Collectors.toList());

            List<String> feignCodes = dto.getAreaBOList().stream().filter(a -> a.getCityCode().equals("0")).map(AreaBO::getProvinceCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(feignCodes)) {
                WrapperResp<List<String>> cityCodesWrapperResp = areaFeign.findCityCodeByProvinceCode(feignCodes);
                if (cityCodesWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode() && !CollectionUtils.isEmpty(cityCodesWrapperResp.getData())) {
                    cityCodes.addAll(cityCodesWrapperResp.getData());
                }
            }
        }

        //调用会员服务 -> 根据会员ID和角色ID获取平台会员的等级、注册年数、信用积分
        MemberFeignReq memberFeignReq = new MemberFeignReq();
        memberFeignReq.setMemberId(user.getMemberId());
        memberFeignReq.setRoleId(user.getMemberRoleId());
        WrapperResp<MemberFeignLrcResp> platformMemberLrcWrapperResp = memberLevelRightCreditControllerFeign.getPlatformMemberLrc(memberFeignReq);
        if (platformMemberLrcWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", platformMemberLrcWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
        }
        //调用会员服务 -> 根据会员Id查询会员注册信息
        MemberFeignIdReq memberFeignIdReq = new MemberFeignIdReq();
        memberFeignIdReq.setMemberId(user.getMemberId());
        WrapperResp<MemberFeignRegisterQueryResp> memberRegisterInfoWrapperResp = memberInnerControllerFeign.getMemberRegisterInfo(memberFeignIdReq);
        if (memberRegisterInfoWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", memberRegisterInfoWrapperResp.getMessage());
        }

        //保存会员采购门户信息
        MemberPurchaseDO memberPurchase = memberPurchaseDao.findFirstByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId()).orElseGet(MemberPurchaseDO::new);
        memberPurchase.setMemberId(user.getMemberId());
        memberPurchase.setRoleId(user.getMemberRoleId());
        memberPurchase.setAreaList(dto.getAreaBOList());
        memberPurchase.setLogo(dto.getLogo());
        memberPurchase.setDescribe(dto.getDescribe());
        memberPurchase.setSlideshowList(dto.getSlideshowList());
        memberPurchase.setCompanyPics(dto.getCompanyPics());
        memberPurchase.setHonorPics(dto.getHonorPics());
        memberPurchase.setAdvertPics(dto.getAdvertPics());
        memberPurchase.setAlbumName(dto.getAlbumName());
        memberPurchase.setAlbumUrl(dto.getAlbumUrl());
        memberPurchase.setAreas(dto.getAreaBOList().stream().map(a -> a.getProvince() + "/" + a.getCity()).collect(Collectors.joining("，")));
        memberPurchase.setProvincesCodeList(String.join("，", provinceCodes));
        memberPurchase.setCityCodeList(String.join("，", cityCodes));
        memberPurchase.setMemberName(user.getMemberName());
        memberPurchase.setLevelTag(platformMemberLrcWrapperResp.getData().getLevelTag());
        memberPurchase.setRegisterYears(platformMemberLrcWrapperResp.getData().getRegisterYears());
        memberPurchase.setCreditPoint(platformMemberLrcWrapperResp.getData().getCreditPoint());
        memberPurchase.setRegisteredCapital(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisteredCapital() : null);
        memberPurchase.setEstablishmentDate(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getEstablishmentDate() : null);
        memberPurchase.setBusinessLicence(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getBusinessLicence() : null);
        memberPurchase.setRegisterArea(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisterArea() : null);
        memberPurchase.setRegisterAddress(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisterAddress() : null);
        memberPurchase.setProvinceName(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getProvinceName() : null);
        memberPurchase.setCityName(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getCityName() : null);
        memberPurchaseDao.saveOrUpdate(memberPurchase);

    }

    /**
     * 获取当前登录会员的采购门户
     *
     * @return 操作结果
     */
    @Override
    public MemberPurchaseDO findCurrMemberPurchase(UserLoginCacheDTO user) {
        return memberPurchaseDao.findFirstByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId()).orElseGet(MemberPurchaseDO::new);
    }

    /**
     * 采购门户列表
     *
     * @param qo 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberPurchaseListResp> memberPurchaseList(MemberPurchaseDataReq qo) {

        //搜索条件 - 平台品类ID。调用商品服务 -> 通过平台品类id查询会员ID集合，根据会员ID过滤采购门户。
        List<Long> memberIdList = new ArrayList<>();
        if (qo.getCategoryId() != null) {
            WrapperResp<List<Long>> getMemberIdListWrapperResp = commodityFeign.getMemberIdList(qo.getCategoryId());
            if (getMemberIdListWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
                if (CollUtil.isNotEmpty(getMemberIdListWrapperResp.getData())) {
                    memberIdList = getMemberIdListWrapperResp.getData();
                } else {
                    return new PageDataResp<>(0L, new ArrayList<>());
                }
            } else {
                log.info("请求商品服务失败:{}", getMemberIdListWrapperResp.getMessage());
            }
        }

        Page<MemberPurchaseDO> page = memberPurchaseDao.findByMemberIdInAndMemberNameLikeAndProvinceCodeLikeOrEqAndCityCodeLikeOrEqOrderByCreditPointAndCreateTimeDesc(
                memberIdList, qo.getMemberName(), qo.getProvinceCode(), "0", qo.getCityCode(), "0", qo.getSortCreditPoint(), qo
        );
        List<MemberPurchaseDO> list = page.getRecords();

        if (CollUtil.isEmpty(list)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //调用商品服务 -> 根据会员ID和角色ID集合获取采购门户的主营分类
        Map<String, StoreResp> map = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityAndCategoryByMemberIdAndMemberRoleId(
                list.stream().map(a -> {
                    MemberReq memberReq = new MemberReq();
                    memberReq.setMemberId(a.getMemberId());
                    memberReq.setMemberRoleId(a.getRoleId());
                    return memberReq;
                }).collect(toList())
        ));

        //调用报表服务 -> 获得采购门户-采购信息统计列表
        WrapperResp<List<PurchasePortalStatisticsResp>> wrapperResp = reportFeign.getPurchasePortalStatisticsInfoList(
                list.stream().map(a -> {
                    PurchasePortalReq request = new PurchasePortalReq();
                    request.setMemberId(a.getMemberId());
                    request.setRoleId(a.getRoleId());
                    return request;
                }).collect(toList())
        );
        if (wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.info("请求报表服务失败:{}", wrapperResp.getMessage());
        }

        //封装每个采购门户的主营分类和采购金额、采购数量、询价数量、招标数量、竞价数量
        List<MemberPurchaseListResp> memberPurchaseListVOList = list.stream().map(a -> {
            MemberPurchaseListResp vo = new MemberPurchaseListResp();
            BeanUtils.copyProperties(a, vo);

            if (!CollectionUtils.isEmpty(map) && map.get(a.getMemberId() + "-" + a.getRoleId()) != null) {
                vo.setMainCategory(map.get(a.getMemberId() + "-" + a.getRoleId()).getCustomerCategoryName());
            }

            if (!CollectionUtils.isEmpty(wrapperResp.getData())) {
                PurchasePortalStatisticsResp purchasePortalStatisticsResp = wrapperResp.getData().stream().filter(b ->
                        b.getMemberId().equals(a.getMemberId()) && b.getRoleId().equals(a.getRoleId())
                ).findFirst().orElse(null);
                if (purchasePortalStatisticsResp != null) {
                    vo.setPurchaseAmount(purchasePortalStatisticsResp.getAmount());
                    vo.setPurchaseNum(purchasePortalStatisticsResp.getNum());
                    vo.setInquiryNum(purchasePortalStatisticsResp.getInquiryNum());
                    vo.setInviteTenderNum(purchasePortalStatisticsResp.getInviteTenderNum());
                    vo.setBiddingNum(purchasePortalStatisticsResp.getBiddingNum());
                }
            }

            return vo;
        }).collect(toList());

        return new PageDataResp<>(page.getTotal(), memberPurchaseListVOList);
    }

    /**
     * 活跃采购商
     *
     * @return 操作结果
     */
    @Override
    public List<MemberPurchaseDO> activeMemberPurchase() {
        WrapperResp<List<ActiveMerchantResp>> wrapperResp = reportFeign.getActiveMerchantList();
        if (wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.info("请求报表服务失败:{}", wrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_REPORT_ERROR);
        }
        if (CollUtil.isEmpty(wrapperResp.getData())) {
            return new ArrayList<>();
        }
        List<String> idStr = wrapperResp.getData().stream().map(a -> a.getMemberId() + "-" + a.getRoleId()).collect(toList());

        List<MemberPurchaseDO> memberPurchaseList = memberPurchaseDao.list();
        if (CollUtil.isEmpty(memberPurchaseList)) {
            return new ArrayList<>();
        }

        return memberPurchaseList.stream().filter(a ->
                idStr.contains((a.getMemberId() + "-" + a.getRoleId()))
        ).collect(toList());
    }

    /**
     * 最新加入
     *
     * @return 操作结果
     */
    @Override
    public List<MemberPurchaseDO> newAddMemberPurchase() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 6);
        return memberPurchaseDao.findNewAddMemberPurchase(calendar.getTimeInMillis());
    }

    /**
     * 采购门户主页
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public MemberPurchaseMainResp memberPurchaseMain(CommonIdReq dto, Long memberId, Long roleId, UserLoginCacheDTO user) {
        //获取采购门户
        MemberPurchaseDO memberPurchase;
        if (dto.getId().equals(0L)) {
            if (memberId == null || roleId == null) {
                throw new BusinessException("会员ID或角色ID不能为空");
            }
            memberPurchase = memberPurchaseDao.findFirstByMemberIdAndRoleId(memberId, roleId).orElse(null);
        } else {
            memberPurchase = memberPurchaseDao.getById(dto.getId());
        }

        if (memberPurchase == null) {
            throw new BusinessException("该采购商还未创建采购门户");
        }

        //拷贝
        MemberPurchaseMainResp memberPurchaseMainVO = new MemberPurchaseMainResp();
        BeanUtils.copyProperties(memberPurchase, memberPurchaseMainVO);

        //收藏状态
        if (user != null) {
            memberPurchaseMainVO.setCollectStatus(memberPurchaseCollectDao.existsByPurchaseIdAndMemberIdAndUserId(memberPurchase.getId(), user.getMemberId(), user.getUserId()));
        }

        //调用商品服务 -> 根据会员ID和角色ID集合获取店铺的主营分类
        MemberReq memberReq = new MemberReq();
        memberReq.setMemberId(memberPurchaseMainVO.getMemberId());
        memberReq.setMemberRoleId(memberPurchaseMainVO.getRoleId());
        Map<String, StoreResp> map = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityAndCategoryByMemberIdAndMemberRoleId(
                Stream.of(memberReq).collect(toList())
        ));
        if (CollUtil.isNotEmpty(map) && map.get(memberPurchaseMainVO.getMemberId() + "-" + memberPurchaseMainVO.getRoleId()) != null) {
            memberPurchaseMainVO.setMainCategory(map.get(memberPurchaseMainVO.getMemberId() + "-" + memberPurchaseMainVO.getRoleId()).getCustomerCategoryName());
        }

        //获取采购SEO
        List<SeoDO> seoList = seoDao.findByDoorTypeAndDoorIdAndStatus(
                SeoDoorTypeEnum.PURCHASE.getCode(), memberPurchase.getId(), CommonBooleanEnum.YES.getCode()
        );
        //封装采购SEO(首页、关于我们)
        if (CollUtil.isNotEmpty(seoList)) {
            SeoDO homePage = seoList.stream().filter(a -> a.getType().equals(SeoTypeEnum.HOME_PAGE.getCode())).findFirst().orElse(null);
            SeoDO aboutUs = seoList.stream().filter(a -> a.getType().equals(SeoTypeEnum.ABOUT_US.getCode())).findFirst().orElse(null);
            memberPurchaseMainVO.setHomePage(homePage);
            memberPurchaseMainVO.setAboutUs(aboutUs);
        }

        //调用报表服务 -> 获得采购门户-采购信息统计
        PurchasePortalReq request = new PurchasePortalReq();
        request.setMemberId(memberPurchaseMainVO.getMemberId());
        request.setRoleId(memberPurchaseMainVO.getRoleId());
        WrapperResp<PurchasePortalStatisticsResp> wrapperResp = reportFeign.getPurchasePortalStatisticsInfo(request);
        if (wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.info("请求报表服务失败:{}", wrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_REPORT_ERROR);
        }
        if (wrapperResp.getData() != null) {
            memberPurchaseMainVO.setPurchaseAmount(wrapperResp.getData().getAmount());
            memberPurchaseMainVO.setPurchaseNum(wrapperResp.getData().getNum());
            memberPurchaseMainVO.setInquiryNum(wrapperResp.getData().getInquiryNum());
            memberPurchaseMainVO.setInviteTenderNum(wrapperResp.getData().getInviteTenderNum());
            memberPurchaseMainVO.setBiddingNum(wrapperResp.getData().getBiddingNum());
        }

        return memberPurchaseMainVO;
    }

    /**
     * 根据会员ID和角色ID获取采购门户
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public MemberPurchaseMainResp findByMemberIdAndRoleId(MemberIdAndRoleIdAndAdornIdReq dto) {
        //获取采购门户
        MemberPurchaseDO memberPurchase = memberPurchaseDao.findFirstByMemberIdAndRoleId(dto.getMemberId(), dto.getRoleId()).orElse(null);
        if (memberPurchase == null) {
            throw new BusinessException("根据ID找不到采购门户");
        }

        //拷贝
        MemberPurchaseMainResp memberPurchaseMainVO = new MemberPurchaseMainResp();
        BeanUtils.copyProperties(memberPurchase, memberPurchaseMainVO);

        //调用报表服务 -> 获得采购门户-采购信息统计
        PurchasePortalReq request = new PurchasePortalReq();
        request.setMemberId(memberPurchaseMainVO.getMemberId());
        request.setRoleId(memberPurchaseMainVO.getRoleId());
        WrapperResp<PurchasePortalStatisticsResp> wrapperResp = reportFeign.getPurchasePortalStatisticsInfo(request);
        if (wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.info("请求报表服务失败:{}", wrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_REPORT_ERROR);
        }
        if (wrapperResp.getData() != null) {
            memberPurchaseMainVO.setPurchaseAmount(wrapperResp.getData().getAmount());
            memberPurchaseMainVO.setPurchaseNum(wrapperResp.getData().getNum());
            memberPurchaseMainVO.setInquiryNum(wrapperResp.getData().getInquiryNum());
            memberPurchaseMainVO.setInviteTenderNum(wrapperResp.getData().getInviteTenderNum());
            memberPurchaseMainVO.setBiddingNum(wrapperResp.getData().getBiddingNum());
        }

        return memberPurchaseMainVO;
    }

    /**
     * 收藏/取消收藏
     *
     * @return 操作结果
     */
    @Override
    @Transactional
    public void collect(CollectReq dto, UserLoginCacheDTO user) {
        if (dto.getStatus()) {
            if (memberPurchaseCollectDao.existsByPurchaseIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId())) {
                throw new BusinessException("不能重复收藏，请刷新页面");
            }
            MemberPurchaseCollectDO collect = new MemberPurchaseCollectDO();
            collect.setPurchaseId(dto.getId());
            collect.setMemberId(user.getMemberId());
            collect.setUserId(user.getUserId());
            memberPurchaseCollectDao.save(collect);
        } else {
            memberPurchaseCollectDao.deleteByPurchaseIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId());
        }

    }

    /**
     * 收藏列表
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberPurchaseDO> collectList(PageDataReq dto, UserLoginCacheDTO user) {
        //按收藏时间倒序获取当前用户收藏的采购门户ID
        Page<MemberPurchaseCollectDO> page = memberPurchaseCollectDao.findByMemberIdAndUserIdOrderByCreateTimeDesc(user.getMemberId(), user.getUserId(), dto);
        List<MemberPurchaseCollectDO> memberPurchaseCollectList = page.getRecords();
        if (CollectionUtils.isEmpty(memberPurchaseCollectList)) {
            log.info("当前用户没有收藏的采购门户");
            return new PageDataResp<>(0L, new ArrayList<>());
        }
        List<Long> memberPurchaseIdList = memberPurchaseCollectList.stream().map(MemberPurchaseCollectDO::getPurchaseId).collect(toList());

        //根据收藏的采购ID获取采购门户
        List<MemberPurchaseDO> memberPurchaseList = memberPurchaseDao.listByIds(memberPurchaseIdList);

        //组装收藏时间
        memberPurchaseList = memberPurchaseList.stream().peek(a -> a.setCreateTime(
                memberPurchaseCollectList.stream().filter(b ->
                        b.getPurchaseId().equals(a.getId())
                ).map(MemberPurchaseCollectDO::getCreateTime).findFirst().orElse(null)
        )).sorted(Comparator.comparingInt(a -> memberPurchaseIdList.indexOf(a.getId()))).collect(Collectors.toList());

        return new PageDataResp<>(page.getTotal(), memberPurchaseList);
    }

    /**
     * 判断是否创建采购门户
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public Boolean exitMemberPurchase(MemberPurchaseInnerDTO dto) {
        //判断是否存在采购门户
        return memberPurchaseDao.existsByMemberIdAndRoleId(dto.getMemberId(), dto.getMemberRoleId());
    }
}
