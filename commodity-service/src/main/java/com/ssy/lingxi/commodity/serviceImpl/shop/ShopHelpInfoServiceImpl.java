package com.ssy.lingxi.commodity.serviceImpl.shop;

import com.ssy.lingxi.commodity.entity.do_.shop.ShopDO;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopHelpInfoDO;
import com.ssy.lingxi.commodity.handler.convert.ShopHelpInfoConvert;
import com.ssy.lingxi.commodity.model.req.shop.ShopHelpInfoEnableReq;
import com.ssy.lingxi.commodity.model.req.shop.ShopHelpInfoSaveReq;
import com.ssy.lingxi.commodity.model.req.shop.ShopHelpInfoTreeSortReq;
import com.ssy.lingxi.commodity.model.req.shop.ShopHelpInfoUpdateReq;
import com.ssy.lingxi.commodity.model.resp.shop.ShopHelpInfoResp;
import com.ssy.lingxi.commodity.model.resp.shop.ShopHelpInfoTreeResp;
import com.ssy.lingxi.commodity.repository.dao.shop.ShopDao;
import com.ssy.lingxi.commodity.repository.dao.shop.ShopHelpInfoDao;
import com.ssy.lingxi.commodity.service.shop.IShopHelpInfoService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.util.TreeUtils;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * 商城帮助信息
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class ShopHelpInfoServiceImpl implements IShopHelpInfoService {

    private final ShopHelpInfoDao shopHelpInfoDao;
    private final ShopDao shopDao;

    /**
     * 校验数据权限
     */
    private static void checkDataPermissions(UserLoginCacheDTO user, ShopDO shopDO) {
        // 校验数据权限
        if (SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode().equals(user.getLoginSource()) && Boolean.TRUE.equals(shopDO.getIsSelf())) {
            // 平台后台不能操作自营商城的数据
            throw new BusinessException(ResponseCodeEnum.COMMODITY_OPT_DATA_NOT_PERMISSIONS);
        }
        if (SystemSourceEnum.BUSINESS_WEB.getCode().equals(user.getLoginSource()) && !Boolean.TRUE.equals(shopDO.getIsSelf())) {
            // 非自营商城不能操作自营商城的数据
            throw new BusinessException(ResponseCodeEnum.COMMODITY_OPT_DATA_NOT_PERMISSIONS);
        }
        if (SystemSourceEnum.BUSINESS_WEB.getCode().equals(user.getLoginSource()) && Boolean.TRUE.equals(shopDO.getIsSelf()) && !Objects.equals(user.getMemberId(), shopDO.getMemberId())) {
            // 自营商城不能操作其他会员的商城的数据
            throw new BusinessException(ResponseCodeEnum.COMMODITY_OPT_DATA_NOT_PERMISSIONS);
        }
    }

    @Override
    public void helpInfoEnable(UserLoginCacheDTO user, ShopHelpInfoEnableReq shopHelpInfoEnableReq) {
        ShopDO shopDO = Optional.ofNullable(shopDao.getById(shopHelpInfoEnableReq.getShopId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MAN_MALL_DOES_NOT_EXIST));

        // 校验数据权限
        checkDataPermissions(user, shopDO);

        shopDao.updateHelpInfoEnableById(shopHelpInfoEnableReq.getShopId(), shopHelpInfoEnableReq.getHelpInfoEnable());
    }

    @Override
    public Boolean helpInfoEnable(Long shopId) {
        ShopDO shopDO = Optional.ofNullable(shopDao.getById(shopId)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MAN_MALL_DOES_NOT_EXIST));

        return shopDO.getHelpInfoEnable();
    }

    @Override
    public void save(UserLoginCacheDTO user, ShopHelpInfoSaveReq shopHelpInfoSaveReq) {
        ShopDO shopDO = Optional.ofNullable(shopDao.getById(shopHelpInfoSaveReq.getShopId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MAN_MALL_DOES_NOT_EXIST));

        // 校验数据权限
        checkDataPermissions(user, shopDO);

        ShopHelpInfoDO shopHelpInfoDO = ShopHelpInfoConvert.INSTANCE.toShopHelpInfoDO(shopHelpInfoSaveReq);
        shopHelpInfoDao.save(shopHelpInfoDO);
    }

    @Override
    public ShopHelpInfoResp detail(Long id) {
        ShopHelpInfoDO shopHelpInfoDO = Optional.ofNullable(shopHelpInfoDao.getById(id)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.COMMODITY_SHOP_HELP_INFO_NOT_FIND));

        return ShopHelpInfoConvert.INSTANCE.toShopHelpInfoResp(shopHelpInfoDO);
    }

    @Transactional
    @Override
    public void delete(UserLoginCacheDTO user, CommonIdReq req) {
        Long shopId = Optional.ofNullable(shopHelpInfoDao.findShopIdById(req.getId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.COMMODITY_DATA_RECORDS_DON_T_EXIST));

        ShopDO shopDO = Optional.ofNullable(shopDao.getById(shopId)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MAN_MALL_DOES_NOT_EXIST));

        // 校验数据权限
        checkDataPermissions(user, shopDO);

        shopHelpInfoDao.removeById(req.getId());
        // 父节点和子节点一并删除
        shopHelpInfoDao.removeByParentId(req.getId());
    }

    @Override
    public void update(UserLoginCacheDTO user, ShopHelpInfoUpdateReq shopHelpInfoUpdateReq) {
        Long shopId = Optional.ofNullable(shopHelpInfoDao.findShopIdById(shopHelpInfoUpdateReq.getId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.COMMODITY_DATA_RECORDS_DON_T_EXIST));

        ShopDO shopDO = Optional.ofNullable(shopDao.getById(shopId)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MAN_MALL_DOES_NOT_EXIST));

        // 校验数据权限
        checkDataPermissions(user, shopDO);

        ShopHelpInfoDO shopHelpInfoDO = ShopHelpInfoConvert.INSTANCE.toShopHelpInfoDO(shopHelpInfoUpdateReq);
        shopHelpInfoDao.updateById(shopHelpInfoDO);
    }

    @Override
    public List<ShopHelpInfoTreeResp> tree(Long shopId) {
        List<ShopHelpInfoTreeResp> shopHelpInfoTreeRespList = shopHelpInfoDao.findShopHelpInfoTreeRespByShopId(shopId);

        return TreeUtils.transferToTree(
                shopHelpInfoTreeRespList, 0L, ShopHelpInfoTreeResp::getId, ShopHelpInfoTreeResp::getParentId,
                ShopHelpInfoTreeResp::setChildren, Comparator.comparingInt(ShopHelpInfoTreeResp::getSort)
        );
    }

    @Override
    @Transactional
    public void sort(UserLoginCacheDTO user, ShopHelpInfoTreeSortReq shopHelpInfoTreeSortReq) {

        Long shopId = Optional.ofNullable(shopHelpInfoTreeSortReq.getIdList()).map(idList -> idList.stream().findAny()).flatMap(Function.identity()).map(
                // 查询商城ID
                shopHelpInfoDao::findShopIdById
        ).orElseThrow(() -> new BusinessException(ResponseCodeEnum.COMMODITY_DATA_RECORDS_DON_T_EXIST));

        ShopDO shopDO = Optional.ofNullable(shopDao.getById(shopId)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.MAN_MALL_DOES_NOT_EXIST));

        // 校验数据权限
        checkDataPermissions(user, shopDO);

        // 更新排序
        shopHelpInfoDao.updateSortByIdIn(shopHelpInfoTreeSortReq.getIdList());
    }

}
