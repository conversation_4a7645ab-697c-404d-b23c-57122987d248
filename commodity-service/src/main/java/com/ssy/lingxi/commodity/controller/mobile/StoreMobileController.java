package com.ssy.lingxi.commodity.controller.mobile;

import com.ssy.lingxi.commodity.entity.do_.shop.StoreDO;
import com.ssy.lingxi.commodity.model.req.common.CollectReq;
import com.ssy.lingxi.commodity.model.req.common.MemberIdAndRoleIdReq;
import com.ssy.lingxi.commodity.model.req.member.StoreMobileDataReq;
import com.ssy.lingxi.commodity.model.req.mobile.StoreInCommodityListMobileReq;
import com.ssy.lingxi.commodity.model.req.mobile.StoreMainReq;
import com.ssy.lingxi.commodity.model.resp.mobile.StoreInCommodityListMobileResp;
import com.ssy.lingxi.commodity.model.resp.mobile.StoreListMobileResp;
import com.ssy.lingxi.commodity.model.resp.mobile.StoreMainMobileResp;
import com.ssy.lingxi.commodity.service.mobile.IStoreMobileService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.report.api.model.resp.PopularShopResp;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * mobile - 会员店铺门户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/12/11
 */
@RestController
@RequestMapping(StoreMobileController.PATH_PREFIX)
public class StoreMobileController extends BaseController {

    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/mobile/storeMobile";

    @Resource
    private IStoreMobileService storeMobileService;

    /**
     * 会员店铺主页
     */
    @GetMapping("/memberShopMain")
    public WrapperResp<StoreMainMobileResp> storeMain(@Valid StoreMainReq dto) {
        return WrapperUtil.success(storeMobileService.storeMain(dto, isLogin() ? getSysUser() : null));
    }

    /**
     * 店铺门户列表
     */
    @GetMapping("/memberShopList")
    public WrapperResp<PageDataResp<StoreListMobileResp>> storeList(@Valid StoreMobileDataReq qo) {
        return WrapperUtil.success(storeMobileService.storeList(qo, getHeadersShopId()));
    }

    /**
     * 人气店铺和最新上架三个商品
     */
    @GetMapping("/popularStore")
    public WrapperResp<List<StoreListMobileResp>> popularStore(@RequestParam(value = "provinceCode",defaultValue = "0") String provinceCode, @RequestParam(value = "cityCode",defaultValue = "0") String cityCode) {
        return WrapperUtil.success(storeMobileService.popularStore(getHeadersShopId(),provinceCode,cityCode));
    }

    /**
     * 人气店铺信息
     */
    @GetMapping("/popularStoreMsg")
    public WrapperResp<List<PopularShopResp>> popularStoreMsg() {
        return WrapperUtil.success(storeMobileService.popularStoreMsg(getHeadersShopId()));
    }

    /**
     * 店铺门户包含商品列表
     */
    @PostMapping("/memberShopInCommodityList")
    public WrapperResp<List<StoreInCommodityListMobileResp>> storeInCommodityList(@RequestBody @Valid StoreInCommodityListMobileReq dto) {
        return WrapperUtil.success(storeMobileService.storeInCommodityList(dto));
    }

    /**
     * 根据会员ID和角色ID获取店铺
     */
    @GetMapping("/findByMemberIdAndRoleId")
    public WrapperResp<StoreMainMobileResp> findByMemberIdAndRoleId(@Valid MemberIdAndRoleIdReq dto) {
        return WrapperUtil.success(storeMobileService.findByMemberIdAndRoleId(dto, isLogin() ? getSysUser() : null));
    }

    /**
     * 根据会员ID和角色ID获取店铺
     */
    @GetMapping("/findById")
    public WrapperResp<StoreDO> findById(@Valid CommonIdReq dto) {
        return WrapperUtil.success(storeMobileService.findById(dto));
    }

    /**
     * 收藏列表
     */
    @GetMapping(value = "/collectList")
    public WrapperResp<PageDataResp<StoreDO>> collectList(PageDataReq dto) {
        return WrapperUtil.success(storeMobileService.collectList(dto, getSysUser()));
    }

    /**
     * 收藏/取消收藏
     */
    @PostMapping(value = "/collect")
    public WrapperResp<Void> collect(@RequestBody @Validated CollectReq dto) {
        storeMobileService.collect(dto, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 新入店铺
     */
    @GetMapping("/newAddStore")
    public WrapperResp<PageDataResp<StoreDO>> newAddStore(PageDataReq dto) {
        return WrapperUtil.success(storeMobileService.newAddStore(dto));
    }
}
