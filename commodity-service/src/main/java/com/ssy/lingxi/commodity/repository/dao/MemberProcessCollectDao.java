package com.ssy.lingxi.commodity.repository.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.collect.MemberProcessCollectDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IMemberProcessCollectMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.springframework.stereotype.Repository;

/**
* <AUTHOR>
*/
@Repository
public class MemberProcessCollectDao extends CommonServiceImpl<IMemberProcessCollectMapper, MemberProcessCollectDO> {

    public Boolean existsByProcessIdAndMemberIdAndUserId(Long processId, Long memberId, Long userId) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberProcessCollectDO.class).select(MemberProcessCollectDO::getId)
                        .eq(MemberProcessCollectDO::getProcessId, processId).eq(MemberProcessCollectDO::getMemberId, memberId).eq(MemberProcessCollectDO::getUserId, userId)
        ).map(MemberProcessCollectDO::getId).orElse(0L) > 0;
    }

    public void deleteByProcessIdAndMemberIdAndUserId(Long processId, Long memberId, Long userId) {
        this.remove(
                Wrappers.lambdaQuery(MemberProcessCollectDO.class).eq(MemberProcessCollectDO::getProcessId, processId)
                        .eq(MemberProcessCollectDO::getMemberId, memberId).eq(MemberProcessCollectDO::getUserId, userId)
        );
    }

    public Page<MemberProcessCollectDO> findByMemberIdAndUserIdOrderByCreateTimeDesc(Long memberId, Long userId, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberProcessCollectDO.class).eq(MemberProcessCollectDO::getMemberId, memberId).eq(MemberProcessCollectDO::getUserId, userId)
                        .orderByDesc(MemberProcessCollectDO::getCreateTime)
        );
    }
}




