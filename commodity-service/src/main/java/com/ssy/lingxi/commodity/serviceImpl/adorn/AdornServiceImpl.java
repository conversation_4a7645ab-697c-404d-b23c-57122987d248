package com.ssy.lingxi.commodity.serviceImpl.adorn;

import com.ssy.lingxi.commodity.domain.AdornDM;
import com.ssy.lingxi.commodity.entity.do_.adorn.AdornDO;
import com.ssy.lingxi.commodity.enums.AdornTypeEnum;
import com.ssy.lingxi.commodity.handler.convert.RespConvert;
import com.ssy.lingxi.commodity.model.req.adorn.AdornReq;
import com.ssy.lingxi.commodity.model.req.adorn.ShopAdornSaveReq;
import com.ssy.lingxi.commodity.model.req.adorn.StoreAdornSaveReq;
import com.ssy.lingxi.commodity.model.resp.adorn.AdornResp;
import com.ssy.lingxi.commodity.repository.dao.adorn.AdornDao;
import com.ssy.lingxi.commodity.service.adorn.IAdornService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AdornServiceImpl implements IAdornService {

    private final AdornDao adornDao;

    @Override
    @Transactional
    public Long save(AdornReq adornReq, UserLoginCacheDTO user) {

        AdornDO adornDO = Objects.isNull(adornReq.getAdornId()) ? AdornDM.buildBy(adornReq) : adornDao.getById(adornReq.getAdornId());
        if (Objects.isNull(adornDO)) {
            throw new BusinessException(ResponseCodeEnum.COMMODITY_ADORN_NOT_EXIST);
        }

        adornDO.setAdornContent(adornReq.getAdornContent());
        adornDO.setCategoryAdornContent(adornReq.getCategoryAdornContent());
        //保存装修
        adornDao.saveOrUpdate(adornDO);

        // 因为目前还没有多套装修的需求, 所以需要将其他关联装修置置为未启用(PS: 目前正常也只会有一条数据, 这里仅为了处理意外情况的出现)
        adornDao.updateEnabledByNotIdAndShopIdAndStoreIdAndType(Boolean.FALSE, adornDO.getId(), adornReq.getShopId(), adornReq.getStoreId());

        return adornDO.getId();
    }

    @Override
    public AdornResp find(Long adornId, UserLoginCacheDTO user) {
        AdornDO adornDO = adornDao.getById(adornId);
        return RespConvert.INSTANCE.toAdornResp(adornDO);
    }

    @Override
    public AdornResp findByShopId(Long shopId) {

        Long adornId = Optional.ofNullable(adornDao.findIdFirstByShopIdAndEnabledAndType(
                shopId, Boolean.TRUE, AdornTypeEnum.SHOP.getCode())
        ).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PT_ADORN_NOT_EXISTS));

        AdornDO adornDO = adornDao.getById(adornId);
        return RespConvert.INSTANCE.toAdornResp(adornDO);
    }

    @Override
    public Long shopAdornSave(ShopAdornSaveReq shopAdornSaveReq, UserLoginCacheDTO user) {

        Long adornId = adornDao.findIdFirstByShopIdAndEnabledAndType(shopAdornSaveReq.getShopId(), Boolean.TRUE, AdornTypeEnum.SHOP.getCode());
        if (Objects.nonNull(adornId)) {
            // 已经有了，防止重复调用
            return adornId;
        }

        AdornDO adornDO = AdornDM.buildBy(shopAdornSaveReq);
        // 保存装修关联表
        adornDao.save(adornDO);

        return adornDO.getId();
    }

    @Override
    public Long storeAdornSave(StoreAdornSaveReq storeAdornSaveReq, UserLoginCacheDTO sysUser) {

        Long adornId = adornDao.findIdFirstByShopIdAndStoreIdAndEnabledAndType(storeAdornSaveReq.getShopId(), storeAdornSaveReq.getStoreId(), Boolean.TRUE, AdornTypeEnum.STORE.getCode());
        if (Objects.nonNull(adornId)) {
            // 已经有了，防止重复调用
            return adornId;
        }

        AdornDO adornDO = AdornDM.buildBy(storeAdornSaveReq);
        // 保存装修关联表
        adornDao.save(adornDO);

        return adornDO.getId();
    }

}
