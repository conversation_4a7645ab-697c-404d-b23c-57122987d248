package com.ssy.lingxi.commodity.repository.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.collect.StoreCollectDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IStoreCollectMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.springframework.stereotype.Repository;

/**
* <AUTHOR>
*/
@Repository
public class StoreCollectDao extends CommonServiceImpl<IStoreCollectMapper, StoreCollectDO> {

    public Boolean existsByShopIdAndMemberIdAndUserId(Long shopId, Long memberId, Long userId) {
        return this.getFirst(
                Wrappers.lambdaQuery(StoreCollectDO.class).select(StoreCollectDO::getId).eq(StoreCollectDO::getStoreId, shopId).eq(StoreCollectDO::getMemberId, memberId).eq(StoreCollectDO::getUserId, userId)
        ).map(StoreCollectDO::getId).orElse(0L) > 0;
    }

    public Page<StoreCollectDO> findByMemberIdAndUserIdOrderByCreateTimeDesc(Long memberId, Long userId, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(StoreCollectDO.class).eq(StoreCollectDO::getMemberId, memberId).eq(StoreCollectDO::getUserId, userId)
                        .orderByDesc(StoreCollectDO::getCreateTime)
        );
    }

    public void deleteByShopIdAndMemberIdAndUserId(Long shopId, Long memberId, Long userId) {
        this.remove(
                Wrappers.lambdaQuery(StoreCollectDO.class).eq(StoreCollectDO::getStoreId, shopId).eq(StoreCollectDO::getMemberId, memberId).eq(StoreCollectDO::getUserId, userId)
        );
    }

}




