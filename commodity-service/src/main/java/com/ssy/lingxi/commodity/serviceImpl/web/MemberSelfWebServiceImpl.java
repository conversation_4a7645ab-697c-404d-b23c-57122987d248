package com.ssy.lingxi.commodity.serviceImpl.web;

import com.ssy.lingxi.commodity.entity.bo.AreaBO;
import com.ssy.lingxi.commodity.entity.do_.door.MemberSelfDO;
import com.ssy.lingxi.commodity.entity.do_.door.SeoDO;
import com.ssy.lingxi.commodity.enums.SeoDoorTypeEnum;
import com.ssy.lingxi.commodity.enums.SeoTypeEnum;
import com.ssy.lingxi.commodity.model.req.common.MemberIdReq;
import com.ssy.lingxi.commodity.model.req.web.SaveSelfReq;
import com.ssy.lingxi.commodity.model.resp.web.MemberSelfMainResp;
import com.ssy.lingxi.commodity.repository.dao.MemberSelfDao;
import com.ssy.lingxi.commodity.repository.dao.SeoDao;
import com.ssy.lingxi.commodity.service.web.IMemberSelfWebService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.manage.api.feign.IAreaFeign;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.feign.IMemberLevelRightCreditFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignIdReq;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLrcResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignRegisterQueryResp;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.model.req.MemberReq;
import com.ssy.lingxi.product.api.model.resp.store.StoreResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * web - 会员自营门户 - 业务实现层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/18
 */
@Slf4j
@Service
public class MemberSelfWebServiceImpl implements IMemberSelfWebService {

    @Resource
    private MemberSelfDao memberSelfDao;
    @Resource
    private IMemberLevelRightCreditFeign memberLevelRightCreditControllerFeign;
    @Resource
    private IMemberFeign memberInnerControllerFeign;
    @Resource
    private IAreaFeign IAreaFeign;
    @Resource
    private ICommodityFeign commodityFeign;
    @Resource
    private SeoDao seoDao;


    /**
     * 保存当前登录会员的自营门户
     *
     * @param dto 请求参数
     * @return 操作参数
     */
    @Override
    public void saveCurrMemberSelf(SaveSelfReq dto, UserLoginCacheDTO user) {
        //校验（只有是服务提供者且企业类型是企业会员或个人会员才能创建自营门户, 一个会员只能有一个自营门户。
        if(!user.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            throw new BusinessException("角色类型不是服务提供者, 不能创建自营门户");
        }
        if(!(user.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || user.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))) {
            throw new BusinessException("会员类型不是企业会员或个人会员, 不能创建自营门户");
        }

        //默认省、市编码都为0（所有/所有）
        List<String> provinceCodes = Stream.of("0").collect(toList());
        List<String> cityCodes = Stream.of("0").collect(toList());
        if(dto.getAreaList().stream().noneMatch(a -> a.getProvinceCode().equals("0"))) {
            provinceCodes = dto.getAreaList().stream().map(AreaBO::getProvinceCode).collect(Collectors.toList());
            cityCodes = dto.getAreaList().stream().filter(a -> !a.getCityCode().equals("0")).map(AreaBO::getCityCode).collect(Collectors.toList());

            List<String> feignCodes = dto.getAreaList().stream().filter(a -> a.getCityCode().equals("0")).map(AreaBO::getProvinceCode).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(feignCodes)) {
                WrapperResp<List<String>> cityCodesWrapperResp = IAreaFeign.findCityCodeByProvinceCode(feignCodes);
                if(cityCodesWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode() && !CollectionUtils.isEmpty(cityCodesWrapperResp.getData())) {
                    cityCodes.addAll(cityCodesWrapperResp.getData());
                }
            }
        }

        //调用会员服务 -> 根据会员ID和角色ID获取平台会员的等级、注册年数、信用积分
        MemberFeignReq memberFeignReq = new MemberFeignReq();
        memberFeignReq.setMemberId(user.getMemberId());
        memberFeignReq.setRoleId(user.getMemberRoleId());
        WrapperResp<MemberFeignLrcResp> platformMemberLrcWrapperResp = memberLevelRightCreditControllerFeign.getPlatformMemberLrc(memberFeignReq);
        if(platformMemberLrcWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", platformMemberLrcWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
        }
        //调用会员服务 -> 根据会员Id查询会员注册信息
        MemberFeignIdReq memberFeignIdReq = new MemberFeignIdReq();
        memberFeignIdReq.setMemberId(user.getMemberId());
        WrapperResp<MemberFeignRegisterQueryResp> memberRegisterInfoWrapperResp = memberInnerControllerFeign.getMemberRegisterInfo(memberFeignIdReq);
        if(memberRegisterInfoWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", memberRegisterInfoWrapperResp.getMessage());
        }

        //保存会员自营门户
        MemberSelfDO memberSelf = memberSelfDao.findFirstByMemberId(user.getMemberId()).orElseGet(MemberSelfDO::new);
        memberSelf.setMemberId(user.getMemberId());
        memberSelf.setRoleId(user.getMemberRoleId());
        memberSelf.setAreaList(dto.getAreaList());
        memberSelf.setDescribe(dto.getDescribe());
        memberSelf.setWorkshopPics(dto.getWorkshopPics());
        memberSelf.setHonorPics(dto.getHonorPics());
        memberSelf.setAlbumName(dto.getAlbumName());
        memberSelf.setAlbumUrl(dto.getAlbumUrl());
        memberSelf.setShopId(dto.getShopId());
        memberSelf.setUrl(dto.getUrl());
        memberSelf.setPhone(dto.getPhone());
        memberSelf.setAddress(dto.getAddress());
        memberSelf.setAreas(dto.getAreaList().stream().map(a -> a.getProvince() + "/" + a.getCity()).collect(Collectors.joining("，")));
        memberSelf.setProvincesCodeList(String.join("，", provinceCodes));
        memberSelf.setCityCodeList(String.join("，", cityCodes));
        memberSelf.setMemberName(user.getMemberName());
        memberSelf.setLevelTag(platformMemberLrcWrapperResp.getData().getLevelTag());
        memberSelf.setRegisterYears(platformMemberLrcWrapperResp.getData().getRegisterYears());
        memberSelf.setCreditPoint(platformMemberLrcWrapperResp.getData().getCreditPoint());
        memberSelf.setRegisteredCapital(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisteredCapital() : null);
        memberSelf.setEstablishmentDate(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getEstablishmentDate() : null);
        memberSelf.setBusinessLicence(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getBusinessLicence() : null);
        memberSelf.setRegisterArea(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisterArea() : null);
        memberSelf.setRegisterAddress(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisterAddress() : null);
        memberSelfDao.saveOrUpdate(memberSelf);

    }

    /**
     * 获取当前登录会员的自营门户
     *
     * @return 操作参数
     */
    @Override
    public MemberSelfDO findCurrMemberSelf(UserLoginCacheDTO user) {
        return memberSelfDao.findFirstByMemberId(user.getMemberId()).orElse(null);
    }

    /**
     * 自营门户主页
     *
     * @param dto 请求参数
     * @return 操作参数
     */
    @Override
    public MemberSelfMainResp memberSelfMain(MemberIdReq dto) {
        //根据会员ID获取自营门户
        MemberSelfDO memberSelf = memberSelfDao.findFirstByMemberId(dto.getMemberId()).orElse(null);
        if(memberSelf == null) {
            throw new BusinessException("未配置关于我们相关的信息");
        }

        //拷贝
        MemberSelfMainResp memberSelfMainVO = new MemberSelfMainResp();
        BeanUtils.copyProperties(memberSelf, memberSelfMainVO);

        //调用商品服务 -> 根据会员ID和角色ID集合获取自营门户的主营分类
        MemberReq memberReq = new MemberReq();
        memberReq.setMemberId(memberSelf.getMemberId());
        memberReq.setMemberRoleId(memberSelf.getRoleId());
        Map<String, StoreResp> map = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityAndCategoryByMemberIdAndMemberRoleId(
                Stream.of(memberReq).collect(toList())
        ));
        if(!CollectionUtils.isEmpty(map) && map.get(memberSelfMainVO.getMemberId()+"-"+memberSelfMainVO.getRoleId()) != null) {
            memberSelfMainVO.setCustomerCategoryName(map.get(memberSelfMainVO.getMemberId()+"-"+memberSelfMainVO.getRoleId()).getCustomerCategoryName());
        }

        //获取自营门户SEO
        List<SeoDO> seoList = seoDao.findByDoorTypeAndDoorIdAndStatus(
                SeoDoorTypeEnum.SELF.getCode(), memberSelf.getId(), CommonBooleanEnum.YES.getCode()
        );
        //封装自营门户SEO(首页、关于我们、行情资讯)
        if(!CollectionUtils.isEmpty(seoList)) {
            SeoDO homePage = seoList.stream().filter(a -> a.getType().equals(SeoTypeEnum.HOME_PAGE.getCode())).findFirst().orElse(null);
            SeoDO aboutUs = seoList.stream().filter(a -> a.getType().equals(SeoTypeEnum.ABOUT_US.getCode())).findFirst().orElse(null);
            SeoDO informationUs = seoList.stream().filter(a -> a.getType().equals(SeoTypeEnum.INFORMATION_US.getCode())).findFirst().orElse(null);
            memberSelfMainVO.setHomePage(homePage);
            memberSelfMainVO.setAboutUs(aboutUs);
            memberSelfMainVO.setInformationUs(informationUs);
        }

        return memberSelfMainVO;
    }
}
