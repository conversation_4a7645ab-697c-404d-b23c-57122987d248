package com.ssy.lingxi.commodity.serviceImpl.adorn;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.adorn.SearchAdornDO;
import com.ssy.lingxi.commodity.model.req.adorn.SearchAdornPageReq;
import com.ssy.lingxi.commodity.model.req.adorn.SearchAdornReq;
import com.ssy.lingxi.commodity.model.req.adorn.SearchAdornUpdateReq;
import com.ssy.lingxi.commodity.model.resp.adorn.SearchAdornDetailResp;
import com.ssy.lingxi.commodity.model.resp.adorn.SearchAdornPageResp;
import com.ssy.lingxi.commodity.repository.dao.adorn.SearchAdornDao;
import com.ssy.lingxi.commodity.service.adorn.ISearchAdornService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SearchAdornServiceImpl implements ISearchAdornService {

    private final SearchAdornDao searchAdornDao;

    @Override
    @Transactional
    public Long save(SearchAdornReq adornReq, UserLoginCacheDTO user) {
        SearchAdornDO searchAdornDO=new SearchAdornDO();
        BeanUtils.copyProperties(adornReq,searchAdornDO);
        searchAdornDO.setUserId(user.getUserId());
        searchAdornDO.setUserName(user.getUserName());
        //保存装修
        searchAdornDao.saveOrUpdate(searchAdornDO);
        return searchAdornDO.getId();
    }

    @Override
    public PageDataResp<SearchAdornPageResp> page(SearchAdornPageReq req, UserLoginCacheDTO sysUser) {
        Page<SearchAdornPageResp> adornTopicPageListRespPage = searchAdornDao.pageList(req, sysUser);
        return new PageDataResp<>(adornTopicPageListRespPage.getTotal(), adornTopicPageListRespPage.getRecords());
    }


    @Override
    public SearchAdornDetailResp detail(CommonIdReq req, UserLoginCacheDTO sysUser) {
        SearchAdornDO searchAdornDO = searchAdornDao.getById(req.getId());
        SearchAdornDetailResp resp=new SearchAdornDetailResp();
        BeanUtils.copyProperties(searchAdornDO,resp);
        return resp;
    }

    @Override
    public void update(SearchAdornUpdateReq req, UserLoginCacheDTO user) {
        SearchAdornDO searchAdornDO = searchAdornDao.getById(req.getId());
        if(searchAdornDO==null){
            throw new BusinessException(ResponseCodeEnum.SETTLE_ACCOUNTS_DON_T_EXIST);
        }
        searchAdornDO.setCreateTime(System.currentTimeMillis());
        searchAdornDO.setUserId(user.getUserId());
        searchAdornDO.setUserName(user.getUserName());
        searchAdornDO.setAdornContent(req.getAdornContent());
        searchAdornDO.setName(req.getName());
        searchAdornDO.setNav(req.getNav());
        //保存装修
        searchAdornDao.saveOrUpdate(searchAdornDO);
    }

    @Override
    public void batchDelete(CommonIdListReq req, UserLoginCacheDTO sysUser) {
        searchAdornDao.updateIsDeletedBy(req.getIdList(),1);
    }


}
