package com.ssy.lingxi.commodity.controller.support;

import com.ssy.lingxi.commodity.api.model.resp.support.LanguageInfoResp;
import com.ssy.lingxi.commodity.api.model.resp.support.LanguageResp;
import com.ssy.lingxi.commodity.model.req.support.LanguageListDataReq;
import com.ssy.lingxi.commodity.model.req.support.LanguageUpdateStatusReq;
import com.ssy.lingxi.commodity.service.support.ILanguageService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 语言
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/5
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(LanguageController.PATH_PREFIX)
public class LanguageController {

    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/language/";

    private final ILanguageService languageService;

    /**
     * 列表查询
     */
    @GetMapping(value = "getLanguageList")
    public WrapperResp<List<LanguageInfoResp>> getLanguageList() {
        return WrapperUtil.success(languageService.getLanguageList());
    }

    /**
     * 分页查询
     * @param languageListReq 参数
     */
    @GetMapping(value = "getLanguagePage")
    public WrapperResp<PageDataResp<LanguageResp>> getLanguagePage(LanguageListDataReq languageListReq) {
        return WrapperUtil.success(languageService.getLanguagePage(languageListReq));
    }

    /**
     * 启用/停用
     * @param languageUpdateStatusReq 参数
     */
    @PostMapping(value = "updateStatus")
    public WrapperResp<Boolean> updateStatus(@RequestBody @Valid LanguageUpdateStatusReq languageUpdateStatusReq) {
        return WrapperUtil.success(languageService.updateStatus(languageUpdateStatusReq));
    }
}
