package com.ssy.lingxi.commodity.repository.dao.support;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ssy.lingxi.commodity.api.model.resp.support.UnitRep;
import com.ssy.lingxi.commodity.entity.do_.support.UnitDO;
import com.ssy.lingxi.commodity.repository.mapper.support.IUnitMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class UnitDao extends ServiceImpl<IUnitMapper, UnitDO> {

    public Page<UnitRep> findPageByNameLikeOrderByIdDesc(PageDataReq pageVO, String name) {
        Page<UnitRep> page = Page.of(pageVO.getCurrent(), pageVO.getPageSize());
        return getBaseMapper().findPageByNameLikeOrderByIdDesc(page, name);
    }

    public List<UnitRep> findByStatus(Boolean status) {
        return getBaseMapper().findByStatus(status);
    }

    public List<UnitDO> findByIdList(List<Long> unitIdList) {
        return getBaseMapper().selectList(Wrappers.<UnitDO>lambdaQuery().in(UnitDO::getId, unitIdList));
    }
}
