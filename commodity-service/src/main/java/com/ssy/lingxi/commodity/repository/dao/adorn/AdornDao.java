package com.ssy.lingxi.commodity.repository.dao.adorn;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ssy.lingxi.commodity.entity.do_.adorn.AdornDO;
import com.ssy.lingxi.commodity.enums.AdornTypeEnum;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IAdornMapper;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Repository
public class AdornDao extends CommonServiceImpl<IAdornMapper, AdornDO> {

    /**
     * 通过商城ID查询一条已启用的装修集<br/>
     * 一个商城只能有一个已启用的装修集
     *
     * @param enabled 已启用的装修集
     */
    public Long findIdFirstByShopIdAndEnabledAndType(Long shopId, Boolean enabled, Integer type) {
        return this.getFirst(
                Wrappers.lambdaQuery(AdornDO.class).select(AdornDO::getId)
                        .eq(AdornDO::getShopId, shopId)
                        .eq(AdornDO::getEnabled, enabled)
                        .eq(AdornDO::getType, type)
        ).map(AdornDO::getId).orElse(null);
    }

    /**
     * 通过商城ID和店铺ID查询一条已启用的装修集<br/>
     * 一个商城里的一个店铺只能有一个已启用的装修集
     *
     * @param enabled 已启用的装修集
     */
    public Long findIdFirstByShopIdAndStoreIdAndEnabledAndType(Long shopId, Long storeId, Boolean enabled, Integer type) {
        return this.getFirst(
                Wrappers.lambdaQuery(AdornDO.class).select(AdornDO::getId)
                        .eq(AdornDO::getShopId, shopId)
                        .eq(AdornDO::getStoreId, storeId)
                        .eq(AdornDO::getEnabled, enabled)
                        .eq(AdornDO::getType, type)
        ).map(AdornDO::getId).orElse(null);
    }

    /**
     * 更新数据
     */
    public int updateEnabledByNotIdAndShopIdAndStoreIdAndType(Boolean enabled, Long id, Long shopId, Long storeId) {
        return getBaseMapper().update(
                Wrappers.lambdaUpdate(AdornDO.class)
                        .set(AdornDO::getEnabled, enabled)
                        .ne(AdornDO::getId, id)
                        .eq(AdornDO::getShopId, shopId)
                        .eq(Objects.nonNull(storeId), AdornDO::getStoreId, storeId)
                        .eq(AdornDO::getType, Objects.nonNull(storeId) ? AdornTypeEnum.STORE.getCode() : AdornTypeEnum.SHOP.getCode())
        );
    }
}




