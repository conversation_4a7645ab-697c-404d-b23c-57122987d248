package com.ssy.lingxi.commodity.repository.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.door.MemberProcessDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IMemberProcessMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public class MemberProcessDao extends CommonServiceImpl<IMemberProcessMapper, MemberProcessDO> {

    public Page<MemberProcessDO> findAllByIdNotInAndMemberNameContains(List<Long> idList, String memberName, PageDataReq pageDataReq) {
        return this.page(
                Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberProcessDO.class).notIn(MemberProcessDO::getId, idList).like(MemberProcessDO::getMemberName, "%" + memberName + "%")
        );
    }

    public Page<MemberProcessDO> findAllByIdNotIn(List<Long> idList, PageDataReq pageDataReq) {
        return this.page(
                Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberProcessDO.class).notIn(MemberProcessDO::getId, idList)
        );
    }

    public Page<MemberProcessDO> findAllByIdIn(List<Long> idList, PageDataReq pageDataReq) {
        return this.page(
                Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberProcessDO.class).in(MemberProcessDO::getId, idList)
        );
    }

    public Optional<MemberProcessDO> findFirstByMemberIdAndRoleId(Long memberId, Long roleId) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberProcessDO.class).eq(MemberProcessDO::getMemberId, memberId).eq(MemberProcessDO::getRoleId, roleId)
        );
    }

    public List<MemberProcessDO> findByRoleId(Long roleId) {
        return this.list(
                Wrappers.lambdaQuery(MemberProcessDO.class).eq(MemberProcessDO::getRoleId, roleId)
        );
    }

    public List<MemberProcessDO> findByYearProcessAmountAndProvincesCodeListLikeOrEqAndCityCodeListLikeOrEqOrderByCreateTimeDesc(Integer yearProcessAmount, String provinceCode, String provinceCodeOr, String cityCode, String cityCodeOr) {
        return this.list(
                Wrappers.lambdaQuery(MemberProcessDO.class).eq(Objects.nonNull(yearProcessAmount), MemberProcessDO::getYearProcessAmount, yearProcessAmount)
                        .and(StringUtils.isNotBlank(provinceCode), wrapper -> wrapper.like(MemberProcessDO::getProvincesCodeList, "%" + provinceCode + "%").or().eq(MemberProcessDO::getProvincesCodeList, provinceCodeOr))
                        .and(StringUtils.isNotBlank(cityCode), wrapper -> wrapper.like(MemberProcessDO::getCityCodeList, "%" + cityCode + "%").or().eq(MemberProcessDO::getCityCodeList, cityCodeOr))
                        .orderByDesc(MemberProcessDO::getCreateTime)
        );
    }

    public List<MemberProcessDO> findByIdInAndProvinceCodeLikeOrEqAndCityCodeLikeOrEq(List<Long> idList, String provinceCode, String provinceCodeOr, String cityCode, String cityCodeOr) {
        return this.list(
                Wrappers.lambdaQuery(MemberProcessDO.class).in(CollUtil.isNotEmpty(idList), MemberProcessDO::getId, idList)
                        .and(StringUtils.isNotBlank(provinceCode), wrapper -> wrapper.like(MemberProcessDO::getProvincesCodeList, "%" + provinceCode + "%").or().eq(MemberProcessDO::getProvincesCodeList, provinceCodeOr))
                        .and(StringUtils.isNotBlank(cityCode), wrapper -> wrapper.like(MemberProcessDO::getCityCodeList, "%" + cityCode + "%").or().eq(MemberProcessDO::getCityCodeList, cityCodeOr))
        );
    }
}




