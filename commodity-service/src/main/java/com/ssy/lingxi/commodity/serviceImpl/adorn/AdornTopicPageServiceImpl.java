package com.ssy.lingxi.commodity.serviceImpl.adorn;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.domain.AdornDM;
import com.ssy.lingxi.commodity.entity.do_.adorn.AdornTopicPageDO;
import com.ssy.lingxi.commodity.handler.convert.RespConvert;
import com.ssy.lingxi.commodity.model.req.adorn.AdornTopicPageListReq;
import com.ssy.lingxi.commodity.model.req.adorn.AdornTopicPageReq;
import com.ssy.lingxi.commodity.model.req.adorn.AdornTopicPageUpdateReq;
import com.ssy.lingxi.commodity.model.resp.adorn.AdornTopicPageDetailResp;
import com.ssy.lingxi.commodity.model.resp.adorn.AdornTopicPageListResp;
import com.ssy.lingxi.commodity.repository.dao.adorn.AdornTopicPageDao;
import com.ssy.lingxi.commodity.service.adorn.IAdornTopicPageService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 装修专题页
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AdornTopicPageServiceImpl implements IAdornTopicPageService {

    private final AdornTopicPageDao adornTopicPageDao;

    @Override
    @Transactional
    public Long save(AdornTopicPageReq adornTopicPageReq, UserLoginCacheDTO user) {
        AdornTopicPageDO adornTopicPageDO = Objects.isNull(adornTopicPageReq.getId()) ?
                AdornDM.buildBy(adornTopicPageReq, user) : adornTopicPageDao.getById(adornTopicPageReq.getId());
        if (Objects.isNull(adornTopicPageDO)) {
            throw new BusinessException(ResponseCodeEnum.COMMODITY_ADORN_NOT_EXIST);
        }

        AdornDM.updateBy(adornTopicPageReq, adornTopicPageDO);
        adornTopicPageDao.saveOrUpdate(adornTopicPageDO);

        return adornTopicPageDO.getId();
    }

    @Override
    public void update(AdornTopicPageUpdateReq req, UserLoginCacheDTO user) {

        // 校验专题页是否属于当前用户
        if (!adornTopicPageDao.findIdByIdAndMemberIdAndMemberRoleId(req.getId(), user.getMemberId(), user.getMemberRoleId()).isPresent()) {
            throw new BusinessException(ResponseCodeEnum.COMMODITY_ADORN_TOPIC_PAGE_NOT_EXIST);
        }

        // storeId不为空时, shopId是必须的
        if (Objects.nonNull(req.getStoreId()) && Objects.isNull(req.getShopId())) {
            throw new BusinessException(ResponseCodeEnum.COMMODITY_ADORN_TOPIC_PAGE_STORE_ID_NOT_EMPTY_SHOP_ID_NOT_EMPTY_TOO);
        }

        adornTopicPageDao.updateBy(req);
    }

    @Override
    public AdornTopicPageDetailResp find(Long id, UserLoginCacheDTO user) {
        AdornTopicPageDO adornTopicPageDO = adornTopicPageDao.getById(id);

        return RespConvert.INSTANCE.toAdornTopicPageResp(adornTopicPageDO);
    }

    @Override
    public PageDataResp<AdornTopicPageListResp> pageList(AdornTopicPageListReq adornTopicPageListReq, UserLoginCacheDTO user) {
        Page<AdornTopicPageListResp> adornTopicPageListRespPage = adornTopicPageDao.pageList(adornTopicPageListReq, user);
        return new PageDataResp<>(adornTopicPageListRespPage.getTotal(), adornTopicPageListRespPage.getRecords());
    }

    @Override
    public void delete(Long id, UserLoginCacheDTO user) {

        // 校验专题页是否属于当前用户
        if (!adornTopicPageDao.findIdByIdAndMemberIdAndMemberRoleId(id, user.getMemberId(), user.getMemberRoleId()).isPresent()) {
            throw new BusinessException(ResponseCodeEnum.COMMODITY_ADORN_TOPIC_PAGE_NOT_EXIST);
        }

        // 软删除
        adornTopicPageDao.updateIsDeletedBy(id, 1);
    }

}
