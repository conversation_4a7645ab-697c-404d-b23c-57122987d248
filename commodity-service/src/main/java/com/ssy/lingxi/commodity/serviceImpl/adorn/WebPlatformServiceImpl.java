package com.ssy.lingxi.commodity.serviceImpl.adorn;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.bo.CategoryBO;
import com.ssy.lingxi.commodity.entity.bo.WebPlatformBO;
import com.ssy.lingxi.commodity.entity.do_.adorn.AdornMainPortalDO;
import com.ssy.lingxi.commodity.entity.do_.door.MemberLogisticsDO;
import com.ssy.lingxi.commodity.entity.do_.door.MemberProcessDO;
import com.ssy.lingxi.commodity.entity.do_.shop.StoreDO;
import com.ssy.lingxi.commodity.enums.SelectStatusEnum;
import com.ssy.lingxi.commodity.model.req.adorn.WebPlatformAdornReq;
import com.ssy.lingxi.commodity.model.req.web.RecommendDataReq;
import com.ssy.lingxi.commodity.model.resp.adorn.CommodityResp;
import com.ssy.lingxi.commodity.model.resp.adorn.LogisticsResp;
import com.ssy.lingxi.commodity.model.resp.adorn.ProcessResp;
import com.ssy.lingxi.commodity.repository.dao.MemberLogisticsDao;
import com.ssy.lingxi.commodity.repository.dao.MemberProcessDao;
import com.ssy.lingxi.commodity.repository.dao.adorn.AdornMainPortalDao;
import com.ssy.lingxi.commodity.repository.dao.shop.StoreDao;
import com.ssy.lingxi.commodity.service.adorn.IWebPlatformService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.product.PriceTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.feign.ITemplateFeign;
import com.ssy.lingxi.product.api.model.req.MemberReq;
import com.ssy.lingxi.product.api.model.req.TemplateCommoditySearchReq;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsBrandResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.api.model.resp.store.StoreResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * 装修 - WEB平台首页装修 - 业务实现层
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/04/20
 */
@Slf4j
@Service
public class WebPlatformServiceImpl implements IWebPlatformService {

    @Resource
    private AdornMainPortalDao adornMainPortalDao;
    @Resource
    private ITemplateFeign templateFeign;
    @Resource
    private MemberLogisticsDao memberLogisticsDao;
    @Resource
    private MemberProcessDao memberProcessDao;
    @Resource
    private ICommodityFeign commodityFeign;
    @Resource
    private StoreDao storeDao;

    /**
     * 保存WEB平台首页装修
     *
     * @param webPlatformAdornReq 请求参数
     */
    @Override
    public void save(WebPlatformAdornReq webPlatformAdornReq, UserLoginCacheDTO sysUser) {
        Long adornId = webPlatformAdornReq.getAdornId();

        AdornMainPortalDO adornMainPortalDO = adornMainPortalDao.findFirstByAdornId(adornId).orElseGet(AdornMainPortalDO::new);
        adornMainPortalDO.setAdornId(adornId);
        adornMainPortalDO.setList(webPlatformAdornReq.getList());
        adornMainPortalDao.saveOrUpdate(adornMainPortalDO);
    }

    /**
     * 获取WEB平台首页装修
     */
    @Override
    public List<WebPlatformBO> find(Long adornId) {

        return adornMainPortalDao.findFirstByAdornId(adornId).map(AdornMainPortalDO::getList).orElse(null);
    }

    /**
     * 获取商品列表
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<CommodityResp> findCommodityList(RecommendDataReq dto) {
        //拿到商品ID集合
        List<Long> commodityIdList = dto.getCommodityIdList() != null && CollUtil.isNotEmpty(dto.getCommodityIdList()) ? dto.getCommodityIdList() : null;

        /*
            调用搜索服务 -> 查询商品信息接口:
               0.未选择 根据商品ID查出不包含的商品
               1.已选择 根据商品ID查出包含的商品
               2.所有  查出所有
        */
        WrapperResp<PageDataResp<EsCommodityResp>> wrapperResp = null;
        if (dto.getType().equals(SelectStatusEnum.UNSELECTED.getCode())) {
            TemplateCommoditySearchReq request = new TemplateCommoditySearchReq();
            request.setCurrent(dto.getCurrent());
            request.setPageSize(dto.getPageSize());
            request.setShopId(dto.getShopId());
            request.setCategoryId(dto.getCategoryId());
            request.setBrandId(dto.getBrandId());
            request.setMemberName(dto.getSupplyName());
            request.setName(dto.getCommodityName());
            request.setIdNotInList(commodityIdList);
            request.setPriceTypeList(Stream.of(PriceTypeEnum.CASH.getCode()).collect(Collectors.toList()));
            wrapperResp = templateFeign.searchCommodityList(request);
        }
        if (dto.getType().equals(SelectStatusEnum.SELECTED.getCode())) {
            if (commodityIdList == null) {
                return new PageDataResp<>(0L, new ArrayList<>());
            }
            TemplateCommoditySearchReq request = new TemplateCommoditySearchReq();
            request.setCurrent(dto.getCurrent());
            request.setPageSize(dto.getPageSize());
            request.setShopId(dto.getShopId());
            request.setCategoryId(dto.getCategoryId());
            request.setBrandId(dto.getBrandId());
            request.setMemberName(dto.getSupplyName());
            request.setName(dto.getCommodityName());
            request.setIdInList(commodityIdList);
            request.setPriceTypeList(Stream.of(PriceTypeEnum.CASH.getCode()).collect(Collectors.toList()));
            wrapperResp = templateFeign.searchCommodityList(request);
        }

        //校验返回结果
        if (wrapperResp == null || wrapperResp.getData() == null || CollUtil.isEmpty(wrapperResp.getData().getData())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //拼装
        return new PageDataResp<>(wrapperResp.getData().getTotalCount(), wrapperResp.getData().getData().stream().map(commoditySearchResp -> {
            CommodityResp commodityResp = new CommodityResp();
            commodityResp.setCommodityId(commoditySearchResp.getId());
            commodityResp.setCommodityPicUrl(commoditySearchResp.getMainPic());
            commodityResp.setCommodityName(commoditySearchResp.getName());
            commodityResp.setCommoditySlogan(commoditySearchResp.getSlogan());
            commodityResp.setCommodityPoints(commoditySearchResp.getSellingPoint());
            commodityResp.setCategoryName(commoditySearchResp.getCustomerCategoryName());
            commodityResp.setBrandName(commoditySearchResp.getBrandName());
            commodityResp.setPriceRange(commoditySearchResp.getMin() + "~" + commoditySearchResp.getMax());
            commodityResp.setSupplier(commoditySearchResp.getMemberName());
            commodityResp.setPriceType(commoditySearchResp.getPriceType());
            commodityResp.setShopId(commoditySearchResp.getStoreId());
            commodityResp.setMemberId(commoditySearchResp.getMemberId());
            commodityResp.setMemberRoleId(commoditySearchResp.getMemberRoleId());
            return commodityResp;
        }).collect(Collectors.toList()));
    }

    /**
     * 获取品牌列表
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<com.ssy.lingxi.commodity.model.resp.adorn.BrandResp> findBrandList(RecommendDataReq dto) {
        //拿到品牌ID集合
        List<Long> brandIds = dto.getBrandIdList() != null && CollUtil.isNotEmpty(dto.getBrandIdList()) ? dto.getBrandIdList() : null;

        /*
            调用搜索服务 -> 查询品牌信息接口:
               0.未选择 根据品牌ID查出不包含的品牌
               1.已选择 根据品牌ID查出包含的品牌
               2.所有  查出所有
        */
        WrapperResp<PageDataResp<EsBrandResp>> wrapperResp = null;
        if (dto.getType().equals(SelectStatusEnum.UNSELECTED.getCode())) {
            wrapperResp = templateFeign.getBrandList(dto.getCurrent(), dto.getPageSize(), dto.getShopId(), null, null, null, null, null, dto.getBrandName(), null, brandIds);
        }
        if (dto.getType().equals(SelectStatusEnum.SELECTED.getCode())) {
            if (brandIds == null) {
                return new PageDataResp<>(0L, new ArrayList<>());
            }
            wrapperResp = templateFeign.getBrandList(dto.getCurrent(), dto.getPageSize(), dto.getShopId(), null, null, null, null, null, null, brandIds, null);
        }
        if (dto.getType().equals(SelectStatusEnum.ALL.getCode())) {
            wrapperResp = templateFeign.getBrandList(dto.getCurrent(), dto.getPageSize(), null, null, null, null, null, null, null, null, null);
        }

        //校验返回结果
        if (wrapperResp == null || wrapperResp.getData() == null || CollUtil.isEmpty(wrapperResp.getData().getData())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //拼装
        return new PageDataResp<>(wrapperResp.getData().getTotalCount(), wrapperResp.getData().getData().stream().map(brand -> {
            com.ssy.lingxi.commodity.model.resp.adorn.BrandResp brandVO = new com.ssy.lingxi.commodity.model.resp.adorn.BrandResp();
            brandVO.setBrandId(brand.getId());
            brandVO.setBrandLogo(brand.getLogoUrl());
            brandVO.setBrandName(brand.getName());
            return brandVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 获取店铺列表
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<com.ssy.lingxi.commodity.model.resp.adorn.StoreResp> findStoreList(RecommendDataReq req) {
        if (req.getType().equals(SelectStatusEnum.UNSELECTED.getCode())) {

            Page<StoreDO> storeDOPage = storeDao.findStore(req);

            long totalCount = storeDOPage.getTotal();
            List<StoreDO> storeDOList = storeDOPage.getRecords();

            if (CollUtil.isEmpty(storeDOList)) {
                return new PageDataResp<>(0L, new ArrayList<>());
            }

            //调用商品服务 -> 根据会员ID和角色ID集合获取店铺的主营分类
            Map<String, StoreResp> map = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityAndCategoryByMemberIdAndMemberRoleId(
                    storeDOList.stream().map(a -> {
                        MemberReq memberReq = new MemberReq();
                        memberReq.setMemberId(a.getMemberId());
                        memberReq.setMemberRoleId(a.getRoleId());
                        return memberReq;
                    }).collect(toList())
            ));

            return new PageDataResp<>(totalCount, storeDOList.stream().map(a -> {
                com.ssy.lingxi.commodity.model.resp.adorn.StoreResp storeVO = new com.ssy.lingxi.commodity.model.resp.adorn.StoreResp();
                BeanUtils.copyProperties(a, storeVO);
                if (!CollectionUtils.isEmpty(map) && map.get(a.getMemberId() + "-" + a.getRoleId()) != null) {
                    storeVO.setMainCategory(map.get(a.getMemberId() + "-" + a.getRoleId()).getCustomerCategoryName());
                }
                return storeVO;
            }).collect(Collectors.toList()));

        }
        if (req.getType().equals(SelectStatusEnum.SELECTED.getCode())) {
            if (CollUtil.isEmpty(req.getStoreIdList())) {
                return new PageDataResp<>(0L, new ArrayList<>());
            }
            Page<StoreDO> storeDOPage = storeDao.findStore(req);

            long totalCount = storeDOPage.getTotal();
            List<StoreDO> storeDOList = storeDOPage.getRecords();
            if (CollUtil.isEmpty(storeDOList)) {
                return new PageDataResp<>(0L, new ArrayList<>());
            }
            List<StoreDO> list = storeDOList.stream().sorted(Comparator.comparingInt(a -> req.getStoreIdList().indexOf(a.getId()))).collect(toList());
            //调用商品服务 -> 根据会员ID和角色ID集合获取店铺的主营分类
            Map<String, StoreResp> map = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityAndCategoryByMemberIdAndMemberRoleId(
                    list.stream().map(a -> {
                        MemberReq memberReq = new MemberReq();
                        memberReq.setMemberId(a.getMemberId());
                        memberReq.setMemberRoleId(a.getRoleId());
                        return memberReq;
                    }).collect(toList())
            ));

            return new PageDataResp<>(totalCount, list.stream().map(a -> {
                com.ssy.lingxi.commodity.model.resp.adorn.StoreResp storeVO = new com.ssy.lingxi.commodity.model.resp.adorn.StoreResp();
                BeanUtils.copyProperties(a, storeVO);
                if (!CollectionUtils.isEmpty(map) && map.get(a.getMemberId() + "-" + a.getRoleId()) != null) {
                    storeVO.setMainCategory(map.get(a.getMemberId() + "-" + a.getRoleId()).getCustomerCategoryName());
                }
                return storeVO;
            }).collect(Collectors.toList()));
        }

        return new PageDataResp<>(0L, new ArrayList<>());
    }

    /**
     * 获取物流列表
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<LogisticsResp> findLogisticsList(RecommendDataReq req) {
        if (req.getType().equals(SelectStatusEnum.UNSELECTED.getCode())) {
            Page<MemberLogisticsDO> page;
            if (CollUtil.isNotEmpty(req.getLogisticsIdList())) {
                page = StringUtils.isNotBlank(req.getMemberName()) ? memberLogisticsDao.findAllByIdNotInAndMemberNameContains(req.getLogisticsIdList(), req.getMemberName(), req) : memberLogisticsDao.findAllByIdNotIn(req.getLogisticsIdList(), req);
            } else {
                page = memberLogisticsDao.page(Page.of(req.getCurrent(), req.getPageSize()));
            }
            List<MemberLogisticsDO> memberLogisticsDOList = page.getRecords();
            if (CollUtil.isEmpty(memberLogisticsDOList)) {
                return new PageDataResp<>(0L, new ArrayList<>());
            }
            return new PageDataResp<>(page.getTotal(), page.getRecords().stream().map(a -> {
                LogisticsResp logisticsVO = new LogisticsResp();
                BeanUtils.copyProperties(a, logisticsVO);
                if (!CollectionUtils.isEmpty(a.getMainBusiness())) {
                    logisticsVO.setMainBusiness(StringUtils.join(a.getMainBusiness(), "|"));
                }
                return logisticsVO;
            }).collect(Collectors.toList()));

        }
        if (req.getType().equals(SelectStatusEnum.SELECTED.getCode())) {
            if (CollUtil.isEmpty(req.getLogisticsIdList())) {
                return new PageDataResp<>(0L, new ArrayList<>());
            }
            Page<MemberLogisticsDO> page = memberLogisticsDao.findAllByIdIn(req.getLogisticsIdList(), req);
            List<MemberLogisticsDO> list = page.getRecords().stream().sorted(Comparator.comparingInt(a -> req.getLogisticsIdList().indexOf(a.getId()))).collect(toList());

            return new PageDataResp<>(page.getTotal(), list.stream().map(a -> {
                LogisticsResp logisticsVO = new LogisticsResp();
                BeanUtils.copyProperties(a, logisticsVO);
                if (!CollectionUtils.isEmpty(a.getMainBusiness())) {
                    logisticsVO.setMainBusiness(StringUtils.join(a.getMainBusiness(), "|"));
                }
                return logisticsVO;
            }).collect(Collectors.toList()));
        }

        return new PageDataResp<>(0L, new ArrayList<>());
    }

    /**
     * 获取加工列表
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<ProcessResp> findProcessList(RecommendDataReq req) {
        if (req.getType().equals(SelectStatusEnum.UNSELECTED.getCode())) {
            Page<MemberProcessDO> page;
            if (CollUtil.isNotEmpty(req.getProcessIdList())) {
                page = StringUtils.isNotBlank(req.getMemberName()) ? memberProcessDao.findAllByIdNotInAndMemberNameContains(req.getProcessIdList(), req.getMemberName(), req) : memberProcessDao.findAllByIdNotIn(req.getProcessIdList(), req);
            } else {
                page = memberProcessDao.page(Page.of(req.getCurrent(), req.getPageSize()));
            }
            List<MemberProcessDO> processes = page.getRecords();
            if (CollUtil.isEmpty(processes)) {
                return new PageDataResp<>(0L, new ArrayList<>());
            }

            return new PageDataResp<>(page.getTotal(), page.getRecords().stream().map(a -> {
                ProcessResp processVO = new ProcessResp();
                BeanUtils.copyProperties(a, processVO);
                List<String> firstNameList = a.getCategoryList().stream().map(CategoryBO::getFirstName).collect(toList());
                if (!CollectionUtils.isEmpty(firstNameList)) {
                    processVO.setCategoryBOList(StringUtils.join(firstNameList, "|"));
                }
                return processVO;
            }).collect(Collectors.toList()));

        }
        if (req.getType().equals(SelectStatusEnum.SELECTED.getCode())) {
            if (CollUtil.isEmpty(req.getProcessIdList())) {
                return new PageDataResp<>(0L, new ArrayList<>());
            }
            Page<MemberProcessDO> page = memberProcessDao.findAllByIdIn(req.getProcessIdList(), req);
            List<MemberProcessDO> list = page.getRecords().stream().sorted(Comparator.comparingInt(a -> req.getProcessIdList().indexOf(a.getId()))).collect(toList());

            return new PageDataResp<>(page.getTotal(), list.stream().map(a -> {
                ProcessResp processVO = new ProcessResp();
                BeanUtils.copyProperties(a, processVO);
                List<String> firstNameList = a.getCategoryList().stream().map(CategoryBO::getFirstName).collect(toList());
                if (!CollectionUtils.isEmpty(firstNameList)) {
                    processVO.setCategoryBOList(StringUtils.join(firstNameList, "|"));
                }
                return processVO;
            }).collect(Collectors.toList()));
        }

        return new PageDataResp<>(0L, new ArrayList<>());
    }
}
