package com.ssy.lingxi.commodity.repository.dao.support;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ssy.lingxi.commodity.entity.do_.support.UnitNameDO;
import com.ssy.lingxi.commodity.repository.mapper.support.IUnitNameMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Repository
public class UnitNameDao extends ServiceImpl<IUnitNameMapper, UnitNameDO> {

    public boolean existsByUnitIdNotAndLanguageAndName(Long unitId, String language, String value) {
        return this.exists(
                Wrappers.lambdaQuery(UnitNameDO.class)
                        .ne(Objects.nonNull(unitId), UnitNameDO::getUnitId, unitId)
                        .eq(StringUtils.isNotBlank(language), UnitNameDO::getLanguage, language)
                        .eq(StringUtils.isNotBlank(value), UnitNameDO::getName, value)
        );
    }

    public boolean deleteByUnitId(Long unitId) {
        return this.remove(
                Wrappers.lambdaQuery(UnitNameDO.class)
                        .eq(UnitNameDO::getUnitId, unitId)
        );
    }

    public List<UnitNameDO> getByUnitId(Long unitId) {
        return this.list(
                Wrappers.lambdaQuery(UnitNameDO.class)
                        .eq(UnitNameDO::getUnitId, unitId)
        );
    }

    public List<UnitNameDO> getByUnitIdList(List<Long> unitIdList) {
        return this.list(
                Wrappers.lambdaQuery(UnitNameDO.class).in(CollUtil.isNotEmpty(unitIdList), UnitNameDO::getUnitId, unitIdList)
        );
    }

}
