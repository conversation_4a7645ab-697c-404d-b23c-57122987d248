package com.ssy.lingxi.commodity.serviceImpl.mobile;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.commodity.entity.do_.adorn.AdornDO;
import com.ssy.lingxi.commodity.handler.convert.RespConvert;
import com.ssy.lingxi.commodity.model.req.common.MemberIdAndRoleIdAndAdornIdReq;
import com.ssy.lingxi.commodity.model.resp.mobile.MobileCategoryResp;
import com.ssy.lingxi.commodity.repository.dao.adorn.AdornDao;
import com.ssy.lingxi.commodity.service.mobile.ICategoryMobileService;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.feign.ITemplateFeign;
import com.ssy.lingxi.product.api.model.resp.CategoryBaseResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0.0
 * mobile - 品类 - 业务实现层
 * @since 2021/09/02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryMobileServiceImpl implements ICategoryMobileService {

    private final AdornDao adornDao;
    private final ITemplateFeign templateFeign;

    /**
     * APP企业商城首页（B端、C端）联营商城品类
     */
    @Override
    public List<MobileCategoryResp> enterpriseCategory(Long shopId, Long adornId) {

        AdornDO adornDO = adornDao.getById(adornId);

        //获取装修对应的品类导航装修内容里的所有一级平台品类
        List<Long> categoryIdList = Optional.ofNullable(adornDO).map(AdornDO::getCategoryAdornContent).map(JSONUtil::parseObj).map(o -> o.getJSONArray("category")).orElseGet(JSONArray::new).stream().map(o -> (JSONObject) o).filter(
                // 过滤出visible为true的数据
                o -> Boolean.TRUE.equals(o.getBool("visible"))
        ).map(o -> o.getLong("id")).collect(Collectors.toList());

        //调用搜索服务 -> 获取所有一级平台品类
        List<CategoryBaseResp> categoryBaseRespList = WrapperUtil.getDataOrThrow(templateFeign.getFirstCategoryListByMemberId(shopId, null, null));

        //组装
        return Optional.ofNullable(categoryBaseRespList).orElseGet(ArrayList::new).stream().map(
                c -> RespConvert.INSTANCE.toMobileCategoryResp(c, categoryIdList.contains(c.getId()))
        ).collect(Collectors.toList());
    }

    /**
     * 校验平台品类是否存在于品类导航页
     *
     * @return 操作结果
     */
    @Override
    public Boolean checkCategory(Long adornId, Long categoryId) {

        //获取装修对应的品类导航装修内容里的所有一级平台品类，然后跟参数平台品类ID进行匹配
        AdornDO adornDO = adornDao.getById(adornId);

        return Optional.ofNullable(adornDO).map(AdornDO::getCategoryAdornContent).map(JSONUtil::parseObj).map(o -> o.getJSONArray("category")).orElseGet(JSONArray::new).stream().map(o -> (JSONObject) o).anyMatch(
                // 任意匹配 visible为true 并且 id相等 的数据
                o -> Boolean.TRUE.equals(o.getBool("visible")) && categoryId.equals(o.getLong("id"))
        );
    }

    /**
     * APP自营商城首页会员品类
     */
    @Override
    public List<MobileCategoryResp> selfMemberCategory(Long shopId, MemberIdAndRoleIdAndAdornIdReq memberIdAndRoleIdAndAdornIdReq) {

        //获取装修对应的品类导航装修内容里的所有一级商家品类
        AdornDO adornDO = adornDao.getById(memberIdAndRoleIdAndAdornIdReq.getAdornId());

        List<Long> categoryIdList = Optional.ofNullable(adornDO).map(AdornDO::getCategoryAdornContent).map(JSONUtil::parseObj).map(o -> o.getJSONArray("category")).orElseGet(JSONArray::new).stream().map(o -> (JSONObject) o).filter(
                // 过滤出 visible为true 的数据
                o -> Boolean.TRUE.equals(o.getBool("visible"))
        ).map(o -> o.getLong("id")).collect(Collectors.toList());

        //调用搜索服务 -> 获取所有一级商家品类
        List<CategoryBaseResp> categoryBaseRespList = WrapperUtil.getDataOrThrow(templateFeign.getFirstCategoryListByMemberId(shopId, memberIdAndRoleIdAndAdornIdReq.getMemberId(), memberIdAndRoleIdAndAdornIdReq.getRoleId()));

        //组装
        return Optional.ofNullable(categoryBaseRespList).orElseGet(ArrayList::new).stream().map(
                c -> RespConvert.INSTANCE.toMobileCategoryResp(c, categoryIdList.contains(c.getId()))
        ).collect(Collectors.toList());
    }

}
