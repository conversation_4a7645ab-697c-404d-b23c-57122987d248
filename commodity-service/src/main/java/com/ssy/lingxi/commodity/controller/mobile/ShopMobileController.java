package com.ssy.lingxi.commodity.controller.mobile;

import com.ssy.lingxi.commodity.entity.do_.shop.ShopDO;
import com.ssy.lingxi.commodity.model.req.shop.ShopAllReq;
import com.ssy.lingxi.commodity.model.resp.shop.SelfBusinessShopListResp;
import com.ssy.lingxi.commodity.model.resp.shop.ShopSelectResp;
import com.ssy.lingxi.commodity.service.mobile.IShopMobileService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * mobile - 商城
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/09/27
 */
@RestController
@RequestMapping(ShopMobileController.PATH_PREFIX)
public class ShopMobileController extends BaseController {

    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/mobile/shopMobile";

    @Resource
    private IShopMobileService shopMobileService;

    /**
     * 启动页商城列表
     */
    @GetMapping("/startPageShopList")
    public WrapperResp<List<ShopDO>> startPageShopList() {
        return WrapperUtil.success(shopMobileService.startPageShopList());
    }

    /**
     * 自营商家商城列表
     */
    @GetMapping("/selfBusinessShopList")
    public WrapperResp<List<SelfBusinessShopListResp>> selfBusinessShopList() {
        return WrapperUtil.success(shopMobileService.selfBusinessShopList(getHeaderEnvironment()));
    }

    /**
     * 所有
     */
    @PostMapping("/all")
    public WrapperResp<List<ShopDO>> all(@RequestBody @Valid ShopAllReq req) {
        return WrapperUtil.success(shopMobileService.all(req));
    }

    /**
     * 商城选择
     */
    @GetMapping("/shopSelect")
    public WrapperResp<ShopSelectResp> shopSelect(@RequestParam(defaultValue = "3") Integer environment) {
        return WrapperUtil.success(shopMobileService.shopSelect(environment));
    }

    /**
     * 判断商城是否由会员来运营行情资讯门户
     */
    @GetMapping("/checkShopMemberOperate")
    public WrapperResp<Boolean> checkShopMemberOperate(Long shopId) {
        return WrapperUtil.success(shopMobileService.checkShopMemberOperate(shopId));
    }
}
