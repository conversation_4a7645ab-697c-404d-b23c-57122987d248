package com.ssy.lingxi.commodity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 商品服务启动类
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/13
 */
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = {"com.ssy.lingxi.**.api.feign"})
@ComponentScan(basePackages = {
        "com.ssy.lingxi.component",
        "com.ssy.lingxi.**.api.fallback",
        "com.ssy.lingxi." + ServiceModuleConstant.COMMODITY + ".config",
        "com.ssy.lingxi." + ServiceModuleConstant.COMMODITY + ".controller",
        "com.ssy.lingxi." + ServiceModuleConstant.COMMODITY + ".entity",
        "com.ssy.lingxi." + ServiceModuleConstant.COMMODITY + ".handler",
        "com.ssy.lingxi." + ServiceModuleConstant.COMMODITY + ".repository",
        "com.ssy.lingxi." + ServiceModuleConstant.COMMODITY + ".serviceImpl",
})
@EnableDiscoveryClient
@SpringBootApplication
public class CommodityServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(CommodityServiceApplication.class, args);
    }
}
