package com.ssy.lingxi.commodity.repository.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.low.MemberTemplateDO;
import com.ssy.lingxi.commodity.model.req.low.TemplateDataReq;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.low.IMemberTemplateMapper;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
* <AUTHOR>
*/
@Repository
public class MemberTemplateDao extends CommonServiceImpl<IMemberTemplateMapper, MemberTemplateDO> {

    public Page<MemberTemplateDO> findPageByMemberIdAndRoleIdAndMenuPathOrderById(TemplateDataReq req) {
        return this.page(Page.of(req.getCurrent(), req.getPageSize()),
                Wrappers.lambdaQuery(MemberTemplateDO.class)
                        .eq(MemberTemplateDO::getMemberId, req.getMemberId())
                        .eq(MemberTemplateDO::getRoleId, req.getRoleId())
                        .eq(MemberTemplateDO::getMenuPath, req.getMenuPath())
        );
    }

    public void updateStatusByMemberIdAndRoleIdAndMenuPath(Integer status, Long memberId, Long roleId, String menuPath) {
        this.update(
                Wrappers.lambdaUpdate(MemberTemplateDO.class).set(MemberTemplateDO::getStatus, status)
                        .eq(MemberTemplateDO::getMemberId, memberId).eq(MemberTemplateDO::getRoleId, roleId).eq(MemberTemplateDO::getMenuPath, menuPath)
        );
    }

    public Optional<MemberTemplateDO> findFirstByTemplateNameAndMemberIdAndRoleIdAndMenuPath(String templateName, Long memberId, Long roleId, String menuPath) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberTemplateDO.class).eq(MemberTemplateDO::getTemplateName, templateName).eq(MemberTemplateDO::getMemberId, memberId).eq(MemberTemplateDO::getRoleId, roleId).eq(MemberTemplateDO::getMenuPath, menuPath)
        );
    }

    public Optional<MemberTemplateDO> findFirstByTemplateNameAndMemberIdAndRoleIdAndMenuPathAndIdNe(String templateName, Long memberId, Long roleId, String menuPath, Long id) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberTemplateDO.class).eq(MemberTemplateDO::getTemplateName, templateName)
                        .eq(MemberTemplateDO::getMemberId, memberId).eq(MemberTemplateDO::getRoleId, roleId)
                        .eq(MemberTemplateDO::getMenuPath, menuPath).ne(MemberTemplateDO::getId, id)
        );
    }

    public Optional<MemberTemplateDO> findFirstByIdAndMemberIdAndRoleId(Long id, Long memberId, Long roleId) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberTemplateDO.class).eq(MemberTemplateDO::getId, id).eq(MemberTemplateDO::getMemberId, memberId).eq(MemberTemplateDO::getRoleId, roleId)
        );
    }

    public Optional<MemberTemplateDO> findFirstByMemberIdAndRoleIdAndMenuPathAndStatus(Long memberId, Long roleId, String menuPath, Integer status) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberTemplateDO.class).eq(MemberTemplateDO::getMemberId, memberId)
                        .eq(MemberTemplateDO::getRoleId, roleId).eq(MemberTemplateDO::getMenuPath, menuPath)
                        .eq(MemberTemplateDO::getStatus, status)
        );
    }
}




