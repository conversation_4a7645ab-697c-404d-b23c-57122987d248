package com.ssy.lingxi.commodity.repository.dao.adorn;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.adorn.SearchAdornDO;
import com.ssy.lingxi.commodity.model.req.adorn.SearchAdornPageReq;
import com.ssy.lingxi.commodity.model.req.adorn.SearchAdornUpdateReq;
import com.ssy.lingxi.commodity.model.resp.adorn.SearchAdornPageResp;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.ISearchAdornMapper;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.util.JsonUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class SearchAdornDao extends CommonServiceImpl<ISearchAdornMapper, SearchAdornDO> {

    /**
     * 分页列表
     */
    public Page<SearchAdornPageResp> pageList(SearchAdornPageReq adornTopicPageListReq, UserLoginCacheDTO user) {
        Page<SearchAdornPageResp> page = Page.of(adornTopicPageListReq.getCurrent(), adornTopicPageListReq.getPageSize());
        return getBaseMapper().pageList(page, adornTopicPageListReq, user);
    }

    public void updateBy(SearchAdornUpdateReq req) {
        this.update(
                Wrappers.lambdaUpdate(SearchAdornDO.class)
                        .set(CharSequenceUtil.isNotBlank(req.getName()), SearchAdornDO::getName, req.getName())
                        .set(CollUtil.isNotEmpty(req.getAdornContent()), SearchAdornDO::getAdornContent, JsonUtil.toJson(req.getAdornContent()))
                        .eq(SearchAdornDO::getId, req.getId())
        );
    }

    public void updateIsDeletedBy(List<Long> ids, Integer isDeleted) {
        this.update(
                Wrappers.lambdaUpdate(SearchAdornDO.class)
                        .set(SearchAdornDO::getIsDeleted, isDeleted)
                        .in(SearchAdornDO::getId, ids)
        );
    }

}




