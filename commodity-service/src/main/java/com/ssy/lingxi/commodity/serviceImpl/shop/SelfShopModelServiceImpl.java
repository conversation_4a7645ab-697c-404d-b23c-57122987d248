package com.ssy.lingxi.commodity.serviceImpl.shop;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.ssy.lingxi.commodity.domain.ShopDM;
import com.ssy.lingxi.commodity.entity.do_.shop.SelfShopModelDO;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopDO;
import com.ssy.lingxi.commodity.handler.component.cache.ShopCacheComponent;
import com.ssy.lingxi.commodity.model.req.shop.AllocatedSelfShopModelIdListReq;
import com.ssy.lingxi.commodity.model.req.shop.AllocationSelfShopReq;
import com.ssy.lingxi.commodity.model.req.shop.EditSelfShopModelInfoReq;
import com.ssy.lingxi.commodity.model.req.shop.EnvironmentReq;
import com.ssy.lingxi.commodity.model.resp.shop.AllocatedSelfShopModelIdListResp;
import com.ssy.lingxi.commodity.model.resp.shop.SelfShopModelListResp;
import com.ssy.lingxi.commodity.repository.dao.shop.SelfShopModelDao;
import com.ssy.lingxi.commodity.repository.dao.shop.ShopDao;
import com.ssy.lingxi.commodity.service.shop.ISelfShopModelService;
import com.ssy.lingxi.common.model.resp.select.SelectLongResp;
import com.ssy.lingxi.common.util.PredicateUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SelfShopModelServiceImpl implements ISelfShopModelService {

    private final SelfShopModelDao selfShopModelDao;
    private final ShopDao shopDao;
    private final ShopCacheComponent shopCacheComponent;


    @Override
    public List<SelfShopModelListResp> shopModelList(EnvironmentReq req) {
        return selfShopModelDao.findSelfShopModelListByEnvironmentOrderById(req.getEnvironment());
    }

    @Override
    public Boolean allocationSelfShop(AllocationSelfShopReq allocationSelfShopReq) {

        // 查询已分配给会员的自营商城模型ID
        List<Long> selfShopModelIdList = shopDao.findSelfShopModelIdByMemberIdAndMemberRoleId(allocationSelfShopReq.getMemberId(), allocationSelfShopReq.getMemberRoleId());

        // 判断当前自营商城模型ID是否已经分配过了
        Long repeatSelfShopModelId = CollUtil.findOne(allocationSelfShopReq.getSelfShopModelIdList(), o -> CollUtil.contains(selfShopModelIdList, o));
        if (Objects.nonNull(repeatSelfShopModelId)) {
            // 当前自营商城模型ID已经分配过了
            String selfShopModelName = selfShopModelDao.findNameById(repeatSelfShopModelId);
            throw new BusinessException(ResponseCodeEnum.COMMODITY_SELF_SHOP_MODEL_ID_REPEAT.getCode(),
                    StrUtil.format(ResponseCodeEnum.COMMODITY_SELF_SHOP_MODEL_ID_REPEAT.getMessage(),
                            MapUtil.builder().put("商城名称", selfShopModelName).build()
                    )
            );
        }

        // 查询自营商城模型数据
        List<SelfShopModelDO> selfShopModelDOList = selfShopModelDao.listByIds(allocationSelfShopReq.getSelfShopModelIdList());

        List<ShopDO> shopDOList = ShopDM.buildBy(allocationSelfShopReq, selfShopModelDOList);
        // 添加商城
        boolean saved = shopDao.saveBatch(shopDOList);

        // 更新缓存
        shopCacheComponent.selfShopToRedis(shopDOList);

        return saved;
    }

    @Override
    public void editSelfShopModelInfo(EditSelfShopModelInfoReq editSelfShopModelInfoReq) {

        SelfShopModelDO selfShopModelDO = selfShopModelDao.getById(editSelfShopModelInfoReq.getId());

        selfShopModelDO.setName(editSelfShopModelInfoReq.getName());
        selfShopModelDO.setLogoUrl(editSelfShopModelInfoReq.getLogoUrl());
        PredicateUtil.when(editSelfShopModelInfoReq.getDescribe()).then(() -> selfShopModelDO.setDescribe(editSelfShopModelInfoReq.getDescribe()));

        selfShopModelDao.updateNameAndLogoUrlAndDescribe(editSelfShopModelInfoReq);

    }

    @Override
    public AllocatedSelfShopModelIdListResp allocatedSelfShopModelIdList(AllocatedSelfShopModelIdListReq allocatedSelfShopModelIdListReq) {
        List<Long> selfShopModelIdList = shopDao.findSelfShopModelIdByMemberIdAndMemberRoleId(
                allocatedSelfShopModelIdListReq.getMemberId(), allocatedSelfShopModelIdListReq.getMemberRoleId()
        );

        return new AllocatedSelfShopModelIdListResp().setSelfShopModelIdList(selfShopModelIdList);
    }

    @Override
    public List<SelectLongResp> selfShopModelList() {
        return selfShopModelDao.findSelectResp();
    }

}
