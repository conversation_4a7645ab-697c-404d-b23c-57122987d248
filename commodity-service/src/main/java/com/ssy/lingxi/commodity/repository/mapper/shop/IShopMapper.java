package com.ssy.lingxi.commodity.repository.mapper.shop;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.model.resp.shop.SelfBusinessShopLogoResp;
import com.ssy.lingxi.commodity.api.model.resp.shop.ShopDetailResp;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopDO;
import com.ssy.lingxi.commodity.model.dto.AbilitySelfShopListDTO;
import com.ssy.lingxi.commodity.model.req.shop.ListShopByReq;
import com.ssy.lingxi.commodity.model.req.shop.SelfShopPageListReq;
import com.ssy.lingxi.commodity.model.req.shop.ShopPageListDataReq;
import com.ssy.lingxi.commodity.model.resp.shop.*;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
*/
@Mapper
public interface IShopMapper extends BaseMapper<ShopDO> {

    SelfBusinessShopLogoResp findFirstByIdAndMemberIdIn(@Param("id") Long id, @Param("memberList") List<MemberAndRoleIdDTO> memberList);

    Page<ShopInfoResp> shopList(Page<ShopInfoResp> page, @Param("shopPageListReq") ShopPageListDataReq shopPageListReq);

    Page<SelfShopInfoV2Resp> selfShopList(Page<SelfShopInfoV2Resp> page, @Param("selfShopPageListReq") SelfShopPageListReq selfShopPageListReq);

    ShopInfoResp shopInfo(@Param("id") Long id);

    List<ShopListV2Resp> abilitySelfShopList(@Param("abilitySelfShopListDTO") AbilitySelfShopListDTO abilitySelfShopListDTO);

    List<ShopStoreListResp> findShopStoreListRespByEnabledAndTypeAndSelf(@Param("storeId") Long storeId, @Param("enabled") Boolean enabled, @Param("type") Integer type, @Param("isSelf") Boolean isSelf);

    List<ShopDOResp> findByEnabledAndMemberIdOrderById(@Param("enabled") Boolean enabled, @Param("memberId") Long memberId);

    List<ShopListV2Resp> findShopListV2Resp(@Param("typeList") List<Integer> typeList);

    List<ShopDetailResp> findByListShopByReq(@Param("req") ListShopByReq listShopByReq);

    List<ShopSelectListResp> findShopSelectRespSelfByEnvironmentAndEnabledAndType(@Param("environment") Integer environment, @Param("enabled") Boolean enabled, @Param("type") Integer type);

    List<ShopDO> findAllByEnabledAndMemberIdIn(@Param("memberList") Set<MemberAndRoleIdDTO> memberList);
}




