package com.ssy.lingxi.commodity.repository.dao.shop;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.model.resp.StoreInnerResp;
import com.ssy.lingxi.commodity.entity.do_.shop.StoreDO;
import com.ssy.lingxi.commodity.enums.SelectStatusEnum;
import com.ssy.lingxi.commodity.handler.convert.StoreConvert;
import com.ssy.lingxi.commodity.model.req.member.StoreMobileDataReq;
import com.ssy.lingxi.commodity.model.req.web.RecommendDataReq;
import com.ssy.lingxi.commodity.model.resp.web.StoreV2ListResp;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IStoreMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class StoreDao extends CommonServiceImpl<IStoreMapper, StoreDO> {

    public List<StoreDO> findByIdInAndProvincesCodeListLikeOrEqAndCityCodeListLikeOrEq(List<Long> idList, String provinceCode, String provinceCodeOr, String cityCode, String cityCodeOr) {
        return this.list(
                Wrappers.lambdaQuery(StoreDO.class).in(CollUtil.isNotEmpty(idList), StoreDO::getId, idList)
                        .and(StringUtils.isNotBlank(provinceCode), wrapper -> wrapper.like(StoreDO::getProvincesCodeList, "%" + provinceCode + "%").or().eq(StoreDO::getProvincesCodeList, provinceCodeOr))
                        .and(StringUtils.isNotBlank(cityCode), wrapper -> wrapper.like(StoreDO::getCityCodeList, "%" + cityCode + "%").or().eq(StoreDO::getCityCodeList, cityCodeOr))
                        .orderByDesc(StoreDO::getId)
        );
    }

    public List<StoreDO> findAllByIdIn(List<Long> shopIdList) {
        return this.list(
                Wrappers.lambdaQuery(StoreDO.class).in(StoreDO::getId, shopIdList)
        );
    }

    public List<StoreInnerResp> findByIdIn(List<Long> shopIdList) {
        List<StoreDO> storeDOList = this.list(
                Wrappers.lambdaQuery(StoreDO.class).select(StoreDO::getId, StoreDO::getName, StoreDO::getLogo, StoreDO::getMemberId, StoreDO::getRoleId).in(StoreDO::getId, shopIdList)
        );
        if(CollectionUtils.isEmpty(storeDOList)){
            return new ArrayList<>();
        }
        return storeDOList.stream().map(storeDO -> BeanUtil.copyProperties(storeDO, StoreInnerResp.class)).collect(Collectors.toList());
    }

    public Page<StoreDO> findStore(RecommendDataReq req) {
        return this.page(Page.of(req.getCurrent(), req.getPageSize()),
                Wrappers.lambdaQuery(StoreDO.class).notIn(
                        CollUtil.isNotEmpty(req.getStoreIdList()) && req.getType().equals(SelectStatusEnum.UNSELECTED.getCode()),
                        StoreDO::getId, req.getStoreIdList()
                ).in(
                        CollUtil.isNotEmpty(req.getStoreIdList()) && !req.getType().equals(SelectStatusEnum.UNSELECTED.getCode()),
                        StoreDO::getId, req.getStoreIdList()
                ).like(
                        StringUtils.isNotBlank(req.getShopName()), StoreDO::getName, "%" + req.getShopName() + "%"
                ).like(
                        StringUtils.isNotBlank(req.getMemberName()), StoreDO::getMemberName, "%" + req.getMemberName() + "%"
                ).and(
                        StringUtils.isNotBlank(req.getProvinceCode()),
                        wrapper -> wrapper.like(StoreDO::getProvincesCodeList, "%" + req.getProvinceCode() + "%").or().eq(StoreDO::getProvincesCodeList, "0")
                ).and(
                        StringUtils.isNotBlank(req.getCityCode()),
                        wrapper -> wrapper.like(StoreDO::getCityCodeList, "%" + req.getCityCode() + "%").or().eq(StoreDO::getCityCodeList, "0")
                ).orderByDesc(StoreDO::getCreateTime)
        );
    }

    public Optional<StoreDO> findFirstByMemberIdAndRoleId(Long memberId, Long roleId) {
        return this.getFirst(
                Wrappers.lambdaQuery(StoreDO.class).eq(StoreDO::getMemberId, memberId).eq(StoreDO::getRoleId, roleId)
        );
    }

    public List<StoreV2ListResp> findStoreV2ListRespByMemberIdAndRoleId(Long memberId, Long roleId) {
        return this.list(Wrappers.lambdaQuery(StoreDO.class).select(
                StoreDO::getId, StoreDO::getName, StoreDO::getLogo, StoreDO::getStatus, StoreDO::getAreaList
        ).eq(StoreDO::getMemberId, memberId).eq(StoreDO::getRoleId, roleId).orderByDesc(StoreDO::getId)).stream().map(
                StoreConvert.INSTANCE::toStoreV2ListResp
        ).collect(Collectors.toList());
    }

    public List<StoreDO> findByRoleId(Long roleId) {
        return this.list(
                Wrappers.lambdaQuery(StoreDO.class).eq(StoreDO::getRoleId, roleId)
        );
    }

    public List<StoreDO> findByMemberIdIn(List<Long> memberIdList) {
        return this.list(
                Wrappers.lambdaQuery(StoreDO.class).in(StoreDO::getMemberId, memberIdList)
        );
    }

    public Page<StoreDO> findStore(StoreMobileDataReq storeMobileReq, List<Long> storeIdList) {
        return this.page(Page.of(storeMobileReq.getCurrent(), storeMobileReq.getPageSize()),
                Wrappers.lambdaQuery(StoreDO.class).in(CollUtil.isNotEmpty(storeIdList), StoreDO::getId, storeIdList)
                        // 店铺名称
                        .like(StringUtils.isNotBlank(storeMobileReq.getMemberName()), StoreDO::getName, "%" + storeMobileReq.getMemberName() + "%")
                        // 省编码
                        .and(StringUtils.isNotBlank(storeMobileReq.getProvinceCode()), wrapper -> wrapper.like(StoreDO::getProvincesCodeList, "%" + storeMobileReq.getProvinceCode() + "%").or().eq(StoreDO::getProvincesCodeList, "0"))
                        // 市编码
                        .and(StringUtils.isNotBlank(storeMobileReq.getCityCode()), wrapper -> wrapper.like(StoreDO::getCityCodeList, "%" + storeMobileReq.getCityCode() + "%").or().eq(StoreDO::getCityCodeList, "0"))
                        .orderBy(Objects.nonNull(storeMobileReq.getOrderType()), storeMobileReq.getOrderType().equals(1), StoreDO::getCreditPoint)
        );
    }

    public Page<StoreDO> findByCreateTimeGeOrderByCreateTimeDesc(Long createTime, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(StoreDO.class).ge(StoreDO::getCreateTime, createTime)
                        .orderByDesc(StoreDO::getCreateTime)
        );
    }

    public Page<StoreDO> findByStoreIdInAndMemberNameLikeAndNameLikeAndProvinceCodeLikeOrEqAndCityCodeLikeOrEqAndStatusOrderByCreditPointAndCreateTimeDesc(
            List<Long> storeIdList, String memberName, String name, String provinceCode, String provinceCodeOr, String cityCode, String cityCodeOr, Integer status, String sortCreditPoint, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(StoreDO.class).in(CollUtil.isNotEmpty(storeIdList), StoreDO::getId, storeIdList)
                        .like(StringUtils.isNotBlank(memberName), StoreDO::getMemberName, "%" + memberName + "%")
                        .like(StringUtils.isNotBlank(name), StoreDO::getName, "%" + name + "%")
                        .and(StringUtils.isNotBlank(provinceCode), wrapper -> wrapper.like(StoreDO::getProvincesCodeList, "%" + provinceCode + "%").or().eq(StoreDO::getProvincesCodeList, provinceCodeOr))
                        .and(StringUtils.isNotBlank(cityCode), wrapper -> wrapper.like(StoreDO::getCityCodeList, "%" + cityCode + "%").or().eq(StoreDO::getCityCodeList, cityCodeOr))
                        .eq(Objects.nonNull(status), StoreDO::getStatus, status)
                        .orderBy(StringUtils.isNotBlank(sortCreditPoint), sortCreditPoint.equals("ASC"), StoreDO::getCreditPoint)
                        .orderByDesc(StoreDO::getCreateTime)
        );
    }

    public Page<StoreDO> findByMemberNameContainsOrderByCreateTimeDesc(String memberName, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(StoreDO.class).like(StoreDO::getMemberName, "%" + memberName + "%")
                        .orderByDesc(StoreDO::getCreateTime)
        );
    }

    public Page<StoreDO> findAllOrderByCreateTimeDesc(PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()));
    }

    public Page<StoreDO> findNewAddStore(Page<StoreDO> page, long timeInMillis) {
        return getBaseMapper().findNewAddStore(page, timeInMillis);
    }

    public Page<StoreDO> findByIdNotInAndMemberNameContainsOrderByCreateTimeDesc(List<Long> idList, String memberName, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(StoreDO.class).notIn(CollUtil.isNotEmpty(idList), StoreDO::getId, idList)
                        .like(StringUtils.isNotBlank(memberName), StoreDO::getMemberName, "%" + memberName + "%")
                        .orderByDesc(StoreDO::getCreateTime)
        );
    }

    public Page<StoreDO> findByIdIn(List<Long> idList, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(StoreDO.class).in(CollUtil.isNotEmpty(idList), StoreDO::getId, idList)
        );
    }

    public Optional<Boolean> findOpenMroFirstById(Long id) {
        return this.getFirst(
                Wrappers.lambdaQuery(StoreDO.class).eq(StoreDO::getId, id)
        ).map(StoreDO::getIsOpenMro);
    }

    public void updateStatusById(Long id, Integer status) {
        this.update(
                Wrappers.lambdaUpdate(StoreDO.class).set(StoreDO::getStatus, status).eq(StoreDO::getId, id)
        );
    }

    public long countByCreateTimeBetween(Long startTime, Long endTime) {
        return this.count(
                Wrappers.lambdaQuery(StoreDO.class).between(StoreDO::getCreateTime, startTime, endTime)
        );
    }
}




