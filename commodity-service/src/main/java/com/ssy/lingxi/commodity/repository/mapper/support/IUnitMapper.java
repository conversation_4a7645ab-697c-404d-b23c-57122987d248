package com.ssy.lingxi.commodity.repository.mapper.support;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.model.resp.support.UnitRep;
import com.ssy.lingxi.commodity.entity.do_.support.UnitDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface IUnitMapper extends BaseMapper<UnitDO> {

    Page<UnitRep> findPageByNameLikeOrderByIdDesc(Page<UnitRep> page, @Param("name") String name);

    List<UnitRep> findByStatus(@Param("status") Boolean status);
}
