package com.ssy.lingxi.commodity.controller;

import com.ssy.lingxi.commodity.model.resp.CurrencySelectResp;
import com.ssy.lingxi.commodity.service.shop.ISelfShopModelService;
import com.ssy.lingxi.commodity.service.support.ICountryAreaService;
import com.ssy.lingxi.commodity.service.support.ICurrencyService;
import com.ssy.lingxi.commodity.service.support.IUnitService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.PhoneSelectResp;
import com.ssy.lingxi.common.model.resp.select.SelectLongResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 下拉框管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(SelectController.PATH_PREFIX)
public class SelectController {

    /**
     * 路径前缀
     */
    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/select/";

    private final IUnitService unitService;
    private final ICountryAreaService countryAreaService;
    private final ISelfShopModelService selfShopModelService;
    private final ICurrencyService currencyService;

    /**
     * 查询手机区号
     */
    @GetMapping(value = "getTelCode")
    public WrapperResp<List<PhoneSelectResp>> getTelCode() {
        return WrapperUtil.success(countryAreaService.listAreaCode());
    }

    /**
     * 查询单位下拉框
     * @param name 单位名称
     */
    @GetMapping(value = "getSelectUnit")
    public WrapperResp<List<SelectLongResp>> getSelectUnit(@RequestParam(value = "name", required = false) String name) {
        return WrapperUtil.success(unitService.getSelectUnit(name));
    }

    /**
     * 查询币种
     */
    @GetMapping(value = "/getSelectCurrency")
    public WrapperResp<List<CurrencySelectResp>> getSelectCurrency(@RequestParam(value = "name", required = false) String name) {
        return WrapperUtil.success(currencyService.getSelectCurrency(name));
    }

    /**
     * 自营商城模型列表
     */
    @GetMapping("/selfShopModelList")
    public WrapperResp<List<SelectLongResp>> selfShopModelList() {
        return WrapperUtil.success(selfShopModelService.selfShopModelList());
    }

}
