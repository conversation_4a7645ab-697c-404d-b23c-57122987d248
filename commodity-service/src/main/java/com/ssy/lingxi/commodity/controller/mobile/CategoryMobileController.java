package com.ssy.lingxi.commodity.controller.mobile;

import com.ssy.lingxi.commodity.model.req.common.MemberIdAndRoleIdAndAdornIdReq;
import com.ssy.lingxi.commodity.model.resp.mobile.MobileCategoryResp;
import com.ssy.lingxi.commodity.service.mobile.ICategoryMobileService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 *  mobile - 品类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/09/02
 */
@RestController
@RequestMapping(CategoryMobileController.PATH_PREFIX)
public class CategoryMobileController extends BaseController {

    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/mobile/categoryMobile";

    @Resource
    private ICategoryMobileService categoryMobileService;

    /**
     * APP企业商城首页（B端、C端）联营商城品类
     */
    @GetMapping("/enterpriseCategory")
    public WrapperResp<List<MobileCategoryResp>> enterpriseCategory(@RequestParam Long adornId) {
        Long shopId = getHeadersShopId();
        return WrapperUtil.success(categoryMobileService.enterpriseCategory(shopId, adornId));
    }

    /**
     * 校验平台品类是否存在于品类导航页
     *
     * @return 操作结果
     */
    @GetMapping("/checkCategory")
    public WrapperResp<Boolean> checkCategory(@RequestParam Long adornId, Long categoryId) {
        return WrapperUtil.success(categoryMobileService.checkCategory(adornId, categoryId));
    }

    /**
     * APP自营商城首页会员品类
     */
    @GetMapping("/selfMemberCategory")
    public WrapperResp<List<MobileCategoryResp>> selfMemberCategory(@Valid MemberIdAndRoleIdAndAdornIdReq memberIdAndRoleIdAndAdornIdReq) {
        Long shopId = getHeadersShopId();
        return WrapperUtil.success(categoryMobileService.selfMemberCategory(shopId, memberIdAndRoleIdAndAdornIdReq));
    }
}
