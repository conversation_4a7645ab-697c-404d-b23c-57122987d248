package com.ssy.lingxi.commodity.serviceImpl.web;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.bo.AreaBO;
import com.ssy.lingxi.commodity.entity.do_.collect.MemberLogisticsCollectDO;
import com.ssy.lingxi.commodity.entity.do_.door.MemberLogisticsDO;
import com.ssy.lingxi.commodity.model.req.common.CollectReq;
import com.ssy.lingxi.commodity.model.req.member.MemberLogisticsDataReq;
import com.ssy.lingxi.commodity.model.req.web.SaveLogisticsReq;
import com.ssy.lingxi.commodity.model.resp.web.MemberLogisticsListResp;
import com.ssy.lingxi.commodity.model.resp.web.MemberLogisticsMainResp;
import com.ssy.lingxi.commodity.repository.dao.MemberLogisticsCollectDao;
import com.ssy.lingxi.commodity.repository.dao.MemberLogisticsDao;
import com.ssy.lingxi.commodity.service.web.IMemberLogisticsWebService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.manage.api.feign.IAreaFeign;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.feign.IMemberLevelRightCreditFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignIdReq;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLrcResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignRegisterQueryResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * web - 会员物流门户 - 业务实现层
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/04/14
 */
@Slf4j
@Service
public class MemberLogisticsWebServiceImpl implements IMemberLogisticsWebService {

    @Resource
    private MemberLogisticsDao memberLogisticsDao;
    @Resource
    private IMemberLevelRightCreditFeign memberLevelRightCreditControllerFeign;
    @Resource
    private IMemberFeign memberInnerControllerFeign;
    @Resource
    private IAreaFeign areaFeign;
    @Resource
    private MemberLogisticsCollectDao memberLogisticsCollectDao;

    /**
     * 保存当前登录会员的物流门户
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional
    public void saveCurrMemberLogistics(SaveLogisticsReq dto, UserLoginCacheDTO user) {
        //校验（只有是服务提供者且企业类型是企业会员或个人会员才能创建物流门户
        if (!user.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            throw new BusinessException("角色类型不是服务提供者, 不能创建物流门户");
        }
        if (!(user.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || user.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))) {
            throw new BusinessException("会员类型不是企业会员或个人会员, 不能创建物流门户");
        }

        //默认省、市编码都为0（所有/所有）
        List<String> provinceCodes = Stream.of("0").collect(toList());
        List<String> cityCodes = Stream.of("0").collect(toList());
        if (dto.getAreaBOList().stream().noneMatch(a -> a.getProvinceCode().equals("0"))) {
            provinceCodes = dto.getAreaBOList().stream().map(AreaBO::getProvinceCode).collect(Collectors.toList());
            cityCodes = dto.getAreaBOList().stream().filter(a -> !a.getCityCode().equals("0")).map(AreaBO::getCityCode).collect(Collectors.toList());

            List<String> feignCodes = dto.getAreaBOList().stream().filter(a -> a.getCityCode().equals("0")).map(AreaBO::getProvinceCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(feignCodes)) {
                WrapperResp<List<String>> cityCodesWrapperResp = areaFeign.findCityCodeByProvinceCode(feignCodes);
                if (cityCodesWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode() && !CollectionUtils.isEmpty(cityCodesWrapperResp.getData())) {
                    cityCodes.addAll(cityCodesWrapperResp.getData());
                }
            }
        }

        //调用会员服务 -> 根据会员ID和角色ID获取平台会员的等级、注册年数、信用积分
        MemberFeignReq memberFeignReq = new MemberFeignReq();
        memberFeignReq.setMemberId(user.getMemberId());
        memberFeignReq.setRoleId(user.getMemberRoleId());
        WrapperResp<MemberFeignLrcResp> platformMemberLrcWrapperResp = memberLevelRightCreditControllerFeign.getPlatformMemberLrc(memberFeignReq);
        if (platformMemberLrcWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", platformMemberLrcWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
        }
        //调用会员服务 -> 根据会员Id查询会员注册信息
        MemberFeignIdReq memberFeignIdReq = new MemberFeignIdReq();
        memberFeignIdReq.setMemberId(user.getMemberId());
        WrapperResp<MemberFeignRegisterQueryResp> memberRegisterInfoWrapperResp = memberInnerControllerFeign.getMemberRegisterInfo(memberFeignIdReq);
        if (memberRegisterInfoWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", memberRegisterInfoWrapperResp.getMessage());
        }

        //保存会员物流门户信息
        MemberLogisticsDO memberLogistics = memberLogisticsDao.findFirstByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId()).orElseGet(MemberLogisticsDO::new);
        memberLogistics.setMemberId(user.getMemberId());
        memberLogistics.setRoleId(user.getMemberRoleId());
        memberLogistics.setMainBusiness(dto.getMainBusiness());
        memberLogistics.setAreaList(dto.getAreaBOList());
        memberLogistics.setLogo(dto.getLogo());
        memberLogistics.setDescribe(dto.getDescribe());
        memberLogistics.setSlideshowList(dto.getSlideshowList());
        memberLogistics.setCompanyPics(dto.getCompanyPics());
        memberLogistics.setHonorPics(dto.getHonorPics());
        memberLogistics.setAlbumName(dto.getAlbumName());
        memberLogistics.setAlbumUrl(dto.getAlbumUrl());
        memberLogistics.setAboutSeo(dto.getAboutSeo());
        memberLogistics.setAreas(dto.getAreaBOList().stream().map(a -> a.getProvince() + "/" + a.getCity()).collect(Collectors.joining("，")));
        memberLogistics.setProvincesCodeList(String.join("，", provinceCodes));
        memberLogistics.setCityCodeList(String.join("，", cityCodes));
        memberLogistics.setMemberName(user.getMemberName());
        memberLogistics.setLevelTag(platformMemberLrcWrapperResp.getData().getLevelTag());
        memberLogistics.setRegisterYears(platformMemberLrcWrapperResp.getData().getRegisterYears());
        memberLogistics.setCreditPoint(platformMemberLrcWrapperResp.getData().getCreditPoint());
        memberLogistics.setRegisteredCapital(Optional.ofNullable(memberRegisterInfoWrapperResp.getData()).map(MemberFeignRegisterQueryResp::getRegisteredCapital).orElse(null));
        memberLogistics.setEstablishmentDate(Optional.ofNullable(memberRegisterInfoWrapperResp.getData()).map(MemberFeignRegisterQueryResp::getEstablishmentDate).orElse(null));
        memberLogistics.setBusinessLicence(Optional.ofNullable(memberRegisterInfoWrapperResp.getData()).map(MemberFeignRegisterQueryResp::getBusinessLicence).orElse(null));
        memberLogistics.setRegisterArea(Optional.ofNullable(memberRegisterInfoWrapperResp.getData()).map(MemberFeignRegisterQueryResp::getRegisterArea).orElse(null));
        memberLogistics.setRegisterAddress(Optional.ofNullable(memberRegisterInfoWrapperResp.getData()).map(MemberFeignRegisterQueryResp::getRegisterAddress).orElse(null));
        memberLogisticsDao.saveOrUpdate(memberLogistics);
    }

    /**
     * 获取当前登录会员的物流门户
     *
     * @return 操作结果
     */
    @Override
    public MemberLogisticsDO findCurrMemberLogistics(UserLoginCacheDTO user) {
        return memberLogisticsDao.findFirstByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId()).orElse(null);
    }

    /**
     * 物流门户列表
     *
     * @param qo 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberLogisticsListResp> memberLogisticsList(MemberLogisticsDataReq qo) {

        Page<MemberLogisticsDO> page = memberLogisticsDao.findByProvinceCodeListLikeOrEqAndCityCodeLikeOrEqOrderByCreateTimeDesc(
                qo.getProvinceCode(), "0", qo.getCityCode(), "0", qo
        );
        List<MemberLogisticsDO> list = page.getRecords();

        if (CollUtil.isEmpty(list)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //封装
        List<MemberLogisticsListResp> memberLogisticsListVOList = list.stream().map(a -> {
            //拷贝
            MemberLogisticsListResp vo = new MemberLogisticsListResp();
            BeanUtils.copyProperties(a, vo);

            //格式化主营业务
            vo.setMainBusiness(StringUtils.join(a.getMainBusiness(), "|"));

            //默认先满星
            if (a.getAvgTradeCommentStar() == null || a.getAvgTradeCommentStar() == 0) {
                a.setAvgTradeCommentStar(5);
            }

            return vo;
        }).collect(toList());

        return new PageDataResp<>(page.getTotal(), memberLogisticsListVOList);
    }

    /**
     * 物流门户主页
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public MemberLogisticsMainResp memberLogisticsMain(CommonIdReq dto, UserLoginCacheDTO user) {
        //获取物流门户
        MemberLogisticsDO memberLogistics = memberLogisticsDao.getById(dto.getId());
        if (memberLogistics == null) {
            throw new BusinessException("根据ID找不到物流门户");
        }

        //拷贝
        MemberLogisticsMainResp memberLogisticsMainVO = new MemberLogisticsMainResp();
        BeanUtils.copyProperties(memberLogistics, memberLogisticsMainVO);

        //收藏状态
        if (user != null) {
            memberLogisticsMainVO.setCollectStatus(memberLogisticsCollectDao.existsByLogisticsIdAndMemberIdAndUserId(memberLogistics.getId(), user.getMemberId(), user.getUserId()));
        }

        //默认先满星
        if (memberLogisticsMainVO.getAvgTradeCommentStar() == null || memberLogisticsMainVO.getAvgTradeCommentStar() == 0) {
            memberLogisticsMainVO.setAvgTradeCommentStar(5);
        }

        return memberLogisticsMainVO;
    }

    /**
     * 根据物流门户ID集合获取物流门户
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public List<MemberLogisticsDO> findByIdIn(CommonIdListReq dto) {
        return memberLogisticsDao.listByIds(dto.getIdList()).stream().sorted(Comparator.comparingInt(a -> dto.getIdList().indexOf(a.getId()))).collect(toList());
    }


    /**
     * 收藏/取消收藏
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional
    public void collect(CollectReq dto, UserLoginCacheDTO user) {
        if (dto.getStatus()) {
            if (memberLogisticsCollectDao.existsByLogisticsIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId())) {
                throw new BusinessException("不能重复收藏，请刷新页面");
            }
            MemberLogisticsCollectDO collect = new MemberLogisticsCollectDO();
            collect.setLogisticsId(dto.getId());
            collect.setMemberId(user.getMemberId());
            collect.setUserId(user.getUserId());
            memberLogisticsCollectDao.save(collect);
        } else {
            memberLogisticsCollectDao.deleteByLogisticsIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId());
        }

    }

    /**
     * 收藏列表
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<MemberLogisticsDO> collectList(PageDataReq dto, UserLoginCacheDTO user) {
        //按收藏时间倒序获取当前用户收藏的物流门户ID
        Page<MemberLogisticsCollectDO> page = memberLogisticsCollectDao.findByMemberIdAndUserIdOrderByCreateTimeDesc(user.getMemberId(), user.getUserId(), dto);
        List<MemberLogisticsCollectDO> memberLogisticsCollectList = page.getRecords();
        if (CollUtil.isEmpty(memberLogisticsCollectList)) {
            log.info("当前用户没有收藏的物流门户");
            return new PageDataResp<>(0L, new ArrayList<>());
        }
        List<Long> memberLogisticsIdList = memberLogisticsCollectList.stream().map(MemberLogisticsCollectDO::getLogisticsId).collect(toList());

        //根据收藏的物流ID获取物流门户
        List<MemberLogisticsDO> memberLogisticsList = memberLogisticsDao.listByIds(memberLogisticsIdList);

        //组装收藏时间
        memberLogisticsList = memberLogisticsList.stream().peek(a -> a.setCreateTime(
                memberLogisticsCollectList.stream().filter(b ->
                        b.getLogisticsId().equals(a.getId())
                ).map(MemberLogisticsCollectDO::getCreateTime).findFirst().orElse(null)
        )).sorted(Comparator.comparingInt(a -> memberLogisticsIdList.indexOf(a.getId()))).collect(Collectors.toList());

        return new PageDataResp<>(page.getTotal(), memberLogisticsList);
    }
}
