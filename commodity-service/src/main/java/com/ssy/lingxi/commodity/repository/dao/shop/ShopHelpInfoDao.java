package com.ssy.lingxi.commodity.repository.dao.shop;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopHelpInfoDO;
import com.ssy.lingxi.commodity.handler.convert.ShopHelpInfoConvert;
import com.ssy.lingxi.commodity.model.resp.shop.ShopHelpInfoTreeResp;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.shop.IShopHelpInfoMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 商城帮助信息
 *
 * <AUTHOR>
 */
@Repository
public class ShopHelpInfoDao extends CommonServiceImpl<IShopHelpInfoMapper, ShopHelpInfoDO> {

    /**
     * 根据商城id查询帮助信息
     */
    @SuppressWarnings("unchecked")
    public List<ShopHelpInfoTreeResp> findShopHelpInfoTreeRespByShopId(Long shopId) {
        List<ShopHelpInfoDO> shopHelpInfoDOList = baseMapper.selectList(
                Wrappers.lambdaQuery(ShopHelpInfoDO.class)
                        .select(ShopHelpInfoDO::getId, ShopHelpInfoDO::getParentId, ShopHelpInfoDO::getShopId, ShopHelpInfoDO::getSort, ShopHelpInfoDO::getLevel, ShopHelpInfoDO::getName, ShopHelpInfoDO::getSkipType, ShopHelpInfoDO::getSkipUrl)
                        .eq(ShopHelpInfoDO::getShopId, shopId)
        );
        return shopHelpInfoDOList.stream().map(ShopHelpInfoConvert.INSTANCE::toShopHelpInfoTreeResp).collect(Collectors.toList());
    }

    /**
     * 更新排序，通过排序范围和偏移量
     *
     * @param parentId 父ID
     * @param minSort  最小排序
     * @param maxSort  最大排序
     * @param offset   偏移量
     */
    public void updateSortBySortRangeAndOffset(Long parentId, Integer minSort, Integer maxSort, Integer offset) {
        baseMapper.update(null, Wrappers.lambdaUpdate(ShopHelpInfoDO.class)
                .eq(ShopHelpInfoDO::getParentId, parentId)
                .ge(ShopHelpInfoDO::getSort, minSort).le(ShopHelpInfoDO::getSort, maxSort)
                .setSql(ShopHelpInfoDO.Fields.sort + " = " + ShopHelpInfoDO.Fields.sort + " + {0}", offset));
    }

    /**
     * 更新排序，通过ID更新
     */
    public void updateSortById(Long id, Integer sort) {
        baseMapper.update(null, Wrappers.lambdaUpdate(ShopHelpInfoDO.class)
                .eq(ShopHelpInfoDO::getId, id)
                .set(ShopHelpInfoDO::getSort, sort));
    }

    /**
     * 查询同一层级且父ID相同的排序最大值
     */
    @SuppressWarnings("unchecked")
    public Integer findFirstSortByShopIdAndParentIdOrderBySortDesc(Long shopId, Long parentId) {
        return this.getFirst(
                Wrappers.lambdaQuery(ShopHelpInfoDO.class).select(ShopHelpInfoDO::getSort)
                        .eq(ShopHelpInfoDO::getShopId, shopId).eq(ShopHelpInfoDO::getParentId, parentId)
                        .orderByDesc(ShopHelpInfoDO::getSort)
        ).map(ShopHelpInfoDO::getSort).orElse(0);
    }

    /**
     * 通过父ID删除所有子节点
     */
    public void removeByParentId(Long parentId) {
        baseMapper.delete(Wrappers.lambdaQuery(ShopHelpInfoDO.class).eq(ShopHelpInfoDO::getParentId, parentId));
    }

    /**
     * 通过ID查询商城ID
     */
    @SuppressWarnings("unchecked")
    public Long findShopIdById(Long id) {
        return this.getFirst(Wrappers.lambdaQuery(ShopHelpInfoDO.class).select(ShopHelpInfoDO::getShopId).eq(ShopHelpInfoDO::getId, id)).map(ShopHelpInfoDO::getShopId).orElse(null);
    }

    /**
     * 通过ID列表更新排序
     */
    public void updateSortByIdIn(List<Long> idList) {
        baseMapper.updateSortByIdIn(idList);
    }
}




