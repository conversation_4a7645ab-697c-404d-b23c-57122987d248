package com.ssy.lingxi.commodity.controller.support;

import com.ssy.lingxi.commodity.api.model.req.support.UnitReq;
import com.ssy.lingxi.commodity.api.model.resp.support.UnitRep;
import com.ssy.lingxi.commodity.service.support.IUnitService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 单位管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(UnitController.PATH_PREFIX)
public class UnitController {

    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/unit";

    private final IUnitService unitService;

    /**
     * 查询单位列表
     * @param pageDataReq 分页实体
     * @param name 单位名称
     * @return Wrapper<PageData<Unit>>
     */
    @GetMapping(value = "/getUnitList")
    public WrapperResp<PageDataResp<UnitRep>> getUnitList(PageDataReq pageDataReq, @RequestParam(value = "name", required = false) String name) {
        return WrapperUtil.success(unitService.getUnitList(pageDataReq, name));
    }

    /**
     * 查询单位信息
     * @param id 单位id
     * @return Wrapper<?>
     */
    @GetMapping(value = "/getUnit")
    public WrapperResp<UnitRep> getUnit(@RequestParam("id") Long id) {
        return WrapperUtil.success(unitService.getUnit(id));
    }

    /**
     * 新增/修改单位
     * @param unitRequest 单位实体
     */
    @PostMapping(value = "/saveOrUpdateUnit")
    public WrapperResp<Boolean> saveOrUpdateUnit(@RequestBody UnitReq unitRequest){
        return WrapperUtil.success(unitService.saveOrUpdateUnit(unitRequest));
    }

    /**
     * 删除单位
     * @param id 单位id
     */
    @GetMapping(value = "/deleteUnit")
    public WrapperResp<Boolean> deleteUnit(@RequestParam("id") Long id){
        return WrapperUtil.success(unitService.deleteUnit(id));
    }

    /**
     * 启用/停用单位
     * @param id 单位id
     * @param status 是否启用
     */
    @GetMapping(value = "/updateUnitStatus")
    public WrapperResp<Boolean> updateUnitStatus(@RequestParam("id") Long id, @RequestParam("status") Boolean status) {
        return WrapperUtil.success(unitService.updateUnitStatus(id, status));
    }
}
