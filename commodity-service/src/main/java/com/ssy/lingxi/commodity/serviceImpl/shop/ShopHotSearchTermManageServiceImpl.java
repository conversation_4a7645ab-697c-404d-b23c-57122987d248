package com.ssy.lingxi.commodity.serviceImpl.shop;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.enums.ShopHotSearchTermSourceTypeEnum;
import com.ssy.lingxi.commodity.api.enums.ShopHotSearchTermStatusEnum;
import com.ssy.lingxi.commodity.api.model.resp.shop.ShopHotSearchTermManageResp;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopHotSearchTermManageDO;
import com.ssy.lingxi.commodity.model.req.shop.ShopHotSearchTermManageSaveReq;
import com.ssy.lingxi.commodity.repository.dao.shop.ShopHotSearchTermManageDao;
import com.ssy.lingxi.commodity.service.shop.IShopHotSearchTermManageService;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.api.feign.IMemberAbilityUserFeign;
import com.ssy.lingxi.member.api.model.resp.MemberUserFeignResp;
import com.ssy.lingxi.product.api.model.req.StatusUpdateReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 热搜词管理服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-30
 */
@Service
public class ShopHotSearchTermManageServiceImpl implements IShopHotSearchTermManageService {

    @Resource
    private ShopHotSearchTermManageDao shopHotSearchTermManageDao;
    @Resource
    private IRedisUtils redisUtils;
    @Resource
    private IMemberAbilityUserFeign memberAbilityUserFeign;

    @Override
    @Transactional
    public void saveOrUpdate(ShopHotSearchTermManageSaveReq saveReq, UserLoginCacheDTO user) {
        ShopHotSearchTermManageDO manageDO = null;
        if(saveReq.getId() != null){
            manageDO = shopHotSearchTermManageDao.getById(saveReq.getId());
            if(manageDO == null){
                throw new BusinessException(ResponseCodeEnum.COMMODITY_SHOP_HOT_SEARCH_TERM_NOT_EXIST);
            }
            BeanUtil.copyProperties(saveReq, manageDO);
            manageDO.setUpdateUserId(user.getUserId());
            manageDO.setWeight(saveReq.getWeight() != null ? saveReq.getWeight() : 0);
        }else {
            manageDO = BeanUtil.copyProperties(saveReq, ShopHotSearchTermManageDO.class);
            manageDO.setSourceType(1);
            manageDO.setStatus(ShopHotSearchTermStatusEnum.SHOW.getCode());
            manageDO.setMemberId(user.getMemberId());
            manageDO.setMemberRoleId(user.getMemberRoleId());
            manageDO.setCreateUserId(user.getUserId());
            manageDO.setUpdateUserId(user.getUserId());
            manageDO.setSearchCount(0);
            manageDO.setWeight(saveReq.getWeight() != null ? saveReq.getWeight() : 0);
        }
        shopHotSearchTermManageDao.saveOrUpdate(manageDO);

        saveRedis();
    }

    //根据需求将前12的热搜词放入redis
    private void saveRedis(){
        List<ShopHotSearchTermManageDO> manageDOList = shopHotSearchTermManageDao.findByLimit12();
        if(CollUtil.isNotEmpty(manageDOList)){
            List<String> nameList = manageDOList.stream().map(ShopHotSearchTermManageDO::getName).collect(Collectors.toList());
            redisUtils.stringSet(Constant.SHOP_HOT_SEARCH_TERM_KEY, JsonUtil.toJson(nameList), RedisConstant.REDIS_PRODUCT_INDEX);
        }
    }

    @Override
    public PageDataResp<ShopHotSearchTermManageResp> getListByPage(String name, PageDataReq pageDataReq, UserLoginCacheDTO user) {
        Page<ShopHotSearchTermManageDO> manageDOPage = shopHotSearchTermManageDao.getListByPage(name, pageDataReq, user);
        List<ShopHotSearchTermManageDO> manageDOList = manageDOPage.getRecords();
        if(CollUtil.isNotEmpty(manageDOList)){
            List<Long> userIds = manageDOList.stream().filter(item -> item.getUpdateUserId() != null).map(ShopHotSearchTermManageDO::getUpdateUserId).collect(Collectors.toList());
            List<MemberUserFeignResp> userList = new ArrayList<>();
            if(CollUtil.isNotEmpty(userIds)){
                CommonIdListReq userReq = new CommonIdListReq();
                userReq.setIdList(userIds);
                List<MemberUserFeignResp> userFeignRespList = WrapperUtil.getData(memberAbilityUserFeign.getListByIds(userReq));
                if(CollUtil.isNotEmpty(userFeignRespList)){
                    userList.addAll(userFeignRespList);
                }
            }
            Map<Long, MemberUserFeignResp> userFeignRespMap = userList.stream().collect(Collectors.toMap(MemberUserFeignResp::getUserId, Function.identity()));
            List<ShopHotSearchTermManageResp> manageRespList = manageDOList.stream().map(shopHotSearchTermManageDO -> {
                ShopHotSearchTermManageResp manageResp = BeanUtil.toBean(shopHotSearchTermManageDO, ShopHotSearchTermManageResp.class);
                manageResp.setSourceTypeName(ShopHotSearchTermSourceTypeEnum.getNameByCode(manageResp.getSourceType()));
                manageResp.setStatusName(ShopHotSearchTermStatusEnum.getNameByCode(manageResp.getStatus()));
                manageResp.setUpdateTimeStr(DateTimeUtil.timestampToDateTimeString(shopHotSearchTermManageDO.getUpdateTime()));
                MemberUserFeignResp userFeignResp = userFeignRespMap.get(manageResp.getUpdateUserId());
                manageResp.setUpdateUserName(userFeignResp != null ? userFeignResp.getName() : "");
                return manageResp;
            }).collect(Collectors.toList());
            return new PageDataResp<>(manageDOPage.getTotal(), manageRespList);
        }
        return new PageDataResp<>();
    }

    @Override
    public ShopHotSearchTermManageResp getById(Long id) {
        ShopHotSearchTermManageDO manageDO = shopHotSearchTermManageDao.getById(id);
        if(manageDO != null){
            return BeanUtil.toBean(manageDO, ShopHotSearchTermManageResp.class);
        }
        return null;
    }

    @Override
    @Transactional
    public void deleteById(Long id, UserLoginCacheDTO user) {
        ShopHotSearchTermManageDO manageDO = shopHotSearchTermManageDao.getById(id);
        if(manageDO == null){
            throw new BusinessException(ResponseCodeEnum.COMMODITY_SHOP_HOT_SEARCH_TERM_NOT_EXIST);
        }
        shopHotSearchTermManageDao.removeById(id);
        saveRedis();
    }

    @Override
    public void updateStatus(StatusUpdateReq updateReq, UserLoginCacheDTO user) {
        ShopHotSearchTermManageDO manageDO = shopHotSearchTermManageDao.getById(updateReq.getId());
        if(manageDO == null){
            throw new BusinessException(ResponseCodeEnum.COMMODITY_SHOP_HOT_SEARCH_TERM_NOT_EXIST);
        }
        manageDO.setStatus(updateReq.getStatus());
        manageDO.setUpdateUserId(user.getUserId());
        shopHotSearchTermManageDao.updateById(manageDO);
        saveRedis();
    }

    @Override
    public List<String> getList() {
        String value = redisUtils.stringGet(Constant.SHOP_HOT_SEARCH_TERM_KEY, RedisConstant.REDIS_PRODUCT_INDEX);
        if(StringUtils.isNotBlank(value)){
            return JsonUtil.toList(value);
        }else {
            List<ShopHotSearchTermManageDO> manageDOList = shopHotSearchTermManageDao.findByLimit12();
            if(CollUtil.isNotEmpty(manageDOList)){
                List<String> nameList = manageDOList.stream().map(ShopHotSearchTermManageDO::getName).collect(Collectors.toList());
                redisUtils.stringSet(Constant.SHOP_HOT_SEARCH_TERM_KEY, JsonUtil.toJson(nameList), RedisConstant.REDIS_PRODUCT_INDEX);
                return nameList;
            }
        }
        return Collections.emptyList();
    }

    @Override
    @Transactional
    public void updateSearchCount(String name) {
        List<ShopHotSearchTermManageDO> manageDOList = shopHotSearchTermManageDao.findByName(name);
        if(CollUtil.isNotEmpty(manageDOList)){
            manageDOList.forEach(manageDO ->{
                manageDO.setSearchCount(manageDO.getSearchCount() != null ? manageDO.getSearchCount() + 1 : 1);
            });
            shopHotSearchTermManageDao.updateBatchById(manageDOList);
            saveRedis();
        }else {
            ShopHotSearchTermManageDO manageDO = new ShopHotSearchTermManageDO();
            manageDO.setName(name);
            manageDO.setSearchCount(1);
            manageDO.setWeight(0);
            manageDO.setStatus(ShopHotSearchTermStatusEnum.SHOW.getCode());
            manageDO.setSourceType(ShopHotSearchTermSourceTypeEnum.STATISTICS.getCode());
            shopHotSearchTermManageDao.saveOrUpdate(manageDO);
        }
    }
}
