package com.ssy.lingxi.commodity.repository.dao.shop;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ssy.lingxi.commodity.entity.do_.shop.SelfShopModelDO;
import com.ssy.lingxi.commodity.handler.convert.SelfShopModelConvert;
import com.ssy.lingxi.commodity.model.req.shop.EditSelfShopModelInfoReq;
import com.ssy.lingxi.commodity.model.resp.shop.SelfShopModelListResp;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.shop.ISelfShopModelMapper;
import com.ssy.lingxi.common.model.resp.select.SelectLongResp;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class SelfShopModelDao extends CommonServiceImpl<ISelfShopModelMapper, SelfShopModelDO> {

    public List<SelfShopModelListResp> findSelfShopModelListByEnvironmentOrderById(Integer environment) {
        return baseMapper.findSelfShopModelListByEnvironmentOrderById(environment);
    }

    public void updateNameAndLogoUrlAndDescribe(EditSelfShopModelInfoReq editSelfShopModelInfoReq) {
        this.update(
                Wrappers.lambdaUpdate(SelfShopModelDO.class).set(SelfShopModelDO::getName, editSelfShopModelInfoReq.getName())
                        .set(SelfShopModelDO::getLogoUrl, editSelfShopModelInfoReq.getLogoUrl())
                        .set(StringUtils.isNotBlank(editSelfShopModelInfoReq.getDescribe()), SelfShopModelDO::getDescribe, editSelfShopModelInfoReq.getDescribe())
                        .eq(SelfShopModelDO::getId, editSelfShopModelInfoReq.getId())
        );
    }

    public String findNameById(Long id) {
        return this.getFirst(
                Wrappers.lambdaQuery(SelfShopModelDO.class).select(SelfShopModelDO::getName).eq(SelfShopModelDO::getId, id)
        ).map(SelfShopModelDO::getName).orElse("");
    }

    public List<SelectLongResp> findSelectResp() {
        return this.list(Wrappers.lambdaQuery(SelfShopModelDO.class).select(
                SelfShopModelDO::getId, SelfShopModelDO::getName
        )).stream().map(SelfShopModelConvert.INSTANCE::toSelectResp).collect(Collectors.toList());
    }
}

