package com.ssy.lingxi.commodity.controller.mobile;

import com.ssy.lingxi.commodity.model.req.mobile.MemberSelfMainMobileReq;
import com.ssy.lingxi.commodity.model.resp.mobile.MemberSelfMainMobileResp;
import com.ssy.lingxi.commodity.service.mobile.IMemberSelfMobileService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * mobile - 会员自营门户
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(MemberSelfMobileController.PATH_PREFIX)
public class MemberSelfMobileController extends BaseController {

    /**
     * 路径前缀
     */
    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/mobile/memberSelfMobile";

    private final IMemberSelfMobileService memberSelfMobileService;

    /**
     * 会员自营主页
     *
     * @param memberSelfMainMobileReq 请求参数
     * @return 操作结果
     */
    @GetMapping("/memberSelfMain")
    public WrapperResp<MemberSelfMainMobileResp> memberSelfMain(MemberSelfMainMobileReq memberSelfMainMobileReq) {
        return WrapperUtil.success(memberSelfMobileService.memberSelfMain(memberSelfMainMobileReq));
    }

}
