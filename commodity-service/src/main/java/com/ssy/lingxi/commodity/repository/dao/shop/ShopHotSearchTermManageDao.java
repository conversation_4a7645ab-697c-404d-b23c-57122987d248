package com.ssy.lingxi.commodity.repository.dao.shop;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.enums.ShopHotSearchTermStatusEnum;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopHotSearchTermManageDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.shop.IShopHotSearchTermManageMapper;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 热搜词管理dao层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-30
 */
@Repository
public class ShopHotSearchTermManageDao extends CommonServiceImpl<IShopHotSearchTermManageMapper, ShopHotSearchTermManageDO> {

    public Page<ShopHotSearchTermManageDO> getListByPage(String name, PageDataReq pageDataReq, UserLoginCacheDTO user){
        QueryWrapper<ShopHotSearchTermManageDO> query = new QueryWrapper<>();
        if(StringUtils.isNotBlank(name)){
            query.lambda().like(ShopHotSearchTermManageDO::getName, "%" + name + "%");
        }
        query.last("order by search_count + weight desc");
//        query.lambda().eq(ShopHotSearchTermManageDO::getMemberId, user.getMemberId());
//        query.lambda().eq(ShopHotSearchTermManageDO::getMemberRoleId, user.getMemberRoleId());
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()), query);
    }

    public ShopHotSearchTermManageDO findByMemberIdAndRoleIdAndId(Long id, UserLoginCacheDTO user){
        QueryWrapper<ShopHotSearchTermManageDO> query = new QueryWrapper<>();
        query.lambda().eq(ShopHotSearchTermManageDO::getMemberId, user.getMemberId());
        query.lambda().eq(ShopHotSearchTermManageDO::getMemberRoleId, user.getMemberRoleId());
        query.lambda().eq(ShopHotSearchTermManageDO::getId, id);
        return baseMapper.selectOne(query);
    }

    public List<ShopHotSearchTermManageDO> findByLimit12(){
        QueryWrapper<ShopHotSearchTermManageDO> query = new QueryWrapper<>();
        query.lambda().eq(ShopHotSearchTermManageDO::getStatus, ShopHotSearchTermStatusEnum.SHOW.getCode());
        query.last("order by search_count + weight desc limit 12");
        return baseMapper.selectList(query);
    }

    public List<ShopHotSearchTermManageDO> findByName(String name){
        QueryWrapper<ShopHotSearchTermManageDO> query = new QueryWrapper<>();
        query.lambda().like(ShopHotSearchTermManageDO::getName, "%" + name + "%");
        return baseMapper.selectList(query);
    }

}
