package com.ssy.lingxi.commodity.serviceImpl.web;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.door.MemberPurchaseDO;
import com.ssy.lingxi.commodity.entity.do_.door.MemberSelfDO;
import com.ssy.lingxi.commodity.entity.do_.door.SeoDO;
import com.ssy.lingxi.commodity.entity.do_.shop.StoreDO;
import com.ssy.lingxi.commodity.enums.SeoDoorTypeEnum;
import com.ssy.lingxi.commodity.model.req.seo.SeoDataReq;
import com.ssy.lingxi.commodity.model.req.web.SaveSeoReq;
import com.ssy.lingxi.commodity.repository.dao.MemberPurchaseDao;
import com.ssy.lingxi.commodity.repository.dao.MemberSelfDao;
import com.ssy.lingxi.commodity.repository.dao.SeoDao;
import com.ssy.lingxi.commodity.repository.dao.shop.StoreDao;
import com.ssy.lingxi.commodity.service.web.ISeoWebService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.CommonStatusReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * web - SEO - 业务实现层
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/04/14
 */
@Slf4j
@Service
public class SeoWebServiceImpl implements ISeoWebService {

    @Resource
    private SeoDao seoDao;
    @Resource
    private StoreDao storeDao;
    @Resource
    private MemberPurchaseDao memberPurchaseDao;
    @Resource
    private MemberSelfDao memberSelfDao;

    /**
     * 列表
     *
     * @param qo 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<SeoDO> page(SeoDataReq qo, UserLoginCacheDTO user) {
        //根据门户类型获取对应得门户ID
        Long doorId = getDoorId(qo.getDoorType(), user);
        if (doorId == null) {
            throw new BusinessException(ResponseCodeEnum.PT_COULD_NOT_FIND_THE_CORRESPONDING_PORTAL_ID);
        }

        Page<SeoDO> page = seoDao.findByDoorTypeAndDoorIdAndNameLikeOrderByCreateTimeDesc(
                qo.getDoorType(), doorId, qo.getName(), qo
        );

        return new PageDataResp<>(page.getTotal(), page.getRecords());
    }

    /**
     * 详情
     *
     * @return 操作结果
     */
    @Override
    public SeoDO get(CommonIdReq dto, UserLoginCacheDTO user) {
        SeoDO seo = seoDao.getById(dto.getId());
        if (seo == null) {
            throw new BusinessException(ResponseCodeEnum.PT_RECORDS_DON_T_EXIST);
        }
        return seo;
    }

    /**
     * 新增
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional
    public void add(SaveSeoReq dto, UserLoginCacheDTO user) {
        //根据门户类型获取对应得门户ID
        Long doorId = getDoorId(dto.getDoorType(), user);
        if (doorId == null) {
            throw new BusinessException(ResponseCodeEnum.PT_COULD_NOT_FIND_THE_CORRESPONDING_PORTAL_ID);
        }
        //判断是否已存在相同的页面类型
        Integer doorType = dto.getDoorType();
        Integer type = dto.getType();
        System.out.println(type);
        List<SeoDO> seoList = seoDao.findByDoorIdAndDoorTypeAndType(doorId, doorType, type);
        if (seoList.size() > 0) {
            throw new BusinessException(ResponseCodeEnum.PT_PAGE_TYPE_ALREADY_EXISTS);
        }
        //新增
        SeoDO seo = new SeoDO();
        BeanUtils.copyProperties(dto, seo);
        seo.setDoorId(doorId);
        seoDao.saveOrUpdate(seo);
    }

    /**
     * 修改
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional
    public void update(SaveSeoReq dto, UserLoginCacheDTO user) {
        SeoDO seo = seoDao.getById(dto.getId());
        if (seo == null) {
            throw new BusinessException(ResponseCodeEnum.PT_RECORDS_DON_T_EXIST);
        }
        if (seo.getStatus().equals(CommonBooleanEnum.YES.getCode())) {
            throw new BusinessException(ResponseCodeEnum.PT_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }
        //判断是否已存在相同的页面类型
        List<SeoDO> seoList = seoDao.findByDoorIdAndDoorTypeAndTypeAndIdNot(seo.getDoorId(), dto.getDoorType(), dto.getType(), dto.getId());
        if (CollUtil.isNotEmpty(seoList)) {
            throw new BusinessException(ResponseCodeEnum.PT_PAGE_TYPE_ALREADY_EXISTS);
        }
        seo.setType(dto.getType());
        seo.setName(dto.getName());
        seo.setLink(dto.getLink());
        seo.setTitle(dto.getTitle());
        seo.setDescription(dto.getDescription());
        seo.setKeywords(dto.getKeywords());
        seoDao.saveOrUpdate(seo);
    }

    /**
     * 停用/启用
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional
    public void updateStatus(CommonStatusReq dto, UserLoginCacheDTO user) {
        SeoDO seo = seoDao.getById(dto.getId());
        if (seo == null) {
            throw new BusinessException(ResponseCodeEnum.PT_RECORDS_DON_T_EXIST);
        }
        seo.setStatus(dto.getStatus());
        seoDao.saveOrUpdate(seo);
    }

    /**
     * 删除
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional
    public void delete(CommonIdReq dto, UserLoginCacheDTO user) {
        SeoDO seo = seoDao.getById(dto.getId());
        if (seo == null) {
            throw new BusinessException(ResponseCodeEnum.PT_RECORDS_DON_T_EXIST);
        }
        if (seo.getStatus().equals(CommonBooleanEnum.YES.getCode())) {
            throw new BusinessException(ResponseCodeEnum.PT_DATA_STATUS_INCORRECT_OPERATE_INVALID);
        }
        seoDao.removeById(seo.getId());
    }

    /**
     * 根据门户类型获取对应得门户ID
     *
     * @param doorType 门户类型：1-店铺门户  2-渠道门户  3-采购门户
     * @param user     当前登录用户
     */
    private Long getDoorId(Integer doorType, UserLoginCacheDTO user) {
        Long doorId = null;
        if (doorType.equals(SeoDoorTypeEnum.SHOP.getCode())) {
            StoreDO storeDO = storeDao.findFirstByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId()).orElse(null);
            doorId = storeDO != null ? storeDO.getId() : doorId;
        }
        if (doorType.equals(SeoDoorTypeEnum.PURCHASE.getCode())) {
            MemberPurchaseDO memberPurchase = memberPurchaseDao.findFirstByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId()).orElse(null);
            doorId = memberPurchase != null ? memberPurchase.getId() : doorId;
        }
        if (doorType.equals(SeoDoorTypeEnum.SELF.getCode())) {
            MemberSelfDO memberSelf = memberSelfDao.findFirstByMemberId(user.getMemberId()).orElse(null);
            doorId = memberSelf != null ? memberSelf.getId() : doorId;
        }
        return doorId;
    }
}
