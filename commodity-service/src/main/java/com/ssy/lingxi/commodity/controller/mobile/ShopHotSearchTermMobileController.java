package com.ssy.lingxi.commodity.controller.mobile;

import com.ssy.lingxi.commodity.service.shop.IShopHotSearchTermManageService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 移动端热搜词
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-05
 */
@RestController
@RequestMapping(ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/mobile/shopHotSearchTerm")
public class ShopHotSearchTermMobileController extends BaseController {

    @Resource
    private IShopHotSearchTermManageService shopHotSearchTermManageService;

    /**
     * 获取前12的热搜词
     */
    @GetMapping("/getList")
    public WrapperResp<List<String>> getList(){
        return WrapperUtil.success(shopHotSearchTermManageService.getList());
    }
}
