package com.ssy.lingxi.commodity.serviceImpl.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.bo.AreaBO;
import com.ssy.lingxi.commodity.entity.do_.collect.StoreCollectDO;
import com.ssy.lingxi.commodity.entity.do_.door.SeoDO;
import com.ssy.lingxi.commodity.entity.do_.shop.StoreDO;
import com.ssy.lingxi.commodity.enums.AdornTypeEnum;
import com.ssy.lingxi.commodity.enums.SeoDoorTypeEnum;
import com.ssy.lingxi.commodity.enums.SeoTypeEnum;
import com.ssy.lingxi.commodity.handler.convert.StoreConvert;
import com.ssy.lingxi.commodity.model.req.common.CollectReq;
import com.ssy.lingxi.commodity.model.req.common.CommonIdListPageDataReq;
import com.ssy.lingxi.commodity.model.req.member.StoreDataReq;
import com.ssy.lingxi.commodity.model.req.member.StoreInCommodityListAdornReq;
import com.ssy.lingxi.commodity.model.req.mobile.StoreMainReq;
import com.ssy.lingxi.commodity.model.req.web.SaveStoreReq;
import com.ssy.lingxi.commodity.model.resp.web.*;
import com.ssy.lingxi.commodity.repository.dao.SeoDao;
import com.ssy.lingxi.commodity.repository.dao.StoreCollectDao;
import com.ssy.lingxi.commodity.repository.dao.adorn.AdornDao;
import com.ssy.lingxi.commodity.repository.dao.shop.StoreDao;
import com.ssy.lingxi.commodity.service.web.IStoreWebService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.CommonStatusReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.ReportTodayResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.manage.api.feign.IAreaFeign;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.feign.IMemberLevelRightCreditFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignIdReq;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLrcResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignRegisterQueryResp;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.feign.ITemplateFeign;
import com.ssy.lingxi.product.api.model.req.MemberReq;
import com.ssy.lingxi.product.api.model.req.StoreIdListReq;
import com.ssy.lingxi.product.api.model.req.TemplateCommoditySearchReq;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.api.model.resp.store.StoreResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * web - 会员店铺门户 - 业务实现层
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/12/09
 */
@Slf4j
@Service
public class StoreWebServiceImpl implements IStoreWebService {

    @Resource
    private AdornDao adornDao;
    @Resource
    private StoreDao storeDao;
    @Resource
    private IMemberLevelRightCreditFeign memberLevelRightCreditControllerFeign;
    @Resource
    private StoreCollectDao storeCollectDao;
    @Resource
    private ICommodityFeign commodityFeign;
    @Resource
    private IMemberFeign memberInnerControllerFeign;
    @Resource
    private IAreaFeign areaFeign;
    @Resource
    private ITemplateFeign templateFeign;
    @Resource
    private SeoDao seoDao;

    /**
     * 保存当前登录会员的店铺门户
     */
    @Override
    @Transactional
    public void saveCurrStore(SaveStoreReq saveStoreReq, UserLoginCacheDTO user) {
        //校验（只有是服务提供者且企业类型是企业会员或个人会员才能创建店铺, 支持一个会员多个店铺, 因为一个会员可以有多个服务提供角色）
        if (!Objects.equals(user.getMemberRoleType(), RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            throw new BusinessException(ResponseCodeEnum.COMMODITY_CURR_ROLE_NOT_ALLOW_CREATE_STORE);
        }

        //默认省、市编码都为0（所有/所有）
        List<String> provinceCodes = Stream.of("0").collect(toList());
        List<String> cityCodes = Stream.of("0").collect(toList());
        if (saveStoreReq.getStoreAreas().stream().noneMatch(a -> a.getProvinceCode().equals("0"))) {
            provinceCodes = saveStoreReq.getStoreAreas().stream().map(AreaBO::getProvinceCode).collect(Collectors.toList());
            cityCodes = saveStoreReq.getStoreAreas().stream().map(AreaBO::getCityCode).filter(cityCode -> !cityCode.equals("0")).collect(Collectors.toList());

            List<String> feignCodes = saveStoreReq.getStoreAreas().stream().filter(a -> a.getCityCode().equals("0")).map(AreaBO::getProvinceCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(feignCodes)) {
                WrapperResp<List<String>> cityCodesWrapperResp = areaFeign.findCityCodeByProvinceCode(feignCodes);
                if (cityCodesWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode() && CollUtil.isNotEmpty(cityCodesWrapperResp.getData())) {
                    cityCodes.addAll(cityCodesWrapperResp.getData());
                }
            }
        }

        //调用会员服务 -> 根据会员ID和角色ID获取平台会员的等级、注册年数、信用积分
        MemberFeignReq memberFeignReq = new MemberFeignReq();
        memberFeignReq.setMemberId(user.getMemberId());
        memberFeignReq.setRoleId(user.getMemberRoleId());
        WrapperResp<MemberFeignLrcResp> platformMemberLrcWrapperResp = memberLevelRightCreditControllerFeign.getPlatformMemberLrc(memberFeignReq);
        if (platformMemberLrcWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", platformMemberLrcWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
        }
        //调用会员服务 -> 根据会员Id查询会员注册信息
        MemberFeignIdReq memberFeignIdReq = new MemberFeignIdReq();
        memberFeignIdReq.setMemberId(user.getMemberId());
        WrapperResp<MemberFeignRegisterQueryResp> memberRegisterInfoWrapperResp = memberInnerControllerFeign.getMemberRegisterInfo(memberFeignIdReq);
        if (memberRegisterInfoWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("调用会员服务失败：{}", memberRegisterInfoWrapperResp.getMessage());
        }

        //根据会员ID和角色ID获取会员店铺。flag是判断需不需要调用报表服务的标识（为true说明是创建店铺，false是更新店铺）
        StoreDO storeDO = storeDao.findFirstByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId()).orElse(null);
        //如果会员店铺不为空则是更新店铺，否则就是初始化店铺
        storeDO = storeDO != null ? storeDO : new StoreDO();
        storeDO.setMemberId(user.getMemberId());
        storeDO.setRoleId(user.getMemberRoleId());
        storeDO.setName(saveStoreReq.getName());
        storeDO.setAreaList(saveStoreReq.getStoreAreas());
        storeDO.setLogo(saveStoreReq.getLogo());
        storeDO.setDescribe(saveStoreReq.getDescribe());
        storeDO.setWorkshopPics(saveStoreReq.getWorkshopPics());
        storeDO.setHonorPics(saveStoreReq.getHonorPics());
        storeDO.setAlbumName(saveStoreReq.getAlbumName());
        storeDO.setAlbumUrl(saveStoreReq.getAlbumUrl());
        storeDO.setPromotionPic(saveStoreReq.getPromotionPic());
        storeDO.setPhone(saveStoreReq.getPhone());
        storeDO.setAddress(saveStoreReq.getAddress());
        storeDO.setLng(saveStoreReq.getLng());
        storeDO.setLat(saveStoreReq.getLat());
        storeDO.setAreas(saveStoreReq.getStoreAreas().stream().map(a -> a.getProvince() + "/" + a.getCity()).collect(Collectors.joining("，")));
        storeDO.setProvincesCodeList(String.join("，", provinceCodes));
        storeDO.setCityCodeList(String.join("，", cityCodes));
        storeDO.setMemberName(user.getMemberName());
        storeDO.setLevelTag(platformMemberLrcWrapperResp.getData().getLevelTag());
        storeDO.setRegisterYears(platformMemberLrcWrapperResp.getData().getRegisterYears());
        storeDO.setCreditPoint(platformMemberLrcWrapperResp.getData().getCreditPoint());
        storeDO.setRegisteredCapital(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisteredCapital() : null);
        storeDO.setEstablishmentDate(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getEstablishmentDate() : null);
        storeDO.setBusinessLicence(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getBusinessLicence() : null);
        storeDO.setRegisterArea(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisterArea() : null);
        storeDO.setRegisterAddress(memberRegisterInfoWrapperResp.getData() != null ? memberRegisterInfoWrapperResp.getData().getRegisterAddress() : null);
        storeDao.saveOrUpdate(storeDO);
    }

    /**
     * 获取当前登录会员的店铺列表
     */
    @Override
    public List<StoreV2ListResp> storeList(UserLoginCacheDTO user) {
        return storeDao.findStoreV2ListRespByMemberIdAndRoleId(user.getMemberId(), user.getMemberRoleId());
    }

    @Override
    public StoreDetailResp storeDetail(Long id, UserLoginCacheDTO user) {
        StoreDO storeDO = Optional.ofNullable(storeDao.getById(id)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PT_NOT_FOUND_MEMBER_SHOP));
        if (!(Objects.equals(storeDO.getMemberId(), user.getMemberId()) && Objects.equals(storeDO.getRoleId(), user.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.PT_NOT_FOUND_MEMBER_SHOP);
        }

        return StoreConvert.INSTANCE.toStoreDetailResp(storeDO);
    }

    @Override
    public void updateStatus(CommonStatusReq commonStatusReq, UserLoginCacheDTO user) {
        StoreDO storeDO = Optional.ofNullable(storeDao.getById(commonStatusReq.getId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PT_NOT_FOUND_MEMBER_SHOP));
        if (!(Objects.equals(storeDO.getMemberId(), user.getMemberId()) && Objects.equals(storeDO.getRoleId(), user.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.PT_NOT_FOUND_MEMBER_SHOP);
        }

        // 更新状态
        storeDao.updateStatusById(commonStatusReq.getId(), commonStatusReq.getStatus());
    }

    /**
     * 店铺门户主页
     */
    @Override
    public StoreMainResp storeMain(StoreMainReq storeMainReq, UserLoginCacheDTO user) {
        StoreDO storeDO = Optional.ofNullable(storeDao.getById(storeMainReq.getStoreId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PT_NOT_FOUND_MEMBER_SHOP));

        //拷贝
        StoreMainResp storeMainResp = BeanUtil.copyProperties(storeDO, StoreMainResp.class);

        // 查询装修ID
        Long adornId = adornDao.findIdFirstByShopIdAndStoreIdAndEnabledAndType(storeMainReq.getShopId(), storeMainReq.getStoreId(), Boolean.TRUE, AdornTypeEnum.STORE.getCode());
        storeMainResp.setAdornId(adornId);

        //收藏状态
        if (user != null) {
            storeMainResp.setCollectStatus(storeCollectDao.existsByShopIdAndMemberIdAndUserId(storeDO.getId(), user.getMemberId(), user.getUserId()));
        }

        //调用商品服务 -> 根据会员ID和角色ID集合获取店铺的主营分类
        MemberReq memberReq = new MemberReq();
        memberReq.setMemberId(storeMainResp.getMemberId());
        memberReq.setMemberRoleId(storeMainResp.getRoleId());
        Map<String, StoreResp> map = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityAndCategoryByMemberIdAndMemberRoleId(
                Stream.of(memberReq).collect(toList())
        ));
        if (CollUtil.isNotEmpty(map) && map.get(storeMainResp.getMemberId() + "-" + storeMainResp.getRoleId()) != null) {
            storeMainResp.setCustomerCategoryName(map.get(storeMainResp.getMemberId() + "-" + storeMainResp.getRoleId()).getCustomerCategoryName());
        }

        //获取店铺SEO
        List<SeoDO> seoList = seoDao.findByDoorTypeAndDoorIdAndStatus(
                SeoDoorTypeEnum.SHOP.getCode(), storeDO.getId(), CommonBooleanEnum.YES.getCode()
        );
        //封装店铺SEO(首页、关于我们)
        if (CollUtil.isNotEmpty(seoList)) {
            SeoDO homePage = seoList.stream().filter(a -> a.getType().equals(SeoTypeEnum.HOME_PAGE.getCode())).findFirst().orElse(null);
            SeoDO aboutUs = seoList.stream().filter(a -> a.getType().equals(SeoTypeEnum.ABOUT_US.getCode())).findFirst().orElse(null);
            storeMainResp.setHomePage(homePage);
            storeMainResp.setAboutUs(aboutUs);
        }

        return storeMainResp;
    }

    /**
     * 店铺门户列表
     */
    @Override
    public PageDataResp<StoreListResp> storeList(StoreDataReq req, Long shopId) {

        //搜索条件 - 平台品类ID。调用搜索服务 -> 通过品类查询会员信息（平台品类关联商品，商品关联会员）。
        List<Long> storeIdList = Optional.ofNullable(req.getCategoryId()).map(categoryId -> WrapperUtil.getDataOrThrow(templateFeign.getStoreIdByCategory(shopId, categoryId))).orElseGet(ArrayList::new);

        if (Objects.nonNull(req.getCategoryId()) && CollUtil.isEmpty(storeIdList)) {
            // 当前品类没有店铺
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        Page<StoreDO> page = storeDao.findByStoreIdInAndMemberNameLikeAndNameLikeAndProvinceCodeLikeOrEqAndCityCodeLikeOrEqAndStatusOrderByCreditPointAndCreateTimeDesc(
                storeIdList, req.getMemberName(), req.getName(), req.getProvinceCode(), "0", req.getCityCode(), "0", CommonBooleanEnum.YES.getCode(), req.getSortCreditPoint(), req
        );
        List<StoreDO> list = page.getRecords();

        if (CollUtil.isEmpty(list)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //调用搜索服务 -> 根据店铺ID集合获取店铺最新上架的商品
        StoreIdListReq storeIdListReq = new StoreIdListReq();
        storeIdListReq.setIdList(list.stream().map(StoreDO::getId).collect(Collectors.toList()));
        storeIdListReq.setCount(2);
        storeIdListReq.setShopId(shopId);
        Map<Long, List<EsCommodityResp>> commoditySearchRespMap =  WrapperUtil.getDataOrThrow(templateFeign.getCommodityListByPublishTime(storeIdListReq));

        //调用商品服务 -> 根据会员ID和角色ID集合获取店铺的主营分类
        Map<String, StoreResp> storeRespMap = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityAndCategoryByMemberIdAndMemberRoleId(
                list.stream().map(a -> MemberReq.buildBy(a.getMemberId(), a.getRoleId())).collect(toList())
        ));

        //封装每个店铺最新上架商品、主营分类
        List<StoreListResp> storeListRespList = list.stream().map(a -> {
            StoreListResp storeListResp = BeanUtil.copyProperties(a, StoreListResp.class);

            List<StoreListResp.ProductVO> productVOList = Optional.ofNullable(commoditySearchRespMap).map(o -> o.get(a.getId())).orElseGet(ArrayList::new).stream().map(
                    product -> BeanUtil.copyProperties(product, StoreListResp.ProductVO.class)
            ).collect(toList());
            storeListResp.setProductList(productVOList);

            String customerCategoryName = Optional.ofNullable(storeRespMap).map(o -> o.get(a.getMemberId() + "-" + a.getRoleId())).map(StoreResp::getCustomerCategoryName).orElse("");
            storeListResp.setCustomerCategoryName(customerCategoryName);

            return storeListResp;
        }).collect(toList());

        return new PageDataResp<>(page.getTotal(), storeListRespList);
    }

    /**
     * 店铺门户列表（装修）
     */
    @Override
    public PageDataResp<StoreDO> storeListAdorn(PageDataReq dto, String memberName) {
        Page<StoreDO> page = StringUtils.isNotBlank(memberName) ? storeDao.findByMemberNameContainsOrderByCreateTimeDesc(memberName, dto) : storeDao.findAllOrderByCreateTimeDesc(dto);
        return new PageDataResp<>(page.getTotal(), page.getRecords());
    }

    /**
     * 店铺门户包含商品列表（装修）
     */
    @Override
    public List<StoreInCommodityListAdornResp> storeInCommodityListAdorn(StoreInCommodityListAdornReq qo) {
        //拿到请求参数[ {店铺ID:1, 商品ID:[1,2,3]}, 店铺ID:2, 商品ID:[4,5,6]}, {...}, {...}  ]
        List<StoreInCommodityListAdornReq.StoreInCommodity> storeInCommodityList = qo.getStoreInCommodityList();
        if (CollUtil.isEmpty(storeInCommodityList)) {
            return new ArrayList<>();
        }

        //取出所有店铺ID, 根据店铺ID获取店铺, 再按照店铺ID再集合内的顺序进行排序
        List<Long> storeIdList = storeInCommodityList.stream().map(StoreInCommodityListAdornReq.StoreInCommodity::getStoreId).collect(toList());

        List<StoreDO> storeList = storeDao.findByIdInAndProvincesCodeListLikeOrEqAndCityCodeListLikeOrEq(
                storeIdList, qo.getProvinceCode(), "0", qo.getCityCode(), "0"
        );

        if (CollUtil.isEmpty(storeList)) {
            return new ArrayList<>();
        }
        storeList = storeList.stream().sorted(Comparator.comparingInt(a -> storeIdList.indexOf(a.getId()))).collect(toList());

        //key为店铺ID, value则是每一个店铺ID对应的商品ID
        Map<Long, List<Long>> map = new HashMap<>();
        storeInCommodityList.forEach(a -> {
            if (!CollectionUtils.isEmpty(a.getCommodityIdList())) {
                map.put(a.getStoreId(), a.getCommodityIdList());
            }
        });

        //拿到每个店铺对应商品的ID去调用搜索服务 -> 查询商品
        List<EsCommodityResp> templateResponseList = new ArrayList<>();
        if (CollUtil.isNotEmpty(map)) {
            List<Long> commodityIdLists = map.values().stream().flatMap(Collection::stream).collect(toList());
            TemplateCommoditySearchReq searchRequest = new TemplateCommoditySearchReq();
            searchRequest.setCurrent(1);
            searchRequest.setPageSize(commodityIdLists.size());
            searchRequest.setShopId(qo.getShopId());
            searchRequest.setIdInList(commodityIdLists);
            WrapperResp<PageDataResp<EsCommodityResp>> wrapperResp = templateFeign.searchCommodityList(searchRequest);
            if (wrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode() && CollUtil.isNotEmpty(wrapperResp.getData().getData())) {
                templateResponseList = wrapperResp.getData().getData();
            }
        }

        //组装
        List<EsCommodityResp> finalTemplateResponseList = templateResponseList;

        return storeList.stream().map(a -> {
            StoreInCommodityListAdornResp storeInCommodityListAdornResp = new StoreInCommodityListAdornResp();
            BeanUtils.copyProperties(a, storeInCommodityListAdornResp);

            if (!CollectionUtils.isEmpty(finalTemplateResponseList) && !CollectionUtils.isEmpty(map)) {
                List<Long> commodityIdList = map.get(a.getId());
                List<EsCommodityResp> commodityList = finalTemplateResponseList.stream().filter(b -> !CollectionUtils.isEmpty(commodityIdList) && commodityIdList.contains(b.getId())).collect(toList());
                storeInCommodityListAdornResp.setCommodityVOList(
                        commodityList.stream().map(b -> {
                            StoreInCommodityListAdornResp.CommodityVO commodityVO = new StoreInCommodityListAdornResp.CommodityVO();
                            BeanUtils.copyProperties(b, commodityVO);
                            return commodityVO;
                        }).collect(toList())
                );
            }
            return storeInCommodityListAdornResp;
        }).collect(toList());
    }

    /**
     * 最新加入
     */
    @Override
    public List<StoreDO> newAddStore() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 6);
        return storeDao.findNewAddStore(Page.of(1, 5), calendar.getTimeInMillis()).getRecords();
    }

    /**
     * 收藏/取消收藏
     */
    @Override
    @Transactional
    public void collect(CollectReq dto, UserLoginCacheDTO user) {
        if (dto.getStatus()) {
            if (storeCollectDao.existsByShopIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId())) {
                throw new BusinessException("不能重复收藏，请刷新页面");
            }
            StoreCollectDO collect = new StoreCollectDO();
            collect.setStoreId(dto.getId());
            collect.setMemberId(user.getMemberId());
            collect.setUserId(user.getUserId());
            storeCollectDao.save(collect);
        } else {
            storeCollectDao.deleteByShopIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId());
        }

    }

    /**
     * 收藏列表
     */
    @Override
    public PageDataResp<StoreDO> collectList(PageDataReq dto, UserLoginCacheDTO user) {
        //按收藏时间倒序获取当前用户收藏的店铺ID
        Page<StoreCollectDO> page = storeCollectDao.findByMemberIdAndUserIdOrderByCreateTimeDesc(user.getMemberId(), user.getUserId(), dto);
        List<StoreCollectDO> storeCollectList = page.getRecords();
        if (CollUtil.isEmpty(storeCollectList)) {
            log.info("当前用户没有收藏的店铺");
            return new PageDataResp<>(0L, new ArrayList<>());
        }
        List<Long> storeIdList = storeCollectList.stream().map(StoreCollectDO::getStoreId).collect(toList());

        //根据收藏的店铺ID获取店铺
        List<StoreDO> storeList = storeDao.findAllByIdIn(storeIdList);

        //组装收藏时间
        storeList = storeList.stream().peek(a -> a.setCreateTime(
                storeCollectList.stream().filter(b ->
                        b.getStoreId().equals(a.getId())
                ).map(StoreCollectDO::getCreateTime).findFirst().orElse(null)
        )).sorted(Comparator.comparingInt(a -> storeIdList.indexOf(a.getId()))).collect(Collectors.toList());

        return new PageDataResp<>(page.getTotal(), storeList);
    }

    /**
     * 根据店铺ID获取店铺
     */
    @Override
    public StoreDO findById(CommonIdReq dto) {
        return storeDao.getById(dto.getId());
    }

    /**
     * 根据店铺ID集合获取店铺
     */
    @Override
    public List<StoreDO> findByIdIn(CommonIdListReq dto) {
        return storeDao.listByIds(dto.getIdList()).stream().sorted(Comparator.comparingInt(a -> dto.getIdList().indexOf(a.getId()))).collect(toList());
    }

    /**
     * 根据不包含的店铺ID集合分页获取店铺
     *
     * @param qo
     * @return
     */
    @Override
    public PageDataResp<StoreDO> pageByIdNotIn(CommonIdListPageDataReq qo) {
        Page<StoreDO> page = storeDao.findByIdNotInAndMemberNameContainsOrderByCreateTimeDesc(qo.getIdList(), qo.getName(), qo);
        return new PageDataResp<>(page.getTotal(), page.getRecords());
    }

    /**
     * 根据包含的店铺ID集合分页获取店铺
     */
    @Override
    public PageDataResp<StoreDO> pageByIdIn(CommonIdListPageDataReq qo) {
        Page<StoreDO> page = storeDao.findByIdIn(qo.getIdList(), qo);
        List<StoreDO> list = page.getRecords().stream().sorted(Comparator.comparingInt(a -> qo.getIdList().indexOf(a.getId()))).collect(toList());
        return new PageDataResp<>(page.getTotal(), list);
    }

    /**
     * 根据店铺ID集合分页获取店铺【最新商品】
     */
    @Override
    public PageDataResp<StoreProductResp> pageProductByIdIn(CommonIdListPageDataReq qo, Long shopId) {
        //根据店铺ID集合获取店铺
        Page<StoreDO> page = storeDao.findByIdIn(qo.getIdList(), qo);
        if (CollUtil.isEmpty(page.getRecords())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //调用搜索服务 -> 根据店铺ID集合获取店铺最新上架的商品
        StoreIdListReq storeIdListReq = new StoreIdListReq();
        storeIdListReq.setIdList(page.getRecords().stream().map(StoreDO::getId).collect(Collectors.toList()));
        storeIdListReq.setCount(3);
        storeIdListReq.setShopId(shopId);
        WrapperResp<Map<Long, List<EsCommodityResp>>> wrapperResp = templateFeign.getCommodityListByPublishTime(storeIdListReq);
        if (wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("请求搜索服务失败：{}", wrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_SEARCH_ERROR);
        }

        //拼装
        List<StoreProductResp> list = page.getRecords().stream().sorted(Comparator.comparingInt(a ->
                qo.getIdList().indexOf(a.getId()))
        ).map(a -> {
            //拷贝
            StoreProductResp storeProductResp = new StoreProductResp();
            BeanUtils.copyProperties(a, storeProductResp);

            //根据店铺ID封装每个店铺最新上架商品
            if (wrapperResp.getData() != null && !CollectionUtils.isEmpty(wrapperResp.getData().get(a.getId()))) {
                storeProductResp.setProductList(
                        wrapperResp.getData().get(a.getId()).stream().map(product -> {
                            StoreProductResp.ProductVO productVO = new StoreProductResp.ProductVO();
                            BeanUtils.copyProperties(product, productVO);
                            return productVO;
                        }).collect(Collectors.toList())
                );
            }
            return storeProductResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(page.getTotal(), list);
    }

    /**
     * 根据店铺ID集合获取店铺【主营分类】
     */
    @Override
    public List<StoreMainCategoryResp> findMainCategoryByIdIn(CommonIdListReq dto) {
        //根据店铺ID集合获取店铺
        List<StoreDO> storeList = storeDao.listByIds(dto.getIdList());
        if (CollUtil.isEmpty(storeList)) {
            return new ArrayList<>();
        }

        //调用商品服务 -> 根据会员ID和角色ID集合获取店铺的主营分类
        Map<String, StoreResp> map = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityAndCategoryByMemberIdAndMemberRoleId(
                storeList.stream().map(a -> {
                    MemberReq memberReq = new MemberReq();
                    memberReq.setMemberId(a.getMemberId());
                    memberReq.setMemberRoleId(a.getRoleId());
                    return memberReq;
                }).collect(toList())
        ));

        //封装主营分类并且返回
        return storeList.stream().sorted(Comparator.comparingInt(a ->
                dto.getIdList().indexOf(a.getId())
        )).map(a -> {
            StoreMainCategoryResp storeMainCategoryResp = new StoreMainCategoryResp();
            BeanUtils.copyProperties(a, storeMainCategoryResp);
            if (!CollectionUtils.isEmpty(map) && map.get(a.getMemberId() + "-" + a.getRoleId()) != null) {
                storeMainCategoryResp.setMainCategory(map.get(a.getMemberId() + "-" + a.getRoleId()).getCustomerCategoryName());
            }
            return storeMainCategoryResp;
        }).collect(toList());
    }

    @Override
    public ReportTodayResp getTodayNew() {
        long todayCount = storeDao.countByCreateTimeBetween(DateTimeUtil.getTodayBegin(), DateTimeUtil.getTodayEnd());
        long yesterdayCount = storeDao.countByCreateTimeBetween(DateTimeUtil.getYesterdayBegin(), DateTimeUtil.getYesterdayEnd());

        //计算增长率(相比昨天同比增长率)
        BigDecimal rate = BigDecimal.ZERO;
        if(yesterdayCount > 0){
            //（今日新增-昨日新增）/ 昨日新增 * 100
            rate = BigDecimal.valueOf(todayCount - yesterdayCount).divide(BigDecimal.valueOf(yesterdayCount), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }else{
            if(todayCount > 0){
                rate = BigDecimal.valueOf(100);
            }
        }

        ReportTodayResp reportTodayResp = new ReportTodayResp();
        reportTodayResp.setTodayCount(todayCount);
        reportTodayResp.setRate(rate);
        return reportTodayResp;
    }
}
