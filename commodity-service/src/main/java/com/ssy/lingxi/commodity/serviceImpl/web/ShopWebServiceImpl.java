package com.ssy.lingxi.commodity.serviceImpl.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.commodity.api.model.req.shop.SelfBusinessShopLogoReq;
import com.ssy.lingxi.commodity.api.model.resp.shop.SelfBusinessShopLogoResp;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopDO;
import com.ssy.lingxi.commodity.model.dto.BaseMemberDTO;
import com.ssy.lingxi.commodity.model.req.shop.SelfShopReq;
import com.ssy.lingxi.commodity.model.req.shop.ShopAllAndSuperiorReq;
import com.ssy.lingxi.commodity.model.req.shop.ShopAllReq;
import com.ssy.lingxi.commodity.model.resp.shop.SelfShopListResp;
import com.ssy.lingxi.commodity.model.resp.shop.ShopRuleDetailResp;
import com.ssy.lingxi.commodity.repository.dao.shop.ShopDao;
import com.ssy.lingxi.commodity.service.web.IShopWebService;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopEnvironmentEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignQueryResp;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * web - 商城 - 业务实现层
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/09/26
 */
@Service
@RequiredArgsConstructor
public class ShopWebServiceImpl implements IShopWebService {

    private final ShopDao shopDao;

    @Resource
    private IMemberFeign memberInnerControllerFeign;

    @Override
    public List<SelfShopListResp> selfShopList(String shopName) {

        List<ShopDO> shopList = shopDao.findBySelfAndEnabledAndNameLikeOrderById(
                Boolean.TRUE, Boolean.TRUE, shopName
        );

        if (CollUtil.isEmpty(shopList)) {
            return new ArrayList<>();
        }

        //封装
        return shopList.stream().map(a -> BeanUtil.copyProperties(a, SelfShopListResp.class)).collect(Collectors.toList());
    }

    @Override
    public List<ShopDO> all(ShopAllReq req, UserLoginCacheDTO user) {

        Long memberId = req.getMemberId();
        Long roleId = req.getRoleId();

        //商家（需要自营商城且筛选出属于自己的自营商城）&& 服务消费者没有自营商城
        if ((Objects.isNull(memberId) || Objects.isNull(roleId)) && RoleTypeEnum.SERVICE_PROVIDER.getCode().equals(user.getMemberRoleType())) {
            memberId = user.getMemberId();
            roleId = user.getMemberRoleId();
        }

        Map<Long, ShopDO> shopIdSelfDetailMap = new HashMap<>();
        //获取会员以上级供应商的自营商城, PS: 平台不需要自营商城
        if (!user.getLoginSource().equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode()) && Objects.nonNull(memberId) && Objects.nonNull(roleId)) {
            //调用会员服务，获取当前会员的全部上级会员
            List<MemberFeignQueryResp> listWrapperResp = WrapperUtil.getDataOrThrow(memberInnerControllerFeign.listUpperMembers(new MemberFeignReq(memberId, roleId)));
            Set<MemberAndRoleIdDTO> memberAndRoleIdDTOSet = listWrapperResp.stream().map(m -> new MemberAndRoleIdDTO(m.getMemberId(), m.getRoleId())).collect(Collectors.toSet());
            memberAndRoleIdDTOSet.add(new MemberAndRoleIdDTO(memberId, roleId));
            List<ShopDO> detailList = shopDao.findAllByEnabledAndMemberIdIn(memberAndRoleIdDTOSet);

            shopIdSelfDetailMap.putAll(detailList.stream().collect(Collectors.toMap(ShopDO::getId, o -> o, (entity1, entity2) -> entity1)));
        }

        //是否按当会员类型筛选
        Integer type2 = null;
        if (Boolean.TRUE.equals(req.getIsMemberType()) && (user.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || user.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))) {
                type2 = ShopTypeEnum.ENTERPRISE.getCode();
        }

        List<ShopDO> list = shopDao.findByEnabledAndEnvironmentAndTypeOrderById(
                Boolean.TRUE, req.getEnvironment(), req.getType(), type2
        );

        // 筛选联营商场列表
        List<ShopDO> unionShopList = list.stream().filter(shopDO -> !shopDO.getIsSelf()).collect(Collectors.toList());

        // 筛选当前查询会员的自营商城列表
        List<ShopDO> selfShopList = list.stream().filter(shopDO -> shopIdSelfDetailMap.containsKey(shopDO.getId())).collect(Collectors.toList());

        // 更新ID为自营商城ID和自营商城名称
        selfShopList.forEach(shopDO -> {
            ShopDO shopRuleDetail = shopIdSelfDetailMap.get(shopDO.getId());
            shopDO.setId(shopRuleDetail.getId());
            shopDO.setName(shopRuleDetail.getName());
        });

        // 合并结果
        return CollUtil.unionAll(unionShopList, selfShopList);
    }

    @Override
    public List<ShopDO> allShop(ShopAllAndSuperiorReq req, UserLoginCacheDTO user) {
        List<ShopDO> shopList = shopDao.findByEnabledAndSelfAndTypeNot(
                Boolean.TRUE, Boolean.FALSE, ShopTypeEnum.INFORMATION.getCode()
        );
        //筛选出非自营的商城ID
        List<Long> shopIdList = shopList.stream().map(ShopDO::getId).collect(Collectors.toList());
        //获取上级会员的全部自营商城
        //调用会员服务 ->获取当前会员的全部上级会员
        MemberFeignReq memberFeignVO = new MemberFeignReq();
        memberFeignVO.setMemberId(user.getMemberId());
        memberFeignVO.setRoleId(user.getMemberRoleId());
        WrapperResp<List<MemberFeignQueryResp>> listUpperMembers = memberInnerControllerFeign.listUpperMembers(memberFeignVO);
        List<MemberFeignQueryResp> membersData = listUpperMembers.getData();

        //是否按当会员类型筛选
        Integer type2 = null;
        if (req.getIsMemberType() != null && req.getIsMemberType() && (user.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || user.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode()))) {
                type2 = ShopTypeEnum.ENTERPRISE.getCode();
        }

        List<ShopDO> list = shopDao.findByEnabledIdInAndEnvironmentAndTypeOrderById(
                Boolean.TRUE, shopIdList, req.getEnvironment(), req.getType(), type2
        );

        //step2->获取当前会员以及上级会员自己设置的自营商城的名称
        //商家（需要自营商城且筛选出属于自己的自营商城）&&服务消费者没有自营商城
        Long memberId = null;
        Long roleId = null;
        if (user.getLoginSource().equals(SystemSourceEnum.BUSINESS_WEB.getCode()) && RoleTypeEnum.SERVICE_PROVIDER.getCode().equals(user.getMemberRoleType())) {
            memberId = user.getMemberId();
            roleId = user.getMemberRoleId();
        }

        List<BaseMemberDTO> baseMemberDTOList = Optional.ofNullable(membersData).map(BaseMemberDTO::buildBy).orElse(null);

        List<ShopDO> detailList = shopDao.findByMemberIdEqOrAndMemberRoleIdEqOrAndEnabledOrderById(
                memberId, roleId, baseMemberDTOList, Boolean.TRUE
        );

        List<ShopDO> selfShops = detailList.stream().map(d -> BeanUtil.copyProperties(d, ShopDO.class)).collect(Collectors.toList());
        list.addAll(selfShops);
        //过滤掉行情资讯
        return list.stream().filter(s -> !(s.getIsSelf() != null && s.getIsSelf()) && !ShopTypeEnum.INFORMATION.getCode().equals(s.getType())).collect(Collectors.toList());
    }

    @Override
    public List<ShopDO> findWebEnterpriseSelfShop(UserLoginCacheDTO sysUser) {

        List<ShopDO> shopList = new ArrayList<>();
        if (sysUser == null) {
            return shopList;
        }

        List<ShopDO> detailList = shopDao.findByMemberIdAndMemberRoleIdAndEnabledAndEnvironmentOrderById(
                sysUser.getMemberId(), sysUser.getMemberRoleId(), Boolean.TRUE, ShopEnvironmentEnum.WEB.getCode()
        );

        if (CollUtil.isNotEmpty(detailList)) {
            shopList = detailList.stream().map(d -> BeanUtil.copyProperties(d, ShopDO.class)).collect(Collectors.toList());
        }
        return shopList;
    }

    @Override
    public List<ShopDO> findAppSelfShopByCurrMember(UserLoginCacheDTO user) {
        return shopDao.findByMemberIdAndMemberRoleIdAndEnabledAndEnvironmentOrderById(
                user.getMemberId(), user.getMemberRoleId(), Boolean.TRUE, ShopEnvironmentEnum.APP.getCode()
        );
    }

    @Override
    public List<ShopDO> findAppletsSelfShopByCurrMember(UserLoginCacheDTO user) {
        return shopDao.findByMemberIdAndMemberRoleIdAndEnabledAndEnvironmentOrderById(
                user.getMemberId(), user.getMemberRoleId(), Boolean.TRUE, ShopEnvironmentEnum.APPLETS.getCode()
        );
    }

    @Override
    public List<ShopRuleDetailResp> findSelfShop(UserLoginCacheDTO sysUser, SelfShopReq selfShopReq) {
        return shopDao.findByMemberIdAndMemberRoleIdAndEnabledAndEnvironmentAndNameLikeOrderById(
                sysUser.getMemberId(), sysUser.getMemberRoleId(), Boolean.TRUE, selfShopReq.getEnvironment(), selfShopReq.getShopName()
        );
    }

    @Override
    public List<Long> findSelfBusinessShopId(MemberAndRoleIdDTO dto) {
        return shopDao.findIdByMemberIdAndMemberRoleIdAndEnabled(
                dto.getMemberId(), dto.getRoleId(), Boolean.TRUE
        );
    }

    @Override
    public List<SelfBusinessShopLogoResp> findSelfBusinessShopLogo(SelfBusinessShopLogoReq dto) {
        SelfBusinessShopLogoResp selfBusinessShopLogoResp = Optional.ofNullable(shopDao.findFirstByIdAndMemberIdIn(dto.getShopId(), dto.getList())).orElseGet(SelfBusinessShopLogoResp::new);

        //组装
        return dto.getList().stream().map(a -> {
            SelfBusinessShopLogoResp vo = new SelfBusinessShopLogoResp();
            vo.setMemberId(a.getMemberId());
            vo.setMemberRoleId(a.getRoleId());

            if (Objects.equals(a.getMemberId(), selfBusinessShopLogoResp.getMemberId()) && Objects.equals(a.getRoleId(), selfBusinessShopLogoResp.getMemberRoleId())) {
                vo.setLogoUrl(selfBusinessShopLogoResp.getLogoUrl());
                vo.setName(selfBusinessShopLogoResp.getName());
            }

            return vo;
        }).collect(Collectors.toList());
    }

}
