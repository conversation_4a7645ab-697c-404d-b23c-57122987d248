package com.ssy.lingxi.commodity.serviceImpl.mobile;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.collect.StoreCollectDO;
import com.ssy.lingxi.commodity.entity.do_.shop.StoreDO;
import com.ssy.lingxi.commodity.enums.AdornTypeEnum;
import com.ssy.lingxi.commodity.model.req.common.CollectReq;
import com.ssy.lingxi.commodity.model.req.common.MemberIdAndRoleIdReq;
import com.ssy.lingxi.commodity.model.req.member.StoreMobileDataReq;
import com.ssy.lingxi.commodity.model.req.mobile.StoreInCommodityListMobileReq;
import com.ssy.lingxi.commodity.model.req.mobile.StoreMainReq;
import com.ssy.lingxi.commodity.model.resp.mobile.StoreInCommodityListMobileResp;
import com.ssy.lingxi.commodity.model.resp.mobile.StoreListMobileResp;
import com.ssy.lingxi.commodity.model.resp.mobile.StoreMainMobileResp;
import com.ssy.lingxi.commodity.repository.dao.StoreCollectDao;
import com.ssy.lingxi.commodity.repository.dao.adorn.AdornDao;
import com.ssy.lingxi.commodity.repository.dao.shop.StoreDao;
import com.ssy.lingxi.commodity.service.mobile.IStoreMobileService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.marketing.api.feign.IActivityGoodsFeign;
import com.ssy.lingxi.marketing.api.model.request.ProductTagReq;
import com.ssy.lingxi.marketing.api.model.response.ProductTagResp;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.feign.ITemplateFeign;
import com.ssy.lingxi.product.api.model.req.MemberReq;
import com.ssy.lingxi.product.api.model.req.StoreIdListReq;
import com.ssy.lingxi.product.api.model.req.TemplateCommoditySearchReq;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.api.model.resp.store.StoreResp;
import com.ssy.lingxi.report.api.feign.IReportFeign;
import com.ssy.lingxi.report.api.model.req.PopularShopReq;
import com.ssy.lingxi.report.api.model.resp.PopularShopResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * mobile - 会员店铺 - 业务实现层
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/12/11
 */
@Slf4j
@Service
public class StoreMobileServiceImpl implements IStoreMobileService {

    @Resource
    private StoreDao storeDao;
    @Resource
    private StoreCollectDao storeCollectDao;
    @Resource
    private IReportFeign reportFeign;
    @Resource
    private ITemplateFeign templateFeign;
    @Resource
    private ICommodityFeign commodityFeign;
    @Resource
    private AdornDao adornDao;
    @Resource
    private IActivityGoodsFeign activityGoodsFeign;

    /**
     * APP商城装修(B端)
     * 店铺推荐分页列表
     *
     * @return 操作结果
     */
    @Override
    public StoreMainMobileResp storeMain(StoreMainReq storeMainReq, UserLoginCacheDTO user) {
        //根据店铺ID获取会员店铺
        StoreDO storeDO = storeDao.getById(storeMainReq.getStoreId());
        if (storeDO == null) {
            throw new BusinessException(ResponseCodeEnum.PT_NOT_FOUND_MEMBER_SHOP);
        }

        //拷贝
        StoreMainMobileResp storeMainMobileResp = BeanUtil.copyProperties(storeDO, StoreMainMobileResp.class);

        // 查询装修ID
        Long adornId = adornDao.findIdFirstByShopIdAndStoreIdAndEnabledAndType(storeMainReq.getShopId(), storeMainReq.getStoreId(), Boolean.TRUE, AdornTypeEnum.STORE.getCode());

        storeMainMobileResp.setAdornId(adornId);

        // 有使用装修则查询装修内容
        if (Objects.nonNull(adornId)) {
            Optional.ofNullable(adornDao.getById(adornId)).ifPresent(appAdornDO -> storeMainMobileResp.setAdornContent(appAdornDO.getAdornContent()));
        }

        //收藏状态
        if (user != null) {
            storeMainMobileResp.setCollectStatus(storeCollectDao.existsByShopIdAndMemberIdAndUserId(storeDO.getId(), user.getMemberId(), user.getUserId()));
        }

        //调用商品服务 -> 根据会员ID和角色ID集合获取店铺的主营分类
        try {
            MemberReq memberReq = new MemberReq();
            memberReq.setMemberId(storeMainMobileResp.getMemberId());
            memberReq.setMemberRoleId(storeMainMobileResp.getRoleId());
            Map<String, StoreResp> map = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityAndCategoryByMemberIdAndMemberRoleId(
                    Stream.of(memberReq).collect(toList())
            ));
            if (CollUtil.isNotEmpty(map) && map.get(storeMainMobileResp.getMemberId() + "-" + storeMainMobileResp.getRoleId()) != null) {
                storeMainMobileResp.setCustomerCategoryName(map.get(storeMainMobileResp.getMemberId() + "-" + storeMainMobileResp.getRoleId()).getCustomerCategoryName());
            }
        } catch (Exception e) {
            log.error("调用商品服务出错：{}", e.getMessage());
        }

        return storeMainMobileResp;
    }

    /**
     * 会员店店铺门户列表铺主页
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<StoreListMobileResp> storeList(StoreMobileDataReq req, Long shopId) {

        //搜索条件 - 平台品类ID。调用搜索服务 -> 通过品类查询会员信息（平台品类关联商品，商品关联会员）。
        List<Long> storeIdList = Optional.ofNullable(req.getCategoryId()).map(categoryId -> WrapperUtil.getDataOrThrow(templateFeign.getStoreIdByCategory(shopId, categoryId))).orElseGet(ArrayList::new);

        Page<StoreDO> page = storeDao.findStore(req, storeIdList);
        if (Objects.isNull(page)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        if (CollUtil.isEmpty(page.getRecords())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //调用搜索服务 -> 根据店铺ID集合获取店铺最新上架的商品
        StoreIdListReq storeIdListReq = new StoreIdListReq();
        storeIdListReq.setIdList(page.getRecords().stream().map(StoreDO::getId).collect(Collectors.toList()));
        storeIdListReq.setCount(3);
        storeIdListReq.setShopId(shopId);
        storeIdListReq.setPriceType(req.getPriceType());
        //todo 需要添加区域筛选
        WrapperResp<Map<Long, List<EsCommodityResp>>> wrapperResp = templateFeign.getCommodityListByPublishTime(storeIdListReq);
        if (wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("请求搜索服务失败：{}", wrapperResp.getMessage());
        }

        //封装每个店铺最新上架商品
        List<StoreListMobileResp> storeListMobileRespList = page.getRecords().stream().map(a -> {
            StoreListMobileResp resp = new StoreListMobileResp();
            BeanUtils.copyProperties(a, resp);

            if (wrapperResp.getData() != null && CollUtil.isNotEmpty(wrapperResp.getData().get(a.getId()))) {
                resp.setProductList(
                        wrapperResp.getData().get(a.getId()).stream().map(product -> {
                            StoreListMobileResp.ProductVO productVO = new StoreListMobileResp.ProductVO();
                            BeanUtils.copyProperties(product, productVO);
                            return productVO;
                        }).collect(toList())
                );
            }

            return resp;
        }).collect(toList());

        return new PageDataResp<>(page.getTotal(), storeListMobileRespList);
    }

    /**
     * 人气店铺和最新上架三个商品
     *
     * @param provinceCode 省编码
     * @param cityCode     市编码
     * @return 操作结果
     */
    @Override
    public List<StoreListMobileResp> popularStore(Long shopId, String provinceCode, String cityCode) {
        //调用报表服务 -> 获取人气店铺ID
        PopularShopReq popularShopReq = new PopularShopReq();
        popularShopReq.setCount(10);
        WrapperResp<List<Long>> popularShopIdListWrapperResp = reportFeign.getPopularShopIdList(popularShopReq);
        if (popularShopIdListWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.info("请求报表服务失败:{}", popularShopIdListWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_REPORT_ERROR);
        }
        if (CollUtil.isEmpty(popularShopIdListWrapperResp.getData())) {
            return new ArrayList<>();
        }

        List<StoreDO> list = storeDao.findByIdInAndProvincesCodeListLikeOrEqAndCityCodeListLikeOrEq(
                popularShopIdListWrapperResp.getData(), provinceCode, "0", cityCode, "0"
        );

        //调用搜索服务 -> 根据店铺ID集合获取店铺最新上架的商品
        StoreIdListReq storeIdListReq = new StoreIdListReq();
        storeIdListReq.setIdList(list.stream().map(StoreDO::getId).collect(Collectors.toList()));
        storeIdListReq.setCount(3);
        storeIdListReq.setShopId(shopId);
        storeIdListReq.setProvinceCode(provinceCode);
        storeIdListReq.setCityCode(cityCode);
        WrapperResp<Map<Long, List<EsCommodityResp>>> wrapperResp = templateFeign.getCommodityListBySoldCount(storeIdListReq);
        if (wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("请求搜索服务失败：{}", wrapperResp.getMessage());
        }

        //封装每个店铺最新上架商品
        return list.stream().map(a -> {
            StoreListMobileResp resp = new StoreListMobileResp();
            BeanUtils.copyProperties(a, resp);

            if (wrapperResp.getData() != null && !CollectionUtils.isEmpty(wrapperResp.getData().get(a.getId()))) {
                resp.setProductList(
                        wrapperResp.getData().get(a.getId()).stream().map(product -> {
                            StoreListMobileResp.ProductVO productVO = new StoreListMobileResp.ProductVO();
                            BeanUtils.copyProperties(product, productVO);
                            return productVO;
                        }).collect(toList())
                );
            }

            return resp;
        }).collect(toList());
    }

    /**
     * 人气店铺信息
     *
     * @param shopId 商城id
     * @return 操作结果
     */
    @Override
    public List<PopularShopResp> popularStoreMsg(Long shopId) {
        //调用报表服务 -> 获取人气店铺信息
        PopularShopReq popularShopReq = new PopularShopReq();
        popularShopReq.setCount(10);
        WrapperResp<List<PopularShopResp>> popularShopIdListWrapperResp = reportFeign.getPopularShopList(popularShopReq);
        if (popularShopIdListWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.info("请求报表服务失败:{}", popularShopIdListWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_REPORT_ERROR);
        }
        if (CollUtil.isEmpty(popularShopIdListWrapperResp.getData())) {
            return new ArrayList<>();
        }
        return popularShopIdListWrapperResp.getData();
    }

    /**
     * 店铺门户包含商品列表
     *
     * @param storeInCommodityListMobileReq 请求参数
     * @return 操作结果
     */
    @Override
    public List<StoreInCommodityListMobileResp> storeInCommodityList(StoreInCommodityListMobileReq storeInCommodityListMobileReq) {
        //拿到请求参数[ {店铺ID:1, 商品ID:[1,2,3]}, 店铺ID:2, 商品ID:[4,5,6]}, {...}, {...}  ]
        List<StoreInCommodityListMobileReq.StoreInCommodity> storeInCommodityList = storeInCommodityListMobileReq.getStoreInCommodityList();
        if (CollUtil.isEmpty(storeInCommodityList)) {
            return new ArrayList<>();
        }

        //取出所有店铺ID, 根据店铺ID获取店铺, 再按照店铺ID再集合内的顺序进行排序
        List<Long> storeIdList = storeInCommodityList.stream().map(StoreInCommodityListMobileReq.StoreInCommodity::getStoreId).collect(toList());

        List<StoreDO> storeList = storeDao.findByIdInAndProvincesCodeListLikeOrEqAndCityCodeListLikeOrEq(
                storeIdList, storeInCommodityListMobileReq.getProvinceCode(), "0", storeInCommodityListMobileReq.getCityCode(), "0"
        );

        if (CollUtil.isEmpty(storeList)) {
            return new ArrayList<>();
        }
        storeList = storeList.stream().sorted(Comparator.comparingInt(a -> storeIdList.indexOf(a.getId()))).collect(toList());

        //key为店铺ID, value则是每一个店铺ID对应的商品ID
        Map<Long, List<Long>> map = new HashMap<>();
        storeInCommodityList.forEach(a -> {
            if (!CollectionUtils.isEmpty(a.getCommodityIdList())) {
                map.put(a.getStoreId(), a.getCommodityIdList());
            }
        });

        //拿到每个店铺对应商品的ID去调用搜索服务 -> 查询商品
        List<EsCommodityResp> esCommodityRespList = new ArrayList<>();
        List<ProductTagResp> productTagRespList = new ArrayList<>();
        if (CollUtil.isNotEmpty(map)) {
            //调用搜索服务 -> 查询商品
            List<Long> commodityIdLists = map.values().stream().flatMap(Collection::stream).collect(toList());
            TemplateCommoditySearchReq searchRequest = new TemplateCommoditySearchReq();
            searchRequest.setCurrent(1);
            searchRequest.setPageSize(commodityIdLists.size());
            searchRequest.setShopId(storeInCommodityListMobileReq.getShopId());
            searchRequest.setIdInList(commodityIdLists);
            searchRequest.setProvinceCode(storeInCommodityListMobileReq.getProvinceCode());
            searchRequest.setCityCode(storeInCommodityListMobileReq.getCityCode());
            WrapperResp<PageDataResp<EsCommodityResp>> commoditySearchPageWrapperResp = templateFeign.searchCommodityList(searchRequest);
            if (commoditySearchPageWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode() && CollUtil.isNotEmpty(commoditySearchPageWrapperResp.getData().getData())) {
                esCommodityRespList = commoditySearchPageWrapperResp.getData().getData();
            }

            //调用营销服务 -> 获取商品的最大优惠价格
            ProductTagReq req = new ProductTagReq();
            req.setShopId(storeInCommodityListMobileReq.getShopId());
            req.setProductIds(commodityIdLists);
            WrapperResp<List<ProductTagResp>> productListWrapperResp = activityGoodsFeign.getPreferentialPrice(req);
            if (productListWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode() && CollUtil.isNotEmpty(productListWrapperResp.getData())) {
                productTagRespList = productListWrapperResp.getData();
            }
        }

        //组装
        List<EsCommodityResp> finalEsCommodityRespList = esCommodityRespList;
        List<ProductTagResp> finalProductTagRespList = productTagRespList;

        return storeList.stream().map(a -> {
            StoreInCommodityListMobileResp storeInCommodityListMobileResp = new StoreInCommodityListMobileResp();
            BeanUtils.copyProperties(a, storeInCommodityListMobileResp);

            if (!CollectionUtils.isEmpty(finalEsCommodityRespList) && !CollectionUtils.isEmpty(map)) {
                List<Long> commodityIdList = map.get(a.getId());
                List<EsCommodityResp> commodityList = finalEsCommodityRespList.stream().filter(b ->
                        commodityIdList != null && commodityIdList.contains(b.getId())
                ).collect(toList());
                storeInCommodityListMobileResp.setCommodityVOList(
                        commodityList.stream().map(b -> {
                            StoreInCommodityListMobileResp.CommodityVO commodityVO = new StoreInCommodityListMobileResp.CommodityVO();
                            BeanUtils.copyProperties(b, commodityVO);
                            commodityVO.setPrice(b.getMin());

                            if (!CollectionUtils.isEmpty(finalProductTagRespList)) {
                                BigDecimal price = finalProductTagRespList.stream().filter(c -> c.getProductId().equals(b.getId())).map(ProductTagResp::getPreferentialPrice).findFirst().orElse(b.getMin());
                                commodityVO.setPrice(price);
                            }

                            return commodityVO;
                        }).collect(toList())
                );
            }

            return storeInCommodityListMobileResp;
        }).collect(toList());
    }

    /**
     * 根据会员ID和角色ID获取店铺
     *
     * @param req 请求参数
     * @return 操作结果
     */
    @Override
    public StoreMainMobileResp findByMemberIdAndRoleId(MemberIdAndRoleIdReq req, UserLoginCacheDTO user) {
        //根据会员ID和角色ID获取会员店铺
        StoreDO storeDO = storeDao.findFirstByMemberIdAndRoleId(req.getMemberId(), req.getRoleId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PT_NOT_FOUND_MEMBER_SHOP));

        //拷贝
        StoreMainMobileResp storeMainMobileResp = new StoreMainMobileResp();
        BeanUtils.copyProperties(storeDO, storeMainMobileResp);

        //收藏状态
        if (user != null) {
            storeMainMobileResp.setCollectStatus(storeCollectDao.existsByShopIdAndMemberIdAndUserId(storeDO.getId(), user.getMemberId(), user.getUserId()));
        }

        return storeMainMobileResp;
    }

    /**
     * 根据店铺ID获取店铺
     *
     * @param dto 请求参数
     * @return 操作结果
     */
    @Override
    public StoreDO findById(CommonIdReq dto) {
        return storeDao.getById(dto.getId());
    }

    /**
     * 收藏列表
     *
     * @param pageDataReq 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<StoreDO> collectList(PageDataReq pageDataReq, UserLoginCacheDTO user) {
        //按收藏时间倒序获取当前用户收藏的店铺ID
        Page<StoreCollectDO> page = storeCollectDao.findByMemberIdAndUserIdOrderByCreateTimeDesc(user.getMemberId(), user.getUserId(), pageDataReq);
        List<StoreCollectDO> storeCollectList = page.getRecords();
        if (CollUtil.isEmpty(storeCollectList)) {
            log.info("当前用户没有收藏的店铺");
            return new PageDataResp<>(0L, new ArrayList<>());
        }
        List<Long> storeIdList = storeCollectList.stream().map(StoreCollectDO::getStoreId).collect(toList());

        //根据收藏的店铺ID获取店铺
        List<StoreDO> storeList = storeDao.findAllByIdIn(storeIdList);

        //组装收藏时间
        storeList = storeList.stream().peek(a -> a.setCreateTime(
                storeCollectList.stream().filter(b -> b.getStoreId().equals(a.getId())).map(StoreCollectDO::getCreateTime).findFirst().orElse(null)
        )).sorted(Comparator.comparingInt(a -> storeIdList.indexOf(a.getId()))).collect(Collectors.toList());

        return new PageDataResp<>(page.getTotal(), storeList);
    }

    /**
     * 收藏/取消收藏
     *
     * @param dto 请求参数
     */
    @Override
    @Transactional
    public void collect(CollectReq dto, UserLoginCacheDTO user) {
        if (dto.getStatus()) {
            if (storeCollectDao.existsByShopIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId())) {
                throw new BusinessException("不能重复收藏，请刷新页面");
            }
            StoreCollectDO collect = new StoreCollectDO();
            collect.setStoreId(dto.getId());
            collect.setMemberId(user.getMemberId());
            collect.setUserId(user.getUserId());
            storeCollectDao.save(collect);
        } else {
            storeCollectDao.deleteByShopIdAndMemberIdAndUserId(dto.getId(), user.getMemberId(), user.getUserId());
        }

    }

    /**
     * 新入店铺
     *
     * @param pageDataReq 请求参数
     * @return 操作结果
     */
    @Override
    public PageDataResp<StoreDO> newAddStore(PageDataReq pageDataReq) {
        //获取7天内新加入的店铺
        long time = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -6)).getTime();
        Page<StoreDO> page = storeDao.findByCreateTimeGeOrderByCreateTimeDesc(time, pageDataReq);
        return new PageDataResp<>(page.getTotal(), page.getRecords());
    }


}
