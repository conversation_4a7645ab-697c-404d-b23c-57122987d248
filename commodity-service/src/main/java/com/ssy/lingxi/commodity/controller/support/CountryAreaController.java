package com.ssy.lingxi.commodity.controller.support;

import com.ssy.lingxi.commodity.api.model.resp.support.CountryAreaResp;
import com.ssy.lingxi.commodity.model.req.support.CountryAreaListDataReq;
import com.ssy.lingxi.commodity.model.req.support.CountryAreaUpdateStatusReq;
import com.ssy.lingxi.commodity.service.support.ICountryAreaService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 国家(地区)
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/5
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(CountryAreaController.PATH_PREFIX)
public class CountryAreaController {

    public static final String PATH_PREFIX = ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/countryArea/";

    private final ICountryAreaService countryAreaService;

    /**
     * 查询列表
     * @param countryAreaListReq 参数
     */
    @GetMapping(value = "getCountryAreaSelectList")
    public WrapperResp<List<CountryAreaResp>> getCountryAreaSelectList(CountryAreaListDataReq countryAreaListReq) {
        return WrapperUtil.success(countryAreaService.getCountryAreaSelectList(countryAreaListReq));
    }

    /**
     * 查询列表
     * @param countryAreaListReq 参数
     */
    @GetMapping(value = "getCountryAreaList")
    public WrapperResp<PageDataResp<CountryAreaResp>> getCountryAreaList(CountryAreaListDataReq countryAreaListReq) {
        return WrapperUtil.success(countryAreaService.getCountryAreaList(countryAreaListReq));
    }

    /**
     * 启用/停用
     * @param countryAreaUpdateStatusReq 参数
     */
    @PostMapping(value = "updateStatus")
    public WrapperResp<Boolean> updateStatus(@RequestBody @Valid CountryAreaUpdateStatusReq countryAreaUpdateStatusReq) {
        return WrapperUtil.success(countryAreaService.updateStatus(countryAreaUpdateStatusReq));
    }
}
