package com.ssy.lingxi.commodity.repository.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.collect.MemberLogisticsCollectDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IMemberLogisticsCollectMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.springframework.stereotype.Repository;

/**
* <AUTHOR>
*/
@Repository
public class MemberLogisticsCollectDao extends CommonServiceImpl<IMemberLogisticsCollectMapper, MemberLogisticsCollectDO> {

    public Boolean existsByLogisticsIdAndMemberIdAndUserId(Long logisticsId, Long memberId, Long userId) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberLogisticsCollectDO.class)
                        .select(MemberLogisticsCollectDO::getId)
                        .eq(MemberLogisticsCollectDO::getLogisticsId, logisticsId).eq(MemberLogisticsCollectDO::getMemberId, memberId)
                        .eq(MemberLogisticsCollectDO::getUserId, userId)
        ).map(MemberLogisticsCollectDO::getId).orElse(0L) > 0;
    }

    public void deleteByLogisticsIdAndMemberIdAndUserId(Long logisticsId, Long memberId, Long userId) {
        this.remove(
                Wrappers.lambdaQuery(MemberLogisticsCollectDO.class).eq(MemberLogisticsCollectDO::getLogisticsId, logisticsId).eq(MemberLogisticsCollectDO::getMemberId, memberId)
                        .eq(MemberLogisticsCollectDO::getUserId, userId)
        );
    }

    public Page<MemberLogisticsCollectDO> findByMemberIdAndUserIdOrderByCreateTimeDesc(Long memberId, Long userId, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberLogisticsCollectDO.class).eq(MemberLogisticsCollectDO::getMemberId, memberId).eq(MemberLogisticsCollectDO::getUserId, userId)
                        .orderByDesc(MemberLogisticsCollectDO::getCreateTime)
        );
    }
}




