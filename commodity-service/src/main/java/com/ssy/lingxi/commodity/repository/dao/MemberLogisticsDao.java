package com.ssy.lingxi.commodity.repository.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.door.MemberLogisticsDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IMemberLogisticsMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
* <AUTHOR>
*/
@Repository
public class MemberLogisticsDao extends CommonServiceImpl<IMemberLogisticsMapper, MemberLogisticsDO> {

    public Page<MemberLogisticsDO> findAllByIdNotInAndMemberNameContains(List<Long> idList, String memberName, PageDataReq pageDataReq) {
        return this.page(
                Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberLogisticsDO.class).notIn(MemberLogisticsDO::getId, idList).like(MemberLogisticsDO::getMemberName, "%" + memberName + "%")
        );
    }

    public Page<MemberLogisticsDO> findAllByIdNotIn(List<Long> idList, PageDataReq pageDataReq) {
        return this.page(
                Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberLogisticsDO.class).notIn(MemberLogisticsDO::getId, idList)
        );
    }

    public Page<MemberLogisticsDO> findAllByIdIn(List<Long> idList, PageDataReq pageDataReq) {
        return this.page(
                Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberLogisticsDO.class).in(MemberLogisticsDO::getId, idList)
        );
    }

    public Optional<MemberLogisticsDO> findFirstByMemberIdAndRoleId(Long memberId, Long roleId) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberLogisticsDO.class).eq(MemberLogisticsDO::getMemberId, memberId).eq(MemberLogisticsDO::getRoleId, roleId)
        );
    }

    public List<MemberLogisticsDO> findByRoleId(Long roleId) {
        return this.list(
                Wrappers.lambdaQuery(MemberLogisticsDO.class).eq(MemberLogisticsDO::getRoleId, roleId)
        );
    }

    public Page<MemberLogisticsDO> findByProvinceCodeListLikeOrEqAndCityCodeLikeOrEqOrderByCreateTimeDesc(String provinceCode, String provinceCodeOr, String cityCode, String cityCodeOr, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberLogisticsDO.class).and(Objects.nonNull(provinceCode), wrapper -> wrapper.like(MemberLogisticsDO::getProvincesCodeList, "%" + provinceCode + "%").or().eq(MemberLogisticsDO::getProvincesCodeList, provinceCodeOr))
                        .and(Objects.nonNull(cityCode), wrapper -> wrapper.like(MemberLogisticsDO::getCityCodeList, "%" + cityCode + "%").or().eq(MemberLogisticsDO::getCityCodeList, cityCodeOr))
                        .orderByDesc(MemberLogisticsDO::getCreateTime)
        );
    }

    public List<MemberLogisticsDO> findByIdInAndProvinceCodeListLikeOrEqAndCityCodeLikeOrEq(List<Long> idList, String provinceCode, String provinceCodeOr, String cityCode, String cityCodeOr) {
        return this.list(
                Wrappers.lambdaQuery(MemberLogisticsDO.class).in(CollUtil.isNotEmpty(idList), MemberLogisticsDO::getId, idList)
                        .and(Objects.nonNull(provinceCode), wrapper -> wrapper.like(MemberLogisticsDO::getProvincesCodeList, "%" + provinceCode + "%").or().eq(MemberLogisticsDO::getProvincesCodeList, provinceCodeOr))
                        .and(Objects.nonNull(cityCode), wrapper -> wrapper.like(MemberLogisticsDO::getCityCodeList, "%" + cityCode + "%").or().eq(MemberLogisticsDO::getCityCodeList, cityCodeOr))
        );
    }
}




