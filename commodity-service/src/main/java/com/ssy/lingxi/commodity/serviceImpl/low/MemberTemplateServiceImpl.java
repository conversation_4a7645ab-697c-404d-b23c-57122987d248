package com.ssy.lingxi.commodity.serviceImpl.low;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.low.MemberContentDO;
import com.ssy.lingxi.commodity.entity.do_.low.MemberTemplateDO;
import com.ssy.lingxi.commodity.model.req.low.*;
import com.ssy.lingxi.commodity.model.resp.low.TemplateDetailResp;
import com.ssy.lingxi.commodity.model.resp.low.TemplateResp;
import com.ssy.lingxi.commodity.repository.dao.MemberContentDao;
import com.ssy.lingxi.commodity.repository.dao.MemberTemplateDao;
import com.ssy.lingxi.commodity.service.low.IMemberTemplateService;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/13 16:02
 **/
@Service
@Slf4j
public class MemberTemplateServiceImpl implements IMemberTemplateService {

    @Resource
    private MemberTemplateDao memberTemplateDao;

    @Resource
    private MemberContentDao memberContentDao;

    @Resource
    private PlatformTransactionManager platformTransactionManager;

    /**
     * 查询模板
     */
    @Override
    public PageDataResp<TemplateResp> page(TemplateDataReq templateQuery) {
        Page<MemberTemplateDO> templateDOPage = memberTemplateDao.findPageByMemberIdAndRoleIdAndMenuPathOrderById(templateQuery);

        List<TemplateResp> respList = templateDOPage.getRecords().stream().map(this::createTemplateResponse).collect(Collectors.toList());

        return new PageDataResp<>(templateDOPage.getTotal(), respList);
    }

    /**
     * 构造TemplateResponse对象
     *
     * @param memberTemplate memberTemplate
     * @return TemplateResponse
     */
    private TemplateResp createTemplateResponse(MemberTemplateDO memberTemplate) {
        TemplateResp templateResponse = new TemplateResp();
        templateResponse.setId(memberTemplate.getId());
        templateResponse.setMemberId(memberTemplate.getMemberId());
        templateResponse.setRoleId(memberTemplate.getRoleId());
        templateResponse.setTemplateName(memberTemplate.getTemplateName());
        templateResponse.setContentId(memberTemplate.getContentId());
        templateResponse.setMenuPath(memberTemplate.getMenuPath());
        templateResponse.setStatus(memberTemplate.getStatus());
        return templateResponse;
    }


    /**
     * 新增模板
     *
     * @param templateAddRequest 新增模板请求对象
     * @return true-成功 false-失败
     */
    @Override
    public Boolean add(TemplateAddReq templateAddRequest) {
        // 查重，模板名称不能重复
        checkRepeat(templateAddRequest);
        // 新增
        saveTemplate(templateAddRequest);

        return Boolean.TRUE;
    }

    /**
     * 新增模板
     *
     * @param templateAddRequest 新增模板请求对象
     */
    protected void saveTemplate(TemplateAddReq templateAddRequest) {
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        transactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);

        MemberTemplateDO memberTemplate = new MemberTemplateDO();
        memberTemplate.setMenuPath(templateAddRequest.getMenuPath());
        memberTemplate.setMemberId(templateAddRequest.getMemberId());
        memberTemplate.setRoleId(templateAddRequest.getRoleId());
        memberTemplate.setTemplateName(templateAddRequest.getTemplateName());
        memberTemplate.setStatus(templateAddRequest.getStatus());

        try {
            // 如果修改为生效，则重置同纬度的其他模板为失效
            if (CommonBooleanEnum.YES.getCode().equals(memberTemplate.getStatus())) {
                resetStatus(templateAddRequest);
            }

            // 生成模板内容id
            Long contentId = saveContent(templateAddRequest.getContent());
            memberTemplate.setContentId(contentId);

            memberTemplateDao.save(memberTemplate);

            // 手动提交事务
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            log.error("新增模板失败，回滚事务，templateAddRequest:{}", templateAddRequest);
            platformTransactionManager.rollback(transactionStatus);
        }
    }

    /**
     * 保存模板内容
     *
     * @param content 模板内容
     * @return 新增的主键id
     */
    protected Long saveContent(String content) {
        MemberContentDO memberContent = new MemberContentDO();
        memberContent.setContent(content);
        memberContentDao.save(memberContent);
        return memberContent.getId();
    }

    /**
     * 重置其他同一维度的模板状态
     * 同模块同菜单同页面类型的自定义模板的其他模板的状态设置为不生效
     *
     * @param templateAddRequest 新增模板请求对象
     */
    protected void resetStatus(TemplateAddReq templateAddRequest) {

        memberTemplateDao.updateStatusByMemberIdAndRoleIdAndMenuPath(
                CommonBooleanEnum.NO.getCode(), templateAddRequest.getMemberId(), templateAddRequest.getRoleId(), templateAddRequest.getMenuPath()
        );
    }


    /**
     * 新增-根据模板名称查重
     *
     * @param templateAddRequest 新增模板请求对象
     */
    private void checkRepeat(TemplateAddReq templateAddRequest) {
        MemberTemplateDO memberTemplate = memberTemplateDao.findFirstByTemplateNameAndMemberIdAndRoleIdAndMenuPath(
                templateAddRequest.getTemplateName(), templateAddRequest.getMemberId(), templateAddRequest.getRoleId(), templateAddRequest.getMenuPath()
        ).orElse(null);

        if (Objects.nonNull(memberTemplate)) {
            log.error("新增模板失败，模板名称已存在,templateAddRequest:{}", templateAddRequest);
            throw new BusinessException("模板名称重复");
        }
    }

    /**
     * 修改-根据模板名称查重
     *
     * @param templateUpdateRequest 修改模板请求对象
     */
    private void checkRepeat(TemplateUpdateReq templateUpdateRequest) {

        MemberTemplateDO memberTemplate = memberTemplateDao.findFirstByTemplateNameAndMemberIdAndRoleIdAndMenuPathAndIdNe(
                templateUpdateRequest.getTemplateName(), templateUpdateRequest.getMemberId(), templateUpdateRequest.getRoleId(), templateUpdateRequest.getMenuPath(), templateUpdateRequest.getId()
        ).orElse(null);

        if (Objects.nonNull(memberTemplate)) {
            log.error("修改模板失败，模板名称已存在,templateUpdateRequest:{}", templateUpdateRequest);
            throw new BusinessException("模板名称重复");
        }
    }


    /**
     * 修改模板
     *
     * @param templateUpdateRequest 修改模板请求对象
     * @return true-成功 false-失败
     */
    @Override
    public Boolean update(TemplateUpdateReq templateUpdateRequest) {
        // 查询模板
        MemberTemplateDO memberTemplate = findTemplate(
                templateUpdateRequest.getId(),
                templateUpdateRequest.getMemberId(),
                templateUpdateRequest.getRoleId()
        );
        // 查重
        checkRepeat(templateUpdateRequest);
        // 修改模板
        updateTemplate(templateUpdateRequest, memberTemplate);

        return Boolean.TRUE;
    }

    /**
     * 修改模板
     *
     * @param templateUpdateRequest 修改模板请求对象
     * @param memberTemplate        要修改的模板对象
     */
    private void updateTemplate(TemplateUpdateReq templateUpdateRequest, MemberTemplateDO memberTemplate) {
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        transactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);

        memberTemplate.setId(templateUpdateRequest.getId());
        memberTemplate.setMemberId(templateUpdateRequest.getMemberId());
        memberTemplate.setRoleId(templateUpdateRequest.getRoleId());
        memberTemplate.setTemplateName(templateUpdateRequest.getTemplateName());
        memberTemplate.setStatus(templateUpdateRequest.getStatus());
        memberTemplate.setMenuPath(templateUpdateRequest.getMenuPath());

        try {
            // 如果修改为生效，则重置同纬度的其他模板为失效
            if (CommonBooleanEnum.YES.getCode().equals(memberTemplate.getStatus())) {
                resetStatus(templateUpdateRequest);
            }

            // 重新生成模板内容id
            Long contentId = resetContent(memberTemplate.getContentId(), templateUpdateRequest.getContent());
            memberTemplate.setContentId(contentId);

            memberTemplateDao.saveOrUpdate(memberTemplate);

            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            log.error("修改模板失败，回滚事务，templateUpdateRequest:{}", templateUpdateRequest);
            platformTransactionManager.rollback(transactionStatus);
        }
    }

    /**
     * 重置模板内容
     *
     * @param contentId 旧模板内容id
     * @param content   新模板内容
     * @return 新模板内容id
     */
    private Long resetContent(Long contentId, String content) {
        memberContentDao.removeById(contentId);
        MemberContentDO memberContent = new MemberContentDO();
        memberContent.setContent(content);
        memberContentDao.save(memberContent);
        return memberContent.getId();
    }

    /**
     * 重置其他同一维度的模板状态
     * 同模块同菜单同页面类型的自定义模板的其他模板的状态设置为不生效
     *
     * @param templateUpdateRequest 修改模板请求对象
     */
    private void resetStatus(TemplateUpdateReq templateUpdateRequest) {

        memberTemplateDao.updateStatusByMemberIdAndRoleIdAndMenuPath(
                CommonBooleanEnum.NO.getCode(), templateUpdateRequest.getMemberId(), templateUpdateRequest.getRoleId(), templateUpdateRequest.getMenuPath()
        );
    }

    /**
     * 查询模板
     *
     * @param id       模板id
     * @param memberId 会员id
     * @param roleId   角色id
     * @return 模板
     */
    private MemberTemplateDO findTemplate(Long id, Long memberId, Long roleId) {
        // 防止水平越权
        MemberTemplateDO memberTemplate = memberTemplateDao.findFirstByIdAndMemberIdAndRoleId(
                id, memberId, roleId
        ).orElse(null);

        if (Objects.isNull(memberTemplate)) {
            log.error("查询模板失败，模板不存在，id:{},memberId:{},roleId:{}", id, memberId, roleId);
            throw new BusinessException("模板不存在");
        }
        return memberTemplate;
    }

    /**
     * 删除模板
     *
     * @param templateDeleteRequest 删除模板请求对象
     * @return true-成功 false-失败
     */
    @Override
    public Boolean delete(TemplateDeleteReq templateDeleteRequest) {
        MemberTemplateDO template = findTemplate(
                templateDeleteRequest.getId(), templateDeleteRequest.getMemberId(), templateDeleteRequest.getRoleId()
        );
        memberTemplateDao.removeById(template.getId());
        return Boolean.TRUE;
    }

    /**
     * 模板详情
     *
     * @param templateDetailQuery 模板详情请求对象
     * @return 模板详情
     */
    @Override
    public TemplateDetailResp detail(TemplateDetailReq templateDetailQuery) {
        Long id = templateDetailQuery.getId();
        Long memberId = templateDetailQuery.getMemberId();
        Long roleId = templateDetailQuery.getRoleId();
        MemberTemplateDO template = findTemplate(id, memberId, roleId);
        MemberContentDO memberContent = findContentByContentId(template.getContentId());

        return createTemplateDetailResponse(template, memberContent);
    }

    /**
     * 根据内容id查询内容
     *
     * @param contentId 内容id
     * @return 模板内容对象
     */
    private MemberContentDO findContentByContentId(Long contentId) {
        return memberContentDao.getById(contentId);
    }

    /**
     * 创建模板详情对象
     *
     * @param template      模板对象
     * @param memberContent 模板内容对象
     * @return 模板详情对象
     */
    private TemplateDetailResp createTemplateDetailResponse(MemberTemplateDO template, MemberContentDO memberContent) {
        TemplateDetailResp templateDetailResponse = new TemplateDetailResp();
        templateDetailResponse.setId(template.getId());
        templateDetailResponse.setTemplateName(template.getTemplateName());
        templateDetailResponse.setStatus(template.getStatus());
        templateDetailResponse.setMenuPath(template.getMenuPath());
        if (Objects.nonNull(memberContent)) {
            templateDetailResponse.setContent(memberContent.getContent());
        }
        return templateDetailResponse;
    }

    /**
     * 修改模板状态
     *
     * @param templateUpdateStatusRequest 修改模板状态请求对象
     * @return true-成功 false-失败
     */
    @Override
    public Boolean updateStatus(TemplateUpdateStatusReq templateUpdateStatusRequest) {
        DefaultTransactionDefinition transactionDefinition = new DefaultTransactionDefinition();
        transactionDefinition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);

        TemplateDetailResp templateDetailResponse = findTemplateDetail(templateUpdateStatusRequest.getId(), templateUpdateStatusRequest.getMemberId(), templateUpdateStatusRequest.getRoleId());
        TemplateUpdateReq templateUpdateRequest = createTemplateUpdateRequest(templateDetailResponse);
        templateUpdateRequest.setMemberId(templateUpdateStatusRequest.getMemberId());
        templateUpdateRequest.setRoleId(templateUpdateStatusRequest.getRoleId());
        templateUpdateRequest.setStatus(templateUpdateStatusRequest.getStatus());

        try {
            if (CommonBooleanEnum.YES.getCode().equals(templateUpdateRequest.getStatus())) {
                resetStatus(templateUpdateRequest);
            }

            update(templateUpdateRequest);
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            log.error("修改状态失败！templateUpdateStatusRequest:{}", templateUpdateStatusRequest);
            platformTransactionManager.rollback(transactionStatus);
        }

        return Boolean.TRUE;
    }

    /**
     * 构建修改模板对象
     *
     * @param templateDetailResponse 模板详情对象
     * @return 修改模板请求对象
     */
    private TemplateUpdateReq createTemplateUpdateRequest(TemplateDetailResp templateDetailResponse) {
        TemplateUpdateReq templateUpdateRequest = new TemplateUpdateReq();
        templateUpdateRequest.setId(templateDetailResponse.getId());
        templateUpdateRequest.setMenuPath(templateDetailResponse.getMenuPath());
        templateUpdateRequest.setTemplateName(templateDetailResponse.getTemplateName());
        templateUpdateRequest.setContent(templateDetailResponse.getContent());
        templateUpdateRequest.setStatus(templateDetailResponse.getStatus());
        return templateUpdateRequest;
    }

    /**
     * 查询模板详情
     *
     * @param id       模板id
     * @param memberId 会员id
     * @param roleId   角色id
     * @return 模板详情
     */
    private TemplateDetailResp findTemplateDetail(Long id, Long memberId, Long roleId) {
        TemplateDetailReq templateDetailQuery = new TemplateDetailReq();
        templateDetailQuery.setId(id);
        templateDetailQuery.setMemberId(memberId);
        templateDetailQuery.setRoleId(roleId);
        return detail(templateDetailQuery);
    }

    /**
     * 根据查询条件查出唯一的模板详情
     *
     * @param templateContentQuery 模板内容请求对象
     * @return 模板详情
     */
    @Override
    public TemplateDetailResp contentDetail(TemplateContentReq templateContentQuery) {
        MemberTemplateDO template = findTemplateDetail(templateContentQuery);
        MemberContentDO memberContent = findContentByContentId(template.getContentId());
        return createTemplateDetailResponse(template, memberContent);

    }

    /**
     * 根据条件查询已启用的模板
     *
     * @param templateContentQuery templateContentQuery
     * @return 模板
     */
    private MemberTemplateDO findTemplateDetail(TemplateContentReq templateContentQuery) {
        Long memberId = templateContentQuery.getMemberId();
        Long roleId = templateContentQuery.getRoleId();
        String menuUrl = templateContentQuery.getMenuPath();
        // 防止水平越权

        MemberTemplateDO memberTemplate = memberTemplateDao.findFirstByMemberIdAndRoleIdAndMenuPathAndStatus(
                memberId, roleId, menuUrl, CommonBooleanEnum.YES.getCode()
        ).orElse(null);

        if (Objects.isNull(memberTemplate)) {
            log.error("查询模板失败，模板不存在，memberId:{},roleId:{},menuUrl:{}", memberId, roleId, menuUrl);
            throw new BusinessException(ResponseCodeEnum.COMMODITY_LOW_TEMPLATE_NOT_EXIST);
        }
        return memberTemplate;
    }
}
