package com.ssy.lingxi.commodity.repository.dao.shop;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.api.model.resp.shop.SelfBusinessShopLogoResp;
import com.ssy.lingxi.commodity.api.model.resp.shop.ShopDetailResp;
import com.ssy.lingxi.commodity.api.model.resp.shop.ShopOpenMroResp;
import com.ssy.lingxi.commodity.entity.do_.BaseDO;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopDO;
import com.ssy.lingxi.commodity.handler.convert.ShopConvert;
import com.ssy.lingxi.commodity.model.dto.AbilitySelfShopListDTO;
import com.ssy.lingxi.commodity.model.dto.BaseMemberDTO;
import com.ssy.lingxi.commodity.model.req.shop.*;
import com.ssy.lingxi.commodity.model.resp.ShopBListResp;
import com.ssy.lingxi.commodity.model.resp.shop.*;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.shop.IShopMapper;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.dto.SelfShopRedisDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.manage.ShopPropertyEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商城相关 - 数据处理层
 *
 * <AUTHOR>
 */
@Repository
public class ShopDao extends CommonServiceImpl<IShopMapper, ShopDO> {

    /**
     * 分页查询商城列表
     */
    public PageDataResp<ShopInfoResp> shopList(ShopPageListDataReq shopPageListReq) {
        Page<ShopInfoResp> page = this.baseMapper.shopList(
                Page.of(shopPageListReq.getCurrent(), shopPageListReq.getPageSize()),
                shopPageListReq
        );
        return new PageDataResp<>(page.getTotal(), page.getRecords());
    }

    /**
     * 查询商城列表
     */
    @SuppressWarnings("unchecked")
    public List<ShopListThinResp> findShopListThinRespByEnabledAndTypeAndSelf(Boolean enabled, Integer type, Boolean isSelf) {
        return this.list(Wrappers.lambdaQuery(ShopDO.class).select(
                ShopDO::getId, ShopDO::getName, ShopDO::getProperty, ShopDO::getEnvironment, ShopDO::getUrl, BaseDO::getCreateTime
        ).eq(ShopDO::getEnabled, enabled).eq(ShopDO::getType, type).eq(ShopDO::getIsSelf, isSelf).orderByAsc(ShopDO::getId)).stream().map(
                ShopConvert.INSTANCE::toShopListThinResp
        ).collect(Collectors.toList());
    }

    /**
     * 查询商城店铺列表
     */
    public List<ShopStoreListResp> findShopStoreListRespByEnabledAndTypeAndSelf(Long storeId, Boolean enabled, Integer type, Boolean isSelf) {
        return baseMapper.findShopStoreListRespByEnabledAndTypeAndSelf(storeId, enabled, type, isSelf);
    }

    /**
     * 自营商城列表
     */
    public PageDataResp<SelfShopInfoV2Resp> selfShopList(SelfShopPageListReq selfShopPageListReq) {

        com.baomidou.mybatisplus.extension.plugins.pagination.Page<SelfShopInfoV2Resp> page = this.baseMapper.selfShopList(
                com.baomidou.mybatisplus.extension.plugins.pagination.Page.of(selfShopPageListReq.getCurrent(), selfShopPageListReq.getPageSize()),
                selfShopPageListReq
        );

        return new PageDataResp<>(page.getTotal(), page.getRecords());
    }

    /**
     * 商城信息
     */
    public ShopInfoResp shopInfo(Long shopId) {
        return this.baseMapper.shopInfo(shopId);
    }

    /**
     * 查询商城
     */
    public List<ShopDOResp> findShop(FindShopReq findShopReq) {
        List<ShopDO> shopDOList = this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getEnabled, Boolean.TRUE)
                        // 筛选商城环境
                        .eq(Objects.nonNull(findShopReq.getEnvironment()), ShopDO::getEnvironment, findShopReq.getEnvironment())
                        // 筛选门户类型
                        .in(Objects.nonNull(findShopReq.getDoorType()), ShopDO::getType, ShopTypeEnum.findShopTypeListByDoorType(findShopReq.getDoorType()))
                        // 筛选自营商城
                        .eq(Objects.nonNull(findShopReq.getIsSelf()), ShopDO::getIsSelf, findShopReq.getIsSelf())
        );

        return shopDOList.stream().map(ShopDOResp::buildBy).collect(Collectors.toList());
    }

    /**
     * 更新商城是否默认
     */
    public void updateIsDefaultByIsDefaultAndEnvironment(Boolean isDefault, Boolean oldIsDefault, Integer environment) {
        this.update(Wrappers.lambdaUpdate(ShopDO.class).set(ShopDO::getIsDefault, isDefault).eq(ShopDO::getIsDefault, oldIsDefault).eq(ShopDO::getEnvironment, environment));
    }

    /**
     * 查询已启用商城列表
     */
    public List<ShopDO> findByEnabled(Boolean enabled) {
        return Optional.ofNullable(this.list(Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getEnabled, enabled))).orElseGet(ArrayList::new);
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findAllByTypeAndEnabled(Integer type, Boolean enabled) {
        return Optional.ofNullable(this.list(Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getType, type).eq(ShopDO::getEnabled, enabled))).orElseGet(ArrayList::new);
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByTypeAndEnabledAndEnvironment(Integer shopType, Boolean enabled, Integer environment) {
        return Optional.ofNullable(this.list(Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getType, shopType).eq(ShopDO::getEnabled, enabled).eq(ShopDO::getEnvironment, environment))).orElseGet(ArrayList::new);
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findAllByEnvironmentAndEnabled(Integer environment, Boolean enabled) {
        return Optional.ofNullable(this.list(Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getEnvironment, environment).eq(ShopDO::getEnabled, enabled))).orElseGet(ArrayList::new);
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findAllByTypeInAndEnvironmentAndEnabled(List<Integer> typeList, Integer environment, Boolean enabled) {
        return Optional.ofNullable(this.list(Wrappers.lambdaQuery(ShopDO.class).in(ShopDO::getType, typeList).eq(ShopDO::getEnvironment, environment).eq(ShopDO::getEnabled, enabled))).orElseGet(ArrayList::new);
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findAllByShopListReqOrderByIdAsc(ShopListReq request, Boolean enabled) {
        return Optional.ofNullable(this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getType, request.getType()).eq(ShopDO::getEnvironment, request.getEnvironment())
                        .eq(ShopDO::getProperty, request.getProperty()).eq(ShopDO::getEnabled, enabled).eq(Objects.nonNull(request.getIsSelf()), ShopDO::getIsSelf, request.getIsSelf())
                        .like(Objects.nonNull(request.getName()), ShopDO::getName, "%" + request.getName() + "%").orderByAsc(ShopDO::getId)
        )).orElseGet(ArrayList::new);
    }

    /**
     * 查询商城是否开启MRO
     */
    @SuppressWarnings("unchecked")
    public List<ShopOpenMroResp> findIdAndOpenMroByIdInAndEnabled(List<Long> idList, Boolean enabled) {
        return Optional.ofNullable(this.list(
                Wrappers.lambdaQuery(ShopDO.class).select(ShopDO::getId, ShopDO::getIsOpenMro).in(ShopDO::getId, idList).eq(ShopDO::getEnabled, enabled)
        )).orElseGet(ArrayList::new).stream().map(
                shopDO -> new ShopOpenMroResp(shopDO.getId(), shopDO.getIsOpenMro())
        ).collect(Collectors.toList());
    }

    /**
     * 查询商城ID
     */
    @SuppressWarnings("unchecked")
    public Long findShopIdByEnabledAndFunc(Boolean enabled, SFunction<ShopDO, Long> sFunction, Long sFunctionId) {
        return this.getFirst(
                Wrappers.lambdaQuery(ShopDO.class).select(ShopDO::getId).eq(ShopDO::getEnabled, enabled).eq(sFunction, sFunctionId), Convert::toLong
        ).orElse(null);
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByTypeAndEnabledAndPropertyAndSelf(Integer type, Boolean enabled, Integer property, Boolean isSelf) {
        return Optional.ofNullable(this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getType, type).eq(ShopDO::getEnabled, enabled)
                        .eq(ShopDO::getProperty, property).eq(ShopDO::getIsSelf, isSelf)
        )).orElseGet(ArrayList::new);
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByEnabledAndEnvironmentAndTypeOrderById(Boolean enabled, Integer environment, Integer type) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getEnabled, enabled).eq(ShopDO::getEnvironment, environment).eq(ShopDO::getType, type)
                        .orderByAsc(ShopDO::getId)
        );
    }

    /**
     * 查询联营商城，优先默认商城, 没有则查询一个最新的商城
     */
    @SuppressWarnings("unchecked")
    public ShopSelectListResp findShopSelectRespUnionFirstByEnvironmentAndEnabledAndType(Integer environment, Boolean enabled, Integer type) {
        return this.getFirst(
                Wrappers.lambdaQuery(ShopDO.class)
                        .select(ShopDO::getId, ShopDO::getMemberId, ShopDO::getMemberRoleId, ShopDO::getName, ShopDO::getIsSelf, ShopDO::getLogoUrl, ShopDO::getIsMemberOperate, ShopDO::getProperty, ShopDO::getUrl)
                        .eq(ShopDO::getEnvironment, environment).eq(ShopDO::getEnabled, enabled).eq(ShopDO::getType, type).eq(ShopDO::getIsSelf, false)
                        .orderByDesc(ShopDO::getIsDefault, ShopDO::getCreateTime)
        ).map(ShopConvert.INSTANCE::toShopSelectResp).orElse(null);
    }

    /**
     * 查询自营商城，优先默认商城，没有则找最新的商城，每个会员只查一个商城
     */
    public List<ShopSelectListResp> findShopSelectRespSelfByEnvironmentAndEnabledAndType(Integer environment, Boolean enabled, Integer type) {
        return baseMapper.findShopSelectRespSelfByEnvironmentAndEnabledAndType(environment, enabled, type);
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findBySelfAndEnabledAndNameLikeOrderById(Boolean isSelf, Boolean enabled, String name) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getIsSelf, isSelf).eq(ShopDO::getEnabled, enabled).like(ShopDO::getName, "%" + name + "%")
                        .orderByAsc(ShopDO::getId)
        );
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByEnabledAndEnvironmentAndTypeOrderById(Boolean enabled, Integer environment, Integer type, Integer type2) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(Objects.nonNull(enabled), ShopDO::getEnabled, enabled)
                        .eq(Objects.nonNull(environment), ShopDO::getEnvironment, environment)
                        .eq(Objects.nonNull(type), ShopDO::getType, type)
                        .eq(Objects.nonNull(type2), ShopDO::getType, type2)
                        .orderByAsc(ShopDO::getId)
        );
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByEnabledAndSelfAndTypeNot(Boolean enabled, Boolean isSelf, Integer type) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getEnabled, enabled).eq(ShopDO::getIsSelf, isSelf).ne(ShopDO::getType, type)
        );
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByEnabledIdInAndEnvironmentAndTypeOrderById(Boolean enabled, List<Long> shopIdList, Integer environment, Integer type, Integer type2) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(Objects.nonNull(enabled), ShopDO::getEnabled, enabled)
                        .in(CollUtil.isNotEmpty(shopIdList), ShopDO::getId, shopIdList)
                        .eq(Objects.nonNull(environment), ShopDO::getEnvironment, environment)
                        .eq(Objects.nonNull(type), ShopDO::getType, type)
                        .eq(Objects.nonNull(type2), ShopDO::getType, type2)
                        .orderByAsc(ShopDO::getId)
        );
    }

    /**
     * 查询商城列表
     */
    @SuppressWarnings("unchecked")
    public List<SelfShopRedisDTO> findSelfShopCacheDTOByEnabled(Boolean enabled) {
        List<ShopDO> shopDOList = this.list(
                Wrappers.lambdaQuery(ShopDO.class).select(
                        ShopDO::getId, ShopDO::getMemberId, ShopDO::getMemberRoleId, ShopDO::getName, ShopDO::getType, ShopDO::getEnvironment,
                        ShopDO::getProperty, ShopDO::getLogoUrl, ShopDO::getEnabled
                ).eq(ShopDO::getIsSelf, true).eq(ShopDO::getEnabled, enabled)
        );
        return shopDOList.stream().map(ShopConvert.INSTANCE::toSelfShopCacheDTO).collect(Collectors.toList());
    }

    /**
     * 查询商城列表
     */
    public List<ShopDOResp> findByEnabledAndMemberIdOrderById(Boolean enabled, Long memberId) {
        return baseMapper.findByEnabledAndMemberIdOrderById(enabled, memberId);
    }

    /**
     * 查询商城是否存在
     */
    @SuppressWarnings("unchecked")
    public Boolean existsByEnabledAndMemberIdAndMemberRoleId(Boolean enabled, Long memberId, Long memberRoleId) {
        return this.getFirst(
                Wrappers.lambdaQuery(ShopDO.class).select(ShopDO::getId).eq(ShopDO::getEnabled, enabled).eq(ShopDO::getMemberId, memberId).eq(ShopDO::getMemberRoleId, memberRoleId)
        ).map(ShopDO::getId).orElse(0L) > 0;
    }

    /**
     * 更新商城是否启用
     */
    public void updateEnabledById(Boolean enabled, Long id) {
        this.update(
                Wrappers.lambdaUpdate(ShopDO.class).eq(ShopDO::getId, id)
                        .set(ShopDO::getEnabled, enabled)
                        .set(ShopDO::getIsDefault, Boolean.FALSE)
        );
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByMemberIdAndMemberRoleIdAndEnabled(Long memberId, Long memberRoleId, Boolean enabled) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getMemberId, memberId).eq(ShopDO::getMemberRoleId, memberRoleId).eq(ShopDO::getEnabled, enabled)
        );
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByMemberIdEqOrAndMemberRoleIdEqOrAndEnabledOrderById(Long memberId, Long memberRoleId, List<BaseMemberDTO> baseMemberDTOList, Boolean enabled) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(Objects.nonNull(memberId), ShopDO::getMemberId, memberId).eq(Objects.nonNull(memberRoleId), ShopDO::getMemberRoleId, memberRoleId)
                        .eq(Objects.nonNull(enabled), ShopDO::getEnabled, enabled).and(CollUtil.isNotEmpty(baseMemberDTOList), o -> o.or(wrapper -> baseMemberDTOList.forEach(
                                baseMemberDTO -> wrapper.eq(ShopDO::getMemberId, baseMemberDTO.getMemberId()).eq(ShopDO::getMemberRoleId, baseMemberDTO.getRoleId())
                        ))).orderByAsc(ShopDO::getId)
        );
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByMemberIdAndMemberRoleIdAndEnabledAndEnvironmentOrderById(Long memberId, Long memberRoleId, Boolean enabled, Integer environment) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getMemberId, memberId).eq(ShopDO::getMemberRoleId, memberRoleId).eq(ShopDO::getEnabled, enabled).eq(ShopDO::getEnvironment, environment)
                        .orderByAsc(ShopDO::getId)
        );
    }

    /**
     * 查询商城列表
     */
    public List<ShopRuleDetailResp> findByMemberIdAndMemberRoleIdAndEnabledAndEnvironmentAndNameLikeOrderById(Long memberId, Long memberRoleId, Boolean enabled, Integer environment, String name) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getMemberId, memberId).eq(ShopDO::getMemberRoleId, memberRoleId).eq(ShopDO::getEnabled, enabled)
                        .eq(Objects.nonNull(environment), ShopDO::getEnvironment, environment).like(StringUtils.isNotBlank(name), ShopDO::getName, "%" + name + "%").orderByAsc(ShopDO::getId)
        ).stream().map(ShopConvert.INSTANCE::toShopRuleDetailResp).collect(Collectors.toList());
    }

    /**
     * 查询商城ID列表
     */
    @SuppressWarnings("unchecked")
    public List<Long> findIdByMemberIdAndMemberRoleIdAndEnabled(Long memberId, Long memberRoleId, Boolean enabled) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).select(ShopDO::getId).eq(ShopDO::getMemberId, memberId).eq(ShopDO::getMemberRoleId, memberRoleId).eq(ShopDO::getEnabled, enabled)
        ).stream().map(ShopDO::getId).collect(Collectors.toList());
    }

    /**
     * 查询商城信息
     */
    public SelfBusinessShopLogoResp findFirstByIdAndMemberIdIn(Long id, List<MemberAndRoleIdDTO> memberList) {
        return baseMapper.findFirstByIdAndMemberIdIn(id, memberList);
    }

    /**
     * 查询商城列表
     */
    @SuppressWarnings("unchecked")
    public List<SelfBusinessShopListResp> findByEnabledAndEnvironment(Boolean enabled, Integer environment) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).select(
                        ShopDO::getId, ShopDO::getSelfShopModelId, ShopDO::getName, ShopDO::getProperty, ShopDO::getLogoUrl, ShopDO::getUrl, ShopDO::getMemberId, ShopDO::getMemberName, ShopDO::getMemberRoleId
                ).eq(ShopDO::getEnabled, enabled).eq(ShopDO::getEnvironment, environment)
        ).stream().map(ShopConvert.INSTANCE::toSelfBusinessShopListResp).collect(Collectors.toList());
    }

    /**
     * 查询商城列表
     */
    public List<ShopDO> findByMemberIdAndMemberRoleIdAndEnabledOrderById(Long memberId, Long memberRoleId, Boolean enabled) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).eq(ShopDO::getMemberId, memberId).eq(ShopDO::getMemberRoleId, memberRoleId).eq(ShopDO::getEnabled, enabled)
        );
    }

    /**
     * 查询商城是否开启MRO
     */
    @SuppressWarnings("unchecked")
    public Optional<Boolean> findOpenMroFirstById(Long id) {
        return this.getFirst(
                Wrappers.lambdaQuery(ShopDO.class).select(ShopDO::getIsOpenMro).eq(ShopDO::getId, id)
        ).map(ShopDO::getIsOpenMro);
    }

    /**
     * 更新商城是否开启MRO
     */
    public void updateOpenMroById(Long id, Boolean enabled) {
        this.update(
                Wrappers.lambdaUpdate(ShopDO.class).set(ShopDO::getIsOpenMro, enabled).eq(ShopDO::getId, id)
        );
    }

    /**
     * 查询商城模型ID列表
     */
    @SuppressWarnings("unchecked")
    public List<Long> findSelfShopModelIdByMemberIdAndMemberRoleId(Long memberId, Long memberRoleId) {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class).select(ShopDO::getSelfShopModelId)
                        .eq(ShopDO::getMemberId, memberId)
                        .eq(ShopDO::getMemberRoleId, memberRoleId)
        ).stream().map(ShopDO::getSelfShopModelId).collect(Collectors.toList());
    }

    /**
     * 查询商城列表
     */
    public List<ShopListV2Resp> findShopListV2Resp() {
        return baseMapper.findShopListV2Resp(ShopTypeEnum.getPortalTypeList());
    }

    /**
     * 更新是否默认商城
     */
    public void updateIsDefaultById(Long id, Boolean enabled) {
        this.update(
                Wrappers.lambdaUpdate(ShopDO.class).set(ShopDO::getIsDefault, enabled).eq(ShopDO::getId, id)
        );
    }

    /**
     * 更新商城信息
     */
    public void updateEditShopInfoReqById(EditShopInfoReq editShopInfoReq) {
        this.update(
                Wrappers.lambdaUpdate(ShopDO.class)
                        .set(StringUtils.isNotBlank(editShopInfoReq.getName()), ShopDO::getName, editShopInfoReq.getName())
                        .set(StringUtils.isNotBlank(editShopInfoReq.getLogoUrl()), ShopDO::getLogoUrl, editShopInfoReq.getLogoUrl())
                        .set(StringUtils.isNotBlank(editShopInfoReq.getDescribe()), ShopDO::getDescribe, editShopInfoReq.getDescribe())
                        .set(Objects.nonNull(editShopInfoReq.getOpenMro()), ShopDO::getIsOpenMro, editShopInfoReq.getOpenMro())
                        .eq(ShopDO::getId, editShopInfoReq.getShopId())
        );
    }

    /**
     * 查询商城装修列表
     */
    public List<ShopListV2Resp> abilitySelfShopList(AbilitySelfShopListDTO abilitySelfShopListDTO) {
        return this.baseMapper.abilitySelfShopList(abilitySelfShopListDTO);
    }

    /**
     * 查询商城装修列表
     */
    public List<ShopDetailResp> findByListShopByReq(ListShopByReq listShopByReq) {
        return baseMapper.findByListShopByReq(listShopByReq);
    }

    /**
     * 查询商城列表
     */
    @SuppressWarnings("unchecked")
    public List<ShopBListResp> findShopBListResp() {
        return this.list(
                Wrappers.lambdaQuery(ShopDO.class)
                        .select(ShopDO::getId, ShopDO::getName, ShopDO::getLogoUrl)
                        .eq(ShopDO::getEnabled, Boolean.TRUE)
                        .eq(ShopDO::getType, ShopTypeEnum.ENTERPRISE.getCode())
                        .eq(ShopDO::getProperty, ShopPropertyEnum.B.getCode())
                        .eq(ShopDO::getIsSelf, Boolean.FALSE)
        ).stream().map(ShopConvert.INSTANCE::toShopBListResp).collect(Collectors.toList());
    }

    /**
     * 更新是否启用商城帮助信息
     */
    public void updateHelpInfoEnableById(Long shopId, Boolean helpInfoEnable) {
        baseMapper.update(null, Wrappers.lambdaUpdate(ShopDO.class).set(ShopDO::getHelpInfoEnable, helpInfoEnable).eq(ShopDO::getId, shopId));
    }

    /**
     * 根据会员列表查询商城信息
     */
    public List<ShopDO> findAllByEnabledAndMemberIdIn(Set<MemberAndRoleIdDTO> memberList) {
        return baseMapper.findAllByEnabledAndMemberIdIn( memberList);
    }
}




