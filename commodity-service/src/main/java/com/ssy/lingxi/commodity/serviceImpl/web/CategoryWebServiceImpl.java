package com.ssy.lingxi.commodity.serviceImpl.web;

import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.commodity.model.resp.web.CategoryTreeResp;
import com.ssy.lingxi.commodity.service.web.ICategoryWebService;
import com.ssy.lingxi.common.model.resp.NodeResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.product.api.feign.ITemplateFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 2.0.0
 * web - 平台品类 - 业务实现层
 * @since 2020/12/15
 */
@Slf4j
@Service
public class CategoryWebServiceImpl implements ICategoryWebService {
    @Resource
    private ITemplateFeign templateFeign;

    /**
     * 获取平台首页平台品类树
     *
     * @param shopId 商城id
     */
    @Override
    public List<NodeResp> findPlatformCategoryTree(Long shopId) {
        //调用搜索服务 -> 获取全部商品品类树（平台品类）
        WrapperResp<List<NodeResp>> categoryWrapperResp = templateFeign.getCategoryTree(shopId, null, null);
        if (categoryWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.info("请求搜索服务失败:{}", categoryWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_SEARCH_ERROR);
        }
        if (CollectionUtils.isEmpty(categoryWrapperResp.getData())) {
            log.info("获取不到平台品类树（检查平台后台是否添加了品类）");
            return new ArrayList<>();
        }

        return categoryWrapperResp.getData();
    }

    /**
     * 获取企业商城首页平台品类树
     */
    @Override
    public List<CategoryTreeResp> findEnterpriseCategoryTree(Long shopId, Long adornId) {

        //调用搜索服务 -> 获取全部商品品类树（平台品类）
        WrapperResp<List<NodeResp>> categoryWrapperResp = templateFeign.getCategoryTree(shopId, null, null);
        if (categoryWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.info("请求搜索服务失败:{}", categoryWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_SEARCH_ERROR);
        }
        if (CollUtil.isEmpty(categoryWrapperResp.getData())) {
            log.info("获取不到平台品类树（检查平台后台是否添加了品类）");
            return new ArrayList<>();
        }

        //根据平台一级品类获取到关联的推荐品牌并封装到品类树进去
        return categoryWrapperResp.getData().stream().map(n -> {
            CategoryTreeResp vo = new CategoryTreeResp();
            vo.setId(Long.parseLong(n.getId()));
            vo.setName(n.getName());
            vo.setParentId(n.getParentId());
            vo.setChecked(n.getChecked());
            vo.setImageUrl(n.getImageUrl());
            vo.setChildren(n.getChildren());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取企业直采平台品类树
     */
    @Override
    public List<NodeResp> findPurchaseCategoryTree() {
        //调用搜索服务 -> 获取全部商品品类树（平台品类）
        WrapperResp<List<NodeResp>> categoryWrapperResp = templateFeign.getCategoryTree(null, null, null);
        if (categoryWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.info("请求搜索服务失败:{}", categoryWrapperResp.getMessage());
            throw new BusinessException(ResponseCodeEnum.SERVICE_SEARCH_ERROR);
        }
        if (CollectionUtils.isEmpty(categoryWrapperResp.getData())) {
            log.info("获取不到平台品类树（检查平台后台是否添加了品类）");
            return new ArrayList<>();
        }

        return categoryWrapperResp.getData();
    }

}
