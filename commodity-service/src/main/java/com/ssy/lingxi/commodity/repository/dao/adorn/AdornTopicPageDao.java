package com.ssy.lingxi.commodity.repository.dao.adorn;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.adorn.AdornTopicPageDO;
import com.ssy.lingxi.commodity.enums.AdornTopicPageTypeEnum;
import com.ssy.lingxi.commodity.model.req.adorn.AdornTopicPageListReq;
import com.ssy.lingxi.commodity.model.req.adorn.AdornTopicPageUpdateReq;
import com.ssy.lingxi.commodity.model.resp.adorn.AdornTopicPageListResp;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IAdornTopicPageMapper;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.util.JsonUtil;
import org.springframework.stereotype.Repository;

import java.util.Objects;
import java.util.Optional;

/**
 * 装修专题页
 *
 * <AUTHOR>
 */
@Repository
public class AdornTopicPageDao extends CommonServiceImpl<IAdornTopicPageMapper, AdornTopicPageDO> {

    /**
     * 分页列表
     */
    public Page<AdornTopicPageListResp> pageList(AdornTopicPageListReq adornTopicPageListReq, UserLoginCacheDTO user) {
        Page<AdornTopicPageListResp> page = Page.of(adornTopicPageListReq.getCurrent(), adornTopicPageListReq.getPageSize());
        return getBaseMapper().pageList(page, adornTopicPageListReq, user);
    }

    public void updateBy(AdornTopicPageUpdateReq req) {
        this.update(
                Wrappers.lambdaUpdate(AdornTopicPageDO.class)
                        .set(Objects.nonNull(req.getShopId()), AdornTopicPageDO::getShopId, req.getShopId())
                        .set(Objects.nonNull(req.getStoreId()), AdornTopicPageDO::getStoreId, req.getStoreId())
                        .set(AdornTopicPageDO::getType, Objects.nonNull(req.getStoreId()) ? AdornTopicPageTypeEnum.STORE.getCode() : AdornTopicPageTypeEnum.SHOP.getCode())
                        .set(CharSequenceUtil.isNotBlank(req.getName()), AdornTopicPageDO::getName, req.getName())
                        .set(CollUtil.isNotEmpty(req.getAdornContent()), AdornTopicPageDO::getAdornContent, JsonUtil.toJson(req.getAdornContent()))
                        .eq(AdornTopicPageDO::getId, req.getId())
        );
    }

    public void updateIsDeletedBy(Long id, Integer isDeleted) {
        this.update(
                Wrappers.lambdaUpdate(AdornTopicPageDO.class)
                        .set(AdornTopicPageDO::getIsDeleted, isDeleted)
                        .eq(AdornTopicPageDO::getId, id)
        );
    }

    public Optional<Long> findIdByIdAndMemberIdAndMemberRoleId(Long id, Long memberId, Long memberRoleId) {
        return this.getOneOpt(
                Wrappers.lambdaQuery(AdornTopicPageDO.class).select(AdornTopicPageDO::getId)
                        .eq(AdornTopicPageDO::getId, id)
                        .eq(AdornTopicPageDO::getMemberId, memberId)
                        .eq(AdornTopicPageDO::getMemberRoleId, memberRoleId),
                false
        ).map(AdornTopicPageDO::getId);
    }
}




