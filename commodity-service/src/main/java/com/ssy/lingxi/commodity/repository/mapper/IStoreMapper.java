package com.ssy.lingxi.commodity.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.shop.StoreDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface IStoreMapper extends BaseMapper<StoreDO> {

    @Select(value = "SELECT ms.* FROM " + StoreDO.TABLE_NAME + " ms WHERE ms.create_time > #{timeInMillis} ORDER BY ms.create_time DESC")
    Page<StoreDO> findNewAddStore(@Param("page") Page<StoreDO> page, @Param("timeInMillis") long timeInMillis);

}
