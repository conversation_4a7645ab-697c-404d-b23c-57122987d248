package com.ssy.lingxi.commodity.repository.dao.support;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.support.CountryAreaDO;
import com.ssy.lingxi.commodity.model.req.support.CountryAreaListDataReq;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.support.ICountryAreaMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CountryAreaDao extends CommonServiceImpl<ICountryAreaMapper, CountryAreaDO> {

    public List<CountryAreaDO> findAllByStatus(Boolean status) {
        return this.list(Wrappers.lambdaQuery(CountryAreaDO.class).eq(CountryAreaDO::getStatus, status));
    }

    public CountryAreaDO findFirstByCode(String code) {
        return this.getFirst(
                Wrappers.lambdaQuery(CountryAreaDO.class).eq(CountryAreaDO::getCode, code)
        ).orElse(null);
    }

    public CountryAreaDO findFirstByTelCodeAndStatus(String telCode, Boolean status) {
        return this.getFirst(
                Wrappers.lambdaQuery(CountryAreaDO.class).eq(CountryAreaDO::getTelCode, telCode).eq(CountryAreaDO::getStatus, status)
        ).orElse(null);
    }

    public Page<CountryAreaDO> findPageByCodeLikeAndNameLikeAndNameEnLikeOrderById(CountryAreaListDataReq countryAreaListReq) {
        return this.page(Page.of(countryAreaListReq.getCurrent(), countryAreaListReq.getPageSize()),
                Wrappers.lambdaQuery(CountryAreaDO.class)
                        .like(StringUtils.isNotBlank(countryAreaListReq.getCode()), CountryAreaDO::getCode, "%" + countryAreaListReq.getCode() + "%")
                        .like(StringUtils.isNotBlank(countryAreaListReq.getName()), CountryAreaDO::getName, "%" + countryAreaListReq.getName() + "%")
                        .like(StringUtils.isNotBlank(countryAreaListReq.getNameEn()), CountryAreaDO::getNameEn, "%" + countryAreaListReq.getNameEn() + "%")
                        .orderByAsc(CountryAreaDO::getId)
        );
    }

    public List<CountryAreaDO> findByIdIn(List<Long> idList) {
        return this.list(
                Wrappers.lambdaQuery(CountryAreaDO.class).in(CountryAreaDO::getId, idList)
        );
    }
}
