package com.ssy.lingxi.commodity.serviceImpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.ssy.lingxi.commodity.api.model.req.HasAssociationShopReq;
import com.ssy.lingxi.commodity.api.model.req.shop.OpenMroInnerReq;
import com.ssy.lingxi.commodity.api.model.req.shop.ShopMemberReq;
import com.ssy.lingxi.commodity.api.model.resp.shop.ShopDetailResp;
import com.ssy.lingxi.commodity.api.model.resp.shop.ShopInnerResp;
import com.ssy.lingxi.commodity.api.model.resp.shop.ShopOpenMroResp;
import com.ssy.lingxi.commodity.domain.ShopDM;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopDO;
import com.ssy.lingxi.commodity.enums.AdornTypeEnum;
import com.ssy.lingxi.commodity.handler.component.cache.ShopCacheComponent;
import com.ssy.lingxi.commodity.handler.convert.ShopConvert;
import com.ssy.lingxi.commodity.model.req.common.MemberIdReq;
import com.ssy.lingxi.commodity.model.req.common.SwitchReq;
import com.ssy.lingxi.commodity.model.req.shop.*;
import com.ssy.lingxi.commodity.model.resp.ShopBListResp;
import com.ssy.lingxi.commodity.model.resp.shop.*;
import com.ssy.lingxi.commodity.repository.dao.adorn.AdornDao;
import com.ssy.lingxi.commodity.repository.dao.shop.ShopDao;
import com.ssy.lingxi.commodity.repository.dao.shop.StoreDao;
import com.ssy.lingxi.commodity.service.IShopService;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopEnvironmentEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopPropertyEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.member.RoleTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 商城相关 - 数据处理层
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ShopServiceImpl implements IShopService {

    private final ShopDao shopDao;
    private final AdornDao adornDao;
    private final StoreDao storeDao;
    private final ShopCacheComponent shopCacheComponent;


    @Override
    public PageDataResp<ShopInfoResp> shopList(ShopPageListDataReq shopPageListReq) {
        // 查询商城数据
        return shopDao.shopList(shopPageListReq);
    }

    @Override
    public List<ShopListThinResp> shopThinList() {
        return shopDao.findShopListThinRespByEnabledAndTypeAndSelf(
                Boolean.TRUE, ShopTypeEnum.ENTERPRISE.getCode(), Boolean.FALSE
        );
    }

    @Override
    public List<ShopStoreListResp> shopStoreList(Long storeId) {
        return shopDao.findShopStoreListRespByEnabledAndTypeAndSelf(
                storeId, Boolean.TRUE, ShopTypeEnum.ENTERPRISE.getCode(), Boolean.FALSE
        );
    }

    @Override
    public PageDataResp<SelfShopInfoV2Resp> selfShopList(SelfShopPageListReq selfShopPageListReq) {
        return shopDao.selfShopList(selfShopPageListReq);
    }

    @Override
    public ShopInfoResp shopInfo(Long shopId) {
        // 查询商城数据
        return shopDao.shopInfo(shopId);
    }

    @Override
    @Transactional
    public void editShopInfo(EditShopInfoReq editShopInfoReq) {

        // 更新数据
        shopDao.updateEditShopInfoReqById(editShopInfoReq);

        // 更新完数据后, 查询更新后的数据, 更新缓存
        ShopDO shopDO = Optional.ofNullable(shopDao.getById(editShopInfoReq.getShopId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.COMMODITY_NO_SHOP_FOUND_BASED_ON_ID));

        // 更新缓存
        updateCache(shopDO);
    }

    /**
     * 更新缓存
     */
    private void updateCache(ShopDO shopDO) {
        if (Boolean.FALSE.equals(shopDO.getIsSelf())) {
            // 非自营商城
            shopCacheComponent.shopToRedis(shopDO);
        }

        if (Boolean.TRUE.equals(shopDO.getIsSelf())) {
            // 自营商城
            shopCacheComponent.selfShopToRedis(shopDO);
        }
    }

    /**
     * 查询所有商城信息
     */
    @Override
    public List<ShopDetailResp> shopAll() {

        List<ShopDO> shopDOList = shopDao.findByEnabled(Boolean.TRUE);

        return shopDOList.stream().map(shop -> BeanUtil.copyProperties(shop, ShopDetailResp.class)).collect(Collectors.toList());
    }

    /**
     * 根据ID查询商城信息
     */
    @Override
    public ShopDetailResp shopDetails(Long id) {
        if (id == null) {
            throw new BusinessException(ResponseCodeEnum.MAN_ID_DOES_NOT_EXIST);
        }

        ShopDO shopDO = Optional.ofNullable(shopDao.getById(id)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.COMMODITY_NO_SHOP_FOUND_BASED_ON_ID));

        Long adornId = adornDao.findIdFirstByShopIdAndEnabledAndType(shopDO.getId(), Boolean.TRUE, AdornTypeEnum.SHOP.getCode());

        ShopDetailResp shopDetailResp = BeanUtil.copyProperties(shopDO, ShopDetailResp.class);
        shopDetailResp.setAdornId(adornId);

        return shopDetailResp;
    }

    /**
     * 根据商城类型查询商城信息
     *
     * @param shopType 商城类型
     * @see ShopTypeEnum
     */
    @Override
    public List<ShopDetailResp> shopAllByShopType(Integer shopType) {

        List<ShopDO> shopDOList = shopDao.findAllByTypeAndEnabled(shopType, Boolean.TRUE);
        return shopDOList.stream().map(shop -> BeanUtil.copyProperties(shop, ShopDetailResp.class)).collect(Collectors.toList());
    }

    /**
     * 根据商城类型和环境查询商城信息
     *
     * @param shopType    商城类型
     * @param environment 商城环境：1-web 2-H5 3-小程序 4-APP
     * @see ShopTypeEnum
     */
    @Override
    public List<ShopDetailResp> findAllByShopTypeAndEnvironment(Integer shopType, Integer environment) {
        if (shopType == null || environment == null) {
            throw new BusinessException(ResponseCodeEnum.COMMODITY_PARAMETER_CANNOT_BE_EMPTY);
        }

        List<ShopDO> shopDOList = shopDao.findByTypeAndEnabledAndEnvironment(shopType, Boolean.TRUE, environment);

        return shopDOList.stream().map(shop -> BeanUtil.copyProperties(shop, ShopDetailResp.class)).collect(Collectors.toList());
    }

    /**
     * 根据商城环境查询所有商城信息
     */
    @Override
    public List<ShopDO> findShopsByEnvironment(EnvironmentReq request) {
        //获取商城
        return shopDao.findAllByEnvironmentAndEnabled(request.getEnvironment(), Boolean.TRUE);
    }

    /**
     * 根据门户类型获取商城【店铺、渠道、采购、物流、加工】
     */
    @Override
    public List<ShopDO> findByDoorType(DoorTypeReq request, HttpServletRequest httpServletRequest) {
        //获取商城
        return shopDao.findAllByTypeInAndEnvironmentAndEnabled(
                ShopTypeEnum.findShopTypeListByDoorType(request.getDoorType()),
                ShopEnvironmentEnum.WEB.getCode(),
                Boolean.TRUE
        );
    }

    @Override
    public List<ShopDOResp> findShop(FindShopReq findShopReq) {
        return shopDao.findShop(findShopReq);
    }

    /**
     * 根据当前登录会员类型获取商城
     */
    @Override
    public List<ShopDO> findByMemberType(UserLoginCacheDTO user) {
        List<ShopDO> shopList = new ArrayList<>();
        if (user.getMemberRoleType().equals(RoleTypeEnum.SERVICE_PROVIDER.getCode())) {
            if (user.getMemberType().equals(MemberTypeEnum.MERCHANT.getCode()) || user.getMemberType().equals(MemberTypeEnum.MERCHANT_PERSONAL.getCode())) {
                shopList = shopDao.findAllByTypeAndEnabled(ShopTypeEnum.ENTERPRISE.getCode(), Boolean.TRUE);
            }
        }

        return shopList;
    }

    @Override
    public List<ShopDetailResp> shopByIdList(CommonIdListReq request) {
        List<ShopDO> shopList = shopDao.listByIds(request.getIdList());
        if (CollUtil.isEmpty(shopList)) {
            return new ArrayList<>();
        }

        return shopList.stream().map(a -> BeanUtil.copyProperties(a, ShopDetailResp.class)).collect(Collectors.toList());
    }

    @Override
    public List<ShopDetailResp> byShopIds(CommonIdListReq request) {

        List<ShopDO> shopList = shopDao.listByIds(request.getIdList());

        return ShopConvert.INSTANCE.toShopRespList(shopList);
    }

    @Override
    public List<ShopInnerResp> shopByIds(CommonIdListReq request) {
        List<ShopDO> shopList = shopDao.listByIds(request.getIdList());
        if (CollUtil.isEmpty(shopList)) {
            return new ArrayList<>();
        }

        return shopList.stream().map(a -> {
            ShopInnerResp shopResponse = new ShopInnerResp();
            BeanUtils.copyProperties(a, shopResponse);
            shopResponse.setUrl("http://" + a.getUrl() + ".lingxitest.com/");
            return shopResponse;
        }).collect(Collectors.toList());
    }

    /**
     * 商城列表（装修）
     */
    @Override
    public List<ShopDO> listAdorn(UserLoginCacheDTO sysUser, ShopListReq request) {
        return shopDao.findAllByShopListReqOrderByIdAsc(request, Boolean.TRUE);
    }

    @Override
    public Boolean openMro(Long shopId) {
        ShopDO shop = shopDao.getById(shopId);
        if (shop == null) {
            throw new BusinessException(ResponseCodeEnum.MAN_MALL_DOES_NOT_EXIST);
        }
        return shop.getIsOpenMro();
    }

    @Override
    public List<EnterpriseShopListResp> listEnterpriseShop(UserLoginCacheDTO sysUser) {
        //获取当前用户登录企业B端商城
        List<ShopDO> shopList = shopDao.findByTypeAndEnabledAndPropertyAndSelf(
                ShopTypeEnum.ENTERPRISE.getCode(), Boolean.TRUE, ShopPropertyEnum.B.getCode(), Boolean.FALSE
        );

        return shopList.stream().map(shop -> {
            EnterpriseShopListResp response = new EnterpriseShopListResp();
            response.setId(shop.getId());
            response.setType(shop.getType());
            response.setEnvironment(shop.getEnvironment());
            response.setName(shop.getName());
            response.setProperty(shop.getProperty());
            return response;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ShopDetailResp> listShopByReq(ListShopByReq listShopByReq) {
        //获取当前用户商城
        return shopDao.findByListShopByReq(listShopByReq);
    }

    @Override
    public List<ShopOpenMroResp> getOpenMroList(CommonIdListReq request) {
        if (CollUtil.isEmpty(CollUtil.removeNull(request.getIdList()))) {
            return new ArrayList<>();
        }

        return shopDao.findIdAndOpenMroByIdInAndEnabled(request.getIdList(), Boolean.TRUE);
    }

    @Override
    public Boolean hasAssociationShop(HasAssociationShopReq hasAssociationShopReq) {
        SFunction<ShopDO, Long> sFunction = ShopDM.findFieldFunc(hasAssociationShopReq);

        BusinessAssertUtil.notNull(sFunction, ResponseCodeEnum.PT_SHOP_NOT_FIND_PREDICATE);

        Long shopId = shopDao.findShopIdByEnabledAndFunc(Boolean.TRUE, sFunction, hasAssociationShopReq.getId());

        return Objects.nonNull(shopId);
    }

    @Override
    public void openMroEnabled(Long shopId, Boolean enabled) {
        shopDao.updateOpenMroById(shopId, enabled);
    }

    @Override
    public Boolean getOpenMro(OpenMroInnerReq req) {

        if (OpenMroInnerReq.TypeEnum.shop.equals(req.getType())) {

            return shopDao.findOpenMroFirstById(req.getId()).orElse(Boolean.FALSE);
        }

        return storeDao.findOpenMroFirstById(req.getId()).orElse(false);
    }

    @Override
    public List<ShopListV2Resp> portalList() {
        return shopDao.findShopListV2Resp();
    }

    @Override
    public void shopSwitch(SwitchReq switchReq) {
        ShopDO shopDO = shopDao.getById(switchReq.getId());
        shopDO.setEnabled(switchReq.getEnabled());
        shopDO.setIsDefault(Boolean.FALSE);

        // 切换商城启用状态
        shopDao.updateEnabledById(switchReq.getEnabled(), switchReq.getId());

        // 更新缓存
        shopCacheComponent.selfShopToRedis(shopDO);
    }

    @Override
    public void isDefaultSwitch(IsDefaultSwitchReq isDefaultSwitchReq) {

        ShopDO shopDO = shopDao.getById(isDefaultSwitchReq.getId());
        shopDO.setIsDefault(isDefaultSwitchReq.getEnabled());

        if (Boolean.TRUE.equals(isDefaultSwitchReq.getEnabled())) {
            // 同环境下的原默认商城改为非默认
            shopDao.updateIsDefaultByIsDefaultAndEnvironment(Boolean.FALSE, Boolean.TRUE, shopDO.getEnvironment());
        }

        // 切换是否默认商城
        shopDao.updateIsDefaultById(isDefaultSwitchReq.getId(), isDefaultSwitchReq.getEnabled());

        // 更新缓存
        shopCacheComponent.selfShopToRedis(shopDO);
    }

    @Override
    public Boolean existSelfShop(ShopMemberReq shopMemberReq) {
        return shopDao.existsByEnabledAndMemberIdAndMemberRoleId(Boolean.TRUE, shopMemberReq.getMemberId(), shopMemberReq.getMemberRoleId());
    }

    @Override
    public List<ShopDOResp> findSelfListByMemberId(MemberIdReq memberIdReq) {
        return shopDao.findByEnabledAndMemberIdOrderById(Boolean.TRUE, memberIdReq.getMemberId());
    }

    @Override
    public ShopListV2Resp mainPortalInfo() {
        List<ShopDO> shopDOList = shopDao.findByTypeAndEnabledAndEnvironment(ShopTypeEnum.MAIN_PORTAL.getCode(), Boolean.TRUE, ShopEnvironmentEnum.WEB.getCode());
        // 主门户只会有一个，通过主门户类型和环境只会查到一条记录，且只会有web环境
        Optional<ShopListV2Resp> shopListV2RespOptional = Optional.ofNullable(CollUtil.isNotEmpty(shopDOList) ? ShopConvert.INSTANCE.toShopListV2Resp(shopDOList.get(0)) : null);

        shopListV2RespOptional.ifPresent(shopListV2Resp -> {
            Long adornId = adornDao.findIdFirstByShopIdAndEnabledAndType(shopListV2Resp.getId(), Boolean.TRUE, AdornTypeEnum.SHOP.getCode());
            shopListV2Resp.setAdornId(adornId);
        });

        return shopListV2RespOptional.orElse(null);
    }

    @Override
    public List<ShopBListResp> shopBList() {
        return shopDao.findShopBListResp();
    }

}




