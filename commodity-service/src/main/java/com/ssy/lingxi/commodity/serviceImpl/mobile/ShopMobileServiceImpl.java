package com.ssy.lingxi.commodity.serviceImpl.mobile;

import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.commodity.entity.do_.shop.ShopDO;
import com.ssy.lingxi.commodity.model.req.shop.ShopAllReq;
import com.ssy.lingxi.commodity.model.resp.shop.SelfBusinessShopListResp;
import com.ssy.lingxi.commodity.model.resp.shop.ShopSelectListResp;
import com.ssy.lingxi.commodity.model.resp.shop.ShopSelectResp;
import com.ssy.lingxi.commodity.repository.dao.shop.ShopDao;
import com.ssy.lingxi.commodity.service.mobile.IShopMobileService;
import com.ssy.lingxi.common.enums.manage.PlatformParameterEnum;
import com.ssy.lingxi.common.enums.manage.PlatformParameterValueEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopEnvironmentEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.manage.api.feign.IPlatformParameterFeign;
import com.ssy.lingxi.manage.api.model.resp.parameter.PlatformParameterManageResp;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * mobile - 商城 - 业务实现层
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/09/27
 */
@Primary
@Service
@RequiredArgsConstructor
public class ShopMobileServiceImpl implements IShopMobileService {

    private final ShopDao shopDao;

    private final IPlatformParameterFeign parameterManageFeign;


    @Override
    public List<ShopDO> startPageShopList() {

        //获取商城
        List<ShopDO> shopList = shopDao.findAllByEnvironmentAndEnabled(
                ShopEnvironmentEnum.APP.getCode(),
                Boolean.TRUE
        );

        //只获取企业商城、渠道商城、渠道自有商城
        return shopList.stream().filter(a ->
                ShopTypeEnum.checkEnterpriseOrChannelOrChannelSelf(a.getType())
        ).collect(Collectors.toList());
    }

    @Override
    public List<SelfBusinessShopListResp> selfBusinessShopList(Integer environment) {
        //根据商城ID获取商城规则详情
        return Optional.ofNullable(shopDao.findByEnabledAndEnvironment(Boolean.TRUE, environment)).orElseGet(ArrayList::new);
    }

    @Override
    public List<ShopDO> all(ShopAllReq req) {

        //获取商城
        List<ShopDO> list = shopDao.findByEnabledAndEnvironmentAndTypeOrderById(Boolean.TRUE, req.getEnvironment(), req.getType());

        //如果传了会员id和角色id就取该值去查找所属自营商城
        if (req.getMemberId() != null && req.getMemberId() > 0 && req.getRoleId() != null && req.getRoleId() > 0) {
            //获取全部自营商城
            List<Long> selfShopIds = list.stream().filter(p -> Boolean.TRUE.equals(p.getIsDefault())).map(ShopDO::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(selfShopIds)) {

                List<ShopDO> detailList = shopDao.findByMemberIdAndMemberRoleIdAndEnabledOrderById(
                        req.getMemberId(), req.getRoleId(), Boolean.TRUE
                );

                Map<Long, ShopDO> shopNameMap = detailList.stream().collect(Collectors.toMap(ShopDO::getId, o -> o));
                list.stream().filter(p -> shopNameMap.containsKey(p.getId())).forEach(p -> {
                    ShopDO shopRuleDetail = shopNameMap.get(p.getId());
                    p.setId(shopRuleDetail.getId());
                    p.setName(shopRuleDetail.getName());
                });
            }
        }
        return list;
    }

    @Override
    public Boolean checkShopMemberOperate(Long shopId) {
        //校验
        if (shopId == null) {
            throw new BusinessException(ResponseCodeEnum.MAN_SHOP_ID_CANNOT_BE_EMPTY);
        }

        ShopDO shopDO = shopDao.getById(shopId);
        if (shopDO == null) {
            throw new BusinessException(ResponseCodeEnum.MAN_DATA_RECORD_DOES_NOT_EXIST);
        }
        return shopDO.getIsMemberOperate();

    }

    @Override
    public ShopSelectResp shopSelect(Integer environment) {

        // 调用平台后台接口，查询默认商城主体
        PlatformParameterManageResp platformParameterManageResp = WrapperUtil.getDataOrThrow(parameterManageFeign.getPlatformParameter(PlatformParameterEnum.MOBILE_DEFAULT_SHOP.getCode()));

        // 是否查询自营商城, 默认查联营商城
        Boolean isSelf = PlatformParameterValueEnum.MOBILE_DEFAULT_SHOP_UNION.getParameterValue().equals(platformParameterManageResp.getParameterValue()) ? Boolean.FALSE : Boolean.TRUE;

        if (Boolean.FALSE.equals(isSelf)) {
            // 查询联营商城，优先默认商城, 没有则查询一个最新的商城
            ShopSelectListResp shopSelectListResp = Optional.ofNullable(
                    shopDao.findShopSelectRespUnionFirstByEnvironmentAndEnabledAndType(environment, Boolean.TRUE, ShopTypeEnum.ENTERPRISE.getCode())
            ).orElseThrow(() -> new BusinessException(ResponseCodeEnum.COMMODITY_SHOP_NOT_FOUND));

            return ShopSelectResp.buildBy(platformParameterManageResp.getParameterValue(), shopSelectListResp);
        }

        // 查询自营商城，优先默认商城，没有则找最新的商城，每个会员只查一个商城
        List<ShopSelectListResp> selfShopSelectListRespList = Optional.ofNullable(
                shopDao.findShopSelectRespSelfByEnvironmentAndEnabledAndType(environment, Boolean.TRUE, ShopTypeEnum.ENTERPRISE.getCode())
        ).filter(CollUtil::isNotEmpty).orElseThrow(() -> new BusinessException(ResponseCodeEnum.COMMODITY_SHOP_NOT_FOUND));

        return ShopSelectResp.buildBy(platformParameterManageResp.getParameterValue(), selfShopSelectListRespList);
    }

}
