package com.ssy.lingxi.commodity.controller;

import com.ssy.lingxi.commodity.service.web.IStoreWebService;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.ReportTodayResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 报表统计
 * <AUTHOR>
 * @version V3.0.0
 * @since 2023/12/7
 */
@RestController
@RequestMapping(ServiceModuleConstant.COMMODITY_PATH_PREFIX + "/report")
public class StoreReportController extends BaseController {

    @Resource
    private IStoreWebService storeWebService;

    /**
     * 今日新增--平台后台
     */
    @GetMapping(value = "/getTodayNew")
    WrapperResp<ReportTodayResp> getTodayNew() {
        return WrapperUtil.success(storeWebService.getTodayNew());
    }
}
