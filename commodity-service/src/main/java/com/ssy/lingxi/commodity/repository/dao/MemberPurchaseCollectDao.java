package com.ssy.lingxi.commodity.repository.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ssy.lingxi.commodity.entity.do_.collect.MemberPurchaseCollectDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IMemberPurchaseCollectMapper;
import com.ssy.lingxi.common.model.req.PageDataReq;
import org.springframework.stereotype.Repository;

/**
* <AUTHOR>
*/
@Repository
public class MemberPurchaseCollectDao extends CommonServiceImpl<IMemberPurchaseCollectMapper, MemberPurchaseCollectDO> {

    public Boolean existsByPurchaseIdAndMemberIdAndUserId(Long purchaseId, Long memberId, Long userId) {
        return this.getFirst(
                Wrappers.lambdaQuery(MemberPurchaseCollectDO.class).select(MemberPurchaseCollectDO::getId)
                        .eq(MemberPurchaseCollectDO::getPurchaseId, purchaseId).eq(MemberPurchaseCollectDO::getMemberId, memberId).eq(MemberPurchaseCollectDO::getUserId, userId)
        ).map(MemberPurchaseCollectDO::getId).orElse(0L) > 0;
    }

    public void deleteByPurchaseIdAndMemberIdAndUserId(Long purchaseId, Long memberId, Long userId) {
        this.remove(
                Wrappers.lambdaQuery(MemberPurchaseCollectDO.class).eq(MemberPurchaseCollectDO::getPurchaseId, purchaseId).eq(MemberPurchaseCollectDO::getMemberId, memberId).eq(MemberPurchaseCollectDO::getUserId, userId)
        );
    }

    public Page<MemberPurchaseCollectDO> findByMemberIdAndUserIdOrderByCreateTimeDesc(Long memberId, Long userId, PageDataReq pageDataReq) {
        return this.page(Page.of(pageDataReq.getCurrent(), pageDataReq.getPageSize()),
                Wrappers.lambdaQuery(MemberPurchaseCollectDO.class).eq(MemberPurchaseCollectDO::getMemberId, memberId).eq(MemberPurchaseCollectDO::getUserId, userId)
                        .orderByDesc(MemberPurchaseCollectDO::getCreateTime)
        );
    }
}




