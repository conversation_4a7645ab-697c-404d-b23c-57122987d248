package com.ssy.lingxi.commodity.repository.dao.adorn;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ssy.lingxi.commodity.entity.do_.adorn.AdornMainPortalDO;
import com.ssy.lingxi.commodity.repository.dao.base.CommonServiceImpl;
import com.ssy.lingxi.commodity.repository.mapper.IAdornMainPortalMapper;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
* <AUTHOR>
*/
@Repository
public class AdornMainPortalDao extends CommonServiceImpl<IAdornMainPortalMapper, AdornMainPortalDO> {

    public Optional<AdornMainPortalDO> findFirstByAdornId(Long adornId) {
        return this.getFirst(
                Wrappers.lambdaQuery(AdornMainPortalDO.class).eq(AdornMainPortalDO::getAdornId, adornId)
        );
    }
}




