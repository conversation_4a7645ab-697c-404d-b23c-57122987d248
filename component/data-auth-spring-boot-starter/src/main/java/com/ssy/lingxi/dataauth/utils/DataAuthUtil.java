package com.ssy.lingxi.dataauth.utils;

import java.net.URI;

/**
 * 数据权限工具类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-28
 */
public class DataAuthUtil {
    /**
     * 从HttpHeader中的Referer，获得前端页面的路径
     * @return 如果有路径，返回页面路径，如果没有返回Null，此时不做数据权限验证
     */
    public static String findRequestPath(String referer) {
        try {
            URI uri = new URI(referer);
            return uri.getPath();
        } catch (Exception ignored) {
            return "";
        }
    }

    /**
     * 判断字符串是否为Null或空字符
     * @param plainString 字符串
     * @return true-是， false-否
     */
    public static boolean isEmpty(String plainString) {
        return plainString == null || plainString.length() == 0;
    }

    /**
     * 判断字符串是否不为Null
     * @param plainString 字符串
     * @return true-是， false-否
     */
    public static boolean notEmpty(String plainString) {
        return plainString != null && plainString.length() > 0;
    }
}
