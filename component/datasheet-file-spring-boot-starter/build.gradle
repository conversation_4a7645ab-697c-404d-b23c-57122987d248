plugins {
    id 'java-library'
    id 'org.sonarqube' version '3.2.0'
}

group = 'com.ssy.lingxi.component.datasheetFile'
version = '3.0.0'

repositories {
    mavenLocal()
    maven {
        url 'http://10.0.0.21:8081/repository/maven-public/'
    }
    maven {
        url 'https://maven.aliyun.com/repository/public'
    }
    mavenCentral()
}

dependencies {
    implementation project(':component:base-spring-boot-starter')
    annotationProcessor project(':component:base-spring-boot-starter')
    implementation project(':component:rabbitMQ-spring-boot-starter')

    // openfeign
    compileOnly('org.springframework.cloud:spring-cloud-starter-openfeign:3.0.2')

    // 校验工具
    compileOnly('org.hibernate.validator:hibernate-validator:6.1.7.Final')

    //servlet
    compileOnly 'javax.servlet:javax.servlet-api:4.0.1'

    api 'com.alibaba:easyexcel:4.0.3'

    //excel工具
    implementation 'org.apache.poi:poi-ooxml:5.3.0'

    // 用于编译期间生成DO to DTO等转换工具类
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'
    annotationProcessor "org.projectlombok:lombok-mapstruct-binding:0.2.0"

}

test {
    useJUnitPlatform()
}

sonarqube {
    properties {
        property "sonar.host.url", project.properties['sonarHostUrl']
        property "sonar.login", project.properties['sonarUserName']
        property "sonar.password", project.properties['sonarUserPassword']
        property "sonar.projectKey", project.properties['projectName'] + "." + project.name
        property "sonar.projectName", project.properties['projectName'] + "." + project.name
        property "sonar.projectVersion", version
        property "sonar.sources", "src/main/java"
        property "sonar.sourceEncoding", "UTF-8"
    }
}