package com.ssy.lingxi.common.constant.mq;

import java.io.Serializable;

/**
 * 消息队列常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class SupportMqConstant implements Serializable {
    private static final long serialVersionUID = 7040087603639632795L;

    /**
     * websocket消息
     */
    public static final String WS_DELIVER_MESSAGE_QUEUE = "ws_deliver_message_queue";
    public static final String WS_DELIVER_MESSAGE_EXCHANGE = "ws_deliver_message_exchange";

    /**
     * 单个发送系统消息
     */
    public static final String SYSTEM_MESSAGE_SEND_QUEUE = "system_message_send_queue";
    public static final String SYSTEM_MESSAGE_SEND_EXCHANGE = "system_message_send_exchange";
    public static final String SYSTEM_MESSAGE_SEND_ROUTINGKEY = "system_message_send_routingkey";

    /**
     * 批量发送系统消息
     */
    public static final String SYSTEM_MESSAGE_BATCH_SEND_QUEUE = "system_message_batch_send_queue";
    public static final String SYSTEM_MESSAGE_BATCH_SEND_EXCHANGE = "system_message_batch_send_exchange";
    public static final String SYSTEM_MESSAGE_BATCH_SEND_ROUTINGKEY = "system_message_batch_send_routingkey";

    /**
     * 发送自定义系统消息
     */
    public static final String SYSTEM_MESSAGE_SEND_CUSTOM_QUEUE = "system_message_send_custom_queue";
    public static final String SYSTEM_MESSAGE_SEND_CUSTOM_EXCHANGE = "system_message_send_custom_exchange";
    public static final String SYSTEM_MESSAGE_SEND_CUSTOM_ROUTINGKEY = "system_message_send_custom_routingkey";

    /**
     * aop日志信息
     */
    public static final String SYSTEM_AOP_LOG_QUEUE = "system_aop_log_queue";
    public static final String SYSTEM_AOP_LOG_EXCHANGE = "system_aop_log_exchange";
    public static final String SYSTEM_AOP_LOG_ROUTING_KEY = "system_aop_log_routing_key";

    /**
     * 数据表文件-商品服务专用
     */
    public static final String SUPPORT_COMMODITY_DATASHEET_FILE_QUEUE = "support_commodity_datasheet_file_queue";
    public static final String SUPPORT_COMMODITY_DATASHEET_FILE_EXCHANGE = "support_commodity_datasheet_file_exchange";
    public static final String SUPPORT_COMMODITY_DATASHEET_FILE_ROUTINGKEY = "support_commodity_datasheet_file_routingkey";

}
