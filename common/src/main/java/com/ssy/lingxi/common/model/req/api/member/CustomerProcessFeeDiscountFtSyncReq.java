package com.ssy.lingxi.common.model.req.api.member;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 客户工费优惠附体明细Req（入参）
 */
@Data
public class CustomerProcessFeeDiscountFtSyncReq implements Serializable {
    /**
     * 单据流水号（组合主键之一）
     */
    @NotNull(message = "附体明细：单据流水号不能为空")
    private Long djlsh;
    /**
     * 单据附体号（组合主键之一）
     */
    @NotNull(message = "附体明细：单据附体号不能为空")
    private Integer djfth;
    /**
     * 序号
     */
    private Integer xhf;
    /**
     * 物品编码
     */
    private String wpbmf;
    /**
     * 物品名称
     */
    private String wpmcf;
    /**
     * 克工费|克工费优惠
     */
    private BigDecimal kgfyhf;
    /**
     * 是否无基本工费（选项值：是，否）
     */
    private String sfwjbgff;
    /**
     * 指定工费|指定克工费
     */
    private BigDecimal zdkgff;
    /**
     * 材质编码
     */
    private String czbmf;
    /**
     * 材质名称
     */
    private String czmcf;
    /**
     * 优惠截止时间（过期后该条不生效）
     */
    private String yhjzsjf;
}

