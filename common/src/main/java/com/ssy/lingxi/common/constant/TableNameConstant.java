package com.ssy.lingxi.common.constant;

import java.io.Serializable;

/**
 * 公共的常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/19
 */
public class TableNameConstant implements Serializable {
    private static final long serialVersionUID = 6247286067131707070L;

    /**
     * 数据库表schema
     */
    public static final String TABLE_SCHEMA = "public";

    /**
     * 数据库表名前缀-售后服务
     */
    public static final String TABLE_PRE_AFTERSALES_SERVICE = "aft_";

    /**
     * 数据库表名前缀-api服务
     */
    public static final String TABLE_PRE_API_SERVICE = "api_";

    /**
     * 数据库表名前缀-合同服务
     */
    public static final String TABLE_PRE_CONTRACT_SERVICE = "con_";

    /**
     * 数据库表名前缀-加工服务
     */
    public static final String TABLE_PRE_ENHANCE_SERVICE = "enh_";

    /**
     * 数据库表名前缀-物流服务
     */
    public static final String TABLE_PRE_LOGISTICS_SERVICE = "log_";

    /**
     * 数据库表名前缀-营销服务
     */
    public static final String TABLE_PRE_MARKETING_SERVICE = "mar_";

    /**
     * 数据库表名前缀-会员服务
     */
    public static final String TABLE_PRE_MEMBER_SERVICE = "mem_";

    /**
     * 数据库表名前缀-支撑服务
     */
    public static final String TABLE_PRE_SUPPORT_SERVICE = "sup_";

    /**
     * 数据库表名前缀-订单服务
     */
    public static final String TABLE_PRE_ORDER_SERVICE = "ord_";

    /**
     * 数据库表名前缀-支付服务
     */
    public static final String TABLE_PRE_PAY_SERVICE = "pay_";

    /**
     * 数据库表名前缀-平台后台服务
     */
    public static final String TABLE_PRE_MANAGE_SERVICE = "man_";

    /**
     * 数据库表名前缀-商品服务
     */
    public static final String TABLE_PRE_PRODUCT_SERVICE = "pro_";

    /**
     * 数据库表名前缀-采购服务
     */
    public static final String TABLE_PRE_PURCHASE_SERVICE = "pur_";

    /**
     * 数据库表名前缀-报表服务
     */
    public static final String TABLE_PRE_REPORT_SERVICE = "rep_";

    /**
     * 数据库表名前缀-定时任务服务
     */
    public static final String TABLE_PRE_SCHEDULE_SERVICE = "sch_";

    /**
     * 数据库表名前缀-规则引擎服务
     */
    public static final String TABLE_PRE_ENGINE_SERVICE = "eng_";

    /**
     * 数据库表名前缀-结算服务
     */
    public static final String TABLE_PRE_SETTLEMENT_SERVICE = "set_";

    /**
     * 数据库表名前缀-交易服务
     */
    public static final String TABLE_PRE_TRADE_SERVICE = "tra_";

    /**
     * 数据库表名前缀-工作流服务
     */
    public static final String TABLE_PRE_WORKFLOW_SERVICE = "wor_";
}
