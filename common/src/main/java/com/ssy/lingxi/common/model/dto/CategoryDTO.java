package com.ssy.lingxi.common.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @description redis缓存中品类对象
 * @since 2021/8/23
 */
@Data
public class CategoryDTO {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 品类名称
     */
    private String name;

    /**
     * 品类类型：1-实物商品、2-虚拟商品、3-服务商品、4-积分兑换商品
     */
    private Integer type;

    /**
     * 品类图片url路径
     */
    private String imageUrl;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 品类父id
     */
    private Long parentId;

    /**
     * 完整Id
     */
    private String fullId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员角色id
     */
    private Long memberRoleId;
}
