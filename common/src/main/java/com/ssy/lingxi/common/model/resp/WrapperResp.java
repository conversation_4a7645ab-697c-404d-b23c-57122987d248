package com.ssy.lingxi.common.model.resp;


import java.io.Serializable;

/**
 * 统一返回数据模型
 */
public class WrapperResp<T> implements Serializable {
    private static final long serialVersionUID = 3184376297284610305L;

    /**
     * 状态码
     */
    private int code;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应时间戳
     */
    private Long time;

    public WrapperResp() {
    }

    public WrapperResp(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public WrapperResp(int code, String message, T data, Long time) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.time = time;
    }

    public WrapperResp(int code, String message, Long time) {
        this.code = code;
        this.message = message;
        this.data = null;
        this.time = time;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public T getData() {
        return data;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }
}
