package com.ssy.lingxi.common.model.resp.engine;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 规则引擎配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/21
 */
@Getter
@Setter
public class RuleEngineInfoResp implements Serializable {
    private static final long serialVersionUID = 809438539846578874L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 规则引擎类型：1-物料管理; 2-采购单流程; 3-合同管理; 4-合同协同; 5-请购单流程; 6-请款单管理;
     * 7-采购订单SRM; 8-售后管理B2B; 9-质量管理; 10-生命周期变更;
     * RuleEngineTypeEnum枚举类
     */
    private Integer type;

    /**
     * 流程ID
     */
    private String processId;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 流程步骤
     */
    private Integer processStep;

    /**
     * 流程步骤名称
     */
    private String processStepName;

    /**
     * 流程类型
     * ProcessTypeDetailEnum.type
     */
    private Integer processType;

    /**
     * 流程规则
     */
    private List<RuleEngineFieldResp> ruleEngineConfigFieldRelations = new ArrayList<>();

    /**
     * 添加关联字段
     * @param ruleEngineField
     */
    public void addFieldRelation(RuleEngineFieldResp ruleEngineField){
        this.ruleEngineConfigFieldRelations.add(ruleEngineField);
    }
}
