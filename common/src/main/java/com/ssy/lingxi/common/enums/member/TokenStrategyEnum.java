package com.ssy.lingxi.common.enums.member;

import lombok.Getter;

/**
 * token策略枚举
 *
 * <AUTHOR>
 * @version 3.0.0
 * @date 2023/12/19
 */
@Getter
public enum TokenStrategyEnum {
    /**
     * 登录
     */
    LOGIN(1),

    /**
     * 续期
     */
    RENEWAL(2),

    /**
     * 切换角色
     */
    SWITCH_ROLES(3),

    /**
     * 单点登录（未开发）
     */
    SSO(4),
    ;

    private final int code;

    TokenStrategyEnum(int code) {
        this.code = code;
    }
}
