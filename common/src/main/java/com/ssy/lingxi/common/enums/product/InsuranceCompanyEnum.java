package com.ssy.lingxi.common.enums.product;

import lombok.Getter;

/**
 * 保价公司
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/23
 */
@Getter
public enum InsuranceCompanyEnum {

    /**
     * 顺丰保险
     */
    SF_INSURANCE(1, "顺丰保险"),

    /**
     * 太平
     */
    TAIPING_INSURANCE(2, "太平保险");

    private final Integer code;
    private final String name;

    InsuranceCompanyEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取名称
     */
    public static String getNameByCode(Integer code) {
        for (InsuranceCompanyEnum insuranceCompanyEnum : values()) {
            if (insuranceCompanyEnum.getCode().equals(code)) {
                return insuranceCompanyEnum.getName();
            }
        }
        return null;
    }
}
