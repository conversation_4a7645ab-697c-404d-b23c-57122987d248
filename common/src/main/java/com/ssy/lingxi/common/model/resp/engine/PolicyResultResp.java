package com.ssy.lingxi.common.model.resp.engine;

import com.ssy.lingxi.common.model.dto.engine.PolicyDetailDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 决策结果
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-04
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolicyResultResp implements Serializable {

    private static final long serialVersionUID = -2092889337223054378L;

    /**
     * 是否跳过
     */
    private Boolean skip;

    /**
     * 跳过多少步
     */
    private Integer stepWidth;

    /**
     * 决策明细
     */
    private List<PolicyDetailDTO> detailList = new ArrayList<>();
}
