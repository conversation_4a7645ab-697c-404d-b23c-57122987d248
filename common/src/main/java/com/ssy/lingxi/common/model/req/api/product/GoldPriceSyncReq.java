package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 黄金价格同步
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-28
 */
@Getter
@Setter
public class GoldPriceSyncReq implements Serializable {
    private static final long serialVersionUID = 6963329417929452679L;

    /**
     * 单据号
     */
    private String djh;

    /**
     * 金价
     */
    @NotNull(message = "金价不能为空")
    private BigDecimal jj;

    /**
     * 发布时间
     */
    private String fbsj;
}
