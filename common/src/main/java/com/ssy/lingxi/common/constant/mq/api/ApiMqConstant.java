package com.ssy.lingxi.common.constant.mq.api;

import java.io.Serializable;

/**
 * 消息队列常量类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class ApiMqConstant implements Serializable {
    private static final long serialVersionUID = 1550285993754492277L;

    /**
     * 接口调用失败重试(调用外部系统接口)
     */
    public static final String OPEN_API_RETRY_QUEUE = "open_api_retry_queue";
    public static final String OPEN_API_RETRY_EXCHANGE = "open_api_retry_exchange";
    public static final String OPEN_API_RETRY_ROUTINGKEY = "open_api_retry_routingKey";

    /**
     * 记录错误日志(用于入库操作)
     */
    public static final String OPEN_API_ERROR_QUEUE = "open_api_error_queue";
    public static final String OPEN_API_ERROR_EXCHANGE = "open_api_error_exchange";
    public static final String OPEN_API_ERROR_ROUTINGKEY = "open_api_error_routingKey";

    /**
     * API接口原数据记录
     */
    public static final String OPEN_API_DATA_QUEUE = "open_api_data_queue";
    public static final String OPEN_API_DATA_EXCHANGE = "open_api_data_exchange";
    public static final String OPEN_API_DATA_ROUTINGKEY = "open_api_data_routingKey";
}
