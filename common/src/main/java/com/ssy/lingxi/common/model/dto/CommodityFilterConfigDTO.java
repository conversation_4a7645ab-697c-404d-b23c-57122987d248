package com.ssy.lingxi.common.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 商品筛选配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-06
 */
@Getter
@Setter
public class CommodityFilterConfigDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 筛选项名称
     */
    private String name;

    /**
     * 筛选类型
     * CommodityFilterTypeEnum
     */
    private Integer type;

    /**
     * 品类
     */
    private Long[] customerCategoryIds;

    /**
     * 配置的值列表
     */
    List<CommodityFilterConfigValueDTO> configValueDTOList;
}
