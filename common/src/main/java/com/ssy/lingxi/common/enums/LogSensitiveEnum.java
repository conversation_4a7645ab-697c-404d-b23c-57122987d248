package com.ssy.lingxi.common.enums;

import lombok.Getter;

/**
 * 日志中需要过滤的敏感信息
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/1/15
 */
@Getter
public enum LogSensitiveEnum {
    /**
     * 密码
     */
    PASSWORD("password"),

    /**
     * 密钥
     */
    SECRET("secret"),

    /**
     * 会话
     */
    TOKEN("token")

    ;

    private final String code;

    LogSensitiveEnum(String code) {
        this.code = code;
    }
}
