package com.ssy.lingxi.common.model.req.api.order;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 订单状态同步请求实体类
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderProducerReq extends ApiBaseReq implements Serializable {
    private static final long serialVersionUID = -6386064244121398438L;

    /**
     * 订单号（销售订单）
     */
    @NotEmpty(message = "销售单订单号不能为空")
    private String orderNo;

    /**
     * 单件信息
     */
    @NotEmpty(message = "单件信息不能为空")
    private List<CommodityReq> commodityReqList;

}
