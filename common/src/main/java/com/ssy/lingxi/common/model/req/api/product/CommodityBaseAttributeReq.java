package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.enums.CommodityDescribeAttributeDataTypeEnum;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 商品基础属性
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-14
 */
@Getter
@Setter
public class CommodityBaseAttributeReq implements Serializable {
    private static final long serialVersionUID = -2660079897612503183L;

    /**
     * 商品描述字段名称
     */
    @NotBlank(message = "商品描述字段名称不能为空")
    private String attributeName;

    /**
     * 关联商品字段层级
     */
    @NotNull(message = "请选择关联商品字段层级")
    private Integer fieldLevelType;

    /**
     * 是否必填：true-是，false-否
     */
    @NotNull(message = "请选择是否必填")
    private Boolean mustFlag;

    /**
     * 商品关联属性类型
     */
    @NotNull(message = "请选择商品关联属性类型")
    private Integer fieldType;

    /**
     * 属性数据类型
     * @see CommodityDescribeAttributeDataTypeEnum
     */
    @NotNull(message = "请选择属性数据类型")
    private Integer attributeDataType;

    /**
     * 关联的属性值列表
     */
    private List<CommodityBaseAttributeValueReq> attributeValueList;
}
