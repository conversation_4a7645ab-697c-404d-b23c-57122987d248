package com.ssy.lingxi.common.model.resp.engine;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 规则引擎配置字段
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/21
 */
@Getter
@Setter
public class RuleEngineConfigFieldResp implements Serializable {
    private static final long serialVersionUID = -4437282057559053030L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 是否连表
     */
    private Boolean joinTableEnable;

    /**
     * 关联表
     */
    private String joinTable;

    /**
     * 主表关联字段
     */
    private String mainField;

    /**
     * 关联字段
     */
    private String joinField;

    /**
     * 数据库字段编码
     */
    private String code;

    /**
     * 业务字段类型: 1-字符; 2-数字; 3-日期;
     */
    private Integer type;

    /**
     * 条件: 1(=); 2(!=); 3(>); 4(>=); 5(<); 6(<=); 7(包含); 8(不包含);
     * 当type=1时：1(=); 2(!=); 7(包含); 8(不包含);
     * 当type=2和3时：1(=); 2(!=); 3(>); 4(>=); 5(<); 6(<=);
     */
    private Integer condition;

    /**
     * 值
     */
    private String value;

    /**
     * 是否查询所有(0.否 1.是)
     */
    private Integer isQueryAll = 0;
}
