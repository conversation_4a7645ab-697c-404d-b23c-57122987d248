package com.ssy.lingxi.common.model.req.api.product;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 属性值实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@Data
public class AttributeValueProducerReq implements Serializable {
    private static final long serialVersionUID = 1049609832708617917L;

    /**
     * 属性值名称
     */
    @NotEmpty(message = "属性值名称不能为空")
    @Length(max = 32,message = "属性值名称最大字符长度32")
    private String value;

    /**
     * 属性值图片url路径
     */
    @Length(max = 250,message = "属性值图片url路径最大字符长度250")
    private String imageUrl;

    /**
     * 是否有效
     */
    @NotNull(message = "是否有效不能为空")
    private Boolean isEnable;

}
