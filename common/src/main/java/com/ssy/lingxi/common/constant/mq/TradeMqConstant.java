package com.ssy.lingxi.common.constant.mq;

import lombok.NoArgsConstructor;

/**
 * 交易服务-mq常量类
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class TradeMqConstant {

    /**
     * 寻源报价--延迟队列
     */
    public static final String TRADE_DELAY_QUEUE = "trade_delay_queue";
    /**
     * 寻源报价--延迟交换机
     */
    public static final String TRADE_DELAY_EXCHANGE = "trade_delay_exchange";
    /**
     * 寻源报价--延迟路由key
     */
    public static final String TRADE_DELAY_ROUTING_KEY = "trade_delay_routing_key";

}
