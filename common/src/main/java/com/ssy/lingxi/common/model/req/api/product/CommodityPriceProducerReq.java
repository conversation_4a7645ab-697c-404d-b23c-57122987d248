package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 商品实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CommodityPriceProducerReq extends ApiBaseReq implements Serializable {
    private static final long serialVersionUID = 4670828196588936744L;

    /**
     * 商品价格
     */
    @Valid
    @NotEmpty(message = "商品价格不能为空")
    private List<CommodityPriceListReq> commodityPrice;

}
