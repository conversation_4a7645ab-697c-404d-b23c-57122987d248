package com.ssy.lingxi.common.model.resp.select;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 系统通用 - 手机号下拉框返回Resp
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023-09-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class PhoneSelectResp extends SelectStringResp implements Serializable {
    private static final long serialVersionUID = -8822150563565236296L;

    public PhoneSelectResp(String value, String label, Integer phoneLength) {
        super(value, label);
        this.phoneLength = phoneLength;
    }

    /**
     * 手机号码位数
     */
    private Integer phoneLength;
}
