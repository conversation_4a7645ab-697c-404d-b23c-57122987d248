package com.ssy.lingxi.common.model.req.api.member;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 实控人同步请求参数
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/7
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ActualControllerSyncReq extends ApiBaseReq implements Serializable {
    private static final long serialVersionUID = 8292303272809601005L;

    /**
     * 实控人编码
     */
    @NotBlank(message = "实控人编码不能为空")
    private String code;

    /**
     * 实控人名称
     */
    @NotBlank(message = "实控人名称不能为空")
    private String name;

    /**
     * 实控人身份证号码
     */
    private String idCardNo;

    /**
     * 实控人手机号
     */
    private String phone;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 授信额度 - 单位kg
     */
    @NotNull(message = "授信额度不能为空")
    private BigDecimal creditQuota;

    /**
     * 授信有效期开始时间
     */
    @NotBlank(message = "授信有效期开始时间不能为空")
    private String validStartTime;

    /**
     * 授信有效期结束时间
     */
    @NotBlank(message = "授信有效期结束时间不能为空")
    private String validEndTime;

    /**
     * 客户授信
     */
    @NotEmpty(message = "客户授信不能为空")
    private List<CustomerCreditSync> customerCreditSyncs;

}
