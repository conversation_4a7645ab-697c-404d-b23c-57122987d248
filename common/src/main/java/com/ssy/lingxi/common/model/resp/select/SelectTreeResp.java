package com.ssy.lingxi.common.model.resp.select;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONField;
import com.ssy.lingxi.common.util.TreeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 系统通用 - 树型下拉框返回Resp
 * @see TreeUtils
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/9/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SelectTreeResp extends SelectStringResp implements Serializable {
    private static final long serialVersionUID = 1267940863265625612L;

    public SelectTreeResp(String id, String pid, String value, String label) {
        super(value, label);
        this.id = id;
        this.pid = pid;
    }

    public SelectTreeResp(String id, String pid, String value, String label, List<SelectStringResp> children) {
        super(value, label);
        this.id = id;
        this.pid = pid;
        this.children = children;
    }

    public SelectTreeResp(String id, String pid, String value, String label, Boolean disabled, List<SelectStringResp> children) {
        super(value, label, disabled);
        this.id = id;
        this.pid = pid;
        this.children = children;
    }

    /**
     * id
     */
    @JSONField(serialize = false)
    private String id;

    /**
     * 父id
     */
    @JSONField(serialize = false)
    private String pid;

    /**
     * 子下拉框
     */
    @JSONField(serializeFeatures = JSONWriter.Feature.WriteNullListAsEmpty)
    private List<SelectStringResp> children;
}
