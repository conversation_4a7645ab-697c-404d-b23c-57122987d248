package com.ssy.lingxi.common.model.req.api.product;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@Data
public class CommodityPriceListReq implements Serializable {
    private static final long serialVersionUID = 6964353265837433874L;

    /**
     * 商品sku编码
     */
    @NotEmpty(message = "商品sku编码不能为空")
    @Length(max = 32,message = "商品sku编码最大字符长度32")
    private String skuCode;

    /**
     * 建议售价
     */
    private BigDecimal guidePrice;

    /**
     * 采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 最低售价
     */
    private BigDecimal minPrice;
}
