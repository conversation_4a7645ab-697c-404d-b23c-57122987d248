package com.ssy.lingxi.common.model.req;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 分页查询请求
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-01
 */
@Getter
@Setter
public class PageDataReq implements Serializable {
    private static final long serialVersionUID = 2857050142049334121L;

    /**
     * 当前页(默认值: 1)
     */
    @Positive(message = "{PageDataReq.current.Positive}")
    private int current = 1;

    /**
     * 每页行数(默认值: 10)
     */
    @Positive(message = "{PageDataReq.pageSize.Positive}")
    private int pageSize = 10;

    /**
     * 获取当前页的偏移量
     * eg: jpaQueryFactory.select(xxx).from(xxxDO).where(xxx).offset(pageDataReq.getCurrentOffset()).limit(pageDataReq.getPageSize()).fetch();
     */
    public int getCurrentOffset() {
        return current <= 1 ? 0 : (current - 1) * pageSize;
    }

    /**
     * 创建分页查询请求
     */
    public static PageDataReq of(int current, int pageSize) {
        PageDataReq pageDataReq = new PageDataReq();
        pageDataReq.setCurrent(current);
        pageDataReq.setPageSize(pageSize);
        return pageDataReq;
    }

}
