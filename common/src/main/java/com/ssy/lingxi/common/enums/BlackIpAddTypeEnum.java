package com.ssy.lingxi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 添加ip黑名单的方式枚举
 *
 * <AUTHOR>
 * @version 3.0.0
 * @date 2024/1/20
 */
@Getter
@AllArgsConstructor
public enum BlackIpAddTypeEnum {
    /**
     * 人工手动增加
     */
    MANUAL(1, "人工"),

    /**
     * 系统自动检测到自动增加
     */
    SYSTEM(2, "监控");

    private final Integer code;
    private final String msg;

    public static String getMsg(Integer code) {
        return Arrays.stream(BlackIpAddTypeEnum.values())
                .filter(blackIpAddTypeEnum -> Objects.equals(blackIpAddTypeEnum.code, code))
                .map(BlackIpAddTypeEnum::getMsg)
                .findAny()
                .orElse("");
    }
}
