package com.ssy.lingxi.common.constant.mq;

import java.io.Serializable;

/**
 * 消息队列常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class PayMqConstant implements Serializable {
    private static final long serialVersionUID = 6433587020325818095L;

    /**
     * 会员资金账户充值--延迟队列
     */
    public static final String PAY_RECHARGE_DELAY_QUEUE = "pay_recharge_delay_queue";
    public static final String PAY_RECHARGE_DELAY_EXCHANGE = "pay_recharge_delay_exchange";
    public static final String PAY_RECHARGE_DELAY_ROUTING_KEY = "pay_recharge_delay_routing_Key";


    /**
     * 线上支付渠道的支付结果查询--延迟队列
     */
    public static final String PAY_RESULT_QUERY_DELAY_QUEUE = "pay_result_query_delay_queue";
    public static final String PAY_RESULT_QUERY_DELAY_EXCHANGE = "pay_result_query_delay_exchange";
    public static final String PAY_RESULT_QUERY_DELAY_ROUTING_KEY = "pay_result_query_delay_routing_Key";
}
