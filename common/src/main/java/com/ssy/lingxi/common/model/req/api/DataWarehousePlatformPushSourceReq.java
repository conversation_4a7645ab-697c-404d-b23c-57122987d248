package com.ssy.lingxi.common.model.req.api;

import com.ssy.lingxi.common.enums.DataWarehousePlatformPushTableTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 数仓平台推送数据来源
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-17
 */
@Getter
@Setter
public class DataWarehousePlatformPushSourceReq implements Serializable {
    private static final long serialVersionUID = 665157457620083350L;

    /**
     * 系统标识
     */
    private String name;

    /**
     * 数据库名称
     */
    private String db;

    /**
     * 表名称
     * @see DataWarehousePlatformPushTableTypeEnum
     */
    private String table;
}
