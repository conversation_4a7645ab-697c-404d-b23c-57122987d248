package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 数仓平台库存批量同步
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-23
 */
@Getter
@Setter
public class FreightSpaceDataBatchSyncReq implements Serializable {
    private static final long serialVersionUID = 2302781071123161844L;

    /**
     * 库存列表
     */
    @NotEmpty(message = "库存数组不能为空")
    private List<FreightSpaceDataSyncReq> freightSpaceList;
}
