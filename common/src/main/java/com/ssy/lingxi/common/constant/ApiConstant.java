package com.ssy.lingxi.common.constant;

import java.io.Serializable;

/**
 * 公共的常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/19
 */
public class ApiConstant implements Serializable {
    private static final long serialVersionUID = -2278476453377694641L;

    /**
     * openApi错误消息
     */
    public static final String API_ERROR_PREFIX = "error:";

    /**
     * openApi错误消息,redis可以过期天数
     */
    public static final Integer ERROR_KEY_DAYS = 3;

    /**
     * openApi失败重试次数
     */
    public static final Integer COUNT = 3;

    /**
     * openApi失败重试间隔(秒)
     */
    public static final Long INTERVAL = 10L;

    /**
     * openApi接口响应成功结果
     */
    public static final Integer SUCCESS_CODE = 200;

}
