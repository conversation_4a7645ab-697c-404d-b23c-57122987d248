package com.ssy.lingxi.common.enums.product;

/**
 * 物料价格状态类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/25
 */
public enum MaterielPriceStatusEnum {

    /**
     * 1:正常; 2:变更; 3:到期; 4:停用; 5:作废;
     */
    NORMAL(1, "正常"),
    CHANGE(2, "变更"),
    MATURITY(3, "到期"),
    DISABLE(4, "停用"),
    ABANDONED(5, "作废");

    private final Integer code;
    private final String message;

    MaterielPriceStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
