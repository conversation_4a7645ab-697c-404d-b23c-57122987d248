package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 数仓平台库存同步入参
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-23
 */
@Getter
@Setter
public class FreightSpaceDataSyncReq implements Serializable {
    private static final long serialVersionUID = 3353949910836704816L;

    /**
     * 库存状态
     * （库存：单件商品还在该库存里
     * 销售：单件商品已被销售
     * 作废：单件商品数据被作废）
     */
    private String kczt;

    /**
     * 开单状态
     * （未开单：闲置状态
     * 已开单：被单据占用状态
     * 锁定中：被单据占用状态）
     */
    private String kdzt;

    /**
     * 商品条码
     */
    private String sptm;

    /**
     * 仓库编码
     */
    private String ckbm;

    /**
     * 仓库名称
     */
    private String ckmc;

    /**
     * 物品编码
     */
    private String wpbm;

    /**
     * 物品名称
     */
    private String wpmc;

    /**
     * 规格
     */
    private String gg;

    /**
     * 件数
     */
    private String js;

    /**
     * 金重
     */
    private String jz;

    /**
     * 货重
     */
    private String hz;

    /**
     * 材质编码
     */
    private String czbm;

    /**
     * 材质名称
     */
    private String czmc;

    /**
     * 款式编码
     */
    private String ksbm;

    /**
     * sku编码
     */
    private String skunumber;

    /**
     * 成本克工费
     */
    private String cbkgf;

    /**
     * 成本件工费
     */
    private String cbjgf;

    /**
     * 销售克工费：附件克工费
     */
    private String xskgf;

    /**
     * 销售件工费：件工费
     */
    private String xsjgf;

    /**
     * 标签价格
     */
    private String bqjg;

    /**
     * 基本工费：基础工费
     */
    private String jbgf;
}
