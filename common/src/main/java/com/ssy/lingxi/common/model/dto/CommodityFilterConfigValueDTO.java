package com.ssy.lingxi.common.model.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 商品筛选配置属性值
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-06
 */
@Getter
@Setter
public class CommodityFilterConfigValueDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 筛选配置ID
     */
    private Long filterConfigId;

    /**
     * 属性ID
     * CommodityDescribeAttributeDO
     * CustomerAttributeDO
     */
    private Long attributeId;

    /**
     * 属性值ID
     * CommodityDescribeAttributeValueDO
     * CustomerAttributeValueDO
     */
    private Long attributeValueId;

    /**
     * 属性值名称
     */
    private String attributeValueName;

    /**
     * 属性类型
     * CustomerAttributeTypeEnum
     */
    private Integer attributeType;

    /**
     * 品类
     */
    private Long[] customerCategoryIds;
}
