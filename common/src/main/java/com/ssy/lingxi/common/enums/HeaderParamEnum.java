package com.ssy.lingxi.common.enums;

import lombok.Getter;

/**
 * http头部参数
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/2/24
 */
@Getter
public enum HeaderParamEnum {
    ACCESS_TOKEN("accessToken", "登录用户唯一标识"),
    SHOP_ID("shopid", "商城id"),
    ACCEPT_LANGUAGE("accept-language", "语言"),
    FEIGN_HEADER("feignHeader", "Feign内部接口头部固定常量");

    private final String code;
    private final String remark;

    HeaderParamEnum(String code, String remark) {
        this.code = code;
        this.remark = remark;
    }

}
