package com.ssy.lingxi.common.model.resp.engine;

import com.ssy.lingxi.common.model.dto.engine.ProcessEngineRuleFieldDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * 流程规则VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-31
 **/
@Data
public class ProcessEngineRuleResp implements Serializable {

    private static final long serialVersionUID = -6456533960907537406L;

    /**
     * 流程规则Id
     */
    private Long processRuleId;

    /**
     * 流程标识
     */
    private String processKey;

    /**
     * 是否默认流程 0.否 1.是
     */
    private Integer isDefault = 0;

    /**
     * 字段关系: 1-and; 2-or;
     */
    private Integer relation;

    /**
     * 流程引擎配置字段
     */
    private List<ProcessEngineRuleFieldDTO> ruleFieldList = new LinkedList<>();

}
