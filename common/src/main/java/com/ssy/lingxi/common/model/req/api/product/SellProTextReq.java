package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 数仓商品描述富文本
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-27
 */
@Getter
@Setter
public class SellProTextReq implements Serializable {
    private static final long serialVersionUID = -7339253172154603387L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 商品id
     */
    private Integer sell_pro_id;

    /**
     * 类型：描述:1   模板:2
     */
    private Integer text_type;

    /**
     * 文本内容
     */
    private String sell_pro_text;
}
