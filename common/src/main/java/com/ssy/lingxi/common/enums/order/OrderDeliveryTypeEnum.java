package com.ssy.lingxi.common.enums.order;

import lombok.Getter;

/**
 * 配送方式
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/8
 */
@Getter
public enum OrderDeliveryTypeEnum {

    /**
     * 自提
     */
    SELF_PICKUP(1, "自提"),

    /**
     * 快递配送
     */
    EXPRESS_DELIVERY(2, "快递配送"),

    /**
     * 快递代发
     */
    EXPRESS_AGENCY(3, "快递代发");

    private final Integer code;
    private final String desc;

    OrderDeliveryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取desc
     * @param code 枚举code
     * @return desc
     */
    public static String getDescByCode(Integer code) {
        for (OrderDeliveryTypeEnum type : OrderDeliveryTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

}
