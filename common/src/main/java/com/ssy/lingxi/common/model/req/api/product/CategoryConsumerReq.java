package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 品类实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CategoryConsumerReq extends ApiBaseReq implements Serializable {
    private static final long serialVersionUID = -9122771279317902666L;

    /**
     * 品类编码
     */
    private String itemCateCode;

    /**
     * 品类名称
     */
    private String itemCateName;

    /**
     * 父编码
     */
    private String pCode;

    /**
     * 层级
     */
    private Integer level;

}
