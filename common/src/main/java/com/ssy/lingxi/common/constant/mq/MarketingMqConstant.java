package com.ssy.lingxi.common.constant.mq;

import java.io.Serializable;

/**
 * 营销活动，redis key获取
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/22
 */
public class MarketingMqConstant implements Serializable {
    private static final long serialVersionUID = 5306785283170618420L;

    /**
     * 营销活动--延迟队列（拼团超时、优惠券超时）
     */
    public static final String MK_DELAY_QUEUE = "mk_delay_queue";
    public static final String MK_DELAY_EXCHANGE = "mk_delay_exchange";
    public static final String MK_DELAY_ROUTING_KEY = "mk_delay_routing_key";

    /**
     * 营销活动--普通队列(赠送促销-赠优惠券)
     */
    public static final String MK_NORMAL_QUEUE = "mk_normal_queue";
    public static final String MK_NORMAL_EXCHANGE = "mk_normal_exchange";
    public static final String MK_NORMAL_ROUTING_KEY = "mk_normal_routing_key";

    /**
     * 活动商品销量监听-普通队列-加锁
     */
    public static final String MK_GOODS_SALES_QUEUE = "mk_goods_sales_queue";
    public static final String MK_GOODS_SALES_EXCHANGE = "mk_goods_sales_exchange";
    public static final String MK_GOODS_SALESROUTINGKEY = "mk_goods_sales_routingkey";

}
