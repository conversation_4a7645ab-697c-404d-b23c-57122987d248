package com.ssy.lingxi.common.model.resp.engine;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 规则引擎响应类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/29
 */
@Data
public class RuleEngineProcessResp implements Serializable {
    private static final long serialVersionUID = 809438529846578874L;

    /**
     * 流程规则ID
     */
    private Long processId;

    /**
     * 流程key
     */
    private String processKey;

    /**
     * 流程name
     */
    private String processName;

    /**
     * 流程类型 MemberCycleProcessTypeEnum
     *  1. 供应商生命周期变更流程规则
     *  2. 客户生命周期变更流程规则
     */
    private Integer processType;

    /**
     * 流程步骤列表
     */
    private List<SimpleProcessResp> responses;


}
