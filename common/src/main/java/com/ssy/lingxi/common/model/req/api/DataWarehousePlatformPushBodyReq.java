package com.ssy.lingxi.common.model.req.api;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 数仓平台推送数据body
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-17
 */
@Getter
@Setter
public class DataWarehousePlatformPushBodyReq implements Serializable {
    private static final long serialVersionUID = -2164489708614264693L;

    /**
     * 变更前数据json字符串
     */
    private Object before;

    /**
     * 变更后数据json字符串
     */
    private Object after;

    /**
     * 数据来源
     */
    private Object source;

    /**
     * - r (Read) : 表示该记录是在初始阶段读取的。
     * - c (Create) : 插入新记录。
     * - u (Update) : 更新记录。
     * - d (Delete) : 删除记录。
     */
    private String op;
}
