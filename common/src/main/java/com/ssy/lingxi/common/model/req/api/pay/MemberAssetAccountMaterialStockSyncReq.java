package com.ssy.lingxi.common.model.req.api.pay;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 存料数据同步请求入参
 *
 * @version 3.0.0
 * @autho zhuzhenxuan
 * @since 2025/5/23
 */
@Data
public class MemberAssetAccountMaterialStockSyncReq {


    /**
     * 客户编码
     */
    @NotEmpty(message = "主客户编码，不能为空")
    @NotBlank
    private String khbm;

    /**
     * 客户名称
     */
    private String khmc;

    /**
     * 存欠金料
     */
    private BigDecimal cqjl;

    /**
     * 存欠金额
     */
    private BigDecimal cqje;

}
