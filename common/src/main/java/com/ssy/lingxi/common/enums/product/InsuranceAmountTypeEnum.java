package com.ssy.lingxi.common.enums.product;

import lombok.Getter;

/**
 * 保费收费类型
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/22
 */
@Getter
public enum InsuranceAmountTypeEnum {

    /**
     * 比例
     */
    PERCENTAGE(1, "比例"),

    /**
     * 固定
     */
    FIXED(2, "固定");

    private final Integer code;
    private final String message;

    InsuranceAmountTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessage(Integer code) {
        for (InsuranceAmountTypeEnum typeEnum : InsuranceAmountTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getMessage();
            }
        }
        return "";
    }
}
