package com.ssy.lingxi.common.model.resp.engine;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 规则引擎配置字段关系(流程规则)
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/21
 */
@Getter
@Setter
public class RuleEngineFieldRelationResp implements Serializable {

    private static final long serialVersionUID = 7092992173444181106L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 规则引擎配置字段id
     */
    private RuleEngineConfigFieldResp ruleEngineConfigField;

    /**
     * 字段关系: 1-and; 2-or;
     */
    private Integer relation;

}
