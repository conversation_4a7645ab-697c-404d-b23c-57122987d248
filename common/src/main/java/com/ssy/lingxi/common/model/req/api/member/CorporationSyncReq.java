package com.ssy.lingxi.common.model.req.api.member;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * 企业同步请求参数
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/7
 */
@Data
public class CorporationSyncReq implements Serializable {
    private static final long serialVersionUID = -5695654682007860804L;

    /**
     * 企业编码
     */
    @JSONField(name = "company_code")
    @NotBlank(message = "企业编码不能为空")
    private String code;

    /**
     * 企业名称
     */
    @JSONField(name = "company_name")
    @NotBlank(message = "企业名称不能为空")
    private String name;
    
    /**
     * 统一社会信用代码
     */
    @JSONField(name = "license_no")
    @NotBlank(message = "会员统一社会信用代码不能为空")
    private String unifiedSocialCode;

    /**
     * 营业执照有效期
     */
    private String businessLicenseValidEndTime;

    /**
     * 营业执照
     */
    private String businessLicense;

    /**
     * 会员法人姓名
     */
    @JSONField(name = "legal_person")
    @NotBlank(message = "会员法人姓名不能为空")
    private String legalPersonName;

    /**
     * 会员法人身份证号
     */
    @JSONField(name = "legal_person_card_no")
    @NotBlank(message = "会员法人身份证号不能为空")
    private String legalPersonIdCardNo;

    /**
     * 会员法人手机号
     */
    @JSONField(name = "legal_person_phone")
    @NotBlank(message = "会员法人手机号不能为空")
    private String legalPersonPhone;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 业务负责人
     */
    @JSONField(name = "business_owner1")
    private String businessManager;

    /**
     * 业务负责人手机号
     */
    @JSONField(name = "business_owner_phone1")
    private String businessManagerPhone;

    /**
     * 财务负责人
     */
    @JSONField(name = "business_owner2")
    private String financeManager;

    /**
     * 财务负责人手机号
     */
    @JSONField(name = "business_owner_phone2")
    private String financeManagerPhone;

    /**
     * 数据来源
     */
    @JSONField(name = "c_app")
    private String dataSource;

}
