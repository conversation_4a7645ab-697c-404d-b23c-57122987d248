package com.ssy.lingxi.common.model.resp.select;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.io.Serializable;

/**
 * 系统通用 - 下拉框返回Resp
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023-09-04
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@FieldNameConstants
public class SelectStringResp extends SelectBaseResp implements Serializable {
    private static final long serialVersionUID = -4614814985106664352L;

    public SelectStringResp(String value, String label) {
        this.value = value;
        this.setLabel(label);
    }

    public SelectStringResp(String value, String label, Boolean disabled) {
        this.value = value;
        this.setLabel(label);
        this.setDisabled(disabled);
    }

    /**
     * 下拉选择框的值
     */
    private String value;
}
