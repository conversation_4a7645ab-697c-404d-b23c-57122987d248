package com.ssy.lingxi.common.model.req.api.product;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 物流对象
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/20
 */
@Data
public class LogisticsProducerReq implements Serializable {
    private static final long serialVersionUID = 8721781239633383629L;

    /**
     * 配送方式: 1-物流（默认）, 2-自提, 3-无需配送, 4-物流+自提
     */
    private Integer deliveryType = 1;

    /**
     * 运费方式: 1-卖家承担运费（默认）, 2-买家承担运费
     */
    private Integer carriageType = 1;

    /**
     * 重量: 单位-KG（公斤）
     */
    private BigDecimal weight;
}
