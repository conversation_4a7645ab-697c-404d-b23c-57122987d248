package com.ssy.lingxi.common.model.req.engine;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 删除基础流程
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-27
 */
@Data
public class DeleteBaseProcessReq implements Serializable {

    private static final long serialVersionUID = 2328433497389749938L;

    /**
     * 引擎ID
     */
    @NotNull(message = "引擎ID不能为空")
    private Long engineId;

    /**
     * 流程类型
     */
    @NotNull(message = "流程类型不能为空")
    private Integer type;
}
