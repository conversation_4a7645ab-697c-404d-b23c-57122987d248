package com.ssy.lingxi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 状态类型枚举
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/12/18
 */
@Getter
@AllArgsConstructor
public enum StatusEnum {
    STOP(0, "停用"),
    START(1, "启用"),
    ;

    /**
     * 状态
     */
    private final Integer status;


    /**
     * 描述
     */
    private final String name;

    public static String getNameByCode(Integer status){
        StatusEnum statusEnum = Arrays.stream(StatusEnum.values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
        return statusEnum != null ? statusEnum.getName() : "-";
    }
}
