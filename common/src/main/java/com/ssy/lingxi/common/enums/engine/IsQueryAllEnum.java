package com.ssy.lingxi.common.enums.engine;

/**
 * 是否查询所有
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/26
 */
public enum IsQueryAllEnum {

    /**
     * 不查询所有
     */
    NO(0, "否"),

    /**
     * 查询所有
     */
    YES(1, "是");

    private final Integer code;
    private final String message;

    IsQueryAllEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
