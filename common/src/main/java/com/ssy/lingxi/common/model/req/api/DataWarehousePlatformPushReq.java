package com.ssy.lingxi.common.model.req.api;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 数仓平台数据推送入参
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-17
 */
@Getter
@Setter
public class DataWarehousePlatformPushReq extends ApiBaseReq implements Serializable {
    private static final long serialVersionUID = -2527411825714826168L;

    /**
     * 数据body：json字符串数组
     */
    @NotEmpty(message = "数组不能为空")
    @Valid
    private List<DataWarehousePlatformPushBodyReq> batch;
}
