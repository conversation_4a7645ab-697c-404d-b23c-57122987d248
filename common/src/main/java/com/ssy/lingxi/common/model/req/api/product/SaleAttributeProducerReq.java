package com.ssy.lingxi.common.model.req.api.product;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 销售属性对象
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/20
 */
@Data
public class SaleAttributeProducerReq implements Serializable {
    private static final long serialVersionUID = 4242625413471952502L;

    /**
     * 销售属性编码
     */
    @NotEmpty(message = "销售属性编码不能为空")
    @Length(max = 32,message = "销售属性编码最大字符长度32")
    private String attributeCode;

    /**
     * 销售属性值
     */
    @NotEmpty(message = "销售属性值不能为空")
    @Length(max = 128,message = "销售属性值最大字符长度128")
    private String attributeValue;

}
