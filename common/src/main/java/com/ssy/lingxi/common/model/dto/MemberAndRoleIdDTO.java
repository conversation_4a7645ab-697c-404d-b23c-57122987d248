package com.ssy.lingxi.common.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 会员Id和角色Id的缓存实体
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-09-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberAndRoleIdDTO implements Serializable {
    private static final long serialVersionUID = -5453487724686944198L;

    /**
     * 会员Id
     */
    private Long memberId;

    /**
     * 角色Id
     */
    private Long roleId;
}
