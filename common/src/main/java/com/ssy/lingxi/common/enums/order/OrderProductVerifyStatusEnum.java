package com.ssy.lingxi.common.enums.order;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/30
 */
@Getter
public enum OrderProductVerifyStatusEnum {

    /**
     * 未验证 - 0
     */
    NOT_VERIFIED(0, "未验证"),

    /**
     * 验证中 - 1
     */
    VERIFYING(1, "验证中");

    private final Integer code;
    private final String name;

    OrderProductVerifyStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(Integer code) {
        if (code == null) {
            return "未知";
        }
        for (OrderProductVerifyStatusEnum status : OrderProductVerifyStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return "未知";
    }

}
