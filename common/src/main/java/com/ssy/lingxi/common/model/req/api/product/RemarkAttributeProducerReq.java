package com.ssy.lingxi.common.model.req.api.product;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 描述属性对象
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/20
 */
@Data
public class RemarkAttributeProducerReq implements Serializable {
    private static final long serialVersionUID = 3692027857971808982L;

    /**
     * 描述属性编码
     */
    @NotEmpty(message = "描述属性编码不能为空")
    @Length(max = 32,message = "描述属性编码最大字符长度32")
    private String attributeCode;

    /**
     * 描述属性值
     */
    @NotEmpty(message = "描述属性值不能为空")
    private List<String> attributeValueList;

}
