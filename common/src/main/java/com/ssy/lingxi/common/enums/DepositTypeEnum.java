package com.ssy.lingxi.common.enums;

import lombok.Getter;

/**
 * 定金类型
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/8
 */
@Getter
public enum DepositTypeEnum {

    /**
     * 定金类型 1 - 固定金额 2 - 比例
     */
    FIXED_AMOUNT(1, "固定金额"),
    PROPORTION(2, "比例");

    private final Integer code;
    private final String message;

    DepositTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据code获取message
     *
     * @param code
     * @return
     */
    public static String getByCode(Integer code) {
        for (DepositTypeEnum value : DepositTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getMessage();
            }
        }
        return null;
    }
}
