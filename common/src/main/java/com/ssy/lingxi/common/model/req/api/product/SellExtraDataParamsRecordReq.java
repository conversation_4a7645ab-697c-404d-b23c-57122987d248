package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.enums.DataWarehousePlatformPushTableTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 第三方参数记录
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-07-16
 */
@Getter
@Setter
public class SellExtraDataParamsRecordReq implements Serializable {
    private static final long serialVersionUID = 5260386093082900790L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 销售产品id
     */
    private Integer sell_pro_id;

    /**
     * 参数id
     */
    private Integer params_id;

    /**
     * 类型
     */
    private String component_type;

    /**
     * 名称
     */
    private String name;

    /**
     * 值
     */
    private String value;

    /**
     * 增加三方参数code
     */
    private String code;

    /**
     * 层级值,用于后续筛选
     */
    private String tree_value;

    /**
     * 三方参数物品类型
     */
    private String type;

    /**
     * 三方参数Id
     */
    private String extra_id;

    /**
     * 表名
     * @see DataWarehousePlatformPushTableTypeEnum
     */
    private String tableName;

    /**
     * 重试
     */
    private Integer retryCount;

    public String checkEmpty(){
        if(this.id == null){
            return "id字段不能为空";
        }
        if(this.sell_pro_id == null){
            return "sell_pro_id字段不能为空";
        }
        if(this.params_id == null){
            return "params_id字段不能为空";
        }
        return "success";
    }
}
