package com.ssy.lingxi.common.enums.product;

import lombok.Getter;

/**
 * 商品价格策略适用类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/9
 */
@Getter
public enum UnitPriceStrategyApplyTypeEnum {

    MEMBER(1, "会员"),
    MEMBER_LEVEL(2, "会员等级");

    private final Integer code;
    private final String message;

    UnitPriceStrategyApplyTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

}
