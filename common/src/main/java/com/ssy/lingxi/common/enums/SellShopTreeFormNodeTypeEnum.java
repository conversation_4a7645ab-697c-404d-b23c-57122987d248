package com.ssy.lingxi.common.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 数仓节点类型枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-22
 */
@Getter
public enum SellShopTreeFormNodeTypeEnum {

    /**
     * 系列管理
     */
    SERIES("系列管理", "SERIES"),

    /**
     * 店内分类管理
     */
    IN_STORE_CLASSIFY("店内分类管理", "INNER_CATEGORY"),

    /**
     * 营销品类管理
     */
    MARKETING_CATEGORY("营销品类管理", "MARKETING_CATEGORY"),

    /**
     * 商品品类
     */
    CUSTOMER_CATEGORY("商品品类", "CATEGORY");

    /**
     * 商城端属性，品类
     */
    private final String name;

    /**
     * 数仓节点类型
     */
    private final String nodeType;

    SellShopTreeFormNodeTypeEnum(String name, String nodeType) {
        this.name = name;
        this.nodeType = nodeType;
    }

    public static SellShopTreeFormNodeTypeEnum getEnumByType(String nodeType){
        return Arrays.stream(SellShopTreeFormNodeTypeEnum.values()).filter(item -> item.getNodeType().equals(nodeType)).findFirst().orElse(null);
    }
}
