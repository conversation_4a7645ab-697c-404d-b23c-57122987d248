package com.ssy.lingxi.common.constant.mq;

import java.io.Serializable;

/**
 * 消息队列常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class OrderMqConstant implements Serializable {
    private static final long serialVersionUID = 6893294675480286750L;

    /**
     * 订单服务公共队列
     */
    public static final String ORDER_COMMON_QUEUE = "order_common_queue";
    public static final String ORDER_COMMON_EXCHANGE = "order_common_exchange";
    public static final String ORDER_COMMON_ROUTING_KEY = "order_common_routingKey";

    /**
     * 订单拼团结果通知队列
     */
    public static final String ORDER_GROUP_QUERY = "order_group_queue";
    public static final String ORDER_GROUP_EXCHANGE = "order_group_exchange";
    public static final String ORDER_GROUP_ROUTINGKEY = "order_group_key";

    /**
     * 订单支付结果通知队列
     */
    public static final String ORDER_PAY_CALLBACK_QUERY = "order_pay_callback_queue";
    public static final String ORDER_PAY_CALLBACK_EXCHANGE = "order_pay_callback_exchange";
    public static final String ORDER_PAY_CALLBACK_ROUTINGKEY = "order_pay_callback_key";

    /**
     * 订单服务内部延迟队列定义
     */
    public static final String ORDER_SERVICE_DELAY_QUERY = "order_service_delay_queue";
    public static final String ORDER_SERVICE_DELAY_EXCHANGE = "order_service_delay_exchange";
    public static final String ORDER_SERVICE_DELAY_ROUTING_KEY = "order_service_delay_key";

    /**
     * 物料价格信息变更MQ任务
     * 采购询价合同、采购竞价合同、采购招标合同、请购单采购 确认收货时需要同步更新物料价格库
     */
    public static final String ORDER_MATERIEL_PRICE_CHANGE_QUEUE = "order_materiel_price_change_queue";
    public static final String ORDER_MATERIEL_PRICE_CHANGE_EXCHANGE = "order_materiel_price_change_exchange";
    public static final String ORDER_MATERIEL_PRICE_CHANGE_ROUTING_KEY = "order_materiel_price_change_routing_key";
}
