package com.ssy.lingxi.common.constant.mq;

import java.io.Serializable;

/**
 * 消息队列常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class PurchaseMqConstant implements Serializable {
    private static final long serialVersionUID = -4893252328043001351L;

    /**
     * 确认报价标题
     */
    public static final String CONFIRM_DEMAND_QUOTATION = "确认报价";

    /**
     * 招投标--报名时间截止，外部状态改成待审核报名--延迟队列
     */
    public static final String PURCHASE_SUBMIT_TENDER_DELAY_QUEUE = "purchase_submit_tender_delay_queue";
    public static final String PURCHASE_SUBMIT_TENDER_DELAY_EXCHANGE = "purchase_submit_tender_delay_exchange";
    public static final String PURCHASE_SUBMIT_TENDER_ROUTINGKEY = "purchase_submit_tender_routingKey";

    /**
     * 招投标--提交资格预审时间开始，外部状态改成待提交资格预审--延迟队列
     */
    public static final String PURCHASE_NOT_SUBMIT_QUALIFICATIONS_CHECK_DELAY_QUEUE = "purchase_not_submit_qualifications_check_delay_queue";
    public static final String PURCHASE_NOT_SUBMIT_QUALIFICATIONS_CHECK_DELAY_EXCHANGE = "purchase_not_submit_qualifications_check_delay_exchange";
    public static final String PURCHASE_NOT_SUBMIT_QUALIFICATIONS_CHECK_ROUTINGKEY = "purchase_not_submit_qualifications_check_routingKey";

    /**
     * 招投标--提交资格预审时间截止，外部状态改成待资格预审--延迟队列
     */
    public static final String PURCHASE_NOT_QUALIFICATIONS_CHECK_DELAY_QUEUE = "purchase_not_qualifications_check_delay_queue";
    public static final String PURCHASE_NOT_QUALIFICATIONS_CHECK_DELAY_EXCHANGE = "purchase_not_qualifications_check_delay_exchange";
    public static final String PURCHASE_NOT_QUALIFICATIONS_CHECK_ROUTINGKEY = "purchase_not_qualifications_check_routingKey";

    /**
     * 招投标--投标时间开始，外部状态改成待投标--延迟队列
     */
    public static final String PURCHASE_NOT_SUBMIT_TENDER_DELAY_QUEUE = "purchase_not_submit_tender_delay_queue";
    public static final String PURCHASE_NOT_SUBMIT_TENDER_DELAY_EXCHANGE = "purchase_not_submit_tender_delay_exchange";
    public static final String PURCHASE_NOT_SUBMIT_TENDER_ROUTINGKEY = "purchase_not_submit_tender_routingKey";

    /**
     * 招投标--投标时间截止，外部状态改成待开标--延迟队列
     */
    public static final String PURCHASE_NOT_OPEN_TENDER_DELAY_QUEUE = "purchase_not_open_tender_delay_queue";
    public static final String PURCHASE_NOT_OPEN_TENDER_DELAY_EXCHANGE = "purchase_not_open_tender_delay_exchange";
    public static final String PURCHASE_NOT_OPEN_TENDER_ROUTINGKEY = "purchase_not_open_tender_routingKey";

    /**
     * 招投标--开标时间到，外部状态改成待评标--延迟队列
     */
    public static final String PURCHASE_OPEN_TENDER_DELAY_QUEUE = "purchase_open_tender_delay_queue";
    public static final String PURCHASE_OPEN_TENDER_DELAY_EXCHANGE = "purchase_open_tender_delay_exchange";
    public static final String PURCHASE_OPEN_TENDER_ROUTINGKEY = "purchase_open_tender_routingKey";

    /**
     * 招投标--评标时间开始，内部状态改成待提交评标报告--延迟队列
     */
    public static final String PURCHASE_EVALUATION_TENDER_DELAY_QUEUE = "purchase_evaluation_tender_delay_queue";
    public static final String PURCHASE_EVALUATION_TENDER_DELAY_EXCHANGE = "purchase_evaluation_tender_delay_exchange";
    public static final String PURCHASE_EVALUATION_TENDER_ROUTINGKEY = "purchase_evaluation_tender_routingKey";

    // ******************************************************采购竞价**********************************************************************
    /**
     * 采购竞价 自动处理到期报名的单据
     */
    public static final String PURCHASE_BIDDING_DELAY_QUEUE = "purchase_bidding_delay_queue";
    public static final String PURCHASE_BIDDING_DELAY_EXCHANGE = "purchase_bidding_delay_exchange";
    public static final String PURCHASE_BIDDING_DELAY_KEY = "purchase_bidding_delay_key";

    /**
     * 采购竞价 自动处理竞价截止时间到期的单据
     */
    public static final String PURCHASE_END_BIDDING_DELAY_QUEUE = "purchase_end_bidding_delay_queue";
    public static final String PURCHASE_END_BIDDING_DELAY_EXCHANGE = "purchase_end_bidding_delay_exchange";
    public static final String PURCHASE_END_BIDDING_DELAY_KEY = "purchase_end_bidding_delay_key";

    // ******************************************************采购询价**********************************************************************
    /**
     * 采购询价 自动处理到期报名的单据
     */
    public static final String PURCHASE_INQUIRY_DELAY_QUEUE = "purchase_inquiry_delay_queue";
    public static final String PURCHASE_INQUIRY_DELAY_EXCHANGE = "purchase_inquiry_delay_exchange";
    public static final String PURCHASE_INQUIRY_DELAY_KEY = "purchase_inquiry_delay_key";
}
