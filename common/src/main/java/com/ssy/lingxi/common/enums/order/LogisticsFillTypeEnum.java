package com.ssy.lingxi.common.enums.order;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/4
 */
@Getter
public enum LogisticsFillTypeEnum {

    /**
     * 自动获取
     */
    AUTO(1, "自动获取"),

    /**
     * 手工填写
     */
    MANUAL(2, "手工填写");

    private final Integer code;
    private final String desc;

    LogisticsFillTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取desc
     *
     * @param code
     * @return
     */
    public static String getDescByCode(Integer code) {
        for (LogisticsFillTypeEnum type : LogisticsFillTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.getDesc();
            }
        }
        return null;
    }

    public static LogisticsFillTypeEnum getByCode(Integer code) {
        for (LogisticsFillTypeEnum type : LogisticsFillTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
