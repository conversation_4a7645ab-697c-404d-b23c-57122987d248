package com.ssy.lingxi.common.enums;

import lombok.Getter;

/**
 * 数仓参数管理组件类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-27
 */
@Getter
public enum SellShopParamsComponentTypeEnum {

    SINGLE_CHOICE("singleChoice", "单选"),
    MULTI_CHOICE("multiChoice", "多选"),
    TEXT("text", "文本"),
    DATE("date", "日期"),
    TREE_SELECT("treeSelect", "树形级联"),
    NUMBER("number", "数字");

    private final String code;
    private final String name;

    SellShopParamsComponentTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
