package com.ssy.lingxi.common.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 请求对象，只需要传对象id
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonIdListReq {
    /**
     * id值数组
     */
    @NotEmpty(message = "{CommonIdListReq.id.NotEmpty}")
    private List<Long> idList;
}
