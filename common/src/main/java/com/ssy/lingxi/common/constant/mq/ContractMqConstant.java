package com.ssy.lingxi.common.constant.mq;

import java.io.Serializable;

/**
 * 消息队列常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class ContractMqConstant implements Serializable {
    private static final long serialVersionUID = 7048101024411163255L;

    /**
     * 订单-新增/取消合同订单（通知调整合同剩余金额，仅限下单模式：询价、竞价、招标合同下单）
     */
    public static final String CONTRACT_ORDER_CHANGE_QUEUE = "contract_order_change_queue";
    public static final String CONTRACT_ORDER_CHANGE_EXCHANGE = "contract_order_change_exchange";
    public static final String CONTRACT_ORDER_CHANGE_ROUTINGKEY = "contract_order_change_routingKey";

    /**
     * 物料价格信息变更MQ任务
     * 合同订单有变更、到期、停用、作废都需要同步更新物料价格库
     */
    public static final String MATERIEL_PRICE_CHANGE_QUEUE = "materiel_price_change_queue";
    public static final String MATERIEL_PRICE_CHANGE_EXCHANGE = "materiel_price_change_exchange";
    public static final String MATERIEL_PRICE_CHANGE_ROUTING_KEY = "materiel_price_change_routing_key";
}
