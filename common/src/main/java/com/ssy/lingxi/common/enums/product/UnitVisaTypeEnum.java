package com.ssy.lingxi.common.enums.product;

import lombok.Getter;

/**
 * 挂签计价单位
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/22
 */
@Getter
public enum UnitVisaTypeEnum {

    /**
     * 克
     */
    GRAM(1, "克"),

    /**
     * 件
     */
    PIECE(2, "件");

    private final Integer code;
    private final String message;

    UnitVisaTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static UnitVisaTypeEnum getByCode(Integer code) {
        for (UnitVisaTypeEnum value : UnitVisaTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据凑得，查询message
     */
    public static String getMessageByCode(Integer code) {
        UnitVisaTypeEnum unitVisaTypeEnum = getByCode(code);
        if (unitVisaTypeEnum != null) {
            return unitVisaTypeEnum.getMessage();
        }
        return null;
    }
}
