package com.ssy.lingxi.common.enums.product;

import lombok.Getter;

/**
 * 挂签类型
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/22
 */
@Getter
public enum VisaTypeEnum {

    /**
     * 挂签
     */
    VISA(1, "挂签"),

    /**
     * 证书
     */
    CERTIFICATE(2, "证书");

    private final Integer code;
    private final String message;

    VisaTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static VisaTypeEnum getByCode(Integer code) {
        for (VisaTypeEnum value : VisaTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
