package com.ssy.lingxi.common.enums.logistics;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 物流公司枚举
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum LogisticsCompanyEnum {

    /**
     * 顺丰速运
     */
    SF("SF", "顺丰速运"),
    /**
     * 圆通速递
     */
    YTO("YTO", "圆通速递"),
    /**
     * 中通快递
     */
    ZTO("ZTO", "中通快递"),
    /**
     * 韵达速递
     */
    YD("YD", "韵达速递"),
    /**
     * 韵达快运
     */
    YDKY("YDKY", "韵达快运"),
    /**
     * 申通快递
     */
    STO("STO", "申通快递"),
    /**
     * 京东快递
     */
    JD("JD", "京东快递"),
    /**
     * 包裹/平邮/挂号信
     */
    YZGN("YZGN", "包裹/平邮/挂号信"),
    /**
     * 德邦快递
     */
    DBL("DBL", "德邦快递"),
    /**
     * 德邦快运/德邦物流
     */
    DBLKY("DBLKY", "德邦快运/德邦物流"),
    /**
     * 优速快递
     */
    UC("UC", "优速快递"),
    /**
     * 极兔速递
     */
    JTSD("JTSD", "极兔速递"),
    /**
     * 跨越速运
     */
    KYSY("KYSY", "跨越速运"),
    /**
     * 顺心捷达
     */
    SX("SX", "顺心捷达"),
    /**
     * 安能物流
     */
    ANEKY("ANEKY", "安能物流"),
    /**
     * 百世快运
     */
    BTWL("BTWL", "百世快运"),
    /**
     * 丰网速运
     */
    FWX("FWX", "丰网速运"),
    /**
     * 壹米滴答
     */
    YMDD("YMDD", "壹米滴答"),
    /**
     * 加运美
     */
    JYM("JYM", "加运美"),
    /**
     * 中通快运
     */
    ZTOKY("ZTOKY", "中通快运"),
    /**
     * 京广速递
     */
    JGSD("JGSD", "京广速递"),
    /**
     * 宅急送
     */
    ZJS("ZJS", "宅急送"),
    /**
     * 日日顺物流
     */
    RRS("RRS", "日日顺物流"),
    /**
     * 苏宁物流
     */
    SNWL("SNWL", "苏宁物流"),
    /**
     * 丹鸟物流
     */
    DNWL("DNWL", "丹鸟物流"),
    /**
     * 如风达
     */
    RFD("RFD", "如风达"),
    /**
     * 远成快运
     */
    YCSY("YCSY", "远成快运"),
    /**
     * 九曳供应链
     */
    JIUYE("JIUYE", "九曳供应链"),
    /**
     * 天地华宇
     */
    HOAU("HOAU", "天地华宇"),
    /**
     * 中铁物流
     */
    FT("FT", "中铁物流"),
    /**
     * 飞豹快递
     */
    FBKD("FBKD", "飞豹快递"),
    /**
     * 全日通快递
     */
    QRT("QRT", "全日通快递"),
    /**
     * 新邦物流
     */
    XBWL("XBWL", "新邦物流"),
    /**
     * 联昊通速递
     */
    LHT("LHT", "联昊通速递"),
    /**
     * 盛辉物流
     */
    SHWL("SHWL", "盛辉物流"),
    /**
     * 黄马褂
     */
    HMGEXPRESS("HMGEXPRESS", "黄马褂"),
    /**
     * 南京晟邦物流
     */
    NJSBWL("NJSBWL", "南京晟邦物流"),
    /**
     * 飞远配送
     */
    FYPS("FYPS", "飞远配送"),
    /**
     * 黑猫跨境物流
     */
    TCATCN("TCATCN", "黑猫跨境物流"),
    /**
     * COE东方快递
     */
    COE("COE", "COE东方快递"),
    /**
     * 大田物流
     */
    DTWL("DTWL", "大田物流"),
    /**
     * 亚风速递
     */
    YFSD("YFSD", "亚风速递"),
    /**
     * 万家物流
     */
    WJWL("WJWL", "万家物流"),
    /**
     * 递四方速递
     */
    D4PX("D4PX", "递四方速递"),
    /**
     * UPS
     */
    UPS("UPS", "UPS"),
    /**
     * 穗佳物流
     */
    SJWL("SJWL", "穗佳物流"),
    /**
     * 速腾快递
     */
    STWL("STWL", "速腾快递"),
    /**
     * 安得物流
     */
    ANNTO("ANNTO", "安得物流"),
    /**
     * D速物流
     */
    DSWL("DSWL", "D速物流"),
    /**
     * 郑州建华
     */
    ZZJH("ZZJH", "郑州建华"),
    /**
     * 中通国际
     */
    ZTOGLOBAL("ZTOGLOBAL", "中通国际"),
    /**
     * 河北建华
     */
    HBJH("HBJH", "河北建华"),
    /**
     * 畅顺通达
     */
    CSTD("CSTD", "畅顺通达"),
    /**
     * 云达速递
     */
    YUNDAEXUS("YUNDAEXUS", "云达速递"),
    /**
     * 泛捷快递
     */
    PANEX("PANEX", "泛捷快递"),
    /**
     * 贝海国际速递
     */
    XLOBO("XLOBO", "贝海国际速递"),
    /**
     * 卓志速运
     */
    ESDEX("ESDEX", "卓志速运"),
    /**
     * 圆通国际
     */
    YTOGJ("YTOGJ", "圆通国际"),
    /**
     * 速通物流
     */
    ST("ST", "速通物流"),
    /**
     * 优邦国际速运
     */
    UBONEX("UBONEX", "优邦国际速运"),
    /**
     * wndirect快递
     */
    WNDIRECT("WNDIRECT", "wndirect快递"),
    /**
     * 百世跨境
     */
    BAISHIGUOJI("BAISHIGUOJI", "百世跨境"),
    /**
     * DHL
     */
    DHL("DHL", "DHL"),
    /**
     * 安世通快递
     */
    ASTEXPRESS("ASTEXPRESS", "安世通快递"),
    /**
     * 富腾达
     */
    FTD("FTD", "富腾达"),
    /**
     * 程光物流
     */
    CG("CG", "程光物流"),
    /**
     * 门对门快递
     */
    MDM("MDM", "门对门快递"),
    /**
     * 易达通快递
     */
    YDT("YDT", "易达通快递"),
    /**
     * 安鲜达
     */
    AXD("AXD", "安鲜达"),
    /**
     * 宇鑫物流
     */
    YXWL("YXWL", "宇鑫物流"),

    ;

    private final String code;
    private final String name;


}
