package com.ssy.lingxi.common.constant.mq;

import java.io.Serializable;

/**
 * 消息队列常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class MemberMqConstant implements Serializable {
    private static final long serialVersionUID = 3965311964421238207L;

    /**
     * 会员邀请--邀请码失效--延迟队列
     */
    public static final String INVITATION_CODE_INVALID_DELAY_QUEUE = "invitation_code_invalid_delay_queue";
    public static final String INVITATION_CODE_INVALID_DELAY_EXCHANGE = "invitation_code_invalid_delay_exchange";
    public static final String INVITATION_CODE_INVALID_DELAY_ROUTINGKEY = "invitation_code_invalid_delay_routingKey";

    /**
     * 会员服务外部消息公共队列
     */
    public static final String MEMBER_OUTER_COMMON_QUEUE = "member_outer_common_queue";
    public static final String MEMBER_OUTER_COMMON_EXCHANGE = "member_outer_common_exchange";
    public static final String MEMBER_OUTER_COMMON_ROUTING_KEY = "member_outer_common_routingKey";

    /**
     * 会员注册成功分佣处理队列
     */
    public static final String MEMBER_REGISTRATION_COMMISSION_QUEUE = "member_registration_commission_queue";
    public static final String MEMBER_REGISTRATION_COMMISSION_EXCHANGE = "member_registration_commission_exchange";
    public static final String MEMBER_REGISTRATION_COMMISSION_ROUTING_KEY = "member_registration_commission_routingKey";

    /**
     * 会员企业认证成功分佣处理队列
     */
    public static final String MEMBER_CERTIFICATION_COMMISSION_QUEUE = "member_certification_commission_queue";
    public static final String MEMBER_CERTIFICATION_COMMISSION_EXCHANGE = "member_certification_commission_exchange";
    public static final String MEMBER_CERTIFICATION_COMMISSION_ROUTING_KEY = "member_certification_commission_routingKey";

    /**
     * 订单变更分佣处理队列
     */
    public static final String ORDER_CHANGE_COMMISSION_QUEUE = "order_change_commission_queue";
    public static final String ORDER_CHANGE_COMMISSION_EXCHANGE = "order_change_commission_exchange";
    public static final String ORDER_CHANGE_COMMISSION_ROUTING_KEY = "order_change_commission_routingKey";

}
