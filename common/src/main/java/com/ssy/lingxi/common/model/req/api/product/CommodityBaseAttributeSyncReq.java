package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 商品基础属性同步
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-14
 */
@Getter
@Setter
public class CommodityBaseAttributeSyncReq extends ApiBaseReq {
    private static final long serialVersionUID = 3980281648777864345L;

    /**
     * 基础属性列表
     */
    private List<CommodityBaseAttributeReq> attributeList;
}
