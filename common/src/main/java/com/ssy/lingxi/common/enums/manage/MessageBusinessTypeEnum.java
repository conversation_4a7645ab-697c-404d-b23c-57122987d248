package com.ssy.lingxi.common.enums.manage;

/**
 * 信息业务类型枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/3/15
 */
public enum MessageBusinessTypeEnum {

    /**
     * product - 商品
     */
    PRODUCT("product", "商品"),

    /**
     * member - 会员
     */
    MEMBER("member", "会员"),

    /**
     * appraisal - 会员考评
     */
    APPRAISAL("appraisal", "会员考评"),

    /**
     * rectify - 会员整改
     */
    RECTIFY("rectify", "会员整改"),

    /**
     * inviteMember - 会员邀请
     */
    INVITE_MEMBER("inviteMember", "会员邀请"),

    /**
     * customer - 客户
     */
    CUSTOMER("customer", "客户"),

    /**
     * vendor - 供应商
     */
    VENDOR("vendor", "供应商"),

    /**
     * contract - 合同
     */
    CONTRACT("contract", "合同"),

    /**
     * requireQuote - 需求报价
     */
    REQUIRE_QUOTE("requireQuote", "需求报价"),

    /**
     * inquiryQuote - 询价报价
     */
    INQUIRY_QUOTE("inquiryQuote", "询价报价"),

    /**
     * inquiry - 询价
     */
    INQUIRY("inquiry", "询价"),

    /**
     * quote - 报价
     */
    QUOTE("quote", "报价"),

    /**
     * order - 订单
     */
    ORDER("order", "订单"),

    /**
     * capital - 资金
     */
    CAPITAL("capital", "资金"),

    /**
     * integral - 积分
     */
    INTEGRAL("integral", "积分"),

    /**
     * logistics - 物流
     */
    LOGISTICS("logistics", "物流"),

    /**
     * process - 加工生产
     */
    PROCESS("process", "加工生产"),

    /**
     * after_sale - 售后
     */
    AFTER_SALE("afterSale", "售后"),

    /**
     * marketing - 营销
     */
    MARKETING("marketing", "营销"),

    /**
     * settlement - 结算
     */
    SETTLEMENT("settlement", "结算"),

    /**
     * requisitions - 请购单
     */
    REQUISITIONS("requisitions","请购单"),

    /**
     * quality - 质量中心
     */
    QUALITY("quality","质量中心"),

    /**
     * inviteTender - 招标
     */
    INVITE_TENDER("inviteTender","招标"),

    /**
     * requirePlan - 需求计划
     */
    REQUIRE_PLAN("requirePlan","需求计划"),

    /**
     * purchasePlan - 采购计划
     */
    PURCHASE_PLAN("purchasePlan","采购计划"),

    /**
     * purchaseBidding - 采购竞价
     */
    PURCHASE_BIDDING("purchaseBidding","采购竞价"),

    /**
     * contractSign - 电子签章
     */
    CONTRACT_SIGN("contractSign","电子签章"),

    /**
     * custom - 自定义消息
     */
    CUSTOM("custom","自定义消息"),

    ;

    private final String code;
    private final String message;

    MessageBusinessTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

}
