package com.ssy.lingxi.common.enums.marketing;

import java.util.Arrays;

/**
 * 活动类型对应装修活动类型
 * 活动类型: 1-特价促销 2-直降促销 3-折扣促销 4-满量促销 5-满额促销
 * 6-赠送促销 7-多件促销 8-组合促销 9-拼团 10-抽奖
 * 11-砍价 12-秒杀 13-换购 14-预售 15-套餐 16-试用
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/24
 */
public enum ActivityTypeToAdornEnum {

    SPECIAL_OFFER(1, "特价促销", "4,5,6,13","specialOffer"),
    PLUMMET(2, "直降促销", "4,5,6,13","plummet"),
    DISCOUNT(3, "折扣促销 ", "4,5,6,13","discount"),
    FULL_QUANTITY(4, "满量促销", "1,2,3,6,13","fullQuantitySub,fullQuantityDiscount"),
    FULL_MONEY(5, "满额促销", "1,2,3,6,13","fullMoneySub,fullMoneyDiscount"),
    GIVE(6, "赠送促销", "1,2,3,4,5,7,8,13","giveProduct,giveCoupon"),
    MORE_PIECE(7, "多件促销", "6,13","morePiece"),
    COMBINATION(8, "组合促销", "6,13","combination"),
    GROUP_PURCHASE(9, "拼团", "","groupPurchase"),
    LOTTERY(10, "抽奖", "",""),
    BARGAIN(11, "砍价", "","bargain"),
    SEC_KILL(12, "秒杀", "","secKill"),
    SWAP(13, "换购", "1,2,3,4,5,6,7,8","fullSwap,buySwap"),
    PRE_SALE(14, "预售", "","preSale"),
    SET_MEAL(15, "套餐", "","setMeal"),
    ATTEMPT(16, "试用", "","attempt"),
    ;

    // 活动类型
    private final Integer code;

    // 活动类型名称
    private final String message;

    // 允许叠加活动类型
    private final String superpositionItem;

    //对应活动装修页字符串
    public final String activityStr;

    ActivityTypeToAdornEnum(Integer code, String message, String superpositionItem, String activityStr) {
        this.code = code;
        this.message = message;
        this.superpositionItem = superpositionItem;
        this.activityStr=activityStr;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getSuperpositionItem() {
        return superpositionItem;
    }

    public String getActivityStr() {
        return activityStr;
    }

    /**
     * 根据code获取对应message
     * @param code 活动类型
     */
    public static String getMessage(Integer code) {
        return Arrays.stream(ActivityTypeToAdornEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().map(ActivityTypeToAdornEnum::getMessage).orElse("");
    }


    /**
     * 根据code获取对应activityStr
     * @param code 活动类型
     */
    public static String getActivityStr(Integer code) {
        return Arrays.stream(ActivityTypeToAdornEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().map(ActivityTypeToAdornEnum::getActivityStr).orElse("");
    }
}
