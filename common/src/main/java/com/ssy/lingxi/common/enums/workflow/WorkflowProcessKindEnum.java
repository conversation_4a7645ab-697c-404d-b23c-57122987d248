package com.ssy.lingxi.common.enums.workflow;

import java.util.Arrays;

/**
 * 工作流流程类型枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-24
 */
public enum WorkflowProcessKindEnum {
    /**
     * 内部流程 - 1
     */
    INTERNAL(1, "内部流程"),

    /**
     * 外部流程 - 2
     */
    EXTERNAL(2, "外部流程"),

    /**
     * 内外结合的复杂流程 - 3
     */
    COMPLEX(3, "复杂流程"),

    /**
     * 未知 - 99
     */
    UNKNOWN(99, "未知");

    WorkflowProcessKindEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static WorkflowProcessKindEnum parse(Integer code) {
        return Arrays.stream(WorkflowProcessKindEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(UNKNOWN);
    }
}
