package com.ssy.lingxi.common.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商品仓位库存数量和仓库地址信息
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/7/4
 */
@Data
public class InventoryByProductDTO implements Serializable {
    private static final long serialVersionUID = 8170168873040941594L;

    /**
     * 仓位id
     */
    private Long positionId;

    /**
     * 仓库id
     */
    private Long warehouseId;

    /**
     * 仓库name
     */
    private String warehouseName;

    /**
     * 仓位仓库地址(省+市 广东省广州市)
     */
    private String warehouseAddress;

    /**
     * 库存数量
     */
    private BigDecimal stockCount = BigDecimal.ZERO;
}
