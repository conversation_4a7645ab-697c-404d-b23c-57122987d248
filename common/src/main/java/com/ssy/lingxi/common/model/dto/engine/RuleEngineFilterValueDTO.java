package com.ssy.lingxi.common.model.dto.engine;

import lombok.Data;

import java.io.Serializable;

/**
 * 规则引擎过滤值
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-13 10:20
 **/
@Data
public class RuleEngineFilterValueDTO implements Serializable {

    private static final long serialVersionUID = -4419339212319697080L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 流程ID
     */
    private String processId;

    /**
     * 流程步骤
     */
    private Integer processStep;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员角色id
     */
    private Long memberRoleId;

    /**
     * 关联数据
     */
    private Long entityId;
}
