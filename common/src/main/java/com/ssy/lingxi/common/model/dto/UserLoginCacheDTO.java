package com.ssy.lingxi.common.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 用户登录成功后，保存在缓存中的信息
 * 平台后台和能力中心等共用该登陆缓存对象，但有些数据平台后台没有，二开时候要稍微注意
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-06-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserLoginCacheDTO implements Serializable {
    private static final long serialVersionUID = 5780646525950777184L;

    //************************************** 会员相关信息 **************************************
    /**
     * 关系表id
     */
    private Long relId;

    /**
     * 会员Id
     */
    private Long memberId;

    /**
     * 会员名称（会员公司名称）
     */
    private String memberName;

    /**
     * 会员角色Id
     */
    private Long memberRoleId;

    /**
     * 会员角色名称
     */
    private String memberRoleName;

    /**
     * 会员角色类型枚举(RoleTypeEnum)
     */
    private Integer memberRoleType;

    /**
     * 会员类型枚举(MemberTypeEnum)
     */
    private Integer memberType;

    /**
     * 角色标签(RoleTagEnum)
     */
    private Integer roleTag;

    /**
     * 平台会员等级(不是当前登录用户在不同供应商的会员等级)
     */
    private Integer level;

    /**
     * 会员等级类型枚举(MemberLevelTypeEnum)
     */
    private Integer memberLevelType;

    /**
     * 登录来源
     * com.ssy.lingxi.component.base.enums.SystemSourceEnum
     */
    private Integer loginSource;

    /**
     * 至少一次平台审核通过
     */
    private Integer verified;

    //************************************** 用户相关信息 **************************************
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户账号
     */
    private String account;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号码前缀，例如“+86”
     */
    private String telCode;

    /**
     * 用户手机号码
     */
    private String userPhone;

    /**
     * 用户手机邮箱
     */
    private String userEmail;

    /**
     * 用户职位
     */
    private String jobTitle;

    /**
     * 用户所属组织机构名称
     */
    private String orgName;

    /**
     * 用户所属的角色Id（会员自定义角色）列表
     */
    private List<Long> userRoleIds;

    /**
     * 用户所属的角色名称（会员自定义角色，如果有多个以逗号分隔）
     */
    private String userRoleName;

    /**
     * 用户类型(UserTypeEnum)
     */
    private Integer userType;

    /**
     * 用户头像
     */
    private String logo;

    //************************************** 通用信息 **************************************

    /**
     * new店铺id
     */
    private Long branchId;

}
