package com.ssy.lingxi.common.constant.mq;

import java.io.Serializable;

/**
 * 消息队列常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class ProductMqConstant implements Serializable {
    private static final long serialVersionUID = 243361694675154419L;

    /**
     * 刷新ES索引-延迟队列-延迟时间
     */
    public static final Long ES_INDEX_REFRESH_DELAY_TIME = 60 * 1000L;

    /**
     * 刷新ES索引-延迟队列
     */
    public static final String ES_INDEX_REFRESH_DELAY_QUEUE = "es_index_refresh_delay_queue";
    public static final String ES_INDEX_REFRESH_DELAY_EXCHANGE = "es_index_refresh_delay_exchange";
    public static final String ES_INDEX_REFRESH_DELAY_ROUTING_KEY = "es_index_refresh_delay_routingKey";

    /**
     * 商品已售数量--(订单确认收货时通知)
     */
    public static final String ORDER_COMMODITY_SOLD_QUEUE = "order_commodity_sold_queue";
    public static final String ORDER_COMMODITY_SOLD_EXCHANGE = "order_commodity_sold_exchange";
    public static final String ORDER_COMMODITY_SOLD_ROUTING_KEY = "order_commodity_sold_routingKey";
}
