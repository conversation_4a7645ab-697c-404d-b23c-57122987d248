package com.ssy.lingxi.common.model.dto.engine;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 决策明细
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-04
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolicyDetailDTO implements Serializable {

    private static final long serialVersionUID = -2092889337223054378L;

    /**
     * 流程ID
     */
    private String processId;

    /**
     * 流程步骤
     */
    private Integer processStep;
}
