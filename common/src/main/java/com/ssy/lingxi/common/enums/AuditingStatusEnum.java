package com.ssy.lingxi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 状态类型枚举
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/12/18
 */
@Getter
@AllArgsConstructor
public enum AuditingStatusEnum {
    DEFAULT(-1, "停用"),
    NOT_GO(0, "不通过"),
    PASS(1, "通过"),
    ;

    /**
     * 状态
     */
    private final Integer state;


    /**
     * 描述
     */
    private final String name;
}
