package com.ssy.lingxi.common.constant.mq.api;

import java.io.Serializable;

/**
 * 消息队列常量类(调用外部系统时用到)
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class OuterApiMqConstant implements Serializable {
    private static final long serialVersionUID = 1550285993754492277L;

    /**
     * 销售订单
     */
    public static final String API_SYNC_VENDOR_ORDER_QUEUE = "api_sync_vendor_order_queue";
    public static final String API_SYNC_VENDOR_ORDER_EXCHANGE = "api_sync_vendor_order_exchange";
    public static final String API_SYNC_VENDOR_ORDER_ROUTING_KEY = "api_sync_vendor_order_routingKey";
}
