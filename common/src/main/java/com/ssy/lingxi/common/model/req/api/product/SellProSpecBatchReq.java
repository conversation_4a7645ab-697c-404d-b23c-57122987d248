package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 数仓商品sku批量推送
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-27
 */
@Getter
@Setter
public class SellProSpecBatchReq implements Serializable {
    private static final long serialVersionUID = 1162355743388798980L;

    /**
     * 商品sku列表
     */
    @NotEmpty(message = "batch对象不能为空")
    private List<String> batch;

    /**
     * 商品sku总数
     */
    private Integer size;
}
