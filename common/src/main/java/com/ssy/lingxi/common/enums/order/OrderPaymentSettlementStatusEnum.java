package com.ssy.lingxi.common.enums.order;

import java.util.Arrays;

/**
 * 订单支付记录结算状态
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-20
 */
public enum OrderPaymentSettlementStatusEnum {
    /**
     * 无需结算 - 0
     */
    NONE(0, "无需结算"),

    /**
     * 结算中 - 1
     */
    PROCEEDING(1, "结算中"),

    /**
     * 结算成功 - 2
     */
    SUCCESS(2, "结算成功"),

    /**
     * 结算失败
     */
    FAILED(3, "结算失败");

    OrderPaymentSettlementStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(Integer code) {
        return code == null ? "未知" : Arrays.stream(OrderPaymentSettlementStatusEnum.values()).filter(e -> e.getCode().equals(code)).map(OrderPaymentSettlementStatusEnum::getName).findFirst().orElse("未知");
    }
}
