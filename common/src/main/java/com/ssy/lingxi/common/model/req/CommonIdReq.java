package com.ssy.lingxi.common.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * 请求对象，只需要传对象id
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonIdReq {
    /**
     * id值
     */
    @NotNull(message = "{CommonIdReq.id.NotNull}")
    @Positive(message = "{CommonIdReq.id.Positive}")
    private Long id;
}
