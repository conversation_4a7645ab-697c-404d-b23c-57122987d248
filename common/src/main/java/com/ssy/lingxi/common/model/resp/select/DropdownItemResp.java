package com.ssy.lingxi.common.model.resp.select;

import lombok.Data;

import java.io.Serializable;

/**
 * 下拉框内容返回VO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-07-14
 */
@Data
public class DropdownItemResp implements Serializable {
    private static final long serialVersionUID = -4614814985106664352L;

    public DropdownItemResp() {
    }

    public DropdownItemResp(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public DropdownItemResp(Integer id, String text, Integer phoneLength) {
        this.id = id;
        this.text = text;
        this.phoneLength = phoneLength;
    }

    /**
     * 下拉选择框的id
     */
    private Integer id;

    /**
     * 下拉选择框的文本内容
     */
    private String text;

    /**
     * 手机号码位数
     */
    private Integer phoneLength;
}
