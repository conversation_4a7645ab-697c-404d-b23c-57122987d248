package com.ssy.lingxi.common.enums.product;

import lombok.Getter;

/**
 * 接口平台库存开单状态
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-30
 */
@Getter
public enum FreightSpaceSaleStatusEnum {
    NOT_SOLD_OUT("未开单", "闲置状态"),
    SOLD_OUT("已开单", "被单据占用状态"),
    LOCK("锁定中", "被单据占用状态");

    private final String status;
    private final String describe;

    FreightSpaceSaleStatusEnum(String status, String describe) {
        this.status = status;
        this.describe = describe;
    }
}
