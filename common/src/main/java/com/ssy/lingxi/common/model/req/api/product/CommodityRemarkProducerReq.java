package com.ssy.lingxi.common.model.req.api.product;

import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;

/**
 * 商品描述
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/8/10
 */
@Data
public class CommodityRemarkProducerReq implements Serializable {
    private static final long serialVersionUID = -983503022498182501L;

    /**
     * 视频
     */
    private String[] video;

    /**
     * 图片
     */
    @Valid
    private List<CommodityRemarkImageProducerReq> imageList;

    /**
     * 文字
     */
    private String[] word;
}
