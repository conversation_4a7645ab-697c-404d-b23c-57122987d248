package com.ssy.lingxi.common.model.resp.api;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 接口响应实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@Getter
@Setter
public class ApiResp implements Serializable {
    private static final long serialVersionUID = -410050086607476411L;

    /**
     * 编码
     */
    private Integer code;

    /**
     * 提示信息
     */
    private String msg;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 错误代码
     */
    private String errorNo;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 响应内容
     */
    private Object data;

    /**
     * 响应时间:
     * 格式: yyyy-MM-dd HH:mm:ss
     */
    private String time;

}
