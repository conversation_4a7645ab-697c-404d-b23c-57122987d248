package com.ssy.lingxi.common.model.req.api.pay;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/5
 */
@Data
public class MaterialStockDataSyncReq {

    /**
     * 存料数据列表
     */
    private List<DataSchemaInfo> batch;


    //数据库信息
    public static class DataSchemaInfo{
        //会员信息
        private MemberInfo row;

        public MemberInfo getRow() {
            return row;
        }

        public void setRow(MemberInfo row) {
            this.row = row;
        }
    }

    //会员信息
    public static class MemberInfo{

        /**
         * 客户编码
         */
        private String khbm;

        public String getKhbm() {
            return khbm;
        }

        public void setKhbm(String khbm) {
            this.khbm = khbm;
        }
    }
}
