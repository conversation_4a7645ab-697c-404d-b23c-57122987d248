package com.ssy.lingxi.common.model.resp;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 通用分页数据返回对象
 *
 * @param <T> 数据体
 */
@Data
public class PageDataResp<T> {
    /**
     * 记录总条数
     */
    private Long totalCount;

    /**
     * 数据体
     */
    private List<T> data;

    /**
     * 无参构造器
     */
    public PageDataResp() {
        this.totalCount = 0L;
        this.data = new ArrayList<>();
    }

    /**
     * 带参构造器
     */
    public PageDataResp(Long totalCount, List<T> data) {
        this.totalCount = totalCount;
        this.data = data;
    }

    /**
     * 创建一个PageDataResp对象
     */
    public static <T> PageDataResp<T> buildBy(Integer totalCount, List<T> data) {
        totalCount = Optional.ofNullable(totalCount).orElse(0);
        return new PageDataResp<>((long) totalCount, data);
    }

}
