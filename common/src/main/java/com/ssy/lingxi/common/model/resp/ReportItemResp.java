package com.ssy.lingxi.common.model.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 首页-待办
 * <AUTHOR>
 * @version V3.0.0
 * @since 2023/12/7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReportItemResp {
    /**
     * 计数名称
     */
    private String name;

    /**
     * 链接
     */
    private String link;

    /**
     * 数量
     */
    private Long count;
}
