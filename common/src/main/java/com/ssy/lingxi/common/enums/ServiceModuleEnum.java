package com.ssy.lingxi.common.enums;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import lombok.Getter;

import java.util.Arrays;

/**
 * 项目部署 - 瓴犀项目部署枚举
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2023-04-13
 **/
@Getter
public enum ServiceModuleEnum {
    AFTERSALES_SERVICE(1, "售后服务", ServiceModuleConstant.AFTERSALES_SERVICE),
    API_SERVICE(2, "api服务", ServiceModuleConstant.API_SERVICE),
    COMMODITY_SERVICE(3, "新商品服务", ServiceModuleConstant.COMMODITY_SERVICE),
    CONTRACT_SERVICE(4, "合同服务", ServiceModuleConstant.CONTRACT_SERVICE),
    ENGINE_SERVICE(5, "引擎服务", ServiceModuleConstant.ENGINE_SERVICE),
    ENHANCE_SERVICE(6, "加工服务", ServiceModuleConstant.ENHANCE_SERVICE),
    LOGISTICS_SERVICE(7, "物流服务", ServiceModuleConstant.LOGISTICS_SERVICE),
    MANAGE_SERVICE(8, "后台管理服务", ServiceModuleConstant.MANAGE_SERVICE),
    MARKETING_SERVICE(9, "营销服务", ServiceModuleConstant.MARKETING_SERVICE),
    MEMBER_SERVICE(10, "会员服务", ServiceModuleConstant.MEMBER_SERVICE),
    ORDER_SERVICE(11, "订单服务", ServiceModuleConstant.ORDER_SERVICE),
    PAY_SERVICE(12, "支付服务", ServiceModuleConstant.PAY_SERVICE),
    PRODUCT_SERVICE(13, "商品服务", ServiceModuleConstant.PRODUCT_SERVICE),
    PURCHASE_SERVICE(14, "采购服务", ServiceModuleConstant.PURCHASE_SERVICE),
    REPORT_SERVICE(15, "报表服务", ServiceModuleConstant.REPORT_SERVICE),
    SETTLEMENT_SERVICE(17, "结算服务", ServiceModuleConstant.SETTLEMENT_SERVICE),
    SUPPORT_SERVICE(18, "支撑服务", ServiceModuleConstant.SUPPORT_SERVICE),
    TRADE_SERVICE(19, "交易服务", ServiceModuleConstant.TRADE_SERVICE),
    ;

    private final Integer code;
    private final String serviceName;
    private final String moduleName;

    ServiceModuleEnum(Integer code, String serviceName, String moduleName) {
        this.code = code;
        this.serviceName = serviceName;
        this.moduleName = moduleName;
    }

    public static String getServiceName(String moduleName){
        ServiceModuleEnum serviceModuleEnum = Arrays.stream(ServiceModuleEnum.values()).filter(v -> v.moduleName.equals(moduleName)).findAny().orElse(null);
        return null == serviceModuleEnum ? "未知服务" : serviceModuleEnum.serviceName;
    }

    public static String getServiceFilePrefix(Integer code){
        ServiceModuleEnum serviceModuleEnum = Arrays.stream(ServiceModuleEnum.values()).filter(v -> v.code.equals(code)).findAny().orElse(null);
        return null == serviceModuleEnum ? "other" : serviceModuleEnum.moduleName;
    }
}
