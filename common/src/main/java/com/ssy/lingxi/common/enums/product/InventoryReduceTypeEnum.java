package com.ssy.lingxi.common.enums.product;

/**
 * 库存扣减策略
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-10
 */
public enum InventoryReduceTypeEnum {

    /**
     * 按仓位随机扣减(默认) - 1
     */
    RANDOM(1, "按仓位随机扣减(默认)"),

    /**
     * 按仓库物理地址远近顺序扣减 - 2
     */
    ORDER(2, "按仓库物理地址远近顺序扣减");

    InventoryReduceTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
