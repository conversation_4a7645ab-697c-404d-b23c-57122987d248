package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品规格（数仓）
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-21
 */
@Getter
@Setter
public class SellProSpecReq implements Serializable {
    private static final long serialVersionUID = 1535426540137422385L;

    /**
     * 规格ID
     */
    private Integer id;

    /**
     * 商品ID
     */
    private Integer sell_pro_id;

    /**
     * 状态: 1=启用,0=禁用
     */
    private Integer status;

    /**
     * 商家编码
     */
    private String store_code;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 库存数量,默认为0
     */
    private Integer stock;

    /**
     * 最小起订量
     */
    private Integer min_qty;

    /**
     * 基础工费
     */
    private BigDecimal base_fee;

    /**
     * 克工费
     */
    private BigDecimal gram_fee;

    /**
     * 件工费
     */
    private BigDecimal price_fee;

    /**
     * 克重范围（开始）
     */
    private BigDecimal weight_start;

    /**
     * 克重范围（结束）
     */
    private BigDecimal weight_end;

    /**
     * 规格备注(生产要求)
     */
    private String spec_remark;

    /**
     * 供应链规格编码
     */
    private String scm_code;

    /**
     * 属性(镶嵌类)
     */
    private Object inlay_attr;

    /**
     * 商品sku属性数组字符串
     */
    private String commodityAttributeList;

    /**
     * 商品sku属性数组
     */
    private List<SellProSpecAttributeReq> skuAttributeList;

    /**
     * 重试
     */
    private Integer retryCount;
}
