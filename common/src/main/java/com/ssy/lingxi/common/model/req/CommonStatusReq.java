package com.ssy.lingxi.common.model.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 停用/启用 - DTO
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/04/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonStatusReq implements Serializable {

    private static final long serialVersionUID = 684221748498401093L;
    /**
     * id
     */
    @NotNull(message = "{CommonStatusReq.id.NotNull}")
    private Long id;

    /**
     * 状态 0-无效 1-有效
     */
    @NotNull(message = "{CommonStatusReq.status.NotNull}")
    private Integer status;

}
