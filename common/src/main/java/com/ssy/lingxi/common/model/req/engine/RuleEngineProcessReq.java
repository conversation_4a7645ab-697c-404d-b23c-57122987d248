package com.ssy.lingxi.common.model.req.engine;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 规则引擎请求参数类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/29
 */
@Data
public class RuleEngineProcessReq implements Serializable {
    private static final long serialVersionUID = 809438529846518874L;

    /**
     * 流程规则ID
     */
    @NotNull(message = "流程规则ID不能为空")
    private Long processId;
}
