package com.ssy.lingxi.common.model.dto.mq;

import lombok.*;

/**
 * 队列消息
 *
 * @param <T> 消息体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueueMsgDTO<T> {

    /**
     * 消息类型
     */
    private TypeEnum type;
    /**
     * 消息体
     */
    private T body;

    /**
     * 消息类型
     */
    @Getter
    @RequiredArgsConstructor
    public enum TypeEnum {
        /**
         * 单个
         */
        SINGLE(),
        /**
         * 批量
         */
        BATCH(),

    }

    /**
     * 构建单个消息
     */
    public static <T> QueueMsgDTO<T> buildSingleBy(T body) {
        QueueMsgDTO<T> queueMsgDTO = new QueueMsgDTO<>();
        queueMsgDTO.setType(TypeEnum.SINGLE);
        queueMsgDTO.setBody(body);
        return queueMsgDTO;
    }

    /**
     * 构建批量消息
     */
    public static <T> QueueMsgDTO<T> buildBatchBy(T body) {
        QueueMsgDTO<T> queueMsgDTO = new QueueMsgDTO<>();
        queueMsgDTO.setType(TypeEnum.BATCH);
        queueMsgDTO.setBody(body);
        return queueMsgDTO;
    }

}
