package com.ssy.lingxi.common.model.req.api.member;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 客户授信同步请求参数
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/14
 */
@Data
public class CustomerCreditSync implements Serializable {
    private static final long serialVersionUID = -7632771983907664629L;

    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String corporationCode;

    /**
     * 客户编码
     */
    @NotBlank(message = "客户编码不能为空")
    private String memberCode;

    /**
     * 授信额度
     */
    @NotBlank(message = "授信额度不能为空")
    private BigDecimal creditQuota;

}
