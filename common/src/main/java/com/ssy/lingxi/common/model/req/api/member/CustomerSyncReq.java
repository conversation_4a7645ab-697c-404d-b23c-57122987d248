package com.ssy.lingxi.common.model.req.api.member;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 品牌会员同步请求参数
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/6/5
 */
@Data
public class CustomerSyncReq implements Serializable {
    private static final long serialVersionUID = 8861167211703734480L;

    /**
     * 账号
     */
    @JSONField(name = "account")
    @NotBlank(message = "账号不能为空")
    private String account;

    /**
     * 密码
     */
    @JSONField(name = "passwd")
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 品牌会员名称
     */
    @JSONField(name = "company_name")
    @NotBlank(message = "品牌会员名称不能为空")
    private String name;

    /**
     * 品牌会员编码
     */
    @JSONField(name = "company_code")
    @NotBlank(message = "品牌会员编码不能为空")
    private String code;

    /**
     * 品牌会员手机号码
     */
    private String phone;

    /**
     * 邮箱
     */
    @Email(message = "品牌会员邮箱格式不正确")
    private String email;

    /**
     * 品牌会员简称
     */
    @JSONField(name = "abbr")
    private String simpleName;

    /**
     * 数据来源
     */
    @JSONField(name = "c_app")
    private String dataSource;

}
