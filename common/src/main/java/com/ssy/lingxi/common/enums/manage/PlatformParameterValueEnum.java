package com.ssy.lingxi.common.enums.manage;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 平台参数配置实体类-参数值
 * // * @see com.ssy.lingxi.manage.entity.ParameterDO#parameterValue
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PlatformParameterValueEnum {

    /**
     * 联营商城
     */
    MOBILE_DEFAULT_SHOP_UNION(PlatformParameterEnum.MOBILE_DEFAULT_SHOP, "1", "联营商城"),
    /**
     * 自营商城
     */
    MOBILE_DEFAULT_SHOP_SELF(PlatformParameterEnum.MOBILE_DEFAULT_SHOP, "2", "自营商城"),

    ;

    /**
     * 关联的参数枚举
     */
    private final PlatformParameterEnum platformParameterEnum;
    /**
     * 数据库中存储的参数值
     * // * @see com.ssy.lingxi.manage.entity.ParameterDO#parameterValue
     */
    private final String parameterValue;
    /**
     * 参数值对应的前端展示名
     */
    private final String parameterValueShowName;

    private static final List<PlatformParameterValueEnum> enumList = Arrays.stream(PlatformParameterValueEnum.values()).collect(Collectors.toList());


    /**
     * 查找参数值的前端展示名，找不到则直接显示原始参数值
     *
     * @param platformParameterEnumCode 参数编码 {@link PlatformParameterEnum#getCode()}
     * @param parameterValue            找不到则返回当前值
     */
    public static String getParameterValueShowName(String platformParameterEnumCode, String parameterValue) {
        return enumList.stream().filter(
                o -> o.getPlatformParameterEnum().getCode().equals(platformParameterEnumCode) && o.getParameterValue().equals(parameterValue)
        ).findFirst().map(PlatformParameterValueEnum::getParameterValueShowName).orElse(parameterValue);
    }

    /**
     * 查找参数值的前端展示名列表
     *
     * @param platformParameterEnumCode 参数编码 {@link PlatformParameterEnum#getCode()}
     */
    public static List<PlatformParameterValueEnum> getParameterValueEnumList(String platformParameterEnumCode) {
        return enumList.stream().filter(o -> o.getPlatformParameterEnum().getCode().equals(platformParameterEnumCode)).collect(Collectors.toList());
    }

}
