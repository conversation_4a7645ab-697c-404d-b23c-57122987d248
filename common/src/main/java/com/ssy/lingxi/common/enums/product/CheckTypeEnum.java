package com.ssy.lingxi.common.enums.product;

/**
 * 审核类型
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/5
 */
public enum CheckTypeEnum {

    MEMBER_CHECK(1, "会员审核"),
    PLATFORM_CHECK(2, "平台审核");

    private final Integer code;
    private final String message;

    CheckTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
