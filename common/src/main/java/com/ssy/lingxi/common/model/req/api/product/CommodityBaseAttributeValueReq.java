package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.enums.CommodityDescribeAttributeDataTypeEnum;
import com.ssy.lingxi.common.enums.SellShopTreeFormNodeTypeEnum;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 基础属性值
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-14
 */
@Getter
@Setter
public class CommodityBaseAttributeValueReq implements Serializable {
    private static final long serialVersionUID = 5668238481414702048L;

    /**
     * 属性值名称
     */
    @NotBlank(message = "请填写属性值名称")
    private String attributeValueName;

    /**
     * 属性值编号
     */
    @NotBlank(message = "请填写属性值编号")
    private String attributeValueCode;

    /**
     * 状态: 1=启用,0=禁用
     */
    private Integer status;

    /**
     * 所属的属性数据类型
     * @see CommodityDescribeAttributeDataTypeEnum
     */
    private Integer attributeDataType;

    /**
     * 节点类型,品类,系列,店内分类
     * @see SellShopTreeFormNodeTypeEnum
     */
    private String nodeType;

    /**
     * 排序
     */
    private Integer sortIndex;

    /**
     * 数仓父ID（表名:id）
     */
    private String dataWarehouseParentId;

    /**
     * 级联ID,如果是根节点,为自身ID+: ,如果是子级节点,则为父级联ID+当前节点ID+:
     */
    private String jointId;

    /**
     * 数仓平台表的主键ID（表名:id）
     */
    private String dataWarehousePkId;

    /**
     * 关联的属性名称
     */
    private String associationAttributeValueName;

    /**
     * - r (Read) : 表示该记录是在初始阶段读取的。
     * - c (Create) : 插入新记录。
     * - u (Update) : 更新记录。
     * - d (Delete) : 删除记录。
     */
    private String op;

    /**
     * 子级属性值列表
     */
    private List<CommodityBaseAttributeValueReq> childrenValueList;
}
