package com.ssy.lingxi.common.constant;

import java.io.Serializable;

/**
 * 公共的常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/19
 */
public class Constant implements Serializable {
    private static final long serialVersionUID = -6249413045216324355L;

    /**
     * HttpHead中，AccessToken所在的字段名
     */
    public static final String ACCESS_TOKEN = "accessToken";

    /**
     * HttpHead中，RefreshToken所在的字段名
     */
    public static final String REFRESH_TOKEN = "refreshToken";

    /**
     * HttpHead中，branchId所在的字段名
     */
    public static final String BRANCH_ID = "branchId";

    /**
     * HttpHeader中，客户端标识所在的字段名
     * com.ssy.lingxi.component.base.enums.SystemSourceEnum
     */
    public static final String LOGIN_SOURCE = "source";

    /**
     * HttpHeader中，代客下单会员字段名
     */
    public static final String MK_AGENT_MEMBER_ID = "agentMemberId";

    /**
     * HttpHeader中，代客下单会员角色字段名
     */
    public static final String MK_AGENT_ROLE_ID = "agentRoleId";

    /**
     * 会员积分分隔符
     */
    public static final String MEMBER_LRC_SPLIT = ":";

    /**
     * 品类fullId为8位
     */
    public static final String CATEGORY_FULL_ID_NUM = "%08d";

    /**
     * fullId为8位
     */
    public static final String FULL_ID_NUM = "%08d";

    /**
     * fullId分隔符
     */
    public static final String SPLIT_STR_FULL_ID = ".";

    /**
     * 短信服务配置参数值和参数注释的分割符;单位和品类初始化到redis分隔符;
     */
    public static final String SPLIT_STR = "|";

    /**
     * 转义符
     */
    public static final String TRANSFER_STR = "\\";

    /**
     * 下划线
     */
    public static final String UNDERLINE_STR = "_";

    /**
     * 中划线
     */
    public static final String MIDDLE_LINE_STR = "-";

    /**
     * 逗号
     */
    public static final String COMMA_STR = ",";

    /**
     * 商品名称和规格分隔符
     */
    public static final String COMMODITY_NAME_SPLIT_STR = "/";

    /**
     * 编号默认位数
     */
    public static final Integer CODE_NUM = 6;

    /**
     * 商品编号前缀
     */
    public static final String COMMODITY_CODE_PREFIX = "P";

    /**
     * 账户交易流水号
     */
    public static final String ACCOUNT_TRACE_CODE = "account-trace-code";

    /**
     * 账户交易流水号:时间格式
     */
    public static final String ACCOUNT_TRACE_CODE_DATE = "yyyyMMddHHmm";

    /**
     * 账户交易流水号位数
     */
    public static final Integer ACCOUNT_TRACE_CODE_NUM_LEN = 6;

    /**
     * 单号位数
     */
    public static final Integer ORDER_NO = 6;

    /**
     * 一天换成成毫秒
     */
    public static final long DAY_TO_MILLISECONDS = 24 * 60 * 60 * 1000L;

    /**
     * 一天换成成秒
     */
    public static final long DAY_TO_SECONDS = 24 * 60 * 60L;

    /**
     * 商品索引
     */
    public static final String ES_COMMODITY_INDEX = "commodity";

    /**
     * 结算流水号
     */
    public static final String SETTLEMENT_CODE = "settlement_code";

    /**
     * 结算流水号:时间格式
     */
    public static final String SETTLEMENT_CODE_DATE = "yyyyMMddHHmm";

    /**
     * 结算流水号位数
     */
    public static final Integer SETTLEMENT_CODE_NUM_LEN = 6;

    /**
     * 全部
     */
    public static final String ALL = "ALL";

    /**
     * 商品价格非阶梯价的话，key为0-0
     */
    public static final String NOT_STEP_PRICE_KEY = "0-0";

    /**
     * 金价key
     */
    public static final String GOLD_PRICE_KEY = "gold_price";

    /**
     * 金价过期时间1小时
     */
    public static final Long GOLD_PRICE_TIME = 60 * 60L;

    /**
     * 商城热搜词key
     */
    public static final String SHOP_HOT_SEARCH_TERM_KEY = "shop_hot_search_term";

}
