package com.ssy.lingxi.common.model.req;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 通用ID - 分页 - 请求实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CommonIdPageDataReq extends PageDataReq {
    private static final long serialVersionUID = 7984028145032102617L;

    /**
     * id值
     */
    @NotNull(message = "{CommonIdPageDataReq.id.NotNull}")
    private Long id;
}
