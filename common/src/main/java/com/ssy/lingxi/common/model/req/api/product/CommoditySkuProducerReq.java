package com.ssy.lingxi.common.model.req.api.product;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品sku对象
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/20
 */
@Data
public class CommoditySkuProducerReq implements Serializable {
    private static final long serialVersionUID = -6236138644022797117L;

    /**
     * 商品sku编码
     */
    @NotEmpty(message = "商品sku编码不能为空")
    @Length(max = 32,message = "商品sku编码最大字符长度32")
    private String skuCode;

    /**
     * 建议售价
     */
    @NotNull(message = "建议售价不能为空")
    private BigDecimal unitPrice;

    /**
     * 最低售价
     */
    @NotNull(message = "最低售价不能为空")
    private BigDecimal minPrice;

    /**
     * 商品图片（图片url路径数组，第一张当做商品主图）
     */
    private String[] commodityPic;

    /**
     * sku名称
     */
    @NotEmpty(message = "sku名称不能为空")
    @Length(max = 100,message = "sku名称最大字符长度100")
    private String name;

    /**
     * 副单位单价换算比率
     */
    private BigDecimal priceRate;

    /**
     * 是否允许使用会员折扣价购买
     */
    @NotNull(message = "是否允许使用会员折扣价购买不能为空")
    private Boolean isMemberPrice;

    /**
     * HS编码
     */
    @Length(max = 50,message = "HS编码最大字符长度50")
    private String hsCode;

    /**
     * 商品销售属性值组合
     */
    @Valid
    private List<SaleAttributeProducerReq> saleAttributeValueList;
}
