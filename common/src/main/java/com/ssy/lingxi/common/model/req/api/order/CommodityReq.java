package com.ssy.lingxi.common.model.req.api.order;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.math.BigDecimal;

/**
 * 单件信息
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/11
 */
@Data
public class CommodityReq {

    /**
     * 单货商品编码
     */
    @NotEmpty(message = "单货商品编码不能为空")
    private String skuCode;

    /**
     * 单件编号
     */
    @NotEmpty(message = "单件编号不能为空")
    private String singleCode;

    /**
     * new净重(需要大于零)
     */
    @NotNull(message = "净重不能为空")
    @PositiveOrZero(message = "净重必须大于等于零")
    private BigDecimal netWeight;

    /**
     * new基础工费
     */
    private BigDecimal baseLaborCosts = BigDecimal.ZERO;

    /**
     * new附加工费
     */
    private BigDecimal additionalLaborCosts = BigDecimal.ZERO;

    /**
     * new件工费
     */
    private BigDecimal pieceLaborCosts = BigDecimal.ZERO;

    /**
     * 仓库编码
     */
    @NotEmpty(message = "仓库编码不能为空")
    private String code;

    /**
     * 判断是否被使用过
     */
    private Boolean used = false;
}
