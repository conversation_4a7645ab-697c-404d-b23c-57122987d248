package com.ssy.lingxi.common.model.req.api.member;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 客户工费优惠主表Req（入参）
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerProcessFeeDiscountSyncReq extends ApiBaseReq implements Serializable {

    private static final long serialVersionUID = -8347044449669659282L;
    /**
     * 单据流水号（主键）
     */
    @NotNull(message = "单据流水号不能为空")
    private Long djlsh;
    /**
     * 单据号
     */
    private String djh;
    /**
     * 客户编码
     */
    @NotEmpty(message = "客户编码不能为空")
    private String khbm;
    /**
     * 客户名称
     */
    @NotEmpty(message = "客户名称不能为空")
    private String khmc;
    /**
     * 最后维护时间
     */
    private String zhwhsj;
    /**
     * 是否通用（默认都是：否）
     */
    private String sfty;
    /**
     * 单据状态
     */
    private String djzt;
    /**
     * 千足基础工费优惠
     */
    private BigDecimal qzjcgfyh;
    /**
     * 万足基础工费优惠
     */
    private BigDecimal wzjcgfyh;
    /**
     * 五个9基础工费优惠
     */
    private BigDecimal wgjjcgfyh;
    /**
     * 优惠申请时间
     */
    private String yhsqsj;
    /**
     * 优惠截止时间（过期后不生效）
     */
    private String yhjzsj;
    /**
     * 是否启用（选项值：是，否）否为不生效
     */
    private String sfqy;
    /**
     * 备注信息
     */
    private String bzxx;
    /**
     * 制单人ID
     */
    private String zdrid;
    /**
     * 制单人
     */
    private String zdr;
    /**
     * 表体明细
     */
    @Valid
    private List<CustomerProcessFeeDiscountBtSyncReq> bt_content;
    /**
     * 附体明细
     */
    @Valid
    private List<CustomerProcessFeeDiscountFtSyncReq> ft_content;
}
