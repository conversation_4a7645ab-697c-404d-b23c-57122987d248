package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.enums.DataWarehousePlatformPushTableTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 参数管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-17
 */
@Getter
@Setter
public class SellShopParamsReq implements Serializable {
    private static final long serialVersionUID = 3055960087891842246L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 参数名
     */
    private String param_name;

    /**
     * 组件类型,目前有 singleChoice,multiChoice,text,date,规格参数的组件类型不能是multiChoice
     */
    private String component_type;

    /**
     * 参数类型,商品参数：SPU_PARAM/规格参数
     */
    private String param_type;

    /**
     * 如果是list类型,需要配置预设值,用json数组格式保存
     */
    private Object pre_selected_value;

    /**
     * 是否展示在商品发布页,0=是,1=否,默认为0
     */
    private Integer show;

    /**
     * 所属一级分类,json数组,如果是规格参数,这个字段可以留空,格式如 ["戒指","手镯"]
     */
    private Object category;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 最后更新人
     */
    private String revisor;

    /**
     * url
     */
    private String url;

    /**
     * 表名
     * @see DataWarehousePlatformPushTableTypeEnum
     */
    private String tableName;
}
