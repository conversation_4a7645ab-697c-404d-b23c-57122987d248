package com.ssy.lingxi.common.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SelfShopRedisDTO {

    private Long id;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员角色id
     */
    private Long memberRoleId;

    /**
     * 商城名称
     */
    private String name;

    /**
     * 商城类型
     * @see ShopTypeEnum
     */
    private Integer type;

    /**
     * 商城环境:1.web 2.H5 3.小程序 4.APP
     */
    private Integer environment;

    /**
     * 商城属性: 1.B端商城 2.C端商城
     */
    private Integer property;

    /**
     * 商城LOGO
     */
    private String logoUrl;

    /**
     * 启用状态：true启用 false禁用
     */
    private Boolean enabled;

}
