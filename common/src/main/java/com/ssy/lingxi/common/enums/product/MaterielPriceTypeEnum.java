package com.ssy.lingxi.common.enums.product;

/**
 * 物料价格类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/25
 */
public enum MaterielPriceTypeEnum {

    /**
     * 1:合同价格; 2:订单价格
     */
    CONTRACT_PRICE(1, "合同价格"),
    ORDER_PRICE(2, "订单价格");

    private final Integer code;
    private final String message;

    MaterielPriceTypeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
