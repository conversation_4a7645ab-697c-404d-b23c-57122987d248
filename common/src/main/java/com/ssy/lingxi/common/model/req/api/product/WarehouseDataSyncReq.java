package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 数仓平台仓库同步入参
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-23
 */
@Getter
@Setter
public class WarehouseDataSyncReq implements Serializable {
    private static final long serialVersionUID = -7359574311311899469L;

    /**
     * 仓库类型
     */
    private String cklx;

    /**
     * 仓库编码
     */
    @NotBlank(message = "仓库编码不能为空")
    private String ckid;

    /**
     * 仓库名称
     */
    @NotBlank(message = "仓库名称不能为空")
    private String ckmc;

    /**
     * 启用（是：启用，否：停用）
     */
    private String qy;
}
