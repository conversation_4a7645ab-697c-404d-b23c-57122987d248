package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CommodityProducerReq extends ApiBaseReq implements Serializable {
    private static final long serialVersionUID = 7572646587318184280L;

    /**
     * 商品名称
     */
    @NotEmpty(message = "商品名称不能为空")
    @Length(max = 100,message = "商品名称最大字符长度100")
    private String name;

    /**
     * 商品编码
     */
    @NotEmpty(message = "商品编码不能为空")
    @Length(max = 32,message = "商品编码最大字符长度32")
    private String code;

    /**
     * 商品品类编码
     */
    @NotEmpty(message = "商品品类编码不能为空")
    @Length(max = 32,message = "商品品类编码最大字符长度32")
    private String categoryCode;

    /**
     * 商品类型：1-实物商品(默认)、2-虚拟商品、3-服务商品、4-积分兑换商品
     */
    @NotNull(message = "商品类型不能为空")
    private Integer commodityType = 1;

    /**
     * 产品定价：1-现货价格, 2-价格需要询价, 3-积分兑换商品, 4-赠品
     */
    private Integer priceType = 1;

    /**
     * 税率
     */
    @NotNull(message = "税率不能为空")
    private BigDecimal taxRate;

    /**
     * 商品品牌编码
     */
    @Length(max = 32,message = "商品品牌编码最大字符长度32")
    private String brandCode;

    /**
     * 商品标语
     */
    @Length(max = 100,message = "商品标语最大字符长度100")
    private String slogan;

    /**
     * 商品卖点
     */
    private String[] sellingPoint;

    /**
     * 是否跨境商品
     */
    private Boolean isCrossBorder = false;

    /**
     * 送货周期
     */
    private Integer sendCycle;

    /**
     * 是否开发票
     */
    private Boolean isInvoice;

    /**
     * 唛头
     */
    @Length(max = 50,message = "唛头最大字符长度50")
    private String marks;

    /**
     * 包装清单
     */
    @Length(max = 120,message = "包装清单最大字符长度120")
    private String packing;

    /**
     * 售后服务
     */
    @Length(max = 300,message = "售后服务最大字符长度300")
    private String afterService;

    /**
     * SEO优化标题
     */
    @Length(max = 50,message = "SEO优化标题最大字符长度50")
    private String title;

    /**
     * SEO优化描述
     */
    @Length(max = 200,message = "SEO优化描述最大字符长度200")
    private String description;

    /**
     * SEO优化关键字
     */
    @Length(max = 100,message = "SEO优化关键字最大字符长度100")
    private String keywords;

    /**
     * 商品详情描述
     */
    @Valid
    private List<CommodityRemarkProducerReq> commodityRemarkList;

    /**
     * 物流信息
     */
    @Valid
    private LogisticsProducerReq logistics;

    /**
     * 商品sku
     */
    @Valid
    @NotEmpty(message = "商品sku不能为空")
    private List<CommoditySkuProducerReq> commoditySkuList;

    /**
     * 商品描述属性值组合
     */
    @Valid
    private List<RemarkAttributeProducerReq> remarkAttributeValueList;

    /**
     * 税收分类码
     */
    @NotEmpty(message = "税收分类码不能为空")
    @Length(max = 32,message = "税收分类码最大字符长度32")
    private String taxType;

    /**
     * 销项税率号
     */
    @NotEmpty(message = "销项税率号不能为空")
    @Length(max = 32,message = "销项税率号最大字符长度32")
    private String taxCode;

    /**
     * 启用序列号
     */
    private Boolean isSerial = false;

    /**
     * 启用批次号
     */
    private Boolean isBatch = false;

    /**
     * 保质期
     */
    private Integer quality;

    /**
     * 保质期单位
     */
    @Length(max = 32,message = "保质期单位最大字符长度32")
    private String qualityUnit;

    /**
     * 配货模式
     */
    @Length(max = 32,message = "配货模式最大字符长度32")
    private String distributionMode;

    /**
     * 允许分批送货
     */
    private Boolean isBatchSend = false;

    /**
     * 最长送货时间
     */
    private Integer maxDeliveryTime;

    /**
     * 最长送货时间单位：1-年; 2-月; 3-周; 4-日;
     */
    private Integer maxDeliveryTimeUnit;

    /**
     * 进项税率号
     */
    @Length(max = 32,message = "进项税率号最大字符长度32")
    private String incomeTaxCode;

    /**
     * 进项税率
     */
    private BigDecimal incomeTaxRate = BigDecimal.ONE;

    /**
     * 是否推工单
     */
    private Boolean isPushOrder = false;

    /**
     * 服务类型
     */
    @Length(max = 32,message = "服务类型最大字符长度32")
    private String serviceType;

    /**
     * 是否安装
     */
    private Boolean isInstall = false;

    /**
     * 是否售后上门服务
     */
    private Boolean isDoorService = false;

    /**
     * 是否退换货
     */
    private Boolean isReturn = false;

    /**
     * 退换货承诺
     */
    @Length(max = 100,message = "退换货承诺最大字符长度100")
    private String isReturnPromise;

    /**
     * 是否保修
     */
    private Boolean isGuarantee = false;

    /**
     * 保修时长
     */
    private Integer guaranteeTime;

    /**
     * 保修时长单位
     */
    @Length(max = 32,message = "保修时长单位最大字符长度32")
    private String guaranteeTimeUnit;

    /**
     * 商品证件
     */
    private List<CommodityLicenseReq> commodityLicenseList;

}
