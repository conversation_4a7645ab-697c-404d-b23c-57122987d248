package com.ssy.lingxi.common.model.req.engine;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 流程引擎规则配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EngineRuleQueryReq implements Serializable {

    private static final long serialVersionUID = -1290763209539091592L;

    /**
     * 流程类型<ProcessTypeDetailEnum.code>
     */
    @NotNull(message = "流程类型不能为空")
    private Integer type;

    /**
     * 会员id
     */
    @NotNull(message = "会员Id不能为空")
    private Long memberId;

    /**
     * 会员角色id
     */
    @NotNull(message = "会员角色Id不能为空")
    private Long memberRoleId;

}
