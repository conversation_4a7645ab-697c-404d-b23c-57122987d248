package com.ssy.lingxi.common.constant;

import java.io.Serializable;

/**
 * 公共的常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/19
 */
public class RedisConstant implements Serializable {
    private static final long serialVersionUID = 4220122758599803278L;

    // ================================================== redis库配置 ==================================================
    /**
     * redis数据库下标值（用户库）
     */
    public static final Integer REDIS_USER_INDEX = 0;

    /**
     * redis数据库下标值（商品库）
     */
    public static final Integer REDIS_PRODUCT_INDEX = 1;

    /**
     * redis数据库下标值（订单库）
     */
    public static final Integer REDIS_ORDER_INDEX = 2;

    /**
     * redis数据库下标值（平台后台基础库）
     */
    public static final Integer REDIS_PLATFORM_MANAGE_INDEX = 3;

    /**
     * redis数据库下标值（短信服务库）
     */
    public static final Integer REDIS_SMS_INDEX = 4;

    /**
     * redis数据库下标值（报表服务库）
     */
    public static final Integer REDIS_REPORT_INDEX = 5;

    /**
     * redis数据库下标值（支付服务库）
     */
    public static final Integer REDIS_PAY_INDEX = 6;

    /**
     * redis数据库下标值（采购服务库）
     */
    public static final Integer REDIS_PURCHASE_INDEX = 7;

    /**
     * redis数据库下标值（合同服务库）
     */
    public static final Integer REDIS_CONTRACT_INDEX = 8;

    /**
     * redis数据库下标值（API服务库）
     */
    public static final Integer REDIS_API_INDEX = 9;

    /**
     * redis数据库下标值（结算服务）
     */
    public static final Integer REDIS_SETTLEMENT_INDEX = 10;

    /**
     * redis数据库下标值（营销服务）
     */
    public static final Integer REDIS_MARKETING_INDEX = 11;

    /**
     * redis数据库下标值（工作流服务）
     */
    public static final Integer REDIS_WORKFLOW_INDEX = 12;

    // =============================================== token-redis-key ===============================================
    /**
     * Token在redis的通用前缀
     */
    public static final String TOKEN_REDIS_PREFIX = "Token";

    /**
     * AccessToken在redis的通用前缀
     */
    public static final String ACCESS_TOKEN_PREFIX = "AT";

    /**
     * RefreshToken在redis的通用前缀
     */
    public static final String REFRESH_TOKEN_PREFIX = "RT";


    // ================================================== redis-key ==================================================
    /**
     * ip监控 - 白名单前缀
     */
    public static final String IP_MONITOR_WHITELIST = "IP_MONITOR:WHITELIST";

    /**
     * ip监控 - 黑名单前缀
     */
    public static final String IP_MONITOR_BLACKLIST = "IP_MONITOR:BLACKLIST";

    /**
     * 会员积分前缀
     */
    public static final String MEMBER_LRC_PREFIX = "lrc";

    /**
     * 实名验证配置信息
     */
    public static final String REDIS_REAL_NAME_CONFIG = "real-name-config";

    /**
     * 短信服务器配置信息
     */
    public static final String REDIS_SMS_CONFIG = "sms-config";

    /**
     * 平台后台--行政区域key
     */
    public static final String REDIS_KEY_AREA = "area";

    /**
     * 平台后台--行政区域key(缓存带区域首字母)
     */
    public static final String REDIS_KEY_AREA_INITIAL = "area_initial";

    /**
     * 平台后台--商城key
     */
    public static final String REDIS_KEY_SHOP = "shop";

    /**
     * 平台后台--自营商城key
     */
    public static final String REDIS_KEY_SELF_SHOP = "self_shop";

    /**
     * 平台后台--项目部署-yapi-cookie key
     */
    public static final String REDIS_KEY_DEPLOY_YAPI_COOKIE = "deploy_yapi_Cookie";

    /**
     * 商品服务--平台后台品类
     */
    public static final String REDIS_KEY_CATEGORY = "category";

    /**
     * 商品服务--品牌
     */
    public static final String REDIS_KEY_BRAND = "brand";

    /**
     * 商品服务--会员品类
     */
    public static final String REDIS_KEY_CUSTOMER_CATEGORY = "customer-category";

    /**
     * 商品服务--会员品类属性排序
     */
    public static final String REDIS_KEY_CUSTOMER_CATEGORY_ATTRIBUTE_SORT= "customer_category_attribute_sort_";

    /**
     * 商品服务--送样需求单号
     */

    public static final String REDIS_KEY_SAMPLE_DELIVERY_NO = "sample_delivery_no";

    /**
     * 支付服务--建行瓦片平台权限
     */
    public static final String REDIS_KEY_CCB_PAY_AUTH = "ccb-pay-auth";

    /**
     * 结算服务 --对账单号
     */
    public static final String REDIS_KEY_SETTLEMENT_RECONCILIATION_NO = "settlement_reconciliation_no";

    /**
     * 结算服务 -- 请款单号
     */
    public static final String REDIS_KEY_SETTLEMENT_APPLY_AMOUNT_NO = "settlement_apply_amount_no";

    /**
     * 采购服务--招标编号
     */
    public static final String REDIS_KEY_PURCHASE_INVITE_TENDER = "invite_tender";

    /**
     * 采购服务--投标编号
     */
    public static final String REDIS_KEY_PURCHASE_SUBMIT_TENDER = "submit_tender";

    /**
     * 合同服务--合同编号(用于生成编号)
     */
    public static final String REDIS_KEY_CONTRACT_NO = "contract_no";

    /**
     * 合同服务--合同编号(用于合同保存)
     */
    public static final String REDIS_KEY_CONTRACT_NO_SAVE = "contract_no:save";

    /**
     * 合同服务--电子合同id(用于回调填充用户信息)
     */
    public static final String REDIS_KEY_CONTRACT_ID = "contract_id";

    /**
     * 订单服务--质检单编号
     */
    public static final String REDIS_KEY_QUALITY_NO = "quality_no";

    /**
     * 订单服务--订单分布式锁
     */
    public static final String REDIS_KEY_ORDER_STATUS = "ORDER:STATUS:";

    /**
     * 营销服务--活动商品sku会员购买数（前缀：活动id=hkey   skuid:memberId:roleId =hfield）
     */
    public static final String REDIS_KEY_MARKETING_ACTIVITY_GOODS_MEMBER_BUY_NUM_PREFIX = "activity_goods_member_buy_num";
    /**
     * 营销服务--活动商品sku统计销量（key=销量前缀:活动id:skuid=pk1）
     */
    public static final String REDIS_KEY_MARKETING_ACTIVITY_GOODS_SKU_SALES_STATISTICS_PREFIX = "activity_goods_sales";
    /**
     * 营销服务--活动商品总实购数量(key=pk1:total)[string数据类型]
     */
    public static final String REDIS_KEY_MARKETING_SKU_SALES = "total";
    /**
     * 营销服务--活动商品参与客户数[set数据类型]
     */
    public static final String REDIS_KEY_MARKETING_CUSTOMER_NUM = "customer_num";
    /**
     * 营销服务--活动商品sku销量添加锁（key=销量添加前缀:活动类型：活动id:skuid）
     */
    public static final String REDIS_KEY_MARKETING_ACTIVITY_GOODS_SKU_SALES_ADD_PREFIX = "activity_goods_sales_add";
    /**
     * 营销服务--拼团时效（key=拼团时效前缀:拼团id）
     */
    public static final String REDIS_KEY_MARKETING_ACTIVITY_GOODS_GP_PREFIX = "activity_goods_gp";
    /**
     * 营销服务--活动执行缓存前缀
     */
    public static final String REDIS_KEY_MARKETING_ACTIVITY_EXECUTE_PREFIX = "activity_execute";
    /**
     * 营销服务--参与客户数
     */
    public static final String REDIS_KEY_MARKETING_ACTIVITY_EXECUTE_CUSTOMER_COUNT = "customer_count";

    /**
     * 营销服务--活动已执行订单数
     */
    public static final String REDIS_KEY_MARKETING_ACTIVITY_EXECUTE_ORDER_COUNT = "order_count";
    /**
     * 营销服务--已执行订单金额
     */
    public static final String REDIS_KEY_MARKETING_ACTIVITY_EXECUTE_ORDER_AMOUNT = "order_amount";

    /**
     * 结算服务--token前缀(由于只能存储38位，token已经32位了)
     */
    public static final String REDIS_KEY_SETTLEMENT_TOKEN_PREFIX ="settlement_all_in_token";

    /**
     * 结算服务-结算支付结果前缀
     */
    public static final String REDIS_KEY_SETTLEMENT_CODE_RESULT = "settlement_code_result";

    /**
     * redisKey分隔符
     */
    public static final String REDIS_KEY_SPLIT = ":";

    /**
     * 仓位库存数量redisKey
     */
    public static final String REDIS_KEY_FREIGHT_SPACE = "FREIGHT_SPACE:";

    /**
     * 仓位仓库redisKey
     */
    public static final String REDIS_KEY_WAREHOUSE = "WAREHOUSE:";

    /**
     * 仓位库存-所有会员共用
     */
    public static final String REDIS_KEY_FREIGHT_ALL = "all";

    /**
     * 商品筛选项配置key
     */
    public static final String REDIS_COMMODITY_FILTER_CONFIG_KEY = "commodity_filter_config";

    /**
     * 仓位库存-根据商城id和skuId生成redisKey
     */
    public static String joinStockRedisKey(Long shopId, Long skuId) {
        return REDIS_KEY_FREIGHT_SPACE + shopId + "_" + skuId;
    }

}
