package com.ssy.lingxi.common.model.resp.engine;

import lombok.Data;

import java.util.Map;

/**
 * 流程步骤实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/29
 */
@Data
public class SimpleProcessResp {
    private static final long serialVersionUID = 809431529846578874L;
    /**
     * 流程步骤
     */
    private Integer taskStep;

    /**
     * 流程名称
     */
    private String taskName;

    /**
     * 可执行此步骤流程的角色名称
     * <p>当查询的是外部流程，此字段表示会员角色名称</p>
     * <p>当查询的是内部流程，此字段表示用户角色名称</p>
     */
    private String roleName;

    /**
     * 流程自定义属性
     */
    private Map<String, String> properties;
}
