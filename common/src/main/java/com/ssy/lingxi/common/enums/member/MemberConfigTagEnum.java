package com.ssy.lingxi.common.enums.member;

import java.util.Arrays;

/**
 * 会员注册资料字段标签枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-10-19
 */
public enum MemberConfigTagEnum {
    /**
     * 会员名称 - 1
     */
    MEMBER_NAME(1, "会员名称"),

    /**
     * 会员手机号 - 2
     */
    MEMBER_PHONE(2, "会员手机号"),

    /**
     * 会员身份证号 - 3
     */
    MEMBER_IDENTITY_CARD(3, "会员身份证号"),

    /**
     * 会员身份证正面 - 4
     */
    MEMBER_IDENTITY_CARD_FRONT(4, "会员身份证正面"),

    /**
     * 会员身份证反面 - 5
     */
    MEMBER_IDENTITY_CARD_BACK(5, "会员身份证反面"),

    /**
     * 法人姓名 - 6
     */
    LEGAL_PERSON_NAME(6, "法人姓名"),

    /**
     * 法人手机号 - 7
     */
    LEGAL_PERSON_PHONE(7, "法人手机号"),

    /**
     * 法人身份证号 - 8
     */
    LEGAL_PERSON_IDENTITY_CARD(8, "法人身份证号"),

    /**
     * 法人身份证正面 - 9
     */
    LEGAL_PERSON_IDENTITY_CARD_FRONT(9, "法人身份证正面"),

    /**
     * 法人身份证反面 - 10
     */
    LEGAL_PERSON_IDENTITY_CARD_BACK(10, "法人身份证反面"),

    /**
     * 统一信用代码 - 11
     */
    UNIFIED_CREDIT_CODE(11, "统一信用代码"),

    /**
     * 注册资本 - 12
     */
    REGISTERED_CAPITAL(12, "注册资本"),

    /**
     * 成立日期 - 13
     */
    ESTABLISHMENT_DATE(13, "成立日期"),

    /**
     * 营业执照 - 14
     */
    BUSINESS_LICENCE(14, "营业执照"),

    /**
     * 注册地址（省、市、区） - 15
     */
    REGISTER_AREA(15, "注册地址(省、市、区)"),

    /**
     * 注册地址（详细地址）
     */
    REGISTER_ADDRESS(16, "注册地址(详细地址)"),

    /**
     * 行业
     */
    BUSINESS(17, "行业");

    MemberConfigTagEnum(Integer code, String tagName) {
        this.code = code;
        this.tagName = tagName;
    }

    /**
     * 标签枚举值
     */
    private Integer code;

    /**
     * 标签名称
     */
    private String tagName;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    /**
     * 判断是否为字段标签的枚举值
     * @param code 整数类型
     * @return true:是 false:否
     */
    public static Boolean isEnumCode(Integer code) {
        return Arrays.stream(MemberConfigTagEnum.values()).anyMatch(memberConfigTagEnum -> memberConfigTagEnum.getCode().equals(code));
    }

    /**
     * 根据枚举值，获得标签名称
     * @param code 整数枚举值
     * @return 如果包含枚举，返回标签名称，否则返回空字符串
     */
    public static String findTagName(Integer code) {
        MemberConfigTagEnum tagEnum = Arrays.stream(MemberConfigTagEnum.values()).filter(memberConfigTagEnum -> memberConfigTagEnum.getCode().equals(code)).findFirst().orElse(null);
        return tagEnum == null ? "" : tagEnum.getTagName();
    }

    /**
     * 根据枚举值，转换枚举实例
     * @param code 整数枚举值
     * @return 如果包含枚举，返回实例，否则返回Null
     */
    public static MemberConfigTagEnum parseCode(Integer code) {
        return Arrays.stream(MemberConfigTagEnum.values()).filter(memberConfigTagEnum -> memberConfigTagEnum.getCode().equals(code)).findFirst().orElse(null);
    }
}
