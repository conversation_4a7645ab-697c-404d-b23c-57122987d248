package com.ssy.lingxi.common.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 平台会员（即上级会员是平台后台）的等级（level)、权益（right）、信用（credit）信息缓存对象
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020-11-23
 */
@Data
public class MemberLrcCacheDTO implements Serializable {
    private static final long serialVersionUID = -2566100745324668895L;

    public MemberLrcCacheDTO() {
        this.score = 0;
        this.level = 0;
        this.levelTag = "";
        this.sumReturnMoney = BigDecimal.ZERO;
        this.sumPoint = 0;
        this.currentPoint = 0;
        this.sumUsedPoint = 0;
        this.creditPoint = 0;
        this.tradeCommentPoint = 0;
        this.afterSaleCommentPoint = 0;
        this.complainPoint = 0;
        this.registerYearsPoint = 0;
    }

    /**
     * 当前的等级积分
     */
    private Integer score;

    /**
     * 当前等级
     */
    private Integer level;

    /**
     * 当前的等级名称
     */
    private String levelTag;

    /**
     * 累积返现金额
     */
    private BigDecimal sumReturnMoney;

    /**
     * 累计获得的权益积分
     */
    private Integer sumPoint;

    /**
     * 当前可用的权益积分 = 累计获得的权益积分 - 累计已用权益积分
     */
    private Integer currentPoint;

    /**
     * 累计使用的权益积分
     */
    private Integer sumUsedPoint;

    /**
     * 信用积分 = 交易评价积分 + 售后评价积分 + 投诉扣分 + 入驻年数积分
     */
    private Integer creditPoint;

    /**
     * 交易评价信用积分
     */
    private Integer tradeCommentPoint;

    /**
     * 售后评价信用积分
     */
    private Integer afterSaleCommentPoint;

    /**
     * 投诉扣分
     */
    private Integer complainPoint;

    /**
     * 注册年数
     */
    private Integer registerYears;

    /**
     * 入驻年数积分
     */
    private Integer registerYearsPoint;

    /**
     * 交易评价平均星级（总体满意度）
     */
    private Integer avgTradeCommentStar;
}
