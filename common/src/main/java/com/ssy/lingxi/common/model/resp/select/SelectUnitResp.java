package com.ssy.lingxi.common.model.resp.select;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 单位下拉框实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/30
 */
@Data
@Deprecated
public class SelectUnitResp {
    /**
     * id
     */
    @NotEmpty(message = "id不能为空")
    private Long id;

    /**
     * 名称
     */
    @NotEmpty(message = "名称不能为空")
    private String name;

    /**
     * 英文简称
     */
    @NotEmpty(message = "英文简称不能为空")
    private String englishShortName;
}
