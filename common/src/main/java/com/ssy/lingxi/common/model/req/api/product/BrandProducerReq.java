package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 品牌实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BrandProducerReq extends ApiBaseReq implements Serializable {
    private static final long serialVersionUID = -8347044449669659281L;

    /**
     * 品牌编码
     */
    @NotEmpty(message = "品牌编码不能为空")
    @Length(max = 32,message = "品牌编码最大字符长度32")
    private String code;

    /**
     * 品牌名称
     */
    @NotEmpty(message = "品牌名称不能为空")
    @Length(max = 32,message = "品牌名称最大字符长度32")
    private String name;

    /**
     * 品牌英文名称
     */
    @Length(max = 32,message = "品牌英文名称最大字符长度32")
    private String englishName;

    /**
     * 品牌logo
     */
    @Length(max = 250,message = "品牌logo最大字符长度250")
    private String logoUrl;

    /**
     * 是否有效(false:否,true:是)
     */
    @NotNull(message = "是否有效不能为空")
    private Boolean isEnable;

}
