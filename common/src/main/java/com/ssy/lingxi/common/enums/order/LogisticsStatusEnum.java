package com.ssy.lingxi.common.enums.order;

import lombok.Getter;

/**
 * 物流状态类型
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/26
 */
@Getter
public enum LogisticsStatusEnum {

    /**
     * 已下单
     */
    ORDERED(1, "已下单"),

    /**
     * 已上传凭证，待商家确认
     */
    UPLOAD_INVALID(2, "已上传凭证，待商家确认"),

    /**
     * 已上传凭证无效，待重新支付
     */
    UPLOAD_VALID(3, "已上传凭证无效，待重新支付"),

    /**
     * 商家已确认支付凭证
     */
    MERCHANT_CONFIRMED(4, "商家已确认支付凭证"),

    /**
     * 支付成功，待商家确认
     */
    PAYMENT_SUCCESS(5, "支付成功，待商家确认"),

    /**
     * 商家已确认，待拣货
     */
    MERCHANT_CONFIRMED_PICKING(6, "商家已确认，待拣货"),

    /**
     * 拣货完成，待打包
     */
    PICKING_COMPLETED(7, "拣货完成，待打包"),

    /**
     * 打包完成，待发货
     */
    PACKAGING_COMPLETED(8, "打包完成，待发货"),

    /**
     * 已发货，待快递揽收
     */
    SHIPPED_WAITING_PICKUP(9, "已发货，待快递揽收"),

    /**
     * 订货订单 - 商家已确认，待生产
     */
    MERCHANT_CONFIRMED_PRODUCTION(10, "商家已确认，待生产"),

    /**
     * 订货订单 - 已完成生产，待支付尾款
     */
    PRODUCTION_COMPLETED(11, "已完成生产，待支付尾款"),

    /**
     * 订货订单 - 尾款支付成功，待拣货
     */
    TAIL_PAYMENT_SUCCESS(12, "尾款支付成功，待拣货"),

    /**
     * 订货订单 - 定金支付成功，待商家确认
     */
    DEPOSIT_PAYMENT_SUCCESS(13, "定金支付成功，待商家确认")
    ;

    private final Integer code;
    private final String message;

    LogisticsStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 根据code获取对应的名称
     * @param code 状态码
     * @return message
     */
    public static String getMessageByCode(Integer code) {
        for (LogisticsStatusEnum status : LogisticsStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status.getMessage();
            }
        }
        return null; // 或者抛出异常
    }
}
