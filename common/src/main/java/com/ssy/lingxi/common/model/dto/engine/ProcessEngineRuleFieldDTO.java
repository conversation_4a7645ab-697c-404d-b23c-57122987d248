package com.ssy.lingxi.common.model.dto.engine;

import lombok.Data;

import java.io.Serializable;

/**
 * 流程引擎规则字段
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-31
 **/
@Data
public class ProcessEngineRuleFieldDTO implements Serializable {

    private static final long serialVersionUID = 2592423117236121143L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 数据库字段编码
     */
    private String code;

    /**
     * 业务字段类型: 1-字符; 2-数字; 3-日期;
     */
    private Integer type;

    /**
     * 条件: 1(=); 2(!=); 3(>); 4(>=); 5(<); 6(<=); 7(包含); 8(不包含);
     * 当type=1时：1(=); 2(!=); 7(包含); 8(不包含);
     * 当type=2和3时：1(=); 2(!=); 3(>); 4(>=); 5(<); 6(<=);
     */
    private Integer condition;

    /**
     * 值
     */
    private String value;

    /**
     * 字段关系: 1-and; 2-or;
     */
    private Integer relation;

    /**
     * 是否查询所有(0.否 1.是)
     */
    private Integer isQueryAll;
}
