package com.ssy.lingxi.common.model.resp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 今日新增报表
 * <AUTHOR>
 * @since 2023/2/21
 * @version 3.0.0
 */
@Data
public class ReportTodayResp implements Serializable {
    private static final long serialVersionUID = -2577558679887054585L;

    /**
     * 今日新增数量
     */
    private long todayCount = 0;

    /**
     * 增长率(相比昨天同比增长率)
     * （今日新增订单-昨日新增订单）/ 昨日新增订单 * 100%
     */
    private BigDecimal rate = BigDecimal.ZERO;
}
