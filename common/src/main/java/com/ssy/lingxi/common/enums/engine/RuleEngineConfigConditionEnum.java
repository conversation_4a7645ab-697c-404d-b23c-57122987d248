package com.ssy.lingxi.common.enums.engine;

import java.util.Arrays;
import java.util.Objects;

/**
 * 规则引擎配置字段条件
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/26
 */
public enum RuleEngineConfigConditionEnum {
    EQUAL(1, "="),
    NOT_EQUAL(2, "!="),
    GREATER(3, ">"),
    GREATER_E(4, ">="),
    LESS(5, "<"),
    LESS_E(6, "<="),
    INCLUDE(7, "包含"),
    EXCLUDE(8, "不包含"),;

    private final Integer code;
    private final String message;

    RuleEngineConfigConditionEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessage(Integer code){
        return Arrays.stream(RuleEngineConfigConditionEnum.values()).filter(value -> Objects.equals(value.code, code)).map(RuleEngineConfigConditionEnum::getMessage).findFirst().orElse("");
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
