package com.ssy.lingxi.common.model.resp.engine;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 规则引擎配置字段关系(流程规则)
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/21
 */
@Getter
@Setter
public class RuleEngineFieldResp implements Serializable {

    private static final long serialVersionUID = 168555493362474147L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 处理会员角色id
     */
    private Long handleMemberRoleId;

    /**
     * 规则引擎配置字段id
     */
    private List<RuleEngineConfigFieldResp> ruleEngineConfigFields = new ArrayList<>();

    /**
     * 字段关系: 1-and; 2-or;
     */
    private Integer relation;

    /**
     * 添加字段
     * @param ruleEngineConfigField
     */
    public void addRuleEngineField(RuleEngineConfigFieldResp ruleEngineConfigField){
        this.ruleEngineConfigFields.add(ruleEngineConfigField);
    }

}
