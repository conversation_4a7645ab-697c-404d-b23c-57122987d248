package com.ssy.lingxi.common.model.req.api.product;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 商品描述图片对象
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/8/20
 */
@Data
public class CommodityRemarkImageProducerReq implements Serializable {
    private static final long serialVersionUID = -731331059840218815L;

    /**
     * 图片路径
     */
    @NotEmpty(message = "图片路径不能为空")
    @Length(max = 250,message = "图片路径最大字符长度250")
    private String url;

    /**
     * 图片类型: 1-外部链接; 2-内部链接;
     */
    private Integer linkType = 1;

    /**
     * 图片链接
     */
    @Length(max = 250,message = "图片链接最大字符长度250")
    private String link;

    /**
     * 图片类型: 1-商品描述图片; 2-厂商资质图片; 3-商品检测报告图片; 4-商品详情头部图片；5-商品详情尾部图片
     */
    private Integer imageType = 1;
}
