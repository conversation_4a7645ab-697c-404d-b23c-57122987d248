package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 属性值实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AttributeProducerReq extends ApiBaseReq implements Serializable {
    private static final long serialVersionUID = -4844971025939948295L;

    /**
     * 属性编码
     */
    @NotEmpty(message = "属性编码不能为空")
    @Length(max = 32,message = "属性编码最大字符长度32")
    private String code;

    /**
     * 属性名称
     */
    @NotEmpty(message = "属性名称不能为空")
    @Length(max = 32,message = "属性名称最大字符长度32")
    private String name;

    /**
     * 属性组名称
     */
    private String groupName = "默认";

    /**
     * 属性类型：1-单选、2-多选、3-输入
     */
    @NotNull(message = "属性类型不能为空")
    private Integer type;

    /**
     * 是否启用
     */
    @NotNull(message = "是否启用不能为空")
    private Boolean isEnable;

    /**
     * 是否必填
     */
    @NotNull(message = "是否必填不能为空")
    private Boolean isMust;

    /**
     * 是否价格属性
     */
    @NotNull(message = "是否价格属性不能为空")
    private Boolean isPrice;

    /**
     * 是否搜索属性
     */
    @NotNull(message = "是否搜索属性不能为空")
    private Boolean isSearch;

    /**
     * 品类编码
     */
    @NotEmpty(message = "品类编码不能为空")
    @Length(max = 32,message = "品类编码最大字符长度32")
    private String categoryCode;

    /**
     * 属性值集合
     */
    @Valid
    private List<AttributeValueProducerReq> attributeValueList;
}
