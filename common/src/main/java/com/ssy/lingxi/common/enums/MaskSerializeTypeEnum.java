package com.ssy.lingxi.common.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 敏感信息枚举类
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/7/20
 */
@Getter
public enum MaskSerializeTypeEnum {
    /**
     * 自定义
     */
    CUSTOMER(1),

    /**
     * 用户名, 刘*华, 徐*
     */
    CHINESE_NAME(2),

    /**
     * 身份证号, 110110********1234
     */
    ID_CARD(3),

    /**
     * 座机号, ****1234
     */
    FIXED_PHONE(4),

    /**
     * 手机号, 176****1234
     */
    MOBILE_PHONE(5),

    /**
     * 地址, 北京********
     */
    ADDRESS(6),

    /**
     * 电子邮件, s*****<EMAIL>
     */
    EMAIL(7),

    /**
     * 银行卡, 622202************1234
     */
    BANK_CARD(8),

    /**
     * 密码, 永远是 ******, 与长度无关
     */
    PASSWORD(9),

    /**
     * 密钥, 永远是 ******, 与长度无关
     */
    KEY(10),

    /**
     * 默认规则, 不加密
     */
    DEFAULT(11);


    private final Integer code;

    MaskSerializeTypeEnum(Integer code) {
        this.code = code;
    }

    public static MaskSerializeTypeEnum parseCode(Integer code) {
        return Arrays.stream(MaskSerializeTypeEnum.values())
                .filter(memberConfigTagEnum -> memberConfigTagEnum.getCode().equals(code))
                .findFirst()
                .orElse(DEFAULT);
    }
}
