package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 商品信息（数仓）
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-21
 */
@Getter
@Setter
public class SellProReq implements Serializable {
    private static final long serialVersionUID = 5845757587055570192L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 名称,100个字符(不论中英文)
     */
    private String name;

    /**
     * 编码，最大30个字符
     */
    private String code;

    /**
     * 主图信息,JSON数组，如 ["/group1/Hk2AKAeTtkAARErpcEBrA343.jpg","/group1/usmHk2AKAeTtkAARErpcEBsa8K21.jpg"]
     */
    private Object pic_url;

    /**
     * 视频
     */
    private String video_url;

    /**
     * 系列ID,如果有多级,则用 : 将各层系列ID拼接, 如  1:2
     */
    private String goods_series;

    /**
     * 公司id
     */
    private Integer company_id;

    /**
     * 一级品类ID
     */
    private String first_category;

    /**
     * 商品分类ID,从第二级开始,若有多级分类直接使用 : 拼接,比如 2:12:21
     */
    private String sub_category;

    /**
     * 店内分类ID,多级分类直接使用 : 拼接,如 15:19
     */
    private String inner_category;

    /**
     * 是否专销,0=是,1=不是，默认为1
     */
    private Integer solely;

    /**
     * 渠道类型 总部:HQ 网店(online store):OS  多个渠道以逗号拼接 例如: HQ,OS
     */
    private String channel_type;

    /**
     * 专销--专销特有类型 默认为空
     */
    private String solely_type;

    /**
     * 营销标签,如果有多个,以 , 拼接,修改之后直接覆盖即可
     */
    private String sales_label;

    /**
     * 销售方式,如果有多个,以 : 拼接,修改时直接覆盖即可
     */
    private String sales_method;

    /**
     * 创建人名称,最大30个字符
     */
    private String creator;

    /**
     * 最后修改人
     */
    private String update_name;

    /**
     * 最后操作人姓名,最大30个字符
     */
    private String final_operator;

    /**
     * 1=下架,2=上架 3=删除
     */
    private Integer status;

    /**
     * 合计库存
     */
    private Integer total_stock;

    /**
     * 规格数
     */
    private Integer spec_num;

    /**
     * 成色,如果有多个,以 , 拼接,修改时直接覆盖即可
     */
    private String conditi;

    /**
     * 总销量
     */
    private Integer total_sales_volume;

    /**
     * 描述信息,最大200个字符
     */
    private String description;

    /**
     * 1:自建 2:草稿
     */
    private Integer source_type;

    /**
     * 状态原因
     */
    private String status_reason;

    /**
     * 营销品类
     */
    private String marketing_category;

    /**
     * 负责人
     */
    private Integer responsibility_user;

    /**
     * 标准产品编码
     */
    private String standard_product_code;

    /**
     * 自建 BUILT_IN 平台 SCM
     */
    private String goods_source;

    /**
     * 计价类型: GRAM=按克计费,BUY_NOW=一口价
     */
    private String charge_type;

    /**
     * 材质
     */
    private String material;

    /**
     * 材质编码
     */
    private String material_code;

    /**
     * 宝石分类
     */
    private String jewel;

    /**
     * 辅料分类
     */
    private String acc;

    /**
     * 是否镶嵌: 1=是,0=否
     */
    private Integer inlay_status;

    /**
     * 数据状态：正常 NORMAL 已变更 UPDATE
     */
    private String data_status;

    /**
     * 订货周期最小值
     */
    private String min_pro_period;

    /**
     * 订货周期最大值
     */
    private String max_pro_period;

    /**
     * 属性字符串
     */
    private String commodityAttributeList;

    /**
     * 商品属性列表
     */
    private List<SellProAttributeReq> attributeList;
}
