package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 数仓商品sku属性
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-27
 */
@Getter
@Setter
public class SellProSpecAttributeReq implements Serializable {
    private static final long serialVersionUID = -8016981402253667691L;

    /**
     * 属性id
     */
    private Integer attributeId;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 属性类型
     */
    private Integer attributeType;

    /**
     * 属性性值数组
     */
    private Integer attributeValueId;

    /**
     * 属性性值，多个用逗号隔开
     */
    private String attributeValueName;
}
