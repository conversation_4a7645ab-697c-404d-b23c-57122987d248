package com.ssy.lingxi.common.model.dto.engine;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 引擎返回结果
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-31
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EngineResultDTO implements Serializable {

    private static final long serialVersionUID = 5927649184057848503L;

    /**
     * 流程规则Id
     */
    private Long processRuleId;

    /**
     * 流程标识
     */
    private String processKey;

}
