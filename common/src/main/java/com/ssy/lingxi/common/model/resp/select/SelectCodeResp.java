package com.ssy.lingxi.common.model.resp.select;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 下拉框实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/30
 */
@Data
@Deprecated
public class SelectCodeResp {
    /**
     * 编号
     */
    @NotEmpty(message = "编号不能为空")
    private String code;

    /**
     * 名称
     */
    @NotEmpty(message = "名称不能为空")
    private String name;
}
