package com.ssy.lingxi.common.model.resp.engine;

import com.ssy.lingxi.common.model.dto.engine.RuleEngineFilterValueDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 规则引擎配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/21
 */
@Getter
@Setter
public class RuleEngineConfigResp implements Serializable {
    private static final long serialVersionUID = 809438539846578874L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 规则引擎类型：1-物料管理; 2-采购单流程; 3-合同管理; 4-合同协同; 5-请购单流程; 6-请款单管理;
     * 7-采购订单SRM; 8-售后管理B2B; 9-质量管理; 10-生命周期变更;
     * RuleEngineTypeEnum枚举类
     */
    private Integer type;

    /**
     * 流程ID
     */
    private String processId;

    /**
     * 流程名称
     */
    private String processName;

    /**
     * 流程步骤
     */
    private Integer processStep;

    /**
     * 流程步骤名称
     */
    private String processStepName;

    /**
     * 处理会员角色id
     */
    private Long handleMemberRoleId;

    /**
     * 状态 0.禁用 1.启用
     */
    private Integer state;

    /**
     * 来源 1.页面新增 1.接口新增
     */
    private Integer source;

    /**
     * 流程规则
     */
    private List<RuleEngineConfigFieldRelationResp> ruleEngineConfigFieldRelationList;

    /**
     * 过滤值
     */
    private List<RuleEngineFilterValueDTO> filterValueList = new ArrayList<>();

    /**
     * 处理用户
     */
    private List<Long> users;
}
