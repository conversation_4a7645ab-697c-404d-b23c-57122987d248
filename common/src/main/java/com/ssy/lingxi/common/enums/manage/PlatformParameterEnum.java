package com.ssy.lingxi.common.enums.manage;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 平台参数配置实体类-参数编码枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PlatformParameterEnum {

    /**
     * 订单取消时间
     */
    ORDER_CANCEL_TIME("A01", "订单取消时间"),
    /**
     * 积分兑换比率
     */
    INTEGRAL_EXCHANGE_RATE("A02", "积分兑换比率"),
    /**
     * 佣金比例
     */
    COMMISSION_RATE("A03", "佣金比例"),
    /**
     * 交易评价时间
     */
    TRADE_EVALUATE_TIME("A04", "交易评价时间"),
    /**
     * 商家审核评价内容
     */
    MERCHANT_AUDIT_EVALUATE_CONTENT("A07", "商家审核评价内容"),
    /**
     * 移动端默认商城
     */
    MOBILE_DEFAULT_SHOP("A09", "移动端默认商城"),
    /**
     * 系统品牌标识
     */
    SYSTEM_BRAND_IDENTIFICATION("A10", "系统品牌标识"),
    /**
     * 系统页面版权信息
     */
    SYS_PAGE_COPYRIGHT_INFO("A11", "系统页面版权信息"),

    /**
     * 字印承诺书模板
     */
    ZY_COMMITMENT_TEMPLATE("A12", "字印承诺书模板"),
    ;

    private final String code;
    private final String name;


}
