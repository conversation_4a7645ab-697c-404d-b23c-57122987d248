package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * 商品证件表
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/9/18
 */
@Getter
@Setter
public class CommodityLicenseReq implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 证照名称
     */
    @Length(max = 120,message = "证照名称最大字符长度120")
    private String licenseName;
    /**
     * 证照类型
     */
    @Length(max = 32,message = "证照类型最大字符长度32")
    private String licenseType;


    /**
     * 证照有效期开始时间
     */
    @Length(max = 32,message = "证照有效期开始时间最大字符长度32")
    private String licenseStartTime;

    /**
     * 证照有效期结束时间
     */
    @Length(max = 32,message = "证照有效期结束时间最大字符长度32")
    private String licenseEndTime;

    /**
     * 资质附件
     */
    private String[] licensePic;
}
