package com.ssy.lingxi.common.model.req.api.member;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 客户工费优惠表体明细Req（入参）
 */
@Data
public class CustomerProcessFeeDiscountBtSyncReq implements Serializable {
    /**
     * 单据流水号（组合主键之一）
     */
    @NotNull(message = "表体明细：单据流水号不能为空")
    private Long djlsh;
    /**
     * 单据表体号（组合主键之一）
     */
    @NotNull(message = "表体明细：单据表体号不能为空")
    private Integer djbth;
    /**
     * 行号
     */
    private Integer xh;
    /**
     * 物品分类
     */
    private String wpfl;
    /**
     * 克工费|参与优惠克工费区间|起始克工费(>)
     */
    private BigDecimal zxkgf;
    /**
     * 克工费|参与优惠克工费区间|截止克工费(<=)
     */
    private BigDecimal zdkgf;
    /**
     * 克工费|克工费优惠(每克减)
     */
    private BigDecimal kgfyh;
    /**
     * 克工费|克工费折扣(每克折扣)
     */
    private BigDecimal kgfzk;
    /**
     * 件工费|参与优惠件工费区间|起始件工费(>)
     */
    private BigDecimal zxjgf;
    /**
     * 件工费|参与优惠件工费区间|截止件工费(<=)
     */
    private BigDecimal zdjgf;
    /**
     * 件工费|件工费优惠(每件减)
     */
    private BigDecimal jgfyh;
    /**
     * 件工费|件工费折扣(每件折扣)
     */
    private BigDecimal jgfzk;
    /**
     * 备注
     */
    private String bz;
}

