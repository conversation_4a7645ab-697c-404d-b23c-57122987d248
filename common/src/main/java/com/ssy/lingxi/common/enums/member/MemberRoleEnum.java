package com.ssy.lingxi.common.enums.member;

/**
 *  会员角色枚举
 * <AUTHOR>
 * @since 2021/6/15
 * @version 2.0.0
 */
public enum MemberRoleEnum {
    /**
     * 平台会员id与角色id
     */
    PLATFORM(1L, 1L);

    private final Long memberId;
    private final Long roleId;

    MemberRoleEnum(Long memberId, Long roleId) {
        this.memberId = memberId;
        this.roleId = roleId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public Long getRoleId() {
        return roleId;
    }
}
