package com.ssy.lingxi.common.model.req.api;

import com.ssy.lingxi.common.util.UUIDUtil;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 基础实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@Data
public class ApiBaseReq implements Serializable {
    private static final long serialVersionUID = -8578866583939260791L;

    /**
     * 唯一标识
     */
    @NotEmpty(message = "唯一标识不能为空")
    private String uniqueNo = UUIDUtil.randomUUID();

    /**
     * 时间戳
     */
    @NotNull(message = "时间戳不能为空")
    private Long time = System.currentTimeMillis();

}
