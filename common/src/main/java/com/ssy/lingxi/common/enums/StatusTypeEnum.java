package com.ssy.lingxi.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 状态类型枚举
 * <AUTHOR>
 * @version 3.0.0
 * @since 2023/12/18
 */
@Getter
@AllArgsConstructor
public enum StatusTypeEnum {
    STOP(0, "无效"),
    START(1, "有效"),
    ;

    /**
     * 状态
     */
    private final Integer status;


    /**
     * 描述
     */
    private final String name;

    /**
     * 根据状态获取描述
     * @param status 状态
     * @return 描述
     */
    public static String getNameByCode(Integer status) {
        StatusTypeEnum statusTypeEnum = Arrays.stream(StatusTypeEnum.values()).filter(item -> item.getStatus().equals(status)).findFirst().orElse(null);
        return statusTypeEnum != null ? statusTypeEnum.getName() : "-";
    }
}
