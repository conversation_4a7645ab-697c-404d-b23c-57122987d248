package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 数仓商品属性
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-27
 */
@Getter
@Setter
public class SellProAttributeReq implements Serializable {
    private static final long serialVersionUID = 3579044810224387169L;

    /**
     * 属性id
     */
    private Integer attributeId;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 属性性值数组
     */
    private List<Integer> attributeValueIds;

    /**
     * 属性性值，多个用逗号隔开
     */
    private String attributeValueName;
}
