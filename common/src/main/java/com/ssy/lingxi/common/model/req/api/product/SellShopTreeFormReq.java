package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 商品分类，店内分类，系列
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-20
 */
@Getter
@Setter
public class SellShopTreeFormReq implements Serializable {
    private static final long serialVersionUID = -1588410171376135364L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 节点名称
     */
    private String node_name;

    /**
     * 排序,排序值越小,越靠前
     */
    private String sort_index;

    /**
     * 节点类型,品类,系列,店内分类
     */
    private String node_type;

    /**
     * 父节点ID
     */
    private Integer parent_node_id;

    /**
     * 级联ID,如果是根节点,为自身ID+: ,如果是子级节点,则为父级联ID+当前节点ID+:
     */
    private String joint_id;
}
