package com.ssy.lingxi.common.constant.mq;

import java.io.Serializable;

/**
 * 消息队列常量类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class SettlementMqConstant implements Serializable {
    private static final long serialVersionUID = 7463099513998867463L;

    /**
     * 结算服务标准通知队列
     */
    public static final String SETTLE_ACCOUNT_BASE_QUEUE = "settle_account_base_queue";
    public static final String SETTLE_ACCOUNT_BASE_EXCHANGE = "settle_account_base_exchange";
    public static final String SETTLE_ACCOUNT_BASE_ROUTING_KEY = "settle_account_base_routing_key";

    /**
     * 平台支付-通联支付-付款通知订单代收结算完成队列
     */
    public static final String SA_PLATFORM_SETTLEMENT_ALL_IN_PAY_EXCHANGE = "sa_platform_settlement_all_in_pay_exchange";
    public static final String SA_PLATFORM_SETTLEMENT_ALL_IN_PAY_QUEUE = "sa_platform_settlement_all_in_pay_queue";
    public static final String SA_PLATFORM_SETTLEMENT_ALL_IN_ROUTING_KEY = "sa_platform_settlement_all_in_routing_key";

    /**
     * 自动添加结算待对账订单-商品信息通知队列
     */
    public static final String SA_AUTO_RECONCILIATION_QUEUE = "sa_auto_reconciliation_queue";
    public static final String SA_AUTO_RECONCILIATION_EXCHANGE = "sa_auto_reconciliation_exchange";
    public static final String SA_AUTO_RECONCILIATION_ROUTINGKEY = "sa_auto_reconciliation_routingKey";
}
