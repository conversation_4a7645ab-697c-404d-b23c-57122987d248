package com.ssy.lingxi.common.constant;

import cn.hutool.core.text.CharPool;

import java.io.Serializable;

/**
 * 模块相关常量
 *
 * <AUTHOR>
 */
public class ServiceModuleConstant implements Serializable {
    private static final long serialVersionUID = -7975984841516878213L;

    /**
     * 服务
     */
    public static final String SERVICE = "service";

    /**
     * 内部feign
     */
    public static final String FEIGN = "feign";

    /**
     * 网关
     */
    public static final String GATEWAY = "gateway";

    /**
     * 售后
     */
    public static final String AFTERSALES = "aftersales";

    /**
     * 合同
     */
    public static final String CONTRACT = "contract";

    /**
     * 加工
     */
    public static final String ENHANCE = "enhance";

    /**
     * 物流
     */
    public static final String LOGISTICS = "logistics";

    /**
     * 营销
     */
    public static final String MARKETING = "marketing";

    /**
     * 会员
     */
    public static final String MEMBER = "member";

    /**
     * 支撑
     */
    public static final String SUPPORT = "support";

    /**
     * 订单
     */
    public static final String ORDER = "order";

    /**
     * 支付
     */
    public static final String PAY = "pay";

    /**
     * 后台管理
     */
    public static final String MANAGE = "manage";

    /**
     * 商品
     */
    public static final String PRODUCT = "product";

    /**
     * 采购
     */
    public static final String PURCHASE = "purchase";

    /**
     * 报表
     */
    public static final String REPORT = "report";

    /**
     * 引擎
     */
    public static final String ENGINE = "engine";

    /**
     * 结算
     */
    public static final String SETTLEMENT = "settlement";

    /**
     * 交易
     */
    public static final String TRADE = "trade";

    /**
     * 定时任务
     */
    public static final String SCHEDULER = "scheduler";

    /**
     * 工作流任务
     */
    public static final String WORKFLOW = "workflow";

    /**
     * api
     */
    public static final String API = "api";

    /**
     * 商品
     */
    public static final String COMMODITY = "commodity";

    /**
     * 网关服务
     */
    public static final String GATEWAY_SERVICE = GATEWAY + CharPool.DASHED + SERVICE;

    /**
     * 售后服务
     */
    public static final String AFTERSALES_SERVICE = AFTERSALES + CharPool.DASHED + SERVICE;

    /**
     * 合同服务
     */
    public static final String CONTRACT_SERVICE = CONTRACT + CharPool.DASHED + SERVICE;

    /**
     * 加工服务
     */
    public static final String ENHANCE_SERVICE = ENHANCE + CharPool.DASHED + SERVICE;

    /**
     * 物流服务
     */
    public static final String LOGISTICS_SERVICE = LOGISTICS + CharPool.DASHED + SERVICE;

    /**
     * 营销服务
     */
    public static final String MARKETING_SERVICE = MARKETING + CharPool.DASHED + SERVICE;

    /**
     * 会员服务
     */
    public static final String MEMBER_SERVICE = MEMBER + CharPool.DASHED + SERVICE;

    /**
     * 支撑服务
     */
    public static final String SUPPORT_SERVICE = SUPPORT + CharPool.DASHED + SERVICE;

    /**
     * 订单服务
     */
    public static final String ORDER_SERVICE = ORDER + CharPool.DASHED + SERVICE;

    /**
     * 订单服务
     */
    public static final String PAY_SERVICE = PAY + CharPool.DASHED + SERVICE;

    /**
     * 后台管理服务
     */
    public static final String MANAGE_SERVICE = MANAGE + CharPool.DASHED + SERVICE;

    /**
     * 商品服务
     */
    public static final String PRODUCT_SERVICE = PRODUCT + CharPool.DASHED + SERVICE;

    /**
     * 采购服务
     */
    public static final String PURCHASE_SERVICE = PURCHASE + CharPool.DASHED + SERVICE;

    /**
     * 报表服务
     */
    public static final String REPORT_SERVICE = REPORT + CharPool.DASHED + SERVICE;

    /**
     * 引擎服务
     */
    public static final String ENGINE_SERVICE = ENGINE + CharPool.DASHED + SERVICE;

    /**
     * 结算服务
     */
    public static final String SETTLEMENT_SERVICE = SETTLEMENT + CharPool.DASHED + SERVICE;

    /**
     * 交易服务
     */
    public static final String TRADE_SERVICE = TRADE + CharPool.DASHED + SERVICE;

    /**
     * 定时任务服务
     */
    public static final String SCHEDULER_SERVICE = SCHEDULER + CharPool.DASHED + SERVICE;

    /**
     * 工作流服务
     */
    public static final String WORKFLOW_SERVICE = WORKFLOW + CharPool.DASHED + SERVICE;

    /**
     * api服务
     */
    public static final String API_SERVICE = API + CharPool.DASHED + SERVICE;

    /**
     * 商品服务接口服务
     */
    public static final String COMMODITY_SERVICE = COMMODITY + CharPool.DASHED + SERVICE;

    /**
     * 售后服务接口路径前缀
     */
    public static final String AFTERSALES_PATH_PREFIX = CharPool.SLASH + AFTERSALES;

    /**
     * 合同服务接口路径前缀
     */
    public static final String CONTRACT_PATH_PREFIX = CharPool.SLASH + CONTRACT;

    /**
     * 加工服务接口路径前缀
     */
    public static final String ENHANCE_PATH_PREFIX = CharPool.SLASH + ENHANCE;

    /**
     * 物流服务接口路径前缀
     */
    public static final String LOGISTICS_PATH_PREFIX = CharPool.SLASH + LOGISTICS;

    /**
     * 营销服务接口路径前缀
     */
    public static final String MARKETING_PATH_PREFIX = CharPool.SLASH + MARKETING;

    /**
     * 会员服务接口路径前缀
     */
    public static final String MEMBER_PATH_PREFIX = CharPool.SLASH + MEMBER;

    /**
     * 支撑服务接口路径前缀
     */
    public static final String SUPPORT_PATH_PREFIX = CharPool.SLASH + SUPPORT;

    /**
     * 订单服务接口路径前缀
     */
    public static final String ORDER_PATH_PREFIX = CharPool.SLASH + ORDER;

    /**
     * 支付服务接口路径前缀
     */
    public static final String PAY_PATH_PREFIX = CharPool.SLASH + PAY;

    /**
     * 后台管理服务接口路径前缀
     */
    public static final String MANAGE_PATH_PREFIX = CharPool.SLASH + MANAGE;

    /**
     * 商品服务接口路径前缀
     */
    public static final String PRODUCT_PATH_PREFIX = CharPool.SLASH + PRODUCT;

    /**
     * 采购服务接口路径前缀
     */
    public static final String PURCHASE_PATH_PREFIX = CharPool.SLASH + PURCHASE;

    /**
     * 报表服务接口路径前缀
     */
    public static final String REPORT_PATH_PREFIX = CharPool.SLASH + REPORT;

    /**
     * 引擎服务接口路径前缀
     */
    public static final String ENGINE_PATH_PREFIX = CharPool.SLASH + ENGINE;

    /**
     * 结算服务接口路径前缀
     */
    public static final String SETTLEMENT_PATH_PREFIX = CharPool.SLASH + SETTLEMENT;

    /**
     * 交易服务接口路径前缀
     */
    public static final String TRADE_PATH_PREFIX = CharPool.SLASH + TRADE;

    /**
     * api服务接口路径前缀
     */
    public static final String API_PATH_PREFIX = CharPool.SLASH + API;

    /**
     * 商品服务路径前缀
     */
    public static final String COMMODITY_PATH_PREFIX = CharPool.SLASH + COMMODITY;

    /**
     * 售后服务内部接口路径前缀
     */
    public static final String AFTERSALES_FEIGN_PATH_PREFIX = CharPool.SLASH + AFTERSALES + CharPool.SLASH + FEIGN;

    /**
     * 合同服务内部接口路径前缀
     */
    public static final String CONTRACT_FEIGN_PATH_PREFIX = CharPool.SLASH + CONTRACT + CharPool.SLASH + FEIGN;

    /**
     * 加工服务内部接口路径前缀
     */
    public static final String ENHANCE_FEIGN_PATH_PREFIX = CharPool.SLASH + ENHANCE + CharPool.SLASH + FEIGN;

    /**
     * 物流服务内部接口路径前缀
     */
    public static final String LOGISTICS_FEIGN_PATH_PREFIX = CharPool.SLASH + LOGISTICS + CharPool.SLASH + FEIGN;

    /**
     * 营销服务内部接口路径前缀
     */
    public static final String MARKETING_FEIGN_PATH_PREFIX = CharPool.SLASH + MARKETING + CharPool.SLASH + FEIGN;

    /**
     * 会员服务内部接口路径前缀
     */
    public static final String MEMBER_FEIGN_PATH_PREFIX = CharPool.SLASH + MEMBER + CharPool.SLASH + FEIGN;

    /**
     * 支撑服务内部接口路径前缀
     */
    public static final String SUPPORT_FEIGN_PATH_PREFIX = CharPool.SLASH + SUPPORT + CharPool.SLASH + FEIGN;

    /**
     * 订单服务内部接口路径前缀
     */
    public static final String ORDER_FEIGN_PATH_PREFIX = CharPool.SLASH + ORDER + CharPool.SLASH + FEIGN;

    /**
     * 支付服务内部接口路径前缀
     */
    public static final String PAY_FEIGN_PATH_PREFIX = CharPool.SLASH + PAY + CharPool.SLASH + FEIGN;

    /**
     * 后台管理服务内部接口路径前缀
     */
    public static final String MANAGE_FEIGN_PATH_PREFIX = CharPool.SLASH + MANAGE + CharPool.SLASH + FEIGN;

    /**
     * 商品服务内部接口路径前缀
     */
    public static final String PRODUCT_FEIGN_PATH_PREFIX = CharPool.SLASH + PRODUCT + CharPool.SLASH + FEIGN;

    /**
     * 采购服务内部接口路径前缀
     */
    public static final String PURCHASE_FEIGN_PATH_PREFIX = CharPool.SLASH + PURCHASE + CharPool.SLASH + FEIGN;

    /**
     * 报表服务内部接口路径前缀
     */
    public static final String REPORT_FEIGN_PATH_PREFIX = CharPool.SLASH + REPORT + CharPool.SLASH + FEIGN;

    /**
     * 引擎服务内部接口路径前缀
     */
    public static final String ENGINE_FEIGN_PATH_PREFIX = CharPool.SLASH + ENGINE + CharPool.SLASH + FEIGN;

    /**
     * 结算服务内部接口路径前缀
     */
    public static final String SETTLEMENT_FEIGN_PATH_PREFIX = CharPool.SLASH + SETTLEMENT + CharPool.SLASH + FEIGN;

    /**
     * 交易服务内部接口路径前缀
     */
    public static final String TRADE_FEIGN_PATH_PREFIX = CharPool.SLASH + TRADE + CharPool.SLASH + FEIGN;

    /**
     * 定时任务服务内部接口路径前缀
     */
    public static final String SCHEDULER_FEIGN_PATH_PREFIX = CharPool.SLASH + SCHEDULER + CharPool.SLASH + FEIGN;

    /**
     * 工作流服务内部接口路径前缀
     */
    public static final String WORKFLOW_FEIGN_PATH_PREFIX = CharPool.SLASH + WORKFLOW + CharPool.SLASH + FEIGN;

    /**
     * 商品服务内部接口路径前缀
     */
    public static final String COMMODITY_FEIGN_PATH_PREFIX = CharPool.SLASH + COMMODITY + CharPool.SLASH + FEIGN;
}
