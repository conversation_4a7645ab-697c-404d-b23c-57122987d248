package com.ssy.lingxi.common.enums;

import lombok.Getter;

/**
 * 数仓平台推送数据库表类型枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-17
 */
@Getter
public enum DataWarehousePlatformPushTableTypeEnum {

    SELL_SHOP_PARAMS("sell_shop_params", "参数管理(属性值)"),
    TYPE_MODE_MANAGE("type_mode_manage", "材质管理（金属材质管理，宝石分类管理，辅料分类管理）"),
    JEWEL_MATERIAL_MANAGE("jewel_material_manage", "宝石材质管理"),
    SELL_SHOP_TREE_FORM("sell_shop_tree_form", "商品分类，店内分类，系列"),
    SELL_PRO("sell_pro", "销售产品基础信息(商品信息)"),
    SELL_PRO_SPEC("sell_pro_spec", "销售产品规格信息(商品sku)"),
    CORPORATION("c_company_base", "主客户(企业)"),
    CUSTOMER("c_company_body", "子客户(会员)"),
    SELL_PRO_TEXT("sell_pro_text", "商品描述富文本"),
    SELL_EXTRA_DATA_PARAMS("sell_extra_data_params", "第三方参数表"),
    SELL_EXTRA_DATA_PARAMS_RECORD("sell_extra_data_params_record", "第三方参数记录表")
    ;

    /**
     * 表名
     */
    private final String tableName;

    /**
     * 描述
     */
    private final String describe;

    DataWarehousePlatformPushTableTypeEnum(String tableName, String describe) {
        this.tableName = tableName;
        this.describe = describe;
    }
}
