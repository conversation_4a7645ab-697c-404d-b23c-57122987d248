package com.ssy.lingxi.common.model.req.api;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 错误记录实体类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@Getter
@Setter
public class ApiDataRecordReq implements Serializable {
    private static final long serialVersionUID = 7140014535656598122L;

    /**
     * 数据类型：1=api接口数据
     */
    private Integer dataType=1;

    /**
     * 接口名称
     */
    private String apiName;

    /**
     * 接口url
     */
    private String apiUrl;

    /**
     * 接口类型: 1-调用方; 2-被调用方; 3-系统错误;
     * @see  ApiTypeEnum
     */
    private Integer type = 1;

    /**
     * 错误消息唯一值
     */
    private String uniqueNo;

    /**
     * 消息内容
     */
    private String body;

}
