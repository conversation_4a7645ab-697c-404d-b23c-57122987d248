package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 数仓商品批量推送
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-27
 */
@Getter
@Setter
public class SellProBatchReq implements Serializable {
    private static final long serialVersionUID = 3011467721975168812L;

    /**
     * 商品列表
     */
    @NotEmpty(message = "batch对象不能为空")
    private List<Object> batch;

    /**
     * 商品总数
     */
    private Integer size;
}
