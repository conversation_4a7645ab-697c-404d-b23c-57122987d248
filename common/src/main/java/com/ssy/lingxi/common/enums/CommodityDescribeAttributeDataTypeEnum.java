package com.ssy.lingxi.common.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 商品描述属性数据类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-15
 */
@Getter
public enum CommodityDescribeAttributeDataTypeEnum {

    /**
     * 0、无
     */
    NOT(0, "无", "无"),

    /**
     * 1、金属材质管理
     */
    METAL(1, "金属材质管理", "MATERIAL"),

    /**
     * 2、宝石分类管理
     */
    JEWEL_CLASSIFY(2, "宝石分类管理", "JEWEL"),

    /**
     * 3、辅料分类管理
     */
    SUBSIDIARY_MATERIAL(3, "辅料分类管理", "AUX"),

    /**
     * 4、宝石材质管理
     */
    JEWEL_MATERIAL(4, "宝石材质管理", "无"),

    /**
     * 5、系列管理
     */
    SERIES(5, "系列管理", "SERIES"),

    /**
     * 6、店内分类管理
     */
    IN_STORE_CLASSIFY(6, "店内分类管理", "INNER_CATEGORY"),

    /**
     * 7、营销品类管理
     */
    MARKETING_CATEGORY(7, "营销品类管理", "MARKETING_CATEGORY");

    private final Integer code;
    private final String name;
    /**
     * 数仓平台接口属性值类型
     */
    private final String apiType;

    CommodityDescribeAttributeDataTypeEnum(Integer code, String name, String apiType) {
        this.code = code;
        this.name = name;
        this.apiType = apiType;
    }

    public static Integer getCodeByApiType(String apiType){
        CommodityDescribeAttributeDataTypeEnum typeEnum = Arrays.stream(CommodityDescribeAttributeDataTypeEnum.values()).filter(item -> item.getApiType().equals(apiType)).findFirst().orElse(null);
        return typeEnum != null ? typeEnum.getCode() : 0;
    }
}
