package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 数仓平台仓库批量同步
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-23
 */
@Getter
@Setter
public class WarehouseBatchSyncReq implements Serializable {
    private static final long serialVersionUID = -3661881669950755471L;

    /**
     * 仓库信息
     */
    @Valid
    @NotEmpty(message = "仓库数组不能为空")
    private List<WarehouseDataSyncReq> warehouseList;
}
