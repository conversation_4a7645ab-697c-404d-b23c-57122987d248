package com.ssy.lingxi.common.enums.product;

/**
 * 状态
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/29
 */
public enum BrandStatusEnum {

    NOT_SUBMITTED(1, "待提交审核"),
    NOT_CHECK(2, "待审核"),
    NOT_PASS(3, "审核不通过"),
    PASS(4, "审核通过");

    private final Integer code;
    private final String message;

    BrandStatusEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
