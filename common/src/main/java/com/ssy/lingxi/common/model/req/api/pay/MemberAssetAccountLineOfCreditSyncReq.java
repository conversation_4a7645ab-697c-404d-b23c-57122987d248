package com.ssy.lingxi.common.model.req.api.pay;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 会员账户信息同步请求参数-第三方调用
 *
 * @version 3.0.0
 * @autho zhuzhenxuan
 * @since 2025/5/22
 */
@Data
public class MemberAssetAccountLineOfCreditSyncReq {

    /**
     * 单据流水号（主键）
     */
    //@NotNull(message = "{CommonIdReq.id.NotNull}")
    //private Integer djlsh;

    /**
     * 主客户编码
     */
    @NotBlank(message = "主客户编码，不能为空")
    private String zkhbm;

    /**
     * 主客户名称
     */
    private String zkhmc;

    /**
     * 授信金料（公斤）
     */
    @NotNull(message = "授信金料，不能为空")
    private BigDecimal sxjl;

    /**
     * 授信截止时间
     */
    @NotBlank(message = "授信截止时间，不能为空")
    private String sxjzsj;

}
