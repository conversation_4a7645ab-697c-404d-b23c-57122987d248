package com.ssy.lingxi.common.model.req.api.order;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/6/11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderSettleReq  extends ApiBaseReq implements Serializable {

    /**
     * 订单号（销售单号）
     */
    @NotEmpty(message = "销售订单号不能为空")
    private String orderNo;

    /**
     * 结算状态(是否成功)
     */
    @NotNull(message = "结算状态不能为空")
    private Boolean status;

}
