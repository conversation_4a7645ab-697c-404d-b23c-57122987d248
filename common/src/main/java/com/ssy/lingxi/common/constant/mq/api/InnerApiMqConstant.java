package com.ssy.lingxi.common.constant.mq.api;

import java.io.Serializable;

/**
 * 消息队列常量类(被外部系统调用时用到)
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/29
 */
public class InnerApiMqConstant implements Serializable {
    private static final long serialVersionUID = 1550285993754492277L;

    /**
     * 品牌
     */
    public static final String API_PRODUCER_BRAND_QUEUE = "api_producer_brand_queue";
    public static final String API_PRODUCER_BRAND_EXCHANGE = "api_producer_brand_exchange";
    public static final String API_PRODUCER_BRAND_ROUTING_KEY = "api_producer_brand_routingKey";

    /**
     * 品类
     */
    public static final String API_PRODUCER_CATEGORY_QUEUE = "api_producer_category_queue";
    public static final String API_PRODUCER_CATEGORY_EXCHANGE = "api_producer_category_exchange";
    public static final String API_PRODUCER_CATEGORY_ROUTING_KEY = "api_producer_category_routingKey";

    /**
     * 属性值
     */
    public static final String API_PRODUCER_ATTRIBUTE_VALUE_QUEUE = "api_producer_attribute_value_queue";
    public static final String API_PRODUCER_ATTRIBUTE_VALUE_EXCHANGE = "api_producer_attribute_value_exchange";
    public static final String API_PRODUCER_ATTRIBUTE_VALUE_ROUTING_KEY = "api_producer_attribute_value_routingKey";

    /**
     * 商品信息
     */
    public static final String API_PRODUCER_COMMODITY_QUEUE = "api_producer_commodity_queue";
    public static final String API_PRODUCER_COMMODITY_EXCHANGE = "api_producer_commodity_exchange";
    public static final String API_PRODUCER_COMMODITY_ROUTING_KEY = "api_producer_commodity_routingKey";

    /**
     * 商品价格信息
     */
    public static final String API_PRODUCER_COMMODITY_PRICE_QUEUE = "api_producer_commodity_price_queue";
    public static final String API_PRODUCER_COMMODITY_PRICE_EXCHANGE = "api_producer_commodity_price_exchange";
    public static final String API_PRODUCER_COMMODITY_PRICE_ROUTING_KEY = "api_producer_commodity_price_routingKey";

    /**
     * 订单状态
     */
    public static final String API_PRODUCER_ORDER_STATUS_QUEUE = "api_producer_order_status_queue";
    public static final String API_PRODUCER_ORDER_STATUS_EXCHANGE = "api_producer_order_status_exchange";
    public static final String API_PRODUCER_ORDER_STATUS_ROUTING_KEY = "api_producer_order_status_routingKey";

    /**
     * 订单结算
     */
    public static final String API_PRODUCER_ORDER_SETTLEMENT_QUEUE = "api_producer_order_settlement_queue";
    public static final String API_PRODUCER_ORDER_SETTLEMENT_EXCHANGE = "api_producer_order_settlement_exchange";
    public static final String API_PRODUCER_ORDER_SETTLEMENT_ROUTING_KEY = "api_producer_order_settlement_routingKey";

    /**
     * 商品基础属性
     */
    public static final String API_COMMODITY_BASE_ATTRIBUTE_SYNC_QUEUE = "api_commodity_base_attribute_sync_queue";
    public static final String API_COMMODITY_BASE_ATTRIBUTE_SYNC_EXCHANGE = "api_commodity_base_attribute_sync_exchange";
    public static final String API_COMMODITY_BASE_ATTRIBUTE_SYNC_ROUTING_KEY = "api_commodity_base_attribute_sync_routing_key";

    /**
     * 商品基础属性值（数仓->商城）
     */
    public static final String API_COMMODITY_BASE_ATTRIBUTE_VALUE_QUEUE = "api_commodity_base_attribute_value_queue";
    public static final String API_COMMODITY_BASE_ATTRIBUTE_VALUE_EXCHANGE = "api_commodity_base_attribute_value_exchange";
    public static final String API_COMMODITY_BASE_ATTRIBUTE_VALUE_ROUTING_KEY = "api_commodity_base_attribute_value_routing_key";

    /**
     * 商品信息同步（数仓->商城）
     */
    public static final String API_COMMODITY_BASE_SYNC_QUEUE = "api_commodity_base_sync_queue";
    public static final String API_COMMODITY_BASE_SYNC_EXCHANGE = "api_commodity_base_sync_exchange";
    public static final String API_COMMODITY_BASE_SYNC_ROUTING_KEY = "api_commodity_base_sync_routing_key";

    /**
     * 商品sku信息同步（数仓->商城）
     */
    public static final String API_COMMODITY_SKU_SYNC_QUEUE = "api_commodity_sku_sync_queue";
    public static final String API_COMMODITY_SKU_SYNC_EXCHANGE = "api_commodity_sku_sync_exchange";
    public static final String API_COMMODITY_SKU_SYNC_ROUTING_KEY = "api_commodity_sku_sync_routing_key";

    /**
     * 属性信息同步（数仓->商城）
     */
    public static final String API_CUSTOMER_ATTRIBUTE_SYNC_QUEUE = "api_customer_attribute_sync_queue";
    public static final String API_CUSTOMER_ATTRIBUTE_SYNC_EXCHANGE = "api_customer_attribute_sync_exchange";
    public static final String API_CUSTOMER_ATTRIBUTE_SYNC_ROUTING_KEY = "api_customer_attribute_sync_routing_key";

    /**
     * 仓库同步（数仓->商城）
     */
    public static final String API_WAREHOUSE_SYNC_QUEUE = "api_warehouse_sync_queue";
    public static final String API_WAREHOUSE_SYNC_EXCHANGE = "api_warehouse_sync_exchange";
    public static final String API_WAREHOUSE_SYNC_ROUTING_KEY = "api_warehouse_sync_routing_key";

    /**
     * 库存同步（数仓->商城）
     */
    public static final String API_FREIGHT_SPACE_SYNC_QUEUE = "api_freight_space_sync_queue";
    public static final String API_FREIGHT_SPACE_SYNC_EXCHANGE = "api_freight_space_sync_exchange";
    public static final String API_FREIGHT_SPACE_SYNC_ROUTING_KEY = "api_freight_space_sync_routing_key";

    /**
     * 商品描述同步（数仓->商城）
     */
    public static final String API_COMMODITY_REMARK_SYNC_QUEUE = "api_commodity_remark_sync_queue";
    public static final String API_COMMODITY_REMARK_SYNC_EXCHANGE = "api_commodity_remark_sync_exchange";
    public static final String API_COMMODITY_REMARK_SYNC_ROUTING_KEY = "api_commodity_remark_sync_routing_key";

    /**
     * 商品与第三方参数关联记录同步（数仓->商城）
     */
    public static final String API_SELL_EXTRA_DATA_PARAMS_RECORD_SYNC_QUEUE = "api_sell_extra_data_params_record_sync_queue";
    public static final String API_SELL_EXTRA_DATA_PARAMS_RECORD_SYNC_EXCHANGE = "api_sell_extra_data_params_record_sync_exchange";
    public static final String API_SELL_EXTRA_DATA_PARAMS_RECORD_SYNC_ROUTING_KEY = "api_sell_extra_data_params_record_sync_routing_key";
}
