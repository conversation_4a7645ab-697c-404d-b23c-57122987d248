package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * 材质管理入参
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-17
 */
@Getter
@Setter
public class TypeModeManageReq implements Serializable {
    private static final long serialVersionUID = -6144510272782375027L;

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 类型: 金属材质:MATERIAL,宝石分类:JEWEL,辅件分类:AUX,计量单位:METERAGE
     */
    private String type;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态: 1=启用,0=禁用
     */
    private Integer status;

    /**
     * 创建人名称,最大30个字符
     */
    private String creator_name;

    /**
     * 更新人名称
     */
    private String update_name;

    /**
     * 对象是否为空
     */
    private Boolean isEmpty;

    public Boolean getEmpty() {
        return StringUtils.isBlank(this.code) || StringUtils.isBlank(this.name) || StringUtils.isBlank(this.type);
    }

}
