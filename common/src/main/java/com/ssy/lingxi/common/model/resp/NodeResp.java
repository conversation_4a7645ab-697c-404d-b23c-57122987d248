package com.ssy.lingxi.common.model.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * 树节点实体
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NodeResp {
    /**
     * id
     */
    @NotEmpty
    private String id;

    /**
     * 父节点id
     */
    @NotEmpty
    private String parentId;

    /**
     * 节点名称
     */
    @NotEmpty
    private String name;

    /**
     * 是否选中
     */
    @NotEmpty
    private Boolean checked = false;

    /**
     * 图片url路径
     */
    private String imageUrl;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 子节点集合
     */
    private List<NodeResp> children = new ArrayList<>();
}
