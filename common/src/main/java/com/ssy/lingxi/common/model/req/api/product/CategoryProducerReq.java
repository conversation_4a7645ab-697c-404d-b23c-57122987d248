package com.ssy.lingxi.common.model.req.api.product;

import com.ssy.lingxi.common.model.req.api.ApiBaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 品类实体类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/8/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CategoryProducerReq extends ApiBaseReq implements Serializable {
    private static final long serialVersionUID = -9122771279317902666L;

    /**
     * 品类名称
     */
    @NotEmpty(message = "品类名称不能为空")
    @Length(max = 32,message = "品类名称最大字符长度32")
    private String name;

    /**
     * 品类编码
     */
    @NotEmpty(message = "品类编码不能为空")
    @Length(max = 32,message = "品类编码最大字符长度32")
    private String code;

    /**
     * 品类图片url路径
     */
    @Length(max = 250,message = "品类图片url路径最大字符长度250")
    private String imageUrl;

    /**
     * 等级
     */
    @NotNull(message = "等级不能为空")
    private Integer level;

    /**
     * 父编码
     */
    @Length(max = 32,message = "父编码最大字符长度32")
    private String parentCode;

    /**
     * 类型：1-实物商品(默认)、2-虚拟商品、3-服务商品、4-积分兑换商品
     */
    private Integer type = 1;

    /**
     * 排序
     */
    private Integer sort;

}
