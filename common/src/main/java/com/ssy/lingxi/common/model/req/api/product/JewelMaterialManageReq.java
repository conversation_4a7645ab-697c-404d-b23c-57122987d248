package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 宝石材质管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-20
 */
@Getter
@Setter
public class JewelMaterialManageReq implements Serializable {
    private static final long serialVersionUID = 2108589128338870546L;

    /**
     * 主键ID（数仓）
     */
    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 宝石分类名称
     */
    private String jewel_category_name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态: 1=启用,0=禁用
     */
    private Integer status;

    /**
     * 创建人名称,最大30个字符
     */
    private String creator_name;

    /**
     * 最后更新人
     */
    private String update_name;
}
