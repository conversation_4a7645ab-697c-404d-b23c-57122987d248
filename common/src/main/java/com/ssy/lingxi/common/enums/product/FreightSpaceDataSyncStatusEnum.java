package com.ssy.lingxi.common.enums.product;

import lombok.Getter;

/**
 * 接口平台库存状态
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-30
 */
@Getter
public enum FreightSpaceDataSyncStatusEnum {

    FREIGHT_SPACE("库存", "单件商品还在该库存里"),
    SALE("销售", "单件商品已被销售"),
    CANCEL("作废", "单件商品数据被作废");

    private final String status;
    private final String describe;

    FreightSpaceDataSyncStatusEnum(String status, String describe) {
        this.status = status;
        this.describe = describe;
    }
}
