package com.ssy.lingxi.common.enums.order;

import java.util.Arrays;

/**
 * 订单来源（种类）枚举
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-02
 */
public enum OrderSourceKindEnum {
    /**
     * 采购订单 - 1
     */
    BUYER(1, "采购订单"),

    /**
     * SRM订单 - 2
     */
    SRM(2, "SRM订单"),

    /**
     * B2B订单 - 3
     */
    B2B(3, "B2B订单"),

    /**
     * 移动端采购订单 - 4
     */
    MOBILE_BUYER(4, "移动端采购订单"),

    /**
     * 积分兑换订单 - 5
     */
    RIGHT_POINT(5, "积分订单"),

    /**
     * 代客订单 - 6
     */
    AGENT(6, "代客订单"),

    /**
     * 采购请购单 - 7
     */
    REQUISITION(7, "采购请购单"),

    /**
     * 移动端积分兑换订单 - 8
     */
    MOBILE_RIGHT_POINT(8, "移动端积分订单"),

    /**
     * 拼团订单 - 9
     */
    GROUP(9, "拼团订单"),

    /**
     * 未知 - 99
     */
    UNKNOWN(99, "未知");

    OrderSourceKindEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据枚举值，获得名称
     * @param code 枚举值
     * @return 枚举名称
     */
    public static String getNameByCode(Integer code) {
        return Arrays.stream(OrderSourceKindEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(UNKNOWN).getName();
    }

    /**
     * 根据枚举之，获取枚举
     * @param code 枚举值
     * @return 枚举
     */
    public static OrderSourceKindEnum getEnumByCode(Integer code) {
        return Arrays.stream(OrderSourceKindEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(UNKNOWN);
    }
}
