package com.ssy.lingxi.common.model.req.engine;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 基础流程
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-27
 */
@Data
public class ProcessEngineReq implements Serializable {

    private static final long serialVersionUID = 4265341940110767311L;

    /**
     * 引擎ID
     */
    @NotNull(message = "引擎ID不能为空")
    private Long engineId;

    /**
     * 流程对应的工作流的key
     */
    @NotEmpty(message = "流程标识不能为空")
    private String processKey;

    /**
     * 流程名称
     */
    @NotEmpty(message = "流程名称不能为空")
    private String processName;

    /**
     * 流程类型
     */
    @NotNull(message = "流程类型不能为空")
    private Integer type;

    /**
     * 流程类型
     */
    @NotNull(message = "流程类型不能为空")
    private Integer processType;

    /**
     * 是否默认流程 0.非默认 1.默认
     */
    @NotNull(message = "是否默认不能为空")
    private Integer isDefault;

    /**
     * 流程图
     */
    @NotEmpty(message = "流程图不能为空")
    private String processImage;

    /**
     * 流程描述
     */
    private String description;

}
