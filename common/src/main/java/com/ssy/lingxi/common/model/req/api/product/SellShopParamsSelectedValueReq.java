package com.ssy.lingxi.common.model.req.api.product;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 数仓参数管理树形属性值对象
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-27
 */
@Getter
@Setter
public class SellShopParamsSelectedValueReq implements Serializable {
    private static final long serialVersionUID = -8795131282335647613L;

    private Integer ind;

    private String paramsName;

    private String parentName;

    private List<SellShopParamsSelectedValueReq> children;
}
