package com.ssy.lingxi.product.controller.pc.shop;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CommodityCollectBatchReq;
import com.ssy.lingxi.product.api.model.req.CommodityCollectReq;
import com.ssy.lingxi.product.api.model.resp.CollectResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.entity.do_.CommodityCollectDO;
import com.ssy.lingxi.product.entity.esCommodity.EsCommodity;
import com.ssy.lingxi.product.entity.esCommodity.EsCommodityShop;
import com.ssy.lingxi.product.entity.esCommodity.EsCommoditySoldCount;
import com.ssy.lingxi.product.model.resp.CommodityCollectResp;
import com.ssy.lingxi.product.repository.esCommodity.EsCommodityRepository;
import com.ssy.lingxi.product.service.feign.ICommodityFeignService;
import com.ssy.lingxi.product.service.shop.ICommodityCollectService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商城--商品收藏管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/2
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/commodityCollect")
public class CommodityCollectController extends BaseController {
    private final ICommodityCollectService commodityCollectService;
    private final EsCommodityRepository esCommodityRepository;

    private final ICommodityFeignService commodityFeignService;

    /**
     * 查询商品是否已经收藏
     * @param commodityId 商品id
     */
    @GetMapping("/getCommodityCollect")
    public WrapperResp<CollectResp> getCommodityCollect(@RequestParam Long commodityId) {
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(commodityCollectService.getCommodityCollectByCommodityId(commodityId, sysUser, shopId));
    }

    /**
     * 查询商品收藏列表
     * @return
     */
    @GetMapping("/getCommodityCollectList")
    public WrapperResp<PageDataResp<CommodityCollectResp>> getCommodityCollectList(PageDataReq pageDataReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<CommodityCollectDO> result = commodityCollectService.getCommodityCollectList(sysUser, pageDataReq);
        List<CommodityCollectResp> resultList = result.getContent().stream().map(commodityCollect -> {
            CommodityCollectResp commodityCollectResp = new CommodityCollectResp();
            commodityCollectResp.setIsPublish(false);
            Long commodityId = commodityCollect.getCommodityId();
            Long commodityCollectId = commodityCollect.getId();
            Long createTime = commodityCollect.getCreateTime();
            Long shopId = commodityCollect.getShopId();
            commodityCollectResp.setId(commodityCollectId);
            commodityCollectResp.setShopId(shopId);
            commodityCollectResp.setCreateTime(createTime);
            EsCommodity commodity = esCommodityRepository.findById(commodityId).orElse(null);
            if(commodity != null){
                EsCommodityResp esCommodityResp = new EsCommodityResp();
                BeanUtil.copyProperties(commodity, esCommodityResp);

                esCommodityResp.setUnitName(commodityFeignService.getUnitName(commodity.getUnitId()));
                commodityCollectResp.setCommodity(esCommodityResp);

                //不同商城显示不同的已售数量
                List<EsCommoditySoldCount> commoditySoldCountList = commodity.getCommoditySoldCountList();
                if(commoditySoldCountList != null && commoditySoldCountList.size() > 0){
                    commoditySoldCountList.forEach(commoditySoldCount -> {
                        Long commoditySoldCountShopId = commoditySoldCount.getShopId();
                        BigDecimal sold = commoditySoldCount.getSold();
                        if(commoditySoldCountShopId.equals(shopId)){
                            esCommodityResp.setSold(sold);
                        }
                    });
                }

                //判断会员商品是否上架
                List<EsCommodityShop> commodityShopList = commodity.getCommodityShopList();
                if(commodityShopList != null && commodityShopList.size() > 0){
                    List<Long> shopIdList = commodityShopList.stream().map(EsCommodityShop::getShopId).collect(Collectors.toList());
                    if(shopIdList.contains(shopId)){
                        commodityCollectResp.setIsPublish(true);
                    }
                }
            }else{
                return null;
            }
            return commodityCollectResp;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 新增商品收藏
     * @param commodityCollectReq 商品收藏实体
     * @return 商品收藏id
     */
    @PostMapping("/saveCommodityCollect")
    public WrapperResp<Long> saveCommodityCollect(@RequestBody CommodityCollectReq commodityCollectReq){
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(commodityCollectService.saveCommodityCollect(sysUser, shopId, commodityCollectReq));
    }

    /**
     * 批量新增商品收藏
     * @param simpleCommodityIdListRequest 商品收藏实体
     * @return
     */
    @PostMapping("/saveCommodityCollectBatch")
    public WrapperResp<Boolean> saveCommodityCollectBatch(@RequestBody CommodityCollectBatchReq simpleCommodityIdListRequest){
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(commodityCollectService.saveBatchCommodityCollect(sysUser, shopId, simpleCommodityIdListRequest));
    }

    /**
     * 删除商品收藏
     * @param commodityCollectReq
     * @return
     */
    @PostMapping("/deleteCommodityCollect")
    public WrapperResp<String> deleteCommodityCollect(@RequestBody CommodityCollectReq commodityCollectReq){
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(commodityCollectService.deleteCommodityCollect(sysUser, shopId, commodityCollectReq));
    }

    /**
     * 删除商品收藏通过id
     * @param commonIdRequest 商品收藏id
     * @return
     */
    @PostMapping("/deleteCommodityCollectById")
    public WrapperResp<String> deleteCommodityCollectById(@RequestBody CommonIdReq commonIdRequest){
        UserLoginCacheDTO sysUser = this.getSysUser();
        Long id = commonIdRequest.getId();
        return WrapperUtil.success(commodityCollectService.deleteCommodityCollectById(sysUser, id));
    }

}
