package com.ssy.lingxi.product.controller.pc.warehouse;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.warehouse.InvoicesTypeAddOrUpdateReq;
import com.ssy.lingxi.product.api.model.req.warehouse.InvoicesTypeListReq;
import com.ssy.lingxi.product.api.model.req.warehouse.InvoicesTypeStateUpdateReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.InvoicesTypeListResp;
import com.ssy.lingxi.product.service.warehouse.IInvoicesTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 进销存-单据类型管理
 * <AUTHOR>
 * @since 2020/7/20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX)
public class InvoicesTypeController extends BaseController {
    private final IInvoicesTypeService invoicesTypeService;

    /**
     * 单据类型列表查询
     * @param request
     * @return
     */
    @GetMapping("/invoices/type/list")
    public WrapperResp<PageDataResp<InvoicesTypeListResp>> invoicesTypeList(InvoicesTypeListReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(invoicesTypeService.invoicesTypeList(request,sysUser));
    }

    /**
     * 单据类型详情查询
     * @param id
     * @return
     */
    @GetMapping("/invoices/type/details")
    public WrapperResp<InvoicesTypeListResp> invoicesTypeDetails(Long id) {
        return WrapperUtil.success(invoicesTypeService.invoicesTypeDetails(id));
    }

    /**
     * 查询单据类型全部
     * @param httpServletRequest
     * @return
     */
    @GetMapping("/invoices/type/all")
    public WrapperResp<List<InvoicesTypeListResp>> invoicesTypeAll(HttpServletRequest httpServletRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser(httpServletRequest);
        return WrapperUtil.success(invoicesTypeService.invoicesTypeAll(sysUser));
    }

    /**
     * 单据类型删除
     * @param id
     * @return
     */
    @GetMapping("/invoices/type/delete")
    public WrapperResp<Void> invoicesTypeDelete(Long id) {
        return WrapperUtil.success(invoicesTypeService.invoicesTypeDelete(id));
    }

    /**
     * 单据类型增加/修改
     * @param request
     * @return
     */
    @PostMapping("/invoices/type/addOrUpdate")
    public WrapperResp<Void> invoicesTypeAddOrUpdate(@RequestBody InvoicesTypeAddOrUpdateReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(invoicesTypeService.invoicesTypeAddOrUpdate(request, sysUser));
    }

    /**
     * 单据类型停用&启用
     * @param request
     * @return
     */
    @PostMapping("/invoices/type/startOrStop")
    public WrapperResp<Void> invoicesTypeStartOrStop(@RequestBody InvoicesTypeStateUpdateReq request) {
        return WrapperUtil.success(invoicesTypeService.invoicesTypeStartOrStop(request));
    }
}
