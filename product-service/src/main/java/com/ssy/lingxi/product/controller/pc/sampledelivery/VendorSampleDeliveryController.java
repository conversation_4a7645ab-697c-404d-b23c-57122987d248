package com.ssy.lingxi.product.controller.pc.sampledelivery;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.model.req.sampledelivery.SampleDeliverySendReq;
import com.ssy.lingxi.product.model.req.sampledelivery.SampleDeliveryVendorPageDataReq;
import com.ssy.lingxi.product.model.req.sampledelivery.SampleDeliveryVendorToConfirmPageDataReq;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryDetailResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryVendorQueryResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryVendorToConfirmQueryResp;
import com.ssy.lingxi.product.service.sampleDelivery.IVendorSampleDeliveryService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 能力中心-商品能力-送样协同
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/23 13:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/sampleDeliver/vendor")
public class VendorSampleDeliveryController extends BaseController {
    private final IVendorSampleDeliveryService vendorSampleDeliveryService;

    /**
     * 送样需求单分页列表
     *
     * @param pageVO 分页查询传参
     * @return 分页列表
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<SampleDeliveryVendorQueryResp>> page(SampleDeliveryVendorPageDataReq pageVO) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(vendorSampleDeliveryService.page(pageVO, sysUser));
    }

    /**
     * 待确认寄样送样需求单列表
     *
     * @param pageVO 分页查询传参
     * @return 分页列表
     */
    @GetMapping("/pageToConfirm")
    public WrapperResp<PageDataResp<SampleDeliveryVendorToConfirmQueryResp>> pageToConfirm(SampleDeliveryVendorToConfirmPageDataReq pageVO) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(vendorSampleDeliveryService.pageToConfirm(pageVO, sysUser));
    }

    /**
     * 送样需求单详情
     *
     * @param commonIdReq id参数
     * @return 送样需求单详情
     */
    @GetMapping("/detail")
    public WrapperResp<SampleDeliveryDetailResp> detail(@Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(vendorSampleDeliveryService.detail(commonIdReq.getId(), sysUser));
    }

    /**
     * 确认寄样
     *
     * @param sampleDeliverySendReq 确认传参
     * @return 操作结果
     */
    @PostMapping("/confirmSend")
    public WrapperResp<Void> confirmSend(@RequestBody @Valid SampleDeliverySendReq sampleDeliverySendReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        vendorSampleDeliveryService.send(sampleDeliverySendReq, sysUser);
        return WrapperUtil.success();
    }
}
