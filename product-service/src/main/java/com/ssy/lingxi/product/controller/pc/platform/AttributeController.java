package com.ssy.lingxi.product.controller.pc.platform;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.AttributeReq;
import com.ssy.lingxi.product.api.model.req.SimpleStatusReq;
import com.ssy.lingxi.product.api.model.resp.AttributeResp;
import com.ssy.lingxi.product.api.model.resp.tree.AttributeNodeResp;
import com.ssy.lingxi.product.entity.do_.platform.AttributeDO;
import com.ssy.lingxi.product.model.req.CommodityAttributeReq;
import com.ssy.lingxi.product.model.resp.CommodityAttributeAndBrandResp;
import com.ssy.lingxi.product.service.platform.IAttributeService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台--属性管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/platform")
public class AttributeController extends BaseController {
    private final IAttributeService attributeService;

    /**
     * 查询属性树
     * @param filterInput 是否需要过滤输入类型
     * @return Wrapper<List<Node>>
     */
    @GetMapping("/getAttributeTree")
    public WrapperResp<List<AttributeNodeResp>> getAttributeTree(@RequestParam(value = "filterInput", required = false) Boolean filterInput) {
        return WrapperUtil.success(attributeService.getAttributeTree(filterInput));
    }
    
    /**
     * 查询属性列表
     * @param isEnable 是否有效
     * @param pageDataReq 分页实体
     * @param groupName 属性组名称
     * @param name 属性名称
     * @return
     */
    @GetMapping("/getAttributeList")
    public WrapperResp<PageDataResp<AttributeResp>> getAttributeList(PageDataReq pageDataReq, @RequestParam(value = "isEnable", required = false) Boolean isEnable, @RequestParam(value = "groupName", required = false) String groupName, @RequestParam(value = "name", required = false) String name) {
        Page<AttributeDO> result = attributeService.getAttributeList(pageDataReq, isEnable, groupName, name == null ? "" : name);
        List<AttributeResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<AttributeResp>>() {}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 获取可选的属性
     *
     * @param commodityAttributeReq
     * @return
     */
    @PostMapping("/getEffectiveAttribute")
    public WrapperResp<CommodityAttributeAndBrandResp> getEffectiveAttribute(@RequestBody @Valid CommodityAttributeReq commodityAttributeReq) {
        return WrapperUtil.success(attributeService.getEffectiveAttribute(commodityAttributeReq));
    }

    /**
     * 查询属性信息
     * @param id 属性id
     * @return Wrapper<Attribute>
     */
    @GetMapping("/getAttribute")
    public WrapperResp<AttributeResp> getAttribute(@RequestParam("id") Long id) {
        AttributeDO attribute = attributeService.getAttribute(id);
        if(attribute != null){
            return WrapperUtil.success(this.modelMapper.map(attribute, AttributeResp.class));
        }else{
            return WrapperUtil.success(null);
        }
    }

    /**
     * 新增/修改属性
     * @param attributeReq 属性实体
     * @return
     */
    @PostMapping("/saveOrUpdateAttribute")
    public WrapperResp<String> saveOrUpdateAttribute(@RequestBody AttributeReq attributeReq){
        return WrapperUtil.success(attributeService.saveOrUpdateAttribute(this.modelMapper.map(attributeReq, AttributeDO.class)));
    }

    /**
     * 删除属性
     * @param commonIdReq
     * @return
     */
    @PostMapping("deleteAttribute")
    public WrapperResp<String> deleteAttribute(@RequestBody CommonIdReq commonIdReq){
        return WrapperUtil.success(attributeService.deleteAttribute(commonIdReq.getId()));
    }

    /**
     * 启用/停用属性
     * @param simpleStatusReq
     * @return
     * @throws
     */
    @PostMapping("/updateAttributeStatus")
    public WrapperResp<String> updateAttributeStatus(@RequestBody SimpleStatusReq simpleStatusReq) {
        return WrapperUtil.success(attributeService.updateAttributeStatus(simpleStatusReq.getId(), simpleStatusReq.getIsEnable()));
    }
}
