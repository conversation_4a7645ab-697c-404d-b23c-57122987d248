package com.ssy.lingxi.product.controller.pc.warehouse;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseAddOrUpdateReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseStartRoStopReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseResp;
import com.ssy.lingxi.product.service.warehouse.IWarehouseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 进销存-仓库管理
 * <AUTHOR>
 * @since 2020/7/20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/warehouse")
public class WarehouseController extends BaseController {
    private final IWarehouseService warehouseService;

    /**
     * 查询仓库列表
     * @param request 参数
     */
    @GetMapping("/list")
    public WrapperResp<PageDataResp<WarehouseResp>> warehouseList(WarehouseReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseService.warehouseList(request,sysUser));
    }

    /**
     * 查询仓库详情
     * @param id 仓库id
     */
    @GetMapping("/details")
    public WrapperResp<WarehouseResp> warehouseDetails(Long id) {
        return WrapperUtil.success(warehouseService.warehouseDetails(id));
    }

    /**
     * 查询全部仓库
     */
    @GetMapping("/all")
    public WrapperResp<List<WarehouseResp>> warehouseAll() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseService.warehouseAll(sysUser));
    }

    /**
     * 添加/修改仓库
     * @param request 参数
     */
    @PostMapping("/addOrUpdate")
    public WrapperResp<Void> warehouseAddOrUpdate(@RequestBody @Valid WarehouseAddOrUpdateReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseService.warehouseAddOrUpdate(request,sysUser));
    }

    /**
     * 删除仓库
     * @param id 仓库id
     */
    @GetMapping("/delete")
    public WrapperResp<Void> warehouseDelete(Long id) {
        return WrapperUtil.success(warehouseService.warehouseDelete(id));
    }

    /**
     * 启动停用仓库
     * @param request 参数
     */
    @PostMapping("/startOrStop")
    public WrapperResp<Void> warehouseStartOrStop(@RequestBody WarehouseStartRoStopReq request) {
        return WrapperUtil.success(warehouseService.warehouseStartOrStop(request));
    }
}
