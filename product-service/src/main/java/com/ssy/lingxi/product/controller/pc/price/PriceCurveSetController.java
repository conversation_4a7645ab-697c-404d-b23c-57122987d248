package com.ssy.lingxi.product.controller.pc.price;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.PriceCurveSetBatchReq;
import com.ssy.lingxi.product.api.model.resp.PriceCurveSetResp;
import com.ssy.lingxi.product.service.IPriceCurveSetService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 价格曲线设置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/price/priceCurveSet")
public class PriceCurveSetController extends BaseController {
    private final IPriceCurveSetService priceCurveSetService;

    /**
     * 查询价格曲线设置列表
     * @return 价格曲线设置列表
     */
    @GetMapping("/getPriceCurveSetList")
    public WrapperResp<List<PriceCurveSetResp>> getPriceCurveSetList() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(priceCurveSetService.getPriceCurveSetList(sysUser));
    }

    /**
     * 查询是否显示价格曲线
     * @param commodityId 商品id
     * @return 是/否显示
     */
    @GetMapping("/getIsShowPriceCurve")
    public WrapperResp<Boolean> getIsShowPriceCurve(@RequestParam("commodityId") Long commodityId) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(priceCurveSetService.getPriceCurveSet(shopId, commodityId));
    }

    /**
     * 新增价格曲线设置
     * @param priceCurveSetBatchReq 参数实体
     * @return 是否设置成功
     */
    @PostMapping("/savePriceCurveSet")
    public WrapperResp<Boolean> savePriceCurveSet(@RequestBody @Valid PriceCurveSetBatchReq priceCurveSetBatchReq){
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(priceCurveSetService.savePriceCurveSet(sysUser, priceCurveSetBatchReq.getPriceCurveSetList()));
    }

}
