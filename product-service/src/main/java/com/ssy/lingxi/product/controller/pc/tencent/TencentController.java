package com.ssy.lingxi.product.controller.pc.tencent;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.component.rest.service.TencentService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.tiia.v20190529.models.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 腾讯云图片搜索服务
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/6
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/tencent")
public class TencentController {

    @Resource
    private TencentService tencentService;

    /**
     * 创建图片库
     * @param createGroupRequest 创建图片库请求
     * @return 创建图片库响应
     */
    @PostMapping("/createImageLibrary")
    public CreateGroupResponse createImageLibrary(@RequestBody CreateGroupRequest createGroupRequest) throws TencentCloudSDKException {
        return tencentService.createImageLibrary(createGroupRequest);
    }

    /**
     * 腾讯创建图片
     * @param createImageRequest 创建图片请求
     * @return 创建图片响应
     */
    @PostMapping("/createImage")
    public CreateImageResponse createImage(@RequestBody CreateImageRequest createImageRequest) throws TencentCloudSDKException {
        createImageRequest.setImageUrl("https://shushangyun01.oss-cn-shenzhen.aliyuncs.com/other/63ef8ea9b50f492797cfdc9dee9d43a6.png");
        createImageRequest.setEntityId("123");
        createImageRequest.setPicName("test_image");
        return tencentService.createImage(createImageRequest);
    }

    /**
     * 删除图片
     * @param deleteImagesRequest 删除图片请求
     * @return 删除图片响应
     */
    @PostMapping("/deleteImage")
    public DeleteImagesResponse deleteImage(@RequestBody DeleteImagesRequest deleteImagesRequest) throws TencentCloudSDKException {
        return tencentService.deleteImage(deleteImagesRequest);
    }

    /**
     * 检索图片
     * @param searchImageRequest 检索图片请求
     * @return 检索图片响应
     */
    @PostMapping("/searchImage")
    public SearchImageResponse searchImage(@RequestBody SearchImageRequest searchImageRequest) throws TencentCloudSDKException {
        return tencentService.searchImage(searchImageRequest);
    }
}
