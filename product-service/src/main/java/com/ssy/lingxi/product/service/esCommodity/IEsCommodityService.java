package com.ssy.lingxi.product.service.esCommodity;


import com.ssy.lingxi.component.rabbitMQ.model.dto.CommoditySoldDTO;
import com.ssy.lingxi.product.api.model.req.CommodityCreditScoreReq;
import com.ssy.lingxi.product.api.model.resp.BrandBaseResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CommodityAreaResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CommodityDetailResp;
import com.ssy.lingxi.product.entity.esCommodity.EsCommodity;

import java.util.List;

/**
 * 商品搜索管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/28
 */
public interface IEsCommodityService {
    /**
     * 更新会员商品已售数量
     */
    Boolean updateCommoditySold(CommoditySoldDTO commoditySoldDTO);

    /**
     * 更新商品对应的店铺积分
     * @param commodityCreditScoreReq
     * @return
     */
    Boolean updateCommodityCreditScore(CommodityCreditScoreReq commodityCreditScoreReq);

    /**
     * 修改商品品牌
     * @return
     */
    Boolean updateCommodityBrand(BrandBaseResp brandResponse);

    /**
     * 更新商品销售区域
     */
    void updateCommoditySalesArea(Long salesAreaTemplateId, List<CommodityAreaResp> commodityAreaRespList);

    /**
     * 上架/下架商品信息
     * @param commodityId   商品id
     */
    Boolean publishCommodity(Long commodityId);

    /**
     * 批量上架/下架商品信息
     * @param commodityDetailRespList
     */
    Boolean publishCommodityBatch(List<CommodityDetailResp> commodityDetailRespList);

    /**
     * 下架所有商品
     * @return
     */
    Boolean offPublishAllCommodity(List<Long> idList);

    /**
     * 赋值
     * @param esCommodity es
     * @param commodityDetailResp 商品
     */
    void setEsCommodity(EsCommodity esCommodity, CommodityDetailResp commodityDetailResp);
}
