package com.ssy.lingxi.product.constant;

import cn.hutool.core.collection.CollUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 商品图片目录结构常量
 *
 * <AUTHOR>
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommodityDirConstant {

    /**
     * 压缩目录
     */
    public static final String COMMODITY_PIC_DIR_STRUCTURE_DIR = "_CommodityPicImport";
    /**
     * sku图片目录
     */
    public static final String SKU_PICS_DIR = "sku_pics";
    /**
     * 商品主图目录
     */
    public static final String MAIN_PIC_DIR = "商品主图_mainPic";
    /**
     * 商品详情图目录
     */
    public static final String DETAILS_PIC_DIR = "商品详情图_detailsPic";
    /**
     * 商品图根目录
     */
    public static final String COMMODITY_PIC_DIR = "commodityPicDir";
    /**
     * 支持的文件后缀
     */
    public static final List<String> SUPPORT_COMMODITY_PIC_FILE_SUFFIX = CollUtil.toList("jpg", "jpeg", "png", "gif", "webp");
    /**
     * sku图片数量
     */
    public static final Integer SKU_PIC_COUNT = 6;

}
