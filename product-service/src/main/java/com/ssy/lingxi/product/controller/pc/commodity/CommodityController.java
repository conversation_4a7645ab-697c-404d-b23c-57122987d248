package com.ssy.lingxi.product.controller.pc.commodity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.enums.product.CheckTypeEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.api.product.SellExtraDataParamsRecordReq;
import com.ssy.lingxi.common.model.req.api.product.SellProSpecReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CommodityImportBathReq;
import com.ssy.lingxi.product.api.model.req.CommoditySkuListQueryReq;
import com.ssy.lingxi.product.api.model.req.commodity.*;
import com.ssy.lingxi.product.api.model.resp.BrandResp;
import com.ssy.lingxi.product.api.model.resp.commodity.*;
import com.ssy.lingxi.product.enums.CommodityTypeEnum;
import com.ssy.lingxi.product.service.commodity.ICommodityExcelService;
import com.ssy.lingxi.product.service.commodity.ICommodityPublishService;
import com.ssy.lingxi.product.service.commodity.ICommodityService;
import com.ssy.lingxi.product.service.commodity.ICommoditySkuService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 商品管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodity")
public class CommodityController extends BaseController {
    private final ICommodityService commodityService;
    private final ICommoditySkuService commoditySkuService;
    private final ICommodityExcelService commodityExcelService;
    private final ICommodityPublishService commodityPublishService;

    /**
     * 查询商品信息
     * @param id 商品id
     * @return 操作结果
     */
    @GetMapping(value = "/getCommodity")
    public WrapperResp<CommodityDetailResp> getCommodity(@RequestParam("id") Long id) {
        return WrapperUtil.success(commodityService.getCommodityDetail(id));
    }

    /**
     * 查询商品列表
     * @param commodityQueryReq 查询条件实体
     * @return 操作结果
     */
    @PostMapping(value = "/getCommodityList")
    public WrapperResp<PageDataResp<CommodityListResp>> getCommodityList(@RequestBody CommodityQueryReq commodityQueryReq) {
        UserLoginCacheDTO sysUser = getSysUser();
        commodityQueryReq.setMemberId(sysUser.getMemberId());
        commodityQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        return WrapperUtil.success(commodityService.getCommodityListPage(commodityQueryReq));
    }

    /**
     * 查询待审核商品列表
     * @param commodityQueryReq 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getUnCheckCommodityList")
    public WrapperResp<PageDataResp<CommodityListResp>> getUnCheckCommodityList(CommodityQueryReq commodityQueryReq) {
        UserLoginCacheDTO sysUser = getSysUser();
        commodityQueryReq.setMemberId(sysUser.getMemberId());
        commodityQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        commodityQueryReq.setStatusList(Collections.singletonList(2));
        commodityQueryReq.setCheckType(CheckTypeEnum.MEMBER_CHECK.getCode());
        return WrapperUtil.success(commodityService.getCommodityListPage(commodityQueryReq));
    }

    /**
     * 查询上游供应商品
     * @param id 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getUpperCommodity")
    public WrapperResp<CommodityDetailResp> getUpperCommodity(@RequestParam("id") Long id) {
        return WrapperUtil.success(commodityService.getUpperCommodity(id));
    }

    /**
     * 查询下游销售商品列表
     * @param commodityLowerQueryReq 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getLowerCommodityList")
    public WrapperResp<PageDataResp<CommodityResp>> getLowerCommodityList(CommodityLowerQueryReq commodityLowerQueryReq) {
        return WrapperUtil.success(commodityService.getLowerCommodityList(getSysUser(), commodityLowerQueryReq));
    }

    /**
     * 新增/修改商品
     * @param commodityAddOrUpdateReq 商品实体
     * @return 操作结果
     */
    @PostMapping(value = "/saveOrUpdateCommodity")
    public WrapperResp<Long> saveOrUpdateCommodity(@RequestBody @Valid CommodityAddOrUpdateReq commodityAddOrUpdateReq){
        return WrapperUtil.success(commodityService.saveOrUpdateCommodity(getSysUser(), commodityAddOrUpdateReq));
    }
    
    /**
     * 复制商品
     * @param commonIdReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/copyCommodity")
    public WrapperResp<Void> copyCommodity(@RequestBody @Valid CommonIdReq commonIdReq) {
        CommonIdListReq commonIdListReq = new CommonIdListReq();
        commonIdListReq.setIdList(Collections.singletonList(commonIdReq.getId()));
        commodityService.copyCommodity(getSysUser(), commonIdListReq, null);
        return WrapperUtil.success();
    }

    /**
     * 批量新增上游会员商品
     * @param commonIdListRequest 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/saveUpperCommodity")
    public WrapperResp<Void> saveUpperCommodity(@RequestBody @Valid CommonIdListReq commonIdListRequest) {
        commodityService.copyCommodity(getSysUser(), commonIdListRequest, CommodityTypeEnum.UPPER.getCode());
        return WrapperUtil.success();
    }

    /**
     * 批量删除商品
     * @param commonIdListRequest 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "deleteBatchCommodity")
    public WrapperResp<String> deleteBatchCommodity(@RequestBody @Valid CommonIdListReq commonIdListRequest){
        return WrapperUtil.success(commodityService.deleteBatchCommodity(getSysUser(), commonIdListRequest.getIdList()));
    }

    /**
     * 提交审核商品
     * @param commonIdReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/applyCheckCommodity")
    public WrapperResp<Void> applyCheckCommodity(@RequestBody @Valid CommonIdReq commonIdReq) {
        commodityService.applyCheckCommodity(getSysUser(), commonIdReq.getId());
        return WrapperUtil.success();
    }

    /**
     * 批量提交审核
     * @param idListReq 参数
     */
    @PostMapping("/batchCheckCommodity")
    public WrapperResp<Void> batchCheckCommodity(@RequestBody @Valid CommonIdListReq idListReq){
        commodityService.batchCheckCommodity(getSysUser(), idListReq.getIdList());
        return WrapperUtil.success();
    }

    /**
     * 获取上架商城(单个商品上架)
     * @param id 商品id
     * @return 操作结果
     */
    @GetMapping(value = "/getShop")
    public WrapperResp<List<CommodityShopResp>> getShop(@RequestParam("id") Long id) {
        return WrapperUtil.success(commodityPublishService.getShop(id));
    }

    /**
     * 获取已上架的商城
     */
    @GetMapping("/getPublishedShop")
    public WrapperResp<List<CommodityShopResp>> getPublishedShop(@RequestParam("id") Long id) {

        return WrapperUtil.success(commodityPublishService.getPublishedShop(id));
    }

    /**
     * 获取上架商城(批量上架)
     * @param commonIdListRequest 商品id数组
     * @return 操作结果
     */
    @PostMapping(value = "/getShopBatch")
    public WrapperResp<List<CommodityShopResp>> getShopBatch(@RequestBody @Valid CommonIdListReq commonIdListRequest) {
        return WrapperUtil.success(commodityPublishService.getShopBatch(commonIdListRequest.getIdList()));
    }

    /**
     * 上架商品
     * @param commodityShopReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/publishCommodity")
    public WrapperResp<String> publishCommodity(@RequestBody @Valid CommodityShopReq commodityShopReq) {
        commodityPublishService.publishCommodity(commodityShopReq, true);
        return WrapperUtil.success();
    }

    /**
     * 批量上架商品
     * @param commodityShopBatchReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/publishCommodityBatch")
    public WrapperResp<String> publishCommodityBatch(@RequestBody @Valid CommodityShopBatchReq commodityShopBatchReq) {
        commodityPublishService.publishCommodityBatch(getSysUser(), commodityShopBatchReq, true);
        return WrapperUtil.success();
    }

    /**
     * 下架商品
     * @param commodityShopReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/offPublishCommodity")
    public WrapperResp<String> offPublishCommodity(@RequestBody @Valid CommodityShopReq commodityShopReq) {
        commodityPublishService.publishCommodity(commodityShopReq, false);
        return WrapperUtil.success();
    }

    /**
     * 批量下架商品
     * @param commodityShopBatchReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/offPublishCommodityBatch")
    public WrapperResp<String> offPublishCommodityBatch(@RequestBody @Valid CommodityShopBatchReq commodityShopBatchReq) {
        commodityPublishService.publishCommodityBatch(getSysUser(), commodityShopBatchReq, false);
        return WrapperUtil.success();
    }

    /**
     * 获取商品属性
     * @param commoditySkuId skuId
     * @return 操作结果
     */
    @GetMapping(value = "/getCommodityAttributeByCommoditySkuId")
    public WrapperResp<List<CommodityAttributeResp>> getCommodityAttributeByCommoditySkuId(@RequestParam("commoditySkuId") Long commoditySkuId) {
        return WrapperUtil.success(commodityService.getCommodityAttributeByCommoditySkuId(getSysUser(), commoditySkuId));
    }

    /**
     * 获取商品价格/积分
     * @param skuId 关联表id
     * @return 操作结果
     */
    @GetMapping(value = "/getCommodityPrice")
    public WrapperResp<Map<String, BigDecimal>> getCommodityPrice(@RequestParam Long skuId) {
        return WrapperUtil.success(commoditySkuService.getCommodityPrice(getSysUser(), skuId));
    }

    /**
     * 修改价格/积分
     * @param unitPriceReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/updateCommodityPrice")
    public WrapperResp<Void> updateCommodityPrice(@RequestBody @Valid UnitPriceReq unitPriceReq) {
        commoditySkuService.updateCommodityPrice(getSysUser(), unitPriceReq);
        return WrapperUtil.success();
    }

    /**
     * 查询商品sku信息
     * @param id 商品id
     * @return 操作结果
     */
    @GetMapping(value = "/getCommoditySkuList")
    public WrapperResp<List<CommoditySkuResp>> getCommoditySkuList(@RequestParam Long id) {
        return WrapperUtil.success(commoditySkuService.getCommoditySkuList(id));
    }

    /**
     * 导出商品二维码
     * @param commonIdListReq 商品id数组
     */
    @PostMapping(value = "/exportCommodityQRCode")
    public void exportCommodityQRCode(@RequestBody @Valid CommonIdListReq commonIdListReq, HttpServletRequest request, HttpServletResponse response) {
        commodityExcelService.exportCommodityQRCode(getSysUser(), commonIdListReq.getIdList(), request, response);
    }

    /**
     * 查询商品导入批次
     * @param name 商品导入批次
     */
    @GetMapping(value = "/getCommodityImportBath")
    public WrapperResp<List<String>> getCommodityImportBath(@RequestParam(value = "name", required = false) String name) {
        return WrapperUtil.success(commodityExcelService.getCommodityImportBath(getSysUser(), name));
    }

    /**
     * 通过商品导入批次删除商品
     * @param importBath 商品导入批次
     */
    @PostMapping(value = "/deleteCommodityByImportBath")
    public WrapperResp<Boolean> deleteCommodityByImportBath(@RequestBody @Valid CommodityImportBathReq importBath) {
        return WrapperUtil.success(commodityExcelService.deleteCommodityByImportBath(getSysUser(), importBath.getCommodityImportBath()));
    }

    /**
     * 导出商品sku纬度数据
     */
    @PostMapping(value = "/exportCommoditySkuByCommodityIdList")
    public void exportCommoditySkuByCommodityIdList(@RequestBody @Valid CommonIdListReq commonIdListRequest, HttpServletResponse response) {
        commodityExcelService.exportCommoditySkuByCommodityIdList(response, commonIdListRequest.getIdList());
    }

    /**
     * 通过商品id数组查询对应的商品skuId数组
     * @param commonIdListRequest 商品id数组
     */
    @PostMapping(value = "/getCommoditySkuIdList")
    public WrapperResp<List<CommodityIdAndSkuIdListResp>> getCommoditySkuIdList(@RequestBody @Valid CommonIdListReq commonIdListRequest) {
        return WrapperUtil.success(commodityService.getCommoditySkuIdList(commonIdListRequest.getIdList()));
    }

    /**
     * 查询商品是否配置了库存
     * @param commonIdListRequest 商品id数组
     * @return 返回没有配置库存的商品id数组
     */
    @PostMapping(value = "/getCommodityIsExistStock")
    public WrapperResp<List<Long>> getCommodityIsExistStock(@RequestBody @Valid CommonIdListReq commonIdListRequest) {
        return WrapperUtil.success(commodityService.getCommodityIsExistStock(getSysUser(), commonIdListRequest.getIdList()));
    }

    /**
     * 查询商品列表--通过会员商品sku集合
     * @param commonIdListRequest 查询条件实体
     * @return 操作结果
     */
    @PostMapping(value = "/getCommodityByCommoditySkuIdList")
    public WrapperResp<List<CommoditySkuStockResp>> getCommodityByCommoditySkuIdList(@RequestBody @Valid CommonIdListReq commonIdListRequest) {
        return WrapperUtil.success(commoditySkuService.getCommoditySkuList(commonIdListRequest.getIdList()));
    }

    /**
     * 查询商品列表
     */
    @GetMapping(value = "/getCommoditySkuListByQuery")
    public WrapperResp<List<CommoditySkuStockResp>> getCommoditySkuListByQuery(@Valid CommoditySkuQueryReq req) {
        List<CommoditySkuStockResp> commodityDetailList = commoditySkuService.getCommoditySkuList(req, null, null);
        return WrapperUtil.success(commodityDetailList);
    }

    /**
     * 判断物料是否与商品关联
     * @param request 请求参数
     * @return Boolean
     */
    @PostMapping(value = "/materielUsing")
    public WrapperResp<Boolean> materielUsing(@RequestBody @Valid CommonIdListReq request) {
        return WrapperUtil.success(commodityService.materielUsing(request));
    }

    /**
     * 根据会员品类获取品牌
     */
    @GetMapping("/getBrandListByCustomerCategoryId")
    public WrapperResp<List<BrandResp>> getBrandListByCustomerCategoryId(@RequestParam("customerCategoryId") Long customerCategoryId) {
        return WrapperUtil.success(commodityService.getBrandListByCustomerCategoryId(customerCategoryId));
    }

    /**
     * 根据平台品类获取品牌
     */
    @GetMapping("/getBrandListByCategoryId")
    public WrapperResp<List<BrandResp>> getBrandListByCategoryId(@RequestParam("categoryId") Long categoryId) {
        return WrapperUtil.success(commodityService.getBrandListByCategoryId(categoryId));
    }

    /**
     * 更新同步上游商品
     * @param idReq 参数
     */
    @PostMapping("/syncUpperCommodity")
    public WrapperResp<Void> syncUpperCommodity(@RequestBody @Valid CommonIdReq idReq){
        commodityService.syncUpperCommodity(idReq.getId(), getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 手动同步sku
     * @param specReq 参数
     */
    @PostMapping("/skuSyncHandler")
    public WrapperResp<Void> skuSyncHandler(@RequestBody SellProSpecReq specReq){
        commoditySkuService.commoditySkuSyncHandler(specReq);
        return WrapperUtil.success();
    }

    /**
     * 手动刷新es商品显示状态
     */
    @PostMapping("/timingRefreshEsInventory")
    public WrapperResp<Void> timingRefreshEsInventory(){
        commodityService.timingRefreshEsInventory();
        return WrapperUtil.success();
    }

    /**
     * 手动处理第三方参数
     * @param recordReq 参数
     */
    @PostMapping("/syncSellExtraDataParamsRecord")
    public WrapperResp<Void> syncSellExtraDataParamsRecord(@RequestBody SellExtraDataParamsRecordReq recordReq){
        commodityService.syncSellExtraDataParamsRecord(recordReq);
        return WrapperUtil.success();
    }

    /**
     * 根据条件查询某商品的sku列表
     * @param queryReq 参数
     */
    @GetMapping("/getCommodityDetailSkuList")
    public WrapperResp<List<CommodityDetailSkuResp>> getCommodityDetailSkuList(CommoditySkuListQueryReq queryReq){
        return WrapperUtil.success(commoditySkuService.getCommodityDetailSkuList(queryReq));
    }

    /**
     * 批量修改sku图片，状态
     * @param updateReq 参数
     */
    @PostMapping("/updateSkuImgAndStatus")
    public WrapperResp<Void> updateSkuImgAndStatus(@RequestBody CommoditySkuBatchUpdateReq updateReq){
        commoditySkuService.updateSkuImgAndStatus(updateReq);
        return WrapperUtil.success();
    }
}
