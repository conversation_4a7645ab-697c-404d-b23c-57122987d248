package com.ssy.lingxi.product.service.saleorder;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.product.api.model.req.warehouse.InvoicesAddReq;
import com.ssy.lingxi.product.api.model.req.warehouse.InvoicesUpdateReq;
import com.ssy.lingxi.product.entity.do_.warehouse.InvoicesDO;

import java.util.List;

/**
 * 单据业务处理基类 根据单据类型区分
 *
 * <AUTHOR>
 * @since 2020/12/21
 */
public interface InvoicesTypeStrategy {


    /**
     * 添加单据业务处理基类
     */
    WrapperResp invoicesAdd(InvoicesDO invoices, InvoicesAddReq request, UserLoginCacheDTO sysUser);

    /**
     * 修改单据业务处理基类
     */
    WrapperResp invoicesUpdata(InvoicesDO invoices, InvoicesUpdateReq request);

    /**
    * 批量审核单据
    * <AUTHOR>
    * @since 2020/12/29
    */
    WrapperResp invoicesReviewAll(List<InvoicesDO> invoicess, UserLoginCacheDTO sysUser);
}
