package com.ssy.lingxi.product.controller.pc.materiel;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.commodity.api.feign.IUnitFeign;
import com.ssy.lingxi.commodity.api.model.resp.support.UnitFeignRep;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.*;
import com.ssy.lingxi.product.api.model.resp.*;
import com.ssy.lingxi.product.entity.do_.materiel.MaterielDO;
import com.ssy.lingxi.product.entity.do_.materiel.MaterielSupplyListDO;
import com.ssy.lingxi.product.enums.MaterielsAddInternalStatusEnum;
import com.ssy.lingxi.product.model.req.materiel.MaterielExamineReq;
import com.ssy.lingxi.product.model.resp.materiel.MaterielInnerLogResp;
import com.ssy.lingxi.product.model.resp.materiel.MaterielInnerStatusResp;
import com.ssy.lingxi.product.service.materiel.IMaterielService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 物料管理管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/materiel")
public class MaterielController extends BaseController {
    private final IMaterielService materielService;
    private final IUnitFeign unitFeign;

    /**
     * 物料管理-物料查询-获取物料工作流程全部状态
     *
     * @return 操作结果
     */
    @GetMapping("/getInnerStatus")
    public WrapperResp<List<MaterielInnerStatusResp>> getInnerStatus() {
        return WrapperUtil.success(materielService.getInnerStatus());
    }

    /**
     * 物料管理-物料查询-详情-内部单据流转记录
     *
     * @param materielId 物料id
     * @return 操作结果
     */
    @GetMapping("/getMaterInnerLogList")
    public WrapperResp<List<MaterielInnerLogResp>> getMaterInnerLogList(@RequestParam("materielId") Long materielId) {
        return WrapperUtil.success(materielService.getMaterInnerLogList(materielId));
    }

    /**
     * 物料管理-物料查询-列表
     *
     * @param materielQueryReq 货品查询类
     * @return 操作结果
     */
    @PostMapping("/getMaterialList")
    public WrapperResp<PageDataResp<MaterielResp>> getMaterialList(@RequestBody MaterielQueryReq materielQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        PageDataReq pageDataReq = new PageDataReq();
        pageDataReq.setCurrent(materielQueryReq.getCurrent());
        pageDataReq.setPageSize(materielQueryReq.getPageSize());
        Page<MaterielDO> result = materielService.getMaterialList(sysUser, pageDataReq, materielQueryReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielResp materielResp = BeanUtil.copyProperties(materielDO, MaterielResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielResp;
        }).collect(Collectors.toList());
        //设置生产厂家和产地
        materielService.setMaterielResponseData(resultList, result.getContent(), sysUser);
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料管理-物料查询-冻结/启用
     *
     * @param materielFreezeEnableReq 请求参数
     * @return 操作结果
     */
    @PostMapping("/freezeOrEnableMateriel")
    public WrapperResp<String> freezeOrEnableMateriel(@RequestBody @Validated MaterielFreezeEnableReq materielFreezeEnableReq) {
        //冻结
        if (MaterielsAddInternalStatusEnum.FROZEN.getCode().equals(materielFreezeEnableReq.getStatus())) {
            return WrapperUtil.success(materielService.freezeMateriel(this.getSysUser(), materielFreezeEnableReq));
        } else {
            //启用
            return WrapperUtil.success(materielService.enableMateriel(this.getSysUser(), materielFreezeEnableReq));
        }
    }

    /**
     * 物料管理-物料查询-冻结/启用(批量)
     *
     * @param materielFreezeEnableBatchReq 请求参数
     * @return 操作结果
     */
    @PostMapping("/freezeOrEnableMaterielBatch")
    public WrapperResp<String> freezeOrEnableMaterielBatch(@RequestBody @Validated MaterielFreezeEnableBatchReq materielFreezeEnableBatchReq) {
        //批量冻结
        if (MaterielsAddInternalStatusEnum.FROZEN.getCode().equals(materielFreezeEnableBatchReq.getStatus())) {
            return WrapperUtil.success(materielService.freezeMaterielBatch(this.getSysUser(), materielFreezeEnableBatchReq));
        } else {
            //批量启用
            return WrapperUtil.success(materielService.enableMaterielBatch(this.getSysUser(), materielFreezeEnableBatchReq));
        }
    }

    /**
     * 物料管理-物料查询-物料清单-列表
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @GetMapping("/getMaterielSourceList")
    public WrapperResp<List<MaterielSupplyListDO>> getMaterielSourceList(CommonIdReq request) {
        return WrapperUtil.success(materielService.getMaterielSourceList(request));
    }

    /**
     * 物料管理-物料查询-物料清单-提交保存
     *
     * @param list 请求参数
     * @return 操作结果
     */
    @PostMapping("/submitMaterielSourceList")
    public WrapperResp<String> submitMaterielSourceList(@RequestBody @Validated MaterielSupplyReq list) {
        return WrapperUtil.success(materielService.submitMaterielSourceList(list));
    }

    /**
     * 物料管理-查询货品列表
     *
     * @param pageDataReq            分页实体
     * @param materielQueryReq 货品查询类
     * @return 操作结果
     */
    @GetMapping("/getMaterielList")
    public WrapperResp<PageDataResp<MaterielResp>> getMaterielList(PageDataReq pageDataReq, MaterielQueryReq materielQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielDO> result = materielService.getMaterielList(sysUser, pageDataReq, materielQueryReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielResp materielResp = BeanUtil.copyProperties(materielDO, MaterielResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielResp;
        }).collect(Collectors.toList());
        //设置生产厂家和产地
        materielService.setMaterielResponseData(resultList, result.getContent(), sysUser);
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料管理-待新增物料列表
     *
     * @param pageDataReq            分页实体
     * @param materielQueryReq 货品查询类
     * @return 操作结果
     */
    @GetMapping("/getToBeAddMaterielList")
    public WrapperResp<PageDataResp<MaterielResp>> getToBeAddMaterielList(PageDataReq pageDataReq, MaterielQueryReq materielQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielDO> result = materielService.getToBeAddMaterielList(sysUser, pageDataReq, materielQueryReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielResp materielResp = BeanUtil.copyProperties(materielDO, MaterielResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料管理-查询货品列表(内部状态不为冻结)
     *
     * @param pageDataReq            分页实体
     * @param materielQueryReq 货品查询类
     * @return 操作结果
     */
    @GetMapping("/getDoesNotFreezeMaterielList")
    public WrapperResp<PageDataResp<MaterielResp>> getDoesNotFreezeMaterielList(PageDataReq pageDataReq, MaterielQueryReq materielQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielDO> result = materielService.getDoesNotFreezeMaterielList(sysUser, pageDataReq, materielQueryReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielResp materielResp = BeanUtil.copyProperties(materielDO, MaterielResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }


    /**
     * 物料管理-查询货品列表(内部状态为已确认)
     *
     * @param pageDataReq            分页实体
     * @param materielQueryReq 货品查询类
     * @return 操作结果
     */
    @GetMapping("/getConfirmedMaterielList")
    public WrapperResp<PageDataResp<MaterielResp>> getConfirmedMaterielList(PageDataReq pageDataReq, MaterielQueryReq materielQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielDO> result = materielService.getConfirmedMaterielList(sysUser, pageDataReq, materielQueryReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielResp materielResp = BeanUtil.copyProperties(materielDO, MaterielResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料管理-待新增物料-查询下级供应商货品列表(采购选品)（添加供应商物料）
     *
     * @param pageDataReq                 分页实体
     * @param materielQueryBySubReq 货品查询类
     * @return 操作结果
     */
    @GetMapping("/getSubMaterielList")
    public WrapperResp<PageDataResp<MaterielSubResp>> getSubMaterielList(PageDataReq pageDataReq, MaterielQueryBySubReq materielQueryBySubReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielDO> result = materielService.getSubMaterielList(sysUser, pageDataReq, materielQueryBySubReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielSubResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielSubResp materielSubResp = BeanUtil.copyProperties(materielDO, MaterielSubResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielSubResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielSubResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料管理-待新增物料-查询货品信息
     *
     * @param id 货品id
     * @return Wrapper<?>
     */
    @GetMapping("/getMateriel")
    public WrapperResp<MaterielResp> getMateriel(@RequestParam("id") Long id) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        MaterielDO materielDO = materielService.getMateriel(sysUser, id);
        if (materielDO != null) {
            //设置内部状态
            if (materielDO.getExternalState() != null && materielDO.getExternalState() > 0) {
                materielDO.setInteriorState(materielDO.getExternalState());
            }
            MaterielResp materielResp = this.modelMapper.map(materielDO, MaterielResp.class);
            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            // 查询物料版本
            materielService.getMaterielVersion(materielDO,sysUser, materielResp);
            return WrapperUtil.success(materielResp);
        } else {
            return WrapperUtil.success(null);
        }
    }


    /**
     * 物料管理-查询货品信息(同时返回工作流信息)
     *
     * @param id 货品id
     * @return Wrapper<?>
     */
    @GetMapping("/getMaterielProcessDetail")
    public WrapperResp<MaterielAndProcessResp> getMaterielProcessDetail(@RequestParam("id") Long id) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        MaterielDO materielDO = materielService.getMateriel(sysUser, id);
        if (materielDO != null) {
            MaterielAndProcessResp materielAndProcessResp = this.modelMapper.map(materielDO, MaterielAndProcessResp.class);
            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielAndProcessResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            //处理数据
            materielService.getProcessInfo(materielDO, sysUser, materielAndProcessResp);
            // 获取物料版本
            materielService.getMaterielVersion(materielDO, sysUser, materielAndProcessResp);
            return WrapperUtil.success(materielAndProcessResp);
        } else {
            return WrapperUtil.success(null);
        }
    }


    /**
     * 物料管理-待新增物料-新增/修改货品
     *
     * @param materielReq 货品实体
     * @return 操作结果
     */
    @PostMapping("/saveOrUpdateMateriel")
    public WrapperResp<String> saveOrUpdateMateriel(@RequestBody @Valid MaterielReq materielReq) {
        return WrapperUtil.success(materielService.saveOrUpdateMateriel(getSysUser(), materielReq));
    }

    /**
     * 物料管理-待新增物料-批量删除货品
     *
     * @param simpleIdListRequest 请求参数
     * @return 操作结果
     */
    @PostMapping("deleteBatchMateriel")
    public WrapperResp<String> deleteBatchMateriel(@RequestBody @Validated CommonIdListReq simpleIdListRequest) {
        return WrapperUtil.success(materielService.deleteBatchMateriel(getSysUser(), simpleIdListRequest.getIdList()));
    }

    /**
     * 物料管理-待新增物料-提交审核物料单
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/submit")
    public WrapperResp<Void> materielSubmit(@Validated @RequestBody CommonIdReq request) {
        return WrapperUtil.success(materielService.materielSubmit(request, getSysUser()));
    }

    /**
     * 物料管理-待审核物料(一级)-列表
     *
     * @param pageDataReq            分页参数
     * @param materielQueryReq 请求参数
     * @return 操作结果
     */
    @GetMapping("/materielExamineList1")
    public WrapperResp<PageDataResp<MaterielResp>> materielExamineList1(PageDataReq pageDataReq, MaterielQueryReq materielQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielDO> result = materielService.materielExamineList1(sysUser, pageDataReq, materielQueryReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielResp materielResp = BeanUtil.copyProperties(materielDO, MaterielResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料管理-待审核物料(一级)-审核
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/materielExamine1")
    public WrapperResp<Void> materielExamine1(@Validated @RequestBody MaterielExamineReq request) {
        return WrapperUtil.success(materielService.materielExamine1(getSysUser(), request));
    }

    /**
     * 物料管理-待审核物料(一级)-批量审核通过
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/materielExamineBatch1")
    public WrapperResp<Void> materielExamineBatch1(@Validated @RequestBody CommonIdListReq request) {
        return WrapperUtil.success(materielService.materielExamineBatch1(getSysUser(), request));
    }

    /**
     * 物料管理-待审核物料(二级)-列表
     *
     * @param pageDataReq            分页参数
     * @param materielQueryReq 请求参数
     * @return 操作结果
     */
    @GetMapping("/materielExamineList2")
    public WrapperResp<PageDataResp<MaterielResp>> materielExamineList2(PageDataReq pageDataReq, MaterielQueryReq materielQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielDO> result = materielService.materielExamineList2(sysUser, pageDataReq, materielQueryReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielResp materielResp = BeanUtil.copyProperties(materielDO, MaterielResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料管理-待审核物料(二级)-审核
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/materielExamine2")
    public WrapperResp<Void> materielExamine2(@Validated @RequestBody MaterielExamineReq request) {
        return WrapperUtil.success(materielService.materielExamine2(getSysUser(), request));
    }

    /**
     * 物料管理-待审核物料(二级)-批量审核通过
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/materielExamineBatch2")
    public WrapperResp<Void> materielExamineBatch2(@Validated @RequestBody CommonIdListReq request) {
        return WrapperUtil.success(materielService.materielExamineBatch2(getSysUser(), request));
    }

    /**
     * 物料管理-待审核物料变更(一级)-列表
     *
     * @param pageDataReq            分页参数
     * @param materielQueryReq 请求参数
     * @return 操作结果
     */
    @GetMapping("/materielExamineChangeList1")
    public WrapperResp<PageDataResp<MaterielResp>> materielExamineChangeList1(PageDataReq pageDataReq, MaterielQueryReq materielQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielDO> result = materielService.materielExamineChangeList1(sysUser, pageDataReq, materielQueryReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielResp materielResp = BeanUtil.copyProperties(materielDO, MaterielResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料管理-待审核物料变更(一级)-审核
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/materielExamineChange1")
    public WrapperResp<Void> materielExamineChange1(@Validated @RequestBody MaterielExamineReq request) {
        return WrapperUtil.success(materielService.materielExamineChange1(getSysUser(), request));
    }

    /**
     * 物料管理-待审核物料变更(一级)-批量审核通过
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/materielExamineChangeBatch1")
    public WrapperResp<Void> materielExamineChangeBatch1(@Validated @RequestBody CommonIdListReq request) {
        return WrapperUtil.success(materielService.materielExamineChangeBatch1(getSysUser(), request));
    }

    /**
     * 物料管理-待审核物料变更(二级)-列表
     *
     * @param pageDataReq            分页参数
     * @param materielQueryReq 请求参数
     * @return 操作结果
     */
    @GetMapping("/materielExamineChangeList2")
    public WrapperResp<PageDataResp<MaterielResp>> materielExamineChangeList2(PageDataReq pageDataReq, MaterielQueryReq materielQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielDO> result = materielService.materielExamineChangeList2(sysUser, pageDataReq, materielQueryReq);
        if(CollectionUtils.isEmpty(result.getContent())){
            return WrapperUtil.success(new PageDataResp<>());
        }

        List<MaterielResp> resultList = result.getContent().stream().map(materielDO -> {
            MaterielResp materielResp = BeanUtil.copyProperties(materielDO, MaterielResp.class);

            UnitFeignRep unitRep = WrapperUtil.getDataOrThrow(unitFeign.getUnit(materielDO.getUnitId()));
            materielResp.setUnitName(Objects.nonNull(unitRep) ? unitRep.getName() : null);
            return materielResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料管理-待审核物料变更(二级)-审核
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/materielExamineChange2")
    public WrapperResp<Void> materielExamineChange2(@Validated @RequestBody MaterielExamineReq request) {
        return WrapperUtil.success(materielService.materielExamineChange2(getSysUser(), request));
    }

    /**
     * 物料管理-待审核物料变更(二级)-批量审核通过
     *
     * @param request 请求参数
     * @return 操作结果
     */
    @PostMapping("/materielExamineChangeBatch2")
    public WrapperResp<Void> materielExamineChangeBatch2(@Validated @RequestBody CommonIdListReq request) {
        return WrapperUtil.success(materielService.materielExamineChangeBatch2(getSysUser(), request));
    }

    /************************************************请购单使用****************************************************/

    /**
     * 物料管理-查询货品列表(同时返回生产厂家和产地)
     *
     * @param pageDataReq                  分页实体
     * @param materielSourceQueryReq 货品查询类
     * @return 操作结果
     */
    @GetMapping("/getMaterielByMemberList")
    public WrapperResp<PageDataResp<MaterielSourceResp>> getMaterielByMemberList(PageDataReq pageDataReq, MaterielSourceQueryReq materielSourceQueryReq) {
        return WrapperUtil.success(materielService.getMaterielByMemberList(pageDataReq, getSysUser(), materielSourceQueryReq));
    }

    /**
     * 物料管理-查询货品列表(同时返回生产厂家和产地)SRM端使用
     *
     * @param pageDataReq                  分页实体
     * @param materielSourceQuerySRMReq 货品查询类
     * @return 操作结果
     */
    @GetMapping("/getMaterielByMemberListSrm")
    public WrapperResp<PageDataResp<MaterielSourceResp>> getMaterielByMemberListSrm(PageDataReq pageDataReq, MaterielSourceQuerySRMReq materielSourceQuerySRMReq) {
        return WrapperUtil.success(materielService.getMaterielByMemberListSrm(pageDataReq, getSysUser(), materielSourceQuerySRMReq));
    }

    /**
     * 新增请购单-查询物料货源清单的供应商(列表)
     *
     * @param pageDataReq                  分页实体
     * @param materielSupplyPageReq 货品查询类
     * @return 操作结果
     */
    @GetMapping("/getMaterielSupplyList")
    public WrapperResp<PageDataResp<MaterielSupplyResp>> getMaterielSupplyList(PageDataReq pageDataReq, MaterielSupplyPageReq materielSupplyPageReq) {
        return WrapperUtil.success(materielService.getMaterielSupplyList(pageDataReq, getSysUser(), materielSupplyPageReq));
    }


    /**
     * 导出物料导入模板
     *
     * @param request  httpServletRequest
     * @param response httpServletResponse
     */
    @GetMapping("/exportMaterielTemplate")
    public void exportMaterielTemplate(HttpServletRequest request, HttpServletResponse response) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        materielService.exportMaterielTemplate(sysUser, request, response);
    }

    /**
     * 导入货品
     *
     * @param file 物料excel
     * @return 导入情况说明
     */
    @PostMapping("/importMateriel")
    public WrapperResp<Boolean> importMateriel(MultipartFile file) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(materielService.importMateriel(sysUser, file));
    }

    /************************************************供应商供货能力使用****************************************************/


    /**
     * 货源清单-查询供货商供货能力(采购商下的所有下级供应商会员)
     *
     * @param pageDataReq                        分页实体
     * @param materielSupplyAbilityPageReq 查询传参
     * @return 供货商供货能力分页列表
     */
    @GetMapping("/getMaterielSupplyAbilityList")
    public WrapperResp<PageDataResp<MaterielSupplyAbilityResp>> getMaterielSupplyAbilityList(PageDataReq pageDataReq, MaterielSupplyAbilityPageReq materielSupplyAbilityPageReq) {
        return WrapperUtil.success(materielService.getMaterielSupplyAbilityList(pageDataReq, getSysUser(), materielSupplyAbilityPageReq));
    }


    /**
     * 货源清单-查询供货商供货能力(采购商下的某一下级供应商会员)
     *
     * @param pageDataReq                        分页实体
     * @param materielSupplyAbilityBySupplierPageReq 查询传参
     * @return 供货商供货能力分页列表
     */
    @GetMapping("/getMaterielSupplyAbilityListBySupplier")
    public WrapperResp<PageDataResp<MaterielSupplyAbilityResp>> getMaterielSupplyAbilityListBySupplier(PageDataReq pageDataReq, @Valid MaterielSupplyAbilityBySupplierPageReq materielSupplyAbilityBySupplierPageReq) {
        return WrapperUtil.success(materielService.getMaterielSupplyAbilityListBySupplier(pageDataReq, getSysUser(), materielSupplyAbilityBySupplierPageReq));
    }

}
