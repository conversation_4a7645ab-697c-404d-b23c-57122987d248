package com.ssy.lingxi.product.controller.pc.warehouse;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseAutoEnterReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseSyncReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseAutoEnterResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseSyncResp;
import com.ssy.lingxi.product.service.warehouse.IWarehouseRuleConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 进销存-仓库库存规则配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/7/1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/warehouseRuleConfig")
public class WarehouseRuleConfigController extends BaseController {
    private final IWarehouseRuleConfigService warehouseRuleConfigService;

    /**
     * 查询自动入库配置信息
     */
    @GetMapping("/getWarehouseAutoEnter")
    public WrapperResp<WarehouseAutoEnterResp> getWarehouseAutoEnter() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseRuleConfigService.getWarehouseAutoEnter(sysUser));
    }

    /**
     * 新增/修改自动入库配置
     * @param warehouseAutoEnterReq 自动入库实体
     * @return 自动入库id
     */
    @PostMapping("/saveOrUpdateWarehouseAutoEnter")
    public WrapperResp<Long> saveOrUpdateWarehouseAutoEnter(@RequestBody WarehouseAutoEnterReq warehouseAutoEnterReq){
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseRuleConfigService.saveOrUpdateWarehouseAutoEnter(sysUser, warehouseAutoEnterReq));
    }

    /**
     * 查询物料仓库库存同步配置信息
     */
    @GetMapping("/getWarehouseSync")
    public WrapperResp<WarehouseSyncResp> getWarehouseSync() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseRuleConfigService.getWarehouseSync(sysUser));
    }

    /**
     * 新增/修改物料仓库库存同步配置
     * @param warehouseSyncReq 物料仓库库存同步配置实体
     * @return 物料仓库库存同步配置id
     */
    @PostMapping("/saveOrUpdateWarehouseSync")
    public WrapperResp<Long> saveOrUpdateWarehouseSync(@RequestBody @Valid WarehouseSyncReq warehouseSyncReq){
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseRuleConfigService.saveOrUpdateWarehouseSync(sysUser, warehouseSyncReq));
    }

}
