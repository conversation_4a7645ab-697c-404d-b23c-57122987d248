package com.ssy.lingxi.product.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.feign.IWarehouseFeign;
import com.ssy.lingxi.product.api.model.req.warehouse.*;
import com.ssy.lingxi.product.api.model.resp.warehouse.InvoicesResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseAutoEnterResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseAutoOutResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseResp;
import com.ssy.lingxi.product.service.warehouse.IInvoicesService;
import com.ssy.lingxi.product.service.warehouse.IWarehouseAutoOutService;
import com.ssy.lingxi.product.service.warehouse.IWarehouseRuleConfigService;
import com.ssy.lingxi.product.service.warehouse.IWarehouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 仓库内部接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/7/1
 * @ignore 不需要提交到Yapi
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class WarehouseFeignController extends BaseController implements IWarehouseFeign {
    private final IInvoicesService invoicesService;
    private final IWarehouseRuleConfigService warehouseRuleConfigService;
    private final IWarehouseAutoOutService warehouseAutoOutService;
    private final IWarehouseService warehouseService;

    /**
     * 添加单据内部接口
     * @param request 参数
     */
    @Override
    public WrapperResp<InvoicesResp> invoicesAddFeign(@Valid @RequestBody InvoicesAddFeignReq request) {
        UserLoginCacheDTO sysUser = new UserLoginCacheDTO();
        sysUser.setMemberId(request.getCreateMemberId());
        sysUser.setMemberRoleId(request.getCreateRoleId());
        return WrapperUtil.success(invoicesService.invoicesAddOrUpdate(BeanUtil.copyProperties(request, InvoicesAddOrUpdateReq.class), sysUser, false));
    }

    /**
     * 单据单个审核/批量审核
     * 单据单个反审/批量反审
     * @param request 参数
     */
    @Override
    public WrapperResp<Void> invoicesReview(@Valid @RequestBody InvoicesReviewReq request) {
        return WrapperUtil.success(invoicesService.invoicesReview(request));
    }

    /**
     * 收货后自动创建采购入库单
     * @param request 参数
     */
    @Override
    public WrapperResp<Void> autoCreateEnterInvoices(@RequestBody @Valid InvoicesAutoCreateReq request) {
        log.info("收货后自动创建采购入库单, 参数：{}", JSONUtil.toJsonStr(request));
        UserLoginCacheDTO sysUser = new UserLoginCacheDTO();
        sysUser.setMemberId(request.getMemberId());
        sysUser.setMemberRoleId(request.getMemberRoleId());
        WarehouseAutoEnterResp warehouseAutoEnter = warehouseRuleConfigService.getWarehouseAutoEnter(sysUser);
        if(warehouseAutoEnter != null && warehouseAutoEnter.getIsCreate()){
            List<InvoicesAddOrUpdateReq> invoicesAddOrUpdateReqList = new ArrayList<>();
            String invoicesAbstract = request.getInvoicesAbstract();
            Long invoicesTime = request.getInvoicesTime();
            Integer source = request.getSource();
            List<InvoicesReq> invoicesList = request.getInvoicesList();
            invoicesList.forEach(invoicesReq -> {
                Long warehouseId = invoicesReq.getWarehouseId();
                String warehouseRole = invoicesReq.getWarehouseRole();
                List<InvoicesMaterielReq> invoicesDetailsList = invoicesReq.getInvoicesDetailsList();
                InvoicesAddOrUpdateReq invoicesAddOrUpdateReq = new InvoicesAddOrUpdateReq();
                invoicesAddOrUpdateReq.setInvoicesTypeId(warehouseAutoEnter.getInvoicesTypeId());
                invoicesAddOrUpdateReq.setWarehouseId(warehouseId);
                invoicesAddOrUpdateReq.setWarehouseRole(warehouseRole);
                invoicesAddOrUpdateReq.setInvoicesAbstract(invoicesAbstract);
                invoicesAddOrUpdateReq.setInvoicesTime(invoicesTime);
                invoicesAddOrUpdateReq.setSource(source);
                invoicesAddOrUpdateReq.setInvoicesDetailsList(invoicesDetailsList);
                invoicesAddOrUpdateReqList.add(invoicesAddOrUpdateReq);
            });
            return WrapperUtil.success(invoicesService.autoCreateInvoicesBatch(invoicesAddOrUpdateReqList, sysUser));
        }
        return new WrapperResp<>();
    }

    /**
     * 发货后自动创建销售出库单
     * @param request 参数
     */
    @Override
    public WrapperResp<Void> autoCreateOutInvoices(@RequestBody @Valid InvoicesAutoCreateReq request) {
        log.info("发货后自动创建销售出库单, 参数：{}", JSONUtil.toJsonStr(request));
        UserLoginCacheDTO sysUser = new UserLoginCacheDTO();
        sysUser.setMemberId(request.getMemberId());
        sysUser.setMemberRoleId(request.getMemberRoleId());
        WarehouseAutoOutResp warehouseAutoOut = warehouseAutoOutService.getWarehouseAutoOut(sysUser);
        if(warehouseAutoOut != null && warehouseAutoOut.getIsCreate()){
            List<InvoicesAddOrUpdateReq> invoicesAddOrUpdateReqList = new ArrayList<>();
            String invoicesAbstract = request.getInvoicesAbstract();
            Long invoicesTime = request.getInvoicesTime();
            Integer source = request.getSource();
            List<InvoicesReq> invoicesList = request.getInvoicesList();
            invoicesList.forEach(invoicesReq -> {
                Long warehouseId = invoicesReq.getWarehouseId();
                String warehouseRole = invoicesReq.getWarehouseRole();
                List<InvoicesMaterielReq> invoicesDetailsList = invoicesReq.getInvoicesDetailsList();
                InvoicesAddOrUpdateReq invoicesAddOrUpdateReq = new InvoicesAddOrUpdateReq();
                invoicesAddOrUpdateReq.setInvoicesTypeId(warehouseAutoOut.getInvoicesTypeId());
                invoicesAddOrUpdateReq.setWarehouseId(warehouseId);
                invoicesAddOrUpdateReq.setWarehouseRole(warehouseRole);
                invoicesAddOrUpdateReq.setInvoicesAbstract(invoicesAbstract);
                invoicesAddOrUpdateReq.setInvoicesTime(invoicesTime);
                invoicesAddOrUpdateReq.setSource(source);
                invoicesAddOrUpdateReq.setInvoicesDetailsList(invoicesDetailsList);
                invoicesAddOrUpdateReqList.add(invoicesAddOrUpdateReq);
            });
            return WrapperUtil.success(invoicesService.autoCreateInvoicesBatch(invoicesAddOrUpdateReqList, sysUser));
        }
        return new WrapperResp<>();
    }

    /**
     * 根据id查询仓库信息
     * @param id
     * @return
     */
    @Override
    public WrapperResp<WarehouseResp> findById(Long id) {
        WarehouseResp warehouseResp = warehouseService.findById(id);
        if (warehouseResp == null) {
            return WrapperUtil.fail(ResponseCodeEnum.WAREHOUSE_FREIGHT_SPACE_NOT_EXIST);
        }
        return WrapperUtil.success(warehouseResp);
    }

    @Override
    public WrapperResp<List<WarehouseResp>> findByIds(@RequestBody List<Long> ids) {
        List<WarehouseResp> warehouseResps = warehouseService.findByIds(ids);
        if (CollectionUtils.isEmpty(warehouseResps)) {
            return WrapperUtil.fail(ResponseCodeEnum.WAREHOUSE_FREIGHT_SPACE_NOT_EXIST);
        }
        return WrapperUtil.success(warehouseResps);
    }

    /**
     * 根据仓库编码获取物流模板id
     * @param warehouseCodeList 仓库编码
     * @return 物流模板id
     */
    @Override
    public WrapperResp<Map<String, Long>> findLogisticsTemplateIdByWarehouseCode(List<String> warehouseCodeList) {
        return WrapperUtil.success(warehouseService.findByWarehouseCode(warehouseCodeList));
    }
}
