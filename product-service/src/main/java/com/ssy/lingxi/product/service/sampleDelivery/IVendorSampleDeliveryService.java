package com.ssy.lingxi.product.service.sampleDelivery;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.model.req.sampledelivery.SampleDeliverySendReq;
import com.ssy.lingxi.product.model.req.sampledelivery.SampleDeliveryVendorPageDataReq;
import com.ssy.lingxi.product.model.req.sampledelivery.SampleDeliveryVendorToConfirmPageDataReq;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryDetailResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryVendorQueryResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryVendorToConfirmQueryResp;

/**
 * 送样需求单 - 业务
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/23 14:18
 */
public interface IVendorSampleDeliveryService {
    /**
     * 送样需求单分页列表
     *
     * @param pageVO  分页查询传参
     * @param sysUser 登录用户信息
     * @return 分页列表
     */
    PageDataResp<SampleDeliveryVendorQueryResp> page(SampleDeliveryVendorPageDataReq pageVO, UserLoginCacheDTO sysUser);

    /**
     * 待确认寄样送样需求单列表
     *
     * @param pageVO  分页查询传参
     * @param sysUser 登录用户信息
     * @return 分页列表
     */
    PageDataResp<SampleDeliveryVendorToConfirmQueryResp> pageToConfirm(SampleDeliveryVendorToConfirmPageDataReq pageVO, UserLoginCacheDTO sysUser);

    /**
     * 送样需求单详情
     *
     * @param id      主键id
     * @param sysUser 登录用户信息
     * @return 送样需求单详情
     */
    SampleDeliveryDetailResp detail(Long id, UserLoginCacheDTO sysUser);

    /**
     * 确认寄样
     *
     * @param sampleDeliverySendReq 寄样传参
     * @param sysUser              登录用户信息
     */
    void send(SampleDeliverySendReq sampleDeliverySendReq, UserLoginCacheDTO sysUser);

}
