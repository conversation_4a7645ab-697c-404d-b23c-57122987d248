package com.ssy.lingxi.product.controller.pc.commodity;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.enums.product.CheckTypeEnum;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.product.CommodityStatusEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityCheckBatchReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityCheckReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityPlatformReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityQueryReq;
import com.ssy.lingxi.product.api.model.resp.commodity.CommodityResp;
import com.ssy.lingxi.product.model.resp.platform.CommodityCheckRecordResp;
import com.ssy.lingxi.product.service.commodity.ICommodityCheckService;
import com.ssy.lingxi.product.service.commodity.ICommodityService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 商品管理--平台后台
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodity/platform")
public class CommodityPlatformController extends BaseController {
    private final ICommodityService commodityService;
    private final ICommodityCheckService commodityCheckService;

    /**
     * 查询商品列表
     * @param pageDataReq 分页实体
     * @param commodityPlatformReq 请求参数
     * @return 操作结果
     */
    @GetMapping(value = "/getPlatformCommodityList")
    public WrapperResp<PageDataResp<CommodityResp>> getPlatformCommodityList(PageDataReq pageDataReq, CommodityPlatformReq commodityPlatformReq) {
        Integer status = commodityPlatformReq.getStatus();
        Integer priceType = commodityPlatformReq.getPriceType();
        CommodityQueryReq commodityQueryReq = BeanUtil.copyProperties(commodityPlatformReq, CommodityQueryReq.class);
        //状态
        List<Integer> statusList = new ArrayList<>();
        if(status != null && status > 0){
            statusList.add(status);
        }else{
            statusList.add(CommodityStatusEnum.NOT_CHECK.getCode());
            statusList.add(CommodityStatusEnum.NOT_PASS.getCode());
            statusList.add(CommodityStatusEnum.PASS.getCode());
            statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
            statusList.add(CommodityStatusEnum.OFF_SHELF.getCode());
            statusList.add(CommodityStatusEnum.NEVER_SHELF.getCode());
        }
        commodityQueryReq.setStatusList(statusList);
        //产品类型
        List<Integer> priceTypeList = new ArrayList<>();
        if(priceType != null && priceType > 0){
            priceTypeList.add(priceType);
        }
        commodityQueryReq.setPriceTypeList(priceTypeList);
        commodityQueryReq.setCurrent(pageDataReq.getCurrent());
        commodityQueryReq.setPageSize(pageDataReq.getPageSize());

        return WrapperUtil.success(commodityService.getCommodityPage(commodityQueryReq));
    }

    /**
     * 查询待审核商品列表
     * @param pageDataReq 分页实体
     * @param commodityPlatformReq 请求参数
     * @return 操作结果
     */
    @GetMapping(value = "/getPlatformUnCheckCommodityList")
    public WrapperResp<PageDataResp<CommodityResp>> getPlatformUnCheckCommodityList(PageDataReq pageDataReq, CommodityPlatformReq commodityPlatformReq) {
        Integer priceType = commodityPlatformReq.getPriceType();
        CommodityQueryReq commodityQueryReq = BeanUtil.copyProperties(commodityPlatformReq, CommodityQueryReq.class);
        //产品类型
        List<Integer> priceTypeList = new ArrayList<>();
        if(priceType != null && priceType > 0){
            priceTypeList.add(priceType);
        }
        commodityQueryReq.setPriceTypeList(priceTypeList);
        commodityQueryReq.setStatusList(Collections.singletonList(CommodityStatusEnum.NOT_CHECK.getCode()));
        commodityQueryReq.setCheckType(CheckTypeEnum.PLATFORM_CHECK.getCode());
        commodityQueryReq.setCurrent(pageDataReq.getCurrent());
        commodityQueryReq.setPageSize(pageDataReq.getPageSize());

        return WrapperUtil.success(commodityService.getCommodityPage(commodityQueryReq));
    }

    /**
     * 审核商品
     * @param commodityCheckReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/checkCommodity")
    public WrapperResp<String> checkCommodity(@RequestBody @Valid CommodityCheckReq commodityCheckReq) {
        commodityCheckService.checkCommodity(getSysUser(), commodityCheckReq.getId(), commodityCheckReq.getStatus(), commodityCheckReq.getCheckRemark());
        return WrapperUtil.success();
    }

    /**
     * 审核商品 - 批量
     */
    @PostMapping(value = "/checkCommodity/batch")
    public WrapperResp<String> checkCommodityBatch(@RequestBody @Valid CommodityCheckBatchReq commodityCheckReq) {
        commodityCheckService.checkCommodityBatch(getSysUser(), commodityCheckReq);
        return WrapperUtil.success();
    }

    /**
     * 查询商品审核记录
     * @param commodityId 商品id
     */
    @GetMapping(value = "/getCommodityCheckRecord")
    public WrapperResp<List<CommodityCheckRecordResp>> getCommodityCheckRecord(@RequestParam("commodityId") Long commodityId) {
        return WrapperUtil.success(commodityCheckService.getCommodityCheckRecord(commodityId));
    }

    /**
     * 查询商品审核记录--操作日志
     * @param commodityId 商品id
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    @GetMapping(value = "/getCommodityCheckRecordList")
    public WrapperResp<PageDataResp<CommodityCheckRecordResp>> getCommodityCheckRecordList(PageDataReq pageDataReq, @RequestParam(value = "commodityId",required = false) Long commodityId, @RequestParam(value = "startTime", required = false) Long startTime, @RequestParam(value = "endTime",required = false) Long endTime) {
        return WrapperUtil.success(commodityCheckService.getCommodityCheckRecordList(pageDataReq, commodityId, startTime, endTime));
    }
}
