package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.rabbitMQ.model.dto.CommoditySoldDTO;
import com.ssy.lingxi.product.api.model.resp.OftenBuyCommodityResp;

/**
 * 商品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface IOftenBuyCommodityService {

    /**
     * 查询常购清单列表
     * @param pageDataReq        分页实体
     * @param shopId        商城id
     * @param sysUser       当前登录用户
     * @return 常购清单列表
     */
    PageDataResp<OftenBuyCommodityResp> getOftenBuyCommodityList(PageDataReq pageDataReq, Long shopId, String name, UserLoginCacheDTO sysUser);

    /**
     * 更新常购清单
     */
    Boolean updateOftenBuyCommodity(CommoditySoldDTO commoditySoldDTO);
}
