package com.ssy.lingxi.product.controller.pc.shop;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.PurchaseCustomerBatchReq;
import com.ssy.lingxi.product.api.model.req.PurchaseCustomerReq;
import com.ssy.lingxi.product.entity.do_.PurchaseDO;
import com.ssy.lingxi.product.model.req.PurchaseQueryReq;
import com.ssy.lingxi.product.model.resp.PurchaseResp;
import com.ssy.lingxi.product.service.shop.IPurchaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 能力中心--代客下单的进货单管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/4
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/customer/purchase")
public class PurchaseCustomerController extends BaseController {
    private final IPurchaseService purchaseService;

    /**
     * 查询进货单列表
     * @param customerMemberId 客户会员id
     * @param customerMemberRoleId 客户会员角色id
     * @param orderId 订单id
     */
    @GetMapping("/getPurchaseList")
    public WrapperResp<List<PurchaseResp>> getPurchaseList(@RequestParam("customerMemberId") Long customerMemberId, @RequestParam("customerMemberRoleId") Long customerMemberRoleId, @RequestParam(value = "orderId", required = false) Long orderId) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        PurchaseQueryReq purchaseQueryReq = new PurchaseQueryReq();
        purchaseQueryReq.setMemberId(sysUser.getMemberId());
        purchaseQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        purchaseQueryReq.setShopId(getHeadersShopId());
        purchaseQueryReq.setCustomerMemberId(customerMemberId);
        purchaseQueryReq.setCustomerMemberRoleId(customerMemberRoleId);
        purchaseQueryReq.setOrderId(orderId);
        return WrapperUtil.success(purchaseService.getPurchaseList(purchaseQueryReq, true));
    }

    /**
     * 新增/修改进货单
     * @param purchaseRequest 进货单实体
     * @return 进货单数量
     */
    @PostMapping("/saveOrUpdatePurchase")
    public WrapperResp<Integer> saveOrUpdatePurchase(@RequestBody @Valid PurchaseCustomerReq purchaseRequest){
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        PurchaseDO purchaseDO = BeanUtil.copyProperties(purchaseRequest, PurchaseDO.class);
        purchaseDO.setShopId(shopId);
        return WrapperUtil.success(purchaseService.saveOrUpdatePurchase(sysUser, purchaseDO,null));
    }

    /**
     * 批量新增进货单
     * @param purchaseBatchRequest 进货单实体
     * @return 是否成功
     */
    @PostMapping("/savePurchaseBatch")
    public WrapperResp<Boolean> savePurchaseBatch(@RequestBody PurchaseCustomerBatchReq purchaseBatchRequest){
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        List<PurchaseCustomerReq> purchaseCustomerList = purchaseBatchRequest.getPurchaseBatchList();
        return WrapperUtil.success(purchaseService.savePurchaseCustomerBatch(sysUser, shopId, purchaseCustomerList));
    }

    /**
     * 删除进货单
     * @param commonIdListRequest 参数
     */
    @PostMapping("/deletePurchase")
    public WrapperResp<String> deletePurchase(@RequestBody CommonIdListReq commonIdListRequest){
        return WrapperUtil.success(purchaseService.deletePurchase(commonIdListRequest));
    }

    /**
     * 查询进货单数量
     * @param customerMemberId 客户会员id
     * @param customerMemberRoleId 客户会员角色id
     * @param orderId 订单id
     */
    @GetMapping("/getPurchaseCount")
    public WrapperResp<Integer> getPurchaseCount(@RequestParam("customerMemberId") Long customerMemberId, @RequestParam("customerMemberRoleId") Long customerMemberRoleId, @RequestParam(value = "orderId", required = false) Long orderId){
        UserLoginCacheDTO sysUser = this.getSysUser();
        PurchaseQueryReq purchaseQueryReq = new PurchaseQueryReq();
        purchaseQueryReq.setOrderId(orderId);
        purchaseQueryReq.setShopId(getHeadersShopId());
        purchaseQueryReq.setMemberId(sysUser.getMemberId());
        purchaseQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        purchaseQueryReq.setCustomerMemberId(customerMemberId);
        purchaseQueryReq.setCustomerMemberRoleId(customerMemberRoleId);
        return WrapperUtil.success(purchaseService.getPurchaseCount(purchaseQueryReq));
    }
}
