package com.ssy.lingxi.product.service.warehouse;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.select.SelectVO;
import com.ssy.lingxi.product.api.model.req.warehouse.InvoicesTypeAddOrUpdateReq;
import com.ssy.lingxi.product.api.model.req.warehouse.InvoicesTypeListReq;
import com.ssy.lingxi.product.api.model.req.warehouse.InvoicesTypeStateUpdateReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.InvoicesTypeListResp;

import java.util.List;

/**
* 单据类型管理
* <AUTHOR>
* @since 2020/6/22
*/
public interface IInvoicesTypeService {

    /**
    * 查询单据类型列表
    * <AUTHOR>
    * @since 2020/6/22
     * @param request
    */
    PageDataResp<InvoicesTypeListResp> invoicesTypeList(InvoicesTypeListReq request, UserLoginCacheDTO sysUser);

    /**
    * 查询单据类型详情
    * <AUTHOR>
    * @since 2020/8/6
    */
    InvoicesTypeListResp invoicesTypeDetails(Long id);

    /**
    * 删除单据类型
    * <AUTHOR>
    * @since 2020/6/22
    * @param id 单据类型id
    */
    Void invoicesTypeDelete(Long id);

    /**
    * 增加/修改单据类型
    * <AUTHOR>
    * @since 2020/6/22
    */
    Void invoicesTypeAddOrUpdate(InvoicesTypeAddOrUpdateReq request, UserLoginCacheDTO sysUser);

    /**
    * 停用启用单据类型
    * <AUTHOR>
    * @since 2020/6/22
    */
    Void invoicesTypeStartOrStop(InvoicesTypeStateUpdateReq request);

    /**
    * 查询全部单据类型
    * <AUTHOR>
    * @since 2020/8/5
    */
    List<InvoicesTypeListResp> invoicesTypeAll(UserLoginCacheDTO sysUser);

    /**
     * 查询单据类型下拉框
     */
    List<SelectVO> getInvoicesType(UserLoginCacheDTO sysUser, Integer direction);

}
