package com.ssy.lingxi.product.controller.pc.baitai;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdPageDataReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.product.api.model.req.baitai.*;
import com.ssy.lingxi.product.api.model.resp.baitai.VisaCategoryOrderResp;
import com.ssy.lingxi.product.api.model.resp.baitai.VisaCategoryResp;
import com.ssy.lingxi.product.api.model.resp.baitai.VisaDetailResp;
import com.ssy.lingxi.product.api.model.resp.baitai.VisaPageResp;
import com.ssy.lingxi.product.service.baitai.VisaCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 挂签品类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/visaCategory")
public class VisaCategoryController extends BaseController {

    @Resource
    private VisaCategoryService visaCategoryService;

    /**
     * 新增挂签品类
     *
     * @param visaCategoryReq 挂签品类请求参数
     * @return WrapperResp
     */
    @PostMapping("/addVisaCategory")
    public WrapperResp<Void> addVisaCategory(@RequestBody @Valid VisaCategoryReq visaCategoryReq) {
        return visaCategoryService.addVisaCategory(visaCategoryReq);
    }

    /**
     * 编辑挂签品类
     *
     * @param visaCategoryReq 挂签品类请求参数
     * @return WrapperResp
     */
    @PostMapping("/editVisaCategory")
    public WrapperResp<Void> editVisaCategory(@RequestBody @Valid VisaCategoryReq visaCategoryReq) {
        return visaCategoryService.editVisaCategory(visaCategoryReq);
    }

    /**
     * 修改挂签品类状态
     *
     * @param visaCategoryStatusReq 挂签品类状态请求参数
     * @return 结果
     */
    @PostMapping("/updateVisaCategoryStatus")
    public WrapperResp<Void> updateVisaCategoryStatus(@RequestBody @Valid VisaCategoryStatusReq visaCategoryStatusReq) {
        return visaCategoryService.editVisaCategoryStatus(visaCategoryStatusReq);
    }

    /**
     * 删除挂签品类
     *
     * @param commonIdPageDataReq 挂签品类ID
     * @return 结果
     */
    @PostMapping("/deleteVisaCategory")
    public WrapperResp<Void> deleteVisaCategory(@RequestBody @Valid CommonIdPageDataReq commonIdPageDataReq) {
        return visaCategoryService.deleteVisaCategory(commonIdPageDataReq.getId());
    }

    /**
     * 新增挂签品类关联商品信息
     *
     * @param visaCategoryReq 挂签品类请求参数
     * @return 新增结果
     */
    @PostMapping("/addVisaCategoryCommodity")
    public WrapperResp<Void> addVisaCategoryCommodity(@RequestBody @Valid VisaCategoryCommodityReq visaCategoryReq) {
        return visaCategoryService.addVisaCategoryCommodity(visaCategoryReq);
    }

    /**
     * 删除挂签品类关联商品信息
     *
     * @param visaCategoryReq 挂签品类请求参数
     * @return 删除结果
     */
    @PostMapping("/deleteVisaCategoryCommodity")
    public WrapperResp<Void> deleteVisaCategoryCommodity(@RequestBody @Valid VisaCategoryCommodityReq visaCategoryReq) {
        return visaCategoryService.deleteVisaCategoryCommodity(visaCategoryReq);
    }

    /**
     * 查询关联挂签品类信息
     *
     * @return 结果
     */
    @GetMapping("/getVisaCategoryCommodity")
    public WrapperResp<PageDataResp<VisaCategoryResp>> getVisaCategoryCommodity(@Valid VisaCategoryNameReq visaCategoryNameReq) {
        return visaCategoryService.getVisaCategoryCommodity(visaCategoryNameReq);
    }

    /**
     * 新增签证
     *
     * @param visaReq 签证请求参数
     * @return 结果
     */
    @PostMapping("/addVisa")
    public WrapperResp<Void> addVisa(@RequestBody @Valid VisaReq visaReq) {
        return visaCategoryService.addVisa(visaReq);
    }

    /**
     * 编辑签证
     *
     * @param visaReq 签证请求参数
     * @return 结果
     */
    @PostMapping("/editVisa")
    public WrapperResp<Void> editVisa(@RequestBody @Valid VisaReq visaReq) {
        return visaCategoryService.editVisa(visaReq);
    }

    /**
     * 修改签证状态
     *
     * @param visaCategoryStatusReq 签证状态请求参数
     * @return 结果
     */
    @PostMapping("/updateVisaStatus")
    public WrapperResp<Void> updateVisaStatus(@RequestBody @Valid VisaCategoryStatusReq visaCategoryStatusReq) {
        return visaCategoryService.editVisaStatus(visaCategoryStatusReq);
    }

    /**
     * 删除签证
     *
     * @param commonIdReq 签证ID
     * @return 结果
     */
    @PostMapping("/deleteVisa")
    public WrapperResp<Void> deleteVisa(@RequestBody @Valid CommonIdReq commonIdReq) {
        return visaCategoryService.deleteVisa(commonIdReq.getId());
    }

    /**
     * 分页查询挂签
     *
     * @param visaNameReq 挂签请求参数
     * @return 结果
     */
    @PostMapping("/getVisaPage")
    public WrapperResp<PageDataResp<VisaPageResp>> getVisaPage(@RequestBody @Valid VisaNameReq visaNameReq) {
        return visaCategoryService.getVisaPage(visaNameReq);
    }

    /**
     * 查询挂签信息
     *
     * @param id 挂签ID
     * @return 结果
     */
    @GetMapping("/getVisa")
    public WrapperResp<VisaDetailResp> getVisa(@RequestParam Long id) {
        return visaCategoryService.getVisaInfo(id);
    }

    /**
     * 根据商品id，挂签配置价格信息
     *
     * @param visaCategoryOrderReq 挂签品类请求参数
     * @return 结果
     */
    @PostMapping("/updateVisaCategoryCommodityPrice")
    public WrapperResp<VisaCategoryOrderResp> updateVisaCategoryCommodityPrice(@RequestBody @Valid VisaCategoryOrderReq visaCategoryOrderReq) {
        return visaCategoryService.getVisaCategoryOrder(visaCategoryOrderReq);
    }

    /**
     * 查询挂签品类详情
     * @param commonIdReq 挂签品类ID
     * @return 挂签品类详情
     */
    @GetMapping("/getVisaCategoryDetail")
    public WrapperResp<VisaCategoryResp> getVisaCategoryDetail(@Valid CommonIdReq commonIdReq) {
        return visaCategoryService.getVisaCategoryById(commonIdReq);
    }

}
