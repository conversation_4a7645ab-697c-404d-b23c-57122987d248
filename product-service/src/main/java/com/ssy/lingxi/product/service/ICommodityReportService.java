package com.ssy.lingxi.product.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.ReportTodayResp;
import com.ssy.lingxi.product.model.resp.CommodityPlatformReportResp;
import com.ssy.lingxi.product.model.resp.CommodityReportResp;

/**
 * 首页-商品中心
 * <AUTHOR>
 * @version V3.0.0
 * @since 2023/12/7
 */
public interface ICommodityReportService {

    /**
     * 待办统计
     * @param sysUser 登录用户
     */
    CommodityReportResp getCommodity(UserLoginCacheDTO sysUser);

    /**
     * 今日新增--平台后台
     */
    ReportTodayResp getTodayNew();

    /**
     * 待办统计--平台后台
     */
    CommodityPlatformReportResp getPlatformCommodity();
}
