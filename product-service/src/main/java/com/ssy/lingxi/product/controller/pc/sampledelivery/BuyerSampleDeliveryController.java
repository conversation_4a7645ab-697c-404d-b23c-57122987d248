package com.ssy.lingxi.product.controller.pc.sampledelivery;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.enums.SampleDeliveryScenesEnum;
import com.ssy.lingxi.product.model.req.sampledelivery.*;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryBuyerManageQueryResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryBuyerQueryResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryCreateQualityOrderResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryDetailResp;
import com.ssy.lingxi.product.service.sampleDelivery.IBuyerSampleDeliveryService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 能力中心-商品能力-送样管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/23 13:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/sampleDeliver/buyer")
public class BuyerSampleDeliveryController extends BaseController {
    private final IBuyerSampleDeliveryService buyerSampleDeliveryService;

    /**
     * 送样需求单分页列表
     *
     * @param pageVO 分页查询传参
     * @return 分页列表
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<SampleDeliveryBuyerQueryResp>> page(SampleDeliveryBuyerPageDataReq pageVO) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(buyerSampleDeliveryService.page(pageVO, sysUser));
    }

    /**
     * 送样需求单管理分页列表-SRM
     */
    @GetMapping("/pageBySrmManage")
    public WrapperResp<PageDataResp<SampleDeliveryBuyerManageQueryResp>> pageBySrmManage(SampleDeliveryBuyerManagePageDataReq pageVO) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(buyerSampleDeliveryService.pageByManage(pageVO, sysUser, SampleDeliveryScenesEnum.SRM.getCode()));
    }

    /**
     * 送样需求单管理分页列表-B2B
     */
    @GetMapping("/pageByB2bManage")
    public WrapperResp<PageDataResp<SampleDeliveryBuyerManageQueryResp>> pageByB2bManage(SampleDeliveryBuyerManagePageDataReq pageVO) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(buyerSampleDeliveryService.pageByManage(pageVO, sysUser, SampleDeliveryScenesEnum.B2B.getCode()));
    }

    /**
     * 送样需求单详情
     *
     * @param commonIdReq id参数
     * @return 送样需求单详情
     */
    @GetMapping("/detail")
    public WrapperResp<SampleDeliveryDetailResp> detail(@Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(buyerSampleDeliveryService.detail(commonIdReq.getId(), sysUser));
    }

    /**
     * 新增送样需求单 -B2B
     *
     * @param sampleDeliverySaveReq 新增传参
     * @return 操作结果
     */
    @PostMapping("/b2b/create")
    public WrapperResp<Void> createB2b(@RequestBody @Valid SampleDeliverySaveReq sampleDeliverySaveReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        buyerSampleDeliveryService.create(sampleDeliverySaveReq, sysUser, SampleDeliveryScenesEnum.B2B.getCode());
        return WrapperUtil.success();
    }

    /**
     * 新增送样需求单 -SRM
     *
     * @param sampleDeliverySaveReq 新增传参
     * @return 操作结果
     */
    @PostMapping("/srm/create")
    public WrapperResp<Void> createSrm(@RequestBody @Valid SampleDeliverySaveReq sampleDeliverySaveReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        buyerSampleDeliveryService.create(sampleDeliverySaveReq, sysUser, SampleDeliveryScenesEnum.SRM.getCode());
        return WrapperUtil.success();
    }

    /**
     * 更新送样需求单
     *
     * @param sampleDeliveryUpdateReq 更新传参
     * @return 操作结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(@RequestBody @Valid SampleDeliveryUpdateReq sampleDeliveryUpdateReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        buyerSampleDeliveryService.update(sampleDeliveryUpdateReq, sysUser);
        return WrapperUtil.success();
    }

    /**
     * 删除送样需求单
     *
     * @param commonIdReq 主键id参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delete(@RequestBody @Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        buyerSampleDeliveryService.delete(commonIdReq.getId(), sysUser);
        return WrapperUtil.success();
    }

    /**
     * 取消送样需求单
     *
     * @param cancelVO 取消传参
     * @return 操作结果
     */
    @PostMapping("/cancel")
    public WrapperResp<Void> cancel(@RequestBody @Valid SampleDeliveryCancelReq cancelVO) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        buyerSampleDeliveryService.cancel(cancelVO, sysUser);
        return WrapperUtil.success();
    }

    /**
     * 提交送样需求单
     *
     * @param commonIdReq 主键id参数
     * @return 操作结果
     */
    @PostMapping("/submit")
    public WrapperResp<Void> submit(@RequestBody @Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        buyerSampleDeliveryService.submit(commonIdReq.getId(), sysUser);
        return WrapperUtil.success();
    }

    /**
     * 待收样送样需求单列表
     *
     * @param pageVO 分页查询传参
     * @return 分页列表
     */
    @GetMapping("/pageToReceive")
    public WrapperResp<PageDataResp<SampleDeliveryBuyerManageQueryResp>> pageToReceive(SampleDeliveryBuyerReceivePageDataReq pageVO) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(buyerSampleDeliveryService.pageToReceive(pageVO, sysUser));
    }

    /**
     * 确认收样
     *
     * @param commonIdReq 主键id参数
     * @return 操作结果
     */
    @PostMapping("/receiveSample")
    public WrapperResp<Void> receiveSample(@RequestBody @Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        buyerSampleDeliveryService.receiveSample(commonIdReq.getId(), sysUser);
        return WrapperUtil.success();
    }

    /**
     * “送样需求单” - 生成质检单所需数据
     *
     * @param commonIdReq 主键id参数
     * @return 查询结果
     */
    @GetMapping("/qualityCreateBySampleDelivery")
    public WrapperResp<SampleDeliveryCreateQualityOrderResp> qualityCreateBySampleDelivery(@Valid CommonIdReq commonIdReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(buyerSampleDeliveryService.qualityCreateBySampleDelivery(sysUser, commonIdReq.getId()));
    }

    /**
     * 退样
     *
     * @param sampleDeliveryReturnReq 退样传参
     * @return 操作结果
     */
    @PostMapping("/returnSample")
    public WrapperResp<Void> returnSample(@RequestBody @Valid SampleDeliveryReturnReq sampleDeliveryReturnReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        buyerSampleDeliveryService.returnSample(sampleDeliveryReturnReq, sysUser);
        return WrapperUtil.success();
    }
}
