package com.ssy.lingxi.product.service.freightSpace;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.api.product.FreightSpaceDataSyncReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.warehouse.*;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuStockResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.*;
import com.ssy.lingxi.product.entity.do_.freightSpace.FreightSpaceDO;
import com.ssy.lingxi.product.entity.do_.warehouse.WarehouseInventoryDO;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
* 仓位库存管理
* <AUTHOR>
* @since 2020/6/28
*/
public interface IFreightSpaceService {

    /**
    * 仓位库存列表查询
    * <AUTHOR>
    * @since 2020/6/28
    */
    PageDataResp<FreightSpaceListResp> freightSpaceList(FreightSpaceListReq request, UserLoginCacheDTO sysUser);

    /**
    * 停用&启用仓位库存管理
    * <AUTHOR>
    * @since 2020/6/28
    */
    void freightSpaceStopRoStart(FreightSpaceStopRoStartReq request, UserLoginCacheDTO sysUser);

    /**
    * 新增仓位库存
    * <AUTHOR>
    * @since 2020/6/29
    */
    void freightSpaceAdd(FreightSpaceAddReq request, UserLoginCacheDTO sysUser);

    /**
     * 新增库存
     * @param request 参数
     * @param sysUser 用户
     */
    void addFreightSpace(FreightSpaceAddReq request, UserLoginCacheDTO sysUser);

    /**
     * 批量新增仓位库存
     * <AUTHOR>
     * @since 2021/5/11
     */
    void freightSpaceAddBatch(FreightSpaceAddBatchReq request, UserLoginCacheDTO sysUser);

    /**
    * 仓位库存详情查询
    * <AUTHOR>
    * @since 2020/7/8
    */
    FreightSpaceDetailsResp freightSpaceDetails(FreightSpaceDetailsReq request, UserLoginCacheDTO sysUser);

    /**
    * 修改仓位信息
    * <AUTHOR>
    * @since 2020/7/9
    */
    void freightSpaceUpdate(FreightSpaceUpdateReq request, UserLoginCacheDTO sysUser);

    /**
     * 修改库存信息
     * @param request 参数
     * @param sysUser 用户
     */
    void freightSpaceUpdateNew(FreightSpaceUpdateReq request, UserLoginCacheDTO sysUser);

    /**
     * 仓位库存信息批量修改
     * @param request 参数
     */
    void freightSpaceUpdateBatch(FreightSpaceUpdateBatchReq request, UserLoginCacheDTO sysUser);

    /**
    * 仓位库存调拨-调出
    * <AUTHOR>
    * @since 2020/7/9
    */
    FreightSpaceAllotFoldResp freightSpaceAllotExport(FreightSpaceAllotReq request, UserLoginCacheDTO sysUser);

    /**
     * 仓位库存调拨-调入
     * <AUTHOR>
     * @since 2020/7/9
     */
    FreightSpaceAllotFoldResp freightSpaceAllotFold(FreightSpaceAllotReq request, UserLoginCacheDTO sysUser);

    /**
    * 根据商品ID查询仓位库存
    * <AUTHOR>
    * @since 2020/7/9
    */
    List<FreightSpaceProductIdListResp> freightSpaceByProductId(FreightSpaceProductIdListReq request, UserLoginCacheDTO sysUser);

    /**
    * 查询全部仓位
    * <AUTHOR>
    * @since 2020/8/7
    */
    List<FreightSpaceListResp> freightSpaceAll();

    /**
    * 查询仓位调拨记录ID
    * <AUTHOR>
    * @since 2020/8/14
    */
    PageDataResp<FreightSpaceAllotLogListResp> freightSpaceAllotFoldLog(FreightSpaceAllotLogListReq request);

    /**
    * 根据仓位ID查询仓位绑定的会员
    * <AUTHOR>
    * @since 2020/9/18
    */
    PageDataResp<ApplyMemberReq> freightSpaceMamberList(FreightSpaceAllotLogListReq request);

    /**
     * 查询商品的仓位集合
     * <AUTHOR>
     * @since 2020/11/4
     */
    List<FreightSpaceDO> getFreightSpace(InventoryByProductIdReq inventoryByProductIdReq);

    /**
     * 查询商品的仓位集合
     * <AUTHOR>
     * @since 2020/11/4
     */
    List<FreightSpaceDO> getFreightSpaceCustomInventory(FreightSpaceCustomInventoryReq freightSpaceCustomInventoryReq);

    /**
     * 同步仓位的可用库存和占用库存
     */
    void syncSaveFreightSpace(List<FreightSpaceDO> freightSpaceDOList);

    /**
    * 根据商品ID查询仓位扣减记录
    * <AUTHOR>
    * @since 2020/9/24
    */
    PageDataResp<FreightSpaceInventoryRecordResp> positionDeductionRecordList(UserLoginCacheDTO sysUser , PositionInventoryDeductionRecordReq request);

    /**
     * 根据商品ID查询仓位扣减记录- 平台后台
     */
    PageDataResp<FreightSpaceInventoryRecordResp> platformPositionDeductionRecordList(UserLoginCacheDTO sysUser , PositionInventoryDeductionRecordReq request);

    /**
     *查询商品的仓位库存
     */
    BigDecimal stockByProductId(StockByProductIdReq request, UserLoginCacheDTO sysUser);

    /**
     * 查询订单下每个商品对应的仓位占用库存
     */
    List<OrderCommodityOccupiedInventoryResp> getOrderCommodityOccupiedInventory(List<OrderCommodityOccupiedInventoryReq> orderCommodityOccupiedInventoryReqList);

    /**
     *批量查询商品的仓位库存-内部
     */
    List<StockByProductIdsResp> stockByProductIdInsides(StockByProductIdsReq request);

    /**
     *筛选适用会员角色为服务消费者的商品SKU
     */
    List<Long> stockByProductIdInsides(List<Long> skuIds, Long memberId, Long memberRoleId);

    /**
     * 仓位库存列表修改库存
     * @param request 参数
     */
    Boolean updateInventory(FreightSpaceUpdateInventoryReq request, UserLoginCacheDTO sysUser);

    /**
     * 自动创建仓位
     */
    void autoCreateFreightSpace(UserLoginCacheDTO sysUser, List<CommoditySkuStockResp> commodityDetailList);

    /**
     * 批量查询商品库存模式
     * @param idList 商品skuIdList
     */
    List<InventoryPatternResp> getInventoryPattern(List<Long> idList);

    /**
     * 将所有同步的仓位设置为不同步
     * @param sysUser 当前登录用户
     */
    void setNotSync(UserLoginCacheDTO sysUser);

    /**
     * 将仓位库存同步成仓库库存
     * @param inventoryList  仓库库存
     */
    void syncWarehouseInventory(List<WarehouseInventoryDO> inventoryList);

    /**
     * 数仓平台库存同步到商城
     * @param syncReq 参数
     */
    void freightSpaceDataSyncHandler(FreightSpaceDataSyncReq syncReq);

    /**
     * 根据skuID查询单品列表
     * @param skuId 规格ID
     * @return 结果
     */
    List<FreightSpaceSingleProductResp> getSingleProductList(Long skuId);

    /**
     * 导出单件列表
     * @param request 参数
     */
    void exportSingleProduct(FreightSpaceListReq request, UserLoginCacheDTO sysUser, HttpServletResponse response);
}
