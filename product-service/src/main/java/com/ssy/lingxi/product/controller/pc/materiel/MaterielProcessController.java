package com.ssy.lingxi.product.controller.pc.materiel;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.model.req.ProcessQueryReq;
import com.ssy.lingxi.product.model.req.materiel.*;
import com.ssy.lingxi.product.model.req.platform.SaveDefaultReq;
import com.ssy.lingxi.product.model.resp.materiel.MaterielProcessDetailResp;
import com.ssy.lingxi.product.model.resp.materiel.MaterielProcessPageQueryResp;
import com.ssy.lingxi.product.model.resp.materiel.MaterielProcessRelMaterielGroupQueryResp;
import com.ssy.lingxi.product.model.resp.materiel.MaterielProcessRelMaterielQueryResp;
import com.ssy.lingxi.product.model.resp.platform.PlatformBaseMaterielProcessResp;
import com.ssy.lingxi.product.service.materiel.IMaterielProcessService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 商品能力 - 物料流程规则配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/28 13:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/material/process")
public class MaterielProcessController extends BaseController {
    private final IMaterielProcessService service;

    /**
     * 分页查询物料流程规则
     * @param request 请求
     * @param pageVO 分页查询条件
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MaterielProcessPageQueryResp>> pageMaterialProcesses(HttpServletRequest request, @Valid MaterielProcessPageDataReq pageVO) {
        return WrapperUtil.success(service.pageMaterialProcesses(getSysUser(request), pageVO));
    }

    /**
     * 查询物料流程规则
     * @param request 请求
     * @param queryRequest 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public WrapperResp<List<MaterielProcessPageQueryResp>> listMaterialProcesses(HttpServletRequest request, @Valid ProcessQueryReq queryRequest) {
        return WrapperUtil.success(service.listMaterialProcesses(getSysUser(request), queryRequest));
    }

    /**
     * 设置物料默认流程
     * @param request HttpHeaders信息
     * @param defaultRequest 接口参数
     * @return Void
     */
    @PostMapping("/saveDefault")
    public WrapperResp<Void> saveDefault(HttpServletRequest request, @RequestBody @Valid SaveDefaultReq defaultRequest) {
        return WrapperUtil.success(service.saveDefault(getSysUser(request), defaultRequest));
    }


    /**
     * 新增物料流程规则页面 - 查询基础物料流程列表
     * @param request 请求域
     * @return 查询结果
     */
    @GetMapping("/base/list")
    public WrapperResp<List<PlatformBaseMaterielProcessResp>> listBaseMaterialProcesses(HttpServletRequest request, @Valid ProcessQueryReq queryRequest) {
        return WrapperUtil.success(service.listBaseMaterialProcesses(getSysUser(request), queryRequest));
    }

    /**
     * 物料流程规则新增
     * @param request 请求域
     * @param saveVO 物料流程规则
     * @return 新增结果
     */
    @PostMapping("/save")
    public WrapperResp<Void> save(HttpServletRequest request, @RequestBody @Valid MaterielProcessReq saveVO) {
        return WrapperUtil.success(service.save(getSysUser(request), saveVO));
    }

    /**
     * 查询物料流程规则详情
     * @param request 请求域
     * @param processId 流程id
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<MaterielProcessDetailResp> getInfo(HttpServletRequest request, @RequestParam("processId") Long processId) {
        return WrapperUtil.success(service.getInfo(getSysUser(request), processId));
    }

    /**
     * 查询物料组勾选树结构
     * @param request 请求域
     * @param processId 物料流程id
     * @return 查询结果
     */
    @GetMapping("/tree/rel/material/group")
    public WrapperResp<List<MaterielProcessRelMaterielGroupQueryResp>> treeRelMaterialGroup(HttpServletRequest request, @RequestParam("processId") Long processId) {
        return WrapperUtil.success(service.treeRelMaterialGroup(getSysUser(request), processId));
    }

    /**
     * 分页查询部分物料
     * @param request 请求域
     * @param pageVO 分页查询请求
     * @return 查询结果
     */
    @GetMapping("/page/rel/material")
    public WrapperResp<List<MaterielProcessRelMaterielQueryResp>> pageRelMaterial(HttpServletRequest request, @Valid MaterielProcessRelMaterielPageDataReq pageVO) {
        return WrapperUtil.success(service.listRelMaterial(getSysUser(request), pageVO));
    }

    /**
     * 修改物料流程规则
     * @param request 请求域
     * @param updateVO 修改数据
     * @return 修改结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(HttpServletRequest request, @RequestBody @Valid MaterielProcessUpdateReq updateVO) {
        return WrapperUtil.success(service.update(getSysUser(request), updateVO));
    }

    /**
     * 修改物料流程状态
     * @param request 请求域
     * @param updateStatusVO 修改物料状态
     * @return 修改结果
     */
    @PostMapping("/update/status")
    public WrapperResp<Void> updateStatus(HttpServletRequest request, @RequestBody @Valid MaterielProcessUpdateStatusReq updateStatusVO) {
        return WrapperUtil.success(service.updateStatus(getSysUser(request), updateStatusVO));
    }

    /**
     * 删除物料流程规则
     * @param request 请求域
     * @param processId 要删除的物料流程规则id
     * @return 删除结果
     */
    @GetMapping("/delete")
    public WrapperResp<Void> delete(HttpServletRequest request, @RequestParam("processId") Long processId) {
        return WrapperUtil.success(service.delete(getSysUser(request), processId));
    }

    /**
     * 查询物料是否配置了业务流程
     * @param request 请求域
     * @param queryVO 查询参数
     * @return 查询结果
     */
    @GetMapping("/is/exist/material/process")
    public WrapperResp<Boolean> isExistMaterialProcess(HttpServletRequest request, @Valid MaterielProcessRelReq queryVO) {
        return WrapperUtil.success(service.isExistMaterialProcess(getSysUser(request), queryVO));
    }

}
