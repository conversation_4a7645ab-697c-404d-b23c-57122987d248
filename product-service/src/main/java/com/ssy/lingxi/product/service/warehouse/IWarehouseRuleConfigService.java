package com.ssy.lingxi.product.service.warehouse;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseAutoEnterReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseSyncReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseAutoEnterResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseSyncResp;

import java.util.List;

/**
 * 库存规则配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/7/1
 */
public interface IWarehouseRuleConfigService {

    /**
     * 查询自动入库配置信息
     */
    WarehouseAutoEnterResp getWarehouseAutoEnter(UserLoginCacheDTO sysUser);

    /**
     * 新增/修改自动入库
     * @param warehouseAutoEnterReq 自动入库实体
     * @return 自动入库id
     */
    Long saveOrUpdateWarehouseAutoEnter(UserLoginCacheDTO sysUser, WarehouseAutoEnterReq warehouseAutoEnterReq);

    /**
     * 查询物料仓库库存同步配置信息
     */
    WarehouseSyncResp getWarehouseSync(UserLoginCacheDTO sysUser);

    /**
     * 新增/修改物料仓库库存同步配置
     * @param warehouseSyncReq 物料仓库库存同步配置实体
     * @return 物料仓库库存同步配置id
     */
    Long saveOrUpdateWarehouseSync(UserLoginCacheDTO sysUser, WarehouseSyncReq warehouseSyncReq);

    /**
     * 查询物料需要同步的会员角色
     * @param sysUser 当前登录用户
     */
    List<Long> getMaterialSyncMemberRoleIdList(UserLoginCacheDTO sysUser);

    /**
     * 查询仓库需要同步的会员角色
     * @param sysUser 当前登录用户
     */
    List<Long> getWarehouseSyncMemberRoleIdList(UserLoginCacheDTO sysUser);
}
