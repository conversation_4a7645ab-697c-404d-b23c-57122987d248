package com.ssy.lingxi.product.controller.pc.commodity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CommodityDraftReq;
import com.ssy.lingxi.product.api.model.resp.CommodityDraftResp;
import com.ssy.lingxi.product.service.commodity.IDraftService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 商品草稿管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodity/draft")
public class DraftController extends BaseController {
    private final IDraftService draftService;

    /**
     * 查询商品草稿列表
     * @param pageDataReq 分页实体
     */
    @GetMapping(value = "/getCommodityDraftList")
    public WrapperResp<PageDataResp<CommodityDraftResp>> getCommodityDraftList(PageDataReq pageDataReq) {
        return WrapperUtil.success(draftService.getCommodityDraftList(getSysUser(), pageDataReq));
    }

    /**
     * 查询商品草稿
     * @param commodityDraftId 商品草稿id
     */
    @GetMapping(value = "/getCommodityDraftById")
    public WrapperResp<CommodityDraftResp> getCommodityDraftById(@RequestParam("commodityDraftId") Long commodityDraftId) {
        return WrapperUtil.success(draftService.getCommodityDraftById(getSysUser(), commodityDraftId));
    }

    /**
     * 保存商品草稿
     */
    @PostMapping(value = "/saveOrUpdateCommodityDraft")
    public WrapperResp<Long> saveOrUpdateCommodityDraft(@RequestBody @Valid CommodityDraftReq commodityDraftReq){
        return WrapperUtil.success(draftService.saveOrUpdateCommodityDraft(getSysUser(), commodityDraftReq));
    }

    /**
     * 删除商品草稿
     */
    @PostMapping(value = "/deleteCommodityDraftBatch")
    public WrapperResp<Boolean> deleteCommodityDraftBatch(@RequestBody @Valid CommonIdListReq commonIdListRequest) {
        return WrapperUtil.success(draftService.deleteCommodityDraft(getSysUser(), commonIdListRequest.getIdList()));
    }
}
