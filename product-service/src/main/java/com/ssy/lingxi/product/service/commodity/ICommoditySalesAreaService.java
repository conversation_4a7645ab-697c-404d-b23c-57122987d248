package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySalesAreaTreeResp;
import com.ssy.lingxi.product.entity.do_.commodity.CommoditySalesAreaDO;
import com.ssy.lingxi.product.entity.do_.commodity.CommoditySalesAreaTemplateDO;
import com.ssy.lingxi.product.model.req.CommoditySalesAreaTemplateAddReq;
import com.ssy.lingxi.product.model.req.CommoditySalesAreaTemplateEditReq;

import java.util.List;

/**
 * 商品管理-销售区域
 *
 * <AUTHOR>
 */
public interface ICommoditySalesAreaService {

    /**
     * 销售区域列表(查看区域)
     *
     * @param templateId 销售区域模板ID
     */
    List<CommoditySalesAreaTreeResp> list(UserLoginCacheDTO sysUser, Long templateId);

    /**
     * 添加销售区域数据
     */
    void add(CommoditySalesAreaTemplateAddReq commoditySalesAreaTemplateAddReq, CommoditySalesAreaTemplateDO commoditySalesAreaTemplateDO);

    /**
     * 编辑销售区域数据
     */
    void edit(CommoditySalesAreaTemplateEditReq commoditySalesAreaTemplateEditReq, CommoditySalesAreaTemplateDO commoditySalesAreaTemplateDO);

    /**
     * 查询销售区域数据
     */
    List<CommoditySalesAreaDO> findByTemplateId(Long templateId);

}
