package com.ssy.lingxi.product.controller.pc;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.SaleGroupSaveReq;
import com.ssy.lingxi.product.api.model.req.StatusUpdateReq;
import com.ssy.lingxi.product.api.model.resp.SaleGroupDetailResp;
import com.ssy.lingxi.product.api.model.resp.SaleGroupResp;
import com.ssy.lingxi.product.service.ISaleGroupService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 销售群组管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-13
 */
@RestController
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/saleGroup")
public class SaleGroupController extends BaseController {

    @Resource
    private ISaleGroupService saleGroupService;

    /**
     * 新增或修改
     * @param saveReq 参数
     */
    @PostMapping("/addOrUpdate")
    public WrapperResp<Void> addOrUpdate(@RequestBody @Valid SaleGroupSaveReq saveReq){
        saleGroupService.addOrUpdate(saveReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 销售群组列表分页查询
     * @param groupName 参数
     * @param pageDataReq 分页
     */
    @GetMapping("/getListByPage")
    public WrapperResp<PageDataResp<SaleGroupResp>> getListByPage(@RequestParam(required = false) String groupName, @RequestParam(required = false) Integer status, PageDataReq pageDataReq){
        return WrapperUtil.success(saleGroupService.getListByPage(groupName, status, pageDataReq, getSysUser()));
    }

    /**
     * 修改状态
     * @param updateReq 参数
     */
    @PostMapping("/updateStatus")
    public WrapperResp<Void> updateStatus(@RequestBody @Valid StatusUpdateReq updateReq){
        saleGroupService.updateStatus(updateReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 销售群组详情
     * @param idReq 参数
     */
    @GetMapping("/getDetailById")
    public WrapperResp<SaleGroupDetailResp> getDetailById(CommonIdReq idReq){
        return WrapperUtil.success(saleGroupService.getDetailById(idReq.getId()));
    }

    /**
     * 删除群组
     * @param idReq 参数
     */
    @PostMapping("/deleteById")
    public WrapperResp<Void> deleteById(@RequestBody @Valid CommonIdReq idReq){
        saleGroupService.deleteById(idReq.getId(), getSysUser());
        return WrapperUtil.success();
    }
}
