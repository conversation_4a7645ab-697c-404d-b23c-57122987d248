package com.ssy.lingxi.product.controller.pc.customer;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.req.api.DataWarehousePlatformPushReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CustomerAttributeReq;
import com.ssy.lingxi.product.api.model.req.SimpleStatusReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityDescribeAttributeQueryReq;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeListResp;
import com.ssy.lingxi.product.api.model.resp.CustomerAttributeResp;
import com.ssy.lingxi.product.api.model.resp.tree.AttributeNodeResp;
import com.ssy.lingxi.product.entity.do_.customer.CustomerAttributeDO;
import com.ssy.lingxi.product.model.req.CommoditySkuAttributeReq;
import com.ssy.lingxi.product.model.req.CustomerCategoryAttributeReq;
import com.ssy.lingxi.product.model.resp.CommoditySkuAttributeAndBrandResp;
import com.ssy.lingxi.product.model.resp.CustomerCategoryAttributeResp;
import com.ssy.lingxi.product.service.customer.ICustomerAttributeService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品能力--属性管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/customer")
public class CustomerAttributeController extends BaseController {
    private final ICustomerAttributeService customerAttributeService;

    /**
     * 同步平台后台属性
     * @param commonIdListRequest 平台后台属性id集合
     * @return 操作结果
     */
    @PostMapping("/syncAttribute")
    public WrapperResp<Boolean> syncAttribute(@RequestBody CommonIdListReq commonIdListRequest) {
        return WrapperUtil.success(customerAttributeService.syncAttribute(getSysUser(), commonIdListRequest));
    }

    /**
     * 查询属性树
     * @param filterInput 是否需要过滤输入类型
     * @return Wrapper<List<Node>>
     */
    @GetMapping("/getCustomerAttributeTree")
    public WrapperResp<List<AttributeNodeResp>> getCustomerAttributeTree(@RequestParam(value = "filterInput", required = false) Boolean filterInput) {
        return WrapperUtil.success(customerAttributeService.getCustomerAttributeTree(getSysUser(), filterInput));
    }

    /**
     * 获取可选的属性
     *
     * @param commoditySkuAttributeReq 请求参数
     * @return 操作结果
     */
    @PostMapping("/getEffectiveAttribute")
    public WrapperResp<CommoditySkuAttributeAndBrandResp> getEffectiveAttribute(@RequestBody @Valid CommoditySkuAttributeReq commoditySkuAttributeReq) {
        return WrapperUtil.success(customerAttributeService.getEffectiveAttribute(commoditySkuAttributeReq));
    }

    /**
     * 查询属性信息
     * @param id 属性id
     * @return Wrapper<CustomerAttribute>
     */
    @GetMapping("/getCustomerAttribute")
    public WrapperResp<CustomerAttributeResp> getCustomerAttribute(@RequestParam("id") Long id) {
        CustomerAttributeDO customerAttribute = customerAttributeService.getCustomerAttribute(getSysUser(), id);
        return WrapperUtil.success(BeanUtil.copyProperties(customerAttribute, CustomerAttributeResp.class));
    }

    /**
     * 查询属性列表
     * @param pageDataReq 分页实体
     * @param name 属性名称
     * @return Wrapper<PageData<CustomerAttribute>>
     */
    @GetMapping("/getCustomerAttributeList")
    public WrapperResp<PageDataResp<CustomerAttributeResp>> getCustomerAttributeList(PageDataReq pageDataReq, @RequestParam("name") String name) {
        Page<CustomerAttributeDO> result = customerAttributeService.getCustomerAttributeList(getSysUser(), pageDataReq, name);
        List<CustomerAttributeResp> resultList = BeanUtil.copyToList(result.getContent(), CustomerAttributeResp.class);
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 新增/修改属性
     * @param customerAttributeReq 属性实体
     * @return 操作结果
     */
    @PostMapping("/saveOrUpdateCustomerAttribute")
    public WrapperResp<String> saveOrUpdateCustomerAttribute(@RequestBody CustomerAttributeReq customerAttributeReq){
        customerAttributeService.saveOrUpdateCustomerAttribute(getSysUser(), this.modelMapper.map(customerAttributeReq, CustomerAttributeDO.class));
        return WrapperUtil.success();
    }

    /**
     * 删除属性
     * @param commonIdReq 请求参数
     * @return 操作结果
     */
    @PostMapping("/deleteCustomerAttribute")
    public WrapperResp<String> deleteCustomerAttribute(@RequestBody CommonIdReq commonIdReq){
        return WrapperUtil.success(customerAttributeService.deleteCustomerAttribute(getSysUser(), commonIdReq.getId()));
    }

    /**
     * 启用/停用属性
     * @param simpleStatusReq 请求参数
     * @return 操作结果
     */
    @PostMapping("/updateCustomerAttributeStatus")
    public WrapperResp<String> updateCustomerAttributeStatus(@RequestBody SimpleStatusReq simpleStatusReq) {
        return WrapperUtil.success(customerAttributeService.updateCustomerAttributeStatus(getSysUser(), simpleStatusReq.getId(), simpleStatusReq.getIsEnable()));
    }


    /**
     * 查询会员品类属性及属性值
     * 只查规格属性
     *
     * @param customerAttributeRequest 查询会员品类属性及属性值的请求对象
     * @return 操作结果
     */
    @PostMapping("/getMemberCustomerCategoryAttribute")
    public WrapperResp<List<CustomerCategoryAttributeResp>> getMemberCustomerAttribute(@RequestBody @Valid CustomerCategoryAttributeReq customerAttributeRequest) {
        return WrapperUtil.success(customerAttributeService.getMemberCustomerAttribute(customerAttributeRequest));
    }

    /**
     * 商品筛选项获取属性分页列表
     *
     * @param queryReq 参数
     */
    @GetMapping("/getCustomerAttributeListByPage")
    public WrapperResp<PageDataResp<CommodityDescribeAttributeListResp>> getDetailListByPage(CommodityDescribeAttributeQueryReq queryReq) {
        return WrapperUtil.success(customerAttributeService.getDetailListByPage(queryReq));
    }

    /**
     * 手动同步属性数据
     * @param pushReq 参数
     */
    @PostMapping("/sendSyncToMq")
    public WrapperResp<Void> sendSyncToMq(@RequestBody @Valid DataWarehousePlatformPushReq pushReq){
        customerAttributeService.sendSyncToMq(pushReq);
        return WrapperUtil.success();
    }
}
