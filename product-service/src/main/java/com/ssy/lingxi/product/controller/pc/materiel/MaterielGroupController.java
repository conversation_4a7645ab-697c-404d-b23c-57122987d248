package com.ssy.lingxi.product.controller.pc.materiel;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.NodeResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.entity.do_.materiel.MaterielGroupDO;
import com.ssy.lingxi.product.model.req.materiel.MaterielGroupChangeParentReq;
import com.ssy.lingxi.product.model.req.materiel.MaterielGroupReq;
import com.ssy.lingxi.product.model.resp.materiel.MaterielGroupDetailResp;
import com.ssy.lingxi.product.service.materiel.IMaterielGroupService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品能力-物料组管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 22/03/22 16:50
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/materialGroup")
public class MaterielGroupController extends BaseController {
    private final IMaterielGroupService materialGroupService;

    /**
     * 获取物料组树
     * @param rootNodeId 根节点id
     * @param excludeId 排除id（用于选择移动物料组）
     * @return 树形列表
     */
    @GetMapping("/tree")
    public WrapperResp<List<NodeResp>> tree(@RequestParam(value = "rootNodeId", required = false) Long rootNodeId, @RequestParam(value = "excludeId",required = false) Long excludeId){
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(materialGroupService.getMaterialGroupTree(sysUser, rootNodeId, excludeId));

    }

    /**
     * 获取物料组详情
     * @param commonIdReq idVo
     * @return 物料组详情
     */
    @GetMapping("/detail")
    public WrapperResp<MaterielGroupDetailResp> detail(@Valid CommonIdReq commonIdReq){
        UserLoginCacheDTO sysUser = this.getSysUser();

        MaterielGroupDO materialGroup= materialGroupService.getMaterialGroup(commonIdReq.getId());
        if (materialGroup != null){
            MaterielGroupDetailResp materielGroupDetailResp = this.modelMapper.map(materialGroup, MaterielGroupDetailResp.class);

            return WrapperUtil.success(materielGroupDetailResp);
        }
        return WrapperUtil.success(null);

    }

    /**
     * 新增修改物料
     * @param materielGroupReq 物料组请求
     * @return 默认成功信息
     */
    @PostMapping("/saveOrUpdate")
    public WrapperResp<Void> add(@RequestBody @Valid MaterielGroupReq materielGroupReq){
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(materialGroupService.saveOrUpdateMaterialGroup(sysUser, this.modelMapper.map(materielGroupReq, MaterielGroupDO.class)));
    }

    /**
     * 移动物料组
     * @param materielGroupChangeParentRequst 移动物料组请求
     * @return 默认成功信息
     */
    @PostMapping("/changeParent")
    public WrapperResp<Void> changeParent(@RequestBody @Valid MaterielGroupChangeParentReq materielGroupChangeParentRequst){
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(materialGroupService.changeParent(sysUser, materielGroupChangeParentRequst));
    }

    /**
     * 删除物料组
     * @param commonIdReq idVo
     * @return 默认成功信息
     */
    @PostMapping("/delete")
    public WrapperResp<Void> delete(@RequestBody @Valid CommonIdReq commonIdReq){

        UserLoginCacheDTO sysUser = this.getSysUser();

        return WrapperUtil.success(materialGroupService.delete(sysUser, commonIdReq.getId()));
    }




}
