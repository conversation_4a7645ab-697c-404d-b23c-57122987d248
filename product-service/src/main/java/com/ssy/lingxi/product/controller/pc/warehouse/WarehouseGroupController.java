package com.ssy.lingxi.product.controller.pc.warehouse;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.StatusUpdateReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseGroupSaveReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseGroupResp;
import com.ssy.lingxi.product.service.warehouse.IWarehouseGroupService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 仓库组管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-12
 */
@RestController
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/warehouseGroup")
public class WarehouseGroupController extends BaseController {

    @Resource
    private IWarehouseGroupService warehouseGroupService;

    /**
     * 新增或编辑
     * @param saveReq 参数
     */
    @PostMapping("/addOrUpdate")
    public WrapperResp<Void> addOrUpdate(@RequestBody @Valid WarehouseGroupSaveReq saveReq){
        warehouseGroupService.addOrUpdate(saveReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 仓库组列表分页查询
     * @param name 参数
     * @param pageDataReq 分页
     */
    @GetMapping("/getListByPage")
    public WrapperResp<PageDataResp<WarehouseGroupResp>> getListByPage(@RequestParam(required = false) String name, @RequestParam(required = false) Integer status, PageDataReq pageDataReq){
        return WrapperUtil.success(warehouseGroupService.getListByPage(name, status, pageDataReq));
    }

    /**
     * 修改状态
     * @param updateReq 参数
     */
    @PostMapping("/updateStatus")
    public WrapperResp<Void> updateStatus(StatusUpdateReq updateReq){
        warehouseGroupService.updateStatus(updateReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 删除
     * @param idReq 参数
     */
    @PostMapping("/deleteById")
    public WrapperResp<Void> deleteById(@RequestBody CommonIdReq idReq){
        warehouseGroupService.deleteById(idReq.getId(), getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 获取仓库组详情
     * @param idReq 参数
     */
    @GetMapping("/getById")
    public WrapperResp<WarehouseGroupResp> getById(CommonIdReq idReq){
        return WrapperUtil.success(warehouseGroupService.getById(idReq.getId()));
    }
}
