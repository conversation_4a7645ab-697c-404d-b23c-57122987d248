package com.ssy.lingxi.product.service.warehouse;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.warehouse.*;
import com.ssy.lingxi.product.api.model.resp.warehouse.*;
import com.ssy.lingxi.product.entity.do_.warehouse.WarehouseInventoryDO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
* 库存管理
* <AUTHOR>
* @since 2020/6/29
*/
public interface IWarehouseInventoryService {

    /**
    * 查询库存列表
    * <AUTHOR>
    * @since 2020/6/29
    */
    PageDataResp<InventoryListResp> inventoryList(InventoryListReq request, UserLoginCacheDTO sysUser);

    /**
    * 单个/批量调整安全库存
    * <AUTHOR>
    * @since 2020/6/30
    */
    Void inventorySafetyUpdate(InventorySafetyUpdateReq request);

    /**
    * 查询仓库货品库存
    * <AUTHOR>
    * @since 2020/6/30
    */
    QueryMaterielInventoryResp queryMaterielInventory(QueryMaterielInventoryReq request);

    /**
    * 根据仓库和货号ID查询库存
    * <AUTHOR>
    * @since 2020/8/7
    */
    QueryInventoryByItemNoResp queryInventoryByItemNo(QueryInventoryByItemNoReq request);

    /**
     * 查询已经配置的仓库库存
     * @param sysUser 当前登录用户
     * @param materielIdList 物料id
     */
    List<WarehouseInventoryResp> getWarehouseInventory(UserLoginCacheDTO sysUser, List<Long> materielIdList);

    /**
     * 从其他角色同步库存数据到该角色下
     * @param sysUser 当前登录用户
     * @param inventoryMemberRoleId 需要同步的角色id
     */
    void syncInventory(UserLoginCacheDTO sysUser, Long inventoryMemberRoleId);

    /**
     * 通过仓库查询物料id
     * @param sysUser 当前登录用户
     * @param warehouseIdList 仓库id
     */
    List<Long> getMaterielIdListByWarehouse(UserLoginCacheDTO sysUser, List<Long> warehouseIdList);

    /**
     * 扣减库存时同步仓位的可用库存和占用库存
     * @param warehouseMap   key: 仓库id_物料id; value: 数量
     */
    void deductInventoryByFreightSpace(Map<String, BigDecimal> warehouseMap);

    /**
     * 返回库存时同步仓位的可用库存和占用库存
     * @param warehouseMap   key: 仓库id_物料id; value: 数量
     */
    void returnInventoryByFreightSpace(Map<String, BigDecimal> warehouseMap);

    /**
     * 释放占用库存时同步仓位的可用库存和占用库存
     * @param warehouseMap   key: 仓库id_物料id; value: 数量
     */
    void returnOccupiedInventoryByFreightSpace(Map<String, BigDecimal> warehouseMap);

    /**
     * 发货时扣减的可用库存
     * @param warehouseMap   key: 仓库id_物料id; value: 数量
     */
    void sendDeductInventoryByFreightSpace(Map<String, BigDecimal> warehouseMap);

    /**
     * 通过仓库id和物料id查询仓库信息
     * @param warehouseIdList   仓库id数组
     * @param materielIdList       物料id数组
     */
    List<WarehouseInventoryDO> queryWarehouseInventory(List<Long> warehouseIdList, List<Long> materielIdList);

    /**
     * 批量查询仓库下的物料对应的可配货库存
     * @param request 参数
     */
    List<DistributableInventoryResp> getWarehouseDistributableInventory(DistributableInventoryReq request);
}
