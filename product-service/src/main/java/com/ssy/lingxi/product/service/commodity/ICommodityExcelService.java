package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.util.thread.CommonThreadUtil;
import com.ssy.lingxi.component.datasheetFile.model.dto.BizHandleResultDTO;
import com.ssy.lingxi.component.datasheetFile.model.dto.CommodityImportDTO;
import com.ssy.lingxi.component.datasheetFile.model.dto.customParam.CommodityPicCustomParamDTO;
import com.ssy.lingxi.component.datasheetFile.model.dto.mq.DatasheetFileQueueMsgDTO;
import com.ssy.lingxi.component.datasheetFile.model.dto.mq.ResultDTO;
import com.ssy.lingxi.product.entity.do_.BrandDO;
import com.ssy.lingxi.product.entity.do_.commodity.CommodityDO;
import com.ssy.lingxi.product.entity.do_.customer.CustomerAttributeDO;
import com.ssy.lingxi.product.entity.do_.customer.CustomerAttributeValueDO;
import com.ssy.lingxi.product.entity.do_.customer.CustomerCategoryDO;
import com.ssy.lingxi.product.entity.do_.materiel.MaterielDO;
import com.ssy.lingxi.product.model.dto.commodity.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;

/**
 * 商品Excel相关操作管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface ICommodityExcelService {

    /**
     * IO型业务线程池
     */
    ThreadPoolExecutor EXECUTOR = CommonThreadUtil.threadPoolExecutorIo(ICommodityExcelService.class);

    /**
     * 导出商品导入模板
     */
    BizHandleResultDTO.ExportDTO commodityTemplateExport(DatasheetFileQueueMsgDTO.UserDTO userDTO);

    /**
     * 导入商品
     */
    Function<List<CommodityImportDTO>, ResultDTO> commodityImport(DatasheetFileQueueMsgDTO msgDTO);

    /**
     * 商品sku图片保存处理
     */
    void commoditySkuPicSaveHandler(CommodityPicCustomParamDTO customParamDTO, List<CommoditySkuPicImportDTO> commoditySkuPicImportDTOList);

    /**
     * 商品主图保存处理
     */
    void commodityMainPicHandler(CommodityPicCustomParamDTO customParamDTO, List<CommodityPicImportDTO> commodityPicImportDTOList);

    /**
     * 商品详情图处理
     */
    void commodityDetailsPicSaveHandler(CommodityPicCustomParamDTO customParamDTO, List<CommodityPicImportDTO> commodityPicImportDTOList);

    /**
     * 查询商品导入批次
     * @param importBath 商品导入批次
     */
    List<String> getCommodityImportBath(UserLoginCacheDTO sysUser, String importBath);

    /**
     * 通过商品导入批次删除商品
     * @param importBath 商品导入批次
     */
    Boolean deleteCommodityByImportBath(UserLoginCacheDTO sysUser, String importBath);

    /**
     * 保存excel导入的商品
     */
    void saveCommodityImport(DatasheetFileQueueMsgDTO msgDTO, List<CommodityImportDTO> commodityImportDTOList, Map<Long, CustomerCategoryDO> customerCategoryMap, Map<Long, BrandDO> brandMap, Map<String, MaterielDO> materielMap);

    /**
     * 商品关联数据处理
     */
    void commodityRelationDataHandle(DatasheetFileQueueMsgDTO msgDTO, List<CommodityImportDTO> commodityImportDTOList, Map<String, CommodityDO> commodityMap, Map<String, MaterielDO> materielMap, Map<Long, CustomerCategoryDO> customerCategoryMap);

    /**
     * 保存逻辑处理
     */
    void saveHandle(DatasheetFileQueueMsgDTO msgDTO, CommodityRelationDataDOResultDTO commodityRelationDataDOResultDTO, CompletableFuture<List<CustomerAttributeDO>> customerAttributeDOListCF, CompletableFuture<List<CustomerAttributeValueDO>> customerAttributeValueDOListCF);

    /**
     * 商品sku逻辑处理
     *
     * @return
     */
    CommoditySkuDOResultDTO skuHandle(DatasheetFileQueueMsgDTO.UserDTO userDTO, CommodityImportDTO commodityImportDTO, CommodityDO commodity, Map<String, MaterielDO> materielMap, Map<Long, CustomerCategoryDO> customerCategoryMap);

    /**
     * sku属性处理
     */
    CommoditySkuAttrDOResultDTO skuAttributeHandle(DatasheetFileQueueMsgDTO.UserDTO userDTO, CommodityDO commodity, Map<Long, CustomerCategoryDO> customerCategoryMap, String commodityAttributeImport, String commodityAttributeValueImport);

    /**
     * 导出商品sku纬度数据
     */
    void exportCommoditySkuByCommodityIdList(HttpServletResponse response, List<Long> commodityIdList);

    /**
     * 导出商品二维码
     * @param idList 商品id数组
     */
    void exportCommodityQRCode(UserLoginCacheDTO sysUser, List<Long> idList, HttpServletRequest request, HttpServletResponse response);

    /**
     * 商品图片目录结构导出
     */
    String commodityPicDirStructureExport(DatasheetFileQueueMsgDTO msgDTO);

    /**
     * 商品图片导入
     */
    Function<List<CommodityImportDTO>, ResultDTO> commodityPicImport(DatasheetFileQueueMsgDTO msgDTO);

}
