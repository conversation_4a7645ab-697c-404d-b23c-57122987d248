package com.ssy.lingxi.product.controller.pc.sampledelivery;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.service.sampleDelivery.IBaseSampleDeliveryService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 送样管理单通用接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/29 13:48
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/sampleDeliver")
public class SampleDeliveryController extends BaseController {
    private final IBaseSampleDeliveryService baseSampleDeliveryService;

    /**
     * 获取送样需求单状态下拉框数据
     *
     * @return 送样需求单状态下拉框数据
     */
    @GetMapping("/status/dropItems")
    public WrapperResp<List<DropdownItemResp>> statusDropItems() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(baseSampleDeliveryService.statusDropItems(sysUser));
    }

    /**
     * 获取送样需求单类型下拉框数据
     *
     * @return 送样需求单类型下拉框数据
     */
    @GetMapping("/type/dropItems")
    public WrapperResp<List<DropdownItemResp>> typeDropItems() {
        return WrapperUtil.success(baseSampleDeliveryService.typeDropItems());
    }

    /**
     * 获取送样需求单紧急程度下拉框数据
     *
     * @return 送样需求单紧急程度下拉框数据
     */
    @GetMapping("/emergencyLevel/dropItems")
    public WrapperResp<List<DropdownItemResp>> emergencyLevelDropItems() {
        return WrapperUtil.success(baseSampleDeliveryService.emergencyLevelDropItems());
    }


}
