package com.ssy.lingxi.product.controller.pc.platform;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.NodeResp;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.AttributeValueReq;
import com.ssy.lingxi.product.api.model.req.SimpleStatusReq;
import com.ssy.lingxi.product.api.model.resp.AttributeValueResp;
import com.ssy.lingxi.product.entity.do_.platform.AttributeValueDO;
import com.ssy.lingxi.product.service.platform.IAttributeValueService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 平台后台--属性值管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/platform")
public class AttributeValueController extends BaseController {
    private final IAttributeValueService attributeValueService;

    /**
     * 查询属性值树
     * @return Wrapper<List<Node>>
     */
    @GetMapping("/getAttributeValueTree")
    public WrapperResp<List<NodeResp>> getAttributeValueTree() {
        return WrapperUtil.success(attributeValueService.getAttributeValueTree());
    }

    /**
     * 查询属性值列表
     * @param isEnable 是否有效
     * @param attributeId 属性id
     * @param pageDataReq 分页实体
     * @param name 属性值名称
     * @return
     */
    @GetMapping("/getAttributeValueList")
    public WrapperResp<PageDataResp<AttributeValueResp>> getAttributeValueList(PageDataReq pageDataReq, @RequestParam(value = "isEnable", required = false) Boolean isEnable, @RequestParam("attributeId") Long attributeId, @RequestParam("name") String name) {
        Page<AttributeValueDO> result = attributeValueService.getAttributeValueList(pageDataReq, isEnable, attributeId, name);
        List<AttributeValueResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<AttributeValueResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 查询同步属性值列表
     * @param attributeGroupName 属性组名称
     * @param attributeName 属性名称
     * @param attributeValue 属性值
     * @param pageDataReq 分页实体
     * @return
     */
    @GetMapping("/getSyncAttributeValueList")
    public WrapperResp<PageDataResp<AttributeValueResp>> getSyncAttributeValueList(PageDataReq pageDataReq, @RequestParam(value = "attributeGroupName", required = false) String attributeGroupName, @RequestParam(value = "attributeName", required = false) String attributeName, @RequestParam(value = "attributeValue", required = false) String attributeValue) {
        Page<AttributeValueDO> result = attributeValueService.getSyncAttributeValueList(pageDataReq, attributeGroupName, attributeName, attributeValue);
        List<AttributeValueResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<AttributeValueResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 查询属性值信息
     * @param id 属性值id
     * @return Wrapper<AttributeValue>
     */
    @GetMapping("/getAttributeValue")
    public WrapperResp<AttributeValueResp> getAttributeValue(@RequestParam("id") Long id) {
        AttributeValueDO attributeValue = attributeValueService.getAttributeValue(id);
        if(attributeValue != null){
            return WrapperUtil.success(this.modelMapper.map(attributeValue, AttributeValueResp.class));
        }else{
            return WrapperUtil.success(null);
        }
    }

    /**
     * 新增/修改属性值
     * @param attributeValueReq
     * @return
     */
    @PostMapping("/saveOrUpdateAttributeValue")
    public WrapperResp<String> saveOrUpdateAttributeValue(@RequestBody AttributeValueReq attributeValueReq){
        return WrapperUtil.success(attributeValueService.saveOrUpdateAttributeValue(this.modelMapper.map(attributeValueReq, AttributeValueDO.class)));
    }

    /**
     * 删除属性值
     * @param commonIdReq 属性值id
     * @return
     */
    @PostMapping("deleteAttributeValue")
    public WrapperResp<String> deleteAttributeValue(@RequestBody CommonIdReq commonIdReq){
        return WrapperUtil.success(attributeValueService.deleteAttributeValue(commonIdReq.getId()));
    }

    /**
     * 启用/停用属性值
     * @param simpleStatusReq
     * @return
     * @throws Exception
     */
    @PostMapping("/updateAttributeValueStatus")
    public WrapperResp<String> updateAttributeValueStatus(@RequestBody SimpleStatusReq simpleStatusReq) {
        return WrapperUtil.success(attributeValueService.updateAttributeValueStatus(simpleStatusReq.getId(), simpleStatusReq.getIsEnable()));
    }
}
