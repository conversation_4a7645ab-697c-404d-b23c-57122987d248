package com.ssy.lingxi.product.service.esCommodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.NodeResp;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.EsCommoditySearchReq;
import com.ssy.lingxi.product.api.model.req.OrderReq;
import com.ssy.lingxi.product.api.model.req.ShareCodeReq;
import com.ssy.lingxi.product.api.model.resp.BaseNameUrlResp;
import com.ssy.lingxi.product.api.model.resp.BrandResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.*;
import com.ssy.lingxi.product.entity.esCommodity.EsCommodity;
import com.ssy.lingxi.product.model.resp.CategoryCommodityResp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * 商品搜索管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/28
 */
public interface IShopService {
    /**
     * 分页查询商品列表
     * @param searchRequest             请求参数
     * @param isHighlight               是否需要高亮显示
     * @param isScoreShop               是否是积分商城
     */
    PageDataResp<EsCommodityResp> searchCommodityList(EsCommoditySearchReq searchRequest, Boolean isHighlight, Boolean isScoreShop);

    /**
     * 查询商品列表
     * @param searchRequest
     * @return
     */
    List<EsCommodityResp> queryCommodityList(EsCommoditySearchReq searchRequest);

    /**
     * 查询商品详情
     * @param commodityId
     * @return
     */
    EsCommodityDetailResp getCommodityDetail(Long commodityId, Long shopId, UserLoginCacheDTO sysUser);

    /**
     * 通过商品skuId查询商品
     * @param commoditySkuId
     * @return
     */
    EsCommodityDetailResp getCommodityDetailBySkuId(Long commoditySkuId, Long shopId, UserLoginCacheDTO sysUser);

    /**
     * 查询商品库存
     * @param commodity
     * @param memberId
     * @return
     */
    void getCommodityStock(EsCommodity commodity, Long memberId, Long shopId);

    /**
     * 查询商品品类树
     * @param memberId 会员id
     * @return
     */
    List<NodeResp> getCategoryTree(Long shopId, Long memberId);

    /**
     * 查询属性
     * @param memberId
     * @param categoryId
     * @return
     */
    List<EsAttributeResp> getAttributeByCategoryId(Long shopId, Long memberId, Long categoryId);

    /**
     * 查询商品品牌
     * @return
     */
    List<EsBrandResp> getBrand(Long shopId, Long memberId);

    /**
     * 查询商品品牌
     * @return
     */
    List<EsBrandNodeResp> getPinyinSortBrand(Long shopId, Long memberId);

    /**
     * 查询商品最大价格/最大积分
     * @return
     */
    BigDecimal getCommodityMaxPrice(Long storeId);

    /**
     * 商品搜索自动补全
     * @param shopId            商城id
     * @param channelShopId     渠道商城id
     * @param name 商品名称
     * @return
     */
    List<String> getCommodityCompletion(Long shopId, Long channelShopId, String name);

    /**
     * 查询商品是否上架
     * @param commodityId
     * @return
     */
    Boolean getCommodityIsPublish(Long commodityId, Integer type, Long channelMemberId, Long memberId);



    /**################################################  APP  ##############################################################**/
    /**
     * 查询一级商品品类
     * @return
     */
    List<BaseNameUrlResp> getFirstCategory(Long shopId, Long memberId);

    /**
     * 根据品牌id查询品类下的品牌
     * @returnPOST
     */
    List<EsCategoryBrandResp> getCategoryByBrand(List<Long> brandIdList, Long shopId);

    /**
     * 根据商品id查询品类下的商品
     * @return
     */
    List<CategoryCommodityResp> getCategoryByCommodityId(List<Long> commodityIdList);

    /**
     * 根据一级品类查询品牌
     * @param categoryId 一级品类id
     * @returnPOST
     */
    Set<BrandResp> getBrandByFirstCategory(Long categoryId, Long shopId);

    /**
     * 根据一级品类查询品牌
     * @param customerCategoryId 一级品类id
     * @returnPOST
     */
    Set<BrandResp> getBrandByFirstCustomerCategory(Long customerCategoryId, Long shopId);

    /**
     * 根据不同会员匹配价格策略
     * @param commodity
     * @param shopId
     * @param loginMemberId
     * @param loginMemberRoleId
     */
    void setCommodityPrice(EsCommodity commodity, Long shopId, Long loginMemberId, Long loginMemberRoleId, Integer loginMemberLevel);

    /**
     * 根据不同会员匹配价格策略
     * @param commodity
     * @param shopId
     * @param loginMemberId
     * @param loginMemberRoleId
     * @param updatePrice 更新价格 用于判断是否根据价格策略更新商品展示价格
     */
    void setCommodityPrice(EsCommodity commodity, Long shopId, Long loginMemberId, Long loginMemberRoleId, Integer loginMemberLevel, Boolean updatePrice);

    /**
     * 保存分享码
     * @param shareCodeReq
     * @return
     */
    Boolean saveShareCode(ShareCodeReq shareCodeReq);

    /**
     * 查询分享码
     * @param code
     * @return
     */
    String getShareCode(String code);

    /**
     * 通过商品skuId查询商品库存
     * @param shopId            商城id
     * @param memberId          会员id
     * @param commoditySkuId    商品skuId
     */
    BigDecimal getCommodityStockBySkuId(Long shopId, Long memberId, Long commoditySkuId);

    /**
     * 通过商品skuId查询商品仓位库存
     *
     * @param shopId         商城id
     * @param commoditySkuId 商品skuId
     * @param positionId     仓位id
     * @param warehouseId    仓库id
     */
    BigDecimal getCommodityPositionStockBySkuId(Long shopId, Long commoditySkuId, Long positionId, Long warehouseId);

    /**
     * 查询商品sku信息
     * @param esCommoditySearchReq 请求参数
     */
    List<EsCommoditySkuResp> getCommoditySkuList(EsCommoditySearchReq esCommoditySearchReq);

    /**
     * 查询商品会员
     * @param orderReq 参数
     */
    List<Long> getMemberIdList(OrderReq orderReq);
}
