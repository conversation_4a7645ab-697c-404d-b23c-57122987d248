package com.ssy.lingxi.product.controller.pc.commodity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.product.CommodityStatusEnum;
import com.ssy.lingxi.component.base.enums.product.PriceTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.SimpleStatusReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityQueryReq;
import com.ssy.lingxi.product.api.model.req.commodity.UnitPriceStrategyAddReq;
import com.ssy.lingxi.product.api.model.req.commodity.UnitPriceStrategyCommodityReq;
import com.ssy.lingxi.product.api.model.req.commodity.UnitPriceStrategyReq;
import com.ssy.lingxi.product.api.model.resp.commodity.CommodityResp;
import com.ssy.lingxi.product.api.model.resp.commodity.UnitPriceMemberResp;
import com.ssy.lingxi.product.api.model.resp.commodity.UnitPriceStrategyDetailResp;
import com.ssy.lingxi.product.api.model.resp.commodity.UnitPriceStrategyListResp;
import com.ssy.lingxi.product.service.commodity.ICommodityService;
import com.ssy.lingxi.product.service.commodity.IUnitPriceStrategyService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品价格策略管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodity/unitPriceStrategy")
public class UnitPriceStrategyController extends BaseController {
    private final ICommodityService commodityService;
    private final IUnitPriceStrategyService unitPriceStrategyService;

    /**
     * 查询价格策略列表
     * @param pageDataReq 分页实体
     * @param unitPriceStrategyReq 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getUnitPriceStrategyList")
    public WrapperResp<PageDataResp<UnitPriceStrategyListResp>> getUnitPriceStrategyList(PageDataReq pageDataReq, UnitPriceStrategyReq unitPriceStrategyReq) {
        return WrapperUtil.success(unitPriceStrategyService.getUnitPriceStrategyList(pageDataReq, getSysUser(), unitPriceStrategyReq));
    }

    /**
     * 查询商品列表(新增价格策略)
     * @param unitPriceStrategyCommodity 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getStrategyCommodityList")
    public WrapperResp<PageDataResp<CommodityResp>> getStrategyCommodityList(UnitPriceStrategyCommodityReq unitPriceStrategyCommodity) {
        CommodityQueryReq commodityQueryReq = new CommodityQueryReq();
        commodityQueryReq.setName(unitPriceStrategyCommodity.getName());
        commodityQueryReq.setBrandId(unitPriceStrategyCommodity.getBrandId());
        commodityQueryReq.setCustomerCategoryId(unitPriceStrategyCommodity.getCustomerCategoryId());
        commodityQueryReq.setShopId(unitPriceStrategyCommodity.getShopId());
        commodityQueryReq.setType(unitPriceStrategyCommodity.getType());
        commodityQueryReq.setEnvironment(unitPriceStrategyCommodity.getEnvironment());
        //商品定价为现货价格的商品才能设置价格策略
        List<Integer> priceTypeList = new ArrayList<>();
        priceTypeList.add(PriceTypeEnum.CASH.getCode());
        commodityQueryReq.setPriceTypeList(priceTypeList);
        //已上架的商品
        List<Integer> statusList = new ArrayList<>();
        statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
        commodityQueryReq.setStatusList(statusList);

        UserLoginCacheDTO sysUser = this.getSysUser();
        commodityQueryReq.setMemberId(sysUser.getMemberId());
        commodityQueryReq.setMemberRoleId(sysUser.getMemberRoleId());

        return WrapperUtil.success(commodityService.getCommodityPage(commodityQueryReq));
    }

    /**
     * 新增/修改价格策略
     * @param unitPriceStrategyAddReq 价格策略实体
     * @return 操作结果
     */
    @PostMapping(value = "/saveOrUpdateUnitPriceStrategy")
    public WrapperResp<Long> saveOrUpdateUnitPriceStrategy(@RequestBody @Valid UnitPriceStrategyAddReq unitPriceStrategyAddReq){
        return WrapperUtil.success(unitPriceStrategyService.saveOrUpdateUnitPriceStrategy(getSysUser(), unitPriceStrategyAddReq));
    }

    /**
     * 查询价格策略
     * @param id 价格策略id
     * @return 操作结果
     */
    @GetMapping(value = "/getUnitPriceStrategy")
    public WrapperResp<UnitPriceStrategyDetailResp> getUnitPriceStrategy(@RequestParam("id") Long id){
        return WrapperUtil.success(unitPriceStrategyService.getUnitPriceStrategy(id));
    }

    /**
     * 通过商品id获取价格策略的所有会员
     * @param commodityId   商品id
     * @param shopId        商城id
     * @return 操作结果
     */
    @GetMapping(value = "/getStrategyMemberByCommodityId")
    public WrapperResp<List<UnitPriceMemberResp>> getStrategyMemberByCommodityId(@RequestParam("commodityId") Long commodityId, @RequestParam("shopId") Long shopId){
        return WrapperUtil.success(unitPriceStrategyService.getStrategyMemberByCommodityId(commodityId, shopId));
    }

    /**
     * 启用/停用价格策略
     * @param simpleStatusReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/updateUnitPriceStrategyStatus")
    public WrapperResp<String> updateUnitPriceStrategyStatus(@RequestBody @Valid SimpleStatusReq simpleStatusReq) {
        return WrapperUtil.success(unitPriceStrategyService.updateUnitPriceStrategyStatus(getSysUser(), simpleStatusReq.getId(), simpleStatusReq.getIsEnable()));
    }

    /**
     * 删除价格策略
     * @param commonIdReq 请求参数
     * @return 操作结果
     */
    @PostMapping(value = "/deleteUnitPriceStrategy")
    public WrapperResp<String> deleteUnitPriceStrategy(@RequestBody @Valid CommonIdReq commonIdReq){
        return WrapperUtil.success(unitPriceStrategyService.deleteUnitPriceStrategy(commonIdReq.getId()));
    }

}
