package com.ssy.lingxi.product.controller.pc.shop;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.EsCommoditySearchReq;
import com.ssy.lingxi.product.api.model.req.TemplateCommoditySearchReq;
import com.ssy.lingxi.product.api.model.resp.CategoryBaseResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsBrandResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import com.ssy.lingxi.product.service.shop.ITemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 模板相关
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodity/template/")
public class TemplateController {
    private final IShopService esCommodityService;
    private final ITemplateService templateCommodityService;

    /**
     * 查询品牌信息
     * @param current 当前页码
     * @param pageSize 每页条数
     * @param customerCategoryId 会员分类id
     * @param categoryId 平台分类id
     * @param memberId 会员id
     * @param name 品牌名称
     * @param idInList 包含
     * @param idNotInList 不包含
     */
    @GetMapping(value = "getBrandList")
    public WrapperResp<PageDataResp<EsBrandResp>> getBrandList(@RequestParam("current") Integer current, @RequestParam("pageSize") Integer pageSize,
                                                               @RequestParam(value = "shopId", required = false) Long shopId,
                                                               @RequestParam(value = "customerCategoryId",required = false) Long customerCategoryId,
                                                               @RequestParam(value = "categoryId",required = false) Long categoryId,
                                                               @RequestParam(value = "memberId",required = false) Long memberId,
                                                               @RequestParam(value = "memberRoleId",required = false) Long memberRoleId,
                                                               @RequestParam(value = "memberName",required = false) String memberName,
                                                               @RequestParam(value = "name",required = false) String name,
                                                               @RequestParam(value = "idInList",required = false) List<Long> idInList,
                                                               @RequestParam(value = "idNotInList",required = false) List<Long> idNotInList){

        //构造查询条件
        PageDataReq pageDataReq = new PageDataReq();
        pageDataReq.setCurrent(current);
        pageDataReq.setPageSize(pageSize);
        EsCommoditySearchReq esCommoditySearchReq = new EsCommoditySearchReq();
        esCommoditySearchReq.setCategoryId(categoryId);
        esCommoditySearchReq.setCustomerCategoryId(customerCategoryId);
        esCommoditySearchReq.setName(name);
        esCommoditySearchReq.setMemberId(memberId);
        esCommoditySearchReq.setMemberRoleId(memberRoleId);
        esCommoditySearchReq.setMemberName(memberName);
        esCommoditySearchReq.setIdInList(idInList);
        esCommoditySearchReq.setIdNotInList(idNotInList);
        esCommoditySearchReq.setShopId(shopId);

        return WrapperUtil.success(templateCommodityService.searchBrandList(pageDataReq, esCommoditySearchReq));
    }

    /**
     * 查询一级品类信息
     * @param shopId            商城id
     * @param memberId          会员id
     * @param memberRoleId      会员角色id
     */
    @GetMapping(value = "getFirstCategoryListByMemberId")
    public WrapperResp<List<CategoryBaseResp>> getFirstCategoryListByMemberId(
            @RequestParam(value = "shopId") Long shopId,
            @RequestParam(value = "memberId", required = false) Long memberId,
            @RequestParam(value = "memberRoleId", required = false) Long memberRoleId){
        //return WrapperUtil.success(templateCommodityService.getFirstCategoryList(shopId, memberId, memberRoleId));
        return WrapperUtil.success(templateCommodityService.getFirstCategoryListV2(shopId, memberId, memberRoleId));
    }

    /**
     * 查询商品信息
     * @param templateCommoditySearchReq 请求参数
     */
    @PostMapping(value = "searchCommodityList")
    public WrapperResp<PageDataResp<EsCommodityResp>> searchCommodityList(@RequestBody TemplateCommoditySearchReq templateCommoditySearchReq) {
        //构造查询条件
        EsCommoditySearchReq esCommoditySearchReq = BeanUtil.copyProperties(templateCommoditySearchReq, EsCommoditySearchReq.class);
        return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, false, false));
    }

    /**
     * 查询二级品类信息
     * @param shopId            商城id
     * @param memberId          会员id
     * @param memberRoleId      会员角色id
     * @param categoryId        品类id
     */
    @GetMapping(value = "getSecondCategoryListByMemberId")
    public WrapperResp<List<CategoryBaseResp>> getSecondCategoryListByMemberId(
            @RequestParam(value = "shopId") Long shopId,
            @RequestParam(value = "memberId", required = false) Long memberId,
            @RequestParam(value = "memberRoleId", required = false) Long memberRoleId,
            @RequestParam(value = "categoryId") Long categoryId){
        return WrapperUtil.success(templateCommodityService.getSecondCategoryList(shopId, memberId, memberRoleId, categoryId));
    }

}
