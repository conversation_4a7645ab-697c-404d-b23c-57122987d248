package com.ssy.lingxi.product.controller.feign;

import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.feign.IPurchaseFeign;
import com.ssy.lingxi.product.model.req.PurchaseQueryReq;
import com.ssy.lingxi.product.service.shop.IPurchaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * 商城--进货单管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/4
 */
@RestController
@RequiredArgsConstructor
public class PurchaseFeignController extends BaseController implements IPurchaseFeign {
    private final IPurchaseService purchaseService;

    /**
     * 删除进货单
     * @param commonIdListRequest id集合
     * @return 删除结果
     */
    @Override
    public WrapperResp<String> deletePurchase(@RequestBody CommonIdListReq commonIdListRequest){
        return WrapperUtil.success(purchaseService.deletePurchase(commonIdListRequest));
    }

    /**
     * 查询会员在商城的进货单里面商品的数量(内部接口)
     */
    @Override
    public WrapperResp<BigDecimal> getPurchaseCommodityCount(@RequestParam("shopId") Long shopId, @RequestParam("memberId") Long memberId, @RequestParam("memberRoleId") Long memberRoleId, @RequestParam("commoditySkuId") Long commoditySkuId){
        PurchaseQueryReq purchaseQueryReq = new PurchaseQueryReq();
        purchaseQueryReq.setShopId(shopId);
        purchaseQueryReq.setMemberId(memberId);
        purchaseQueryReq.setMemberRoleId(memberRoleId);
        purchaseQueryReq.setCommoditySkuId(commoditySkuId);
        return WrapperUtil.success(purchaseService.getPurchaseCommodityCount(purchaseQueryReq));
    }

}
