package com.ssy.lingxi.product.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.CommodityFilterConfigSaveReq;
import com.ssy.lingxi.product.api.model.req.StatusUpdateReq;
import com.ssy.lingxi.product.api.model.resp.CommodityFilterConfigDetailResp;
import com.ssy.lingxi.product.api.model.resp.CommodityFilterConfigResp;

/**
 * 商品筛选配置服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-06
 */
public interface ICommodityFilterConfigService {

    /**
     * 新增或修改筛选配置
     * @param saveReq 参数
     * @param sysUser 用户
     */
    void saveOrUpdate(CommodityFilterConfigSaveReq saveReq, UserLoginCacheDTO sysUser);

    /**
     * 筛选配置列表分页
     * @param name 名称
     * @param pageDataReq 分页
     * @param sysUser 用户
     * @return 结果
     */
    PageDataResp<CommodityFilterConfigResp> getListByPage(String name, PageDataReq pageDataReq, UserLoginCacheDTO sysUser);

    /**
     * 删除配置
     * @param id ID
     * @param sysUser 用户
     */
    void deleteById(Long id, UserLoginCacheDTO sysUser);

    /**
     * 修改配置状态
     * @param updateReq 参数
     * @param sysUser 用户
     */
    void updateStatus(StatusUpdateReq updateReq, UserLoginCacheDTO sysUser);

    /**
     * 修改筛选项属性值状态
     * @param updateReq 参数
     * @param sysUser 用户
     */
    void updateValueStatus(StatusUpdateReq updateReq, UserLoginCacheDTO sysUser);

    /**
     * 获取配置详情
     * @param id ID
     * @return 结果
     */
    CommodityFilterConfigDetailResp getDetailById(Long id);
}
