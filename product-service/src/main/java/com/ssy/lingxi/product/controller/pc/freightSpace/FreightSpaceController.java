package com.ssy.lingxi.product.controller.pc.freightSpace;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.api.product.FreightSpaceDataSyncReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.warehouse.*;
import com.ssy.lingxi.product.api.model.resp.feign.CommoditySkuStockResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.*;
import com.ssy.lingxi.product.service.freightSpace.IFreightSpaceService;
import com.ssy.lingxi.product.service.freightSpace.IInventoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * 仓位管理-仓位库存管理
 * <AUTHOR>
 * @since 2020/7/20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX)
public class FreightSpaceController extends BaseController {
    private final IInventoryService inventoryService;
    private final IFreightSpaceService freightSpaceService;

    /**
     * 仓位库存列表查询
     * @param request 参数
     */
    @GetMapping("/freight/space/list")
    public WrapperResp<PageDataResp<FreightSpaceListResp>> freightSpaceList(FreightSpaceListReq request) {
        return WrapperUtil.success(freightSpaceService.freightSpaceList(request, getSysUser()));
    }

    /**
    * 仓位管理-查询全部仓位
    * <AUTHOR>
    * @since 2020/8/7
    */
    @GetMapping("/freight/space/all")
    public WrapperResp<List<FreightSpaceListResp>> freightSpaceAll() {
        return WrapperUtil.success(freightSpaceService.freightSpaceAll());
    }

    /**
     * 仓位库存停用&启用
     * @param request 参数
     */
    @PostMapping("/freight/space/stop/start")
    public WrapperResp<Void> freightSpaceStopRoStart(@Validated @RequestBody FreightSpaceStopRoStartReq request) {
        freightSpaceService.freightSpaceStopRoStart(request, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 仓位库存添加
     * @param request 参数
     */
    @PostMapping("/freight/space/add")
    public WrapperResp<Void> freightSpaceAdd(@Validated @RequestBody FreightSpaceAddReq request) {
        freightSpaceService.addFreightSpace(request, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 仓位库存批量添加
     * @param request 参数
     */
    @PostMapping("/freight/space/addBatch")
    public WrapperResp<Void> freightSpaceAddBatch(@Validated @RequestBody FreightSpaceAddBatchReq request) {
        freightSpaceService.freightSpaceAddBatch(request, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 仓位库存信息修改
     * @param request 参数
     */
    @PostMapping("/freight/space/updata")
    public WrapperResp<Void> freightSpaceUpdate(@Validated @RequestBody FreightSpaceUpdateReq request) {
        freightSpaceService.freightSpaceUpdateNew(request, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 仓位库存列表修改库存
     * @param request 参数
     */
    @PostMapping("/freight/space/updateInventory")
    public WrapperResp<Boolean> updateInventory(@Valid @RequestBody FreightSpaceUpdateInventoryReq request) {
        return WrapperUtil.success(freightSpaceService.updateInventory(request,getSysUser()));
    }

    /**
     * 仓位库存信息批量修改
     * @param request 参数
     */
    @PostMapping("/freight/space/updateBatch")
    public WrapperResp<Void> freightSpaceUpdateBatch(@Validated @RequestBody FreightSpaceUpdateBatchReq request) {
        freightSpaceService.freightSpaceUpdateBatch(request, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 仓位库存详情
     * @param request 参数
     */
    @GetMapping("/freight/space/details")
    public WrapperResp<FreightSpaceDetailsResp> freightSpaceDetails(FreightSpaceDetailsReq request) {
        return WrapperUtil.success(freightSpaceService.freightSpaceDetails(request, getSysUser()));
    }

    /**
     * 仓位库存调拨-调出
     * @param request 参数
     */
    @PostMapping("/freight/space/allot/export")
    public WrapperResp<FreightSpaceAllotFoldResp> freightSpaceAllotExport(@RequestBody FreightSpaceAllotReq request) {
        return WrapperUtil.success(freightSpaceService.freightSpaceAllotExport(request, getSysUser()));
    }

    /**
     * 仓位库存调拨-调入
     * @param request 参数
     */
    @PostMapping("/freight/space/allot/fold")
    public WrapperResp<FreightSpaceAllotFoldResp> freightSpaceAllotFold(@RequestBody FreightSpaceAllotReq request) {
        return WrapperUtil.success(freightSpaceService.freightSpaceAllotFold(request, getSysUser()));
    }

    /**
     * 仓位库存调拨记录
     * @param request 参数
     */
    @GetMapping("/freight/space/allot/fold/log")
    public WrapperResp<PageDataResp<FreightSpaceAllotLogListResp>> freightSpaceAllotFoldLog(FreightSpaceAllotLogListReq request) {
        return WrapperUtil.success(freightSpaceService.freightSpaceAllotFoldLog(request));
    }

    /**
     * 根据仓位ID查询仓位绑定的会员
     * @param request 参数
     */
    @GetMapping("/freight/space/mamber/list")
    public WrapperResp<PageDataResp<ApplyMemberReq>> freightSpaceMemberList(FreightSpaceAllotLogListReq request) {
        return WrapperUtil.success(freightSpaceService.freightSpaceMamberList(request));
    }

    /**
     * 根据仓位库存和商品ID查询仓位库存列表
     * @param request 参数
     */
    @GetMapping("/freight/space/list/by/productid")
    public WrapperResp<List<FreightSpaceProductIdListResp>> freightSpaceByProductId(FreightSpaceProductIdListReq request) {
        return WrapperUtil.success(freightSpaceService.freightSpaceByProductId(request, getSysUser()));
    }

    /**
     * 根据商品ID查询仓位扣减记录
     * @param request 参数
     */
    @GetMapping("/position/deduction/record/list")
    public WrapperResp<PageDataResp<FreightSpaceInventoryRecordResp>> positionDeductionRecordList(PositionInventoryDeductionRecordReq request) {
        return WrapperUtil.success(freightSpaceService.positionDeductionRecordList(getSysUser(), request));
    }

    /**
     * 根据商品ID查询仓位扣减记录-平台后台
     * @param request 参数
     */
    @GetMapping("/platform/position/deduction/record/list")
    public WrapperResp<PageDataResp<FreightSpaceInventoryRecordResp>> platformPositionDeductionRecordList(PositionInventoryDeductionRecordReq request) {
        return WrapperUtil.success(freightSpaceService.platformPositionDeductionRecordList(getSysUser(), request));
    }

    /**
     * 查询商品的仓位库存
     * @param request 参数
     */
    @GetMapping("/stock/byProductId")
    public WrapperResp<BigDecimal> stockByProductId(StockByProductIdReq request) {
        return WrapperUtil.success(freightSpaceService.stockByProductId(request, getSysUser()));
    }

    /**
     * 批量查询商品库存模式
     * @param commonIdListReq 商品skuId数组
     */
    @PostMapping("/freight/getInventoryPattern")
    public WrapperResp<List<InventoryPatternResp>> getInventoryPattern(@RequestBody CommonIdListReq commonIdListReq) {
        return WrapperUtil.success(freightSpaceService.getInventoryPattern(commonIdListReq.getIdList()));
    }

    /**
     * 查询sku库存
     */
    @GetMapping("/freight/getCommoditySkuStockList")
    public WrapperResp<List<CommoditySkuStockResp>> getCommoditySkuRedisStockList(@Valid CommoditySkuStockReq commoditySkuStockReq) {
        return WrapperUtil.success(inventoryService.getCommoditySkuRedisStockList(commoditySkuStockReq.getShopIdList(), commoditySkuStockReq.getCommoditySkuIdList(), commoditySkuStockReq.getMemberId(), commoditySkuStockReq.getCustomerMemberId()));
    }

    /**
     * 根据skuID查询单品列表
     * @param idReq 规格ID
     */
    @GetMapping("/getSingleProductList")
    public WrapperResp<List<FreightSpaceSingleProductResp>> getSingleProductList(CommonIdReq idReq){
        getSysUser();
        return WrapperUtil.success(freightSpaceService.getSingleProductList(idReq.getId()));
    }

    /**
     * 手动处理库存同步
     * @param syncReq 参数
     */
    @PostMapping("/freightSpaceDataSyncHandler")
    public WrapperResp<Void> freightSpaceDataSyncHandler(@RequestBody FreightSpaceDataSyncReq syncReq){
        freightSpaceService.freightSpaceDataSyncHandler(syncReq);
        return WrapperUtil.success();
    }

    /**
     * 单件库存列表导出
     * @param request 参数
     * @param response 响应
     */
    @PostMapping("/exportSingleProduct")
    public void exportSingleProduct(@RequestBody FreightSpaceListReq request, HttpServletResponse response){
        freightSpaceService.exportSingleProduct(request, getSysUser(), response);
    }
}
