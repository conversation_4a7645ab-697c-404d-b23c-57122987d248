package com.ssy.lingxi.product.controller.pc.shop;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CommodityFilterConfigSaveReq;
import com.ssy.lingxi.product.api.model.req.StatusUpdateReq;
import com.ssy.lingxi.product.api.model.resp.CommodityFilterConfigDetailResp;
import com.ssy.lingxi.product.api.model.resp.CommodityFilterConfigResp;
import com.ssy.lingxi.product.service.ICommodityFilterConfigService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 商城商品筛选项配置管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-06-07
 */
@RestController
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/commodityFilterConfig")
public class CommodityFilterConfigController extends BaseController {

    @Resource
    private ICommodityFilterConfigService commodityFilterConfigService;

    /**
     * 保存或修改配置
     * @param saveReq 参数
     */
    @PostMapping("/saveOrUpdate")
    public WrapperResp<Void> saveOrUpdate(@RequestBody @Valid CommodityFilterConfigSaveReq saveReq){
        commodityFilterConfigService.saveOrUpdate(saveReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 筛选配置列表分页
     * @param name 名称
     * @param pageDataReq 分页
     */
    @GetMapping("/getListByPage")
    public WrapperResp<PageDataResp<CommodityFilterConfigResp>> getListByPage(@RequestParam(required = false) String name, PageDataReq pageDataReq){
        return WrapperUtil.success(commodityFilterConfigService.getListByPage(name, pageDataReq, getSysUser()));
    }

    /**
     * 删除配置
     * @param idReq 参数
     */
    @PostMapping("/deleteById")
    public WrapperResp<Void> deleteById(@RequestBody @Valid CommonIdReq idReq){
        commodityFilterConfigService.deleteById(idReq.getId(), getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 修改筛选项配置状态
     * @param updateReq 参数
     */
    @PostMapping("/updateStatus")
    public WrapperResp<Void> updateStatus(@RequestBody @Valid StatusUpdateReq updateReq){
        commodityFilterConfigService.updateStatus(updateReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 修改筛选项属性值状态
     * @param updateReq 参数
     */
    @PostMapping("/updateValueStatus")
    public WrapperResp<Void> updateValueStatus(@RequestBody @Valid StatusUpdateReq updateReq){
        commodityFilterConfigService.updateValueStatus(updateReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 根据ID获取配置详情
     * @param commonIdReq 参数
     */
    @GetMapping("/getDetailById")
    public WrapperResp<CommodityFilterConfigDetailResp> getDetailById(CommonIdReq commonIdReq){
        return WrapperUtil.success(commodityFilterConfigService.getDetailById(commonIdReq.getId()));
    }
}
