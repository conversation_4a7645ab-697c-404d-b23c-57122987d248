package com.ssy.lingxi.product.controller.pc.freightSpace;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.warehouse.FreightSpaceRuleConfigReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.FreightSpaceRuleConfigResp;
import com.ssy.lingxi.product.service.freightSpace.IFreightSpaceRuleConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 仓位管理-仓位库存规则配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/7/5
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/freightSpaceRuleConfig")
public class FreightSpaceRuleConfigController extends BaseController {
    private final IFreightSpaceRuleConfigService freightSpaceRuleConfigService;

    /**
     * 查询仓位库存规则配置信息
     */
    @GetMapping("/getFreightSpaceRuleConfig")
    public WrapperResp<FreightSpaceRuleConfigResp> getFreightSpaceRuleConfig() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(freightSpaceRuleConfigService.getFreightSpaceRuleConfig(sysUser));
    }

    /**
     * 新增/修改仓位库存规则配置
     * @param freightSpaceRuleConfigReq 仓位库存规则配置实体
     * @return 仓位库存规则配置id
     */
    @PostMapping("/saveOrUpdateFreightSpaceRuleConfig")
    public WrapperResp<Long> saveOrUpdateFreightSpaceRuleConfig(@RequestBody FreightSpaceRuleConfigReq freightSpaceRuleConfigReq){
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(freightSpaceRuleConfigService.saveOrUpdateFreightSpaceRuleConfig(sysUser, freightSpaceRuleConfigReq));
    }
}
