package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySalesAreaTemplateResp;
import com.ssy.lingxi.product.model.req.CommoditySalesAreaTemplateAddReq;
import com.ssy.lingxi.product.model.req.CommoditySalesAreaTemplateEditReq;
import com.ssy.lingxi.product.model.resp.CommoditySalesAreaTemplateDetailResp;
import com.ssy.lingxi.product.model.resp.CommoditySalesAreaTemplateListResp;

/**
 * 商品管理-销售区域模板管理
 *
 * <AUTHOR>
 */
public interface ICommoditySalesAreaTemplateService {

    /**
     * 新增模板
     */
    void add(UserLoginCacheDTO sysUser, CommoditySalesAreaTemplateAddReq commoditySalesAreaTemplateAddReq);

    /**
     * 编辑模板
     */
    void edit(UserLoginCacheDTO sysUser, CommoditySalesAreaTemplateEditReq commoditySalesAreaTemplateEditReq);

    /**
     * 列表查询
     */
    PageDataResp<CommoditySalesAreaTemplateListResp> list(UserLoginCacheDTO sysUser, PageDataReq pageDataReq);

    /**
     * 销售区域详情
     *
     * @param id 销售区域模板ID
     */
    CommoditySalesAreaTemplateDetailResp detail(Long id, Long loginMemberId, Long loginMemberRoleId);

    /**
     * 销售区域详情
     *
     * @param id 销售区域模板ID
     */
    CommoditySalesAreaTemplateResp getCommoditySalesAreaTemplate(Long id);

    /**
     * 销售区域详情
     *
     * @param id 销售区域模板ID
     */
    CommoditySalesAreaTemplateDetailResp getCommoditySalesAreaTemplateDetail(Long id);

}
