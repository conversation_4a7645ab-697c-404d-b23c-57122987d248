package com.ssy.lingxi.product.controller.pc.platform;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.NodeResp;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CategoryReq;
import com.ssy.lingxi.product.api.model.resp.CategoryDetailResp;
import com.ssy.lingxi.product.api.model.resp.CategoryResp;
import com.ssy.lingxi.product.entity.do_.platform.CategoryDO;
import com.ssy.lingxi.product.service.platform.ICategoryService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台--品类管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/platform")
public class CategoryController extends BaseController {
    private final ICategoryService categoryService;

    /**
     * 查询品类树
     * @param rootNodeId 根节点的key,空代表查询全部
     * @param excludeType 排除品类类型 品类类型：1-实物商品、2-虚拟商品、3-服务商品、4-积分兑换商品
     */
    @GetMapping("/getCategoryTree")
    public WrapperResp<List<NodeResp>> getCategoryTree(@RequestParam(value = "rootNodeId", required = false) Long rootNodeId, Integer excludeType) {
        return WrapperUtil.success(categoryService.getCategoryTree(rootNodeId,excludeType));
    }

    /**
     * 查询品类列表
     * @param pageDataReq 分页实体
     * @param name 品类名称
     */
    @GetMapping(value = "/getCategoryList")
    public WrapperResp<PageDataResp<CategoryResp>> getCategoryList(PageDataReq pageDataReq, @RequestParam("name") String name) {
        Page<CategoryDO> result = categoryService.getCategoryList(pageDataReq, name);
        List<CategoryResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<CategoryResp>>() {}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 查询品类信息
     * @param id 品类id
     */
    @GetMapping(value = "/getCategory")
    public WrapperResp<CategoryResp> getCategory(@RequestParam("id") Long id) {
        return WrapperUtil.success(categoryService.getCategory(id));
    }

    /**
     * 查询品类信息--包括品类、属性、属性值
     * @param id 品类id
     */
    @GetMapping(value = "/getCategoryById")
    public WrapperResp<CategoryDetailResp> getCategoryById(@RequestParam("id") Long id) {
        return WrapperUtil.success(categoryService.getCategoryById(id));
    }

    /**
     * 新增/修改品类
     * @param categoryReq 品类实体
     */
    @PostMapping(value = "/saveOrUpdateCategory")
    public WrapperResp<Void> saveOrUpdateCategory(@RequestBody @Valid CategoryReq categoryReq){
        categoryService.saveOrUpdateCategory(categoryReq);
        return WrapperUtil.success();
    }

    /**
     * 删除品类
     * @param commonIdReq   参数
     */
    @PostMapping(value = "deleteCategory")
    public WrapperResp<Void> deleteCategory(@RequestBody CommonIdReq commonIdReq){
        categoryService.deleteCategory(commonIdReq.getId());
        return WrapperUtil.success();
    }

    /**
     * 排序
     */
    @PostMapping(value = "/category/sort")
    public WrapperResp<Void> sort(@RequestBody CommonIdListReq commonIdListReq){
        UserLoginCacheDTO sysUser = this.getSysUser();
        categoryService.sort(sysUser, commonIdListReq);
        return WrapperUtil.success();
    }

}
