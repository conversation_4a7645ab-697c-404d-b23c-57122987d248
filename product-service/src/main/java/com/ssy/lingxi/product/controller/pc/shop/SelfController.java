package com.ssy.lingxi.product.controller.pc.shop;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.CommodityFilterConfigDTO;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.resp.ProvinceResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.EsCommoditySearchReq;
import com.ssy.lingxi.product.api.model.req.SelfShopSearchCustomerReq;
import com.ssy.lingxi.product.api.model.req.SelfShopSearchReq;
import com.ssy.lingxi.product.api.model.resp.CategoryTreeBrandResp;
import com.ssy.lingxi.product.api.model.resp.CustomerAttributeValueBaseResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsAttributeResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsBrandResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import com.ssy.lingxi.product.service.shop.ISelfService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 自营商城
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/self")
public class SelfController extends BaseController {
    private final ISelfService selfService;
    private final IShopService esCommodityService;

    /**
     * 查询商品会员品类树
     * @param memberId 会员id
     * @return 商品会员品类树
     */
    @GetMapping("/getCustomerCategoryTree")
    public WrapperResp<List<CategoryTreeBrandResp>> getCustomerCategoryTree(@RequestParam(value = "memberId") Long memberId) {
        Long shopId = this.getHeadersShopId();
        //return WrapperUtil.success(selfService.getCategoryTreeAndBrand(shopId, memberId));
        return WrapperUtil.success(selfService.getCategoryTree());
    }

    /**
     * 查询商品属性
     * @param memberId 会员id
     * @param categoryId 品类id
     * @return 商品属性
     */
    @GetMapping(value = "/getCustomerAttributeByCategoryId")
    public WrapperResp<List<EsAttributeResp>> getCustomerAttributeByCategoryId(@RequestParam(value = "memberId") Long memberId, @RequestParam(value = "categoryId") Long categoryId) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(selfService.getAttributeByCategoryId(shopId, memberId, categoryId));
    }

    /**
     * 查询商品品牌
     * @param memberId 会员id
     * @return 商品品牌
     */
    @GetMapping(value = "/getBrand")
    public WrapperResp<List<EsBrandResp>> getBrand(@RequestParam(value = "memberId") Long memberId) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(selfService.getBrand(shopId, memberId));
    }

    /**
     * 查询归属地市
     * @return 归属地市
     */
    @GetMapping("/getArea")
    public WrapperResp<List<ProvinceResp>> getArea() {
        return WrapperUtil.success(AreaUtil.findAllAsTree());
    }

    /**
     * 查询商品最大价格(积分)
     * @param memberId 会员id
     * @return 商品最大价格(积分)
     */
    @GetMapping("/getCommodityMaxPrice")
    public WrapperResp<BigDecimal> getCommodityMaxPrice(@RequestParam(value = "memberId", required = false) Long memberId) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(selfService.getCommodityMaxPrice(shopId, memberId));
    }

    /**
     * 查询商品列表
     * @param selfShopSearchReq 查询条件实体
     * @return 商品列表
     */
    @PostMapping(value = "/getCommodityList")
    public WrapperResp<PageDataResp<EsCommodityResp>> searchCommodityList(@RequestBody SelfShopSearchReq selfShopSearchReq) {
        Long shopId = this.getHeadersShopId();
        Long memberId = selfShopSearchReq.getMemberId();
        if(memberId != null && memberId > 0){
            EsCommoditySearchReq esCommoditySearchReq = this.modelMapper.map(selfShopSearchReq, EsCommoditySearchReq.class);
            esCommoditySearchReq.setShopId(shopId);
            //判断是否已登录
            if(this.isLogin()){
                UserLoginCacheDTO sysUser = this.getSysUser();
                esCommoditySearchReq.setLoginMemberId(sysUser.getMemberId());
                esCommoditySearchReq.setLoginMemberRoleId(sysUser.getMemberRoleId());
            }
            return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, true, false));
        }else{
            throw new BusinessException(ResponseCodeEnum.PRODUCT_MEMBER_NOT_EXIST);
        }
    }

    /**
     * 查询商品列表(积分商城)
     * @param selfShopSearchReq 查询条件实体
     * @return 商品列表
     */
    @PostMapping(value = "/getScoreCommodityList")
    public WrapperResp<PageDataResp<EsCommodityResp>> getScoreCommodityList(@RequestBody SelfShopSearchReq selfShopSearchReq) {
        Long shopId = this.getHeadersShopId();
        Long memberId = selfShopSearchReq.getMemberId();
        if(memberId != null && memberId > 0){
            EsCommoditySearchReq esCommoditySearchReq = this.modelMapper.map(selfShopSearchReq, EsCommoditySearchReq.class);
            esCommoditySearchReq.setShopId(shopId);
            //判断是否已登录
            if(this.isLogin()){
                UserLoginCacheDTO sysUser = this.getSysUser();
                esCommoditySearchReq.setLoginMemberId(sysUser.getMemberId());
                esCommoditySearchReq.setLoginMemberRoleId(sysUser.getMemberRoleId());
            }
            return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, true, true));
        }else{
            throw new BusinessException(ResponseCodeEnum.PRODUCT_MEMBER_NOT_EXIST);
        }
    }

    /**
     * 查询商品列表--代客下单
     * @param selfShopSearchCustomerReq 查询条件实体
     * @return 商品列表
     */
    @PostMapping(value = "/getCustomerCommodityList")
    public WrapperResp<PageDataResp<EsCommodityResp>> getCustomerCommodityList(@RequestBody SelfShopSearchCustomerReq selfShopSearchCustomerReq) {
        Long shopId = this.getHeadersShopId();
        Long memberId = selfShopSearchCustomerReq.getMemberId();
        if(memberId != null && memberId > 0){
            EsCommoditySearchReq esCommoditySearchReq = this.modelMapper.map(selfShopSearchCustomerReq, EsCommoditySearchReq.class);
            esCommoditySearchReq.setShopId(shopId);
            esCommoditySearchReq.setLoginMemberId(selfShopSearchCustomerReq.getCustomerMemberId());
            esCommoditySearchReq.setLoginMemberRoleId(selfShopSearchCustomerReq.getCustomerMemberRoleId());
            return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, true, false));
        }else{
            throw new BusinessException(ResponseCodeEnum.PRODUCT_MEMBER_NOT_EXIST);
        }
    }

    /**
     * 查询商品列表(积分商城)--代客下单
     * @param selfShopSearchCustomerReq 查询条件实体
     * @return 商品列表
     */
    @PostMapping(value = "/getCustomerScoreCommodityList")
    public WrapperResp<PageDataResp<EsCommodityResp>> getCustomerScoreCommodityList(@RequestBody SelfShopSearchCustomerReq selfShopSearchCustomerReq) {
        Long shopId = this.getHeadersShopId();
        Long memberId = selfShopSearchCustomerReq.getMemberId();
        if(memberId != null && memberId > 0){
            EsCommoditySearchReq esCommoditySearchReq = this.modelMapper.map(selfShopSearchCustomerReq, EsCommoditySearchReq.class);
            esCommoditySearchReq.setShopId(shopId);
            esCommoditySearchReq.setLoginMemberId(selfShopSearchCustomerReq.getCustomerMemberId());
            esCommoditySearchReq.setLoginMemberRoleId(selfShopSearchCustomerReq.getCustomerMemberRoleId());
            return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, true, true));
        }else{
            throw new BusinessException(ResponseCodeEnum.PRODUCT_MEMBER_NOT_EXIST);
        }
    }

    /**
     * 商城获取所有启用的商品筛选项
     */
    @GetMapping("/getAllFilterConfigList")
    public WrapperResp<List<CommodityFilterConfigDTO>> getAllFilterConfigList(){
        return WrapperUtil.success(selfService.getAllFilterConfigList());
    }

    /**
     * 获取成色属性值列表
     */
    @GetMapping("/getFinenessAttributeValueList")
    public WrapperResp<List<CustomerAttributeValueBaseResp>> getFinenessAttributeValueList(){
        return WrapperUtil.success(selfService.getAttributeValueList());
    }
}
