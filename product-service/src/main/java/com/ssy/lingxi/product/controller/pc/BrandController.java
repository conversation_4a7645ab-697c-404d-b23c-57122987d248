package com.ssy.lingxi.product.controller.pc;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.BrandReq;
import com.ssy.lingxi.product.api.model.req.CheckReq;
import com.ssy.lingxi.product.api.model.req.SimpleStatusReq;
import com.ssy.lingxi.product.api.model.resp.BrandResp;
import com.ssy.lingxi.product.entity.do_.BrandCheckRecordDO;
import com.ssy.lingxi.product.entity.do_.BrandDO;
import com.ssy.lingxi.product.service.IBrandService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 品牌管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/brand")
public class BrandController extends BaseController {
    private final IBrandService brandService;

    /**
     * 查询品牌信息--商品能力
     * @param id 品牌id
     */
    @GetMapping("/getBrand")
    public WrapperResp<BrandResp> getBrand(@RequestParam Long id) {
        return WrapperUtil.success(brandService.getBrand(getSysUser(), id));
    }

    /**
     * 查询品牌列表--商品能力
     * @param pageDataReq 分页实体
     * @param name 品牌名称
     * @param status 品牌状态(0-代表查全部,1-待提交审核,2-待审核,3-审核不通过,4-审核通过)
     * @return Wrapper<PageData<BrandResponse>>
     */
    @GetMapping("/getBrandList")
    public WrapperResp<PageDataResp<BrandResp>> getBrandList(PageDataReq pageDataReq, @RequestParam("name") String name, @RequestParam(value = "status", required = false) Integer status) {
        Page<BrandDO> result = brandService.getBrandList(getSysUser(), pageDataReq, name, status);
        List<BrandResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<BrandResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 查询待审核品牌列表--商品能力
     * @param pageDataReq 分页实体
     * @param status 品牌状态(0-代表查全部,2-待审核,3-审核不通过,4-审核通过)
     * @return Wrapper<PageData<Brand>>
     */
    @GetMapping("/getUnCheckBrandList")
    public WrapperResp<PageDataResp<BrandResp>> getUnCheckBrandList(PageDataReq pageDataReq, @RequestParam(value = "name",defaultValue = "") String name, @RequestParam(value = "memberName",defaultValue = "") String  memberName, @RequestParam(value = "status") Integer status) {
        Page<BrandDO> result = brandService.getUnCheckBrandList(getSysUser(), pageDataReq, name,memberName,status);
        List<BrandResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<BrandResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 审核品牌--商品能力
     * @param checkReq 审核
     * @return 操作结果
     */
    @PostMapping("/checkBrand")
    public WrapperResp<Long> checkBrand(@RequestBody CheckReq checkReq) {
        return WrapperUtil.success(brandService.checkBrand(getSysUser(), checkReq));
    }

    /**
     * 新增/修改品牌--商品能力
     * @param brandReq 品牌实体
     * @return 品牌id
     */
    @PostMapping("/saveOrUpdateBrand")
    public WrapperResp<Long> saveOrUpdateBrand(@RequestBody @Valid BrandReq brandReq){
        return WrapperUtil.success(brandService.saveOrUpdateBrand(getSysUser(), brandReq));
    }

    /**
     * 删除品牌--商品能力
     * @param commonIdReq 请求实体
     * @return Wrapper<String>
     */
    @PostMapping(value = "/deleteBrand")
    public WrapperResp<Void> deleteBrand(@RequestBody @Valid CommonIdReq commonIdReq){
        brandService.deleteBrand(getSysUser(), commonIdReq.getId());
        return WrapperUtil.success();
    }

    /**
     * 启用/停用品牌--商品能力
     * @param simpleStatusReq 请求实体
     * @return Wrapper<String>
     */
    @PostMapping(value = "/updateBrandEnable")
    public WrapperResp<Void> updateBrandEnable(@RequestBody SimpleStatusReq simpleStatusReq) {
        brandService.updateBrandEnable(getSysUser(), simpleStatusReq.getId(), simpleStatusReq.getIsEnable());
        return WrapperUtil.success();
    }

    /**
     * 提交审核品牌--商品能力
     * @param commonIdReq 品牌
     * @return Wrapper<String>
     */
    @PostMapping(value = "/applyCheckBrand")
    public WrapperResp<Void> applyCheckBrand(@RequestBody CommonIdReq commonIdReq) {
        brandService.applyCheckBrand(getSysUser(), commonIdReq.getId());
        return WrapperUtil.success();
    }

    /**
     * 查询品牌审核记录
     * @param brandId 品牌id
     * @return Wrapper<List<BrandCheckRecord>>
     */
    @GetMapping("/getBrandCheckRecord")
    public WrapperResp<List<BrandCheckRecordDO>> getBrandCheckRecord(@RequestParam("brandId") Long brandId) {
        return WrapperUtil.success(brandService.getBrandCheckRecord(brandId));
    }

    /******************************************* 平台后台 *****************************************************/

    /**
     * 查询品牌列表--平台后台
     * @param pageDataReq 分页实体
     * @param name 品牌名称
     * @param status 品牌状态(0-代表查全部,2-待审核,3-审核不通过,4-审核通过)
     * @return Wrapper<PageData<Brand>>
     */
    @GetMapping("/getPlatformBrandList")
    public WrapperResp<PageDataResp<BrandResp>> getPlatformBrandList(PageDataReq pageDataReq,
                                                                     @RequestParam("name") String name,
                                                                     @RequestParam(value = "status", required = false) Integer status,
                                                                     @RequestParam(value = "memberName", required = false) String memberName) {
        Page<BrandDO> result = brandService.getPlatformBrandList(pageDataReq, name, status, memberName);
        List<BrandResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<BrandResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 查询待审核品牌列表--平台后台
     * @param pageDataReq 分页实体
     * @param status 品牌状态(0-代表查全部,2-待审核,3-审核不通过,4-审核通过)
     * @return Wrapper<PageData<Brand>>
     */
    @GetMapping("/getPlatformUnCheckBrandList")
    public WrapperResp<PageDataResp<BrandResp>> getPlatformUnCheckBrandList(PageDataReq pageDataReq,
                                                                            @RequestParam(value = "status") Integer status,
                                                                            @RequestParam(value = "name",required = false)String name,
                                                                            @RequestParam(value = "memberName", required = false)String memberName) {
        Page<BrandDO> result = brandService.getPlatformUnCheckBrandList(pageDataReq, status,name, memberName);
        List<BrandResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<BrandResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 查询品牌信息--平台后台
     * @param id 品牌id
     * @return Wrapper<Brand>
     */
    @GetMapping("/getPlatformBrand")
    public WrapperResp<BrandResp> getPlatformBrand(@RequestParam("id") Long id) {
        return WrapperUtil.success(brandService.getBrand(id));
    }

    /**
     * 审核品牌--平台后台
     * @param checkReq 审核
     * @return Wrapper<Long>
     */
    @PostMapping("/checkPlatformBrand")
    public WrapperResp<Long> checkPlatformBrand(@RequestBody CheckReq checkReq) {
        return WrapperUtil.success(brandService.checkPlatformBrand(getSysUser(), checkReq));
    }

    /**
     * 查询品牌审核记录-平台后台
     * @param brandId 品牌id
     * @return Wrapper<List<BrandCheckRecord>>
     */
    @GetMapping("/getPlatformBrandCheckRecord")
    public WrapperResp<List<BrandCheckRecordDO>> getPlatformBrandCheckRecord(@RequestParam("brandId") Long brandId) {
        List<BrandCheckRecordDO> list = brandService.getBrandCheckRecord(brandId);
        return WrapperUtil.success(list);
    }
}
