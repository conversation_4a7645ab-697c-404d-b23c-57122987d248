package com.ssy.lingxi.product.controller.pc.commodity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.product.CommodityStatusEnum;
import com.ssy.lingxi.component.base.enums.product.PriceTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CouponCommodityReq;
import com.ssy.lingxi.product.api.model.req.MaterielBySkuListReq;
import com.ssy.lingxi.product.api.model.req.commodity.*;
import com.ssy.lingxi.product.api.model.resp.commodity.*;
import com.ssy.lingxi.product.service.commodity.ICommodityService;
import com.ssy.lingxi.product.service.commodity.ICommoditySkuService;
import com.ssy.lingxi.product.service.warehouse.IWarehouseInventoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品公共接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodity/common")
public class CommodityCommonController extends BaseController {
    private final ICommodityService commodityService;
    private final ICommoditySkuService commoditySkuService;
    private final IWarehouseInventoryService warehouseInventoryService;


    /**
     * 查询供应商的商品-IM聊天中获取供应商已上架的商品
     *
     * @param commodityQueryReq
     * @return
     */
    @PostMapping(value = "/getCommodityList")
    public WrapperResp<PageDataResp<CommodityListResp>> getCommodityList(@RequestBody CommodityQueryReq commodityQueryReq) {
        return WrapperUtil.success(commodityService.getCommodityListPage(commodityQueryReq));
    }

    /**
     * 销售订单转请购单校验选中销售订单明细商品是否关联有物料返回对应物料信息
     * @param request 销售订单传参
     * @return 转换后的请购单参数
     */
    @PostMapping(value = "/checkAssociatedMateriel")
    public WrapperResp<List<MaterielBySkuResp>> checkAssociatedMateriel(@RequestBody MaterielBySkuListReq request){
        checkLogin();
        return WrapperUtil.success(commodityService.checkAssociatedMateriel(request.getOrderSkuList()));
    }

    /**
     * 查询商品列表--质检能力使用
     * @param pageDataReq 分页实体
     * @param commoditySellerRequest 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getCommodityListBySellerToQuality")
    public WrapperResp<PageDataResp<CommoditySkuStockResp>> getCommodityListBySellerToQuality(PageDataReq pageDataReq, CommoditySellerToQualityReq commoditySellerRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        CommoditySkuQueryReq commoditySkuQueryReq = BeanUtil.copyProperties(commoditySellerRequest, CommoditySkuQueryReq.class);
        //商品状态->为已上架
        List<Integer> statusList = new ArrayList<>();
        statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
        commoditySkuQueryReq.setStatusList(statusList);
        //商城类型
        return WrapperUtil.success(commoditySkuService.getPageCommoditySku(pageDataReq, commoditySkuQueryReq, sysUser.getMemberId(), sysUser.getMemberRoleId()));
    }

    /**
     * 查询商品列表
     */
    @GetMapping(value = "/getPageCommoditySku")
    public WrapperResp<PageDataResp<CommoditySkuStockResp>> getPageCommoditySku(PageDataReq pageDataReq, CommoditySkuQueryReq commoditySkuQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(commoditySkuService.getPageCommoditySku(pageDataReq, commoditySkuQueryReq, sysUser.getMemberId(), sysUser.getMemberRoleId()));
    }

    /**
     * 查询商品列表--代客下单
     * @param pageDataReq 分页实体
     * @param commodityGuestReq 查询条件实体
     */
    @GetMapping(value = "/getCommodityListByGuest")
    public WrapperResp<PageDataResp<CommoditySkuStockResp>> getCommodityListByGuest(PageDataReq pageDataReq, CommodityGuestReq commodityGuestReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        CommoditySkuQueryReq commoditySkuQueryReq = BeanUtil.copyProperties(commodityGuestReq, CommoditySkuQueryReq.class);

        //商品状态
        List<Integer> statusList = new ArrayList<>();
        statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
        commoditySkuQueryReq.setStatusList(statusList);

        //供应商
        commoditySkuQueryReq.setMemberId(sysUser.getMemberId());
        commoditySkuQueryReq.setMemberRoleId(sysUser.getMemberRoleId());

        //商城id
        Long shopId = commodityGuestReq.getShopId();
        if(shopId != null && shopId > 0){
            List<Long> shopIdList = new ArrayList<>();
            shopIdList.add(shopId);
            commoditySkuQueryReq.setShopIdList(shopIdList);
        }
        return WrapperUtil.success(commoditySkuService.getPageCommoditySku(pageDataReq, commoditySkuQueryReq, commodityGuestReq.getMemberId(), commodityGuestReq.getMemberRoleId()));
    }

    /**
     * 查询商品列表--采购商角色(买方)
     * @param pageDataReq 分页实体
     * @param commodityBuyerReq 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getCommodityListByBuyer")
    public WrapperResp<PageDataResp<CommoditySkuStockResp>> getCommodityListByBuyer(PageDataReq pageDataReq, @Valid CommodityBuyerReq commodityBuyerReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        CommoditySkuQueryReq commoditySkuQueryReq = BeanUtil.copyProperties(commodityBuyerReq, CommoditySkuQueryReq.class);
        //商品状态
        List<Integer> statusList = new ArrayList<>();
        statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
        commoditySkuQueryReq.setStatusList(statusList);
        //供应商会员id
        commoditySkuQueryReq.setMemberId(commodityBuyerReq.getMemberId());
        //供应商角色id
        commoditySkuQueryReq.setMemberRoleId(commodityBuyerReq.getMemberRoleId());
        //查询skuId
        commoditySkuQueryReq.setCommoditySkuId(commodityBuyerReq.getSkuId());
        //商城id
        Long shopId = commodityBuyerReq.getShopId();
        if(shopId != null && shopId > 0){
            List<Long> shopIdList = new ArrayList<>();
            shopIdList.add(shopId);
            commoditySkuQueryReq.setShopIdList(shopIdList);
        }

        return WrapperUtil.success(commoditySkuService.getPageCommoditySku(pageDataReq, commoditySkuQueryReq, sysUser.getMemberId(), sysUser.getMemberRoleId()));
    }

    /**
     * 查询商品列表--供应商角色(卖方)
     * @param pageDataReq 分页实体
     * @param commoditySellerReq 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getCommodityListBySeller")
    public WrapperResp<PageDataResp<CommoditySkuStockResp>> getCommodityListBySeller(PageDataReq pageDataReq, CommoditySellerReq commoditySellerReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        CommoditySkuQueryReq commoditySkuQueryReq = BeanUtil.copyProperties(commoditySellerReq, CommoditySkuQueryReq.class);

        //商品状态
        List<Integer> statusList = commoditySellerReq.getStatusList();
        if (CollUtil.isEmpty(statusList)) {
            statusList = new ArrayList<>();
            statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
        }else{
            commoditySkuQueryReq.setShopType(null);
            commoditySkuQueryReq.setEnvironment(null);
        }
        commoditySkuQueryReq.setStatusList(statusList);

        //供应商
        commoditySkuQueryReq.setMemberId(sysUser.getMemberId());

        //商城id
        Long shopId = commoditySellerReq.getShopId();
        if(shopId != null && shopId > 0){
            List<Long> shopIdList = new ArrayList<>();
            shopIdList.add(shopId);
            commoditySkuQueryReq.setShopIdList(shopIdList);
        }
        return WrapperUtil.success(commoditySkuService.getPageCommoditySku(pageDataReq, commoditySkuQueryReq, sysUser.getMemberId(), sysUser.getMemberRoleId()));
    }

    /**
     * 查询商品列表--平台(全部商品)
     * @param commodityPlatformRequest 查询条件实体
     * @return 操作结果
     */
    @PostMapping(value = "/getCommodityListByPlatform")
    public WrapperResp<PageDataResp<CommoditySkuResp>> getCommodityListByPlatform(@RequestBody CommonCommodityPlatformReq commodityPlatformRequest) {
        CommoditySkuQueryReq commoditySkuQueryReq = BeanUtil.copyProperties(commodityPlatformRequest, CommoditySkuQueryReq.class);
        //商品状态
        List<Integer> statusList = new ArrayList<>();
        statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
        commoditySkuQueryReq.setStatusList(statusList);
        //商城id
        Long shopId = commodityPlatformRequest.getShopId();
        if(shopId != null && shopId > 0){
            List<Long> shopIdList = new ArrayList<>();
            shopIdList.add(shopId);
            commoditySkuQueryReq.setShopIdList(shopIdList);
        }
        //分页参数
        PageDataReq pageDataReq = new PageDataReq();
        pageDataReq.setCurrent(commodityPlatformRequest.getCurrent());
        pageDataReq.setPageSize(commodityPlatformRequest.getPageSize());
        return WrapperUtil.success(commoditySkuService.getCommoditySkuPlatformList(pageDataReq, commoditySkuQueryReq));
    }

    /**
     * 查询商品列表--库存设置
     * @param commodityStockReq 查询条件实体
     * @return 操作结果
     */
    @PostMapping(value = "/getCommodityListByStock")
    public WrapperResp<PageDataResp<CommoditySkuMemberStockResp>> getCommodityListByStock(@RequestBody CommodityStockReq commodityStockReq) {
        Long brandId = commodityStockReq.getBrandId();
        Long customerCategoryId = commodityStockReq.getCustomerCategoryId();
        String name = commodityStockReq.getName();

        //构建分页参数
        int current = commodityStockReq.getCurrent();
        int pageSize = commodityStockReq.getPageSize();
        PageDataReq pageDataReq = new PageDataReq();
        pageDataReq.setCurrent(current);
        pageDataReq.setPageSize(pageSize);

        //构建查询参数
        CommoditySkuQueryReq commoditySkuQueryReq = new CommoditySkuQueryReq();
        commoditySkuQueryReq.setBrandId(brandId);
        commoditySkuQueryReq.setCustomerCategoryId(customerCategoryId);
        commoditySkuQueryReq.setName(name);

        //会员信息
        UserLoginCacheDTO sysUser = this.getSysUser();
        commoditySkuQueryReq.setMemberId(sysUser.getMemberId());
        commoditySkuQueryReq.setMemberRoleId(sysUser.getMemberRoleId());

        //通过仓库id查询物料id
        List<Long> warehouseIdList = commodityStockReq.getWarehouseIdList();
        if(!CollectionUtils.isEmpty(warehouseIdList)){
            //-1代表查询全部
            if(warehouseIdList.contains(-1L)){
                warehouseIdList = new ArrayList<>();
            }
            List<Long> materielIdList = warehouseInventoryService.getMaterielIdListByWarehouse(sysUser, warehouseIdList);
            if(!CollectionUtils.isEmpty(materielIdList)){
                commoditySkuQueryReq.setMaterielIdList(materielIdList);
            }else{
                return WrapperUtil.success(new PageDataResp<>());
            }
        }else{
            //查询没有绑定物料的商品
            commoditySkuQueryReq.setIsMateriel(false);
        }

        //商品状态
        commoditySkuQueryReq.setStatusList(CollUtil.toList(
                CommodityStatusEnum.PASS.getCode(), CommodityStatusEnum.ON_SHELF.getCode(), CommodityStatusEnum.OFF_SHELF.getCode(), CommodityStatusEnum.NEVER_SHELF.getCode()
        ));
        //价格类型
        commoditySkuQueryReq.setPriceTypeList(commodityStockReq.getPriceTypeList());
        return WrapperUtil.success(commoditySkuService.getCommoditySkuStockList(pageDataReq, commoditySkuQueryReq, sysUser.getMemberRoleType()));
    }

    /**
     * 查询商品列表--供应商角色(卖方)-规则引擎配置用到
     * @param pageDataReq 分页实体
     * @param commoditySellerToRuleReq 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getCommodityListBySellerToRule")
    public WrapperResp<PageDataResp<CommoditySkuResp>> getCommodityListBySellerToRule(PageDataReq pageDataReq, CommoditySellerToRuleReq commoditySellerToRuleReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        CommoditySkuQueryReq commoditySkuQueryReq = BeanUtil.copyProperties(commoditySellerToRuleReq, CommoditySkuQueryReq.class);

        //商品状态->为已上架
        List<Integer> statusList = new ArrayList<>();
        statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
        commoditySkuQueryReq.setStatusList(statusList);

        //供应商
        commoditySkuQueryReq.setMemberId(sysUser.getMemberId());

        //返回结果
        List<CommoditySkuResp> commoditySkuRespList =new ArrayList<>();
        PageDataResp<CommoditySkuStockResp> commoditySkuDetailRespList = commoditySkuService.getPageCommoditySku(pageDataReq, commoditySkuQueryReq, sysUser.getMemberId(), sysUser.getMemberRoleId());
        if (CollectionUtils.isEmpty(commoditySkuDetailRespList.getData())){
            return WrapperUtil.success(new PageDataResp<>());
        }
        commoditySkuDetailRespList.getData().forEach(commoditySkuDetailResp -> {
            CommoditySkuResp commoditySkuResp = new CommoditySkuResp();
            BeanUtil.copyProperties(commoditySkuDetailResp, commoditySkuResp);
            commoditySkuResp.setPriceTypeName(PriceTypeEnum.getNameByCode(commoditySkuDetailResp.getPriceType()));
            commoditySkuRespList.add(commoditySkuResp);
        });
        return WrapperUtil.success(new PageDataResp<>(commoditySkuDetailRespList.getTotalCount(), commoditySkuRespList));
    }

    /**
     * 查询商品sku列表(快捷修改商品单价)
     * @param commodityQueryReq 查询条件实体
     * @return 操作结果
     */
    @GetMapping(value = "/getCommodityDetailList")
    public WrapperResp<PageDataResp<CommoditySkuStockResp>> getCommodityDetailList(CommodityQueryReq commodityQueryReq) {
        CommoditySkuQueryReq commoditySkuQueryReq = BeanUtil.copyProperties(commodityQueryReq, CommoditySkuQueryReq.class);
        UserLoginCacheDTO sysUser = this.getSysUser();
        commoditySkuQueryReq.setMemberId(sysUser.getMemberId());
        commoditySkuQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        List<Integer> priceTypeList = commodityQueryReq.getPriceTypeList();
        if(priceTypeList == null || priceTypeList.isEmpty()){
            priceTypeList = new ArrayList<>();
            priceTypeList.add(PriceTypeEnum.CASH.getCode());
            priceTypeList.add(PriceTypeEnum.SCORE.getCode());
        }
        commoditySkuQueryReq.setPriceTypeList(priceTypeList);
        List<Integer> statusList = new ArrayList<>();
        statusList.add(CommodityStatusEnum.PASS.getCode());
        statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
        statusList.add(CommodityStatusEnum.OFF_SHELF.getCode());
        commoditySkuQueryReq.setStatusList(statusList);
        //分页参数
        PageDataReq pageDataReq = new PageDataReq();
        pageDataReq.setCurrent(commodityQueryReq.getCurrent());
        pageDataReq.setPageSize(commodityQueryReq.getPageSize());
        return WrapperUtil.success(commoditySkuService.getPageCommoditySku(pageDataReq, commoditySkuQueryReq));
    }

    /**
     * 通过商城id查询商品sku列表
     * @param couponCommodityReq 实体
     * @return 操作结果
     */
    @PostMapping(value = "/getCommoditySkuListByShopId")
    public WrapperResp<PageDataResp<CommoditySkuStockResp>> getCommoditySkuListByShopId(@RequestBody @Valid CouponCommodityReq couponCommodityReq) {
        UserLoginCacheDTO sysUser = getSysUser();

        //查询条件
        CommoditySkuQueryReq commoditySkuQueryReq = new CommoditySkuQueryReq();
        commoditySkuQueryReq.setMemberId(sysUser.getMemberId());
        commoditySkuQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        commoditySkuQueryReq.setCustomerCategoryId(couponCommodityReq.getCustomerCategoryId());
        commoditySkuQueryReq.setBrandId(couponCommodityReq.getBrandId());
        commoditySkuQueryReq.setName(couponCommodityReq.getCommodityName());
        commoditySkuQueryReq.setIsMorePrice(couponCommodityReq.getIsMorePrice());
        commoditySkuQueryReq.setIdNotInList(couponCommodityReq.getIdNotInList());

        //商城
        commoditySkuQueryReq.setShopIdList(couponCommodityReq.getShopIdList());

        //商品类型
        List<Integer> priceTypeList = new ArrayList<>();
        Boolean isGift = couponCommodityReq.getIsGift();
        if(isGift != null && isGift){
            //赠品
            priceTypeList.add(PriceTypeEnum.GIFT.getCode());
        }else{
            //现货商品
            priceTypeList.add(PriceTypeEnum.CASH.getCode());
        }
        //commoditySkuQueryReq.setPriceTypeList(priceTypeList);

        //已上架
        List<Integer> statusList = new ArrayList<>();
        statusList.add(CommodityStatusEnum.ON_SHELF.getCode());
        commoditySkuQueryReq.setStatusList(statusList);

        PageDataReq pageDataReq = new PageDataReq();
        pageDataReq.setCurrent(couponCommodityReq.getCurrent());
        pageDataReq.setPageSize(couponCommodityReq.getPageSize());
        return WrapperUtil.success(commoditySkuService.getPageCommoditySku(pageDataReq, commoditySkuQueryReq));
    }
}
