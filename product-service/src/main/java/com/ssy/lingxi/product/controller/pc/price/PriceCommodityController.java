package com.ssy.lingxi.product.controller.pc.price;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.resp.CommodityPriceRecordResp;
import com.ssy.lingxi.product.service.commodity.IPriceRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品历史价格管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/price/commodity")
public class PriceCommodityController extends BaseController {
    private final IPriceRecordService priceRecordService;

    /**
     * 查询商品历史价格
     * @param commoditySkuId  商品skuId
     * @param days         最近多少天
     * @return 商品历史价格
     */
    @GetMapping(value = "/getPriceRecord")
    public WrapperResp<CommodityPriceRecordResp> getPriceRecord(@RequestParam("commoditySkuId") Long commoditySkuId, @RequestParam("days") Integer days) {
        return WrapperUtil.success(priceRecordService.getPriceRecord(commoditySkuId, days));
    }

}
