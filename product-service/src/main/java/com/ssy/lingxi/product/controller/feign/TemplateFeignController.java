package com.ssy.lingxi.product.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.NodeResp;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.feign.ITemplateFeign;
import com.ssy.lingxi.product.api.model.req.EsCommoditySearchReq;
import com.ssy.lingxi.product.api.model.req.StoreIdListReq;
import com.ssy.lingxi.product.api.model.req.TemplateCommoditySearchReq;
import com.ssy.lingxi.product.api.model.resp.CategoryBaseResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsBrandResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import com.ssy.lingxi.product.service.shop.ITemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 模板服务内部接口类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/8/1
 * @ignore 不需要提交到Yapi
 */
@Primary
@RestController
@RequiredArgsConstructor
public class TemplateFeignController extends BaseController implements ITemplateFeign {
    private final IShopService esCommodityService;
    private final ITemplateService templateCommodityService;

    /**
     * 查询商品分类树
     * @param shopId            商城id
     * @param memberId          会员id
     * @param memberRoleId      会员角色id
     */
    @Override
    public WrapperResp<List<NodeResp>> getCategoryTree(
            @RequestParam(value = "shopId") Long shopId,
            @RequestParam(value = "memberId", required = false) Long memberId,
            @RequestParam(value = "memberRoleId", required = false) Long memberRoleId) {
        return WrapperUtil.success(templateCommodityService.getCategoryTree(shopId, memberId, memberRoleId));
    }

    /**
     * 通过品类查询会员信息
     * @param shopId            商城id
     * @param categoryId        品类id
     */
    @Override
    public WrapperResp<List<Long>> getMemberIdByCategory(@RequestParam(value = "shopId") Long shopId, @RequestParam(value = "categoryId") Long categoryId){
        return WrapperUtil.success(templateCommodityService.getMemberIdByCategory(shopId, categoryId));
    }

    /**
     * 通过品类查询店铺ID
     * @param shopId            商城id
     * @param categoryId        品类id
     */
    @Override
    public WrapperResp<List<Long>> getStoreIdByCategory(Long shopId, Long categoryId) {
        return WrapperUtil.success(templateCommodityService.getStoreIdByCategory(shopId, categoryId));
    }

    /**
     * 查询一级品类信息
     * @param shopId            商城id
     * @param memberId          会员id
     * @param memberRoleId      会员角色id
     */
    @Override
    public WrapperResp<List<CategoryBaseResp>> getFirstCategoryListByMemberId(
            @RequestParam(value = "shopId") Long shopId,
            @RequestParam(value = "memberId", required = false) Long memberId,
            @RequestParam(value = "memberRoleId", required = false) Long memberRoleId){
        return WrapperUtil.success(templateCommodityService.getFirstCategoryList(shopId, memberId, memberRoleId));
    }

    /**
     * 查询二级品类信息
     * @param shopId            商城id
     * @param memberId          会员id
     * @param memberRoleId      会员角色id
     * @param categoryId        品类id
     */
    @Override
    public WrapperResp<List<CategoryBaseResp>> getSecondCategoryListByMemberId(
            @RequestParam(value = "shopId") Long shopId,
            @RequestParam(value = "memberId", required = false) Long memberId,
            @RequestParam(value = "memberRoleId", required = false) Long memberRoleId,
            @RequestParam(value = "categoryId") Long categoryId){
        return WrapperUtil.success(templateCommodityService.getSecondCategoryList(shopId, memberId, memberRoleId, categoryId));
    }

    /**
     * 查询品牌信息
     * @param current 当前页码
     * @param pageSize 每页条数
     * @param customerCategoryId 会员分类id
     * @param categoryId 平台分类id
     * @param memberId 会员id
     * @param name 品牌名称
     * @param idInList 包含
     * @param idNotInList 不包含
     */
    @Override
    public WrapperResp<PageDataResp<EsBrandResp>> getBrandList(@RequestParam("current") Integer current, @RequestParam("pageSize") Integer pageSize,
                                                               @RequestParam(value = "shopId", required = false) Long shopId,
                                                               @RequestParam(value = "customerCategoryId",required = false) Long customerCategoryId,
                                                               @RequestParam(value = "categoryId",required = false) Long categoryId,
                                                               @RequestParam(value = "memberId",required = false) Long memberId,
                                                               @RequestParam(value = "memberRoleId",required = false) Long memberRoleId,
                                                               @RequestParam(value = "memberName",required = false) String memberName,
                                                               @RequestParam(value = "name",required = false) String name,
                                                               @RequestParam(value = "idInList",required = false) List<Long> idInList,
                                                               @RequestParam(value = "idNotInList",required = false) List<Long> idNotInList){
        //构造查询条件
        PageDataReq pageDataReq = new PageDataReq();
        pageDataReq.setCurrent(current);
        pageDataReq.setPageSize(pageSize);
        EsCommoditySearchReq esCommoditySearchReq = new EsCommoditySearchReq();
        esCommoditySearchReq.setCategoryId(categoryId);
        esCommoditySearchReq.setCustomerCategoryId(customerCategoryId);
        esCommoditySearchReq.setName(name);
        esCommoditySearchReq.setMemberId(memberId);
        esCommoditySearchReq.setMemberRoleId(memberRoleId);
        esCommoditySearchReq.setMemberName(memberName);
        esCommoditySearchReq.setIdInList(idInList);
        esCommoditySearchReq.setIdNotInList(idNotInList);
        esCommoditySearchReq.setShopId(shopId);

        return WrapperUtil.success(templateCommodityService.searchBrandList(pageDataReq, esCommoditySearchReq));
    }

    /**
     * 查询商品信息
     * @param templateCommoditySearchReq 请求参数
     */
    @Override
    public WrapperResp<PageDataResp<EsCommodityResp>> searchCommodityList(@RequestBody TemplateCommoditySearchReq templateCommoditySearchReq) {
        //构造查询条件
        EsCommoditySearchReq esCommoditySearchReq = BeanUtil.copyProperties(templateCommoditySearchReq, EsCommoditySearchReq.class);
        return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, false, false));
    }

    /**
     * 根据店铺id集合查找最新上架的商品
     * @param storeIdListReq 参数
     */
    @Override
    public WrapperResp<Map<Long, List<EsCommodityResp>>> getCommodityListByPublishTime(@RequestBody StoreIdListReq storeIdListReq){
        return WrapperUtil.success(templateCommodityService.getCommodityListByPublishTime(storeIdListReq));
    }

    /**
     * 根据店铺id集合查找销量最高的商品
     * @param storeIdListReq 参数
     */
    @Override
    public WrapperResp<Map<Long, List<EsCommodityResp>>> getCommodityListBySoldCount(@RequestBody StoreIdListReq storeIdListReq){
        return WrapperUtil.success(templateCommodityService.getCommodityListBySoldCount(storeIdListReq));
    }

}
