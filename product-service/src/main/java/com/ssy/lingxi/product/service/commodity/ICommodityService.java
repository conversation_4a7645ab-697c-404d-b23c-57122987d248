package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.api.product.SellExtraDataParamsRecordReq;
import com.ssy.lingxi.common.model.req.api.product.SellProReq;
import com.ssy.lingxi.common.model.req.api.product.SellProTextReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.product.api.model.req.MaterielBySkuReq;
import com.ssy.lingxi.product.api.model.req.MemberReq;
import com.ssy.lingxi.product.api.model.req.baitai.SpaceSingleProductStatusReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityAddOrUpdateReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityLowerQueryReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityQueryReq;
import com.ssy.lingxi.product.api.model.resp.BrandResp;
import com.ssy.lingxi.product.api.model.resp.commodity.*;
import com.ssy.lingxi.product.api.model.resp.store.StoreResp;
import com.ssy.lingxi.product.entity.do_.commodity.CommodityDO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface ICommodityService {
    /**
     * 查询商品列表 -- 商品能力
     */
    PageDataResp<CommodityResp> getCommodityPage(CommodityQueryReq commodityQueryReq);

    /**
     * 查询商品列表 -- 商品能力
     */
    PageDataResp<CommodityDetailResp> getCommodityDetailPage(CommodityQueryReq commodityQueryReq);

    /**
     * 查询商品--商品能力
     * @param commodityQueryReq 请求参数
     */
    PageDataResp<CommodityListResp> getCommodityListPage(CommodityQueryReq commodityQueryReq);

    /**
     * 查询商品详情
     * @param id id
     */
    CommodityDetailResp getCommodityDetail(Long id);

    /**
     * 查询缓存中的商品详情
     */
    CommodityDetailResp getCommodityDetailCache(Long id);

    /**
     * 新增/修改商品
     * @param sysUser 当前登录用户
     * @param commodityAddOrUpdateReq 请求参数
     * @return 操作结果
     */
    Long saveOrUpdateCommodity(UserLoginCacheDTO sysUser, CommodityAddOrUpdateReq commodityAddOrUpdateReq);

    /**
     * 批量删除商品
     * @param idList ID列表
     * @return 操作结果
     */
    String deleteBatchCommodity(UserLoginCacheDTO sysUser, List<Long> idList);

    /**
     * 提交审核
     * @param id id
     */
    void applyCheckCommodity(UserLoginCacheDTO sysUser, Long id);

    /**
     * 批量提交审核
     * @param sysUser 用户
     * @param idList id列表
     */
    void batchCheckCommodity(UserLoginCacheDTO sysUser, List<Long> idList);

    /**
     * 复制商品--商品能力
     * @param commonIdListRequest 商品Id集合
     * @param commodityType 商品类型
     */
    void copyCommodity(UserLoginCacheDTO sysUser, CommonIdListReq commonIdListRequest, Integer commodityType);

    /**
     * 获取商品属性
     * @param commoditySkuId 商品skuId
     * @return 操作结果
     */
    List<CommodityAttributeResp> getCommodityAttributeByCommoditySkuId(UserLoginCacheDTO sysUser, Long commoditySkuId);

    /**
     * 查询商品是否用到单位
     * @param id 单位id
     * @return 操作结果
     */
    boolean getCommodityUseUnit(Long id);

    /**
     * 查询商品并通过商城id分组
     * @param commodityIdList 商品id数组
     * @return key:商城id; value:商品id数组
     */
    Map<Long, List<CommodityDO>> getCommodityListGroupByShopId(List<Long> commodityIdList);

    /**
     * 通过商品id查询对应的商品skuId数组
     * @param idList 商品id数组
     */
    List<CommodityIdAndSkuIdListResp> getCommoditySkuIdList(List<Long> idList);

    /**
     * 查询商品是否配置了库存
     * @param commodityIdList 商品id数组
     */
    List<Long> getCommodityIsExistStock(UserLoginCacheDTO userLoginCacheDTO, List<Long> commodityIdList);

    /**
     * 判断货品是否于商品关联
     *
     * @param request 货品(物料)ids
     * @return Boolean
     */
    Boolean materielUsing(CommonIdListReq request);

    /**
     * 销售订单转请购单校验选中销售订单明细商品是否关联有物料
     * @param dtos 请求参数
     * @return 操作结果
     */
    List<MaterielBySkuResp> checkAssociatedMateriel(List<MaterielBySkuReq> dtos);

    /**
     * 查询商品品类id列表--通过商品skuId集合
     * @param commoditySkuIdList 请求参数
     * @return 商品信息
     */
    Map<Long,Long> getCategoryIdByCommoditySkuIdList(List<Long> commoditySkuIdList);

    /**
     * 查询上游供应商品
     * @param id 查询条件实体
     */
    CommodityDetailResp getUpperCommodity(Long id);

    /**
     * 查询下游销售商品列表--商品能力
     * @param commodityLowerQueryReq 查询条件实体
     * @return 操作结果
     */
    PageDataResp<CommodityResp> getLowerCommodityList(UserLoginCacheDTO sysUser, CommodityLowerQueryReq commodityLowerQueryReq);

    /**
     * 通过会员id和会员角色id查询会员品类和商品图片
     */
    Map<String, StoreResp> getCommodityAndCategoryByMemberIdAndMemberRoleId(List<MemberReq> memberReqList);

    /**
     * 通过会员id查询会员品类和商品图片
     */
    Map<Long, StoreResp> getCommodityAndCategoryByMemberId(CommonIdListReq commonIdListReq);

    /**
     * 根据商品的会员品类id获取品牌
     * @param customerCategoryId    会员品类id
     */
    List<BrandResp> getBrandListByCustomerCategoryId(Long customerCategoryId);

    /**
     * 根据商品的平台品类id获取品牌
     * @param categoryId     平台后台品类id
     */
    List<BrandResp> getBrandListByCategoryId(Long categoryId);

    /**
     * 组装商品信息
     * @param commodityId   商品id
     */
    CommodityBaseResp getCommodityBaseResp(Long commodityId);

    /**
     * 组装商品信息
     * @param commodityDO   商品对象
     */
    CommodityResp getCommodityResp(CommodityDO commodityDO);

    /**
     * 组装商品信息
     * @param commodityDO   商品对象
     */
    CommodityDetailResp getCommodityDetailResp(CommodityDO commodityDO);

    /**
     * 数仓平台商品同步处理
     * @param sellProReq 参数
     */
    void commodityBaseSyncHandler(SellProReq sellProReq);

    /**
     * 根据商品code查询商品信息
     * @param code 编码
     * @return 结果
     */
    CommodityDetailResp getByCode(String code);

    /**
     * 更新同步上游商品
     * @param id
     */
    void syncUpperCommodity(Long id, UserLoginCacheDTO sysUser);

    /**
     * 根据商品skuId集合查询商品是否有库存
     * @param commoditySkuIdList 商品skuId集合
     * @return 商品是否有库存
     */
    WrapperResp<Map<Long, Boolean>> getCommoditySkuIdListByIsExistStock(Set<Long> commoditySkuIdList);

    /**
     * 根据商品单件id，修改库存状态
     * @param spaceSingleProductStatusReq 商品单件id
     * @return 是否成功
     */
    WrapperResp<Void> updateFreightSpaceSingleProductStatus(SpaceSingleProductStatusReq spaceSingleProductStatusReq);
    /**
     * 根据sku下的单品code查询单品信息
     * @param code 单件编码
     */
    WrapperResp<FreightSpaceSingleProductResp> getSingleProductByCode(String code);

    /**
     * 商品描述富文本同步（数仓）
     * @param textReq 参数
     */
    void commodityRemarkSyncHandler(SellProTextReq textReq);

    /**
     * 获取单件商品列表
     * @param codes
     * @return
     */
    public List<FreightSpaceSingleProductResp> getSingleProductByCodes(List<String> codes);

    /**
     * 同步数仓第三方参数关联记录
     * @param recordReq 参数
     */
    void syncSellExtraDataParamsRecord(SellExtraDataParamsRecordReq recordReq);

    /**
     * 商品id获取第三方参数列表
     * @param commodityId id
     * @return 结果
     */
    List<CommodityExtraDataParamResp> getExtraDataParamListById(Long commodityId);

    /**
     * 商品id获取第三方参数列表
     * @param commodityIds 商品id
     * @return 结果
     */
    List<CommodityExtraDataParamResp> getExtraDataParamListByIds(CommonIdListReq commodityIds);

    /**
     * 定时刷新上架到es的商品库存，显示
     */
    void timingRefreshEsInventory();
}
