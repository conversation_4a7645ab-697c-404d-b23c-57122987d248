package com.ssy.lingxi.product.controller.pc.commodity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.model.req.CommoditySalesAreaTemplateAddReq;
import com.ssy.lingxi.product.model.req.CommoditySalesAreaTemplateEditReq;
import com.ssy.lingxi.product.model.resp.CommoditySalesAreaTemplateDetailResp;
import com.ssy.lingxi.product.model.resp.CommoditySalesAreaTemplateListResp;
import com.ssy.lingxi.product.service.commodity.ICommoditySalesAreaTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 商品管理-销售区域模板管理
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodity/salesAreaTemplate")
public class CommoditySalesAreaTemplateController extends BaseController {
    private final ICommoditySalesAreaTemplateService commoditySalesAreaTemplateService;

    /**
     * 新增模板
     */
    @PostMapping("/add")
    public WrapperResp<Void> add(@RequestBody @Valid CommoditySalesAreaTemplateAddReq commoditySalesAreaTemplateAddReq) {
        commoditySalesAreaTemplateService.add(getSysUser(), commoditySalesAreaTemplateAddReq);
        return WrapperUtil.success();
    }

    /**
     * 编辑模板
     */
    @PostMapping("/edit")
    public WrapperResp<Void> edit(@RequestBody @Valid CommoditySalesAreaTemplateEditReq commoditySalesAreaTemplateEditReq) {
        commoditySalesAreaTemplateService.edit(getSysUser(), commoditySalesAreaTemplateEditReq);
        return WrapperUtil.success();
    }

    /**
     * 列表查询
     */
    @GetMapping("/list")
    public WrapperResp<PageDataResp<CommoditySalesAreaTemplateListResp>> list(@Valid PageDataReq pageDataReq) {
        return WrapperUtil.success(commoditySalesAreaTemplateService.list(getSysUser(), pageDataReq));
    }

    /**
     * 模板详情
     *
     * @param id 销售区域模板ID
     */
    @GetMapping("/detail")
    public WrapperResp<CommoditySalesAreaTemplateDetailResp> detail(@RequestParam Long id) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(commoditySalesAreaTemplateService.detail(id, sysUser.getMemberId(), sysUser.getMemberRoleId()));
    }

}
