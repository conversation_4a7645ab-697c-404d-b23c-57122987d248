package com.ssy.lingxi.product.service.commodity;

import cn.hutool.core.text.StrPool;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.req.api.product.SellProSpecReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.CommoditySkuListQueryReq;
import com.ssy.lingxi.product.api.model.req.baitai.CommoditySkuIdsReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommoditySkuBatchUpdateReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommoditySkuQueryReq;
import com.ssy.lingxi.product.api.model.req.commodity.UnitPriceReq;
import com.ssy.lingxi.product.api.model.req.feign.CommodityPriceReq;
import com.ssy.lingxi.product.api.model.req.feign.CommoditySinglePriceReq;
import com.ssy.lingxi.product.api.model.resp.commodity.*;
import com.ssy.lingxi.product.api.model.resp.feign.CommodityPriceResp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品sku管理类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface ICommoditySkuService {

    /**
     * 通过商品skuId查询sku属性
     */
    String getCommoditySkuName(Long commoditySkuId);

    /**
     * 通过商品skuId查询sku属性
     *
     * @param conjunction 分隔符 示例: {@link StrPool#COMMA}
     */
    String getCommoditySkuName(Long commoditySkuId, String conjunction);

    /**
     * 查询商品信息
     *
     * @param commoditySkuId 商品skuId
     */
    CommoditySkuResp getCommoditySku(Long commoditySkuId);

    /**
     * 查询sku信息
     * @param commoditySkuId id
     * @return 参数
     */
    CommoditySkuResp getSkuById(Long commoditySkuId);

    /**
     * 查询商品sku信息
     *
     * @param commodityId 商品id
     * @return 操作结果
     */
    List<CommoditySkuResp> getCommoditySkuList(Long commodityId);

    /**
     * 分页-查询商品sku列表
     *
     * @param pageDataReq          分页实体
     * @param commoditySkuQueryReq 查询条件实体
     * @return 操作结果
     */
    PageDataResp<CommoditySkuStockResp> getPageCommoditySku(PageDataReq pageDataReq, CommoditySkuQueryReq commoditySkuQueryReq);

    /**
     * 查询商品列表--平台后台
     *
     * @param pageDataReq          分页实体
     * @param commoditySkuQueryReq 查询条件实体
     * @return 操作结果
     */
    PageDataResp<CommoditySkuResp> getCommoditySkuPlatformList(PageDataReq pageDataReq, CommoditySkuQueryReq commoditySkuQueryReq);

    /**
     * 分页-查询商品sku列表
     *
     * @param pageDataReq          分页实体
     * @param commoditySkuQueryReq 查询条件实体
     * @return 操作结果
     */
    PageDataResp<CommoditySkuStockResp> getPageCommoditySku(PageDataReq pageDataReq, CommoditySkuQueryReq commoditySkuQueryReq, Long loginMemberId, Long loginMemberRoleId);

    /**
     * 分页-查询商品sku列表--库存设置
     *
     * @param pageDataReq          分页实体
     * @param commoditySkuQueryReq 查询条件实体
     * @param memberRoleType       会员角色类型
     * @return 操作结果
     */
    PageDataResp<CommoditySkuMemberStockResp> getCommoditySkuStockList(PageDataReq pageDataReq, CommoditySkuQueryReq commoditySkuQueryReq, Integer memberRoleType);

    /**
     * 查询商品sku列表--不分页
     *
     * @param commoditySkuQueryReq 查询条件实体
     */
    List<CommoditySkuStockResp> getCommoditySkuList(CommoditySkuQueryReq commoditySkuQueryReq, Long loginMemberId, Long loginMemberRoleId);

    /**
     * 查询商品sku列表--通过商品skuIdList
     *
     * @param commoditySkuIdList 查询条件实体
     * @return 操作结果
     */
    List<CommoditySkuStockResp> getCommoditySkuList(List<Long> commoditySkuIdList);

    /**
     * 查询商品信息列表--订单校验商品信息
     *
     * @param commodityPriceReq 查询条件实体
     * @return 操作结果
     */
    List<CommoditySkuPublishResp> getCommodityListByOrder(CommodityPriceReq commodityPriceReq);

    /**
     * 查询商品信息列表--订单校验商品单件信息
     * @param commoditySinglePriceReq 查询条件实体
     * @return 商品信息
     */
    List<CommoditySkuSingleResp> getCommodityListByOrderSingle(CommoditySinglePriceReq commoditySinglePriceReq);

    /**
     * 查询上游供应会员商品sku列表--通过商品skuIdList
     *
     * @param commoditySkuIdList 查询条件实体
     * @return 操作结果
     */
    List<UpperCommoditySkuResp> getUpperCommoditySkuList(List<Long> commoditySkuIdList);

    /**
     * 批量获取商品sku价格
     *
     * @param commoditySkuIdList 请求参数
     * @return 操作结果
     */
    Map<Long, Map<String, BigDecimal>> getCommodityPriceBatch(Long shopId, List<Long> commoditySkuIdList, Long memberId, Long memberRoleId);

    /**
     * 批量查询商品sku价格和是否允许使用会员权益
     *
     * @param commoditySkuIdList 请求参数
     * @return 操作结果
     */
    Map<Long, CommodityPriceResp> getCommodityPriceAndMemberPriceBatch(Long shopId, List<Long> commoditySkuIdList, Long memberId, Long memberRoleId);

    /**
     * 获取商品sku价格
     *
     * @param commoditySkuId 请求参数
     * @return 操作结果
     */
    Map<String, BigDecimal> getCommodityPrice(UserLoginCacheDTO sysUser, Long commoditySkuId);

    /**
     * 修改商品sku价格
     *
     * @param unitPriceReq 请求参数
     * @return 操作结果
     */
    void updateCommodityPrice(UserLoginCacheDTO sysUser, UnitPriceReq unitPriceReq);

    /**
     * 数仓平台商品sku信息同步到商城
     * @param proSpecReq 参数
     */
    void commoditySkuSyncHandler(SellProSpecReq proSpecReq);

    /**
     * 根据skuIds 获取 sku详情信息
     * @param skuIds
     * @return
     */
    List<CommoditySkuDetailsResp> getCommoditySkuByIds(CommoditySkuIdsReq skuIds);

    /**
     * 查询sku列表
     * @param queryReq 参数
     * @return 结果
     */
    List<CommodityDetailSkuResp> getCommodityDetailSkuList(CommoditySkuListQueryReq queryReq);

    /**
     * 批量修改sku图片，状态
     * @param updateReq 参数
     */
    void updateSkuImgAndStatus(CommoditySkuBatchUpdateReq updateReq);
}
