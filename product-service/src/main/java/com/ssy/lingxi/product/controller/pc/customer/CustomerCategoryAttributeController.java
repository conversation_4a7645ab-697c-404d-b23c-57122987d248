package com.ssy.lingxi.product.controller.pc.customer;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CustomerCategoryAttributeReq;
import com.ssy.lingxi.product.api.model.req.CustomerCategoryAttributeSortReq;
import com.ssy.lingxi.product.api.model.resp.CustomerAttributeResp;
import com.ssy.lingxi.product.entity.do_.customer.CustomerAttributeDO;
import com.ssy.lingxi.product.service.customer.ICustomerCategoryAttributeService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品能力--品类关联属性
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/customer")
public class CustomerCategoryAttributeController extends BaseController {
    private final ICustomerCategoryAttributeService customerCategoryAttributeService;

    /**
     * 查询品类下的属性列表
     * @param pageDataReq 分页实体
     * @param name 属性名称
     * @param categoryId 品类id
     * @param isByCategory 是否查询该品类下的属性
     */
    @GetMapping("/getCustomerCategoryAttributeList")
    public WrapperResp<PageDataResp<CustomerAttributeResp>> getCustomerCategoryAttributeList(PageDataReq pageDataReq, @RequestParam(value = "name", required = false) String name, @RequestParam(value = "groupName", required = false) String groupName, @RequestParam(value = "categoryId") Long categoryId, @RequestParam(value = "isByCategory") Boolean isByCategory) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<CustomerAttributeDO> result = customerCategoryAttributeService.getCustomerCategoryAttributeList(sysUser, pageDataReq, name, groupName, categoryId, isByCategory);
        List<CustomerAttributeResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<CustomerAttributeResp>>(){}.getType());
        resultList = resultList
                .stream()
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(CustomerAttributeResp::getSort))
                .collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 查询品类下的属性列表-MRO模式 按照特定的排序返回结果
     * @param categoryId 品类id
     * @param isByCategory 是否查询该品类下的属性
     */
    @GetMapping("/getMroCustomerCategoryAttributeList")
    public WrapperResp<List<CustomerAttributeDO>> getMroCustomerCategoryAttributeList(@RequestParam(value = "name", required = false) String name, @RequestParam(value = "groupName", required = false) String groupName, @RequestParam(value = "categoryId") Long categoryId, @RequestParam(value = "isByCategory") Boolean isByCategory) {
        UserLoginCacheDTO sysUser = new UserLoginCacheDTO();
        if (isLogin()) {
            sysUser  = this.getSysUser();
        }
        return WrapperUtil.success(customerCategoryAttributeService.getMroCustomerCategoryAttributeList(sysUser, name, groupName, categoryId, isByCategory));
    }

    /**
     * 新增/修改品类下的属性
     * @param customerCategoryAttributeReq
     * @return
     */
    @PostMapping("/saveCustomerCategoryAttribute")
    public WrapperResp<String> saveCustomerCategoryAttribute(@RequestBody CustomerCategoryAttributeReq customerCategoryAttributeReq){
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(customerCategoryAttributeService.saveCustomerCategoryAttribute(sysUser, customerCategoryAttributeReq));
    }

    /**
     * 解除品类属性关系
     * @param customerCategoryAttributeReq
     * @return
     */
    @PostMapping("deleteCustomerCategoryAttribute")
    public WrapperResp<String> deleteCustomerCategoryAttribute(@RequestBody CustomerCategoryAttributeReq customerCategoryAttributeReq){
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(customerCategoryAttributeService.deleteCustomerCategoryAttribute(sysUser, customerCategoryAttributeReq));
    }

    /**
     * 新增/修改品类下的属性排序
     *
     * @param customerCategoryAttributeSortReqList
     * @return
     */
    @PostMapping("/saveCustomerCategoryAttributeSort")
    public WrapperResp<Boolean> saveCustomerCategoryAttributeSort(@RequestBody List<CustomerCategoryAttributeSortReq> customerCategoryAttributeSortReqList) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(customerCategoryAttributeService.saveCustomerCategoryAttributeSort(sysUser, customerCategoryAttributeSortReqList));
    }

}
