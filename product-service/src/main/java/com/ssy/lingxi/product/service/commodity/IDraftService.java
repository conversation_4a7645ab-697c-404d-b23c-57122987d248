package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.CommodityDraftReq;
import com.ssy.lingxi.product.api.model.resp.CommodityDraftResp;

import java.util.List;

/**
 * 商品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface IDraftService {

    /**
     * 查询商品草稿列表
     * @param pageDataReq 分页实体
     */
    PageDataResp<CommodityDraftResp> getCommodityDraftList(UserLoginCacheDTO sysUser, PageDataReq pageDataReq);

    /**
     * 查询商品草稿
     * @param commodityDraftId 商品草稿id
     */
    CommodityDraftResp getCommodityDraftById(UserLoginCacheDTO sysUser, Long commodityDraftId);

    /**
     * 保存商品草稿
     */
    Long saveOrUpdateCommodityDraft(UserLoginCacheDTO sysUser, CommodityDraftReq commodityDraftReq);

    /**
     * 删除商品草稿
     */
    Boolean deleteCommodityDraft(UserLoginCacheDTO sysUser, List<Long> commodityDraftIdList);
}
