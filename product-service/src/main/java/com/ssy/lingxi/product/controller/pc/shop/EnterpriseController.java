package com.ssy.lingxi.product.controller.pc.shop;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.NodeResp;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.model.resp.ProvinceResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.EsCommoditySearchReq;
import com.ssy.lingxi.product.api.model.req.ShopSearchReq;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsAttributeResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsBrandResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 企业商城
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/8/1
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/enterprise")
public class EnterpriseController extends BaseController {
    private final IShopService esCommodityService;

    /**
     * 查询商品品类树
     *
     * @return 操作结果
     */
    @GetMapping("/getCategoryTree")
    public WrapperResp<List<NodeResp>> getCategoryTree() {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(esCommodityService.getCategoryTree(shopId, null));
    }

    /**
     * 查询商品属性
     *
     * @param categoryId 品类id
     * @return 操作结果
     */
    @GetMapping(value = "/getAttributeByCategoryId")
    public WrapperResp<List<EsAttributeResp>> getAttributeByCategoryId(@RequestParam(value = "categoryId") Long categoryId) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(esCommodityService.getAttributeByCategoryId(shopId, null, categoryId));
    }

    /**
     * 查询商品品牌
     *
     * @return 操作结果
     */
    @GetMapping(value = "/getBrand")
    public WrapperResp<List<EsBrandResp>> getBrand() {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(esCommodityService.getBrand(shopId, null));
    }

    /**
     * 查询归属地市
     */
    @GetMapping("/getArea")
    public WrapperResp<List<ProvinceResp>> getArea() {
        return WrapperUtil.success(AreaUtil.findAllAsTree());
    }

    /**
     * 查询商品列表
     *
     * @param shopSearchReq 查询条件实体
     */
    @PostMapping(value = "/getCommodityList")
    public WrapperResp<PageDataResp<EsCommodityResp>> searchCommodityList(@RequestBody ShopSearchReq shopSearchReq) {
        Long shopId = this.getHeadersShopId();
        EsCommoditySearchReq esCommoditySearchReq = this.modelMapper.map(shopSearchReq, EsCommoditySearchReq.class);
        esCommoditySearchReq.setShopId(shopId);
        //判断是否已登录
        if (this.isLogin()) {
            UserLoginCacheDTO sysUser = this.getSysUser();
            esCommoditySearchReq.setLoginMemberId(sysUser.getMemberId());
            esCommoditySearchReq.setLoginMemberRoleId(sysUser.getMemberRoleId());
        }
        return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, true, false));
    }

}
