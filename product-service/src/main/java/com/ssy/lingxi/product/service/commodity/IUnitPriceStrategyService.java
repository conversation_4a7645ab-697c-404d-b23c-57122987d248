package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.commodity.UnitPriceStrategyAddReq;
import com.ssy.lingxi.product.api.model.req.commodity.UnitPriceStrategyReq;
import com.ssy.lingxi.product.api.model.resp.commodity.UnitPriceMemberResp;
import com.ssy.lingxi.product.api.model.resp.commodity.UnitPriceStrategyDetailResp;
import com.ssy.lingxi.product.api.model.resp.commodity.UnitPriceStrategyListResp;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 商品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface IUnitPriceStrategyService {

    /**
     * 查询价格策略列表--商品能力
     * @param pageDataReq 分页参数
     * @param sysUser 操作用户
     * @param unitPriceStrategyReq 请求参数
     * @return 操作结果
     */
    PageDataResp<UnitPriceStrategyListResp> getUnitPriceStrategyList(PageDataReq pageDataReq, UserLoginCacheDTO sysUser, UnitPriceStrategyReq unitPriceStrategyReq);

    /**
     * 新增/修改价格策略--商品能力
     * @param sysUser 操作用户
     * @param unitPriceStrategyAddReq 请求参数
     * @return 操作结果
     */
    Long saveOrUpdateUnitPriceStrategy(UserLoginCacheDTO sysUser, UnitPriceStrategyAddReq unitPriceStrategyAddReq);

    /**
     * 查询价格策略
     * @param commodityId 商品id
     */
    List<UnitPriceStrategyDetailResp> getUnitPriceStrategyList(Long commodityId);

    /**
     * 查询价格策略--商品能力
     * @param id 价格策略id
     * @return 操作结果
     */
    UnitPriceStrategyDetailResp getUnitPriceStrategy(Long id);

    /**
     * 通过商品id获取价格策略的所有会员--商品能力
     * @param commodityId   商品id
     * @param shopId        商城id
     * @return 操作结果
     */
    List<UnitPriceMemberResp> getStrategyMemberByCommodityId(Long commodityId, Long shopId);

    /**
     * 启用/停用价格策略--商品能力
     * @return 操作结果
     */
    String updateUnitPriceStrategyStatus(UserLoginCacheDTO sysUser, Long id, Boolean isEnable);

    /**
     * 删除价格策略--商品能力
     * @param id 价格策略id
     * @return 操作结果
     */
    String deleteUnitPriceStrategy(Long id);

    /**
     * 获取价格策略中设置的价格
     * @param commodityId              商品id
     * @param commoditySkuId           商品skuId
     * @param shopIdList               商城id数组
     * @param loginMemberId            登录会员id
     * @param loginMemberRoleId        登录会员角色id
     * @param memberId                 供应商会员ID
     * @param memberRoleId             供应商会员角色id
     * @param memberLevelMap           登录会员在上级中的所有等级
     */
    Map<String, BigDecimal> getStrategyPrice(Long commodityId, Long commoditySkuId, List<Long> shopIdList, Long loginMemberId, Long loginMemberRoleId, Long memberId, Long memberRoleId, Map<String, Integer> memberLevelMap);
}
