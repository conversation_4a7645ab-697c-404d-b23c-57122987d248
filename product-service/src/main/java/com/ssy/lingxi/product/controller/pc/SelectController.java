package com.ssy.lingxi.product.controller.pc;

import com.ssy.lingxi.commodity.api.feign.IUnitFeign;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.SelectVO;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseSelectResp;
import com.ssy.lingxi.product.enums.InvoicesTypeEnum;
import com.ssy.lingxi.product.service.IBrandService;
import com.ssy.lingxi.product.service.customer.ICustomerCategoryService;
import com.ssy.lingxi.product.service.platform.ICategoryService;
import com.ssy.lingxi.product.service.warehouse.IInvoicesTypeService;
import com.ssy.lingxi.product.service.warehouse.IWarehouseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 下拉框管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/30
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/select")
public class SelectController extends BaseController {
    //内部接口类
    private final IUnitFeign unitFeign;
    //接口类
    private final IBrandService brandService;
    private final ICategoryService categoryService;
    private final IWarehouseService warehouseService;
    private final IInvoicesTypeService invoicesTypeService;
    private final ICustomerCategoryService customerCategoryService;

    /**
     * 查询品类下拉框--供应商
     * @param name 品类名称
     * @param memberId 供应商id
     * @param memberRoleId 供应商角色id
     * @return
     */
    @GetMapping("/getMemberCategory")
    public WrapperResp<List<SelectVO>> getMemberCategory(@RequestParam(value = "name", required = false) String name, @RequestParam("memberId") Long memberId, @RequestParam("memberRoleId") Long memberRoleId) {
        return WrapperUtil.success(customerCategoryService.getSelectCustomerCategory(memberId, memberRoleId, name));
    }

    /**
     * 查询品牌下拉框--供应商
     * @param name 品牌名称
     * @param memberId 供应商id
     * @param memberRoleId 供应商角色id
     * @return
     */
    @GetMapping("/getMemberBrand")
    public WrapperResp<List<SelectVO>> getMemberBrand(@RequestParam(value = "name", required = false) String name, @RequestParam("memberId") Long memberId, @RequestParam("memberRoleId") Long memberRoleId) {
        return WrapperUtil.success(brandService.getSelectBrand(memberId, memberRoleId, name));
    }

    /**
     * 查询品类下拉框--当前会员
     * @param name 品类名称
     * @return
     */
    @GetMapping("/getSelectCustomerCategory")
    public WrapperResp<List<SelectVO>> getCustomerCategory(@RequestParam(value = "name", required = false) String name) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(customerCategoryService.getCustomerCategory(sysUser.getMemberId(), sysUser.getMemberRoleId(), name));
    }

    /**
     * 查询品类最大级别--当前会员
     * @return
     */
    @GetMapping("/getMaxCustomerCategory")
    public WrapperResp<Integer> getMaxCustomerCategory() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(customerCategoryService.getMaxCustomerCategory(sysUser));
    }

    /**
     * 根据父级id查询品类--当前会员
     * @return
     */
    @GetMapping("/getCustomerCategoryByParentId")
    public WrapperResp<List<SelectVO>> getCustomerCategoryByParentId(@RequestParam("parentId") Long parentId) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(customerCategoryService.getCustomerCategoryByParentId(sysUser, parentId));
    }

    /**
     * 查询品牌下拉框--当前会员
     * @param name 品牌名称
     * @return
     */
    @GetMapping("/getSelectBrand")
    public WrapperResp<List<SelectVO>> getSelectBrand(@RequestParam(value = "name", required = false) String name) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(brandService.getSelectBrand(sysUser.getMemberId(), sysUser.getMemberRoleId(), name));
    }

    /**
     * 查询当前用户下状态为有效的仓库下拉框
     * @param materielId 物料Id
     */
    @GetMapping(value = "/getWarehouse")
    public WrapperResp<List<WarehouseSelectResp>> getWarehouse(@RequestParam(value = "materielId", required = false) Long materielId) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(warehouseService.getWarehouse(sysUser, materielId));
    }

    /**
     * 查询单据“入库+”下拉框
     */
    @GetMapping("/getInvoicesPlus")
    public WrapperResp<List<SelectVO>> getInvoicesPlus() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(invoicesTypeService.getInvoicesType(sysUser, InvoicesTypeEnum.STORAGE.getCode()));
    }

    /**
     * 查询单据“出库-”下拉框
     */
    @GetMapping("/getInvoicesReduce")
    public WrapperResp<List<SelectVO>> getInvoicesReduce() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(invoicesTypeService.getInvoicesType(sysUser, InvoicesTypeEnum.OUTGOING.getCode()));
    }

    /**
     * 查询品类下拉框--平台后台
     * @param name 品类名称
     * @return
     */
    @GetMapping("/getSelectCategory")
    public WrapperResp<List<SelectVO>> getSelectCategory(@RequestParam(value = "name", required = false) String name) {
        return WrapperUtil.success(categoryService.getSelectCategory(name));
    }

    /**
     * 查询品类最大级别--平台后台
     * @return
     */
    @GetMapping("/getMaxCategory")
    public WrapperResp<Integer> getMaxCategory() {
        return WrapperUtil.success(categoryService.getCategoryMaxLevel());
    }

    /**
     * 根据父级id查询品类--平台后台
     * @return
     */
    @GetMapping("/getCategoryByParentId")
    public WrapperResp<List<SelectVO>> getCategoryByParentId(@RequestParam("parentId") Long parentId) {
        return WrapperUtil.success(categoryService.getCategoryByParentId(parentId));
    }

    /**
     * 查询品牌下拉框--平台后台
     * @param name 品牌名称
     * @return
     */
    @GetMapping("/getSelectPlatformBrand")
    public WrapperResp<List<SelectVO>> getSelectPlatformBrand(@RequestParam(value = "name", required = false) String name) {
        return WrapperUtil.success(brandService.getSelectPlatformBrand(name));
    }


}
