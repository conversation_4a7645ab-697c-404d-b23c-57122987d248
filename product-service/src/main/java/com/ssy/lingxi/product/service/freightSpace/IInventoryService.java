package com.ssy.lingxi.product.service.freightSpace;

import com.ssy.lingxi.product.api.model.req.warehouse.FreightSpaceCustomInventoryReq;
import com.ssy.lingxi.product.api.model.req.warehouse.InventoryByProductIdReq;
import com.ssy.lingxi.product.api.model.req.warehouse.OccupiedInventoryReq;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
* 库存管理
* <AUTHOR>
* @since 2020/6/28
*/
public interface IInventoryService {

    /**
    * 扣除商品库存
    * <AUTHOR>
    * @since 2020/9/8
    */
    void deductInventory(InventoryByProductIdReq request);

    /**
    * 返回库存
    * <AUTHOR>
    * @since 2020/9/16
    */
    void returnInventory(InventoryByProductIdReq request);

    /**
     * 释放占用库存(订单发货)
     * @param request 参数
     */
    Boolean returnOccupiedInventory(InventoryByProductIdReq request);
    /**
     * 扣除库存(指定仓位扣除)
     * @param request 参数
     */
    Boolean deductCustomInventory(FreightSpaceCustomInventoryReq request);

    /**
     * 返回库存(指定仓位返回)
     * @param request 参数
     */
    Boolean returnCustomInventory(FreightSpaceCustomInventoryReq request);

    /**
     * 指定仓库释放占用库存(订单发货)
     * @param request 参数
     */
    Boolean returnCustomOccupiedInventory(OccupiedInventoryReq request);

    /**
     * redis库存初始化
     */
    void initFreightSpaceInventory();

    /**
     * 批量把会员库存缓存到redis中去
     */
    void inventorySynchronizationAll(List<Long> productIdList);

    /**
     * 查询redis中分配给会员的批量商品sku库存
     * @param shopIdList            商城id集合
     * @param commoditySkuIdList    商品sku集合
     * @param memberId              会员id
     * @param customerMemberId      客户会员id(代客下单场景中用到)
     */
    List<com.ssy.lingxi.product.api.model.resp.feign.CommoditySkuStockResp> getCommoditySkuRedisStockList(List<Long> shopIdList, List<Long> commoditySkuIdList, Long memberId, Long customerMemberId);

    /**
     * 获取上级供应商的商品库存
     * @param memberId 会员id
     * @param shopType 商城类型
     * @param commoditySkuIdList skuId列表
     * @return 操作结果
     */
    HashMap<Long, BigDecimal> getUpperCommoditySkuStock(Long memberId, Integer shopType, List<Long> commoditySkuIdList);
}
