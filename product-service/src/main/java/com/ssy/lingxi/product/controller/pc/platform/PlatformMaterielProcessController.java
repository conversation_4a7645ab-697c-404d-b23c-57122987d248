package com.ssy.lingxi.product.controller.pc.platform;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.model.req.ProcessQueryReq;
import com.ssy.lingxi.product.model.req.platform.*;
import com.ssy.lingxi.product.model.resp.platform.PlatformBaseMaterielProcessResp;
import com.ssy.lingxi.product.model.resp.platform.PlatformMaterielProcessDetailResp;
import com.ssy.lingxi.product.model.resp.platform.PlatformMaterielProcessMemberResp;
import com.ssy.lingxi.product.model.resp.platform.PlatformMaterielProcessPageQueryResp;
import com.ssy.lingxi.product.service.platform.IPlatformMaterielProcessService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 平台后台 - 物料流程规则配置相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/25 13:51
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/platform/material/process")
public class PlatformMaterielProcessController extends BaseController {
    private final IPlatformMaterielProcessService service;

    /**
     * 分页查询物料流程规则配置
     * @param request HttpHeaders信息
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<PlatformMaterielProcessPageQueryResp>> pageMaterialProcess(HttpServletRequest request, @Valid PlatformMaterielProcessPageDataReq pageVO) {
        return WrapperUtil.success(service.pageMaterialProcess(getSysUser(request), pageVO));
    }

    /**
     * 查询物料流程规则
     * @param request HttpHeaders信息
     * @param queryRequest 接口参数
     * @return 查询结果
     */
    @GetMapping("/list")
    public WrapperResp<List<PlatformMaterielProcessPageQueryResp>> materialProcessList(HttpServletRequest request, @Valid ProcessQueryReq queryRequest) {
        return WrapperUtil.success(service.materialProcessList(getSysUser(request), queryRequest));
    }

    /**
     * 设置物料默认流程
     * @param request HttpHeaders信息
     * @param defaultRequest 接口参数
     * @return Void
     */
    @PostMapping("/saveDefault")
    public WrapperResp<Void> saveDefault(HttpServletRequest request, @RequestBody @Valid SaveDefaultReq defaultRequest) {
        return WrapperUtil.success(service.saveDefault(getSysUser(request), defaultRequest));
    }
    
    /**
     * 查询基础物料流程列表
     * @param request Http头部信息
     * @return 查询结果
     */
    @GetMapping("/base/list")
    public WrapperResp<List<PlatformBaseMaterielProcessResp>> listBaseMaterialProcess(HttpServletRequest request, @Valid ProcessQueryReq queryRequest) {
        return WrapperUtil.success(service.listBaseMaterialProcess(getSysUser(request), queryRequest));
    }

    /**
     * 新增物料流程规则
     * @param request HttpHeaders信息
     * @param saveVO 接口参数
     * @return 新增结果
     */
    @PostMapping("/save")
    public WrapperResp<Void> save(HttpServletRequest request, @RequestBody @Valid PlatformMaterielProcessReq saveVO) {
        return WrapperUtil.success(service.save(getSysUser(request), saveVO));
    }

    /**
     * 查询物料流程规则详情
     * @param request HttpHeaders信息
     * @param processId 物料流程id， 必传且要大于0
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<PlatformMaterielProcessDetailResp> getInfo(HttpServletRequest request, @RequestParam("processId") Long processId) {
        return WrapperUtil.success(service.getInfo(getSysUser(request), processId));
    }

    /**
     * 分页查询物料流程规则适用会员列表
     * @param request HttpHeaders信息
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @GetMapping("/member/page")
    public WrapperResp<List<PlatformMaterielProcessMemberResp>> pageMaterialProcessMembers(HttpServletRequest request, @Valid PlatformMaterielProcessMemberPageDataReq pageVO) {
        return WrapperUtil.success(service.listMaterialProcessMembers(getSysUser(request), pageVO));
    }

    /**
     * 修改物料流程规则
     * @param request HttpHeaders信息
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/update")
    public WrapperResp<Void> update(HttpServletRequest request, @RequestBody @Valid PlatformMaterielProcessUpdateReq updateVO) {
        return WrapperUtil.success(service.update(getSysUser(request), updateVO));
    }

    /**
     * 修改物料流程规则状态
     * @param request HttpHeaders信息
     * @param updateStatusVO 接口参数
     * @return 修改结果
     */
    @PostMapping("/status/update")
    public WrapperResp<Void> updateStatus(HttpServletRequest request, @RequestBody @Valid PlatformMaterielProcessUpdateStatusReq updateStatusVO) {
        return WrapperUtil.success(service.updateStatus(getSysUser(request), updateStatusVO));
    }

    /**
     * 删除物料流程规则
     * @param request HttpHeaders信息
     * @param processId 物料流程规则配置id
     * @return 删除结果
     */
    @GetMapping("/delete")
    public WrapperResp<Void> delete(HttpServletRequest request, @RequestParam("processId") Long processId) {
        return WrapperUtil.success(service.delete(getSysUser(request), processId));
    }
}
