package com.ssy.lingxi.product.controller.pc.shop;

import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.NodeResp;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.product.PriceTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.EsCommoditySearchReq;
import com.ssy.lingxi.product.api.model.req.ShopSearchReq;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import com.ssy.lingxi.product.service.shop.IScoreService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 积分兑换-企业商城和店铺商城
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/8/1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/score")
public class ScoreController extends BaseController {
    private final IShopService esCommodityService;
    private final IScoreService scoreService;

    /**
     * 查询商品品类树
     * @param storeId 店铺id
     */
    @GetMapping("/getCategoryTree")
    public WrapperResp<List<NodeResp>> getCategoryTree(@RequestParam(value = "storeId", required = false) Long storeId) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(scoreService.getCategoryTree(shopId, storeId));
    }

    /**
     * 查询商品列表
     * @param shopSearchReq 查询条件实体
     */
    @PostMapping(value = "/getCommodityList")
    public WrapperResp<PageDataResp<EsCommodityResp>> searchCommodityList(@RequestBody ShopSearchReq shopSearchReq) {
        Long shopId = this.getHeadersShopId();
        EsCommoditySearchReq esCommoditySearchReq = this.modelMapper.map(shopSearchReq, EsCommoditySearchReq.class);
        esCommoditySearchReq.setShopId(shopId);
        esCommoditySearchReq.setPriceTypeList(CollUtil.toList(PriceTypeEnum.SCORE.getCode()));
        //判断是否已登录
        if(this.isLogin()){
            UserLoginCacheDTO sysUser = this.getSysUser();
            esCommoditySearchReq.setLoginMemberId(sysUser.getMemberId());
            esCommoditySearchReq.setLoginMemberRoleId(sysUser.getMemberRoleId());
        }
        return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, true, true));
    }

}
