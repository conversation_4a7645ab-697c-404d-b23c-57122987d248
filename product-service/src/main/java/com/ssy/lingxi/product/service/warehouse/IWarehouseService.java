package com.ssy.lingxi.product.service.warehouse;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.api.product.WarehouseDataSyncReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseAddOrUpdateReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseStartRoStopReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseSelectResp;

import java.util.List;
import java.util.Map;

/**
* 仓库管理
* <AUTHOR>
* @since 2020/6/19
*/
public interface IWarehouseService {
    /**
    * 查询仓库列表
    * <AUTHOR>
    * @since 2020/6/19
    */
    PageDataResp<WarehouseResp> warehouseList(WarehouseReq request, UserLoginCacheDTO sysUser);

    /**
    * 查询仓库详情
    * <AUTHOR>
    * @since 2020/8/5
    */
    WarehouseResp warehouseDetails(Long id);

    /**
    * 查询全部仓库
    * <AUTHOR>
    * @since 2020/8/5
    */
    List<WarehouseResp> warehouseAll(UserLoginCacheDTO sysUser);

    /**
    * 添加/修改仓库
    * <AUTHOR>
    * @since 2020/6/19
    */
    Void warehouseAddOrUpdate(WarehouseAddOrUpdateReq request, UserLoginCacheDTO sysUser);

    /**
    * 删除仓库
    * <AUTHOR>
    * @since 2020/6/19
    */
    Void warehouseDelete(Long id);

    /**
    * 停用启用
    * <AUTHOR>
    * @since 2020/6/19
    */
    Void warehouseStartOrStop(WarehouseStartRoStopReq request);

    /**
     * 获取仓库下拉框
     * @param materielId 物料id
     */
    List<WarehouseSelectResp> getWarehouse(UserLoginCacheDTO sysUser, Long materielId);

    /**
     * 从其他角色同步仓库数据到该角色下
     * @param sysUser   当前登录用户
     * @param warehouseMemberRoleId  需要同步的角色id
     */
    void syncWarehouse(UserLoginCacheDTO sysUser, Long warehouseMemberRoleId);

    /**
     * 数仓平台仓库信息同步处理
     * @param syncReq 参数
     */
    void warehouseSyncHandler(WarehouseDataSyncReq syncReq);

    /**
     * 根据仓库id查询仓库信息
     * @param warehouseId 仓库id
     * @return 仓库信息
     */
    WarehouseResp findById(Long warehouseId);

    /**
     * 根据仓库id列表查询仓库信息
     * @param ids
     * @return
     */
    List<WarehouseResp> findByIds(List<Long> ids);

    /**
     * 根据仓库编码获取物流模板id
     * @param warehouseCodeList 仓库编码
     * @return 物流模板id
     */
    Map<String, Long> findByWarehouseCode(List<String> warehouseCodeList);

}
