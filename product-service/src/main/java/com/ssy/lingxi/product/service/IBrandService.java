package com.ssy.lingxi.product.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.req.api.product.BrandProducerReq;
import com.ssy.lingxi.common.model.resp.select.SelectVO;
import com.ssy.lingxi.product.api.model.req.BrandReq;
import com.ssy.lingxi.product.api.model.req.CheckReq;
import com.ssy.lingxi.product.api.model.resp.BrandResp;
import com.ssy.lingxi.product.entity.do_.BrandCheckRecordDO;
import com.ssy.lingxi.product.entity.do_.BrandDO;
import com.ssy.lingxi.product.entity.do_.commodity.CommodityDO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 品牌管理类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface IBrandService {
    /**
     * 查询品牌--商品能力
     *
     * @param pageDataReq 分页实体
     * @param name   品牌名称
     * @param status 品牌状态(0-代表查全部,2-待审核,3-审核不通过,4-审核通过)
     * @return Wrapper<Page < Brand>>
     */
    Page<BrandDO> getBrandList(UserLoginCacheDTO sysUser, PageDataReq pageDataReq, String name, Integer status);

    /**
     * 查询待审核品牌列表--商品能力
     *
     * @param pageDataReq 分页实体
     * @param status 品牌状态(0-代表查全部,2-待审核,3-审核不通过,4-审核通过)
     * @return 列表
     */
    Page<BrandDO> getUnCheckBrandList(UserLoginCacheDTO sysUser, PageDataReq pageDataReq, String name, String memberName, Integer status);

    /**
     * 查询品牌
     *
     * @param id 品牌id
     * @return Wrapper<Brand>
     */
    BrandResp getBrand(UserLoginCacheDTO sysUser, Long id);

    /**
     * 添加/修改品牌
     *
     * @param brandReq 请求参数体
     * @return 操作结果
     */
    Long saveOrUpdateBrand(UserLoginCacheDTO sysUser, BrandReq brandReq);

    /**
     * 删除品牌
     * @param id 品牌id
     */
    void deleteBrand(UserLoginCacheDTO sysUser, Long id);

    /**
     * 启用/停用
     *
     * @param id       品牌id
     * @param isEnable 启用/停用
     * @return 操作结果
     */
    void updateBrandEnable(UserLoginCacheDTO sysUser, Long id, Boolean isEnable);

    /**
     * 提交审核
     *
     * @param id 品牌id
     * @return 操作结果
     */
    void applyCheckBrand(UserLoginCacheDTO sysUser, Long id);

    /**
     * 查询品牌审核记录
     *
     * @return Wrapper<?>
     */
    List<BrandCheckRecordDO> getBrandCheckRecord(Long brandId);

    /**
     * 查询品牌下拉框
     *
     * @param name 品牌名称
     * @return 操作结果
     */
    List<SelectVO> getSelectBrand(Long memberId, Long memberRoleId, String name);

    /**
     * 查询品牌下拉框--平台后台
     *
     * @param name 品牌名称
     * @return 操作结果
     */
    List<SelectVO> getSelectPlatformBrand(String name);

    /**
     * 查询品牌--平台后台
     *
     * @param pageDataReq 分页实体
     * @param name 品牌名称
     * @param status 品牌状态(0-代表查全部,2-待审核,3-审核不通过,4-审核通过)
     * @param memberName
     * @return Wrapper<Page < Brand>>
     */
    Page<BrandDO> getPlatformBrandList(PageDataReq pageDataReq, String name, Integer status, String memberName);

    /**
     * 查询品牌 -- 平台后台
     *
     * @param id 品牌id
     * @return Brand
     */
    BrandResp getBrand(Long id);

    /**
     * 查询品牌 -- 平台后台(不报异常, 没有响应null)
     * @param id        品牌id
     */
    BrandResp getBrandNonEx(Long id);

    /**
     * 查询品牌名称
     *
     * @param id 品牌id
     * @return Brand
     */
    String getBrandName(Long id);

    /**
     * 判断品牌是否可用
     * @param id 品牌id
     */
    boolean existBrand(Long id);

    /**
     * 审核-平台后台
     *
     * @param checkReq 审核实体
     * @return 操作结果
     */
    Long checkPlatformBrand(UserLoginCacheDTO sysUser, CheckReq checkReq);

    /**
     * 审核-商品能力
     * @param sysUser 当前登录用户
     * @param checkReq 审核实体
     * @return 操作结果
     */
    Long checkBrand(UserLoginCacheDTO sysUser, CheckReq checkReq);
    /**
     * 通过id查询品牌
     *
     * @param idList 品牌ids
     * @return 操作结果
     */
    List<BrandDO> getBrandByIdList(List<Long> idList);

    void initToRedis();

    /**
     * 查询待审核品牌列表--平台后台
     *
     * @param pageDataReq 分页实体
     * @param status 品牌状态(0-代表查全部,2-待审核,3-审核不通过,4-审核通过)
     * @param memberName
     * @return 列表
     */
    Page<BrandDO> getPlatformUnCheckBrandList(PageDataReq pageDataReq, Integer status, String name, String memberName);

    /**
     * 获取品牌返回值
     * @param commodityList 商品集合
     * @return 品牌列表
     */
    List<BrandResp> getBrandResponse(List<CommodityDO> commodityList);

    /**
     * 同步品牌
     * @param brandProducerRequest 请求参数体
     * @return 操作结果
     */
    Boolean syncBrand(BrandProducerReq brandProducerRequest);
}
