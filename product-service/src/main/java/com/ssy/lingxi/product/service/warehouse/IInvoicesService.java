package com.ssy.lingxi.product.service.warehouse;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.warehouse.*;
import com.ssy.lingxi.product.api.model.resp.warehouse.InvoicesDetailsResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.InvoicesListResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.InvoicesResp;
import com.ssy.lingxi.product.entity.do_.warehouse.InvoicesImportBatchNoDO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
*
* <AUTHOR>
* @since 2020/6/30
*/
public interface IInvoicesService {

    /**
    * 查询单据列表
    * <AUTHOR>
    * @since 2020/6/30
    */
    PageDataResp<InvoicesListResp> invoicesList(InvoicesListReq request, UserLoginCacheDTO sysUser );



//    /**
//    * 数据导出
//    * <AUTHOR>
//    * @since 2020/7/3
//    */
//    Wrapper importExcel(MultipartFile file);
//
//
//    /**
//    * 数据导出检查
//    * <AUTHOR>
//    * @since 2020/7/3
//    */
//    Wrapper importExcelCheck(MultipartFile file);

    /**
    * 单据单个删除/批量删除
    * <AUTHOR>
    * @since 2020/7/6
    */
    Void invoicesDelete(InvoicesDeleteReq request);

    /**
     * 单据单个审核/批量审核
     * 单据单个反审/批量反审
     * @param request
     * @return
     */
    Void invoicesReview(InvoicesReviewReq request);

    /**
    * 查询导入批次号列表
    * <AUTHOR>
    * @since 2020/7/7
    */
    List<InvoicesImportBatchNoDO> invoicesImportBatchList(InvoicesImportBatchListReq request);

    /**
    * 删除批次号并且删除导入的未审核的单据数据
    * <AUTHOR>
    * @since 2020/7/7
    */
    Void invoicesImportBatchDelete(@RequestBody InvoicesImportBatchDeleteReq request);

    /**
    * 添加/修改单据
    * <AUTHOR>
    * @since 2020/7/7
    */
    InvoicesResp invoicesAddOrUpdate(InvoicesAddOrUpdateReq request, UserLoginCacheDTO sysUser, Boolean autoCheck);

    /**
     * 自动创建单据(批量添加单据)
     */
    Void autoCreateInvoicesBatch(List<InvoicesAddOrUpdateReq> requestList, UserLoginCacheDTO sysUser);

    /**
    * 查询单据详情
    * <AUTHOR>
    * @since 2020/7/8
    */
    InvoicesDetailsResp invoicesDetails(Long id);

    /**
    * 查询单据详情
    */
    InvoicesDetailsResp invoicesDetailsByNo(InvoicesDetailsByNoReq request);
}
