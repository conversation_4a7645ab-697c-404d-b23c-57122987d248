package com.ssy.lingxi.product.controller.pc.customer;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CustomerAttributeValueReq;
import com.ssy.lingxi.product.api.model.req.SimpleStatusReq;
import com.ssy.lingxi.product.api.model.resp.CustomerAttributeValueResp;
import com.ssy.lingxi.product.entity.do_.customer.CustomerAttributeValueDO;
import com.ssy.lingxi.product.service.customer.ICustomerAttributeValueService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品能力--属性值管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/customer")
public class CustomerAttributeValueController extends BaseController {
    private final ICustomerAttributeValueService customerAttributeValueService;

    /**
     * 同步平台后台属性
     * @param commonIdListRequest 平台后台属性值id集合
     * @return
     */
    @PostMapping("/syncAttributeValue")
    public WrapperResp<Boolean> syncAttributeValue(@RequestBody CommonIdListReq commonIdListRequest) {
        return WrapperUtil.success(customerAttributeValueService.syncAttributeValue(getSysUser(), commonIdListRequest));
    }

    /**
     * 查询属性值列表
     * @param pageDataReq 分页实体
     * @param customerAttributeId 属性名id
     * @param name 属性值名称
     * @return Wrapper<PageData<CustomerAttributeValue>>
     */
    @GetMapping("/getCustomerAttributeValueList")
    public WrapperResp<PageDataResp<CustomerAttributeValueResp>> getCustomerAttributeValueList(PageDataReq pageDataReq, @RequestParam("customerAttributeId") Long customerAttributeId, @RequestParam(value = "name", required = false) String name) {
        Page<CustomerAttributeValueDO> result = customerAttributeValueService.getCustomerAttributeValueList(getSysUser(), pageDataReq, customerAttributeId, name == null ? "" : name);
        List<CustomerAttributeValueResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<CustomerAttributeValueResp>>(){}.getType());
        List<CustomerAttributeValueResp> attributeValueRespList = customerAttributeValueService.buildTree(resultList, customerAttributeId);
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), attributeValueRespList));
    }

    /**
     * 查询属性值信息
     * @param id 属性值id
     * @return Wrapper<CustomerAttributeValue>
     */
    @GetMapping("/getCustomerAttributeValue")
    public WrapperResp<CustomerAttributeValueResp> getCustomerAttributeValue(@RequestParam("id") Long id) {
        return WrapperUtil.success(customerAttributeValueService.getCustomerAttributeValue(getSysUser(), id));
    }

    /**
     * 新增/修改属性值
     * @param customerAttributeValueReq 参数
     * @return 属性值id
     */
    @PostMapping(value = "/saveOrUpdateCustomerAttributeValue")
    public WrapperResp<Long> saveOrUpdateCustomerAttributeValue(@RequestBody @Valid CustomerAttributeValueReq customerAttributeValueReq){
        return WrapperUtil.success(customerAttributeValueService.saveOrUpdateCustomerAttributeValue(getSysUser(), customerAttributeValueReq));
    }

    /**
     * 删除属性值
     * @param commonIdReq
     * @return
     */
    @PostMapping(value = "/deleteCustomerAttributeValue")
    public WrapperResp<Void> deleteCustomerAttributeValue(@RequestBody CommonIdReq commonIdReq){
        customerAttributeValueService.deleteCustomerAttributeValue(getSysUser(), commonIdReq.getId());
        return WrapperUtil.success();
    }

    /**
     * 启用/停用属性值
     * @param simpleStatusReq
     * @return
     * @throws
     */
    @PostMapping(value = "/updateCustomerAttributeValueStatus")
    public WrapperResp<Void> updateCustomerAttributeValueStatus(@RequestBody SimpleStatusReq simpleStatusReq) {
        customerAttributeValueService.updateCustomerAttributeValueStatus(getSysUser(), simpleStatusReq.getId(), simpleStatusReq.getIsEnable());
        return WrapperUtil.success();
    }
}
