package com.ssy.lingxi.product.controller.pc.shop;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.EsCommoditySearchReq;
import com.ssy.lingxi.product.api.model.req.MroSearchReq;
import com.ssy.lingxi.product.api.model.resp.MroCommoditySearchResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Collectors;

/**
 * 店铺商城
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/8/1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/mro")
public class MroController extends BaseController {
    private final IShopService esCommodityService;

    /**
     * 查询商品列表
     *
     * @param mroSearchReq
     * @return
     */
    @PostMapping("/getCommodityList")
    public WrapperResp<PageDataResp<MroCommoditySearchResp>> searchCommodityList(@RequestBody MroSearchReq mroSearchReq) {
        Long shopId = this.getHeadersShopId();
        EsCommoditySearchReq esCommoditySearchReq = this.modelMapper.map(mroSearchReq, EsCommoditySearchReq.class);
        esCommoditySearchReq.setShopId(shopId);

        //判断是否已登录
        if (this.isLogin()) {
            UserLoginCacheDTO sysUser = this.getSysUser();
            esCommoditySearchReq.setLoginMemberId(sysUser.getMemberId());
            esCommoditySearchReq.setLoginMemberRoleId(sysUser.getMemberRoleId());
        }

        return shopSearch(esCommoditySearchReq);
    }

    /**
     * 企业商城
     *
     * @param esCommoditySearchReq
     * @return
     */
    private WrapperResp<PageDataResp<MroCommoditySearchResp>> shopSearch(EsCommoditySearchReq esCommoditySearchReq) {
        PageDataResp<EsCommodityResp> pageDataResp = esCommodityService.searchCommodityList(esCommoditySearchReq, true, false);
        return WrapperUtil.success(new PageDataResp<>(pageDataResp.getTotalCount(), pageDataResp
                .getData()
                .stream()
                .map(commoditySearchResponse -> {
                    MroCommoditySearchResp mroCommoditySearchResp = new MroCommoditySearchResp();
                    BeanUtil.copyProperties(commoditySearchResponse, mroCommoditySearchResp);
                    return mroCommoditySearchResp;
                }).collect(Collectors.toList())));
    }
}
