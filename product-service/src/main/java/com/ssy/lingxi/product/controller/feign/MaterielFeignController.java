package com.ssy.lingxi.product.controller.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.feign.IMaterielFeign;
import com.ssy.lingxi.product.service.materiel.IMaterielService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 货品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/2
 * @ignore 不需要提交到Yapi
 */
@RestController
@RequiredArgsConstructor
public class MaterielFeignController extends BaseController implements IMaterielFeign {
    private final IMaterielService materielService;

    /**
     * 查询商品品类id列表--通过物料集合
     * @param materielCodeList 请求参数
     * @return 商品信息
     */
    @Override
    public WrapperResp<Map<String, Long>> getCategoryIdByMaterielCodeList(@RequestBody List<String> materielCodeList) {
        return WrapperUtil.success(materielService.getCategoryIdByMaterielCodeList(materielCodeList));
    }

}
