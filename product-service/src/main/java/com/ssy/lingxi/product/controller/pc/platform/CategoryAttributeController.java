package com.ssy.lingxi.product.controller.pc.platform;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.CategoryAttributeReq;
import com.ssy.lingxi.product.api.model.req.CategoryAttributeSortReq;
import com.ssy.lingxi.product.api.model.resp.AttributeResp;
import com.ssy.lingxi.product.entity.do_.platform.AttributeDO;
import com.ssy.lingxi.product.service.platform.ICategoryAttributeService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 平台后台--品类关联属性
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/platform")
public class CategoryAttributeController extends BaseController {
    private final ICategoryAttributeService CategoryAttributeService;

    /**
     * 查询品类下的属性列表
     * @param pageDataReq 分页实体
     * @param name 属性名称
     * @param categoryId 品类id
     * @param isByCategory 是否查询该品类下的属性
     * @return
     */
    @GetMapping("/getCategoryAttributeList")
    public WrapperResp<PageDataResp<AttributeResp>> getCategoryAttributeList(PageDataReq pageDataReq, @RequestParam(value = "name", required = false) String name, @RequestParam(value = "groupName", required = false) String groupName, @RequestParam(value = "categoryId") Long categoryId, @RequestParam("isByCategory") Boolean isByCategory) {
        Page<AttributeDO> result = CategoryAttributeService.getCategoryAttributeList(pageDataReq, name, groupName, categoryId, isByCategory);
        List<AttributeResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<AttributeResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 查询品类下的属性列表-MRO模式 按照特定的排序返回结果
     *
     * @param name         属性名称
     * @param categoryId   品类id
     * @param isByCategory 是否查询该品类下的属性
     * @return
     */
    @GetMapping("/getMroCategoryAttributeList")
    public WrapperResp<List<AttributeDO>> getMroCategoryAttributeList(@RequestParam(value = "name", required = false) String name, @RequestParam(value = "groupName", required = false) String groupName, @RequestParam(value = "categoryId") Long categoryId, @RequestParam("isByCategory") Boolean isByCategory) {
        return WrapperUtil.success(CategoryAttributeService.getMroCategoryAttributeList(name, groupName, categoryId, isByCategory));
    }

    /**
     * 新增/修改品类下的属性
     * @param categoryAttributeReq
     * @return
     */
    @PostMapping("/saveCategoryAttribute")
    public WrapperResp<String> saveCategoryAttribute(@RequestBody CategoryAttributeReq categoryAttributeReq){
        return WrapperUtil.success(CategoryAttributeService.saveCategoryAttribute(categoryAttributeReq));
    }

    /**
     * 解除品类属性关系
     * @param categoryAttributeReq
     * @return
     */
    @PostMapping("deleteCategoryAttribute")
    public WrapperResp<String> deleteCategoryAttribute(@RequestBody CategoryAttributeReq categoryAttributeReq){
        return WrapperUtil.success(CategoryAttributeService.deleteCategoryAttribute(categoryAttributeReq));
    }

    /**
     * 新增/修改品类下的属性排序
     *
     * @param categoryAttributeSortReqList
     * @return
     */
    @PostMapping("/saveCategoryAttributeSort")
    public WrapperResp<Boolean> saveCategoryAttributeSort(@RequestBody List<CategoryAttributeSortReq> categoryAttributeSortReqList) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(CategoryAttributeService.saveCategoryAttributeSort(sysUser, categoryAttributeSortReqList));
    }

}
