package com.ssy.lingxi.product.controller.pc.warehouse;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.warehouse.DistributableInventoryReq;
import com.ssy.lingxi.product.api.model.req.warehouse.InventoryListReq;
import com.ssy.lingxi.product.api.model.req.warehouse.InventorySafetyUpdateReq;
import com.ssy.lingxi.product.api.model.req.warehouse.QueryInventoryByItemNoReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.DistributableInventoryResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.InventoryListResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.QueryInventoryByItemNoResp;
import com.ssy.lingxi.product.service.warehouse.IWarehouseInventoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 进销存-库存管理
 * <AUTHOR>
 * @since 2020/7/20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/inventory")
public class WarehouseInventoryController extends BaseController {
    private final IWarehouseInventoryService inventoryService;

    /**
     * 库存列表查询
     * @param request 参数
     */
    @GetMapping("/list")
    public WrapperResp<PageDataResp<InventoryListResp>> inventoryList(InventoryListReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(inventoryService.inventoryList(request,sysUser));
    }

    /**
     * 单个/批量调整安全库存
     * @param request 参数
     */
    @PostMapping("/safety/batch/update")
    public WrapperResp<Void> inventorySafetyUpdate(@RequestBody InventorySafetyUpdateReq request) {
        return WrapperUtil.success(inventoryService.inventorySafetyUpdate(request));
    }

    /**
    * 根据仓库名称和货号ID查询仓位库存
    * <AUTHOR>
    * @since 2020/8/7
    */
    @GetMapping("/by/item/no")
    public WrapperResp<QueryInventoryByItemNoResp> queryInventoryByItemNo(QueryInventoryByItemNoReq request) {
        return WrapperUtil.success(inventoryService.queryInventoryByItemNo(request));
    }

    /**
     * 批量查询仓库下的物料对应的可配货库存
     * @param request 参数
     */
    @PostMapping("/getWarehouseDistributableInventory")
    public WrapperResp<List<DistributableInventoryResp>> getWarehouseDistributableInventory(@RequestBody @Valid DistributableInventoryReq request) {
        return WrapperUtil.success(inventoryService.getWarehouseDistributableInventory(request));
    }

}
