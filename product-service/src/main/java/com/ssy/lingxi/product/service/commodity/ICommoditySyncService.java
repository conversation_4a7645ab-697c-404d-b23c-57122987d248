package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.req.api.product.CommodityPriceProducerReq;
import com.ssy.lingxi.common.model.req.api.product.CommodityProducerReq;

/**
 * 商品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface ICommoditySyncService {

    /**
     * 同步商品信息
     * @param commodityProducerRequest 参数
     */
    Boolean syncCommodity(CommodityProducerReq commodityProducerRequest);

    /**
     * 同步商品价格
     * @param commodityPriceProducerRequest 参数
     * @return 是否成功
     */
    Boolean syncCommodityPrice(CommodityPriceProducerReq commodityPriceProducerRequest);
}
