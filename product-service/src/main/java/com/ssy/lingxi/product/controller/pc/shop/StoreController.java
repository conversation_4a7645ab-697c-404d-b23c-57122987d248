package com.ssy.lingxi.product.controller.pc.shop;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.resp.ProvinceResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.EsCommoditySearchReq;
import com.ssy.lingxi.product.api.model.req.ShopSearchCustomerReq;
import com.ssy.lingxi.product.api.model.req.ShopSearchReq;
import com.ssy.lingxi.product.api.model.resp.BrandResp;
import com.ssy.lingxi.product.api.model.resp.CategoryTreeBrandResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsAttributeResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityDetailResp;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommodityResp;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import com.ssy.lingxi.product.service.shop.IStoreService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 店铺商城
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/8/1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/store")
public class StoreController extends BaseController {
    private final IStoreService storeService;
    private final IShopService esCommodityService;

    /**
     * 查询商品会员品类树
     * @param storeId 店铺id
     * @return
     */
    @GetMapping("/getCustomerCategoryTree")
    public WrapperResp<List<CategoryTreeBrandResp>> getCustomerCategoryTree(@RequestParam(value = "storeId") Long storeId) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(storeService.getCategoryTreeAndBrand(shopId, storeId));
    }

    /**
     * 查询商品属性
     * @param storeId 店铺id
     * @param categoryId 品类id
     * @return
     */
    @GetMapping(value = "/getCustomerAttributeByCategoryId")
    public WrapperResp<List<EsAttributeResp>> getCustomerAttributeByCategoryId(@RequestParam(value = "storeId") Long storeId, @RequestParam(value = "categoryId") Long categoryId) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(storeService.getAttributeByCategoryId(shopId, storeId, categoryId));
    }

    /**
     * 查询商品品牌
     * @param storeId 店铺id
     * @return
     */
    @GetMapping("/getBrand")
    public WrapperResp<List<BrandResp>> getBrand(@RequestParam(value = "storeId") Long storeId) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(storeService.getBrand(shopId, storeId));
    }

    /**
     * 查询归属地市
     */
    @GetMapping("/getArea")
    public WrapperResp<List<ProvinceResp>> getArea() {
        return WrapperUtil.success(AreaUtil.findAllAsTree());
    }

    /**
     * 查询商品最大价格(积分)
     * @param storeId 店铺id
     * @return
     */
    @GetMapping("/getCommodityMaxPrice")
    public WrapperResp<BigDecimal> getCommodityMaxPrice(@RequestParam(value = "storeId", required = false) Long storeId) {
        return WrapperUtil.success(esCommodityService.getCommodityMaxPrice(storeId));
    }

    /**
     * 查询商品列表
     * @param shopSearchReq 查询条件实体
     * @return
     */
    @PostMapping(value = "/getCommodityList")
    public WrapperResp<PageDataResp<EsCommodityResp>> searchCommodityList(@RequestBody ShopSearchReq shopSearchReq) {
        Long shopId = this.getHeadersShopId();
        Long storeId = shopSearchReq.getStoreId();
        if(storeId != null && storeId > 0){
            EsCommoditySearchReq esCommoditySearchReq = this.modelMapper.map(shopSearchReq, EsCommoditySearchReq.class);
            esCommoditySearchReq.setShopId(shopId);
            //判断是否已登录
            if(this.isLogin()){
                UserLoginCacheDTO sysUser = this.getSysUser();
                esCommoditySearchReq.setLoginMemberId(sysUser.getMemberId());
                esCommoditySearchReq.setLoginMemberRoleId(sysUser.getMemberRoleId());
            }
            return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, true, false));
        }else{
            throw new BusinessException(ResponseCodeEnum.PRODUCT_STORE_NOT_EXIST);
        }
    }

    /**
     * 查询商品详情
     * @param commodityId 商品id
     * @return
     */
    @GetMapping(value = "/getCommodityDetail")
    public WrapperResp<EsCommodityDetailResp> getCommodityDetail(@RequestParam("commodityId") Long commodityId) {
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = null;
        //判断当前用户是否已登录
        if(this.isLogin()){
            sysUser = this.getSysUser();
        }
        return WrapperUtil.success(esCommodityService.getCommodityDetail(commodityId, shopId, sysUser));
    }

    /**
     * 通过商品skuId查询商品详情
     * @param commoditySkuId 商品skuId
     * @return
     */
    @GetMapping(value = "/getCommodityDetailBySkuId")
    public WrapperResp<EsCommodityDetailResp> getCommodityDetailBySkuId(@RequestParam("commoditySkuId") Long commoditySkuId) {
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = null;
        //判断当前用户是否已登录
        if(this.isLogin()){
            sysUser = this.getSysUser();
        }
        return WrapperUtil.success(esCommodityService.getCommodityDetailBySkuId(commoditySkuId, shopId, sysUser));
    }

    /**
     * 查询商品列表--代客下单
     * @param shopSearchCustomerReq 查询条件实体
     * @return
     */
    @PostMapping(value = "/getCustomerCommodityList")
    public WrapperResp<PageDataResp<EsCommodityResp>> getCustomerCommodityList(@RequestBody ShopSearchCustomerReq shopSearchCustomerReq) {
        Long shopId = this.getHeadersShopId();
        Long storeId = shopSearchCustomerReq.getStoreId();
        if(storeId != null && storeId > 0){
            EsCommoditySearchReq esCommoditySearchReq = this.modelMapper.map(shopSearchCustomerReq, EsCommoditySearchReq.class);
            esCommoditySearchReq.setShopId(shopId);
            esCommoditySearchReq.setLoginMemberId(shopSearchCustomerReq.getCustomerMemberId());
            esCommoditySearchReq.setLoginMemberRoleId(shopSearchCustomerReq.getCustomerMemberRoleId());
            return WrapperUtil.success(esCommodityService.searchCommodityList(esCommoditySearchReq, true, false));
        }else{
            throw new BusinessException(ResponseCodeEnum.PRODUCT_STORE_NOT_EXIST);
        }
    }

    /**
     * 查询商品详情--代客下单
     * @param commodityId 商品id
     * @return
     */
    @GetMapping(value = "/getCustomerCommodityDetail")
    public WrapperResp<EsCommodityDetailResp> getCustomerCommodityDetail(@RequestParam("commodityId") Long commodityId, @RequestParam("customerMemberId") Long customerMemberId, @RequestParam("customerMemberRoleId") Long customerMemberRoleId) {
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = new UserLoginCacheDTO();
        sysUser.setMemberId(customerMemberId);
        sysUser.setMemberRoleId(customerMemberRoleId);
        return WrapperUtil.success(esCommodityService.getCommodityDetail(commodityId, shopId, sysUser));
    }

    /**
     * 通过商品skuId查询商品详情--代客下单
     * @param commoditySkuId 商品skuId
     * @return
     */
    @GetMapping(value = "/getCustomerCommodityDetailBySkuId")
    public WrapperResp<EsCommodityDetailResp> getCustomerCommodityDetailBySkuId(@RequestParam("commoditySkuId") Long commoditySkuId, @RequestParam("customerMemberId") Long customerMemberId, @RequestParam("customerMemberRoleId") Long customerMemberRoleId) {
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = new UserLoginCacheDTO();
        sysUser.setMemberId(customerMemberId);
        sysUser.setMemberRoleId(customerMemberRoleId);
        return WrapperUtil.success(esCommodityService.getCommodityDetailBySkuId(commoditySkuId, shopId, sysUser));
    }
}
