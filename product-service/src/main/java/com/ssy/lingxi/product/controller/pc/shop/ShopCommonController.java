package com.ssy.lingxi.product.controller.pc.shop;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商城公共接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/8/1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/common")
public class ShopCommonController extends BaseController {
    private final IShopService esCommodityService;

    /**
     * 会员商品搜索自动补全
     * @param name 商品名称
     * @return
     */
    @GetMapping("/getCommodityCompletion")
    public WrapperResp<List<String>> getCommodityCompletion(@RequestParam(value = "name") String name) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(esCommodityService.getCommodityCompletion(shopId, null, name));
    }

    /**
     * 渠道商品搜索自动补全
     * @param name 商品名称
     * @return
     */
    @GetMapping("/getChannelCommodityCompletion")
    public WrapperResp<List<String>> getChannelCommodityCompletion(@RequestParam(value = "name") String name) {
        Long shopId = this.getHeadersShopId();
        return WrapperUtil.success(esCommodityService.getCommodityCompletion(null, shopId, name));
    }

    /**
     * 查询商品是否上架
     * @param commodityId 商品id
     * @param type 商城类型：1.企业商城 2.积分商城 3.渠道商城 4.渠道自有商城 5.渠道积分商城
     * @param channelMemberId 渠道会员id
     * @return
     */
    @GetMapping("/getCommodityIsPublish")
    public WrapperResp<Boolean> getCommodityIsPublish(@RequestParam("commodityId") Long commodityId, @RequestParam(value = "type", required = false) Integer type, @RequestParam(value = "channelMemberId", required = false) Long channelMemberId) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        if(type == null){
            type = 1;
        }
        return WrapperUtil.success(esCommodityService.getCommodityIsPublish(commodityId, type, channelMemberId, sysUser.getMemberId()));
    }

    /**
     * 通过商品skuId查询商品库存
     * @param commoditySkuId 商品skuId
     */
    @GetMapping("/getCommodityStockBySkuId")
    public WrapperResp<BigDecimal> getCommodityStockBySkuId(@RequestParam("commoditySkuId") Long commoditySkuId) {
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(esCommodityService.getCommodityStockBySkuId(shopId, sysUser.getMemberId(), commoditySkuId));
    }
}
