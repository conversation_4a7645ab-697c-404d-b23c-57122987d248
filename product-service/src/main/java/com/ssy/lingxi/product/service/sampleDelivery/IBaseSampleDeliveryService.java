package com.ssy.lingxi.product.service.sampleDelivery;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.product.api.model.req.sampleDelivery.SampleDeliveryUpdateByQualityOrderReq;
import com.ssy.lingxi.product.api.model.resp.ProcessDefResp;
import com.ssy.lingxi.product.entity.do_.sampledelivery.SampleDeliveryDO;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryDetailResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryOuterHistoryResp;
import com.ssy.lingxi.workflow.api.model.resp.SimpleTaskCompleteResp;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 送样需求单 -基础业务
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/23 15:09
 */
public interface IBaseSampleDeliveryService {

    /**
     * 启动并执行工作流
     *
     * @param sysUser    登录用户信息
     * @param id         主键id
     * @param processKey 工作流key
     * @return 任务执行返回
     */
    SimpleTaskCompleteResp startSimpleProcessThenCompleteFirstTask(UserLoginCacheDTO sysUser, Long id, String processKey);

    /**
     * 执行工作流
     *
     * @param memberId     会员id
     * @param memberRoleId 角色id
     * @param id           主键id
     * @param processKey   工作流key
     * @param taskId       任务id
     * @param agree        同意状态
     * @return 任务执行返回数据
     */
    SimpleTaskCompleteResp executeWork(Long memberId, Long memberRoleId, Long id, String processKey, String taskId, Integer agree);

    /**
     * 获取工作流信息
     *
     * @param sampleDeliveryDO 送样需求单信息
     * @param memberId         会员id
     * @return 工作流信息
     */
    ProcessDefResp getProcessInfo(SampleDeliveryDO sampleDeliveryDO, Long memberId);

    /**
     * 保存外部流转记录
     *
     * @param sampleDeliveryDO 送样需求单实体
     * @param memberId         操作会员id
     * @param roleId           操作角色id
     * @param memberName       操作会员名称
     * @param roleName         操作角色名称
     * @param operateCode      操作码
     * @param operation        操作描述
     * @param remark           备注
     * @param createTime       创建时间
     */
    void saveOuterHistory(SampleDeliveryDO sampleDeliveryDO, Long memberId, Long roleId, String memberName, String roleName
            , Integer operateCode, String operation, String remark, LocalDateTime createTime);

    /**
     * 获取外部流转记录信息
     *
     * @param sampleDeliveryId 送样需求单id
     * @return 外部流转记录信息
     */
    List<SampleDeliveryOuterHistoryResp> getOuterHistories(Long sampleDeliveryId);

    /**
     * 获取送样需求单详情信息
     *
     * @param sampleDeliveryDO 送样需求单实体
     * @param memberId         会员id
     * @return
     */
    SampleDeliveryDetailResp getDetail(SampleDeliveryDO sampleDeliveryDO, Long memberId);

    /**
     * 获取送样需求单状态下拉框数据
     *
     * @return 送样需求单状态下拉框数据
     * @param sysUser
     */
    List<DropdownItemResp> statusDropItems(UserLoginCacheDTO sysUser);

    /**
     * 获取送样需求单类型下拉框数据
     *
     * @return 送样需求单类型下拉框数据
     */
    List<DropdownItemResp> typeDropItems();

    /**
     * 获取送样需求单紧急程度下拉框数据
     *
     * @return 送样需求单紧急程度下拉框数据
     */
    List<DropdownItemResp> emergencyLevelDropItems();

    /**
     * 将质检单信息同步更新到送样需求单 (内部接口)
     *
     * @param updateVO 质检单同步信息
     */
    void updateByQualityOrder(SampleDeliveryUpdateByQualityOrderReq updateVO);
}
