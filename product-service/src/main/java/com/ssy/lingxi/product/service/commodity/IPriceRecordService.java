package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.product.api.model.resp.CommodityPriceRecordResp;

/**
 * 商品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface IPriceRecordService {

    /**
     * 查询商品历史价格
     * @param commoditySkuId  商品SkuId
     * @param days         最近多少天
     * @return 商品历史价格
     */
    CommodityPriceRecordResp getPriceRecord(Long commoditySkuId, Integer days);
}
