package com.ssy.lingxi.product.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.SaleGroupSaveReq;
import com.ssy.lingxi.product.api.model.req.StatusUpdateReq;
import com.ssy.lingxi.product.api.model.resp.SaleGroupDetailResp;
import com.ssy.lingxi.product.api.model.resp.SaleGroupResp;

/**
 * 销售群组接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-13
 */
public interface ISaleGroupService {

    /**
     * 新增或修改
     * @param saveReq 参数
     * @param sysUser 用户
     */
    void addOrUpdate(SaleGroupSaveReq saveReq, UserLoginCacheDTO sysUser);

    /**
     * 销售群组列表分页
     * @param groupName 参数
     * @param pageDataReq 分页
     * @param sysUser 用户
     * @return 结果
     */
    PageDataResp<SaleGroupResp> getListByPage(String groupName, Integer status, PageDataReq pageDataReq, UserLoginCacheDTO sysUser);

    /**
     * 修改状态
     * @param updateReq 参数
     * @param sysUser 用户
     */
    void updateStatus(StatusUpdateReq updateReq, UserLoginCacheDTO sysUser);

    /**
     * 销售群组详情
     * @param id ID
     * @return 结果
     */
    SaleGroupDetailResp getDetailById(Long id);

    /**
     * 删除
     * @param id ID
     */
    void deleteById(Long id, UserLoginCacheDTO sysUser);
}
