package com.ssy.lingxi.product.service.sampleDelivery;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.model.req.sampledelivery.*;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryBuyerManageQueryResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryBuyerQueryResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryCreateQualityOrderResp;
import com.ssy.lingxi.product.model.resp.sampledelivery.SampleDeliveryDetailResp;

/**
 * 送样需求单 - 采购商业务
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/23 14:18
 */
public interface IBuyerSampleDeliveryService {
    /**
     * 送样需求单分页列表
     *
     * @param pageVO  分页查询传参
     * @param sysUser 登录用户信息
     * @return 分页列表
     */
    PageDataResp<SampleDeliveryBuyerQueryResp> page(SampleDeliveryBuyerPageDataReq pageVO, UserLoginCacheDTO sysUser);

    /**
     * 送样需求单详情
     *
     * @param id      主键id
     * @param sysUser 登录用户信息
     * @return 送样需求单详情
     */
    SampleDeliveryDetailResp detail(Long id, UserLoginCacheDTO sysUser);

    /**
     * 新增送样需求单
     *
     * @param sampleDeliverySaveReq 新增传参
     * @param sysUser              登录用户信息
     * @param scenes               场景
     */
    void create(SampleDeliverySaveReq sampleDeliverySaveReq, UserLoginCacheDTO sysUser, Integer scenes);

    /**
     * 更新送样需求单
     *
     * @param sampleDeliveryUpdateReq 更新传参
     * @param sysUser                登录用户信息
     */
    void update(SampleDeliveryUpdateReq sampleDeliveryUpdateReq, UserLoginCacheDTO sysUser);

    /**
     * 删除送样需求单
     *
     * @param id      主键id
     * @param sysUser 登录用户信息
     */
    void delete(Long id, UserLoginCacheDTO sysUser);

    /**
     * 取消送样需求单
     *
     * @param cancelVO 取消传参
     * @param sysUser  登录用户信息
     */
    void cancel(SampleDeliveryCancelReq cancelVO, UserLoginCacheDTO sysUser);

    /**
     * 提交送样需求单
     *
     * @param id      主键id
     * @param sysUser 登录用户信息
     */
    void submit(Long id, UserLoginCacheDTO sysUser);

    /**
     * 待收样送样需求单列表
     * @param sysUser 登录用户信息
     * @param pageVO 分页查询传参
     * @return 分页列表
     */
    PageDataResp<SampleDeliveryBuyerManageQueryResp> pageToReceive(SampleDeliveryBuyerReceivePageDataReq pageVO, UserLoginCacheDTO sysUser);

    /**
     * 确认收样
     *
     * @param id      主键id
     * @param sysUser 登录用户信息
     */
    void receiveSample(Long id, UserLoginCacheDTO sysUser);

    /**
     * 退样
     *
     * @param sampleDeliveryReturnReq 退样传参
     * @param sysUser                登录用户信息
     */
    void returnSample(SampleDeliveryReturnReq sampleDeliveryReturnReq, UserLoginCacheDTO sysUser);

    /**
     * 送样需求单管理分页列表
     *
     * @param pageVO  分页查询传参
     * @param sysUser 登录用户信息
     * @param scenes  场景
     * @return 分页列表
     */
    PageDataResp<SampleDeliveryBuyerManageQueryResp> pageByManage(SampleDeliveryBuyerManagePageDataReq pageVO, UserLoginCacheDTO sysUser, Integer scenes);

    /**
     * “送样需求单” - 生成质检单所需数据
     *
     * @param sysUser 登录用户信息
     * @param id      主键id
     * @return 生成质检单所需数据
     */
    SampleDeliveryCreateQualityOrderResp qualityCreateBySampleDelivery(UserLoginCacheDTO sysUser, Long id);

}
