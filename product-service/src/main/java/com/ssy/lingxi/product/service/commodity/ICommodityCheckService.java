package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.rabbitMQ.model.req.SystemMessageReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityCheckBatchReq;
import com.ssy.lingxi.product.entity.do_.commodity.CommodityDO;
import com.ssy.lingxi.product.model.resp.platform.CommodityCheckRecordResp;

import java.util.List;

/**
 * 商品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface ICommodityCheckService {

    /**
     * 审核--平台后台
     * @param id id
     * @return 操作结果
     */
    void checkCommodity(UserLoginCacheDTO sysUser, Long id, Integer status, String checkRemark);

    /**
     * 审核--平台后台
     */
    void checkCommodityBatch(UserLoginCacheDTO sysUser, CommodityCheckBatchReq commodityCheckReq);

    /**
     * 保存审核记录
     * @param sysUser               登录用户
     * @param commodityDO           商品
     * @param operation             审核状态
     * @param operation             操作
     * @param checkRemark           备注
     * @param createTime            创建时间
     * @param systemMessageReq      消息
     */
    void saveCommodityCheckRecord(UserLoginCacheDTO sysUser, CommodityDO commodityDO, Integer checkStatus, Integer operation, String checkRemark, Long createTime, SystemMessageReq systemMessageReq);

    /**
     * 查询商品审核记录--平台后台
     * @return Wrapper<?>
     */
    List<CommodityCheckRecordResp> getCommodityCheckRecord(Long commodityId);

    /**
     * 查询商品审核记录--平台后台
     * @return Wrapper<?>
     */
    PageDataResp<CommodityCheckRecordResp> getCommodityCheckRecordList(PageDataReq pageDataReq, Long commodityId, Long startTime, Long endTime);
}
