package com.ssy.lingxi.product.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.api.product.CommodityBaseAttributeReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.StatusUpdateReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityDescribeAttributeConfigReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityDescribeAttributeQueryReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityDescribeAttributeSaveReq;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeConfigResp;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeDetailResp;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeListResp;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeResp;

import java.util.List;

/**
 * 商品描述属性服务
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-10
 */
public interface ICommodityDescribeAttributeService {

    /**
     * 商品描述属性列表分页查询
     * @param queryReq 参数
     * @param sysUser 用户
     * @return 结果
     */
    PageDataResp<CommodityDescribeAttributeResp> getListByPage(CommodityDescribeAttributeQueryReq queryReq, UserLoginCacheDTO sysUser);

    /**
     * 根据ID查询详情
     * @param id ID
     * @return 结果
     */
    CommodityDescribeAttributeResp getDetailById(Long id);

    /**
     * 根据ID查询详情
     * @param id ID
     * @return 结果
     */
    CommodityDescribeAttributeDetailResp getDetailDataById(Long id);

    /**
     * 新增或修改商品描述属性
     * @param saveReq 参数
     * @param sysUser 用户
     */
    void addOrUpdate(CommodityDescribeAttributeSaveReq saveReq, UserLoginCacheDTO sysUser);

    /**
     * 获取所有可用的属性
     * @return 结果
     */
    List<CommodityDescribeAttributeDetailResp> getAllUsableList();

    /**
     * 基础属性同步处理
     * @param attributeReq 参数
     */
    void baseAttributeSyncHandler(CommodityBaseAttributeReq attributeReq);

    /**
     * 修改状态
     * @param updateReq 参数
     * @param sysUser 用户
     */
    void updateStatus(StatusUpdateReq updateReq, UserLoginCacheDTO sysUser);

    /**
     * 保存配置
     * @param configReq 参数
     * @param sysUser 用户
     */
    void savaConfig(CommodityDescribeAttributeConfigReq configReq, UserLoginCacheDTO sysUser);

    /**
     * 获取属性的配置信息
     * @param id 属性ID
     * @return 结果
     */
    CommodityDescribeAttributeConfigResp getConfig(Long id);

    /**
     * 删除
     * @param id 参数
     */
    void deleteById(Long id);

    /**
     * 七种固定属性初始化
     */
    void attributeInit();

    /**
     * 商品筛选项获取描述属性分页列表
     * @param queryReq 参数
     * @return 结果
     */
    PageDataResp<CommodityDescribeAttributeListResp> getDetailListByPage(CommodityDescribeAttributeQueryReq queryReq);
}
