package com.ssy.lingxi.product.controller.pc.warehouse;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.warehouse.*;
import com.ssy.lingxi.product.api.model.resp.warehouse.InvoicesDetailsResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.InvoicesListResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.InvoicesResp;
import com.ssy.lingxi.product.entity.do_.warehouse.InvoicesImportBatchNoDO;
import com.ssy.lingxi.product.service.warehouse.IInvoicesService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 进销存-单据管理
 * <AUTHOR>
 * @since 2020/7/20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX)
public class InvoicesController extends BaseController {
    private final IInvoicesService invoicesService;

    /**
    * 单据管理列表查询
    */
    @GetMapping("/invoices/list")
    public WrapperResp<PageDataResp<InvoicesListResp>> invoicesList(InvoicesListReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(invoicesService.invoicesList(request, sysUser));
    }

    /**
    * 添加/修改单据
    */
    @PostMapping("/invoices/addOrUpdate")
    public WrapperResp<InvoicesResp> invoicesAddOrUpdate(@Valid @RequestBody InvoicesAddOrUpdateReq request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(invoicesService.invoicesAddOrUpdate(request, sysUser, false));
    }

    /**
     * 单据单个删除/批量删除
     * @param request
     * @return
     */
    @PostMapping("/invoices/batch/delete")
    public WrapperResp invoicesDelete(@RequestBody InvoicesDeleteReq request) {
        return WrapperUtil.success(invoicesService.invoicesDelete(request));
    }

    /**
     * 单据单个审核/批量审核
     * 单据单个反审/批量反审
     * @param request
     * @return
     */
    @PostMapping("/invoices/batch/review")
    public WrapperResp<Void> invoicesReview(@Valid @RequestBody InvoicesReviewReq request) {
        return WrapperUtil.success(invoicesService.invoicesReview(request));
    }

    /**
     * 查询导入批次号列表
     * @param request
     * @return
     */
    @GetMapping("/invoices/import/batch/list")
    public WrapperResp<List<InvoicesImportBatchNoDO>> invoicesImportBatchList(InvoicesImportBatchListReq request) {
        return WrapperUtil.success(invoicesService.invoicesImportBatchList(request));
    }

    /**
     * 删除批次
     * @param request
     * @return
     */
    @PostMapping("/invoices/import/batch/delete")
    public WrapperResp<Void> invoicesImportBatchDelete(@RequestBody InvoicesImportBatchDeleteReq request) {
        return WrapperUtil.success(invoicesService.invoicesImportBatchDelete(request));
    }

    /**
     * 查看单据详情
     * @param id
     * @return
     */
    @GetMapping("/invoices/details")
    public WrapperResp<InvoicesDetailsResp> invoicesDetails(Long id) {
        return WrapperUtil.success(invoicesService.invoicesDetails(id));
    }

    /**
    * 根据单号查看单据详情
    * <AUTHOR>
    * @since 2021/1/4
    */
    @GetMapping("/invoices/details/byNo")
    public WrapperResp<InvoicesDetailsResp> invoicesDetailsByNo(InvoicesDetailsByNoReq request) {
        return WrapperUtil.success(invoicesService.invoicesDetailsByNo(request));
    }
}
