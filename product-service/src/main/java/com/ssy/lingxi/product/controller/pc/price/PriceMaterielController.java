package com.ssy.lingxi.product.controller.pc.price;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.price.MaterielPriceQueryReq;
import com.ssy.lingxi.product.api.model.req.price.PriceAddReq;
import com.ssy.lingxi.product.api.model.req.price.PriceHistoryQueryReq;
import com.ssy.lingxi.product.api.model.resp.price.MaterielPriceResp;
import com.ssy.lingxi.product.api.model.resp.price.PriceHistoryResp;
import com.ssy.lingxi.product.entity.do_.materiel.MaterielDO;
import com.ssy.lingxi.product.entity.do_.price.MaterielPriceDO;
import com.ssy.lingxi.product.entity.do_.price.MaterielPriceHistoryDO;
import com.ssy.lingxi.product.service.price.IPriceManagementService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物料价格管理模块
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/25
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/price/materiel")
public class PriceMaterielController extends BaseController {
    private final IPriceManagementService priceManagementService;

    /**
     * 查询物料价格列表
     * @param pageDataReq 分页实体
     * @param materielPriceQueryReq 物料价格查询类
     */
    @GetMapping(value = "/getMaterielPriceList")
    public WrapperResp<PageDataResp<MaterielPriceResp>> getMaterielPriceList(PageDataReq pageDataReq, MaterielPriceQueryReq materielPriceQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Page<MaterielPriceDO> result = priceManagementService.getMaterielPriceList(sysUser, pageDataReq, materielPriceQueryReq);
        if (result.getTotalElements() <= 0){
            return WrapperUtil.success(new PageDataResp<>(0L, new ArrayList<>()));
        }
        List<MaterielPriceResp> resultList = result.getContent().stream().map(price -> {
            MaterielPriceResp response = BeanUtil.copyProperties(price, MaterielPriceResp.class);
            MaterielDO materielDO = price.getMateriel();
            BeanUtil.copyProperties(materielDO, response);
            response.setMaterielCode(materielDO.getCode());
            response.setCode(price.getCode());
            response.setId(price.getId());
            return response;
        }).collect(Collectors.toList());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 物料价格历史列表 - 新增市场价
     * @param addRequest 价格信息
     */
    @GetMapping(value = "/addMaterielPrice")
    public WrapperResp<Void> AddMaterielPrice(@Validated PriceAddReq addRequest) {
        return WrapperUtil.success(priceManagementService.AddMaterielPrice(addRequest));
    }

    /**
     * 查询物料价格历史列表 - 查看单个物料历史价格详情
     * @param pageDataReq 分页实体
     * @param queryRequest 物料价格历史信息查询类
     */
    @GetMapping(value = "/getMaterielPriceHistory")
    public WrapperResp<PageDataResp<PriceHistoryResp>> getMaterielPriceHistory(PageDataReq pageDataReq, PriceHistoryQueryReq queryRequest) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        PageDataResp<MaterielPriceHistoryDO> result = priceManagementService.getMaterielPriceHistory(sysUser, pageDataReq, queryRequest);
        List<PriceHistoryResp> resultList = this.modelMapper.map(result.getData(), new TypeToken<List<PriceHistoryResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalCount(), resultList));
    }

}
