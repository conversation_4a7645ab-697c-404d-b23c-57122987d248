package com.ssy.lingxi.product.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.req.api.product.CommodityBaseAttributeValueReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityDescribeAttributeValueSaveReq;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeValueResp;

import java.util.List;

/**
 * 商品描述属性字段属性值服务接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-10
 */
public interface ICommodityDescribeAttributeValueService {

    /**
     * 新增或修改
     * @param saveReq 参数
     * @param sysUser 用户
     */
    Long addOrUpdate(CommodityDescribeAttributeValueSaveReq saveReq, UserLoginCacheDTO sysUser);

    /**
     * 根据描述属性ID查询属性值列表分页
     * @param attributeId 属性ID
     * @param pageDataReq 分页
     * @param sysUser 用户
     * @return 结果
     */
    PageDataResp<CommodityDescribeAttributeValueResp> getListByPage(Long attributeId, PageDataReq pageDataReq, UserLoginCacheDTO sysUser);

    /**
     * 根据描述属性ID查询属性值列表
     * @param attributeId 属性ID
     * @return 结果
     */
    List<CommodityDescribeAttributeValueResp> getListByAttributeId(Long attributeId);

    /**
     * 属性值同步处理
     * @param attributeValueReq 参数
     */
    void baseAttributeValueSyncHandler(CommodityBaseAttributeValueReq attributeValueReq);

    /**
     * 根据属性类型查询属性值列表
     * @param attributeType 属性类型
     */
    List<CommodityDescribeAttributeValueResp> getListByAttributeType(Integer attributeType);

    /**
     * 根据ID列表查询关联的属性值列表
     * @param idList ID列表
     * @return 结果
     */
    List<CommodityDescribeAttributeValueResp> getAssociationListByIds(List<Long> idList);
}
