package com.ssy.lingxi.product.controller.pc.shop;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.PurchaseBatchReq;
import com.ssy.lingxi.product.api.model.req.PurchaseReq;
import com.ssy.lingxi.product.entity.do_.PurchaseDO;
import com.ssy.lingxi.product.model.req.PurchaseQueryReq;
import com.ssy.lingxi.product.model.resp.PurchaseResp;
import com.ssy.lingxi.product.service.shop.IPurchaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商城--进货单管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/4
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/purchase")
public class PurchaseController extends BaseController {
    private final IPurchaseService purchaseService;

    /**
     * 查询进货单列表
     * @return 操作结果
     */
    @GetMapping("/getPurchaseList")
    public WrapperResp<List<PurchaseResp>> getPurchaseList() {
        UserLoginCacheDTO sysUser = getSysUser();
        PurchaseQueryReq purchaseQueryReq = new PurchaseQueryReq();
        purchaseQueryReq.setShopId(getHeadersShopId());
        purchaseQueryReq.setMemberId(sysUser.getMemberId());
        purchaseQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        purchaseQueryReq.setUserId(sysUser.getUserId());
        purchaseQueryReq.setBranchId(sysUser.getBranchId());
        return WrapperUtil.success(purchaseService.getPurchaseList(purchaseQueryReq, true));
    }

    /**
     * 新增/修改进货单
     * @param purchaseReq 进货单实体
     * @return 进货单数量
     */
    @PostMapping("/saveOrUpdatePurchase")
    public WrapperResp<Integer> saveOrUpdatePurchase(@RequestBody PurchaseReq purchaseReq){
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        PurchaseDO purchaseDO = BeanUtil.copyProperties(purchaseReq, PurchaseDO.class);
        purchaseDO.setShopId(shopId);
        purchaseDO.setBranchId(sysUser.getBranchId());
        return WrapperUtil.success(purchaseService.saveOrUpdatePurchase(sysUser, purchaseDO, purchaseReq.getPurchaseProductPositionRequest()));
    }

    /**
     * 批量新增进货单(MRO模式)
     * @param purchaseBatchReq 进货单实体
     * @return 是否成功
     */
    @PostMapping("/savePurchaseBatchByMro")
    public WrapperResp<Integer> savePurchaseBatchByMro(@RequestBody PurchaseBatchReq purchaseBatchReq){
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        List<PurchaseReq> purchaseList = purchaseBatchReq.getPurchaseBatchList();
        for (PurchaseReq purchaseReq : purchaseList) {
            purchaseReq.setBranchId(sysUser.getBranchId());
        }
        return WrapperUtil.success(purchaseService.savePurchaseBatch(sysUser, shopId, purchaseList));
    }

    /**
     * 删除进货单
     * @param commonIdListRequest id集合
     * @return 删除结果
     */
    @PostMapping("/deletePurchase")
    public WrapperResp<String> deletePurchase(@RequestBody CommonIdListReq commonIdListRequest){
        return WrapperUtil.success(purchaseService.deletePurchase(commonIdListRequest));
    }

    /**
     * 查询进货单数量
     */
    @GetMapping("/getPurchaseCount")
    public WrapperResp<Integer> getPurchaseCount(){
        UserLoginCacheDTO sysUser = getSysUser();
        PurchaseQueryReq purchaseQueryReq = new PurchaseQueryReq();
        purchaseQueryReq.setMemberId(sysUser.getMemberId());
        purchaseQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        purchaseQueryReq.setShopId(getHeadersShopId());
        purchaseQueryReq.setBranchId(sysUser.getBranchId());
        return WrapperUtil.success(purchaseService.getPurchaseCount(purchaseQueryReq));
    }

}
