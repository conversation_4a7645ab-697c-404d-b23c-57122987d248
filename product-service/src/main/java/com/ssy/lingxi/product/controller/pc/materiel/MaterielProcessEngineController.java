package com.ssy.lingxi.product.controller.pc.materiel;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.engine.RuleEngineProcessReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.RuleEngineProcessResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.model.req.materiel.MaterielProcessPageDataReq;
import com.ssy.lingxi.product.model.resp.materiel.MaterielProcessPageQueryResp;
import com.ssy.lingxi.product.service.materiel.IMaterielProcessEngineService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 商品能力 - 物料流程规则引擎配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/4/24
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/material/process/engine")
public class MaterielProcessEngineController extends BaseController {
    private final IMaterielProcessEngineService materialProcessEngineService;

    /**
     * 分页查询物料流程规则引擎
     *
     * @param pageVO 分页查询条件
     * @return 查询结果
     */
    @GetMapping("/page")
    public WrapperResp<PageDataResp<MaterielProcessPageQueryResp>> pageMaterialProcesses(@Valid MaterielProcessPageDataReq pageVO) {
        return WrapperUtil.success(materialProcessEngineService.pageMaterialProcesses(getSysUser(), pageVO));
    }

    /**
     * 查询物料流程规则引擎详情
     *
     * @return 查询结果
     */
    @GetMapping("/get")
    public WrapperResp<RuleEngineProcessResp> getMaterialProcesses(@Valid RuleEngineProcessReq request) {
        return WrapperUtil.success(materialProcessEngineService.getMaterialProcesses(getSysUser(), request));
    }

}
