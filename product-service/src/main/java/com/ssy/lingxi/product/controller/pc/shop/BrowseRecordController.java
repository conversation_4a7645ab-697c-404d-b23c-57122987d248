package com.ssy.lingxi.product.controller.pc.shop;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.model.resp.BrowseRecordResp;
import com.ssy.lingxi.product.service.shop.IBrowseRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 商城--浏览记录管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/9/18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/browseRecord")
public class BrowseRecordController extends BaseController {
    private final IBrowseRecordService browseRecordService;

    /**
     * IM功能 查询全部商城的浏览记录列表
     *
     * @param current  当前页
     * @param pageSize 每页行数
     */
    @GetMapping("/getBrowseRecordListAll")
    public WrapperResp<PageDataResp<BrowseRecordResp>> getBrowseRecordListAll(@RequestParam("current") int current, @RequestParam("pageSize") int pageSize) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(browseRecordService.getBrowseRecordList(sysUser, current, pageSize));
    }
    /**
     * 查询浏览记录列表
     * @param current 当前页
     * @param pageSize 每页行数
     * @return
     */
    @GetMapping("/getBrowseRecordList")
    public WrapperResp<PageDataResp<BrowseRecordResp>> getBrowseRecordList(@RequestParam("current") int current, @RequestParam("pageSize") int pageSize) {
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(browseRecordService.getBrowseRecordList(sysUser, shopId, current, pageSize));
    }

    /**
     * 删除浏览记录
     * @param commonIdListRequest
     * @return
     */
    @PostMapping("/deleteBrowseRecord")
    public WrapperResp<Boolean> deleteBrowseRecord(@RequestBody CommonIdListReq commonIdListRequest){
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(browseRecordService.deleteBrowseRecord(sysUser, shopId, commonIdListRequest));
    }

    /**
     * 清空浏览记录
     * @return
     */
    @PostMapping("/clearBrowseRecord")
    public WrapperResp<Boolean> clearBrowseRecord(){
        Long shopId = this.getHeadersShopId();
        UserLoginCacheDTO sysUser = this.getSysUser();
        return WrapperUtil.success(browseRecordService.clearBrowseRecord(sysUser, shopId));
    }
}
