package com.ssy.lingxi.product.controller.pc.commodity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.resp.OftenBuyCommodityResp;
import com.ssy.lingxi.product.service.commodity.IOftenBuyCommodityService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商品常购清单管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodity/oftenBuy")
public class OftenBuyController extends BaseController {
    private final IOftenBuyCommodityService oftenBuyService;

    /**
     * 查询常购清单列表
     * @param pageDataReq 分页实体
     * @param name 商品名称
     * @return 常购清单列表
     */
    @GetMapping(value = "/getOftenBuyCommodityList")
    public WrapperResp<PageDataResp<OftenBuyCommodityResp>> getOftenBuyCommodityList(PageDataReq pageDataReq, @RequestParam(value = "name", required = false) String name) {
        return WrapperUtil.success(oftenBuyService.getOftenBuyCommodityList(pageDataReq, getHeadersShopId(), name, getSysUser()));
    }

}
