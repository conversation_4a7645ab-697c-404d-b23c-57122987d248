package com.ssy.lingxi.product.service.freightSpace;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.product.api.model.req.warehouse.FreightSpaceRuleConfigReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.FreightSpaceRuleConfigResp;

/**
 * 仓位库存规则配置
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/7/5
 */
public interface IFreightSpaceRuleConfigService {

    /**
     * 查询仓位库存规则配置信息
     */
    FreightSpaceRuleConfigResp getFreightSpaceRuleConfig(UserLoginCacheDTO sysUser);

    /**
     * 新增/修改仓位库存规则配置
     * @param freightSpaceRuleConfigReq 仓位库存规则配置实体
     * @return 仓位库存规则配置id
     */
    Long saveOrUpdateFreightSpaceRuleConfig(UserLoginCacheDTO sysUser, FreightSpaceRuleConfigReq freightSpaceRuleConfigReq);
}
