package com.ssy.lingxi.product.controller.pc.commodity;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySalesAreaTreeResp;
import com.ssy.lingxi.product.entity.do_.commodity.CommoditySalesAreaTemplateDO;
import com.ssy.lingxi.product.service.commodity.ICommoditySalesAreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品管理-销售区域
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodity/salesArea")
public class CommoditySalesAreaController extends BaseController {
    private final ICommoditySalesAreaService commoditySalesAreaService;

    /**
     * 销售区域列表(查看区域)
     *
     * @param templateId 销售区域模板ID
     * @see CommoditySalesAreaTemplateDO#getId() 销售区域模板ID
     */
    @GetMapping("/list")
    public WrapperResp<List<CommoditySalesAreaTreeResp>> list(@RequestParam Long templateId) {
        return WrapperUtil.success(commoditySalesAreaService.list(getSysUser(), templateId));
    }

}
