package com.ssy.lingxi.product.service.commodity;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityShopBatchReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityShopReq;
import com.ssy.lingxi.product.api.model.req.feign.CommodityNameReq;
import com.ssy.lingxi.product.api.model.resp.commodity.CommodityBaseResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CommodityShopResp;

import java.util.List;

/**
 * 商品上下架管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface ICommodityPublishService {

    /**
     * 商品上架/下架--商品能力
     * @param commodityShopReq 请求参数
     * @param isPublish 是否上架
     * @return 操作结果
     */
    void publishCommodity(CommodityShopReq commodityShopReq, boolean isPublish);

    /**
     * 商品上架/下架--商品能力
     * @param commodityShopBatchReq 请求参数
     * @param isPublish 是否上架
     * @return 操作结果
     */
    void publishCommodityBatch(UserLoginCacheDTO sysUser, CommodityShopBatchReq commodityShopBatchReq, boolean isPublish);

    /**
     * 下架某个会员的所有商品--商品能力
     * @param memberId 会员id
     * @param memberRoleId 会员角色id
     * @return 操作结果
     */
    void offPublishAllCommodity(Long memberId, Long memberRoleId);

    /**
     * 过滤出已上架的商品Id列表
     * @return 操作结果
     */
    List<Long> getCommodityListByIsPublish(Long shopId, List<CommodityNameReq> commodityNameList);

    /**
     * 判断商品是否上架
     * @return 操作结果
     */
    List<CommodityBaseResp> getCommodityInfoListByIsPublish(Long shopId, List<CommodityNameReq> commodityNameList);

    /**
     * 判断商品是否上架
     * @param commodityId 商品id
     * @param shopId 商城id
     */
    Boolean getCommodityIsPublish(Long commodityId, Long shopId);

    /**
     * 获取上架商城(单个商品上架)--商品能力
     * @param id 商品id
     * @return 操作结果
     */
    List<CommodityShopResp> getShop(Long id);

    /**
     * 获取已上架的商城
     */
    List<CommodityShopResp> getPublishedShop(Long id);

    /**
     * 获取上架商城(批量上架)--商品能力
     * @param idList 商品id数组
     * @return 操作结果
     */
    List<CommodityShopResp> getShopBatch(List<Long> idList);

}
