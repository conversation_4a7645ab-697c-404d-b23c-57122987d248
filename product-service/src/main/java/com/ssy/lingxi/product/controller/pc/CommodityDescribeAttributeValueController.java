package com.ssy.lingxi.product.controller.pc;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.req.api.product.CommodityBaseAttributeValueReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityDescribeAttributeValueSaveReq;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeValueResp;
import com.ssy.lingxi.product.service.ICommodityDescribeAttributeValueService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 商品描述属性字段属性值管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-10
 */
@RestController
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodityDescribeAttributeValue")
public class CommodityDescribeAttributeValueController extends BaseController {

    @Resource
    private ICommodityDescribeAttributeValueService commodityDescribeAttributeValueService;

    /**
     * 新增或修改属性值
     * @param saveReq 参数
     */
    @PostMapping("/addOrUpdate")
    public WrapperResp<Long> addOrUpdate(@RequestBody @Valid CommodityDescribeAttributeValueSaveReq saveReq){
        return WrapperUtil.success(commodityDescribeAttributeValueService.addOrUpdate(saveReq, getSysUser()));
    }

    /**
     * 根据描述属性ID查询属性值列表分页
     * @param commonIdReq 属性ID
     * @param pageDataReq 分页
     */
    @GetMapping("/getListByPage")
    public WrapperResp<PageDataResp<CommodityDescribeAttributeValueResp>> getListByPage(CommonIdReq commonIdReq, PageDataReq pageDataReq){
        return WrapperUtil.success(commodityDescribeAttributeValueService.getListByPage(commonIdReq.getId(), pageDataReq, getSysUser()));
    }

    /**
     * 根据描述属性ID查询属性值列表
     * @param commonIdReq 属性ID
     */
    @GetMapping("/getListByAttributeId")
    public WrapperResp<List<CommodityDescribeAttributeValueResp>> getListByAttributeId(CommonIdReq commonIdReq){
        return WrapperUtil.success(commodityDescribeAttributeValueService.getListByAttributeId(commonIdReq.getId()));
    }

    /**
     * 根据属性类型查询属性值列表
     * @param attributeType 属性类型
     */
    @GetMapping("/getListByAttributeType")
    public WrapperResp<List<CommodityDescribeAttributeValueResp>> getListByAttributeType(@RequestParam Integer attributeType){
        return WrapperUtil.success(commodityDescribeAttributeValueService.getListByAttributeType(attributeType));
    }

    /**
     * 根据ID列表查询关联的属性值列表
     * @param idListReq ID列表
     */
    @GetMapping("/getAssociationListByIds")
    public WrapperResp<List<CommodityDescribeAttributeValueResp>> getAssociationListByIds(CommonIdListReq idListReq){
        return WrapperUtil.success(commodityDescribeAttributeValueService.getAssociationListByIds(idListReq.getIdList()));
    }

    /**
     * 手动处理属性值同步
     * @param valueReq 参数
     */
    @PostMapping("/baseAttributeValueSyncHandler")
    public WrapperResp<Void> baseAttributeValueSyncHandler(@RequestBody CommodityBaseAttributeValueReq valueReq){
        commodityDescribeAttributeValueService.baseAttributeValueSyncHandler(valueReq);
        return WrapperUtil.success();
    }
}
