package com.ssy.lingxi.product.controller.pc;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.StatusUpdateReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityDescribeAttributeConfigReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityDescribeAttributeQueryReq;
import com.ssy.lingxi.product.api.model.req.commodity.CommodityDescribeAttributeSaveReq;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeConfigResp;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeDetailResp;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeListResp;
import com.ssy.lingxi.product.api.model.resp.CommodityDescribeAttributeResp;
import com.ssy.lingxi.product.service.ICommodityDescribeAttributeService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 商品描述属性管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-10
 */
@RestController
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/commodityDescribeAttribute")
public class CommodityDescribeAttributeController extends BaseController {

    @Resource
    private ICommodityDescribeAttributeService commodityDescribeAttributeService;

    /**
     * 商品描述属性列表分页查询
     * @param queryReq 参数
     */
    @GetMapping("/getListByPage")
    public WrapperResp<PageDataResp<CommodityDescribeAttributeResp>> getListByPage(CommodityDescribeAttributeQueryReq queryReq){
        return WrapperUtil.success(commodityDescribeAttributeService.getListByPage(queryReq, getSysUser()));
    }

    /**
     * 根据ID查询详情
     * @param commonIdReq 参数
     */
    @GetMapping("/getDetailById")
    public WrapperResp<CommodityDescribeAttributeDetailResp> getDetailById(CommonIdReq commonIdReq){
        getSysUser();
        return WrapperUtil.success(commodityDescribeAttributeService.getDetailDataById(commonIdReq.getId()));
    }

    /**
     * 新增或修改
     * @param saveReq 参数
     */
    @PostMapping("/addOrUpdate")
    public WrapperResp<Void> addOrUpdate(@RequestBody @Valid CommodityDescribeAttributeSaveReq saveReq){
        commodityDescribeAttributeService.addOrUpdate(saveReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 获取所有可用的动态属性
     */
    @GetMapping("/getAllUsableList")
    public WrapperResp<List<CommodityDescribeAttributeDetailResp>> getAllUsableList(){
        return WrapperUtil.success(commodityDescribeAttributeService.getAllUsableList());
    }

    /**
     * 修改状态
     * @param updateReq 参数
     */
    @PostMapping("/updateStatus")
    public WrapperResp<Void> updateStatus(@RequestBody @Valid StatusUpdateReq updateReq){
        commodityDescribeAttributeService.updateStatus(updateReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 保存配置
     * @param configReq 参数
     */
    @PostMapping("/savaConfig")
    public WrapperResp<Void> savaConfig(@RequestBody @Valid CommodityDescribeAttributeConfigReq configReq){
        commodityDescribeAttributeService.savaConfig(configReq, getSysUser());
        return WrapperUtil.success();
    }

    /**
     * 获取属性的配置信息
     * @param idReq 属性ID
     */
    @GetMapping("/getConfig")
    public WrapperResp<CommodityDescribeAttributeConfigResp> getConfig(CommonIdReq idReq){
        return WrapperUtil.success(commodityDescribeAttributeService.getConfig(idReq.getId()));
    }

    /**
     * 删除
     * @param idReq 参数
     */
    @PostMapping("/deleteById")
    public WrapperResp<Void> deleteById(@RequestBody @Valid CommonIdReq idReq){
        commodityDescribeAttributeService.deleteById(idReq.getId());
        return WrapperUtil.success();
    }

    /**
     * 七种固定属性初始化
     */
    @PostMapping("/attributeInit")
    public WrapperResp<Void> attributeInit(){
        commodityDescribeAttributeService.attributeInit();
        return WrapperUtil.success();
    }

    /**
     * 商品筛选项获取描述属性分页列表
     * @param queryReq 参数
     */
    @GetMapping("/getDetailListByPage")
    public WrapperResp<PageDataResp<CommodityDescribeAttributeListResp>> getDetailListByPage(CommodityDescribeAttributeQueryReq queryReq){
        return WrapperUtil.success(commodityDescribeAttributeService.getDetailListByPage(queryReq));
    }
}
