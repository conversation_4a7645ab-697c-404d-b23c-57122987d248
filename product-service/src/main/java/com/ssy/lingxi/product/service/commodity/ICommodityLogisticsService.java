package com.ssy.lingxi.product.service.commodity;

/**
 * 商品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
public interface ICommodityLogisticsService {

    /**
     * 查询商品是否用到运费模板
     * @param id 运费模板id
     * @return 操作结果
     */
    boolean getCommodityUseTemplate(Long id);

    /**
     * 查询商品是否用到发货地址
     * @param id 发货地址id
     * @return 操作结果
     */
    boolean getCommodityUseSendAddress(Long id);

    /**
     * 查询商品是否用到物流公司
     * @param id 物流公司id
     * @return 操作结果
     */
    boolean getCommodityUseCompany(Long id);
}
