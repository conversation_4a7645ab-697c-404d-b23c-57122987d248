package com.ssy.lingxi.product.serviceImpl.shop;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.ssy.lingxi.commodity.api.model.resp.StoreInnerResp;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.DepositTypeEnum;
import com.ssy.lingxi.common.model.dto.InventoryByProductDTO;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonNoListReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.marketing.ActivityTypeEnum;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.redis.service.IRedissonUtils;
import com.ssy.lingxi.component.rest.model.resp.eos.GoldPriceResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.logistics.api.feign.IShipperAddressFeign;
import com.ssy.lingxi.logistics.api.model.resp.ShipperAddressDetailResp;
import com.ssy.lingxi.marketing.api.feign.IActivityGoodsFeign;
import com.ssy.lingxi.marketing.api.model.request.GoodsCartSkuItemReq;
import com.ssy.lingxi.member.api.feign.IMemberCustomerProcessFeeDiscountFeign;
import com.ssy.lingxi.member.api.feign.IMemberLevelRightCreditFeign;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLevelDetailResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignRightDetailResp;
import com.ssy.lingxi.member.api.model.resp.MobileCustomerFeeDiscountResp;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.MemberAndRoleIdFeignReq;
import com.ssy.lingxi.order.api.model.req.OrderFreeExpressFeignReq;
import com.ssy.lingxi.order.api.model.resp.OrderDepositResp;
import com.ssy.lingxi.order.api.model.resp.OrderFreeExpressConfigResp;
import com.ssy.lingxi.product.api.enums.FreightSpaceSingleProductStatusEnum;
import com.ssy.lingxi.product.api.model.req.PurchaseCustomerReq;
import com.ssy.lingxi.product.api.model.req.PurchaseOrderImportReq;
import com.ssy.lingxi.product.api.model.req.PurchaseProductPositionReq;
import com.ssy.lingxi.product.api.model.req.PurchaseReq;
import com.ssy.lingxi.product.api.model.resp.MobileOrderProductResp;
import com.ssy.lingxi.product.api.model.resp.PurchaseProductPositionResp;
import com.ssy.lingxi.product.api.model.resp.baitai.FeeDiscountDetailsResp;
import com.ssy.lingxi.product.api.model.resp.baitai.OrderProductResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.*;
import com.ssy.lingxi.product.constant.ProductConstant;
import com.ssy.lingxi.product.entity.do_.PurchaseDO;
import com.ssy.lingxi.product.entity.do_.PurchaseProductPositionDO;
import com.ssy.lingxi.product.entity.do_.freightSpace.FreightSpaceSingleProductDO;
import com.ssy.lingxi.product.entity.do_.warehouse.WarehouseDO;
import com.ssy.lingxi.product.entity.do_.warehouse.WarehouseLogisticsDO;
import com.ssy.lingxi.product.entity.esCommodity.EsCommodity;
import com.ssy.lingxi.product.entity.esCommodity.EsCommodityShop;
import com.ssy.lingxi.product.entity.esCommodity.EsCommoditySku;
import com.ssy.lingxi.product.entity.esCommodity.EsCommoditySkuAttribute;
import com.ssy.lingxi.product.enums.PurchaseCommodityTypeEnum;
import com.ssy.lingxi.product.model.req.PurchaseQueryReq;
import com.ssy.lingxi.product.model.resp.PurchaseCommodityResp;
import com.ssy.lingxi.product.model.resp.PurchaseResp;
import com.ssy.lingxi.product.model.resp.PurchaseSkuResp;
import com.ssy.lingxi.product.repository.commodity.FreightSpaceSingleProductRepository;
import com.ssy.lingxi.product.repository.esCommodity.EsCommodityRepository;
import com.ssy.lingxi.product.repository.shop.PurchaseProductPositionRepository;
import com.ssy.lingxi.product.repository.shop.PurchaseRepository;
import com.ssy.lingxi.product.repository.warehouse.WarehouseLogisticsRepository;
import com.ssy.lingxi.product.repository.warehouse.WarehouseRepository;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import com.ssy.lingxi.product.service.feign.ICommodityFeignService;
import com.ssy.lingxi.product.service.freightSpace.IFreightSpaceService;
import com.ssy.lingxi.product.service.freightSpace.IInventoryService;
import com.ssy.lingxi.product.service.shop.IPurchaseService;
import com.ssy.lingxi.product.service.shop.ISelfService;
import com.ssy.lingxi.product.service.warehouse.IWarehouseService;
import com.ssy.lingxi.product.util.DataNullSafeGetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum.ORDER;
import static com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum.SPOT;

/**
 * 进货单实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/22
 */
@Slf4j
@Service
public class PurchaseServiceImpl implements IPurchaseService {

    @Resource
    private PurchaseRepository purchaseRepository;

    @Resource
    private EsCommodityRepository esCommodityRepository;

    @Resource
    private IShopService shopService;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private IRedissonUtils redissonUtils;

    @Resource
    private IMemberLevelRightCreditFeign memberLevelRightCreditFeign;

    @Resource
    private IOrderProcessFeign orderFeignService;

    @Resource
    private IActivityGoodsFeign activityGoodsFeign;

    @Resource
    private IMemberLevelRightCreditFeign iMemberLevelRightCreditFeign;

    @Resource
    private PurchaseProductPositionRepository purchaseProductPositionRepository;

    @Resource
    private ICommodityFeignService commodityFeignService;

    @Resource
    private IInventoryService inventoryService;

    @Resource
    private IFreightSpaceService freightSpaceService;

    @Resource
    private ISelfService selfService;

    @Resource
    private FreightSpaceSingleProductRepository freightSpaceSingleProductRepository;

    @Resource
    private IWarehouseService warehouseService;

    @Resource
    private IMemberCustomerProcessFeeDiscountFeign memberCustomerProcessFeeDiscountFeign;

    @Resource
    private EosApiService eosApiService;

    @Resource
    private IOrderProcessFeign orderProcessFeign;

    @Resource
    private WarehouseLogisticsRepository warehouseLogisticsRepository;

    @Resource
    private WarehouseRepository warehouseRepository;

    @Resource
    private IShipperAddressFeign shipperAddressFeign;

    /**
     * 查询进货单列表
     *
     * @param purchaseQueryReq 参数
     * @param flag             是否需要查询活动数据
     */
    @Override
    public List<PurchaseResp> getPurchaseList(PurchaseQueryReq purchaseQueryReq, Boolean flag) {
        List<Long> skuIds = new ArrayList<>();
        List<PurchaseResp> purchaseRespList = new ArrayList<>();
        Long shopId = purchaseQueryReq.getShopId();
        Long memberId = purchaseQueryReq.getMemberId();
        Long memberRoleId = purchaseQueryReq.getMemberRoleId();
        Long customerMemberId = purchaseQueryReq.getCustomerMemberId();
        Long customerMemberRoleId = purchaseQueryReq.getCustomerMemberRoleId();
        OrderDepositResp depositResp = null;
        if (purchaseQueryReq.getSaleMode().equals(CommoditySaleModeEnum.ORDER.getCode())) {
            // 查看-订货订单定金/支付手续费/打包费配置
            WrapperResp<OrderDepositResp> orderConfig = orderProcessFeign.findOrderConfig();
            depositResp = orderConfig.getData();
        }

        //查询进货单
        Pageable page = PageRequest.of(0, ProductConstant.PURCHASE_MAX_COUNT);
        Page<PurchaseDO> purchasePage = purchaseRepository.findAll(getSpecification(purchaseQueryReq), page);
        List<PurchaseDO> purchaseDOList = purchasePage.getContent();

        //判断是否代客下单
        if (customerMemberId == null || customerMemberId <= 0) {
            skuIds = purchaseDOList.stream().map(PurchaseDO::getCommoditySkuId).collect(Collectors.toList());
        }

        if (!CollectionUtils.isEmpty(purchaseDOList)) {
            //定义临时变量
            List<Long> commoditySkuIdList = new ArrayList<>();
            List<MemberFeignUpperMemberReq> memberFeignUpperMemberReqList = new ArrayList<>();
            List<GoodsCartSkuItemReq> goodsCartSkuItemReqList = new ArrayList<>();
            List<MemberAndRoleIdFeignReq> memberAndRoleIdFeignReqList = new ArrayList<>();

            //查询登录用户的所有上级对应的会员等级
            Map<String, Integer> memberLevelMap = new HashMap<>();
            MemberFeignReq memberFeignReq = new MemberFeignReq();
            //判断是否代客下单
            if (customerMemberId != null && customerMemberId > 0) {
                memberFeignReq.setMemberId(customerMemberId);
                memberFeignReq.setRoleId(customerMemberRoleId);
            } else {
                memberFeignReq.setMemberId(memberId);
                memberFeignReq.setRoleId(memberRoleId);
            }
            WrapperResp<List<MemberFeignLevelDetailResp>> subMemberLevels = iMemberLevelRightCreditFeign.findSubMemberLevels(memberFeignReq);
            if (subMemberLevels != null && ResponseCodeEnum.SUCCESS.getCode() == subMemberLevels.getCode()) {
                List<MemberFeignLevelDetailResp> data = subMemberLevels.getData();
                if (!CollectionUtils.isEmpty(data)) {
                    data.stream().filter(memberFeignLevelDetailVO -> memberFeignLevelDetailVO.getRelType() == 1).forEach(memberFeignLevelDetailVO -> {
                        Long upperMemberId = memberFeignLevelDetailVO.getUpperMemberId();
                        Long upperRoleId = memberFeignLevelDetailVO.getUpperRoleId();
                        Integer level = memberFeignLevelDetailVO.getLevel();
                        memberLevelMap.put(upperMemberId + Constant.UNDERLINE_STR + upperRoleId, level);
                    });
                }
            }

            MemberFeignBatchReq memberFeignBatchReq = new MemberFeignBatchReq();
            //判断是否代客下单
            if (customerMemberId != null && customerMemberId > 0) {
                memberFeignBatchReq.setMemberId(customerMemberId);
                memberFeignBatchReq.setRoleId(customerMemberRoleId);
            } else {
                memberFeignBatchReq.setMemberId(memberId);
                memberFeignBatchReq.setRoleId(memberRoleId);
            }


            purchaseDOList.forEach(purchase -> {
                Long commoditySkuId = purchase.getCommoditySkuId();
                Integer purchaseCommodityType = purchase.getPurchaseCommodityType();

                //记录所有skuId，用于查询商品活动信息
                GoodsCartSkuItemReq goodsCartSkuItemReq = new GoodsCartSkuItemReq();
                goodsCartSkuItemReq.setSkuId(commoditySkuId);
                if (purchaseCommodityType != null && purchaseCommodityType.equals(PurchaseCommodityTypeEnum.SET_MEAL.getCode())) {
                    goodsCartSkuItemReq.setActivityType(ActivityTypeEnum.SET_MEAL.getCode());
                    goodsCartSkuItemReq.setGroupNo(purchase.getSetMealId().intValue());
                    goodsCartSkuItemReq.setParentSkuId(purchase.getParentSkuId());
                } else if (purchaseCommodityType != null && purchaseCommodityType.equals(PurchaseCommodityTypeEnum.EXCHANGE.getCode())) {
                    goodsCartSkuItemReq.setActivityType(ActivityTypeEnum.SWAP.getCode());
                    goodsCartSkuItemReq.setParentSkuId(purchase.getParentSkuId());
                }
                goodsCartSkuItemReq.setIsMain(purchase.getIsMain());
                goodsCartSkuItemReqList.add(goodsCartSkuItemReq);

                //记录所有skuId，用于查询商品
                commoditySkuIdList.add(commoditySkuId);
            });

            //批量查询商品信息
            HashMap<Long, EsCommodity> commodityHashMap = new HashMap<>();
            List<EsCommodity> commodityList = esCommodityRepository.findByCommoditySkuListIdIn(commoditySkuIdList);
            log.info("commodityList: {}", JSONUtil.toJsonStr(commodityList));
            //查询店铺信息
            Map<Long, StoreInnerResp> storeMap = Maps.newHashMap();
            List<Long> storeIds = commodityList.stream().map(EsCommodity::getStoreId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(storeIds)) {
                storeMap = commodityFeignService.getStoreListByIds(storeIds).stream().collect(Collectors.toMap(StoreInnerResp::getId, Function.identity()));
            }
            // 获取实时金价
            BigDecimal goldPrice = selfService.getGoldPrice();
            ;
            commodityList.forEach(commodity -> {
                List<EsCommoditySku> commoditySkuList = commodity.getCommoditySkuList();
                commoditySkuList.forEach(commoditySku -> commodityHashMap.put(commoditySku.getId(), commodity));

                //记录所有供应商信息，用于查询价格权益
                MemberFeignUpperMemberReq memberFeignUpperMemberReq = new MemberFeignUpperMemberReq();
                memberFeignUpperMemberReq.setUpperMemberId(commodity.getMemberId());
                memberFeignUpperMemberReq.setUpperRoleId(commodity.getMemberRoleId());
                if (!memberFeignUpperMemberReqList.contains(memberFeignUpperMemberReq)) {
                    memberFeignUpperMemberReqList.add(memberFeignUpperMemberReq);
                }

                //记录所有供应商信息，用于查询商品是否满额包邮
                MemberAndRoleIdFeignReq memberAndRoleIdFeignReq = new MemberAndRoleIdFeignReq();
                memberAndRoleIdFeignReq.setMemberId(commodity.getMemberId());
                memberAndRoleIdFeignReq.setRoleId(commodity.getMemberRoleId());
                if (!memberAndRoleIdFeignReqList.contains(memberAndRoleIdFeignReq)) {
                    memberAndRoleIdFeignReqList.add(memberAndRoleIdFeignReq);
                }
            });

            //批量查询商品活动信息
//            Map<String, GoodsCartResp> goodsCartResponseHashMap = new HashMap<>();
//            try {
//                if (Boolean.TRUE.equals(flag)) {
//                    GoodsCartReq goodsCartReq = new GoodsCartReq();
//                    //判断是否代客下单
//                    if (customerMemberId != null && customerMemberId > 0) {
//                        goodsCartReq.setMemberId(customerMemberId);
//                        goodsCartReq.setRoleId(customerMemberRoleId);
//                    } else {
//                        goodsCartReq.setMemberId(memberId);
//                        goodsCartReq.setRoleId(memberRoleId);
//                    }
//                    goodsCartReq.setShopId(shopId);
//                    goodsCartReq.setSkuList(goodsCartSkuItemReqList);
//                    WrapperResp<List<GoodsCartResp>> activityGoodsCart = activityGoodsFeign.listActivityGoodsCart(goodsCartReq);
//                    if (activityGoodsCart != null && activityGoodsCart.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
//                        List<GoodsCartResp> data = activityGoodsCart.getData();
//                        if (!CollectionUtils.isEmpty(data)) {
//                            for (GoodsCartResp goodsCartResp : data) {
//                                Integer groupNo = null;
//                                Long skuId = goodsCartResp.getSkuId();
//                                Integer activityType = goodsCartResp.getActivityType();
//                                if (ActivityTypeEnum.SET_MEAL.getCode().equals(activityType)) {
//                                    activityType = PurchaseCommodityTypeEnum.SET_MEAL.getCode();
//                                    groupNo = goodsCartResp.getGroupNo();
//                                } else {
//                                    activityType = null;
//                                }
//                                Boolean isMain = goodsCartResp.getIsMain();
//                                Long parentSkuId = goodsCartResp.getParentSkuId();
//                                String key = skuId + "_" + activityType + "_" + isMain + "_" + parentSkuId + "_" + groupNo;
//                                goodsCartResponseHashMap.put(key, goodsCartResp);
//                            }
//                        }
//                    }
//                    log.info("批量查询商品活动信息，参数{}", JSONUtil.toJsonStr(goodsCartReq));
//                    log.info("批量查询商品活动信息，返回值{}", JSONUtil.toJsonStr(activityGoodsCart));
//                }
//            } catch (Exception e) {
//                log.error("批量查询商品活动信息异常", e);
//            }

            //查询价格权益
            memberFeignBatchReq.setUpperMembers(memberFeignUpperMemberReqList);
            HashMap<String, BigDecimal> memberMap = new HashMap<>();
            try {
                WrapperResp<List<MemberFeignRightDetailResp>> listWrapperResp = memberLevelRightCreditFeign.batchMemberPriceRight(memberFeignBatchReq);
                if (listWrapperResp != null && listWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
                    List<MemberFeignRightDetailResp> data = listWrapperResp.getData();
                    data.forEach(memberFeignRightDetailVO -> {
                        String member = memberFeignRightDetailVO.getUpperMemberId() + "_" + memberFeignRightDetailVO.getUpperRoleId();
                        memberMap.put(member, memberFeignRightDetailVO.getParameter());
                    });
                }
            } catch (Exception e) {
                log.error("查询价格权益异常", e);
            }

            //查询供应商商品是否满额包邮
            OrderFreeExpressFeignReq orderFreeExpressFeignReq = new OrderFreeExpressFeignReq();
            orderFreeExpressFeignReq.setItemList(memberAndRoleIdFeignReqList);
            HashMap<String, BigDecimal> orderFreeExpressMap = new HashMap<>();
            try {
                WrapperResp<List<OrderFreeExpressConfigResp>> listWrapperResp = orderFeignService.getOrderFreeExpressConfigList(orderFreeExpressFeignReq);
                if (listWrapperResp != null && listWrapperResp.getCode() == ResponseCodeEnum.SUCCESS.getCode()) {
                    List<OrderFreeExpressConfigResp> data = listWrapperResp.getData();
                    data.forEach(orderFreeExpressConfigVO -> {
                        String member = orderFreeExpressConfigVO.getMemberId() + "_" + orderFreeExpressConfigVO.getRoleId();
                        orderFreeExpressMap.put(member, orderFreeExpressConfigVO.getOrderAmount());
                    });
                }
            } catch (Exception e) {
                log.error("查询供应商商品是否满额包邮异常", e);
            }

            //获取开启仓位同步库存的sku
            if (CollUtil.isNotEmpty(skuIds)) {
                List<InventoryPatternResp> inventoryPatternRespList = freightSpaceService.getInventoryPattern(skuIds);
                skuIds = inventoryPatternRespList.stream().filter(Objects::nonNull).filter(InventoryPatternResp::getIsInventory).map(InventoryPatternResp::getId).distinct().collect(Collectors.toList());
            }
            //记录下单数量不为0的仓位库存信息
            Map<Long, List<PurchaseProductPositionResp>> map = new HashMap<>();
            //获取库存数据并赋值给每个进货单
//            List<CommoditySkuStockResp> commoditySkuRedisStockList = inventoryService.getCommoditySkuRedisStockList(Collections.singletonList(shopId), commoditySkuIdList, memberId, customerMemberId);
            //查询单件商品库存
            List<Long> commoditySingleIdList = purchaseDOList.stream().filter(o -> SPOT.getCode().equals(o.getSaleMode())).map(PurchaseDO::getCommoditySkuId).collect(Collectors.toList());
            List<FreightSpaceSingleProductDO> bySkuIdIn = freightSpaceSingleProductRepository.findBySkuIdIn(commoditySingleIdList);
            for (PurchaseDO purchaseDO : purchaseDOList) {
                PurchaseResp purchaseResp = BeanUtil.copyProperties(purchaseDO, PurchaseResp.class);
                //计算当前商品的库存数
                if (CollectionUtils.isEmpty(purchaseDO.getPurchaseProductPositionDOS())) {
                    FreightSpaceSingleProductDO freightSpaceSingleProductDO = bySkuIdIn.stream().filter(p -> p.getSkuId().equals(purchaseDO.getCommoditySkuId()) && p.getSingleCode().equals(purchaseDO.getSingleCode())).findFirst().orElseGet(FreightSpaceSingleProductDO::new);
                    boolean canUse = FreightSpaceSingleProductStatusEnum.NOT_USE.getCode().equals(freightSpaceSingleProductDO.getStatus());
                    //如果库存比0小就把状态设置成失效
                    if (canUse) {
                        purchaseResp.setIsStock(Boolean.TRUE);
                    } else {
                        purchaseResp.setIsStock(Boolean.FALSE);
                    }
                } else {
                    //如果不开启仓位同步库存,则清掉响应数据
                    if (!skuIds.contains(purchaseDO.getCommoditySkuId())) {
                        purchaseResp.setPurchaseProductPositions(new ArrayList<>());
                    }
                    //操作仓位仓库
                    if (!CollectionUtils.isEmpty(purchaseResp.getPurchaseProductPositions())) {
                        List<PurchaseProductPositionResp> productPositionResponses = purchaseResp.getPurchaseProductPositions().stream().filter(p -> p.getPositionQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                        //只保存下单数大于0的仓位仓库
                        purchaseResp.setPurchaseProductPositions(productPositionResponses);

                        //过滤下单数不为0的仓位仓库
                        List<PurchaseProductPositionResp> list = map.get(purchaseDO.getCommoditySkuId());
                        if (!CollectionUtils.isEmpty(list)) {
                            list.addAll(productPositionResponses);
                            map.put(purchaseDO.getCommoditySkuId(), list);
                        } else {
                            map.put(purchaseDO.getCommoditySkuId(), productPositionResponses);
                        }
                    }
                    purchaseResp.setStockCount(purchaseDO.getPurchaseProductPositionDOS().stream().map(PurchaseProductPositionDO::getPositionQuantity).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                }

                Long commoditySkuId = purchaseDO.getCommoditySkuId();
                EsCommodity commodity = commodityHashMap.get(commoditySkuId);
                if (commodity != null) {
                    Integer memberLevel = memberLevelMap.get(commodity.getMemberId() + Constant.UNDERLINE_STR + commodity.getMemberRoleId());
                    //判断是否代客下单
                    if (customerMemberId != null && customerMemberId > 0) {
                        //根据价格策略设置价格
                        shopService.setCommodityPrice(commodity, shopId, customerMemberId, customerMemberRoleId, memberLevel == null ? 0 : memberLevel);
                    } else {
                        //根据价格策略设置价格
                        shopService.setCommodityPrice(commodity, shopId, memberId, memberRoleId, memberLevel == null ? 0 : memberLevel);
                    }
                    //获取sku信息
                    List<Long> finalSkuIds = skuIds;
                    Map<Long, StoreInnerResp> finalStoreMap = storeMap;
                    commodity.getCommoditySkuList().forEach(commoditySku -> {
                        Long skuId = commoditySku.getId();
                        log.info("skuId is {},commoditySkuId is {},commoditySku is {}", skuId, commoditySkuId, JSONUtil.toJsonStr(commoditySku));
                        if (skuId.longValue() == commoditySkuId.longValue()) {
                            PurchaseSkuResp purchaseSkuResp = new PurchaseSkuResp();
                            BeanUtil.copyProperties(commoditySku, purchaseSkuResp);
                            PurchaseCommodityResp purchaseCommodityResp = new PurchaseCommodityResp();
                            BeanUtil.copyProperties(commodity, purchaseCommodityResp);

                            StoreInnerResp storeResp = finalStoreMap.get(commodity.getStoreId());
                            purchaseCommodityResp.setStoreName(Optional.ofNullable(storeResp).map(StoreInnerResp::getName).orElse(null));
                            purchaseCommodityResp.setStoreLogo(Optional.ofNullable(storeResp).map(StoreInnerResp::getLogo).orElse(null));


                            //获取单位
                            purchaseCommodityResp.setUnitName(commodityFeignService.getUnitName(commodity.getUnitId()));
                            purchaseSkuResp.setCommodity(purchaseCommodityResp);
                            purchaseSkuResp.setDeliveryPeriodMin(commodity.getDeliveryPeriodMin());
                            purchaseSkuResp.setDeliveryPeriodMax(commodity.getDeliveryPeriodMax());
                            purchaseSkuResp.setSingleCode(purchaseDO.getSingleCode());
                            purchaseSkuResp.setSkuCode(commodity.getCode());
                            purchaseSkuResp.setCommoditySingleId(purchaseDO.getCommoditySingleId());
                            // 计算工费总额和料金额
                            calculateLaborCostsAndMaterialAmount(purchaseSkuResp, purchaseDO, commoditySku, purchaseQueryReq.getSaleMode(), goldPrice);
                            //保存仓库库存信息
                            if (finalSkuIds.contains(purchaseDO.getCommoditySkuId())) {
                                String warehouseKey = RedisConstant.REDIS_KEY_WAREHOUSE + shopId + "_" + skuId;
                                String stringStringMap = redisUtils.stringGet(warehouseKey, RedisConstant.REDIS_PRODUCT_INDEX);
                                if (StringUtils.isNotBlank(stringStringMap)) {
                                    JSONArray array = JSONUtil.parseArray(stringStringMap);
                                    if (!CollectionUtils.isEmpty(array)) {
                                        List<InventoryByProductDTO> inventoryByProductDTOS = JSONUtil.toList(array, InventoryByProductDTO.class);
                                        List<PurchaseProductPositionResp> purchaseProductPositionRespons = map.get(skuId);
                                        if (!CollectionUtils.isEmpty(purchaseProductPositionRespons)) {
                                            inventoryByProductDTOS = inventoryByProductDTOS.stream().filter(p -> purchaseProductPositionRespons.stream().anyMatch(position -> p.getPositionId().equals(position.getPositionId()) && p.getWarehouseId().equals(position.getWarehouseId()))).collect(Collectors.toList());
                                        } else {
                                            inventoryByProductDTOS = new ArrayList<>();
                                        }
                                        purchaseSkuResp.setInventoryByProductDTOS(inventoryByProductDTOS);
                                    }
                                }
                            }
                            purchaseResp.setPurchaseSkuResp(purchaseSkuResp);
                        }
                    });
                    //判断商品是否上架
                    List<EsCommodityShop> commodityShopList = commodity.getCommodityShopList();
                    if (CollUtil.isNotEmpty(commodityShopList)) {
                        List<Long> commodityShopIdList = commodityShopList.stream().map(EsCommodityShop::getShopId).collect(Collectors.toList());
                        if (CollUtil.isNotEmpty(commodityShopIdList)) {
                            purchaseResp.setIsPublish(commodityShopIdList.contains(shopId));
                        }
                    }

                    //获取价格权益(默认为1, 即不使用会员权益)
                    BigDecimal bigDecimal = memberMap.getOrDefault(commodity.getMemberId() + "_" + commodity.getMemberRoleId(), BigDecimal.ONE);
                    if (Boolean.TRUE.equals(commodity.getIsMemberPrice())) {
                        purchaseResp.setParameter(bigDecimal);
                    } else {
                        purchaseResp.setParameter(BigDecimal.ONE);
                    }

                    //获取供应商商品是否满额包邮
                    BigDecimal orderAmount = orderFreeExpressMap.get(commodity.getMemberId() + "_" + commodity.getMemberRoleId());
                    if (orderAmount != null && orderAmount.compareTo(BigDecimal.ZERO) > 0) {
                        purchaseResp.setOrderAmount(orderAmount);
                    }

                    //获取活动相关信息
//                    Long setMealId = null;
//                    Integer purchaseCommodityType = purchaseDO.getPurchaseCommodityType();
//                    Boolean isMain = purchaseDO.getIsMain();
//                    Long parentSkuId = purchaseDO.getParentSkuId();
//                    if (PurchaseCommodityTypeEnum.SET_MEAL.getCode().equals(purchaseCommodityType)) {
//                        setMealId = purchaseDO.getSetMealId();
//                    } else {
//                        purchaseCommodityType = null;
//                    }
//                    String key = commoditySkuId + "_" + purchaseCommodityType + "_" + isMain + "_" + parentSkuId + "_" + setMealId;
//                    log.info("批量查询商品活动信息，key{}", key);
//                    purchaseResp.setGoodsCartResp(goodsCartResponseHashMap.get(key));

                    if (CommoditySaleModeEnum.ORDER.getCode().equals(purchaseQueryReq.getSaleMode())) {
                        if (ObjectUtil.isNotEmpty(depositResp)) {
                            purchaseResp.setDepositType(depositResp.getDepositType());
                            // 固定金额
                            purchaseResp.setDeposit(depositResp.getDepositAmount());
                        }
                    }

                    purchaseRespList.add(purchaseResp);
                }
            }

            return purchaseRespList;
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 添加/修改进货单
     *
     * @param purchaseDO                 进货单实体类
     * @param purchaseProductPositionReq 进货单商品仓位同步库存实体类
     * @return 操作结果
     */
    @Override
    public Integer saveOrUpdatePurchase(UserLoginCacheDTO sysUser, PurchaseDO purchaseDO, PurchaseProductPositionReq purchaseProductPositionReq) {
        // 构建锁key
        String lockKey = "purchase:" + sysUser.getMemberId() + ":" + purchaseDO.getCommoditySkuId() + ":" +
                purchaseDO.getSingleCode() + ":" + purchaseDO.getShopId() + ":" + sysUser.getMemberId() + ":" + sysUser.getMemberRoleId() + ":" + sysUser.getUserId();

        // 获取分布式锁
        RLock lock = redissonUtils.getLock(lockKey);
        try {
            // 尝试加锁，等待时间1秒，锁定时间30秒
            if (!redissonUtils.tryAndLock(lock, 1, 30, java.util.concurrent.TimeUnit.SECONDS)) {
                throw new BusinessException("当前商品正在被处理，请稍后再试");
            }

            return doSaveOrUpdatePurchase(sysUser, purchaseDO, purchaseProductPositionReq);
        } finally {
            redissonUtils.unlock(lock);
        }
    }

    private Integer doSaveOrUpdatePurchase(UserLoginCacheDTO sysUser, PurchaseDO purchaseDO, PurchaseProductPositionReq purchaseProductPositionReq) {

        Long memberId = sysUser.getMemberId();
        Long memberRoleId = sysUser.getMemberRoleId();
        Long id = purchaseDO.getId();
        Long shopId = purchaseDO.getShopId();
        Long customerMemberId = purchaseDO.getCustomerMemberId();
        Long customerMemberRoleId = purchaseDO.getCustomerMemberRoleId();
        Long orderId = purchaseDO.getOrderId();
        Long branchId = purchaseDO.getBranchId();

        if (id != null && id > 0) {
            // 修改逻辑
            PurchaseDO oldPurchaseDO = purchaseRepository.findById(id).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PRODUCT_PURCHASE_NOT_EXIST));
            Long oldShopId = oldPurchaseDO.getShopId();
            Long commoditySkuId = oldPurchaseDO.getCommoditySkuId();
            if (shopId.longValue() == oldShopId.longValue()) {
                //标志是否开启物料同步仓库
                boolean flag = Objects.nonNull(purchaseProductPositionReq) && purchaseProductPositionReq.getPositionId() != null && purchaseProductPositionReq.getWarehouseId() != null;
                log.info("是否开启物料同步仓库: {}", flag);
                if (flag) {
                    //获取之前的下单仓位信息
                    BigDecimal count = purchaseProductPositionReq.getPositionQuantity();
                    BigDecimal stock = shopService.getCommodityPositionStockBySkuId(shopId, commoditySkuId, purchaseProductPositionReq.getPositionId(), purchaseProductPositionReq.getWarehouseId());

                    if (count.compareTo(stock) > 0) {
                        throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                    }

                    //修改下单仓位信息
                    oldPurchaseDO.getPurchaseProductPositionDOS().stream().filter(
                            p -> purchaseProductPositionReq.getPositionId().equals(p.getPositionId()) && purchaseProductPositionReq.getWarehouseId().equals(p.getWarehouseId())
                    ).forEach(p -> p.setPositionQuantity(count));
                    //须计算总数量
                    BigDecimal totalCount = oldPurchaseDO.getPurchaseProductPositionDOS().stream().map(PurchaseProductPositionDO::getPositionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add).add(purchaseProductPositionReq.getPositionQuantity());
                    oldPurchaseDO.setCount(totalCount);
                } else {
                    BigDecimal count = purchaseDO.getCount();
                    //  现货场景才需要判断库存
                    if (SPOT.getCode().equals(purchaseDO.getSaleMode())) {
                        FreightSpaceSingleProductDO bySingleCode = freightSpaceSingleProductRepository.findBySingleCode(purchaseDO.getSingleCode());
                        if (bySingleCode == null) {
                            throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                        }
                        log.info("查询单件库存返回的结果为 {}", bySingleCode);
                        boolean canUse = FreightSpaceSingleProductStatusEnum.NOT_USE.getCode().equals(bySingleCode.getStatus());
                        //判断是否代客下单
//                        if (customerMemberId != null && customerMemberId > 0) {
//                            stock = shopService.getCommodityStockBySkuId(shopId, customerMemberId, commoditySkuId);
//                        } else {
//                            stock = shopService.getCommodityStockBySkuId(shopId, memberId, commoditySkuId);
//                        }

                        if (!canUse) {
                            throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                        }
                    }

                    oldPurchaseDO.setCount(count);
                    purchaseRepository.saveAndFlush(oldPurchaseDO);
                    //判断是否代客下单
                    if (customerMemberId != null && customerMemberId > 0) {
                        if (orderId != null && orderId > 0) {
                            return purchaseRepository.countByMemberIdAndMemberRoleIdAndShopIdAndCustomerMemberIdAndCustomerMemberRoleIdAndOrderId(memberId, memberRoleId, shopId, customerMemberId, customerMemberRoleId, orderId);
                        } else {
                            return purchaseRepository.countByMemberIdAndMemberRoleIdAndShopIdAndCustomerMemberIdAndCustomerMemberRoleId(memberId, memberRoleId, shopId, customerMemberId, customerMemberRoleId);
                        }
                    } else {
                        return purchaseRepository.countByMemberIdAndMemberRoleIdAndShopIdAndBranchId(memberId, memberRoleId, shopId, branchId);
                    }
                }
            }
            PurchaseQueryReq purchaseQueryReq = new PurchaseQueryReq();
            purchaseQueryReq.setMemberId(sysUser.getMemberId());
            purchaseQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
            purchaseQueryReq.setShopId(shopId);
            purchaseQueryReq.setCustomerMemberId(customerMemberId);
            purchaseQueryReq.setCustomerMemberRoleId(customerMemberRoleId);
            purchaseQueryReq.setOrderId(orderId);
            purchaseQueryReq.setBranchId(branchId);
            return this.getPurchaseCount(purchaseQueryReq);
        }

        // 新增逻辑

        //检查商品规格
        Long commoditySkuId = purchaseDO.getCommoditySkuId();
        if (commoditySkuId == null || commoditySkuId <= 0) {
            throw new BusinessException(ResponseCodeEnum.PRODUCT_PURCHASE_UNITPRICE_NOT_EXIST);
        }

        Optional.ofNullable(esCommodityRepository.findByCommoditySkuListId(commoditySkuId)).orElseThrow(
                () -> new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_NOT_EXIST)
        );

        //查询库存
        BigDecimal stock = BigDecimal.ONE;
        //  现货场景才需要判断库存
        if (SPOT.getCode().equals(purchaseDO.getSaleMode())) {
            FreightSpaceSingleProductDO bySingleCode = freightSpaceSingleProductRepository.findBySingleCode(purchaseDO.getSingleCode());
            if (bySingleCode == null) {
                throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
            }
            log.info("查询单件库存返回的结果为 {}", bySingleCode);
//            stock = bySingleCode.getInventory();
            boolean canUse = FreightSpaceSingleProductStatusEnum.NOT_USE.getCode().equals(bySingleCode.getStatus());
            //判断是否代客下单
//                        if (customerMemberId != null && customerMemberId > 0) {
//                            stock = shopService.getCommodityStockBySkuId(shopId, customerMemberId, commoditySkuId);
//                        } else {
//                            stock = shopService.getCommodityStockBySkuId(shopId, memberId, commoditySkuId);
//                        }

            if (!canUse) {
                throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
            }
        }
        //判断是否代客下单
//        if (customerMemberId != null && customerMemberId > 0) {
//            stock = shopService.getCommodityStockBySkuId(shopId, customerMemberId, commoditySkuId);
//        } else {
//            stock = shopService.getCommodityStockBySkuId(shopId, memberId, commoditySkuId);
//        }

        Integer purchaseCommodityType = purchaseDO.getPurchaseCommodityType();
        PurchaseDO oldPurchaseDO;
        //判断是否代客下单
        if (customerMemberId != null && customerMemberId > 0) {
            if (orderId != null && orderId > 0) {
                oldPurchaseDO = purchaseRepository.findFirstByMemberIdAndMemberRoleIdAndCommoditySkuIdAndShopIdAndPurchaseCommodityTypeAndCustomerMemberIdAndCustomerMemberRoleIdAndOrderId(memberId, memberRoleId, commoditySkuId, shopId, purchaseCommodityType, customerMemberId, customerMemberRoleId, orderId);
            } else {
                oldPurchaseDO = purchaseRepository.findFirstByMemberIdAndMemberRoleIdAndCommoditySkuIdAndShopIdAndPurchaseCommodityTypeAndCustomerMemberIdAndCustomerMemberRoleId(memberId, memberRoleId, commoditySkuId, shopId, purchaseCommodityType, customerMemberId, customerMemberRoleId);
            }
        } else {
            oldPurchaseDO = purchaseRepository.findFirstByMemberIdAndMemberRoleIdAndCommoditySkuIdAndShopIdAndPurchaseCommodityType(memberId, memberRoleId, commoditySkuId, shopId, purchaseCommodityType);
        }

        if (oldPurchaseDO != null) {
            BigDecimal count = oldPurchaseDO.getCount().add(purchaseDO.getCount());
            if (count.compareTo(stock) > 0) {
                if (SPOT.getCode().equals(purchaseDO.getSaleMode())) {
                    throw new BusinessException(ResponseCodeEnum.ALREADY_IN_CART);
                }
                throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
            }
            oldPurchaseDO.setCount(count);
        } else {
            Integer existPurchaseCount;
            //判断是否代客下单
            if (customerMemberId != null && customerMemberId > 0) {
                if (orderId != null && orderId > 0) {
                    existPurchaseCount = purchaseRepository.countByMemberIdAndMemberRoleIdAndShopIdAndCustomerMemberIdAndCustomerMemberRoleIdAndOrderId(memberId, memberRoleId, shopId, customerMemberId, customerMemberRoleId, orderId);
                } else {
                    existPurchaseCount = purchaseRepository.countByMemberIdAndMemberRoleIdAndShopIdAndCustomerMemberIdAndCustomerMemberRoleId(memberId, memberRoleId, shopId, customerMemberId, customerMemberRoleId);
                }
            } else {
                existPurchaseCount = purchaseRepository.countByMemberIdAndMemberRoleIdAndShopId(memberId, memberRoleId, shopId);
            }

            if (existPurchaseCount >= ProductConstant.PURCHASE_MAX_COUNT) {
                throw new BusinessException(ResponseCodeEnum.PRODUCT_PURCHASE_COUNT_OUT);
            }

            BigDecimal count = purchaseDO.getCount();
            if (count.compareTo(stock) > 0) {
                throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
            }

            oldPurchaseDO = new PurchaseDO();
            //数据库持久化对象
            oldPurchaseDO.setShopId(shopId);
            oldPurchaseDO.setCount(purchaseDO.getCount());
            if (SPOT.getCode().equals(purchaseDO.getSaleMode())) {
                oldPurchaseDO.setCount(count);
            }
            oldPurchaseDO.setCommoditySkuId(commoditySkuId);
            oldPurchaseDO.setMemberId(memberId);
            oldPurchaseDO.setBranchId(branchId);
            oldPurchaseDO.setSingleCode(purchaseDO.getSingleCode());
            oldPurchaseDO.setSaleMode(purchaseDO.getSaleMode());
            oldPurchaseDO.setCommoditySingleId(purchaseDO.getCommoditySingleId());
            oldPurchaseDO.setMemberName(sysUser.getMemberName());
            oldPurchaseDO.setMemberRoleId(memberRoleId);
            oldPurchaseDO.setMemberRoleName(sysUser.getMemberRoleName());
            oldPurchaseDO.setUserId(sysUser.getUserId());
            oldPurchaseDO.setUserName(sysUser.getUserName());
            oldPurchaseDO.setCustomerMemberId(customerMemberId);
            oldPurchaseDO.setCustomerMemberRoleId(customerMemberRoleId);
            oldPurchaseDO.setPurchaseCommodityType(purchaseDO.getPurchaseCommodityType());
            oldPurchaseDO.setOrderId(orderId);
        }

        purchaseRepository.saveAndFlush(oldPurchaseDO);

        PurchaseQueryReq purchaseQueryReq = new PurchaseQueryReq();
        purchaseQueryReq.setMemberId(sysUser.getMemberId());
        purchaseQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        purchaseQueryReq.setUserId(sysUser.getUserId());
        purchaseQueryReq.setShopId(shopId);
        purchaseQueryReq.setCustomerMemberId(customerMemberId);
        purchaseQueryReq.setCustomerMemberRoleId(customerMemberRoleId);
        purchaseQueryReq.setOrderId(orderId);
        return this.getPurchaseCount(purchaseQueryReq);
    }

    /**
     * 删除进货单
     *
     * @param commonIdListReq 进货单id集合
     * @return 操作结果
     */
    @Override
    public String deletePurchase(CommonIdListReq commonIdListReq) {
        List<PurchaseDO> deletePurchaseListDO = new ArrayList<>();
        //验证数据库中是否存在该数据
        List<Long> idList = commonIdListReq.getIdList();
        List<PurchaseDO> purchaseDOList = purchaseRepository.findByIdIn(idList);
        if (purchaseDOList != null && purchaseDOList.size() == idList.size()) {
            purchaseDOList.forEach(purchase -> {
                //删除主商品，需要把子商品一起删除
                Integer purchaseCommodityType = purchase.getPurchaseCommodityType();
                if (PurchaseCommodityTypeEnum.SET_MEAL.getCode().equals(purchaseCommodityType) || PurchaseCommodityTypeEnum.EXCHANGE.getCode().equals(purchaseCommodityType)) {
                    Boolean isMain = purchase.getIsMain();
                    if (isMain != null && isMain) {
                        Long userId = purchase.getUserId();
                        Long setMealId = purchase.getSetMealId();
                        Long shopId = purchase.getShopId();
                        List<PurchaseDO> purchaseDOSetMealList = purchaseRepository.findByUserIdAndSetMealIdAndShopId(userId, setMealId, shopId);
                        purchaseDOSetMealList.forEach(purchaseSetMeal -> {
                            if (!deletePurchaseListDO.contains(purchaseSetMeal)) {
                                deletePurchaseListDO.add(purchaseSetMeal);
                            }
                        });
                    } else {
                        deletePurchaseListDO.add(purchase);
                    }
                } else {
                    deletePurchaseListDO.add(purchase);
                }
            });
            if (!deletePurchaseListDO.isEmpty()) {
                purchaseRepository.deleteAll(deletePurchaseListDO);
                return ResponseCodeEnum.SUCCESS.getMessage();
            }
        }
        throw new BusinessException(ResponseCodeEnum.PRODUCT_PURCHASE_NOT_EXIST);
    }

    /**
     * 批量新增进货单
     *
     * @return 是否成功
     */
    @Override
    public Integer savePurchaseBatch(UserLoginCacheDTO sysUser, Long shopId, List<PurchaseReq> purchaseReqList) {
        Long userId = sysUser.getUserId();
        long startTime = System.currentTimeMillis();
        log.info("批量加购开始，userId为{}", userId);
        // 构建锁key
        String lockKey = "purchase:batch:" + shopId + ":" + sysUser.getMemberId() + ":" + sysUser.getMemberRoleId() + ":" + userId;

        // 获取分布式锁
        RLock lock = redissonUtils.getLock(lockKey);
        try {
            // 尝试加锁，等待时间2秒，锁定时间60秒
            if (!redissonUtils.tryAndLock(lock, 2, 60, java.util.concurrent.TimeUnit.SECONDS)) {
                throw new BusinessException("批量加购正在处理中，请稍后再试");
            }

            return doSavePurchaseBatch(sysUser, shopId, purchaseReqList);
        } finally {
            redissonUtils.unlock(lock);
            log.info("批量加购结束，userId为{},耗时{}", userId, System.currentTimeMillis() - startTime);
        }
    }

    private Integer doSavePurchaseBatch(UserLoginCacheDTO sysUser, Long shopId, List<PurchaseReq> purchaseReqList) {
        long methodStartTime = System.currentTimeMillis();
        log.info("批量加购开始，用户：{}，商品数量：{}", sysUser.getMemberId(), purchaseReqList.size());

        Long memberId = sysUser.getMemberId();
        Long memberRoleId = sysUser.getMemberRoleId();
        //保存商品仓位库存信息
        Set<PurchaseProductPositionDO> productPositions = new HashSet<>();

        long streamProcessStartTime = System.currentTimeMillis();
        List<PurchaseDO> purchaseDOList = purchaseReqList.stream().map(purchaseRequest -> {
            //检查商品规格
            Long commoditySkuId = purchaseRequest.getCommoditySkuId();
            Long setMealId = purchaseRequest.getSetMealId();
            Integer purchaseCommodityType = purchaseRequest.getPurchaseCommodityType();
            //仓位库存信息
            PurchaseProductPositionReq purchaseProductPosition = purchaseRequest.getPurchaseProductPositionRequest();
            //标志是否开启物料同步仓库
            boolean flag = Objects.nonNull(purchaseProductPosition) && purchaseProductPosition.getPositionId() != null && purchaseProductPosition.getWarehouseId() != null;
            if (commoditySkuId != null && commoditySkuId > 0) {
                // ES商品查询耗时统计
                long esQueryStartTime = System.currentTimeMillis();
                EsCommodity commodity = esCommodityRepository.findByCommoditySkuListId(commoditySkuId);
                long esQueryEndTime = System.currentTimeMillis();
                log.info("ES商品查询耗时：{}ms，商品SKU：{}", (esQueryEndTime - esQueryStartTime), commoditySkuId);

                if (commodity != null) {
                    //判断商品是否上架
                    List<EsCommodityShop> commodityShopList = commodity.getCommodityShopList();
                    if (commodityShopList != null && !commodityShopList.isEmpty()) {
                        List<Long> commodityShopIdList = commodityShopList.stream().map(EsCommodityShop::getShopId).collect(Collectors.toList());
                        if (!commodityShopIdList.isEmpty() && !commodityShopIdList.contains(shopId)) {
                            throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                        }
                    } else {
                        throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                    }

                    PurchaseProductPositionDO ordPurchaseProductPositionDO;
                    //区分是否套餐
                    PurchaseDO oldPurchaseDO;
                    Long branchId = purchaseRequest.getBranchId();

                    // 购物车重复商品查询耗时统计
                    long purchaseQueryStartTime = System.currentTimeMillis();
                    if (setMealId != null && setMealId > 0) {
                        if (SPOT.getCode().equals(purchaseRequest.getSaleMode())) {
                            oldPurchaseDO = purchaseRepository.findFirstByMemberIdAndMemberRoleIdAndCommoditySkuIdAndShopIdAndPurchaseCommodityTypeAndSetMealIdAndParentSkuIdAndSaleModeAndSingleCodeAndBranchId(memberId, memberRoleId,
                                    commoditySkuId, shopId, purchaseCommodityType,
                                    setMealId, purchaseRequest.getParentSkuId(), purchaseRequest.getSaleMode(), purchaseRequest.getSingleCode(), branchId);
                        } else {
                            // 如果存在多次添加同一个套餐，往这个套餐叠加数量
                            oldPurchaseDO = purchaseRepository.findFirstByMemberIdAndMemberRoleIdAndCommoditySkuIdAndShopIdAndPurchaseCommodityTypeAndSetMealIdAndParentSkuIdAndSaleModeAndBranchId(memberId, memberRoleId, commoditySkuId,
                                    shopId, purchaseCommodityType, setMealId, purchaseRequest.getParentSkuId(), purchaseRequest.getSaleMode(), branchId);
                        }
                    } else {
                        oldPurchaseDO = purchaseRepository.findFirstByMemberIdAndMemberRoleIdAndCommoditySkuIdAndShopIdAndPurchaseCommodityTypeAndBranchId(memberId, memberRoleId, commoditySkuId, shopId, purchaseCommodityType, branchId);
                    }
                    long purchaseQueryEndTime = System.currentTimeMillis();
                    log.info("购物车重复商品查询耗时：{}ms，商品SKU：{}，套餐ID：{}",
                            (purchaseQueryEndTime - purchaseQueryStartTime), commoditySkuId, setMealId);
                    BigDecimal addCount = purchaseRequest.getCount();
                    if (oldPurchaseDO != null) {
                        if (flag) {
                            //获取之前的下单仓位信息
                            ordPurchaseProductPositionDO = oldPurchaseDO.getPurchaseProductPositionDOS().stream().filter(p -> purchaseProductPosition.getPositionId().equals(p.getPositionId()) && purchaseProductPosition.getWarehouseId().equals(p.getWarehouseId())).findFirst().orElse(null);
                            if (Objects.nonNull(ordPurchaseProductPositionDO)) {
                                BigDecimal count = purchaseProductPosition.getPositionQuantity().add(ordPurchaseProductPositionDO.getPositionQuantity());

                                // 仓位库存查询耗时统计
                                long positionStockQueryStartTime = System.currentTimeMillis();
                                BigDecimal stock = shopService.getCommodityPositionStockBySkuId(shopId, commoditySkuId, purchaseProductPosition.getPositionId(), purchaseProductPosition.getWarehouseId());
                                long positionStockQueryEndTime = System.currentTimeMillis();
                                log.info("仓位库存查询耗时：{}ms，商品SKU：{}，仓位ID：{}，仓库ID：{}",
                                        (positionStockQueryEndTime - positionStockQueryStartTime), commoditySkuId,
                                        purchaseProductPosition.getPositionId(), purchaseProductPosition.getWarehouseId());
                                if (count.compareTo(stock) <= 0) {
                                    //修改下单仓位信息
                                    oldPurchaseDO.getPurchaseProductPositionDOS().stream().filter(p -> purchaseProductPosition.getPositionId().equals(p.getPositionId()) && purchaseProductPosition.getWarehouseId().equals(p.getWarehouseId())).forEach(p -> p.setPositionQuantity(count));
                                    //须计算总数量
                                    BigDecimal totalCount = oldPurchaseDO.getPurchaseProductPositionDOS().stream().map(PurchaseProductPositionDO::getPositionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add).add(purchaseProductPosition.getPositionQuantity());
                                    oldPurchaseDO.setCount(totalCount);
                                } else {
                                    throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                                }
                            } else {
                                BigDecimal count = purchaseProductPosition.getPositionQuantity();

                                // 仓位库存查询耗时统计
                                long positionStockQueryStartTime = System.currentTimeMillis();
                                BigDecimal stock = shopService.getCommodityPositionStockBySkuId(shopId, commoditySkuId, purchaseProductPosition.getPositionId(), purchaseProductPosition.getWarehouseId());
                                long positionStockQueryEndTime = System.currentTimeMillis();
                                log.info("仓位库存查询耗时：{}ms，商品SKU：{}，仓位ID：{}，仓库ID：{}",
                                        (positionStockQueryEndTime - positionStockQueryStartTime), commoditySkuId,
                                        purchaseProductPosition.getPositionId(), purchaseProductPosition.getWarehouseId());
                                if (count.compareTo(stock) <= 0) {
                                    Set<PurchaseProductPositionDO> purchaseProductPositionDOS = oldPurchaseDO.getPurchaseProductPositionDOS();
                                    //须计算总数量
                                    BigDecimal totalCount = purchaseProductPositionDOS.stream().map(PurchaseProductPositionDO::getPositionQuantity).reduce(BigDecimal.ZERO, BigDecimal::add).add(purchaseProductPosition.getPositionQuantity());
                                    oldPurchaseDO.setCount(totalCount);
                                    //保存商品仓位库存信息 (以下逻辑代码只为补偿旧数据)  ---start-----
                                    ordPurchaseProductPositionDO = new PurchaseProductPositionDO();
                                    ordPurchaseProductPositionDO.setPositionId(purchaseProductPosition.getPositionId());
                                    ordPurchaseProductPositionDO.setWarehouseId(purchaseProductPosition.getWarehouseId());
                                    ordPurchaseProductPositionDO.setWarehouseName(purchaseProductPosition.getWarehouseName());
                                    ordPurchaseProductPositionDO.setWarehouseAddress(purchaseProductPosition.getWarehouseAddress());
                                    ordPurchaseProductPositionDO.setPositionQuantity(purchaseProductPosition.getPositionQuantity());
                                    ordPurchaseProductPositionDO.setPurchaseDO(oldPurchaseDO);
                                    purchaseProductPositionDOS.add(ordPurchaseProductPositionDO);
                                    oldPurchaseDO.setPurchaseProductPositionDOS(purchaseProductPositionDOS);
                                    productPositions.addAll(purchaseProductPositionDOS);
                                    //-----------------------------------------------end-------
                                } else {
                                    throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                                }
                            }
                        } else {
                            BigDecimal count = oldPurchaseDO.getCount().add(addCount);

                            //  现货场景才需要判断库存
                            if (SPOT.getCode().equals(oldPurchaseDO.getSaleMode())) {
//                                FreightSpaceSingleProductDO bySingleCode = freightSpaceSingleProductRepository.findBySingleCode(oldPurchaseDO.getSingleCode());
//                                if (bySingleCode == null) {
//                                    throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
//                                }
//                                log.info("查询单件库存返回的结果为 {}", bySingleCode);
////                                BigDecimal stock = bySingleCode.getInventory();
//                                boolean canUse = FreightSpaceSingleProductStatusEnum.NOT_USE.getCode().equals(bySingleCode.getStatus());
//                                //判断是否代客下单
////                        if (customerMemberId != null && customerMemberId > 0) {
////                            stock = shopService.getCommodityStockBySkuId(shopId, customerMemberId, commoditySkuId);
////                        } else {
////                            stock = shopService.getCommodityStockBySkuId(shopId, memberId, commoditySkuId);
////                        }
//
//                                if (!canUse || count.compareTo(BigDecimal.ONE) > 0) {
//                                    throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
//                                }
                                throw new BusinessException(ResponseCodeEnum.ALREADY_IN_CART);
                            }
                            oldPurchaseDO.setCount(count);
//                            BigDecimal stock = shopService.getCommodityStockBySkuId(shopId, memberId, commoditySkuId);
//                            if(count.compareTo(stock) <= 0) {
//                                oldPurchaseDO.setCount(count);
//                            }else{
//                                throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
//                            }
                        }
                    } else {
                        // 购物车数量统计查询耗时统计
                        long countQueryStartTime = System.currentTimeMillis();
                        Integer existPurchaseCount = purchaseRepository.countByMemberIdAndMemberRoleIdAndShopId(memberId, memberRoleId, shopId);
                        long countQueryEndTime = System.currentTimeMillis();
                        log.info("购物车数量统计查询耗时：{}ms，用户：{}，当前数量：{}",
                                (countQueryEndTime - countQueryStartTime), memberId, existPurchaseCount);

                        if (existPurchaseCount >= ProductConstant.PURCHASE_MAX_COUNT) {
                            throw new BusinessException(ResponseCodeEnum.PRODUCT_PURCHASE_COUNT_OUT);
                        }
                        //  现货场景才需要判断库存
                        if (SPOT.getCode().equals(purchaseRequest.getSaleMode())) {
                            // 单件库存查询耗时统计
                            long singleStockQueryStartTime = System.currentTimeMillis();
                            FreightSpaceSingleProductDO bySingleCode = freightSpaceSingleProductRepository.findBySingleCode(purchaseRequest.getSingleCode());
                            long singleStockQueryEndTime = System.currentTimeMillis();
                            log.info("单件库存查询耗时：{}ms，单件编码：{}",
                                    (singleStockQueryEndTime - singleStockQueryStartTime), purchaseRequest.getSingleCode());

                            if (bySingleCode == null) {
                                throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                            }
                            boolean canUse = FreightSpaceSingleProductStatusEnum.NOT_USE.getCode().equals(bySingleCode.getStatus());
                            log.info("查询单件库存返回的结果为 {}", JSONUtil.toJsonStr(bySingleCode));
//                            stock = bySingleCode.getInventory();
                            //判断是否代客下单
//                        if (customerMemberId != null && customerMemberId > 0) {
//                            stock = shopService.getCommodityStockBySkuId(shopId, customerMemberId, commoditySkuId);
//                        } else {
//                            stock = shopService.getCommodityStockBySkuId(shopId, memberId, commoditySkuId);
//                        }

                            if (!canUse || addCount.compareTo(BigDecimal.ONE) > 0) {
                                throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                            }
                        }
//                        if (flag){
//                             count = purchaseProductPosition.getPositionQuantity();
//                             stock = shopService.getCommodityPositionStockBySkuId(shopId, commoditySkuId, purchaseProductPosition.getPositionId(), purchaseProductPosition.getWarehouseId());
//                        }else {
//                            count = purchaseRequest.getCount();
//                            stock = shopService.getCommodityStockBySkuId(shopId, memberId, commoditySkuId);
//                        }

                        oldPurchaseDO = new PurchaseDO();
                        //数据库持久化对象
                        oldPurchaseDO.setShopId(shopId);
                        oldPurchaseDO.setCount(addCount);
                        oldPurchaseDO.setCommoditySkuId(commoditySkuId);
                        oldPurchaseDO.setMemberId(memberId);
                        oldPurchaseDO.setMemberName(sysUser.getMemberName());
                        oldPurchaseDO.setMemberRoleId(memberRoleId);
                        oldPurchaseDO.setMemberRoleName(sysUser.getMemberRoleName());
                        oldPurchaseDO.setUserId(sysUser.getUserId());
                        oldPurchaseDO.setUserName(sysUser.getUserName());
                        oldPurchaseDO.setPurchaseCommodityType(purchaseRequest.getPurchaseCommodityType());
                        oldPurchaseDO.setSetMealId(purchaseRequest.getSetMealId());
                        oldPurchaseDO.setSetMealName(purchaseRequest.getSetMealName());
                        oldPurchaseDO.setBranchId(branchId);
                        oldPurchaseDO.setSaleMode(purchaseRequest.getSaleMode());
                        oldPurchaseDO.setSingleCode(purchaseRequest.getSingleCode());
                        oldPurchaseDO.setCommoditySingleId(purchaseRequest.getCommoditySingleId());
                        if (purchaseRequest.getIsMain() != null) {
                            oldPurchaseDO.setIsMain(purchaseRequest.getIsMain());
                        }
                        oldPurchaseDO.setParentSkuId(purchaseRequest.getParentSkuId());
                        if (flag) {
                            //保存商品仓位库存信息
                            ordPurchaseProductPositionDO = new PurchaseProductPositionDO();
                            ordPurchaseProductPositionDO.setPositionId(purchaseProductPosition.getPositionId());
                            ordPurchaseProductPositionDO.setWarehouseId(purchaseProductPosition.getWarehouseId());
                            ordPurchaseProductPositionDO.setWarehouseName(purchaseProductPosition.getWarehouseName());
                            ordPurchaseProductPositionDO.setWarehouseAddress(purchaseProductPosition.getWarehouseAddress());
                            ordPurchaseProductPositionDO.setPositionQuantity(purchaseProductPosition.getPositionQuantity());
                            ordPurchaseProductPositionDO.setPurchaseDO(oldPurchaseDO);
                            productPositions.add(ordPurchaseProductPositionDO);
                            oldPurchaseDO.setPurchaseProductPositionDOS(productPositions);
                        }

                    }
                    return oldPurchaseDO;
                } else {
                    throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_NOT_EXIST);
                }
            } else {
                throw new BusinessException(ResponseCodeEnum.PRODUCT_PURCHASE_UNITPRICE_NOT_EXIST);
            }
        }).collect(Collectors.toList());

        if (!purchaseDOList.isEmpty()) {
            // 批量保存操作耗时统计
            long batchSaveStartTime = System.currentTimeMillis();
            purchaseRepository.saveAll(purchaseDOList);
            purchaseRepository.flush();
            long purchaseSaveEndTime = System.currentTimeMillis();
            log.info("购物车批量保存耗时：{}ms，保存数量：{}",
                    (purchaseSaveEndTime - batchSaveStartTime), purchaseDOList.size());

            if (!productPositions.isEmpty()) {
                long positionSaveStartTime = System.currentTimeMillis();
                purchaseProductPositionRepository.saveAll(productPositions);
                long positionSaveEndTime = System.currentTimeMillis();
                log.info("仓位信息批量保存耗时：{}ms，保存数量：{}",
                        (positionSaveEndTime - positionSaveStartTime), productPositions.size());
            }
        }

        // 最终购物车数量查询耗时统计
        long finalCountQueryStartTime = System.currentTimeMillis();
        PurchaseQueryReq purchaseQueryReq = new PurchaseQueryReq();
        purchaseQueryReq.setMemberId(sysUser.getMemberId());
        purchaseQueryReq.setMemberRoleId(sysUser.getMemberRoleId());
        purchaseQueryReq.setShopId(shopId);
        purchaseQueryReq.setBranchId(purchaseReqList.get(0).getBranchId());
        Integer result = this.getPurchaseCount(purchaseQueryReq);
        long finalCountQueryEndTime = System.currentTimeMillis();
        log.info("最终购物车数量查询耗时：{}ms，结果：{}",
                (finalCountQueryEndTime - finalCountQueryStartTime), result);

        // 方法总耗时统计
        long methodEndTime = System.currentTimeMillis();
        long totalTime = methodEndTime - methodStartTime;
        log.info("批量加购完成，总耗时：{}ms，用户：{}，商品数量：{}，最终购物车数量：{}",
                totalTime, sysUser.getMemberId(), purchaseReqList.size(), result);

        // 如果总耗时超过阈值，记录警告日志
        if (totalTime > 2000) { // 3秒阈值
            log.info("批量加购耗时过长！总耗时：{}ms，用户：{}，商品数量：{}，建议优化",
                    totalTime, sysUser.getMemberId(), purchaseReqList.size());
        }

        return result;
    }

    /**
     * 批量新增进货单
     *
     * @return 是否成功
     */
    @Override
    public Boolean savePurchaseCustomerBatch(UserLoginCacheDTO sysUser, Long shopId, List<PurchaseCustomerReq> purchaseRequestList) {
        Long memberId = sysUser.getMemberId();
        Long memberRoleId = sysUser.getMemberRoleId();
        List<PurchaseDO> purchaseDOList = purchaseRequestList.stream().map(purchaseRequest -> {
            //检查商品规格
            Long commoditySkuId = purchaseRequest.getCommoditySkuId();
            Long setMealId = purchaseRequest.getSetMealId();
            Integer purchaseCommodityType = purchaseRequest.getPurchaseCommodityType();
            Long customerMemberId = purchaseRequest.getCustomerMemberId();
            Long customerMemberRoleId = purchaseRequest.getCustomerMemberRoleId();
            Long orderId = purchaseRequest.getOrderId();
            if (commoditySkuId != null && commoditySkuId > 0) {
                EsCommodity commodity = esCommodityRepository.findByCommoditySkuListId(commoditySkuId);
                if (commodity != null) {
                    //查询库存
                    BigDecimal stock;
                    //判断是否代客下单
                    if (customerMemberId != null && customerMemberId > 0) {
                        stock = shopService.getCommodityStockBySkuId(shopId, customerMemberId, commoditySkuId);
                    } else {
                        stock = shopService.getCommodityStockBySkuId(shopId, memberId, commoditySkuId);
                    }

                    PurchaseQueryReq purchaseQueryReq = new PurchaseQueryReq();
                    purchaseQueryReq.setMemberId(memberId);
                    purchaseQueryReq.setMemberRoleId(memberRoleId);
                    purchaseQueryReq.setCommoditySkuId(commoditySkuId);
                    purchaseQueryReq.setShopId(shopId);
                    purchaseQueryReq.setPurchaseCommodityType(purchaseCommodityType);
                    purchaseQueryReq.setCustomerMemberId(customerMemberId);
                    purchaseQueryReq.setOrderId(orderId);
                    purchaseQueryReq.setSetMealId(setMealId);

                    Pageable page = PageRequest.of(0, ProductConstant.PURCHASE_MAX_COUNT);
                    Page<PurchaseDO> purchasePage = purchaseRepository.findAll(getSpecification(purchaseQueryReq), page);
                    List<PurchaseDO> content = purchasePage.getContent();

                    PurchaseDO oldPurchaseDO = null;
                    if (!CollectionUtils.isEmpty(content)) {
                        oldPurchaseDO = content.get(0);
                    }
                    if (oldPurchaseDO != null) {
                        BigDecimal count = oldPurchaseDO.getCount().add(purchaseRequest.getCount());
                        if (count.compareTo(stock) <= 0) {
                            oldPurchaseDO.setCount(count);
                        } else {
                            throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                        }
                    } else {
                        Integer existPurchaseCount;
                        //判断是否代客下单
                        if (customerMemberId != null && customerMemberId > 0) {
                            if (orderId != null && orderId > 0) {
                                existPurchaseCount = purchaseRepository.countByMemberIdAndMemberRoleIdAndShopIdAndCustomerMemberIdAndCustomerMemberRoleIdAndOrderId(memberId, memberRoleId, shopId, customerMemberId, customerMemberRoleId, orderId);
                            } else {
                                existPurchaseCount = purchaseRepository.countByMemberIdAndMemberRoleIdAndShopIdAndCustomerMemberIdAndCustomerMemberRoleId(memberId, memberRoleId, shopId, customerMemberId, customerMemberRoleId);
                            }
                        } else {
                            existPurchaseCount = purchaseRepository.countByMemberIdAndMemberRoleIdAndShopId(memberId, memberRoleId, shopId);
                        }
                        if (existPurchaseCount >= ProductConstant.PURCHASE_MAX_COUNT) {
                            throw new BusinessException(ResponseCodeEnum.PRODUCT_PURCHASE_COUNT_OUT);
                        }
                        BigDecimal count = purchaseRequest.getCount();
                        if (count.compareTo(stock) <= 0) {
                            oldPurchaseDO = new PurchaseDO();
                            //数据库持久化对象
                            oldPurchaseDO.setShopId(shopId);
                            oldPurchaseDO.setCount(purchaseRequest.getCount());
                            oldPurchaseDO.setCommoditySkuId(commoditySkuId);
                            oldPurchaseDO.setMemberId(memberId);
                            oldPurchaseDO.setMemberName(sysUser.getMemberName());
                            oldPurchaseDO.setMemberRoleId(memberRoleId);
                            oldPurchaseDO.setMemberRoleName(sysUser.getMemberRoleName());
                            oldPurchaseDO.setUserId(sysUser.getUserId());
                            oldPurchaseDO.setUserName(sysUser.getUserName());
                            oldPurchaseDO.setPurchaseCommodityType(purchaseRequest.getPurchaseCommodityType());
                            oldPurchaseDO.setSetMealId(purchaseRequest.getSetMealId());
                            oldPurchaseDO.setSetMealName(purchaseRequest.getSetMealName());
                            oldPurchaseDO.setIsMain(purchaseRequest.getIsMain());
                            oldPurchaseDO.setParentSkuId(purchaseRequest.getParentSkuId());
                            oldPurchaseDO.setCustomerMemberId(customerMemberId);
                            oldPurchaseDO.setCustomerMemberRoleId(customerMemberRoleId);
                            oldPurchaseDO.setOrderId(orderId);
                        } else {
                            throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_PASS_STOCK);
                        }
                    }
                    return oldPurchaseDO;
                } else {
                    throw new BusinessException(ResponseCodeEnum.PRODUCT_COMMODITY_NOT_EXIST);
                }
            } else {
                throw new BusinessException(ResponseCodeEnum.PRODUCT_PURCHASE_UNITPRICE_NOT_EXIST);
            }
        }).collect(Collectors.toList());
//        long streamProcessEndTime = System.currentTimeMillis();
//        log.info("商品处理流程耗时：{}ms，处理商品数量：{}",
//                (streamProcessEndTime - streamProcessStartTime), purchaseReqList.size());

        if (!purchaseDOList.isEmpty()) {
            purchaseRepository.saveAll(purchaseDOList);
            return true;
        } else {
            return false;
        }
    }

    /**
     * 查询进货单数量
     *
     * @param purchaseQueryReq 参数
     */
    @Override
    public Integer getPurchaseCount(PurchaseQueryReq purchaseQueryReq) {
        long methodStartTime = System.currentTimeMillis();
        log.info("开始查询购物车数量，用户：{}，商店：{}", purchaseQueryReq.getMemberId(), purchaseQueryReq.getShopId());

        Pageable page = PageRequest.of(0, ProductConstant.PURCHASE_MAX_COUNT);
        Page<PurchaseDO> purchasePage = purchaseRepository.findAll(getSpecification(purchaseQueryReq), page);
        long purchaseQueryEndTime = System.currentTimeMillis();
        log.info("购物车数据查询耗时：{}ms，查询到数量：{}",
                (purchaseQueryEndTime - methodStartTime), purchasePage.getContent().size());

        List<Long> commoditySkuIdList = purchasePage.getContent().stream().map(PurchaseDO::getCommoditySkuId).collect(Collectors.toList());

        //批量查询商品信息
        HashMap<Long, EsCommodity> commodityHashMap = new HashMap<>();

        // ES批量查询商品信息耗时统计
        long esBatchQueryStartTime = System.currentTimeMillis();
        List<EsCommodity> commodityList = esCommodityRepository.findByCommoditySkuListIdIn(commoditySkuIdList);
        long esBatchQueryEndTime = System.currentTimeMillis();
        log.info("ES批量查询商品信息耗时：{}ms，查询SKU数量：{}，返回商品数量：{}",
                (esBatchQueryEndTime - esBatchQueryStartTime), commoditySkuIdList.size(), commodityList.size());

        // 构建商品映射耗时统计
        long mapBuildStartTime = System.currentTimeMillis();
        commodityList.forEach(commodity -> {
            List<EsCommoditySku> commoditySkuList = commodity.getCommoditySkuList();
            commoditySkuList.forEach(commoditySku -> commodityHashMap.put(commoditySku.getId(), commodity));
        });
        long mapBuildEndTime = System.currentTimeMillis();
        log.info("构建商品映射耗时：{}ms，映射数量：{}",
                (mapBuildEndTime - mapBuildStartTime), commodityHashMap.size());

        //过滤掉那些关联不到商品的进货单
        long filterCountStartTime = System.currentTimeMillis();
        int result = (int) purchasePage.getContent().stream()
                .map(PurchaseDO::getCommoditySkuId)
                .map(commodityHashMap::get)
                .filter(Objects::nonNull)
                .count();
        long filterCountEndTime = System.currentTimeMillis();
        log.info("过滤和计数耗时：{}ms，有效商品数量：{}",
                (filterCountEndTime - filterCountStartTime), result);

        // 方法总耗时统计
        long methodEndTime = System.currentTimeMillis();
        long totalTime = methodEndTime - methodStartTime;
        log.info("查询购物车数量完成，总耗时：{}ms，用户：{}，结果：{}",
                totalTime, purchaseQueryReq.getMemberId(), result);

        // 如果总耗时超过阈值，记录警告日志
        if (totalTime > 1000) { // 1秒阈值
            log.warn("查询购物车数量耗时过长！总耗时：{}ms，用户：{}，建议优化",
                    totalTime, purchaseQueryReq.getMemberId());
        }

        return result;
    }

    /**
     * 查询会员在商城的进货单里面商品的数量
     */
    @Override
    public BigDecimal getPurchaseCommodityCount(PurchaseQueryReq purchaseQueryReq) {
        Pageable page = PageRequest.of(0, ProductConstant.PURCHASE_MAX_COUNT);

        // 购物车数据查询耗时统计
        long purchaseQueryStartTime = System.currentTimeMillis();
        Page<PurchaseDO> purchasePage = purchaseRepository.findAll(getSpecification(purchaseQueryReq), page);
        List<PurchaseDO> content = purchasePage.getContent();
        if (!CollectionUtils.isEmpty(content)) {
            PurchaseDO purchaseDO = content.get(0);
            if (purchaseDO != null) {
                return purchaseDO.getCount();
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 构建查询条件
     *
     * @param purchaseQueryReq 参数
     */
    private Specification<PurchaseDO> getSpecification(PurchaseQueryReq purchaseQueryReq) {
        return (root, query, criteriaBuilder) -> {
            Predicate finalConditions = criteriaBuilder.conjunction();
            Long shopId = purchaseQueryReq.getShopId();
            Long memberId = purchaseQueryReq.getMemberId();
            Long memberRoleId = purchaseQueryReq.getMemberRoleId();
            Long userId = purchaseQueryReq.getUserId();
            Long customerMemberId = purchaseQueryReq.getCustomerMemberId();
            Long customerMemberRoleId = purchaseQueryReq.getCustomerMemberRoleId();
            Integer purchaseCommodityType = purchaseQueryReq.getPurchaseCommodityType();
            Long commoditySkuId = purchaseQueryReq.getCommoditySkuId();
            Long setMealId = purchaseQueryReq.getSetMealId();
            Long orderId = purchaseQueryReq.getOrderId();
            Long branchId = purchaseQueryReq.getBranchId();
            Integer saleMode = purchaseQueryReq.getSaleMode();

            //商城id
            if (shopId != null && shopId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.shopId), shopId));
            }

            //会员id
            if (memberId != null && memberId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.memberId), memberId));
            }

            //会员角色id
            if (memberRoleId != null && memberRoleId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.memberRoleId), memberRoleId));
            }

            //会员用户id
            if (userId != null && userId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.userId), userId));
            }

            //商品skuId
            if (commoditySkuId != null && commoditySkuId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.commoditySkuId), commoditySkuId));
            }

            //商品类型
            if (purchaseCommodityType != null && purchaseCommodityType > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.purchaseCommodityType), purchaseCommodityType));
            }

            //套餐id
            if (setMealId != null && setMealId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.setMealId), setMealId));
            }

            //代客下单-客户会员id
            if (customerMemberId != null && customerMemberId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.customerMemberId), customerMemberId));
            }

            //代客下单-客户会员角色id
            if (customerMemberRoleId != null && customerMemberRoleId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.customerMemberRoleId), customerMemberRoleId));
            }

            //代客下单-订单id
            if (orderId != null && orderId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.orderId), orderId));
            }

            if (branchId != null && branchId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.branchId), branchId));
            }

            if (saleMode != null && saleMode > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(PurchaseDO.Fields.saleMode), saleMode));
            }


            //排序
            Order idOrder = criteriaBuilder.desc(root.get(PurchaseDO.Fields.id));

            return query.orderBy(idOrder).where(finalConditions).getRestriction();
        };
    }

    /**
     * 计算工费总额和料金额
     *
     * @param purchaseSkuResp 进货单SKU响应对象
     * @param purchaseDO      进货单DO对象
     * @param commoditySku
     * @param saleMode
     */
    private void calculateLaborCostsAndMaterialAmount(PurchaseSkuResp purchaseSkuResp, PurchaseDO purchaseDO, EsCommoditySku commoditySku, Integer saleMode, BigDecimal goldPrice) {
        try {
            // 1. 计算工费总额（仅对现货商品）
            if (SPOT.getCode().equals(saleMode)) {
                BigDecimal totalLaborCosts = BigDecimal.ZERO;
                //去数据库中查询
                List<FreightSpaceSingleProductResp> singleProductList = freightSpaceService.getSingleProductList(commoditySku.getId());
                if (!CollectionUtils.isEmpty(singleProductList)) {
                    List<FreightSpaceSingleProductResp> spaceSingleProducts = singleProductList.stream().filter(Objects::nonNull).filter(o -> o.getSingleCode().equals(purchaseDO.getSingleCode())).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(spaceSingleProducts)) {
                        FreightSpaceSingleProductResp singleProduct = spaceSingleProducts.get(0);
                        purchaseSkuResp.setNetWeight(singleProduct.getNetWeight());
                        //扩展费用总额
                        BigDecimal extendTotal = Optional.ofNullable(singleProduct.getOtherExtendList())
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(Objects::nonNull)
                                .map(FreightSpaceSingleProductExtendResp::getFieldValue)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        // 计算工费总额 = (基础工费 + 附加工费) × 金重 + 扩展费用总额 + 件工费
                        BigDecimal baseLabor = DataNullSafeGetUtil.nullSafeGet(singleProduct::getBaseLaborCosts);
                        BigDecimal additionalLabor = DataNullSafeGetUtil.nullSafeGet(singleProduct::getAdditionalLaborCosts);
                        BigDecimal pieceLaborCosts = DataNullSafeGetUtil.nullSafeGet(singleProduct::getPieceLaborCosts);
                        BigDecimal netWeight = DataNullSafeGetUtil.nullSafeGet(singleProduct::getNetWeight);
                        BigDecimal singleTotalLaborCosts = baseLabor.add(additionalLabor)
                                .multiply(netWeight)
                                .add(extendTotal).add(pieceLaborCosts).setScale(2, RoundingMode.HALF_UP);
                        // 工费总额
                        purchaseSkuResp.setTotalLaborCosts(singleTotalLaborCosts);
                    } else {
                        // 计算工费总额 = (基础工费 + 附加工费) × 金重 + 件工费
                        BigDecimal baseLabor = DataNullSafeGetUtil.nullSafeGet(commoditySku::getBaseLaborCosts);
                        BigDecimal additionalLabor = DataNullSafeGetUtil.nullSafeGet(commoditySku::getAdditionalLaborCosts);
                        BigDecimal pieceLaborCosts = DataNullSafeGetUtil.nullSafeGet(commoditySku::getPieceLaborCosts);
                        BigDecimal netWeight = DataNullSafeGetUtil.nullSafeGet(commoditySku::getNetWeight);
                        BigDecimal singleTotalLaborCosts = baseLabor.add(additionalLabor)
                                .multiply(netWeight)
                                .add(pieceLaborCosts).setScale(2, RoundingMode.HALF_UP);
                        // 工费总额
                        purchaseSkuResp.setTotalLaborCosts(singleTotalLaborCosts);
                    }
                }

                log.debug("计算工费总额成功，商品类型：现货，SKU ID：{}，工费总额：{}",
                        purchaseDO.getCommoditySkuId(), totalLaborCosts);


            } else if (ORDER.getCode().equals(saleMode)) {
                // 订货商品：计算预估工费（与现货商品逻辑相同）
                BigDecimal totalLaborCosts = BigDecimal.ZERO;

                // 订货商品使用商品SKU的基础工费信息进行预估
                BigDecimal baseLabor = DataNullSafeGetUtil.nullSafeGet(commoditySku::getBaseLaborCosts);
                BigDecimal additionalLabor = DataNullSafeGetUtil.nullSafeGet(commoditySku::getAdditionalLaborCosts);
                BigDecimal pieceLaborCosts = DataNullSafeGetUtil.nullSafeGet(commoditySku::getPieceLaborCosts);
                BigDecimal netWeight = DataNullSafeGetUtil.nullSafeGet(commoditySku::getWeight);

                // 设置净重
                purchaseSkuResp.setNetWeight(netWeight);

                // 计算预估工费总额 = (基础工费 + 附加工费) × 金重 + 件工费
                BigDecimal estimatedTotalLaborCosts = baseLabor.add(additionalLabor)
                        .multiply(netWeight)
                        .add(pieceLaborCosts).setScale(2, RoundingMode.HALF_UP);

                // 设置预估工费总额
                purchaseSkuResp.setTotalLaborCosts(estimatedTotalLaborCosts);

                log.info("计算预估工费总额成功，商品类型：订货，SKU ID：{}，预估工费总额：{}",
                        purchaseDO.getCommoditySkuId(), estimatedTotalLaborCosts);

            } else {
                // 其他类型商品工费总额为0
                purchaseSkuResp.setTotalLaborCosts(BigDecimal.ZERO);
                log.debug("其他类型商品，工费总额设为0，商品类型：{}，SKU ID：{}",
                        purchaseDO.getPurchaseCommodityType(), purchaseDO.getCommoditySkuId());
            }

            // 2. 计算料金额（克重 × 实时金价）
            BigDecimal materialAmount = BigDecimal.ZERO;

            BigDecimal netWeight = purchaseSkuResp.getNetWeight();
            if (netWeight != null && netWeight.compareTo(BigDecimal.ZERO) > 0) {

                if (goldPrice != null && goldPrice.compareTo(BigDecimal.ZERO) > 0) {
                    materialAmount = netWeight.multiply(goldPrice).setScale(2, RoundingMode.HALF_UP);
                    log.info("计算料金额成功，SKU ID：{}，克重：{}，金价：{}，料金额：{}",
                            purchaseDO.getCommoditySkuId(), netWeight, goldPrice, materialAmount);
                } else {
                    log.warn("获取实时金价失败或金价为0，SKU ID：{}，金价：{}",
                            purchaseDO.getCommoditySkuId(), goldPrice);
                }
            } else {
                log.info("商品克重为空或为0，料金额设为0，SKU ID：{}，克重：{}",
                        purchaseDO.getCommoditySkuId(), netWeight);
            }

            purchaseSkuResp.setMaterialAmount(materialAmount);

            // 3. 计算总金额（原料额 + 工费总额）
            BigDecimal totalAmount = materialAmount.add(purchaseSkuResp.getTotalLaborCosts() != null ?
                    purchaseSkuResp.getTotalLaborCosts() : BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);

            purchaseSkuResp.setTotalAmount(totalAmount);

            // 设置商品总价格（用于购物车显示）
            // 购物车列表使用unitPrice Map来存储价格信息
            Map<String, BigDecimal> unitPriceMap = purchaseSkuResp.getUnitPrice();
            if (unitPriceMap == null) {
                unitPriceMap = new HashMap<>();
                purchaseSkuResp.setUnitPrice(unitPriceMap);
            }
            // 使用常量键来存储总金额
            unitPriceMap.put(Constant.NOT_STEP_PRICE_KEY, totalAmount);

            log.debug("计算总金额成功，SKU ID：{}，原料额：{}，工费总额：{}，总金额：{}",
                    purchaseDO.getCommoditySkuId(), materialAmount,
                    purchaseSkuResp.getTotalLaborCosts(), totalAmount);

        } catch (Exception e) {
            log.error("计算工费总额和料金额失败，SKU ID：{}，错误信息：{}",
                    purchaseDO.getCommoditySkuId(), e.getMessage(), e);
            // 异常情况下设置默认值
            purchaseSkuResp.setTotalLaborCosts(BigDecimal.ZERO);
            purchaseSkuResp.setMaterialAmount(BigDecimal.ZERO);
        }
    }

    /**
     * 订单导入批量新增进货单
     * 先根据skuId去重，判断是现货订单还是订货订单
     * 现货订单则根据skuId查询库存，然后得到任一单件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer savePurchaseBatchByOrderImport(UserLoginCacheDTO sysUser, Long shopId, List<PurchaseOrderImportReq> purchaseOrderImportReqList) {
        log.info("订单导入批量新增进货单开始，商城ID：{}，进货单数量：{}", shopId, purchaseOrderImportReqList.size());

        if (CollectionUtils.isEmpty(purchaseOrderImportReqList)) {
            log.warn("进货单列表为空，无需处理");
            return 0;
        }

        // 1. 转换为标准的 PurchaseReq 对象
        List<PurchaseReq> purchaseReqList = convertToPurchaseReqList(purchaseOrderImportReqList);

        // 2. 根据skuId去重，合并相同商品的数量
        Map<Long, PurchaseReq> skuIdToPurchaseMap = deduplicateBySkuId(purchaseReqList);
        log.info("根据skuId去重后，商品种类数量：{}", skuIdToPurchaseMap.size());

        // 3. 查询商品信息，判断销售方式
        List<PurchaseReq> processedPurchaseList = new ArrayList<>();
        for (Map.Entry<Long, PurchaseReq> entry : skuIdToPurchaseMap.entrySet()) {
            Long skuId = entry.getKey();
            PurchaseReq purchaseReq = entry.getValue();

            try {
                // 查询商品信息
                EsCommodity commodity = esCommodityRepository.findByCommoditySkuListId(skuId);
                if (commodity == null) {
                    log.warn("商品不存在，跳过处理，SKU ID：{}", skuId);
                    continue;
                }

                // 判断销售方式并处理
                PurchaseReq processedPurchase = processPurchaseByMode(purchaseReq, commodity);
                if (processedPurchase != null) {
                    processedPurchaseList.add(processedPurchase);
                }

            } catch (Exception e) {
                log.error("处理商品失败，SKU ID：{}，错误信息：{}", skuId, e.getMessage(), e);
                // 继续处理其他商品，不中断整个流程
            }
        }

        if (CollectionUtils.isEmpty(processedPurchaseList)) {
            log.warn("没有有效的进货单数据，处理结束");
            return 0;
        }

        // 4. 调用批量加购方法
        log.info("开始调用批量加购方法，有效进货单数量：{}", processedPurchaseList.size());
        Integer result = savePurchaseBatch(sysUser, shopId, processedPurchaseList);

        log.info("订单导入批量新增进货单完成，处理结果：{}", result);
        return result;
    }

    /**
     * 根据skuId去重，合并相同商品的数量
     */
    private Map<Long, PurchaseReq> deduplicateBySkuId(List<PurchaseReq> purchaseReqList) {
        Map<Long, PurchaseReq> skuIdToPurchaseMap = new HashMap<>();

        for (PurchaseReq purchaseReq : purchaseReqList) {
            if (purchaseReq.getCommoditySkuId() == null) {
                log.warn("进货单中商品SKU ID为空，跳过处理");
                continue;
            }

            Long skuId = purchaseReq.getCommoditySkuId();
            if (skuIdToPurchaseMap.containsKey(skuId)) {
                // 合并数量
                PurchaseReq existingPurchase = skuIdToPurchaseMap.get(skuId);
                BigDecimal existingCount = existingPurchase.getCount() != null ? existingPurchase.getCount() : BigDecimal.ZERO;
                BigDecimal newCount = purchaseReq.getCount() != null ? purchaseReq.getCount() : BigDecimal.ZERO;
                existingPurchase.setCount(existingCount.add(newCount));

                log.debug("合并相同SKU商品数量，SKU ID：{}，原数量：{}，新增数量：{}，合并后数量：{}",
                        skuId, existingCount, newCount, existingPurchase.getCount());
            } else {
                // 新增商品
                skuIdToPurchaseMap.put(skuId, purchaseReq);
            }
        }

        return skuIdToPurchaseMap;
    }

    /**
     * 根据销售方式处理进货单
     */
    private PurchaseReq processPurchaseByMode(PurchaseReq purchaseReq, EsCommodity commodity) {
        // 判断销售方式
        boolean isSpotGoods = isSpotGoods(purchaseReq);

        if (isSpotGoods) {
            // 现货商品处理
            return processSpotGoods(purchaseReq, commodity);
        } else {
            // 订货商品处理
            return processOrderGoods(purchaseReq, commodity);
        }
    }

    /**
     * 判断是否为现货商品
     */
    private boolean isSpotGoods(PurchaseReq purchaseReq) {

        if (SPOT.getCode().equals(purchaseReq.getSaleMode())) {
            return true;
        }
        return false;
    }

    /**
     * 处理现货商品
     * 根据skuId查询库存，然后得到任一单件
     */
    private PurchaseReq processSpotGoods(PurchaseReq purchaseReq, EsCommodity commodity) {
        Long skuId = purchaseReq.getCommoditySkuId();

        try {
            // 查询单件库存
            List<FreightSpaceSingleProductDO> singleProducts = freightSpaceSingleProductRepository.findBySkuIdIn(Collections.singletonList(skuId));

            if (CollectionUtils.isEmpty(singleProducts)) {
                log.warn("现货商品未找到单件库存，SKU ID：{}", skuId);
                return null;
            }

            // 获取任一单件（取第一个可用的）
            FreightSpaceSingleProductDO singleProduct = singleProducts.stream()
                    .filter(sp -> FreightSpaceSingleProductStatusEnum.NOT_USE.getCode().equals(sp.getStatus()))
                    .findFirst()
                    .orElse(null); // 如果没有库存大于0的，就取第一个
            if (Objects.isNull(singleProduct)) {
                log.warn("skuId:{} 对应的单件都无库存", skuId);
                return null;
            }

            // 设置现货商品相关字段
            purchaseReq.setSaleMode(SPOT.getCode());
            purchaseReq.setCommoditySingleId(singleProduct.getId());
            purchaseReq.setSingleCode(singleProduct.getSingleCode());
            purchaseReq.setCount(BigDecimal.ONE);

            log.info("处理现货商品成功，SKU ID：{}，单件ID：{}，单件编码：{}",
                    skuId, singleProduct.getId(), singleProduct.getSingleCode());

            return purchaseReq;

        } catch (Exception e) {
            log.error("处理现货商品失败，SKU ID：{}，错误信息：{}", skuId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 处理订货商品
     */
    private PurchaseReq processOrderGoods(PurchaseReq purchaseReq, EsCommodity commodity) {
        // 设置订货商品相关字段
        purchaseReq.setSaleMode(CommoditySaleModeEnum.ORDER.getCode());
        // 订货商品不需要设置单件相关字段
        purchaseReq.setCommoditySingleId(null);
        purchaseReq.setSingleCode(null);

        log.info("处理订货商品成功，SKU ID：{}", purchaseReq.getCommoditySkuId());

        return purchaseReq;
    }

    /**
     * 转换 PurchaseOrderImportReq 列表为 PurchaseReq 列表
     */
    private List<PurchaseReq> convertToPurchaseReqList(List<PurchaseOrderImportReq> purchaseOrderImportReqList) {
        List<PurchaseReq> purchaseReqList = new ArrayList<>();

        for (PurchaseOrderImportReq importReq : purchaseOrderImportReqList) {
            PurchaseReq purchaseReq = new PurchaseReq();

            // 复制基本字段
            purchaseReq.setCommoditySkuId(importReq.getCommoditySkuId());
            purchaseReq.setSaleMode(importReq.getSaleMode());
            purchaseReq.setPurchaseCommodityType(importReq.getPurchaseCommodityType());
            purchaseReq.setCount(importReq.getCount());
            purchaseReq.setBranchId(importReq.getBranchId());

            // 设置默认值
            purchaseReq.setIsMain(null); // 非套餐商品
            purchaseReq.setSetMealId(null);
            purchaseReq.setParentSkuId(null);

            purchaseReqList.add(purchaseReq);
        }

        log.info("转换订单导入请求完成，原始数量：{}，转换后数量：{}",
                purchaseOrderImportReqList.size(), purchaseReqList.size());

        return purchaseReqList;
    }

    /**
     * 根据购物车ID列表查询下单前的数据准备字段
     * 将购物车数据转换为 MobileOrderProductReq 格式，用于下单
     */
    @Override
    public List<MobileOrderProductResp> getOrderProductsByCartIds(UserLoginCacheDTO sysUser, Long shopId, CommonIdListReq purchaseCartIdsReq, BigDecimal goldPrice, OrderDepositResp depositResp) {
        log.info("根据购物车ID列表查询下单前数据开始，商城ID：{}，购物车ID数量：{}", shopId, purchaseCartIdsReq.getIdList().size());

        List<Long> cartIds = purchaseCartIdsReq.getIdList();
        if (CollectionUtils.isEmpty(cartIds)) {
            log.warn("购物车ID列表为空");
            return new ArrayList<>();
        }

        // 1. 查询购物车数据（进货单数据）
        List<PurchaseDO> purchaseList = purchaseRepository.findByIdIn(cartIds);

        if (CollectionUtils.isEmpty(purchaseList)) {
            log.warn("未找到有效的购物车数据，购物车ID：{}", cartIds);
            return new ArrayList<>();
        }

        // 2. 转换为 MobileOrderProductResp 列表
        List<MobileOrderProductResp> orderProducts = new ArrayList<>();

        // 3. 计算客户工费优惠
        List<Long> skuIdList = purchaseList.stream().map(PurchaseDO::getCommoditySkuId).collect(Collectors.toList());
        List<EsCommodity> commoditySkuListIdIn = esCommodityRepository.findByCommoditySkuListIdIn(skuIdList);
        List<MobileCustomerFeeDiscountResp> customerFeeDiscountRespList = getMobileCustomerFeeDiscountResps(sysUser, purchaseList, commoditySkuListIdIn);

        // 4. 获取仓库模板id
        List<FreightSpaceSingleProductDO> freightSpaceSingleProductList = new ArrayList<>();
        List<WarehouseLogisticsDO> warehouseIdList = new ArrayList<>();
        List<String> singleCodeList = purchaseList.stream().map(PurchaseDO::getSingleCode).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(singleCodeList)) {
            freightSpaceSingleProductList = freightSpaceSingleProductRepository.findAllBySingleCodeIn(singleCodeList);
            List<Long> warehouseList = freightSpaceSingleProductList.stream().map(FreightSpaceSingleProductDO::getWarehouseId).collect(Collectors.toList());
            warehouseIdList = warehouseLogisticsRepository.findAllByWarehouseIdIn(warehouseList);
        }

        for (PurchaseDO purchase : purchaseList) {
            try {
                MobileOrderProductResp orderProduct = convertToMobileOrderProduct(purchase, sysUser, shopId, goldPrice, depositResp, customerFeeDiscountRespList);
                if (orderProduct != null && StringUtils.isNotEmpty(orderProduct.getSingleCode())) {
                    Optional<FreightSpaceSingleProductDO> singleProductDO = freightSpaceSingleProductList.stream().filter(f -> f.getSingleCode().equals(orderProduct.getSingleCode())).findFirst();
                    if (singleProductDO.isPresent()) {
                        Optional<WarehouseLogisticsDO> warehouseLogisticsDO = warehouseIdList.stream().filter(w -> w.getWarehouseId().equals(singleProductDO.get().getWarehouseId())).findFirst();
                        warehouseLogisticsDO.ifPresent(logisticsDO -> {
                            orderProduct.setTemplateId(logisticsDO.getTemplateId());
                            orderProduct.setLogisticsTemplateId(logisticsDO.getTemplateId());
                        });
                    }
                }
                if (orderProduct != null) {
                    orderProducts.add(orderProduct);
                }
            } catch (Exception e) {
                log.error("转换购物车数据失败，购物车ID：{}，错误信息：{}", purchase.getId(), e.getMessage(), e);
            }
        }

        log.info("查询下单前数据完成，总数量：{}", orderProducts.size());

        return orderProducts;
    }

    private List<MobileCustomerFeeDiscountResp> getMobileCustomerFeeDiscountResps(UserLoginCacheDTO sysUser, List<PurchaseDO> purchaseList, List<EsCommodity> commoditySkuListIdIn) {
        List<MobileCustomerFeeDiscountResp> customerFeeDiscountRespList;
        if (SPOT.getCode().equals(purchaseList.get(0).getSaleMode())) {
            CustomerCalReq customerCalReq = new CustomerCalReq();
            List<CalDiscountSkuReq> discountSkuReqList = purchaseList.stream().map(s -> {
                CalDiscountSkuReq calDiscountSkuReq = new CalDiscountSkuReq();
                calDiscountSkuReq.setSingleCode(s.getSingleCode());
                return calDiscountSkuReq;
            }).collect(Collectors.toList());
            customerCalReq.setSkuIdList(discountSkuReqList);
            customerCalReq.setCustomerId(sysUser.getMemberId());
            WrapperResp<List<MobileCustomerFeeDiscountResp>> calculatedDiscount = memberCustomerProcessFeeDiscountFeign.calculateDiscount(customerCalReq);
            customerFeeDiscountRespList = calculatedDiscount.getData();
        } else {
            // 获取工费优惠信息
            CustomerCalReq customerCalReq = new CustomerCalReq();
            List<CalDiscountSkuReq> discountSkuReqList = new ArrayList<>();
            commoditySkuListIdIn.stream().forEach(s -> {
                s.getCommoditySkuList().forEach(sku -> {
                    CalDiscountSkuReq calDiscountSkuReq = new CalDiscountSkuReq();
                    calDiscountSkuReq.setSingleCode(null);
                    calDiscountSkuReq.setBaseLaborCosts(sku.getBaseLaborCosts());
                    calDiscountSkuReq.setAdditionalLaborCosts(sku.getAdditionalLaborCosts());
                    calDiscountSkuReq.setPieceLaborCosts(sku.getPieceLaborCosts());
                    calDiscountSkuReq.setQuantity(1);
                    calDiscountSkuReq.setNetWeight(sku.getWeight());
                    calDiscountSkuReq.setSkuId(sku.getId());
                    calDiscountSkuReq.setSaleMode(ORDER.getCode());
                    discountSkuReqList.add(calDiscountSkuReq);
                });
            });
            customerCalReq.setSkuIdList(discountSkuReqList);
            customerCalReq.setCustomerId(sysUser.getMemberId());
            WrapperResp<List<MobileCustomerFeeDiscountResp>> calculatedDiscount = memberCustomerProcessFeeDiscountFeign.calculateDiscount(customerCalReq);
            customerFeeDiscountRespList = calculatedDiscount.getData();
        }
        return customerFeeDiscountRespList;
    }

    /**
     * 根据购物车id，查询商品信息（根据仓库组拆分）
     *
     * @param commonIdListRequest 购物车ID请求
     * @return 商品信息列表
     */
    @Override
    public List<OrderProductResp> getPurchaseCartProductByCartIds(UserLoginCacheDTO sysUser, Long shopId, CommonIdListReq commonIdListRequest) {
        // new获取实时金价
        GoldPriceResp goldPrice = eosApiService.getGoldPrice();

        // 查看-订货订单定金/支付手续费/打包费配置
        WrapperResp<OrderDepositResp> orderConfig = orderProcessFeign.findOrderConfig();

        // 根据购物车ID列表查询下单前的数据准备字段 将购物车数据转换为 MobileOrderProductReq 格式，用于下单
        List<MobileOrderProductResp> orderProductsByCartIds = getOrderProductsByCartIds(sysUser, shopId, commonIdListRequest, goldPrice.getJj(), orderConfig.getData());

        if (CollUtil.isEmpty(orderProductsByCartIds)) {
            log.warn("根据购物车ID列表未查询到下单前数据，购物车ID列表：{}", commonIdListRequest.getIdList());
            return new ArrayList<>();
        }

        List<OrderProductResp> orderProductRespList = new ArrayList<>();

        // 判断是否需要拆单
        if (SPOT.getCode().equals(orderProductsByCartIds.get(0).getSaleMode())) {
            orderProductsByCartIds.stream().forEach(s -> {
                // 如果是现货商品，根据仓库组拆单
                Optional<OrderProductResp> respOptional = orderProductRespList.stream().filter(o -> o.getWarehouseGroupId().equals(s.getWarehouseResp().getWarehouseGroupId())).findFirst();
                if (respOptional.isPresent()) {
                    respOptional.get().getMobileOrderProductRespList().add(s);
                } else {
                    OrderProductResp orderProductResp = new OrderProductResp();
                    orderProductResp.setWarehouseGroupId(s.getWarehouseResp().getWarehouseGroupId());
                    orderProductResp.setWarehouseGroupName(s.getWarehouseResp().getWarehouseGroupName());
                    orderProductResp.setMobileOrderProductRespList(new ArrayList<>());
                    orderProductResp.getMobileOrderProductRespList().add(s);
                    orderProductRespList.add(orderProductResp);
                }
            });
        } else {
            // 订货商品不需要根据仓库组分组
            OrderProductResp orderProductResp = new OrderProductResp();
            orderProductResp.setWarehouseGroupId(0L); // 订货商品没有仓库组
            orderProductResp.setWarehouseGroupName("订货商品");
            orderProductResp.setMobileOrderProductRespList(orderProductsByCartIds);
            orderProductRespList.add(orderProductResp);
        }
        // 计算订单明细
        orderProductRespList.forEach(orderProductOrderResp -> {
            orderProductOrderResp.setPackingFee(orderConfig.getData().getPackingFee());
            // 统计商品的原料额
            BigDecimal totalMaterialAmount = orderProductOrderResp.getMobileOrderProductRespList().stream()
                    .map(s -> s.getPrice().multiply(s.getQuantity()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            orderProductOrderResp.setRawMaterialAmount(totalMaterialAmount.setScale(2, RoundingMode.HALF_UP));
            // 统计商品的工费总额
            BigDecimal processingFeeAmount = orderProductOrderResp.getMobileOrderProductRespList().stream().filter(m -> m.getProcessingFeeAmount() != null)
                    .map(s -> s.getProcessingFeeAmount().multiply(s.getQuantity()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.info("统计商品的工费总额，工费总额：{}", processingFeeAmount);
            orderProductOrderResp.setProcessingFeeAmount(processingFeeAmount.setScale(2, RoundingMode.HALF_UP));
            // 统计商品优惠总金额
            BigDecimal totalDiscountAmount = orderProductOrderResp.getMobileOrderProductRespList().stream().filter(m -> m.getCraftDiscountAmount() != null)
                    .map(s -> s.getCraftDiscountAmount().multiply(s.getQuantity()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            orderProductOrderResp.setCraftDiscountAmount(totalDiscountAmount);
            orderProductOrderResp.setDepositType(orderConfig.getData().getDepositType());
            orderProductOrderResp.setDepositAmount(orderProductOrderResp.getMobileOrderProductRespList().stream()
                    .map(MobileOrderProductResp::getDeposit)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            orderProductOrderResp.setServiceCharge(orderConfig.getData().getServiceCharge());
        });

        orderProductRespList.forEach(s -> {
            s.setRealTimeGoldPrice(goldPrice.getJj());
            // 把商品里的物流模板ID保持一致
            if (CollUtil.isNotEmpty(s.getMobileOrderProductRespList())) {
                Optional<Long> templateId = s.getMobileOrderProductRespList().stream().filter(mobileOrderProductResp -> ObjectUtil.isNotEmpty(mobileOrderProductResp.getTemplateId())).map(mobileOrderProductResp -> mobileOrderProductResp.getTemplateId()).findFirst();
                templateId.ifPresent(aLong -> s.getMobileOrderProductRespList().forEach(product -> product.setLogisticsTemplateId(aLong)));
            }
        });

        // 判断是否有支付，查看价格权限
        return orderProductRespList;
    }

    /**
     * 根据仓库id，查询自提地址
     *
     * @param warehouseId 仓库ID
     * @return 自提地址列表
     */
    @Override
    public List<PickupAddressResp> getPickupAddressByWarehouseId(CommonIdListReq warehouseId) {
        List<WarehouseDO> warehouseDOList = warehouseRepository.findAllById(warehouseId.getIdList());

        List<WarehouseLogisticsDO> logisticsDOList = warehouseLogisticsRepository.findAllByWarehouseIdIn(warehouseId.getIdList());

        List<PickupAddressResp> warehouseList = new ArrayList<>();
        if (CollUtil.isNotEmpty(logisticsDOList)) {
            CommonIdListReq commonIdListReq = new CommonIdListReq();
            commonIdListReq.setIdList(logisticsDOList.stream().map(WarehouseLogisticsDO::getSendAddressId).collect(Collectors.toList()));
            WrapperResp<List<ShipperAddressDetailResp>> listWrapperResp = shipperAddressFeign.listShipperAddress(commonIdListReq);
            if (WrapperUtil.isOk(listWrapperResp) && CollUtil.isNotEmpty(listWrapperResp.getData())) {
                listWrapperResp.getData().forEach(s -> {
                    PickupAddressResp warehouseResp = new PickupAddressResp();
                    BeanUtil.copyProperties(s, warehouseResp);
                    warehouseList.add(warehouseResp);
                });
            }
        }

        // 获取仓库组信息
        warehouseList.forEach(s -> {
            // 获取物流信息是属于哪个仓库组的，先根据仓库物流信息获取对应仓库id
            Optional<WarehouseLogisticsDO> warehouseLogisticsDO = logisticsDOList.stream().filter(logistics -> logistics.getSendAddressId().equals(s.getId())).findFirst();
            if (warehouseLogisticsDO.isPresent()) {
                s.setDeliveryPeriod(warehouseLogisticsDO.get().getDeliveryPeriod());
                Optional<WarehouseDO> optionalWarehouseDO = warehouseDOList.stream().filter(warehouseDO -> warehouseDO.getId().equals(warehouseLogisticsDO.get().getWarehouseId())).findFirst();
                optionalWarehouseDO.ifPresent(warehouseDO -> s.setWarehouseGroupId(warehouseDO.getWarehouseGroupId()));
                // 根据当前时间增加发货时间，获取预计发货时间(YYYY-MM-DD)
                s.setPickupTime(DateUtil.format(DateUtil.offsetDay(new Date(), warehouseLogisticsDO.get().getDeliveryPeriod()), "yyyy-MM-dd"));
            }
        });
        return warehouseList;
    }

    /**
     * 根据仓库code，查询自提地址
     *
     * @param warehouseCode 仓库code
     * @return 自提地址列表
     */
    @Override
    public List<PickupAddressResp> getPickupAddressByWarehouseCode(CommonNoListReq warehouseCode) {
        List<WarehouseDO> warehouseDOList = warehouseRepository.findAllByCodeIn(warehouseCode.getNoList());

        if (CollectionUtil.isEmpty(warehouseDOList)) {
            return new ArrayList<>();
        }
        List<Long> longList = warehouseDOList.stream().map(WarehouseDO::getId).collect(Collectors.toList());
        CommonIdListReq commonIdListReq = new CommonIdListReq();
        commonIdListReq.setIdList(longList);
        return getPickupAddressByWarehouseId(commonIdListReq);
    }

    /**
     * 将购物车数据转换为移动端订单商品数据
     */
    private MobileOrderProductResp convertToMobileOrderProduct(PurchaseDO purchase, UserLoginCacheDTO sysUser, Long shopId, BigDecimal goldPrice, OrderDepositResp depositResp, List<MobileCustomerFeeDiscountResp> customerFeeDiscountRespList) {
        try {
            // 查询商品信息
            EsCommodity commodity = esCommodityRepository.findByCommoditySkuListId(purchase.getCommoditySkuId());
            if (commodity == null) {
                log.warn("商品不存在，SKU ID：{}", purchase.getCommoditySkuId());
                return null;
            }

            // 创建订单商品对象
            MobileOrderProductResp orderProduct = new MobileOrderProductResp();

            // 设置基本信息
            orderProduct.setCartId(purchase.getId()); // 购物车ID就是进货单ID
            orderProduct.setProductId(commodity.getId());
            orderProduct.setSkuId(purchase.getCommoditySkuId());
            orderProduct.setQuantity(purchase.getCount());
            orderProduct.setSingleCode(purchase.getSingleCode());
            orderProduct.setCommoditySingleId(purchase.getCommoditySingleId());

            // 设置供应商信息
            orderProduct.setVendorMemberId(commodity.getMemberId());
            orderProduct.setVendorRoleId(commodity.getMemberRoleId());
            orderProduct.setVendorMemberName(commodity.getMemberName());

            // 设置上游供应商信息
            orderProduct.setSupplyMemberId(commodity.getUpperMemberId());
            orderProduct.setSupplyRoleId(commodity.getUpperMemberRoleId());
            orderProduct.setSupplyMemberName(commodity.getUpperMemberName());

            // 设置商品基本信息
            orderProduct.setName(commodity.getName());
            orderProduct.setCategory(commodity.getCustomerCategoryName());
            orderProduct.setBrand(commodity.getBrandName());
            orderProduct.setUnit(""); // 单位名称需要通过其他方式获取
            orderProduct.setLogo(commodity.getMainPic()); // 使用主图作为Logo
            orderProduct.setSpec(""); // 规格需要通过其他方式获取

            // 设置价格信息
            if (!CollectionUtils.isEmpty(commodity.getCommoditySkuList())) {
                EsCommoditySku sku = commodity.getCommoditySkuList().stream()
                        .filter(s -> s.getId().equals(purchase.getCommoditySkuId()))
                        .findFirst()
                        .orElse(null);
                if (ObjectUtil.isNotEmpty(sku)) {
                    orderProduct.setPrice(goldPrice.multiply(sku.getWeight()).setScale(2, RoundingMode.HALF_UP));
                    orderProduct.setSkuCode(sku.getCode()); // SKU编码需要通过其他方式获取
                    orderProduct.setWeight(sku.getWeight());
                    orderProduct.setNetWeight(sku.getWeight());
                    orderProduct.setSkuGoldWeight(sku.getWeight()); // 使用净重作为金重
                    // 获取商品属性里的成色
                    if (CollectionUtil.isNotEmpty(sku.getCommoditySkuAttributeList())) {
                        Optional<EsCommoditySkuAttribute> commoditySkuAttribute = sku.getCommoditySkuAttributeList().stream().filter(s -> s.getCustomerAttribute().getName().equals("成色")).findFirst();
                        commoditySkuAttribute.ifPresent(esCommoditySkuAttribute -> orderProduct.setFineness(esCommoditySkuAttribute.getCustomerAttributeValue().getValue()));
                    }
                    orderProduct.setBaseLaborCosts(ObjectUtil.isNotEmpty(sku.getBaseLaborCosts()) ? sku.getBaseLaborCosts().multiply(sku.getWeight()) : BigDecimal.ZERO);
                    orderProduct.setBaseLaborCostsPerGram(sku.getBaseLaborCosts());
                    orderProduct.setAdditionalLaborCosts(ObjectUtil.isNotEmpty(sku.getAdditionalLaborCosts()) ? sku.getAdditionalLaborCosts().multiply(sku.getWeight()) : BigDecimal.ZERO);
                    orderProduct.setAdditionalLaborCostsPerGram(sku.getAdditionalLaborCosts());
                    orderProduct.setPieceLaborCosts(ObjectUtil.isNotEmpty(sku.getPieceLaborCosts()) ? sku.getPieceLaborCosts() : BigDecimal.ZERO);
                    orderProduct.setRefPrice(orderProduct.getPrice().add(orderProduct.getBaseLaborCosts()).add(orderProduct.getAdditionalLaborCosts()).add(orderProduct.getPieceLaborCosts()));
                }
            }

            // TODO 设置商品编码
            orderProduct.setSpuCode(commodity.getCode()); // SPU编码需要通过其他方式获取

            // 设置销售方式
            orderProduct.setSaleMode(purchase.getSaleMode());

            // 设置现货商品特有字段和库存信息
            if (SPOT.getCode().equals(purchase.getSaleMode())) {
                orderProduct.setCommoditySingleId(purchase.getCommoditySingleId());
                orderProduct.setSingleCode(purchase.getSingleCode());
                // 现货商品：根据单件SKU ID查询库存
                setSpotGoodsStockBySingleSkuCode(orderProduct, purchase.getSingleCode(), goldPrice);

            } else {
                // 订货商品：使用常规库存查询
                orderProduct.setStockStatus(Boolean.TRUE);
                if (orderProduct.getBaseLaborCosts() != null) {
                    orderProduct.setProcessingFeeAmount(orderProduct.getProcessingFeeAmount().add(orderProduct.getBaseLaborCosts()));
                }
                if (orderProduct.getAdditionalLaborCosts() != null) {
                    orderProduct.setProcessingFeeAmount(orderProduct.getProcessingFeeAmount().add(orderProduct.getAdditionalLaborCosts()));
                }
                if (orderProduct.getPieceLaborCosts() != null) {
                    orderProduct.setProcessingFeeAmount(orderProduct.getProcessingFeeAmount().add(orderProduct.getPieceLaborCosts()));
                }
            }

            // 设置商品状态
            orderProduct.setProductStatus(true); // 默认商品有效

            // 设置默认值
            orderProduct.setPriceType(1); // 默认现货价格
            orderProduct.setDiscount(BigDecimal.ONE); // 默认无折扣
            orderProduct.setTax(false); // 默认不含税
            orderProduct.setCrossBorder(false); // 默认非跨境商品
            orderProduct.setDeliveryType(1); // 默认物流配送
            orderProduct.setFreightType(2); // 默认买家承担运费

            // 设置交货周期
            orderProduct.setDeliveryPeriodMin(commodity.getDeliveryPeriodMin());
            orderProduct.setDeliveryPeriodMax(commodity.getDeliveryPeriodMax());

            // 设置材质信息
            orderProduct.setMaterialInfo(commodity.getMetalMaterialValue()); // 使用金属材质值

            if (ObjectUtil.isNotEmpty(depositResp)) {
                if (DepositTypeEnum.FIXED_AMOUNT.getCode().equals(depositResp.getDepositType())) {
                    // 固定金额
                    orderProduct.setDeposit(depositResp.getDepositAmount().multiply(orderProduct.getQuantity()));
                } else {
                    // 比例
                    orderProduct.setDeposit(orderProduct.getPrice().multiply(depositResp.getDepositAmount().divide(new BigDecimal(100))).setScale(3, RoundingMode.HALF_UP));
                }
            }

            // 优惠工费金额
            if (CollectionUtil.isNotEmpty(customerFeeDiscountRespList)) {
                Optional<MobileCustomerFeeDiscountResp> discountResp = Optional.empty();
                if (ORDER.getCode().equals(purchase.getSaleMode())) {
                    discountResp = customerFeeDiscountRespList.stream()
                            .filter(commonId -> commonId.getSkuId().equals(orderProduct.getSkuId()))
                            .findFirst();
                } else {
                    discountResp = customerFeeDiscountRespList.stream().filter(commonId -> commonId.getSingleCode().equals(orderProduct.getSingleCode())).findFirst();
                }

                if (discountResp.isPresent()) {
                    FeeDiscountDetailsResp feeDiscountDetails = new FeeDiscountDetailsResp();
                    feeDiscountDetails.setBaseLaborCosts(ObjectUtil.isNotEmpty(discountResp.get().getBaseLaborCostDiscountAmount()) ? discountResp.get().getBaseLaborCostDiscountAmount() : BigDecimal.ZERO);
                    feeDiscountDetails.setAdditionalLaborCosts(ObjectUtil.isNotEmpty(discountResp.get().getGramLaborCostDiscountAmount()) ? discountResp.get().getGramLaborCostDiscountAmount() : BigDecimal.ZERO);
                    feeDiscountDetails.setPieceLaborCosts(ObjectUtil.isNotEmpty(discountResp.get().getPieceLaborCostDiscountAmount()) ? discountResp.get().getPieceLaborCostDiscountAmount() : BigDecimal.ZERO);
                    orderProduct.setFeeDiscountDetails(feeDiscountDetails);
                    orderProduct.setCraftDiscountAmount(feeDiscountDetails.getBaseLaborCosts()
                            .add(feeDiscountDetails.getAdditionalLaborCosts())
                            .add(feeDiscountDetails.getPieceLaborCosts()));
                    orderProduct.setRefPrice(orderProduct.getRefPrice().subtract(orderProduct.getCraftDiscountAmount()));
                }
            }
            log.debug("转换购物车数据成功，购物车ID：{}，商品名称：{}", purchase.getId(), commodity.getName());

            return orderProduct;

        } catch (Exception e) {
            log.error("转换购物车数据失败，购物车ID：{}，错误信息：{}", purchase.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据单件编码设置现货商品库存信息
     * 通过单件编码查询单件信息，并设置相关的库存、仓库、仓库组等信息
     *
     * @param orderProduct 订单商品对象
     * @param singleCode   单件编码
     * @return 是否设置成功
     */
    private boolean setSpotGoodsStockBySingleSkuCode(MobileOrderProductResp orderProduct, String singleCode, BigDecimal goldPrice) {
        try {
            if (StringUtils.isBlank(singleCode)) {
                log.warn("单件编码为空，无法设置现货商品库存信息");
                return false;
            }

            // 1. 根据单件编码查询单件信息
            FreightSpaceSingleProductDO singleProduct = freightSpaceSingleProductRepository.findBySingleCode(singleCode);
            if (singleProduct == null) {
                log.warn("未找到单件信息，单件编码：{}", singleCode);
                return false;
            }
            boolean canUse = FreightSpaceSingleProductStatusEnum.NOT_USE.getCode().equals(singleProduct.getStatus());
            // 2. 设置单件相关信息
            orderProduct.setCommoditySingleId(singleProduct.getId());
            orderProduct.setSingleCode(singleProduct.getSingleCode());
            orderProduct.setStock(canUse ? BigDecimal.ONE : BigDecimal.ZERO);
            orderProduct.setStockStatus(canUse);
            orderProduct.setPrice(singleProduct.getNetWeight().multiply(goldPrice).setScale(2, RoundingMode.HALF_UP));
            orderProduct.setRefPrice(orderProduct.getPrice());

            // 3. 设置仓库信息
            orderProduct.setWarehouseId(singleProduct.getWarehouseId());
            orderProduct.setWarehouseName(singleProduct.getWarehouseName());

            // 4. 查询并设置仓库组信息
            setWarehouseGroupInfo(orderProduct, singleProduct.getWarehouseId());

            // 5. 设置商品重量和工费信息
            if (singleProduct.getNetWeight() != null) {
                orderProduct.setNetWeight(singleProduct.getNetWeight());
                orderProduct.setSkuGoldWeight(singleProduct.getNetWeight()); // 使用净重作为金重
            }
            if (singleProduct.getActualWeight() != null) {
                orderProduct.setWeight(singleProduct.getActualWeight());
            }
            if (singleProduct.getBaseLaborCosts() != null) {
                orderProduct.setBaseLaborCosts(singleProduct.getBaseLaborCosts().multiply(singleProduct.getNetWeight()));
                orderProduct.setBaseLaborCostsPerGram(singleProduct.getBaseLaborCosts());
                orderProduct.setProcessingFeeAmount(orderProduct.getProcessingFeeAmount().add(singleProduct.getBaseLaborCosts().multiply(singleProduct.getNetWeight())));
            }
            if (singleProduct.getAdditionalLaborCosts() != null) {
                orderProduct.setAdditionalLaborCostsPerGram(singleProduct.getAdditionalLaborCosts());
                orderProduct.setAdditionalLaborCosts(singleProduct.getAdditionalLaborCosts().multiply(singleProduct.getNetWeight()));
                orderProduct.setProcessingFeeAmount(orderProduct.getProcessingFeeAmount().add(singleProduct.getAdditionalLaborCosts().multiply(singleProduct.getNetWeight())));
            }
            if (singleProduct.getPieceLaborCosts() != null) {
                orderProduct.setPieceLaborCosts(singleProduct.getPieceLaborCosts());
                orderProduct.setProcessingFeeAmount(orderProduct.getProcessingFeeAmount().add(singleProduct.getPieceLaborCosts()));
            }
            orderProduct.setGoodsWeight(singleProduct.getActualWeight());

            BigDecimal otherLaborCosts = BigDecimal.ZERO;
            if (ObjectUtil.isNotEmpty(singleProduct.getExtend())) {
                // 把其它工费加上，fieldValue转换成BigDecimal
                List<FreightSpaceSingleProductExtendResp> productExtendResps = JsonUtil.toList(singleProduct.getExtend(), FreightSpaceSingleProductExtendResp.class);
                otherLaborCosts = productExtendResps.stream()
                        .filter(ext -> ObjectUtil.isNotEmpty(ext.getFieldValue()))
                        .map(FreightSpaceSingleProductExtendResp::getFieldValue)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                orderProduct.setOtherLaborCostsDetails(productExtendResps);
                //orderProduct.setProcessingFeeAmount(orderProduct.getProcessingFeeAmount().add(otherLaborCosts));
            }
            orderProduct.setProcessingFeeAmount(orderProduct.getProcessingFeeAmount().setScale(2, RoundingMode.HALF_UP));
            orderProduct.setRefPrice(orderProduct.getRefPrice().add(orderProduct.getProcessingFeeAmount()));

            log.debug("根据单件编码设置现货商品库存信息成功，单件编码：{}，库存：{}，仓库：{}",
                    singleCode, singleProduct.getStatus(), singleProduct.getWarehouseName());

            return true;

        } catch (Exception e) {
            log.error("根据单件编码设置现货商品库存信息失败，单件编码：{}，错误信息：{}", singleCode, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 设置仓库组信息
     * 根据仓库ID查询仓库详情，然后获取仓库组信息
     *
     * @param orderProduct 订单商品对象
     * @param warehouseId  仓库ID
     */
    private void setWarehouseGroupInfo(MobileOrderProductResp orderProduct, Long warehouseId) {
        try {
            if (warehouseId == null) {
                return;
            }

            // 查询单个仓库详情
            WarehouseResp warehouse = warehouseService.warehouseDetails(warehouseId);
            if (warehouse == null) {
                log.warn("未找到仓库信息，仓库ID：{}", warehouseId);
                return;
            }

            orderProduct.setWarehouseResp(warehouse);

        } catch (Exception e) {
            log.error("设置仓库组信息失败，仓库ID：{}，错误信息：{}", warehouseId, e.getMessage(), e);
        }
    }

}

