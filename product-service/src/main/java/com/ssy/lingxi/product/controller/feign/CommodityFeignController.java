package com.ssy.lingxi.product.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.model.req.*;
import com.ssy.lingxi.product.api.model.req.baitai.CommoditySkuIdsReq;
import com.ssy.lingxi.product.api.model.req.baitai.SpaceSingleProductStatusReq;
import com.ssy.lingxi.product.api.model.req.feign.CommodityPriceReq;
import com.ssy.lingxi.product.api.model.req.feign.CommodityPublishReq;
import com.ssy.lingxi.product.api.model.req.feign.CommoditySinglePriceReq;
import com.ssy.lingxi.product.api.model.req.feign.OffPublishCommodityReq;
import com.ssy.lingxi.product.api.model.resp.BrandResp;
import com.ssy.lingxi.product.api.model.resp.commodity.*;
import com.ssy.lingxi.product.api.model.resp.esCommodity.EsCommoditySkuResp;
import com.ssy.lingxi.product.api.model.resp.feign.CommodityPriceResp;
import com.ssy.lingxi.product.api.model.resp.store.StoreResp;
import com.ssy.lingxi.product.entity.do_.BrandDO;
import com.ssy.lingxi.product.entity.do_.customer.CustomerCategoryDO;
import com.ssy.lingxi.product.service.IBrandService;
import com.ssy.lingxi.product.service.commodity.ICommodityLogisticsService;
import com.ssy.lingxi.product.service.commodity.ICommodityPublishService;
import com.ssy.lingxi.product.service.commodity.ICommodityService;
import com.ssy.lingxi.product.service.commodity.ICommoditySkuService;
import com.ssy.lingxi.product.service.customer.ICustomerCategoryService;
import com.ssy.lingxi.product.service.esCommodity.IEsCommodityService;
import com.ssy.lingxi.product.service.esCommodity.IShopService;
import com.ssy.lingxi.product.service.materiel.IMaterielService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 商品管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/28
 * @ignore 不需要提交到Yapi
 */
@RestController
@RequiredArgsConstructor
public class CommodityFeignController extends BaseController implements ICommodityFeign {
    private final IShopService shopService;
    private final IBrandService brandService;
    private final IMaterielService materielService;
    private final ICommodityService commodityService;
    private final IEsCommodityService esCommodityService;
    private final ICommoditySkuService commoditySkuService;
    private final ICommodityPublishService commodityPublishService;
    private final ICustomerCategoryService customerCategoryService;
    private final ICommodityLogisticsService commodityLogisticsService;

    /**
     * 查询商品是否用到运费模板
     * @param id 运费模板id
     */
    @Override
    public WrapperResp<Boolean> getCommodityUseTemplate(@RequestParam("id") Long id) {
        return WrapperUtil.success(commodityLogisticsService.getCommodityUseTemplate(id));
    }

    /**
     * 查询商品是否用到发货地址
     * @param id 发货地址id
     */
    @Override
    public WrapperResp<Boolean> getCommodityUseSendAddress(@RequestParam("id") Long id) {
        return WrapperUtil.success(commodityLogisticsService.getCommodityUseSendAddress(id));
    }

    /**
     * 查询商品是否用到物流公司
     * @param id 物流公司id
     */
    @Override
    public WrapperResp<Boolean> getCommodityUseCompany(@RequestParam("id") Long id) {
        return WrapperUtil.success(commodityLogisticsService.getCommodityUseCompany(id));
    }

    /**
     * 查询商品是否用到单位
     * @param id 单位id
     */
    @Override
    public WrapperResp<Boolean> getCommodityUseUnit(@RequestParam("id") Long id) {
        return WrapperUtil.success(commodityService.getCommodityUseUnit(id));
    }

    /**
     * 批量查询商品价格
     */
    @Override
    public WrapperResp<Map<Long, Map<String, BigDecimal>>> getCommodityPriceBatch(@RequestBody @Valid CommodityPriceReq commodityPriceReq) {
        return WrapperUtil.success(commoditySkuService.getCommodityPriceBatch(commodityPriceReq.getShopId(), commodityPriceReq.getCommoditySkuIdList(), commodityPriceReq.getMemberId(), commodityPriceReq.getMemberRoleId()));
    }

    /**
     * 批量查询商品价格和是否允许使用会员权益
     */
    @Override
    public WrapperResp<Map<Long, CommodityPriceResp>> getCommodityPriceAndMemberPriceBatch(@RequestBody @Valid CommodityPriceReq commodityPriceReq) {
        return WrapperUtil.success(commoditySkuService.getCommodityPriceAndMemberPriceBatch(commodityPriceReq.getShopId(), commodityPriceReq.getCommoditySkuIdList(), commodityPriceReq.getMemberId(), commodityPriceReq.getMemberRoleId()));
    }

    /**
     * 通过会员品类id查询会员信息
     */
    @Override
    public WrapperResp<List<Long>> getMemberIdList(@RequestParam("id") Long id){
        return WrapperUtil.success(customerCategoryService.getMemberIdList(id));
    }

    /**
     * 查询商品信息列表--订单校验商品信息
     * @param commodityPriceReq 查询条件实体
     */
    @Override
    public WrapperResp<List<CommoditySkuPublishResp>> getCommodityListByOrder(@RequestBody @Valid CommodityPriceReq commodityPriceReq) {
        return WrapperUtil.success(commoditySkuService.getCommodityListByOrder(commodityPriceReq));
    }

    /**
     * 查询商品信息列表--订单校验商品单件信息
     * @param commoditySinglePriceReq 查询条件实体
     * @return 商品信息
     */
    @Override
    public WrapperResp<List<CommoditySkuSingleResp>> getCommodityListByOrderSingle(CommoditySinglePriceReq commoditySinglePriceReq) {
        return WrapperUtil.success(commoditySkuService.getCommodityListByOrderSingle(commoditySinglePriceReq));
    }

    /**
     * 通过会员id查询会员品类和商品图片
     */
    @Override
    public WrapperResp<Map<Long, StoreResp>> getCommodityAndCategoryByMemberId(@RequestBody CommonIdListReq commonIdListReq) {
        return WrapperUtil.success(commodityService.getCommodityAndCategoryByMemberId(commonIdListReq));
    }

    /**
     * 通过会员id和会员角色id查询会员品类和商品图片
     */
    @Override
    public WrapperResp<Map<String, StoreResp>> getCommodityAndCategoryByMemberIdAndMemberRoleId(@RequestBody List<MemberReq> memberReqList) {
        return WrapperUtil.success(commodityService.getCommodityAndCategoryByMemberIdAndMemberRoleId(memberReqList));
    }

    /**
     * 查询商品信息
     * @param id 商品skuId
     */
    @Override
    public WrapperResp<CommoditySkuResp> getCommodityBySkuId(@RequestParam("id") Long id) {
        return WrapperUtil.success(commoditySkuService.getCommoditySku(id));
    }

    @Override
    public WrapperResp<CommoditySkuResp> getSkuById(Long id) {
        return WrapperUtil.success(commoditySkuService.getSkuById(id));
    }

    /**
     * 下架某个会员的所有商品
     */
    @Override
    public WrapperResp<Boolean> offPublishAllCommodity(@RequestBody OffPublishCommodityReq offPublishCommodityReq) {
        commodityPublishService.offPublishAllCommodity(offPublishCommodityReq.getMemberId(), offPublishCommodityReq.getMemberRoleId());
        return WrapperUtil.success();
    }

    /**
     * 查询品类信息
     * @param idList 品类id
     */
    @Override
    public WrapperResp<List<CustomerCategoryResp>> getCustomerCategoryById(@RequestBody List<Long> idList) {
        List<CustomerCategoryDO> customerCategoryList = customerCategoryService.getCustomerCategoryByIdList(idList);
        List<CustomerCategoryResp> customerCategoryRespList = customerCategoryList.stream().map(customerCategory -> BeanUtil.copyProperties(customerCategory, CustomerCategoryResp.class)).collect(Collectors.toList());
        return WrapperUtil.success(customerCategoryRespList);
    }

    /**
     * 批量查询品类信息--通过品类名称
     * @param nameList 品类名称集合
     * @return 品类信息，key为品类名称，value为品类ID
     */
    @Override
    public WrapperResp<Map<String, Long>> getCustomerCategoryByName(@RequestBody List<String> nameList) {
        return WrapperUtil.success(customerCategoryService.getCustomerCategoryIdByNameList(nameList));
    }

    /**
     * 查询品牌信息
     * @param idList 品牌id
     */
    @Override
    public WrapperResp<List<BrandResp>> getBrandById(@RequestBody List<Long> idList) {
        List<BrandDO> brandList = brandService.getBrandByIdList(idList);
        List<BrandResp> brandRespList = brandList.stream().map(brand -> BeanUtil.copyProperties(brand, BrandResp.class)).collect(Collectors.toList());
        return WrapperUtil.success(brandRespList);
    }

    /**
     * 查询商品列表--通过会员商品sku集合
     * @param commoditySkuIdList 查询条件实体
     */
    @Override
    public WrapperResp<List<CommoditySkuStockResp>> getCommodityByCommoditySkuIdList(@RequestBody List<Long> commoditySkuIdList) {
        return WrapperUtil.success(commoditySkuService.getCommoditySkuList(commoditySkuIdList));
    }

    /**
     * 查询上游供应会员商品列表--通过会员商品sku集合
     * @param commoditySkuIdList 查询条件实体
     */
    @Override
    public WrapperResp<List<UpperCommoditySkuResp>> getUpperCommodityByCommoditySkuIdList(@RequestBody List<Long> commoditySkuIdList) {
        return WrapperUtil.success(commoditySkuService.getUpperCommoditySkuList(commoditySkuIdList));
    }

    /**
     * 过滤出已上架的商品Id列表
     * @param commodityPublishReq 查询条件实体
     * @return 上架的商品id数组
     */
    @Override
    public WrapperResp<List<Long>> getCommodityListByIsPublish(@RequestBody CommodityPublishReq commodityPublishReq) {
        return WrapperUtil.success(commodityPublishService.getCommodityListByIsPublish(commodityPublishReq.getShopId(), commodityPublishReq.getCommodityList()));
    }

    /**
     * 判断商品是否上架
     * @param commodityPublishReq 查询条件实体
     * @return 上架的商品信息
     */
    @Override
    public WrapperResp<List<CommodityBaseResp>> getCommodityInfoListByIsPublish(@RequestBody CommodityPublishReq commodityPublishReq) {
        return WrapperUtil.success(commodityPublishService.getCommodityInfoListByIsPublish(commodityPublishReq.getShopId(), commodityPublishReq.getCommodityList()));
    }

    /**
     * 更新商品对应的店铺积分
     */
    @Override
    public WrapperResp<Boolean> updateCommodityCreditScore(@RequestBody CommodityCreditScoreReq commodityCreditScoreReq){
        return WrapperUtil.success(esCommodityService.updateCommodityCreditScore(commodityCreditScoreReq));
    }

    /**
     * 通过物料Id数组查询对应的货品一级品类id
     * @param commonIdListRequest 货品Id数组
     */
    @Override
    public WrapperResp<List<MaterielIdAndFirstCategoryIdResp>> getMaterielFirstCategoryIdList(@RequestBody @Valid CommonIdListReq commonIdListRequest) {
        return WrapperUtil.success(materielService.getMaterielFirstCategoryIdList(commonIdListRequest.getIdList()));
    }

    /**
     * 获取货品绑定商品sku
     */
    @Override
    public WrapperResp<List<Long>> getSkuListByMaterielId(@RequestParam("materielId") Long materielId){
        return WrapperUtil.success(materielService.getSkuListByMaterielId(materielId));
    }

    /**
     * 查询商品品类id列表--通过商品skuId集合
     * @param commoditySkuIdList 请求参数
     * @return 商品信息
     */
    @Override
    public WrapperResp<Map<Long,Long>> getCategoryIdByCommoditySkuIdList(@RequestBody List<Long> commoditySkuIdList){
        return WrapperUtil.success(commodityService.getCategoryIdByCommoditySkuIdList(commoditySkuIdList));
    }

    /**
     * 通过物料编号数组查询对应的货品 品类id
     * @param simpleIdListRequest 物料编号数组
     */
    @Override
    public WrapperResp<List<MaterielIdAndFirstCategoryIdResp>> getMaterielCategoryIdList(@RequestBody @Valid StringNoListReq simpleIdListRequest) {
        return WrapperUtil.success(materielService.getMaterielCategoryIdList(simpleIdListRequest.getIdList()));
    }

    /**
     * 查询商品sku信息
     * @param commoditySkuReq 请求参数
     */
    @Override
    public WrapperResp<List<EsCommoditySkuResp>> getCommoditySkuList(@RequestBody @Valid CommoditySkuReq commoditySkuReq) {
        return WrapperUtil.success(shopService.getCommoditySkuList(BeanUtil.copyProperties(commoditySkuReq, EsCommoditySearchReq.class)));
    }

    /**
     * 查询会员
     * @param orderReq 参数
     */
    @Override
    public WrapperResp<List<Long>> getMemberIdList(@RequestBody OrderReq orderReq) {
        return WrapperUtil.success(shopService.getMemberIdList(orderReq));
    }

    @Override
    public WrapperResp<CommodityDetailResp> getCommodityByCode(String code) {
        return WrapperUtil.success(commodityService.getByCode(code));
    }

    /**
     * 根据商品skuId集合查询商品是否有库存
     * @param commoditySkuIdList 商品skuId集合
     * @return 商品是否有库存
     */
    @Override
    public WrapperResp<Map<Long, Boolean>> getCommoditySkuHasStock(Set<Long> commoditySkuIdList) {
        return commodityService.getCommoditySkuIdListByIsExistStock(commoditySkuIdList);
    }

    /**
     * 根据商品单件id，修改库存状态
     * @param spaceSingleProductStatusReq 商品单件id
     * @return 是否成功
     */
    @Override
    public WrapperResp<Void> updateFreightSpaceSingleProductStatus(SpaceSingleProductStatusReq spaceSingleProductStatusReq) {
        return commodityService.updateFreightSpaceSingleProductStatus(spaceSingleProductStatusReq);
    }
    /**
     * 根据sku下的单品code查询单品信息
     * @param code 单件编码
     */
    @Override
    public WrapperResp<FreightSpaceSingleProductResp> getSingleProductByCode(String code) {
        return commodityService.getSingleProductByCode(code);
    }

    /**
     * 根据sku下的单品code列表查询单品信息
     * @param codes 单件编码
     */
    @Override
    public WrapperResp<List<FreightSpaceSingleProductResp>> getSingleProductByCodes(List<String> codes) {
        return WrapperUtil.success(commodityService.getSingleProductByCodes(codes));
    }

    /**
     *  用下游skuId查询上游的商品sku
     * @param skuIds 下游skuId
     * @return
     */
    @Override
    public WrapperResp<List<CommoditySkuDetailsResp>> getCommodityByCommoditySkuIds(@RequestBody CommoditySkuIdsReq skuIds) {
        return WrapperUtil.success(commoditySkuService.getCommoditySkuByIds(skuIds));
    }

    @Override
    public WrapperResp<List<CommodityExtraDataParamResp>> getExtraDataParamListById(CommonIdReq commonIdReq) {
        return WrapperUtil.success(commodityService.getExtraDataParamListById(commonIdReq.getId()));
    }

    /**
     * 商品id获取第三方参数列表
     * @param commodityIds 商品id
     * @return 结果
     */
    @Override
    public WrapperResp<List<CommodityExtraDataParamResp>> getExtraDataParamListByIds(CommonIdListReq commodityIds) {
        return WrapperUtil.success(commodityService.getExtraDataParamListByIds(commodityIds));
    }
}
