package com.ssy.lingxi.product.controller.pc.shop;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.product.api.model.req.OftenSelectReq;
import com.ssy.lingxi.product.api.model.resp.OftenSelectNameResp;
import com.ssy.lingxi.product.api.model.resp.OftenSelectResp;
import com.ssy.lingxi.product.entity.do_.OftenSelectDO;
import com.ssy.lingxi.product.service.shop.IOftenSelectService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 商城--常用筛选管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/9/2
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(ServiceModuleConstant.PRODUCT_PATH_PREFIX + "/shop/oftenSelect")
public class OftenSelectController extends BaseController {
    private final IOftenSelectService oftenSelectService;

    /**
     * 查询常用筛选信息
     * @param id 常用筛选id
     * @return Wrapper<OftenSelect>
     */
    @GetMapping("/getOftenSelect")
    public WrapperResp<OftenSelectResp> getOftenSelect(@RequestParam Long id) {
        OftenSelectDO oftenSelectDO = oftenSelectService.getOftenSelect(id);
        if(oftenSelectDO != null){
            return WrapperUtil.success(this.modelMapper.map(oftenSelectDO, OftenSelectResp.class));
        }else{
            return WrapperUtil.success(null);
        }
    }

    /**
     * 查询常用筛选列表
     * @param pageDataReq 分页实体
     * @return
     */
    @GetMapping("/getOftenSelectList")
    public WrapperResp<PageDataResp<OftenSelectNameResp>> getOftenSelectList(HttpServletRequest request, PageDataReq pageDataReq) {
        UserLoginCacheDTO sysUser = this.getSysUser(request);
        Page<OftenSelectDO> result = oftenSelectService.getOftenSelectList(sysUser, pageDataReq);
        List<OftenSelectNameResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<OftenSelectNameResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 新增/修改常用筛选
     * @param oftenSelectReq 常用筛选实体
     * @return 常用筛选id
     */
    @PostMapping("/saveOrUpdateOftenSelect")
    public WrapperResp<Long> saveOrUpdateOftenSelect(HttpServletRequest request, @RequestBody OftenSelectReq oftenSelectReq){
        UserLoginCacheDTO sysUser = this.getSysUser(request);
        return WrapperUtil.success(oftenSelectService.saveOrUpdateOftenSelect(sysUser, this.modelMapper.map(oftenSelectReq, OftenSelectDO.class)));
    }

    /**
     * 删除常用筛选
     * @param commonIdRequest
     * @return
     */
    @PostMapping("/deleteOftenSelect")
    public WrapperResp<String> deleteOftenSelect(@RequestBody CommonIdReq commonIdRequest){
        return WrapperUtil.success(oftenSelectService.deleteOftenSelect(commonIdRequest.getId()));
    }

}
