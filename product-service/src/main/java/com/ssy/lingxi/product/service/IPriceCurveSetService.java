package com.ssy.lingxi.product.service;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.product.api.model.req.PriceCurveSetReq;
import com.ssy.lingxi.product.api.model.resp.PriceCurveSetResp;

import java.util.List;

/**
 * 价格曲线设置管理类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/10/20
 */
public interface IPriceCurveSetService {

    /**
     * 查询价格曲线设置列表
     * @return 价格曲线设置列表
     */
    List<PriceCurveSetResp> getPriceCurveSetList(UserLoginCacheDTO sysUser);

    /**
     * 新增价格曲线设置
     * @return 是否设置成功
     */
    Boolean savePriceCurveSet(UserLoginCacheDTO sysUser, List<PriceCurveSetReq> priceCurveSetReqList);

    /**
     * 查询是否显示价格曲线
     * @return 是/否显示
     */
    Boolean getPriceCurveSet(Long shopId, Long commodityId);
}
