package com.ssy.lingxi.product.service.warehouse;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.product.api.model.req.StatusUpdateReq;
import com.ssy.lingxi.product.api.model.req.warehouse.WarehouseGroupSaveReq;
import com.ssy.lingxi.product.api.model.resp.warehouse.WarehouseGroupResp;

/**
 * 仓库组接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-05-12
 */
public interface IWarehouseGroupService {

    /**
     * 新增或编辑
     * @param saveReq 参数
     * @param user 用户
     */
    void addOrUpdate(WarehouseGroupSaveReq saveReq, UserLoginCacheDTO user);

    /**
     * 仓库组列表分页查询
     * @param name 参数
     * @param pageDataReq 分页
     * @return 结果
     */
    PageDataResp<WarehouseGroupResp> getListByPage(String name, Integer status, PageDataReq pageDataReq);

    /**
     * 修改状态
     * @param updateReq 参数
     * @param user 用户
     */
    void updateStatus(StatusUpdateReq updateReq, UserLoginCacheDTO user);

    /**
     * 删除
     * @param id ID
     * @param user 用户
     */
    void deleteById(Long id, UserLoginCacheDTO user);

    /**
     * 获取仓库组信息
     * @param id id
     * @return 结果
     */
    WarehouseGroupResp getById(Long id);
}
