package com.ssy.lingxi.product.constant;

/**
 * 送样需求单 - 公共常量
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/6/27 14:48
 */
public class ProductConstant {

    /**
     * 商品导入属性
     */
    public static final String COMMODITY_IMPORT_GROUP_NAME = "商品导入属性";

    /**
     * 商品编码前缀
     */
    public static final String COMMODITY_CODE_PREFIX = "C";

    /**
     * 商品sku编码前缀
     */
    public static final String COMMODITY_SKU_CODE_PREFIX = "S";

    /**
     * 送样需求单单号前缀
     */
    public static final String SAMPLE_DELIVERY_NO_PREFIX = "SY";

    /**
     * 送样需求单单编号数据位长度（SY22020526[00001]）
     */
    public static final Integer SAMPLE_DELIVERY_NO_LENGTH = 5;

    /**
     * 商品仓位库存
     */
    public static final String FREIGHT_SPACE_REDIS_KEY = "commodity_stock_";

    /**
     * 商品前缀
     */
    public static final String SHOP_COMMODITY_PREFIX = "shop_commodity_";

    /**
     * 商城端商品缓存时长(单位：秒)
     */
    public static final Long COMMODITY_EXPIRE = 60 * 60L;

    /**
     * 进货单最大记录数
     */
    public static final Integer PURCHASE_MAX_COUNT = 99;

    /**
     * 品类key
     */
    public static final String PRODUCT_CUSTOMER_CATEGORY_KEY = "product:customer_category_key";

    /**
     * 成色属性对应的属性值key
     */
    public static final String PRODUCT_FINENESS_CUSTOMER_ATTRIBUTE_VALUE_KEY = "product_fineness_customer_attribute_value_key";

    /**
     * 审核类型：0.反审 1.审核
     */
    public interface ReviewType {
        Integer AGAINST_REVIEW = 0;
        Integer REVIEW = 1;
    }

    /**
     * 单据状态：0.未审核 1.已审核
     */
    public interface State {
        Integer NOT_EXAMINE = 0;
        Integer YES_EXAMINE = 1;
    }

}
