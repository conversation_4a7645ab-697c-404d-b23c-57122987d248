//package com.ssy.lingxi.order;
//
//import cn.hutool.json.JSONUtil;
//import com.ssy.lingxi.contract.constant.SignConstant;
//import com.ssy.lingxi.contract.esign.EsignProperties;
//import com.ssy.lingxi.contract.esign.api.AuthToken;
//import com.ssy.lingxi.contract.esign.api.SignApi;
//import com.ssy.lingxi.contract.esign.constant.EsignConstants;
//import com.ssy.lingxi.contract.esign.request.FaceCheckAuthRequest;
//import com.ssy.lingxi.contract.esign.request.FaceRealNameAuthRequest;
//import com.ssy.lingxi.contract.esign.request.PersonalAccountCreateRequest;
//import com.ssy.lingxi.contract.esign.response.*;
//import com.ssy.lingxi.contract.service.esign.EsignCache;
//import com.ssy.lingxi.contract.service.esign.EsignLock;
//import org.junit.jupiter.api.Test;
//
//import java.util.Objects;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.locks.ReentrantLock;
//
///**
// * signAuth api 测试
// * <AUTHOR>
// * @version 2.0.0
// * @since 2021/12/1
// */
//public class SignAuthTest {
//
//    private static SignApi signApi = null;
//
//    static {
//        ReentrantLock reentrantLock = new ReentrantLock();
//        ConcurrentHashMap<String, Object> concurrentHashMap = new ConcurrentHashMap<>();
//        EsignLock esignLock = new EsignLock() {
//
//            @Override
//            public Object lock(String key) {
//                if (reentrantLock.tryLock()) {
//                    return key;
//                }
//
//                return null;
//            }
//
//            @Override
//            public boolean releaseLock(Object obj) {
//                if (Objects.nonNull(obj)) {
//                    reentrantLock.unlock();
//                    return true;
//                }
//
//                return false;
//            }
//        };
//
//        EsignCache esignCache = new EsignCache() {
//            @Override
//            public Object get(String key) {
//                return concurrentHashMap.get(key);
//            }
//
//            @Override
//            public boolean put(String key, Object obj, long expire) {
//                Object put = concurrentHashMap.put(key, obj);
//                return Objects.nonNull(put);
//            }
//        };
//
//
//        EsignProperties esignProperties = new EsignProperties();
//        esignProperties.setAppId("**********");
//        esignProperties.setAppSecret("f0c7a973274fe884ed09b5515324a432");
//        esignProperties.setNotifyUrl("");
//        esignProperties.setSandBox(true);
//
//        AuthToken authToken = new AuthToken(esignLock, esignCache, esignProperties);
//
//        signApi = new SignApi(authToken, esignProperties);
//    }
//
//    /**
//     * 初始化
//     */
//    @Test
//    public void init() throws Exception {
//        // 个人账户信息
////        signApi.account().personalAccountDelete("0b6e9d98e6ed49d5ab0f8bc1f061e364");
//        // 机构账户
//        signApi.account().orgAccountDelete("24d1b0dba2aa4ce99275f25778b3751a");
//    }
//
//    /**
//     * 查询认证主流程明细
//     */
//    @Test
//    public void authFlowDetail() throws Exception {
//        AuthFlowDetailResponse authFlowDetailResponse = signApi.account().authFlowDetail("1666429917371398044");
//        System.out.println(JSONUtil.toJsonStr(authFlowDetailResponse));
//    }
//
//    // ==============================account===============================
//
//    /**
//     * 查询个人账户（按照账户ID查询）
//     */
//    @Test
//    public void personalAccountDetail() throws Exception {
//        PersonalAccountDetailResponse response = signApi.account().personalAccountDetail("40ffde489cde427e81322e336616e482");
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 查询个人账户（按照第三方用户ID查询）
//     */
//    @Test
//    public void personalAccountByThirdId() throws Exception {
//        PersonalAccountDetailResponse response = signApi.account().personalAccountByThirdId("441322199610065213");
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 查询机构账号（按照账号ID查询）
//     */
//    @Test
//    public void orgAccountDetail() throws Exception {
//        OrgAccountDetailResponse response = signApi.account().orgAccountDetail("39b08819801147ef8c39f26f48ae36b4");
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 查询机构账号（按照第三方机构ID查询）
//     */
//    @Test
//    public void orgAccountByThirdId() throws Exception {
//        OrgAccountDetailResponse response = signApi.account().orgAccountByThirdId("91440105065805264E1");
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    // ==============================seal===============================
//
//    /**
//     * 查询个人印章
//     */
//    @Test
//    public void personalSealDetail() throws Exception {
//        SealDetailResponse response = signApi.seal().personalSealDetail("e80410a7b04d426d956a78b34cc0ec96");
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 发起个人刷脸实名认证
//     */
//    @Test
//    public void faceRealNameAuth() throws Exception {
//        FaceRealNameAuthRequest faceRealNameAuthRequest = FaceRealNameAuthRequest.builder()
//                .accountId("4914d788a27e4b1d8a85ef45d3cc76df")
//                .faceauthMode(EsignConstants.faceauthMode.ZHIMACREDIT)
//                .callbackUrl("https://www.baidu.com")
//                .notifyUrl(signApi.getProperties().getNotifyUrl() + SignConstant.faceAuthUri)
//                .build();
//
//        // 发起刷脸认证
//        FaceAuthResponse faceAuthResponse = signApi.personalAuth().faceRealNameAuth(faceRealNameAuthRequest);
//        System.out.println(JSONUtil.toJsonStr(faceAuthResponse));
//    }
//
//    /**
//     * 发起个人刷脸核身认证
//     */
//    @Test
//    public void faceCheckAuth() throws Exception {
//        FaceCheckAuthRequest request = FaceCheckAuthRequest.builder()
//                .name("谢建松")
//                .idNo("******************")
//                .faceauthMode("ZHIMACREDIT")
//                .callbackUrl("https://www.baidu.com")
//                .notifyUrl(signApi.getProperties().getNotifyUrl() + SignConstant.faceAuthUri)
//                .build();
//
//        // 发起刷脸认证
//        FaceAuthResponse faceAuthResponse = signApi.personalAuth().faceCheckAuth(request);
//        System.out.println(JSONUtil.toJsonStr(faceAuthResponse));
//    }
//}
