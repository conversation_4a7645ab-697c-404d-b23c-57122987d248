//package com.ssy.lingxi.order;
//
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONUtil;
//import com.ssy.lingxi.common.exception.BusinessException;
//import com.ssy.lingxi.common.response.globalization.ResponseCode;
//import com.ssy.lingxi.contract.constant.SignConstant;
//import com.ssy.lingxi.contract.esign.EsignProperties;
//import com.ssy.lingxi.contract.esign.api.SignApi;
//import com.ssy.lingxi.contract.esign.constant.EsignConstants;
//import com.ssy.lingxi.contract.esign.exception.EsignException;
//import com.ssy.lingxi.contract.esign.request.*;
//import com.ssy.lingxi.contract.esign.response.*;
//import com.ssy.lingxi.contract.esign.util.ApiUtil;
//import org.junit.jupiter.api.Test;
//
//import java.io.BufferedInputStream;
//import java.io.ByteArrayOutputStream;
//import java.io.File;
//import java.io.FileInputStream;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//
//public class ContractSignTest {
//
//    // {"expiresIn":"1600405395755","token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************.MZCyfVTFkvWzInxazXHx_cGtEeRifq_99w3JU0jBgzg","refreshToken":"7d7170846b27fb072ea99e2b14c18a83"}
//
//    private static SignApi signApi = null;
//
//    static {
//        EsignProperties esignProperties = new EsignProperties();
//        esignProperties.setAppId("7438817714");
//        esignProperties.setAppSecret("f0c7a973274fe884ed09b5515324a432");
//        esignProperties.setNotifyUrl("");
//        esignProperties.setSandBox(true);
//
//        signApi = new SignApi(esignProperties);
//    }
//
//    /**
//     * 流程文档详情
//     * @throws EsignException
//     */
//    @Test
//    public void test2() throws EsignException {
//        String fileId = "********************************";
//        FileDetailResponse fileDetailResponse = signApi.fileTemplate().fileDetail(fileId);
//        System.out.println(JSONUtil.toJsonStr(fileDetailResponse));
//    }
//
//    /**
//     * 流程文档下载
//     * @throws EsignException
//     */
//    @Test
//    public void signFlowDocumentsDownload() throws EsignException {
//        String flowId = "21eaf5c1feaf4bf8b632499116a6ba94";
//
//        SignFlowDocResponse signFlowDocResponse = signApi.signFlow().signFlowDocumentsDownload(flowId);
//        System.out.println(JSONUtil.toJsonStr(signFlowDocResponse));
//    }
//
//    @Test
//    public void test9() throws Exception {
////        // 流程创建
//        SignFlowCreateRequest request = new SignFlowCreateRequest();
//        request.setBusinessScene("购销合同");
//
//        SignFlowResponse signFlowResponse = signApi.signFlow().signFlowCreate(request);
//        System.out.println(JSONUtil.toJsonStr(signFlowResponse));
//
//        String flowId = signFlowResponse.getFlowId();
//
////        // 添加文件
//        String fileId = "8e0ac00bf3944fb083c4faf4fe95724b";
//        SignFlowDocumentsAddRequest.Doc doc = new SignFlowDocumentsAddRequest.Doc();
//        doc.setFileId(fileId);
//
//        SignFlowDocumentsAddRequest signFlowDocumentsAddRequest = SignFlowDocumentsAddRequest.builder()
//                .flowId(signFlowResponse.getFlowId())
//                .docs(Collections.singletonList(doc))
//                .build();
//
//        signApi.signFlow().signFlowDocumentsAdd(signFlowDocumentsAddRequest);
////
////        // 添加平台签署区
//        SignFieldPlatformRequest.Signfield signfieldsBean = new SignFieldPlatformRequest.Signfield();
//        signfieldsBean.setFileId(fileId);
//
//        SignFieldPlatformRequest.Signfield.PosBean posBean = new SignFieldPlatformRequest.Signfield.PosBean();
//        posBean.setPosPage("3");
//        posBean.setPosX(139F);
//        posBean.setPosY(291F);
//        signfieldsBean.setPosBean(posBean);
//
//        SignFieldPlatformRequest signFieldPlatformRequest = SignFieldPlatformRequest.builder()
//                .flowId(signFlowResponse.getFlowId())
//                .signfields(Collections.singletonList(signfieldsBean))
//                .build();
//
//        SignFieldPlatformResponse response = signApi.signFlow().signFieldPlatform(signFieldPlatformRequest);
//
//
////        // 添加手动签署区
//        SignFieldHandSignRequest.Signfield signfieldsBean1 = new SignFieldHandSignRequest.Signfield();
//        signfieldsBean1.setFileId(fileId);
//        signfieldsBean1.setSignerAccountId("80726229255243d183a527d6d2ba7113");
//
//        SignFieldHandSignRequest.Signfield.PosBean posBean1 = new SignFieldHandSignRequest.Signfield.PosBean();
//        posBean1.setPosPage("3");
//        posBean1.setPosX(410F);
//        posBean1.setPosY(275F);
//        signfieldsBean1.setPosBean(posBean1);
//
//
//        SignFieldHandSignRequest signFieldHandSignRequest = SignFieldHandSignRequest.builder()
//                .flowId(flowId)
//                .signfields(Collections.singletonList(signfieldsBean1))
//                .build();
//
//
//        SignFieldAutoResponse response1 = signApi.signFlow().signFieldHandSign(signFieldHandSignRequest);
//
//        // 开启流程
//        signApi.signFlow().signFlowStart(flowId);
//
//        // 获取签署链接
//        ExecuteUrlRequest build = ExecuteUrlRequest.builder()
//                .flowId(flowId)
//                .accountId("80726229255243d183a527d6d2ba7113")
//                .urlType(1)
//                .build();
//        ExecuteUrlResponse executeUrlResponse = signApi.signFlow().executeUrl(build);
//        System.out.println(JSONUtil.toJsonStr(executeUrlResponse));
//    }
//
//    @Test
//    public void test10() throws EsignException {
//        String fileId = "********************************";
//        String fileName = "购销合同.pdf";
//        String scene = "购销合同";
//        String platformSingId = "8d7e22a765f5468b851ce17448e3ee92";
//        String personalAutoSingId = "4b9649a2fb974f0f8f38ae8e2284b06b";
//        String autoSingId = "402b2c6ab92f440e8983d79a48706024";
//
//        // 待签署文档
//        SignFlowCreateOneStepRequest.Doc doc = new SignFlowCreateOneStepRequest.Doc();
//        doc.setFileId(fileId);
//        doc.setFileName(fileName);
//
//
//        // 任务配置信息
//        SignFlowCreateOneStepRequest.FlowInfoBean.FlowConfigInfo flowConfigInfo = new SignFlowCreateOneStepRequest.FlowInfoBean.FlowConfigInfo();
//        flowConfigInfo.setNoticeDeveloperUrl(SignConstant.singFlowFinish);
//        flowConfigInfo.setNoticeType(EsignConstants.NoticeType.SMS);
//
//        // 流程基本信息
//        SignFlowCreateOneStepRequest.FlowInfoBean flowInfoBean = new SignFlowCreateOneStepRequest.FlowInfoBean();
//        flowInfoBean.setAutoArchive(true);
//        flowInfoBean.setAutoInitiate(true);
//        flowInfoBean.setBusinessScene(scene);
//        flowInfoBean.setFlowConfigInfo(flowConfigInfo);
//        flowInfoBean.setInitiatorAuthorizedAccountId(platformSingId);
//
//        // ===============================平台方签署====================================
//
//        // 签署区位置信息
//        SignFlowCreateOneStepRequest.Signer.Signfield.PosBean posBean1 = new SignFlowCreateOneStepRequest.Signer.Signfield.PosBean();
//        posBean1.setPosPage("3");
//        posBean1.setPosX(139F);
//        posBean1.setPosY(291F);
//
//        // 签署文件信息
//        SignFlowCreateOneStepRequest.Signer.Signfield signfield1 = new SignFlowCreateOneStepRequest.Signer.Signfield();
//        signfield1.setAutoExecute(true);
//        signfield1.setActorIndentityType(2);
//        signfield1.setFileId(fileId);
//        signfield1.setPosBean(posBean1);
//
//        // 签署方信息
//        SignFlowCreateOneStepRequest.Signer signer1 = new SignFlowCreateOneStepRequest.Signer();
//        signer1.setPlatformSign(true);
//        signer1.setSignfields(Collections.singletonList(signfield1));
//
//        // ===============================签署方签署====================================
//
//        // 签署区位置信息
//        SignFlowCreateOneStepRequest.Signer.Signfield.PosBean posBean2 = new SignFlowCreateOneStepRequest.Signer.Signfield.PosBean();
//        posBean2.setPosPage("3");
//        posBean2.setPosX(410F);
//        posBean2.setPosY(275F);
//
//        // 签署账号信息
//        SignFlowCreateOneStepRequest.Signer.SignerAccount signerAccount = new SignFlowCreateOneStepRequest.Signer.SignerAccount();
//        signerAccount.setSignerAccountId(personalAutoSingId);
//        signerAccount.setAuthorizedAccountId(autoSingId);
//
//        // 签署文件信息
//        SignFlowCreateOneStepRequest.Signer.Signfield signfield2 = new SignFlowCreateOneStepRequest.Signer.Signfield();
//        signfield2.setAutoExecute(true);
//        signfield2.setActorIndentityType(2);
//        signfield2.setFileId(fileId);
//        signfield2.setPosBean(posBean2);
//
//        // 签署方信息
//        SignFlowCreateOneStepRequest.Signer signer2 = new SignFlowCreateOneStepRequest.Signer();
//        signer2.setPlatformSign(false);
//        signer2.setSignerAccount(signerAccount);
//        signer2.setSignfields(Collections.singletonList(signfield2));
//
//        List<SignFlowCreateOneStepRequest.Signer> signerList = new ArrayList<>();
//        signerList.add(signer1);
//        signerList.add(signer2);
//        SignFlowCreateOneStepRequest oneStepRequest = SignFlowCreateOneStepRequest.builder()
//                .docs(Collections.singletonList(doc))
//                .flowInfo(flowInfoBean)
//                .signers(signerList)
//                .build();
//
//        SignFlowResponse signFlowResponse = signApi.signFlow().signFlowCreateOneStep(oneStepRequest);
//        // b1a9f3f5aea7416e9eac6c669a38f50a
//        System.out.println(JSONUtil.toJsonStr(signFlowResponse));
//    }
//
//
//    /**
//     * 签署流程查询
//     */
//    @Test
//    public void signFlowDetail() throws EsignException {
//        String flowId = "95e057222fff4dbd8300e4dc113da2d4";
//        SignFlowDetailResponse response = signApi.signFlow().signFlowDetail(flowId);
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 查询个人印章
//     */
//    @Test
//    public void personalSealDetail() throws EsignException {
//        String sealId = "7a50fb78ed394ddeb2d08363c342b257";
//        SealDetailResponse response = signApi.seal().personalSealDetail(sealId);
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 查询机构印章
//     */
//    @Test
//    public void organizationSealDetail() throws EsignException {
//        String sealId = "39b08819801147ef8c39f26f48ae36b4";
//        SealDetailResponse response = signApi.seal().organizationSealDetail(sealId);
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 查询签署区列表
//     */
//    @Test
//    public void signFieldDetail() throws EsignException {
//        SignFieldDetailRequest request = new SignFieldDetailRequest();
//        request.setFlowId("c8fdeaa443744948a9a65b75f2c8ffe2");
//        SignFieldDetailResponse response = signApi.signFlow().signFieldDetail(request);
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 查询文档详情
//     */
//    @Test
//    public void fileDetail() throws EsignException {
//        String fileId = "eb0ca83a90fe4da5b10ef416bbb52232";
//        FileDetailResponse response = signApi.fileTemplate().fileDetail(fileId);
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 文档搜索关键字
//     */
//    @Test
//    public void wordsPositionSearch() throws EsignException {
//        WordsPositionSearchRequest request = WordsPositionSearchRequest.builder()
//                .fileId("eb0ca83a90fe4da5b10ef416bbb52232")
//                .keywords("谢建松")
//                .build();
//        List<WordsPositionSearchResponse> response = signApi.helper().wordsPositionSearch(request);
//        System.out.println(JSONUtil.toJsonStr(response));
//    }
//
//    /**
//     * 设置静默签署
//     * @throws EsignException
//     */
//    @Test
//    public void silentSignSet() throws EsignException {
//        String id = "508d6c322f2949ad98dd90cb2795522c";
//
//        // 静默签署
//        SilentSignSetRequest silentSignSetRequest0 = SilentSignSetRequest.builder().accountId(id).build();
//        signApi.account().silentSignSet(silentSignSetRequest0);
//    }
//
//    /**
//     * 撤销静默签署
//     * @throws EsignException
//     */
//    @Test
//    public void silentSignCancel() throws EsignException {
//        // 静默签署
//        signApi.account().silentSignCancel("2bf5dc5fe4ce4da48585bf21e3034a5a");
//    }
//
//
//    /**
//     * 读取本地文件
//     * @param file
//     * @return
//     */
//    private byte[] readLocalFile(File file) {
//        try (
//                BufferedInputStream bis = new BufferedInputStream(new FileInputStream(file));
//                ByteArrayOutputStream baos = new ByteArrayOutputStream();
//        ) {
//            int length;
//            byte[] buffer = new byte[1024];
//            while ((length = bis.read(buffer)) != -1) {
//                baos.write(buffer, 0, length);
//            }
//
//            return baos.toByteArray();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return new byte[0];
//    }
//
//
//    /**
//     * 合同文件上传
//     * @return
//     */
//    private String contractFileUpload(String fileName, byte[] bytes) {
//        // 加签文件数据
//        String md5Base64 = ApiUtil.fileMd5(bytes);
//        FileUploadCreateRequest fileUploadCreateRequest = FileUploadCreateRequest.builder()
//                .contentMd5(md5Base64)
//                .contentType("application/octet-stream")
//                .convert2Pdf(true)
//                .fileName(fileName)
//                .fileSize((long) bytes.length)
//                .build();
//
//        String fileId;
//        try {
//            FileUploadCreateResponse fileUploadCreateResponse = signApi.fileTemplate().fileUploadCreate(fileUploadCreateRequest);
//            fileId = fileUploadCreateResponse.getFileId();
//
//            // 上传文件
//            signApi.fileTemplate().fileUpload(fileUploadCreateResponse.getUploadUrl(), md5Base64, bytes, "application/octet-stream");
//        } catch (EsignException e) {
//            System.out.println("e签宝合同文件上传创建: " + e.getError());
//            throw new BusinessException(ResponseCode.CONTRACT_ESIGN_FAIL);
//        }
//
//        return fileId;
//    }
//
//    /**
//     * 签署流程创建
//     * @return
//     */
//    private String signFlowCreate(String firstAccountId, String firstOrgId, String fileName) {
//        SignFlowCreateRequest signFlowCreateRequest = new SignFlowCreateRequest();
//        signFlowCreateRequest.setInitiatorAccountId(firstAccountId);
//        signFlowCreateRequest.setInitiatorAuthorizedAccountId(firstOrgId);
//
//        SignFlowCreateRequest.ConfigInfoBean configInfoBean = new SignFlowCreateRequest.ConfigInfoBean();
//        // 回调地址
//        configInfoBean.setNoticeDeveloperUrl(signApi.getProperties().getNotifyUrl() + SignConstant.singFlowFinish);
//        configInfoBean.setNoticeType(EsignConstants.NoticeType.SMS);
//
//        // 设置自动归档
//        signFlowCreateRequest.setAutoArchive(true);
//        signFlowCreateRequest.setBusinessScene(StrUtil.subBefore(fileName, ".", false));
//        signFlowCreateRequest.setConfigInfo(configInfoBean);
//
//        String flowId;
//        try {
//            SignFlowResponse signFlowResponse = signApi.signFlow().signFlowCreate(signFlowCreateRequest);
//            flowId = signFlowResponse.getFlowId();
//        } catch (EsignException e) {
//            System.out.println("e签宝创建签署流程: " + e.getError());
//            throw new BusinessException(ResponseCode.CONTRACT_ESIGN_FAIL);
//        }
//
//        return flowId;
//    }
//
//    private void flowBindFile(String flowId, String fileId) {
//        SignFlowDocumentsAddRequest.Doc doc = new SignFlowDocumentsAddRequest.Doc();
//        doc.setFileId(fileId);
//
//        SignFlowDocumentsAddRequest request = SignFlowDocumentsAddRequest.builder()
//                .flowId(flowId)
//                .docs(Collections.singletonList(doc))
//                .build();
//
//        try {
//            signApi.signFlow().signFlowDocumentsAdd(request);
//        } catch (EsignException e) {
//            System.out.println("e签宝签署流添加文档: " + e.getError());
//            throw new BusinessException(ResponseCode.CONTRACT_ESIGN_FAIL);
//        }
//    }
//
//    private void addHandSignField(String flowId, String fileId, String accountId, String orgId) {
//        SignFieldHandSignRequest.Signfield signfield = new SignFieldHandSignRequest.Signfield();
//        signfield.setSignerAccountId(accountId);
//        signfield.setAuthorizedAccountId(orgId);
//        signfield.setFileId(fileId);
//        signfield.setSignType(0);
//        SignFieldHandSignRequest request = SignFieldHandSignRequest.builder()
//                .flowId(flowId)
//                .signfields(Arrays.asList(signfield))
//                .build();
//        try {
//            SignFieldAutoResponse signFieldAutoResponse = signApi.signFlow().signFieldHandSign(request);
//        } catch (EsignException e) {
//            System.out.println("e签宝签署添加手动签署区: {}" + e.getError());
//            throw new BusinessException(ResponseCode.CONTRACT_ESIGN_FAIL);
//        }
//    }
//
//    private String getSignUrl(String flowId, String firstAccountId, String firstOrgId) {
//        ExecuteUrlRequest request = ExecuteUrlRequest.builder()
//                .flowId(flowId)
//                .accountId(firstAccountId)
//                .organizeId(firstOrgId)
//                .build();
//
//        String url;
//        try {
//            ExecuteUrlResponse response = signApi.signFlow().executeUrl(request);
//            url = response.getUrl();
//        } catch (EsignException e) {
//            System.out.println("e签宝获取签署地址: {}" + e.getError());
//            throw new BusinessException(ResponseCode.CONTRACT_ESIGN_FAIL);
//        }
//
//        return url;
//    }
//
//    private void signFlowStart(String flowId) {
//        try {
//            signApi.signFlow().signFlowStart(flowId);
//        } catch (EsignException e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void fileStatus(String fileId) {
//        try {
//            Integer status = EsignConstants.fileStatus.upload_none;
//            while (status != EsignConstants.fileStatus.file_convert_complete) {
//                FileStatusResponse fileStatusResponse = signApi.fileTemplate().fileStatus(fileId);
//                System.out.println("查询文件状态....状态值为: " + fileStatusResponse.getStatus());
//                status = fileStatusResponse.getStatus();
//                Thread.sleep(1000);
//            }
//
//        } catch (EsignException e) {
//            e.printStackTrace();
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//    }
//
//    @Test
//    public void test() {
//        File file = new File("D://购销合同-填充.doc");
//
//        String firstAccountId = "40ffde489cde427e81322e336616e482";
//        String firstOrgId = "2bf5dc5fe4ce4da48585bf21e3034a5a";
//
//        String secondAccountId = "";
//        String secondOrgId = "";
//
//        String contractName = file.getNameByCode();
//
//
//        byte[] bytes = readLocalFile(file);
//        // =================================合同文件上传创建=================================
//        String fileId = contractFileUpload(file.getNameByCode(), bytes);
//
//        // =================================创建签署流程=================================
//        String flowId = signFlowCreate(firstAccountId, firstOrgId, file.getNameByCode());
//
//        // =================================流程文档添加=================================
//        fileStatus(fileId);
//        flowBindFile(flowId, fileId);
//
//        // =================================添加签署区=================================
//        addHandSignField(flowId, fileId, firstAccountId, firstOrgId);
//        addHandSignField(flowId, fileId, secondAccountId, secondOrgId);
//
//        // =================================流程开启=================================
//        signFlowStart(flowId);
//
//        // =================================获取签署地址=================================
//        String firstSignUrl = getSignUrl(flowId, firstAccountId, firstOrgId);
//        String secondSignUrl = getSignUrl(flowId, secondAccountId, secondOrgId);
//
//
//
//
//        // 保存签章记录
//        System.out.println("fileId: " + fileId);
//        System.out.println("flowId: " + flowId);
//        System.out.println("firstSignUrl: " + firstSignUrl);
//        System.out.println("secondSignUrl: " + secondSignUrl);
//    }
//
//}
