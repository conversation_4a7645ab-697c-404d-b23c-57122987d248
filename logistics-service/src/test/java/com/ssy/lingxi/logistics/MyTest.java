//package com.ssy.lingxi.logistics;
//
//import com.ssy.lingxi.logistics.model.enums.*;
//import com.ssy.lingxi.report.api.enums.CommonBooleanEnum;
//import org.junit.jupiter.api.Test;
//
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.util.ArrayList;
//import java.util.List;
//
//public class MyTest {
//
//    @Test
//    public void test() throws IOException {
//        String filepath = "d:/base_messages.properties";
//
//        List<String> content = new ArrayList<>();
//        for (CommonBooleanEnum value : CommonBooleanEnum.values()) {
//            String key = value.getClass().getName();
//            content.add(key.substring(key.lastIndexOf(".") + 1).concat(".").concat(String.valueOf(value.getCode())).concat("=").concat(value.getNameByCode()));
//        }
//
//        content.add("");
//
//
//        for (CommonStatusEnum value : CommonStatusEnum.values()) {
//            String key = value.getClass().getName();
//            content.add(key.substring(key.lastIndexOf(".") + 1).concat(".").concat(String.valueOf(value.getCode())).concat("=").concat(value.getNameByCode()));
//        }
//
//        content.add("");
//
//
//        for (CooperateTypeEnum value : CooperateTypeEnum.values()) {
//            String key = value.getClass().getName();
//            content.add(key.substring(key.lastIndexOf(".") + 1).concat(".").concat(String.valueOf(value.getCode())).concat("=").concat(value.getNameByCode()));
//        }
//
//        content.add("");
//
//        for (LogisticsOrderCreateTypeEnum value : LogisticsOrderCreateTypeEnum.values()) {
//            String key = value.getClass().getName();
//            content.add(key.substring(key.lastIndexOf(".") + 1).concat(".").concat(String.valueOf(value.getCode())).concat("=").concat(value.getNameByCode()));
//        }
//
//        content.add("");
//
//        for (LogisticsOrderStatusEnum value : LogisticsOrderStatusEnum.values()) {
//            String key = value.getClass().getName();
//            content.add(key.substring(key.lastIndexOf(".") + 1).concat(".").concat(String.valueOf(value.getCode())).concat("=").concat(value.getNameByCode()));
//        }
//
//        content.add("");
//
//        for (OrderLogisticsTypeEnum value : OrderLogisticsTypeEnum.values()) {
//            String key = value.getClass().getName();
//            content.add(key.substring(key.lastIndexOf(".") + 1).concat(".").concat(String.valueOf(value.getCode())).concat("=").concat(value.getName()));
//        }
//
//        content.add("");
//
//        for (PricingModeEnum value : PricingModeEnum.values()) {
//            String key = value.getClass().getName();
//            content.add(key.substring(key.lastIndexOf(".") + 1).concat(".").concat(String.valueOf(value.getCode())).concat("=").concat(value.getNameByCode()));
//        }
//
//        content.add("");
//
//        for (ReceiverAddressCreateTypeEnum value : ReceiverAddressCreateTypeEnum.values()) {
//            String key = value.getClass().getName();
//            content.add(key.substring(key.lastIndexOf(".") + 1).concat(".").concat(String.valueOf(value.getCode())).concat("=").concat(value.getNameByCode()));
//        }
//
//        content.add("");
//
//        for (TransportModeEnum value : TransportModeEnum.values()) {
//            String key = value.getClass().getName();
//            content.add(key.substring(key.lastIndexOf(".") + 1).concat(".").concat(String.valueOf(value.getCode())).concat("=").concat(value.getNameByCode()));
//        }
//
//        content.add("");
//
//
//
//        Files.write(Paths.get(filepath), content, StandardCharsets.UTF_8);
//    }
//}
