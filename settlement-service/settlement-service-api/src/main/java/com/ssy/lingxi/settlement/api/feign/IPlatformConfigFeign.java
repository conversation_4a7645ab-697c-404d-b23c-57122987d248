package com.ssy.lingxi.settlement.api.feign;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.settlement.api.fallback.factory.PlatformConfigFeignFallbackFactory;
import com.ssy.lingxi.settlement.api.model.req.MemberSettlementStrategyReq;
import com.ssy.lingxi.settlement.api.model.resp.MemberSettlementStrategyResp;
import com.ssy.lingxi.settlement.api.model.resp.PlatformAccountConfigDetailResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 结算服务平台配置Feign接口
 * <AUTHOR>
 * @since 2020/11/19
 * @version 2.0.0
 */
@FeignClient(name = ServiceModuleConstant.SETTLEMENT_SERVICE, fallbackFactory = PlatformConfigFeignFallbackFactory.class)
public interface IPlatformConfigFeign {
    
    String PATH_PREFIX = ServiceModuleConstant.SETTLEMENT_FEIGN_PATH_PREFIX +  "/platform/config/";
    
    /**
     * 获取平台对公账户配置
     * @return 查询结果
     */
    @PostMapping(value = PATH_PREFIX + "getPlatformAccountConfig")
    WrapperResp<PlatformAccountConfigDetailResp> getPlatformAccountConfig();

    /**
     * 获取会员有效结算策略(账期、月结)
     * @return 查询结果（如下级会员未开通账期、月结则返回的data为Null）
     */
    @PostMapping(value = PATH_PREFIX + "getMemberSettlementStrategy")
    WrapperResp<MemberSettlementStrategyResp> getMemberSettlementStrategy(@RequestBody @Valid MemberSettlementStrategyReq memberSettlementStrategyReq);
}
