package com.ssy.lingxi.settlement.api.feign;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.settlement.api.fallback.factory.SettlementProductFeignFallbackFactory;
import com.ssy.lingxi.settlement.api.model.req.SettlementOrderProductReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 2.06.18
 * @since 2022-06-16
 */
@FeignClient(name = ServiceModuleConstant.SETTLEMENT_SERVICE, fallbackFactory = SettlementProductFeignFallbackFactory.class)
public interface ISettlementProductFeign {

    String PATH_PREFIX = ServiceModuleConstant.SETTLEMENT_FEIGN_PATH_PREFIX +  "/settlement/product/";

    /**
     * 根据订单号、收货单号，物料编码修改验退数量
     */
    @PostMapping(value = PATH_PREFIX + "updateQuantity")
    WrapperResp<Void> updateQuantity(@Valid @RequestBody List<SettlementOrderProductReq> orderProductVOs);
}
