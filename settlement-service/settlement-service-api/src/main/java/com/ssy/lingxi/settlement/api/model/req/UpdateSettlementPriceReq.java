package com.ssy.lingxi.settlement.api.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 待对账商品修改单价VO
 * <AUTHOR>
 * @version 2.07.18
 * @since 2022-07-04
 */
public class UpdateSettlementPriceReq implements Serializable {
    private static final long serialVersionUID = 5690041562637282893L;

    /**
     * 订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String billNo;

    /**
     * 收货单号
     */
    @NotBlank(message = "收货单号不能为空")
    private String receiveNo;

    /**
     * 物料编码
     */
    @NotBlank(message = " 物料编码不能为空")
    private String productNo;

    /**
     * 单价
     */
    @NotNull(message = " 单价不能为空")
    private BigDecimal price;

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getReceiveNo() {
        return receiveNo;
    }

    public void setReceiveNo(String receiveNo) {
        this.receiveNo = receiveNo;
    }

    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Override
    public String toString() {
        return "UpdateSettlementPriceVO{" +
                "billNo='" + billNo + '\'' +
                ", receiveNo='" + receiveNo + '\'' +
                ", productNo='" + productNo + '\'' +
                ", price=" + price +
                '}';
    }
}
