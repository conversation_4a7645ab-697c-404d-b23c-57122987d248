package com.ssy.lingxi.settlement.api.enums;


import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;

/**
 * 结算服务内部消息队列类型
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/17 17:33
 */
public enum SettleAccountQueueMessageTypeEnum {

    /**
     * 待对账 - 1
     */
    TO_RECONCILIATION(1, "待对账"),
    /**
     * 平台优惠券结算 - 2
     */
    PLATFORM_COUPON_SETTLEMENT(2, "平台优惠券结算"),
    /**
     * 未知类型
     */
    UNKNOWN(99, "未知");


    SettleAccountQueueMessageTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private final Integer code;
    private final String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return LanguageHolder.getTranslation(this.getClass(), this.name, this.code);
    }

    public static SettleAccountQueueMessageTypeEnum parse(Integer code) {
        return code == null ? null : Arrays.stream(SettleAccountQueueMessageTypeEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(UNKNOWN);
    }
}
