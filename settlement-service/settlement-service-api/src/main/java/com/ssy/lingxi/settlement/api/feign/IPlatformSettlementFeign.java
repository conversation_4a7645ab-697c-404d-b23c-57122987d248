package com.ssy.lingxi.settlement.api.feign;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.settlement.api.fallback.factory.PlatformSettlementFeignFallbackFactory;
import com.ssy.lingxi.settlement.api.model.resp.PlatformSettlementAllInPayResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 结算服务平台Feign接口
 * <AUTHOR>
 * @since 2021/12/21
 * @version 2.0.0
 */
@FeignClient(name = ServiceModuleConstant.SETTLEMENT_SERVICE, fallbackFactory = PlatformSettlementFeignFallbackFactory.class)
public interface IPlatformSettlementFeign {

    String PATH_PREFIX = ServiceModuleConstant.SETTLEMENT_FEIGN_PATH_PREFIX +  "/platform/settlement/";

    @GetMapping(value = PATH_PREFIX + "getPlatformSettlement")
    WrapperResp<PlatformSettlementAllInPayResp> getPlatformSettlementByDetailPaymentId(@RequestParam("paymentId") Long paymentId);
}
