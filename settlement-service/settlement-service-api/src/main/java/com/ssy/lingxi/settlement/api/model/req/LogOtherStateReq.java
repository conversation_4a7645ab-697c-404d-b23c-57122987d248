package com.ssy.lingxi.settlement.api.model.req;

/**
 * 外部流转图实体
 */
public class LogOtherStateReq {

    /**
     *需求单状态
     */
    private Integer state;

    /**
     *状态
     */
    private String stateName;
    /**
     *角色名字
     */
    private String roleName;

    /**
     *此流程是否已经执行：1.是 0.否
     */
    private Integer isExecute;

    /**
     *需求单操作流程
     */
    private String operationalProcess;

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getStateName() {
        return stateName;
    }

    public void setStateName(String stateName) {
        this.stateName = stateName;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getIsExecute() {
        return isExecute;
    }

    public void setIsExecute(Integer isExecute) {
        this.isExecute = isExecute;
    }

    public String getOperationalProcess() {
        return operationalProcess;
    }

    public void setOperationalProcess(String operationalProcess) {
        this.operationalProcess = operationalProcess;
    }
}
