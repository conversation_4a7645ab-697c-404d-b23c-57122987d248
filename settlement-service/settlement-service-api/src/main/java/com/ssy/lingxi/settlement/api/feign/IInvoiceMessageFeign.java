package com.ssy.lingxi.settlement.api.feign;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.settlement.api.fallback.factory.InvoiceMessageFeignFallbackFactory;
import com.ssy.lingxi.settlement.api.model.req.InvoiceMessageListReq;
import com.ssy.lingxi.settlement.api.model.req.InvoiceProveGetReq;
import com.ssy.lingxi.settlement.api.model.resp.InvoiceMessageResp;
import com.ssy.lingxi.settlement.api.model.resp.InvoiceProveResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = ServiceModuleConstant.SETTLEMENT_SERVICE, fallbackFactory = InvoiceMessageFeignFallbackFactory.class)
public interface IInvoiceMessageFeign {

    String PATH_PREFIX = ServiceModuleConstant.SETTLEMENT_FEIGN_PATH_PREFIX +  "/invoice/message/";
    
    /**
    * 结算规则配置 发票信息详情查询
    * <AUTHOR>
    * @since 2020/10/28
    */
    @GetMapping(value = PATH_PREFIX + "details/inside")
    WrapperResp<InvoiceMessageResp> invoiceMessageDetails(@RequestParam("id") Long id);

    /**
     * 根据会员角色ids,查询会员默认发票
     * @param memberList   参数
     */
    @PostMapping(value = PATH_PREFIX + "member/list")
    WrapperResp<List<InvoiceMessageResp>> getMemberInvoiceMessageList(@RequestBody @Valid InvoiceMessageListReq memberList);

    /**
    * 根据单据编号+结算类型，获取单据[发票凭证]信息列表
    * <AUTHOR>
    * @since 2021/4/12
    */
    @GetMapping(value = PATH_PREFIX + "getInvoiceProve")
    WrapperResp<List<InvoiceProveResp>> getInvoiceProve(@RequestBody @Valid InvoiceProveGetReq id);
}
