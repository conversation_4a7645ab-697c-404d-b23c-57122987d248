package com.ssy.lingxi.settlement.api.enums;

import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;

/**
 * 请款单明细单据类型枚举
 * <AUTHOR>
 * @since 2021/12/19
 * @version 2.0.0
 */
public enum ApplyAmountRowBillTypeEnum {

    /**
     * 订单
     */
    ORDER(1, "订单"),
    /**
     * 合同
     */
    CONTRACT(2, "合同"),
    /**
     * 退货申请
     */
    RETURN_REQUEST(3, "收货退货"),

    ;

    /**
     * 值
     */
    private final int code;

    /**
     * 标签
     */
    private final String name;

    ApplyAmountRowBillTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return LanguageHolder.getTranslation(this.getClass(), this.name, this.code);
    }

    public static String getNameByCode(int code) {
        ApplyAmountRowBillTypeEnum billTypeEnum = Arrays.stream(ApplyAmountRowBillTypeEnum.values()).filter(f -> f.getCode() == code).findFirst().orElse(null);
        return billTypeEnum == null ? SettlementHelpEnum.UNKNOWN.getMessage() : billTypeEnum.getName();
    }
}
