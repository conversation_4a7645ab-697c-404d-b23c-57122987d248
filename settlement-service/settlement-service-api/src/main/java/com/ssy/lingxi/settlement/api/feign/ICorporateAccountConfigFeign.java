package com.ssy.lingxi.settlement.api.feign;


import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.settlement.api.fallback.factory.CorporateAccountConfigFeignFallbackFactory;
import com.ssy.lingxi.settlement.api.model.resp.CorporateAccountConfigResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ServiceModuleConstant.SETTLEMENT_SERVICE, fallbackFactory = CorporateAccountConfigFeignFallbackFactory.class)
public interface ICorporateAccountConfigFeign {

    String PATH_PREFIX = ServiceModuleConstant.SETTLEMENT_FEIGN_PATH_PREFIX +  "/corporate/account/";

    /**
     * 结算能力-结算规则配置-对公账户配置
     * <AUTHOR>
     * @since 2020/8/25
     */
    @GetMapping(value = PATH_PREFIX + "config")
    WrapperResp<CorporateAccountConfigResp> corporateAccountConfig(@RequestParam("memberId")Long memberId, @RequestParam("memberRoleId")Long memberRoleId);

}
