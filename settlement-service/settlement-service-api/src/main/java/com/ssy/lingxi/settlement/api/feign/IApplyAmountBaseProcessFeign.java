package com.ssy.lingxi.settlement.api.feign;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.req.engine.DeleteBaseProcessReq;
import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.req.engine.ProcessEngineReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.settlement.api.fallback.factory.ApplyAmountBaseProcessFeignFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 请款单基础流程
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-05-31
 **/
@FeignClient(name = ServiceModuleConstant.SETTLEMENT_SERVICE, fallbackFactory = ApplyAmountBaseProcessFeignFallbackFactory.class)
public interface IApplyAmountBaseProcessFeign {

    String PATH_PREFIX = ServiceModuleConstant.SETTLEMENT_FEIGN_PATH_PREFIX +  "/accounts/process/";
    
    /**
     * 保存基础流程
     * @param baseProcess 基础流程
     * @return Void
     */
    @PostMapping(value = PATH_PREFIX + "saveBaseProcess")
    WrapperResp<Void> saveBaseProcess(@RequestBody @Valid ProcessEngineReq baseProcess);

    /**
     * 删除基础流程
     * @param deleteDTO 基础流程
     * @return Void
     */
    @PostMapping(value = PATH_PREFIX + "deleteBaseProcess")
    WrapperResp<Void> deleteBaseProcess(@RequestBody @Valid DeleteBaseProcessReq deleteDTO);


    /**
     * 查询会员物料流程
     * @param engineRuleQueryReq 查询流程
     * @return ProcessEngineRuleVO
     */
    @PostMapping(value = PATH_PREFIX + "getMemberProcess")
    WrapperResp<List<ProcessEngineRuleResp>> getMemberProcess(@RequestBody @Valid EngineRuleQueryReq engineRuleQueryReq);

}
