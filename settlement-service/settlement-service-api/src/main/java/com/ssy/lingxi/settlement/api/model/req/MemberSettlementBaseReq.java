package com.ssy.lingxi.settlement.api.model.req;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 通用会员结算实体
 * <AUTHOR>
 * @since 2022/1/5
 * @version 2.0.0
 */
public class MemberSettlementBaseReq implements Serializable {
    private static final long serialVersionUID = -222051197730460167L;

    /**
     * id, 新增时候不用传递、修改是必须传递
     */
    private Long id;
    /**
     * 结算会员id
     */
    private Long memberId;
    /**
     * 结算角色id
     */
    private Long roleId;
    /**
     * 结算方
     */
    private String settlementName;
    /**
     * 付款会员id
     */
    private Long payMemberId;
    /**
     * 付款角色id
     */
    private Long payRoleId;
    /**
     * 支付方
     */
    private String payName;
    /**
     * 结算方式:1.账期 2.月结
     */
    private Integer settlementWay;
    /**
     * 结算单据类型:1.生成通知单 2.物流单 3.订单 4.积分订单 5.退货申请单 6.请款单
     * 来源枚举： SettlementOrderTypeEnum
     */
    private Integer orderType;
    /**
     * 结算状态
     */
    private Integer status;
    /**
     * 开始时间
     */
    private Long startTime;
    /**
     * 结束时间
     */
    private Long endTime;
    /**
     * 总单数
     */
    private Long totalCount;
    /**
     * 结算金额
     */
    private BigDecimal amount;
    /**
     * 结算日期
     */
    private Long settlementDate;
    /**
     * 结算时间
     */
    private Long settlementTime;
    /**
     * 预付款时间
     */
    private Long prePayTime;
    /**
     * 支付方式：1.线上支付 2.线下 3.授信 4.货到付款
     */
    private Integer payWay;
    /**
     *
     */
    private Long strategyId;

    private List<PayProveBaseReq> payProveList;
    /**
     * 结算单据信息
     */
    private SettlementOrderBaseReq orderVO;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public String getSettlementName() {
        return settlementName;
    }

    public void setSettlementName(String settlementName) {
        this.settlementName = settlementName;
    }

    public Long getPayMemberId() {
        return payMemberId;
    }

    public void setPayMemberId(Long payMemberId) {
        this.payMemberId = payMemberId;
    }

    public Long getPayRoleId() {
        return payRoleId;
    }

    public void setPayRoleId(Long payRoleId) {
        this.payRoleId = payRoleId;
    }

    public String getPayName() {
        return payName;
    }

    public void setPayName(String payName) {
        this.payName = payName;
    }

    public Integer getSettlementWay() {
        return settlementWay;
    }

    public void setSettlementWay(Integer settlementWay) {
        this.settlementWay = settlementWay;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(Long settlementDate) {
        this.settlementDate = settlementDate;
    }

    public Long getSettlementTime() {
        return settlementTime;
    }

    public void setSettlementTime(Long settlementTime) {
        this.settlementTime = settlementTime;
    }

    public Long getPrePayTime() {
        return prePayTime;
    }

    public void setPrePayTime(Long prePayTime) {
        this.prePayTime = prePayTime;
    }

    public Integer getPayWay() {
        return payWay;
    }

    public void setPayWay(Integer payWay) {
        this.payWay = payWay;
    }

    public Long getStrategyId() {
        return strategyId;
    }

    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    public List<PayProveBaseReq> getPayProveList() {
        return payProveList;
    }

    public void setPayProveList(List<PayProveBaseReq> payProveList) {
        this.payProveList = payProveList;
    }

    public SettlementOrderBaseReq getOrderVO() {
        return orderVO;
    }

    public void setOrderVO(SettlementOrderBaseReq orderVO) {
        this.orderVO = orderVO;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
