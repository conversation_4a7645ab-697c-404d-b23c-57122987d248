package com.ssy.lingxi.settlement.api.model.req;
/**
* 添加发票信息
* <AUTHOR>
* @since 2020/8/25
*/
public class InvoiceMessageAddReq {

    /**
     * 发票种类:1.增值税普通发票（默认） 2.增值税专用发票
     */
    private Integer kind=1;


    /**
     * 发票类型:1.企业（默认） 2.个人
     */
    private Integer type=1;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 纳税号
     */
    private String taxNo;

    /**
     * 开户行
     */
    private String bankOfDeposit ;

    /**
     * 账号
     */
    private String account;

    /**
     * 地址
     */
    private String address;

    /**
     * 电话
     */
    private String tel;


    /**
     * 默认: 1.是 0.否
     */
    private Integer isDefault;

    /**
     * 会员ID
     */
    private Long memberId;

    public Integer getKind() {
        return kind;
    }

    public void setKind(Integer kind) {
        this.kind = kind;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getTaxNo() {
        return taxNo;
    }

    public void setTaxNo(String taxNo) {
        this.taxNo = taxNo;
    }

    public String getBankOfDeposit() {
        return bankOfDeposit;
    }

    public void setBankOfDeposit(String bankOfDeposit) {
        this.bankOfDeposit = bankOfDeposit;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String addres) {
        this.address = addres;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }
}
