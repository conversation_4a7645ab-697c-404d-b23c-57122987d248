package com.ssy.lingxi.settlement.api.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *  对公账户查询
 * <AUTHOR>
 * @since 2021/9/1
 * @version 2.0.0
 */
@Data
public class CorporateAccountDetailReq implements Serializable {

    private static final long serialVersionUID = -6325678850051068205L;
    /**
     * 账户类型： 1=平台对公，2=会员对公
     */
    @NotNull(message = "对公账户类型必填")
    private Integer type;

    /**
     * 账户名称
     */
    private Long memberId;

    /**
     * 银行账号
     */
    private Long roleId;

}
