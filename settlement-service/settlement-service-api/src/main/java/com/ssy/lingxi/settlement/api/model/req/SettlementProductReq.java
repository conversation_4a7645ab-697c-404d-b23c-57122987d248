package com.ssy.lingxi.settlement.api.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.PositiveOrZero;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 待对账单接口商品参数
 * <AUTHOR>
 * @since 2021/12/15
 * @version 2.0.0
 */
public class SettlementProductReq implements Serializable {

    private static final long serialVersionUID = 5967833488147524117L;

    /**
     * 付款方式：
     * 定义在 PurchaseContractPayTypeEnum 中
     */
    private Integer payWay;

    /**
     * 付款方式参数-月：付款方式为账期-月，显示月数
     */
    private Integer payMonth;

    /**
     * 付款方式参数-日：付款方式为账期-月或者账期-天，显示日期或天数
     */
    private Integer payDate;

    /**
     * 物料编码
     */
    @NotBlank(message = "物料编码不能为空")
    private String productNo;

    /**
     * 物料名称
     */
    @NotBlank(message = "物料名称不能为空")
    private String name;

    /**
     * 商品规格
     */
    //@NotBlank(message = "商品规格不能为空")
    private String spec;

    /**
     * 商品品类
     */
    //@NotBlank(message = "商品品类不能为空")
    private String category;

    /**
     * 商品品牌
     */
    //@NotBlank(message = "商品品牌不能为空")
    private String brand;

    /**
     * 单位
     */
    //@NotBlank(message = "单位不能为空")
    private String unit;

    /**
     * 税率（百分比的分子部分）
     */
    //@NotNull(message = "税率要大于等于0")
    @PositiveOrZero(message = "税率要大于等于0")
    private BigDecimal taxRate;

    /**
     * 单价（含税）
     */
    @NotNull(message = "单价要大于等于0")
    @PositiveOrZero(message = "单价要大于等于0")
    private BigDecimal price;

    /**
     * 收货数量
     */
    @NotNull(message = "收货数量要大于等于0")
    @PositiveOrZero(message = "收货数量要大于等于0")
    private BigDecimal receiveQuantity;

    /**
     * 验退数量
     */
    private BigDecimal rejectCount;

    public Integer getPayWay() {
        return payWay;
    }

    public void setPayWay(Integer payWay) {
        this.payWay = payWay;
    }

    public Integer getPayMonth() {
        return payMonth;
    }

    public void setPayMonth(Integer payMonth) {
        this.payMonth = payMonth;
    }

    public Integer getPayDate() {
        return payDate;
    }

    public void setPayDate(Integer payDate) {
        this.payDate = payDate;
    }

    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getReceiveQuantity() {
        return receiveQuantity;
    }

    public void setReceiveQuantity(BigDecimal receiveQuantity) {
        this.receiveQuantity = receiveQuantity;
    }

    public BigDecimal getRejectCount() {
        return rejectCount;
    }

    public void setRejectCount(BigDecimal rejectCount) {
        this.rejectCount = rejectCount;
    }
}
