package com.ssy.lingxi.settlement.api.model.req;

import com.alibaba.fastjson2.annotation.JSONField;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 待对账单接口订单参数
 * <AUTHOR>
 * @since 2021/12/15
 * @version 2.0.0
 */
public class SettlementOrderReq implements Serializable {
    private static final long serialVersionUID = -7023329810734263865L;

    /**
     * 单据id，可以是订单id，或者售后id
     */
    @NotNull
    @PositiveOrZero(message = "单据id要大于0")
    private Long billId;

    /**
     * 单据编号，可以是订单编号，或者售后编号
     */
    @NotBlank
    @Size(max = 50, message = "单据编号最长50个字符")
    private String billNo;

    /**
     * 买方（采购）会员Id
     */
    @NotNull(message = "买方（采购）会员Id要大于0")
    @Positive(message = "买方（采购）会员Id要大于0")
    private Long buyerMemberId;

    /**
     * 买方（采购）会员角色Id
     */
    @NotNull(message = "买方（采购）会员角色Id要大于0")
    @Positive(message = "买方（采购）会员角色Id要大于0")
    private Long buyerRoleId;

    /**
     * 卖方（供应）会员Id
     */
    @NotNull(message = "卖方（供应）会员Id要大于0")
    @Positive(message = "卖方（供应）会员Id要大于0")
    private Long vendorMemberId;

    /**
     * 卖方（供应）会员角色Id
     */
    @NotNull(message = "卖方（供应）会员角色Id要大于0")
    @Positive(message = "卖方（供应）会员角色Id要大于0")
    private Long vendorRoleId;

    /**
     * 收款方
     */
    @NotBlank(message = "收款方不能为空")
    private String payee;

    /**
     * 付款方
     */
    @NotBlank(message = "付款方不能为空")
    private String payer;

    /**
     * 发货批次
     */
    @NotNull(message = "发货批次不能为空")
    private Integer deliveryBatch;

    /**
     * 发货单号
     */
    @NotBlank(message = "发货单号不能为空")
    private String deliveryNo;

    /**
     * 发货时间，格式为"yyyy-MM-dd HH:mm:ss"
     */
    @NotNull(message = "发货时间不能为空")
    private Long deliveryTime;

    /**
     * 收货单号
     */
    @NotBlank(message = "收货单号不能为空")
    private String receiveNo;

    /**
     * 收货时间，格式为"yyyy-MM-dd HH:mm:ss"
     */
    @NotNull(message = "收货时间不能为空")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Long receiveTime;

    /**
     * 订单状态
     */
    @NotBlank(message = "订单状态不能为空")
    private String orderStatus;

    /**
     * 订单类型，定义在ApplyAmountTypeEnum中
     */
    @NotNull(message = "订单类型不能为空")
    @PositiveOrZero(message = "订单类型要大于等于0")
    private Integer orderType;

    /**
     * 单据类型 定义在 ApplyAmountRowBillTypeEnum 中
     */
    @NotNull(message = "单据类型不能为空")
    private Integer billType;

    /**
     * 币别
     * 定义在 CurrencyTypeEnum 中
     */
    private Integer currencyType;

    /**
     * 单据金额
     */
    @NotNull(message = "单据金额不能为空")
    private BigDecimal billAmount;

    /**
     * 单据日期
     */
    @NotNull(message = "单据日期不能为空")
    private Long billDate;

    /**
     * 单据摘要
     */
    private String billAbstract;

    /**
     * 来源合同id
     */
    private Long sourceContractId;

    /**
     * 一对多双向关联结算商品明细
     */
    @Valid
    @NotEmpty(message = "结算商品明细信息必填")
    private List<SettlementProductReq> products;

    public Long getBillId() {
        return billId;
    }

    public void setBillId(Long billId) {
        this.billId = billId;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public Long getBuyerMemberId() {
        return buyerMemberId;
    }

    public void setBuyerMemberId(Long buyerMemberId) {
        this.buyerMemberId = buyerMemberId;
    }

    public Long getBuyerRoleId() {
        return buyerRoleId;
    }

    public void setBuyerRoleId(Long buyerRoleId) {
        this.buyerRoleId = buyerRoleId;
    }

    public Long getVendorMemberId() {
        return vendorMemberId;
    }

    public void setVendorMemberId(Long vendorMemberId) {
        this.vendorMemberId = vendorMemberId;
    }

    public Long getVendorRoleId() {
        return vendorRoleId;
    }

    public void setVendorRoleId(Long vendorRoleId) {
        this.vendorRoleId = vendorRoleId;
    }

    public String getPayee() {
        return payee;
    }

    public void setPayee(String payee) {
        this.payee = payee;
    }

    public String getPayer() {
        return payer;
    }

    public void setPayer(String payer) {
        this.payer = payer;
    }

    public Integer getDeliveryBatch() {
        return deliveryBatch;
    }

    public void setDeliveryBatch(Integer deliveryBatch) {
        this.deliveryBatch = deliveryBatch;
    }

    public String getDeliveryNo() {
        return deliveryNo;
    }

    public void setDeliveryNo(String deliveryNo) {
        this.deliveryNo = deliveryNo;
    }


    public Long getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Long deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public String getReceiveNo() {
        return receiveNo;
    }

    public void setReceiveNo(String receiveNo) {
        this.receiveNo = receiveNo;
    }

    public Long getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Long receiveTime) {
        this.receiveTime = receiveTime;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getBillType() {
        return billType;
    }

    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    public List<SettlementProductReq> getProducts() {
        return products;
    }

    public void setProducts(List<SettlementProductReq> products) {
        this.products = products;
    }

    public Integer getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(Integer currencyType) {
        this.currencyType = currencyType;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public Long getBillDate() {
        return billDate;
    }

    public void setBillDate(Long billDate) {
        this.billDate = billDate;
    }

    public String getBillAbstract() {
        return billAbstract;
    }

    public void setBillAbstract(String billAbstract) {
        this.billAbstract = billAbstract;
    }

    public Long getSourceContractId() {
        return sourceContractId;
    }

    public void setSourceContractId(Long sourceContractId) {
        this.sourceContractId = sourceContractId;
    }
}
