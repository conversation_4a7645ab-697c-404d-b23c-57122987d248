package com.ssy.lingxi.settlement.api.model.req;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 查询发票请求参数
 * <AUTHOR>
 * @since 2021/2/4
 * @version 2.0.0
 */
@Data
@Accessors(chain = true)
public class InvoiceMessageGetReq implements Serializable {

    private static final long serialVersionUID = 5967833488147523117L;

    @NotNull(message = "会员id不能为空")
    @Positive(message = "会员id不能为空")
    private Long memberId;

    private Long roleId;
}
