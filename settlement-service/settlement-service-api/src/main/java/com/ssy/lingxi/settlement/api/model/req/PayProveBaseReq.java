package com.ssy.lingxi.settlement.api.model.req;

import java.io.Serializable;

/**
 * 支付凭证VO
 * <AUTHOR>
 * @since 2021/1/5
 * @version 2.0.0
 */
public class PayProveBaseReq implements Serializable {
    private static final long serialVersionUID = -5066390902327562206L;
    /**
     * 凭证名称
     */
    private String name;

    /**
     * 凭证地址
     */
    private String proveUrl;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProveUrl() {
        return proveUrl;
    }

    public void setProveUrl(String proveUrl) {
        this.proveUrl = proveUrl;
    }
}
