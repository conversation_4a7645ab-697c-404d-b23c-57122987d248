package com.ssy.lingxi.settlement.api.model.req;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 发票信息修改
*/
@Data
public class InvoiceMessageUpdateReq {

    @NotNull(message = "主键ID不能为空")
    private Long id;
    /**
     * 发票种类:1.增值税普通发票（默认） 2.增值税专用发票
     */
    private Integer kind=1;


    /**
     * 发票类型:1.企业（默认） 2.个人
     */
    private Integer type=1;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 纳税号
     */
    private String taxNo;

    /**
     * 开户行
     */
    private String bankOfDeposit ;

    /**
     * 账号
     */
    private String account;

    /**
     * 地址
     */
    private String address;

    /**
     * 电话
     */
    private String tel;


    /**
     * 默认: 1.是 0.否
     */
    private Integer isDefault;

}
