package com.ssy.lingxi.settlement.api.model.req;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 联通支付付款VO
 * <AUTHOR>
 * @since 2021/11/29
 * @version 2.0.0
 */
public class CommunicationPayReq implements Serializable {
    private static final long serialVersionUID = 5410249739847071105L;
    /**
     * 结算id
     */
    @NotNull(message = "结算id大于0")
    @Positive(message = "结算id大于0")
    private Long id;

    /**
     * 支付渠道类型： 11.微信 12.支付 13.快捷支付 14.网银支付 15.余额支付
     */
    @NotNull(message = "支付渠道类型必须选取")
    @Positive(message = "支付渠道类型必须选取")
    private Integer payChannelType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPayChannelType() {
        return payChannelType;
    }

    public void setPayChannelType(Integer payChannelType) {
        this.payChannelType = payChannelType;
    }
}
