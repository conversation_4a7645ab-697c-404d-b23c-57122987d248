package com.ssy.lingxi.settlement.api.model.req;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 获取发票凭证VO
 * <AUTHOR>
 * @since 2021/3/10
 * @version 2.0.0
 */
public class InvoiceProveGetReq implements Serializable {
    private static final long serialVersionUID = 5333753025791267744L;

    /**
     * 单据号
     */
    @NotBlank(message = "单据号不能为空")
    private String orderNo;

    /**
     * 单据类型
     * 1.生成通知单
     * 2.物流单
     * 3.订单
     * 4.积分订单
     * 6.请款单
     */
    @NotNull(message = "单据类型不能为空")
    @Positive(message = "单据类型不能为空")
    private Integer settlementOrderType;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getSettlementOrderType() {
        return settlementOrderType;
    }

    public void setSettlementOrderType(Integer settlementOrderType) {
        this.settlementOrderType = settlementOrderType;
    }
}
