package com.ssy.lingxi.settlement.api.model.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/17 17:33
 */
public class SettleAccountQueueDTO implements Serializable {
    private static final long serialVersionUID = 8254408940072300928L;

    public SettleAccountQueueDTO() {
        this.times = 0;
    }

    public SettleAccountQueueDTO(Integer type, String message, long delayTime) {
        this.type = type;
        this.message = message;
        this.delayTime = delayTime;
        this.times = 0;
    }

    /**
     * 队列类型
     * 1 - 待对账（待定）
     * 2 - 平台优惠券结算
     * 定义: SettleAccountQueueMessageTypeEnum
     */
    private Integer type;

    /**
     * 队列中的消息，根据队列类型，由不同的业务接口进行处理
     * 队列类型:2 - PlatformCouponSettlementRequestVO
     */
    private String message;

    /**
     * （重新）入队列次数，初始值为0
     */
    private Integer times;

    /**
     * 延迟时间（毫秒）
     */
    private long delayTime;


    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public long getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(long delayTime) {
        this.delayTime = delayTime;
    }
}
