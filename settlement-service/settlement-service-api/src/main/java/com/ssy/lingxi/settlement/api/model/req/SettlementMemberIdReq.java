package com.ssy.lingxi.settlement.api.model.req;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.io.Serializable;

/**
 * 会员idVO
 *
 * <AUTHOR>
 * @version 2.08.18
 * @since 2022-08-15
 */
public class SettlementMemberIdReq implements Serializable {

    private static final long serialVersionUID = 2380675045848176508L;

    @NotNull(message = "会员Id要大于0")
    @Positive(message = "会员Id要大于0")
    private Long memberId;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    @Override
    public String toString() {
        return "MemberIdVO{" +
                "memberId=" + memberId +
                '}';
    }
}
