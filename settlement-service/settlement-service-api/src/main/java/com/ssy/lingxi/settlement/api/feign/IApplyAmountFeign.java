package com.ssy.lingxi.settlement.api.feign;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.settlement.api.fallback.factory.ApplyAmountFeignFallbackFactory;
import com.ssy.lingxi.settlement.api.model.req.ApplyAmountDetailSunQueryDataReq;
import com.ssy.lingxi.settlement.api.model.req.ApplyAmountDetailTotalSunQueryReq;
import com.ssy.lingxi.settlement.api.model.req.ApplyAmountSunQueryDataReq;
import com.ssy.lingxi.settlement.api.model.resp.ApplyAmountDetailSunResp;
import com.ssy.lingxi.settlement.api.model.resp.ApplyAmountDetailTotalSunResp;
import com.ssy.lingxi.settlement.api.model.resp.ApplyAmountSunResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = ServiceModuleConstant.SETTLEMENT_SERVICE, fallbackFactory = ApplyAmountFeignFallbackFactory.class)
public interface IApplyAmountFeign {

    String PATH_PREFIX = ServiceModuleConstant.SETTLEMENT_FEIGN_PATH_PREFIX +  "/apply/amount/";
    
    /**
     * 合同服务-查询请款执行请款
     * @param queryVO 查询条件
     * @return 请款执行请款
     */
    @PostMapping(value = PATH_PREFIX + "pageContractExecuteDetail")
    WrapperResp<PageDataResp<ApplyAmountDetailSunResp>> pageContractExecuteDetail(@RequestBody ApplyAmountDetailSunQueryDataReq queryVO);

    /**
     * 合同服务-查询请款执行请款汇总
     * @param queryVO 查询条件
     * @return 请款执行请款汇总
     */
    @PostMapping(value = PATH_PREFIX + "pageContractExecuteDetailSum")
    WrapperResp<List<ApplyAmountDetailTotalSunResp>> pageContractExecuteDetailSum(@RequestBody ApplyAmountDetailTotalSunQueryReq queryVO);


    /**
     * 合同服务-查询 合同请款情况统计
     * @param queryVO 查询条件
     * @return 合同请款情况统计
     */
    @PostMapping(value = PATH_PREFIX + "pageListForSummaryByParty")
    WrapperResp<PageDataResp<ApplyAmountSunResp>> pageListForSummaryByParty(@RequestBody ApplyAmountSunQueryDataReq queryVO);

    /**
     * 合同服务-查询 合同请款付款情况统计
     * @param queryVO 查询条件
     * @return 合同请款付款情况统计
     */
    @PostMapping(value = PATH_PREFIX + "listForPaySummaryByParty")
    WrapperResp<List<ApplyAmountSunResp>> listForPaySummaryByParty(@RequestBody ApplyAmountSunQueryDataReq queryVO);
}
