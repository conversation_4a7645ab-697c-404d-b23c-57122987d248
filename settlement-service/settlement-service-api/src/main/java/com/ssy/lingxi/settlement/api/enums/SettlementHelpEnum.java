package com.ssy.lingxi.settlement.api.enums;

import com.ssy.lingxi.component.base.language.LanguageHolder;

import java.util.Arrays;

/**
 * 结算辅助国际化枚举
 * <AUTHOR>
 * @since 2022/2/17
 * @version 2.0.0
 */
public enum SettlementHelpEnum {
    UNKNOWN(1, "未知"),
    DAY(2, "天"),
    NO(3, "号"),
    PAY_AFTER_SETTLEMENT(4, "结算后支付"),
    ;
    private final Integer code;
    private final String message;

    SettlementHelpEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return LanguageHolder.getTranslation(this.getClass(), this.message, this.code);
    }

    public static String getMessage(Integer code) {
        SettlementHelpEnum item = Arrays.stream(SettlementHelpEnum.values()).filter(e -> e.getCode().equals(code)).findFirst().orElse(null);
        return null != item ? item.getMessage() : "";
    }
}
