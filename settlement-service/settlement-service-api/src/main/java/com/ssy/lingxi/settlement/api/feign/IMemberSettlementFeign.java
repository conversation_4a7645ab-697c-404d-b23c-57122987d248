package com.ssy.lingxi.settlement.api.feign;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.settlement.api.fallback.factory.MemberSettlementFeignFallbackFactory;
import com.ssy.lingxi.settlement.api.model.req.AddSettlementApplyAmountReq;
import com.ssy.lingxi.settlement.api.model.req.CallbackCommunicationPayReq;
import com.ssy.lingxi.settlement.api.model.req.SettlementMemberIdReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 结算服务会员结算Feign接口
 * <AUTHOR>
 * @since 2021/03/24
 * @version 2.0.0
 */
@Primary
@FeignClient(name = ServiceModuleConstant.SETTLEMENT_SERVICE, fallbackFactory = MemberSettlementFeignFallbackFactory.class)
public interface IMemberSettlementFeign {

    String PATH_PREFIX = ServiceModuleConstant.SETTLEMENT_FEIGN_PATH_PREFIX +  "/member/";
    
    /**
     * 新增请款单
     * <AUTHOR>
     * @since 2021/3/24
     **/
    @PostMapping(value = PATH_PREFIX + "addApplyAmount")
    WrapperResp<Void> addApplyAmount(@RequestBody @Valid AddSettlementApplyAmountReq addVO);

    /**
     * 应付账款-通联支付-回调地址
     * @param request 请求参数
     * @return 处理结果
     */
    @PostMapping(value = PATH_PREFIX + "settlement/callback/communication/pay")
    WrapperResp<Boolean> confirmCommunicationPay(@RequestBody @Valid CallbackCommunicationPayReq request);

    /**
     * 是否存在未完成的应收应付
     *
     * @param memberIdReq 会员id
     * @return false - 全都完成，true-未完成
     */
    @PostMapping(value = PATH_PREFIX + "is/unfinished")
    WrapperResp<Boolean> isUnfinishedMemberSettlement(@RequestBody @Valid SettlementMemberIdReq memberIdReq);
}
