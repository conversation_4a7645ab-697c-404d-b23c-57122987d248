package com.ssy.lingxi.settlement.api.model.req;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 查询会员发票列表请求参数
 * <AUTHOR>
 * @since 2021/2/4
 * @version 2.0.0
 */
public class InvoiceMessageListReq implements Serializable {


    private static final long serialVersionUID = -2534888402497285762L;

    @Valid
    @Size(min = 1,message = "会员信息必填")
    private List<InvoiceMessageGetReq> memberList;

    public List<InvoiceMessageGetReq> getMemberList() {
        return memberList;
    }

    public void setMemberList(List<InvoiceMessageGetReq> memberList) {
        this.memberList = memberList;
    }
}
