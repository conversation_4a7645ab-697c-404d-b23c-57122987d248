BuyerInnerStatusEnum.0=Contract to be signed
BuyerInnerStatusEnum.1=Pending submission for review
BuyerInnerStatusEnum.2=Pending Review (Level 1)
BuyerInnerStatusEnum.3=Approval failed (Level 1)
BuyerInnerStatusEnum.4=To be reviewed (level 2)
BuyerInnerStatusEnum.5=Approval failed (level 2)
BuyerInnerStatusEnum.6=To be submitted
BuyerInnerStatusEnum.7=Submitted
BuyerInnerStatusEnum.8=To be confirmed electronic contract
BuyerInnerStatusEnum.9=Electronic contract confirmed
BuyerInnerStatusEnum.10=Pending payment
BuyerInnerStatusEnum.19=Paying
BuyerInnerStatusEnum.11=Successful payment
BuyerInnerStatusEnum.12=Payment failed
BuyerInnerStatusEnum.13=Purchase receipt order to be added
BuyerInnerStatusEnum.14=Purchase receipt pending approval
BuyerInnerStatusEnum.15=Approved purchase receipt
BuyerInnerStatusEnum.16=To be confirmed receipt
BuyerInnerStatusEnum.17=Acknowledged receipt
BuyerInnerStatusEnum.18=To be archived
BuyerInnerStatusEnum.1000=Completed
BuyerInnerStatusEnum.1001=Cancelled
BuyerInnerStatusEnum.1002=Aborted
BuyerInnerStatusEnum.1003=Settled

VendorInnerStatusEnum.100=Waiting for buyer to submit order
VendorInnerStatusEnum.101=Pending submission for review
VendorInnerStatusEnum.102=Submission review failed
VendorInnerStatusEnum.103=To be reviewed (Level 1)
VendorInnerStatusEnum.104=Approval failed (Level 1)
VendorInnerStatusEnum.105=To be reviewed (level 2)
VendorInnerStatusEnum.106=The review failed (level 2)
VendorInnerStatusEnum.107=To be confirmed
VendorInnerStatusEnum.108=Confirmed
VendorInnerStatusEnum.109=Payment result to be confirmed
VendorInnerStatusEnum.110=Payment result confirmed
VendorInnerStatusEnum.111=To be added sales invoice
VendorInnerStatusEnum.112=Pending Review Sales Invoice
VendorInnerStatusEnum.113=Approved Sales Invoice
VendorInnerStatusEnum.114=Logistics order to be added
VendorInnerStatusEnum.115=Logistics order to be confirmed
VendorInnerStatusEnum.116=Not accepting logistics order
VendorInnerStatusEnum.117=Logistics order confirmed
VendorInnerStatusEnum.118=To be confirmed shipment
VendorInnerStatusEnum.119=Confirmed Shipment
VendorInnerStatusEnum.120=Receipt to be confirmed
VendorInnerStatusEnum.121=Invoice confirmed
VendorInnerStatusEnum.122=To be archived
VendorInnerStatusEnum.123=Pick-up order has been written off
VendorInnerStatusEnum.1000=Completed
VendorInnerStatusEnum.1001=Cancelled
VendorInnerStatusEnum.1002=Aborted

OrderOuterStatusEnum.0=Contract to be signed
OrderOuterStatusEnum.1=To be submitted
OrderOuterStatusEnum.2=To be confirmed
OrderOuterStatusEnum.3=Order not accepted
OrderOuterStatusEnum.4=To be confirmed electronic contract
OrderOuterStatusEnum.5=Do not agree to sign electronic contract
OrderOuterStatusEnum.6=To be paid
OrderOuterStatusEnum.7=Payment result to be confirmed
OrderOuterStatusEnum.8=Confirmation not received
OrderOuterStatusEnum.9=To be added sales invoice
OrderOuterStatusEnum.10=Outbound order to be added
OrderOuterStatusEnum.11=To be confirmed shipment
OrderOuterStatusEnum.12=Purchase receipt order to be added
OrderOuterStatusEnum.13=To be confirmed receipt
OrderOuterStatusEnum.14=Receipt to be confirmed
OrderOuterStatusEnum.15=To be archived
OrderOuterStatusEnum.16=Purchase Receipt to be added
OrderOuterStatusEnum.100=Completed
OrderOuterStatusEnum.101=Cancelled
OrderOuterStatusEnum.102=Aborted

OrderOperationEnum.1=Add Order
OrderOperationEnum.2=Modify order
OrderOperationEnum.3=Delete order
OrderOperationEnum.4=Submit for review
OrderOperationEnum.5=Review Order (Level 1)
OrderOperationEnum.6=Review Order (Level 2)
OrderOperationEnum.7=Submit order
OrderOperationEnum.8=Accept Order
OrderOperationEnum.9=Order not accepted
OrderOperationEnum.10=Confirm Electronic Contract
OrderOperationEnum.11=Order payment
OrderOperationEnum.12=Successful payment
OrderOperationEnum.13=Payment failed
OrderOperationEnum.14=Confirm payment result
OrderOperationEnum.15=Confirmation not received
OrderOperationEnum.16=Add Sales Invoice
OrderOperationEnum.17=Add Logistics Order
OrderOperationEnum.18=Order Shipment Confirmation
OrderOperationEnum.19=Add Purchase Receipt
OrderOperationEnum.20=Order receipt confirmation
OrderOperationEnum.21=Confirm Receipt
OrderOperationEnum.22=Archive
OrderOperationEnum.23=Abort order
OrderOperationEnum.24=Cancel order
OrderOperationEnum.25=Adjust delivery time
OrderOperationEnum.26=Modify order unit price
OrderOperationEnum.27=Modify order shipping
OrderOperationEnum.28=Order Refund
OrderOperationEnum.29=The group is successful
OrderOperationEnum.30=Organization failed
OrderOperationEnum.31=Confirm Electronic Contract
OrderOperationEnum.32=Change order
OrderOperationEnum.33=Cancel change order
OrderOperationEnum.34=Submit a change order

OrderProductDeliverTypeEnum.1=Logistics
OrderProductDeliverTypeEnum.2=Self-delivery
OrderProductDeliverTypeEnum.3=No Shipping Required
OrderProductDeliverTypeEnum.4=Logistics + Self-pickup

OrderStringEnum.1=Invoice number:
OrderStringEnum.2=Modify
OrderStringEnum.3=Order shipping fee changed from \uFFE5{} to \uFFE5{}; reason: {};
OrderStringEnum.4=Receiver Number:
OrderStringEnum.5=Refund successful
OrderStringEnum.6=Refund failed
OrderStringEnum.7=Payment times:

BaseTradeProcessEnum.1=Order multiple shipment process
BaseTradeProcessEnum.2=Pay once and ship many processes
BaseTradeProcessEnum.3=Point Order Process
BaseTradeProcessEnum.4=Multiple Payments Multiple Shipments Process
BaseTradeProcessEnum.5=Order Review Multiple Payment Multiple Shipping Process
BaseTradeProcessEnum.6=After-sale replacement processing process 1 (including receipt and delivery order)
BaseTradeProcessEnum.7=After-sale exchange processing process 2 (no receipt and delivery order)
BaseTradeProcessEnum.8=After-sale replacement processing process 3 (offline logistics)
BaseTradeProcessEnum.9=After-sales return processing process 1 (including receipt and delivery order)
BaseTradeProcessEnum.10=After-sales return processing process 2 (offline logistics)
BaseTradeProcessEnum.11=After-sales return processing process 3 (no receipt and delivery note)
BaseTradeProcessEnum.12=After-sale return processing process 4 (platform review without receipt and delivery order)
BaseTradeProcessEnum.13=Processing production process
BaseTradeProcessEnum.14=Processing production manual shipment processing process
BaseTradeProcessEnum.15=After-sales maintenance process
BaseTradeProcessEnum.16=Multiple payment and multiple shipments with receipt and delivery order online logistics process
BaseTradeProcessEnum.17=Cross-border e-commerce import order transaction process
BaseTradeProcessEnum.18=Level 2 review of the internal process of after-sales exchange
BaseTradeProcessEnum.19=Level 2 audit of the internal process for after-sales returns
BaseTradeProcessEnum.20=Level 2 audit of after-sales maintenance internal process
BaseTradeProcessEnum.21=Order Buyer Change Review Process
BaseTradeProcessEnum.22=Order Review Contract Confirm Multiple Payment Multiple Shipment Process

BaseTradeProcessEnum.1.r=1-order submission-->2-order delivery-->3-order receipt
BaseTradeProcessEnum.2.r=1-Submit order-->2-Order payment-->3-Confirm payment result-->4-Order delivery confirmation-->5-Order receipt confirmation
BaseTradeProcessEnum.3.r=1-Submit order-->2-Order payment-->3-Confirm payment result-->4-Order delivery confirmation-->5-Order receipt confirmation
BaseTradeProcessEnum.4.r=1-Submit order-->2-Order payment-->3-Confirm payment result-->4-Order delivery confirmation-->5-Order receipt confirmation-->6-Order payment -- >7-Confirm payment result
BaseTradeProcessEnum.5.r=1-Submit order-->2-Confirm order-->3-Order payment-->4-Confirm payment result-->5-Order delivery confirmation-->6-Order receipt confirmation -- >7-Order payment-->8-Confirm payment result
BaseTradeProcessEnum.6.r=1-Submit the exchange application-->2-Confirm the exchange application-->3-Add a return shipping order-->4-Add a logistics order-->5-Return shipping Goods-- >6-Add return receipt-->7-Return receipt-->8-Confirm return receipt-->9-Add replacement invoice-->10-Add logistics order -->11-Replacement and delivery-->12- Add replacement receipt-->13-Receipt for replacement-->14-Confirm replacement receipt-->15-Confirm after-sale completion
BaseTradeProcessEnum.7.r=1-Submit the exchange request-->2-Confirm the exchange request-->3-Return shipment-->4-Return shipment-->5-Replacement shipment-->6-Exchange goods receipt -->7-Confirm after-sale completion
BaseTradeProcessEnum.8.r=1-The buyer submits the replacement application form-->2-The supplier confirms the replacement application form-->3-The buyer adds a return delivery note-->4-The buyer returns the delivery - ->5-Supplier Adds a Return Receipt-->6-Supplier Returns Receipt-->7-Supplier Adds a Replacement Invoice-->8-Supplier Exchange Delivery-->9-Buyer adds replacement receipt-->10-Buyer replacement receipt-->11-Buyer confirms after-sale completion
BaseTradeProcessEnum.9.r=1: The buyer submits a return application -->2: The supplier confirms the return application -->3: The buyer adds a return shipment -->4: The buyer adds a logistics order- -> 5: Buyer returns goods-->6: Supplier adds a new return receipt-->7: Supplier returns goods-->8: Buyer confirms return receipt-->9: Supplier Refund --> 10: The buyer confirms that the after-sale is completed
BaseTradeProcessEnum.10.r=1: Buyer submits a return request form --> 2: Supplier confirms return request form --> 3: Buyer adds a return shipment --> 4: Buyer returns shipment -- >5: The supplier adds a new return receipt-->6: The supplier returns the goods-->7: The supplier refunds the goods-->8: The buyer confirms that the after-sale is completed
BaseTradeProcessEnum.11.r=1: Buyer submits return application --> 2: Supplier confirms return application --> 3: Buyer returns goods --> 4: Supplier returns goods --> 5: Supplier refund --> 6: Buyer confirms after-sale completion
BaseTradeProcessEnum.12.r=1: Buyer submits return application --> 2: Supplier confirms return application --> 3: Platform confirms return application --> 4: Buyer returns delivery --> 5: Supplier returns and receives goods -- > 6: Supplier refunds --> 7: Buyer confirms after-sales completion
BaseTradeProcessEnum.13.r=1-Submit production order-->2-Confirm production order-->3-Add processing invoice-->4-Add logistics order-->5-Send production order Goods-->6-Add Processing Receipt-->7-Production Notice Receipt-->8-Confirm Receipt-->9-Complete
BaseTradeProcessEnum.14.r=1-Submit Production Notice-->2-Confirm Production Notice-->5-Production Notice Delivery-->7-Production Notice Receipt-->9-Complete
BaseTradeProcessEnum.15.r=1-Submit maintenance application for review-->2-Review maintenance application (Level 1)-->3-Review maintenance application (Level 2)-->4-Confirm maintenance application-- >5- Done
BaseTradeProcessEnum.16.r=1-Submit order-->2-Order payment-->3-Confirm payment result-->4-Add sales invoice-->5-Add logistics order-->6-Order Delivery Confirmation- ->7-Add Purchase Receipt-->8-Order Receipt Confirmation
BaseTradeProcessEnum.17.r=1-Order submission-->2-Order payment-->3-Order delivery-->4-Order receipt-->5-Order evaluation-->5-Order completed
BaseTradeProcessEnum.18.r=1-Submission and review of after-sales replacement application form-->2-After-sales replacement application form review level 1-->3-After-sales replacement application form review level 2-->4-After-sales replacement application single confirmation
BaseTradeProcessEnum.19.r=1-Submission and review of after-sales return application form-->2-After-sales return application form review level 1-->3-After-sales return application form review level 2-->4-After-sales return application form confirmation
BaseTradeProcessEnum.20.r=1-Submission and review of the application form-->2-The first level of the application form review-->3-The second level of the application form review-->4-The confirmation of the application form
BaseTradeProcessEnum.21.r=1-Order Submit Review-->2-Order Review (Level 1)-->3-Order Review (Level 2)-->4-Order Submit
BaseTradeProcessEnum.22.r=1-Submit order-->2-Confirm order-->3Confirm contract-->4-Order payment-->5-Confirm payment result-->6-Order delivery confirmation--> 7-Order receipt confirmation-->8-Order payment-->9-Confirm payment result

BasePurchaseProcessEnum.1=Purchase RFQ transaction process
BasePurchaseProcessEnum.2=Purchase requisition order process
BasePurchaseProcessEnum.3=After-sale replacement processing process 1 (including receipt and delivery note)
BasePurchaseProcessEnum.4=After-sale replacement processing process 2 (no receipt and delivery note)
BasePurchaseProcessEnum.5=After-sale replacement processing process 3 (offline logistics)
BasePurchaseProcessEnum.6=After-sale return processing process 1 (contract includes receipt and delivery note)
BasePurchaseProcessEnum.7=After-sale return processing process 2 (contract offline logistics)
BasePurchaseProcessEnum.8=After-sales return processing process 3 (contract without receipt and delivery note)
BasePurchaseProcessEnum.9=After-sales maintenance process
BasePurchaseProcessEnum.10=Order Purchaser Change Review Process
BasePurchaseProcessEnum.11=Purchase bidding contract transaction process

BasePurchaseProcessEnum.1.r=1-order submission-->2-order delivery-->3-order receipt
BasePurchaseProcessEnum.2.r=1-order submission-->2-order delivery-->3-order receipt
BasePurchaseProcessEnum.3.r=1-After-sale exchange order application-->2-After-sale exchange order review-->3-After-sale exchange order confirmation-->4-Return delivery processing-->5-Return Processing-->6-Replacement receipt delivery processing-->7-Replacement receipt processing
BasePurchaseProcessEnum.4.r=1-Application for after-sales replacement order-->2-After-sales replacement order review-->3-After-sales replacement order confirmation-->4-Confirm after-sales completion
BasePurchaseProcessEnum.5.r=1-Application for after-sales replacement order-->2-After-sales replacement order review-->3-After-sales replacement order confirmation-->4-Confirm after-sales completion
BasePurchaseProcessEnum.6.r=1: Buyer submits a return request form-->2: Supplier confirms return request form-->3: Buyer adds a return delivery note-->4: Buyer adds a logistics note-->5: Buyer returns goods-->6: Supplier adds a new return receipt-->7: Supplier returns goods-->8: Buyer confirms return receipt-->9: Buyer Confirm after-sale completion
BasePurchaseProcessEnum.7.r=1: The buyer submits the return application form --> 2: The supplier confirms the return application form --> 3: The buyer adds a new return delivery document --> 4: The buyer returns the delivery -- >5: The supplier adds a new return receipt-->6: The supplier returns the goods-->7: The buyer confirms that the after-sale is completed
BasePurchaseProcessEnum.8.r=1: Buyer submits return application --> 2: Supplier confirms return application --> 3: Buyer returns delivery --> 4: Supplier returns receipt --> 5: Buyer confirms after-sale completion
BasePurchaseProcessEnum.9.r=1-Submit the maintenance application for review-->2-Review the maintenance application (Level 1)-->3-Review the maintenance application (Level 2)-->4-Confirm the maintenance application- -> 5-Done
BasePurchaseProcessEnum.10.r=1-Order Submit Review-->2-Order Review (Level 1)-->3-Order Review (Level 2)-->4-Order Submit
BasePurchaseProcessEnum.11.r=1-Order Submit-->2-Order Confirmation-->3-Confirm Electronic Contract/Delivery-->4-Order Shipment-->5-Order Receipt

DeliveryHistoryStatusEnum.1=To be delivered
DeliveryHistoryStatusEnum.2=To be confirmed
DeliveryHistoryStatusEnum.3=To be revised
DeliveryHistoryStatusEnum.4=Confirmed
DeliveryHistoryStatusEnum.5=Delivery note generated
DeliveryHistoryStatusEnum.6=Obsolete
DeliveryHistoryStatusEnum.7=Submitted
DeliveryHistoryStatusEnum.8=Received

DeliveryOrderOuterStatusEnum.1=Submitted
DeliveryOrderOuterStatusEnum.2=Received
DeliveryOrderOuterStatusEnum.3=Obsolete

ReceiveOrderOuterStatusEnum.1=Submitted
ReceiveOrderOuterStatusEnum.3=Obsolete
ReceiveOrderOuterStatusEnum.4=Create quality

DeliveryOperationEnum.1=Create delivery plan
DeliveryOperationEnum.2=Change delivery schedule
DeliveryOperationEnum.3=Confirm delivery plan
DeliveryOperationEnum.4=Generate delivery order
DeliveryOperationEnum.5=Change Delivery Notice
DeliveryOperationEnum.6=Confirm delivery order
DeliveryOperationEnum.7=Void
DeliveryOperationEnum.8=Generate delivery order
DeliveryOperationEnum.9=Change Delivery Note
DeliveryOperationEnum.10=Confirm Delivery Note
DeliveryOperationEnum.11=Generate delivery order
DeliveryOperationEnum.12=Change delivery order
DeliveryOperationEnum.13=Confirm Receipt
DeliveryOperationEnum.14=Submit delivery plan

InboundOrderEnum.1=Order number
InboundOrderEnum.2=Inbound order number
InboundOrderEnum.3=Inbound Order Number

CurrencyTypeEnum.1=CNY
CurrencyTypeEnum.2=USD
CurrencyTypeEnum.3=JPY
CurrencyTypeEnum.4=EUR

QualityTypeEnum.1=Incoming Quality Inspection
QualityTypeEnum.2=Sample quality inspection
QualityTypeEnum.3=Trial quality inspection

InspectionTypeEnum.1=Exemption from inspection
InspectionTypeEnum.2=Full Inspection
InspectionTypeEnum.3=Sampling Inspection

BatchJudgmentTypeEnum.1=Qualified
BatchJudgmentTypeEnum.2=Partially qualified
BatchJudgmentTypeEnum.3=Receive in concession
BatchJudgmentTypeEnum.4=Reject

InspectionJudgmentTypeEnum.1=Qualified
InspectionJudgmentTypeEnum.2=Unqualified

ReturnTypeEnum.1=Rebate
ReturnTypeEnum.2=Refund

HandleTypeEnum.1=Maintenance
HandleTypeEnum.2=In-Place Repair
HandleTypeEnum.3=Rework
HandleTypeEnum.4=Scrap

QualityStatusEnum.1=Quality inspection in progress
QualityStatusEnum.2=Quality inspection completed
QualityStatusEnum.3=After-sales order has been generated

QualityOrderOperationEnum.1=Add Quality Inspection Order
QualityOrderOperationEnum.2=Modify Quality Inspection Order
QualityOrderOperationEnum.3=Submit a quality check
QualityOrderOperationEnum.4=Generate after sales order

QualityOrderStatusEnum.1=under quality inspection
QualityOrderStatusEnum.2=Quality inspection order has been generated
QualityOrderStatusEnum.3=Quality inspection completed
QualityOrderStatusEnum.4=After-sales order has been generated

QualityOrderResourceEnum.1=New
QualityOrderResourceEnum.2=Receipt Generation
QualityOrderResourceEnum.3=Generate sample request form

QualityOrderAfterSalesStatusEnum.0=
QualityOrderAfterSalesStatusEnum.1=Not generated
QualityOrderAfterSalesStatusEnum.2=Generated

ProblemDegreeTypeEnum.1=urgent
ProblemDegreeTypeEnum.2=general emergency
ProblemDegreeTypeEnum.3=not urgent

EightDRectificationSourceTypeEnum.1=Receipt quality inspection
EightDRectificationSourceTypeEnum.2=process discovery
EightDRectificationSourceTypeEnum.3=Sample quality inspection
EightDRectificationSourceTypeEnum.4=Trial quality inspection

EightDRectificationSourceOrderEnum.1=Receipt
EightDRectificationSourceOrderEnum.2=Quality inspection list
EightDRectificationSourceOrderEnum.3=Order

EightDRectificationExternalStatusEnum.1=8D to be submitted
EightDRectificationExternalStatusEnum.2=Awaiting ICA feedback
EightDRectificationExternalStatusEnum.3=Pending review ICA feedback
EightDRectificationExternalStatusEnum.4=ICA feedback review failed
EightDRectificationExternalStatusEnum.5=Pending PCA feedback
EightDRectificationExternalStatusEnum.6=Pending review PCA feedback
EightDRectificationExternalStatusEnum.7=PCA feedback review failed
EightDRectificationExternalStatusEnum.99=completed
EightDRectificationExternalStatusEnum.-1=Cancelled

EightDRectificationInternalStatusEnum.1=8D to be submitted
EightDRectificationInternalStatusEnum.2=8D submitted
EightDRectificationInternalStatusEnum.3=Pending submission for review ICA feedback
EightDRectificationInternalStatusEnum.4=Pending Review ICA Feedback (Level 1)
EightDRectificationInternalStatusEnum.5=ICA feedback review failed (Level 1)
EightDRectificationInternalStatusEnum.6=Pending Review ICA Feedback (Level 2)
EightDRectificationInternalStatusEnum.7=ICA feedback review failed (level 2)
EightDRectificationInternalStatusEnum.8=To be confirmed ICA feedback
EightDRectificationInternalStatusEnum.9=ICA feedback review failed
EightDRectificationInternalStatusEnum.10=ICA feedback confirmed
EightDRectificationInternalStatusEnum.11=Pending submission for review PCA feedback
EightDRectificationInternalStatusEnum.12=Pending PCA Feedback (Level 1)
EightDRectificationInternalStatusEnum.13=PCA feedback review failed (Level 1)
EightDRectificationInternalStatusEnum.14=Pending PCA Feedback (Level 2)
EightDRectificationInternalStatusEnum.15=PCA feedback review failed (level 2)
EightDRectificationInternalStatusEnum.16=To be confirmed PCA feedback
EightDRectificationInternalStatusEnum.17=PCA feedback review failed
EightDRectificationInternalStatusEnum.99=completed
EightDRectificationInternalStatusEnum.-1=Cancelled

EightDRectificationOperateTypeEnum.1=ICA feedback to be submitted for review
EightDRectificationOperateTypeEnum.2=Pending ICA Feedback (Level 1)
EightDRectificationOperateTypeEnum.3=Pending ICA Feedback (Level 2)
EightDRectificationOperateTypeEnum.4=To be confirmed ICA feedback
EightDRectificationOperateTypeEnum.5=Pending Review PCA Feedback
EightDRectificationOperateTypeEnum.6=Pending PCA Feedback (Level 1)
EightDRectificationOperateTypeEnum.7=Pending PCA Feedback (Level 2)
EightDRectificationOperateTypeEnum.8=Pending PCA feedback
EightDRectificationOperateTypeEnum.9=8D to be submitted (SRM)
EightDRectificationOperateTypeEnum.10=To be submitted 8D (B2B)

EightDOperationStateEnum.0=Added 8 D
EightDOperationStateEnum.1=Commit 8 D
EightDOperationStateEnum.2=ICA feedback
EightDOperationStateEnum.3=Review ICA feedback
EightDOperationStateEnum.4=PCA feedback
EightDOperationStateEnum.5=Review PCA Feedback
EightDOperationStateEnum.6=Submit ICA Feedback for Review
EightDOperationStateEnum.7=Review ICA Feedback (Level 1)
EightDOperationStateEnum.8=Review ICA Feedback (Level 2)
EightDOperationStateEnum.9=Confirm ICA Feedback
EightDOperationStateEnum.10=Submit PCA Feedback for Review
EightDOperationStateEnum.11=Review PCA Feedback (Level 1)
EightDOperationStateEnum.12=Review PCA Feedback (Level 2)
EightDOperationStateEnum.13=Confirm PCA Feedback
EightDOperationStateEnum.14=modification 8 D
EightDOperationStateEnum.-1=Cancel 8D

EightTaskStepStatusEnum.1=Added 8D report
EightTaskStepStatusEnum.2=ICA feedback
EightTaskStepStatusEnum.3=Review ICA feedback
EightTaskStepStatusEnum.4=PCA feedback
EightTaskStepStatusEnum.5=Review PCA Feedback

EightIcaTaskStepStatusEnum.1=Submit ICA Feedback for Review
EightIcaTaskStepStatusEnum.2=Review ICA Feedback (Level 1)
EightIcaTaskStepStatusEnum.3=Review ICA Feedback (Level 2)
EightIcaTaskStepStatusEnum.4=Confirm ICA Feedback

EightPcaTaskStepStatusEnum.1=Submit PCA Feedback for Review
EightPcaTaskStepStatusEnum.2=Review PCA Feedback (Level 1)
EightPcaTaskStepStatusEnum.3=Review PCA Feedback (Level 2)
EightPcaTaskStepStatusEnum.4=Confirm PCA Feedback

QualityProcessEnum.1=Quality Management ICA Audit Process - Level 1 Audit
QualityProcessEnum.2=Quality Management ICA Audit Process - Level 2 Audit
QualityProcessEnum.3=Quality Management ICA Audit Process - Level 3 Audit
QualityProcessEnum.4=Quality Management ICA Audit Process - Level 4 Audit
QualityProcessEnum.5=Quality Management PCA Audit Process - Level 1 Audit
QualityProcessEnum.6=Quality Management PCA Audit Process - Level 2 Audit
QualityProcessEnum.7=Quality Management PCA Audit Process - Level 3 Audit
QualityProcessEnum.8=Quality Management PCA Audit Process - Level 4 Audit

QualityProcessEnum.1.r=1- ICA feedback to be confirmed
QualityProcessEnum.2.r=1- ICA feedback to be submitted for review --> ICA feedback to be confirmed
QualityProcessEnum.3.r=1- ICA feedback to be submitted for review --> ICA feedback to be reviewed (level 1) --> ICA feedback to be confirmed
QualityProcessEnum.4.r=1- ICA feedback to be submitted for review --> ICA feedback to be reviewed (level 1) --> ICA feedback to be reviewed (level 2) --> ICA feedback to be confirmed
QualityProcessEnum.5.r=1- PCA feedback to be confirmed
QualityProcessEnum.6.r=1- PCA feedback to be submitted for review --> PCA feedback to be confirmed
QualityProcessEnum.7.r=1- PCA feedback to be submitted for review --> PCA feedback to be reviewed (level 1) --> PCA feedback to be confirmed
QualityProcessEnum.8.r=1- PCA feedback to be submitted for review --> PCA feedback to be reviewed (level 1) --> PCA feedback to be reviewed (level 2) --> PCA feedback to be confirmed

QualityProcessTypeEnum.1=Quality Management ICA Audit Process
QualityProcessTypeEnum.2=Quality Management PCA Audit Process

QualityProcessStatusEnum.0=Disable
QualityProcessStatusEnum.1=Enable

QualityAfterSalesStatusEnum.1=Incomplete
QualityAfterSalesStatusEnum.2=Completed

SaleOrderOperateTypeEnum.1=Order to be submitted for review
SaleOrderOperateTypeEnum.2=Order to be reviewed (Level 1)
SaleOrderOperateTypeEnum.3=Order to be reviewed (level 2)
SaleOrderOperateTypeEnum.4=Order to be confirmed
SaleOrderOperateTypeEnum.5=Payment result order to be confirmed
SaleOrderOperateTypeEnum.6=To be added sales invoice order
SaleOrderOperateTypeEnum.7=Logistics order to be added
SaleOrderOperateTypeEnum.8=Delivery order to be confirmed
SaleOrderOperateTypeEnum.9=Receipt order to be confirmed
SaleOrderOperateTypeEnum.10=Order to be archived
SaleOrderOperateTypeEnum.11=Order to be evaluated

PurchaseOrderOperateTypeEnum.1=Pending review order
PurchaseOrderOperateTypeEnum.2=Pending Order (Level 1)
PurchaseOrderOperateTypeEnum.3=Pending Review Order (Level 2)
PurchaseOrderOperateTypeEnum.4=Pending Confirmation Order
PurchaseOrderOperateTypeEnum.5=Pending Confirmation of Electronic Contract Order
PurchaseOrderOperateTypeEnum.6=Pending payment order
PurchaseOrderOperateTypeEnum.7=Purchase receipt order to be added
PurchaseOrderOperateTypeEnum.8=Pending confirmation receipt order
PurchaseOrderOperateTypeEnum.9=Order to be archived
PurchaseOrderOperateTypeEnum.10=Order to review

TradeDeliveryNoticeOperateTypeEnum.1=Pending Delivery Notice (SRM)
TradeDeliveryNoticeOperateTypeEnum.2=Delivery Notice to Submit (B2B)

TradeDeliveryNoticeCollaborationOperateTypeEnum.1=Delivery notice pending confirmation

TradeDeliveryPlanCollaborationOperateTypeEnum.1=To confirm delivery plan

TradeDeliveryPlanOperateTypeEnum.1=Delivery Plan to Submit (SRM)
TradeDeliveryPlanOperateTypeEnum.2=Delivery Plan to Submit (B2B)

TradeReceiptOperateTypeEnum.1=Pending delivery note

SaleOrderPlatformOperateTypeEnum.1=Order with payment results to be confirmed

InvoiceKindEnum.0=all
InvoiceKindEnum.1=VAT normal invoice
InvoiceKindEnum.2=VAT special invoice

InvoiceTypeEnum.0=all
InvoiceTypeEnum.1=Enterprise
InvoiceTypeEnum.2=Personal

PurchaseOrderExportFileEnum.tableName=PurchaseOrder
PurchaseOrderExportFileEnum.orderNo=OrderNo
PurchaseOrderExportFileEnum.digest=Digest
PurchaseOrderExportFileEnum.vendorMemberName=VendorMemberName
PurchaseOrderExportFileEnum.createTime=CreateTime
PurchaseOrderExportFileEnum.totalAmount=TotalAmount
PurchaseOrderExportFileEnum.orderType=OrderType
PurchaseOrderExportFileEnum.outerStatus=OuterStatus
PurchaseOrderExportFileEnum.vendorInnerStatus=VendorInnerStatus

SaleOrderExportFileEnum.tableName=SaleOrder
SaleOrderExportFileEnum.orderNo=OrderNo
SaleOrderExportFileEnum.digest=Digest
SaleOrderExportFileEnum.buyerMemberName=BuyerMemberName
SaleOrderExportFileEnum.createTime=CreateTime
SaleOrderExportFileEnum.totalAmount=TotalAmount
SaleOrderExportFileEnum.orderType=OrderType
SaleOrderExportFileEnum.transferOrderNo=TransferOrder
SaleOrderExportFileEnum.outerStatus=OuterStatus
SaleOrderExportFileEnum.vendorInnerStatus=VendorInnerStatus