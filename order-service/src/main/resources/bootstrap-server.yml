spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.address}:${nacos.port}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}
        group: ${nacos.group}
        register-enabled: ${nacos.register-enabled}
      config:
        server-addr: ${nacos.address}:${nacos.port}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}
        group: ${nacos.group}
        prefix: ${spring.application.name}
        file-extension: yml
        extension-configs:
          - data-id: common.yml
            group: ${nacos.group}
            refresh: true
seata:
  enabled: ${seata.enable}
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}_tx_group
  # 是否自动开启数据源代理
  enable-auto-data-source-proxy: true
  # 数据源代理模式，使用AT模式
  data-source-proxy-mode: AT
  config:
    type: nacos
    nacos:
      server-addr: ${nacos.address}:${nacos.port}
      namespace: ${seata.namespace}
      username: ${nacos.username}
      password: ${nacos.password}
      group: SEATA_GROUP
      data-id: common.yml
  registry:
    type: nacos
    nacos:
      server-addr: ${nacos.address}:${nacos.port}
      namespace: ${seata.namespace}
      username: ${nacos.username}
      password: ${nacos.password}
      group : SEATA_GROUP
      application: seata-server