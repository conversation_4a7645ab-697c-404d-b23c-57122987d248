BuyerInnerStatusEnum.0=서명 대기 중인 계약
BuyerInnerStatusEnum.1=검토를 위해 제출 대기 중
BuyerInnerStatusEnum.2=검토 보류(레벨 1)
BuyerInnerStatusEnum.3=승인 실패(레벨 ​​1)
BuyerInnerStatusEnum.4=검토 예정(레벨 2)
BuyerInnerStatusEnum.5=승인 실패(레벨 ​​2)
BuyerInnerStatusEnum.6=제출 예정
BuyerInnerStatusEnum.7=제출됨
BuyerInnerStatusEnum.8=전자 계약 확인 예정
BuyerInnerStatusEnum.9=전자 계약 확인됨
BuyerInnerStatusEnum.10=결제 대기 중
BuyerInnerStatusEnum.19=결제 중
BuyerInnerStatusEnum.11=성공적인 결제
BuyerInnerStatusEnum.12=결제 실패
BuyerInnerStatusEnum.13=구매 영수증 주문 추가 예정
BuyerInnerStatusEnum.14=승인 보류 중인 구매 영수증
BuyerInnerStatusEnum.15=승인된 구매 영수증
BuyerInnerStatusEnum.16=영수증 확인 예정
BuyerInnerStatusEnum.17=확인된 영수증
BuyerInnerStatusEnum.18=보관 예정
BuyerInnerStatusEnum.1000=완료
BuyerInnerStatusEnum.1001=취소됨
BuyerInnerStatusEnum.1002=중단됨
BuyerInnerStatusEnum.1003=정착

VendorInnerStatusEnum.100=구매자가 주문을 제출할 때까지 대기 중
VendorInnerStatusEnum.101=검토를 위해 제출 보류 중
VendorInnerStatusEnum.102=제출 검토 실패
VendorInnerStatusEnum.103=검토 예정(레벨 1)
VendorInnerStatusEnum.104=승인 실패(레벨 ​​1)
VendorInnerStatusEnum.105=검토 예정(레벨 2)
VendorInnerStatusEnum.106=검토 실패(레벨 ​​2)
VendorInnerStatusEnum.107=확인 예정
VendorInnerStatusEnum.108=확인됨
VendorInnerStatusEnum.109=결제 결과 확인 예정
VendorInnerStatusEnum.110=결제 결과 확인됨
VendorInnerStatusEnum.111=판매 송장 추가 예정
VendorInnerStatusEnum.112=판매 송장 검토 보류 중
VendorInnerStatusEnum.113=승인된 판매 송장
VendorInnerStatusEnum.114=물류 주문 추가 예정
VendorInnerStatusEnum.115=물류 주문 확인 예정
VendorInnerStatusEnum.116=물류 주문을 수락하지 않음
VendorInnerStatusEnum.117=물류 주문 확인됨
VendorInnerStatusEnum.118=배송 확인 예정
VendorInnerStatusEnum.119=배송 확인
VendorInnerStatusEnum.120=확인할 영수증
VendorInnerStatusEnum.121=송장 확인됨
VendorInnerStatusEnum.122=보관 예정
VendorInnerStatusEnum.123=픽업 주문이 취소되었습니다.
VendorInnerStatusEnum.1000=완료
VendorInnerStatusEnum.1001=취소됨
VendorInnerStatusEnum.1002=중단됨

OrderOuterStatusEnum.0=서명 대기 중인 계약서
OrderOuterStatusEnum.1=제출 예정
OrderOuterStatusEnum.2=확인 예정
OrderOuterStatusEnum.3=주문 불가
OrderOuterStatusEnum.4=전자 계약 확인 예정
OrderOuterStatusEnum.5=전자 계약 서명에 동의하지 않음
OrderOuterStatusEnum.6=지급 예정
OrderOuterStatusEnum.7=결제 결과 확인 예정
OrderOuterStatusEnum.8=확인이 수신되지 않음
OrderOuterStatusEnum.9=판매 송장 추가 예정
OrderOuterStatusEnum.10=추가할 아웃바운드 주문
OrderOuterStatusEnum.11=배송 확인 예정
OrderOuterStatusEnum.12=추가할 구매 영수증 주문
OrderOuterStatusEnum.13=수령 확인 예정
OrderOuterStatusEnum.14=확인할 영수증
OrderOuterStatusEnum.15=보관 예정
OrderOuterStatusEnum.16=추가할 구매 영수증
OrderOuterStatusEnum.100=완료
OrderOuterStatusEnum.101=취소됨
OrderOuterStatusEnum.102=중단됨

OrderOperationEnum.1=주문 추가
OrderOperationEnum.2=주문 수정
OrderOperationEnum.3=주문 삭제
OrderOperationEnum.4=검토를 위해 제출
OrderOperationEnum.5=주문 검토(레벨 1)
OrderOperationEnum.6=주문 검토(레벨 2)
OrderOperationEnum.7=주문 제출
OrderOperationEnum.8=주문 수락
OrderOperationEnum.9=주문 불가
OrderOperationEnum.10=전자 계약 확인
OrderOperationEnum.11=주문 결제
OrderOperationEnum.12=결제 성공
OrderOperationEnum.13=결제 실패
OrderOperationEnum.14=결제 결과 확인
OrderOperationEnum.15=확인이 수신되지 않음
OrderOperationEnum.16=판매 송장 추가
OrderOperationEnum.17=물류 주문 추가
OrderOperationEnum.18=주문 배송 확인
OrderOperationEnum.19=구매 영수증 추가
OrderOperationEnum.20=주문 접수 확인
OrderOperationEnum.21=영수증 확인
OrderOperationEnum.22=아카이브
OrderOperationEnum.23=주문 중단
OrderOperationEnum.24=주문 취소
OrderOperationEnum.25=배송 시간 조정
OrderOperationEnum.26=주문 단가 수정
OrderOperationEnum.27=주문 배송 수정
OrderOperationEnum.28=주문 환불
OrderOperationEnum.29=그룹 성공
OrderOperationEnum.30=조직 실패
OrderOperationEnum.31=전자 계약 확인
OrderOperationEnum.32=변경주문서
OrderOperationEnum.33=주문변경취소
OrderOperationEnum.34=변경 주문서를 제출하다

OrderProductDeliverTypeEnum.1=물류
OrderProductDeliverTypeEnum.2=자체 배송
OrderProductDeliverTypeEnum.3=배송 필요 없음
OrderProductDeliverTypeEnum.4=물류 + 셀프 픽업

OrderStringEnum.1=송장 번호:
OrderStringEnum.2=수정
OrderStringEnum.3=주문 배송료가 ￥{}에서 ￥{}로 변경됨, 이유: {};
OrderStringEnum.4=수취인 번호:
OrderStringEnum.5=환불 성공
OrderStringEnum.6=환불 실패
OrderStringEnum.7=결제 시간:

BaseTradeProcessEnum.1=여러 배송 프로세스 주문
BaseTradeProcessEnum.2=한 번 지불하고 많은 프로세스를 배송
BaseTradeProcessEnum.3=포인트 주문 프로세스
BaseTradeProcessEnum.4=복수 결제 복수 배송 프로세스
BaseTradeProcessEnum.5=주문 검토 다중 결제 다중 배송 프로세스
BaseTradeProcessEnum.6=판매 후 교체 처리 프로세스 1(입고 및 배송 주문 포함)
BaseTradeProcessEnum.7=판매 후 교환 처리 프로세스 2(수령 및 배송 주문 없음)
BaseTradeProcessEnum.8=판매 후 교체 처리 프로세스 3(오프라인 물류)
BaseTradeProcessEnum.9=판매 후 반품 처리 프로세스 1(입고 및 배송 주문 포함)
BaseTradeProcessEnum.10=판매 ​​후 반품 처리 프로세스 2(오프라인 물류)
BaseTradeProcessEnum.11=판매 후 반품 처리 프로세스 3(영수증 및 배송 메모 없음)
BaseTradeProcessEnum.12=판매 후 반품 처리 프로세스 4(접수 및 배송 주문 없이 플랫폼 검토)
BaseTradeProcessEnum.13=생산 공정 처리 중
BaseTradeProcessEnum.14=생산 수동 출하 처리 프로세스 처리
BaseTradeProcessEnum.15=판매 후 유지 관리 프로세스
BaseTradeProcessEnum.16=수령 및 배송 주문을 통한 다중 결제 및 다중 배송 온라인 물류 프로세스
BaseTradeProcessEnum.17=국경 전자상거래 수입 주문 거래 프로세스
BaseTradeProcessEnum.18=판매 후 교환의 내부 프로세스에 대한 레벨 2 검토
BaseTradeProcessEnum.19=판매 후 반품에 대한 내부 프로세스의 레벨 2 감사
BaseTradeProcessEnum.20=판매 후 유지 보수 내부 프로세스의 레벨 2 감사
BaseTradeProcessEnum.21=주문 구매자 변경 검토 프로세스
BaseTradeProcessEnum.22=주문 검토 계약 확인 다중 결제 다중 배송 프로세스

BaseTradeProcessEnum.1.r=1-주문 제출-->2-주문 배송-->3-주문 접수
BaseTradeProcessEnum.2.r=1-주문 제출-->2-주문 결제-->3-결제 결과 확인-->4-주문 배송 확인-->5-주문 접수 확인
BaseTradeProcessEnum.3.r=1-주문 제출-->2-주문 결제-->3-결제 결과 확인-->4-주문 배송 확인-->5-주문 접수 확인
BaseTradeProcessEnum.4.r=1-주문 제출-->2-주문 결제-->3-결제 결과 확인-->4-주문 배송 확인-->5-주문 접수 확인-->6-주문 결제 -- >7-결제 결과 확인
BaseTradeProcessEnum.5.r=1-주문 제출-->2-주문 확인-->3-주문 결제-->4-결제 결과 확인-->5-주문 배송 확인-->6-주문 접수 확인- >7-주문결제-->8-결제결과 확인
BaseTradeProcessEnum.6.r=1-교환 신청 제출-->2-교환 신청 확인-->3-반품 배송 주문 추가-->4-물류 주문 추가-->5-배송 상품 반품-- >6-반품 영수증 추가-->7-반품 영수증-->8-반품 영수증 확인-->9-교체 인보이스 추가-->10-물류 주문 추가 -->11-교체 및 배송-->12- 교환 영수증 추가-->13-교환 영수증-->14-교환 영수증 확인-->15-판매 완료 확인
BaseTradeProcessEnum.7.r=1-교환 요청 제출-->2-교환 요청 확인-->3-배송 반품-->4-배송 반품-->5-교환 배송-->6-수령 교환 -->7-판매 완료 확인
BaseTradeProcessEnum.8.r=1-구매자가 대체 신청서를 제출함-->2-공급자가 대체 신청서를 확인함-->3-구매자가 반품 배송 메모를 추가함-->4-구매자가 배송을 반품함 - ->5-공급자 반품 영수증 추가-->6-공급자 반품 영수증-->7-공급자가 교체 송장 추가-->8-공급자 교환 배송-->9-구매자가 교체 영수증 추가-->10-구매자 교체 영수증-->11-구매자 확인 후 판매 완료
BaseTradeProcessEnum.9.r=1: 구매자가 반품 신청서 제출 -->2: 공급자가 반품 신청 확인 -->3: 구매자가 반품 배송 추가 -->4: 구매자가 물류 주문 추가--> 5: 구매자가 상품을 반품합니다-->6: 공급자가 새 반품 영수증을 추가합니다-->7: 공급자가 상품을 반품합니다-->8: 구매자가 반품 영수증을 확인합니다-->9: 공급자 환불 --> 10: 구매자가 다음을 확인합니다. 판매 후 완료
BaseTradeProcessEnum.10.r=1: 구매자가 반품 요청 양식 제출 --> 2: 공급자가 반품 요청 양식 확인 --> 3: 구매자가 반품 배송 추가 --> 4: 구매자 반품 배송 -- >5: 공급자 추가 신규 반품 접수-->6: 공급자 반품-->7: 공급자 환불->8: 구매자가 판매 후 완료 확인
BaseTradeProcessEnum.11.r=1: 구매자가 반품 신청서 제출 --> 2: 공급자가 반품 신청 확인 --> 3: 구매자 반품 --> 4: 공급자 반품 --> 5: 공급자 환불 --> 6: 구매자 판매 완료 확인
BaseTradeProcessEnum.12.r=1: 구매자가 반품 신청서 제출 --> 2: 공급업체가 반품 신청 확인 --> 3: 플랫폼에서 반품 신청 확인 --> 4: 구매자 반품 배송 --> 5: 공급자 반품 및 상품 수령 -- > 6: 공급업체 환불 --> 7: 구매자가 판매 후 완료 확인
BaseTradeProcessEnum.13.r=1-생산 주문 제출-->2-생산 주문 확인-->3-처리 송장 추가-->4-물류 주문 추가-->5-생산 주문 상품 보내기-->6-추가 접수처리-->7-제작통지접수-->8-접수확인-->9-완료
BaseTradeProcessEnum.14.r=1-생산 통지서 제출-->2-생산 통지 확인-->5-생산 통지 전달-->7-생산 통지 수령-->9-완료
BaseTradeProcessEnum.15.r=1-검토를 위해 유지관리 신청서 제출-->2-유지관리 신청서 검토(레벨 1)-->3-유지관리 신청서 검토(레벨 2)-->4-유지관리 신청서 확인-- >5- 완료
BaseTradeProcessEnum.16.r=1-주문 제출-->2-결제 주문-->3-결제 결과 확인-->4-판매 인보이스 추가-->5-물류 주문 추가-->6-주문 배송 확인- ->7-구매 영수증 추가-->8-주문 영수증 확인
BaseTradeProcessEnum.17.r=1-주문 제출-->2-주문 결제-->3-주문 배송-->4-주문 접수-->5-주문 평가-->5-주문 완료
BaseTradeProcessEnum.18.r=1-판매 후 교체 신청서 제출 및 검토-->2-판매 후 교체 신청서 검토 수준 1-->3-판매 후 교체 신청서 검토 수준 2-->4 - 애프터 교환 신청 단일 확인
BaseTradeProcessEnum.19.r=1-판매 후 반품 신청서 제출 및 검토-->2-판매 후 반품 신청서 검토 레벨 1-->3-판매 후 반품 신청서 검토 레벨 2-->4 - 판매 후 반품 신청서 확인서
BaseTradeProcessEnum.20.r=1-지원서 제출 및 검토-->2-지원서 1단계 검토-->3-지원서 검토 2단계-->4-지원서 확인 신청서
BaseTradeProcessEnum.21.r=1-주문 검토 제출-->2-주문 검토(레벨 1)-->3-주문 검토(레벨 2)-->4-주문 제출
BaseTradeProcessEnum.22.r=1-주문 제출-->2-주문 확인-->3계약 확인-->4-주문 결제-->5-결제 결과 확인-->6-주문 배송 확인--> 7- 주문접수 확인-->8-주문결제-->9-결제결과 확인

BasePurchaseProcessEnum.1=구매 RFQ 트랜잭션 프로세스
BasePurchaseProcessEnum.2=구매 요청 주문 프로세스
BasePurchaseProcessEnum.3=판매 후 교체 처리 프로세스 1(영수증 및 배송 메모 포함)
BasePurchaseProcessEnum.4=판매 후 교체 처리 프로세스 2(영수증 및 배송 메모 없음)
BasePurchaseProcessEnum.5=판매 후 교체 처리 프로세스 3(오프라인 물류)
BasePurchaseProcessEnum.6=판매 후 반품 처리 프로세스 1(계약서에는 입고 및 배송 메모가 포함됨)
BasePurchaseProcessEnum.7=판매 후 반품 처리 프로세스 2(오프라인 물류 계약)
BasePurchaseProcessEnum.8=판매 후 반품 처리 프로세스 3(영수증 및 배송 메모가 없는 계약)
BasePurchaseProcessEnum.9=판매 후 유지 관리 프로세스
BasePurchaseProcessEnum.10=주문 구매자 변경 검토 프로세스
BasePurchaseProcessEnum.11=구매 입찰 계약 거래 프로세스

BasePurchaseProcessEnum.1.r=1-주문 제출-->2-주문 배송-->3-주문 접수
BasePurchaseProcessEnum.2.r=1-주문 접수-->2-주문 배송-->3-주문 접수
BasePurchaseProcessEnum.3.r=1-판매 후 교환 신청 접수-->2-판매 후 교환 주문 검토-->3-판매 후 교환 주문 확인-->4-반품 배송 처리-->5-반품 처리-->6-교체 영수증 배송 처리-->7-교체 영수증 처리
BasePurchaseProcessEnum.4.r=1-판매 후 교환 주문 신청-->2-판매 후 교환 주문 검토-->3-판매 후 교환 주문 확인-->4-판매 후 완료 확인
BasePurchaseProcessEnum.5.r=1-판매 후 교환 주문 신청-->2-판매 후 교환 주문 검토-->3-판매 후 교환 주문 확인-->4-판매 후 완료 확인
BasePurchaseProcessEnum.6.r=1: 구매자가 반품 요청 양식 제출-->2: 공급자가 반품 요청 양식 확인-->3: 구매자가 반품 배송 메모 추가-->4: 구매자가 물류 메모 추가-->5: 구매자 반품 상품-->6: 공급자가 새 반품 영수증 추가-->7: 공급자 반품 상품-->8: 구매자가 반품 영수증 확인-->9: 구매자 판매 후 완료 확인
BasePurchaseProcessEnum.7.r=1: 구매자가 반품 신청서 제출 --> 2: 공급자가 반품 신청서 확인 --> 3: 구매자가 새 반품 배송 문서 추가 --> 4: 구매자가 배송을 반품 -- >5: 공급자가 새로운 반품 영수증을 추가합니다-->6: 공급자가 상품을 반품합니다-->7: 구매자는 판매 후 완료를 확인합니다.
BasePurchaseProcessEnum.8.r=1: 구매자가 반품 신청서 제출 --> 2: 공급업체가 반품 신청 확인 --> 3: 구매자 반품 배송 --> 4: 공급업체 반품 영수증 --> 5: 구매자가 판매 후 완료 확인
BasePurchaseProcessEnum.9.r=1-검토를 위해 유지관리 신청서 제출-->2-유지관리 신청서 검토(레벨 1)-->3-유지관리 신청서 검토(레벨 2)-->4-유지관리 신청서 확인- -> 5-완료
BasePurchaseProcessEnum.10.r=1-주문 검토 제출-->2-주문 검토(레벨 1)-->3-주문 검토(레벨 2)-->4-주문 제출
BasePurchaseProcessEnum.11.r=1-주문 제출-->2-주문 확인-->3-전자 계약/배송 확인-->4-주문 배송-->5-주문 영수증

DeliveryHistoryStatusEnum.1=배송 예정
DeliveryHistoryStatusEnum.2=확인 예정
DeliveryHistoryStatusEnum.3=수정 예정
DeliveryHistoryStatusEnum.4=확인됨
DeliveryHistoryStatusEnum.5=배송 메모 생성됨
DeliveryHistoryStatusEnum.6=단종
DeliveryHistoryStatusEnum.7=제출됨
DeliveryHistoryStatusEnum.8=수신

DeliveryOrderOuterStatusEnum.1=제출됨
DeliveryOrderOuterStatusEnum.2=수신됨
DeliveryOrderOuterStatusEnum.3=단종

ReceiveOrderOuterStatusEnum.1=제출됨
ReceiveOrderOuterStatusEnum.3=단종
ReceiveOrderOuterStatusEnum.4=품질 생성

DeliveryOperationEnum.1=배송 계획 생성
DeliveryOperationEnum.2=배송 일정 변경
DeliveryOperationEnum.3=배송 계획 확인
DeliveryOperationEnum.4=배송 주문 생성
DeliveryOperationEnum.5=배송 알림 변경
DeliveryOperationEnum.6=배송 주문 확인
DeliveryOperationEnum.7=무효
DeliveryOperationEnum.8=배송 주문 생성
DeliveryOperationEnum.9=배송 메모 변경
DeliveryOperationEnum.10=배송 메모 확인
DeliveryOperationEnum.11=배송 주문 생성
DeliveryOperationEnum.12=배송 주문 변경
DeliveryOperationEnum.13=영수증 확인
DeliveryOperationEnum.14=배송 계획 제출

InboundOrderEnum.1=주문 번호
InboundOrderEnum.2=인바운드 주문 번호
InboundOrderEnum.3=인바운드 주문 번호

CurrencyTypeEnum.1=CNY
CurrencyTypeEnum.2=USD
CurrencyTypeEnum.3=엔
CurrencyTypeEnum.4=유로

QualityTypeEnum.1=수신 품질 검사
QualityTypeEnum.2=샘플 품질 검사
QualityTypeEnum.3=시범 품질 검사

InspectionTypeEnum.1=검사 면제
InspectionTypeEnum.2=전체 검사
InspectionTypeEnum.3=샘플링 검사

BatchJudgmentTypeEnum.1=적격
BatchJudgmentTypeEnum.2=부분적 자격
BatchJudgmentTypeEnum.3=양보 받기
BatchJudgmentTypeEnum.4=거부

InspectionJudgmentTypeEnum.1=적격
InspectionJudgmentTypeEnum.2=자격 없음

ReturnTypeEnum.1=리베이트
ReturnTypeEnum.2=환불

HandleTypeEnum.1=유지보수
HandleTypeEnum.2=현장 수리
HandleTypeEnum.3=재작업
HandleTypeEnum.4=스크랩

QualityStatusEnum.1=품질 검사 진행 중
QualityStatusEnum.2=품질 검사 완료
QualityStatusEnum.3=판매 후 주문이 생성되었습니다.

QualityOrderOperationEnum.1=품질 검사 주문 추가
QualityOrderOperationEnum.2=품질 검사 순서 수정
QualityOrderOperationEnum.3=품질 검사 완료
QualityOrderOperationEnum.4=판매 후 주문 생성

QualityOrderStatusEnum.1=제출됨
QualityOrderStatusEnum.2=품질 검사 주문이 생성되었습니다.
QualityOrderStatusEnum.3=품질 검사 완료
QualityOrderStatusEnum.4=판매 후 주문이 생성됨

QualityOrderResourceEnum.1=신규
QualityOrderResourceEnum.2=영수증 생성
QualityOrderResourceEnum.3=샘플 요청 양식 생성

QualityOrderAfterSalesStatusEnum.0=
QualityOrderAfterSalesStatusEnum.1=생성되지 않음
QualityOrderAfterSalesStatusEnum.2=생성됨

ProblemDegreeTypeEnum.1=긴급
ProblemDegreeTypeEnum.2=일반 비상
ProblemDegreeTypeEnum.3=긴급하지 않음

EightDRectificationSourceTypeEnum.1=영수증 품질 검사
EightDRectificationSourceTypeEnum.2=프로세스 발견
EightDRectificationSourceTypeEnum.3=샘플 품질 검사
EightDRectificationSourceTypeEnum.4=시험 품질 검사

EightDRectificationSourceOrderEnum.1=영수증
EightDRectificationSourceOrderEnum.2=품질 검사 목록
EightDRectificationSourceOrderEnum.3=주문

EightDRectificationExternalStatusEnum.1=8D 제출 예정
EightDRectificationExternalStatusEnum.2=ICA 피드백 대기 중
EightDRectificationExternalStatusEnum.3=ICA 피드백 검토 대기 중
EightDRectificationExternalStatusEnum.4=ICA 피드백 검토 실패
EightDRectificationExternalStatusEnum.5=PCA 피드백 대기 중
EightDRectificationExternalStatusEnum.6=검토 대기 중인 PCA 피드백
EightDRectificationExternalStatusEnum.7=PCA 피드백 검토 실패
EightDRectificationExternalStatusEnum.99=완료
EightDRectificationExternalStatusEnum.-1=취소됨

EightDRectificationInternalStatusEnum.1=8D 제출 예정
EightDRectificationInternalStatusEnum.2=8D 제출
EightDRectificationInternalStatusEnum.3=ICA 피드백 검토를 위해 제출 보류 중
EightDRectificationInternalStatusEnum.4=ICA 피드백 검토 보류(레벨 1)
EightDRectificationInternalStatusEnum.5=ICA 피드백 검토 실패(레벨 ​​1)
EightDRectificationInternalStatusEnum.6=ICA 피드백 검토 보류(레벨 2)
EightDRectificationInternalStatusEnum.7=ICA 피드백 검토 실패(레벨 ​​2)
EightDRectificationInternalStatusEnum.8=ICA 피드백 확인 예정
EightDRectificationInternalStatusEnum.9=ICA 피드백 검토 실패
EightDRectificationInternalStatusEnum.10=ICA 피드백 확인됨
EightDRectificationInternalStatusEnum.11=검토 PCA 피드백을 위해 제출 보류 중
EightDRectificationInternalStatusEnum.12=PCA 피드백 보류 중(레벨 1)
EightDRectificationInternalStatusEnum.13=PCA 피드백 검토 실패(레벨 ​​1)
EightDRectificationInternalStatusEnum.14=PCA 피드백 보류 중(레벨 2)
EightDRectificationInternalStatusEnum.15=PCA 피드백 검토 실패(레벨 ​​2)
EightDRectificationInternalStatusEnum.16=PCA 피드백 확인 예정
EightDRectificationInternalStatusEnum.17=PCA 피드백 검토 실패
EightDRectificationInternalStatusEnum.99=완료
EightDRectificationInternalStatusEnum.-1=취소됨

EightDRectificationOperateTypeEnum.1=검토를 위해 제출할 ICA 피드백
EightDRectificationOperateTypeEnum.2=ICA 피드백 보류 중(레벨 1)
EightDRectificationOperateTypeEnum.3=ICA 피드백 보류 중(레벨 2)
EightDRectificationOperateTypeEnum.4=ICA 피드백 확인 예정
EightDRectificationOperateTypeEnum.5=검토 보류 중인 PCA 피드백
EightDRectificationOperateTypeEnum.6=PCA 피드백 보류 중(레벨 1)
EightDRectificationOperateTypeEnum.7=PCA 피드백 대기 중(레벨 2)
EightDRectificationOperateTypeEnum.8=PCA 피드백 대기 중
EightDRectificationOperateTypeEnum.9=8D 제출 예정(SRM)
EightDRectificationOperateTypeEnum.10=8D(B2B) 제출 예정

EightDOOperationStateEnum.0=8D 추가
EightDOOperationStateEnum.1=커밋 8 D
EightDOOperationStateEnum.2=ICA 피드백
EightDOOperationStateEnum.3=ICA 피드백 검토
EightDOOperationStateEnum.4=PCA 피드백
EightDOOperationStateEnum.5=PCA 피드백 검토
EightDOOperationStateEnum.6=검토를 위해 ICA 피드백 제출
EightDOOperationStateEnum.7=ICA 피드백 검토(레벨 1)
EightDOOperationStateEnum.8=ICA 피드백 검토(레벨 2)
EightDOOperationStateEnum.9=ICA 피드백 확인
EightDOOperationStateEnum.10=검토를 위해 PCA 피드백 제출
EightDOOperationStateEnum.11=PCA 피드백 검토(레벨 1)
EightDOOperationStateEnum.12=PCA 피드백 검토(레벨 2)
EightDOOperationStateEnum.13=PCA 피드백 확인
EightDOOperationStateEnum.14=수정 8 D
EightDOOperationStateEnum.-1=8D 취소

EightTaskStepStatusEnum.1=8D 보고서 추가
EightTaskStepStatusEnum.2=ICA 피드백
EightTaskStepStatusEnum.3=ICA 피드백 검토
EightTaskStepStatusEnum.4=PCA 피드백
EightTaskStepStatusEnum.5=PCA 피드백 검토

EightIcaTaskStepStatusEnum.1=검토를 위해 ICA 피드백 제출
EightIcaTaskStepStatusEnum.2=ICA 피드백 검토(레벨 1)
EightIcaTaskStepStatusEnum.3=ICA 피드백 검토(레벨 2)
EightIcaTaskStepStatusEnum.4=ICA 피드백 확인

EightPcaTaskStepStatusEnum.1=검토를 위해 PCA 피드백 제출
EightPcaTaskStepStatusEnum.2=PCA 피드백 검토(레벨 1)
EightPcaTaskStepStatusEnum.3=PCA 피드백 검토(레벨 2)
EightPcaTaskStepStatusEnum.4=PCA 피드백 확인

QualityProcessEnum.1=품질 관리 ICA 감사 프로세스 - 레벨 1 감사
QualityProcessEnum.2=품질 관리 ICA 감사 프로세스 - 레벨 2 감사
QualityProcessEnum.3=품질 관리 ICA 감사 프로세스 - 레벨 3 감사
QualityProcessEnum.4=품질 관리 ICA 감사 프로세스 - 레벨 4 감사
QualityProcessEnum.5=품질 관리 PCA 감사 프로세스 - 레벨 1 감사
QualityProcessEnum.6=품질 관리 PCA 감사 프로세스 - 레벨 2 감사
QualityProcessEnum.7=품질 관리 PCA 감사 프로세스 - 레벨 3 감사
QualityProcessEnum.8=품질 관리 PCA 감사 프로세스 - 레벨 4 감사

QualityProcessEnum.1.r=1- 확인해야 하는 ICA 피드백
QualityProcessEnum.2.r=1- 검토를 위해 제출할 ICA 피드백 --> 확인해야 할 ICA 피드백
QualityProcessEnum.3.r=1- 검토를 위해 제출할 ICA 피드백 --> 검토할 ICA 피드백(레벨 1) --> 확인할 ICA 피드백
QualityProcessEnum.4.r=1- 검토를 위해 제출할 ICA 피드백 --> 검토할 ICA 피드백(레벨 1) --> 검토할 ICA 피드백(레벨 2) --> 확인할 ICA 피드백
QualityProcessEnum.5.r=1- 확인될 PCA 피드백
QualityProcessEnum.6.r=1- 검토를 위해 제출할 PCA 피드백 --> 확인할 PCA 피드백
QualityProcessEnum.7.r=1- 검토를 위해 제출할 PCA 피드백 --> 검토할 PCA 피드백(레벨 1) --> 확인할 PCA 피드백
QualityProcessEnum.8.r=1- 검토를 위해 제출할 PCA 피드백 --> 검토할 PCA 피드백(레벨 1) --> 검토할 PCA 피드백(레벨 2) --> 확인할 PCA 피드백

QualityProcessTypeEnum.1=품질 관리 ICA 감사 프로세스
QualityProcessTypeEnum.2=품질 관리 PCA 감사 프로세스

QualityProcessStatusEnum.0=비활성화
QualityProcessStatusEnum.1=활성화

QualityAfterSalesStatusEnum.1=미완성
QualityAfterSalesStatusEnum.2=완료됨

SaleOrderOperateTypeEnum.1=검토를 위해 제출할 주문
SaleOrderOperateTypeEnum.2=검토할 주문(레벨 1)
SaleOrderOperateTypeEnum.3=검토할 주문(레벨 2)
SaleOrderOperateTypeEnum.4=확인할 주문
SaleOrderOperateTypeEnum.5=결제 결과 주문 확정
SaleOrderOperateTypeEnum.6=판매 송장 주문 추가 예정
SaleOrderOperateTypeEnum.7=물류 주문 추가
SaleOrderOperateTypeEnum.8=배송 주문 확인
SaleOrderOperateTypeEnum.9=주문 접수 확인
SaleOrderOperateTypeEnum.10=보관할 주문
SaleOrderOperateTypeEnum.11=평가할 주문

PurchaseOrderOperateTypeEnum.1=주문 검토 대기 중
PurchaseOrderOperateTypeEnum.2=보류 주문(레벨 1)
PurchaseOrderOperateTypeEnum.3=검토 주문 보류(레벨 2)
PurchaseOrderOperateTypeEnum.4=확인 주문 보류 중
PurchaseOrderOperateTypeEnum.5=전자 계약 주문 확인 대기 중
PurchaseOrderOperateTypeEnum.6=결제 주문 대기 중
PurchaseOrderOperateTypeEnum.7=추가할 구매 영수증 주문
PurchaseOrderOperateTypeEnum.8=확인 영수증 주문 대기 중
PurchaseOrderOperateTypeEnum.9=보관할 주문
PurchaseOrderOperateTypeEnum.10=검토할 주문

TradeDeliveryNoticeOperateTypeEnum.1=배송 알림 보류(SRM)
TradeDeliveryNoticeOperateTypeEnum.2=제출할 배송 통지서(B2B)

TradeDeliveryNoticeCollaborationOperateTypeEnum.1=확인 대기 중인 배송 알림

TradeDeliveryPlanCollaborationOperateTypeEnum.1=배송 계획을 확인하려면

TradeDeliveryPlanOperateTypeEnum.1=제출할 배송 계획(SRM)
TradeDeliveryPlanOperateTypeEnum.2=배송 계획 제출(B2B)

TradeReceiptOperateTypeEnum.1=보류 중인 배달 메모

SaleOrderPlatformOperateTypeEnum.1=결제 결과 확인 대기 주문

InvoiceKindEnum.0=모두
InvoiceKindEnum.1=VAT 일반 송장
InvoiceKindEnum.2=VAT 특별 송장

InvoiceTypeEnum.0=모두
InvoiceTypeEnum.1=기업
InvoiceTypeEnum.2=개인

PurchaseOrderExportFileEnum.tableName=구매 주문
PurchaseOrderExportFileEnum.orderNo=주문 번호
PurchaseOrderExportFileEnum.digest=주문 요약
PurchaseOrderExportFileEnum.vendorMemberName=공급 회원 이름
PurchaseOrderExportFileEnum.createTime=주문 시간
PurchaseOrderExportFileEnum.totalAmount=총 주문
PurchaseOrderExportFileEnum.orderType=주문 유형 이름
PurchaseOrderExportFileEnum.outerStatus=외부 상태 이름
PurchaseOrderExportFileEnum.vendorInnerStatus=내부 상태 이름

SalePurchaseOrderExportFileEnum.tableName=영업 주문
SalePurchaseOrderExportFileEnum.orderNo=주문 번호
SalePurchaseOrderExportFileEnum.digest=주문 요약
SalePurchaseOrderExportFileEnum.buyerMemberName=구매 회원 이름
SalePurchaseOrderExportFileEnum.createTime=주문 시간
SalePurchaseOrderExportFileEnum.totalAmount=총 주문
SalePurchaseOrderExportFileEnum.orderType=주문 유형 이름
SalePurchaseOrderExportFileEnum.transferOrderNo=송장 주문 번호
SalePurchaseOrderExportFileEnum.outerStatus=외부 상태 이름
SalePurchaseOrderExportFileEnum.vendorInnerStatus=내부 상태 이름