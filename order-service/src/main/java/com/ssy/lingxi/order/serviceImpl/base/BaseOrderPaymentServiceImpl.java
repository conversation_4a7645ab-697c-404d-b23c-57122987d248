package com.ssy.lingxi.order.serviceImpl.base;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ssy.lingxi.common.enums.DepositTypeEnum;
import com.ssy.lingxi.common.enums.order.LogisticsStatusEnum;
import com.ssy.lingxi.common.enums.order.OrderPaymentSettlementStatusEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.*;
import com.ssy.lingxi.component.base.enums.FundModeEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRightSpendTypeEnum;
import com.ssy.lingxi.component.base.enums.order.*;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.rest.model.resp.eos.GoldPriceResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.member.api.model.resp.MemberBrandInfoResp;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.resp.OrderDepositResp;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.*;
import com.ssy.lingxi.order.model.bo.*;
import com.ssy.lingxi.order.model.dto.*;
import com.ssy.lingxi.order.model.req.basic.OrderPaymentReq;
import com.ssy.lingxi.order.model.req.vendor.VendorTransferOrderNewReq;
import com.ssy.lingxi.order.model.resp.basic.OrderPayTypeDetailResp;
import com.ssy.lingxi.order.model.resp.buyer.BuyerOrderPayNodeQueryResp;
import com.ssy.lingxi.order.model.resp.buyer.BuyerOrderPaymentDetailResp;
import com.ssy.lingxi.order.model.resp.buyer.BuyerPayResultDetailResp;
import com.ssy.lingxi.order.model.resp.mobile.MobileOrderPaymentDetailResp;
import com.ssy.lingxi.order.model.resp.vendor.VendorOrderPaymentDetailResp;
import com.ssy.lingxi.order.model.resp.vendor.VendorOrderSubPaymentDetailResp;
import com.ssy.lingxi.order.repository.OrderLogisticsRepository;
import com.ssy.lingxi.order.repository.OrderPaymentRepository;
import com.ssy.lingxi.order.repository.OrderRepository;
import com.ssy.lingxi.order.repository.SubOrderPaymentRepository;
import com.ssy.lingxi.order.service.base.*;
import com.ssy.lingxi.order.service.feign.IMemberFeignService;
import com.ssy.lingxi.order.service.feign.IPayFeignService;
import com.ssy.lingxi.order.service.feign.ISettleAccountFeignService;
import com.ssy.lingxi.order.service.web.IOrderCreationService;
import com.ssy.lingxi.order.service.web.OrderService;
import com.ssy.lingxi.order.util.QRCodeUtil;
import com.ssy.lingxi.pay.api.enums.PayChannelEnum;
import com.ssy.lingxi.pay.api.enums.ServiceTypeEnum;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.api.feign.IWeiQiFuPayFeign;
import com.ssy.lingxi.pay.api.model.req.assetAccount.OrderPayInfoReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.UnFrozenAccountBalanceReq;
import com.ssy.lingxi.pay.api.model.req.weiQiFuPay.GoodsReq;
import com.ssy.lingxi.pay.api.model.req.weiQiFuPay.WeiQiFuPayRefundReq;
import com.ssy.lingxi.pay.api.model.req.weiQiFuPay.WeiQiFuPayReq;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.AccountBalancePaidResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.AccountPayChannelResultResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.OrderFeeInfoResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.UnPayOrderInfo;
import com.ssy.lingxi.pay.api.model.resp.weiQiFuPay.WeiQiFuPayRefundResp;
import com.ssy.lingxi.pay.api.model.resp.weiQiFuPay.WeiQiFuPayResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum.ALIPAY;

/**
 * 订单支付记录相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-20
 */
@Slf4j
@Service
public class BaseOrderPaymentServiceImpl implements IBaseOrderPaymentService {
    private static final Logger logger = LoggerFactory.getLogger(BaseOrderPaymentServiceImpl.class);
    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderPaymentRepository orderPaymentRepository;

    @Resource
    private IBaseOrderService baseOrderService;

    @Resource
    private IBaseOrderHistoryService baseOrderHistoryService;

    @Resource
    private IBaseOrderProcessService baseOrderProcessService;

    @Resource
    private IBaseOrderTaskService baseOrderTaskService;

    @Resource
    private IBaseOrderProductService baseOrderProductService;

    @Resource
    private IPayFeignService payFeignService;

    @Resource
    private IMemberFeignService memberFeignService;

    @Resource
    private ISettleAccountFeignService settleAccountFeignService;

    @Resource
    private IBaseCacheService baseCacheService;

    @Resource
    private IBaseOrderScheduleService baseOrderScheduleService;

    @Resource
    private IWeiQiFuPayFeign weiQiFuPayFeign;

    @Resource
    private EosApiService eosApiService;
    @Resource
    private SubOrderPaymentRepository subOrderPaymentRepository;
    @Resource
    private IAssetAccountFeign assetAccountFeign;

    @Resource
    private IOrderProcessFeign orderProcessFeign;
    @Resource
    private OrderService orderService;
    @Resource
    private IOrderCreationService baseOrderCreationService;

    @Resource
    private OrderLogisticsRepository orderLogisticsDORepository;

    /**
     * 校验、保存订单支付记录，调用方要保存 OrderDO
     *
     * @param order 订单
     * @param payType 订单支付方式
     * @param payChannel 订单支付渠道
     * @param payNodes 交易流程中设置的支付环节
     * @param orderDepositConfig 订单资金归集配置
     * @return 资金归集模式
     */
    @Override
    public OrderPaymentCheckBO checkOrderPayment(OrderDO order, Integer payType, Integer payChannel, List<PayNodeBO> payNodes, OrderDepositResp orderDepositConfig) {
        //Step 1: 如果交易流程中的支付环节为空，表示不需要支付，返回
        if(CollectionUtils.isEmpty(payNodes) || payType.equals(OrderPayTypeEnum.DOES_NOT_NEED.getCode())) {
            order.setPayments(new HashSet<>());
            return new OrderPaymentCheckBO();
        }

        Integer fundMode;
        if(payType.equals(OrderPayTypeEnum.SETTLEMENT.getCode())) {
            OrderPayTypeDetailResp settlementResult = settleAccountFeignService.findSettlementPaySetting(order);
            if(Objects.isNull(settlementResult) || settlementResult.getPayChannels().stream().noneMatch(channel -> channel.getPayChannel().equals(payChannel))) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SETTLEMENT_PAYMENT_SETTING_NOT_SET_YET);
            }

            fundMode = settlementResult.getFundMode();
        } else {
            //Step 2: 查询平台后台 - 会员支付策略配置，获得资金归集模式，拼接结算支付查询
            List<PlatformPayTypeBO> platformPayTypes = baseOrderProcessService.findPlatformMemberPayment(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId(), payChannel.equals(OrderPayTypeEnum.SETTLEMENT.getCode()));
            if(CollectionUtils.isEmpty(platformPayTypes)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_SETTING_NOT_SET_YET);
            }

            fundMode = platformPayTypes.stream().filter(platformPayType -> platformPayType.getPayType().equals(payType) && platformPayType.getChannels().stream().anyMatch(platformPayChannel -> platformPayChannel.getPayChannel().equals(payChannel))).map(PlatformPayTypeBO::getFundMode).findFirst().orElse(0);
            if(NumberUtil.isNullOrZero(fundMode)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_SETTING_MISMATCH);
            }
        }

        //Step 3: 计算每批次支付的金额
        //Map<Integer, BigDecimal> batchAmount = averagePayNodeAmount(order.getTotalAmount(), payNodes);

        //Step 4: 生成支付记录
        List<OrderPaymentDO> orderPayments = payNodes.stream().map(payNode -> {
            OrderPaymentDO orderPayment = new OrderPaymentDO();
            orderPayment.setOrder(order);
            orderPayment.setPayTime(null);
            orderPayment.setConfirmTime(null);
            orderPayment.setSerialNo(payNode.getSerialNo());
            orderPayment.setBatchNo(payNode.getBatchNo());
            orderPayment.setPayType(payType);
            orderPayment.setPayChannel(payChannel);
            orderPayment.setFundMode(fundMode);
            orderPayment.setPayNode(payNode.getPayNode());
            orderPayment.setPayRate(payNode.getPayRate());
            //BigDecimal payAmount = batchAmount.getOrDefault(payNode.getBatchNo(), BigDecimal.ZERO);
            BigDecimal payAmount = order.getTotalAmount();
            orderPayment.setPayAmount(payAmount);
            orderPayment.setDepositType(orderDepositConfig.getDepositType());
            orderPayment.setAdvancePayRate(DepositTypeEnum.PROPORTION.getCode().equals(orderDepositConfig.getDepositType()) ? orderDepositConfig.getDepositAmount() : BigDecimal.ZERO);
            orderPayment.setAdvancePayAmount(DepositTypeEnum.FIXED_AMOUNT.getCode().equals(orderDepositConfig.getDepositType()) ? orderDepositConfig.getDepositAmount().multiply(BigDecimal.valueOf(order.getTotalProductCount())) : BigDecimal.ZERO);
            orderPayment.setTotalWeight(order.getTotalWeight());
            orderPayment.setLaborFee(order.getTotalCraftPrice().subtract(order.getCraftDiscountAmount()));
            orderPayment.setOtherFee(order.getTotalServicePrice());

            //支付金额为0的，设置为完成状态
            if(payAmount.compareTo(BigDecimal.ZERO) == 0) {
                orderPayment.setPayTime(LocalDateTime.now());
                orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode());
                orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_PAYMENT_CONFIRMED.getCode());
                orderPayment.setOuterStatus(OrderOuterStatusEnum.ACCOMPLISHED.getCode());
            } else {
                orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode());
                orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode());
                orderPayment.setOuterStatus(OrderOuterStatusEnum.TO_PAY.getCode());
            }
            orderPayment.setSettlementStatus(OrderPaymentSettlementStatusEnum.NONE.getCode());
            orderPayment.setTradeNo("");
            orderPayment.setRefundNo("");
            orderPayment.setVouchers(new ArrayList<>());
            return orderPayment;
        }).collect(Collectors.toList());

        //Step 6: 保存
        orderPaymentRepository.saveAll(orderPayments);

        //Step 7: 设置关联，调用方保存OrderDO
        order.setPayments(new HashSet<>(orderPayments));

        //Step 8: 返回信息
        return new OrderPaymentCheckBO(fundMode, orderPayments.stream().anyMatch(payment -> payment.getPayRate().compareTo(BigDecimal.ONE) == 0));
    }

    /**
     * 校验、保存（积分兑换）订单支付记录，调用方要保存 OrderDO
     *
     * @param order      订单
     * @param payType    订单支付方式
     * @param payChannel 订单支付渠道
     * @param payNodes   交易流程中设置的支付环节
     * @return 资金归集模式
     */
    @Override
    public Integer checkPointsOrderPayment(OrderDO order, Integer payType, Integer payChannel, List<PayNodeBO> payNodes) {
        //Step 1: 如果交易流程中的支付环节为空，表示不需要支付，返回
        if(CollectionUtils.isEmpty(payNodes) || payType.equals(OrderPayTypeEnum.DOES_NOT_NEED.getCode())) {
            order.setPayments(new HashSet<>());
            return 0;
        }

        //Step 2: 计算每批次支付的金额
        Map<Integer, BigDecimal> batchAmount = averagePayNodeAmount(order.getTotalAmount(), payNodes);

        //Step 3: 生成支付记录
        List<OrderPaymentDO> orderPayments = payNodes.stream().map(payNode -> {
            OrderPaymentDO orderPayment = new OrderPaymentDO();
            orderPayment.setOrder(order);
            orderPayment.setPayTime(null);
            orderPayment.setConfirmTime(null);
            orderPayment.setSerialNo(payNode.getSerialNo());
            orderPayment.setBatchNo(payNode.getBatchNo());
            orderPayment.setPayType(payType);
            orderPayment.setPayChannel(payChannel);
            //积分订单的资金归集模式暂时设置为“平台代收”，待支付时才根据接口参数进行修改
            orderPayment.setFundMode(FundModeEnum.PLATFORM_EXCHANGE.getCode());
            orderPayment.setPayNode(payNode.getPayNode());
            orderPayment.setPayRate(payNode.getPayRate());
            BigDecimal payAmount = batchAmount.getOrDefault(payNode.getBatchNo(), BigDecimal.ZERO);
            orderPayment.setPayAmount(payAmount);
            //支付金额为0的，设置为完成状态
            if(payAmount.compareTo(BigDecimal.ZERO) == 0) {
                orderPayment.setPayTime(LocalDateTime.now());
                orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode());
                orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_PAYMENT_CONFIRMED.getCode());
                orderPayment.setOuterStatus(OrderOuterStatusEnum.ACCOMPLISHED.getCode());
            } else {
                orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode());
                orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode());
                orderPayment.setOuterStatus(OrderOuterStatusEnum.TO_PAY.getCode());
            }
            orderPayment.setSettlementStatus(OrderPaymentSettlementStatusEnum.NONE.getCode());
            orderPayment.setTradeNo("");
            orderPayment.setRefundNo("");
            orderPayment.setVouchers(new ArrayList<>());
            return orderPayment;
        }).collect(Collectors.toList());

        //Step 4: 保存
        orderPaymentRepository.saveAll(orderPayments);

        //Step 5: 设置关联，调用方保存OrderDO
        order.setPayments(new HashSet<>(orderPayments));
        return null;
    }

    /**
     * 校验、保存B2B订单支付记录，调用方要保存OrderDO
     *
     * @param order        订单
     * @param payNodes     供应商交易流程设置中的支付环节
     * @param payments     接口参数
     * @param isCreate     是否是新增，true-新增，false-修改
     * @return 校验结果
     */
    @Override
    public Void checkOrderCreatePayment(OrderDO order, List<PayNodeBO> payNodes, List<OrderPaymentReq> payments, boolean isCreate) {
        //Step 1: 如果不需要支付，返回
        if(CollectionUtils.isEmpty(payNodes)) {
            orderPaymentRepository.deleteByOrder(order);
            order.setPayments(new HashSet<>());
            return null;
        }

        //Step 2: 查询平台后台 - 会员支付策略配置，获得资金归集模式，拼接结算支付方式查询
        List<PlatformPayTypeBO> platformPayTypes = baseOrderProcessService.findPlatformMemberPayment(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId(), payments.stream().allMatch(p -> p.getPayType().equals(OrderPayTypeEnum.SETTLEMENT.getCode())));
        if(CollectionUtils.isEmpty(platformPayTypes)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_SETTING_NOT_SET_YET);
        }

        //Step 3: 判断支付记录的次数是否与供应商交易流程规则配置中的次数相匹配，忽略支付比例配置为0的支付环节
        if(CollectionUtils.isEmpty(payments) || payments.size() != payNodes.stream().filter(payNode -> payNode.getPayRate().compareTo(BigDecimal.ZERO) > 0).count() || payments.stream().anyMatch(payment -> payNodes.stream().noneMatch(payNode -> payNode.getBatchNo().equals(payment.getBatchNo())))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENTS_MISMATCH_WITH_TRADE_PROCESS);
        }

        //Step 4: 判断支付方式与支付渠道，是否在会员支付策略中
        if(payments.stream().anyMatch(payment -> platformPayTypes.stream().noneMatch(platformPayType -> platformPayType.getPayType().equals(payment.getPayType()) || platformPayType.getChannels().stream().anyMatch(payChannel -> payChannel.getPayChannel().equals(payment.getPayChannel()))))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENTS_MISMATCH_WITH_PLATFORM_PAYMENTS);
        }

        //Step 5: 计算每批次支付的金额
        Map<Integer, BigDecimal> batchAmount = averagePayNodeAmount(order.getTotalAmount(), payNodes);

        //Step 6: 生成支付记录（创建订单由于前端传递的是支付次数已经过滤掉了支付比例为0的，所以这里需要补齐）
        List<OrderPaymentDO> orderPayments = payNodes.stream().map(payNode -> {
            OrderPaymentReq paymentVO = payments.stream().filter(p -> p.getBatchNo().equals(payNode.getBatchNo())).findFirst().orElse(null);

            OrderPaymentDO orderPayment = new OrderPaymentDO();
            orderPayment.setOrder(order);
            orderPayment.setPayTime(null);
            orderPayment.setConfirmTime(null);
            orderPayment.setSerialNo(payNode.getSerialNo());
            orderPayment.setBatchNo(payNode.getBatchNo());
            orderPayment.setPayNode(payNode.getPayNode());
            orderPayment.setPayRate(payNode.getPayRate());

            if(paymentVO == null) {
                orderPayment.setPayType(OrderPayTypeEnum.DOES_NOT_NEED.getCode());
                orderPayment.setPayChannel(OrderPayTypeEnum.DOES_NOT_NEED.getCode());
                orderPayment.setFundMode(FundModeEnum.PLATFORM_EXCHANGE.getCode());
            } else {
                orderPayment.setPayType(paymentVO.getPayType());
                orderPayment.setPayChannel(paymentVO.getPayChannel());
                platformPayTypes.stream().filter(platformPayType -> platformPayType.getPayType().equals(paymentVO.getPayType()) && platformPayType.getChannels().stream().anyMatch(platformPayChannel -> platformPayChannel.getPayChannel().equals(paymentVO.getPayChannel()))).findFirst().ifPresent(platformPayType -> orderPayment.setFundMode(platformPayType.getFundMode()));
            }

            orderPayment.setPayAmount(batchAmount.getOrDefault(payNode.getBatchNo(), BigDecimal.ZERO));
            //支付金额为0的，设置为完成状态
            if(orderPayment.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
                orderPayment.setPayTime(LocalDateTime.now());
                orderPayment.setConfirmTime(LocalDateTime.now());
                orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode());
                orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_PAYMENT_CONFIRMED.getCode());
                orderPayment.setOuterStatus(OrderOuterStatusEnum.ACCOMPLISHED.getCode());
            } else {
                orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode());
                orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode());
                orderPayment.setOuterStatus(OrderOuterStatusEnum.TO_PAY.getCode());
            }
            orderPayment.setSettlementStatus(OrderPaymentSettlementStatusEnum.NONE.getCode());
            orderPayment.setTradeNo("");
            orderPayment.setRefundNo("");
            orderPayment.setVouchers(new ArrayList<>());
            return orderPayment;
        }).collect(Collectors.toList());

        //如果是修改，删除原有的
        if(!isCreate) {
            orderPaymentRepository.deleteByOrder(order);
        }

        //保存并设置关联
        orderPaymentRepository.saveAll(orderPayments);
        order.setPayments(new HashSet<>(orderPayments));

        return null;
    }

    /**
     * 修改新增订单支付记录
     *
     * @param order    订单
     * @param payments 接口参数
     * @return 校验结果
     */
    @Override
    public Void updateOrderCreatePayment(OrderDO order, List<OrderPaymentReq> payments) {
        //Step 1: 查询平台后台 - 会员支付策略配置，获得资金归集模式
        List<PlatformPayTypeBO> platformPayTypes = baseOrderProcessService.findPlatformMemberPayment(order.getVendorMemberId(), order.getVendorRoleId());
        if(CollectionUtils.isEmpty(platformPayTypes)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_SETTING_NOT_SET_YET);
        }

        //Step 2: 如果不需要支付，返回
        if(CollectionUtils.isEmpty(platformPayTypes)) {
            orderPaymentRepository.deleteByOrder(order);
            order.setPayments(new HashSet<>());
            return null;
        }

        //Step 3: 判断支付方式与支付渠道，是否在会员支付策略中
        if(CollectionUtils.isEmpty(payments) || payments.stream().anyMatch(payment -> platformPayTypes.stream().noneMatch(platformPayType -> platformPayType.getPayType().equals(payment.getPayType()) || platformPayType.getChannels().stream().anyMatch(payChannel -> payChannel.getPayChannel().equals(payment.getPayChannel()))))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENTS_MISMATCH_WITH_PLATFORM_PAYMENTS);
        }

        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrder(order);
        if(payments.stream().anyMatch(payment -> orderPayments.stream().noneMatch(p -> p.getBatchNo().equals(payment.getBatchNo())))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        //Step 4: 修改支付方式、支付渠道、资金归集模式
        List<OrderPaymentDO> updatePayments = new ArrayList<>();
        for (OrderPaymentDO orderPayment : orderPayments) {
            OrderPaymentReq paymentVO = payments.stream().filter(p -> p.getBatchNo().equals(orderPayment.getBatchNo())).findFirst().orElse(null);
            if(paymentVO == null) {
                continue;
            }

            orderPayment.setPayType(paymentVO.getPayType());
            orderPayment.setPayChannel(paymentVO.getPayChannel());
            platformPayTypes.stream().filter(platformPayType -> platformPayType.getPayType().equals(paymentVO.getPayType()) && platformPayType.getChannels().stream().anyMatch(platformPayChannel -> platformPayChannel.getPayChannel().equals(paymentVO.getPayChannel()))).findFirst().ifPresent(platformPayType -> orderPayment.setFundMode(platformPayType.getFundMode()));
            updatePayments.add(orderPayment);
        }

        orderPaymentRepository.saveAll(updatePayments);

        return null;
    }

    /**
     * 根据订单中的订单总额，修改支付记录中的支付金额
     *
     * @param order 订单
     * @return 修改结果
     */
    @Override
    public Void vendorUpdatePaymentRate(OrderDO order) {
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrderAndPayAmountGreaterThan(order, BigDecimal.ZERO, Sort.by("batchNo").ascending());
        if(CollectionUtils.isEmpty(orderPayments)) {
            return null;
        }

        //Step 1: 计算每批次支付的金额
        List<PayNodeBO> payNodes = orderPayments.stream().map(payment -> new PayNodeBO(null , payment.getBatchNo(), payment.getPayNode(), payment.getPayRate())).collect(Collectors.toList());
        Map<Integer, BigDecimal> batchAmount = averagePayNodeAmount(order.getTotalAmount(), payNodes);

        //Step 2: 修改每批次的支付金额
        orderPayments.forEach(payment -> {
            payment.setPayAmount(batchAmount.getOrDefault(payment.getBatchNo(), payment.getPayAmount()));
            //支付金额为0的，设置为完成状态
            if(payment.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
                payment.setPayTime(LocalDateTime.now());
                payment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode());
                payment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_PAYMENT_CONFIRMED.getCode());
                payment.setOuterStatus(OrderOuterStatusEnum.ACCOMPLISHED.getCode());
            }
        });

        orderPaymentRepository.saveAll(orderPayments);
        return vendorUpdatePaymentRate(order, payNodes);
    }

    /**
     * 根据订单中的订单总额，修改支付记录中的支付金额
     *
     * @param order    订单
     * @param payNodes 支付环节（其中的支付比例要为小数，保留4位数）
     * @return 修改结果
     */
    @Override
    public Void vendorUpdatePaymentRate(OrderDO order, List<PayNodeBO> payNodes) {
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrderAndPayAmountGreaterThan(order, BigDecimal.ZERO, Sort.by("batchNo").ascending());
        if(CollectionUtils.isEmpty(orderPayments)) {
            return null;
        }

        if(payNodes.stream().anyMatch(payNode -> orderPayments.stream().noneMatch(payment -> payment.getBatchNo().equals(payNode.getBatchNo())))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        if(payNodes.stream().map(PayNodeBO::getPayRate).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(BigDecimal.ONE) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_TRADE_PROCESS_PAYMENT_RATE_SUM_MUST_EQUAL_TO_100);
        }

        //Step 1: 计算每批次支付的金额
        Map<Integer, BigDecimal> batchAmount = averagePayNodeAmount(order.getTotalAmount(), payNodes);

        //Step 2: 修改每批次的支付金额
        orderPayments.forEach(payment -> {
            payNodes.stream().filter(payNode -> payNode.getBatchNo().equals(payment.getBatchNo())).findFirst().ifPresent(payNode -> payment.setPayRate(payNode.getPayRate()));
            payment.setPayAmount(batchAmount.getOrDefault(payment.getBatchNo(), payment.getPayAmount()));
            //如果修改后的支付金额为0，则修改状态为已经完成
            if(payment.getPayAmount().compareTo(BigDecimal.ZERO) == 0 && Objects.isNull(payment.getPayTime())) {
                payment.setPayTime(LocalDateTime.now());
                payment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode());
                payment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_PAYMENT_CONFIRMED.getCode());
                payment.setOuterStatus(OrderOuterStatusEnum.ACCOMPLISHED.getCode());
            }
        });

        orderPaymentRepository.saveAll(orderPayments);
        return null;
    }

    /**
     * （转单）复制交易记录
     *
     * @param order          订单
     * @param payNodes       供应商交易流程设置中的支付环节
     * @param payTypeDetails 上游供应商支付方式列表
     * @return 操作结果
     */
    @Override
    public Void transferOrderPayments(OrderDO order, List<PayNodeBO> payNodes, List<OrderPayTypeDetailResp> payTypeDetails) {
        //Step 1: 如果不需要支付，返回
        if(CollectionUtils.isEmpty(payNodes)) {
            order.setPayments(new HashSet<>());
            return null;
        }

        //Step 2: 计算每批次支付的金额
        Map<Integer, BigDecimal> batchAmount = averagePayNodeAmount(order.getTotalAmount(), payNodes);

        Integer defaultFundMode = payTypeDetails.get(0).getFundMode();
        Integer defaultPayType = payTypeDetails.get(0).getPayType();
        Integer defaultPayChannel = payTypeDetails.get(0).getPayChannels().get(0).getPayChannel();

        //Step 3: 生成支付记录
        List<OrderPaymentDO> orderPayments = payNodes.stream().map(payNode -> {
            OrderPaymentDO orderPayment = new OrderPaymentDO();
            orderPayment.setOrder(order);
            orderPayment.setPayTime(null);
            orderPayment.setConfirmTime(null);
            orderPayment.setSerialNo(payNode.getSerialNo());
            orderPayment.setBatchNo(payNode.getBatchNo());
            orderPayment.setFundMode(defaultFundMode);
            orderPayment.setPayType(defaultPayType);
            orderPayment.setPayChannel(defaultPayChannel);
            orderPayment.setPayNode(payNode.getPayNode());
            orderPayment.setPayRate(payNode.getPayRate());
            orderPayment.setPayAmount(batchAmount.getOrDefault(payNode.getBatchNo(), BigDecimal.ZERO));
            //支付金额为0的，设置为完成状态
            if(orderPayment.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
                orderPayment.setPayTime(LocalDateTime.now());
                orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode());
                orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_PAYMENT_CONFIRMED.getCode());
                orderPayment.setOuterStatus(OrderOuterStatusEnum.ACCOMPLISHED.getCode());
            } else {
                orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode());
                orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode());
                orderPayment.setOuterStatus(OrderOuterStatusEnum.TO_PAY.getCode());
            }
            orderPayment.setSettlementStatus(OrderPaymentSettlementStatusEnum.NONE.getCode());
            orderPayment.setTradeNo("");
            orderPayment.setRefundNo("");
            orderPayment.setVouchers(new ArrayList<>());
            return orderPayment;
        }).collect(Collectors.toList());

        //保存并设置关联
        orderPaymentRepository.saveAll(orderPayments);
        order.setPayments(new HashSet<>(orderPayments));

        return null;
    }

    /**
     * 修改B2B订单时，查询支付记录
     *
     * @param order 订单
     * @return 查询结果
     */
    @Override
    public List<BuyerOrderPayNodeQueryResp> findBusinessOrderPayments(OrderDO order) {
        return orderPaymentRepository.findByOrderAndPayAmountGreaterThan(order, BigDecimal.ZERO, Sort.by("batchNo").ascending()).stream().map(orderPayment -> {
            BuyerOrderPayNodeQueryResp queryVO = new BuyerOrderPayNodeQueryResp();
            queryVO.setBatchNo(orderPayment.getBatchNo());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(orderPayment.getOuterStatus()));
            queryVO.setPayAmount(NumberUtil.formatAmount(orderPayment.getPayAmount()));
            queryVO.setPayNode(OrderPayNodeEnum.getNameByCode(orderPayment.getPayNode()));
            queryVO.setPayType(orderPayment.getPayType());
            queryVO.setPayChannel(orderPayment.getPayChannel());
            queryVO.setPayRate(NumberUtil.formatPayRate(orderPayment.getPayRate()));
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * 平均计算每批次支付的金额
     * @param totalAmount 订单总金额
     * @param payNodes 支付环节设置，支付环节中的支付比例保留4位小数
     * @return 每批次支付的金额
     */
    private Map<Integer, BigDecimal> averagePayNodeAmount(BigDecimal totalAmount, List<PayNodeBO> payNodes) {
        Integer maxBatch = payNodes.stream().map(PayNodeBO::getBatchNo).max(Comparator.comparingInt(Integer::intValue)).orElse(0);
        Map<Integer, BigDecimal> map = payNodes.stream().filter(payNode -> payNode.getBatchNo().compareTo(maxBatch) < 0).collect(Collectors.toMap(PayNodeBO::getBatchNo, payNode -> totalAmount.multiply(payNode.getPayRate()).setScale(2, RoundingMode.HALF_UP)));
        map.put(maxBatch, totalAmount.subtract(map.values().stream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
        return map;
    }

    /**
     * 采购商 - 订单支付记录列表
     *
     * @param order 订单
     * @return 支付记录列表
     */
    @Override
    public List<BuyerOrderPaymentDetailResp> findBuyerOrderPayments(OrderDO order) {
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrderAndPayAmountGreaterThan(order, BigDecimal.ZERO, Sort.by("batchNo").ascending());
        if(CollectionUtils.isEmpty(orderPayments)) {
            return new ArrayList<>();
        }

        //只有最小未支付的次数，才显示“支付”
        Integer batchNo = orderPayments.stream().filter(orderPayment -> orderPayment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode()) || orderPayment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode()) || (orderPayment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode()) && orderPayment.getOuterStatus().equals(OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode()))).map(OrderPaymentDO::getBatchNo).min(Comparator.comparingInt(Integer::intValue)).orElse(0);
        AtomicInteger tag = new AtomicInteger(1);
        List<BuyerOrderPaymentDetailResp> mobileOrderPaymentDetailResps = new ArrayList<>();
        final BigDecimal[] payRate = {BigDecimal.ZERO};

        orderPayments.stream().sorted(Comparator.comparingInt(OrderPaymentDO::getBatchNo)).forEach(payment -> {
            int andIncrement = tag.getAndIncrement();
            BigDecimal decimal = order.getTotalAmount();
            BuyerOrderPaymentDetailResp buyerOrderPaymentDetailResp = convertBuyerOrderPaymentDetailResp(payment, batchNo, decimal);
            buyerOrderPaymentDetailResp.setPayTypeName("全款支付");
            buyerOrderPaymentDetailResp.setPayRate("100");
            if ((CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode()) && andIncrement > 1) || ObjectUtil.isEmpty(payment.getDepositType())) {
                return;
            }
            BigDecimal amount = DepositTypeEnum.FIXED_AMOUNT.getCode().equals(payment.getDepositType()) ? payment.getAdvancePayAmount() : payment.getAdvancePayRate().divide(new BigDecimal(100)).multiply(order.getTotalAmount());
            //amount = ObjectUtil.isNotEmpty(payment.getServiceFee()) ? amount.add(payment.getServiceFee()) : amount;
            if (andIncrement == 1 && CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode())) {
                payRate[0] = amount.divide(order.getTotalAmount(), 10, RoundingMode.HALF_UP).setScale(2, RoundingMode.DOWN);
                buyerOrderPaymentDetailResp = convertBuyerOrderPaymentDetailResp(payment, batchNo, amount);
                buyerOrderPaymentDetailResp.setPayTypeName("定金支付");
                buyerOrderPaymentDetailResp.setPayRate(NumberUtil.formatAmount(payRate[0].multiply(new BigDecimal(100))));
            }
            if (andIncrement == 2 && CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode())) {
                BigDecimal payRateTwo = new BigDecimal(1).subtract(payRate[0]);
                buyerOrderPaymentDetailResp = convertBuyerOrderPaymentDetailResp(payment, batchNo, order.getTotalAmount().subtract(amount));
                buyerOrderPaymentDetailResp.setPayTypeName("尾款支付");
                buyerOrderPaymentDetailResp.setPayRate(NumberUtil.formatAmount(payRateTwo.multiply(new BigDecimal(100))));
            }
            mobileOrderPaymentDetailResps.add(buyerOrderPaymentDetailResp);
        });
        return mobileOrderPaymentDetailResps;
    }

    /**
     * 支付信息转换s
     * @param payment
     * @param payBatchNo
     * @param payAmount
     * @return
     */
    private BuyerOrderPaymentDetailResp convertBuyerOrderPaymentDetailResp(OrderPaymentDO payment, Integer payBatchNo, BigDecimal payAmount){
        BuyerOrderPaymentDetailResp buyerOrderPaymentDetailResp = new BuyerOrderPaymentDetailResp();
        buyerOrderPaymentDetailResp.setPaymentId(payment.getId());
        buyerOrderPaymentDetailResp.setBatchNo(payment.getBatchNo());
        buyerOrderPaymentDetailResp.setPayType(payment.getPayType());
        buyerOrderPaymentDetailResp.setPayTypeName(OrderPayTypeEnum.getNameByCode(payment.getPayType()));
        buyerOrderPaymentDetailResp.setPayChannel(payment.getPayChannel());
        buyerOrderPaymentDetailResp.setPayChannelName(OrderPayChannelEnum.getNameByCode(payment.getPayChannel()));
        buyerOrderPaymentDetailResp.setPayNode(OrderPayNodeEnum.getNameByCode(payment.getPayNode()));
        buyerOrderPaymentDetailResp.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(payment.getBuyerInnerStatus()));
        buyerOrderPaymentDetailResp.setOuterStatusName(payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()) ? VendorInnerStatusEnum.getNameByCode(payment.getVendorInnerStatus()) : OrderOuterStatusEnum.getNameByCode(payment.getOuterStatus()));
        buyerOrderPaymentDetailResp.setPayRate(NumberUtil.formatPayRate(payment.getPayRate()));
        buyerOrderPaymentDetailResp.setPayAmount(NumberUtil.formatAmount(payAmount));
        buyerOrderPaymentDetailResp.setVouchers(payment.getVouchers());
        buyerOrderPaymentDetailResp.setShowPayment(payment.getBatchNo().equals(payBatchNo));
        buyerOrderPaymentDetailResp.setShowView(!payment.getVouchers().isEmpty());
        buyerOrderPaymentDetailResp.setServiceFee(payment.getServiceFee() != null ? payment.getServiceFee() : BigDecimal.ZERO);
        List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdAndTradeNoAndSubStatus(payment.getId(), payment.getTradeNo(), SubPaymentOrderStatusEnum.SUCCESS.getCode());
        if(!CollectionUtils.isEmpty(subOrderPaymentDOS)){
            List<VendorOrderSubPaymentDetailResp> subPaymentDetailResps = subOrderPaymentDOS.stream().map(subOrderPaymentDO -> {
                VendorOrderSubPaymentDetailResp subPaymentDetailResp = new VendorOrderSubPaymentDetailResp();
                subPaymentDetailResp.setSubStatus(subOrderPaymentDO.getSubStatus());
                subPaymentDetailResp.setChannelTradeNo(subOrderPaymentDO.getChannelTradeNo());
                subPaymentDetailResp.setWeight(subOrderPaymentDO.getWeight());
                subPaymentDetailResp.setPayChannel(subOrderPaymentDO.getPayChannel());
                subPaymentDetailResp.setPayAmount(subOrderPaymentDO.getPayAmount());
                subPaymentDetailResp.setBatchNo(subOrderPaymentDO.getBatchNo());
                subPaymentDetailResp.setConfirmTime(subOrderPaymentDO.getConfirmTime());
                subPaymentDetailResp.setRefundNo(subOrderPaymentDO.getRefundNo());
                subPaymentDetailResp.setFundMode(subOrderPaymentDO.getFundMode());
                subPaymentDetailResp.setPayType(subOrderPaymentDO.getPayType());
                subPaymentDetailResp.setPayTime(subOrderPaymentDO.getPayTime());
                subPaymentDetailResp.setPayChannelName(OrderPayChannelEnum.getNameByCode(subOrderPaymentDO.getPayChannel()));
                subPaymentDetailResp.setSubStatusName(SubPaymentOrderStatusEnum.getNameByCode(subOrderPaymentDO.getSubStatus()));
                subPaymentDetailResp.setFundModeName(FundModeEnum.getNameByCode(subOrderPaymentDO.getFundMode()));
                OrderPayChannelEnum payChannelEnum = OrderPayChannelEnum.parse(subPaymentDetailResp.getPayChannel());
                switch (payChannelEnum){
                    case WEI_QI_FU:
                        buyerOrderPaymentDetailResp.setWeiqifuPayAmount(subOrderPaymentDO.getPayAmount());
                        buyerOrderPaymentDetailResp.setCurrentPayAmount(subOrderPaymentDO.getPayAmount());
                        break;
                    case ACCOUNT_BALANCE:
                        buyerOrderPaymentDetailResp.setUseAmount(subOrderPaymentDO.getPayAmount());
                        buyerOrderPaymentDetailResp.setUseAmountToGoleWeight(subOrderPaymentDO.getWeight());
                        break;
                    case CREDIT:
                        buyerOrderPaymentDetailResp.setUseMaterialStock(subOrderPaymentDO.getWeight());
                        break;
                    case OFFLINE:
                        buyerOrderPaymentDetailResp.setOfflinePayAmount(subOrderPaymentDO.getPayAmount());
                        buyerOrderPaymentDetailResp.setCurrentPayAmount(subOrderPaymentDO.getPayAmount());
                        break;
                }
                return subPaymentDetailResp;
            }).collect(Collectors.toList());
            buyerOrderPaymentDetailResp.setSubPaymentDetails(subPaymentDetailResps);
        }
        return buyerOrderPaymentDetailResp;
    }

    /**
     * 供应商 - 订单支付记录列表
     *
     * @param order 订单
     * @return 支付记录列表
     */
    @Override
    public List<VendorOrderPaymentDetailResp> findVendorOrderPayments(OrderDO order) {
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrderAndPayAmountGreaterThan(order, BigDecimal.ZERO, Sort.by("batchNo").ascending());
        if(CollectionUtils.isEmpty(orderPayments)) {
            return new ArrayList<>();
        }

        //只有最小未确认支付结果的次数，才显示“确认支付结果”
        Integer batchNo = orderPayments.stream().filter(orderPayment -> orderPayment.getFundMode().equals(FundModeEnum.DIRECT_TO_ACCOUNT.getCode()) && orderPayment.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode()) && orderPayment.getOuterStatus().equals(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode())).map(OrderPaymentDO::getBatchNo).min(Comparator.comparingInt(Integer::intValue)).orElse(0);

        List<VendorOrderPaymentDetailResp> mobileOrderPaymentDetailResps = new ArrayList<>();

        // 支付记录
        if (!CollectionUtils.isEmpty(order.getPayments()) && order.getRelationId() == 0L) {
            //只有最小未支付的次数，才显示“支付”
            AtomicInteger tag = new AtomicInteger(1);
            final BigDecimal[] payRate = {BigDecimal.ZERO};
            orderPayments.stream().sorted(Comparator.comparingInt(OrderPaymentDO::getBatchNo)).forEach(payment -> {
                int andIncrement = tag.getAndIncrement();
                BigDecimal decimal = ObjectUtil.isNotEmpty(payment.getServiceFee()) ? order.getTotalAmount().add(payment.getServiceFee()) : order.getTotalAmount();
                VendorOrderPaymentDetailResp vendorOrderPaymentDetailResp = getVendorOrderPaymentDetailResp(order, payment, batchNo, decimal);
                vendorOrderPaymentDetailResp.setPayRate("100");
                if (CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode()) && andIncrement > 1) {
                    return;
                }

                // 定金金额
                BigDecimal amount = DepositTypeEnum.FIXED_AMOUNT.getCode().equals(payment.getDepositType()) ? payment.getAdvancePayAmount() : payment.getAdvancePayRate().divide(new BigDecimal(100)).multiply(order.getTotalAmount());
                //amount = ObjectUtil.isNotEmpty(payment.getServiceFee()) ? amount.add(payment.getServiceFee()) : amount;

                if (andIncrement == 1 && CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode())) {
                    payRate[0] = amount.divide(order.getTotalAmount(), 10, RoundingMode.HALF_UP).setScale(2, RoundingMode.DOWN);
                    vendorOrderPaymentDetailResp = getVendorOrderPaymentDetailResp(order, payment, batchNo, amount);
                    vendorOrderPaymentDetailResp.setPayTypeName("定金支付");
                    vendorOrderPaymentDetailResp.setPayRate(NumberUtil.formatAmount(payRate[0].multiply(new BigDecimal(100))));
                }
                if (andIncrement == 2 && CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode())) {
                    BigDecimal payRateTwo = new BigDecimal(1).subtract(payRate[0]);
                    vendorOrderPaymentDetailResp = getVendorOrderPaymentDetailResp(order, payment, batchNo, order.getTotalAmount().subtract(amount));
                    vendorOrderPaymentDetailResp.setPayTypeName("尾款支付");
                    vendorOrderPaymentDetailResp.setPayRate(NumberUtil.formatAmount(payRateTwo.multiply(new BigDecimal(100))));
                }
                mobileOrderPaymentDetailResps.add(vendorOrderPaymentDetailResp);
            });
        }
        return mobileOrderPaymentDetailResps;
    }

    private VendorOrderPaymentDetailResp getVendorOrderPaymentDetailResp(OrderDO order, OrderPaymentDO payment, Integer batchNo, BigDecimal payAmount) {
        VendorOrderPaymentDetailResp detailVO = new VendorOrderPaymentDetailResp();
        detailVO.setPaymentId(payment.getId());
        detailVO.setBatchNo(payment.getBatchNo());
        detailVO.setPayType(payment.getPayType());
        detailVO.setPayTypeName(OrderPayTypeEnum.getNameByCode(payment.getPayType()));
        detailVO.setPayChannel(payment.getPayChannel());
        detailVO.setPayChannelName(ObjectUtil.isEmpty(payment.getConfirmTime()) ? "" : OrderPayChannelEnum.getNameByCode(payment.getPayChannel()));
        detailVO.setPayNode(CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode()) ? "全款支付" : OrderPayNodeEnum.getNameByCode(payment.getPayNode()));
        detailVO.setInnerStatusName(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode().equals(payment.getVendorInnerStatus()) && CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode()) ? "预估" : VendorInnerStatusEnum.getNameByCode(payment.getVendorInnerStatus()));
        //供应商的外部状态为采购商的内部状态
        detailVO.setOuterStatusName(payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()) ? BuyerInnerStatusEnum.getNameByCode(payment.getBuyerInnerStatus()) : OrderOuterStatusEnum.getNameByCode(payment.getOuterStatus()));
        detailVO.setPayRate(NumberUtil.formatPayRate(payment.getPayRate()));
        detailVO.setPayAmount(String.valueOf(payAmount));
        detailVO.setVouchers(payment.getVouchers());
        detailVO.setName(payment.getName());
        detailVO.setBankAccount(payment.getBankAccount());
        detailVO.setBankDeposit(payment.getBankDeposit());
        detailVO.setShowConfirm(payment.getBatchNo().equals(batchNo));
        detailVO.setShowView(!CollectionUtils.isEmpty(payment.getVouchers()));
        detailVO.setServiceFee(payment.getServiceFee());
        List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdAndTradeNo(payment.getId(), payment.getTradeNo());
        if(!CollectionUtils.isEmpty(subOrderPaymentDOS)){
            List<VendorOrderSubPaymentDetailResp> subPaymentDetailResps = subOrderPaymentDOS.stream().map(subOrderPaymentDO -> {
                VendorOrderSubPaymentDetailResp subPaymentDetailResp = new VendorOrderSubPaymentDetailResp();
                subPaymentDetailResp.setSubStatus(subOrderPaymentDO.getSubStatus());
                subPaymentDetailResp.setChannelTradeNo(subOrderPaymentDO.getChannelTradeNo());
                subPaymentDetailResp.setWeight(subOrderPaymentDO.getWeight());
                subPaymentDetailResp.setPayChannel(subOrderPaymentDO.getPayChannel());
                subPaymentDetailResp.setPayAmount(subOrderPaymentDO.getPayAmount());
                subPaymentDetailResp.setBatchNo(subOrderPaymentDO.getBatchNo());
                subPaymentDetailResp.setConfirmTime(subOrderPaymentDO.getConfirmTime());
                subPaymentDetailResp.setRefundNo(subOrderPaymentDO.getRefundNo());
                subPaymentDetailResp.setFundMode(subOrderPaymentDO.getFundMode());
                subPaymentDetailResp.setPayType(subOrderPaymentDO.getPayType());
                subPaymentDetailResp.setPayTime(subOrderPaymentDO.getPayTime());
                subPaymentDetailResp.setPayChannelName(OrderPayChannelEnum.getNameByCode(subOrderPaymentDO.getPayChannel()));
                subPaymentDetailResp.setSubStatusName(SubPaymentOrderStatusEnum.getNameByCode(subOrderPaymentDO.getSubStatus()));
                subPaymentDetailResp.setFundModeName(FundModeEnum.getNameByCode(subOrderPaymentDO.getFundMode()));
                OrderPayChannelEnum payChannelEnum = OrderPayChannelEnum.parse(subPaymentDetailResp.getPayChannel());
                switch (payChannelEnum){
                    case WEI_QI_FU:
                        detailVO.setWeiqifuPayAmount(subOrderPaymentDO.getPayAmount());
                        detailVO.setCurrentPayAmount(subOrderPaymentDO.getPayAmount());
                        break;
                    case ACCOUNT_BALANCE:
                        detailVO.setUseAmount(subOrderPaymentDO.getPayAmount());
                        detailVO.setUseAmountToGoleWeight(subOrderPaymentDO.getWeight());
                        break;
                    case CREDIT:
                        detailVO.setUseMaterialStock(subOrderPaymentDO.getWeight());
                        break;
                    case OFFLINE:
                        detailVO.setOfflinePayAmount(subOrderPaymentDO.getPayAmount());
                        detailVO.setCurrentPayAmount(subOrderPaymentDO.getPayAmount());
                        break;
                }
                return subPaymentDetailResp;
            }).collect(Collectors.toList());
            detailVO.setSubPaymentDetails(subPaymentDetailResps);
        }
        return detailVO;
    }

    /**
     * 平台后台 - 订单支付记录列表
     *
     * @param order 订单
     * @return 支付记录列表
     */
    @Override
    public List<VendorOrderPaymentDetailResp> findPlatformOrderPayments(OrderDO order) {
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrderAndPayAmountGreaterThan(order, BigDecimal.ZERO, Sort.by("batchNo").ascending());
        if(CollectionUtils.isEmpty(orderPayments)) {
            return new ArrayList<>();
        }

        //只有最小未确认支付结果的次数，才显示“确认支付结果”
        Integer batchNo = orderPayments.stream().filter(orderPayment -> orderPayment.getFundMode().equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) && orderPayment.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode()) && orderPayment.getOuterStatus().equals(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode())).map(OrderPaymentDO::getBatchNo).min(Comparator.comparingInt(Integer::intValue)).orElse(0);

        return orderPayments.stream().map(payment -> {
            VendorOrderPaymentDetailResp detailVO = new VendorOrderPaymentDetailResp();
            detailVO.setPaymentId(payment.getId());
            detailVO.setBatchNo(payment.getBatchNo());
            detailVO.setPayType(payment.getPayType());
            detailVO.setPayTypeName(OrderPayTypeEnum.getNameByCode(payment.getPayType()));
            detailVO.setPayChannel(payment.getPayChannel());
            detailVO.setPayChannelName(OrderPayChannelEnum.getNameByCode(payment.getPayChannel()));
            detailVO.setPayNode(OrderPayNodeEnum.getNameByCode(payment.getPayNode()));
            detailVO.setInnerStatusName(VendorInnerStatusEnum.getNameByCode(payment.getVendorInnerStatus()));
            //供应商的外部状态为采购商的内部状态
            detailVO.setOuterStatusName(payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()) ? BuyerInnerStatusEnum.getNameByCode(payment.getBuyerInnerStatus()) : OrderOuterStatusEnum.getNameByCode(payment.getOuterStatus()));
            detailVO.setPayRate(NumberUtil.formatPayRate(payment.getPayRate()));
            detailVO.setPayAmount(NumberUtil.formatAmount(payment.getPayAmount()));
            detailVO.setVouchers(payment.getVouchers());
            detailVO.setShowConfirm(payment.getBatchNo().equals(batchNo));
            detailVO.setShowView(!CollectionUtils.isEmpty(payment.getVouchers()));
            List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdAndTradeNo(payment.getId(), payment.getTradeNo());
            if(!CollectionUtils.isEmpty(subOrderPaymentDOS)) {
                List<VendorOrderSubPaymentDetailResp> subPaymentDetailResps = subOrderPaymentDOS.stream().map(subOrderPaymentDO -> {
                    VendorOrderSubPaymentDetailResp subPaymentDetailResp = new VendorOrderSubPaymentDetailResp();
                    subPaymentDetailResp.setSubStatus(subOrderPaymentDO.getSubStatus());
                    subPaymentDetailResp.setChannelTradeNo(subOrderPaymentDO.getChannelTradeNo());
                    subPaymentDetailResp.setWeight(subOrderPaymentDO.getWeight());
                    subPaymentDetailResp.setPayChannel(subOrderPaymentDO.getPayChannel());
                    subPaymentDetailResp.setPayAmount(subOrderPaymentDO.getPayAmount());
                    subPaymentDetailResp.setBatchNo(subOrderPaymentDO.getBatchNo());
                    subPaymentDetailResp.setConfirmTime(subOrderPaymentDO.getConfirmTime());
                    subPaymentDetailResp.setRefundNo(subOrderPaymentDO.getRefundNo());
                    subPaymentDetailResp.setFundMode(subOrderPaymentDO.getFundMode());
                    subPaymentDetailResp.setPayType(subOrderPaymentDO.getPayType());
                    subPaymentDetailResp.setPayTime(subOrderPaymentDO.getPayTime());
                    subPaymentDetailResp.setPayChannelName(OrderPayChannelEnum.getNameByCode(subOrderPaymentDO.getPayChannel()));
                    subPaymentDetailResp.setSubStatusName(SubPaymentOrderStatusEnum.getNameByCode(subOrderPaymentDO.getSubStatus()));
                    subPaymentDetailResp.setFundModeName(FundModeEnum.getNameByCode(subOrderPaymentDO.getFundMode()));
                    OrderPayChannelEnum payChannelEnum = OrderPayChannelEnum.parse(subPaymentDetailResp.getPayChannel());
                    switch (payChannelEnum) {
                        case WEI_QI_FU:
                            detailVO.setWeiqifuPayAmount(subOrderPaymentDO.getPayAmount());
                            detailVO.setCurrentPayAmount(subOrderPaymentDO.getPayAmount());
                            break;
                        case ACCOUNT_BALANCE:
                            detailVO.setUseAmount(subOrderPaymentDO.getPayAmount());
                            detailVO.setUseAmountToGoleWeight(subOrderPaymentDO.getWeight());
                            break;
                        case CREDIT:
                            detailVO.setUseMaterialStock(subOrderPaymentDO.getWeight());
                            break;
                        case OFFLINE:
                            detailVO.setOfflinePayAmount(subOrderPaymentDO.getPayAmount());
                            detailVO.setCurrentPayAmount(subOrderPaymentDO.getPayAmount());
                            break;
                    }
                    return subPaymentDetailResp;
                }).collect(Collectors.toList());
                detailVO.setSubPaymentDetails(subPaymentDetailResps);
            }
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * 订单创建后，判断、执行支付流程
     *
     * @param order 订单
     * @return 执行结果
     */
    @Override
    public OrderPaymentBO startOrderPaySerialTasks(OrderDO order, GoldPriceResp goldPrice) {
        if(CollectionUtils.isEmpty(order.getPayments()) || !order.getTask().getNextPay() || NumberUtil.isNullOrZero(order.getTask().getPayStartNode()) || NumberUtil.isNullOrZero(order.getTask().getPayEndNode())) {
            return new OrderPaymentBO();
        }

        //找出支付次数之间的支付记录
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrderAndBatchNoGreaterThanEqualAndBatchNoLessThanEqual(order, order.getTask().getPayStartNode(), order.getTask().getPayEndNode());
        return completeOrderPaySerialTasks(order, orderPayments, goldPrice);
    }

    /**
     * 订单执行过程中，支付流程执行之前，判断是否跳过支付流程
     *
     * @param order 订单
     * @return 执行结果
     */
    @Override
    public OrderPaymentBO jumpOverOrderPaySerialTasks(OrderDO order) {
        if(!order.getTask().getNextPay() || NumberUtil.isNullOrZero(order.getTask().getNextSerialNo()) || !StringUtils.hasLength(order.getTask().getTaskId())) {
            return new OrderPaymentBO();
        }

        //根据支付批次查询支付记录列表
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrderAndSerialNo(order, order.getTask().getNextSerialNo());
        return completeOrderPaySerialTasks(order, orderPayments, null);
    }

    /**
     * 更加支付记录中的支付金额，判断是否执行支付流程
     * @param order  订单
     * @param orderPayments 支付记录列表
     * @return 执行结果
     */
    private OrderPaymentBO completeOrderPaySerialTasks(OrderDO order, List<OrderPaymentDO> orderPayments, GoldPriceResp goldPrice) {
        OrderPaymentBO paymentBO = new OrderPaymentBO();
        //如果没有需要支付的记录，返回
        if(CollectionUtils.isEmpty(orderPayments)) {
            return paymentBO;
        }

        //Step 1: 如果所有支付记录的金额都为0，跳过支付流程
        if(orderPayments.stream().allMatch(p -> p.getPayAmount().compareTo(BigDecimal.ZERO) == 0)) {
            ProcessTaskBO taskResult = baseOrderTaskService.execOrderPayTasks(order, 2, Stream.of(OrderConstant.AGREE, OrderConstant.AGREE).collect(Collectors.toList()));
            return paymentBO;
        }

        if (ObjectUtil.isEmpty(goldPrice)) {
            goldPrice = eosApiService.getGoldPrice();
        }

        //Step 2: 找出需要最小支付的次数，返回
        GoldPriceResp finalGoldPrice = goldPrice;
        orderPayments.stream().filter(p -> p.getPayAmount().compareTo(BigDecimal.ZERO) > 0).min(Comparator.comparingInt(OrderPaymentDO::getBatchNo)).ifPresent(orderPayment -> {
            paymentBO.setRequirePayment(true);
            paymentBO.setBatchNo(orderPayment.getBatchNo());
            if (CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode())) {
                if (DepositTypeEnum.PROPORTION.getCode().equals(orderPayment.getDepositType())) {
                    paymentBO.setPayAmount(orderPayment.getTotalWeight().multiply(finalGoldPrice.getJj()).multiply(orderPayment.getAdvancePayRate().divide(BigDecimal.valueOf(100))).setScale(2, RoundingMode.HALF_UP));
                } else {
                    paymentBO.setPayAmount(orderPayment.getAdvancePayAmount());
                }
            } else {
                paymentBO.setPayAmount(orderPayment.getPayAmount());
            }
            paymentBO.setPayType(orderPayment.getPayType());
            paymentBO.setPayChannel(orderPayment.getPayChannel());
        });

        return paymentBO;
    }

    /**
     * 订单支付后，判断、执行支付流程，调用方要保存 List<OrderDO>
     *
     * @param orders         订单
     * @param orderPaymentList 所有支付记录列表
     * @param batchNo       支付次数
     * @param paySuccess    是否支付成功
     * @param tradeNo       交易订单号
     */
    @Override
    public void execOrderPaySerialTasks(List<OrderDO> orders, List<OrderPaymentDO> orderPaymentList, Integer batchNo, boolean paySuccess, String tradeNo) {
        execOrderPaySerialTasks(orders, orderPaymentList, batchNo, paySuccess, tradeNo, null);
    }

    /**
     * 订单支付后，判断、执行支付流程，调用方要保存 List<OrderDO>
     *
     * @param orders         订单
     * @param orderPaymentList 所有支付记录列表
     * @param batchNo       支付次数
     * @param paySuccess    是否支付成功
     * @param tradeNo       交易订单号
     * @param channelTradeNo       渠道交易订单号
     */
    @Override
    public void execOrderPaySerialTasks(List<OrderDO> orders, List<OrderPaymentDO> orderPaymentList, Integer batchNo, boolean paySuccess, String tradeNo, String channelTradeNo) {
        if(CollectionUtils.isEmpty(orderPaymentList) || orders.stream().anyMatch(order -> !order.getTask().getNextPay() || NumberUtil.isNullOrZero(order.getTask().getPayStartNode()) || NumberUtil.isNullOrZero(order.getTask().getPayEndNode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        //循环每个订单
        for (OrderDO order : orders) {
            //Step 1: 找出订单的支付记录， 支付次数之间的支付记录，与当前的支付记录
            List<OrderPaymentDO> orderPayments = orderPaymentList.stream().filter(payment -> payment.getOrder().getId().equals(order.getId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(orderPayments)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
            }
            List<OrderPaymentDO> currentPayments = orderPayments.stream().filter(payment -> payment.getBatchNo().compareTo(order.getTask().getPayStartNode()) >= 0 && payment.getBatchNo().compareTo(order.getTask().getPayEndNode()) <= 0).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(currentPayments)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
            }
            //如果未找到流程定义的支付次数相匹配的支付记录，说明上一次支付处于“支付中”（等待回调通知）的状态
            OrderPaymentDO orderPayment = currentPayments.stream().filter(payment -> payment.getBatchNo().equals(batchNo)).findFirst().orElse(null);
            LocalDateTime now = LocalDateTime.now();
            if(orderPayment == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_IS_PROCESSING);
            }

            //Step 2: 修改支付时间，当前支付记录的状态
            orderPayment.setPayTime(now);

            //记录交易订单号，用于退款
            if(StringUtils.hasLength(tradeNo)) {
                orderPayment.setTradeNo(tradeNo);
            }

            // 记录渠道交易订单号，用于小程序发货
            if(StringUtils.hasLength(channelTradeNo)) {
                orderPayment.setChannelTradeNo(channelTradeNo);
            }

            //如果支付成功，记录“确认到账”时间
            if(paySuccess) {
                orderPayment.setConfirmTime(now);
                List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdAndTradeNoAndSubStatus(orderPayment.getId(), tradeNo, SubPaymentOrderStatusEnum.PENDING.getCode());
                for (SubOrderPaymentDO subOrderPaymentDO : subOrderPaymentDOS) {
                    subOrderPaymentDO.setSubStatus(SubPaymentOrderStatusEnum.SUCCESS.getCode());
                    subOrderPaymentDO.setPayTime(now);
                    if(OrderPayChannelEnum.WEI_QI_FU.getCode().equals(subOrderPaymentDO.getPayChannel())){
                        subOrderPaymentDO.setChannelTradeNo(channelTradeNo);
                    }else{
                        subOrderPaymentDO.setChannelTradeNo(tradeNo);
                    }
                    subOrderPaymentDO.setTradeNo(StringUtils.hasText(subOrderPaymentDO.getTradeNo()) ? subOrderPaymentDO.getTradeNo() : tradeNo);
                }
                orderPayment.setPayAmount(subOrderPaymentDOS.stream().map(SubOrderPaymentDO::getPayAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                subOrderPaymentRepository.saveAll(subOrderPaymentDOS);
            }

            orderPayment.setBuyerInnerStatus(paySuccess ? BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode() : BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode());
            orderPayment.setVendorInnerStatus(paySuccess ? VendorInnerStatusEnum.VENDOR_PAYMENT_CONFIRMED.getCode() : VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode());
            orderPayment.setOuterStatus(paySuccess ? OrderOuterStatusEnum.ACCOMPLISHED.getCode() : OrderOuterStatusEnum.TO_PAY.getCode());
            orderPaymentRepository.saveAndFlush(orderPayment);

            //Step 3: 判断流程条件，执行流程
            if(paySuccess) {
                if(currentPayments.stream().allMatch(payment -> payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()))) {
                    baseOrderTaskService.execOrderPayTasks(order, 2, Stream.of(OrderConstant.AGREE, OrderConstant.AGREE).collect(Collectors.toList()));
                } else {
                    baseOrderTaskService.execOrderPayTasks(order, 2, Stream.of(OrderConstant.AGREE, OrderConstant.PAY_SERIAL_UNCOMPLETED).collect(Collectors.toList()));
                }
            } else {
                baseOrderTaskService.execOrderProcess(order, OrderConstant.DISAGREE);
            }
            //Step 4: 判断是否已经成功支付过一次，设置订单支付金额
            order.setPaidAmount(orderPayments.stream().filter(payment -> payment.getPayAmount().compareTo(BigDecimal.ZERO) > 0 && payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode())).map(OrderPaymentDO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            order.setHasPaid(order.getPaidAmount().compareTo(BigDecimal.ZERO) > 0);
            if((batchNo == 1 && CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode()))
                    || (batchNo == 2 && CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode()))){
                order.setTotalAmount(order.getPaidAmount());
                order.setSettlementGoldPrice(orderPayment.getSettlementGoldPrice());
                orderService.saveOrderProductPrice(order.getId(), orderPayment.getSettlementGoldPrice());
            }
            //Step 5: 订单商品加权平均已支付金额
            baseOrderProductService.updateProductPayAmount(order, order.getPaidAmount());
            //Step 5-1 :支付成功后转单,判断是否是现货订单，如果是自动跳过生产流程
            orderService.afterOrderPaySuccess(order, batchNo);
        }

        //Step 6: 缓存支付结果
        if(StringUtils.hasLength(tradeNo)) {
            baseCacheService.cacheOrderPayResult(tradeNo, paySuccess);
        }
    }

    /**
     * 计算出total按比例所得
     * @param total
     * @param map
     * @return
     */
    private Map<Object,BigDecimal> calcRateVal(BigDecimal total, Map<Object,BigDecimal> map){
        if (map.isEmpty() || total == null) {
            return new HashMap<>();
        }
        logger.info("calcRateVal total:{}, map:{}",total,JSONUtil.toJsonStr(map));
        Map<Object,BigDecimal> result=new HashMap<>(map.size());
        if(map.size()==1){
            for (Object obj :map.keySet()){
                result.put(obj,total);
            }
        }else{
            Object[] objects = map.keySet().toArray();
            BigDecimal temp=BigDecimal.ZERO;
            BigDecimal itemTotal = map.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            for (int i = 0; i < objects.length-1; i++) {
                BigDecimal itemRate=map.get(objects[i]).divide(itemTotal,3,RoundingMode.HALF_DOWN);
                BigDecimal itemVal = itemRate.multiply(total);
                temp=temp.add(itemVal);
                result.put(objects[i],itemVal);
            }
            result.put(objects[objects.length-1],total.subtract(temp));
        }
        logger.info("calcRateVal result:{}",JSONUtil.toJsonStr(result));
        return result;
    }

    private void calcTaxRatePayAmount(List<OrderPaymentDO> orderPayments){
        for(OrderPaymentDO pay :orderPayments) {
            //查询支付记录关联的订单商品
            List<OrderProductDO> productDOS = baseOrderProductService.findByOrderIn(Collections.singletonList(pay.getOrder()));
            //商品税率分组
            Map<Object, BigDecimal> taxMap = productDOS.stream().collect(Collectors.groupingBy(OrderProductDO::getTaxRate,Collectors.reducing(BigDecimal.ZERO, OrderProductDO::getAmount, BigDecimal::add)));
            //运费
            Map<Object, BigDecimal> calcFreightMap = calcRateVal(pay.getOrder().getFreight().multiply(pay.getPayRate()), taxMap);
            //相同税率的支付金额
            Map<Object, BigDecimal> calcPayMap = calcRateVal(pay.getPayAmount(), taxMap);
            List<TaxRatePayBO> taxRatePayBOList=new ArrayList<>();
            for(Object rate:taxMap.keySet()){
                TaxRatePayBO bo=new TaxRatePayBO();
                bo.setTaxRate((BigDecimal) rate);
                BigDecimal payAmountBd = taxMap.get(rate);
                bo.setOrderProductAmount(payAmountBd);
                //运费
                BigDecimal freight = calcFreightMap.getOrDefault(rate, BigDecimal.ZERO);
                bo.setDeliveryFree(freight);
                //相同税率的支付金额
                BigDecimal sameTaxPayAmount = calcPayMap.getOrDefault(rate, BigDecimal.ZERO);
                bo.setOrderProductPayAmount(sameTaxPayAmount.subtract(freight));
                taxRatePayBOList.add(bo);
            }
            pay.setTaxRatePayBO(taxRatePayBOList);
        }
        orderPaymentRepository.saveAll(orderPayments);
    }

    /**
     * 采购商，订单支付
     *
     * @param loginUser   用户登录
     * @param orders       订单
     * @param fundMode    资金归集模式
     * @param batchNo     支付次数
     * @param payType     支付方式
     * @param vouchers    支付凭证列表
     * @param payPassword 支付密码
     * @param weChatCode  微信小程序支付时的小程序登录凭证
     * @param returnUrl   支付后的跳转链接
     * @param wechatBrowser 是否微信浏览器
     * @return 支付结果
     */
    @Override
    public OrderPayResultBO buyerPay(UserLoginCacheDTO loginUser, List<OrderDO> orders, Integer fundMode, Integer batchNo, Integer payType, Integer payChannel, List<String> vouchers, String payPassword, String weChatCode, String returnUrl, Integer wechatBrowser, String name, String bankAccount, String bankDeposit) {
        //Step 1: 检查参数
        OrderPayTypeEnum orderPayType = OrderPayTypeEnum.parse(payType);
        OrderPayChannelEnum orderPayChannel = OrderPayChannelEnum.parse(payChannel);
        if(orderPayType == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_TYPE_MISMATCH);
        }

        if(orderPayChannel == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_CHANNEL_MISMATCH);
        }

        if(orderPayType.getPayChannels().stream().noneMatch(channel -> channel.getCode().equals(payChannel))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_TYPE_AND_PAY_CHANNEL_MISMATCH);
        }

        if(orderPayType.equals(OrderPayTypeEnum.DOES_NOT_NEED) || orderPayChannel.equals(OrderPayChannelEnum.DOES_NOT_NEED)) {
            return new OrderPayResultBO();
        }

        // 如果是拆单支付（资金归集模式必须是“平台代收”）
        if(orders.size() > 1 && !fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_FUND_MODE_MUST_BE_PLATFORM_EXCHANGE);
        }

        //Step 2: 检查采购商是否相同
        if(orders.stream().map(order -> new MemberDTO(order.getBuyerMemberId(), order.getBuyerRoleId())).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_FUND_MODE_MUST_BE_PLATFORM_EXCHANGE);
        }

        //Step 3: 查询支付记录
        List<OrderPaymentDO> orderPaymentList = orderPaymentRepository.findByOrderIn(orders);
        if(CollectionUtils.isEmpty(orderPaymentList)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        List<OrderPaymentDO> orderPayments = orderPaymentList.stream().filter(payment -> payment.getBatchNo().equals(batchNo)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderPayments) || orderPayments.size() != orders.size()) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        //判断是否正在支付中，或已经支付完成（支付金额为0的支付记录，已经在创建的时候设置为“已完成”状态了）
        if(orderPayments.stream().anyMatch(payment -> payment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAYING.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_PROCESSING);
        }

        if(orderPayments.stream().anyMatch(payment -> payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_HAS_PAID_SUCCESS);
        }

        //支付金额
        BigDecimal payAmount = orderPayments.stream().map(OrderPaymentDO::getPayAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        //修改支付方式和支付渠道
        orderPayments.forEach(orderPayment -> {
            orderPayment.setFundMode(fundMode);
            orderPayment.setPayType(payType);
            orderPayment.setPayChannel(payChannel);
        });

        // 默认为 “不执行流程”，“支付失败”
        PayChannelResultDTO payResult = new PayChannelResultDTO();
        logger.info("orderPayType:{}", orderPayType);
        logger.info("orderPayChannel:{}", orderPayChannel);
        //Step 3-2: 根据不同的支付方式、支付渠道，调用不同的接口
        switch (orderPayType) {
            case ONLINE_PAYMENT:
                switch (orderPayChannel) {
                    case ALIPAY:
                        payResult = aliPay(loginUser, orders, orderPayments, fundMode, payAmount);
                        break;
                    case WECHAT_PAY:
                        //根据前端传递的参数中是否包含“微信小程序登录凭证”来判断是小程序支付，还是Native/App支付
                        payResult = StringUtils.hasLength(weChatCode) ? weChatMiniAppPay(loginUser, orders, orderPayments, fundMode, payAmount, weChatCode,wechatBrowser) : weChatPay(loginUser, orders, orderPayments, fundMode, payAmount);
                        break;
                    case UNION_PAY:
                        break;
                    case ACCOUNT_BALANCE:
                        payResult = balancePay(orders, orderPaymentList, batchNo, payAmount, payPassword, fundMode);
                        break;
                    case WEI_QI_FU:
                        payResult = weiQiFuPay(loginUser, orders, orderPaymentList, payAmount,null);
                        break;
                    case PINGAN_BANK:
                        //payResult = pingAnBankPay(orders, orderPayments, fundMode, payAmount);
                        break;
                }
                break;
            case OFFLINE_PAYMENT:
                payResult = buyerUpdateVouchers(orders, orderPayments, vouchers, name, bankAccount, bankDeposit);
                break;
            case CREDIT_LINE_PAYMENT:
                payResult = creditPay(orders, orderPaymentList, batchNo, payAmount, fundMode, payPassword);
                break;
            case CASH_ON_DELIVERY:
                payResult = cashOnDeliveryPay(orders, orderPaymentList, batchNo, payAmount, fundMode);
                break;
            case SETTLEMENT:
                payResult = settlementPay(orders, orderPaymentList, batchNo, payAmount, fundMode);
                break;
            case MEMBER_RIGHT:
                payResult = rightPointsPay(orders, orderPaymentList, batchNo, payAmount, payPassword, fundMode);
                break;
            case ALLIN_PAY:
                payResult = allInPay(loginUser, orderPayChannel, orders, orderPayments, fundMode, payAmount, weChatCode);
                break;
            case CCB_PAY:
                switch (orderPayChannel) {
                    case CCB_B2B:
                        payResult = cbcPay(loginUser, orderPayChannel, orders, orderPayments, batchNo, payAmount);
                        break;
                    case CCB_DIGITAL:
                        payResult = cbcDigitalPay(loginUser, orderPayChannel, orders, orderPayments, batchNo, payAmount, returnUrl);
                        break;
                }
                break;
            case COMMERCE_IMPORT_PAY:
                payResult = commerceImportPay(orders, orderPaymentList, batchNo, payAmount, fundMode);
                break;
            default:
                break;
        }

        //Step 2：计算各税率下的支付金额
        calcTaxRatePayAmount(orderPayments);

        //Step 3: 支付结果
        OrderPayResultBO orderPay = new OrderPayResultBO();
        orderPay.setBatchNo(batchNo);
        orderPay.setPayAmount(payAmount);
        orderPay.setCodeUrl(payResult.getCodeUrl());
        orderPay.setTradeNo(payResult.getTradeNo());
        orderPay.setMpAppid(payResult.getMpAppid());
        orderPay.setMpPath(payResult.getMpPath());
        orderPay.setMpH5(payResult.getMpH5());
        orderPay.setMpUsername(payResult.getMpUsername());
        orderPay.setMpVersion(payResult.getMpVersion());
        return orderPay;
    }




    /**
     * 采购商，订单支付
     *
     * @param loginUser   用户登录
     * @param orders       订单
     * @param fundMode    资金归集模式
     * @param batchNo     支付次数
     * @param payTypes     支付方式
     * @param vouchers    支付凭证列表
     * @param payPassword 支付密码
     * @param weChatCode  微信小程序支付时的小程序登录凭证
     * @param returnUrl   支付后的跳转链接
     * @param wechatBrowser 是否微信浏览器
     * @return 支付结果
     */
    @Override
    public OrderPayResultBO buyerPay(UserLoginCacheDTO loginUser, List<OrderDO> orders, Integer fundMode, Integer batchNo, List<Integer> payTypes, List<Integer> payChannels, List<String> vouchers, String payPassword, String weChatCode, String returnUrl, Integer wechatBrowser, String name, String bankAccount, String bankDeposit) {
        //Step 1: 检查参数
//        OrderPayTypeEnum orderPayType = OrderPayTypeEnum.parse(payType);
//
//        if(orderPayType == null) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_TYPE_MISMATCH);
//        }
        List<OrderPayChannelEnum> orderPayChannels = OrderPayChannelEnum.parse(payChannels);

        if(CollectionUtils.isEmpty(orderPayChannels)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_CHANNEL_MISMATCH);
        }

//        if(orderPayType.getPayChannels().stream().noneMatch(channel -> payChannels.contains(channel.getCode()))) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_TYPE_AND_PAY_CHANNEL_MISMATCH);
//        }
        List<Integer> channels = payChannels.stream().filter(channel -> !OrderPayChannelEnum.CREDIT.getCode().equals(channel) && !OrderPayChannelEnum.ACCOUNT_BALANCE.getCode().equals(channel)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(channels) && channels.size() > 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_TYPE_MISMATCH);
        }
        //Step 2: 检查采购商是否相同
        if(orders.stream().map(order -> new MemberDTO(order.getBuyerMemberId(), order.getBuyerRoleId())).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_FUND_MODE_MUST_BE_PLATFORM_EXCHANGE);
        }

        //Step 3: 查询支付记录
        List<OrderPaymentDO> orderPaymentList = orderPaymentRepository.findByOrderIn(orders);
        if(CollectionUtils.isEmpty(orderPaymentList)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        List<OrderPaymentDO> orderPayments = orderPaymentList.stream().filter(payment -> payment.getBatchNo().equals(batchNo)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderPayments) || orderPayments.size() != orders.size()) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        //判断是否正在支付中，或已经支付完成（支付金额为0的支付记录，已经在创建的时候设置为“已完成”状态了）
        if(orderPayments.stream().anyMatch(payment -> payment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAYING.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_PROCESSING);
        }

        if(orderPayments.stream().anyMatch(payment -> payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_HAS_PAID_SUCCESS);
        }

        List<Long> orderPaymentIds = orderPayments.stream().map(OrderPaymentDO::getId).collect(Collectors.toList());
        List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdInAndSubStatus(orderPaymentIds, SubPaymentOrderStatusEnum.PENDING.getCode());
        if(!CollectionUtils.isEmpty(subOrderPaymentDOS)){
            for (SubOrderPaymentDO subOrderPaymentDO : subOrderPaymentDOS) {
                subOrderPaymentDO.setSubStatus(SubPaymentOrderStatusEnum.CLOSE.getCode());
            }
            subOrderPaymentRepository.saveAll(subOrderPaymentDOS);
        }
        AccountPayChannelResultResp accountPayChannelResultResp = null;
        List<UnPayOrderInfo> unPayOrderInfos = convertPayInfoDTO(orderPayments);
        if(!BigDecimalUtil.isZeroOrNegative(unPayOrderInfos.stream().map(UnPayOrderInfo::getUnPayOtherFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                && payChannels.size() == 1 && payChannels.contains(OrderPayChannelEnum.CREDIT.getCode())){
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_CHANNEL_ERROR);
        }
        BigDecimal payAmount = BigDecimal.ZERO;
        String tradeNo = unPayOrderInfos.stream().findFirst().get().getTradeNo();
        //Step 3-2: 根据不同的支付方式、支付渠道，调用不同的接口
        if(payChannels.contains(OrderPayChannelEnum.ACCOUNT_BALANCE.getCode()) || payChannels.contains(OrderPayChannelEnum.CREDIT.getCode())) {
            //授信/余额支付
            accountPayChannelResultResp = balancePay(orders, orderPaymentList, batchNo, payAmount, payPassword, fundMode, payChannels, unPayOrderInfos, tradeNo);
            if(!CollectionUtils.isEmpty(accountPayChannelResultResp.getPaidOrders())){
                //重新计算未支付的金额
                payAmount = accountPayChannelResultResp.getPaidOrders().stream().map(AccountBalancePaidResp::getPaidAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            unPayOrderInfos = accountPayChannelResultResp.getUnPayOrders();
            if(CollectionUtils.isEmpty(unPayOrderInfos)){
                OrderPayResultBO orderPay = new OrderPayResultBO();
                orderPay.setBatchNo(batchNo);
                orderPay.setPayAmount(payAmount);
                //已完成订单付款
                orderPay.setPayFinishedFlag(true);
                return orderPay;
            }
            for (UnPayOrderInfo unPayOrder : accountPayChannelResultResp.getUnPayOrders()) {
                unPayOrder.setTradeNo(tradeNo);
            }
        }
        PayChannelResultDTO payResult = new PayChannelResultDTO();
        BigDecimal unPayOtherAmount = unPayOrderInfos.stream().map(UnPayOrderInfo::getUnPayOtherFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unPayGoldFee = unPayOrderInfos.stream().map(UnPayOrderInfo::getUnPayAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        payAmount = BigDecimalUtil.add(unPayGoldFee, unPayOtherAmount).setScale(2, RoundingMode.HALF_UP);
        for (Integer payType : payTypes) {
            OrderPayTypeEnum orderPayType = OrderPayTypeEnum.parse(payType);
            // 默认为 “不执行流程”，“支付失败”
            logger.info("orderPayType:{}", orderPayType);
            logger.info("orderPayChannel:{}", orderPayChannels);
            //Step 3-2: 根据不同的支付方式、支付渠道，调用不同的接口
            OrderPayChannelEnum payChannelEnum = OrderPayChannelEnum.parse(channels.get(0));
                switch (orderPayType) {
                case ONLINE_PAYMENT:
                    switch (payChannelEnum) {
                        case ALIPAY:
                            payResult = aliPay(loginUser, orders, orderPayments, fundMode, payAmount);
                            break;
                        case WECHAT_PAY:
                            //根据前端传递的参数中是否包含“微信小程序登录凭证”来判断是小程序支付，还是Native/App支付
                            payResult = StringUtils.hasLength(weChatCode) ? weChatMiniAppPay(loginUser, orders, orderPayments, fundMode, payAmount, weChatCode,wechatBrowser) : weChatPay(loginUser, orders, orderPayments, fundMode, payAmount);
                            break;
                        case UNION_PAY:
                            break;
//                        case ACCOUNT_BALANCE:
//                            payResult = balancePay(orders, orderPaymentList, batchNo, payAmount, payPassword, fundMode);
//                            break;
                        case WEI_QI_FU:
                            payResult = weiQiFuPay(loginUser, orders, orderPaymentList, payAmount, unPayOrderInfos);
                            break;
                        case PINGAN_BANK:
                            //payResult = pingAnBankPay(orders, orderPayments, fundMode, payAmount);
                            break;
                    }
                    break;
                case OFFLINE_PAYMENT:
                    payResult = buyerUpdateVouchersNew(orders, orderPayments, vouchers, name, bankAccount, bankDeposit,unPayOrderInfos);
                    break;
                case CREDIT_LINE_PAYMENT:
                    payResult = creditPay(orders, orderPaymentList, batchNo, payAmount, fundMode, payPassword);
                    break;
                case CASH_ON_DELIVERY:
                    payResult = cashOnDeliveryPay(orders, orderPaymentList, batchNo, payAmount, fundMode);
                    break;
                case SETTLEMENT:
                    payResult = settlementPay(orders, orderPaymentList, batchNo, payAmount, fundMode);
                    break;
                case MEMBER_RIGHT:
                    payResult = rightPointsPay(orders, orderPaymentList, batchNo, payAmount, payPassword, fundMode);
                    break;
                case COMMERCE_IMPORT_PAY:
                    payResult = commerceImportPay(orders, orderPaymentList, batchNo, payAmount, fundMode);
                    break;
                default:
                    break;
            }
        }
        //Step 2：计算各税率下的支付金额
        calcTaxRatePayAmount(orderPayments);

        //Step 3: 支付结果
        OrderPayResultBO orderPay = new OrderPayResultBO();
        orderPay.setBatchNo(batchNo);
        orderPay.setPayAmount(payAmount);
        orderPay.setCodeUrl(payResult.getCodeUrl());
        orderPay.setTradeNo(payResult.getTradeNo());
        orderPay.setMpAppid(payResult.getMpAppid());
        orderPay.setMpPath(payResult.getMpPath());
        orderPay.setMpH5(payResult.getMpH5());
        orderPay.setMpUsername(payResult.getMpUsername());
        orderPay.setMpVersion(payResult.getMpVersion());
        orderPay.setQrCodeImageBase64(payResult.getQrCodeImageBase64());
        orderPay.setGetPayResultParam(payResult.getGetPayResultParam());
        return orderPay;
    }




    /**
     * 将支付记录统一转换为未支付订单信息 （适配多渠道统一参数）
     * @param orderPayments
     * @return
     */
    private List<UnPayOrderInfo> convertPayInfoDTO(List<OrderPaymentDO> orderPayments) {
        GoldPriceResp goldPriceResp = eosApiService.getGoldPrice();
        if(Objects.nonNull(goldPriceResp) && BigDecimalUtil.isZeroOrNegative(goldPriceResp.getJj())){
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_GOLE_ERROR);
        }
        //统一支付流水号
        String tradeNo = RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        BigDecimal goldPrice = goldPriceResp.getJj();
        if(BigDecimalUtil.isNullOrZero(goldPrice) || BigDecimalUtil.isGreaterThan(BigDecimal.ZERO, goldPrice)){
            log.error("获取的金价数据：",goldPrice);
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_GOLE_ERROR);
        }
        List<UnPayOrderInfo> unPayOrderInfos = new ArrayList<>();
        for (OrderPaymentDO orderPayment : orderPayments) {
            UnPayOrderInfo unPayOrderInfo = new UnPayOrderInfo();
            unPayOrderInfo.setGoldPrice(goldPrice);
            OrderDO order = orderPayment.getOrder();
            //计算黄金费用
            setTotaolPayAmount(orderPayment, order, unPayOrderInfo);
            //如果不是第一次支付，则把其他费用加上
            if((CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode()) && !OrderPayNodeEnum.AFTER_ORDER_SUBMIT.getCode().equals(orderPayment.getPayNode())) || CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode())) {
                BigDecimal otherFee = BigDecimalUtil.nullToZero(orderPayment.getOtherFee());
                BigDecimal laborFee = BigDecimalUtil.nullToZero(orderPayment.getLaborFee());
                unPayOrderInfo.setUnPayOtherFee(otherFee.add(laborFee).setScale(2, RoundingMode.HALF_UP));
                unPayOrderInfo.setTotalPayAmount(BigDecimalUtil.add(unPayOrderInfo.getTotalPayAmount(), unPayOrderInfo.getUnPayOtherFee()));
            }
            unPayOrderInfo.setOrderNo(order.getOrderNo());
            unPayOrderInfo.setOrderPaymentId(orderPayment.getId());
            unPayOrderInfo.setOrderId(order.getId());
            unPayOrderInfo.setBatchNo(orderPayment.getBatchNo());
            unPayOrderInfo.setTradeNo(tradeNo);
            unPayOrderInfos.add(unPayOrderInfo);
        }
        return unPayOrderInfos;
    }

    /**
     * 根据订单类型和付款次数获取当前实际要支付的总金额
     * @param orderPayment
     * @param order
     * @param unPayOrderInfo
     */
    private void setTotaolPayAmount(OrderPaymentDO orderPayment, OrderDO order, UnPayOrderInfo unPayOrderInfo) {
        BigDecimal goldPrice = unPayOrderInfo.getGoldPrice();
        if(CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode())){
            if(orderPayment.getBatchNo() == 1){
                if(DepositTypeEnum.FIXED_AMOUNT.getCode().equals(orderPayment.getDepositType())){
                    unPayOrderInfo.setUnPayOtherFee(orderPayment.getAdvancePayAmount());
                }else{
                    unPayOrderInfo.setUnPayOtherFee(BigDecimalUtil.multiplyAndScaleThree(BigDecimalUtil.multiply(goldPrice,orderPayment.getTotalWeight()), orderPayment.getAdvancePayAmount()).setScale(2, RoundingMode.HALF_UP));
                }
                unPayOrderInfo.setUnPayGoldWeight(BigDecimal.ZERO);
            }else{
                List<OrderPaymentDO> orderPaymentDOS = orderPaymentRepository.findByOrder(order);
                OrderPaymentDO firstOrderPayment = orderPaymentDOS.stream().filter(orderPaymentDO -> orderPaymentDO.getBatchNo() == 1).collect(Collectors.toList()).stream().findFirst().get();
                List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentId(firstOrderPayment.getId());
                SubOrderPaymentDO firstPaySubOrderPaymentDO = subOrderPaymentDOS.get(0);
                //计算定金换成成黄金金重
                BigDecimal firstPayAmount = firstPaySubOrderPaymentDO.getPayAmount();
                orderService.getOrderTotalPrice(order, goldPrice);
                BigDecimal firstPayGoldWeight = firstPayAmount.divide(goldPrice, 2, RoundingMode.HALF_DOWN);
                BigDecimal payWeight = BigDecimalUtil.subtract(order.getTotalWeight(), firstPayGoldWeight);
                unPayOrderInfo.setUnPayAmount(payWeight.multiply(goldPrice));
                unPayOrderInfo.setUnPayGoldWeight(payWeight);
                //更新支付定金换算成黄金的克重
                firstPaySubOrderPaymentDO.setWeight(firstPayGoldWeight);
                subOrderPaymentRepository.save(firstPaySubOrderPaymentDO);
            }
        }else{
            unPayOrderInfo.setUnPayGoldFee(orderPayment.getTotalWeight().multiply(goldPrice).setScale(2, RoundingMode.HALF_UP));
            unPayOrderInfo.setUnPayAmount(unPayOrderInfo.getUnPayGoldFee());
            unPayOrderInfo.setUnPayGoldWeight(orderPayment.getTotalWeight());
        }
        unPayOrderInfo.setTotalPayAmount(unPayOrderInfo.getUnPayAmount());
    }


    /**
     * 生成子支付记录
     * @param tradeNo
     * @param orderPayments
     * @param payChannel
     * @param payType
     * @param unPayOrderInfos
     * @param payTotalAmount
     * @return
     */
    public List<SubOrderPaymentDO> createSubOrderPayments(String tradeNo, List<OrderPaymentDO> orderPayments, Integer payChannel, Integer payType, List<UnPayOrderInfo> unPayOrderInfos, BigDecimal payTotalAmount) {
        //Map<String,OrderDO> orderDOMap = orders.stream().collect(Collectors.toMap(OrderDO::getOrderNo, Function.identity()));
        Map<Long,OrderPaymentDO> orderPaymentMap = orderPayments.stream().collect(Collectors.toMap(OrderPaymentDO::getId, Function.identity()));
        List<SubOrderPaymentDO> subOrderPaymentDOS = new ArrayList<>();
        for (UnPayOrderInfo unPayOrderInfo : unPayOrderInfos) {
            SubOrderPaymentDO subOrderPaymentDO = new SubOrderPaymentDO();
            subOrderPaymentDO.setPayType(payType);
            subOrderPaymentDO.setPayChannel(payChannel);
            subOrderPaymentDO.setPayAmount(BigDecimalUtil.add(unPayOrderInfo.getUnPayAmount(), unPayOrderInfo.getUnPayOtherFee()));
            subOrderPaymentDO.setWeight(unPayOrderInfo.getUnPayGoldWeight());
            subOrderPaymentDO.setPayTime(DateTimeUtil.getTodayNowLocal());
            //subOrderPaymentDO.setTradeNo(frozenAccountBalanceResp.getUnPayGoldWeight());
            subOrderPaymentDO.setSubStatus(SubPaymentOrderStatusEnum.PENDING.getCode());
            OrderPaymentDO orderPaymentDO = orderPaymentMap.get(unPayOrderInfo.getOrderPaymentId());
            subOrderPaymentDO.setOrderPayment(orderPaymentDO);
            subOrderPaymentDO.setTradeNo(tradeNo);
            subOrderPaymentDO.setFundMode(orderPaymentDO.getFundMode());
            subOrderPaymentDO.setChannelTradeNo(orderPaymentDO.getChannelTradeNo());
            subOrderPaymentDO.setOrder(orderPaymentDO.getOrder());
            subOrderPaymentDO.setBatchNo(orderPaymentDO.getBatchNo());
            subOrderPaymentDO.setChannelPayTotalAmount(payTotalAmount);
            subOrderPaymentDO.setServiceFee(orderPaymentDO.getServiceFee());
            subOrderPaymentDOS.add(subOrderPaymentDO);
        }
        return subOrderPaymentDOS;
    }



    /**
     * 微企付支付
     * @param loginUser 登录用户
     * @param orders 订单列表
     * @param orderPayments 订单支付记录列表
     * @param payAmount 支付金额
     * @return 支付结果
     */
    private PayChannelResultDTO weiQiFuPay(UserLoginCacheDTO loginUser, List<OrderDO> orders, List<OrderPaymentDO> orderPayments, BigDecimal payAmount, List<UnPayOrderInfo> unPayOrderInfos) {
        String tradeNo = unPayOrderInfos.stream().findFirst().get().getTradeNo();
        //Step 1: 支付金额要大于0
        WrapperResp<OrderDepositResp> orderConfig = orderProcessFeign.findOrderConfig();
        OrderDepositResp orderDepositResp = WrapperUtil.getDataOrThrow(orderConfig);
        BigDecimal totalServerFee = null;
        if(orderDepositResp != null && orderDepositResp.getServiceCharge() != null){
            totalServerFee = payAmount.multiply(orderDepositResp.getServiceCharge().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
        }
        payAmount = payAmount.add(totalServerFee);

        //Step 2: 调用支付参数
        WeiQiFuPayReq weiQiFuPayReq = new WeiQiFuPayReq();
        weiQiFuPayReq.setPayChannel(loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MOBILE.getCode()) ? PayChannelEnum.Applet.getCode() : PayChannelEnum.Platform.getCode());
        weiQiFuPayReq.setOutPlatformId(tradeNo);
        long payAmountCent = payAmount.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).toBigInteger().longValue();
        weiQiFuPayReq.setPayMoney(payAmountCent);
        weiQiFuPayReq.setServiceType(ServiceTypeEnum.Order_Pay.getCode());
        List<GoodsReq> goodsReqs = new ArrayList<>();
        for (OrderDO order : orders) {
            weiQiFuPayReq.setOrderId(order.getId());
            order.getProducts().stream().forEach(product ->{
                GoodsReq goodsReq = new GoodsReq();
                goodsReq.setGoodNumber(product.getQuantity().intValue());
                goodsReq.setGoodAmount(payAmountCent);
                goodsReq.setGoodName(product.getName());
                goodsReqs.add(goodsReq);
            });
        }
        weiQiFuPayReq.setGoodsReqList(goodsReqs);
        String attach = UUIDUtil.randomUUID();
        PaymentCacheDTO paymentCache = new PaymentCacheDTO(loginUser, tradeNo, payAmount, unPayOrderInfos.stream().map(payment -> new OrderPaymentDTO(payment.getOrderId(), payment.getOrderPaymentId(), payment.getBatchNo())).collect(Collectors.toList()));
        weiQiFuPayReq.setAttach(attach);
        //Step 4: 缓存支付用户信息，用于回调查询
        baseCacheService.cacheOrderPayment(attach, paymentCache);
        WrapperResp<WeiQiFuPayResp> weiQiFuPayRespWrapperResp = weiQiFuPayFeign.orderPay(weiQiFuPayReq);
        if (WrapperUtil.isFail(weiQiFuPayRespWrapperResp)) {
            throw new BusinessException(weiQiFuPayRespWrapperResp.getCode(), weiQiFuPayRespWrapperResp.getMessage());
        }
        PayChannelResultDTO payResult = new PayChannelResultDTO();
        payResult.setCodeUrl(weiQiFuPayRespWrapperResp.getData().getStaticQrcode());
        payResult.setTradeNo(tradeNo);
        payResult.setMpAppid(weiQiFuPayRespWrapperResp.getData().getMpAppid());
        payResult.setMpPath(weiQiFuPayRespWrapperResp.getData().getMpPath());
        payResult.setMpH5(weiQiFuPayRespWrapperResp.getData().getWxH5());
        payResult.setMpUsername(weiQiFuPayRespWrapperResp.getData().getMpUsername());
        payResult.setMpVersion(weiQiFuPayRespWrapperResp.getData().getMpVersion());
        payResult.setGetPayResultParam(weiQiFuPayRespWrapperResp.getData().getGetPayResultParam());
        if(StringUtils.hasText(weiQiFuPayRespWrapperResp.getData().getStaticQrcode())){
            payResult.setQrCodeImageBase64(QrCodeUtil.generateAsBase64(weiQiFuPayRespWrapperResp.getData().getStaticQrcode(), new QrConfig(200,200), ImgUtil.IMAGE_TYPE_JPG));
        }
        //Step 3: 修改支付记录的支付时间，记录微信商户订单号(注意此时不能更改支付记录的状态，因为生成支付二维码后，用户有可能不支付)
        List<OrderPaymentDO> updOrderPaymentDOS = new ArrayList<>();
        for (UnPayOrderInfo unPayOrderInfo : unPayOrderInfos) {
            OrderPaymentDO orderPayment = orderPaymentRepository.findById(unPayOrderInfo.getOrderPaymentId()).orElse(null);
            orderPayment.setPayTime(LocalDateTime.now());
            orderPayment.setTradeNo(tradeNo);
            orderPayment.setPayChannel(OrderPayChannelEnum.WEI_QI_FU.getCode());
            orderPayment.setPayType(OrderPayTypeEnum.ONLINE_PAYMENT.getCode());
            orderPayment.setFundMode(FundModeEnum.DIRECT_TO_ACCOUNT.getCode());
            orderPayment.setSettlementGoldPrice(unPayOrderInfo.getGoldPrice());
            if(BigDecimalUtil.notNullOrZero(totalServerFee)){
                orderPayment.setServiceFee(totalServerFee.divide(BigDecimal.valueOf(unPayOrderInfos.size())).setScale(2, RoundingMode.HALF_UP));
            }
            updOrderPaymentDOS.add(orderPayment);
        }
        List<SubOrderPaymentDO> subOrderPayments = createSubOrderPayments(tradeNo, orderPayments, OrderPayChannelEnum.WEI_QI_FU.getCode(), OrderPayTypeEnum.ONLINE_PAYMENT.getCode(), unPayOrderInfos, payAmount);
        subOrderPaymentRepository.saveAll(subOrderPayments);
        orderPaymentRepository.saveAll(updOrderPaymentDOS);
        return payResult;
    }

    /**
     * 微信Native、App支付
     * @param loginUser 登录用户
     * @param orders 订单列表
     * @param orderPayments 订单支付记录列表
     * @param fundMode 资金归集模式
     * @return 二维码链接
     */
    private PayChannelResultDTO weChatPay(UserLoginCacheDTO loginUser, List<OrderDO> orders, List<OrderPaymentDO> orderPayments, Integer fundMode, BigDecimal payAmount) {
        //Step 1: 支付金额要大于0
        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 2: 查询支付参数
        OrderPaymentParameterDetailBO parameterResult = fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? baseOrderProcessService.findPlatformPaymentParameters(OrderPayChannelEnum.WECHAT_PAY.getCode()) : baseOrderProcessService.findMemberPaymentParameters(OrderPayChannelEnum.WECHAT_PAY.getCode(), orders.get(0).getVendorMemberId(), orders.get(0).getVendorRoleId());
        if(CollectionUtils.isEmpty(parameterResult.getParameters())) {
            throw new BusinessException(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? ResponseCodeEnum.ORDER_PLATFORM_PARAMETER_DOES_NOT_EXIST : ResponseCodeEnum.ORDER_MEMBER_PARAMETER_DOES_NOT_EXIST);
        }

        String merchantId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String appId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.WECHAT_APP_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String apiKey = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.WECHAT_API_KEY.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        if(!StringUtils.hasLength(merchantId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_MERCHANT_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(appId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_APP_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(apiKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_API_KEY_IS_MISSING);
        }

        //Step 2: 调用支付服务，微信预支付
        //        由于同一个订单号不能重复支付，（比如生成二维码后不支付，在采购订单中再去支付，微信会报商户订单号重复的错误，所以这里生成一个随机订单号
        //        同时生成一个UUID字符串，作为透传参数，并且作为缓存支付信息的Key，用于在回调时获取支付用户、支付记录等信息
        String tradeNo = orders.size() == 1 ? RandomNumberUtil.randomUniqueNumber(orders.get(0).getOrderNo() + CharPool.DASHED, OrderConstant.PAY_ORDER_NO_SHORT_LENGTH) : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        String attach = UUIDUtil.randomUUID();
        PaymentCacheDTO paymentCache = new PaymentCacheDTO(loginUser, tradeNo, payAmount, orderPayments.stream().map(payment -> new OrderPaymentDTO(payment.getOrder().getId(), payment.getId(), payment.getBatchNo())).collect(Collectors.toList()));

        String prePayResult = payFeignService.weChatPay(loginUser.getLoginSource(), parameterResult.getMemberId(), parameterResult.getRoleId(), parameterResult.getFundMode(), tradeNo, orders.get(0).getDigest(), payAmount, attach, merchantId, appId, apiKey,orders.get(0).getShopEnvironment());
        
        //Step 4: 缓存支付用户信息，用于回调查询
        baseCacheService.cacheOrderPayment(attach, paymentCache);

        //Step 4: 修改支付记录的支付时间，记录微信商户订单号
        //        注意此时不能更改支付记录的状态，因为生成支付二维码后，用户有可能不支付
        orderPayments.forEach(orderPayment -> {
            orderPayment.setPayTime(LocalDateTime.now());
            orderPayment.setTradeNo(tradeNo);
        });

        orderPaymentRepository.saveAll(orderPayments);
        return new PayChannelResultDTO(prePayResult, tradeNo);
    }

    /**
     * 微信小程序支付
     * @param loginUser 登录用户
     * @param orders 订单列表
     * @param orderPayments 订单支付记录列表
     * @param fundMode 资金归集模式
     * @param weChatCode 小程序登录凭证
     * @param wechatBrowser 是否微信浏览器
     * @return 二维码链接
     */
    private PayChannelResultDTO weChatMiniAppPay(UserLoginCacheDTO loginUser, List<OrderDO> orders, List<OrderPaymentDO> orderPayments, Integer fundMode, BigDecimal payAmount, String weChatCode, Integer wechatBrowser) {
        logger.info("微信小程序支付");
        //Step 1: 支付金额要大于0
        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 2: 查询支付参数
        OrderPaymentParameterDetailBO parameterResult = fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? baseOrderProcessService.findPlatformPaymentParameters(OrderPayChannelEnum.WECHAT_PAY.getCode()) : baseOrderProcessService.findMemberPaymentParameters(OrderPayChannelEnum.WECHAT_PAY.getCode(), orders.get(0).getVendorMemberId(), orders.get(0).getVendorRoleId());
        if(CollectionUtils.isEmpty(parameterResult.getParameters())) {
            throw new BusinessException(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? ResponseCodeEnum.ORDER_PLATFORM_PARAMETER_DOES_NOT_EXIST : ResponseCodeEnum.ORDER_MEMBER_PARAMETER_DOES_NOT_EXIST);
        }
        String merchantId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String apiKey = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.WECHAT_API_KEY.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String appletApiId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.WECHAT_APPLET_APP_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String appletApiKey = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.WECHAT_APPLET_APP_KEY.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        if(!StringUtils.hasLength(merchantId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_MERCHANT_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(apiKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_API_KEY_IS_MISSING);
        }

        if(!StringUtils.hasLength(appletApiId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_APP_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(appletApiKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_APP_KEY_IS_MISSING);
        }

        //Step 2: 调用支付服务，微信预支付
        //        由于同一个订单号不能重复支付，（比如生成二维码后不支付，在采购订单中再去支付，微信会报商户订单号重复的错误，所以这里生成一个随机订单号
        //        同时生成一个UUID字符串，作为透传参数，并且作为缓存支付信息的Key，用于在回调时获取支付用户、支付记录等信息
        String tradeNo = orders.size() == 1 ? RandomNumberUtil.randomUniqueNumber(orders.get(0).getOrderNo() + CharPool.DASHED, OrderConstant.PAY_ORDER_NO_SHORT_LENGTH) : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        String attach = UUIDUtil.randomUUID();
        PaymentCacheDTO paymentCache = new PaymentCacheDTO(loginUser, tradeNo, payAmount, orderPayments.stream().map(payment -> new OrderPaymentDTO(payment.getOrder().getId(), payment.getId(), payment.getBatchNo())).collect(Collectors.toList()));

        String prePayResult = payFeignService.weChatMiniAppPay(parameterResult.getMemberId(), parameterResult.getRoleId(), parameterResult.getFundMode(), tradeNo, orders.get(0).getDigest(), payAmount, attach, merchantId, apiKey, appletApiId, appletApiKey, weChatCode,wechatBrowser);

        //Step 4: 缓存支付用户信息，用于回调查询
        baseCacheService.cacheOrderPayment(attach, paymentCache);

        //Step 4: 修改支付记录的支付时间，记录微信商户订单号
        //        注意此时不能更改支付记录的状态，因为生成支付二维码后，用户有可能不支付
        orderPayments.forEach(orderPayment -> {
            orderPayment.setPayTime(LocalDateTime.now());
            orderPayment.setTradeNo(tradeNo);
        });

        orderPaymentRepository.saveAll(orderPayments);
        return new PayChannelResultDTO(prePayResult, tradeNo);
    }


    /**
     * 余额支付
     * @param orders 订单列表
     * @param orderPaymentList 所有支付记录列表
     * @param batchNo 支付次数
     * @param payAmount 支付金额
     * @param payPassword  Aes加密后的支付密码
     * @param fundMode 资金归集模式
     * @return 支付结果
     */
    public AccountPayChannelResultResp balancePay(List<OrderDO> orders, List<OrderPaymentDO> orderPaymentList, Integer batchNo, BigDecimal payAmount, String payPassword, Integer fundMode, List<Integer> balancePayMethods, List<UnPayOrderInfo> unPayOrderInfos, String tradeNo) {
        //TODO 6-30 先不校验密码
//        if(!StringUtils.hasLength(payPassword)) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_PASSWORD_CAN_NOT_BE_EMPTY);
//        }
        //Step 1: 支付金额要大于0
//        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
//        }
        //Step 2: 查询支付参数设置
        OrderPaymentParameterDetailBO parameterResult = fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? baseOrderProcessService.findPlatformPaymentParameters(OrderPayChannelEnum.ACCOUNT_BALANCE.getCode()) : baseOrderProcessService.findMemberPaymentParameters(OrderPayChannelEnum.ACCOUNT_BALANCE.getCode(), orders.get(0).getVendorMemberId(), orders.get(0).getVendorRoleId());
        //Step 3: 调用支付服务接口进行支付
        Map<Long,OrderPaymentDO> orderPaymentDOMap = orderPaymentList.stream().collect(Collectors.toMap(OrderPaymentDO::getId, Function.identity()));
        List<OrderPayInfoReq> orderPayInfoReqs = unPayOrderInfos.stream().map(orderPayment -> new OrderPayInfoReq(orderPayment.getOrderNo(), orderPayment.getUnPayAmount(), orderPayment.getUnPayOtherFee(), balancePayMethods, orderPayment.getUnPayGoldWeight(), orderPayment.getGoldPrice(), orderPayment.getOrderPaymentId())).collect(Collectors.toList());
        UnPayOrderInfo unPayOrderInfo = unPayOrderInfos.stream().findFirst().get();
        List<Long> branchIds = orders.stream().map(OrderDO::getBranchId).collect(Collectors.toList());
        CommonIdListReq commonIdListReq = new CommonIdListReq();
        commonIdListReq.setIdList(branchIds);
        List<MemberBrandInfoResp> memberBrandInfoRespList = memberFeignService.findMemberBrandByIds(commonIdListReq);
        if(CollectionUtils.isEmpty(memberBrandInfoRespList) || CollectionUtils.isEmpty(memberBrandInfoRespList.stream().map(MemberBrandInfoResp::getMemberId).collect(Collectors.toSet()))){
            throw new BusinessException(ResponseCodeEnum.ORDER_RELATION_BRANCH_NOT_EXIST);
        }
        List<Long> memberIds = memberBrandInfoRespList.stream().map(MemberBrandInfoResp::getMemberId).distinct().collect(Collectors.toList());
        List<Long> branchIdList = memberBrandInfoRespList.stream().map(MemberBrandInfoResp::getBranchId).collect(Collectors.toList());
        if( !branchIdList.containsAll(branchIds) || memberIds.size() != 1){
            throw new BusinessException(ResponseCodeEnum.ORDER_BRANCH_ORDER_DISAFFINITY);
        }
        MemberBrandInfoResp memberBrandInfoResp = memberBrandInfoRespList.get(0);
        AccountPayChannelResultResp payResult = payFeignService.frozenbalance(memberBrandInfoResp.getMemberId(), memberBrandInfoResp.getRoleId(), parameterResult.getMemberId(), parameterResult.getRoleId(), parameterResult.getFundMode(), tradeNo, payAmount, payPassword, orderPayInfoReqs, balancePayMethods);
        List<SubOrderPaymentDO> subOrderPaymentDOS = new ArrayList<>();
        //生成子支付记录
        payResult.getPaidOrders().stream().forEach(paidOrder -> {
            SubOrderPaymentDO subOrderPayment = new SubOrderPaymentDO();
            OrderPaymentDO orderPaymentDO = orderPaymentDOMap.get(paidOrder.getOrderPaymentId());
            subOrderPayment.setFundMode(FundModeEnum.DIRECT_TO_ACCOUNT.getCode());
            subOrderPayment.setPayType(OrderPayTypeEnum.ONLINE_PAYMENT.getCode());
            subOrderPayment.setPayChannel(paidOrder.getPayType());
            subOrderPayment.setPayAmount(paidOrder.getPaidAmount());
            orderPaymentDO.setPayChannel(paidOrder.getPayType());
            orderPaymentDO.setPayType(OrderPayTypeEnum.ONLINE_PAYMENT.getCode());
            orderPaymentDO.setFundMode(FundModeEnum.DIRECT_TO_ACCOUNT.getCode());
            orderPaymentDO.setServiceFee(BigDecimal.ZERO);
            orderPaymentDO.setSettlementGoldPrice(unPayOrderInfo.getGoldPrice());
            subOrderPayment.setWeight(paidOrder.getPaidGoldWeight());
            subOrderPayment.setPayTime(LocalDateTime.now());
            subOrderPayment.setTradeNo(tradeNo);
            subOrderPayment.setSubStatus(SubPaymentOrderStatusEnum.PENDING.getCode());
            subOrderPayment.setOrderPayment(orderPaymentDO);
            subOrderPayment.setBatchNo(orderPaymentDO.getBatchNo());
            subOrderPayment.setChannelTradeNo(tradeNo);
            subOrderPayment.setOrder(orderPaymentDO.getOrder());
            paidOrder.setOrderNo(tradeNo);
            subOrderPaymentDOS.add(subOrderPayment);
        });
        payResult.getUnPayOrders().stream().forEach(unPayOrder -> {
            OrderPaymentDO orderPaymentDO = orderPaymentDOMap.get(unPayOrder.getOrderPaymentId());
            unPayOrder.setOrderId(orderPaymentDO.getOrder().getId());
            unPayOrder.setBatchNo(orderPaymentDO.getBatchNo());
            unPayOrder.setGoldPrice(unPayOrderInfo.getGoldPrice());
        });
        List<OrderPaymentDO> orderPaymentDOS = orderPaymentDOMap.values().stream().collect(Collectors.toList());
        orderPaymentRepository.saveAll(orderPaymentDOS);
        subOrderPaymentRepository.saveAll(subOrderPaymentDOS);
        BigDecimal unPayAmount = payResult.getUnPayOrders().stream().map(UnPayOrderInfo::getUnPayAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unpayOtherFee = payResult.getUnPayOrders().stream().map(UnPayOrderInfo::getUnPayOtherFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal unPayGoldWeight = payResult.getUnPayOrders().stream().map(UnPayOrderInfo::getUnPayGoldWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        //Step 4: 支付成功，执行流程
        if(unPayAmount.compareTo(BigDecimal.ZERO) <= 0 && unPayGoldWeight.compareTo(BigDecimal.ZERO) <= 0 && unpayOtherFee.compareTo(BigDecimal.ZERO) <= 0) {
            execOrderPaySerialTasks(orders, orderPaymentList, batchNo, true, tradeNo);
            payResult.setUnPayOrders(null);
        }
        return payResult;
    }




    /**
     * 余额支付退款
     * @param orderPayment 支付记录
     * @param remark 备注说明
     * @return 退款结果
     */
    private String balancePayRefund(OrderPaymentDO orderPayment, SubOrderPaymentDO subOrderPaymentDO, String remark) {
        logger.info("余额支付退款开始 => " + orderPayment.getOrder().getId());
        String refundNo = UUIDUtil.randomUUID();
        UnFrozenAccountBalanceReq unFrozenAccountBalanceReq = new UnFrozenAccountBalanceReq();
        String tradeNo = subOrderPaymentDO.getTradeNo();
        Set<String> tradeNoSet = new HashSet<>();
        tradeNoSet.add(tradeNo);
        unFrozenAccountBalanceReq.setTradeCode(tradeNoSet);
        assetAccountFeign.unFrozenAccountBalance(unFrozenAccountBalanceReq);
        return refundNo;
    }






    /**
     * 支付宝支付
     * @param loginUser 登录用户
     * @param orders 订单列表
     * @param orderPayments 订单支付记录列表
     * @param fundMode 资金归集模式
     * @return 二维码链接
     */
    private PayChannelResultDTO aliPay(UserLoginCacheDTO loginUser, List<OrderDO> orders, List<OrderPaymentDO> orderPayments, Integer fundMode, BigDecimal payAmount) {
        //Step 1: 支付金额要大于0
        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 2: 查询支付参数
        OrderPaymentParameterDetailBO parameterResult = fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? baseOrderProcessService.findPlatformPaymentParameters(ALIPAY.getCode()) : baseOrderProcessService.findMemberPaymentParameters(ALIPAY.getCode(), orders.get(0).getVendorMemberId(), orders.get(0).getVendorRoleId());

        if(CollectionUtils.isEmpty(parameterResult.getParameters())) {
            throw new BusinessException(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? ResponseCodeEnum.ORDER_PLATFORM_PARAMETER_DOES_NOT_EXIST : ResponseCodeEnum.ORDER_MEMBER_PARAMETER_DOES_NOT_EXIST);
        }

        String appId, publicKey, privateKey, appAuthToken = "";
        if (loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_WEB.getCode()) || loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode())) {
            appId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.ALIPAY_APP_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
            publicKey = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
            privateKey = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.ALIPAY_PRIVATE_KEY.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
            appAuthToken = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.ALIPAY_THREAD_APP_AUTH_TOKEN.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        } else {
            appId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.ALIPAY_APP_PAY_APP_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
            publicKey = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.ALIPAY_APP_PAY_PUBLIC_KEY.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
            privateKey = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.ALIPAY_APP_PAY_PRIVATE_KEY.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        }

        if(!StringUtils.hasLength(appId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_ALIPAY_APP_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(publicKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_ALIPAY_PUBLIC_KEY_IS_MISSING);
        }

        if(!StringUtils.hasLength(privateKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_ALIPAY_PRIVATE_KEY_MISSING);
        }

        if(fundMode.equals(FundModeEnum.DIRECT_TO_ACCOUNT.getCode()) && !StringUtils.hasLength(appAuthToken)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_ALIPAY_APP_AUTH_TOKEN_IS_MISSING);
        }

        //Step 2: 调用支付服务，支付宝支付
        //        由于同一个订单号不能重复支付，（比如生成二维码后不支付，在采购订单中再去支付，支付宝会报商户订单号重复的错误，所以这里生成一个随机订单号
        //        同时生成一个UUID字符串，作为透传参数，并且作为缓存支付信息的Key，用于在回调时获取支付用户、支付记录等信息
        String tradeNo = orders.size() == 1 ? RandomNumberUtil.randomUniqueNumber(orders.get(0).getOrderNo() + CharPool.DASHED, OrderConstant.PAY_ORDER_NO_SHORT_LENGTH) : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        String attach = UUIDUtil.randomUUID();
        PaymentCacheDTO paymentCache = new PaymentCacheDTO(loginUser, tradeNo, payAmount, orderPayments.stream().map(payment -> new OrderPaymentDTO(payment.getOrder().getId(), payment.getId(), payment.getBatchNo())).collect(Collectors.toList()));

        String prePayResult = payFeignService.aliPay(loginUser.getLoginSource(), parameterResult.getMemberId(), parameterResult.getRoleId(), parameterResult.getFundMode(), tradeNo, orders.get(0).getDigest(), payAmount, attach, appId, publicKey, privateKey, appAuthToken,orders.get(0).getShopEnvironment());

        //Step 4: 缓存支付用户信息，用于回调查询
        baseCacheService.cacheOrderPayment(attach, paymentCache);

        //Step 4: 修改支付记录的支付时间，记录微信商户订单号
        //        注意此时不能更改支付记录的状态，因为生成支付二维码后，用户有可能不支付
        orderPayments.forEach(orderPayment -> {
            orderPayment.setPayTime(LocalDateTime.now());
            orderPayment.setTradeNo(tradeNo);
        });

        orderPaymentRepository.saveAll(orderPayments);
        return new PayChannelResultDTO(prePayResult, tradeNo);
    }

    /**
     * 余额支付
     * @param orders 订单列表
     * @param orderPaymentList 所有支付记录列表
     * @param batchNo 支付次数
     * @param payAmount 支付金额
     * @param payPassword  Aes加密后的支付密码
     * @param fundMode 资金归集模式
     * @return 支付结果
     */
    private PayChannelResultDTO balancePay(List<OrderDO> orders, List<OrderPaymentDO> orderPaymentList, Integer batchNo, BigDecimal payAmount, String payPassword, Integer fundMode) {
        if(!StringUtils.hasLength(payPassword)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_PASSWORD_CAN_NOT_BE_EMPTY);
        }

        //Step 1: 支付金额要大于0
        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 2: 查询支付参数设置
        OrderPaymentParameterDetailBO parameterResult = fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? baseOrderProcessService.findPlatformPaymentParameters(OrderPayChannelEnum.ACCOUNT_BALANCE.getCode()) : baseOrderProcessService.findMemberPaymentParameters(OrderPayChannelEnum.ACCOUNT_BALANCE.getCode(), orders.get(0).getVendorMemberId(), orders.get(0).getVendorRoleId());
        //Step 3: 调用支付服务接口进行支付
        String tradeNo = RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        String payResult = payFeignService.balancePay(orders.get(0).getBuyerMemberId(), orders.get(0).getBuyerRoleId(), parameterResult.getMemberId(), parameterResult.getRoleId(), parameterResult.getFundMode(), tradeNo, payAmount, payPassword);

        //Step 4: 支付成功，执行流程
        execOrderPaySerialTasks(orders, orderPaymentList, batchNo, true, tradeNo);

        return new PayChannelResultDTO(tradeNo);
    }

    /**
     * 积分支付（积分兑换）
     * @param orders 订单列表
     * @param orderPaymentList 所有支付记录列表
     * @param batchNo 支付次数
     * @param payAmount 支付金额
     * @param payPassword  Aes加密后的支付密码
     * @param fundMode 资金归集模式
     * @return 支付结果
     */
    private PayChannelResultDTO rightPointsPay(List<OrderDO> orders, List<OrderPaymentDO> orderPaymentList, Integer batchNo, BigDecimal payAmount, String payPassword, Integer fundMode) {
        if(!StringUtils.hasLength(payPassword)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_PASSWORD_CAN_NOT_BE_EMPTY);
        }

        //Step 1: 支付金额要大于0
        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 2: 调用会员服务接口进行支付
        String tradeNo = orders.size() == 1 ? orders.get(0).getOrderNo() : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        for (OrderDO order : orders) {
            memberFeignService.calculateMemberUsedRightPoint(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId(), fundMode, payPassword, MemberRightSpendTypeEnum.ORDER, payAmount, tradeNo);
        }

        //Step 3: 支付成功，执行流程
        execOrderPaySerialTasks(orders, orderPaymentList, batchNo, true, tradeNo);
        return new PayChannelResultDTO(tradeNo);
    }

    /**
     * 授信额度支付
     * @param orders 订单列表
     * @param orderPaymentList 所有支付记录列表
     * @param batchNo 支付次数
     * @param payAmount 支付金额
     * @param fundMode 资金归集模式
     * @param payPassword （Aes加密后的）支付密码
     * @return 支付结果
     */
    private PayChannelResultDTO creditPay(List<OrderDO> orders, List<OrderPaymentDO> orderPaymentList, Integer batchNo, BigDecimal payAmount, Integer fundMode, String payPassword) {
        //Step 1: 资金归集模式必须为“会员直接”到账
        if(!fundMode.equals(FundModeEnum.DIRECT_TO_ACCOUNT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_FUND_MODE_MUST_BE_DIRECT_TO_ACCOUNT);
        }

        //判断多订单的供应商是否相同
        List<VendorBO> vendors = orders.stream().map(order -> new VendorBO(order.getVendorMemberId(), order.getVendorRoleId())).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(vendors) || vendors.size() > 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_FUND_MODE_MUST_BE_DIRECT_TO_ACCOUNT);
        }

        //Step 2: 支付金额要大于0
        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 3: 调用支付服务“授信支付”接口，进行支付
        String payResult = payFeignService.creditPay(orders.get(0).getBuyerMemberId(), orders.get(0).getBuyerRoleId(), orders.get(0).getVendorMemberId(), orders.get(0).getVendorRoleId(), fundMode, orders.get(0).getOrderNo(), payAmount, payPassword);

        //Step 4: 交易号为支付服务返回的交易号。直接将支付记录设置为“支付成功”，执行流程
        execOrderPaySerialTasks(orders, orderPaymentList, batchNo, true, payResult);

        return new PayChannelResultDTO(payResult);
    }

    /**
     * 货到付款支付
     * @param orders 订单列表
     * @param orderPaymentList 所有支付记录列表
     * @param batchNo 支付次数
     * @param payAmount 支付金额
     * @param fundMode 资金归集模式
     * @return 支付结果
     */
    private PayChannelResultDTO cashOnDeliveryPay(List<OrderDO> orders, List<OrderPaymentDO> orderPaymentList, Integer batchNo, BigDecimal payAmount, Integer fundMode) {
        //货到付款，只需要直接修改状态
        //Step 1: 支付金额要大于0
        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 2: 直接将支付记录设置为“支付成功”，执行流程
        String tradeNo = orders.size() == 1 ? orders.get(0).getOrderNo() : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        execOrderPaySerialTasks(orders, orderPaymentList, batchNo, true, tradeNo);
        return new PayChannelResultDTO(tradeNo);
    }

    /**
     * 账期、月结支付
     * @param orders 订单列表
     * @param orderPaymentList 所有支付记录列表
     * @param batchNo 支付次数
     * @param payAmount 支付金额
     * @param fundMode 资金归集模式
     * @return 支付结果
     */
    private PayChannelResultDTO settlementPay(List<OrderDO> orders, List<OrderPaymentDO> orderPaymentList, Integer batchNo, BigDecimal payAmount, Integer fundMode) {
        //账期、月结支付，只需要直接修改状态
        //Step 1: 资金归集模式必须为“会员直接”到账
        if(!fundMode.equals(FundModeEnum.DIRECT_TO_ACCOUNT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_FUND_MODE_MUST_BE_DIRECT_TO_ACCOUNT);
        }

        //Step 2: 支付金额要大于0
        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 3: 直接将支付记录设置为“支付成功”，执行流程
        String tradeNo = orders.size() == 1 ? orders.get(0).getOrderNo() : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        execOrderPaySerialTasks(orders, orderPaymentList, batchNo, true, tradeNo);
        return new PayChannelResultDTO(tradeNo);
    }

    /**
     * 通联支付
     * @param loginUser       登录用户
     * @param orderPayChannel 支付渠道
     * @param orders          订单列表
     * @param orderPayments   订单支付记录列表
     * @param fundMode        资金归集模式
     * @param payAmount       支付金额
     * @param weChatCode      小程序登录凭证
     * @return 支付结果
     */
    private PayChannelResultDTO allInPay(UserLoginCacheDTO loginUser, OrderPayChannelEnum orderPayChannel, List<OrderDO> orders, List<OrderPaymentDO> orderPayments, Integer fundMode, BigDecimal payAmount, String weChatCode) {
        //支付金额要大于0
        if (payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        String tradeNo = orders.size() == 1 ? RandomNumberUtil.randomUniqueNumber(orders.get(0).getOrderNo() + CharPool.DASHED, OrderConstant.PAY_ORDER_NO_SHORT_LENGTH) : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        String attach = UUIDUtil.randomUUID();
        PaymentCacheDTO paymentCache = new PaymentCacheDTO(loginUser, tradeNo, payAmount, orderPayments.stream().map(payment -> new OrderPaymentDTO(payment.getOrder().getId(), payment.getId(), payment.getBatchNo())).collect(Collectors.toList()));

        List<PayAmountDTO> payAmounts = orderPayments.stream().map(payment -> new PayAmountDTO(payment.getOrder().getVendorMemberId(), payment.getOrder().getVendorRoleId(), payment.getPayAmount())).collect(Collectors.toList());
        String prePayResult = payFeignService.allInPay(loginUser.getLoginSource(), orderPayChannel, loginUser.getMemberId(), loginUser.getMemberRoleId(), fundMode, payAmount, payAmounts, tradeNo, attach, weChatCode);

        //缓存支付用户信息，用于回调查询
        baseCacheService.cacheOrderPayment(attach, paymentCache);

        //修改支付记录的支付时间，记录微信商户订单号
        //注意此时不能更改支付记录的状态，因为生成支付二维码后，用户有可能不支付
        for (OrderPaymentDO orderPayment : orderPayments) {
            orderPayment.setPayTime(LocalDateTime.now());
            orderPayment.setTradeNo(tradeNo);
        }

        orderPaymentRepository.saveAll(orderPayments);
        return new PayChannelResultDTO(prePayResult, tradeNo);
    }

    /**
     * 建行B2B支付
     * @param loginUser       登录用户
     * @param orderPayChannel 支付渠道
     * @param orders          订单列表
     * @param orderPayments   订单支付记录列表
     * @param batchNo         支付次数
     * @param payAmount       支付金额
     * @return 支付结果
     */
    private PayChannelResultDTO cbcPay(UserLoginCacheDTO loginUser, OrderPayChannelEnum orderPayChannel, List<OrderDO> orders, List<OrderPaymentDO> orderPayments, Integer batchNo, BigDecimal payAmount) {
        //支付金额要大于0
        if (payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 2: 查询支付参数，建行的支付参数在“平台支付参数”中配置
        OrderPaymentParameterDetailBO parameterResult = baseOrderProcessService.findPlatformPaymentParameters(orderPayChannel.getCode());

        if(CollectionUtils.isEmpty(parameterResult.getParameters())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PARAMETER_DOES_NOT_EXIST);
        }

        String merchantId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_MERCHANT_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String password = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_PASSWORD.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String branchId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_BRANCH_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String posId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_POS_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String publicKey = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_PUBLIC_KEY.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String timeoutHours = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_TIME_OUT_HOURS.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");

        if(!StringUtils.hasLength(merchantId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_MERCHANT_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(password)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_PASSWORD_IS_MISSING);
        }

        if(!StringUtils.hasLength(branchId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_BRANCH_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(posId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_POS_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(publicKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_PUBLIC_KEY_IS_MISSING);
        }

        if(!StringUtils.hasLength(timeoutHours)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_TIMEOUT_HOURS_IS_MISSING);
        }

        String tradeNo = orders.size() == 1 ? RandomNumberUtil.randomUniqueNumber(orders.get(0).getOrderNo() + CharPool.DASHED, OrderConstant.PAY_ORDER_NO_SHORT_LENGTH) : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        //建行支付订单号不能有“-”
        tradeNo = tradeNo.replaceAll("-", "");
        //建行支付没法在短时间内回调，所以要用消息队列进行接收支付服务的回调通知
        String attach = SerializeUtil.serialize(new OrderPaymentAttachDTO(orderPayChannel.getCode(), orders.stream().map(OrderDO::getId).collect(Collectors.toList()), batchNo,  tradeNo, loginUser.getUserId()));
        String prePayResult = payFeignService.ccbPay(orders.get(0).getBuyerMemberName(), payAmount, tradeNo, attach, merchantId, password, branchId, posId, publicKey, DateTimeUtil.findDateAfterHours(Long.parseLong(timeoutHours)));

        //修改支付记录的支付时间，记录交易订单号
        //注意建行支付这里有一个“支付中的状态
        for (OrderPaymentDO orderPayment : orderPayments) {
            orderPayment.setPayTime(LocalDateTime.now());
            orderPayment.setTradeNo(tradeNo);
            orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAYING.getCode());
        }

        orderPaymentRepository.saveAll(orderPayments);

        //推送至延迟队列，查询订单结果
        OrderPaymentQueueDTO queueDTO = new OrderPaymentQueueDTO();
        queueDTO.setPayChannel(OrderPayChannelEnum.CCB_B2B.getCode());
        queueDTO.setOrderIds(orders.stream().map(OrderDO::getId).collect(Collectors.toList()));
        queueDTO.setBatchNo(batchNo);
        queueDTO.setTradeNo(tradeNo);
        queueDTO.setPaymentIds(orderPayments.stream().map(OrderPaymentDO::getId).collect(Collectors.toList()));
        queueDTO.setUserId(loginUser.getUserId());
        queueDTO.setUserName(loginUser.getUserName());
        queueDTO.setOrganizationName(loginUser.getOrgName());
        queueDTO.setJobTitle(loginUser.getJobTitle());
        baseOrderScheduleService.scheduleDelayMessage(OrderQueueMessageTypeEnum.PAYMENT_QUERY, queueDTO, Long.parseLong(timeoutHours) * 60 * 60 * 1000);

        return new PayChannelResultDTO(prePayResult, tradeNo);
    }

    /**
     * 查询建行B2B支付结果
     * @param orderPayChannel 支付渠道枚举
     * @param tradeNo 交易号
     * @param orderIds 订单Id列表
     * @param paymentIds 支付记录Id列表
     * @return 查询结果
     */
    private OrderPaymentCallbackStatusEnum findCbcPayStatus(OrderPayChannelEnum orderPayChannel, String tradeNo, List<Long> orderIds, List<Long> paymentIds) {
        //Step 1: 查询支付参数，建行的支付参数在“平台支付参数”中配置
        OrderPaymentParameterDetailBO parameterResult = baseOrderProcessService.findPlatformPaymentParameters(orderPayChannel.getCode());

        if(CollectionUtils.isEmpty(parameterResult.getParameters())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PARAMETER_DOES_NOT_EXIST);
        }

        String merchantId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_MERCHANT_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String password = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_PASSWORD.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String branchId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_BRANCH_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String posId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_B2B_POS_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");

        if(!StringUtils.hasLength(merchantId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_MERCHANT_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(password)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_PASSWORD_IS_MISSING);
        }

        if(!StringUtils.hasLength(branchId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_BRANCH_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(posId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_POS_ID_IS_MISSING);
        }

        return payFeignService.queryCcbOrders(tradeNo, merchantId, branchId, posId, password);
    }

    /**
     * 建行数字人民币支付
     * @param loginUser       当前登录用户
     * @param orderPayChannel 支付渠道
     * @param orders          订单列表
     * @param orderPayments   订单支付记录列表
     * @param batchNo         支付次数
     * @param payAmount       支付金额
     * @param returnUrl       支付后跳转链接
     * @return 支付结果
     */
    private PayChannelResultDTO cbcDigitalPay(UserLoginCacheDTO loginUser, OrderPayChannelEnum orderPayChannel, List<OrderDO> orders, List<OrderPaymentDO> orderPayments, Integer batchNo, BigDecimal payAmount, String returnUrl) {
        //支付金额要大于0
        if (payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //Step 2: 查询支付参数，建行的支付参数在“平台支付参数”中配置
        OrderPaymentParameterDetailBO parameterResult = baseOrderProcessService.findPlatformPaymentParameters(orderPayChannel.getCode());

        if(CollectionUtils.isEmpty(parameterResult.getParameters())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PARAMETER_DOES_NOT_EXIST);
        }

        String merchantId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_DIGITAL_MERCHANT_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String branchId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_DIGITAL_BRANCH_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String posId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_DIGITAL_POS_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String publicKey = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_DIGITAL_PUBLIC_KEY.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String timeoutHours = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_DIGITAL_TIME_OUT_HOURS.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        //这两个是非必填参数，不用校验
        String settlementAccount = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_DIGITAL_SETTLEMENT_ACCOUNT.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
        String subMerchantId = parameterResult.getParameters().stream().filter(p -> p.getCode().equals(OrderPaymentParameterEnum.CCB_DIGITAL_SUB_MERCHANT_ID.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");

        if(!StringUtils.hasLength(merchantId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_MERCHANT_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(branchId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_BRANCH_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(posId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_POS_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(publicKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_PUBLIC_KEY_IS_MISSING);
        }

        if(!StringUtils.hasLength(timeoutHours)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_TIMEOUT_HOURS_IS_MISSING);
        }

        String tradeNo = orders.size() == 1 ? RandomNumberUtil.randomUniqueNumber(orders.get(0).getOrderNo() + CharPool.DASHED, OrderConstant.PAY_ORDER_NO_SHORT_LENGTH) : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        //建行支付订单号不能有“-”
        tradeNo = tradeNo.replaceAll("-", "");
        //建行支付没法在短时间内回调，所以要用消息队列进行接收支付服务的回调通知
        String attach = SerializeUtil.serialize(new OrderPaymentAttachDTO(orderPayChannel.getCode(), orders.stream().map(OrderDO::getId).collect(Collectors.toList()), batchNo, tradeNo, loginUser.getUserId()));
        //App或小程序支付，在returnUrl之后拼接交易号
        if(loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MOBILE.getCode()) && StringUtils.hasLength(returnUrl)) {
            returnUrl = returnUrl.concat("/").concat(tradeNo);
        }

        String prePayResult = payFeignService.ccbDigitalPay(loginUser.getLoginSource(), orders.get(0).getBuyerMemberName(), payAmount, tradeNo, attach, merchantId, branchId, posId, publicKey, DateTimeUtil.findDateAfterHours(Long.parseLong(timeoutHours)), settlementAccount, subMerchantId, returnUrl);

        //修改支付记录的支付时间，记录交易订单号
        //注意建行支付-数字人民币支付没有“支付中的状态
        for (OrderPaymentDO orderPayment : orderPayments) {
            orderPayment.setPayTime(LocalDateTime.now());
            orderPayment.setTradeNo(tradeNo);
        }

        orderPaymentRepository.saveAll(orderPayments);
        return new PayChannelResultDTO(prePayResult, tradeNo);
    }

    /**
     * 跨境电商接口支付
     * @param orders 订单列表
     * @param orderPaymentList 所有支付记录列表
     * @param batchNo 支付次数
     * @param payAmount 支付金额
     * @param fundMode 资金归集模式
     * @return 支付结果
     */
    private PayChannelResultDTO commerceImportPay(List<OrderDO> orders, List<OrderPaymentDO> orderPaymentList, Integer batchNo, BigDecimal payAmount, Integer fundMode) {
        //支付金额要大于0
        if(payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_AMOUNT_MUST_GT_ZERO);
        }

        //直接将支付记录设置为“支付成功”，执行流程
        String tradeNo = orders.size() == 1 ? orders.get(0).getOrderNo() : RandomNumberUtil.randomUniqueNumber(OrderConstant.PAY_ORDER_NO_PREFIX, OrderConstant.PAY_ORDER_NO_LENGTH);
        execOrderPaySerialTasks(orders, orderPaymentList, batchNo, true, tradeNo);
        return new PayChannelResultDTO(tradeNo);
    }

    /**
     * 线下支付 - 支付凭证
     *
     * @param orders 订单列表
     * @param orderPayments 支付记录列表
     * @param vouchers 支付凭证列表
     * @return 更新结果
     */
    private PayChannelResultDTO buyerUpdateVouchers(List<OrderDO> orders, List<OrderPaymentDO> orderPayments, List<String> vouchers, String name, String bankAccount, String bankDeposit) {
        if(CollectionUtils.isEmpty(vouchers)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_VOUCHERS_CAN_NOT_BE_EMPTY);
        }

        orders.forEach(s -> {
            OrderLogisticsDO orderLogistics = new OrderLogisticsDO();
            orderLogistics.setOrder(s);
            orderLogistics.setLogisticsStatus(LogisticsStatusEnum.UPLOAD_INVALID.getCode());
            orderLogistics.setCreateTime(LocalDateTime.now());
            orderLogisticsDORepository.saveAndFlush(orderLogistics);
        });

        for (OrderDO order : orders) {
            baseOrderTaskService.execOrderProcess(order, OrderConstant.AGREE);
        }

        orderPayments.forEach(orderPayment -> {
            orderPayment.setPayTime(LocalDateTime.now());
            orderPayment.setVouchers(vouchers);
            orderPayment.setName(name);
            orderPayment.setBankAccount(bankAccount);
            orderPayment.setBankDeposit(bankDeposit);
            orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode());
            orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode());
            orderPayment.setOuterStatus(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode());
        });

        orderPaymentRepository.saveAll(orderPayments);
        return new PayChannelResultDTO();
    }


    /**
     * new 线下支付 - 支付凭证
     *
     * @param orders 订单列表
     * @param orderPayments 支付记录列表
     * @param vouchers 支付凭证列表
     * @return 更新结果
     */
    private PayChannelResultDTO buyerUpdateVouchersNew(List<OrderDO> orders, List<OrderPaymentDO> orderPayments, List<String> vouchers, String name, String bankAccount, String bankDeposit, List<UnPayOrderInfo> unPayOrderInfos) {
        UnPayOrderInfo unPayOrderInfo = unPayOrderInfos.stream().findFirst().get();
        String tradeNo = unPayOrderInfo.getTradeNo();
        BigDecimal payTotalAmount = unPayOrderInfos.stream().map(UnPayOrderInfo::getUnPayOtherFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        if(CollectionUtils.isEmpty(vouchers)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_VOUCHERS_CAN_NOT_BE_EMPTY);
        }

        orders.forEach(s -> {
            OrderLogisticsDO orderLogistics = new OrderLogisticsDO();
            orderLogistics.setOrder(s);
            orderLogistics.setLogisticsStatus(LogisticsStatusEnum.UPLOAD_INVALID.getCode());
            orderLogistics.setCreateTime(LocalDateTime.now());
            orderLogisticsDORepository.saveAndFlush(orderLogistics);
        });

        for (OrderDO order : orders) {
            baseOrderTaskService.execOrderProcess(order, OrderConstant.AGREE);
        }
        List<SubOrderPaymentDO> subOrderPayments = createSubOrderPayments(tradeNo, orderPayments, OrderPayChannelEnum.OFFLINE.getCode(), OrderPayTypeEnum.OFFLINE_PAYMENT.getCode(), unPayOrderInfos, payTotalAmount);
        subOrderPaymentRepository.saveAll(subOrderPayments);
        orderPayments.forEach(orderPayment -> {
            orderPayment.setPayTime(LocalDateTime.now());
            orderPayment.setVouchers(vouchers);
            orderPayment.setName(name);
            orderPayment.setBankAccount(bankAccount);
            orderPayment.setBankDeposit(bankDeposit);
            orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode());
            orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_PAYMENT.getCode());
            orderPayment.setOuterStatus(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode());
            orderPayment.setPayType(OrderPayTypeEnum.OFFLINE_PAYMENT.getCode());
            orderPayment.setPayChannel(OrderPayChannelEnum.OFFLINE.getCode());
            orderPayment.setTradeNo(tradeNo);
            orderPayment.setServiceFee(BigDecimal.ZERO);
            orderPayment.setSettlementGoldPrice(unPayOrderInfo.getGoldPrice());
        });

        orderPaymentRepository.saveAll(orderPayments);
        return new PayChannelResultDTO();
    }


    /**
     * 查询支付结果
     *
     * @param tradeNo 交易订单号
     * @return 支付结果
     */
    @Override
    public BuyerPayResultDetailResp findPayResult(String tradeNo) {
        //从Redis中查
        PayResultDTO resultDTO = baseCacheService.findOrderPayResult(tradeNo);
        return resultDTO == null ? null : new BuyerPayResultDetailResp(resultDTO.getPaySuccess());
    }

    /**
     * 订单支付回调，通过OpenFeign接口回调
     *
     * @param orders         订单列表
     * @param paymentCache   支付缓存信息
     * @param channelTradeNo 渠道交易订单号
     * @param paySuccess     是否支付成功
     */
    @Override
    public void buyerPayCallback(List<OrderDO> orders, PaymentCacheDTO paymentCache, String channelTradeNo, Boolean paySuccess) {
        //Step 1: 查询所有支付记录
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrderIn(orders);
        if(CollectionUtils.isEmpty(orderPayments)) {
            return;
        }

        //Step 2: 如果有任一条支付次数的状态为“已完成”，返回，避免重复回调
        if(orderPayments.stream().filter(payment -> payment.getBatchNo().equals(paymentCache.getPayments().get(0).getBatchNo())).anyMatch(payment -> payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()))) {
            return;
        }
        
        //Step 3: 执行流程
        execOrderPaySerialTasks(orders, orderPayments, paymentCache.getPayments().get(0).getBatchNo(), paySuccess, paymentCache.getTradeNo(), channelTradeNo);
    }

    /**
     * 订单支付回调，通过消息队列回调
     *
     * @param orderIds         订单Id列表
     * @param batchNo          支付次数
     * @param tradeNo          订单服务生成的支付号
     * @param callbackStatus   支付状态
     * @param userName         用户姓名
     * @param organizationName 用户组织机构名称
     * @param jobTitle         职位
     */
    @Transactional
    @Override
    public void buyerPayCallback(List<Long> orderIds, Integer batchNo, String tradeNo, OrderPaymentCallbackStatusEnum callbackStatus, String userName, String organizationName, String jobTitle) {
        //Step 1: 查询所有订单
        List<OrderDO> orders = orderRepository.findAllById(orderIds);
        if(CollectionUtils.isEmpty(orders)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 2: 查询所有支付记录
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrderIn(orders);
        if(CollectionUtils.isEmpty(orderPayments)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        //Step 3: 记录采购、供应的上一次内部状态，用于发送报表统计数据
        List<OrderStatusDTO> lastStatus = orders.stream().map(order -> new OrderStatusDTO(order.getId(), order.getBuyerInnerStatus(), order.getVendorInnerStatus())).collect(Collectors.toList());

        //Step 4: 更新订单状态
        // a. 如果是“支付中”，不做任何处理，因为在支付时已经设置为“支付中”了
        // b. 如果是“成功”或“失败”，在流程中进行处理
        boolean paySuccess = false;
        switch (callbackStatus) {
            case PAYING:
                return;
            case FAILED:
                break;
            case SUCCESS:
                paySuccess = true;
                break;
            default:
                break;
        }

        //Step 3: 执行流程
        execOrderPaySerialTasks(orders, orderPayments, batchNo, paySuccess, tradeNo);

        //设置订单完成时间
        orders.stream().filter(v -> OrderOuterStatusEnum.ACCOMPLISHED.getCode().equals(v.getOuterStatus())).forEach(v -> v.setFinishTime(LocalDateTime.now()));

        orderRepository.saveAll(orders);

        boolean finalPaySuccess = paySuccess;
        orders.forEach(order -> {
            String remark = OrderStringEnum.NUMBER_OF_PAYMENTS.getName().concat(String.valueOf(batchNo));
            if(finalPaySuccess) {
                //Step 4-2-a : 订单内、外流转记录 ： 内部记录：支付成功；外部记录：一次支付成功，一次确认到账
                baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), userName, organizationName, jobTitle, order.getId(), order.getOrderNo(), OrderOperationEnum.PAY_SUCCESS, order.getBuyerInnerStatus(), remark);
                baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), OrderConstant.SYSTEM_NAME, OrderOperationEnum.CONFIRM_PAYMENT, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), remark);
            } else {
                //Step 4-2-b : 订单内部流转记录 ： 支付失败
                baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), userName, organizationName, jobTitle, order.getId(), order.getOrderNo(), OrderOperationEnum.PAY_FAILED, order.getBuyerInnerStatus(), remark);
            }

            //Step 4-4: 向消息服务发送采购商、供应商实时消息
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(),order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());
            baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(),order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());

            //Step 4-5： 如果是拼团订单，通知营销服务
            baseOrderService.notifyGroupOrder(order);

            //Step 4-5-1: 如果是代发货订单,向营销服务发送优惠券信息
            baseOrderService.sendCoupons(order);

            //Step 4-6 : 订单完成后的操作
            baseOrderService.operateAfterOrderAccomplished(order);
        });
    }

    /**
     * 供应商 - 确认支付结果
     *
     * @param order   订单
     * @param batchNo 支付批次
     * @param agree   确认结果，0-确认未支付，1-确认已经支付
     * @return 操作结果
     */
    @Transactional
    @Override
    public Void vendorConfirmPayment(OrderDO order, Integer batchNo, Integer agree) {
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrder(order);
        if(CollectionUtils.isEmpty(orderPayments)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        List<OrderPaymentDO> currentPayments = orderPayments.stream().filter(payment -> payment.getBatchNo().compareTo(order.getTask().getPayStartNode()) >= 0 && payment.getBatchNo().compareTo(order.getTask().getPayEndNode()) <=0).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(currentPayments)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        OrderPaymentDO orderPayment = currentPayments.stream().filter(payment -> payment.getBatchNo().equals(batchNo)).findFirst().orElse(null);
        if(orderPayment == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_DOES_NOT_EXIST);
        }

        if(orderPayment.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_PAYMENT_CONFIRMED.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_HAS_BEEN_CONFIRMED);
        }

        logger.info("确认支付结果, 订单: {}, 支付批次: {}, 确认结果: {}, 支付方式: {}", order.getOrderNo(), batchNo, agree, orderPayment.getId());

        //Step 1: 更新支付记录的内部状态、外部状态
        // 确认到账：   采购内部状态为“支付成功”， 供应内部状态为“已确认支付结果”， 外部状态为“已完成”
        // 确认未到账： 采购内部状态为“支付成功”， 供应内部状态为“已确认支付结果”， 外部状态为“确认未到账”
        orderPayment.setConfirmTime(agree.equals(OrderConstant.AGREE) ? LocalDateTime.now() : null);
        orderPayment.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode());
        orderPayment.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_PAYMENT_CONFIRMED.getCode());
        orderPayment.setOuterStatus(agree.equals(OrderConstant.AGREE) ? OrderOuterStatusEnum.ACCOMPLISHED.getCode() : OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode());
        HashSet<Long> orderPaymentIdSet = SetUtils.hashSet(orderPayment.getId());
        List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdInAndTradeNoInAndSubStatusAndPayChannelIn(orderPaymentIdSet, SetUtils.hashSet(orderPayment.getTradeNo()), SubPaymentOrderStatusEnum.PENDING.getCode(), OrderPayChannelEnum.ACCOUNT_PAY_LIST);
        if(!CollectionUtils.isEmpty(subOrderPaymentDOS)){
            if(!agree.equals(OrderConstant.AGREE)){
                //解冻授信/余额
                payFeignService.unfrozenbalance(orderPaymentIdSet);
            }
            for (SubOrderPaymentDO subOrderPaymentDO : subOrderPaymentDOS) {
                if(OrderConstant.AGREE.equals(agree)){
                    subOrderPaymentDO.setSubStatus(SubPaymentOrderStatusEnum.SUCCESS.getCode());
                    subOrderPaymentDO.setChannelTradeNo(subOrderPaymentDO.getTradeNo());
                    subOrderPaymentDO.setConfirmTime(DateTimeUtil.getTodayNowLocal());
                }else{
                    subOrderPaymentDO.setSubStatus(SubPaymentOrderStatusEnum.FAILED.getCode());
                }
            }
            orderPayment.setPayAmount(subOrderPaymentDOS.stream().map(SubOrderPaymentDO::getPayAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        subOrderPaymentRepository.saveAll(subOrderPaymentDOS);
        orderPaymentRepository.saveAndFlush(orderPayment);

        OrderLogisticsDO orderLogistics = new OrderLogisticsDO();
        orderLogistics.setOrder(order);
        orderLogistics.setLogisticsStatus(agree.equals(OrderConstant.AGREE) ? LogisticsStatusEnum.MERCHANT_CONFIRMED.getCode() : LogisticsStatusEnum.UPLOAD_VALID.getCode());
        orderLogistics.setCreateTime(LocalDateTime.now());
        orderLogisticsDORepository.saveAndFlush(orderLogistics);
        //Step 2: 如果确认到账，且有支付未完成，进行循环，否则执行跳转条件
        if(agree.equals(OrderConstant.AGREE)) {
            if(currentPayments.stream().allMatch(payment -> payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()))) {
                baseOrderTaskService.execOrderProcess(order, OrderConstant.AGREE);
            } else {
                baseOrderTaskService.execOrderProcess(order, OrderConstant.PAY_SERIAL_UNCOMPLETED);
            }
        } else {
            baseOrderTaskService.execOrderProcess(order, OrderConstant.DISAGREE);
        }
        //Step 4: 判断是否已经成功支付过一次，设置订单支付金额
        order.setPaidAmount(orderPayments.stream().filter(payment -> payment.getPayAmount().compareTo(BigDecimal.ZERO) > 0 && payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode())).map(OrderPaymentDO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        order.setHasPaid(order.getPaidAmount().compareTo(BigDecimal.ZERO) > 0);
        //订单支付完所有环节，则把总金额改成支付金额
        if(((batchNo == 1 && CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode()))
                || (batchNo == 2 && CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode()))) && OrderConstant.AGREE.equals(agree)){
            order.setTotalAmount(order.getPaidAmount());
            order.setSettlementGoldPrice(orderPayment.getSettlementGoldPrice());
            orderService.saveOrderProductPrice(order.getId(), orderPayment.getSettlementGoldPrice());
        }
        //Step 6: 订单商品加权平均已支付金额
        baseOrderProductService.updateProductPayAmount(order, order.getPaidAmount());

        orderRepository.saveAndFlush(order);
        //Step 5-1 :支付成功后转单,判断是否是现货订单，如果是自动跳过生产流程
        orderService.afterOrderPaySuccess(order, batchNo);
        return null;
    }

    /**
     * 售后服务 - 退货 - 修改支付次数的金额
     *
     * @param orders        订单列表
     * @param returnAmounts 订单Id及退款金额
     */
    @Override
    public void batchUpdateOrderPayments(List<OrderDO> orders, List<OrderReturnAmountDTO> returnAmounts) {
        if(CollectionUtils.isEmpty(returnAmounts)) {
            return;
        }

        //查询支付金额大于0，且外部状态为“待支付”和“确认未到账”的支付记录
        List<Integer> outerStatus = Stream.of(OrderOuterStatusEnum.TO_PAY.getCode(), OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode()).collect(Collectors.toList());
        List<OrderPaymentDO> orderPaymentList = orderPaymentRepository.findByOrderInAndPayAmountGreaterThanAndOuterStatusIn(orders, BigDecimal.ZERO, outerStatus);

        for (OrderReturnAmountDTO returnAmount : returnAmounts) {
            List<OrderPaymentDO> orderPayments = orderPaymentList.stream().filter(payment -> payment.getOrder().getId().equals(returnAmount.getOrderId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(orderPayments)) {
                continue;
            }

            //要加上运费
            BigDecimal leftAmount = returnAmount.getLeftAmount().add(orders.stream().filter(order -> order.getId().equals(returnAmount.getOrderId())).map(OrderDO::getFreight).findFirst().orElse(BigDecimal.ZERO));

            Map<Integer, BigDecimal> payAmountMap = averageLeftPayAmount(leftAmount, orderPayments);

            orderPayments.forEach(payment -> payment.setPayAmount(payAmountMap.getOrDefault(payment.getBatchNo(), payment.getPayAmount())));
        }

        orderPaymentRepository.saveAll(orderPaymentList);
    }

    /**
     * 拼团订单退款
     *
     * @param order 订单
     * @return 退款结果
     */
    @Override
    public Boolean groupOrderRefund(OrderDO order) {
        //Step 1: 退款条件判断
        //Step 1-1: 订单如果不是“拼团失败”，返回
        if(!order.getPromotionStatus().equals(OrderPromotionStatusEnum.GROUP_FAILED.getCode())) {
            return false;
        }

        //Step 1-2: 订单已经全部退款，返回
        if(Objects.nonNull(order.getHasRefund()) && order.getHasRefund()) {
            return false;
        }

        //Step 1-3: 如果订单没有支付过，返回
        if(!order.getHasPaid()) {
            return false;
        }

        //Step 1-4: 如果订单不是一次支付100%金额，返回
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findByOrder(order);
        if(CollectionUtils.isEmpty(orderPayments)) {
            return false;
        }

        OrderPaymentDO orderPayment = orderPayments.stream().filter(payment -> payment.getPayRate().compareTo(BigDecimal.ONE) == 0 && payment.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode())).findFirst().orElse(null);
        if(orderPayment == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CAN_NOT_REFUND_BECAUSE_OF_MANY_PAYMENTS);
        }

        return orderRefund(order, orderPayment, "拼团失败");
    }

    /**
     * 拼团订单退款
     * @param order 订单
     * @param orderPayment 要退款的支付记录
     * @param remark 备注说明
     * @return 退款结果
     */
    @Override
    public Boolean orderRefund(OrderDO order, OrderPaymentDO orderPayment, String remark) {
        //Step 2: 支付方式和支付渠道
        OrderPayTypeEnum orderPayType = OrderPayTypeEnum.parse(orderPayment.getPayType());
        OrderPayChannelEnum orderPayChannel = OrderPayChannelEnum.parse(orderPayment.getPayChannel());
        if(orderPayType == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_TYPE_MISMATCH);
        }

        if(orderPayChannel == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_CHANNEL_MISMATCH);
        }

        //Step 2-1: 根据不同的支付方式、支付渠道，调用不同的接口
        String refundResult = "";
        switch (orderPayType) {
            case ONLINE_PAYMENT:
                switch (orderPayChannel) {
                    case ALIPAY:
                        refundResult = aliPayRefund(order, orderPayment);
                        break;
                    case WECHAT_PAY:
                        refundResult = weChatRefund(order, orderPayment);
                        break;
                    case UNION_PAY:
                        break;
                    case ACCOUNT_BALANCE:
                        refundResult = balancePayRefund(orderPayment, remark);
                        break;
                }
                break;
            case OFFLINE_PAYMENT:
                //线下支付方式，直接走售后流程，不需要处理
                throw new BusinessException(ResponseCodeEnum.ORDER_OFFLINE_REFUND_HANDLE_IN_THE_AFTER_SALES_SERVICE);
            case CREDIT_LINE_PAYMENT:
                refundResult = creditPayRefund(order, orderPayment, remark);
                break;
            case CASH_ON_DELIVERY:
                //货到付款方式，直接走售后流程，不需要处理
                throw new BusinessException(ResponseCodeEnum.ORDER_CASH_ON_DELIVERY_REFUND_HANDLE_IN_THE_AFTER_SALES_SERVICE);
            case SETTLEMENT:
                //结算支付方式，直接走售后流程，不需要处理
                throw new BusinessException(ResponseCodeEnum.ORDER_SETTLEMENT_REFUND_HANDLE_IN_THE_AFTER_SALES_SERVICE);
            case MEMBER_RIGHT:
                //积分支付方式，只能换货，不能退款，不需要处理
                throw new BusinessException(ResponseCodeEnum.ORDER_RIGHTS_REFUND_HANDLE_IN_THE_AFTER_SALES_SERVICE);
            case ALLIN_PAY:
                refundResult = allInPayRefund(order, orderPayment);
                break;
            case CCB_PAY:
                switch (orderPayChannel) {
                    case CCB_B2B:
                        refundResult = ccbPayRefund(order, orderPayment);
                        break;
                    case CCB_DIGITAL:
                        refundResult = ccbDigitalPayRefund(order, orderPayment);
                        break;
                }
            default:
                break;
        }

        orderPayment.setRefundNo(refundResult);
        order.setHasRefund(true);

        orderPaymentRepository.saveAndFlush(orderPayment);
        orderRepository.saveAndFlush(order);

        return true;
    }

    /**
     * 更新支付记录的结算状态
     *
     * @param paymentIds       支付记录
     * @param settlementStatus 结算状态
     * @return 更新结果
     */
    @Override
    public Void updatePaymentSettlementStatus(List<Long> paymentIds, Integer settlementStatus) {
        List<OrderPaymentDO> orderPayments = orderPaymentRepository.findAllById(paymentIds);
        orderPayments.forEach(payment -> payment.setSettlementStatus(settlementStatus));
        orderPaymentRepository.saveAll(orderPayments);
        return null;
    }

    /**
     * （调用支付服务接口）查询支付状态
     *
     * @param payChannel 支付渠道
     * @param tradeNo    交易号
     * @param orderIds   订单Id列表
     * @param paymentIds 支付记录列表
     * @return 查询结果
     */
    @Override
    public OrderPaymentCallbackStatusEnum findPaymentStatus(Integer payChannel, String tradeNo, List<Long> orderIds, List<Long> paymentIds) {
        OrderPayChannelEnum orderPayChannel = OrderPayChannelEnum.parse(payChannel);
        if(orderPayChannel == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_CHANNEL_MISMATCH);
        }

        if (orderPayChannel == OrderPayChannelEnum.CCB_B2B) {
            return findCbcPayStatus(orderPayChannel, tradeNo, orderIds, paymentIds);
        }

        return OrderPaymentCallbackStatusEnum.PAYING;
    }

    /**
     * 微信退款
     * @param order 订单
     * @param orderPayment 支付记录
     * @return 退款结果
     */
    private String weChatRefund(OrderDO order, OrderPaymentDO orderPayment) {
        //Step 1: 查询支付参数
        OrderPaymentParameterDetailBO parameterResult = orderPayment.getFundMode().equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? baseOrderProcessService.findPlatformPaymentParameters(OrderPayChannelEnum.WECHAT_PAY.getCode()) : baseOrderProcessService.findMemberPaymentParameters(OrderPayChannelEnum.WECHAT_PAY.getCode(), order.getVendorMemberId(), order.getVendorRoleId());

        if(CollectionUtils.isEmpty(parameterResult.getParameters())) {
            throw new BusinessException(orderPayment.getFundMode().equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? ResponseCodeEnum.ORDER_PLATFORM_PARAMETER_DOES_NOT_EXIST : ResponseCodeEnum.ORDER_MEMBER_PARAMETER_DOES_NOT_EXIST);
        }

        String merchantId = findPaymentParameter(parameterResult.getParameters(), OrderPaymentParameterEnum.WECHAT_MERCHANT_ID);
        String appId = findPaymentParameter(parameterResult.getParameters(), OrderPaymentParameterEnum.WECHAT_APP_ID);
        String apiKey = findPaymentParameter(parameterResult.getParameters(), OrderPaymentParameterEnum.WECHAT_API_KEY);
        String keyPath = findPaymentParameter(parameterResult.getParameters(), OrderPaymentParameterEnum.WECHAT_KEY_PATH);

        if(!StringUtils.hasLength(merchantId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_MERCHANT_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(appId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_APP_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(apiKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WECHAT_API_KEY_IS_MISSING);
        }

        //生成退款单号
        String refundNo = UUIDUtil.randomUUID();
        payFeignService.weChatRefund(order.getVendorMemberId(), order.getVendorRoleId(), orderPayment.getFundMode(), orderPayment.getTradeNo(), orderPayment.getPayAmount(), refundNo, orderPayment.getPayAmount(), merchantId, appId, apiKey, keyPath);
        return refundNo;
    }

    /**
     * 支付宝退款
     * @param order 订单
     * @param orderPayment 支付记录
     * @return 退款结果
     */
    private String aliPayRefund(OrderDO order, OrderPaymentDO orderPayment) {
        OrderPaymentParameterDetailBO parameterResult = orderPayment.getFundMode().equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? baseOrderProcessService.findPlatformPaymentParameters(ALIPAY.getCode()) : baseOrderProcessService.findMemberPaymentParameters(ALIPAY.getCode(), order.getVendorMemberId(), order.getVendorRoleId());

        String appId = findPaymentParameter(parameterResult.getParameters(), OrderPaymentParameterEnum.ALIPAY_APP_ID);
        String publicKey = findPaymentParameter(parameterResult.getParameters(), OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY);
        String privateKey = findPaymentParameter(parameterResult.getParameters(), OrderPaymentParameterEnum.ALIPAY_PRIVATE_KEY);
        String appAuthToken = findPaymentParameter(parameterResult.getParameters(), OrderPaymentParameterEnum.ALIPAY_THREAD_APP_AUTH_TOKEN);
        if(!StringUtils.hasLength(appId)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_ALIPAY_APP_ID_IS_MISSING);
        }

        if(!StringUtils.hasLength(publicKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_ALIPAY_PUBLIC_KEY_IS_MISSING);
        }

        if(!StringUtils.hasLength(privateKey)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_ALIPAY_PRIVATE_KEY_MISSING);
        }

        if(orderPayment.getFundMode().equals(FundModeEnum.DIRECT_TO_ACCOUNT.getCode()) && !StringUtils.hasLength(appAuthToken)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_ALIPAY_APP_AUTH_TOKEN_IS_MISSING);
        }

        //生成退款单号
        String refundNo = UUIDUtil.randomUUID();
        payFeignService.aliPayRefund(order.getVendorMemberId(), order.getVendorRoleId(), orderPayment.getFundMode(), orderPayment.getTradeNo(), refundNo, orderPayment.getPayAmount(), appId, publicKey, privateKey, appAuthToken);
        return refundNo;
    }

    /**
     * 余额支付退款
     * @param orderPayment 支付记录
     * @param remark 备注说明
     * @return 退款结果
     */
    private String balancePayRefund(OrderPaymentDO orderPayment, String remark) {
        logger.info("余额支付退款开始 => " + orderPayment.getOrder().getId());
        String refundNo = UUIDUtil.randomUUID();
        payFeignService.balancePayRefund(orderPayment.getTradeNo(), orderPayment.getPayAmount(), remark);
        return refundNo;
    }

    /**
     * 余额支付退款
     * @param subOrderPayment 支付记录
     * @param remark 备注说明
     * @return 退款结果
     */
    private String balancePayRefundNew(SubOrderPaymentDO subOrderPayment, String remark) {
        logger.info("余额支付退款开始 => " + subOrderPayment.getOrder().getId());
        String refundNo = UUIDUtil.randomUUID();
        payFeignService.balancePayRefund(subOrderPayment.getOrderPayment().getId().toString(), subOrderPayment.getPayAmount(), remark);
        return refundNo;
    }

    /**
     * 授信支付退款
     * @param order 订单
     * @param orderPayment 支付记录
     * @param remark 备注说明
     * @return 退款结果
     */
    private String creditPayRefund(OrderDO order, OrderPaymentDO orderPayment, String remark) {
        //Step 1: 资金归集模式必须为“会员直接”到账
        if(!orderPayment.getFundMode().equals(FundModeEnum.DIRECT_TO_ACCOUNT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_FUND_MODE_MUST_BE_DIRECT_TO_ACCOUNT);
        }

        return payFeignService.creditPayRefund(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId(), orderPayment.getTradeNo(), orderPayment.getPayAmount(), remark);
    }

    /**
     * 通联支付退款
     * @param order 订单
     * @param orderPayment 支付记录
     * @return 退款结果
     */
    private String allInPayRefund(OrderDO order, OrderPaymentDO orderPayment) {
        //生成退款单号
        String refundNo = UUIDUtil.randomUUID();
        payFeignService.allInPayRefund(order.getBuyerBizUserId(), order.getBuyerMemberId(), order.getBuyerRoleId(), orderPayment.getFundMode(), orderPayment.getPayAmount(), orderPayment.getTradeNo(), refundNo);
        return refundNo;
    }

    /**
     * 建行B2B支付退款
     * @param order 订单
     * @param orderPayment 支付记录
     * @return 退款结果
     */
    private String ccbPayRefund(OrderDO order, OrderPaymentDO orderPayment) {
        //生成退款单号
        String refundNo = UUIDUtil.randomUUID();
        payFeignService.ccbPayRefund(orderPayment.getTradeNo(), refundNo, orderPayment.getPayAmount());
        return refundNo;
    }

    /**
     * 建行数字人民币支付退款
     * @param order 订单
     * @param orderPayment 支付记录
     * @return 退款结果
     */
    private String ccbDigitalPayRefund(OrderDO order, OrderPaymentDO orderPayment) {
        //生成退款单号
        String refundNo = UUIDUtil.randomUUID();
        payFeignService.ccbDigitalPayRefund(order.getCreateTime(), orderPayment.getTradeNo(), refundNo, orderPayment.getPayAmount());

        return refundNo;
    }

    /**
     * 根据支付参数枚举，查找参数
     * @param parameters 平台或会员支付参数设置查询到的支付参数列表
     * @param parameterEnum 支付参数枚举
     * @return 支付参数
     */
    private String findPaymentParameter(List<PayChannelParameterBO> parameters, OrderPaymentParameterEnum parameterEnum) {
        return parameters.stream().filter(p -> p.getCode().equals(parameterEnum.getCode())).map(PayChannelParameterBO::getValue).findFirst().orElse("");
    }

    /**
     * 退货后，计算剩余支付次数的支付金额
     * @param leftAmount （退货后的）商品总额
     * @param orderPayments 支付次数，支付比例保留4位小数
     * @return 每批次支付的金额
     */
    private Map<Integer, BigDecimal> averageLeftPayAmount(BigDecimal leftAmount, List<OrderPaymentDO> orderPayments) {
        if(orderPayments.size() == 1) {
            return orderPayments.stream().collect(Collectors.toMap(OrderPaymentDO::getBatchNo, payment -> leftAmount.multiply(payment.getPayRate()).setScale(2, RoundingMode.HALF_UP)));
        }

        Integer maxBatch = orderPayments.stream().map(OrderPaymentDO::getBatchNo).max(Comparator.comparingInt(Integer::intValue)).orElse(0);
        Map<Integer, BigDecimal> map = orderPayments.stream().filter(payment -> payment.getBatchNo().compareTo(maxBatch) < 0).collect(Collectors.toMap(OrderPaymentDO::getBatchNo, payment -> leftAmount.multiply(payment.getPayRate()).setScale(2, RoundingMode.HALF_UP)));
        map.put(maxBatch, leftAmount.subtract(map.values().stream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
        return map;
    }



    /**
     * 订单退款
     * @param order 订单
     * @param orderPayment 要退款的支付记录
     * @param remark 备注说明
     * @return 退款结果
     */
    @Transactional
    @Override
    public Boolean orderRefundNew(OrderDO order, OrderPaymentDO orderPayment, String remark) {
        //查询子支付记录，计算总费用，在进行退款
        List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdAndTradeNo(orderPayment.getId(), orderPayment.getTradeNo());
        subOrderPaymentDOS = subOrderPaymentDOS.stream().filter(subOrderPaymentDO -> !StringUtils.hasText(subOrderPaymentDO.getRefundNo())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(subOrderPaymentDOS)){
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_TYPE_MISMATCH);
        }
        OrderPayTypeEnum orderPayType = OrderPayTypeEnum.parse(orderPayment.getPayType());
        if(orderPayType == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAY_TYPE_MISMATCH);
        }
        //微企付退款金额
//        BigDecimal refundAmount = subOrderPaymentDOS.stream().map(SubOrderPaymentDO::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//        BigDecimal payAmount = orderPayment.getPayRate().multiply(orderPayment.getPayAmount());
//        if(refundAmount.compareTo(payAmount) > 0) {
//            throw new BusinessException("退款金额不能大于总支付金额");
//        }
        //boolean isBalanceFLag = subOrderPaymentDOS.stream().anyMatch(e -> OrderPayChannelEnum.WEI_QI_FU.getCode().equals(e.getPayChannel()));
        //List<Integer>  payChannels = subOrderPaymentDOS.stream().map(SubOrderPaymentDO::getPayChannel).collect(Collectors.toList());
//        if(isBalanceFLag){
//            balancePayRefundNew(orderPayment, remark);
//        }
        //Step 2-1: 根据不同的支付方式、支付渠道，调用不同的接口
//        List<Integer> channels = payChannels.stream().filter(channel -> !OrderPayChannelEnum.CREDIT.getCode().equals(channel) && !OrderPayChannelEnum.ACCOUNT_BALANCE.getCode().equals(channel)).collect(Collectors.toList());
//        if(CollectionUtils.isEmpty(channels)){
//            return true;
//        }
        String refundResult = "";
        for (SubOrderPaymentDO subOrderPaymentDO : subOrderPaymentDOS) {
            OrderPayChannelEnum orderPayChannel = OrderPayChannelEnum.parse(subOrderPaymentDO.getPayChannel());
            OrderPayTypeEnum orderPayTypeEnum = OrderPayTypeEnum.parse(subOrderPaymentDO.getPayType());
            switch (orderPayTypeEnum) {
                case ONLINE_PAYMENT:
                    switch (orderPayChannel) {
                        case CREDIT:
                        case ACCOUNT_BALANCE:
                            refundResult = balancePayRefundNew(subOrderPaymentDO, remark);
                            break;
                        case ALIPAY:
                            refundResult = aliPayRefund(order, orderPayment);
                            break;
                        case WECHAT_PAY:
                            refundResult = weChatRefund(order, orderPayment);
                            break;
                        case UNION_PAY:
                            break;
                        case WEI_QI_FU:
                            refundResult = weiQiFuRefund(subOrderPaymentDO, subOrderPaymentDO.getPayAmount(), remark);
                            break;
                    }
                    break;
                case OFFLINE_PAYMENT:
                    //线下支付方式，直接走售后流程，不需要处理
                    throw new BusinessException(ResponseCodeEnum.ORDER_OFFLINE_REFUND_HANDLE_IN_THE_AFTER_SALES_SERVICE);
                case CREDIT_LINE_PAYMENT:
                    refundResult = creditPayRefund(order, orderPayment, remark);
                    break;
                case CASH_ON_DELIVERY:
                    //货到付款方式，直接走售后流程，不需要处理
                    throw new BusinessException(ResponseCodeEnum.ORDER_CASH_ON_DELIVERY_REFUND_HANDLE_IN_THE_AFTER_SALES_SERVICE);
                case SETTLEMENT:
                    //结算支付方式，直接走售后流程，不需要处理
                    throw new BusinessException(ResponseCodeEnum.ORDER_SETTLEMENT_REFUND_HANDLE_IN_THE_AFTER_SALES_SERVICE);
                case MEMBER_RIGHT:
                    //积分支付方式，只能换货，不能退款，不需要处理
                    throw new BusinessException(ResponseCodeEnum.ORDER_RIGHTS_REFUND_HANDLE_IN_THE_AFTER_SALES_SERVICE);
                case ALLIN_PAY:
                    refundResult = allInPayRefund(order, orderPayment);
                    break;
                case CCB_PAY:
                    switch (orderPayChannel) {
                        case CCB_B2B:
                            refundResult = ccbPayRefund(order, orderPayment);
                            break;
                        case CCB_DIGITAL:
                            refundResult = ccbDigitalPayRefund(order, orderPayment);
                            break;
                    }
                default:
                    break;
            }
            subOrderPaymentDO.setRefundNo(refundResult);
        }
        orderPayment.setRefundNo(refundResult);
        order.setHasRefund(true);

        orderPaymentRepository.saveAndFlush(orderPayment);
        subOrderPaymentRepository.saveAll(subOrderPaymentDOS);
        orderRepository.saveAndFlush(order);
        return true;
    }

    public String weiQiFuRefund(SubOrderPaymentDO subOrderPayment, BigDecimal refundAmount, String remark) {
        //生成退款单号
        String refundNo = UUIDUtil.randomUUID();
        WeiQiFuPayRefundReq req = new WeiQiFuPayRefundReq();
        req.setOutPlatformId(subOrderPayment.getTradeNo());
        long amount = refundAmount.multiply(BigDecimal.valueOf(100)).longValue();
        long channelPayTotalAmount = subOrderPayment.getChannelPayTotalAmount().multiply(BigDecimal.valueOf(100)).longValue();
        req.setRefundAmount(amount);
        req.setTotalAmount(channelPayTotalAmount);
        req.setOutRefundId(refundNo);
        req.setRefundReason(remark);
        WrapperUtil.throwWhenFailAndLog(weiQiFuPayFeign.refund(req), log);
        return refundNo;
    }

}
