package com.ssy.lingxi.order.serviceImpl.platform;

import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.PlatformRuleTypeEnum;
import com.ssy.lingxi.component.base.enums.settle.SettlementTypeEnum;
import com.ssy.lingxi.order.api.model.resp.PlatformSettlementTypeResp;
import com.ssy.lingxi.order.repository.BaseOrderRuleRepository;
import com.ssy.lingxi.order.service.platform.IPlatformSettlementTypeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 平台后台 - 结算支付方式相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-9
 */
@Service
public class PlatformSettlementTypeServiceImpl implements IPlatformSettlementTypeService {

    @Resource
    private BaseOrderRuleRepository baseOrderRuleRepository;

    @Override
    public List<PlatformSettlementTypeResp> findSettlementTypeByEnable() {
        return baseOrderRuleRepository.findByRuleTypeAndStatus(PlatformRuleTypeEnum.SETTLEMENT_TYPE.getCode(), EnableDisableStatusEnum.ENABLE.getCode()).stream().map(map -> new PlatformSettlementTypeResp(map.getMethodCode(), SettlementTypeEnum.getName(map.getMethodCode()))).collect(Collectors.toList());
    }

    @Override
    public List<PlatformSettlementTypeResp> findSettlementCategoryByEnable() {
        return baseOrderRuleRepository.findByRuleTypeAndStatus(PlatformRuleTypeEnum.SETTLEMENT.getCode(), EnableDisableStatusEnum.ENABLE.getCode()).stream().map(map -> new PlatformSettlementTypeResp(map.getMethodCode(), map.getMethodName())).collect(Collectors.toList());
    }
}
