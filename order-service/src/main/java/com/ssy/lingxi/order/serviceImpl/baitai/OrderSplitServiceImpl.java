package com.ssy.lingxi.order.serviceImpl.baitai;

import cn.hutool.core.collection.CollUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.rest.model.resp.eos.LockProStockResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.entity.OrderProductDO;
import com.ssy.lingxi.order.enums.OrderOuterStatusEnum;
import com.ssy.lingxi.order.model.req.mobile.BuyerOrderListReq;
import com.ssy.lingxi.order.model.req.mobile.BuyerOrderReq;
import com.ssy.lingxi.order.model.resp.basic.OrderCompleteResp;
import com.ssy.lingxi.order.repository.OrderRepository;
import com.ssy.lingxi.order.service.baitai.OrderSplitService;
import com.ssy.lingxi.order.service.base.IBaseOrderService;
import com.ssy.lingxi.order.service.web.IOrderCreationService;
import com.ssy.lingxi.order.util.OrderCommissionMqUtil;
import com.ssy.lingxi.product.api.enums.FreightSpaceSingleProductStatusEnum;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.model.req.baitai.SpaceSingleProductStatusReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/7/28
 */
@Slf4j
@Service
public class OrderSplitServiceImpl implements OrderSplitService {

    @Resource
    private IOrderCreationService orderCreationService;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private ICommodityFeign commodityFeign;

    @Resource
    private EosApiService eosApiService;

    @Resource
    private IBaseOrderService baseOrderService;

    @Resource
    private OrderCommissionMqUtil orderCommissionMqUtil;

    @Override
    @Transactional
    public OrderCompleteResp createBuyerSplitOrder(UserLoginCacheDTO loginUser, BuyerOrderListReq orderListVO) {
        List<OrderCompleteResp> orderCompleteRespList = new ArrayList<>();
        // 校验当前会员及角色，在这个门店是否有未支付的订单
        Optional<BuyerOrderReq> orderReq = orderListVO.getBuyerOrderReqList().stream().findFirst();
        if (CommoditySaleModeEnum.SPOT.getCode().equals(orderReq.get().getSaleMode())) {
            long counted = orderRepository.countByBuyerMemberIdAndBuyerRoleIdAndOuterStatusAndBranchIdAndSaleMode(loginUser.getMemberId(), loginUser.getMemberRoleId(), OrderOuterStatusEnum.TO_PAY.getCode(), orderReq.get().getBranchId(), CommoditySaleModeEnum.SPOT.getCode());
            if (counted > 0) {
                throw new BusinessException("店铺有未支付的现货定单，请完成支付或取消后再下单！");
            }
        }

        // 创建订单
        orderListVO.getBuyerOrderReqList().forEach(s -> {
            orderCompleteRespList.add(orderCreationService.createBuyerOrder(loginUser, s));
        });

        // 获取所有订单
        List<Long> orders = orderCompleteRespList.stream().flatMap(resp -> resp.getOrderIds().stream())
                .collect(Collectors.toList());
        List<OrderDO> orderDOS = orderRepository.findAllById(orders);
        // 如果是现货订单，则锁库存,获取商品单件getProducts里的id
        if (CommoditySaleModeEnum.SPOT.getCode().equals(orderReq.get().getSaleMode())) {
            proStockLockStatus(orderDOS);
        }

        // 发送订单创建分佣消息
        orderDOS.forEach(orderDO -> {
            orderCommissionMqUtil.sendOrderCreatedMessage(orderDO.getOrderNo(),
                    orderDO.getBuyerUserId(),
                    orderDO.getBuyerMemberId(),
                    orderDO.getBuyerRoleId(),
                    orderDO.getTotalAmount(),
                    orderDO.getTotalWeight());
        });

        // 获取购物车id
        List<Long> cartIds = orderDOS.stream()
                .flatMap(order -> order.getProducts().stream()
                        .map(OrderProductDO::getCartId)
                        .filter(Objects::nonNull))
                .distinct()
                .collect(Collectors.toList());

        // 通知搜索服务，删除购物车
        baseOrderService.emptyProductCarts(orderReq.get().getShopType(), cartIds);

        // 整理提交订单后，返回的结果
        List<Long> orderIds = new ArrayList<>();
        OrderCompleteResp orderCompleteResp = orderCompleteRespList.stream().findFirst().get();
        orderCompleteRespList.forEach(s -> {
            orderIds.addAll(s.getOrderIds());
            orderCompleteResp.setPaymentRequired(s.getPaymentRequired());
            orderCompleteResp.setFundMode(s.getFundMode());
            orderCompleteResp.setBatchNo(s.getBatchNo());
            orderCompleteResp.setPayType(s.getPayType());
            orderCompleteResp.setPayChannel(s.getPayChannel());
        });
        orderCompleteResp.setPayAmount(orderCompleteRespList.stream().map(OrderCompleteResp::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        orderCompleteResp.setOrderIds(orderIds);
        return orderCompleteResp;
    }

    /**
     * 锁定商品单件库存
     * @param orders
     */
    private void proStockLockStatus(List<OrderDO> orders) {
        List<Long> commoditySingleIdList = orders.stream()
                .filter(s -> CommoditySaleModeEnum.SPOT.getCode().equals(s.getSaleMode()))
                .flatMap(order -> order.getProducts().stream().map(OrderProductDO::getCommoditySingleId))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(commoditySingleIdList)) {
            SpaceSingleProductStatusReq commoditySingleProductStatusReq = new SpaceSingleProductStatusReq();
            commoditySingleProductStatusReq.setSingleProductIds(commoditySingleIdList);
            commoditySingleProductStatusReq.setStatus(FreightSpaceSingleProductStatusEnum.OCCUPY.getCode());
            WrapperResp<Void> voidWrapperResp = commodityFeign.updateFreightSpaceSingleProductStatus(commoditySingleProductStatusReq);
            if (WrapperUtil.isFail(voidWrapperResp)) {
                log.error("更新现货商品单件状态失败: {}", voidWrapperResp.getMessage());
                throw new BusinessException(voidWrapperResp.getCode(), voidWrapperResp.getMessage());
            }
        }
        List<String> singleCodeList = orders.stream()
                .filter(s -> CommoditySaleModeEnum.SPOT.getCode().equals(s.getSaleMode()))
                .flatMap(order -> order.getProducts().stream().map(OrderProductDO::getSingleCode))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(singleCodeList)) {
            //TODO 接口修改，先注释
            List<LockProStockResp> lockProStockResps = eosApiService.orderProStockLockStatus(singleCodeList);
            // 筛选出lockProStockResps里的commodityStatus的状态为"没库存"的信息，抛出异常
            List<LockProStockResp> noStockList = lockProStockResps.stream()
                    .filter(resp -> "锁定失败".equals(resp.getCommodityStatus()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(noStockList)) {
                // 筛选出noStockList里的商品条码, 用逗号拼接
                String noStockNames = noStockList.stream()
                        .map(LockProStockResp::getBarCode)
                        .collect(Collectors.joining(", "));
                throw new BusinessException("锁定现货商品单件失败: 没有库存的商品条码: " + noStockNames);
            }
        }
    }
}
