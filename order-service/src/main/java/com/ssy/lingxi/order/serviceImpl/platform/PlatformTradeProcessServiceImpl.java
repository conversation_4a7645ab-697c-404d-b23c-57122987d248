package com.ssy.lingxi.order.serviceImpl.platform;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.engine.DeleteBaseProcessReq;
import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.req.engine.ProcessEngineReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayNodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTradeProcessTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.engine.api.enums.ProcessDefaultEnum;
import com.ssy.lingxi.engine.api.enums.ProcessSourceEnum;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.BaseTradeProcessDO;
import com.ssy.lingxi.order.entity.OrderTradeProcessDO;
import com.ssy.lingxi.order.entity.PlatformTradeProcessDO;
import com.ssy.lingxi.order.entity.PlatformTradeProcessMemberDO;
import com.ssy.lingxi.order.enums.BaseTradeProcessEnum;
import com.ssy.lingxi.order.model.bo.PayNodeBO;
import com.ssy.lingxi.order.model.dto.OrderMemberQueryDTO;
import com.ssy.lingxi.order.model.dto.ProcessQueryRequest;
import com.ssy.lingxi.order.model.dto.SaveDefaultRequest;
import com.ssy.lingxi.order.model.req.basic.OrderProcessIdReq;
import com.ssy.lingxi.order.model.req.basic.OrderProcessUpdateStatusReq;
import com.ssy.lingxi.order.model.req.platform.PlatformProcessMemberPageDataReq;
import com.ssy.lingxi.order.model.req.platform.PlatformProcessPageDataReq;
import com.ssy.lingxi.order.model.req.platform.PlatformProcessReq;
import com.ssy.lingxi.order.model.req.platform.PlatformProcessUpdateReq;
import com.ssy.lingxi.order.model.resp.platform.PlatformBaseTradeProcessResp;
import com.ssy.lingxi.order.model.resp.platform.PlatformProcessPageQueryResp;
import com.ssy.lingxi.order.model.resp.platform.TradeProcessDetailResp;
import com.ssy.lingxi.order.model.resp.process.BaseTradeProcessResp;
import com.ssy.lingxi.order.model.resp.process.OrderTradeProcessPaymentDetailGroupResp;
import com.ssy.lingxi.order.model.resp.process.OrderTradeProcessPaymentDetailResp;
import com.ssy.lingxi.order.repository.BaseTradeProcessRepository;
import com.ssy.lingxi.order.repository.PlatformTradeProcessRepository;
import com.ssy.lingxi.order.service.base.IBasePlatformTradeProcessMemberService;
import com.ssy.lingxi.order.service.base.IBaseTradeProcessService;
import com.ssy.lingxi.order.service.platform.IPlatformTradeProcessService;
import com.ssy.lingxi.order.service.web.IOrderTradeProcessService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 平台后台 - 交易规则配置相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-26
 */
@Service
public class PlatformTradeProcessServiceImpl implements IPlatformTradeProcessService {
    @Resource
    private IBaseTradeProcessService baseTradeProcessService;

    @Resource
    private IBasePlatformTradeProcessMemberService basePlatformTradeProcessMemberService;

    @Resource
    private PlatformTradeProcessRepository platformTradeProcessRepository;

    @Resource
    private BaseTradeProcessRepository baseTradeProcessRepository;

    @Resource
    private IOrderTradeProcessService orderTradeProcessService;

    /**
     * 保存基础流程
     *
     * @param engineBO 基础流程
     * @return Void
     */
    @Transactional
    @Override
    public Void saveBaseProcess(ProcessEngineReq engineBO) {

        // 查询平台基础流程
        BaseTradeProcessDO baseProcess = baseTradeProcessRepository.findFirstByProcessKeyAndProcessType(engineBO.getProcessKey(), engineBO.getProcessType()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_PROCESS_INVALID));

        // 基础流程关联规则引擎
        baseProcess.setEngineId(engineBO.getEngineId());
        baseProcess.setProcessName(engineBO.getProcessName());
        baseProcess.setIsDefault(engineBO.getIsDefault());
        baseProcess.setProcessImage(engineBO.getProcessImage());
        baseProcess.setDescription(engineBO.getDescription());
        baseTradeProcessRepository.saveAndFlush(baseProcess);

        // 生成平台默认规则
        this.saveDefaultProcess(baseProcess, engineBO);

        // 生成能力中心默认流程
        orderTradeProcessService.saveDefaultProcess(baseProcess, engineBO);

        return null;
    }

    /**
     * 创建平台默认流程
     * @param baseProcess 基础流程
     */
    private void saveDefaultProcess(BaseTradeProcessDO baseProcess, ProcessEngineReq engineBO){

        // 查询当前类型的默认流程
        PlatformTradeProcessDO defaultProcess = platformTradeProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode());

        // 不存在则创建默认流程
        if (Objects.equals(engineBO.getIsDefault(), ProcessDefaultEnum.YES.getCode()) && Objects.isNull(defaultProcess)){
            createDefault(baseProcess);
            return;
        }

        // 非默认流程不处理
        if (Objects.isNull(defaultProcess)){
            return;
        }

        // 取消默认则删除
        if (Objects.equals(ProcessDefaultEnum.NO.getCode(), engineBO.getIsDefault())){
            platformTradeProcessRepository.delete(defaultProcess);
            return;
        }

        // 更新默认工作流
        defaultProcess.setEngineId(baseProcess.getEngineId());
        defaultProcess.setName(engineBO.getProcessName());
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        defaultProcess.setProcess(baseProcess);
        platformTradeProcessRepository.save(defaultProcess);
    }

    /**
     * 创建平台默认流程
     * @param baseProcess 基础流程
     */
    private void createDefault(BaseTradeProcessDO baseProcess){
        PlatformTradeProcessDO defaultProcess = new PlatformTradeProcessDO();
        defaultProcess.setEngineId(baseProcess.getEngineId());
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        defaultProcess.setProcessType(baseProcess.getProcessType());
        defaultProcess.setName(baseProcess.getProcessName());
        defaultProcess.setMembers(new HashSet<>());
        defaultProcess.setAllMembers(Boolean.TRUE);
        defaultProcess.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        defaultProcess.setCreateTime(LocalDateTime.now());
        defaultProcess.setSource(ProcessSourceEnum.PAAS.getCode());
        defaultProcess.setIsDefault(ProcessDefaultEnum.YES.getCode());
        defaultProcess.setProcess(baseProcess);
        defaultProcess.setSkipFirstStep(baseProcess.getSkipFirstStep());
        defaultProcess.setPayTimes(baseProcess.getPayTimes());
        defaultProcess.setProcessKind(baseProcess.getProcessKind());
        platformTradeProcessRepository.saveAndFlush(defaultProcess);
    }

    /**
     * 删除基础流程
     *
     * @param deleteDTO 基础流程
     * @return Void
     */
    @Override
    public Void deleteBaseProcess(DeleteBaseProcessReq deleteDTO) {

//        // 查询
//        BaseTradeProcessDO process = baseTradeProcessRepository.findFirstByEngineId(deleteDTO.getEngineId()).orElse(null);
//
//        // 执行删除
//        if (Objects.nonNull(process)){
//
//        }

        return null;
    }

    /**
     * 获取会员交易流程
     * @param engineRuleQueryReq 基础流程
     * @return 查询结果
     */
    @Override
    public List<ProcessEngineRuleResp> getMemberProcess(EngineRuleQueryReq engineRuleQueryReq) {

        // 查询默认流程
        List<OrderTradeProcessDO> memberProcessList = orderTradeProcessService.getMemberProcess(engineRuleQueryReq.getMemberId(), engineRuleQueryReq.getMemberRoleId(), engineRuleQueryReq.getType());

        if (memberProcessList.isEmpty()){
            return new ArrayList<>();
        }

        // 组装数据
        return memberProcessList.stream().map(memberProcess -> {
            ProcessEngineRuleResp engineRuleVO = new ProcessEngineRuleResp();
            engineRuleVO.setProcessRuleId(memberProcess.getId());
            engineRuleVO.setIsDefault(memberProcess.getIsDefault());
            engineRuleVO.setProcessKey(memberProcess.getProcessKey());
            return engineRuleVO;
        }).collect(Collectors.toList());
    }

    /**
     * 分页查询交易流程规则配置
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<PlatformProcessPageQueryResp> pageTradeProcess(UserLoginCacheDTO loginUser, PlatformProcessPageDataReq pageVO) {
        // 查询平台默认
        List<Integer> types = platformTradeProcessRepository.findByIsDefaultAndSource(ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.SYSTEM.getCode()).stream().map(PlatformTradeProcessDO::getProcessType).collect(Collectors.toList());

        // 平台未存在的默认流程类型
        List<Integer> typeList = Arrays.stream(OrderTradeProcessTypeEnum.values()).map(OrderTradeProcessTypeEnum::getCode).filter(code -> !types.contains(code)).collect(Collectors.toList());

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("isDefault", "createTime").descending());
        Specification<PlatformTradeProcessDO> specification = (root, query, criteriaBuilder) -> {

            // 平台流程
            List<Predicate> cond = new ArrayList<>();
            cond.add(criteriaBuilder.equal(root.get("source"), ProcessSourceEnum.SYSTEM.getCode()));
            if (StringUtils.hasText(pageVO.getName())) {
                cond.add(criteriaBuilder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }

            if (typeList.isEmpty()){
                return criteriaBuilder.and(cond.toArray(new Predicate[0]));
            }

            // 系统默认
            List<Predicate> orList = new ArrayList<>();
            orList.add(criteriaBuilder.equal(root.get("source"), ProcessSourceEnum.PAAS.getCode()));
            orList.add(criteriaBuilder.in(root.get("processType")).value(typeList));
            if (StringUtils.hasText(pageVO.getName())) {
                orList.add(criteriaBuilder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }

            // 组合查询
            return criteriaBuilder.or( criteriaBuilder.and(cond.toArray(new Predicate[0])), criteriaBuilder.and(orList.toArray(new Predicate[0])));
        };

        Page<PlatformTradeProcessDO> pageList = platformTradeProcessRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(this::createQueryVO).collect(Collectors.toList()));
    }

    /**
     * 创建展示VO
     * @param process 平台交易规则
     * @return 查询结果
     */
    private PlatformProcessPageQueryResp createQueryVO(PlatformTradeProcessDO process){
        PlatformProcessPageQueryResp queryVO = new PlatformProcessPageQueryResp();
        queryVO.setProcessId(process.getId());
        queryVO.setCreateTime(process.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
        if (Objects.equals(ProcessDefaultEnum.YES.getCode(), process.getIsDefault())){
            queryVO.setName(Optional.ofNullable(process.getProcess().getProcessName()).orElse(process.getProcess().getName()));
        }else {
            queryVO.setName(process.getName());
        }
        queryVO.setStatus(process.getStatus());
        queryVO.setStatusName(EnableDisableStatusEnum.getNameByCode(process.getStatus()));
        queryVO.setProcessName(Optional.ofNullable(process.getProcess().getProcessName()).orElse(process.getProcess().getName()));
        queryVO.setIsDefault(process.getIsDefault());
        queryVO.setProcessType(process.getProcessType());
        return queryVO;
    }

    @Override
    public List<PlatformProcessPageQueryResp> listTradeProcess(UserLoginCacheDTO loginUser, ProcessQueryRequest queryRequest) {
        Specification<PlatformTradeProcessDO> spec = (root, query, builder) -> {
            List<Predicate> list = new ArrayList<>();
            if (Objects.nonNull(queryRequest.getProcessType())) {
                list.add(builder.equal(root.get("processType").as(Integer.class), queryRequest.getProcessType()));
            }
            return query.where(list.toArray(new Predicate[0])).getRestriction();
        };
        return platformTradeProcessRepository.findAll(spec, Sort.by("createTime").descending()).stream().map(this::createQueryVO).collect(Collectors.toList());
    }

    @Override
    public Void saveDefault(SaveDefaultRequest defaultRequest) {

        // 查询当前规则
        BaseTradeProcessDO baseProcess = baseTradeProcessRepository.findById(defaultRequest.getProcessId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_PROCESS_INVALID));

        // 查询默认流程
        PlatformTradeProcessDO defaultProcess = platformTradeProcessRepository.findByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.SYSTEM.getCode()).stream().findFirst().orElse(null);

        if (Objects.isNull(defaultProcess)){

            // 系统默认流程
            PlatformTradeProcessDO defaultProcessPaas = Optional.ofNullable(platformTradeProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_DEFAULT_PROCESS_NON_EXISTENT));

            // 保存默认流程
            defaultProcess = BeanUtil.copyProperties(defaultProcessPaas, PlatformTradeProcessDO.class);
            defaultProcess.setId(null);
            defaultProcess.setProcessKey(baseProcess.getProcessKey());
            defaultProcess.setName(Optional.ofNullable(baseProcess.getProcessName()).orElse(baseProcess.getName()));
            defaultProcess.setProcess(baseProcess);
            defaultProcess.setSource(ProcessSourceEnum.SYSTEM.getCode());
            platformTradeProcessRepository.save(defaultProcess);

        } else {

            // 更新默认流程
            defaultProcess.setProcessKey(baseProcess.getProcessKey());
            defaultProcess.setName(Optional.ofNullable(baseProcess.getProcessName()).orElse(baseProcess.getName()));
            defaultProcess.setProcessType(baseProcess.getProcessType());
            defaultProcess.setProcess(baseProcess);
            platformTradeProcessRepository.save(defaultProcess);
        }

        // 设置能力中心默认流程
        orderTradeProcessService.defaultProcess(defaultProcess);

        return null;
    }

    /**
     * 新增交易规则页面 - 查询基础交易流程列表
     *
     * @param loginUser 登录用户
     * @return 查询结果
     */
    @Override
    public List<PlatformBaseTradeProcessResp> listBaseTradeProcess(UserLoginCacheDTO loginUser, ProcessQueryRequest queryRequest) {
        return baseTradeProcessService.listProcessByPlatform(queryRequest);
    }

    /**
     * 新增交易流程规则
     *
     * @param loginUser 登录用户
     * @param processVO 接口参数
     * @return 新增结果
     */
    @Transactional
    @Override
    public Void createTradeProcess(UserLoginCacheDTO loginUser, PlatformProcessReq processVO) {
        //Step 1: 判断基础流程
        BaseTradeProcessDO baseTradeProcess = baseTradeProcessService.findById(processVO.getBaseProcessId());
        if(baseTradeProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BASE_TRADE_PROCESS_DOES_NOT_EXIST);
        }

        //Step 2: 判断是否已经存在
        if(platformTradeProcessRepository.existsByProcessAndIsDefault(baseTradeProcess, ProcessDefaultEnum.NO.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_TRADE_PROCESS_EXISTS);
        }

        //Step 3: 新增
        PlatformTradeProcessDO tradeProcess = new PlatformTradeProcessDO();
        tradeProcess.setCreateTime(LocalDateTime.now());
        tradeProcess.setName(processVO.getName());
        tradeProcess.setProcess(baseTradeProcess);
        tradeProcess.setProcessKey(baseTradeProcess.getProcessKey());
        tradeProcess.setProcessType(baseTradeProcess.getProcessType());
        tradeProcess.setProcessKind(baseTradeProcess.getProcessKind());
        tradeProcess.setSkipFirstStep(baseTradeProcess.getSkipFirstStep());
        tradeProcess.setPayTimes(baseTradeProcess.getPayTimes());
        tradeProcess.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        tradeProcess.setAllMembers(processVO.getAllMembers());
        tradeProcess.setIsDefault(ProcessDefaultEnum.NO.getCode());
        tradeProcess.setSource(ProcessSourceEnum.SYSTEM.getCode());
        //由于关联实体时 CascadeType.DETACH, 所以要先保存一次
        platformTradeProcessRepository.saveAndFlush(tradeProcess);

        // 3-1 : 校验、保存关联的会员
        basePlatformTradeProcessMemberService.checkMembers(tradeProcess, processVO.getAllMembers(), processVO.getMembers());

        platformTradeProcessRepository.saveAndFlush(tradeProcess);
        return null;
    }

    /**
     * 查询交易流程规则详情
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public TradeProcessDetailResp getTradeProcess(UserLoginCacheDTO loginUser, OrderProcessIdReq idVO) {
        PlatformTradeProcessDO tradeProcess = platformTradeProcessRepository.findById(idVO.getProcessId()).orElse(null);
        if(tradeProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_TRADE_PROCESS_DOES_NOT_EXIST);
        }
        TradeProcessDetailResp detailVO = new TradeProcessDetailResp();
        detailVO.setProcessId(tradeProcess.getId());
        detailVO.setName(tradeProcess.getName());
        detailVO.setBaseProcessId(tradeProcess.getProcess().getId());
        detailVO.setAllMembers(tradeProcess.getAllMembers());
        detailVO.setStatus(tradeProcess.getStatus());
        detailVO.setStatusName(EnableDisableStatusEnum.getNameByCode(tradeProcess.getStatus()));
        detailVO.setIsDefault(Optional.ofNullable(tradeProcess.getIsDefault()).orElse(0));
        detailVO.setBaseProcess(createProcessVO(tradeProcess.getProcess()));
        return detailVO;
    }

    /**
     * 创建展示类
     * @param process 基础交易规则
     * @return 转换结果
     */
    private PlatformBaseTradeProcessResp createProcessVO(BaseTradeProcessDO process){
        PlatformBaseTradeProcessResp processVO = new PlatformBaseTradeProcessResp();
        processVO.setBaseProcessid(process.getId());
        processVO.setProcessName(Optional.ofNullable(process.getProcessName()).orElse(BaseTradeProcessEnum.getNameByCode(process.getCode())));
        processVO.setProcessType(process.getProcessType());
        processVO.setProcessTypeName(OrderTradeProcessTypeEnum.getNameByCode(process.getProcessType()));
        processVO.setDescription(Optional.ofNullable(process.getDescription()).orElse(BaseTradeProcessEnum.getRemarkByCode(process.getCode())));
        processVO.setProcessImage(process.getProcessImage());
        return processVO;
    }

    /**
     * 分页查询交易流程规则适用会员列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderMemberQueryDTO> pageTradeProcessMembers(UserLoginCacheDTO loginUser, PlatformProcessMemberPageDataReq pageVO) {
        PlatformTradeProcessDO tradeProcess = platformTradeProcessRepository.findById(pageVO.getProcessId()).orElse(null);
        if(tradeProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_TRADE_PROCESS_DOES_NOT_EXIST);
        }

        return basePlatformTradeProcessMemberService.pageMembers(tradeProcess, pageVO.getName(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 修改交易流程规则
     *
     * @param loginUser 登录用户
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @Transactional
    @Override
    public Void updateTradeProcess(UserLoginCacheDTO loginUser, PlatformProcessUpdateReq updateVO) {
        PlatformTradeProcessDO tradeProcess = platformTradeProcessRepository.findById(updateVO.getProcessId()).orElse(null);
        if(tradeProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_TRADE_PROCESS_DOES_NOT_EXIST);
        }

        //Step 1: 判断基础流程
        BaseTradeProcessDO baseTradeProcess = baseTradeProcessService.findById(updateVO.getBaseProcessId());
        if(baseTradeProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BASE_TRADE_PROCESS_DOES_NOT_EXIST);
        }

        //Step 2: 判断是否已经存在
        if(platformTradeProcessRepository.existsByProcessAndSourceAndIdNot(baseTradeProcess, ProcessSourceEnum.SYSTEM.getCode(), tradeProcess.getId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_TRADE_PROCESS_EXISTS);
        }

        //Step 3: 修改
        tradeProcess.setCreateTime(LocalDateTime.now());
        tradeProcess.setName(updateVO.getName());
        tradeProcess.setProcess(baseTradeProcess);
        tradeProcess.setProcessKey(baseTradeProcess.getProcessKey());
        tradeProcess.setProcessType(baseTradeProcess.getProcessType());
        tradeProcess.setProcessKind(baseTradeProcess.getProcessKind());
        tradeProcess.setSkipFirstStep(baseTradeProcess.getSkipFirstStep());
        tradeProcess.setPayTimes(baseTradeProcess.getPayTimes());
        tradeProcess.setAllMembers(updateVO.getAllMembers());

        // 3-1 : 校验、修改关联的会员
        basePlatformTradeProcessMemberService.updateMembers(tradeProcess, updateVO.getAllMembers(), updateVO.getMembers());

        platformTradeProcessRepository.saveAndFlush(tradeProcess);
        return null;
    }

    /**
     * 修改交易流程规则状态
     *
     * @param loginUser 登录用户
     * @param statusVO 接口参数
     * @return 修改结果
     */
    @Override
    public Void updateTradeProcessStatus(UserLoginCacheDTO loginUser, OrderProcessUpdateStatusReq statusVO) {
        PlatformTradeProcessDO tradeProcess = platformTradeProcessRepository.findById(statusVO.getProcessId()).orElse(null);
        if(tradeProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_TRADE_PROCESS_DOES_NOT_EXIST);
        }

        tradeProcess.setCreateTime(LocalDateTime.now());
        tradeProcess.setStatus(statusVO.getStatus());
        platformTradeProcessRepository.saveAndFlush(tradeProcess);
        return null;
    }

    /**
     * 删除交易流程规则
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 删除结果
     */
    @Override
    public Void deleteTradeProcess(UserLoginCacheDTO loginUser, OrderProcessIdReq idVO) {
        PlatformTradeProcessDO tradeProcess = platformTradeProcessRepository.findById(idVO.getProcessId()).orElse(null);
        if(tradeProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_TRADE_PROCESS_DOES_NOT_EXIST);
        }

        platformTradeProcessRepository.delete(tradeProcess);
        return null;
    }

    /**
     * 系统能力 - 新增交易流程规则配置时，根据平台后台的配置查询基础交易流程列表
     *
     * @param memberId 会员Id
     * @param roleId   会员角色Id
     * @return 查询结果
     */
    @Override
    public List<BaseTradeProcessResp> listOrderTradeProcess(Long memberId, Long roleId, ProcessQueryRequest queryRequest) {
        Specification<PlatformTradeProcessDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> orList = new ArrayList<>();
            Predicate allMembers = criteriaBuilder.isTrue(root.get("allMembers").as(Boolean.class));
            if (Objects.nonNull(queryRequest.getProcessType())) {
                allMembers = criteriaBuilder.and(allMembers, criteriaBuilder.equal(root.get("processType").as(Integer.class), queryRequest.getProcessType()));
            }
            orList.add(allMembers);
            Join<PlatformTradeProcessDO, PlatformTradeProcessMemberDO> memberJoin = root.join("members", JoinType.LEFT);
            Predicate member = criteriaBuilder.and(criteriaBuilder.equal(memberJoin.get("memberId").as(Long.class), memberId), criteriaBuilder.equal(memberJoin.get("roleId").as(Long.class), roleId));
            if (Objects.nonNull(queryRequest.getProcessType())) {
                member = criteriaBuilder.and(member, criteriaBuilder.equal(root.get("processType").as(Integer.class), queryRequest.getProcessType()));
            }
            orList.add(member);
            Predicate[] p = new Predicate[orList.size()];
            return criteriaBuilder.or(orList.toArray(p));
        };
        List<PlatformTradeProcessDO> repeatList = new ArrayList<>();
        List<PlatformTradeProcessDO> list = platformTradeProcessRepository.findAll(specification);
        return list.stream()
            .filter(tradeProcess -> isFilter(tradeProcess, queryRequest) && isNoDefaultRepeat(tradeProcess, list , repeatList))
            .map(PlatformTradeProcessDO::getProcess)
            .sorted(Comparator.comparingLong(BaseTradeProcessDO::getId))
            .map(process -> {
                BaseTradeProcessResp processVO = new BaseTradeProcessResp();
                processVO.setBaseProcessid(process.getId());
                processVO.setProcessName(BaseTradeProcessEnum.getNameByCode(process.getCode()));
                processVO.setProcessType(process.getProcessType());
                processVO.setProcessTypeName(OrderTradeProcessTypeEnum.getNameByCode(process.getProcessType()));
                processVO.setDescription(BaseTradeProcessEnum.getRemarkByCode(process.getCode()));
                processVO.setPayTimes(process.getPayTimes());
                processVO.setPayments(CollectionUtils.isEmpty(process.getPayNodes()) ? new ArrayList<>() : process.getPayNodes().stream().collect(Collectors.groupingBy(PayNodeBO::getSerialNo)).entrySet().stream().map(entry -> {
                OrderTradeProcessPaymentDetailGroupResp groupVO = new OrderTradeProcessPaymentDetailGroupResp();
                groupVO.setSerialNo(entry.getKey());
                groupVO.setNodes(entry.getValue().stream().map(node -> {
                    OrderTradeProcessPaymentDetailResp nodeVO = new OrderTradeProcessPaymentDetailResp();
                    nodeVO.setBatchNo(node.getBatchNo());
                    nodeVO.setPayNode(OrderPayNodeEnum.getNameByCode(node.getPayNode()));
                    nodeVO.setPayRate("");
                    return nodeVO;
                }).sorted(Comparator.comparingInt(OrderTradeProcessPaymentDetailResp::getBatchNo)).collect(Collectors.toList()));
                return groupVO;
            }).sorted(Comparator.comparingInt(OrderTradeProcessPaymentDetailGroupResp::getSerialNo)).collect(Collectors.toList()));
                processVO.setIsDefault(Optional.ofNullable(process.getIsDefault()).orElse(CommonBooleanEnum.NO.getCode()));
                processVO.setProcessImage(process.getProcessImage());
                processVO.setEngineId(process.getEngineId());
            return processVO;
        }).collect(Collectors.toList());
    }

    private boolean isNoDefaultRepeat(PlatformTradeProcessDO tradeProcess, List<PlatformTradeProcessDO> list,  List<PlatformTradeProcessDO> repeatList) {
        if (Objects.equals(tradeProcess.getIsDefault(), CommonBooleanEnum.NO.getCode())) {
            // 当前非默认
            // 查询所有交易规则中，非默认中，除开自己有没有相同的流程类型
            if(list.stream().noneMatch(f -> !Objects.equals(f.getId(), tradeProcess.getId()) // 除开自己
                    && Objects.equals(f.getIsDefault(), CommonBooleanEnum.NO.getCode()) // 非默认
                    && Objects.equals(f.getProcessKey(), tradeProcess.getProcessKey()) // 都不存在相同类型
            )){
                // 不存在，直接返回true
                return true;
            }
            // 存在，判断重复列是否已经添加过滤的数据，没添加到过滤数据集合，返回true
            if (repeatList.stream().map(PlatformTradeProcessDO::getProcessKey).anyMatch(f -> Objects.equals(f, tradeProcess.getProcessKey()))) {
                return false;
            }
            repeatList.add(tradeProcess);
            return true;
        }
       return list.stream().noneMatch(f -> !Objects.equals(tradeProcess.getId(), f.getId()) && Objects.equals(tradeProcess.getProcessKey(), f.getProcessKey()));
    }

    /**
     * 过滤数据
     * @param process       流程
     * @param queryRequest  请求参数
     * @return Boolean
     */
    private Boolean isFilter(PlatformTradeProcessDO process, ProcessQueryRequest queryRequest){

        // 有效的
        Set<Integer> processTypeSet = Stream.of(OrderTradeProcessTypeEnum.AFTER_SALES_EXCHANGES.getCode(), OrderTradeProcessTypeEnum.AFTER_SALES_RETURNS.getCode(), OrderTradeProcessTypeEnum.AFTER_SALES_MAINTENANCE.getCode(), OrderTradeProcessTypeEnum.AFTER_SALES_INTERNAL_EXCHANGES.getCode(), OrderTradeProcessTypeEnum.AFTER_SALES_INTERNAL_RETURNS.getCode(), OrderTradeProcessTypeEnum.AFTER_SALES_MAINTENANCE.getCode()).collect(Collectors.toSet());

        // 订单
        Set<Integer> orderProcessTypeSet = Stream.of(OrderTradeProcessTypeEnum.ORDER_TRADE.getCode(), OrderTradeProcessTypeEnum.ORDER_PURCHASE.getCode(), OrderTradeProcessTypeEnum.RIGHT_POINT.getCode(), OrderTradeProcessTypeEnum.ORDER_COMMERCE_IMPORT.getCode()).collect(Collectors.toSet());

        boolean isFilter = Objects.equals(process.getStatus(), EnableDisableStatusEnum.ENABLE.getCode());
        if (Objects.equals(queryRequest.getType(),1)) {
            isFilter = isFilter && processTypeSet.contains(process.getProcessType());
        }
        if (Objects.equals(queryRequest.getType(),2)) {
            isFilter = isFilter && orderProcessTypeSet.contains(process.getProcessType());
        }
        // 非默认的
        if (Objects.isNull(queryRequest.getProcessType())){
            isFilter = isFilter && !Objects.equals(process.getIsDefault(), ProcessDefaultEnum.YES.getCode());
        }

        // 指定类型
        if (Objects.nonNull(queryRequest.getProcessType())) {
            return isFilter && Objects.equals(process.getProcessType(), queryRequest.getProcessType());
        }

        return isFilter;
    }
}
