package com.ssy.lingxi.order.serviceImpl.base;

import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.engine.EngineResultDTO;
import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.FundModeEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.enums.order.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.EngineRuleUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.contract.api.feign.IContractFeign;
import com.ssy.lingxi.contract.api.model.req.ContractIdReq;
import com.ssy.lingxi.contract.api.model.resp.ContractCodeAndIdResp;
import com.ssy.lingxi.engine.api.enums.ProcessDefaultEnum;
import com.ssy.lingxi.engine.api.enums.ProcessTypeDetailEnum;
import com.ssy.lingxi.engine.api.feign.IProcessEngineRuleFeign;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.OrderFreightTypeEnum;
import com.ssy.lingxi.order.model.bo.*;
import com.ssy.lingxi.order.model.req.basic.OrderSeparateProductReq;
import com.ssy.lingxi.order.model.req.basic.OrderSeparateReq;
import com.ssy.lingxi.order.model.req.basic.OrderSeparateVendorReq;
import com.ssy.lingxi.order.model.req.buyer.BuyerSrmOrderChangeReq;
import com.ssy.lingxi.order.model.req.buyer.RequisitionOrderChangeReq;
import com.ssy.lingxi.order.model.req.common.OrderProductPriceTypeReq;
import com.ssy.lingxi.order.model.resp.basic.OrderPayChannelDetailResp;
import com.ssy.lingxi.order.model.resp.basic.OrderPayTypeDetailResp;
import com.ssy.lingxi.order.model.resp.common.OrderProductProcessQueryResp;
import com.ssy.lingxi.order.repository.OrderTradeProcessRepository;
import com.ssy.lingxi.order.service.base.IBaseOrderProcessService;
import com.ssy.lingxi.order.service.feign.ISettleAccountFeignService;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuStockResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 流程规则配置、会员支付策略、会员支付参数查询接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-30
 */
@Slf4j
@Service
public class BaseOrderProcessServiceImpl implements IBaseOrderProcessService {
    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private ISettleAccountFeignService settleAccountFeignService;

    @Resource
    private IProcessEngineRuleFeign processEngineRuleFeign;

    @Resource
    private ICommodityFeign commodityFeign;

    @Resource
    private IContractFeign contractControllerFeign;

    @Resource
    private OrderTradeProcessRepository orderTradeProcessRepository;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    /**
     * 校验采购订单（多供应商）合并下单条件
     *
     * @param separateVO 接口参数
     */
    @Override
    public void checkOrderMergeCondition(OrderSeparateReq separateVO) {
        //如果供应会员的个数为1，且订单商品数为1，正常下单
        List<VendorBO> vendors = separateVO.getVendors().stream().map(separate -> new VendorBO(separate.getVendorMemberId(), separate.getVendorRoleId())).collect(Collectors.toList());
        if(vendors.size() == 1 && separateVO.getVendors().stream().mapToLong(vendor -> vendor.getProducts().size()).sum() == 1) {
            return;
        }

        //同一个供应商相同skuId不能下单
        for (OrderSeparateVendorReq separateVendor : separateVO.getVendors()) {
            if (separateVendor.getProducts().size() != separateVendor.getProducts().stream().map(OrderSeparateProductReq::getSkuId).collect(Collectors.toSet()).size()) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_SKU_IS_CHECK_SAME);
            }
        }

        //跨境商品与其他商品不能同时下单
        List<Boolean> crossBorders = separateVO.getVendors().stream().flatMap(vendor -> vendor.getProducts().stream()).map(OrderSeparateProductReq::getCrossBorder).distinct().collect(Collectors.toList());
        if(crossBorders.size() > 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 1: 运费方式（如有）必须为“卖家承担”
        if(vendors.size() > 1 && separateVO.getVendors().stream().flatMap(vendor -> vendor.getProducts().stream().map(product -> NumberUtil.isNullOrNegative(product.getFreightType()) ? OrderFreightTypeEnum.VENDOR.getCode() : product.getFreightType())).anyMatch(freightType -> !freightType.equals(OrderFreightTypeEnum.VENDOR.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_FREIGHT_TYPE_MUST_BE_VENDOR);
        }

        //Step 2: 查询资金归集模式
        findPlatformMemberPayment(separateVO.getVendors().stream().map(separate -> new VendorBO(separate.getVendorMemberId(), separate.getVendorRoleId())).collect(Collectors.toList()));

        //Step 3: 查询交易流程规则配置
        List<VendorProductBO> vendorProducts = separateVO.getVendors().stream().map(vendor -> {
            VendorProductBO productBO = new VendorProductBO();
            productBO.setVendorMemberId(vendor.getVendorMemberId());
            productBO.setVendorRoleId(vendor.getVendorRoleId());
            productBO.setProducts(vendor.getProducts().stream().map(p -> new OrderProductBO(p.getProductId(), p.getSkuId())).collect(Collectors.toList()));
            return productBO;
        }).collect(Collectors.toList());
        // 0618修改，通过流程引擎搜索交易规则
        findB2BOrderProcess(separateVO.getShopId(), crossBorders.get(0) ? OrderTradeProcessTypeEnum.ORDER_COMMERCE_IMPORT : OrderTradeProcessTypeEnum.ORDER_TRADE, vendorProducts);
    }

    /**
     * （批量）查询供应商交易流程配置
     *
     * @param shopId               商城Id
     * @param tradeProcessTypeEnum 交易流程类型
     * @param vendorProducts       供应会员及商品信息列表
     * @return 查询结果
     */
    @Override
    public List<TradeProcessDetailBO> findVendorTradeProcessByProducts(Long shopId, OrderTradeProcessTypeEnum tradeProcessTypeEnum, List<VendorProductBO> vendorProducts) {
        QOrderTradeProcessDO qOrderTradeProcess = QOrderTradeProcessDO.orderTradeProcessDO;
        QOrderTradeProcessProductDO qOrderTradeProcessProduct = QOrderTradeProcessProductDO.orderTradeProcessProductDO;
        QOrderTradeProcessPaymentDO qOrderTradeProcessPayment = QOrderTradeProcessPaymentDO.orderTradeProcessPaymentDO;
//        QOrderTradeProcessContractDO qOrderTradeProcessContract = QOrderTradeProcessContractDO.orderTradeProcessContractDO;

        // allProduct = false的情况：
        //    如果productId和skuId不匹配，不会出现 process 的配置
        // allProduct = true的情况
        //    即使多个productId和skuId不匹配，有且仅有一条 process 的配置，此时返回的ProductId和SkuId通过构造函数的Null值处理，变为0

        //Step 1: 拼接会员、商品Id、SkuId查询条件
        Predicate[] predicates = vendorProducts.stream().map(vendor -> {
            Predicate[] productPredicates = vendor.getProducts().stream().map(product -> qOrderTradeProcessProduct.productId.eq(product.getProductId()).and(qOrderTradeProcessProduct.skuId.eq(product.getSkuId()))).toArray(Predicate[]::new);
            return qOrderTradeProcess.memberId.eq(vendor.getVendorMemberId()).and(qOrderTradeProcess.roleId.eq(vendor.getVendorRoleId())).and(qOrderTradeProcess.allProducts.isTrue().or(ExpressionUtils.anyOf(productPredicates)));
        }).toArray(Predicate[]::new);

        return new ArrayList<>(jpaQueryFactory
                .from(qOrderTradeProcess)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .leftJoin(qOrderTradeProcessPayment).on(qOrderTradeProcess.id.eq(qOrderTradeProcessPayment.process.id))
//                .leftJoin(qOrderTradeProcessContract).on(qOrderTradeProcess.id.eq(qOrderTradeProcessContract.process.id))
                .where(qOrderTradeProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode()).and(qOrderTradeProcess.processType.eq(tradeProcessTypeEnum.getCode())).and(qOrderTradeProcess.shopId.eq(shopId)))
                .where(ExpressionUtils.anyOf(predicates))
                .transform(GroupBy.groupBy(qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours).as(
                        Projections.constructor(TradeProcessDetailBO.class, qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.processKind, qOrderTradeProcess.payTimes, qOrderTradeProcess.skipFirstStep, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours,
                                GroupBy.list(Projections.constructor(PayNodeBO.class, qOrderTradeProcessPayment.serialNo, qOrderTradeProcessPayment.batchNo, qOrderTradeProcessPayment.payNode, qOrderTradeProcessPayment.payRate))
                        ))).values());
    }

    /**
     * 查询（供应）会员采购流程配置
     *
     * @param shopId               商城Id
     * @param tradeProcessTypeEnum 交易流程类型
     * @param vendorProducts       供应会员及商品信息列表
     * @return 查询结果
     */
    @Override
    public OrderTradeProcessBO findVendorTradeProcess(Long shopId, OrderTradeProcessTypeEnum tradeProcessTypeEnum, List<VendorProductBO> vendorProducts) {
        List<TradeProcessDetailBO> processDetails = findVendorTradeProcessByProducts(shopId, tradeProcessTypeEnum, vendorProducts);
        //Step 0: 如果查询结果为空，返回错误
        if(CollectionUtils.isEmpty(processDetails)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VENDOR_DOES_NOT_CONFIG_TRADE_PROCESS);
        }

        //Step 1: 判断所有会员、商品是否查询到流程
        for (VendorProductBO vendorProduct : vendorProducts) {
            List<TradeProcessDetailBO> detailList = processDetails.stream().filter(detail -> detail.getVendorMemberId().equals(vendorProduct.getVendorMemberId()) && detail.getVendorRoleId().equals(vendorProduct.getVendorRoleId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(detailList)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_VENDOR_DOES_NOT_CONFIG_TRADE_PROCESS);
            }

            if(vendorProduct.getProducts().stream().anyMatch(product -> detailList.stream().noneMatch(detail -> detail.getAllProducts() || (detail.getProductId().equals(product.getProductId()) && detail.getSkuId().equals(product.getSkuId()))))) {
                throw new BusinessException(ResponseCodeEnum.ORDER_VENDOR_TRADE_PROCESS_DOES_NOT_CONTAIN_PRODUCTS);
            }
        }

        //只有一个供应商，且只有一个商品时，不判断其他条件
        if(vendorProducts.size() == 1 && vendorProducts.stream().mapToLong(p -> p.getProducts().size()).sum() == 1) {
            return new OrderTradeProcessBO(processDetails);
        }

        //Step 2: 判断所有流程是否 一致
        if(processDetails.stream().map(TradeProcessDetailBO::getProcessKey).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 3: 判断合同是否不一致（无合同时，合同Id等于0）
        if(processDetails.stream().map(TradeProcessDetailBO::getContractTemplateId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 4: 判断订单取消时间是否一致
        if(processDetails.stream().map(TradeProcessDetailBO::getExpireHours).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 5: 判断支付次数是否一致
        if(processDetails.stream().map(TradeProcessDetailBO::getPayTimes).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 6: 判断支付环节名称、比例是否一致
        if(processDetails.stream().map(detail -> detail.getPayNodes().stream().sorted(Comparator.comparingInt(PayNodeBO::getBatchNo)).map(p -> OrderPayNodeEnum.getNameByCode(p.getPayNode()).concat(NumberUtil.formatPayRate(p.getPayRate()))).collect(Collectors.joining(""))).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        return new OrderTradeProcessBO(processDetails);
    }

    @Override
    public OrderTradeProcessBO findB2BOrderProcess(Long shopId, OrderTradeProcessTypeEnum processTypeEnum, List<VendorProductBO> vendorProducts) {
        List<Long> skuIds = vendorProducts.stream().flatMap(vendorProduct -> vendorProduct.getProducts().stream().map(OrderProductBO::getSkuId)).distinct().collect(Collectors.toList());
        WrapperResp<List<CommoditySkuStockResp>> commodityByCommoditySkuIdList = commodityFeign.getCommodityByCommoditySkuIdList(skuIds);
        WrapperUtil.throwWhenFail(commodityByCommoditySkuIdList);

        if(CollectionUtils.isEmpty(commodityByCommoditySkuIdList.getData())){
            throw new BusinessException(ResponseCodeEnum.ORDER_SEPARATED_PRODUCT_DOES_NOT_EXIST);
        }

        Map<Long, CommoditySkuStockResp> commoditySkuDetailRespMap = commodityByCommoditySkuIdList.getData().stream().collect(Collectors.toMap(CommoditySkuStockResp::getId, Function.identity(), (v1, v2) -> v2));
        Map<VendorProductBO, List<OrderProductBO>> vendorProductMap = vendorProducts.stream().collect(Collectors.toMap(v -> v, VendorProductBO::getProducts));
        List<OrderDO> orders = vendorProductMap.entrySet().stream().map(entry -> {
            OrderDO order = new OrderDO();
            order.setVendorMemberId(entry.getKey().getVendorMemberId());
            order.setVendorRoleId(entry.getKey().getVendorRoleId());
            order.setShopId(shopId);
            Set<OrderProductDO> products = entry.getValue().stream().map(p -> {
                OrderProductDO orderProductDO = new OrderProductDO();
                CommoditySkuStockResp commoditySkuStockResp = commoditySkuDetailRespMap.get(p.getSkuId());
                if(Objects.isNull(commoditySkuStockResp)){
                    throw new BusinessException(ResponseCodeEnum.ORDER_SEPARATED_PRODUCT_DOES_NOT_EXIST);
                }
                orderProductDO.setName(commoditySkuStockResp.getName());
                orderProductDO.setCategory(commoditySkuStockResp.getCustomerCategoryFullName());
                return orderProductDO;
            }).collect(Collectors.toSet());
            order.setProducts(products);
            return order;
        }).collect(Collectors.toList());
        return findB2BOrderProcess(orders,processTypeEnum);
    }

    /**
     * 查询（供应）会员交易流程配置
     *
     * @param vendorMemberId       供应会员Id
     * @param vendorRoleId         供应会员角色Id
     * @param shopId               商城Id
     * @param tradeProcessTypeEnum 交易流程类型
     * @param products             采购商品列表
     * @return 查询结果
     */
    @Override
    public OrderTradeProcessBO findVendorTradeProcess(Long vendorMemberId, Long vendorRoleId, Long shopId, OrderTradeProcessTypeEnum tradeProcessTypeEnum, List<OrderProductBO> products) {
        VendorProductBO vendorProduct = new VendorProductBO();
        vendorProduct.setVendorMemberId(vendorMemberId);
        vendorProduct.setVendorRoleId(vendorRoleId);
        vendorProduct.setProducts(products);
        // 0618修改，通过流程引擎搜索交易规则
        //return findVendorTradeProcess(shopId, tradeProcessTypeEnum, Collections.singletonList(vendorProduct));
        return findB2BOrderProcess(shopId, tradeProcessTypeEnum, Collections.singletonList(vendorProduct));
    }

    /**
     * 查询采购订单（B2B）流程配置 -0618
     * @param orders          订单集合
     * @param tradeProcessTypeEnum 交易流程类型
     * @return 查询结果
     */
    @Override
    public OrderTradeProcessBO findB2BOrderProcess(List<OrderDO> orders, OrderTradeProcessTypeEnum tradeProcessTypeEnum) {

        Integer processType;
        switch (tradeProcessTypeEnum) {
            case ORDER_TRADE: // 订单交易流程
                processType = ProcessTypeDetailEnum.TRANSACTION_ORDER.getCode();
                break;
            case RIGHT_POINT: // 积分订单流程
                processType = ProcessTypeDetailEnum.TRANSACTION_POINTS_ORDER.getCode();
                break;
            case ORDER_COMMERCE_IMPORT: // 跨境电商进口订单流程
                processType = ProcessTypeDetailEnum.TRANSACTION_IMPORT_ORDER.getCode();
                break;
            default:
                // 非以上流程则未配置采购交易流程，无法提交订单
                throw new BusinessException(ResponseCodeEnum.ORDER_BASE_TRADE_PROCESS_DOES_NOT_EXIST);
        }
        List<EngineResultDTO> engineResultDTOS = new ArrayList<>();
        orders.forEach(order -> {
            List<EngineResultDTO> engineResultDTOList = getEngineResult(order, order.getVendorMemberId(), order.getVendorRoleId(), processType);
            engineResultDTOS.addAll(engineResultDTOList);
        });
        if(engineResultDTOS.isEmpty()){
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        //Step 3: 获取所有流程
        List<Long> processRuleIds = engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).collect(Collectors.toList());
        QOrderTradeProcessDO qOrderTradeProcess = QOrderTradeProcessDO.orderTradeProcessDO;
        QOrderTradeProcessProductDO qOrderTradeProcessProduct = QOrderTradeProcessProductDO.orderTradeProcessProductDO;
        QOrderTradeProcessPaymentDO qOrderTradeProcessPayment = QOrderTradeProcessPaymentDO.orderTradeProcessPaymentDO;
//        QOrderTradeProcessContractDO qOrderTradeProcessContract = QOrderTradeProcessContractDO.orderTradeProcessContractDO;

        List<TradeProcessDetailBO> processDetails = new ArrayList<>(jpaQueryFactory
                .from(qOrderTradeProcess)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .leftJoin(qOrderTradeProcessPayment).on(qOrderTradeProcess.id.eq(qOrderTradeProcessPayment.process.id))
//                .leftJoin(qOrderTradeProcessContract).on(qOrderTradeProcess.id.eq(qOrderTradeProcessContract.process.id))
                .where(qOrderTradeProcess.id.in(processRuleIds))
                .transform(GroupBy.groupBy(qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours).as(
                        Projections.constructor(TradeProcessDetailBO.class, qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.processKind, qOrderTradeProcess.payTimes, qOrderTradeProcess.skipFirstStep, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours,
                                GroupBy.list(Projections.constructor(PayNodeBO.class, qOrderTradeProcessPayment.serialNo, qOrderTradeProcessPayment.batchNo, qOrderTradeProcessPayment.payNode, qOrderTradeProcessPayment.payRate))
                        ))).values());

        if(CollectionUtils.isEmpty(processDetails)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VENDOR_DOES_NOT_CONFIG_TRADE_PROCESS);
        }

        //只有一个订单，且只有一个商品时，不判断其他条件
        if(orders.size() == 1 && orders.stream().mapToLong(p -> p.getProducts().size()).sum() == 1) {
            return new OrderTradeProcessBO(processDetails);
        }

        //Step 4: 判断所有流程是否一致
        if(engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 5: 判断合同是否不一致（无合同时，合同Id等于0）
        if(processDetails.stream().map(TradeProcessDetailBO::getContractTemplateId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 6: 判断订单取消时间是否一致
        if(processDetails.stream().map(TradeProcessDetailBO::getExpireHours).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 7: 判断支付次数是否一致
        if(processDetails.stream().map(TradeProcessDetailBO::getPayTimes).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 8: 判断支付环节名称、比例是否一致
        if(processDetails.stream().map(detail -> detail.getPayNodes().stream().sorted(Comparator.comparingInt(PayNodeBO::getBatchNo)).map(p -> OrderPayNodeEnum.getNameByCode(p.getPayNode()).concat(NumberUtil.formatPayRate(p.getPayRate()))).collect(Collectors.joining(""))).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        return new OrderTradeProcessBO(processDetails);
    }

    /**
     * 查询（B2B）会员交易流程配置 -0618
     * @return 查询结果
     */
    @Override
    public OrderTradeProcessBO findB2BOrderProcessPurchase() {

        //Step 3: 获取所有流程
        QOrderTradeProcessDO qOrderTradeProcess = QOrderTradeProcessDO.orderTradeProcessDO;
        QOrderTradeProcessProductDO qOrderTradeProcessProduct = QOrderTradeProcessProductDO.orderTradeProcessProductDO;
        QOrderTradeProcessPaymentDO qOrderTradeProcessPayment = QOrderTradeProcessPaymentDO.orderTradeProcessPaymentDO;
//        QOrderTradeProcessContractDO qOrderTradeProcessContract = QOrderTradeProcessContractDO.orderTradeProcessContractDO;

        List<TradeProcessDetailBO> processDetails = new ArrayList<>(jpaQueryFactory
                .from(qOrderTradeProcess)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .leftJoin(qOrderTradeProcessPayment).on(qOrderTradeProcess.id.eq(qOrderTradeProcessPayment.process.id))
//                .leftJoin(qOrderTradeProcessContract).on(qOrderTradeProcess.id.eq(qOrderTradeProcessContract.process.id))
                .where(qOrderTradeProcess.processType.eq(OrderTradeProcessTypeEnum.ORDER_PURCHASE.getCode())
                        .and(qOrderTradeProcess.memberId.eq(baiTaiMemberProperties.getSelfMemberId())))
                .transform(GroupBy.groupBy(qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours).as(
                        Projections.constructor(TradeProcessDetailBO.class, qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.processKind, qOrderTradeProcess.payTimes, qOrderTradeProcess.skipFirstStep, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours,
                                GroupBy.list(Projections.constructor(PayNodeBO.class, qOrderTradeProcessPayment.serialNo, qOrderTradeProcessPayment.batchNo, qOrderTradeProcessPayment.payNode, qOrderTradeProcessPayment.payRate))
                        ))).values());

        if(CollectionUtils.isEmpty(processDetails)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VENDOR_DOES_NOT_CONFIG_TRADE_PROCESS);
        }

        //Step 5: 判断合同是否不一致（无合同时，合同Id等于0）
        if(processDetails.stream().map(TradeProcessDetailBO::getContractTemplateId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 6: 判断订单取消时间是否一致
        if(processDetails.stream().map(TradeProcessDetailBO::getExpireHours).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 7: 判断支付次数是否一致
        if(processDetails.stream().map(TradeProcessDetailBO::getPayTimes).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 8: 判断支付环节名称、比例是否一致
        if(processDetails.stream().map(detail -> detail.getPayNodes().stream().sorted(Comparator.comparingInt(PayNodeBO::getBatchNo)).map(p -> OrderPayNodeEnum.getNameByCode(p.getPayNode()).concat(NumberUtil.formatPayRate(p.getPayRate()))).collect(Collectors.joining(""))).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        return new OrderTradeProcessBO(processDetails);
    }

    /**
     * 转单 检查并返回查询会员交易流程配置
     * @param vendorProducts
     * @return 查询结果
     */
    @Override
    public Map<Long, OrderTradeProcessBO> findB2BOrderProcessByVendorProducts(List<OrderShopProductBO> vendorProducts) {
        List<OrderDO> orders = vendorProducts.stream().map(orderVO -> {
            OrderDO order = new OrderDO();
            order.setId(orderVO.getOrderId());
            order.setBuyerMemberId(orderVO.getBuyerMemberId());
            order.setBuyerRoleId(orderVO.getBuyerRoleId());
            order.setBuyerMemberName(orderVO.getBuyerMemberName());
            order.setVendorMemberId(orderVO.getVendorMemberId());
            order.setVendorRoleId(orderVO.getVendorRoleId());
            order.setShopId(orderVO.getShopId());
            Set<OrderProductDO> products = orderVO.getProducts().stream().map(p -> {
                OrderProductDO orderProductDO = new OrderProductDO();
                orderProductDO.setName(p.getName());
                orderProductDO.setCategory(p.getCategory());
                return orderProductDO;
            }).collect(Collectors.toSet());
            order.setProducts(products);
            return order;
        }).collect(Collectors.toList());
        Map<Long, OrderTradeProcessBO> processMap = new HashMap<>();
        for (OrderDO order : orders) {
            OrderTradeProcessBO tradeProcessWrapperResp = findB2BOrderProcessPurchase();
            processMap.put(order.getId(), tradeProcessWrapperResp);
        }
        return processMap;
    }

    @Override
    public Map<Long, OrderTradeProcessBO> findB2BOrderProcess(List<OrderShopProductBO> vendorProducts) {
        List<OrderDO> orders = vendorProducts.stream().map(orderVO -> {
            OrderDO order = new OrderDO();
            order.setId(orderVO.getOrderId());
            order.setBuyerMemberId(orderVO.getBuyerMemberId());
            order.setBuyerRoleId(orderVO.getBuyerRoleId());
            order.setBuyerMemberName(orderVO.getBuyerMemberName());
            order.setVendorMemberId(orderVO.getVendorMemberId());
            order.setVendorRoleId(orderVO.getVendorRoleId());
            order.setShopId(orderVO.getShopId());
            Set<OrderProductDO> products = orderVO.getProducts().stream().map(p -> {
                OrderProductDO orderProductDO = new OrderProductDO();
                orderProductDO.setName(p.getName());
                orderProductDO.setCategory(p.getCategory());
                return orderProductDO;
            }).collect(Collectors.toSet());
            order.setProducts(products);
            return order;
        }).collect(Collectors.toList());
        Map<Long, OrderTradeProcessBO> processMap = new HashMap<>();
        for (OrderDO order : orders) {
            OrderTradeProcessBO tradeProcessWrapperResp = findB2BOrderProcess(Stream.of(order).collect(Collectors.toList()), OrderTradeProcessTypeEnum.ORDER_TRADE);
            processMap.put(order.getId(), tradeProcessWrapperResp);
        }
        return processMap;
    }

    /**
     * (转单）批量查询会员交易流程配置
     *
     * @param vendorProducts 订单Id、商城Id及供应会员、供应会员商品列表
     * @return 查询结果
     */
    @Override
    public Map<Long, OrderTradeProcessBO> findVendorTradeProcess(List<OrderShopProductBO> vendorProducts) {
        QOrderTradeProcessDO qOrderTradeProcess = QOrderTradeProcessDO.orderTradeProcessDO;
        QOrderTradeProcessProductDO qOrderTradeProcessProduct = QOrderTradeProcessProductDO.orderTradeProcessProductDO;
        QOrderTradeProcessPaymentDO qOrderTradeProcessPayment = QOrderTradeProcessPaymentDO.orderTradeProcessPaymentDO;
//        QOrderTradeProcessContractDO qOrderTradeProcessContract = QOrderTradeProcessContractDO.orderTradeProcessContractDO;

        // allProduct = false的情况：
        //    如果productId和skuId不匹配，不会出现 process 的配置
        // allProduct = true的情况
        //    即使多个productId和skuId不匹配，有且仅有一条 process 的配置，此时返回的ProductId和SkuId通过构造函数的Null值处理，变为0

        //Step 1: 拼接会员、商品Id、SkuId查询条件
        Predicate[] predicates = vendorProducts.stream().map(vendor -> {
            Predicate[] productPredicates = vendor.getProducts().stream().map(product -> qOrderTradeProcessProduct.productId.eq(product.getProductId()).and(qOrderTradeProcessProduct.skuId.eq(product.getSkuId()))).toArray(Predicate[]::new);
            return qOrderTradeProcess.memberId.eq(vendor.getVendorMemberId()).and(qOrderTradeProcess.roleId.eq(vendor.getVendorRoleId())).and(qOrderTradeProcess.shopId.eq(vendor.getShopId())).and(qOrderTradeProcess.allProducts.isTrue().or(ExpressionUtils.anyOf(productPredicates)));
        }).toArray(Predicate[]::new);

        List<TradeProcessDetailBO> processDetailList = new ArrayList<>(jpaQueryFactory
                .from(qOrderTradeProcess)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .leftJoin(qOrderTradeProcessPayment).on(qOrderTradeProcess.id.eq(qOrderTradeProcessPayment.process.id))
//                .leftJoin(qOrderTradeProcessContract).on(qOrderTradeProcess.id.eq(qOrderTradeProcessContract.process.id))
                .where(qOrderTradeProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode()).and(qOrderTradeProcess.processType.eq(OrderTradeProcessTypeEnum.ORDER_TRADE.getCode())))
                .where(ExpressionUtils.anyOf(predicates))
                .transform(GroupBy.groupBy(qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours).as(
                        Projections.constructor(TradeProcessDetailBO.class, qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.processKind, qOrderTradeProcess.payTimes, qOrderTradeProcess.skipFirstStep, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours,
                                GroupBy.list(Projections.constructor(PayNodeBO.class, qOrderTradeProcessPayment.serialNo, qOrderTradeProcessPayment.batchNo, qOrderTradeProcessPayment.payNode, qOrderTradeProcessPayment.payRate))
                        ))).values());

        //Step 1: 判断所有会员、商品的交易流程
        Map<Long, OrderTradeProcessBO> processMap = new HashMap<>();
        for (OrderShopProductBO vendorProduct : vendorProducts) {
            List<TradeProcessDetailBO> processDetails = processDetailList.stream().filter(detail -> detail.getVendorMemberId().equals(vendorProduct.getVendorMemberId()) && detail.getVendorRoleId().equals(vendorProduct.getVendorRoleId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(processDetails)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_MEMBER_DOES_NOT_CONFIG_TRADE_PROCESS);
            }

            if(vendorProduct.getProducts().stream().anyMatch(product -> processDetails.stream().noneMatch(detail -> detail.getAllProducts() || (detail.getProductId().equals(product.getProductId()) && detail.getSkuId().equals(product.getSkuId()))))) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_MEMBER_DOES_NOT_CONFIG_TRADE_PROCESS);
            }

            if(vendorProduct.getProducts().size() == 1) {
                processMap.put(vendorProduct.getOrderId(), new OrderTradeProcessBO(processDetails));
                continue;
            }

            //Step 2: 判断所有流程是否一致
            if(processDetails.stream().map(TradeProcessDetailBO::getProcessKey).distinct().count() != 1) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_MEMBER_TRADE_PROCESSES_ARE_DIFFERENT);
            }

            //Step 3: 判断合同是否不一致（无合同时，合同Id等于0）
            if(processDetails.stream().map(TradeProcessDetailBO::getContractTemplateId).distinct().count() != 1) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_MEMBER_TRADE_PROCESSES_ARE_DIFFERENT);
            }

            //Step 5: 判断订单取消时间是否一致
            if(processDetails.stream().map(TradeProcessDetailBO::getExpireHours).distinct().count() != 1) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_MEMBER_TRADE_PROCESSES_ARE_DIFFERENT);
            }

            //Step 5: 判断支付次数是否一致
            if(processDetails.stream().map(TradeProcessDetailBO::getPayTimes).distinct().count() != 1) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_MEMBER_TRADE_PROCESSES_ARE_DIFFERENT);
            }

            //Step 6: 判断支付环节名称、比例是否一致
            if(processDetails.stream().map(detail -> detail.getPayNodes().stream().sorted(Comparator.comparingInt(PayNodeBO::getBatchNo)).map(p -> OrderPayNodeEnum.getNameByCode(p.getPayNode()).concat(NumberUtil.formatPayRate(p.getPayRate()))).collect(Collectors.joining(""))).distinct().count() != 1) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_MEMBER_TRADE_PROCESSES_ARE_DIFFERENT);
            }

            processMap.put(vendorProduct.getOrderId(), new OrderTradeProcessBO(processDetails));
        }

        return processMap;
    }

    /**
     * 查询（采购）会员采购流程配置
     *
     * @param buyerMemberId           采购会员Id
     * @param buyerRoleId             采购会员角色Id
     * @param purchaseProcessTypeEnum 采购流程类型
     * @param contractIds             合同Id列表
     * @return 查询结果
     */
    @Override
    public OrderPurchaseProcessBO findBuyerPurchaseProcess(Long buyerMemberId, Long buyerRoleId, OrderPurchaseProcessTypeEnum purchaseProcessTypeEnum, List<Long> contractIds) {
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;

        OrderPurchaseProcessBO processBO = jpaQueryFactory.select(Projections.constructor(OrderPurchaseProcessBO.class, qOrderPurchaseProcess.processKey, qOrderPurchaseProcess.skipFirstStep, qOrderPurchaseProcess.processKind,qOrderPurchaseProcess.id))
                .from(qOrderPurchaseProcess)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrderPurchaseProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode()).and(qOrderPurchaseProcess.memberId.eq(buyerMemberId)).and(qOrderPurchaseProcess.roleId.eq(buyerRoleId)).and(qOrderPurchaseProcess.processType.eq(purchaseProcessTypeEnum.getCode())))
                .where(qOrderPurchaseProcess.allContracts.isTrue().or(qOrderPurchaseProcessContract.contractId.in(contractIds)))
                .fetchFirst();

        if(processBO == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        return processBO;
    }

    @Override
    public OrderPurchaseProcessBO findSRMOrderProcess(Long buyerMemberId, Long buyerRoleId, OrderPurchaseProcessTypeEnum purchaseProcessTypeEnum, List<Long> contractIds) {
        Integer processType;
        switch (purchaseProcessTypeEnum) {
            case ORDER_TRADE: // 合同下单流程
                processType = ProcessTypeDetailEnum.TRANSACTION_CONTRACT_ORDER.getCode();
                break;
            case REQUISITION: // 请购单下单流程
                processType = ProcessTypeDetailEnum.PURCHASE_REQUEST_ORDER.getCode();
                break;
//            case REQUISITION_CONTRACT: // 暂无请购单合同订单流程
//                break;
            default:
                // 非以上流程则未配置采购交易流程，无法提交订单
                throw new BusinessException(ResponseCodeEnum.ORDER_BASE_TRADE_PROCESS_DOES_NOT_EXIST);
        }

        EngineRuleQueryReq query = new EngineRuleQueryReq(processType, buyerMemberId, buyerRoleId);
        List<EngineResultDTO> engineResultDTOS = new ArrayList<>();
        WrapperResp<List<ProcessEngineRuleResp>> wrapperResp = processEngineRuleFeign.getEngineRuleList(query);
        WrapperUtil.throwWhenFail(wrapperResp);

        ContractIdReq contractIdReq = new ContractIdReq();
        contractIdReq.setContractIds(new HashSet<>(contractIds));
        WrapperResp<List<ContractCodeAndIdResp>> listWrapperResp = contractControllerFeign.listContractCodeAndIdVOS(contractIdReq);
        WrapperUtil.throwWhenFail(listWrapperResp);

        List<ContractCodeAndIdResp> contracts = listWrapperResp.getData();
        if(CollectionUtils.isEmpty(contracts)){
            throw new BusinessException(ResponseCodeEnum.CONTRACT_NOT_EXIST);
        }
        Map<Long, ContractCodeAndIdResp> ContractCodeMap = contracts.stream().collect(Collectors.toMap(ContractCodeAndIdResp::getContractId, ContractCodeAndIdVO -> ContractCodeAndIdVO, (v1, v2) -> v2));
        OrderDO order = new OrderDO();
        order.setBuyerRoleId(buyerRoleId);
        order.setBuyerMemberId(buyerMemberId);
        for (Long contractId : contractIds) {
            ContractCodeAndIdResp contract = ContractCodeMap.get(contractId);
            if(Objects.isNull(contract)){
                throw new BusinessException(ResponseCodeEnum.CONTRACT_NOT_EXIST);
            }
//            OrderContractDO orderContractDO = new OrderContractDO();
//            orderContractDO.setContractNo(contract.getContractNo());
//            orderContractDO.setContractId(contractId);
//            order.setContract(orderContractDO);
            EngineResultDTO engineResultDTO = new EngineRuleUtil<>(order, wrapperResp.getData()).meta();
            if (engineResultDTO == null || engineResultDTO.getProcessKey() == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_PURCHASE_PROCESS_DOES_NOT_CONTAIN_CONTRACTS);
            }
            engineResultDTOS.add(engineResultDTO);
        }
        if (engineResultDTOS.isEmpty()) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        //判断所有流程是否一致
        if(engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        List<Long> processRuleIds = engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).collect(Collectors.toList());
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;

        List<OrderPurchaseProcessBO> processDetails = jpaQueryFactory.select(Projections.constructor(OrderPurchaseProcessBO.class, qOrderPurchaseProcess.processKey, qOrderPurchaseProcess.skipFirstStep, qOrderPurchaseProcess.processKind,qOrderPurchaseProcess.id))
                .from(qOrderPurchaseProcess)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrderPurchaseProcess.id.in(processRuleIds))
                .fetch();

        if(CollectionUtils.isEmpty(processDetails)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VENDOR_DOES_NOT_CONFIG_TRADE_PROCESS);
        }
        return new OrderPurchaseProcessBO(processDetails);
    }

    /**
     * 查询采购订单（SRM）流程配置
     * @param order 订单
     * @param memberId 采购商会员id
     * @param memberRoleId 采购商会员角色id
     */
    @Override
    public OrderPurchaseProcessBO findSRMOrderProcess(OrderDO order, Long memberId, Long memberRoleId, OrderPurchaseProcessTypeEnum purchaseProcessTypeEnum) {

        Integer processType;
        switch (purchaseProcessTypeEnum) {
            case ORDER_TRADE: // 合同下单流程
                processType = ProcessTypeDetailEnum.TRANSACTION_CONTRACT_ORDER.getCode();
                break;
            case REQUISITION: // 请购单下单流程
                processType = ProcessTypeDetailEnum.PURCHASE_REQUEST_ORDER.getCode();
                break;
//            case REQUISITION_CONTRACT: // 暂无请购单合同订单流程
//                break;
            default:
                // 非以上流程则未配置采购交易流程，无法提交订单
                throw new BusinessException(ResponseCodeEnum.ORDER_BASE_TRADE_PROCESS_DOES_NOT_EXIST);
        }

        //Step 1: 新版匹配流程配置只支持单个商品匹配，所以将订单商品分开多次请求获取流程接口
        List<EngineResultDTO> engineResultDTOS = getEngineResult(order, memberId, memberRoleId,processType);

        //Step 2: 如果查询结果为空，返回错误
        if(CollectionUtils.isEmpty(engineResultDTOS)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        //Step 3: 判断所有流程是否一致
        if(engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 4: 获取通过流程id拿到采购流程查询结果
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;
        OrderPurchaseProcessBO processBO = jpaQueryFactory.select(Projections.constructor(OrderPurchaseProcessBO.class, qOrderPurchaseProcess.processKey, qOrderPurchaseProcess.skipFirstStep, qOrderPurchaseProcess.processKind,qOrderPurchaseProcess.id))
                .from(qOrderPurchaseProcess)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrderPurchaseProcess.id.eq(engineResultDTOS.get(0).getProcessRuleId()))
                .fetchFirst();

        if(processBO == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        return processBO;
    }

    @Override
    public OrderPurchaseProcessBO checkSrmOrderProcessByChangeOrder(BuyerSrmOrderChangeReq orderVO, OrderPurchaseProcessTypeEnum purchaseProcessTypeEnum, OrderDO orderDO) {
        //718之前订单并未保存流程规则id，所以无法对旧订单进行变更处理
        OrderProcessTaskDO task = orderDO.getTask();
        if(task.getProcessId() == null){
            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_CAN_NOT_TRANSFER);
        }
        Integer processType;
        switch (purchaseProcessTypeEnum) {
            case ORDER_TRADE: // 合同下单流程
                processType = ProcessTypeDetailEnum.TRANSACTION_CONTRACT_ORDER.getCode();
                break;
            case REQUISITION: // 请购单下单流程
                processType = ProcessTypeDetailEnum.PURCHASE_REQUEST_ORDER.getCode();
                break;
            default:
                // 非以上流程则未配置采购交易流程，无法提交订单
                throw new BusinessException(ResponseCodeEnum.ORDER_BASE_TRADE_PROCESS_DOES_NOT_EXIST);
        }
        OrderDO order = new OrderDO();
        order.setVendorRoleId(orderDO.getVendorRoleId());
        order.setVendorMemberId(orderDO.getVendorMemberId());
        order.setVendorMemberName(orderDO.getVendorMemberName());
        order.setBuyerMemberId(orderDO.getBuyerMemberId());
        order.setBuyerRoleId(orderDO.getBuyerRoleId());
//        SrmOrderContractChangeReq contract = orderVO.getContract();
//        if(!Objects.isNull(contract)){
//            OrderContractDO orderContractDO = new OrderContractDO();
//            orderContractDO.setContractId(contract.getContractId());
//            orderContractDO.setContractNo(contract.getContractNo());
//            order.setContract(orderContractDO);
//        }
        order.setProducts(orderVO.getProducts().stream().map(p -> {
            OrderProductDO orderProduct = new OrderProductDO();
            orderProduct.setName(p.getName());
            orderProduct.setCategory(p.getCategory());
            return orderProduct;
        }).collect(Collectors.toSet()));

        //Step 1: 新版匹配流程配置只支持单个商品匹配，所以将订单商品分开多次请求获取流程接口
        List<EngineResultDTO> engineResultDTOS = getEngineResult(order, orderDO.getBuyerMemberId(), orderDO.getBuyerRoleId(),processType);

        //Step 2: 如果查询结果为空，返回错误
        if(CollectionUtils.isEmpty(engineResultDTOS)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        //Step 3: 判断所有流程是否一致
        if(engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 4: 获取通过流程id拿到采购流程查询结果
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;
        OrderPurchaseProcessBO processBO = jpaQueryFactory.select(Projections.constructor(OrderPurchaseProcessBO.class, qOrderPurchaseProcess.processKey, qOrderPurchaseProcess.skipFirstStep, qOrderPurchaseProcess.processKind,qOrderPurchaseProcess.id))
                .from(qOrderPurchaseProcess)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrderPurchaseProcess.id.eq(engineResultDTOS.get(0).getProcessRuleId()))
                .fetchFirst();

        if(processBO == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        if(!task.getProcessId().equals(processBO.getProcessId())){
            throw new BusinessException(ResponseCodeEnum.ORDER_PROCESS_INCONFORMITY_ERROR);
        }

        return processBO;
    }

    /**
     * 查询采购订单（SRM）修改流程配置-0618
     * @param orderVO 订单
     */
    @Override
    public OrderPurchaseProcessBO findSrmOrderChangeProcess(BuyerSrmOrderChangeReq orderVO, OrderDO orderDO) {
        OrderDO order = new OrderDO();
        order.setVendorRoleId(orderDO.getVendorRoleId());
        order.setVendorMemberId(orderDO.getVendorMemberId());
        order.setVendorMemberName(orderDO.getVendorMemberName());
        order.setBuyerMemberId(orderDO.getBuyerMemberId());
        order.setBuyerRoleId(orderDO.getBuyerRoleId());
//        SrmOrderContractChangeReq contract = orderVO.getContract();
//        if(!Objects.isNull(contract)){
//            OrderContractDO orderContractDO = new OrderContractDO();
//            orderContractDO.setContractId(contract.getContractId());
//            orderContractDO.setContractNo(contract.getContractNo());
//            order.setContract(orderContractDO);
//        }
        order.setProducts(orderVO.getProducts().stream().map(p -> {
            OrderProductDO orderProduct = new OrderProductDO();
            orderProduct.setName(p.getName());
            orderProduct.setCategory(p.getCategory());
            return orderProduct;
        }).collect(Collectors.toSet()));
        //Step 1: 新版匹配流程配置只支持单个商品匹配，所以将订单商品分开多次请求获取流程接口
        List<EngineResultDTO> engineResultDTOS = getEngineResult(order, orderDO.getBuyerMemberId(), orderDO.getBuyerRoleId(),ProcessTypeDetailEnum.ORDER_UPDATE_VALIDATE_SRM.getCode());

        //Step 2: 如果查询结果为空，返回错误
        if(CollectionUtils.isEmpty(engineResultDTOS)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        //Step 3: 判断所有流程是否一致
        if(engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_PROCESS_IS_DIFFERENT);
        }
        //Step 4: 获取通过流程id拿到采购流程查询结果
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;
        OrderPurchaseProcessBO processBO = jpaQueryFactory.select(Projections.constructor(OrderPurchaseProcessBO.class, qOrderPurchaseProcess.processKey, qOrderPurchaseProcess.skipFirstStep, qOrderPurchaseProcess.processKind,qOrderPurchaseProcess.id))
                .from(qOrderPurchaseProcess)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrderPurchaseProcess.id.eq(engineResultDTOS.get(0).getProcessRuleId()))
                .fetchFirst();

        if(processBO == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        return processBO;
    }

    @Override
    public OrderPurchaseProcessBO checkSrmOrderProcessByChangeOrder(RequisitionOrderChangeReq orderVO, OrderPurchaseProcessTypeEnum purchaseProcessTypeEnum, OrderDO orderDO) {
        //718之前订单并未保存流程规则id，所以无法对旧订单进行变更处理
        OrderProcessTaskDO task = orderDO.getTask();
        if(task.getProcessId() == null){
            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_CAN_NOT_TRANSFER);
        }
        Integer processType;
        switch (purchaseProcessTypeEnum) {
            case ORDER_TRADE: // 合同下单流程
                processType = ProcessTypeDetailEnum.TRANSACTION_CONTRACT_ORDER.getCode();
                break;
            case REQUISITION: // 请购单下单流程
                processType = ProcessTypeDetailEnum.PURCHASE_REQUEST_ORDER.getCode();
                break;
            default:
                // 非以上流程则未配置采购交易流程，无法提交订单
                throw new BusinessException(ResponseCodeEnum.ORDER_BASE_TRADE_PROCESS_DOES_NOT_EXIST);
        }
        OrderDO order = new OrderDO();
        order.setVendorRoleId(orderVO.getVendorRoleId());
        order.setVendorMemberId(orderVO.getVendorMemberId());
        order.setVendorMemberName(orderVO.getVendorMemberName());
        order.setBuyerMemberId(orderDO.getBuyerMemberId());
        order.setBuyerRoleId(orderDO.getBuyerRoleId());
        order.setProducts(orderVO.getProducts().stream().map(p -> {
            OrderProductDO orderProduct = new OrderProductDO();
            orderProduct.setName(p.getName());
            orderProduct.setCategory(p.getCategory());
            return orderProduct;
        }).collect(Collectors.toSet()));

        //Step 1: 新版匹配流程配置只支持单个商品匹配，所以将订单商品分开多次请求获取流程接口
        List<EngineResultDTO> engineResultDTOS = getEngineResult(order, orderDO.getBuyerMemberId(), orderDO.getBuyerRoleId(),processType);

        //Step 2: 如果查询结果为空，返回错误
        if(CollectionUtils.isEmpty(engineResultDTOS)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        //Step 3: 判断所有流程是否一致
        if(engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 4: 获取通过流程id拿到采购流程查询结果
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;
        OrderPurchaseProcessBO processBO = jpaQueryFactory.select(Projections.constructor(OrderPurchaseProcessBO.class, qOrderPurchaseProcess.processKey, qOrderPurchaseProcess.skipFirstStep, qOrderPurchaseProcess.processKind,qOrderPurchaseProcess.id))
                .from(qOrderPurchaseProcess)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrderPurchaseProcess.id.eq(engineResultDTOS.get(0).getProcessRuleId()))
                .fetchFirst();

        if(processBO == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        if(!task.getProcessId().equals(processBO.getProcessId())){
            throw new BusinessException(ResponseCodeEnum.ORDER_PROCESS_INCONFORMITY_ERROR);
        }

        return processBO;
    }

    @Override
    public OrderPurchaseProcessBO findSrmOrderChangeProcess(RequisitionOrderChangeReq orderVO, OrderDO orderDO) {
        OrderDO order = new OrderDO();
        order.setVendorRoleId(orderVO.getVendorRoleId());
        order.setVendorMemberId(orderVO.getVendorMemberId());
        order.setVendorMemberName(orderVO.getVendorMemberName());
        order.setBuyerMemberId(orderDO.getBuyerMemberId());
        order.setBuyerRoleId(orderDO.getBuyerRoleId());
        order.setProducts(orderVO.getProducts().stream().map(p -> {
            OrderProductDO orderProduct = new OrderProductDO();
            orderProduct.setName(p.getName());
            orderProduct.setCategory(p.getCategory());
            return orderProduct;
        }).collect(Collectors.toSet()));
        //Step 1: 新版匹配流程配置只支持单个商品匹配，所以将订单商品分开多次请求获取流程接口
        List<EngineResultDTO> engineResultDTOS = getEngineResult(order, orderDO.getBuyerMemberId(), orderDO.getBuyerRoleId(),ProcessTypeDetailEnum.ORDER_UPDATE_VALIDATE_SRM.getCode());

        //Step 2: 如果查询结果为空，返回错误
        if(CollectionUtils.isEmpty(engineResultDTOS)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        //Step 3: 判断所有流程是否一致
        if(engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_PROCESS_IS_DIFFERENT);
        }
        //Step 4: 获取通过流程id拿到采购流程查询结果
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;
        OrderPurchaseProcessBO processBO = jpaQueryFactory.select(Projections.constructor(OrderPurchaseProcessBO.class, qOrderPurchaseProcess.processKey, qOrderPurchaseProcess.skipFirstStep, qOrderPurchaseProcess.processKind,qOrderPurchaseProcess.id))
                .from(qOrderPurchaseProcess)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrderPurchaseProcess.id.eq(engineResultDTOS.get(0).getProcessRuleId()))
                .fetchFirst();

        if(processBO == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        return processBO;
    }

    /**
     * 查询采购订单（SRM）变更流程
     * @param order 订单
     * @param memberId 采购商会员id
     * @param memberRoleId 采购商会员角色id
     */
    @Override
    public OrderPurchaseProcessBO getSrmUpdateValidateProcess(OrderDO order, Long memberId, Long memberRoleId) {
        //Step 1: 新版匹配流程配置只支持单个商品匹配，所以将订单商品分开多次请求获取流程接口
        List<EngineResultDTO> engineResultDTOS = getEngineResult(order, memberId, memberRoleId,ProcessTypeDetailEnum.ORDER_UPDATE_VALIDATE_SRM.getCode());

        //Step 2: 如果查询结果为空，返回错误
        if(CollectionUtils.isEmpty(engineResultDTOS)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        //Step 3: 判断所有流程是否一致
        if(engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 4: 获取通过流程id拿到采购流程查询结果
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;
        OrderPurchaseProcessBO processBO = jpaQueryFactory.select(Projections.constructor(OrderPurchaseProcessBO.class, qOrderPurchaseProcess.processKey, qOrderPurchaseProcess.skipFirstStep, qOrderPurchaseProcess.processKind,qOrderPurchaseProcess.id))
                .from(qOrderPurchaseProcess)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrderPurchaseProcess.id.eq(engineResultDTOS.get(0).getProcessRuleId()))
                .fetchFirst();

        if(processBO == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        return processBO;
    }

    /**
     * 查询采购订单（B2B）变更流程
     * @param orders          订单集合
     * @return 查询结果
     */
    @Override
    public OrderTradeProcessBO getB2bUpdateValidateProcess(List<OrderDO> orders) {
        List<EngineResultDTO> engineResultDTOS = new ArrayList<>();
        orders.forEach(order -> {
            List<EngineResultDTO> engineResultDTOList = getEngineResult(order, order.getVendorMemberId(), order.getVendorRoleId(), ProcessTypeDetailEnum.ORDER_UPDATE_VALIDATE_B2B.getCode());
            engineResultDTOS.addAll(engineResultDTOList);
        });
        if(engineResultDTOS.isEmpty()){
            throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_DOES_NOT_CONFIG_PURCHASE_PROCESS);
        }

        //Step 3: 获取所有流程
        List<Long> processRuleIds = engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).collect(Collectors.toList());
        QOrderTradeProcessDO qOrderTradeProcess = QOrderTradeProcessDO.orderTradeProcessDO;
        QOrderTradeProcessProductDO qOrderTradeProcessProduct = QOrderTradeProcessProductDO.orderTradeProcessProductDO;
        QOrderTradeProcessPaymentDO qOrderTradeProcessPayment = QOrderTradeProcessPaymentDO.orderTradeProcessPaymentDO;
//        QOrderTradeProcessContractDO qOrderTradeProcessContract = QOrderTradeProcessContractDO.orderTradeProcessContractDO;

        List<TradeProcessDetailBO> processDetails = new ArrayList<>(jpaQueryFactory
                .from(qOrderTradeProcess)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .leftJoin(qOrderTradeProcessPayment).on(qOrderTradeProcess.id.eq(qOrderTradeProcessPayment.process.id))
//                .leftJoin(qOrderTradeProcessContract).on(qOrderTradeProcess.id.eq(qOrderTradeProcessContract.process.id))
                .where(qOrderTradeProcess.id.in(processRuleIds))
                .transform(GroupBy.groupBy(qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours).as(
                        Projections.constructor(TradeProcessDetailBO.class, qOrderTradeProcess.memberId, qOrderTradeProcess.roleId, qOrderTradeProcess.processKey, qOrderTradeProcess.processKind, qOrderTradeProcess.payTimes, qOrderTradeProcess.skipFirstStep, qOrderTradeProcess.allProducts, qOrderTradeProcessProduct.productId, qOrderTradeProcessProduct.skuId, qOrderTradeProcess.hasContract, qOrderTradeProcess.contractTemplateId, qOrderTradeProcess.expireHours,
                                GroupBy.list(Projections.constructor(PayNodeBO.class, qOrderTradeProcessPayment.serialNo, qOrderTradeProcessPayment.batchNo, qOrderTradeProcessPayment.payNode, qOrderTradeProcessPayment.payRate))
                        ))).values());

        if(CollectionUtils.isEmpty(processDetails)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VENDOR_DOES_NOT_CONFIG_TRADE_PROCESS);
        }

        //只有一个订单，且只有一个商品时，不判断其他条件
        if(orders.size() == 1 && orders.stream().mapToLong(p -> p.getProducts().size()).sum() == 1) {
            return new OrderTradeProcessBO(processDetails);
        }

        //Step 4: 判断所有流程是否一致
        if(engineResultDTOS.stream().map(EngineResultDTO::getProcessRuleId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 5: 判断合同是否不一致（无合同时，合同Id等于0）
        if(processDetails.stream().map(TradeProcessDetailBO::getContractTemplateId).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 6: 判断订单取消时间是否一致
        if(processDetails.stream().map(TradeProcessDetailBO::getExpireHours).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 7: 判断支付次数是否一致
        if(processDetails.stream().map(TradeProcessDetailBO::getPayTimes).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 8: 判断支付环节名称、比例是否一致
        if(processDetails.stream().map(detail -> detail.getPayNodes().stream().sorted(Comparator.comparingInt(PayNodeBO::getBatchNo)).map(p -> OrderPayNodeEnum.getNameByCode(p.getPayNode()).concat(NumberUtil.formatPayRate(p.getPayRate()))).collect(Collectors.joining(""))).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        return new OrderTradeProcessBO(processDetails);
    }

    /**
     * （批量）从平台后台的会员支付策略中，查询会员支付方式、支付渠道设置
     *
     * @param vendors 供应会员Id与角色Id列表
     * @return 查询结果
     */
    @Override
    public List<PlatformPaymentDetailBO> findPlatformMemberPayment(List<VendorBO> vendors) {
        QPlatformPaymentDO qPlatformPayment = QPlatformPaymentDO.platformPaymentDO;
        QPlatformPaymentTypeDO qPlatformPaymentType = QPlatformPaymentTypeDO.platformPaymentTypeDO;
        QPlatformPaymentChannelDO qPlatformPaymentChannel = QPlatformPaymentChannelDO.platformPaymentChannelDO;
        QPlatformPaymentMemberDO qPlatformPaymentMember = QPlatformPaymentMemberDO.platformPaymentMemberDO;

        //Step 1: 拼接会员、商品Id、SkuId查询条件
        Predicate[] predicates = vendors.stream().map(vendor -> qPlatformPaymentMember.memberId.eq(vendor.getVendorMemberId()).and(qPlatformPaymentMember.roleId.eq(vendor.getVendorRoleId()))).toArray(Predicate[]::new);

        //Step 2: 投影查询
        List<PlatformPaymentDetailBO> paymentDetails = jpaQueryFactory.select(Projections.constructor(PlatformPaymentDetailBO.class, qPlatformPaymentMember.memberId, qPlatformPaymentMember.roleId, qPlatformPaymentType.payType, qPlatformPaymentChannel.payChannel, qPlatformPaymentType.fundMode))
                .from(qPlatformPaymentType)
                .leftJoin(qPlatformPayment).on(qPlatformPayment.id.eq(qPlatformPaymentType.payment.id))
                .leftJoin(qPlatformPaymentChannel).on(qPlatformPaymentType.id.eq(qPlatformPaymentChannel.payType.id))
                .leftJoin(qPlatformPaymentMember).on(qPlatformPayment.id.eq(qPlatformPaymentMember.payment.id))
                .where(qPlatformPayment.status.eq(EnableDisableStatusEnum.ENABLE.getCode()))
                .where(qPlatformPayment.allMembers.isTrue().or(ExpressionUtils.anyOf(predicates)))
                .fetch();

        if(vendors.size() == 1) {
            return paymentDetails;
        }

        // 如果有会员没有设置支付策略，不能合并下单
        if(paymentDetails.stream().anyMatch(payment -> !payment.getVendorMemberId().equals(0L) || !payment.getVendorRoleId().equals(0L)) && vendors.stream().anyMatch(vendor -> paymentDetails.stream().noneMatch(payment -> payment.getVendorMemberId().equals(vendor.getVendorMemberId()) && payment.getVendorRoleId().equals(vendor.getVendorRoleId())))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CAN_NOT_MERGE_CAUSE_OF_PAYMENT_NOT_SET);
        }

        // 如果所有的支付方式的归集模式不为“平台代收”，不能合并下单
        if(paymentDetails.stream().anyMatch(payment -> !payment.getFundMode().equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_MEMBER_FUND_MODE_MUST_BE_PLATFORM_EXCHANGE);
        }

        // 如果支付方式不一致，不能合并下单
        if(paymentDetails.stream().collect(Collectors.groupingBy(p -> new VendorBO(p.getVendorMemberId(), p.getVendorRoleId()))).values().stream().map(v -> v.stream().sorted(Comparator.comparingInt(PlatformPaymentDetailBO::getPayChannel)).map(c -> String.valueOf(c.getPayChannel())).collect(Collectors.joining(""))).distinct().count() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_MEMBER_PAY_CHANNELS_CAN_NOT_BE_DIFFERENT);
        }

        return paymentDetails;
    }

    /**
     * 从平台后台的会员支付策略中，查询会员支付方式、支付渠道设置
     *
     * @param memberId 供应会员Id
     * @param roleId   供应会员角色Id
     * @return 查询结果
     */
    @Override
    public List<PlatformPayTypeBO> findPlatformMemberPayment(Long memberId, Long roleId) {
        QPlatformPaymentDO qPlatformPayment = QPlatformPaymentDO.platformPaymentDO;
        QPlatformPaymentTypeDO qPlatformPaymentType = QPlatformPaymentTypeDO.platformPaymentTypeDO;
        QPlatformPaymentChannelDO qPlatformPaymentChannel = QPlatformPaymentChannelDO.platformPaymentChannelDO;
        QPlatformPaymentMemberDO qPlatformPaymentMember = QPlatformPaymentMemberDO.platformPaymentMemberDO;

        return new ArrayList<>(jpaQueryFactory.from(qPlatformPaymentType).distinct()
                .leftJoin(qPlatformPayment).on(qPlatformPayment.id.eq(qPlatformPaymentType.payment.id))
                .leftJoin(qPlatformPaymentChannel).on(qPlatformPaymentType.id.eq(qPlatformPaymentChannel.payType.id))
                .leftJoin(qPlatformPaymentMember).on(qPlatformPayment.id.eq(qPlatformPaymentMember.payment.id))
                .where(qPlatformPayment.status.eq(EnableDisableStatusEnum.ENABLE.getCode()))
                .where(qPlatformPayment.allMembers.isTrue().or(qPlatformPaymentMember.memberId.eq(memberId).and(qPlatformPaymentMember.roleId.eq(roleId))))
                .transform(GroupBy.groupBy(qPlatformPaymentType.id).as(
                        Projections.constructor(PlatformPayTypeBO.class, qPlatformPaymentType.fundMode, qPlatformPaymentType.payType, qPlatformPaymentType.payTypeName,
                                GroupBy.list(Projections.constructor(PlatformPayChannelBO.class, qPlatformPaymentChannel.payChannel, qPlatformPaymentChannel.payChannelName)))))
                .values());
    }

    /**
     * 从平台后台的会员支付策略中，查询会员支付方式、支付渠道设置，拼接结算支付
     *
     * @param buyerMemberId  采购会员Id
     * @param buyerRoleId    采购会员角色Id
     * @param vendorMemberId 供应会员Id
     * @param vendorRoleId   供应会员角色Id
     * @return 查询结果
     */
    @Override
    public List<PlatformPayTypeBO> findPlatformMemberPayment(Long buyerMemberId, Long buyerRoleId, Long vendorMemberId, Long vendorRoleId, boolean needCheckSettlementPayType) {
        QPlatformPaymentDO qPlatformPayment = QPlatformPaymentDO.platformPaymentDO;
        QPlatformPaymentTypeDO qPlatformPaymentType = QPlatformPaymentTypeDO.platformPaymentTypeDO;
        QPlatformPaymentChannelDO qPlatformPaymentChannel = QPlatformPaymentChannelDO.platformPaymentChannelDO;
        QPlatformPaymentMemberDO qPlatformPaymentMember = QPlatformPaymentMemberDO.platformPaymentMemberDO;

        List<PlatformPayTypeBO> platformPayTypes = new ArrayList<>(jpaQueryFactory.from(qPlatformPaymentType).distinct()
                .leftJoin(qPlatformPayment).on(qPlatformPayment.id.eq(qPlatformPaymentType.payment.id))
                .leftJoin(qPlatformPaymentChannel).on(qPlatformPaymentType.id.eq(qPlatformPaymentChannel.payType.id))
                .leftJoin(qPlatformPaymentMember).on(qPlatformPayment.id.eq(qPlatformPaymentMember.payment.id))
                .where(qPlatformPayment.status.eq(EnableDisableStatusEnum.ENABLE.getCode()))
                .where(qPlatformPayment.allMembers.isTrue().or(qPlatformPaymentMember.memberId.eq(vendorMemberId).and(qPlatformPaymentMember.roleId.eq(vendorRoleId))))
                .transform(GroupBy.groupBy(qPlatformPaymentType.id).as(
                        Projections.constructor(PlatformPayTypeBO.class, qPlatformPaymentType.fundMode, qPlatformPaymentType.payType, qPlatformPaymentType.payTypeName,
                                GroupBy.list(Projections.constructor(PlatformPayChannelBO.class, qPlatformPaymentChannel.payChannel, qPlatformPaymentChannel.payChannelName)))))
                .values());

        //拼接结算支付
        if(needCheckSettlementPayType) {
            OrderPayTypeDetailResp settlementResult = settleAccountFeignService.findSettlementPaySetting(buyerMemberId, buyerRoleId, vendorMemberId, vendorRoleId);
            if (Objects.nonNull(settlementResult)) {
                platformPayTypes.add(new PlatformPayTypeBO(settlementResult.getFundMode(), settlementResult.getPayType(), settlementResult.getPayTypeName(), settlementResult.getPayChannels().stream().map(payChannel -> new PlatformPayChannelBO(payChannel.getPayChannel(), payChannel.getPayChannelName())).collect(Collectors.toList())));
            }
        }

        return platformPayTypes;
    }

    /**
     * 从平台后台的会员支付策略中，查询指定的支付渠道设置
     *
     * @param payChannel 支付渠道
     * @param memberId   会员Id
     * @param roleId     会员角色Id
     * @return 查询结果
     */
    @Override
    public PlatformPayTypeBO findPlatformMemberPayment(Integer payChannel, Long memberId, Long roleId) {
        QPlatformPaymentDO qPlatformPayment = QPlatformPaymentDO.platformPaymentDO;
        QPlatformPaymentTypeDO qPlatformPaymentType = QPlatformPaymentTypeDO.platformPaymentTypeDO;
        QPlatformPaymentChannelDO qPlatformPaymentChannel = QPlatformPaymentChannelDO.platformPaymentChannelDO;
        QPlatformPaymentMemberDO qPlatformPaymentMember = QPlatformPaymentMemberDO.platformPaymentMemberDO;

        PlatformPayTypeBO platformPayType = jpaQueryFactory
                .select(Projections.constructor(PlatformPayTypeBO.class, qPlatformPaymentType.fundMode, qPlatformPaymentType.payType, qPlatformPaymentType.payTypeName, Projections.list(Projections.constructor(PlatformPayChannelBO.class, qPlatformPaymentChannel.payChannel, qPlatformPaymentChannel.payChannelName))))
                .from(qPlatformPaymentType)
                .leftJoin(qPlatformPayment).on(qPlatformPaymentType.payment.id.eq(qPlatformPayment.id))
                .leftJoin(qPlatformPaymentChannel).on(qPlatformPaymentType.id.eq(qPlatformPaymentChannel.payType.id))
                .leftJoin(qPlatformPaymentMember).on(qPlatformPayment.id.eq(qPlatformPaymentMember.payment.id))
                .where(qPlatformPaymentChannel.payChannel.eq(payChannel))
                .where(qPlatformPayment.status.eq(EnableDisableStatusEnum.ENABLE.getCode()))
                .where(qPlatformPayment.allMembers.isTrue().or(qPlatformPaymentMember.memberId.eq(memberId).and(qPlatformPaymentMember.roleId.eq(roleId))))
                .fetchFirst();

        if (Objects.isNull(platformPayType)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_SETTING_NOT_SET_YET);
        }

        return platformPayType;
    }

    /**
     * 从平台后台的会员支付策略中，（批量）查询会员支付方式、支付渠道设置
     *
     * @param vendors 供应会员列表
     * @return 查询结果
     */
    @Override
    public List<OrderPayTypeDetailResp> findMemberPayment(Long buyerMemberId, Long buyerRoleId, List<VendorBO> vendors) {
        //如果只有一个供应商，拼接从资金账户服务查询到的结算支付方式
        if(vendors.size() == 1) {
            return findMemberPayment(buyerMemberId, buyerRoleId, vendors.get(0).getVendorMemberId(), vendors.get(0).getVendorRoleId());
        }

        List<PlatformPaymentDetailBO> paymentResult = findPlatformMemberPayment(vendors);

        //如果有多个供应商，资金归集模式必须是“平台代收”
        return paymentResult.stream().collect(Collectors.groupingBy(PlatformPaymentDetailBO::getPayType)).entrySet().stream().map(entry -> new OrderPayTypeDetailResp(FundModeEnum.PLATFORM_EXCHANGE.getCode(), entry.getKey(), OrderPayTypeEnum.getNameByCode(entry.getKey()), entry.getValue().stream().map(PlatformPaymentDetailBO::getPayChannel).distinct().sorted(Comparator.comparingInt(Integer::intValue)).map(payChannel -> new OrderPayChannelDetailResp(payChannel, OrderPayChannelEnum.getNameByCode(payChannel))).collect(Collectors.toList()))).sorted(Comparator.comparingInt(OrderPayTypeDetailResp::getPayType)).collect(Collectors.toList());
    }

    /**
     * 从平台后台的会员支付策略中，查询会员支付方式、支付渠道设置
     *
     * @param vendorMemberId 会员Id
     * @param vendorRoleId   会员角色Id
     * @return 查询结果
     */
    @Override
    public List<OrderPayTypeDetailResp> findMemberPayment(Long vendorMemberId, Long vendorRoleId) {
        //规则：
        // a. 平台后台“会员支付策略”过滤出平台代收模式、会员直接到账模式的支付渠道列表
        // b. 分别查询“平台支付参数设置”、“会员支付参数设置”中的支付方式与渠道，进行合并

        //Step 1: 查询平台后台会员支付策略
        List<PlatformPayTypeBO> platformPayTypes = findPlatformMemberPayment(vendorMemberId, vendorRoleId);
        if(CollectionUtils.isEmpty(platformPayTypes)) {
            return new ArrayList<>();
        }

        //Step 2: 查询平台支付参数设置
        QPlatformPaymentParameterDO qPlatformPaymentParameter = QPlatformPaymentParameterDO.platformPaymentParameterDO;
        List<PlatformPaymentParameterDO> platformParameters = jpaQueryFactory.select(qPlatformPaymentParameter).from(qPlatformPaymentParameter)
                .where(qPlatformPaymentParameter.payChannel.in(platformPayTypes.stream().filter(payType -> payType.getFundMode().equals(FundModeEnum.PLATFORM_EXCHANGE.getCode())).flatMap(payType -> payType.getChannels().stream().map(PlatformPayChannelBO::getPayChannel)).collect(Collectors.toList())))
                .fetch();

        List<OrderPayTypeDetailResp> platformTypes = platformParameters.stream().collect(Collectors.groupingBy(PlatformPaymentParameterDO::getPayType)).entrySet().stream().map(entry -> {
            OrderPayTypeDetailResp detailVO = new OrderPayTypeDetailResp();
            detailVO.setFundMode(FundModeEnum.PLATFORM_EXCHANGE.getCode());
            detailVO.setPayType(entry.getKey());
            detailVO.setPayTypeName(OrderPayTypeEnum.getNameByCode(entry.getKey()));
            detailVO.setPayChannels(entry.getValue().stream().map(parameter ->
                    new OrderPayChannelDetailResp(parameter.getPayChannel(), OrderPayChannelEnum.getNameByCode(parameter.getPayChannel()))
            ).sorted(Comparator.comparingInt(OrderPayChannelDetailResp::getPayChannel)).collect(Collectors.toList()));
            return detailVO;
        }).collect(Collectors.toList());

        //Step 3: 查询会员支付参数设置
        QMemberPaymentParameterDO qMemberPaymentParameter = QMemberPaymentParameterDO.memberPaymentParameterDO;
        List<MemberPaymentParameterDO> memberParameters = jpaQueryFactory
                .select(qMemberPaymentParameter).from(qMemberPaymentParameter)
                .where(qMemberPaymentParameter.memberId.eq(vendorMemberId).and(qMemberPaymentParameter.roleId.eq(vendorRoleId)).and(qMemberPaymentParameter.payChannel.in(platformPayTypes.stream().filter(payType -> payType.getFundMode().equals(FundModeEnum.DIRECT_TO_ACCOUNT.getCode())).flatMap(payType -> payType.getChannels().stream().map(PlatformPayChannelBO::getPayChannel)).collect(Collectors.toList()))))
                .fetch();

        List<OrderPayTypeDetailResp> memberTypes = memberParameters.stream().collect(Collectors.groupingBy(MemberPaymentParameterDO::getPayType)).entrySet().stream().map(entry -> {
            OrderPayTypeDetailResp detailVO = new OrderPayTypeDetailResp();
            detailVO.setFundMode(FundModeEnum.DIRECT_TO_ACCOUNT.getCode());
            detailVO.setPayType(entry.getKey());
            detailVO.setPayTypeName(OrderPayTypeEnum.getNameByCode(entry.getKey()));
            detailVO.setPayChannels(entry.getValue().stream().map(parameter ->
                    new OrderPayChannelDetailResp(parameter.getPayChannel(), OrderPayChannelEnum.getNameByCode(parameter.getPayChannel()))
            ).sorted(Comparator.comparingInt(OrderPayChannelDetailResp::getPayChannel)).collect(Collectors.toList()));
            return detailVO;
        }).collect(Collectors.toList());

        //Step 4: 合并
        platformTypes.addAll(memberTypes);

        //Step 5: 排序返回
        return platformTypes.stream().sorted(Comparator.comparingInt(OrderPayTypeDetailResp::getPayType)).collect(Collectors.toList());
    }

    /**
     * 从平台后台的会员支付策略、与结算服务中，查询会员支付方式、支付渠道设置
     *
     * @param buyerMemberId  采购会员Id
     * @param buyerRoleId    采购会员角色Id
     * @param vendorMemberId 供应会员Id
     * @param vendorRoleId   供应会员角色Id
     * @return 查询结果
     */
    @Override
    public List<OrderPayTypeDetailResp> findMemberPayment(Long buyerMemberId, Long buyerRoleId, Long vendorMemberId, Long vendorRoleId) {
        //规则：
        // a. 平台后台“会员支付策略”过滤出平台代收模式、会员直接到账模式的支付渠道列表
        // b. 分别查询“平台支付参数设置”、“会员支付参数设置”中的支付方式与渠道，进行合并
        // c. 拼接从结算服务查询到的“账期”、“月结”的支付方式配置

        //Step 1: 从结算服务查询是否配置了“账期”与“月结”的支付方式
        OrderPayTypeDetailResp settlementResult = settleAccountFeignService.findSettlementPaySetting(buyerMemberId, buyerRoleId, vendorMemberId, vendorRoleId);

        //Step 1: 查询平台后台会员支付策略
        List<PlatformPayTypeBO> platformPayTypes = findPlatformMemberPayment(vendorMemberId, vendorRoleId);
        if(CollectionUtils.isEmpty(platformPayTypes)) {
            return new ArrayList<>();
        }

        //Step 2: 查询平台支付参数设置
        QPlatformPaymentParameterDO qPlatformPaymentParameter = QPlatformPaymentParameterDO.platformPaymentParameterDO;
        List<PlatformPaymentParameterDO> platformParameters = jpaQueryFactory.select(qPlatformPaymentParameter).from(qPlatformPaymentParameter)
                .where(qPlatformPaymentParameter.payChannel.in(platformPayTypes.stream().filter(payType -> payType.getFundMode().equals(FundModeEnum.PLATFORM_EXCHANGE.getCode())).flatMap(payType -> payType.getChannels().stream().map(PlatformPayChannelBO::getPayChannel)).collect(Collectors.toList())))
                .fetch();

        List<OrderPayTypeDetailResp> platformTypes = platformParameters.stream().collect(Collectors.groupingBy(PlatformPaymentParameterDO::getPayType)).entrySet().stream().map(entry -> {
            OrderPayTypeDetailResp detailVO = new OrderPayTypeDetailResp();
            detailVO.setFundMode(FundModeEnum.PLATFORM_EXCHANGE.getCode());
            detailVO.setPayType(entry.getKey());
            detailVO.setPayTypeName(OrderPayTypeEnum.getNameByCode(entry.getKey()));
            detailVO.setPayChannels(entry.getValue().stream().map(parameter ->
                    new OrderPayChannelDetailResp(parameter.getPayChannel(), OrderPayChannelEnum.getNameByCode(parameter.getPayChannel()))
            ).sorted(Comparator.comparingInt(OrderPayChannelDetailResp::getPayChannel)).collect(Collectors.toList()));
            return detailVO;
        }).collect(Collectors.toList());

        //Step 3: 查询会员支付参数设置
        QMemberPaymentParameterDO qMemberPaymentParameter = QMemberPaymentParameterDO.memberPaymentParameterDO;
        List<MemberPaymentParameterDO> memberParameters = jpaQueryFactory
                .select(qMemberPaymentParameter).from(qMemberPaymentParameter)
                .where(qMemberPaymentParameter.memberId.eq(vendorMemberId).and(qMemberPaymentParameter.roleId.eq(vendorRoleId)).and(qMemberPaymentParameter.payChannel.in(platformPayTypes.stream().filter(payType -> payType.getFundMode().equals(FundModeEnum.DIRECT_TO_ACCOUNT.getCode())).flatMap(payType -> payType.getChannels().stream().map(PlatformPayChannelBO::getPayChannel)).collect(Collectors.toList()))))
                .fetch();

        List<OrderPayTypeDetailResp> memberTypes = memberParameters.stream().collect(Collectors.groupingBy(MemberPaymentParameterDO::getPayType)).entrySet().stream().map(entry -> {
            OrderPayTypeDetailResp detailVO = new OrderPayTypeDetailResp();
            detailVO.setFundMode(FundModeEnum.DIRECT_TO_ACCOUNT.getCode());
            detailVO.setPayType(entry.getKey());
            detailVO.setPayTypeName(OrderPayTypeEnum.getNameByCode(entry.getKey()));
            detailVO.setPayChannels(entry.getValue().stream().map(parameter ->
                    new OrderPayChannelDetailResp(parameter.getPayChannel(), OrderPayChannelEnum.getNameByCode(parameter.getPayChannel()))
            ).sorted(Comparator.comparingInt(OrderPayChannelDetailResp::getPayChannel)).collect(Collectors.toList()));
            return detailVO;
        }).collect(Collectors.toList());

        //Step 4: 合并
        platformTypes.addAll(memberTypes);

        //Step 5: 账期、月结
        if(Objects.nonNull(settlementResult)) {
            platformTypes.add(settlementResult);
        }

        //Step 6: 排序返回
        return platformTypes.stream().sorted(Comparator.comparingInt(OrderPayTypeDetailResp::getPayType)).collect(Collectors.toList());
    }

    /**
     * （拆单）批量查询、校验供应商支付方式及渠道配置
     *
     * @param vendors 供应商列表
     * @return 查询结果
     */
    @Override
    public Map<VendorBO, List<OrderPayTypeDetailResp>> checkMemberPayments(List<VendorBO> vendors) {
        Map<VendorBO, List<OrderPayTypeDetailResp>> paymentMap = new HashMap<>();
        for (VendorBO vendor : vendors) {
            List<OrderPayTypeDetailResp> paymentResult = findMemberPayment(vendor.getVendorMemberId(), vendor.getVendorRoleId());
            if(CollectionUtils.isEmpty(paymentResult)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_MEMBER_HAS_NOT_PAYMENT_SETTING);
            }

            paymentMap.put(vendor, paymentResult);
        }

        return paymentMap;
    }

    /**
     * 查询支付参数
     *
     * @param payChannel 支付渠道
     * @param memberId   会员Id
     * @param roleId     会员角色Id
     * @return 支付参数
     */
    @Override
    public OrderPaymentParameterDetailBO findOrderPaymentParameters(Integer payChannel, Long memberId, Long roleId) {
        //Step 1: 先查询平台支付策略配置
        List<PlatformPayTypeBO> platformPayTypes = findPlatformMemberPayment(memberId, roleId);
        if(CollectionUtils.isEmpty(platformPayTypes)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_SETTING_NOT_SET_YET);
        }

        PlatformPayTypeBO platformPayType = platformPayTypes.stream().filter(payType -> payType.getChannels().stream().anyMatch(platformPayChannel -> platformPayChannel.getPayChannel().equals(payChannel))).findFirst().orElse(null);
        if(platformPayType == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_SETTING_DOES_NOT_EXIST);
        }

        //Step 2:如果支付渠道的资金归集模式是“平台代收模式”，查询平台支付参数设置
        if(platformPayType.getFundMode().equals(FundModeEnum.PLATFORM_EXCHANGE.getCode())) {
            return findPlatformPaymentParameters(payChannel);
        }

        //Step 3:如果支付渠道的资金归集模式是“会员直接到账”，查询会员支付参数设置
        return findMemberPaymentParameters(payChannel, memberId, roleId);
    }

    /**
     * 查询支付参数（直接查询平台支付参数设置）
     *
     * @param payChannel 支付渠道
     * @return 支付参数
     */
    @Override
    public OrderPaymentParameterDetailBO findPlatformPaymentParameters(Integer payChannel) {
        QPlatformPaymentParameterDO qPlatformPaymentParameter = QPlatformPaymentParameterDO.platformPaymentParameterDO;
        PlatformPaymentParameterDO platformPaymentParameter = jpaQueryFactory.select(qPlatformPaymentParameter)
                .from(qPlatformPaymentParameter)
                .where(qPlatformPaymentParameter.payChannel.eq(payChannel)).fetchFirst();
        if(platformPaymentParameter == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PARAMETER_DOES_NOT_EXIST);
        }

        return new OrderPaymentParameterDetailBO(platformPaymentParameter.getMemberId(), platformPaymentParameter.getRoleId(), FundModeEnum.PLATFORM_EXCHANGE.getCode(), platformPaymentParameter.getPayChannel(), platformPaymentParameter.getPayChannelName(), platformPaymentParameter.getParameters().stream().map(parameter -> new PayChannelParameterBO(parameter.getCode(), OrderPaymentParameterEnum.getKeyByCode(parameter.getCode()), parameter.getValue())).collect(Collectors.toList()));
    }

    /**
     * 查询支付参数（直接查询会员支付参数设置）
     *
     * @param payChannel 支付渠道
     * @param memberId   会员Id
     * @param roleId     会员角色Id
     * @return 支付参数
     */
    @Override
    public OrderPaymentParameterDetailBO findMemberPaymentParameters(Integer payChannel, Long memberId, Long roleId) {
        QMemberPaymentParameterDO qMemberPaymentParameter = QMemberPaymentParameterDO.memberPaymentParameterDO;
        MemberPaymentParameterDO memberPaymentParameter = jpaQueryFactory.select(qMemberPaymentParameter)
                .from(qMemberPaymentParameter)
                .where(qMemberPaymentParameter.payChannel.eq(payChannel).and(qMemberPaymentParameter.memberId.eq(memberId)).and(qMemberPaymentParameter.roleId.eq(roleId)))
                .fetchFirst();
        if(memberPaymentParameter == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_MEMBER_PARAMETER_DOES_NOT_EXIST);
        }

        return new OrderPaymentParameterDetailBO(memberPaymentParameter.getMemberId(), memberPaymentParameter.getRoleId(), FundModeEnum.DIRECT_TO_ACCOUNT.getCode(), memberPaymentParameter.getPayChannel(), memberPaymentParameter.getPayChannelName(), memberPaymentParameter.getParameters().stream().map(parameter -> new PayChannelParameterBO(parameter.getCode(), OrderPaymentParameterEnum.getKeyByCode(parameter.getCode()), parameter.getValue())).collect(Collectors.toList()));
    }

    /**
     * 商品能力 - 上架指引查询商品是否配置了交易流程
     *
     * @param memberId （供应商）会员Id
     * @param roleId   （供应商）会员角色Id
     * @param shopIds  商城Id列表
     * @param products 商品Id、SkuId、价格类型列表
     * @return 没有配置交易流程的商品Id、SkuId、价格类型列表
     */
    @Override
    public List<OrderProductProcessQueryResp> findProductProcess(Long memberId, Long roleId, List<Long> shopIds, Integer shopType, List<OrderProductPriceTypeReq> products) {

        List<Integer> processTypes = new ArrayList<>();
        //根据商城类型判断交易流程类型
        OrderProductPriceTypeReq productPriceType = products.stream().findFirst().orElse(null);
        if (productPriceType != null && productPriceType.getCrossBorder() != null && productPriceType.getCrossBorder()) {
            processTypes.add(OrderTradeProcessTypeEnum.ORDER_COMMERCE_IMPORT.getCode());
        } else if (shopType.equals(ShopTypeEnum.SCORE.getCode())) {
            processTypes.add(OrderTradeProcessTypeEnum.RIGHT_POINT.getCode());
        } else {
            processTypes.add(OrderTradeProcessTypeEnum.ORDER_TRADE.getCode());
        }
        List<OrderTradeProcessDO> processes = orderTradeProcessRepository.findByProcessTypeInAndIsDefaultAndMemberIdAndRoleId(processTypes,ProcessDefaultEnum.YES.getCode(),0L,0L);
        //如果没有默认交易规则返回所有商品
        if(CollectionUtils.isEmpty(processes)){
            return products.stream().map(product -> new OrderProductProcessQueryResp(product.getProductId(), product.getSkuId(), product.getPriceType())).collect(Collectors.toList());
        }else{
            return new ArrayList<>();
        }
    }

    /**
     * 获取引擎返回流程
     * @param order         订单
     * @param memberId      会员ID
     * @param memberRoleId  会员角色ID
     * @param type          流程类型
     * @return EngineResult
     */
    private List<EngineResultDTO> getEngineResult(OrderDO order, Long memberId, Long memberRoleId, Integer type){
        EngineRuleQueryReq query = new EngineRuleQueryReq(type, memberId, memberRoleId);
        List<EngineResultDTO> engineResultDTOS = new ArrayList<>();
        WrapperResp<List<ProcessEngineRuleResp>> wrapperResp = processEngineRuleFeign.getEngineRuleList(query);
        if (WrapperUtil.isFail(wrapperResp.getCode())){
            throw new BusinessException(wrapperResp.getCode(), wrapperResp.getMessage());
        }
        Set<OrderProductDO> orderProducts = new HashSet<>();
        log.info("++++++++++++++++++订单流程匹配, 采购会员名称: {}, 采购会员角色Id：{}, 买方（采购）会员Id：{}", order.getBuyerMemberName(),order.getBuyerRoleId(),order.getBuyerMemberId());
        order.getProducts().forEach( p -> {
            orderProducts.clear();
            if (StringUtils.hasLength(p.getSpec())){
                p.setName(String.join("/", p.getName(), p.getSpec()));
            }
            orderProducts.add(p);
            order.setProducts(orderProducts);
            log.info("++++++++++++++++++订单流程匹配, 商品名称: {}, 商品规格: {}, 商品品类名称：{}", p.getName(), p.getSpec(), p.getCategory());
            EngineResultDTO engineResultDTO = new EngineRuleUtil<>(order, wrapperResp.getData()).meta();
            if (engineResultDTO == null || engineResultDTO.getProcessKey() == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_VENDOR_TRADE_PROCESS_DOES_NOT_CONTAIN_PRODUCTS);
            }
            engineResultDTOS.add(engineResultDTO);
        });
        return engineResultDTOS;
    }
}
