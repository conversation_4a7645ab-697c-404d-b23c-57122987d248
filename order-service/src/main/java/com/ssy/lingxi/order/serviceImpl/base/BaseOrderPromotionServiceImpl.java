package com.ssy.lingxi.order.serviceImpl.base;

import com.ssy.lingxi.common.enums.order.OrderSourceKindEnum;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.marketing.ActivityTypeEnum;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.domain.OrderDM;
import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.entity.OrderProductDO;
import com.ssy.lingxi.order.entity.OrderPromotionDO;
import com.ssy.lingxi.order.model.dto.OrderSeparateDTO;
import com.ssy.lingxi.order.model.req.basic.OrderPromotionReq;
import com.ssy.lingxi.order.model.resp.vendor.VendorOrderProductPromotionResp;
import com.ssy.lingxi.order.repository.OrderPromotionRepository;
import com.ssy.lingxi.order.service.base.IBaseOrderPromotionService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单商品关联的营销活动相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-23
 */
@Service
public class BaseOrderPromotionServiceImpl implements IBaseOrderPromotionService {
    @Resource
    private OrderPromotionRepository orderPromotionRepository;

    /**
     * （拆单）根据拆单订单商品金额总和，加权平均计算拆单后的营销活动金额
     *
     * @param promotionAmount    订单营销活动金额
     * @param promotionAmountMap 拆单订单商品金额总和
     * @return 拆单订单营销活动金额
     */
    @Override
    public Map<OrderSeparateDTO, BigDecimal> findSeparateOrderPromotionAmount(BigDecimal promotionAmount, Map<OrderSeparateDTO, BigDecimal> promotionAmountMap) {
        if(NumberUtil.isNullOrZero(promotionAmount)) {
            return promotionAmountMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, p -> BigDecimal.ZERO));
        }

        BigDecimal totalAmount = promotionAmountMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        if(totalAmount.compareTo(BigDecimal.ZERO) == 0) {
            return promotionAmountMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, p -> BigDecimal.ZERO));
        }

        return promotionAmountMap.entrySet().stream().collect(Collectors.toMap(
                // key为供应商(即对应拆单后的一个订单), value为((p.getValue()为营销活动价格总和) * (promotionAmount为前端传递的营销金额) / (totalAmount为拆单前的营销活动价格总和))
                Map.Entry::getKey, p -> p.getValue().multiply(promotionAmount).divide(totalAmount, 4, RoundingMode.HALF_UP)
        ));
    }

    /**
     * 检查并保存订单商品关联的营销活动
     *
     * @param orderProduct 订单商品
     * @param promotions   营销活动列表
     */
    @Override
    public void checkProductPromotions(OrderProductDO orderProduct, List<OrderPromotionReq> promotions) {
        if(CollectionUtils.isEmpty(promotions)) {
            orderProduct.setPromotions(new HashSet<>());
            return;
        }

        List<OrderPromotionDO> orderPromotions = promotions.stream().map(p -> OrderDM.buildOrderPromotionDOBy(orderProduct, p)).collect(Collectors.toList());

        orderPromotionRepository.saveAll(orderPromotions);

        orderProduct.setPromotions(new HashSet<>(orderPromotions));
    }

    /**
     * 查询订单商品营销活动记录
     *
     * @param orderProduct 订单商品
     * @return 查询结果
     */
    @Override
    public List<VendorOrderProductPromotionResp> findOrderProductPromotions(OrderProductDO orderProduct) {
        return orderPromotionRepository.findByProduct(orderProduct).stream().map(promotion -> {
            VendorOrderProductPromotionResp promotionVO = new VendorOrderProductPromotionResp();
            promotionVO.setPromotionId(promotion.getPromotionId());
            promotionVO.setName(promotion.getName());
            promotionVO.setPromotionType(promotion.getPromotionType());
            promotionVO.setPromotionTypeName(ActivityTypeEnum.getNameByCode(promotion.getPromotionType()));
            promotionVO.setBelongType(promotion.getBelongType());
            promotionVO.setBelongTypeName(BelongTypeEnum.getNameByCode(promotion.getBelongType()));
            promotionVO.setStartTime(promotion.getStartTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            promotionVO.setExpireTime(promotion.getExpireTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            return promotionVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询拼团订单的拼团Id
     *
     * @param order 订单
     * @return 拼团Id
     */
    @Override
    public Long findGroupOrderRecordId(OrderDO order) {
        if(!order.getOrderKind().equals(OrderSourceKindEnum.GROUP.getCode())) {
            return 0L;
        }

        return order.getGroupId();
    }
}
