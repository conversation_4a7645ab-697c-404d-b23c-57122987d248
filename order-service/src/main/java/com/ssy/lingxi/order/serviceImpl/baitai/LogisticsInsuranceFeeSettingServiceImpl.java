package com.ssy.lingxi.order.serviceImpl.baitai;

import cn.hutool.core.bean.BeanUtil;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.enums.StatusTypeEnum;
import com.ssy.lingxi.common.enums.product.InsuranceAmountTypeEnum;
import com.ssy.lingxi.common.enums.product.InsuranceCompanyEnum;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.order.entity.LogisticsInsuranceFeeSettingDO;
import com.ssy.lingxi.order.entity.QLogisticsInsuranceFeeSettingDO;
import com.ssy.lingxi.order.model.bo.SettingBO;
import com.ssy.lingxi.order.model.req.baitai.CalculateReq;
import com.ssy.lingxi.order.model.req.baitai.LogisticsInsuranceFeeSettingCreateReq;
import com.ssy.lingxi.order.model.req.baitai.LogisticsInsuranceFeeSettingStatusReq;
import com.ssy.lingxi.order.model.resp.baitai.CalculateResp;
import com.ssy.lingxi.order.model.resp.baitai.LogisticsInsuranceFeeSettingDetailResp;
import com.ssy.lingxi.order.model.resp.baitai.LogisticsInsuranceFeeSettingPageResp;
import com.ssy.lingxi.order.repository.LogisticsInsuranceFeeSettingRepository;
import com.ssy.lingxi.order.service.baitai.LogisticsInsuranceFeeSettingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/22
 */
@Service
public class LogisticsInsuranceFeeSettingServiceImpl implements LogisticsInsuranceFeeSettingService {

    @Resource
    private LogisticsInsuranceFeeSettingRepository logisticsInsuranceFeeSettingRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    /**
     * 新增物流保费设置
     * @param request 新增请求参数
     * @return 新增结果
     */
    @Override
    public WrapperResp<Void> create(LogisticsInsuranceFeeSettingCreateReq request) {
        LogisticsInsuranceFeeSettingDO logisticsInsuranceFeeSettingDO = BeanUtil.copyProperties(request, LogisticsInsuranceFeeSettingDO.class);
        logisticsInsuranceFeeSettingDO.setStatus(StatusTypeEnum.START.getStatus());
        logisticsInsuranceFeeSettingRepository.save(logisticsInsuranceFeeSettingDO);
        return WrapperUtil.success();
    }

    /**
     * 更新物流保费设置
     *
     * @param request 更新请求参数
     * @return 更新结果
     */
    @Override
    public WrapperResp<Void> update(LogisticsInsuranceFeeSettingCreateReq request) {
        // 最小保额要大于最低保额
//        if (request.getMinInsuranceAmount().compareTo(request.getMinAmount()) < 0) {
//            return WrapperUtil.fail("最小保额必须大于最低保额");
//        }

        // 校验最小保费
        List<SettingBO> settingBOList = request.getSettingBOList().stream().sorted(Comparator.comparing(SettingBO::getMinAmount)).collect(Collectors.toList());
        // 阶梯保费的最小值，必须和上一个阶梯的最大值相等
        for (int i = 0; i < settingBOList.size(); i++) {
            SettingBO settingBO = settingBOList.get(i);
            if (i > 0) {
                SettingBO preSettingBO = settingBOList.get(i - 1);
                if (settingBO.getMinAmount().compareTo(preSettingBO.getMaxAmount()) != 0) {
                    return WrapperUtil.fail("阶梯保费的最小值，必须和上一个阶梯的最大值相等");
                }
            }
            // 当前阶梯最小保额，要小于当前阶梯最大保额
            if (settingBO.getMinAmount().compareTo(settingBO.getMaxAmount()) > 0) {
                return WrapperUtil.fail("阶梯保费的最小值，必须小于当前阶梯最大值");
            }
            // 校验最小保额
            if (settingBO.getMinAmount().compareTo(request.getMinInsuranceAmount()) < 0) {
                return WrapperUtil.fail("阶梯保费的最小值，必须大于等于最小保额");
            }
        }

        LogisticsInsuranceFeeSettingDO logisticsInsuranceFeeSettingDO = logisticsInsuranceFeeSettingRepository.findById(request.getId()).orElseThrow(() -> new RuntimeException("物流保费设置不存在"));
        BeanUtil.copyProperties(request, logisticsInsuranceFeeSettingDO);
        logisticsInsuranceFeeSettingRepository.save(logisticsInsuranceFeeSettingDO);
        return WrapperUtil.success();
    }

    /**
     * 查询物流保费详情
     *
     * @param id 物流保费设置id
     * @return 物流保费设置详情
     */
    @Override
    public WrapperResp<LogisticsInsuranceFeeSettingDetailResp> getDetail(Long id) {
        LogisticsInsuranceFeeSettingDO logisticsInsuranceFeeSettingDO = logisticsInsuranceFeeSettingRepository.findById(id).orElseThrow(() -> new RuntimeException("物流保费设置不存在"));
        LogisticsInsuranceFeeSettingDetailResp logisticsInsuranceFeeSettingDetailResp = BeanUtil.copyProperties(logisticsInsuranceFeeSettingDO, LogisticsInsuranceFeeSettingDetailResp.class);
        logisticsInsuranceFeeSettingDetailResp.setStatusName(StatusTypeEnum.getNameByCode(logisticsInsuranceFeeSettingDO.getStatus()));
        logisticsInsuranceFeeSettingDetailResp.setMinInsuranceAmountTypeName(InsuranceAmountTypeEnum.getMessage(logisticsInsuranceFeeSettingDetailResp.getMinInsuranceAmountType()));
        logisticsInsuranceFeeSettingDetailResp.setMaxInsuranceAmountTypeName(InsuranceAmountTypeEnum.getMessage(logisticsInsuranceFeeSettingDetailResp.getMaxInsuranceAmountType()));
        logisticsInsuranceFeeSettingDetailResp.setInsuranceCompanyName(InsuranceCompanyEnum.getNameByCode(logisticsInsuranceFeeSettingDetailResp.getInsuranceCompany()));
        return WrapperUtil.success(logisticsInsuranceFeeSettingDetailResp);
    }

    /**
     * 查询物流设置列表
     * @return 查询结果
     */
    @Override
    public WrapperResp<List<LogisticsInsuranceFeeSettingPageResp>> getList() {
        List<LogisticsInsuranceFeeSettingDO> insuranceFeeSettingDOList = logisticsInsuranceFeeSettingRepository.findAll();
        List<LogisticsInsuranceFeeSettingPageResp> insuranceFeeSettingPageRespList = insuranceFeeSettingDOList.stream().map(s -> {
            LogisticsInsuranceFeeSettingPageResp logisticsInsuranceFeeSettingPageResp = BeanUtil.copyProperties(s, LogisticsInsuranceFeeSettingPageResp.class);
            logisticsInsuranceFeeSettingPageResp.setStatusName(StatusTypeEnum.getNameByCode(s.getStatus()));
            logisticsInsuranceFeeSettingPageResp.setIsMustInsuranceName(CommonBooleanEnum.getMessage(logisticsInsuranceFeeSettingPageResp.getIsMustInsurance()));
            logisticsInsuranceFeeSettingPageResp.setInsuranceCompanyName(InsuranceCompanyEnum.getNameByCode(s.getInsuranceCompany()));
            return logisticsInsuranceFeeSettingPageResp;
        }).collect(Collectors.toList());
        return WrapperUtil.success(insuranceFeeSettingPageRespList);
    }

    /**
     * 根据保额计算保费
     * @param calculateReq 保额
     * @return 保费
     */
    @Override
    public WrapperResp<CalculateResp> calculateInsuranceFee(CalculateReq calculateReq) {
        LogisticsInsuranceFeeSettingDO logisticsInsuranceFeeSettingDO = logisticsInsuranceFeeSettingRepository.findFirstByInsuranceCompany(calculateReq.getInsuranceCompanyType());
        // 保费
        BigDecimal insuranceFee = BigDecimal.ZERO;
        // 保额比例
        BigDecimal insuranceFeeRate = BigDecimal.ZERO;
        // 保额比例，整数
        BigDecimal insuranceAmountRate = BigDecimal.ZERO;
        // 保额类型
        Integer insuranceAmountType = InsuranceAmountTypeEnum.FIXED.getCode();

        // 判断保额是否小于最小保额，不满足按照最小保额计算
        if (calculateReq.getInsuranceAmount().compareTo(logisticsInsuranceFeeSettingDO.getMinInsuranceAmount()) < 0) {
            insuranceAmountType = logisticsInsuranceFeeSettingDO.getMinInsuranceAmountType();
            if (InsuranceAmountTypeEnum.FIXED.getCode().equals(logisticsInsuranceFeeSettingDO.getMinInsuranceAmountType())) {
                insuranceFee = logisticsInsuranceFeeSettingDO.getMinInsuranceAmountValue();
            } else {
                insuranceAmountRate = logisticsInsuranceFeeSettingDO.getMinInsuranceAmountValue();
                insuranceFeeRate = logisticsInsuranceFeeSettingDO.getMinInsuranceAmountValue().divide(new BigDecimal(100000), 5, RoundingMode.HALF_UP);
                insuranceFee = insuranceFeeRate.multiply(calculateReq.getInsuranceAmount());
            }
        }
        // 判断保额是否大于或等于最大保额
        else if (calculateReq.getInsuranceAmount().compareTo(logisticsInsuranceFeeSettingDO.getMaxInsuranceAmount()) >= 0) {
            insuranceAmountType = logisticsInsuranceFeeSettingDO.getMaxInsuranceAmountType();
            if (InsuranceAmountTypeEnum.FIXED.getCode().equals(logisticsInsuranceFeeSettingDO.getMaxInsuranceAmountType())) {
                insuranceFee = logisticsInsuranceFeeSettingDO.getMaxInsuranceAmountValue();
            } else {
                // 计算保费，十万分之几，现在保费比例是整数，转换成小数（calculateReq.getInsuranceAmount()）
                insuranceAmountRate = logisticsInsuranceFeeSettingDO.getMaxInsuranceAmountValue();
                insuranceFeeRate = logisticsInsuranceFeeSettingDO.getMaxInsuranceAmountValue().divide(new BigDecimal(100000), 5, RoundingMode.HALF_UP);
                insuranceFee = insuranceFeeRate.multiply(calculateReq.getInsuranceAmount());
            }
        }
        // 根据保费阶梯价格，计算保费
        // 按照保额最小值排序
        List<SettingBO> settingBOList = logisticsInsuranceFeeSettingDO.getSettingBOList().stream().sorted(Comparator.comparing(SettingBO::getMinAmount)).collect(Collectors.toList());
        // 从小到大获取
        for (SettingBO settingBO : settingBOList) {
            // 判断保额是否在阶梯范围内
            if (calculateReq.getInsuranceAmount().compareTo(settingBO.getMinAmount()) >= 0 && calculateReq.getInsuranceAmount().compareTo(settingBO.getMaxAmount()) < 0) {
                insuranceAmountType = settingBO.getInsuranceAmountType();
                if (InsuranceAmountTypeEnum.FIXED.getCode().equals(settingBO.getInsuranceAmountType())) {
                    insuranceFee = settingBO.getInsuranceAmount();
                } else {
                    // 计算保费，十万分之几，现在保费比例是整数，转换成小数（calculateReq.getInsuranceAmount()）
                    insuranceAmountRate = settingBO.getInsuranceAmount();
                    insuranceFeeRate = settingBO.getInsuranceAmount().divide(new BigDecimal(100000), 5, RoundingMode.HALF_UP);
                    insuranceFee = insuranceFeeRate.multiply(calculateReq.getInsuranceAmount());
                }
                break;
            }
        }
        // 如果保费小于最小保费，则按照最小保费计算
        if (insuranceFee.compareTo(logisticsInsuranceFeeSettingDO.getMinInsuranceAmountValue()) < 0) {
            insuranceFee = logisticsInsuranceFeeSettingDO.getMinInsuranceAmountValue();
        }

        CalculateResp insuranceFeeResp = new CalculateResp();
        insuranceFeeResp.setInsuranceFee(insuranceFee);
        insuranceFeeResp.setInsuranceFeeRate(insuranceAmountRate);
        insuranceFeeResp.setInsuranceAmountType(insuranceAmountType);
        return WrapperUtil.success(insuranceFeeResp);
    }

    /**
     * 修改保费设置状态
     * @paraml LogisticsInsuranceFeeSettingStatusReq 状态请求参数
     * @return 操作结果
     */
    @Override
    @Transactional
    public WrapperResp<Void> updateStatus(LogisticsInsuranceFeeSettingStatusReq request) {
        QLogisticsInsuranceFeeSettingDO qLogisticsInsuranceFeeSettingDO = QLogisticsInsuranceFeeSettingDO.logisticsInsuranceFeeSettingDO;
        jpaQueryFactory.update(qLogisticsInsuranceFeeSettingDO)
                .set(qLogisticsInsuranceFeeSettingDO.status, request.getStatus())
                .where(qLogisticsInsuranceFeeSettingDO.id.eq(request.getId()))
                .execute();;
        return WrapperUtil.success();
    }
}
