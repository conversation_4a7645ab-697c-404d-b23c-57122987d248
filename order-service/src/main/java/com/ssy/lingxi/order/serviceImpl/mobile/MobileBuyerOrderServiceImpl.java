package com.ssy.lingxi.order.serviceImpl.mobile;

import cn.hutool.core.util.ObjectUtil;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.rest.model.resp.eos.GoldPriceResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.member.api.feign.IMemberDetailFeign;
import com.ssy.lingxi.member.api.model.req.UserIdFeignReq;
import com.ssy.lingxi.order.api.model.resp.OrderDepositResp;
import com.ssy.lingxi.order.controller.feign.OrderProcessFeignController;
import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.entity.OrderPaymentDO;
import com.ssy.lingxi.order.enums.BuyerInnerStatusEnum;
import com.ssy.lingxi.order.enums.OrderOuterStatusEnum;
import com.ssy.lingxi.order.model.dto.MobileOrderPageDTO;
import com.ssy.lingxi.order.model.dto.MobileOrderQueryDTO;
import com.ssy.lingxi.order.model.req.basic.OrderAgreeReq;
import com.ssy.lingxi.order.model.req.basic.OrderIdReq;
import com.ssy.lingxi.order.model.req.basic.OrderReasonReq;
import com.ssy.lingxi.order.model.req.buyer.BuyerOrderPayReq;
import com.ssy.lingxi.order.model.req.buyer.BuyerPayResultReq;
import com.ssy.lingxi.order.model.req.buyer.BuyerReceiveReq;
import com.ssy.lingxi.order.model.req.buyer.OrderBuyerMergePayReq;
import com.ssy.lingxi.order.model.req.mobile.MobileManagePageDataReq;
import com.ssy.lingxi.order.model.req.mobile.MobileOrderDeliveryTimeUpdateReq;
import com.ssy.lingxi.order.model.req.mobile.MobilePageDataReq;
import com.ssy.lingxi.order.model.resp.basic.OrderPayResultDetailResp;
import com.ssy.lingxi.order.model.resp.basic.OrderPayTypeDetailResp;
import com.ssy.lingxi.order.model.resp.buyer.BuyerPayResultDetailResp;
import com.ssy.lingxi.order.model.resp.mobile.*;
import com.ssy.lingxi.order.repository.OrderPaymentRepository;
import com.ssy.lingxi.order.repository.OrderRepository;
import com.ssy.lingxi.order.service.base.IBaseMobileOrderService;
import com.ssy.lingxi.order.service.mobile.IMobileBuyerOrderService;
import com.ssy.lingxi.order.service.web.IBuyerOrderService;
import com.ssy.lingxi.order.service.web.IOrderModuleService;
import com.ssy.lingxi.order.service.web.IOrderParamConfigService;
import com.ssy.lingxi.order.service.web.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * App - 采购订单相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-22
 */
@Service
public class MobileBuyerOrderServiceImpl implements IMobileBuyerOrderService {
    @Resource
    private IBaseMobileOrderService baseMobileOrderService;

    @Resource
    private IBuyerOrderService buyerOrderService;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private IOrderModuleService orderModuleService;

    @Resource
    private IMemberDetailFeign memberDetailFeign;
    @Resource
    private IOrderParamConfigService orderParamConfigService;
    @Resource
    private OrderPaymentRepository orderPaymentRepository;

    /**
     * APP-未发货调整订单送货时间
     * @param loginUser 登录用户
     * @param updateVO 接口参数
     * @return 操作结果
     */
    @Override
    public Void updateDeliveryTime(UserLoginCacheDTO loginUser, MobileOrderDeliveryTimeUpdateReq updateVO) {
        OrderDO order = orderRepository.findById(updateVO.getOrderId()).orElse(null);
        if (order == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        //更新订单送货时间
        order.setDeliverPeriod(updateVO.getDeliverPeriod());

        orderRepository.save(order);

        return null;
    }

    /**
     * 获取订单内部、外部状态列表
     *
     * @param loginUser 登录用户
     * @return 查询结果
     */
    @Override
    public MobilePageItemResp getPageItems(UserLoginCacheDTO loginUser) {
        MobilePageItemResp itemVO = new MobilePageItemResp();
        itemVO.setInnerStatus(BuyerInnerStatusEnum.toDropdownList());
        itemVO.setOuterStatus(OrderOuterStatusEnum.toDropdownList());
        return itemVO;
    }

    /**
     * 全部订单 - 分页查询订单
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileOrderManageQueryResp> pageOrders(UserLoginCacheDTO loginUser, MobileManagePageDataReq pageVO) {
        MobileOrderPageDTO result = baseMobileOrderService.pageMobileBuyerOrders(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getKeyword(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getInnerStatus(), pageVO.getOuterStatus(), pageVO.getCurrent(), pageVO.getPageSize(), pageVO.getSaleMode(), pageVO.getInnerStatusList(), loginUser.getBranchId());
        UserIdFeignReq userIdFeignReq = new UserIdFeignReq();
        userIdFeignReq.setUserId(loginUser.getUserId());
        WrapperResp<Boolean> orderAuth = memberDetailFeign.hasOrderAuth(userIdFeignReq);
        if (WrapperUtil.isOk(orderAuth) && !orderAuth.getData()) {
            // 商品单价，订单实付金额，定金，尾款 等信息不对外展示
            result.getOrders().forEach(order -> {
                order.setTotalAmount(new BigDecimal(-1));
                order.getProducts().forEach(product -> product.setRefPrice(new BigDecimal(-1)));
                order.setHasOrderAuth(orderAuth.getData());
            });
        }
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(order -> {
            MobileOrderManageQueryResp queryVO = new MobileOrderManageQueryResp(order);
            // 判断是否有权限
            queryVO.setShowAddToCart(true);
            queryVO.setShowCancelOrder(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode().equals(order.getInnerStatus()));
            queryVO.setShowGoPay(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode().equals(order.getInnerStatus()) ||
                    BuyerInnerStatusEnum.TO_PAY_BALANCE.getCode().equals(order.getInnerStatus()) ||
                    BuyerInnerStatusEnum.BUYER_PAY_NOT_ARRIVED.getCode().equals(order.getInnerStatus()) ||
                    BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode().equals(order.getInnerStatus()) ||
                    BuyerInnerStatusEnum.BALANCE_NOT_ARRIVED.getCode().equals(order.getInnerStatus()));
            queryVO.setShowQueryPickupCode(BuyerInnerStatusEnum.TO_SELF_PICKUP.getCode().equals(order.getInnerStatus()));
            queryVO.setShowEvaluate(BuyerInnerStatusEnum.ACCOMPLISHED.getCode().equals(order.getInnerStatus()));
            queryVO.setShowConfirmReceive(BuyerInnerStatusEnum.TO_RECEIVE.getCode().equals(order.getInnerStatus()));

            if (CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode())) {
                if (ObjectUtil.isNotEmpty(order.getPaidAmount()) && order.getPaidAmount().equals(order.getTotalAmount())) {
                    queryVO.setTotalAmount(NumberUtil.formatAmount(order.getTotalAmount()));
                } else {
                    queryVO.setTotalAmount(NumberUtil.formatAmount(order.getDepositAmount()));
                }
            }

            queryVO.setShowAfterSales(baseMobileOrderService.canOrderAfterSale(order));
            //queryVO.setShowArchive(baseMobileOrderService.canOrderArchive(order));
            queryVO.setShowBuyAgain(baseMobileOrderService.canOrderAgain(order));
            queryVO.setShowCancel(baseMobileOrderService.canOrderCancel(order));
            queryVO.setShowComment(baseMobileOrderService.canOrderComment(order));
            queryVO.setShowConfirmContract(baseMobileOrderService.canOrderConfirmContract(order));
            queryVO.setShowConfirmReception(baseMobileOrderService.canOrderConfirmReception(order));
            queryVO.setShowGradeOne(baseMobileOrderService.canOrderValidateGradeOne(order));
            queryVO.setShowGradeTwo(baseMobileOrderService.canOrderValidateGradeTwo(order));
            queryVO.setShowPay(baseMobileOrderService.canOrderPay(order));
            queryVO.setShowReceive(BuyerInnerStatusEnum.TO_RECEIVE.getCode().equals(order.getInnerStatus()));
            queryVO.setShowReception(baseMobileOrderService.canOrderCreateReception(order));
            queryVO.setShowSubmit(baseMobileOrderService.canOrderSubmit(order));
            queryVO.setShowModifyDeliverTime(baseMobileOrderService.canBuyerModifyDeliverTime(order));
            queryVO.setShowRefund(baseMobileOrderService.canBuyerRefundOrder(order));
            queryVO.setShowDelete(baseMobileOrderService.canBuyerDeleteOrder(order));
            String wechatBrowser = redisUtils.stringGet(loginUser.getMemberId() + "_" + order.getOrderId(), RedisConstant.REDIS_ORDER_INDEX);
            if (StringUtils.hasText(wechatBrowser)) {
                queryVO.setWechatBrowser(Integer.parseInt(wechatBrowser));
            }
            List<OrderPaymentDO> orderPaymentDOS = orderPaymentRepository.findByOrderId(order.getOrderId());
            queryVO.setBatchNo(CollectionUtils.isEmpty(orderPaymentDOS) ? 0 : orderPaymentDOS.stream().filter(payment -> payment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode()) || payment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode()) || (payment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode()) && payment.getOuterStatus().equals(OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode()))).map(OrderPaymentDO::getBatchNo).min(Comparator.comparingInt(Integer::intValue)).orElse(0));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 订单退款
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 退款结果
     */
    @Override
    public Void orderRefund(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        buyerOrderService.orderRefund(loginUser, idVO);
        return null;
    }

    /**
     * 取消订单
     *
     * @param loginUser 登录用户
     * @param reasonVO 接口参数
     * @return 操作结果
     */
    @Transactional
    @Override
    public Void cancelOrder(UserLoginCacheDTO loginUser, OrderReasonReq reasonVO) {
        buyerOrderService.cancelOrder(loginUser, reasonVO);
        return null;
    }

    /**
     * 全部订单 - 分页查询订单 - 订单删除
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 操作结果
     */
    @Override
    public Void pageOrderDelete(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        buyerOrderService.pageOrderDelete(loginUser, idVO);
        return null;
    }

    /**
     * “待审核订单（一级）” - 分页查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileOrderQueryResp> pageToValidateGradeOneOrders(UserLoginCacheDTO loginUser, MobilePageDataReq pageVO) {
        MobileOrderPageDTO result = baseMobileOrderService.pageMobileBuyerOrders(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getKeyword(), BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_ONE.getCode(), null, pageVO.getCurrent(), pageVO.getPageSize(), pageVO.getSaleMode());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(MobileOrderQueryResp::new).collect(Collectors.toList()));
    }

    /**
     * “待审核订单（一级）” - 审核
     *
     * @param loginUser 登录用户
     * @param agreeVO 接口参数
     * @return 审核结果
     */
    @Override
    public Void validateOrderGradeOne(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        buyerOrderService.validateOrderGradeOne(loginUser, agreeVO);
        return null;
    }

    /**
     * “待审核订单（二级）” - 分页查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileOrderQueryResp> pageToValidateGradeTwoOrders(UserLoginCacheDTO loginUser, MobilePageDataReq pageVO) {
        MobileOrderPageDTO result = baseMobileOrderService.pageMobileBuyerOrders(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getKeyword(), BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_TWO.getCode(), null, pageVO.getCurrent(), pageVO.getPageSize(), pageVO.getSaleMode());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(MobileOrderQueryResp::new).collect(Collectors.toList()));
    }

    /**
     * “待审核订单（二级）” - 审核
     *
     * @param loginUser 登录用户
     * @param agreeVO 接口参数
     * @return 审核结果
     */
    @Transactional
    @Override
    public Void validateOrderGradeTwo(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        buyerOrderService.validateOrderGradeTwo(loginUser, agreeVO);
        return null;
    }

    /**
     * “待提交订单” - 分页查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileToSubmitOrderQueryResp> pageToSubmitOrders(UserLoginCacheDTO loginUser, MobilePageDataReq pageVO) {
        MobileOrderPageDTO result = baseMobileOrderService.pageMobileBuyerOrders(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getKeyword(), BuyerInnerStatusEnum.BUYER_TO_SUBMIT.getCode(), null, pageVO.getCurrent(), pageVO.getPageSize(), pageVO.getSaleMode());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(order -> {
            MobileToSubmitOrderQueryResp queryVO = new MobileToSubmitOrderQueryResp(order);
            queryVO.setShowCancel(baseMobileOrderService.canOrderCancel(order));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待提交订单” - 提交
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 审核结果
     */
    @Transactional
    @Override
    public void submitValidateOrder(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        buyerOrderService.submitValidateOrder(loginUser, idVO);
    }

    /**
     * “待支付订单” - 分页查询订单列表
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileOrderQueryResp> pageToPayOrders(UserLoginCacheDTO loginUser, MobilePageDataReq pageVO) {
        MobileOrderPageDTO result = baseMobileOrderService.pageMobileToPayOrders(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getKeyword(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(order -> {
            MobileOrderManageQueryResp queryVO = new MobileOrderManageQueryResp(order);
            queryVO.setShowCancel(baseMobileOrderService.canOrderCancel(order));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待支付订单” - 查询支付方式与支付渠道列表
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public List<MobilePayTypeDetailResp> getToPayOrderPayTypes(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        List<OrderPayTypeDetailResp> payTypeResult = buyerOrderService.getToPayOrderPayTypes(loginUser, idVO);
        
        return payTypeResult.stream().flatMap(payType -> payType.getPayChannels().stream().map(payChannel -> new MobilePayTypeDetailResp(payType.getFundMode(), payType.getPayType(), payType.getPayTypeName(), payChannel.getPayChannel(), payChannel.getPayChannelName()))).sorted(Comparator.comparingInt(MobilePayTypeDetailResp::getPayType).thenComparingInt(MobilePayTypeDetailResp::getPayChannel)).collect(Collectors.toList());
    }

    /**
     * “待支付订单” - 订单支付
     *
     * @param loginUser 登录用户
     * @param payVO   接口参数
     * @return 支付链接（在线支付）
     */
    @Transactional
    @Override
    public OrderPayResultDetailResp orderPay(UserLoginCacheDTO loginUser, BuyerOrderPayReq payVO) {
        return orderModuleService.orderPay(loginUser, payVO);
    }


    /**
     * “待支付订单” - 订单支付
     *
     * @param loginUser 登录用户
     * @param payVO   接口参数
     * @return 支付链接（在线支付）
     */
    @Transactional
    @Override
    public OrderPayResultDetailResp orderPay(UserLoginCacheDTO loginUser, OrderBuyerMergePayReq payVO) {
        return orderModuleService.orderPay(loginUser, payVO);
    }

    /**
     * “待支付订单” - 查询支付结果
     *
     * @param loginUser 登录用户
     * @param resultVO 接口参数
     * @return 支付结果
     */
    @Override
    public BuyerPayResultDetailResp findPayResult(UserLoginCacheDTO loginUser, BuyerPayResultReq resultVO) {
        return buyerOrderService.findPayResult(loginUser, resultVO);
    }

    /**
     * “待发货订单” - 查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileToDeliveryOrderQueryResp> pageToDeliveryOrders(UserLoginCacheDTO loginUser, MobilePageDataReq pageVO) {
        MobileOrderPageDTO result = baseMobileOrderService.pageMobileBuyerOrders(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getKeyword(),  null, OrderOuterStatusEnum.TO_CONFIRM_DELIVERY.getCode(), pageVO.getCurrent(), pageVO.getPageSize(), pageVO.getSaleMode());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(order->{
            MobileToDeliveryOrderQueryResp queryVO = new MobileToDeliveryOrderQueryResp(order);
            queryVO.setShowAfterSales(baseMobileOrderService.canOrderAfterSale(order));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待确认收货订单” -查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileOrderQueryResp> pageToReceiveOrders(UserLoginCacheDTO loginUser, MobilePageDataReq pageVO) {
        MobileOrderPageDTO result = baseMobileOrderService.pageMobileBuyerOrders(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getKeyword(), BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode(), null, pageVO.getCurrent(), pageVO.getPageSize(), pageVO.getSaleMode());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(MobileOrderQueryResp::new).collect(Collectors.toList()));
    }

    /**
     * “待确认收货订单” - 确认收货
     *
     * @param loginUser 登录用户
     * @param receiveVO 接口参数
     * @return 查询结果
     */
    @Override
    public Void receiveOrder(UserLoginCacheDTO loginUser, BuyerReceiveReq receiveVO) {
        buyerOrderService.receiveOrder(loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), loginUser.getMemberRoleName(), receiveVO.getOrderId(), receiveVO.getBatchNo(), receiveVO.getReceiveBill(),receiveVO.getWarehousingOrderProductDetailVOS());
        return null;
    }

    /**
     * 采购订单详情
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public MobileOrderDetailResp findOrderDetail(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        return baseMobileOrderService.findMobileOrderDetail(loginUser, idVO.getOrderId());
    }

    @Override
    public PayOrderInfoResp findOrderByIds(UserLoginCacheDTO sysUser, List<Long> orderIds) {
        List<OrderDO> orderDOS = orderRepository.findAllById(orderIds);
        if(CollectionUtils.isEmpty(orderDOS)){
          throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        orderDOS = orderDOS.stream().filter(orderDO -> sysUser.getMemberId().equals(orderDO.getBuyerMemberId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderDOS)){
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        //获取过期时间最短的订单
        LocalDateTime minPayTimeOut = orderDOS.stream().map(OrderDO::getPayTimeout).filter(time -> time != null).min(Comparator.comparing(LocalDateTime::toLocalTime)).orElse(null);
        //LocalDateTime minPayTimeOut = orderDOS.stream().map(OrderDO::getPayTimeout).filter(time -> time != null && getPayOrderInfoReq.getBatchNo() == 1).min(Comparator.comparing(LocalDateTime::toLocalTime)).orElse(null);

        OrderDepositResp orderDepositConfig = orderParamConfigService.getOrderDepositConfig();
        PayOrderInfoResp payOrderInfoResp = new PayOrderInfoResp();
        payOrderInfoResp.setPayTimeout(minPayTimeOut);
        if(orderDepositConfig != null){
            payOrderInfoResp.setServiceCharge(orderDepositConfig.getServiceCharge());
        }
        payOrderInfoResp.setNumber(orderDOS.size());
        payOrderInfoResp.setGoldWeight(orderDOS.stream().map(OrderDO::getTotalWeight).reduce(BigDecimal.ZERO, BigDecimal::add));
        return payOrderInfoResp;
    }
}
