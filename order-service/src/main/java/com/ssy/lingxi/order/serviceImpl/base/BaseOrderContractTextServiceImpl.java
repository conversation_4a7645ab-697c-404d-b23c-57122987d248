//package com.ssy.lingxi.order.serviceImpl.base;
//
//import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
//import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
//import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
//import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
//import com.ssy.lingxi.component.base.model.BusinessException;
//import com.ssy.lingxi.contract.api.model.resp.SignCreateResp;
//import com.ssy.lingxi.order.api.model.req.OrderContractTextUpdateSignReq;
//import com.ssy.lingxi.order.common.enums.VendorInnerStatusEnum;
//import com.ssy.lingxi.order.entity.OrderContractFileDO;
//import com.ssy.lingxi.order.entity.OrderDO;
//import com.ssy.lingxi.order.model.req.buyer.BuyerOrderContractTextUpdateReq;
//import com.ssy.lingxi.order.model.req.buyer.OrderContractTextReq;
//import com.ssy.lingxi.order.repository.OrderContractTextRepository;
//import com.ssy.lingxi.order.service.base.IBaseOrderContractTextService;
//import com.ssy.lingxi.order.service.feign.IContractFeignService;
//import com.ssy.lingxi.order.util.OrderProcessTaskUtil;
//import com.ssy.lingxi.purchase.api.common.enums.purchase.PurchaseStatusEnum;
//import org.springframework.data.jpa.domain.Specification;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.util.CollectionUtils;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.Objects;
//import java.util.Optional;
//
///**
// * 基础-订单电子合同文本服务实现
// * <AUTHOR>
// * @since 2022/6/29 15:02
// */
//@Service
//public class BaseOrderContractTextServiceImpl implements IBaseOrderContractTextService {
//
//    @Resource
//    private OrderContractTextRepository repository;
//
//    @Resource
//    private IContractFeignService contractFeignService;
//
//    @Override
//    public void checkOrderContractText(OrderDO order, BuyerOrderContractTextUpdateReq contractText, boolean isCreate) {
//        // step 1: 校验srm订单节点是否有确认合同流程,
//        // 如果有合同确认流程节点，那合同文本信息必填
//        if (OrderProcessTaskUtil.hasSrmContractConfirmationNode(order) && Objects.isNull(contractText)) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_ELECTRONIC_CONTRACT_INFO_NOT_EXIST);
//        }
//
//        // step 2: 删除就电子合同
//        if (!isCreate) {
//            deleteContractTextByOrder(order);
//        }
//        // step 3: 保存电子文本
//        if (Objects.nonNull(contractText)) {
//            OrderContractFileDO entity = builderOrderContractTextDO(order, contractText);
//            repository.saveAndFlush(entity);
//            order.setContractText(entity);
//        }
//    }
//
//    /**
//     * 删除电子合同
//     * @param order 订单信息
//     */
//    private void deleteContractTextByOrder(OrderDO order) {
//        // step 1: 校验删除，当前电子合同没签署可以删除
//        OrderContractFileDO contractText = order.getContractText();
//        if (Optional.ofNullable(contractText).map(OrderContractFileDO::getSignatureLogId).map(map -> !Objects.equals(map, 0L)).orElse(false)) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_ELECTRONIC_CONTRACT_SIGNED);
//        }
//        // step 2: 查询可删除的电子合同信息删除
//        Specification<OrderContractFileDO> spec = (root, query, builder) -> query.where(builder.equal(root.get("order").as(OrderDO.class), order), builder.notEqual(root.get("signatureLogId").as(Long.class), 0L)).getRestriction();
//        List<OrderContractFileDO> entities = repository.findAll(spec);
//        if (!CollectionUtils.isEmpty(entities)) {
//            repository.deleteAll(entities);
//        }
//    }
//
//    /**
//     * 构建新增电子合同文本保存信息实体
//     * @param order 订单信息
//     * @param contractText 电子合同信息
//     * @return 构建对象
//     */
//    private OrderContractFileDO builderOrderContractTextDO(OrderDO order, BuyerOrderContractTextUpdateReq contractText) {
//
//        OrderContractFileDO target = Objects.isNull(order.getContractText()) ? new OrderContractFileDO() : order.getContractText();
//        target.setOrder(order);
//        target.setOrderNO(order.getOrderNo());
//        target.setTemplateId(contractText.getTemplateId());
//        target.setIsUseElectronicContract(contractText.getIsUseElectronicContract());
//        target.setContractName(contractText.getContractName());
//        target.setContractUrl(contractText.getContractUrl());
//        return target;
//    }
//
//    @Override
//    public OrderContractTextReq getOrderContractText(OrderDO order) {
//       return Optional.ofNullable(order.getContractText()).map(map -> {
//            OrderContractTextReq target  = new OrderContractTextReq();
//            target.setId(map.getId());
//            target.setTemplateId(map.getTemplateId());
//            target.setIsUseElectronicContract(map.getIsUseElectronicContract());
//            target.setContractName(map.getContractName());
//            target.setContractUrl(map.getContractUrl());
//            return target;
//        }).orElse(new OrderContractTextReq(PurchaseStatusEnum.NO.getState()));
//    }
//
//    @Override
//    @Transactional
//    public void toSubmitOrderSaveContractText(OrderDO order, BuyerOrderContractTextUpdateReq contractText) {
//        // step 1：校验订单， 外部状态待提交， 且工作流有确认合同节点
//        Optional.ofNullable(order).filter(f -> Objects.equals(VendorInnerStatusEnum.VENDOR_TO_SUBMIT_VALIDATE.getCode(), f.getVendorInnerStatus()) && OrderProcessTaskUtil.hasB2bContractConfirmationNode(f)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST));
//
//        // step 2: 删除原有电子合同
//        deleteContractTextByOrder(order);
//
//        // step 3: 保存新的电子合同
//        OrderContractFileDO newEntity = builderOrderContractTextDO(order, contractText);
//        repository.saveAndFlush(newEntity);
//        order.setContractText(newEntity);
//    }
//
//    @Override
//    public void contractConfirm(OrderDO order, UserLoginCacheDTO loginUser) {
//
//        // step 1: 判断当前订单是否存在合同确认流程
//        if(!OrderProcessTaskUtil.hasContractConfirmationNode(order)) {
//            return;
//        }
////        OrderContractTextDO entity = Optional.ofNullable(order.getContractText()).orElseThrow(() -> new BusinessException(ResponseCode.ORDER_ELECTRONIC_CONTRACT_DOES_NOT_EXIST));
//        OrderContractFileDO entity = order.getContractText();
//        if (Objects.isNull(entity)) {
//            return;
//        }
//        // step 2: 是否使用电子合同
//        if (Objects.equals(CommonBooleanEnum.NO.getCode(), entity.getIsUseElectronicContract())) {
//            return;
//        }
//        // step 3: 调用合同签署服务
//        OrderTypeEnum orderTypeEnum = OrderTypeEnum.parse(order.getOrderType());
//        switch (orderTypeEnum) {
//            case REQUISITION_TO_PURCHASE: // 请购单采购
//            case REQUISITION_CONTRACT: // 请购单合同
//            case QUERY_PRICE_CONTRACT: // 采购询价合同
//            case PURCHASE_CONTRACT_BIDDING: // 采购招标合同
//            case PRICE_COMPETITION_CONTRACT_PURCHASE: // 采购竞价合同
//            case FRAME_CONTRACT_ORDER: // 框架合同订单
//            case MANUAL_MATERIAL_ORDER_PLACEMENT: // 手工物料订单
//            case MATERIAL_SAMPLE_ORDER: // 物料样品订单
//                srmOrderContractSign(order,loginUser, entity);
//            case SPOT_PURCHASING: // 现货采购
//            case CHANNEL_STRAIGHT_MINING: // 渠道直采
//            case CHANNEL_SPOT: // 渠道现货
//            case INQUIRY_TO_PURCHASE: // 询价采购
//            case CHANNEL_POINT_EXCHANGE: // 渠道积分兑换
//            case COMMERCE_IMPORT: // 跨境电商进口
//                b2bOrderContractSign(order,loginUser, entity);
//            default:
//                throw new BusinessException(ResponseCodeEnum.ORDER_ELECTRONIC_CONTRACT_ORDER_TYPE_NOT_SUPPORT);
//        }
//
//    }
//
//    @Override
//    @Transactional
//    public Void updateOrderContractTextUrl(OrderContractTextUpdateSignReq requestVO) {
//        // step 1: 查询电子合同文本信息
//        Specification<OrderContractFileDO> spec = (root, query, build) -> build.equal(root.get("signatureLogId").as(Long.class), requestVO.getSignatureLogId());
//        OrderContractFileDO entity = repository.findOne(spec).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ORDER_ELECTRONIC_CONTRACT_DOES_NOT_EXIST));
//
//        // step 2: 更新电子合同文本信息
//        entity.setOldContractUrl(entity.getContractUrl());
//        entity.setContractUrl(requestVO.getSaveFileUrl());
//        repository.saveAndFlush(entity);
//        return null;
//    }
//
//    /**
//     * b2b订单合同签署
//     * @param order 订单信息
//     * @param loginUser 操作用户
//     * @param entity 电子合同文本
//     */
//    private void b2bOrderContractSign(OrderDO order, UserLoginCacheDTO loginUser, OrderContractFileDO entity) {
//        // step 1： 调用合同服务一键1签署
//        SignCreateResp signCreateResponseWrapperResp = contractFeignService.srmOrderContractSign(order, loginUser);
//        // step 2: 更新电子合同文本信息
//        entity.setSignatureLogId(Optional.ofNullable(signCreateResponseWrapperResp).map(SignCreateResp::getSignatureLogId).orElse(0L));
//        repository.saveAndFlush(entity);
//    }
//
//    /**
//     * srm订单合同签署
//     * @param order 订单信息
//     * @param loginUser 操作用户
//     * @param entity 电子合同文本信息
//     */
//    private void srmOrderContractSign(OrderDO order, UserLoginCacheDTO loginUser, OrderContractFileDO entity) {
//        // step 1: 调用合同服务一键签署
//        SignCreateResp signCreateResponseWrapperResp = contractFeignService.srmOrderContractSign(order, loginUser);
//
//        // step 2: 更新电子合同文本信息
//        entity.setSignatureLogId(Optional.ofNullable(signCreateResponseWrapperResp).map(SignCreateResp::getSignatureLogId).orElse(0L));
//        repository.saveAndFlush(entity);
//    }
//}
