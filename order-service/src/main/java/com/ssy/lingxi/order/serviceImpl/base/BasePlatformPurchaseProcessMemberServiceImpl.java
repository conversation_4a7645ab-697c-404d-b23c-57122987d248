package com.ssy.lingxi.order.serviceImpl.base;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.CollectionPageUtil;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.order.entity.PlatformPurchaseProcessDO;
import com.ssy.lingxi.order.entity.PlatformPurchaseProcessMemberDO;
import com.ssy.lingxi.order.model.dto.OrderMemberQueryDTO;
import com.ssy.lingxi.order.model.req.basic.OrderMemberIdAndRoleIdReq;
import com.ssy.lingxi.order.repository.PlatformPurchaseProcessMemberRepository;
import com.ssy.lingxi.order.service.base.IBasePlatformPurchaseProcessMemberService;
import com.ssy.lingxi.order.service.feign.IMemberFeignService;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 平台后台 - 采购流程关联的会员相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-27
 */
@Service
public class BasePlatformPurchaseProcessMemberServiceImpl implements IBasePlatformPurchaseProcessMemberService {
    @Resource
    private PlatformPurchaseProcessMemberRepository platformPurchaseProcessMemberRepository;

    @Resource
    private IMemberFeignService memberFeignService;

    /**
     * 校验、新增采购流程关联的会员
     *
     * @param purchaseProcess 采购流程
     * @param allMembers      是否适用于所有会员
     * @param memberList      会员列表
     */
    @Override
    public void checkMembers(PlatformPurchaseProcessDO purchaseProcess, Boolean allMembers, List<OrderMemberIdAndRoleIdReq> memberList) {
        if(allMembers) {
            purchaseProcess.setMembers(new HashSet<>());
            return;
        }

        List<PlatformPurchaseProcessMemberDO> members = memberList.stream().map(memberVO -> {
            PlatformPurchaseProcessMemberDO processMember = new PlatformPurchaseProcessMemberDO();
            processMember.setProcess(purchaseProcess);
            processMember.setMemberId(memberVO.getMemberId());
            processMember.setRoleId(memberVO.getRoleId());
            return processMember;
        }).collect(Collectors.toList());

        platformPurchaseProcessMemberRepository.saveAll(members);

        purchaseProcess.setMembers(new HashSet<>(members));
    }

    /**
     * 分页查询采购流程关联的会员列表
     *
     * @param purchaseProcess 采购流程
     * @param name            会员名称
     * @param current         当前页
     * @param pageSize        每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderMemberQueryDTO> pageMembers(PlatformPurchaseProcessDO purchaseProcess, String name, int current, int pageSize) {
        if(purchaseProcess.getAllMembers()) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        List<PlatformPurchaseProcessMemberDO> members = platformPurchaseProcessMemberRepository.findByProcess(purchaseProcess, Sort.by("id").ascending());
        if(CollectionUtils.isEmpty(members)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //从会员服务查询
        List<MemberFeignReq> feignList = members.stream().map(member -> {
            MemberFeignReq feignVO = new MemberFeignReq();
            feignVO.setMemberId(member.getMemberId());
            feignVO.setRoleId(member.getRoleId());
            return feignVO;
        }).collect(Collectors.toList());

        List<OrderMemberQueryDTO> feignResult = memberFeignService.findPlatformMembers(name, feignList);

        if(CollectionUtils.isEmpty(feignResult)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        return new PageDataResp<>((long) feignResult.size(), CollectionPageUtil.pageList(feignResult, current, pageSize));
    }

    /**
     * 校验、修改采购流程关联的会员
     *
     * @param purchaseProcess 采购流程
     * @param allMembers      是否适用于所有会员
     * @param memberList      会员列表
     */
    @Override
    public void updateMembers(PlatformPurchaseProcessDO purchaseProcess, Boolean allMembers, List<OrderMemberIdAndRoleIdReq> memberList) {
        if(allMembers) {
            platformPurchaseProcessMemberRepository.deleteByProcess(purchaseProcess);
            purchaseProcess.setMembers(new HashSet<>());
            return;
        }

        //先删除再保存
        platformPurchaseProcessMemberRepository.deleteByProcess(purchaseProcess);

        List<PlatformPurchaseProcessMemberDO> members = memberList.stream().map(memberVO -> {
            PlatformPurchaseProcessMemberDO processMember = new PlatformPurchaseProcessMemberDO();
            processMember.setProcess(purchaseProcess);
            processMember.setMemberId(memberVO.getMemberId());
            processMember.setRoleId(memberVO.getRoleId());
            return processMember;
        }).collect(Collectors.toList());

        platformPurchaseProcessMemberRepository.saveAll(members);

        purchaseProcess.setMembers(new HashSet<>(members));
    }
}
