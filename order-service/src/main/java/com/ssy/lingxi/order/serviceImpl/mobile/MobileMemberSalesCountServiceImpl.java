package com.ssy.lingxi.order.serviceImpl.mobile;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ObjectUtil;
import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.member.api.feign.IMemberAbilitySalesFeign;
import com.ssy.lingxi.member.api.feign.IMobileMemberSalesFeign;
import com.ssy.lingxi.member.api.model.req.MemberSalesFindUserIdReq;
import com.ssy.lingxi.member.api.model.resp.MemberSalesFeignPageQueryResp;
import com.ssy.lingxi.order.entity.QOrderDO;
import com.ssy.lingxi.order.entity.QOrderPaymentDO;
import com.ssy.lingxi.order.entity.QOrderProductDO;
import com.ssy.lingxi.order.enums.OrderOuterStatusEnum;
import com.ssy.lingxi.order.model.req.mobile.MobileWechatAppletBindMemberOrderCountDataReq;
import com.ssy.lingxi.order.model.resp.basic.OrderAmountResp;
import com.ssy.lingxi.order.model.resp.mobile.MobileAchievementCountQueryResp;
import com.ssy.lingxi.order.model.resp.mobile.MobileMemberSalesCountQueryResp;
import com.ssy.lingxi.order.model.resp.mobile.MobileMemberSalesOrderCountQueryResp;
import com.ssy.lingxi.order.model.resp.mobile.MobileWechatAppletAchievementCountResp;
import com.ssy.lingxi.order.service.base.IBaseCacheService;
import com.ssy.lingxi.order.service.mobile.IMobileMemberSalesCountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务员小程序-业绩统计相关接口
 *
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-24
 */
@Service
public class MobileMemberSalesCountServiceImpl implements IMobileMemberSalesCountService {
    private final static Logger logger = LoggerFactory.getLogger(MobileMemberSalesCountServiceImpl.class);

    @Resource
    IMobileMemberSalesFeign mobileMemberSalesFeign;

    @Resource
    IMemberAbilitySalesFeign memberAbilitySalesFeign;

    @Resource
    IBaseCacheService baseCacheService;

    @Resource
    JPAQueryFactory jpaQueryFactory;

    /**
     * 根据业务员Id查询业绩并进行统计返回
     *
     * @param loginUser 登录用户
     * @return 返回符合条件的业务员
     */
    @Override
    public MobileMemberSalesOrderCountQueryResp orderCountWechatApplet(UserLoginCacheDTO loginUser) {
        // 首页业绩统计主要是统计全部已经完成的金额和本月的金额
        // 步骤1：计算该业务员的全部订单总金额。根据业务员Id，订单类型，用户角色为消费者且订单状态为已完成这四个查询条件，找到所有订单，订单完成总金额 = 已经支付 - 已经退款
        // 步骤2：计算全部下级会员数。根据登录的会员Id，角色Id和业务员Id 找到该业务员下面绑定的所有下级会员，将下级会员数统计到memberAllQuantity这个字段里
        // 步骤3：计算本月的应付金额和已付金额。根据当前系统的时间，得到当月的开始时间1号0点和结束时间最后一天的最后一刻
        // 然后根据业务员Id，订单类型，角色为消费者，开始时间和结束时间对应的订单提交时间，找到本月符合条件的订单进行统计
        // 步骤4：计算本月的完成金额。本月订单状态为已完成，本月完成金额 = 订单已付金额 - 订单退款金额
        // 角色为消费者的Ids
        List<Long> roles = memberAbilitySalesFeign.roleIds().getData();
        // 订单类型
        List<Integer> orderTypes = orderTypes();
        // 本月的开始时间和结束时间
        LocalDateTime startTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime endTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).with(TemporalAdjusters.lastDayOfMonth());

        MobileMemberSalesOrderCountQueryResp queryVO = new MobileMemberSalesOrderCountQueryResp();

        // 步骤1：计算该业务员的全部订单总金额，订单完成总金额 = 已经支付 - 已经退款
        Long userId = loginUser.getUserId();
        queryVO.setOrderFinishAllAmount(getAllFinishedAmount(userId, orderTypes, roles));

        // 步骤2：计算全部下级会员数
        queryVO.setMemberAllQuantity(countMember(loginUser.getMemberId(), loginUser.getMemberRoleId(), userId));

        // 步骤3：计算本月的应付金额和已付金额
        List<OrderAmountResp> orderAmountLists = paidAmount(userId, roles, orderTypes, startTime, endTime);
        ArrayList<OrderAmountResp> orderAmountList = orderAmountLists.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(OrderAmountResp::getOrderId))), ArrayList::new));
        // 本月的下单金额 -- 一提交订单就统计
        queryVO.setOrderAmount(orderAmountList.stream().map(OrderAmountResp::getTotalAmount).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add));
        // 本月的已收款金额 -- 一确认钱到账就统计
        BigDecimal amountPaid = orderAmountList.stream().map(OrderAmountResp::getPaidAmount).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add);
        queryVO.setOrderPaidAmount(amountPaid);

        // 步骤4：计算本月的完成金额，订单状态为已完成
        queryVO.setOrderFinishAmount(getMothFinishedAmount(userId, orderTypes, roles, startTime, endTime));

        return queryVO;
    }

    /**
     * 业务员小程序 - 查看绑定会员订单统计
     *
     * @param loginUser 登录用户
     * @param orderCountVO 搜索时间
     * @return 返回会员订单统计列表
     */
    @Override
    public PageDataResp<MobileMemberSalesCountQueryResp> bindMemberOrderCountWechatApplet(UserLoginCacheDTO loginUser, MobileWechatAppletBindMemberOrderCountDataReq orderCountVO) {
        // 根据登录的业务员Id找到所有该业务员的下级会员，再根据业务员Id，下级会员Id和角色Id,找到符合条件的订单并进行统计
        // 步骤1：根据业务员Id找到所有下级会员
        // 步骤2：根据下级会员信息找到所有订单
        // 步骤3：对得到的订单进行统计。其中订单状态为已取消的金额不计算，有总金额 已付金额 退款金额。下单数量要把已经取消的订单也算进去

        // 拼接时间，得到选择的月开始时间和月结束时间
        LocalDate selectTime = LocalDate.parse(orderCountVO.getCountTime().concat("-01"), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDateTime startTime = LocalDateTime.of(selectTime, LocalTime.MIN).with(TemporalAdjusters.firstDayOfMonth());
        LocalDateTime endTime = LocalDateTime.of(selectTime, LocalTime.MAX).with(TemporalAdjusters.lastDayOfMonth());

        // 步骤1：根据业务员Id找到所有下级会员，对下级会员进行分页查询
        MemberSalesFindUserIdReq userIdVO = new MemberSalesFindUserIdReq(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserId(), orderCountVO.getMemberName(), endTime, orderCountVO.getCurrent(), orderCountVO.getPageSize());
        WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> userWrapperResp = mobileMemberSalesFeign.getSalesList(userIdVO);
        if (ObjectUtil.isNull(userWrapperResp.getData()) || CollectionUtils.isEmpty(userWrapperResp.getData().getData())) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }
        List<MemberSalesFeignPageQueryResp> memberList = userWrapperResp.getData().getData();

        // 订单类型
        List<Integer> orderTypes = orderTypes();

        // 步骤2：根据下级会员信息找到所有订单
        PageDataResp<MobileMemberSalesCountQueryResp> queryPageDataRespVO = orderCount(memberList, loginUser.getUserId(), orderTypes, startTime, endTime, orderCountVO.getMemberName(), 0);
        PageDataResp<MobileMemberSalesCountQueryResp> orderQuantityVO = orderCount(memberList, loginUser.getUserId(), orderTypes, startTime, endTime, orderCountVO.getMemberName(), null);

        // 步骤3：对得到的订单进行统计，并对没有订单的下级会员赋初始值
        List<MobileMemberSalesCountQueryResp> result = memberList.stream().map(member -> {
            MobileMemberSalesCountQueryResp viewVO = new MobileMemberSalesCountQueryResp();
            // 符合条件的绑定会员订单统计--计算取消订单的数据
            List<MobileMemberSalesCountQueryResp> countQueryVO = queryPageDataRespVO.getData().stream().filter(queryVO -> member.getMemberId().equals(queryVO.getMemberId()) && member.getMemberRoleId().equals(queryVO.getMemberRoleId())).collect(Collectors.toList());
            // 计算不取消的订单数量
            List<MobileMemberSalesCountQueryResp> countQuery = orderQuantityVO.getData().stream().filter(queryVO -> member.getMemberId().equals(queryVO.getMemberId()) && member.getMemberRoleId().equals(queryVO.getMemberRoleId())).collect(Collectors.toList());
            // 会员Id
            viewVO.setMemberId(member.getMemberId());
            // 会员名字
            viewVO.setMemberName(member.getMemberName());
            // 会员角色Id
            viewVO.setMemberRoleId(member.getMemberRoleId());
            // 会员角色名称
            viewVO.setRoleName(member.getRoleName());

            if (!CollectionUtils.isEmpty(countQueryVO)) {
                // 总下单数
                ArrayList<MobileMemberSalesCountQueryResp> orderCount = countQuery.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MobileMemberSalesCountQueryResp::getOrderCount))), ArrayList::new));
                viewVO.setOrderCount((long) orderCount.size());
                // 总金额
                viewVO.setAmountPayable(countQueryVO.stream().map(MobileMemberSalesCountQueryResp::getAmountPayable).reduce(new BigDecimal(0), BigDecimal::add));
                // 已付金额
                viewVO.setAmountPaid(countQueryVO.stream().map(MobileMemberSalesCountQueryResp::getAmountPaid).reduce(new BigDecimal(0), BigDecimal::add));
                // 退款金额
                viewVO.setRefundAmount(countQueryVO.stream().map(MobileMemberSalesCountQueryResp::getRefundAmount).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add));
                // 支付次数
                viewVO.setPayCount(0);
            } else {
                // 对没有订单数据的会员赋初始值
                viewVO.setOrderCount(0L);
                viewVO.setAmountPayable(new BigDecimal(0));
                viewVO.setAmountPaid(new BigDecimal(0));
                viewVO.setRefundAmount(new BigDecimal(0));
                viewVO.setPayCount(0);
            }
            return viewVO;
        }).sorted(Comparator.comparing(MobileMemberSalesCountQueryResp::getMemberId).reversed()).collect(Collectors.toList());

        return new PageDataResp<>(userWrapperResp.getData().getTotalCount(), result);
    }

    /**
     * 查看业务员个人业绩
     *
     */
    @Override
    public PageDataResp<MobileAchievementCountQueryResp> personalAchievement(UserLoginCacheDTO loginUser, PageDataReq pageDataReq) {
        // 根据业务员Id对订单数据按月份分组统计
        // 步骤1：按月分页，计算开始月份和结束月份。根据分页信息判断是否从当前月开始往后统计的，如果不是的话，需要把开始月份计算出来
        // 步骤2：按月份在订单中查找全部符合条件的数据
        // 步骤3：计算总条数。当前月份的时间-订单开始月份
        // 步骤4：根据月份和查询条件找到所有订单，将没有业绩在月份计算出来，合并到全部业绩月份里
        // 步骤5：对订单数据按月进行分组统计，没有业绩在月份赋初始值0

        // 步骤1：按月分页，计算开始月份和结束月份
        LocalDateTime startTime = getMonth(pageDataReq.getCurrent(), pageDataReq.getPageSize(), true);
        LocalDateTime endTime = getMonth(pageDataReq.getCurrent(), pageDataReq.getPageSize(), false);

        // 得到角色为消费者的Ids
        List<Long> roleIds = memberAbilitySalesFeign.roleIds().getData();

        // 根据业务员Id找到下级会员信息
        MemberSalesFindUserIdReq userIdVO = new MemberSalesFindUserIdReq(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserId(), null, null, null, null);
        WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> userWrapperResp = mobileMemberSalesFeign.getSalesList(userIdVO);

        return updatePersonalAchievementNow(userWrapperResp, roleIds, loginUser.getUserId(), startTime, endTime);
    }

    /**
     * 首页统计
     */
    private PageDataResp<MobileMemberSalesCountQueryResp> indexOrderCount(Long userId, List<Integer> orderTypes, List<Long> roleIds, LocalDateTime startTime, LocalDateTime endTime) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        JPAQuery<MobileMemberSalesCountQueryResp> query = jpaQueryFactory.select(Projections.constructor(MobileMemberSalesCountQueryResp.class, qOrder.id, qOrder.totalAmount, qOrder.paidAmount, qOrderProduct.refReturnAmount.sum()))
                .from(qOrder)
                .leftJoin(qOrderProduct).on(qOrderProduct.order.id.eq(qOrder.id))
                .where(qOrder.vendorUserId.eq(userId))
                .where(qOrder.orderType.in(orderTypes))
                .where(qOrder.buyerRoleId.in(roleIds))
                .where(qOrder.outerStatus.eq(OrderOuterStatusEnum.ACCOMPLISHED.getCode()))
                .groupBy(qOrder.id, qOrder.totalAmount, qOrder.paidAmount);

        if (ObjectUtil.isNotNull(startTime) && ObjectUtil.isNotNull(endTime)) {
            query.where(qOrder.finishTime.after(startTime).and(qOrder.finishTime.before(endTime)));
        }
        //总数
        long totalCount = query.fetch().size();
        List<MobileMemberSalesCountQueryResp> result = query.fetch();
        return new PageDataResp<>(totalCount, result);
    }

    /**
     * 绑定会员订单统计
     */
    private PageDataResp<MobileMemberSalesCountQueryResp> orderCount(List<MemberSalesFeignPageQueryResp> memberList, Long userId, List<Integer> orderTypes, LocalDateTime startTime, LocalDateTime endTime, String memberName, Integer orderStatus) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        //拼接采购商会员Id、角色Id
        com.querydsl.core.types.Predicate[] predicates = memberList.stream().map(member -> qOrder.buyerMemberId.eq(member.getMemberId()).and(qOrder.buyerRoleId.eq(member.getMemberRoleId()))).toArray(com.querydsl.core.types.Predicate[]::new);

        JPAQuery<MobileMemberSalesCountQueryResp> query = jpaQueryFactory.select(Projections.constructor(MobileMemberSalesCountQueryResp.class, qOrder.buyerMemberId, qOrder.buyerMemberName, qOrder.buyerRoleId, qOrder.id, qOrder.totalAmount, qOrder.paidAmount, qOrderProduct.refReturnAmount.sum()))
                .from(qOrder)
                .leftJoin(qOrderProduct).on(qOrderProduct.order.id.eq(qOrder.id))
                .where(ExpressionUtils.anyOf(predicates))
                .where(qOrder.vendorUserId.eq(userId))
                .where(qOrder.orderType.in(orderTypes))
                .groupBy(qOrder.buyerMemberId, qOrder.buyerMemberName, qOrder.buyerRoleId, qOrder.id, qOrder.totalAmount, qOrder.paidAmount);

        if (ObjectUtil.isNotNull(orderStatus)) {
            query.where(qOrder.outerStatus.notIn(101));
        }
        if (ObjectUtil.isNotNull(startTime) && ObjectUtil.isNotNull(endTime)) {
            query.where(qOrder.submitTime.after(startTime).and(qOrder.submitTime.before(endTime)));
        }

        if (StringUtils.hasLength(memberName)) {
            query.where(qOrder.buyerMemberName.like("%" + memberName.trim() + "%"));
        }
        //总数
        long totalCount = query.fetch().size();

        List<MobileMemberSalesCountQueryResp> result = query.orderBy(qOrder.buyerMemberId.asc()).fetch();

        return new PageDataResp<>(totalCount, result);
    }

    /**
     * 业务员个人业绩更新
     *
     * @param userWrapperResp 下级会员信息
     * @param roleIds     角色类型
     * @param userId      业务员Id
     * @param startTime   开始月份
     * @param endTime     结束月份
     * @return 根据月份进行分组统计每月的业绩
     */
    private PageDataResp<MobileAchievementCountQueryResp> updatePersonalAchievementNow(WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> userWrapperResp, List<Long> roleIds, Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        // 订单类型
        List<Integer> orderTypes = orderTypes();

        // 步骤2：按月份在订单中查找全部符合条件的数据
        // 不包含订单状态为取消的--下单会员数，应付金额，已付金额，退款金额
        List<MobileWechatAppletAchievementCountResp> resultCount = getPersonalAchievement(orderTypes, roleIds, userId, startTime, endTime, 0);
        // 包含订单状态为取消的--订单数量、商品数量、品类数量
        List<MobileWechatAppletAchievementCountResp> resultCounts = getPersonalAchievement(orderTypes, roleIds, userId, startTime, endTime, null);
        // 总条数
        List<MobileWechatAppletAchievementCountResp> totalCounts = getPersonalAchievement(orderTypes, roleIds, userId, null, null, null);

        // 步骤3：计算总条数。当前月份的时间-订单开始月份，如果没有一条订单，对当前月赋初始值并返回出去
        long totalCount;
        Optional<Integer> startOrderMonth = totalCounts.stream().map(MobileWechatAppletAchievementCountResp::getYear).filter(ObjectUtil::isNotNull).distinct().sorted().findFirst();
        if (startOrderMonth.isPresent()) {
            // 订单开始时间，Integer类型202204
            int startOrderMonthInteger = startOrderMonth.get();
            LocalDate orderStartMonth = LocalDate.of(startOrderMonthInteger / 100, startOrderMonthInteger % 100, 1);
            // 相差月份
            totalCount = orderStartMonth.until(LocalDate.now(), ChronoUnit.MONTHS) + 1;
        } else {
            // 没有数据则构建空的集合返回
            List<MobileAchievementCountQueryResp> resultVO = new ArrayList<>();
            resultVO.add(buildAchievementCountVO(LocalDate.now().withDayOfMonth(1), userId));
            totalCount = 1L;
            return new PageDataResp<>(totalCount, resultVO);
        }

        // 步骤4：根据月份和查询条件找到所有订单，将没有业绩的月份计算出来，合并到全部业绩月份里
        List<MobileWechatAppletAchievementCountResp> notInMonth = monthsHandle(resultCounts, startTime.toLocalDate(), endTime.toLocalDate(), startOrderMonth.get(), userId);
        resultCounts.addAll(notInMonth);

        // 步骤5：对订单数据按月进行分组统计，没有业绩的月份赋初始值0，转成前端接收的VO
        String day = "01";
        List<MobileAchievementCountQueryResp> queryVO = resultCounts.stream().filter(ObjectUtil::isNotNull).collect(Collectors.groupingBy(MobileWechatAppletAchievementCountResp::getYear)).entrySet().stream().map(entry -> {
            Integer key = entry.getKey();
            // 这个月--不包含订单状态为取消的--下单会员数，应付金额，已付金额，退款金额
            List<MobileWechatAppletAchievementCountResp> values = resultCount.stream().filter(c -> key.equals(c.getYear())).filter(ObjectUtil::isNotNull).collect(Collectors.toList()); //
            // 月统计时间
            LocalDate month = LocalDate.parse(key.toString().substring(0, 4) + "-" + key.toString().substring(4) + "-" + day, DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));

            // 构建返回的VO
            MobileAchievementCountQueryResp vo = new MobileAchievementCountQueryResp();
            vo.setMonthStatistical(month);
            // 这个月--包含订单状态为取消的--订单次数、商品数量、品类数量
            List<MobileWechatAppletAchievementCountResp> numbers = entry.getValue();
            vo.setMemberUserId(userId);
            if (CollectionUtils.isEmpty(numbers)) {
                vo.setMemberCount(0L);
                vo.setCategoryCount(0L);
                vo.setOrderCount(0L);
                vo.setCommodityCount(0L);
                vo.setAmountPaid(BigDecimal.ZERO);
                vo.setAmountPayable(BigDecimal.ZERO);
                vo.setRefundAmount(BigDecimal.ZERO);
            } else {
                // 订单次数
                vo.setOrderCount(numbers.stream().map(MobileWechatAppletAchievementCountResp::getOrderId).filter(ObjectUtil::isNotNull).distinct().count());
                // 订单商品种类数量统计
                vo.setCommodityCount(numbers.stream().map(MobileWechatAppletAchievementCountResp::getCommodityId).filter(ObjectUtil::isNotNull).distinct().count());
                // 商品品类种类数量
                List<String> categoryCount = numbers.stream().map(MobileWechatAppletAchievementCountResp::getCategory).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
                vo.setCategoryCount((long) categoryCount.size());
                if (CollectionUtils.isEmpty(resultCount)) {
                    vo.setMemberCount(0L);
                    vo.setAmountPaid(BigDecimal.ZERO);
                    vo.setAmountPayable(BigDecimal.ZERO);
                    vo.setRefundAmount(BigDecimal.ZERO);
                } else {
                    // 下单会员数
                    ArrayList<MobileWechatAppletAchievementCountResp> memberCount = values.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(order -> order.getBuyMemberId() + ";" + order.getBuyMemberRoleId()))), ArrayList::new));
                    if (ObjectUtil.isNull(userWrapperResp.getData()) || ObjectUtil.isNull(userWrapperResp.getData().getData())) {
                        vo.setMemberCount(0L);
                    } else {
                        List<MemberSalesFeignPageQueryResp> memberUserList = userWrapperResp.getData().getData();
                        List<MemberSalesFeignPageQueryResp> count = memberUserList.stream().filter(v1 -> memberCount.stream().anyMatch(v2 -> v1.getMemberId().equals(v2.getBuyMemberId()) && v1.getMemberRoleId().equals(v2.getBuyMemberRoleId()))).collect(Collectors.toList());
                        vo.setMemberCount((long) count.size());
                    }
                    ArrayList<MobileWechatAppletAchievementCountResp> amountPayableList = values.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(MobileWechatAppletAchievementCountResp::getOrderId))), ArrayList::new));

                    // 已经支付的金额
                    vo.setAmountPaid(amountPayableList.stream().map(MobileWechatAppletAchievementCountResp::getAmountPaid).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add));
                    // 订单应该支付的金额
                    vo.setAmountPayable(amountPayableList.stream().map(MobileWechatAppletAchievementCountResp::getAmountPayable).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add));
                    // 退款金额
                    vo.setRefundAmount(values.stream().map(MobileWechatAppletAchievementCountResp::getRefundAmount).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add));
                }
            }
            return vo;
        }).collect(Collectors.toList());
        List<MobileAchievementCountQueryResp> result = queryVO.stream().sorted(Comparator.comparing(MobileAchievementCountQueryResp::getMonthStatistical).reversed()).collect(Collectors.toList());
        return new PageDataResp<>(totalCount, result);
    }

    /**
     * 业务员个人业绩数据查询
     */
    private List<MobileWechatAppletAchievementCountResp> getPersonalAchievement(List<Integer> orderTypes, List<Long> roleIds, Long userId, LocalDateTime startTime, LocalDateTime endTime, Integer orderStatus) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        // qOrder.submitTime.yearMonth().as("yearMonth") 返回出来的是Integer类型，如202204
        JPAQuery<MobileWechatAppletAchievementCountResp> query =
                jpaQueryFactory.select(Projections.constructor(MobileWechatAppletAchievementCountResp.class, qOrder.submitTime.yearMonth().as("yearMonth"), qOrder.vendorUserId, qOrder.buyerMemberId, qOrder.buyerRoleId, qOrder.id, qOrderProduct.productId, qOrderProduct.category, qOrder.totalAmount, qOrder.paidAmount, qOrderProduct.refReturnAmount))
                        .from(qOrderProduct)
                        .leftJoin(qOrder).on(qOrderProduct.order.id.eq(qOrder.id))
                        .where(qOrder.orderType.in(orderTypes))
                        .where(qOrder.buyerRoleId.in(roleIds))
                        .groupBy(qOrder.vendorUserId, qOrder.buyerMemberId, qOrder.buyerRoleId, qOrder.id, qOrderProduct.productId, qOrderProduct.category, qOrder.paidAmount, qOrderProduct.refReturnAmount, qOrderProduct.refPrice, qOrderProduct.quantity);

        if (ObjectUtil.isNotNull(startTime) && ObjectUtil.isNotNull(endTime)) {
            query.where(qOrder.submitTime.after(endTime).and(qOrder.submitTime.before(startTime)));
        }
        if (ObjectUtil.isNotNull(userId)) {
            query.where(qOrder.vendorUserId.eq(userId));
        }
        if (ObjectUtil.isNotNull(orderStatus)) {
            query.where(qOrder.outerStatus.notIn(101));
        }
        return query.orderBy(qOrder.submitTime.asc()).fetch();
    }

    /**
     * 业务员业绩统计-月份处理
     */
    private List<MobileWechatAppletAchievementCountResp> monthsHandle(List<MobileWechatAppletAchievementCountResp> resultCounts, LocalDate startTime, LocalDate endTime, Integer startOrderMonthInteger, Long userId) {
        // 根据搜索开始时间和搜索结束时间，得到中间差的月份，然后找到所有这中间的月份
        // 比如开始时间是202204，结束时间是202107，monthLength中间长度为10，全部月份就是202204，202203，，，202106，202107
        LocalDate orderStartMonth = LocalDate.of(startOrderMonthInteger / 100, startOrderMonthInteger % 100, 1);
        long monthLength;
        if (orderStartMonth.isBefore(endTime)) {
            // 订单开始时间小于订单结束时间
            monthLength = endTime.until(startTime, ChronoUnit.MONTHS) + 1;
        } else {
            //订单开始时间要是202202，搜索的结束时间是202201，那么从202202以后的月份就不再添加了
            monthLength = orderStartMonth.until(startTime, ChronoUnit.MONTHS) + 1;
        }

        // 全部年份
        HashSet<Integer> allDateSet = new HashSet<>();
        for (int i = 0; i < monthLength; i++) {
            LocalDate date = startTime.minusMonths(i);
            allDateSet.add(Integer.valueOf(date.getYear() + date.toString().substring(5, 7)));
        }
        // 有业绩的月份
        Set<Integer> months = resultCounts.stream().filter(ObjectUtil::isNotNull).map(MobileWechatAppletAchievementCountResp::getYear).collect(Collectors.toSet());
        // 两个List取补集，即相同的月份都去掉
        allDateSet.removeAll(months);
        return allDateSet.stream().map(s -> {
            MobileWechatAppletAchievementCountResp vo = new MobileWechatAppletAchievementCountResp();
            vo.setUserId(userId);
            vo.setYear(s);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据业务员Id查找下单已付金额,应付金额，退款金额
     */
    private List<OrderAmountResp> paidAmount(Long userId, List<Long> roles, List<Integer> orderTypes, LocalDateTime startTime, LocalDateTime endTime) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderPaymentDO qOrderPaymentDO = QOrderPaymentDO.orderPaymentDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        JPAQuery<OrderAmountResp> query = jpaQueryFactory.select(Projections.constructor(OrderAmountResp.class, qOrder.id, qOrder.paidAmount, qOrder.totalAmount, qOrderProduct.refReturnAmount))
                .from(qOrder)
                .leftJoin(qOrderPaymentDO).on(qOrderPaymentDO.order.id.eq(qOrder.id))
                .leftJoin(qOrderProduct).on(qOrderProduct.order.id.eq(qOrder.id))
                .where(qOrder.orderType.in(orderTypes))
                .where(qOrder.vendorUserId.eq(userId))
                .where(qOrder.buyerRoleId.in(roles))
                .where(qOrder.outerStatus.notIn(101))
                .where(qOrder.submitTime.after(startTime).and(qOrder.submitTime.before(endTime)));

        return query.fetch();
    }


    /**
     * 订单类型
     */
    private ArrayList<Integer> orderTypes() {
        ArrayList<Integer> orderTypes = new ArrayList<>();
        orderTypes.add(OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode());
        orderTypes.add(OrderTypeEnum.SPOT_PURCHASING.getCode());
//        orderTypes.add(OrderTypeEnum.CHANNEL_STRAIGHT_MINING.getCode());
//        orderTypes.add(OrderTypeEnum.CHANNEL_SPOT.getCode());
        return orderTypes;
    }

    /**
     * 业务员小程序 -首页统计：统计下级会员个数
     *
     * @param memberId 会员id
     * @param roleId   角色id
     * @param userId   用户id
     * @return 返回下级会员个数
     */
    private Integer countMember(Long memberId, Long roleId, Long userId) {

        MemberSalesFindUserIdReq userIdVO = new MemberSalesFindUserIdReq(memberId, roleId, userId, null, null, null, null);
        logger.info("向会员服务发送请求：-----会员ID:" + userIdVO.getMemberId() + "角色Id:" + userIdVO.getMemberRoleId() + "用户Id" + userIdVO.getUserId());
        WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> userWrapperResp = mobileMemberSalesFeign.getSalesList(userIdVO);
        if (ObjectUtil.isNull(userWrapperResp.getData()) && !CollectionUtils.isEmpty(userWrapperResp.getData().getData())) {
            logger.info("该业务员没有下级会员信息");
            return 0;
        }
        // 得到全部会员
        List<MemberSalesFeignPageQueryResp> memberList = userWrapperResp.getData().getData();
        ArrayList<MemberSalesFeignPageQueryResp> userCount = memberList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(user -> user.getMemberId() + ";" + user.getMemberRoleId()))), ArrayList::new));
        return userCount.size();
    }

    /**
     * 业务员小程序 -首页统计：查找该业务员的全部订单的已完成金额
     *
     * @param userId     用户id
     * @param orderTypes 订单类型
     * @param roles      角色id
     * @return 返回该业务员的全部订单的已完成金额
     */
    private BigDecimal getAllFinishedAmount(Long userId, List<Integer> orderTypes, List<Long> roles) {
        PageDataResp<MobileMemberSalesCountQueryResp> memberCountDataVO = indexOrderCount(userId, orderTypes, roles, null, null);
        if (CollectionUtils.isEmpty(memberCountDataVO.getData())) {
            return new BigDecimal(0);
        }
        List<MobileMemberSalesCountQueryResp> orderCountVO = memberCountDataVO.getData();

        // 订单完成全部已支付金额
        BigDecimal reduce = orderCountVO.stream().map(MobileMemberSalesCountQueryResp::getAmountPaid).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add);
        // 订单完成全部已退款金额
        BigDecimal returnAmountAll = orderCountVO.stream().map(MobileMemberSalesCountQueryResp::getRefundAmount).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add);
        // 订单完成全部完成金额 = 全部已支付金额 - 全部已退款金额
        return reduce.subtract(returnAmountAll);
    }

    /**
     * 业务员小程序 -首页统计：查找该业务员 本月的全部订单的已完成金额
     *
     * @param userId     用户id
     * @param orderTypes 订单类型
     * @param roles      用户角色
     * @param startTime  开始时间
     * @param endTime    结束时间
     */
    private BigDecimal getMothFinishedAmount(Long userId, List<Integer> orderTypes, List<Long> roles, LocalDateTime startTime, LocalDateTime endTime) {
        PageDataResp<MobileMemberSalesCountQueryResp> queryPageDataResp = indexOrderCount(userId, orderTypes, roles, startTime, endTime);
        if (CollectionUtils.isEmpty(queryPageDataResp.getData())) {
            return new BigDecimal(0);
        } else {
            // 本月的完成金额  -- 收款金额-退款金额
            BigDecimal amountPaidFinish = queryPageDataResp.getData().stream().map(MobileMemberSalesCountQueryResp::getAmountPaid).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add);
            BigDecimal returnAmountFinish = queryPageDataResp.getData().stream().map(MobileMemberSalesCountQueryResp::getRefundAmount).filter(ObjectUtil::isNotNull).reduce(new BigDecimal(0), BigDecimal::add);
            return amountPaidFinish.subtract(returnAmountFinish);
        }
    }

    /**
     * 构建MobileAchievementCountQueryVO
     *
     * @param month  统计时间
     * @param userId 业务员Id
     * @return 返回vo
     */
    private MobileAchievementCountQueryResp buildAchievementCountVO(LocalDate month, Long userId) {
        MobileAchievementCountQueryResp vo = new MobileAchievementCountQueryResp();
        // 月统计时间
        vo.setMonthStatistical(month);
        vo.setMemberCount(0L);
        vo.setCategoryCount(0L);
        vo.setOrderCount(0L);
        vo.setCommodityCount(0L);
        vo.setAmountPaid(BigDecimal.ZERO);
        vo.setAmountPayable(BigDecimal.ZERO);
        vo.setRefundAmount(BigDecimal.ZERO);
        vo.setMemberUserId(userId);
        return vo;
    }

    /**
     * 根据分页信息得到月份开始时间和结束时间
     *
     * @param current     当前页
     * @param pageSize    每页条数
     * @param isStartTime 是否开始时间
     * @return 返回开始时间或结束时间
     */
    private LocalDateTime getMonth(int current, int pageSize, Boolean isStartTime) {
        if (isStartTime) {
            // 月份开始时间
            LocalDate startMonth;
            if (current == 1) {
                startMonth = LocalDate.now();
            } else {
                int month = (current - 1) * pageSize;
                startMonth = LocalDate.now().minusMonths(month);
            }
            return LocalDateTime.of(startMonth, LocalTime.MAX).with(TemporalAdjusters.lastDayOfMonth());
        } else {
            // 月份结束时间
            int endMonthInt = current * pageSize - 1;
            LocalDate endMonth = LocalDate.now().minusMonths(endMonthInt);
            return LocalDateTime.of(endMonth, LocalTime.MIN).with(TemporalAdjusters.firstDayOfMonth());
        }
    }

}
