package com.ssy.lingxi.order.serviceImpl.feign;

import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.logistics.api.feign.IFreightRemoteFeign;
import com.ssy.lingxi.logistics.api.feign.IShipperAddressFeign;
import com.ssy.lingxi.logistics.api.model.dto.OrderSeparateProductFreightDetailDTO;
import com.ssy.lingxi.logistics.api.model.req.*;
import com.ssy.lingxi.logistics.api.model.resp.FreightResp;
import com.ssy.lingxi.logistics.api.model.resp.OrderSeparateFreightListResp;
import com.ssy.lingxi.logistics.api.model.resp.ShipperAddressDetailResp;
import com.ssy.lingxi.order.api.model.resp.OrderFreeExpressConfigResp;
import com.ssy.lingxi.order.model.bo.LogisticsProductBO;
import com.ssy.lingxi.order.model.bo.LogisticsProductDetailBO;
import com.ssy.lingxi.order.model.dto.OrderConsigneeDTO;
import com.ssy.lingxi.order.model.dto.OrderSeparateDTO;
import com.ssy.lingxi.order.model.dto.ProductAddressDTO;
import com.ssy.lingxi.order.model.req.basic.OrderProductFreeFreightReq;
import com.ssy.lingxi.order.model.req.basic.ProductFreeFreightReq;
import com.ssy.lingxi.order.service.feign.ILogisticsFeignService;
import com.ssy.lingxi.order.service.web.IOrderParamConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 物流服务Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-14
 */
@Slf4j
@Service
public class LogisticsFeignServiceImpl implements ILogisticsFeignService {

    @Resource
    private IShipperAddressFeign shipperAddressFeign;
    @Resource
    private IFreightRemoteFeign freightRemoteControllerFeign;
    @Resource
    private IOrderParamConfigService orderParamConfigService;


    /**
     * 查询配送方式为“自提”的商品地址
     *
     * @param addressIds 地址Id列表
     * @return 查询结果
     */
    @Override
    public List<ProductAddressDTO> findProductAddresses(List<Long> addressIds) {
        if(CollectionUtils.isEmpty(addressIds)) {
            return new ArrayList<>();
        }

        CommonIdListReq request = new CommonIdListReq();
        request.setIdList(addressIds);
        try {
            WrapperResp<List<ShipperAddressDetailResp>> addressResult = shipperAddressFeign.listShipperAddress(request);
            WrapperUtil.throwWhenFail(addressResult);

            return addressResult.getData().stream().map(r -> {
                ProductAddressDTO productAddress = new ProductAddressDTO();
                productAddress.setAddressId(r.getId());
                productAddress.setProvinceCode(r.getProvinceCode());
                productAddress.setProvinceName(r.getProvinceName());
                productAddress.setCityCode(r.getCityCode());
                productAddress.setCityName(r.getCityName());
                productAddress.setDistrictCode(r.getDistrictCode());
                productAddress.setDistrictName(r.getDistrictName());
                productAddress.setStreetCode(r.getStreetCode());
                productAddress.setStreetName(r.getStreetName());
                productAddress.setAddress(r.getAddress());
                productAddress.setReceiver(r.getShipperName());
                productAddress.setCountryCode(r.getCountryCode());
                productAddress.setPhone(r.getPhone());
                productAddress.setFullAddress(r.getFullAddress());
                return productAddress;
            }).collect(Collectors.toList());
        } catch (Exception ignored) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_LOGISTICS_ERROR);
        }
    }

    /**
     * 生成供应商收货人信息，计算转单订单的运费
     *
     * @param logisticsProducts 订单、供应商、上游供应商商品信息
     * @return 查询结果
     */
    @Override
    public List<OrderConsigneeDTO> findVendorConsignee(List<LogisticsProductBO> logisticsProducts) {
        List<TransferOrderFreightReq> requests = logisticsProducts.stream().map(logisticsProduct -> {
            TransferOrderFreightReq request = new TransferOrderFreightReq();
            request.setOrderId(logisticsProduct.getOrderId());
            request.setMemberId(logisticsProduct.getVendorMemberId());
            request.setRoleId(logisticsProduct.getVendorRoleId());
            request.setReceiverAddressId(logisticsProduct.getConsigneeId());
            request.setProductFreightDetailList(logisticsProduct.getSupplyProducts().stream().map(product -> {
                ProductFreightDetailReq detail = new ProductFreightDetailReq();
                detail.setTemplateId(product.getTemplateId());
                detail.setWeight(product.getWeight());
                detail.setCount(product.getQuantity());
                return detail;
            }).collect(Collectors.toList()));
            return request;
        }).collect(Collectors.toList());

        WrapperResp<List<FreightResp>> logisticsResult = freightRemoteControllerFeign.batchCalFreightPrice(requests);
        WrapperUtil.throwWhenFail(logisticsResult);

        return CollectionUtils.isEmpty(logisticsResult.getData()) ? new ArrayList<>() : logisticsResult.getData().stream().map(r -> {
            OrderConsigneeDTO consigneeDTO = new OrderConsigneeDTO();
            consigneeDTO.setOrderId(r.getOrderId());
            consigneeDTO.setConsigneeId(r.getReceiverAddressId());
            consigneeDTO.setConsignee(r.getReceiverName());
            consigneeDTO.setProvinceCode(r.getProvinceCode());
            consigneeDTO.setProvinceName(r.getProvinceName());
            consigneeDTO.setCityCode(r.getCityCode());
            consigneeDTO.setCityName(r.getCityName());
            consigneeDTO.setDistrictCode(r.getDistrictCode());
            consigneeDTO.setDistrictName(r.getDistrictName());
            consigneeDTO.setAddress(StringUtils.hasLength(r.getAddress()) ? r.getAddress() : "");
            consigneeDTO.setPostalCode(StringUtils.hasLength(r.getPostalCode()) ? r.getPostalCode() : "");
            consigneeDTO.setCountryCode(StringUtils.hasLength(r.getAreaCode()) ? r.getAreaCode() : "");
            consigneeDTO.setPhone(StringUtils.hasLength(r.getPhone()) ? r.getPhone() : "");
            consigneeDTO.setTelephone(StringUtils.hasLength(r.getTel()) ? r.getTel() : "");
            consigneeDTO.setDefaultConsignee(!NumberUtil.isNullOrZero(r.getIsDefault()) && (r.getIsDefault().compareTo(1) == 0));
            consigneeDTO.setFreight(NumberUtil.isNullOrZero(r.getFreightPrice()) ? BigDecimal.ZERO : r.getFreightPrice());
            return consigneeDTO;
        }).collect(Collectors.toList());
    }

    /**
     * （拆单）查询商品的运费
     *
     * @param consigneeId 收货人地址Id
     * @param products 拆单后的商品列表
     * @return 查询结果
     */
    @Override
    public Map<OrderSeparateDTO, BigDecimal> findSeparateOrderFreight(Long consigneeId, Map<OrderSeparateDTO,  List<LogisticsProductDetailBO>> products) {
        List<OrderSeparateFreightListReq> requests = products.entrySet().stream().map(entry -> {
            OrderSeparateFreightListReq request = new OrderSeparateFreightListReq();
            request.setRequest(new OrderSeparateFreightReq(entry.getKey().getVendorMemberId(), entry.getKey().getVendorRoleId(), entry.getKey().getSupplyMemberId(), entry.getKey().getSupplyRoleId()));
            request.setProducts(entry.getValue().stream().filter(productVO -> NumberUtil.notNullAndPositive(productVO.getTemplateId()) && NumberUtil.notNullAndPositive(productVO.getWeight()) && NumberUtil.notNullAndPositive(productVO.getQuantity())).map(product -> {
                OrderSeparateProductFreightDetailDTO detail = new OrderSeparateProductFreightDetailDTO();
                detail.setReceiverAddressId(consigneeId);
                detail.setTemplateId(product.getTemplateId());
                detail.setWeight(product.getWeight());
                detail.setCount(product.getQuantity());
                return detail;
            }).collect(Collectors.toList()));
            return request;
        }).collect(Collectors.toList());

        requests.removeIf(r -> CollectionUtils.isEmpty(r.getProducts()));

        if(CollectionUtils.isEmpty(requests)) {
            return products.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, p -> BigDecimal.ZERO));
        }

        WrapperResp<List<OrderSeparateFreightListResp>> freightResult = freightRemoteControllerFeign.findSeparateOrderFreight(requests);
        WrapperUtil.throwWhenFail(freightResult);

        return products.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, p ->
            freightResult.getData().stream().filter(r -> r.getRequest().equals(new OrderSeparateFreightReq(p.getKey().getVendorMemberId(), p.getKey().getVendorRoleId(), p.getKey().getSupplyMemberId(), p.getKey().getSupplyRoleId()))).map(OrderSeparateFreightListResp::getFreight).findFirst().orElse(BigDecimal.ZERO)
        ));
    }

    /**
     * 查询订单运费
     *
     * @param consigneeId       收货人地址Id
     * @param logisticsProducts 订单商品信息列表
     * @return 订单运费
     */
    @Override
    public BigDecimal findOrderFreight(Long consigneeId, List<LogisticsProductDetailBO> logisticsProducts) {
        if(CollectionUtils.isEmpty(logisticsProducts)) {
            return BigDecimal.ZERO;
        }

        OrderFreightReq request = new OrderFreightReq();
        request.setReceiverAddressId(consigneeId);
        request.setProductFreightDetailList(logisticsProducts.stream().map(product -> {
            ProductFreightDetailReq detail = new ProductFreightDetailReq();
            detail.setTemplateId(product.getTemplateId());
            detail.setCount(product.getQuantity());
            detail.setWeight(product.getWeight());
            return detail;
        }).collect(Collectors.toList()));

        WrapperResp<BigDecimal> result = freightRemoteControllerFeign.calFreightPrice(request);
        WrapperUtil.throwWhenFail(result);

        return result.getData();
    }

    /**
     * 查询满额包邮的商品总运费
     *
     * @param freightVO 接口参数
     * @return 订单运费
     */
    @Override
    public BigDecimal findOrderFreeFreight(UserLoginCacheDTO loginUser, OrderProductFreeFreightReq freightVO) {
        List<ProductFreeFreightReq> logisticsProducts = freightVO.getProductFreightDetailList();
        if (CollectionUtils.isEmpty(logisticsProducts)) {
            return BigDecimal.ZERO;
        }

        List<MemberAndRoleIdDTO> memberList = logisticsProducts.stream().map(
                p -> new MemberAndRoleIdDTO(p.getMemberId(), p.getRoleId())
        ).collect(Collectors.toList());
        List<OrderFreeExpressConfigResp> freeExpressConfigList = orderParamConfigService.getOrderFreeExpressConfigList(memberList);

        Map<Long, BigDecimal> totalAmountMap = logisticsProducts.stream().collect(Collectors.toMap(ProductFreeFreightReq::getMemberId, p -> p.getRefPrice().multiply(p.getCount()), BigDecimal::add));

        List<ProductFreeFreightReq> freightProductList = new ArrayList<>(logisticsProducts);
        for (OrderFreeExpressConfigResp configVO : freeExpressConfigList) {
            BigDecimal totalAmount = totalAmountMap.getOrDefault(configVO.getMemberId(), BigDecimal.ZERO);
            if (totalAmount.compareTo(configVO.getOrderAmount()) >= 0) {
                freightProductList.removeIf(p -> p.getMemberId().equals(configVO.getMemberId()) && p.getRoleId().equals(configVO.getRoleId()));
            }
        }

        OrderFreightReq request = new OrderFreightReq();
        request.setReceiverAddressId(freightVO.getReceiverAddressId());
        request.setProductFreightDetailList(freightProductList.stream().map(product -> {
            ProductFreightDetailReq detail = new ProductFreightDetailReq();
            detail.setTemplateId(product.getTemplateId());
            detail.setCount(product.getCount());
            detail.setWeight(product.getWeight());
            return detail;
        }).collect(Collectors.toList()));

        WrapperResp<BigDecimal> result = freightRemoteControllerFeign.calFreightPrice(request);
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }
}
