package com.ssy.lingxi.order.serviceImpl.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.enums.order.OrderSourceKindEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.BigDecimalUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.FundModeEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.component.base.enums.order.SubPaymentOrderStatusEnum;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.rest.model.resp.eos.GoldPriceResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.contract.api.model.resp.OrderContractResp;
import com.ssy.lingxi.member.api.feign.IMemberDetailFeign;
import com.ssy.lingxi.member.api.model.req.UserIdFeignReq;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.BuyerInnerStatusEnum;
import com.ssy.lingxi.order.enums.OrderOuterStatusEnum;
import com.ssy.lingxi.order.enums.OrderPromotionStatusEnum;
import com.ssy.lingxi.order.enums.VendorInnerStatusEnum;
import com.ssy.lingxi.order.model.dto.MobileOrderPageDTO;
import com.ssy.lingxi.order.model.dto.MobileOrderProductQueryDTO;
import com.ssy.lingxi.order.model.dto.MobileOrderQueryDTO;
import com.ssy.lingxi.order.model.resp.mobile.*;
import com.ssy.lingxi.order.model.resp.vendor.VendorOrderSubPaymentDetailResp;
import com.ssy.lingxi.order.repository.SubOrderPaymentRepository;
import com.ssy.lingxi.order.service.base.IBaseMobileOrderService;
import com.ssy.lingxi.order.service.feign.IContractFeignService;
import com.ssy.lingxi.order.service.web.OrderService;
import com.ssy.lingxi.product.api.feign.IWarehouseFeign;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * App - 基础订单服务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-22
 */
@Service
public class BaseMobileOrderServiceImpl implements IBaseMobileOrderService {
    @Resource
    private JPAQueryFactory jpaQueryFactory;
    @Resource
    private IContractFeignService contractFeignService;

    @Resource
    private IMemberDetailFeign memberDetailFeign;

    @Resource
    private OrderService orderService;

    @Resource
    private IWarehouseFeign warehouseFeign;

    @Resource
    private SubOrderPaymentRepository subOrderPaymentRepository;

    @Resource
    private EosApiService eosApiService;

    /**
     * 订单是否没有被取消或没有被中止
     * @param query 订单查询DTO
     * @return true-没有被取消或中止，false-已经被取消或中止
     */
    @Override
    public Boolean notCancelledOrTerminated(MobileOrderQueryDTO query) {
        return !query.getInnerStatus().equals(BuyerInnerStatusEnum.CANCELLED.getCode()) && !query.getInnerStatus().equals(BuyerInnerStatusEnum.TERMINATED.getCode()) && !query.getOuterStatus().equals(OrderOuterStatusEnum.CANCELLED.getCode()) && !query.getOuterStatus().equals(OrderOuterStatusEnum.TERMINATED.getCode());
    }

    /**
     * 订单是否能调整送货时间
     * @param query 订单查询DTO
     * @return true-是， false-否
     */
    @Override
    public Boolean canBuyerModifyDeliverTime(MobileOrderQueryDTO query) {
        //订单状态不为已完成，已取消，已中止且未发货过
        return !(query.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()) || query.getOuterStatus().equals(OrderOuterStatusEnum.CANCELLED.getCode()) || query.getOuterStatus().equals(OrderOuterStatusEnum.TERMINATED.getCode()) || query.getHasDelivered());
    }

    /**
     * 订单是否能退款
     * @param query 订单查询DTO
     * @return true-是， false-否
     */
    @Override
    public Boolean canBuyerRefundOrder(MobileOrderQueryDTO query) {
        //退款条件：订单为拼团订单，外部状态为“已取消”，退款失败，支付记录包含一次100%支付的记录
        return query.getOrderKind().equals(OrderSourceKindEnum.GROUP.getCode()) && query.getOuterStatus().equals(OrderOuterStatusEnum.CANCELLED.getCode()) && !query.getHasRefund() && query.getCanRefund();
    }

    /**
     * 订单是否能“售后”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderAfterSale(MobileOrderQueryDTO query) {
        //如果是拼团订单：（拼团状态为拼团失败，且订单的外部状态为“待确认发货”）或（拼团成功）， 则可以售后
        //如果非拼团订单：已经有收到款或有发货的订单即显示售后按钮（包括状态为已中止的订单）
        return query.getOrderKind().equals(OrderSourceKindEnum.GROUP.getCode()) ?
                (((query.getOuterStatus().equals(OrderOuterStatusEnum.TO_CONFIRM_DELIVERY.getCode()) && query.getPromotionStatus().equals(OrderPromotionStatusEnum.GROUP_FAILED.getCode()))) || query.getPromotionStatus().equals(OrderPromotionStatusEnum.GROUP_SUCCESS.getCode())) :
                ((query.getHasPaid() || query.getHasDelivered()) && !query.getOuterStatus().equals(OrderOuterStatusEnum.TERMINATED.getCode()));
    }

    /**
     * 订单是否能“归档”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderArchive(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_ARCHIVE.getCode());
    }

    /**
     * 订单是否能“再次购买”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderAgain(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && (query.getInnerStatus().equals(BuyerInnerStatusEnum.ACCOMPLISHED.getCode()) || query.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode())) && !OrderTypeEnum.CREDITS_EXCHANGE.getCode().equals(query.getOrderType());
    }

    /**
     * 订单是否能“取消”
     * @param mobileOrderQueryDTO 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderCancel(MobileOrderQueryDTO mobileOrderQueryDTO) {
        return BuyerInnerStatusEnum.BUYER_TO_PAY.getCode().equals(mobileOrderQueryDTO.getInnerStatus()) ||
                BuyerInnerStatusEnum.BUYER_PAY_NOT_ARRIVED.getCode().equals(mobileOrderQueryDTO.getInnerStatus());
    }

    /**
     * 订单是否能“评论”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderComment(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && !query.getBuyerCommented() && (query.getInnerStatus().equals(BuyerInnerStatusEnum.ACCOMPLISHED.getCode()) || query.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode()));
    }

    /**
     * 订单是否能“确认电子合同”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderConfirmContract(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_CONFIRM_CONTRACT.getCode());
    }

    /**
     * 订单是否能“确认采购收货单”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderConfirmReception(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_VALIDATE_DEPOT.getCode());
    }

    /**
     * 订单是否能“新增采购收货单”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderCreateReception(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_DEPOSIT.getCode());
    }

    /**
     * 订单是否能“支付”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderPay(MobileOrderQueryDTO query) {
        //“待支付订单” 的 状态：“未支付”、“支付失败”、“支付成功，但被供应商确认未到账”
        return notCancelledOrTerminated(query) && (query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode()) || query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode()) || (query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode()) && query.getOuterStatus().equals(OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode())));
    }

    /**
     * 订单是否能“确认收货”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderReceive(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode());
    }

    /**
     * 订单是否能“提交”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderSubmit(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_SUBMIT.getCode());
    }

    /**
     * 订单是否能“一级审核”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderValidateGradeOne(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_ONE.getCode());
    }

    /**
     * 订单是否能“二级审核”
     * @param query 订单查询DTO
     * @return 是-true，否-false
     */
    @Override
    public Boolean canOrderValidateGradeTwo(MobileOrderQueryDTO query) {
        return notCancelledOrTerminated(query) && query.getInnerStatus().equals(BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_TWO.getCode());
    }

    /**
     * 判断采购商是否能删除订单
     * @param query 订单查询DTO
     * @return true-是，false-否
     */
    @Override
    public Boolean canBuyerDeleteOrder(MobileOrderQueryDTO query) {
        //删除条件：除拼团订单且订单外部状态为”已取消“且拼团退款失败的订单外，显示”删除“按钮
        return !query.getOrderKind().equals(OrderSourceKindEnum.GROUP.getCode()) && query.getOuterStatus().equals(OrderOuterStatusEnum.CANCELLED.getCode());
    }

    /**
     * 分页查询采购订单
     * @param buyerMemberId    采购会员Id
     * @param buyerRoleId      采购会员角色Id
     * @param keyword          订单编号/供应会员/订单摘要
     * @param buyerInnerStatus 采购会员内部状态
     * @param outerStatus      订单外部状态
     * @param current          当前页
     * @param pageSize         每页行数
     * @return 查询结果
     */
    @Override
    public MobileOrderPageDTO pageMobileBuyerOrders(Long buyerMemberId, Long buyerRoleId, String keyword, Integer buyerInnerStatus, Integer outerStatus, int current, int pageSize, Integer saleMode) {
        return pageMobileBuyerOrders(buyerMemberId, buyerRoleId, keyword, "", "", buyerInnerStatus, outerStatus, current, pageSize, saleMode, null, null);
    }

    /**
     * 分页查询采购订单
     * @param buyerMemberId    采购会员Id
     * @param buyerRoleId      采购会员角色Id
     * @param keyword          订单编号/供应会员/订单摘要
     * @param startDate        订单起始日期
     * @param endDate          订单结束日期
     * @param buyerInnerStatus 采购会员内部状态
     * @param outerStatus      订单外部状态
     * @param current          当前页
     * @param pageSize         每页行数
     * @param saleMode         销售方式
     * @param innerStatusList 采购会员内部状态集合
     * @return 查询结果
     */
    @Override
    public MobileOrderPageDTO pageMobileBuyerOrders(Long buyerMemberId, Long buyerRoleId, String keyword, String startDate, String endDate, Integer buyerInnerStatus, Integer outerStatus, int current, int pageSize, Integer saleMode, List<Integer> innerStatusList, Long branchId) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
//        QOrderContractDO qOrderContract = QOrderContractDO.orderContractDO;

        //Step 1: 首先分页查询订单表与其一对一关联的表（这样才可以分页）。不查询Srm订单和请购单订单
        JPAQuery<MobileOrderQueryDTO> query = jpaQueryFactory
                .selectDistinct(Projections.constructor(MobileOrderQueryDTO.class,
                        qOrder.shopId,
                        qOrder.id,
                        qOrder.orderNo,
                        qOrder.orderMode,
                        qOrder.orderKind,
                        qOrder.vendorMemberId,
                        qOrder.vendorRoleId,
                        qOrder.vendorMemberName,
                        qOrder.vendorLogo,
                        qOrder.totalAmount,
                        qOrder.buyerInnerStatus,
                        qOrder.outerStatus,
                        qOrder.buyerCommented,
                        qOrder.hasPaid,
                        qOrder.hasDelivered,
                        qOrder.promotionStatus,
                        qOrder.groupId,
                        qOrder.canRefund,
                        qOrder.hasRefund,
                        qOrder.orderType,
                        qOrder.shopName,
                        qOrder.storeId,
                        qOrder.storeName,
                        qOrder.storeLogo,
                        qOrder.saleMode,
                        qOrder.paidAmount,
                        qOrder.depositAmount,
                        qOrder.totalWeight,
                        qOrder.totalCraftPrice,
                        qOrder.totalServicePrice,
                        qOrder.craftDiscountAmount
                ))
                .from(qOrder)
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .where(qOrder.buyerMemberId.eq(buyerMemberId).and(qOrder.buyerRoleId.eq(buyerRoleId)))
                .where(qOrder.orderKind.ne(OrderSourceKindEnum.SRM.getCode()).and(qOrder.orderKind.ne(OrderSourceKindEnum.REQUISITION.getCode())));

        //Step 2: 构造查询条件
        if (StringUtils.hasLength(keyword)) {
            String pattern = "%".concat(keyword.trim()).concat("%");
            query.where(qOrder.orderNo.like(pattern).or(qOrder.vendorMemberName.like(pattern)).or(qOrderProduct.name.like(pattern)));
        }

        //订单起始时间
        if (StringUtils.hasLength(startDate)) {
            query.where(qOrder.createTime.after(LocalDateTime.parse(startDate.concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //订单起始时间
        if (StringUtils.hasLength(endDate)) {
            query.where(qOrder.createTime.before(LocalDateTime.parse(endDate.concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //采购商内部状态
        if (NumberUtil.notNullAndPositive(buyerInnerStatus)) {
            query.where(qOrder.buyerInnerStatus.eq(buyerInnerStatus));
        }

        //采购商内部状态集合
        if (CollUtil.isNotEmpty(innerStatusList)) {
            query.where(qOrder.buyerInnerStatus.in(innerStatusList));
        }

        //外部状态
        if (NumberUtil.notNullAndPositive(outerStatus)) {
            query.where(qOrder.outerStatus.eq(outerStatus));
        }

        // 销售方式
        if (NumberUtil.notNullAndPositive(saleMode)) {
            query.where(qOrder.saleMode.eq(saleMode));
        }

        // 店铺id
        if (ObjectUtil.isNotEmpty(branchId)) {
            query.where(qOrder.branchId.eq(branchId));
        }

        //Step 3: 倒序排序、分页、总数
        long totalCount = query.fetchCount();
        query.orderBy(qOrder.id.desc());
        query.limit(pageSize).offset((long) (current - 1) * pageSize);

        List<MobileOrderQueryDTO> orders = query.fetch();

        //Step 4: 再查询订单商品表
        List<MobileOrderProductQueryDTO> orderProducts = jpaQueryFactory
                .select(Projections.constructor(MobileOrderProductQueryDTO.class,
                        qOrderProduct.order.id,
                        qOrderProduct.id,
                        qOrderProduct.productId,
                        qOrderProduct.skuId,
                        qOrderProduct.stockId,
                        qOrderProduct.priceType,
                        qOrderProduct.logo,
                        qOrderProduct.name,
                        qOrderProduct.category,
                        qOrderProduct.brand,
                        qOrderProduct.spec,
                        qOrderProduct.unit,
                        qOrderProduct.refPrice,
                        qOrderProduct.quantity,
                        qOrderProduct.deliverType,
                        qOrderProduct.address,
                        qOrderProduct.receiver,
                        qOrderProduct.phone,
                        qOrderProduct.skuCode,
                        qOrderProduct.netWeight,
                        qOrderProduct.singleCode
                )).from(qOrderProduct).where(qOrderProduct.order.id.in(orders.stream().map(MobileOrderQueryDTO::getOrderId).collect(Collectors.toList())))
                .fetch();

        //拼接订单商品
        orders.forEach(order -> order.setProducts(orderProducts.stream().filter(orderProduct -> orderProduct.getOrderId().equals(order.getOrderId())).collect(Collectors.toList())));


        GoldPriceResp goldPrice = eosApiService.getGoldPrice();
        orders.forEach(s -> {
            if (s.getTotalAmount().compareTo(s.getPaidAmount()) != 0) {
                BigDecimal multiply = s.getTotalWeight().multiply(goldPrice.getJj()).setScale(2, RoundingMode.HALF_UP);
                BigDecimal totalAmount = multiply.add(s.getTotalCraftPrice()).add(s.getTotalServicePrice()).subtract(s.getCraftDiscountAmount());
                s.setTotalAmount(totalAmount);
            }
        });

        // 根据订单状态，修改订单总金额
        orders.stream().filter(s-> CommoditySaleModeEnum.ORDER.getCode().equals(s.getSaleMode())).forEach(s -> {
            if (ObjectUtil.isNotEmpty(s.getPaidAmount()) && s.getPaidAmount().equals(s.getTotalAmount())) {
                s.setTotalAmount(s.getTotalAmount());
            } else {
                s.setTotalAmount(s.getDepositAmount());
            }
        });
        return new MobileOrderPageDTO(totalCount, orders);
    }

    /**
     * 分页查询“待支付”的采购订单
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param keyword       订单编号/供应会员/订单摘要
     * @param current       当前页
     * @param pageSize      每页行数
     * @return 查询结果
     */
    @Override
    public MobileOrderPageDTO pageMobileToPayOrders(Long buyerMemberId, Long buyerRoleId, String keyword, int current, int pageSize) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
//        QOrderContractDO qOrderContract = QOrderContractDO.orderContractDO;

        //Step 1: 定义关联关系，OrderDO表必须要有distinct()。不查询Srm订单和请购单订单
        JPAQuery<MobileOrderQueryDTO> query = jpaQueryFactory
                .selectDistinct(Projections.constructor(MobileOrderQueryDTO.class,
                        qOrder.shopId,
                        qOrder.id,
                        qOrder.orderNo,
                        qOrder.orderMode,
                        qOrder.orderKind,
                        qOrder.vendorMemberId,
                        qOrder.vendorRoleId,
                        qOrder.vendorMemberName,
                        qOrder.vendorLogo,
                        qOrder.totalAmount,
                        qOrder.buyerInnerStatus,
                        qOrder.outerStatus,
                        qOrder.buyerCommented,
                        qOrder.hasPaid,
                        qOrder.hasDelivered,
                        qOrder.promotionStatus,
                        qOrder.groupId,
                        qOrder.canRefund,
                        qOrder.hasRefund,
                        qOrder.orderType,
                        qOrder.shopName,
                        qOrder.storeId,
                        qOrder.storeName,
                        qOrder.storeLogo
                ))
                .from(qOrder)
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .where(qOrder.buyerMemberId.eq(buyerMemberId).and(qOrder.buyerRoleId.eq(buyerRoleId)))
                .where(qOrder.orderKind.ne(OrderSourceKindEnum.SRM.getCode()).and(qOrder.orderKind.ne(OrderSourceKindEnum.REQUISITION.getCode())));

        //Step 2: 构造查询条件
        if (StringUtils.hasLength(keyword)) {
            String pattern = "%".concat(keyword.trim()).concat("%");
            query.where(qOrder.orderNo.like(pattern).or(qOrder.vendorMemberName.like(pattern)).or(qOrderProduct.name.like(pattern)));
        }

        //“待支付订单” 的 状态：“未支付”、“支付失败”、“支付成功，但被供应商确认未到账”
        query.where(qOrder.buyerInnerStatus.eq(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode())
                .or(qOrder.buyerInnerStatus.eq(BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode()))
                .or(qOrder.buyerInnerStatus.eq(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode()).and(qOrder.outerStatus.eq(OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode()))));

        //Step 3: 倒序排序、分页、总数
        long totalCount = query.fetchCount();
        query.orderBy(qOrder.id.desc());
        query.limit(pageSize).offset((long) (current - 1) * pageSize);

        List<MobileOrderQueryDTO> orders = query.fetch();

        //Step 4: 再查询订单商品表
        List<MobileOrderProductQueryDTO> orderProducts = jpaQueryFactory
                .select(Projections.constructor(MobileOrderProductQueryDTO.class,
                        qOrderProduct.order.id,
                        qOrderProduct.id,
                        qOrderProduct.productId,
                        qOrderProduct.skuId,
                        qOrderProduct.stockId,
                        qOrderProduct.priceType,
                        qOrderProduct.logo,
                        qOrderProduct.name,
                        qOrderProduct.category,
                        qOrderProduct.brand,
                        qOrderProduct.spec,
                        qOrderProduct.unit,
                        qOrderProduct.refPrice,
                        qOrderProduct.quantity,
                        qOrderProduct.deliverType,
                        qOrderProduct.address,
                        qOrderProduct.receiver,
                        qOrderProduct.phone,
                        qOrderProduct.skuCode,
                        qOrderProduct.netWeight,
                        qOrderProduct.singleCode
                )).from(qOrderProduct).where(qOrderProduct.order.id.in(orders.stream().map(MobileOrderQueryDTO::getOrderId).collect(Collectors.toList())))
                .fetch();

        //拼接订单商品
        orders.forEach(order -> order.setProducts(orderProducts.stream().filter(orderProduct -> orderProduct.getOrderId().equals(order.getOrderId())).collect(Collectors.toList())));
        return new MobileOrderPageDTO(totalCount, orders);
    }

    /**
     * 查询订单详情
     *
     * @param loginUser 登录用户（采购商）
     * @param orderId   订单Id
     * @return 查询结果
     */
    @Override
    public MobileOrderDetailResp findMobileOrderDetail(UserLoginCacheDTO loginUser, Long orderId) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderDeliveryDO qOrderDelivery = QOrderDeliveryDO.orderDeliveryDO;
        QOrderDeliveryProductDO qOrderDeliveryProduct = QOrderDeliveryProductDO.orderDeliveryProductDO;

        // Step 1: 使用 FetchJoin，强制关联查询指定的关联字段
        OrderDO order = jpaQueryFactory.selectDistinct(qOrder)
                .from(qOrder)
                .leftJoin(qOrder.products).fetchJoin()
                .leftJoin(qOrder.payments).fetchJoin()
//                .leftJoin(qOrder.contract).fetchJoin()
                .leftJoin(qOrder.consignee).fetchJoin()
                .leftJoin(qOrder.invoice).fetchJoin()
                .leftJoin(qOrder.requirement).fetchJoin()
                .where(qOrder.id.eq(orderId).and(qOrder.buyerMemberId.eq(loginUser.getMemberId())).and(qOrder.buyerRoleId.eq(loginUser.getMemberRoleId())))
                .fetchFirst();

        if (Objects.isNull(order)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        // 刷新订单实时价格
        if (order.getTotalAmount().compareTo(order.getPaidAmount()) != 0) {
            orderService.getOrderTotalPrice(order);
        }
        MobileOrderDetailResp orderDetail = new MobileOrderDetailResp(order);

        //Step 2: 查询订单收发货记录（这里要有一个where条件：qOrderDeliveryProduct.orderId.eq(orderId)）
        List<MobileOrderDeliveryDetailResp> deliveryDetails = new ArrayList<>(jpaQueryFactory
                .from(qOrderDelivery).distinct()
                .leftJoin(qOrderDeliveryProduct).on(qOrderDelivery.batchNo.eq(qOrderDeliveryProduct.batchNo))
                .leftJoin(qOrderProduct).on(qOrderDeliveryProduct.orderProductId.eq(qOrderProduct.id))
                .where(qOrderDelivery.order.id.eq(orderId).and(qOrderDeliveryProduct.orderId.eq(orderId)))
                .orderBy(qOrderDelivery.batchNo.asc())
                .transform(GroupBy.groupBy(qOrderDelivery.id).as(
                        Projections.constructor(MobileOrderDeliveryDetailResp.class, qOrderDelivery.createTime, qOrderDelivery.batchNo, qOrderDelivery.logisticsNo, qOrderDelivery.company, qOrderDelivery.receiptTime, qOrderDelivery.receiveBill, qOrderDelivery.selfCode, qOrderDelivery.buyerInnerStatus,
                                GroupBy.list(Projections.constructor(MobileOrderDeliverProductDetailResp.class, qOrderDeliveryProduct.id, qOrderProduct.logo, qOrderDeliveryProduct.name, qOrderDeliveryProduct.category, qOrderDeliveryProduct.brand, qOrderDeliveryProduct.unit, qOrderDeliveryProduct.spec, qOrderDeliveryProduct.delivered, qOrderDeliveryProduct.received)))))
                .values());

        //Step 3: 拼接查询结果，在MobileOrderDetailVO中 字段的setter方法中处理列表和相关字段
        orderDetail.setDeliveries(deliveryDetails);
        List<MobileOrderPaymentDetailResp> payments = orderDetail.getPayments();
        if(!CollectionUtils.isEmpty(payments)){
            //结价金重
            BigDecimal settlementGoleWeight = BigDecimal.ZERO;
            BigDecimal payMaterialStock = BigDecimal.ZERO;
            BigDecimal insuranceFee = BigDecimal.ZERO;
            if(order.getPacInsuranceSelected()){
                insuranceFee = BigDecimalUtil.add(order.getPacInsuranceFee(), insuranceFee);
            }
            if(order.getSfInsuranceSelected()){
                insuranceFee = BigDecimalUtil.add(order.getSfInsuranceFee(), insuranceFee);
            }
            for (MobileOrderPaymentDetailResp payment : payments) {
                BigDecimal payAmount = BigDecimal.ZERO;
                List<SubOrderPaymentDO> subOrderPaymentDOList = subOrderPaymentRepository.findAllByOrderPaymentIdAndSubStatus(payment.getPaymentId(), SubPaymentOrderStatusEnum.SUCCESS.getCode());
                payment.setInsuranceFee(insuranceFee);
                if (!CollectionUtils.isEmpty(subOrderPaymentDOList)) {
                    List<VendorOrderSubPaymentDetailResp> vendorOrderSubPaymentDetailResps = new ArrayList<>();
                    for (SubOrderPaymentDO subOrderPaymentDO : subOrderPaymentDOList) {
                        VendorOrderSubPaymentDetailResp subPaymentDetailResp = new VendorOrderSubPaymentDetailResp();
                        subPaymentDetailResp.setSubStatus(subOrderPaymentDO.getSubStatus());
                        subPaymentDetailResp.setChannelTradeNo(subOrderPaymentDO.getChannelTradeNo());
                        subPaymentDetailResp.setWeight(subOrderPaymentDO.getWeight());
                        subPaymentDetailResp.setPayChannel(subOrderPaymentDO.getPayChannel());
                        subPaymentDetailResp.setPayAmount(subOrderPaymentDO.getPayAmount());
                        subPaymentDetailResp.setBatchNo(subOrderPaymentDO.getBatchNo());
                        subPaymentDetailResp.setConfirmTime(subOrderPaymentDO.getConfirmTime());
                        subPaymentDetailResp.setRefundNo(subOrderPaymentDO.getRefundNo());
                        subPaymentDetailResp.setFundMode(subOrderPaymentDO.getFundMode());
                        subPaymentDetailResp.setPayType(subOrderPaymentDO.getPayType());
                        subPaymentDetailResp.setPayTime(subOrderPaymentDO.getPayTime());
                        subPaymentDetailResp.setPayChannelName(OrderPayChannelEnum.getNameByCode(subOrderPaymentDO.getPayChannel()));
                        subPaymentDetailResp.setSubStatusName(SubPaymentOrderStatusEnum.getNameByCode(subOrderPaymentDO.getSubStatus()));
                        subPaymentDetailResp.setFundModeName(FundModeEnum.getNameByCode(subOrderPaymentDO.getFundMode()));
                        vendorOrderSubPaymentDetailResps.add(subPaymentDetailResp);
                        OrderPayChannelEnum payChannelEnum = OrderPayChannelEnum.parse(subPaymentDetailResp.getPayChannel());
                        switch (payChannelEnum) {
                            case WEI_QI_FU:
                                BigDecimal weiqifuPaySumAmount = BigDecimalUtil.add(subOrderPaymentDO.getPayAmount(), payment.getServiceFee());
                                payment.setWeiqifuPayAmount(weiqifuPaySumAmount);
                                payment.setCurrentPayAmount(weiqifuPaySumAmount);
                                payment.setWeiqifuPayAmount2(subOrderPaymentDO.getPayAmount());
                                payment.setCurrentPayAmount2(subOrderPaymentDO.getPayAmount());
                                settlementGoleWeight = BigDecimalUtil.add(settlementGoleWeight, subOrderPaymentDO.getWeight());
                                payAmount = BigDecimalUtil.add(payAmount, subOrderPaymentDO.getPayAmount());
                                break;
                            case ACCOUNT_BALANCE:
                                payment.setUseAmount(subOrderPaymentDO.getPayAmount());
                                settlementGoleWeight = BigDecimalUtil.add(settlementGoleWeight, subOrderPaymentDO.getWeight());
                                payAmount = BigDecimalUtil.add(payAmount, subOrderPaymentDO.getPayAmount());
                                break;
                            case CREDIT:
                                payment.setPayMaterialStock(subOrderPaymentDO.getWeight());
                                payMaterialStock = BigDecimalUtil.add(payMaterialStock, subOrderPaymentDO.getWeight());
                                break;
                            case OFFLINE:
                                payment.setOfflinePayAmount(subOrderPaymentDO.getPayAmount());
                                payment.setCurrentPayAmount(subOrderPaymentDO.getPayAmount());
                                payment.setCurrentPayAmount2(subOrderPaymentDO.getPayAmount());
                                settlementGoleWeight = BigDecimalUtil.add(settlementGoleWeight, subOrderPaymentDO.getWeight());
                                payAmount = BigDecimalUtil.add(payAmount, subOrderPaymentDO.getPayAmount());
                                break;
                        }
                    }
                    payment.setSubPaymentDetails(vendorOrderSubPaymentDetailResps);
                }
                payment.setSettlementGoleWeight(settlementGoleWeight);
                payment.setSettlementAmount(BigDecimalUtil.multiply(settlementGoleWeight, payment.getSettlementGoldPrice()));
                payment.setSumPayOrderAmount(BigDecimalUtil.add(payAmount, payment.getServiceFee()));
                payment.setSumPayOrderAmount2(payAmount);
                payment.setSumPayGoleWeight(payMaterialStock);
                payment.setSumPayAmount(BigDecimalUtil.add(payAmount, payment.getServiceFee()));
                payment.setSumPayAmount2(payAmount);
                //payment.setSettlementGoleWeight(payment.getSettlementGoldPrice());
            }

        }

        UserIdFeignReq userIdFeignReq = new UserIdFeignReq();
        userIdFeignReq.setUserId(loginUser.getUserId());
        WrapperResp<Boolean> orderAuth = memberDetailFeign.hasOrderAuth(userIdFeignReq);
        if (WrapperUtil.isOk(orderAuth) && !orderAuth.getData()) {
            // 用户没有权限查询价格信息
            orderDetail.setDepositAmount(new BigDecimal(-1));
            orderDetail.setBalanceAmount(new BigDecimal(-1));
            orderDetail.setTotalMaterialAmount(new BigDecimal(-1));
            orderDetail.setTotalAmount("-1");
            orderDetail.setRawMaterialAmount(new BigDecimal(-1));
            orderDetail.setProductAmount("-1");
            orderDetail.setHasOrderAuth(orderAuth.getData());
        }

        List<String> warehouseCodeList = orderDetail.getProducts().stream()
                .map(MobileOrderProductDetailResp::getWarehouseCode)
                .filter(StringUtils::hasLength)
                .distinct()
                .collect(Collectors.toList());
        WrapperResp<Map<String, Long>> templateIdByWarehouseCode = warehouseFeign.findLogisticsTemplateIdByWarehouseCode(warehouseCodeList);
        if (WrapperUtil.isOk(templateIdByWarehouseCode)) {
            orderDetail.getProducts().forEach(s -> {
                s.setTemplateId(templateIdByWarehouseCode.getData().getOrDefault(s.getWarehouseCode(), null));
            });
        }
        // 判断订单商品是否有生产
        if (orderDetail.getSaleMode().equals(CommoditySaleModeEnum.SPOT.getCode()) || orderDetail.getProducts().stream().allMatch(s-> ObjectUtil.isNotEmpty(s.getSingleCode()))) {
            orderDetail.setHasProduction(true);
        }

        return orderDetail;
    }

    /**
     * 分页查询销售订单
     * @param vendorUserId  登录用户id
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param month         下单月份
     * @param memberName    采购会员名称
     * @param orderType     订单类型
     * @param current       当前页
     * @param pageSize      每页行数
     * @return 查询结果
     */
    @Override
    public MobileOrderPageDTO pageMobileVendorOrders(Long vendorUserId, Long buyerMemberId, List<Long> buyerRoleId, String month, String memberName, Integer orderType, int current, int pageSize) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;

        //Step 1: 首先分页查询订单表与其一对一关联的表（这样才可以分页）
        JPAQuery<MobileOrderQueryDTO> query = jpaQueryFactory
                .selectDistinct(Projections.constructor(MobileOrderQueryDTO.class,
                        qOrder.shopId,
                        qOrder.id,
                        qOrder.orderNo,
                        qOrder.orderMode,
                        qOrder.orderKind,
                        qOrder.vendorMemberId,
                        qOrder.vendorRoleId,
                        qOrder.vendorMemberName,
                        qOrder.vendorLogo,
                        qOrder.totalAmount,
                        qOrder.buyerInnerStatus,
                        qOrder.outerStatus,
                        qOrder.buyerCommented,
                        qOrder.hasPaid,
                        qOrder.hasDelivered,
                        qOrder.promotionStatus,
                        qOrder.groupId,
                        qOrder.canRefund,
                        qOrder.hasRefund,
                        qOrder.orderType,
                        qOrder.shopName,
                        qOrder.storeId,
                        qOrder.storeName,
                        qOrder.storeLogo
                ))
                .from(qOrder)
                .where(qOrder.vendorUserId.eq(vendorUserId))
//                .where(qOrder.outerStatus.eq(OrderOuterStatusEnum.ACCOMPLISHED.getCode()))
                .where(qOrder.buyerRoleId.in(buyerRoleId));

        //Step 2: 构造查询条件
        if (buyerMemberId != null) {
            query.where(qOrder.buyerMemberId.eq(buyerMemberId));
        }

        if (orderType == null) {
            query.where(qOrder.orderType.in(OrderTypeEnum.SPOT_PURCHASING.getCode(), OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode()));
        } else {
            query.where(qOrder.orderType.eq(orderType));
        }

        if (StringUtils.hasLength(memberName)) {
            query.where((qOrder.buyerMemberName.like("%".concat(memberName.trim()).concat("%"))));
        }

        //下单月份
        if (StringUtils.hasLength(month)) {
            query.where(qOrder.submitTime.after(LocalDateTime.parse(month.concat("-01 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //下单月份
        if (StringUtils.hasLength(month)) {
            query.where(qOrder.submitTime.before(LocalDateTime.parse(month.concat("-31 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //Step 3: 倒序排序、分页、总数
        long totalCount = query.fetchCount();
        query.orderBy(qOrder.id.desc());
        query.limit(pageSize).offset((long) (current - 1) * pageSize);

        List<MobileOrderQueryDTO> orders = query.fetch();

        //Step 4: 再查询订单商品表
        List<MobileOrderProductQueryDTO> orderProducts = jpaQueryFactory
                .select(Projections.constructor(MobileOrderProductQueryDTO.class,
                        qOrderProduct.order.id,
                        qOrderProduct.id,
                        qOrderProduct.productId,
                        qOrderProduct.skuId,
                        qOrderProduct.stockId,
                        qOrderProduct.priceType,
                        qOrderProduct.logo,
                        qOrderProduct.name,
                        qOrderProduct.category,
                        qOrderProduct.brand,
                        qOrderProduct.spec,
                        qOrderProduct.unit,
                        qOrderProduct.refPrice,
                        qOrderProduct.quantity,
                        qOrderProduct.deliverType,
                        qOrderProduct.address,
                        qOrderProduct.receiver,
                        qOrderProduct.phone
                )).from(qOrderProduct).where(qOrderProduct.order.id.in(orders.stream().map(MobileOrderQueryDTO::getOrderId).collect(Collectors.toList())))
                .fetch();

        //拼接订单商品
        orders.forEach(order -> order.setProducts(orderProducts.stream().filter(orderProduct -> orderProduct.getOrderId().equals(order.getOrderId())).collect(Collectors.toList())));

        return new MobileOrderPageDTO(totalCount, orders);
    }

    /**
     * 查询销售订单详情
     *
     * @param loginUser 登录用户（供应商）
     * @param orderId   订单Id
     * @return 查询结果
     */
    @Override
    public MobileVendorOrderDetailResp getMobileVendorOrderDetail(UserLoginCacheDTO loginUser, Long orderId) {
        QOrderDO qOrder = QOrderDO.orderDO;

        OrderDO order = jpaQueryFactory.selectDistinct(qOrder)
                .from(qOrder)
                .leftJoin(qOrder.products).fetchJoin()
                .leftJoin(qOrder.payments).fetchJoin()
//                .leftJoin(qOrder.contract).fetchJoin()
                .leftJoin(qOrder.consignee).fetchJoin()
                .leftJoin(qOrder.invoice).fetchJoin()
                .leftJoin(qOrder.requirement).fetchJoin()
                .where(qOrder.id.eq(orderId).and(qOrder.vendorMemberId.eq(loginUser.getMemberId())).and(qOrder.vendorRoleId.eq(loginUser.getMemberRoleId())))
                .fetchFirst();

        if (Objects.isNull(order)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        return new MobileVendorOrderDetailResp(order);
    }

    /**
     * 订单审核 - 分页查询销售订单
     * @param vendorUserId      登录用户id
     * @param buyerMemberId     采购会员Id
     * @param buyerRoleId       采购会员角色Id
     * @param keyword           订单编号/供应会员
     * @param vendorInnerStatus 供应会员内部状态
     * @param current           当前页
     * @param pageSize          每页行数
     * @return 查询结果
     */
    @Override
    public MobileOrderPageDTO pageMobileVendorValidateOrders(Long vendorUserId, List<Long> buyerMemberId, List<Long> buyerRoleId, String keyword, Integer vendorInnerStatus, int current, int pageSize) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;

        //Step 1: 首先分页查询订单表与其一对一关联的表（这样才可以分页）。不查询Srm订单和请购单订单
        JPAQuery<MobileOrderQueryDTO> query = jpaQueryFactory
                .selectDistinct(Projections.constructor(MobileOrderQueryDTO.class, qOrder.shopId, qOrder.id, qOrder.orderNo, qOrder.orderType, qOrder.orderMode, qOrder.orderKind, qOrder.vendorMemberId, qOrder.vendorRoleId, qOrder.vendorMemberName, qOrder.vendorLogo, qOrder.totalAmount, qOrder.vendorInnerStatus, qOrder.outerStatus, qOrder.buyerCommented, qOrder.hasPaid, qOrder.hasDelivered, qOrder.promotionStatus))
                .from(qOrder)
                .where(qOrder.buyerRoleId.in(buyerRoleId))
                .where(qOrder.vendorUserId.eq(vendorUserId));

        query.where(qOrder.orderType.in(OrderTypeEnum.SPOT_PURCHASING.getCode(), OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode()));

        //Step 2: 构造查询条件
        if (StringUtils.hasLength(keyword)) {
            String pattern = "%".concat(keyword.trim()).concat("%");
            query.where(qOrder.orderNo.like(pattern).or(qOrder.buyerMemberName.like(pattern)));
        }

        //供应商内部状态：待提交审核，待审核一级，待审核二级，待确认订单
        if (vendorInnerStatus == null) {
            query.where(qOrder.vendorInnerStatus.in(VendorInnerStatusEnum.VENDOR_TO_SUBMIT_VALIDATE.getCode(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_ONE.getCode(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_TWO.getCode(), VendorInnerStatusEnum.VENDOR_TO_CONFIRM.getCode()));
        } else {
            query.where(qOrder.vendorInnerStatus.eq(vendorInnerStatus));
        }

        //Step 3: 倒序排序、分页、总数
        long totalCount = query.fetchCount();
        query.orderBy(qOrder.id.desc());
        query.limit(pageSize).offset((long) (current - 1) * pageSize);

        List<MobileOrderQueryDTO> orders = query.fetch();

        //Step 4: 再查询订单商品表
        List<MobileOrderProductQueryDTO> orderProducts = jpaQueryFactory
                .select(Projections.constructor(MobileOrderProductQueryDTO.class,
                        qOrderProduct.order.id,
                        qOrderProduct.id,
                        qOrderProduct.productId,
                        qOrderProduct.skuId,
                        qOrderProduct.stockId,
                        qOrderProduct.priceType,
                        qOrderProduct.logo,
                        qOrderProduct.name,
                        qOrderProduct.category,
                        qOrderProduct.brand,
                        qOrderProduct.spec,
                        qOrderProduct.unit,
                        qOrderProduct.refPrice,
                        qOrderProduct.quantity,
                        qOrderProduct.deliverType,
                        qOrderProduct.address,
                        qOrderProduct.receiver,
                        qOrderProduct.phone
                )).from(qOrderProduct).where(qOrderProduct.order.id.in(orders.stream().map(MobileOrderQueryDTO::getOrderId).collect(Collectors.toList())))
                .fetch();

        //拼接订单商品
        orders.forEach(order -> order.setProducts(orderProducts.stream().filter(orderProduct -> orderProduct.getOrderId().equals(order.getOrderId())).collect(Collectors.toList())));

        return new MobileOrderPageDTO(totalCount, orders);
    }
}
