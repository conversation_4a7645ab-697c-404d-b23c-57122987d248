package com.ssy.lingxi.order.serviceImpl.base;

import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.DeliveryOuterHistoryDO;
import com.ssy.lingxi.order.enums.*;
import com.ssy.lingxi.order.model.bo.OrderIdBO;
import com.ssy.lingxi.order.model.resp.basic.DeliveryOuterHistoryResp;
import com.ssy.lingxi.order.repository.DeliveryOuterHistoryRepository;
import com.ssy.lingxi.order.service.base.IBaseDeliveryHistoryService;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 送货计划相关外部流转记录接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-19
 */
@Service
public class BaseDeliveryHistoryServiceImpl implements IBaseDeliveryHistoryService {

    @Resource
    private DeliveryOuterHistoryRepository deliveryOuterHistoryRepository;

    @Override
    public void saveBuyerOuterHistory(Long memberId, Long roleId, String userName, String organizationName, String roleName, Long orderId, String orderNo, DeliveryOperationEnum operation, Integer status, String remark, Integer DeliveryHistoryType) {
        saveDeliveryOuterHistory(memberId, roleId, userName, organizationName, roleName, Collections.singletonList(new OrderIdBO(orderId, orderNo)), OrderMemberTypeEnum.BUYER, operation, status, DeliveryOuterStatusEnum.getNameByCode(status), remark,DeliveryHistoryType);
    }

    @Override
    public void saveVendorOuterHistory(Long memberId, Long roleId, String userName, String organizationName, String roleName, Long orderId, String orderNo, DeliveryOperationEnum operation, Integer status, String remark, Integer DeliveryHistoryType) {
        saveDeliveryOuterHistory(memberId, roleId, userName, organizationName, roleName, Collections.singletonList(new OrderIdBO(orderId, orderNo)), OrderMemberTypeEnum.VENDOR, operation, status, DeliveryOuterStatusEnum.getNameByCode(status), remark,DeliveryHistoryType);
    }

    @Override
    public void saveVendorOuterHistoryByDeliveryOrder(Long memberId, Long roleId, String userName, String organizationName, String roleName, Long orderId, String orderNo, DeliveryOperationEnum operation, DeliveryOrderOuterStatusEnum statusEnum, String remark, Integer deliveryHistoryType) {
        saveDeliveryOuterHistory(memberId, roleId, userName, organizationName, roleName, Collections.singletonList(new OrderIdBO(orderId, orderNo)), OrderMemberTypeEnum.VENDOR, operation, statusEnum.getCode(), statusEnum.getName(), remark,deliveryHistoryType);
    }

    @Override
    public void saveVendorOuterHistoryByReceiveOrder(Long memberId, Long roleId, String userName, String organizationName, String roleName, Long orderId, String orderNo, DeliveryOperationEnum operation, ReceiveOrderOuterStatusEnum statusEnum, String remark, Integer deliveryHistoryType) {
        saveDeliveryOuterHistory(memberId, roleId, userName, organizationName, roleName, Collections.singletonList(new OrderIdBO(orderId, orderNo)), OrderMemberTypeEnum.VENDOR, operation, statusEnum.getCode(), statusEnum.getName(), remark,deliveryHistoryType);
    }

    @Async
    @Override
    public void saveDeliveryOuterHistory(Long memberId, Long roleId, String userName, String organizationName, String roleName, List<OrderIdBO> orderIds, OrderMemberTypeEnum memberType, DeliveryOperationEnum operation, Integer status, String statusName, String remark, Integer deliveryHistoryType) {
        List<DeliveryOuterHistoryDO> deliveryHistoryList = orderIds.stream().map(order -> {
            DeliveryOuterHistoryDO deliveryHistory = new DeliveryOuterHistoryDO();
            deliveryHistory.setCreateTime(LocalDateTime.now());
            deliveryHistory.setOrderId(order.getOrderId());
            deliveryHistory.setOrderNo(order.getOrderNo());
            deliveryHistory.setMemberId(memberId);
            deliveryHistory.setRoleId(roleId);
            deliveryHistory.setDeliveryHistoryType(deliveryHistoryType);
            deliveryHistory.setMemberType(memberType.getCode());
            deliveryHistory.setOperatorMemberName(userName);
            deliveryHistory.setOperatorRoleName(roleName);
            deliveryHistory.setOperateCode(operation.getCode());
            deliveryHistory.setOperation(operation.getName());
            deliveryHistory.setStatus(status);
            deliveryHistory.setStatusName(statusName);
            deliveryHistory.setRemark(StringUtils.hasLength(remark) ? remark.trim() : "");
            return deliveryHistory;
        }).collect(Collectors.toList());
        deliveryOuterHistoryRepository.saveAll(deliveryHistoryList);
    }

    @Override
    public List<DeliveryOuterHistoryResp> listDeliveryOuterHistory(Long orderId, Integer deliveryHistoryType) {
        return deliveryOuterHistoryRepository.findByOrderIdAndDeliveryHistoryType(orderId,deliveryHistoryType, Sort.by("id").descending())
                .stream().map(historyDO -> {
                    DeliveryOuterHistoryResp deliveryOuterHistoryResp = new DeliveryOuterHistoryResp();
                    deliveryOuterHistoryResp.setId(historyDO.getId());
                    deliveryOuterHistoryResp.setCreateTime(historyDO.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
                    deliveryOuterHistoryResp.setOperatorRoleName(historyDO.getOperatorRoleName());
                    deliveryOuterHistoryResp.setOperation(NumberUtil.isNullOrZero(historyDO.getOperateCode()) ? historyDO.getOperation() : DeliveryOperationEnum.getNameByCode(historyDO.getOperateCode()));
                    deliveryOuterHistoryResp.setStatusName(NumberUtil.isNullOrZero(historyDO.getStatus()) ? historyDO.getStatusName() : DeliveryHistoryStatusEnum.getNameByCode(historyDO.getStatus()));
                    if(DeliveryHistoryTypeEnum.DELIVERY_ORDER.getCode().equals(historyDO.getDeliveryHistoryType())){
                        deliveryOuterHistoryResp.setStatusName(DeliveryOrderOuterStatusEnum.getNameByCode(historyDO.getStatus()));
                    }
                    if(DeliveryHistoryTypeEnum.RECEIVE_ORDER.getCode().equals(historyDO.getDeliveryHistoryType())){
                        deliveryOuterHistoryResp.setStatusName(ReceiveOrderOuterStatusEnum.getNameByCode(historyDO.getStatus()));
                    }
                    deliveryOuterHistoryResp.setRemark(historyDO.getRemark());
                    return deliveryOuterHistoryResp;
                }).collect(Collectors.toList());
    }

}
