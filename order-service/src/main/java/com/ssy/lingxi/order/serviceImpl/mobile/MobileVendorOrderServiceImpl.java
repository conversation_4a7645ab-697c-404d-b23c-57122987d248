package com.ssy.lingxi.order.serviceImpl.mobile;

import cn.hutool.core.text.StrFormatter;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.sql.SQLExpressions;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberAbilitySalesFeign;
import com.ssy.lingxi.member.api.feign.IMobileMemberSalesFeign;
import com.ssy.lingxi.member.api.model.req.MemberSalesFindUserIdReq;
import com.ssy.lingxi.member.api.model.resp.MemberSalesFeignPageQueryResp;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.entity.QOrderDO;
import com.ssy.lingxi.order.enums.OrderOperationEnum;
import com.ssy.lingxi.order.enums.OrderOuterStatusEnum;
import com.ssy.lingxi.order.enums.OrderStringEnum;
import com.ssy.lingxi.order.enums.VendorInnerStatusEnum;
import com.ssy.lingxi.order.model.bo.OrderPaymentBO;
import com.ssy.lingxi.order.model.bo.PayNodeBO;
import com.ssy.lingxi.order.model.bo.ProcessTaskBO;
import com.ssy.lingxi.order.model.bo.UpdateOrderProductPriceBO;
import com.ssy.lingxi.order.model.dto.MobileOrderPageDTO;
import com.ssy.lingxi.order.model.req.basic.OrderAgreeReq;
import com.ssy.lingxi.order.model.req.basic.OrderIdReq;
import com.ssy.lingxi.order.model.req.mobile.MobileVendorOrderPageDataReq;
import com.ssy.lingxi.order.model.req.mobile.MobileVendorUpdateFreightReq;
import com.ssy.lingxi.order.model.req.mobile.MobileVendorValidateOrderPageDataReq;
import com.ssy.lingxi.order.model.req.vendor.VendorUpdatePayRateReq;
import com.ssy.lingxi.order.model.resp.mobile.MobileVendorOrderDetailResp;
import com.ssy.lingxi.order.model.resp.mobile.MobileVendorOrderQueryResp;
import com.ssy.lingxi.order.model.resp.mobile.MobileVendorValidateSubscriptResp;
import com.ssy.lingxi.order.repository.OrderRepository;
import com.ssy.lingxi.order.service.base.*;
import com.ssy.lingxi.order.service.mobile.IMobileVendorOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * App - 业务员（销售）订单接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-03-22
 **/
@Service
public class MobileVendorOrderServiceImpl implements IMobileVendorOrderService {
    @Resource
    private IBaseMobileOrderService baseMobileOrderService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private IBaseOrderProductService baseOrderProductService;
    @Resource
    private IBaseOrderPaymentService baseOrderPaymentService;
    @Resource
    private IBaseOrderHistoryService baseOrderHistoryService;
    @Resource
    private IBaseOrderService baseOrderService;
    @Resource
    private IBaseOrderTaskService baseOrderTaskService;
    @Resource
    private JPAQueryFactory jpaQueryFactory;
    @Resource
    private IMobileMemberSalesFeign mobileMemberSalesFeign;
    @Resource
    private IMemberAbilitySalesFeign memberAbilitySalesFeign;


    /**
     * 查看绑定订单 - 分页查询订单
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileVendorOrderQueryResp> pageOrders(UserLoginCacheDTO loginUser, MobileVendorOrderPageDataReq pageVO) {
        Long memberId = null;
        List<Long> memberRoleIds;
        if (pageVO.getMemberId() == null && pageVO.getMemberRoleId() == null) {
            MemberSalesFindUserIdReq salesVO = new MemberSalesFindUserIdReq();
            salesVO.setMemberId(loginUser.getMemberId());
            salesVO.setMemberRoleId(loginUser.getMemberRoleId());
            salesVO.setUserId(loginUser.getUserId());

            //查询当前业务员绑定的所有下级会员
            WrapperResp<List<Long>> resp = memberAbilitySalesFeign.roleIds();
            WrapperUtil.throwWhenFail(resp);

            memberRoleIds = resp.getData();
        } else {
            memberId = pageVO.getMemberId();
            memberRoleIds = Collections.singletonList(pageVO.getMemberRoleId());
        }

        MobileOrderPageDTO result = baseMobileOrderService.pageMobileVendorOrders(loginUser.getUserId(), memberId, memberRoleIds, pageVO.getMonth(), pageVO.getMemberName(), pageVO.getOrderType(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(MobileVendorOrderQueryResp::new).collect(Collectors.toList()));
    }

    /**
     * 查看绑定订单 - 订单详情
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public MobileVendorOrderDetailResp getOrderDetail(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        return baseMobileOrderService.getMobileVendorOrderDetail(loginUser, idVO.getOrderId());
    }

    /**
     * 订单审核 - 订单数量角标
     * @param loginUser 登录用户
     * @return 查询结果
     */
    @Override
    public List<MobileVendorValidateSubscriptResp> getOrderSubscripts(UserLoginCacheDTO loginUser) {
        MemberSalesFindUserIdReq salesVO = new MemberSalesFindUserIdReq();
        salesVO.setMemberId(loginUser.getMemberId());
        salesVO.setMemberRoleId(loginUser.getMemberRoleId());
        salesVO.setUserId(loginUser.getUserId());
        //查询当前业务员绑定的所有下级会员
        WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> resultVO = mobileMemberSalesFeign.getSalesList(salesVO);
        WrapperUtil.throwWhenFail(resultVO);

        List<Long> memberIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberId).collect(Collectors.toList());
        List<Long> memberRoleIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberRoleId).collect(Collectors.toList());

        QOrderDO qOrder = QOrderDO.orderDO;
        return jpaQueryFactory.select(Projections.constructor(MobileVendorValidateSubscriptResp.class, qOrder.vendorInnerStatus, SQLExpressions.count()))
                .from(qOrder)
                .where(qOrder.vendorUserId.eq(loginUser.getUserId()))
                .where(qOrder.buyerRoleId.in(memberRoleIds))
                .where(qOrder.vendorInnerStatus.in(VendorInnerStatusEnum.VENDOR_TO_SUBMIT_VALIDATE.getCode(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_ONE.getCode(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_TWO.getCode(), VendorInnerStatusEnum.VENDOR_TO_CONFIRM.getCode()))
                .where(qOrder.orderType.in(OrderTypeEnum.SPOT_PURCHASING.getCode(), OrderTypeEnum.INQUIRY_TO_PURCHASE.getCode()))
                .groupBy(qOrder.vendorInnerStatus)
                .fetch();
    }

    /**
     * 订单审核 - 分页查询列表
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileVendorOrderQueryResp> pageValidateOrders(UserLoginCacheDTO loginUser, MobileVendorValidateOrderPageDataReq pageVO) {
        MemberSalesFindUserIdReq salesVO = new MemberSalesFindUserIdReq();
        salesVO.setMemberId(loginUser.getMemberId());
        salesVO.setMemberRoleId(loginUser.getMemberRoleId());
        salesVO.setUserId(loginUser.getUserId());
        //查询当前业务员绑定的所有下级会员
        WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> resultVO = mobileMemberSalesFeign.getSalesList(salesVO);
        WrapperUtil.throwWhenFail(resultVO);

        List<Long> memberIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberId).collect(Collectors.toList());
        List<Long> memberRoleIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberRoleId).collect(Collectors.toList());

        MobileOrderPageDTO result = baseMobileOrderService.pageMobileVendorValidateOrders(loginUser.getUserId(), memberIds, memberRoleIds, pageVO.getKeyword(), null, pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(MobileVendorOrderQueryResp::new).collect(Collectors.toList()));
    }

    /**
     * “待提交审核订单” - 查询订单列表
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileVendorOrderQueryResp> pageSubmitToConfirmOrders(UserLoginCacheDTO loginUser, MobileVendorValidateOrderPageDataReq pageVO) {
        MemberSalesFindUserIdReq salesVO = new MemberSalesFindUserIdReq();
        salesVO.setMemberId(loginUser.getMemberId());
        salesVO.setMemberRoleId(loginUser.getMemberRoleId());
        salesVO.setUserId(loginUser.getUserId());
        //查询当前业务员绑定的所有下级会员
        WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> resultVO = mobileMemberSalesFeign.getSalesList(salesVO);
        WrapperUtil.throwWhenFail(resultVO);

        List<Long> memberIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberId).collect(Collectors.toList());
        List<Long> memberRoleIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberRoleId).collect(Collectors.toList());

        MobileOrderPageDTO result = baseMobileOrderService.pageMobileVendorValidateOrders(loginUser.getUserId(), memberIds, memberRoleIds, pageVO.getKeyword(), VendorInnerStatusEnum.VENDOR_TO_SUBMIT_VALIDATE.getCode(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(MobileVendorOrderQueryResp::new).collect(Collectors.toList()));
    }

    /**
     * “分页查询订单列表、待提交审核订单” - 修改运费和商品价格
     * @param loginUser 登录用户
     * @param freightVO 接口参数
     * @return 查询结果
     */
    @Override
    @Transactional
    public Void updateFreightAndProductPrices(UserLoginCacheDTO loginUser, MobileVendorUpdateFreightReq freightVO) {
        OrderDO order = orderRepository.findById(freightVO.getOrderId()).orElse(null);
        if (order == null || !order.getVendorMemberId().equals(loginUser.getMemberId()) || !order.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 判断条件
        //只有“待提交审核订单”，才能修改运费与到手价
        if (!order.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_TO_SUBMIT_VALIDATE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_STATUS_IS_NOT_MATCHED);
        }

        //如果已经支付过，不能修改
        if (order.getHasPaid()) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CAN_NOT_UPDATE_FREIGHT_WHEN_HAS_PAID);
        }

        //Step 2: 修改订单商品到手价、金额
        if (!CollectionUtils.isEmpty(freightVO.getPrices())) {
            UpdateOrderProductPriceBO productUpdateResult = baseOrderProductService.vendorUpdateProductPrices(order, freightVO.getPrices());
            //保存修改商品单价外部订单流转记录
            productUpdateResult.getRemarks().forEach(remark -> baseOrderHistoryService.saveVendorOrderOuterHistory(order.getId(), order.getOrderNo(), order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.MODIFY_UINT_PRICE, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), remark));

            order.setProductAmount(productUpdateResult.getTotalAmount());
        }

        //Step 3: 修改运费
        if (NumberUtil.notNullAndPositiveZero(freightVO.getFreight())) {
            //修改前运费+修改后运费+修改原因
            String remark = StrFormatter.format(OrderStringEnum.REMARK_FORMAT1.getName(), NumberUtil.formatAmount(order.getFreight()), NumberUtil.formatAmount(freightVO.getFreight()), freightVO.getReason());
            //保存修改运费外部订单流转记录
            baseOrderHistoryService.saveVendorOrderOuterHistory(order.getId(), order.getOrderNo(), order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.MODIFY_FREIGHT, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), remark);

            order.setFreight(freightVO.getFreight());
        }

        //Step 4: 修改订单总额
        order.setTotalAmount(order.getProductAmount().add(order.getFreight()).subtract(order.getPromotionAmount()).subtract(order.getCouponAmount()).subtract(Optional.ofNullable(order.getDeductionAmount()).orElse(BigDecimal.ZERO)).setScale(2, RoundingMode.HALF_UP));

        //Step 5: 修改支付记录中的支付金额
        baseOrderPaymentService.vendorUpdatePaymentRate(order);

        orderRepository.saveAndFlush(order);
        return null;
    }

    /**
     * “待提交审核订单” - 修改支付比例
     * @param loginUser 登录用户
     * @param payRateVO 接口参数
     * @return 查询结果
     */
    @Override
    public Void updatePayRate(UserLoginCacheDTO loginUser, VendorUpdatePayRateReq payRateVO) {
        OrderDO order = orderRepository.findById(payRateVO.getOrderId()).orElse(null);
        if (order == null || !order.getVendorMemberId().equals(loginUser.getMemberId()) || !order.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 判断条件
        //只有“待提交审核订单”，才能修改运费与到手价
        if (!order.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_TO_SUBMIT_VALIDATE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_STATUS_IS_NOT_MATCHED);
        }

        //如果已经支付过，不能修改
        if (order.getHasPaid()) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CAN_NOT_UPDATE_PAY_RATE_WHEN_HAS_PAID);
        }

        //Step 2: 修改支付比例设置
        List<PayNodeBO> payNodes = payRateVO.getPayRates().stream().map(p -> new PayNodeBO(0, p.getBatchNo(), 1, p.getPayRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP))).collect(Collectors.toList());
        return baseOrderPaymentService.vendorUpdatePaymentRate(order, payNodes);
    }

    /**
     * “待审核订单（一级）” - 查询订单列表
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileVendorOrderQueryResp> pageToValidateGradeOneOrders(UserLoginCacheDTO loginUser, MobileVendorValidateOrderPageDataReq pageVO) {
        MemberSalesFindUserIdReq salesVO = new MemberSalesFindUserIdReq();
        salesVO.setMemberId(loginUser.getMemberId());
        salesVO.setMemberRoleId(loginUser.getMemberRoleId());
        salesVO.setUserId(loginUser.getUserId());
        //查询当前业务员绑定的所有下级会员
        WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> resultVO = mobileMemberSalesFeign.getSalesList(salesVO);
        WrapperUtil.throwWhenFail(resultVO);

        List<Long> memberIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberId).collect(Collectors.toList());
        List<Long> memberRoleIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberRoleId).collect(Collectors.toList());

        MobileOrderPageDTO result = baseMobileOrderService.pageMobileVendorValidateOrders(loginUser.getUserId(), memberIds, memberRoleIds, pageVO.getKeyword(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_ONE.getCode(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(MobileVendorOrderQueryResp::new).collect(Collectors.toList()));
    }

    /**
     * “待提交审核订单” - 提交审核
     * @param loginUser 登录用户
     * @param agreeVO 接口参数
     * @return 查询结果
     */
    @Override
    public Void submitToConfirmOrder(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        OrderDO order = orderRepository.findById(agreeVO.getOrderId()).orElse(null);
        if (order == null || !order.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_TO_SUBMIT_VALIDATE.getCode()) || !order.getVendorMemberId().equals(loginUser.getMemberId()) || !order.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 参数判断
        if (agreeVO.getAgree().equals(OrderConstant.DISAGREE) && !StringUtils.hasLength(agreeVO.getReason())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VALIDATE_REASON_CAN_NOT_BE_EMPTY);
        }

        //记录上一次的供应商状态，用于发送报表数据
        int lastVendorInnerStatus = order.getVendorInnerStatus();

        //Step 2: 执行工作流任务（修改订单状态、记录内外流转记录）
        ProcessTaskBO taskResult = baseOrderTaskService.execOrderProcess(order, agreeVO.getAgree());

        //Step 3: 如果是拼团订单，向营销服务发送拼团数据，等待拼团结果通知
        baseOrderService.notifyGroupOrder(order);

        orderRepository.saveAndFlush(order);

        //Step 4: 向消息服务发送供应购商实时消息
        baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());

        //Step 5: 订单内部流转记录
        baseOrderHistoryService.saveVendorInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.SUBMIT_VALIDATE, order.getVendorInnerStatus(), StringUtils.hasLength(agreeVO.getReason()) ? agreeVO.getReason() : "");

        return null;
    }

    /**
     * “待审核订单（一级）” - 提交审核
     * @param loginUser 登录用户
     * @param agreeVO 接口参数
     * @return 查询结果
     */
    @Override
    public Void validateOrderGradeOne(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        OrderDO order = orderRepository.findById(agreeVO.getOrderId()).orElse(null);
        if (order == null || !order.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_ONE.getCode()) || !order.getVendorMemberId().equals(loginUser.getMemberId()) || !order.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 参数判断
        if (agreeVO.getAgree().equals(OrderConstant.DISAGREE) && !StringUtils.hasLength(agreeVO.getReason())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VALIDATE_REASON_CAN_NOT_BE_EMPTY);
        }

        //记录上一次的供应商状态，用于发送报表数据
        int lastVendorInnerStatus = order.getVendorInnerStatus();

        //Step 2: 执行工作流任务（修改订单状态、记录内外流转记录）
        ProcessTaskBO taskResult = baseOrderTaskService.execOrderProcess(order, agreeVO.getAgree());

        orderRepository.saveAndFlush(order);

        //Step 3: 向消息服务发送供应购商实时消息
        baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());

        //Step 4: 订单内部流转记录
        baseOrderHistoryService.saveVendorInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.VALIDATE_GRADE_ONE, order.getVendorInnerStatus(), StringUtils.hasLength(agreeVO.getReason()) ? agreeVO.getReason() : "");

        return null;
    }

    /**
     * “待审核订单（二级）” - 查询订单列表
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileVendorOrderQueryResp> pageToValidateGradeTwoOrders(UserLoginCacheDTO loginUser, MobileVendorValidateOrderPageDataReq pageVO) {
        MemberSalesFindUserIdReq salesVO = new MemberSalesFindUserIdReq();
        salesVO.setMemberId(loginUser.getMemberId());
        salesVO.setMemberRoleId(loginUser.getMemberRoleId());
        salesVO.setUserId(loginUser.getUserId());
        //查询当前业务员绑定的所有下级会员
        WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> resultVO = mobileMemberSalesFeign.getSalesList(salesVO);
        WrapperUtil.throwWhenFail(resultVO);

        List<Long> memberIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberId).collect(Collectors.toList());
        List<Long> memberRoleIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberRoleId).collect(Collectors.toList());

        MobileOrderPageDTO result = baseMobileOrderService.pageMobileVendorValidateOrders(loginUser.getUserId(), memberIds, memberRoleIds, pageVO.getKeyword(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_TWO.getCode(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(MobileVendorOrderQueryResp::new).collect(Collectors.toList()));
    }

    /**
     * “待审核订单（二级）” - 提交审核
     * @param loginUser 登录用户
     * @param agreeVO 接口参数
     * @return 查询结果
     */
    @Override
    public Void validateOrderGradeTwo(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        OrderDO order = orderRepository.findById(agreeVO.getOrderId()).orElse(null);
        if (order == null || !order.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_TWO.getCode()) || !order.getVendorMemberId().equals(loginUser.getMemberId()) || !order.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 参数判断
        if (agreeVO.getAgree().equals(OrderConstant.DISAGREE) && !StringUtils.hasLength(agreeVO.getReason())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VALIDATE_REASON_CAN_NOT_BE_EMPTY);
        }

        //记录上一次的供应商状态，用于发送报表数据
        int lastVendorInnerStatus = order.getVendorInnerStatus();

        //Step 2: 执行工作流任务（修改订单状态、记录内外流转记录）
        ProcessTaskBO taskResult = baseOrderTaskService.execOrderProcess(order, agreeVO.getAgree());

        orderRepository.saveAndFlush(order);

        //Step 3: 向消息服务发送供应购商实时消息
        baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());

        //Step 4: 订单内部流转记录
        baseOrderHistoryService.saveVendorInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.VALIDATE_GRADE_TWO, order.getVendorInnerStatus(), StringUtils.hasLength(agreeVO.getReason()) ? agreeVO.getReason() : "");

        return null;
    }

    /**
     * “待确认订单” - 查询订单列表
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileVendorOrderQueryResp> pageToConfirmOrders(UserLoginCacheDTO loginUser, MobileVendorValidateOrderPageDataReq pageVO) {
        MemberSalesFindUserIdReq salesVO = new MemberSalesFindUserIdReq();
        salesVO.setMemberId(loginUser.getMemberId());
        salesVO.setMemberRoleId(loginUser.getMemberRoleId());
        salesVO.setUserId(loginUser.getUserId());
        //查询当前业务员绑定的所有下级会员
        WrapperResp<PageDataResp<MemberSalesFeignPageQueryResp>> resultVO = mobileMemberSalesFeign.getSalesList(salesVO);
        WrapperUtil.throwWhenFail(resultVO);

        List<Long> memberIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberId).collect(Collectors.toList());
        List<Long> memberRoleIds = resultVO.getData().getData().stream().map(MemberSalesFeignPageQueryResp::getMemberRoleId).collect(Collectors.toList());
//        List<Integer> vendorInnerStatuses = Stream.of(VendorInnerStatusEnum.VENDOR_SUBMIT_VALIDATE_NOT_PASSED.getCode(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_ONE_NOT_PASSED.getCode(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_TWO_NOT_PASSED.getCode(), VendorInnerStatusEnum.VENDOR_TO_CONFIRM.getCode()).collect(Collectors.toList());
        MobileOrderPageDTO result = baseMobileOrderService.pageMobileVendorValidateOrders(loginUser.getUserId(), memberIds, memberRoleIds, pageVO.getKeyword(), VendorInnerStatusEnum.VENDOR_TO_CONFIRM.getCode(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(result.getTotalCount(), result.getOrders().stream().map(MobileVendorOrderQueryResp::new).collect(Collectors.toList()));
    }

    /**
     * “待确认订单” - 确认
     * @param loginUser 登录用户
     * @param agreeVO 接口参数
     * @return 查询结果
     */
    @Override
    public Void confirmOrder(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        List<Integer> vendorInnerStatuses = Stream.of(VendorInnerStatusEnum.VENDOR_SUBMIT_VALIDATE_NOT_PASSED.getCode(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_ONE_NOT_PASSED.getCode(), VendorInnerStatusEnum.VENDOR_VALIDATE_GRADE_TWO_NOT_PASSED.getCode(), VendorInnerStatusEnum.VENDOR_TO_CONFIRM.getCode()).collect(Collectors.toList());
        OrderDO order = orderRepository.findById(agreeVO.getOrderId()).orElse(null);
        if (order == null || !vendorInnerStatuses.contains(order.getVendorInnerStatus()) || !order.getVendorMemberId().equals(loginUser.getMemberId()) || !order.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 参数判断
        if (agreeVO.getAgree().equals(OrderConstant.DISAGREE) && !StringUtils.hasLength(agreeVO.getReason())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VALIDATE_REASON_CAN_NOT_BE_EMPTY);
        }

        //记录上一次的供应商状态，用于发送报表数据
        int lastVendorInnerStatus = order.getVendorInnerStatus();

        //Step 2: 执行工作流任务（修改订单状态、记录内外流转记录）
        ProcessTaskBO taskResult = baseOrderTaskService.execOrderProcess(order, agreeVO.getAgree());

        //Step 3 : 确认订单之后有可能是支付环节，判断是否跳过支付环节
        if (agreeVO.getAgree().equals(OrderConstant.AGREE)) {
            OrderPaymentBO jumpResult = baseOrderPaymentService.jumpOverOrderPaySerialTasks(order);
        }

        //Step 4: 如果是拼团订单，向营销服务发送拼团数据，等待拼团结果通知
        baseOrderService.notifyGroupOrder(order);

        orderRepository.saveAndFlush(order);

        //Step 5: 向消息服务发送采购商、供应购商实时消息
        baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());
        baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

        //Step 6: 订单内、外流转记录
        baseOrderHistoryService.saveVendorInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), agreeVO.getAgree().equals(OrderConstant.AGREE) ? OrderOperationEnum.ACCEPT : OrderOperationEnum.REFUSE, order.getVendorInnerStatus(), StringUtils.hasLength(agreeVO.getReason()) ? agreeVO.getReason() : "");
        baseOrderHistoryService.saveVendorOrderOuterHistory(order.getId(), order.getOrderNo(), order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorMemberName(), loginUser.getMemberRoleName(), agreeVO.getAgree().equals(OrderConstant.AGREE) ? OrderOperationEnum.ACCEPT : OrderOperationEnum.REFUSE, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

        return null;
    }
}