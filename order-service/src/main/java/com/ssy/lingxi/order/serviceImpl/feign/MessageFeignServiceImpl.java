package com.ssy.lingxi.order.serviceImpl.feign;

import com.ssy.lingxi.component.base.service.IMessageService;
import com.ssy.lingxi.component.rabbitMQ.model.req.SystemMessageReq;
import com.ssy.lingxi.order.service.feign.IMessageFeignService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 消息服务Feign接口调用实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-16
 */
@Service
public class MessageFeignServiceImpl implements IMessageFeignService {
    @Resource
    private IMessageService messageService;

    /**
     * 发送采购商、供应商的订单消息
     *
     * @param memberId    会员Id
     * @param roleId      会员角色Id
     * @param messageCode 操作编码
     * @param orderNo     订单号
     * @param digest      订单摘要
     */
    @Async
    @Override
    public void sendOrderMessageAsync(Long memberId, Long roleId, Long userId, String messageCode, String orderNo, String digest) {
        List<String> parameters = Stream.of(orderNo, digest).collect(Collectors.toList());
        SystemMessageReq systemMessageReq = new SystemMessageReq();
        systemMessageReq.setMemberId(memberId);
        systemMessageReq.setRoleId(roleId);
        systemMessageReq.setMessageNotice(messageCode);
        systemMessageReq.setParams(parameters);
        systemMessageReq.setUserId(userId);
        //将消息存入队列中
        messageService.sendSystemMessage(systemMessageReq);
    }


    /**
     * 发送采购商、供应商的订单消息
     * @param memberId    会员Id
     * @param roleId      会员角色Id
     * @param messageCode 操作编码
     * @param params      消息参数列表
     */
    @Async
    @Override
    public void sendOrderMessageAsync(Long memberId, Long roleId, Long userId, String messageCode, List<String> params) {
        SystemMessageReq systemMessageReq = new SystemMessageReq();
        systemMessageReq.setMemberId(memberId);
        systemMessageReq.setRoleId(roleId);
        systemMessageReq.setMessageNotice(messageCode);
        systemMessageReq.setParams(params);
        systemMessageReq.setUserId(userId);
        //将消息存入队列中
        messageService.sendSystemMessage(systemMessageReq);
    }
}
