package com.ssy.lingxi.order.serviceImpl.feign;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.model.bo.RequisitionBO;
import com.ssy.lingxi.order.service.feign.IPurchaseFeignService;
import com.ssy.lingxi.purchase.api.feign.IPurchaseQuoteFeign;
import com.ssy.lingxi.purchase.api.feign.IPurchaseRequisitionFeign;
import com.ssy.lingxi.purchase.api.model.req.RequisitionProductReq;
import com.ssy.lingxi.purchase.api.model.req.RequisitionQuantityReq;
import com.ssy.lingxi.purchase.api.model.resp.purchase.QuotedMaterielResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 采购服务Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-11-08
 */
@Service
public class PurchaseFeignServiceImpl implements IPurchaseFeignService {
    @Resource
    private IPurchaseRequisitionFeign purchaseRequisitionFeign;

    @Resource
    private IPurchaseQuoteFeign purchaseQuoteFeign;

    /**
     * 请购单转请购订单扣减请购数量、增加已转订单数量
     * @param order 请购单订单
     */
    @Override
    public void requisitionQuantityDeduct(OrderDO order) {
        this.quantityDeduct(order, new HashMap<>());
    }

    /**
     * 请购单转请购订单扣减请购数量、增加已转订单数量
     * @param order 请购单订单
     */
    @Override
    public void requisitionQuantityDeduct(OrderDO order, Map<Long, BigDecimal> changeQuantity) {
        this.quantityDeduct(order, changeQuantity);
    }

    /**
     * 获取报价单信息
     */
    @Override
    public List<QuotedMaterielResp> getPurchaseQuoteDetails(Long id) {
        return WrapperUtil.getDataOrThrow(purchaseQuoteFeign.getPurchaseQuoteDetails(id));
    }

    /**
     * 请购单转请购订单扣减请购数量、增加已转订单数量
     * @param order 请购单订单
     */
    public void quantityDeduct(OrderDO order, Map<Long, BigDecimal> changeQuantity) {
        if (OrderTypeEnum.REQUISITION_TO_PURCHASE.getCode().equals(order.getOrderType())){
            RequisitionQuantityReq req = new RequisitionQuantityReq();
            req.setRequisitionId(order.getQuoteId());
            req.setOrderType(order.getOrderType());
            req.setProducts(order.getProducts().stream().map(product -> {
                RequisitionProductReq productReq = new RequisitionProductReq();
                productReq.setProductId(product.getProductId());
                productReq.setQuantity(Optional.ofNullable(changeQuantity.get(product.getProductId())).orElse(product.getQuantity()));
                return productReq;
            }).collect(Collectors.toList()));
            WrapperResp<Void> result = purchaseRequisitionFeign.requisitionQuantityDeduct(req);
            WrapperUtil.throwWhenFail(result);
        }else if (OrderTypeEnum.REQUISITION_CONTRACT.getCode().equals(order.getOrderType())){
            RequisitionQuantityReq req = new RequisitionQuantityReq();
            req.setOrderType(order.getOrderType());
            List<RequisitionProductReq> requisitionProductReqs = new ArrayList<>();
            order.getProducts().forEach(product -> {
                List<RequisitionBO> requisitions = product.getMaterial().getRequisitions();
                List<RequisitionProductReq> collect = requisitions.stream().map(requisitionBO -> {
                    RequisitionProductReq productReq = new RequisitionProductReq();
                    productReq.setPurchaseProductId(requisitionBO.getRequisitionProductId());
                    productReq.setQuantity(Optional.ofNullable(changeQuantity.get(requisitionBO.getRequisitionProductId())).orElse(BigDecimal.valueOf(requisitionBO.getOrderQuantity())));
                    return productReq;
                }).collect(Collectors.toList());
                requisitionProductReqs.addAll(collect);
            });
            req.setProducts(requisitionProductReqs);
            WrapperResp<Void> result = purchaseRequisitionFeign.requisitionQuantityDeduct(req);
            WrapperUtil.throwWhenFail(result);
        }
    }

}
