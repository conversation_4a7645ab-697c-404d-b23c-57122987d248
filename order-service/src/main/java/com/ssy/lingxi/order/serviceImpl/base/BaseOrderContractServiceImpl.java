//package com.ssy.lingxi.order.serviceImpl.base;
//
//import com.ssy.lingxi.common.util.NumberUtil;
//import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
//import com.ssy.lingxi.component.base.enums.contract.ContractSourceTypeEnum;
//import com.ssy.lingxi.component.base.model.BusinessException;
//import com.ssy.lingxi.contract.api.model.resp.OrderContractResp;
//import com.ssy.lingxi.order.common.constant.OrderConstant;
//import com.ssy.lingxi.order.entity.OrderContractDO;
//import com.ssy.lingxi.order.entity.OrderDO;
//import com.ssy.lingxi.order.model.req.basic.OrderContractReq;
//import com.ssy.lingxi.order.model.req.basic.SrmOrderContractChangeReq;
//import com.ssy.lingxi.order.model.req.basic.SrmOrderContractReq;
//import com.ssy.lingxi.order.repository.OrderContractRepository;
//import com.ssy.lingxi.order.service.base.IBaseOrderContractService;
//import com.ssy.lingxi.order.service.feign.IContractFeignService;
//import org.springframework.stereotype.Service;
//import org.springframework.util.StringUtils;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.Objects;
//
///**
// * 订单合同相关接口实现类
// * <AUTHOR>
// * @version 2.0.0
// * @since 2021-07-19
// */
//@Service
//public class BaseOrderContractServiceImpl implements IBaseOrderContractService {
//    @Resource
//    private OrderContractRepository orderContractRepository;
//
//    @Resource
//    private IContractFeignService contractFeignService;
//
//    /**
//     * 校验并生成订单合同，调用方需要保存OrderDO
//     *
//     * @param order      订单
//     * @param contractVO 订单合同接口参数
//     * @param isCreate 是否新增， true-新增，false-修改
//     */
//    @Override
//    public void checkOrderContract(OrderDO order, OrderContractReq contractVO, boolean isCreate) {
//        if(!isCreate) {
//            orderContractRepository.deleteByOrder(order);
//        }
//
//        //订单可以不需要合同
//        if(contractVO == null) {
//            order.setContract(null);
//            return;
//        }
//
//        OrderContractDO contract = new OrderContractDO();
//        contract.setOrder(order);
//        contract.setContractId(0L);
//        contract.setContractNo("");
//        contract.setDigest("");
//        contract.setEffectDate(null);
//        contract.setExpireDate(null);
//        contract.setPartB("");
//        //采购订单没有合同类型，设置为Null
//        contract.setContractType(null);
//        contract.setLeftAmount(null);
//        contract.setReceiptNo("");
//        contract.setFileName(contractVO.getFileName());
//        contract.setUrl(contractVO.getUrl());
//
//        order.setContract(contract);
//    }
//
//    /**
//     * 校验并生成Srm订单合同，调用方需要保存OrderDO
//     *
//     * @param order      订单
//     * @param contractVO 订单合同接口参数
//     * @param isCreate 是否新增， true-新增，false-修改
//     */
//    @Override
//    public void checkSrmOrderContract(OrderDO order, SrmOrderContractReq contractVO, boolean isCreate) {
//        if(!isCreate) {
//            orderContractRepository.deleteByOrder(order);
//        }
//
//        //Srm订单必须有合同
//        // todo 目前再选取合同中过滤 已创建新合同，新合同未审核通过，旧合同状态未变为“已停用”，提示“当前合同已变更，无法下单”
//        // 调用接口合同服务接口
//        ContractOneIdReq contractOneIdReq = new ContractOneIdReq(contractVO.getContractId());
//        if (contractFeignService.checkContractIsChange(contractOneIdReq)) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_CONTRACT_HAS_BEEN_CHANGE);
//        }
//
//        OrderContractDO contract = Objects.isNull(order.getContract()) ? new OrderContractDO() : order.getContract();
//        contract.setOrder(order);
//        contract.setContractId(contractVO.getContractId());
//        contract.setContractNo(contractVO.getContractNo());
//        contract.setDigest(contractVO.getDigest());
//        contract.setEffectDate(LocalDateTime.parse(contractVO.getEffectDate().concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER));
//        contract.setExpireDate(LocalDateTime.parse(contractVO.getExpireDate().concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER));
//        contract.setPartB(contractVO.getPartB());
//        contract.setContractType(contractVO.getContractType());
//        contract.setLeftAmount(contractVO.getLeftAmount());
//        contract.setReceiptNo(StringUtils.hasLength(contractVO.getReceiptNo()) ? contractVO.getReceiptNo() : "");
//        contract.setFileName("");
//        contract.setUrl("");
//
//        order.setContract(contract);
//    }
//
//    /**
//     * 校验并生成Srm订单合同，调用方需要保存OrderDO
//     *
//     * @param order      订单
//     * @param contractVO 订单合同接口参数
//     * @param isCreate 是否新增， true-新增，false-修改
//     */
//    @Override
//    public void checkSrmOrderContract(OrderDO order, SrmOrderContractChangeReq contractVO, boolean isCreate) {
//        if(!isCreate) {
//            orderContractRepository.deleteByOrder(order);
//        }
//
//        //Srm订单必须有合同
//        // todo 目前再选取合同中过滤 已创建新合同，新合同未审核通过，旧合同状态未变为“已停用”，提示“当前合同已变更，无法下单”
//        // 调用接口合同服务接口
//        ContractOneIdReq contractOneIdReq = new ContractOneIdReq(contractVO.getContractId());
//        if (contractFeignService.checkContractIsChange(contractOneIdReq)) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_CHANGE_CONTRACT_HAS_BEEN_CHANGE);
//        }
//
//        OrderContractDO contract = Objects.isNull(order.getContract()) ? new OrderContractDO() : order.getContract();
//        contract.setOrder(order);
//        contract.setContractId(contractVO.getContractId());
//        contract.setContractNo(contractVO.getContractNo());
//        contract.setDigest(contractVO.getDigest());
//        contract.setEffectDate(LocalDateTime.parse(contractVO.getEffectDate().concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER));
//        contract.setExpireDate(LocalDateTime.parse(contractVO.getExpireDate().concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER));
//        contract.setPartB(contractVO.getPartB());
//        contract.setContractType(contractVO.getContractType());
//        contract.setLeftAmount(contractVO.getLeftAmount());
//        contract.setReceiptNo(StringUtils.hasLength(contractVO.getReceiptNo()) ? contractVO.getReceiptNo() : "");
//        contract.setFileName("");
//        contract.setUrl("");
//
//        order.setContract(contract);
//    }
//
//    /**
//     * （转单） - 生成合同
//     *
//     * @param order      订单
//     * @param contractId 交易流程中配置的合同Id
//     */
//    @Override
//    public void transferOrderContract(OrderDO order, Long contractId) {
//        //根据合同Id是否大于0来判断
//        if(NumberUtil.isNullOrZero(contractId)) {
//            order.setContract(null);
//            return;
//        }
//
//        OrderContractDO contract = new OrderContractDO();
//        contract.setOrder(order);
//        contract.setContractId(contractId);
//        contract.setContractNo("");
//        contract.setDigest("");
//        contract.setEffectDate(null);
//        contract.setExpireDate(null);
//        contract.setPartB("");
//        contract.setContractType(null);
//        contract.setLeftAmount(null);
//        contract.setReceiptNo("");
//        contract.setFileName("");
//        contract.setUrl("");
//
//        order.setContract(contract);
//    }
//
//    /**
//     * 查询订单合同信息
//     *
//     * @param order 订单
//     * @return 合同信息
//     */
//    @Override
//    public OrderContractResp getOrderContract(OrderDO order) {
//        contractFeignService.
//        OrderContractDO contract = orderContractRepository.findFirstByOrder(order);
//        if(contract == null) {
//            return null;
//        }
//
//        OrderContractResp detailVO = new OrderContractResp();
//        detailVO.setContractId(contract.getContractId());
//        detailVO.setContractNo(contract.getContractNo());
//        detailVO.setDigest(contract.getDigest());
//        detailVO.setEffectDate(contract.getEffectDate() == null ? "" : contract.getEffectDate().format(OrderConstant.DEFAULT_DATE_FORMATTER));
//        detailVO.setExpireDate(contract.getExpireDate() == null ? "" : contract.getExpireDate().format(OrderConstant.DEFAULT_DATE_FORMATTER));
//        detailVO.setPartB(contract.getPartB());
//        detailVO.setContractType(contract.getContractType() == null ? -1 : contract.getContractType());
//        detailVO.setContractTypeName(contract.getContractType() == null ? "" : ContractSourceTypeEnum.getNameByCode(contract.getContractType()));
//        detailVO.setLeftAmount(contract.getLeftAmount() == null ? "" : NumberUtil.formatAmount(contract.getLeftAmount()));
//        contract.setReceiptNo(contract.getReceiptNo());
//        detailVO.setFileName(contract.getFileName());
//        detailVO.setUrl(contract.getUrl());
//        return detailVO;
//    }
//}
