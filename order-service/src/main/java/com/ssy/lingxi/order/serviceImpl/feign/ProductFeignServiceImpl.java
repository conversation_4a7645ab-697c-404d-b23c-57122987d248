package com.ssy.lingxi.order.serviceImpl.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.constant.mq.ProductMqConstant;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.enums.product.PriceTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.rabbitMQ.model.dto.CommoditySoldDTO;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.member.api.model.req.MemberFeignRelationReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignRelationRightDetailResp;
import com.ssy.lingxi.order.enums.InboundOrderEnum;
import com.ssy.lingxi.order.enums.OrderFreightTypeEnum;
import com.ssy.lingxi.order.enums.OrderProductDeliverTypeEnum;
import com.ssy.lingxi.order.enums.OrderPromotionTypeEnum;
import com.ssy.lingxi.order.model.bo.InventoryCustomProductBO;
import com.ssy.lingxi.order.model.bo.InventoryProductBO;
import com.ssy.lingxi.order.model.bo.OrderProductBO;
import com.ssy.lingxi.order.model.bo.VendorProductBO;
import com.ssy.lingxi.order.model.dto.*;
import com.ssy.lingxi.order.model.resp.basic.OccupyInventoryProductPositionResp;
import com.ssy.lingxi.order.model.resp.basic.OutOfStockOrderProductDetailResp;
import com.ssy.lingxi.order.model.resp.basic.WarehousingOrderProductDetailResp;
import com.ssy.lingxi.order.service.feign.IMemberFeignService;
import com.ssy.lingxi.order.service.feign.IProductFeignService;
import com.ssy.lingxi.product.api.enums.FreightSpaceSingleProductStatusEnum;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.feign.IFreightSpaceFeign;
import com.ssy.lingxi.product.api.feign.IWarehouseFeign;
import com.ssy.lingxi.product.api.model.req.feign.CommodityPriceReq;
import com.ssy.lingxi.product.api.model.req.feign.CommoditySinglePriceReq;
import com.ssy.lingxi.product.api.model.req.warehouse.*;
import com.ssy.lingxi.product.api.model.resp.commodity.*;
import com.ssy.lingxi.product.api.model.resp.warehouse.OrderCommodityOccupiedInventoryResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品服务Feign接口调用类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-30
 */
@Slf4j
@Service
public class ProductFeignServiceImpl implements IProductFeignService {
    @Resource
    private IFreightSpaceFeign freightSpaceFeign;
    @Resource
    private ICommodityFeign commodityFeign;
    @Resource
    private IMemberFeignService memberFeignService;
    @Resource
    private IWarehouseFeign warehouseFeign;
    @Resource
    private IMqUtils mqUtils;

    /**
     * 批量校验商品价格
     *
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param shopId        商城Id
     * @param memberType    会员类型，用于判断是会员商品，还是渠道商品
     * @param skuIds        商品SkuId列表
     * @return 校验结果
     */
    @Override
    public Map<Long, Map<String, BigDecimal>> batchCheckProductPrice(Long buyerMemberId, Long buyerRoleId, Long shopId, Integer memberType, List<Long> skuIds) {
        try {
            CommodityPriceReq request = new CommodityPriceReq();
            request.setMemberId(buyerMemberId);
            request.setMemberRoleId(buyerRoleId);
            request.setShopId(shopId);
            request.setCommoditySkuIdList(skuIds);
            return WrapperUtil.getDataOrThrow(commodityFeign.getCommodityPriceBatch(request));
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_PRODUCT_ERROR);
        }
    }

    /**
     * 扣减供应商库存
     *
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param orderId       订单Id
     * @param shopId        商城Id
     * @param shopType      商城类型（用于判断是渠道商城还是会员商城）
     * @param products      商品和采购数量
     */
    @Override
    public void deductInventory(Long buyerMemberId, Long buyerRoleId, Long orderId, Long shopId, Integer shopType, List<InventoryProductBO> products) {
        InventoryByProductIdReq request = new InventoryByProductIdReq();
        request.setSupplyMembersId(buyerMemberId);
        request.setMemberRoleId(buyerRoleId);
        request.setOrderId(orderId);
        request.setShopId(shopId);
        request.setDeductInventoryRequests(products.stream().map(product -> {
            DeductInventoryReq deductInventoryReq = new DeductInventoryReq();
            deductInventoryReq.setProductId(product.getProductId());
            deductInventoryReq.setProductSkuId(product.getSkuId());
            deductInventoryReq.setProductName(product.getProductName());
            deductInventoryReq.setPurchaseCount(product.getQuantity());
            return deductInventoryReq;
        }).collect(Collectors.toList()));
        log.info("旧接口扣除库存信息:{}",JSONUtil.toJsonStr(request));
        WrapperResp<Void> wrapperResp = freightSpaceFeign.deductInventory(request);
        WrapperUtil.throwWhenFail(wrapperResp);
    }

    /**
     * 返还供应商库存
     *
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param orderId       订单Id
     * @param shopId        商城Id
     * @param shopType      商城类型（用于判断是渠道商城还是会员商城）
     * @param products      商品和返还数量
     */
    @Override
    public void resumeInventory(Long buyerMemberId, Long buyerRoleId, Long orderId, Long shopId, Integer shopType, List<InventoryProductBO> products) {
        InventoryByProductIdReq request = new InventoryByProductIdReq();
        request.setSupplyMembersId(buyerMemberId);
        request.setMemberRoleId(buyerRoleId);
        request.setOrderId(orderId);
        request.setShopId(shopId);
        request.setDeductInventoryRequests(products.stream().map(product -> {
            DeductInventoryReq r = new DeductInventoryReq();
            r.setProductId(product.getProductId());
            r.setProductSkuId(product.getSkuId());
            r.setProductName(product.getProductName());
            r.setPurchaseCount(product.getQuantity());
            return r;
        }).collect(Collectors.toList()));
        WrapperResp<Void> result = freightSpaceFeign.returnInventory(request);
        WrapperUtil.throwWhenFail(result);
    }

    /**
     * 根据订单中的上游供应商与供应商商品，查询上游供应商商品信息
     *
     * @param roleId         当前订单供应商要转单的服务消费者角色Id
     * @param vendorProducts 供应商会“会员商品”SkuId列表
     * @return 上游供应商商品列表
     */
    @Override
    public List<SupplyProductDTO> findSupplyProducts(Long roleId, List<VendorProductBO> vendorProducts) {
        List<Long> skuIds = vendorProducts.stream().flatMap(vendorProduct -> vendorProduct.getProducts().stream().map(OrderProductBO::getSkuId)).distinct().collect(Collectors.toList());
        WrapperResp<List<UpperCommoditySkuResp>> commodityResult = commodityFeign.getUpperCommodityByCommoditySkuIdList(skuIds);
        WrapperUtil.throwWhenFail(commodityResult);

        //如果返回结果中包含了“允许使用会员折扣购买”，查询会员折扣
        List<MemberFeignRelationReq> vendorRelations = commodityResult.getData().stream().filter(CommodityResp::getIsMemberPrice).map(response -> {
            VendorProductBO vendorProduct = vendorProducts.stream().filter(vendorProductBO -> vendorProductBO.getProducts().stream().anyMatch(product -> product.getSkuId().equals(response.getSubCommoditySkuId()))).findFirst().orElse(null);
            if (vendorProduct == null) {
                return null;
            } else {
                MemberFeignRelationReq relationVO = new MemberFeignRelationReq();
                relationVO.setSubMemberId(vendorProduct.getVendorMemberId());
                relationVO.setSubRoleId(roleId);
                relationVO.setUpperMemberId(response.getMemberId());
                relationVO.setUpperRoleId(response.getMemberRoleId());
                return relationVO;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        //从会员服务查询会员折扣
        List<MemberFeignRelationRightDetailResp> memberResult = memberFeignService.batchFindMemberPriceRight(vendorRelations);

        return commodityResult.getData().stream().map(commodity -> {
            SupplyProductDTO product = new SupplyProductDTO();
            product.setVendorSkuId(commodity.getSubCommoditySkuId());
            product.setSupplyMemberId(commodity.getMemberId());
            product.setSupplyRoleId(commodity.getMemberRoleId());
            product.setSupplyMemberName(commodity.getMemberName());
            product.setProductId(commodity.getCommodityId());
            product.setProductNo(StringUtils.hasLength(commodity.getCode()) ? commodity.getCode() : "");
            product.setSkuId(commodity.getId());
            product.setName(StringUtils.hasLength(commodity.getName()) ? commodity.getName() : "");
            product.setCategory(StringUtils.hasLength(commodity.getCustomerCategoryFullName()) ? commodity.getCustomerCategoryFullName() : "");
            product.setBrand(StringUtils.hasLength(commodity.getBrandName()) ? commodity.getBrandName() : "");
            product.setUnit(StringUtils.hasLength(commodity.getUnitName()) ? commodity.getUnitName() : "");
            product.setSpec(StringUtils.hasLength(commodity.getCommodityAttribute()) ? commodity.getCommodityAttribute() : "");
            product.setLogo(StringUtils.hasLength(commodity.getMainPic()) ? commodity.getMainPic() : "");
            product.setPriceType(NumberUtil.isNullOrZero(commodity.getPriceType()) ? PriceTypeEnum.CASH.getCode() : commodity.getPriceType());
            product.setPrices(CollectionUtils.isEmpty(commodity.getUnitPrice()) ? new HashMap<>() : commodity.getUnitPrice());
            product.setDiscount(Boolean.TRUE.equals(commodity.getIsMemberPrice()) ? memberResult.stream().filter(right -> right.getUpperMemberId().equals(commodity.getMemberId()) && right.getUpperRoleId().equals(commodity.getMemberRoleId())).map(MemberFeignRelationRightDetailResp::getParameter).findFirst().orElse(BigDecimal.ONE) : BigDecimal.ONE);
            product.setStock(NumberUtil.isNullOrZero(commodity.getStockCount()) ? BigDecimal.ZERO : commodity.getStockCount());
            product.setTaxRate(NumberUtil.isNullOrZero(commodity.getTaxRate()) ? BigDecimal.ZERO : commodity.getTaxRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
            product.setTax(!Objects.isNull(product.getTaxRate()) && product.getTaxRate().compareTo(BigDecimal.ZERO) > 0);
            product.setDeliverType(commodity.getLogistics() == null ? OrderProductDeliverTypeEnum.NO_DELIVERY.getCode() : (NumberUtil.isNullOrZero(commodity.getLogistics().getDeliveryType()) ? OrderProductDeliverTypeEnum.NO_DELIVERY.getCode() : commodity.getLogistics().getDeliveryType()));
            product.setFreightType(commodity.getLogistics() == null ? OrderFreightTypeEnum.NONE.getCode() : (NumberUtil.isNullOrZero(commodity.getLogistics().getCarriageType()) ? OrderFreightTypeEnum.NONE.getCode() : commodity.getLogistics().getCarriageType()));
            product.setWeight(commodity.getLogistics() == null ? BigDecimal.ZERO : (NumberUtil.isNullOrZero(commodity.getLogistics().getWeight()) ? BigDecimal.ZERO : commodity.getLogistics().getWeight()));
            product.setLogisticsTemplateId(commodity.getLogistics() == null ? 0L : (NumberUtil.notNullAndPositive(commodity.getLogistics().getTemplateId()) ? commodity.getLogistics().getTemplateId() : 0L));
            product.setAddressId(commodity.getLogistics() == null ? 0L : (NumberUtil.notNullAndPositive(commodity.getLogistics().getSendAddressId()) ? commodity.getLogistics().getSendAddressId() : 0L));
            product.setReceiver("");
            product.setPhone("");
            product.setAddress("");
            return product;
        }).collect(Collectors.toList());
    }

    /**
     * 更新商品售卖数量
     *
     * @param shopId          商城Id
     * @param storeId         店铺Id
     * @param shopType        商城类型枚举
     * @param buyerMemberId   采购会员Id
     * @param buyerRoleId     采购会员角色Id
     * @param buyerMemberName 采购会员名称
     * @param skuQuantities   商品SkuId+采购数量
     */
    @Async
    @Override
    public void updateProductSoldCountAsync(Long shopId, Long storeId, Integer shopType, Long buyerMemberId, Long buyerRoleId, String buyerMemberName, List<OrderProductStockDTO> skuQuantities) {
        CommoditySoldDTO sold = new CommoditySoldDTO();
        sold.setShopId(shopId);
        sold.setStoreId(storeId);
        sold.setMemberId(buyerMemberId);
        sold.setMemberRoleId(buyerRoleId);
        sold.setMemberName(buyerMemberName);
        try {
            switch (ShopTypeEnum.parseCode(shopType)) {
                case ENTERPRISE:
                case SCORE:
                    sold.setCommodityCountMap(new HashMap<>(skuQuantities.stream().collect(Collectors.toMap(OrderProductStockDTO::getSkuId, OrderProductStockDTO::getQuantity))));
                    //将商品已售数量存入消息队列中
                    mqUtils.sendMsg(ProductMqConstant.ORDER_COMMODITY_SOLD_EXCHANGE, ProductMqConstant.ORDER_COMMODITY_SOLD_ROUTING_KEY, JSONUtil.toJsonStr(sold));
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("通知商品服务更新商品售卖数量错误：" + e.getMessage() + "\n 参数:" + SerializeUtil.serialize(sold));
            throw new BusinessException(ResponseCodeEnum.SERVICE_ERROR, e.getMessage());
        }
    }

    /**
     * 查询商品详细信息
     *
     * @param shopId               商城Id
     * @param buyerMemberId        采购会员Id
     * @param buyerRoleId          采购会员角色Id
     * @param buyerMemberLevelType 采购会员等级类型
     * @param products             商品SkuId列表
     * @return 转换后的商品信息列表
     */
    @Override
    public List<OrderProductDetailDTO> findProductDetails(Long shopId, Long buyerMemberId, Long buyerRoleId, Integer buyerMemberLevelType, List<ProductSkuDTO> products, Integer saleMode, Long certificateId) {
        CommoditySinglePriceReq request = new CommoditySinglePriceReq();
        request.setMemberId(buyerMemberId);
        request.setMemberRoleId(buyerRoleId);
        request.setShopId(shopId);
        request.setSkuIdList(products.stream().map(ProductSkuDTO::getSkuId).collect(Collectors.toList()));
        request.setCommoditySingleIdList(products.stream().map(ProductSkuDTO::getCommoditySingleId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList()));
        request.setVisaId(certificateId);
        List<CommoditySkuSingleResp> resultList = WrapperUtil.getDataOrThrow(commodityFeign.getCommodityListByOrderSingle(request));
        if (CollectionUtils.isEmpty(resultList) || products.stream().anyMatch(s -> resultList.stream().noneMatch(product -> product.getId().equals(s.getSkuId())))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }

        // 判断如果有商品没有库存，抛出异常
        if (CommoditySaleModeEnum.SPOT.getCode().equals(saleMode)) {
            List<CommoditySkuSingleResp> outOfStockProducts = resultList.stream().filter(product -> !FreightSpaceSingleProductStatusEnum.NOT_USE.getCode().equals(product.getStatus())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(outOfStockProducts)) {
                throw new BusinessException("商品码不可用，请返回购车重新下单!");
            }
        }

        List<OrderProductDetailDTO> productDetails = products.stream().map(product -> {
            OrderProductDetailDTO productDetail = new OrderProductDetailDTO();
            productDetail.setSkuId(product.getSkuId());
            productDetail.setBuyerMemberId(product.getBuyerMemberId());
            productDetail.setBuyerRoleId(product.getBuyerRoleId());
            productDetail.setVendorMemberId(product.getVendorMemberId());
            productDetail.setVendorRoleId(product.getVendorRoleId());
            productDetail.setPromotionType(product.getPromotionType());
            productDetail.setGroupNo(product.getGroupNo());
            productDetail.setParentSkuId(product.getParentSkuId());
            productDetail.setQuantity(product.getQuantity());

            resultList.stream().filter(r -> CommoditySaleModeEnum.SPOT.getCode().equals(saleMode) ? r.getSingleId().equals(product.getCommoditySingleId()) : r.getId().equals(product.getSkuId())).findFirst().ifPresent(result -> {
                productDetail.setProductId(result.getCommodityId());
                productDetail.setSkuId(result.getId());
                productDetail.setPublished(result.getIsPublish() != null && result.getIsPublish());
                productDetail.setName(result.getName());
                productDetail.setLogo(result.getMainPic());
                productDetail.setCategory(result.getCustomerCategoryName());
                productDetail.setBrand(result.getBrandName());
                productDetail.setUnit(result.getUnitName());
                productDetail.setSpec(StringUtils.hasLength(result.getCommodityAttribute()) ? result.getCommodityAttribute() : "");
                productDetail.setMinOrder(NumberUtil.isNullOrNegative(result.getMinOrder()) ? BigDecimal.ZERO : result.getMinOrder());
                productDetail.setDiscountable(result.getIsMemberPrice());
                productDetail.setPriceMap(result.getUnitPrice());
                productDetail.setPriceType(ObjectUtil.isNotEmpty(result.getPriceType()) ? result.getPriceType() : 1);
                productDetail.setTax(!NumberUtil.isNullOrNegative(result.getTaxRate()));
                productDetail.setTaxRate(NumberUtil.isNullOrNegative(result.getTaxRate()) ? BigDecimal.ZERO : result.getTaxRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
                productDetail.setIsInlay(result.getIsInlay());

                //物流信息
                productDetail.setDeliveryType(result.getLogistics() == null || NumberUtil.isNullOrNegativeZero(result.getLogistics().getDeliveryType()) ? OrderProductDeliverTypeEnum.LOGISTICS.getCode() : result.getLogistics().getDeliveryType());
                productDetail.setFreightType(result.getLogistics() == null || NumberUtil.isNullOrNegativeZero(result.getLogistics().getCarriageType()) ? OrderFreightTypeEnum.VENDOR.getCode() : result.getLogistics().getCarriageType());
                productDetail.setWeight(result.getLogistics() == null || NumberUtil.isNullOrNegativeZero(result.getLogistics().getWeight()) ? BigDecimal.ZERO : result.getLogistics().getWeight());
                productDetail.setLogisticsTemplateId(result.getLogistics() == null || result.getLogistics().getUseTemplate() == null || result.getLogistics().getUseTemplate().equals(false) || NumberUtil.isNullOrNegativeZero(result.getLogistics().getTemplateId()) ? 0L : result.getLogistics().getTemplateId());
                productDetail.setConsigneeId(result.getLogistics() == null || NumberUtil.isNullOrNegativeZero(result.getLogistics().getSendAddressId()) ? 0L : result.getLogistics().getSendAddressId());
                productDetail.setDiscountable(result.getIsMemberPrice());
                //productDetail.setIsCrossBorder(result.getIsCrossBorder());

                // 设置门店信息
                productDetail.setStoreId(result.getStoreId());
                productDetail.setStoreName(result.getStoreName());
                productDetail.setStoreLogo(result.getStoreLogo());

                //要查询会员服务设置
                productDetail.setDiscount(Boolean.FALSE.equals(productDetail.getDiscountable()) ? BigDecimal.ONE : BigDecimal.ZERO);

                //商品单件信息
                productDetail.setSingleId(result.getSingleId());
                productDetail.setCommoditySingleCode(result.getCommoditySingleCode());
                productDetail.setBaseLaborCosts(result.getBaseLaborCosts());
                productDetail.setAdditionalLaborCosts(result.getAdditionalLaborCosts());
                productDetail.setPieceLaborCosts(result.getPieceLaborCosts());
                productDetail.setNetWeight(result.getNetWeight());
                if (product.getNeedCertificate()) {
                    productDetail.setVisaPrice(result.getVisaPrice());
                    productDetail.setUnitVisaType(result.getUnitVisaType());
                    productDetail.setVisaUnitPrice(result.getVisaUnitPrice());
                }
                if (CollectionUtil.isNotEmpty(result.getFreightSpaceSingleProductExtendList())) {
                    List<FreightSpaceSingleProductExtendDTO> singleProductExtendDTOList = BeanUtil.copyToList(result.getFreightSpaceSingleProductExtendList(), FreightSpaceSingleProductExtendDTO.class);
                    productDetail.setFreightSpaceSingleProductExtendList(singleProductExtendDTOList);
                }
            });

            return productDetail;
        }).collect(Collectors.toList());

        //从会员服务查询折扣
        List<MemberFeignRelationReq> vendorRelations = productDetails.stream().filter(OrderProductDetailDTO::getDiscountable).map(r -> {
            MemberFeignRelationReq relationVO = new MemberFeignRelationReq();
            relationVO.setSubMemberId(r.getBuyerMemberId());
            relationVO.setSubRoleId(r.getBuyerRoleId());
            relationVO.setUpperMemberId(r.getVendorMemberId());
            relationVO.setUpperRoleId(r.getVendorRoleId());
            return relationVO;
        }).collect(Collectors.toList());

        List<MemberFeignRelationRightDetailResp> rightResult = memberFeignService.batchFindMemberPriceRight(vendorRelations);

        return productDetails.stream().peek(productDetailDTO -> {
            //被换购的商品没有会员折扣
            if (OrderPromotionTypeEnum.EXCHANGED.getCode().equals(productDetailDTO.getPromotionType())) {
                productDetailDTO.setDiscountable(false);
                productDetailDTO.setDiscount(BigDecimal.ONE);
            } else if (Boolean.TRUE.equals(productDetailDTO.getDiscountable())) {
                rightResult.stream().filter(right -> right.getSubMemberId().equals(productDetailDTO.getBuyerMemberId()) && right.getSubRoleId().equals(productDetailDTO.getBuyerRoleId()) && right.getUpperMemberId().equals(productDetailDTO.getVendorMemberId()) && right.getUpperRoleId().equals(productDetailDTO.getVendorRoleId())).findFirst().ifPresent(right -> productDetailDTO.setDiscount(right.getParameter()));
            }
        }).collect(Collectors.toList());
    }

    /**
     * 查询Srm订单物料一级品类
     *
     * @param productIds 物料商品Id列表
     * @return 查询结果
     */
    @Override
    public List<OrderProductCategoryDTO> findProductCategories(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }

        try {
            CommonIdListReq request = new CommonIdListReq();
            request.setIdList(productIds);
            log.info("从商品服务查询物料一级品类Id => " + SerializeUtil.serialize(request));

            WrapperResp<List<MaterielIdAndFirstCategoryIdResp>> result = commodityFeign.getMaterielFirstCategoryIdList(request);
            log.info("从商品服务查询物料一级品类返回 => " + SerializeUtil.serialize(result));
            WrapperUtil.throwWhenFail(result);

            return CollectionUtils.isEmpty(result.getData()) ? new ArrayList<>() : result.getData().stream().map(r -> new OrderProductCategoryDTO(r.getMaterielId(), r.getCategoryId())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("从商品服务查询物料一级品类Id异常, msg:" + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 通知商品服务，创建采购入库单
     *
     * @param memberId                         会员id
     * @param roleId                           角色id
     * @param memberName                       会员名称
     * @param warehousingOrderProductDetailResp 自动入库订单物料信息VO
     * @param orderNo                          订单号/发货单号
     * @param receiptNo                        收货单号
     */
    @Override
    public Void createInboundOrder(Long memberId, Long roleId, String memberName, List<WarehousingOrderProductDetailResp> warehousingOrderProductDetailResp, String orderNo, String receiptNo) {
        //校验处理
        warehousingOrderProductDetailResp = warehousingOrderProductDetailResp.stream().map(w -> {
            //关联仓库物料和入库仓库只能同时选，或同时不选。
            // 若只选择关联物料不选择仓库，点确定时报错：${商品名称}未选择入库仓库；若只选择选择仓库不关联物料，点确定时报错：${商品名称}未关联仓库物料；
            if (NumberUtil.notNullOrZero(w.getGoodsId()) && NumberUtil.isNullOrZero(w.getInboundWarehouseId())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_INBOUND_WAREHOUSE_NOT_SELECTED, w.getName());
            }
            if (NumberUtil.isNullOrZero(w.getGoodsId()) && NumberUtil.notNullOrZero(w.getInboundWarehouseId())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_UNASSOCIATED_WAREHOUSE_ITEM, w.getName());
            }
            if (NumberUtil.isNullOrZero(w.getInboundWarehouseId())) {
                return null;
            }
            return w;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        //封装传递参数
        InvoicesAutoCreateReq invoicesAutoCreateReq = new InvoicesAutoCreateReq();
        invoicesAutoCreateReq.setInvoicesAbstract(InboundOrderEnum.ORDER_NUMBER.getName() + ":" + orderNo + "," + InboundOrderEnum.RECEIPT_NUMBER.getName() + "：" + receiptNo);
        invoicesAutoCreateReq.setInvoicesTime(System.currentTimeMillis());
        invoicesAutoCreateReq.setSource(2);
        invoicesAutoCreateReq.setMemberId(memberId);
        invoicesAutoCreateReq.setMemberRoleId(roleId);

        List<InvoicesReq> invoicesReqs = new ArrayList<>();
        //根据入库仓库id分组
        Map<Long, List<WarehousingOrderProductDetailResp>> map = warehousingOrderProductDetailResp.stream().collect(Collectors.groupingBy(WarehousingOrderProductDetailResp::getInboundWarehouseId));
        if (!map.isEmpty()) {
            for (Map.Entry<Long, List<WarehousingOrderProductDetailResp>> entry : map.entrySet()) {

                InvoicesReq request = new InvoicesReq();
                request.setWarehouseId(entry.getKey());
                request.setWarehouseRole(StringUtils.hasLength(entry.getValue().get(0).getWarehouseRole()) ? entry.getValue().get(0).getWarehouseRole() : memberName);//对应仓库人员全部一致
                List<InvoicesMaterielReq> invoicesDetailsList = new ArrayList<>();
                entry.getValue().forEach(e -> {
                    InvoicesMaterielReq invoicesMaterielReq = new InvoicesMaterielReq();
                    invoicesMaterielReq.setMaterielId(e.getGoodsId());
                    invoicesMaterielReq.setMaterielNo(e.getSkuId());
                    invoicesMaterielReq.setMaterielName(e.getName());
                    invoicesMaterielReq.setSpecifications(e.getSpec());
                    invoicesMaterielReq.setCategory(e.getCategory());
                    invoicesMaterielReq.setBrand(e.getBrand());
                    invoicesMaterielReq.setUnit(e.getUnit());
                    invoicesMaterielReq.setCostPrice(e.getCostPrice());
                    if (StringUtils.hasLength(e.getReceived())) {
                        invoicesMaterielReq.setInvoicesCount(new BigDecimal(e.getReceived()));
                        invoicesMaterielReq.setTotalPrice(e.getCostPrice() != null ? new BigDecimal(e.getReceived()).multiply(e.getCostPrice()) : BigDecimal.ZERO);
                    }
                    invoicesMaterielReq.setMaterialGroupId(e.getMaterialGroupId());
                    invoicesDetailsList.add(invoicesMaterielReq);
                });
                request.setInvoicesDetailsList(invoicesDetailsList);
                invoicesReqs.add(request);
            }
            invoicesAutoCreateReq.setInvoicesList(invoicesReqs);
            log.info("自动创建采购入库单:{}", JSONUtil.toJsonStr(invoicesAutoCreateReq));
            //创建采购入库单
            WrapperResp<Void> result = warehouseFeign.autoCreateEnterInvoices(invoicesAutoCreateReq);
            WrapperUtil.throwWhenFail(result);
        }
        return null;
    }

    /**
     * 通知商品服务，创建采购出库单
     *
     * @param vendorMemberId                     会员id
     * @param vendorRoleId                       角色id
     * @param vendorMemberName                   会员角色名称
     * @param outOfStockOrderProductDetailRespList 自动出库订单物料信息VO
     * @param orderNo                            订单号/发货单号
     * @param deliveryNo                         发货单号
     */
    @Override
    public void createOutOfStockOrder(Long vendorMemberId, Long vendorRoleId, String vendorMemberName, List<OutOfStockOrderProductDetailResp> outOfStockOrderProductDetailRespList, String orderNo, String deliveryNo) {
        //封装传递参数
        InvoicesAutoCreateReq invoicesAutoCreateReq = new InvoicesAutoCreateReq();
        invoicesAutoCreateReq.setInvoicesAbstract(InboundOrderEnum.ORDER_NUMBER.getName() + ":" + orderNo + "," + InboundOrderEnum.DELIVERY_NUMBER.getName() + "：" + deliveryNo);
        invoicesAutoCreateReq.setInvoicesTime(System.currentTimeMillis());
        invoicesAutoCreateReq.setSource(2);
        invoicesAutoCreateReq.setMemberId(vendorMemberId);
        invoicesAutoCreateReq.setMemberRoleId(vendorRoleId);

        List<InvoicesReq> invoicesReqs = new ArrayList<>();
        //根据入库仓库id分组
        Map<Long, List<OutOfStockOrderProductDetailResp>> map = outOfStockOrderProductDetailRespList.stream().collect(Collectors.groupingBy(OutOfStockOrderProductDetailResp::getOutOfStockId));
        for (Map.Entry<Long, List<OutOfStockOrderProductDetailResp>> entry : map.entrySet()) {
            InvoicesReq request = new InvoicesReq();
            request.setWarehouseId(entry.getKey());
            request.setWarehouseRole(StringUtils.hasLength(entry.getValue().get(0).getWarehouseRole()) ? entry.getValue().get(0).getWarehouseRole() : vendorMemberName);//对应仓库人员全部一致
            List<InvoicesMaterielReq> invoicesDetailsList = new ArrayList<>();
            entry.getValue().forEach(e -> {
                InvoicesMaterielReq invoicesMaterielReq = new InvoicesMaterielReq();
                invoicesMaterielReq.setMaterielId(e.getGoodsId());
                invoicesMaterielReq.setMaterielNo(e.getSkuId());
                invoicesMaterielReq.setMaterielName(e.getName());
                invoicesMaterielReq.setSpecifications(e.getSpec());
                invoicesMaterielReq.setCategory(e.getCategory());
                invoicesMaterielReq.setBrand(e.getBrand());
                invoicesMaterielReq.setUnit(e.getUnit());
                invoicesMaterielReq.setCostPrice(e.getCostPrice());
                if (StringUtils.hasLength(e.getReceived())) {
                    invoicesMaterielReq.setInvoicesCount(new BigDecimal(e.getReceived()));
                    invoicesMaterielReq.setTotalPrice(e.getCostPrice() != null ? new BigDecimal(e.getReceived()).multiply(e.getCostPrice()) : BigDecimal.ZERO);
                }
                invoicesMaterielReq.setMaterialGroupId(e.getMaterialGroupId());
                invoicesDetailsList.add(invoicesMaterielReq);
            });
            request.setInvoicesDetailsList(invoicesDetailsList);
            invoicesReqs.add(request);
        }
        invoicesAutoCreateReq.setInvoicesList(invoicesReqs);

        log.info("创建采购出库单信息:{}", SerializeUtil.serialize(invoicesAutoCreateReq));
        //创建采购出库单
        WrapperResp<Void> wrapperResp = warehouseFeign.autoCreateOutInvoices(invoicesAutoCreateReq);
        WrapperUtil.throwWhenFail(wrapperResp);
    }

    /**
     * 获取全部仓位的占用库存
     *
     * @param orderCommodityOccupiedInventoryReqList         请求参数
     * @return 操作结果
     */
    @Override
    public List<OccupyInventoryProductPositionResp> getCommodityInventoryBatch(List<OrderCommodityOccupiedInventoryReq> orderCommodityOccupiedInventoryReqList) {
        //响应结果
        List<OccupyInventoryProductPositionResp> orderProductPositionVOS = new ArrayList<>();
        //查询订单下每个商品对应的仓位占用库存
        WrapperResp<List<OrderCommodityOccupiedInventoryResp>> wrapperResp = freightSpaceFeign.getOrderCommodityOccupiedInventory(orderCommodityOccupiedInventoryReqList);
        WrapperUtil.throwWhenFail(wrapperResp);

        if (!CollectionUtils.isEmpty(wrapperResp.getData())) {
            wrapperResp.getData().forEach(freightSpaceResponse -> freightSpaceResponse.getOrderCommodityFreightSpaceList().forEach(orderCommodityFreightSpaceResponse -> {
                OccupyInventoryProductPositionResp occupyInventoryProductPositionResp = new OccupyInventoryProductPositionResp();
                occupyInventoryProductPositionResp.setPositionId(orderCommodityFreightSpaceResponse.getFreightSpaceId());
                occupyInventoryProductPositionResp.setPositionName(orderCommodityFreightSpaceResponse.getFreightSpaceName());
                occupyInventoryProductPositionResp.setPositionQuantity(orderCommodityFreightSpaceResponse.getOccupiedInventory());
                occupyInventoryProductPositionResp.setSkuId(freightSpaceResponse.getCommoditySkuId());
                occupyInventoryProductPositionResp.setOrderId(freightSpaceResponse.getOrderId());
                orderProductPositionVOS.add(occupyInventoryProductPositionResp);
            }));
        }
        return orderProductPositionVOS;
    }

    /**
     * 扣减供应商库存
     * @param buyerMemberId   采购会员Id
     * @param buyerRoleId     采购会员角色Id
     * @param orderId         订单Id
     * @param shopId          商城Id
     * @param shopType        商城类型（用于判断是渠道商城还是会员商城）
     * @param products        商品和采购数量
     */
    @Override
    public void deductCustomInventory(Long buyerMemberId, Long buyerRoleId, Long orderId, Long shopId, Integer shopType, List<InventoryCustomProductBO> products) {
        FreightSpaceCustomInventoryReq request = new FreightSpaceCustomInventoryReq();
        request.setSupplyMembersId(buyerMemberId);
        request.setMemberRoleId(buyerRoleId);
        request.setOrderId(orderId);
        request.setShopId(shopId);
        request.setDeductInventoryRequests(products.stream().map(product -> {
            CustomInventoryCommodityReq r = new CustomInventoryCommodityReq();
            r.setProductId(product.getProductId());
            r.setProductSkuId(product.getSkuId());
            r.setProductName(product.getProductName());
            r.setCustomInventoryRequestList(product.getCustomInventoryPurchaseBOS().stream().map(c->BeanUtil.copyProperties(c, CustomInventoryPurchaseReq.class)).collect(Collectors.toList()));
            return r;
        }).collect(Collectors.toList()));
        log.info("扣减供应商库存:{}",JSONUtil.toJsonStr(request));
        WrapperResp<Boolean> wrapperResp = freightSpaceFeign.deductCustomInventory(request);
        WrapperUtil.throwWhenFail(wrapperResp);

        if (Boolean.FALSE.equals(wrapperResp.getData())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DEDUCTION_OF_INVENTORY_FAILED);
        }
    }

    /**
     * 返回供应商库存
     * @param buyerMemberId   采购会员Id
     * @param buyerRoleId     采购会员角色Id
     * @param orderId         订单Id
     * @param shopId          商城Id
     * @param shopType        商城类型（用于判断是渠道商城还是会员商城）
     * @param products        商品和采购数量
     */
    @Override
    public void returnCustomInventory(Long buyerMemberId, Long buyerRoleId, Long orderId, Long shopId, Integer shopType, List<InventoryCustomProductBO> products) {
        FreightSpaceCustomInventoryReq request = new FreightSpaceCustomInventoryReq();
        request.setSupplyMembersId(buyerMemberId);
        request.setMemberRoleId(buyerRoleId);
        request.setOrderId(orderId);
        request.setShopId(shopId);
        request.setDeductInventoryRequests(products.stream().map(product -> {
            CustomInventoryCommodityReq r = new CustomInventoryCommodityReq();
            r.setProductId(product.getProductId());
            r.setProductSkuId(product.getSkuId());
            r.setProductName(product.getProductName());
            r.setCustomInventoryRequestList(product.getCustomInventoryPurchaseBOS().stream().map(c->BeanUtil.copyProperties(c, CustomInventoryPurchaseReq.class)).collect(Collectors.toList()));
            return r;
        }).collect(Collectors.toList()));
        WrapperResp<Boolean> wrapperResp = freightSpaceFeign.returnCustomInventory(request);
        WrapperUtil.throwWhenFail(wrapperResp);

        if (Boolean.FALSE.equals(wrapperResp.getData())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_RETURN_INVENTORY_FAILED);
        }
    }

    /**
     * 释放占用库存(订单发货)
     *
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param orderId       订单Id
     * @param shopId        商城Id
     * @param shopType      商城类型（用于判断是渠道商城还是会员商城）
     * @param products      商品和返还数量
     */
    @Override
    public void returnOccupiedInventory(Long buyerMemberId, Long buyerRoleId, Long orderId, Long shopId, Integer shopType, List<InventoryProductBO> products) {
        InventoryByProductIdReq request = new InventoryByProductIdReq();
        request.setSupplyMembersId(buyerMemberId);
        request.setMemberRoleId(buyerRoleId);
        request.setOrderId(orderId);
        request.setShopId(shopId);
        request.setDeductInventoryRequests(products.stream().map(product -> {
            DeductInventoryReq r = new DeductInventoryReq();
            r.setProductId(product.getProductId());
            r.setProductSkuId(product.getSkuId());
            r.setProductName(product.getProductName());
            //每一次的发货数量
            r.setPurchaseCount(product.getQuantity());
            return r;
        }).collect(Collectors.toList()));
        log.info("调用商品服务->释放占用库存(订单发货):{}",JSONUtil.toJsonStr(request));
        //调用商品服务->释放占用库存(订单发货)
        WrapperResp<Boolean> wrapperResp = freightSpaceFeign.returnOccupiedInventory(request);
        WrapperUtil.throwWhenFail(wrapperResp);
    }

    /**
     * 指定仓库释放占用库存(订单发货)
     *
     * @param buyerMemberId 采购会员Id
     * @param buyerRoleId   采购会员角色Id
     * @param orderId       订单Id
     * @param shopId        商城Id
     * @param shopType      商城类型（用于判断是渠道商城还是会员商城）
     * @param warehouseId   仓库ID
     * @param products      商品和返还数量
     */
    @Override
    public void returnCustomOccupiedInventory(Long buyerMemberId, Long buyerRoleId, Long orderId, Long shopId, Integer shopType, Long warehouseId, List<InventoryProductBO> products) {
        OccupiedInventoryReq request = new OccupiedInventoryReq();
        request.setSupplyMembersId(buyerMemberId);
        request.setMemberRoleId(buyerRoleId);
        request.setOrderId(orderId);
        request.setShopId(shopId);
        request.setWarehouseId(warehouseId);
        request.setDeductInventoryRequests(products.stream().map(product -> {
            DeductInventoryReq r = new DeductInventoryReq();
            r.setProductId(product.getProductId());
            r.setProductSkuId(product.getSkuId());
            r.setProductName(product.getProductName());
            //每一次的发货数量
            r.setPurchaseCount(product.getQuantity());
            return r;
        }).collect(Collectors.toList()));
        log.info("调用商品服务->指定仓库释放占用库存(订单发货):{}",JSONUtil.toJsonStr(request));
        //调用商品服务->释放占用库存(订单发货)
        WrapperResp<Boolean> wrapperResp = freightSpaceFeign.returnCustomOccupiedInventory(request);
        WrapperUtil.throwWhenFail(wrapperResp);
    }
}
