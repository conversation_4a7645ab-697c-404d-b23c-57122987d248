package com.ssy.lingxi.order.serviceImpl.platform;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.engine.DeleteBaseProcessReq;
import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.req.engine.ProcessEngineReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPurchaseProcessTypeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTradeProcessTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.engine.api.enums.ProcessDefaultEnum;
import com.ssy.lingxi.engine.api.enums.ProcessSourceEnum;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.BasePurchaseProcessEnum;
import com.ssy.lingxi.order.model.dto.OrderMemberQueryDTO;
import com.ssy.lingxi.order.model.dto.ProcessQueryRequest;
import com.ssy.lingxi.order.model.dto.SaveDefaultRequest;
import com.ssy.lingxi.order.model.req.basic.OrderProcessIdReq;
import com.ssy.lingxi.order.model.req.basic.OrderProcessUpdateStatusReq;
import com.ssy.lingxi.order.model.req.platform.PlatformProcessMemberPageDataReq;
import com.ssy.lingxi.order.model.req.platform.PlatformProcessPageDataReq;
import com.ssy.lingxi.order.model.req.platform.PlatformProcessReq;
import com.ssy.lingxi.order.model.req.platform.PlatformProcessUpdateReq;
import com.ssy.lingxi.order.model.resp.platform.PlatformBasePurchaseProcessResp;
import com.ssy.lingxi.order.model.resp.platform.PlatformProcessDetailResp;
import com.ssy.lingxi.order.model.resp.platform.PlatformProcessPageQueryResp;
import com.ssy.lingxi.order.model.resp.process.BasePurchaseProcessResp;
import com.ssy.lingxi.order.repository.BasePurchaseProcessRepository;
import com.ssy.lingxi.order.repository.PlatformPurchaseProcessRepository;
import com.ssy.lingxi.order.service.base.IBasePlatformPurchaseProcessMemberService;
import com.ssy.lingxi.order.service.base.IBasePurchaseProcessService;
import com.ssy.lingxi.order.service.platform.IPlatformPurchaseProcessService;
import com.ssy.lingxi.order.service.web.IOrderPurchaseProcessService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 平台后台 - 采购流程规则配置相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-26
 */
@Service
public class PlatformPurchaseProcessServiceImpl implements IPlatformPurchaseProcessService {
    @Resource
    private IBasePurchaseProcessService basePurchaseProcessService;

    @Resource
    private IBasePlatformPurchaseProcessMemberService basePlatformPurchaseProcessMemberService;

    @Resource
    private PlatformPurchaseProcessRepository platformPurchaseProcessRepository;

    @Resource
    private BasePurchaseProcessRepository basePurchaseProcessRepository;

    @Resource
    private IOrderPurchaseProcessService orderPurchaseProcessService;

    /**
     * 保存基础流程
     * @param engineBO 基础流程
     * @return Void
     */
    @Transactional
    @Override
    public Void saveBaseProcess(ProcessEngineReq engineBO) {

        // 查询平台基础流程
        BasePurchaseProcessDO baseProcess = basePurchaseProcessRepository.findByProcessKeyAndProcessType(engineBO.getProcessKey(), engineBO.getProcessType()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_PROCESS_INVALID));

        // 基础流程关联规则引擎
        baseProcess.setEngineId(engineBO.getEngineId());
        baseProcess.setProcessName(engineBO.getProcessName());
        baseProcess.setIsDefault(engineBO.getIsDefault());
        baseProcess.setProcessImage(engineBO.getProcessImage());
        baseProcess.setDescription(engineBO.getDescription());
        basePurchaseProcessRepository.saveAndFlush(baseProcess);

        // 生成平台默认规则
        this.saveDefaultProcess(baseProcess, engineBO);

        // 保存能力中心默认流程
        orderPurchaseProcessService.saveDefaultProcess(baseProcess, engineBO);

        return null;
    }

    /**
     * 创建平台默认流程
     * @param baseProcess 基础流程
     * @param engineBO 引擎数据
     */
    private void saveDefaultProcess(BasePurchaseProcessDO baseProcess, ProcessEngineReq engineBO){

        // 查询当前类型的默认流程
        PlatformPurchaseProcessDO defaultProcess = platformPurchaseProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode());

        // 不存在则创建默认流程
        if (Objects.equals(engineBO.getIsDefault(), ProcessDefaultEnum.YES.getCode()) && Objects.isNull(defaultProcess)){
            createDefault(baseProcess);
        }

        // 非默认流程不处理
        if (Objects.isNull(defaultProcess)){
            return;
        }

        // 取消默认则删除
        if (Objects.equals(ProcessDefaultEnum.NO.getCode(), engineBO.getIsDefault())){
            platformPurchaseProcessRepository.delete(defaultProcess);
            return;
        }

        // 更新默认工作流
        defaultProcess.setEngineId(baseProcess.getEngineId());
        defaultProcess.setName(baseProcess.getProcessName());
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        defaultProcess.setProcess(baseProcess);
        platformPurchaseProcessRepository.save(defaultProcess);
    }

    /**
     * 创建平台默认工作流
     * @param baseProcess 基础流程
     */
    private void createDefault(BasePurchaseProcessDO baseProcess){
        PlatformPurchaseProcessDO defaultProcess = new PlatformPurchaseProcessDO();
        defaultProcess.setEngineId(baseProcess.getEngineId());
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        defaultProcess.setProcessType(baseProcess.getProcessType());
        defaultProcess.setName(baseProcess.getProcessName());
        defaultProcess.setMembers(new HashSet<>());
        defaultProcess.setAllMembers(Boolean.TRUE);
        defaultProcess.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        defaultProcess.setCreateTime(LocalDateTime.now());
        defaultProcess.setSource(ProcessSourceEnum.PAAS.getCode());
        defaultProcess.setIsDefault(ProcessDefaultEnum.YES.getCode());
        defaultProcess.setProcess(baseProcess);
        platformPurchaseProcessRepository.saveAndFlush(defaultProcess);
    }


    /**
     * 删除基础流程
     * @param deleteDTO 基础流程
     * @return Void
     */
    @Override
    public Void deleteBaseProcess(DeleteBaseProcessReq deleteDTO) {

        // 查询
//        BasePurchaseProcessDO process = basePurchaseProcessRepository.findByEngineId(deleteDTO.getEngineId()).orElse(null);
//
//        // 执行删除
//        if (Objects.nonNull(process)){
//
//
//        }

        return null;
    }


    /**
     * 查询会员采购单流程
     * @param engineRuleQueryReq 基础流程
     * @return 查询数据
     */
    @Override
    public List<ProcessEngineRuleResp> getMemberProcess(EngineRuleQueryReq engineRuleQueryReq) {

        // 查询流程
        List<OrderPurchaseProcessDO> memberProcessList = orderPurchaseProcessService.getMemberProcess(engineRuleQueryReq.getMemberId(), engineRuleQueryReq.getMemberRoleId(), engineRuleQueryReq.getType());

        if (memberProcessList.isEmpty()){
            return new ArrayList<>();
        }

        // 组装数据
        return memberProcessList.stream().map(memberProcess -> {
            ProcessEngineRuleResp engineRuleVO = new ProcessEngineRuleResp();
            engineRuleVO.setProcessRuleId(memberProcess.getId());
            engineRuleVO.setIsDefault(memberProcess.getIsDefault());
            engineRuleVO.setProcessKey(memberProcess.getProcessKey());
            return engineRuleVO;
        }).collect(Collectors.toList());
    }

    /**
     * 分页查询采购流程规则配置
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<PlatformProcessPageQueryResp> pagePurchaseProcess(UserLoginCacheDTO loginUser, PlatformProcessPageDataReq pageVO) {
        // 查询平台默认
        List<Integer> types = platformPurchaseProcessRepository.findByIsDefaultAndSource(ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.SYSTEM.getCode()).stream().map(PlatformPurchaseProcessDO::getProcessType).collect(Collectors.toList());

        // 需要查询系统默认的流程
        List<Integer> typeList = Arrays.stream(OrderPurchaseProcessTypeEnum.values()).map(OrderPurchaseProcessTypeEnum::getCode).filter(code -> !types.contains(code)).collect(Collectors.toList());

        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("isDefault", "createTime").descending());
        Specification<PlatformPurchaseProcessDO> specification = (root, query, criteriaBuilder) -> {

            // 平台流程
            List<Predicate> cond = new ArrayList<>();
            cond.add(criteriaBuilder.equal(root.get("source"), ProcessSourceEnum.SYSTEM.getCode()));
            if (StringUtils.hasText(pageVO.getName())) {
                cond.add(criteriaBuilder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }
            if (typeList.isEmpty()){
                return criteriaBuilder.and(cond.toArray(new Predicate[0]));
            }

            // 系统默认
            List<Predicate> orList = new ArrayList<>();
            orList.add(criteriaBuilder.equal(root.get("source"), ProcessSourceEnum.PAAS.getCode()));
            orList.add(criteriaBuilder.in(root.get("processType")).value(typeList));
            if (StringUtils.hasText(pageVO.getName())) {
                orList.add(criteriaBuilder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }

            // 组合查询
            return criteriaBuilder.or(criteriaBuilder.and(cond.toArray(new Predicate[0])), criteriaBuilder.and(orList.toArray(new Predicate[0])));
        };

        Page<PlatformPurchaseProcessDO> pageList = platformPurchaseProcessRepository.findAll(specification, pageable);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(this::createQueryVO).collect(Collectors.toList()));
    }

    /**
     * 创建展示类
     * @param process 流程
     * @return 展示数据
     */
    private PlatformProcessPageQueryResp createQueryVO(PlatformPurchaseProcessDO process){
        PlatformProcessPageQueryResp queryVO = new PlatformProcessPageQueryResp();
        queryVO.setProcessId(process.getId());
        queryVO.setCreateTime(process.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
        if (Objects.equals(ProcessDefaultEnum.YES.getCode(), process.getIsDefault())){
            queryVO.setName(Optional.ofNullable(process.getProcess().getProcessName()).orElse(process.getProcess().getName()));
        }else {
            queryVO.setName(process.getName());
        }
        queryVO.setStatus(process.getStatus());
        queryVO.setStatusName(EnableDisableStatusEnum.getNameByCode(process.getStatus()));
        queryVO.setProcessName(Optional.ofNullable(process.getProcess().getProcessName()).orElse(process.getProcess().getName()));
        queryVO.setIsDefault(Optional.ofNullable(process.getIsDefault()).orElse(0));
        return queryVO;
    }

    @Override
    public List<PlatformProcessPageQueryResp> listPurchaseProcess(UserLoginCacheDTO loginUser, ProcessQueryRequest queryRequest) {
        Specification<PlatformPurchaseProcessDO> spec = (root, query, builder) -> {
            List<Predicate> list = new ArrayList<>();
            if (Objects.nonNull(queryRequest.getProcessType())) {
                list.add(builder.equal(root.get("processType").as(Integer.class), queryRequest.getProcessType()));
            }
            return query.where(list.toArray(new Predicate[0])).getRestriction();
        };
        return platformPurchaseProcessRepository.findAll(spec, Sort.by("createTime").descending()).stream().map(this::createQueryVO).collect(Collectors.toList());
    }

    @Override
    public Void saveDefault(SaveDefaultRequest defaultRequest) {

        // 查询当前规则
        BasePurchaseProcessDO baseProcess = basePurchaseProcessRepository.findById(defaultRequest.getProcessId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_PROCESS_INVALID));

        // 查询默认流程
        PlatformPurchaseProcessDO defaultProcess = platformPurchaseProcessRepository.findByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.SYSTEM.getCode()).stream().findFirst().orElse(null);

        if (Objects.isNull(defaultProcess)){

            // 系统默认流程
            PlatformPurchaseProcessDO defaultProcessPaas = Optional.ofNullable(platformPurchaseProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_DEFAULT_PROCESS_NON_EXISTENT));

            // 保存默认流程
            defaultProcess = BeanUtil.copyProperties(defaultProcessPaas, PlatformPurchaseProcessDO.class);
            defaultProcess.setId(null);
            defaultProcess.setProcessKey(baseProcess.getProcessKey());
            defaultProcess.setName(Optional.ofNullable(baseProcess.getProcessName()).orElse(baseProcess.getName()));
            defaultProcess.setProcess(baseProcess);
            defaultProcess.setSource(ProcessSourceEnum.SYSTEM.getCode());
            platformPurchaseProcessRepository.save(defaultProcess);

        } else {

            // 更新默认流程
            defaultProcess.setProcessKey(baseProcess.getProcessKey());
            defaultProcess.setName(Optional.ofNullable(baseProcess.getProcessName()).orElse(baseProcess.getName()));
            defaultProcess.setProcessType(baseProcess.getProcessType());
            defaultProcess.setProcess(baseProcess);
            platformPurchaseProcessRepository.save(defaultProcess);
        }

        // 设置平台默认流程
        orderPurchaseProcessService.defaultProcess(defaultProcess);

        return null;
    }

    /**
     * 新增采购规则页面 - 查询基础采购流程列表
     *
     * @param loginUser 登录用户
     * @return 查询结果
     */
    @Override
    public List<PlatformBasePurchaseProcessResp> listBasePurchaseProcess(UserLoginCacheDTO loginUser, ProcessQueryRequest queryRequest) {
        return basePurchaseProcessService.listProcessByPlatform(queryRequest);
    }

    /**
     * 新增采购流程规则
     *
     * @param loginUser 登录用户
     * @param processVO 接口参数
     * @return 新增结果
     */
    @Transactional
    @Override
    public Void createPurchaseProcess(UserLoginCacheDTO loginUser, PlatformProcessReq processVO) {
        //Step 1: 判断基础流程
        BasePurchaseProcessDO basePurchaseProcess = basePurchaseProcessService.findById(processVO.getBaseProcessId());
        if(basePurchaseProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BASE_PURCHASE_PROCESS_DOES_NOT_EXIST);
        }

        //Step 2: 判断是否已经存在
        if(platformPurchaseProcessRepository.existsByProcessAndIsDefault(basePurchaseProcess, ProcessDefaultEnum.NO.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PURCHASE_PROCESS_EXISTS);
        }

        //Step 3: 新增
        PlatformPurchaseProcessDO purchaseProcess = new PlatformPurchaseProcessDO();
        purchaseProcess.setCreateTime(LocalDateTime.now());
        purchaseProcess.setName(processVO.getName());
        purchaseProcess.setProcess(basePurchaseProcess);
        purchaseProcess.setProcessKey(basePurchaseProcess.getProcessKey());
        purchaseProcess.setProcessType(basePurchaseProcess.getProcessType());
        purchaseProcess.setProcessKind(basePurchaseProcess.getProcessKind());
        purchaseProcess.setSkipFirstStep(basePurchaseProcess.getSkipFirstStep());
        purchaseProcess.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        purchaseProcess.setAllMembers(processVO.getAllMembers());
        purchaseProcess.setIsDefault(ProcessDefaultEnum.NO.getCode());
        purchaseProcess.setSource(ProcessSourceEnum.SYSTEM.getCode());
        //由于关联实体时 CascadeType.DETACH, 所以要先保存一次
        platformPurchaseProcessRepository.saveAndFlush(purchaseProcess);

        // 3-1 : 校验、保存关联的会员
        basePlatformPurchaseProcessMemberService.checkMembers(purchaseProcess, processVO.getAllMembers(), processVO.getMembers());

        platformPurchaseProcessRepository.saveAndFlush(purchaseProcess);
        return null;
    }

    /**
     * 查询采购流程规则详情
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public PlatformProcessDetailResp getPurchaseProcess(UserLoginCacheDTO loginUser, OrderProcessIdReq idVO) {
        PlatformPurchaseProcessDO purchaseProcess = platformPurchaseProcessRepository.findById(idVO.getProcessId()).orElse(null);
        if(purchaseProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PURCHASE_PROCESS_DOES_NOT_EXIST);
        }
        PlatformProcessDetailResp detailVO = new PlatformProcessDetailResp();
        detailVO.setProcessId(purchaseProcess.getId());
        detailVO.setName(purchaseProcess.getName());
        detailVO.setBaseProcessId(purchaseProcess.getProcess().getId());
        detailVO.setAllMembers(purchaseProcess.getAllMembers());
        detailVO.setStatus(purchaseProcess.getStatus());
        detailVO.setStatusName(EnableDisableStatusEnum.getNameByCode(purchaseProcess.getStatus()));
        detailVO.setIsDefault(Optional.ofNullable(purchaseProcess.getIsDefault()).orElse(0));
        detailVO.setBaseProcess(createProcessVO(purchaseProcess.getProcess()));
        return detailVO;
    }

    /**
     * 创建展示类
     * @param process 基础流程
     * @return PlatformBasePurchaseProcessVO
     */
    private PlatformBasePurchaseProcessResp createProcessVO(BasePurchaseProcessDO process){
        PlatformBasePurchaseProcessResp processVO = new PlatformBasePurchaseProcessResp();
        processVO.setBaseProcessid(process.getId());
        processVO.setProcessName(BasePurchaseProcessEnum.getNameByCode(process.getCode()));
        processVO.setProcessType(process.getProcessType());
        processVO.setProcessTypeName(OrderPurchaseProcessTypeEnum.getNameByCode(process.getProcessType()));
        processVO.setDescription(BasePurchaseProcessEnum.getRemarkByCode(process.getCode()));
        processVO.setProcessImage(Optional.ofNullable(process.getProcessImage()).orElse(""));
        return processVO;
    }


    /**
     * 分页查询采购流程规则适用会员列表
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderMemberQueryDTO> pagePurchaseProcessMembers(UserLoginCacheDTO loginUser, PlatformProcessMemberPageDataReq pageVO) {
        PlatformPurchaseProcessDO purchaseProcess = platformPurchaseProcessRepository.findById(pageVO.getProcessId()).orElse(null);
        if(purchaseProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PURCHASE_PROCESS_DOES_NOT_EXIST);
        }

        return basePlatformPurchaseProcessMemberService.pageMembers(purchaseProcess, pageVO.getName(), pageVO.getCurrent(), pageVO.getPageSize());
    }

    /**
     * 修改采购流程规则
     *
     * @param loginUser 登录用户
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @Transactional
    @Override
    public Void updatePurchaseProcess(UserLoginCacheDTO loginUser, PlatformProcessUpdateReq updateVO) {
        PlatformPurchaseProcessDO purchaseProcess = platformPurchaseProcessRepository.findById(updateVO.getProcessId()).orElse(null);
        if(purchaseProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PURCHASE_PROCESS_DOES_NOT_EXIST);
        }

        //Step 1: 判断基础流程
        BasePurchaseProcessDO basePurchaseProcess = basePurchaseProcessService.findById(updateVO.getBaseProcessId());
        if(basePurchaseProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_BASE_PURCHASE_PROCESS_DOES_NOT_EXIST);
        }

        //Step 2: 判断是否已经存在
        if(platformPurchaseProcessRepository.existsByProcessAndIsDefaultAndIdNot(basePurchaseProcess, ProcessDefaultEnum.NO.getCode(), purchaseProcess.getId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PURCHASE_PROCESS_EXISTS);
        }

        //Step 3: 修改
        purchaseProcess.setCreateTime(LocalDateTime.now());
        purchaseProcess.setName(updateVO.getName());
        purchaseProcess.setProcess(basePurchaseProcess);
        purchaseProcess.setProcessKey(basePurchaseProcess.getProcessKey());
        purchaseProcess.setProcessType(basePurchaseProcess.getProcessType());
        purchaseProcess.setProcessKind(basePurchaseProcess.getProcessKind());
        purchaseProcess.setSkipFirstStep(basePurchaseProcess.getSkipFirstStep());
        purchaseProcess.setAllMembers(updateVO.getAllMembers());

        // 3-1 : 校验、修改关联的会员
        basePlatformPurchaseProcessMemberService.updateMembers(purchaseProcess, updateVO.getAllMembers(), updateVO.getMembers());

        platformPurchaseProcessRepository.saveAndFlush(purchaseProcess);
        return null;
    }

    /**
     * 修改采购流程规则状态
     *
     * @param loginUser 登录用户
     * @param statusVO 接口参数
     * @return 修改结果
     */
    @Override
    public Void updatePurchaseProcessStatus(UserLoginCacheDTO loginUser, OrderProcessUpdateStatusReq statusVO) {
        PlatformPurchaseProcessDO purchaseProcess = platformPurchaseProcessRepository.findById(statusVO.getProcessId()).orElse(null);
        if(purchaseProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PURCHASE_PROCESS_DOES_NOT_EXIST);
        }

        purchaseProcess.setCreateTime(LocalDateTime.now());
        purchaseProcess.setStatus(statusVO.getStatus());
        platformPurchaseProcessRepository.saveAndFlush(purchaseProcess);
        return null;
    }

    /**
     * 删除采购流程规则
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 删除结果
     */
    @Override
    public Void deletePurchaseProcess(UserLoginCacheDTO loginUser, OrderProcessIdReq idVO) {
        PlatformPurchaseProcessDO purchaseProcess = platformPurchaseProcessRepository.findById(idVO.getProcessId()).orElse(null);
        if(purchaseProcess == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_PURCHASE_PROCESS_DOES_NOT_EXIST);
        }

        platformPurchaseProcessRepository.delete(purchaseProcess);
        return null;
    }

    /**
     * 系统能力 - 新增采购流程规则配置时，根据平台后台的配置查询基础采购流程列表
     *
     * @param memberId 会员Id
     * @param roleId   会员角色Id
     * @return 查询结果
     */
    @Override
    public List<BasePurchaseProcessResp> findOrderPurchaseProcess(Long memberId, Long roleId, ProcessQueryRequest queryRequest) {
        Specification<PlatformPurchaseProcessDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> orList = new ArrayList<>();

            Predicate allMembers = criteriaBuilder.isTrue(root.get("allMembers").as(Boolean.class));
            if (Objects.nonNull(queryRequest.getProcessType())){
                allMembers = criteriaBuilder.and(allMembers, criteriaBuilder.equal(root.get("processType").as(Integer.class), queryRequest.getProcessType()));
            }
            orList.add(allMembers);

            Join<PlatformTradeProcessDO, PlatformPurchaseProcessMemberDO> memberJoin = root.join("members", JoinType.LEFT);
            Predicate member = criteriaBuilder.and(criteriaBuilder.equal(memberJoin.get("memberId").as(Long.class), memberId), criteriaBuilder.equal(memberJoin.get("roleId").as(Long.class), roleId));
            if (Objects.nonNull(queryRequest.getProcessType())){
                member = criteriaBuilder.and(member, criteriaBuilder.equal(root.get("processType").as(Integer.class), queryRequest.getProcessType()));
            }
            orList.add(member);

            Predicate[] p = new Predicate[orList.size()];
            return criteriaBuilder.or(orList.toArray(p));
        };
        List<PlatformPurchaseProcessDO> list = platformPurchaseProcessRepository.findAll(specification);
        return list.stream()
                .filter(purchaseProcess -> isFilter(purchaseProcess, queryRequest))
                .map(PlatformPurchaseProcessDO::getProcess).sorted(Comparator.comparingLong(BasePurchaseProcessDO::getId)).map(process -> {
                    BasePurchaseProcessResp processVO = new BasePurchaseProcessResp();
                    processVO.setBaseProcessid(process.getId());
                    processVO.setProcessName(BasePurchaseProcessEnum.getNameByCode(process.getCode()));
                    processVO.setProcessType(process.getProcessType());
                    processVO.setProcessTypeName(OrderPurchaseProcessTypeEnum.getNameByCode(process.getProcessType()));
                    processVO.setDescription(BasePurchaseProcessEnum.getRemarkByCode(process.getCode()));
                    processVO.setEngineId(process.getEngineId());
                    processVO.setProcessImage(process.getProcessImage());
                    processVO.setIsDefault(Optional.ofNullable(process.getIsDefault()).orElse(CommonBooleanEnum.NO.getCode()));
                    processVO.setEngineId(process.getEngineId());
                    return processVO;
                }).distinct().collect(Collectors.toList());
    }

    /**
     * 过滤数据
     * @param process       流程
     * @param queryRequest  请求参数
     * @return Boolean
     */
    private Boolean isFilter(PlatformPurchaseProcessDO process, ProcessQueryRequest queryRequest){

        // 有效的
        boolean isFilter = Objects.equals(process.getStatus(), EnableDisableStatusEnum.ENABLE.getCode());

        // 非默认的
        if (Objects.isNull(queryRequest.getProcessType())){
            isFilter = isFilter && !Objects.equals(process.getIsDefault(), ProcessDefaultEnum.YES.getCode());
        }

        // 指定类型
        if (Objects.nonNull(queryRequest.getProcessType())) {
            return isFilter && Objects.equals(process.getProcessType(), queryRequest.getProcessType());
        }

        Set<Integer> processTypeSet = Stream.of(OrderTradeProcessTypeEnum.AFTER_SALES_EXCHANGES.getCode(), OrderTradeProcessTypeEnum.AFTER_SALES_RETURNS.getCode(), OrderTradeProcessTypeEnum.AFTER_SALES_MAINTENANCE.getCode()).collect(Collectors.toSet());
        Set<Integer> orderProcessTypeSes = Stream.of(OrderTradeProcessTypeEnum.ORDER_TRADE.getCode(), OrderPurchaseProcessTypeEnum.ORDER_UPDATE_VALIDATE.getCode(), OrderPurchaseProcessTypeEnum.REQUISITION.getCode()).collect(Collectors.toSet());
        Integer type = Optional.ofNullable(queryRequest.getType()).orElse(-1);

        switch (type) {
            case 1:
                return isFilter && processTypeSet.contains(process.getProcessType());
            case 2:
                return isFilter && orderProcessTypeSes.contains(process.getProcessType());
            default:
                break;
        }

        return isFilter;
    }
}
