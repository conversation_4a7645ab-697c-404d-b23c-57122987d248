package com.ssy.lingxi.order.serviceImpl.base;

import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.constant.mq.MarketingMqConstant;
import com.ssy.lingxi.common.constant.mq.OrderMqConstant;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.rabbitMQ.constant.MqConstant;
import com.ssy.lingxi.component.rabbitMQ.model.dto.MarketingQueueDTO;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.marketing.api.enums.MkQueueMessageTypeEnum;
import com.ssy.lingxi.order.api.model.dto.OrderGroupDTO;
import com.ssy.lingxi.order.api.model.dto.OrderPaymentCallbackDTO;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.enums.OrderPaymentCallbackStatusEnum;
import com.ssy.lingxi.order.enums.OrderPromotionStatusEnum;
import com.ssy.lingxi.order.enums.OrderQueueMessageTypeEnum;
import com.ssy.lingxi.order.model.dto.*;
import com.ssy.lingxi.order.service.base.IBaseOrderPaymentService;
import com.ssy.lingxi.order.service.base.IBaseOrderScheduleService;
import com.ssy.lingxi.order.service.feign.IMemberFeignService;
import com.ssy.lingxi.order.service.web.IBuyerOrderService;
import com.ssy.lingxi.order.service.web.IOrderTimeParamService;
import com.ssy.lingxi.order.service.web.IVendorOrderService;
import com.ssy.lingxi.settlement.api.model.req.PlatformSettlementOrderPaymentReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单相关定时任务接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-10-21
 */
@Slf4j
@Service
public class BaseOrderScheduleServiceImpl implements IBaseOrderScheduleService {

    @Resource
    private IMqUtils mqUtils;

    @Resource
    private IOrderTimeParamService orderTimeParamService;

    @Resource
    private IBuyerOrderService buyerOrderService;

    @Resource
    private IVendorOrderService vendorOrderService;

    @Resource
    private IBaseOrderPaymentService baseOrderPaymentService;

    @Resource
    private IMemberFeignService memberFeignService;

    /**
     * 将延迟消息重新加入队列
     *
     * @param messageTypeEnum 消息类型
     * @param jsonMessage     消息实体
     * @param millSeconds     延迟时间（毫秒）
     * @param times           重新入队列次数
     */
    @Override
    public void reScheduleDelayMessage(OrderQueueMessageTypeEnum messageTypeEnum, String jsonMessage, Integer millSeconds, Integer times) {
        OrderQueueDTO queueDTO = new OrderQueueDTO();
        queueDTO.setType(messageTypeEnum.getCode());
        queueDTO.setMessage(jsonMessage);
        queueDTO.setTimes(times);
        queueDTO.setDelayTime((long) millSeconds);

        try {
            //延迟时间的单位是毫秒
            mqUtils.sendDelayMsg(OrderMqConstant.ORDER_SERVICE_DELAY_EXCHANGE, OrderMqConstant.ORDER_SERVICE_DELAY_ROUTING_KEY, SerializeUtil.serialize(queueDTO), millSeconds);
        } catch (Exception e) {
            log.error("重新推送至延迟消息队列错误，data: " + SerializeUtil.serialize(queueDTO) + ", error:" + e.getMessage());
        }
    }

    /**
     * 延迟队列 - 推送
     *
     * @param messageType 消息类型
     * @param object      消息对象
     * @param millSeconds 延迟时间（毫秒）
     */
    @Override
    public void scheduleDelayMessage(OrderQueueMessageTypeEnum messageType, Object object, Long millSeconds) {
        //转换为Json字符串，推送至消息队列
        String json = SerializeUtil.serialize(object);

        OrderQueueDTO queueDTO = new OrderQueueDTO(messageType.getCode(), json, millSeconds);
        String jsonMessage = SerializeUtil.serialize(queueDTO);

        //延时时间，如果大于 MqConstant.DELAYED_MAX_TIME，在接收消息时处理
        Long delayMillSeconds = millSeconds > MqConstant.DELAYED_MAX_TIME ? MqConstant.DELAYED_MAX_TIME : millSeconds;

        try {
            log.info("推送至延迟消息队列 => " + jsonMessage);
            //延迟时间的单位是毫秒
            mqUtils.sendDelayMsg(OrderMqConstant.ORDER_SERVICE_DELAY_EXCHANGE, OrderMqConstant.ORDER_SERVICE_DELAY_ROUTING_KEY, jsonMessage, delayMillSeconds);
        } catch (Exception e) {
            log.error("推送至延迟消息队列错误，data: " + json + ", error:" + e.getMessage());
        }
    }

    /**
     * （交易流程规则配置）订单自动取消 - 将订单推送至延迟队列
     *
     * @param orders 订单列表
     */
    @Override
    public void scheduleOrderCancel(List<OrderDO> orders) {
        orders.forEach(order -> scheduleOrderCancel(order.getId(), order.getTask() == null ? BigDecimal.ZERO : order.getTask().getExpireHours()));
    }

    /**
     * （交易流程规则配置）订单自动取消 - 将订单推送至延迟队列
     *
     * @param orderId    订单Id
     * @param delayHours 延迟时间
     */
    @Override
    public void scheduleOrderCancel(Long orderId, BigDecimal delayHours) {
        if(NumberUtil.isNullOrNegativeZero(orderId) || NumberUtil.isNullOrNegativeZero(delayHours)) {
            return;
        }

        long millSeconds = delayHours.multiply(BigDecimal.valueOf(60 * 60 * 1000L)).longValue();
        OrderCancelDTO cancelDTO = new OrderCancelDTO(orderId);
        scheduleDelayMessage(OrderQueueMessageTypeEnum.AUTO_CANCEL, cancelDTO, millSeconds);
    }

    /**
     * 订单自动取消
     *
     * @param jsonMessage 消息队列中的消息
     */
    @Override
    public void orderCancel(String jsonMessage) {
        log.info("订单自动取消消息 => " + jsonMessage);
        OrderCancelDTO cancelDTO = SerializeUtil.deserialize(jsonMessage, OrderCancelDTO.class);
        if(cancelDTO == null || NumberUtil.isNullOrNegativeZero(cancelDTO.getOrderId())) {
            log.error("订单自动取消反序列化消息错误 => " + jsonMessage);
            return;
        }

        vendorOrderService.cancelOrder(cancelDTO.getOrderId(), OrderConstant.SYSTEM_NAME, "", "", OrderConstant.SYSTEM_NAME, "交易流程设置自动取消");
    }

    /**
     * 定时收货 - 将发货批次推送至延迟队列
     *
     * @param order    订单
     * @param batchNo 发货批次
     */
    @Override
    public void scheduleOrderDelivery(OrderDO order, Integer batchNo) {
        //查询供应商自动收货时间（单位：天）配置
        Integer receiveDays = orderTimeParamService.getReceiveDays(order.getVendorMemberId(), order.getVendorRoleId(), order.getShopId());
        if(NumberUtil.isNullOrNegativeZero(receiveDays)) {
            return;
        }

        //转换为Json字符串，推送至消息队列
        String json = SerializeUtil.serialize(new OrderScheduleReceiveDTO(order.getId(), batchNo));

        OrderQueueDTO queueDTO = new OrderQueueDTO(OrderQueueMessageTypeEnum.AUTO_RECEIVE.getCode(), json, receiveDays * 86400000L);
        String jsonMessage = SerializeUtil.serialize(queueDTO);
        try {
            //延迟时间的单位是毫秒
            mqUtils.sendDelayMsg(OrderMqConstant.ORDER_SERVICE_DELAY_EXCHANGE, OrderMqConstant.ORDER_SERVICE_DELAY_ROUTING_KEY, jsonMessage, receiveDays * 86400000L);
        } catch (Exception e) {
            log.error("自动收货推送至消息队列错误，data: " + json + ", error:" + e.getMessage());
        }
    }

    /**
     * 定时收货 - 接收延迟队列数据，发货
     *
     * @param jsonMessage 从延迟队列中接收到的消息（订单Id和发货批次）
     */
    @Override
    public void scheduleOrderReceive(String jsonMessage) {
        try {
            OrderScheduleReceiveDTO scheduleReceive = SerializeUtil.deserialize(jsonMessage, OrderScheduleReceiveDTO.class);
            if(scheduleReceive == null || NumberUtil.isNullOrZero(scheduleReceive.getOrderId()) || NumberUtil.isNullOrZero(scheduleReceive.getBatchNo())) {
                log.error("自动收货错误，接收到的消息反序列化后数据错误：" + jsonMessage);
                return;
            }

            buyerOrderService.receiveOrder(OrderConstant.SYSTEM_NAME, "", "", OrderConstant.SYSTEM_NAME, scheduleReceive.getOrderId(), scheduleReceive.getBatchNo(), "",new ArrayList<>());
        } catch (Exception e) {
            log.error("自动收货错误，data:" + jsonMessage + ", error:" + e.getMessage());
        }
    }

    /**
     * 当调用营销服务接口返回拼团失败后，将拼团失败的订单重新加入Mq进行处理
     *
     * @param orderId 拼团订单Id
     * @param groupId 拼团Id（拼团失败时会为Null）
     */
    @Override
    public void scheduleGroupOrder(Long orderId, Long groupId) {
        OrderGroupDTO groupOrder = new OrderGroupDTO();
        groupOrder.setOrderId(orderId);
        groupOrder.setStatus(0);
        groupOrder.setGroupPurchaseId(groupId);
        String jsonMessage = SerializeUtil.serialize(groupOrder);
        log.info("重新将拼团失败的订单加入队列 => " + jsonMessage);
        try {
            mqUtils.sendMsg(OrderMqConstant.ORDER_GROUP_EXCHANGE, OrderMqConstant.ORDER_GROUP_ROUTINGKEY, jsonMessage);
        } catch (Exception e) {
            log.info("重新将拼团失败的订单加入队列异常 => " + jsonMessage + " , msg:" + e.getMessage());
        }
    }

    /**
     * 拼团订单通知
     *
     * @param jsonMessage 从消息队列中接收到的消息（订单Id和拼团状态）
     */
    @Override
    public void updateOrderGroupStatus(String jsonMessage) {
        log.info("接收到Mq拼团消息 => " + jsonMessage);

        OrderGroupDTO orderGroup = SerializeUtil.deserialize(jsonMessage, OrderGroupDTO.class);
        if(orderGroup == null || orderGroup.getStatus() == null || NumberUtil.isNullOrZero(orderGroup.getOrderId())) {
            log.error("拼团订单通知错误，接收到的消息反序列化后数据错误：" + jsonMessage);
            return;
        }

        OrderPromotionStatusEnum promotionStatus;
        switch (orderGroup.getStatus()) {
            case 0:
                promotionStatus = OrderPromotionStatusEnum.GROUP_FAILED;
                break;
            case 1:
                promotionStatus = OrderPromotionStatusEnum.GROUP_SUCCESS;
                break;
            default:
                log.error("拼团订单通知错误，接收到的消息反序列化后数据错误：" + jsonMessage);
                return;
        }

        buyerOrderService.updateGroupOrderStatus(OrderConstant.SYSTEM_NAME, "", "", OrderConstant.SYSTEM_NAME, orderGroup.getOrderId(), promotionStatus, orderGroup.getGroupPurchaseId());
    }

    /**
     * 结算服务 - 更新订单支付记录结算状态
     *
     * @param jsonMessage 从消息队列中接收到的消息（订单Id列表和结算状态）
     */
    @Override
    public void updateOrderPaymentStatus(String jsonMessage) {
        log.info("接收到Mq更新订单支付记录结算状态消息 => " + jsonMessage);

        PlatformSettlementOrderPaymentReq paymentVO = SerializeUtil.deserialize(jsonMessage, PlatformSettlementOrderPaymentReq.class);
        if(paymentVO == null || paymentVO.getSettlementStatus() == null || CollectionUtils.isEmpty(paymentVO.getPaymentIds())) {
            log.error("更新订单支付记录结算状态消息反序列化后数据错误：" + jsonMessage);
            return;
        }

        try {
            baseOrderPaymentService.updatePaymentSettlementStatus(paymentVO.getPaymentIds(), paymentVO.getSettlementStatus());
        } catch (BusinessException e) {
            log.info("更新订单支付记录结算状态 => mq_msg:" + jsonMessage + ", code:" + e.getErrorCode() + ", msg:" + e.getMessage());
        } catch (Exception e) {
            log.info("更新订单支付记录结算状态 => mq_msg:" + jsonMessage + ", msg:" + e.getMessage());
        }
    }

    /**
     * 延迟队列 - 接收到查询支付结果消息时，查询支付状态，并更新订单状态
     * @param times        加入队列的次数
     * @param jsonMessage 从消息队列中接收到的消息
     */
    @Transactional
    @Override
    public void orderQueueCallback(Integer times, String jsonMessage) {
        log.info("接收到延迟队列查询订单支付状态消息 => " + jsonMessage);

        OrderPaymentQueueDTO queueDTO = SerializeUtil.deserialize(jsonMessage, OrderPaymentQueueDTO.class);
        if(queueDTO == null || CollectionUtils.isEmpty(queueDTO.getOrderIds()) || CollectionUtils.isEmpty(queueDTO.getPaymentIds())) {
            log.error("延迟队列查询订单支付状态消息反序列化后数据错误：" + jsonMessage);
            return;
        }
        OrderPaymentCallbackStatusEnum statusResult = null;
        try {
            statusResult = baseOrderPaymentService.findPaymentStatus(queueDTO.getPayChannel(), queueDTO.getTradeNo(), queueDTO.getOrderIds(), queueDTO.getPaymentIds());
        } catch (BusinessException e) {
            log.error("订单延迟队列处理错误：code:" + e.getErrorCode() + ", msg:" + e.getMessage());
            //重新加入队列，10分钟后查询
            reScheduleDelayMessage(OrderQueueMessageTypeEnum.PAYMENT_QUERY, jsonMessage, 10 * 60 * 1000, times + 1);
            return;
        } catch (Exception e) {
            log.error("订单延迟队列处理错误：" + e.getMessage());
            //重新加入队列，10分钟后查询
            reScheduleDelayMessage(OrderQueueMessageTypeEnum.PAYMENT_QUERY, jsonMessage, 10 * 60 * 1000, times + 1);
            return;
        }

        try {
            baseOrderPaymentService.buyerPayCallback(queueDTO.getOrderIds(), queueDTO.getBatchNo(), queueDTO.getTradeNo(), statusResult, queueDTO.getUserName(), queueDTO.getOrganizationName(), queueDTO.getJobTitle());
        } catch (BusinessException e) {
            log.error("订单查询状态后更新处理错误，code:" + e.getErrorCode() + ", msg:" + e.getMessage());
        } catch (Exception e) {
            log.error("订单查询状态后更新处理错误，:" +  e.getMessage());
        }
    }

    /**
     * 订单支付 - 回调通知队列
     *
     * @param jsonMessage 从消息队列中接收到的消息
     */
    @Transactional
    @Override
    public void orderPayCallback(String jsonMessage) {
        log.info("接收到Mq订单支付回调消息 => " + jsonMessage);
        OrderPaymentCallbackDTO callbackDTO = SerializeUtil.deserialize(jsonMessage, OrderPaymentCallbackDTO.class);
        if(callbackDTO == null || callbackDTO.getPayStatus() == null || !StringUtils.hasLength(callbackDTO.getAttach())) {
            log.error("订单支付回调消息反序列化后数据错误：" + jsonMessage);
            return;
        }

        OrderPaymentCallbackStatusEnum callbackStatusEnum = OrderPaymentCallbackStatusEnum.parse(callbackDTO.getPayStatus());
        if(callbackStatusEnum == null) {
            log.error("订单支付回调消息反序列化后数据错误：" + jsonMessage);
            return;
        }

        OrderPaymentAttachDTO attachDTO = SerializeUtil.deserialize(callbackDTO.getAttach(), OrderPaymentAttachDTO.class);
        if(attachDTO == null || NumberUtil.isNullOrNegativeZero(attachDTO.getBatchNo()) || CollectionUtils.isEmpty(attachDTO.getOrderIds())) {
            log.error("订单支付回调消息，透传消息反序列化后数据错误：" + jsonMessage);
            return;
        }

        //从会员服务查询用户信息
        MemberUserDTO memberUserDTO = NumberUtil.isNullOrZero(attachDTO.getUserId()) ? new MemberUserDTO() : memberFeignService.findUserDetail(attachDTO.getUserId());

        try {
            baseOrderPaymentService.buyerPayCallback(attachDTO.getOrderIds(), attachDTO.getBatchNo(), attachDTO.getTradeNo(), callbackStatusEnum, memberUserDTO.getUserName(), memberUserDTO.getOrganizationName(), memberUserDTO.getJobTitle());
        } catch (BusinessException e) {
            log.error("订单支付mq回调处理错误，code:" + e.getErrorCode() + ", msg:" + e.getMessage());
        } catch (Exception e) {
            log.error("订单支付mq回调处理错误, msg:" + e.getMessage());
        }
    }

    /**
     * 订单发送赠品优惠券
     * @param jsonMessage 消息队列中的消息
     */
    @Override
    public void sengCoupon(String jsonMessage) {
        MarketingQueueDTO marketingQueueDTO = new MarketingQueueDTO();
        marketingQueueDTO.setType(MkQueueMessageTypeEnum.GIFT_COUPON.getCode());
        marketingQueueDTO.setMessage(jsonMessage);
        //转换为Json字符串，推送至消息队列
        String json = JSONUtil.toJsonStr(marketingQueueDTO);
        log.info("订单发送赠品优惠券消息:{}",json);
        mqUtils.sendMsg(MarketingMqConstant.MK_NORMAL_EXCHANGE, MarketingMqConstant.MK_NORMAL_ROUTING_KEY, json);
    }
}
