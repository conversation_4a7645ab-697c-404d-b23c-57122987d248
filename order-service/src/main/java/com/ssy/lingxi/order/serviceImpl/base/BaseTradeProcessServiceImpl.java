package com.ssy.lingxi.order.serviceImpl.base;

import com.ssy.lingxi.component.base.enums.order.OrderTradeProcessTypeEnum;
import com.ssy.lingxi.order.entity.BaseTradeProcessDO;
import com.ssy.lingxi.order.enums.BaseTradeProcessEnum;
import com.ssy.lingxi.order.model.dto.ProcessQueryRequest;
import com.ssy.lingxi.order.model.resp.platform.PlatformBaseTradeProcessResp;
import com.ssy.lingxi.order.model.resp.process.BaseTradeProcessResp;
import com.ssy.lingxi.order.repository.BaseTradeProcessRepository;
import com.ssy.lingxi.order.service.base.IBaseTradeProcessService;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 基础交易流程相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-24
 */
@Service
public class BaseTradeProcessServiceImpl implements IBaseTradeProcessService {
    @Resource
    private BaseTradeProcessRepository baseTradeProcessRepository;

    /**
     * 查询所有交易流程
     *
     * @return 交易流程列表
     */
    @Override
    public List<BaseTradeProcessResp> listProcesses() {
        return baseTradeProcessRepository.findAll(Sort.by("id").ascending()).stream().map(process -> {
            BaseTradeProcessResp processVO = new BaseTradeProcessResp();
            processVO.setBaseProcessid(process.getId());
            processVO.setProcessName(BaseTradeProcessEnum.getNameByCode(process.getCode()));
            processVO.setProcessType(process.getProcessType());
            processVO.setProcessTypeName(OrderTradeProcessTypeEnum.getNameByCode(process.getProcessType()));
            processVO.setDescription(BaseTradeProcessEnum.getRemarkByCode(process.getCode()));
            processVO.setPayTimes(process.getPayTimes());
            return processVO;
        }).collect(Collectors.toList());
    }

    /**
     * 从平台后台查询基础交易流程
     *
     * @return 交易流程列表
     */
    @Override
    public List<PlatformBaseTradeProcessResp> listProcessByPlatform(ProcessQueryRequest queryRequest) {
        Specification<BaseTradeProcessDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> andList = new ArrayList<>();
            if (Objects.nonNull(queryRequest.getProcessType())){
                andList.add(criteriaBuilder.equal(root.get("processType").as(Integer.class), queryRequest.getProcessType()));
            }
            return criteriaBuilder.and(andList.toArray(new Predicate[0]));
        };
        return baseTradeProcessRepository.findAll(specification, Sort.by("id").ascending()).stream().map(this::createProcessVO).collect(Collectors.toList());
    }

    /**
     * 创建展示类
     * @param process
     * @return
     */
    private PlatformBaseTradeProcessResp createProcessVO(BaseTradeProcessDO process){
        PlatformBaseTradeProcessResp processVO = new PlatformBaseTradeProcessResp();
        processVO.setBaseProcessid(process.getId());
        processVO.setProcessName(Optional.ofNullable(process.getProcessName()).orElse(BaseTradeProcessEnum.getNameByCode(process.getCode())));
        processVO.setProcessType(process.getProcessType());
        processVO.setProcessTypeName(OrderTradeProcessTypeEnum.getNameByCode(process.getProcessType()));
        processVO.setDescription(Optional.ofNullable(process.getDescription()).orElse(BaseTradeProcessEnum.getRemarkByCode(process.getCode())));
        processVO.setProcessImage(process.getProcessImage());
        return processVO;
    }

    /**
     * 根据Id查询
     *
     * @param id id
     * @return 查询结果
     */
    @Override
    public BaseTradeProcessDO findById(Long id) {
        return baseTradeProcessRepository.findById(id).orElse(null);
    }

    /**
     * 根据基础ID查询
     *
     * @param engineId
     * @return
     */
    @Override
    public Optional<BaseTradeProcessDO> findByEngineId(Long engineId) {
        return baseTradeProcessRepository.findFirstByEngineId(engineId);
    }

}
