package com.ssy.lingxi.order.serviceImpl.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ssy.lingxi.common.enums.order.OrderDeliveryTypeEnum;
import com.ssy.lingxi.common.enums.order.OrderSourceKindEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.RandomNumberUtil;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.MessageNoticeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.rest.config.ThirdPartyConfig;
import com.ssy.lingxi.component.rest.model.req.yatai.HeadReq;
import com.ssy.lingxi.component.rest.model.req.yatai.ProposalReq;
import com.ssy.lingxi.component.rest.model.resp.yatai.ProposalResp;
import com.ssy.lingxi.component.rest.service.CargoApplyService;
import com.ssy.lingxi.contract.api.model.resp.OrderContractResp;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.resp.MemberInsuredIdNoResp;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.*;
import com.ssy.lingxi.order.model.bo.InventoryProductBO;
import com.ssy.lingxi.order.model.bo.OrderDeliveryBO;
import com.ssy.lingxi.order.model.bo.OrderReceiveBO;
import com.ssy.lingxi.order.model.dto.MemberCategoryDTO;
import com.ssy.lingxi.order.model.dto.OrderProductCategoryDTO;
import com.ssy.lingxi.order.model.dto.OrderSettlementDTO;
import com.ssy.lingxi.order.model.dto.ReceivedProductSettlementDTO;
import com.ssy.lingxi.order.model.req.basic.ProductReceiveOrDeliveryReq;
import com.ssy.lingxi.order.model.req.vendor.VendorDeliveryReq;
import com.ssy.lingxi.order.model.req.vendor.VendorToDeliveryProductReq;
import com.ssy.lingxi.order.model.resp.basic.*;
import com.ssy.lingxi.order.model.resp.platform.PlatformOrderDeliveryDetailResp;
import com.ssy.lingxi.order.repository.*;
import com.ssy.lingxi.order.service.base.*;
import com.ssy.lingxi.order.service.feign.IContractFeignService;
import com.ssy.lingxi.order.service.feign.IMemberFeignService;
import com.ssy.lingxi.order.service.feign.IProductFeignService;
import com.ssy.lingxi.order.service.feign.ISettleAccountFeignService;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.feign.IFreightSpaceFeign;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuStockResp;
import com.ssy.lingxi.product.api.model.resp.warehouse.InventoryPatternResp;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单收货-发货相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-20
 */
@Service
public class BaseOrderDeliveryServiceImpl implements IBaseOrderDeliveryService {
    @Resource
    private OrderProductRepository orderProductRepository;

    @Resource
    private OrderDeliveryRepository orderDeliveryRepository;

    @Resource
    private OrderDeliveryProductRepository orderDeliveryProductRepository;

    @Resource
    private IMemberFeignService memberFeignService;

    @Resource
    private IProductFeignService productFeignService;

    @Resource
    private ISettleAccountFeignService settleAccountFeignService;

    @Resource
    private IBaseOrderService baseOrderService;

    @Resource
    private IBaseOrderTaskService baseOrderTaskService;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private IBaseOrderScheduleService baseOrderScheduleService;

    @Resource
    private IBaseOrderHistoryService baseOrderHistoryService;

    @Resource
    private IBaseOrderDeliveryService baseOrderDeliveryService;

    @Resource
    private IBaseOrderPaymentService baseOrderPaymentService;

    @Resource
    private IContractFeignService contractFeignService;

    @Resource
    private IFreightSpaceFeign freightSpaceFeign;

    @Resource
    private ICommodityFeign commodityFeign;

    @Resource
    private ThirdPartyConfig thirdPartyConfig;

    @Resource
    private IMemberFeign memberFeign;

    @Resource
    private CargoApplyService cargoApplyService;

    @Resource
    private OrderPacInsuranceRepository orderPacInsuranceRepository;

    /**
     * 查询采购商订单收发货明细
     * @param order 订单
     * @param warehouseId 仓库id
     * @param warehouseName 仓库name
     * @return 收发货统计
     */
    @Override
    public List<BuyerOrderDeliveryDetailResp> listBuyerOrderDeliveryDetails(OrderDO order, Long warehouseId, String warehouseName) {
        List<OrderDeliveryDO> deliveries = orderDeliveryRepository.findByOrder(order, Sort.by("id").ascending());
        return deliveries.stream().map(orderDelivery -> {
            BuyerOrderDeliveryDetailResp detailVO = new BuyerOrderDeliveryDetailResp();
            detailVO.setBatchNo(orderDelivery.getBatchNo());
            detailVO.setCreateTime(orderDelivery.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            detailVO.setDeliveryNo(orderDelivery.getDeliveryNo());
            detailVO.setLogisticsNo(orderDelivery.getLogisticsNo());
            if (StringUtils.hasLength(orderDelivery.getCompany())) {
                detailVO.setCompany(orderDelivery.getCompany());
            } else {
                //如果没有物流公司名称，判断有没有顺丰单号
                if (StringUtils.hasLength(order.getSfLogisticsNo())) {
                    detailVO.setCompany("顺丰快递");
                }
            }
            detailVO.setReceiptNo(orderDelivery.getReceiptNo());
            detailVO.setReceiptTime(orderDelivery.getReceiptTime() == null ? "" : orderDelivery.getReceiptTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            detailVO.setReceiveBill(orderDelivery.getReceiveBill());
            detailVO.setSelfCode(StrUtil.isEmpty(orderDelivery.getSelfCode()) ? "" : orderDelivery.getSelfCode());
            detailVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(orderDelivery.getBuyerInnerStatus()));
            //是否显示“确认收货”按钮
            detailVO.setShowReceive(orderDelivery.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode()));
            detailVO.setProducts(orderDelivery.getDeliveryProducts().stream().sorted(Comparator.comparingLong(OrderDeliveryProductDO::getOrderProductId)).map(product -> {
                OrderDeliveryProductResp productVO = new OrderDeliveryProductResp();
                productVO.setProductId(product.getProductId());
                productVO.setSkuId(product.getSkuId());
                productVO.setProductNo(product.getProductNo());
                productVO.setName(StringUtils.hasLength(product.getSpec()) ? product.getName().concat("/").concat(product.getSpec()) : product.getName());
                productVO.setCategory(product.getCategory());
                productVO.setBrand(product.getBrand());
                productVO.setSpec(product.getSpec());
                productVO.setUnit(product.getUnit());
                productVO.setQuotedProductId(product.getQuotedProductId());
                productVO.setQuotedSkuId(product.getQuotedSkuId());
                productVO.setQuotedProductNo(product.getQuotedProductNo());
                productVO.setQuotedName(product.getQuotedName());
                productVO.setQuotedCategory(product.getQuotedCategory());
                productVO.setQuotedBrand(product.getQuotedBrand());
                productVO.setQuotedSpec(product.getQuotedSpec());
                productVO.setQuantity(NumberUtil.formatToInteger(product.getQuantity()));
                productVO.setDelivered(NumberUtil.formatToInteger(product.getDelivered()));
                productVO.setReceived(NumberUtil.formatToInteger(product.getReceived()));
                productVO.setDifferCount(NumberUtil.formatToInteger(product.getDifferCount()));
                productVO.setWarehouseId(warehouseId);
                productVO.setWarehouseName(warehouseName);
                productVO.setNetWeight(product.getNetWeight());
                productVO.setGoodsWeight(product.getGoodsWeight());
                productVO.setBatchJudgmentType(product.getBatchJudgmentType());
                if(product.getAcceptanceCount() != null){
                    productVO.setAcceptanceCount(NumberUtil.formatToInteger(product.getAcceptanceCount()));
                }
                if(product.getConcessionToReceiveCount() != null){
                    productVO.setConcessionToReceiveCount(NumberUtil.formatToInteger(product.getConcessionToReceiveCount()));
                }
                if(product.getRejectCount() != null){
                    productVO.setRejectCount(NumberUtil.formatToInteger(product.getRejectCount()));
                }
                return productVO;
            }).collect(Collectors.toList()));
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询供应商订单收发货明细
     * @param order 订单
     * @return 收发货统计
     */
    @Override
    public List<VendorOrderDeliveryDetailResp> listVendorOrderDeliveryDetails(OrderDO order) {
        List<OrderDeliveryDO> deliveries = orderDeliveryRepository.findByOrder(order, Sort.by("id").ascending());
        return deliveries.stream().map(orderDelivery -> {
            VendorOrderDeliveryDetailResp detailVO = new VendorOrderDeliveryDetailResp();
            detailVO.setBatchNo(orderDelivery.getBatchNo());
            detailVO.setCreateTime(orderDelivery.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            detailVO.setDeliveryNo(orderDelivery.getDeliveryNo());
            detailVO.setLogisticsNo(orderDelivery.getLogisticsNo());
            detailVO.setCompany(StringUtils.hasLength(orderDelivery.getCompany()) ? orderDelivery.getCompany() : "顺丰快递");
            detailVO.setReceiptNo(orderDelivery.getReceiptNo());
            detailVO.setReceiptTime(orderDelivery.getReceiptTime() == null ? "" : orderDelivery.getReceiptTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            detailVO.setReceiveBill(orderDelivery.getReceiveBill());
            detailVO.setInnerStatusName(VendorInnerStatusEnum.getNameByCode(orderDelivery.getVendorInnerStatus()));
            detailVO.setProducts(orderDelivery.getDeliveryProducts().stream().sorted(Comparator.comparingLong(OrderDeliveryProductDO::getSkuId)).map(product -> {
                OrderDeliveryProductResp productVO = new OrderDeliveryProductResp();
                productVO.setProductId(product.getProductId());
                productVO.setSkuId(product.getSkuId());
                productVO.setProductNo(product.getProductNo());
                productVO.setName(StringUtils.hasLength(product.getSpec()) ? product.getName().concat("/").concat(product.getSpec()) : product.getName());
                productVO.setCategory(product.getCategory());
                productVO.setBrand(product.getBrand());
                productVO.setSpec(product.getSpec());
                productVO.setUnit(product.getUnit());
                productVO.setQuotedProductId(product.getQuotedProductId());
                productVO.setQuotedSkuId(product.getQuotedSkuId());
                productVO.setQuotedProductNo(product.getQuotedProductNo());
                productVO.setQuotedName(product.getQuotedName());
                productVO.setQuotedCategory(product.getQuotedCategory());
                productVO.setQuotedBrand(product.getQuotedBrand());
                productVO.setQuotedSpec(product.getQuotedSpec());
                productVO.setQuantity(NumberUtil.formatToInteger(product.getQuantity()));
                productVO.setDelivered(NumberUtil.formatToInteger(product.getDelivered()));
                productVO.setReceived(NumberUtil.formatToInteger(product.getReceived()));
                productVO.setDifferCount(NumberUtil.formatToInteger(product.getDifferCount()));
                productVO.setBatchJudgmentType(product.getBatchJudgmentType());
                if(product.getAcceptanceCount() != null){
                    productVO.setAcceptanceCount(NumberUtil.formatToInteger(product.getAcceptanceCount()));
                }
                if(product.getConcessionToReceiveCount() != null){
                    productVO.setConcessionToReceiveCount(NumberUtil.formatToInteger(product.getConcessionToReceiveCount()));
                }
                if(product.getRejectCount() != null){
                    productVO.setRejectCount(NumberUtil.formatToInteger(product.getRejectCount()));
                }
                return productVO;
            }).collect(Collectors.toList()));
            //是否显示“确认发货”、“确认回单”按钮
            detailVO.setShowDelivery(false);
            detailVO.setShowReply(false);
            //是否显示“核销自提订单”按钮
            detailVO.setShowVerify(orderDelivery.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_DELIVERY_CONFIRMED.getCode()) && StrUtil.isNotEmpty(orderDelivery.getSelfCode()));
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * 平台后台 - 查询订单收发货明细
     * @param order 订单
     * @return 收发货统计
     */
    @Override
    public List<PlatformOrderDeliveryDetailResp> listOrderDeliveryDetails(OrderDO order) {
        List<OrderDeliveryDO> deliveries = orderDeliveryRepository.findByOrder(order, Sort.by("id").ascending());
        return deliveries.stream().map(orderDelivery -> {
            PlatformOrderDeliveryDetailResp detailVO = new PlatformOrderDeliveryDetailResp();
            detailVO.setBatchNo(orderDelivery.getBatchNo());
            detailVO.setCreateTime(orderDelivery.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            detailVO.setDeliveryNo(orderDelivery.getDeliveryNo());
            detailVO.setLogisticsNo(orderDelivery.getLogisticsNo());
            detailVO.setCompany(orderDelivery.getCompany());
            detailVO.setReceiptNo(orderDelivery.getReceiptNo());
            detailVO.setReceiptTime(orderDelivery.getReceiptTime() == null ? "" : orderDelivery.getReceiptTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            detailVO.setInnerStatusName(orderDelivery.getReceiptTime() == null ? VendorInnerStatusEnum.getNameByCode(orderDelivery.getVendorInnerStatus()) : BuyerInnerStatusEnum.getNameByCode(orderDelivery.getBuyerInnerStatus()));
            detailVO.setProducts(orderDelivery.getDeliveryProducts().stream().sorted(Comparator.comparingLong(OrderDeliveryProductDO::getSkuId)).map(product -> {
                OrderDeliveryProductResp productVO = new OrderDeliveryProductResp();
                productVO.setProductId(product.getProductId());
                productVO.setSkuId(product.getSkuId());
                productVO.setProductNo(product.getProductNo());
                productVO.setName(StringUtils.hasLength(product.getSpec()) ? product.getName().concat("/").concat(product.getSpec()) : product.getName());
                productVO.setCategory(product.getCategory());
                productVO.setBrand(product.getBrand());
                productVO.setSpec(product.getSpec());
                productVO.setUnit(product.getUnit());
                productVO.setQuotedProductId(product.getQuotedProductId());
                productVO.setQuotedSkuId(product.getQuotedSkuId());
                productVO.setQuotedProductNo(product.getQuotedProductNo());
                productVO.setQuotedName(product.getQuotedName());
                productVO.setQuotedCategory(product.getQuotedCategory());
                productVO.setQuotedBrand(product.getQuotedBrand());
                productVO.setQuotedSpec(product.getQuotedSpec());
                productVO.setQuantity(NumberUtil.formatToInteger(product.getQuantity()));
                productVO.setDelivered(NumberUtil.formatToInteger(product.getDelivered()));
                productVO.setReceived(NumberUtil.formatToInteger(product.getReceived()));
                productVO.setDifferCount(NumberUtil.formatToInteger(product.getDifferCount()));
                productVO.setBatchJudgmentType(product.getBatchJudgmentType());
                if(product.getAcceptanceCount() != null){
                    productVO.setAcceptanceCount(NumberUtil.formatToInteger(product.getAcceptanceCount()));
                }
                if(product.getConcessionToReceiveCount() != null){
                    productVO.setConcessionToReceiveCount(NumberUtil.formatToInteger(product.getConcessionToReceiveCount()));
                }
                if(product.getRejectCount() != null){
                    productVO.setRejectCount(NumberUtil.formatToInteger(product.getRejectCount()));
                }
                return productVO;
            }).collect(Collectors.toList()));
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * 供应订单发货，调用方需要保存OrderDO
     * @param order      订单
     * @param deliveryVO 接口参数
     * @return 发货结果
     */
    @Transactional
    @Override
    public OrderDeliveryBO checkVendorOrderDelivery(OrderDO order, VendorDeliveryReq deliveryVO) {
        //Step 1: 发货数量可以为0，要进行过滤
        deliveryVO.getProducts().removeIf(p -> p.getDeliveryCount().compareTo(BigDecimal.ZERO) <= 0);
        if (CollectionUtils.isEmpty(deliveryVO.getProducts())) {
            return new OrderDeliveryBO(true);
        }

        //Step 1: 查询商品
        List<OrderProductDO> orderProducts = orderProductRepository.findByOrder(order);
        if (deliveryVO.getProducts().stream().anyMatch(p -> orderProducts.stream().noneMatch(orderProduct -> orderProduct.getId().equals(p.getOrderProductId())))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }

        //Step 2 : 判断是否已经全部发货完毕，并统计：差异数量=已发货-已收货，未发货=采购数量-已发货
        for (OrderProductDO orderProduct : orderProducts) {
            deliveryVO.getProducts().stream().filter(product -> product.getOrderProductId().equals(orderProduct.getId())).findFirst().ifPresent(productVO ->{
                orderProduct.setDelivered(orderProduct.getDelivered().add(productVO.getDeliveryCount()));
                orderProduct.setLastCount(productVO.getDeliveryCount());
            });

            //关键：如果发货数超过购买数，剩余数量设置为0
            orderProduct.setLeftCount(NumberUtil.max(orderProduct.getQuantity().subtract(orderProduct.getDelivered()), BigDecimal.ZERO));
            orderProduct.setDifferCount(orderProduct.getDelivered().subtract(orderProduct.getReceived()));
        }

        orderProductRepository.saveAll(orderProducts);

        //Step 3 : 发货批次+1
        int batchNo = orderDeliveryRepository.findByOrder(order).stream().mapToInt(OrderDeliveryDO::getBatchNo).max().orElse(0) + 1;
        String deliveryNo = RandomNumberUtil.randomUniqueNumber("FH", 7);

        //Step 4 : 生成发货记录，记录发货商品
        OrderDeliveryDO delivery = new OrderDeliveryDO();
        delivery.setOrder(order);
        delivery.setCreateTime(StringUtils.hasLength(deliveryVO.getDeliveryTime()) ? LocalDateTime.parse(deliveryVO.getDeliveryTime(), OrderConstant.DEFAULT_TIME_FORMATTER) : LocalDateTime.now());
        delivery.setBatchNo(batchNo);
        delivery.setDeliveryNo(deliveryNo);
        delivery.setAddress(deliveryVO.getAddress());
        delivery.setAddressId(deliveryVO.getAddressId());
        delivery.setLogisticsNo(StringUtils.hasLength(deliveryVO.getLogisticsNo()) ? deliveryVO.getLogisticsNo().trim() : "");
        delivery.setCompany(StringUtils.hasLength(deliveryVO.getCompany()) ? deliveryVO.getCompany() : "");
        delivery.setCompanyCode(StringUtils.hasLength(deliveryVO.getCompanyCode()) ? deliveryVO.getCompanyCode() : "");
        delivery.setReceiptNo("");
        delivery.setReceiptTime(null);
        delivery.setReceiveBill("");
        delivery.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_DELIVERY_CONFIRMED.getCode());
        delivery.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode());
        delivery.setLogisticsFillType(deliveryVO.getLogisticsFillType());

        List<OrderDeliveryProductDO> deliveryProducts = deliveryVO.getProducts().stream().map(productVO -> {
            OrderDeliveryProductDO deliveryProduct = new OrderDeliveryProductDO();
            deliveryProduct.setOrderId(order.getId());
            deliveryProduct.setBatchNo(batchNo);
            deliveryProduct.setDeliveryNo(deliveryNo);
            orderProducts.stream().filter(orderProduct -> orderProduct.getId().equals(productVO.getOrderProductId())).findFirst().ifPresent(orderProduct -> {
                deliveryProduct.setOrderProductId(orderProduct.getId());
                deliveryProduct.setProductId(orderProduct.getProductId());
                deliveryProduct.setSkuId(orderProduct.getSkuId());
                deliveryProduct.setProductNo(orderProduct.getProductNo());
                deliveryProduct.setName(orderProduct.getName());
                deliveryProduct.setCategory(orderProduct.getCategory());
                deliveryProduct.setBrand(orderProduct.getBrand());
                deliveryProduct.setUnit(orderProduct.getUnit());
                deliveryProduct.setSpec(orderProduct.getSpec());
                deliveryProduct.setPrice(orderProduct.getPrice());
                deliveryProduct.setRefPrice(orderProduct.getRefPrice());
                deliveryProduct.setTaxRate(orderProduct.getTaxRate());
                deliveryProduct.setQuotedProductId(orderProduct.getMaterial() == null ? 0L : orderProduct.getMaterial().getProductId());
                deliveryProduct.setQuotedSkuId(orderProduct.getMaterial() == null ? 0L : orderProduct.getMaterial().getSkuId());
                deliveryProduct.setQuotedProductNo(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getProductNo());
                deliveryProduct.setQuotedName(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getName());
                deliveryProduct.setQuotedCategory(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getCategory());
                deliveryProduct.setQuotedBrand(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getBrand());
                deliveryProduct.setQuotedSpec(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getSpec());
                deliveryProduct.setQuantity(orderProduct.getQuantity());
                deliveryProduct.setDelivered(productVO.getDeliveryCount());
                deliveryProduct.setReceived(BigDecimal.ZERO);
                deliveryProduct.setDifferCount(productVO.getDeliveryCount());
                deliveryProduct.setNetWeight(orderProduct.getNetWeight());
                deliveryProduct.setGoodsWeight(orderProduct.getGoodsWeight());
            });
            return deliveryProduct;
        }).collect(Collectors.toList());

        //判断是否有自提商品，有则生成自提码
        Set<Long> orderProductIds = deliveryVO.getProducts().stream().map(VendorToDeliveryProductReq::getOrderProductId).collect(Collectors.toSet());
        boolean havePickUp = orderProducts.stream().filter(p -> orderProductIds.contains(p.getId())).anyMatch(p -> OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode().equals(p.getDeliverType()));
        delivery.setSelfCode(havePickUp ? RandomNumberUtil.randomUniqueNumber() : "");

        //是否有自提商品
        order.setPickUp(havePickUp);

        //Step 5 : 保存
        orderDeliveryProductRepository.saveAll(deliveryProducts);
        delivery.setDeliveryProducts(new HashSet<>(deliveryProducts));
        orderDeliveryRepository.saveAndFlush(delivery);

        // Step 5.1 : 快递太平洋保险
        // 判断订单保费大于0，如果大于0，则投保
        if (ObjectUtil.isNotEmpty(order.getPacInsuranceAmount()) && order.getPacInsuranceAmount().compareTo(BigDecimal.ZERO) > 0) {
            ProposalReq proposalReq = new ProposalReq();
            HeadReq head = new HeadReq();
            head.setTransNo(String.valueOf(System.currentTimeMillis()));
            head.setBusiSrc("SJY");
            head.setInfType("PROPOSAL");
            head.setUserCode(thirdPartyConfig.getUserCode());
            head.setPassword(thirdPartyConfig.getPassword());
            // 获取当前时间，转换成yyyyMMddHHmmss格式
            head.setTransTime(DateUtil.now());

            ProposalReq.Request request = new ProposalReq.Request();
            request.setHead(head);

            ProposalReq.Request.Body body = new ProposalReq.Request.Body();
            body.setProductCode("0205");
            body.setInsuranceCode("06");
            body.setSumInsured(order.getPacInsuranceAmount().doubleValue());
            body.setRemark("投保");

            ProposalReq.Request.Body.AppliVo appliVo = new ProposalReq.Request.Body.AppliVo();
            appliVo.setAppliName("深圳市尚金缘珠宝实业有限公司");
            appliVo.setAppliType("02");
            appliVo.setIdType("04");
            appliVo.setIdNo("91440300777174800T");
            appliVo.setMobilePhone(thirdPartyConfig.getMobile());
            appliVo.setEMail(thirdPartyConfig.getEmail());
            body.setAppliVo(appliVo);

            ProposalReq.Request.Body.InsuredVo insuredVo = new ProposalReq.Request.Body.InsuredVo();
            insuredVo.setInsuredName("深圳市尚金缘珠宝实业有限公司");
            insuredVo.setInsuredType("02");
            insuredVo.setInsuredIdType("04");
            insuredVo.setInsuredIdNo("91440300777174800T");
            insuredVo.setInsuredMobilePhone(thirdPartyConfig.getMobile());
            insuredVo.setEmail(thirdPartyConfig.getEmail());
            body.setInsuredVo(insuredVo);

            ProposalReq.Request.Body.ItemMainVo itemMainVo = new ProposalReq.Request.Body.ItemMainVo();
            itemMainVo.setExpressType("02");
            itemMainVo.setExpressNo(deliveryVO.getLogisticsNo());
            itemMainVo.setTransDate(DateUtil.now());
            // 判断地址是否超过50个字符，如果超过，则截取前50个字符
            itemMainVo.setLoadPort(StrUtil.sub(deliveryVO.getAddress(), 0, 50));
            itemMainVo.setTargetPort(order.getConsignee().getCityName());
            // 获取订单商品名称，用逗号分割
            String productNames = orderProducts.stream()
                    .filter(orderProduct -> deliveryVO.getProducts().stream().anyMatch(p -> p.getOrderProductId().equals(orderProduct.getId())))
                    .map(OrderProductDO::getName)
                    .collect(Collectors.joining(","));
            itemMainVo.setDetailName(productNames);
            itemMainVo.setDetailNo(String.valueOf(order.getTotalProductCount()));
            body.setItemMainVo(itemMainVo);

            request.setBody(body);
            proposalReq.setRequest(request);

//            try {
//                ProposalResp proposal = cargoApplyService.proposal(proposalReq);
//                OrderPacInsuranceDO orderPacInsurance = new OrderPacInsuranceDO();
//                orderPacInsurance.setOrder(order);
//                orderPacInsurance.setApplyNo(proposal.getResponse().getBody().getApplyNo());
//                orderPacInsurance.setSumPremium(BigDecimal.valueOf(proposal.getResponse().getBody().getSumPremium()));
//                orderPacInsurance.setProposalNo(proposal.getResponse().getBody().getProposalNo());
//                orderPacInsurance.setPolicyNo(proposal.getResponse().getBody().getPolicyNo());
//                orderPacInsurance.setPolicyUrl(proposal.getResponse().getBody().getPolicyUrl());
//
//                orderPacInsuranceRepository.saveAndFlush(orderPacInsurance);
//                order.setPacInsurance(orderPacInsurance);
//                orderRepository.saveAndFlush(order);
//            } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
//                throw new RuntimeException(e);
//            }
        }


        //Step 6: 设置订单关联，在调用方需要保存OrderDO
        order.setHasDelivered(true);
        order.getDeliveries().add(delivery);

        //Step 7: 判断流程跳转参数（跳转参数在流程图中定义）
        // A. 如果发货未完成
        //          流程不跳转，即参数为0
        // B. 如果发货完成（此时必有至少一次未收货）
        //    b-1: 有且仅有一次未收货， 跳转参数为 1，流程执行到“收货”任务，当最后一次收货完成后设置跳转参数为-1，收发货流程完成
        //    b-2: 大于一次未收货，跳转参数为 2，执行另一个回环分支，改变供应商状态
        boolean deliveryDone = false;
        int taskJumpParameter = 0;
        if (orderProducts.stream().allMatch(orderProduct -> orderProduct.getLeftCount().compareTo(BigDecimal.ZERO) == 0)) {
            deliveryDone = true;
            if (order.getDeliveries().stream().filter(d -> !d.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_CONFIRM_RECEIPT.getCode())).count() == 1) {
                taskJumpParameter = 1;
            } else {
                taskJumpParameter = 2;
            }
        }

        //发送订单发货确认通知消息给采购会员
        if (havePickUp) {
            baseOrderService.messageBuyerDeliverSelfOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), MessageNoticeEnum.SELL_DELIVER_SELF_ORDER.getCode(), order.getOrderNo(), order.getDigest(), delivery.getAddress(), delivery.getSelfCode());
        } else {
            baseOrderService.messageBuyerDeliverOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), MessageNoticeEnum.SELL_DELIVER_ORDER.getCode(), order.getOrderNo(), order.getDigest(), deliveryVO.getCompany(), deliveryVO.getLogisticsNo());
        }

        return new OrderDeliveryBO(taskJumpParameter, batchNo, deliveryNo, deliveryDone);
    }

    /**
     * 采购订单收货
     * @param order       订单
     * @param batchNo     发货批次
     * @param receiveBill 收货回单url地址
     * @return 收货结果
     */
    @Override
    public OrderReceiveBO checkBuyerOrderReceive(OrderDO order, Integer batchNo, String receiveBill) {
        //Step 1 : 查询所有发货记录，用于判断是否已经全部收货完毕
        List<OrderDeliveryDO> deliveries = orderDeliveryRepository.findByOrder(order);

        //Step 2 : 修改本次收货的发货记录
        OrderDeliveryDO delivery = deliveries.stream().filter(orderDelivery -> orderDelivery.getBatchNo().equals(batchNo)).findFirst().orElse(null);
        if (delivery == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DELIVERY_DOES_NOT_EXIST);
        }

        // 判断本次的发货是否已经被确认收货
        if (delivery.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_CONFIRM_RECEIPT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DELIVERY_HAS_BE_RECEIVED);
        }

        String receiptNo = RandomNumberUtil.randomUniqueNumber("SH", 7);
        delivery.setReceiptNo(receiptNo);
        delivery.setReceiptTime(LocalDateTime.now());
        delivery.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_CONFIRM_RECEIPT.getCode());
        delivery.setReceiveBill(StringUtils.hasLength(receiveBill) ? receiveBill : "");
        orderDeliveryRepository.saveAndFlush(delivery);

        //Step 3 ： 修改本次发货商品的数据：已收货=已发货 差异数量=已发货-已收货
        List<OrderDeliveryProductDO> deliveryProducts = orderDeliveryProductRepository.findByOrderIdAndBatchNo(order.getId(), batchNo);
        if (CollectionUtils.isEmpty(deliveryProducts) || deliveryProducts.stream().anyMatch(deliveryProduct -> !deliveryProduct.getDeliveryNo().equals(delivery.getDeliveryNo()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DELIVERY_PRODUCT_DOES_NOT_EXIST);
        }

        deliveryProducts.forEach(deliveryProduct -> {
            deliveryProduct.setReceived(deliveryProduct.getDelivered());
            deliveryProduct.setDifferCount(NumberUtil.max(deliveryProduct.getDelivered().subtract(deliveryProduct.getReceived()), BigDecimal.ZERO));
        });
        orderDeliveryProductRepository.saveAll(deliveryProducts);

        //Step 4 : 修改订单商品的数据：已收货 = 当前的已收货 + 发货商品的收货数 ；差异数量=已发货-已收货
        List<OrderProductDO> orderProducts = orderProductRepository.findByOrder(order);
        if (CollectionUtils.isEmpty(orderProducts)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }

        orderProducts.forEach(orderProduct -> deliveryProducts.stream().filter(deliveryProduct -> deliveryProduct.getProductId().equals(orderProduct.getProductId()) && deliveryProduct.getSkuId().equals(orderProduct.getSkuId())).findFirst().ifPresent(deliveryProduct -> {
            orderProduct.setReceived(orderProduct.getReceived().add(deliveryProduct.getReceived()));
            orderProduct.setDifferCount(NumberUtil.max(orderProduct.getDelivered().subtract(orderProduct.getReceived()), BigDecimal.ZERO));
        }));

        orderProductRepository.saveAll(orderProducts);

        //设置“已经收过一次货”标记
        order.setHasReceived(true);

        //Step 5: 判断流程跳转参数（在收货的时候，有可能已经发货全部完成了，所以要以 “收货数量 - 购买数量 < 0” 为条件来判断）
        // A. 如果收货已经全部完成，则本次收货必定是最后一次收货，流程任务为“收货”任务
        //         所以跳转参数为 -1， 完成收货发货流程
        // B. 如果收货未完成：
        //    b-1: 如果发货已经完成
        //       b-1-1: 如果仅有一次未收货，跳转参数为 1
        //       b-1-1: 否则，跳转参数为2
        //    b-2: 否则，跳转参数为0

        boolean receiveDone = false;
        long unconfirmCount = orderProducts.stream().filter(orderProduct -> orderProduct.getReceived().subtract(orderProduct.getQuantity()).compareTo(BigDecimal.ZERO) < 0).count();
        int taskJumpParameter = 0;
        if (unconfirmCount == 0) {
            receiveDone = true;
            taskJumpParameter = -1;
        } else {
            if (orderProducts.stream().allMatch(orderProduct -> orderProduct.getLeftCount().compareTo(BigDecimal.ZERO) == 0)) {
                if (deliveries.stream().filter(d -> d.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode())).count() == 1) {
                    taskJumpParameter = 1;
                } else {
                    taskJumpParameter = 2;
                }
            }
        }

        return new OrderReceiveBO(taskJumpParameter, receiptNo, receiveDone);
    }

    /**
     * 采购Srm订单收货后，将收货数据通过MQ发送给结算服务
     * @param order   订单
     * @param batchNo 发货批次
     */
    @Override
    public void notifySrmOrderReceive(OrderDO order, Integer batchNo) {
        //Step 1: 如果订单不是SRM订单，返回
        if (!order.getOrderKind().equals(OrderSourceKindEnum.SRM.getCode()) && !order.getOrderKind().equals(OrderSourceKindEnum.REQUISITION.getCode())) {
            return;
        }

        //Step 2: 根据发货批次查询已经收货的记录
        OrderDeliveryDO delivery = orderDeliveryRepository.findFirstByOrderAndBatchNo(order, batchNo);
        //如果还没收货，返回
        if (delivery == null || !delivery.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_CONFIRM_RECEIPT.getCode())) {
            return;
        }

        //Step 3: 从会员服务查询入库资料 - 入库分类 - 主营品类信息
        List<MemberCategoryDTO> memberCategories = memberFeignService.findMemberBusinessCategories(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId());

        //Step 4: 从商品服务，查询商品（物料）一级品类Id
        List<OrderProductCategoryDTO> productCategories = productFeignService.findProductCategories(delivery.getDeliveryProducts().stream().map(OrderDeliveryProductDO::getProductId).collect(Collectors.toList()));

        //Step 5: 构造拼接DTO，推送至消息队列
        OrderSettlementDTO settlementDTO = new OrderSettlementDTO();
        settlementDTO.setOrderId(order.getId());
        settlementDTO.setOrderNo(order.getOrderNo());
        settlementDTO.setCreateTime(DateTimeUtil.localDateTimeToTimestamp(order.getCreateTime()));
        settlementDTO.setDigest(order.getDigest());
        settlementDTO.setTotalAmount(order.getTotalAmount());
        settlementDTO.setBuyerMemberId(order.getBuyerMemberId());
        settlementDTO.setBuyerRoleId(order.getBuyerRoleId());
        settlementDTO.setBuyerMemberName(order.getBuyerMemberName());
        settlementDTO.setVendorMemberId(order.getVendorMemberId());
        settlementDTO.setVendorRoleId(order.getVendorRoleId());
        settlementDTO.setVendorMemberName(order.getVendorMemberName());
        settlementDTO.setBatchNo(delivery.getBatchNo());
        settlementDTO.setDeliveryNo(delivery.getDeliveryNo());
        settlementDTO.setDeliveryTime(DateTimeUtil.localDateTimeToTimestamp(delivery.getCreateTime()));
        settlementDTO.setReceiveNo(delivery.getReceiptNo());
        settlementDTO.setReceiveTime(DateTimeUtil.localDateTimeToTimestamp(delivery.getReceiptTime()));
        settlementDTO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
        settlementDTO.setOrderType(order.getOrderType());
        settlementDTO.setCurrencyType(order.getCurrencyType());
        // 找到默认品类的结算方式
        MemberCategoryDTO memberCategoryDTO = memberCategories.stream().filter(f -> Objects.nonNull(f.getIsDefault()) && EnableDisableStatusEnum.ENABLE.getCode().equals(f.getIsDefault())).findFirst().orElse(null);
        //已收货商品（物料）信息
        settlementDTO.setReceivedProducts(delivery.getDeliveryProducts().stream().map(deliveryProduct -> {

            // 根据商品id，找到对应的品类id
            Long categoryId = productCategories.stream().filter(p -> p.getProductId().equals(deliveryProduct.getProductId())).map(OrderProductCategoryDTO::getCategoryId).findFirst().orElse(0L);

            ReceivedProductSettlementDTO p = new ReceivedProductSettlementDTO();

            // 根据品类id找到对应的品类结算方式
            if (!CollectionUtils.isEmpty(memberCategories)) {
                MemberCategoryDTO memberCategory = memberCategories.stream().filter(category -> !CollectionUtils.isEmpty(category.getCategories()) && category.getCategories().stream().anyMatch(c -> c.getCategoryId().equals(categoryId))).findFirst().orElse(null);
                if (Objects.nonNull(memberCategory)) {
                    // 存在品类的结算方式
                    p.setPayType(memberCategory.getPayType());
                    p.setMonth(memberCategory.getMonth());
                    p.setMonthDay(memberCategory.getMonthDay());
                    p.setDays(memberCategory.getDays());
                } else if (Objects.nonNull(memberCategoryDTO)) {
                    // 存在默认的结算方式
                    p.setPayType(memberCategoryDTO.getPayType());
                    p.setMonth(memberCategoryDTO.getMonth());
                    p.setMonthDay(memberCategoryDTO.getMonthDay());
                    p.setDays(memberCategoryDTO.getDays());
                }
            }
            p.setProductNo(deliveryProduct.getProductNo());
            p.setName(deliveryProduct.getName());
            p.setSpec(deliveryProduct.getSpec());
            p.setCategory(deliveryProduct.getCategory());
            p.setBrand(deliveryProduct.getBrand());
            p.setUnit(deliveryProduct.getUnit());
            p.setTaxRate(deliveryProduct.getTaxRate());
            p.setRefPrice(deliveryProduct.getRefPrice());
            p.setQuantity(deliveryProduct.getReceived());
            return p;
        }).collect(Collectors.toList()));

        //合同
        OrderContractResp orderContractResp = contractFeignService.getOrderContractByOrderId(new CommonIdReq(order.getId()));
        settlementDTO.setContractId(orderContractResp == null ? 0L : orderContractResp.getContractId());

        settleAccountFeignService.notifyOrderSettlement(settlementDTO);
    }

    /**
     * （新增收货单）采购Srm订单收货后，将收货数据通过MQ发送给结算服务
     * @param orders     订单
     * @param deliveryNo 发货单号
     */
    @Override
    public void notifySrmOrderReceive(List<OrderDO> orders, String deliveryNo) {
        //根据发货单号查询已经收货的记录
        List<OrderDeliveryDO> deliveryList = orderDeliveryRepository.findByDeliveryNoAndBuyerInnerStatus(deliveryNo, BuyerInnerStatusEnum.BUYER_CONFIRM_RECEIPT.getCode());

        for (OrderDO order : orders) {
            if (!order.getOrderKind().equals(OrderSourceKindEnum.SRM.getCode()) && !order.getOrderKind().equals(OrderSourceKindEnum.REQUISITION.getCode())) {
                return;
            }

            //当前订单发货记录
            OrderDeliveryDO delivery = deliveryList.stream().filter(deliveryVO -> deliveryVO.getOrder().equals(order)).findFirst().orElse(null);

            //如果还没收货，返回
            if (delivery == null || !delivery.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_CONFIRM_RECEIPT.getCode())) {
                return;
            }

            //Step 3: 从会员服务查询入库资料 - 入库分类 - 主营品类信息
            List<MemberCategoryDTO> memberCategories = memberFeignService.findMemberBusinessCategories(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId());

            //Step 4: 从商品服务，查询商品（物料）一级品类Id
            List<OrderProductCategoryDTO> productCategories = productFeignService.findProductCategories(delivery.getDeliveryProducts().stream().filter(p->order.getId().equals(p.getOrderId())).map(OrderDeliveryProductDO::getProductId).collect(Collectors.toList()));

            //Step 5: 构造拼接DTO，推送至消息队列
            OrderSettlementDTO settlementDTO = new OrderSettlementDTO();
            settlementDTO.setOrderId(order.getId());
            settlementDTO.setOrderNo(order.getOrderNo());
            settlementDTO.setCreateTime(DateTimeUtil.localDateTimeToTimestamp(order.getCreateTime()));
            settlementDTO.setDigest(order.getDigest());
            settlementDTO.setTotalAmount(order.getTotalAmount());
            settlementDTO.setBuyerMemberId(order.getBuyerMemberId());
            settlementDTO.setBuyerRoleId(order.getBuyerRoleId());
            settlementDTO.setBuyerMemberName(order.getBuyerMemberName());
            settlementDTO.setVendorMemberId(order.getVendorMemberId());
            settlementDTO.setVendorRoleId(order.getVendorRoleId());
            settlementDTO.setVendorMemberName(order.getVendorMemberName());
            settlementDTO.setBatchNo(delivery.getBatchNo());
            settlementDTO.setDeliveryNo(delivery.getDeliveryNo());
            settlementDTO.setDeliveryTime(DateTimeUtil.localDateTimeToTimestamp(delivery.getCreateTime()));
            settlementDTO.setReceiveNo(delivery.getReceiptNo());
            settlementDTO.setReceiveTime(DateTimeUtil.localDateTimeToTimestamp(delivery.getReceiptTime()));
            settlementDTO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            settlementDTO.setOrderType(order.getOrderType());
            settlementDTO.setCurrencyType(order.getCurrencyType());
            // 找到默认品类的结算方式
            MemberCategoryDTO memberCategoryDTO = memberCategories.stream().filter(f -> Objects.nonNull(f.getIsDefault()) && EnableDisableStatusEnum.ENABLE.getCode().equals(f.getIsDefault())).findFirst().orElse(null);
            //已收货商品（物料）信息(当前订单的商品/物料)
            settlementDTO.setReceivedProducts(delivery.getDeliveryProducts().stream().filter(p->order.getId().equals(p.getOrderId())).map(deliveryProduct -> {
                Long categoryId = productCategories.stream().filter(p -> p.getProductId().equals(deliveryProduct.getProductId())).map(OrderProductCategoryDTO::getCategoryId).findFirst().orElse(0L);

                ReceivedProductSettlementDTO p = new ReceivedProductSettlementDTO();

                // 根据品类id找到对应的品类结算方式
                MemberCategoryDTO memberCategory;
                if(!CollectionUtils.isEmpty(memberCategories)){
                    memberCategory = memberCategories.stream().filter(category ->!CollectionUtils.isEmpty(category.getCategories()) && category.getCategories().stream().anyMatch(c -> c.getCategoryId().equals(categoryId))).findFirst().orElse(null);

                }else {
                    memberCategory = null;
                }

                if (Objects.nonNull(memberCategory)) {
                    // 设置了品类的结算方式，使用设置的
                    p.setPayType(memberCategory.getPayType());
                    p.setMonth(memberCategory.getMonth());
                    p.setMonthDay(memberCategory.getMonthDay());
                    p.setDays(memberCategory.getDays());
                }else {
                    // 没有设置品类的结算方式
                    if(Objects.nonNull(memberCategoryDTO)){
                        // 有默认的结算方式，使用默认的
                        p.setPayType(memberCategoryDTO.getPayType());
                        p.setMonth(memberCategoryDTO.getMonth());
                        p.setMonthDay(memberCategoryDTO.getMonthDay());
                        p.setDays(memberCategoryDTO.getDays());
                    }else {
                        // 没有默认的结算方式，设为空
                        p.setPayType(null);
                        p.setMonth(null);
                        p.setMonthDay(null);
                        p.setDays(null);
                    }
                }

                p.setProductNo(deliveryProduct.getProductNo());
                p.setName(deliveryProduct.getName());
                p.setSpec(deliveryProduct.getSpec());
                p.setCategory(deliveryProduct.getCategory());
                p.setBrand(deliveryProduct.getBrand());
                p.setUnit(deliveryProduct.getUnit());
                p.setTaxRate(deliveryProduct.getTaxRate());
                p.setRefPrice(deliveryProduct.getRefPrice());
                p.setQuantity(deliveryProduct.getReceived());
                return p;
            }).collect(Collectors.toList()));

            //合同
            OrderContractResp orderContractResp = contractFeignService.getOrderContractByOrderId(new CommonIdReq(order.getId()));
            settlementDTO.setContractId(orderContractResp == null ? 0L : orderContractResp.getContractId());
            settleAccountFeignService.notifyOrderSettlement(settlementDTO);
        }

    }

    /**
     * 新增送货单 - 更新销售订单发货数量和工作流内外部状态
     * @param loginUser     登录用户信息
     * @param deliveryOrder 送货单信息
     * @param productVO     商品发货数量
     */
    @Override
    @Transactional
    public void checkCreateVendorDelivery(UserLoginCacheDTO loginUser, DeliveryOrderDO deliveryOrder, List<ProductReceiveOrDeliveryReq> productVO) {
        if (CollUtil.isEmpty(productVO)) {
            return;
        }

        //发货数量map
        Map<Long, BigDecimal> productMap = productVO.stream().collect(Collectors.toMap(ProductReceiveOrDeliveryReq::getOrderProductId, ProductReceiveOrDeliveryReq::getQuantity));

        List<Long> productIds = productVO.stream().map(ProductReceiveOrDeliveryReq::getOrderProductId).collect(Collectors.toList());
        List<OrderProductDO> productList = orderProductRepository.findByIdIn(productIds);
        Map<Long, OrderProductDO> orderProductMap = productList.stream().collect(Collectors.toMap(OrderProductDO::getId, OrderProductDO -> OrderProductDO, (v1, v2) -> v2));
        productVO.forEach(p-> p.setOrderId(orderProductMap.get(p.getOrderProductId()).getOrder().getId()));
        //所有商品关联的订单
        Set<OrderDO> orders = new HashSet<>();

        //修改订单商品未发货数量
        for (OrderProductDO product : productList) {
            BigDecimal quantity = productMap.getOrDefault(product.getId(), BigDecimal.ZERO);
            if (quantity.compareTo(product.getLeftCount()) > 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_QUANTITY_DELIVER_GT_NOT_DELIVER);
            }
            product.setDelivered(quantity.add(product.getDelivered()));
            product.setLeftCount(NumberUtil.max(product.getQuantity().subtract(product.getDelivered()), BigDecimal.ZERO));
            product.setDifferCount(product.getDelivered().subtract(product.getReceived()));
            orders.add(product.getOrder());
        }

        for (OrderDO order : orders) {
            //发货批次+1
            int batchNo = order.getDeliveries().stream().mapToInt(OrderDeliveryDO::getBatchNo).max().orElse(0) + 1;

            //生成发货记录，记录发货商品
            OrderDeliveryDO delivery = new OrderDeliveryDO();
            delivery.setOrder(order);
            delivery.setCreateTime(LocalDateTime.now());
            delivery.setBatchNo(batchNo);
            delivery.setDeliveryNo(deliveryOrder.getDeliveryNo());
            delivery.setAddress(delivery.getAddress());
            delivery.setAddressId(delivery.getAddressId());
            delivery.setLogisticsNo(StrUtil.isEmpty(delivery.getLogisticsNo()) ? "" : delivery.getLogisticsNo());
            delivery.setCompany(StrUtil.isEmpty(delivery.getCompany()) ? "" : delivery.getCompany());
            delivery.setCompanyCode(StrUtil.isEmpty(delivery.getCompanyCode()) ? "" : delivery.getCompanyCode());
            delivery.setReceiptNo("");
            delivery.setReceiptTime(null);
            delivery.setReceiveBill("");
            delivery.setVendorInnerStatus(VendorInnerStatusEnum.VENDOR_DELIVERY_CONFIRMED.getCode());
            delivery.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode());

            List<OrderDeliveryProductDO> deliveryProducts = productVO.stream().filter(p->order.getId().equals(p.getOrderId()))
                    .map(product -> {
                        OrderDeliveryProductDO deliveryProduct = new OrderDeliveryProductDO();
                        deliveryProduct.setOrderId(order.getId());
                        deliveryProduct.setBatchNo(batchNo);
                        deliveryProduct.setDeliveryNo(deliveryOrder.getDeliveryNo());
                        productList.stream().filter(p -> p.getId().equals(product.getOrderProductId())).findFirst().ifPresent(orderProduct -> {
                            deliveryProduct.setOrderProductId(orderProduct.getId());
                            deliveryProduct.setProductId(orderProduct.getProductId());
                            deliveryProduct.setSkuId(orderProduct.getSkuId());
                            deliveryProduct.setProductNo(orderProduct.getProductNo());
                            deliveryProduct.setName(orderProduct.getName());
                            deliveryProduct.setCategory(orderProduct.getCategory());
                            deliveryProduct.setBrand(orderProduct.getBrand());
                            deliveryProduct.setUnit(orderProduct.getUnit());
                            deliveryProduct.setSpec(orderProduct.getSpec());
                            deliveryProduct.setPrice(orderProduct.getPrice());
                            deliveryProduct.setRefPrice(orderProduct.getRefPrice());
                            deliveryProduct.setTaxRate(orderProduct.getTaxRate());
                            deliveryProduct.setQuotedProductId(orderProduct.getMaterial() == null ? 0L : orderProduct.getMaterial().getProductId());
                            deliveryProduct.setQuotedSkuId(orderProduct.getMaterial() == null ? 0L : orderProduct.getMaterial().getSkuId());
                            deliveryProduct.setQuotedProductNo(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getProductNo());
                            deliveryProduct.setQuotedName(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getName());
                            deliveryProduct.setQuotedCategory(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getCategory());
                            deliveryProduct.setQuotedBrand(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getBrand());
                            deliveryProduct.setQuotedSpec(orderProduct.getMaterial() == null ? "" : orderProduct.getMaterial().getSpec());
                            deliveryProduct.setQuantity(orderProduct.getQuantity());
                            deliveryProduct.setDelivered(product.getQuantity());
                            deliveryProduct.setReceived(BigDecimal.ZERO);
                            deliveryProduct.setDifferCount(product.getQuantity());
                            deliveryProduct.setNetWeight(orderProduct.getNetWeight());
                            deliveryProduct.setGoodsWeight(orderProduct.getGoodsWeight());
                        });
                        return deliveryProduct;
                    }).collect(Collectors.toList());

            //判断是否有自提商品，有则生成自提码
            Set<Long> orderProductIds = deliveryProducts.stream().map(OrderDeliveryProductDO::getOrderProductId).collect(Collectors.toSet());
            boolean havePickUp = productList.stream().filter(p -> orderProductIds.contains(p.getId())).anyMatch(p -> OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode().equals(p.getDeliverType()));
            delivery.setSelfCode(havePickUp ? RandomNumberUtil.randomUniqueNumber() : "");

            //是否有自提商品
            order.setPickUp(havePickUp);

            //保存
            orderDeliveryProductRepository.saveAll(deliveryProducts);
            delivery.setDeliveryProducts(new HashSet<>(deliveryProducts));
            orderDeliveryRepository.saveAndFlush(delivery);

            //设置“已经发过一次货”标记
            order.setHasDelivered(true);
            //设置订单关联
            order.getDeliveries().add(delivery);

            //判断流程跳转参数（跳转参数在流程图中定义）
            // A. 如果发货未完成
            //          流程不跳转，即参数为0
            // B. 如果发货完成（此时必有至少一次未收货）
            //    b-1: 有且仅有一次未收货， 跳转参数为 1，流程执行到“收货”任务，当最后一次收货完成后设置跳转参数为-1，收发货流程完成
            //    b-2: 大于一次未收货，跳转参数为 2，执行另一个回环分支，改变供应商状态
            boolean deliveryDone = false;
            int taskJumpParameter = 0;
            if (order.getProducts().stream().allMatch(orderProduct -> orderProduct.getLeftCount().compareTo(BigDecimal.ZERO) == 0)) {
                deliveryDone = true;
                if (order.getDeliveries().stream().filter(d -> !d.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_CONFIRM_RECEIPT.getCode())).count() == 1) {
                    taskJumpParameter = 1;
                } else {
                    taskJumpParameter = 2;
                }
            }

            //执行工作流任务（修改订单状态、记录内外流转记录）
            baseOrderTaskService.execOrderProcess(order, taskJumpParameter);

            //发送订单发货确认通知消息给采购会员
            if (havePickUp) {
                baseOrderService.messageBuyerDeliverSelfOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), MessageNoticeEnum.SELL_DELIVER_SELF_ORDER.getCode(), order.getOrderNo(), order.getDigest(), delivery.getAddress(), delivery.getSelfCode());
            } else {
                baseOrderService.messageBuyerDeliverOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), MessageNoticeEnum.SELL_DELIVER_ORDER.getCode(), order.getOrderNo(), order.getDigest(), deliveryOrder.getLogisticsCompany(), deliveryOrder.getLogisticsNo());
            }

            // 检查自动收货配置，将发货信息推送至消息队列，自动收货
            baseOrderScheduleService.scheduleOrderDelivery(order, batchNo);

            orderRepository.saveAndFlush(order);

            //向消息服务发送采购商实时消息
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

            //订单内、外流转记录
            baseOrderHistoryService.saveVendorInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.CONFIRM_DELIVERY, order.getVendorInnerStatus(), OrderStringEnum.DELIVERY_NUMBER.getName().concat(delivery.getDeliveryNo()));
            baseOrderHistoryService.saveVendorOrderOuterHistory(order.getId(), order.getOrderNo(), order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.CONFIRM_DELIVERY, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), OrderStringEnum.DELIVERY_NUMBER.getName().concat(delivery.getDeliveryNo()));
        }

        //保存
        orderProductRepository.saveAll(productList);
    }

    /**
     * 新增收货单 - 更新采购订单发货数量和工作流内外部状态
     * @param loginUser    登录用户信息
     * @param receiveOrder 收货单
     * @param productVO    商品收货数量
     */
    @Override
    @Transactional
    public void checkCreateBuyerReceive(UserLoginCacheDTO loginUser, ReceiveOrderDO receiveOrder, List<ProductReceiveOrDeliveryReq> productVO) {
        if (CollUtil.isEmpty(productVO)) {
            return;
        }

        //收货数量map
        Map<Long, BigDecimal> productMap = productVO.stream().collect(Collectors.toMap(ProductReceiveOrDeliveryReq::getOrderProductId, ProductReceiveOrDeliveryReq::getQuantity));

        List<Long> productIds = productVO.stream().map(ProductReceiveOrDeliveryReq::getOrderProductId).collect(Collectors.toList());
        List<OrderProductDO> productList = orderProductRepository.findByIdIn(productIds);

        //所有商品关联的订单
        Set<OrderDO> orders = new HashSet<>();

        productList.forEach(product -> {
            BigDecimal quantity = productMap.getOrDefault(product.getId(), BigDecimal.ZERO);
            product.setReceived(quantity.add(product.getReceived()));
            product.setDifferCount(NumberUtil.max(product.getDelivered().subtract(product.getReceived()), BigDecimal.ZERO));
            orders.add(product.getOrder());
        });

        //关联的发货记录
        List<OrderDeliveryDO> deliveryList = orderDeliveryRepository.findByDeliveryNo(receiveOrder.getDeliveryNo());
        deliveryList.forEach(delivery -> {
            delivery.setReceiptNo(receiveOrder.getReceiveNo());
            delivery.setReceiptTime(LocalDateTime.now());
            delivery.setGoodsReceipt(receiveOrder.getGoodsReceipt());
            delivery.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_CONFIRM_RECEIPT.getCode());
            delivery.setReceiveBill("");

            for (OrderDeliveryProductDO deliveryProduct : delivery.getDeliveryProducts()) {
                deliveryProduct.setReceived(deliveryProduct.getDelivered());
                deliveryProduct.setDifferCount(NumberUtil.max(deliveryProduct.getDelivered().subtract(deliveryProduct.getReceived()), BigDecimal.ZERO));
            }
        });


        for (OrderDO order : orders) {
            //设置“已经收过一次货”标记
            order.setHasReceived(true);

            //判断流程跳转参数（在收货的时候，有可能已经发货全部完成了，所以要以 “收货数量 - 购买数量 < 0” 为条件来判断）
            // A. 如果收货已经全部完成，则本次收货必定是最后一次收货，流程任务为“收货”任务
            //         所以跳转参数为 -1， 完成收货发货流程
            // B. 如果收货未完成：
            //    b-1: 如果发货已经完成
            //       b-1-1: 如果仅有一次未收货，跳转参数为 1
            //       b-1-1: 否则，跳转参数为2
            //    b-2: 否则，跳转参数为0
            boolean receiveDone = false;
            long unconfirmCount = order.getProducts().stream().filter(orderProduct -> orderProduct.getReceived().subtract(orderProduct.getQuantity()).compareTo(BigDecimal.ZERO) < 0).count();
            int taskJumpParameter = 0;
            if (unconfirmCount == 0) {
                receiveDone = true;
                taskJumpParameter = -1;
            } else {
                if (order.getProducts().stream().allMatch(orderProduct -> orderProduct.getLeftCount().compareTo(BigDecimal.ZERO) == 0)) {
                    if (order.getDeliveries().stream().filter(d -> d.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode())).count() == 1) {
                        taskJumpParameter = 1;
                    } else {
                        taskJumpParameter = 2;
                    }
                }
            }

            baseOrderTaskService.execOrderProcess(order, taskJumpParameter);

            // 记录采购、供应的上一次内部状态，用于发送报表统计数据
            int lastBuyerInnerStatus = order.getBuyerInnerStatus();
            int lastVendorInnerStatus = order.getVendorInnerStatus();

            //收货全部完成之后有可能是支付环节，判断是否跳过支付环节
            if (receiveDone) {
                baseOrderPaymentService.jumpOverOrderPaySerialTasks(order);
            }
            // 添加完成时间
            if (order.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode())) {
                order.setFinishTime(LocalDateTime.now());
            }
            orderRepository.saveAndFlush(order);

            //向消息服务发送采购商实时消息
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());
            baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());

            //订单完成后的操作
            baseOrderService.operateAfterOrderAccomplished(order);

            //订单内、外流转记录
            baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.CONFIRM_RECEIPT, order.getBuyerInnerStatus(), OrderStringEnum.RECEIPT_NUMBER.getName().concat(receiveOrder.getReceiveNo()));
            baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.CONFIRM_RECEIPT, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), OrderStringEnum.RECEIPT_NUMBER.getName().concat(receiveOrder.getReceiveNo()));
        }

        // 如果是Srm订单、采购请购单收货，发送收发货记录给结算服务
        baseOrderDeliveryService.notifySrmOrderReceive(new ArrayList<>(orders), receiveOrder.getDeliveryNo());

        //保存
        orderProductRepository.saveAll(productList);
        orderDeliveryRepository.saveAll(deliveryList);
    }

    /**
     * 查询订单是否有未收货明细
     * @param order 订单
     * @return 收发货统计
     */
    @Override
    public boolean checkOrderDelivery(OrderDO order) {
        List<OrderDeliveryDO> deliveries = orderDeliveryRepository.findByOrder(order);
        return !CollectionUtils.isEmpty(deliveries) && deliveries.stream().anyMatch(d -> !StringUtils.hasLength(d.getReceiptNo()));
    }

    /**
     * 收货后自动创建采购入库单
     * @param memberId 会员id
     * @param roleId 角色id
     * @param memberRoleName 会员名称
     * @param warehousingOrderProductDetailResps  自动入库订单物料信息VO
     * @param deliveryNo  订单号/发货单号
     * @param receiveNo  收货单号
     */
    @Override
    public void createInboundOrder(Long memberId, Long roleId, String memberRoleName, List<WarehousingOrderProductDetailResp> warehousingOrderProductDetailResps, String deliveryNo, String receiveNo) {
        //通知商品服务，创建采购入库单
        productFeignService.createInboundOrder(memberId, roleId, memberRoleName, warehousingOrderProductDetailResps, deliveryNo, receiveNo);
    }

    /**
     * 是否自动创建销售出库单
     *
     * @param order      订单
     * @param deliveryVO 订单发货确认接口参数
     * @param deliveryNo 发货单号
     */
    @Override
    public void autoCreateOutOfStockOrder(OrderDO order, VendorDeliveryReq deliveryVO, String deliveryNo) {
        List<OutOfStockOrderProductDetailResp> outOfStockOrderProductDetailRespList = deliveryVO.getProducts().stream().map(VendorToDeliveryProductReq::getOutOfStockOrderProductDetailVO).filter(Objects::nonNull).collect(Collectors.toList());
        //开启仓位同步库存模式
        if (!CollectionUtils.isEmpty(outOfStockOrderProductDetailRespList)) {
            //集合里面的出库仓库id都是一样的，所以获取第一个就好
            Long warehouseId = outOfStockOrderProductDetailRespList.get(0).getOutOfStockId();
            //获取开启仓位同步库存的sku
            List<Long> skuIds = outOfStockOrderProductDetailRespList.stream().map(OutOfStockOrderProductDetailResp::getSkuId).filter(Objects::nonNull).map(Long::valueOf).collect(Collectors.toList());
            CommonIdListReq request = new CommonIdListReq();
            request.setIdList(skuIds);
            WrapperResp<List<InventoryPatternResp>> wrapperResp = freightSpaceFeign.getInventoryPattern(request);
            if (wrapperResp.getCode() != WrapperUtil.success().getCode()) {
                throw new BusinessException(wrapperResp.getCode(), wrapperResp.getMessage());
            }
            skuIds = wrapperResp.getData().stream().filter(Objects::nonNull).filter(InventoryPatternResp::getIsInventory).map(InventoryPatternResp::getId).distinct().collect(Collectors.toList());
            List<Long> finalSkuIds = skuIds;
            //自动创建销售出库单
            outOfStockOrderProductDetailRespList = outOfStockOrderProductDetailRespList.stream().filter(s -> finalSkuIds.contains(Long.valueOf(s.getSkuId()))).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(outOfStockOrderProductDetailRespList)) {
                baseOrderService.createOutOfStockOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorMemberName(), outOfStockOrderProductDetailRespList, order.getOrderNo(), deliveryNo);
            }
            //手动创建销售出库单时,需释放占用库存
            List<OrderProductDO> productDOS = order.getProducts().stream().filter(s -> finalSkuIds.contains(s.getSkuId())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(productDOS)) {
                //未开启仓位同步库存模式 需指定释放占用库存
                productFeignService.returnCustomOccupiedInventory(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getId(), order.getShopId(), order.getShopType(), warehouseId, productDOS.stream().map(product -> new InventoryProductBO(product.getProductId(), product.getSkuId(), product.getName(), product.getLastCount())).collect(Collectors.toList()));
            }
        }else {
            //前端传递的参数中：发货数量可以为0，在前面的判断中，已经把接口参数发货数量为0的数据给移除了，所以这里也要根据前端参数过滤出订单商品
            List<InventoryProductBO> products = order.getProducts().stream().filter(p -> deliveryVO.getProducts().stream().anyMatch(dp -> dp.getOrderProductId().equals(p.getId()))).map(product -> new InventoryProductBO(product.getProductId(), product.getSkuId(), product.getName(), product.getLastCount())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(products)) {
                return;
            }

            //未开启仓位同步库存模式 需释放占用库存
            productFeignService.returnOccupiedInventory(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getId(), order.getShopId(), order.getShopType(), products);
        }
    }

    /**
     * 送货单->自动创建出库单
     *
     * @param deliveryOrderDO                    送货单实体
     * @param outOfStockOrderProductDetailRespList 自动出库订单物料信息VO集合
     */
    @Override
    public void createOutOfStockOrder(DeliveryOrderDO deliveryOrderDO, List<OutOfStockOrderProductDetailResp> outOfStockOrderProductDetailRespList) {

        //订单号不能为空
        if (outOfStockOrderProductDetailRespList.stream().anyMatch(o-> org.apache.commons.lang3.StringUtils.isBlank(o.getOrderNo()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_NUMBER_CANNOT_BE_EMPTY);
        }
        //skuId不能为空
        if (outOfStockOrderProductDetailRespList.stream().anyMatch(o -> org.apache.commons.lang3.StringUtils.isBlank(o.getSkuId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_SKU_ID_CANNOT_BE_EMPTY);
        }
        //从商品服务中获取物料id
        Set<Long> skuIds = outOfStockOrderProductDetailRespList.stream().map(o -> Long.valueOf(o.getSkuId())).collect(Collectors.toSet());
        WrapperResp<List<CommoditySkuStockResp>> wrapperResp = commodityFeign.getCommodityByCommoditySkuIdList(new ArrayList<>(skuIds));
        if (wrapperResp.getCode() != WrapperUtil.success().getCode()) {
            throw new BusinessException(wrapperResp.getCode(), wrapperResp.getMessage());
        }
        if (wrapperResp.getData().size() > 0) {
            //key:skuId value:goodsId
            Map<Long, CommoditySkuStockResp> goodsIdMap = wrapperResp.getData().stream().collect(Collectors.toMap(CommoditySkuStockResp::getId, CommodityGoodsResponse->CommodityGoodsResponse, (v1, v2) -> v2));
            outOfStockOrderProductDetailRespList = outOfStockOrderProductDetailRespList.stream().peek(o -> {
                CommoditySkuStockResp commoditySkuStockResp = goodsIdMap.get(Long.valueOf(o.getSkuId()));
                o.setGoodsId(commoditySkuStockResp.getMaterielId());
                o.setProductId(commoditySkuStockResp.getCommodityId());
            }).collect(Collectors.toList());
            //按订单编号分组(过滤物料id为null的)
            Map<String, List<OutOfStockOrderProductDetailResp>> map = outOfStockOrderProductDetailRespList.stream().filter(o -> NumberUtil.notNullOrZero(o.getGoodsId())).collect(Collectors.groupingBy(OutOfStockOrderProductDetailResp::getOrderNo));
            map.forEach((key, value) -> productFeignService.createOutOfStockOrder(deliveryOrderDO.getVendorMemberId(), deliveryOrderDO.getVendorRoleId(), deliveryOrderDO.getVendorMemberName(), value, key, deliveryOrderDO.getDeliveryNo()));
            //释放占用库存
            List<String> orderNoList = outOfStockOrderProductDetailRespList.stream().map(OutOfStockOrderProductDetailResp::getOrderNo).collect(Collectors.toList());
            List<OrderDO> orderDOList = orderRepository.findByOrderNoIn(orderNoList);
            //(不过滤物料id为null的)
            map = outOfStockOrderProductDetailRespList.stream().collect(Collectors.groupingBy(OutOfStockOrderProductDetailResp::getOrderNo));
            Map<String, List<OutOfStockOrderProductDetailResp>> finalMap = map;
            orderDOList.forEach(order -> {
                List<OutOfStockOrderProductDetailResp> outOfStockOrderProductDetailResps = finalMap.get(order.getOrderNo());
                if (!CollectionUtils.isEmpty(outOfStockOrderProductDetailResps)){
                    productFeignService.returnOccupiedInventory(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getId(), order.getShopId(), order.getShopType(), outOfStockOrderProductDetailResps.stream().map(product -> new InventoryProductBO(product.getProductId(), Long.valueOf(product.getSkuId()), product.getName(), new BigDecimal(product.getReceived()))).collect(Collectors.toList()));
                }
            });
        }
    }
}
