package com.ssy.lingxi.order.serviceImpl.mobile;

import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.PlatformRuleTypeEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTradeProcessTypeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.component.base.enums.pay.BalanceTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.OrderOuterStatusEnum;
import com.ssy.lingxi.order.model.dto.MemberLogoDTO;
import com.ssy.lingxi.order.model.req.basic.OrderIdReq;
import com.ssy.lingxi.order.model.req.common.OrderProductHistoryDataReq;
import com.ssy.lingxi.order.model.req.mobile.MobileOrderAfterSaleReq;
import com.ssy.lingxi.order.model.resp.basic.OrderRuleResp;
import com.ssy.lingxi.order.model.resp.common.MobileOrderProductHistoryQueryResp;
import com.ssy.lingxi.order.model.resp.common.OrderAfterSalePaymentDetailResp;
import com.ssy.lingxi.order.model.resp.mobile.MobileAfterSalePageQueryResp;
import com.ssy.lingxi.order.model.resp.mobile.MobileAfterSaleProductDetailResp;
import com.ssy.lingxi.order.model.resp.mobile.QMobileAfterSalePageQueryResp;
import com.ssy.lingxi.order.model.resp.mobile.QMobileAfterSaleProductDetailResp;
import com.ssy.lingxi.order.service.feign.IMemberFeignService;
import com.ssy.lingxi.order.service.mobile.IMobileOrderCommonService;
import com.ssy.lingxi.order.service.web.IOrderCommonService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * App - 订单其他相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-09-26
 */
@Service
public class MobileOrderCommonServiceImpl implements IMobileOrderCommonService {
    @Resource
    private IOrderCommonService orderCommonService;

    @Resource
    private IMemberFeignService memberFeignService;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    /**
     * “商品详情” - 分页查询商品交易记录
     *
     * @param historyVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MobileOrderProductHistoryQueryResp> pageOrderProductHistories(OrderProductHistoryDataReq historyVO) {
        QOrderProductHistoryDO qOrderProductHistory = QOrderProductHistoryDO.orderProductHistoryDO;
        JPAQuery<MobileOrderProductHistoryQueryResp> query = jpaQueryFactory.select(Projections.constructor(MobileOrderProductHistoryQueryResp.class, qOrderProductHistory.createTime, qOrderProductHistory.buyerMemberId, qOrderProductHistory.buyerMemberName, qOrderProductHistory.quantity, qOrderProductHistory.unit))
                .from(qOrderProductHistory)
                .where(qOrderProductHistory.shopId.eq(historyVO.getShopId()).and(qOrderProductHistory.productId.eq(historyVO.getProductId())))
                .orderBy(qOrderProductHistory.id.desc());

        long totalCount = query.fetchCount();

        //从会员服务查询采购商Logo
        List<MobileOrderProductHistoryQueryResp> result = query.limit(historyVO.getPageSize()).offset(historyVO.getCurrentOffset()).fetch();
        if(!CollectionUtils.isEmpty(result)) {
            List<Long> buyerMemberIds = result.stream().map(MobileOrderProductHistoryQueryResp::getBuyerMemberId).distinct().collect(Collectors.toList());
            List<MemberLogoDTO> logoResult = memberFeignService.findMemberLogos(buyerMemberIds);

            result = result.stream().peek(queryVO -> logoResult.stream().filter(r -> r.getMemberId().equals(queryVO.getBuyerMemberId())).findFirst().ifPresent(r -> queryVO.setLogo(r.getLogo()))).collect(Collectors.toList());
        }

        return new PageDataResp<>(totalCount, result);
    }

    /**
     * “申请售后” - 查询订单商品信息
     *
     * @param loginUser 登录用户
     * @param afterSaleVO 接口参数
     * @return 查询结果
     */
    @Override
    public MobileAfterSalePageQueryResp findAfterSaleOrder(UserLoginCacheDTO loginUser, MobileOrderAfterSaleReq afterSaleVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        OrderDO order = jpaQueryFactory.select(qOrder).from(qOrder).where(qOrder.id.eq(afterSaleVO.getOrderId())).fetchFirst();
        if(order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        OrderTypeEnum orderType = OrderTypeEnum.parse(order.getOrderType());
        if(orderType == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_TYPE_DOES_NOT_EXIST);
        }

        // （App额外规则）订单类型为积分兑换或渠道积分兑换的隐藏退货与维修二类售后
        if((orderType.equals(OrderTypeEnum.CREDITS_EXCHANGE)) && (afterSaleVO.getAfterSalesType().equals(3) || afterSaleVO.getAfterSalesType().equals(4))) {
            return null;
        }

        //Step 2: 规则
        // 2-1. 查询采购订单中选择的供应会员的订单数据，根据选择的售后订单类型进行筛选，只显示已收到第一批货之后的订单且当前订单外部状态不是待确认支付结果
        // 2-2. 如果订单类型为现货采购、询价采购、集采、积分兑换、渠道直采、渠道现货、渠道积分兑换，
        //      查询交易流程规则配置中流程类型为售后换货流程的工作流
        // 2-3. 如果订单类型为采购询价合同、采购竞价合同、采购招标合同只允许选择合同编号相同的订单，
        //      查询采购流程规则配置的流程类型为售后换货流程的适用当前合同的工作流

        //Step 3: 在IOrderCommonService中有个类似的接口，如这里修改了则要做相应的修改
        switch (orderType) {
            case SPOT_PURCHASING:
            case INQUIRY_TO_PURCHASE:
            case COLLECTIVE_PURCHASE:
//            case CHANNEL_STRAIGHT_MINING:
//            case CHANNEL_SPOT:
            case CREDITS_EXCHANGE:
            case FRAME_CONTRACT_ORDER:
            case MANUAL_MATERIAL_ORDER_PLACEMENT:
            case MATERIAL_SAMPLE_ORDER:
//            case CHANNEL_POINT_EXCHANGE:
                return pageAfterSaleOrdersByTradeProcess(afterSaleVO.getOrderId(), afterSaleVO.getAfterSalesType(), afterSaleVO.getShopId());
            case QUERY_PRICE_CONTRACT:
            case PRICE_COMPETITION_CONTRACT_PURCHASE:
            case PURCHASE_CONTRACT_BIDDING:
            case REQUISITION_TO_PURCHASE:
                return pageAfterSaleOrdersByPurchaseProcess(afterSaleVO.getOrderId(), afterSaleVO.getAfterSalesType());
            default:
                return null;
        }
    }

    /**
     * 售后能力 - 查询已经确认支付结果的支付记录列表
     *
     * @param loginUser 登录用户
     * @param idVO    接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderAfterSalePaymentDetailResp> findAfterSaleOrderPayments(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        return orderCommonService.findAfterSaleOrderPayments(idVO);
    }

    /**
     * “店铺会员” - 查询平台账户余额类型是否启用
     *
     * @param loginUser 登录用户
     * @return 查询结果
     */
    @Override
    public OrderRuleResp findPlatformBalanceTypeStatus(UserLoginCacheDTO loginUser) {
        QBaseOrderRuleDO qBaseOrderRule = QBaseOrderRuleDO.baseOrderRuleDO;
        BaseOrderRuleDO baseOrderRule = jpaQueryFactory.select(qBaseOrderRule).from(qBaseOrderRule)
                .where(qBaseOrderRule.ruleType.eq(PlatformRuleTypeEnum.BALANCE_TYPE.getCode()).and(qBaseOrderRule.methodCode.eq(BalanceTypeEnum.PLATFORM.getCode())))
                .fetchFirst();
        return baseOrderRule == null ? new OrderRuleResp(PlatformRuleTypeEnum.BALANCE_TYPE.getCode(), BalanceTypeEnum.PLATFORM.getCode(), EnableDisableStatusEnum.DISABLE.getCode()) : new OrderRuleResp(baseOrderRule.getRuleType(), baseOrderRule.getMethodCode(), baseOrderRule.getStatus());
    }

    /**
     * 根据交易流程规则配置查询售后订单
     * @param orderId        订单Id
     * @param afterSalesType 售后类型，前端接口参数中定义的枚举，要和交易流程类型枚举一致
     * @param shopId         App商城Id
     * @return               查询结果
     */
    private MobileAfterSalePageQueryResp pageAfterSaleOrdersByTradeProcess(Long orderId, Integer afterSalesType, Long shopId) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderMaterialDO qOrderMaterial = QOrderMaterialDO.orderMaterialDO;
//        QOrderContractDO qOrderContract = QOrderContractDO.orderContractDO;
        QOrderTradeProcessDO qOrderTradeProcess = QOrderTradeProcessDO.orderTradeProcessDO;
        QOrderTradeProcessProductDO qOrderTradeProcessProduct = QOrderTradeProcessProductDO.orderTradeProcessProductDO;

        //Step 1: 定义关联关系，OrderDO表必须要有distinct()
        // ShopId要以App商城Id为主，即售后从哪个商城发起，就查询哪个商城的交易流程规则配置
        // 构建链接表条件
        BooleanExpression joinOrderTradeProcessOn = Expressions.anyOf(qOrder.vendorMemberId.eq(qOrderTradeProcess.memberId).and(qOrder.vendorRoleId.eq(qOrderTradeProcess.roleId)), qOrderTradeProcess.memberId.eq(0L).and(qOrderTradeProcess.roleId.eq(0L)));
        JPAQuery<?> query = jpaQueryFactory.from(qOrder).distinct()
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderMaterial).on(qOrderProduct.id.eq(qOrderMaterial.orderProduct.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .leftJoin(qOrderTradeProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderTradeProcessProduct).on(qOrderTradeProcess.id.eq(qOrderTradeProcessProduct.process.id))
                .where(qOrder.id.eq(orderId)/*.and(qOrderTradeProcess.shopId.eq(shopId))*/);

        //Step 2: 固定的查询条件
        //只可选择已退货数量小于订单数量的订单商品
        query.where(qOrderProduct.returnCount.lt(qOrderProduct.quantity));

        //订单状态：
        //换货：只显示已收到第一批货之后的订单且当前订单外部状态不是待确认支付结果
        //退货：只显示已发过第一批货之后、或已经支付成功过一次的订单且当前订单外部状态不是待确认支付结果，且订单状态不是已中止
        //维修：只显示已收到第一批货之后的订单
        if(afterSalesType.equals(OrderTradeProcessTypeEnum.AFTER_SALES_EXCHANGES.getCode())) {
            query.where(qOrder.hasReceived.isTrue().and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode())));
        } else if(afterSalesType.equals(OrderTradeProcessTypeEnum.AFTER_SALES_RETURNS.getCode())) {
            query.where(qOrder.hasDelivered.isTrue().or(qOrder.hasPaid.isTrue()));
            query.where(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode()));
        } else {
            query.where(qOrder.hasReceived.isTrue());
        }

        //流程类型、状态
        query.where(qOrderTradeProcess.processType.eq(afterSalesType).and(qOrderTradeProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
        query.where(qOrderTradeProcess.allProducts.isTrue().or(qOrderTradeProcessProduct.productId.eq(qOrderProduct.productId).and(qOrderTradeProcessProduct.skuId.eq(qOrderProduct.skuId))));

        //Step 5: 使用transform()对结果进行聚合统计，并通过Projections.Constructor到VO对象
        Map<Long, MobileAfterSalePageQueryResp> transform = query.transform(GroupBy.groupBy(qOrder.id).as(
                new QMobileAfterSalePageQueryResp(qOrder.shopId, qOrder.id, qOrder.orderNo, qOrder.digest, qOrder.vendorMemberId,
                        qOrder.vendorRoleId, qOrder.vendorMemberName, qOrder.vendorLogo, qOrder.orderType, qOrder.createTime, qOrder.outerStatus,
                        GroupBy.list(new QMobileAfterSaleProductDetailResp(
                                qOrderProduct.id, qOrder.orderType, qOrderProduct.logo, qOrderProduct.skuId, qOrderProduct.productNo,
                                qOrderProduct.name, qOrderProduct.category, qOrderProduct.brand, qOrderProduct.unit, qOrderProduct.spec,
                                qOrderProduct.quantity, qOrderProduct.refPrice, qOrderProduct.amount, qOrderProduct.paidAmount,
                                qOrderProduct.exchangeCount, qOrderProduct.returnCount, qOrderProduct.maintainCount, qOrderProduct.returnAmount,
                                qOrderProduct.tax, qOrderProduct.taxRate, qOrderProduct.priceType, qOrderMaterial.skuId, qOrderMaterial.productNo,
                                qOrderMaterial.name, qOrderMaterial.category, qOrderMaterial.brand, qOrderMaterial.spec
                        ))
                )
        ));

        return transform.getOrDefault(orderId, null);
    }

    /**
     * 根据采购流程规则配置查询售后订单
     * @param orderId         订单Id
     * @param afterSalesType  售后类型，前端接口参数中定义的枚举，要和采购流程类型枚举一致
     * @return               查询结果
     */
    private MobileAfterSalePageQueryResp pageAfterSaleOrdersByPurchaseProcess(Long orderId, Integer afterSalesType) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
        QOrderMaterialDO qOrderMaterial = QOrderMaterialDO.orderMaterialDO;
//        QOrderContractDO qOrderContract = QOrderContractDO.orderContractDO;
        QOrderPurchaseProcessDO qOrderPurchaseProcess = QOrderPurchaseProcessDO.orderPurchaseProcessDO;
        QOrderPurchaseProcessContractDO qOrderPurchaseProcessContract = QOrderPurchaseProcessContractDO.orderPurchaseProcessContractDO;

        //Step 1: 定义关联关系，OrderDO表必须要有distinct()
        BooleanExpression joinOrderTradeProcessOn = Expressions.anyOf(qOrder.vendorMemberId.eq(qOrderPurchaseProcess.memberId).and(qOrder.vendorRoleId.eq(qOrderPurchaseProcess.roleId)), qOrderPurchaseProcess.memberId.eq(0L).and(qOrderPurchaseProcess.roleId.eq(0L)));
        JPAQuery<?> query = jpaQueryFactory.from(qOrder).distinct()
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .leftJoin(qOrderMaterial).on(qOrderProduct.id.eq(qOrderMaterial.orderProduct.id))
//                .leftJoin(qOrderContract).on(qOrder.id.eq(qOrderContract.order.id))
                .leftJoin(qOrderPurchaseProcess).on(joinOrderTradeProcessOn)
                .leftJoin(qOrderPurchaseProcessContract).on(qOrderPurchaseProcess.id.eq(qOrderPurchaseProcessContract.process.id))
                .where(qOrder.id.eq(orderId));

        //Step 2: 固定的查询条件
        //只可选择已退货数量小于订单数量的订单商品
        query.where(qOrderProduct.returnCount.lt(qOrderProduct.quantity));

        //订单状态：
        //换货：只显示已收到第一批货之后的订单且当前订单外部状态不是待确认支付结果
        //退货：只显示已发过第一批货之后、或已经支付成功过一次的订单且当前订单外部状态不是待确认支付结果，且订单状态不是已中止
        //维修：只显示已收到第一批货之后的订单
        if(afterSalesType.equals(OrderTradeProcessTypeEnum.AFTER_SALES_EXCHANGES.getCode())) {
            query.where(qOrder.hasReceived.isTrue().and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode())));
        } else if(afterSalesType.equals(OrderTradeProcessTypeEnum.AFTER_SALES_RETURNS.getCode())) {
            query.where(qOrder.hasDelivered.isTrue().or(qOrder.hasPaid.isTrue()));
            query.where(qOrder.outerStatus.ne(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode()).and(qOrder.outerStatus.ne(OrderOuterStatusEnum.TERMINATED.getCode())));
        } else {
            query.where(qOrder.hasReceived.isTrue());
        }

        //流程类型、状态
        query.where(qOrderPurchaseProcess.processType.eq(afterSalesType).and(qOrderPurchaseProcess.status.eq(EnableDisableStatusEnum.ENABLE.getCode())));
        //流程规则配置
//        query.where(qOrderPurchaseProcess.allContracts.isTrue().or(qOrderPurchaseProcessContract.contractId.eq(qOrderContract.contractId)));

        //Step 5: 使用transform()对结果进行聚合统计，并通过Projections.Constructor到VO对象
        Map<Long, MobileAfterSalePageQueryResp> transform = query.transform(GroupBy.groupBy(qOrder.id).as(
                Projections.constructor(MobileAfterSalePageQueryResp.class, qOrder.shopId, qOrder.id, qOrder.orderNo, qOrder.digest, qOrder.vendorMemberId, qOrder.vendorRoleId, qOrder.vendorMemberName, qOrder.vendorLogo, qOrder.orderType, qOrder.createTime, qOrder.outerStatus,
                        GroupBy.list(Projections.constructor(MobileAfterSaleProductDetailResp.class,
                                qOrderProduct.id,
                                qOrder.orderType,
//                                qOrderPurchaseProcess.processKey,
                                qOrderProduct.logo,
                                qOrderProduct.skuId,
                                qOrderProduct.productNo,
                                qOrderProduct.name,
                                qOrderProduct.category,
                                qOrderProduct.brand,
                                qOrderProduct.unit,
                                qOrderProduct.spec,
                                qOrderProduct.quantity,
                                qOrderProduct.refPrice,
                                qOrderProduct.amount,
                                qOrderProduct.paidAmount,
                                qOrderProduct.exchangeCount,
                                qOrderProduct.returnCount,
                                qOrderProduct.maintainCount,
                                qOrderProduct.returnAmount,
                                qOrderProduct.tax,
                                qOrderProduct.taxRate,
                                qOrderProduct.priceType,
                                qOrderMaterial.skuId,
                                qOrderMaterial.productNo,
                                qOrderMaterial.name,
                                qOrderMaterial.category,
                                qOrderMaterial.brand,
                                qOrderMaterial.spec
                        )))));

        return transform.getOrDefault(orderId, null);
    }
}
