package com.ssy.lingxi.order.serviceImpl.feign;

import com.ssy.lingxi.commodity.api.feign.IStoreFeign;
import com.ssy.lingxi.commodity.api.model.dto.MerchantLogoInnerDTO;
import com.ssy.lingxi.commodity.api.model.dto.StoreListInnerDTO;
import com.ssy.lingxi.commodity.api.model.resp.MerchantLogoInnerResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.order.model.bo.VendorBO;
import com.ssy.lingxi.order.model.dto.VendorLogoDTO;
import com.ssy.lingxi.order.service.feign.IPlatformTemplateFeignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 店铺模板服务Feign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-10-29
 */
@Slf4j
@Service
public class PlatformTemplateFeignServiceImpl implements IPlatformTemplateFeignService {
    @Resource
    private IStoreFeign storeFeign;

    /**
     * 查询供应商店铺或商城Logo
     *
     * @param shopId  商城Id
     * @param vendors 供应商列表
     * @return 查询结果
     */
    @Override
    public List<VendorLogoDTO> findVendorShopLogos(Long shopId, List<VendorBO> vendors) {
        MerchantLogoInnerDTO innerDTO = new MerchantLogoInnerDTO();
        innerDTO.setShopId(shopId);
        innerDTO.setList(vendors.stream().map(vendor -> {
            StoreListInnerDTO member = new StoreListInnerDTO();
            member.setMemberId(vendor.getVendorMemberId());
            member.setRoleId(vendor.getVendorRoleId());
            return member;
        }).collect(Collectors.toList()));

        try {
            WrapperResp<List<MerchantLogoInnerResp>> result = storeFeign.findMerchantLogoByShopId(innerDTO);
            WrapperUtil.throwWhenFail(result);

            return CollectionUtils.isEmpty(result.getData()) ? new ArrayList<>() : result.getData().stream().map(merchantLogoInnerVO ->
                    new VendorLogoDTO(shopId, merchantLogoInnerVO.getMemberId(), merchantLogoInnerVO.getRoleId(), merchantLogoInnerVO.getName(), merchantLogoInnerVO.getLogo())
            ).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询供应商店铺或商城Logo失败", e);
            if(e instanceof BusinessException){
                throw e;
            }
            throw new BusinessException(ResponseCodeEnum.SERVICE_PRODUCT_ERROR);
        }
    }

    /**
     * 查询供应商店铺或商城Logo
     *
     * @param shopId         商城Id
     * @param vendorMemberId 供应商会员Id
     * @param vendorRoleId   供应商会员角色Id
     * @return 供应商Logo
     */
    @Override
    public VendorLogoDTO findVendorShopLogo(Long shopId, Long vendorMemberId, Long vendorRoleId) {
        MerchantLogoInnerDTO innerDTO = new MerchantLogoInnerDTO();
        innerDTO.setShopId(shopId);
        StoreListInnerDTO member = new StoreListInnerDTO();
        member.setMemberId(vendorMemberId);
        member.setRoleId(vendorRoleId);
        innerDTO.setList(Collections.singletonList(member));

        try {
            WrapperResp<List<MerchantLogoInnerResp>> result = storeFeign.findMerchantLogoByShopId(innerDTO);
            WrapperUtil.throwWhenFail(result);

            if (!CollectionUtils.isEmpty(result.getData())) {
                MerchantLogoInnerResp innerVO = result.getData().stream().filter(r -> r.getMemberId().equals(vendorMemberId) && r.getRoleId().equals(vendorRoleId)).findFirst().orElse(null);
                if (innerVO != null) {
                    return new VendorLogoDTO(shopId, vendorMemberId, vendorRoleId, innerVO.getName(), innerVO.getLogo());
                }
            }

            return new VendorLogoDTO(shopId, vendorMemberId, vendorRoleId);
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_PRODUCT_ERROR, e.getMessage());
        }
    }
}
