package com.ssy.lingxi.order;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 订单服务启动类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/6/19
 */
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = {"com.ssy.lingxi.**.api.feign"})
@ComponentScan(basePackages = {
        "com.ssy.lingxi.component",
        "com.ssy.lingxi.order.util",
        "com.ssy.lingxi.**.api.fallback",
        "com.ssy.lingxi." + ServiceModuleConstant.ORDER + ".config",
        "com.ssy.lingxi." + ServiceModuleConstant.ORDER + ".controller",
        "com.ssy.lingxi." + ServiceModuleConstant.ORDER + ".entity",
        "com.ssy.lingxi." + ServiceModuleConstant.ORDER + ".handler",
        "com.ssy.lingxi." + ServiceModuleConstant.ORDER + ".repository",
        "com.ssy.lingxi." + ServiceModuleConstant.ORDER + ".serviceImpl",
})
@EnableDiscoveryClient
@SpringBootApplication
public class OrderServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderServiceApplication.class, args);
    }

}
