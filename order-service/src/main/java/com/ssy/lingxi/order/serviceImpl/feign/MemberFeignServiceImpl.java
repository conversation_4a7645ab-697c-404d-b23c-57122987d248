package com.ssy.lingxi.order.serviceImpl.feign;

import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.FundModeEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRightSpendTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberDetailFeign;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.feign.IMemberLevelRightCreditFeign;
import com.ssy.lingxi.member.api.feign.IMemberOrderCommentFeign;
import com.ssy.lingxi.member.api.model.req.*;
import com.ssy.lingxi.member.api.model.resp.*;
import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.model.bo.VendorBO;
import com.ssy.lingxi.order.model.dto.*;
import com.ssy.lingxi.order.model.req.basic.OrderDeductionReq;
import com.ssy.lingxi.order.service.feign.IMemberFeignService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员服务Feign接口调用类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-23
 */
@Service
public class MemberFeignServiceImpl implements IMemberFeignService {
    private static final Logger logger = LoggerFactory.getLogger(MemberFeignServiceImpl.class);

    @Resource
    private IMemberLevelRightCreditFeign memberLevelRightCreditControllerFeign;

    @Resource
    private IMemberFeign memberInnerControllerFeign;

    @Resource
    private IMemberOrderCommentFeign memberOrderCommentControllerFeign;

    @Resource
    private IMemberDetailFeign memberDetailFeign;

    /**
     * （异步）通知会员服务，计算等级、权益、积分
     * @param buyerMemberId  采购商会员Id
     * @param buyerRoleId    采购商会员角色Id
     * @param vendorMemberId 供应商会员Id
     * @param vendorRoleId   供应商会员角色Id
     * @param amount         订单金额
     * @param orderNo        订单号
     */
    @Async
    @Override
    public void calculateMemberLrcAsync(Long buyerMemberId, Long buyerRoleId, Long vendorMemberId, Long vendorRoleId, BigDecimal amount, String orderNo) {
        try {
            MemberFeignOrderReq orderVO = new MemberFeignOrderReq();
            orderVO.setUpperMemberId(vendorMemberId);
            orderVO.setUpperRoleId(vendorRoleId);
            orderVO.setSubMemberId(buyerMemberId);
            orderVO.setSubRoleId(buyerRoleId);
            orderVO.setOrderNo(orderNo);
            orderVO.setAmount(amount);
            logger.info("通知会员服务，计算等级、权益、积分 => " + SerializeUtil.serialize(orderVO));
            WrapperResp<MemberFeignReturnRightResp> returnRight = memberLevelRightCreditControllerFeign.calculateMemberLrcByOrder(orderVO);
            if (returnRight.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                logger.error("通知会员服务，计算等级、权益、积分错误：" + returnRight.getMessage());
            }
        } catch (Exception e) {
            logger.error("通知会员服务，计算等级、权益、积分错误：" + e.getMessage());
        }
    }

    /**
     * 通知会员服务，计算等级、权益、积分
     * @param buyerMemberId  采购商会员Id
     * @param buyerRoleId    采购商会员角色Id
     * @param vendorMemberId 供应商会员Id
     * @param vendorRoleId   供应商会员角色Id
     * @param amount         订单金额
     * @param orderNo        订单号
     * @return 订单返现金额
     */
    @Override
    public BigDecimal calculateMemberLrc(Long buyerMemberId, Long buyerRoleId, Long vendorMemberId, Long vendorRoleId, BigDecimal amount, String orderNo) {
        MemberFeignOrderReq orderVO = new MemberFeignOrderReq();
        orderVO.setUpperMemberId(vendorMemberId);
        orderVO.setUpperRoleId(vendorRoleId);
        orderVO.setSubMemberId(buyerMemberId);
        orderVO.setSubRoleId(buyerRoleId);
        orderVO.setOrderNo(orderNo);
        orderVO.setAmount(amount);
        WrapperResp<MemberFeignReturnRightResp> returnRight = memberLevelRightCreditControllerFeign.calculateMemberLrcByOrder(orderVO);
        WrapperUtil.throwWhenFail(returnRight);

        return returnRight.getData().getReturnMoney();
    }

    /**
     * 积分支付订单，校验可用信用积分、支付密码
     * @param buyerMemberId            采购会员Id
     * @param buyerRoleId              采购会员角色Id
     * @param vendorMemberId           供应会员Id
     * @param vendorRoleId             供应会员角色Id
     * @param fundMode                 资金归集模式，1-平台代收，2-会员直接到账
     * @param payPassword              前端传递的Aes加密后的支付密码
     * @param memberRightSpendTypeEnum 积分消费类型枚举
     * @param payAmount                使用的积分
     * @param orderNo                  订单号
     */
    @Override
    public void calculateMemberUsedRightPoint(Long buyerMemberId, Long buyerRoleId, Long vendorMemberId, Long vendorRoleId, Integer fundMode, String payPassword, MemberRightSpendTypeEnum memberRightSpendTypeEnum, BigDecimal payAmount, String orderNo) {
        MemberFeignRightSpendReq spendVO = new MemberFeignRightSpendReq();
        spendVO.setUpperMemberId(vendorMemberId);
        spendVO.setUpperRoleId(vendorRoleId);
        spendVO.setSubMemberId(buyerMemberId);
        spendVO.setSubRoleId(buyerRoleId);
        spendVO.setRelType(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? 0 : 1);
        spendVO.setPayPassword(payPassword);
        spendVO.setUsedPoint(payAmount.intValue());
        spendVO.setSpentType(memberRightSpendTypeEnum.getTypeEnum());
        spendVO.setOrderNo(orderNo);
        WrapperResp<Void> result = memberLevelRightCreditControllerFeign.calculateMemberUsedRightPoint(spendVO);
        WrapperUtil.throwWhenFail(result);
    }

    /**
     * 批量查询会员价格权益（会员折扣）
     * @param vendorRelations 会员关系列表
     * @return 查询结果
     */
    @Override
    public List<MemberFeignRelationRightDetailResp> batchFindMemberPriceRight(List<MemberFeignRelationReq> vendorRelations) {
        vendorRelations = vendorRelations.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vendorRelations)) {
            return new ArrayList<>();
        }

        WrapperResp<List<MemberFeignRelationRightDetailResp>> result = memberLevelRightCreditControllerFeign.batchMemberPriceRight(vendorRelations);
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 订单完成后，向会员服务的订单评论功能发送订单数据
     * @param order 订单
     */
    @Async
    @Override
    public void saveMemberOrderCommentAsync(OrderDO order) {
        try {
            MemberOrderCommentReq commentVO = new MemberOrderCommentReq();
            commentVO.setId(order.getId());
            commentVO.setCreateTime(order.getCreateTime());
            commentVO.setBuyerMemberId(order.getBuyerMemberId());
            commentVO.setBuyerRoleId(order.getBuyerRoleId());
            commentVO.setBuyerMemberName(order.getBuyerMemberName());
            commentVO.setVendorMemberId(order.getVendorMemberId());
            commentVO.setVendorRoleId(order.getVendorRoleId());
            commentVO.setVendorMemberName(order.getVendorMemberName());
            commentVO.setOrderNo(order.getOrderNo());
            commentVO.setShopId(order.getShopId());
            commentVO.setShopType(order.getShopType());
            commentVO.setShopEnvironment(order.getShopEnvironment());
            commentVO.setShopName(order.getShopName());
            commentVO.setOrderMode(order.getOrderMode());
            commentVO.setOrderType(order.getOrderType());
            commentVO.setOrderKind(order.getOrderKind());
            commentVO.setQuoteNo(order.getQuoteNo());
            commentVO.setDigest(order.getDigest());
            commentVO.setProductAmount(order.getProductAmount());
            commentVO.setFreight(order.getFreight());
            commentVO.setPromotionAmount(order.getPromotionAmount());
            commentVO.setCouponAmount(order.getCouponAmount());
            commentVO.setTotalAmount(order.getTotalAmount());
            commentVO.setBuyerInnerStatus(order.getBuyerInnerStatus());
            commentVO.setVendorInnerStatus(order.getVendorInnerStatus());
            commentVO.setOuterStatus(order.getOuterStatus());
            commentVO.setProducts(order.getProducts().stream().map(product -> {
                MemberOrderProductCommentReq productCommentVO = new MemberOrderProductCommentReq();
                productCommentVO.setId(product.getId());
                productCommentVO.setProductId(product.getProductId());
                productCommentVO.setSkuId(product.getSkuId());
                productCommentVO.setProductNo(product.getProductNo());
                productCommentVO.setName(product.getName());
                productCommentVO.setCategory(product.getCategory());
                productCommentVO.setBrand(product.getBrand());
                productCommentVO.setSpec(product.getSpec());
                productCommentVO.setUnit(product.getUnit());
                productCommentVO.setLogo(product.getLogo());
                productCommentVO.setPrice(product.getRefPrice());
                productCommentVO.setQuantity(product.getQuantity());
                productCommentVO.setAmount(product.getAmount());
                productCommentVO.setDeliverType(product.getDeliverType());
                productCommentVO.setAddress(product.getAddress());
                productCommentVO.setReceiver(product.getReceiver());
                productCommentVO.setPhone(product.getPhone());
                return productCommentVO;
            }).collect(Collectors.toList()));
            WrapperResp<Void> result = memberOrderCommentControllerFeign.saveMemberOrderComment(commentVO);
            if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                logger.error("订单完成后，向会员服务的订单评论功能发送订单数据错误：" + result.getMessage());
            }

        } catch (Exception e) {
            logger.error("订单完成后，向会员服务的订单评论功能发送订单数据错误：" + e.getMessage());
        }
    }

    /**
     * 查询平台会员的信息
     * @param members 会员Id和角色Id列表
     * @param name    会员名称
     * @return 查询结果
     */
    @Override
    public List<OrderMemberQueryDTO> findPlatformMembers(String name, List<MemberFeignReq> members) {
        WrapperResp<List<MemberFeignPageQueryResp>> feignResult = memberInnerControllerFeign.findPlatformMembers(members);
        WrapperUtil.throwWhenFail(feignResult);

        if (CollectionUtils.isEmpty(feignResult.getData())) {
            return new ArrayList<>();
        }

        return feignResult.getData().stream().map(member -> {
            OrderMemberQueryDTO queryVO = new OrderMemberQueryDTO();
            queryVO.setMemberId(member.getMemberId());
            queryVO.setRoleId(member.getRoleId());
            queryVO.setName(member.getName());
            queryVO.setRoleName(member.getRoleName());
            queryVO.setMemberTypeName(member.getMemberTypeName());
            queryVO.setLevel(member.getLevel());
            queryVO.setLevelTag(member.getLevelTag());
            return queryVO;
        }).collect(Collectors.toList()).stream().filter(r -> {
            if (StringUtils.hasLength(name)) {
                return r.getName().contains(name.trim());
            } else {
                return true;
            }
        }).sorted(Comparator.comparingLong(OrderMemberQueryDTO::getMemberId).thenComparingLong(OrderMemberQueryDTO::getRoleId)).collect(Collectors.toList());
    }

    /**
     * 批量查询会员Logo
     * @param memberIds 会员Id列表
     * @return 会员logo列表
     */
    @Override
    public List<MemberLogoDTO> findMemberLogos(List<Long> memberIds) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return new ArrayList<>();
        }
        WrapperResp<List<MemberFeignLogoResp>> logoResult = memberInnerControllerFeign.getMemberLogos(memberIds);
        WrapperUtil.throwWhenFail(logoResult);

        return logoResult.getData().stream().map(result -> new MemberLogoDTO(result.getMemberId(), result.getName(), result.getLogo())).collect(Collectors.toList());
    }

    /**
     * 查询会员Logo
     * @param memberId 会员Id
     * @return 会员logo
     */
    @Override
    public MemberLogoDTO findMemberLogo(Long memberId) {
        WrapperResp<List<MemberFeignLogoResp>> logoResult = memberInnerControllerFeign.getMemberLogos(Collections.singletonList(memberId));
        WrapperUtil.throwWhenFail(logoResult);

        return logoResult.getData().stream().filter(member -> member.getMemberId().equals(memberId)).map(member -> new MemberLogoDTO(member.getMemberId(), member.getName(), member.getLogo())).findFirst().orElse(new MemberLogoDTO(memberId));
    }

    /**
     * 平台后台-商户支付策略配置，查询角色类型为服务提供者的平台商户会员列表
     * @param name    会员名称
     * @param members 会员Id和角色Id列表
     * @return 查询结果
     */
    @Override
    public List<OrderMemberQueryDTO> listPlatformMembers(String name, List<MemberDTO> members) {
        MemberFeignPaymentReq paymentVO = new MemberFeignPaymentReq();
        paymentVO.setName(StringUtils.hasLength(name) ? name : "");
        paymentVO.setMembers(CollectionUtils.isEmpty(members) ? new ArrayList<>() : members.stream().map(member -> {
            MemberFeignReq feignVO = new MemberFeignReq();
            feignVO.setMemberId(member.getMemberId());
            feignVO.setRoleId(member.getRoleId());
            return feignVO;
        }).collect(Collectors.toList()));

        WrapperResp<List<MemberFeignPageQueryResp>> feignResult = memberInnerControllerFeign.findProviderMerchant(paymentVO);
        WrapperUtil.throwWhenFail(feignResult);

        if (CollectionUtils.isEmpty(feignResult.getData())) {
            return new ArrayList<>();
        }

        return feignResult.getData().stream().map(member -> {
            OrderMemberQueryDTO queryVO = new OrderMemberQueryDTO();
            queryVO.setMemberId(member.getMemberId());
            queryVO.setRoleId(member.getRoleId());
            queryVO.setName(member.getName());
            queryVO.setRoleName(member.getRoleName());
            queryVO.setMemberTypeName(member.getMemberTypeName());
            queryVO.setLevel(member.getLevel());
            queryVO.setLevelTag(member.getLevelTag());
            return queryVO;
        }).collect(Collectors.toList());
    }

    /**
     * SRM订单 - 查询会员入库分类 - 主营品类及结算方式信息
     * @param buyerMemberId  采购会员Id
     * @param buyerRoleId    采购会员角色Id
     * @param vendorMemberId 供应会员Id
     * @param vendorRoleId   供应会员角色Id
     * @return 查询结果
     */
    @Override
    public List<MemberCategoryDTO> findMemberBusinessCategories(Long buyerMemberId, Long buyerRoleId, Long vendorMemberId, Long vendorRoleId) {
        //Srm订单，上级会员是“采购会员”，下级会员是“供应会员”
        MemberFeignRelationReq feignVO = new MemberFeignRelationReq();
        feignVO.setUpperMemberId(buyerMemberId);
        feignVO.setUpperRoleId(buyerRoleId);
        feignVO.setSubMemberId(vendorMemberId);
        feignVO.setSubRoleId(vendorRoleId);
        try {
            logger.info("从会员服务查询结算方式参数 => " + SerializeUtil.serialize(feignVO));
            WrapperResp<List<MemberCategoryFeignResp>> result = memberDetailFeign.findMemberBusinessCategories(feignVO);
            if (result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new ArrayList<>();
            }

            logger.info("从会员服务查询结算方式返回 => " + SerializeUtil.serialize(result));
            if (CollectionUtils.isEmpty(result.getData())) {
                return new ArrayList<>();
            } else {
                return result.getData().stream().map(category -> {
                    MemberCategoryDTO categoryDTO = new MemberCategoryDTO();
                    if (!CollectionUtils.isEmpty(category.getCategories())) {
                        categoryDTO.setCategories(category.getCategories().stream().filter(Objects::nonNull).map(c -> {
                            MemberBusinessCategoryDTO businessCategoryDTO = new MemberBusinessCategoryDTO();
                            businessCategoryDTO.setLevel(c.getLevel());
                            businessCategoryDTO.setCategoryId(c.getCategoryId());
                            businessCategoryDTO.setName(c.getName());
                            return businessCategoryDTO;
                        }).collect(Collectors.toList()));
                    } else {
                        categoryDTO.setCategories(null);
                    }
                    categoryDTO.setPayType(category.getPayType());
                    categoryDTO.setPayTypeName(category.getPayTypeName());
                    categoryDTO.setMonth(category.getMonth());
                    categoryDTO.setMonthDay(category.getMonthDay());
                    categoryDTO.setDays(category.getDays());
                    categoryDTO.setInvoiceType(category.getInvoiceType());
                    categoryDTO.setInvoiceTypeName(category.getInvoiceTypeName());
                    categoryDTO.setTaxPoint(category.getTaxPoint());
                    categoryDTO.setIsDefault(category.getIsDefault());
                    return categoryDTO;
                }).collect(Collectors.toList());

            }
        } catch (Exception ignored) {
            return new ArrayList<>();
        }
    }

    /**
     * 查询供应商业务员用户Id
     * @param buyerMemberId  采购会员Id
     * @param buyerRoleId    采购会员角色Id
     * @param vendorMemberId 供应会员Id
     * @param vendorRoleId   供应会员角色Id
     * @return 查询结果
     */
    @Override
    public Long findVendorSalesUserId(Long buyerMemberId, Long buyerRoleId, Long vendorMemberId, Long vendorRoleId) {
        OrderMemberDTO member = new OrderMemberDTO(buyerMemberId, buyerRoleId, vendorMemberId, vendorRoleId);
        List<OrderSalesDTO> salesResult = findVendorSales(Collections.singletonList(member));

        return CollectionUtils.isEmpty(salesResult) ? 0L : salesResult.stream().filter(r -> r.getBuyerMemberId().equals(buyerMemberId) && r.getBuyerRoleId().equals(buyerRoleId) && r.getVendorMemberId().equals(vendorMemberId) && r.getVendorRoleId().equals(vendorRoleId)).map(OrderSalesDTO::getVendorUserId).findFirst().orElse(0L);
    }

    /**
     * 批量查询供应商业务员用户Id
     * @param buyerMemberId 采购会员id
     * @param buyerRoleId   采购会员角色Id
     * @param vendors       供应商列表
     * @return 查询结果
     */
    @Override
    public List<OrderSalesDTO> findVendorSales(Long buyerMemberId, Long buyerRoleId, List<VendorBO> vendors) {
        List<OrderMemberDTO> members = vendors.stream().map(vendor -> new OrderMemberDTO(buyerMemberId, buyerRoleId, vendor.getVendorMemberId(), vendor.getVendorRoleId())).distinct().collect(Collectors.toList());
        return findVendorSales(members);
    }

    /**
     * 批量查询供应商业务员用户Id
     * @param members 采购会员与供应会员列表
     * @return 查询结果
     */
    @Override
    public List<OrderSalesDTO> findVendorSales(List<OrderMemberDTO> members) {
        List<MemberFeignRelationReq> relations = members.stream().map(member -> {
            MemberFeignRelationReq relationVO = new MemberFeignRelationReq();
            relationVO.setSubMemberId(member.getBuyerMemberId());
            relationVO.setSubRoleId(member.getBuyerRoleId());
            relationVO.setUpperMemberId(member.getVendorMemberId());
            relationVO.setUpperRoleId(member.getVendorRoleId());
            return relationVO;
        }).collect(Collectors.toList());

        try {
            WrapperResp<List<MemberSalesFeignResp>> salesResult = memberInnerControllerFeign.findMemberSales(relations);
            WrapperUtil.throwWhenFail(salesResult);

            if (CollectionUtils.isEmpty(salesResult.getData())) {
                return new ArrayList<>();
            }

            return salesResult.getData().stream().map(r ->
                    new OrderSalesDTO(r.getSubMemberId(), r.getSubRoleId(), r.getMemberId(), r.getRoleId(), r.getUserId())
            ).collect(Collectors.toList());
        } catch (Exception ignored) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_MEMBER_ERROR);
        }
    }

    /**
     * 查询用户信息
     * @param userId 用户Id
     * @return 用户信息
     */
    @Override
    public MemberUserDTO findUserDetail(Long userId) {
        MemberFeignUserIdReq idVO = new MemberFeignUserIdReq();
        idVO.setUserId(userId);
        try {
            WrapperResp<MemberFeignUserDetailResp> userResult = memberDetailFeign.findMemberUser(idVO);
            if (userResult.getCode() == ResponseCodeEnum.SUCCESS.getCode() && Objects.nonNull(userResult.getData())) {
                return new MemberUserDTO(userResult.getData().getUserId(), userResult.getData().getUserName(), userResult.getData().getOrganizationName(), userResult.getData().getJobTitle());
            }

            return new MemberUserDTO(userId);
        } catch (Exception ignored) {
            return new MemberUserDTO(userId);
        }
    }

    public List<MemberFeignRightByOrderResp> batchMemberPriceRightForOrder(Long buyerMemberId, Long buyerRoleId, List<MemberAndRoleIdDTO> upperMemberList) {
        MemberFeignBatchReq spendVO = new MemberFeignBatchReq();
        spendVO.setMemberId(buyerMemberId);
        spendVO.setRoleId(buyerRoleId);
        List<MemberFeignUpperMemberReq> upperMembers = upperMemberList.stream().map(o -> {
            MemberFeignUpperMemberReq feignUpperMemberVO = new MemberFeignUpperMemberReq();
            feignUpperMemberVO.setUpperMemberId(o.getMemberId());
            feignUpperMemberVO.setUpperRoleId(o.getRoleId());
            return feignUpperMemberVO;
        }).collect(Collectors.toList());
        spendVO.setUpperMembers(upperMembers);
        WrapperResp<List<MemberFeignRightByOrderResp>> result = memberLevelRightCreditControllerFeign.batchMemberPriceRightForOrder(spendVO);
        WrapperUtil.throwWhenFail(result);

        return result.getData();
    }

    /**
     * 扣减会员积分或平台积分
     * @param deductions 积分抵扣列表
     * @param order      订单
     */
    @Override
    public void memberDeductionPoints(OrderDO order, List<OrderDeductionReq> deductions, BigDecimal usePoints) {
        MemberFeignRightDeductionReq deductionVO = new MemberFeignRightDeductionReq();
        deductionVO.setSubMemberId(order.getBuyerMemberId());
        deductionVO.setSubRoleId(order.getBuyerRoleId());
        deductionVO.setRelType(deductions.get(0).getRelType());
        deductionVO.setItemList(deductions.stream().map(deduction -> {
            MemberFeignRightDeductionItemReq itemVO = new MemberFeignRightDeductionItemReq();
            itemVO.setOrderNo(order.getOrderNo());
            itemVO.setUpperMemberId(deduction.getVendorMemberId());
            itemVO.setUpperRoleId(deduction.getVendorRoleId());
            itemVO.setUsedPoint(usePoints.intValue());
            return itemVO;
        }).collect(Collectors.toList()));

        WrapperResp<Void> result = memberLevelRightCreditControllerFeign.calculateMemberDeductionRightPoint(deductionVO);
        WrapperUtil.throwWhenFail(result);
    }

    /**
     * 订单取消后，返还已抵扣的积分
     * @param order 订单
     */
    @Override
    public void returnDeductionPoints(OrderDO order) {
        MemberFeignRightReturnReq returnVO = new MemberFeignRightReturnReq();
        returnVO.setOrderNo(order.getOrderNo());
        returnVO.setSubMemberId(order.getBuyerMemberId());
        returnVO.setSubRoleId(order.getBuyerRoleId());

        WrapperResp<Void> result = memberLevelRightCreditControllerFeign.returnMemberRightPoint(returnVO);
        WrapperUtil.throwWhenFail(result);
    }

    @Override
    public List<MemberBrandInfoResp> findMemberBrandByIds(CommonIdListReq commonIdListReq) {
        return WrapperUtil.getDataOrThrow(memberInnerControllerFeign.findMemberBrandByIds(commonIdListReq));
    }

    @Override
    public MemberBrandInfoResp findMemberBrandById(CommonIdReq commonIdReq) {
        return WrapperUtil.getDataOrThrow(memberInnerControllerFeign.findMemberBrandById(commonIdReq));
    }
}
