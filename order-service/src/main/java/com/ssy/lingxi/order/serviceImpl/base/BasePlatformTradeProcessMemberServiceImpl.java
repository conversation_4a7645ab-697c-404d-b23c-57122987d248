package com.ssy.lingxi.order.serviceImpl.base;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.CollectionPageUtil;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.order.entity.PlatformTradeProcessDO;
import com.ssy.lingxi.order.entity.PlatformTradeProcessMemberDO;
import com.ssy.lingxi.order.model.dto.OrderMemberQueryDTO;
import com.ssy.lingxi.order.model.req.basic.OrderMemberIdAndRoleIdReq;
import com.ssy.lingxi.order.repository.PlatformTradeProcessMemberRepository;
import com.ssy.lingxi.order.service.base.IBasePlatformTradeProcessMemberService;
import com.ssy.lingxi.order.service.feign.IMemberFeignService;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 平台后台 - 交易流程规则关联的会员相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-26
 */
@Service
public class BasePlatformTradeProcessMemberServiceImpl implements IBasePlatformTradeProcessMemberService {
    @Resource
    private PlatformTradeProcessMemberRepository platformTradeProcessMemberRepository;

    @Resource
    private IMemberFeignService memberFeignService;


    /**
     * 校验、新增交易流程关联的会员，调用方要保存 PlatformTradeProcessDO
     *
     * @param tradeProcess 交易流程
     * @param allMembers   是否适用于所有会员
     * @param memberList      会员列表
     */
    @Override
    public void checkMembers(PlatformTradeProcessDO tradeProcess, Boolean allMembers, List<OrderMemberIdAndRoleIdReq> memberList) {
        if(allMembers) {
            tradeProcess.setMembers(new HashSet<>());
            return;
        }

        List<PlatformTradeProcessMemberDO> members = memberList.stream().map(memberVO -> {
            PlatformTradeProcessMemberDO processMember = new PlatformTradeProcessMemberDO();
            processMember.setProcess(tradeProcess);
            processMember.setMemberId(memberVO.getMemberId());
            processMember.setRoleId(memberVO.getRoleId());
            return processMember;
        }).collect(Collectors.toList());

        platformTradeProcessMemberRepository.saveAll(members);

        tradeProcess.setMembers(new HashSet<>(members));
    }

    /**
     * 分页查询交易流程关联的会员列表
     *
     * @param tradeProcess 交易流程
     * @param name     会员名称
     * @param current  当前页
     * @param pageSize 每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderMemberQueryDTO> pageMembers(PlatformTradeProcessDO tradeProcess, String name, int current, int pageSize) {
        if(tradeProcess.getAllMembers()) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        List<PlatformTradeProcessMemberDO> members = platformTradeProcessMemberRepository.findByProcess(tradeProcess, Sort.by("id").ascending());
        if(CollectionUtils.isEmpty(members)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //从会员服务查询
        List<MemberFeignReq> feignList = members.stream().map(member -> {
            MemberFeignReq feignVO = new MemberFeignReq();
            feignVO.setMemberId(member.getMemberId());
            feignVO.setRoleId(member.getRoleId());
            return feignVO;
        }).collect(Collectors.toList());

        List<OrderMemberQueryDTO> feignResult = memberFeignService.findPlatformMembers(name, feignList);

        if(CollectionUtils.isEmpty(feignResult)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        return new PageDataResp<>((long) feignResult.size(), CollectionPageUtil.pageList(feignResult, current, pageSize));
    }

    /**
     * 校验、修改交易流程关联的会员，调用方要保存 PlatformTradeProcessDO
     *
     * @param tradeProcess 交易流程
     * @param allMembers   是否适用于所有会员
     * @param memberList      会员列表
     */
    @Transactional
    @Override
    public void updateMembers(PlatformTradeProcessDO tradeProcess, Boolean allMembers, List<OrderMemberIdAndRoleIdReq> memberList) {
        if(allMembers) {
            platformTradeProcessMemberRepository.deleteByProcess(tradeProcess);
            tradeProcess.setMembers(new HashSet<>());
            return;
        }

        //先删除再保存
        platformTradeProcessMemberRepository.deleteByProcess(tradeProcess);

        List<PlatformTradeProcessMemberDO> members = memberList.stream().map(memberVO -> {
            PlatformTradeProcessMemberDO processMember = new PlatformTradeProcessMemberDO();
            processMember.setProcess(tradeProcess);
            processMember.setMemberId(memberVO.getMemberId());
            processMember.setRoleId(memberVO.getRoleId());
            return processMember;
        }).collect(Collectors.toList());

        platformTradeProcessMemberRepository.saveAll(members);

        tradeProcess.setMembers(new HashSet<>(members));
    }
}
