package com.ssy.lingxi.order.serviceImpl.feign;

import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.order.service.feign.ISearchFeignService;
import com.ssy.lingxi.product.api.feign.IPurchaseFeign;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品搜索服务Feign接口调用类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-30
 */
@Service
public class SearchFeignServiceImpl implements ISearchFeignService {
    private static final Logger logger = LoggerFactory.getLogger(SearchFeignServiceImpl.class);

    @Resource
    private IPurchaseFeign purchaseFeign;

    /**
     * 下单成功后，删除购物车
     *
     * @param shopType 商城类型
     * @param cartIds  购物车Id列表
     */
    @Async
    @Override
    public void emptyProductCartAsync(Integer shopType, List<Long> cartIds) {
        CommonIdListReq request = new CommonIdListReq();
        request.setIdList(cartIds);
        try {
            switch (ShopTypeEnum.parseCode(shopType)) {
                case ENTERPRISE:
                case SCORE:
                    purchaseFeign.deletePurchase(request);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            logger.error("调用商品搜索服务清除购物车错误 ：" + e.getMessage());
        }
    }
}
