package com.ssy.lingxi.order.serviceImpl.feign;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ssy.lingxi.common.constant.mq.ContractMqConstant;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.CommonNoReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.contract.api.feign.IContractBaseFeign;
import com.ssy.lingxi.contract.api.feign.IContractFeign;
import com.ssy.lingxi.contract.api.feign.IOrderContractFeign;
import com.ssy.lingxi.contract.api.model.req.*;
import com.ssy.lingxi.contract.api.model.resp.ContractMaterielListResp;
import com.ssy.lingxi.contract.api.model.resp.ContractResp;
import com.ssy.lingxi.contract.api.model.resp.ContractTemplateResp;
import com.ssy.lingxi.contract.api.model.resp.OrderContractResp;
import com.ssy.lingxi.order.model.dto.OrderProductDTO;
import com.ssy.lingxi.order.service.feign.IContractFeignService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同服务Feign接口调用实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-26
 */
@Slf4j
@Service
public class ContractFeignServiceImpl implements IContractFeignService {
    @Resource
    private IMqUtils mqUtils;

    @Resource
    private IContractFeign contractFeign;

    @Resource
    private IContractBaseFeign contractBaseFeign;

    @Resource
    private IOrderContractFeign orderContractFeign;

    /**
     * 合同订单（SRM订单）创建后，向合同服务同步订单数据
     *
     * @param orderId     订单Id
     * @param totalAmount 订单金额
     * @param products    商品信息列表
     */
    @Override
    public void createSrmOrderAsync(Long orderId, BigDecimal totalAmount, List<OrderProductDTO> products) {
        srmOrderAsync(orderId, totalAmount, 1, products);
    }

    /**
     * 合同订单（SRM订单）取消后，向合同服务同步订单数据
     *
     * @param orderId     订单Id
     * @param totalAmount 订单金额
     * @param products    商品信息列表
     */
    @Override
    public void cancelSrmOrderAsync(Long orderId, BigDecimal totalAmount, List<OrderProductDTO> products) {
        srmOrderAsync(orderId, totalAmount, 2, products);
    }

    /**
     * 合同订单（SRM订单）变更后，向合同服务同步订单数据
     *
     * @param orderId     订单Id
     * @param totalAmount 订单金额
     * @param products    商品信息列表
     */
    @Override
    public void changeSrmOrderAsync(Long orderId, BigDecimal totalAmount, List<OrderProductDTO> products) {
        srmOrderAsync(orderId, totalAmount, 3, products);
    }

    /**
     * 向合同服务同步Srm订单数据
     *
     * @param orderId     订单Id
     * @param totalAmount 订单金额
     * @param type        类型：1-创建订单，2-取消订单
     * @param products    商品信息列表
     */
    @Async
    @Override
    public void srmOrderAsync(Long orderId, BigDecimal totalAmount, Integer type, List<OrderProductDTO> products) {
        ContractOrderAmountLogReq request = new ContractOrderAmountLogReq();
        request.setOrderId(orderId);
        request.setSumPrice(totalAmount);
        request.setType(type);
        request.setContractOrderMaterielRequestList(products.stream().map(p -> {
            ContractOrderMaterielReq materielRequest = new ContractOrderMaterielReq();
            materielRequest.setAssociatedDataId(p.getProductId());
            materielRequest.setAssociatedMaterielNo(p.getProductNo());
            materielRequest.setCount(p.getQuantity());
            materielRequest.setRequisitionList(p.getRequisitions().stream().map(requisition -> {
                ContractOrderMaterielDetailReq requisitionRequest = new ContractOrderMaterielDetailReq();
                requisitionRequest.setDetailId(requisition.getDetailId());
                requisitionRequest.setCount(requisition.getCount());
                return requisitionRequest;
            }).collect(Collectors.toList()));
            return materielRequest;
        }).collect(Collectors.toList()));

        try {
            ObjectMapper mapper = new ObjectMapper();
            String json = mapper.writeValueAsString(request);
            log.info("向合同服务发送消息：" + json);
            mqUtils.sendMsg(ContractMqConstant.CONTRACT_ORDER_CHANGE_EXCHANGE, ContractMqConstant.CONTRACT_ORDER_CHANGE_ROUTINGKEY, json);
        } catch (Exception e) {
            log.error("向合同服务发送mq消息错误：" + e.getMessage());
        }
    }

    /**
     * 根据合同id查询订单是否已经停用
     * @param commonIdReq 合同id查询条件
     */
    @Override
    public Boolean checkContractIsChange(CommonIdReq commonIdReq) {
        WrapperResp<Boolean> result = contractFeign.checkContractIsChange(commonIdReq);
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 根据订单id查询最新合同(先有合同，再有订单)
     * @param commonIdReq 订单id查询条件
     */
    @Override
    public ContractResp getTheLastContractByOrderId(CommonIdReq commonIdReq) {
        return WrapperUtil.getData(contractFeign.getTheLastContractByOrderId(commonIdReq));
    }

    /**
     * 根据合同编码查询合同信息(先有合同，再有订单)
     * @param commonNoReq 同编码
     */
    @Override
    public ContractResp getTheLastContractByContractNo(CommonNoReq commonNoReq) {
        return WrapperUtil.getData(contractFeign.getTheLastContractByContractNo(commonNoReq));
    }

    /**
     * 根据订单id查询最新合同(先有订单，再有合同)
     * @param commonIdReq 订单id查询条件
     */
    @Override
    public OrderContractResp getOrderContractByOrderId(CommonIdReq commonIdReq) {
        return WrapperUtil.getData(orderContractFeign.getTheLastContractByOrderId(commonIdReq));
    }

    /**
     * 根据合同id查询合同物料剩余数量(变更合同则返回新合同数量)
     * @param request 参数
     */
    @Override
    public List<ContractMaterielListResp> getNewMaterielList(ContractMaterielListReq request) {
        return WrapperUtil.getData(contractFeign.getNewMaterielList(request));
    }

    /**
     * 根据合同模板id查询合同模板
     * @param commonIdReq 合同模板id查询条件
     */
    @Override
    public ContractTemplateResp getContractTemplateById(CommonIdReq commonIdReq) {
        return WrapperUtil.getData(contractBaseFeign.getContractTemplateById(commonIdReq));
    }

    /**
     * 获取确认签署跳转url
     * @param contractSignReq 参数
     */
    @Override
    public String getSignUrl(ContractSignReq contractSignReq) {
        return WrapperUtil.getData(contractBaseFeign.getSignUrl(contractSignReq));
    }

//    @Override
//    public SignCreateResp srmOrderContractSign(OrderDO order, UserLoginCacheDTO loginUser) {
//        // step 1: 组装调用接口参数
//        OrderContractSignReq request = new OrderContractSignReq();
//        request.setSysUser(loginUser);
//
//        OrderSettleSignReq orderSettleSignReq = new OrderSettleSignReq();
//        OrderContractFileDO contractText = order.getContractText();
//        orderSettleSignReq.setContractTemplateId(Optional.ofNullable(contractText.getTemplateId()).orElse(0L));
//        orderSettleSignReq.setContractName(Optional.ofNullable(contractText.getContractName()).orElse(""));
//        orderSettleSignReq.setContractUrl(Optional.ofNullable(contractText.getContractUrl()).orElse(""));
//        orderSettleSignReq.setVendorMemberId(order.getVendorMemberId());
//        request.setRequest(orderSettleSignReq);
//
//        // step 2: 调用
//        WrapperResp<SignCreateResp> signCreateResponseWrapperResp = contractSignContractFeign.srmOrderSign(request);
//        WrapperUtil.throwWhenFail(signCreateResponseWrapperResp);
//
//        return signCreateResponseWrapperResp.getData();
//    }
//
//    @Override
//    public SignCreateResp b2bOrderContractSign(OrderDO order, UserLoginCacheDTO loginUser) {
//        // step 1: 组装调用接口参数
//        OrderContractSignReq request = new OrderContractSignReq();
//        // b2b订单合同签订登录用户是采购商，这里要替换下
//        request.setSysUser(loginUser);
//
//        OrderSettleSignReq orderSettleSignReq = new OrderSettleSignReq();
//        OrderContractFileDO contractText = order.getContractText();
//        orderSettleSignReq.setContractTemplateId(Optional.ofNullable(contractText.getTemplateId()).orElse(0L));
//        orderSettleSignReq.setContractName(Optional.ofNullable(contractText.getContractName()).orElse(""));
//        orderSettleSignReq.setContractUrl(Optional.ofNullable(contractText.getContractUrl()).orElse(""));
//        orderSettleSignReq.setVendorMemberId(order.getVendorMemberId());
//        request.setRequest(orderSettleSignReq);
//
//        // step 2: 调用
//        WrapperResp<SignCreateResp> signCreateResponseWrapperResp = contractSignContractFeign.srmOrderSign(request);
//        WrapperUtil.throwWhenFail(signCreateResponseWrapperResp);
//
//        return signCreateResponseWrapperResp.getData();
//    }
}
