package com.ssy.lingxi.order.serviceImpl.base;

import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.CollectionPageUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.order.entity.PlatformPaymentDO;
import com.ssy.lingxi.order.entity.PlatformPaymentMemberDO;
import com.ssy.lingxi.order.model.dto.OrderMemberQueryDTO;
import com.ssy.lingxi.order.model.req.basic.OrderMemberIdAndRoleIdReq;
import com.ssy.lingxi.order.repository.PlatformPaymentMemberRepository;
import com.ssy.lingxi.order.service.base.IBasePlatformPaymentMemberService;
import com.ssy.lingxi.order.service.feign.IMemberFeignService;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 平台后台 - 会员支付策略配置 - 关联的会员相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-11
 */
@Service
public class BasePlatformPaymentMemberServiceImpl implements IBasePlatformPaymentMemberService {
    @Resource
    private PlatformPaymentMemberRepository platformPaymentMemberRepository;

    @Resource
    private IMemberFeignService memberFeignService;

    /**
     * 检查、保存会员支付策略关联的会员
     *
     * @param platformPayment 会员支付策略
     * @param members         会员列表
     * @param isCreate        是否新增，true-新增，false-更新
     */
    @Override
    public void checkMembers(PlatformPaymentDO platformPayment, List<OrderMemberIdAndRoleIdReq> members, boolean isCreate) {
        //Step 1: 校验会员是否有重复
        if(!CollectionUtils.isEmpty(members) && members.size() != members.stream().distinct().count()) {
            throw new BusinessException(ResponseCodeEnum.ORDER_MEMBERS_DUPLICATED);
        }

        //Step 2: 如果是更新，删除完再新增
        if(!isCreate) {
            platformPaymentMemberRepository.deleteByPayment(platformPayment);
        }

        //Step 3: 新增
        if(platformPayment.getAllMembers()) {
            platformPayment.setMembers(new HashSet<>());
            return;
        }

        List<PlatformPaymentMemberDO> paymentMembers = members.stream().map(member -> {
            PlatformPaymentMemberDO paymentMember = new PlatformPaymentMemberDO();
            paymentMember.setMemberId(member.getMemberId());
            paymentMember.setRoleId(member.getRoleId());
            paymentMember.setPayment(platformPayment);
            return paymentMember;
        }).collect(Collectors.toList());

        platformPaymentMemberRepository.saveAll(paymentMembers);

        //Step 4: 设置关联，调用方要保存PlatformPaymentDO
        platformPayment.setMembers(new HashSet<>(paymentMembers));
    }

    /**
     * 分页查询会员支付策略关联的会员列表
     *
     * @param platformPayment 会员支付策略
     * @param name            会员名称
     * @param current         当前页
     * @param pageSize        每页行数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderMemberQueryDTO> pageMembers(PlatformPaymentDO platformPayment, String name, int current, int pageSize) {
        if(platformPayment.getAllMembers()) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        List<PlatformPaymentMemberDO> members = platformPaymentMemberRepository.findByPayment(platformPayment, Sort.by("id").ascending());
        if(CollectionUtils.isEmpty(members)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        //从会员服务查询
        List<MemberFeignReq> feignList = members.stream().map(member -> {
            MemberFeignReq feignVO = new MemberFeignReq();
            feignVO.setMemberId(member.getMemberId());
            feignVO.setRoleId(member.getRoleId());
            return feignVO;
        }).collect(Collectors.toList());

        List<OrderMemberQueryDTO> feignResult = memberFeignService.findPlatformMembers(name, feignList);

        if(CollectionUtils.isEmpty(feignResult)) {
            return new PageDataResp<>(0L, new ArrayList<>());
        }

        return new PageDataResp<>((long) feignResult.size(), CollectionPageUtil.pageList(feignResult, current, pageSize));
    }
}
