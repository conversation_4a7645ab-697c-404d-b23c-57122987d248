package com.ssy.lingxi.order.serviceImpl.feign;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.*;
import com.ssy.lingxi.component.base.enums.manage.ShopEnvironmentEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPaymentParameterEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.order.entity.SubOrderPaymentDO;
import com.ssy.lingxi.order.enums.OrderPaymentCallbackStatusEnum;
import com.ssy.lingxi.order.model.bo.PaymentParameterBO;
import com.ssy.lingxi.order.model.dto.PayAmountDTO;
import com.ssy.lingxi.order.service.feign.IPayFeignService;
import com.ssy.lingxi.pay.api.enums.CcbB2bPayResultEnum;
import com.ssy.lingxi.pay.api.enums.EAccountPayChannelEnum;
import com.ssy.lingxi.pay.api.enums.PayChannelEnum;
import com.ssy.lingxi.pay.api.enums.ServiceTypeEnum;
import com.ssy.lingxi.pay.api.feign.*;
import com.ssy.lingxi.pay.api.model.req.*;
import com.ssy.lingxi.pay.api.model.req.aliPay.AliPayComputerReq;
import com.ssy.lingxi.pay.api.model.req.aliPay.AliPayRefundReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.*;
import com.ssy.lingxi.pay.api.model.req.ccb.*;
import com.ssy.lingxi.pay.api.model.req.eAccount.CacheParamsReq;
import com.ssy.lingxi.pay.api.model.req.eAccount.EAccountOrderPayReq;
import com.ssy.lingxi.pay.api.model.req.eAccount.EAccountRefundReq;
import com.ssy.lingxi.pay.api.model.req.eAccount.ReceiveReq;
import com.ssy.lingxi.pay.api.model.resp.CreditFeignDetailResp;
import com.ssy.lingxi.pay.api.model.resp.CreditPayResponseResp;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.AccountPayChannelResultResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 支付服务OpenFeign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-18
 */
@Slf4j
@Service
public class PayFeignServiceImpl implements IPayFeignService {

    @Resource
    private IWeChatPayFeign weChatPayFeign;

    @Resource
    private IAliPayFeign aliPayFeign;

    @Resource
    private IAssetAccountFeign assetAccountFeign;

    @Resource
    private ICreditFeign creditFeign;

    @Resource
    private IAllInPayFeign allInPayFeign;

    @Resource
    private ICcbPayFeign ccbPayFeign;

    @Resource
    private IRedisUtils redisUtils;


    /**
     * 缓存通联支付参数
     *
     * @param parameters 支付参数
     */
    @Async
    @Override
    public void cacheAllInPayParameters(List<PaymentParameterBO> parameters) {
        if(CollectionUtils.isEmpty(parameters)) {
            return;
        }

        List<PaymentParameterBO> cacheList = new ArrayList<>();
        for (OrderPaymentParameterEnum allInPayParameter : OrderPaymentParameterEnum.findAllInPayParameters()) {
            PaymentParameterBO parameterBO = parameters.stream().filter(p -> p.getCode().equals(allInPayParameter.getCode()) && StringUtils.hasLength(p.getValue())).findFirst().orElse(null);
            if(parameterBO != null && cacheList.stream().noneMatch(cache -> cache.getCode().equals(parameterBO.getCode()))) {
                PaymentParameterBO p = new PaymentParameterBO();
                p.setCode(parameterBO.getCode());
                p.setValue(parameterBO.getValue());
                cacheList.add(p);
            }
        }

        if(CollectionUtils.isEmpty(cacheList)) {
            return;
        }

        List<CacheParamsReq> cacheParamsReq = cacheList.stream().map(parameter -> {
            CacheParamsReq request = new CacheParamsReq();
            request.setCode(parameter.getCode());
            request.setValue(parameter.getValue());
            return request;
        }).collect(Collectors.toList());
        try {
            WrapperResp<Void> cacheResult = allInPayFeign.cacheParams(cacheParamsReq);
            if(cacheResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                log.error("缓存通联支付参数错误，code:" + cacheResult.getCode() + ", msg:" + cacheResult.getMessage());
            }
        } catch (Exception e) {
            log.error("缓存通联支付参数错误, msg:" + e.getMessage());
        }
    }

    /**
     * 删除建行支付参数
     */
    @Override
    public void removeCcbPayParameters() {
        redisUtils.keyDel(RedisConstant.REDIS_KEY_CCB_PAY_AUTH, RedisConstant.REDIS_PAY_INDEX);
    }

    /**
     * 微信Web Native支付
     *
     * @param loginSource       用户登录来源（用于标识Web、App支付）
     * @param recipientMemberId 收款方会员Id
     * @param recipientRoleId   收款方会员角色Id
     * @param fundMode          资金归集模式
     * @param tradeNo           生成的微信订单号
     * @param digest            订单摘要
     * @param payAmount         支付金额
     * @param attach            透传参数
     * @param merchantId        微信商户号
     * @param appId             微信AppId
     * @param apiKey            微信ApiKey
     * @param shopEnvironment   商城适用环境  ShopEnvironmentEnum.class
     * @return 二维码链接
     */
    @Override
    public String weChatPay(Integer loginSource, Long recipientMemberId, Long recipientRoleId, Integer fundMode, String tradeNo, String digest, BigDecimal payAmount, String attach, String merchantId, String appId, String apiKey, Integer shopEnvironment) {
        MobilePayReq payRequest = new MobilePayReq();
        payRequest.setMemberId(recipientMemberId);
        payRequest.setMemberRoleId(recipientRoleId);
        //微信订单号
        payRequest.setOrderCode(tradeNo);
        //透传参数
        payRequest.setAttach(attach);
        payRequest.setPayMoney(payAmount.setScale(2, RoundingMode.HALF_UP));
        payRequest.setServiceType(ServiceTypeEnum.Order_Pay.getCode());
        payRequest.setRemark(StringUtils.hasLength(digest) ? digest : "");
        payRequest.setPayType(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? 1 : 2);
        payRequest.setPayChannel(loginSource.equals(SystemSourceEnum.BUSINESS_WEB.getCode()) || loginSource.equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode()) ? 1 : 2);
        if(ShopEnvironmentEnum.H5.getCode().equals(shopEnvironment)){
            payRequest.setPayChannel(PayChannelEnum.JsApi.getCode());
        }
        payRequest.setMerchantId(merchantId);
        payRequest.setAppId(appId);
        payRequest.setApiKey(apiKey);
        WrapperResp<String> result = weChatPayFeign.orderPay(payRequest);
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 微信小程序支付
     *
     * @param recipientMemberId 收款方会员Id
     * @param recipientRoleId   收款方会员角色Id
     * @param fundMode          资金归集模式
     * @param tradeNo           订单号
     * @param digest            订单摘要
     * @param payAmount         支付金额
     * @param attach            透传参数
     * @param merchantId        微信商户号
     * @param apiKey            微信ApiKey
     * @param appletAppId       微信AppId
     * @param appletAppKey      微信开发者密钥AppKey
     * @param weChatCode        小程序登录凭证
     * @param wechatBrowser    微信浏览器标识
     * @return 二维码链接
     */
    @Override
    public String weChatMiniAppPay(Long recipientMemberId, Long recipientRoleId, Integer fundMode, String tradeNo, String digest, BigDecimal payAmount, String attach, String merchantId, String apiKey, String appletAppId, String appletAppKey, String weChatCode, Integer wechatBrowser) {
        MobilePayReq payRequest = new MobilePayReq();
        payRequest.setMemberId(recipientMemberId);
        payRequest.setMemberRoleId(recipientRoleId);
        //微信订单号
        payRequest.setOrderCode(tradeNo);
        //透传参数
        payRequest.setAttach(attach);
        payRequest.setPayMoney(payAmount.setScale(2, RoundingMode.HALF_UP));
        payRequest.setServiceType(ServiceTypeEnum.Order_Pay.getCode());
        payRequest.setRemark(StringUtils.hasLength(digest) ? digest : "");
        payRequest.setPayType(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? 1 : 2);
        // 获取微信H5内部支付标识
        log.info("微信H5内部支付标识：{}", wechatBrowser);
        if (CommonBooleanEnum.YES.getCode().equals(wechatBrowser)) {
            payRequest.setPayChannel(PayChannelEnum.JsApi.getCode());
        } else {
            payRequest.setPayChannel(PayChannelEnum.Applet.getCode());
        }
        payRequest.setWechatBrowser(wechatBrowser);
        payRequest.setMerchantId(merchantId);
        payRequest.setApiKey(apiKey);
        payRequest.setAppletAppId(appletAppId);
        payRequest.setAppletAppKey(appletAppKey);
        payRequest.setJsCode(weChatCode);
        WrapperResp<String> result = weChatPayFeign.orderPay(payRequest);
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 微信退款
     *
     * @param recipientMemberId 收款方会员Id
     * @param recipientRoleId   收款方会员角色Id
     * @param fundMode          资金归集模式
     * @param tradeNo           支付单号
     * @param payAmount         订单支付金额
     * @param refundNo          退款单号
     * @param refundAmount      退款金额
     * @param merchantId        微信商户Id
     * @param appId             微信AppId
     * @param apiKey            微信ApiKey
     * @param keyPath           微信退款证书路径
     */
    @Override
    public void weChatRefund(Long recipientMemberId, Long recipientRoleId, Integer fundMode, String tradeNo, BigDecimal payAmount, String refundNo, BigDecimal refundAmount, String merchantId, String appId, String apiKey, String keyPath) {
        WeChatRefundReq request = new WeChatRefundReq();
        request.setMemberId(recipientMemberId);
        request.setMemberRoleId(recipientRoleId);
        request.setOutTradeNo(tradeNo);
        request.setPayMoney(payAmount);
        request.setOutRefundNo(refundNo);
        request.setRefundMoney(refundAmount);
        request.setPayType(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? 1 : 2);
        request.setMerchantId(merchantId);
        request.setAppId(appId);
        request.setApiKey(apiKey);
        request.setKeyPath(keyPath);

        WrapperUtil.throwWhenFailAndLog(weChatPayFeign.refund(request), log);
    }

    /**
     * 微信Web Native支付
     *
     * @param loginSource       用户登录来源（用于标识Web、App支付）
     * @param recipientMemberId 收款方会员Id
     * @param recipientRoleId   收款方会员角色Id
     * @param fundMode          资金归集模式
     * @param tradeNo           订单号
     * @param digest            订单摘要
     * @param payAmount         支付金额
     * @param attach            透传参数
     * @param appId             支付宝AppId
     * @param publicKey         支付宝publicKey
     * @param privateKey        支付宝privateKey
     * @param appAuthToken      支付宝第三方授权Token
     * @param shopEnvironment   商城适用环境  ShopEnvironmentEnum.class
     * @return 二维码链接
     */
    @Override
    public String aliPay(Integer loginSource, Long recipientMemberId, Long recipientRoleId, Integer fundMode, String tradeNo, String digest, BigDecimal payAmount, String attach, String appId, String publicKey, String privateKey, String appAuthToken, Integer shopEnvironment) {
        AliPayComputerReq payRequest = new AliPayComputerReq();
        payRequest.setMemberId(recipientMemberId);
        payRequest.setMemberRoleId(recipientRoleId);
        //支付宝订单号
        payRequest.setOrderCode(tradeNo);
        //透传参数
        payRequest.setBody(attach);
        payRequest.setPayMoney(payAmount.setScale(2, RoundingMode.HALF_UP));
        payRequest.setServiceType(ServiceTypeEnum.Order_Pay.getCode());
        payRequest.setRemark(StringUtils.hasLength(digest) ? digest : "");
        payRequest.setPayType(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? 1 : 2);
        //根据登录来源转换支付渠道
        payRequest.setPayChannel(loginSource.equals(SystemSourceEnum.BUSINESS_WEB.getCode()) || loginSource.equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode()) ? 1 : 2);
        if(ShopEnvironmentEnum.H5.getCode().equals(shopEnvironment)){
            payRequest.setPayChannel(PayChannelEnum.JsApi.getCode());
        }
        payRequest.setAppId(appId);
        payRequest.setAlipayPublicKey(publicKey);
        payRequest.setAppPrivateKey(privateKey);
        payRequest.setAppAuthToken(appAuthToken);
        WrapperResp<String> result = aliPayFeign.orderPay(payRequest);
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 支付宝退款
     *
     * @param vendorMemberId 供应会员Id
     * @param vendorRoleId   供应会员角色id
     * @param fundMode       资金归集模式
     * @param tradeNo        交易单号
     * @param refundNo       退款单号
     * @param refundAmount   退款金额
     * @param appId          支付宝AppId
     * @param publicKey      支付宝公钥
     * @param privateKey     支付宝私钥
     * @param appAuthToken   支付宝第三方授权Token
     */
    @Override
    public void aliPayRefund(Long vendorMemberId, Long vendorRoleId, Integer fundMode, String tradeNo, String refundNo, BigDecimal refundAmount, String appId, String publicKey, String privateKey, String appAuthToken) {
        AliPayRefundReq request = new AliPayRefundReq();
        request.setMemberId(vendorMemberId);
        request.setMemberRoleId(vendorRoleId);
        request.setTransactionId(tradeNo);
        request.setOutRefundNo(refundNo);
        request.setRefundMoney(refundAmount);
        request.setRefundReason("");
        request.setPayType(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? 1 : 2);
        request.setAppId(appId);
        request.setPublicKey(publicKey);
        request.setPrivateKey(privateKey);
        request.setAppAuthToken(appAuthToken);
        WrapperUtil.throwWhenFailAndLog(aliPayFeign.refund(request), log);
    }

    /**
     * 余额支付
     *
     * @param buyerMemberId     采购会员Id
     * @param buyerRoleId       采购会员角色Id
     * @param recipientMemberId 收款方会员Id
     * @param recipientRoleId   收款方会员角色Id
     * @param fundMode          资金归集模式
     * @param orderNo           订单号
     * @param payAmount         支付金额
     * @param payPassword       Aes加密后的支付密码
     * @return 支付结果
     */
    @Override
    public String balancePay(Long buyerMemberId, Long buyerRoleId, Long recipientMemberId, Long recipientRoleId, Integer fundMode, String orderNo, BigDecimal payAmount, String payPassword) {
        BalancePayReq request = new BalancePayReq();
        request.setMemberId(buyerMemberId);
        request.setMemberRoleId(buyerRoleId);
        request.setParentMemberId(recipientMemberId);
        request.setParentMemberRoleId(recipientRoleId);
        request.setPayType(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? 1 : 2);
        request.setPayMoney(payAmount);
        request.setOrderCode(orderNo);
        request.setRemark("");
        request.setPayPassword(payPassword);

        WrapperResp<String> result = assetAccountFeign.balancePay(request);
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 余额支付退款
     * @param tradeNo      交易单号
     * @param refundAmount 退款金额
     * @param remark       备注
     */
    @Override
    public void balancePayRefund(String tradeNo, BigDecimal refundAmount, String remark) {
        BalanceRefundReq request = new BalanceRefundReq();
        request.setPayPlatformTradeCode(tradeNo);
        request.setRemark(remark);
        request.setTradeMoney(refundAmount);
        WrapperUtil.throwWhenFailAndLog(assetAccountFeign.balanceRefund(request), log);
    }

    /**
     * 通联支付
     *
     * @param loginSource     用户登录来源（用于标识Web、App支付）
     * @param orderPayChannel 支付渠道
     * @param buyerMemberId   采购会员Id
     * @param buyerRoleId     采购会员角色Id
     * @param fundMode        资金归集模式
     * @param payAmount       支付总金额
     * @param payAmounts      （如果是会员直接到账）收款方会员Id、角色Id、付款金额列表
     * @param tradeNo         付款交易号
     * @param attach          透传参数
     * @param weChatCode      小程序登录凭证
     * @return 支付结果
     */
    @Override
    public String allInPay(Integer loginSource, OrderPayChannelEnum orderPayChannel, Long buyerMemberId, Long buyerRoleId, Integer fundMode, BigDecimal payAmount, List<PayAmountDTO> payAmounts, String tradeNo, String attach, String weChatCode) {
        EAccountOrderPayReq request = new EAccountOrderPayReq();
        request.setPayMemberId(buyerMemberId);
        request.setPayMemberRoleId(buyerRoleId);
        request.setOrderCode(tradeNo);
        request.setPayMoney(payAmount);

        if(payAmounts.size() == 1) {
            request.setMemberId(payAmounts.get(0).getVendorMemberId());
            request.setMemberRoleId(payAmounts.get(0).getVendorRoleId());
            request.setPayMoney(payAmounts.get(0).getPayAmount());
        } else {
            request.setReceiveList(payAmounts.stream().map(pay -> {
                ReceiveReq receiveReq = new ReceiveReq();
                receiveReq.setMemberId(pay.getVendorMemberId());
                receiveReq.setMemberRoleId(pay.getVendorRoleId());
                receiveReq.setPayMoney(pay.getPayAmount());
                return receiveReq;
            }).collect(Collectors.toList()));
        }

        request.setServiceType(ServiceTypeEnum.Order_Pay.getCode());
        request.setRemark("");
        request.setAttach(attach);
        request.setPayType(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? 1 : 2);

        Integer payChannel = null;
        switch (orderPayChannel) {
            case ALLIN_WECHAT:
                if (StrUtil.isNotEmpty(weChatCode)) {
                    payChannel = EAccountPayChannelEnum.Applet_Wechat.getCode();
                } else if (loginSource.equals(SystemSourceEnum.BUSINESS_MOBILE.getCode())) {
                    payChannel = EAccountPayChannelEnum.APP_Wechat.getCode();
                } else {
                    payChannel = EAccountPayChannelEnum.Platform_Wechat.getCode();
                }
                break;
            case ALLIN_ALI_PAY:
                if (loginSource.equals(SystemSourceEnum.BUSINESS_MOBILE.getCode())) {
                    payChannel = EAccountPayChannelEnum.APP_Alipay.getCode();
                } else {
                    payChannel = EAccountPayChannelEnum.Platform_Alipay.getCode();
                }
                break;
            case ALLIN_BALANCE:
                payChannel = EAccountPayChannelEnum.Balance.getCode();
                break;
            case ALLIN_QUICK:
                payChannel = EAccountPayChannelEnum.Quick_Pay.getCode();
                break;
            case ALLIN_UNION:
                payChannel = EAccountPayChannelEnum.GateWay_Pay.getCode();
                break;
        }

        request.setPayChannel(payChannel);
        request.setJsCode(weChatCode);
        log.info("通联支付参数 => " + SerializeUtil.serialize(request));
        WrapperResp<String> result = allInPayFeign.orderPay(request);
        log.info("通联支付接口返回 => code:" + result.getCode() + ", msg:" + result.getMessage());
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 通联支付 退款
     *
     * @param buyerMemberId 采购会员Id（付款方会员Id）
     * @param buyerRoleId   采购会员角色Id（付款方会员角色Id）
     * @param fundMode          资金归集模式
     * @param payAmount         支付金额
     * @param tradeNo           （支付）交易号
     * @param refundNo          退款交易号
     */
    @Override
    public void allInPayRefund(String buyerBizUserId, Long buyerMemberId, Long buyerRoleId, Integer fundMode, BigDecimal payAmount, String tradeNo, String refundNo) {
        EAccountRefundReq refundVO = new EAccountRefundReq();
        refundVO.setMemberId(buyerMemberId);
        refundVO.setMemberRoleId(buyerRoleId);
        refundVO.setOrderCode(refundNo);
        refundVO.setPayCode(tradeNo);
        refundVO.setRefundMoney(payAmount);
        refundVO.setBuyerBizUserId(buyerBizUserId);
        log.info("通联支付退款参数 => " + SerializeUtil.serialize(refundVO));
        WrapperResp<?> result = allInPayFeign.orderRefund(refundVO);
        log.info("通联支付退款结果 => code:" + result.getCode() + ", msg:" + result.getMessage());
        WrapperUtil.throwWhenFail(result);
    }

    /**
     * 建行支付
     *
     * @param buyerMemberName 采购会员名称
     * @param payAmount     支付总金额
     * @param tradeNo       付款交易号
     * @param attach        透传参数
     * @param merchantId    建行支付商户号
     * @param password      建行支付密码
     * @param branchId      建行支付分行代码
     * @param posId         建行支付商户柜台代码
     * @param publicKey     建行支付柜台公钥
     * @param timeoutDateTime   建行支付超时时间，格式为yyyyMMddHHmmss
     * @return 支付结果
     */
    @Override
    public String ccbPay(String buyerMemberName, BigDecimal payAmount, String tradeNo, String attach, String merchantId, String password, String branchId, String posId, String publicKey, String timeoutDateTime) {
        CcbB2bPayReq payVo = new CcbB2bPayReq();
        payVo.setMerchantId(merchantId);
        payVo.setPwd(password);
        payVo.setBranchId(branchId);
        payVo.setPosId(posId);
        payVo.setPublicKey(publicKey);
        payVo.setOrderCode(tradeNo);
        payVo.setPayMent(payAmount);
        payVo.setAttach(attach);
        payVo.setRemark1(buyerMemberName);
        payVo.setRemark2("");
        payVo.setTimeout(timeoutDateTime);
        log.info("建行B2B支付参数 => " + SerializeUtil.serialize(payVo));
        WrapperResp<String> result = ccbPayFeign.b2bPay(payVo);
        log.info("建行B2B支付结果，code:" + result.getCode() + ", msg:" + result.getMessage() + ", data:" + result.getData());
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 建行B2B支付退款
     *
     * @param tradeNo   支付订单号
     * @param refundNo  退款订单号
     * @param payAmount 支付金额
     */
    @Override
    public void ccbPayRefund(String tradeNo, String refundNo, BigDecimal payAmount) {
        B2bRefundReq refundVO = new B2bRefundReq();
        refundVO.setOrderId(tradeNo);
        refundVO.setRefundCode(refundNo);
        refundVO.setMoney(payAmount.toPlainString());
        log.info("建行B2B支付退款参数 => " + SerializeUtil.serialize(refundVO));
        WrapperResp<Boolean> refundResult = ccbPayFeign.b2bRefund(refundVO);
        log.info("建行B2B支付退款结果，code:" + refundResult.getCode() + ", msg:" + refundResult.getMessage() + ", data:" + refundResult.getData());
        WrapperUtil.throwWhenFail(refundResult);
    }

    /**
     * 建行数字人民币支付
     *
     * @param loginSource       用户登录来源（用于标识Web、App支付）
     * @param buyerMemberName   采购会员名称
     * @param payAmount         支付总金额
     * @param tradeNo           付款交易号
     * @param attach            透传参数
     * @param merchantId        建行支付商户号
     * @param branchId          建行支付分行代码
     * @param posId             建行支付商户柜台代码
     * @param publicKey         建行支付柜台公钥
     * @param timeoutDateTime   建行支付超时时间，格式为yyyyMMddHHmmss
     * @param settlementAccount 商户结算账户
     * @param subMerchantId     二级商户号
     * @param returnUrl         退出支付流程时返回商户URL(H5支付才生效，二维码支付不生效)
     * @return 支付结果
     */
    @Override
    public String ccbDigitalPay(Integer loginSource, String buyerMemberName, BigDecimal payAmount, String tradeNo, String attach, String merchantId, String branchId, String posId, String publicKey, String timeoutDateTime, String settlementAccount, String subMerchantId, String returnUrl) {
        CcbDigitalPayReq payVo = new CcbDigitalPayReq();
        payVo.setPayType(loginSource.equals(SystemSourceEnum.BUSINESS_WEB.getCode()) || loginSource.equals(SystemSourceEnum.BUSINESS_MANAGEMENT_PLATFORM.getCode()) ? 1 : 2);
        payVo.setMerchantId(merchantId);
        payVo.setPosId(posId);
        payVo.setBranchId(branchId);
        payVo.setPublicKey(publicKey);
        payVo.setOrderCode(tradeNo);
        payVo.setPayMent(payAmount);
        payVo.setAttach(attach);
        payVo.setRemark1(buyerMemberName);
        payVo.setRemark2("");
        payVo.setTimeout(timeoutDateTime);
        payVo.setSubMerchantId(subMerchantId);
        payVo.setCdtrWltId(settlementAccount);
        payVo.setMrchUrl(StringUtils.hasLength(returnUrl) ? returnUrl : "");
        log.info("建行数字人民币支付参数 => " + SerializeUtil.serialize(payVo));
        WrapperResp<String> result = ccbPayFeign.digitalPay(payVo);
        log.info("建行数字人民币支付结果，code:" + result.getCode() + ", msg:" + result.getMessage() + ", data:" + result.getData());
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 建行数字人民币支付退款
     *
     * @param orderCreateTime 订单创建时间
     * @param tradeNo         支付订单号
     * @param refundNo        退款订单号
     * @param payAmount       支付金额
     */
    @Override
    public void ccbDigitalPayRefund(LocalDateTime orderCreateTime, String tradeNo, String refundNo, BigDecimal payAmount) {
        DigitalRefundReq refundVO = new DigitalRefundReq();
        refundVO.setOrderCode(tradeNo);
        refundVO.setOrderStartTime(orderCreateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        refundVO.setOrderEndTime(orderCreateTime.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        refundVO.setMoney(payAmount);
        refundVO.setRefundSn(refundNo);
        log.info("建行数字人民币退款参数 => " + SerializeUtil.serialize(refundVO));
        WrapperResp<Boolean> refundResult = ccbPayFeign.digitalRefund(refundVO);
        log.info("建行数字人民币支付退款结果，code:" + refundResult.getCode() + ", msg:" + refundResult.getMessage() + ", data:" + refundResult.getData());
        WrapperUtil.throwWhenFail(refundResult);
    }

    /**
     * 查询建行B2B支付订单
     *
     * @param tradeNo    付款交易号
     * @param merchantId 建行支付商户号
     * @param branchId   建行支付分行代码
     * @param posId      建行支付商户柜台代码
     * @param password   建行支付密码
     * @return 查询结果
     */
    @Override
    public OrderPaymentCallbackStatusEnum queryCcbOrders(String tradeNo, String merchantId, String branchId, String posId, String password) {
        CcbB2bPayQueryReq queryVo = new CcbB2bPayQueryReq();
        queryVo.setOrderCode(tradeNo);
        queryVo.setMerchantId(merchantId);
        queryVo.setBranchId(branchId);
        queryVo.setPosId(posId);
        queryVo.setPassword(password);

        log.info("查询建行B2B支付订单 => " + SerializeUtil.serialize(queryVo));
        WrapperResp<String> result = ccbPayFeign.b2bPayStatusQuery(queryVo);
        WrapperUtil.throwWhenFail(result);

        if(result.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            log.error("查询建行B2B支付订单返回错误，code:" + result.getCode() + ", msg:" + result.getMessage());
            //当订单不存在时，认为是“支付失败”
            return OrderPaymentCallbackStatusEnum.FAILED;
        }

        if(StringUtils.hasLength(result.getData())) {
            if(result.getData().equals(CcbB2bPayResultEnum.FAIL.getCode())) {
                return OrderPaymentCallbackStatusEnum.FAILED;
            }

            if(result.getData().equals(CcbB2bPayResultEnum.SUCCESS.getCode())) {
                return OrderPaymentCallbackStatusEnum.SUCCESS;
            }

            if(result.getData().equals(CcbB2bPayResultEnum.BANK_NOT_CONFIRM.getCode())) {
                return OrderPaymentCallbackStatusEnum.PAYING;
            }

            //如果查询为“已退款”或“已经部分退款”，判定为“支付失败”
            return OrderPaymentCallbackStatusEnum.FAILED;
        }

        //如果查询返回Null，判定为“支付失败”
        return OrderPaymentCallbackStatusEnum.FAILED;
    }

    /**
     * “会员支付参数”配置时，如果会员配置了授信额度支付，通知支付服务创建授信账户
     *
     * @param memberId 会员Id
     * @param roleId   会员角色Id
     */
    @Async
    @Override
    public void notifyCreditAsync(Long memberId, Long roleId) {
        CreditInitReq initVO = new CreditInitReq();
        initVO.setMemberId(memberId);
        initVO.setRoleId(roleId);
        WrapperResp<?> creditResult = creditFeign.init(initVO);
        WrapperUtil.throwWhenFail(creditResult);
    }

    /**
     * 查询会员授信是否存在
     *
     * @param buyerMemberId  采购会员Id
     * @param buyerRoleId    采购会员角色Id
     * @param vendorMemberId 供应会员Id
     * @param vendorRoleId   供应会员角色Id
     * @return true-存在，false-服务异常或不存在
     */
    @Override
    public Boolean findMemberCredit(Long buyerMemberId, Long buyerRoleId, Long vendorMemberId, Long vendorRoleId) {
        try {
            CreditFeignReq feignVO = new CreditFeignReq();
            feignVO.setMemberId(buyerMemberId);
            feignVO.setRoleId(buyerRoleId);
            feignVO.setParentMemberId(vendorMemberId);
            feignVO.setParentMemberRoleId(vendorRoleId);

            WrapperResp<CreditFeignDetailResp> creditResult = creditFeign.findCredit(feignVO);
            return creditResult.getCode() == ResponseCodeEnum.SUCCESS.getCode() && creditResult.getData() != null && creditResult.getData().getIsUsable().equals(EnableDisableStatusEnum.ENABLE.getCode());
        } catch (Exception e) {
            log.error("支付服务查询授信账户错误：" + e.getMessage());
            return false;
        }
    }

    /**
     * 授信支付
     *
     * @param buyerMemberId     采购会员Id
     * @param buyerRoleId       采购会员角色Id
     * @param recipientMemberId 收款方会员Id
     * @param recipientRoleId   收款方会员角色Id
     * @param fundMode          资金归集模式
     * @param orderNo           订单号
     * @param payAmount         支付金额
     * @param payPassword       Aes加密后的支付密码
     * @return 支付结果
     */
    @Override
    public String creditPay(Long buyerMemberId, Long buyerRoleId, Long recipientMemberId, Long recipientRoleId, Integer fundMode, String orderNo, BigDecimal payAmount, String payPassword) {
        CreditPayReq payVO = new CreditPayReq();
        payVO.setMemberId(buyerMemberId);
        payVO.setRoleId(buyerRoleId);
        payVO.setParentMemberId(recipientMemberId);
        payVO.setParentMemberRoleId(recipientRoleId);
        payVO.setPayMoney(payAmount);
        payVO.setOrderCode(orderNo);
        payVO.setPayPassword(payPassword);
        payVO.setRemark("");
        WrapperResp<CreditPayResponseResp> payResult = creditFeign.pay(payVO);
        WrapperUtil.throwWhenFail(payResult);

        return payResult.getData().getPayCode();
    }

    /**
     * 授信支付退款
     *
     * @param buyerMemberId     采购会员Id
     * @param buyerRoleId       采购会员角色Id
     * @param recipientMemberId 收款方会员Id
     * @param recipientRoleId   收款方会员角色Id
     * @param tradeNo           支付单号
     * @param refundAmount      退款金额
     * @param reason            备注说明
     * @return 支付结果
     */
    @Override
    public String creditPayRefund(Long buyerMemberId, Long buyerRoleId, Long recipientMemberId, Long recipientRoleId, String tradeNo, BigDecimal refundAmount, String reason) {
        CreditRefundReq refundVO = new CreditRefundReq();
        refundVO.setMemberId(buyerMemberId);
        refundVO.setMemberRoleId(buyerRoleId);
        refundVO.setParentMemberId(recipientMemberId);
        refundVO.setParentMemberRoleId(recipientRoleId);
        refundVO.setPayCode(tradeNo);
        refundVO.setRefundAmount(refundAmount);
        refundVO.setRemark(reason);
        WrapperResp<String> result = creditFeign.refund(refundVO);
        WrapperUtil.throwWhenFail(result);
        return result.getData();
    }

    /**
     * 微企付支付
     */
    @Override
    public String weChatPayWqf() {
        return "";
    }

    @Override
    public void unfrozenbalance(Set<Long> paymentIds) {
        UnFrozenAccountBalanceReq unFrozenAccountBalanceReq = new UnFrozenAccountBalanceReq();
        Set<String> paymentIdSet = paymentIds.stream().map(String::valueOf).collect(Collectors.toSet());
        unFrozenAccountBalanceReq.setTradeCode(paymentIdSet);
        assetAccountFeign.unFrozenAccountBalance(unFrozenAccountBalanceReq);
    }

    /**
     * 冻结余额
     *
     * @param buyerMemberId     采购会员Id
     * @param buyerRoleId       采购会员角色Id
     * @param recipientMemberId 收款方会员Id
     * @param recipientRoleId   收款方会员角色Id
     * @param fundMode          资金归集模式
     * @param orderNo           订单号
     * @param payAmount         支付金额
     * @param payPassword       Aes加密后的支付密码
     * @return 支付结果
     */
    @Transactional
    @Override
    public AccountPayChannelResultResp frozenbalance(Long buyerMemberId, Long buyerRoleId, Long recipientMemberId, Long recipientRoleId, Integer fundMode, String orderNo, BigDecimal payAmount, String payPassword, List<OrderPayInfoReq> orderPayInfoReqs, List<Integer> balancePayMethods) {
//        BalancePayReq request = new BalancePayReq();
//        request.setMemberId(buyerMemberId);
//        request.setMemberRoleId(buyerRoleId);
//        request.setParentMemberId(recipientMemberId);
//        request.setParentMemberRoleId(recipientRoleId);
//        request.setPayType(fundMode.equals(FundModeEnum.PLATFORM_EXCHANGE.getCode()) ? 1 : 2);
//        request.setPayMoney(payAmount);
//        request.setOrderCode(orderNo);
//        request.setRemark("");
//        request.setPayPassword(payPassword);

        FrozenAccountBalanceReq frozenAccountBalanceReq = new FrozenAccountBalanceReq();
        //frozenAccountBalanceReq.setLockBalance(payAmount);
        //frozenAccountBalanceReq.setAccountId();
        frozenAccountBalanceReq.setMemberId(buyerMemberId);
        //frozenAccountBalanceReq.setRemark();
        frozenAccountBalanceReq.setPayPlatformTradeCode(orderNo);
        //frozenAccountBalanceReq.setLockMaterialStock(payMaterialStock);
        frozenAccountBalanceReq.setPayPassword(payPassword);
        frozenAccountBalanceReq.setBalancePayMethods(balancePayMethods);
        frozenAccountBalanceReq.setOrderPayInfoReqs(orderPayInfoReqs);
        frozenAccountBalanceReq.setGoldPrice(orderPayInfoReqs.get(0).getGoldPrice());
        WrapperResp<AccountPayChannelResultResp> result = assetAccountFeign.frozenAccountBalance(frozenAccountBalanceReq);
        WrapperUtil.throwWhenFail(result);
        log.info("钱包支付完后的信息："+ JSONObject.toJSONString(result.getData()));
        return result.getData();
    }

}
