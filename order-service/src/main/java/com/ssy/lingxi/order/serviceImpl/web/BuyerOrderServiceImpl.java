package com.ssy.lingxi.order.serviceImpl.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.enums.order.LogisticsStatusEnum;
import com.ssy.lingxi.common.enums.order.OrderDeliveryTypeEnum;
import com.ssy.lingxi.common.enums.order.OrderSourceKindEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.ReportItemResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.manage.ShopTypeEnum;
import com.ssy.lingxi.component.base.enums.member.PaymentTypeEnum;
import com.ssy.lingxi.component.base.enums.member.UserTypeEnum;
import com.ssy.lingxi.component.base.enums.order.*;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.AopProxyUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.redis.service.IRedissonUtils;
import com.ssy.lingxi.component.rest.model.req.eos.CheckPickingNotifyReq;
import com.ssy.lingxi.component.rest.model.req.eos.UpdateSalesOrderGoodsStatus;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.contract.api.model.resp.OrderContractProductResp;
import com.ssy.lingxi.contract.api.model.resp.OrderContractResp;
import com.ssy.lingxi.member.api.feign.IMemberDetailFeign;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.feign.IMemberOrderCommentFeign;
import com.ssy.lingxi.member.api.model.req.MemberOrderCommentReportReq;
import com.ssy.lingxi.member.api.model.req.UserIdFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignCodeRes;
import com.ssy.lingxi.order.api.model.req.OrderPayCallbackFeignReq;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.*;
import com.ssy.lingxi.order.enums.report.PurchaseOrderOperateTypeEnum;
import com.ssy.lingxi.order.model.bo.*;
import com.ssy.lingxi.order.model.dto.*;
import com.ssy.lingxi.order.model.req.basic.*;
import com.ssy.lingxi.order.model.req.buyer.*;
import com.ssy.lingxi.order.model.req.vendor.VendorTransferOrderNewReq;
import com.ssy.lingxi.order.model.resp.basic.*;
import com.ssy.lingxi.order.model.resp.buyer.*;
import com.ssy.lingxi.order.model.resp.report.OrderReportShopResp;
import com.ssy.lingxi.order.repository.*;
import com.ssy.lingxi.order.service.base.*;
import com.ssy.lingxi.order.service.feign.IContractFeignService;
import com.ssy.lingxi.order.service.feign.ILogisticsFeignService;
import com.ssy.lingxi.order.service.feign.IPayFeignService;
import com.ssy.lingxi.order.service.web.*;
import com.ssy.lingxi.order.serviceImpl.feign.PayFeignServiceImpl;
import com.ssy.lingxi.order.util.OrderCommissionMqUtil;
import com.ssy.lingxi.pay.api.enums.PayChannelEnum;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.api.model.req.assetAccount.FrozenAccountBalanceReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.UnFrozenAccountBalanceReq;
import com.ssy.lingxi.product.api.enums.FreightSpaceSingleProductStatusEnum;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.model.req.baitai.SpaceSingleProductStatusReq;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuStockResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 采购订单相关接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-20
 */
@Slf4j
@Service
public class BuyerOrderServiceImpl implements IBuyerOrderService {
    @Resource
    private IBaseCacheService baseCacheService;

    @Resource
    private IBaseOrderService baseOrderService;

    @Resource
    private IBaseOrderAuthService baseOrderAuthService;

    @Resource
    private IBaseOrderTaskService baseOrderTaskService;

    @Resource
    private IOrderCreationService orderCreationService;

    @Resource
    private IBaseOrderProcessService baseOrderProcessService;

    @Resource
    private IBaseOrderPaymentService baseOrderPaymentService;

    @Resource
    private IBaseOrderProductService baseOrderProductService;

    @Resource
    private IBaseOrderDeliveryService baseOrderDeliveryService;

    @Resource
    private IBaseOrderHistoryService baseOrderHistoryService;

    @Resource
    private IBaseOrderScheduleService baseOrderScheduleService;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private BaseOrderTimeParamRepository orderTimeParamRepository;

    @Resource
    private IOrderTimeParamService orderTimeParamService;

    @Resource
    private IBaseOrderConsigneeService baseOrderConsigneeService;

    @Resource
    private IBaseOrderPromotionService baseOrderPromotionService;

    @Resource
    private OrderInvoiceRepository orderInvoiceRepository;

    @Resource
    private SellDeliveryGoodsRepository sellDeliveryGoodsRepository;

    @Resource
    private IBaseOrderDeductionService baseOrderDeductionService;

    @Resource
    private ILogisticsFeignService logisticsFeignService;

    @Resource
    private IDeliveryPlanService deliveryPlanService;

    @Resource
    private OrderProductRepository orderProductRepository;

    @Resource
    private ICommodityFeign commodityFeign;

//    @Resource
//    private IBaseOrderContractTextService baseOrderContractTextService;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private IRedissonUtils redissonUtils;

    @Resource
    private IOrderChangeService orderChangeService;

    @Resource
    private IContractFeignService contractFeignService;

    @Resource
    private IMemberOrderCommentFeign memberOrderCommentFeign;

    @Resource
    private IMemberDetailFeign memberDetailFeign;

    @Resource
    private IOrderCreationService orderCreationServiceProxy;

    @Resource
    private EosApiService eosApiService;

    @Resource
    private OrderService orderServiceProxy;

    @Resource
    private OrderService orderService;

    @Resource
    private OrderLogisticsRepository orderLogisticsDORepository;
    @Resource
    private IAssetAccountFeign assetAccountFeign;
    @Resource
    private OrderPaymentRepository orderPaymentRepository;
    @Resource
    private SubOrderPaymentRepository subOrderPaymentRepository;
    @Resource
    private IPayFeignService payFeignService;
    @Resource
    private IMemberFeign memberFeign;
    @Resource
    private OrderCommissionMqUtil orderCommissionMqUtil;
    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;


    /**
     * （待分配订单页面） 获取前端页面下拉框列表
     *
     * @param loginUser 登录用户
     * @return 查询结果
     */
    @Override
    public List<DropdownItemResp> getTakePageItems(UserLoginCacheDTO loginUser) {
        return baseOrderService.listOrderTypes();
    }

    /**
     * “待分配订单” - 查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BaseBuyerOrderQueryResp> pageToTakeOrders(UserLoginCacheDTO loginUser, OrderPageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerToTakeOrders(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BaseBuyerOrderQueryResp queryVO = new BaseBuyerOrderQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setVendorMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            if (OrderDeliveryTypeEnum.SELF_PICKUP.getCode().equals(order.getDeliveryType())) {
                queryVO.setDeliverAddress(baseOrderConsigneeService.pickupConsigneeToString(order.getOrderPickup()));
            }else {
                queryVO.setDeliverAddress(baseOrderConsigneeService.orderConsigneeToString(order.getConsignee()));
            }
            queryVO.setQuoteNo(order.getQuoteNo());
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待分配订单” - 订单详情
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public BuyerOrderDetailResp getToTakeOrderDetail(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        return baseOrderService.getBuyerOrderDetail(order);
    }

    /**
     * “待分配订单” - 领取订单
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 操作结果
     */
    @Override
    public Void takeOrder(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //判断是否已经被领取
        if (!baseOrderService.canBuyerTakeOrder(order)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_HAS_BEEN_TAKEN);
        }

        order.setBuyerUserId(loginUser.getUserId());
        orderRepository.saveAndFlush(order);
        return null;
    }

    /**
     * “待分配订单” - 批量领取订单
     *
     * @param loginUser 登录用户
     * @param orderIds  订单Id列表
     * @return 操作结果
     */
    @Override
    public Void batchTakeOrders(UserLoginCacheDTO loginUser, List<OrderIdReq> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return null;
        }

        List<OrderDO> orders = orderRepository.findAllById(orderIds.stream().map(OrderIdReq::getOrderId).collect(Collectors.toList()));
        if (orders.size() != orderIds.size() || orders.stream().anyMatch(order -> !order.getBuyerMemberId().equals(loginUser.getMemberId()) && !order.getBuyerRoleId().equals(loginUser.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        if (orders.stream().anyMatch(order -> !baseOrderService.canBuyerTakeOrder(order))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_HAS_BEEN_TAKEN);
        }

        orders.forEach(order -> order.setBuyerUserId(loginUser.getUserId()));
        orderRepository.saveAll(orders);
        return null;
    }

    /**
     * 获取前端页面下拉框列表
     *
     * @param loginUser 登录用户
     * @return 查询结果
     */
    @Override
    public PageItemResp getPageItems(UserLoginCacheDTO loginUser) {
        PageItemResp itemVO = new PageItemResp();
        Stream<BuyerInnerStatusEnum> statusEnumStream = Stream.of(BuyerInnerStatusEnum.TO_PRODUCE, BuyerInnerStatusEnum.TO_VALIDATE_PRODUCE,
                BuyerInnerStatusEnum.TO_SETTLE, BuyerInnerStatusEnum.TO_PICKING, BuyerInnerStatusEnum.TO_PICKING_CHECK, BuyerInnerStatusEnum.TO_VENDOR_PICKING_CHECK,
                BuyerInnerStatusEnum.PICKING_ERROR, BuyerInnerStatusEnum.ACCOMPLISHED, BuyerInnerStatusEnum.CANCELLED);
        List<DropdownItemResp> dropdownItemRespList = statusEnumStream.map(e -> new DropdownItemResp(e.getCode(), BuyerInnerStatusEnum.getNameByCode(e.getCode()))).collect(Collectors.toList());
        itemVO.setInnerStatus(dropdownItemRespList);
        itemVO.setOuterStatus(OrderOuterStatusEnum.toDropdownList());
        itemVO.setOrderTypes(baseOrderService.listOrderTypes());
        return itemVO;
    }

    /**
     * 分页查询订单
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BuyerOrderQueryResp> pageOrders(UserLoginCacheDTO loginUser, OrderManagePageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getMemberId(), pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), pageVO.getInnerStatus(), pageVO.getOuterStatus(), OrderUpdateStepEnum.ALL.getStep(), pageVO.getCurrent(), pageVO.getPageSize());
        UserIdFeignReq userIdFeignReq = new UserIdFeignReq();
        userIdFeignReq.setUserId(loginUser.getUserId());
        WrapperResp<Boolean> orderAuth = memberDetailFeign.hasOrderAuth(userIdFeignReq);

        CommonIdListReq commonIdListReq = new CommonIdListReq();
        commonIdListReq.setIdList(pageList.getContent().stream().map(OrderDO::getVendorMemberId).distinct().collect(Collectors.toList()));
        WrapperResp<List<MemberFeignCodeRes>> memberFeignList = memberFeign.findMemberById(commonIdListReq);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerOrderQueryResp queryVO = new BuyerOrderQueryResp();
            queryVO.setShopId(order.getShopId());
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setVendorMemberName(order.getVendorMemberName());
            queryVO.setBuyerMemberName(order.getBuyerMemberName());
            String userType = UserTypeEnum.ADMIN.getCode().equals(order.getUserType()) ? "主账号" : "子账号";
            queryVO.setBuyerAccount(order.getBuyerAccount() + "(" + userType + ")");
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            if (OrderDeliveryTypeEnum.SELF_PICKUP.getCode().equals(order.getDeliveryType())) {
                queryVO.setDeliverAddress(baseOrderConsigneeService.pickupConsigneeToString(order.getOrderPickup()));
            }else {
                queryVO.setDeliverAddress(baseOrderConsigneeService.orderConsigneeToString(order.getConsignee()));
            }
            queryVO.setQuoteNo(order.getQuoteNo());
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            queryVO.setTotalWeight(order.getTotalWeight());
            //按钮显示
            queryVO.setShowAfterSales(baseOrderService.canAfterSaleOrder(order));
            queryVO.setShowComment(baseOrderService.canBuyerCommentOrder(order));
            queryVO.setShowCancel(baseOrderService.canBuyerCancelOrderNew(order));
            queryVO.setShowModifyDeliverTime(baseOrderService.canBuyerModifyDeliverTime(order));
            queryVO.setShowRefund(baseOrderService.canBuyerRefundOrder(order));
            queryVO.setShowDelete(baseOrderService.canBuyerDeleteOrder(order));
            queryVO.setShowChange(baseOrderService.canBuyerChangeOrder(order));
            if (WrapperUtil.isOk(orderAuth) && !orderAuth.getData()) {
                queryVO.setAmount("***");
            }
            if (WrapperUtil.isOk(memberFeignList) && CollUtil.isNotEmpty(memberFeignList.getData())) {
                Optional<MemberFeignCodeRes> memberFeignCodeRes = memberFeignList.getData().stream().filter(s -> s.getId().equals(order.getVendorMemberId())).findFirst();
                memberFeignCodeRes.ifPresent(feignCodeRes -> queryVO.setVendorMemberName(feignCodeRes.getName()));
            }
            queryVO.setTotalProductCount(order.getTotalProductCount());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 订单退款
     *
     * @param loginUser 订单退款
     * @param idVO      接口参数
     * @return 退款结果
     */
    @Override
    public Void orderRefund(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        // 获取锁
        RLock lock = redissonUtils.getLock(RedisConstant.REDIS_KEY_ORDER_STATUS + order.getId());
        try {
            // 尝试加锁
            if (!redissonUtils.tryAndLock(lock, -1, 10, TimeUnit.SECONDS)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_OPERATION_IN_PROGRESS_PLEASE_TRY_AGAIN_LATER);
            }

            // 执行业务流程
            if (baseOrderPaymentService.groupOrderRefund(order)) {
                //订单内、外流转记录
                baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.REFUND, order.getBuyerInnerStatus(), "");
                baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.REFUND, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

                //将订单未发货的数量返回库存
                baseOrderService.resumeInventory(order);
            }
        } finally {
            redissonUtils.unlock(lock);
        }

        return null;
    }

    /**
     * 分页查询订单 - 订单删除
     *
     * @param loginUser 用户信息
     * @param idVO      接口参数
     * @return 操作结果
     */
    @Override
    public Void pageOrderDelete(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        // 获取锁
        RLock lock = redissonUtils.getLock(RedisConstant.REDIS_KEY_ORDER_STATUS + idVO.getOrderId());
        try {
            // 尝试加锁
            if (!redissonUtils.tryAndLock(lock, -1, 10, TimeUnit.SECONDS)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_OPERATION_IN_PROGRESS_PLEASE_TRY_AGAIN_LATER);
            }

            // 执行业务流程
            OrderDO order = orderRepository.findById(idVO.getOrderId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST));
            orderRepository.delete(order);
        } finally {
            redissonUtils.unlock(lock);
        }

        return null;
    }

    /**
     * 订单详情
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public BuyerOrderDetailResp getOrderDetail(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        if (!order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_WRONG_ROLE);
        }

        return baseOrderService.getBuyerOrderDetail(order);
    }

    /**
     * 采购订单详情 - 申请开票
     *
     * @param loginUser 登录用户
     * @param invoiceVO 接口参数
     * @return 操作结果
     */
    @Transactional
    @Override
    public Void applyInvoice(UserLoginCacheDTO loginUser, BuyerApplyInvoiceReq invoiceVO) {
        OrderDO order = orderRepository.findById(invoiceVO.getOrderId()).orElse(null);
        if (order == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        orderInvoiceRepository.deleteByOrder(order);

        OrderInvoiceDO invoice = new OrderInvoiceDO();
        invoice.setInvoiceId(invoiceVO.getInvoiceId());
        invoice.setOrder(order);
        invoice.setInvoiceKind(invoiceVO.getInvoiceKind());
        invoice.setInvoiceType(invoiceVO.getInvoiceType());
        invoice.setTitle(invoiceVO.getTitle());
        invoice.setTaxNo(StringUtils.hasLength(invoiceVO.getTaxNo()) ? invoiceVO.getTaxNo() : "");
        invoice.setBank(StringUtils.hasLength(invoiceVO.getBank()) ? invoiceVO.getBank().trim() : "");
        invoice.setAccount(StringUtils.hasLength(invoiceVO.getAccount()) ? invoiceVO.getAccount().trim() : "");
        invoice.setAddress(StringUtils.hasLength(invoiceVO.getAddress()) ? invoiceVO.getAddress().trim() : "");
        invoice.setPhone(StringUtils.hasLength(invoiceVO.getPhone()) ? invoiceVO.getPhone().trim() : "");
        invoice.setDefaultInvoice(true);

        order.setInvoice(invoice);
        orderRepository.save(order);

        return null;
    }

    /**
     * 根据供应商会员信息和商城id获取预约时长和配置配送时间段
     *
     * @param loginUser       登录用户
     * @param deliveryDateReq 接口参数
     * @return 查询结果
     */
    @Override
    public OrderDeliveryDateResp findBuyerDeliveryDate(UserLoginCacheDTO loginUser, DeliveryDateReq deliveryDateReq) {
        return orderTimeParamService.findBuyerDeliveryDate(deliveryDateReq.getVendorMemberId(), deliveryDateReq.getVendorRoleId(), deliveryDateReq.getShopId());
    }

    /**
     * 查看订单送货时间
     *
     * @param loginUser 登录用户
     * @param timeVO    接口参数
     * @return 查询结果
     */
    @Override
    public OrderDeliverTimeDetailResp getDeliveryTime(UserLoginCacheDTO loginUser, BuyerOrderDeliveryTimeReq timeVO) {
        OrderDO order = orderRepository.findById(timeVO.getOrderId()).orElse(null);
        if (order == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //订单送货时间段
        OrderDeliverTimeDetailResp resp = new OrderDeliverTimeDetailResp();
        String deliverPeriod = order.getDeliverPeriod();
        resp.setDeliverDate(deliverPeriod);

        if (StrUtil.isNotEmpty(deliverPeriod)) {
            String deliveryTime = deliverPeriod.concat(" ").concat("00:00");
            boolean isPeriod = deliveryTime.split(" ")[1].contains("-");
            if (isPeriod) {
                resp.setDeliverDate(deliveryTime.split(" ")[0]);
                resp.setDeliverPeriod(deliveryTime.split(" ")[1]);
            }
        }

        //配置的时间参数
        List<BaseOrderTimeParamDO> timeParamList = orderTimeParamRepository.findAllByMemberIdAndMemberRoleIdAndShopIdAndStatus(order.getVendorMemberId(), order.getVendorRoleId(), timeVO.getShopId(), EnableDisableStatusEnum.ENABLE.getCode());
        //送货预约时长
        BaseOrderTimeParamDO dayParam = timeParamList.stream().filter(dateParam -> OrderTimeParamEnum.DELIVERY_APPOINTMENT_DAY.getCode().equals(dateParam.getParamType())).findFirst().orElse(null);
        if (dayParam != null) {
            resp.setAppointmentDay(true);
            resp.setDays(dayParam.getDays());
        }

        //配送时间段
        List<DeliveryTimeResp> timeParam = timeParamList.stream().filter(dateParam -> OrderTimeParamEnum.DELIVERY_TIME.getCode().equals(dateParam.getParamType()))
                .map(time -> new DeliveryTimeResp(time.getStartTime(), time.getEndTime())).sorted(Comparator.comparing(DeliveryTimeResp::getStartTime, Comparator.nullsLast(String::compareTo))).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(timeParam)) {
            resp.setDeliveryTime(true);
            resp.setParamList(timeParam);
        }

        return resp;
    }

    /**
     * 订单查询导出
     *
     * @param accessToken 登录token
     * @param pageVO      接口参数
     * @param response    响应体
     */
    @Override
    public void exportOrders(HttpServletResponse response, OrderManagePageDataReq pageVO, String accessToken, UserLoginCacheDTO loginUser) {
        //获取采购订单导出数据
        List<OrderDO> orderList = baseOrderService.exportBuyerOrders(loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), pageVO.getInnerStatus() == null ? null : Collections.singletonList(pageVO.getInnerStatus()), pageVO.getOuterStatus());

        List<Map<String, Object>> rows = orderList.stream().map(order -> {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(PurchaseOrderExportFileEnum.COLUMN_ORDER_NO.getMessage(), order.getOrderNo());
            map.put(PurchaseOrderExportFileEnum.COLUMN_DIGEST.getMessage(), order.getDigest());
            map.put(PurchaseOrderExportFileEnum.COLUMN_VENDOR_MEMBER_NAME.getMessage(), order.getVendorMemberName());
            map.put(PurchaseOrderExportFileEnum.COLUMN_CREATE_TIME.getMessage(), order.getCreateTime());
            map.put(PurchaseOrderExportFileEnum.COLUMN_TOTAL_AMOUNT.getMessage(), NumberUtil.formatAmount(order.getTotalAmount()));
            map.put(PurchaseOrderExportFileEnum.COLUMN_ORDER_TYPE.getMessage(), OrderTypeEnum.getNameByCode(order.getOrderType()));
            map.put(PurchaseOrderExportFileEnum.COLUMN_OUTER_STATUS.getMessage(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            // 该接口只有在采购商使用，内部状态调整
            map.put(PurchaseOrderExportFileEnum.COLUMN_BUYER_INNER_STATUS.getMessage(), BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            return map;
        }).collect(Collectors.toList());


        ExcelWriter writer = ExcelUtil.getBigWriter();
        CellStyle style = writer.getHeadCellStyle();
        style.setWrapText(true);
        writer.write(rows);

        String fileName = PurchaseOrderExportFileEnum.TABLE_NAME.getMessage() + "-" + DateUtil.date() + ".xls";
        try (ServletOutputStream out = response.getOutputStream()) {
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            writer.flush(out, true);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            writer.close();
        }
    }

    /**
     * 未发货调整订单送货时间
     *
     * @param loginUser 登录用户
     * @param updateVO  接口参数
     * @return 操作结果
     */
    @Transactional
    @Override
    public Void updateDeliveryTime(UserLoginCacheDTO loginUser, BuyerOrderDeliveryTimeUpdateReq updateVO) {
        OrderDO order = orderRepository.findById(updateVO.getOrderId()).orElse(null);
        if (order == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        //更新订单送货时间
        order.setDeliverPeriod(updateVO.getDeliverPeriod());
        orderRepository.saveAndFlush(order);

        //内部流转记录调整理由
        baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.MODIFY_DELIVER_PERIOD, order.getBuyerInnerStatus(), updateVO.getReason());
        return null;
    }

    /**
     * 取消订单
     *
     * @param loginUser 登录用户
     * @param reasonVO  接口参数
     * @return 操作结果
     */
    @Transactional
    @Override
    public Void cancelOrder(UserLoginCacheDTO loginUser, OrderReasonReq reasonVO) {
        // 获取锁
        RLock lock = redissonUtils.getLock(RedisConstant.REDIS_KEY_ORDER_STATUS + reasonVO.getOrderId());
        try {
            // 尝试加锁
            if (!redissonUtils.tryAndLock(lock, -1, 10, TimeUnit.SECONDS)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_OPERATION_IN_PROGRESS_PLEASE_TRY_AGAIN_LATER);
            }

            // 执行业务流程
            OrderDO order = orderRepository.findById(reasonVO.getOrderId()).orElse(null);
            if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
            }
            if (Objects.isNull(order.getChangeProcessStatus()) || CommonBooleanEnum.NO.getCode().equals(order.getChangeProcessStatus())) {
                //判断取消条件
                if (Boolean.FALSE.equals(baseOrderService.canBuyerCancelOrderNew(order))) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_CAN_NOT_BE_CANCEL);
                }

                //将订单未发货的数量返回库存，SRM订单、请购采购订单不需要返回库存
                //baseOrderService.resumeInventory(order);

                //返还已抵扣的积分
                baseOrderDeductionService.returnDeductionPoints(order);

                order.setBuyerInnerStatus(BuyerInnerStatusEnum.CANCELLED.getCode());
                order.setVendorInnerStatus(VendorInnerStatusEnum.CANCELLED.getCode());
                order.setOuterStatus(OrderOuterStatusEnum.CANCELLED.getCode());
                orderRepository.saveAndFlush(order);

                // 调用优时接口释放库存
                if (CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode())) {
                    List<Long> commoditySingleIdList = order.getProducts()
                            .stream().map(OrderProductDO::getCommoditySingleId)
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(commoditySingleIdList)) {
                        SpaceSingleProductStatusReq commoditySingleProductStatusReq = new SpaceSingleProductStatusReq();
                        commoditySingleProductStatusReq.setSingleProductIds(commoditySingleIdList);
                        commoditySingleProductStatusReq.setStatus(FreightSpaceSingleProductStatusEnum.NOT_USE.getCode());
                        WrapperResp<Void> voidWrapperResp = commodityFeign.updateFreightSpaceSingleProductStatus(commoditySingleProductStatusReq);
                        if (WrapperUtil.isFail(voidWrapperResp)) {
                            log.error("更新现货商品单件状态失败: {}", voidWrapperResp.getMessage());
                            throw new BusinessException(voidWrapperResp.getCode(), voidWrapperResp.getMessage());
                        }
                    }
                    List<String> singleCodeList = order.getProducts()
                            .stream().map(OrderProductDO::getSingleCode)
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(singleCodeList)) {
                        String status = eosApiService.releaseStock(singleCodeList);
                        if (!"Y".equals(status)) {
                            log.error("更新现货商品单件状态失败: {}", status);
                            throw new BusinessException("更新现货商品单件状态失败");
                        }
                    }
                }

                //如果当前版本大于1则变更过，需要返还合同数量
                if (order.getVersion() > 1) {
                    //通知合同服务，取消SRM订单数据
                    baseOrderService.cancelSrmOrder(order);
                }

                //通知营销服务，返还营销活动、优惠券记录
                baseOrderService.resumePromotions(order);

                //内部、外部记录
                baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.CANCEL, order.getBuyerInnerStatus(), reasonVO.getReason().trim());
                baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.CANCEL, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), reasonVO.getReason().trim());
            } else {
                orderChangeService.returnSrmOrder(order);
                baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.CANCEL_CHANGE_ORDER, order.getBuyerInnerStatus(), reasonVO.getReason().trim());
                baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.CANCEL_CHANGE_ORDER, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), reasonVO.getReason().trim());
            }

            // 发送订单取消分佣消息
            orderCommissionMqUtil.sendOrderCancelledMessage(order.getOrderNo(), order.getBuyerUserId(), order.getBuyerMemberId(), order.getBuyerRoleId());

            UnFrozenAccountBalanceReq unFrozenAccountBalanceReq = new UnFrozenAccountBalanceReq();
            List<OrderPaymentDO> orderPaymentDOS = orderPaymentRepository.findByOrder(order);
            OrderPaymentDO orderPaymentDO = orderPaymentDOS.stream().filter(orderPayment -> Objects.nonNull(orderPayment.getBatchNo()) && orderPayment.getBatchNo() == 1).findFirst().get();
            Set<String> tradeNoSet = new HashSet<>();
            tradeNoSet.add(String.valueOf(orderPaymentDO.getId()));
            unFrozenAccountBalanceReq.setTradeCode(tradeNoSet);
            assetAccountFeign.unFrozenAccountBalance(unFrozenAccountBalanceReq);
        } finally {
            redissonUtils.unlock(lock);
        }

        return null;
    }

    /**
     * “待新增订单” - 获取前端页面下拉框列表
     *
     * @param loginUser 登录用户
     * @return 查询结果
     */
    @Override
    public BuyerToCreatePageItemResp getToCreatePageItems(UserLoginCacheDTO loginUser) {
        BuyerToCreatePageItemResp itemVO = new BuyerToCreatePageItemResp();
        itemVO.setOrderTypes(Stream.of(OrderTypeEnum.FRAME_CONTRACT_ORDER, OrderTypeEnum.INQUIRY_TO_PURCHASE, OrderTypeEnum.QUERY_PRICE_CONTRACT, OrderTypeEnum.PRICE_COMPETITION_CONTRACT_PURCHASE, OrderTypeEnum.PURCHASE_CONTRACT_BIDDING).map(e -> new DropdownItemResp(e.getCode(), e.getName())).collect(Collectors.toList()));
        itemVO.setOrderModes(Stream.of(OrderModeEnum.FRAME_CONTRACT_ORDER, OrderModeEnum.QUOTATION, OrderModeEnum.PURCHASE_INQUIRY, OrderModeEnum.PURCHASE_PRICE_COMPETITION, OrderModeEnum.PURCHASE_BIDDING, OrderModeEnum.REQUISITION_CONTRACT).map(e -> new DropdownItemResp(e.getCode(), e.getName())).collect(Collectors.toList()));
        return itemVO;
    }

    /**
     * “待新增订单” - 分页查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BuyerToCreateQueryResp> pageToCreateOrders(UserLoginCacheDTO loginUser, OrderPageDataReq pageVO) {
        List<OrderTypeEnum> orderTypes = NumberUtil.isNullOrNegativeZero(pageVO.getOrderType()) ? Stream.of(OrderTypeEnum.INQUIRY_TO_PURCHASE).collect(Collectors.toList()) : Collections.singletonList(OrderTypeEnum.parse(pageVO.getOrderType()));
        Page<OrderDO> pageList = baseOrderService.pageCreatedOrders(loginUser, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), orderTypes, OrderUpdateStepEnum.CHANGE_ORDER.getStep(), pageVO.getCurrent(), pageVO.getPageSize(), Collections.singletonList(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode()));
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerToCreateQueryResp queryVO = new BuyerToCreateQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setVendorMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            queryVO.setQuoteNo(order.getQuoteNo());
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setShowUpdate(baseOrderService.canBuyerUpdateOrder(order));
            queryVO.setShowChange(baseOrderService.canBuyerChangeOrder(order));
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            queryVO.setShowDelete(baseOrderService.canBuyerDeleteOrder(order));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待新增订单” - 分页查询SRM订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BuyerToCreateQueryResp> pageSrmOrders(UserLoginCacheDTO loginUser, OrderPageDataReq pageVO) {
        List<OrderTypeEnum> orderTypes = NumberUtil.isNullOrNegativeZero(pageVO.getOrderType()) ? Stream.of(OrderTypeEnum.QUERY_PRICE_CONTRACT, OrderTypeEnum.PRICE_COMPETITION_CONTRACT_PURCHASE, OrderTypeEnum.PURCHASE_CONTRACT_BIDDING, OrderTypeEnum.REQUISITION_CONTRACT, OrderTypeEnum.FRAME_CONTRACT_ORDER).collect(Collectors.toList()) : Collections.singletonList(OrderTypeEnum.parse(pageVO.getOrderType()));
        Page<OrderDO> pageList = baseOrderService.pageCreatedOrders(loginUser, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), orderTypes, OrderUpdateStepEnum.CHANGE_ORDER.getStep(), pageVO.getCurrent(), pageVO.getPageSize(), Stream.of(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode(), BuyerInnerStatusEnum.BUYER_GRADE_ONE_NOT_PASSED.getCode(), BuyerInnerStatusEnum.BUYER_GRADE_TWO_NOT_PASSED.getCode()).collect(Collectors.toList()));
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerToCreateQueryResp queryVO = new BuyerToCreateQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setVendorMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            queryVO.setQuoteNo(order.getQuoteNo());
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setShowUpdate(baseOrderService.canBuyerUpdateOrder(order));
            queryVO.setShowChange(baseOrderService.canBuyerChangeOrder(order));
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            queryVO.setShowDelete(baseOrderService.canBuyerDeleteOrder(order));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待新增订单” - 创建SRM订单
     *
     * @param loginUser 登录用户
     * @param orderVO   接口参数
     * @return 创建结果
     */
    @Override
    public Void createSrmOrder(UserLoginCacheDTO loginUser, BuyerSrmOrderReq orderVO) {
        return orderCreationService.createSrmOrder(loginUser, orderVO);
    }

    /**
     * 变更SRM订单
     *
     * @param loginUser 登录用户
     * @param orderVO   接口参数
     * @return 创建结果
     */
    @Override
    public Void changeSrmOrder(UserLoginCacheDTO loginUser, BuyerSrmOrderChangeReq orderVO) {
        return orderChangeService.changeSrmOrder(loginUser, orderVO);
    }

    /**
     * “待新增订单” - 查询订单详情
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public BuyerOrderDetailResp getToCreateOrderDetails(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        return baseOrderService.getBuyerOrderDetail(order, BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE);
    }

    /**
     * “待新增订单” - 修改Srm订单
     *
     * @param loginUser 登录用户
     * @param updateVO  接口参数
     * @return 修改结果
     */
    @Override
    public Void updateSrmOrder(UserLoginCacheDTO loginUser, BuyerSrmOrderUpdateReq updateVO) {
        return orderCreationService.updateSrmOrder(loginUser, updateVO);
    }

    /**
     * “SRM订单” - 查看物料关联的请购单
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderRequisitionQueryResp> findSrmRequisition(UserLoginCacheDTO loginUser, OrderRequisitionPageDataReq pageVO) {
        return baseOrderProductService.findSrmRequisition(pageVO);
    }

    /**
     * “待新增订单” - “创建B2B订单” - 查询支付环节列表
     *
     * @param loginUser 登录用户
     * @param payVO     接口参数
     * @return 查询结果
     */
    @Override
    public List<BuyerOrderPayNodeDetailResp> findBusinessOrderProcessPayment(UserLoginCacheDTO loginUser, OrderProcessPayReq payVO) {
        //Step 1: 跨境商品不能与其他商品一起下单
        List<Boolean> crossBorders = payVO.getProducts().stream().map(product -> !Objects.isNull(product.getCrossBorder()) && product.getCrossBorder()).distinct().collect(Collectors.toList());
        if (crossBorders.size() > 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TRADE_PROCESS_IS_DIFFERENT);
        }

        //Step 2: 查询供应商交易流程规则
        // 0618修改，通过流程引擎搜索交易规则
        OrderTradeProcessBO tradeProcessResult = findB2BTradeProcess(payVO, loginUser);

        //Step 3: 返回支付环节配置
        return tradeProcessResult.getPayNodes().stream().filter(payNode -> payNode.getPayRate().compareTo(BigDecimal.ZERO) > 0).map(payNode -> {
            BuyerOrderPayNodeDetailResp detailVO = new BuyerOrderPayNodeDetailResp();
            detailVO.setBatchNo(payNode.getBatchNo());
            detailVO.setPayNode(OrderPayNodeEnum.getNameByCode(payNode.getPayNode()));
            detailVO.setOuterStatusName(OrderOuterStatusEnum.TO_PAY.getName());
            detailVO.setPayRate(NumberUtil.formatPayRate(payNode.getPayRate()));
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * “待新增订单” - “修改B2B订单” - 查询支付环节列表
     *
     * @param loginUser 登录用户
     * @param updateVO  接口参数
     * @return 查询结果
     */
    @Override
    public List<BuyerOrderPayNodeQueryResp> findBusinessOrderProcessPaymentUpdate(UserLoginCacheDTO loginUser, BuyerBusinessOrderProcessPayUpdateReq updateVO) {
        OrderDO order = orderRepository.findById(updateVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 查询会员支付方式
        List<OrderPayTypeDetailResp> payTypeResult = baseOrderProcessService.findMemberPayment(loginUser.getMemberId(), loginUser.getMemberRoleId(), updateVO.getVendorMemberId(), updateVO.getVendorRoleId());

        //Step 2:  查询支付记录，如果会员支付方式中不包含已经有的支付记录中的支付方式和渠道，设置为Null
        List<BuyerOrderPayNodeQueryResp> payNodes = baseOrderPaymentService.findBusinessOrderPayments(order);
        payNodes.forEach(payNode -> {
            OrderPayTypeDetailResp payTypeDetail = payTypeResult.stream().filter(payType -> payType.getPayType().equals(payNode.getPayType())).findFirst().orElse(null);
            if (payTypeDetail == null) {
                payNode.setPayType(null);
                payNode.setPayChannel(null);
                return;
            }

            if (payTypeDetail.getPayChannels().stream().noneMatch(payChannel -> payChannel.getPayChannel().equals(payNode.getPayChannel()))) {
                payNode.setPayChannel(null);
            }
        });

        return payNodes;
    }

    /**
     * “待新增订单” - B2B订单 - 查询支付方式与支付渠道列表
     *
     * @param loginUser      登录用户
     * @param vendorMemberVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderPayTypeDetailResp> findBusinessOrderPayTypes(UserLoginCacheDTO loginUser, OrderVendorMemberReq vendorMemberVO) {
        return baseOrderProcessService.findMemberPayment(loginUser.getMemberId(), loginUser.getMemberRoleId(), vendorMemberVO.getVendorMemberId(), vendorMemberVO.getVendorRoleId());
    }

    /**
     * “待新增订单” - 创建B2B订单
     *
     * @param loginUser 登录用户
     * @param orderVO   接口参数
     * @return 创建结果
     */
    @Override
    public Void createBusinessOrder(UserLoginCacheDTO loginUser, BuyerBusinessOrderReq orderVO) {
        return orderCreationService.createBusinessOrder(loginUser, orderVO);
    }

    /**
     * “待新增订单” - 修改B2B订单
     *
     * @param loginUser 登录用户
     * @param updateVO  接口参数
     * @return 创建结果
     */
    @Override
    public Void updateBusinessOrder(UserLoginCacheDTO loginUser, BuyerBusinessOrderUpdateReq updateVO) {
        return orderCreationService.updateBusinessOrder(loginUser, updateVO);
    }

    /**
     * “待新增订单” - 删除订单
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 删除结果
     */
    @Transactional
    @Override
    public Void deleteOrder(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //将合同数量返还
        baseOrderService.cancelSrmOrder(order);

        //如果是转单后的订单，将原订单的关联关系取消
        if (NumberUtil.notNullOrZero(order.getRelationId()) && NumberUtil.notNullOrZero(order.getSeparateType()) && order.getSeparateType().equals(OrderSeparateTypeEnum.TRANSFERRED.getCode())) {
            OrderDO separateOrder = orderRepository.findById(order.getRelationId()).orElse(null);
            if (separateOrder != null) {
                separateOrder.setRelationId(0L);
                separateOrder.setRelationNo("");
                separateOrder.setSeparateType(OrderSeparateTypeEnum.DEFAULT.getCode());
                orderRepository.saveAndFlush(separateOrder);
            }
        }

        if (!CollectionUtils.isEmpty(order.getDeliveries())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DELETE_DELIVERY_ERROR);
        }
        orderRepository.delete(order);
        return null;
    }

    /**
     * “待新增订单” - 批量删除订单
     *
     * @param loginUser 登录用户
     * @param orderIds  接口参数
     * @return 删除结果
     */
    @Transactional
    @Override
    public Void batchDeleteOrder(UserLoginCacheDTO loginUser, List<OrderIdReq> orderIds) {
        List<OrderDO> orders = orderRepository.findAllById(orderIds.stream().map(OrderIdReq::getOrderId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(orders) || orderIds.size() != orders.size() || orders.stream().anyMatch(order -> !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //将合同数量返还
        for (OrderDO order : orders) {
            baseOrderService.cancelSrmOrder(order);
        }

        orderRepository.deleteAll(orders);
        return null;
    }

    /**
     * “待新增订单” - 提交
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 删除结果
     */
    @Transactional
    @Override
    public Void submitOrder(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        // step 2: 校验订单
        // 1.操作用户是否是订单的采购会员、角色
        // 2.采购商内部状态是否是待提交审核
        OrderDO order = orderRepository.findById(idVO.getOrderId()).filter(f -> Objects.equals(f.getBuyerInnerStatus(), BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode()) && Objects.equals(f.getBuyerMemberId(), loginUser.getMemberId()) && Objects.equals(f.getBuyerRoleId(), loginUser.getMemberRoleId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST));

        // step 3: 校验数据
        //所有请购单信息
        List<RequisitionBO> requisitionBOS = new ArrayList<>();
        Set<OrderProductDO> products = order.getProducts();
        List<OrderMaterialDO> materialDOList = products.stream().map(OrderProductDO::getMaterial).filter(Objects::nonNull).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(materialDOList)) {
            materialDOList.forEach(materialDO -> {
                List<RequisitionBO> requisitions = materialDO.getRequisitions();
                if (!CollectionUtils.isEmpty(requisitions)) {
                    requisitionBOS.addAll(requisitions);
                }
            });
        }
        if (order.getOrderKind().equals(OrderSourceKindEnum.SRM.getCode()) && Objects.nonNull(order.getOrderContractType()) && !CollectionUtils.isEmpty(order.getProducts()) && order.getVersion() == 1) {
            //1 判断采购合同订单是否超出合同物料限制
            //1-1 判断请购合同订单
            baseOrderService.judgeQuantity(loginUser, requisitionBOS, order.getId());
            //1-2 判断采购询价、竞价、招标、框架合同订单
            baseOrderService.judgeContractQuantity(loginUser, Collections.singletonList(order));
            //2 判断合同是否处于变更状态
            baseOrderService.judgeContractStatus(order.getId());
        }

        //Step 0: 查询供应商业务员Id
        Long salesResult = baseOrderAuthService.findVendorSalesUserId(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId());

        //Step 1: 非Srm订单扣减库存
        baseOrderService.deduceInventoryWithOutStatus(order);

        //Step 2 : 执行工作流任务（修改订单状态、记录内外流转记录）
        int lastBuyerInnerStatus = order.getBuyerInnerStatus();
        //返还库存
        if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
            baseOrderTaskService.execOrderChangeProcess(order, OrderConstant.DIRECT_EXEC_TASK);
        } else {
            baseOrderTaskService.execOrderProcess(order, OrderConstant.DIRECT_EXEC_TASK);
        }

//        if(taskResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
//            //返还库存
//            baseOrderService.resumeInventory(order);
//            throw new BusinessException(taskResult.getCode(), taskResult.getMessage());
//        }

        //Step 3: 以外部状态“待提交”作判断，订单是否已经提交给供应商
        //        如果外部状态为“待提交”，表示订单处于采购商审核过程中，否则表示订单已经提交给了“供应商”
        if (baseOrderService.hasSubmittedToVendor(order)) {
            //Step 3-1: 订单提交时间与供应商用户Id
            order.setSubmitTime(LocalDateTime.now());
            order.setVendorUserId(salesResult);

            //当前订单版本为1的时候同步合同服务，
            if (order.getVersion() == 1) {
                //Step 3-2: 同步Srm订单数据至合同服务
                baseOrderService.sendSrmOrder(order);
            }

            //Step 3-3 : 订单外部流转记录
            baseOrderHistoryService.saveVendorOrderOuterHistory(order.getId(), order.getOrderNo(), order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.SUBMIT, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

            //Step 3-5 : 发送供应商实时消息
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

            //Step 3-6 ： 供应商交易流程规则配置中的自动取消
            baseOrderScheduleService.scheduleOrderCancel(order.getId(), order.getTask() == null ? BigDecimal.ZERO : order.getTask().getExpireHours());

            //Step 4 : 订单内部流转记录的操作为“提交”
            baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.SUBMIT, order.getBuyerInnerStatus(), "");
        } else {
            //Step 4 : 订单内部流转记录的操作为“提交审核”
            baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.SUBMIT_VALIDATE, order.getBuyerInnerStatus(), "");
        }

        //Step 5 : 发送采购商实时消息
        baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

        //Step 6: 如果是拼团订单，向营销服务发送拼团数据，等待拼团结果通知
        baseOrderService.notifyGroupOrder(order);

        //Step 7: 如果是代发货订单,向营销服务发送优惠券信息
        baseOrderService.sendCoupons(order);

        //Last step: 保存订单
        orderRepository.saveAndFlush(order);

        //维护送货计划
        deliveryPlanService.saveDeliveryPlanProduct(order);

        return null;
    }

    /**
     * “待新增订单” - 批量提交
     *
     * @param loginUser 登录用户
     * @param orderIds  接口参数
     * @return 删除结果
     */
    @Transactional
    @Override
    public Void batchSubmitOrder(UserLoginCacheDTO loginUser, List<OrderIdReq> orderIds) {
        List<OrderDO> orders = orderRepository.findAllById(orderIds.stream().map(OrderIdReq::getOrderId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(orders) || orderIds.size() != orders.size() || orders.stream().anyMatch(order -> !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 0: 查询供应商业务员用户Id
        List<OrderMemberDTO> members = orders.stream().map(order -> new OrderMemberDTO(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId())).distinct().collect(Collectors.toList());
        List<OrderSalesDTO> salesResult = baseOrderAuthService.findVendorSales(members);

        //Step 1: 记录扣减库存成功的订单，在其他订单扣减库存失败，或工作流任务失败时，返还库存
        List<OrderDO> deduceSuccessList = new ArrayList<>();

        //获取所有请购单信息
        List<RequisitionBO> requisitionBOS = new ArrayList<>();
        //所有请购单信息
        orders.forEach(orderDO -> {
            Set<OrderProductDO> products = orderDO.getProducts();
            List<OrderMaterialDO> materialDOList = products.stream().map(OrderProductDO::getMaterial).filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(materialDOList)) {
                materialDOList.forEach(materialDO -> {
                    List<RequisitionBO> requisitions = materialDO.getRequisitions();
                    if (!CollectionUtils.isEmpty(requisitions)) {
                        requisitionBOS.addAll(requisitions);
                    }
                });
            }
        });

        //Step 2 : 循环执行提交
        for (OrderDO order : orders) {

            if (order.getOrderKind().equals(OrderSourceKindEnum.SRM.getCode()) && Objects.nonNull(order.getOrderContractType()) && !CollectionUtils.isEmpty(order.getProducts()) && order.getVersion() == 1) {
                //1 判断请购合同订单是否超出合同物料限制
                baseOrderService.judgeQuantity(loginUser, requisitionBOS, order.getId());
                //2 判断采购询价、竞价、招标、框架合同订单
                baseOrderService.judgeContractQuantity(loginUser, Collections.singletonList(order));
                //3 判断合同是否处于变更状态
                baseOrderService.judgeContractStatus(order.getId());
            }

            //非Srm订单扣减库存
            baseOrderService.deduceInventoryWithOutStatus(order);
            deduceSuccessList.add(order);

            // 检查是否需要调用合同确认
            int lastBuyerInnerStatus = order.getBuyerInnerStatus();
            if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
                try {
                    baseOrderTaskService.execOrderChangeProcess(order, OrderConstant.DIRECT_EXEC_TASK);
                } catch (BusinessException e) {
                    deduceSuccessList.forEach(deduceOrder -> baseOrderService.resumeInventory(deduceOrder));
                    throw e;
                }
            } else {
                try {
                    baseOrderTaskService.execOrderProcess(order, OrderConstant.DIRECT_EXEC_TASK);
                } catch (BusinessException e) {
                    deduceSuccessList.forEach(deduceOrder -> baseOrderService.resumeInventory(deduceOrder));
                    throw e;
                }
            }
            //Step 3: 以外部状态“待提交”作判断，订单是否已经提交给供应商
            //        如果外部状态为“待提交”，表示订单处于采购商审核过程中，否则表示订单已经提交给了“供应商”
            if (baseOrderService.hasSubmittedToVendor(order)) {
                //Step 3-1: 订单提交时间与供应商用户Id
                order.setSubmitTime(LocalDateTime.now());
                order.setVendorUserId(salesResult.stream().filter(s -> s.getBuyerMemberId().equals(order.getBuyerMemberId()) && s.getBuyerRoleId().equals(order.getBuyerRoleId()) && s.getVendorMemberId().equals(order.getVendorMemberId()) && s.getVendorRoleId().equals(order.getVendorRoleId())).map(OrderSalesDTO::getVendorUserId).findFirst().orElse(null));

                if (order.getVersion() == 1) {
                    //Step 3-2: 同步Srm订单数据至合同服务
                    baseOrderService.sendSrmOrder(order);
                }

                //Step 3-3 : 订单外部流转记录
                baseOrderHistoryService.saveVendorOrderOuterHistory(order.getId(), order.getOrderNo(), order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.SUBMIT, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

                //Step 3-4 : 发送供应商实时消息
                baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

                //Step 3-5 ： 供应商交易流程规则配置中的自动取消
                baseOrderScheduleService.scheduleOrderCancel(order.getId(), order.getTask() == null ? BigDecimal.ZERO : order.getTask().getExpireHours());

                //Step 4 : 订单内部流转记录的操作为“提交”
                baseOrderHistoryService.saveVendorInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.SUBMIT, order.getVendorInnerStatus(), "");
            } else {
                //Step 4 : 订单内部流转记录的操作为“提交审核”
                baseOrderHistoryService.saveVendorInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.SUBMIT_VALIDATE, order.getVendorInnerStatus(), "");
            }

            //Step 5 : 发送采购商实时消息
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

            //Step 6: 如果是拼团订单，向营销服务发送拼团数据，等待拼团结果通知
            baseOrderService.notifyGroupOrder(order);

            //Step 7: 如果是代发货订单,向营销服务发送优惠券信息
            baseOrderService.sendCoupons(order);
        }

        //Last step: 保存订单
        orderRepository.saveAll(orders);

        return null;
    }

    /**
     * “新增现货采购订单” - 分页查询现货采购订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BuyerToCreateQueryResp> pagePurchaseOrders(UserLoginCacheDTO loginUser, BuyerOrderCreatePageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageCreatedOrders(loginUser, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), Stream.of(OrderTypeEnum.SPOT_PURCHASING, OrderTypeEnum.COMMODITY_SAMPLE_ORDER).collect(Collectors.toList()), OrderUpdateStepEnum.CHANGE_ORDER.getStep(), pageVO.getCurrent(), pageVO.getPageSize(), Collections.singletonList(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode()));
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerToCreateQueryResp queryVO = new BuyerToCreateQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setVendorMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            queryVO.setQuoteNo(order.getQuoteNo());
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setShowUpdate(baseOrderService.canBuyerUpdateOrder(order));
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            queryVO.setShowChange(baseOrderService.canBuyerChangeOrder(order));
            queryVO.setShowDelete(baseOrderService.canBuyerDeleteOrder(order));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “新增现货采购订单” - 根据商城类型查询订单类型和订单模式
     *
     * @param loginUser  登录用户
     * @param shopTypeVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderModeResp> findPurchaseOrderTypes(UserLoginCacheDTO loginUser, PurchaseOrderShopTypeReq shopTypeVO) {
        if (Objects.requireNonNull(ShopTypeEnum.parseCode(shopTypeVO.getShopType())) == ShopTypeEnum.ENTERPRISE) {
            return Arrays.asList(new OrderModeResp(OrderModeEnum.BUYER), new OrderModeResp(OrderModeEnum.COMMODITY_SAMPLE_ORDER));
        }
        return Collections.singletonList(new OrderModeResp());
    }

    /**
     * “新增现货采购订单” - 创建现货采购订单
     *
     * @param loginUser 登录用户
     * @param orderVO   接口参数
     * @return 新增结果
     */
    @Override
    public Void createPurchaseOrder(UserLoginCacheDTO loginUser, BuyerPurchaseOrderReq orderVO) {
        return orderCreationService.createPurchaseOrder(loginUser, orderVO);
    }

    /**
     * “新增现货采购订单” - 修改现货采购订单
     *
     * @param loginUser 登录用户
     * @param updateVO  接口参数
     * @return 新增结果
     */
    @Override
    public Void updatePurchaseOrder(UserLoginCacheDTO loginUser, BuyerPurchaseOrderUpdateReq updateVO) {
        return orderCreationService.updatePurchaseOrder(loginUser, updateVO);
    }

    /**
     * 请购单订单 - 新增订单
     *
     * @param loginUser 登录用户
     * @param orderVO   接口参数
     * @return 新增结果
     */
    @Override
    public Void createRequisitionOrder(UserLoginCacheDTO loginUser, RequisitionOrderCreateReq orderVO) {
        return orderCreationService.createRequisitionOrder(loginUser, orderVO);
    }

    /**
     * 请购单订单 - 修改订单
     *
     * @param loginUser 登录用户
     * @param orderVO   接口参数
     * @return 操作结果
     */
    @Override
    public Void updateRequisitionOrder(UserLoginCacheDTO loginUser, RequisitionOrderUpdateReq orderVO) {
        return orderCreationService.updateRequisitionOrder(loginUser, orderVO);
    }

    /**
     * 新增请购单订单 - 分页查询
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 新增结果
     */
    @Override
    public PageDataResp<BuyerToCreateQueryResp> createRequisitionPage(UserLoginCacheDTO loginUser, BuyerOrderCreatePageDataReq pageVO) {
        List<OrderTypeEnum> orderTypes = Collections.singletonList(OrderTypeEnum.REQUISITION_TO_PURCHASE);

        Page<OrderDO> pageList = baseOrderService.pageCreatedOrders(loginUser, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), orderTypes, OrderUpdateStepEnum.CHANGE_ORDER.getStep(), pageVO.getCurrent(), pageVO.getPageSize(), Collections.singletonList(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode()));
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerToCreateQueryResp queryVO = new BuyerToCreateQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setVendorMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            queryVO.setQuoteNo(order.getQuoteNo());
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setShowUpdate(baseOrderService.canBuyerUpdateOrder(order));
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            queryVO.setShowChange(baseOrderService.canBuyerChangeOrder(order));
            queryVO.setShowDelete(baseOrderService.canBuyerDeleteOrder(order));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 物料订单 - 新增订单
     *
     * @param loginUser 登录用户
     * @param orderVO   接口参数
     * @return 新增结果
     */
    @Override
    public Void createMaterielOrder(UserLoginCacheDTO loginUser, MaterielOrderCreateReq orderVO) {
        return orderCreationService.createMaterielOrder(loginUser, orderVO);
    }

    /**
     * 物料订单 - 修改订单
     *
     * @param loginUser 登录用户
     * @param orderVO   接口参数
     * @return 操作结果
     */
    @Override
    public Void updateMaterielOrder(UserLoginCacheDTO loginUser, MaterielOrderUpdateReq orderVO) {
        return orderCreationService.updateMaterielOrder(loginUser, orderVO);
    }

    /**
     * 物料订单 - 分页查询待新增订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 列表数据
     */
    @Override
    public PageDataResp<BuyerToCreateQueryResp> materielPage(UserLoginCacheDTO loginUser, BuyerOrderCreatePageDataReq pageVO) {
        //订单类型
        List<OrderTypeEnum> orderTypes = Arrays.asList(OrderTypeEnum.MANUAL_MATERIAL_ORDER_PLACEMENT, OrderTypeEnum.MATERIAL_SAMPLE_ORDER);
        //内部状态为 待提交审核、一级审核不通过、二级审核不通过
        List<Integer> statusList = Arrays.asList(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode(), BuyerInnerStatusEnum.BUYER_GRADE_ONE_NOT_PASSED.getCode(), BuyerInnerStatusEnum.BUYER_GRADE_TWO_NOT_PASSED.getCode());
        Page<OrderDO> pageList = baseOrderService.pageCreatedOrders(loginUser, loginUser.getMemberId(), loginUser.getMemberRoleId(), pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), orderTypes, OrderUpdateStepEnum.CHANGE_ORDER.getStep(), pageVO.getCurrent(), pageVO.getPageSize(), statusList);
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerToCreateQueryResp queryVO = new BuyerToCreateQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setVendorMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            queryVO.setQuoteNo(order.getQuoteNo());
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setShowUpdate(baseOrderService.canBuyerUpdateOrder(order));
            queryVO.setShowChange(baseOrderService.canBuyerChangeOrder(order));
            queryVO.setShowDelete(baseOrderService.canBuyerDeleteOrder(order));
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * （订单审核各个页面）获取前端页面下拉框列表
     *
     * @param loginUser 登录用户
     * @return 查询结果
     */
    @Override
    public ValidatePageItemResp getValidatePageItems(UserLoginCacheDTO loginUser) {
        ValidatePageItemResp itemVO = new ValidatePageItemResp();
        itemVO.setOrderTypes(baseOrderService.listOrderTypes());
        return itemVO;
    }

    /**
     * “待审核订单（一级）” - 分页查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderPageQueryResp> pageToValidateGradeOneOrders(UserLoginCacheDTO loginUser, OrderPageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_ONE.getCode(), null, OrderUpdateStepEnum.CHANGE_ORDER_EXAMINE_1.getStep(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(this::builderOrderPageQueryVO).collect(Collectors.toList()));
    }

    /**
     * 构建返回查询分页数据
     *
     * @param order 订单数据
     * @return 分页列表数据
     */
    private OrderPageQueryResp builderOrderPageQueryVO(OrderDO order) {
        OrderPageQueryResp queryVO = new OrderPageQueryResp();
        queryVO.setOrderId(order.getId());
        queryVO.setOrderNo(order.getOrderNo());
        queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
        queryVO.setDigest(order.getDigest());
        queryVO.setMemberName(order.getVendorMemberName());
        queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
        queryVO.setOrderType(order.getOrderType());
        queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
        queryVO.setInnerStatus(order.getBuyerInnerStatus());
        queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
        queryVO.setOuterStatus(order.getOuterStatus());
        queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
        queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
        queryVO.setTotalProductCount(order.getTotalProductCount());
        queryVO.setTotalWeight(order.getTotalWeight());
        return queryVO;
    }

    /**
     * “待审核订单（一级）” - 查询订单详情
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public BuyerOrderDetailResp getToValidateGradeOneOrderDetails(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        return baseOrderService.getBuyerOrderDetail(order, BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_ONE);
    }

    /**
     * 待拣货复核订单 - 分页查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderPageQueryResp> pageToValidateGradeOnePickOrders(UserLoginCacheDTO loginUser, OrderPageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), BuyerInnerStatusEnum.TO_VENDOR_PICKING_CHECK.getCode(), null, OrderUpdateStepEnum.CHANGE_ORDER_EXAMINE_1.getStep(), pageVO.getCurrent(), pageVO.getPageSize());
        CommonIdListReq commonIdListReq = new CommonIdListReq();
        commonIdListReq.setIdList(pageList.stream().map(OrderDO::getVendorMemberId).distinct().collect(Collectors.toList()));
        WrapperResp<List<MemberFeignCodeRes>> memberFeignList = memberFeign.findMemberById(commonIdListReq);
        pageList.getContent().forEach(s -> {
            if (WrapperUtil.isOk(memberFeignList) && CollUtil.isNotEmpty(memberFeignList.getData())) {
                List<MemberFeignCodeRes> memberFeignCodeResList = memberFeignList.getData();
                Optional<MemberFeignCodeRes> optional = memberFeignCodeResList.stream().filter(member -> member.getId().equals(s.getVendorMemberId())).findFirst();
                optional.ifPresent(memberFeignCodeRes -> s.setVendorMemberName(memberFeignCodeRes.getName()));
            }
        });
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(this::builderOrderPageQueryVO).collect(Collectors.toList()));
    }

    /**
     * 待拣货复核订单 - 拣货复核
     * @param agreeVO 接口参数
     * @return 审核结果
     */
    @Override
    public Void validateOrderGradeOnePick(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        OrderDO order = orderRepository.findById(agreeVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.TO_VENDOR_PICKING_CHECK.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 参数判断
        if (agreeVO.getAgree().equals(OrderConstant.DISAGREE) && !StringUtils.hasLength(agreeVO.getReason())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VALIDATE_REASON_CAN_NOT_BE_EMPTY);
        }
        //校验销售单下所有采购单是否都已经拣货完成
        List<OrderDO> orderDOS = orderRepository.findByRelationId(order.getRelationId());
        try{
            boolean pickedFlag = orderDOS.stream().allMatch(orderDO -> (orderDO.getId().equals(order.getId()) || VendorInnerStatusEnum.TO_PACKAGE.getName().equals(orderDO.getVendorInnerStatus())));
            if(pickedFlag){
                //因订单工作流报错后，数据无法回滚，所以把推送放到工作流前面
                OrderDO saleOrderDO = orderRepository.findById(order.getRelationId()).orElse(null);
                if(Objects.isNull(saleOrderDO)){
                    throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
                }
                CheckPickingNotifyReq checkPickingNotifyReq = new CheckPickingNotifyReq();
                checkPickingNotifyReq.setDdzt(VendorInnerStatusEnum.TO_PACKAGE.getName());
                checkPickingNotifyReq.setGxsj(DateTimeUtil.getCurrentDateTime());
                checkPickingNotifyReq.setXsddh(saleOrderDO.getOrderNo());
                eosApiService.checkPickingNotify(checkPickingNotifyReq);
            }
        }catch (Exception e){

        }
        //Step 2: 执行工作流任务（修改订单状态、记录内外流转记录）
        //变更流程审核兼容,如果当前订单处于变更流程，则走变更工作流
        baseOrderTaskService.execOrderProcess(order, agreeVO.getAgree());

        orderRepository.saveAndFlush(order);

        //Step 2 : 订单内部流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.MERCHANT_PICKING_REVIEW, order.getBuyerInnerStatus(), StringUtils.hasLength(agreeVO.getReason()) ? agreeVO.getReason() : "");

        //Step 3 : 发送采购商实时消息
        baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

        // 判断所有采购订单是否都已完成
        OrderDO orderDO = orderRepository.findById(order.getRelationId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST));
        List<Long> orderIds = orderDO.getOrderRelationDOSet().stream().map(OrderRelationDO::getRelationId).collect(Collectors.toList());
        List<OrderDO> relatedOrders = orderRepository.findAllById(orderIds);
        if (relatedOrders.stream().allMatch(o -> o.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.ACCOMPLISHED.getCode()))) {
            // 判断订单是自提还是发货
            baseOrderTaskService.execOrderProcess(orderDO, OrderConstant.DIRECT_EXEC_TASK);

            orderRepository.saveAndFlush(orderDO);

            OrderLogisticsDO orderLogistics = new OrderLogisticsDO();
            orderLogistics.setOrder(orderDO);
            orderLogistics.setLogisticsStatus(LogisticsStatusEnum.PICKING_COMPLETED.getCode());
            orderLogistics.setCreateTime(LocalDateTime.now());
            orderLogisticsDORepository.saveAndFlush(orderLogistics);

            // 商品库存扣减
            if (CommoditySaleModeEnum.SPOT.getCode().equals(orderDO.getSaleMode())) {
                orderServiceProxy.deductStock(orderDO);
            }

            baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), orderDO.getId(), orderDO.getOrderNo(), OrderOperationEnum.MERCHANT_PICKING_REVIEW, orderDO.getBuyerInnerStatus(), StringUtils.hasLength(agreeVO.getReason()) ? agreeVO.getReason() : "");
            baseOrderService.messageBuyerOrder(orderDO.getBuyerMemberId(), orderDO.getBuyerRoleId(), orderDO.getBuyerUserId(), orderDO.getBuyerInnerStatus(), orderDO.getOrderNo(), orderDO.getDigest());
        }
        return null;
    }

    /**
     * “待审核订单（一级）” - 审核
     *
     * @param loginUser 登录用户
     * @param agreeVO   接口参数
     * @return 审核结果
     */
    @Transactional
    @Override
    public Void validateOrderGradeOne(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        OrderDO order = orderRepository.findById(agreeVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_ONE.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 参数判断
        if (agreeVO.getAgree().equals(OrderConstant.DISAGREE) && !StringUtils.hasLength(agreeVO.getReason())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VALIDATE_REASON_CAN_NOT_BE_EMPTY);
        }
        int lastBuyerInnerStatus = order.getBuyerInnerStatus();

        //Step 2: 执行工作流任务（修改订单状态、记录内外流转记录）
        //变更流程审核兼容,如果当前订单处于变更流程，则走变更工作流
        if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
            baseOrderTaskService.execOrderChangeProcess(order, agreeVO.getAgree());
        } else {
            baseOrderTaskService.execOrderProcess(order, agreeVO.getAgree());
        }

        orderRepository.saveAndFlush(order);

        //Step 2 : 订单内部流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.VALIDATE_GRADE_ONE, order.getBuyerInnerStatus(), StringUtils.hasLength(agreeVO.getReason()) ? agreeVO.getReason() : "");

        //Step 3 : 发送采购商实时消息
        baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

        //审核失败的变更中的srm订单回退版本
        if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus()) && OrderConstant.DISAGREE.equals(agreeVO.getAgree()) && (order.getOrderKind().equals(OrderSourceKindEnum.SRM.getCode()) || order.getOrderKind().equals(OrderSourceKindEnum.REQUISITION.getCode()))) {
            orderChangeService.returnSrmOrder(order);
        }

        return null;
    }

    /**
     * “待审核订单（一级）” - 批量审核
     *
     * @param loginUser 登录用户
     * @param orderIds  接口参数
     * @return 审核结果
     */
    @Transactional
    @Override
    public Void batchValidateOrdersGradeOne(UserLoginCacheDTO loginUser, List<OrderIdReq> orderIds) {
        List<OrderDO> orders = orderRepository.findAllById(orderIds.stream().map(OrderIdReq::getOrderId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(orders) || orderIds.size() != orders.size() || orders.stream().anyMatch(order -> !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_ONE.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 记录订单的上一次内部状态，用于向报表服务发送采购商消息
        Map<Long, Integer> lastBuyerInnerStatusMap = orders.stream().collect(Collectors.toMap(OrderDO::getId, OrderDO::getBuyerInnerStatus));

        //Step 2 : 执行工作流任务
        for (OrderDO order : orders) {
            if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
                baseOrderTaskService.execOrderChangeProcess(order, OrderConstant.AGREE);
            } else {
                baseOrderTaskService.execOrderProcess(order, OrderConstant.AGREE);
            }
        }
        orderRepository.saveAll(orders);

        //Step 3 : 订单内部流转记录、消息服务发送
        orders.forEach(order -> {
            baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.VALIDATE_GRADE_ONE, order.getBuyerInnerStatus(), "");
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());
        });

        return null;
    }

    /**
     * “待审核订单（二级）” - 分页查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderPageQueryResp> pageToValidateGradeTwoOrders(UserLoginCacheDTO loginUser, OrderPageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_TWO.getCode(), null, OrderUpdateStepEnum.CHANGE_ORDER_EXAMINE_2.getStep(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(this::builderOrderPageQueryVO).collect(Collectors.toList()));
    }

    /**
     * “待审核订单（二级）” - 查询订单详情
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public BuyerOrderDetailResp getToValidateGradeTwoOrderDetails(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        return baseOrderService.getBuyerOrderDetail(order, BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_TWO);
    }

    /**
     * “待审核订单（二级）” - 审核
     *
     * @param loginUser 登录用户
     * @param agreeVO   接口参数
     * @return 审核结果
     */
    @Transactional
    @Override
    public Void validateOrderGradeTwo(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        OrderDO order = orderRepository.findById(agreeVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_TWO.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 参数判断
        if (agreeVO.getAgree().equals(OrderConstant.DISAGREE) && !StringUtils.hasLength(agreeVO.getReason())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VALIDATE_REASON_CAN_NOT_BE_EMPTY);
        }

        int lastBuyerInnerStatus = order.getBuyerInnerStatus();

        //Step 2: 执行工作流任务（修改订单状态、记录内外流转记录）
        //变更流程审核兼容,如果当前订单处于变更流程，则走变更工作流
        if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
            baseOrderTaskService.execOrderChangeProcess(order, agreeVO.getAgree());
        } else {
            baseOrderTaskService.execOrderProcess(order, agreeVO.getAgree());
        }

        orderRepository.saveAndFlush(order);

        //Step 2 : 订单内部流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.VALIDATE_GRADE_TWO, order.getBuyerInnerStatus(), StringUtils.hasLength(agreeVO.getReason()) ? agreeVO.getReason() : "");

        //Step 3 : 发送采购商实时消息
        baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

        //todo 目前只支持srm订单。需要兼容b2b订单
        if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus()) && OrderConstant.DISAGREE.equals(agreeVO.getAgree()) && (order.getOrderKind().equals(OrderSourceKindEnum.SRM.getCode()) || order.getOrderKind().equals(OrderSourceKindEnum.REQUISITION.getCode()))) {
            orderChangeService.returnSrmOrder(order);
        }

        return null;
    }

    /**
     * “待审核订单（二级）” - 批量审核
     *
     * @param loginUser 登录用户
     * @param orderIds  接口参数
     * @return 审核结果
     */
    @Transactional
    @Override
    public Void batchValidateOrdersGradeTwo(UserLoginCacheDTO loginUser, List<OrderIdReq> orderIds) {
        List<OrderDO> orders = orderRepository.findAllById(orderIds.stream().map(OrderIdReq::getOrderId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(orders) || orderIds.size() != orders.size() || orders.stream().anyMatch(order -> !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_TWO.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 记录订单的上一次内部状态，用于向报表服务发送采购商消息
        Map<Long, Integer> lastBuyerInnerStatusMap = orders.stream().collect(Collectors.toMap(OrderDO::getId, OrderDO::getBuyerInnerStatus));

        //Step 2 : 执行工作流任务
        for (OrderDO order : orders) {
            if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
                baseOrderTaskService.execOrderChangeProcess(order, OrderConstant.AGREE);
            } else {
                baseOrderTaskService.execOrderProcess(order, OrderConstant.AGREE);
            }
        }
        orderRepository.saveAll(orders);

        //Step 3 : 订单内部流转记录、消息服务发送
        orders.forEach(order -> {
            baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.VALIDATE_GRADE_TWO, order.getBuyerInnerStatus(), "");
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());
        });

        return null;
    }

    /**
     * “待提交订单” - 分页查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderPageQueryResp> pageToSubmitOrders(UserLoginCacheDTO loginUser, OrderPageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), BuyerInnerStatusEnum.BUYER_TO_SUBMIT.getCode(), null, OrderUpdateStepEnum.CHANGE_ORDER_SUBMIT.getStep(), pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(this::builderOrderPageQueryVO).collect(Collectors.toList()));
    }

    /**
     * “待提交订单” - 查询订单详情
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public BuyerOrderDetailResp getToSubmitOrderDetails(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        return baseOrderService.getBuyerOrderDetail(order, BuyerInnerStatusEnum.BUYER_TO_SUBMIT);
    }

    /**
     * “待提交订单” - 提交
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 审核结果
     */
    @Override
    public void submitValidateOrder(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_SUBMIT.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1 : 执行工作流任务（修改订单状态、记录内外流转记录）
        if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
            baseOrderTaskService.execOrderChangeProcess(order, OrderConstant.DIRECT_EXEC_TASK);

            // 变更订单完成，将变更订单流程换至交易流程
            orderChangeService.submitOrder(order);
            //订单内部流转记录
            baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.SUBMIT_CHANGE, order.getBuyerInnerStatus(), "");
        } else {
//            //校验合同数量能否满足订单数
//            checkContract(order);

            //(需要审核下单的订单)扣减库存
            baseOrderService.deduceInventoryWithOutStatus(order);

            baseOrderTaskService.execOrderProcess(order, OrderConstant.DIRECT_EXEC_TASK);
            //Step 2 : 提交之后有可能是支付环节，判断是否跳过支付环节
            baseOrderPaymentService.jumpOverOrderPaySerialTasks(order);

            //Step 3: 查询供应商业务员Id
            Long salesResult = baseOrderAuthService.findVendorSalesUserId(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId());

            //Step 4: 以外部状态“待提交”作判断，订单是否已经提交给供应商
            //        如果外部状态为“待提交”，表示订单处于采购商审核过程中，否则表示订单已经提交给了“供应商”
            if (baseOrderService.hasSubmittedToVendor(order)) {
                //Step 4-1: 订单提交时间与供应商用户Id
                order.setSubmitTime(LocalDateTime.now());
                order.setVendorUserId(salesResult);

                if (order.getVersion() == 1) {
                    //Step 4-2: 同步Srm订单数据至合同服务
                    baseOrderService.sendSrmOrder(order);
                }

                //Step 4-3 : 订单外部流转记录
                baseOrderHistoryService.saveVendorOrderOuterHistory(order.getId(), order.getOrderNo(), order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.SUBMIT, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

                //Step 4-4 : 发送供应商实时消息
                baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

                //Step 4-5 ： 供应商交易流程规则配置中的自动取消
                baseOrderScheduleService.scheduleOrderCancel(order.getId(), order.getTask() == null ? BigDecimal.ZERO : order.getTask().getExpireHours());
            }

            //Step 5 : 订单内部流转记录
            baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.SUBMIT, order.getBuyerInnerStatus(), "");

            //Step 6 : 发送采购商实时消息
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

            //Step 7: 如果是拼团订单，向营销服务发送拼团数据，等待拼团结果通知
            baseOrderService.notifyGroupOrder(order);

            //Step 8: 如果是代发货订单,向营销服务发送优惠券信息
            baseOrderService.sendCoupons(order);

            //Step Final: 保存订单
            orderRepository.saveAndFlush(order);
        }
    }

    private void checkContract(OrderDO order) {
        if (Objects.nonNull(order.getOrderContractType())) {
            //获取最新合同
            OrderContractResp orderContractResp = contractFeignService.getOrderContractByOrderId(new CommonIdReq(order.getId()));
            //合同剩余金额不足，无法提交
            if (order.getTotalAmount().compareTo(orderContractResp.getLeftAmount()) > 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUBMIT_CONTRACT_LEFT_AMOUNT_ERROR);
            }
            List<OrderContractProductResp> products = orderContractResp.getProductList();
            //todo 818有坑 会出现重复物料
            Map<String, OrderContractProductResp> orderProductMap = products.stream().collect(Collectors.toMap(OrderContractProductResp::getProductNo, v -> v, (v1, v2) -> v2));
            for (OrderProductDO product : order.getProducts()) {
                OrderContractProductResp contractProduct = orderProductMap.get(product.getProductNo());
                //当前合同没有对应物料，无法提交
                if (Objects.isNull(contractProduct)) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_SUBMIT_CONTRACT_PRODUCT_ERROR);
                }
                //当前合同物料剩余数量小于订单采购数，无法提交
                if (product.getQuantity().compareTo(contractProduct.getLeftCount()) > 0) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_SUBMIT_CONTRACT_PRODUCT_LEFT_COUNT_ERROR);
                }
            }
        }
    }

    /**
     * “待提交订单” - 批量提交
     *
     * @param loginUser 登录用户
     * @param orderIds  接口参数
     * @return 审核结果
     */
    @Transactional
    @Override
    public Void batchSubmitValidateOrders(UserLoginCacheDTO loginUser, List<OrderIdReq> orderIds) {
        List<OrderDO> orders = orderRepository.findAllById(orderIds.stream().map(OrderIdReq::getOrderId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(orders) || orderIds.size() != orders.size() || orders.stream().anyMatch(order -> !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_SUBMIT.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 0: 查询供应商业务员用户Id
        List<OrderMemberDTO> members = orders.stream().map(order -> new OrderMemberDTO(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getVendorMemberId(), order.getVendorRoleId())).distinct().collect(Collectors.toList());
        List<OrderSalesDTO> salesResult = baseOrderAuthService.findVendorSales(members);

        //Step 1: 批量记录上一次的采购商内部状态，用于发送报表统计数据
        Map<Long, Integer> lastBuyerInnerStatusMap = orders.stream().collect(Collectors.toMap(OrderDO::getId, OrderDO::getBuyerInnerStatus));

        //Step 2 : 循环执行提交
        for (OrderDO order : orders) {

            //校验合同数量能否满足订单数
            checkContract(order);

            if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
                baseOrderTaskService.execOrderChangeProcess(order, OrderConstant.DIRECT_EXEC_TASK);

                orderChangeService.submitOrder(order);
                baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.SUBMIT_CHANGE, order.getBuyerInnerStatus(), "");
            } else {

                //(需要审核下单的订单)扣减库存
                baseOrderService.deduceInventoryWithOutStatus(order);

                baseOrderTaskService.execOrderProcess(order, OrderConstant.DIRECT_EXEC_TASK);

                //Step 3 : 提交之后有可能是支付环节，判断是否跳过支付环节
                baseOrderPaymentService.jumpOverOrderPaySerialTasks(order);

                //Step 4: 以外部状态“待提交”作判断，订单是否已经提交给供应商
                //        如果外部状态为“待提交”，表示订单处于采购商审核过程中，否则表示订单已经提交给了“供应商”
                if (baseOrderService.hasSubmittedToVendor(order)) {
                    //Step 4-1: 订单提交时间与供应商用户Id
                    order.setSubmitTime(LocalDateTime.now());
                    order.setVendorUserId(salesResult.stream().filter(s -> s.getBuyerMemberId().equals(order.getBuyerMemberId()) && s.getBuyerRoleId().equals(order.getBuyerRoleId()) && s.getVendorMemberId().equals(order.getVendorMemberId()) && s.getVendorRoleId().equals(order.getVendorRoleId())).map(OrderSalesDTO::getVendorUserId).findFirst().orElse(null));

                    if (order.getVersion() == 1) {
                        //Step 4-2: 同步Srm订单数据至合同服务
                        baseOrderService.sendSrmOrder(order);
                    }

                    //Step 4-3 : 订单外部流转记录
                    baseOrderHistoryService.saveVendorOrderOuterHistory(order.getId(), order.getOrderNo(), order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.SUBMIT, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

                    //Step 4-4 : 发送供应商实时消息
                    baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

                    //Step 4-5： 供应商交易流程规则配置中的自动取消
                    baseOrderScheduleService.scheduleOrderCancel(order.getId(), order.getTask() == null ? BigDecimal.ZERO : order.getTask().getExpireHours());
                }

                //Step 5 : 订单内部流转记录
                baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.SUBMIT, order.getBuyerInnerStatus(), "");

                //Step 6 : 发送采购商实时消息
                baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

                //Step 7: 如果是拼团订单，向营销服务发送拼团数据，等待拼团结果通知
                baseOrderService.notifyGroupOrder(order);

                //Step 8: 如果是代发货订单,向营销服务发送优惠券信息
                baseOrderService.sendCoupons(order);
            }
        }

        //Last step : 保存订单
        orderRepository.saveAll(orders);

        return null;
    }

    /**
     * “待支付订单” - 查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BuyerToPayQueryResp> pageToPayOrders(UserLoginCacheDTO loginUser, OrderValidatePageDataReq pageVO) {
        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("id").descending());
        Specification<OrderDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("buyerMemberId").as(Long.class), loginUser.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("buyerRoleId").as(Long.class), loginUser.getMemberRoleId()));

            //订单编号
            if (StringUtils.hasLength(pageVO.getOrderNo())) {
                list.add(criteriaBuilder.like(root.get("orderNo").as(String.class), "%" + pageVO.getOrderNo().trim() + "%"));
            }

            //订单摘要
            if (StringUtils.hasLength(pageVO.getDigest())) {
                list.add(criteriaBuilder.like(root.get("digest").as(String.class), "%" + pageVO.getDigest().trim() + "%"));
            }

            //供应会员名称
            if (StringUtils.hasLength(pageVO.getMemberName())) {
                list.add(criteriaBuilder.like(root.get("vendorMemberName").as(String.class), "%" + pageVO.getMemberName().trim() + "%"));
            }

            //订单起始时间
            if (StringUtils.hasLength(pageVO.getStartDate())) {
                list.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            }

            //订单结束时间
            if (StringUtils.hasLength(pageVO.getEndDate())) {
                list.add(criteriaBuilder.lessThanOrEqualTo(root.get("createTime").as(LocalDateTime.class), LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
            }

            //订单类型
            if (NumberUtil.notNullOrZero(pageVO.getOrderType())) {
                list.add(criteriaBuilder.equal(root.get("orderType").as(Integer.class), pageVO.getOrderType()));
            }

            //店铺id
            if (NumberUtil.notNullOrZero(pageVO.getBranchId())) {
                list.add(criteriaBuilder.equal(root.get("branchId").as(Long.class), pageVO.getBranchId()));
            }

            //“待支付订单” 的 状态：“未支付”、“支付中”、 “支付失败”、“支付成功，但被供应商确认未到账”
            list.add(criteriaBuilder.or(
                    criteriaBuilder.equal(root.get("buyerInnerStatus").as(Integer.class), BuyerInnerStatusEnum.BUYER_TO_PAY.getCode()),
                    criteriaBuilder.equal(root.get("buyerInnerStatus").as(Integer.class), BuyerInnerStatusEnum.BUYER_PAYING.getCode()),
                    criteriaBuilder.equal(root.get("buyerInnerStatus").as(Integer.class), BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode()),
                    criteriaBuilder.equal(root.get("buyerInnerStatus").as(Integer.class), BuyerInnerStatusEnum.TO_PAY_BALANCE.getCode()),
                    criteriaBuilder.equal(root.get("buyerInnerStatus").as(Integer.class), BuyerInnerStatusEnum.BUYER_PAY_NOT_ARRIVED.getCode()),
                    criteriaBuilder.and(criteriaBuilder.equal(root.get("buyerInnerStatus").as(Integer.class), BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode()),
                            criteriaBuilder.equal(root.get("outerStatus").as(Integer.class), OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode())
                    )
            ));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        Page<OrderDO> pageList = orderRepository.findAll(specification, pageable);

        UserIdFeignReq userIdReq = new UserIdFeignReq();
        userIdReq.setUserId(loginUser.getUserId());
        WrapperResp<Boolean> orderAuth = memberDetailFeign.hasOrderAuth(userIdReq);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerToPayQueryResp queryVO = new BuyerToPayQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            //显示最小的未支付批次：（内部状态为“未支付”或“支付失败”） 或 （“支付成功”且外部状态为“确认未到账”）
            queryVO.setBatchNo(CollectionUtils.isEmpty(order.getPayments()) ? 0 : order.getPayments().stream().filter(payment -> payment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode()) || payment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode()) || (payment.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode()) && payment.getOuterStatus().equals(OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode()))).map(OrderPaymentDO::getBatchNo).min(Comparator.comparingInt(Integer::intValue)).orElse(0));
            queryVO.setBatchCount(CollectionUtils.isEmpty(order.getPayments()) ? 0 : order.getPayments().size());
            queryVO.setPaidAmount(NumberUtil.formatAmount(order.getPaidAmount()));
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            String userType = UserTypeEnum.ADMIN.getCode().equals(order.getUserType()) ? "主账号" : "子账号";
            queryVO.setBuyerAccount(order.getBuyerAccount() + "(" + userType + ")");
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            queryVO.setTotalProductCount(order.getTotalProductCount());
            queryVO.setCurrentPayStage(CommoditySaleModeEnum.SPOT.getCode().equals(order.getSaleMode()) ? "全款支付" : OrderOuterStatusEnum.TO_PAY.getCode().equals(order.getOuterStatus()) || OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode().equals(order.getOuterStatus()) ? "首付款" : "尾款");
            queryVO.setCurrentPayAmount(NumberUtil.formatAmount(order.getTotalAmount().subtract(order.getPaidAmount())));
            // 获取订单支付到期时间
            BigDecimal expireHours = order.getTask().getExpireHours();
            if (expireHours == null || expireHours.compareTo(BigDecimal.ZERO) <= 0) {
                expireHours = BigDecimal.valueOf(0.5); // 设置默认支付时限为24小时
            }
            // 将小时转换为分钟
            BigDecimal expireMinutes = expireHours.multiply(BigDecimal.valueOf(60));
            // 计算支付到期时间
            LocalDateTime payTimeout = order.getCreateTime().plusMinutes(expireMinutes.longValue());
            // 计算时间差（分钟单位）
            long minutesDifference = java.time.Duration.between(LocalDateTime.now(), ObjectUtil.isNotEmpty(order.getPayTimeout()) ? order.getPayTimeout() : payTimeout).toMinutes();
            queryVO.setPayTimeout(minutesDifference < 0 ? "已超时" : "剩余" + minutesDifference + " 分钟");
            if (OrderOuterStatusEnum.TO_PAY_BALANCE.getCode().equals(order.getOuterStatus())) {
                queryVO.setPayTimeout("--");
            }
            if (WrapperUtil.isOk(orderAuth) && !orderAuth.getData()) {
                queryVO.setAmount("***");
                queryVO.setPaidAmount("***");
                queryVO.setCurrentPayAmount("***");
            }
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待支付订单” - 查询订单详情
     *
     * @param idVO 接口参数
     * @return 查询结果
     */
    @Override
    public BuyerOrderDetailResp getToPayOrderDetails(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        if (!(order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode()) || order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.TO_PAY_BALANCE.getCode()) || order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_NOT_ARRIVED.getCode()) || order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAYING.getCode()) || order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode()) || (order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode()) && order.getOuterStatus().equals(OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode())))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_STATUS_IS_NOT_MATCHED);
        }

        return baseOrderService.getBuyerOrderDetail(order);
    }

    /**
     * “待支付订单” - 订单详情 - 查询支付方式与支付渠道列表
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderPayTypeDetailResp> getToPayOrderPayTypes(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        Long vendorMemberId = baiTaiMemberProperties.getSelfMemberId();
        Long vendorRoleId = baiTaiMemberProperties.getSelfRoleId();
        if(idVO.getOrderId() != null){
            OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
            if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
            }
            vendorMemberId = order.getVendorMemberId();
            vendorRoleId = order.getVendorRoleId();
        }
        return baseOrderProcessService.findMemberPayment(loginUser.getMemberId(), loginUser.getMemberRoleId(), vendorMemberId, vendorRoleId);
    }

    /**
     * “待支付订单” - 订单支付
     *
     * @param loginUser 登录用户
     * @param payVO     接口参数
     * @return 支付链接（在线支付）
     */
    @Transactional
    @Override
    public PayResultDetailResp orderPay(UserLoginCacheDTO loginUser, BuyerOrderPayReq payVO) {
        BuyerMergePayReq mergePayVO = new BuyerMergePayReq();
        mergePayVO.setOrderIds(Collections.singletonList(payVO.getOrderId()));
        mergePayVO.setFundMode(payVO.getFundMode());
        mergePayVO.setPayType(payVO.getPayType());
        mergePayVO.setPayChannel(payVO.getPayChannel());
        mergePayVO.setBatchNo(payVO.getBatchNo());
        mergePayVO.setVouchers(payVO.getVouchers());
        mergePayVO.setPayPassword(payVO.getPayPassword());
        mergePayVO.setWeChatCode(payVO.getWeChatCode());
        mergePayVO.setReturnUrl(payVO.getReturnUrl());
        mergePayVO.setName(payVO.getName());
        mergePayVO.setBankAccount(payVO.getBankAccount());
        mergePayVO.setBankDeposit(payVO.getBankDeposit());
        mergePayVO.setPayChannels(payVO.getPayChannels());
        return orderPay(loginUser, mergePayVO);
    }

    /**
     * “待支付订单” - 订单支付
     *
     * @param loginUser 登录用户
     * @param payVO     接口参数
     * @return 支付链接（在线支付）
     */
    @Transactional
    @Override
    public PayResultDetailResp orderPay(UserLoginCacheDTO loginUser, BuyerMergePayReq payVO) {
        //兼容旧接口
        if(CollectionUtils.isEmpty(payVO.getPayTypes())){
            payVO.setPayTypes(Arrays.asList(payVO.getPayType()));
        }
        String ordersKey = payVO.getOrderIds().stream().map(String::valueOf).collect(Collectors.joining("_"));
        log.info("订单支付, 会员: {}, ordersKey: {}, 支付参数: {}", loginUser.getMemberId(), ordersKey, JSONUtil.toJsonStr(payVO));
        if (payVO.getWechatBrowser() != null) {
            // 缓存订单支付时的环境标识
            if (CommonBooleanEnum.YES.getCode().equals(payVO.getWechatBrowser())) {
                log.info("订单支付, 会员: {}, ordersKey: {}, 缓存微信支付参数", loginUser.getMemberId(), ordersKey);
                redisUtils.stringSet(loginUser.getMemberId() + "_" + ordersKey, payVO.getWechatBrowser(), RedisConstant.REDIS_ORDER_INDEX);
            }
        } else {
            String wechatBrowser = redisUtils.stringGet(loginUser.getMemberId() + "_" + ordersKey, RedisConstant.REDIS_ORDER_INDEX);
            log.info("订单支付, 会员: {}, ordersKey: {}, 微信浏览器标识: {}", loginUser.getMemberId(), ordersKey, wechatBrowser);
            if (StringUtils.hasText(wechatBrowser)) {
                payVO.setWechatBrowser(Integer.parseInt(wechatBrowser));
            }
        }
        log.info("订单支付, 会员: {}, ordersKey: {}, wechatBrowser: {}", loginUser.getMemberId(), ordersKey, payVO.getWechatBrowser());
        //建行B2B支付暂不支持App、小程序支付
        if (loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MOBILE.getCode()) && payVO.getPayChannel().equals(OrderPayChannelEnum.CCB_B2B.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_B2B_PAY_DOES_NOT_SUPPORT_APP_PAY);
        }

        List<OrderDO> orders = orderRepository.findAllById(payVO.getOrderIds());
        if (CollectionUtils.isEmpty(orders) || orders.stream().anyMatch(order -> !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        // 查询订单，校验所有订单外部状态都是待支付或者是确认未到帐
        if(orders.stream().anyMatch(order -> OrderOuterStatusEnum.CANCELLED.getCode().equals(order.getOuterStatus()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_TIMEOUT);
        }

        // 查询订单，校验所有订单外部状态都是待支付或者是确认未到帐
        Set<Integer> outerStatus = Stream.of(OrderOuterStatusEnum.TO_PAY.getCode(), OrderOuterStatusEnum.PAYMENT_NOT_ACCOMPLISH.getCode(), OrderOuterStatusEnum.TO_PAY_BALANCE.getCode()).collect(Collectors.toSet());
        if(orders.stream().anyMatch(order -> !outerStatus.contains(order.getOuterStatus()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_STATUS_IS_NOT_MATCHED);
        }
        List<OrderPaymentDO> orderPaymentList = orderPaymentRepository.findByOrderIn(orders);
        Set<Long> paymentIds = orderPaymentList.stream().map(OrderPaymentDO::getId).collect(Collectors.toSet());
        Set<String> tradeNos = orderPaymentList.stream().map(OrderPaymentDO::getTradeNo).collect(Collectors.toSet());
        List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdInAndTradeNoInAndSubStatusAndPayChannelIn(paymentIds, tradeNos, SubPaymentOrderStatusEnum.PENDING.getCode(), OrderPayChannelEnum.ACCOUNT_PAY_LIST);
        if(!CollectionUtils.isEmpty(subOrderPaymentDOS)){
            payFeignService.unfrozenbalance(paymentIds);
        }
        //不需要支付时，直接返回
        if (payVO.getPayType().equals(OrderPayTypeEnum.DOES_NOT_NEED.getCode()) || payVO.getPayChannel().equals(OrderPayChannelEnum.DOES_NOT_NEED.getCode())) {
            return new PayResultDetailResp(Boolean.FALSE, new OrderPayResultDetailResp(), null, null, payVO);
        }

        //Step 1: 记录采购、供应的上一次内部状态，用于发送报表统计数据
        List<OrderStatusDTO> lastStatus = orders.stream().map(order -> new OrderStatusDTO(order.getId(), order.getBuyerInnerStatus(), order.getVendorInnerStatus())).collect(Collectors.toList());

        //Step 2: 支付
        OrderPayResultBO payResult = baseOrderPaymentService.buyerPay(loginUser, orders, payVO.getFundMode(), payVO.getBatchNo(), payVO.getPayTypes(), payVO.getPayChannels(), payVO.getVouchers(), payVO.getPayPassword(), payVO.getWeChatCode(), payVO.getReturnUrl(), payVO.getWechatBrowser(), payVO.getName(), payVO.getBankAccount(), payVO.getBankDeposit());

        //如果是建行支付，将订单状态设置为“支付中”状态
        if (payVO.getPayChannel().equals(OrderPayChannelEnum.CCB_B2B.getCode())) {
            orders.forEach(order -> order.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAYING.getCode()));
        }

        orders.stream().filter(orderDO -> orderDO.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode())).forEach(orderDO -> orderDO.setFinishTime(LocalDateTime.now()));
        orderRepository.saveAll(orders);

        //Step 5: 支付后，将订单的“平台优惠券”信息，同步给结算服务
        baseOrderService.notifyPlatformCoupons(orders);

        OrderPayResultDetailResp orderPayResultDetailResp = new OrderPayResultDetailResp(payResult.getCodeUrl(), payResult.getTradeNo(), payResult.getMpAppid(), payResult.getMpPath(), payResult.getMpUsername(), payResult.getMpVersion());
        orderPayResultDetailResp.setPayFinishedFlag(payResult.getPayFinishedFlag());
        orderPayResultDetailResp.setQrCodeImageBase64(payResult.getQrCodeImageBase64());
        orderPayResultDetailResp.setMpH5(payResult.getMpH5());
        return new PayResultDetailResp(Boolean.TRUE, orderPayResultDetailResp, orders, lastStatus, payVO);
    }


    /**
     * “待支付订单” - 订单支付
     *
     * @param loginUser 登录用户
     * @param payVO     接口参数
     * @return 支付链接（在线支付）
     */
    @Transactional
    @Override
    public MergePayResultDetailResp  mergeOrderPay(UserLoginCacheDTO loginUser, OrderBuyerMergePayReq payVO) {
        //兼容旧接口
        if(CollectionUtils.isEmpty(payVO.getPayTypes())){
            payVO.setPayTypes(Arrays.asList(payVO.getPayType()));
        }
        String ordersKey = payVO.getOrderIds().stream().map(String::valueOf).collect(Collectors.joining("_"));
        log.info("订单支付, 会员: {}, ordersKey: {}, 支付参数: {}", loginUser.getMemberId(), ordersKey, JSONUtil.toJsonStr(payVO));
        if (payVO.getWechatBrowser() != null) {
            // 缓存订单支付时的环境标识
            if (CommonBooleanEnum.YES.getCode().equals(payVO.getWechatBrowser())) {
                log.info("订单支付, 会员: {}, ordersKey: {}, 缓存微信支付参数", loginUser.getMemberId(), ordersKey);
                redisUtils.stringSet(loginUser.getMemberId() + "_" + ordersKey, payVO.getWechatBrowser(), RedisConstant.REDIS_ORDER_INDEX);
            }
        } else {
            String wechatBrowser = redisUtils.stringGet(loginUser.getMemberId() + "_" + ordersKey, RedisConstant.REDIS_ORDER_INDEX);
            log.info("订单支付, 会员: {}, ordersKey: {}, 微信浏览器标识: {}", loginUser.getMemberId(), ordersKey, wechatBrowser);
            if (StringUtils.hasText(wechatBrowser)) {
                payVO.setWechatBrowser(Integer.parseInt(wechatBrowser));
            }
        }
        log.info("订单支付, 会员: {}, ordersKey: {}, wechatBrowser: {}", loginUser.getMemberId(), ordersKey, payVO.getWechatBrowser());
        //建行B2B支付暂不支持App、小程序支付
        if (loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MOBILE.getCode()) && payVO.getPayChannels().contains(OrderPayChannelEnum.CCB_B2B.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CBC_B2B_PAY_DOES_NOT_SUPPORT_APP_PAY);
        }

        List<OrderDO> orders = orderRepository.findAllById(payVO.getOrderIds());
        //尾款支付，校验是否有收货地址
        if(orders.stream().anyMatch(order -> order.getDeliveryType() == null && payVO.getBatchNo() == 2)){
            throw new BusinessException(ResponseCodeEnum.ENHANCE_WHEN_DELIVERY_TYPE_IS_LOGISTICS_RECEIVE_NOT_NULL);
        }
        if (CollectionUtils.isEmpty(orders) || orders.stream().anyMatch(order -> !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        if(orders.stream().anyMatch(orderDO ->  orderDO.getPayTimeout() != null && DateTimeUtil.toMilliSecond(orderDO.getPayTimeout()) < DateTimeUtil.getTodayNow() && payVO.getBatchNo() == 1)){
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_INVALID);
        }
        List<OrderPaymentDO> orderPaymentList = orderPaymentRepository.findByOrderIn(orders);
        Set<Long> paymentIds = orderPaymentList.stream().map(OrderPaymentDO::getId).collect(Collectors.toSet());
        Set<String> tradeNos = orderPaymentList.stream().map(OrderPaymentDO::getTradeNo).collect(Collectors.toSet());
        List<SubOrderPaymentDO> subOrderPaymentDOS = subOrderPaymentRepository.findAllByOrderPaymentIdInAndTradeNoInAndSubStatusAndPayChannelIn(paymentIds, tradeNos, SubPaymentOrderStatusEnum.PENDING.getCode(), OrderPayChannelEnum.ACCOUNT_PAY_LIST);
        //解冻授信/余额
        if(!CollectionUtils.isEmpty(subOrderPaymentDOS)){
            payFeignService.unfrozenbalance(paymentIds);
        }
        //不需要支付时，直接返回
        if (payVO.getPayTypes().stream().anyMatch(payType-> payType.equals(OrderPayTypeEnum.DOES_NOT_NEED.getCode()) || OrderPayTypeEnum.parse(payType).getPayChannels().contains(OrderPayChannelEnum.DOES_NOT_NEED.getCode()))) {
            return new MergePayResultDetailResp(Boolean.FALSE, new OrderPayResultDetailResp(), null, null,payVO);
        }
        //Step 1: 记录采购、供应的上一次内部状态，用于发送报表统计数据
        List<OrderStatusDTO> lastStatus = orders.stream().map(order -> new OrderStatusDTO(order.getId(), order.getBuyerInnerStatus(), order.getVendorInnerStatus())).collect(Collectors.toList());
        if(orders.stream().anyMatch(order -> CommoditySaleModeEnum.ORDER.getCode().equals(order.getSaleMode())) && payVO.getBatchNo() == 1
            && payVO.getPayChannels().stream().anyMatch(paychannel -> OrderPayChannelEnum.CANNOT_PAY_DEPOSIT_LIST.contains(paychannel))){
            throw new BusinessException("该支付渠道不支持支付定金");
        }
        //Step 2: 支付
        OrderPayResultBO payResult = baseOrderPaymentService.buyerPay(loginUser, orders, payVO.getFundMode(), payVO.getBatchNo(), payVO.getPayTypes(), payVO.getPayChannels(), payVO.getVouchers(), payVO.getPayPassword(), payVO.getWeChatCode(), payVO.getReturnUrl(), payVO.getWechatBrowser(), payVO.getName(), payVO.getBankAccount(), payVO.getBankDeposit());

        //如果是建行支付，将订单状态设置为“支付中”状态
        if (payVO.getPayChannels().contains(OrderPayChannelEnum.CCB_B2B.getCode())) {
            orders.forEach(order -> order.setBuyerInnerStatus(BuyerInnerStatusEnum.BUYER_PAYING.getCode()));
        }

        orders.stream().filter(orderDO -> orderDO.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode())).forEach(orderDO -> orderDO.setFinishTime(LocalDateTime.now()));
        orderRepository.saveAll(orders);

        //Step 5: 支付后，将订单的“平台优惠券”信息，同步给结算服务
        baseOrderService.notifyPlatformCoupons(orders);

        OrderPayResultDetailResp orderPayResultDetailResp = new OrderPayResultDetailResp(payResult.getCodeUrl(), payResult.getTradeNo(), payResult.getMpAppid(), payResult.getMpPath(), payResult.getMpUsername(), payResult.getMpVersion());
        orderPayResultDetailResp.setPayFinishedFlag(payResult.getPayFinishedFlag());
        orderPayResultDetailResp.setQrCodeImageBase64(payResult.getQrCodeImageBase64());
        orderPayResultDetailResp.setGetPayResultParam(payResult.getGetPayResultParam());
        orderPayResultDetailResp.setMpH5(payResult.getMpH5());
        return new MergePayResultDetailResp(Boolean.TRUE, orderPayResultDetailResp, orders, lastStatus, payVO);
    }



    /**
     * 订单支付后续处理
     *
     * @param loginUser 登录用户
     * @param detail    支付返回结果
     */
    @Override
    public void orderPayHandler(UserLoginCacheDTO loginUser, PayResultDetailResp detail) {
        if (Objects.isNull(detail) || !detail.isPay() || Objects.isNull(detail.getOrders())) {
            return;
        }

        log.info("订单支付后续处理, 会员: {}, 订单列表: {}", loginUser.getMemberId(), detail.getOrders().stream().map(OrderDO::getOrderNo).collect(Collectors.toList()));

        //Step 4-1 : 订单内、外流转记录
        detail.getOrders().forEach(order -> {
            baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.PAY, order.getBuyerInnerStatus(), "");
            baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.PAY, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

            //Step 4-3: 向消息服务发送采购商、供应商实时消息
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());
            baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());

            //Step 4-5: 如果是拼团订单，向营销服务发送拼团数据，等待拼团结果通知
            baseOrderService.notifyGroupOrder(order);

            //Step 4-5-1: 如果是代发货订单,向营销服务发送优惠券信息
            baseOrderService.sendCoupons(order);

            //Step 4-4 : 订单完成后的操作
            baseOrderService.operateAfterOrderAccomplished(order);
        });
    }


    /**
     * 订单组合支付后续处理
     *
     * @param loginUser 登录用户
     * @param detail    支付返回结果
     */
    @Override
    public void orderPayHandler(UserLoginCacheDTO loginUser, MergePayResultDetailResp detail) {
        if (Objects.isNull(detail) || !detail.isPay() || Objects.isNull(detail.getOrders())) {
            return;
        }

        log.info("订单支付后续处理, 会员: {}, 订单列表: {}", loginUser.getMemberId(), detail.getOrders().stream().map(OrderDO::getOrderNo).collect(Collectors.toList()));

        //Step 4-1 : 订单内、外流转记录
        detail.getOrders().forEach(order -> {
            baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.PAY, order.getBuyerInnerStatus(), "");
            baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), loginUser.getMemberRoleName(), OrderOperationEnum.PAY, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");

            //Step 4-3: 向消息服务发送采购商、供应商实时消息
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());
            baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());

            //Step 4-5: 如果是拼团订单，向营销服务发送拼团数据，等待拼团结果通知
            baseOrderService.notifyGroupOrder(order);

            //Step 4-5-1: 如果是代发货订单,向营销服务发送优惠券信息
            baseOrderService.sendCoupons(order);

            //Step 4-4 : 订单完成后的操作
            baseOrderService.operateAfterOrderAccomplished(order);
        });
    }

    /**
     * “待支付订单” - 查询支付结果
     *
     * @param loginUser 登录用户
     * @param resultVO  接口参数
     * @return 支付结果
     */
    @Override
    public BuyerPayResultDetailResp findPayResult(UserLoginCacheDTO loginUser, BuyerPayResultReq resultVO) {
        return baseOrderPaymentService.findPayResult(resultVO.getTradeNo());
    }

    /**
     * 订单支付 - （线上支付）回调接口
     */
    @Transactional
    @Override
    public void orderPayCallback(OrderPayCallbackFeignReq callbackFeignVO) {
        String tradeNo = callbackFeignVO.getTradeNo();
        String attach = callbackFeignVO.getAttach();
        Boolean paySuccess = callbackFeignVO.getPaySuccess();
        String buyerBizUserId = callbackFeignVO.getBuyerBizUserId();
        String channelTradeNo = callbackFeignVO.getChannelTradeNo();

        //Step 1: 从缓存中读取支付用户信息
        PaymentCacheDTO paymentCache = baseCacheService.findOrderPayment(attach);
        if (paymentCache == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_CACHE_DOES_NOT_EXIST);
        }

        List<OrderDO> orders = orderRepository.findAllById(paymentCache.getPayments().stream().map(OrderPaymentDTO::getOrderId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(orders) || orders.size() != paymentCache.getPayments().size()) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //Step 1: 记录采购、供应的上一次内部状态，用于发送报表统计数据
        List<OrderStatusDTO> lastStatus = orders.stream().map(order -> new OrderStatusDTO(order.getId(), order.getBuyerInnerStatus(), order.getVendorInnerStatus())).collect(Collectors.toList());

        //Step 2: 修改支付记录状态，执行流程
        baseOrderPaymentService.buyerPayCallback(orders, paymentCache, channelTradeNo, paySuccess);

        orders.stream().filter(orderDO -> orderDO.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode())).forEach(orderDO -> orderDO.setFinishTime(LocalDateTime.now()));
        orders.forEach(o -> o.setBuyerBizUserId(buyerBizUserId));
        orderRepository.saveAll(orders);
        orders.forEach(order -> {
            String remark = paymentCache.getPayments().stream().filter(p -> p.getOrderId().equals(order.getId())).map(p -> "支付次数:".concat(String.valueOf(p.getBatchNo()))).findFirst().orElse("");
            if (paySuccess) {
                //Step 4-2-a : 订单内、外流转记录 ： 内部记录：支付成功；外部记录：一次支付成功，一次确认到账
                baseOrderHistoryService.saveBuyerInnerHistory(paymentCache.getMemberId(), paymentCache.getRoleId(), paymentCache.getUserName(), paymentCache.getOrganization(), paymentCache.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.PAY_SUCCESS, order.getBuyerInnerStatus(), remark);
                baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), OrderConstant.SYSTEM_NAME, OrderOperationEnum.CONFIRM_PAYMENT, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), remark);
            } else {
                //Step 4-2-b : 订单内部流转记录 ： 支付失败
                baseOrderHistoryService.saveBuyerInnerHistory(paymentCache.getMemberId(), paymentCache.getRoleId(), paymentCache.getUserName(), paymentCache.getOrganization(), paymentCache.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.PAY_FAILED, order.getBuyerInnerStatus(), remark);
            }

            //Step 4-4: 向消息服务发送采购商、供应商实时消息
            baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());
            baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());

            //Step 4-5： 如果是拼团订单，通知营销服务
            baseOrderService.notifyGroupOrder(order);

            //Step 4-5-1: 如果是代发货订单,向营销服务发送优惠券信息
            baseOrderService.sendCoupons(order);

            //Step 4-6 : 订单完成后的操作
            baseOrderService.operateAfterOrderAccomplished(order);

            //订单确认时维护送货计划物料数据
            if (paySuccess) {
                deliveryPlanService.saveDeliveryPlanProduct(order);
            }

        });
    }

    /**
     * “待发货订单” - 查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BuyerToDeliveryQueryResp> pageToDeliveryOrders(UserLoginCacheDTO loginUser, OrderValidatePageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), null, OrderOuterStatusEnum.TO_CONFIRM_DELIVERY.getCode(), null, pageVO.getCurrent(), pageVO.getPageSize());
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerToDeliveryQueryResp queryVO = new BuyerToDeliveryQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setShopId(order.getShopId());
            queryVO.setShopEnvironment(order.getShopEnvironment());
            //是否显示“售后”：当前订单是参与了拼团活动的订单，且拼团状态为拼团失败，且订单的外部状态为“待确认发货
            queryVO.setShowAfterSales(baseOrderService.canAfterSaleOrder(order));
            //是否可以“邀请拼团”
            queryVO.setShowInvite(baseOrderService.canBuyerInviteGroup(order));
            queryVO.setGroupId(queryVO.getShowInvite() ? baseOrderPromotionService.findGroupOrderRecordId(order) : 0L);
            queryVO.setSkuId(queryVO.getShowInvite() ? order.getProducts().stream().map(OrderProductDO::getSkuId).findFirst().orElse(0L) : 0L);
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待发货订单” - 订单详情 - 邀请好友拼团 - 生成小程序码分享链接
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public String createDeliveryMiniAppCode(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //获取小程序配置参数，param：二维码参数，page: 跳转页面路径 = order/myCommodityDetails 。小程序未发布增加这个参数会报错，暂时定义为空
        return baseOrderService.createMiniCode(order.getId().toString(), "");
    }

    /**
     * “待新增采购收货单” - 查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BuyerOrderReceiveQueryResp> pageCreateReceiveOrders(UserLoginCacheDTO loginUser, BuyerOrderReceivePageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), null, OrderOuterStatusEnum.TO_RECEIVE.getCode(), null, pageVO.getCurrent(), pageVO.getPageSize());

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerOrderReceiveQueryResp queryVO = new BuyerOrderReceiveQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            //显示最后一次的发货批次，即发货记录的条数
            queryVO.setBatchNo(order.getDeliveries().size());
            queryVO.setReceiptNo("");
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待新增采购收货单” - 创建收货单
     *
     * @param loginUser 登录用户
     * @param orderVO   接口参数
     * @return 操作结果
     */
    @Transactional
    @Override
    public Void createReceiveOrder(UserLoginCacheDTO loginUser, BuyerCreateReceiveReq orderVO) {
        OrderDO order = orderRepository.findById(orderVO.getOrderId()).orElse(null);
        if (order == null || !order.getVendorMemberId().equals(loginUser.getMemberId()) || !order.getVendorRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //执行工作流任务（修改订单状态、记录内外流转记录）
        baseOrderTaskService.execOrderProcess(order);

        SellDeliveryGoodsDO deliveryDO = new SellDeliveryGoodsDO();
        deliveryDO.setDigest(orderVO.getDigest());
        deliveryDO.setCreateTime(LocalDateTime.parse(orderVO.getOrderTime(), OrderConstant.DEFAULT_TIME_FORMATTER));
        deliveryDO.setInventoryName(orderVO.getInventoryName());
        deliveryDO.setInventoryRole(orderVO.getInventoryRole());
        deliveryDO.setOrderId(orderVO.getOrderId());
        deliveryDO.setOrderNo(order.getOrderNo());
        deliveryDO.setRemark(orderVO.getRemark());
        deliveryDO.setVendorMemberId(order.getVendorMemberId());
        deliveryDO.setVendorRoleId(order.getVendorRoleId());
        deliveryDO.setVendorMemberName(order.getVendorMemberName());
        //没有该字段，设置默认值
        deliveryDO.setBuyerMemberId(0L);
        deliveryDO.setBuyerRoleId(0L);
        deliveryDO.setBuyerMemberName("");
        //商品明细
        deliveryDO.setDetailList(orderVO.getDetailList().stream().map(detail -> {
            SellDeliveryGoodsDetailDO detailDO = new SellDeliveryGoodsDetailDO();
            detailDO.setProductId(detail.getProductId());
            detailDO.setName(detail.getName());
            detailDO.setCategory(detail.getCategory());
            detailDO.setBrand(detail.getBrand());
            detailDO.setUnit(detail.getUnit());
            detailDO.setOrderQuantity(detail.getOrderQuantity());
            detailDO.setPrice(detail.getPrice());
            detailDO.setDeliveryQuantity(detail.getDeliveryQuantity());
            detailDO.setAmount(detail.getAmount());
            detailDO.setWeight(detail.getWeight());
            detailDO.setDeliveryGoods(deliveryDO);
            return detailDO;
        }).collect(Collectors.toSet()));

        //保存发货单
        sellDeliveryGoodsRepository.save(deliveryDO);

        //保存订单，修改订单状态
        orderRepository.saveAndFlush(order);

        return null;
    }

    /**
     * “待新增采购收货单” - 查看详情
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public BuyerReceiveDetailResp getReceiveDetail(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        SellDeliveryGoodsDO delivery = sellDeliveryGoodsRepository.findFirstByOrderId(idVO.getOrderId());
        if (delivery == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_SELL_DELIVERY_BILL_DOES_NOT_EXIST);
        }
        BuyerReceiveDetailResp detailVO = new BuyerReceiveDetailResp();

        detailVO.setInventoryName(delivery.getInventoryName());
        detailVO.setInventoryRole(delivery.getInventoryRole());
        detailVO.setDigest(delivery.getDigest());
        detailVO.setCreateTime(delivery.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
        detailVO.setRemark(delivery.getRemark());
        detailVO.setOrderNo(delivery.getOrderNo());
        detailVO.setOrderId(idVO.getOrderId());
        detailVO.setVendorMemberId(delivery.getVendorMemberId());
        detailVO.setVendorRoleId(delivery.getVendorRoleId());
        detailVO.setVendorMemberName(delivery.getVendorMemberName());
        //收货单商品明细
        detailVO.setDetailList(delivery.getDetailList().stream().map(detail -> {
            SellDeliveryGoodsDetailResp goodsDetailVO = new SellDeliveryGoodsDetailResp();
            goodsDetailVO.setId(detail.getId());
            goodsDetailVO.setProductId(detail.getProductId());
            goodsDetailVO.setName(detail.getName());
            goodsDetailVO.setCategory(detail.getCategory());
            goodsDetailVO.setBrand(detail.getBrand());
            goodsDetailVO.setUnit(detail.getUnit());
            goodsDetailVO.setOrderQuantity(detail.getOrderQuantity());
            goodsDetailVO.setPrice(detail.getPrice());
            goodsDetailVO.setDeliveryQuantity(detail.getDeliveryQuantity());
            goodsDetailVO.setAmount(detail.getAmount());
            goodsDetailVO.setWeight(detail.getWeight());
            return goodsDetailVO;
        }).collect(Collectors.toList()));

        //订单发货人信息
        detailVO.setConsignee(baseOrderConsigneeService.getOrderConsignee(idVO.getOrderId()));

        return detailVO;
    }

    /**
     * “待新增采购收货单” - 提交审核
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public Void validateToReceiveOrder(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_VALIDATE_DEPOT.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        //执行工作流任务（修改订单状态、记录内外流转记录）
        baseOrderTaskService.execOrderProcess(order);

        orderRepository.saveAndFlush(order);

        return null;
    }

    /**
     * “待确认收货订单” -查询订单列表
     *
     * @param loginUser 登录用户
     * @param pageVO    接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BuyerReceiveQueryResp> pageToReceiveOrders(UserLoginCacheDTO loginUser, OrderValidatePageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), BuyerInnerStatusEnum.TO_RECEIVE.getCode(), null, null, pageVO.getCurrent(), pageVO.getPageSize(), pageVO.getBranchId());

        // 是否具有支付及查看订单价格权限
        UserIdFeignReq userIdReq = new UserIdFeignReq();
        userIdReq.setUserId(loginUser.getUserId());
        WrapperResp<Boolean> orderAuth = memberDetailFeign.hasOrderAuth(userIdReq);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerReceiveQueryResp queryVO = new BuyerReceiveQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            //显示最后一次的发货批次，即发货记录的条数
            queryVO.setBatchNo(order.getDeliveries().size());
            queryVO.setReceiptNo("");
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            String userType = UserTypeEnum.ADMIN.getCode().equals(order.getUserType()) ? "主账号" : "子账号";
            queryVO.setBuyerAccount(order.getBuyerAccount() + "(" + userType + ")");
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            queryVO.setTotalWeight(order.getTotalWeight());
            queryVO.setTotalProductCount(order.getTotalProductCount());
            if (WrapperUtil.isOk(orderAuth) && !orderAuth.getData()) {
                queryVO.setAmount("***");
            }
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * 待自提订单 - 查询订单列表
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<BuyerReceiveQueryResp> pageToPickupOrders(UserLoginCacheDTO loginUser, OrderValidatePageDataReq pageVO) {
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), BuyerInnerStatusEnum.TO_SELF_PICKUP.getCode(), null, null, pageVO.getCurrent(), pageVO.getPageSize(), pageVO.getBranchId());

        // 是否具有支付及查看订单价格权限
        UserIdFeignReq userIdReq = new UserIdFeignReq();
        userIdReq.setUserId(loginUser.getUserId());
        WrapperResp<Boolean> orderAuth = memberDetailFeign.hasOrderAuth(userIdReq);

        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(order -> {
            BuyerReceiveQueryResp queryVO = new BuyerReceiveQueryResp();
            queryVO.setOrderId(order.getId());
            queryVO.setOrderNo(order.getOrderNo());
            queryVO.setCreateTime(order.getCreateTime().format(OrderConstant.DEFAULT_TIME_FORMATTER));
            queryVO.setDigest(order.getDigest());
            queryVO.setMemberName(order.getVendorMemberName());
            queryVO.setAmount(NumberUtil.formatAmount(order.getTotalAmount()));
            //显示最后一次的发货批次，即发货记录的条数
            queryVO.setBatchNo(order.getDeliveries().size());
            queryVO.setReceiptNo("");
            queryVO.setOrderType(order.getOrderType());
            queryVO.setOrderTypeName(OrderTypeEnum.getNameByCode(order.getOrderType()));
            String userType = UserTypeEnum.ADMIN.getCode().equals(order.getUserType()) ? "主账号" : "子账号";
            queryVO.setBuyerAccount(order.getBuyerAccount() + "(" + userType + ")");
            queryVO.setInnerStatus(order.getBuyerInnerStatus());
            queryVO.setInnerStatusName(BuyerInnerStatusEnum.getNameByCode(order.getBuyerInnerStatus()));
            queryVO.setOuterStatus(order.getOuterStatus());
            queryVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            queryVO.setVersionName(OrderConstant.VERSION_PREFIX + order.getVersion());
            queryVO.setTotalWeight(order.getTotalWeight());
            queryVO.setTotalProductCount(order.getTotalProductCount());
            if (WrapperUtil.isOk(orderAuth) && !orderAuth.getData()) {
                queryVO.setAmount("***");
            }
            return queryVO;
        }).collect(Collectors.toList()));
    }

    /**
     * “待确认收货订单” -查询订单详情
     *
     * @param loginUser 登录用户
     * @param idVO      接口参数
     * @return 查询结果
     */
    @Override
    public BuyerOrderDetailResp getToReceiveOrderDetails(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).orElse(null);
        if (order == null || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        return baseOrderService.getBuyerOrderDetail(order);
    }

    /**
     * “待确认收货订单” - 确认收货
     *
     * @param loginUser 登录用户
     * @param receiveVO 接口参数
     * @return 查询结果
     */
    @Transactional
    @Override
    public Void receiveOrder(UserLoginCacheDTO loginUser, BuyerReceiveReq receiveVO) {
        return receiveOrder(loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), loginUser.getMemberRoleName(), receiveVO.getOrderId(), receiveVO.getBatchNo(), receiveVO.getReceiveBill(), receiveVO.getWarehousingOrderProductDetailVOS());
    }

    /**
     * “待确认收货订单” - 确认收货（此接口同时是： 自动收货功能调用的接口）
     *
     * @param userName                          登录用户姓名
     * @param organizationName                  组织机构名称
     * @param jobTitle                          职位
     * @param roleName                          会员角色名称
     * @param orderId                           订单Id
     * @param batchNo                           发货批次
     * @param receiveBill                       收货回单url地址
     * @param warehousingOrderProductDetailResp 自动入库订单物料信息VO
     * @return 查询结果
     */
    @Transactional
    @Override
    public Void receiveOrder(String userName, String organizationName, String jobTitle, String roleName, Long orderId, Integer batchNo, String receiveBill, List<WarehousingOrderProductDetailResp> warehousingOrderProductDetailResp) {
        OrderDO order = orderRepository.findById(orderId).orElse(null);
        if (order == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        try {
            //Step 1: 校验发货条件
            //TransferOrderReceiveBO receiveCheckResult = baseOrderProductService.checkOrderReceive(order);
            UpdateSalesOrderGoodsStatus updateSalesOrderGoodsStatus = new UpdateSalesOrderGoodsStatus();
            updateSalesOrderGoodsStatus.setDdzt(VendorInnerStatusEnum.ACCOMPLISHED.getName());
            updateSalesOrderGoodsStatus.setGxsj(DateTimeUtil.getCurrentDateTime());
            updateSalesOrderGoodsStatus.setXsddh(order.getOrderNo());
            eosApiService.updateSalesGoodsOrderStatus(updateSalesOrderGoodsStatus);
        }catch (Exception e){

        }
        //Step 2: 当前订单收货
        receiveOrder(userName, organizationName, jobTitle, roleName, order, batchNo, receiveBill, warehousingOrderProductDetailResp);
        //Step 3: 根据检查结果，判断是否要将转单后订单收货

        // 发送订单完成分佣消息
        orderCommissionMqUtil.sendOrderCompletedMessage(order.getOrderNo(), order.getBuyerUserId(), order.getBuyerMemberId(), order.getBuyerRoleId());

        return null;
    }

    /**
     * “待确认收货订单” - 确认收货
     *
     * @param userName                          登录用户姓名
     * @param organizationName                  组织机构名称
     * @param jobTitle                          职位
     * @param roleName                          会员角色名称
     * @param order                             订单
     * @param batchNo                           发货批次
     * @param receiveBill                       收货回单url地址
     * @param warehousingOrderProductDetailResp 自动入库订单物料信息VO
     * @return 收货结果
     */
    @Transactional
    @Override
    public Void receiveOrder(String userName, String organizationName, String jobTitle, String roleName, OrderDO order, Integer batchNo, String receiveBill, List<WarehousingOrderProductDetailResp> warehousingOrderProductDetailResp) {
        //Step 1 : 校验参数，修改发货记录统计数据
        OrderReceiveBO checkResult = baseOrderDeliveryService.checkBuyerOrderReceive(order, batchNo, receiveBill);

        //前端已做校验(以下类型仅作为记录)
        //如果订单类型是 合同采购订单、请购单订单、物料订单、询价报价下单、进货单下单、手工下单、合并订单下单、积分兑换订单下单、渠道直采手工下单、渠道直采进货单下单、渠道现货手工下单、渠道现货进货单下单、渠道积分兑换订单下单
        //Step 2 : 通知商品服务，创建采购入库单
        if (!CollectionUtils.isEmpty(warehousingOrderProductDetailResp)) {
            baseOrderService.createInboundOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), warehousingOrderProductDetailResp, order.getOrderNo(), checkResult.getReceiptNo());
        }

        // 记录采购、供应的上一次内部状态，用于发送报表统计数据
        int lastBuyerInnerStatus = order.getBuyerInnerStatus();
        int lastVendorInnerStatus = order.getVendorInnerStatus();

        //Step 3 : 执行工作流任务（修改订单状态、记录内外流转记录）
        baseOrderTaskService.execOrderProcess(order, checkResult.getTaskJumpParameter());

        //Step 4 : 收货全部完成之后有可能是支付环节，判断是否跳过支付环节
        if (checkResult.getReceiveDone()) {
            baseOrderPaymentService.jumpOverOrderPaySerialTasks(order);
        }
        // 添加完成时间
        if (order.getOuterStatus().equals(OrderOuterStatusEnum.ACCOMPLISHED.getCode())) {
            order.setFinishTime(LocalDateTime.now());
        }
        orderRepository.saveAndFlush(order);

        //Step 5: 如果是Srm订单、采购请购单收货，发送收发货记录给结算服务
        baseOrderDeliveryService.notifySrmOrderReceive(order, batchNo);

        //Step 6 : 订单内、外流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), userName, organizationName, jobTitle, order.getId(), order.getOrderNo(), OrderOperationEnum.CONFIRM_RECEIPT, order.getBuyerInnerStatus(), OrderStringEnum.RECEIPT_NUMBER.getName().concat(checkResult.getReceiptNo()));
        baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), roleName, OrderOperationEnum.CONFIRM_RECEIPT, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), OrderStringEnum.RECEIPT_NUMBER.getName().concat(checkResult.getReceiptNo()));

        //Step 7: 向消息服务发送采购商实时消息
        baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());
        baseOrderService.messageVendorOrder(order.getVendorMemberId(), order.getVendorRoleId(), order.getVendorUserId(), order.getVendorInnerStatus(), order.getOrderNo(), order.getDigest());

        //Step 8 : 订单完成后的操作
        baseOrderService.operateAfterOrderAccomplished(order);

        return null;
    }

    /**
     * 更新拼团订单状态
     *
     * @param userName         登录用户姓名
     * @param organizationName 组织机构名称
     * @param jobTitle         职位
     * @param roleName         会员角色名称
     * @param orderId          订单Id
     * @param promotionStatus  营销活动状态
     * @param groupId          拼团Id
     * @return 处理结果
     */
    @Transactional
    @Override
    public Void updateGroupOrderStatus(String userName, String organizationName, String jobTitle, String roleName, Long orderId, OrderPromotionStatusEnum promotionStatus, Long groupId) {
        log.info("开始处理拼团回调 => promotionStatus:" + promotionStatus.getName() + ", promotionCode:" + promotionStatus.getCode() + ", groupId:" + groupId);
        OrderDO order = orderRepository.findById(orderId).orElse(null);
        if (order == null) {
            return null;
        }

        //如果状态不是“等待通知”，返回
//        if(!order.getPromotionStatus().equals(OrderPromotionStatusEnum.GROUP_WAITING_CONFIRM.getCode())) {
//            logger.info("开始处理拼团回调 => 状态不是“等待通知”，返回");
//            return Wrapper.success();
//        }

        //更新状态和拼团Id
        order.setPromotionStatus(promotionStatus.getCode());
        order.setGroupId(NumberUtil.isNullOrZero(groupId) ? 0L : groupId);

        //如果订单拼团失败，取消订单
        if (promotionStatus.equals(OrderPromotionStatusEnum.GROUP_FAILED)) {
            order.setBuyerInnerStatus(BuyerInnerStatusEnum.CANCELLED.getCode());
            order.setVendorInnerStatus(VendorInnerStatusEnum.CANCELLED.getCode());
            order.setOuterStatus(OrderOuterStatusEnum.CANCELLED.getCode());
        }

        orderRepository.saveAndFlush(order);

        //如果是拼团失败，退款，返还库存
        if (promotionStatus.equals(OrderPromotionStatusEnum.GROUP_FAILED)) {
            //“拼团失败”的内、外流转记录
            baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), userName, organizationName, jobTitle, order.getId(), order.getOrderNo(), OrderOperationEnum.GROUP_FAILED, order.getBuyerInnerStatus(), "");
            baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), roleName, OrderOperationEnum.GROUP_FAILED, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");


            baseOrderPaymentService.groupOrderRefund(order);
            //“退款成功”的内、外流转记录
            baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), userName, organizationName, jobTitle, order.getId(), order.getOrderNo(), OrderOperationEnum.REFUND, order.getBuyerInnerStatus(), OrderStringEnum.REFUND_SUCCESSFULLY.getName());
            baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), roleName, OrderOperationEnum.REFUND, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), OrderStringEnum.REFUND_SUCCESSFULLY.getName());

            //将订单未发货的数量返回库存
            baseOrderService.resumeInventory(order);
            //通知营销服务，返还营销活动、优惠券记录
            baseOrderService.resumePromotions(order);
        } else if (promotionStatus.equals(OrderPromotionStatusEnum.GROUP_SUCCESS)) {
            //“拼团成功”的内、外流转记录
            baseOrderHistoryService.saveBuyerInnerHistory(order.getBuyerMemberId(), order.getBuyerRoleId(), userName, organizationName, jobTitle, order.getId(), order.getOrderNo(), OrderOperationEnum.GROUP_SUCCESS, order.getBuyerInnerStatus(), "");
            baseOrderHistoryService.saveBuyerOrderOuterHistory(order.getId(), order.getOrderNo(), order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerMemberName(), roleName, OrderOperationEnum.GROUP_SUCCESS, order.getOuterStatus(), OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()), "");
        }

        return null;
    }

    /**
     * 订单分页查询 - 通用 - 所有订单类型下拉框列表
     *
     * @return 查询结果
     */
    @Override
    public List<DropdownItemResp> getAllOrderType() {
        return baseOrderService.listOrderTypes();
    }

    /**
     * 提交订单 - 查询满额包邮的商品总运费
     *
     * @param loginUser 登录用户
     * @param freightVO 接口参数
     * @return 查询结果
     */
    @Override
    public BigDecimal findProductFreeFreight(UserLoginCacheDTO loginUser, OrderProductFreeFreightReq freightVO) {
        return logisticsFeignService.findOrderFreeFreight(loginUser, freightVO);
    }

    /**
     * 所有付款方式下拉框列表
     *
     * @return 查询结果
     */
    @Override
    public List<DropdownItemResp> getAllPaymentType() {
        return baseOrderService.listPaymentTypes();
    }

    @Override
    public PageDataResp<OrderPageQueryResp> pageToConfirmElectronicContract(UserLoginCacheDTO loginUser, OrderPageDataReq pageVO) {
        // step 1: 查询数据库数据
        Page<OrderDO> pageList = baseOrderService.pageBuyerOrders(loginUser, pageVO.getOrderNo(), pageVO.getDigest(), pageVO.getMemberName(), pageVO.getStartDate(), pageVO.getEndDate(), pageVO.getOrderType(), BuyerInnerStatusEnum.BUYER_TO_CONFIRM_CONTRACT.getCode(), null, null, pageVO.getCurrent(), pageVO.getPageSize());
        // step 2: 组装数据
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(this::builderOrderPageQueryVO).collect(Collectors.toList()));
    }

    @Override
    public BuyerOrderDetailResp getToConfirmElectronicContractDetails(UserLoginCacheDTO loginUser, OrderIdReq idVO) {
        OrderDO order = orderRepository.findById(idVO.getOrderId()).filter(f -> Objects.equals(loginUser.getMemberId(), f.getBuyerMemberId()) && Objects.equals(loginUser.getMemberRoleId(), f.getBuyerRoleId())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST));
        return baseOrderService.getBuyerOrderDetail(order, BuyerInnerStatusEnum.BUYER_TO_CONFIRM_CONTRACT);
    }

    @Override
    @Transactional
    public Void updateToConfirmElectronicContractPromisedDeliveryDate(UserLoginCacheDTO loginUser, OrderProductPromisedDeliveryDateReq updateVO) {
        // 校验数据 外部状态【待确定电子合同】， 采购商内部状态【待确认电子合同】
        OrderDO order = orderRepository.findById(updateVO.getOrderId()).filter(f -> Objects.equals(loginUser.getMemberId(), f.getBuyerMemberId()) && Objects.equals(loginUser.getMemberRoleId(), f.getBuyerRoleId()) && Objects.equals(OrderOuterStatusEnum.TO_CONFIRM_CONTRACT.getCode(), f.getOuterStatus()) && Objects.equals(BuyerInnerStatusEnum.BUYER__CONFIRMED.getCode(), f.getBuyerInnerStatus())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST));
        // 更新订单商品 承诺交期
        Set<OrderProductDetailPromisedDeliveryDateReq> updateList = updateVO.getUpdateList();
        Set<Long> updateOrderProductIds = updateList.stream().map(OrderProductDetailPromisedDeliveryDateReq::getOrderProductId).collect(Collectors.toSet());
        Set<OrderProductDO> updateOrderProducts = order.getProducts().stream().filter(f -> updateOrderProductIds.contains(f.getId())).peek(p -> updateList.stream().filter(f -> Objects.equals(p.getId(), f.getOrderProductId())).findFirst().ifPresent(e -> {
            LocalDateTime deliverTime = DateUtil.parseLocalDateTime(e.getPromisedDeliveryDate(), "yyyy-MM-dd");
            p.setPromisedDeliveryDate(deliverTime);
        })).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(updateOrderProductIds)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }
        orderProductRepository.saveAll(updateOrderProducts);
        return null;
    }

    @Override
    @Transactional
    public Void confirmElectronicContractValidateOrder(UserLoginCacheDTO loginUser, OrderAgreeReq agreeVO) {
        // step 1: 查询订单
        OrderDO order = orderRepository.findById(agreeVO.getOrderId()).filter(f -> Objects.equals(loginUser.getMemberId(), f.getBuyerMemberId()) && Objects.equals(loginUser.getMemberRoleId(), f.getBuyerRoleId()) && Objects.equals(OrderOuterStatusEnum.TO_CONFIRM_CONTRACT.getCode(), f.getOuterStatus())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST));
        // step 2: 参数判断
        if (agreeVO.getAgree().equals(OrderConstant.DISAGREE) && !StringUtils.hasLength(agreeVO.getReason())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_VALIDATE_REASON_CAN_NOT_BE_EMPTY);
        }
//        // step 3: 调用合同签订
//        if (agreeVO.getAgree().equals(OrderConstant.AGREE)) {
//            baseOrderContractTextService.contractConfirm(order, loginUser);
//        }
        // step 4: 执行工作流
        int lastBuyerInnerStatus = order.getBuyerInnerStatus();
        baseOrderTaskService.execOrderProcess(order, agreeVO.getAgree());

        orderRepository.saveAndFlush(order);

        // step 5: 订单内部流转记录
        baseOrderHistoryService.saveBuyerInnerHistory(loginUser.getMemberId(), loginUser.getMemberRoleId(), loginUser.getUserName(), loginUser.getOrgName(), loginUser.getJobTitle(), order.getId(), order.getOrderNo(), OrderOperationEnum.CONFIRM_CONTRACT, order.getBuyerInnerStatus(), StringUtils.hasLength(agreeVO.getReason()) ? agreeVO.getReason() : "");

        // step 6： 发送采购商实时消息
        baseOrderService.messageBuyerOrder(order.getBuyerMemberId(), order.getBuyerRoleId(), order.getBuyerUserId(), order.getBuyerInnerStatus(), order.getOrderNo(), order.getDigest());

        return null;
    }

    @Override
    public Void batchConfirmElectronicContractValidateOrder(UserLoginCacheDTO loginUser, List<OrderIdReq> orderIds) {
        List<OrderDO> orders = orderRepository.findAllById(orderIds.stream().map(OrderIdReq::getOrderId).distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(orders) || orderIds.size() != orders.size() || orders.stream().anyMatch(order -> !order.getOuterStatus().equals(OrderOuterStatusEnum.TO_CONFIRM_CONTRACT.getCode()) || !order.getBuyerInnerStatus().equals(BuyerInnerStatusEnum.BUYER_TO_CONFIRM_CONTRACT.getCode()) || !order.getBuyerMemberId().equals(loginUser.getMemberId()) || !order.getBuyerRoleId().equals(loginUser.getMemberRoleId()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        // step 2: 组装遍历调用确认电子合同
        for (OrderDO order : orders) {
            OrderAgreeReq agreeVO = new OrderAgreeReq();
            agreeVO.setAgree(OrderConstant.AGREE);
            agreeVO.setReason("");
            agreeVO.setOrderId(order.getId());
            AopProxyUtil.getCurrentProxy(this.getClass()).confirmElectronicContractValidateOrder(loginUser, agreeVO);
        }
        return null;
    }

    @Override
    public Void changeRequisitionOrder(UserLoginCacheDTO loginUser, RequisitionOrderChangeReq orderVO) {
        return orderChangeService.changeRequisitionOrder(loginUser, orderVO);
    }

    @Override
    public Void changeMaterielOrder(UserLoginCacheDTO loginUser, MaterielOrderChangeReq orderVO) {
        return orderChangeService.changeMaterielOrder(loginUser, orderVO);
    }

    @Override
    public List<ReportItemResp> homeBacklog(UserLoginCacheDTO loginUser) {
        //查询采购订单数据
        List<OrderDO> data = baseOrderService.findOrderByLoginUser(loginUser);
        //开始处理采购订单相关数据统计
        List<ReportItemResp> resultVo = new ArrayList<>();
        //拼接map值
        Map<Integer, Integer> reportMap = new HashMap<>();
        //待提交审核订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_VALIFY_COMMIT.getCode());
        //待审核订单（一级）
        reportMap.put(BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_ONE.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_VALIFY_STEP1.getCode());
        //待审核订单（二级）
        reportMap.put(BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_TWO.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_VALIFY_STEP2.getCode());
        //待确认订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_SUBMIT.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_COMMIT.getCode());
        //待确认电子合同订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_CONFIRM_CONTRACT.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_CONFIRM_CONTRACT.getCode());
        //待支付订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_PAY.getCode());
        //待确认收货订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_CONFIRM_RECEIVE_GOODS.getCode());
        //待归档订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_ARCHIVE.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_ARCHIVE.getCode());
        for (Integer code : reportMap.keySet()) {
            ReportItemResp verifyCommitItemVO = new ReportItemResp();
            if (!CollectionUtils.isEmpty(data)) {
                List<OrderDO> valifyCommitOrderList = data.stream().filter(order -> Objects.equals(order.getBuyerInnerStatus(), code)).collect(Collectors.toList());
                if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode())) {
                    List<Integer> orderTypes = Arrays.asList(OrderTypeEnum.MANUAL_MATERIAL_ORDER_PLACEMENT.getCode(), OrderTypeEnum.MATERIAL_SAMPLE_ORDER.getCode());
                    valifyCommitOrderList = valifyCommitOrderList.stream().filter(order -> orderTypes.contains(order.getOrderType())).collect(Collectors.toList());
                }
                //待支付
                if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_TO_PAY.getCode())) {
                    valifyCommitOrderList = valifyCommitOrderList.stream().filter(order -> (Objects.equals(order.getBuyerInnerStatus(), BuyerInnerStatusEnum.BUYER_TO_PAY.getCode()) ||
                                    Objects.equals(order.getBuyerInnerStatus(), BuyerInnerStatusEnum.BUYER_PAYING.getCode()) ||
                                    Objects.equals(order.getBuyerInnerStatus(), BuyerInnerStatusEnum.BUYER_PAY_FAIL.getCode()) ||
                                    Objects.equals(order.getBuyerInnerStatus(), BuyerInnerStatusEnum.BUYER_PAY_SUCCESS.getCode())) &&
                                    Objects.equals(order.getOuterStatus(), OrderOuterStatusEnum.TO_PAY.getCode()))
                            .collect(Collectors.toList());
                }
                verifyCommitItemVO.setCount(CollectionUtils.isEmpty(valifyCommitOrderList) ? 0L : valifyCommitOrderList.size());
                if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_TO_PAY.getCode())) {
                    //待支付
                    OrderValidatePageDataReq vo = new OrderValidatePageDataReq();
                    vo.setCurrent(1);
                    vo.setPageSize(10);
                    verifyCommitItemVO.setCount(this.pageToPayOrders(loginUser, vo).getTotalCount());
                }
                if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode())) {
                    //新增订单
                    BuyerOrderCreatePageDataReq vo = new BuyerOrderCreatePageDataReq();
                    vo.setCurrent(1);
                    vo.setPageSize(10);
                    verifyCommitItemVO.setCount(this.materielPage(loginUser, vo).getTotalCount());
                }
                if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_ONE.getCode())) {
                    //一级审核
                    OrderPageDataReq vo = new OrderPageDataReq();
                    vo.setCurrent(1);
                    vo.setPageSize(10);
                    verifyCommitItemVO.setCount(this.pageToValidateGradeOneOrders(loginUser, vo).getTotalCount());
                }
                if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_VALIDATE_GRADE_TWO.getCode())) {
                    //二级审核
                    OrderPageDataReq vo = new OrderPageDataReq();
                    vo.setCurrent(1);
                    vo.setPageSize(10);
                    verifyCommitItemVO.setCount(this.pageToValidateGradeTwoOrders(loginUser, vo).getTotalCount());
                }
                if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_TO_SUBMIT.getCode())) {
                    //待确认订单
                    OrderPageDataReq vo = new OrderPageDataReq();
                    vo.setCurrent(1);
                    vo.setPageSize(10);
                    verifyCommitItemVO.setCount(this.pageToSubmitOrders(loginUser, vo).getTotalCount());
                }
                if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode())) {
                    OrderValidatePageDataReq vo = new OrderValidatePageDataReq();
                    vo.setCurrent(1);
                    vo.setPageSize(10);
                    verifyCommitItemVO.setCount(this.pageToReceiveOrders(loginUser, vo).getTotalCount());
                }
            } else {
                verifyCommitItemVO.setCount(0L);
            }
            PurchaseOrderOperateTypeEnum typeEnum = PurchaseOrderOperateTypeEnum.getEnum(reportMap.get(code));
            verifyCommitItemVO.setLink(typeEnum.getLink());
            verifyCommitItemVO.setName(typeEnum.getName());
            resultVo.add(verifyCommitItemVO);
        }
        //待评价 --> 采购会员评价管理 --> 订单查询
        ReportItemResp itemVO = new ReportItemResp();
        if (!CollectionUtils.isEmpty(data)) {
            List<Long> orderIdList = data.stream().map(OrderDO::getId).collect(Collectors.toList());
            MemberOrderCommentReportReq vo = new MemberOrderCommentReportReq();
            vo.setType(2);
            vo.setLoginUser(loginUser);
            vo.setOrderListId(orderIdList);
            WrapperResp<Integer> countWrapper = memberOrderCommentFeign.findWaitCommentOrder(vo);
            itemVO.setCount(Long.valueOf(countWrapper.getData()));
        } else {
            itemVO.setCount(0L);
        }
        itemVO.setName(PurchaseOrderOperateTypeEnum.TO_BE_EVALUATE.getName());
        itemVO.setLink(PurchaseOrderOperateTypeEnum.TO_BE_EVALUATE.getLink());
        resultVo.add(itemVO);
        return resultVo;
    }

    @Override
    public OrderReportShopResp getShopOrder(UserLoginCacheDTO loginUser) {
        OrderReportShopResp orderReportShopResp = new OrderReportShopResp();
        //查询采购订单数据
        List<OrderDO> data = baseOrderService.findOrderByLoginUser(loginUser);
        //拼接map值
        Map<Integer, Integer> reportMap = new HashMap<>();
        //待提交审核订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_VALIFY_COMMIT.getCode());
        //待支付订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_PAY.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_PAY.getCode());
        //待确认收货订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_CONFIRM_RECEIVE_GOODS.getCode());
        //待归档订单
        reportMap.put(BuyerInnerStatusEnum.BUYER_TO_ARCHIVE.getCode(), PurchaseOrderOperateTypeEnum.TO_BE_ARCHIVE.getCode());
        for (Integer code : reportMap.keySet()) {
            //待提交审核订单
            List<OrderDO> valifyCommitOrderList = data.stream().filter(order -> Objects.equals(order.getBuyerInnerStatus(), code)).collect(Collectors.toList());
            if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_TO_SUBMIT_VALIDATE.getCode())) {
                List<Integer> orderTypes = Arrays.asList(OrderTypeEnum.MANUAL_MATERIAL_ORDER_PLACEMENT.getCode(), OrderTypeEnum.MATERIAL_SAMPLE_ORDER.getCode());
                valifyCommitOrderList = valifyCommitOrderList.stream().filter(order -> orderTypes.contains(order.getOrderType())).collect(Collectors.toList());

                orderReportShopResp.setPurchaseToBeVerify((long) valifyCommitOrderList.size());
            }
            //待支付订单
            if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_TO_PAY.getCode())) {
                OrderValidatePageDataReq vo = new OrderValidatePageDataReq();
                vo.setCurrent(1);
                vo.setPageSize(10);
                orderReportShopResp.setPurchaseToBePay(this.pageToPayOrders(loginUser, vo).getTotalCount());
            }
            //待确认收货订单
            if (Objects.equals(code, BuyerInnerStatusEnum.BUYER_TO_RECEIVE.getCode())) {
                OrderValidatePageDataReq vo = new OrderValidatePageDataReq();
                vo.setCurrent(1);
                vo.setPageSize(10);
                orderReportShopResp.setPurchaseToBeReceive(this.pageToReceiveOrders(loginUser, vo).getTotalCount());
            }
        }
        return orderReportShopResp;
    }

    private OrderTradeProcessBO findB2BTradeProcess(OrderProcessPayReq payVO, UserLoginCacheDTO loginUser) {
        //Step 2-1: 获取商品skuId获取商品数据
        List<Long> skuIds = payVO.getProducts().stream().map(ProductIdReq::getSkuId).collect(Collectors.toList());
        WrapperResp<List<CommoditySkuStockResp>> commodityByCommoditySkuIdList = commodityFeign.getCommodityByCommoditySkuIdList(skuIds);
        WrapperUtil.throwWhenFail(commodityByCommoditySkuIdList);

        if (CollectionUtils.isEmpty(commodityByCommoditySkuIdList.getData())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_SEPARATED_PRODUCT_DOES_NOT_EXIST);
        }

        List<CommoditySkuStockResp> commoditySkuStockRespList = commodityByCommoditySkuIdList.getData();
        Map<Long, CommoditySkuStockResp> commodityGoodsResponseMap = commoditySkuStockRespList.stream().collect(Collectors.toMap(CommoditySkuStockResp::getId, CommodityGoodsResponse -> CommodityGoodsResponse, (v1, v2) -> v2));
        Set<OrderProductDO> orderProducts = new HashSet<>();
        for (ProductIdReq product : payVO.getProducts()) {
            CommoditySkuStockResp commoditySkuStockResp = commodityGoodsResponseMap.get(product.getSkuId());
            if (Objects.isNull(commoditySkuStockResp)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SEPARATED_PRODUCT_DOES_NOT_EXIST);
            }
            OrderProductDO orderProductDO = new OrderProductDO();
            orderProductDO.setName(commoditySkuStockResp.getName());
            orderProductDO.setCategory(commoditySkuStockResp.getCustomerCategoryFullName());
            orderProducts.add(orderProductDO);
        }
        //组装查询交易流程订单主体
        OrderDO order = new OrderDO();
        order.setProducts(orderProducts);
        order.setBuyerMemberId(loginUser.getMemberId());
        order.setBuyerRoleId(loginUser.getMemberRoleId());
        order.setBuyerMemberName(loginUser.getMemberName());
        order.setShopId(payVO.getShopId());
        order.setVendorMemberId(payVO.getMemberId());
        order.setVendorRoleId(payVO.getRoleId());
        return baseOrderProcessService.findB2BOrderProcess(Stream.of(order).collect(Collectors.toList()), OrderTradeProcessTypeEnum.ORDER_TRADE);
    }
}
