package com.ssy.lingxi.order.serviceImpl.baitai;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.enums.order.SealTypeEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.select.DropdownItemResp;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.resp.MemberFeignMerchantResp;
import com.ssy.lingxi.order.entity.OrderSealCustomerDO;
import com.ssy.lingxi.order.entity.OrderSealDO;
import com.ssy.lingxi.order.entity.QOrderSealCustomerDO;
import com.ssy.lingxi.order.entity.QOrderSealDO;
import com.ssy.lingxi.order.enums.BuyerInnerStatusEnum;
import com.ssy.lingxi.order.enums.MemberTypeEnum;
import com.ssy.lingxi.order.enums.SealCreateUserEnum;
import com.ssy.lingxi.order.enums.SealStatusEnum;
import com.ssy.lingxi.order.model.req.baitai.*;
import com.ssy.lingxi.order.model.resp.baitai.SealCustomerPageResp;
import com.ssy.lingxi.order.model.resp.baitai.SealDetailResp;
import com.ssy.lingxi.order.model.resp.baitai.SealPageResp;
import com.ssy.lingxi.order.model.resp.baitai.SealStatusResp;
import com.ssy.lingxi.order.repository.OrderSealCustomerRepository;
import com.ssy.lingxi.order.repository.OrderSealRepository;
import com.ssy.lingxi.order.service.baitai.OrderSealService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/10
 */
@Service
public class OrderSealServiceImpl implements OrderSealService {

    @Resource
    private OrderSealRepository orderSealRepository;

    @Resource
    private OrderSealCustomerRepository orderSealCustomerRepository;

    @Resource
    private IMemberFeign memberFeign;

    @Resource
    private JPAQueryFactory jpaQueryFactory;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;

    private final QOrderSealDO qOrderSealDO = QOrderSealDO.orderSealDO;

    private final QOrderSealCustomerDO qOrderSealCustomerDO = QOrderSealCustomerDO.orderSealCustomerDO;

    /**
     * 分页查询字印
     *
     * @param sysUser 当前登录用户
     * @param request 分页查询字印请求参数
     * @return 字印分页查询结果
     */
    @Override
    public PageDataResp<SealPageResp> page(UserLoginCacheDTO sysUser, SealPageReq request) {
        BooleanBuilder builder = new BooleanBuilder();
        if (!request.getIsQueryCustomer()) {
            builder.and(qOrderSealDO.memberId.eq(sysUser.getMemberId()));
            builder.and(qOrderSealDO.memberRoleId.eq(sysUser.getMemberRoleId()));
        }
        if (ObjectUtil.isNotEmpty(request.getSealType())) {
            builder.and(qOrderSealDO.sealType.eq(request.getSealType()));
        }
        // 字印内容
        if (ObjectUtil.isNotEmpty(request.getSealContent())) {
            builder.and(qOrderSealDO.sealContent.like("%" + request.getSealContent() + "%"));
        }
        // 客户关联表，客户名称
        if (ObjectUtil.isNotEmpty(request.getName())) {
            builder.and(jpaQueryFactory.selectOne()
                    .from(qOrderSealCustomerDO)
                    .where(qOrderSealCustomerDO.orderSeal.id.eq(qOrderSealDO.id)
                            .and(qOrderSealCustomerDO.name.like("%" + request.getName() + "%")))
                    .exists());
        }
        // 字印状态
        if (ObjectUtil.isNotEmpty(request.getSealStatus())) {
            builder.and(qOrderSealDO.status.eq(request.getSealStatus()));
        }
        // 是否查询客户字印信息
        if (request.getIsQueryCustomer()) {
            builder.and(qOrderSealDO.createUserType.eq(SealCreateUserEnum.CUSTOMER.getCode()));
        }
        // 是否查询厂家字印信息
        if (request.getIsQueryFactory()) {
            builder.and(qOrderSealDO.createUserType.eq(SealCreateUserEnum.PLATFORM.getCode()));
        }
        // 是否全部客户适用
        if (ObjectUtil.isNotEmpty(request.getAllCustomer())) {
            builder.and(qOrderSealDO.allCustomer.eq(request.getAllCustomer()));
        }
        // 所属客户名称
        if (ObjectUtil.isNotEmpty(request.getMemberName())) {
            builder.and(qOrderSealDO.memberName.like("%" + request.getMemberName() + "%"));
        }
        JPAQuery<OrderSealDO> query = jpaQueryFactory.select(qOrderSealDO).from(qOrderSealDO).where(builder);

        // 分页查询
        if (request.getIsPage()) {
            query.offset(request.getCurrentOffset()).orderBy(qOrderSealDO.createTime.desc()).limit(request.getPageSize());
        }

        long totalCount = query.fetchCount();
        List<OrderSealDO> orderSealDOList = query.fetch();

        List<SealPageResp> sealPageRespList = orderSealDOList.stream().map(s -> {
            SealPageResp sealPageResp = BeanUtil.copyProperties(s, SealPageResp.class);
            sealPageResp.setSealTypeName(SealTypeEnum.getByCode(sealPageResp.getSealType()));
            sealPageResp.setStatusName(SealStatusEnum.getByCode(sealPageResp.getStatus()));
            return sealPageResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(totalCount, sealPageRespList);
    }

    /**
     * 分页 - 查询厂家字印列表
     * @param request 分页查询厂家字印请求参数
     * @return 厂家字印分页查询结果
     */
    @Override
    public PageDataResp<SealPageResp> factoryPage(UserLoginCacheDTO sysUser, SealPageReq request) {
        BooleanBuilder builder = new BooleanBuilder();
        if (!request.getIsQueryCustomer()) {
            builder.and(qOrderSealDO.memberId.eq(sysUser.getMemberId()));
            builder.and(qOrderSealDO.memberRoleId.eq(sysUser.getMemberRoleId()));
        }

        if (ObjectUtil.isNotEmpty(request.getSealType())) {
            builder.and(qOrderSealDO.sealType.eq(request.getSealType()));
        }
        // 字印内容
        if (ObjectUtil.isNotEmpty(request.getSealContent())) {
            builder.and(qOrderSealDO.sealContent.like("%" + request.getSealContent() + "%"));
        }
        // 客户关联表，客户名称
        if (ObjectUtil.isNotEmpty(request.getName())) {
            builder.and(jpaQueryFactory.selectOne()
                    .from(qOrderSealCustomerDO)
                    .where(qOrderSealCustomerDO.orderSeal.id.eq(qOrderSealDO.id)
                            .and(qOrderSealCustomerDO.name.like("%" + request.getName() + "%")))
                    .exists());
        }
        // 所属客户名称
        if (ObjectUtil.isNotEmpty(request.getMemberName())) {
            builder.and(qOrderSealDO.memberName.like("%" + request.getMemberName() + "%"));
        }
        // 字印状态
        if (ObjectUtil.isNotEmpty(request.getSealStatus())) {
            builder.and(qOrderSealDO.status.eq(request.getSealStatus()));
        }
        // 是否查询客户字印信息
        if (request.getIsQueryCustomer()) {
            builder.and(qOrderSealDO.createUserType.eq(SealCreateUserEnum.CUSTOMER.getCode()));
        }
        BooleanBuilder builderTwo = new BooleanBuilder();
        builderTwo.and(qOrderSealDO.createUserType.eq(SealCreateUserEnum.PLATFORM.getCode()));
        builderTwo.and(jpaQueryFactory.selectOne()
                .from(qOrderSealCustomerDO)
                .where(qOrderSealCustomerDO.orderSeal.id.eq(qOrderSealDO.id)
                        .and(qOrderSealCustomerDO.subMemberId.eq(sysUser.getMemberId()))
                        .and(qOrderSealCustomerDO.roleId.eq(sysUser.getMemberRoleId())))
                .exists());
        builder.or(builderTwo);

        JPAQuery<OrderSealDO> query = jpaQueryFactory.select(qOrderSealDO).from(qOrderSealDO).where(builder);

        // 分页查询
        if (request.getIsPage()) {
            query.offset(request.getCurrentOffset()).orderBy(qOrderSealDO.createTime.desc()).limit(request.getPageSize());
        }

        long totalCount = query.fetchCount();
        List<OrderSealDO> orderSealDOList = query.fetch();

        List<SealPageResp> sealPageRespList = orderSealDOList.stream().map(s -> {
            SealPageResp sealPageResp = BeanUtil.copyProperties(s, SealPageResp.class);
            sealPageResp.setSealTypeName(SealTypeEnum.getByCode(sealPageResp.getSealType()));
            sealPageResp.setStatusName(SealStatusEnum.getByCode(sealPageResp.getStatus()));
            return sealPageResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(totalCount, sealPageRespList);
    }

    /**
     * 新增或编辑字印
     *
     * @param request 新增字印请求参数
     * @return 新增字印结果
     */
    @Override
    @Transactional(rollbackOn = Exception.class)
    public WrapperResp<Void> create(UserLoginCacheDTO sysUser, SaveSealReq request) {
        if (ObjectUtil.isEmpty(request.getId())) {
            OrderSealDO orderSealDO = new OrderSealDO();
            BeanUtil.copyProperties(request, orderSealDO);
            SealStatusEnum sealStatusEnum=SealStatusEnum.VALID;
            if(request.getPlatformAdd()){
                //平台免审核
            }else if(SealTypeEnum.OTHER.getCode().equals(request.getSealType())){
                //其它字印，免审核
            }else{
                sealStatusEnum=SealStatusEnum.WAIT;
            }
            orderSealDO.setStatus(sealStatusEnum.getCode());
            orderSealDO.setMemberId(sysUser.getMemberId());
            orderSealDO.setMemberName(sysUser.getMemberName());
            orderSealDO.setMemberRoleId(sysUser.getMemberRoleId());
            orderSealDO.setCreateTime(LocalDateTime.now());
            orderSealDO.setCreateUserType(baiTaiMemberProperties.getSelfMemberId().equals(sysUser.getMemberId()) ? SealCreateUserEnum.PLATFORM.getCode() : SealCreateUserEnum.CUSTOMER.getCode());
            if (CollUtil.isNotEmpty(request.getCustomerIdList())) {
                List<OrderSealCustomerDO> orderSealCustomerDOS = BeanUtil.copyToList(request.getCustomerIdList(), OrderSealCustomerDO.class);
                for (OrderSealCustomerDO customerDO : orderSealCustomerDOS) {
                    customerDO.setOrderSeal(orderSealDO);
                }
                orderSealDO.setOrderSealCustomerDO(orderSealCustomerDOS);
                orderSealCustomerRepository.saveAll(orderSealCustomerDOS);
            }
            orderSealRepository.save(orderSealDO);
            return WrapperUtil.success();
        }
        OrderSealDO orderSealDO = orderSealRepository.findById(request.getId()).orElseThrow(() -> new RuntimeException("字印不存在"));
        // 只有会员和会员角色id相同的用户才能修改字印
        if (!orderSealDO.getMemberId().equals(sysUser.getMemberId()) || !orderSealDO.getMemberRoleId().equals(sysUser.getMemberRoleId())) {
            return WrapperUtil.fail("没有权限修改字印");
        }
        BeanUtil.copyProperties(request, orderSealDO);
        orderSealDO.setMemberId(sysUser.getMemberId());
        orderSealDO.setMemberRoleId(sysUser.getMemberRoleId());
        if (CollUtil.isNotEmpty(orderSealDO.getOrderSealCustomerDO())) {
            orderSealCustomerRepository.deleteAll(orderSealDO.getOrderSealCustomerDO());
        }
        if (CollUtil.isNotEmpty(request.getCustomerIdList())) {
            List<OrderSealCustomerDO> orderSealCustomerDOS = BeanUtil.copyToList(request.getCustomerIdList(), OrderSealCustomerDO.class);
            for (OrderSealCustomerDO customerDO : orderSealCustomerDOS) {
                customerDO.setOrderSeal(orderSealDO);
            }
            orderSealDO.setOrderSealCustomerDO(orderSealCustomerDOS);
            orderSealCustomerRepository.saveAll(orderSealCustomerDOS);
        }

        // 如果设置当前字印为默认字印，则需要将当前会员的其他字印设置为非默认
        if (CommonBooleanEnum.YES.getCode().equals(request.getDefaultSeal())) {
            jpaQueryFactory.update(qOrderSealDO)
                    .set(qOrderSealDO.defaultSeal, CommonBooleanEnum.NO.getCode())
                    .where(qOrderSealDO.memberId.eq(sysUser.getMemberId())
                            .and(qOrderSealDO.memberRoleId.eq(sysUser.getMemberRoleId()))
                            .and(qOrderSealDO.defaultSeal.eq(CommonBooleanEnum.YES.getCode()))
                            .and(qOrderSealDO.id.ne(request.getId())))
                    .execute();
        }
        orderSealRepository.save(orderSealDO);
        return WrapperUtil.success();
    }

    /**
     * 删除字印
     *
     * @param id 字印id
     * @return 删除结果
     */
    @Override
    public WrapperResp<Void> delete(UserLoginCacheDTO sysUser, Long id) {
        orderSealRepository.deleteById(id);
        return WrapperUtil.success();
    }

    /**
     * 查询字印详情
     *
     * @param id 字印id
     * @return 字印详情
     */
    @Override
    public WrapperResp<SealDetailResp> detail(UserLoginCacheDTO sysUser, Long id) {
        OrderSealDO orderSealDO = orderSealRepository.findById(id).orElseThrow(() -> new RuntimeException("字印不存在"));
        SealDetailResp sealDetailResp = BeanUtil.copyProperties(orderSealDO, SealDetailResp.class);
        sealDetailResp.setSealTypeName(SealTypeEnum.getByCode(sealDetailResp.getSealType()));
        sealDetailResp.setStatusName(SealStatusEnum.getByCode(sealDetailResp.getStatus()));
        // 获取适用客户id
        List<Long> customerIds = orderSealDO.getOrderSealCustomerDO().stream().map(OrderSealCustomerDO::getSubMemberId).collect(Collectors.toList());
        sealDetailResp.setCustomerIds(customerIds);
        List<SealCustomerPageResp> sealPageRespList = orderSealDO.getOrderSealCustomerDO().stream().map(s -> {
            SealCustomerPageResp sealCustomerPageResp = BeanUtil.copyProperties(s, SealCustomerPageResp.class);
            sealCustomerPageResp.setCustomerTypeName(MemberTypeEnum.getName(sealCustomerPageResp.getCustomerType()));
            return sealCustomerPageResp;
        }).collect(Collectors.toList());
        sealDetailResp.setCustomerList(sealPageRespList);
        return WrapperUtil.success(sealDetailResp);
    }

    /**
     * 审核字印
     * @param sealAuditReq 审核请求参数
     * @return 审核结果
     */
    @Override
    public WrapperResp<Void> audit(UserLoginCacheDTO sysUser, SealAuditReq sealAuditReq) {
        OrderSealDO orderSealDO = orderSealRepository.findById(sealAuditReq.getSealId()).orElseThrow(() -> new RuntimeException("字印不存在"));
        orderSealDO.setStatus(sealAuditReq.getAuditPass() ? SealStatusEnum.VALID.getCode() : SealStatusEnum.REJECT.getCode());
        orderSealDO.setRemark(sealAuditReq.getRemark());
        orderSealRepository.save(orderSealDO);
        return WrapperUtil.success();
    }

    /**
     * 启用或禁用字印
     * @param request 启用或禁用请求参数
     * @return 启用或禁用结果
     */
    @Override
    public WrapperResp<Void> enable(UserLoginCacheDTO sysUser, SealEnableReq request) {
        OrderSealDO orderSealDO = orderSealRepository.findById(request.getSealId()).orElseThrow(() -> new RuntimeException("字印不存在"));
        orderSealDO.setStatus(request.getEnable() ? SealStatusEnum.VALID.getCode() : SealStatusEnum.DISABLE.getCode());
        orderSealRepository.save(orderSealDO);
        return WrapperUtil.success();
    }

    /**
     * 分页查询适用客户列表
     *
     * @param request 分页查询适用客户列表请求参数
     * @return 适用客户列表分页查询结果
     */
    @Override
    public PageDataResp<SealCustomerPageResp> customerPage(UserLoginCacheDTO sysUser, SealCustomerReq request) {
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qOrderSealCustomerDO.orderSeal.id.eq(request.getSealId()));

        JPAQuery<OrderSealCustomerDO> query = jpaQueryFactory.select(qOrderSealCustomerDO).from(qOrderSealCustomerDO).where(builder);
        // 分页查询
        query.offset(request.getCurrentOffset()).limit(request.getPageSize());

        long totalCount = query.fetchCount();

        List<OrderSealCustomerDO> orderSealCustomerDOList = query.fetch();
        List<SealCustomerPageResp> sealPageRespList = orderSealCustomerDOList.stream().map(s -> {
            SealCustomerPageResp sealCustomerPageResp = BeanUtil.copyProperties(s, SealCustomerPageResp.class);
            sealCustomerPageResp.setCustomerTypeName(MemberTypeEnum.getName(sealCustomerPageResp.getCustomerType()));
            return sealCustomerPageResp;
        }).collect(Collectors.toList());
        return new PageDataResp<>(totalCount, sealPageRespList);
    }

    /**
     * 删除适用客户
     *
     * @param id 适用客户id
     * @return 删除结果
     */
    @Override
    public WrapperResp<Void> deleteCustomer(UserLoginCacheDTO sysUser, Long id) {
        orderSealCustomerRepository.deleteById(id);
        return WrapperUtil.success();
    }

    /**
     * 添加适用客户
     *
     * @param request 添加适用客户请求参数
     * @return 添加适用客户结果
     */
    @Override
    public WrapperResp<Void> customerCreate(UserLoginCacheDTO sysUser, SealCustomerSaveReq request) {
        OrderSealCustomerDO orderSealCustomerDO = BeanUtil.copyProperties(request, OrderSealCustomerDO.class);
        OrderSealDO orderSealDO = new OrderSealDO();
        orderSealDO.setId(request.getSealId());
        orderSealCustomerDO.setOrderSeal(orderSealDO);
        orderSealCustomerRepository.save(orderSealCustomerDO);
        return WrapperUtil.success();
    }

    @Override
    public WrapperResp<SealStatusResp> statusInfo() {
        SealStatusResp sealStatusResp = new SealStatusResp();

        List<SealTypeEnum> sealTypeEnumList = Stream.of(SealTypeEnum.OTHER, SealTypeEnum.BRAND).collect(Collectors.toList());
        sealStatusResp.setSealType(sealTypeEnumList.stream().map(e -> new DropdownItemResp(e.getCode(), SealTypeEnum.getByCode(e.getCode()))).collect(Collectors.toList())); ;
        sealStatusResp.setPlatformSealType(Arrays.stream(SealTypeEnum.values()).map(e -> new DropdownItemResp(e.getCode(), SealTypeEnum.getByCode(e.getCode()))).collect(Collectors.toList())); ;
        sealStatusResp.setStatus(Arrays.stream(SealStatusEnum.values()).map(e -> new DropdownItemResp(e.getCode(), SealStatusEnum.getByCode(e.getCode()))).collect(Collectors.toList())); ;
        return WrapperUtil.success(sealStatusResp);
    }

    /**
     * 设置字印为默认字印
     * @param sealIdReq 字印id
     * @return 设置结果
     */
    @Override
    @Transactional(rollbackOn = Exception.class)
    public WrapperResp<Void> setDefault(UserLoginCacheDTO sysUser, SealIdReq sealIdReq) {
        OrderSealDO orderSealDO = orderSealRepository.findById(sealIdReq.getSealId()).orElseThrow(() -> new RuntimeException("字印不存在"));

        // 只有会员和会员角色id相同的用户才能修改字印
        if (!orderSealDO.getMemberId().equals(sysUser.getMemberId()) || !orderSealDO.getMemberRoleId().equals(sysUser.getMemberRoleId())) {
            return WrapperUtil.fail("没有权限修改字印");
        }

        if (CommonBooleanEnum.YES.getCode().equals(sealIdReq.getDefaultSeal())) {
            // 查询当前会员的默认字印
            OrderSealDO defaultSeal = orderSealRepository.findFirstByMemberIdAndMemberRoleIdAndDefaultSeal(sysUser.getMemberId(), sysUser.getMemberRoleId(), CommonBooleanEnum.YES.getCode());
            if (ObjectUtil.isNotEmpty(defaultSeal)) {
                // 如果当前会员已经有默认字印，则将其设置为非默认
                defaultSeal.setDefaultSeal(CommonBooleanEnum.NO.getCode());
                orderSealRepository.save(defaultSeal);
            }
        }

        // 设置当前字印
        orderSealDO.setDefaultSeal(sealIdReq.getDefaultSeal());
        orderSealRepository.save(orderSealDO);
        return WrapperUtil.success();
    }
}
