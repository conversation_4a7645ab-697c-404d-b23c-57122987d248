package com.ssy.lingxi.order.serviceImpl.base;

import cn.hutool.core.util.StrUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.model.resp.AreaCodeNameResp;
import com.ssy.lingxi.component.base.util.AreaUtil;
import com.ssy.lingxi.order.entity.OrderConsigneeDO;
import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.entity.OrderDeliverDO;
import com.ssy.lingxi.order.enums.OrderProductDeliverTypeEnum;
import com.ssy.lingxi.order.model.req.basic.OrderDeliverReq;
import com.ssy.lingxi.order.repository.OrderDeliverRepository;
import com.ssy.lingxi.order.service.base.IBaseOrderDeliverService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/12
 */
@Service
public class IBaseOrderDeliverServiceImpl implements IBaseOrderDeliverService {

    @Resource
    private OrderDeliverRepository orderDeliverRepository;

    /**
     * 校验并生成订单发货人信息
     *
     * @param order       订单
     * @param consigneeVO 订单发货人接口参数
     * @param isCreate    是否新增，true-新增，false-修改
     */
    @Override
    public void checkOrderConsignee(OrderDO order, OrderDeliverReq consigneeVO, boolean isCreate) {
        if(!isCreate) {
            List<OrderDeliverDO> consigneeDOS = orderDeliverRepository.findByOrder(order);
            consigneeDOS.forEach(orderConsigneeDO -> orderConsigneeDO.setOrder(null));
            orderDeliverRepository.deleteAll(consigneeDOS);
        }

        if(Objects.isNull(consigneeVO)) {
            order.setConsignee(null);
            if(order.getProducts().stream().anyMatch(orderProductDO -> OrderProductDeliverTypeEnum.LOGISTICS.getCode().equals(orderProductDO.getDeliverType()) || OrderProductDeliverTypeEnum.LOGISTICS_PICK_UP.getCode().equals(orderProductDO.getDeliverType()))){
                throw new BusinessException(ResponseCodeEnum.PRODUCT_SAMPLE_DELIVERY_ADDRESS_NOT_NULL);
            }
            return;
        }

        String provinceName = "", cityName = "", districtName = "", streetName = "";
        if (StringUtils.hasLength(consigneeVO.getProvinceCode())) {
            List<String> areaCodeList = Stream.of(consigneeVO.getProvinceCode(), consigneeVO.getCityCode(), consigneeVO.getDistrictCode(), consigneeVO.getStreetCode()).filter(StringUtils::hasLength).collect(Collectors.toList());
            List<AreaCodeNameResp> areaList = AreaUtil.findByCodeIn(areaCodeList);
            provinceName = areaList.stream().filter(area -> area.getCode().equals(consigneeVO.getProvinceCode())).map(AreaCodeNameResp::getName).findFirst().orElse(null);
            if (!StringUtils.hasLength(provinceName)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PROVINCE_DOES_NOT_EXIST);
            }

            cityName = areaList.stream().filter(area -> area.getCode().equals(consigneeVO.getCityCode())).map(AreaCodeNameResp::getName).findFirst().orElse(null);
            if (!StringUtils.hasLength(cityName)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_CITY_DOES_NOT_EXIST);
            }

            districtName = areaList.stream().filter(area -> area.getCode().equals(consigneeVO.getDistrictCode())).map(AreaCodeNameResp::getName).findFirst().orElse(null);
            if (!StringUtils.hasLength(districtName)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_DISTRICT_DOES_NOT_EXIST);
            }

            if (StringUtils.hasLength(consigneeVO.getStreetCode())) {
                streetName = areaList.stream().filter(area -> area.getCode().equals(consigneeVO.getStreetCode())).map(AreaCodeNameResp::getName).findFirst().orElse(null);
            }
        }

        OrderDeliverDO consignee = Objects.isNull(order.getDeliver()) ? new OrderDeliverDO() : order.getDeliver();
        consignee.setOrder(order);
        consignee.setDeliverId(consigneeVO.getConsigneeId());
        consignee.setDeliver(consigneeVO.getConsignee());
        consignee.setProvinceCode(consigneeVO.getProvinceCode());
        consignee.setProvinceName(provinceName);
        consignee.setCityCode(consigneeVO.getCityCode());
        consignee.setCityName(cityName);
        consignee.setDistrictCode(consigneeVO.getDistrictCode());
        consignee.setDistrictName(districtName);
        consignee.setStreetCode(StrUtil.isEmpty(consigneeVO.getStreetCode()) ? "" : consigneeVO.getStreetCode());
        consignee.setStreetName(streetName);
        consignee.setAddress(consigneeVO.getAddress());
        consignee.setPostalCode(StringUtils.hasLength(consigneeVO.getPostalCode()) ? consigneeVO.getPostalCode().trim() : "");
        consignee.setCountryCode(consigneeVO.getCountryCode());
        consignee.setPhone(consigneeVO.getPhone());
        consignee.setTelephone(StringUtils.hasLength(consigneeVO.getTelephone()) ? consigneeVO.getTelephone().trim() : consigneeVO.getTelephone());
        consignee.setDefaultConsignee(consigneeVO.getDefaultConsignee());
        order.setDeliver(consignee);
    }
}
