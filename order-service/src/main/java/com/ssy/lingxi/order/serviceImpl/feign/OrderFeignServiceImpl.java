package com.ssy.lingxi.order.serviceImpl.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.group.GroupBy;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.FundModeEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.enums.order.*;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.contract.api.model.resp.OrderContractResp;
import com.ssy.lingxi.order.api.model.req.*;
import com.ssy.lingxi.order.api.model.resp.*;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.BuyerInnerStatusEnum;
import com.ssy.lingxi.order.enums.OrderOuterStatusEnum;
import com.ssy.lingxi.order.model.bo.*;
import com.ssy.lingxi.order.model.dto.OrderReturnAmountDTO;
import com.ssy.lingxi.order.repository.OrderProductRepository;
import com.ssy.lingxi.order.repository.OrderRepository;
import com.ssy.lingxi.order.repository.OrderRequisitionProductRepository;
import com.ssy.lingxi.order.service.base.IBaseOrderPaymentService;
import com.ssy.lingxi.order.service.base.IBaseOrderProcessService;
import com.ssy.lingxi.order.service.base.IBaseOrderService;
import com.ssy.lingxi.order.service.feign.IContractFeignService;
import com.ssy.lingxi.order.service.feign.IOrderFeignService;
import com.ssy.lingxi.order.service.platform.IPlatformSettlementTypeService;
import com.ssy.lingxi.order.service.web.IBuyerOrderService;
import com.ssy.lingxi.order.service.web.IOrderParamConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单服务对外OpenFeign接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-31
 */
@Slf4j
@Service
public class OrderFeignServiceImpl implements IOrderFeignService {

    @Resource
    private IBaseOrderProcessService baseOrderProcessService;

    @Resource
    private IContractFeignService contractFeignService;

    @Resource
    private OrderProductRepository orderProductRepository;

    @Resource
    private IBaseOrderPaymentService baseOrderPaymentService;

    @Resource
    private IBuyerOrderService buyerOrderService;

    @Resource
    private IBaseOrderService baseOrderService;

    @Resource
    private IPlatformSettlementTypeService platformSettlementTypeService;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private JPAQueryFactory jpaQueryFactory;
    @Resource
    private IOrderParamConfigService orderParamConfigService;

    @Resource
    private OrderRequisitionProductRepository orderRequisitionProductRepository;

    /**
     * 查询（供应）会员交易流程规则配置
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public OrderTradeProcessFeignDetailResp findVendorTradeProcess(OrderTradeProcessFeignReq feignVO) {
        VendorProductBO vendorProduct = new VendorProductBO();
        vendorProduct.setVendorMemberId(feignVO.getMemberId());
        vendorProduct.setVendorRoleId(feignVO.getRoleId());
        vendorProduct.setProducts(CollectionUtils.isEmpty(feignVO.getProducts()) ? new ArrayList<>() : feignVO.getProducts().stream().map(p -> new OrderProductBO(p.getProductId(), p.getSkuId())).collect(Collectors.toList()));

        // Wrapper<OrderTradeProcessBO> result = baseOrderProcessService.findVendorTradeProcess(feignVO.getShopId(), feignVO.getProcessTypeEnum(), Collections.singletonList(vendorProduct));
        // 0618修改，通过流程引擎搜索交易规则
        OrderTradeProcessBO result = baseOrderProcessService.findB2BOrderProcess(feignVO.getShopId(), feignVO.getProcessTypeEnum(), Collections.singletonList(vendorProduct));

        OrderTradeProcessFeignDetailResp resp = new OrderTradeProcessFeignDetailResp();
        resp.setProcessKey(result.getProcessKey());
        resp.setSkipFirstStep(result.getSkipFirstStep());
        resp.setProcessKind(result.getProcessKind());
        resp.setExpireHours(result.getExpireHours());
        resp.setPayTimes(result.getPayTimes());
        resp.setPayments(result.getPayNodes().stream().map(processPayment -> new OrderTradeProcessPaymentFeignDetailResp(processPayment.getBatchNo(), OrderPayNodeEnum.getNameByCode(processPayment.getPayNode()), processPayment.getPayRate())).collect(Collectors.toList()));
        return resp;
    }

    /**
     * 查询（采购）会员采购流程规则配置
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public OrderPurchaseProcessFeignDetailResp findBuyerPurchaseProcess(OrderPurchaseProcessFeignReq feignVO) {
        List<Long> contractIds = CollectionUtils.isEmpty(feignVO.getContractIds()) ? new ArrayList<>() : feignVO.getContractIds().stream().distinct().collect(Collectors.toList());

        OrderPurchaseProcessBO result = baseOrderProcessService.findSRMOrderProcess(feignVO.getMemberId(), feignVO.getRoleId(), feignVO.getProcessTypeEnum(), contractIds);

        OrderPurchaseProcessFeignDetailResp resp = new OrderPurchaseProcessFeignDetailResp();
        resp.setProcessKey(result.getProcessKey());
        resp.setProcessKind(result.getProcessKind());
        resp.setSkipFirstStep(result.getSkipFirstStep());
        return resp;
    }

    /**
     * 查询平台后台 - 会员支付策略设置
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<PlatformPayTypeFeignDetailResp> findPlatformPayments(OrderMemberFeignReq feignVO) {
        List<PlatformPayTypeBO> platformPayTypes = baseOrderProcessService.findPlatformMemberPayment(feignVO.getMemberId(), feignVO.getRoleId());
        if(feignVO.getPayType() != null) {
            platformPayTypes = platformPayTypes.stream().filter(platformPayType -> platformPayType.getPayType().equals(feignVO.getPayType().getCode())).collect(Collectors.toList());
        }

        if(feignVO.getPayChannel() != null) {
            platformPayTypes = platformPayTypes.stream().filter(platformPayType -> platformPayType.getChannels().stream().anyMatch(payChannel -> payChannel.getPayChannel().equals(feignVO.getPayChannel().getCode()))).collect(Collectors.toList());
        }

        if(CollectionUtils.isEmpty(platformPayTypes)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PAYMENT_SETTING_NOT_SET_YET);
        }

        return platformPayTypes.stream().map(platformPayType -> {
            PlatformPayTypeFeignDetailResp detailVO = new PlatformPayTypeFeignDetailResp();
            detailVO.setFundMode(FundModeEnum.parse(platformPayType.getFundMode()));
            detailVO.setPayType(OrderPayTypeEnum.parse(platformPayType.getPayType()));
            detailVO.setPayChannels(platformPayType.getChannels().stream().map(channel -> OrderPayChannelEnum.parse(channel.getPayChannel())).collect(Collectors.toList()));
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * 查询支付参数
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public PaymentParameterFeignDetailResp findPaymentParameters(OrderPayParameterFeignReq feignVO) {
        OrderPaymentParameterDetailBO parameterResult = baseOrderProcessService.findOrderPaymentParameters(feignVO.getPayChannel().getCode(), feignVO.getMemberId(), feignVO.getRoleId());

        return new PaymentParameterFeignDetailResp(parameterResult.getMemberId(), parameterResult.getRoleId(), FundModeEnum.parse(parameterResult.getFundMode()), OrderPayChannelEnum.parse(parameterResult.getPayChannel()), parameterResult.getParameters().stream().map(p -> new PayChannelParameterFeignDetailResp(OrderPaymentParameterEnum.parse(p.getCode()), p.getValue())).collect(Collectors.toList()));
    }

    /**
     * 查询支付参数（直接查询平台支付参数配置）
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public PaymentParameterFeignDetailResp findPlatformPaymentParameters(OrderPayChannelFeignReq feignVO) {
        OrderPaymentParameterDetailBO parameterResult = baseOrderProcessService.findPlatformPaymentParameters(feignVO.getPayChannel().getCode());

        return new PaymentParameterFeignDetailResp(parameterResult.getMemberId(), parameterResult.getRoleId(), FundModeEnum.parse(parameterResult.getFundMode()), OrderPayChannelEnum.parse(parameterResult.getPayChannel()), parameterResult.getParameters().stream().map(p -> new PayChannelParameterFeignDetailResp(OrderPaymentParameterEnum.parse(p.getCode()), p.getValue())).collect(Collectors.toList()));
    }

    /**
     * 查询支付参数（直接查询会员支付参数配置）
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public PaymentParameterFeignDetailResp findMemberPaymentParameters(OrderPayParameterFeignReq feignVO) {
        OrderPaymentParameterDetailBO parameterResult = baseOrderProcessService.findMemberPaymentParameters(feignVO.getPayChannel().getCode(), feignVO.getMemberId(), feignVO.getRoleId());

        return new PaymentParameterFeignDetailResp(parameterResult.getMemberId(), parameterResult.getRoleId(), FundModeEnum.parse(parameterResult.getFundMode()), OrderPayChannelEnum.parse(parameterResult.getPayChannel()), parameterResult.getParameters().stream().map(p -> new PayChannelParameterFeignDetailResp(OrderPaymentParameterEnum.parse(p.getCode()), p.getValue())).collect(Collectors.toList()));
    }

    /**
     * 售后服务 - 更新订单商品的换货、退货、维修数量
     *
     * @param afterSales 接口参数
     * @return 更新结果
     */
    @Transactional
    @Override
    public Void updateAfterSaleOrderProduct(List<OrderAfterSaleReq> afterSales) {
        if(CollectionUtils.isEmpty(afterSales)) {
            return null;
        }

        List<OrderDO> orders = orderRepository.findAllById(afterSales.stream().map(OrderAfterSaleReq::getOrderId).collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(orders) || orders.size() != afterSales.size()) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }

        List<OrderProductDO> orderProducts = orderProductRepository.findByOrderIn(orders);
        if(CollectionUtils.isEmpty(orderProducts)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }

        List<OrderProductAfterSaleReq> afterSaleProducts = afterSales.stream().flatMap(a -> a.getProducts().stream()).collect(Collectors.toList());
        orderProducts.forEach(orderProduct -> afterSaleProducts.stream().filter(afterSaleProduct -> afterSaleProduct.getProductId().equals(orderProduct.getId())).findFirst().ifPresent(afterSaleProduct -> {
            orderProduct.setReturnCount(orderProduct.getReturnCount().add(afterSaleProduct.getReturnCount()));
            orderProduct.setExchangeCount(orderProduct.getExchangeCount().add(afterSaleProduct.getExchangeCount()));
            orderProduct.setMaintainCount(orderProduct.getMaintainCount().add(afterSaleProduct.getMaintainCount()));
            orderProduct.setReturnAmount(orderProduct.getReturnAmount().add(afterSaleProduct.getReturnAmount()));
        }));

        orderProductRepository.saveAll(orderProducts);

        baseOrderPaymentService.batchUpdateOrderPayments(orders, orderProducts.stream().collect(Collectors.groupingBy(p -> p.getOrder().getId())).entrySet().stream().map(entry ->
                new OrderReturnAmountDTO(entry.getKey(), entry.getValue().stream().map(p -> p.getQuantity().subtract(p.getReturnCount()).multiply(p.getRefPrice())).reduce(BigDecimal.ZERO, BigDecimal::add))
        ).collect(Collectors.toList()));

        return null;
    }

    /**
     * 售后服务 - 更新订单商品的实际退款金额
     *
     * @param orderReturnAmountUpdateReq 退款金额
     * @return 无返回值
     */
    @Override
    public Void updateAfterSaleOrderProductAmount(OrderReturnAmountUpdateReq orderReturnAmountUpdateReq) {
        if(ObjectUtils.isEmpty(orderReturnAmountUpdateReq)) {
            return null;
        }
        OrderDO order = orderRepository.findById(orderReturnAmountUpdateReq.getOrderId()).orElse(null);
        if(ObjectUtils.isEmpty(order)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        Set<OrderProductDO> products = order.getProducts();
        if(CollectionUtils.isEmpty(products)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }
        OrderProductDO orderProductDO = products.stream().filter(p -> p.getId().equals(orderReturnAmountUpdateReq.getProductId()) && p.getSkuId().equals(orderReturnAmountUpdateReq.getSkuId())).distinct().findFirst().orElse(null);
       if(!ObjectUtils.isEmpty(orderProductDO)){
           if(ObjectUtils.isEmpty(orderProductDO.getRefReturnAmount())){
               orderProductDO.setRefReturnAmount(new BigDecimal(0).add(orderReturnAmountUpdateReq.getRefundAmount()));
           }else {
               orderProductDO.setRefReturnAmount(orderReturnAmountUpdateReq.getRefundAmount().add(orderProductDO.getRefReturnAmount()));
           }

       }
        assert orderProductDO != null;
        orderProductRepository.saveAndFlush(orderProductDO);

        return null ;
    }

    /**
     * 售后服务 - 查询订单商品的换货、退货、维修数量、退款金额
     *
     * @param orderProductIds 订单商品Id列表
     * @return 查询结果
     */
    @Override
    public List<OrderAfterSaleProductFeignDetailResp> findAfterSaleOrderProducts(List<OrderProductIdFeignReq> orderProductIds) {
        if(CollectionUtils.isEmpty(orderProductIds)) {
            return new ArrayList<>();
        }

        List<Long> productIds = orderProductIds.stream().map(OrderProductIdFeignReq::getOrderProductId).collect(Collectors.toList());
        List<OrderProductDO> orderProducts = orderProductRepository.findAllById(productIds);
        if(CollectionUtils.isEmpty(orderProducts)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }

        return orderProducts.stream().map(orderProduct -> {
            OrderAfterSaleProductFeignDetailResp detailVO = new OrderAfterSaleProductFeignDetailResp();
            detailVO.setOrderProductId(orderProduct.getId());
            detailVO.setQuantity(orderProduct.getQuantity());
            detailVO.setRefPrice(orderProduct.getRefPrice());
            detailVO.setPaidAmount(orderProduct.getPaidAmount());
            detailVO.setExchangeCount(orderProduct.getExchangeCount());
            detailVO.setMaintainCount(orderProduct.getMaintainCount());
            detailVO.setReturnCount(orderProduct.getReturnCount());
            detailVO.setReturnAmount(orderProduct.getReturnAmount());
            return detailVO;
        }).collect(Collectors.toList());
    }

    /**
     * 售后服务 - 批量查询订单外部状态
     *
     * @param feignVO 订单Id列表
     * @return 查询结果
     */
    @Override
    public List<OrderAfterSaleStatusResp> findAfterSaleOrderStatuses(OrderIdsFeignReq feignVO) {
        List<OrderDO> orders = CollectionUtils.isEmpty(feignVO.getOrderIds()) ? orderRepository.findByOrderNoIn(feignVO.getOrderNos()) : orderRepository.findAllById(feignVO.getOrderIds());
        return orders.stream().map(order -> {
            OrderAfterSaleStatusResp statusVO = new OrderAfterSaleStatusResp();
            statusVO.setOrderId(order.getId());
            statusVO.setOrderNo(order.getOrderNo());
            statusVO.setOuterStatus(order.getOuterStatus());
            statusVO.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(order.getOuterStatus()));
            return statusVO;
        }).collect(Collectors.toList());
    }

    /**
     * 售后服务 - 根据合同Id查询订单列表
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderAfterSaleOrderContractFeignResp> findAfterSaleOrderContracts(OrderContractFeignReq feignVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;
//        QOrderContractDO qOrderContract = QOrderContractDO.orderContractDO;

        JPAQuery<?> query = jpaQueryFactory.from(qOrder)
//                .leftJoin(qOrder).on(qOrderContract.order.id.eq(qOrder.id))
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .where(qOrder.id.eq(feignVO.getOrderId()));

        if(StringUtils.isNotBlank(feignVO.getOrderNo())) {
            query.where(qOrder.orderNo.like("%" + feignVO.getOrderNo().trim() + "%"));
        }

        if(StringUtils.isNotBlank(feignVO.getDigest())) {
            query.where(qOrder.digest.like("%" + feignVO.getDigest().trim() + "%"));
        }

        if(NumberUtil.notNullAndPositive(feignVO.getStartTime())) {
            query.where(qOrder.createTime.after(DateTimeUtil.convertToLocalDateTime(feignVO.getStartTime())));
        }

        if(NumberUtil.notNullAndPositive(feignVO.getEndTime())) {
            query.where(qOrder.createTime.before(DateTimeUtil.convertToLocalDateTime(feignVO.getEndTime())));
        }

        List<OrderAfterSaleOrderContractFeignResp> result = query.transform(GroupBy.groupBy(qOrderProduct.tax, qOrderProduct.taxRate).as(GroupBy.list(Projections.constructor(OrderAfterSaleOrderContractFeignResp.class,
                qOrder.id,
                qOrder.orderNo,
                qOrder.digest,
                qOrder.orderType,
                qOrder.createTime,
                qOrderProduct.refPrice,
                qOrderProduct.quantity,
                qOrderProduct.tax,
                qOrderProduct.taxRate,
                qOrder.outerStatus
        )))).values().stream().flatMap(Collection::stream).collect(Collectors.toList());

        result.forEach(r -> r.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(r.getOuterStatus())));

        return result;
    }

    /**
     * 售后能力 - 查询已经确认支付结果的支付记录列表
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderAfterSalePaymentFeignDetailResp> findAfterSaleOrderPayments(OrderIdsFeignReq feignVO) {
        QOrderPaymentDO qOrderPayment = QOrderPaymentDO.orderPaymentDO;
        QOrderDO qOrder = QOrderDO.orderDO;

        JPAQuery<OrderAfterSalePaymentFeignDetailResp> query = jpaQueryFactory.select(Projections.constructor(OrderAfterSalePaymentFeignDetailResp.class, qOrderPayment.order.id, qOrderPayment.order.orderNo, qOrderPayment.id, qOrderPayment.payTime, qOrderPayment.batchNo, qOrderPayment.payNode, qOrderPayment.payRate, qOrderPayment.payAmount, qOrderPayment.fundMode, qOrderPayment.payType, qOrderPayment.payChannel, qOrderPayment.tradeNo, qOrderPayment.settlementStatus, qOrder.outerStatus.eq(OrderOuterStatusEnum.TO_CONFIRM_PAYMENT.getCode()), qOrderPayment.outerStatus.eq(OrderOuterStatusEnum.ACCOMPLISHED.getCode())))
                .from(qOrderPayment)
                .leftJoin(qOrder).on(qOrderPayment.order.id.eq(qOrder.id))
                .where(qOrderPayment.payAmount.gt(BigDecimal.ZERO));

        if(!CollectionUtils.isEmpty(feignVO.getOrderIds())) {
            query.where(qOrderPayment.order.id.in(feignVO.getOrderIds()));
        } else {
            query.where(qOrderPayment.order.orderNo.in(feignVO.getOrderNos()));
        }

        return query.fetch();
    }

    /**
     * 物流能力 - 确认物流单后，更新订单的物流单号
     *
     * @param feignVO 接口参数
     * @return 更新结果
     */
    @Transactional
    @Override
    public Void updateOrderLogisticsNo(OrderUpdateLogisticsNoFeignReq feignVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        jpaQueryFactory.update(qOrder).where(qOrder.id.eq(feignVO.getOrderId())).set(qOrder.logisticsNo, feignVO.getLogisticsNo()).execute();
        return null;
    }

    /**
     * 加工能力 - 更新订单商品加工数量
     *
     * @param feigns 接口参数
     * @return 更新结果
     */
    @Override
    public Void updateOrderProductEnhanceCount(List<OrderUpdateEnhanceFeignReq> feigns) {
        if(CollectionUtils.isEmpty(feigns)) {
            return null;
        }

        List<OrderProductDO> orderProducts = orderProductRepository.findAllById(feigns.stream().map(OrderUpdateEnhanceFeignReq::getOrderProductId).collect(Collectors.toList()));
        orderProducts.forEach(orderProduct -> feigns.stream().filter(feignProduct -> feignProduct.getOrderProductId().equals(orderProduct.getId())).findFirst().ifPresent(feignProduct -> orderProduct.setEnhanceCount(orderProduct.getEnhanceCount().add(feignProduct.getEnhanceCount()))));
        orderProductRepository.saveAll(orderProducts);
        return null;
    }

    /**
     * 结算服务 - 查询订单发票信息
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public OrderSettleAccountInvoiceFeignDetailResp findSettleAccountInvoiceDetail(OrderNoFeignReq feignVO) {
        QOrderInvoiceDO qOrderInvoice = QOrderInvoiceDO.orderInvoiceDO;
        QOrderDO qOrder = QOrderDO.orderDO;
        return jpaQueryFactory.select(Projections.constructor(OrderSettleAccountInvoiceFeignDetailResp.class, qOrderInvoice.id, qOrder.id, qOrder.orderNo, qOrderInvoice.invoiceId, qOrderInvoice.invoiceKind, qOrderInvoice.invoiceType, qOrderInvoice.title, qOrderInvoice.taxNo, qOrderInvoice.bank, qOrderInvoice.account, qOrderInvoice.address, qOrderInvoice.phone, qOrderInvoice.defaultInvoice))
                .from(qOrderInvoice)
                .leftJoin(qOrder).on(qOrderInvoice.order.id.eq(qOrder.id))
                .where(qOrder.orderNo.eq(feignVO.getOrderNo())).fetchFirst();
    }

    /**
     * 会员服务 - 订单评论完成后，更改订单评论状态
     *
     * @param feignVO 接口参数
     * @return 修改结果
     */
    @Transactional
    @Override
    public Void updateOrderComment(OrderCommentFeignReq feignVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        if(Objects.nonNull(feignVO.getBuyerCommentComplete()) && feignVO.getBuyerCommentComplete()) {
            jpaQueryFactory.update(qOrder).where(qOrder.id.eq(feignVO.getOrderId())).set(qOrder.buyerCommented, true).execute();
        }

        if(Objects.nonNull(feignVO.getVendorCommentComplete()) && feignVO.getVendorCommentComplete()) {
            jpaQueryFactory.update(qOrder).where(qOrder.id.eq(feignVO.getOrderId())).set(qOrder.vendorCommented, true).execute();
        }

        return null;
    }

    /**
     * 支付服务 - 订单服务回调
     *
     * @param callbackFeignVO 接口参数
     * @return 操作结果
     */
    @Override
    public Void orderPayCallback(OrderPayCallbackFeignReq callbackFeignVO) {
        log.info("feign接收到回调：" + SerializeUtil.serialize(callbackFeignVO));
        buyerOrderService.orderPayCallback(callbackFeignVO);
        return null;
    }

    /**
     * 售后服务 - 订单会员信息校验
     * @param checkVO 请求参数
     * @return 操作结果
     */
    @Override
    public Boolean checkAfterSaleOrderMember(CheckOrderMemberAfterSaleReq checkVO) {
        if (CollUtil.isEmpty(checkVO.getOrderIds())) {
            return Boolean.FALSE;
        }
        int count = orderRepository.countByIdInAndBuyerMemberIdAndBuyerRoleIdAndVendorMemberIdAndVendorRoleId(checkVO.getOrderIds(), checkVO.getBuyerMemberId(), checkVO.getBuyerRoleId(), checkVO.getVendorMemberId(), checkVO.getVendorRoleId());
        if (count == checkVO.getOrderIds().size()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public List<PlatformSettlementTypeResp> findSettlementTypeByStatusEqualEnable() {
        return platformSettlementTypeService.findSettlementTypeByEnable();
    }

    @Override
    public List<MemberSettleChannelResp> findSettlementChannel(MemberSettleChannelQueryReq queryVO) {
        QMemberPaymentParameterDO qPlatformPaymentParameterDO = QMemberPaymentParameterDO.memberPaymentParameterDO;
        return jpaQueryFactory.select(Projections.bean(MemberSettleChannelResp.class,
                        qPlatformPaymentParameterDO.payChannel,
                        qPlatformPaymentParameterDO.payChannelName))
                .from(qPlatformPaymentParameterDO)
                .where(qPlatformPaymentParameterDO.payType.eq(queryVO.getPayType()),
                        qPlatformPaymentParameterDO.memberId.eq(queryVO.getMemberId()),
                        qPlatformPaymentParameterDO.roleId.eq(queryVO.getRoleId())).fetch();
    }

    /**
     * 查询平台规则配置
     *
     * @param feignVO 查询条件
     * @return 查询结果
     */
    @Override
    public List<OrderRuleDetailResp> findOrderRules(OrderRuleDetailFeignReq feignVO) {
        QBaseOrderRuleDO qBaseOrderRule = QBaseOrderRuleDO.baseOrderRuleDO;
        JPAQuery<OrderRuleDetailResp> query = jpaQueryFactory.select(Projections.constructor(OrderRuleDetailResp.class, qBaseOrderRule.ruleType, qBaseOrderRule.ruleName, qBaseOrderRule.methodCode, qBaseOrderRule.methodName, qBaseOrderRule.status))
                .from(qBaseOrderRule);

        if(NumberUtil.notNullOrZero(feignVO.getRuleType())) {
            query.where(qBaseOrderRule.ruleType.eq(feignVO.getRuleType()));
        }

        if(NumberUtil.notNullOrZero(feignVO.getMethodCode())) {
            query.where(qBaseOrderRule.methodCode.eq(feignVO.getMethodCode()));
        }

        if(NumberUtil.notNullOrZero(feignVO.getStatus())) {
            query.where(qBaseOrderRule.status.eq(feignVO.getStatus()));
        }

        return query.fetch();
    }

    /**
     * 营销服务 - 查询订单商品信息
     *
     * @param feignVO 查询条件
     * @return 查询结果
     */
    @Override
    public List<OrderMarketingDetailResp> findOrderMarketingProducts(OrderMarketingFeignReq feignVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;

        List<OrderMarketingDetailResp> result = jpaQueryFactory.select(Projections.constructor(OrderMarketingDetailResp.class, qOrder.id, qOrderProduct.skuId, qOrder.outerStatus, qOrderProduct.tax, qOrderProduct.taxRate, qOrderProduct.price, qOrderProduct.refPrice, qOrderProduct.quantity))
                .from(qOrder).leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .where(qOrder.id.in(feignVO.getOrderIds()).and(qOrderProduct.skuId.in(feignVO.getSkuIds())))
                .fetch();

        result.forEach(r -> r.setOuterStatusName(OrderOuterStatusEnum.getNameByCode(r.getOuterStatus())));

        return result;
    }

    /**
     * 会员服务 - 查询订单供应会员会员Logo、或店铺Logo、或自营商城Logo
     * @param feignVO 接口参数
     * @return 修改结果
     */
    @Override
    public List<OrderVendorLogoFeignResp> findOrderVendorLogo(OrderFeignIdsReq feignVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        return jpaQueryFactory.select(Projections.constructor(OrderVendorLogoFeignResp.class, qOrder.id, qOrder.vendorLogo)).from(qOrder).where(qOrder.id.in(feignVO.getOrderIds())).fetch();
    }

    /**
     * 结算能力 - 新增请款单-查询请款类型和付款方过滤采购订单数据
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderBuyerSettleQueryResp> pageSettleBuyerOrders(OrderBuyerSettlePageDataReq pageVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;

        JPAQuery<OrderBuyerQueryResp> query = jpaQueryFactory.from(qOrder)
                .select(Projections.constructor(OrderBuyerQueryResp.class, qOrder.id, qOrder.orderNo, qOrder.digest, qOrder.createTime, qOrder.buyerInnerStatus, qOrder.productAmount, qOrderProduct.tax, qOrderProduct.taxRate, qOrderProduct.amount.sum()))
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .where(qOrder.buyerMemberId.eq(pageVO.getBuyerMemberId()).and(qOrder.buyerRoleId.eq(pageVO.getBuyerRoleId())).and(qOrder.orderType.eq(pageVO.getOrderType())).and(qOrder.buyerInnerStatus.ne(BuyerInnerStatusEnum.CANCELLED.getCode())))
                .groupBy(qOrder.id, qOrderProduct.tax, qOrderProduct.taxRate);

        //订单号
        if (StringUtils.isNotEmpty(pageVO.getOrderNo())) {
            query.where(qOrder.orderNo.like("%" + pageVO.getOrderNo().trim() + "%"));
        }

        //订单摘要
        if (StringUtils.isNotEmpty(pageVO.getDigest())) {
            query.where(qOrder.digest.like("%" + pageVO.getDigest().trim() + "%"));
        }

        //订单起始时间
        if (StringUtils.isNotEmpty(pageVO.getStartDate())) {
            query.where(qOrder.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }

        //订单起始时间
        if (StringUtils.isNotEmpty(pageVO.getEndDate())) {
            query.where(qOrder.createTime.before(LocalDateTime.parse(pageVO.getEndDate().concat(" 23:59:59"), OrderConstant.DEFAULT_TIME_FORMATTER)));
        }
        long totalCount = query.fetch().size();
        query.orderBy(qOrder.id.desc());
        query.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());
        List<OrderBuyerQueryResp> orderBuyerQueryRespList = query.fetch();
        if(CollectionUtils.isEmpty(orderBuyerQueryRespList)){
            return new PageDataResp<>();
        }

        List<OrderBuyerSettleQueryResp> orderBuyerSettleQueryRespList = orderBuyerQueryRespList.stream().map(orderBuyerQueryResp -> {
            OrderBuyerSettleQueryResp orderBuyerSettleQueryResp = BeanUtil.copyProperties(orderBuyerQueryResp, OrderBuyerSettleQueryResp.class);
            orderBuyerSettleQueryResp.setStatusName(BuyerInnerStatusEnum.getNameByCode(orderBuyerQueryResp.getStatus()));
            OrderContractResp orderContractResp = contractFeignService.getOrderContractByOrderId(new CommonIdReq(orderBuyerQueryResp.getOrderId()));
            orderBuyerSettleQueryResp.setContractId(Objects.nonNull(orderContractResp) ? orderContractResp.getContractId() : null);
            return orderBuyerSettleQueryResp;
        }).collect(Collectors.toList());

        return new PageDataResp<>(totalCount, orderBuyerSettleQueryRespList);
    }

    /**
     * 结算服务 - 付款完成后修改订单收发货记录状态为已结算
     * @param feignVO 接口参数
     */
    @Async
    @Override
    @Transactional
    public void updateOrderDeliveries(OrderUpdateDeliveriesFeignReq feignVO) {
        QOrderDeliveryDO qOrderDelivery = QOrderDeliveryDO.orderDeliveryDO;

        jpaQueryFactory.update(qOrderDelivery).where(qOrderDelivery.order.id.eq(feignVO.getOrderId()).and(qOrderDelivery.batchNo.eq(feignVO.getBatchNo()))).set(qOrderDelivery.buyerInnerStatus, BuyerInnerStatusEnum.HAVE_SETTLED.getCode()).execute();
    }

    /**
     * 结算服务 - 协同对账 - 对账单生成请款单采购订单数据
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderBuyerSettleReconciliationFeignResp> settleReconciliationBuyerOrders(OrderFeignIdsReq feignVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;

        return jpaQueryFactory.from(qOrder)
                .select(Projections.constructor(OrderBuyerSettleReconciliationFeignResp.class, qOrder.id, qOrderProduct.tax, qOrderProduct.taxRate, qOrderProduct.amount.sum()))
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .where(qOrder.id.in(feignVO.getOrderIds()))
                .groupBy(qOrder.id, qOrderProduct.tax, qOrderProduct.taxRate)
                .fetch();
    }

    /**
     * 结算服务 - 生成结算订单-待对账列表数据来源时, 根据订单编码查询订单信息()
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderQueryResp> findOrdersByNos(OrderFeignNosReq feignVO) {
        if (CollectionUtils.isEmpty(feignVO.getOrderNos())){
            return new ArrayList<>();
        }
        List<OrderDO> orders = orderRepository.findByOrderNoIn(feignVO.getOrderNos());
        if (CollectionUtils.isEmpty(orders)){
            return new ArrayList<>();
        }
        return orders.stream().map(v ->new OrderQueryResp(v.getId(), v.getOrderNo(), v.getCurrencyType())).collect(Collectors.toList());
    }

    /**
     * “售后服务” - 查询订单商品使用“平台优惠券”后的优惠抵扣金额
     *
     * @param feignVO 接口参数
     * @return 查询结果
     */
    @Override
    public List<OrderProductCouponFeignResp> findOrderProductCouponAmounts(OrderFeignIdsReq feignVO) {
        QOrderDO qOrder = QOrderDO.orderDO;
        QOrderCouponDO qOrderCoupon = QOrderCouponDO.orderCouponDO;
        QOrderProductDO qOrderProduct = QOrderProductDO.orderProductDO;

        //查询订单是否有“平台优惠券”，每个订单最多只能使用一个“平台优惠券”
        return jpaQueryFactory.select(Projections.constructor(OrderProductCouponFeignResp.class, qOrderProduct.id, qOrderProduct.skuId, qOrderCoupon.couponId, qOrderProduct.platformCouponAmount))
                .from(qOrder)
                .leftJoin(qOrderCoupon).on(qOrder.id.eq(qOrderCoupon.order.id))
                .leftJoin(qOrderProduct).on(qOrder.id.eq(qOrderProduct.order.id))
                .where(qOrder.id.in(feignVO.getOrderIds()).and(qOrderCoupon.belongType.eq(BelongTypeEnum.PLATFORM.getCode())))
                .fetch();
    }

    @Override
    public List<OrderFreeExpressConfigResp> getOrderFreeExpressConfigList(OrderFreeExpressFeignReq feignVO) {
        //构造多字段查询条件
        List<MemberAndRoleIdDTO> memberAndRoleIdDTOS = feignVO.getItemList().stream().map(o -> new MemberAndRoleIdDTO(o.getMemberId(), o.getRoleId())).collect(Collectors.toList());
        return orderParamConfigService.getOrderFreeExpressConfigList(memberAndRoleIdDTOS);
    }


    public List<OrderScoreConfigResp> getOrderScoreConfigList(OrderScoreFeignReq feignVO) {
        //构造多字段查询条件
        List<MemberAndRoleIdDTO> memberAndRoleIdDTOS = feignVO.getItemList().stream().map(o -> new MemberAndRoleIdDTO(o.getMemberId(), o.getRoleId())).collect(Collectors.toList());
        return orderParamConfigService.getOrderScoreConfigList(memberAndRoleIdDTOS);
    }

    /**
     * 根据物料信息查询订单(商品能力- 物料价格库)
     * <AUTHOR>
     * @since 2022/4/2
     **/
    @Override
    public OrderPriceQueryResp materielPriceByMateriel(OrderPriceQueryReq request) {
        String code = request.getCode();
        Long memberId = request.getMemberId();
        Long memberRoleId = request.getMemberRoleId();
        QOrderProductDO qOrderProductDO = QOrderProductDO.orderProductDO;
        QOrderDO qOrderDO = QOrderDO.orderDO;
        BooleanBuilder predicates = new BooleanBuilder();
        predicates.and(qOrderDO.buyerMemberId.eq(memberId));
        predicates.and(qOrderDO.buyerRoleId.eq(memberRoleId));
        //订单类型为 采购询价合同、采购竞价合同、采购招标合同、请购单采购
        predicates.and(qOrderDO.orderType.in(OrderTypeEnum.QUERY_PRICE_CONTRACT.getCode(), OrderTypeEnum.PRICE_COMPETITION_CONTRACT_PURCHASE.getCode(), OrderTypeEnum.PURCHASE_CONTRACT_BIDDING.getCode(), OrderTypeEnum.REQUISITION_TO_PURCHASE.getCode()));
        predicates.and(qOrderProductDO.productNo.eq(code));

        JPAQuery<OrderPriceQueryResp> query = jpaQueryFactory.select(
                Projections.bean(OrderPriceQueryResp.class,
                        qOrderProductDO.productNo.as("code"),
                        qOrderProductDO.price.as("totalAmount"),
                        qOrderDO.orderNo.as("contractNo"),
                        qOrderDO.createTime.as("startTime"),
                        qOrderDO.createTime.as("endTime"),
                        qOrderDO.vendorMemberName.as("partyBName"),
                        qOrderDO.vendorMemberId.as("partyBMemberId"),
                        qOrderDO.vendorRoleId.as("partyBRoleId")))
                .from(qOrderDO)
                .innerJoin(qOrderProductDO)
                .on(qOrderDO.id.eq(qOrderProductDO.order.id))
                .where(predicates);
        List<OrderPriceQueryResp> queryVOS = query.fetch();
        if (CollectionUtils.isEmpty(queryVOS)){
            return null;
        }
        //筛选最新的合同信息
        return queryVOS.stream().max(Comparator.comparing(OrderPriceQueryResp::getStartTime)).orElse(null);
    }

    @Override
    @Transactional
    public Void updateOrderProductRequisitionedByAddRequisition(List<UpdateOrderProductRequisitionedFeignReq> feignVOS) {
        //更新新的关系
        dealUpdateOrderProductRequisitioned(feignVOS);
        return null;
    }


    @Override
    @Transactional
    public Void updateOrderProductRequisitionedByUpdateRequisition(UpdateOrderProductRequisitionedByUpdateRequisitionFeignReq feignVO) {
        List<Long> oldPurchaseProductIds = feignVO.getOldPurchaseProductIds();
        List<UpdateOrderProductRequisitionedFeignReq> feignVOList = feignVO.getFeignVOList();

        //更新新的关系
        dealUpdateOrderProductRequisitioned(feignVOList);

        //删除原先的关系
        if (!CollectionUtils.isEmpty(oldPurchaseProductIds)){
            orderRequisitionProductRepository.deleteByPurchaseProductIdIn(oldPurchaseProductIds);
        }

        return null;
    }

//    @Override
//    @Transactional
//    public Void updateOrderContractTextUrl(OrderContractTextUpdateSignReq requestVO) {
//        return contractTextService.updateOrderContractTextUrl(requestVO);
//    }

    @Override
    public Boolean checkUnclosedOrder(OrderFeignMemberIdReq feignVO) {
        return baseOrderService.checkUnclosedOrder(feignVO.getMemberId());
    }

    @Override
    public String findBuyerBizUserIdByOrderNo(String orderNo) {
        OrderDO orderDO = orderRepository.findFirstByOrderNo(orderNo);
        if (Objects.isNull(orderDO)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DOES_NOT_EXIST);
        }
        return orderDO.getBuyerBizUserId();
    }

    private void dealUpdateOrderProductRequisitioned(List<UpdateOrderProductRequisitionedFeignReq> feignVOList){
        feignVOList.forEach(vo ->{
            ArrayList<OrderRequisitionProductDO> orderRequisitionProductDOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(vo.getOrderProductIds())){
                List<OrderProductDO> orderProductDOS = orderProductRepository.findByIdIn(vo.getOrderProductIds());
                orderProductDOS.forEach(orderProductDO -> {
                    orderProductDO.setRequisitioned(true);
                    List<OrderRequisitionProductDO> purchaseProduct = orderProductDO.getPurchaseProduct();
                    OrderRequisitionProductDO orderRequisitionProduct = new OrderRequisitionProductDO();
                    orderRequisitionProduct.setPurchaseProductId(vo.getPurchaseProductId());
                    if (CollectionUtils.isEmpty(purchaseProduct)){
                        orderProductDO.setPurchaseProduct(Stream.of(orderRequisitionProduct).collect(Collectors.toList()));
                    }else {
                        purchaseProduct.add(orderRequisitionProduct);
                        orderProductDO.setPurchaseProduct(purchaseProduct);
                    }
                    orderRequisitionProduct.setProduct(orderProductDO);
                    orderRequisitionProductDOS.addAll(purchaseProduct);
                });
                orderRequisitionProductRepository.saveAll(orderRequisitionProductDOS);
            }
        });
    }
}
