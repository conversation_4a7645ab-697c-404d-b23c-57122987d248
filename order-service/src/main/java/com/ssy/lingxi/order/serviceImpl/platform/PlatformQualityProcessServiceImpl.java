package com.ssy.lingxi.order.serviceImpl.platform;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.engine.EngineRuleQueryReq;
import com.ssy.lingxi.common.model.req.engine.ProcessEngineReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.model.resp.engine.ProcessEngineRuleResp;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.engine.api.enums.ProcessDefaultEnum;
import com.ssy.lingxi.engine.api.enums.ProcessSourceEnum;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignPageQueryResp;
import com.ssy.lingxi.order.entity.BaseQualityProcessDO;
import com.ssy.lingxi.order.entity.PlatformQualityProcessDO;
import com.ssy.lingxi.order.entity.PlatformQualityProcessMemberDO;
import com.ssy.lingxi.order.entity.QualityProcessDO;
import com.ssy.lingxi.order.enums.QualityProcessEnum;
import com.ssy.lingxi.order.enums.QualityProcessStatusEnum;
import com.ssy.lingxi.order.enums.QualityProcessTypeEnum;
import com.ssy.lingxi.order.model.dto.ProcessQueryRequest;
import com.ssy.lingxi.order.model.dto.SaveDefaultRequest;
import com.ssy.lingxi.order.model.req.platform.*;
import com.ssy.lingxi.order.model.resp.platform.PlatformBaseQualityProcessQueryResp;
import com.ssy.lingxi.order.model.resp.platform.PlatformQualityProcessDetailQueryResp;
import com.ssy.lingxi.order.model.resp.platform.PlatformQualityProcessMemberQueryResp;
import com.ssy.lingxi.order.model.resp.platform.PlatformQualityProcessPageQueryResp;
import com.ssy.lingxi.order.repository.BaseQualityProcessRepository;
import com.ssy.lingxi.order.repository.PlatformQualityProcessMemberRepository;
import com.ssy.lingxi.order.repository.PlatformQualityProcessRepository;
import com.ssy.lingxi.order.repository.QualityProcessRepository;
import com.ssy.lingxi.order.service.platform.IPlatformQualityProcessService;
import com.ssy.lingxi.order.service.web.IQualityProcessService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台后台 - 质量规则配置相关接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022-06-08
 */
@Service
public class PlatformQualityProcessServiceImpl implements IPlatformQualityProcessService {
    @Resource
    private BaseQualityProcessRepository baseQualityProcessRepository;

    @Resource
    private PlatformQualityProcessRepository platformQualityProcessRepository;

    @Resource
    private QualityProcessRepository qualityProcessRepository;

    @Resource
    private IQualityProcessService qualityProcessService;

    @Resource
    private PlatformQualityProcessMemberRepository platformQualityProcessMemberRepository;

    @Resource
    private IMemberFeign memberInnerControllerFeign;

    @Transactional
    @Override
    public Void saveBaseProcess(ProcessEngineReq engineBO) {

        // 查询基础工作流
        BaseQualityProcessDO baseProcess = baseQualityProcessRepository.findByProcessKeyAndProcessType(engineBO.getProcessKey(), engineBO.getProcessType()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_PROCESS_INVALID));

        // 基础工作流关联流程引擎
        baseProcess.setEngineId(engineBO.getEngineId());
        baseProcess.setProcessName(engineBO.getProcessName());
        baseProcess.setIsDefault(engineBO.getIsDefault());
        baseProcess.setProcessImage(engineBO.getProcessImage());
        baseProcess.setDescription(engineBO.getDescription());
        baseQualityProcessRepository.saveAndFlush(baseProcess);

        // 创建平台默认工作流
        this.createDefaultProcess(baseProcess, engineBO);

        // 创建能力中心默认工作流
        qualityProcessService.saveDefaultProcess(baseProcess, engineBO);

        return null;
    }

    /**
     * 创建平台默认工作流
     *
     * @param baseProcess 基础流程
     * @param engineBO    流程引擎
     */
    private void createDefaultProcess(BaseQualityProcessDO baseProcess, ProcessEngineReq engineBO) {

        // 查询当前类型的默认流程
        PlatformQualityProcessDO defaultProcess = platformQualityProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode());

        // 不存在则创建默认流程
        if (Objects.equals(engineBO.getIsDefault(), ProcessDefaultEnum.YES.getCode()) && Objects.isNull(defaultProcess)) {
            createDefault(baseProcess);
            return;
        }

        // 非默认流程不处理
        if (Objects.isNull(defaultProcess)) {
            return;
        }

        // 取消默认则删除
        if (Objects.equals(ProcessDefaultEnum.NO.getCode(), engineBO.getIsDefault())) {
            platformQualityProcessRepository.delete(defaultProcess);
            return;
        }

        // 更新默认工作流
        defaultProcess.setName(baseProcess.getProcessName());
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        defaultProcess.setProcess(baseProcess);
        platformQualityProcessRepository.save(defaultProcess);
    }

    /**
     * 创建默认工作流
     *
     * @param baseProcess 基础流程
     */
    private void createDefault(BaseQualityProcessDO baseProcess) {
        PlatformQualityProcessDO defaultProcess = new PlatformQualityProcessDO();
        defaultProcess.setProcessKey(baseProcess.getProcessKey());
        defaultProcess.setName(baseProcess.getName());
        defaultProcess.setMembers(new ArrayList<>());
        defaultProcess.setAllMembers(Boolean.TRUE);
        defaultProcess.setStatus(EnableDisableStatusEnum.ENABLE.getCode());
        defaultProcess.setCreateTime(LocalDateTime.now());
        defaultProcess.setSource(ProcessSourceEnum.PAAS.getCode());
        defaultProcess.setIsDefault(ProcessDefaultEnum.YES.getCode());
        defaultProcess.setProcess(baseProcess);
        defaultProcess.setProcessType(baseProcess.getProcessType());
        platformQualityProcessRepository.saveAndFlush(defaultProcess);
    }

    @Override
    public List<ProcessEngineRuleResp> getMemberProcess(EngineRuleQueryReq engineRuleQueryReq) {

        // 查询流程
        List<QualityProcessDO> memberProcessList = qualityProcessService.getMemberProcess(engineRuleQueryReq.getMemberId(), engineRuleQueryReq.getMemberRoleId(), engineRuleQueryReq.getType());

        if (memberProcessList.isEmpty()) {
            return new ArrayList<>();
        }

        // 组装数据
        return memberProcessList.stream().map(memberProcess -> {
            ProcessEngineRuleResp engineRuleVO = new ProcessEngineRuleResp();
            engineRuleVO.setProcessRuleId(memberProcess.getId());
            engineRuleVO.setIsDefault(memberProcess.getIsDefault());
            engineRuleVO.setProcessKey(memberProcess.getProcessKey());
            return engineRuleVO;
        }).collect(Collectors.toList());
    }

    /**
     * 分页查询质量管理流程规则配置
     *
     * @param sysUser 当前登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<PlatformQualityProcessPageQueryResp> pageQualityManageProcess(UserLoginCacheDTO sysUser, PlatformQualityProcessPageDataReq pageVO) {
        // step 1:构建查询条件

        // 查询平台默认
        List<Integer> types = platformQualityProcessRepository.findByIsDefaultAndSource(ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.SYSTEM.getCode()).stream().map(PlatformQualityProcessDO::getProcessType).collect(Collectors.toList());

        // 需要查询默认流程类型
        List<Integer> typeList = Arrays.stream(QualityProcessEnum.values()).map(QualityProcessEnum::getProcessType).distinct().filter(type -> !types.contains(type)).collect(Collectors.toList());

        // 分页查询
        Pageable pageable = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("isDefault", "id").descending());

        // 查询条件
        Specification<PlatformQualityProcessDO> spec = (root, query, builder) -> {

            // 平台流程
            List<Predicate> cond = new ArrayList<>();
            cond.add(builder.equal(root.get("source"), ProcessSourceEnum.SYSTEM.getCode()));
            if (StringUtils.hasText(pageVO.getName())) {
                cond.add(builder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }
            if (typeList.isEmpty()) {
                return builder.and(cond.toArray(new Predicate[0]));
            }

            // 系统默认
            List<Predicate> orList = new ArrayList<>();
            orList.add(builder.equal(root.get("source"), ProcessSourceEnum.PAAS.getCode()));
            orList.add(builder.in(root.get("processType")).value(typeList));
            if (StringUtils.hasText(pageVO.getName())) {
                orList.add(builder.like(root.get("name").as(String.class), "%".concat(pageVO.getName()).concat("%")));
            }

            // 组合查询
            return builder.or(builder.and(cond.toArray(new Predicate[0])), builder.and(orList.toArray(new Predicate[0])));
        };

        // step 2:查询数据
        Page<PlatformQualityProcessDO> pageList = platformQualityProcessRepository.findAll(spec, pageable);
        // step 3:组装数据
        return new PageDataResp<>(pageList.getTotalElements(), pageList.getContent().stream().map(this::builderPlatformQualityProcessPageQueryVO).collect(Collectors.toList()));
    }

    @Override
    public Void saveDefault(SaveDefaultRequest defaultRequest) {

        // 查询当前规则
        BaseQualityProcessDO baseProcess = baseQualityProcessRepository.findById(defaultRequest.getProcessId()).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_PROCESS_INVALID));

        // 查询默认流程
        PlatformQualityProcessDO defaultProcess = platformQualityProcessRepository.findByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.SYSTEM.getCode()).stream().findFirst().orElse(null);

        if (Objects.isNull(defaultProcess)) {

            // 系统默认流程
            PlatformQualityProcessDO defaultProcessPaas = Optional.ofNullable(platformQualityProcessRepository.findFirstByProcessTypeAndIsDefaultAndSource(baseProcess.getProcessType(), ProcessDefaultEnum.YES.getCode(), ProcessSourceEnum.PAAS.getCode())).orElseThrow(() -> new BusinessException(ResponseCodeEnum.ENGINE_DEFAULT_PROCESS_NON_EXISTENT));

            // 保存默认流程
            defaultProcess = BeanUtil.copyProperties(defaultProcessPaas, PlatformQualityProcessDO.class);
            defaultProcess.setId(null);
            defaultProcess.setProcessKey(baseProcess.getProcessKey());
            defaultProcess.setName(Optional.ofNullable(baseProcess.getProcessName()).orElse(baseProcess.getName()));
            defaultProcess.setProcess(baseProcess);
            defaultProcess.setSource(ProcessSourceEnum.SYSTEM.getCode());
            platformQualityProcessRepository.save(defaultProcess);

        } else {

            // 更新默认流程
            defaultProcess.setProcessKey(baseProcess.getProcessKey());
            defaultProcess.setName(Optional.ofNullable(baseProcess.getProcessName()).orElse(baseProcess.getName()));
            defaultProcess.setProcessType(baseProcess.getProcessType());
            defaultProcess.setProcess(baseProcess);
            platformQualityProcessRepository.save(defaultProcess);
        }

        // 设置能力中心默认流程
        qualityProcessService.defaultProcess(defaultProcess);

        return null;
    }

    /**
     * 将数据库的平台质量流程规则配置组装成返回给前端的结构
     *
     * @param entity 数据库的平台质量管理流程规则配置
     * @return 给前端的结构
     */
    private PlatformQualityProcessPageQueryResp builderPlatformQualityProcessPageQueryVO(PlatformQualityProcessDO entity) {
        PlatformQualityProcessPageQueryResp target = new PlatformQualityProcessPageQueryResp();
        target.setProcessId(entity.getId());
        if (Objects.equals(ProcessDefaultEnum.YES.getCode(), entity.getIsDefault())) {
            target.setName(Optional.ofNullable(entity.getProcess().getProcessName()).orElse(entity.getProcess().getName()));
        } else {
            target.setName(entity.getName());
        }
        target.setStatus(entity.getStatus());
        target.setStatusName(QualityProcessStatusEnum.getMessage(entity.getStatus()));
        target.setCreateTime(entity.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        target.setProcessType(entity.getProcessType());
        target.setIsDefault(Optional.ofNullable(entity.getIsDefault()).orElse(0));
        target.setProcessName(entity.getProcess().getName());
        return target;
    }

    /**
     * 新增质量管理规则页面 - 查询基础质量管理流程列表
     *
     * @param sysUser 当前登录用户
     * @return 查询结果
     */
    @Override
    public List<PlatformBaseQualityProcessQueryResp> listBaseQualityProcess(UserLoginCacheDTO sysUser, ProcessQueryRequest queryRequest) {
        Specification<BaseQualityProcessDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> andList = new ArrayList<>();
            if (Objects.nonNull(queryRequest.getProcessType())) {
                andList.add(criteriaBuilder.equal(root.get("processType").as(Integer.class), queryRequest.getProcessType()));
            }
            return criteriaBuilder.and(andList.toArray(new Predicate[0]));
        };
        return baseQualityProcessRepository.findAll(specification, Sort.by("code").ascending()).stream().map(this::builderPlatformBaseQualityProcessVO).collect(Collectors.toList());
    }

    /**
     * 封装数据
     *
     * @param baseQualityProcessDO 初始参数
     * @return 封装结果
     */
    private PlatformBaseQualityProcessQueryResp builderPlatformBaseQualityProcessVO(BaseQualityProcessDO baseQualityProcessDO) {
        PlatformBaseQualityProcessQueryResp target = new PlatformBaseQualityProcessQueryResp();
        target.setBaseProcessId(baseQualityProcessDO.getId());
        target.setProcessName(Optional.ofNullable(baseQualityProcessDO.getProcessName()).orElse(QualityProcessEnum.getNameByCode(baseQualityProcessDO.getCode())));
        target.setProcessType(baseQualityProcessDO.getProcessType());
        target.setProcessTypeName(QualityProcessTypeEnum.getMessage(baseQualityProcessDO.getProcessType()));
        target.setDescription(Optional.ofNullable(baseQualityProcessDO.getDescription()).orElse(QualityProcessEnum.getRemarkByCode(baseQualityProcessDO.getCode())));
        target.setProcessImage(baseQualityProcessDO.getProcessImage());
        return target;
    }

    /**
     * 新增质量管理流程规则
     *
     * @param sysUser 当前登录用户
     * @param saveVO  接口参数
     * @return 新增结果
     */
    @Override
    @Transactional
    public Void save(UserLoginCacheDTO sysUser, PlatformQualityProcessReq saveVO) {
        // step 1:校验
        BaseQualityProcessDO checkWrapperResp = checkSave(saveVO);

        // step 2:组装数据
        PlatformQualityProcessDO entity = builderEntity(saveVO, checkWrapperResp);
        // step 3:保存
        platformQualityProcessRepository.saveAndFlush(entity);
        return null;
    }


    /**
     * 保存校验，同时获取基础质量管理流程
     *
     * @param saveVO 保存数据
     * @return 基础质量管理流程
     */
    private BaseQualityProcessDO checkSave(PlatformQualityProcessReq saveVO) {
        // step 1:判断基础流程
        BaseQualityProcessDO baseQualityProcess = baseQualityProcessRepository.findById(saveVO.getBaseProcessId()).orElse(null);
        if (Objects.isNull(baseQualityProcess)) {
            throw new BusinessException(ResponseCodeEnum.BASE_QUALITY_MANAGE_PROCESS_DOES_NOT_EXIST);
        }
        // step 2:校验、保存关联的会员
        if (!saveVO.getAllMembers() && CollectionUtils.isEmpty(saveVO.getMembers())) {
            throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_MEMBER_IS_EMPTY);
        }
        // step 3:判断是否已经存在启用的流程
        Specification<PlatformQualityProcessDO> spec = (root, query, builder) -> {
            List<Predicate> condition = new ArrayList<>();
            condition.add(builder.equal(root.get("process").as(BaseQualityProcessDO.class), baseQualityProcess));
            condition.add(builder.equal(root.get("isDefault").as(Integer.class), ProcessDefaultEnum.NO.getCode()));
            condition.add(builder.equal(root.get("status").as(Integer.class), QualityProcessStatusEnum.ENABLE.getCode()));
            return builder.and(condition.toArray(new Predicate[0]));
        };

        List<PlatformQualityProcessDO> qualityProcesses = platformQualityProcessRepository.findAll(spec);
        if (saveVO.getAllMembers()) {
            if (!CollectionUtils.isEmpty(qualityProcesses)) {
                throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_EXISTS);
            }
        } else {
            if (!CollectionUtils.isEmpty(qualityProcesses)) {
                //如果其中有条流程包括全部会员则抛出提示
                if (qualityProcesses.stream().anyMatch(PlatformQualityProcessDO::getAllMembers)) {
                    throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_EXISTS);
                }
                //获取需要保存的会员信息
                List<PlatformQualityProcessMemberReq> reqList = saveVO.getMembers();
                qualityProcesses.stream().map(PlatformQualityProcessDO::getMembers)
                        .forEach(members ->
                                members.forEach(member ->
                                        reqList.stream().filter(r -> Objects.equals(r.getMemberId(), member.getMemberId()) && Objects.equals(r.getRoleId(), member.getRoleId()))
                                                .forEach(r -> {
                                                    throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_EXISTS);
                                                })));
            }
        }
        // step 4: 判断当前
        return baseQualityProcess;
    }

    /**
     * 根据新增数据构建保存数据库数据
     *
     * @param saveVO               前端传输数据
     * @param baseQualityProcessDO 基础质量管理流程
     * @return 保存数据库的格式
     */
    private PlatformQualityProcessDO builderEntity(PlatformQualityProcessReq saveVO, BaseQualityProcessDO baseQualityProcessDO) {
        PlatformQualityProcessDO entity = new PlatformQualityProcessDO();
        entity.setCreateTime(LocalDateTime.now());
        entity.setName(saveVO.getName());
        entity.setProcessKey(baseQualityProcessDO.getProcessKey());
        entity.setProcessType(baseQualityProcessDO.getProcessType());
        entity.setStatus(QualityProcessStatusEnum.ENABLE.getCode());
        entity.setAllMembers(saveVO.getAllMembers());
        entity.setProcess(baseQualityProcessDO);
        List<PlatformQualityProcessMemberDO> members = builderPlatformQualityProcessMemberDO(entity, saveVO);
        entity.setMembers(members);
        entity.setIsDefault(ProcessDefaultEnum.NO.getCode());
        entity.setSource(ProcessSourceEnum.SYSTEM.getCode());
        return entity;
    }

    /**
     * 构建关联平台质量管理流程会员数据
     *
     * @param entity 平台质量管理流程
     * @param saveVO 保存质量管理流程数据
     * @return 关联平台质量管理流程会员数据
     */
    private List<PlatformQualityProcessMemberDO> builderPlatformQualityProcessMemberDO(PlatformQualityProcessDO entity, PlatformQualityProcessReq saveVO) {
        List<PlatformQualityProcessMemberDO> targetList = new ArrayList<>();
        if (CollectionUtils.isEmpty(saveVO.getMembers())) {
            return targetList;
        }
        List<PlatformQualityProcessMemberDO> members = saveVO.getMembers().stream().map(map -> {
            PlatformQualityProcessMemberDO target = new PlatformQualityProcessMemberDO();
            target.setProcess(entity);
            target.setMemberId(map.getMemberId());
            target.setRoleId(map.getRoleId());
            return target;
        }).collect(Collectors.toList());
        targetList.addAll(members);
        return targetList;
    }

    /**
     * 查询质量管理流程规则详情
     *
     * @param sysUser   当前登录用户
     * @param processId 请质量管理流程id， 必传且要大于0
     * @return 查询结果
     */
    @Override
    public PlatformQualityProcessDetailQueryResp getInfo(UserLoginCacheDTO sysUser, Long processId) {
        // step 1: 校验数据
        PlatformQualityProcessDO entity = platformQualityProcessRepository.findById(processId).orElse(null);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_DOES_NOT_EXIST);
        }

        // step 2: 组装数据
        PlatformQualityProcessDetailQueryResp target = new PlatformQualityProcessDetailQueryResp();
        target.setProcessId(entity.getId());
        target.setName(entity.getName());
        target.setStatus(entity.getStatus());
        target.setStatusName(QualityProcessStatusEnum.getMessage(entity.getStatus()));
        target.setBaseProcessId(Optional.ofNullable(entity.getProcess()).map(BaseQualityProcessDO::getId).orElse(0L));
        target.setAllMembers(entity.getAllMembers());
        target.setIsDefault(Optional.ofNullable(entity.getIsDefault()).orElse(0));
        baseQualityProcessRepository.findById(entity.getProcess().getId()).ifPresent(p -> {
            PlatformBaseQualityProcessQueryResp baseQualityProcessQueryVO = builderPlatformBaseQualityProcessVO(p);
            target.setBaseProcess(baseQualityProcessQueryVO);
        });
        return target;
    }

    /**
     * 分页查询质量管理流程规则适用会员列表
     *
     * @param sysUser 当前登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public List<PlatformQualityProcessMemberQueryResp> listQualityManageProcessMembers(UserLoginCacheDTO sysUser, PlatformQualityProcessMemberPageReq pageVO) {
        // step 1:校验数据
        PlatformQualityProcessDO entity = platformQualityProcessRepository.findById(pageVO.getProcessId()).orElse(null);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_DOES_NOT_EXIST);
        }
        if (entity.getAllMembers()) {
            return new ArrayList<>();
        }
        Specification<PlatformQualityProcessMemberDO> spec = (root, query, builder) -> builder.equal(root.get("process").as(PlatformQualityProcessDO.class), entity);
        List<PlatformQualityProcessMemberDO> members = platformQualityProcessMemberRepository.findAll(spec, Sort.by("id").descending());
        if (CollectionUtils.isEmpty(members)) {
            return new ArrayList<>();
        }
        // step 2:从会员服务查询
        List<MemberFeignReq> memberFeignReqList = members.stream().map(map -> {
            MemberFeignReq feignVO = new MemberFeignReq();
            feignVO.setMemberId(map.getMemberId());
            feignVO.setRoleId(map.getRoleId());
            return feignVO;
        }).collect(Collectors.toList());

        return findPlatformMembers(pageVO.getName(), memberFeignReqList);
    }

    /**
     * 查询平台会员的信息
     *
     * @param name              会员名称
     * @param memberFeignReqList 会员Id和角色Id列表
     * @return 查询结果
     */
    private List<PlatformQualityProcessMemberQueryResp> findPlatformMembers(String name, List<MemberFeignReq> memberFeignReqList) {
        WrapperResp<List<MemberFeignPageQueryResp>> feignResult = memberInnerControllerFeign.findPlatformMembers(memberFeignReqList);
        WrapperUtil.throwWhenFail(feignResult);

        if (CollectionUtils.isEmpty(feignResult.getData())) {
            return new ArrayList<>();
        }

        return feignResult.getData().stream().map(map -> {
                    PlatformQualityProcessMemberQueryResp target = new PlatformQualityProcessMemberQueryResp();
                    target.setMemberId(map.getMemberId());
                    target.setRoleId(map.getRoleId());
                    target.setName(map.getName());
                    target.setRoleName(map.getRoleName());
                    target.setMemberTypeName(map.getMemberTypeName());
                    target.setLevel(map.getLevel());
                    target.setLevelTag(map.getLevelTag());
                    return target;
                }).collect(Collectors.toList()).stream().filter(f -> !StringUtils.hasLength(name) || f.getName().contains(name.trim()))//判断是否根据会员名称筛选
                .sorted(Comparator.comparingLong(PlatformQualityProcessMemberQueryResp::getMemberId)//根据会员id和角色id排序
                        .thenComparingLong(PlatformQualityProcessMemberQueryResp::getRoleId)).collect(Collectors.toList());
    }

    /**
     * 修改质量管理流程规则
     *
     * @param sysUser  当前登录用户
     * @param updateVO 接口参数
     * @return 修改结果
     */
    @Override
    @Transactional
    public Void update(UserLoginCacheDTO sysUser, PlatformQualityProcessUpdateReq updateVO) {
        // step 1: 修改校验
        PlatformQualityProcessDO checkWrapperResp = checkUpdate(updateVO);

        // step 2:删除历史关联会员数据
        List<PlatformQualityProcessMemberDO> oldMembers = platformQualityProcessRepository.findById(updateVO.getProcessId()).map(PlatformQualityProcessDO::getMembers).orElse(null);
        if (!CollectionUtils.isEmpty(oldMembers)) {
            platformQualityProcessMemberRepository.deleteInBatch(oldMembers);
        }
        // step 3:组装数据
        checkWrapperResp.setCreateTime(LocalDateTime.now());
        checkWrapperResp.setName(updateVO.getName());
        checkWrapperResp.setProcessKey(checkWrapperResp.getProcess().getProcessKey());
        checkWrapperResp.setProcessType(checkWrapperResp.getProcess().getProcessType());
        checkWrapperResp.setStatus(checkWrapperResp.getStatus());
        checkWrapperResp.setAllMembers(updateVO.getAllMembers());
        checkWrapperResp.setMembers(builderPlatformQualityProcessMemberDO(checkWrapperResp, updateVO));
        // step 4:保存数据
        platformQualityProcessRepository.saveAndFlush(checkWrapperResp);
        return null;
    }

    /**
     * 校验修改平台质量管理流程
     *
     * @param updateVO 更新平台质量管理流程
     * @return 修改结果
     */
    private PlatformQualityProcessDO checkUpdate(PlatformQualityProcessUpdateReq updateVO) {
        // step 1:判断基础流程
        BaseQualityProcessDO baseQualityProcessDO = baseQualityProcessRepository.findById(updateVO.getBaseProcessId()).orElse(null);
        if (Objects.isNull(baseQualityProcessDO)) {
            throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_DOES_NOT_EXIST);
        }
        // step 2:校验是否存在质量流程
        PlatformQualityProcessDO entity = platformQualityProcessRepository.findById(updateVO.getProcessId()).orElse(null);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_DOES_NOT_EXIST);
        }

        if (QualityProcessStatusEnum.ENABLE.getCode().equals(Objects.requireNonNull(entity).getStatus())) {
            if (Objects.equals(entity.getStatus(), QualityProcessStatusEnum.ENABLE.getCode())) {
                throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_NOT_UPDATE_PERMISSION);
            }
            // step 3:校验、保存关联的会员
            if (!updateVO.getAllMembers() && CollectionUtils.isEmpty(updateVO.getMembers())) {
                throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_MEMBER_IS_EMPTY);
            }
        }
        //  step 4:校验是否已存在相同流程规则
        Specification<PlatformQualityProcessDO> spec =
                (root, query, builder) -> query.where(builder.equal(root.get("process").as(BaseQualityProcessDO.class), baseQualityProcessDO),
                        builder.notEqual(root.get("id").as(Long.class), updateVO.getProcessId()),
                        builder.notEqual(root.get("isDefault"), ProcessDefaultEnum.YES.getCode())).getRestriction();
        List<PlatformQualityProcessDO> all = platformQualityProcessRepository.findAll(spec);
        if (!CollectionUtils.isEmpty(all)) {
            throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_EXISTS);
        }
        entity.setProcess(baseQualityProcessDO);
        return entity;
    }

    /**
     * 构建关联平台质量管理流程会员数据
     *
     * @param entity 平台质量管理流程
     * @param saveVO 保存质量管理流程数据
     * @return 关联平台质量管理流程会员数据
     */
    private List<PlatformQualityProcessMemberDO> builderPlatformQualityProcessMemberDO(PlatformQualityProcessDO entity, PlatformQualityProcessUpdateReq saveVO) {
        List<PlatformQualityProcessMemberDO> targetList = new ArrayList<>();
        if (CollectionUtils.isEmpty(saveVO.getMembers())) {
            return targetList;
        }
        List<PlatformQualityProcessMemberDO> members = saveVO.getMembers().stream().map(map -> {
            PlatformQualityProcessMemberDO target = new PlatformQualityProcessMemberDO();
            target.setProcess(entity);
            target.setMemberId(map.getMemberId());
            target.setRoleId(map.getRoleId());
            return target;
        }).collect(Collectors.toList());
        targetList.addAll(members);
        return targetList;
    }

    /**
     * 修改质量管理流程规则状态
     *
     * @param sysUser        当前登录用户
     * @param updateStatusVO 接口参数
     * @return 修改结果
     */
    @Override
    @Transactional
    public Void updateStatus(UserLoginCacheDTO sysUser, PlatformQualityProcessUpdateStatusReq updateStatusVO) {
        // step 1:校验数据是否存在
        PlatformQualityProcessDO entity = platformQualityProcessRepository.findById(updateStatusVO.getProcessId()).orElse(null);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_DOES_NOT_EXIST);
        }
        // step 2:流程停用时
        if (Objects.equals(updateStatusVO.getStatus(), QualityProcessStatusEnum.DISABLE.getCode())) {
            //判断是否为默认流程
            if (Objects.equals(entity.getIsDefault(), ProcessDefaultEnum.YES.getCode())) {
                throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_IS_DEFAULT_NOT_STOP);
            }
            // 查看是否默认所有会员
            if (entity.getAllMembers()) {
                Specification<QualityProcessDO> spec = (root, query, builder) -> {
                    Predicate processKey = builder.equal(root.get("processKey").as(String.class), entity.getProcessKey());
                    Predicate isDefault = builder.equal(root.get("isDefault").as(Integer.class), entity.getIsDefault());
                    Predicate status = builder.equal(root.get("status").as(Integer.class), QualityProcessStatusEnum.ENABLE.getCode());
                    return query.where(processKey, isDefault, status).getRestriction();
                };
                List<QualityProcessDO> qualityProcessDOList = qualityProcessRepository.findAll(spec);
                if (!CollectionUtils.isEmpty(qualityProcessDOList)) {
                    throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_NOT_UPDATE_PERMISSION);
                }
            } else {
                //判断是否有会员正在使用该流程
                Specification<QualityProcessDO> spec = (root, query, builder) -> {
                    Predicate processKey = builder.equal(root.get("processKey").as(String.class), entity.getProcessKey());
                    Predicate isDefault = builder.equal(root.get("isDefault").as(Integer.class), entity.getIsDefault());
                    Predicate status = builder.equal(root.get("status").as(Integer.class), QualityProcessStatusEnum.ENABLE.getCode());
                    Predicate[] predicates = entity.getMembers().stream().map(map -> {
                        Predicate memberId = builder.equal(root.get("memberId").as(Long.class), map.getMemberId());
                        Predicate roleId = builder.equal(root.get("roleId").as(Long.class), map.getRoleId());
                        return builder.and(memberId, roleId);
                    }).toArray(Predicate[]::new);
                    Predicate or = builder.or(predicates);
                    return query.where(processKey, isDefault, status, or).getRestriction();
                };
                List<QualityProcessDO> qualityProcessDOS = qualityProcessRepository.findAll(spec);
                if (!CollectionUtils.isEmpty(qualityProcessDOS)) {
                    throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_NOT_UPDATE_PERMISSION);
                }
            }
        } else {
            //校验该流程能不能开启
            Specification<PlatformQualityProcessDO> specification = (root, query, builder) -> {
                Predicate processKey = builder.equal(root.get("processKey").as(String.class), entity.getProcessKey());
                Predicate isDefault = builder.equal(root.get("isDefault").as(Integer.class), entity.getIsDefault());
                Predicate status = builder.equal(root.get("status").as(Integer.class), QualityProcessStatusEnum.ENABLE.getCode());
                Predicate id = builder.notEqual(root.get("id").as(Long.class), entity.getId());
                return query.where(processKey, isDefault, status, id).getRestriction();
            };
            List<PlatformQualityProcessDO> platformQualityProcessDOList = platformQualityProcessRepository.findAll(specification);
            if (!CollectionUtils.isEmpty(platformQualityProcessDOList)) {
                //判断该流程是否存在开启全员
                if (platformQualityProcessDOList.stream().anyMatch(PlatformQualityProcessDO::getAllMembers)) {
                    throw new BusinessException(ResponseCodeEnum.QUALITY_MEMBER_USING_THIS_TYPE_OF_PROCESS);
                } else {
                    //判断当前流程会员是否已经有正在有效的流程
                    boolean anyMatch = platformQualityProcessDOList.stream().map(PlatformQualityProcessDO::getMembers)
                            .anyMatch(members -> members.stream().anyMatch(member -> entity.getMembers().stream().anyMatch(m -> Objects.equals(m.getMemberId(), member.getMemberId()) && Objects.equals(m.getRoleId(), member.getRoleId()))));
                    if (anyMatch) {
                        throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_EXISTS);
                    }
                }
            }
        }
        // step 3:修改状态
        entity.setStatus(updateStatusVO.getStatus());
        entity.setCreateTime(LocalDateTime.now());
        platformQualityProcessRepository.saveAndFlush(entity);
        return null;
    }

    /**
     * 删除质量管理流程规则
     *
     * @param sysUser   当前登录用户
     * @param processId 质量管理流程规则配置id
     * @return 删除结果
     */
    @Override
    public Void delete(UserLoginCacheDTO sysUser, Long processId) {
        // step 1:校验删除数据
        PlatformQualityProcessDO entity = platformQualityProcessRepository.findById(processId).orElse(null);
        if (Objects.isNull(entity)) {
            throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_PROCESS_DOES_NOT_EXIST);
        }
        if (Objects.equals(entity.getStatus(), QualityProcessStatusEnum.ENABLE.getCode())) {
            throw new BusinessException(ResponseCodeEnum.PLATFORM_QUALITY_MANAGE_NOT_DELETE_PERMISSION);
        }
        platformQualityProcessRepository.deleteById(processId);
        return null;
    }
}
