package com.ssy.lingxi.order.serviceImpl.platform;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.order.entity.QOrderInnerHistoryDO;
import com.ssy.lingxi.order.entity.QOrderOuterHistoryDO;
import com.ssy.lingxi.order.model.req.platform.PlatformOrderHistoryPageDataReq;
import com.ssy.lingxi.order.model.resp.platform.PlatformOrderInnerHistoryResp;
import com.ssy.lingxi.order.model.resp.platform.PlatformOrderOuterHistoryResp;
import com.ssy.lingxi.order.service.platform.IPlatformOrderHistoryService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 平台后台 - 日志中心 - 订单历史记录查询相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-12-04
 */
@Service
public class PlatformOrderHistoryServiceImpl implements IPlatformOrderHistoryService {
    @Resource
    private JPAQueryFactory jpaQueryFactory;

    /**
     * 分页查询订单外部历史记录
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<PlatformOrderOuterHistoryResp> pageOrderOuterHistories(UserLoginCacheDTO loginUser, PlatformOrderHistoryPageDataReq pageVO) {
        QOrderOuterHistoryDO qOrderOuterHistory = QOrderOuterHistoryDO.orderOuterHistoryDO;
        JPAQuery<PlatformOrderOuterHistoryResp> query = jpaQueryFactory
                .select(Projections.constructor(PlatformOrderOuterHistoryResp.class, qOrderOuterHistory.orderNo, qOrderOuterHistory.createTime, qOrderOuterHistory.operatorRoleName, qOrderOuterHistory.outerStatus, qOrderOuterHistory.statusName, qOrderOuterHistory.operateCode, qOrderOuterHistory.operation, qOrderOuterHistory.remark))
                .from(qOrderOuterHistory);

        //订单编号
        if(StringUtils.hasText(pageVO.getOrderNo())) {
            query.where(qOrderOuterHistory.orderNo.like("%" + pageVO.getOrderNo().trim() + "%"));
        }

        //起始时间
        if(StringUtils.hasText(pageVO.getStartDate())) {
            query.where(qOrderOuterHistory.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        //结束时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            query.where(qOrderOuterHistory.createTime.before(LocalDateTime.parse(pageVO.getStartDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        //Step 4: 倒序排序、分页、总数
        query.orderBy(qOrderOuterHistory.createTime.desc());
        query.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());
        long totalCount = query.fetchCount();

        return new PageDataResp<>(totalCount, query.fetch());
    }

    /**
     * 分页查询订单内部历史记录
     *
     * @param loginUser 登录用户
     * @param pageVO  接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<PlatformOrderInnerHistoryResp> pageOrderInnerHistories(UserLoginCacheDTO loginUser, PlatformOrderHistoryPageDataReq pageVO) {
        QOrderInnerHistoryDO qOrderInnerHistory = QOrderInnerHistoryDO.orderInnerHistoryDO;
        JPAQuery<PlatformOrderInnerHistoryResp> query = jpaQueryFactory
                .select(Projections.constructor(PlatformOrderInnerHistoryResp.class, qOrderInnerHistory.orderNo, qOrderInnerHistory.createTime, qOrderInnerHistory.operator, qOrderInnerHistory.department, qOrderInnerHistory.jobTitle, qOrderInnerHistory.memberType, qOrderInnerHistory.statusCode, qOrderInnerHistory.statusName, qOrderInnerHistory.operateCode, qOrderInnerHistory.operation, qOrderInnerHistory.remark))
                .from(qOrderInnerHistory);

        //订单编号
        if(StringUtils.hasText(pageVO.getOrderNo())) {
            query.where(qOrderInnerHistory.orderNo.like("%" + pageVO.getOrderNo().trim() + "%"));
        }

        //起始时间
        if(StringUtils.hasText(pageVO.getStartDate())) {
            query.where(qOrderInnerHistory.createTime.after(LocalDateTime.parse(pageVO.getStartDate().concat(" 00:00:00"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        //结束时间
        if(StringUtils.hasLength(pageVO.getEndDate())) {
            query.where(qOrderInnerHistory.createTime.before(LocalDateTime.parse(pageVO.getStartDate().concat(" 23:59:59"), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
        }

        //Step 4: 倒序排序、分页、总数
        query.orderBy(qOrderInnerHistory.createTime.desc());
        query.limit(pageVO.getPageSize()).offset(pageVO.getCurrentOffset());
        long totalCount = query.fetchCount();

        return new PageDataResp<>(totalCount, query.fetch());
    }
}
