package com.ssy.lingxi.order.serviceImpl.mobile;

import cn.hutool.core.util.ObjectUtil;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.order.entity.SalesmanPerformanceStatisticsDO;
import com.ssy.lingxi.order.model.resp.mobile.MobileAchievementCountQueryResp;
import com.ssy.lingxi.order.repository.SalesmanPerformanceStatisticsRepository;
import com.ssy.lingxi.order.service.mobile.IMobileWechatAppletSalesmanPerformanceStatisticsService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.stream.Collectors;

/**
 * 业务员微信小程序-业务员业绩统计相关接口
 *
 * <AUTHOR>
 * @version 2.02.18
 * @since 2022-03-28
 */
@Service
public class MobileWechatAppletSalesmanPerformanceStatisticsServiceImpl implements IMobileWechatAppletSalesmanPerformanceStatisticsService {

    @Resource
    SalesmanPerformanceStatisticsRepository salesmanPerformanceStatisticsRepository;

    /**
     * 添加业务员业绩月份统计
     *
     * @param mobileAchievementCountQueryResp 业务员业绩统计VO
     */
    @Override
    public void insertSalesmanPerformanceStatistics(MobileAchievementCountQueryResp mobileAchievementCountQueryResp) {
        SalesmanPerformanceStatisticsDO salesmanPerformanceStatisticsDO = new SalesmanPerformanceStatisticsDO();
        BeanUtils.copyProperties(mobileAchievementCountQueryResp, salesmanPerformanceStatisticsDO);
        salesmanPerformanceStatisticsRepository.saveAndFlush(salesmanPerformanceStatisticsDO);
    }

    /**
     * 修改业务员业绩月份统计
     *
     * @param mobileAchievementCountQueryResp 业务员业绩统计VO
     */
    @Override
    public void updateSalesmanPerformanceStatistics(MobileAchievementCountQueryResp mobileAchievementCountQueryResp) {
        SalesmanPerformanceStatisticsDO salesmanPerformanceStatisticsDO = new SalesmanPerformanceStatisticsDO();
        BeanUtils.copyProperties(mobileAchievementCountQueryResp, salesmanPerformanceStatisticsDO);
        salesmanPerformanceStatisticsRepository.saveAndFlush(salesmanPerformanceStatisticsDO);
    }

    /**
     * 分页查询业务员业绩统计
     *
     * @param memberUserId 业务员Id
     * @param pageDataReq       分页信息
     * @return 分页返回业务员业绩统计信息
     */
    @Override
    public PageDataResp<MobileAchievementCountQueryResp> pageSalesmanPerformanceStatistics(Long memberUserId, PageDataReq pageDataReq) {
        Pageable pageable = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize(), Sort.by("monthStatistical").descending());
        Specification<SalesmanPerformanceStatisticsDO> spec = (root, query, cb) -> cb.equal(root.get("memberUserId").as(Long.class), memberUserId);
        Page<SalesmanPerformanceStatisticsDO> result = salesmanPerformanceStatisticsRepository.findAll(spec, pageable);
        return new PageDataResp<>(result.getTotalElements(), result.getContent().stream().map(salesmanPerformanceStatistics -> {
            MobileAchievementCountQueryResp vo = new MobileAchievementCountQueryResp();
            BeanUtils.copyProperties(salesmanPerformanceStatistics, vo);
            return vo;
        }).collect(Collectors.toList()));
    }

    /**
     * 根据业务员Id和统计月份查询
     *
     * @param memberUserId     业务员Id
     * @param monthStatistical 统计月份
     * @return 返回业务员业绩统计详情
     */
    @Override
    public MobileAchievementCountQueryResp findByMemberUserIdAndMonthStatistical(Long memberUserId, LocalDate monthStatistical) {
        SalesmanPerformanceStatisticsDO salesmanPerformanceStatistics = salesmanPerformanceStatisticsRepository.findByMemberUserIdAndMonthStatistical(memberUserId, monthStatistical).orElse(null);
        if(ObjectUtil.isNotNull(salesmanPerformanceStatistics)){
            MobileAchievementCountQueryResp mobileAchievementCountQueryResp = new MobileAchievementCountQueryResp();
            BeanUtils.copyProperties(salesmanPerformanceStatistics, mobileAchievementCountQueryResp);
            return mobileAchievementCountQueryResp;
        }
        return null;
    }
}
