package com.ssy.lingxi.order.serviceImpl.feign;

import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.order.model.dto.OrderQuotationDTO;
import com.ssy.lingxi.order.service.feign.ITradeFeignService;
import com.ssy.lingxi.trade.api.feign.ITradeFeign;
import com.ssy.lingxi.trade.api.model.resp.AskPurchaseQuoteGoodsListResp;
import com.ssy.lingxi.trade.api.model.resp.InquiryListProductResp;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 交易服务Feign接口调用实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-08-26
 */
@Service
public class TradeFeignServiceImpl implements ITradeFeignService {
    @Resource
    private ITradeFeign tradeFeign;

    /**
     * 根据报价单号，查询订单商品信息
     *
     * @param quoteNo 报价单号
     * @return 查询结果
     */
    @Override
    public List<OrderQuotationDTO> findInquiryOrderPrices(String quoteNo) {
        WrapperResp<List<InquiryListProductResp>> quoteResult = tradeFeign.getInquiryProduct(quoteNo);
        WrapperUtil.throwWhenFail(quoteResult);

        return CollectionUtils.isEmpty(quoteResult.getData()) ? new ArrayList<>() : quoteResult.getData().stream().map(p -> {
            OrderQuotationDTO quotation = new OrderQuotationDTO();
            quotation.setInquiryId(p.getInquiryListId());
            quotation.setQuoteId(p.getProductQuotationId());
            quotation.setProductId(p.getCommodityId());
            quotation.setSkuId(p.getProductId());
            quotation.setProductName(p.getProductName());
            quotation.setCategory(p.getCategory());
            quotation.setBrand(p.getBrand());
            quotation.setUnit(p.getUnit());
            quotation.setQuantity(p.getPurchaseCount());
            quotation.setProductAmount(p.getMoney());
            quotation.setPrice(p.getPrice());
            quotation.setTaxRate(p.getTaxRate());
            quotation.setMinimumQuantity(p.getMinOrder());
            return quotation;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AskPurchaseQuoteGoodsListResp> getAskPurchaseQuoteGoodsList(Long askPurchaseQuoteId) {
        return WrapperUtil.getDataOrThrow(tradeFeign.getAskPurchaseQuoteGoodsList(new CommonIdReq(askPurchaseQuoteId)));
    }
}
