package com.ssy.lingxi.order.serviceImpl.base;

import com.ssy.lingxi.component.base.enums.order.OrderPurchaseProcessTypeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTradeProcessTypeEnum;
import com.ssy.lingxi.order.entity.BasePurchaseProcessDO;
import com.ssy.lingxi.order.enums.BasePurchaseProcessEnum;
import com.ssy.lingxi.order.model.dto.ProcessQueryRequest;
import com.ssy.lingxi.order.model.resp.platform.PlatformBasePurchaseProcessResp;
import com.ssy.lingxi.order.model.resp.process.BasePurchaseProcessResp;
import com.ssy.lingxi.order.repository.BasePurchaseProcessRepository;
import com.ssy.lingxi.order.service.base.IBasePurchaseProcessService;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 基础采购流程相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-24
 */
@Service
public class BasePurchaseProcessServiceImpl implements IBasePurchaseProcessService {
    @Resource
    private BasePurchaseProcessRepository basePurchaseProcessRepository;

    /**
     * 查询基础采购流程列表
     *
     * @return 基础采购流程列表
     */
    @Override
    public List<BasePurchaseProcessResp> listProcesses() {
        return basePurchaseProcessRepository.findAll(Sort.by("id").ascending()).stream().map(process -> {
            BasePurchaseProcessResp processVO = new BasePurchaseProcessResp();
            processVO.setBaseProcessid(process.getId());
            processVO.setProcessName(BasePurchaseProcessEnum.getNameByCode(process.getCode()));
            processVO.setProcessType(process.getProcessType());
            processVO.setProcessTypeName(OrderTradeProcessTypeEnum.getNameByCode(process.getProcessType()));
            processVO.setDescription(BasePurchaseProcessEnum.getRemarkByCode(process.getCode()));
            return processVO;
        }).collect(Collectors.toList());
    }

    /**
     * 从平台后台查询基础采购流程
     *
     * @return 基础采购流程
     */
    @Override
    public List<PlatformBasePurchaseProcessResp> listProcessByPlatform(ProcessQueryRequest queryRequest) {
        Specification<BasePurchaseProcessDO> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> andList = new ArrayList<>();
            if (Objects.nonNull(queryRequest.getProcessType())){
                andList.add(criteriaBuilder.equal(root.get("processType").as(Integer.class), queryRequest.getProcessType()));
            }
            return criteriaBuilder.and(andList.toArray(new Predicate[0]));
        };
        return basePurchaseProcessRepository.findAll(specification, Sort.by("id").ascending()).stream().map(this::createProcessVO).collect(Collectors.toList());
    }

    /**
     * 创建展示类
     * @param process 基础流程
     * @return PlatformBasePurchaseProcessVO
     */
    private PlatformBasePurchaseProcessResp createProcessVO(BasePurchaseProcessDO process){
        PlatformBasePurchaseProcessResp processVO = new PlatformBasePurchaseProcessResp();
        processVO.setBaseProcessid(process.getId());
        processVO.setProcessName(Optional.ofNullable(process.getProcessName()).orElse(BasePurchaseProcessEnum.getNameByCode(process.getCode())));
        processVO.setProcessType(process.getProcessType());
        processVO.setProcessTypeName(OrderPurchaseProcessTypeEnum.getNameByCode(process.getProcessType()));
        processVO.setDescription(BasePurchaseProcessEnum.getRemarkByCode(process.getCode()));
        processVO.setProcessImage(Optional.ofNullable(process.getProcessImage()).orElse(""));
        return processVO;
    }

    /**
     * 根据Id查询
     *
     * @param id id
     * @return 查询结果
     */
    @Override
    public BasePurchaseProcessDO findById(Long id) {
        return basePurchaseProcessRepository.findById(id).orElse(null);
    }
}
