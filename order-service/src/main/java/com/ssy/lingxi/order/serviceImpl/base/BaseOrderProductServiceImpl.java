package com.ssy.lingxi.order.serviceImpl.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.enums.DepositTypeEnum;
import com.ssy.lingxi.common.enums.order.OrderDeliveryTypeEnum;
import com.ssy.lingxi.common.enums.order.OrderSourceKindEnum;
import com.ssy.lingxi.common.enums.product.InsuranceCompanyEnum;
import com.ssy.lingxi.common.enums.product.UnitVisaTypeEnum;
import com.ssy.lingxi.common.model.dto.MemberAndRoleIdDTO;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.NumberUtil;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.marketing.ActivityTypeEnum;
import com.ssy.lingxi.component.base.enums.marketing.BelongTypeEnum;
import com.ssy.lingxi.component.base.enums.marketing.MerchantCouponTypeEnum;
import com.ssy.lingxi.component.base.enums.marketing.PlatformCouponTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberRelationTypeEnum;
import com.ssy.lingxi.component.base.enums.member.SfPaymentTypeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderTypeEnum;
import com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum;
import com.ssy.lingxi.component.base.enums.product.PriceTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.rest.model.resp.eos.GetCommodityCodeStatusResp;
import com.ssy.lingxi.component.rest.model.resp.eos.GoldPriceResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.contract.api.enums.DeliveryMethodEnum;
import com.ssy.lingxi.member.api.feign.IMemberCustomerProcessFeeDiscountFeign;
import com.ssy.lingxi.member.api.model.req.CalDiscountSkuReq;
import com.ssy.lingxi.member.api.model.req.CustomerCalReq;
import com.ssy.lingxi.member.api.model.resp.MobileCustomerFeeDiscountResp;
import com.ssy.lingxi.order.api.model.resp.OrderDepositResp;
import com.ssy.lingxi.order.api.model.resp.OrderFreeExpressConfigResp;
import com.ssy.lingxi.order.constant.OrderConstant;
import com.ssy.lingxi.order.domain.OrderDM;
import com.ssy.lingxi.order.entity.*;
import com.ssy.lingxi.order.enums.*;
import com.ssy.lingxi.order.model.bo.*;
import com.ssy.lingxi.order.model.dto.*;
import com.ssy.lingxi.order.model.req.baitai.CalculateReq;
import com.ssy.lingxi.order.model.req.basic.*;
import com.ssy.lingxi.order.model.req.buyer.BuyerPurchaseOrderReq;
import com.ssy.lingxi.order.model.req.mobile.BuyerOrderReq;
import com.ssy.lingxi.order.model.req.vendor.AgentPurchaseOrderReq;
import com.ssy.lingxi.order.model.req.vendor.VendorDeliveryReq;
import com.ssy.lingxi.order.model.req.vendor.VendorToDeliveryProductReq;
import com.ssy.lingxi.order.model.req.vendor.VendorUpdateProductPriceReq;
import com.ssy.lingxi.order.model.resp.baitai.CalculateResp;
import com.ssy.lingxi.order.model.resp.basic.*;
import com.ssy.lingxi.order.model.resp.vendor.VendorDeliveryProductResp;
import com.ssy.lingxi.order.repository.*;
import com.ssy.lingxi.order.service.baitai.LogisticsInsuranceFeeSettingService;
import com.ssy.lingxi.order.service.base.IBaseOrderProductService;
import com.ssy.lingxi.order.service.base.IBaseOrderPromotionService;
import com.ssy.lingxi.order.service.feign.*;
import com.ssy.lingxi.order.service.web.IOrderParamConfigService;
import com.ssy.lingxi.product.api.feign.ICommodityFeign;
import com.ssy.lingxi.product.api.model.req.warehouse.OrderCommodityOccupiedInventoryReq;
import com.ssy.lingxi.product.api.model.resp.commodity.CommodityExtraDataParamResp;
import com.ssy.lingxi.product.api.model.resp.commodity.CommoditySkuStockResp;
import com.ssy.lingxi.purchase.api.feign.IPurchaseRequisitionFeign;
import com.ssy.lingxi.purchase.api.model.resp.purchase.QuotedMaterielResp;
import com.ssy.lingxi.purchase.api.model.resp.purchase.RequisitionQueryDetailResp;
import com.ssy.lingxi.trade.api.model.resp.AskPurchaseQuoteGoodsListResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.ssy.lingxi.component.base.enums.product.CommoditySaleModeEnum.ORDER;

/**
 * 订单商品相关接口实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-18
 */
@Slf4j
@Service
public class BaseOrderProductServiceImpl implements IBaseOrderProductService {

    @Resource
    private IBaseOrderPromotionService baseOrderPromotionService;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderProductRepository orderProductRepository;

    @Resource
    private IProductFeignService productFeignService;

    @Resource
    private IMarketingFeignService marketingFeignService;

    @Resource
    private ILogisticsFeignService logisticsFeignService;

    @Resource
    private ITradeFeignService tradeFeignService;

    @Resource
    private IOrderParamConfigService orderParamConfigService;

    @Resource
    private OrderGiveCouponRepository orderGiveCouponRepository;

    @Resource
    private IPurchaseRequisitionFeign purchaseRequisitionFeign;

    @Resource
    private IPurchaseFeignService purchaseFeignService;

    @Resource
    private OrderProductPositionRepository orderProductPositionRepository;

    @Resource
    private OrderDeliveryProductRepository orderDeliveryProductRepository;

    @Resource
    private ICommodityFeign commodityFeign;

    @Resource
    private EosApiService eosApiService;

    @Resource
    private IMemberCustomerProcessFeeDiscountFeign memberCustomerProcessFeeDiscountFeign;

    @Resource
    private LogisticsInsuranceFeeSettingService logisticsInsuranceFeeSettingService;

    @Resource
    private CommodityExtraDataParamRepository commodityExtraDataParamRepository;

    /**
     * 批量查询订单商品列表
     *
     * @param orders 订单列表
     * @return 订单商品列表
     */
    @Override
    public List<OrderProductDO> findByOrderIn(List<OrderDO> orders) {
        return orderProductRepository.findByOrderIn(orders);
    }

    /**
     * 校验商品价格、运费
     *
     * @param buyerMemberId    采购会员Id
     * @param buyerRoleId      采购会员角色Id
     * @param memberType       登录用户的会员类型，以此判断采购商品是会员商品，还是渠道商品
     * @param shopId           前端商城Id
     * @param freight          前端传递的运费
     * @param vendorProductMap 供应商及其商品列表
     * @return 订单运费
     */
    @Override
    public BigDecimal checkOrderProductPrices(Long buyerMemberId, Long buyerRoleId, Integer memberType, Long shopId, BigDecimal freight, Map<VendorBO, List<OrderProductReq>> vendorProductMap) {
        //多供应商时，所有商品的运费方式必须为“无运费”或“卖家承担”
        if (vendorProductMap.size() > 1 && vendorProductMap.values().stream().flatMap(products -> products.stream().map(product -> NumberUtil.isNullOrNegative(product.getFreightType()) ? OrderFreightTypeEnum.VENDOR.getCode() : product.getFreightType())).anyMatch(freightType -> !freightType.equals(OrderFreightTypeEnum.VENDOR.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_FREIGHT_TYPE_MUST_BE_VENDOR);
        }

        for (Map.Entry<VendorBO, List<OrderProductReq>> entry : vendorProductMap.entrySet()) {
            //从商品服务获得商品阶梯价格
//            Wrapper<Map<Long, Map<String, BigDecimal>>> priceResult = productFeignService.batchCheckProductPrice(buyerMemberId, buyerRoleId, shopId, memberType, entry.getValue().stream().map(OrderProductVO::getSkuId).collect(Collectors.toList()));
//            if(priceResult.getCode() != ResponseCode.SUCCESS.getCode()) {
//                return Wrapper.fail(priceResult.getCode(), priceResult.getNameByCode());
//            }

            // 校验商品价格
            for (OrderProductReq product : entry.getValue()) {
//                Map<String, BigDecimal> priceMap = priceResult.getData().get(product.getSkuId());
//                if(priceMap == null) {
//                    return Wrapper.fail(ResponseCode.ORDER_PRODUCT_DOES_NOT_EXIST);
//                }
//
//                Wrapper<Void> priceCheckResult = checkProductPrice(priceMap, product.getQuantity(), product.getPrice());
//                if(priceCheckResult.getCode() != ResponseCode.SUCCESS.getCode()) {
//                    return Wrapper.fail(priceCheckResult.getCode(), priceCheckResult.getNameByCode());
//                }

                //校验其他参数（需要判断运费模板Id）
                if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())) {
                    if (!StringUtils.hasText(product.getReceiver())) {
                        throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_IS_MISSING);
                    }

                    if (!StringUtils.hasText(product.getPhone())) {
                        throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_PHONE_IS_MISSING);
                    }

                    if (!StringUtils.hasText(product.getAddress())) {
                        throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_ADDRESS_IS_MISSING);
                    }
                }
            }
        }

        return vendorProductMap.size() == 1 ? freight : BigDecimal.ZERO;
    }

    /**
     * App - （商品、会员、物流、营销服务）查询商品详情、折扣、物流、促销活动等信息
     *
     * @param orderVO        订单接口参数
     * @param vendorCount    供应商数量
     * @param processPayment 交易流程是否需要支付
     * @param firstPayRate   交易流程中第一批次中最小支付次数的支付比例
     * @return 校验、修改接口参数中的商品到手价格
     */
    @Override
    public BuyerOrderCheckBO checkMobileOrderProductPrices(UserLoginCacheDTO loginUser, BuyerOrderReq orderVO, int vendorCount, boolean processPayment, BigDecimal firstPayRate, OrderDepositResp orderDepositConfig, GoldPriceResp goldPrice) {
        Long buyerMemberId = loginUser.getMemberId();
        Long buyerRoleId = loginUser.getMemberRoleId();
        Integer buyerMemberLevelType = loginUser.getMemberLevelType();

        //****************************************************
        // Step 1： 接口参数、规则判断
        //****************************************************
        List<MobileOrderProductReq> orderProducts = orderVO.getProducts();
        // Step 1-1: 商品SkuId不能重复
//        if (orderProducts.size() != orderProducts.stream().map(MobileOrderProductReq::getSkuId).distinct().count()) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_SKU_ID_DUPLICATED);
//        }

        // Step 1-2 : 多供应商时，所有商品的运费方式必须为“无运费”或“卖家承担”
        if (vendorCount > 1 && orderProducts.stream().map(product -> NumberUtil.isNullOrNegative(product.getFreightType()) ? OrderFreightTypeEnum.VENDOR.getCode() : product.getFreightType()).anyMatch(freightType -> !freightType.equals(OrderFreightTypeEnum.VENDOR.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_FREIGHT_TYPE_MUST_BE_VENDOR);
        }

        // Step 1-3 : 到手价格：“普通商品”的到手价应该小于等于单价
//        if (orderProducts.stream().filter(product -> NumberUtil.isNullOrZero(product.getPromotionType())).anyMatch(product -> product.getRefPrice().compareTo(product.getPrice()) > 0)) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_SHOULD_LTE_PRICE);
//        }

        // Step 1-4 : 订单商品需要物流配送时，收货人信息不能为空
        if (orderProducts.stream().anyMatch(product -> product.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode())) && orderVO.getConsignee() == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CONSIGNEE_MUST_NOT_EMPTY);
        }

        // Step 1-4 : 检查订单接口参数：商品列表
        for (MobileOrderProductReq product : orderProducts) {
            //套餐主商品的套餐编号要大于0
            if (ObjectUtil.isEmpty(product.getPromotionType())) {
                product.setPromotionType(OrderPromotionTypeEnum.NONE.getCode());
            }
            if (product.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode()) && NumberUtil.isNullOrNegativeZero(product.getGroupNo())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_PRIMARY_OF_SET_GROUP_NO_SHOULD_GT_ZERO);
            }

            //如果是赠品，到手价要为0
            if (product.getPriceType().equals(PriceTypeEnum.GIFT.getCode()) && product.getRefPrice().compareTo(BigDecimal.ZERO) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_GIFT_REF_PRICE_SHOULD_BE_ZERO);
            }

            //被换购商品的parentSkuId要大于0，并且存在于商品列表中
            if (product.getPromotionType().equals(OrderPromotionTypeEnum.EXCHANGED.getCode())) {
                if (NumberUtil.isNullOrNegativeZero(product.getParentSkuId())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_EXCHANGED_PRODUCT_PARENT_SKU_ID_SHOULD_GT_ZERO);
                }

                if (orderVO.getProducts().stream().noneMatch(p -> p.getSkuId().equals(product.getParentSkuId()))) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_TO_EXCHANGED_PRODUCT_SKU_ID_SHOULD_INCLUDE_IN_PRODUCTS);
                }
            }

            //如果配送方式为“物流”，配送方式不能为空 (百泰收货，改为订单维度，所以废弃掉这段代码)
//            if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode())) {
//                if (NumberUtil.isNullOrZero(product.getFreightType())) {
//                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_FREIGHT_TYPE_CAN_NOT_BE_NULL);
//                }
//
//                //如果运费类型为“买家承担”，物流模板Id、商品重量不能为空或0
//                if (product.getFreightType().equals(OrderFreightTypeEnum.BUYER.getCode())) {
//                    if (NumberUtil.isNullOrNegativeZero(product.getLogisticsTemplateId())) {
//                        throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_LOGISTICS_TEMPLATE_ID_MUST_GREATER_THAN_ZERO);
//                    }
//
//                    if (NumberUtil.isNullOrNegativeZero(product.getWeight())) {
//                        throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_WEIGHT_MUST_GREATER_THAN_ZERO);
//                    }
//                }
//            }

            // 为方便后续校验，如果前端传Null值，将配送方式、自提地址Id，运费类型、营销活动列表设置为非Null值
            product.setDeliveryType(NumberUtil.isNullOrNegativeZero(product.getDeliveryType()) ? 0 : product.getDeliveryType());
            product.setAddressId(NumberUtil.isNullOrZero(product.getAddressId()) ? 0 : product.getAddressId());
            product.setFreightType(NumberUtil.isNullOrNegativeZero(product.getFreightType()) ? 0 : product.getFreightType());
            product.setPromotions(CollectionUtils.isEmpty(product.getPromotions()) ? new ArrayList<>() : product.getPromotions());
            //套餐子商品的到手价为0
            if (product.getPromotionType().equals(OrderPromotionTypeEnum.PART_OF_SET.getCode())) {
                product.setRefPrice(BigDecimal.ZERO);
            }
        }

        // 为方便后续校验，如果前端传Null值，将优惠券列表、积分抵扣列表、配送时间和备注设置成空列表
        orderVO.setCoupons(CollectionUtils.isEmpty(orderVO.getCoupons()) ? new ArrayList<>() : orderVO.getCoupons());
        orderVO.setDeductions(CollectionUtils.isEmpty(orderVO.getDeductions()) ? new ArrayList<>() : orderVO.getDeductions());
        orderVO.setDeliverTimes(CollectionUtils.isEmpty(orderVO.getDeliverTimes()) ? new ArrayList<>() : orderVO.getDeliverTimes());

        // Step 1-5 : 检查订单接口参数：优惠券列表
        for (OrderCouponReq coupon : orderVO.getCoupons()) {
            // 校验商家优惠券归属的会员
            if (coupon.getBelongType().equals(BelongTypeEnum.MERCHANT.getCode())) {
                if (NumberUtil.isNullOrNegativeZero(coupon.getVendorMemberId()) || NumberUtil.isNullOrNegativeZero(coupon.getVendorRoleId())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_VENDOR_ID_CAN_NOT_BE_NULL_OR_ZERO);
                }

                if (orderProducts.stream().noneMatch(p -> p.getVendorMemberId().equals(coupon.getVendorMemberId()) && p.getVendorRoleId().equals(coupon.getVendorRoleId()))) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_MERCHANT_COUPON_VENDOR_MUST_INCLUDE_IN_THE_PRODUCTS);
                }
            }

            //如果是商品优惠券，商品SkuId要大于0，且必须在商品列表中
            if (isProductCoupon(coupon.getBelongType(), coupon.getCouponType())) {
                if (NumberUtil.isNullOrNegativeZero(coupon.getSkuId())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_SKU_ID_SHOULD_GT_ZERO);
                }

                MobileOrderProductReq productVO = orderProducts.stream().filter(p -> p.getSkuId().equals(coupon.getSkuId())).findFirst().orElse(null);
                if (productVO == null) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_SKU_ID_MUST_INCLUDE_IN_PRODUCTS);
                }

                //如果是0元抵扣券，购买商品的数量必须为1
                if ((coupon.getBelongType().equals(BelongTypeEnum.PLATFORM.getCode()) && coupon.getCouponType().equals(PlatformCouponTypeEnum.ZERO_DISCOUNT.getCode())) || (coupon.getBelongType().equals(BelongTypeEnum.MERCHANT.getCode()) && coupon.getCouponType().equals(MerchantCouponTypeEnum.ZERO_DISCOUNT.getCode()))) {
                    if (productVO.getQuantity().compareTo(BigDecimal.ONE) != 0) {
                        throw new BusinessException(ResponseCodeEnum.ORDER_ZERO_COUPON_SKU_QUANTITY_MUST_EQUAL_TO_ONE);
                    }
                }

                //优惠券不能用于赠品
                if (productVO.getPriceType().equals(PriceTypeEnum.GIFT.getCode())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_CAN_NOT_USE_TO_GIFT_PRODUCT);
                }
            }
        }

        //Step 1-6: 最多只能使用一个平台优惠券
        if (orderVO.getCoupons().stream().filter(coupon -> coupon.getBelongType().equals(BelongTypeEnum.PLATFORM.getCode())).count() > 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_COUPON_CAN_NOT_EXCEED_ONE);
        }

        //****************************************************
        // Step 2: 从商品服务、会员服务查询商品详细信息，
        // 必须保证接口参数的商品与List<OrderProductDetailDTO>中的商品是一对一的关系（以SkuId判断）
        //****************************************************

        // Step 2-0: 校验优时商品状态
        if (CommoditySaleModeEnum.SPOT.getCode().equals(orderVO.getSaleMode())) {
            List<String> singleCodeList = orderProducts.stream().map(MobileOrderProductReq::getSingleCode).collect(Collectors.toList());
            List<GetCommodityCodeStatusResp> commodityCodeStatusRespList = eosApiService.getGoodsStatus(singleCodeList);
            // 判断commodityCodeStatusRespList里的kczt，是否都是库存，如果有一个不是未开单，则抛出异常
            if (commodityCodeStatusRespList.stream().anyMatch(status -> !"未开单".equals(status.getKdzt()))) {
                // 获取不可用的商品码
                String unavailableCodes = commodityCodeStatusRespList.stream()
                        .filter(status -> !"未开单".equals(status.getKdzt()))
                        .map(GetCommodityCodeStatusResp::getSptm)
                        .collect(Collectors.joining(", "));
                throw new BusinessException(unavailableCodes + "商品码不可用，请返回购车重新下单!");
            }
        }

        // Step 2-1: 在调用商品服务的接口中，校验下单接口传递的商品是否全包含在商品服务的查询结果中
        List<ProductSkuDTO> productStocks = orderProducts.stream().map(product -> new ProductSkuDTO(buyerMemberId, buyerRoleId, product.getVendorMemberId(), product.getVendorRoleId(), product.getSkuId(), product.getQuantity(), product.getPromotionType(), product.getGroupNo(), product.getParentSkuId(), product.getCommoditySingleId(), product.getNeedCertificate())).collect(Collectors.toList());
        List<OrderProductDetailDTO> productResult = productFeignService.findProductDetails(orderVO.getShopId(), buyerMemberId, buyerRoleId, buyerMemberLevelType, productStocks, orderVO.getSaleMode(), orderVO.getCertificateId());

        // Step 2-1-1: 解析挂签类型
        if (ObjectUtil.isNotEmpty(orderVO.getCertificateId()) && ObjectUtil.isEmpty(orderVO.getUnitVisaType())) {
            Optional<OrderProductDetailDTO> detailDTO = productResult.stream().filter(productDetailDTO -> ObjectUtil.isNotEmpty(productDetailDTO.getUnitVisaType())).findFirst();
            detailDTO.ifPresent(productVisa -> {
                orderVO.setUnitVisaType(productVisa.getUnitVisaType());
                orderVO.setVisaUnitPrice(productVisa.getVisaUnitPrice());
            });
        }

        // Step 2-2: 校验是否有商品已经下架
        if (productResult.stream().anyMatch(productDetail -> !productDetail.getPublished())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_IS_OFF_LINE);
        }

        // 获取工费优惠信息
        List<MobileCustomerFeeDiscountResp> customerFeeDiscountRespList = null;
        if (CommoditySaleModeEnum.SPOT.getCode().equals(orderVO.getSaleMode())) {
            CustomerCalReq customerCalReq = new CustomerCalReq();
            List<CalDiscountSkuReq> discountSkuReqList = productResult.stream().map(s -> {
                CalDiscountSkuReq calDiscountSkuReq = new CalDiscountSkuReq();
                calDiscountSkuReq.setSingleCode(s.getCommoditySingleCode());
                return calDiscountSkuReq;
            }).collect(Collectors.toList());
            customerCalReq.setSkuIdList(discountSkuReqList);
            customerCalReq.setCustomerId(loginUser.getMemberId());
            WrapperResp<List<MobileCustomerFeeDiscountResp>> calculatedDiscount = memberCustomerProcessFeeDiscountFeign.calculateDiscount(customerCalReq);
            if (WrapperUtil.isOk(calculatedDiscount)) {
                customerFeeDiscountRespList = calculatedDiscount.getData();
            }
        } else {
            // 获取工费优惠信息
            CustomerCalReq customerCalReq = new CustomerCalReq();
            List<CalDiscountSkuReq> discountSkuReqList = new ArrayList<>();
            productResult.forEach(sku -> {
                CalDiscountSkuReq calDiscountSkuReq = new CalDiscountSkuReq();
                calDiscountSkuReq.setSingleCode(null);
                calDiscountSkuReq.setBaseLaborCosts(sku.getBaseLaborCosts());
                calDiscountSkuReq.setAdditionalLaborCosts(sku.getAdditionalLaborCosts());
                calDiscountSkuReq.setPieceLaborCosts(sku.getPieceLaborCosts());
                calDiscountSkuReq.setQuantity(1);
                calDiscountSkuReq.setNetWeight(sku.getNetWeight());
                calDiscountSkuReq.setSkuId(sku.getSkuId());
                calDiscountSkuReq.setSaleMode(ORDER.getCode());
                discountSkuReqList.add(calDiscountSkuReq);
            });
            customerCalReq.setSkuIdList(discountSkuReqList);
            customerCalReq.setCustomerId(loginUser.getMemberId());
            WrapperResp<List<MobileCustomerFeeDiscountResp>> calculatedDiscount = memberCustomerProcessFeeDiscountFeign.calculateDiscount(customerCalReq);
            customerFeeDiscountRespList = calculatedDiscount.getData();
        }

        // Step 2-3-0: 判断保费
        if (orderVO.getSfInsuranceSelected() && ObjectUtil.isNotEmpty(orderVO.getSfInsuranceFee())) {
            CalculateReq calculateReq = new CalculateReq();
            calculateReq.setInsuranceAmount(orderVO.getSfInsuranceAmount());
            calculateReq.setInsuranceCompanyType(InsuranceCompanyEnum.SF_INSURANCE.getCode());
            WrapperResp<CalculateResp> respWrapperResp = logisticsInsuranceFeeSettingService.calculateInsuranceFee(calculateReq);
            if (orderVO.getSfInsuranceFee().compareTo(respWrapperResp.getData().getInsuranceFee()) != 0) {
                throw new BusinessException("顺丰保费不正确，请重新下单");
            }
        }

        if (orderVO.getPacInsuranceSelected() && ObjectUtil.isNotEmpty(orderVO.getPacInsuranceFee())) {
            CalculateReq calculateTwoReq = new CalculateReq();
            calculateTwoReq.setInsuranceAmount(orderVO.getPacInsuranceAmount());
            calculateTwoReq.setInsuranceCompanyType(InsuranceCompanyEnum.TAIPING_INSURANCE.getCode());
            WrapperResp<CalculateResp> respWrapperTwoResp = logisticsInsuranceFeeSettingService.calculateInsuranceFee(calculateTwoReq);
            if (orderVO.getPacInsuranceFee().compareTo(respWrapperTwoResp.getData().getInsuranceFee()) != 0) {
                throw new BusinessException("平安保费不正确，请重新下单");
            }
        }

        // 获取挂签价格
        BigDecimal certificatePrice = BigDecimal.ZERO;

        // Step 2-3: 重新构建商品单价
        for (OrderProductDetailDTO productDetail : productResult) {
            //BigDecimal priceResult = findProductPrice(productDetail.getPriceMap(), productDetail.getQuantity());

            // 黄金克重乘于实时金价productDetail.getGoldPrice()
            BigDecimal priceResult = productDetail.getNetWeight().multiply(goldPrice.getJj()).setScale(2, RoundingMode.HALF_UP);
            // 工费金额
            BigDecimal laborCosts = BigDecimal.ZERO;

            // 加上基础工费，附加工费，件工费，挂签费
            if (productDetail.getBaseLaborCosts() != null) {
                laborCosts = laborCosts.add(productDetail.getBaseLaborCosts().multiply(productDetail.getNetWeight()));
            }
            if (productDetail.getAdditionalLaborCosts() != null) {
                laborCosts = laborCosts.add(productDetail.getAdditionalLaborCosts().multiply(productDetail.getNetWeight()));
            }
            if (productDetail.getPieceLaborCosts() != null) {
                laborCosts = laborCosts.add(productDetail.getPieceLaborCosts());
            }
            priceResult = priceResult.add(laborCosts.setScale(3, RoundingMode.HALF_UP));
            if (productDetail.getVisaPrice() != null) {
                log.info("挂签费：{}, 挂签类型: {}", productDetail.getVisaPrice(), productDetail.getUnitVisaType());
                orderVO.setVisaUnitPrice(productDetail.getVisaUnitPrice());
                certificatePrice = certificatePrice.add(productDetail.getVisaPrice().multiply(productDetail.getQuantity()).setScale(2, RoundingMode.HALF_UP));
            }
            if (CollUtil.isNotEmpty(productDetail.getFreightSpaceSingleProductExtendList())) {
                // 把其它工费加上，fieldValue转换成BigDecimal
                BigDecimal otherLaborCosts = productDetail.getFreightSpaceSingleProductExtendList().stream()
                        .filter(ext -> StrUtil.isNotBlank(ext.getFieldValue()) && StrUtil.isNotBlank(ext.getFieldName()))
                        .map(ext -> new BigDecimal(ext.getFieldValue()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                //priceResult = priceResult.add(otherLaborCosts);
            }
            // 如果有工费优惠，则减去工费优惠
            if (CollectionUtil.isNotEmpty(customerFeeDiscountRespList)) {
                Optional<MobileCustomerFeeDiscountResp> discountRespOptional = Optional.empty();
                if (CommoditySaleModeEnum.SPOT.getCode().equals(orderVO.getSaleMode())) {
                    discountRespOptional = customerFeeDiscountRespList.stream().filter(customerFeeDiscountResp -> customerFeeDiscountResp.getSingleCode().equals(productDetail.getCommoditySingleCode())).findFirst();
                } else {
                    discountRespOptional = customerFeeDiscountRespList.stream().filter(customerFeeDiscountResp -> customerFeeDiscountResp.getSkuId().equals(productDetail.getSkuId())).findFirst();
                }
                if (discountRespOptional.isPresent()) {
                    // 优惠金额
                    BigDecimal discountAmount = discountRespOptional.get().getBaseLaborCostDiscountAmount() != null ? discountRespOptional.get().getBaseLaborCostDiscountAmount() : BigDecimal.ZERO;
                    discountAmount = discountAmount.add(discountRespOptional.get().getGramLaborCostDiscountAmount() != null ? discountRespOptional.get().getGramLaborCostDiscountAmount() : BigDecimal.ZERO);
                    discountAmount = discountAmount.add(discountRespOptional.get().getPieceLaborCostDiscountAmount() != null ? discountRespOptional.get().getPieceLaborCostDiscountAmount() : BigDecimal.ZERO);
                    priceResult = priceResult.subtract(discountAmount);

                    if (CommoditySaleModeEnum.SPOT.getCode().equals(orderVO.getSaleMode())) {
                        Optional<MobileCustomerFeeDiscountResp> finalDiscountRespOptional = discountRespOptional;
                        Optional<MobileOrderProductReq> orderProductReq = orderProducts.stream().filter(productReq -> productReq.getSingleCode().equals(finalDiscountRespOptional.get().getSingleCode())).findFirst();
                        orderProductReq.ifPresent(productReq -> productReq.getFeeDiscountDetails().setDiscountPerGram(finalDiscountRespOptional.get().getGramLaborCostDiscountPerGram()));
                    } else {
                        Optional<MobileCustomerFeeDiscountResp> finalDiscountRespOptional = discountRespOptional;
                        Optional<MobileOrderProductReq> orderProductReq = orderProducts.stream().filter(productReq -> productReq.getSkuId().equals(finalDiscountRespOptional.get().getSkuId())).findFirst();
                        orderProductReq.ifPresent(productReq -> productReq.getFeeDiscountDetails().setDiscountPerGram(finalDiscountRespOptional.get().getGramLaborCostDiscountPerGram()));
                    }
                }
            }

            productDetail.setPrice(priceResult);
            productDetail.setRefPrice(priceResult);
            if (CommoditySaleModeEnum.ORDER.getCode().equals(orderVO.getSaleMode())) {
                if (DepositTypeEnum.FIXED_AMOUNT.getCode().equals(orderDepositConfig.getDepositType())) {
                    // 固定金额
                    productDetail.setDeposit(orderDepositConfig.getDepositAmount().multiply(productDetail.getQuantity()));
                } else {
                    // 比例
                    BigDecimal divide = orderDepositConfig.getDepositAmount().divide(new BigDecimal(100));
                    productDetail.setDeposit(productDetail.getNetWeight().multiply(goldPrice.getJj()).multiply(divide).setScale(3, RoundingMode.HALF_UP));
                    productDetail.getDeposit().add(priceResult.subtract(productDetail.getNetWeight().multiply(goldPrice.getJj())));
                }
            }

            // 判断门店ID一致，设置门店名称
            if (Objects.equals(productDetail.getStoreId(), orderVO.getStoreId())) {
                orderVO.setStoreName(productDetail.getStoreName());
                orderVO.setStoreLogo(productDetail.getStoreLogo());
            }
        }

        // 校验订单挂签价格
        if (ObjectUtil.isNotEmpty(orderVO.getCertificateFee()) && certificatePrice.setScale(3, RoundingMode.HALF_UP).compareTo(orderVO.getCertificateFee().setScale(3, RoundingMode.HALF_UP)) != 0) {
            log.info("订单挂签价格不正确, 入参挂签价格: {}, 计算挂签价格: {}", orderVO.getCertificateFee(), certificatePrice);
            throw new BusinessException("订单挂签价格不正确");
        }

        // Step 2-3: 校验接口参数中所有商品的单价
        List<AskPurchaseQuoteGoodsListResp> askPurchaseQuoteGoodsListRespList = CollUtil.toList();
        if (Objects.nonNull(orderVO.getAskPurchaseQuoteId())) {
            // 求购报价单下单, 金额交易通过报价单的金额进行校验
            askPurchaseQuoteGoodsListRespList = tradeFeignService.getAskPurchaseQuoteGoodsList(orderVO.getAskPurchaseQuoteId());
        }

        for (MobileOrderProductReq orderProduct : orderProducts) {
            OrderProductDetailDTO productDetail = new OrderProductDetailDTO();
            if (CommoditySaleModeEnum.ORDER.getCode().equals(orderProduct.getSaleMode())) {
                productDetail = productResult.stream().filter(p -> p.getSkuId().equals(orderProduct.getSkuId())).findFirst().orElse(null);
            } else {
                productDetail = productResult.stream().filter(p -> p.getSingleId().equals(orderProduct.getCommoditySingleId())).findFirst().orElse(null);
            }

            // 查询商品信息补充到入参
            orderProduct.setIsInlay(productDetail.getIsInlay());

            if (productDetail == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
            }

            if (CommoditySaleModeEnum.ORDER.getCode().equals(orderVO.getSaleMode())) {
                // 校验定金金额
                if (orderProduct.getDeposit().setScale(3, RoundingMode.HALF_UP).compareTo(productDetail.getDeposit().setScale(3, RoundingMode.HALF_UP)) != 0) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DEPOSIT_IS_NOT_CORRECT);
                }
            }

            // 校验挂签价格
            if (orderProduct.getNeedCertificate() && orderProduct.getVisaPrice() != null) {
                if (UnitVisaTypeEnum.GRAM.getCode().equals(productDetail.getUnitVisaType())) {
                    orderProduct.setVisaPrice(orderProduct.getVisaPrice().multiply(orderProduct.getNetWeight()));
                }
                if (orderProduct.getVisaPrice().setScale(2, RoundingMode.HALF_UP).compareTo(productDetail.getVisaPrice().setScale(2, RoundingMode.HALF_UP)) != 0) {
                    log.info("订单商品挂签价格不正确, 入参挂签价格: {}, 计算挂签价格: {}", orderProduct.getVisaPrice(), productDetail.getVisaPrice());
                    throw new BusinessException("订单商品挂签价格不正确");
                }
            }

            if (orderProduct.getRefPrice().setScale(2, RoundingMode.HALF_UP).compareTo(productDetail.getPrice().setScale(2, RoundingMode.HALF_UP)) != 0) {
                if (Objects.isNull(orderVO.getAskPurchaseQuoteId())) {
                    // 求购报价单下单, 金额交易通过报价单的金额进行校验
                    log.info("订单商品到手价格不正确, 入参到手价格: {}, 计算到手价格: {}", orderProduct.getRefPrice(), productDetail.getPrice());
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_PRICE_IS_NOT_CORRECT);
                }
                AskPurchaseQuoteGoodsListResp quoteGoodsListResp = askPurchaseQuoteGoodsListRespList.stream().filter(p -> p.getSkuId().equals(orderProduct.getSkuId())).findFirst().orElse(null);
                if (quoteGoodsListResp == null) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
                }

                BigDecimal quoteGoodsPrice = quoteGoodsListResp.getUnitPriceWithTax();
                if (orderProduct.getPrice().setScale(3, RoundingMode.HALF_UP).compareTo(quoteGoodsPrice) != 0) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_PRICE_IS_NOT_CORRECT);
                }

            }
        }

        // Step 2-4: 校验接口参数中所有商品的打包费，挂签/证书费，快递费
        if (ObjectUtil.isEmpty(orderDepositConfig)) {
            throw new BusinessException("打包费异常，需要配置！");
        }
        if (orderVO.getPackingFee().setScale(3, RoundingMode.HALF_UP).compareTo(orderDepositConfig.getPackingFee().setScale(3, RoundingMode.HALF_UP)) != 0) {
            throw new BusinessException("订单打包费不正确");
        }

        //****************************************************
        // Step 3: 从营销服务获得商品到手价、套餐、优惠活动等信息
        // 查询普通商品，套餐主商品，换购的商品、被换购的商品（查询普通商品、带营销活动的商品、套餐主商品、换购商品、被换购的商品，不查询套餐中的商品、赠品）
        //****************************************************
        List<PromotionProductDTO> marketingResult = marketingFeignService.findOrderPromotions(orderVO.getShopId(), buyerMemberId, buyerRoleId, buyerMemberLevelType, productResult);

        log.info("从营销服务获得商品到手价, 会员: {}, 角色: {}, 返回信息: {}", buyerMemberId, buyerRoleId, JSONUtil.toJsonStr(marketingResult));

        Map<Long, MobileOrderProductReq> orderProductReqSkuIdMap = orderProducts.stream().collect(Collectors.toMap(MobileOrderProductReq::getSkuId, Function.identity(), (v1, v2) -> v1));

        marketingResult.stream().map(PromotionProductDTO::getGifts).filter(CollUtil::isNotEmpty).flatMap(Collection::stream).forEach(
                // 前端请求商品列表中存在满足活动条件的赠品, 则标记为活动赠品
                gift -> Optional.ofNullable(orderProductReqSkuIdMap.get(gift.getSkuId())).ifPresent(
                        o -> o.setPromotionType(OrderPromotionTypeEnum.PART_GIVE_GIFT.getCode())
                )
        );

        // 可以使用优惠券的营销活动
        Map<Long, PromotionDTO> superCouponMap = marketingResult.stream().flatMap(v -> v.getPromotions().stream()).collect(Collectors.toList()).stream().filter(PromotionDTO::getSuperCoupon).collect(Collectors.toMap(PromotionDTO::getPromotionId, PromotionDTO -> PromotionDTO, (a, b) -> b));

        // 根据活动类型修改会员折扣
        OrderDM.modifyMemberDiscountByActivityType(orderProducts, marketingResult);

        //****************************************************
        // Step 4: 根据营销活动返回的结果，校验、修改
        //****************************************************
        for (MobileOrderProductReq orderProduct : orderProducts) {
            // 移除接口参数中与营销活动返回不匹配的营销活动
            List<PromotionDTO> promotions = marketingResult.stream().filter(p -> p.getSkuId().equals(orderProduct.getSkuId())).flatMap(p -> p.getPromotions().stream()).collect(Collectors.toList());
            orderProduct.getPromotions().removeIf(promotion -> promotions.stream().noneMatch(r -> r.getPromotionId().equals(promotion.getPromotionId())));
        }

        // Step 4-1: 校验营销活动优惠金额
        Map<Long, BigDecimal> discountMap = new HashMap<>();
        for (PromotionProductDTO promotionProduct : marketingResult) {
            discountMap.put(promotionProduct.getSkuId(), promotionProduct.getPromotionAmount());
        }
        BigDecimal promotionAmount = marketingResult.stream().map(PromotionProductDTO::getPromotionAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        if (promotionAmount.setScale(2, RoundingMode.HALF_UP).compareTo(orderVO.getPromotionAmount().setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PROMOTION_AMOUNT_MISMATCHED);
        }

        // Step 4-2: 套餐主商品、套餐中的商品、修改到手价
        Map<Long, Long> subProductPromotion = new HashMap<>();
        List<OrderProductDetailDTO> primaryProducts = productResult.stream().filter(product -> product.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode())).collect(Collectors.toList());
        for (OrderProductDetailDTO primaryProduct : primaryProducts) {
            PromotionProductDTO promotionProduct = marketingResult.stream().filter(p -> p.getSkuId().equals(primaryProduct.getSkuId())).findFirst().orElse(null);
            if (promotionProduct == null || !promotionProduct.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRIMARY_RODUCT_DOES_NOT_EXIST);
            }

            // 判断套餐中的商品是否都存在
            List<OrderProductDetailDTO> subProducts = productResult.stream().filter(p -> promotionProduct.getSubProducts().stream().anyMatch(sub -> sub.getSkuId().equals(p.getSkuId()))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subProducts) || subProducts.size() != promotionProduct.getSubProducts().size()) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUB_PRODUCT_DOES_NOT_EXIST);
            }

            // 营销活动ID
            Long promotionId = promotionProduct.getPromotions().stream().filter(v -> Objects.equals(v.getPromotionType(), ActivityTypeEnum.SET_MEAL.getCode())).map(PromotionDTO::getPromotionId).findFirst().orElse(0L);
            promotionProduct.getSubProducts().forEach(v -> subProductPromotion.put(v.getProductId(), promotionId));

            // 根据单价*数量，加权平均套餐到手价
            // 2022-07-07 修改白标站订单创建错误问题（套餐商品下单未将子商品到手价设置未0，导致将子商品价格计算到订单金额中）
            log.info(" 套餐主商品、套餐中的商品、修改到手价-subProducts:{}", JSONUtil.toJsonStr(subProducts));
            Map<Long, BigDecimal> baseMap = subProducts.stream().collect(Collectors.toMap(OrderProductDetailDTO::getSkuId, p -> p.getPrice().multiply(p.getQuantity()).setScale(3, RoundingMode.HALF_UP)));

            baseMap.put(primaryProduct.getSkuId(), primaryProduct.getPrice().multiply(primaryProduct.getQuantity()).setScale(3, RoundingMode.HALF_UP));

            Map<Long, BigDecimal> priceMap = OrderDM.weightedAmount(baseMap, promotionProduct.getRefPrice().multiply(promotionProduct.getQuantity()));

            // 重新设置套餐主商品、套餐商品的到手价
            subProducts.forEach(p -> p.setRefPrice(priceMap.getOrDefault(p.getSkuId(), BigDecimal.ZERO).divide(p.getQuantity(), 3, RoundingMode.HALF_UP)));
            primaryProduct.setRefPrice(priceMap.getOrDefault(primaryProduct.getSkuId(), BigDecimal.ZERO).divide(primaryProduct.getQuantity(), 3, RoundingMode.HALF_UP));
        }

        // Step 4-3: 除赠品、套餐主商品、套餐中商品外的其他商品，设置到手价
        List<OrderProductDetailDTO> otherProducts = productResult.stream().filter(product -> !product.getPriceType().equals(PriceTypeEnum.GIFT.getCode()) && !product.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode()) && !product.getPromotionType().equals(OrderPromotionTypeEnum.PART_OF_SET.getCode())).collect(Collectors.toList());
        for (OrderProductDetailDTO otherProduct : otherProducts) {
            PromotionProductDTO promotionProduct = marketingResult.stream().filter(p -> p.getSkuId().equals(otherProduct.getSkuId())).findFirst().orElse(null);
            if (promotionProduct == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PROMOTION_PRODUCT_DOES_NOT_EXIST);
            }
            if (promotionProduct.getRefPrice().compareTo(BigDecimal.ZERO) > 0) {
                otherProduct.setRefPrice(promotionProduct.getRefPrice());
            }
        }

        // Step 4-4: 判断赠品是否存在，设置到手价为0
        List<OrderProductDetailDTO> giftProducts = productResult.stream().filter(product -> product.getPriceType().equals(PriceTypeEnum.GIFT.getCode())).collect(Collectors.toList());
        for (OrderProductDetailDTO giftProduct : giftProducts) {
            if (marketingResult.stream().flatMap(promotionProduct -> promotionProduct.getGifts().stream()).noneMatch(gift -> gift.getSkuId().equals(giftProduct.getSkuId()))) {
                throw new BusinessException(ResponseCodeEnum.ORDER_GIFT_PRODUCT_DOES_NOT_EXIST);
            }

            giftProduct.setRefPrice(BigDecimal.ZERO);
        }

        //****************************************************
        // Step 5: 校验接口商品的到手价
        //****************************************************
        for (MobileOrderProductReq orderProduct : orderProducts) {
            OrderProductDetailDTO productDetail = productResult.stream().filter(p -> CommoditySaleModeEnum.SPOT.getCode().equals(orderVO.getSaleMode()) ? p.getSingleId().equals(orderProduct.getCommoditySingleId()) : p.getSkuId().equals(orderProduct.getSkuId())).findFirst().orElse(null);
            if (productDetail == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
            }

            //由于赠品、套餐主商品、套餐中的商品已经重新设置过到手价，这里不校验，并重新设置（在后续会有订单总金额的校验）
            if (productDetail.getPriceType().equals(PriceTypeEnum.GIFT.getCode())) {
                //orderProduct.setRefPrice(productDetail.getRefPrice());
                continue;
            }

            if (productDetail.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode()) || productDetail.getPromotionType().equals(OrderPromotionTypeEnum.PART_OF_SET.getCode())) {
                orderProduct.setRefPrice(productDetail.getRefPrice());
                continue;
            }

            if (CollUtil.isEmpty(askPurchaseQuoteGoodsListRespList)) {
                // 非寻源下单
                if (orderProduct.getRefPrice().setScale(2, RoundingMode.HALF_UP).compareTo(productDetail.getRefPrice().setScale(2, RoundingMode.HALF_UP)) != 0) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_MISMATCHED, orderProduct.getName().concat(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_MISMATCHED.getMessage()));
                }
            }

            if (CollUtil.isNotEmpty(askPurchaseQuoteGoodsListRespList)) {
                // 寻源下单
                AskPurchaseQuoteGoodsListResp quoteGoodsListResp = askPurchaseQuoteGoodsListRespList.stream().filter(p -> p.getSkuId().equals(orderProduct.getSkuId())).findFirst().orElse(null);
                if (quoteGoodsListResp == null) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
                }

                BigDecimal quoteGoodsPrice = quoteGoodsListResp.getUnitPriceWithTax();
                if (orderProduct.getRefPrice().setScale(3, RoundingMode.HALF_UP).compareTo(quoteGoodsPrice) != 0) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_MISMATCHED, orderProduct.getName().concat(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_MISMATCHED.getMessage()));
                }
            }

        }

        //由于在计算优惠券的时候，会改变订单商品的到手价，所以这里先记录一下订单所有商品的金额，用于在后续校验订单实付金额
        BigDecimal productAmount = orderProducts.stream().map(MobileOrderProductReq::getRefPrice).reduce(BigDecimal.ZERO, BigDecimal::add);

        //****************************************************
        //Step 6: 用所有营销活动的到手价，统计商品总额，查询优惠券
        //****************************************************
        BigDecimal promotionRefAmount = productResult.stream().map(detail -> detail.getRefPrice().multiply(detail.getQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<OrderCouponDetailDTO> couponResult = marketingFeignService.findOrderCoupons(orderVO.getShopId(), buyerMemberId, buyerRoleId, orderVO.getCoupons(), productResult, promotionRefAmount);

        log.info("从营销服务查询优惠券, 会员: {}, 角色: {}, 返回信息: {}", buyerMemberId, buyerRoleId, JSONUtil.toJsonStr(couponResult));

        //Step 6-1: 校验所有优惠券，根据优惠券重新调整商品到手价
        //定义一个Map，记录每个商品SkuId使用优惠券后减少的金额，返回用于在创建订单（拆单）时重新统计订单的优惠抵扣总额
        Map<Long, BigDecimal> couponMap = new HashMap<>();
        //再定义一个Map，记录每个商品SkuId使用“平台优惠券”后减少的金额，用于售后服务退款时返还平台优惠券
        Map<Long, BigDecimal> platformCouponMap = new HashMap<>();
        for (OrderCouponReq coupon : orderVO.getCoupons()) {
            OrderCouponDetailDTO couponDetail = couponResult.stream().filter(c -> c.getCouponId().equals(coupon.getCouponId())).findFirst().orElse(null);
            if (couponDetail == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_DOES_NOT_EXIST);
            }

            // 校验适用商品的SkuId
            if (NumberUtil.notNullOrZero(coupon.getSkuId()) && !couponDetail.getSkuIds().contains(coupon.getSkuId())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_SKU_ID_MISMATCHED);
            }

            // 校验卡券面额
            if (coupon.getAmount().setScale(3, RoundingMode.HALF_UP).compareTo(couponDetail.getAmount()) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_PRICE_MISMATCHED);
            }

            // 校验卡券归属和类型
            if (!coupon.getBelongType().equals(couponDetail.getBelongType()) || !coupon.getCouponType().equals(couponDetail.getCouponType())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_TYPE_MISMATCHED);
            }

            // 校验卡券是否可用
            if (!couponDetail.getAvailable()) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_NOT_AVAILABLE);
            }

            // 校验卡券是否过期
            if (!(!LocalDateTime.now().isBefore(couponDetail.getStartTime()) && !LocalDateTime.now().isAfter(couponDetail.getExpireTime()))) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_EXPIRED);
            }

            //根据优惠券重新调整商品到手价（直接更新接口参数中的到手价）
            Map<Long, BigDecimal> couponAmountMap = modifyRefPrice(coupon, orderProducts, superCouponMap, subProductPromotion);
            couponAmountMap.forEach((skuId, amount) -> couponMap.merge(skuId, amount, BigDecimal::add));
            //如果优惠券是“平台优惠券”，再单独记录使用“平台优惠券”的抵扣金额
            if (coupon.getBelongType().equals(BelongTypeEnum.PLATFORM.getCode())) {
                couponAmountMap.forEach((skuId, amount) -> platformCouponMap.merge(skuId, amount, BigDecimal::add));
            }
        }

        //校验计算后的优惠券减少金额
        BigDecimal couponAmount = couponMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        if (couponAmount.setScale(2, RoundingMode.HALF_UP).compareTo(orderVO.getCouponAmount().setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_AMOUNT_MISMATCHED);
        }

        //****************************************************
        //Step 7: 校验运费
        //****************************************************
        //如果有满额包邮商品，则过滤满额包邮的商品，用于运费计算。默认为所有商品
        List<MobileOrderProductReq> freightProductList = orderProducts.stream().filter(
                p -> p.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode())
                        && p.getFreightType().equals(OrderFreightTypeEnum.BUYER.getCode())
        ).collect(Collectors.toList());
        BigDecimal freightAmount = BigDecimal.ZERO;
        if (orderVO.getConsignee() != null && freightProductList != null  && freightProductList.stream().anyMatch(s -> ObjectUtil.isNotEmpty(s.getLogisticsTemplateId()))) {
            OrderProductFreeFreightReq orderProductFreeFreightReq = OrderProductFreeFreightReq.buildBy(orderVO, freightProductList);
            freightAmount = logisticsFeignService.findOrderFreeFreight(loginUser, orderProductFreeFreightReq);
        }

        if (SfPaymentTypeEnum.RECEIVER_PAY.getCode().equals(orderVO.getSfPaymentType())) {
            freightAmount = BigDecimal.ZERO;
        }

        if (Stream.of(OrderDeliveryTypeEnum.EXPRESS_DELIVERY.getCode(), OrderDeliveryTypeEnum.EXPRESS_AGENCY.getCode()).collect(Collectors.toList()).stream().anyMatch(deliver -> deliver.equals(orderVO.getDeliveryType()))) {
            if (orderVO.getFreight().setScale(2, RoundingMode.HALF_UP).compareTo(freightAmount) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_FREIGHT_AMOUNT_MISMATCHED);
            }
        }

        //****************************************************
        //Step 8: 如果商品的配送方式为“自提”，从物流服务查询自提地址信息(百泰收货，改为订单维度，所以废弃掉这段代码)
        //****************************************************‘
//        List<Long> addressIds = orderVO.getProducts().stream().filter(p -> p.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())).map(MobileOrderProductReq::getAddressId).distinct().collect(Collectors.toList());
//        List<ProductAddressDTO> addressResult = logisticsFeignService.findProductAddresses(addressIds);
//
//        //如果是自提，设置收货地址为自提地址用于待核销自提订单列表展示
//        addressResult.stream().findFirst().ifPresent(address -> {
//            OrderConsigneeReq consignee = new OrderConsigneeReq();
//            consignee.setConsigneeId(address.getAddressId());
//            consignee.setConsignee(address.getReceiver());
//            consignee.setProvinceCode(address.getProvinceCode());
//            consignee.setCityCode(address.getCityCode());
//            consignee.setDistrictCode(address.getDistrictCode());
//            consignee.setStreetCode(StrUtil.isEmpty(address.getStreetCode()) ? "" : address.getStreetCode());
//            consignee.setAddress(address.getAddress());
//            consignee.setCountryCode(address.getCountryCode());
//            consignee.setPhone(address.getPhone());
//            consignee.setDefaultConsignee(false);
//            orderVO.setConsignee(consignee);
//        });

//        orderVO.getProducts().forEach(product -> {
//            if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())) {
//                addressResult.stream().filter(r -> r.getAddressId().equals(product.getAddressId())).findFirst().ifPresent(address -> {
//                    product.setAddress(address.getAddress());
//                    product.setReceiver(address.getReceiver());
//                    product.setPhone(address.getPhone());
//                });
//            }
//        });

        //****************************************************
        //Step 9: 计算积分抵扣
        //****************************************************
        //1. 根据供应商查询采购商抵扣金额
        //2. 抵扣金额根据供应商按比例再次降低商品到手价
        //3. 修改订单总金额字段
        List<OrderDeductionReq> deductions = orderVO.getDeductions();
        //计算积分抵扣总金额
        BigDecimal deductionAmount = BigDecimal.ZERO;
        Map<Long, BigDecimal> deductionMap = new HashMap<>();
        if (CollUtil.isNotEmpty(deductions)) {

            deductionAmount = orderVO.getDeductions().stream().map(OrderDeductionReq::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            for (OrderDeductionReq detailVO : deductions) {
                //根据单价*数量，计算平均到手价
                Map<Long, BigDecimal> weightedMap;
                if (MemberRelationTypeEnum.PLATFORM.getCode().equals(detailVO.getRelType())) {//平台积分
                    weightedMap = orderProducts.stream().collect(Collectors.toMap(MobileOrderProductReq::getSkuId, p -> p.getRefPrice().multiply(p.getQuantity())));
                } else {//店铺积分
                    weightedMap = orderProducts.stream().filter(p -> p.getVendorMemberId().equals(detailVO.getVendorMemberId())).collect(Collectors.toMap(MobileOrderProductReq::getSkuId, p -> p.getRefPrice().multiply(p.getQuantity())));
                }
                Map<Long, BigDecimal> deductionAmountMap = OrderDM.weightedAmount(weightedMap, detailVO.getAmount());
                deductionAmountMap.forEach((skuId, amount) -> deductionMap.merge(skuId, amount, BigDecimal::add));
                orderProducts.forEach(p -> {
                    BigDecimal refPrice = NumberUtil.max(p.getRefPrice().subtract(deductionAmountMap.getOrDefault(p.getSkuId(), BigDecimal.ZERO).divide(p.getQuantity(), 3, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP), BigDecimal.ZERO);
                    p.setRefPrice(refPrice);
                });
            }
        }

        //****************************************************
        //Step 10: 计算跨境电商进口商品税费
        //****************************************************
        //跨境商品，含税
        Map<Long, BigDecimal> taxesMap = productResult.stream().filter(p -> p.getIsCrossBorder() != null && p.getIsCrossBorder() && p.getTax()).collect(Collectors.toMap(OrderProductDetailDTO::getSkuId, p -> p.getRefPrice().multiply(p.getTaxRate()).multiply(p.getQuantity()), BigDecimal::add));
        orderProducts.forEach(p -> {
            BigDecimal refPrice = NumberUtil.max(p.getRefPrice().add(taxesMap.getOrDefault(p.getSkuId(), BigDecimal.ZERO).divide(p.getQuantity(), 3, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP), BigDecimal.ZERO);
            p.setRefPrice(refPrice);
        });

        //校验订单实付金额（前端计算方式：实付金额 = (Σ到手价*数量 + 运费 - 优惠券金额 - 积分抵扣金额) * 第一次的支付比例）
        BigDecimal totalAmount;
        BigDecimal amount = orderDepositConfig.getPackingFee().add(orderVO.getSfInsuranceFee()).add(orderVO.getPacInsuranceFee());
        //如果是订货订单，校验定金金额
        if (CommoditySaleModeEnum.ORDER.getCode().equals(orderVO.getSaleMode())) {
            //校验定金金额
            BigDecimal depositAmount = orderProducts.stream().map(MobileOrderProductReq::getDeposit).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (depositAmount.setScale(3, RoundingMode.HALF_UP).compareTo(orderVO.getDepositAmount().setScale(3, RoundingMode.HALF_UP)) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_DEPOSIT_AMOUNT_MISMATCHED);
            }
        } else {
            if (processPayment) {
                //totalAmount = (productAmount.add(freightAmount).add(orderVO.getTaxes()).subtract(couponAmount).subtract(deductionAmount).add(amount)).multiply(firstPayRate).setScale(2, RoundingMode.HALF_UP);
                totalAmount = (productAmount.add(freightAmount).add(orderVO.getTaxes()).subtract(couponAmount).subtract(deductionAmount).add(amount)).add(certificatePrice).setScale(2, RoundingMode.HALF_UP);
            } else {
                //如果不需要支付，实付金额 = (Σ到手价*数量 + 运费 - 优惠券金额 - 积分抵扣金额)
                totalAmount = (productAmount.add(freightAmount).add(orderVO.getTaxes()).subtract(couponAmount).subtract(deductionAmount).add(amount)).add(certificatePrice).setScale(2, RoundingMode.HALF_UP);
            }

            if (orderVO.getTotalAmount().setScale(2, RoundingMode.HALF_UP).compareTo(totalAmount) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_TOTAL_AMOUNT_MISMATCHED);
            }
        }

        //赠送优惠券集合
        List<OrderGiveCouponDTO> orderGiveCouponDTOList = marketingResult.stream().flatMap(p -> p.getGiveCouponDTOList().stream()).collect(Collectors.toList());

        // 结束返回
        BuyerOrderCheckBO buyerOrderCheckBO = new BuyerOrderCheckBO(couponMap, platformCouponMap, deductionMap, taxesMap, freightProductList, orderGiveCouponDTOList);
        buyerOrderCheckBO.setDiscountMap(discountMap);
        return buyerOrderCheckBO;
    }

    /**
     * (商品、会员、物流、营销服务）查询商品详情、折扣、物流、促销活动等信息
     *
     * @param buyerMemberId        采购会员Id
     * @param buyerRoleId          采购会员角色Id
     * @param buyerMemberLevelType 采购会员（当前登录用户）等级类型
     * @param orderVO              订单接口参数
     * @param vendorCount          供应商数量
     * @param processPayment       交易流程是否需要支付
     * @param firstPayRate         交易流程中第一批次中最小支付次数的支付比例
     * @return 校验、修改接口参数中的商品到手价格
     */
    @Override
    public BuyerOrderCheckBO checkAgentPurchaseOrderProduct(Long buyerMemberId, Long buyerRoleId, Integer buyerMemberLevelType, AgentPurchaseOrderReq orderVO, int vendorCount, boolean processPayment, BigDecimal firstPayRate) {
        //****************************************************
        // Step 1： 接口参数、规则判断
        //****************************************************
        List<MobileOrderProductReq> orderProducts = orderVO.getProducts();
        // Step 1-1: 商品SkuId不能重复
        if (orderProducts.size() != orderProducts.stream().map(MobileOrderProductReq::getSkuId).distinct().count()) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_SKU_ID_DUPLICATED);
        }

        // Step 1-2 : 多供应商时，所有商品的运费方式必须为“无运费”或“卖家承担”
        if (vendorCount > 1 && orderProducts.stream().map(product -> NumberUtil.isNullOrNegative(product.getFreightType()) ? OrderFreightTypeEnum.VENDOR.getCode() : product.getFreightType()).anyMatch(freightType -> !freightType.equals(OrderFreightTypeEnum.VENDOR.getCode()))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_FREIGHT_TYPE_MUST_BE_VENDOR);
        }

        // Step 1-3 : 到手价格：“普通商品”的到手价应该小于等于单价
        if (orderProducts.stream().filter(product -> NumberUtil.isNullOrZero(product.getPromotionType())).anyMatch(product -> product.getRefPrice().compareTo(product.getPrice()) > 0)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_SHOULD_LTE_PRICE);
        }

        // Step 1-4 : 订单商品需要物流配送时，收货人信息不能为空
        if (orderProducts.stream().anyMatch(product -> product.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode())) && orderVO.getConsignee() == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CONSIGNEE_MUST_NOT_EMPTY);
        }

        // Step 1-4 : 检查订单接口参数：商品列表
        for (MobileOrderProductReq product : orderProducts) {
            //套餐主商品的套餐编号要大于0
            if (product.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode()) && NumberUtil.isNullOrNegativeZero(product.getGroupNo())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_PRIMARY_OF_SET_GROUP_NO_SHOULD_GT_ZERO);
            }

            //如果是赠品，到手价要为0
            if (product.getPriceType().equals(PriceTypeEnum.GIFT.getCode()) && product.getRefPrice().compareTo(BigDecimal.ZERO) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_GIFT_REF_PRICE_SHOULD_BE_ZERO);
            }

            //被换购商品的parentSkuId要大于0，并且存在于商品列表中
            if (product.getPromotionType().equals(OrderPromotionTypeEnum.EXCHANGED.getCode())) {
                if (NumberUtil.isNullOrNegativeZero(product.getParentSkuId())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_EXCHANGED_PRODUCT_PARENT_SKU_ID_SHOULD_GT_ZERO);
                }

                if (orderVO.getProducts().stream().noneMatch(p -> p.getSkuId().equals(product.getParentSkuId()))) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_TO_EXCHANGED_PRODUCT_SKU_ID_SHOULD_INCLUDE_IN_PRODUCTS);
                }
            }

            //如果配送方式为“自提”，自提地址、联系人、电话不能为空
            if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode()) && NumberUtil.isNullOrZero(product.getAddressId())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_ADDRESS_ID_IS_MISSING);
            }

            //如果配送方式为“物流”，配送方式不能为空
            if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode())) {
                if (NumberUtil.isNullOrZero(product.getFreightType())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_FREIGHT_TYPE_CAN_NOT_BE_NULL);
                }

                //如果运费类型为“买家承担”，物流模板Id、商品重量不能为空或0
                if (product.getFreightType().equals(OrderFreightTypeEnum.BUYER.getCode())) {
                    if (NumberUtil.isNullOrNegativeZero(product.getLogisticsTemplateId())) {
                        throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_LOGISTICS_TEMPLATE_ID_MUST_GREATER_THAN_ZERO);
                    }

                    if (NumberUtil.isNullOrNegativeZero(product.getWeight())) {
                        throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_WEIGHT_MUST_GREATER_THAN_ZERO);
                    }
                }
            }

            // 为方便后续校验，如果前端传Null值，将配送方式、自提地址Id，运费类型、营销活动列表设置为非Null值
            product.setDeliveryType(NumberUtil.isNullOrNegativeZero(product.getDeliveryType()) ? 0 : product.getDeliveryType());
            product.setAddressId(NumberUtil.isNullOrZero(product.getAddressId()) ? 0 : product.getAddressId());
            product.setFreightType(NumberUtil.isNullOrNegativeZero(product.getFreightType()) ? 0 : product.getFreightType());
            product.setPromotions(CollectionUtils.isEmpty(product.getPromotions()) ? new ArrayList<>() : product.getPromotions());
        }

        // 为方便后续校验，如果前端传Null值，将优惠券列表、积分抵扣列表、配送时间和备注设置成空列表
        orderVO.setCoupons(CollectionUtils.isEmpty(orderVO.getCoupons()) ? new ArrayList<>() : orderVO.getCoupons());
        orderVO.setDeductions(CollectionUtils.isEmpty(orderVO.getDeductions()) ? new ArrayList<>() : orderVO.getDeductions());
        orderVO.setDeliverTimes(CollectionUtils.isEmpty(orderVO.getDeliverTimes()) ? new ArrayList<>() : orderVO.getDeliverTimes());

        // Step 1-5 : 检查订单接口参数：优惠券列表
        for (OrderCouponReq coupon : orderVO.getCoupons()) {
            // 校验商家优惠券归属的会员
            if (coupon.getBelongType().equals(BelongTypeEnum.MERCHANT.getCode())) {
                if (NumberUtil.isNullOrNegativeZero(coupon.getVendorMemberId()) || NumberUtil.isNullOrNegativeZero(coupon.getVendorRoleId())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_VENDOR_ID_CAN_NOT_BE_NULL_OR_ZERO);
                }

                if (orderProducts.stream().noneMatch(p -> p.getVendorMemberId().equals(coupon.getVendorMemberId()) && p.getVendorRoleId().equals(coupon.getVendorRoleId()))) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_MERCHANT_COUPON_VENDOR_MUST_INCLUDE_IN_THE_PRODUCTS);
                }
            }

            //如果是商品优惠券，商品SkuId要大于0，且必须在商品列表中
            if (isProductCoupon(coupon.getBelongType(), coupon.getCouponType())) {
                if (NumberUtil.isNullOrNegativeZero(coupon.getSkuId())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_SKU_ID_SHOULD_GT_ZERO);
                }

                MobileOrderProductReq productVO = orderProducts.stream().filter(p -> p.getSkuId().equals(coupon.getSkuId())).findFirst().orElse(null);
                if (productVO == null) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_SKU_ID_MUST_INCLUDE_IN_PRODUCTS);
                }

                //如果是0元抵扣券，购买商品的数量必须为1
                if ((coupon.getBelongType().equals(BelongTypeEnum.PLATFORM.getCode()) && coupon.getCouponType().equals(PlatformCouponTypeEnum.ZERO_DISCOUNT.getCode())) || (coupon.getBelongType().equals(BelongTypeEnum.MERCHANT.getCode()) && coupon.getCouponType().equals(MerchantCouponTypeEnum.ZERO_DISCOUNT.getCode()))) {
                    if (productVO.getQuantity().compareTo(BigDecimal.ONE) != 0) {
                        throw new BusinessException(ResponseCodeEnum.ORDER_ZERO_COUPON_SKU_QUANTITY_MUST_EQUAL_TO_ONE);
                    }
                }

                //优惠券不能用于赠品
                if (productVO.getPriceType().equals(PriceTypeEnum.GIFT.getCode())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_CAN_NOT_USE_TO_GIFT_PRODUCT);
                }
            }
        }

        //Step 1-6: 最多只能使用一个平台优惠券
        if (orderVO.getCoupons().stream().filter(coupon -> coupon.getBelongType().equals(BelongTypeEnum.PLATFORM.getCode())).count() > 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PLATFORM_COUPON_CAN_NOT_EXCEED_ONE);
        }

        //****************************************************
        // Step 2: 从商品服务、会员服务查询商品详细信息，
        // 必须保证接口参数的商品与List<OrderProductDetailDTO>中的商品是一对一的关系（以SkuId判断）
        //****************************************************
        // Step 2-1: 在调用商品服务的接口中，校验下单接口传递的商品是否全包含在商品服务的查询结果中
        List<ProductSkuDTO> productStocks = orderProducts.stream().map(product -> new ProductSkuDTO(buyerMemberId, buyerRoleId, product.getVendorMemberId(), product.getVendorRoleId(), product.getSkuId(), product.getQuantity(), product.getPromotionType(), product.getGroupNo(), product.getParentSkuId())).collect(Collectors.toList());
        List<OrderProductDetailDTO> productResult = productFeignService.findProductDetails(orderVO.getShopId(), buyerMemberId, buyerRoleId, buyerMemberLevelType, productStocks, orderVO.getSaleMode(), null);

        // Step 2-2: 校验是否有商品已经下架
        if (productResult.stream().anyMatch(productDetail -> !productDetail.getPublished())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_IS_OFF_LINE);
        }

        // Step 2-3: 重新构建商品单价
        for (OrderProductDetailDTO productDetail : productResult) {
            BigDecimal priceResult = findProductPrice(productDetail.getPriceMap(), productDetail.getQuantity());
            productDetail.setPrice(priceResult);

            // 获取判断门店ID一致设置门店名称
            if (Objects.equals(productDetail.getStockId(), orderVO.getStoreId())) {
                orderVO.setStoreName(productDetail.getStoreName());
                orderVO.setStoreLogo(productDetail.getStoreLogo());
            }
        }

        // Step 2-3: 校验接口参数中所有商品的单价
        for (MobileOrderProductReq orderProduct : orderProducts) {
            OrderProductDetailDTO productDetail = productResult.stream().filter(p -> p.getSkuId().equals(orderProduct.getSkuId())).findFirst().orElse(null);
            if (productDetail == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
            }

            if (orderProduct.getPrice().setScale(3, RoundingMode.HALF_UP).compareTo(productDetail.getPrice()) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_PRICE_IS_NOT_CORRECT);
            }
        }

        //****************************************************
        // Step 3: 从营销服务获得商品到手价、套餐、优惠活动等信息
        // 查询普通商品，套餐主商品，换购的商品、被换购的商品（查询普通商品、带营销活动的商品、套餐主商品、换购商品、被换购的商品，不查询套餐中的商品、赠品）
        //****************************************************
        List<PromotionProductDTO> marketingResult = marketingFeignService.findOrderPromotions(orderVO.getShopId(), buyerMemberId, buyerRoleId, buyerMemberLevelType, productResult);

        // 可以使用优惠券的营销活动
        Map<Long, PromotionDTO> superCouponMap = marketingResult.stream().flatMap(v -> v.getPromotions().stream()).collect(Collectors.toList()).stream().filter(PromotionDTO::getSuperCoupon).collect(Collectors.toMap(PromotionDTO::getPromotionId, PromotionDTO -> PromotionDTO));

        //****************************************************
        // Step 4: 根据营销活动返回的结果，校验、修改
        //****************************************************
        for (MobileOrderProductReq orderProduct : orderProducts) {
            // 移除接口参数中与营销活动返回不匹配的营销活动
            List<PromotionDTO> promotions = marketingResult.stream().filter(p -> p.getSkuId().equals(orderProduct.getSkuId())).flatMap(p -> p.getPromotions().stream()).collect(Collectors.toList());
            orderProduct.getPromotions().removeIf(promotion -> promotions.stream().noneMatch(r -> r.getPromotionId().equals(promotion.getPromotionId())));
        }

//        for (MobileOrderProductVO orderProduct : orderProducts) {
//            // 校验接口参数中商品关联的营销活动是否存在
//            if(!CollectionUtils.isEmpty(orderProduct.getPromotions())) {
//                List<PromotionDTO> promotions = promotionProducts.stream().filter(p -> p.getSkuId().equals(orderProduct.getSkuId())).flatMap(p -> p.getPromotions().stream()).collect(Collectors.toList());
//                if(orderProduct.getPromotions().stream().anyMatch(p -> promotions.stream().noneMatch(r -> r.getPromotionId().equals(p.getPromotionId())))) {
//                    return Wrapper.fail(ResponseCode.ORDER_PROMOTION_DOES_NOT_EXIST);
//                }
//            }
//        }

        // Step 4-1: 校验营销活动优惠金额
        BigDecimal promotionAmount = marketingResult.stream().map(PromotionProductDTO::getPromotionAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        if (promotionAmount.setScale(2, RoundingMode.HALF_UP).compareTo(orderVO.getPromotionAmount().setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PROMOTION_AMOUNT_MISMATCHED);
        }

        // Step 4-2: 套餐主商品、套餐中的商品、修改到手价
        Map<Long, Long> subProductPromotion = new HashMap<>();
        List<OrderProductDetailDTO> primaryProducts = productResult.stream().filter(product -> product.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode())).collect(Collectors.toList());
        for (OrderProductDetailDTO primaryProduct : primaryProducts) {
            PromotionProductDTO promotionProduct = marketingResult.stream().filter(p -> p.getSkuId().equals(primaryProduct.getSkuId())).findFirst().orElse(null);
            if (promotionProduct == null || !promotionProduct.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRIMARY_RODUCT_DOES_NOT_EXIST);
            }

            // 判断套餐中的商品是否都存在
            List<OrderProductDetailDTO> subProducts = productResult.stream().filter(p -> promotionProduct.getSubProducts().stream().anyMatch(sub -> sub.getSkuId().equals(p.getSkuId()))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(subProducts) || subProducts.size() != promotionProduct.getSubProducts().size()) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUB_PRODUCT_DOES_NOT_EXIST);
            }

            // 营销活动ID
            Long promotionId = promotionProduct.getPromotions().stream().filter(v -> Objects.equals(v.getPromotionType(), ActivityTypeEnum.SET_MEAL.getCode())).map(PromotionDTO::getPromotionId).findFirst().orElse(0L);
            promotionProduct.getSubProducts().forEach(v -> subProductPromotion.put(v.getProductId(), promotionId));

            // 根据单价*数量，加权平均套餐到手价
            Map<Long, BigDecimal> baseMap = subProducts.stream().collect(Collectors.toMap(OrderProductDetailDTO::getSkuId, p -> p.getPrice().multiply(p.getQuantity()).setScale(3, RoundingMode.HALF_UP)));
            baseMap.put(primaryProduct.getSkuId(), primaryProduct.getPrice().multiply(primaryProduct.getQuantity()).setScale(3, RoundingMode.HALF_UP));

            Map<Long, BigDecimal> priceMap = OrderDM.weightedAmount(baseMap, promotionProduct.getRefPrice().multiply(promotionProduct.getQuantity()));

            // 重新设置套餐主商品、套餐商品的到手价
            subProducts.forEach(p -> p.setRefPrice(priceMap.getOrDefault(p.getSkuId(), BigDecimal.ZERO).divide(p.getQuantity(), 3, RoundingMode.HALF_UP)));
            primaryProduct.setRefPrice(priceMap.getOrDefault(primaryProduct.getSkuId(), BigDecimal.ZERO).divide(primaryProduct.getQuantity(), 3, RoundingMode.HALF_UP));
        }

        // Step 4-3: 除赠品、套餐主商品、套餐中商品外的其他商品，设置到手价
        List<OrderProductDetailDTO> otherProducts = productResult.stream().filter(product -> !product.getPriceType().equals(PriceTypeEnum.GIFT.getCode()) && !product.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode()) && !product.getPromotionType().equals(OrderPromotionTypeEnum.PART_OF_SET.getCode())).collect(Collectors.toList());
        for (OrderProductDetailDTO otherProduct : otherProducts) {
            PromotionProductDTO promotionProduct = marketingResult.stream().filter(p -> p.getSkuId().equals(otherProduct.getSkuId())).findFirst().orElse(null);
            if (promotionProduct == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PROMOTION_PRODUCT_DOES_NOT_EXIST);
            }

            otherProduct.setRefPrice(promotionProduct.getRefPrice());
        }

        // Step 4-4: 判断赠品是否存在，设置到手价为0
        List<OrderProductDetailDTO> giftProducts = productResult.stream().filter(product -> product.getPriceType().equals(PriceTypeEnum.GIFT.getCode())).collect(Collectors.toList());
        for (OrderProductDetailDTO giftProduct : giftProducts) {
            if (marketingResult.stream().flatMap(promotionProduct -> promotionProduct.getGifts().stream()).noneMatch(gift -> gift.getSkuId().equals(giftProduct.getSkuId()))) {
                throw new BusinessException(ResponseCodeEnum.ORDER_GIFT_PRODUCT_DOES_NOT_EXIST);
            }

            giftProduct.setRefPrice(BigDecimal.ZERO);
        }

        //****************************************************
        // Step 5: 校验接口商品的到手价
        //****************************************************
        for (MobileOrderProductReq orderProduct : orderProducts) {
            OrderProductDetailDTO productDetail = productResult.stream().filter(p -> p.getSkuId().equals(orderProduct.getSkuId())).findFirst().orElse(null);
            if (productDetail == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
            }

            //由于赠品、套餐主商品、套餐中的商品已经重新设置过到手价，这里不校验，并重新设置（在后续会有订单总金额的校验）
            if (productDetail.getPriceType().equals(PriceTypeEnum.GIFT.getCode())) {
                //orderProduct.setRefPrice(productDetail.getRefPrice());
                continue;
            }

            if (productDetail.getPromotionType().equals(OrderPromotionTypeEnum.PRIMARY_OF_SET.getCode()) || productDetail.getPromotionType().equals(OrderPromotionTypeEnum.PART_OF_SET.getCode())) {
                orderProduct.setRefPrice(productDetail.getRefPrice());
                continue;
            }

            if (orderProduct.getRefPrice().setScale(3, RoundingMode.HALF_UP).compareTo(productDetail.getRefPrice()) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_MISMATCHED, orderProduct.getName().concat(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_MISMATCHED.getMessage()));
            }
        }

        //由于在计算优惠券的时候，会改变订单商品的到手价，所以这里先记录一下订单所有商品的金额，用于在后续校验订单实付金额
        BigDecimal productAmount = orderProducts.stream().map(p -> p.getRefPrice().multiply(p.getQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);

        //****************************************************
        //Step 6: 用所有营销活动的到手价，统计商品总额，查询优惠券
        //****************************************************
        BigDecimal promotionRefAmount = productResult.stream().map(detail -> detail.getRefPrice().multiply(detail.getQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
        List<OrderCouponDetailDTO> couponResult = marketingFeignService.findOrderCoupons(orderVO.getShopId(), buyerMemberId, buyerRoleId, orderVO.getCoupons(), productResult, promotionRefAmount);

        //Step 6-1: 校验所有优惠券，根据优惠券重新调整商品到手价
        //定义一个Map，记录每个商品SkuId使用优惠券后减少的金额，返回用于在创建订单（拆单）时重新统计订单的优惠抵扣总额
        Map<Long, BigDecimal> couponMap = new HashMap<>();
        //再定义一个Map，记录每个商品SkuId使用“平台优惠券”后减少的金额，用于售后服务退款时返还平台优惠券
        Map<Long, BigDecimal> platformCouponMap = new HashMap<>();
        for (OrderCouponReq coupon : orderVO.getCoupons()) {
            OrderCouponDetailDTO couponDetail = couponResult.stream().filter(c -> c.getCouponId().equals(coupon.getCouponId())).findFirst().orElse(null);
            if (couponDetail == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_DOES_NOT_EXIST);
            }

            // 校验适用商品的SkuId
            if (NumberUtil.notNullOrZero(coupon.getSkuId()) && !couponDetail.getSkuIds().contains(coupon.getSkuId())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_SKU_ID_MISMATCHED);
            }

            // 校验卡券面额
            if (coupon.getAmount().setScale(3, RoundingMode.HALF_UP).compareTo(couponDetail.getAmount()) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_PRICE_MISMATCHED);
            }

            // 校验卡券归属和类型
            if (!coupon.getBelongType().equals(couponDetail.getBelongType()) || !coupon.getCouponType().equals(couponDetail.getCouponType())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_TYPE_MISMATCHED);
            }

            // 校验卡券是否可用
            if (!couponDetail.getAvailable()) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_NOT_AVAILABLE);
            }

            // 校验卡券是否过期
            if (!(!LocalDateTime.now().isBefore(couponDetail.getStartTime()) && !LocalDateTime.now().isAfter(couponDetail.getExpireTime()))) {
                throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_EXPIRED);
            }

            //根据优惠券重新调整商品到手价（直接更新接口参数中的到手价）
            Map<Long, BigDecimal> couponAmountMap = modifyRefPrice(coupon, orderProducts, superCouponMap, subProductPromotion);
            couponAmountMap.forEach((skuId, amount) -> couponMap.merge(skuId, amount, BigDecimal::add));
            //如果优惠券是“平台优惠券”，再单独记录使用“平台优惠券”的抵扣金额
            if (coupon.getBelongType().equals(BelongTypeEnum.PLATFORM.getCode())) {
                couponAmountMap.forEach((skuId, amount) -> platformCouponMap.merge(skuId, amount, BigDecimal::add));
            }
        }

        //校验计算后的优惠券减少金额
        BigDecimal couponAmount = couponMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        if (couponAmount.setScale(2, RoundingMode.HALF_UP).compareTo(orderVO.getCouponAmount().setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_COUPON_AMOUNT_MISMATCHED);
        }

        //****************************************************
        //Step 7: 校验运费
        //****************************************************
        //如果有满额包邮商品，则过滤满额包邮的商品，用于运费计算。默认为所有商品
        List<MobileOrderProductReq> freightProductList = orderProducts.stream().filter(p -> p.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode()) && p.getFreightType().equals(OrderFreightTypeEnum.BUYER.getCode())).collect(Collectors.toList());
        BigDecimal freightAmount = BigDecimal.ZERO;
        if (orderVO.getConsignee() != null) {
            List<MemberAndRoleIdDTO> memberList = freightProductList.stream().map(p -> {
                MemberAndRoleIdDTO member = new MemberAndRoleIdDTO();
                member.setMemberId(p.getVendorMemberId());
                member.setRoleId(p.getVendorRoleId());
                return member;
            }).collect(Collectors.toList());
            List<OrderFreeExpressConfigResp> freeExpressConfigList = orderParamConfigService.getOrderFreeExpressConfigList(memberList);

            Map<Long, BigDecimal> totalAmountMap = freightProductList.stream().collect(Collectors.toMap(MobileOrderProductReq::getVendorMemberId, p -> p.getRefPrice().multiply(p.getQuantity()), BigDecimal::add));

            freeExpressConfigList.forEach(configVO -> {
                BigDecimal totalAmount = totalAmountMap.getOrDefault(configVO.getMemberId(), BigDecimal.ZERO);
                if (totalAmount.compareTo(configVO.getOrderAmount()) >= 0) {
                    freightProductList.removeIf(p -> p.getVendorMemberId().equals(configVO.getMemberId()) && p.getVendorRoleId().equals(configVO.getRoleId()));
                }
            });

            BigDecimal freightResult = logisticsFeignService.findOrderFreight(orderVO.getConsignee().getConsigneeId(), freightProductList.stream().map(p -> new LogisticsProductDetailBO(p.getLogisticsTemplateId(), p.getQuantity(), p.getWeight())).collect(Collectors.toList()));
            freightAmount = freightResult.setScale(2, RoundingMode.HALF_UP);
        }

        if (orderVO.getFreight().setScale(2, RoundingMode.HALF_UP).compareTo(freightAmount) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_FREIGHT_AMOUNT_MISMATCHED);
        }

//        if(processPayment) {
//            BigDecimal totalAmount = (productAmount.add(freightAmount).subtract(couponAmount)).multiply(firstPayRate).setScale(2, RoundingMode.HALF_UP);
//            if(orderVO.getTotalAmount().setScale(2, RoundingMode.HALF_UP).compareTo(totalAmount) != 0) {
//                return Wrapper.fail(ResponseCode.ORDER_TOTAL_AMOUNT_MISMATCHED);
//            }
//        } else {
//            if(orderVO.getTotalAmount().compareTo(BigDecimal.ZERO) != 0) {
//                return Wrapper.fail(ResponseCode.ORDER_TOTAL_AMOUNT_MISMATCHED);
//            }
//        }

        //****************************************************
        //Step 8: 如果商品的配送方式为“自提”，从物流服务查询自提地址信息
        //****************************************************‘
        List<Long> addressIds = orderVO.getProducts().stream().filter(p -> p.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())).map(MobileOrderProductReq::getAddressId).distinct().collect(Collectors.toList());
        List<ProductAddressDTO> addressResult = logisticsFeignService.findProductAddresses(addressIds);

        //如果是自提，设置收货地址为自提地址用于待核销自提订单列表展示
        addressResult.stream().findFirst().ifPresent(address -> {
            OrderConsigneeReq consignee = new OrderConsigneeReq();
            consignee.setConsigneeId(address.getAddressId());
            consignee.setConsignee(address.getReceiver());
            consignee.setProvinceCode(address.getProvinceCode());
            consignee.setCityCode(address.getCityCode());
            consignee.setDistrictCode(address.getDistrictCode());
            consignee.setStreetCode(StrUtil.isEmpty(address.getStreetCode()) ? "" : address.getStreetCode());
            consignee.setAddress(address.getAddress());
            consignee.setPhone(address.getPhone());
            consignee.setDefaultConsignee(false);
            orderVO.setConsignee(consignee);
        });

        orderVO.getProducts().forEach(product -> {
            if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())) {
                addressResult.stream().filter(r -> r.getAddressId().equals(product.getAddressId())).findFirst().ifPresent(address -> {
                    product.setAddress(address.getAddress());
                    product.setReceiver(address.getReceiver());
                    product.setPhone(address.getPhone());
                });
            }
        });

        //****************************************************
        //Step 9: 计算积分抵扣
        //****************************************************
        //1. 根据供应商查询采购商抵扣金额
        //2. 抵扣金额根据供应商按比例再次降低商品到手价
        //3. 修改订单总金额字段
        List<OrderDeductionReq> deductions = orderVO.getDeductions();
        //计算积分抵扣总金额
        BigDecimal deductionAmount = BigDecimal.ZERO;
        Map<Long, BigDecimal> deductionMap = new HashMap<>();
        if (CollUtil.isNotEmpty(deductions)) {

            deductionAmount = orderVO.getDeductions().stream().map(OrderDeductionReq::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            for (OrderDeductionReq detailVO : deductions) {
                //根据单价*数量，计算平均到手价
                Map<Long, BigDecimal> weightedMap;
                if (MemberRelationTypeEnum.PLATFORM.getCode().equals(detailVO.getRelType())) {//平台积分
                    weightedMap = orderProducts.stream().collect(Collectors.toMap(MobileOrderProductReq::getSkuId, p -> p.getRefPrice().multiply(p.getQuantity())));
                } else {//店铺积分
                    weightedMap = orderProducts.stream().filter(p -> p.getVendorMemberId().equals(detailVO.getVendorMemberId())).collect(Collectors.toMap(MobileOrderProductReq::getSkuId, p -> p.getRefPrice().multiply(p.getQuantity())));
                }
                Map<Long, BigDecimal> deductionAmountMap = OrderDM.weightedAmount(weightedMap, detailVO.getAmount());
                deductionAmountMap.forEach((skuId, amount) -> deductionMap.merge(skuId, amount, BigDecimal::add));
                orderProducts.forEach(p -> {
                    BigDecimal refPrice = NumberUtil.max(p.getRefPrice().subtract(deductionAmountMap.getOrDefault(p.getSkuId(), BigDecimal.ZERO).divide(p.getQuantity(), 3, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP), BigDecimal.ZERO);
                    p.setRefPrice(refPrice);
                });
            }
        }

        //****************************************************
        //Step 10: 计算跨境电商进口商品税费
        //****************************************************
        //跨境商品，含税
        Map<Long, BigDecimal> taxesMap = productResult.stream().filter(p -> p.getIsCrossBorder() != null && p.getIsCrossBorder() && p.getTax()).collect(Collectors.toMap(OrderProductDetailDTO::getSkuId, p -> p.getRefPrice().multiply(p.getTaxRate()).multiply(p.getQuantity()), BigDecimal::add));
        orderProducts.forEach(p -> {
            BigDecimal refPrice = NumberUtil.max(p.getRefPrice().add(taxesMap.getOrDefault(p.getSkuId(), BigDecimal.ZERO).divide(p.getQuantity(), 3, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP), BigDecimal.ZERO);
            p.setRefPrice(refPrice);
        });

        //校验订单实付金额（前端计算方式：实付金额 = (Σ到手价*数量 + 运费 - 优惠券金额 - 积分抵扣金额) * 第一次的支付比例）
        BigDecimal totalAmount;
        if (processPayment) {
            totalAmount = (productAmount.add(freightAmount).add(orderVO.getTaxes()).subtract(couponAmount).subtract(deductionAmount)).multiply(firstPayRate).setScale(2, RoundingMode.HALF_UP);
        } else {
            //如果不需要支付，实付金额 = (Σ到手价*数量 + 运费 - 优惠券金额 - 积分抵扣金额)
            totalAmount = (productAmount.add(freightAmount).add(orderVO.getTaxes()).subtract(couponAmount).subtract(deductionAmount)).setScale(2, RoundingMode.HALF_UP);
        }

        if (orderVO.getTotalAmount().setScale(2, RoundingMode.HALF_UP).compareTo(totalAmount) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_TOTAL_AMOUNT_MISMATCHED);
        }

        //赠送优惠券集合
        List<OrderGiveCouponDTO> orderGiveCouponDTOList = marketingResult.stream().flatMap(p -> p.getGiveCouponDTOList().stream()).collect(Collectors.toList());

        // 结束返回
        return new BuyerOrderCheckBO(couponMap, platformCouponMap, deductionMap, taxesMap, freightProductList, orderGiveCouponDTOList);
    }


    /**
     * App - 拼团订单 - 查询商品详情、折扣、物流、促销活动等信息
     *
     * @param buyerMemberId        采购会员Id
     * @param buyerRoleId          采购会员角色Id
     * @param buyerMemberLevelType 采购会员（当前登录用户）等级类型
     * @param orderVO              订单接口参数
     * @param processPayment       交易流程是否需要支付
     * @param firstPayRate         交易流程中第一批次中最小支付次数的支付比例
     * @return 拼团Id
     */
    @Override
    public Long checkMobileGroupOrderProductPrices(Long buyerMemberId, Long buyerRoleId, Integer buyerMemberLevelType, BuyerOrderReq orderVO, boolean processPayment, BigDecimal firstPayRate) {
        //****************************************************
        // Step 1： 接口参数、规则判断
        //****************************************************
        List<MobileOrderProductReq> orderProducts = orderVO.getProducts();

        // Step 1-1 : 订单商品需要物流配送时，收货人信息不能为空
        if (orderProducts.stream().anyMatch(product -> product.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode())) && orderVO.getConsignee() == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_CONSIGNEE_MUST_NOT_EMPTY);
        }

        //Step 1-2 : 拼团订单的商品只有一个
        if (orderProducts.size() != 1) {
            throw new BusinessException(ResponseCodeEnum.ORDER_GROUP_PRODUCT_MUST_BE_ONE);
        }

        // Step 1-2 : 检查订单接口参数：商品列表
        for (MobileOrderProductReq product : orderProducts) {
            //拼团商品不能为赠品
            if (product.getPriceType().equals(PriceTypeEnum.GIFT.getCode())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_GROUP_PRODUCT_CAN_NOT_BE_GIFT);
            }

            //拼团商品的营销活动有且只有一个
            if (CollectionUtils.isEmpty(product.getPromotions()) || product.getPromotions().size() != 1) {
                throw new BusinessException(ResponseCodeEnum.ORDER_GROUP_PROMOTION_NUMBER_MUST_EQ_ONE);
            }

            //检查所有营销活动类型
            if (product.getPromotions().stream().anyMatch(promotion -> !promotion.getPromotionType().equals(ActivityTypeEnum.GROUP_PURCHASE.getCode()))) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PROMOTION_TYPE_MISMATCHED);
            }

            //到手价格：应该小于等于单价
            if (product.getRefPrice().compareTo(product.getPrice()) > 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_SHOULD_LTE_PRICE);
            }

            //如果配送方式为“物流”，配送方式不能为空
            if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode())) {
                if (NumberUtil.isNullOrZero(product.getFreightType())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_FREIGHT_TYPE_CAN_NOT_BE_NULL);
                }

                //如果运费类型为“买家承担”，物流模板Id、商品重量不能为空或0
                if (product.getFreightType().equals(OrderFreightTypeEnum.BUYER.getCode())) {
                    if (NumberUtil.isNullOrNegativeZero(product.getLogisticsTemplateId())) {
                        throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_LOGISTICS_TEMPLATE_ID_MUST_GREATER_THAN_ZERO);
                    }

                    if (NumberUtil.isNullOrNegativeZero(product.getWeight())) {
                        throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_WEIGHT_MUST_GREATER_THAN_ZERO);
                    }
                }
            }

            // 为方便后续校验，如果前端传Null值，将配送方式、运费类型、营销活动列表
            product.setFreightType(NumberUtil.isNullOrNegativeZero(product.getFreightType()) ? 0 : product.getFreightType());
        }

        // 为方便后续校验，如果前端传Null值，将配送时间和备注设置成空
        if (CollectionUtils.isEmpty(orderVO.getDeliverTimes())) {
            orderVO.setDeliverTimes(new ArrayList<>());
        }

        //拼团Id
        Long groupId = orderProducts.stream().flatMap(p -> p.getPromotions().stream()).map(p -> NumberUtil.isNullOrNegativeZero(p.getRecordId()) ? 0L : p.getRecordId()).findFirst().orElse(0L);
        //当前端传递拼团Id时，校验是否重复参与拼团
        if (NumberUtil.notNullAndPositive(groupId)) {
            if (marketingFeignService.checkGroupOrder(buyerMemberId, buyerRoleId, groupId)) {
                throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_CAN_NOT_JOIN_ORDER_GROUP_REPEATED);
            }
        }

        //****************************************************
        // Step 2: 以商品关联的营销活动列表判断，
        //****************************************************
        //营销活动优惠金额
        BigDecimal promotionAmount;
        //商品价格
        BigDecimal productAmount = BigDecimal.ZERO;

        List<PromotionProductDTO> marketingResult = marketingFeignService.findGroupOrderPromotions(orderVO.getShopId(), buyerMemberId, buyerRoleId, buyerMemberLevelType, orderVO.getProducts());

        log.info("拼团下单, 营销服务返回数据: {}", JSONUtil.toJsonStr(marketingResult));

        //Step 2-1: 根据营销活动返回的结果，校验接口商品的到手价
        for (MobileOrderProductReq orderProduct : orderProducts) {
            PromotionProductDTO promotionProduct = marketingResult.stream().filter(p -> p.getSkuId().equals(orderProduct.getSkuId())).findFirst().orElse(null);
            if (promotionProduct == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
            }

            if (orderProduct.getRefPrice().setScale(3, RoundingMode.HALF_UP).compareTo(promotionProduct.getRefPrice()) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_MISMATCHED, orderProduct.getName().concat(ResponseCodeEnum.ORDER_PRODUCT_REF_PRICE_MISMATCHED.getMessage()));
            }

            //根据营销服务返回的营销活动列表，判断当前用户是否能购买拼团商品
            if (CollectionUtils.isEmpty(promotionProduct.getPromotions()) || promotionProduct.getPromotions().stream().noneMatch(promotion -> promotion.getPromotionId().equals(orderProduct.getPromotions().get(0).getPromotionId()))) {
                throw new BusinessException(ResponseCodeEnum.ORDER_BUYER_CAN_NOT_CREATE_GROUP_ORDER);
            }

            //商品价格 = Σ(到手价 * 数量)
            productAmount = productAmount.add(orderProduct.getRefPrice().multiply(orderProduct.getQuantity()));
        }

        // 营销活动优惠金额
        promotionAmount = marketingResult.stream().map(PromotionProductDTO::getPromotionAmount).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);

        //****************************************************
        //Step 3: 校验营销活动优惠金额
        //****************************************************
        if (orderVO.getPromotionAmount().setScale(2, RoundingMode.HALF_UP).compareTo(promotionAmount.setScale(2, RoundingMode.HALF_UP)) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PROMOTION_AMOUNT_MISMATCHED);
        }

        //****************************************************
        //Step 4: 校验运费
        //****************************************************
        BigDecimal freightAmount = BigDecimal.ZERO;
        if (orderVO.getConsignee() != null) {
            BigDecimal freightResult = logisticsFeignService.findOrderFreight(orderVO.getConsignee().getConsigneeId(), orderProducts.stream().filter(p -> p.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode()) && p.getFreightType().equals(OrderFreightTypeEnum.BUYER.getCode())).map(p -> new LogisticsProductDetailBO(p.getLogisticsTemplateId(), p.getQuantity(), p.getWeight())).collect(Collectors.toList()));
            freightAmount = freightResult.setScale(2, RoundingMode.HALF_UP);
        }

        if (orderVO.getFreight().setScale(2, RoundingMode.HALF_UP).compareTo(freightAmount) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_FREIGHT_AMOUNT_MISMATCHED);
        }

        //****************************************************
        //Step 5: 校验订单实付金额（前端计算方式：实付金额 = (Σ到手价*数量 + 运费) * 第一次的支付比例）
        //****************************************************
        if (processPayment) {
            BigDecimal totalAmount = (productAmount.add(freightAmount)).multiply(firstPayRate).setScale(2, RoundingMode.HALF_UP);
            log.info("拼团下单, 会员: {}, 前端金额: {}, 后端金额: {}", buyerMemberId, orderVO.getTotalAmount().setScale(2, RoundingMode.HALF_UP), totalAmount);
            if (orderVO.getTotalAmount().setScale(2, RoundingMode.HALF_UP).compareTo(totalAmount) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_TOTAL_AMOUNT_MISMATCHED);
            }
        }
//        else {
//            if(orderVO.getTotalAmount().compareTo(BigDecimal.ZERO) != 0) {
//                return Wrapper.fail(ResponseCode.ORDER_TOTAL_AMOUNT_MISMATCHED);
//            }
//        }

        //****************************************************
        //Step 6: 如果商品的配送方式为“自提”，从物流服务查询自提地址信息
        //****************************************************‘
        List<Long> addressIds = orderVO.getProducts().stream().filter(p -> p.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())).map(MobileOrderProductReq::getAddressId).distinct().collect(Collectors.toList());
        List<ProductAddressDTO> addressResult = logisticsFeignService.findProductAddresses(addressIds);

        //如果是自提，设置收货地址为自提地址用于待核销自提订单列表展示
        addressResult.stream().findFirst().ifPresent(address -> {
            OrderConsigneeReq consignee = new OrderConsigneeReq();
            consignee.setConsigneeId(address.getAddressId());
            consignee.setConsignee(address.getReceiver());
            consignee.setProvinceCode(address.getProvinceCode());
            consignee.setCityCode(address.getCityCode());
            consignee.setDistrictCode(address.getDistrictCode());
            consignee.setStreetCode(StrUtil.isEmpty(address.getStreetCode()) ? "" : address.getStreetCode());
            consignee.setAddress(address.getAddress());
            consignee.setPhone(address.getPhone());
            consignee.setDefaultConsignee(false);
            orderVO.setConsignee(consignee);
        });

        orderVO.getProducts().forEach(product -> {
            if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())) {
                addressResult.stream().filter(r -> r.getAddressId().equals(product.getAddressId())).findFirst().ifPresent(address -> {
                    product.setAddress(address.getAddress());
                    product.setReceiver(address.getReceiver());
                    product.setPhone(address.getPhone());
                });
            }
        });

        // 结束返回
        return groupId;
    }

    /**
     * 判断优惠券是否属于商品优惠券
     *
     * @param belongType 优惠券归属类型
     * @param couponType 优惠券类型
     * @return 是-true，否-false
     */
    private Boolean isProductCoupon(Integer belongType, Integer couponType) {
        return (belongType.equals(BelongTypeEnum.PLATFORM.getCode()) && couponType.equals(PlatformCouponTypeEnum.ZERO_DISCOUNT.getCode())) || (belongType.equals(BelongTypeEnum.MERCHANT.getCode()) && (couponType.equals(MerchantCouponTypeEnum.ZERO_DISCOUNT.getCode()) || couponType.equals(MerchantCouponTypeEnum.COMMODITY.getCode())));
    }

    /**
     * 使用优惠券，重新计算商品到手价格
     *
     * @param couponVO      接口参数
     * @param orderProducts 订单商品Dto列表
     * @return 优惠金额
     */
    @Override
    public Map<Long, BigDecimal> modifyRefPrice(OrderCouponReq couponVO, List<MobileOrderProductReq> orderProducts, Map<Long, PromotionDTO> superCouponMap, Map<Long, Long> subProductPromotion) {
        BelongTypeEnum belongType = BelongTypeEnum.parse(couponVO.getBelongType());
        if (Objects.isNull(belongType)) {
            throw new BusinessException(ResponseCodeEnum.SERVICE_ERROR);
        }

        switch (belongType) {
            case PLATFORM:
                PlatformCouponTypeEnum platformCouponType = PlatformCouponTypeEnum.parse(couponVO.getCouponType());
                switch (platformCouponType) {
                    case ZERO_DISCOUNT:
                        // 0元抵扣优惠券只能用于一个商品，所以不需要判断数量
                        // 0元抵扣：到手价' = 0
                        // 优惠券减少金额 = 原到手价
                        Map<Long, BigDecimal> platformZeroCouponMap = new HashMap<>();
                        orderProducts.stream().filter(p -> p.getSkuId().equals(couponVO.getSkuId())).findFirst().ifPresent(p -> {
                            platformZeroCouponMap.putIfAbsent(p.getSkuId(), p.getRefPrice());
                            p.setRefPrice(BigDecimal.ZERO);
                        });
                        return platformZeroCouponMap;
                    case COMMON:
                        // 可叠加优惠券商品列表
                        List<MobileOrderProductReq> superCouponProducts = orderProducts.stream().filter(v -> orderProductsFilter(v, superCouponMap, subProductPromotion)).collect(Collectors.toList());
                        log.info("优惠券使用, 可用优惠券活动: {}, 可用优惠券商品: {}, 套餐商品活动: {}", superCouponMap.keySet(), superCouponProducts.stream().map(MobileOrderProductReq::getProductId).collect(Collectors.toList()), JSONUtil.toJsonStr(subProductPromotion));
                        // 每个商品优惠券减少的金额 = （原到手价 * 优惠券面额）/Σ(原到手价 * 数量）
                        // 每个商品的 到手价' = Max[（原到手价 - 减少的金额 / 数量）, 0]
                        Map<Long, BigDecimal> weightedMap = superCouponProducts.stream().collect(Collectors.toMap(MobileOrderProductReq::getSkuId, p -> p.getRefPrice().multiply(p.getQuantity())));
                        Map<Long, BigDecimal> platformCommonCouponMap = OrderDM.weightedAmount(weightedMap, couponVO.getAmount());
                        orderProducts.forEach(p -> {
                            BigDecimal refPrice = NumberUtil.max(p.getRefPrice().subtract(platformCommonCouponMap.getOrDefault(p.getSkuId(), BigDecimal.ZERO).divide(p.getQuantity(), 3, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP), BigDecimal.ZERO);
                            p.setRefPrice(refPrice);
                        });
                        return platformCommonCouponMap;
                    default:
                        break;
                }
                break;
            case MERCHANT:
                MerchantCouponTypeEnum merchantCouponType = MerchantCouponTypeEnum.parse(couponVO.getCouponType());
                switch (merchantCouponType) {
                    case ZERO_DISCOUNT:
                        // 0元抵扣优惠券只能用于一个商品，所以不需要判断数量
                        //0元抵扣：到手价' = 0
                        //优惠券减少金额 = 原到手价
                        Map<Long, BigDecimal> zeroCouponMap = new HashMap<>();
                        orderProducts.stream().filter(p -> p.getVendorMemberId().equals(couponVO.getVendorMemberId()) && p.getVendorRoleId().equals(couponVO.getVendorRoleId()) && p.getSkuId().equals(couponVO.getSkuId())).findFirst().ifPresent(p -> {
                            zeroCouponMap.putIfAbsent(p.getSkuId(), p.getRefPrice());
                            p.setRefPrice(BigDecimal.ZERO);
                        });
                        return zeroCouponMap;
                    case COMMON:
                        // 每个商品优惠券减少的金额 = （原到手价 * 优惠券面额）/Σ(原到手价 * 数量）
                        // 每个商品的 到手价' = Max[（原到手价 - 减少的金额 / 数量）, 0]
                        Map<Long, BigDecimal> weightedMap = orderProducts.stream().filter(p -> p.getVendorMemberId().equals(couponVO.getVendorMemberId()) && p.getVendorRoleId().equals(couponVO.getVendorRoleId())).collect(Collectors.toMap(MobileOrderProductReq::getSkuId, p -> p.getRefPrice().multiply(p.getQuantity())));
                        Map<Long, BigDecimal> commonCouponMap = OrderDM.weightedAmount(weightedMap, couponVO.getAmount());
                        orderProducts.stream().filter(p -> p.getVendorMemberId().equals(couponVO.getVendorMemberId()) && p.getVendorRoleId().equals(couponVO.getVendorRoleId())).forEach(p -> {
                            BigDecimal refPrice = NumberUtil.max(p.getRefPrice().subtract(commonCouponMap.getOrDefault(p.getSkuId(), BigDecimal.ZERO).divide(p.getQuantity(), 3, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP), BigDecimal.ZERO);
                            p.setRefPrice(refPrice);
                        });
                        return commonCouponMap;
                    case CATEGORY:
                        break;
                    case BRAND:
                        break;
                    case COMMODITY:
                        //单个商品的优惠券，到手价' = Max[（原到手价 - 面额 / 数量）, 0]
                        //优惠券减少金额 = 面额
                        Map<Long, BigDecimal> productCouponMap = new HashMap<>();
                        orderProducts.stream().filter(p -> p.getVendorMemberId().equals(couponVO.getVendorMemberId()) && p.getVendorRoleId().equals(couponVO.getVendorRoleId()) && p.getSkuId().equals(couponVO.getSkuId())).findFirst().ifPresent(p -> {
                            BigDecimal refPrice = NumberUtil.max(BigDecimal.ZERO, p.getRefPrice().subtract(couponVO.getAmount().divide(p.getQuantity(), 3, RoundingMode.HALF_UP)).setScale(3, RoundingMode.HALF_UP));
                            productCouponMap.putIfAbsent(p.getSkuId(), couponVO.getAmount());
                            p.setRefPrice(refPrice);
                        });
                        return productCouponMap;
                }
                break;
            default:
                break;
        }

        return new HashMap<>();
    }

    /**
     * 过滤可使用优惠券的商品
     *
     * @param orderProductVO 订单商品
     * @param superCouponMap 可使用优惠券的活动
     * @return 是否可使用优惠券
     */
    private boolean orderProductsFilter(MobileOrderProductReq orderProductVO, Map<Long, PromotionDTO> superCouponMap, Map<Long, Long> subProductPromotion) {
        if (Objects.nonNull(orderProductVO.getPromotions()) && !orderProductVO.getPromotions().isEmpty()) {
            return orderProductVO.getPromotions().stream().anyMatch(v -> superCouponMap.containsKey(v.getPromotionId()));
        }
        if (Objects.equals(OrderPromotionTypeEnum.PART_OF_SET.getCode(), orderProductVO.getPromotionType())) {
            return superCouponMap.containsKey(subProductPromotion.get(orderProductVO.getProductId()));
        }
        return true;
    }

    /**
     * （拆单）加权平均拆单前订单的运费
     *
     * @param orderFreight 接口参数中的运费，即拆单前的订单运费
     * @param consigneeVO  接口参数中的收货人信息
     * @param vendorMap    拆单条件
     * @return 查询结果
     */
    @Override
    public Map<OrderSeparateDTO, BigDecimal> findSeparateOrderFreight(BigDecimal orderFreight, OrderConsigneeReq consigneeVO, Map<OrderSeparateDTO, BigDecimal> vendorMap) {
        //Step 1: 如果无收货人地址Id，运费即前端传递的运费
        //        如果只有一个供应商，且没有上游供应商，或同一个上游供应商，无需拆单，运费即为前端传递的运费
        if (NumberUtil.isNullOrZero(orderFreight) || consigneeVO == null || NumberUtil.isNullOrZero(consigneeVO.getConsigneeId()) || vendorMap.size() == 1) {
            return vendorMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, p -> orderFreight));
        }

        return findSeparateOrderFreight(orderFreight, vendorMap);
    }

    /**
     * （拆单）加权平均拆单前订单的运费
     *
     * @param orderFreight 接口参数中的运费，即拆单前的订单运费
     * @param vendorMap    拆单条件
     * @return 查询结果
     */
    @Override
    public Map<OrderSeparateDTO, BigDecimal> findSeparateOrderFreight(BigDecimal orderFreight, Map<OrderSeparateDTO, BigDecimal> vendorMap) {
        //如果运费为0，返回0
        if (orderFreight.compareTo(BigDecimal.ZERO) == 0) {
            return vendorMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, f -> BigDecimal.ZERO));
        }

        //Step 2: 计算总的重量*数量
        BigDecimal totalWeight = vendorMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        //Step 3: 如果所有拆单订单的 重量*数量为0（即没有商品有运费模板），返回 0
        if (totalWeight.compareTo(BigDecimal.ZERO) == 0) {
            return vendorMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, f -> BigDecimal.ZERO));
        }

        return vendorMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, f -> f.getValue().multiply(orderFreight).divide(totalWeight, 2, RoundingMode.HALF_UP)));
    }

    /**
     * （B2B订单）校验询价商品价格
     *
     * @param quoteNo  询价单号
     * @param products 订单商品列表
     */
    @Override
    public void checkBusinessOrderProductPrices(String quoteNo, List<BusinessOrderProductReq> products) {
        List<OrderQuotationDTO> quoteResult = tradeFeignService.findInquiryOrderPrices(quoteNo);

        if (CollectionUtils.isEmpty(quoteResult)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_QUOTATION_DOES_NOT_EXIST);
        }

        for (BusinessOrderProductReq product : products) {
            OrderQuotationDTO quoteProduct = quoteResult.stream().filter(p -> p.getProductId().equals(product.getProductId()) && p.getSkuId().equals(product.getSkuId())).findFirst().orElse(null);
            if (quoteProduct == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCTS_MISMATCH_QUOTATION_PRODUCTS);
            }

            if (product.getPrice().setScale(3, RoundingMode.HALF_UP).compareTo(quoteProduct.getPrice().setScale(3, RoundingMode.HALF_UP)) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_PRICE_IS_NOT_CORRECT);
            }

            if (product.getQuantity().setScale(3, RoundingMode.HALF_UP).compareTo(quoteProduct.getMinimumQuantity().setScale(3, RoundingMode.HALF_UP)) < 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_QUANTITY_LESS_THAN_QUOTATION);
            }

            if (product.getTaxRate().setScale(4, RoundingMode.HALF_UP).compareTo(quoteProduct.getTaxRate().setScale(4, RoundingMode.HALF_UP)) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_TAX_RATE_MISMATCH_QUOTATION);
            }

            //校验其他参数
            if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())) {
                if (!StringUtils.hasText(product.getReceiver())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_IS_MISSING);
                }

                if (!StringUtils.hasText(product.getPhone())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_PHONE_IS_MISSING);
                }

                if (!StringUtils.hasText(product.getAddress())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_ADDRESS_IS_MISSING);
                }
            }
        }
    }

    /**
     * 校验现货采购订单商品价格
     *
     * @return 订单商品总价
     */
    @Override
    public BigDecimal checkPurchaseOrderProductPrices(UserLoginCacheDTO loginUser, BuyerPurchaseOrderReq buyerPurchaseOrderReq) {
        Long buyerMemberId = loginUser.getMemberId();
        Long buyerRoleId = loginUser.getMemberRoleId();
        Integer memberType = loginUser.getMemberType();
        Long shopId = buyerPurchaseOrderReq.getShopId();
        List<PurchaseOrderProductReq> products = buyerPurchaseOrderReq.getProducts();

        List<QuotedMaterielResp> quotedMaterielRespList = CollUtil.toList();
        if (Objects.nonNull(buyerPurchaseOrderReq.getAskPurchaseQuoteId())) {
            // 采购报价单下单, 金额交易通过报价单的金额进行校验
            quotedMaterielRespList = purchaseFeignService.getPurchaseQuoteDetails(buyerPurchaseOrderReq.getAskPurchaseQuoteId());
        }

        //从商品服务获得商品阶梯价格
        Map<Long, Map<String, BigDecimal>> priceResult = productFeignService.batchCheckProductPrice(buyerMemberId, buyerRoleId, shopId, memberType, products.stream().map(PurchaseOrderProductReq::getSkuId).collect(Collectors.toList()));
        // 校验商品价格
        for (PurchaseOrderProductReq product : products) {
            Map<String, BigDecimal> priceMap = priceResult.getOrDefault(product.getSkuId(), null);
            if (priceMap == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
            }

            checkProductPrice(priceMap, product, quotedMaterielRespList);

            //校验其他参数（需要判断运费模板Id）
            if (product.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())) {
                if (!StringUtils.hasText(product.getReceiver())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_IS_MISSING);
                }

                if (!StringUtils.hasText(product.getPhone())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_PHONE_IS_MISSING);
                }

                if (!StringUtils.hasText(product.getAddress())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_ADDRESS_IS_MISSING);
                }
            }
        }

        //不计算折扣
        return products.stream().filter(product -> !product.getPriceType().equals(PriceTypeEnum.GIFT.getCode())).map(product -> product.getPrice().multiply(product.getQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * （物流服务）查询现货采购订单运费
     *
     * @param consigneeVO 收货人信息
     * @param products    现货订单商品列表
     * @return 运费
     */
    @Override
    public BigDecimal findPurchaseOrderFreight(BuyerOrderConsigneeReq consigneeVO, List<PurchaseOrderProductReq> products) {
        if (Objects.isNull(consigneeVO)) {
            return BigDecimal.ZERO;
        }

        List<LogisticsProductDetailBO> logisticsProducts = products.stream().filter(product -> NumberUtil.notNullAndPositive(product.getLogisticsTemplateId()) && NumberUtil.notNullAndPositive(product.getWeight())).map(product -> {
            LogisticsProductDetailBO detailBO = new LogisticsProductDetailBO();
            detailBO.setTemplateId(product.getLogisticsTemplateId());
            detailBO.setQuantity(product.getQuantity());
            detailBO.setWeight(product.getWeight());
            return detailBO;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(logisticsProducts)) {
            return BigDecimal.ZERO;
        }

        return logisticsFeignService.findOrderFreight(consigneeVO.getConsigneeId(), logisticsProducts);
    }

    /**
     * 校验采购订单商品接口参数
     *
     * @param order    订单
     * @param products 订单商品列表
     * @param isCreate 新增订单-true, 修改订单-false
     * @return 订单总金额，是否需要物流
     */
    @Override
    public OrderProductCheckBO checkBuyerOrderProduct(OrderDO order, List<OrderProductReq> products, boolean isCreate) {
        //商品总金额
        BigDecimal productAmount = BigDecimal.ZERO;
        //是否包含“物流”配送方式
        boolean needLogistics = false;
        //购物车Id列表
        List<Long> cartIds = new ArrayList<>();

        List<OrderProductDO> productList = new ArrayList<>();
        for (OrderProductReq orderProductReq : products) {
            needLogistics = needLogistics || orderProductReq.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode());

            OrderProductDO product = new OrderProductDO();
            product.setOrder(order);
            product.setPriceType(orderProductReq.getPriceType());
            product.setProductId(orderProductReq.getProductId());
            product.setSkuId(orderProductReq.getSkuId());
            product.setStockId(NumberUtil.isNullOrZero(orderProductReq.getStockId()) ? 0L : orderProductReq.getStockId());
            product.setCartId(NumberUtil.isNullOrZero(orderProductReq.getCartId()) ? 0L : orderProductReq.getCartId());
            product.setProductNo("");
            product.setName(orderProductReq.getName());
            product.setCategory(orderProductReq.getCategory());
            product.setBrand(StringUtils.hasLength(orderProductReq.getBrand()) ? orderProductReq.getBrand() : "");
            product.setSpec(StringUtils.hasLength(orderProductReq.getSpec()) ? orderProductReq.getSpec() : "");
            product.setUnit(orderProductReq.getUnit());
            product.setLogo(orderProductReq.getLogo());
            product.setOriginalPrice(orderProductReq.getPrice());
            //商品单价 * 折扣 = 到手价
            product.setPrice(orderProductReq.getPrice());
            product.setDiscount(NumberUtil.notNullAndPositive(orderProductReq.getDiscount()) ? orderProductReq.getDiscount() : BigDecimal.ONE);
            product.setRefPrice(orderProductReq.getPriceType().equals(PriceTypeEnum.GIFT.getCode()) ? BigDecimal.ZERO : product.getPrice().multiply(product.getDiscount()).setScale(3, RoundingMode.HALF_UP));
            product.setStock(NumberUtil.isNullOrNegative(orderProductReq.getStock()) ? BigDecimal.ZERO : orderProductReq.getStock());
            product.setQuantity(orderProductReq.getQuantity());
            product.setTax(orderProductReq.getTax() != null && orderProductReq.getTax());
            product.setTaxRate(NumberUtil.notNullAndPositive(orderProductReq.getTaxRate()) ? orderProductReq.getTaxRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            product.setAmount(product.getRefPrice().multiply(orderProductReq.getQuantity()));
            //使用“平台优惠券”后的优惠抵扣金额
            product.setPlatformCouponAmount(BigDecimal.ZERO);
            product.setPaidAmount(BigDecimal.ZERO);
            product.setDeliverType(orderProductReq.getDeliveryType());
            product.setFreightType(NumberUtil.isNullOrNegative(orderProductReq.getFreightType()) ? OrderFreightTypeEnum.NONE.getCode() : orderProductReq.getFreightType());
            product.setWeight(NumberUtil.isNullOrNegativeZero(orderProductReq.getWeight()) ? BigDecimal.ZERO : orderProductReq.getWeight());
            product.setLogisticsTemplateId(NumberUtil.isNullOrNegativeZero(orderProductReq.getLogisticsTemplateId()) ? 0L : orderProductReq.getLogisticsTemplateId());
            product.setReceiver(StringUtils.hasLength(orderProductReq.getReceiver()) ? orderProductReq.getReceiver().trim() : "");
            product.setAddress(StringUtils.hasLength(orderProductReq.getAddress()) ? orderProductReq.getAddress().trim() : "");
            product.setPhone(StringUtils.hasLength(orderProductReq.getPhone()) ? orderProductReq.getPhone() : "");
            //转单商品标记为默认值
            product.setSeparateType(OrderSeparateTypeEnum.DEFAULT.getCode());
            product.setRelationId(0L);

            //备注
            product.setRemark("");

            //关联报价单的商品
            product.setMaterial(null);
            //营销活动
            product.setPromotions(new HashSet<>());

            product.setDelivered(BigDecimal.ZERO);
            product.setReceived(BigDecimal.ZERO);
            product.setExchangeCount(BigDecimal.ZERO);
            product.setReturnCount(BigDecimal.ZERO);
            product.setMaintainCount(BigDecimal.ZERO);
            product.setReturnAmount(BigDecimal.ZERO);
            product.setEnhanceCount(BigDecimal.ZERO);
            product.setLeftCount(product.getQuantity());
            product.setDifferCount(BigDecimal.ZERO);

            productList.add(product);

            //不需要计算折扣（已经计算过了）
            productAmount = productAmount.add(product.getAmount());

            cartIds.add(product.getCartId());
        }

        if (!isCreate) {
            orderProductRepository.deleteByOrder(order);
        }

        orderProductRepository.saveAll(productList);
        order.setProducts(new HashSet<>(productList));
        return new OrderProductCheckBO(productAmount, needLogistics, cartIds);
    }

    /**
     * 校验积分兑换订单商品
     *
     * @param order          订单
     * @param orderProductVO 积分兑换商品
     * @return 订单总积分
     */
    @Override
    public OrderProductCheckBO checkPointsOrderProduct(OrderDO order, PointsOrderProductReq orderProductVO) {
        OrderProductDO product = new OrderProductDO();
        product.setOrder(order);
        product.setPriceType(PriceTypeEnum.SCORE.getCode());
        product.setProductId(orderProductVO.getProductId());
        product.setSkuId(orderProductVO.getSkuId());
        product.setStockId(NumberUtil.isNullOrZero(orderProductVO.getStockId()) ? 0L : orderProductVO.getStockId());
        product.setCartId(0L);
        product.setProductNo("");
        product.setName(orderProductVO.getName());
        product.setCategory(orderProductVO.getCategory());
        product.setBrand(StringUtils.hasLength(orderProductVO.getBrand()) ? orderProductVO.getBrand() : "");
        product.setSpec(StringUtils.hasLength(orderProductVO.getSpec()) ? orderProductVO.getSpec() : "");
        product.setUnit(orderProductVO.getUnit());
        product.setLogo(orderProductVO.getLogo());
        product.setOriginalPrice(orderProductVO.getPrice());
        //积分订单：商品单价、到手价即兑换所需积分
        product.setPrice(orderProductVO.getPrice());
        product.setRefPrice(orderProductVO.getPrice());
        product.setStock(NumberUtil.isNullOrNegative(orderProductVO.getStock()) ? BigDecimal.ZERO : orderProductVO.getStock());
        product.setDiscount(BigDecimal.ONE);
        product.setQuantity(orderProductVO.getQuantity());
        product.setTax(orderProductVO.getTax() != null && orderProductVO.getTax());
        product.setTaxRate(NumberUtil.notNullAndPositive(orderProductVO.getTaxRate()) ? orderProductVO.getTaxRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        product.setAmount(product.getRefPrice().multiply(orderProductVO.getQuantity()));
        //使用“平台优惠券”后的优惠抵扣金额
        product.setPlatformCouponAmount(BigDecimal.ZERO);
        product.setPaidAmount(BigDecimal.ZERO);
        product.setDeliverType(orderProductVO.getDeliveryType());
        //积分订单商品运费类型为“卖家承担”
        product.setFreightType(OrderFreightTypeEnum.VENDOR.getCode());
        product.setWeight(NumberUtil.isNullOrNegativeZero(orderProductVO.getWeight()) ? BigDecimal.ZERO : orderProductVO.getWeight());
        product.setLogisticsTemplateId(NumberUtil.isNullOrNegativeZero(orderProductVO.getLogisticsTemplateId()) ? 0L : orderProductVO.getLogisticsTemplateId());
        product.setReceiver(StringUtils.hasLength(orderProductVO.getReceiver()) ? orderProductVO.getReceiver().trim() : "");
        product.setAddress(StringUtils.hasLength(orderProductVO.getAddress()) ? orderProductVO.getAddress().trim() : "");
        product.setPhone(StringUtils.hasLength(orderProductVO.getPhone()) ? orderProductVO.getPhone() : "");
        //转单商品标记为默认值
        product.setSeparateType(OrderSeparateTypeEnum.DEFAULT.getCode());
        product.setRelationId(0L);

        //备注
        product.setRemark("");

        //关联报价单的商品
        product.setMaterial(null);
        //营销活动
        product.setPromotions(new HashSet<>());

        product.setDelivered(BigDecimal.ZERO);
        product.setReceived(BigDecimal.ZERO);
        product.setExchangeCount(BigDecimal.ZERO);
        product.setReturnCount(BigDecimal.ZERO);
        product.setMaintainCount(BigDecimal.ZERO);
        product.setReturnAmount(BigDecimal.ZERO);
        product.setEnhanceCount(BigDecimal.ZERO);
        product.setLeftCount(product.getQuantity());
        product.setDifferCount(BigDecimal.ZERO);

        orderProductRepository.saveAndFlush(product);
        order.setProducts(Stream.of(product).collect(Collectors.toSet()));

        return new OrderProductCheckBO(product.getAmount(), product.getDeliverType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode()), new ArrayList<>());
    }

    /**
     * 校验B2B订单商品
     *
     * @param order    订单
     * @param products 订单商品列表
     * @param isCreate 新增订单-true, 修改订单-false
     * @return 订单总金额，是否需要物流
     */
    @Override
    public OrderProductCheckBO checkBusinessOrderProduct(OrderDO order, List<BusinessOrderProductReq> products, boolean isCreate) {
        //商品总金额
        BigDecimal productAmount = BigDecimal.ZERO;
        //是否包含“物流”配送方式
        boolean needLogistics = false;
        //购物车Id列表
        List<Long> cartIds = new ArrayList<>();

        List<OrderProductDO> productList = new ArrayList<>();
        for (BusinessOrderProductReq orderProductVO : products) {
            needLogistics = needLogistics || orderProductVO.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode());

            OrderProductDO product = new OrderProductDO();
            product.setOrder(order);
            product.setProductId(orderProductVO.getProductId());
            product.setSkuId(orderProductVO.getSkuId());
            product.setStockId(NumberUtil.isNullOrZero(orderProductVO.getStockId()) ? 0L : orderProductVO.getStockId());
            product.setCartId(NumberUtil.isNullOrZero(orderProductVO.getCartId()) ? 0L : orderProductVO.getCartId());
            product.setProductNo("");
            product.setName(orderProductVO.getName());
            product.setCategory(orderProductVO.getCategory());
            product.setBrand(StringUtils.hasLength(orderProductVO.getBrand()) ? orderProductVO.getBrand() : "");
            product.setSpec(StringUtils.hasLength(orderProductVO.getSpec()) ? orderProductVO.getSpec() : "");
            product.setUnit(orderProductVO.getUnit());
            product.setLogo(orderProductVO.getLogo());
            product.setOriginalPrice(orderProductVO.getPrice());
            //B2B订单：商品单价即到手价
            product.setPriceType(orderProductVO.getPriceType());
            product.setPrice(orderProductVO.getPrice());
            product.setDiscount(NumberUtil.isNullOrNegativeZero(orderProductVO.getDiscount()) ? BigDecimal.ONE : orderProductVO.getDiscount().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
            product.setRefPrice(orderProductVO.getPriceType().equals(PriceTypeEnum.GIFT.getCode()) ? BigDecimal.ZERO : product.getPrice().multiply(product.getDiscount()));
            product.setStock(NumberUtil.isNullOrNegativeZero(orderProductVO.getStock()) ? BigDecimal.ZERO : orderProductVO.getStock());
            product.setQuantity(orderProductVO.getQuantity());
            product.setTax(orderProductVO.getTax() != null && orderProductVO.getTax());
            product.setTaxRate(NumberUtil.notNullAndPositive(orderProductVO.getTaxRate()) ? orderProductVO.getTaxRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            product.setAmount(product.getRefPrice().multiply(orderProductVO.getQuantity()));
            //使用“平台优惠券”后的优惠抵扣金额
            product.setPlatformCouponAmount(BigDecimal.ZERO);
            product.setPaidAmount(BigDecimal.ZERO);
            product.setDeliverType(orderProductVO.getDeliveryType());
            product.setFreightType(OrderFreightTypeEnum.NONE.getCode());
            product.setWeight(NumberUtil.isNullOrNegativeZero(orderProductVO.getWeight()) ? BigDecimal.ZERO : orderProductVO.getWeight());
            product.setLogisticsTemplateId(NumberUtil.isNullOrNegativeZero(orderProductVO.getLogisticsTemplateId()) ? 0L : orderProductVO.getLogisticsTemplateId());
            product.setReceiver(StringUtils.hasLength(orderProductVO.getReceiver()) ? orderProductVO.getReceiver().trim() : "");
            product.setAddress(StringUtils.hasLength(orderProductVO.getAddress()) ? orderProductVO.getAddress().trim() : "");
            product.setPhone(StringUtils.hasLength(orderProductVO.getPhone()) ? orderProductVO.getPhone() : "");
            //转单商品标记为默认值
            product.setSeparateType(OrderSeparateTypeEnum.DEFAULT.getCode());
            product.setRelationId(0L);

            //备注
            product.setRemark("");

            //关联报价单的商品
            product.setMaterial(null);
            //营销活动
            product.setPromotions(new HashSet<>());

            product.setDelivered(BigDecimal.ZERO);
            product.setReceived(BigDecimal.ZERO);
            product.setExchangeCount(BigDecimal.ZERO);
            product.setReturnCount(BigDecimal.ZERO);
            product.setMaintainCount(BigDecimal.ZERO);
            product.setReturnAmount(BigDecimal.ZERO);
            product.setEnhanceCount(BigDecimal.ZERO);
            product.setLeftCount(product.getQuantity());
            product.setDifferCount(BigDecimal.ZERO);

            productList.add(product);

            //不需要计算折扣（已经计算过了）
            productAmount = productAmount.add(product.getAmount());

            cartIds.add(product.getCartId());
        }

        if (!isCreate) {
            orderProductRepository.deleteByOrder(order);
        }

        orderProductRepository.saveAll(productList);
        order.setProducts(new HashSet<>(productList));
        return new OrderProductCheckBO(productAmount, needLogistics, cartIds);
    }

    /**
     * 校验B2B订单商品
     *
     * @param order    订单
     * @param products 订单商品列表
     * @param isCreate 新增订单-true, 修改订单-false
     * @return 订单总金额，是否需要物流
     */
    @Override
    public OrderProductCheckBO checkPurchaseOrderProduct(OrderDO order, List<PurchaseOrderProductReq> products, boolean isCreate) {
        //商品总金额
        BigDecimal productAmount = BigDecimal.ZERO;
        //是否包含“物流”配送方式
        boolean needLogistics = false;

        List<OrderProductDO> productList = new ArrayList<>();
        for (PurchaseOrderProductReq orderProductVO : products) {
            needLogistics = needLogistics || orderProductVO.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode());
            OrderProductDO product = new OrderProductDO();
            product.setOrder(order);
            product.setProductId(orderProductVO.getProductId());
            product.setSkuId(orderProductVO.getSkuId());
            product.setStockId(NumberUtil.isNullOrZero(orderProductVO.getStockId()) ? 0L : orderProductVO.getStockId());
            product.setCartId(0L);
            product.setProductNo("");
            product.setName(orderProductVO.getName());
            product.setCategory(orderProductVO.getCategory());
            product.setBrand(StringUtils.hasLength(orderProductVO.getBrand()) ? orderProductVO.getBrand() : "");
            product.setSpec(StringUtils.hasLength(orderProductVO.getSpec()) ? orderProductVO.getSpec() : "");
            product.setUnit(orderProductVO.getUnit());
            product.setLogo(orderProductVO.getLogo());
            product.setOriginalPrice(orderProductVO.getPrice());
            //商品单价、到手价
            product.setPriceType(orderProductVO.getPriceType());
            product.setPrice(orderProductVO.getPrice());
            product.setDiscount(orderProductVO.getDiscount().setScale(4, RoundingMode.HALF_UP));
            product.setRefPrice(orderProductVO.getPriceType().equals(PriceTypeEnum.GIFT.getCode()) ? BigDecimal.ZERO : product.getPrice().multiply(product.getDiscount()));
            product.setStock(NumberUtil.isNullOrNegativeZero(orderProductVO.getStock()) ? BigDecimal.ZERO : orderProductVO.getStock());
            product.setQuantity(orderProductVO.getQuantity());
            product.setTax(orderProductVO.getTax() != null && orderProductVO.getTax());
            product.setTaxRate(NumberUtil.notNullAndPositive(orderProductVO.getTaxRate()) ? orderProductVO.getTaxRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            product.setAmount(product.getRefPrice().multiply(orderProductVO.getQuantity()));
            //使用“平台优惠券”后的优惠抵扣金额
            product.setPlatformCouponAmount(BigDecimal.ZERO);
            product.setPaidAmount(BigDecimal.ZERO);
            product.setDeliverType(orderProductVO.getDeliveryType());
            product.setFreightType(OrderFreightTypeEnum.NONE.getCode());
            product.setWeight(NumberUtil.isNullOrNegativeZero(orderProductVO.getWeight()) ? BigDecimal.ZERO : orderProductVO.getWeight());
            product.setLogisticsTemplateId(NumberUtil.isNullOrNegativeZero(orderProductVO.getLogisticsTemplateId()) ? 0L : orderProductVO.getLogisticsTemplateId());
            product.setReceiver(StringUtils.hasLength(orderProductVO.getReceiver()) ? orderProductVO.getReceiver().trim() : "");
            product.setAddress(StringUtils.hasLength(orderProductVO.getAddress()) ? orderProductVO.getAddress().trim() : "");
            product.setPhone(StringUtils.hasLength(orderProductVO.getPhone()) ? orderProductVO.getPhone() : "");
            //转单商品标记为默认值
            product.setSeparateType(OrderSeparateTypeEnum.DEFAULT.getCode());
            product.setRelationId(0L);

            //备注
            product.setRemark("");

            //关联报价单的商品
            product.setMaterial(null);
            //营销活动
            product.setPromotions(new HashSet<>());

            product.setDelivered(BigDecimal.ZERO);
            product.setReceived(BigDecimal.ZERO);
            product.setExchangeCount(BigDecimal.ZERO);
            product.setReturnCount(BigDecimal.ZERO);
            product.setMaintainCount(BigDecimal.ZERO);
            product.setReturnAmount(BigDecimal.ZERO);
            product.setEnhanceCount(BigDecimal.ZERO);
            product.setLeftCount(product.getQuantity());
            product.setDifferCount(BigDecimal.ZERO);

            productList.add(product);

            //不需要计算折扣（已经计算过了）
            productAmount = productAmount.add(product.getAmount());
        }

        if (!isCreate) {
            orderProductRepository.deleteByOrder(order);
        }

        orderProductRepository.saveAll(productList);
        order.setProducts(new HashSet<>(productList));
        return new OrderProductCheckBO(productAmount, needLogistics, new ArrayList<>());
    }

    @Override
    public OrderProductCheckBO checkMobileOrderProduct(OrderDO order, List<MobileOrderProductReq> products, BuyerOrderCheckBO checkResult, GoldPriceResp goldPrice, Map<Long, List<CommodityExtraDataParamResp>> commodityExtraDataParamList) {
        Map<Long, BigDecimal> platformCouponMap = checkResult.getPlatformCouponMap(); // 优惠券金额

        //是否包含“物流”配送方式
        boolean needLogistics = false;
        //购物车Id列表
        List<Long> cartIds = new ArrayList<>();

        List<OrderProductDO> productList = new ArrayList<>();
        Set<OrderProductPositionDO> orderProductPositionList = new HashSet<>();
        List<CommodityExtraDataParamDO> commodityExtraDataParamDOList = new ArrayList<>();

        for (MobileOrderProductReq orderProductVO : products) {
            needLogistics = needLogistics || orderProductVO.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode());

            OrderProductDO product = new OrderProductDO();
            product.setOrder(order);
            product.setProductId(orderProductVO.getProductId());
            product.setSkuId(orderProductVO.getSkuId());
            product.setStockId(NumberUtil.isNullOrZero(orderProductVO.getStockId()) ? 0L : orderProductVO.getStockId());
            product.setCartId(NumberUtil.isNullOrZero(orderProductVO.getCartId()) ? 0L : orderProductVO.getCartId());
            product.setProductNo("");
            product.setName(orderProductVO.getName());
            product.setCategory(orderProductVO.getCategory());
            product.setBrand(StringUtils.hasLength(orderProductVO.getBrand()) ? orderProductVO.getBrand() : "");
            product.setSpec(StringUtils.hasLength(orderProductVO.getSpec()) ? orderProductVO.getSpec() : "");
            product.setUnit(orderProductVO.getUnit());
            product.setLogo(orderProductVO.getLogo());
            product.setOriginalPrice(orderProductVO.getPrice());
            //App端由于有优惠券，所以要注意区分单价、到手价
            product.setPromotionType(orderProductVO.getPromotionType());
            product.setPriceType(orderProductVO.getPriceType());
            product.setPrice(orderProductVO.getPrice());
            product.setDiscount(NumberUtil.isNullOrZero(orderProductVO.getDiscount()) ? BigDecimal.ONE : orderProductVO.getDiscount());
            //赠品的到手价为0，前端已经调用营销活动接口获得到手价，这里直接赋值
            product.setRefPrice(orderProductVO.getPriceType().equals(PriceTypeEnum.GIFT.getCode()) ? BigDecimal.ZERO : orderProductVO.getRefPrice().setScale(3, RoundingMode.HALF_UP));
            product.setStock(NumberUtil.isNullOrNegative(orderProductVO.getStock()) ? BigDecimal.ZERO : orderProductVO.getStock());
            product.setQuantity(orderProductVO.getQuantity());
            product.setTax(orderProductVO.getTax() != null && orderProductVO.getTax());
            product.setTaxRate(NumberUtil.notNullAndPositive(orderProductVO.getTaxRate()) ? orderProductVO.getTaxRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            //使用“平台优惠券”后的抵扣金额
            product.setPlatformCouponAmount(CollectionUtils.isEmpty(platformCouponMap) ? BigDecimal.ZERO : platformCouponMap.getOrDefault(orderProductVO.getSkuId(), BigDecimal.ZERO));
            product.setPaidAmount(BigDecimal.ZERO);
            product.setDeliverType(orderProductVO.getDeliveryType());
            product.setFreightType(NumberUtil.isNullOrNegative(orderProductVO.getFreightType()) ? OrderFreightTypeEnum.NONE.getCode() : orderProductVO.getFreightType());
            product.setWeight(NumberUtil.isNullOrNegativeZero(orderProductVO.getWeight()) ? BigDecimal.ZERO : orderProductVO.getWeight());
            product.setLogisticsTemplateId(NumberUtil.isNullOrNegativeZero(orderProductVO.getLogisticsTemplateId()) ? 0L : orderProductVO.getLogisticsTemplateId());
            product.setReceiver(StringUtils.hasLength(orderProductVO.getReceiver()) ? orderProductVO.getReceiver().trim() : "");
            product.setAddress(StringUtils.hasLength(orderProductVO.getAddress()) ? orderProductVO.getAddress().trim() : "");
            product.setPhone(StringUtils.hasLength(orderProductVO.getPhone()) ? orderProductVO.getPhone() : "");
            //转单商品标记为默认值
            product.setSeparateType(OrderSeparateTypeEnum.DEFAULT.getCode());
            product.setRelationId(0L);

            //备注
            product.setRemark("");

            //关联报价单的商品
            product.setMaterial(null);

            // new1.0 新增字段
            product.setMaterialInfo(orderProductVO.getMaterialInfo());
            product.setBaseLaborCosts(ObjectUtil.isNotEmpty(orderProductVO.getBaseLaborCosts()) ? orderProductVO.getBaseLaborCosts() : BigDecimal.ZERO);
            product.setBaseLaborCostsPerGram(ObjectUtil.isNotEmpty(orderProductVO.getBaseLaborCosts()) ? orderProductVO.getBaseLaborCostsPerGram() : BigDecimal.ZERO);
            product.setAdditionalLaborCosts(ObjectUtil.isNotEmpty(orderProductVO.getAdditionalLaborCosts()) ? orderProductVO.getAdditionalLaborCosts() : BigDecimal.ZERO);
            product.setAdditionalLaborCostsPerGram(ObjectUtil.isNotEmpty(orderProductVO.getAdditionalLaborCostsPerGram()) ? orderProductVO.getAdditionalLaborCostsPerGram() : BigDecimal.ZERO);
            product.setPieceLaborCosts(ObjectUtil.isNotEmpty(orderProductVO.getPieceLaborCosts()) ? orderProductVO.getPieceLaborCosts() : BigDecimal.ZERO);
            product.setWarehouseId(orderProductVO.getWarehouseId());
            product.setWarehouseName(orderProductVO.getWarehouseName());
            product.setWarehouseCode(orderProductVO.getWarehouseCode());
            product.setDeliveryType(orderProductVO.getDeliveryType());
            product.setNetWeight(orderProductVO.getNetWeight());
            product.setSkuGoldWeight(orderProductVO.getSkuGoldWeight());
            product.setMaterialAmount(orderProductVO.getNetWeight().multiply(order.getSettlementGoldPrice()));
            product.setNeedCertificate(orderProductVO.getNeedCertificate());
            product.setSingleCode(orderProductVO.getSingleCode());
            product.setCommoditySingleId(orderProductVO.getCommoditySingleId());
            product.setSaleMode(orderProductVO.getSaleMode());
            product.setDeliveryPeriodMin(orderProductVO.getDeliveryPeriodMin());
            product.setDeliveryPeriodMax(orderProductVO.getDeliveryPeriodMax());
            product.setSkuCode(orderProductVO.getSkuCode());
            product.setSpuCode(orderProductVO.getSpuCode());
            product.setGoodsWeight(orderProductVO.getGoodsWeight());
            product.setFineness(orderProductVO.getFineness());
            product.setDeposit(orderProductVO.getDeposit());
            product.setSupplyMemberId(orderProductVO.getSupplyMemberId());
            product.setSupplyRoleId(orderProductVO.getSupplyRoleId());
            product.setSupplyMemberName(orderProductVO.getSupplyMemberName());
            product.setVisaPrice(orderProductVO.getVisaPrice());
            product.setWeightMin(orderProductVO.getWeightMin());
            product.setWeightMax(orderProductVO.getWeightMax());
            if (CollectionUtil.isNotEmpty(orderProductVO.getOtherLaborCostsDetails())) {
                List<FreightSpaceSingleProductExtendDTO> singleProductExtendDTOList = BeanUtil.copyToList(orderProductVO.getOtherLaborCostsDetails(), FreightSpaceSingleProductExtendDTO.class);
                product.setFreightSpaceSingleProductExtendRespList(singleProductExtendDTOList);
            }
            if (ObjectUtil.isNotEmpty(orderProductVO.getFeeDiscountDetails())) {
                product.setDiscountedBaseLaborCosts(ObjectUtil.isNotEmpty(orderProductVO.getFeeDiscountDetails().getBaseLaborCosts()) ? orderProductVO.getFeeDiscountDetails().getBaseLaborCosts() : BigDecimal.ZERO);
                product.setDiscountedAdditionalLaborCosts(ObjectUtil.isNotEmpty(orderProductVO.getFeeDiscountDetails().getAdditionalLaborCosts()) ? orderProductVO.getFeeDiscountDetails().getAdditionalLaborCosts() : BigDecimal.ZERO);
                product.setDiscountedPieceLaborCosts(ObjectUtil.isNotEmpty(orderProductVO.getFeeDiscountDetails().getPieceLaborCosts()) ? orderProductVO.getFeeDiscountDetails().getPieceLaborCosts() : BigDecimal.ZERO);
                product.setDiscountPerGram(ObjectUtil.isNotEmpty(orderProductVO.getFeeDiscountDetails().getDiscountPerGram()) ? orderProductVO.getFeeDiscountDetails().getDiscountPerGram() : BigDecimal.ZERO);
                order.setCraftDiscountAmount(order.getCraftDiscountAmount().add(product.getDiscountedBaseLaborCosts().add(product.getDiscountedAdditionalLaborCosts()).add(product.getDiscountedPieceLaborCosts())));
            }
            product.setAmount(product.getRefPrice());

//            if (ObjectUtil.isNotEmpty(goldPrice) && ObjectUtil.isNotEmpty(goldPrice.getJj())) {
//                product.setAmount(goldPrice.getJj().multiply(product.getNetWeight()));
//                // 计算商品金额，加上工费信息
//                BigDecimal otherLaborCosts = BigDecimal.ZERO;
//                if (CollectionUtil.isNotEmpty(product.getFreightSpaceSingleProductExtendRespList())) {
//                    otherLaborCosts = product.getFreightSpaceSingleProductExtendRespList().stream()
//                            .filter(ext -> StrUtil.isNotBlank(ext.getFieldValue()) && StrUtil.isNotBlank(ext.getFieldName()))
//                            .map(ext -> new BigDecimal(ext.getFieldValue()))
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//                }
//                product.setAmount(product.getAmount().add(product.getBaseLaborCosts()).add(product.getAdditionalLaborCosts()).add(product.getPieceLaborCosts()).add(otherLaborCosts));
//            }

            //关联仓位信息的商品
            if (!CollectionUtils.isEmpty(orderProductVO.getOrderProductPositionVOS())) {
                Set<OrderProductPositionDO> orderProductPositionDOS = orderProductVO.getOrderProductPositionVOS().stream().map(p -> BeanUtil.copyProperties(p, OrderProductPositionDO.class)).collect(Collectors.toSet());
                product.setOrderProductPositionDOS(orderProductPositionDOS);
                orderProductPositionDOS.forEach(orderProductPositionDO -> orderProductPositionDO.setProduct(product));
                orderProductPositionList.addAll(orderProductPositionDOS);
            }

            // new1.0 关联第三方属性信息
            if (!CollectionUtils.isEmpty(commodityExtraDataParamList) && commodityExtraDataParamList.containsKey(orderProductVO.getProductId())) {
                List<CommodityExtraDataParamResp> commodityExtraDataParams = commodityExtraDataParamList.get(orderProductVO.getProductId());
                List<CommodityExtraDataParamDO> dataParamDOList = BeanUtil.copyToList(commodityExtraDataParams, CommodityExtraDataParamDO.class);
                product.setCommodityExtraDataParams(new HashSet<>(dataParamDOList));
                dataParamDOList.forEach(param -> {
                    param.setId(null);
                    param.setCommodityExtraDataId(param.getId());
                    param.setProduct(product);
                });
                commodityExtraDataParamDOList.addAll(dataParamDOList);
            }

            product.setDelivered(BigDecimal.ZERO);
            product.setReceived(BigDecimal.ZERO);
            product.setExchangeCount(BigDecimal.ZERO);
            product.setReturnCount(BigDecimal.ZERO);
            product.setMaintainCount(BigDecimal.ZERO);
            product.setReturnAmount(BigDecimal.ZERO);
            product.setEnhanceCount(BigDecimal.ZERO);
            product.setLeftCount(product.getQuantity());
            product.setDifferCount(BigDecimal.ZERO);

            //保存营销活动
            //baseOrderPromotionService.checkProductPromotions(product, orderProductVO.getPromotions());

            productList.add(product);
            cartIds.add(product.getCartId());
        }

        // 分配订单总优惠金额到订单商品上
        // OrderDM.distributeOrderAmountToProduct(order, productList, checkResult);

        //商品总金额
        BigDecimal productAmount = productList.stream().map(OrderProductDO::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        orderProductRepository.saveAll(productList);
        orderProductRepository.flush();
        orderProductPositionRepository.saveAll(orderProductPositionList);
        commodityExtraDataParamRepository.saveAll(commodityExtraDataParamDOList);
        order.setProducts(new HashSet<>(productList));
        return new OrderProductCheckBO(productAmount, needLogistics, cartIds);
    }

    /**
     * 校验、保存App 拼团订单商品
     *
     * @param order    订单
     * @param products 订单商品列表
     * @return 订单总金额，是否需要物流
     */
    @Override
    public OrderProductCheckBO checkMobileGroupOrderProduct(OrderDO order, List<MobileOrderProductReq> products) {
        //商品总金额
        BigDecimal productAmount = BigDecimal.ZERO;
        //是否包含“物流”配送方式
        boolean needLogistics = false;

        List<OrderProductDO> productList = new ArrayList<>();
        for (MobileOrderProductReq orderProductVO : products) {
            needLogistics = needLogistics || orderProductVO.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode());

            OrderProductDO product = new OrderProductDO();
            product.setOrder(order);
            product.setProductId(orderProductVO.getProductId());
            product.setSkuId(orderProductVO.getSkuId());
            product.setStockId(NumberUtil.isNullOrZero(orderProductVO.getStockId()) ? 0L : orderProductVO.getStockId());
            product.setCartId(0L);
            product.setProductNo("");
            product.setName(orderProductVO.getName());
            product.setCategory(orderProductVO.getCategory());
            product.setBrand(StringUtils.hasLength(orderProductVO.getBrand()) ? orderProductVO.getBrand() : "");
            product.setSpec(StringUtils.hasLength(orderProductVO.getSpec()) ? orderProductVO.getSpec() : "");
            product.setUnit(orderProductVO.getUnit());
            product.setLogo(orderProductVO.getLogo());
            product.setOriginalPrice(orderProductVO.getPrice());
            //App端由于有优惠券，所以要注意区分单价、到手价
            product.setPriceType(orderProductVO.getPriceType());
            product.setPrice(orderProductVO.getPrice());
            //前端已经计算过会员折扣或拼团商品的到手价，这里直接赋值
            product.setDiscount(NumberUtil.isNullOrZero(orderProductVO.getDiscount()) ? BigDecimal.ONE : orderProductVO.getDiscount());
            product.setRefPrice(orderProductVO.getRefPrice().setScale(3, RoundingMode.HALF_UP));
            product.setStock(NumberUtil.isNullOrNegative(orderProductVO.getStock()) ? BigDecimal.ZERO : orderProductVO.getStock());
            product.setQuantity(orderProductVO.getQuantity());
            product.setTax(orderProductVO.getTax() != null && orderProductVO.getTax());
            product.setTaxRate(NumberUtil.notNullAndPositive(orderProductVO.getTaxRate()) ? orderProductVO.getTaxRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            product.setAmount(product.getRefPrice().multiply(orderProductVO.getQuantity()));
            //使用“平台优惠券”后的优惠抵扣金额
            product.setPlatformCouponAmount(BigDecimal.ZERO);
            product.setPaidAmount(BigDecimal.ZERO);
            product.setDeliverType(orderProductVO.getDeliveryType());
            product.setFreightType(NumberUtil.isNullOrNegative(orderProductVO.getFreightType()) ? OrderFreightTypeEnum.NONE.getCode() : orderProductVO.getFreightType());
            product.setWeight(NumberUtil.isNullOrNegativeZero(orderProductVO.getWeight()) ? BigDecimal.ZERO : orderProductVO.getWeight());
            product.setLogisticsTemplateId(NumberUtil.isNullOrNegativeZero(orderProductVO.getLogisticsTemplateId()) ? 0L : orderProductVO.getLogisticsTemplateId());
            product.setReceiver(StringUtils.hasLength(orderProductVO.getReceiver()) ? orderProductVO.getReceiver().trim() : "");
            product.setAddress(StringUtils.hasLength(orderProductVO.getAddress()) ? orderProductVO.getAddress().trim() : "");
            product.setPhone(StringUtils.hasLength(orderProductVO.getPhone()) ? orderProductVO.getPhone() : "");
            //转单商品标记为默认值
            product.setSeparateType(OrderSeparateTypeEnum.DEFAULT.getCode());
            product.setRelationId(0L);

            //备注
            product.setRemark("");

            //关联报价单的商品
            product.setMaterial(null);

            product.setDelivered(BigDecimal.ZERO);
            product.setReceived(BigDecimal.ZERO);
            product.setExchangeCount(BigDecimal.ZERO);
            product.setReturnCount(BigDecimal.ZERO);
            product.setMaintainCount(BigDecimal.ZERO);
            product.setReturnAmount(BigDecimal.ZERO);
            product.setEnhanceCount(BigDecimal.ZERO);
            product.setLeftCount(product.getQuantity());
            product.setDifferCount(BigDecimal.ZERO);

            //保存营销活动
            baseOrderPromotionService.checkProductPromotions(product, orderProductVO.getPromotions());

            productList.add(product);

            productAmount = productAmount.add(product.getAmount());
        }

        orderProductRepository.saveAll(productList);
        order.setProducts(new HashSet<>(productList));
        return new OrderProductCheckBO(productAmount, needLogistics, new ArrayList<>());
    }

    /**
     * 校验Srm订单商品接口参数
     *
     * @param order           订单
     * @param products        订单物料列表
     * @param isCreate        是否新增，true表示新增， false表示修改
     * @param isAddLineNumber 是否为物料信息自增行号
     * @return 订单总金融
     */
    @Override
    public OrderProductCheckBO checkSrmOrderProduct(OrderDO order, List<SrmOrderProductReq> products, boolean isCreate, boolean isAddLineNumber) {
        //商品总金额
        BigDecimal productAmount = BigDecimal.ZERO;
        //是否包含“物流”配送方式
        boolean needLogistics = false;
        //处理物料行号
        if (isAddLineNumber) {
            // 修改订单
            if (!isCreate) {
                Set<OrderProductDO> oldProduct = order.getProducts();
                Long theMaxLineNumber = oldProduct.stream().sorted(
                        Comparator.comparing(OrderProductDO::getLineNumber).reversed()).collect(Collectors.toList()).get(0).getLineNumber();
                AtomicInteger lineNumber = new AtomicInteger(theMaxLineNumber.intValue());
                products.stream().filter(p -> Objects.isNull(p.getLineNumber())).forEach(v -> {
                    lineNumber.getAndIncrement();// 先自增
                    v.setLineNumber(lineNumber.longValue());
                });
            } else {
                AtomicInteger lineNumber = new AtomicInteger(1);
                products.forEach(v -> {
                    v.setLineNumber(lineNumber.longValue());
                    lineNumber.getAndIncrement();//自增
                });
            }
        }
        //获取订单物料采购物料id信息(k:productId,v:可用数量)
//         Map<Long,String> productMap = getRequisitionProduct(order);

        List<OrderProductDO> productList = new ArrayList<>();
        for (SrmOrderProductReq orderProductVO : products) {
            // 检验订单物料采购数量是否小于采购物料可用数量
//            if (!StringUtils.hasLength(productMap.get(orderProductVO.getProductId())) || new BigDecimal(orderProductVO.getProductId()).compareTo(orderProductVO.getQuantity()) < 0){
//                return Wrapper.fail(ResponseCode.ORDER_PURCHASE_ORDER_ITEM_QUANTITY_CANNOT_BE_GREATER_THAN_PURCHASE_REQUISITION_MATERIAL_QUANTITY);
//            }
            if (orderProductVO.getDeliveryType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode())) {
                if (!StringUtils.hasText(orderProductVO.getReceiver())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_IS_MISSING);
                }

                if (!StringUtils.hasText(orderProductVO.getPhone())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_PHONE_IS_MISSING);
                }

                if (!StringUtils.hasText(orderProductVO.getAddress())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_RECEIVER_ADDRESS_IS_MISSING);
                }
            }

            //以SkuId为判断条件，校验关联商品
            if (NumberUtil.notNullAndPositive(orderProductVO.getQuotedSkuId())) {
                if (!StringUtils.hasLength(orderProductVO.getQuotedName())) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_QUOTED_PRODUCT_NAME_CAN_NOT_BE_EMPTY);
                }
            }

            needLogistics = needLogistics || orderProductVO.getDeliveryType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode());

            OrderProductDO product = new OrderProductDO();
            //兼容订单变更
            if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
                OrderProductDO orderProductDO = orderProductRepository.findFirstByOrderAndProductNo(order, orderProductVO.getProductNo());
                if (!Objects.isNull(orderProductDO)) {
                    product = orderProductDO;
                }
            }
            product.setOrder(order);
            product.setProductId(orderProductVO.getProductId());
            product.setLineNumber(orderProductVO.getLineNumber());
            product.setSkuId(0L);
            product.setStockId(0L);
            product.setCartId(0L);
            product.setProductNo(orderProductVO.getProductNo());
            product.setName(orderProductVO.getName());
            product.setCategory(orderProductVO.getCategory());
            product.setBrand(StringUtils.hasLength(orderProductVO.getBrand()) ? orderProductVO.getBrand() : "");
            product.setSpec(StringUtils.hasLength(orderProductVO.getSpec()) ? orderProductVO.getSpec() : "");
            product.setUnit(orderProductVO.getUnit());
            //承诺交易、期望交期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            if (StringUtils.hasText(orderProductVO.getExpectedDelivery())) {
                product.setExpectedDeliveryDate(LocalDate.parse(orderProductVO.getExpectedDelivery(), formatter).atStartOfDay());
            }
            if (StringUtils.hasText(orderProductVO.getPromisedDeliveryDate())) {
                product.setPromisedDeliveryDate(LocalDate.parse(orderProductVO.getPromisedDeliveryDate(), formatter).atStartOfDay());
            }
            //物料没有Logo
            product.setLogo("");
            product.setOriginalPrice(orderProductVO.getPrice());
            //兼容新增和变更待送货数量
            if (product.getQuantity() != null && product.getLeftCount() != null) {
                //采购量差值
                BigDecimal differenceValue = product.getQuantity().subtract(orderProductVO.getQuantity());
                product.setLeftCount(product.getLeftCount().subtract(differenceValue));
            } else {
                product.setLeftCount(orderProductVO.getQuantity());
            }
            //SRM订单：商品没有价格类型，商品单价即到手价
            product.setPriceType(0);
            product.setPrice(orderProductVO.getPrice());
            product.setRefPrice(orderProductVO.getPrice());
            product.setStock(NumberUtil.isNullOrNegative(orderProductVO.getStock()) ? BigDecimal.ZERO : orderProductVO.getStock());
            product.setDiscount(BigDecimal.ONE);
            product.setQuantity(orderProductVO.getQuantity());
            product.setTax(orderProductVO.getTax() != null && orderProductVO.getTax());
            product.setTaxRate(NumberUtil.notNullAndPositive(orderProductVO.getTaxRate()) ? orderProductVO.getTaxRate().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            product.setAmount(product.getRefPrice().multiply(orderProductVO.getQuantity()));
            //使用“平台优惠券”后的优惠抵扣金额
            product.setPlatformCouponAmount(BigDecimal.ZERO);
            product.setPaidAmount(BigDecimal.ZERO);
            product.setDeliverType(orderProductVO.getDeliveryType());
            product.setFreightType(OrderFreightTypeEnum.NONE.getCode());
            product.setWeight(NumberUtil.isNullOrNegativeZero(orderProductVO.getWeight()) ? BigDecimal.ZERO : orderProductVO.getWeight());
            product.setLogisticsTemplateId(NumberUtil.isNullOrNegativeZero(orderProductVO.getLogisticsTemplateId()) ? 0L : orderProductVO.getLogisticsTemplateId());
            product.setReceiver(StringUtils.hasLength(orderProductVO.getReceiver()) ? orderProductVO.getReceiver().trim() : "");
            product.setAddress(StringUtils.hasLength(orderProductVO.getAddress()) ? orderProductVO.getAddress().trim() : "");
            product.setPhone(StringUtils.hasLength(orderProductVO.getPhone()) ? orderProductVO.getPhone() : "");
            //转单商品标记为默认值
            product.setSeparateType(OrderSeparateTypeEnum.DEFAULT.getCode());
            product.setRelationId(0L);
            //备注
            product.setRemark(StringUtils.hasLength(orderProductVO.getRemark()) ? orderProductVO.getRemark() : "");

            //关联报价单的物料
            if (NumberUtil.isNullOrZero(orderProductVO.getQuotedSkuId()) && CollUtil.isEmpty(orderProductVO.getRequisitions())) {
                product.setMaterial(null);
            } else {
                OrderMaterialDO material = new OrderMaterialDO();
                //兼容订单变更
                if (CommonBooleanEnum.YES.getCode().equals(order.getChangeProcessStatus())) {
                    if (!Objects.isNull(product.getMaterial())) {
                        material = product.getMaterial();
                    }
                }
                material.setOrderProduct(product);
                material.setQuotedId(0L);
                material.setProductId(NumberUtil.isNullOrZero(orderProductVO.getQuotedProductId()) ? 0L : orderProductVO.getQuotedProductId());
                material.setProductNo("");
                material.setSkuId(NumberUtil.isNullOrZero(orderProductVO.getQuotedSkuId()) ? 0L : orderProductVO.getQuotedSkuId());
                material.setName(StringUtils.hasLength(orderProductVO.getQuotedName()) ? orderProductVO.getQuotedName() : "");
                material.setSpec(StringUtils.hasLength(orderProductVO.getQuotedSpec()) ? orderProductVO.getQuotedSpec() : "");
                material.setCategory(StringUtils.hasLength(orderProductVO.getQuotedCategory()) ? orderProductVO.getQuotedCategory() : "");
                material.setBrand(StringUtils.hasLength(orderProductVO.getQuotedBrand()) ? orderProductVO.getQuotedBrand() : "");
                material.setStock(BigDecimal.ZERO);
                //关联请购单信息
                material.setRequisitions(orderProductVO.getRequisitions().stream().map(v -> new RequisitionBO(v.getRequisitionId(), v.getRequisitionProductId(), v.getOrderQuantity(), orderProductVO.getPrice())).collect(Collectors.toList()));
                //关联
                product.setMaterial(material);
            }

            //营销活动
            product.setPromotions(new HashSet<>());
            product.setDelivered(product.getDelivered() == null ? BigDecimal.ZERO : product.getDelivered());
            product.setReceived(product.getReceived() == null ? BigDecimal.ZERO : product.getReceived());
            product.setExchangeCount(product.getExchangeCount() == null ? BigDecimal.ZERO : product.getExchangeCount());
            product.setReturnCount(product.getReturnCount() == null ? BigDecimal.ZERO : product.getReturnCount());
            product.setMaintainCount(product.getMaintainCount() == null ? BigDecimal.ZERO : product.getMaintainCount());
            product.setReturnAmount(BigDecimal.ZERO);
            product.setEnhanceCount(product.getEnhanceCount() == null ? BigDecimal.ZERO : product.getEnhanceCount());
            product.setDifferCount(product.getDifferCount() == null ? BigDecimal.ZERO : product.getDifferCount());

            productList.add(product);
            productAmount = productAmount.add(product.getAmount());
        }

        //如果是修改，删除原有数据
        if (!isCreate) {
            orderProductRepository.deleteByOrder(order);
        }

        orderProductRepository.saveAll(productList);

        order.setProducts(new HashSet<>(productList));
        return new OrderProductCheckBO(productAmount, needLogistics, new ArrayList<>());
    }

    /**
     * （供应商）修改订单商品单价
     *
     * @param order  订单
     * @param prices 接口参数
     * @return 修改结果
     */
    @Override
    public UpdateOrderProductPriceBO vendorUpdateProductPrices(OrderDO order, List<VendorUpdateProductPriceReq> prices) {
        List<OrderProductDO> orderProducts = orderProductRepository.findByOrder(order);
        if (CollectionUtils.isEmpty(orderProducts) || prices.stream().anyMatch(price -> orderProducts.stream().noneMatch(orderProduct -> orderProduct.getId().equals(price.getOrderProductId())))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }
        //商品修改信息用于记录在外部订单流转记录
        List<String> remarks = new ArrayList<>();

        List<OrderProductDO> updateProducts = orderProducts.stream().filter(orderProduct -> prices.stream().anyMatch(price -> price.getOrderProductId().equals(orderProduct.getId()))).peek(orderProduct -> prices.stream().filter(price -> price.getOrderProductId().equals(orderProduct.getId())).findFirst().ifPresent(price -> {
            //拼接商品名称+商品ID+修改前单价+修改后单价+修改原因
            String remark = orderProduct.getName() + "（商品ID：" + orderProduct.getProductId() + "）： 从￥" + NumberUtil.formatAmount(orderProduct.getRefPrice()) + "修改为￥" + NumberUtil.formatAmount(price.getPrice()) + "；原因为：" + price.getReason() + "；";
            remarks.add(remark);
            //修改到手价与商品总金额
            orderProduct.setRefPrice(price.getPrice());
            orderProduct.setAmount(orderProduct.getRefPrice().multiply(orderProduct.getQuantity()));
        })).collect(Collectors.toList());

        orderProductRepository.saveAll(updateProducts);

        return new UpdateOrderProductPriceBO(orderProducts.stream().map(OrderProductDO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add), remarks);
    }

    /**
     * 转单 - 订单商品
     *
     * @param order                 转单后的订单
     * @param separateOrderProducts 原订单商品列表
     * @param supplyProducts        上游供应商商品列表
     * @return 订单总金额，是否需要物流
     */
    @Override
    public OrderProductCheckBO transferOrderProduct(OrderDO order, List<OrderProductDO> separateOrderProducts, List<SupplyProductDTO> supplyProducts) {
        //商品总金额
        BigDecimal productAmount = BigDecimal.ZERO;
        //是否包含“物流”配送方式
        boolean needLogistics = false;

        List<OrderProductDO> productList = new ArrayList<>();
        for (OrderProductDO separateProduct : separateOrderProducts) {
            SupplyProductDTO supplyProduct = supplyProducts.stream().filter(product -> product.getVendorSkuId().equals(separateProduct.getSkuId())).findFirst().orElse(null);
            if (supplyProduct == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_PRODUCT_DOES_NOT_EXIST);
            }

            needLogistics = needLogistics || supplyProduct.getDeliverType().equals(OrderProductDeliverTypeEnum.LOGISTICS.getCode());

            OrderProductDO product = new OrderProductDO();
            product.setOrder(order);
            product.setProductId(supplyProduct.getProductId());
            product.setSkuId(supplyProduct.getSkuId());
            product.setStockId(0L);
            product.setCartId(0L);
            product.setProductNo(supplyProduct.getProductNo());
            product.setName(supplyProduct.getName());
            product.setCategory(supplyProduct.getCategory());
            product.setBrand(supplyProduct.getBrand());
            product.setSpec(supplyProduct.getSpec());
            product.setUnit(supplyProduct.getUnit());
            product.setLogo(supplyProduct.getLogo());
            product.setOriginalPrice(supplyProduct.getPrice());
            //转单后的订单：商品单价即到手价
            product.setPriceType(supplyProduct.getPriceType());
            product.setPrice(supplyProduct.getPrice());
            product.setDiscount(supplyProduct.getDiscount());
            product.setRefPrice(separateProduct.getRefPrice());
            product.setStock(supplyProduct.getStock());
            product.setQuantity(separateProduct.getQuantity());
            product.setTax(supplyProduct.getTax());
            product.setTaxRate(supplyProduct.getTaxRate());
            product.setAmount(product.getRefPrice().multiply(separateProduct.getQuantity()));
            //使用“平台优惠券”后的优惠抵扣金额
            product.setPlatformCouponAmount(BigDecimal.ZERO);
            product.setPaidAmount(BigDecimal.ZERO);
            product.setDeliverType(supplyProduct.getDeliverType());
            product.setFreightType(supplyProduct.getFreightType());
            product.setWeight(supplyProduct.getWeight());
            product.setLogisticsTemplateId(supplyProduct.getLogisticsTemplateId());
            product.setReceiver(supplyProduct.getReceiver());
            product.setAddress(supplyProduct.getAddress());
            product.setPhone(supplyProduct.getPhone());

            //转单商品标记
            product.setSeparateType(OrderSeparateTypeEnum.TRANSFERRED.getCode());
            product.setRelationId(separateProduct.getId());

            // new1.0 新增字段
            product.setMaterialInfo(separateProduct.getMaterialInfo());
            product.setBaseLaborCosts(separateProduct.getBaseLaborCosts());
            product.setBaseLaborCostsPerGram(separateProduct.getBaseLaborCostsPerGram());
            product.setAdditionalLaborCosts(separateProduct.getAdditionalLaborCosts());
            product.setPieceLaborCosts(separateProduct.getPieceLaborCosts());
            product.setWarehouseId(separateProduct.getWarehouseId());
            product.setWarehouseName(separateProduct.getWarehouseName());
            product.setDeliveryType(separateProduct.getDeliveryType());
            product.setNetWeight(separateProduct.getNetWeight());
            product.setSkuGoldWeight(separateProduct.getSkuGoldWeight());
            product.setMaterialAmount(separateProduct.getNetWeight().multiply(order.getSettlementGoldPrice()));
            product.setNeedCertificate(separateProduct.getNeedCertificate());
            product.setSingleCode(separateProduct.getSingleCode());
            product.setCommoditySingleId(separateProduct.getCommoditySingleId());
            product.setSaleMode(separateProduct.getSaleMode());
            product.setDeliveryPeriodMin(separateProduct.getDeliveryPeriodMin());
            product.setDeliveryPeriodMax(separateProduct.getDeliveryPeriodMax());
            product.setSkuCode(separateProduct.getSkuCode());
            product.setSpuCode(separateProduct.getSpuCode());
            product.setGoodsWeight(separateProduct.getGoodsWeight());
            product.setFineness(separateProduct.getFineness());
            product.setDeposit(separateProduct.getDeposit());
            product.setSupplyMemberId(separateProduct.getSupplyMemberId());
            product.setSupplyRoleId(separateProduct.getSupplyRoleId());
            product.setSupplyMemberName(separateProduct.getSupplyMemberName());
            product.setVisaPrice(separateProduct.getVisaPrice());
            if (CollectionUtil.isNotEmpty(separateProduct.getFreightSpaceSingleProductExtendRespList())) {
                List<FreightSpaceSingleProductExtendDTO> singleProductExtendDTOList = BeanUtil.copyToList(separateProduct.getFreightSpaceSingleProductExtendRespList(), FreightSpaceSingleProductExtendDTO.class);
                product.setFreightSpaceSingleProductExtendRespList(singleProductExtendDTOList);
            }
            product.setDiscountedBaseLaborCosts(ObjectUtil.isNotEmpty(separateProduct.getDiscountedBaseLaborCosts()) ? separateProduct.getDiscountedBaseLaborCosts() : BigDecimal.ZERO);
            product.setDiscountedAdditionalLaborCosts(ObjectUtil.isNotEmpty(separateProduct.getDiscountedAdditionalLaborCosts()) ? separateProduct.getDiscountedAdditionalLaborCosts() : BigDecimal.ZERO);
            product.setDiscountedPieceLaborCosts(ObjectUtil.isNotEmpty(separateProduct.getDiscountedPieceLaborCosts()) ? separateProduct.getDiscountedPieceLaborCosts() : BigDecimal.ZERO);
            order.setCraftDiscountAmount(order.getCraftDiscountAmount().add(product.getDiscountedBaseLaborCosts().add(product.getDiscountedAdditionalLaborCosts()).add(product.getDiscountedPieceLaborCosts())));
            product.setAmount(product.getRefPrice());

            //备注
            product.setRemark("");

            //转单商品没有关联报价单的物料
            product.setMaterial(null);
            //营销活动
            product.setPromotions(new HashSet<>());

            product.setDelivered(BigDecimal.ZERO);
            product.setReceived(BigDecimal.ZERO);
            product.setExchangeCount(BigDecimal.ZERO);
            product.setReturnCount(BigDecimal.ZERO);
            product.setMaintainCount(BigDecimal.ZERO);
            product.setReturnAmount(BigDecimal.ZERO);
            product.setEnhanceCount(BigDecimal.ZERO);
            product.setLeftCount(product.getQuantity());
            product.setDifferCount(BigDecimal.ZERO);

            productList.add(product);
            productAmount = productAmount.add(product.getAmount());
        }

        orderProductRepository.saveAll(productList);

        //设置转单前的订单的标记
        separateOrderProducts.forEach(separateProduct -> {
            separateProduct.setSeparateType(OrderSeparateTypeEnum.SEPARATED.getCode());
            productList.stream().filter(product -> product.getRelationId().equals(separateProduct.getId())).findFirst().ifPresent(product -> separateProduct.setRelationId(product.getId()));
        });

        orderProductRepository.saveAll(separateOrderProducts);

        order.setProducts(new HashSet<>(productList));
        return new OrderProductCheckBO(productAmount, needLogistics, new ArrayList<>());
    }

    /**
     * 订单商品汇总信息
     *
     * @param order 订单
     * @return 订单商品汇总
     */
    @Override
    public OrderProductSummaryBO summaryOrderProducts(OrderDO order) {
        OrderProductSummaryBO summaryBO = new OrderProductSummaryBO();

        OrderProductSummaryResp summaryVO = new OrderProductSummaryResp();
        //运费
        summaryVO.setFreight(NumberUtil.formatAmount(order.getFreight()));
        //促销立减
        summaryVO.setPromotionAmount(NumberUtil.formatAmount(order.getPromotionAmount()));
        //优惠（券）立减
        summaryVO.setCouponAmount(NumberUtil.formatAmount(order.getCouponAmount()));
        //积分抵扣总金额
        summaryVO.setDeductionAmount(NumberUtil.formatAmount(order.getDeductionAmount()));
        //税费
        summaryVO.setTaxes(NumberUtil.formatAmount(order.getTaxes()));
        //订单总金额
        summaryVO.setTotalAmount(NumberUtil.formatAmount(order.getTotalAmount()));

        //是否全部发货完毕
        AtomicBoolean deliveryDone = new AtomicBoolean(true);
        //是否全部收货完毕
        AtomicBoolean receiveDone = new AtomicBoolean(true);

        //如果是物料订单, 按照行号进行排序
        Sort sort = OrderTypeEnum.MANUAL_MATERIAL_ORDER_PLACEMENT.getCode().equals(order.getOrderType()) || OrderTypeEnum.MATERIAL_SAMPLE_ORDER.getCode().equals(order.getOrderType()) ? Sort.by("lineNumber").ascending() : Sort.by("id").ascending();

        AtomicReference<BigDecimal> productAmount = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> memberDiscount = new AtomicReference<>(BigDecimal.ZERO);

        GoldPriceResp goldPrice = eosApiService.getGoldPrice();

        summaryVO.setCouponAmount(NumberUtil.formatAmount(order.getCraftDiscountAmount()));

        List<OrderProductDO> orderProducts = orderProductRepository.findByOrder(order, sort);
        summaryVO.setProducts(orderProducts.stream().map(product -> {
            OrderProductDetailResp detailVO = new OrderProductDetailResp();
            detailVO.setOrderProductId(product.getId());
            detailVO.setProductId(product.getProductId());
            detailVO.setSkuId(product.getSkuId());
            detailVO.setCommoditySingleId(product.getCommoditySingleId());
            detailVO.setSingleCode(product.getSingleCode());
            detailVO.setStockId(product.getStockId());
            detailVO.setPriceType(product.getPriceType());
            detailVO.setProductNo(StringUtils.hasLength(product.getProductNo()) ? product.getProductNo() : "");
            detailVO.setName(product.getName());
            detailVO.setLogo(product.getLogo());
            if (CollUtil.isNotEmpty(order.getSealsSet())) {
                detailVO.setSealsUrl(order.getSealsSet().stream().findFirst().get().getSealEffectImage());
                detailVO.setSealContent(order.getSealsSet().stream().findFirst().get().getSealContent());
            }
            if (ObjectUtil.isNotEmpty(order.getCertificate())) {
                detailVO.setCertificateName(order.getCertificate().getName());
            }
            detailVO.setCategory(product.getCategory());
            detailVO.setBrand(product.getBrand());
            detailVO.setUnit(product.getUnit());
            detailVO.setSpec(StringUtils.hasLength(product.getSpec()) ? product.getSpec() : "");
            product.setBaseLaborCosts(ObjectUtil.isEmpty(product.getBaseLaborCosts()) ? BigDecimal.ZERO : product.getBaseLaborCosts());
            product.setAdditionalLaborCosts(ObjectUtil.isEmpty(product.getAdditionalLaborCosts()) ? BigDecimal.ZERO : product.getAdditionalLaborCosts());
            product.setPieceLaborCosts(ObjectUtil.isEmpty(product.getPieceLaborCosts()) ? BigDecimal.ZERO : product.getPieceLaborCosts());
            detailVO.setPrice(NumberUtil.formatToInteger(product.getPrice().add(product.getBaseLaborCosts()).add(product.getAdditionalLaborCosts()).add(product.getPieceLaborCosts())));
            detailVO.setRefPrice(NumberUtil.formatToInteger(product.getRefPrice()));
            if (order.getTotalAmount().compareTo(order.getPaidAmount()) == 0 && ObjectUtil.isNotEmpty(product.getNetWeight())) {
                BigDecimal netWeightPrice = product.getNetWeight().multiply(goldPrice.getJj()).setScale(2, RoundingMode.HALF_UP);
                detailVO.setPrice(NumberUtil.formatToInteger(netWeightPrice.add(product.getBaseLaborCosts()).add(product.getAdditionalLaborCosts()).add(product.getPieceLaborCosts())));
                detailVO.setRefPrice(NumberUtil.formatToInteger(product.getRefPrice().subtract(product.getMaterialAmount()).add(netWeightPrice)));
            }
            detailVO.setDiscount(NumberUtil.formatRoundHalfUp(product.getDiscount()));
            detailVO.setQuantity(NumberUtil.formatToInteger(product.getQuantity()));
            detailVO.setTax(product.getTax());
            detailVO.setTaxRate(NumberUtil.formatRoundHalfUp(product.getTaxRate()));
            detailVO.setAmount(NumberUtil.formatAmount(product.getAmount()));
            detailVO.setStock(NumberUtil.formatToInteger(product.getStock()));
            detailVO.setDeliverType(product.getDeliverType());
            detailVO.setDeliverTypeName(OrderProductDeliverTypeEnum.NO_DELIVERY.getCode().equals(product.getDeliverType()) ? "预估" : OrderProductDeliverTypeEnum.getNameByCode(product.getDeliverType()));
            detailVO.setWeight(product.getWeight());
            detailVO.setLogisticsTemplateId(product.getLogisticsTemplateId());
            detailVO.setReceiver(product.getReceiver());
            detailVO.setAddress(product.getAddress());
            detailVO.setPhone(product.getPhone());
            detailVO.setRemark(product.getRemark());
            detailVO.setSpuCode(product.getSpuCode());
            detailVO.setMaterialInfo(product.getMaterialInfo());
            detailVO.setFineness(product.getFineness());
            detailVO.setNetWeight(product.getNetWeight());
            detailVO.setGoodsWeight(product.getGoodsWeight());
            detailVO.setBaseLaborCosts(product.getBaseLaborCosts());
            detailVO.setAdditionalLaborCosts(product.getAdditionalLaborCosts());
            detailVO.setPieceLaborCosts(product.getPieceLaborCosts());
            detailVO.setGrossWeight(product.getNetWeight());
            detailVO.setWarehouse(product.getWarehouseName());
            detailVO.setSupplyMemberId(product.getSupplyMemberId());
            detailVO.setSupplyRoleId(product.getSupplyRoleId());
            detailVO.setSupplyMemberName(product.getSupplyMemberName());
            detailVO.setDelivered(NumberUtil.formatToInteger(product.getDelivered()));
            detailVO.setReceived(NumberUtil.formatToInteger(product.getReceived()));
            detailVO.setLeftCount(NumberUtil.formatToInteger(product.getLeftCount()));
            detailVO.setDifferCount(NumberUtil.formatToInteger(product.getDifferCount()));
            detailVO.setDiscountedBaseLaborCosts(product.getDiscountedBaseLaborCosts());
            detailVO.setDiscountedAdditionalLaborCosts(product.getDiscountedAdditionalLaborCosts());
            detailVO.setDiscountedPieceLaborCosts(product.getDiscountedPieceLaborCosts());
            detailVO.setFreightSpaceSingleProductExtendRespList(product.getFreightSpaceSingleProductExtendRespList());

            // new 1.0 新增字段
            detailVO.setTotalGoldWeight(product.getGoodsWeight());
            if (ObjectUtil.isNotEmpty(product.getBaseLaborCostsPerGram()) && BigDecimal.ZERO.compareTo(product.getBaseLaborCostsPerGram()) != 0) {
                detailVO.setBaseLaborCostsPerGram(product.getBaseLaborCostsPerGram());
                detailVO.setDiscountedBaseLaborCostsPerGram(ObjectUtil.isNotEmpty(product.getDiscountedBaseLaborCosts()) && BigDecimal.ZERO.compareTo(product.getDiscountedBaseLaborCosts()) != 0 ? product.getDiscountedBaseLaborCosts().divide(product.getNetWeight(), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                detailVO.setAdditionalLaborCostsPerGram(detailVO.getBaseLaborCostsPerGram().subtract(detailVO.getDiscountedBaseLaborCostsPerGram()));
            }

            if (ObjectUtil.isNotEmpty(product.getAdditionalLaborCostsPerGram()) && BigDecimal.ZERO.compareTo(product.getAdditionalLaborCostsPerGram()) != 0) {
                detailVO.setDiscountedAdditionalLaborCostsPerGram(product.getAdditionalLaborCostsPerGram());
                detailVO.setAdditionalLaborCostsDiscountPerGram(ObjectUtil.isNotEmpty(product.getAdditionalLaborCosts()) && BigDecimal.ZERO.compareTo(product.getAdditionalLaborCosts()) != 0 ? product.getAdditionalLaborCosts().divide(product.getNetWeight(), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                detailVO.setAdditionalLaborCostsDiscountRate(BigDecimal.ZERO);
                detailVO.setAdditionalLaborCostsPerGramWithDiscount(detailVO.getDiscountedAdditionalLaborCostsPerGram().subtract(detailVO.getAdditionalLaborCostsPerGram()));
            }

            if (ObjectUtil.isNotEmpty(product.getPieceLaborCosts()) && BigDecimal.ZERO.compareTo(product.getPieceLaborCosts()) != 0) {
                detailVO.setPieceLaborCostsPerPiece(product.getPieceLaborCosts());
                detailVO.setPieceLaborCostsDiscountPerPiece(product.getDiscountedPieceLaborCosts());
                detailVO.setPieceLaborCostsPerPieceWithDiscount(product.getPieceLaborCosts().subtract(product.getDiscountedPieceLaborCosts()));
            }

            detailVO.setTotalLaborCosts(detailVO.getAdditionalLaborCostsPerGram().add(detailVO.getAdditionalLaborCostsPerGramWithDiscount()).add(detailVO.getPieceLaborCostsPerPieceWithDiscount()));
            detailVO.setTotalMaterialAmount(product.getGoodsWeight());
            detailVO.setOrderWarehouse(product.getWarehouseName());

            //商品总金额 = Σ(原价 * 数量)
            productAmount.set(productAmount.get().add(product.getPrice().multiply(product.getQuantity())));
            //会员折扣总金额 = Σ[原价 * 数量 * (1-会员折扣)]
            memberDiscount.set(memberDiscount.get().add(product.getPrice().multiply(product.getQuantity()).multiply(BigDecimal.ONE.subtract(product.getDiscount()))));

            //行号
            detailVO.setLineNumber(product.getLineNumber());
            //承诺交易、期望交期
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            detailVO.setExpectedDelivery(product.getExpectedDeliveryDate() == null ? "" : product.getExpectedDeliveryDate().format(formatter));
            detailVO.setPromisedDeliveryDate(product.getPromisedDeliveryDate() == null ? "" : product.getPromisedDeliveryDate().format(formatter));
            //以收货、发货数量大于等于采购数量为条件，判断是否收货完毕、发货完毕
            deliveryDone.set(deliveryDone.get() && (product.getDelivered().compareTo(product.getQuantity()) >= 0));
            receiveDone.set(receiveDone.get() && (product.getReceived().compareTo(product.getQuantity()) >= 0));
            if (product.getMaterial() != null) {
                detailVO.setQuotedId(product.getMaterial().getQuotedId());
                detailVO.setQuotedProductId(product.getMaterial().getProductId());
                detailVO.setQuotedSkuId(product.getMaterial().getSkuId());
                detailVO.setQuotedProductNo(product.getMaterial().getProductNo());
                detailVO.setQuotedName(product.getMaterial().getName());
                detailVO.setQuotedSpec(product.getMaterial().getSpec());
                detailVO.setQuotedCategory(product.getMaterial().getCategory());
                detailVO.setQuotedBrand(product.getMaterial().getBrand());
                detailVO.setQuotedStock(NumberUtil.formatToInteger(product.getMaterial().getStock()));
                //关联请购单
                detailVO.setRequisitions(CollUtil.isEmpty(product.getMaterial().getRequisitions())
                        ? Collections.emptyList()
                        : product.getMaterial().getRequisitions().stream().map(r -> new OrderRequisitionResp(r.getRequisitionId(), r.getOrderQuantity())).collect(Collectors.toList()));
            }
            //保存商品仓位信息
            if (!CollectionUtils.isEmpty(product.getOrderProductPositionDOS())) {
                List<OrderProductPositionResp> orderProductPositionResp = new ArrayList<>();
                product.getOrderProductPositionDOS().forEach(p -> {
                    OrderProductPositionResp positionVO = new OrderProductPositionResp();
                    positionVO.setPositionId(p.getPositionId());
                    positionVO.setPositionName(p.getPositionName());
                    positionVO.setPositionQuantity(p.getPositionQuantity());
                    positionVO.setWarehouseId(p.getWarehouseId());
                    orderProductPositionResp.add(positionVO);
                });
                detailVO.setOrderProductPositionVOS(orderProductPositionResp);
            }
            return detailVO;
        }).collect(Collectors.toList()));

        //商品总金额 = Σ(原价 * 数量)
        summaryVO.setProductAmount(NumberUtil.formatAmount(productAmount.get()));
        //会员折扣总金额 = Σ[原价 * 数量 * (1-会员折扣)]
        summaryVO.setMemberDiscount(NumberUtil.formatAmount(memberDiscount.get()));

        summaryBO.setDeliveryDone(deliveryDone.get());
        summaryBO.setReceiveDone(receiveDone.get());
        summaryBO.setProductSummary(summaryVO);
        // 百泰新增字段
        summaryVO.setTotalProductCount(order.getTotalProductCount());
        summaryVO.setTotalWeight(order.getTotalWeight());
        summaryVO.setSettlementGoldPrice(order.getSettlementGoldPrice());
        summaryVO.setTotalCraftPrice(order.getTotalCraftPrice());
        summaryVO.setTotalServicePrice(order.getTotalServicePrice());
        summaryVO.setActualPaidAmount(order.getPaidAmount());
        summaryVO.setSettlementWeight(order.getTotalWeight());
        summaryVO.setPackingFee(order.getPackingFee());
        if (ObjectUtil.isNotEmpty(order.getSfInsuranceFee()) && ObjectUtil.isNotEmpty(order.getPacInsuranceFee())) {
            summaryVO.setInsuranceFee(order.getSfInsuranceFee().add(order.getPacInsuranceFee()));
        }
        summaryVO.setEngravingFee(BigDecimal.ZERO);
        summaryVO.setCertificateFee(order.getCertificateFee());
        summaryVO.setOtherFee(BigDecimal.ZERO);
        summaryVO.setOrderDiscountAmount(order.getCraftDiscountAmount());
        if (ObjectUtil.isNotEmpty(summaryVO.getTotalCraftPrice()) && ObjectUtil.isNotEmpty(summaryVO.getOrderDiscountAmount())) {
            summaryVO.setOrderTotalAmount(summaryVO.getTotalCraftPrice().subtract(summaryVO.getOrderDiscountAmount()));
        }

        summaryBO.setDeliverySummaries(orderProducts.stream().map(product -> {
            OrderDeliverySummaryResp detailVO = new OrderDeliverySummaryResp();
            detailVO.setProductId(product.getProductId());
            detailVO.setSkuId(product.getSkuId());
            detailVO.setProductNo(product.getProductNo());
            detailVO.setName(product.getName());
            detailVO.setCategory(product.getCategory());
            detailVO.setBrand(product.getBrand());
            detailVO.setUnit(product.getUnit());
            detailVO.setSpec(product.getSpec());
            detailVO.setPrice(NumberUtil.formatToInteger(product.getRefPrice()));
            detailVO.setDiscount(NumberUtil.formatRoundHalfUp(product.getDiscount()));
            detailVO.setQuantity(NumberUtil.formatToInteger(product.getQuantity()));
            detailVO.setTax(product.getTax());
            detailVO.setTaxRate(NumberUtil.formatRoundHalfUp(product.getTaxRate()));
            detailVO.setQuotedId(product.getMaterial() == null ? 0L : product.getMaterial().getQuotedId());
            detailVO.setQuotedProductId(product.getMaterial() == null ? 0L : product.getMaterial().getProductId());
            detailVO.setQuotedSkuId(product.getMaterial() == null ? 0L : product.getMaterial().getSkuId());
            detailVO.setQuotedProductNo(product.getMaterial() == null ? "" : product.getMaterial().getProductNo());
            detailVO.setQuotedName(product.getMaterial() == null ? "" : (StringUtils.hasLength(product.getMaterial().getSpec()) ? product.getMaterial().getName().concat("/").concat(product.getMaterial().getSpec()) : product.getMaterial().getName()));
            detailVO.setQuotedCategory(product.getMaterial() == null ? "" : product.getMaterial().getCategory());
            detailVO.setQuotedBrand(product.getMaterial() == null ? "" : product.getMaterial().getBrand());
            detailVO.setQuotedSpec(product.getMaterial() == null ? "" : product.getMaterial().getSpec());
            detailVO.setAmount(NumberUtil.formatAmount(product.getAmount()));
            detailVO.setDelivered(NumberUtil.formatToInteger(product.getDelivered()));
            detailVO.setReceived(NumberUtil.formatToInteger(product.getReceived()));
            detailVO.setLeftCount(NumberUtil.formatToInteger(product.getLeftCount()));
            detailVO.setDifferCount(NumberUtil.formatToInteger(product.getDifferCount()));
            BigDecimal acceptanceCount = BigDecimal.ZERO;
            BigDecimal concessionToReceiveCount = BigDecimal.ZERO;
            BigDecimal rejectCount = BigDecimal.ZERO;
            List<OrderDeliveryProductDO> orderDeliveryProducts = orderDeliveryProductRepository.findByOrderProductId(product.getId());
            for (OrderDeliveryProductDO p : orderDeliveryProducts) {
                if (p.getAcceptanceCount() != null) {
                    acceptanceCount = acceptanceCount.add(p.getAcceptanceCount());
                }
                if (p.getConcessionToReceiveCount() != null) {
                    concessionToReceiveCount = concessionToReceiveCount.add(p.getConcessionToReceiveCount());
                }
                if (p.getRejectCount() != null) {
                    rejectCount = rejectCount.add(p.getRejectCount());
                }
            }
            detailVO.setAcceptanceCount(NumberUtil.formatToInteger(acceptanceCount));
            detailVO.setConcessionToReceiveCount(NumberUtil.formatToInteger(concessionToReceiveCount));
            detailVO.setRejectCount(NumberUtil.formatToInteger(rejectCount));
            return detailVO;
        }).collect(Collectors.toList()));

        return summaryBO;
    }

    /**
     * 查询供应订单待发货商品信息
     *
     * @param order 订单
     * @return 订单商品
     */
    @Override
    public List<VendorDeliveryProductResp> listVendorDeliveryProducts(OrderDO order) {

        List<OrderProductDO> orderProductDOList = orderProductRepository.findByOrder(order);
        if (CollectionUtils.isEmpty(orderProductDOList)) {
            return new ArrayList<>();
        }

        //获取商品id集合
        Set<Long> skuIds = orderProductDOList.stream().map(OrderProductDO::getSkuId).collect(Collectors.toSet());
        //构造请求参数
        List<OrderCommodityOccupiedInventoryReq> orderCommodityOccupiedInventoryReqList = new ArrayList<>();
        OrderCommodityOccupiedInventoryReq request = new OrderCommodityOccupiedInventoryReq();
        request.setOrderId(order.getId());
        request.setCommoditySkuIdList(new ArrayList<>(skuIds));
        orderCommodityOccupiedInventoryReqList.add(request);
        //获取全部仓位的占用库存
        List<OccupyInventoryProductPositionResp> orderProductPositionVOS = productFeignService.getCommodityInventoryBatch(orderCommodityOccupiedInventoryReqList);
        Map<Long, List<OccupyInventoryProductPositionResp>> skuIdOccupyInventoryMap = new HashMap<>();
        if (orderProductPositionVOS.size() > 0) {
            skuIdOccupyInventoryMap = orderProductPositionVOS.stream().collect(Collectors.groupingBy(OccupyInventoryProductPositionResp::getSkuId));
        }

        //通过sku获取物料id key:skuId value:goodsId
        Map<Long, Long> goodsMap = new HashMap<>();
        WrapperResp<List<CommoditySkuStockResp>> wrapperResp = commodityFeign.getCommodityByCommoditySkuIdList(new ArrayList<>(skuIds));
        if (wrapperResp.getCode() != WrapperUtil.success().getCode()) {
            throw new BusinessException(wrapperResp.getCode(), wrapperResp.getMessage());
        }
        if (wrapperResp.getData().size() > 0) {
            goodsMap = wrapperResp.getData().stream().filter(c -> NumberUtil.notNullOrZero(c.getId()) && NumberUtil.notNullOrZero(c.getMaterielId())).collect(Collectors.toMap(CommoditySkuStockResp::getId, CommoditySkuStockResp::getMaterielId, (v1, v2) -> v2));
        }

        Map<Long, Long> finalGoodsMap = goodsMap;
        Map<Long, List<OccupyInventoryProductPositionResp>> finalSkuIdOccupyInventoryMap = skuIdOccupyInventoryMap;
        return orderProductDOList.stream().map(product -> {
            VendorDeliveryProductResp productVO = new VendorDeliveryProductResp();
            productVO.setOrderProductId(product.getId());
            productVO.setRelationId(product.getRelationId());
            productVO.setProductId(finalGoodsMap.get(product.getSkuId()) != null ? finalGoodsMap.get(product.getSkuId()) : product.getProductId());
            productVO.setSkuId(product.getSkuId());
            productVO.setProductNo(product.getProductNo());
            productVO.setName(product.getName());
            productVO.setCategory(product.getCategory());
            productVO.setSpec(product.getSpec());
            productVO.setBrand(product.getBrand());
            productVO.setUnit(product.getUnit());
            if (product.getMaterial() != null) {
                productVO.setQuotedProductId(product.getMaterial().getProductId());
                productVO.setQuotedSkuId(product.getMaterial().getSkuId());
                productVO.setQuotedProductNo(product.getMaterial().getProductNo());
                productVO.setQuotedName(StringUtils.hasLength(product.getMaterial().getSpec()) ? product.getMaterial().getName().concat("/").concat(product.getMaterial().getSpec()) : product.getMaterial().getName());
                productVO.setQuotedSpec(product.getMaterial().getSpec());
                productVO.setQuotedCategory(product.getMaterial().getCategory());
                productVO.setQuotedBrand(product.getMaterial().getBrand());
            }
            //下单仓位信息
            if (!CollectionUtils.isEmpty(product.getOrderProductPositionDOS())) {
                productVO.setOrderProductPositionVOS(product.getOrderProductPositionDOS().stream().map(p -> BeanUtil.copyProperties(p, OrderProductPositionResp.class)).collect(Collectors.toList()));
            }
            //占用库存信息
            List<OccupyInventoryProductPositionResp> occupyInventoryProductPositionResp = finalSkuIdOccupyInventoryMap.get(productVO.getSkuId());
            if (!CollectionUtils.isEmpty(occupyInventoryProductPositionResp)) {
                productVO.setOccupyInventoryVOS(occupyInventoryProductPositionResp);
                productVO.setAvailableForDeliveryQuantity(BigDecimal.ZERO);
            }
            productVO.setQuantity(NumberUtil.formatToInteger(product.getQuantity()));
            productVO.setDelivered(NumberUtil.formatToInteger(product.getDelivered()));
            productVO.setReceived(NumberUtil.formatToInteger(product.getReceived()));
            productVO.setLeftCount(NumberUtil.formatToInteger(product.getLeftCount()));
            productVO.setDifferCount(NumberUtil.formatToInteger(product.getDifferCount()));
            productVO.setSingleCode(product.getSingleCode());
            productVO.setMaterialInfo(product.getMaterialInfo());
            return productVO;
        }).collect(Collectors.toList());
    }

    /**
     * 修改订单商品的已支付金额
     *
     * @param order     订单
     * @param payAmount 单次支付金额
     */
    @Override
    public void updateProductPayAmount(OrderDO order, BigDecimal payAmount) {
        if (payAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        List<OrderProductDO> products = orderProductRepository.findByOrder(order);
        if (CollectionUtils.isEmpty(products)) {
            return;
        }

        //根据商品总价加权平均
        Map<Long, BigDecimal> map = weightedPayAmount(products, payAmount);
        products.forEach(product -> product.setPaidAmount(map.getOrDefault(product.getId(), BigDecimal.ZERO)));
        orderProductRepository.saveAll(products);
    }

    /**
     * （转单）根据订单中的上游供应商与供应商商品，查询上游供应商商品信息
     *
     * @param roleId        当前供应商要转单的服务消费者角色Id
     * @param orders        订单列表
     * @param orderProducts 订单商品列表
     * @return 上游供应商商品列表
     */
    @Override
    public List<SupplyProductDTO> findSupplyProducts(Long roleId, List<OrderDO> orders, List<OrderProductDO> orderProducts) {
        //转单后订单的单价：
        // 1.当前的销售订单的下单模式是进货单下单，且商品上上游供应会员商品的商品定价为现货价格，则取商品上上游供应会员商品的单价
        // 2.当前的销售订单的下单模式是询报价下单，且商品上上游供应会员商品的商品定价为价格需要询价，且销售订单的商品有二次询价，则取销售订单上对应询报价单的二次询价单中商品的报价单价（转单后的采购订单下单模式是询报价下单，对应报价单为二次询价的询价单号）
        // 3.当前的销售订单的下单模式是询报价下单，且商品上上游供应会员商品的商品定价为价格需要询价，且销售订单的商品没有二次询价，则提示[勾选的销售订单中，有需要询价的商品，无法进行转单。]

        //构造接口参数，从商品服务查询上游供应商商品信息
        List<VendorProductBO> vendorProducts = orders.stream().map(order -> {
            VendorProductBO vendorProduct = new VendorProductBO();
            vendorProduct.setVendorMemberId(order.getVendorMemberId());
            vendorProduct.setVendorRoleId(order.getVendorRoleId());
            vendorProduct.setProducts(orderProducts.stream().filter(orderProduct -> orderProduct.getOrder().getId().equals(order.getId())).map(orderProduct -> new OrderProductBO(orderProduct.getProductId(), orderProduct.getSkuId())).collect(Collectors.toList()));
            return vendorProduct;
        }).collect(Collectors.toList());

        List<SupplyProductDTO> productResult = productFeignService.findSupplyProducts(roleId, vendorProducts);

        //Step 1: 校验每个商品都是否存在上游供应商商品
        if (orderProducts.stream().anyMatch(orderProduct -> productResult.stream().noneMatch(supplyProduct -> supplyProduct.getVendorSkuId().equals(orderProduct.getSkuId())))) {
            throw new BusinessException(ResponseCodeEnum.ORDER_SUPPLY_PRODUCT_DOES_NOT_EXIST);
        }

        //Step 2: 查询配送方式为“自提”的地址，然后返回
        List<Long> addressIds = productResult.stream().filter(p -> p.getDeliverType().equals(OrderProductDeliverTypeEnum.PICK_UP_ADDRESS.getCode()) && NumberUtil.notNullAndPositive(p.getAddressId())).map(SupplyProductDTO::getAddressId).collect(Collectors.toList());
        List<ProductAddressDTO> addressResult = logisticsFeignService.findProductAddresses(addressIds);

        //Step 3: 查询并替换价格、自提地址等信息，然后返回
        productResult.forEach(supplyProduct -> {
            orderProducts.stream().filter(p -> p.getSkuId().equals(supplyProduct.getVendorSkuId())).map(OrderProductDO::getQuantity).findFirst().ifPresent(quantity -> {
                supplyProduct.setPrice(findSupplyProductPrice(supplyProduct.getPrices(), quantity));
                supplyProduct.setQuantity(quantity);
            });

            addressResult.stream().filter(address -> address.getAddressId().equals(supplyProduct.getAddressId())).findFirst().ifPresent(address -> {
                supplyProduct.setAddress(address.getAddress());
                supplyProduct.setReceiver(address.getReceiver());
                supplyProduct.setPhone(address.getPhone());
            });
        });

        return productResult;
    }

    /**
     * （转单）检查订单发货条件，如果是转单订单发货，返回原订单及原订单的发货参数
     *
     * @param order      发货的订单
     * @param deliveryVO 发货接口参数
     * @return 检查结果
     */
    @Override
    public TransferOrderDeliveryBO checkOrderDelivery(OrderDO order, VendorDeliveryReq deliveryVO) {
        //如果订单状态不为“待确认发货”，返回错误
        if (!order.getVendorInnerStatus().equals(VendorInnerStatusEnum.TO_SHIP.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_DELIVERY_HAS_COMPLETED);
        }

        //如果订单是“拼团订单”，拼团不成功时，不允许发货
        if (order.getOrderKind().equals(OrderSourceKindEnum.GROUP.getCode())) {
            if (order.getPromotionStatus().equals(OrderPromotionStatusEnum.GROUPING.getCode()) || order.getPromotionStatus().equals(OrderPromotionStatusEnum.GROUP_WAITING_CONFIRM.getCode())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_CAN_NOT_DELIVER_PRODUCTS_WHEN_GROUPING);
            }

            if (order.getPromotionStatus().equals(OrderPromotionStatusEnum.GROUP_FAILED.getCode())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_CAN_NOT_DELIVER_PRODUCTS_WHEN_GROUP_FAILED);
            }
        }

        // 普通订单
        if (order.getSeparateType().equals(OrderSeparateTypeEnum.DEFAULT.getCode())) {
            return new TransferOrderDeliveryBO(false);
        }

        //如果有转单订单，不允许发货
//        if (order.getSeparateType().equals(OrderSeparateTypeEnum.SEPARATED.getCode())) {
//            throw new BusinessException(ResponseCodeEnum.ORDER_SEPARATED_ORDER_CAN_NOT_DELIVER_PRODUCT);
//        }

        TransferOrderDeliveryBO deliveryBO = new TransferOrderDeliveryBO();
        //如果是转单后的订单，转单前的采购订单(下游采购商的采购订单)外部状态是否“待发货”
        if (order.getSeparateType().equals(OrderSeparateTypeEnum.TRANSFERRED.getCode()) && NumberUtil.notNullAndPositive(order.getRelationId())) {
            OrderDO separateOrder = orderRepository.findById(order.getRelationId()).orElse(null);
            if (separateOrder == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SEPARATED_ORDER_DOES_NOT_EXIST);
            }

            if (!separateOrder.getVendorInnerStatus().equals(VendorInnerStatusEnum.VENDOR_TO_CONFIRM_DELIVERY.getCode())) {
                throw new BusinessException(ResponseCodeEnum.ORDER_SEPARATED_ORDER_MUST_TO_DELIVERY);
            }

            deliveryBO.setDeliverSeparatedOrder(true);
            if (Objects.nonNull(deliveryVO)) {
                //判断接口参数中的relationId字段，要大于0
                if (deliveryVO.getProducts().stream().anyMatch(p -> NumberUtil.isNullOrZero(p.getRelationId()))) {
                    throw new BusinessException(ResponseCodeEnum.ORDER_SEPARATED_PRODUCT_DOES_NOT_EXIST);
                }

                deliveryBO.setOrder(separateOrder);

                //从当前订单的发货参数，构造转单前订单的发货参数
                VendorDeliveryReq deliveryReq = new VendorDeliveryReq();
                deliveryReq.setOrderId(separateOrder.getId());
                deliveryReq.setDeliveryTime(StringUtils.hasLength(deliveryVO.getDeliveryTime()) ? deliveryVO.getDeliveryTime() : LocalDateTime.now().format(OrderConstant.DEFAULT_TIME_FORMATTER));
                deliveryReq.setAddress(StringUtils.hasLength(deliveryVO.getAddress()) ? deliveryVO.getAddress() : "");
                deliveryReq.setAddressId(Objects.nonNull(deliveryVO.getAddressId()) ? deliveryVO.getAddressId() : 0L);
                deliveryReq.setCompany(StringUtils.hasLength(deliveryVO.getCompany()) ? deliveryVO.getCompany() : "");
                deliveryReq.setLogisticsNo(StringUtils.hasLength(deliveryVO.getLogisticsNo()) ? deliveryVO.getLogisticsNo() : "");

                deliveryReq.setProducts(deliveryVO.getProducts().stream().map(product -> {
                    VendorToDeliveryProductReq productVO = new VendorToDeliveryProductReq();
                    productVO.setOrderProductId(product.getRelationId());
                    productVO.setDeliveryCount(product.getDeliveryCount());
                    productVO.setRelationId(0L);
                    return productVO;
                }).collect(Collectors.toList()));

                deliveryBO.setDeliveryVO(deliveryReq);
            }
        }

        return deliveryBO;
    }

    /**
     * （转单）检查订单收货条件，如果有转单订单，返回转单订单
     *
     * @param order 收货的订单
     * @return 检查结果
     */
    @Override
    public TransferOrderReceiveBO checkOrderReceive(OrderDO order) {
        if (order.getSeparateType().equals(OrderSeparateTypeEnum.DEFAULT.getCode())) {
            return new TransferOrderReceiveBO(false);
        }

        //如果当前订单是转单后的订单，不允许收货
        if (order.getSeparateType().equals(OrderSeparateTypeEnum.TRANSFERRED.getCode())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_TRANSFERRED_ORDER_CAN_NOT_RECEIVE_PRODUCT);
        }

        if (NumberUtil.isNullOrZero(order.getRelationId())) {
            throw new BusinessException(ResponseCodeEnum.ORDER_TRANSFERRED_ORDER_DOES_NOT_EXIST);
        }

        OrderDO transferredOrder = orderRepository.findById(order.getRelationId()).orElse(null);
        if (transferredOrder == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_TRANSFERRED_ORDER_DOES_NOT_EXIST);
        }

        return new TransferOrderReceiveBO(transferredOrder);
    }

    /**
     * 保存赠送优惠券信息
     *
     * @param order              订单
     * @param orderProductVOList 订单商品信息
     * @param giveCouponDTOMap   赠送的优惠券
     */
    @Override
    public void addGiveCouponList(OrderDO order, List<MobileOrderProductReq> orderProductVOList, Map<Long, List<OrderGiveCouponDTO>> giveCouponDTOMap) {
        log.info("对优惠券进行分组后的信息:{}", giveCouponDTOMap);
        for (MobileOrderProductReq orderProductVO : orderProductVOList) {
            Long skuId = orderProductVO.getSkuId();
            log.info("对订单分组后skuId:{}", skuId);
            List<OrderGiveCouponDTO> giveCouponDTOList = giveCouponDTOMap.get(skuId);
            if (!CollectionUtils.isEmpty(giveCouponDTOList)) {
                List<OrderGiveCouponDO> giveCouponDOList = giveCouponDTOList.stream().map(give -> {
                    OrderGiveCouponDO orderGiveCouponDO = new OrderGiveCouponDO();
                    orderGiveCouponDO.setOrder(order);
                    orderGiveCouponDO.setGiveId(give.getGiveId());
                    orderGiveCouponDO.setProductId(give.getProductId());
                    orderGiveCouponDO.setBrand(give.getBrand());
                    orderGiveCouponDO.setCategory(give.getCategory());
                    orderGiveCouponDO.setName(give.getName());
                    orderGiveCouponDO.setQuantity(give.getQuantity());
                    orderGiveCouponDO.setSkuId(give.getSkuId());
                    orderGiveCouponDO.setUnit(give.getUnit());
                    return orderGiveCouponDO;
                }).collect(Collectors.toList());
                orderGiveCouponRepository.saveAll(giveCouponDOList);
            }
        }
    }

    /**
     * 根据商品总价加权平均支付金额
     *
     * @param products  订单商品列表
     * @param payAmount 支付金额
     * @return 加权平均结果，key为OrderProductDO的Id，value为加权平均结果
     */
    private Map<Long, BigDecimal> weightedPayAmount(List<OrderProductDO> products, BigDecimal payAmount) {
        Long maxId = products.stream().max(Comparator.comparingLong(OrderProductDO::getId)).map(OrderProductDO::getId).orElse(0L);
        BigDecimal totalAmount = products.stream().map(OrderProductDO::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ONE);
        Map<Long, BigDecimal> map = products.stream().filter(p -> p.getId().compareTo(maxId) < 0).collect(Collectors.toMap(OrderProductDO::getId, p -> payAmount.multiply(p.getAmount()).divide(totalAmount, 2, RoundingMode.HALF_UP)));
        map.put(maxId, payAmount.subtract(map.values().stream().reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
        return map;
    }

    /**
     * 校验商品单价（非到手价）
     */
    private void checkProductPrice(Map<String, BigDecimal> priceMap, PurchaseOrderProductReq productVO, List<QuotedMaterielResp> quotedMaterielRespList) {
        BigDecimal quantity = productVO.getQuantity();
        BigDecimal orderProductPrice = productVO.getPrice();

        if (CollUtil.isNotEmpty(quotedMaterielRespList)) {
            QuotedMaterielResp quotedMaterielResp = quotedMaterielRespList.stream().filter(p -> p.getProductId().equals(productVO.getSkuId())).findFirst().orElse(null);
            if (quotedMaterielResp == null) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
            }

            BigDecimal taxUnitPrice = quotedMaterielResp.getTaxUnitPrice();
            if (productVO.getPrice().setScale(3, RoundingMode.HALF_UP).compareTo(taxUnitPrice) != 0) {
                throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_PRICE_IS_NOT_CORRECT);
            }
            return;
        }

        BigDecimal priceResult = findProductPrice(priceMap, quantity);

        if (priceResult.compareTo(orderProductPrice.setScale(3, RoundingMode.HALF_UP)) != 0) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_PRICE_IS_NOT_CORRECT);
        }
    }

    /**
     * 校验商品单价（非到手价）
     *
     * @param priceMap 从商品服务获取的商品价格
     * @param quantity 订单商品的订购数量
     * @return 校验结果
     */
    private BigDecimal findProductPrice(Map<String, BigDecimal> priceMap, BigDecimal quantity) {
        if (CollectionUtils.isEmpty(priceMap)) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }

        // 商品服务返回的阶梯价格中，如果key是“0-0”，表示没有阶梯价，否则要判断阶梯价
        if (priceMap.containsKey(Constant.NOT_STEP_PRICE_KEY)) {
            return priceMap.get(Constant.NOT_STEP_PRICE_KEY).setScale(5, RoundingMode.HALF_UP);
        }

        // 商品服务返回的阶梯价格的格式为 {"1-5": 10, "7-8": 20}，表示如果购买1-5件商品，价格为10元，购买7-8件商品，价格为20元
        // 规则：
        // a. 其中key中购买数量可以为小数（保留3位小数）
        // b. 如上例，如果购买6件商品，要取key为"1-5"的价格，如果购买9件商品以上，要取key为"7-8"的价格，即：取阶梯价格购买数量小于当前购买数量的，其中购买数量为最大值的那一条数据
        Map.Entry<String, BigDecimal> entry = priceMap.entrySet().stream().filter(stringBigDecimalEntry -> {
            List<BigDecimal> keys = Arrays.stream(stringBigDecimalEntry.getKey().split("-")).map(BigDecimal::new).sorted(Comparator.naturalOrder()).collect(Collectors.toList());
            return quantity.compareTo(keys.get(0)) >= 0 && quantity.compareTo(keys.get(1)) <= 0;
        }).findFirst().orElse(priceMap.entrySet().stream().filter(stringBigDecimalEntry -> {
            List<BigDecimal> keys = Arrays.stream(stringBigDecimalEntry.getKey().split("-")).map(BigDecimal::new).sorted(Comparator.naturalOrder()).collect(Collectors.toList());
            return quantity.compareTo(keys.get(1)) >= 0;
        }).max((first, second) -> {
            BigDecimal maxOfFirst = Arrays.stream(first.getKey().split("-")).map(BigDecimal::new).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            BigDecimal maxOfSecond = Arrays.stream(second.getKey().split("-")).map(BigDecimal::new).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            return maxOfFirst.compareTo(maxOfSecond);
        }).orElse(null));

        if (entry == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_PRICE_IS_NOT_CORRECT);
        }

        return entry.getValue().setScale(5, RoundingMode.HALF_UP);
    }

    /**
     * 从商品服务返回的上游供应商商品列表中，查询上游供应商商品价格
     *
     * @param priceMap 从商品服务获取的商品价格
     * @param quantity 订单商品的订购数量
     * @return 校验结果
     */
    private BigDecimal findSupplyProductPrice(Map<String, BigDecimal> priceMap, BigDecimal quantity) {
        if (CollectionUtils.isEmpty(priceMap) || quantity.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        // 商品服务返回的阶梯价格中，如果key是“0-0”，表示没有阶梯价，否则要判断阶梯价
        if (priceMap.containsKey(Constant.NOT_STEP_PRICE_KEY)) {
            return priceMap.get(Constant.NOT_STEP_PRICE_KEY).setScale(3, RoundingMode.HALF_UP);
        }

        // 商品服务返回的阶梯价格的格式为 {"1-5": 10, "7-8": 20}，表示如果购买1-5件商品，价格为10元，购买7-8件商品，价格为20元
        // 规则：
        // a. 其中key中购买数量可以为小数（保留3位小数）
        // b. 如上例，如果购买6件商品，要取key为"1-5"的价格，如果购买9件商品以上，要取key为"7-8"的价格，即：取阶梯价格购买数量小于当前购买数量的，其中购买数量为最大值的那一条数据
        Map.Entry<String, BigDecimal> entry = priceMap.entrySet().stream().filter(stringBigDecimalEntry -> {
            List<BigDecimal> keys = Arrays.stream(stringBigDecimalEntry.getKey().split("-")).map(BigDecimal::new).sorted(Comparator.naturalOrder()).collect(Collectors.toList());
            return quantity.compareTo(keys.get(0)) >= 0 && quantity.compareTo(keys.get(1)) <= 0;
        }).findFirst().orElse(priceMap.entrySet().stream().filter(stringBigDecimalEntry -> {
            List<BigDecimal> keys = Arrays.stream(stringBigDecimalEntry.getKey().split("-")).map(BigDecimal::new).sorted(Comparator.naturalOrder()).collect(Collectors.toList());
            return quantity.compareTo(keys.get(1)) >= 0;
        }).max((first, second) -> {
            BigDecimal maxOfFirst = Arrays.stream(first.getKey().split("-")).map(BigDecimal::new).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            BigDecimal maxOfSecond = Arrays.stream(second.getKey().split("-")).map(BigDecimal::new).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            return maxOfFirst.compareTo(maxOfSecond);
        }).orElse(null));

        return entry == null ? BigDecimal.ZERO : entry.getValue().setScale(3, RoundingMode.HALF_UP);
    }

    /**
     * “SRM订单” - 查看物料关联的请购单
     *
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<OrderRequisitionQueryResp> findSrmRequisition(OrderRequisitionPageDataReq pageVO) {
        OrderProductDO productDO = orderProductRepository.findFirstByProductId(pageVO.getProductId());
        if (productDO == null) {
            throw new BusinessException(ResponseCodeEnum.ORDER_PRODUCT_DOES_NOT_EXIST);
        }

        if (productDO.getMaterial() != null && CollUtil.isNotEmpty(productDO.getMaterial().getRequisitions())) {
            WrapperResp<List<RequisitionQueryDetailResp>> requisitionWrapperResp = purchaseRequisitionFeign
                    .findRequisitionByIds(productDO.getMaterial().getRequisitions().stream().map(RequisitionBO::getRequisitionId).collect(Collectors.toList()));
            WrapperUtil.throwWhenFail(requisitionWrapperResp);

            List<RequisitionQueryDetailResp> responseList = CollUtil.isEmpty(requisitionWrapperResp.getData()) ? Collections.emptyList() : requisitionWrapperResp.getData();

            List<OrderRequisitionQueryResp> detailResponseList = responseList.stream()
                    .map(detail -> {
                        //当前请购单下单数量
                        Integer orderQuantity = productDO.getMaterial().getRequisitions().stream().filter(requisition -> requisition.getRequisitionId().equals(detail.getRequisitionId())).map(RequisitionBO::getOrderQuantity).findFirst().orElse(0);

                        OrderRequisitionQueryResp queryVO = new OrderRequisitionQueryResp();
                        queryVO.setRequisitionId(detail.getRequisitionId());
                        queryVO.setRequisitionNo(detail.getRequisitionNo());
                        queryVO.setDigest(detail.getDigest());
                        queryVO.setVendorMemberName(detail.getVendorMemberName());
                        queryVO.setDepartment(detail.getDepartment());
                        queryVO.setRequisitioner(detail.getRequisitioner());
                        queryVO.setAdvanceDeliveryDate(detail.getAdvanceDeliveryDate());
                        queryVO.setDeliveryMethod(detail.getDeliveryMethod());
                        queryVO.setDeliveryMethodName(DeliveryMethodEnum.getCodeMessage(detail.getDeliveryMethod()));
                        queryVO.setDeliveryAddress(detail.getDeliveryAddress());
                        queryVO.setQuantity(detail.getQuantity());
                        queryVO.setProductNo(productDO.getProductNo());
                        queryVO.setName(productDO.getName());
                        queryVO.setOrderQuantity(orderQuantity);
                        return queryVO;
                    }).collect(Collectors.toList());

            return new PageDataResp<>((long) detailResponseList.size(), detailResponseList);
        }

        return new PageDataResp<>(0L, new ArrayList<>());
    }
}
