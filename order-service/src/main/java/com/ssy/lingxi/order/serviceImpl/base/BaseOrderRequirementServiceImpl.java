package com.ssy.lingxi.order.serviceImpl.base;

import com.ssy.lingxi.order.entity.OrderDO;
import com.ssy.lingxi.order.entity.OrderRequirementDO;
import com.ssy.lingxi.order.model.bo.OrderRequirementDetailBO;
import com.ssy.lingxi.order.model.req.basic.OrderRequirementReq;
import com.ssy.lingxi.order.model.resp.basic.OrderRequirementDetailResp;
import com.ssy.lingxi.order.repository.OrderRequirementRepository;
import com.ssy.lingxi.order.service.base.IBaseOrderRequirementService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 订单其他要求相关接口实现类
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021-07-19
 */
@Service
public class BaseOrderRequirementServiceImpl implements IBaseOrderRequirementService {
    @Resource
    private OrderRequirementRepository orderRequirementRepository;

    /**
     * 检查并生成订单其他要求，调用方要保存OrderDO
     *
     * @param order    订单
     * @param remark   订单备注
     * @param isCreate 是否新增，true-新增，false-修改
     */
    @Override
    public void checkOrderRemark(OrderDO order, String remark, boolean isCreate) {
        if(!isCreate) {
            orderRequirementRepository.deleteByOrder(order);
        }

        if(!StringUtils.hasText(remark)) {
            order.setRequirement(null);
            return;
        }

        OrderRequirementDO requirement = new OrderRequirementDO();
        requirement.setOrder(order);
        requirement.setDetail(new OrderRequirementDetailBO("", remark));

        order.setRequirement(requirement);
    }

    /**
     * 检查并生成订单其他要求，调用方要保存OrderDO
     *
     * @param order         订单
     * @param requirement   其他要求接口参数
     * @param isCreate      是否新增，true-新增，false-修改
     */
    @Override
    public void checkOrderRequirement(OrderDO order, OrderRequirementReq requirement, boolean isCreate) {
        if(!isCreate) {
            orderRequirementRepository.deleteByOrder(order);
        }

        if(requirement == null) {
            order.setRequirement(null);
            return;
        }

        OrderRequirementDO orderRequirement = Objects.isNull(order.getRequirement()) ? new OrderRequirementDO() : order.getRequirement();
        orderRequirement.setOrder(order);
        orderRequirement.setDetail(new OrderRequirementDetailBO(requirement.getPack(), requirement.getRemark()));

        order.setRequirement(orderRequirement);
    }

    /**
     * （转单） - 检查并生成订单其他要求，调用方要保存OrderDO
     *
     * @param order         转单后的订单
     * @param separateOrder 原订单
     */
    @Override
    public void transferOrderRequirement(OrderDO order, OrderDO separateOrder) {
        OrderRequirementDO separateRequirement = orderRequirementRepository.findFirstByOrder(separateOrder);
        if(separateRequirement == null) {
            order.setRequirement(null);
        } else {
            OrderRequirementDO requirement = new OrderRequirementDO();
            requirement.setOrder(order);
            requirement.setDetail(separateRequirement.getDetail());

            order.setRequirement(requirement);
        }
    }

    /**
     * 查询订单其他要求
     *
     * @param order 订单
     * @return 订单其他要求
     */
    @Override
    public OrderRequirementDetailResp getOrderRequirement(OrderDO order) {
        OrderRequirementDetailResp detailVO = new OrderRequirementDetailResp();
        OrderRequirementDO requirement = orderRequirementRepository.findFirstByOrder(order);
        if(requirement == null || requirement.getDetail() == null) {
            return detailVO;
        }

        detailVO.setPack(StringUtils.hasLength(requirement.getDetail().getPack()) ? requirement.getDetail().getPack() : "");
        detailVO.setRemark(StringUtils.hasLength(requirement.getDetail().getRemark()) ? requirement.getDetail().getRemark() : "");
        return detailVO;
    }
}
