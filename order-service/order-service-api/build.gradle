plugins {
    id 'java'
}

group 'com.ssy.lingxi.order.api'
version '3.0.0'

sourceCompatibility = 1.8

repositories {
    mavenLocal()
    maven {
        url 'http://10.0.0.21:8081/repository/maven-public/'
    }
    maven {
        url 'https://maven.aliyun.com/repository/public'
    }
    mavenCentral()
}

dependencies {
    implementation project(':component:base-spring-boot-starter')
    annotationProcessor project(':component:base-spring-boot-starter')

    // openfeign
    compileOnly('org.springframework.cloud:spring-cloud-starter-openfeign:3.0.2')

    // 校验工具
    compileOnly('org.hibernate.validator:hibernate-validator:6.1.7.Final')
}


