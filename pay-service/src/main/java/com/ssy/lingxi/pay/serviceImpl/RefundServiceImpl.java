package com.ssy.lingxi.pay.serviceImpl;


import cn.hutool.json.JSONObject;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPaymentParameterEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayChannelFeignReq;
import com.ssy.lingxi.order.api.model.resp.PayChannelParameterFeignDetailResp;
import com.ssy.lingxi.order.api.model.resp.PaymentParameterFeignDetailResp;
import com.ssy.lingxi.pay.api.model.req.ccb.B2bRefundQueryReq;
import com.ssy.lingxi.pay.api.model.req.ccb.B2bRefundReq;
import com.ssy.lingxi.pay.model.dto.RefundDTO;
import com.ssy.lingxi.pay.model.dto.RefundQueryDTO;
import com.ssy.lingxi.pay.service.IPayCacheService;
import com.ssy.lingxi.pay.service.IRefundService;
import com.ssy.lingxi.pay.util.CcbPayUtil;
import com.ssy.lingxi.pay.util.RefundUtil;
import com.ssy.lingxi.pay.util.XmlUtil;
import jodd.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @author:linzy
 * @since: 2021/12/27
 * @version 2.0.0
 */
@Service
public class RefundServiceImpl  implements IRefundService {

    private final static Logger logger = LoggerFactory.getLogger(RefundServiceImpl.class);

    @Resource
    private IPayCacheService payCacheService;

    @Resource
    private IOrderProcessFeign orderFeignService;

    @Override
    public Boolean refund (B2bRefundReq refundVO) {
        try{
            String ipAddress = "";      //外联平台ip地址
            String port = "";           //外联平台端口
            String customerId = "";     //商户号
            String userId = "";         //操作员号
            String password = "";       //操作员密码
            //从订单服务查询支付参数
            OrderPayChannelFeignReq orderPayChannelFeignReq = new OrderPayChannelFeignReq();
            orderPayChannelFeignReq.setPayChannel(OrderPayChannelEnum.CCB_B2B);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPlatformPaymentParameters(orderPayChannelFeignReq);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                throw new BusinessException(parameterResult.getCode(), parameterResult.getMessage());
            }
            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                throw new BusinessException(ResponseCodeEnum.PAY_CCB_B2B_PARAM_NOT_EXIST);
            }
            List<PayChannelParameterFeignDetailResp> parameters = parameterResult.getData().getParameters();
            for (PayChannelParameterFeignDetailResp p : parameters) {
                if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_IP_ADDRESS)) {
                    ipAddress = p.getValue();
                }else if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_PORT)) {
                    port = p.getValue();
                }else if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_MERCHANT_ID)) {
                    customerId = p.getValue();
                }else if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_USER_ID)) {
                    userId = p.getValue();
                }else if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_USER_PASSWORD)) {
                    password = p.getValue();
                }
            }
            if(StringUtil.isEmpty(ipAddress)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_IP_ADDRESS.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }
            if(StringUtil.isEmpty(port)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_PORT.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }
            if(StringUtil.isEmpty(customerId)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_MERCHANT_ID.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }
            if(StringUtil.isEmpty(userId)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_USER_ID.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }
            if(StringUtil.isEmpty(password)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_USER_PASSWORD.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }
            //请求序列号
            String requestSn = RefundUtil.getRequestSn();
            //退款金额
            String money = refundVO.getMoney();
            //订单号
            String orderId = refundVO.getOrderId();
            //退款流水号
            String refundCode = refundVO.getRefundCode() == null?"":refundVO.getRefundCode();
            //退款发送请求到外联
            String result = RefundUtil.payByCcbRefund(ipAddress, Integer.parseInt(port), requestSn, userId, password, money, orderId, customerId, refundCode);
            logger.info("B2B商户建行退款响应:"+ result);

            //将返回的xml字符串转成json对象
            JSONObject jsonObject = XmlUtil.xml2Json(result);
            //判断转换之后json是否为空，为空则抛出AssertionError，并终止执行。
            assert jsonObject != null;
            RefundDTO refundDTO = SerializeUtil.deserialize(jsonObject.toString(), RefundDTO.class);
            logger.info("xml转json:{}", jsonObject);
            //将结果返回
            if(CcbPayUtil.success_code.equals(refundDTO.getRefundText().getReturnCode())) {
                return true;
            }else{
                throw new BusinessException(refundDTO.getRefundText().getReturnMsg());
            }
//            if(StringUtil.isNotEmpty(refundDTO.getRefundText().getReturnCode())){
//                B2bRefundReturnVO refundReturn = new B2bRefundReturnVO();
//                refundReturn.setOrderId(refundVO.getOrderId());
//                //响应码
//                refundReturn.setReturnCode(refundDTO.getRefundText().getReturnCode());
//                //响应信息
//                refundReturn.setReturnMsg(refundDTO.getRefundText().getReturnMsg());
//
//                return refundReturn;
//            }

        }catch (Exception e){
            throw new BusinessException(ResponseCodeEnum.PAY_CCB_GATEWAY_EXCEPTION);
        }
    }

    @Override
    public String refundQuery(B2bRefundQueryReq refundQueryVO) {
        try{
            String ipAddress = "";      //外联平台ip地址
            String port = "";           //外联平台端口
            String customerId = "";     //商户号
            String userId = "";         //操作员号
            String password = "";       //操作员密码
            //从订单服务查询支付参数
            OrderPayChannelFeignReq orderPayChannelFeignReq = new OrderPayChannelFeignReq();
            orderPayChannelFeignReq.setPayChannel(OrderPayChannelEnum.CCB_B2B);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPlatformPaymentParameters(orderPayChannelFeignReq);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                throw new BusinessException(parameterResult.getCode(), parameterResult.getMessage());
            }
            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                throw new BusinessException(ResponseCodeEnum.PAY_CCB_B2B_PARAM_NOT_EXIST);
            }
            List<PayChannelParameterFeignDetailResp> parameters = parameterResult.getData().getParameters();
            for (PayChannelParameterFeignDetailResp p : parameters) {
                if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_IP_ADDRESS)) {
                    ipAddress = p.getValue();
                }else if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_PORT)) {
                    port = p.getValue();
                }else if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_MERCHANT_ID)) {
                    customerId = p.getValue();
                }else if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_USER_ID)) {
                    userId = p.getValue();
                }else if (p.getParameter().equals(OrderPaymentParameterEnum.CCB_B2B_USER_PASSWORD)) {
                    password = p.getValue();
                }
            }
            if(StringUtil.isEmpty(ipAddress)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_IP_ADDRESS.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }
            if(StringUtil.isEmpty(port)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_PORT.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }
            if(StringUtil.isEmpty(customerId)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_MERCHANT_ID.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }
            if(StringUtil.isEmpty(userId)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_USER_ID.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }
            if(StringUtil.isEmpty(password)){
                throw new BusinessException(OrderPaymentParameterEnum.CCB_B2B_USER_PASSWORD.getKey() + ResponseCodeEnum.PARAM_NOT_EXIST.getMessage());
            }

            //请求序列号
            String requestSn = RefundUtil.getRequestSn();
            //判断流水类型，查询时间和交易日期相同时，传 【0 未结流水】 查询时间在交易日期不同时，传 【1 已结算流水】
            String kind;
            if(StringUtil.isEmpty(refundQueryVO.getKind())){
                String tradeDate = refundQueryVO.getTradeDate();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                String date = sdf.format(new Date());
                if(tradeDate.equals(date)){
                    kind = "0";
                }else{
                    kind = "1";
                }
            }else{
                kind = refundQueryVO.getKind();
            }
//            //流水类型:0:未结流水,1:已结流水
//            String kind = refundQueryVO.getKind();
            //订单号
            String orderId = refundQueryVO.getOrderId();
            //查询退款流水
            String result = RefundUtil.payByCcbRefundQuery(ipAddress, Integer.parseInt(port), requestSn, customerId, userId, password, orderId, kind);
            logger.info("B2B商户建行退款流水查询响应:"+ result);

            //将返回的xml字符串转成json对象
            JSONObject jsonObject = XmlUtil.xml2Json(result);
            //判断转换之后json是否为空，为空则抛出AssertionError，并终止执行。
            assert jsonObject != null;
            RefundQueryDTO refundQueryDTO = SerializeUtil.deserialize(jsonObject.toString(), RefundQueryDTO.class);
            logger.info("xml转json:{}", jsonObject);

            //将结果返回
            if(CcbPayUtil.success_code.equals(refundQueryDTO.getRefundQueryInfoDTO().getReturnCode())) {
                return refundQueryDTO.getRefundQueryInfoDTO().getRefundQueryPage().get(0).getRefundQueryInfoList().get(0).getStatus();
            }else{
                throw new BusinessException(refundQueryDTO.getRefundQueryInfoDTO().getReturnMsg());
            }
//            if(StringUtil.isNotEmpty(refundQueryDTO.getRefundQueryInfoDTO().getReturnCode())){
//                B2bRefundQueryReturnVO refundQueryReturn = new B2bRefundQueryReturnVO();
//                refundQueryReturn.setOrderId(refundQueryVO.getOrderId());
//                //响应码
//                refundQueryReturn.setReturnCode(refundQueryDTO.getRefundQueryInfoDTO().getReturnCode());
//                //响应信息
//                refundQueryReturn.setReturnMsg(refundQueryDTO.getRefundQueryInfoDTO().getReturnMsg());
//
//                return refundQueryReturn;
//            }

        }catch (Exception e){
            throw new BusinessException(ResponseCodeEnum.PAY_CCB_GATEWAY_EXCEPTION);
        }
    }
}
