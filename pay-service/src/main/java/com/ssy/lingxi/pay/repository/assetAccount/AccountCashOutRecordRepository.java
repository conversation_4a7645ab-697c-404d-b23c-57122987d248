package com.ssy.lingxi.pay.repository.assetAccount;

import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountCashOutRecordDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员资金账户提现处理记录
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
@Repository
public interface AccountCashOutRecordRepository extends JpaRepository<AccountCashOutRecordDO,Long>, JpaSpecificationExecutor<AccountCashOutRecordDO> {
    Page<AccountCashOutRecordDO> findByTradeCode(String tradeCode, Pageable page);
}
