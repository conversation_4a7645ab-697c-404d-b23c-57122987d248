package com.ssy.lingxi.pay.serviceImpl.eAccount;

import com.ssy.lingxi.pay.entity.do_.eAccount.EAccountTradeRecordDO;
import com.ssy.lingxi.pay.enums.TradeStatusEnum;
import com.ssy.lingxi.pay.repository.eAccount.EAccountTradeRecordRepository;
import com.ssy.lingxi.pay.service.eAccount.IEAccountTradeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 会员资金账户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/08
 */
@Service
public class EAccountTradeServiceImpl implements IEAccountTradeService {

    @Resource
    private EAccountTradeRecordRepository eAccountTradeRecordRepository;

    /**
     * 新增e账户交易记录
     */
    @Override
    public void saveEAccountTradeRecord(EAccountTradeRecordDO eAccountTradeRecordDO) {
        eAccountTradeRecordRepository.saveAndFlush(eAccountTradeRecordDO);
    }

    /**
     * 充值回调
     * @param out_trade_no 交易单号
     * @param trade_no 支付平台订单号
     */
    @Override
    public void rechargeNotify(String out_trade_no, String trade_no) {
        EAccountTradeRecordDO eAccountTradeRecordDO = eAccountTradeRecordRepository.findFirstByTradeCode(out_trade_no);
        if (eAccountTradeRecordDO != null) {
            //更新交易记录
            eAccountTradeRecordDO.setStatus(TradeStatusEnum.CONFIRM_ACCOUNT.getCode());
            eAccountTradeRecordDO.setOrderCode(trade_no);
            eAccountTradeRecordRepository.saveAndFlush(eAccountTradeRecordDO);
        }
    }

    /**
     * 提现回调
     * @param out_trade_no 交易单号
     * @param trade_no 支付平台订单号
     */
    @Override
    public void cashOutNotify(String out_trade_no, String trade_no) {
        EAccountTradeRecordDO eAccountTradeRecordDO = eAccountTradeRecordRepository.findFirstByTradeCode(out_trade_no);
        if (eAccountTradeRecordDO != null) {
            //更新交易记录
            eAccountTradeRecordDO.setStatus(TradeStatusEnum.CASH_OUT_SUCCESS.getCode());
            eAccountTradeRecordRepository.saveAndFlush(eAccountTradeRecordDO);
        }
    }

    /**
     * 查询是否充值成功
     * @param tradeCode 交易单号
     * @return 是-true，否-false
     */
    @Override
    public Boolean getRechargeResult(String tradeCode) {
        EAccountTradeRecordDO eAccountTradeRecordDO = eAccountTradeRecordRepository.findFirstByTradeCode(tradeCode);
        return eAccountTradeRecordDO != null && TradeStatusEnum.CONFIRM_ACCOUNT.getCode().equals(eAccountTradeRecordDO.getStatus());
    }
}

