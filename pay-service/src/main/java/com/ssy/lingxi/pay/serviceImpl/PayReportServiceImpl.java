package com.ssy.lingxi.pay.serviceImpl;

import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.member.api.model.req.MemberFeignUserIdReq;
import com.ssy.lingxi.pay.model.resp.PayReportResp;
import com.ssy.lingxi.pay.service.ICreditApplyService;
import com.ssy.lingxi.pay.service.IPayReportService;
import com.ssy.lingxi.pay.service.assetAccount.IAccountTradeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 首页-支付中心
 * <AUTHOR>
 * @version V3.0.0
 * @since 2023/12/7
 */
@Service
@Slf4j
public class PayReportServiceImpl implements IPayReportService {

    @Resource
    private IAccountTradeService accountTradeService ;

    @Resource
    private ICreditApplyService creditApplyService ;

    /**
     * 待办统计
     */
    @Override
    public PayReportResp getPay(UserLoginCacheDTO sysUser) {
        MemberFeignUserIdReq memberFeignUserIdReq = new MemberFeignUserIdReq();
        memberFeignUserIdReq.setUserId(sysUser.getUserId());
        memberFeignUserIdReq.setMemberId(sysUser.getMemberId());
        memberFeignUserIdReq.setRoleId(sysUser.getMemberRoleId());

        PayReportResp payReportResp = new PayReportResp();
        payReportResp.setCreditApplyInfo(creditApplyService.getPayReport(sysUser));
        payReportResp.setAccountManageInfo(accountTradeService.getPayReport(sysUser));
        return payReportResp;
    }
}
