package com.ssy.lingxi.pay.repository;

import com.ssy.lingxi.pay.entity.do_.CreditInnerVerifyRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 授信内部审批记录
 * <AUTHOR>
 * @since 2020/8/18
 * @version 2.0.0
 */
@Repository
public interface CreditInnerVerifyRecordRepository extends JpaRepository<CreditInnerVerifyRecordDO, Long>, JpaSpecificationExecutor<CreditInnerVerifyRecordDO> {

    /**
     * 根据申请id查询审批记录，按操作时间升序排序
     * <AUTHOR>
     * @since 2020/8/18
     **/
    List<CreditInnerVerifyRecordDO> findByApplyIdOrderByOperateTimeAsc(Long applyId);
}
