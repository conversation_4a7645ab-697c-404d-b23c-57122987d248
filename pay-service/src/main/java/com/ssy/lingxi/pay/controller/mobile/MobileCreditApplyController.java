package com.ssy.lingxi.pay.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.pay.model.req.*;
import com.ssy.lingxi.pay.model.resp.*;
import com.ssy.lingxi.pay.service.ICreditApplyService;
import com.ssy.lingxi.pay.service.ICreditBillService;
import com.ssy.lingxi.pay.service.ICreditService;
import com.ssy.lingxi.pay.service.ICreditTradeRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

/**
 * App - 服务消费者授信相关接口
 * <AUTHOR>
 * @since 2020/12/30
 * @version 2.0.0
 */
@RestController
@RequestMapping(ServiceModuleConstant.PAY_PATH_PREFIX + "/mobile/consumer/credit")
public class MobileCreditApplyController extends BaseController {

    @Resource
    private ICreditService creditService;

    @Resource
    private ICreditBillService billService;

    @Resource
    private ICreditTradeRecordService tradeRecordService;

    @Resource
    private ICreditApplyService creditApplyService;

    /**
     * 分页查询授信
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @GetMapping("/pageCredit")
    public WrapperResp<PageDataResp<CreditQueryAppResp>> pageCredit(HttpServletRequest httpRequest, @Valid PageQueryCreditDataReq pageVO) {
        return WrapperUtil.success(creditService.pageCreditByApp(getSysUser(httpRequest), pageVO));
    }

    /**
     * 查询授信详情
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @GetMapping("/getCreditDetail")
    public WrapperResp<CreditDetailResp> getDetail(HttpServletRequest httpRequest, Long id) {
        return WrapperUtil.success(creditService.getDetail(getSysUser(httpRequest), id, false));
    }

    /**
     * 查询账单详情
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @GetMapping("/getCreditBillDetail")
    public WrapperResp<CreditBillDetailResp> getBillDetail(HttpServletRequest httpRequest, Long id) {
        getSysUser(httpRequest);
        return WrapperUtil.success(billService.getDetailByMobile(id));
    }

    /**
     * 分页查询交易记录
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @GetMapping("/pageCreditTradeRecord")
    public WrapperResp<PageDataResp<CreditTradeRecordPageAppResp>> pageTradeRecord(@Valid PageQueryCreditTradeRecordDataReq pageVO) {
        return WrapperUtil.success(tradeRecordService.pageTradeRecordByApp(pageVO));
    }

    /**
     * 分页查询逾期记录
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @GetMapping("/pageCreditOverdue")
    public WrapperResp<PageDataResp<CreditOverduePageQueryAppResp>> pageOverdue(PageQueryCreditOverdueDataReq pageVO) {
        return WrapperUtil.success(billService.pageOverdueByApp(pageVO));
    }

    /**
     * 立即还款
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @PostMapping("/creditRepay")
    public WrapperResp<CreditRepayResultResp> creditRepay(HttpServletRequest httpRequest, @RequestBody @Valid CreditRepayReq vo) {
        return WrapperUtil.success(creditService.repay(getSysUser(httpRequest), vo, httpRequest));
    }

    /**
     * 查询还款结果
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @GetMapping("/getCreditRepayResult")
    public WrapperResp<CreditRepayResultResp> getCreditRepayResult(HttpServletRequest httpRequest, Long recordId) {
        return WrapperUtil.success(creditService.getCreditRepayResult(getSysUser(httpRequest), recordId));
    }

    /**
     * 分页查询授信申请
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @GetMapping("/pageCreditApply")
    public WrapperResp<PageDataResp<CreditApplyPageResp>> pageCreditApply(HttpServletRequest httpRequest, PageQueryCreditApplyDataReq pageVO) {
        return WrapperUtil.success(creditApplyService.pageApplyByLower(getSysUser(httpRequest), pageVO));
    }

    /**
     * 保存授信申请
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @PostMapping("/addCreditApply")
    public WrapperResp<Long> saveCreditApply(HttpServletRequest httpRequest, @RequestBody @Valid CreditApplyAddReq addVO) {
        return WrapperUtil.success(creditApplyService.add(getSysUser(httpRequest), addVO));
    }

    /**
     * 获取授信申请详情
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @GetMapping("/getApplyDetail")
    WrapperResp<CreditApplyDetailResp> getApplyDetail(HttpServletRequest httpRequest, @Valid CreditApplyGetDetailReq getDetailVO) {
        return WrapperUtil.success(creditApplyService.getDetail(getSysUser(httpRequest), getDetailVO, false));
    }

    /**
     * 删除授信申请
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @PostMapping("/deleteCreditApply")
    public WrapperResp<Void> delete(HttpServletRequest httpRequest, @RequestBody CreditApplySubmitReq vo) {
        creditApplyService.delete(getSysUser(httpRequest), vo);
        return WrapperUtil.success();
    }

    /**
     * 提交授信申请
     * <AUTHOR>
     * @since 2020/12/30
     **/
    @RequestMapping("/submitCreditApply")
    public WrapperResp<Void> submitCreditApply(HttpServletRequest httpRequest, @RequestBody CreditApplySubmitReq submitVO) {
        creditApplyService.submit(getSysUser(httpRequest), submitVO);
        return WrapperUtil.success();
    }

    /**
     * 获取可用余额
     * <AUTHOR>
     * @since 2021/1/30
     **/
    @RequestMapping("/getCanUseQuota")
    public WrapperResp<BigDecimal> getCanUseQuota(HttpServletRequest httpRequest) {
        return WrapperUtil.success(creditService.getCanUseQuota(getSysUser(httpRequest)));
    }

    /**
     * 获取授信
     * <AUTHOR>
     * @since 2021/2/23
     **/
    @GetMapping("/getCredit")
    public WrapperResp<CreditResp> getCredit(HttpServletRequest httpRequest, @Valid CreditReq request) {
        return WrapperUtil.success(creditService.getCredit(getSysUser(httpRequest), request));
    }

    /**
     * 获取会员授信
     * <AUTHOR>
     * @since 2021/2/23
     **/
    @GetMapping("/getMemberCredit")
    public WrapperResp<CreditResp> getMemberCredit(HttpServletRequest httpRequest, @Valid CreditRequestReq request) {
        return WrapperUtil.success(creditService.getMemberCredit(getSysUser(httpRequest), request));
    }

    /**
     * 检查是否已申请授信
     * <AUTHOR>
     * @since 2021/3/9
     **/
    @GetMapping("/existApplyCredit")
    public WrapperResp<Boolean> existApplyCredit(HttpServletRequest httpRequest) {
        return WrapperUtil.success(creditService.existApplyCredit(getSysUser(httpRequest)));
    }

    /**
     * 查询授信调额记录
     * <AUTHOR>
     * @since 2021/3/9
     **/
    @GetMapping("/getAdjustQuotaList")
    public WrapperResp<List<CreditAdjustQuotaRecordResp>> getAdjustQuotaList(HttpServletRequest httpRequest, Long creditId) {
        return WrapperUtil.success(creditApplyService.getAdjustQuotaList(getSysUser(httpRequest), creditId));
    }
}
