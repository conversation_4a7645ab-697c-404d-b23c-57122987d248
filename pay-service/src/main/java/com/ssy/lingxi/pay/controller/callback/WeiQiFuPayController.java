package com.ssy.lingxi.pay.controller.callback;

import com.alibaba.fastjson.JSONObject;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.rest.model.req.eos.RechargeAccountReq;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayCallbackFeignReq;
import com.ssy.lingxi.pay.api.enums.ServiceTypeEnum;
import com.ssy.lingxi.pay.api.feign.IAssetAccountFeign;
import com.ssy.lingxi.pay.service.assetAccount.IMemberAssetAccountService;
import com.tenpay.business.entpay.mse.sdk.api.Payment;
import com.tenpay.business.entpay.mse.sdk.common.NotifyHandler;
import com.tenpay.business.entpay.mse.sdk.config.EntpayConfig;
import com.tenpay.business.entpay.mse.sdk.exception.ApiException;
import com.tenpay.business.entpay.mse.sdk.exception.EntpayException;
import com.tenpay.business.entpay.mse.sdk.model.PaymentNotifyModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 微企付支付回调
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/16
 */
@Slf4j
@RestController
@RequestMapping(ServiceModuleConstant.PAY_PATH_PREFIX + "/weiqifu")
public class WeiQiFuPayController {

    @Resource
    private IOrderProcessFeign orderFeignService;
    @Resource
    private IMemberAssetAccountService memberAssetAccountService;

    /**
     * 支付通知接口
     * 1. 首先记录微企付流水
     * 2. 进行业务操作
     */
    @ResponseBody
    @RequestMapping("/notify/{serviceType}")
    public String paymentNotify(HttpServletRequest request, @RequestBody String body,
                                     @RequestHeader("TBEP-Authorization") String authorization, @PathVariable("serviceType") String serviceType) throws ApiException {
        StringBuffer requestURL = request.getRequestURL();
        // 验签并获取model
        PaymentNotifyModel model = NotifyHandler.handlerWebhook(body, authorization, PaymentNotifyModel.class,
                EntpayConfig.getRealTbepPublicKey(null));
        log.info("微企付支付回调通知:{}, serviceType:{}", model, serviceType);

        if ("SUCCEEDED".equals(model.getEventContent().getPayStatus())) {
            // 支付查询(外单号)
            Payment payment = null;
            try {
                payment = Payment.retrieveByOutPaymentId(model.getEventContent().getOutPaymentId());
                log.info("微企付支付查询结果:{}", payment);
            } catch (EntpayException e) {
                throw new RuntimeException(e);
            }
            // 业务处理
            if (ServiceTypeEnum.Order_Pay.getCode().equals(serviceType)) {
                OrderPayCallbackFeignReq orderPayCallbackFeignReq = new OrderPayCallbackFeignReq();
                orderPayCallbackFeignReq.setTradeNo(model.getEventContent().getOutPaymentId());
                orderPayCallbackFeignReq.setChannelTradeNo(model.getEventContent().getPaymentId());
                orderPayCallbackFeignReq.setAttach(payment.getAttachment());
                orderPayCallbackFeignReq.setPaySuccess(true);
                WrapperResp<Void> orderCallbackResult = orderFeignService.orderPayCallback(orderPayCallbackFeignReq);
                log.info(ServiceTypeEnum.Order_Pay.getMessage() + ":==========attach：" + payment.getAttachment() + " , 支付成功通知结果" + orderCallbackResult.getMessage());
            }else if(ServiceTypeEnum.Pay_Recharge.getCode().equals(serviceType)){
                memberAssetAccountService.payNotify(model.getEventContent().getOutPaymentId(), model.getEventContent().getPaymentId());
            }
        }

        // 封装响应参数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("retcode", 0);
        jsonObject.put("retmsg", "SUCCESS");
        return jsonObject.toString();
    }
}
