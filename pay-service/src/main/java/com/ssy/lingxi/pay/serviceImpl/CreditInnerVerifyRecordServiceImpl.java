package com.ssy.lingxi.pay.serviceImpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.ssy.lingxi.pay.entity.do_.CreditInnerVerifyRecordDO;
import com.ssy.lingxi.pay.enums.CreditApplySuperiorActionEnum;
import com.ssy.lingxi.pay.enums.CreditApplySuperiorInnerStatusEnum;
import com.ssy.lingxi.pay.model.resp.CreditInnerVerifyRecordResp;
import com.ssy.lingxi.pay.repository.CreditInnerVerifyRecordRepository;
import com.ssy.lingxi.pay.service.ICreditInnerVerifyRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class CreditInnerVerifyRecordServiceImpl implements ICreditInnerVerifyRecordService {

    @Resource
    private CreditInnerVerifyRecordRepository recordRepository;

    /**
     * 获取审核历史记录
     * <AUTHOR>
     * @since 2020/8/20
     * @param applyId:
     **/
    @Override
    public List<CreditInnerVerifyRecordResp> getHistoryList(Long applyId) {

        // step 1: 分页查询数据
        List<CreditInnerVerifyRecordDO> recordDOList = recordRepository.findByApplyIdOrderByOperateTimeAsc(applyId);

        if(CollUtil.isEmpty(recordDOList)){
            return new ArrayList<>();
        }
        // step 2: 组装返回数据
        return recordDOList.stream().map(r -> {
            CreditInnerVerifyRecordResp queryVO = new CreditInnerVerifyRecordResp();
            queryVO.setStep(r.getStep());
            queryVO.setOperator(r.getOperator());
            queryVO.setDepartment(r.getDepartment());
            queryVO.setJobTitle(r.getJobTitle());
            queryVO.setStatus(r.getStatus());
            queryVO.setOperate(CreditApplySuperiorActionEnum.getItemMessage(r.getStep()));
            queryVO.setOperateTime(DateUtil.format(DateUtil.date(r.getOperateTime()), "yyyy-MM-dd HH:mm"));
            queryVO.setOpinion(r.getOpinion());
            queryVO.setStatusName(CreditApplySuperiorInnerStatusEnum.getItemMessage(r.getStatus()));
            return queryVO;
        }).collect(Collectors.toList());
    }
}
