package com.ssy.lingxi.pay.serviceImpl.assetAccount;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.github.binarywang.wxpay.bean.order.WxPayMwebOrderResult;
import com.google.common.collect.Sets;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.constant.mq.PayMqConstant;
import com.ssy.lingxi.common.enums.DataSourceEnum;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.req.api.member.ActualControllerSyncReq;
import com.ssy.lingxi.common.model.req.api.member.CustomerCreditSync;
import com.ssy.lingxi.common.model.req.api.pay.MaterialStockDataSyncReq;
import com.ssy.lingxi.common.model.req.api.pay.MemberAssetAccountLineOfCreditSyncReq;
import com.ssy.lingxi.common.model.req.api.product.GoldPriceSyncReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.BigDecimalUtil;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.common.util.UUIDUtil;
import com.ssy.lingxi.component.base.config.BaiTaiMemberProperties;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.SystemSourceEnum;
import com.ssy.lingxi.component.base.enums.manage.MessageNoticeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberLevelTypeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.member.MemberTypeEnum;
import com.ssy.lingxi.component.base.enums.order.BalancePayMethodEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayTypeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPaymentParameterEnum;
import com.ssy.lingxi.component.base.enums.pay.DeposiAccountTypeEnum;
import com.ssy.lingxi.component.base.enums.pay.LineOfCreditStatusEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.service.IMessageService;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.datasheetFile.utils.ExcelExportUtil;
import com.ssy.lingxi.component.rabbitMQ.model.req.SystemMessageReq;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.component.rest.model.req.eos.RechargeAccountReq;
import com.ssy.lingxi.component.rest.model.resp.eos.GetAccountInfoResp;
import com.ssy.lingxi.component.rest.model.resp.eos.GoldPriceResp;
import com.ssy.lingxi.component.rest.service.EosApiService;
import com.ssy.lingxi.member.api.enums.MemberPayPasswordCheckEnum;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.req.GetMemberByIdReq;
import com.ssy.lingxi.member.api.model.req.MemberFeignCodeReq;
import com.ssy.lingxi.member.api.model.resp.MemberBrandInfoResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignCodeRes;
import com.ssy.lingxi.member.api.model.req.MemberFeignPayPswCheckReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignPayPswCheckResultResp;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayChannelFeignReq;
import com.ssy.lingxi.order.api.model.req.OrderPayParameterFeignReq;
import com.ssy.lingxi.order.api.model.resp.PaymentParameterFeignDetailResp;
import com.ssy.lingxi.pay.api.constant.WeiqifuStatusConstant;
import com.ssy.lingxi.pay.api.enums.*;
import com.ssy.lingxi.pay.api.model.req.CheckAssetAccountReq;
import com.ssy.lingxi.pay.api.model.req.MemberIdsReq;
import com.ssy.lingxi.pay.api.model.req.MobilePayReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.*;
import com.ssy.lingxi.pay.api.model.req.weiQiFuPay.GoodsReq;
import com.ssy.lingxi.pay.api.model.req.weiQiFuPay.WeiQiFuPayReq;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.*;
import com.ssy.lingxi.pay.api.model.resp.weiQiFuPay.WeiQiFuPayResp;
import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountCashOutRecordDO;
import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountStatusRecordDO;
import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountTradeRecordDO;
import com.ssy.lingxi.pay.entity.do_.assetAccount.MemberAssetAccountDO;
import com.ssy.lingxi.pay.enums.*;
import com.ssy.lingxi.pay.model.req.AddMemberAccountTradeRecordReq;
import com.ssy.lingxi.pay.model.req.AsynGetPayResultReq;
import com.ssy.lingxi.pay.model.req.MemberAssetAccountReq;
import com.ssy.lingxi.pay.model.resp.WeChatPayResultResp;
import com.ssy.lingxi.pay.repository.assetAccount.AccountCashOutRecordRepository;
import com.ssy.lingxi.pay.repository.assetAccount.AccountStatusRecordRepository;
import com.ssy.lingxi.pay.repository.assetAccount.AccountTradeRecordRepository;
import com.ssy.lingxi.pay.repository.assetAccount.MemberAssetAccountRepository;
import com.ssy.lingxi.pay.service.IPayCacheService;
import com.ssy.lingxi.pay.service.IWeChatPayService;
import com.ssy.lingxi.pay.service.WeiQiFuPayService;
import com.ssy.lingxi.pay.service.assetAccount.IAccountTradeService;
import com.ssy.lingxi.pay.service.assetAccount.IMemberAssetAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员资金账户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
@Slf4j
@Service
public class MemberAssetAccountServiceImpl implements IMemberAssetAccountService {
    @Resource
    private IPayCacheService payCacheService;

    @Resource
    private MemberAssetAccountRepository memberAssetAccountRepository;

    @Resource
    private AccountTradeRecordRepository accountTradeRecordRepository;

    @Resource
    private AccountStatusRecordRepository accountStatusRecordRepository;

    @Resource
    private AccountCashOutRecordRepository accountCashOutRecordRepository;

    @Resource
    private IWeChatPayService weChatPayService;

    @Resource
    private IMemberFeign memberInnerControllerFeign;

    @Resource
    private IOrderProcessFeign orderFeignService;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private IMqUtils mqUtils;

    @Resource
    private IMessageService messageService;

    @Resource
    private IMemberFeign memberFeign;

    @Resource
    private BaiTaiMemberProperties baiTaiMemberProperties;
    @Resource
    private EosApiService eosApiService;
    @Resource
    private WeiQiFuPayService weiQiFuPayService;
    @Resource
    private IAccountTradeService accountTradeService;

    /**
     * 查询会员资金账户列表
     * @param pageDataReq 分页参数
     * @param memberAssetAccountMiddle 接口参数
     * @return 查询结果
     */
    @Override
    public Page<MemberAssetAccountDO> getMemberAssetAccountList(PageDataReq pageDataReq, MemberAssetAccountReq memberAssetAccountMiddle) {
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize());
        return memberAssetAccountRepository.findAll(getSpecification(memberAssetAccountMiddle), page);
    }


    /**
     * 查询会员资金账户列表V2
     * @param pageQueryReq 分页参数
     * @param memberAssetAccountReq 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<MemberAssetAccountResp> getMemberAssetAccountListV2(MemberAssetAccountPageQueryReq pageQueryReq, MemberAssetAccountReq memberAssetAccountReq) {
        Pageable page = PageRequest.of(pageQueryReq.getCurrent() - 1, pageQueryReq.getPageSize());
        Specification<MemberAssetAccountDO> specification = getPageQuerySpecification(memberAssetAccountReq, pageQueryReq);
        BigDecimal goldPrice = getGoldPrice();
        Page<MemberAssetAccountDO> memberAssetAccountDOS = memberAssetAccountRepository.findAll(specification, page);
        List<MemberAssetAccountResp> resultList = memberAssetAccountDOS.stream().map(memberAssetAccountDO -> {
            MemberAssetAccountResp memberAssetAccountResp = convert(memberAssetAccountDO, goldPrice);
            return memberAssetAccountResp;
        }).collect(Collectors.toList());
        return new PageDataResp<MemberAssetAccountResp>(memberAssetAccountDOS.getTotalElements(), resultList);
    }

    /**
     * 计算今日账户可用克重
     * @param accountDO 账户对象
     * @param goldPrice 今日金价
     * @return 可用克重
     */
    private BigDecimal calculateUnUseAccountBalance(MemberAssetAccountDO accountDO, BigDecimal goldPrice) {
        if (accountDO == null || goldPrice == null || goldPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal materialStock = accountDO.getMaterialStock() != null ? accountDO.getMaterialStock() : BigDecimal.ZERO;
        BigDecimal lockMaterialStock = accountDO.getLockMaterialStock() != null ? accountDO.getLockMaterialStock() : BigDecimal.ZERO;
        BigDecimal lineOfCredit = accountDO.getLineOfCredit() != null ? accountDO.getLineOfCredit() : BigDecimal.ZERO;
        BigDecimal accountBalance = accountDO.getAccountBalance() != null ? accountDO.getAccountBalance() : BigDecimal.ZERO;
        BigDecimal lockBalance = accountDO.getLockBalance() != null ? accountDO.getLockBalance() : BigDecimal.ZERO;
        // （存料g - 冻结g）+ 授信额度g +（（存款 - 冻结存款）/ 今日金价）
        BigDecimal calcResult = materialStock.subtract(lockMaterialStock)
                .add(lineOfCredit);
        if(BigDecimalUtil.notNullOrZero(goldPrice)){
            calcResult = calcResult.add(accountBalance.subtract(lockBalance).divide(goldPrice, 2, RoundingMode.HALF_UP));
        }
        return calcResult;
    }


    /**
     * 检查远程调用响应会员信息结果
     * @param queryRespWrapperResp
     * @return
     */
    private MemberFeignCodeRes getMemberInfo(WrapperResp<MemberFeignCodeRes> queryRespWrapperResp) {

        if(!WrapperUtil.isOk(queryRespWrapperResp)){
            throw new BusinessException(queryRespWrapperResp.getMessage());
        }
        return WrapperUtil.getData(queryRespWrapperResp);
    }

    /**
     * 同步会员资金账户信息 - 授信额度
     * @param memberAssetAccountSyncReq
     * @return
     */
    @Override
    public void memberAssetAccountLineOfCreditSync(MemberAssetAccountLineOfCreditSyncReq memberAssetAccountSyncReq) {
        MemberFeignCodeReq memberFeignCodeReq = new MemberFeignCodeReq();
        memberFeignCodeReq.setCode(memberAssetAccountSyncReq.getZkhbm());
        WrapperResp<MemberFeignCodeRes> queryRespWrapperResp = memberFeign.findByCode(memberFeignCodeReq);
        MemberFeignCodeRes memberResult = getMemberInfo(queryRespWrapperResp);
        //不存在则不同步
        if(memberResult == null) {
            return;
        }
        List<MemberAssetAccountDO> accountDOList = memberAssetAccountRepository.findByMemberIdAndParentMemberRoleIdAndParentMemberIdAndMemberRoleId(memberResult.getId(), baiTaiMemberProperties.getSelfRoleId(), baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getCustomerRoleId());
        if(!CollectionUtils.isEmpty(accountDOList)){
            MemberAssetAccountDO memberAssetAccountDO = accountDOList.get(0);
            memberAssetAccountDO.setLineOfCredit(memberAssetAccountSyncReq.getSxjl());
            memberAssetAccountDO.setSyncUpdateTime(DateTimeUtil.getTodayNowLocal());
            memberAssetAccountDO.setLineOfCreditUseEndTime(DateTimeUtil.parseDate(memberAssetAccountSyncReq.getSxjzsj()));
            memberAssetAccountRepository.save(memberAssetAccountDO);
        }
    }


    /**
     * 同步会员资金账户信息 -
     * @param materialStockDataSyncReq
     * @return
     */
    @Transactional
    @Override
    public void memberAssetAccountMaterialStockSync(MaterialStockDataSyncReq materialStockDataSyncReq) {
        if(CollectionUtils.isEmpty(materialStockDataSyncReq.getBatch())){
            return;
        }
        List<MaterialStockDataSyncReq.DataSchemaInfo> materialStockDataSyncReqBatch = materialStockDataSyncReq.getBatch();
        List<MaterialStockDataSyncReq.MemberInfo> memberInfos = materialStockDataSyncReqBatch.stream().filter(Objects::nonNull).map(MaterialStockDataSyncReq.DataSchemaInfo::getRow).collect(Collectors.toList());
        Set<String> memberCodeSet = memberInfos.stream().filter(Objects::nonNull).map(MaterialStockDataSyncReq.MemberInfo::getKhbm).filter(StringUtils::hasLength).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(memberCodeSet)) {
            log.info("同步存欠数据的客户编码为空");
            return;
        }
        long currentTimeMillis = System.currentTimeMillis();
        List<GetAccountInfoResp> eosAccountInfos = eosApiService.getAccountInfo(new ArrayList<>(memberCodeSet));
        log.info("同步存欠数据，查询耗时：{}", System.currentTimeMillis() - currentTimeMillis);
        if(CollectionUtils.isEmpty(eosAccountInfos)){
            log.error("同步会员资金账户信息 - 资金账户信息失败, EOS返回的账户信息为空, 会员编号: {}", memberCodeSet);
            return;
        }
        List<MemberAssetAccountDO> accountDOList = memberAssetAccountRepository.findByParentMemberRoleIdAndParentMemberIdAndMemberRoleIdAndMemberCodeIn(baiTaiMemberProperties.getSelfRoleId(), baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getCustomerRoleId(), memberCodeSet);
        if(CollectionUtils.isEmpty(accountDOList)){
            log.error("同步会员资金账户信息 - 资金账户信息失败, 会员资金账户不存在, 会员编号: {}", memberCodeSet);
            return;
        }
        Map<String, GetAccountInfoResp> eosAccountInfoMap = eosAccountInfos.stream().collect(Collectors.toMap(GetAccountInfoResp::getKhbm, eosAccountInfo -> eosAccountInfo));
        Map<String, MemberAssetAccountDO> assetAccountDOMap = accountDOList.stream().collect(Collectors.toMap(MemberAssetAccountDO::getMemberCode, account -> account));
        List<MemberAssetAccountDO> memberAssetAccountDOs = new ArrayList<>();
        assetAccountDOMap.keySet().forEach(key -> {
            GetAccountInfoResp getAccountInfoResp = eosAccountInfoMap.get(key);
            MemberAssetAccountDO memberAssetAccountDO = assetAccountDOMap.get(key);
            if(getAccountInfoResp == null) {
                log.error("同步会员资金账户信息 - 资金账户信息失败, EOS会员资金账户不存在, 会员编号: {}", key);
                return;
            }
            memberAssetAccountDO.setSyncUpdateTime(DateTimeUtil.getTodayNowLocal());
            memberAssetAccountDO.setMaterialStock(BigDecimalUtil.nullToZero(getAccountInfoResp.getQmjz_jl()));
            memberAssetAccountDO.setAccountBalance(BigDecimalUtil.nullToZero(getAccountInfoResp.getQmje()));
            memberAssetAccountDOs.add(memberAssetAccountDO);
        });
        memberAssetAccountRepository.saveAll(memberAssetAccountDOs);
    }


    /**
     * 查询会员资金账户
     * @param id 账户Id
     * @return MemberAssetAccount
     */
    @Override
    public MemberAssetAccountDO getMemberAssetAccount(Long id) {
        return memberAssetAccountRepository.findById(id).orElse(null);
    }


    /**
     * 查询会员资金账户
     * @param id 账户Id
     * @return MemberAssetAccount
     */
    @Override
    public MemberAssetAccountResp getMemberAssetAccountV2(Long id) {
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountRepository.findById(id).orElse(null);
        if(memberAssetAccountDO ==null){
            log.error("查询会员资金账户失败, 账户不存在, id: {}", id);
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
        BigDecimal goldPrice = getGoldPrice();
        MemberAssetAccountResp memberAssetAccountResp = convert(memberAssetAccountDO,goldPrice);
        return memberAssetAccountResp;
    }

    /**
     * 获取金价
     * @return
     */
    private BigDecimal getGoldPrice(){
        String goldPriceStr = redisUtils.stringGet(Constant.GOLD_PRICE_KEY, RedisConstant.REDIS_PRODUCT_INDEX);
        if(StringUtils.hasText(goldPriceStr)){
            GoldPriceSyncReq goldPriceSyncReq = JSON.parseObject(goldPriceStr, GoldPriceSyncReq.class);
            if(!Objects.isNull(goldPriceSyncReq) && goldPriceSyncReq.getJj() != null && goldPriceSyncReq.getJj().compareTo(BigDecimal.ZERO) > 0){
                return goldPriceSyncReq.getJj();
            }
        }
        GoldPriceResp goldPrice = eosApiService.getGoldPrice();
        return goldPrice.getJj();
    }

    /**
     * 对象转换
     * @param memberAssetAccountDO
     * @return
     */
    private MemberAssetAccountResp convert(MemberAssetAccountDO memberAssetAccountDO, BigDecimal goldPrice) {
        MemberAssetAccountResp memberAssetAccountResp = new MemberAssetAccountResp();
        BeanUtils.copyProperties(memberAssetAccountDO, memberAssetAccountResp);
        memberAssetAccountResp.setGoldPrice(BigDecimalUtil.nullToZero(goldPrice));
        if (BigDecimalUtil.notNullOrZero(goldPrice)) {
            memberAssetAccountResp.setAccountBalanceMap(memberAssetAccountDO.getAccountBalance().divide(goldPrice, 2, RoundingMode.HALF_UP));
        }
        BigDecimal canUseMaterialStock = calcCanUseMaterialStock(memberAssetAccountDO, goldPrice);
        BigDecimal canUseBalance = calcCanUseBalance(memberAssetAccountDO, goldPrice);
        //memberAssetAccountResp.setUnUsedAccountBalanceCash(calcCanUseBalance(memberAssetAccountDO, goldPrice));
        memberAssetAccountResp.setMaterialStock(BigDecimalUtil.nullToZero(memberAssetAccountDO.getMaterialStock()));
        memberAssetAccountResp.setMaterialStockAndCredit(canUseMaterialStock);
        memberAssetAccountResp.setUnUsedAccountBalance(canUseBalance.divide(goldPrice, 2, RoundingMode.HALF_UP));
        memberAssetAccountResp.setUnUsedAccountBalanceCash(canUseBalance);
        memberAssetAccountResp.setTotalAmount(BigDecimalUtil.add(canUseMaterialStock, memberAssetAccountResp.getUnUsedAccountBalance()));
        LocalDate lineOfCreditUseStartTime = memberAssetAccountDO.getLineOfCreditUseStartTime();
        LocalDate lineOfCreditUseEndTime = memberAssetAccountDO.getLineOfCreditUseEndTime();
        if(lineOfCreditUseStartTime !=null && lineOfCreditUseEndTime != null && DateTimeUtil.isBelongPeriodOfNow(lineOfCreditUseStartTime.atTime(LocalTime.MIN), lineOfCreditUseEndTime.atTime(LocalTime.MAX))) {
            memberAssetAccountResp.setLineOfCreditStatus(LineOfCreditStatusEnum.EFFECTIVE.getCode());
            memberAssetAccountResp.setLineOfCreditStatusName(LineOfCreditStatusEnum.EFFECTIVE.getName());
        }else{
            memberAssetAccountResp.setLineOfCreditStatus(LineOfCreditStatusEnum.INVALID.getCode());
            memberAssetAccountResp.setLineOfCreditStatusName(LineOfCreditStatusEnum.INVALID.getName());
        }
        memberAssetAccountResp.setMemberTypeName(MemberTypeEnum.getName(memberAssetAccountDO.getMemberType()));
        memberAssetAccountResp.setSyncUpdateTime(DateTimeUtil.formatDateTime(memberAssetAccountDO.getSyncUpdateTime()));
        memberAssetAccountResp.setLineOfCreditUseEndTimeStr(DateTimeUtil.formatDate(memberAssetAccountDO.getLineOfCreditUseEndTime()));
        memberAssetAccountResp.setLineOfCreditUseStartTimeStr(DateTimeUtil.formatDate(memberAssetAccountDO.getLineOfCreditUseStartTime()));
        return memberAssetAccountResp;
    }

    /**
     * 账户充值
     * @param depositAccountReq
     * @return
     */
    @Override
    public void depositAccount(DepositAccountReq depositAccountReq) {
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountRepository.findById(depositAccountReq.getAccountId()).orElseThrow(() -> {
            log.error("充值账户不存在, 账户id: {}", depositAccountReq.getAccountId());
            return new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        });
        //验证会员状态
        checkAccountStatus(memberAssetAccountDO);
        GetMemberByIdReq getMemberByIdReq = new GetMemberByIdReq();
        getMemberByIdReq.setMemberId(memberAssetAccountDO.getMemberId());
        WrapperResp<MemberFeignCodeRes> queryRespWrapperResp = memberFeign.findMemberById(getMemberByIdReq);
        MemberFeignCodeRes memberResult = getMemberInfo(queryRespWrapperResp);
        //不存在则不同步
        if(memberResult == null) {
            log.error("充值会员信息不存在, 会员编号: {}", memberAssetAccountDO.getMemberId());
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
        RechargeAccountReq rechargeAccountReq = new RechargeAccountReq();
        rechargeAccountReq.setBz(depositAccountReq.getRemark());
        rechargeAccountReq.setKhbm(String.valueOf(queryRespWrapperResp.getData().getCode()));
        rechargeAccountReq.setDzje(depositAccountReq.getDepositNum());
        rechargeAccountReq.setZfsj(depositAccountReq.getPayTime());
        rechargeAccountReq.setZflsh(depositAccountReq.getPayCode());
        rechargeAccountReq.setZfqd(depositAccountReq.getTypeName());
        eosApiService.rechargeAccount(rechargeAccountReq);
    }

    @Override
    public void updateLineOfCredit(UpdateLineOfCreditReq updateLineOfCreditReq) {
        memberAssetAccountRepository.findById(updateLineOfCreditReq.getAccountId()).ifPresent(memberAssetAccountDO -> {
            BigDecimal lineOfCredit = BigDecimalUtil.nullToZero(memberAssetAccountDO.getLineOfCredit());
            memberAssetAccountDO.setLineOfCredit(lineOfCredit.add(updateLineOfCreditReq.getAmount()));
            memberAssetAccountRepository.save(memberAssetAccountDO);
        });
    }


    /**
     * 冻结账户额度
     * 当使用余料付款时：使用余料付款不足时，可以使用余额进行抵押 (当前)
     *
     * @param frozenAccountBalanceReq
     * @return
     */
    @Transactional
    @Override
    public AccountPayChannelResultResp frozenAccountBalance(FrozenAccountBalanceReq frozenAccountBalanceReq) {
        List<MemberAssetAccountDO> memberAssetAccountDOs = memberAssetAccountRepository.findByMemberIdAndParentMemberRoleIdAndParentMemberIdAndMemberRoleId(frozenAccountBalanceReq.getMemberId(), baiTaiMemberProperties.getSelfRoleId(), baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getCustomerRoleId());
        if(CollectionUtils.isEmpty(memberAssetAccountDOs)){
            log.error("冻结账户失败, 会员资金账户不存在, 会员id: {}", frozenAccountBalanceReq.getMemberId());
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountDOs.get(0);
        // 1、根据订单号计算具体冻结克重
        String memberCode = memberAssetAccountDO.getMemberCode();
        List<String> codes = Arrays.asList(memberCode);
        List<GetAccountInfoResp> eosAccountInfo = eosApiService.getAccountInfo(codes);
        if(CollectionUtils.isEmpty(eosAccountInfo)){
            log.error("冻结账户失败, 尚金源返回的账户信息为空, 账户编号: {}", memberCode);
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
        GetAccountInfoResp getAccountInfoResp = eosAccountInfo.get(0);
        checkAccountStatus(memberAssetAccountDO);
        BigDecimal qmje = BigDecimalUtil.nullToZero(getAccountInfoResp.getQmje());
        BigDecimal qmjzJl = BigDecimalUtil.nullToZero(getAccountInfoResp.getQmjz_jl());
        memberAssetAccountDO.setAccountBalance(qmje);
        memberAssetAccountDO.setMaterialStock(qmjzJl);
        BigDecimal oldLockBalance = BigDecimalUtil.nullToZero(memberAssetAccountDO.getLockBalance());
        BigDecimal oldMaterialStock = BigDecimalUtil.nullToZero(memberAssetAccountDO.getMaterialStock());
        BigDecimal unUseMaterialStock = BigDecimal.ZERO;
        boolean accountBalanceUsed = false;
        List<UnPayOrderInfo> unPayOrderResult = new ArrayList<>();
        List<AccountBalancePaidResp> accountBalancePaidOrderResult = new ArrayList<>();
        BigDecimal goldPrice = frozenAccountBalanceReq.getGoldPrice();
        String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);
        List<AddMemberAccountTradeRecordReq> addTradeRecordReqs = new ArrayList<>();
        List<Integer> otherPayChannels = frozenAccountBalanceReq.getBalancePayMethods().stream().filter(e -> !BalancePayMethodEnum.MATERIAL_STOCK.getCode().equals(e) && !BalancePayMethodEnum.BALANCE.getCode().equals(e)).collect(Collectors.toList());
        //每个订单+每个支付方式(授信/存料/存款)都要生成一条支付记录（退款使用）
        for (OrderPayInfoReq orderPayInfoReq : frozenAccountBalanceReq.getOrderPayInfoReqs()) {
            //true,表示钱包余额用完
            if(accountBalanceUsed){
                UnPayOrderInfo unPayOrderInfo = new UnPayOrderInfo();
                unPayOrderInfo.setOrderNo(orderPayInfoReq.getOrderNo());
                unPayOrderInfo.setUnPayAmount(orderPayInfoReq.getPayAmount());
                unPayOrderInfo.setUnPayOtherFee(orderPayInfoReq.getOtherAmount());
                unPayOrderInfo.setUnPayGoldWeight(orderPayInfoReq.getGoldWeight());
                unPayOrderInfo.setOrderPaymentId(orderPayInfoReq.getOrderPaymentId());
                unPayOrderResult.add(unPayOrderInfo);
                continue;
            }
            BigDecimal unPayGoldWeight = orderPayInfoReq.getGoldWeight();

            //余料支付
            if(BalancePayMethodEnum.isMaterialStockPayMethod(frozenAccountBalanceReq.getBalancePayMethods())){
                if(BalancePayMethodEnum.isMaterialStockPayMethod(frozenAccountBalanceReq.getBalancePayMethods()) && frozenAccountBalanceReq.getBalancePayMethods().size() == 1 && !BigDecimalUtil.isNullOrZero(orderPayInfoReq.getOtherAmount())) {
                    throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_PAY_METHOD_EXCEPTION);
                }
                unUseMaterialStock = calcCanUseMaterialStock(memberAssetAccountDO,  goldPrice);
                BigDecimal lockGoleWeight = null;
                //授信支付逻辑（先扣除库存，如果库存不足，在获取授信额度扣除，在扣除钱包余额）
                if(BigDecimalUtil.isGreaterThan(unUseMaterialStock, unPayGoldWeight)){
                    lockGoleWeight = unPayGoldWeight;
                    unPayGoldWeight = BigDecimal.ZERO;
                }else{
                    unPayGoldWeight = unPayGoldWeight.subtract(unUseMaterialStock);
                    lockGoleWeight = unUseMaterialStock;
                    //当只使用了授信支付，并且余料不足时，可以用余额进行抵押，但不实际扣款
                    if(!frozenAccountBalanceReq.getBalancePayMethods().contains(OrderPayChannelEnum.ACCOUNT_BALANCE.getCode())) {
                        BigDecimal balanceToMaterial = calcCanUseBalance(memberAssetAccountDO, goldPrice).divide(goldPrice,2,BigDecimal.ROUND_HALF_UP);
                        if(frozenAccountBalanceReq.getBalancePayMethods().size() ==1 && BigDecimalUtil.isGreaterThan(unPayGoldWeight, balanceToMaterial)){
                            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_MORE_EXCEPTION);
                        }
                        //BigDecimal canUseBalance = calcCanUseBalance(memberAssetAccountDO, goldPrice);
                        lockGoleWeight = BigDecimalUtil.add(lockGoleWeight, unPayGoldWeight);
                    }
                }

                memberAssetAccountDO.setLockMaterialStock(BigDecimalUtil.add(memberAssetAccountDO.getLockMaterialStock(), lockGoleWeight));

                //记录交易记录
                if(BigDecimalUtil.isGreaterThan(lockGoleWeight, BigDecimal.ZERO)){
                    AddMemberAccountTradeRecordReq addTradeRecordReq = new AddMemberAccountTradeRecordReq();
                    BigDecimal materialStockBefore = BigDecimalUtil.subtract(qmjzJl, oldMaterialStock);
                    addTradeRecordReq.setBalanceAfter(materialStockBefore.subtract(lockGoleWeight));
                    addTradeRecordReq.setBalanceChange(lockGoleWeight);
                    addTradeRecordReq.setBalanceBefore(materialStockBefore);
                    addTradeRecordReq.setTradeTime(frozenAccountBalanceReq.getTradeTime());
                    addTradeRecordReq.setSubType(AccountTradeRecordSubTypeEnum.CONSUME.getCode());
                    addTradeRecordReq.setMemberAssetAccountDO(memberAssetAccountDO);
                    addTradeRecordReq.setTradeCode(tradeCode);
                    addTradeRecordReq.setCategory(MemberAccountTradeCategoryEnum.DEPOSIT_MATERIAL.getCode());
                    addTradeRecordReq.setPayPlatformTradeCode(String.valueOf(orderPayInfoReq.getOrderPaymentId()));
                    addTradeRecordReqs.add(addTradeRecordReq);
                    AccountBalancePaidResp accountBalancePaidResp = new AccountBalancePaidResp();
                    accountBalancePaidResp.setPaidAmount(lockGoleWeight.multiply(goldPrice).setScale(2, RoundingMode.HALF_UP));
                    accountBalancePaidResp.setOrderNo(orderPayInfoReq.getOrderNo());
                    accountBalancePaidResp.setPaidGoldWeight(lockGoleWeight);
                    accountBalancePaidResp.setPayType(OrderPayChannelEnum.CREDIT.getCode());
                    accountBalancePaidResp.setOrderPaymentId(orderPayInfoReq.getOrderPaymentId());
                    accountBalancePaidOrderResult.add(accountBalancePaidResp);
                }
            }

            if(!BalancePayMethodEnum.isBalancePayMethod(frozenAccountBalanceReq.getBalancePayMethods())){
                //付款完商品金额，但未付款其他费用
                UnPayOrderInfo unPayOrderInfo = new UnPayOrderInfo();
                unPayOrderInfo.setOrderNo(orderPayInfoReq.getOrderNo());
                unPayOrderInfo.setUnPayAmount(goldPrice.multiply(unPayGoldWeight).setScale(2, RoundingMode.HALF_UP));
                unPayOrderInfo.setUnPayOtherFee(orderPayInfoReq.getOtherAmount());
                unPayOrderInfo.setUnPayGoldWeight(unPayGoldWeight);
                unPayOrderInfo.setOrderPaymentId(orderPayInfoReq.getOrderPaymentId());
                unPayOrderResult.add(unPayOrderInfo);
                continue;
            }
            //余额支付
            BigDecimal unPayOtherAmount = orderPayInfoReq.getOtherAmount();
            if((!BigDecimalUtil.isNullOrZero(unPayGoldWeight) || !BigDecimalUtil.isNullOrZero(unPayOtherAmount))){
                //先付黄金费用 ， 再付其他费用
                BigDecimal unPayGoldWeightAmount = unPayGoldWeight.multiply(goldPrice).setScale(4, RoundingMode.HALF_UP);
                //重新计算要支付的金额
                BigDecimal lockBalance = BigDecimal.ZERO;
                BigDecimal lockPayGoldBalance = BigDecimal.ZERO;
                BigDecimal canUseBalance = calcCanUseBalance(memberAssetAccountDO, goldPrice);
                if(BigDecimalUtil.isGreaterThan(canUseBalance, unPayGoldWeightAmount)){
                    lockPayGoldBalance = unPayGoldWeightAmount;
                    //支付其他费用
                    //再次计算可用额度
                    canUseBalance = calcCanUseBalance(memberAssetAccountDO, goldPrice).subtract(unPayGoldWeightAmount);
                    if(!BigDecimalUtil.isNullOrZero(unPayOtherAmount) && BigDecimalUtil.isGreaterThan(canUseBalance, unPayOtherAmount)){
                       lockBalance  = BigDecimalUtil.add(lockBalance, unPayOtherAmount);
                    }else{
                        if(CollectionUtils.isEmpty(otherPayChannels)){
                            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_MORE_EXCEPTION);
                        }
                        //付款完商品金额，但未付款其他费用
                        UnPayOrderInfo unPayOrderInfo = new UnPayOrderInfo();
                        unPayOrderInfo.setOrderNo(orderPayInfoReq.getOrderNo());
                        unPayOrderInfo.setUnPayAmount(BigDecimal.ZERO);
                        unPayOrderInfo.setUnPayOtherFee(BigDecimalUtil.subtract(orderPayInfoReq.getOtherAmount(), canUseBalance));
                        unPayOrderInfo.setOrderPaymentId(orderPayInfoReq.getOrderPaymentId());
                        unPayOrderResult.add(unPayOrderInfo);
                        lockBalance  = BigDecimalUtil.add(lockBalance, canUseBalance);
                    }

                }else{
                    unPayGoldWeightAmount = unPayGoldWeightAmount.subtract(canUseBalance);
                    lockPayGoldBalance = canUseBalance;
                    // 是否还有其他支付（其他线上支付）? 没有直接抛异常账户余额不足
                    if(CollectionUtils.isEmpty(otherPayChannels)){
                        throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_MORE_EXCEPTION);
                    }
                    //否则冻结现有的金额
                    String defaultMoneyEncrypt = DigestUtils.md5DigestAsHex(String.valueOf(memberAssetAccountDO.getLockBalance()).getBytes());
                    memberAssetAccountDO.setLockBalanceEncrypt(defaultMoneyEncrypt);
                    //账户余额使用完了，其他订单先不处理
                    accountBalanceUsed = true;
                    //未付款完商品金额，也未付款其他费用
                    UnPayOrderInfo unPayOrderInfo = new UnPayOrderInfo();
                    unPayOrderInfo.setOrderNo(orderPayInfoReq.getOrderNo());
                    unPayOrderInfo.setUnPayAmount(unPayGoldWeightAmount);
                    unPayOrderInfo.setUnPayGoldWeight(unPayOrderInfo.getUnPayAmount().divide(goldPrice, 4, RoundingMode.HALF_UP));
                    unPayOrderInfo.setOrderPaymentId(orderPayInfoReq.getOrderPaymentId());
                    unPayOrderInfo.setUnPayOtherFee(orderPayInfoReq.getOtherAmount());
                    unPayOrderResult.add(unPayOrderInfo);
                }
                lockBalance = lockBalance.add(lockPayGoldBalance);
                memberAssetAccountDO.setLockBalance(BigDecimalUtil.add(memberAssetAccountDO.getLockBalance(), lockBalance));
                String defaultMoneyEncrypt = DigestUtils.md5DigestAsHex(String.valueOf(memberAssetAccountDO.getLockBalance()).getBytes());
                memberAssetAccountDO.setLockBalanceEncrypt(defaultMoneyEncrypt);

                //记录交易记录
                if(BigDecimalUtil.isGreaterThan(lockBalance, BigDecimal.ZERO)){
                    AddMemberAccountTradeRecordReq addTradeRecordReq = new AddMemberAccountTradeRecordReq();
                    BigDecimal balanceBefore = BigDecimalUtil.subtract(memberAssetAccountDO.getAccountBalance(), oldLockBalance);
                    addTradeRecordReq.setBalanceAfter(balanceBefore.subtract(lockBalance));
                    addTradeRecordReq.setBalanceChange(lockBalance);
                    addTradeRecordReq.setBalanceBefore(balanceBefore);
                    addTradeRecordReq.setSubType(AccountTradeRecordSubTypeEnum.CONSUME.getCode());
                    addTradeRecordReq.setMemberAssetAccountDO(memberAssetAccountDO);
                    addTradeRecordReq.setCategory(MemberAccountTradeCategoryEnum.BANLANCE_MONEY.getCode());
                    addTradeRecordReq.setPayPlatformTradeCode(String.valueOf(orderPayInfoReq.getOrderPaymentId()));
                    addTradeRecordReq.setTradeCode(tradeCode);
                    addTradeRecordReqs.add(addTradeRecordReq);
                    AccountBalancePaidResp accountBalancePaidResp = new AccountBalancePaidResp();
                    accountBalancePaidResp.setPaidAmount(lockBalance);
                    accountBalancePaidResp.setOrderNo(orderPayInfoReq.getOrderNo());
                    accountBalancePaidResp.setPaidGoldWeight(lockPayGoldBalance.divide(goldPrice, 4, RoundingMode.HALF_UP));
                    accountBalancePaidResp.setPayType(OrderPayChannelEnum.ACCOUNT_BALANCE.getCode());
                    accountBalancePaidResp.setOrderPaymentId(orderPayInfoReq.getOrderPaymentId());
                    accountBalancePaidOrderResult.add(accountBalancePaidResp);
                }
            }
        }
        memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);
        accountTradeService.addMemberAccountTradeRecord(addTradeRecordReqs);
        AccountPayChannelResultResp accountPayChannelResultResp = new AccountPayChannelResultResp();
        accountPayChannelResultResp.setUnPayOrders(unPayOrderResult);
        accountPayChannelResultResp.setPaidOrders(accountBalancePaidOrderResult);
        return accountPayChannelResultResp;
    }



    /**
     * 计算账户剩余可用的余额
     *  *  授信额度 = 当前授信额度 * 线上可使用比例
     *  *  如果存料为负数，可用余额 = 当前余额 - ABS（(授信额度+存料）*实时金价）- 冻结余额；
     *  *  如果为正数，可用余额 = 当前余额 - 冻结余额；
     * @param account
     * @param goldPrice
     * @return 返回该账户可用余额
     */
    private BigDecimal calcCanUseBalance(MemberAssetAccountDO account, BigDecimal goldPrice) {
        // 参数校验
        if (account == null || goldPrice == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal materialStock = BigDecimalUtil.nullToZero(account.getMaterialStock());
        BigDecimal accountBalance = BigDecimalUtil.nullToZero(account.getAccountBalance());
        BigDecimal lockBalance = account.getLockBalance();
        // 基础条件检查
        if (accountBalance == null || BigDecimalUtil.isZeroOrNegative(accountBalance)) {
            return BigDecimal.ZERO;
        }
        if(lockBalance != null){
            accountBalance = accountBalance.subtract(lockBalance);
        }
        // 计算信用额度可用部分
        BigDecimal availableCredit = BigDecimal.ZERO;
        if (account.getLineOfCredit() != null) {
            availableCredit = account.getLineOfCredit();
            if (account.getOnlineUsedRatio() != null && account.getOnlineUsedRatio() > 0) {
                availableCredit = availableCredit.multiply(BigDecimal.valueOf(account.getOnlineUsedRatio())).setScale(2, RoundingMode.HALF_UP);
            }
        }
        // 计算最终可用余额
        BigDecimal totalAssets = materialStock.add(availableCredit);
        totalAssets = BigDecimalUtil.subtract(totalAssets, account.getLockMaterialStock());
        if(BigDecimalUtil.isZeroOrNegative(totalAssets)){
            BigDecimal goldValue = goldPrice.multiply(totalAssets);
            return accountBalance.add(goldValue).setScale(2, RoundingMode.HALF_UP);
        }
        return accountBalance;

    }


    /**
     * 计算授信余料可用
     *  *  存款为正数时：余料可用 = （当前授信额度 * 线上可使用比例） + (余料 - 冻结余料)
     *  *  存款为负数时：余料可用 = （当前授信额度 * 线上可使用比例） + (余料 - 冻结余料) + (存款/金价).abs
     * @param account
     * @return 返回该账户可用余料
     */
    private BigDecimal calcCanUseMaterialStock(MemberAssetAccountDO account, BigDecimal goldPrice) {
        BigDecimal materialStock = account.getMaterialStock();
        BigDecimal lockMaterialStock = account.getLockMaterialStock();
        LocalDate lineOfCreditUseStartTime = account.getLineOfCreditUseStartTime();
        LocalDate lineOfCreditUseEndTime = account.getLineOfCreditUseEndTime();
        BigDecimal lineOfCredit = BigDecimal.ZERO;
        //校验授信是否在有效期内
        if(lineOfCreditUseStartTime != null && lineOfCreditUseEndTime != null
                && DateTimeUtil.isBelongPeriodOfNow(lineOfCreditUseStartTime.atTime(LocalTime.MIN), lineOfCreditUseEndTime.atTime(LocalTime.MAX))){
            lineOfCredit =  account.getLineOfCredit();
            if(account.getOnlineUsedRatio() != null && account.getOnlineUsedRatio() > 0) {
                lineOfCredit = BigDecimalUtil.multiply(lineOfCredit, BigDecimal.valueOf(account.getOnlineUsedRatio()).divide(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN));
            }
        }
        materialStock = BigDecimalUtil.subtract(materialStock, lockMaterialStock);
        BigDecimal canUseMaterialStock = BigDecimalUtil.add(lineOfCredit, materialStock);
        if(BigDecimalUtil.isZeroOrNegative(account.getAccountBalance())){
            BigDecimal balanceMapGoleWeightAbs = BigDecimalUtil.divide(account.getAccountBalance(), goldPrice, 2).abs();
            canUseMaterialStock = BigDecimalUtil.subtract(canUseMaterialStock, balanceMapGoleWeightAbs);
        }
        return canUseMaterialStock;
    }



    /**
     * 解冻账户余额
     * @param unFrozenAccountBalanceReq
     * @return
     */
    @Transactional
    @Override
    public Boolean unFrozenAccountBalance(UnFrozenAccountBalanceReq unFrozenAccountBalanceReq) {
        //查询账户流水
        List<AccountTradeRecordDO> accountTradeRecordDOs = accountTradeRecordRepository.findByPayPlatformTradeCodeInAndOperationAndStatus(unFrozenAccountBalanceReq.getTradeCode(), TradeOperationEnum.ORDER_PAY.getCode(), TradeStatusEnum.PAYING.getCode());
        //无支付记录，直接返回
        if(CollectionUtils.isEmpty(accountTradeRecordDOs)){
            log.info("流水号：{}，无流水记录", unFrozenAccountBalanceReq.getTradeCode());
            return false;
        }
        MemberAssetAccountDO memberAssetAccount = accountTradeRecordDOs.get(0).getMemberAssetAccount();
        for (AccountTradeRecordDO accountTradeRecordDO : accountTradeRecordDOs) {
            accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING_FAIL.getCode());
            if(MemberAccountTradeCategoryEnum.DEPOSIT_MATERIAL.getCode().equals(accountTradeRecordDO.getCategory())){
                memberAssetAccount.setLockMaterialStock(BigDecimalUtil.subtract(memberAssetAccount.getLockMaterialStock(), accountTradeRecordDO.getTradeMoney()));
            }else if(MemberAccountTradeCategoryEnum.BANLANCE_MONEY.getCode().equals(accountTradeRecordDO.getCategory())){
                memberAssetAccount.setLockBalance(BigDecimalUtil.subtract(memberAssetAccount.getLockBalance(),accountTradeRecordDO.getTradeMoney()));
            }
        }
        memberAssetAccountRepository.saveAndFlush(memberAssetAccount);
        accountTradeRecordRepository.saveAll(accountTradeRecordDOs);
        return true;
    }



    /**
     * 解冻账户余额
     * @param unFrozenAccountBalanceReq
     * @return
     */
    @Override
    public Boolean unLockAccountBalance(UnLockAccountBalanceReq unFrozenAccountBalanceReq) {
        List<MemberAssetAccountDO> memberAssetAccountDOS = memberAssetAccountRepository.findByMemberIdAndParentMemberRoleIdAndParentMemberIdAndMemberRoleId(unFrozenAccountBalanceReq.getMemberId(), baiTaiMemberProperties.getSelfRoleId(), baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getCustomerRoleId());
        if(CollectionUtils.isEmpty(memberAssetAccountDOS)){
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountDOS.get(0);
        memberAssetAccountDO.setLockMaterialStock(BigDecimalUtil.subtract(memberAssetAccountDO.getLockMaterialStock(), unFrozenAccountBalanceReq.getUnLockGoldWeight()));
        if(BigDecimalUtil.isGreaterThan(memberAssetAccountDO.getLockMaterialStock(), BigDecimal.ZERO)){
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_LOCK_MATERIAL_STOCK_ERROR);
        }
        memberAssetAccountDO.setLockBalance(BigDecimalUtil.subtract(memberAssetAccountDO.getLockBalance(), unFrozenAccountBalanceReq.getUnLockOtherFee()));
        if(BigDecimalUtil.isGreaterThan(memberAssetAccountDO.getLockBalance(), BigDecimal.ZERO)){
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_LOCK_BALANCE_ERROR);
        }
        memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);
        return true;
    }

    @Override
    public Boolean payFinishAndUnLock(PayFinishAndUnLockReq payFinishAndUnLockReq) {
        List<AccountTradeRecordDO> accountTradeRecordDOs = accountTradeRecordRepository.findByPayPlatformTradeCodeInAndOperationAndStatus(Sets.newHashSet(payFinishAndUnLockReq.getPaymentId().toString()), TradeOperationEnum.ORDER_PAY.getCode(), TradeStatusEnum.PAYING.getCode());
        if(CollectionUtils.isEmpty(accountTradeRecordDOs)){
            return  false;
        }
        AccountTradeRecordDO accountTradeRecord = accountTradeRecordDOs.get(0);
        MemberAssetAccountDO memberAssetAccount = accountTradeRecord.getMemberAssetAccount();
        //TODO 暂行先不扣除锁定信息
        for (AccountTradeRecordDO accountTradeRecordDO : accountTradeRecordDOs) {
            if(MemberAccountTradeCategoryEnum.BANLANCE_MONEY.getCode().equals(accountTradeRecordDO.getCategory())){
                memberAssetAccount.setLockBalance(BigDecimalUtil.subtract(memberAssetAccount.getLockBalance(), accountTradeRecordDO.getTradeMoney()));
            }else if(MemberAccountTradeCategoryEnum.DEPOSIT_MATERIAL.getCode().equals(accountTradeRecordDO.getCategory())){
                memberAssetAccount.setLockMaterialStock(BigDecimalUtil.subtract(memberAssetAccount.getLockMaterialStock(), accountTradeRecordDO.getTradeMoney()));
            }
            accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING_SUCCESS.getCode());
        }

        //获取存欠数据并更新
        List<GetAccountInfoResp> accountInfo = eosApiService.getAccountInfo(Arrays.asList(memberAssetAccount.getMemberCode()));
        GetAccountInfoResp getAccountInfoResp = accountInfo.get(0);
        memberAssetAccount.setMaterialStock(BigDecimalUtil.nullToZero(getAccountInfoResp.getQmjz_jl()));
        memberAssetAccount.setAccountBalance(BigDecimalUtil.nullToZero(getAccountInfoResp.getQmje()));
        memberAssetAccountRepository.saveAndFlush(memberAssetAccount);
        accountTradeRecordRepository.saveAll(accountTradeRecordDOs);
        return true;
    }

    /**
     * 获取Eos账号余额信息和订单冻结的金额
     * 将orderPaymentId订单冻结的余额/存料重新计算进可用余额/存料中
     * @param eosAccountBalanceReq
     * @return
     */
    @Override
    public MemberAssetAccountResp getEosAccountBalanceAndFrozenAccountBalance(GetEosAccountBalanceAndFrozenAccountBalanceReq eosAccountBalanceReq) {
        MemberAssetAccountResp memberAssetAccountResp = getEosAccountBalance(eosAccountBalanceReq.getMemberId());
        if(CollectionUtils.isEmpty(eosAccountBalanceReq.getPaymentIds())){
            return memberAssetAccountResp;
        }
        //重新计算冻结金额/存料，可用金额/存料
        List<AccountTradeRecordDO> accountTradeRecordDOS = accountTradeRecordRepository.findByPayPlatformTradeCodeInAndOperationAndStatus(eosAccountBalanceReq.getPaymentIds(), TradeOperationEnum.ORDER_PAY.getCode(), TradeStatusEnum.PAYING.getCode());
        if(CollectionUtils.isEmpty(accountTradeRecordDOS)){
            return memberAssetAccountResp;
        }
        for (AccountTradeRecordDO accountTradeRecordDO : accountTradeRecordDOS) {
            if(MemberAccountTradeCategoryEnum.DEPOSIT_MATERIAL.getCode().equals(accountTradeRecordDO.getCategory())){
                BigDecimal tradeMoney = accountTradeRecordDO.getTradeMoney();
                memberAssetAccountResp.setMaterialStockAndCredit(BigDecimalUtil.add(memberAssetAccountResp.getMaterialStockAndCredit(), tradeMoney));
                memberAssetAccountResp.setLockMaterialStock(BigDecimalUtil.subtract(memberAssetAccountResp.getLockMaterialStock(), tradeMoney));
            }else if(MemberAccountTradeCategoryEnum.BANLANCE_MONEY.getCode().equals(accountTradeRecordDO.getCategory())){
                BigDecimal tradeMoney = accountTradeRecordDO.getTradeMoney();
                memberAssetAccountResp.setLockBalance(BigDecimalUtil.subtract(memberAssetAccountResp.getLockBalance(), tradeMoney));
                memberAssetAccountResp.setUnUsedAccountBalanceCash(BigDecimalUtil.add(memberAssetAccountResp.getUnUsedAccountBalanceCash(), tradeMoney));
            }
            memberAssetAccountResp.setUnUsedAccountBalance(memberAssetAccountResp.getUnUsedAccountBalanceCash().divide(memberAssetAccountResp.getGoldPrice(), 2, RoundingMode.HALF_DOWN));
            memberAssetAccountResp.setTotalAmount(BigDecimalUtil.add(memberAssetAccountResp.getMaterialStockAndCredit(), memberAssetAccountResp.getUnUsedAccountBalance()));
        }
        return memberAssetAccountResp;
    }

    /**
     * 检查账户状态
     * @param memberAssetAccountDO 会员资金账户
     */
    private void checkAccountStatus(MemberAssetAccountDO memberAssetAccountDO) {
        //验证会员状态
        Integer memberStatus = memberAssetAccountDO.getMemberStatus();
        if (!MemberStatusEnum.NORMAL.getCode().equals(memberStatus)) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_STATUS_EXCEPTION);
        }
        //验证会员账户状态
        Integer accountStatus = memberAssetAccountDO.getAccountStatus();

        if (!AccountStatusEnum.THAW.getCode().equals(accountStatus)) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_STATUS_EXCEPTION);
        }
    }


    @Override
    public void export(HttpServletResponse response, MemberAssetAccountReq memberAssetAccountMiddle, MemberAssetAccountPageQueryReq memberAssetAccountPageQueryReq) {
        Specification<MemberAssetAccountDO> specification = getPageQuerySpecification(memberAssetAccountMiddle, memberAssetAccountPageQueryReq);
        BigDecimal goldPrice = getGoldPrice();
        List<MemberAssetAccountDO> memberAssetAccountDOS = memberAssetAccountRepository.findAll(specification);

        List<Map<String, Object>> dataList = memberAssetAccountDOS.stream().map(memberAssetAccountDO -> {
            MemberAssetAccountResp memberAssetAccountResp = convert(memberAssetAccountDO, goldPrice);
            BigDecimal materialStock = BigDecimalUtil.nullToZero(memberAssetAccountDO.getMaterialStock());
            //BigDecimal accountBalance = BigDecimalUtil.nullToZero(memberAssetAccountDO.getAccountBalance());
            BigDecimal lockBalance = BigDecimalUtil.nullToZero(memberAssetAccountDO.getLockBalance());
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(MemberAssetAccountExportFileEnum.MEMBER_ID.getMessage(), memberAssetAccountDO.getMemberId());
            map.put(MemberAssetAccountExportFileEnum.MEMBER_NAME.getMessage(), memberAssetAccountDO.getMemberName());
            map.put(MemberAssetAccountExportFileEnum.MEMBER_CODE.getMessage(), memberAssetAccountDO.getMemberCode());
            map.put(MemberAssetAccountExportFileEnum.MEMBER_TYPE.getMessage(), MemberTypeEnum.getName(memberAssetAccountDO.getMemberType()));
            map.put(MemberAssetAccountExportFileEnum.MEMBER_ROLE.getMessage(), OrderMemberTypeEnum.getName(memberAssetAccountDO.getMemberRoleId()));
            map.put(MemberAssetAccountExportFileEnum.TODAY_UN_USE.getMessage(), memberAssetAccountResp.getTotalAmount());
            map.put(MemberAssetAccountExportFileEnum.LOCK_MATERIAL_STOCK.getMessage(), memberAssetAccountDO.getLockMaterialStock());
            map.put(MemberAssetAccountExportFileEnum.MATERIAL_STOCK.getMessage(), materialStock);
            map.put(MemberAssetAccountExportFileEnum.LOCK_AND_UN_USE_BALANCE.getMessage(), lockBalance + "/" + memberAssetAccountResp.getUnUsedAccountBalanceCash());
            map.put(MemberAssetAccountExportFileEnum.ACCOUNT_BALANCE.getMessage(), memberAssetAccountDO.getAccountBalance());
            map.put(MemberAssetAccountExportFileEnum.LINE_OF_CREDIT.getMessage(), memberAssetAccountDO.getLineOfCredit());


            LocalDate lineOfCreditUseStartTime = memberAssetAccountDO.getLineOfCreditUseStartTime();
            LocalDate lineOfCreditUseEndTime = memberAssetAccountDO.getLineOfCreditUseEndTime();
            if(lineOfCreditUseStartTime !=null && lineOfCreditUseEndTime != null && DateTimeUtil.isBelongPeriodOfNow(lineOfCreditUseStartTime.atTime(LocalTime.MIN), lineOfCreditUseEndTime.atTime(LocalTime.MAX))) {
                map.put(MemberAssetAccountExportFileEnum.LINE_OF_CREDIT_STATUS.getMessage(), LineOfCreditStatusEnum.EFFECTIVE.getName());
            }else{
                map.put(MemberAssetAccountExportFileEnum.LINE_OF_CREDIT_STATUS.getMessage(), LineOfCreditStatusEnum.INVALID.getName());
            }
            map.put(MemberAssetAccountExportFileEnum.LINE_OF_CREDIT_USE_END_TIME.getMessage(), DateTimeUtil.formatDate(memberAssetAccountDO.getLineOfCreditUseEndTime()));
            map.put(MemberAssetAccountExportFileEnum.SYNC_UPDATE_TIME.getMessage(), DateTimeUtil.formatDateTime(memberAssetAccountDO.getSyncUpdateTime()));
            return map;
        }).collect(Collectors.toList());
        try {
            String fileName = DateTimeUtil.getDateTimeAsString(DateTimeUtil.getTodayNowLocal(), "yyyyMMdd")+"-客户审核查询";
            ExcelExportUtil.exportExcel(response, fileName, MemberAssetAccountExportFileEnum.getAllMessages() , dataList);
        } catch (IOException e) {
            throw new BusinessException(ResponseCodeEnum.FILE_EXPORT_ERROR, e.getMessage());
        }
    }

    /**
     * 获取用户钱包账号信息
     * @param sysUser
     * @return
     */
    @Override
    public MemberAssetAccountResp getAccountBalance(UserLoginCacheDTO sysUser) {
        CommonIdReq commonIdReq = new CommonIdReq();
        commonIdReq.setId(sysUser.getBranchId());
        WrapperResp<MemberBrandInfoResp> memberBranchInfoWrapper = memberFeign.findMemberBrandById(commonIdReq);
        MemberBrandInfoResp memberBrandInfoResp = WrapperUtil.getDataOrThrow(memberBranchInfoWrapper);
        BusinessAssertUtil.notNull(memberBrandInfoResp, ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_NOT_EXISTS);
        if(memberBrandInfoResp.getMemberId() == null){
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_NOT_BINDING_TO_MEMBER);
        }
        List<MemberAssetAccountDO> memberAssetAccountDOS = memberAssetAccountRepository.findByMemberIdAndParentMemberRoleIdAndParentMemberIdAndMemberRoleId(memberBrandInfoResp.getMemberId(), baiTaiMemberProperties.getSelfRoleId(), baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getCustomerRoleId());
        if(!CollectionUtils.isEmpty(memberAssetAccountDOS)){
            MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountDOS.get(0);
            BigDecimal goldPrice = getGoldPrice();
            MemberAssetAccountResp memberAssetAccountResp = convert(memberAssetAccountDO, goldPrice);
            memberAssetAccountResp.setGoldPrice(goldPrice);
            memberAssetAccountResp.setAccountBalanceMap(memberAssetAccountDO.getAccountBalance().divide(goldPrice, 0, RoundingMode.HALF_UP));
            return memberAssetAccountResp;
        }
        return null;
    }

    /**
     * 获取eos用户钱包账号信息
     * @param sysUser
     * @return
     */
    @Override
    public MemberAssetAccountResp getEosAccountBalance(Long memberId) {
        List<MemberAssetAccountDO> memberAssetAccountDOS = memberAssetAccountRepository.findByMemberIdAndParentMemberRoleIdAndParentMemberIdAndMemberRoleId(memberId, baiTaiMemberProperties.getSelfRoleId(), baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getCustomerRoleId());
        if(CollectionUtils.isEmpty(memberAssetAccountDOS)){
            log.error("获取会员资金账户失败, 会员Id: {}", memberId);
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountDOS.get(0);
        BigDecimal goldPrice = getGoldPrice();
        if(BigDecimalUtil.isZeroOrNegative(goldPrice)){
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_GOLE_ERROR);
        }
        //账户被冻结，账户余额全部返回0
        if(isFrozenAccount(memberAssetAccountDO)){
            MemberAssetAccountResp memberAssetAccountResp = convert(memberAssetAccountDO, goldPrice);
            memberAssetAccountResp.setLineOfCredit(BigDecimal.ZERO);
            memberAssetAccountResp.setAccountBalance(BigDecimal.ZERO);
            memberAssetAccountResp.setLockMaterialStock(BigDecimal.ZERO);
            memberAssetAccountResp.setGoldPrice(goldPrice);
            return memberAssetAccountResp;
        }
        String code = memberAssetAccountDO.getMemberCode();
        List<String> codes = Arrays.asList(code);
        List<GetAccountInfoResp> eosAccountInfos = eosApiService.getAccountInfo(codes);
        if(CollectionUtils.isEmpty(eosAccountInfos)){
            log.error("查询结账户失败, 尚金源返回的账户信息为空, 会员编号: {}", code);
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
        GetAccountInfoResp eosAccountInfo = eosAccountInfos.get(0);
        memberAssetAccountDO.setMaterialStock(BigDecimalUtil.nullToZero(eosAccountInfo.getQmjz_jl()));
        memberAssetAccountDO.setAccountBalance(BigDecimalUtil.nullToZero(eosAccountInfo.getQmje()));
        memberAssetAccountDO.setSyncUpdateTime(DateTimeUtil.getTodayNowLocal());
        //更新账号信息
        memberAssetAccountRepository.save(memberAssetAccountDO);
        MemberAssetAccountResp memberAssetAccountResp = convert(memberAssetAccountDO,  goldPrice);
        memberAssetAccountResp.setGoldPrice(goldPrice);
        memberAssetAccountResp.setAccountBalanceMap(memberAssetAccountDO.getAccountBalance().divide(goldPrice, 0, RoundingMode.HALF_UP));
        return memberAssetAccountResp;
    }

    @Override
    public Boolean depositAccountByManager(DepositAccountByManagerReq depositAccountReq) {
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountRepository.findById(depositAccountReq.getAccountId()).orElseThrow(() -> {
            log.error("充值账户不存在, 账户id: {}", depositAccountReq.getAccountId());
            return new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        });
        //验证会员状态
        checkAccountStatus(memberAssetAccountDO);
        if(DeposiAccountTypeEnum.WALLET.getCode().equals(depositAccountReq.getDepositType())){
            memberAssetAccountDO.setAccountBalance(BigDecimalUtil.nullToZero(memberAssetAccountDO.getAccountBalance()).add(depositAccountReq.getDepositNum()));
        }else {
            memberAssetAccountDO.setMaterialStock(BigDecimalUtil.nullToZero(memberAssetAccountDO.getMaterialStock()).add(depositAccountReq.getDepositNum()));
        }
        memberAssetAccountRepository.save(memberAssetAccountDO);
        return true;
    }


    /**
     * 判断会员资金账户是否锁定
     * @param memberAssetAccountDO 会员资金账户
     * @return true-锁定，false-未锁定
     */
    public Boolean isFrozenAccount(MemberAssetAccountDO memberAssetAccountDO){
        if(memberAssetAccountDO.getAccountStatus() != null && AccountStatusEnum.FROZEN.getCode().equals(memberAssetAccountDO.getAccountStatus())){
            return true;
        }
        if(memberAssetAccountDO.getMemberStatus() != null && MemberStatusEnum.FROZEN.getCode().equals(memberAssetAccountDO.getMemberStatus())){
            return true;
        }
        return false;
    }

    /**
     * 添加会员资金账户
     * @param memberAssetAccountDORequestList 账户列表
     * @return 提示信息
     */
    @Override
    public String saveOrUpdateMemberAssetAccount(List<MemberAssetAccountDO> memberAssetAccountDORequestList) {
        List<MemberAssetAccountDO> memberAssetAccountDOList = memberAssetAccountDORequestList.stream().map(memberAssetAccount -> {
            //获取参数
            Long id = memberAssetAccount.getId();
            //判断是新增还是修改
            if (id != null && id > 0) {
                MemberAssetAccountDO currentMemberAssetAccountDO = memberAssetAccountRepository.findById(id).orElse(null);
                if (currentMemberAssetAccountDO != null) {
                    BeanUtils.copyProperties(currentMemberAssetAccountDO, memberAssetAccount);
                    return memberAssetAccount;
                } else {
                    throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
                }
            } else {
                //存在的会员资金账户不再新增
                Long memberId = memberAssetAccount.getMemberId();
                Long memberRoleId = memberAssetAccount.getMemberRoleId();
                Long parentMemberId = memberAssetAccount.getParentMemberId();
                Long parentMemberRoleId = memberAssetAccount.getParentMemberRoleId();
                MemberAssetAccountDO existMemberAssetAccountDO = memberAssetAccountRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(memberId, memberRoleId, parentMemberId, parentMemberRoleId);
                if (existMemberAssetAccountDO == null) {
                    //初始化数据
                    BigDecimal defaultMoney = BigDecimal.ZERO;
                    String defaultMoneyEncrypt = DigestUtils.md5DigestAsHex(String.valueOf(defaultMoney).getBytes());
                    memberAssetAccount.setAccountBalance(defaultMoney);
                    memberAssetAccount.setAccountBalanceEncrypt(defaultMoneyEncrypt);
                    memberAssetAccount.setLockBalance(defaultMoney);
                    memberAssetAccount.setLockBalanceEncrypt(defaultMoneyEncrypt);
                    memberAssetAccount.setAccountStatus(AccountStatusEnum.THAW.getCode());
                    memberAssetAccount.setMemberStatus(AccountStatusEnum.THAW.getCode());
                    memberAssetAccount.setCreateTime(System.currentTimeMillis());
                    return memberAssetAccount;
                } else {
                    return null;
                }
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if(!memberAssetAccountDOList.isEmpty()){
            //数据库持久化对象
            memberAssetAccountRepository.saveAll(memberAssetAccountDOList);
        }
        return ResponseCodeEnum.SUCCESS.getMessage();
    }

    /**
     * 修改会员资金账户状态
     * @param memberAssetAccountUpdateReq 接口参数
     * @return 提示信息
     */
    @Override
    public Boolean updateMemberAssetAccount(MemberAssetAccountUpdateReq memberAssetAccountUpdateReq) {
        Long memberId = memberAssetAccountUpdateReq.getMemberId();
        Long memberRoleId = memberAssetAccountUpdateReq.getMemberRoleId();
        Long parentMemberId = memberAssetAccountUpdateReq.getParentMemberId();
        Long parentMemberRoleId = memberAssetAccountUpdateReq.getParentMemberRoleId();
        Integer memberStatus = memberAssetAccountUpdateReq.getMemberStatus();
        Integer memberType = memberAssetAccountUpdateReq.getMemberType();
        List<MemberAssetAccountDO> memberAssetAccountDOList;
        if (AccountTypeEnum.Platform.getCode().equals(memberType)) {
            memberAssetAccountDOList = memberAssetAccountRepository.findByMemberIdAndMemberRoleId(memberId, memberRoleId);
        } else {
            memberAssetAccountDOList = memberAssetAccountRepository.findByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(memberId, memberRoleId, parentMemberId, parentMemberRoleId);
        }
        if (memberAssetAccountDOList != null && !memberAssetAccountDOList.isEmpty()) {
            memberAssetAccountDOList.forEach(memberAssetAccount -> memberAssetAccount.setMemberStatus(memberStatus));
            memberAssetAccountRepository.saveAll(memberAssetAccountDOList);
        }
        return Boolean.TRUE;
    }

    /**
     * 冻结/解冻账号
     * @param id 账户Id
     * @param status 状态
     * @return 提示信息
     */
    @Transactional
    @Override
    public void updateMemberAssetAccountEnable(Long id, Integer status, String remark) {
        if (!AccountStatusEnum.THAW.getCode().equals(status) && !AccountStatusEnum.FROZEN.getCode().equals(status)) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_STATUS_EXCEPTION);
        }
        //验证数据库中是否存在该数据
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountRepository.findById(id).orElse(null);
        if (Objects.isNull(memberAssetAccountDO)) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
        //持久化数据
        memberAssetAccountDO.setAccountStatus(status);
        memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);

        AccountStatusRecordDO accountStatusRecordDO = new AccountStatusRecordDO();
        accountStatusRecordDO.setOperation(status);
        accountStatusRecordDO.setStatus(status);
        accountStatusRecordDO.setCreateTime(System.currentTimeMillis());
        accountStatusRecordDO.setMemberAssetAccount(memberAssetAccountDO);
        accountStatusRecordDO.setRemark(remark);
        accountStatusRecordRepository.saveAndFlush(accountStatusRecordDO);
    }

    /**
     * 资金账户充值
     * @param rechargeReq 接口参数
     * @return 充值结果
     */
    @Transactional
    @Override
    public AccountRechargeResp rechargeAssetAccount(HttpServletRequest request, RechargeReq rechargeReq) {
        Long memberAssetAccountId = rechargeReq.getMemberAssetAccountId();
        BigDecimal money = rechargeReq.getMoney();
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountRepository.findById(memberAssetAccountId).orElse(null);
        if (memberAssetAccountDO != null) {
            //校验账户
            this.checkMemberAssetAccount(memberAssetAccountDO);

            //交易订单号
            String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);

            //获取会员支付参数
            Long parentMemberId = memberAssetAccountDO.getParentMemberId();
            Long parentMemberRoleId = memberAssetAccountDO.getParentMemberRoleId();

            MobilePayReq mobilePayReq = new MobilePayReq();
            mobilePayReq.setOrderCode(tradeCode);
            mobilePayReq.setPayMoney(money.setScale(2, RoundingMode.HALF_UP));
            mobilePayReq.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
            mobilePayReq.setServiceType(ServiceTypeEnum.Pay_Recharge.getCode());
            mobilePayReq.setMemberId(parentMemberId);
            mobilePayReq.setMemberRoleId(parentMemberRoleId);

            //支付
            WeChatPayResultResp weChatPayResultResp;
            Integer memberLevelType = memberAssetAccountDO.getMemberLevelType();
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult;
            if (MemberLevelTypeEnum.PLATFORM.getCode().equals(memberLevelType)) {
                mobilePayReq.setPayType(PayTypeEnum.PLATFORM.getCode());
                OrderPayChannelFeignReq feignVO = new OrderPayChannelFeignReq();
                feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
                parameterResult = orderFeignService.findPlatformPaymentParameters(feignVO);
            } else {
                mobilePayReq.setPayType(PayTypeEnum.MEMBER.getCode());
                OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
                feignVO.setMemberId(parentMemberId);
                feignVO.setRoleId(parentMemberRoleId);
                feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
                parameterResult = orderFeignService.findMemberPaymentParameters(feignVO);
            }

            if(parameterResult.getCode()!= ResponseCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                if(MemberLevelTypeEnum.PLATFORM.getCode().equals(memberLevelType)) {
                    throw new BusinessException(ResponseCodeEnum.PAY_PLATFORM_WECHAT_PARAMETERS_DOES_NOT_EXIST);
                } else {
                    throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_WECHAT_PARAMETERS_DOES_NOT_EXIST);
                }
            }

            parameterResult.getData().getParameters().forEach(p -> {
                if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)) {
                    mobilePayReq.setMerchantId(p.getValue());
                } else if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)) {
                    mobilePayReq.setAppId(p.getValue());
                } else if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)) {
                    mobilePayReq.setApiKey(p.getValue());
                }
            });

            weChatPayResultResp = weChatPayService.nativePay(mobilePayReq, request);

            if (weChatPayResultResp.isSuccess()) {
                //保存交易记录
                AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
                accountTradeRecordDO.setTradeCode(tradeCode);
                accountTradeRecordDO.setTradeMoney(money);
                accountTradeRecordDO.setMemberAssetAccount(memberAssetAccountDO);
                accountTradeRecordDO.setOperation(TradeOperationEnum.ACCOUNT_RECHARGE.getCode());
                accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING.getCode());
                accountTradeRecordDO.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
                accountTradeRecordDO.setTradeTime(System.currentTimeMillis());
                accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);

                //发送消息到队列(2小时)
                Long accountTradeRecordId = accountTradeRecordDO.getId();
                mqUtils.sendDelayMsg(PayMqConstant.PAY_RECHARGE_DELAY_EXCHANGE, PayMqConstant.PAY_RECHARGE_DELAY_ROUTING_KEY, accountTradeRecordId, 7200 * 1000L);

                //支付返回结果
                @SuppressWarnings("unchecked")
                Map<String, String> body = (Map<String, String>) weChatPayResultResp.getBody();
                AccountRechargeResp accountRechargeResp = new AccountRechargeResp();
                accountRechargeResp.setCodeUrl(body.get("code_url"));
                accountRechargeResp.setTradeRecordId(accountTradeRecordId);
                return accountRechargeResp;
            } else {
                throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL, weChatPayResultResp.getMessage());
            }
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
    }

    /**
     * 资金账户充值
     * @param rechargeReq 接口参数
     * @return 充值结果
     */
    @Transactional
    @Override
    public AccountRechargeAppResp rechargeAssetAccountApp(HttpServletRequest request, RechargeReq rechargeReq) {
        Long memberAssetAccountId = rechargeReq.getMemberAssetAccountId();
        BigDecimal money = rechargeReq.getMoney();
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountRepository.findById(memberAssetAccountId).orElse(null);
        if (memberAssetAccountDO != null) {
            //校验账户
            this.checkMemberAssetAccount(memberAssetAccountDO);

            //交易订单号
            String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);

            //获取会员支付参数
            Long parentMemberId = memberAssetAccountDO.getParentMemberId();
            Long parentMemberRoleId = memberAssetAccountDO.getParentMemberRoleId();

            MobilePayReq mobilePayReq = new MobilePayReq();
            mobilePayReq.setOrderCode(tradeCode);
            mobilePayReq.setPayMoney(money.setScale(2, RoundingMode.HALF_UP));
            mobilePayReq.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
            mobilePayReq.setServiceType(ServiceTypeEnum.Pay_Recharge.getCode());
            mobilePayReq.setMemberId(parentMemberId);
            mobilePayReq.setMemberRoleId(parentMemberRoleId);

            //支付
            WeChatPayResultResp weChatPayResultResp;
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult;
            Integer memberLevelType = memberAssetAccountDO.getMemberLevelType();
            if (MemberLevelTypeEnum.PLATFORM.getCode().equals(memberLevelType)) {
                mobilePayReq.setPayType(PayTypeEnum.PLATFORM.getCode());
                OrderPayChannelFeignReq feignVO = new OrderPayChannelFeignReq();
                feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
                parameterResult = orderFeignService.findPlatformPaymentParameters(feignVO);
            } else {
                mobilePayReq.setPayType(PayTypeEnum.MEMBER.getCode());
                OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
                feignVO.setMemberId(parentMemberId);
                feignVO.setRoleId(parentMemberRoleId);
                feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
                parameterResult = orderFeignService.findMemberPaymentParameters(feignVO);
            }

            if(parameterResult.getCode()!= ResponseCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                if(MemberLevelTypeEnum.PLATFORM.getCode().equals(memberLevelType)) {
                    throw new BusinessException(ResponseCodeEnum.PAY_PLATFORM_WECHAT_PARAMETERS_DOES_NOT_EXIST);
                } else {
                    throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_WECHAT_PARAMETERS_DOES_NOT_EXIST);
                }
            }

            parameterResult.getData().getParameters().forEach(p -> {
                if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)) {
                    mobilePayReq.setMerchantId(p.getValue());
                } else if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)) {
                    mobilePayReq.setAppId(p.getValue());
                } else if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)) {
                    mobilePayReq.setApiKey(p.getValue());
                }
            });

            weChatPayResultResp = weChatPayService.appPay(mobilePayReq, request);
            if (weChatPayResultResp.isSuccess()) {
                //保存交易记录
                AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
                accountTradeRecordDO.setTradeCode(tradeCode);
                accountTradeRecordDO.setTradeMoney(money);
                accountTradeRecordDO.setMemberAssetAccount(memberAssetAccountDO);
                accountTradeRecordDO.setOperation(TradeOperationEnum.ACCOUNT_RECHARGE.getCode());
                accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING.getCode());
                accountTradeRecordDO.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
                accountTradeRecordDO.setTradeTime(System.currentTimeMillis());
                accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);

                //发送消息到队列(2小时)
                Long accountTradeRecordId = accountTradeRecordDO.getId();
                mqUtils.sendDelayMsg(PayMqConstant.PAY_RECHARGE_DELAY_EXCHANGE, PayMqConstant.PAY_RECHARGE_DELAY_ROUTING_KEY, accountTradeRecordId, 7200 * 1000L);

                //支付返回结果
                @SuppressWarnings("unchecked")
                Map<String, String> body = (Map<String, String>) weChatPayResultResp.getBody();
                AccountRechargeAppResp accountRechargeAppResp = new AccountRechargeAppResp();
                accountRechargeAppResp.setNoncestr(body.get("noncestr"));
                accountRechargeAppResp.setPartnerid(body.get("partnerid"));
                accountRechargeAppResp.setPrepayid(body.get("prepayid"));
                accountRechargeAppResp.setSign(body.get("sign"));
                accountRechargeAppResp.setTimestamp(body.get("timestamp"));
                accountRechargeAppResp.setTradeRecordId(accountTradeRecordId);
                return accountRechargeAppResp;
            } else {
                throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL, weChatPayResultResp.getMessage());
            }
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
    }

    /**
     * 资金账户充值(H5)
     * @param request  Http请求
     * @param rechargeJsApiReq 接口参数
     * @return 充值结果
     */
    @Override
    public AccountRechargeJsApiResp rechargeAssetAccountJsApi(UserLoginCacheDTO loginUser, HttpServletRequest request, RechargeJsApiReq rechargeJsApiReq) {
        Long memberId = loginUser.getMemberId();
        BigDecimal money = rechargeJsApiReq.getMoney();
        List<MemberAssetAccountDO> memberAssetAccountDOs = memberAssetAccountRepository.findByMemberIdAndParentMemberRoleIdAndParentMemberIdAndMemberRoleId(memberId, baiTaiMemberProperties.getSelfRoleId(), baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getCustomerRoleId());
        if(CollectionUtils.isEmpty(memberAssetAccountDOs)){
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountDOs.get(0);
        //校验账户
        this.checkMemberAssetAccount(memberAssetAccountDO);
        //交易订单号
        String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);
        if(RechargeChannelEnum.WEI_QI_FU.getCode().equals(rechargeJsApiReq.getType())){
            WeiQiFuPayReq weiQiFuPayReq = new WeiQiFuPayReq();
            weiQiFuPayReq.setPayChannel(loginUser.getLoginSource().equals(SystemSourceEnum.BUSINESS_MOBILE.getCode()) ? PayChannelEnum.Applet.getCode() : PayChannelEnum.Platform.getCode());
            weiQiFuPayReq.setOutPlatformId(tradeCode);
            weiQiFuPayReq.setPayMoney(rechargeJsApiReq.getMoney().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP).longValue());
            weiQiFuPayReq.setServiceType(ServiceTypeEnum.Pay_Recharge.getCode());
            List<GoodsReq> goodsReqs = new ArrayList<>();
            GoodsReq goodsReq = new GoodsReq();
            goodsReq.setGoodName(AccountTradeRecordTypeEnum.RECHARGE_ACCOUNTS.getMessage());
            goodsReq.setGoodNumber(1);
            goodsReq.setGoodAmount(rechargeJsApiReq.getMoney().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP).longValue());
            goodsReqs.add(goodsReq);
            weiQiFuPayReq.setGoodsReqList(goodsReqs);
            WeiQiFuPayResp weiQiFuPayResp = new WeiQiFuPayResp();
            if (PayChannelEnum.Applet.getCode().equals(weiQiFuPayReq.getPayChannel())) {
                weiQiFuPayResp = weiQiFuPayService.appletPay(weiQiFuPayReq, request);
            } else if (PayChannelEnum.Platform.getCode().equals(weiQiFuPayReq.getPayChannel())) {
                weiQiFuPayResp = weiQiFuPayService.qrCodePay(weiQiFuPayReq, request);
            }
            DelayQueueGetPayResultEnum theFirstTime = DelayQueueGetPayResultEnum.PULL_UP_PAY_THE_FIRST_TIME;
            AsynGetPayResultReq asynGetPayResultReq = new AsynGetPayResultReq();
            asynGetPayResultReq.setPayChannel(OrderPayChannelEnum.WEI_QI_FU.getCode());
            asynGetPayResultReq.setRecordId(tradeCode);
            asynGetPayResultReq.setServiceType(ServiceTypeEnum.Pay_Recharge.getCode());
            asynGetPayResultReq.setCurrentNotifyNum(theFirstTime.getCode());
            mqUtils.sendDelayMsg(PayMqConstant.PAY_RESULT_QUERY_DELAY_EXCHANGE, PayMqConstant.PAY_RESULT_QUERY_DELAY_ROUTING_KEY, JSON.toJSONString(asynGetPayResultReq), theFirstTime.getTime());
            if(WeiqifuStatusConstant.ORDER_PROCESSING.equals(weiQiFuPayResp.getPayStatus())){
                //保存交易记录
                AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
                accountTradeRecordDO.setTradeCode(tradeCode);
                accountTradeRecordDO.setOperation(TradeOperationEnum.ACCOUNT_RECHARGE.getCode());
                accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING.getCode());
                accountTradeRecordDO.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
                accountTradeRecordDO.setTradeTime(System.currentTimeMillis());
                accountTradeRecordDO.setCategory(MemberAccountTradeCategoryEnum.BANLANCE_MONEY.getCode());
                accountTradeRecordDO.setTransactionType(AccountTradeRecordTypeEnum.RECHARGE_ACCOUNTS.getCode());
                accountTradeRecordDO.setTradeMoneyAfter(BigDecimalUtil.add(memberAssetAccountDO.getAccountBalance(), rechargeJsApiReq.getMoney()));
                accountTradeRecordDO.setTradeMoneyBefore(memberAssetAccountDO.getAccountBalance());
                accountTradeRecordDO.setTradeMoney(rechargeJsApiReq.getMoney());
                AccountTradeRecordDO temp = accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);
                accountTradeRecordDO.setMemberAssetAccount(memberAssetAccountDO);
                accountTradeRecordRepository.saveAndFlush(temp);
            }
            AccountRechargeJsApiResp accountRechargeJsApiResp = BeanUtil.copyProperties(weiQiFuPayResp, AccountRechargeJsApiResp.class);
            accountRechargeJsApiResp.setMpH5(weiQiFuPayResp.getWxH5());
            return accountRechargeJsApiResp;
        }
        //获取会员支付参数
        Long parentMemberId = memberAssetAccountDO.getParentMemberId();
        Long parentMemberRoleId = memberAssetAccountDO.getParentMemberRoleId();

        MobilePayReq mobilePayReq = new MobilePayReq();
        mobilePayReq.setOrderCode(tradeCode);
        mobilePayReq.setPayMoney(money.setScale(2, RoundingMode.HALF_UP));
        mobilePayReq.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
        mobilePayReq.setServiceType(ServiceTypeEnum.Pay_Recharge.getCode());
        mobilePayReq.setMemberId(parentMemberId);
        mobilePayReq.setMemberRoleId(parentMemberRoleId);

        //支付
        WeChatPayResultResp weChatPayResultResp;
        WrapperResp<PaymentParameterFeignDetailResp> parameterResult;
        Integer memberLevelType = memberAssetAccountDO.getMemberLevelType();
        if (MemberLevelTypeEnum.PLATFORM.getCode().equals(memberLevelType)) {
            mobilePayReq.setPayType(PayTypeEnum.PLATFORM.getCode());
            OrderPayChannelFeignReq feignVO = new OrderPayChannelFeignReq();
            feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            parameterResult = orderFeignService.findPlatformPaymentParameters(feignVO);
        } else {
            mobilePayReq.setPayType(PayTypeEnum.MEMBER.getCode());
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(parentMemberId);
            feignVO.setRoleId(parentMemberRoleId);
            feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            parameterResult = orderFeignService.findMemberPaymentParameters(feignVO);
        }


        if(parameterResult.getCode()!= ResponseCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
            if(MemberLevelTypeEnum.PLATFORM.getCode().equals(memberLevelType)) {
                throw new BusinessException(ResponseCodeEnum.PAY_PLATFORM_WECHAT_PARAMETERS_DOES_NOT_EXIST);
            } else {
                throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_WECHAT_PARAMETERS_DOES_NOT_EXIST);
            }
        }

        parameterResult.getData().getParameters().forEach(p -> {
            if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)) {
                mobilePayReq.setMerchantId(p.getValue());
            } else if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)) {
                mobilePayReq.setAppId(p.getValue());
            } else if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)) {
                mobilePayReq.setApiKey(p.getValue());
            }
        });

        weChatPayResultResp = weChatPayService.h5Pay(mobilePayReq, request);
        if (weChatPayResultResp.isSuccess()) {
            //保存交易记录
            AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
            accountTradeRecordDO.setTradeCode(tradeCode);
            accountTradeRecordDO.setTradeMoney(money);
            accountTradeRecordDO.setOperation(TradeOperationEnum.ACCOUNT_RECHARGE.getCode());
            accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING.getCode());
            accountTradeRecordDO.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
            accountTradeRecordDO.setTradeTime(System.currentTimeMillis());
            AccountTradeRecordDO temp = accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);
            accountTradeRecordDO.setMemberAssetAccount(memberAssetAccountDO);
            accountTradeRecordRepository.saveAndFlush(temp);
            //发送消息到队列(2小时)
            Long accountTradeRecordId = accountTradeRecordDO.getId();
            mqUtils.sendDelayMsg(PayMqConstant.PAY_RECHARGE_DELAY_EXCHANGE, PayMqConstant.PAY_RECHARGE_DELAY_ROUTING_KEY, accountTradeRecordId, 7200 * 1000L);

            //支付返回结果
            Object obj = weChatPayResultResp.getBody();
            if (obj instanceof WxPayMwebOrderResult) {
                WxPayMwebOrderResult result = (WxPayMwebOrderResult)obj;
                AccountRechargeJsApiResp accountRechargeJsApiResp = new AccountRechargeJsApiResp();
                accountRechargeJsApiResp.setMwebUrl(result.getMwebUrl());
                accountRechargeJsApiResp.setTradeCode(tradeCode);
                accountRechargeJsApiResp.setAccountTradeRecordId(accountTradeRecordId);
                return accountRechargeJsApiResp;
            }
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL, weChatPayResultResp.getMessage());
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL, weChatPayResultResp.getMessage());
        }
    }

    /**
     * 资金账户充值(小程序)
     * @param rechargeAppletReq 接口参数
     * @return 充值结果
     */
    @SuppressWarnings("unchecked")
    @Transactional
    @Override
    public AccountRechargeAppletResp rechargeAssetAccountApplet(HttpServletRequest request, RechargeAppletReq rechargeAppletReq) {
        String jsCode = rechargeAppletReq.getJsCode();
        BigDecimal money = rechargeAppletReq.getMoney();
        Long memberAssetAccountId = rechargeAppletReq.getMemberAssetAccountId();
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountRepository.findById(memberAssetAccountId).orElse(null);
        if (memberAssetAccountDO != null) {
            //校验账户
            this.checkMemberAssetAccount(memberAssetAccountDO);

            //交易订单号
            String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);

            //获取会员支付参数
            Long parentMemberId = memberAssetAccountDO.getParentMemberId();
            Long parentMemberRoleId = memberAssetAccountDO.getParentMemberRoleId();

            MobilePayReq mobilePayReq = new MobilePayReq();
            mobilePayReq.setOrderCode(tradeCode);
            mobilePayReq.setPayMoney(money.setScale(2, RoundingMode.HALF_UP));
            mobilePayReq.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
            mobilePayReq.setServiceType(ServiceTypeEnum.Pay_Recharge.getCode());
            mobilePayReq.setMemberId(parentMemberId);
            mobilePayReq.setMemberRoleId(parentMemberRoleId);

            //支付
            WeChatPayResultResp weChatPayResultResp;
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult;
            Integer memberLevelType = memberAssetAccountDO.getMemberLevelType();
            if (MemberLevelTypeEnum.PLATFORM.getCode().equals(memberLevelType)) {
                mobilePayReq.setPayType(PayTypeEnum.PLATFORM.getCode());
                OrderPayChannelFeignReq feignVO = new OrderPayChannelFeignReq();
                feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
                parameterResult = orderFeignService.findPlatformPaymentParameters(feignVO);
            } else {
                mobilePayReq.setPayType(PayTypeEnum.MEMBER.getCode());
                OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
                feignVO.setMemberId(parentMemberId);
                feignVO.setRoleId(parentMemberRoleId);
                feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
                parameterResult = orderFeignService.findMemberPaymentParameters(feignVO);
            }

            if(parameterResult.getCode()!= ResponseCodeEnum.SUCCESS.getCode() || CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                if(MemberLevelTypeEnum.PLATFORM.getCode().equals(memberLevelType)) {
                    throw new BusinessException(ResponseCodeEnum.PAY_PLATFORM_WECHAT_PARAMETERS_DOES_NOT_EXIST);
                } else {
                    throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_WECHAT_PARAMETERS_DOES_NOT_EXIST);
                }
            }

            parameterResult.getData().getParameters().forEach(p -> {
                if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)) {
                    mobilePayReq.setMerchantId(p.getValue());
                } else if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)) {
                    mobilePayReq.setApiKey(p.getValue());
                } else if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APPLET_APP_ID)) {
                    mobilePayReq.setAppletAppId(p.getValue());
                } else if(p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APPLET_APP_KEY)) {
                    mobilePayReq.setAppletAppKey(p.getValue());
                }
            });

            mobilePayReq.setJsCode(jsCode);
            weChatPayResultResp = weChatPayService.jsAPIPay(mobilePayReq, request);
            if (weChatPayResultResp.isSuccess()) {
                //保存交易记录
                AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
                accountTradeRecordDO.setTradeCode(tradeCode);
                accountTradeRecordDO.setTradeMoney(money);
                accountTradeRecordDO.setMemberAssetAccount(memberAssetAccountDO);
                accountTradeRecordDO.setOperation(TradeOperationEnum.ACCOUNT_RECHARGE.getCode());
                accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING.getCode());
                accountTradeRecordDO.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
                accountTradeRecordDO.setTradeTime(System.currentTimeMillis());
                accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);

                //发送消息到队列(2小时)
                Long accountTradeRecordId = accountTradeRecordDO.getId();
                mqUtils.sendDelayMsg(PayMqConstant.PAY_RECHARGE_DELAY_EXCHANGE, PayMqConstant.PAY_RECHARGE_DELAY_ROUTING_KEY, accountTradeRecordId, 7200 * 1000L);

                //支付返回结果
                Map<String, String> body = (Map<String, String>) weChatPayResultResp.getBody();
                AccountRechargeAppletResp accountRechargeAppletResp = new AccountRechargeAppletResp();
                accountRechargeAppletResp.setAppId(body.get("appId"));
                accountRechargeAppletResp.setNoncestr(body.get("nonceStr"));
                accountRechargeAppletResp.setPrepayid(body.get("package"));
                accountRechargeAppletResp.setSignType(body.get("signType"));
                accountRechargeAppletResp.setSign(body.get("paySign"));
                accountRechargeAppletResp.setTimestamp(body.get("timeStamp"));
                accountRechargeAppletResp.setTradeRecordId(accountTradeRecordId);
                return accountRechargeAppletResp;
            } else {
                throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL, weChatPayResultResp.getMessage());
            }
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }
    }

    /**
     * 查询账户是否可用
     * @param tradeRecordId 交易记录Id
     * @return 是-true，否-false
     */
    @Override
    public Boolean getRechargeResult(Long tradeRecordId) {
        AccountTradeRecordDO accountTradeRecordDO = accountTradeRecordRepository.findById(tradeRecordId).orElse(null);
        return accountTradeRecordDO != null && TradeStatusEnum.CONFIRM_ACCOUNT.getCode().equals(accountTradeRecordDO.getStatus());
    }

    /**
     * 支付回调
     * @param out_trade_no 交易单号
     * @param trade_no 支付平台订单号
     */
    @Transactional
    @Override
    public void payNotify(String out_trade_no, String trade_no) {
        AccountTradeRecordDO accountTradeRecordDO = accountTradeRecordRepository.findFirstByTradeCode(out_trade_no);
        if(accountTradeRecordDO != null && TradeStatusEnum.CONFIRM_ACCOUNT.getCode().equals(accountTradeRecordDO.getStatus())){
            log.info("充值回调，重复的支付流水号："+trade_no);
            return;
        }
        if (accountTradeRecordDO != null && TradeStatusEnum.PAYING.getCode().equals(accountTradeRecordDO.getStatus())) {
            RechargeAccountReq rechargeAccountReq = new RechargeAccountReq();
            rechargeAccountReq.setZflsh(trade_no);
            rechargeAccountReq.setBz(accountTradeRecordDO.getRemark());
            rechargeAccountReq.setZfsj(accountTradeRecordDO.getTradeTime() != null ? DateTimeUtil.timestampToDateTimeString(accountTradeRecordDO.getTradeTime()): null);
            rechargeAccountReq.setDzje(accountTradeRecordDO.getTradeMoney());
            MemberAssetAccountDO memberAssetAccount = accountTradeRecordDO.getMemberAssetAccount();
            rechargeAccountReq.setKhbm(memberAssetAccount.getMemberCode());
            rechargeAccountReq.setKhmc(memberAssetAccount.getMemberName());
            rechargeAccountReq.setZfqd(OrderPayChannelEnum.WEI_QI_FU.getName());
            //更新交易记录
            accountTradeRecordDO.setStatus(TradeStatusEnum.CONFIRM_ACCOUNT.getCode());
            accountTradeRecordDO.setPayPlatformTradeCode(out_trade_no);
            rechargeAccountReq.setCzdh(accountTradeRecordDO.getPayPlatformTradeCode());
            eosApiService.rechargeAccount(rechargeAccountReq);
            accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);
            //获取交易金额
            BigDecimal tradeMoney = accountTradeRecordDO.getTradeMoney();

            //更新账户信息
            MemberAssetAccountDO memberAssetAccountDO = accountTradeRecordDO.getMemberAssetAccount();
            //校验账号
            this.checkMemberAssetAccount(memberAssetAccountDO);
            BigDecimal accountBalance = memberAssetAccountDO.getAccountBalance();
            BigDecimal accountBalanceMoney = accountBalance.add(tradeMoney).setScale(2, RoundingMode.HALF_UP);
            memberAssetAccountDO.setAccountBalance(accountBalanceMoney);
            memberAssetAccountDO.setAccountBalanceEncrypt(DigestUtils.md5DigestAsHex(String.valueOf(accountBalanceMoney).getBytes()));
            memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);

        }
    }

    /**
     * 更新交易记录为支付失败
     * @param accountTradeRecordId 交易记录id
     */
    @Override
    public void updateAccountTradeRecordPayingFail(Long accountTradeRecordId) {
        AccountTradeRecordDO accountTradeRecordDO = accountTradeRecordRepository.findById(accountTradeRecordId).orElse(null);
        if (accountTradeRecordDO != null) {
            Integer status = accountTradeRecordDO.getStatus();
            if (TradeStatusEnum.PAYING.getCode().equals(status)) {
                accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING_FAIL.getCode());
                accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);
            }
        }
    }

    /**
     * 资金账户提现
     * @param cashOutReq 接口参数
     * @return 提示信息
     */
    @Transactional
    @Override
    public void cashOutAssetAccount(CashOutReq cashOutReq) {
        Long memberAssetAccountId = cashOutReq.getMemberAssetAccountId();
        BigDecimal money = cashOutReq.getMoney().setScale(2, RoundingMode.HALF_UP);
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountRepository.findById(memberAssetAccountId).orElse(null);
        if(Objects.isNull(memberAssetAccountDO)){
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }

        //校验账户
        this.checkMemberAssetAccount(memberAssetAccountDO);
        //判断交易金额
        BigDecimal accountBalance = memberAssetAccountDO.getAccountBalance();
        BigDecimal lockBalance = memberAssetAccountDO.getLockBalance();
        if (money.compareTo(accountBalance.subtract(lockBalance)) > 0) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_MORE_EXCEPTION);
        }

        //保存账户信息
        BigDecimal lockMoney = money.add(lockBalance).setScale(2, RoundingMode.HALF_UP);
        memberAssetAccountDO.setAccountBalance(accountBalance);
        memberAssetAccountDO.setAccountBalanceEncrypt(DigestUtils.md5DigestAsHex(String.valueOf(accountBalance).getBytes()));
        memberAssetAccountDO.setLockBalance(lockMoney);
        memberAssetAccountDO.setLockBalanceEncrypt(DigestUtils.md5DigestAsHex(String.valueOf(lockMoney).getBytes()));
        memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);

        //保存交易记录
        AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
        accountTradeRecordDO.setTradeCode(redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX));
        accountTradeRecordDO.setTradeMoney(money);
        accountTradeRecordDO.setOperation(TradeOperationEnum.ACCOUNT_CASH_OUT.getCode());
        accountTradeRecordDO.setStatus(TradeStatusEnum.APPLY_CASH_OUT.getCode());
        accountTradeRecordDO.setMemberAssetAccount(memberAssetAccountDO);
        accountTradeRecordDO.setRemark(TradeOperationEnum.ACCOUNT_CASH_OUT.getMessage());
        accountTradeRecordDO.setTradeTime(System.currentTimeMillis());
        accountTradeRecordDO.setBankName(cashOutReq.getBankName());
        accountTradeRecordDO.setBankAccount(cashOutReq.getBankAccount());
        accountTradeRecordDO.setBankAccountName(cashOutReq.getBankAccountName());
        accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);

        //发送消息
        SystemMessageReq systemMessageReq = new SystemMessageReq();
        if (MemberLevelTypeEnum.PLATFORM.getCode().equals(memberAssetAccountDO.getMemberLevelType())) {
            // 平台会员
            systemMessageReq.setMemberId(0L);
            systemMessageReq.setRoleId(0L);
            systemMessageReq.setMessageNotice(MessageNoticeEnum.WITHDRAW_PLATFORM_WAIT_AUDIT.getCode());
            systemMessageReq.setParams(Collections.singletonList(memberAssetAccountDO.getMemberName()));
        } else {
            // 普通会员
            systemMessageReq.setMemberId(memberAssetAccountDO.getParentMemberId());
            systemMessageReq.setRoleId(memberAssetAccountDO.getParentMemberRoleId());
            systemMessageReq.setMessageNotice(MessageNoticeEnum.WITHDRAW_WAIT_AUDIT.getCode());
            systemMessageReq.setParams(Collections.singletonList(memberAssetAccountDO.getMemberName()));
        }
        //将消息存入队列中
        messageService.sendSystemMessage(systemMessageReq);
    }

    /**
     * 支付提现
     * @param payReq 接口参数
     */
    @Transactional
    @Override
    public void payCashOut(PayReq payReq) {
        Long id = payReq.getId();
        AccountTradeRecordDO accountTradeRecordDO = accountTradeRecordRepository.findById(id).orElse(null);
        if (accountTradeRecordDO != null) {
            MemberAssetAccountDO memberAssetAccountDO = accountTradeRecordDO.getMemberAssetAccount();
            if (memberAssetAccountDO != null) {
                MemberAssetAccountDO assetAccount = this.getAccount(memberAssetAccountDO);
                memberAssetAccountRepository.saveAndFlush(assetAccount);

                //更新交易记录
                accountTradeRecordDO.setTradeTime(System.currentTimeMillis());
                accountTradeRecordDO.setStatus(TradeStatusEnum.CASH_OUT_SUCCESS.getCode());
                accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);

                //保存提现处理记录
                AccountCashOutRecordDO accountCashOutRecordDO = new AccountCashOutRecordDO();
                accountCashOutRecordDO.setTradeCode(accountTradeRecordDO.getTradeCode());
                accountCashOutRecordDO.setTradeMoney(accountTradeRecordDO.getTradeMoney());
                accountCashOutRecordDO.setOperation(accountTradeRecordDO.getOperation());
                accountCashOutRecordDO.setRemark(payReq.getRemark());
                accountCashOutRecordDO.setPayType(payReq.getPayType());
                accountCashOutRecordDO.setPayChannel(payReq.getPayChannel());
                accountCashOutRecordDO.setStatus(TradeStatusEnum.CASH_OUT_SUCCESS.getCode());
                accountCashOutRecordDO.setTradeTime(System.currentTimeMillis());
                accountCashOutRecordRepository.saveAndFlush(accountCashOutRecordDO);

                //发送消息
                SystemMessageReq systemMessageReq = new SystemMessageReq();
                systemMessageReq.setMemberId(assetAccount.getMemberId());
                systemMessageReq.setRoleId(assetAccount.getMemberRoleId());
                systemMessageReq.setMessageNotice(MessageNoticeEnum.WITHDRAW_AUDIT.getCode());
                systemMessageReq.setParams(Collections.singletonList("支付成功"));
                //将消息存入队列中
                messageService.sendSystemMessage(systemMessageReq);
            } else {
                throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
            }
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_PAY_NOT_EXIST);
        }
    }

    /**
     * 余额支付
     * @param balancePayReq 支付接口参数
     * @return 支付交易号
     */
    @Transactional
    @Override
    public String balancePay(BalancePayReq balancePayReq) {
        log.info("余额支付接收到参数 => " + SerializeUtil.serialize(balancePayReq));

        Long memberId = balancePayReq.getMemberId();
        Long memberRoleId = balancePayReq.getMemberRoleId();
        Long parentMemberId = balancePayReq.getParentMemberId();
        Long parentMemberRoleId = balancePayReq.getParentMemberRoleId();
        String payPassword = balancePayReq.getPayPassword();
        BigDecimal payMoney = balancePayReq.getPayMoney();
        String remark = balancePayReq.getRemark();
        Integer payType = balancePayReq.getPayType();
        String orderCode = balancePayReq.getOrderCode();
        //查询账户信息
        MemberAssetAccountDO memberAssetAccountDO;
        if (payType.equals(PayTypeEnum.PLATFORM.getCode())) {
            memberAssetAccountDO = memberAssetAccountRepository.findFirstByMemberIdAndMemberRoleIdAndMemberLevelType(memberId, memberRoleId, MemberLevelTypeEnum.PLATFORM.getCode());
        } else {
            memberAssetAccountDO = memberAssetAccountRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(memberId, memberRoleId, parentMemberId, parentMemberRoleId);
        }

        if(memberAssetAccountDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }

        //检验账户
        this.checkMemberAssetAccount(memberAssetAccountDO);
        //检验支付密码
        this.checkMemberPayPassword(memberId, payPassword);
        //检验支付金额
        BigDecimal accountBalance = memberAssetAccountDO.getAccountBalance();
        if (accountBalance.subtract(memberAssetAccountDO.getLockBalance()).compareTo(payMoney) < 0) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_MORE_EXCEPTION);
        }

        //四舍五入，保留两位小数
        BigDecimal accountBalanceMoney = accountBalance.subtract(payMoney).setScale(2, RoundingMode.HALF_UP);
        memberAssetAccountDO.setAccountBalance(accountBalanceMoney);
        memberAssetAccountDO.setAccountBalanceEncrypt(DigestUtils.md5DigestAsHex(String.valueOf(accountBalanceMoney).getBytes()));
        memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);

        //保存交易记录
        String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);
        AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
        accountTradeRecordDO.setMemberAssetAccount(memberAssetAccountDO);
        accountTradeRecordDO.setTradeCode(tradeCode);
        accountTradeRecordDO.setTradeMoney(payMoney);
        accountTradeRecordDO.setOperation(TradeOperationEnum.ORDER_PAY.getCode());
        accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING_SUCCESS.getCode());
        accountTradeRecordDO.setRemark(remark);
        accountTradeRecordDO.setTradeTime(System.currentTimeMillis());
        accountTradeRecordDO.setPayPlatformTradeCode(orderCode);
        accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);
        return tradeCode;
    }

    /**
     * 余额返现
     * @param balanceCashbackReq 接口参数
     * @return 交易流水号
     */
    @Transactional
    @Override
    public String balanceCashback(BalanceCashbackReq balanceCashbackReq) {
        Long memberId = balanceCashbackReq.getMemberId();
        Long memberRoleId = balanceCashbackReq.getMemberRoleId();
        Long parentMemberId = balanceCashbackReq.getParentMemberId();
        Long parentMemberRoleId = balanceCashbackReq.getParentMemberRoleId();
        BigDecimal cashbackMoney = balanceCashbackReq.getCashbackMoney();
        String remark = balanceCashbackReq.getRemark();
        Integer cashbackType = balanceCashbackReq.getCashbackType();
        String orderCode = balanceCashbackReq.getOrderCode();

        //查询账户信息
        MemberAssetAccountDO memberAssetAccountDO;
        if (PayTypeEnum.PLATFORM.getCode().equals(cashbackType)) {
            memberAssetAccountDO = memberAssetAccountRepository.findFirstByMemberIdAndMemberRoleIdAndMemberLevelType(memberId, memberRoleId, MemberLevelTypeEnum.PLATFORM.getCode());
        } else {
            memberAssetAccountDO = memberAssetAccountRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(memberId, memberRoleId, parentMemberId, parentMemberRoleId);
        }

        if(memberAssetAccountDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }

        //检验账户
        this.checkMemberAssetAccount(memberAssetAccountDO);
        //返现金额
        BigDecimal accountBalance = memberAssetAccountDO.getAccountBalance();
        //四舍五入，保留两位小数
        BigDecimal accountBalanceMoney = accountBalance.add(cashbackMoney).setScale(2, RoundingMode.HALF_UP);
        memberAssetAccountDO.setAccountBalance(accountBalanceMoney);
        memberAssetAccountDO.setAccountBalanceEncrypt(DigestUtils.md5DigestAsHex(String.valueOf(accountBalanceMoney).getBytes()));
        memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);

        //保存���易记录
        String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);
        AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
        accountTradeRecordDO.setMemberAssetAccount(memberAssetAccountDO);
        accountTradeRecordDO.setTradeCode(tradeCode);
        accountTradeRecordDO.setTradeMoney(cashbackMoney);
        accountTradeRecordDO.setOperation(TradeOperationEnum.ORDER_REBATE.getCode());
        accountTradeRecordDO.setStatus(TradeStatusEnum.CONFIRM_ACCOUNT.getCode());
        accountTradeRecordDO.setRemark(remark);
        accountTradeRecordDO.setTradeTime(System.currentTimeMillis());
        accountTradeRecordDO.setPayPlatformTradeCode(orderCode);
        accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);
        return tradeCode;
    }

    /**
     * 余额退款
     * @param balanceRefundReq 接口参数
     * @return 退款是否成功
     */
    @Transactional
    @Override
    public Boolean balanceRefund(BalanceRefundReq balanceRefundReq) {
        log.info("余额退款接收到参数 => " + SerializeUtil.serialize(balanceRefundReq));
        BigDecimal tradeMoney = balanceRefundReq.getTradeMoney();
        String payPlatformTradeCode = balanceRefundReq.getPayPlatformTradeCode();
        String remark = balanceRefundReq.getRemark();
        List<AccountTradeRecordDO> accountTradeRecordDOs =  accountTradeRecordRepository.findByPayPlatformTradeCodeAndOperationAndStatus(payPlatformTradeCode, TradeOperationEnum.ORDER_PAY.getCode(), TradeStatusEnum.PAYING_SUCCESS.getCode());
        if(CollectionUtils.isEmpty(accountTradeRecordDOs)){
            return false;
        }
        AccountTradeRecordDO accountTradeRecord = accountTradeRecordDOs.get(0);
        MemberAssetAccountDO memberAssetAccount = accountTradeRecord.getMemberAssetAccount();
        //获取存欠数据并更新
        List<GetAccountInfoResp> accountInfo = eosApiService.getAccountInfo(Arrays.asList(memberAssetAccount.getMemberCode()));
        GetAccountInfoResp getAccountInfoResp = accountInfo.get(0);
        memberAssetAccount.setMaterialStock(BigDecimalUtil.nullToZero(getAccountInfoResp.getQmjz_jl()));
        memberAssetAccount.setAccountBalance(BigDecimalUtil.nullToZero(getAccountInfoResp.getQmje()));
        //保存交易记录
        String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);
        //this.checkMemberAssetAccount(memberAssetAccount);
        //验证会员状态
        Integer memberStatus = memberAssetAccount.getMemberStatus();
        if (!MemberStatusEnum.NORMAL.getCode().equals(memberStatus)) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_STATUS_EXCEPTION);
        }
        //验证会员账户状态
        Integer accountStatus = memberAssetAccount.getAccountStatus();
        if (!AccountStatusEnum.THAW.getCode().equals(accountStatus)) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_STATUS_EXCEPTION);
        }
        List<AccountTradeRecordDO> accountTradeRecordDOS = new ArrayList<>();
        for (AccountTradeRecordDO accountTradeRecordDO : accountTradeRecordDOs) {
            AccountTradeRecordDO addAccountTradeRecordDO = new AccountTradeRecordDO();
            if(MemberAccountTradeCategoryEnum.BANLANCE_MONEY.getCode().equals(accountTradeRecordDO.getCategory())){
                BigDecimal balance = BigDecimalUtil.subtract(memberAssetAccount.getAccountBalance() ,memberAssetAccount.getLockBalance());
                memberAssetAccount.setAccountBalance(BigDecimalUtil.add(memberAssetAccount.getAccountBalance(), accountTradeRecordDO.getTradeMoney()));
                memberAssetAccount.setLockBalance(BigDecimalUtil.subtract(memberAssetAccount.getLockBalance(),accountTradeRecordDO.getTradeMoney()));
                addAccountTradeRecordDO.setCategory(MemberAccountTradeCategoryEnum.BANLANCE_MONEY.getCode());
                addAccountTradeRecordDO.setTradeMoneyBefore(balance);
                addAccountTradeRecordDO.setTradeMoney(accountTradeRecordDO.getTradeMoney());
                addAccountTradeRecordDO.setTradeMoneyAfter(BigDecimalUtil.add(accountTradeRecordDO.getTradeMoney(), balance));
            }else if(MemberAccountTradeCategoryEnum.DEPOSIT_MATERIAL.getCode().equals(accountTradeRecordDO.getCategory())){
                BigDecimal materialStock = BigDecimalUtil.subtract(memberAssetAccount.getMaterialStock() ,memberAssetAccount.getLockMaterialStock());
                memberAssetAccount.setLockMaterialStock(BigDecimalUtil.subtract(memberAssetAccount.getLockMaterialStock(), accountTradeRecordDO.getTradeMoney()));
                memberAssetAccount.setMaterialStock(BigDecimalUtil.add(memberAssetAccount.getMaterialStock(), accountTradeRecordDO.getTradeMoney()));
                addAccountTradeRecordDO.setCategory(MemberAccountTradeCategoryEnum.DEPOSIT_MATERIAL.getCode());
                addAccountTradeRecordDO.setTradeMoneyBefore(materialStock);
                addAccountTradeRecordDO.setTradeMoney(accountTradeRecordDO.getTradeMoney());
                addAccountTradeRecordDO.setTradeMoneyAfter(BigDecimalUtil.add(accountTradeRecordDO.getTradeMoney(), materialStock));
            }
            addAccountTradeRecordDO.setTransactionType(AccountTradeRecordTypeEnum.PAYMENT_PLATFORM.getCode());
            addAccountTradeRecordDO.setTradeCode(tradeCode);
            addAccountTradeRecordDO.setTradeMoney(tradeMoney);
            addAccountTradeRecordDO.setOperation(TradeOperationEnum.ORDER_REFUND.getCode());
            addAccountTradeRecordDO.setStatus(TradeStatusEnum.CONFIRM_ACCOUNT.getCode());
            addAccountTradeRecordDO.setRemark(remark);
            addAccountTradeRecordDO.setTradeTime(System.currentTimeMillis());
            addAccountTradeRecordDO.setPayPlatformTradeCode(payPlatformTradeCode);
            addAccountTradeRecordDO.setMemberAssetAccount(memberAssetAccount);
            accountTradeRecordDOS.add(accountTradeRecordDO);
        }
        memberAssetAccountRepository.saveAndFlush(memberAssetAccount);
        accountTradeRecordRepository.saveAll(accountTradeRecordDOS);
        return true;
    }

    /**
     * 查询登��用户的账户余额
     * @param fundMode 资金归集模式: 1-平台代收; 2-会员直接到账
     * @param parentMemberId 上级会员id
     * @param parentMemberRoleId 上级会员角色id
     */
    @Override
    public BigDecimal getUserBalance(Integer fundMode, Long memberId, Long memberRoleId, Long parentMemberId, Long parentMemberRoleId) {
        //查询账户信息
        MemberAssetAccountDO memberAssetAccountDO;
        if (fundMode.equals(PayTypeEnum.PLATFORM.getCode())) {
            memberAssetAccountDO = memberAssetAccountRepository.findFirstByMemberIdAndMemberRoleIdAndMemberLevelType(memberId, memberRoleId, MemberLevelTypeEnum.PLATFORM.getCode());
        } else {
            memberAssetAccountDO = memberAssetAccountRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(memberId, memberRoleId, parentMemberId, parentMemberRoleId);
        }

        if(memberAssetAccountDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }

        //检验账户
        this.checkMemberAssetAccount(memberAssetAccountDO);
        //四舍五入，保留两位小数
        return memberAssetAccountDO.getAccountBalance().subtract(memberAssetAccountDO.getLockBalance()).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 查询采购商的账户余额
     * @param childMemberId 下级会员id
     * @param childMemberRoleId 下级会员角色id
     */
    @Override
    public BigDecimal getChildUserBalance(Long memberId, Long memberRoleId, Long childMemberId, Long childMemberRoleId) {
        //查询账户信息
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(childMemberId, childMemberRoleId, memberId, memberRoleId);

        if(memberAssetAccountDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
        }

        //检验账户
        this.checkMemberAssetAccount(memberAssetAccountDO);
        //四舍五入，保留两位小数
        return memberAssetAccountDO.getAccountBalance().subtract(memberAssetAccountDO.getLockBalance()).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 查询登录用户的所有账户余额
     * @param memberId 会员Id
     * @param memberRoleId 会员角色Id
     * @return 所有账户余额的和
     */
    @Override
    public BigDecimal getUserAllBalance(Long memberId, Long memberRoleId) {
        List<MemberAssetAccountDO> memberAssetAccountDOList = memberAssetAccountRepository.findByMemberIdAndMemberRoleId(memberId, memberRoleId);
        return memberAssetAccountDOList.stream().map(memberAssetAccount -> {
            //检验账户
            this.checkMemberAssetAccount(memberAssetAccount);
            //四舍五入，保留两位小数
            return memberAssetAccount.getAccountBalance().subtract(memberAssetAccount.getLockBalance()).setScale(2, RoundingMode.HALF_UP);
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 查询平台账户详情
     */
    @Override
    public MemberAssetAccountDO getPlatFormAssetAccount(UserLoginCacheDTO sysUser) {
        List<MemberAssetAccountDO> memberAssetAccountDOList = memberAssetAccountRepository.findByMemberIdAndMemberRoleId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        for(MemberAssetAccountDO memberAssetAccountDO : memberAssetAccountDOList){
            Integer memberLevelType = memberAssetAccountDO.getMemberLevelType();
            if(MemberLevelTypeEnum.PLATFORM.getCode().equals(memberLevelType)){
                return memberAssetAccountDO;
            }
        }
        throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
    }

    /**
     * 查询商户账户详情
     */
    @Override
    public MemberAssetAccountDO getMerchantFormAssetAccount(UserLoginCacheDTO sysUser) {
        List<MemberAssetAccountDO> memberAssetAccountDOList = memberAssetAccountRepository.findByMemberIdAndMemberRoleId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        for(MemberAssetAccountDO memberAssetAccountDO : memberAssetAccountDOList){
            Integer memberLevelType = memberAssetAccountDO.getMemberLevelType();
            if(MemberLevelTypeEnum.MERCHANT.getCode().equals(memberLevelType)){
                return memberAssetAccountDO;
            }
        }
        throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
    }

    /**
     * 查询会员用户的账户详情
     * @param memberId              登录用户会员id
     * @param memberRoleId          登录用户会员角色id
     * @param parentMemberId        上级会员id
     * @param parentMemberRoleId    上级会员角色id
     * @return  账户详情
     */
    @Override
    public MemberAssetAccountResp getUserAssetAccount(Long memberId, Long memberRoleId, Long parentMemberId, Long parentMemberRoleId) {
        List<MemberAssetAccountDO> memberAssetAccountDOList = memberAssetAccountRepository.findByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(memberId, memberRoleId, parentMemberId, parentMemberRoleId);
        if(memberAssetAccountDOList != null && memberAssetAccountDOList.size() > 0){
            if(memberAssetAccountDOList.size() == 1){
                MemberAssetAccountDO assetAccount = memberAssetAccountDOList.get(0);
                return BeanUtil.copyProperties(assetAccount, MemberAssetAccountResp.class);
            }else{
                throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_EXCEPTION);
            }
        }
        throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_NOT_EXIST);
    }

    /**
     * 统一检验账户
     */
    @Override
    public void checkMemberAssetAccount(MemberAssetAccountDO memberAssetAccountDO) {
        //验证会员状态
        Integer memberStatus = memberAssetAccountDO.getMemberStatus();
        if (!MemberStatusEnum.NORMAL.getCode().equals(memberStatus)) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_STATUS_EXCEPTION);
        }
        //验证会员账户状态
        Integer accountStatus = memberAssetAccountDO.getAccountStatus();
        if (!AccountStatusEnum.THAW.getCode().equals(accountStatus)) {
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_STATUS_EXCEPTION);
        }
        //验证金额
//        BigDecimal account = memberAssetAccountDO.getAccountBalance();
//        BigDecimal lock = memberAssetAccountDO.getLockBalance();
//        String accountBalance = DigestUtils.md5DigestAsHex(String.valueOf(account).getBytes());
//        String accountBalanceEncrypt = memberAssetAccountDO.getAccountBalanceEncrypt();
//        String lockBalance = DigestUtils.md5DigestAsHex(String.valueOf(lock).getBytes());
//        String lockBalanceEncrypt = memberAssetAccountDO.getLockBalanceEncrypt();
//        if (!accountBalance.equals(accountBalanceEncrypt) || !lockBalance.equals(lockBalanceEncrypt)) {
//            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_MONEY_EXCEPTION);
//        }
    }



    /**
     * 校验当前会员资金账号状态
     * true:存在资金账户, 并且账户余额不为0; false:无资金账户,或者账户余额为0
     * @param request 参数
     */
    @Override
    public Boolean checkAssetAccount(CheckAssetAccountReq request) {
        List<MemberAssetAccountDO> accountList = memberAssetAccountRepository.findByMemberId(request.getMemberId());
        return !CollectionUtils.isEmpty(accountList) && accountList.stream().anyMatch(v -> v.getAccountBalance() != null && v.getAccountBalance().compareTo(BigDecimal.ZERO) > 0);
    }

    /**
     * 统一验证支付密码
     */
    private void checkMemberPayPassword(Long memberId, String password) {
        // 校验支付密码
        MemberFeignPayPswCheckReq checkVO = new MemberFeignPayPswCheckReq();
        checkVO.setMemberId(memberId);
        checkVO.setPayPassword(password);
        WrapperResp<MemberFeignPayPswCheckResultResp> checkResultVOWrapperResp = memberInnerControllerFeign.checkMemberPayPassword(checkVO);
        if (checkResultVOWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(ResponseCodeEnum.getByCode(checkResultVOWrapperResp.getCode()));
        }

        if (!checkResultVOWrapperResp.getData().getCheckResult().equals(MemberPayPasswordCheckEnum.CORRECT.getCode())) {
            // 支付密码未设置
            if (checkResultVOWrapperResp.getData().getCheckResult().equals(MemberPayPasswordCheckEnum.NOT_SET.getCode())) {
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PASSWORD_NOT_SET);
            }
            // 支付密码错误
            else if (checkResultVOWrapperResp.getData().getCheckResult().equals(MemberPayPasswordCheckEnum.INCORRECT.getCode())) {
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PASSWORD_INCORRECT);
            }
            // 支付状态未知时，返回服务内部错误
            else {
                throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR);
            }
        }
    }

    /**
     * 计算资金
     * @param memberAssetAccountDO 接口参数
     * @return 计算结果
     */
    private MemberAssetAccountDO getAccount(MemberAssetAccountDO memberAssetAccountDO) {
        BigDecimal account = memberAssetAccountDO.getAccountBalance();
        BigDecimal lock = memberAssetAccountDO.getLockBalance();
        //检验账户
        this.checkMemberAssetAccount(memberAssetAccountDO);
        //计算余额和锁定金额
        BigDecimal defaultMoney = BigDecimal.ZERO;
        String defaultMoneyEncrypt = DigestUtils.md5DigestAsHex(String.valueOf(defaultMoney).getBytes());
        memberAssetAccountDO.setLockBalance(defaultMoney);
        memberAssetAccountDO.setLockBalanceEncrypt(defaultMoneyEncrypt);

        //四舍五入，保留两位小数
        BigDecimal accountBalanceMoney = account.subtract(lock).setScale(2, RoundingMode.HALF_UP);
        memberAssetAccountDO.setAccountBalance(accountBalanceMoney);
        memberAssetAccountDO.setAccountBalanceEncrypt(DigestUtils.md5DigestAsHex(String.valueOf(accountBalanceMoney).getBytes()));
        return memberAssetAccountDO;
    }

    /**
     * 构建查询条件
     * @param memberAssetAccountMiddle 接口参数
     * @return 查询条件
     */
    private Specification<MemberAssetAccountDO> getSpecification(MemberAssetAccountReq memberAssetAccountMiddle) {
        return (root, query, criteriaBuilder) -> {
            Predicate finalConditions = criteriaBuilder.conjunction();

            String parentMemberName = memberAssetAccountMiddle.getParentMemberName();
            Long parentMemberId = memberAssetAccountMiddle.getParentMemberId();
            Long parentMemberRoleId = memberAssetAccountMiddle.getParentMemberRoleId();
            String memberName = memberAssetAccountMiddle.getMemberName();
            Long memberId = memberAssetAccountMiddle.getMemberId();
            Long memberRoleId = memberAssetAccountMiddle.getMemberRoleId();
            Integer memberType = memberAssetAccountMiddle.getMemberType();
            List<Integer> memberLevelTypeList = memberAssetAccountMiddle.getMemberLevelTypeList();
            Integer memberStatus = memberAssetAccountMiddle.getMemberStatus();
            Integer accountStatus = memberAssetAccountMiddle.getAccountStatus();
            Long startTime = memberAssetAccountMiddle.getStartTime();
            Long endTime = memberAssetAccountMiddle.getEndTime();
            Boolean enableMultiTenancy = memberAssetAccountMiddle.getEnableMultiTenancy();

            //上级会员名称
            if (StringUtils.hasLength(parentMemberName)) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.like(root.get(MemberAssetAccountDO.Fields.parentMemberName).as(String.class), "%" + parentMemberName + "%"));
            }
            //上级会员id
            if (parentMemberId != null && parentMemberId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.parentMemberId).as(Long.class), parentMemberId));
            }
            //上级会员角色id
            if (parentMemberRoleId != null && parentMemberRoleId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.parentMemberRoleId).as(Long.class), parentMemberRoleId));
            }
            //会员名称
            if (StringUtils.hasLength(memberName)) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.like(root.get(MemberAssetAccountDO.Fields.memberName).as(String.class), "%" + memberName + "%"));
            }
            //会员id
            if (memberId != null && memberId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.memberId).as(Long.class), memberId));
            }
            //会员角色id
            if (memberRoleId != null && memberRoleId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.memberRoleId).as(Long.class), memberRoleId));
            }
            //会员类型
            if (memberType != null && memberType > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.memberType).as(Integer.class), memberType));
            }
            //会员等级类型
            if (memberLevelTypeList != null && !memberLevelTypeList.isEmpty()) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.in(root.get(MemberAssetAccountDO.Fields.memberLevelType)).value(memberLevelTypeList));
            }
            //如果开启SaaS多租户,则不筛选会员等级类型为平台会员的数据
            if (enableMultiTenancy){
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.notEqual(root.get(MemberAssetAccountDO.Fields.memberLevelType).as(Integer.class), MemberLevelTypeEnum.PLATFORM.getCode()));
            }
            //会员状态
            if (memberStatus != null && memberStatus > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.memberStatus).as(Integer.class), memberStatus));
            }
            //账号状态
            if (accountStatus != null && accountStatus > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.accountStatus).as(Integer.class), accountStatus));
            }
            //开始时间
            if (startTime != null && startTime > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.ge(root.get(MemberAssetAccountDO.Fields.createTime), startTime));
            }
            //结束时间
            if (endTime != null && endTime > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.le(root.get(MemberAssetAccountDO.Fields.createTime), endTime));
            }
            return finalConditions;
        };
    }


    /**
     * 构建查询条件
     * @param memberAssetAccountMiddle 接口参数
     * @return 查询条件
     */
    private Specification<MemberAssetAccountDO> getPageQuerySpecification(MemberAssetAccountReq memberAssetAccountMiddle, MemberAssetAccountPageQueryReq pageQueryReq) {
        return (root, query, criteriaBuilder) -> {
            Predicate finalConditions = criteriaBuilder.conjunction();
            String parentMemberName = memberAssetAccountMiddle.getParentMemberName();
            String memberName = memberAssetAccountMiddle.getMemberName();
            Integer memberType = memberAssetAccountMiddle.getMemberType();
            Integer memberStatus = memberAssetAccountMiddle.getMemberStatus();
            Integer accountStatus = memberAssetAccountMiddle.getAccountStatus();
            LocalDateTime lineOfCreditUpdateStartTime = null;
            LocalDateTime lineOfCreditUpdateEndTime = null;
            if(pageQueryReq.getUpdateStartTime() != null){
                lineOfCreditUpdateStartTime = DateTimeUtil.toLocalDateTime(pageQueryReq.getUpdateStartTime());
            }
            if(pageQueryReq.getUpdateEndTime() != null){
                lineOfCreditUpdateEndTime =DateTimeUtil.toLocalDateTime(pageQueryReq.getUpdateEndTime());
            }

            finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.parentMemberId).as(Long.class), baiTaiMemberProperties.getSelfMemberId()));
            finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.parentMemberRoleId).as(Long.class), baiTaiMemberProperties.getSelfRoleId()));
            finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.memberRoleId).as(Long.class), baiTaiMemberProperties.getCustomerRoleId()));
            //上级会员名称
            if (StringUtils.hasLength(parentMemberName)) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.like(root.get(MemberAssetAccountDO.Fields.parentMemberName).as(String.class), "%" + parentMemberName + "%"));
            }
            //会员名称
            if (StringUtils.hasLength(memberName)) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.like(root.get(MemberAssetAccountDO.Fields.memberName).as(String.class), "%" + memberName + "%"));
            }
            //会员类型
            if (memberType != null && memberType > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.memberType).as(Integer.class), memberType));
            }
            //会员状态
            if (memberStatus != null && memberStatus > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.memberStatus).as(Integer.class), memberStatus));
            }
            //账号状态
            if (accountStatus != null && accountStatus > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(MemberAssetAccountDO.Fields.accountStatus).as(Integer.class), accountStatus));
            }
            if(lineOfCreditUpdateStartTime != null){
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.greaterThanOrEqualTo(root.get(MemberAssetAccountDO.Fields.syncUpdateTime), lineOfCreditUpdateStartTime));
            }
            if(lineOfCreditUpdateEndTime != null){
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.lessThanOrEqualTo(root.get(MemberAssetAccountDO.Fields.syncUpdateTime), lineOfCreditUpdateEndTime));
            }
            return finalConditions;
        };
    }


    @Override
    public List<MemberLineOfCreditResp> findByMemberIds(MemberIdsReq memberIdsReq) {
        BigDecimal goldPrice = getGoldPrice();
        List<MemberAssetAccountDO> memberAssetAccountDOS = memberAssetAccountRepository.findByMemberIdInAndParentMemberRoleIdAndParentMemberIdAndMemberRoleId(memberIdsReq.getMemberIds(), baiTaiMemberProperties.getSelfRoleId(), baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getCustomerRoleId());
        List<MemberLineOfCreditResp> memberLineOfCreditResps = memberAssetAccountDOS.stream().map(
                memberAssetAccountDO -> {
                    MemberLineOfCreditResp memberLineOfCreditResp = BeanUtil.copyProperties(memberAssetAccountDO, MemberLineOfCreditResp.class);
                    boolean zeroOrNegative = BigDecimalUtil.isZeroOrNegative(memberAssetAccountDO.getMaterialStock());
                    //如果存欠金料小于0，则授信可用 = 授信额度 + 存欠金料，否则授信可用 = 授信额度
                    BigDecimal unUsedLineOfCredit = zeroOrNegative ? BigDecimalUtil.add(memberAssetAccountDO.getLineOfCredit(), memberAssetAccountDO.getMaterialStock()) : memberAssetAccountDO.getLineOfCredit();
                    memberLineOfCreditResp.setUnusedlinfeOfCredit(unUsedLineOfCredit);
                    BigDecimal availableBalance = unUsedLineOfCredit;
                    //可用 = (授信可用 + 账户余额/金价)
                    if(BigDecimalUtil.notNullOrZero(memberAssetAccountDO.getAccountBalance())){
                        BigDecimal accountBalanceToGold = memberAssetAccountDO.getAccountBalance().divide(goldPrice, 2, RoundingMode.HALF_UP);
                        availableBalance = BigDecimalUtil.add(availableBalance, accountBalanceToGold);
                    }
                    memberLineOfCreditResp.setAvailableBalance(availableBalance);
                    memberLineOfCreditResp.setBalanceAmount(memberAssetAccountDO.getAccountBalance());
                    //已使用授信额度 = 授信额度 - 授信可用额度
                    memberLineOfCreditResp.setUsedlinfeOfCredit(BigDecimalUtil.subtract(memberAssetAccountDO.getLineOfCredit(), unUsedLineOfCredit));
                    memberLineOfCreditResp.setOnlineUnusedRatio(memberAssetAccountDO.getOnlineUsedRatio());
                    memberLineOfCreditResp.setAccountId(memberAssetAccountDO.getId());
                    return memberLineOfCreditResp;
                }
        ).collect(Collectors.toList());
        return memberLineOfCreditResps;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void lineOfCreditSync(ActualControllerSyncReq req) {
        if(CollectionUtils.isEmpty(req.getCustomerCreditSyncs())){
            return;
        }
        List<MemberAssetAccountDO> updMemberAssetAccount = new ArrayList<>();
        for (CustomerCreditSync customerCreditSync : req.getCustomerCreditSyncs()) {
            List<MemberAssetAccountDO> memberAssetAccountDOS = memberAssetAccountRepository.findByParentMemberRoleIdAndParentMemberIdAndMemberRoleIdAndMemberCode(baiTaiMemberProperties.getSelfRoleId(), baiTaiMemberProperties.getSelfMemberId(), baiTaiMemberProperties.getCustomerRoleId(), customerCreditSync.getMemberCode());
            if(!CollectionUtils.isEmpty(memberAssetAccountDOS)){
                MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountDOS.get(0);
                //将kg转换为g
                if(customerCreditSync.getCreditQuota() != null){
                    memberAssetAccountDO.setLineOfCredit(customerCreditSync.getCreditQuota().multiply(BigDecimal.valueOf(1000)));
                }
                memberAssetAccountDO.setLineOfCreditUseStartTime(DateTimeUtil.parseDate(req.getValidStartTime()));
                memberAssetAccountDO.setLineOfCreditUseEndTime(DateTimeUtil.parseDate(req.getValidEndTime()));
                memberAssetAccountDO.setSyncUpdateTime(LocalDateTime.now());
                memberAssetAccountDO.setDataSource(DataSourceEnum.EOS.getCode());
                updMemberAssetAccount.add(memberAssetAccountDO);
            }
        }
        if(CollectionUtils.isEmpty(updMemberAssetAccount)){
            return;
        }
        memberAssetAccountRepository.saveAll(updMemberAssetAccount);
    }


    @Override
    public void editLineOfCredit(List<EditLineOfCreditReq> editLineOfCreditReqs) {
        List<Long> ids = editLineOfCreditReqs.stream().map(EditLineOfCreditReq::getAccountId).collect(Collectors.toList());
        List<MemberAssetAccountDO> memberAssetAccountDOS = memberAssetAccountRepository.findAllById(ids);
        Map<Long, EditLineOfCreditReq> editLineOfCreditReqMap = editLineOfCreditReqs.stream().collect(Collectors.toMap(EditLineOfCreditReq::getAccountId, Function.identity(), (e1, e2) -> e1));
        for (MemberAssetAccountDO memberAssetAccountDO : memberAssetAccountDOS) {
            EditLineOfCreditReq editLineOfCreditReq = editLineOfCreditReqMap.get(memberAssetAccountDO.getId());
            memberAssetAccountDO.setLineOfCredit(editLineOfCreditReq.getLineOfCredit());
            memberAssetAccountDO.setLineOfCreditUseStartTime(editLineOfCreditReq.getLineOfCreditUseStartTime());
            memberAssetAccountDO.setLineOfCreditUseEndTime(editLineOfCreditReq.getLineOfCreditUseEndTime());
            memberAssetAccountDO.setOnlineUsedRatio(editLineOfCreditReq.getOnlineUnusedRatio());
        }
        memberAssetAccountRepository.saveAll(memberAssetAccountDOS);
    }
}

