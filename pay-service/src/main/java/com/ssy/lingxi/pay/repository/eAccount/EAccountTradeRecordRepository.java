package com.ssy.lingxi.pay.repository.eAccount;

import com.ssy.lingxi.pay.entity.do_.eAccount.EAccountTradeRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 会员资金账户交易记录
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/08
 */
@Repository
public interface EAccountTradeRecordRepository extends JpaRepository<EAccountTradeRecordDO,Long>, JpaSpecificationExecutor<EAccountTradeRecordDO> {
    EAccountTradeRecordDO findFirstByTradeCode(String out_trade_no);
}
