package com.ssy.lingxi.pay.serviceImpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.MessageNoticeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.service.IMessageService;
import com.ssy.lingxi.component.rabbitMQ.model.req.SystemMessageReq;
import com.ssy.lingxi.pay.api.model.req.CreditBillInitReq;
import com.ssy.lingxi.pay.entity.do_.CreditBillDO;
import com.ssy.lingxi.pay.entity.do_.CreditDO;
import com.ssy.lingxi.pay.enums.CreditRepayStatusEnum;
import com.ssy.lingxi.pay.enums.CreditStatusEnum;
import com.ssy.lingxi.pay.enums.CreditTradeOperationEnum;
import com.ssy.lingxi.pay.enums.CreditTradeStatusEnum;
import com.ssy.lingxi.pay.model.req.PageQueryCreditOverdueDataReq;
import com.ssy.lingxi.pay.model.resp.*;
import com.ssy.lingxi.pay.repository.CreditBillRepository;
import com.ssy.lingxi.pay.repository.CreditRepository;
import com.ssy.lingxi.pay.service.ICreditBillService;
import com.ssy.lingxi.pay.service.ICreditTradeRecordService;
import com.ssy.lingxi.pay.service.IFeignService;
import com.ssy.lingxi.pay.util.CreditUtil;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *  授信账单接口实现
 * <AUTHOR>
 * @since 2020/8/21
 * @version 2.0.0
 */
@Service
public class CreditBillServiceImpl implements ICreditBillService {

    @Resource
    private CreditBillRepository billRepository;

    @Resource
    private CreditRepository creditRepository;

    @Resource
    private ICreditTradeRecordService tradeRecordService;

    @Resource
    private IFeignService feignService;

    @Resource
    private IMessageService messageService;

    /**
     * 初始化授信账单
     * <AUTHOR>
     * @since 2020/8/21
     * @param initVO:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    @Override
    public void init(CreditBillInitReq initVO) {
        Date time = DateUtil.parse(initVO.getInitDate(), DatePattern.NORM_DATE_PATTERN);
        // 获取账单日期
        Integer billDay = DateUtil.dayOfMonth(time);
        // 账单所属时间（格式：当月第一天0点0分时间戳）
        Long billBelongTime = DateUtil.beginOfMonth(time).getTime();

        // 查询授信列表
        List<CreditDO> creditDOList = creditRepository.findByBillDayAndStatusIsNot(billDay, CreditStatusEnum.UN_APPLY.getCode());
        if (!CollectionUtils.isEmpty(creditDOList)) {

            // 查询已存在账单列表
            List<CreditBillDO> billDOList = billRepository.findByBillBelongTimeAndBillDay(billBelongTime, billDay);

            // 定义新增账单列表
            List<CreditBillDO> addList = creditDOList.stream().filter(
                    item -> !billDOList.stream()
                            .map(CreditBillDO::getCreditId)
                            .collect(Collectors.toList())
                            .contains(item.getId()))
                    .collect(Collectors.toList())
                    .stream().map(r -> {
                        CreditBillDO addDo = new CreditBillDO();
                        addDo.setCreditId(r.getId());
                        addDo.setBillBelongTime(billBelongTime);
                        addDo.setBillDay(r.getBillDay());
                        addDo.setRepayPeriod(r.getRepayPeriod());
                        addDo.setCreateTime(System.currentTimeMillis());
                        Date expireTime = CreditUtil.getExpireTime(time, r.getBillDay(), r.getRepayPeriod());
                        addDo.setExpireTime(expireTime.getTime());
                        return addDo;
                    }).collect(Collectors.toList());

            // 新增授信账单列表
            if (!CollectionUtils.isEmpty(addList)) {
                billRepository.saveAll(addList);
            }
        }
    }

    /**
     * 初始化逾期
     * <AUTHOR>
     * @since 2020/8/28
     * @param initVO:
     * @return com.ssy.lingxi.common.response.Wrapper
     **/
    @Override
    public void initOverdue(CreditBillInitReq initVO) {
        Date time = DateUtil.parseDateTime(initVO.getInitDate());
        // 查询逾期账单
        List<CreditBillDO> billDOList = getOverdueBillList(time.getTime());
        if (!CollectionUtils.isEmpty(billDOList)) {

            List<Long> creditIdList = billDOList.stream().map(CreditBillDO::getCreditId).collect(Collectors.toList());
            List<CreditDO> creditDOList = creditRepository.findByIdIn(creditIdList);

            // 修改授信记录的到期时间为对应逾期账单时间，但不能晚于上次到期时间
            List<CreditDO> updateCreditDOList = creditDOList.stream().peek(item -> {
                CreditBillDO billDO = billDOList.stream().filter(bill -> bill.getCreditId().equals(item.getId())).findFirst().orElse(null);
                if (billDO != null && billDO.getExpireTime() <= item.getExpireTime()) {
                    item.setRePayStatus(CreditRepayStatusEnum.OVERDUE.getCode());
                    item.setExpireTime(billDO.getExpireTime());
                    item.setUpdateTime(System.currentTimeMillis());
                }
            }).collect(Collectors.toList());

            // 授信数据入库
            if (!CollectionUtils.isEmpty(updateCreditDOList)) {
                creditRepository.saveAll(updateCreditDOList);
            }
        }
    }

    /**
     * 根据到期时间查询逾期账单
     * <AUTHOR>
     * @since 2020/8/28
     * @param expireTime:
     * @return java.util.List<com.ssy.lingxi.pay.entity.do_.CreditBillDO>
     **/
    private List<CreditBillDO> getOverdueBillList(Long expireTime) {
        // 组装查询条件
        Specification<CreditBillDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            // 指定到期日期
            list.add(criteriaBuilder.equal(root.get("expireTime").as(Long.class), expireTime));
            // 账单额度大于已还额度
            list.add(criteriaBuilder.greaterThan(root.get("billQuota").as(Long.class), root.get("repayQuota").as(Long.class)));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return billRepository.findAll(spec);
    }

    /**
     * 根据授信id获取授信账单下拉框
     * <AUTHOR>
     * @since 2020/8/21
     * @param creditId:
     * @return com.ssy.lingxi.common.response.Wrapper<java.util.List < com.ssy.lingxi.pay.model.resp.CreditBillSelectVO>>
     **/
    @Override
    public List<CreditBillSelectResp> getSelect(Long creditId) {

        List<CreditBillDO> billDOList = billRepository.findByCreditIdOrderByBillBelongTimeDesc(creditId);

        List<CreditBillSelectResp> result = new ArrayList<>();
        for (int i = 0; i < billDOList.size(); i++) {
            // 当前账单
            CreditBillDO currentItem = billDOList.get(i);
            // 下个账单
            CreditBillDO nextItem = null;
            if (i < billDOList.size() - 1) {
                nextItem = billDOList.get(i + 1);
            }

            CreditBillSelectResp vo = new CreditBillSelectResp();
            vo.setId(currentItem.getId());
            Date startDate;
            Date endDate;
            if (nextItem == null) {
                startDate = DateUtil.offsetDay(DateUtil.date(currentItem.getBillBelongTime()), -1 + currentItem.getBillDay());
                endDate = DateUtil.offsetMonth(startDate, 1);
            } else {
                startDate = DateUtil.offsetDay(DateUtil.date(currentItem.getBillBelongTime()), nextItem.getBillDay());
                Date tmpDate = DateUtil.offsetDay(DateUtil.date(currentItem.getBillBelongTime()), -1 + currentItem.getBillDay());
                endDate = DateUtil.offsetMonth(tmpDate, 1);
            }
            String name = (DateUtil.format(startDate, "yyyy/MM/dd") + " ~ " + DateUtil.format(endDate, "yyyy/MM/dd"));
            vo.setName(name);

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取授信账单详情
     * <AUTHOR>
     * @since 2020/8/21
     * @param id:
     **/
    @Override
    public CreditBillDetailResp getDetail(Long id) {
        return getDetail(null,id);
    }

    /**
     * 查询授信详情
     * @param id:
     * @return
     */
    @Override
    public CreditBillDetailResp getDetail(UserLoginCacheDTO user, Long id) {
        CreditBillDO billDO = billRepository.findById(id).orElse(null);
        if (billDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_BILL_NOT_EXIST);
        }
        CreditDO creditDO = creditRepository.findById(billDO.getCreditId()).orElse(null);
        if (creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
        }
        if(user!=null){
            List<Long> memberIds = Stream.of(creditDO.getMemberId(), creditDO.getParentMemberId()).collect(Collectors.toList());
            if(!memberIds.contains(user.getMemberId())){
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
            }
        }
        CreditBillDetailResp result = new CreditBillDetailResp();
        // 计算账单开始时间
        Date startDate = DateUtil.offsetDay(DateUtil.date(billDO.getBillBelongTime()), -1 + billDO.getBillDay());
        // 账单结束时间在开始时间基础上增加一个月
        Date endDate = DateUtil.offsetMonth(startDate, 1);
        String name = (DateUtil.format(startDate, "yyyy/MM/dd") + " ~ " + DateUtil.format(endDate, "yyyy/MM/dd"));
        result.setBillName(name);
        result.setBillQuota(billDO.getBillQuota());
        result.setResidueRepayQuota(billDO.getBillQuota().subtract(billDO.getRepayQuota()).setScale(2, RoundingMode.HALF_UP));    //剩余应还额度=账单额度-偿还额度
        result.setBillDay(billDO.getBillDay());
        result.setRepayPeriod(billDO.getRepayPeriod());
        result.setExpireTime(DateUtil.format(DateUtil.date(billDO.getExpireTime()), DatePattern.NORM_DATE_PATTERN));
        result.setBillExpireTime(billDO.getExpireTime());
        // 如果剩余还款为0，到期日期为0
        if (result.getResidueRepayQuota().compareTo(BigDecimal.ZERO) <= 0) {
            result.setExpireDay(0L);
        } else {
            int overdueDay = CreditUtil.differentDays(new Date(), DateUtil.date(billDO.getExpireTime()));
            result.setExpireDay((long) overdueDay);
        }
        if (billDO.getLastRepayDate() > 0) {
            result.setLastRepayDate(DateUtil.format(DateUtil.date(billDO.getLastRepayDate()), DatePattern.NORM_DATE_PATTERN));
        } else {
            result.setLastRepayDate("-");
        }
        result.setMemberId(creditDO.getParentMemberId());
        result.setMemberRoleId(creditDO.getParentMemberRoleId());

        return result;
    }

    @Override
    public CreditBillDetailResp getDetailByMobile(Long id) {
        CreditBillDetailResp wrapperResp = getDetail(id);
        if (wrapperResp.getBillExpireTime()!=null) {
            String cnDate = DateUtil.formatChineseDate(new Date(wrapperResp.getBillExpireTime()), false, false);
            wrapperResp.setExpireTime(cnDate);
        }
        return wrapperResp;
    }

    /**
     * 分页查询授信逾期
     * <AUTHOR>
     * @since 2020/8/21
     * @param pageVO:
     **/
    @Override
    public PageDataResp<CreditOverduePageQueryResp> pageOverdue(PageQueryCreditOverdueDataReq pageVO) {

        // 组装查询条件
        Specification<CreditBillDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            // 指定授信
            list.add(criteriaBuilder.equal(root.get("creditId").as(Long.class), pageVO.getCreditId()));
            // 账单到期日期小于系统当前时间
            list.add(criteriaBuilder.lessThan(root.get("expireTime").as(Long.class), System.currentTimeMillis()));
            list.add(criteriaBuilder.or(
                    // 还清时间大于到期时间
                    criteriaBuilder.greaterThan(root.get("payOffDate").as(Long.class), root.get("expireTime").as(Long.class)),
                    // 还款额度小于账单额度
                    criteriaBuilder.lessThan(root.get("repayQuota").as(Long.class), root.get("billQuota").as(Long.class))
            ));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // 组装分页参数（按审批时间顺序排序）
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("expireTime").descending());

        // 分页查询数据
        Page<CreditBillDO> result = billRepository.findAll(spec, page);

        List<CreditOverduePageQueryResp> resultList = null;
        if (result.getContent().size() > 0) {

            // 组装账单对应时间范围名称
            List<CreditBillDO> billDOList = billRepository.findByCreditIdOrderByBillBelongTimeDesc(pageVO.getCreditId());
            List<CreditBillSelectResp> billSelectList = new ArrayList<>();
            for (int i = 0; i < billDOList.size(); i++) {
                // 当前账单
                CreditBillDO currentItem = billDOList.get(i);
                // 下个账单
                CreditBillDO nextItem = null;
                if (i < billDOList.size() - 1) {
                    nextItem = billDOList.get(i + 1);
                }

                CreditBillSelectResp vo = new CreditBillSelectResp();
                vo.setId(currentItem.getId());
                Date startDate;
                Date endDate;
                if (nextItem == null) {
                    startDate = DateUtil.offsetDay(DateUtil.date(currentItem.getBillBelongTime()), -1 + currentItem.getBillDay());
                    endDate = DateUtil.offsetMonth(startDate, 1);
                } else {
                    startDate = DateUtil.offsetDay(DateUtil.date(currentItem.getBillBelongTime()), nextItem.getBillDay());
                    Date tmpDate = DateUtil.offsetDay(DateUtil.date(currentItem.getBillBelongTime()), -1 + currentItem.getBillDay());
                    endDate = DateUtil.offsetMonth(tmpDate, 1);
                }
                String name = (DateUtil.format(startDate, "yyyy/MM/dd") + " ~ " + DateUtil.format(endDate, "yyyy/MM/dd"));
                vo.setName(name);
                billSelectList.add(vo);
            }


            // 获取逾期的付款记录
            List<Long> billIdList = result.getContent().stream().map(CreditBillDO::getId).collect(Collectors.toList());
            List<CreditOverdueTradeRecordResp> tradeRecordList = tradeRecordService.getTradeRecordList(billIdList, CreditTradeOperationEnum.REPAY.getCode(), CreditTradeStatusEnum.CONFIRM_RECEIPT.getCode());

            // 组装返回数据
            resultList = result.getContent().stream().map(r -> {
                CreditOverduePageQueryResp queryVO = new CreditOverduePageQueryResp();
                // 逾期天数=还清日期-账单最后还款日期
                int overdueDay = CreditUtil.differentDays(DateUtil.date(r.getExpireTime()), new Date());
                queryVO.setOverdueDay((long) overdueDay);
                billSelectList.stream().filter(s -> s.getId().equals(r.getId())).findFirst().ifPresent(selectVO -> queryVO.setBillName(selectVO.getName()));
                queryVO.setBillQuota(r.getBillQuota());
                queryVO.setLastRepayDate(null != r.getLastRepayDate() && r.getLastRepayDate() > 0 ? DateUtil.format(DateUtil.date(r.getLastRepayDate()), DatePattern.NORM_DATE_PATTERN) : "-");
                queryVO.setLastRepayQuota(r.getLastRepayQuota());
                queryVO.setPayOffDate(null != r.getPayOffDate() && r.getPayOffDate() > 0 ? DateUtil.format(DateUtil.date(r.getPayOffDate()), DatePattern.NORM_DATE_PATTERN) : "-");

                // 赋值逾期交易记录
                if (CollUtil.isNotEmpty(tradeRecordList)) {
                    List<CreditOverdueTradeRecordResp> recordList = tradeRecordList.stream()
                            .filter(recordVO -> recordVO.getBillId().equals(r.getId()))
                            .peek(item -> {
                                // 逾期天数=交易时间-最后还款日期
                                item.setOverdueDay(DateUtil.between(DateUtil.parse(item.getTradeTime()), DateUtil.date(r.getExpireTime()), DateUnit.DAY));
                            }).collect(Collectors.toList());

                    queryVO.setTradeList(recordList);
                }

                return queryVO;
            }).collect(Collectors.toList());

        }

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }

    /**
     * App端分页查询逾期记录
     * <AUTHOR>
     * @since 2021/3/9
     * @param pageVO: 分页请求参数
     * @return 操作结果
     **/
    @Override
    public PageDataResp<CreditOverduePageQueryAppResp> pageOverdueByApp(PageQueryCreditOverdueDataReq pageVO) {

        // 组装查询条件
        Specification<CreditBillDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            // 指定授信
            list.add(criteriaBuilder.equal(root.get("creditId").as(Long.class), pageVO.getCreditId()));
            // 账单到期日期小于系统当前时间
            list.add(criteriaBuilder.lessThan(root.get("expireTime").as(Long.class), System.currentTimeMillis()));
            list.add(criteriaBuilder.or(
                    // 还清时间大于到期时间
                    criteriaBuilder.greaterThan(root.get("payOffDate").as(Long.class), root.get("expireTime").as(Long.class)),
                    // 还款额度小于账单额度
                    criteriaBuilder.lessThan(root.get("repayQuota").as(Long.class), root.get("billQuota").as(Long.class))
            ));
            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // 组装分页参数（按审批时间顺序排序）
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("expireTime").descending());

        // 分页查询数据
        Page<CreditBillDO> result = billRepository.findAll(spec, page);

        List<CreditOverduePageQueryAppResp> resultList = null;
        if (result.getContent().size() > 0) {

            // 获取逾期的付款记录
            List<Long> billIdList = result.getContent().stream().map(CreditBillDO::getId).collect(Collectors.toList());
            List<CreditOverdueTradeRecordAppResp> tradeRecordList = tradeRecordService.getTradeRecordListByApp(billIdList, CreditTradeOperationEnum.REPAY.getCode(), CreditTradeStatusEnum.CONFIRM_RECEIPT.getCode());

            // 组装返回数据
            resultList = result.getContent().stream().map(r -> {
                CreditOverduePageQueryAppResp queryVO = new CreditOverduePageQueryAppResp();

                // 计算账单开始时间
                Date startDate = DateUtil.offsetDay(DateUtil.date(r.getBillBelongTime()), -1 + r.getBillDay());
                // 账单结束时间在开始时间基础上增加一个月
                Date endDate = DateUtil.offsetMonth(startDate, 1);
                // 逾期天数=还清日期-账单最后还款日期
                queryVO.setOverdueDay(DateUtil.between(new Date(), DateUtil.date(r.getExpireTime()), DateUnit.DAY));
                String name = (DateUtil.format(startDate, "yyyy/MM/dd") + " ~ " + DateUtil.format(endDate, "yyyy/MM/dd"));
                queryVO.setBillName(name);
                queryVO.setBillQuota(r.getBillQuota());
                queryVO.setLastRepayDate(null != r.getLastRepayDate() && r.getLastRepayDate() > 0 ? DateUtil.format(DateUtil.date(r.getLastRepayDate()), DatePattern.NORM_DATE_PATTERN) : "-");
                queryVO.setLastRepayQuota(r.getLastRepayQuota());
                queryVO.setPayOffDate(null != r.getPayOffDate() && r.getPayOffDate() > 0 ? DateUtil.format(DateUtil.date(r.getPayOffDate()), DatePattern.NORM_DATE_PATTERN) : "-");

                // 赋值逾期交易记录
                if (CollUtil.isNotEmpty(tradeRecordList)) {
                    List<CreditOverdueTradeRecordAppResp> recordList = tradeRecordList.stream()
                            .filter(recordVO -> recordVO.getBillId().equals(r.getId()))
                            .peek(item -> {
                                // 逾期天数=交易时间-最后还款日期
                                item.setOverdueDay(DateUtil.between(DateUtil.date(item.getTradeTime()), DateUtil.date(r.getExpireTime()), DateUnit.DAY));
                            }).collect(Collectors.toList());

                    queryVO.setTradeList(recordList);
                }

                return queryVO;
            }).collect(Collectors.toList());

        }

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }

    /**
     * 还款通知
     * <AUTHOR>
     * @since 2020/12/21
     * @param time:
     **/
    @Override
    public void repayNotice(String time) {
        // 查询指定时间账单
        List<CreditBillDO> billDOList = billRepository.findAllByExpireTime(DateUtil.parseDateTime(time).getTime());
        if (!CollectionUtils.isEmpty(billDOList)) {
            // 查询授信
            List<Long> creditIdList = billDOList.stream().map(CreditBillDO::getCreditId).collect(Collectors.toList());
            List<CreditDO> creditDOList = creditRepository.findByIsUsableAndIdIn(CommonBooleanEnum.YES.getCode(), creditIdList);
            if (!CollectionUtils.isEmpty(creditDOList)) {
                // 已出逾期账单发送系统消息通知
                List<SystemMessageReq> systemMessageReqList = billDOList.stream().map(r -> {
                    SystemMessageReq request = new SystemMessageReq();
                    CreditDO creditDO = creditDOList.stream().filter(credit -> credit.getId().equals(r.getCreditId())).findFirst().orElse(null);
                    request.setMemberId(creditDO != null ? creditDO.getMemberId() : 0L);
                    request.setRoleId(creditDO != null ? creditDO.getMemberRoleId() : 0L);
                    request.setMessageNotice(MessageNoticeEnum.REPAYMENT.getCode());
                    List<String> params = new ArrayList<>();
                    BigDecimal notYetQuota = r.getBillQuota().subtract(r.getRepayQuota());
                    params.add(notYetQuota.toString());
                    params.add(DateUtil.format(DateUtil.date(r.getExpireTime()), DatePattern.NORM_DATE_PATTERN));
                    request.setParams(params);
                    return request;
                }).collect(Collectors.toList());

                messageService.sendBatchSystemMessage(systemMessageReqList);
            }
        }
    }

    /**
     * 逾期通知
     * <AUTHOR>
     * @since 2020/12/21
     * @param time:
     * @return:
     **/
    @Override
    public void overdueNotice(String time) {
        // 查询逾期账单
        List<CreditBillDO> billDOList = getOverdueBillList(DateUtil.parseDateTime(time).getTime());
        if (!CollectionUtils.isEmpty(billDOList)) {
            // 查询授信
            List<Long> creditIdList = billDOList.stream().map(CreditBillDO::getCreditId).collect(Collectors.toList());
            List<CreditDO> creditDOList = creditRepository.findByIdIn(creditIdList);
            if (!CollectionUtils.isEmpty(creditDOList)) {
                // 已出逾期账单发送系统消息通知
                List<SystemMessageReq> systemMessageReqList = billDOList.stream().map(r -> {
                    SystemMessageReq request = new SystemMessageReq();
                    CreditDO creditDO = creditDOList.stream().filter(credit -> credit.getId().equals(r.getCreditId())).findFirst().orElse(null);
                    request.setMemberId(creditDO != null ? creditDO.getMemberId() : 0L);
                    request.setRoleId(creditDO != null ? creditDO.getMemberRoleId() : 0L);
                    request.setMessageNotice(MessageNoticeEnum.OVERDUE.getCode());
                    List<String> params = new ArrayList<>();
                    BigDecimal notYetQuota = r.getBillQuota().subtract(r.getRepayQuota());
                    params.add(notYetQuota.toString());
                    params.add(DateUtil.format(DateUtil.date(r.getExpireTime()), DatePattern.NORM_DATE_PATTERN));
                    int overdueDay = CreditUtil.differentDays(DateUtil.date(r.getExpireTime()), new Date());
                    params.add(Integer.toString(overdueDay));
                    request.setParams(params);
                    return request;
                }).collect(Collectors.toList());

                messageService.sendBatchSystemMessage(systemMessageReqList);
            }
        }
    }

    /**
     * 账单通知
     * <AUTHOR>
     * @since 2020/12/21
     * @param billTime:
     * @return:
     **/
    @Override
    public void billNotice(String billTime) {
        Date time = DateUtil.parse(billTime, DatePattern.NORM_DATE_PATTERN);
        // 获取账单日期
        Integer billDay = DateUtil.dayOfMonth(time);
        // 账单所属时间（格式：前一月第一天0点0分时间戳）
        Long billBelongTime = DateUtil.beginOfMonth(DateUtil.offsetMonth(time, -1)).getTime();
        // 查询指定时间账单
        List<CreditBillDO> billDOList = billRepository.findByBillBelongTimeAndBillDay(billBelongTime, billDay);
        if (!CollectionUtils.isEmpty(billDOList)) {
            // 查询授信
            List<Long> creditIdList = billDOList.stream().map(CreditBillDO::getCreditId).collect(Collectors.toList());
            List<CreditDO> creditDOList = creditRepository.findByIdIn(creditIdList);
            if (!CollectionUtils.isEmpty(creditDOList)) {
                // 已出逾期账单发送系统消息通知
                List<SystemMessageReq> systemMessageReqList = billDOList.stream().map(r -> {
                    SystemMessageReq request = new SystemMessageReq();
                    CreditDO creditDO = creditDOList.stream().filter(credit -> credit.getId().equals(r.getCreditId())).findFirst().orElse(null);
                    request.setMemberId(creditDO != null ? creditDO.getMemberId() : 0L);
                    request.setRoleId(creditDO != null ? creditDO.getMemberRoleId() : 0L);
                    request.setMessageNotice(MessageNoticeEnum.CHARGE_OFF.getCode());
                    List<String> params = new ArrayList<>();
                    BigDecimal notYetQuota = r.getBillQuota().subtract(r.getRepayQuota());
                    params.add(notYetQuota.toString());
                    params.add(DateUtil.format(DateUtil.date(r.getExpireTime()), DatePattern.NORM_DATE_PATTERN));
                    request.setParams(params);
                    return request;
                }).collect(Collectors.toList());

                messageService.sendBatchSystemMessage(systemMessageReqList);
            }
        }
    }
}
