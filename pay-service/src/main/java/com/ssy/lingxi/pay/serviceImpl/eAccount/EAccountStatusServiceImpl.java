package com.ssy.lingxi.pay.serviceImpl.eAccount;

import com.ssy.lingxi.pay.entity.do_.eAccount.EAccountStatusRecordDO;
import com.ssy.lingxi.pay.repository.eAccount.EAccountStatusRecordRepository;
import com.ssy.lingxi.pay.service.eAccount.IEAccountStatusService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * e账户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/2
 */
@Service
public class EAccountStatusServiceImpl implements IEAccountStatusService {

    @Resource
    private EAccountStatusRecordRepository eAccountStatusRecordRepository;

    /**
     * 查询e账户状态记录
     * @param allInPayId e账号id
     */
    @Override
    public List<EAccountStatusRecordDO> getEAccountStatusRecord(Long allInPayId){
        return eAccountStatusRecordRepository.findByAllInPayIdOrderByCreateTimeAsc(allInPayId);
    }

    /**
     * 查询e账户历史流转记录
     * @param id 主键id
     * @return
     */
    @Override
    public EAccountStatusRecordDO getEAccountById(Long id) {
        return eAccountStatusRecordRepository.findById(id).orElse(null);
    }

    /**
     * 保存记录
     * @param eAccountStatusRecordDO  参数
     */
    @Override
    public void saveEAccountStatusRecord(EAccountStatusRecordDO eAccountStatusRecordDO) {
        eAccountStatusRecordRepository.saveAndFlush(eAccountStatusRecordDO);
    }
}

