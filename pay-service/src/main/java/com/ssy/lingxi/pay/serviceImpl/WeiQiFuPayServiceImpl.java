package com.ssy.lingxi.pay.serviceImpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.zxing.qrcode.encoder.QRCode;
import com.ssy.lingxi.common.constant.mq.PayMqConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayCallbackFeignReq;
import com.ssy.lingxi.pay.api.enums.ServiceTypeEnum;
import com.ssy.lingxi.pay.api.model.req.weiQiFuPay.WeiQiFuPayQueryReq;
import com.ssy.lingxi.pay.api.model.req.weiQiFuPay.WeiQiFuPayRefundReq;
import com.ssy.lingxi.pay.api.model.req.weiQiFuPay.WeiQiFuPayReq;
import com.ssy.lingxi.pay.api.model.resp.weiQiFuPay.WeiQiFuPayDetailsResp;
import com.ssy.lingxi.pay.api.model.resp.weiQiFuPay.WeiQiFuPayRefundResp;
import com.ssy.lingxi.pay.api.model.resp.weiQiFuPay.WeiQiFuPayResp;
import com.ssy.lingxi.pay.config.PayGateWayConfig;
import com.ssy.lingxi.pay.config.WeiQiFuPayConfig;
import com.ssy.lingxi.pay.enums.DelayQueueGetPayResultEnum;
import com.ssy.lingxi.pay.model.req.AsynGetPayResultReq;
import com.ssy.lingxi.pay.service.WeiQiFuPayService;
import com.ssy.lingxi.pay.service.assetAccount.IMemberAssetAccountService;
import com.tenpay.business.entpay.mse.sdk.api.Payment;
import com.tenpay.business.entpay.mse.sdk.api.Refund;
import com.tenpay.business.entpay.mse.sdk.exception.EntpayException;
import com.tenpay.business.entpay.mse.sdk.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * 微企付支付接口
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/16
 */
@Slf4j
@Service
public class WeiQiFuPayServiceImpl implements WeiQiFuPayService {

    @Resource
    private PayGateWayConfig payGateWayConfig;

    @Resource
    private WeiQiFuPayConfig weiQiFuPayConfig;

    @Resource
    private IOrderProcessFeign orderFeignService;

    @Resource
    private IMemberAssetAccountService memberAssetAccountService;

    @Resource
    private IMqUtils mqUtils;


    /**
     * 微企付小程序支付
     *
     * @param weiQiFuPayReq 支付请求参数
     * @param request       http请求
     * @return 支付结果
     */
    @Override
    public WeiQiFuPayResp appletPay(WeiQiFuPayReq weiQiFuPayReq, HttpServletRequest request) {
        // 收款方信息
        PaymentPayee payee = PaymentPayee.builder()
                .entId(weiQiFuPayConfig.getMerchantId()) // 收款商户Id
                .entName(weiQiFuPayConfig.getMerchantName()) // 收款商户名称
                .build();

        // 商品信息
        List<Goods> goodsList = BeanUtil.copyToList(weiQiFuPayReq.getGoodsReqList(), Goods.class);
        //拼接自定义参数
        String selfDefinedParam = "payChannel="+ OrderPayChannelEnum.WEI_QI_FU.getCode()+"&serviceType="+ StrUtil.emptyToDefault(weiQiFuPayReq.getServiceType(), "") + "&recordId="+ StrUtil.emptyToDefault(weiQiFuPayReq.getOutPlatformId(), "") +"&orderId=" + (weiQiFuPayReq.getOrderId() == null ? "" : weiQiFuPayReq.getOrderId().toString());
        FrontCallbackUrl frontCallbackUrl = FrontCallbackUrl.builder()
                .mpPath("packages/order/pages/submitSuccess/index?" + selfDefinedParam)  // Mp 支付完成前端回跳小程序路径
                .mpAppid("wx611487e592840e36") // Mp 支付完成前端回跳小程序appid
//                .mpUsername("mpUsername") // Mp 支付完成前端回跳小程序原始id
//                .mpUrlscheme("mpUrlscheme") // Mp 支付完成前端回跳小程序urlscheme
                .build();
        // 回调url
        PaymentNotifyUrl paymentNotifyUrl = PaymentNotifyUrl.builder()
                .serverNotifyUrl(payGateWayConfig.getNotifyUrl() + "/pay/weiqifu/notify/"+ weiQiFuPayReq.getServiceType()) // 后端支付结果通知url
                .frontCallbackUrl(frontCallbackUrl)
                .build();

        // 过期时间
        Date now = new Date();
        Date expireDate = new Date(now.getTime() + 600000); //10分钟后的时间
        PaymentMpParam paymentParam = PaymentMpParam.builder()
                .outPaymentId(weiQiFuPayReq.getOutPlatformId()) // 平台支付单号
                .amount(weiQiFuPayReq.getPayMoney()) // 支付金额
                .currency(CurrencyConstant.CNY) // 币种
                .expireTime(expireDate)
                .payee(payee)
                .attachment(weiQiFuPayReq.getAttach())
                .goods(goodsList)
                .notifyUrl(paymentNotifyUrl)
                .build();
        // 调用支付接口
        try {
            Payment payment = Payment.createMpPay(paymentParam);
            log.info("微企付小程序支付, 支付返回信息: {}", payment.toString());
            WeiQiFuPayResp weiQiFuPayResp = new WeiQiFuPayResp();
            weiQiFuPayResp.setMpAppid(payment.getRedirect().getMiniProgram().getMpAppid());
            weiQiFuPayResp.setMpPath(payment.getRedirect().getMiniProgram().getMpPath());
            weiQiFuPayResp.setMpUsername(payment.getRedirect().getMiniProgram().getMpUsername());
            weiQiFuPayResp.setMpVersion(payment.getRedirect().getMiniProgram().getMpVersion());
            weiQiFuPayResp.setPayStatus(payment.getPayStatus());
            if(payment.getRedirect().getWxH5() != null){
                weiQiFuPayResp.setWxH5(payment.getRedirect().getWxH5().getWxH5());
            }
            weiQiFuPayResp.setGetPayResultParam(selfDefinedParam);
            return weiQiFuPayResp;
        } catch (EntpayException e) {
            log.error("微企付小程序支付失败, 支付返回信息: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 微企付二维码支付
     *
     * @param weiQiFuPayReq 支付请求参数
     * @param request       http请求
     * @return 下单参数
     */
    @Override
    public WeiQiFuPayResp qrCodePay(WeiQiFuPayReq weiQiFuPayReq, HttpServletRequest request) {
        // 收款方信息
        PaymentPayee payee = PaymentPayee.builder()
                .entId(weiQiFuPayConfig.getMerchantId()) // 收款商户Id
                .entName(weiQiFuPayConfig.getMerchantName()) // 收款商户名称
                .build();
        // 商品信息
        List<Goods> goodsList = BeanUtil.copyToList(weiQiFuPayReq.getGoodsReqList(), Goods.class);

        // 回调url
        PaymentNotifyUrl paymentNotifyUrl = PaymentNotifyUrl.builder()
                .serverNotifyUrl(payGateWayConfig.getNotifyUrl() + "/pay/weiqifu/notify/" + weiQiFuPayReq.getServiceType()) // 后端支付结果通知url
                .build();

        // 过期时间
        Date now = new Date();
        Date expireDate = new Date(now.getTime() + 600000); //10分钟后的时间
        PaymentQRParam paymentParam = PaymentQRParam.builder()
                .outPaymentId(weiQiFuPayReq.getOutPlatformId()) // 平台支付单号
                .amount(weiQiFuPayReq.getPayMoney()) // 支付金额
                .currency(CurrencyConstant.CNY) // 币种
                .expireTime(expireDate)
                .payee(payee)
                .attachment(weiQiFuPayReq.getAttach())
                .goods(goodsList)
                .notifyUrl(paymentNotifyUrl)
                .profitAllocationFlag(ProfitAllocationFlagConstant.NO_PROFIT_ALLOCATION)
                .build();
        // 调用支付接口
        try {
            Payment payment = Payment.createQrCodePay(paymentParam);
            log.info("微企付二维码支付, 支付返回信息: {}", payment.toString());
            WeiQiFuPayResp weiQiFuPayResp = new WeiQiFuPayResp();
            weiQiFuPayResp.setStaticQrcode(payment.getRedirect().getStaticQrcode().getStaticQrcode());
            weiQiFuPayResp.setPayStatus(payment.getPayStatus());
            return weiQiFuPayResp;
        } catch (EntpayException e) {
            log.error("微企付二维码支付失败, 支付返回信息: {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询订单
     *
     * @param req 查询请求参数
     * @return 订单查询结果
     */
    @Override
    public WeiQiFuPayDetailsResp orderQuery(WeiQiFuPayQueryReq req) {
        try {
            Payment payment = Payment.retrieveByOutPaymentId(req.getOutPlatformId());
            log.info("微企付支付查询结果:{}", payment);
            WeiQiFuPayDetailsResp weiQiFuPayDetailsResp = new WeiQiFuPayDetailsResp();
            weiQiFuPayDetailsResp.setIsPay("BANK_ACCEPTED".equals(payment.getPayStatus()));
            return weiQiFuPayDetailsResp;
        } catch (EntpayException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 退款申请
     *
     * @param req 退款请求参数
     * @return 退款结果
     */
    @Override
    public WeiQiFuPayRefundResp refund(WeiQiFuPayRefundReq req) {
        try {
            RefundParam build = RefundParam.builder()
                    .entId(weiQiFuPayConfig.getMerchantId()) // 退款企业id
                    .outRefundId(req.getOutRefundId()) // 平台退款单号
                    .outPaymentId(req.getOutPlatformId())
                    .totalAmount(req.getTotalAmount()) // 原支付订单支付金额
                    .refundAmount(req.getRefundAmount()) // 退款金额
                    .refundReason(req.getRefundReason()) // 退款原因
                    .serverNotifyUrl(payGateWayConfig.getNotifyUrl() + "/pay/weiqifu/notify/4_4") // 回调地址
                    .build();

            // 调用退款申请接口
            Refund refund = Refund.create(build);
            WeiQiFuPayRefundResp weiQiFuPayRefundResp = new WeiQiFuPayRefundResp();
            if("ACCEPTED".equals(refund.getStatus())){
                weiQiFuPayRefundResp.setSuccess(true);
                weiQiFuPayRefundResp.setRefundId(refund.getRefundId());
                weiQiFuPayRefundResp.setOutRefundId(refund.getOutRefundId());
                weiQiFuPayRefundResp.setStatus(refund.getStatus());
            }else {
                weiQiFuPayRefundResp.setSuccess(false);
                weiQiFuPayRefundResp.setRefundId(refund.getRefundId());
                weiQiFuPayRefundResp.setOutRefundId(refund.getOutRefundId());
                weiQiFuPayRefundResp.setStatus(refund.getStatus());
                weiQiFuPayRefundResp.setFailedMsg(refund.getFailedMsg());
            }

            return weiQiFuPayRefundResp;
        } catch (EntpayException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 支付查询结果
     * @return
     */
    public Boolean getPayResult(AsynGetPayResultReq getPayResultReq) {
        // 支付查询(外单号)
        Payment payment = null;
        try {
            payment = Payment.retrieveByOutPaymentId(getPayResultReq.getRecordId());
            log.info("微企付支付查询结果:{}", payment);
        } catch (EntpayException e) {
            log.error("查询支付结果失败: {}", e.getMessage());
            return false;
        }
        if("SUCCEEDED".equals(payment.getPayStatus())){
            String serviceType = getPayResultReq.getServiceType();
            // 业务处理
            if (ServiceTypeEnum.Order_Pay.getCode().equals(serviceType)) {
                OrderPayCallbackFeignReq orderPayCallbackFeignReq = new OrderPayCallbackFeignReq();
                orderPayCallbackFeignReq.setTradeNo(payment.getOutPaymentId());
                orderPayCallbackFeignReq.setChannelTradeNo(payment.getPaymentId());
                orderPayCallbackFeignReq.setAttach(payment.getAttachment());
                orderPayCallbackFeignReq.setPaySuccess(true);
                WrapperResp<Void> orderCallbackResult = orderFeignService.orderPayCallback(orderPayCallbackFeignReq);
                log.info(ServiceTypeEnum.Order_Pay.getMessage() + ":==========attach：" + payment.getAttachment() + " , 支付成功查询结果" + orderCallbackResult.getMessage());
            }else if(ServiceTypeEnum.Pay_Recharge.getCode().equals(serviceType)){
                memberAssetAccountService.payNotify(payment.getOutPaymentId(), payment.getPaymentId());
            }
        }else if("PROCESSING".equals(payment.getPayStatus()) || "BANK_ACCEPTE".equals(payment.getPayStatus())){
            //如果是支付中和交易中，则在延迟查询
            getPayResultReq.setCurrentNotifyNum(getPayResultReq.getCurrentNotifyNum() + 1);
            Long time = DelayQueueGetPayResultEnum.getTimeByCode(getPayResultReq.getCurrentNotifyNum());
            if(time != null){
                mqUtils.sendDelayMsg(PayMqConstant.PAY_RESULT_QUERY_DELAY_EXCHANGE, PayMqConstant.PAY_RESULT_QUERY_DELAY_ROUTING_KEY, JSON.toJSONString(getPayResultReq), time);
            }
            return true;
        }
        return true;
    }


}
