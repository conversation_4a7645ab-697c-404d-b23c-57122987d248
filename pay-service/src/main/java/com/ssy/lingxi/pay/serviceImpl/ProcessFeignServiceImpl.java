package com.ssy.lingxi.pay.serviceImpl;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.pay.service.IProcessFeignService;
import com.ssy.lingxi.pay.util.FeignLogUtil;
import com.ssy.lingxi.workflow.api.feign.IProcessFeign;
import com.ssy.lingxi.workflow.api.model.req.*;
import com.ssy.lingxi.workflow.api.model.resp.ComplexTaskCompleteResp;
import com.ssy.lingxi.workflow.api.model.resp.ComplexTaskDefResp;
import com.ssy.lingxi.workflow.api.model.resp.SimpleProcessDefResp;
import com.ssy.lingxi.workflow.api.model.resp.SimpleTaskCompleteResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 *  新工作流feign
 * <AUTHOR>
 * @since 2021/5/20
 * @version 2.0.0
 */
@Service
public class ProcessFeignServiceImpl implements IProcessFeignService {


    private static final Logger logger = LoggerFactory.getLogger(ProcessFeignServiceImpl.class);

    /**
     * 工作流接口
     */
    @Resource
    private IProcessFeign processFeign;

    @Override
    public ComplexTaskCompleteResp startProcess(TaskStartReq startVO) {
        WrapperResp<ComplexTaskCompleteResp> complexTaskCompleteVOWrapperResp = processFeign.startProcess(startVO);
        FeignLogUtil.printLog("startProcess", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }

    @Override
    public ComplexTaskCompleteResp startProcessThenCompleteFirstTask(TaskStartReq startVO) {
        WrapperResp<ComplexTaskCompleteResp> complexTaskCompleteVOWrapperResp = processFeign.startProcessThenCompleteFirstTask(startVO);
        FeignLogUtil.printLog("startProcessThenCompleteFirstTask", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }

    @Override
    public ComplexTaskCompleteResp completeTask(TaskExecuteReq executeVO) {
        WrapperResp<ComplexTaskCompleteResp> complexTaskCompleteVOWrapperResp = processFeign.completeTask(executeVO);
        FeignLogUtil.printLog("completeTask", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }

    @Override
    public SimpleTaskCompleteResp startSimpleProcess(TaskStartReq startVO) {
        WrapperResp<SimpleTaskCompleteResp> complexTaskCompleteVOWrapperResp = processFeign.startSimpleProcess(startVO);
        FeignLogUtil.printLog("startSimpleProcess", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }

    @Override
    public SimpleTaskCompleteResp startSimpleProcessThenCompleteFirstTask(TaskStartReq startVO) {
        WrapperResp<SimpleTaskCompleteResp> complexTaskCompleteVOWrapperResp = processFeign.startSimpleProcessThenCompleteFirstTask(startVO);
        FeignLogUtil.printLog("startSimpleProcessThenCompleteFirstTask", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }

    @Override
    public SimpleTaskCompleteResp completeSimpleTask(TaskExecuteReq executeVO) {
        WrapperResp<SimpleTaskCompleteResp> complexTaskCompleteVOWrapperResp = processFeign.completeSimpleTask(executeVO);
        FeignLogUtil.printLog("completeSimpleTask", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }

    @Override
    public List<Long> findProcessDataIds(ProcessDataQueryReq queryVO) {
        WrapperResp<List<Long>> complexTaskCompleteVOWrapperResp = processFeign.findProcessDataIds(queryVO);
        FeignLogUtil.printLog("findProcessDataIds", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }

    @Override
    public ComplexTaskDefResp findComplexTaskDefinitions(ComplexProcessQueryReq queryVO) {
        WrapperResp<ComplexTaskDefResp> complexTaskCompleteVOWrapperResp = processFeign.findComplexTaskDefinitions(queryVO);
        FeignLogUtil.printLog("findComplexTaskDefinitions", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }

    @Override
    public SimpleProcessDefResp findSimpleExternalTaskDefinitions(ExternalProcessQueryReq queryVO) {
        WrapperResp<SimpleProcessDefResp> complexTaskCompleteVOWrapperResp = processFeign.findSimpleExternalTaskDefinitions(queryVO);
        FeignLogUtil.printLog("findSimpleExternalTaskDefinitions", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }

    @Override
    public SimpleProcessDefResp findSimpleInternalTaskDefinitions(InternalProcessQueryReq queryVO) {
        WrapperResp<SimpleProcessDefResp> complexTaskCompleteVOWrapperResp = processFeign.findSimpleInternalTaskDefinitions(queryVO);
        FeignLogUtil.printLog("findSimpleInternalTaskDefinitions", complexTaskCompleteVOWrapperResp);
        return complexTaskCompleteVOWrapperResp.getData();
    }
}
