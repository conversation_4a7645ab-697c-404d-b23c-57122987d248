package com.ssy.lingxi.pay.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.pay.model.req.AsynGetPayResultReq;
import com.ssy.lingxi.pay.service.PayService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 支付相关Controller
 *
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/8/11
 */
@RestController
@RequestMapping(ServiceModuleConstant.PAY_PATH_PREFIX + "/mobile/")
public class MobilePayController {


    @Resource
    private PayService payService;

    /**
     * 异步查询线上支付渠道的支付结果
     *
     */
    @GetMapping("asynGetPayResult")
    public WrapperResp<Boolean> asynGetPayResult(AsynGetPayResultReq payResultQueryReq) {
        return WrapperUtil.success(payService.asynGetPayResult(payResultQueryReq));
    }

}
