package com.ssy.lingxi.pay.serviceImpl;

import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayParameterFeignReq;
import com.ssy.lingxi.order.api.model.resp.PaymentParameterFeignDetailResp;
import com.ssy.lingxi.pay.api.model.req.MobilePayReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.BalancePayReq;
import com.ssy.lingxi.pay.enums.PayTypeEnum;
import com.ssy.lingxi.pay.model.resp.WeChatPayResultResp;
import com.ssy.lingxi.pay.service.IFeignService;
import com.ssy.lingxi.pay.service.IWeChatPayService;
import com.ssy.lingxi.pay.service.assetAccount.IMemberAssetAccountService;
import com.ssy.lingxi.pay.util.FeignLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 异步服务
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/11/18
 */
@Slf4j
@Service
public class FeignServiceImpl implements IFeignService {
    @Resource
    private IWeChatPayService weChatPayService;

    @Resource
    private IMemberAssetAccountService memberAssetAccountService;

    @Resource
    private IMqUtils mqUtils;

    @Resource
    private IOrderProcessFeign orderFeignService;

    /**
     * 授信还款
     *
     * @param mobilePayReq
     * @param payType
     * @param request
     * @return
     */
    @Override
    public String creditWeChatRepay(MobilePayReq mobilePayReq, Integer payType, HttpServletRequest request) {
        try {
            mobilePayReq.setPayType(PayTypeEnum.MEMBER.getCode());
            WeChatPayResultResp weChatPayResultResp = weChatPayService.nativePay(mobilePayReq, request);
            if (weChatPayResultResp.isSuccess()) {
                //返回支付路径
                Map<String, String> body = (Map<String, String>) weChatPayResultResp.getBody();
                return body.get("code_url");
            }
            throw new BusinessException(ResponseCodeEnum.WECHAT_PAY_ERROR);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception ex) {
            throw new BusinessException(ResponseCodeEnum.WECHAT_PAY_ERROR);
        }
    }

    /**
     * 授信余额还款
     *
     * @param payRequest: 支付请求参数
     * <AUTHOR>
     * @since 2020/12/31
     * @return:
     **/
    @Override
    public String creditBalanceRepay(BalancePayReq payRequest) {
        return memberAssetAccountService.balancePay(payRequest);
    }

    @Override
    public PaymentParameterFeignDetailResp findPaymentParameters(OrderPayParameterFeignReq payRequest) {
        WrapperResp<PaymentParameterFeignDetailResp> wrapperResp = orderFeignService.findPaymentParameters(payRequest);
        FeignLogUtil.printLog("查询会员支付参数[授信]配置", wrapperResp, payRequest);
        if(wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
            throw new BusinessException(wrapperResp.getCode(),wrapperResp.getMessage());
        }
        return wrapperResp.getData();
    }

    private PaymentParameterFeignDetailResp findPaymentParameters(Long memberId, Long roleId) {
        OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
        feignVO.setMemberId(memberId);
        feignVO.setRoleId(roleId);
        feignVO.setPayChannel(OrderPayChannelEnum.CREDIT);

        return findPaymentParameters(feignVO);
    }

    @Override
    public Boolean isCreditPayment(Long memberId, Long memberRoleId) {
        PaymentParameterFeignDetailResp creditPaymentInfo = getCreditPaymentInfo(memberId, memberRoleId);
        return creditPaymentInfo != null;
    }

    private PaymentParameterFeignDetailResp getCreditPaymentInfo(Long memberId, Long memberRoleId) {
        return findPaymentParameters(memberId, memberRoleId);
    }

    @Override
    public PaymentParameterFeignDetailResp getCreditPayment(Long memberId, Long memberRoleId) {
        return getCreditPaymentInfo(memberId, memberRoleId);
    }
}
