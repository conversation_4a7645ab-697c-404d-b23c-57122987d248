package com.ssy.lingxi.pay.controller.mobile.eAccount;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.MapResp;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.IpUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.pay.api.common.allInPay.AllInPayMemberTypeEnum;
import com.ssy.lingxi.pay.api.enums.EAccountAppletRechargeTypeEnum;
import com.ssy.lingxi.pay.api.enums.EAccountJsApiRechargeTypeEnum;
import com.ssy.lingxi.pay.api.enums.EAccountMobileRechargeTypeEnum;
import com.ssy.lingxi.pay.api.enums.EAccountPCRechargeTypeEnum;
import com.ssy.lingxi.pay.api.model.dto.QueryInExpDetailDTO;
import com.ssy.lingxi.pay.api.model.req.allInPay.BalanceReq;
import com.ssy.lingxi.pay.api.model.req.allInPay.DepositApplyReq;
import com.ssy.lingxi.pay.api.model.req.allInPay.PayReq;
import com.ssy.lingxi.pay.api.model.req.allInPay.WithdrawApplyReq;
import com.ssy.lingxi.pay.api.model.req.eAccount.EAccountCashOutReq;
import com.ssy.lingxi.pay.api.model.req.eAccount.EAccountConfirmPayReq;
import com.ssy.lingxi.pay.api.model.req.eAccount.EAccountMobileRechargeReq;
import com.ssy.lingxi.pay.api.model.resp.allInPay.*;
import com.ssy.lingxi.pay.api.model.resp.eAccount.EAccountDetailResp;
import com.ssy.lingxi.pay.api.model.resp.eAccount.EAccountMobileRechargeResp;
import com.ssy.lingxi.pay.domain.AllInPayDM;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayAttachDO;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayDO;
import com.ssy.lingxi.pay.entity.do_.eAccount.EAccountStatusRecordDO;
import com.ssy.lingxi.pay.entity.do_.eAccount.EAccountTradeRecordDO;
import com.ssy.lingxi.pay.enums.TradeOperationEnum;
import com.ssy.lingxi.pay.enums.TradeStatusEnum;
import com.ssy.lingxi.pay.enums.allInPay.AllInPayEnterpriseStateEnum;
import com.ssy.lingxi.pay.enums.allInPay.AllInPayPersonalStateEnum;
import com.ssy.lingxi.pay.model.req.GateWayPayReq;
import com.ssy.lingxi.pay.model.req.ResendPaySMSReq;
import com.ssy.lingxi.pay.model.resp.ResendPaySMSRespResp;
import com.ssy.lingxi.pay.service.allInPay.IAllInPayService;
import com.ssy.lingxi.pay.service.eAccount.IEAccountService;
import com.ssy.lingxi.pay.service.eAccount.IEAccountStatusService;
import com.ssy.lingxi.pay.service.eAccount.IEAccountTradeService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * APP-e账户管理-通联
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/2
 */
@RestController
@RequestMapping(ServiceModuleConstant.PAY_PATH_PREFIX + "/mobile/eAccount/allInPay")
public class MobileEAccountController extends BaseController {

    @Resource
    private IEAccountService eAccountService;

    @Resource
    private IEAccountStatusService eAccountStatusService;

    @Resource
    private IAllInPayService payService;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private IEAccountTradeService eAccountTradeService;

    @Resource
    private IMemberFeign memberFeign;

    /**
     * 账户详情
     */
    @GetMapping("/getAccountDetail")
    public WrapperResp<EAccountDetailResp> getAccountDetail() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        AllInPayDO allInPayDO = eAccountService.getAccountDetail(sysUser);
        if (allInPayDO != null) {
            //查询账户余额
            BalanceReq balanceReq = payService.queryBalance(sysUser.getMemberId(), sysUser.getMemberRoleId());
            //账户信息
            EAccountDetailResp eAccountResponse = BeanUtil.copyProperties(allInPayDO, EAccountDetailResp.class);
            eAccountResponse.setAccountBalance(balanceReq.getAllAmount());
            eAccountResponse.setLockBalance(balanceReq.getFreeZenAmount());
            eAccountResponse.setUsableBalance(balanceReq.getAvailableAmount());

            //账户状态
            Integer allInMemberType = allInPayDO.getAllInMemberType();
            Integer accountStatus = allInPayDO.getAccountStatus();
            eAccountResponse.setAllInMemberType(allInMemberType);
            if (allInMemberType != null && allInMemberType.equals(3)) {
                eAccountResponse.setAccountStatusName(AllInPayPersonalStateEnum.getName(accountStatus));
            }
            if (allInMemberType != null && allInMemberType.equals(2)) {
                eAccountResponse.setAccountStatusName(AllInPayEnterpriseStateEnum.getName(accountStatus));
            }

            //账户附属信息
            AllInPayAttachDO allInPayAttachDO = eAccountService.getAccountAttachDetail(allInPayDO.getBizUserId());
            if (allInPayAttachDO != null) {
                eAccountResponse.setPhone(allInPayAttachDO.getPhone());
                eAccountResponse.setBankBranchName(allInPayAttachDO.getBankBranchName());
                eAccountResponse.setAccountNo(allInPayAttachDO.getAccountNo());
                eAccountResponse.setBankName(allInPayAttachDO.getBankName());
                eAccountResponse.setBankCardNo(allInPayAttachDO.getBankCardNo());

                if (Objects.equals(eAccountResponse.getAllInMemberType(), AllInPayMemberTypeEnum.PERSONAL.getCode())) {
                    eAccountResponse.setAccountBelong(allInPayAttachDO.getName());
                } else {
                    eAccountResponse.setAccountBelong(allInPayAttachDO.getCompanyName());
                }

            }
            return WrapperUtil.success(eAccountResponse);
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_NOT_EXIST);
        }
    }

    /**
     * 交易记录
     *
     * @param startTime 开始时间,格式:yyyy-MM-dd HH:mm:ss
     * @param endTime   结束时间,格式:yyyy-MM-dd HH:mm:ss
     */
    @GetMapping("/getEAccountTradeRecord")
    public WrapperResp<PageDataResp<InExpDetailResp>> getEAccountTradeRecord(PageDataReq pageDataReq, String startTime, String endTime) {

        if(StringUtils.isBlank(startTime) || StringUtils.isBlank(endTime)){
            throw new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_TRADE_RECORD_TIME_ERROR);
        }

        UserLoginCacheDTO sysUser = this.getSysUser();
        AllInPayDO allInPayDO = eAccountService.getAccountDetail(sysUser);
        if (allInPayDO != null) {
            QueryInExpDetailDTO dto = new QueryInExpDetailDTO();
            dto.setDateStart(startTime);
            dto.setDateEnd(endTime);
            dto.setQueryNum((long) pageDataReq.getPageSize());
            dto.setStartPosition((long) pageDataReq.getCurrentOffset() + 1);
            QueryInExpDetailResp queryInExpDetailResp = payService.queryInExpDetail(allInPayDO.getMemberId(), allInPayDO.getMemberRoleId(), dto);
            if (queryInExpDetailResp != null) {
                return WrapperUtil.success(new PageDataResp<>(queryInExpDetailResp.getTotalNum(), queryInExpDetailResp.getInExpDetails()));
            }
            return WrapperUtil.success(new PageDataResp<>(0L, new ArrayList<>()));
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_NOT_EXIST);
        }
    }

    /**
     * 状态记录
     *
     * @param id e账户id
     */
    @GetMapping("/getEAccountStatusRecord")
    public WrapperResp<List<EAccountStatusRecordDO>> getEAccountStatusRecord(@RequestParam("id") Long id) {
        return WrapperUtil.success(eAccountStatusService.getEAccountStatusRecord(id));
    }

    /**
     * 查询充值方式-app
     */
    @GetMapping("/getRechargeType")
    public WrapperResp<List<MapResp>> getRechargeType() {
        return WrapperUtil.success(EAccountMobileRechargeTypeEnum.getCodes());
    }

    /**
     * 查询充值方式-小程序
     */
    @GetMapping("/getAppletRechargeType")
    public WrapperResp<List<MapResp>> getAppletRechargeType() {
        return WrapperUtil.success(EAccountAppletRechargeTypeEnum.getCodes());
    }

    /**
     * 查询充值方式-h5
     */
    @GetMapping("/getJsApiRechargeType")
    public WrapperResp<List<MapResp>> getJsApiRechargeType() {
        return WrapperUtil.success(EAccountJsApiRechargeTypeEnum.getCodes());
    }

    /**
     * 账户充值
     *
     * @param eAccountMobileRechargeReq 参数
     * @return 操作结果
     */
    @PostMapping("/recharge")
    public WrapperResp<EAccountMobileRechargeResp> recharge(@RequestBody @Valid EAccountMobileRechargeReq eAccountMobileRechargeReq, HttpServletRequest request) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        AllInPayDO allInPayDO = eAccountService.getAccountDetail(sysUser);
        if (allInPayDO != null) {
            String type = eAccountMobileRechargeReq.getType();
            BigDecimal money = eAccountMobileRechargeReq.getMoney();
            String message = EAccountMobileRechargeTypeEnum.getMessage(type);
            if (StringUtils.isNotEmpty(message)) {
                //交易订单号
                String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);

                DepositApplyReq depositApplyReq = new DepositApplyReq();
                depositApplyReq.setAmount(money);
                depositApplyReq.setBizOrderNo(tradeCode);
                depositApplyReq.setFee(new BigDecimal(0));
                depositApplyReq.setPayMethodStr(type);
                if (EAccountPCRechargeTypeEnum.QUICKPAY_VSP.getCode().equals(type)) {
                    depositApplyReq.setValidateType(1);
                } else {
                    depositApplyReq.setValidateType(0);
                }

                //微信小程序需要获取openId
                if (EAccountMobileRechargeTypeEnum.needWechatOpenIdTypeList().contains(type)) {
                    String jsCode = eAccountMobileRechargeReq.getJsCode();
                    if (StringUtils.isEmpty(jsCode)) {
                        throw new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_CODE_NOT_EXIST);
                    }
                    Map<String, String> param = eAccountService.getAllInWechatParam(jsCode);
                    depositApplyReq.setSubAppId(param.get("subAppId"));
                    depositApplyReq.setOpenId(param.get("openId"));
                    depositApplyReq.setVspCusId(param.get("merchantId"));
                }

                //微信小程序需要获取openId
                if(EAccountMobileRechargeTypeEnum.WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG.getCode().equals(type) || EAccountMobileRechargeTypeEnum.SCAN_ALIPAY_ORG.getCode().equals(type)){
                    Map<String, String> param = eAccountService.getMerchantId();
                    depositApplyReq.setVspCusId(param.get("merchantId"));
                }

                //微信h5支付需要用到 subAppId
                if (EAccountJsApiRechargeTypeEnum.WECHATPAY_H5_OPEN.getCode().equals(type)) {
                    String subAppId = eAccountService.getWechatSubAppId();
                    String remoteAddrIp = IpUtil.getIpAddr(request);
                    depositApplyReq.setSubAppId(subAppId);
                    depositApplyReq.setCusIp(remoteAddrIp);
                }

                DepositApplyResp depositApplyResp = payService.depositApply(getSysUser(), depositApplyReq);
                if (depositApplyResp != null) {
                    EAccountTradeRecordDO eAccountTradeRecordDO = new EAccountTradeRecordDO();
                    eAccountTradeRecordDO.setTradeCode(tradeCode);
                    eAccountTradeRecordDO.setTradeMoney(money);
                    eAccountTradeRecordDO.setOperation(TradeOperationEnum.ACCOUNT_RECHARGE.getCode());
                    eAccountTradeRecordDO.setRemark(TradeOperationEnum.ACCOUNT_RECHARGE.getMessage());
                    eAccountTradeRecordDO.setTradeTime(System.currentTimeMillis());
                    eAccountTradeRecordDO.setStatus(TradeStatusEnum.PAYING.getCode());
                    eAccountTradeRecordDO.setMemberId(sysUser.getMemberId());
                    eAccountTradeRecordDO.setMemberName(sysUser.getMemberName());
                    eAccountTradeRecordDO.setMemberRoleId(sysUser.getMemberRoleId());
                    eAccountTradeRecordDO.setMemberRoleName(sysUser.getMemberRoleName());
                    eAccountTradeService.saveEAccountTradeRecord(eAccountTradeRecordDO);

                    EAccountMobileRechargeResp eAccountMobileRechargeResp = new EAccountMobileRechargeResp();
                    if (EAccountPCRechargeTypeEnum.GATEWAY_VSP.getCode().equals(type)) {
                        String remoteAddrIp = IpUtil.getIpAddr(request);
                        GateWayPayReq gateWayPayReq = new GateWayPayReq();
                        gateWayPayReq.setBizOrderNo(tradeCode);
                        gateWayPayReq.setConsumerIp(remoteAddrIp);
                        eAccountMobileRechargeResp.setParam(payService.gateWayPay(sysUser.getMemberId(), sysUser.getMemberRoleId(), gateWayPayReq));
                    } else {
                        eAccountMobileRechargeResp.setParam(depositApplyResp.getPayInfo());
                    }
                    eAccountMobileRechargeResp.setTradeCode(tradeCode);
                    return WrapperUtil.success(eAccountMobileRechargeResp);
                } else {
                    throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR);
                }
            } else {
                throw new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_NOT_SUPPORT);
            }
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_NOT_EXIST);
        }
    }

    /**
     * 查询账户充值结果
     *
     * @param tradeCode 交易单号
     */
    @GetMapping("/getRechargeResult")
    public WrapperResp<Boolean> getRechargeResult(@RequestParam("tradeCode") String tradeCode) {
        return WrapperUtil.success(eAccountTradeService.getRechargeResult(tradeCode));
    }

    /**
     * 账户提现
     *
     * @param eAccountCashOutReq 参数
     */
    @PostMapping("/cashOut")
    public WrapperResp<Boolean> cashOut(@RequestBody @Valid EAccountCashOutReq eAccountCashOutReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        AllInPayDO allInPayDO = eAccountService.getAccountDetail(sysUser);
        if (allInPayDO != null) {
            //交易订单号
            String tradeCode = redisUtils.getSerialNumberByDay(Constant.ACCOUNT_TRACE_CODE, Constant.ACCOUNT_TRACE_CODE_DATE, Constant.ACCOUNT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);

            AllInPayAttachDO allInPayAttachDO = eAccountService.getAccountAttachDetail(allInPayDO.getBizUserId());
            String bankCardNo = AllInPayDM.getBankCard(allInPayDO.getAllInMemberType(), allInPayAttachDO);
            BigDecimal money = eAccountCashOutReq.getMoney();

            WithdrawApplyReq withdrawApplyReq = new WithdrawApplyReq();
            withdrawApplyReq.setBizOrderNo(tradeCode);
            withdrawApplyReq.setAmount(money);
            withdrawApplyReq.setBankCardNo(bankCardNo);
            withdrawApplyReq.setBankCardPro(AllInPayDM.getBankCardPro(allInPayDO.getAllInMemberType()));
            WithdrawApplyResp withdrawApplyResp = payService.withdrawApply(getSysUser(), withdrawApplyReq);
            if (withdrawApplyResp != null) {
                EAccountTradeRecordDO eAccountTradeRecordDO = new EAccountTradeRecordDO();
                eAccountTradeRecordDO.setTradeCode(tradeCode);
                eAccountTradeRecordDO.setTradeMoney(money);
                eAccountTradeRecordDO.setOperation(TradeOperationEnum.ACCOUNT_CASH_OUT.getCode());
                eAccountTradeRecordDO.setRemark(TradeOperationEnum.ACCOUNT_CASH_OUT.getMessage());
                eAccountTradeRecordDO.setTradeTime(System.currentTimeMillis());
                eAccountTradeRecordDO.setBankCardNo(bankCardNo);
                eAccountTradeRecordDO.setMemberId(sysUser.getMemberId());
                eAccountTradeRecordDO.setMemberName(sysUser.getMemberName());
                eAccountTradeRecordDO.setMemberRoleId(sysUser.getMemberRoleId());
                eAccountTradeRecordDO.setMemberRoleName(sysUser.getMemberRoleName());
                eAccountTradeService.saveEAccountTradeRecord(eAccountTradeRecordDO);
                return WrapperUtil.success(true);
            }
            return WrapperUtil.success(false);
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_NOT_EXIST);
        }
    }

    /**
     * 查询登录用户的账户余额
     */
    @GetMapping("/getUserBalance")
    public WrapperResp<BalanceReq> getUserBalance() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        AllInPayDO allInPayDO = eAccountService.getAccountDetail(sysUser);
        if (allInPayDO != null) {
            //查询账户余额
            return WrapperUtil.success(payService.queryBalance(sysUser.getMemberId(), sysUser.getMemberRoleId()));
        } else {
            throw new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_NOT_EXIST);
        }
    }

    /**
     * 重新发送短信验证码(余额支付和快捷支付)
     *
     * @param tradeCode 商户订单交易号
     * @return 操作结果
     */
    @GetMapping("/reSendPayCode")
    public WrapperResp<ResendPaySMSRespResp> reSendPayCode(@RequestParam("tradeCode") String tradeCode) {
        ResendPaySMSReq resendPaySMSReq = new ResendPaySMSReq();
        resendPaySMSReq.setBizOrderNo(tradeCode);
        return WrapperUtil.success(payService.reSendPaySMS(resendPaySMSReq));

    }

    /**
     * 确认支付(余额支付和快捷支付)
     *
     * @return 操作结果
     */
    @PostMapping("/confirmPay")
    public WrapperResp<PayResp> confirmPay(@RequestBody @Valid EAccountConfirmPayReq eAccountConfirmPayReq, HttpServletRequest request) {
        String remoteAddrIp = IpUtil.getIpAddr(request);
        PayReq payNo = new PayReq();
        payNo.setBizOrderNo(eAccountConfirmPayReq.getTradeCode());
        payNo.setVerificationCode(eAccountConfirmPayReq.getVerificationCode());
        payNo.setConsumerIp(remoteAddrIp);
        return WrapperUtil.success(payService.pay(getSysUser(), payNo));
    }
}
