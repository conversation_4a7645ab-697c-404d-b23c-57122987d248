package com.ssy.lingxi.pay.serviceImpl;

import com.alibaba.fastjson.JSON;
import com.ssy.lingxi.common.constant.mq.PayMqConstant;
import com.ssy.lingxi.component.rabbitMQ.service.IMqUtils;
import com.ssy.lingxi.pay.enums.DelayQueueGetPayResultEnum;
import com.ssy.lingxi.pay.model.req.AsynGetPayResultReq;
import com.ssy.lingxi.pay.service.PayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 3.0.0
 * @since 2025/8/11
 */
@Service
@Slf4j
public class PayServiceImpl implements PayService {

    @Resource
    private IMqUtils mqUtils;

    @Override
    public Boolean asynGetPayResult(AsynGetPayResultReq getPayResultReq) {
        log.info("异步获取支付结果, 入参: {}", getPayResultReq);
        DelayQueueGetPayResultEnum theFirstTime = DelayQueueGetPayResultEnum.THE_FIRST_TIME;
        getPayResultReq.setCurrentNotifyNum(theFirstTime.getCode());
        mqUtils.sendDelayMsg(PayMqConstant.PAY_RESULT_QUERY_DELAY_EXCHANGE, PayMqConstant.PAY_RESULT_QUERY_DELAY_ROUTING_KEY, JSON.toJSONString(getPayResultReq), theFirstTime.getTime());
        return true;
    }
}
