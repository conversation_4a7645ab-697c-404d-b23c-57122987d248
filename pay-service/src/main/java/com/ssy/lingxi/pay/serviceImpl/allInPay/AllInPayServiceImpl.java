package com.ssy.lingxi.pay.serviceImpl.allInPay;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.allinpay.sdk.OpenClient;
import com.allinpay.sdk.bean.OpenResponse;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPaymentParameterEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.pay.api.common.allInPay.AllInPayMemberTypeEnum;
import com.ssy.lingxi.pay.api.common.allInPay.AllInPayPayMethodEnum;
import com.ssy.lingxi.pay.api.common.allInPay.EAccountPayStatusEnum;
import com.ssy.lingxi.pay.api.enums.EAccountStatusEnum;
import com.ssy.lingxi.pay.api.model.dto.QueryInExpDetailDTO;
import com.ssy.lingxi.pay.api.model.req.allInPay.*;
import com.ssy.lingxi.pay.api.model.resp.allInPay.*;
import com.ssy.lingxi.pay.config.PayGateWayConfig;
import com.ssy.lingxi.pay.config.allInPay.AllInPayConfig;
import com.ssy.lingxi.pay.config.allInPay.AllInPayProperties;
import com.ssy.lingxi.pay.constant.AllInPayNotifyConstant;
import com.ssy.lingxi.pay.domain.AllInPayDM;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayAttachDO;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayBankDO;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayDO;
import com.ssy.lingxi.pay.enums.allInPay.AllInPayAttachStepEnum;
import com.ssy.lingxi.pay.enums.allInPay.AllInPayEnterpriseStateEnum;
import com.ssy.lingxi.pay.enums.allInPay.AllInPayResultEnum;
import com.ssy.lingxi.pay.handler.convert.AllInPayAttachDOConvert;
import com.ssy.lingxi.pay.handler.convert.RespConvert;
import com.ssy.lingxi.pay.model.dto.AllInPayAttachDTO;
import com.ssy.lingxi.pay.model.req.GateWayPayReq;
import com.ssy.lingxi.pay.model.req.ResendPaySMSReq;
import com.ssy.lingxi.pay.model.req.UserInfoReqReq;
import com.ssy.lingxi.pay.model.req.allInPay.*;
import com.ssy.lingxi.pay.model.resp.ResendPaySMSRespResp;
import com.ssy.lingxi.pay.model.resp.allInPay.AllInPayMemberResp;
import com.ssy.lingxi.pay.model.resp.allInPay.MemberRegAndBindPhoneConfirmResp;
import com.ssy.lingxi.pay.model.resp.allInPay.RegisterCompanyMemberResp;
import com.ssy.lingxi.pay.model.resp.allInPay.memberInfo.EnterpriseMemberInfoResp;
import com.ssy.lingxi.pay.model.resp.allInPay.memberInfo.MemberInfoResp;
import com.ssy.lingxi.pay.model.resp.allInPay.memberInfo.PersonalMemberInfoResp;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayAttachRepository;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayBankRepository;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayRepository;
import com.ssy.lingxi.pay.service.IPayCacheService;
import com.ssy.lingxi.pay.service.allInPay.IAllInPayService;
import com.ssy.lingxi.pay.service.eAccount.IEAccountService;
import com.ssy.lingxi.pay.util.allInPay.MemberServiceUtil;
import com.ssy.lingxi.pay.util.allInPay.MerchantServiceUtil;
import com.ssy.lingxi.pay.util.allInPay.OrderServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通联支付实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/26
 */
@Slf4j
@Service
public class AllInPayServiceImpl implements IAllInPayService, ApplicationContextAware {

    private static final String KEY = "allInPay";

    @Resource
    private AllInPayRepository allInPayRepository;
    @Resource
    private PayGateWayConfig payGateWayConfig;
    @Resource
    private AllInPayAttachRepository allInPayAttachRepository;
    @Resource
    private IPayCacheService payCacheService;
    @Resource
    private AllInPayBankRepository allInPayBankRepository;
    @Resource
    private IRedisUtils redisUtils;
    @Resource
    private IEAccountService eAccountService;
    @Resource
    private AllInPayProperties allInPayProperties;
    @Autowired(required = false)
    private OpenClient openClient;

    private IAllInPayService allInPayService;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 从容器中获取自己的bean实例
        this.allInPayService = applicationContext.getBean(IAllInPayService.class);
    }

    @Override
    public void personalCrate(UserLoginCacheDTO sysUser, AllInPayPersonalAuthReq authReqVO) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        //1-判断手机号码是否绑定成功
        try {
            OpenResponse openResponse = MemberServiceUtil.bindPhone(bizUserId, authReqVO.getPhone(), authReqVO.getVerificationCode());
            if (!AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 个人账户认证 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    public void sendVerificationCode(UserLoginCacheDTO sysUser, SendVerificationCodeReq sendVerificationCodeVO) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            OpenResponse openResponse = MemberServiceUtil.sendVerificationCode(bizUserId, sendVerificationCodeVO.getPhone(), sendVerificationCodeVO.getVerificationCodeType());
            if (!AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 发送短信验证码 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    public void setRealName(UserLoginCacheDTO sysUser, AllInPaySetRealNameReq setRealNameReq) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            OpenResponse openResponse = MemberServiceUtil.setRealName(bizUserId, setRealNameReq.getName(), setRealNameReq.getIdentityCardType(), setRealNameReq.getIdentityCardNo());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                //实名成功后修改数据库状态
                AllInPayAttachDO allInPayAttachDO = allInPayAttachRepository.findByBizUserId(bizUserId);
                allInPayAttachDO.setIdentityCardNo(setRealNameReq.getIdentityCardNo());
                allInPayAttachDO.setIdentityCardType(setRealNameReq.getIdentityCardType());
                allInPayAttachDO.setName(setRealNameReq.getName());
                allInPayAttachDO.setStep(AllInPayAttachStepEnum.AUTH_COMPLETE.getStep());
                allInPayAttachRepository.saveAndFlush(allInPayAttachDO);
            } else {
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 个人实名验证 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    public String signContract(UserLoginCacheDTO sysUser, SignContractReq signContractVO) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        Integer source = signContractVO.getSource();//1-Mobile 2-PC,此属性不影响登录端是app还是pc
        try {
            AllInPayAttachDO allInPayAttachDO = allInPayAttachRepository.findByBizUserId(bizUserId);
            String backUrl = getBackUrl(AllInPayNotifyConstant.SIGN_CONTRACT);//后台通知回调地址
            String signAcctName = SignContractReq.AccountTypeEnum.getSignAcctName(allInPayAttachDO, signContractVO.getAccountType());
            return MemberServiceUtil.signAcctProtocol(
                    bizUserId, signAcctName, source, signContractVO.getJumpPageType(), backUrl, signContractVO.getJumpUrl()
            );
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 账户提现协议签约 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    public String signContractQuery(UserLoginCacheDTO sysUser, SignContractReq signContractVO) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        AllInPayAttachDO allInPayAttachDO = allInPayAttachRepository.findByBizUserId(bizUserId);
        try {
            String signAcctName = SignContractReq.AccountTypeEnum.getSignAcctName(allInPayAttachDO, signContractVO.getAccountType());
            return MemberServiceUtil.signContractQuery(
                    bizUserId, signAcctName, 2, signContractVO.getJumpPageType(), signContractVO.getJumpUrl()
            );
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 账户协议签约查询 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    public void bindBankCardApply(UserLoginCacheDTO sysUser, BindBankCardApplyReq req) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            AllInPayDO allInPayDO = Optional.ofNullable(allInPayRepository.findByBizUserId(bizUserId)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_NOT_EXIST));
            AllInPayAttachDO allInPayAttachDO = Optional.ofNullable(allInPayAttachRepository.findByBizUserId(bizUserId)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_NOT_EXIST));

            String name = AllInPayMemberTypeEnum.ENTERPRISE.getCode().equals(allInPayDO.getAllInMemberType()) ? allInPayAttachDO.getLegalName() : allInPayAttachDO.getName();
            String phone = Optional.ofNullable(req.getPhone()).orElse(allInPayAttachDO.getPhone());

            OpenResponse openResponse = MemberServiceUtil.bindBankCardApply(bizUserId, req.getCardCheck(), phone,
                    req.getBankCardNo(), name, allInPayAttachDO.getIdentityCardType(), allInPayAttachDO.getIdentityCardNo()
            );
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                //保存流水号
                ApplyBindBankCardResp applyBindBankCardResp = JsonUtil.toObj(openResponse.getData(), ApplyBindBankCardResp.class);
                if (Objects.isNull(applyBindBankCardResp)) {
                    log.error("-----------------------请求通联支付-请求绑定银行卡 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                    throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg() + " (响应结果不符合预期)");
                }
                allInPayAttachDO.setBizUserId(bizUserId);
                allInPayAttachDO.setBankTranceNum(applyBindBankCardResp.getTranceNum());
                allInPayAttachDO.setBankReservedPhone(phone);
                allInPayAttachDO.setBankCode(applyBindBankCardResp.getBankCode());
                allInPayAttachDO.setBankCardType(applyBindBankCardResp.getCardType());
                allInPayAttachDO.setBankName(applyBindBankCardResp.getBankName());
                allInPayAttachDO.setBankCardNo(req.getBankCardNo());
                allInPayAttachRepository.saveAndFlush(allInPayAttachDO);
            } else {
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 请求绑定银行卡(发送验证码) 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    public void bindBankCardConfirm(UserLoginCacheDTO sysUser, BindBankCardConfirmReq confirmReq) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            AllInPayAttachDO allInPayAttachDO = Optional.ofNullable(allInPayAttachRepository.findByBizUserId(bizUserId)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_NOT_EXIST));

            String bankReservedPhone = Optional.ofNullable(confirmReq.getPhone()).orElse(allInPayAttachDO.getBankReservedPhone());

            OpenResponse openResponse = MemberServiceUtil.bindBankCardConfirm(bizUserId, allInPayAttachDO.getBankTranceNum(), bankReservedPhone, confirmReq.getVerificationCode());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                //保存数据
                BindBankCardConfirmResp confirmResp = JsonUtil.toObj(openResponse.getData(), BindBankCardConfirmResp.class);
                if (Objects.isNull(confirmResp)) {
                    log.error("-----------------------请求通联支付-请求绑定银行卡 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                    throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg() + " (响应结果不符合预期)");
                }
                allInPayAttachDO.setBizUserId(bizUserId);
                allInPayAttachDO.setBankTranceNum(confirmResp.getTranceNum());
                allInPayAttachDO.setAgreementNo(confirmResp.getAgreementNo());
                allInPayAttachDO.setStep(AllInPayAttachStepEnum.BIND_COMPLETE.getStep());
                allInPayAttachRepository.saveAndFlush(allInPayAttachDO);
            } else {
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 绑定银行卡(确认) 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-解绑手机（验证原手机短信验证码）
     *
     * @param sysUser     当前登录用户
     * @param bindPhoneVO 请求参数
     */
    @Override
    public void unbindPhone(UserLoginCacheDTO sysUser, BindPhoneReq bindPhoneVO) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            OpenResponse openResponse = MemberServiceUtil.unbindPhone(bizUserId, bindPhoneVO.getPhone(), bindPhoneVO.getVerificationCode());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                //删除表中的phone记录
                AllInPayAttachDO allInPayAttachDO = allInPayAttachRepository.findByBizUserId(bizUserId);
                allInPayAttachDO.setPhone(null);
                allInPayAttachDO.setIsPhoneChecked(Boolean.FALSE);
                allInPayAttachRepository.saveAndFlush(allInPayAttachDO);
            } else {
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 解绑手机 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-解绑绑定银行卡
     *
     * @param sysUser       当前操作用户
     * @param unBindPhoneVO 银行卡号
     */
    @Override
    public void unbindBankCard(UserLoginCacheDTO sysUser, UnBindPhoneReq unBindPhoneVO) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            OpenResponse openResponse = MemberServiceUtil.unbindBankCard(bizUserId, unBindPhoneVO.getBankCardNo());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                //删除表中的银行卡信息
                AllInPayAttachDO allInPayAttach = allInPayAttachRepository.findByBizUserId(bizUserId);
                allInPayAttach.setBankName(null);
                allInPayAttach.setBankCardNo(null);
                allInPayAttachRepository.saveAndFlush(allInPayAttach);
            } else {
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 解绑绑定银行卡 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-获取会员信息
     *
     * @param sysUser 当前登录用户
     * @return 操作结果
     */
    @Override
    public AllInPayMemberResp getMemberInfo(UserLoginCacheDTO sysUser) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());

        AllInPayAttachDO allInPayAttachDO = Optional.ofNullable(allInPayAttachRepository.findByBizUserId(bizUserId)).orElseGet(AllInPayAttachDO::new);
        AllInPayDO allInPayDO = Optional.ofNullable(allInPayRepository.findByBizUserId(bizUserId)).orElseGet(AllInPayDO::new);

        boolean doneFinalStep = AllInPayAttachStepEnum.doneFinalStep(allInPayDO.getAllInMemberType(), allInPayAttachDO.getStep());
        if (!doneFinalStep || !BooleanUtil.isTrue(allInPayAttachDO.getDataSyncAfterOpenAcct())) {
            // (未完成开户步骤 或者 (开户完成 且 未同步过数据)), 查询通联接口更新数据
            allInPayService.updateMemberInfo(bizUserId, allInPayAttachDO, allInPayDO);
        }

        AllInPayMemberResp resp = new AllInPayMemberResp();
        RespConvert.INSTANCE.updateDO(allInPayDO, resp);
        RespConvert.INSTANCE.updateDO(allInPayAttachDO, resp);
        if (Objects.isNull(resp.getMemberType())) {
            resp.setMemberType(sysUser.getMemberType());
        }

        if (Objects.equals(resp.getAllInMemberType(), AllInPayMemberTypeEnum.PERSONAL.getCode())) {
            resp.setAccountBelong(allInPayAttachDO.getName());
        } else {
            resp.setAccountBelong(allInPayAttachDO.getCompanyName());
        }

        return resp;
    }

    @Transactional
    @Override
    public void updateMemberInfo(String bizUserId, AllInPayAttachDO allInPayAttachDO, AllInPayDO allInPayDO) {
        OpenResponse openResponse;
        try {
            // 调用通联接口, 获取会员数据
            openResponse = MemberServiceUtil.getMemberInfo(bizUserId);
        } catch (Exception ignore) {
            // 静默更新数据
            return;
        }

        if (Objects.isNull(openResponse)) {
            // 没有响应体
            return;
        }

        if (!AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
            // 响应不成功
            return;
        }

        // 响应成功
        MemberInfoResp memberInfoResp = JsonUtil.toObj(openResponse.getData(), MemberInfoResp.class);

        if (Objects.isNull(memberInfoResp) || Objects.isNull(memberInfoResp.getMemberInfo())) {
            return;
        }

        if (AllInPayMemberTypeEnum.ENTERPRISE.getCode().equals(memberInfoResp.getMemberType())) {
            // 企业会员
            EnterpriseMemberInfoResp enterpriseMemberInfoResp = memberInfoResp.getMemberInfo().to(EnterpriseMemberInfoResp.class);
            AllInPayAttachDOConvert.INSTANCE.updateDO(enterpriseMemberInfoResp, allInPayAttachDO);
            // TODO @Fa 通联功能优化时, 数据库数据改为用密文存储
            allInPayAttachDO.setAccountNo(Optional.ofNullable(enterpriseMemberInfoResp.getAccountNo()).map(o -> openClient.decrypt(o)).orElse(""));
            allInPayAttachDO.setLegalIds(Optional.ofNullable(enterpriseMemberInfoResp.getLegalIds()).map(o -> openClient.decrypt(o)).orElse(""));
            allInPayAttachDO.setStep(AllInPayDM.getStep(enterpriseMemberInfoResp));

            allInPayDO.setAccountStatus(enterpriseMemberInfoResp.getStatus());
        }

        if (AllInPayMemberTypeEnum.PERSONAL.getCode().equals(memberInfoResp.getMemberType())) {
            // 个人会员
            PersonalMemberInfoResp personalMemberInfoResp = memberInfoResp.getMemberInfo().to(PersonalMemberInfoResp.class);
            AllInPayAttachDOConvert.INSTANCE.updateDO(personalMemberInfoResp, allInPayAttachDO);
            // TODO @Fa 通联功能优化时, 数据库数据改为用密文存储
            allInPayAttachDO.setIdentityCardNo(Optional.ofNullable(personalMemberInfoResp.getIdentityCardNo()).map(o -> openClient.decrypt(o)).orElse(""));
//            allInPayAttachDO.setStep(AllInPayDM.getStep(personalMemberInfoResp));

            allInPayDO.setAccountStatus(personalMemberInfoResp.getUserState());
        }

        boolean doneFinalStep = AllInPayAttachStepEnum.doneFinalStep(allInPayDO.getAllInMemberType(), allInPayAttachDO.getStep());
        if (doneFinalStep) {
            // 完成开户, 数据同步完成
            allInPayAttachDO.setDataSyncAfterOpenAcct(true);
        }

        allInPayRepository.saveAndFlush(allInPayDO);
        allInPayAttachRepository.saveAndFlush(allInPayAttachDO);
    }

    /**
     * 通联支付-影印件采集
     *
     * @param sysUser         当前登录用户
     * @param idCardCollectVO 请求数据
     */
    @Override
    public void idCardCollect(UserLoginCacheDTO sysUser, IdCardCollectReq idCardCollectVO) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        String ocrComparisonResultBackUrl = getBackUrl(AllInPayNotifyConstant.ID_CARD_COLLECT);
        //影印件核对结果异步通知地址，不上送则无异步通知
        try {
            URL excelUrl = URLUtil.toUrlForHttp(URLUtil.decode(idCardCollectVO.getPicture()));
            InputStream inputStream = excelUrl.openStream();
            String encode = Base64.encode(inputStream);
            OpenResponse openResponse = MemberServiceUtil.idCardCollect(bizUserId, idCardCollectVO.getPicType(), encode, ocrComparisonResultBackUrl);
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                //修改数据库状态
                AllInPayAttachDO payAttach = allInPayAttachRepository.findByBizUserId(bizUserId);
                payAttach = payAttach != null ? payAttach : new AllInPayAttachDO();
                payAttach.setBizUserId(bizUserId);
                String picUrl = payAttach.getPicUrl();
                if (StringUtils.hasLength(picUrl) && !"null".equals(picUrl)) {
                    List<IdCardCollectReq> idCardCollectVOS = JSON.parseArray(picUrl, IdCardCollectReq.class);
                    //如果类型相同则替换
                    if (idCardCollectVOS.stream().map(IdCardCollectReq::getPicType).collect(Collectors.toList()).contains(idCardCollectVO.getPicType())) {
                        for (IdCardCollectReq cardCollectVO : idCardCollectVOS) {
                            if (idCardCollectVO.getPicType().equals(cardCollectVO.getPicType())) {
                                cardCollectVO.setPicture(idCardCollectVO.getPicture());
                            }
                        }
                    } else {
                        //没有相同则直接添加
                        idCardCollectVOS.add(idCardCollectVO);
                    }
                    payAttach.setPicUrl(JSON.toJSONString(idCardCollectVOS));
                    //当图片数量为3时,添加影印件成功
                    if (idCardCollectVOS.size() == 3) {
                        payAttach.setStep(2);
                    }
                } else {
                    List<IdCardCollectReq> idCardCollectVOS = new ArrayList<>();
                    idCardCollectVOS.add(idCardCollectVO);
                    payAttach.setPicUrl(JSON.toJSONString(idCardCollectVOS));
                }
                allInPayAttachRepository.saveAndFlush(payAttach);
            } else {
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 影印件采集 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-查询余额
     *
     * @param memberId     会员id
     * @param memberRoleId 会员角色id
     * @return 操作结果
     */
    @Override
    public BalanceReq queryBalance(Long memberId, Long memberRoleId) {
        //"7407F9AC0A0000270C83A8495419B262";
        String bizUserId = getBizUserId(memberId, memberRoleId);
        String accountSetNo;//需动态获取每个平台的账户集
        String cacheParams = redisUtils.stringGet(KEY, RedisConstant.REDIS_PAY_INDEX);
        JSONObject cacheValue = JSON.parseObject(cacheParams);
        if (memberId != 1 && memberRoleId != 1) {
            accountSetNo = cacheValue.getString(OrderPaymentParameterEnum.ALLIN_DELEGATE_SET.getCode().toString());
        } else {
            accountSetNo = cacheValue.getString(OrderPaymentParameterEnum.ALLIN_STANDARD_SET.getCode().toString());
        }
        JSONObject resp;
        try {
            OpenResponse openResponse = OrderServiceUtil.queryBalance(bizUserId, accountSetNo);
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                BalanceReq vo = new BalanceReq();
                vo.setAllAmount(!((resp.getLong("allAmount")) <= 0) ? new BigDecimal(resp.getLong("allAmount")).multiply(new BigDecimal("0.01")) : new BigDecimal("0"));//分转元
                vo.setFreeZenAmount(!((resp.getLong("freezenAmount")) <= 0) ? new BigDecimal((resp.getLong("freezenAmount"))).multiply(new BigDecimal("0.01")) : new BigDecimal("0"));//分转元
                vo.setAvailableAmount(!((((resp.getLong("allAmount")) - resp.getLong("freezenAmount"))) <= 0) ? new BigDecimal(((resp.getLong("allAmount")) - (resp.getLong("freezenAmount")))).multiply(new BigDecimal("0.01")) : new BigDecimal("0"));
                return vo;
            } else {
                log.warn("-----------------------请求通联支付-查询余额接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                //当前用户不可用,这个会员ID只是创建了会员，企业认证没认证，没有作为收款方收过钱，余额账户要收过钱系统才会生成
                if (openResponse.getSubCode().equals("30022")) {
                    BalanceReq vo = new BalanceReq();
                    vo.setAllAmount(BigDecimal.ZERO);//分转元
                    vo.setFreeZenAmount(BigDecimal.ZERO);//分转元
                    vo.setAvailableAmount(BigDecimal.ZERO);
                    return vo;
                }
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 查询余额 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-查询账户收支明细
     *
     * @param memberId     会员id
     * @param memberRoleId 会员角色id
     * @param dto          查询条件
     * @return 操作结果
     */
    @Override
    public QueryInExpDetailResp queryInExpDetail(Long memberId, Long memberRoleId, QueryInExpDetailDTO dto) {
        //"7407F9AC0A0000270C83A8495419B262";
        String bizUserId = getBizUserId(memberId, memberRoleId);
        String accountSetNo = null;//需动态获取每个平台的账户集
        JSONObject resp;
        try {
            OpenResponse openResponse = OrderServiceUtil.queryInExpDetail(bizUserId, accountSetNo, dto.getDateStart(), dto.getDateEnd(), dto.getStartPosition(), dto.getQueryNum(), dto.getTradeType());
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                QueryInExpDetailResp vo = new QueryInExpDetailResp();
                List<InExpDetailResp> inExpDetails = new ArrayList<>();
                vo.setBizUserId(resp.getString("bizUserId"));
                vo.setTotalNum(Long.valueOf(resp.getString("totalNum")));
                vo.setExtendInfo(resp.getString("extendInfo"));
                JSONArray array = resp.getJSONArray("inExpDetail");
                for (int i = 0; i < array.size(); i++) {
                    InExpDetailResp detail = new InExpDetailResp();
                    JSONObject jsonObject = array.getJSONObject(i);
                    detail.setTradeNo(jsonObject.getString("tradeNo"));
                    detail.setAccountSetName(jsonObject.getString("accountSetName"));
                    detail.setChangeTime(jsonObject.getString("changeTime"));
                    detail.setCurAmount(new BigDecimal(jsonObject.getLong("curAmount")).multiply(new BigDecimal("0.01")));//分转元
                    detail.setOriAmount(new BigDecimal(jsonObject.getLong("oriAmount")).multiply(new BigDecimal("0.01")));
                    detail.setChgAmount(new BigDecimal(jsonObject.getLong("chgAmount")).multiply(new BigDecimal("0.01")));
                    detail.setCurFreeZenAmount(jsonObject.getLong("curFreeZenAmount"));
                    detail.setBizOrderNo(jsonObject.getString("bizOrderNo"));
                    detail.setTradeType(jsonObject.getString("tradeType"));
                    detail.setType(jsonObject.getString("type"));
                    detail.setRemark(jsonObject.getString("remark"));
                    detail.setExtendInfo(jsonObject.getString("extendInfo"));
                    inExpDetails.add(detail);
                }
                vo.setInExpDetails(inExpDetails);
                return vo;
            } else {
                log.warn("-----------------------请求通联支付-查询账户收支明细接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                //当前用户不可用,这个会员ID只是创建了会员，企业认证没认证，没有作为收款方收过钱，余额账户要收过钱系统才会生成
                if (openResponse.getSubCode().equals("30022")) {
                    return null;
                }
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 查询账户收支明细 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-冻结金额
     *
     * @param memberId         会员id
     * @param memberRoleId     会员角色id
     * @param freezeMoneyReqVO 请求数据
     * @return 冻结结果
     */
    @Override
    public FreezeMoneyResp freezeMoney(Long memberId, Long memberRoleId, FreezeMoneyReqReq freezeMoneyReqVO) {
        String cacheParams = redisUtils.stringGet(KEY, RedisConstant.REDIS_PAY_INDEX);
        JSONObject cacheValue = JSON.parseObject(cacheParams);
        if (memberId != 1 && memberRoleId != 1) {
            freezeMoneyReqVO.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_DELEGATE_SET.getCode().toString()));
        } else {
            freezeMoneyReqVO.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_STANDARD_SET.getCode().toString()));
        }
        String bizUserId = getBizUserId(memberId, memberRoleId);
        OpenResponse openResponse;
        JSONObject resp;
        if (freezeMoneyReqVO.getType().equals(EAccountStatusEnum.freeze.getCode())) {//1-冻结 2-解冻

            try {
                openResponse = OrderServiceUtil.freezeMoney(bizUserId, freezeMoneyReqVO.getBizFreeZenNo(), freezeMoneyReqVO.getAccountSetNo(), freezeMoneyReqVO.getAmount().multiply(new BigDecimal(100)).longValue());//元转分
                resp = JSON.parseObject(openResponse.getData());
                if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                    FreezeMoneyResp vo = new FreezeMoneyResp();
                    vo.setAmount(new BigDecimal(resp.getLong("amount")).multiply(new BigDecimal("0.01")));//分转元
                    vo.setBizFreeZenNo(resp.getString("bizFreezenNo"));
                    return vo;
                } else {
                    log.error("-----------------------请求联支付-冻结金额接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                    throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
                }
            } catch (BusinessException e) {
                throw e;
            } catch (Exception ex) {
                throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
            }
        } else {
            try {
                openResponse = OrderServiceUtil.unfreezeMoney(bizUserId, freezeMoneyReqVO.getBizFreeZenNo(), freezeMoneyReqVO.getAccountSetNo(), freezeMoneyReqVO.getAmount().multiply(new BigDecimal(100)).longValue());//元转分
                resp = JSON.parseObject(openResponse.getData());
                if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                    FreezeMoneyResp vo = new FreezeMoneyResp();
                    vo.setAmount(new BigDecimal(resp.getLong("amount")).multiply(new BigDecimal("0.01")));//分转元
                    vo.setBizFreeZenNo(resp.getString("bizFreezenNo"));
                    return vo;
                } else {
                    log.error("-----------------------请求联支付-冻结金额接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                    throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
                }
            } catch (BusinessException e) {
                throw e;
            } catch (Exception ex) {
                throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
            }
        }
    }

    /**
     * 通联支付-设置或修改或重置支付密码【密码验证版】
     *
     * @param sysUser 当前登录用户
     * @param vo      请求参数
     * @return 操作结果
     */
    @Override
    public String setPayPwd(UserLoginCacheDTO sysUser, SetPayPwdReq vo) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        if (vo.getType() == 1) {//1-设置密码
            String backUrl = getBackUrl(AllInPayNotifyConstant.SET_PAY_PWD);
            try {
                return OrderServiceUtil.setPayPwd(bizUserId, vo.getJumpPageType(), vo.getPhone(), vo.getName(), vo.getIdentityType(), vo.getIdentityNo(), backUrl, vo.getType());
            } catch (BusinessException e) {
                throw e;
            } catch (Exception ex) {
                throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
            }
        }
        if (vo.getType() == 2) {//2-修改密码
            String backUrl = getBackUrl(AllInPayNotifyConstant.UPDATE_PAY_PWD);
            try {
                return OrderServiceUtil.setPayPwd(bizUserId, vo.getJumpPageType(), vo.getPhone(), vo.getName(), vo.getIdentityType(), vo.getIdentityNo(), backUrl, vo.getType());
            } catch (Exception e) {
                throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
            }
        }
        if (vo.getType() == 3) {//3-重置密码
            String backUrl = getBackUrl(AllInPayNotifyConstant.RESET_PAY_PWD);
            try {
                return OrderServiceUtil.setPayPwd(bizUserId, vo.getJumpPageType(), vo.getPhone(), vo.getName(), vo.getIdentityType(), vo.getIdentityNo(), backUrl, vo.getType());
            } catch (Exception e) {
                throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
            }
        }
        return null;
    }

    /**
     * 通联支付-企业会员绑定对公户
     *
     * @param sysUser 当前登录用户
     * @param vo      请求参数
     */
    @Override
    public void bindCompanyAccount(UserLoginCacheDTO sysUser, BindCompanyAccountReq vo) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            //保存对公账号信息
            AllInPayAttachDO allInPayAttach = allInPayAttachRepository.findByBizUserIdAndAccountNo(bizUserId, vo.getAccountNo());
            if (!Objects.isNull(allInPayAttach)) {
                allInPayAttach.setStep(4);//绑定对公账号
                allInPayAttachRepository.saveAndFlush(allInPayAttach);
            } else {
                throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
            }

        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 企业会员绑定对公户 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-充值申请
     *
     * @param sysUser 当前登录用户
     * @param vo      请求参数
     * @return 操作结果
     */
    @Override
    public DepositApplyResp depositApply(UserLoginCacheDTO sysUser, DepositApplyReq vo) {
        String cacheParams = redisUtils.stringGet(KEY, RedisConstant.REDIS_PAY_INDEX);
        JSONObject cacheValue = JSON.parseObject(cacheParams);
        if (sysUser.getMemberId() != 1 && sysUser.getMemberRoleId() != 1) {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_DELEGATE_SET.getCode().toString()));
        } else {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_STANDARD_SET.getCode().toString()));
        }
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        JSONObject resp;
        String bankCardNo = null;
        try {
            //判断是否进行收银宝快捷支付
            if (AllInPayPayMethodEnum.QUICKPAY_VSP.getPayMethod().equals(vo.getPayMethodStr())) {
                AllInPayDO allInPayDO = allInPayRepository.findByBizUserId(bizUserId);
                AllInPayAttachDO payAttach = allInPayAttachRepository.findByBizUserId(bizUserId);
                if (AllInPayMemberTypeEnum.ENTERPRISE.getCode().equals(allInPayDO.getAllInMemberType())) {//企业会员
                    bankCardNo = payAttach.getAccountNo();
                } else {
                    bankCardNo = payAttach.getBankCardNo();
                }
            }
            String backUrl = getBackUrl(AllInPayNotifyConstant.DEPOSIT_APPLY);
            OpenResponse openResponse = OrderServiceUtil.depositApply(bizUserId, vo.getBizOrderNo(), vo.getPayMethodStr(), bankCardNo, "no_credit", vo.getOpenId(), null, vo.getVspCusId(), vo.getSubAppId(), null,
                    null, vo.getAccountSetNo(), "test", backUrl, vo.getAmount().multiply(new BigDecimal("100")).longValue(), vo.getCusIp());
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                String payStatus = resp.getString("payStatus");
                String payFailMessage = resp.getString("payFailMessage");
                if (EAccountPayStatusEnum.FAIL.getCode().equals(payStatus) || EAccountPayStatusEnum.UNPAY.getCode().equals(payStatus)) {
                    throw new BusinessException(payFailMessage);
                } else {
                    DepositApplyResp respVO = new DepositApplyResp();
                    respVO.setPayStatus(payStatus);
                    respVO.setPayFailMessage(payFailMessage);
                    respVO.setBizUserId(resp.getString("bizUserId"));
                    respVO.setOrderNo(resp.getString("orderNo"));
                    respVO.setBizOrderNo(resp.getString("bizOrderNo"));
                    respVO.setWeChatAPPInfo(resp.getString("weChatAPPInfo"));
                    respVO.setPayInfo(resp.getString("payInfo"));
                    if (vo.getPayMethodStr().equals(AllInPayPayMethodEnum.WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG.getPayMethod())) {
                        respVO.setPayInfo(resp.getString("miniprogramPayInfo_VSP"));
                    }
                    respVO.setValidateType(resp.getLong("validateType"));
                    return respVO;
                }
            } else {
                log.error("-----------------------请求通联支付-充值申请接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 充值申请接口 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-提现申请
     *
     * @param sysUser 当前登录用户
     * @param vo      请求参数
     * @return 操作结果
     */
    @Override
    public WithdrawApplyResp withdrawApply(UserLoginCacheDTO sysUser, WithdrawApplyReq vo) {
        String cacheParams = redisUtils.stringGet(KEY, RedisConstant.REDIS_PAY_INDEX);
        JSONObject cacheValue = JSON.parseObject(cacheParams);
        if (sysUser.getMemberId() != 1 && sysUser.getMemberRoleId() != 1) {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_DELEGATE_SET.getCode().toString()));
        } else {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_STANDARD_SET.getCode().toString()));
        }
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        JSONObject resp;
        OpenResponse openResponse;
        String backUrl = getBackUrl(AllInPayNotifyConstant.WITHDRAW_APPLY);
        try {
            openResponse = OrderServiceUtil.withdrawApply(bizUserId, vo.getBizOrderNo(), vo.getAccountSetNo(), vo.getAmount().multiply(new BigDecimal("100")).longValue(), vo.getBankCardNo(), vo.getBankCardPro(), vo.getWithdrawType(), backUrl);
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 提现申请 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
        resp = JSON.parseObject(openResponse.getData());
        if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
            WithdrawApplyResp respVO = new WithdrawApplyResp();
            respVO.setPayStatus(resp.getString("payStatus"));
            respVO.setPayFailMessage(resp.getString("payFailMessage"));
            respVO.setBizUserId(resp.getString("bizUserId"));
            respVO.setOrderNo(resp.getString("orderNo"));
            respVO.setBizOrderNo(resp.getString("bizOrderNo"));
            return respVO;
        } else {
            log.error("-----------------------请求通联支付-提现申请接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
            throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
        }
    }

    /**
     * 通联支付-确认支付(后台+短信验证码确认)
     *
     * @param sysUser 当前登录用户
     * @param vo      请求参数
     * @return 操作结果
     */
    @Override
    public PayResp pay(UserLoginCacheDTO sysUser, PayReq vo) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());

        JSONObject resp;
        try {
            OpenResponse openResponse = OrderServiceUtil.pay(bizUserId, vo.getBizOrderNo(), null, vo.getVerificationCode(), vo.getConsumerIp());
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                String payStatus = resp.getString("payStatus");
                if (EAccountPayStatusEnum.FAIL.getCode().equals(payStatus) || EAccountPayStatusEnum.UNPAY.getCode().equals(payStatus)) {
                    throw new BusinessException(resp.getString("payFailMessage"));
                } else {
                    PayResp respVO = new PayResp();
                    respVO.setPayStatus(payStatus);
                    respVO.setPayFailMessage(resp.getString("payFailMessage"));
                    respVO.setBizUserId(resp.getString("bizUserId"));
                    respVO.setBizOrderNo(resp.getString("bizOrderNo"));
                    return respVO;
                }
            } else {
                log.error("-----------------------请求通联支付-确认支付(后台+短信验证码确认)接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 确认支付(后台+短信验证码确认) 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-消费申请(余额支付方式)
     *
     * @param vo 请求参数
     * @return 操作结果
     */
    @Override
    public ConsumeApplyResp balanceConsumeApply(ConsumeApplyReq vo) {
        String payerId = getBizUserId(vo.getMemberId(), vo.getMemberRoleId());
        String recieverId = getBizUserId(vo.getRecieverMemberId(), vo.getRecieverMemberRoleId());
        String cacheParams = redisUtils.stringGet(KEY, RedisConstant.REDIS_PAY_INDEX);
        JSONObject cacheValue = JSON.parseObject(cacheParams);
        if (vo.getMemberId() != 1 && vo.getMemberRoleId() != 1) {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_DELEGATE_SET.getCode().toString()));
        } else {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_STANDARD_SET.getCode().toString()));

        }

        //这样可以简短附加数据的长度
        AllInPayAttachDTO allInPayAttachDTO = new AllInPayAttachDTO();
        allInPayAttachDTO.setAttach(vo.getExtendInfo());
        allInPayAttachDTO.setServiceType(vo.getServiceType());
        String attachJson = payCacheService.serializeObject(allInPayAttachDTO);
        JSONObject resp;
        try {
            String backUrl = getBackUrl(AllInPayNotifyConstant.CONSUME_APPLY);
            OpenResponse openResponse = OrderServiceUtil.balanceConsumeApply(payerId, recieverId, vo.getBizOrderNo(),
                    new BigDecimal(String.valueOf(vo.getAmount())).multiply(new BigDecimal("100")).longValue(),
                    new BigDecimal(String.valueOf(vo.getFee())).multiply(new BigDecimal("100")).longValue(),
                    vo.getValidateType(), null, backUrl, vo.getPayMethodStr(), vo.getAccountSetNo(), vo.getIndustryCode(), vo.getIndustryName(), attachJson);
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                ConsumeApplyResp respVO = new ConsumeApplyResp();
                respVO.setPayStatus(resp.getString("payStatus"));
                respVO.setPayFailMessage(resp.getString("payFailMessage"));
                respVO.setBizUserId(resp.getString("bizUserId"));
                respVO.setBizOrderNo(resp.getString("bizOrderNo"));
                respVO.setTradeNo(resp.getString("tradeNo"));
                respVO.setExtendInfo(resp.getString("extendInfo"));
                return respVO;
            } else {
                log.error("-----------------------请求通联支付-消费申请(余额支付方式)接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 消费申请(余额支付方式) 失败,payerId: {},recieverId: {}, errMsg: {}", payerId, recieverId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-消费申请(支付宝/微信PC端正扫,支付宝app(原生或者收银宝的),微信app(只有原生))
     *
     * @param vo 请求参数
     * @return 操作结果
     */
    @Override
    public ConsumeApplyResp scanBalanceConsumeApply(ConsumeApplyReq vo) {
        String payerId = getBizUserId(vo.getMemberId(), vo.getMemberRoleId());
        String recieverId = getBizUserId(vo.getRecieverMemberId(), vo.getRecieverMemberRoleId());

        String bizUserId = getBizUserId(vo.getMemberId(), vo.getMemberRoleId());
        //封装通联支付附加数据 长度最多为50
        //这样可以简短附加数据的长度
        AllInPayAttachDTO allInPayAttachDTO = new AllInPayAttachDTO();
        allInPayAttachDTO.setAttach(vo.getExtendInfo());
        allInPayAttachDTO.setServiceType(vo.getServiceType());
        String attachJson = payCacheService.serializeObject(allInPayAttachDTO);
        JSONObject resp;
        try {
            String backUrl = getBackUrl(AllInPayNotifyConstant.CONSUME_APPLY);
            //判断是否进行收银宝快捷支付
            if (AllInPayPayMethodEnum.QUICKPAY_VSP.getPayMethod().equals(vo.getPayMethodStr())) {
                AllInPayDO allInPayDO = allInPayRepository.findByBizUserId(bizUserId);
                AllInPayAttachDO payAttach = allInPayAttachRepository.findByBizUserId(bizUserId);
                if (AllInPayMemberTypeEnum.ENTERPRISE.getCode().equals(allInPayDO.getAllInMemberType())) {//企业会员
                    vo.setBankCardNo(payAttach.getAccountNo());
                } else {
                    vo.setBankCardNo(payAttach.getBankCardNo());
                }
            }
            OpenResponse openResponse = OrderServiceUtil.scanBalanceConsumeApply(payerId, recieverId, vo.getBizOrderNo(),
                    new BigDecimal(String.valueOf(vo.getAmount())).multiply(new BigDecimal("100")).longValue(),
                    new BigDecimal(String.valueOf(vo.getFee())).multiply(new BigDecimal("100")).longValue(), vo.getValidateType(), "test",
                    backUrl, vo.getPayMethodStr(), vo.getLimitPay(), vo.getVspCusId(), vo.getSubAppId(), vo.getAcct(), vo.getAllInPayType(),
                    vo.getBankCardNo(), vo.getIndustryCode(), vo.getIndustryName(), attachJson, vo.getCusIp());
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                ConsumeApplyResp respVO = new ConsumeApplyResp();
                respVO.setPayStatus(resp.getString("payStatus"));
                respVO.setPayFailMessage(resp.getString("payFailMessage"));
                respVO.setBizUserId(resp.getString("bizUserId"));
                respVO.setBizOrderNo(resp.getString("bizOrderNo"));
                respVO.setTradeNo(resp.getString("tradeNo"));
                respVO.setExtendInfo(resp.getString("extendInfo"));
                respVO.setPayInfo(resp.getString("payInfo"));
                respVO.setWeChatAPPInfo(resp.getString("weChatAPPInfo"));
                return respVO;
            } else {
                log.error("-----------------------请求通联支付-消费申请(支付宝/微信PC端正扫,支付宝app(原生或者收银宝的),微信app(只有原生))接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 消费申请(支付宝/微信PC端正扫,支付宝app(原生或者收银宝的),微信app(只有原生)) 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-会员绑定支付账户用户标识
     *
     * @param sysUser 当前登录用户
     * @param vo      请求对象
     */
    @Override
    public void applyBindAcct(UserLoginCacheDTO sysUser, ApplyBindAcctReq vo) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            OpenResponse openResponse = MemberServiceUtil.applyBindAcct(bizUserId, vo.getOperationType(), vo.getAcctType(), vo.getAcct());
            if (!AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 会员绑定支付账户用户标识 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-托管代收申请（标准版）(余额支付方式)
     *
     * @param vo 请求参数
     * @return 操作结果
     */
    @Override
    public ConsumeApplyResp balanceAgentCollectApply(AgentCollectApplyReq vo) {
        String cacheParams = redisUtils.stringGet(KEY, RedisConstant.REDIS_PAY_INDEX);
        JSONObject cacheValue = JSON.parseObject(cacheParams);
        if (vo.getMemberId() != 1 && vo.getMemberRoleId() != 1) {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_DELEGATE_SET.getCode().toString()));
        } else {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_STANDARD_SET.getCode().toString()));
        }
        //封装通联支付附加数据
        //这样可以简短附加数据的长度
        AllInPayAttachDTO allInPayAttachDTO = new AllInPayAttachDTO();
        allInPayAttachDTO.setAttach(vo.getExtendInfo());
        allInPayAttachDTO.setServiceType(vo.getServiceType());
        String attachJson = payCacheService.serializeObject(allInPayAttachDTO);
        JSONObject resp = null;
        OpenResponse openResponse = null;
        try {
            JSONArray jsonArray = new JSONArray();
            if (!CollectionUtils.isEmpty(vo.getRecieverList())) {
                for (ReceiverReq recieverVO : vo.getRecieverList()) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("bizUserId", getBizUserId(recieverVO.getMemberId(), recieverVO.getMemberRoleId()));
                    jsonObject.put("amount", new BigDecimal(String.valueOf(recieverVO.getAmount())).multiply(new BigDecimal("100")).longValue());
                    jsonArray.add(jsonObject);
                }
            }
            String backUrl = getBackUrl(AllInPayNotifyConstant.AGENT_COLLECT_APPLY);
            String frontUrl = payGateWayConfig.getPlatformUrl() + "/orderAbility/purchaseOrder/readyPayOrder";
            AllInPayDO allInPayDO;
            List<AllInPayDO> allByMemberIdAndMemberRoleId = allInPayRepository.findAllByMemberIdAndMemberRoleId(vo.getMemberId(), vo.getMemberRoleId());
            if (allByMemberIdAndMemberRoleId.size() == 1) {
                allInPayDO = allByMemberIdAndMemberRoleId.get(0);
            } else {
                allInPayDO = allByMemberIdAndMemberRoleId.stream().filter(a -> a.getAllInMemberType().equals(2)).findFirst().orElse(null);
                if (Objects.isNull(allInPayDO) || !AllInPayEnterpriseStateEnum.SUCCESSFUL_REVIEW.getCode().equals(allInPayDO.getAccountStatus())) {
                    allInPayDO = allByMemberIdAndMemberRoleId.stream().filter(a -> a.getAllInMemberType().equals(3)).findFirst().orElse(null);
                }
            }

            if (Objects.isNull(allInPayDO)) {
                throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_GET_MEMBER_INFO_ERROR);
            }

            String payerId = allInPayDO.getBizUserId();
            openResponse = OrderServiceUtil.balanceAgentCollectApply(vo.getBizOrderNo(), payerId,
                    new BigDecimal(String.valueOf(vo.getAmount())).multiply(new BigDecimal("100")).longValue(),
                    new BigDecimal(String.valueOf(vo.getFee())).multiply(new BigDecimal("100")).longValue(),
                    vo.getValidateType().longValue(), frontUrl, backUrl, vo.getPayMethodStr(), vo.getAccountSetNo(), vo.getTradeCode(),
                    vo.getIndustryCode(), vo.getIndustryName(), attachJson, vo.getSource(), jsonArray);
            resp = JSON.parseObject(openResponse.getData());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 托管代收申请（标准版）(余额支付方式) 失败,req: {}, errMsg: {}", JsonUtil.toJson(vo), e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
        if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
            ConsumeApplyResp respVO = new ConsumeApplyResp();
            respVO.setPayStatus(resp.getString("payStatus"));
            respVO.setPayFailMessage(resp.getString("payFailMessage"));
            respVO.setBizUserId(resp.getString("bizUserId"));
            respVO.setBizOrderNo(resp.getString("bizOrderNo"));
            respVO.setTradeNo(resp.getString("tradeNo"));
            respVO.setExtendInfo(resp.getString("extendInfo"));
            return respVO;
        } else {
            log.error("-----------------------请求通联支付-托管代收申请（标准版）(余额支付方式) 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
            throw new BusinessException(openResponse.getSubMsg());
        }
    }

    /**
     * 通联支付-托管代收申请（标准版）(支付宝/微信PC端正扫,支付宝app(原生或者收银宝的),微信app(只有原生))
     *
     * @param vo 请求参数
     * @return 操作结果
     */
    @Override
    public ConsumeApplyResp scanAgentCollectApply(AgentCollectApplyReq vo) {
        //封装通联支付附加数据
        //这样可以简短附加数据的长度
        AllInPayAttachDTO allInPayAttachDTO = new AllInPayAttachDTO();
        allInPayAttachDTO.setAttach(vo.getExtendInfo());
        allInPayAttachDTO.setServiceType(vo.getServiceType());
        String attachJson = payCacheService.serializeObject(allInPayAttachDTO);
        JSONObject resp = null;
        OpenResponse openResponse = null;
        try {
            JSONArray recieverList = new JSONArray();
            if (!CollectionUtils.isEmpty(vo.getRecieverList())) {
                for (ReceiverReq recieverVO : vo.getRecieverList()) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("bizUserId", getBizUserId(recieverVO.getMemberId(), recieverVO.getMemberRoleId()));
                    jsonObject.put("amount", new BigDecimal(String.valueOf(recieverVO.getAmount())).multiply(new BigDecimal("100")).longValue());
                    recieverList.add(jsonObject);
                }
            }
            AllInPayDO allInPayDO;
            List<AllInPayDO> allByMemberIdAndMemberRoleId = allInPayRepository.findAllByMemberIdAndMemberRoleId(vo.getMemberId(), vo.getMemberRoleId());
            if (allByMemberIdAndMemberRoleId.size() == 1) {
                allInPayDO = allByMemberIdAndMemberRoleId.get(0);
            } else {
                allInPayDO = allByMemberIdAndMemberRoleId.stream().filter(a -> a.getAllInMemberType().equals(2)).findFirst().orElse(null);
                if (Objects.isNull(allInPayDO) || !AllInPayEnterpriseStateEnum.SUCCESSFUL_REVIEW.getCode().equals(allInPayDO.getAccountStatus())) {
                    allInPayDO = allByMemberIdAndMemberRoleId.stream().filter(a -> a.getAllInMemberType().equals(3)).findFirst().orElse(null);
                }
            }

            if (Objects.isNull(allInPayDO)) {
                throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_GET_MEMBER_INFO_ERROR);
            }

            String bizUserId = allInPayDO.getBizUserId();
            AllInPayAttachDO payAttach = allInPayAttachRepository.findByBizUserId(bizUserId);
            //判断是否进行收银宝快捷支付
            if (AllInPayPayMethodEnum.QUICKPAY_VSP.getPayMethod().equals(vo.getPayMethodStr())) {
                if (AllInPayMemberTypeEnum.ENTERPRISE.getCode().equals(allInPayDO.getAllInMemberType())) {//企业会员
                    vo.setBankCardNo(payAttach.getAccountNo());
                } else {
                    vo.setBankCardNo(payAttach.getBankCardNo());
                }
            }
            Map<String, String> param = eAccountService.getMerchantId();
            vo.setVspCusId(param.get("merchantId"));
            String backUrl = getBackUrl(AllInPayNotifyConstant.AGENT_COLLECT_APPLY);
            String frontUrl = payGateWayConfig.getPlatformUrl() + "/orderAbility/purchaseOrder/readyPayOrder";
            openResponse = OrderServiceUtil.scanAgentCollectApply(vo.getBizOrderNo(), bizUserId,
                    new BigDecimal(String.valueOf(vo.getAmount())).multiply(new BigDecimal("100")).longValue(),
                    new BigDecimal(String.valueOf(vo.getFee())).multiply(new BigDecimal("100")).longValue(), vo.getValidateType().longValue(), frontUrl,
                    backUrl, vo.getPayMethodStr(), vo.getLimitPay(), vo.getVspCusId(), vo.getSubAppId(), vo.getAcct(),
                    vo.getTradeCode(), vo.getAllInPayType(), vo.getBankCardNo(), vo.getIndustryCode(), vo.getIndustryName(), attachJson, vo.getSource(), recieverList, vo.getCusIp());
            resp = JSON.parseObject(openResponse.getData());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 托管代收申请（标准版）(支付宝/微信PC端正扫,支付宝app(原生或者收银宝的),微信app(只有原生)) 失败,req: {}, errMsg: {}", JsonUtil.toJson(vo), e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
        if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
            ConsumeApplyResp respVO = new ConsumeApplyResp();
            respVO.setPayStatus(resp.getString("payStatus"));
            respVO.setPayFailMessage(resp.getString("payFailMessage"));
            respVO.setBizUserId(resp.getString("bizUserId"));
            respVO.setBizOrderNo(resp.getString("bizOrderNo"));
            respVO.setTradeNo(resp.getString("tradeNo"));
            respVO.setExtendInfo(resp.getString("extendInfo"));
            respVO.setPayInfo(resp.getString("payInfo"));
            respVO.setWeChatAPPInfo(resp.getString("weChatAPPInfo"));
            if (vo.getPayMethodStr().equals(AllInPayPayMethodEnum.WECHATPAY_MINIPROGRAM_CASHIER_VSP_ORG.getPayMethod())) {
                respVO.setPayInfo(resp.getString("miniprogramPayInfo_VSP"));
            }
            return respVO;
        } else {
            log.error("-----------------------请求通联支付-托管代收申请（标准版）(支付宝/微信PC端正扫,支付宝app(原生或者收银宝的),微信app(只有原生)) 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
            throw new BusinessException(openResponse.getSubMsg());
        }
    }

    /**
     * 通联支付-单笔托管代付（标准版）
     *
     * @param vo 请求对象
     * @return 操作结果
     */
    @Override
    public SignalAgentPayResp signalAgentPay(SignalAgentPayReq vo) {
        String cacheParams = redisUtils.stringGet(KEY, RedisConstant.REDIS_PAY_INDEX);
        Long memberId = vo.getMemberId();
        Long roleId = vo.getMemberRoleId();
        JSONObject cacheValue = JSON.parseObject(cacheParams);
        if (memberId != 1 && roleId != 1) {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_DELEGATE_SET.getCode().toString()));
        } else {
            vo.setAccountSetNo(cacheValue.getString(OrderPaymentParameterEnum.ALLIN_STANDARD_SET.getCode().toString()));
        }
        JSONArray collectPayList = new JSONArray();
        if (!CollectionUtils.isEmpty(vo.getCollectPayList())) {
            for (CollectPayReq collectPayVO : vo.getCollectPayList()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("bizOrderNo", collectPayVO.getBizOrderNo());
                jsonObject.put("amount", new BigDecimal(String.valueOf(collectPayVO.getAmount())).multiply(new BigDecimal("100")).longValue());
                collectPayList.add(jsonObject);
            }
        }
        JSONObject resp;
        try {

            String backUrl = getBackUrl(AllInPayNotifyConstant.SIGNAL_AGENT_PAY);
            String bizUserId = getBizUserId(memberId, roleId);
            OpenResponse openResponse = OrderServiceUtil.signalAgentPay(vo.getBizOrderNo(), collectPayList, bizUserId, vo.getAccountSetNo(), backUrl,
                    new BigDecimal(String.valueOf(vo.getAmount())).multiply(new BigDecimal("100")).longValue(),
                    new BigDecimal(String.valueOf(vo.getFee())).multiply(new BigDecimal("100")).longValue(), vo.getTradeCode());
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                SignalAgentPayResp respVO = new SignalAgentPayResp();
                respVO.setPayStatus(resp.getString("payStatus"));
                respVO.setPayFailMessage(resp.getString("payFailMessage"));
                respVO.setBizOrderNo(resp.getString("bizOrderNo"));
                respVO.setOrderNo(resp.getString("orderNo"));
                respVO.setPayWhereabouts(resp.getLong("payWhereabouts"));
                return respVO;
            } else {
                log.error("-----------------------请求通联支付-单笔托管代付（标准版）接口失败--------------:{}", JsonUtil.toJson(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 单笔托管代付（标准版） 失败,req: {}, errMsg: {}", JsonUtil.toJson(vo), e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-退款申请
     *
     * @param vo 请求对象
     * @return 操作结果
     */
    @Override
    public RefundResp refund(RefundReq vo) {
        String bizUserId = vo.getBuyerBizUserId();
        JSONObject resp;

        long amount = new BigDecimal(String.valueOf(vo.getAmount())).multiply(new BigDecimal("100")).longValue();
        boolean enoughedVSPFund = enoughVSPFund(amount);

        try {
            String backUrl = getBackUrl(AllInPayNotifyConstant.REFUND);//退款申请 统一回调地址
            OpenResponse openResponse = OrderServiceUtil.refund(vo.getBizOrderNo(), vo.getOriBizOrderNo(), bizUserId, vo.getRefundType(), vo.getRefundList(), backUrl, amount, vo.getCouponAmount(), vo.getFeeAmount(), vo.getExtendInfo(), enoughedVSPFund);
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                RefundResp respVO = new RefundResp();
                respVO.setPayStatus(resp.getString("payStatus"));
                respVO.setPayFailMessage(resp.getString("payFailMessage"));
                respVO.setBizOrderNo(resp.getString("bizOrderNo"));
                respVO.setOrderNo(resp.getString("orderNo"));
                respVO.setAmount(new BigDecimal(resp.getString("amount")).multiply(new BigDecimal("0.01")));
                respVO.setCouponAmount(new BigDecimal(resp.getString("couponAmount")).multiply(new BigDecimal("0.01")));
                respVO.setFeeAmount(new BigDecimal(resp.getString("feeAmount")).multiply(new BigDecimal("0.01")));
                respVO.setExtendInfo(resp.getString("extendInfo"));
                return respVO;
            } else {
                log.error("-----------------------请求通联支付-退款申请接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 退款申请 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 检测收银宝商户资金是否足够退款
     *
     * @param refundAmount 退款总金额
     */
    private boolean enoughVSPFund(long refundAmount) {
        Map<String, String> param = eAccountService.getMerchantId();
        String vspCusid = param.get("merchantId");
        try {
            OpenResponse queryVSPFundResp = MerchantServiceUtil.queryVSPFund(vspCusid);
            if (!AllInPayResultEnum.SUCCESS.getMsg().equals(queryVSPFundResp.getSubCode())) {
                log.error("-----------------------请求通联支付-收银宝商户资金查询 失败--------------:{}", JSONUtil.toJsonStr(queryVSPFundResp));
                throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
            }

            JSONObject queryVSPFundRespData = JSON.parseObject(queryVSPFundResp.getData());
            long balance = queryVSPFundRespData.getLongValue("balance");
            // 账户余额足够退款
            return balance > refundAmount;
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 收银宝商户资金查询 失败,vspCusid: {}, errMsg: {}", vspCusid, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-平台转账
     *
     * @param vo 请求参数
     * @return 操作结果
     */
    @Override
    public ApplicationTransferResp applicationTransfer(ApplicationTransferReq vo) {
        String cacheParams = redisUtils.stringGet(KEY, RedisConstant.REDIS_PAY_INDEX);
        JSONObject cacheResp = JSON.parseObject(cacheParams);
        vo.setSourceAccountSetNo(cacheResp.getString(OrderPaymentParameterEnum.ALLIN_STANDARD_SET.getCode().toString()));
        vo.setTargetAccountSetNo(cacheResp.getString(OrderPaymentParameterEnum.ALLIN_DELEGATE_SET.getCode().toString()));
        String bizUserId = getBizUserId(vo.getMemberId(), vo.getMemberRoleId());
        JSONObject resp;
        try {
            OpenResponse openResponse = OrderServiceUtil.applicationTransfer(vo.getBizTransferNo(), vo.getSourceAccountSetNo(), bizUserId, vo.getTargetAccountSetNo(),
                    new BigDecimal(String.valueOf(vo.getAmount())).multiply(new BigDecimal("100")).longValue(), vo.getExtendInfo());
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                ApplicationTransferResp respVO = new ApplicationTransferResp();
                respVO.setTransferNo(resp.getString("transferNo"));
                respVO.setBizTransferNo(resp.getString("bizTransferNo"));
                respVO.setAmount(new BigDecimal(resp.getString("amount")).multiply(new BigDecimal("0.01")));
                respVO.setExtendInfo(resp.getString("extendInfo"));
                return respVO;
            } else {
                log.error("-----------------------请求通联支付-平台转账失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 平台转账 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 对公银行列表
     *
     * @return 操作结果
     */
    @Override
    public List<AllInPayBankDO> getBankList() {
        return allInPayBankRepository.findAll();
    }

    /**
     * 确认支付（前台+短信验证码确认）
     * 收银宝网关支付时需要用到
     *
     * @return 跳转链接
     */
    @Override
    public String gateWayPay(Long memberId, Long memberRoleId, GateWayPayReq gateWayPayVO) {
        AllInPayDO allInPayDO;
        List<AllInPayDO> allByMemberIdAndMemberRoleId = allInPayRepository.findAllByMemberIdAndMemberRoleId(memberId, memberRoleId);
        if (allByMemberIdAndMemberRoleId.size() == 1) {
            allInPayDO = allByMemberIdAndMemberRoleId.get(0);
        } else {
            allInPayDO = allByMemberIdAndMemberRoleId.stream().filter(a -> a.getAllInMemberType().equals(2)).findFirst().orElse(null);
            if (Objects.isNull(allInPayDO) || !AllInPayEnterpriseStateEnum.SUCCESSFUL_REVIEW.getCode().equals(allInPayDO.getAccountStatus())) {
                allInPayDO = allByMemberIdAndMemberRoleId.stream().filter(a -> a.getAllInMemberType().equals(3)).findFirst().orElse(null);
            }
        }

        if (Objects.isNull(allInPayDO)) {
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_GET_MEMBER_INFO_ERROR);
        }

        String bizUserId = allInPayDO.getBizUserId();
        try {
            return OrderServiceUtil.gateWayPay(bizUserId, gateWayPayVO.getBizOrderNo(), gateWayPayVO.getVerificationCode(), gateWayPayVO.getConsumerIp());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 确认支付（前台+短信验证码确认） 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 通联支付-确认支付(后台+短信验证码确认)-重发支付短信验证码
     * 收银宝网关支付时需要用到
     *
     * @return 操作结果
     */
    @Override
    public ResendPaySMSRespResp reSendPaySMS(ResendPaySMSReq resendPaySMSVO) {
        JSONObject resp;
        try {
            OpenResponse openResponse = OrderServiceUtil.resendPaySMS(resendPaySMSVO.getBizOrderNo());
            resp = JSON.parseObject(openResponse.getData());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                ResendPaySMSRespResp vo = new ResendPaySMSRespResp();
                vo.setBizOrderNo(resp.getString("bizOrderNo"));
                vo.setOrderNo(resp.getString("orderNo"));
                vo.setPhone(resp.getString("phone"));
                return vo;
            } else {
                log.error("-----------------------请求通联支付-确认支付(后台+短信验证码确认)-重发支付短信验证码接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 确认支付(后台+短信验证码确认)-重发支付短信验证码 失败,req: {}, errMsg: {}", JsonUtil.toJson(resendPaySMSVO), e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 获取通联会员手机号码
     *
     * @param userInfoReqVO 会员信息
     * @return 手机号码
     */
    @Override
    public String getUserPhone(UserInfoReqReq userInfoReqVO) {
        String bizUserId = getBizUserId(userInfoReqVO.getMemberId(), userInfoReqVO.getMemberRoleId());
        AllInPayAttachDO allInPayAttachDO = Optional.ofNullable(allInPayAttachRepository.findByBizUserId(bizUserId)).orElseThrow(() -> new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_GET_MEMBER_INFO_ERROR));
        return allInPayAttachDO != null ? allInPayAttachDO.getPhone() : "";
    }

    @Override
    public String getBizUserId(Long memberId, Long memberRoleId) {
        String bizUserIdPrefix = allInPayProperties.getBizUserIdPrefix();
        String bizUserId = memberId + "__" + memberRoleId;
        if (memberId == 1 && memberRoleId == 1) {
            //如果是平台, 使用系统保留用户标识
            bizUserId = "#yunBizUserId_B2C#";
        }
        if (CharSequenceUtil.isNotBlank(bizUserIdPrefix)) {
            // 拼接前缀
            bizUserId = bizUserIdPrefix + bizUserId;
        }
        return bizUserId;
    }

    @Override
    @Transactional
    public RegisterCompanyMemberResp registerCompanyMember(UserLoginCacheDTO sysUser, RegisterCompanyMemberReq registerCompanyMemberReq) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());

        AllInPayAttachDO allInPayAttachDO = allInPayAttachRepository.findByBizUserId(bizUserId);
        if (!registerCompanyMemberReq.getIsReAuth() && Objects.nonNull(allInPayAttachDO) && LocalDateTime.now().isBefore(allInPayAttachDO.getRegInviteLinkEndTime())) {
            // 注册链接没有过期, 则直接返回
            return RegisterCompanyMemberResp.buildBy(allInPayAttachDO);
        }

        // 获取注册公司名称
        String companyName = AllInPayDM.getRegisterCompanyName(registerCompanyMemberReq, allInPayAttachDO);
        //后台通知回调地址
        String backUrl = getBackUrl(AllInPayNotifyConstant.REGISTER_COMPANY_MEMBER);

        try {

            // 调用通联接口
            OpenResponse openResponse = MemberServiceUtil.registerCompanyMember(
                    bizUserId, companyName, backUrl, registerCompanyMemberReq.getJumpPageType(), registerCompanyMemberReq.getJumpUrl()
            );

            RegisterCompanyMemberResp registerCompanyMemberResp = JsonUtil.toObj(openResponse.getData(), RegisterCompanyMemberResp.class);
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                // 更新数据到数据库
                if (Objects.isNull(registerCompanyMemberResp)) {
                    return registerCompanyMemberResp;
                }

                AllInPayDO allInPayDO = allInPayRepository.findByBizUserId(bizUserId);
                allInPayDO = Objects.isNull(allInPayDO) ? AllInPayDM.buildBaseAllInPayDO(sysUser, bizUserId, registerCompanyMemberResp.getUserId()) : allInPayDO;

                allInPayAttachDO = Objects.isNull(allInPayAttachDO) ? AllInPayDM.buildBaseAllInPayAttachDO(allInPayDO, bizUserId, registerCompanyMemberResp.getUserId()) : allInPayAttachDO;
                allInPayAttachDO.setCompanyName(companyName);
                allInPayAttachDO.setRegInviteLink(registerCompanyMemberResp.getRegInviteLink());
                allInPayAttachDO.setRegInviteLinkEndTime(registerCompanyMemberResp.getRegInviteLinkEndTime());

                allInPayRepository.saveAndFlush(allInPayDO);
                allInPayAttachRepository.saveAndFlush(allInPayAttachDO);
                return registerCompanyMemberResp;
            } else {
                log.error("-----------------------请求通联支付-企业会员开户H5 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 企业会员开户H5 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    public void memberRegAndBindPhoneApply(UserLoginCacheDTO sysUser, MemberRegAndBindPhoneApplyReq req) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            OpenResponse openResponse = MemberServiceUtil.memberRegAndBindPhoneApply(bizUserId, req.getPhone());
            if (!AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                log.error("-----------------------请求通联支付-注册并绑手机申请 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 注册并绑手机申请 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    @Transactional
    public void memberRegAndBindPhoneConfirm(UserLoginCacheDTO sysUser, MemberRegAndBindPhoneConfirmReq req) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            OpenResponse openResponse = MemberServiceUtil.memberRegAndBindPhoneConfirm(bizUserId, req.getPhone(), req.getVerificationCode());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                // 确认成功, 更新数据, 确认记录userId和phone
                MemberRegAndBindPhoneConfirmResp confirmResp = JsonUtil.toObj(openResponse.getData(), MemberRegAndBindPhoneConfirmResp.class);
                if (Objects.isNull(confirmResp)) {
                    log.error("-----------------------请求通联支付-注册并绑手机确认 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                    throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg() + " (响应结果不符合预期)");
                }

                AllInPayDO allInPayDO = AllInPayDM.buildBaseAllInPayDO(sysUser, bizUserId, confirmResp.getUserId());
                AllInPayAttachDO allInPayAttachDO = AllInPayDM.buildBaseAllInPayAttachDO(allInPayDO, bizUserId, confirmResp.getUserId());
                allInPayAttachDO.setPhone(confirmResp.getPhone());
                allInPayAttachDO.setIsPhoneChecked(Boolean.TRUE);

                allInPayRepository.saveAndFlush(allInPayDO);
                allInPayAttachRepository.saveAndFlush(allInPayAttachDO);
            } else {
                log.error("-----------------------请求通联支付-注册并绑手机确认 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 注册并绑手机确认 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    public String getHisOrderDetail(UserLoginCacheDTO sysUser, GetHisOrderDetailReq req) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            String backUrl = getBackUrl(AllInPayNotifyConstant.GET_HIS_ORDER_DETAIL);//后台通知回调地址
            // 调用通联接口
            OpenResponse openResponse = OrderServiceUtil.getHisOrderDetail(req.getBizOrderNo(), req.getBizOrderCreateDate(), backUrl);

            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                // 前端暂未用到的接口
                return openResponse.getData();
            } else {
                log.error("-----------------------请求通联支付-历史订单详细信息查询 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 历史订单详细信息查询 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    @Override
    public String queryBankCard(UserLoginCacheDTO sysUser, QueryBankCardReq req) {
        String bizUserId = getBizUserId(sysUser.getMemberId(), sysUser.getMemberRoleId());
        try {
            // 调用通联接口
            OpenResponse openResponse = MemberServiceUtil.queryBankCard(bizUserId, req.getCardNo());
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(openResponse.getSubCode())) {
                // 前端暂未用到的接口
                return openResponse.getData();
            } else {
                log.error("-----------------------请求通联支付-查询绑定银行卡 接口失败--------------:{}", JSONUtil.toJsonStr(openResponse));
                throw new BusinessException(Integer.parseInt(openResponse.getSubCode()), openResponse.getSubMsg());
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error(CharSequenceUtil.format("通联支付: 查询绑定银行卡 失败,bizUserId: {}, errMsg: {}", bizUserId, e.getMessage()), e);
            throw new BusinessException(ResponseCodeEnum.PAY_ALL_IN_PAY_ERROR);
        }
    }

    /**
     * 获取回调地址
     *
     * @param backPathSuffix 回调地址的后缀
     */
    public String getBackUrl(String backPathSuffix) {
        return payGateWayConfig.getNotifyUrl() + AllInPayConfig.backUrl + backPathSuffix;
    }

    public static void main(String[] args) {
        String str = "{\"userId\":\"4119c510-c518-457a-806c-7d68f832361b\",\"regInviteLink\":\"https://test.allinpay.com/wom/#/get?workTaskNo=360&timestamp=20240822160252&sign=db2ea47654e4dde43796412d650e8049\",\"regInviteLinkEndTime\":\"2024-08-25 16:02:52\",\"bizUserId\":\"bp_test_v2_7__3\"}";
        RegisterCompanyMemberResp registerCompanyMemberResp = JsonUtil.toObj(str, RegisterCompanyMemberResp.class);

        System.out.println(registerCompanyMemberResp);
    }

}
