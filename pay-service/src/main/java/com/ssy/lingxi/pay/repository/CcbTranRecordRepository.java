package com.ssy.lingxi.pay.repository;

import com.ssy.lingxi.pay.entity.do_.CcbTranRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface CcbTranRecordRepository extends JpaRepository<CcbTranRecordDO, Long>, JpaSpecificationExecutor<CcbTranRecordDO> {
    CcbTranRecordDO findFirstByOrderIdOrderByIdDesc(String orderId);
}
