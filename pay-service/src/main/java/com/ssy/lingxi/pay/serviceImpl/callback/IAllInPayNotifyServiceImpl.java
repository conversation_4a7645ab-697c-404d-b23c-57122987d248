package com.ssy.lingxi.pay.serviceImpl.callback;

import cn.hutool.core.text.CharSequenceUtil;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.pay.domain.AllInPayDM;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayAttachDO;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayDO;
import com.ssy.lingxi.pay.enums.allInPay.AllInPayAttachStepEnum;
import com.ssy.lingxi.pay.handler.convert.AllInPayAttachDOConvert;
import com.ssy.lingxi.pay.model.req.allInPay.AllInPayNotifyCommonReq;
import com.ssy.lingxi.pay.model.req.allInPay.bizContent.GetHisOrderDetailBizContentReq;
import com.ssy.lingxi.pay.model.req.allInPay.bizContent.RegisterCompanyMemberResultBizContentReq;
import com.ssy.lingxi.pay.model.req.allInPay.bizContent.SignAcctProtocolBizContentReq;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayAttachRepository;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayRepository;
import com.ssy.lingxi.pay.service.callback.IAllInPayNotifyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

/**
 * 通联支付回调接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IAllInPayNotifyServiceImpl implements IAllInPayNotifyService {

    private final AllInPayRepository allInPayRepository;
    private final AllInPayAttachRepository allInPayAttachRepository;

    @Override
    @Transactional
    public void registerCompanyMember(AllInPayNotifyCommonReq commonReq, RegisterCompanyMemberResultBizContentReq bizContentReq) {
        if (Objects.isNull(bizContentReq)) {
            log.error("通联支付-企业会员开户H5: resultReq: {} -- bizContent is null", JsonUtil.toJson(commonReq));
            return;
        }
        String bizUserId = bizContentReq.getBizUserId();
        if (CharSequenceUtil.isBlank(bizUserId)) {
            log.error("通联支付-企业会员开户H5: resultReq: {} -- bizUserId is null", JsonUtil.toJson(commonReq));
            return;
        }

        AllInPayAttachDO allInPayAttachDO = Optional.ofNullable(allInPayAttachRepository.findByBizUserId(bizUserId)).orElseGet(AllInPayAttachDO::new);
        allInPayAttachDO.setBizUserId(bizUserId);

        if (Objects.nonNull(allInPayAttachDO.getNotifyTime()) && allInPayAttachDO.getNotifyTime().isAfter(commonReq.getNotifyTime())) {
            // 旧通知, 忽略
            log.error("通联支付-企业会员开户H5: allInPayAttachDO.getNotifyTime(): {}, resultReq: {} -- 旧通知, 忽略", allInPayAttachDO.getNotifyTime(), JsonUtil.toJson(commonReq));
            return;
        }

        // 更新数据
        AllInPayAttachDOConvert.INSTANCE.updateDO(bizContentReq, allInPayAttachDO);
        allInPayAttachDO.setNotifyTime(commonReq.getNotifyTime());
        allInPayAttachDO.setStep(AllInPayDM.getStep(bizContentReq));
        allInPayAttachRepository.saveAndFlush(allInPayAttachDO);

        AllInPayDO allInPayDO = Optional.ofNullable(allInPayRepository.findByBizUserId(bizUserId)).orElseGet(AllInPayDO::new);
        allInPayDO.setBizUserId(bizUserId);
        allInPayDO.setAccountStatus(bizContentReq.getStatus());
        allInPayRepository.saveAndFlush(allInPayDO);

    }

    @Override
    public void signContract(AllInPayNotifyCommonReq commonReq, SignAcctProtocolBizContentReq bizContentReq) {
        if (Objects.isNull(commonReq)) {
            log.error("异常: 通联支付-会员电子协议签约 返回结果-> 请求参数为空");
            return;
        }
        if (Objects.isNull(bizContentReq)) {
            log.error("通联支付-会员电子协议签约: resultReq: {} -- bizContent is null", JsonUtil.toJson(commonReq));
            return;
        }
        String bizUserId = bizContentReq.getBizUserId();
        if (CharSequenceUtil.isBlank(bizUserId)) {
            log.error("通联支付-会员电子协议签约: resultReq: {} -- bizUserId is null", JsonUtil.toJson(commonReq));
            return;
        }

        AllInPayAttachDO allInPayAttachDO = Optional.ofNullable(allInPayAttachRepository.findByBizUserId(bizUserId)).orElseGet(AllInPayAttachDO::new);
        allInPayAttachDO.setBizUserId(bizUserId);

        if (Objects.nonNull(allInPayAttachDO.getNotifyTime()) && allInPayAttachDO.getNotifyTime().isAfter(commonReq.getNotifyTime())) {
            // 旧通知, 忽略
            log.error("通联支付-会员电子协议签约: allInPayAttachDO.getNotifyTime(): {}, resultReq: {} -- 旧通知, 忽略", allInPayAttachDO.getNotifyTime(), JsonUtil.toJson(commonReq));
            return;
        }

        if (!CharSequenceUtil.equalsAnyIgnoreCase("ok", bizContentReq.getResult())) {
            // 签约不成功
            log.error("通联支付-会员电子协议签约: allInPayAttachDO.getNotifyTime(): {}, resultReq: {} -- 签约不成功", allInPayAttachDO.getNotifyTime(), JsonUtil.toJson(commonReq));
            return;
        }

        allInPayAttachDO.setBizUserId(bizUserId);
        allInPayAttachDO.setAcctProtocolNo(bizContentReq.getAcctProtocolNo());
        allInPayAttachDO.setStep(AllInPayAttachStepEnum.SIGN_CONTRACT_COMPLETE.getStep());
        allInPayAttachRepository.saveAndFlush(allInPayAttachDO);
    }

    @Override
    public void getHisOrderDetail(AllInPayNotifyCommonReq commonReq, GetHisOrderDetailBizContentReq bizContentReq) {
        // 暂时没有需求要做

    }

}
