package com.ssy.lingxi.pay.controller.callback;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.pay.service.ICcbPayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 建行支付回调统一入口
 * @Author: Huang<PERSON>ie<PERSON>hou
 * @Date: 2021/12/21 10:11
 * @Version: 2.0.0
 * @ignore 不需要提交到Yapi
 **/
@RestController
@RequestMapping(ServiceModuleConstant.PAY_PATH_PREFIX + "/ccb")
public class CcbPayController {

    private static final Logger logger = LoggerFactory.getLogger(CcbPayController.class);

    @Resource
    private ICcbPayService iCcbPayService;

    /**
     * 建行b2b支付回调
     * @param request  请求参数
     * @param response 响应
     */
    @RequestMapping(value = "/b2bPay/notify")
    public void b2bPayNotify(HttpServletRequest request, HttpServletResponse response){
        logger.info("------------建行b2b支付回调通知--------------");
        //读取参数
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }
        logger.info("回调参数:{}", params);
        try{
            //进行验签及业务逻辑处理
            boolean flag = iCcbPayService.b2bPayNotify(params);
            //验签成功后，返回success标识字符
            if (flag) {
                response.setContentType("text/html;charset=" + "GBK");
                response.setCharacterEncoding("GBK");
                response.getWriter().write("success");
                response.getWriter().flush();
                response.getWriter().close();
            }
        }catch (Exception e){
            logger.error(e.getMessage());
        }
    }

    /**
     * 建行数字人民币支付回调(服务器端)
     * @param request  请求参数
     * @param response 响应
     */
    @PostMapping("/digitalPay/notify")
    public void digitalPayNotify(HttpServletRequest request, HttpServletResponse response){
        logger.info("------------建行数字人民币支付回调通知(服务器端)--------------");
        //读取参数
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }
        logger.info("回调参数:{}", params);
        try {
            //进行验签及业务逻辑处理
            boolean flag = iCcbPayService.digitalPayNotify(params);
            //验签成功后，返回success标识字符
            if (flag) {
                response.setContentType("text/html;charset=" + "UTF-8");
                response.setCharacterEncoding("UTF-8");
                response.getWriter().write("success");
                response.getWriter().flush();
                response.getWriter().close();
            }
        }catch (Exception e){
            logger.error(e.getMessage());
        }
    }

//    /**
//     * 建行数字人民币支付回调(页面端)
//     * @param request  请求参数
//     * @param response 响应
//     */
//    @GetMapping("/digitalPay/notify/html")
//    public void digitalPayNotifyHtml(HttpServletRequest request, HttpServletResponse response){
//        logger.info("------------建行数字人民币支付回调通知(页面端)--------------");
//        //读取参数
//        Map<String, String> params = new HashMap<>();
//        Map<String, String[]> requestParams = request.getParameterMap();
//        for (String name : requestParams.keySet()) {
//            String[] values = requestParams.get(name);
//            String valueStr = "";
//            for (int i = 0; i < values.length; i++) {
//                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
//            }
//            params.put(name, valueStr);
//        }
//        logger.info("回调参数:{}", params.toString());
//        //通知前端页面
//        String orderCode = params.get("ORDERID");
//        if(StringUtils.isNotEmpty(orderCode)){
//            WebSocketController.sendMessage(orderCode);
//        }
//    }

}
