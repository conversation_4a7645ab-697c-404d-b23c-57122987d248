package com.ssy.lingxi.pay.repository.assetAccount;

import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountStatusRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 会员资金账户审核记录
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
@Repository
public interface AccountStatusRecordRepository extends JpaRepository<AccountStatusRecordDO,Long>, JpaSpecificationExecutor<AccountStatusRecordDO> {
    List<AccountStatusRecordDO> findByMemberAssetAccountIdOrderByCreateTimeAsc(Long memberAssetAccountId);
}
