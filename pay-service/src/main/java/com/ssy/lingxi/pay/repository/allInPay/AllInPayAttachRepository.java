package com.ssy.lingxi.pay.repository.allInPay;

import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayAttachDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/29
 */
@Repository
public interface AllInPayAttachRepository extends JpaRepository<AllInPayAttachDO,Long>, JpaSpecificationExecutor<AllInPayAttachDO> {
    AllInPayAttachDO findByBizUserId(String bizUserId);

    AllInPayAttachDO findByBizUserIdAndAccountNo(String bizUserId, String accountNo);

    AllInPayAttachDO findFirstByBizUserId(String bizUserId);

    List<AllInPayAttachDO> findByBizUserIdIn(List<String> bizUserIdList);

}
