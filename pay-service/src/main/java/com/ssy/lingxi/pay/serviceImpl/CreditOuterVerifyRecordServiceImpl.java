package com.ssy.lingxi.pay.serviceImpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.ssy.lingxi.pay.entity.do_.CreditOuterVerifyRecordDO;
import com.ssy.lingxi.pay.model.resp.CreditOuterVerifyRecordResp;
import com.ssy.lingxi.pay.repository.CreditOuterVerifyRecordRepository;
import com.ssy.lingxi.pay.service.ICreditOuterVerifyRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 授信外部审核记录接口实现
 * <AUTHOR>
 * @since 2020/8/18
 * @version 2.0.0
 */
@Service
public class CreditOuterVerifyRecordServiceImpl implements ICreditOuterVerifyRecordService {

    @Resource
    private CreditOuterVerifyRecordRepository recordRepository;

    /**
     * 获取审核历史记录
     * <AUTHOR>
     * @since 2020/8/20
     * @param applyId:
     **/
    @Override
    public List<CreditOuterVerifyRecordResp> getHistoryList(Long applyId) {

        // step 1: 查询数据
        List<CreditOuterVerifyRecordDO> recordDOList = recordRepository.findByApplyIdOrderByOperateTimeAsc(applyId);
        if(CollUtil.isEmpty(recordDOList)){
            return new ArrayList<>();
        }
        // step 2: 组装返回数据
        return recordDOList.stream().map(r -> {
            CreditOuterVerifyRecordResp queryVO = new CreditOuterVerifyRecordResp();
            queryVO.setRoleName(r.getRoleName());
            queryVO.setStatus(r.getStatus());
            queryVO.setOperate(r.getOperate());
            queryVO.setOperateTime(DateUtil.format(DateUtil.date(r.getOperateTime()), "yyyy-MM-dd HH:mm"));
            queryVO.setOpinion(r.getOpinion());

            return queryVO;
        }).collect(Collectors.toList());

    }
}
