package com.ssy.lingxi.pay.repository.eAccount;

import com.ssy.lingxi.pay.entity.do_.eAccount.EAccountStatusRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * e账户审核记录
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/12/2
 */
@Repository
public interface EAccountStatusRecordRepository extends JpaRepository<EAccountStatusRecordDO,Long>, JpaSpecificationExecutor<EAccountStatusRecordDO> {
    List<EAccountStatusRecordDO> findByAllInPayIdOrderByCreateTimeAsc(Long allInPayId);
}
