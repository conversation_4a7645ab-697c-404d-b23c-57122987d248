package com.ssy.lingxi.pay.controller.mobile;

import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.pay.model.dto.UrlLinkDTO;
import com.ssy.lingxi.pay.model.req.AccessTokenReq;
import com.ssy.lingxi.pay.model.req.JsApiReq;
import com.ssy.lingxi.pay.model.resp.AccessTokenResp;
import com.ssy.lingxi.pay.model.resp.JsApiResultResp;
import com.ssy.lingxi.pay.model.resp.JsApiTicketResp;
import com.ssy.lingxi.pay.service.IWeChatPayService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 微信支付相关接口
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/3/17
 */
@RestController
@RequestMapping(ServiceModuleConstant.PAY_PATH_PREFIX + "/weChat/mobile")
public class MobileWeChatFeignController {

    @Resource
    private IWeChatPayService weChatPayService;

    /**
     * 获取微信AccessToken
     *
     * @param accessTokenReq 请求参数
     * @return  操作结果
     */
    @GetMapping("/getAccessToken")
    public WrapperResp<AccessTokenResp> getAccessToken(AccessTokenReq accessTokenReq) {
        AccessTokenResp accessTokenResp = weChatPayService.getWeChatAccessToken(accessTokenReq);
        return WrapperUtil.success(accessTokenResp);
    }

    /**
     * 获取微信JsApiTicket
     *
     * @param accessTokenReq 请求参数
     * @return 操作结果
     */
    @GetMapping("/getJsApiTicket")
    public WrapperResp<JsApiTicketResp> getJsApiTicket(AccessTokenReq accessTokenReq) {
        JsApiTicketResp jsApiTicketResp = weChatPayService.getJsApiTicket(accessTokenReq);
        return WrapperUtil.success(jsApiTicketResp);
    }

    /**
     * 获取JS-SDK签名
     *
     * @param jsApiRequest 请求参数
     * @return 操作结果
     */
    @GetMapping("/getSignature")
    public WrapperResp<JsApiResultResp> getJsApiResult(JsApiReq jsApiRequest) {
        JsApiResultResp jsApiResultResp = weChatPayService.getJsApiResult(jsApiRequest);
        return WrapperUtil.success(jsApiResultResp);
    }


    /**
     * 先获取微信AccessToken,根据AccessToken获取url_link
     *
     * @return url_link
     */
    @PostMapping("/getUrlLink")
    public WrapperResp<String> getUrlLink(@RequestBody UrlLinkDTO urlLinkDTO) {
        return WrapperUtil.success(weChatPayService.getUrlLink(urlLinkDTO));
    }

}
