package com.ssy.lingxi.pay.serviceImpl;

import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.*;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.RandomNumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPaymentParameterEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayChannelFeignReq;
import com.ssy.lingxi.order.api.model.req.OrderPayParameterFeignReq;
import com.ssy.lingxi.order.api.model.resp.PaymentParameterFeignDetailResp;
import com.ssy.lingxi.pay.api.model.req.aliPay.*;
import com.ssy.lingxi.pay.config.PayGateWayConfig;
import com.ssy.lingxi.pay.enums.AliPayResultEnum;
import com.ssy.lingxi.pay.enums.PayTypeEnum;
import com.ssy.lingxi.pay.model.dto.AliPayAttachDTO;
import com.ssy.lingxi.pay.model.resp.AliPayResultResp;
import com.ssy.lingxi.pay.service.IAliPayService;
import com.ssy.lingxi.pay.service.IPayCacheService;
import com.ssy.lingxi.pay.util.AliPayUtil;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 支付宝支付实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/2
 */
@Service
public class AliPayServiceImpl implements IAliPayService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IOrderProcessFeign orderFeignService;

    @Resource
    private IPayCacheService payCacheService;
    @Resource
    private PayGateWayConfig payGateWayConfig;

    /**
     * 扫码支付
     *
     * @param aliPayComputerReq 接口参数
     * @param request               Http请求
     * @return 支付结果
     */
    @Override
    public AliPayResultResp pay(AliPayComputerReq aliPayComputerReq, HttpServletRequest request) {
        //从订单服务查询支付参数
        if (StringUtils.isAnyEmpty(aliPayComputerReq.getAppId(), aliPayComputerReq.getAlipayPublicKey(), aliPayComputerReq.getAppPrivateKey())) {
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(aliPayComputerReq.getMemberId());
            feignVO.setRoleId(aliPayComputerReq.getMemberRoleId());
            feignVO.setPayChannel(OrderPayChannelEnum.ALIPAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new AliPayResultResp(false, String.valueOf(parameterResult.getCode()), parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new AliPayResultResp(false, String.valueOf(ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getCode()), ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            parameterResult.getData().getParameters().forEach(p -> {
                if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_ID)) {
                    aliPayComputerReq.setAppId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY)) {
                    aliPayComputerReq.setAlipayPublicKey(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PRIVATE_KEY)) {
                    aliPayComputerReq.setAppPrivateKey(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_THREAD_APP_AUTH_TOKEN)) {
                    aliPayComputerReq.setAppAuthToken(p.getValue());
                }
            });
        }
        //生成用于缓存Key的随机字符串，并作为透传参数传递给支付宝支付，在返回时从缓存中查询秘钥信息
        if (StringUtils.isEmpty(aliPayComputerReq.getNonce())) {
            aliPayComputerReq.setNonce(RandomNumberUtil.randomUniqueNumber());
        }
        //缓存支付参数
        payCacheService.cacheAliPayParameters(aliPayComputerReq.getMemberId(), aliPayComputerReq.getMemberRoleId(), aliPayComputerReq.getPayType(), aliPayComputerReq.getNonce(), aliPayComputerReq.getAppId(), aliPayComputerReq.getAlipayPublicKey(), aliPayComputerReq.getAppPrivateKey());

        //封装支付宝附加数据
        //由于支付宝附加信息最长128字符，所以改为用ObjectMapper进行序列化，同时AttachInfo的每个字段增加了@JsonProperty注解
        //这样可以简短附加数据的长度
        AliPayAttachDTO aliPayAttachDTO = new AliPayAttachDTO();
        aliPayAttachDTO.setPayType(aliPayComputerReq.getPayType());
        aliPayAttachDTO.setMemberId(aliPayComputerReq.getMemberId());
        aliPayAttachDTO.setMemberRoleId(aliPayComputerReq.getMemberRoleId());
        aliPayAttachDTO.setAttach(aliPayComputerReq.getBody());
        aliPayAttachDTO.setServiceType(aliPayComputerReq.getServiceType());
        aliPayAttachDTO.setNonce(aliPayComputerReq.getNonce());
        String attachJson = SerializeUtil.serialize(aliPayAttachDTO);
        try {
            //参数配置
            AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setServerUrl(AliPayUtil.gatewayUrl);
            alipayConfig.setAppId(aliPayComputerReq.getAppId());
            alipayConfig.setPrivateKey(aliPayComputerReq.getAppPrivateKey());
            alipayConfig.setAlipayPublicKey(aliPayComputerReq.getAlipayPublicKey());
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            //请求参数
            AlipayTradePagePayRequest alipayTradePagePayRequest = new AlipayTradePagePayRequest();
            //支付宝第三方应用调用时使用
            if (aliPayComputerReq.getPayType().equals(PayTypeEnum.MEMBER.getCode())) {
                alipayTradePagePayRequest.putOtherTextParam("app_auth_token", aliPayComputerReq.getAppAuthToken());
            }
            //请求实体
            AlipayTradePagePayModel model = new AlipayTradePagePayModel();
            model.setOutTradeNo(aliPayComputerReq.getOrderCode());   //商户订单号
            model.setTotalAmount(String.valueOf(aliPayComputerReq.getPayMoney()));//订单总金额
            model.setSubject(aliPayComputerReq.getRemark());         //订单标题
            model.setBody(attachJson);                                   //用作回调校验
            model.setProductCode(AliPayUtil.productCode);         //产品码:当面支付
            model.setQrPayMode("0");                                     //PC扫码支付的方式
            alipayTradePagePayRequest.setBizModel(model);
            alipayTradePagePayRequest.setNotifyUrl(payGateWayConfig.getNotifyUrl() + AliPayUtil.notifyUrl);//回调地址
            AlipayTradePagePayResponse response = alipayClient.pageExecute(alipayTradePagePayRequest);
            if (StringUtil.isNotEmpty(response.getBody())) {
                return new AliPayResultResp(true, response.getCode(), response.getBody());
            } else {
                return new AliPayResultResp(false, response.getCode(), response.getSubMsg());
            }
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.ALIPAY_FAILED, e.getMessage());
        }
    }

    /**
     * 支付宝app支付
     *
     * @param aliPayComputerReq 请求参数
     * @param request               HttpServletRequest
     * @return 操作结果
     */
    @Override
    public AliPayResultResp appPay(AliPayComputerReq aliPayComputerReq, HttpServletRequest request) {
        //从订单服务查询支付参数
        if (StringUtils.isAnyEmpty(aliPayComputerReq.getAppId(), aliPayComputerReq.getAlipayPublicKey(), aliPayComputerReq.getAppPrivateKey())) {
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(aliPayComputerReq.getMemberId());
            feignVO.setRoleId(aliPayComputerReq.getMemberRoleId());
            feignVO.setPayChannel(OrderPayChannelEnum.ALIPAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new AliPayResultResp(false, String.valueOf(parameterResult.getCode()), parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new AliPayResultResp(false, String.valueOf(ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getCode()), ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            parameterResult.getData().getParameters().forEach(p -> {
                if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_PAY_APP_ID)) {
                    aliPayComputerReq.setAppId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_PAY_PUBLIC_KEY)) {
                    aliPayComputerReq.setAlipayPublicKey(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_PAY_PRIVATE_KEY)) {
                    aliPayComputerReq.setAppPrivateKey(p.getValue());
                }
            });
        }

        //生成用于缓存Key的随机字符串，并作为透传参数传递给支付宝支付，在返回时从缓存中查询秘钥信息
        if (StringUtils.isEmpty(aliPayComputerReq.getNonce())) {
            aliPayComputerReq.setNonce(RandomNumberUtil.randomUniqueNumber());
        }
        //缓存支付参数
        payCacheService.cacheAliPayParameters(aliPayComputerReq.getMemberId(), aliPayComputerReq.getMemberRoleId(), aliPayComputerReq.getPayType(), aliPayComputerReq.getNonce(), aliPayComputerReq.getAppId(), aliPayComputerReq.getAlipayPublicKey(), aliPayComputerReq.getAppPrivateKey());

        //封装支付宝附加数据
        //由于支付宝附加信息最长128字符，所以改为用ObjectMapper进行序列化，同时AttachInfo的每个字段增加了@JsonProperty注解
        //这样可以简短附加数据的长度
        AliPayAttachDTO aliPayAttachDTO = new AliPayAttachDTO();
        aliPayAttachDTO.setPayType(aliPayComputerReq.getPayType());
        aliPayAttachDTO.setMemberId(aliPayComputerReq.getMemberId());
        aliPayAttachDTO.setMemberRoleId(aliPayComputerReq.getMemberRoleId());
        aliPayAttachDTO.setAttach(aliPayComputerReq.getBody());
        aliPayAttachDTO.setServiceType(aliPayComputerReq.getServiceType());
        aliPayAttachDTO.setNonce(aliPayComputerReq.getNonce());
        String attachJson = SerializeUtil.serialize(aliPayAttachDTO);
        try {
            //公共参数配置
            AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setServerUrl(AliPayUtil.gatewayUrl);
            alipayConfig.setAppId(aliPayComputerReq.getAppId());
            alipayConfig.setPrivateKey(aliPayComputerReq.getAppPrivateKey());
            alipayConfig.setAlipayPublicKey(aliPayComputerReq.getAlipayPublicKey());

            //初始化请求参数实体
            AlipayTradeAppPayRequest appPayRequest = new AlipayTradeAppPayRequest();
            //设置回调Url
            appPayRequest.setNotifyUrl(payGateWayConfig.getNotifyUrl() + AliPayUtil.notifyUrl);
            //支付宝第三方应用调用时使用
            if (aliPayComputerReq.getPayType().equals(PayTypeEnum.MEMBER.getCode())) {
                appPayRequest.putOtherTextParam("app_auth_token", aliPayComputerReq.getAppAuthToken());
            }

            //请求参数
            Map<String, Object> bizContentMap = new HashMap<>();
            bizContentMap.put("out_trade_no", aliPayComputerReq.getOrderCode());  //商户订单号
            bizContentMap.put("total_amount", aliPayComputerReq.getPayMoney());   //订单总金额
            bizContentMap.put("subject", aliPayComputerReq.getRemark());          //订单标题
            bizContentMap.put("body", attachJson);                                    //回调信息透传，用作回调校验

            //将请求参数转为Json字符串
            String bizContent = SerializeUtil.serialize(bizContentMap);

            //设置请求参数
            appPayRequest.setBizContent(bizContent);

            //发起支付请求，获得App端调起支付需要的参数
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            AlipayTradeAppPayResponse response = alipayClient.sdkExecute(appPayRequest);

            if(response.isSuccess()){
                log.info("支付宝App支付请求成功，返回 => " + SerializeUtil.serialize(response));
                return new AliPayResultResp(true, response.getCode(), response.getBody());
            } else {
                log.info("支付宝App支付请求失败，返回 => " + SerializeUtil.serialize(response));
                return new AliPayResultResp(false, response.getCode(), response.getSubMsg());
            }
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.ALIPAY_FAILED, e.getMessage());
        }
    }

//    /**
//     * 支付宝app支付 - v2版本的接口
//     * 支付宝app支付接口调整，v2版本接口对于新申请的支付宝账号已经不能用了，要用上面的新接口
//     *
//     * @param aliPayComputerRequest 请求参数
//     * @param request               HttpServletRequest
//     * @return 操作结果
//     */
//    @Override
//    public AliPayResult appPay(AliPayComputerRequest aliPayComputerRequest, HttpServletRequest request) {
//        //从订单服务查询支付参数
//        if (StringUtils.isAnyEmpty(aliPayComputerRequest.getAppId(), aliPayComputerRequest.getAlipayPublicKey(), aliPayComputerRequest.getAppPrivateKey())) {
//            OrderPayParameterFeignVO feignVO = new OrderPayParameterFeignVO();
//            feignVO.setMemberId(aliPayComputerRequest.getMemberId());
//            feignVO.setRoleId(aliPayComputerRequest.getMemberRoleId());
//            feignVO.setPayChannel(OrderPayChannelEnum.ALIPAY);
//            Wrapper<PaymentParameterFeignDetailVO> parameterResult = orderFeignService.findPaymentParameters(feignVO);
//            if (parameterResult.getCode() != ResponseCode.SUCCESS.getCode()) {
//                return new AliPayResult(false, String.valueOf(parameterResult.getCode()), parameterResult.getNameByCode());
//            }
//
//            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
//                return new AliPayResult(false, String.valueOf(ResponseCode.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getCode()), ResponseCode.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getNameByCode());
//            }
//
//            parameterResult.getData().getParameters().forEach(p -> {
//                if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_ID)) {
//                    aliPayComputerRequest.setAppId(p.getValue());
//                } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY)) {
//                    aliPayComputerRequest.setAlipayPublicKey(p.getValue());
//                } else if (p.getParameter().equals(OrderPaymentParameterEnum.APP_ALIPAY_PRIVATE_KEY)) {
//                    aliPayComputerRequest.setAppPrivateKey(p.getValue());
//                }
//            });
//        }
//
//        //生成用于缓存Key的随机字符串，并作为透传参数传递给支付宝支付，在返回时从缓存中查询秘钥信息
//        if (StringUtils.isEmpty(aliPayComputerRequest.getNonce())) {
//            aliPayComputerRequest.setNonce(payCacheService.randomString());
//        }
//        //缓存支付参数
//        payCacheService.cacheAliPayParameters(aliPayComputerRequest.getMemberId(), aliPayComputerRequest.getMemberRoleId(), aliPayComputerRequest.getPayType(), aliPayComputerRequest.getNonce(), aliPayComputerRequest.getAppId(), aliPayComputerRequest.getAlipayPublicKey(), aliPayComputerRequest.getAppPrivateKey());
//
//        //封装支付宝附加数据
//        //由于支付宝附加信息最长128字符，所以改为用ObjectMapper进行序列化，同时AttachInfo的每个字段增加了@JsonProperty注解
//        //这样可以简短附加数据的长度
//        AliPayAttachInfo aliPayAttachInfo = new AliPayAttachInfo();
//        aliPayAttachInfo.setPayType(aliPayComputerRequest.getPayType());
//        aliPayAttachInfo.setMemberId(aliPayComputerRequest.getMemberId());
//        aliPayAttachInfo.setMemberRoleId(aliPayComputerRequest.getMemberRoleId());
//        aliPayAttachInfo.setAttach(aliPayComputerRequest.getBody());
//        aliPayAttachInfo.setServiceType(aliPayComputerRequest.getServiceType());
//        aliPayAttachInfo.setNonce(aliPayComputerRequest.getNonce());
//        String attachJson = payCacheService.serializeObject(aliPayAttachInfo);
//        try {
//            //参数配置
//            AlipayConfig alipayConfig = new AlipayConfig();
//            alipayConfig.setServerUrl(AliPayUtil.gatewayUrl);
//            alipayConfig.setAppId(aliPayComputerRequest.getAppId());
//            alipayConfig.setPrivateKey(aliPayComputerRequest.getAppPrivateKey());
//            alipayConfig.setAlipayPublicKey(aliPayComputerRequest.getAlipayPublicKey());
//            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
//            //请求参数
//            AlipayTradeAppPayRequest alipayTradeAppPayRequest = new AlipayTradeAppPayRequest();
//            //支付宝第三方应用调用时使用
//            if (aliPayComputerRequest.getPayType().equals(PayTypeEnum.Member.getCode())) {
//                alipayTradeAppPayRequest.putOtherTextParam("app_auth_token", aliPayComputerRequest.getAppAuthToken());
//            }
//            //请求实体
//            AlipayTradePagePayModel model = new AlipayTradePagePayModel();
//            model.setOutTradeNo(aliPayComputerRequest.getOrderCode());   //商户订单号
//            model.setTotalAmount(aliPayComputerRequest.getPayMoney() + "");//订单总金额
//            model.setSubject(aliPayComputerRequest.getRemark());         //订单标题
//            model.setBody(attachJson);                                   //用作回调校验
//            alipayTradeAppPayRequest.setBizModel(model);
//            alipayTradeAppPayRequest.setNotifyUrl(payGateWayConfig.getNotifyUrl() + AliPayUtil.notifyUrl);//回调地址
//            AlipayTradeAppPayResponse response = alipayClient.pageExecute(alipayTradeAppPayRequest, "GET");//返回url
//            if (StringUtil.isNotEmpty(response.getBody())) {
//                return new AliPayResult(true, response.getCode(), response.getBody());
//            } else {
//                return new AliPayResult(false, response.getCode(), response.getSubMsg());
//            }
//        } catch (Exception e) {
//            throw new BusinessException(ResponseCode.ALIPAY_FAILED, e.getNameByCode());
//        }
//    }

    /**
     * 支付宝手机网站支付
     *
     * @param aliPayComputerReq 接口参数
     * @param request               request请求体
     * @return 操作结果
     */
    @Override
    public AliPayResultResp jsApiPay(AliPayComputerReq aliPayComputerReq, HttpServletRequest request) {
        //从订单服务查询支付参数
        if (StringUtils.isAnyEmpty(aliPayComputerReq.getAppId(), aliPayComputerReq.getAlipayPublicKey(), aliPayComputerReq.getAppPrivateKey())) {
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(aliPayComputerReq.getMemberId());
            feignVO.setRoleId(aliPayComputerReq.getMemberRoleId());
            feignVO.setPayChannel(OrderPayChannelEnum.ALIPAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new AliPayResultResp(false, String.valueOf(parameterResult.getCode()), parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new AliPayResultResp(false, String.valueOf(ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getCode()), ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            parameterResult.getData().getParameters().forEach(p -> {
                if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_ID)) {
                    aliPayComputerReq.setAppId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY)) {
                    aliPayComputerReq.setAlipayPublicKey(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PRIVATE_KEY)) {
                    aliPayComputerReq.setAppPrivateKey(p.getValue());
                }
            });
        }
        //生成用于缓存Key的随机字符串，并作为透传参数传递给支付宝支付，在返回时从缓存中查询秘钥信息
        if (StringUtils.isEmpty(aliPayComputerReq.getNonce())) {
            aliPayComputerReq.setNonce(RandomNumberUtil.randomUniqueNumber());
        }
        //缓存支付参数
        payCacheService.cacheAliPayParameters(aliPayComputerReq.getMemberId(), aliPayComputerReq.getMemberRoleId(), aliPayComputerReq.getPayType(), aliPayComputerReq.getNonce(), aliPayComputerReq.getAppId(), aliPayComputerReq.getAlipayPublicKey(), aliPayComputerReq.getAppPrivateKey());

        //封装支付宝附加数据
        //由于支付宝附加信息最长128字符，所以改为用ObjectMapper进行序列化，同时AttachInfo的每个字段增加了@JsonProperty注解
        //这样可以简短附加数据的长度
        AliPayAttachDTO aliPayAttachDTO = new AliPayAttachDTO();
        aliPayAttachDTO.setPayType(aliPayComputerReq.getPayType());
        aliPayAttachDTO.setMemberId(aliPayComputerReq.getMemberId());
        aliPayAttachDTO.setMemberRoleId(aliPayComputerReq.getMemberRoleId());
        aliPayAttachDTO.setAttach(aliPayComputerReq.getBody());
        aliPayAttachDTO.setServiceType(aliPayComputerReq.getServiceType());
        aliPayAttachDTO.setNonce(aliPayComputerReq.getNonce());
        String attachJson = SerializeUtil.serialize(aliPayAttachDTO);
        try {
            //参数配置
            AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setServerUrl(AliPayUtil.gatewayUrl);
            alipayConfig.setAppId(aliPayComputerReq.getAppId());
            alipayConfig.setPrivateKey(aliPayComputerReq.getAppPrivateKey());
            alipayConfig.setAlipayPublicKey(aliPayComputerReq.getAlipayPublicKey());
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            //请求参数
            AlipayTradeWapPayRequest alipayTradeWapPayRequest = new AlipayTradeWapPayRequest();
            //支付宝第三方应用调用时使用
            if (aliPayComputerReq.getPayType().equals(PayTypeEnum.MEMBER.getCode())) {
                alipayTradeWapPayRequest.putOtherTextParam("app_auth_token", aliPayComputerReq.getAppAuthToken());
            }
            //请求实体
            AlipayTradeWapPayModel model = new AlipayTradeWapPayModel();
            model.setOutTradeNo(aliPayComputerReq.getOrderCode());   //商户订单号
            model.setTotalAmount(String.valueOf(aliPayComputerReq.getPayMoney()));//订单总金额
            model.setSubject(aliPayComputerReq.getRemark());         //订单标题
            model.setBody(attachJson);                                   //用作回调校验
            alipayTradeWapPayRequest.setBizModel(model);
            alipayTradeWapPayRequest.setNotifyUrl(payGateWayConfig.getNotifyUrl() + AliPayUtil.notifyUrl);//回调地址
            AlipayTradeWapPayResponse response = alipayClient.pageExecute(alipayTradeWapPayRequest);
            if (StringUtil.isNotEmpty(response.getBody())) {
                return new AliPayResultResp(true, response.getCode(), response.getBody());
            } else {
                return new AliPayResultResp(false, response.getCode(), response.getSubMsg());
            }
        } catch (Exception e) {
            throw new BusinessException(ResponseCodeEnum.ALIPAY_FAILED, e.getMessage());
        }
    }

    /**
     * 查询订单
     *
     * @param aliPayOrderQueryReq 查询支付宝支付订单
     * @return 查询结果
     */
    @Override
    public AliPayResultResp orderQuery(AliPayOrderQueryReq aliPayOrderQueryReq) {
        try {
            //获取参数
            Long memberId = aliPayOrderQueryReq.getMemberId();
            Long memberRoleId = aliPayOrderQueryReq.getMemberRoleId();
            String outTradeNo = aliPayOrderQueryReq.getOutTradeNo();
            String transactionId = aliPayOrderQueryReq.getTransactionId();
            String orgPid = aliPayOrderQueryReq.getOrgPid();
            Integer payType = aliPayOrderQueryReq.getPayType();
            //从订单服务查询支付参数
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(memberId);
            feignVO.setRoleId(memberRoleId);
            feignVO.setPayChannel(OrderPayChannelEnum.ALIPAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new AliPayResultResp(false, String.valueOf(parameterResult.getCode()), parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new AliPayResultResp(false, String.valueOf(ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getCode()), ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }
            //参数配置
            AlipayConfig alipayConfig = new AlipayConfig();
            //从订单服务中获取动态配置
           /* String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDRjrdW9XaMOKXkV5padfziD8sIxiNFUXepjz4CKt+rSRJNUDSz/3+Eviu0HfJdro1iGI/qbLFcJeo3Mk6LRT2Y68B2l33anjX3x3pZEvjQJmwLuvC5kLb7i9mYFWRMTS14mWlGEQDCDyJYHSZXJrVDU/BMn4fQH0KM01+/tvFnXqnGp111gYtEVwc7WTSWtqwR4nSUxtNHY8Gxkb6n7g5np5jYLRwY1W4tx3yVE5/FuYeR8lysl0A0wXcE3UvJPM6tXMDGXWd5OMJie51XM5Mcx9GsfAVmux2WKnJZTZGivSWo1PXaoX3KsUtVHSmxWDkcxDWM320w5rH77p0gJIsfAgMBAAECggEAFZO4VP/ZQP7kNeq/PelALJartS+RmiWVSKQzIk3/0ccoV0WW56G7I5PhNB6RQgwjCqK0ztJ6QJ62yxAm/jgW65P6sTtP/h6kN+sWMe2+KNCL93k8KB86eBQiR4KMXT7lG7HQISLHDTQ5uRDnTbnymAggigMzHL7bP1zUUw0nNhldRESDSack/Cu7uE7nOvyZEnazAfVKxPgsO0d+Tn7A/GFtcP8+CO9rcEZpkMvkBkaqPxKLUBHcKabDBglvcMcz/a4oKzcSB8B5GvC7h5OPMAAAtMQy+pqIwx7GGRrhGzh1l0thN7qiH/U5sL9JCU3ZY01vOCvIPJDiYGPU66r7oQKBgQD9xMJZStvA1WT7PnFAd/wN+rjAobzokGm/YktC/jyNv4gSBA5ZgC1PTizh/+19XzQIz9YrNAnnEGmWPYecQj80yoDaArbiIH3NCEnHRYiaSomhMcCNaqmVjPBhQT5qkSkypfUj3d8inUyYLSFpz6e+kzTMnwWshlPWprr0FswKCwKBgQDTZm+7PrzogW58IOw4VdH8DvomV8nZ78VOgh8et50wHGw+DIty6+lbvHrRi69vju+zC62hNePWDLYWnn81b/nuErrm3SxQ4uDc8WsafvYiasfzS6fnHC6KGQa6wOsasoYb/iIdcLNqGiVbrJMghex4ENAcYwnXaldpUjwbxwkDvQKBgQDz/c5vVA5eJc7l0m/JcJOkTUB91gRwBwnlvXhnfVJzZ4hI6iVx4E+wRJXa5dtOA89f+ZUODQbIZ3tdvOM6Vme3Nk0VUC7O1DaggCuPsUdaMfiKR++/CKLs64njuHtuswtNa0t7pMP54AvY9Ot/dOzvy1Gku/EUpELPy5dNQO4FaQKBgDHoTRh3h0GczOihHSNvn1NSddv3fb/lyz7tmUs3LfTdwPAlJgbLixwNMzGlOJWtuQkcCLG2d0NhXNuaL43+EZMS+Ozn6j5uUYfXtuFad+FINExL4Ce/j30qy1vrj5WN7/tL0+H/aVvzMD6kT1euRlVSIs+Q0hH5GQnUUDx+kLJBAoGAFdUdC7oszUxhyHoC3Tk2FR9g5M28RNb+C+Iep2gsEU91H1s/Hvq2WkFAiqReSX1Xf28tzDVpHaipQVCAERx64MSm2XIZJeaulR5m55U5zKbc+InhdVrnQKfiICXlsjwRn6fpxwZ6APhQ//Qm9CgU4kKv9ChJcLyhsa1Q/rZr1xw=";
            String alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkDl4qfoTCbqkwva73sRkflg6OKwZ43V9WonRos6LKD5DjR44pNo87ZqGQH3YA/oHtbvdJakJJmfHC2hrtIv8z+wEjzQPE0YOUZ3PCA5MzkivGqDTUKy6h6mQ/uq4Ra+C03aN8w5uphBQuX++CEtHzLPQMoGgrzz5zrgim5XyaxbxoKFoOOqzCwoo2qFljGZ3nWIttobvP8zSWcS1qGPxSQxnfa0jXn1DZhCuaPH55W1J82W5UAx2UPHpQAwUjD8Wu0cfW+ZoLhyNNSEfYTu9+mgSQYDOA2dV+cJwWltO2zdZED/ii6VptUUz8qWu1HcdNR19m5T1GAkTJx475b9eIQIDAQAB";
            alipayConfig.setAppId("2021002190673720");
            alipayConfig.setPrivateKey(privateKey);
            alipayConfig.setAlipayPublicKey(alipayPublicKey);*/
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_ID)).findFirst().ifPresent(p -> alipayConfig.setAppId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY)).findFirst().ifPresent(p -> alipayConfig.setAlipayPublicKey(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PRIVATE_KEY)).findFirst().ifPresent(p -> alipayConfig.setPrivateKey(p.getValue()));
            alipayConfig.setServerUrl(AliPayUtil.gatewayUrl);
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            //统一收单线下交易查询对象:AlipayTradeQueryRequest
            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            //自定义请求参数
            AlipayTradeQueryModel model = new AlipayTradeQueryModel();
            model.setOutTradeNo(outTradeNo);                               //商户订单号      二者必须传其中一个,同时传trade_no优先级高
            model.setTradeNo(transactionId);                               //支付宝交易号
            //  model.setOrgPid(orgPid); //银行间联模式下有用，其它场景请不要使用
            request.setBizModel(model);
            AlipayTradeQueryResponse response = alipayClient.execute(request);
            //验证返回结果
            if (response != null) {
                String returnCode = response.getCode();
                if (AliPayResultEnum.SUCCESS.getCode().equals(returnCode)) {
                    //验证结果
                    return new AliPayResultResp(true, response.getCode(), response.getBody());
                } else {
                    return new AliPayResultResp(false, response.getCode(), response.getSubMsg());
                }
            } else {
                return new AliPayResultResp(false, AliPayUtil.QUERY_ORDER_FILED);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new AliPayResultResp(false, e.getMessage());
        }
    }

    /**
     * 申请退款
     */
    @Override
    public AliPayResultResp refund(AliPayRefundReq aliPayRefundReq) {
        try {
            //获取参数
            Long memberRoleId = aliPayRefundReq.getMemberRoleId();
            String outTradeNo = aliPayRefundReq.getOutTradeNo();
            String transactionId = aliPayRefundReq.getTransactionId();
            String outRefundNo = aliPayRefundReq.getOutRefundNo();
            BigDecimal refundMoney = aliPayRefundReq.getRefundMoney();
            String refundReason = aliPayRefundReq.getRefundReason();
            Integer payType = aliPayRefundReq.getPayType();

            //从订单服务查询支付参数
            //平台支付参数
            if (payType.equals(PayTypeEnum.PLATFORM.getCode()) && StringUtils.isAnyEmpty(aliPayRefundReq.getAppId(), aliPayRefundReq.getPublicKey(), aliPayRefundReq.getPrivateKey())) {
                OrderPayChannelFeignReq feignVO = new OrderPayChannelFeignReq();
                feignVO.setPayChannel(OrderPayChannelEnum.ALIPAY);
                WrapperResp<PaymentParameterFeignDetailResp> platformResult = orderFeignService.findPlatformPaymentParameters(feignVO);
                if (platformResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                    return new AliPayResultResp(false, String.valueOf(platformResult.getCode()), platformResult.getMessage());
                }

                if (CollectionUtils.isEmpty(platformResult.getData().getParameters())) {
                    return new AliPayResultResp(false, String.valueOf(ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getCode()), ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
                }

                platformResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_ID)).findFirst().ifPresent(p -> aliPayRefundReq.setAppId(p.getValue()));
                platformResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY)).findFirst().ifPresent(p -> aliPayRefundReq.setPublicKey(p.getValue()));
                platformResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PRIVATE_KEY)).findFirst().ifPresent(p -> aliPayRefundReq.setPrivateKey(p.getValue()));
            }

            //会员支付参数
            if (payType.equals(PayTypeEnum.MEMBER.getCode()) && StringUtils.isAnyEmpty(aliPayRefundReq.getAppId(), aliPayRefundReq.getPublicKey(), aliPayRefundReq.getPrivateKey(), aliPayRefundReq.getAppAuthToken())) {
                OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
                feignVO.setMemberId(aliPayRefundReq.getMemberId());
                feignVO.setRoleId(aliPayRefundReq.getMemberRoleId());
                feignVO.setPayChannel(OrderPayChannelEnum.ALIPAY);
                WrapperResp<PaymentParameterFeignDetailResp> memberResult = orderFeignService.findMemberPaymentParameters(feignVO);
                if (memberResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                    return new AliPayResultResp(false, String.valueOf(memberResult.getCode()), memberResult.getMessage());
                }

                if (CollectionUtils.isEmpty(memberResult.getData().getParameters())) {
                    return new AliPayResultResp(false, String.valueOf(ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getCode()), ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
                }

                memberResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_ID)).findFirst().ifPresent(p -> aliPayRefundReq.setAppId(p.getValue()));
                memberResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY)).findFirst().ifPresent(p -> aliPayRefundReq.setPublicKey(p.getValue()));
                memberResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PRIVATE_KEY)).findFirst().ifPresent(p -> aliPayRefundReq.setPrivateKey(p.getValue()));
                memberResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_THREAD_APP_AUTH_TOKEN)).findFirst().ifPresent(p -> aliPayRefundReq.setAppAuthToken(p.getValue()));
            }

            //参数配置
            AlipayConfig alipayConfig = new AlipayConfig();
            alipayConfig.setAppId(aliPayRefundReq.getAppId());
            alipayConfig.setAlipayPublicKey(aliPayRefundReq.getPublicKey());
            alipayConfig.setPrivateKey(aliPayRefundReq.getPrivateKey());

            //固定配置
            alipayConfig.setServerUrl(AliPayUtil.gatewayUrl);
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            //统一收单交易退款对象
            AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
            //第三方应用时使用
            if (aliPayRefundReq.getPayType().equals(PayTypeEnum.MEMBER.getCode())) {
                request.putOtherTextParam("app_auth_token", aliPayRefundReq.getAppAuthToken());
            }
            //自定义请求参数
            AlipayTradeRefundModel model = new AlipayTradeRefundModel();
            model.setOutTradeNo(transactionId);                          //商户订单号
            // model.setTradeNo(transactionId);                          //支付宝交易号
            model.setRefundAmount(String.valueOf(refundMoney));       //退款金额
            model.setOutRequestNo(outRefundNo);                       //退款请求号
            model.setRefundReason(refundReason);                       //退款原因
            request.setBizModel(model);
            AlipayTradeRefundResponse response = alipayClient.execute(request);
            //验证返回结果
            if (response != null) {
                String returnCode = response.getCode();
                if (AliPayResultEnum.SUCCESS.getCode().equals(returnCode)) {
                    //验证结果
                    return new AliPayResultResp(true, response.getCode(), response.getCode());
                } else {
                    return new AliPayResultResp(false, response.getCode(), response.getSubMsg());
                }
            } else {
                return new AliPayResultResp(false, AliPayUtil.APPLY_REFUND_FILED);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new AliPayResultResp(false, e.getMessage());
        }
    }

    /**
     * 退款查询
     *
     * @param aliPayRefundQueryReq 支付宝退款信息
     * @return 查询结果
     */
    @Override
    public AliPayResultResp refundQuery(AliPayRefundQueryReq aliPayRefundQueryReq) {
        try {
            //获取参数
            Long memberId = aliPayRefundQueryReq.getMemberId();
            Long memberRoleId = aliPayRefundQueryReq.getMemberRoleId();
            String outTradeNo = aliPayRefundQueryReq.getOutTradeNo();
            String transactionId = aliPayRefundQueryReq.getTransactionId();
            String outRefundNo = aliPayRefundQueryReq.getOutRefundNo();
            Integer payType = aliPayRefundQueryReq.getPayType();
            //从订单服务查询支付参数
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(memberId);
            feignVO.setRoleId(memberRoleId);
            feignVO.setPayChannel(OrderPayChannelEnum.ALIPAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new AliPayResultResp(false, String.valueOf(parameterResult.getCode()), parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new AliPayResultResp(false, String.valueOf(ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getCode()), ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            //参数配置
            AlipayConfig alipayConfig = new AlipayConfig();
          /*  String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDRjrdW9XaMOKXkV5padfziD8sIxiNFUXepjz4CKt+rSRJNUDSz/3+Eviu0HfJdro1iGI/qbLFcJeo3Mk6LRT2Y68B2l33anjX3x3pZEvjQJmwLuvC5kLb7i9mYFWRMTS14mWlGEQDCDyJYHSZXJrVDU/BMn4fQH0KM01+/tvFnXqnGp111gYtEVwc7WTSWtqwR4nSUxtNHY8Gxkb6n7g5np5jYLRwY1W4tx3yVE5/FuYeR8lysl0A0wXcE3UvJPM6tXMDGXWd5OMJie51XM5Mcx9GsfAVmux2WKnJZTZGivSWo1PXaoX3KsUtVHSmxWDkcxDWM320w5rH77p0gJIsfAgMBAAECggEAFZO4VP/ZQP7kNeq/PelALJartS+RmiWVSKQzIk3/0ccoV0WW56G7I5PhNB6RQgwjCqK0ztJ6QJ62yxAm/jgW65P6sTtP/h6kN+sWMe2+KNCL93k8KB86eBQiR4KMXT7lG7HQISLHDTQ5uRDnTbnymAggigMzHL7bP1zUUw0nNhldRESDSack/Cu7uE7nOvyZEnazAfVKxPgsO0d+Tn7A/GFtcP8+CO9rcEZpkMvkBkaqPxKLUBHcKabDBglvcMcz/a4oKzcSB8B5GvC7h5OPMAAAtMQy+pqIwx7GGRrhGzh1l0thN7qiH/U5sL9JCU3ZY01vOCvIPJDiYGPU66r7oQKBgQD9xMJZStvA1WT7PnFAd/wN+rjAobzokGm/YktC/jyNv4gSBA5ZgC1PTizh/+19XzQIz9YrNAnnEGmWPYecQj80yoDaArbiIH3NCEnHRYiaSomhMcCNaqmVjPBhQT5qkSkypfUj3d8inUyYLSFpz6e+kzTMnwWshlPWprr0FswKCwKBgQDTZm+7PrzogW58IOw4VdH8DvomV8nZ78VOgh8et50wHGw+DIty6+lbvHrRi69vju+zC62hNePWDLYWnn81b/nuErrm3SxQ4uDc8WsafvYiasfzS6fnHC6KGQa6wOsasoYb/iIdcLNqGiVbrJMghex4ENAcYwnXaldpUjwbxwkDvQKBgQDz/c5vVA5eJc7l0m/JcJOkTUB91gRwBwnlvXhnfVJzZ4hI6iVx4E+wRJXa5dtOA89f+ZUODQbIZ3tdvOM6Vme3Nk0VUC7O1DaggCuPsUdaMfiKR++/CKLs64njuHtuswtNa0t7pMP54AvY9Ot/dOzvy1Gku/EUpELPy5dNQO4FaQKBgDHoTRh3h0GczOihHSNvn1NSddv3fb/lyz7tmUs3LfTdwPAlJgbLixwNMzGlOJWtuQkcCLG2d0NhXNuaL43+EZMS+Ozn6j5uUYfXtuFad+FINExL4Ce/j30qy1vrj5WN7/tL0+H/aVvzMD6kT1euRlVSIs+Q0hH5GQnUUDx+kLJBAoGAFdUdC7oszUxhyHoC3Tk2FR9g5M28RNb+C+Iep2gsEU91H1s/Hvq2WkFAiqReSX1Xf28tzDVpHaipQVCAERx64MSm2XIZJeaulR5m55U5zKbc+InhdVrnQKfiICXlsjwRn6fpxwZ6APhQ//Qm9CgU4kKv9ChJcLyhsa1Q/rZr1xw=";
            String alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkDl4qfoTCbqkwva73sRkflg6OKwZ43V9WonRos6LKD5DjR44pNo87ZqGQH3YA/oHtbvdJakJJmfHC2hrtIv8z+wEjzQPE0YOUZ3PCA5MzkivGqDTUKy6h6mQ/uq4Ra+C03aN8w5uphBQuX++CEtHzLPQMoGgrzz5zrgim5XyaxbxoKFoOOqzCwoo2qFljGZ3nWIttobvP8zSWcS1qGPxSQxnfa0jXn1DZhCuaPH55W1J82W5UAx2UPHpQAwUjD8Wu0cfW+ZoLhyNNSEfYTu9+mgSQYDOA2dV+cJwWltO2zdZED/ii6VptUUz8qWu1HcdNR19m5T1GAkTJx475b9eIQIDAQAB";
            alipayConfig.setAppId("2021002190673720");
            alipayConfig.setPrivateKey(privateKey);
            alipayConfig.setAlipayPublicKey(alipayPublicKey);*/
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_ID)).findFirst().ifPresent(p -> alipayConfig.setAppId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY)).findFirst().ifPresent(p -> alipayConfig.setAlipayPublicKey(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PRIVATE_KEY)).findFirst().ifPresent(p -> alipayConfig.setPrivateKey(p.getValue()));
            //固定配置
            alipayConfig.setServerUrl(AliPayUtil.gatewayUrl);
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            //统一收单交易退款查询对象:AlipayTradeFastpayRefundQueryRequest
            AlipayTradeFastpayRefundQueryRequest request = new AlipayTradeFastpayRefundQueryRequest();
            //自定义请求参数
            AlipayTradeFastpayRefundQueryModel model = new AlipayTradeFastpayRefundQueryModel();
            model.setOutTradeNo(outTradeNo);        //商户订单号
            model.setTradeNo(transactionId);        //支付宝交易号
            model.setOutRequestNo(outRefundNo);     //退款请求号(请求退款接口时，传入的退款请求号，如果在退款请求时未传入，则该值为创建交易时的商户订单号。)
            request.setBizModel(model);
            AlipayTradeFastpayRefundQueryResponse response = alipayClient.execute(request);
            //验证返回结果
            if (response != null) {
                String returnCode = response.getCode();
                if (AliPayResultEnum.REFUND_SUCCESS.getCode().equals(returnCode)) {
                    //验证支付结果
                    return new AliPayResultResp(true, response.getCode(), response.getBody());
                } else {
                    return new AliPayResultResp(false, response.getCode(), response.getSubMsg());
                }
            } else {
                return new AliPayResultResp(false, AliPayUtil.QUERY_REFUND_FILED);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new AliPayResultResp(false, e.getMessage());
        }
    }

    /**
     * 关闭交易
     *
     * @param aliPayCloseOrderReq 支付宝关闭交易请求体
     * @return 关闭结果
     */

    @Override
    public AliPayResultResp closeOrder(AliPayCloseOrderReq aliPayCloseOrderReq) {
        try {
            //获取参数
            Long memberId = aliPayCloseOrderReq.getMemberId();
            Long memberRoleId = aliPayCloseOrderReq.getMemberRoleId();
            String outTradeNo = aliPayCloseOrderReq.getOutTradeNo();
            String tradeNo = aliPayCloseOrderReq.getTradeNo();
            Long operateId = aliPayCloseOrderReq.getOperateId();
            Integer payType = aliPayCloseOrderReq.getPayType();

            //从订单服务查询支付参数
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(memberId);
            feignVO.setRoleId(memberRoleId);
            feignVO.setPayChannel(OrderPayChannelEnum.ALIPAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new AliPayResultResp(false, String.valueOf(parameterResult.getCode()), parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new AliPayResultResp(false, String.valueOf(ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getCode()), ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }
            //参数配置
            AlipayConfig alipayConfig = new AlipayConfig();
       /*     String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDRjrdW9XaMOKXkV5padfziD8sIxiNFUXepjz4CKt+rSRJNUDSz/3+Eviu0HfJdro1iGI/qbLFcJeo3Mk6LRT2Y68B2l33anjX3x3pZEvjQJmwLuvC5kLb7i9mYFWRMTS14mWlGEQDCDyJYHSZXJrVDU/BMn4fQH0KM01+/tvFnXqnGp111gYtEVwc7WTSWtqwR4nSUxtNHY8Gxkb6n7g5np5jYLRwY1W4tx3yVE5/FuYeR8lysl0A0wXcE3UvJPM6tXMDGXWd5OMJie51XM5Mcx9GsfAVmux2WKnJZTZGivSWo1PXaoX3KsUtVHSmxWDkcxDWM320w5rH77p0gJIsfAgMBAAECggEAFZO4VP/ZQP7kNeq/PelALJartS+RmiWVSKQzIk3/0ccoV0WW56G7I5PhNB6RQgwjCqK0ztJ6QJ62yxAm/jgW65P6sTtP/h6kN+sWMe2+KNCL93k8KB86eBQiR4KMXT7lG7HQISLHDTQ5uRDnTbnymAggigMzHL7bP1zUUw0nNhldRESDSack/Cu7uE7nOvyZEnazAfVKxPgsO0d+Tn7A/GFtcP8+CO9rcEZpkMvkBkaqPxKLUBHcKabDBglvcMcz/a4oKzcSB8B5GvC7h5OPMAAAtMQy+pqIwx7GGRrhGzh1l0thN7qiH/U5sL9JCU3ZY01vOCvIPJDiYGPU66r7oQKBgQD9xMJZStvA1WT7PnFAd/wN+rjAobzokGm/YktC/jyNv4gSBA5ZgC1PTizh/+19XzQIz9YrNAnnEGmWPYecQj80yoDaArbiIH3NCEnHRYiaSomhMcCNaqmVjPBhQT5qkSkypfUj3d8inUyYLSFpz6e+kzTMnwWshlPWprr0FswKCwKBgQDTZm+7PrzogW58IOw4VdH8DvomV8nZ78VOgh8et50wHGw+DIty6+lbvHrRi69vju+zC62hNePWDLYWnn81b/nuErrm3SxQ4uDc8WsafvYiasfzS6fnHC6KGQa6wOsasoYb/iIdcLNqGiVbrJMghex4ENAcYwnXaldpUjwbxwkDvQKBgQDz/c5vVA5eJc7l0m/JcJOkTUB91gRwBwnlvXhnfVJzZ4hI6iVx4E+wRJXa5dtOA89f+ZUODQbIZ3tdvOM6Vme3Nk0VUC7O1DaggCuPsUdaMfiKR++/CKLs64njuHtuswtNa0t7pMP54AvY9Ot/dOzvy1Gku/EUpELPy5dNQO4FaQKBgDHoTRh3h0GczOihHSNvn1NSddv3fb/lyz7tmUs3LfTdwPAlJgbLixwNMzGlOJWtuQkcCLG2d0NhXNuaL43+EZMS+Ozn6j5uUYfXtuFad+FINExL4Ce/j30qy1vrj5WN7/tL0+H/aVvzMD6kT1euRlVSIs+Q0hH5GQnUUDx+kLJBAoGAFdUdC7oszUxhyHoC3Tk2FR9g5M28RNb+C+Iep2gsEU91H1s/Hvq2WkFAiqReSX1Xf28tzDVpHaipQVCAERx64MSm2XIZJeaulR5m55U5zKbc+InhdVrnQKfiICXlsjwRn6fpxwZ6APhQ//Qm9CgU4kKv9ChJcLyhsa1Q/rZr1xw=";
            String alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkDl4qfoTCbqkwva73sRkflg6OKwZ43V9WonRos6LKD5DjR44pNo87ZqGQH3YA/oHtbvdJakJJmfHC2hrtIv8z+wEjzQPE0YOUZ3PCA5MzkivGqDTUKy6h6mQ/uq4Ra+C03aN8w5uphBQuX++CEtHzLPQMoGgrzz5zrgim5XyaxbxoKFoOOqzCwoo2qFljGZ3nWIttobvP8zSWcS1qGPxSQxnfa0jXn1DZhCuaPH55W1J82W5UAx2UPHpQAwUjD8Wu0cfW+ZoLhyNNSEfYTu9+mgSQYDOA2dV+cJwWltO2zdZED/ii6VptUUz8qWu1HcdNR19m5T1GAkTJx475b9eIQIDAQAB";
            alipayConfig.setAppId("2021002190673720");
            alipayConfig.setPrivateKey(privateKey);
            alipayConfig.setAlipayPublicKey(alipayPublicKey);*/
            //从订单服务中获取动态配置
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_APP_ID)).findFirst().ifPresent(p -> alipayConfig.setAppId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PUBLIC_KEY)).findFirst().ifPresent(p -> alipayConfig.setAlipayPublicKey(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.ALIPAY_PRIVATE_KEY)).findFirst().ifPresent(p -> alipayConfig.setPrivateKey(p.getValue()));
            //固定配置
            alipayConfig.setServerUrl(AliPayUtil.gatewayUrl);
            AlipayClient alipayClient = new DefaultAlipayClient(alipayConfig);
            //统一收单交易关闭对象:AlipayTradeCloseRequest
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
            //自定义请求参数
            AlipayTradeCloseModel model = new AlipayTradeCloseModel();
            model.setOutTradeNo(outTradeNo);                          //商户订单号
            model.setTradeNo(tradeNo);                                //支付宝交易号
            model.setOperatorId(String.valueOf(operateId));           //操作人
            request.setBizModel(model);
            AlipayTradeCloseResponse response = alipayClient.execute(request);
            //验证返回结果
            if (response != null) {
                String returnCode = response.getCode();
                if (AliPayResultEnum.SUCCESS.getCode().equals(returnCode)) {
                    //验证结果
                    return new AliPayResultResp(true, response.getBody(), response.getBody());
                } else {
                    return new AliPayResultResp(false, response.getBody(), response.getSubMsg());
                }
            } else {
                return new AliPayResultResp(false, AliPayUtil.CLOSE_TRADE_FIELD);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return new AliPayResultResp(false, e.getMessage());
        }
    }
}

