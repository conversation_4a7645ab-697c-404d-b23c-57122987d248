package com.ssy.lingxi.pay.serviceImpl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.TypeReference;
import com.github.binarywang.wxpay.bean.order.WxPayAppOrderResult;
import com.github.binarywang.wxpay.bean.order.WxPayMwebOrderResult;
import com.github.binarywang.wxpay.bean.request.*;
import com.github.binarywang.wxpay.bean.result.*;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.github.binarywang.wxpay.util.SignUtils;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.common.util.RandomNumberUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.common.util.UUIDUtil;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPaymentParameterEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.IpUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayChannelFeignReq;
import com.ssy.lingxi.order.api.model.req.OrderPayParameterFeignReq;
import com.ssy.lingxi.order.api.model.resp.PayChannelParameterFeignDetailResp;
import com.ssy.lingxi.order.api.model.resp.PaymentParameterFeignDetailResp;
import com.ssy.lingxi.pay.api.model.req.*;
import com.ssy.lingxi.pay.api.model.req.wechatPay.AppletOpenidReq;
import com.ssy.lingxi.pay.api.model.req.wechatPay.UploadShippingInfoListFeignReq;
import com.ssy.lingxi.pay.api.model.resp.wechatPay.AppletOpenidResp;
import com.ssy.lingxi.pay.api.model.resp.wechatPay.UploadShippingInfoResp;
import com.ssy.lingxi.pay.config.PayGateWayConfig;
import com.ssy.lingxi.pay.enums.WechatErrorEnum;
import com.ssy.lingxi.pay.handler.RestTemplateHandler;
import com.ssy.lingxi.pay.model.dto.UrlLinkDTO;
import com.ssy.lingxi.pay.model.dto.WeChatPayAttachDTO;
import com.ssy.lingxi.pay.model.req.AccessTokenReq;
import com.ssy.lingxi.pay.model.req.JsApiReq;
import com.ssy.lingxi.pay.model.req.wechatPay.UploadShippingInfoReq;
import com.ssy.lingxi.pay.model.resp.AccessTokenResp;
import com.ssy.lingxi.pay.model.resp.JsApiResultResp;
import com.ssy.lingxi.pay.model.resp.JsApiTicketResp;
import com.ssy.lingxi.pay.model.resp.WeChatPayResultResp;
import com.ssy.lingxi.pay.model.resp.wechatPay.WxAppletCode2SessionResp;
import com.ssy.lingxi.pay.service.IPayCacheService;
import com.ssy.lingxi.pay.service.IWeChatPayService;
import com.ssy.lingxi.pay.service.eAccount.IEAccountService;
import com.ssy.lingxi.pay.util.WeChatPayUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;

/**
 * 微信支付类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeChatPayServiceImpl implements IWeChatPayService {

    private final IPayCacheService payCacheService;
    private final PayGateWayConfig payGateWayConfig;
    private final IOrderProcessFeign orderFeignService;
    private final IEAccountService eAccountService;

    private final RestTemplateHandler restTemplateHandler;
    private final IRedisUtils redisUtils;

    /**
     * 获取access_token的接口地址（GET） 限200（次/天）
     */
    public final static String access_token_url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET";
    /**
     * 获取小程序 URL Link(POST)<br/>
     * 获取小程序 URL Link，适用于短信、邮件、网页、微信内等拉起小程序的业务场景。目前仅针对国内非个人主体的小程序开放
     */
    public final static String generate_url_link = "https://api.weixin.qq.com/wxa/generate_urllink?access_token=ACCESS_TOKEN";
    /**
     * 获取JsApiTicket
     */
    public final static String ticket_url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=ACCESS_TOKEN&type=jsapi";
    /**
     * 发货信息录入接口<br/>
     * 用户交易后，默认资金将会进入冻结状态，开发者在发货后，需要在小程序平台录入相关发货信息，平台会将发货信息以消息的形式推送给购买的微信用户。
     */
    public final static String upload_shipping_info = "https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token=ACCESS_TOKEN";

    /**
     * native支付(生成二维码，通过手机扫描二维码调起微信支付)
     */
    @Override
    public WeChatPayResultResp nativePay(MobilePayReq mobilePayReq, HttpServletRequest request) {
        //从订单服务查询支付参数
        if (StringUtils.isAnyEmpty(mobilePayReq.getMerchantId(), mobilePayReq.getApiKey(), mobilePayReq.getAppId())) {
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(mobilePayReq.getMemberId());
            feignVO.setRoleId(mobilePayReq.getMemberRoleId());
            feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new WeChatPayResultResp(false, parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new WeChatPayResultResp(false, ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            parameterResult.getData().getParameters().forEach(p -> {
                if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)) {
                    mobilePayReq.setMerchantId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)) {
                    mobilePayReq.setAppId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)) {
                    mobilePayReq.setApiKey(p.getValue());
                }
            });
        }

        //生成用于缓存Key的随机字符串，并作为透传参数传递给微信支付，在返回时从缓存中查询秘钥信息
        if (StringUtils.isEmpty(mobilePayReq.getNonce())) {
            mobilePayReq.setNonce(RandomNumberUtil.randomUniqueNumber());
        }

        //缓存支付参数
        payCacheService.cacheWeChatPayParameters(mobilePayReq.getMemberId(), mobilePayReq.getMemberRoleId(), mobilePayReq.getPayType(), mobilePayReq.getNonce(), mobilePayReq.getMerchantId(), mobilePayReq.getAppId(), mobilePayReq.getApiKey());

        //获取参数
        String orderCode = mobilePayReq.getOrderCode();
        BigDecimal payMoney = mobilePayReq.getPayMoney();
        String remark = mobilePayReq.getRemark();
        String attach = mobilePayReq.getAttach();
        Long memberId = mobilePayReq.getMemberId();
        Long memberRoleId = mobilePayReq.getMemberRoleId();
        String serviceType = mobilePayReq.getServiceType();
        Integer payType = mobilePayReq.getPayType();

        //封装微信附加数据
        //由于微信附加信息最长128字符，所以改为用ObjectMapper进行序列化，同时AttachInfo的每个字段增加了@JsonProperty注解
        //这样可以简短附加数据的长度
        WeChatPayAttachDTO wechatPayAttachDTO = new WeChatPayAttachDTO();
        wechatPayAttachDTO.setPayType(payType);
        wechatPayAttachDTO.setMemberId(memberId);
        wechatPayAttachDTO.setMemberRoleId(memberRoleId);
        wechatPayAttachDTO.setAttach(attach);
        wechatPayAttachDTO.setServiceType(serviceType);
        wechatPayAttachDTO.setNonce(mobilePayReq.getNonce());
        String attachJson = SerializeUtil.serialize(wechatPayAttachDTO);
        //微信支付需要的参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("appid", mobilePayReq.getAppId());                                         //程序ID
        paramMap.put("mch_id", mobilePayReq.getMerchantId());                                   //商户号
        paramMap.put("nonce_str", UUIDUtil.randomUUID());                                     //随机字符串
        paramMap.put("body", remark);                                                               //商品描述
        paramMap.put("out_trade_no", orderCode);                                                    //商户订单号
        paramMap.put("total_fee", String.valueOf(payMoney.multiply(BigDecimal.valueOf(100)).longValue()));    //交易金额
        paramMap.put("spbill_create_ip", IpUtil.getIpAddr(request));                   //客户端id
        paramMap.put("notify_url", payGateWayConfig.getNotifyUrl() + WeChatPayUtil.NOTIFY_URL);      //支付结果通知的回调地址
        paramMap.put("trade_type", WeChatPayUtil.NATIVE);                                           //交易类型
        if (StringUtils.isNotEmpty(attachJson)) {                                                     //附加数据
            paramMap.put("attach", attachJson);
        }
        try {
            //生成签名
            String sign = WeChatPayUtil.createSign(paramMap, mobilePayReq.getApiKey(), "UTF-8");
            paramMap.put("sign", sign);
            //验证必填项
            if (StringUtils.isEmpty(paramMap.get("appid"))) {
                return new WeChatPayResultResp(false, "appid 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("mch_id"))) {
                return new WeChatPayResultResp(false, "mch_id 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("nonce_str"))) {
                return new WeChatPayResultResp(false, "nonce_str 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("body"))) {
                return new WeChatPayResultResp(false, "body 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("out_trade_no"))) {
                return new WeChatPayResultResp(false, "out_trade_no 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("total_fee"))) {
                return new WeChatPayResultResp(false, "total_fee 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("spbill_create_ip"))) {
                return new WeChatPayResultResp(false, "spbill_create_ip 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("notify_url"))) {
                return new WeChatPayResultResp(false, "notify_url 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("trade_type"))) {
                return new WeChatPayResultResp(false, "trade_type 参数为空");
            }
            if (StringUtils.isEmpty(sign)) {
                return new WeChatPayResultResp(false, "sign 参数为空");
            }

            //参数转成XML
            String requestXML = WeChatPayUtil.getRequestXml(paramMap);
            //预支付
            String resultXML = WeChatPayUtil.httpsRequest(WeChatPayUtil.PAY_URL, "POST", requestXML);
            //返回结果转成Map
            Map<String, String> resultMap = WeChatPayUtil.xmlToMap(resultXML);
            //验证返回结果
            String returnCode = resultMap.get("return_code");
            if (WeChatPayUtil.SUCCESS.equals(returnCode)) {
                //验证支付结果
                String resultCode = resultMap.get("result_code");
                if (WeChatPayUtil.SUCCESS.equals(resultCode)) {
                    Map<String, String> result = new HashMap<String, String>();
                    //返回支付url,页面生成二维码
                    result.put("code_url", resultMap.get("code_url"));
                    return new WeChatPayResultResp(true, result);
                } else {
                    return new WeChatPayResultResp(false, resultMap.get("err_code_des"));
                }
            }
            //返回错误信息
            return new WeChatPayResultResp(false, resultMap.get("return_msg"));
        } catch (Exception e) {
            log.error("native支付(生成二维码，通过手机扫描二维码调起微信支付): 统一下单异常：", e);
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL);
        }
    }

    /**
     * app支付(在app软件中直接调起微信支付)
     */
    @Override
    public WeChatPayResultResp appPay(MobilePayReq mobilePayReq, HttpServletRequest request) {
        //从订单服务查询支付参数
        if (StringUtils.isAnyEmpty(mobilePayReq.getMerchantId(), mobilePayReq.getApiKey(), mobilePayReq.getAppId())) {
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(mobilePayReq.getMemberId());
            feignVO.setRoleId(mobilePayReq.getMemberRoleId());
            feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new WeChatPayResultResp(false, parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new WeChatPayResultResp(false, ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            parameterResult.getData().getParameters().forEach(p -> {
                if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)) {
                    mobilePayReq.setMerchantId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)) {
                    mobilePayReq.setAppId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)) {
                    mobilePayReq.setApiKey(p.getValue());
                }
            });
        }

        //生成用于缓存Key的随机字符串，并作为透传参数传递给微信支付，在返回时从缓存中查询秘钥信息
        if (StringUtils.isEmpty(mobilePayReq.getNonce())) {
            mobilePayReq.setNonce(RandomNumberUtil.randomUniqueNumber());
        }

        //缓存支付参数
        payCacheService.cacheWeChatPayParameters(mobilePayReq.getMemberId(), mobilePayReq.getMemberRoleId(), mobilePayReq.getPayType(), mobilePayReq.getNonce(), mobilePayReq.getMerchantId(), mobilePayReq.getAppId(), mobilePayReq.getApiKey());

        //获取参数
        String orderCode = mobilePayReq.getOrderCode();
        BigDecimal payMoney = mobilePayReq.getPayMoney();
        String remark = mobilePayReq.getRemark();
        String attach = mobilePayReq.getAttach();
        Long memberId = mobilePayReq.getMemberId();
        Long memberRoleId = mobilePayReq.getMemberRoleId();
        String serviceType = mobilePayReq.getServiceType();
        Integer payType = mobilePayReq.getPayType();

        //封装微信附加数据
        WeChatPayAttachDTO wechatPayAttachDTO = new WeChatPayAttachDTO();
        wechatPayAttachDTO.setPayType(payType);
        wechatPayAttachDTO.setMemberId(memberId);
        wechatPayAttachDTO.setMemberRoleId(memberRoleId);
        wechatPayAttachDTO.setAttach(attach);
        wechatPayAttachDTO.setServiceType(serviceType);
        wechatPayAttachDTO.setNonce(mobilePayReq.getNonce());
        String attachJson = SerializeUtil.serialize(wechatPayAttachDTO);
        //微信支付需要的参数
        String nonceStr = UUIDUtil.randomUUID();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("appid", mobilePayReq.getAppId());                                                          //程序ID
        paramMap.put("mch_id", mobilePayReq.getMerchantId());                                                              //商户号
        paramMap.put("nonce_str", nonceStr);                                                        //随机字符串
        paramMap.put("body", remark);                                                               //商品描述
        paramMap.put("out_trade_no", orderCode);                                                    //商户订单号
        paramMap.put("fee_type", "CNY");                                                            //商户订单号
        paramMap.put("total_fee", String.valueOf(payMoney.multiply(BigDecimal.valueOf(100)).longValue()));    //交易金额
        paramMap.put("trade_type", WeChatPayUtil.APP);                                              //交易类型
        paramMap.put("spbill_create_ip", IpUtil.getIpAddr(request));                   //客户端id
        paramMap.put("notify_url", payGateWayConfig.getNotifyUrl() + WeChatPayUtil.NOTIFY_URL);      //支付结果通知的回调地址
        paramMap.put("sign_type", "MD5");                                                           //签名类型
        if (StringUtils.isNotEmpty(attachJson)) {                                                     //附加数据
            paramMap.put("attach", attachJson);
        }
        try {
            //生成签名
            String sign = WeChatPayUtil.createSign(paramMap, mobilePayReq.getApiKey(), "UTF-8");
            paramMap.put("sign", sign);
            //验证必填项
            if (StringUtils.isEmpty(paramMap.get("appid"))) {
                return new WeChatPayResultResp(false, "appid 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("mch_id"))) {
                return new WeChatPayResultResp(false, "mch_id 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("nonce_str"))) {
                return new WeChatPayResultResp(false, "nonce_str 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("body"))) {
                return new WeChatPayResultResp(false, "body 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("out_trade_no"))) {
                return new WeChatPayResultResp(false, "out_trade_no 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("total_fee"))) {
                return new WeChatPayResultResp(false, "total_fee 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("spbill_create_ip"))) {
                return new WeChatPayResultResp(false, "spbill_create_ip 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("notify_url"))) {
                return new WeChatPayResultResp(false, "notify_url 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("trade_type"))) {
                return new WeChatPayResultResp(false, "trade_type 参数为空");
            }
            if (StringUtils.isEmpty(sign)) {
                return new WeChatPayResultResp(false, "sign 参数为空");
            }

            //参数转成XML
            String requestXML = WeChatPayUtil.getRequestXml(paramMap);
            //预支付
            String resultXML = WeChatPayUtil.httpsRequest(WeChatPayUtil.PAY_URL, "POST", requestXML);
            //返回结果转成Map
            Map<String, String> resultMap = WeChatPayUtil.xmlToMap(resultXML);
            //验证返回结果
            String returnCode = resultMap.get("return_code");
            if (WeChatPayUtil.SUCCESS.equals(returnCode)) {
                //验证支付结果
                String resultCode = resultMap.get("result_code");
                if (WeChatPayUtil.SUCCESS.equals(resultCode)) {
                    //获取统一下单接口返回的prepay_id
                    String prepay_id = resultMap.get("prepay_id");
                    //组装返回到app页面参数
                    Map<String, String> result = new HashMap<>();
                    result.put("appid", mobilePayReq.getAppId());
                    result.put("partnerid", mobilePayReq.getMerchantId());
                    result.put("prepayid", prepay_id);
                    result.put("noncestr", nonceStr);
                    result.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
                    result.put("package", "Sign=WXPay");
                    //生成签名
                    sign = WeChatPayUtil.createSign(result, mobilePayReq.getApiKey(), "UTF-8");
                    result.put("sign", sign);
                    System.out.println(result);
                    //删除关键信息
                    result.remove("appid");
                    result.remove("package");
                    return new WeChatPayResultResp(true, result);
                } else {
                    return new WeChatPayResultResp(false, resultMap.get("err_code_des"));
                }
            }
            //返回错误信息
            return new WeChatPayResultResp(false, resultMap.get("return_msg"));
        } catch (Exception e) {
            log.error("app支付(在app软件中直接调起微信支付): 统一下单异常：", e);
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL);
        }
    }

    /**
     * JSAPI支付(小程序调起微信支付)
     */
    @Override
    public WeChatPayResultResp jsAPIPay(MobilePayReq mobilePayReq, HttpServletRequest request) {
        //从订单服务查询支付参数
        if (StringUtils.isAnyEmpty(mobilePayReq.getMerchantId(), mobilePayReq.getApiKey(), mobilePayReq.getAppId(), mobilePayReq.getAppKey())) {
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(mobilePayReq.getMemberId());
            feignVO.setRoleId(mobilePayReq.getMemberRoleId());
            feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new WeChatPayResultResp(false, parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new WeChatPayResultResp(false, ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            // 重平台后台获取参数
            resetParams(parameterResult, mobilePayReq);

        }

        //生成用于缓存Key的随机字符串，并作为透传参数传递给微信支付，在返回时从缓存中查询秘钥信息
        if (StringUtils.isEmpty(mobilePayReq.getNonce())) {
            mobilePayReq.setNonce(RandomNumberUtil.randomUniqueNumber());
        }

        //缓存支付参数
        payCacheService.cacheWeChatPayParameters(mobilePayReq.getMemberId(), mobilePayReq.getMemberRoleId(), mobilePayReq.getPayType(), mobilePayReq.getNonce(), mobilePayReq.getMerchantId(), mobilePayReq.getAppId(), mobilePayReq.getApiKey());

        //获取参数
        String orderCode = mobilePayReq.getOrderCode();
        BigDecimal payMoney = mobilePayReq.getPayMoney();
        String remark = mobilePayReq.getRemark();
        String attach = mobilePayReq.getAttach();
        Long memberId = mobilePayReq.getMemberId();
        Long memberRoleId = mobilePayReq.getMemberRoleId();
        String serviceType = mobilePayReq.getServiceType();
        Integer payType = mobilePayReq.getPayType();
        String appletAppId = mobilePayReq.getAppletAppId();
        String appletAppKey = mobilePayReq.getAppletAppKey();
        String merchantId = mobilePayReq.getMerchantId();
        String jsCode = mobilePayReq.getJsCode();
        String apiKey = mobilePayReq.getApiKey();

        //封装微信附加数据
        //由于微信附加信息最长128字符，所以改为用ObjectMapper进行序列化，同时AttachInfo的每个字段增加了@JsonProperty注解
        //这样可以简短附加数据的长度
        WeChatPayAttachDTO wechatPayAttachDTO = new WeChatPayAttachDTO();
        wechatPayAttachDTO.setPayType(payType);
        wechatPayAttachDTO.setMemberId(memberId);
        wechatPayAttachDTO.setMemberRoleId(memberRoleId);
        wechatPayAttachDTO.setAttach(attach);
        wechatPayAttachDTO.setServiceType(serviceType);
        wechatPayAttachDTO.setNonce(mobilePayReq.getNonce());
        String attachJson = SerializeUtil.serialize(wechatPayAttachDTO);
        //微信支付需要的参数
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("appid", appletAppId);                                                         //程序ID
        paramMap.put("mch_id", merchantId);                                                         //商户号
        paramMap.put("nonce_str", UUIDUtil.randomUUID());                                     //随机字符串
        paramMap.put("body", remark);                                                               //商品描述
        paramMap.put("out_trade_no", orderCode);                                                    //商户订单号
        paramMap.put("total_fee", String.valueOf(payMoney.multiply(BigDecimal.valueOf(100)).longValue()));    //交易金额
        paramMap.put("spbill_create_ip", IpUtil.getIpAddr(request));                   //客户端ip
        paramMap.put("notify_url", payGateWayConfig.getNotifyUrl() + WeChatPayUtil.NOTIFY_URL);      //支付结果通知的回调地址
        paramMap.put("time_start", String.valueOf(System.currentTimeMillis() / 1000));              //交易时间
        paramMap.put("trade_type", WeChatPayUtil.JS_API);                                           //交易类型
        if (StringUtils.isNotEmpty(attachJson)) {                                                     //附加数据
            paramMap.put("attach", attachJson);
        }
        //获取openId
        Map<String, String> map = new HashMap<>();
        if (CommonBooleanEnum.YES.getCode().equals(mobilePayReq.getWechatBrowser())) {
            // 微信内置浏览器H5调起微信小程序支付
            String param = "appid=" + appletAppId + "&secret=" + appletAppKey + "&code=" + jsCode + "&grant_type=" + WeChatPayUtil.GRANT_TYPE;
            log.info("======================请求微信openId的请求参数：{}", param);
            String resultStr = WeChatPayUtil.httpsRequest(WeChatPayUtil.INNER_H5_URL, "GET", param);
            map = Optional.ofNullable(JsonUtil.toObj(resultStr, new TypeReference<HashMap<String, String>>() {
            })).orElseGet(HashMap::new);
            log.info("======================请求微信openId返回结果：{}", resultStr);
        } else {
            // 微信小程序支付
            String param = "appid=" + appletAppId + "&secret=" + appletAppKey + "&js_code=" + jsCode + "&grant_type=" + WeChatPayUtil.GRANT_TYPE;
            log.info("======================请求微信openId的请求参数：{}", param);
            String resultStr = WeChatPayUtil.httpsRequest(WeChatPayUtil.LOGIN_URL, "POST", param);
            map = Optional.ofNullable(JsonUtil.toObj(resultStr, new TypeReference<HashMap<String, String>>() {
            })).orElseGet(HashMap::new);
            log.info("======================请求微信openId返回结果：{}", resultStr);
        }

        String openId = map.get("openid");
        if (openId != null && !openId.isEmpty()) {
            paramMap.put("openid", openId);
        } else {
            return new WeChatPayResultResp(false, "获取openid参数出错!");
        }

        try {
            //生成签名
            String sign = WeChatPayUtil.createSign(paramMap, apiKey, "UTF-8");
            paramMap.put("sign", sign);
            //验证必填项
            if (StringUtils.isEmpty(paramMap.get("appid"))) {
                return new WeChatPayResultResp(false, "appid 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("mch_id"))) {
                return new WeChatPayResultResp(false, "mch_id 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("nonce_str"))) {
                return new WeChatPayResultResp(false, "nonce_str 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("body"))) {
                return new WeChatPayResultResp(false, "body 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("out_trade_no"))) {
                return new WeChatPayResultResp(false, "out_trade_no 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("total_fee"))) {
                return new WeChatPayResultResp(false, "total_fee 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("spbill_create_ip"))) {
                return new WeChatPayResultResp(false, "spbill_create_ip 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("notify_url"))) {
                return new WeChatPayResultResp(false, "notify_url 参数为空");
            }
            if (StringUtils.isEmpty(paramMap.get("trade_type"))) {
                return new WeChatPayResultResp(false, "trade_type 参数为空");
            }
            if (StringUtils.isEmpty(sign)) {
                return new WeChatPayResultResp(false, "sign 参数为空");
            }

            //参数转成XML
            String requestXML = WeChatPayUtil.getRequestXml(paramMap);
            //预支付
            String resultXML = WeChatPayUtil.httpsRequest(WeChatPayUtil.PAY_URL, "POST", requestXML);
            //返回结果转成Map
            Map<String, String> resultMap = WeChatPayUtil.xmlToMap(resultXML);
            //验证返回结果
            String returnCode = resultMap.get("return_code");
            if (WeChatPayUtil.SUCCESS.equals(returnCode)) {
                //验证支付结果
                String resultCode = resultMap.get("result_code");
                if (WeChatPayUtil.SUCCESS.equals(resultCode)) {
                    //获取统一下单接口返回的prepay_id
                    String prepay_id = resultMap.get("prepay_id");
                    //组装返回到小程序页面参数
                    Map<String, String> result = new HashMap<>();
                    result.put("appId", appletAppId);
                    result.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
                    result.put("nonceStr", UUIDUtil.randomUUID());
                    result.put("package", "prepay_id=" + prepay_id);
                    result.put("signType", WeChatPayUtil.SIGN_TYPE);
                    //生成签名
                    sign = WeChatPayUtil.createSign(result, apiKey, "UTF-8");
                    result.put("paySign", sign);
                    return new WeChatPayResultResp(true, result);
                } else {
                    return new WeChatPayResultResp(false, resultMap.get("err_code_des"));
                }
            }
            //返回错误信息
            return new WeChatPayResultResp(false, resultMap.get("return_msg"));
        } catch (Exception e) {
            log.error("JSAPI支付(小程序调起微信支付): 统一下单异常：", e);
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL);
        }
    }

    /**
     * H5支付(微信)
     */
    @Override
    public WeChatPayResultResp h5Pay(MobilePayReq mobilePayReq, HttpServletRequest request) {
        //获取参数
        String orderCode = mobilePayReq.getOrderCode();
        BigDecimal payMoney = mobilePayReq.getPayMoney();
        String remark = mobilePayReq.getRemark();
        String attach = mobilePayReq.getAttach();
        Long memberId = mobilePayReq.getMemberId();
        Long memberRoleId = mobilePayReq.getMemberRoleId();
        String serviceType = mobilePayReq.getServiceType();
        Integer payType = mobilePayReq.getPayType();


        //从订单服务查询支付参数
        if (checkParamIfNull(mobilePayReq)) {
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = findPayParamsFromOrderService(mobilePayReq);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new WeChatPayResultResp(false, parameterResult.getMessage());
            }
            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new WeChatPayResultResp(false, ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            // 获取支付参数
            resetParams(parameterResult, mobilePayReq);
        }

        //生成用于缓存Key的随机字符串，并作为透传参数传递给微信支付，在返回时从缓存中查询秘钥信息
        setNonce(mobilePayReq);

        //随机数生成
        String nonceStr = mobilePayReq.getNonce();

        //缓存支付参数
        cachePayParams(mobilePayReq);

        //实例化WxPayService
        WxPayService wxPayService = getWxPayService(mobilePayReq);

        //封装微信附加数据
        String attachJson = getAttachInfoJson(attach, memberId, memberRoleId, nonceStr, serviceType, payType);

        //封装微信H5统一下单的请求参数
        WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = getH5WxPayUnifiedOrderRequest(mobilePayReq, request, nonceStr, orderCode, payMoney, remark, attachJson);
        log.info("=================微信H5统一下单请求参数：wxPayUnifiedOrderRequest={}", wxPayUnifiedOrderRequest);
        try {
            //预支付
            WxPayUnifiedOrderResult wxPayUnifiedOrderResult = wxPayService.unifiedOrder(wxPayUnifiedOrderRequest);
            log.info("=================微信H5统一下单返回结果：wxPayUnifiedOrderResult={}", wxPayUnifiedOrderResult);
            String mwebUrl = wxPayUnifiedOrderResult.getMwebUrl();
            String returnCode = wxPayUnifiedOrderResult.getReturnCode();
            String returnMsg = wxPayUnifiedOrderResult.getReturnMsg();
            String resultCode = wxPayUnifiedOrderResult.getResultCode();
            String errCodeDes = wxPayUnifiedOrderResult.getErrCodeDes();
            if (WeChatPayUtil.SUCCESS.equals(returnCode)) {
                //验证支付结果
                if (WeChatPayUtil.SUCCESS.equals(resultCode)) {
                    //获取统一下单接口返回的mwebUrl
                    return new WeChatPayResultResp(true, getWxPayMwebOrderResult(mwebUrl));
                } else {
                    return new WeChatPayResultResp(false, errCodeDes);
                }
            }
            //返回错误信息
            return new WeChatPayResultResp(false, returnMsg);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL);
        }
    }

    /**
     * H5-获取H5支付地址
     */
    private WxPayMwebOrderResult getWxPayMwebOrderResult(String mwebUrl) {
        WxPayMwebOrderResult wxPayMwebOrderResult = new WxPayMwebOrderResult();
        wxPayMwebOrderResult.setMwebUrl(mwebUrl);
        return wxPayMwebOrderResult;
    }

    /**
     * H5-封装微信统一下单的请求参数
     *
     * @param mobilePayReq 请求支付参数
     * @param request      HttpServletRequest
     * @param nonceStr     随机数
     * @param orderCode    订单编号
     * @param payMoney     支付金额
     * @param remark       商品描述
     * @param attachJson   微信附加数据
     */
    private WxPayUnifiedOrderRequest getH5WxPayUnifiedOrderRequest(MobilePayReq mobilePayReq, HttpServletRequest request, String nonceStr, String orderCode, BigDecimal payMoney, String remark, String attachJson) {
        WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = new WxPayUnifiedOrderRequest();
        wxPayUnifiedOrderRequest.setAppid(mobilePayReq.getAppId());
        wxPayUnifiedOrderRequest.setMchId(mobilePayReq.getMerchantId());
        wxPayUnifiedOrderRequest.setNonceStr(nonceStr);
        wxPayUnifiedOrderRequest.setBody(remark);
        wxPayUnifiedOrderRequest.setOutTradeNo(orderCode);
        wxPayUnifiedOrderRequest.setTotalFee(payMoney.multiply(BigDecimal.valueOf(100)).intValue());
        wxPayUnifiedOrderRequest.setSpbillCreateIp(IpUtil.getIpAddr(request));
        wxPayUnifiedOrderRequest.setNotifyUrl(payGateWayConfig.getNotifyUrl() + WeChatPayUtil.NOTIFY_URL);
        wxPayUnifiedOrderRequest.setTradeType(WeChatPayUtil.MWEB);
        if (StringUtils.isNotEmpty(attachJson)) {
            wxPayUnifiedOrderRequest.setAttach(attachJson);
        }
        String unifiedOrderSign = SignUtils.createSign(wxPayUnifiedOrderRequest, "MD5", mobilePayReq.getApiKey(), null);
        wxPayUnifiedOrderRequest.setSign(unifiedOrderSign);
        log.info("微信统一下单的请求参数:AppId:{},MacId:{},NonceStr:{},Body:{},OutTradeNo:{},TotalFee:{},SpbillCreateIp:{},NotifyUrl:{},TradeType:{},Attach:{},Sign:{}",
                wxPayUnifiedOrderRequest.getAppid(),
                wxPayUnifiedOrderRequest.getMchId(),
                wxPayUnifiedOrderRequest.getNonceStr(),
                wxPayUnifiedOrderRequest.getBody(),
                wxPayUnifiedOrderRequest.getOutTradeNo(),
                wxPayUnifiedOrderRequest.getTotalFee(),
                wxPayUnifiedOrderRequest.getSpbillCreateIp(),
                wxPayUnifiedOrderRequest.getNotifyUrl(),
                wxPayUnifiedOrderRequest.getTradeType(),
                wxPayUnifiedOrderRequest.getAttach(),
                wxPayUnifiedOrderRequest.getSign());
        return wxPayUnifiedOrderRequest;
    }

    /**
     * H5-封装微信附加数据
     *
     * @param attach       请求中的参数，用于回调时校验的
     * @param memberId     会员id
     * @param memberRoleId 角色id
     * @param serviceType  微服务类型
     * @param payType      支付类型
     */
    private String getAttachInfoJson(String attach, Long memberId, Long memberRoleId, String norceStr, String serviceType, Integer payType) {
        WeChatPayAttachDTO wechatPayAttachDTO = new WeChatPayAttachDTO();
        wechatPayAttachDTO.setPayType(payType);
        wechatPayAttachDTO.setMemberId(memberId);
        wechatPayAttachDTO.setMemberRoleId(memberRoleId);
        wechatPayAttachDTO.setNonce(norceStr);
        wechatPayAttachDTO.setAttach(attach);
        wechatPayAttachDTO.setServiceType(serviceType);
        return SerializeUtil.serialize(wechatPayAttachDTO);
    }


    /**
     * H5-实例化WxPayService
     */
    private WxPayService getWxPayService(MobilePayReq mobilePayReq) {
        WxPayService wxPayService = new WxPayServiceImpl();
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setMchId(mobilePayReq.getMerchantId());
        payConfig.setAppId(mobilePayReq.getAppId());
        payConfig.setMchKey(mobilePayReq.getApiKey());
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }

    /**
     * H5-缓存支付参数
     */
    private void cachePayParams(MobilePayReq mobilePayReq) {
        payCacheService.cacheWeChatPayParameters(mobilePayReq.getMemberId(), mobilePayReq.getMemberRoleId(), mobilePayReq.getPayType(), mobilePayReq.getNonce(), mobilePayReq.getMerchantId(), mobilePayReq.getAppId(), mobilePayReq.getApiKey());
    }

    /**
     * H5-生成用于缓存Key的随机字符串，并作为透传参数传递给微信支付，在返回时从缓存中查询秘钥信息
     */
    private void setNonce(MobilePayReq mobilePayReq) {
        if (StringUtils.isEmpty(mobilePayReq.getNonce())) {
            mobilePayReq.setNonce(RandomNumberUtil.randomUniqueNumber());
        }
    }

    /**
     * H5-重新设置支付参数
     */
    private void resetParams(WrapperResp<PaymentParameterFeignDetailResp> parameterResult, MobilePayReq mobilePayReq) {
        parameterResult.getData().getParameters().forEach(p -> {
            if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)) {
                mobilePayReq.setMerchantId(p.getValue());
            } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)) {
                mobilePayReq.setApiKey(p.getValue());
            } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APPLET_APP_ID)) {
                mobilePayReq.setAppletAppId(p.getValue());
            } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APPLET_APP_KEY)) {
                mobilePayReq.setAppletAppKey(p.getValue());
            } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)) {
                mobilePayReq.setAppId(p.getValue());
            }
        });

        // 微信内置浏览器H5支付-使用公众号APPID和APIKEY代替小程序的APPID和APIKEY
        Integer wechatBrowser = mobilePayReq.getWechatBrowser();
        if (CommonBooleanEnum.YES.getCode().equals(wechatBrowser)) {
            parameterResult.getData().getParameters().forEach(p -> {
                if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_PUBLIC_APP_ID)) {
                    mobilePayReq.setAppletAppId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_PUBLIC_API_KEY)) {
                    mobilePayReq.setAppletAppKey(p.getValue());
                }
            });
        }
        log.info("=================支付参数：mobilePayRequest={}", mobilePayReq);
    }

    /**
     * H5-从订单服务中获取支付参数
     */
    private WrapperResp<PaymentParameterFeignDetailResp> findPayParamsFromOrderService(MobilePayReq mobilePayReq) {
        OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
        feignVO.setMemberId(mobilePayReq.getMemberId());
        feignVO.setRoleId(mobilePayReq.getMemberRoleId());
        feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
        return orderFeignService.findPaymentParameters(feignVO);
    }

    /**
     * 从订单服务中获取支付参数
     *
     * @param memberId     收款方会员id
     * @param memberRoleId 收款方会员角色id
     */
    private WrapperResp<PaymentParameterFeignDetailResp> findPayParamsFromOrderService(Long memberId, Long memberRoleId) {
        OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
        feignVO.setMemberId(memberId);
        feignVO.setRoleId(memberRoleId);
        feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
        return orderFeignService.findPaymentParameters(feignVO);
    }

    /**
     * H5-校验支付必要参数是否为空
     */
    private boolean checkParamIfNull(MobilePayReq mobilePayReq) {
        return StringUtils.isAnyEmpty(
                mobilePayReq.getMerchantId(),
                mobilePayReq.getApiKey(),
                mobilePayReq.getAppId(),
                mobilePayReq.getAppKey());
    }

    /**
     * app支付
     */
    @Override
    public WeChatPayResultResp appPay_new(MobilePayReq mobilePayReq, HttpServletRequest request) {
        //获取参数
        String orderCode = mobilePayReq.getOrderCode();
        BigDecimal payMoney = mobilePayReq.getPayMoney();
        String remark = mobilePayReq.getRemark();
        String attach = mobilePayReq.getAttach();
        Long memberId = mobilePayReq.getMemberId();
        Long memberRoleId = mobilePayReq.getMemberRoleId();
        String serviceType = mobilePayReq.getServiceType();
        Integer payType = mobilePayReq.getPayType();
        //获取支付参数
        //从订单服务查询支付参数
        if (StringUtils.isAnyEmpty(mobilePayReq.getMerchantId(), mobilePayReq.getApiKey(), mobilePayReq.getAppId())) {
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(mobilePayReq.getMemberId());
            feignVO.setRoleId(mobilePayReq.getMemberRoleId());
            feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new WeChatPayResultResp(false, parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new WeChatPayResultResp(false, ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            parameterResult.getData().getParameters().forEach(p -> {
                if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)) {
                    mobilePayReq.setMerchantId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)) {
                    mobilePayReq.setAppId(p.getValue());
                } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)) {
                    mobilePayReq.setApiKey(p.getValue());
                }
            });
        }

        //生成用于缓存Key的随机字符串，并作为透传参数传递给微信支付，在返回时从缓存中查询秘钥信息
        if (StringUtils.isEmpty(mobilePayReq.getNonce())) {
            mobilePayReq.setNonce(RandomNumberUtil.randomUniqueNumber());
        }

        //缓存支付参数
        payCacheService.cacheWeChatPayParameters(mobilePayReq.getMemberId(), mobilePayReq.getMemberRoleId(), mobilePayReq.getPayType(), mobilePayReq.getNonce(), mobilePayReq.getMerchantId(), mobilePayReq.getAppId(), mobilePayReq.getApiKey());

        //实例化WxPayService
        WxPayService wxPayService = new WxPayServiceImpl();
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setMchId(mobilePayReq.getMerchantId());
        payConfig.setAppId(mobilePayReq.getAppId());
        payConfig.setMchKey(mobilePayReq.getApiKey());
        wxPayService.setConfig(payConfig);

        //封装微信附加数据
        WeChatPayAttachDTO wechatPayAttachDTO = new WeChatPayAttachDTO();
        wechatPayAttachDTO.setPayType(payType);
        wechatPayAttachDTO.setMemberId(memberId);
        wechatPayAttachDTO.setMemberRoleId(memberRoleId);
        wechatPayAttachDTO.setAttach(attach);
        wechatPayAttachDTO.setServiceType(serviceType);
        String attachJson = SerializeUtil.serialize(wechatPayAttachDTO);

        try {
            //封装参数
            String nonceStr = UUIDUtil.randomUUID();
            WxPayUnifiedOrderRequest wxPayUnifiedOrderRequest = new WxPayUnifiedOrderRequest();
            wxPayUnifiedOrderRequest.setAppid(mobilePayReq.getAppId());
            wxPayUnifiedOrderRequest.setMchId(mobilePayReq.getMerchantId());
            wxPayUnifiedOrderRequest.setNonceStr(nonceStr);
            wxPayUnifiedOrderRequest.setBody(remark);
            wxPayUnifiedOrderRequest.setOutTradeNo(orderCode);
            wxPayUnifiedOrderRequest.setTotalFee(payMoney.multiply(BigDecimal.valueOf(100)).intValue());
            wxPayUnifiedOrderRequest.setSpbillCreateIp(IpUtil.getIpAddr(request));
            wxPayUnifiedOrderRequest.setNotifyUrl(payGateWayConfig.getNotifyUrl() + WeChatPayUtil.NOTIFY_URL);
            wxPayUnifiedOrderRequest.setTradeType(WeChatPayUtil.APP);
            if (StringUtils.isNotEmpty(attachJson)) {
                wxPayUnifiedOrderRequest.setAttach(attachJson);
            }
            String unifiedOrderSign = SignUtils.createSign(wxPayUnifiedOrderRequest, "MD5", mobilePayReq.getApiKey(), null);
            wxPayUnifiedOrderRequest.setSign(unifiedOrderSign);

            WxPayUnifiedOrderResult wxPayUnifiedOrderResult = wxPayService.unifiedOrder(wxPayUnifiedOrderRequest);
            String returnCode = wxPayUnifiedOrderResult.getReturnCode();
            String returnMsg = wxPayUnifiedOrderResult.getReturnMsg();
            String resultCode = wxPayUnifiedOrderResult.getResultCode();
            String errCodeDes = wxPayUnifiedOrderResult.getErrCodeDes();
            String prepayId = wxPayUnifiedOrderResult.getPrepayId();
            if (WeChatPayUtil.SUCCESS.equals(returnCode)) {
                //验证支付结果
                if (WeChatPayUtil.SUCCESS.equals(resultCode)) {
                    //获取统一下单接口返回的prepay_id
                    WxPayAppOrderResult wxPayAppOrderResult = new WxPayAppOrderResult();
                    wxPayAppOrderResult.setAppId(mobilePayReq.getAppId());
                    wxPayAppOrderResult.setNonceStr(nonceStr);
                    wxPayAppOrderResult.setTimeStamp(String.valueOf(System.currentTimeMillis() / 1000));
                    wxPayAppOrderResult.setPrepayId(prepayId);
                    String wxPayAppOrderSign = SignUtils.createSign(wxPayAppOrderResult, "MD5", mobilePayReq.getApiKey(), null);
                    wxPayAppOrderResult.setPartnerId(mobilePayReq.getMerchantId());
                    wxPayAppOrderResult.setPackageValue("Sign=WXPay");
                    wxPayAppOrderResult.setSign(wxPayAppOrderSign);
                    return new WeChatPayResultResp(true, wxPayAppOrderResult);
                } else {
                    return new WeChatPayResultResp(false, errCodeDes);
                }
            }
            //返回错误信息
            return new WeChatPayResultResp(false, returnMsg);
        } catch (Exception e) {
            log.error("app支付: 统一下单异常：", e);
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_PAY_FAIL);
        }
    }

    /**
     * 查询订单
     */
    @Override
    public WeChatPayResultResp orderQuery(WeChatOrderQueryReq weChatOrderQueryReq) {
        try {
            //获取参数
            Long memberId = weChatOrderQueryReq.getMemberId();
            Long memberRoleId = weChatOrderQueryReq.getMemberRoleId();
            String outTradeNo = weChatOrderQueryReq.getOutTradeNo();
            String transactionId = weChatOrderQueryReq.getTransactionId();
            Integer payType = weChatOrderQueryReq.getPayType();
            //从订单服务查询支付参数
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(memberId);
            feignVO.setRoleId(memberRoleId);
            feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new WeChatPayResultResp(false, parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new WeChatPayResultResp(false, ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            WxPayConfig payConfig = new WxPayConfig();
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)).findFirst().ifPresent(p -> payConfig.setMchId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)).findFirst().ifPresent(p -> payConfig.setAppId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)).findFirst().ifPresent(p -> payConfig.setMchKey(p.getValue()));

            //实例化WxPayService
            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(payConfig);

            //封装参数
            WxPayOrderQueryRequest request = new WxPayOrderQueryRequest();
            request.setAppid(payConfig.getAppId());                                                  //程序ID
            request.setMchId(payConfig.getMchId());                                                  //商户号
            request.setNonceStr(UUIDUtil.randomUUID());                                              //随机字符串
            request.setTransactionId(transactionId);                                                 //微信订单号
            request.setOutTradeNo(outTradeNo);                                                       //商户订单号
            request.setSign(WeChatPayUtil.sign(request, "MD5", payConfig.getMchKey()));     //签名
            WxPayOrderQueryResult orderQueryResult = wxPayService.queryOrder(request);
            //验证返回结果
            String returnCode = orderQueryResult.getReturnCode();
            if (WeChatPayUtil.SUCCESS.equals(returnCode)) {
                //验证结果
                String resultCode = orderQueryResult.getResultCode();
                if (WeChatPayUtil.SUCCESS.equals(resultCode)) {
                    return new WeChatPayResultResp(true, orderQueryResult);
                } else {
                    return new WeChatPayResultResp(false, orderQueryResult.getErrCodeDes());
                }
            }
            //返回错误信息
            return new WeChatPayResultResp(false, orderQueryResult.getReturnMsg());
        } catch (WxPayException e) {
            String errCode = e.getErrCode();
            if (StringUtils.isNotEmpty(errCode)) {
                return new WeChatPayResultResp(false, WechatErrorEnum.getMessage(e.getErrCode()));
            } else {
                return new WeChatPayResultResp(false, e.getMessage());
            }
        }
    }

    /**
     * 关闭订单
     */
    @Override
    public WeChatPayResultResp closeOrder(WeChatCloseOrderReq weChatCloseOrderReq) {
        try {
            //获取参数
            Long memberId = weChatCloseOrderReq.getMemberId();
            Long memberRoleId = weChatCloseOrderReq.getMemberRoleId();
            String outTradeNo = weChatCloseOrderReq.getOutTradeNo();
            Integer payType = weChatCloseOrderReq.getPayType();

            //从订单服务查询支付参数
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(memberId);
            feignVO.setRoleId(memberRoleId);
            feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new WeChatPayResultResp(false, parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new WeChatPayResultResp(false, ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            WxPayConfig payConfig = new WxPayConfig();
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)).findFirst().ifPresent(p -> payConfig.setMchId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)).findFirst().ifPresent(p -> payConfig.setAppId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)).findFirst().ifPresent(p -> payConfig.setMchKey(p.getValue()));

            //实例化WxPayService
            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(payConfig);

            //封装参数
            WxPayOrderCloseRequest request = new WxPayOrderCloseRequest();
            request.setAppid(payConfig.getAppId());                                                  //程序ID
            request.setMchId(payConfig.getMchId());                                                  //商户号
            request.setNonceStr(UUIDUtil.randomUUID());                                              //随机字符串
            request.setOutTradeNo(outTradeNo);                                                       //商户订单号
            request.setSign(WeChatPayUtil.sign(request, "MD5", payConfig.getMchKey()));     //签名
            WxPayOrderCloseResult orderCloseResult = wxPayService.closeOrder(request);
            //验证返回结果
            String returnCode = orderCloseResult.getReturnCode();
            if (WeChatPayUtil.SUCCESS.equals(returnCode)) {
                //验证支付结果
                String resultCode = orderCloseResult.getResultCode();
                if (WeChatPayUtil.SUCCESS.equals(resultCode)) {
                    return new WeChatPayResultResp(true, orderCloseResult);
                } else {
                    return new WeChatPayResultResp(false, orderCloseResult.getErrCodeDes());
                }
            }
            //返回错误信息
            return new WeChatPayResultResp(false, orderCloseResult.getReturnMsg());
        } catch (WxPayException e) {
            String errCode = e.getErrCode();
            if (StringUtils.isNotEmpty(errCode)) {
                return new WeChatPayResultResp(false, WechatErrorEnum.getMessage(e.getErrCode()));
            } else {
                return new WeChatPayResultResp(false, e.getMessage());
            }
        }
    }

    /**
     * 申请退款
     */
    @Override
    public WeChatPayResultResp refund(WeChatRefundReq weChatRefundReq) {
        try {
            //获取参数
            Long memberId = weChatRefundReq.getMemberId();
            Long memberRoleId = weChatRefundReq.getMemberRoleId();
//            String outTradeNo = weChatRefundRequest.getOutTradeNo();
//            String transactionId = weChatRefundRequest.getTransactionId();
            String outTradeNo = StringUtils.isEmpty(weChatRefundReq.getOutTradeNo()) ? "" : weChatRefundReq.getOutTradeNo();
            String transactionId = StringUtils.isEmpty(weChatRefundReq.getTransactionId()) ? "" : weChatRefundReq.getTransactionId();

            BigDecimal payMoney = weChatRefundReq.getPayMoney();
            String outRefundNo = weChatRefundReq.getOutRefundNo();
            BigDecimal refundMoney = weChatRefundReq.getRefundMoney();
            Integer payType = weChatRefundReq.getPayType();
            //从订单服务查询支付参数
            if (StringUtils.isAnyEmpty(weChatRefundReq.getMerchantId(), weChatRefundReq.getApiKey(), weChatRefundReq.getAppId(), weChatRefundReq.getKeyPath())) {
                OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
                feignVO.setMemberId(weChatRefundReq.getMemberId());
                feignVO.setRoleId(weChatRefundReq.getMemberRoleId());
                feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
                WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
                if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                    return new WeChatPayResultResp(false, parameterResult.getMessage());
                }

                if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                    return new WeChatPayResultResp(false, ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
                }

                parameterResult.getData().getParameters().forEach(p -> {
                    if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)) {
                        weChatRefundReq.setMerchantId(p.getValue());
                    } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)) {
                        weChatRefundReq.setAppId(p.getValue());
                    } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)) {
                        weChatRefundReq.setApiKey(p.getValue());
                    } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_KEY_PATH)) {
                        weChatRefundReq.setKeyPath(p.getValue());
                    }
                });
            }

            WxPayConfig payConfig = new WxPayConfig();
            payConfig.setMchId(weChatRefundReq.getMerchantId());
            payConfig.setAppId(weChatRefundReq.getAppId());
            payConfig.setMchKey(weChatRefundReq.getApiKey());
            payConfig.setKeyPath(weChatRefundReq.getKeyPath());

            //实例化WxPayService
            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(payConfig);

            //封装参数
            WxPayRefundRequest request = new WxPayRefundRequest();
            request.setAppid(payConfig.getAppId());                                                  //程序ID
            request.setMchId(payConfig.getMchId());                                                  //商户号
            request.setNonceStr(UUIDUtil.randomUUID());                                              //随机字符串
            if (StringUtils.isNotEmpty(transactionId)) {
                request.setTransactionId(transactionId);                                             //微信订单号
            } else {
                request.setOutTradeNo(outTradeNo);                                                   //商户订单号
            }
            request.setTotalFee(payMoney.multiply(BigDecimal.valueOf(100)).intValue());                        //订单金额
            request.setOutRefundNo(outRefundNo);                                                     //商户退款单号
            request.setRefundFee(payMoney.multiply(BigDecimal.valueOf(100)).intValue());                    //退款金额
            request.setSign(WeChatPayUtil.sign(request, "MD5", payConfig.getMchKey()));     //签名
            WxPayRefundResult refundResult = wxPayService.refund(request);
            //验证返回结果
            String returnCode = refundResult.getReturnCode();
            if (WeChatPayUtil.SUCCESS.equals(returnCode)) {
                //验证支付结果
                String resultCode = refundResult.getResultCode();
                if (WeChatPayUtil.SUCCESS.equals(resultCode)) {
                    return new WeChatPayResultResp(true, refundResult.getRefundId());
                } else {
                    return new WeChatPayResultResp(false, refundResult.getErrCodeDes());
                }
            }
            //返回错误信息
            return new WeChatPayResultResp(false, refundResult.getReturnMsg());
        } catch (WxPayException e) {
            String errCode = e.getErrCode();
            if (StringUtils.isNotEmpty(errCode)) {
                return new WeChatPayResultResp(false, WechatErrorEnum.getMessage(e.getErrCode()));
            } else {
                return new WeChatPayResultResp(false, e.getMessage());
            }
        }
    }

    /**
     * 退款查询
     */
    @Override
    public WeChatPayResultResp refundQuery(WeChatRefundQueryReq weChatRefundQueryReq) {
        try {
            //获取参数
            Long memberId = weChatRefundQueryReq.getMemberId();
            Long memberRoleId = weChatRefundQueryReq.getMemberRoleId();
            String outTradeNo = weChatRefundQueryReq.getOutTradeNo();
            String transactionId = weChatRefundQueryReq.getTransactionId();
            String outRefundNo = weChatRefundQueryReq.getOutRefundNo();
            String refundId = weChatRefundQueryReq.getRefundId();
            Integer payType = weChatRefundQueryReq.getPayType();
            //从订单服务查询支付参数
            OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
            feignVO.setMemberId(memberId);
            feignVO.setRoleId(memberRoleId);
            feignVO.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
            if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
                return new WeChatPayResultResp(false, parameterResult.getMessage());
            }

            if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
                return new WeChatPayResultResp(false, ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST.getMessage());
            }

            WxPayConfig payConfig = new WxPayConfig();
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_MERCHANT_ID)).findFirst().ifPresent(p -> payConfig.setMchId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APP_ID)).findFirst().ifPresent(p -> payConfig.setAppId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_API_KEY)).findFirst().ifPresent(p -> payConfig.setMchKey(p.getValue()));

            //实例化WxPayService
            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(payConfig);

            //封装参数
            WxPayRefundQueryRequest request = new WxPayRefundQueryRequest();
            request.setAppid(payConfig.getAppId());                                                  //程序ID
            request.setMchId(payConfig.getMchId());                                                  //商户号
            request.setNonceStr(UUIDUtil.randomUUID());                                              //随机字符串
            request.setTransactionId(transactionId);                                                 //微信订单号
            request.setOutTradeNo(outTradeNo);                                                       //商户订单号
            request.setOutRefundNo(outRefundNo);                                                     //商户退款单号
            request.setRefundId(refundId);                                                           //微信退款单号
            request.setSign(WeChatPayUtil.sign(request, "MD5", payConfig.getMchKey()));     //签名
            WxPayRefundQueryResult refundQueryResult = wxPayService.refundQuery(request);
            //验证返回结果
            String returnCode = refundQueryResult.getReturnCode();
            if (WeChatPayUtil.SUCCESS.equals(returnCode)) {
                //验证支付结果
                String resultCode = refundQueryResult.getResultCode();
                if (WeChatPayUtil.SUCCESS.equals(resultCode)) {
                    return new WeChatPayResultResp(true, refundQueryResult);
                } else {
                    return new WeChatPayResultResp(false, refundQueryResult.getErrCodeDes());
                }
            }
            //返回错误信息
            return new WeChatPayResultResp(false, refundQueryResult.getReturnMsg());
        } catch (WxPayException e) {
            String errCode = e.getErrCode();
            if (StringUtils.isNotEmpty(errCode)) {
                return new WeChatPayResultResp(false, WechatErrorEnum.getMessage(e.getErrCode()));
            } else {
                return new WeChatPayResultResp(false, e.getMessage());
            }
        }
    }

    /**
     * 获取微信AccessToken<br/>
     * 先从缓存中获取，如果缓存中没有，则再请求微信接口，再缓存
     */
    @Override
    public AccessTokenResp getWeChatAccessToken(AccessTokenReq accessTokenReq) {
        getPublicPayParamFromPlatform(accessTokenReq);
        String accessTokenKey = accessTokenReq.getAppId() + "_" + accessTokenReq.getAppSecret() + "_" + "access_token";
        AccessTokenResp accessTokenResp = redisUtils.stringGet(accessTokenKey, AccessTokenResp.class, RedisConstant.REDIS_PAY_INDEX);
        if (Objects.isNull(accessTokenResp)) {
            accessTokenResp = getAccessTokenFromWechat(accessTokenReq);
            redisUtils.stringSet(accessTokenKey, JSONUtil.toJsonStr(accessTokenResp), (long) accessTokenResp.getExpiresIn(), RedisConstant.REDIS_PAY_INDEX);
        }

        return accessTokenResp.updateBy(accessTokenReq);
    }

    @Override
    public AccessTokenResp getWeChatAppletAccessToken(Integer payChannel) {
        AccessTokenReq accessTokenReq = getAppletPayParamFromPlatform(payChannel);
        String accessTokenKey = accessTokenReq.getAppId() + "_" + accessTokenReq.getAppSecret() + "_" + "access_token";
        AccessTokenResp accessTokenResp = redisUtils.stringGet(accessTokenKey, AccessTokenResp.class, RedisConstant.REDIS_PAY_INDEX);
        if (Objects.isNull(accessTokenResp)) {
            accessTokenResp = getAccessTokenFromWechat(accessTokenReq);
            redisUtils.stringSet(accessTokenKey, JSONUtil.toJsonStr(accessTokenResp), (long) accessTokenResp.getExpiresIn(), RedisConstant.REDIS_PAY_INDEX);
        }

        return accessTokenResp.updateBy(accessTokenReq);
    }

    /**
     * 从平台后台获取公众号支付参数
     */
    private void getPublicPayParamFromPlatform(AccessTokenReq accessTokenReq) {
        if (StringUtils.isAnyEmpty(accessTokenReq.getAppId(), accessTokenReq.getAppSecret())) {
            OrderPayChannelFeignReq orderPayChannelFeignReq = new OrderPayChannelFeignReq();
            orderPayChannelFeignReq.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
            WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPlatformPaymentParameters(orderPayChannelFeignReq);
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_PUBLIC_APP_ID)).findFirst().ifPresent(p -> accessTokenReq.setAppId(p.getValue()));
            parameterResult.getData().getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_PUBLIC_API_KEY)).findFirst().ifPresent(p -> accessTokenReq.setAppSecret(p.getValue()));
            if (Objects.isNull(accessTokenReq.getAppId()) || Objects.isNull(accessTokenReq.getAppSecret())) {
                throw new BusinessException(ResponseCodeEnum.PUBLIC_PAY_CONFIG_NOT_FOUND);
            }
        }
    }

    @Override
    public AccessTokenReq getAppletPayParamFromPlatform(Integer payChannel) {
        AccessTokenReq accessTokenReq = new AccessTokenReq();

        PaymentParameterFeignDetailResp paymentParameterFeignDetailResp = WrapperUtil.getDataOrThrow(
                orderFeignService.findPlatformPaymentParameters(new OrderPayChannelFeignReq(OrderPayChannelEnum.parse(payChannel)))
        );
        paymentParameterFeignDetailResp.getParameters().stream().filter(
                p -> p.getParameter().equals(OrderPaymentParameterEnum.ALLIN_WECHAT_APPLET_APP_ID)
        ).findFirst().ifPresent(p -> accessTokenReq.setAppId(p.getValue()));
        paymentParameterFeignDetailResp.getParameters().stream().filter(
                p -> p.getParameter().equals(OrderPaymentParameterEnum.ALLIN_WECHAT_APPLET_APP_KEY)
        ).findFirst().ifPresent(p -> accessTokenReq.setAppSecret(p.getValue()));
        paymentParameterFeignDetailResp.getParameters().stream().filter(
                p -> p.getParameter().equals(OrderPaymentParameterEnum.ALLIN_WX_MCHID)
        ).findFirst().ifPresent(p -> accessTokenReq.setMchid(p.getValue()));

        if (Objects.isNull(accessTokenReq.getAppId()) || Objects.isNull(accessTokenReq.getAppSecret())) {
            throw new BusinessException(ResponseCodeEnum.PAY_WECHAT_APPLET_PARAM_NOT_EXIST);
        }

        return accessTokenReq;
    }

    /**
     * 从微信中请求AccessToken
     */
    private AccessTokenResp getAccessTokenFromWechat(AccessTokenReq accessTokenReq) {
        AccessTokenResp accessTokenResp = new AccessTokenResp();
        String requestUrl = access_token_url.replace("APPID", accessTokenReq.getAppId()).replace("APPSECRET", accessTokenReq.getAppSecret());
        String result = restTemplateHandler.doGet(requestUrl, "获取access_token的接口地址");

        if (Objects.nonNull(result)) {
            JSONObject resultObject = JSONUtil.parseObj(result);
            String token = resultObject.getStr("access_token");
            Integer expiresIn = resultObject.getInt("expires_in");

            if (Objects.isNull(token) || Objects.isNull(expiresIn)) {
                throw new BusinessException(result);
            }

            accessTokenResp.setToken(token);
            accessTokenResp.setExpiresIn(expiresIn);
        }

        return accessTokenResp;
    }

    /**
     * 获取微信JsApiTicket
     */
    @Override
    public JsApiTicketResp getJsApiTicket(AccessTokenReq accessTokenReq) {
        // 从平台后台获取公众号支付参数
        getPublicPayParamFromPlatform(accessTokenReq);
        JsApiTicketResp jsApiTicketResp = new JsApiTicketResp();
        AccessTokenResp accessTokenResp = null;
        String accessTokenKey = accessTokenReq.getAppId() + "_" + accessTokenReq.getAppSecret() + "_" + "access_token";
        accessTokenResp = redisUtils.stringGet(accessTokenKey, AccessTokenResp.class, RedisConstant.REDIS_PAY_INDEX);
        if (Objects.isNull(accessTokenResp)) {
            accessTokenResp = getWeChatAccessToken(accessTokenReq);
            redisUtils.stringSet(accessTokenKey, JSONUtil.toJsonStr(accessTokenResp), (long) accessTokenResp.getExpiresIn(), RedisConstant.REDIS_PAY_INDEX);
        }
        String token = accessTokenResp.getToken();
        String requestUrl = ticket_url.replace("ACCESS_TOKEN", token);
        String result = restTemplateHandler.doGet(requestUrl, "获取JsApiTicket");
        if (Objects.nonNull(result)) {
            JSONObject resultObject = JSONUtil.parseObj(result);
            jsApiTicketResp.setTicket(resultObject.getStr("ticket"));
            jsApiTicketResp.setExpiresIn(resultObject.getInt("expires_in"));
            jsApiTicketResp.setAppId(accessTokenReq.getAppId());
            jsApiTicketResp.setAppSecret(accessTokenReq.getAppSecret());
        }
        return jsApiTicketResp;
    }

    /**
     * 获取JS-SDK签名
     */
    @Override
    public JsApiResultResp getJsApiResult(JsApiReq jsApiRequest) {
        // 从平台后台获取公众号支付参数
        getPublicPayParamFromPlatform(jsApiRequest);
        //jsApiTicket
        JsApiTicketResp jsApiTicketResp = null;
        String jsApiTicketKey = jsApiRequest.getAppId() + "_" + jsApiRequest.getAppSecret() + "_" + "jsApiTicket";
        jsApiTicketResp = redisUtils.stringGet(jsApiTicketKey, JsApiTicketResp.class, RedisConstant.REDIS_PAY_INDEX);
        if (Objects.isNull(jsApiTicketResp)) {
            jsApiTicketResp = getJsApiTicket(jsApiRequest);
            redisUtils.stringSet(jsApiTicketKey, JSONUtil.toJsonStr(jsApiTicketResp), (long) jsApiTicketResp.getExpiresIn(), RedisConstant.REDIS_PAY_INDEX);
        }
        //当前网页URL
        String url = jsApiRequest.getUrl();
        System.out.println(url);
        try {
            url = URLDecoder.decode(url, "UTF-8");
            System.out.println(url);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        //签名随机串
        String nonceStr = UUIDUtil.randomUUID();
        //当前时间戳
        long time = DateUtil.current() / 1000;
        String timestamp = String.format("%010d", time);
        StringBuilder result = new StringBuilder();
        result.append("jsapi_ticket=").append(jsApiTicketResp.getTicket());
        result.append("&");
        result.append("noncestr=").append(nonceStr);
        result.append("&");
        result.append("timestamp=").append(timestamp);
        result.append("&");
        result.append("url=").append(url);
        System.out.println(result);
        //加密
        String signature = DigestUtil.sha1Hex(result.toString());

        JsApiResultResp jsApiResultResp = new JsApiResultResp();
        jsApiResultResp.setAppId(jsApiRequest.getAppId());
        jsApiResultResp.setTimestamp(timestamp);
        jsApiResultResp.setNonceStr(nonceStr);
        jsApiResultResp.setSignature(signature);

        return jsApiResultResp;
    }

    /**
     * 微信内置浏览器H5支付
     *
     * @param mobilePayReq 支付请求参数
     * @param request      请求对象
     * @return 支付结果
     */
    @Override
    public WeChatPayResultResp innerH5pay(MobilePayReq mobilePayReq, HttpServletRequest request) {
        Integer wechatBrowser = mobilePayReq.getWechatBrowser();
        if (CommonBooleanEnum.YES.getCode().equals(wechatBrowser)) {
            // 如果是微信浏览器H5支付，则调用JSAPI支付
            return jsAPIPay(mobilePayReq, request);
        }
        throw new BusinessException(ResponseCodeEnum.INNER_WECHAT_H5_PAY);
    }

    @Override
    public AppletOpenidResp getAppletOpenid(AppletOpenidReq req) {
        WxAppletCode2SessionResp resp = eAccountService.wxAppletCode2Session(req);
        return new AppletOpenidResp(resp.getOpenid(), resp.getAppId());
    }

    @Override
    public void uploadShippingInfo(UploadShippingInfoListFeignReq uploadShippingInfoListFeignReq) {
        // 获取微信小程序AccessToken
        AccessTokenResp accessTokenResp = getWeChatAppletAccessToken(uploadShippingInfoListFeignReq.getPayChannel());

        String requestUrl = upload_shipping_info.replace("ACCESS_TOKEN", accessTokenResp.getToken());
        List<UploadShippingInfoReq> reqList = UploadShippingInfoReq.buildBy(uploadShippingInfoListFeignReq, accessTokenResp);
        reqList.forEach(req -> {
            UploadShippingInfoResp resp = restTemplateHandler.doPost(requestUrl, req, UploadShippingInfoResp.class, "上传物流信息");
            if (!Optional.ofNullable(resp).map(UploadShippingInfoResp::isBizPass).orElse(Boolean.FALSE)) {
                // 异常, 发货失败
                throw new BusinessException(ResponseCodeEnum.PAY_WECHAT_APPLET_UPLOAD_SHIPPING_INFO_FAIL);
            }
        });

    }

    @Override
    public String getUrlLink(UrlLinkDTO urlLinkDTO) {
        String appLetAppId = "";//小程序appid
        String appLetAppKey = "";//小程序密钥

        //从缓存中获取微信支付参数
        OrderPayChannelFeignReq vo = new OrderPayChannelFeignReq();
        vo.setPayChannel(OrderPayChannelEnum.WECHAT_PAY);
        WrapperResp<PaymentParameterFeignDetailResp> parameters = orderFeignService.findPlatformPaymentParameters(vo);
        if (parameters.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(parameters.getCode(), parameters.getMessage());
        }
        List<PayChannelParameterFeignDetailResp> list = parameters.getData().getParameters();
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(ResponseCodeEnum.PAY_PLATFORM_PARAMETERS_DOES_NOT_EXIST);
        }
        for (PayChannelParameterFeignDetailResp p : list) {
            if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APPLET_APP_ID)) {
                appLetAppId = p.getValue();
            } else if (p.getParameter().equals(OrderPaymentParameterEnum.WECHAT_APPLET_APP_KEY)) {
                appLetAppKey = p.getValue();
            }

        }
        //配置请求链接
        String requestUrl = access_token_url.replace("APPID", appLetAppId).replace("APPSECRET", appLetAppKey);
        String result = restTemplateHandler.doGet(requestUrl, "获取access_token的接口地址");
        if (Objects.nonNull(result)) {
            JSONObject resultObject = JSONUtil.parseObj(result);
            String accessToken = resultObject.getStr("access_token");
            //配置请求链接
            String generateUrlLink = generate_url_link.replace("ACCESS_TOKEN", accessToken);
            //根据accessToken获取url_link
            Map<String, Object> map = new HashMap<>();
            map.put("path", urlLinkDTO.getPath());
            map.put("env_version", "trial");
            map.put("is_expire", urlLinkDTO.getIsExpire());
            map.put("expire_type", urlLinkDTO.getExpireType());
            map.put("expire_time", urlLinkDTO.getExpireTime());
            map.put("expire_interval", urlLinkDTO.getExpireInterval());
            map.put("cloud_base", urlLinkDTO.getCloudBase());
            String resp = restTemplateHandler.doPost(generateUrlLink, map, "获取小程序 URL Link");
            JSONObject jsonObject = JSONUtil.parseObj(resp);
            if (jsonObject.getInt("errcode") == 0) {
                return jsonObject.getStr("url_link");
            } else {
                throw new BusinessException(jsonObject.getInt("errcode"), jsonObject.getStr("errmsg"));
            }
        }
        return null;
    }

}
