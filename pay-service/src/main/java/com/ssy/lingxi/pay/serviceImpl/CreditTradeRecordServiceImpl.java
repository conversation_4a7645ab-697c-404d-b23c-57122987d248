package com.ssy.lingxi.pay.serviceImpl;

import cn.hutool.core.date.DateUtil;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.pay.entity.do_.CreditTradeRecordDO;
import com.ssy.lingxi.pay.enums.CreditTradeOperationEnum;
import com.ssy.lingxi.pay.enums.CreditTradeStatusEnum;
import com.ssy.lingxi.pay.model.req.PageQueryCreditTradeRecordDataReq;
import com.ssy.lingxi.pay.model.resp.CreditOverdueTradeRecordAppResp;
import com.ssy.lingxi.pay.model.resp.CreditOverdueTradeRecordResp;
import com.ssy.lingxi.pay.model.resp.CreditTradeRecordPageAppResp;
import com.ssy.lingxi.pay.model.resp.CreditTradeRecordPageResp;
import com.ssy.lingxi.pay.repository.CreditTradeRecordRepository;
import com.ssy.lingxi.pay.service.ICreditTradeRecordService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 授信交易记录接口实现
 * <AUTHOR>
 * @since 2020/8/21
 * @version 2.0.0
 */
@Service
public class CreditTradeRecordServiceImpl implements ICreditTradeRecordService {

    @Resource
    private CreditTradeRecordRepository recordRepository;

    /**
     * 分页查询授信交易记录
     * <AUTHOR>
     * @since 2020/8/21
     * @param pageVO:
     **/
    @Override
    public PageDataResp<CreditTradeRecordPageResp> pageTradeRecord(PageQueryCreditTradeRecordDataReq pageVO) {

        // step 1: 组装查询条件
        Specification<CreditTradeRecordDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("creditId").as(Long.class), pageVO.getCreditId()));
            list.add(criteriaBuilder.equal(root.get("billId").as(Long.class), pageVO.getBillId()));
            list.add(criteriaBuilder.equal(root.get("isTemp").as(Integer.class), CommonBooleanEnum.NO.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // step 2: 组装分页参数（按审批时间顺序排序）
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("tradeTime").descending());

        // step 3: 分页查询数据
        Page<CreditTradeRecordDO> result = recordRepository.findAll(spec, page);

        // step 4: 组装返回数据
        List<CreditTradeRecordPageResp> resultList = result.getContent().stream().map(r -> {
            CreditTradeRecordPageResp queryVO = new CreditTradeRecordPageResp();
            queryVO.setId(r.getId());
            queryVO.setTradeCode(r.getTradeCode());
            queryVO.setTradeTime(DateUtil.format(DateUtil.date(r.getTradeTime()), "yyyy-MM-dd HH:mm"));
            queryVO.setTradeMoney(r.getTradeMoney());
            queryVO.setStatus(r.getStatus());
            queryVO.setStatusName(CreditTradeStatusEnum.getItemMessage(r.getStatus()));
            queryVO.setOperation(r.getOperation());
            queryVO.setOperationName(CreditTradeOperationEnum.getItemMessage(r.getOperation()));
            queryVO.setRemark(r.getRemark());
            queryVO.setPayProveList(r.getPayProveList());

            return queryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }

    /**
     * 获取交易记录
     * @param billIdList: 账单id列表
     * @param operation: 交易项目: 1-订单支付, 2-订单退款, 3-
     * @param tradeStatus:交易状态
     * @return
     */
    @Override
    public List<CreditOverdueTradeRecordResp> getTradeRecordList(List<Long> billIdList, Integer operation, Integer tradeStatus) {

        Specification<CreditTradeRecordDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("operation").as(Integer.class), operation));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), tradeStatus));
            // 账单id
            Expression<Integer> exp = root.get("billId").as(Integer.class);
            list.add(exp.in(billIdList));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<CreditTradeRecordDO> result = recordRepository.findAll(spec);

        // step 4: 组装返回数据
        List<CreditOverdueTradeRecordResp> resultList = result.stream().map(r -> {
            CreditOverdueTradeRecordResp queryVO = new CreditOverdueTradeRecordResp();
            queryVO.setBillId(r.getBillId());
            queryVO.setTradeCode(r.getTradeCode());
            queryVO.setTradeTime(DateUtil.format(DateUtil.date(r.getTradeTime()), "yyyy-MM-dd HH:mm"));
            queryVO.setTradeMoney(r.getTradeMoney());
            queryVO.setOperation(r.getOperation());
            queryVO.setPayProveList(r.getPayProveList());

            return queryVO;
        }).collect(Collectors.toList());

        return resultList;
    }

    /**
     * App端查询逾期交易记录
     * @param billIdList
     * @param operation
     * @param tradeStatus
     * @return
     */
    @Override
    public List<CreditOverdueTradeRecordAppResp> getTradeRecordListByApp(List<Long> billIdList, Integer operation, Integer tradeStatus){

        Specification<CreditTradeRecordDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("operation").as(Integer.class), operation));
            list.add(criteriaBuilder.equal(root.get("status").as(Integer.class), tradeStatus));
            // 账单id
            Expression<Integer> exp = root.get("billId").as(Integer.class);
            list.add(exp.in(billIdList));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        List<CreditTradeRecordDO> result = recordRepository.findAll(spec);

        // step 4: 组装返回数据
        List<CreditOverdueTradeRecordAppResp> resultList = result.stream().map(r -> {
            CreditOverdueTradeRecordAppResp queryVO = new CreditOverdueTradeRecordAppResp();
            queryVO.setBillId(r.getBillId());
            queryVO.setTradeCode(r.getTradeCode());
            queryVO.setTradeTime(r.getTradeTime());
            queryVO.setTradeMoney(r.getTradeMoney());
            queryVO.setStatus(r.getStatus());
            queryVO.setStatusName(CreditTradeStatusEnum.getItemMessage(r.getStatus()));
            queryVO.setOperation(r.getOperation());
            queryVO.setPayProveList(r.getPayProveList());

            return queryVO;
        }).collect(Collectors.toList());

        return resultList;
    }

    /**
     * App端分页查询授信交易记录
     * <AUTHOR>
     * @since 2021/3/5
     * @param pageVO: 请求参数
     * @return 操作结果
     **/
    @Override
    public PageDataResp<CreditTradeRecordPageAppResp> pageTradeRecordByApp(PageQueryCreditTradeRecordDataReq pageVO) {
        // step 1: 组装查询条件
        Specification<CreditTradeRecordDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("creditId").as(Long.class), pageVO.getCreditId()));
            list.add(criteriaBuilder.equal(root.get("billId").as(Long.class), pageVO.getBillId()));
            list.add(criteriaBuilder.equal(root.get("isTemp").as(Integer.class), CommonBooleanEnum.NO.getCode()));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // step 2: 组装分页参数（按审批时间顺序排序）
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("tradeTime").descending());

        // step 3: 分页查询数据
        Page<CreditTradeRecordDO> result = recordRepository.findAll(spec, page);

        // step 4: 组装返回数据
        List<CreditTradeRecordPageAppResp> resultList = result.getContent().stream().map(r -> {
            CreditTradeRecordPageAppResp queryVO = new CreditTradeRecordPageAppResp();
            queryVO.setId(r.getId());
            queryVO.setTradeCode(r.getTradeCode());
            queryVO.setTradeTime(r.getTradeTime());
            queryVO.setTradeMoney(r.getTradeMoney());
            queryVO.setStatus(r.getStatus());
            queryVO.setStatusName(CreditTradeStatusEnum.getItemMessage(r.getStatus()));
            queryVO.setOperation(r.getOperation());
            queryVO.setOperationName(CreditTradeOperationEnum.getItemMessage(r.getOperation()));
            queryVO.setRemark(r.getRemark());
            queryVO.setPayProveList(r.getPayProveList());

            return queryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }
}
