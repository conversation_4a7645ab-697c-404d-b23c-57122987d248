package com.ssy.lingxi.pay.serviceImpl.assetAccount;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdListReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.ReportItemResp;
import com.ssy.lingxi.common.util.BigDecimalUtil;
import com.ssy.lingxi.common.util.DateTimeUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.manage.MessageNoticeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberLevelTypeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.service.IMessageService;
import com.ssy.lingxi.component.rabbitMQ.model.req.SystemMessageReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.CheckReq;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.AccountTradeRecordResp;
import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountCashOutRecordDO;
import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountTradeRecordDO;
import com.ssy.lingxi.pay.entity.do_.assetAccount.MemberAssetAccountDO;
import com.ssy.lingxi.pay.enums.*;
import com.ssy.lingxi.pay.enums.report.AccountOperateTypeEnum;
import com.ssy.lingxi.pay.model.req.AddMemberAccountTradeRecordReq;
import com.ssy.lingxi.pay.model.req.CashOutReq;
import com.ssy.lingxi.pay.model.req.MemberAccountTradeRecordPageQueryReq;
import com.ssy.lingxi.pay.model.resp.AccountTradeRecordInfoResp;
import com.ssy.lingxi.pay.model.resp.MemberAccountTradeRecordPageQueryResp;
import com.ssy.lingxi.pay.repository.assetAccount.AccountCashOutRecordRepository;
import com.ssy.lingxi.pay.repository.assetAccount.AccountTradeRecordRepository;
import com.ssy.lingxi.pay.repository.assetAccount.MemberAssetAccountRepository;
import com.ssy.lingxi.pay.service.assetAccount.IAccountTradeService;
import com.ssy.lingxi.pay.service.assetAccount.IMemberAssetAccountService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员资金账户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
@Service
public class AccountTradeServiceImpl implements IAccountTradeService {

    @Resource
    private MemberAssetAccountRepository memberAssetAccountRepository;

    @Resource
    private AccountTradeRecordRepository accountTradeRecordRepository;

    @Resource
    private AccountCashOutRecordRepository accountCashOutRecordRepository;

    @Resource
    private IMemberAssetAccountService memberAssetAccountService;

    @Resource
    private IMessageService messageService;

    /**
     * 查询会员资金账户交易记录
     * @return
     */
    @Override
    public PageDataResp<AccountTradeRecordInfoResp> getAccountTradeRecord(PageDataReq pageDataReq, Long memberAssetAccountId) {
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize());
        Page<AccountTradeRecordDO> accountTradeRecordDOPage = accountTradeRecordRepository.findByMemberAssetAccountIdOrderByTradeTimeDesc(memberAssetAccountId, page);
        List<AccountTradeRecordInfoResp> accountTradeRecordResps = BeanUtil.copyToList(accountTradeRecordDOPage.toList(), AccountTradeRecordInfoResp.class);
        for (AccountTradeRecordInfoResp accountTradeRecordResp : accountTradeRecordResps) {
            accountTradeRecordResp.setStatusName(TradeStatusEnum.getNameByCode(accountTradeRecordResp.getStatus()));
            accountTradeRecordResp.setCategoryName(MemberAccountTradeCategoryEnum.getNameByCode(accountTradeRecordResp.getCategory()));
            accountTradeRecordResp.setSubTypeName(AccountTradeRecordSubTypeEnum.getNameByCode(accountTradeRecordResp.getSubType()));
            accountTradeRecordResp.setTransactionTypeName(AccountTradeRecordTypeEnum.getNameByCode(accountTradeRecordResp.getTransactionType()));
        }
        return new PageDataResp<>(accountTradeRecordDOPage.getTotalElements(), accountTradeRecordResps);
    }

    /**
     * 查询会员资金账户交易记录
     * @return
     */
    @Override
    public PageDataResp<MemberAccountTradeRecordPageQueryResp> pageQueryAccountTradeRecord(MemberAccountTradeRecordPageQueryReq pageQueryReq) {
        Pageable page = PageRequest.of(pageQueryReq.getCurrent() - 1, pageQueryReq.getPageSize());
        Specification<AccountTradeRecordDO> specification = (root, query, criteriaBuilder) -> {
            Predicate finalConditions = criteriaBuilder.conjunction();
            finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get("memberAssetAccount").get("id"), pageQueryReq.getMemberAssetAccountId()));
            if (pageQueryReq.getTradeStartTime() != null) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.greaterThanOrEqualTo(root.get(AccountTradeRecordDO.Fields.tradeTime), DateTimeUtil.convertToTimeMillis(pageQueryReq.getTradeStartTime())));
            }
            if(pageQueryReq.getTradeEndTime() != null){
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.lessThanOrEqualTo(root.get(AccountTradeRecordDO.Fields.tradeTime), DateTimeUtil.convertToTimeMillis(pageQueryReq.getTradeEndTime())));
            }
            criteriaBuilder.desc(root.get(AccountTradeRecordDO.Fields.tradeTime));
            return finalConditions;
        };
        Page<AccountTradeRecordDO> accountTradeRecordDOPage = accountTradeRecordRepository.findAll(specification, page);
        List<MemberAccountTradeRecordPageQueryResp> resultList = accountTradeRecordDOPage.stream().map(memberAccountTradeRecordDO -> {
            MemberAccountTradeRecordPageQueryResp memberAccountTradeRecordPageQueryResp = BeanUtil.copyProperties(memberAccountTradeRecordDO, MemberAccountTradeRecordPageQueryResp.class);
            //设置类型名称
            memberAccountTradeRecordPageQueryResp.setCategoryName(MemberAccountTradeCategoryEnum.getNameByCode(memberAccountTradeRecordDO.getCategory()));
            memberAccountTradeRecordPageQueryResp.setTransactionTypeName(AccountTradeRecordTypeEnum.getNameByCode(memberAccountTradeRecordDO.getTransactionType()));
            memberAccountTradeRecordPageQueryResp.setSubTypeName(AccountTradeRecordSubTypeEnum.getNameByCode(memberAccountTradeRecordDO.getSubType()));
            return memberAccountTradeRecordPageQueryResp;
        }).collect(Collectors.toList());
        return new PageDataResp<>(accountTradeRecordDOPage.getTotalElements(),resultList);
    }

    /**
     * 查询会员资金账户交易记录
     * @return
     */
    @Override
    public Page<AccountTradeRecordDO> getAccountTradeRecordList(PageDataReq pageDataReq, Long memberAssetAccountId, List<Integer> operationList, Long startTime, Long endTime) {
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize());
        return accountTradeRecordRepository.findByMemberAssetAccountIdAndOperationInAndTradeTimeGreaterThanEqualAndTradeTimeLessThanEqualOrderByTradeTimeDesc(memberAssetAccountId, operationList, startTime, endTime, page);
    }

    /**
     * 待审核提现
     * @param cashOutReq
     */
    @Override
    public Page<AccountTradeRecordDO> getCheckCashOutList(PageDataReq pageDataReq, CashOutReq cashOutReq) {
        Sort sort = Sort.by("tradeTime").descending();
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize(), sort);
        return accountTradeRecordRepository.findAll(getAccountCheckSpecification(cashOutReq), page);
    }

    /**
     * 批量审核
     * @param commonIdListRequest
     */
    @Override
    public void batchCheck(CommonIdListReq commonIdListRequest) {
        List<Long> idList = commonIdListRequest.getIdList();
        List<AccountTradeRecordDO> accountTradeRecordDOList = accountTradeRecordRepository.findAllById(idList);
        List<AccountCashOutRecordDO> accountCashOutRecordDOList = new ArrayList<>();
        accountTradeRecordDOList.forEach(accountTradeRecord -> {
            Integer status = accountTradeRecord.getStatus();
            if (!TradeStatusEnum.APPLY_CASH_OUT.getCode().equals(status)) {
                throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_STATUS_EXCEPTION);
            } else {
                MemberAssetAccountDO memberAssetAccountDO = accountTradeRecord.getMemberAssetAccount();
                //统一校验账号
                memberAssetAccountService.checkMemberAssetAccount(memberAssetAccountDO);

                BigDecimal lock = memberAssetAccountDO.getLockBalance();
                BigDecimal account = memberAssetAccountDO.getAccountBalance();
                //四舍五入，保留两位小数
                BigDecimal accountBalanceMoney = account.subtract(lock).setScale(2, RoundingMode.HALF_UP);
                memberAssetAccountDO.setAccountBalance(accountBalanceMoney);
                memberAssetAccountDO.setAccountBalanceEncrypt(DigestUtils.md5DigestAsHex(String.valueOf(accountBalanceMoney).getBytes()));
                //锁定金额清0
                BigDecimal defaultMoney = BigDecimal.ZERO;
                String defaultMoneyEncrypt = DigestUtils.md5DigestAsHex(String.valueOf(defaultMoney).getBytes());
                memberAssetAccountDO.setLockBalance(defaultMoney);
                memberAssetAccountDO.setLockBalanceEncrypt(defaultMoneyEncrypt);
                memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);

                try {
                    //
                    // 发送消息-待支付提现消息
                    SystemMessageReq waitWithdrawMessage = new SystemMessageReq();
                    if (MemberLevelTypeEnum.PLATFORM.getCode().equals(memberAssetAccountDO.getMemberLevelType())) {
                        waitWithdrawMessage.setMemberId(0L);
                        waitWithdrawMessage.setRoleId(0L);
                        waitWithdrawMessage.setMessageNotice(MessageNoticeEnum.WITHDRAW_PLATFORM_PAYMENT.getCode());
                        waitWithdrawMessage.setParams(Collections.singletonList(memberAssetAccountDO.getMemberName()));
                    } else {
                        waitWithdrawMessage.setMemberId(memberAssetAccountDO.getParentMemberId());
                        waitWithdrawMessage.setRoleId(memberAssetAccountDO.getParentMemberRoleId());
                        waitWithdrawMessage.setMessageNotice(MessageNoticeEnum.WITHDRAW_PAYMENT.getCode());
                        waitWithdrawMessage.setParams(Collections.singletonList(memberAssetAccountDO.getMemberName()));
                    }
                    // 发送消息-提现申请审核消息
                    SystemMessageReq applyAuditResultMessage = new SystemMessageReq();
                    applyAuditResultMessage.setMemberId(memberAssetAccountDO.getMemberId());
                    applyAuditResultMessage.setRoleId(memberAssetAccountDO.getMemberRoleId());
                    applyAuditResultMessage.setMessageNotice(MessageNoticeEnum.WITHDRAW_APPLY.getCode());
                    applyAuditResultMessage.setParams(Collections.singletonList(TradeStatusEnum.CHECK_PASS.getMessage()));
                    //将消息存入队列中
                    messageService.sendBatchSystemMessage(Arrays.asList(waitWithdrawMessage, applyAuditResultMessage));
                } catch (Exception e) {
                    e.printStackTrace();
                }

                accountTradeRecord.setStatus(TradeStatusEnum.CHECK_PASS.getCode());
                //提现处理记录
                AccountCashOutRecordDO accountCashOutRecordDO = new AccountCashOutRecordDO();
                accountCashOutRecordDO.setTradeCode(accountTradeRecord.getTradeCode());
                accountCashOutRecordDO.setTradeMoney(accountTradeRecord.getTradeMoney());
                accountCashOutRecordDO.setOperation(accountTradeRecord.getOperation());
                accountCashOutRecordDO.setRemark("同意提现");
                accountCashOutRecordDO.setStatus(TradeStatusEnum.CHECK_PASS.getCode());
                accountCashOutRecordDO.setTradeTime(System.currentTimeMillis());
                accountCashOutRecordDOList.add(accountCashOutRecordDO);
            }
        });
        //保存提现处理记录
        accountCashOutRecordRepository.saveAll(accountCashOutRecordDOList);
        //保存资金账户信息
        accountTradeRecordRepository.saveAll(accountTradeRecordDOList);
    }

    /**
     * 审核提现
     * @param checkReq
     */
    @Transactional
    @Override
    public void check(CheckReq checkReq) {
        Integer tradeStatus;
        if (checkReq.getStatus() == 1) {
            tradeStatus = TradeStatusEnum.CHECK_PASS.getCode();
        } else {
            tradeStatus = TradeStatusEnum.CHECK_NOT_PASS.getCode();
        }
        String remark = checkReq.getRemark();
        AccountTradeRecordDO accountTradeRecordDO = accountTradeRecordRepository.findById(checkReq.getId()).orElse(null);
        if(Objects.isNull(accountTradeRecordDO)){
            throw new BusinessException(ResponseCodeEnum.PAY_MEMBER_ASSET_ACCOUNT_PAY_NOT_EXIST);
        }

        BigDecimal tradeMoney = accountTradeRecordDO.getTradeMoney();
        MemberAssetAccountDO memberAssetAccountDO = accountTradeRecordDO.getMemberAssetAccount();
        //统一校验账号
        memberAssetAccountService.checkMemberAssetAccount(memberAssetAccountDO);

        BigDecimal lock = memberAssetAccountDO.getLockBalance();
        BigDecimal account = memberAssetAccountDO.getAccountBalance();

        //审核不通过，锁定资金返回到账号余额里面
        if (TradeStatusEnum.CHECK_NOT_PASS.getCode().equals(tradeStatus)) {
            //锁定金额-交易金额
            BigDecimal lockMoney = lock.subtract(tradeMoney).setScale(2, RoundingMode.HALF_UP);
            String lockMoneyEncrypt = DigestUtils.md5DigestAsHex(String.valueOf(lockMoney).getBytes());
            memberAssetAccountDO.setLockBalance(lockMoney);
            memberAssetAccountDO.setLockBalanceEncrypt(lockMoneyEncrypt);
            memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);

            try {
                //发送消息
                // 提现申请审核消息
                SystemMessageReq applyAuditResultMessage = new SystemMessageReq();
                applyAuditResultMessage.setMemberId(memberAssetAccountDO.getMemberId());
                applyAuditResultMessage.setRoleId(memberAssetAccountDO.getMemberRoleId());
                applyAuditResultMessage.setMessageNotice(MessageNoticeEnum.WITHDRAW_APPLY.getCode());
                applyAuditResultMessage.setParams(Collections.singletonList(TradeStatusEnum.CHECK_NOT_PASS.getMessage()));
                //将消息存入队列中
                messageService.sendSystemMessage(applyAuditResultMessage);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {   //审核通过
            //账号金额-交易金额
            BigDecimal accountBalanceMoney = account.subtract(tradeMoney);
            memberAssetAccountDO.setAccountBalance(accountBalanceMoney);
            memberAssetAccountDO.setAccountBalanceEncrypt(DigestUtils.md5DigestAsHex(String.valueOf(accountBalanceMoney).getBytes()));
            //锁定金额-交易金额
            BigDecimal lockMoney = lock.subtract(tradeMoney).setScale(2, RoundingMode.HALF_UP);
            String lockMoneyEncrypt = DigestUtils.md5DigestAsHex(String.valueOf(lockMoney).getBytes());
            memberAssetAccountDO.setLockBalance(lockMoney);
            memberAssetAccountDO.setLockBalanceEncrypt(lockMoneyEncrypt);
            memberAssetAccountRepository.saveAndFlush(memberAssetAccountDO);

            try {
                // 发送消息-待支付提现消息
                SystemMessageReq waitWithdrawMessage = new SystemMessageReq();
                if (MemberLevelTypeEnum.PLATFORM.getCode().equals(memberAssetAccountDO.getMemberLevelType())) {
                    waitWithdrawMessage.setMemberId(0L);
                    waitWithdrawMessage.setRoleId(0L);
                    waitWithdrawMessage.setMessageNotice(MessageNoticeEnum.WITHDRAW_PLATFORM_PAYMENT.getCode());
                    waitWithdrawMessage.setParams(Collections.singletonList(memberAssetAccountDO.getMemberName()));
                } else {
                    waitWithdrawMessage.setMemberId(memberAssetAccountDO.getParentMemberId());
                    waitWithdrawMessage.setRoleId(memberAssetAccountDO.getParentMemberRoleId());
                    waitWithdrawMessage.setMessageNotice(MessageNoticeEnum.WITHDRAW_PAYMENT.getCode());
                    waitWithdrawMessage.setParams(Collections.singletonList(memberAssetAccountDO.getMemberName()));
                }
                // 发送消息-提现申请审核消息
                SystemMessageReq applyAuditResultMessage = new SystemMessageReq();
                applyAuditResultMessage.setMemberId(memberAssetAccountDO.getMemberId());
                applyAuditResultMessage.setRoleId(memberAssetAccountDO.getMemberRoleId());
                applyAuditResultMessage.setMessageNotice(MessageNoticeEnum.WITHDRAW_APPLY.getCode());
                applyAuditResultMessage.setParams(Collections.singletonList(TradeStatusEnum.CHECK_PASS.getMessage()));
                //将消息存入队列中
                messageService.sendBatchSystemMessage(Arrays.asList(waitWithdrawMessage, applyAuditResultMessage));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //保存交易记录
        accountTradeRecordDO.setStatus(tradeStatus);
        accountTradeRecordDO.setRemark(remark);
        accountTradeRecordRepository.saveAndFlush(accountTradeRecordDO);
        //保存提现处理记录
        AccountCashOutRecordDO accountCashOutRecordDO = new AccountCashOutRecordDO();
        accountCashOutRecordDO.setTradeCode(accountTradeRecordDO.getTradeCode());
        accountCashOutRecordDO.setTradeMoney(accountTradeRecordDO.getTradeMoney());
        accountCashOutRecordDO.setOperation(accountTradeRecordDO.getOperation());
        accountCashOutRecordDO.setRemark(remark);
        accountCashOutRecordDO.setStatus(tradeStatus);
        accountCashOutRecordDO.setTradeTime(System.currentTimeMillis());
        accountCashOutRecordRepository.saveAndFlush(accountCashOutRecordDO);
    }

    @Override
    public List<ReportItemResp> getPayReport(UserLoginCacheDTO sysUser) {
        List<ReportItemResp> resultList = Lists.newArrayList() ;

        CashOutReq cashOutReq = new CashOutReq() ;
        cashOutReq.setParentMemberId(sysUser.getMemberId());
        cashOutReq.setParentMemberRoleId(sysUser.getMemberRoleId());

        //待审核提现
        cashOutReq.setStatusList(Lists.newArrayList(TradeStatusEnum.APPLY_CASH_OUT.getCode() , TradeStatusEnum.CHECK_PASS.getCode() , TradeStatusEnum.CHECK_NOT_PASS.getCode()));
        resultList.add(new ReportItemResp(AccountOperateTypeEnum.TO_BE_VALIFY_CASHOUT.getName() , AccountOperateTypeEnum.TO_BE_VALIFY_CASHOUT.getLink() , accountTradeRecordRepository.count(getAccountCheckSpecification(cashOutReq))));

        //待支付体现
        cashOutReq.setStatusList(Lists.newArrayList(TradeStatusEnum.CHECK_PASS.getCode() , TradeStatusEnum.CASH_OUT_SUCCESS.getCode() , TradeStatusEnum.CASH_OUT_FAIL.getCode()));
        resultList.add(new ReportItemResp(AccountOperateTypeEnum.TO_BE_PAY_CASHOUT.getName() , AccountOperateTypeEnum.TO_BE_PAY_CASHOUT.getLink() , accountTradeRecordRepository.count(getAccountCheckSpecification(cashOutReq))));

        return resultList ;
    }


    /**
     * 添加交易记录-（存料和余额进行组合支付会生成两条记录）
     * @param addTradeRecordReq 交易记录
     * @return 是否添加成功
     */
    @Override
    public Boolean addMemberAccountTradeRecord(AddMemberAccountTradeRecordReq addTradeRecordReq) {
        List<AccountTradeRecordDO> accountTradeRecordDOList = new ArrayList<>();
        if(!BigDecimalUtil.isNullOrZero(addTradeRecordReq.getBalanceChange())){
            //消费
            AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
            accountTradeRecordDO.setTradeCode(addTradeRecordReq.getTradeCode());
            accountTradeRecordDO.setTradeTime(DateTimeUtil.convertToTimeMillis(addTradeRecordReq.getTradeTime()));
            accountTradeRecordDO.setTradeMoney(addTradeRecordReq.getBalanceChange());
            accountTradeRecordDO.setTradeMoneyBefore(addTradeRecordReq.getBalanceBefore());
            accountTradeRecordDO.setTradeMoneyAfter(addTradeRecordReq.getBalanceAfter());
            accountTradeRecordDO.setCategory(addTradeRecordReq.getCategory());
            accountTradeRecordDO.setPayPlatformTradeCode(addTradeRecordReq.getPayPlatformTradeCode());
            accountTradeRecordDO.setTradeCode(addTradeRecordReq.getTradeCode());
            accountTradeRecordDO.setRemark(addTradeRecordReq.getRemark());
            accountTradeRecordDO.setTransactionType(addTradeRecordReq.getTransactionType());
            accountTradeRecordDO.setSubType(addTradeRecordReq.getSubType());
            accountTradeRecordDOList.add(accountTradeRecordDO);
        }
        if(!BigDecimalUtil.isNullOrZero(addTradeRecordReq.getMaterialStockChange())){
            //存料
            AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
            accountTradeRecordDO.setTradeCode(addTradeRecordReq.getTradeCode());
            accountTradeRecordDO.setTradeTime(DateTimeUtil.convertToTimeMillis(addTradeRecordReq.getTradeTime()));
            accountTradeRecordDO.setTradeMoney(addTradeRecordReq.getMaterialStockChange());
            accountTradeRecordDO.setTradeMoneyBefore(addTradeRecordReq.getMaterialStockBefore());
            accountTradeRecordDO.setTradeMoneyAfter(addTradeRecordReq.getMaterialStockAfter());
            accountTradeRecordDO.setCategory(addTradeRecordReq.getCategory());
            accountTradeRecordDO.setPayPlatformTradeCode(addTradeRecordReq.getPayPlatformTradeCode());
            accountTradeRecordDO.setTradeCode(addTradeRecordReq.getTradeCode());
            accountTradeRecordDO.setRemark(addTradeRecordReq.getRemark());
            accountTradeRecordDO.setTransactionType(addTradeRecordReq.getTransactionType());
            accountTradeRecordDO.setSubType(addTradeRecordReq.getSubType());
            accountTradeRecordDOList.add(accountTradeRecordDO);
        }
        accountTradeRecordRepository.saveAll(accountTradeRecordDOList);

        return true;
    }


    /**
     * 添加交易记录-（存料和余额进行组合支付会生成两条记录）
     * @param addTradeRecordReqs 交易记录
     * @return 是否添加成功
     */
    @Transactional
    @Override
    public Boolean addMemberAccountTradeRecord(List<AddMemberAccountTradeRecordReq> addTradeRecordReqs) {
        List<AccountTradeRecordDO> accountTradeRecordDOList = new ArrayList<>();
        for (AddMemberAccountTradeRecordReq addTradeRecordReq : addTradeRecordReqs) {
            if(!BigDecimalUtil.isNullOrZero(addTradeRecordReq.getBalanceChange())){
                AccountTradeRecordDO accountTradeRecordDO = new AccountTradeRecordDO();
                accountTradeRecordDO.setTradeMoney(addTradeRecordReq.getBalanceChange());
                accountTradeRecordDO.setTradeMoneyBefore(addTradeRecordReq.getBalanceBefore());
                accountTradeRecordDO.setTradeMoneyAfter(addTradeRecordReq.getBalanceAfter());
                accountTradeRecordDO.setCategory(addTradeRecordReq.getCategory());
                accountTradeRecordDO.setPayPlatformTradeCode(addTradeRecordReq.getPayPlatformTradeCode());
                accountTradeRecordDO.setTradeCode(addTradeRecordReq.getTradeCode());
                accountTradeRecordDO.setRemark(addTradeRecordReq.getRemark());
                accountTradeRecordDO.setSubType(addTradeRecordReq.getSubType());
                accountTradeRecordDO.setOperation(TradeOperationEnum.ORDER_PAY.getCode());
                accountTradeRecordDO.setStatus(TradeStatusEnum.PAYING.getCode());
                accountTradeRecordDO.setTransactionType(AccountTradeRecordTypeEnum.PAYMENT_PLATFORM.getCode());
                accountTradeRecordDO.setMemberAssetAccount(addTradeRecordReq.getMemberAssetAccountDO());
                accountTradeRecordDO.setTradeTime(DateTimeUtil.getTodayNow());
                accountTradeRecordDOList.add(accountTradeRecordDO);
            }
        }
        accountTradeRecordRepository.saveAll(accountTradeRecordDOList);
        return true;
    }



    /**
     * 构建查询条件
     * @param cashOutReq  参数
     */
    private Specification<AccountTradeRecordDO> getAccountCheckSpecification(CashOutReq cashOutReq) {
        return (root, query, criteriaBuilder) -> {
            Predicate finalConditions = criteriaBuilder.conjunction();

            String memberName = cashOutReq.getMemberName();
            Long parentMemberId = cashOutReq.getParentMemberId();
            Long parentMemberRoleId = cashOutReq.getParentMemberRoleId();
            Long startTime = cashOutReq.getStartTime();
            Long endTime = cashOutReq.getEndTime();
            List<Integer> statusList = cashOutReq.getStatusList();
            List<Integer> memberLevelTypeList = cashOutReq.getMemberLevelTypeList();

            //会员名称
            if (StringUtils.isNotEmpty(memberName)) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.like(root.get(AccountTradeRecordDO.Fields.memberAssetAccount).get(MemberAssetAccountDO.Fields.memberName), "%" + memberName + "%"));
            }
            //父级会员id
            if (parentMemberId != null && parentMemberId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(AccountTradeRecordDO.Fields.memberAssetAccount).get(MemberAssetAccountDO.Fields.parentMemberId), parentMemberId));
            }
            //父级会员角色id
            if (parentMemberRoleId != null && parentMemberRoleId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(AccountTradeRecordDO.Fields.memberAssetAccount).get(MemberAssetAccountDO.Fields.parentMemberRoleId), parentMemberRoleId));
            }
            //开始时间
            if (startTime != null && startTime > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.ge(root.get(AccountTradeRecordDO.Fields.tradeTime), startTime));
            }
            //结束时间
            if (endTime != null && endTime > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.le(root.get(AccountTradeRecordDO.Fields.tradeTime), endTime));
            }
            //状态
            if (statusList != null && !statusList.isEmpty()) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.in(root.get(AccountTradeRecordDO.Fields.status)).value(statusList));
            }
            //会员等级类型
            if (memberLevelTypeList != null && !memberLevelTypeList.isEmpty()) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.in(root.get(AccountTradeRecordDO.Fields.memberAssetAccount).get(MemberAssetAccountDO.Fields.memberLevelType)).value(memberLevelTypeList));
            }
            return finalConditions;
        };
    }

}


