package com.ssy.lingxi.pay.repository;

import com.ssy.lingxi.pay.entity.do_.WeChatNotifyRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 微信回调通知记录
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/10/21
 */
@Repository
public interface WeChatNotifyRecordRepository extends JpaRepository<WeChatNotifyRecordDO,Long>, JpaSpecificationExecutor<WeChatNotifyRecordDO> {
}
