package com.ssy.lingxi.pay.serviceImpl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.constant.RedisConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.enums.CommonBooleanEnum;
import com.ssy.lingxi.component.base.enums.EnableDisableStatusEnum;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPaymentParameterEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.AopProxyUtil;
import com.ssy.lingxi.component.redis.service.IRedisUtils;
import com.ssy.lingxi.member.api.enums.MemberPayPasswordCheckEnum;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.req.MemberFeignPayPswCheckReq;
import com.ssy.lingxi.member.api.model.req.MemberFeignReq;
import com.ssy.lingxi.member.api.model.req.MemberFeignSubReq;
import com.ssy.lingxi.member.api.model.req.MemberRelationFeignReq;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLogoResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignPayPswCheckResultResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignQueryResp;
import com.ssy.lingxi.order.api.model.resp.PayChannelParameterFeignDetailResp;
import com.ssy.lingxi.order.api.model.resp.PaymentParameterFeignDetailResp;
import com.ssy.lingxi.pay.api.constant.CreditTradeConstant;
import com.ssy.lingxi.pay.api.enums.ServiceTypeEnum;
import com.ssy.lingxi.pay.api.model.req.*;
import com.ssy.lingxi.pay.api.model.req.assetAccount.BalancePayReq;
import com.ssy.lingxi.pay.api.model.resp.CreditFeignDetailResp;
import com.ssy.lingxi.pay.api.model.resp.CreditPayResponseResp;
import com.ssy.lingxi.pay.constant.PayConstant;
import com.ssy.lingxi.pay.entity.bo.PayProveBO;
import com.ssy.lingxi.pay.entity.do_.CreditApplyDO;
import com.ssy.lingxi.pay.entity.do_.CreditBillDO;
import com.ssy.lingxi.pay.entity.do_.CreditDO;
import com.ssy.lingxi.pay.entity.do_.CreditTradeRecordDO;
import com.ssy.lingxi.pay.enums.*;
import com.ssy.lingxi.pay.model.req.*;
import com.ssy.lingxi.pay.model.resp.*;
import com.ssy.lingxi.pay.repository.CreditApplyRepository;
import com.ssy.lingxi.pay.repository.CreditBillRepository;
import com.ssy.lingxi.pay.repository.CreditRepository;
import com.ssy.lingxi.pay.repository.CreditTradeRecordRepository;
import com.ssy.lingxi.pay.service.ICreditApplyService;
import com.ssy.lingxi.pay.service.ICreditBillService;
import com.ssy.lingxi.pay.service.ICreditService;
import com.ssy.lingxi.pay.service.IFeignService;
import com.ssy.lingxi.pay.util.CreditUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员授信接口实现
 * <AUTHOR>
 * @since 2020/8/13 15:55
 * @version 2.0.0
 */
@Slf4j
@Service
public class CreditServiceImpl implements ICreditService {
    @Resource
    private CreditRepository creditRepository;

    @Resource
    private ICreditApplyService creditApplyService;

    @Resource
    private CreditApplyRepository creditApplyRepository;

    @Resource
    private ICreditBillService creditBillService;

    @Resource
    private IMemberFeign memberFeign;

    @Resource
    private CreditBillRepository creditBillRepository;

    @Resource
    private CreditTradeRecordRepository creditTradeRecordRepository;

    @Resource
    private IRedisUtils redisUtils;

    @Resource
    private IFeignService feignService;

    /**
     * 初始化会员授信
     * @param initVO 接口参数
     */
    @Transactional
    @Override
    public void init(CreditInitReq initVO) {
        log.info("初始化授信申请记录initVO:{}", JSONUtil.toJsonStr(initVO));
        // step 1: 查询父级会员是否开启授信支付
        boolean isPass = feignService.isCreditPayment(initVO.getMemberId(), initVO.getRoleId());
        if (isPass) {
            // step 3: 开启授信支付，进行业务逻辑处理

            // step 3.1: 查询子会员列表数据
            MemberFeignSubReq memberFeignVO = new MemberFeignSubReq();
            memberFeignVO.setMemberId(initVO.getMemberId());
            memberFeignVO.setRoleId(initVO.getRoleId());
            WrapperResp<List<MemberFeignQueryResp>> listWrapperResp = memberFeign.listLowerMembers(memberFeignVO);
            if(listWrapperResp == null){
                throw new BusinessException(ResponseCodeEnum.FEIGN_SERVICE_ERROR);
            }else if(listWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
                throw new BusinessException(listWrapperResp.getCode(),listWrapperResp.getMessage());
            }
            List<MemberFeignQueryResp> memberList = listWrapperResp.getData();
            if (!CollectionUtils.isEmpty(memberList)) {
                // step 3.2: 查询未产生授信数据的子会员
                List<CreditDO> childList = creditRepository.findByParentMemberIdAndParentMemberRoleId(initVO.getMemberId(), initVO.getRoleId());
                // 定义全局的到期时间
                Long expireTime = CreditUtil.getExpireTime(DateUtil.date(), PayConstant.PAY_CREDIT_DEFAULT_BILL_DAY, PayConstant.PAY_CREDIT_DEFAULT_BILL_REPAY_PERIOD).getTime();
                // 根据会员id+角色id，匹配尚未新增授信的数据，并生成待新增的授信
                // 以子会员id+子会员角色id为唯一键去重，得到需要新增的数据列表
                List<CreditDO> addList = memberList.stream()
                        .filter(member -> childList.stream().noneMatch(child -> child.getMemberId().equals(member.getMemberId()) && child.getMemberRoleId().equals(member.getRoleId())))
                        .map(r -> {
                            CreditDO addDO = new CreditDO();
                            addDO.setMemberId(r.getMemberId());
                            addDO.setMemberRoleId(r.getRoleId());
                            addDO.setParentMemberId(initVO.getMemberId());
                            addDO.setParentMemberRoleId(initVO.getRoleId());
                            addDO.setQuota(BigDecimal.ZERO);
                            addDO.setUseQuota(BigDecimal.ZERO);
                            addDO.setBillDay(PayConstant.PAY_CREDIT_DEFAULT_BILL_DAY);
                            addDO.setRepayPeriod(PayConstant.PAY_CREDIT_DEFAULT_BILL_REPAY_PERIOD);
                            addDO.setStatus(CreditStatusEnum.UN_APPLY.getCode());
                            addDO.setRePayStatus(CreditRepayStatusEnum.PAY_OFF.getCode());
                            addDO.setIsHasApply(EnableDisableStatusEnum.DISABLE.getCode());
                            addDO.setCreateTime(System.currentTimeMillis());
                            addDO.setUpdateTime(0L);
                            addDO.setExpireTime(expireTime);
                            addDO.setLastRepayTime(0L);
                            addDO.setIsUsable(CommonBooleanEnum.NO.getCode());
                            return addDO;
                        }).collect(Collectors.toList());
                // step 3.3: 为未产生授信数据的子会员新增授信信息
                if (addList.size() > 0) {
                    creditRepository.saveAll(addList);
                }
            }
        }
    }

    /**
     * 查询会员授信
     *
     * @param feignVO 上下级会员Id
     * @return 查询结果，如无授信，返回Null
     */
    @Override
    public CreditFeignDetailResp findCredit(CreditFeignReq feignVO) {
        // 验证授信是否存在
        CreditDO creditDO = creditRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(feignVO.getMemberId(), feignVO.getRoleId(), feignVO.getParentMemberId(), feignVO.getParentMemberRoleId());
        if (creditDO == null) {
            return null;
        }

        CreditFeignDetailResp result = new CreditFeignDetailResp();

        // 赋值授信信息
        result.setQuota(creditDO.getQuota());
        result.setUseQuota(creditDO.getUseQuota());
        result.setCanUseQuota(creditDO.getQuota().subtract(creditDO.getUseQuota()));
        result.setIsUsable(creditDO.getIsUsable());
        return result;
    }

    /**
     * 新增授信
     * @param creditAddReq 接口参数
     */
    @Override
    public void add(CreditAddReq creditAddReq) {
        log.info("添加授信申请记录creditAddVO:{}", JSONUtil.toJsonStr(creditAddReq));
        // step 1: 查询父级会员是否开启授信支付
        boolean isPass = feignService.isCreditPayment(creditAddReq.getParentMemberId(), creditAddReq.getParentMemberRoleId());
        if (!isPass) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_LINE_PAYMENT_NOT_OPEN);
        } else {
            // step 2: 验证是否存在相同数据
            CreditDO oldCreditDO = creditRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(
                    creditAddReq.getMemberId(), creditAddReq.getRoleId(), creditAddReq.getParentMemberId(), creditAddReq.getParentMemberRoleId());
            if (oldCreditDO != null) {
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_EXIST);
            }

            // step 5: 新增授信
            CreditDO creditDO = new CreditDO();
            creditDO.setMemberId(creditAddReq.getMemberId());
            creditDO.setMemberRoleId(creditAddReq.getRoleId());
            creditDO.setParentMemberId(creditAddReq.getParentMemberId());
            creditDO.setParentMemberRoleId(creditAddReq.getParentMemberRoleId());
            creditDO.setQuota(BigDecimal.ZERO);
            creditDO.setUseQuota(BigDecimal.ZERO);
            creditDO.setBillDay(PayConstant.PAY_CREDIT_DEFAULT_BILL_DAY);
            creditDO.setRepayPeriod(PayConstant.PAY_CREDIT_DEFAULT_BILL_REPAY_PERIOD);
            creditDO.setStatus(CreditStatusEnum.UN_APPLY.getCode());
            creditDO.setRePayStatus(CreditRepayStatusEnum.PAY_OFF.getCode());
            creditDO.setIsHasApply(EnableDisableStatusEnum.DISABLE.getCode());
            creditDO.setCreateTime(System.currentTimeMillis());
            creditDO.setUpdateTime(0L);
            Long expireTime = CreditUtil.getExpireTime(DateUtil.date(), PayConstant.PAY_CREDIT_DEFAULT_BILL_DAY, PayConstant.PAY_CREDIT_DEFAULT_BILL_REPAY_PERIOD).getTime();
            creditDO.setExpireTime(expireTime);
            creditDO.setLastRepayTime(0L);
            creditDO.setIsUsable(CommonBooleanEnum.NO.getCode());
            creditRepository.saveAndFlush(creditDO);
        }
    }

    /**
     * 分页获取会员授信
     * @param user 登录用户
     * @param pageVO 分页查询授信VO
     * @return 查询结果
     */
    @Override
    public PageDataResp<CreditQueryResp> pageCredit(UserLoginCacheDTO user, PageQueryCreditDataReq pageVO) {

        // 查询会员信息
        MemberFeignReq memberFeignReq = new MemberFeignReq();
        memberFeignReq.setMemberId(user.getMemberId());
        memberFeignReq.setRoleId(user.getMemberRoleId());
        WrapperResp<List<MemberFeignQueryResp>> listWrapperResp = memberFeign.listUpperMembers(memberFeignReq);
        if(listWrapperResp == null){
            throw new BusinessException(ResponseCodeEnum.FEIGN_SERVICE_ERROR);
        }else if(listWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
            throw new BusinessException(listWrapperResp.getCode(),listWrapperResp.getMessage());
        }

        // step 1: 组装查询条件
        Specification<CreditDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), user.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("memberRoleId").as(Long.class), user.getMemberRoleId()));
            if (!pageVO.getStatus().equals(CreditStatusEnum.ALL.getCode())) {
                list.add(criteriaBuilder.equal(root.get("status").as(int.class), pageVO.getStatus()));
            }
            if (!pageVO.getRePayStatus().equals(CreditRepayStatusEnum.ALL.getCode())) {
                list.add(criteriaBuilder.equal(root.get("rePayStatus").as(int.class), pageVO.getRePayStatus()));
            }
            // 筛选上级会员名称
            if (StringUtils.hasLength(pageVO.getParentMemberName())) {
                List<Long> idList = listWrapperResp.getData().stream()
                        .filter(r -> r.getMemberName().contains(pageVO.getParentMemberName()))
                        .map(MemberFeignQueryResp::getMemberId).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(idList)) {
                    list.add(root.get("parentMemberId").in(idList));
                }else{
                    list.add(criteriaBuilder.equal(root.get("parentMemberId").as(Long.class),-1L));
                }
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // step 2: 组装分页参数
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("updateTime").descending());

        // step 3: 分页查询数据
        Page<CreditDO> result = creditRepository.findAll(spec, page);

        // step 4: 组装返回数据
        List<CreditQueryResp> resultList = result.getContent().stream().map(r -> {
            CreditQueryResp queryVO = new CreditQueryResp();
            queryVO.setId(r.getId());

            MemberFeignQueryResp memberFeignQueryResp = listWrapperResp.getData().stream().filter(item -> item.getMemberId().equals(r.getParentMemberId())
                    && item.getRoleId().equals(r.getParentMemberRoleId())).findFirst().orElse(null);
            if (memberFeignQueryResp != null) {
                queryVO.setParentMemberName(memberFeignQueryResp.getMemberName());
                queryVO.setMemberTypeName(memberFeignQueryResp.getMemberTypeName());
                queryVO.setMemberRoleName(memberFeignQueryResp.getRoleName());
                queryVO.setMemberLevelName(memberFeignQueryResp.getLevelTag());
            }
            queryVO.setQuota(r.getQuota());
            queryVO.setUseQuota(r.getUseQuota());
            queryVO.setCanUseQuota(r.getQuota().subtract(r.getUseQuota()).setScale(2, RoundingMode.HALF_UP));
            queryVO.setStatus(r.getStatus());
            queryVO.setStatusName(CreditStatusEnum.getItem(r.getStatus()).getMessage());
            queryVO.setRepayStatus(r.getRePayStatus());
            queryVO.setIsHasApply(r.getIsHasApply());

            CreditRepayStatusEnum repayStatusEnum = CreditRepayStatusEnum.getItem(r.getRePayStatus());
            if (CreditRepayStatusEnum.OVERDUE.getCode().equals(r.getRePayStatus())) {
                // 如果支付状态为逾期，则计算逾期天数
                Long overdueDay = DateUtil.between(DateUtil.date(r.getExpireTime()), DateUtil.date(), DateUnit.DAY, false);
                queryVO.setOverdueDay(overdueDay);
                String repayStatusName = repayStatusEnum.getRemark() + overdueDay + "天";
                queryVO.setRepayStatusName(repayStatusName);

            } else {
                queryVO.setOverdueDay(0L);
                queryVO.setRepayStatusName(repayStatusEnum.getRemark());
            }
            queryVO.setIsCanApply(CommonBooleanEnum.YES.getCode());
            CreditApplyDO applyDo = creditApplyRepository.findByIsCurrentAndCreditIdAndIsDelete(CommonBooleanEnum.YES.getCode(), r.getId(), CommonBooleanEnum.NO.getCode());
            if (applyDo != null) {
                PaymentParameterFeignDetailResp payParameters = feignService.getCreditPayment(r.getParentMemberId(), r.getParentMemberRoleId());
                if (payParameters != null && !CollectionUtils.isEmpty(payParameters.getParameters())) {
                    // 获取允许申请的间隔天数
                    PayChannelParameterFeignDetailResp detailVO  = payParameters.getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.CREDIT_DAYS) && StringUtils.hasLength(p.getValue())).findFirst().orElse(null);
                    long intervalDay = detailVO == null ? 0L : Long.parseLong(detailVO.getValue());
                    long betweenDay = DateUtil.between(
                            DateUtil.date(applyDo.getApplyTime()),
                            DateUtil.date(System.currentTimeMillis()),
                            DateUnit.DAY);
                    // 间隔时间小于等配置时间则不允许再次申请
                    if (betweenDay < intervalDay) {
                        queryVO.setIsCanApply(CommonBooleanEnum.NO.getCode());
                    }
                } else {
                    queryVO.setIsCanApply(CommonBooleanEnum.NO.getCode());
                }
            }

            return queryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }

    /**
     * App端分页查询会员授信
     * @param user 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<CreditQueryAppResp> pageCreditByApp(UserLoginCacheDTO user, PageQueryCreditDataReq pageVO) {

        // 查询会员信息
        MemberFeignReq memberFeignReq = new MemberFeignReq();
        memberFeignReq.setMemberId(user.getMemberId());
        memberFeignReq.setRoleId(user.getMemberRoleId());
        WrapperResp<List<MemberFeignQueryResp>> listWrapperResp = memberFeign.listUpperMembers(memberFeignReq);
        if(listWrapperResp == null){
            throw new BusinessException(ResponseCodeEnum.FEIGN_SERVICE_ERROR);
        }else if(listWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
            throw new BusinessException(listWrapperResp.getCode(),listWrapperResp.getMessage());
        }
        // step 1: 组装查询条件
        Specification<CreditDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("memberId").as(Long.class), user.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("memberRoleId").as(Long.class), user.getMemberRoleId()));
            if (!pageVO.getStatus().equals(CreditStatusEnum.ALL.getCode())) {
                list.add(criteriaBuilder.equal(root.get("status").as(int.class), pageVO.getStatus()));
            }
            if (!pageVO.getRePayStatus().equals(CreditRepayStatusEnum.ALL.getCode())) {
                list.add(criteriaBuilder.equal(root.get("rePayStatus").as(int.class), pageVO.getRePayStatus()));
            }
            // 筛选上级会员名称
            if (StringUtils.hasLength(pageVO.getParentMemberName())) {
                List<Long> idList = listWrapperResp.getData().stream()
                        .filter(r -> r.getMemberName().contains(pageVO.getParentMemberName()))
                        .map(MemberFeignQueryResp::getMemberId).collect(Collectors.toList());
                list.add(criteriaBuilder.in(root.get("parentMemberId")).value(idList));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // step 2: 组装分页参数
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("updateTime").descending());

        // step 3: 分页查询数据
        Page<CreditDO> result = creditRepository.findAll(spec, page);

        // 查询log
        List<Long> memberIds = result.getContent().stream().map(CreditDO::getParentMemberId).collect(Collectors.toList());
        WrapperResp<List<MemberFeignLogoResp>> logListWrapperResp = memberFeign.getMemberLogos(memberIds);
        if(logListWrapperResp == null){
            throw new BusinessException(ResponseCodeEnum.FEIGN_SERVICE_ERROR);
        }else if(logListWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
            throw new BusinessException(logListWrapperResp.getCode(),logListWrapperResp.getMessage());
        }
        // step 4: 组装返回数据
        List<CreditQueryAppResp> resultList = result.getContent().stream().map(r -> {
            CreditQueryAppResp queryVO = new CreditQueryAppResp();
            queryVO.setId(r.getId());
            MemberFeignQueryResp memberFeignQueryResp = listWrapperResp.getData().stream().filter(item -> item.getMemberId().equals(r.getParentMemberId())
                    && item.getRoleId().equals(r.getParentMemberRoleId())).findFirst().orElse(null);
            if (memberFeignQueryResp != null) {
                queryVO.setParentMemberName(memberFeignQueryResp.getMemberName());
                queryVO.setMemberTypeName(memberFeignQueryResp.getMemberTypeName());
                queryVO.setMemberRoleName(memberFeignQueryResp.getRoleName());
                queryVO.setMemberLevelName(memberFeignQueryResp.getLevelTag());
                queryVO.setLogo(memberFeignQueryResp.getLogo());
            }
            queryVO.setQuota(r.getQuota());
            queryVO.setUseQuota(r.getUseQuota());
            queryVO.setCanUseQuota(r.getQuota().subtract(r.getUseQuota()).setScale(2, RoundingMode.HALF_UP));
            queryVO.setStatus(r.getStatus());
            queryVO.setStatusName(CreditStatusEnum.getItem(r.getStatus()).getMessage());
            queryVO.setRepayStatus(r.getRePayStatus());
            queryVO.setIsHasApply(r.getIsHasApply());
            CreditRepayStatusEnum repayStatusEnum = CreditRepayStatusEnum.getItem(r.getRePayStatus());
            if (CreditRepayStatusEnum.OVERDUE.getCode().equals(r.getRePayStatus())) {
                // 如果支付状态为逾期，则计算逾期天数
                Long overdueDay = DateUtil.between(DateUtil.date(r.getExpireTime()), DateUtil.date(), DateUnit.DAY, false);
                queryVO.setOverdueDay(overdueDay);
                String repayStatusName = repayStatusEnum.getRemark() + overdueDay + "天";
                queryVO.setRepayStatusName(repayStatusName);

            } else {
                queryVO.setOverdueDay(0L);
                queryVO.setRepayStatusName(repayStatusEnum.getRemark());
            }
            queryVO.setIsCanApply(CommonBooleanEnum.YES.getCode());
            CreditApplyDO applyDo = creditApplyRepository.findByIsCurrentAndCreditIdAndIsDelete(CommonBooleanEnum.YES.getCode(), r.getId(), CommonBooleanEnum.NO.getCode());
            if (applyDo != null) {
                PaymentParameterFeignDetailResp payParameters = feignService.getCreditPayment(r.getParentMemberId(), r.getParentMemberRoleId());
                if (payParameters != null) {
                    // 获取允许申请的间隔天数
                    PayChannelParameterFeignDetailResp detailVO = payParameters.getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.CREDIT_DAYS) && StringUtils.hasLength(p.getValue())).findFirst().orElse(null);
                    long intervalDay = detailVO == null ? 0L : Long.parseLong(detailVO.getValue());
                    long betweenDay = DateUtil.between(
                            DateUtil.date(applyDo.getApplyTime()),
                            DateUtil.date(System.currentTimeMillis()),
                            DateUnit.DAY);
                    // 间隔时间小于等配置时间则不允许再次申请
                    if (betweenDay < intervalDay) {
                        queryVO.setIsCanApply(CommonBooleanEnum.NO.getCode());
                    }
                } else {
                    queryVO.setIsCanApply(CommonBooleanEnum.NO.getCode());
                }
            }

            logListWrapperResp.getData().stream().filter(item -> item.getMemberId().equals(r.getParentMemberId())).findFirst().ifPresent(memberLogQueryVO -> queryVO.setParentMemberLog(memberLogQueryVO.getLogo()));

            return queryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }

    /**
     * 分页获取子会员授信
     * @param user 登录用户
     * @param pageVO 接口参数
     * @return 查询结果
     */
    @Override
    public PageDataResp<ChildCreditQueryResp> pageChildCredit(UserLoginCacheDTO user, PageQueryChildCreditDataReq pageVO) {

        // 查询会员信息
        MemberFeignSubReq memberFeignVO = new MemberFeignSubReq();
        memberFeignVO.setMemberId(user.getMemberId());
        memberFeignVO.setRoleId(user.getMemberRoleId());
        memberFeignVO.setSubMemberName(pageVO.getMemberName());
        memberFeignVO.setLevel(pageVO.getLevel());
        memberFeignVO.setMemberType(pageVO.getMemberTypeId());
        memberFeignVO.setSubRoleId(pageVO.getSubRoleId());
        WrapperResp<List<MemberFeignQueryResp>> listWrapperResp = memberFeign.listLowerMembers(memberFeignVO);
        if(listWrapperResp == null){
            throw new BusinessException(ResponseCodeEnum.FEIGN_SERVICE_ERROR);
        }else if(listWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
            throw new BusinessException(listWrapperResp.getCode(),listWrapperResp.getMessage());
        }
        List<Long> idList = listWrapperResp.getData().stream().map(MemberFeignQueryResp::getMemberId).collect(Collectors.toList());

        // step 1: 组装查询条件
        Specification<CreditDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            list.add(criteriaBuilder.equal(root.get("parentMemberId").as(Long.class), user.getMemberId()));
            list.add(criteriaBuilder.equal(root.get("parentMemberRoleId").as(Long.class), user.getMemberRoleId()));
            if (!pageVO.getStatus().equals(CreditStatusEnum.ALL.getCode())) {
                list.add(criteriaBuilder.equal(root.get("status").as(int.class), pageVO.getStatus()));
            }
            if (!pageVO.getRePayStatus().equals(CreditRepayStatusEnum.ALL.getCode())) {
                list.add(criteriaBuilder.equal(root.get("rePayStatus").as(int.class), pageVO.getRePayStatus()));
            }
            // 增加会员筛选条件
            if (StringUtils.hasLength(pageVO.getMemberName())
                    || null != pageVO.getMemberTypeId()
                    || null != pageVO.getSubRoleId()
                    || null != pageVO.getLevel()) {
                list.add(criteriaBuilder.in(root.get("memberId")).value(idList));
            }

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        // step 2: 组装分页参数
        Pageable page = PageRequest.of(pageVO.getCurrent() - 1, pageVO.getPageSize(), Sort.by("updateTime").descending());

        // step 3: 分页查询数据
        Page<CreditDO> result = creditRepository.findAll(spec, page);

        // step 4: 组装返回数据
        List<ChildCreditQueryResp> resultList = result.getContent().stream().map(r -> {
            ChildCreditQueryResp queryVO = new ChildCreditQueryResp();
            queryVO.setId(r.getId());
            queryVO.setMemberId(r.getMemberId());
            MemberFeignQueryResp memberFeignQueryResp = listWrapperResp.getData().stream()
                    .filter(item -> item.getMemberId().equals(r.getMemberId())
                            && item.getRoleId().equals(r.getMemberRoleId())).findFirst().orElse(null);
            if (memberFeignQueryResp != null) {
                queryVO.setMemberName(memberFeignQueryResp.getMemberName());
                queryVO.setMemberTypeName(memberFeignQueryResp.getMemberTypeName());
                queryVO.setMemberRoleName(memberFeignQueryResp.getRoleName());
                queryVO.setMemberLevelName(memberFeignQueryResp.getLevelTag());
            }
            queryVO.setQuota(r.getQuota());
            queryVO.setUseQuota(r.getUseQuota());
            queryVO.setCanUseQuota(r.getQuota().subtract(r.getUseQuota()));
            queryVO.setStatus(r.getStatus());
            queryVO.setStatusName(CreditStatusEnum.getItem(r.getStatus()).getMessage());
            queryVO.setRepayStatus(r.getRePayStatus());
            queryVO.setIsHasApply(r.getIsHasApply());

            CreditRepayStatusEnum repayStatusEnum = CreditRepayStatusEnum.getItem(r.getRePayStatus());
            if (CreditRepayStatusEnum.OVERDUE.getCode().equals(r.getRePayStatus())) {
                // 如果支付状态为逾期，则计算逾期天数
                Long overdueDay = DateUtil.between(DateUtil.date(r.getExpireTime()), DateUtil.date(), DateUnit.DAY, false);
                queryVO.setOverdueDay(overdueDay);
                String repayStatusName = repayStatusEnum.getRemark() + overdueDay + "天";
                queryVO.setRepayStatusName(repayStatusName);
            } else {
                queryVO.setRepayStatusName(repayStatusEnum.getRemark());
            }

            CreditApplyDO applyDo = creditApplyRepository.findByIsCurrentAndCreditIdAndIsDelete(CommonBooleanEnum.YES.getCode(), r.getId(), CommonBooleanEnum.NO.getCode());
            if (applyDo != null) {
                PaymentParameterFeignDetailResp payParameters = feignService.getCreditPayment(r.getParentMemberId(), r.getParentMemberRoleId());
                if (payParameters != null) {
                    // 获取允许申请的间隔天数
                    PayChannelParameterFeignDetailResp detailVO = payParameters.getParameters().stream().filter(p -> p.getParameter().equals(OrderPaymentParameterEnum.CREDIT_DAYS) && StringUtils.hasLength(p.getValue())).findFirst().orElse(null);
                    long intervalDay = detailVO == null ? 0L : Long.parseLong(detailVO.getValue());
                    long betweenDay = DateUtil.between(
                            DateUtil.date(applyDo.getApplyTime()),
                            DateUtil.date(System.currentTimeMillis()),
                            DateUnit.DAY);
                    // 间隔时间小于等配置时间则不允许再次申请
                    if (betweenDay < intervalDay) {
                        queryVO.setIsCanApply(CommonBooleanEnum.NO.getCode());
                    } else {
                        queryVO.setIsCanApply(CommonBooleanEnum.YES.getCode());
                    }
                } else {
                    queryVO.setIsCanApply(CommonBooleanEnum.NO.getCode());
                }
            } else {
                queryVO.setIsCanApply(CommonBooleanEnum.NO.getCode());
            }

            return queryVO;
        }).collect(Collectors.toList());

        return new PageDataResp<>(result.getTotalElements(), resultList);
    }

    /**
     * 更新会员授信状态
     * @param user 登录用户
     * @param memberCreditUpdateReq 接口参数
     * @return 记录Id
     */
    @Override
    public Long updateCreditStatus(UserLoginCacheDTO user, CreditUpdateReq memberCreditUpdateReq) {

        // step 1: 验证修改的状态符合要求
        if (!CreditStatusEnum.THAW.getCode().equals(memberCreditUpdateReq.getStatus()) && !CreditStatusEnum.FROZEN.getCode().equals(memberCreditUpdateReq.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_STATUS_PARAM_ERROR);
        }

        // step 2: 验证授信是否存在
        CreditDO creditDO = creditRepository.findById(memberCreditUpdateReq.getId()).orElse(null);
        if (creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
        }

        // step 2.1: 修改状态为正常时，原数据状态必须为冻结
        if (CreditStatusEnum.THAW.getCode().equals(memberCreditUpdateReq.getStatus())) {
            if (!creditDO.getStatus().equals(CreditStatusEnum.FROZEN.getCode())) {
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_STATUS_NOT_FROZEN);
            }
            creditDO.setIsUsable(CommonBooleanEnum.YES.getCode());
        }
        // step 2.2: 修改状态为冻结时，原数据状态必须为正常
        else {
            if (!creditDO.getStatus().equals(CreditStatusEnum.THAW.getCode())) {
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_STATUS_NOT_THAW);
            }
            creditDO.setIsUsable(CommonBooleanEnum.NO.getCode());
        }
        creditDO.setStatus(memberCreditUpdateReq.getStatus());
        creditDO.setUpdateTime(System.currentTimeMillis());

        creditRepository.saveAndFlush(creditDO);

        return creditDO.getId();
    }

    /**
     * 获取授信详情
     * @param user 登录用户
     * @param id 记录Id
     * @param isUpperSle 是否上级查询
     * @return 查询结果
     */
    @Override
    public CreditDetailResp getDetail(UserLoginCacheDTO user, Long id, Boolean isUpperSle) {

        // 验证授信是否存在
        CreditDO creditDO = creditRepository.findById(id).orElse(null);
        if (creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
        }

        CreditDetailResp result = new CreditDetailResp();

        // 赋值授信信息
        result.setId(creditDO.getId());
        result.setQuota(creditDO.getQuota());
        result.setUseQuota(creditDO.getUseQuota());

        BigDecimal quota = creditDO.getQuota();
        BigDecimal useQuota = creditDO.getUseQuota();
        result.setCanUseQuota(quota.subtract(useQuota));
        result.setBillDay(creditDO.getBillDay());
        result.setRepayPeriod(creditDO.getRepayPeriod());
        result.setLastRepayTime(null != creditDO.getLastRepayTime() && creditDO.getLastRepayTime() > 0 ? DateUtil.format(DateUtil.date(creditDO.getLastRepayTime()), "yyyy-MM-dd") : "-");

        // 查询会员信息
        CreditMemberResp member = new CreditMemberResp();
        MemberRelationFeignReq memberRelationFeignReq = new MemberRelationFeignReq();
        memberRelationFeignReq.setMemberId(creditDO.getMemberId());
        memberRelationFeignReq.setRoleId(creditDO.getMemberRoleId());
        memberRelationFeignReq.setUpperMemberId(creditDO.getParentMemberId());
        memberRelationFeignReq.setUpperRoleId(creditDO.getParentMemberRoleId());
        WrapperResp<MemberFeignQueryResp> memberFeignQueryVOWrapperResp = memberFeign.getMemberInfo(memberRelationFeignReq);
        if(memberFeignQueryVOWrapperResp == null){
            throw new BusinessException(ResponseCodeEnum.FEIGN_SERVICE_ERROR);
        }else if(memberFeignQueryVOWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
            throw new BusinessException(memberFeignQueryVOWrapperResp.getCode(),memberFeignQueryVOWrapperResp.getMessage());
        }
        // 赋值授信会员信息
        if (memberFeignQueryVOWrapperResp.getData() != null) {
            member.setMemberId(memberFeignQueryVOWrapperResp.getData().getMemberId());
            member.setParentMemberName(memberFeignQueryVOWrapperResp.getData().getMemberName());
            member.setMemberName(memberFeignQueryVOWrapperResp.getData().getSubMemberName());
            member.setMemberTypeName(memberFeignQueryVOWrapperResp.getData().getMemberTypeName());
            member.setRoleName(memberFeignQueryVOWrapperResp.getData().getRoleName());
            member.setRoleId(memberFeignQueryVOWrapperResp.getData().getRoleId());
            member.setLevelTag(memberFeignQueryVOWrapperResp.getData().getLevelTag());
            member.setLogo(memberFeignQueryVOWrapperResp.getData().getLogo());
        }
        member.setRepayStatus(creditDO.getRePayStatus());
        // 只要非冻结状态，其他状态返回正常
        if (CreditStatusEnum.FROZEN.getCode().equals(creditDO.getStatus())) {
            member.setStatus(creditDO.getStatus());
        } else {
            member.setStatus(CreditStatusEnum.THAW.getCode());
        }
        result.setMember(member);

        // 赋值授信账单下拉框信息
        List<CreditBillSelectResp> billSelectWrapperResp = creditBillService.getSelect(creditDO.getId());
        if (CollUtil.isNotEmpty(billSelectWrapperResp)) {
            result.setBillSelectList(billSelectWrapperResp);
            // 赋值授信账单信息
            CreditBillDetailResp billDetailWrapperResp = creditBillService.getDetail(billSelectWrapperResp.get(0).getId());
            result.setBill(billDetailWrapperResp);
        }

        // 赋值授信历史申请
        List<CreditHistoryApplyResp> applyListWrapperResp = creditApplyService.getHistoryList(id, isUpperSle);
        result.setHistoryApplyList(applyListWrapperResp);

        return result;
    }

    /**
     * 授信支付
     * @param payVO 接口参数
     * @return 支付结果
     */
    @Transactional
    @Override
    public CreditPayResponseResp pay(CreditPayReq payVO) {
        if (!StringUtils.hasLength(payVO.getOrderCode())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PAY_ORDER_CODE_EMPTY);
        }

        CreditDO creditDO = creditRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(payVO.getMemberId(), payVO.getRoleId(), payVO.getParentMemberId(), payVO.getParentMemberRoleId());
        if (creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
        }
        // 会员本身未开启授信支付
        if (!creditDO.getIsUsable().equals(CommonBooleanEnum.YES.getCode())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PAYMENT_NOT_OPEN);
        }

        // 上级未开启授信支付
        boolean isPass = feignService.isCreditPayment(creditDO.getParentMemberId(), creditDO.getParentMemberRoleId());
        if (!isPass) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_LINE_PAYMENT_NOT_OPEN);
        }

        // 可用金额不足
        if ((creditDO.getQuota().subtract(creditDO.getUseQuota())).compareTo(payVO.getPayMoney()) < 0) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_ENOUGH_BALANCE);
        }

        // 校验支付密码
        MemberFeignPayPswCheckReq checkVO = new MemberFeignPayPswCheckReq();
        checkVO.setMemberId(payVO.getMemberId());
        checkVO.setPayPassword(payVO.getPayPassword());
        WrapperResp<MemberFeignPayPswCheckResultResp> checkResultVOWrapperResp = memberFeign.checkMemberPayPassword(checkVO);
        if(checkResultVOWrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
            throw new BusinessException(checkResultVOWrapperResp.getCode(),checkResultVOWrapperResp.getMessage());
        }

        if (!checkResultVOWrapperResp.getData().getCheckResult().equals(MemberPayPasswordCheckEnum.CORRECT.getCode())) {
            if (checkResultVOWrapperResp.getData().getCheckResult().equals(MemberPayPasswordCheckEnum.NOT_SET.getCode())) {
                // 支付密码未设置
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PASSWORD_NOT_SET);
            } else if (checkResultVOWrapperResp.getData().getCheckResult().equals(MemberPayPasswordCheckEnum.INCORRECT.getCode())) {
                // 支付密码错误
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PASSWORD_INCORRECT);
            } else {
                // 支付状态未知时，返回服务内部错误
                throw new BusinessException(ResponseCodeEnum.BUSINESS_ERROR);
            }
        }

        // 修改当前账单
        int nowDay = DateUtil.date().dayOfMonth();
        long billBelongTime;
        // 当前天大于等于账单日，则账单所属时间为当月
        if (nowDay >= creditDO.getBillDay()) {
            billBelongTime = DateUtil.beginOfMonth(DateUtil.date()).getTime();
        } else {
            // 否则账单所属时间为前一月
            billBelongTime = DateUtil.offsetMonth(DateUtil.beginOfMonth(DateUtil.date()), -1).getTime();
        }

        CreditBillDO billDO = creditBillRepository.findByBillBelongTimeAndCreditId(billBelongTime, creditDO.getId());
        if (billDO == null) {
            // 查询是否存在对应月份账单，不存在则新增
            billDO = new CreditBillDO();
            billDO.setCreditId(creditDO.getId());
            billDO.setBillBelongTime(billBelongTime);
            billDO.setBillDay(creditDO.getBillDay());
            billDO.setRepayPeriod(creditDO.getRepayPeriod());
            Date expireTime = CreditUtil.getExpireTime(DateUtil.date(billBelongTime), creditDO.getBillDay(), creditDO.getRepayPeriod());
            billDO.setExpireTime(expireTime.getTime());
        } else {
            billDO.setUpdateTime(System.currentTimeMillis());
        }
        billDO.setBillQuota(billDO.getBillQuota().add(payVO.getPayMoney()));
        creditBillRepository.saveAndFlush(billDO);

        // 新增交易记录
        CreditTradeRecordDO recordDO = new CreditTradeRecordDO();
        recordDO.setCreditId(billDO.getCreditId());
        recordDO.setBillId(billDO.getId());
        String tradeCode = redisUtils.getSerialNumberByDay(PayConstant.CREDIT_TRACE_CODE, PayConstant.CREDIT_TRACE_CODE_DATE, PayConstant.CREDIT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);
        recordDO.setTradeCode(tradeCode);
        recordDO.setTradeTime(System.currentTimeMillis());
        recordDO.setTradeMoney(payVO.getPayMoney());
        recordDO.setOperation(CreditTradeOperationEnum.PAY.getCode());
        recordDO.setStatus(CreditTradeStatusEnum.CONFIRM_RECEIPT.getCode());
        recordDO.setOrderCode(payVO.getOrderCode());
        recordDO.setTradeType(CreditTradeConstant.TradeType.ONLINE);
        recordDO.setTradeChannel(CreditTradeConstant.TradeChannel.CREDIT);
        recordDO.setRemark(payVO.getRemark());
        recordDO.setIsTemp(CommonBooleanEnum.NO.getCode());
        creditTradeRecordRepository.saveAndFlush(recordDO);

        // 更新授信
        creditDO.setUseQuota(creditDO.getUseQuota().add(payVO.getPayMoney()));
        //如果当前还款状态为：账单已还清则在支付后修改还款状态为：账单待还，其他状态不做修改
        if (CreditRepayStatusEnum.PAY_OFF.getCode().equals(creditDO.getRePayStatus())) {
            creditDO.setRePayStatus(CreditRepayStatusEnum.WAIT_REPAY.getCode());
        }
        creditDO.setUpdateTime(System.currentTimeMillis());
        creditRepository.saveAndFlush(creditDO);

        CreditPayResponseResp result = new CreditPayResponseResp();
        result.setPayId(recordDO.getId());
        result.setPayCode(recordDO.getTradeCode());

        return result;
    }

    /**
     * 授信还款
     * @param user 登录用户
     * @param vo 接口参数
     * @param httpRequest Http请求
     * @return 还款结果
     */
    @Override
    @Transactional
    public CreditRepayResultResp repay(UserLoginCacheDTO user, CreditRepayReq vo, HttpServletRequest httpRequest) {

        if (!CreditTradeConstant.TradeType.ONLINE.equals(vo.getTradeType())
                && !CreditTradeConstant.TradeType.OFFLINE.equals(vo.getTradeType())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PAY_TRADE_TYPE_ERROR);
        }
        if (CreditTradeConstant.TradeType.OFFLINE.equals(vo.getTradeType())) {
            if (vo.getPayProveList() == null || vo.getPayProveList().size() <= 0) {
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_OFFLINE_PAY_PROVE_EMPTY);
            }
            for (PayProveBO proveBO : vo.getPayProveList()) {
                if (!StringUtils.hasLength(proveBO.getName()) || !StringUtils.hasLength(proveBO.getProveUrl())) {
                    throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_OFFLINE_PAY_PROVE_PARAM_ERROR);
                }
            }
        } else {
            if (!CreditTradeConstant.TradeChannel.WECHAT.equals(vo.getTradeChannel())
                    && !CreditTradeConstant.TradeChannel.ALI.equals(vo.getTradeChannel())
                    && !CreditTradeConstant.TradeChannel.UNION.equals(vo.getTradeChannel())
                    && !CreditTradeConstant.TradeChannel.BALANCE.equals(vo.getTradeChannel())) {
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PAY_TRADE_CHANNEL_ERROR);
            }
        }

        CreditBillDO billDO = creditBillRepository.findById(vo.getBillId()).orElse(null);
        if (billDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_BILL_NOT_EXIST);
        }
        BigDecimal billQuota = billDO.getBillQuota();
        BigDecimal repayQuota = billDO.getRepayQuota();
        if (billQuota.subtract(repayQuota).compareTo(vo.getRepayQuota()) < 0) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_OVERSTEP_BILL_REPAY);
        }
        if (billDO.getWaitConfirmRepayQuota().compareTo(BigDecimal.ZERO) > 0) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_BILL_LAST_REPAY_NOT_CONFIRM);
        }
        CreditDO creditDO = creditRepository.findById(billDO.getCreditId()).orElse(null);
        if (creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
        }

        // 线下交易才需要设置待确认还款金额
        if (CreditTradeConstant.TradeType.OFFLINE.equals(vo.getTradeType())) {
            billDO.setWaitConfirmRepayQuota(billDO.getWaitConfirmRepayQuota().add(vo.getRepayQuota()));
        }
        // 更新账单记录
        // 待确认还款=原待确认还款+还款额度
        billDO.setLastRepayDate(System.currentTimeMillis());
        billDO.setUpdateTime(System.currentTimeMillis());
        creditBillRepository.saveAndFlush(billDO);

        // 新增交易记录
        CreditTradeRecordDO recordDO = new CreditTradeRecordDO();
        recordDO.setCreditId(billDO.getCreditId());
        recordDO.setBillId(billDO.getId());
        String tradeCode = redisUtils.getSerialNumberByDay(PayConstant.CREDIT_TRACE_CODE, PayConstant.CREDIT_TRACE_CODE_DATE, PayConstant.CREDIT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);
        recordDO.setTradeCode(tradeCode);
        recordDO.setTradeTime(System.currentTimeMillis());
        recordDO.setTradeMoney(vo.getRepayQuota());
        recordDO.setOperation(CreditTradeOperationEnum.REPAY.getCode());
        recordDO.setStatus(CreditTradeStatusEnum.WAIT_CONFIRM.getCode());
        recordDO.setRemark("");
        recordDO.setOrderCode("");
        recordDO.setTradeType(vo.getTradeType());
        recordDO.setTradeChannel(vo.getTradeChannel());
        recordDO.setPayProveList(vo.getPayProveList());
        // 如果是线上还款，交易记录默认为临时
        if (CreditTradeConstant.TradeType.ONLINE.equals(vo.getTradeType())) {
            recordDO.setIsTemp(CommonBooleanEnum.YES.getCode());
        } else {
            recordDO.setIsTemp(CommonBooleanEnum.NO.getCode());
        }
        creditTradeRecordRepository.saveAndFlush(recordDO);

        CreditRepayResultResp result = new CreditRepayResultResp();
        result.setRecordId(recordDO.getId());
        result.setStatus(recordDO.getStatus());
        result.setStatusName(CreditTradeStatusEnum.getItemMessage(recordDO.getStatus()));

        if (CreditTradeConstant.TradeType.ONLINE.equals(vo.getTradeType())) {
            // 如果是线上微信还款，则返回支付二维码收款字符串
            if (CreditTradeConstant.TradeChannel.WECHAT.equals(vo.getTradeChannel())) {
                MobilePayReq mobilePayReq = new MobilePayReq();
                mobilePayReq.setMemberId(creditDO.getParentMemberId());
                mobilePayReq.setMemberRoleId(creditDO.getParentMemberRoleId());
                mobilePayReq.setOrderCode(recordDO.getTradeCode());
                mobilePayReq.setPayMoney(recordDO.getTradeMoney());
                mobilePayReq.setServiceType(ServiceTypeEnum.Pay_Credit.getCode());
                mobilePayReq.setRemark("授信微信还款");
                String wrapperResp = feignService.creditWeChatRepay(mobilePayReq, PayTypeEnum.MEMBER.getCode(), httpRequest);
                if(wrapperResp == null){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    throw new BusinessException(ResponseCodeEnum.FEIGN_SERVICE_ERROR);
                }
//                else if(wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
//                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//                    throw new BusinessException(wrapperResp.getCode(),wrapperResp.getMessage());
//                }
                result.setPayQRCode(wrapperResp);
            } else if (CreditTradeConstant.TradeChannel.BALANCE.equals(vo.getTradeChannel())) {
                BalancePayReq payRequest = new BalancePayReq();
                payRequest.setMemberId(creditDO.getMemberId());
                payRequest.setMemberRoleId(creditDO.getMemberRoleId());
                payRequest.setParentMemberId(creditDO.getParentMemberId());
                payRequest.setParentMemberRoleId(creditDO.getParentMemberRoleId());
                payRequest.setOrderCode(recordDO.getTradeCode());
                payRequest.setPayMoney(recordDO.getTradeMoney());
                payRequest.setPayPassword(vo.getPassWord());
                payRequest.setPayType(PayTypeEnum.MEMBER.getCode());
                payRequest.setRemark("授信余额还款");
                String wrapperResp = feignService.creditBalanceRepay(payRequest);
                if(wrapperResp == null){
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    throw new BusinessException(ResponseCodeEnum.FEIGN_SERVICE_ERROR);
                }
//                else if(wrapperResp.getCode() != ResponseCodeEnum.SUCCESS.getCode()){
//                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//                    throw new BusinessException(wrapperResp.getCode(),wrapperResp.getMessage());
//                }
                recordDO.setStatus(CreditTradeStatusEnum.CONFIRM_RECEIPT.getCode());
                recordDO.setRemark("余额还款：" + wrapperResp);
                recordDO.setIsTemp(CommonBooleanEnum.NO.getCode());
                creditTradeRecordRepository.saveAndFlush(recordDO);

                // 修改账单
                billDO.setRepayQuota(billDO.getRepayQuota().add(recordDO.getTradeMoney()));
                billDO.setLastRepayDate(System.currentTimeMillis());
                billDO.setLastRepayQuota(recordDO.getTradeMoney());
                // 账单已还清时，记录最后还清时间
                if (billDO.getBillQuota().compareTo(billDO.getRepayQuota()) <= 0) {
                    billDO.setPayOffDate(System.currentTimeMillis());
                }
                // 上线还款没有待确认还款金额
                billDO.setUpdateTime(System.currentTimeMillis());
                creditBillRepository.saveAndFlush(billDO);

                // 更新授信还款状态
                AopProxyUtil.getCurrentProxy(this.getClass()).updateCreditRepayStatus(creditDO, billDO, recordDO.getTradeMoney());

                result.setStatus(recordDO.getStatus());
                result.setStatusName(CreditTradeStatusEnum.getItemMessage(recordDO.getStatus()));
            } else {
                // 其他还款方式尚未对接
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PAY_TRADE_CHANNEL_ERROR);
            }
        }

        // 如果是线下还款,更改授信还款状态为待确认还款
        if (CreditTradeConstant.TradeType.OFFLINE.equals(vo.getTradeType())) {
            creditDO.setRePayStatus(CreditRepayStatusEnum.WAIT_CONFIRM.getCode());
            creditRepository.saveAndFlush(creditDO);
        }

        return result;
    }

    /**
     * 获取授信还款结果
     * @param user 登录用户
     * @param recordId 记录Id
     * @return 查询结果
     */
    @Override
    public CreditRepayResultResp getCreditRepayResult(UserLoginCacheDTO user, Long recordId) {
        CreditTradeRecordDO recordDO = creditTradeRecordRepository.findById(recordId).orElse(null);
        if (recordDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_TRADE_RECORD_NOT_EXIST);
        }

        CreditRepayResultResp result = new CreditRepayResultResp();
        result.setRecordId(recordId);
        result.setStatus(recordDO.getStatus());
        result.setStatusName(CreditTradeStatusEnum.getItemMessage(recordDO.getStatus()));

        return result;
    }

    /**
     * 确认还款成功
     * @param tradeCode 交易单号
     * @param orderCode 订单号
     */
    @Override
    public void confirmRepaySucceed(String tradeCode, String orderCode) {
        CreditTradeRecordDO recordDO = creditTradeRecordRepository.findByTradeCode(tradeCode);
        if (recordDO == null) {
            log.info("确认还款失败，没有找到指定的交易记录：{}", tradeCode);
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_TRADE_RECORD_NOT_EXIST);
        }
        if (!CreditTradeStatusEnum.WAIT_CONFIRM.getCode().equals(recordDO.getStatus())) {
            log.info("确认还款失败：{}，现有状态为：{}", tradeCode, recordDO.getStatus());
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_TRADE_RECORD_STATUS_PARAM_ERROR);
        }
        CreditBillDO billDO = creditBillRepository.findById(recordDO.getBillId()).orElse(null);
        if (billDO == null) {
            log.info("确认还款失败，没有找到指定的账单：{}", recordDO.getBillId());
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_BILL_NOT_EXIST);
        }
        CreditDO creditDO = creditRepository.findById(recordDO.getCreditId()).orElse(null);
        if (creditDO == null) {
            log.info("确认还款失败，没有找到指定的授信：{}", recordDO.getCreditId());
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
        }

        // 修改交易记录
        recordDO.setIsTemp(CommonBooleanEnum.NO.getCode());
        recordDO.setOrderCode(orderCode);
        recordDO.setStatus(CreditTradeStatusEnum.CONFIRM_RECEIPT.getCode());
        creditTradeRecordRepository.saveAndFlush(recordDO);

        // 修改账单
        billDO.setRepayQuota(billDO.getRepayQuota().add(recordDO.getTradeMoney()));
        billDO.setLastRepayDate(System.currentTimeMillis());
        billDO.setLastRepayQuota(recordDO.getTradeMoney());
        // 账单已还清时，记录最后还清时间
        if (billDO.getBillQuota().compareTo(billDO.getRepayQuota()) <= 0) {
            billDO.setPayOffDate(System.currentTimeMillis());
        }
        // 上线还款没有待确认还款金额
        billDO.setUpdateTime(System.currentTimeMillis());
        creditBillRepository.saveAndFlush(billDO);

        // 更新授信还款状态
        AopProxyUtil.getCurrentProxy(this.getClass()).updateCreditRepayStatus(creditDO, billDO, recordDO.getTradeMoney());

    }

    /**
     * 确认还款
     * @param user 登录用户
     * @param vo 接口参数
     * @return 还款结果
     */
    @Override
    @Transactional
    public void confirmRepay(UserLoginCacheDTO user, CreditConfirmRepayReq vo) {
        if (!vo.getStatus().equals(CreditTradeStatusEnum.CONFIRM_NOT_RECEIPT.getCode())
                && !vo.getStatus().equals(CreditTradeStatusEnum.CONFIRM_RECEIPT.getCode())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_TRADE_RECORD_STATUS_PARAM_ERROR);
        }

        CreditTradeRecordDO recordDO = creditTradeRecordRepository.findById(vo.getRecordId()).orElse(null);
        if (recordDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_TRADE_RECORD_NOT_EXIST);
        }

        if (!CreditTradeStatusEnum.WAIT_CONFIRM.getCode().equals(recordDO.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_TRADE_RECORD_NOT_WAIT_CONFIRM);
        }

        CreditBillDO billDO = creditBillRepository.findById(recordDO.getBillId()).orElse(null);
        if (billDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_BILL_NOT_EXIST);
        }

        CreditDO creditDO = creditRepository.findById(recordDO.getCreditId()).orElse(null);
        if (creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
        }

        // 修改交易记录
        recordDO.setStatus(vo.getStatus());
        creditTradeRecordRepository.saveAndFlush(recordDO);

        // 修改账单
        if (CreditTradeStatusEnum.CONFIRM_RECEIPT.getCode().equals(vo.getStatus())) {
            billDO.setRepayQuota(billDO.getRepayQuota().add(recordDO.getTradeMoney()));

            billDO.setLastRepayDate(System.currentTimeMillis());
            billDO.setLastRepayQuota(recordDO.getTradeMoney());
            // 账单已还清时，记录最后还清时间
            if (billDO.getBillQuota().compareTo(billDO.getRepayQuota()) <= 0) {
                billDO.setPayOffDate(System.currentTimeMillis());
            }

            // 更新授信还款状态
            AopProxyUtil.getCurrentProxy(this.getClass()).updateCreditRepayStatus(creditDO, billDO, recordDO.getTradeMoney());
        }

        // 更新账单待还款金额
        billDO.setWaitConfirmRepayQuota(billDO.getWaitConfirmRepayQuota().subtract(recordDO.getTradeMoney()));
        billDO.setUpdateTime(System.currentTimeMillis());
        creditBillRepository.saveAndFlush(billDO);

        // 已用额度大于0，授信还款状态修改为：账单待还
        if (creditDO.getUseQuota().compareTo(BigDecimal.ZERO) > 0) {
            creditDO.setRePayStatus(CreditRepayStatusEnum.WAIT_REPAY.getCode());
            creditRepository.saveAndFlush(creditDO);
        }
    }

    /**
     * 更新授信还款状态（内部接口不验证参数）
     * <AUTHOR>
     * @since 2021/1/11
     * @param creditDO: 授信
     * @param billDO: 账单
     * @param tradeMoney: 交易金额
     **/
    @Transactional
    public void updateCreditRepayStatus(CreditDO creditDO, CreditBillDO billDO, BigDecimal tradeMoney) {

        // 修改授信
        creditDO.setUseQuota(creditDO.getUseQuota().subtract(tradeMoney));
        creditDO.setLastRepayTime(System.currentTimeMillis());
        creditDO.setUpdateTime(System.currentTimeMillis());
        // 已用额度小于等于0，授信还款状态修改为：账单已还清，反之修改为：账单待还
        if (creditDO.getUseQuota().compareTo(BigDecimal.ZERO) <= 0) {
            creditDO.setRePayStatus(CreditRepayStatusEnum.PAY_OFF.getCode());
        } else {
            creditDO.setRePayStatus(CreditRepayStatusEnum.WAIT_REPAY.getCode());
        }

        // 账单已还清时，查询是否还有逾期记录
        if (billDO.getBillQuota().compareTo(billDO.getRepayQuota()) <= 0) {
            // 查询除当前账单其他未还完逾期账单
            CreditBillDO billDO1 = getOldestOverdue(billDO.getCreditId(), billDO.getId());
            // 有逾期，则更改授信到期日期，还款状态为：逾期
            if (billDO1 != null) {
                creditDO.setExpireTime(billDO1.getExpireTime());
                creditDO.setRePayStatus(CreditRepayStatusEnum.OVERDUE.getCode());
            }
        } else {
            // 如果当前账单为逾期账单（到期时间早已系统当前时间），修改授信还款状态为：逾期
            if (billDO.getExpireTime() < System.currentTimeMillis()) {
                creditDO.setExpireTime(billDO.getExpireTime());
                creditDO.setRePayStatus(CreditRepayStatusEnum.OVERDUE.getCode());
            }
        }

        creditDO.setUpdateTime(System.currentTimeMillis());
        creditRepository.saveAndFlush(creditDO);
    }

    /**
     * 获取最旧的逾期账单
     * <AUTHOR>
     * @since 2020/8/28
     * @param creditId:
     * @param billId:
     * @return com.ssy.lingxi.pay.entity.do_.CreditBillDO
     **/
    private CreditBillDO getOldestOverdue(Long creditId, Long billId) {
        // 组装查询条件
        Specification<CreditBillDO> spec =  (root, query, criteriaBuilder) -> {
            List<Predicate> list = new ArrayList<>();
            // 指定授信id
            list.add(criteriaBuilder.equal(root.get("creditId").as(Long.class), creditId));
            // 已到期
            list.add(criteriaBuilder.lessThan(root.get("expireTime").as(Long.class), System.currentTimeMillis()));
            // 还款额度小于账单额度
            list.add(criteriaBuilder.lessThan(root.get("repayQuota").as(Long.class), root.get("billQuota").as(Long.class)));
            // 排除当前账单
            list.add(criteriaBuilder.notEqual(root.get("id").as(Long.class), billId));

            Predicate[] p = new Predicate[list.size()];
            return criteriaBuilder.and(list.toArray(p));
        };

        return creditBillRepository.findAll(spec, Sort.by(Sort.Order.asc("expireTime"))).stream().findFirst().orElse(null);
    }

    /**
     * 退款
     * @param refundVO 接口参数
     * @return 退款交易号
     */
    @Override
    @Transactional
    public String refund(CreditRefundReq refundVO) {
        CreditTradeRecordDO recordDO = creditTradeRecordRepository.findByTradeCode(refundVO.getPayCode());
        if (recordDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PAY_RECORD_NOT_EXIST);
        }

        if (recordDO.getTradeMoney().compareTo(refundVO.getRefundAmount()) < 0 || refundVO.getRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_REFUND_AMOUNT_ERROR);
        }

        if (!recordDO.getTradeCode().equals(refundVO.getPayCode())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PAY_RECORD_TRADE_CODE_ERROR);
        }

        // 查询已退款金额
        BigDecimal tradeMoney = creditTradeRecordRepository.sumTradeMoney(recordDO.getBillId(), CreditTradeOperationEnum.REFUND.getCode(), recordDO.getId().toString());
        tradeMoney = (null != tradeMoney ? tradeMoney : BigDecimal.ZERO);
        if (recordDO.getTradeMoney().compareTo(refundVO.getRefundAmount().add(tradeMoney)) < 0) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_REFUND_AMOUNT_ERROR);
        }
        CreditBillDO billDO = creditBillRepository.findById(recordDO.getBillId()).orElse(null);
        if (billDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_BILL_NOT_EXIST);
        }

        CreditDO creditDO = creditRepository.findById(billDO.getCreditId()).orElse(null);
        if (creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
        }

        if (!creditDO.getMemberId().equals(refundVO.getMemberId()) || !creditDO.getMemberRoleId().equals(refundVO.getMemberRoleId())
                || !creditDO.getParentMemberId().equals(refundVO.getParentMemberId()) || !creditDO.getParentMemberRoleId().equals(refundVO.getParentMemberRoleId())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_PAY_RECORD_NOT_EXIST);
        }

        // 修改账单
        billDO.setRefundQuota(billDO.getRefundQuota().add(refundVO.getRefundAmount()));
        billDO.setRepayQuota(billDO.getRepayQuota().add(refundVO.getRefundAmount()));
        billDO.setLastRepayDate(System.currentTimeMillis());
        billDO.setLastRepayQuota(refundVO.getRefundAmount());
        // 账单已还清时，记录最后还清时间
        if (billDO.getBillQuota().compareTo(billDO.getRepayQuota()) <= 0) {
            billDO.setPayOffDate(System.currentTimeMillis());
        }
        billDO.setUpdateTime(System.currentTimeMillis());
        creditBillRepository.saveAndFlush(billDO);

        // 更新授信还款状态
        AopProxyUtil.getCurrentProxy(this.getClass()).updateCreditRepayStatus(creditDO, billDO, refundVO.getRefundAmount());

        // 新增退款交易记录，原支付信息复制到退款记录中
        CreditTradeRecordDO recordAddDO = new CreditTradeRecordDO();
        recordAddDO.setCreditId(billDO.getCreditId());
        recordAddDO.setBillId(billDO.getId());
        String tradeCode = redisUtils.getSerialNumberByDay(PayConstant.CREDIT_TRACE_CODE, PayConstant.CREDIT_TRACE_CODE_DATE, PayConstant.CREDIT_TRACE_CODE_NUM_LEN, RedisConstant.REDIS_ORDER_INDEX);
        recordAddDO.setTradeCode(tradeCode);
        recordAddDO.setTradeTime(System.currentTimeMillis());
        recordAddDO.setTradeMoney(refundVO.getRefundAmount());
        recordAddDO.setOperation(CreditTradeOperationEnum.REFUND.getCode());    // 设置退款
        recordAddDO.setStatus(CreditTradeStatusEnum.CONFIRM_RECEIPT.getCode()); // 退款直接到账
        recordAddDO.setRemark(refundVO.getRemark());
        recordAddDO.setOrderCode(recordDO.getId().toString());  // 设置原始支付记录Id为订单id
        recordAddDO.setTradeType(recordDO.getTradeType());
        recordAddDO.setTradeChannel(recordDO.getTradeChannel());
        recordAddDO.setIsTemp(CommonBooleanEnum.NO.getCode());
        recordAddDO.setPayProveList(null);
        creditTradeRecordRepository.saveAndFlush(recordAddDO);

        return recordAddDO.getTradeCode();
    }

    /**
     * 采购商分页查询会员列表页面搜索条件内容
     * @param user 登录用户
     * @return 查询结果
     */
    @Override
    public CreditPageItemsResp pageItemsByConsumer(UserLoginCacheDTO user) {
        CreditPageItemsResp result = new CreditPageItemsResp();
        // 组装授信状态
        result.setStatusList(convertCreditStatus());
        // 组装还款状态
        result.setRepayStatusList(convertCreditRepayStatus());
        // 组装外部状态
        result.setOuterStatusList(convertCreditOuterStatus(false));
        // 组装内部状态
        result.setInnerStatusList(convertCreditInnerStatus(false));

        return result;
    }

    /**
     * 供应商商分页查询会员列表页面搜索条件内容
     * @param user 登录用户
     * @return 查询结果
     */
    @Override
    public CreditPageItemsResp pageItemsBySupplier(UserLoginCacheDTO user) {

        CreditPageItemsResp result = new CreditPageItemsResp();

        // 组装授信状态
        result.setStatusList(convertCreditStatus());
        // 组装还款状态
        result.setRepayStatusList(convertCreditRepayStatus());
        // 组装外部状态
        result.setOuterStatusList(convertCreditOuterStatus(true));
        // 组装内部状态
        result.setInnerStatusList(convertCreditInnerStatus(true));
        // 组装申请类型
        result.setApplyTypeList(convertCreditApplyType());

        return result;
    }

    /**
     * 获取授信
     * @param user 登录用户
     * @param request 接口参数
     * @return 查询结果
     */
    @Override
    public CreditResp getCredit(UserLoginCacheDTO user, CreditReq request) {

        CreditRequestReq requestVO = new CreditRequestReq();
        requestVO.setMemberId(user.getMemberId());
        requestVO.setRoleId(user.getMemberRoleId());
        requestVO.setParentMemberId(request.getParentMemberId());
        requestVO.setParentMemberRoleId(request.getParentMemberRoleId());

        return getCreditDetail(requestVO);
    }

    /**
     * 获取会员授信
     * @param user 登录用户
     * @param request 接口参数
     * @return 查询结果
     */
    @Override
    public CreditResp getMemberCredit(UserLoginCacheDTO user, CreditRequestReq request) {
        return getCreditDetail(request);
    }

    /**
     * 获取授信信息
     * <AUTHOR>
     * @since 2020/12/28
     * @param request 上、下级会员Id和角色Id
     * @return 查询结果
     **/
    @Override
    public CreditResp getCreditDetail(CreditRequestReq request) {
        // 验证授信是否存在
        CreditDO creditDO = creditRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(
                request.getMemberId(), request.getRoleId(), request.getParentMemberId(), request.getParentMemberRoleId());
        if (creditDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_NOT_EXIST);
        }

        CreditResp result = new CreditResp();

        // 赋值授信信息
        result.setQuota(creditDO.getQuota());
        result.setUseQuota(creditDO.getUseQuota());
        result.setCanUseQuota(creditDO.getQuota().subtract(creditDO.getUseQuota()));
        result.setIsUsable(CommonBooleanEnum.NO.getCode());
        if (creditDO.getIsUsable().equals(CommonBooleanEnum.YES.getCode())) {
            Boolean isOpen = feignService.isCreditPayment(creditDO.getParentMemberId(), creditDO.getParentMemberRoleId());
            if (isOpen) {
                result.setIsUsable(CommonBooleanEnum.YES.getCode());
            }
        }

        return result;
    }

    /**
     * 组装授信状态
     * <AUTHOR>
     * @since 2020/10/16
     * @return java.util.List<com.ssy.lingxi.pay.model.resp.CreditPageItemVO>
     **/
    private List<CreditPageItemResp> convertCreditStatus() {
        return Arrays.stream(CreditStatusEnum.values()).map(r -> {
            CreditPageItemResp status = new CreditPageItemResp();
            status.setStatus(r.getCode());
            status.setName(r.getMessage());
            return status;
        }).collect(Collectors.toList());
    }

    /**
     * 组装授信还款状态
     * <AUTHOR>
     * @since 2020/10/16
     * @return java.util.List<com.ssy.lingxi.pay.model.resp.CreditPageItemVO>
     **/
    private List<CreditPageItemResp> convertCreditRepayStatus() {
        return Arrays.stream(CreditRepayStatusEnum.values()).map(r -> {
            CreditPageItemResp status = new CreditPageItemResp();
            status.setStatus(r.getCode());
            status.setName(r.getMessage());
            return status;
        }).collect(Collectors.toList());
    }

    /**
     * 组装授信申请外部状态
     * <AUTHOR>
     * @since 2020/10/16
     * @param isSupplier:
     * @return java.util.List<com.ssy.lingxi.pay.model.resp.CreditPageItemVO>
     **/
    private List<CreditPageItemResp> convertCreditOuterStatus(Boolean isSupplier) {

        return Arrays.stream(CreditApplyOuterStatusEnum.values()).filter(r -> {
            if (isSupplier) {
                return !r.getCode().equals(CreditApplyOuterStatusEnum.WAIT_SUBMIT.getCode());
            } else {
                return true;
            }
        }).map(r -> {
            CreditPageItemResp status = new CreditPageItemResp();
            status.setStatus(r.getCode());
            status.setName(r.getMessage());
            return status;
        }).collect(Collectors.toList());
    }

    /**
     * 组装授信申请内部状态
     * <AUTHOR>
     * @since 2020/10/16
     * @param isSupplier:
     * @return java.util.List<com.ssy.lingxi.pay.model.resp.CreditPageItemVO>
     **/
    private List<CreditPageItemResp> convertCreditInnerStatus(Boolean isSupplier) {

        if (isSupplier) {
            return Arrays.stream(CreditApplySuperiorInnerStatusEnum.values()).map(r -> {
                CreditPageItemResp status = new CreditPageItemResp();
                status.setStatus(r.getCode());
                status.setName(r.getMessage());
                return status;
            }).collect(Collectors.toList());
        } else {
            return Arrays.stream(CreditApplyLowerInnerStatusEnum.values()).map(r -> {
                CreditPageItemResp status = new CreditPageItemResp();
                status.setStatus(r.getCode());
                status.setName(r.getMessage());
                return status;
            }).collect(Collectors.toList());
        }
    }

    /**
     * 组装授信申请类型
     * <AUTHOR>
     * @since 2020/10/16
     * @return java.util.List<com.ssy.lingxi.pay.model.resp.CreditPageItemVO>
     **/
    private List<CreditPageItemResp> convertCreditApplyType() {
        return Arrays.stream(CreditApplyTypeEnum.values()).map(r -> {
            CreditPageItemResp status = new CreditPageItemResp();
            status.setStatus(r.getCode());
            status.setName(r.getMessage());
            return status;
        }).collect(Collectors.toList());
    }

    /**
     * 冻结授信
     * @param frozenCreditReq 接口参数
     */
    @Override
    public void frozenCredit(FrozenCreditReq frozenCreditReq) {
        // step 1: 验证修改的状态符合要求
        if (!CommonBooleanEnum.YES.getCode().equals(frozenCreditReq.getStatus()) && !CommonBooleanEnum.NO.getCode().equals(frozenCreditReq.getStatus())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_STATUS_PARAM_ERROR);
        }

        // step 2: 验证授信是否存在
        CreditDO creditDO = creditRepository.findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(frozenCreditReq.getMemberId(), frozenCreditReq.getRoleId(), frozenCreditReq.getParentMemberId(), frozenCreditReq.getParentMemberRoleId());
        if (creditDO != null) {
            if (CommonBooleanEnum.YES.getCode().equals(frozenCreditReq.getStatus())) {
                creditDO.setIsUsable(CommonBooleanEnum.NO.getCode());
                creditDO.setStatus(CreditStatusEnum.FROZEN.getCode());
            } else {
                creditDO.setIsUsable(CommonBooleanEnum.YES.getCode());
                creditDO.setStatus(CreditStatusEnum.THAW.getCode());
            }
            creditDO.setUpdateTime(System.currentTimeMillis());

            creditRepository.saveAndFlush(creditDO);
        }
    }

    /**
     * 获取可用余额
     * @param user 登录用户
     * @return 可用余额
     */
    @Override
    public BigDecimal getCanUseQuota(UserLoginCacheDTO user) {
        return creditRepository.sumCanUseQuota(user.getMemberId(), user.getMemberRoleId());
    }

    /**
     * 检查是否申请授信
     * @param user 登录用户
     * @return 检查结果
     */
    @Override
    public Boolean existApplyCredit(UserLoginCacheDTO user) {
        Long count = creditRepository.existApplyCredit(user.getMemberId(), user.getMemberRoleId());
        return count > 0;
    }
}
