package com.ssy.lingxi.pay.model.req.allInPay;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 个人实名认证
 *
 * <AUTHOR>
 */
@Data
public class AllInPaySetRealNameReq {

    /**
     * 通联用户名称
     */
    @NotNull(message = "请输入姓名")
    private String name;
    /**
     * 证件类型(目前只支持身份证) 1-身份证 2-护照 3-军官证。。。
     */
    @NotNull(message = "请选择证件类型")
    private Integer identityCardType;

    /**
     * 证件号码
     */
    @NotNull(message = "请输入证件号码")
    private String identityCardNo;

}
