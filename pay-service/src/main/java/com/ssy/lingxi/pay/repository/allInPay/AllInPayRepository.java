package com.ssy.lingxi.pay.repository.allInPay;

import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/26
 */
@Repository
public interface AllInPayRepository extends JpaRepository<AllInPayDO, Long>, JpaSpecificationExecutor<AllInPayDO> {
    AllInPayDO findByBizUserId(String bizUserId);

    AllInPayDO findFirstByMemberIdAndMemberRoleId(Long memberId, Long memberRoleId);

    List<AllInPayDO> findAllByMemberIdAndMemberRoleId(Long memberId, Long memberRoleId);

}
