package com.ssy.lingxi.pay.repository.assetAccount;

import com.ssy.lingxi.pay.entity.do_.assetAccount.MemberAssetAccountDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.List;

/**
 * 会员资金账户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
@Repository
public interface MemberAssetAccountRepository extends JpaRepository<MemberAssetAccountDO,Long>, JpaSpecificationExecutor<MemberAssetAccountDO> {
    List<MemberAssetAccountDO> findByMemberIdAndMemberRoleId(Long memberId, Long memberRoleId);

    List<MemberAssetAccountDO> findByMemberIdAndMemberRoleIdAndMemberLevelType(Long memberId, Long memberRoleId, Integer memberLevelType);

    MemberAssetAccountDO findFirstByMemberIdAndMemberRoleIdAndMemberLevelType(Long memberId, Long memberRoleId, Integer memberLevelType);

    List<MemberAssetAccountDO> findByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(Long memberId, Long memberRoleId, Long parentMemberId, Long parentMemberRoleId);

    MemberAssetAccountDO findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(Long memberId, Long memberRoleId, Long parentMemberId, Long parentMemberRoleId);

    List<MemberAssetAccountDO> findByMemberId(Long memberId);

    List<MemberAssetAccountDO> findByMemberIdAndParentMemberRoleIdAndParentMemberIdAndMemberRoleId(Long memberId, Long parentMemberRoleId, Long parentMemberId, Long memberRoleId);

    List<MemberAssetAccountDO> findByMemberCode(@NotEmpty(message = "主客户编码，不能为空") @NotBlank String khbm);

    List<MemberAssetAccountDO> findByParentMemberRoleIdAndParentMemberIdAndMemberRoleIdAndMemberCodeIn(Long parentMemberRoleId, Long parentMemberId, Long memberRoleId, Collection<String> codes);

    List<MemberAssetAccountDO> findByParentMemberRoleIdAndParentMemberIdAndMemberRoleIdAndMemberCode(Long parentMemberRoleId, Long parentMemberId, Long memberRoleId, String code);

    List<MemberAssetAccountDO> findByMemberIdInAndParentMemberRoleIdAndParentMemberIdAndMemberRoleId(Collection<Long> memberIds, Long parentMemberRoleId, Long parentMemberId, Long memberRoleId);
}
