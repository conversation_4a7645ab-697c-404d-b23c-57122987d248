package com.ssy.lingxi.pay.repository;

import com.ssy.lingxi.pay.entity.do_.CreditVerifyDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 授信审批
 * <AUTHOR>
 * @since 2020/8/18
 * @version 2.0.0
 */
@Repository
public interface CreditVerifyRepository extends JpaRepository<CreditVerifyDO, Long>, JpaSpecificationExecutor<CreditVerifyDO> {

    /**
     * 获取授信审核信息
     * <AUTHOR>
     * @since 2020/8/18
     **/
    CreditVerifyDO findByApplyId(Long applyId);
}
