package com.ssy.lingxi.pay.serviceImpl.assetAccount;

import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountStatusRecordDO;
import com.ssy.lingxi.pay.repository.assetAccount.AccountStatusRecordRepository;
import com.ssy.lingxi.pay.service.assetAccount.IAccountStatusService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 会员资金账户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
@Service
public class AccountStatusServiceImpl implements IAccountStatusService {

    @Autowired
    private AccountStatusRecordRepository accountStatusRecordRepository;

    /**
     * 查询会员资金账户状态记录
     * @return
     */
    @Override
    public List<AccountStatusRecordDO> getAccountStatusRecord(Long memberAssetAccountId){
        return accountStatusRecordRepository.findByMemberAssetAccountIdOrderByCreateTimeAsc(memberAssetAccountId);
    }
}

