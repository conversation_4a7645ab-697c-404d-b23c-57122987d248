package com.ssy.lingxi.pay.serviceImpl.init;

import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayBankDO;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayBankRepository;
import com.ssy.lingxi.pay.service.init.IAllInPayInitService;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 初始化数据实现类
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2022/1/7
 */
@Service
public class AllInPayInitServiceImpl implements IAllInPayInitService {
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private AllInPayBankRepository allInPayBankRepository;

    /**
     * 初始化数据
     */
    @Transactional(rollbackFor = BusinessException.class)
    @Override
    public void initData() {
        initBankData();
    }

    /**
     * 初始化通联对公银行数据
     */
    private void initBankData() {
        if (countTable("pay_all_in_pay_bank") > 0) {
            return;
        }
        List<AllInPayBankDO> payBanks = new ArrayList<>();
        AllInPayBankDO allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("安顺西航南马村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("白城农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("包商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("包商银行股份有限公司鞍山道支行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("保定银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("北京农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("北京农商行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("北京顺义银座村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("北京银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("渤海银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("沧州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("常熟市农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("成都农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("成都商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("成都银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);
        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("承德银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("城市商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("创兴银行有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("村镇银行有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("大华银行（中国）");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("大连银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("丹东银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("德州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("东莞银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("东亚银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("东营银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("鄂尔多斯银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("福建海峡银行股份有限公司  ");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("福建省农村信用社联合社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("抚州农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("富滇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("甘肃银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("高州市农村信用合作联社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("工商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("光大银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广东澄海潮商村镇银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广东高州农村商业银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广东华兴银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广东南海农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广东南粤银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广东省农村信用社联合社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广东阳东农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广发银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广发银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广西北部湾银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广西农村信用社联合社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广西农信社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广州农村商业银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("广州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("贵阳银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("贵州省农村信用社联合社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("贵州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("贵州遵义汇川农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("桂林银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("哈尔滨市商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("哈尔滨银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("邯郸银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("韩亚银行（中国）");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("汉口银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("杭州联合银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("杭州商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("杭州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("合肥科技农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("河北银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("河北银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("恒丰银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("恒生银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("衡水银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("红星农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("湖北农信");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("湖北三峡农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("湖北银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("湖南汝城农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("湖南三湘银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("花旗银行(中国)有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("华侨永亨银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("华融湘江");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("华润银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("华商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("华夏银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("华夏银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("华一银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("徽商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("汇丰银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("吉林银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("集友银行有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("济宁银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("嘉兴银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("建设银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("江苏江南农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("江苏银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("江苏长江商业银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("江苏紫金农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("江西赣州银座村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("江西农信");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("江西银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("交通银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("焦作中旅银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("锦州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("晋城银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("晋商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("晋中市商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("九江银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("跨境平台");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("昆仑银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("昆仑银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("昆山农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("昆山农信社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("莱商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("兰州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("兰州银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("廊坊银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("乐山市商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("乐山银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("连云港东方农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("辽阳银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("聊城农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("临商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("柳州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("龙江银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("泸州商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("洛阳银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("绵阳商行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("民生银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("南充农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("南京银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("南洋商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("内蒙古银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("内蒙农信");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("宁波银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("宁夏银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农村村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农村信用社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信安徽");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信福建");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信河南");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信湖南");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信辽宁");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信内蒙");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信山东");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信山西");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信银");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农信浙江");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("农业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("盘锦银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("平安银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("平顶山银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("浦北国民村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("浦东发展银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("浦发银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("齐鲁银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("齐商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("企业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("钦南国民村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("钦州市钦南国民村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("青岛即墨惠民村镇银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("青岛胶南海汇村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("青岛农商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("青岛银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("泉州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("日本三井住友银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("日本三菱信用卡公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("日照银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("厦门国际银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("厦门农信");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("厦门银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("山东农信社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("陕西省农村信用社联合社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("上海农商行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("上海银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("上饶市商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("深圳农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("盛京银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("石河子国民村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("石家庄汇融农村合作银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("顺德农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("四川省农村信用社联合社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("四川天府银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("苏州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("苏州银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("遂宁银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("台州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("泰安商行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("泰安市商业银行新区支行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("泰隆城市信用社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("天津滨海农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("天津农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("天津市商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("天津银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("通联跨境");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("威海银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("潍坊农村商业银行东明支行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("潍坊银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("温州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("乌海银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("乌鲁木齐市商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("乌鲁木齐银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("无锡农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("武汉农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("武汉农商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("武汉众邦银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("西安银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("新韩银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("新疆石河子交银村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("星展银行(中国)有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("邢台银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("兴业柳银村镇银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("兴业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("烟台银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("尧都农信社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("营口银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("永亨银行(中国)有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("永隆银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("邮储银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("友利银行(中国)有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("枣庄银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("渣打银行中国有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("张家港农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("张家口银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("长安银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("长沙农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("长沙银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("长治银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("招商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("浙江民泰商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("浙江民泰银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("浙江泰隆商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("浙商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("郑州市市郊农村信用合作联社");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("郑州银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("郑州银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国工商银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国工商银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国建设银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国民生银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国民生银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国农业发展银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国农业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国农业银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国邮储银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中国邮政储蓄银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中信银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("中原银行股份有限公司");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("重庆农村商业银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("重庆农商行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("重庆三峡银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);

        allInPayBankDO = new AllInPayBankDO();
        allInPayBankDO.setName("驻马店银行");
        allInPayBankDO.setCreateTime(System.currentTimeMillis());
        allInPayBankDO.setUpdateTime(System.currentTimeMillis());
        payBanks.add(allInPayBankDO);
        allInPayBankRepository.saveAll(payBanks);

    }

    /**
     * 查询表行数
     *
     * @param dbTableName 数据库表名称
     * @return 表行数
     */
    private long countTable(String dbTableName) {
        String sql = "select count(id) as total from " + dbTableName + ";";
        SqlRowSet sqlRowSet = jdbcTemplate.queryForRowSet(sql);
        long total = 0;
        while (sqlRowSet.next()) {
            total = sqlRowSet.getLong("total");
        }
        return total;
    }
}
