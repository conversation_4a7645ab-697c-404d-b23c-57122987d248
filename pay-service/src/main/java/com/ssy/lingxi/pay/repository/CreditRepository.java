package com.ssy.lingxi.pay.repository;

import com.ssy.lingxi.pay.entity.do_.CreditDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 会员授信
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/8/12 19:19
 */
@Repository
public interface CreditRepository extends JpaRepository<CreditDO, Long>, JpaSpecificationExecutor<CreditDO> {
    /**
     * 查询会员授信信息
     * <AUTHOR>
     * @since 2020/11/6
     * @param memberId 会员id
     * @param roleId  会员角色id
     * @param parentMemberId   父级会员id
     * @param parentRoleId 父级会员角色id
     * @return com.ssy.lingxi.pay.entity.do_.CreditDO
     **/
    CreditDO findFirstByMemberIdAndMemberRoleIdAndParentMemberIdAndParentMemberRoleId(Long memberId, Long roleId, Long parentMemberId, Long parentRoleId);

    /**
     * 根据父级会员id、父级会员角色id查询所有授信
     * <AUTHOR>
     * @since 2020/8/20
     * @param parentMemberId:
     * @param parentMemberRoleId:
     * @return java.util.List<com.ssy.lingxi.pay.entity.do_.CreditDO>
     **/
    List<CreditDO> findByParentMemberIdAndParentMemberRoleId(Long parentMemberId, Long parentMemberRoleId);

    /**
     * 根据账单日期查询非指定状态下的授信列表
     * <AUTHOR>
     * @since 2020/8/21
     * @param billDay:
     * @return java.util.List<com.ssy.lingxi.pay.entity.do_.CreditDO>
     **/
    List<CreditDO> findByBillDayAndStatusIsNot(Integer billDay, Integer status);

    /**
     * 根据授信id查询授信列表
     * <AUTHOR>
     * @since 2020/8/28
     * @param idList:
     * @return java.util.List<com.ssy.lingxi.pay.entity.do_.CreditDO>
     **/
    List<CreditDO> findByIdIn(List<Long> idList);

    /**
     * 根据可用状态与授信id查询授信列表
     * <AUTHOR>
     * @since 2021/3/2
     * @param isUsable: 可用状态
     * @param idList: 授信id
     * @return 授信列表
     **/
    List<CreditDO> findByIsUsableAndIdIn(Integer isUsable, List<Long> idList);

    /**
     * 查询所有可用余额
     * <AUTHOR>
     * @since 2021/1/30
     * @param memberId: 会员id
     * @param roleId: 角色id
     * @return 所有可用余额
     **/
    @Query(value = "select coalesce(sum(quota-use_quota),0) from pay_credit where member_id=:memberId and member_role_id=:roleId", nativeQuery = true)
    BigDecimal sumCanUseQuota(Long memberId, Long roleId);

    /**
     * 查询已申请过授信的数量
     * <AUTHOR>
     * @since 2021/3/9
     * @param memberId: 会员id
     * @param roleId: 角色id
     * @return 已申请数量
     **/
    @Query(value = "select count(id) from pay_credit where member_id=:memberId and member_role_id=:roleId and is_has_apply=1", nativeQuery = true)
    Long existApplyCredit(Long memberId, Long roleId);
}
