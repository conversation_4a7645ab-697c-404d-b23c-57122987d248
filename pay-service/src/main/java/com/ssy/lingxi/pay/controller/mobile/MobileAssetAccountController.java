package com.ssy.lingxi.pay.controller.mobile;

import cn.hutool.core.bean.BeanUtil;
import com.ssy.lingxi.common.constant.Constant;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.CommonIdReq;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.PageDataResp;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.component.base.controller.BaseController;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.component.base.util.BusinessAssertUtil;
import com.ssy.lingxi.component.base.util.WrapperUtil;
import com.ssy.lingxi.member.api.feign.IMemberFeign;
import com.ssy.lingxi.member.api.model.resp.MemberBrandInfoResp;
import com.ssy.lingxi.member.api.model.resp.MemberFeignLogoResp;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayParameterFeignReq;
import com.ssy.lingxi.order.api.model.resp.PaymentParameterFeignDetailResp;
import com.ssy.lingxi.pay.api.model.req.FrozenCreditReq;
import com.ssy.lingxi.pay.api.model.req.assetAccount.*;
import com.ssy.lingxi.pay.api.model.resp.assetAccount.*;
import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountStatusRecordDO;
import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountTradeRecordDO;
import com.ssy.lingxi.pay.entity.do_.assetAccount.MemberAssetAccountDO;
import com.ssy.lingxi.pay.model.req.BalanceReq;
import com.ssy.lingxi.pay.model.req.FrozenAccountBalanceReq;
import com.ssy.lingxi.pay.model.req.MemberAssetAccountReq;
import com.ssy.lingxi.pay.service.assetAccount.IAccountStatusService;
import com.ssy.lingxi.pay.service.assetAccount.IAccountTradeService;
import com.ssy.lingxi.pay.service.assetAccount.IMemberAssetAccountService;
import org.modelmapper.TypeToken;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * APP-资金账户管理
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/12/28
 */
@RestController
@RequestMapping(ServiceModuleConstant.PAY_PATH_PREFIX + "/mobile/assetAccount")
public class MobileAssetAccountController extends BaseController {

    @Resource
    private IMemberAssetAccountService memberAssetAccountService;

    @Resource
    private IAccountTradeService accountTradeService;

    @Resource
    private IAccountStatusService accountStatusService;

    @Resource
    private IOrderProcessFeign orderFeignService;

    @Resource
    private IMemberFeign memberInnerControllerFeign;

    /**
     * 查询资金账户列表(卡包)
     * @param pageDataReq 分页实体
     * @param assetAccountQueryReq 参数
     */
    @GetMapping("/getAssetAccountList")
    public WrapperResp<PageDataResp<MemberAssetAccountResp>> getAssetAccountList(PageDataReq pageDataReq, AssetAccountQueryReq assetAccountQueryReq) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Long memberId = sysUser.getMemberId();
        Long memberRoleId = sysUser.getMemberRoleId();
        Integer memberLevelType = sysUser.getMemberLevelType();
        MemberAssetAccountReq memberAssetAccountMiddle = this.modelMapper.map(assetAccountQueryReq, MemberAssetAccountReq.class);
        memberAssetAccountMiddle.setMemberId(memberId);
        memberAssetAccountMiddle.setMemberRoleId(memberRoleId);

        //只查询商家会员账户
        List<Integer> memberLevelTypeList = new ArrayList<>();
        memberLevelTypeList.add(memberLevelType);
        memberAssetAccountMiddle.setMemberLevelTypeList(memberLevelTypeList);

        Page<MemberAssetAccountDO> result = memberAssetAccountService.getMemberAssetAccountList(pageDataReq, memberAssetAccountMiddle);
        List<MemberAssetAccountDO> memberAssetAccountDOList = result.getContent();
        if(memberAssetAccountDOList.size() > 0){
            HashMap<Long, String> map = new HashMap<>();
            //查询会员头像
            List<Long> memberIdList = memberAssetAccountDOList.stream().map(MemberAssetAccountDO::getMemberId).collect(Collectors.toList());
            WrapperResp<List<MemberFeignLogoResp>> memberLogos = memberInnerControllerFeign.getMemberLogos(memberIdList);
            if(memberLogos != null && memberLogos.getCode() == ResponseCodeEnum.SUCCESS.getCode()){
                List<MemberFeignLogoResp> data = memberLogos.getData();
                data.forEach(memberFeignLogoVO -> map.put(memberFeignLogoVO.getMemberId(), memberFeignLogoVO.getLogo()));
            }
            //遍历设置logo
            List<MemberAssetAccountResp> resultList = memberAssetAccountDOList.stream().map(memberAssetAccount -> {
                MemberAssetAccountResp memberAssetAccountResp = BeanUtil.copyProperties(memberAssetAccount, MemberAssetAccountResp.class);
                memberAssetAccountResp.setMemberLogo(map.get(memberAssetAccount.getId()));
                return memberAssetAccountResp;
            }).collect(Collectors.toList());
            return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
        }else{
            return WrapperUtil.success(new PageDataResp<>(0L, new ArrayList<>()));
        }
    }

    /**
     * 账户详情
     * @param id 资金账户id
     */
    @GetMapping("/getAssetAccount")
    public WrapperResp<MemberAssetAccountResp> getAssetAccount(@RequestParam Long id) {
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountService.getMemberAssetAccount(id);
        if(memberAssetAccountDO != null){
            return WrapperUtil.success(this.modelMapper.map(memberAssetAccountDO, MemberAssetAccountResp.class));
        }else{
            return WrapperUtil.success(null);
        }
    }

    /**
     * 查询平台账户详情
     */
    @GetMapping("/getPlatFormAssetAccount")
    public WrapperResp<MemberAssetAccountResp> getPlatFormAssetAccount() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        MemberAssetAccountDO memberAssetAccountDO = memberAssetAccountService.getPlatFormAssetAccount(sysUser);
        if(memberAssetAccountDO != null){
            return WrapperUtil.success(this.modelMapper.map(memberAssetAccountDO, MemberAssetAccountResp.class));
        }else{
            return WrapperUtil.success(null);
        }
    }

    /**
     * 查询会员用户的账户详情
     * @param parentMemberId 上级会员id
     * @param parentMemberRoleId 上级会员角色id
     */
    @GetMapping("/getUserAssetAccount")
    public WrapperResp<MemberAssetAccountResp> getUserAssetAccount(@RequestParam Long parentMemberId, @RequestParam Long parentMemberRoleId) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Long memberId = sysUser.getMemberId();
        Long memberRoleId = sysUser.getMemberRoleId();
        return WrapperUtil.success(memberAssetAccountService.getUserAssetAccount(memberId, memberRoleId, parentMemberId, parentMemberRoleId));
    }

    /**
     * 交易记录
     * @param tradeRecordReq 参数
     */
    @PostMapping("/getAccountTradeRecord")
    public WrapperResp<PageDataResp<AccountTradeRecordResp>> getAccountTradeRecord(@RequestBody TradeRecordReq tradeRecordReq) {
        Long memberAssetAccountId = tradeRecordReq.getMemberAssetAccountId();
        List<Integer> operationList = tradeRecordReq.getOperationList();
        Long startTime = tradeRecordReq.getStartTime();
        Long endTime = tradeRecordReq.getEndTime();

        PageDataReq pageDataReq = new PageDataReq();
        pageDataReq.setCurrent(tradeRecordReq.getCurrent());
        pageDataReq.setPageSize(tradeRecordReq.getPageSize());
        Page<AccountTradeRecordDO> result = accountTradeService.getAccountTradeRecordList(pageDataReq, memberAssetAccountId, operationList, startTime, endTime);
        List<AccountTradeRecordResp> resultList = this.modelMapper.map(result.getContent(), new TypeToken<List<AccountTradeRecordResp>>(){}.getType());
        return WrapperUtil.success(new PageDataResp<>(result.getTotalElements(), resultList));
    }

    /**
     * 状态记录
     * @param memberAssetAccountId 会员资金账户id
     */
    @GetMapping("/getAccountStatusRecord")
    public WrapperResp<List<AccountStatusRecordDO>> getAccountStatusRecord(@RequestParam("memberAssetAccountId") Long memberAssetAccountId) {
        return WrapperUtil.success(accountStatusService.getAccountStatusRecord(memberAssetAccountId));
    }

    /**
     * 账户充值(app)
     * @param rechargeReq 参数
     */
    @PostMapping("/recharge")
    public WrapperResp<AccountRechargeAppResp> recharge(@RequestBody @Valid RechargeReq rechargeReq, HttpServletRequest request) {
        return WrapperUtil.success(memberAssetAccountService.rechargeAssetAccountApp(request, rechargeReq));
    }

    /**
     * 账户充值(小程序)
     * @param rechargeAppletReq 参数
     */
    @PostMapping("/rechargeApplet")
    public WrapperResp<AccountRechargeAppletResp> rechargeApplet(@RequestBody @Valid RechargeAppletReq rechargeAppletReq, HttpServletRequest request) {
        return WrapperUtil.success(memberAssetAccountService.rechargeAssetAccountApplet(request, rechargeAppletReq));
    }

    /**
     * 账户充值(H5)
     * @param rechargeJsApiReq 参数
     */
    @PostMapping("/rechargeJsApi")
    public WrapperResp<AccountRechargeJsApiResp> rechargeJsApi(@RequestBody @Valid RechargeJsApiReq rechargeJsApiReq, HttpServletRequest request) {
        return WrapperUtil.success(memberAssetAccountService.rechargeAssetAccountJsApi(getSysUser(),request, rechargeJsApiReq));
    }

    /**
     * 查询账户充值结果
     * @param tradeRecordId 交易记录id
     */
    @GetMapping("/getRechargeResult")
    public WrapperResp<Boolean> getRechargeResult(@RequestParam("tradeRecordId") Long tradeRecordId) {
        return WrapperUtil.success(memberAssetAccountService.getRechargeResult(tradeRecordId));
    }

    /**
     * 申请提现
     * @param cashOutReq 参数
     */
    @PostMapping("/cashOut")
    public WrapperResp<Void> cashOut(@RequestBody @Valid CashOutReq cashOutReq) {
        memberAssetAccountService.cashOutAssetAccount(cashOutReq);
        return WrapperUtil.success();
    }

    /**
     * 余额支付
     * @param balancePayReq 参数
     */
    @PostMapping("/balancePay")
    public WrapperResp<String> balancePay(@RequestBody BalancePayReq balancePayReq) {
        return WrapperUtil.success(memberAssetAccountService.balancePay(balancePayReq));
    }

    /**
     * 查询会员账户余额
     * @param queryVO 接口参数
     * @return 查询结果
     */
    @GetMapping("/balance")
    public WrapperResp<BigDecimal> getAccountBalance(@Valid BalanceReq queryVO) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Long memberId = sysUser.getMemberId();
        Long memberRoleId = sysUser.getMemberRoleId();
        return WrapperUtil.success(memberAssetAccountService.getUserBalance(queryVO.getFundMode(), memberId, memberRoleId, queryVO.getVendorMemberId(), queryVO.getVendorRoleId()));
    }

    /**
     * 查询登录用户的账户余额
     * @param parentMemberId 上级会员id
     * @param parentMemberRoleId 上级会员角色id
     */
    @GetMapping("/getUserBalance")
    public WrapperResp<BigDecimal> getUserBalance(@RequestParam Long parentMemberId, @RequestParam Long parentMemberRoleId) {
        //查询当前用户的资金归集模式: 1-平台，2-会员
        //从会员服务查询资金归集模式
        OrderPayParameterFeignReq feignVO = new OrderPayParameterFeignReq();
        feignVO.setMemberId(parentMemberId);
        feignVO.setRoleId(parentMemberRoleId);
        feignVO.setPayChannel(OrderPayChannelEnum.ACCOUNT_BALANCE);
        WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPaymentParameters(feignVO);
        if(parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            return WrapperUtil.fail(parameterResult.getCode(), parameterResult.getMessage());
        }

        UserLoginCacheDTO sysUser = this.getSysUser();
        Long memberId = sysUser.getMemberId();
        Long memberRoleId = sysUser.getMemberRoleId();
        return WrapperUtil.success(memberAssetAccountService.getUserBalance(parameterResult.getData().getFundMode().getCode(), memberId, memberRoleId, parentMemberId, parentMemberRoleId));
    }

    /**
     * 查询登录用户的所有账户余额
     */
    @GetMapping("/getUserAllBalance")
    public WrapperResp<BigDecimal> getUserAllBalance() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        Long memberId = sysUser.getMemberId();
        Long memberRoleId = sysUser.getMemberRoleId();
        return WrapperUtil.success(memberAssetAccountService.getUserAllBalance(memberId, memberRoleId));
    }

    /**
     * 查询登录用户的所有账户余额
     */
    @GetMapping("/getAccountBalance")
    public WrapperResp<MemberAssetAccountResp> getAccountBalance(@RequestHeader HttpHeaders headers) {
        UserLoginCacheDTO sysUser = this.getSysUser();
        String branchId = headers.getFirst(Constant.BRANCH_ID);
        if(StringUtils.isEmpty(branchId)){
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_NOT_EXISTS);
        }
        sysUser.setBranchId(Long.valueOf(branchId));
        return WrapperUtil.success(memberAssetAccountService.getAccountBalance(sysUser));
    }


    /**
     * 查询Eos账号余额
     */
    @GetMapping("/getEosAccountBalance")
    public WrapperResp<MemberAssetAccountResp> getEosAccountBalance() {
        UserLoginCacheDTO sysUser = this.getSysUser();
        CommonIdReq commonIdReq = new CommonIdReq();
        commonIdReq.setId(sysUser.getBranchId());
        WrapperResp<MemberBrandInfoResp> memberBranchInfoWrapper = memberInnerControllerFeign.findMemberBrandById(commonIdReq);
        MemberBrandInfoResp memberBrandInfoResp = WrapperUtil.getDataOrThrow(memberBranchInfoWrapper);
        BusinessAssertUtil.notNull(memberBrandInfoResp, ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_NOT_EXISTS);
        if(memberBrandInfoResp.getMemberId() == null){
            throw new BusinessException(ResponseCodeEnum.MC_MS_MEMBER_RUN_BRAND_CODE_NOT_BINDING_TO_MEMBER);
        }
        return WrapperUtil.success(memberAssetAccountService.getEosAccountBalance(memberBrandInfoResp.getMemberId()));
    }

}
