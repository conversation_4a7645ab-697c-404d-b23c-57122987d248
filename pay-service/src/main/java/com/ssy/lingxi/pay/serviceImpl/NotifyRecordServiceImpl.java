package com.ssy.lingxi.pay.serviceImpl;

import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyResult;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.pay.entity.do_.AliPayNotifyRecordDO;
import com.ssy.lingxi.pay.entity.do_.WeChatNotifyRecordDO;
import com.ssy.lingxi.pay.repository.AliPayNotifyRecordRepository;
import com.ssy.lingxi.pay.repository.WeChatNotifyRecordRepository;
import com.ssy.lingxi.pay.service.INotifyRecordService;
import com.ssy.lingxi.pay.service.IPayCacheService;
import jodd.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;


@Service
public class NotifyRecordServiceImpl implements INotifyRecordService {
    @Autowired
    private WeChatNotifyRecordRepository weChatNotifyRecordRepository;
    @Autowired
    private AliPayNotifyRecordRepository aliPayNotifyRecordRepository;
    @Autowired
    private IPayCacheService payCacheService;

    /**
     * 记录微信回调日志
     *
     * @param wxPayOrderNotifyResult 回调信息
     * @return 解析后的回调信息
     */
    @Override
    public WeChatNotifyRecordDO saveWeChatNotifyRecord(WxPayOrderNotifyResult wxPayOrderNotifyResult) {
        WeChatNotifyRecordDO weChatNotify = new WeChatNotifyRecordDO();
        BeanUtils.copyProperties(wxPayOrderNotifyResult, weChatNotify);
        return weChatNotifyRecordRepository.saveAndFlush(weChatNotify);
    }

    /**
     * 记录支付宝回调日志
     *
     * @param params 回调信息
     */
    @Override
    public AliPayNotifyRecordDO saveAliPayNotifyRecord(Map<String, String> params) {
        AliPayNotifyRecordDO aliPayNotifyRecordDO = new AliPayNotifyRecordDO();
        aliPayNotifyRecordDO.setAppId(params.get("app_id"));
        aliPayNotifyRecordDO.setBuyerId(params.get("buyer_id"));
        aliPayNotifyRecordDO.setBuyerOpenId(params.get("buyer_open_id"));
        aliPayNotifyRecordDO.setAuthAppId(params.get("auth_app_id"));
        String buyerPayAmount = params.get("buyer_pay_amount");
        if (StringUtil.isNotEmpty(buyerPayAmount)) {
            aliPayNotifyRecordDO.setBuyerPayAmount(new BigDecimal(buyerPayAmount).multiply(BigDecimal.valueOf(100)).intValue());
        }
        String fundBullList = params.get("fund_bull_list");
        if (StringUtil.isNotEmpty(fundBullList)) {
            aliPayNotifyRecordDO.setFundBillList(SerializeUtil.serialize(fundBullList));
        }
        String invoiceAmount = params.get("invoice_amount");
        if (StringUtil.isNotEmpty(invoiceAmount)) {
            aliPayNotifyRecordDO.setInvoiceAmount(new BigDecimal(invoiceAmount).multiply(BigDecimal.valueOf(100)).intValue());
        }
        aliPayNotifyRecordDO.setNotifyId(params.get("notify_id"));
        aliPayNotifyRecordDO.setNotifyTime(params.get("notify_time"));
        aliPayNotifyRecordDO.setNotifyType(params.get("notify_type"));
        aliPayNotifyRecordDO.setOutTradeNo(params.get("out_trade_no"));
        String passbackParams = params.get("passback_params");
        if (StringUtil.isNotEmpty(passbackParams)) {
            aliPayNotifyRecordDO.setPassbackParams(SerializeUtil.serialize(passbackParams));
        }
        String body = params.get("body");
        if (StringUtil.isNotEmpty(body)) {
            aliPayNotifyRecordDO.setBody(SerializeUtil.serialize(body));
        }
        String pointAmount = params.get("point_amount");
        if (StringUtil.isNotEmpty(pointAmount)) {
            aliPayNotifyRecordDO.setPointAmount(new BigDecimal(pointAmount).multiply(BigDecimal.valueOf(100)).intValue());
        }
        String receiptAmount = params.get("receipt_amount");
        if (StringUtil.isNotEmpty(receiptAmount)) {
            aliPayNotifyRecordDO.setPointAmount(new BigDecimal(receiptAmount).multiply(BigDecimal.valueOf(100)).intValue());
        }
        aliPayNotifyRecordDO.setSellerId(params.get("seller_id"));
        aliPayNotifyRecordDO.setSubject(params.get("subject"));
        aliPayNotifyRecordDO.setTradeNo(params.get("trade_no"));
        aliPayNotifyRecordDO.setTradeStatus(params.get("trade_status"));
        String voucherDetailList = params.get("voucher_detail_list");
        if (StringUtil.isNotEmpty(voucherDetailList)) {
            aliPayNotifyRecordDO.setVoucherDetailList(SerializeUtil.serialize(voucherDetailList));
        }
        String sign = params.get("sign");
        if (StringUtil.isNotEmpty(sign)) {
            aliPayNotifyRecordDO.setSign(SerializeUtil.serialize(sign));
        }
        aliPayNotifyRecordDO.setGmtCreate(params.get("gmt_create"));
        aliPayNotifyRecordDO.setGmtPayment(params.get("gmt_payment"));
        return aliPayNotifyRecordRepository.saveAndFlush(aliPayNotifyRecordDO);
    }
}
