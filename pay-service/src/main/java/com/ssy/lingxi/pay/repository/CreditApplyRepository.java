package com.ssy.lingxi.pay.repository;

import com.ssy.lingxi.pay.entity.do_.CreditApplyDO;
import com.ssy.lingxi.pay.model.dto.SuperiorInnerStatusDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 授信申请
 * <AUTHOR>
 * @since 2020/8/15 10:04
 * @version 2.0.0
 */
@Repository
public interface CreditApplyRepository extends JpaRepository<CreditApplyDO, Long>, JpaSpecificationExecutor<CreditApplyDO> {

    /**
     * 根据授信id查与删除状态询授信申请，按申请时间倒序排序
     * <AUTHOR>
     * @since 2020/8/19
     **/
    List<CreditApplyDO> findByCreditIdAndIsDeleteOrderByApplyTimeDesc(Long creditId, Integer isDelete);

    /**
     * 根据授信id、删除状态、申请类型查询授信申请，按申请时间倒序排序
     * <AUTHOR>
     * @since 2020/8/29
     **/
    List<CreditApplyDO> findByCreditIdAndIsDeleteAndApplyTypeOrderByApplyTimeDesc(Long creditId, Integer isDelete, Integer applyType);


    /**
     * 获取当前授信申请
     * <AUTHOR>
     * @since 2020/8/19
     **/
    CreditApplyDO findByIsCurrentAndCreditIdAndIsDelete(Integer isCurrent, Long creditId, Integer isDelete);

    @Query("select new com.ssy.lingxi.pay.model.dto.SuperiorInnerStatusDTO(cr.superiorInnerStatus as key , count(*) as num ) from #{#entityName} cr where cr.isDelete = 0 and cr.outerStatus = 2 and  cr.isCurrent = 1 and cr.parentMemberId = ?1 and cr.parentMemberRoleId = ?2 group by cr.superiorInnerStatus")
    List<SuperiorInnerStatusDTO> countGroupByParentStatus(Long parentMemberId, Long parentMemberRoleId) ;
}
