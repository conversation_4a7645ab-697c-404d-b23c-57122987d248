package com.ssy.lingxi.pay.repository;

import com.ssy.lingxi.pay.entity.do_.CreditBillDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 授信账单
 * <AUTHOR>
 * @since 2020/8/21
 * @version 2.0.0
 */
@Repository
public interface CreditBillRepository extends JpaRepository<CreditBillDO, Long>, JpaSpecificationExecutor<CreditBillDO> {

    /**
     * 根据账单所属时间与账单日期查询所有账单
     * <AUTHOR>
     * @since 2020/8/21
     * @param belongTime:
     * @param billDay:
     * @return java.util.List<com.ssy.lingxi.pay.entity.do_.CreditBillDO>
     **/
    List<CreditBillDO> findByBillBelongTimeAndBillDay(Long belongTime, Integer billDay);

    /**
     * 根据账单所属时间、账单日期、授信查询账单
     * <AUTHOR>
     * @since 2020/8/28
     * @param belongTime:
     * @param billDay:
     * @param creditId:
     * @return com.ssy.lingxi.pay.entity.do_.CreditBillDO
     **/
    CreditBillDO findByBillBelongTimeAndBillDayAndCreditId(Long belongTime, Integer billDay, Long creditId);

    /***
     * 根据账单所属时间查询所有账单
     * <AUTHOR>
     * @since 2020/8/24
     * @param belongTime:
     * @return java.util.List<com.ssy.lingxi.pay.entity.do_.CreditBillDO>
     **/
    List<CreditBillDO> findByBillBelongTime(Long belongTime);

    /**
     * 根据授信id获取授信账单下拉框，并按账单时间倒序排序
     * <AUTHOR>
     * @since 2020/8/21
     * @param creditId:
     * @return java.util.List<com.ssy.lingxi.pay.entity.do_.CreditBillDO>
     **/
    List<CreditBillDO> findByCreditIdOrderByBillBelongTimeDesc(long creditId);

    /**
     * 根据账单所属时间与授信id查询账单
     * <AUTHOR>
     * @since 2020/8/25
     * @param belongTime:
     * @param creditId:
     * @return com.ssy.lingxi.pay.entity.do_.CreditBillDO
     **/
    CreditBillDO findByBillBelongTimeAndCreditId(Long belongTime, Long creditId);

    /**
     * 获取到期账单
     * <AUTHOR>
     * @since 2021/1/22
     * @param expireTime: 到期时间
     * @return 账单列表
     **/
    List<CreditBillDO> findAllByExpireTime(Long expireTime);
}
