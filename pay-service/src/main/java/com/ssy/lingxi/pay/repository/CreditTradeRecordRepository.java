package com.ssy.lingxi.pay.repository;

import com.ssy.lingxi.pay.entity.do_.CreditTradeRecordDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

/**
 * 授信交易记录
 * <AUTHOR>
 * @since 2020/8/21
 * @version 2.0.0
 */
@Repository
public interface CreditTradeRecordRepository extends JpaRepository<CreditTradeRecordDO, Long>, JpaSpecificationExecutor<CreditTradeRecordDO> {

    /**
     * 根据账单id、交易项目、订单编码检查是否存在交易记录
     * <AUTHOR>
     * @since 2020/8/31
     * @param billId:
     * @param operation:
     * @param orderCode:
     * @return java.lang.Boolean
     **/
    Boolean existsByBillIdAndOperationAndOrderCode(Long billId, Integer operation, String orderCode);

    /**
     * 根据交易流水号查询交易记录
     * @param tradeCode
     * @return
     */
    CreditTradeRecordDO findByTradeCode(String tradeCode);

    /**
     * 根据账单id、交易项目、订单编码统计交易金额
     * @param billId
     * @param operation
     * @param orderCode
     * @return
     */
    @Query("select max(c.tradeMoney) from CreditTradeRecordDO c where c.billId = ?1 and c.operation = ?2 and c.orderCode = ?3")
    BigDecimal sumTradeMoney(Long billId, Integer operation, String orderCode);
}
