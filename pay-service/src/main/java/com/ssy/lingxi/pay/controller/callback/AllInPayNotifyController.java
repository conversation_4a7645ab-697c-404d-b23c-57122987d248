package com.ssy.lingxi.pay.controller.callback;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.ssy.lingxi.common.constant.ServiceModuleConstant;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.common.util.SerializeUtil;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayCallbackFeignReq;
import com.ssy.lingxi.pay.api.enums.ServiceTypeEnum;
import com.ssy.lingxi.pay.constant.AllInPayNotifyConstant;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayAttachDO;
import com.ssy.lingxi.pay.enums.allInPay.AllInPayResultEnum;
import com.ssy.lingxi.pay.model.dto.AllInPayAttachDTO;
import com.ssy.lingxi.pay.model.req.allInPay.AllInPayNotifyCommonReq;
import com.ssy.lingxi.pay.model.req.allInPay.bizContent.GetHisOrderDetailBizContentReq;
import com.ssy.lingxi.pay.model.req.allInPay.bizContent.RegisterCompanyMemberResultBizContentReq;
import com.ssy.lingxi.pay.model.req.allInPay.bizContent.SignAcctProtocolBizContentReq;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayAttachRepository;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayRepository;
import com.ssy.lingxi.pay.service.ICreditService;
import com.ssy.lingxi.pay.service.IPayCacheService;
import com.ssy.lingxi.pay.service.assetAccount.IMemberAssetAccountService;
import com.ssy.lingxi.pay.service.callback.IAllInPayNotifyService;
import com.ssy.lingxi.pay.service.eAccount.IEAccountTradeService;
import com.ssy.lingxi.settlement.api.feign.IMemberSettlementFeign;
import com.ssy.lingxi.settlement.api.model.req.CallbackCommunicationPayReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

/**
 * 通联支付回调接口
 *
 * <AUTHOR>
 * @version 2.0.0
 * @ignore 不需要提交到Yapi
 * @since 2021/11/29
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(AllInPayNotifyController.PATH_PREFIX)
public class AllInPayNotifyController {

    /**
     * 路径前缀
     */
    public static final String PATH_PREFIX = ServiceModuleConstant.PAY_PATH_PREFIX + "/allInPay/notify";

    private final AllInPayRepository allInPayRepository;
    private final AllInPayAttachRepository allInPayAttachRepository;

    private final ICreditService creditService;
    private final IPayCacheService payCacheService;
    private final IEAccountTradeService eAccountTradeService;
    private final IMemberAssetAccountService memberAssetAccountService;
    private final IAllInPayNotifyService allInPayNotifyService;

    private final IOrderProcessFeign orderProcessFeign;
    private final IMemberSettlementFeign settlementControllerFeign;

    /**
     * 会员签约结果回调地址
     */
    @PostMapping(AllInPayNotifyConstant.SIGN_CONTRACT)
    public void signContract(AllInPayNotifyCommonReq commonReq) {
        String reqJsonStr = JsonUtil.toJson(commonReq);
        log.info("会员电子协议签约返回结果->{}", reqJsonStr);
        SignAcctProtocolBizContentReq bizContentReq = JsonUtil.toObj(commonReq.getBizContent(), SignAcctProtocolBizContentReq.class);
        allInPayNotifyService.signContract(commonReq, bizContentReq);
    }


    /**
     * 影印件核对结果异步通知地址
     *
     * @param param 操作结果
     */
    @PostMapping(AllInPayNotifyConstant.ID_CARD_COLLECT)
    public void idCardCollect(@RequestParam Map<String, Object> param) {
        log.info("影印件核对结果异步通知返回结果->{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            JSONObject resp = JSONObject.parseObject(rps);
            String status = resp.getString("status");
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(status)) {
                String bizUserId = resp.getString("bizUserId");
                Long ocrRegNumComparisonResult = resp.getLong("ocrRegnumComparisonResult");//OCR 识别与企业工商认证信息 是否一致 0-否1-是该字段与“OCR 识别与企业法 人实名信息是否一致”字段有 一方发生变更即返值 若营业执照未进行识别该字段不返
                Long ocrIdCardComparisonResult = resp.getLong("ocrIdcardComparisonResult");//OCR 识别与企业法人实名信息 是否一致 0-否1-是该字段与“OCR 识别与企业工 商认证信息是否一致”字段有 一方发生变更即返值 若法人身份证未进行识别该字段不返
                AllInPayAttachDO allInPayAttach = allInPayAttachRepository.findByBizUserId(bizUserId);
                allInPayAttach = allInPayAttach != null ? allInPayAttach : new AllInPayAttachDO();
                allInPayAttach.setOcrRegNumComparisonResult(ocrRegNumComparisonResult.intValue());
                allInPayAttach.setOcrIdCardComparisonResult(ocrIdCardComparisonResult.intValue());
                allInPayAttachRepository.saveAndFlush(allInPayAttach);
            }
        }
    }

    /**
     * 通联支付-设置支付密码【密码验证版】回调信息
     */
    @PostMapping(AllInPayNotifyConstant.SET_PAY_PWD)
    public void setPayPwd(@RequestParam Map<String, Object> param) {
        log.info("联支付-设置支付密码【密码验证版】回调信息返回结果->{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            JSONObject resp = JSONObject.parseObject(rps);
            String status = resp.getString("status");
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(status)) {
                AllInPayAttachDO payAttach = allInPayAttachRepository.findByBizUserId(resp.getString("bizUserId"));
                payAttach.setIsSetPayPwd(Boolean.TRUE);
                allInPayAttachRepository.saveAndFlush(payAttach);
            }
        }
    }

    /**
     * 通联支付-修改支付密码【密码验证版】回调信息
     */
    @PostMapping(AllInPayNotifyConstant.UPDATE_PAY_PWD)
    public void updatePayPwd(@RequestParam Map<String, Object> param) {
        log.info("联支付-修改支付密码【密码验证版】回调信息返回结果->{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            JSONObject resp = JSONObject.parseObject(rps);
            JSONObject returnValue = resp.getJSONObject("returnValue");
            //TODO:待添加修改成功标识
        }
    }

    /**
     * 通联支付-重置支付密码【密码验证版】回调信息
     */
    @PostMapping(AllInPayNotifyConstant.RESET_PAY_PWD)
    public void resetPayPwd(@RequestParam Map<String, Object> param) {
        log.info("联支付-重置支付密码【密码验证版】回调信息返回结果->{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            log.info("------------通联支付回调通知:rps--------------:{}", rps);
            JSONObject resp = JSONObject.parseObject(rps);
            JSONObject returnValue = resp.getJSONObject("returnValue");
            //TODO:待重置修改成功标识
        }
    }

    /**
     * 支付通知接口
     * 1. 首先记录支付宝流水
     * 2. 进行业务操作
     */
    @PostMapping(AllInPayNotifyConstant.CONSUME_APPLY)
    public void notify(@RequestParam Map<String, Object> param) {
        log.info("------------通联支付回调通知--------------:{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            JSONObject returnValue = JSONObject.parseObject(rps);
            String status = returnValue.getString("status");
            String buyerBizUserId = returnValue.getString("buyerBizUserId");
            String amount = returnValue.getString("amount");
            String trade_no = returnValue.getString("orderNo");
            String payDatetime = returnValue.getString("payDatetime");
            String out_trade_no = returnValue.getString("bizOrderNo");


            String attachStr = returnValue.getString("extendInfo");
            AllInPayAttachDTO attachInfo = SerializeUtil.deserialize(attachStr, AllInPayAttachDTO.class);
            //解析附加信息
            String serviceType = attachInfo.getServiceType();       //服务类型p
            String attach = attachInfo.getAttach();                 //附加信息

            if (AllInPayResultEnum.SUCCESS.getMsg().equals(status)) {
                //回调业务方法
                if (ServiceTypeEnum.Pay_Credit.getCode().equals(serviceType)) {
                    creditService.confirmRepaySucceed(out_trade_no, trade_no);
                    log.info(ServiceTypeEnum.Pay_Credit.getMessage() + ":==========订单号：" + out_trade_no + "通知成功");
                } else if (ServiceTypeEnum.Order_Pay.getCode().equals(serviceType)) {
                    OrderPayCallbackFeignReq orderPayCallbackFeignReq = new OrderPayCallbackFeignReq();
                    orderPayCallbackFeignReq.setTradeNo(out_trade_no);
                    orderPayCallbackFeignReq.setChannelTradeNo(trade_no);
                    orderPayCallbackFeignReq.setAttach(attach);
                    orderPayCallbackFeignReq.setPaySuccess(true);
                    orderPayCallbackFeignReq.setBuyerBizUserId(buyerBizUserId);
                    WrapperResp<Void> orderCallbackResult = orderProcessFeign.orderPayCallback(orderPayCallbackFeignReq);
                    log.info(ServiceTypeEnum.Order_Pay.getMessage() + ":==========订单号：" + out_trade_no + " attach：" + attach + " , 支付成功通知结果" + orderCallbackResult.getMessage());
                } else if (ServiceTypeEnum.SETTLEMENT_Pay.getCode().equals(serviceType)) {
                    CallbackCommunicationPayReq request = new CallbackCommunicationPayReq();
                    request.setTradeNo(out_trade_no);
                    request.setToken(attach);
                    log.info("调用前" + ServiceTypeEnum.Order_Pay.getMessage() + ":" + JSONUtil.toJsonStr(request));
                    WrapperResp<Boolean> orderCallbackResult = settlementControllerFeign.confirmCommunicationPay(request);
                    log.info(ServiceTypeEnum.Order_Pay.getMessage() + ":==========订单号：" + out_trade_no + " attach：" + attach + " , 支付成功通知结果" + orderCallbackResult.getMessage());

                } else {
                    log.error("==========订单号：" + out_trade_no + "通知失败，未找到对应的服务");
                }
            } else {
                if (ServiceTypeEnum.Order_Pay.getCode().equals(serviceType)) {
                    OrderPayCallbackFeignReq orderPayCallbackFeignReq = new OrderPayCallbackFeignReq();
                    orderPayCallbackFeignReq.setTradeNo(out_trade_no);
                    orderPayCallbackFeignReq.setChannelTradeNo(trade_no);
                    orderPayCallbackFeignReq.setAttach(attach);
                    orderPayCallbackFeignReq.setPaySuccess(false);
                    orderPayCallbackFeignReq.setBuyerBizUserId(buyerBizUserId);
                    WrapperResp<Void> orderCallbackResult = orderProcessFeign.orderPayCallback(orderPayCallbackFeignReq);
                    log.info(ServiceTypeEnum.Order_Pay.getMessage() + ":==========订单号：" + out_trade_no + " attach：" + attach + " , 支付失败通知结果" + orderCallbackResult.getMessage());
                }
            }
        }
    }


    /**
     * 代收支付通知接口
     * 1. 首先记录支付宝流水
     * 2. 进行业务操作
     */
    @PostMapping(AllInPayNotifyConstant.AGENT_COLLECT_APPLY)
    public void agentCollectApply(@RequestParam Map<String, Object> param) {
        log.info("------------通联支付回调通知--------------:{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            log.info("------------通联支付回调通知:rps--------------:{}", rps);
            JSONObject resp = JSONObject.parseObject(rps);
            String status = resp.getString("status");
            // 云商通订单号
            String trade_no = resp.getString("orderNo");
            String payDatetime = resp.getString("payDatetime");
            // 商户订单号（支付订单）
            String out_trade_no = resp.getString("bizOrderNo");
            // 通道交易流水号(支付渠道的交易流水号，微信订单详情“商户单号”，支付宝订单详情“商家订单号  走收银宝渠道-对应收银宝接口指定trxid)
            String payInterfaceOutTradeNo = resp.getString("payInterfaceOutTradeNo");
            // 支付渠道交易单号(如支付宝“订单号”，微信“交易单号”，在用户提供微信及支付宝侧支付信息时，商户可通过此单号快速定位到商户业务系统订单。)
            String chnltrxid = resp.getString("chnltrxid");
            String attachStr = resp.getString("extendInfo");
            String buyerBizUserId = resp.getString("buyerBizUserId");
            AllInPayAttachDTO allInPayAttachDTO = payCacheService.deserializeObject(attachStr, AllInPayAttachDTO.class);
            //解析附加信息
            String serviceType = allInPayAttachDTO.getServiceType();       //服务类型p
            String attach = allInPayAttachDTO.getAttach();                 //附加信息

            //通联支付交易记录会在通联平台自己新增
            //判断是否支付成功(待测试)
            if (AllInPayResultEnum.SUCCESS.getMsg().equals(status)) {

                //回调业务方法
                if (ServiceTypeEnum.Pay_Recharge.getCode().equals(serviceType)) {
                    memberAssetAccountService.payNotify(out_trade_no, trade_no);
                    log.info(ServiceTypeEnum.Pay_Recharge.getMessage() + ":==========订单号：" + out_trade_no + "通知成功");
                } else if (ServiceTypeEnum.Pay_Credit.getCode().equals(serviceType)) {
                    creditService.confirmRepaySucceed(out_trade_no, trade_no);
                    log.info(ServiceTypeEnum.Pay_Credit.getMessage() + ":==========订单号：" + out_trade_no + "通知成功");
                } else if (ServiceTypeEnum.Order_Pay.getCode().equals(serviceType)) {
                    OrderPayCallbackFeignReq orderPayCallbackFeignReq = new OrderPayCallbackFeignReq();
                    orderPayCallbackFeignReq.setTradeNo(out_trade_no);
                    orderPayCallbackFeignReq.setChannelTradeNo(chnltrxid);
                    orderPayCallbackFeignReq.setAttach(attach);
                    orderPayCallbackFeignReq.setPaySuccess(true);
                    orderPayCallbackFeignReq.setBuyerBizUserId(buyerBizUserId);
                    WrapperResp<Void> orderCallbackResult = orderProcessFeign.orderPayCallback(orderPayCallbackFeignReq);
                    log.info(ServiceTypeEnum.Order_Pay.getMessage() + ":==========订单号：" + out_trade_no + " attach：" + attach + " , 支付成功通知结果" + orderCallbackResult.getMessage());
                } else {
                    log.error("==========订单号：" + out_trade_no + "通知失败，未找到对应的服务");
                }
            } else {
                if (ServiceTypeEnum.Order_Pay.getCode().equals(serviceType)) {
                    OrderPayCallbackFeignReq orderPayCallbackFeignReq = new OrderPayCallbackFeignReq();
                    orderPayCallbackFeignReq.setTradeNo(out_trade_no);
                    orderPayCallbackFeignReq.setChannelTradeNo(trade_no);
                    orderPayCallbackFeignReq.setAttach(attach);
                    orderPayCallbackFeignReq.setPaySuccess(false);
                    orderPayCallbackFeignReq.setBuyerBizUserId(buyerBizUserId);
                    WrapperResp<Void> orderCallbackResult = orderProcessFeign.orderPayCallback(orderPayCallbackFeignReq);
                    log.info(ServiceTypeEnum.Order_Pay.getMessage() + ":==========订单号：" + out_trade_no + " attach：" + attach + " , 支付失败通知结果" + orderCallbackResult.getMessage());
                }
            }
        }
    }

    /**
     * 通联支付-单笔托管代付（标准版）(付款到账户余额)回调信息
     */
    @PostMapping(AllInPayNotifyConstant.SIGNAL_AGENT_PAY)
    public void signalAgentPay(@RequestParam Map<String, Object> param) {
        log.info("通联支付-单笔托管代付（标准版）(付款到账户余额)回调信息返回结果->{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            JSONObject resp = JSONObject.parseObject(rps);
            String status = resp.getString("status");
            String out_trade_no = resp.getString("bizOrderNo");
        }
    }

    /**
     * 通联支付-退款申请
     */
    @PostMapping(AllInPayNotifyConstant.REFUND)
    public void refund(@RequestParam Map<String, Object> param) {
        log.info("通联支付-退款申请回调信息返回结果->{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            log.info("------------通联支付回调通知:rps--------------:{}", rps);
            JSONObject resp = JSONObject.parseObject(rps);
            String status = resp.getString("status");
        }
    }

    /**
     * 通联支付-充值申请
     */
    @PostMapping(AllInPayNotifyConstant.DEPOSIT_APPLY)
    public void depositApply(@RequestParam Map<String, Object> param) {
        log.info("通联支付-充值申请回调信息返回结果->{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            log.info("------------通联支付回调通知:rps--------------:{}", rps);
            JSONObject resp = JSONObject.parseObject(rps);
            String status = resp.getString("status");
            String payInterfaceOutTradeNo = resp.getString("payInterfaceOutTradeNo");//
            String buyerBizUserId = resp.getString("buyerBizUserId");//购买者
            Long amount = resp.getLong("amount");//充值金额,单位 分
            Long channelFee = resp.getLong("channelFee");//手续费,单位 分
            String channelPaytime = resp.getString("channelPaytime");
            String payDatetime = resp.getString("payDatetime");//支付时间
            String payInterfacetrxcode = resp.getString("payInterfacetrxcode");
            String orderNo = resp.getString("orderNo");//支付单号
            String cusId = resp.getString("cusid");
            String bizOrderNo = resp.getString("bizOrderNo");//这个是我们充值时候传过去的订单号
            String acct = resp.getString("acct");

            if (AllInPayResultEnum.SUCCESS.getMsg().equals(status)) {
                log.info("------------通联支付回调通知:更新交易记录bizOrderNo--------------:{}", bizOrderNo);
                log.info("------------通联支付回调通知:更新交易记录orderNo--------------:{}", orderNo);
                eAccountTradeService.rechargeNotify(bizOrderNo, orderNo);
            }
        }
    }

    /**
     * 通联支付-提现申请
     */
    @PostMapping(AllInPayNotifyConstant.WITHDRAW_APPLY)
    public void withdrawApply(@RequestParam Map<String, Object> param) {
        log.info("通联支付-提现申请回调信息返回结果->{}", param);
        if (!Objects.isNull(param)) {
            String rps = param.get("bizContent").toString();
            log.info("------------提现申请回调通知:rps--------------:{}", rps);
            JSONObject returnValue = JSONObject.parseObject(rps);
            String status = returnValue.getString("status");
            String buyerBizUserId = returnValue.getString("buyerBizUserId");//提现者
            String amount = returnValue.getString("amount");//提现金额 单位:分
            String orderNo = returnValue.getString("orderNo");
            String extendInfo = returnValue.getString("extendInfo");
            String payDatetime = returnValue.getString("status");
            String acct = returnValue.getString("acct");
            String bizOrderNo = returnValue.getString("bizOrderNo");

            if (AllInPayResultEnum.SUCCESS.getMsg().equals(status)) {
                log.info("------------提现申请回调信息返回结果:更新交易记录bizOrderNo--------------:{}", bizOrderNo);
                log.info("------------提现申请回调信息返回结果:更新交易记录orderNo--------------:{}", orderNo);
                eAccountTradeService.cashOutNotify(bizOrderNo, orderNo);
            }
        }
    }

    /**
     * 通联支付-企业会员开户H5
     */
    @PostMapping(AllInPayNotifyConstant.REGISTER_COMPANY_MEMBER)
    public void registerCompanyMember(AllInPayNotifyCommonReq commonReq) {
        String reqJsonStr = JsonUtil.toJson(commonReq);
        log.info("通联支付-企业会员开户H5 回调信息返回结果->{}", reqJsonStr);
        RegisterCompanyMemberResultBizContentReq bizContentReq = JsonUtil.toObj(commonReq.getBizContent(), RegisterCompanyMemberResultBizContentReq.class);
        allInPayNotifyService.registerCompanyMember(commonReq, bizContentReq);
    }

    /**
     * 通联支付-历史订单详细信息查询
     */
    @PostMapping(AllInPayNotifyConstant.GET_HIS_ORDER_DETAIL)
    public void getHisOrderDetail(AllInPayNotifyCommonReq commonReq) {
        String reqJsonStr = JsonUtil.toJson(commonReq);
        log.info("通联支付-历史订单详细信息查询 回调信息返回结果->{}", reqJsonStr);
        GetHisOrderDetailBizContentReq bizContentReq = JsonUtil.toObj(commonReq.getBizContent(), GetHisOrderDetailBizContentReq.class);
        allInPayNotifyService.getHisOrderDetail(commonReq, bizContentReq);
    }

}
