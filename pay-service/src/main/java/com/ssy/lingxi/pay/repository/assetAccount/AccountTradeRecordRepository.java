package com.ssy.lingxi.pay.repository.assetAccount;

import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountTradeRecordDO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 会员资金账户交易记录
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
@Repository
public interface AccountTradeRecordRepository extends JpaRepository<AccountTradeRecordDO,Long>, JpaSpecificationExecutor<AccountTradeRecordDO> {
    Page<AccountTradeRecordDO> findByMemberAssetAccountIdOrderByTradeTimeDesc(Long memberAssetAccountId, Pageable page);

    AccountTradeRecordDO findFirstByTradeCode(String tradeCode);

    List<AccountTradeRecordDO> findByPayPlatformTradeCodeAndOperationAndStatus(String platformTradeCode, Integer operation, Integer status);

    List<AccountTradeRecordDO> findByTradeCodeAndPayPlatformTradeCodeAndOperationAndStatus(String tradeCode, String platformTradeCode, Integer operation, Integer status);

    Page<AccountTradeRecordDO> findByMemberAssetAccountIdAndOperationInAndTradeTimeGreaterThanEqualAndTradeTimeLessThanEqualOrderByTradeTimeDesc(Long memberAssetAccountId, List<Integer> operationList, Long startTime, Long endTime, Pageable page);

    List<AccountTradeRecordDO> findByPayPlatformTradeCodeInAndOperationAndStatus(Set<String> platformTradeCode, Integer operation, Integer status);
}
