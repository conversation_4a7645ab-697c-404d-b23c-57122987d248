package com.ssy.lingxi.pay.serviceImpl.assetAccount;

import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.pay.entity.do_.assetAccount.AccountCashOutRecordDO;
import com.ssy.lingxi.pay.repository.assetAccount.AccountCashOutRecordRepository;
import com.ssy.lingxi.pay.service.assetAccount.IAccountCashOutRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * 会员资金账户
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
@Service
public class AccountCashOutRecordServiceImpl implements IAccountCashOutRecordService {

    @Autowired
    private AccountCashOutRecordRepository accountCashOutRecordRepository;

    /**
     * 查询提现处理记录列表
     * @param tradeCode
     */
    @Override
    public Page<AccountCashOutRecordDO> getCheckCashOutList(PageDataReq pageDataReq, String tradeCode) {
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize());
        return accountCashOutRecordRepository.findByTradeCode(tradeCode, page);
    }
}

