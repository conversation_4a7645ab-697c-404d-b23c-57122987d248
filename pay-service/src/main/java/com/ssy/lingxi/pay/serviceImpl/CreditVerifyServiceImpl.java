package com.ssy.lingxi.pay.serviceImpl;

import cn.hutool.core.date.DateUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.pay.constant.PayConstant;
import com.ssy.lingxi.pay.entity.do_.CreditApplyDO;
import com.ssy.lingxi.pay.entity.do_.CreditVerifyDO;
import com.ssy.lingxi.pay.enums.CreditApplyOuterStatusEnum;
import com.ssy.lingxi.pay.enums.CreditApplySuperiorInnerStatusEnum;
import com.ssy.lingxi.pay.model.req.CreditVerifyUpdateReq;
import com.ssy.lingxi.pay.model.resp.CreditVerifyResp;
import com.ssy.lingxi.pay.repository.CreditApplyRepository;
import com.ssy.lingxi.pay.repository.CreditVerifyRepository;
import com.ssy.lingxi.pay.service.ICreditVerifyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 授信审核接口实现
 * <AUTHOR>
 * @since 2020/8/18
 * @version 2.0.0
 */
@Service
public class CreditVerifyServiceImpl implements ICreditVerifyService {


    @Resource
    private CreditVerifyRepository verifyRepository;

    @Resource
    private CreditApplyRepository applyRepository;

    /**
     * 获取授信申请审批信息
     * <AUTHOR>
     * @since 2020/8/20
     * @param applyId:
     * @return com.ssy.lingxi.common.response.Wrapper<com.ssy.lingxi.pay.model.resp.CreditVerifyVO>
     **/
    @Override
    public CreditVerifyResp getVerify(Long applyId) {

        CreditVerifyDO verifyDO = verifyRepository.findByApplyId(applyId);

        CreditVerifyResp result = null;
        if (verifyDO != null) {
            result = new CreditVerifyResp();
            result.setBillDay(verifyDO.getBillDay());
            result.setQuota(verifyDO.getQuota());
            result.setRepayPeriod(verifyDO.getRepayPeriod());
            result.setVerifyTime(verifyDO.getVerifyTime() > 0 ? DateUtil.format(DateUtil.date(verifyDO.getVerifyTime()), "yyyy-MM-dd HH:mm") : "");
        }

        return result;
    }

    /**
     * 更新授信审批信息
     * <AUTHOR>
     * @since 2020/8/18
     * @param user :
     * @param updateVO:
     **/
    @Transactional
    @Override
    public Long updateVerify(UserLoginCacheDTO user, CreditVerifyUpdateReq updateVO) {

        // step 1: 参数验证
        if (updateVO.getBillDay() > PayConstant.PAY_CREDIT_MAX_BILL_DAY) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_BILL_DAY_ERROR);
        }
        CreditApplyDO applyDO = applyRepository.findById(updateVO.getApplyId()).orElse(null);
        if (applyDO == null) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_APPLY_NOT_EXIST);
        }
        // 非待提交状态不允许修改数据
        if (!CreditApplyOuterStatusEnum.WAIT_CONFIRM.getCode().equals(applyDO.getOuterStatus())
                || !CreditApplySuperiorInnerStatusEnum.WAIT_SUBMIT.getCode().equals(applyDO.getSuperiorInnerStatus())) {
            throw new BusinessException(ResponseCodeEnum.PAY_CREDIT_VERIFY_NOT_UPDATE);
        }

        // 查询审批信息，没有则新增
        CreditVerifyDO verifyDO = verifyRepository.findByApplyId(updateVO.getApplyId());
        if (verifyDO == null) {
            verifyDO = new CreditVerifyDO();
            verifyDO.setApplyId(updateVO.getApplyId());
        }
        verifyDO.setQuota(updateVO.getQuota());
        verifyDO.setBillDay(updateVO.getBillDay());
        verifyDO.setRepayPeriod(updateVO.getRepayPeriod());
        verifyDO.setVerifyTime(0L);
        verifyRepository.saveAndFlush(verifyDO);

        // 如果审批的额度与申请额度不相同，则更新申请中的审批额度
        if (!updateVO.getQuota().equals(applyDO.getAuditQuota())) {
            applyDO.setAuditQuota(updateVO.getQuota());
            applyRepository.saveAndFlush(applyDO);
        }

        return verifyDO.getId();
    }
}
