package com.ssy.lingxi.pay.repository.allInPay;

import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayBankDO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version 2.0.0
 * @since 2021/11/26
 */
@Repository
public interface AllInPayBankRepository extends JpaRepository<AllInPayBankDO, Long>, JpaSpecificationExecutor<AllInPayBankDO> {
}
