package com.ssy.lingxi.pay.serviceImpl.eAccount;

import cn.hutool.json.JSONUtil;
import com.ssy.lingxi.common.model.dto.UserLoginCacheDTO;
import com.ssy.lingxi.common.model.req.PageDataReq;
import com.ssy.lingxi.common.model.resp.WrapperResp;
import com.ssy.lingxi.common.util.JsonUtil;
import com.ssy.lingxi.component.base.enums.ResponseCodeEnum;
import com.ssy.lingxi.component.base.enums.member.MemberStatusEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPayChannelEnum;
import com.ssy.lingxi.component.base.enums.order.OrderPaymentParameterEnum;
import com.ssy.lingxi.component.base.model.BusinessException;
import com.ssy.lingxi.order.api.feign.IOrderProcessFeign;
import com.ssy.lingxi.order.api.model.req.OrderPayChannelFeignReq;
import com.ssy.lingxi.order.api.model.resp.PayChannelParameterFeignDetailResp;
import com.ssy.lingxi.order.api.model.resp.PaymentParameterFeignDetailResp;
import com.ssy.lingxi.pay.api.model.req.eAccount.EAccountQueryReq;
import com.ssy.lingxi.pay.api.model.req.wechatPay.AppletOpenidReq;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayAttachDO;
import com.ssy.lingxi.pay.entity.do_.allInPay.AllInPayDO;
import com.ssy.lingxi.pay.model.req.AccessTokenReq;
import com.ssy.lingxi.pay.model.resp.wechatPay.WxAppletCode2SessionResp;
import com.ssy.lingxi.pay.model.resp.wechatPay.openApi.WxAppletCode2sessionResp;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayAttachRepository;
import com.ssy.lingxi.pay.repository.allInPay.AllInPayRepository;
import com.ssy.lingxi.pay.service.IWeChatPayService;
import com.ssy.lingxi.pay.service.eAccount.IEAccountService;
import com.ssy.lingxi.pay.util.WeChatPayUtil;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 会员资金账户
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2020/7/21
 */
@Service
public class EAccountServiceImpl implements IEAccountService {

    @Resource
    private AllInPayRepository allInPayRepository;

    @Resource
    private AllInPayAttachRepository allInPayAttachRepository;

    @Resource
    private IOrderProcessFeign orderFeignService;

    @Resource
    @Lazy
    private IWeChatPayService weChatPayService;

    /**
     * 查询e账户主表
     */
    @Override
    public AllInPayDO getAccountDetail(UserLoginCacheDTO sysUser) {
        return allInPayRepository.findFirstByMemberIdAndMemberRoleId(sysUser.getMemberId(), sysUser.getMemberRoleId());
    }

    /**
     * 查询e账户附属表
     */
    @Override
    public AllInPayAttachDO getAccountAttachDetail(String bizUserId) {
        return allInPayAttachRepository.findFirstByBizUserId(bizUserId);
    }

    /**
     * 查询会员资金账户
     */
    @Override
    public AllInPayDO getAccountDetail(Long id) {
        return allInPayRepository.findById(id).orElse(null);
    }

    /**
     * 查询e账户
     *
     * @param pageDataReq      分页实体
     * @param eAccountQueryReq 查询参数
     */
    @Override
    public Page<AllInPayDO> getPlatFormEAccountList(PageDataReq pageDataReq, EAccountQueryReq eAccountQueryReq) {
        Pageable page = PageRequest.of(pageDataReq.getCurrent() - 1, pageDataReq.getPageSize());
        return allInPayRepository.findAll(getSpecification(eAccountQueryReq), page);
    }

    /**
     * 冻结/解冻e账户的会员状态
     *
     * @param memberId     会员id
     * @param memberRoleId 会员角色id
     * @param memberStatus 会员状态
     */
    @Override
    public void updateMemberAssetAccount(Long memberId, Long memberRoleId, Integer memberStatus) {
        AllInPayDO allInPayDO = allInPayRepository.findFirstByMemberIdAndMemberRoleId(memberId, memberRoleId);
        if (allInPayDO != null) {
            allInPayDO.setMemberStatus(memberStatus);
            allInPayRepository.saveAndFlush(allInPayDO);
        }
    }

    /**
     * 构建查询条件
     *
     * @param eAccountQueryReq 接口参数
     * @return 查询条件
     */
    private Specification<AllInPayDO> getSpecification(EAccountQueryReq eAccountQueryReq) {
        return (root, query, criteriaBuilder) -> {
            Predicate finalConditions = criteriaBuilder.conjunction();

            String memberName = eAccountQueryReq.getMemberName();
            Long memberRoleId = eAccountQueryReq.getMemberRoleId();
            Integer memberType = eAccountQueryReq.getMemberType();
            Integer memberStatus = eAccountQueryReq.getMemberStatus();
            Integer accountStatus = eAccountQueryReq.getAccountStatus();

            //会员名称
            if (StringUtils.hasLength(memberName)) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.like(root.get(AllInPayDO.Fields.memberName), "%" + memberName + "%"));
            }
            //会员角色id
            if (memberRoleId != null && memberRoleId > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(AllInPayDO.Fields.memberRoleId), memberRoleId));
            }
            //会员类型
            if (memberType != null && memberType > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(AllInPayDO.Fields.memberType), memberType));
            }
            //会员状态
            if (memberStatus != null && memberStatus > 0) {
                if (MemberStatusEnum.NORMAL.getCode().equals(memberStatus)) {
                    finalConditions = criteriaBuilder.and(finalConditions, root.get(AllInPayDO.Fields.memberStatus).isNull());
                }
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(AllInPayDO.Fields.memberStatus), memberStatus));
            }
            //账号状态
            if (accountStatus != null && accountStatus > 0) {
                finalConditions = criteriaBuilder.and(finalConditions, criteriaBuilder.equal(root.get(AllInPayDO.Fields.accountStatus), accountStatus));
            }
            return finalConditions;
        };
    }

    /**
     * 获取微信参数(通联)
     *
     * @param jsCode 微信小程序code
     */
    @Override
    public Map<String, String> getAllInWechatParam(String jsCode) {
        //通过小程序获取openId
        String merchantId = "";
        String appId = "";
        String appKey = "";
        //从订单服务查询支付参数
        OrderPayChannelFeignReq orderPayChannelFeignReq = new OrderPayChannelFeignReq();
        orderPayChannelFeignReq.setPayChannel(OrderPayChannelEnum.ALLIN_WECHAT);
        WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPlatformPaymentParameters(orderPayChannelFeignReq);
        if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(parameterResult.getCode(), parameterResult.getMessage());
        }
        if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
            throw new BusinessException(ResponseCodeEnum.ALLINPAY_WECHAT_APPLET_NOT_EXIST);
        }
        List<PayChannelParameterFeignDetailResp> parameters = parameterResult.getData().getParameters();
        for (PayChannelParameterFeignDetailResp p : parameters) {
            if (p.getParameter().equals(OrderPaymentParameterEnum.ALLIN_WECHAT_APPLET_APP_ID)) {
                appId = p.getValue();
            } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALLIN_WECHAT_APPLET_APP_KEY)) {
                appKey = p.getValue();
            } else if (p.getParameter().equals(OrderPaymentParameterEnum.ALLIN_VSP_CUS_ID)) {
                merchantId = p.getValue();
            }
        }
        //获取openId
        String param = "appid=" + appId + "&secret=" + appKey + "&js_code=" + jsCode + "&grant_type=" + WeChatPayUtil.GRANT_TYPE;
        String resultStr = WeChatPayUtil.httpsRequest(WeChatPayUtil.LOGIN_URL, "POST", param);
        Map<String, String> resultMap = (Map<String, String>) JSONUtil.toBean(resultStr, Map.class);
        String openId = resultMap.get("openid");
        if (org.apache.commons.lang3.StringUtils.isEmpty(openId)) {
            throw new BusinessException(ResponseCodeEnum.PAY_E_ACCOUNT_OPENID_EXCEPTION);
        }
        //定义返回map
        Map<String, String> map = new HashMap<>();
        map.put("openId", openId);
        map.put("merchantId", merchantId);
        map.put("subAppId", appId);

        return map;
    }

    @Override
    public WxAppletCode2SessionResp wxAppletCode2Session(AppletOpenidReq req) {

        //从订单服务查询支付参数
        AccessTokenReq accessTokenReq = weChatPayService.getAppletPayParamFromPlatform(req.getPayChannel());

        // 小程序登录, 获取openId
        String param = "appid=" + accessTokenReq.getAppId() + "&secret=" + accessTokenReq.getAppSecret() + "&js_code=" + req.getJsCode() + "&grant_type=" + WeChatPayUtil.GRANT_TYPE;
        String resultStr = WeChatPayUtil.httpsRequest(WeChatPayUtil.LOGIN_URL, "POST", param);
        WxAppletCode2sessionResp resp = JsonUtil.toObj(resultStr, WxAppletCode2sessionResp.class);
        if (Objects.isNull(resp)) {
            throw new BusinessException(ResponseCodeEnum.PAY_WECHAT_APPLET_LOGIN_CODE2SESSION_FAIL);
        }

        return new WxAppletCode2SessionResp(resp.getOpenid(), accessTokenReq.getAppId());
    }

    @Override
    public Map<String, String> getMerchantId() {
        //通过小程序获取openId
        String merchantId = "";
        //从订单服务查询支付参数
        OrderPayChannelFeignReq orderPayChannelFeignVO = new OrderPayChannelFeignReq();
        orderPayChannelFeignVO.setPayChannel(OrderPayChannelEnum.ALLIN_WECHAT);
        WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPlatformPaymentParameters(orderPayChannelFeignVO);
        if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(parameterResult.getCode(), parameterResult.getMessage());
        }
        if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
            throw new BusinessException(ResponseCodeEnum.ALLINPAY_WECHAT_APPLET_NOT_EXIST);
        }
        List<PayChannelParameterFeignDetailResp> parameters = parameterResult.getData().getParameters();
        for (PayChannelParameterFeignDetailResp p : parameters) {
            if (p.getParameter().equals(OrderPaymentParameterEnum.ALLIN_VSP_CUS_ID)) {
                merchantId = p.getValue();
            }
        }
        //定义返回map
        Map<String, String> map = new HashMap<>();
        map.put("merchantId", merchantId);
        return map;
    }

    /**
     * 获取微信subAppId
     *
     * @return subAppId
     */
    @Override
    public String getWechatSubAppId() {
        String appId = "";
        //从订单服务查询支付参数
        OrderPayChannelFeignReq orderPayChannelFeignReq = new OrderPayChannelFeignReq();
        orderPayChannelFeignReq.setPayChannel(OrderPayChannelEnum.ALLIN_WECHAT);
        WrapperResp<PaymentParameterFeignDetailResp> parameterResult = orderFeignService.findPlatformPaymentParameters(orderPayChannelFeignReq);
        if (parameterResult.getCode() != ResponseCodeEnum.SUCCESS.getCode()) {
            throw new BusinessException(parameterResult.getCode(), parameterResult.getMessage());
        }
        if (CollectionUtils.isEmpty(parameterResult.getData().getParameters())) {
            throw new BusinessException(ResponseCodeEnum.ALLINPAY_WECHAT_APPLET_NOT_EXIST);
        }
        List<PayChannelParameterFeignDetailResp> parameters = parameterResult.getData().getParameters();
        for (PayChannelParameterFeignDetailResp p : parameters) {
            if (p.getParameter().equals(OrderPaymentParameterEnum.ALLIN_WECHAT_APPLET_APP_ID)) {
                appId = p.getValue();
            }
        }
        return appId;
    }

    @Override
    public List<AllInPayAttachDO> getEAccountAttachList(List<String> bizUserIdList) {
        return allInPayAttachRepository.findByBizUserIdIn(bizUserIdList);
    }
}

