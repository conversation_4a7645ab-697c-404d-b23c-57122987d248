AllInPayEnterpriseStateEnum.1=Pending review
AllInPayEnterpriseStateEnum.2=Approval successful
AllInPayEnterpriseStateEnum.3=Audit failed

AllInPayErrorCodeEnum.10000=Parameter error
AllInPayErrorCodeEnum.10001=Parameter cannot be empty
AllInPayErrorCodeEnum.10002=Parameter service error
AllInPayErrorCodeEnum.10003=Parameter method error
AllInPayErrorCodeEnum.10004=Parameter length too long
AllInPayErrorCodeEnum.10005=Concurrency error
AllInPayErrorCodeEnum.20000=App does not exist
AllInPayErrorCodeEnum.20001=The application does not enable B2C mode
AllInPayErrorCodeEnum.20002=The app has not enabled cross-app transfer
AllInPayErrorCodeEnum.20003=App does not have permission to use this interface
AllInPayErrorCodeEnum.20004=The user does not have a password to confirm payment
AllInPayErrorCodeEnum.20005= The user has not activated the three-element binding card
AllInPayErrorCodeEnum.20006=Decoding error
AllInPayErrorCodeEnum.20007=Exceeded the number of personal card binding settings
AllInPayErrorCodeEnum.20008=Little B's permission to bind personal card has not been enabled or set
AllInPayErrorCodeEnum.20009=Channel merchant information error
AllInPayErrorCodeEnum.20010=Password error
AllInPayErrorCodeEnum.20011=Verification code error
AllInPayErrorCodeEnum.20012=Unsupported document type
AllInPayErrorCodeEnum.20013=Unsupported request source (eg mobile phone, PC)
AllInPayErrorCodeEnum.20014=Bank code error
AllInPayErrorCodeEnum.20015=Error setting expiration time
AllInPayErrorCodeEnum.20016=Encryption failed
AllInPayErrorCodeEnum.20017=Decryption failed
AllInPayErrorCodeEnum.20018=Signature failed
AllInPayErrorCodeEnum.20019=Signature verification failed
AllInPayErrorCodeEnum.20020=The bank card does not support cash withdrawal or payment/decoding error
AllInPayErrorCodeEnum.20021=Cache operation error
AllInPayErrorCodeEnum.20022=Duplicate request pipeline
AllInPayErrorCodeEnum.20023=Merchant system user ID error
AllInPayErrorCodeEnum.20024=Yunshangtong bank code is not configured
AllInPayErrorCodeEnum.20025=The payee clearing line number is not configured
AllInPayErrorCodeEnum.20026=Please call [Photocopy collection and set enterprise information (H5)] to supplement the photocopy information
AllInPayErrorCodeEnum.20027=The withdrawal amount exceeds the channel limit
AllInPayErrorCodeEnum.30000=User already exists
AllInPayErrorCodeEnum.30001=User does not exist
AllInPayErrorCodeEnum.30002=User has been locked
AllInPayErrorCodeEnum.30003=User has been activated
AllInPayErrorCodeEnum.30004=User is already a developer
AllInPayErrorCodeEnum.30005=User is not a developer
AllInPayErrorCodeEnum.30006=User type is incorrect
AllInPayErrorCodeEnum.30007=User has been authenticated by real name
AllInPayErrorCodeEnum.30008=User has not been authenticated with real name
AllInPayErrorCodeEnum.30009=Verification of real-name information failed
AllInPayErrorCodeEnum.300010=User has set login SMS notification
AllInPayErrorCodeEnum.300011=User has set login email notification
AllInPayErrorCodeEnum.300012=ID verification failed
AllInPayErrorCodeEnum.300013=Template type error
AllInPayErrorCodeEnum.300014=Failed to bind bank card
AllInPayErrorCodeEnum.300015=Failed to unbind bank card
AllInPayErrorCodeEnum.300016=Bank card binding record does not exist
AllInPayErrorCodeEnum.300017=The bank card is bound
AllInPayErrorCodeEnum.300018=Account set error
AllInPayErrorCodeEnum.300019=Account set does not exist
AllInPayErrorCodeEnum.300020=Email already exists
AllInPayErrorCodeEnum.300021=Unbound phone
AllInPayErrorCodeEnum.300022=User unavailable
AllInPayErrorCodeEnum.300023=Corporate membership has been reviewed
AllInPayErrorCodeEnum.300024=Mobile phone bound
AllInPayErrorCodeEnum.300030=Enterprise information not set
AllInPayErrorCodeEnum.300031=Payment password has been set
AllInPayErrorCodeEnum.300032=Payment password not set
AllInPayErrorCodeEnum.300033=Payment password error exceeds limit
AllInPayErrorCodeEnum.300034=The bank card has not completed the contract verification
AllInPayErrorCodeEnum.300035=Real-name authentication has been performed
AllInPayErrorCodeEnum.300036=The member security level is not set
AllInPayErrorCodeEnum.300037=The member's security level is low and the current operation is not supported
AllInPayErrorCodeEnum.300038=The real-name payment record does not exist or has been confirmed
AllInPayErrorCodeEnum.300039=The bank card has not completed the real-name payment verification
AllInPayErrorCodeEnum.300040=Incorrect card type
AllInPayErrorCodeEnum.300041=The mobile phone number is not bound to the bank card
AllInPayErrorCodeEnum.300042=Enterprise authentication failed
AllInPayErrorCodeEnum.300043=The member has signed an electronic agreement
AllInPayErrorCodeEnum.300044=The bank card does not exist
AllInPayErrorCodeEnum.300045=The bank card is not bound
AllInPayErrorCodeEnum.300046=The member did not sign the electronic agreement
AllInPayErrorCodeEnum.300047=This bank card is not currently supported
AllInPayErrorCodeEnum.300048=The member has not opened a wealth management account
AllInPayErrorCodeEnum.300049=Card bin does not exist in card bin table
AllInPayErrorCodeEnum.300050=Enterprise membership not approved
AllInPayErrorCodeEnum.300051=Account set does not support query
AllInPayErrorCodeEnum.300052=The balance transfer agreement has been signed
AllInPayErrorCodeEnum.300053=Balance payment agreement does not exist
AllInPayErrorCodeEnum.300054=Protocol information does not match
AllInPayErrorCodeEnum.300055=The agreement is in canceled state and no need to cancel the contract
AllInPayErrorCodeEnum.300056=Protocol status error
AllInPayErrorCodeEnum.300057=User application mismatch
AllInPayErrorCodeEnum.300058=Payee does not exist
AllInPayErrorCodeEnum.300059=The payee cannot be a platform
AllInPayErrorCodeEnum.300060=The privileged member does not have the right to call
AllInPayErrorCodeEnum.300061=The reserve information is not configured
AllInPayErrorCodeEnum.300062=The depository institution sub-account has not been opened
AllInPayErrorCodeEnum.300063=The member has not opened this account set
AllInPayErrorCodeEnum.300064=Background notification address is not configured
AllInPayErrorCodeEnum.300065=Errors related to reserve fund configuration
AllInPayErrorCodeEnum.300066=The corporate bank card is not allowed to unbind
AllInPayErrorCodeEnum.300067=The new phone number is the same as the original phone number
AllInPayErrorCodeEnum.300068=The certificate type does not match the real-name certificate type
AllInPayErrorCodeEnum.300069=The real-name authentication information is inconsistent with the original real-name authentication information!
AllInPayErrorCodeEnum.300070=The card bound to the cash register is only for ID card type!
AllInPayErrorCodeEnum.300071=Number verification service failed
AllInPayErrorCodeEnum.300072=Incomplete member information
AllInPayErrorCodeEnum.300073=OCR identification route is not configured
AllInPayErrorCodeEnum.300074=The number of members bound to the mobile phone number and ID number is not configured
AllInPayErrorCodeEnum.300075=The number of members bound to the mobile phone number is limited
AllInPayErrorCodeEnum.300076=The number of members bound with the ID number is limited
AllInPayErrorCodeEnum.300077=The business license has been recognized and does not need to be re-uploaded
AllInPayErrorCodeEnum.300078=The legal person ID card has been recognized, no need to re-upload
AllInPayErrorCodeEnum.40000=Order does not exist
AllInPayErrorCodeEnum.40001=Item type mismatch
AllInPayErrorCodeEnum.40002=Order error
AllInPayErrorCodeEnum.40003=Order has expired
AllInPayErrorCodeEnum.40004=Order is not in unpaid status
AllInPayErrorCodeEnum.40005=The order amount does not match the payment amount
AllInPayErrorCodeEnum.40006=Payment failed
AllInPayErrorCodeEnum.40007=Item already exists
AllInPayErrorCodeEnum.40008=Item type does not exist
AllInPayErrorCodeEnum.40009=No payment channel available
AllInPayErrorCodeEnum.40010=Transaction type does not exist
AllInPayErrorCodeEnum.40011=Insufficient handling fee in reserve account
AllInPayErrorCodeEnum.40012=Credit card not supported
AllInPayErrorCodeEnum.40013=Commodity entry failed
AllInPayErrorCodeEnum.40014=Insufficient account balance
AllInPayErrorCodeEnum.40015=Transaction rule limit
AllInPayErrorCodeEnum.40016=The handling fee is greater than the amount
AllInPayErrorCodeEnum.40017=The order number is duplicated, please change the order number and initiate the transaction
AllInPayErrorCodeEnum.40018=Incorrect order type
AllInPayErrorCodeEnum.40019=Incorrect order status
AllInPayErrorCodeEnum.40020=Incorrect order amount
AllInPayErrorCodeEnum.40021=Strong real-name authentication error
AllInPayErrorCodeEnum.40022=Order and user do not match
AllInPayErrorCodeEnum.40023=The refund type refundType is incorrect
AllInPayErrorCodeEnum.40025=The application's payment channel configuration does not exist
AllInPayErrorCodeEnum.40026=No payment permission
AllInPayErrorCodeEnum.40027=Withdrawal payment method not set
AllInPayErrorCodeEnum.40028=Incorrect transaction verification method
AllInPayErrorCodeEnum.40029=URL format error
AllInPayErrorCodeEnum.40030=Withdrawal payment channel account is not configured
AllInPayErrorCodeEnum.40031=The handling fee is not set
AllInPayErrorCodeEnum.40032=Channel bank not set
AllInPayErrorCodeEnum.40033=Duplicate order confirmation
AllInPayErrorCodeEnum.40034=Payment channel properties do not match
AllInPayErrorCodeEnum.40035=Withdrawal only supports T0T1
AllInPayErrorCodeEnum.40036=Missing payment line number
AllInPayErrorCodeEnum.40037=Payment line number format error
AllInPayErrorCodeEnum.40039=The original order does not support refund, please contact the merchant for processing
AllInPayErrorCodeEnum.40040=Insufficient amount of collection order
AllInPayErrorCodeEnum.40041=The original order has not been paid and cannot initiate a refund
AllInPayErrorCodeEnum.40042=This withdrawal method is not supported
AllInPayErrorCodeEnum.40043=The withdrawal method does not have permission
AllInPayErrorCodeEnum.40044=Incorrect date format
AllInPayErrorCodeEnum.40045=Accumulated number of risk control overruns per day
AllInPayErrorCodeEnum.40046=Risk control limits the amount of a single order
AllInPayErrorCodeEnum.40047=Accumulated amount of risk control exceeding the limit in a single day
AllInPayErrorCodeEnum.40048=Member is not bound to vspCusid
AllInPayErrorCodeEnum.40049=The payment method vspCusid is inconsistent with the vspCusid bound by the receiving member
AllInPayErrorCodeEnum.40050=Item type or item code is inconsistent with payee
AllInPayErrorCodeEnum.40051=The application's payment channel configuration does not exist
AllInPayErrorCodeEnum.40052=The total number of product numbers is inconsistent with the total number of transactions!
AllInPayErrorCodeEnum.40055=The registered member of Tonglian Wallet is abnormal!
AllInPayErrorCodeEnum.40056=There is an error in the splitting data, the maximum splitting level is 3, and the maximum number of splitting members is 10!
AllInPayErrorCodeEnum.40057=Payment channel limit exceeded
AllInPayErrorCodeEnum.40058=Error checking bank position
AllInPayErrorCodeEnum.40059=Order resending SMS failed
AllInPayErrorCodeEnum.40060=Incorrect amount
AllInPayErrorCodeEnum.40061=Amount exceeds allowable range
AllInPayErrorCodeEnum.40064=Non-Depository Bank
AllInPayErrorCodeEnum.40065=Order not allowed to close
AllInPayErrorCodeEnum.50001=Insufficient account balance
AllInPayErrorCodeEnum.50002=Account frozen
AllInPayErrorCodeEnum.50003=Insufficient frozen balance
AllInPayErrorCodeEnum.50004=Frozen record does not exist
AllInPayErrorCodeEnum.50005=The member or account type corresponding to the internal account is inconsistent
AllInPayErrorCodeEnum.50006=Account does not exist
AllInPayErrorCodeEnum.9000=Other errors
AllInPayErrorCodeEnum.9100=Unknown error
AllInPayErrorCodeEnum.9200=Database error
AllInPayErrorCodeEnum.9300=External error
AllInPayErrorCodeEnum.9400=Request timed out
AllInPayErrorCodeEnum.9500=http communication error (error in communication process or return code is not 200)
AllInPayErrorCodeEnum.9999=System error

AllInPayIndustryEnum.1010=Insurance Agent
AllInPayIndustryEnum.1011=Insurance Broker
AllInPayIndustryEnum.1012=Insurance Adjuster
AllInPayIndustryEnum.1013=Property Insurance Company
AllInPayIndustryEnum.1014=Life Insurance Company
AllInPayIndustryEnum.1015=Health Insurance
AllInPayIndustryEnum.1016=Pension insurance
AllInPayIndustryEnum.1110=Micro Loan Company
AllInPayIndustryEnum.1111=P2P platform
AllInPayIndustryEnum.1114=Finance Guarantee
AllInPayIndustryEnum.1115=Financial lease
AllInPayIndustryEnum.1116=Investment and wealth management
AllInPayIndustryEnum.1117=Invoice Transaction
AllInPayIndustryEnum.1118=Crowdfunding transaction
AllInPayIndustryEnum.1119=Shares
AllInPayIndustryEnum.1410=Car Sales
AllInPayIndustryEnum.1411=Auto Finance
AllInPayIndustryEnum.1412=Car Rental
AllInPayIndustryEnum.1413=Automobile after-sales
AllInPayIndustryEnum.1414=Motor Transportation
AllInPayIndustryEnum.1510=School
AllInPayIndustryEnum.1511=Training
AllInPayIndustryEnum.1512=Exam Center
AllInPayIndustryEnum.1514=Media
AllInPayIndustryEnum.1610=Property Sales
AllInPayIndustryEnum.1611=Real estate agency
AllInPayIndustryEnum.1612=Property Management
AllInPayIndustryEnum.1613=Property rental
AllInPayIndustryEnum.1910=Other
AllInPayIndustryEnum.1912=Luxury Jewelry
AllInPayIndustryEnum.1914=Exchange Market
AllInPayIndustryEnum.1915=Utilities
AllInPayIndustryEnum.1916=Convenient Payment
AllInPayIndustryEnum.1917=Bidding
AllInPayIndustryEnum.1918=Group purchase
AllInPayIndustryEnum.1929=Logistics
AllInPayIndustryEnum.1930=Medical
AllInPayIndustryEnum.1940=Business Travel
AllInPayIndustryEnum.1941=Telecom Operators and Air Chargers
AllInPayIndustryEnum.1944=The collection of funds for production or distribution enterprises
AllInPayIndustryEnum.1947=Consultation Management
AllInPayIndustryEnum.1953=Other applications within the bank
AllInPayIndustryEnum.1954=Bank (Finance)
AllInPayIndustryEnum.2310=Care Supplies
AllInPayIndustryEnum.2311=Food & Beverage
AllInPayIndustryEnum.2312=Health Products
AllInPayIndustryEnum.2313=Tobacco and Alcohol
AllInPayIndustryEnum.2314=Feed
AllInPayIndustryEnum.2413=Electronics
AllInPayIndustryEnum.2414=Personal Care Makeup
AllInPayIndustryEnum.2415=Jewelry
AllInPayIndustryEnum.2416=Outdoor Sports
AllInPayIndustryEnum.2417=Household appliances
AllInPayIndustryEnum.2418=Diet and Health
AllInPayIndustryEnum.2419=Education Fees
AllInPayIndustryEnum.2420=Airline ticket
AllInPayIndustryEnum.2421=Hotel Accommodation
AllInPayIndustryEnum.2422=Other
AllInPayIndustryEnum.2510=Clothing shoes and hats
AllInPayIndustryEnum.2511=Luggage accessories
AllInPayIndustryEnum.2512=Household Appliances
AllInPayIndustryEnum.2513=Electronics
AllInPayIndustryEnum.2514=Personal Care Makeup
AllInPayIndustryEnum.2515=Jewelry
AllInPayIndustryEnum.2516=Outdoor Sports
AllInPayIndustryEnum.2517=Household appliances
AllInPayIndustryEnum.2518=Food and Health
AllInPayIndustryEnum.2519=Education Fees
AllInPayIndustryEnum.2520=Airline ticket
AllInPayIndustryEnum.2521=Hotel Accommodation
AllInPayIndustryEnum.2523=Funds inbound
AllInPayIndustryEnum.2524=Customs declaration business
AllInPayIndustryEnum.2522=Other

AllInPayMemberTypeEnum.2=Enterprise Member
AllInPayMemberTypeEnum.3=Individual Member

AllInPayPersonalStateEnum.1=Valid
AllInPayPersonalStateEnum.3=Audit failed
AllInPayPersonalStateEnum.5=Locked
AllInPayPersonalStateEnum.7=Pending review

EAccountPayStatusEnum.success=Success
EAccountPayStatusEnum.pending=Pending
EAccountPayStatusEnum.fail=Failed
EAccountPayStatusEnum.unpay=Unpaid

TradeTypeEnum.1=Deposit
TradeTypeEnum.2=Transfer
TradeTypeEnum.3=Withdrawal
TradeTypeEnum.4=Refund
TradeTypeEnum.5=Accounts Receivable Confirmation
TradeTypeEnum.6=Cashierbao refund funds transfer
TradeTypeEnum.7=Accounts receivable handling fee confirmation

AccountTypeEnum.1=Platform Type
AccountTypeEnum.2=Member Type

CcbB2bPayRefundEnum.0=Failed
CcbB2bPayRefundEnum.1=Success
CcbB2bPayRefundEnum.2=To be confirmed by the bank
CcbB2bPayRefundEnum.5=To be confirmed by the bank

CcbB2bPayResultEnum.0=Failed
CcbB2bPayResultEnum.1=Success
CcbB2bPayResultEnum.2=To be confirmed by the bank
CcbB2bPayResultEnum.3=Partial refund
CcbB2bPayResultEnum.4=Fully refunded
CcbB2bPayResultEnum.5=To be confirmed by the bank

CcbDigitalPayResultEnum.00=Success, interrupt polling
CcbDigitalPayResultEnum.01=Failed, aborted polling
CcbDigitalPayResultEnum.02=Not sure, continue polling

CcbDigitalPayTypeEnum.1=Dynamic QR code
CcbDigitalPayTypeEnum.2=H5

EAccountStatusEnum.1=Unfreeze
EAccountStatusEnum.2=Freeze

ServiceTypeEnum.1_1=Payment Service--Account Recharge
ServiceTypeEnum.1_2=Payment Service--Credit Repayment
ServiceTypeEnum.2_1=Order Service--Order Payment
ServiceTypeEnum.3_1=Settlement Services--Payables--Tonglian Payment

AccountStatusEnum.1=OK
AccountStatusEnum.2=Frozen

CreditApplyLowerInnerStatusEnum.0=All
CreditApplyLowerInnerStatusEnum.1=To be submitted
CreditApplyLowerInnerStatusEnum.2=Submitted

CreditApplyOuterStatusEnum.0=All Status
CreditApplyOuterStatusEnum.1=To be submitted
CreditApplyOuterStatusEnum.2=To be confirmed
CreditApplyOuterStatusEnum.3=Accept application
CreditApplyOuterStatusEnum.4=Not accepting applications

CreditApplySuperiorActionEnum.0=all
CreditApplySuperiorActionEnum.1=Submit credit application for review
CreditApplySuperiorActionEnum.2=Review credit application form (level 1)
CreditApplySuperiorActionEnum.3=Review credit application form (level 2)
CreditApplySuperiorActionEnum.4=Review credit application form (level 3)
CreditApplySuperiorActionEnum.5=Confirm credit application form

CreditApplySuperiorInnerStatusEnum.0=All
CreditApplySuperiorInnerStatusEnum.1=Pending submission for review
CreditApplySuperiorInnerStatusEnum.2=Failed to submit for review
CreditApplySuperiorInnerStatusEnum.3=Pending Review (Level 1)
CreditApplySuperiorInnerStatusEnum.4=Review failed (Level 1)
CreditApplySuperiorInnerStatusEnum.5=To be reviewed (Level 2)
CreditApplySuperiorInnerStatusEnum.6=Review failed (Secondary)
CreditApplySuperiorInnerStatusEnum.7=To be reviewed (Level 3)
CreditApplySuperiorInnerStatusEnum.8=Review failed (level 3)
CreditApplySuperiorInnerStatusEnum.9=To be confirmed
CreditApplySuperiorInnerStatusEnum.10=Confirmed (failed)
CreditApplySuperiorInnerStatusEnum.11=Confirmed (Passed)

CreditApplyTypeEnum.0=all
CreditApplyTypeEnum.1=External Application
CreditApplyTypeEnum.2=Internal Adjustment Order

CreditRepayStatusEnum.0=All Status
CreditRepayStatusEnum.1=Bill pending
CreditRepayStatusEnum.2=To be confirmed repayment result
CreditRepayStatusEnum.3=Bill has been paid
CreditRepayStatusEnum.4=Overdue

CreditStatusEnum.0=All Status
CreditStatusEnum.1=Not applied
CreditStatusEnum.2=Applying
CreditStatusEnum.3=Normal
CreditStatusEnum.4=Frozen

CreditTradeOperationEnum.0=all
CreditTradeOperationEnum.1=Order Payment
CreditTradeOperationEnum.2=Order refund
CreditTradeOperationEnum.3=Repayment

CreditTradeStatusEnum.0=all
CreditTradeStatusEnum.1=To be confirmed repayment result
CreditTradeStatusEnum.2=Confirmation not credited
CreditTradeStatusEnum.3=Confirmation to account

PayChannelEnum.1=Platform payment
PayChannelEnum.2=APP payment
PayChannelEnum.3=Mini Program Payment

PayTypeEnum.1=Platform payment configuration
PayTypeEnum.2=Member payment configuration

TradeOperationEnum.1=Account Recharge
TradeOperationEnum.2=Account Withdrawal
TradeOperationEnum.3=Order payment
TradeOperationEnum.4=Order refund
TradeOperationEnum.5=Order rebate

TradeStatusEnum.1=Apply for withdrawal
TradeStatusEnum.2=Approved
TradeStatusEnum.3=Approval failed
TradeStatusEnum.4=Withdrawal successful
TradeStatusEnum.5=Withdrawal failed
TradeStatusEnum.6=Paying
TradeStatusEnum.7=Payment failed
TradeStatusEnum.8=Confirm receipt
TradeStatusEnum.9=Successful payment

WechatErrorEnum.BIZERR_NEED_RETRY=There is an error in the refund business process, and the merchant needs to trigger a retry to solve it
WechatErrorEnum.TRADE_OVERDUE=The order has exceeded the refund period
WechatErrorEnum.ERROR=Business Error
WechatErrorEnum.USER_ACCOUNT_ABNORMAL=Refund request failed
WechatErrorEnum.INVALID_REQ_TOO_MUCH=Too many invalid requests
WechatErrorEnum.INVALID_TRANSACTIONID=Invalid transaction_id
WechatErrorEnum.PARAM_ERROR=Parameter error
WechatErrorEnum.CERT_ERROR=Certificate verification error
WechatErrorEnum.REFUND_FEE_MISMATCH=The order amount or refund amount is inconsistent with the previous request, please verify and try again
WechatErrorEnum.ORDER_NOT_READY=The order is being processed. Refunds are currently unavailable, please try again later
WechatErrorEnum.FREQUENCY_LIMITED=Frequency Limit
WechatErrorEnum.INVALID_REQUEST=Parameter error
WechatErrorEnum.NOAUTH=The merchant does not have this interface permission
WechatErrorEnum.NOTENOUGH=Insufficient balance
WechatErrorEnum.REFUNDNOTEXIST=Refund order query failed
WechatErrorEnum.APPID_NOT_EXIST=APPID does not exist
WechatErrorEnum.MCHID_NOT_EXIST=MCHID does not exist
WechatErrorEnum.APPID_MCHID_NOT_MATCH=appid and mch_id do not match
WechatErrorEnum.LACK_PARAMS=Missing parameters
WechatErrorEnum.OUT_TRADE_NO_USED=Duplicate Merchant Order Number
WechatErrorEnum.POST_DATA_EMPTY=The post data is empty
WechatErrorEnum.NOT_UTF8=Encoding format error
WechatErrorEnum.ORDERNOTEXIST=This trade order number does not exist
WechatErrorEnum.ORDERPAID=Order paid
WechatErrorEnum.SYSTEMERROR=System Error
WechatErrorEnum.ORDERCLOSED=Order closed
WechatErrorEnum.SIGNERROR=Signature error
WechatErrorEnum.REQUIRE_POST_METHOD=Please use post method
WechatErrorEnum.XML_FORMAT_ERROR=XML format error

AccountOperateTypeEnum.1=Withdrawal pending review
AccountOperateTypeEnum.2=Pending payment withdrawal

CreditOperateTypeEnum.1=To be submitted for review credit application
CreditOperateTypeEnum.2=Pending credit application form (level 1)
CreditOperateTypeEnum.3=Pending credit application form (level 2)
CreditOperateTypeEnum.4=Pending credit application form (level 3)
CreditOperateTypeEnum.5=To be confirmed credit application form