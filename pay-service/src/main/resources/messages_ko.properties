AllInPayEnterpriseStateEnum.1=검토 대기 중
AllInPayEnterpriseStateEnum.2=승인 성공
AllInPayEnterpriseStateEnum.3=감사 실패

AllInPayErrorCodeEnum.10000=매개변수 오류
AllInPayErrorCodeEnum.10001=매개변수는 비워둘 수 없습니다.
AllInPayErrorCodeEnum.10002=파라미터 서비스 오류
AllInPayErrorCodeEnum.10003=매개변수 메소드 오류
AllInPayErrorCodeEnum.10004=매개변수 길이가 너무 깁니다.
AllInPayErrorCodeEnum.10005=동시성 오류
AllInPayErrorCodeEnum.20000=앱이 존재하지 않습니다
AllInPayErrorCodeEnum.20001=응용 프로그램이 B2C 모드를 활성화하지 않습니다.
AllInPayErrorCodeEnum.20002=앱이 앱 간 전송을 활성화하지 않았습니다.
AllInPayErrorCodeEnum.20003=앱에 이 인터페이스를 사용할 권한이 없습니다.
AllInPayErrorCodeEnum.20004=사용자에게 결제 확인을 위한 비밀번호가 없습니다.
AllInPayErrorCodeEnum.20005= 사용자가 3요소 바인딩 카드를 활성화하지 않았습니다.
AllInPayErrorCodeEnum.20006=디코딩 오류
AllInPayErrorCodeEnum.20007=개인 카드 바인딩 설정 수 초과
AllInPayErrorCodeEnum.20008=꼬마 B의 개인 카드 묶기 권한이 활성화 또는 설정되지 않았습니다.
AllInPayErrorCodeEnum.20009=채널 가맹점 정보 오류
AllInPayErrorCodeEnum.20010=비밀번호 오류
AllInPayErrorCodeEnum.20011=인증 코드 오류
AllInPayErrorCodeEnum.20012=지원되지 않는 문서 유형
AllInPayErrorCodeEnum.20013=지원되지 않는 요청 소스(예: 휴대폰, PC)
AllInPayErrorCodeEnum.20014=은행 코드 오류
AllInPayErrorCodeEnum.20015=만료 시간 설정 오류
AllInPayErrorCodeEnum.20016=암호화 실패
AllInPayErrorCodeEnum.20017=복호화 실패
AllInPayErrorCodeEnum.20018=서명 실패
AllInPayErrorCodeEnum.20019=서명 확인 실패
AllInPayErrorCodeEnum.20020=은행 카드는 현금 인출 또는 지불/디코딩 오류를 지원하지 않습니다
AllInPayErrorCodeEnum.20021=캐시 작업 오류
AllInPayErrorCodeEnum.20022=중복 요청 파이프라인
AllInPayErrorCodeEnum.20023=판매자 시스템 사용자 ID 오류
AllInPayErrorCodeEnum.20024=Yunshangtong 은행 코드가 구성되지 않았습니다.
AllInPayErrorCodeEnum.20025=수취인 정산 라인 번호가 구성되지 않았습니다
AllInPayErrorCodeEnum.20026=복사정보 보완을 위해 [복사수집 및 기업정보 설정(H5)]으로 전화주세요.
AllInPayErrorCodeEnum.20027=출금 금액이 채널 제한을 초과했습니다
AllInPayErrorCodeEnum.30000=사용자가 이미 존재합니다
AllInPayErrorCodeEnum.30001=사용자가 존재하지 않습니다
AllInPayErrorCodeEnum.30002=사용자가 잠겼습니다
AllInPayErrorCodeEnum.30003=사용자가 활성화되었습니다
AllInPayErrorCodeEnum.30004=사용자는 이미 개발자입니다
AllInPayErrorCodeEnum.30005=사용자는 개발자가 아닙니다.
AllInPayErrorCodeEnum.30006=사용자 유형이 올바르지 않습니다.
AllInPayErrorCodeEnum.30007=사용자가 실명으로 인증되었습니다.
AllInPayErrorCodeEnum.30008=사용자가 실명으로 인증되지 않았습니다.
AllInPayErrorCodeEnum.30009=실명확인 실패
AllInPayErrorCodeEnum.300010=사용자가 로그인 SMS 알림을 설정했습니다.
AllInPayErrorCodeEnum.300011=사용자가 로그인 이메일 알림을 설정했습니다.
AllInPayErrorCodeEnum.300012=신분증 확인 실패
AllInPayErrorCodeEnum.300013=템플릿 유형 오류
AllInPayErrorCodeEnum.300014=은행 카드 바인딩 실패
AllInPayErrorCodeEnum.300015=은행 카드 바인딩 해제 실패
AllInPayErrorCodeEnum.300016=은행 카드 바인딩 기록이 존재하지 않습니다
AllInPayErrorCodeEnum.300017=은행 카드가 묶여 있습니다.
AllInPayErrorCodeEnum.300018=계정 설정 오류
AllInPayErrorCodeEnum.300019=계정 세트가 존재하지 않습니다
AllInPayErrorCodeEnum.300020=이메일이 이미 존재합니다
AllInPayErrorCodeEnum.300021=연결 해제된 전화
AllInPayErrorCodeEnum.300022=사용자를 사용할 수 없음
AllInPayErrorCodeEnum.300023=기업 회원이 검토되었습니다
AllInPayErrorCodeEnum.300024=휴대폰 바인딩
AllInPayErrorCodeEnum.300030=기업 정보가 설정되지 않았습니다.
AllInPayErrorCodeEnum.300031=결제 비밀번호가 설정되었습니다.
AllInPayErrorCodeEnum.300032=결제 비밀번호가 설정되지 않았습니다.
AllInPayErrorCodeEnum.300033=결제 비밀번호 오류가 한도를 초과했습니다
AllInPayErrorCodeEnum.300034=은행 카드가 계약 확인을 완료하지 않았습니다.
AllInPayErrorCodeEnum.300035=실명인증이 완료되었습니다.
AllInPayErrorCodeEnum.300036=구성원 보안 수준이 설정되지 않았습니다.
AllInPayErrorCodeEnum.300037=회원의 보안 수준이 낮고 현재 작업이 지원되지 않습니다
AllInPayErrorCodeEnum.300038=실명 결제 기록이 존재하지 않거나 확인되었습니다.
AllInPayErrorCodeEnum.300039=은행카드 실명인증이 완료되지 않았습니다.
AllInPayErrorCodeEnum.300040=잘못된 카드 유형
AllInPayErrorCodeEnum.300041=은행 카드에 묶인 휴대폰 번호가 아닙니다.
AllInPayErrorCodeEnum.300042=기업 인증 실패
AllInPayErrorCodeEnum.300043=회원이 전자 계약에 서명했습니다.
AllInPayErrorCodeEnum.300044=은행 카드가 존재하지 않습니다
AllInPayErrorCodeEnum.300045=은행 카드가 묶이지 않았습니다.
AllInPayErrorCodeEnum.300046=회원이 전자계약에 서명하지 않았습니다.
AllInPayErrorCodeEnum.300047=이 은행 카드는 현재 지원되지 않습니다
AllInPayErrorCodeEnum.300048=회원이 자산 관리 계정을 개설하지 않았습니다.
AllInPayErrorCodeEnum.300049=카드함 테이블에 카드함이 없습니다.
AllInPayErrorCodeEnum.300050=기업 멤버십이 승인되지 않았습니다.
AllInPayErrorCodeEnum.300051=계정 집합이 쿼리를 지원하지 않습니다
AllInPayErrorCodeEnum.300052=잔액 이체 계약이 체결되었습니다.
AllInPayErrorCodeEnum.300053=잔액 지불 계약이 존재하지 않습니다
AllInPayErrorCodeEnum.300054=프로토콜 정보가 일치하지 않습니다
AllInPayErrorCodeEnum.300055=계약이 취소된 상태이며 계약을 취소할 필요가 없습니다.
AllInPayErrorCodeEnum.300056=프로토콜 상태 오류
AllInPayErrorCodeEnum.300057=사용자 애플리케이션 불일치
AllInPayErrorCodeEnum.300058=수취인이 존재하지 않습니다
AllInPayErrorCodeEnum.300059=수취인은 플랫폼이 될 수 없습니다.
AllInPayErrorCodeEnum.300060=특권 회원은 호출 권한이 없습니다
AllInPayErrorCodeEnum.300061=예약 정보가 구성되지 않았습니다.
AllInPayErrorCodeEnum.300062=예금 기관 보조 계좌가 개설되지 않았습니다.
AllInPayErrorCodeEnum.300063=구성원이 이 계정 세트를 열지 않았습니다.
AllInPayErrorCodeEnum.300064=백그라운드 알림 주소가 구성되지 않았습니다
AllInPayErrorCodeEnum.300065=예치금 구성 관련 오류
AllInPayErrorCodeEnum.300066=회사 은행 카드는 바인딩 해제가 허용되지 않습니다.
AllInPayErrorCodeEnum.300067=새 전화번호는 원래 전화번호와 동일합니다.
AllInPayErrorCodeEnum.300068=인증서 유형이 실명인증서 유형과 일치하지 않습니다.
AllInPayErrorCodeEnum.300069=실명 인증 정보가 원본 실명 인증 정보와 일치하지 않습니다!
AllInPayErrorCodeEnum.300070=금전등록기에 묶인 카드는 신분증 전용입니다!
AllInPayErrorCodeEnum.300071=번호 확인 서비스 실패
AllInPayErrorCodeEnum.300072=불완전한 회원 정보
AllInPayErrorCodeEnum.300073=OCR 식별 경로가 구성되지 않았습니다
AllInPayErrorCodeEnum.300074=휴대전화번호와 아이디에 묶인 회원수가 설정되어 있지 않습니다.
AllInPayErrorCodeEnum.300075=휴대폰 번호에 묶인 회원 수가 제한되어 있습니다.
AllInPayErrorCodeEnum.300076=ID 번호로 묶인 회원 수가 제한되어 있습니다.
AllInPayErrorCodeEnum.300077=비즈니스 라이센스가 인식되었으며 다시 업로드할 필요가 없습니다.
AllInPayErrorCodeEnum.300078=법인 신분증이 인식되어 재업로드할 필요가 없습니다.
AllInPayErrorCodeEnum.40000=주문이 존재하지 않습니다
AllInPayErrorCodeEnum.40001=항목 유형 불일치
AllInPayErrorCodeEnum.40002=주문 오류
AllInPayErrorCodeEnum.40003=주문이 만료되었습니다
AllInPayErrorCodeEnum.40004=주문이 미결제 상태가 아닙니다.
AllInPayErrorCodeEnum.40005=주문 금액이 결제 금액과 일치하지 않습니다.
AllInPayErrorCodeEnum.40006=결제 실패
AllInPayErrorCodeEnum.40007=항목이 이미 존재합니다
AllInPayErrorCodeEnum.40008=항목 유형이 존재하지 않습니다
AllInPayErrorCodeEnum.40009=사용 가능한 결제 채널이 없습니다.
AllInPayErrorCodeEnum.40010=거래 유형이 존재하지 않습니다
AllInPayErrorCodeEnum.40011=예치금 계좌에 수수료가 부족합니다.
AllInPayErrorCodeEnum.40012=신용카드가 지원되지 않습니다
AllInPayErrorCodeEnum.40013=상품 입력 실패
AllInPayErrorCodeEnum.40014=계정 잔액 부족
AllInPayErrorCodeEnum.40015=거래 규칙 제한
AllInPayErrorCodeEnum.40016=수수료가 금액보다 큽니다.
AllInPayErrorCodeEnum.40017=주문 번호가 중복되었습니다. 주문 번호를 변경하고 거래를 시작하십시오
AllInPayErrorCodeEnum.40018=잘못된 주문 유형
AllInPayErrorCodeEnum.40019=잘못된 주문 상태
AllInPayErrorCodeEnum.40020=잘못된 주문 금액
AllInPayErrorCodeEnum.40021=강력한 실명인증 오류
AllInPayErrorCodeEnum.40022=주문과 사용자가 일치하지 않습니다
AllInPayErrorCodeEnum.40023=환불 유형 환불 유형이 올바르지 않습니다.
AllInPayErrorCodeEnum.40025=응용 프로그램의 지불 채널 구성이 존재하지 않습니다
AllInPayErrorCodeEnum.40026=결제 권한 없음
AllInPayErrorCodeEnum.40027=출금 결제 방법이 설정되지 않았습니다.
AllInPayErrorCodeEnum.40028=잘못된 거래 확인 방법
AllInPayErrorCodeEnum.40029=URL 형식 오류
AllInPayErrorCodeEnum.40030=출금 결제 채널 계정이 구성되지 않았습니다.
AllInPayErrorCodeEnum.40031=수수료가 설정되지 않았습니다.
AllInPayErrorCodeEnum.40032=채널 뱅크가 설정되지 않았습니다.
AllInPayErrorCodeEnum.40033=중복 주문 확인
AllInPayErrorCodeEnum.40034=결제 채널 속성이 일치하지 않습니다
AllInPayErrorCodeEnum.40035=출금은 T0T1만 지원합니다
AllInPayErrorCodeEnum.40036=결제 라인 번호 누락
AllInPayErrorCodeEnum.40037=결제 라인 번호 형식 오류
AllInPayErrorCodeEnum.40039=원래 주문은 환불을 지원하지 않습니다. 처리를 위해 판매자에게 문의하십시오
AllInPayErrorCodeEnum.40040=수집 주문량 부족
AllInPayErrorCodeEnum.40041=원래 주문이 결제되지 않았으므로 환불을 시작할 수 없습니다.
AllInPayErrorCodeEnum.40042=이 출금 방법은 지원되지 않습니다
AllInPayErrorCodeEnum.40043=출금 방법에 권한이 없습니다
AllInPayErrorCodeEnum.40044=잘못된 날짜 형식
AllInPayErrorCodeEnum.40045=일당 누적 위험 통제 초과 횟수
AllInPayErrorCodeEnum.40046=위험 관리는 단일 주문 금액을 제한합니다.
AllInPayErrorCodeEnum.40047=하루에 한도를 초과하는 위험 통제 누적량
AllInPayErrorCodeEnum.40048=회원이 vspCusid에 바인딩되지 않았습니다.
AllInPayErrorCodeEnum.40049=결제 수단 vspCusid가 받는 회원이 묶은 vspCusid와 일치하지 않습니다.
AllInPayErrorCodeEnum.40050=항목 유형 또는 항목 코드가 수취인과 일치하지 않습니다.
AllInPayErrorCodeEnum.40051=응용 프로그램의 지불 채널 구성이 존재하지 않습니다
AllInPayErrorCodeEnum.40052=제품 번호의 총 수가 총 거래 수와 일치하지 않습니다!
AllInPayErrorCodeEnum.40055=통리안 지갑에 등록된 회원이 비정상입니다!
AllInPayErrorCodeEnum.40056=분할 데이터에 오류가 있습니다. 최대 분할 수준은 3이고, 분할 구성원의 최대 수는 10입니다!
AllInPayErrorCodeEnum.40057=결제 채널 제한 초과
AllInPayErrorCodeEnum.40058=은행 위치 확인 오류
AllInPayErrorCodeEnum.40059=SMS 재전송 주문 실패
AllInPayErrorCodeEnum.40060=잘못된 금액
AllInPayErrorCodeEnum.40061=금액이 허용 범위를 초과합니다
AllInPayErrorCodeEnum.40064=예금이 아닌 은행
AllInPayErrorCodeEnum.40065=주문 마감 불가
AllInPayErrorCodeEnum.50001=계정 잔액 부족
AllInPayErrorCodeEnum.50002=계정 동결
AllInPayErrorCodeEnum.50003=고정 잔액 부족
AllInPayErrorCodeEnum.50004=고정된 레코드가 존재하지 않습니다
AllInPayErrorCodeEnum.50005=내부 계정에 해당하는 구성원 또는 계정 유형이 일치하지 않습니다.
AllInPayErrorCodeEnum.50006=계정이 존재하지 않습니다
AllInPayErrorCodeEnum.9000=기타 오류
AllInPayErrorCodeEnum.9100=알 수 없는 오류
AllInPayErrorCodeEnum.9200=데이터베이스 오류
AllInPayErrorCodeEnum.9300=외부 오류
AllInPayErrorCodeEnum.9400=요청 시간 초과
AllInPayErrorCodeEnum.9500=http 통신 오류 (통신 과정에서 오류가 발생하거나 리턴 코드가 200이 아님)
AllInPayErrorCodeEnum.9999=시스템 오류

AllInPayIndustryEnum.1010=보험 대리인
AllInPayIndustryEnum.1011=보험 브로커
AllInPayIndustryEnum.1012=보험 조정자
AllInPayIndustryEnum.1013=재산 보험 회사
AllInPayIndustryEnum.1014=생명 보험 회사
AllInPayIndustryEnum.1015=건강 보험
AllInPayIndustryEnum.1016=연금 보험
AllInPayIndustryEnum.1110=소액 대출 회사
AllInPayIndustryEnum.1111=P2P 플랫폼
AllInPayIndustryEnum.1114=재정 보증
AllInPayIndustryEnum.1115=금융 임대
AllInPayIndustryEnum.1116=투자 및 자산 관리
AllInPayIndustryEnum.1117=송장 거래
AllInPayIndustryEnum.1118=크라우드펀딩 거래
AllInPayIndustryEnum.1119=주
AllInPayIndustryEnum.1410=자동차 판매
AllInPayIndustryEnum.1411=자동 금융
AllInPayIndustryEnum.1412=자동차 렌탈
AllInPayIndustryEnum.1413=자동차 판매 후
AllInPayIndustryEnum.1414=자동차 운송
AllInPayIndustryEnum.1510=학교
AllInPayIndustryEnum.1511=훈련
AllInPayIndustryEnum.1512=시험 센터
AllInPayIndustryEnum.1514=미디어
AllInPayIndustryEnum.1610=부동산 판매
AllInPayIndustryEnum.1611=부동산 중개소
AllInPayIndustryEnum.1612=재산 관리
AllInPayIndustryEnum.1613=부동산 임대
AllInPayIndustryEnum.1910=기타
AllInPayIndustryEnum.1912=고급 보석
AllInPayIndustryEnum.1914=거래소
AllInPayIndustryEnum.1915=유틸리티
AllInPayIndustryEnum.1916=편리한 결제
AllInPayIndustryEnum.1917=입찰
AllInPayIndustryEnum.1918=단체 구매
AllInPayIndustryEnum.1929=물류
AllInPayIndustryEnum.1930=의료
AllInPayIndustryEnum.1940=출장 여행
AllInPayIndustryEnum.1941=통신 사업자 및 공기 충전기
AllInPayIndustryEnum.1944=생산 또는 유통 기업을 위한 자금 수집
AllInPayIndustryEnum.1947=상담 관리
AllInPayIndustryEnum.1953=은행 내 기타 애플리케이션
AllInPayIndustryEnum.1954=은행(금융)
AllInPayIndustryEnum.2310=관리 용품
AllInPayIndustryEnum.2311=식음료
AllInPayIndustryEnum.2312=건강 제품
AllInPayIndustryEnum.2313=담배와 술
AllInPayIndustryEnum.2314=피드
AllInPayIndustryEnum.2413=전자제품
AllInPayIndustryEnum.2414=퍼스널 케어 메이크업
AllInPayIndustryEnum.2415=보석
AllInPayIndustryEnum.2416=야외 스포츠
AllInPayIndustryEnum.2417=가전제품
AllInPayIndustryEnum.2418=다이어트 및 건강
AllInPayIndustryEnum.2419=교육비
AllInPayIndustryEnum.2420=항공권
AllInPayIndustryEnum.2421=호텔 숙박
AllInPayIndustryEnum.2422=기타
AllInPayIndustryEnum.2510=신발 및 모자 의류
AllInPayIndustryEnum.2511=수하물 액세서리
AllInPayIndustryEnum.2512=가전제품
AllInPayIndustryEnum.2513=전자제품
AllInPayIndustryEnum.2514=퍼스널 케어 메이크업
AllInPayIndustryEnum.2515=보석
AllInPayIndustryEnum.2516=야외 스포츠
AllInPayIndustryEnum.2517=가전제품
AllInPayIndustryEnum.2518=식품 및 건강
AllInPayIndustryEnum.2519=교육비
AllInPayIndustryEnum.2520=항공권
AllInPayIndustryEnum.2521=호텔 숙박
AllInPayIndustryEnum.2523=자금 유입
AllInPayIndustryEnum.2524=관세 신고 업무
AllInPayIndustryEnum.2522=기타

AllInPayMemberTypeEnum.2=기업 회원
AllInPayMemberTypeEnum.3=개인 회원

AllInPayPersonalStateEnum.1=유효
AllInPayPersonalStateEnum.3=감사 실패
AllInPayPersonalStateEnum.5=잠김
AllInPayPersonalStateEnum.7=검토 대기 중

EAccountPayStatusEnum.success=성공
EAccountPayStatusEnum.pending=보류 중
EAccountPayStatusEnum.fail=실패
EAccountPayStatusEnum.unpay=미지급

TradeTypeEnum.1=예금
TradeTypeEnum.2=이체
TradeTypeEnum.3=출금
TradeTypeEnum.4=환불
TradeTypeEnum.5=미수금 확인서
TradeTypeEnum.6=캐셔바오 환불 자금 이체
TradeTypeEnum.7=수취 계정 수수료 확인

AccountTypeEnum.1=플랫폼 유형
AccountTypeEnum.2=회원 유형

CcbB2bPayRefundEnum.0=실패
CcbB2bPayRefundEnum.1=성공
CcbB2bPayRefundEnum.2=은행 확인
CcbB2bPayRefundEnum.5=은행 확인

CcbB2bPayResultEnum.0=실패
CcbB2bPayResultEnum.1=성공
CcbB2bPayResultEnum.2=은행 확인
CcbB2bPayResultEnum.3=부분 환불
CcbB2bPayResultEnum.4=전액 환불
CcbB2bPayResultEnum.5=은행 확인

CcbDigitalPayResultEnum.00=성공, 폴링 중단
CcbDigitalPayResultEnum.01=실패, 폴링 중단
CcbDigitalPayResultEnum.02=확실하지 않습니다. 폴링을 계속하십시오.

CcbDigitalPayTypeEnum.1=동적 QR 코드
CcbDigitalPayTypeEnum.2=H5

EAccountStatusEnum.1=고정 해제
EAccountStatusEnum.2=정지

ServiceTypeEnum.1_1=결제 서비스--계정 충전
ServiceTypeEnum.1_2=결제 서비스--신용 상환
ServiceTypeEnum.2_1=서비스 주문--결제 주문
ServiceTypeEnum.3_1=결제 서비스--지급금--통련 결제

AccountStatusEnum.1=확인
AccountStatusEnum.2=정지

CreditApplyLowerInnerStatusEnum.0=모두
CreditApplyLowerInnerStatusEnum.1=제출 예정
CreditApplyLowerInnerStatusEnum.2=제출됨

CreditApplyOuterStatusEnum.0=모든 상태
CreditApplyOuterStatusEnum.1=제출 예정
CreditApplyOuterStatusEnum.2=확인 예정
CreditApplyOuterStatusEnum.3=신청 수락
CreditApplyOuterStatusEnum.4=신청을 수락하지 않음

CreditApplySuperiorActionEnum.0=모두
CreditApplySuperiorActionEnum.1=검토를 위해 신용 신청서 제출
CreditApplySuperiorActionEnum.2=크레딧 신청서 검토(레벨 1)
CreditApplySuperiorActionEnum.3=신용 신청서 검토(레벨 2)
CreditApplySuperiorActionEnum.4=크레딧 신청서 검토(레벨 3)
CreditApplySuperiorActionEnum.5=신용 신청서 확인

CreditApplySuperiorInnerStatusEnum.0=모두
CreditApplySuperiorInnerStatusEnum.1=검토를 위해 제출 대기 중
CreditApplySuperiorInnerStatusEnum.2=검토를 위해 제출 실패
CreditApplySuperiorInnerStatusEnum.3=검토 보류(레벨 1)
CreditApplySuperiorInnerStatusEnum.4=검토 실패(레벨 ​​1)
CreditApplySuperiorInnerStatusEnum.5=검토 예정(레벨 2)
CreditApplySuperiorInnerStatusEnum.6=검토 실패(보조)
CreditApplySuperiorInnerStatusEnum.7=검토 예정(레벨 3)
CreditApplySuperiorInnerStatusEnum.8=검토 실패(레벨 ​​3)
CreditApplySuperiorInnerStatusEnum.9=확인 예정
CreditApplySuperiorInnerStatusEnum.10=확인됨(실패)
CreditApplySuperiorInnerStatusEnum.11=확인(통과)

CreditApplyTypeEnum.0=모두
CreditApplyTypeEnum.1=외부 애플리케이션
CreditApplyTypeEnum.2=내부 조정 명령

CreditRepayStatusEnum.0=모든 상태
CreditRepayStatusEnum.1=청구서 보류 중
CreditRepayStatusEnum.2=상환 결과 확인 예정
CreditRepayStatusEnum.3=청구서가 지불되었습니다
CreditRepayStatusEnum.4=연체

CreditStatusEnum.0=모든 상태
CreditStatusEnum.1=적용되지 않음
CreditStatusEnum.2=신청 중
CreditStatusEnum.3=정상
CreditStatusEnum.4=동결

CreditTradeOperationEnum.0=모두
CreditTradeOperationEnum.1=결제 주문
CreditTradeOperationEnum.2=주문 환불
CreditTradeOperationEnum.3=상환

CreditTradeStatusEnum.0=모두
CreditTradeStatusEnum.1=상환 결과 확인 예정
CreditTradeStatusEnum.2=확인이 신용되지 않음
CreditTradeStatusEnum.3=계정 확인

PayChannelEnum.1=플랫폼 결제
PayChannelEnum.2=앱 결제
PayChannelEnum.3=미니 프로그램 결제

PayTypeEnum.1=플랫폼 결제 구성
PayTypeEnum.2=회원 결제 설정

TradeOperationEnum.1=계정 충전
TradeOperationEnum.2=계정 인출
TradeOperationEnum.3=주문 결제
TradeOperationEnum.4=주문 환불
TradeOperationEnum.5=주문 환불

TradeStatusEnum.1=출금 신청
TradeStatusEnum.2=승인됨
TradeStatusEnum.3=승인 실패
TradeStatusEnum.4=출금 성공
TradeStatusEnum.5=출금 실패
TradeStatusEnum.6=결제 중
TradeStatusEnum.7=결제 실패
TradeStatusEnum.8=영수증 확인
TradeStatusEnum.9=성공적인 결제

WechatErrorEnum.BIZERR_NEED_RETRY=환불 업무 프로세스에 오류가 있으며 판매자는 이를 해결하기 위해 재시도해야 합니다.
WechatErrorEnum.TRADE_OVERDUE=주문이 환불 기간을 초과했습니다
WechatErrorEnum.ERROR=비즈니스 오류
WechatErrorEnum.USER_ACCOUNT_ABNORMAL=환불 요청 실패
WechatErrorEnum.INVALID_REQ_TOO_MUCH=잘못된 요청이 너무 많습니다.
WechatErrorEnum.INVALID_TRANSACTIONID=잘못된 transaction_id
WechatErrorEnum.PARAM_ERROR=매개변수 오류
WechatErrorEnum.CERT_ERROR=인증서 확인 오류
WechatErrorEnum.REFUND_FEE_MISMATCH=주문 금액 또는 환불 금액이 이전 요청과 일치하지 않습니다. 확인하고 다시 시도하십시오.
WechatErrorEnum.ORDER_NOT_READY=주문 처리 중입니다. 현재 환불이 불가능합니다. 나중에 다시 시도해 주세요.
WechatErrorEnum.FREQUENCY_LIMITED=주파수 제한
WechatErrorEnum.INVALID_REQUEST=매개변수 오류
WechatErrorEnum.NOAUTH=판매자는 이 인터페이스 권한이 없습니다.
WechatErrorEnum.NOTENOUGH=잔액 부족
WechatErrorEnum.REFUNDNOTEXIST=환불 주문 조회 실패
WechatErrorEnum.APPID_NOT_EXIST=APPID가 존재하지 않습니다
WechatErrorEnum.MCHID_NOT_EXIST=MCHID가 존재하지 않습니다
WechatErrorEnum.APPID_MCHID_NOT_MATCH=appid와 mch_id가 일치하지 않습니다
WechatErrorEnum.LACK_PARAMS=매개변수 누락
WechatErrorEnum.OUT_TRADE_NO_USED=판매자 주문 번호 중복
WechatErrorEnum.POST_DATA_EMPTY=게시물 데이터가 비어 있습니다.
WechatErrorEnum.NOT_UTF8=인코딩 형식 오류
WechatErrorEnum.ORDERNOTEXIST=이 거래 주문 번호는 존재하지 않습니다
WechatErrorEnum.ORDERPAID=주문 결제
WechatErrorEnum.SYSTEMERROR=시스템 오류
WechatErrorEnum.ORDERCLOSED=주문 마감
WechatErrorEnum.SIGNERROR=서명 오류
WechatErrorEnum.REQUIRE_POST_METHOD=포스트 방식을 사용하세요
WechatErrorEnum.XML_FORMAT_ERROR=XML 형식 오류

AccountOperateTypeEnum.1=출금 검토 보류
AccountOperateTypeEnum.2=지급 인출 대기 중

CreditOperateTypeEnum.1=검토 크레딧 신청을 위해 제출 예정
CreditOperateTypeEnum.2=보류 중인 신용 신청 양식(레벨 1)
CreditOperateTypeEnum.3=보류 중인 신용 신청 양식(레벨 2)
CreditOperateTypeEnum.4=보류 중인 신용 신청 양식(레벨 3)
CreditOperateTypeEnum.5=신용 신청서 확인 예정